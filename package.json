{"name": "ziyouke.net", "version": "1.2.4", "description": "自由客紧固件一站式采购平台", "author": "自由客紧固件一站式采购平台", "license": "MIT", "scripts": {"dev": "SET NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service serve", "build:prod": "SET NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service build", "build:stage": "SET NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service build --mode staging", "preview": "node build/index.js --preview", "lint": "eslint --ext .js,.vue src"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "keywords": ["vue", "admin", "dashboard", "element-ui", "boilerplate", "admin-template", "management-system"], "repository": {"type": "git", "url": "https://gitee.com/y_project/RuoYi-Vue.git"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@ckeditor/ckeditor5-build-classic": "^38.1.0", "@form-create/element-ui": "^2.5.28", "@plugin-web-update-notification/webpack": "^1.6.5", "@riophae/vue-treeselect": "^0.4.0", "@vue-office/excel": "^1.7.11", "@vue/composition-api": "^1.7.2", "ali-oss": "^6.20.0", "aplayer": "^1.10.1", "axios": "0.24.0", "chinese-lunar-calendar": "^1.0.1", "clipboard": "2.0.8", "compressorjs": "^1.2.1", "core-js": "3.25.3", "docxtemplater": "^3.37.11", "echarts": "^5.4.2", "el-table-infinite-scroll": "^2.0.2", "element-china-area-data": "^6.1.0", "element-ui": "2.15.12", "file-saver": "2.0.5", "fuse.js": "6.4.3", "highlight.js": "9.18.5", "hls.js": "^1.5.20", "html-docx-js": "^0.3.1", "html2canvas": "^1.4.1", "i18n-jsautotranslate": "^3.13.12", "jquery": "^3.7.1", "js-beautify": "1.13.0", "js-cookie": "3.0.1", "js-pinyin": "^0.2.5", "jsencrypt": "^3.0.0-rc.1", "jspdf": "^2.5.1", "jszip": "^3.10.1", "lottie-web": "^5.12.2", "lunar-calendar": "^0.1.4", "lunar-javascript": "^1.6.13", "mammoth": "^1.6.0", "moment": "^2.29.4", "nprogress": "0.2.0", "print-js": "^1.6.0", "qrcodejs2": "0.0.2", "quill": "1.3.7", "quill-emoji": "^0.2.0", "screenfull": "5.0.2", "sortablejs": "^1.10.2", "spark-md5": "^3.0.2", "vue": "2.6.12", "vue-aplayer": "^1.6.1", "vue-baidu-map": "^0.21.22", "vue-click-outside": "^1.1.0", "vue-count-to": "1.0.13", "vue-cropper": "0.5.5", "vue-demi": "^0.14.6", "vue-esign": "^1.1.4", "vue-meta": "2.4.0", "vue-monoplasty-slide-verify": "^1.3.1", "vue-pdf": "^4.3.0", "vue-resource": "^1.5.3", "vue-router": "3.4.9", "vue-ueditor-wrap": "^2.5.6", "vuedraggable": "^2.24.3", "vuex": "3.6.0", "xlsx": "^0.14.1", "xlsx-style": "^0.8.13", "zlib": "^1.0.5"}, "devDependencies": {"@vue/cli-plugin-babel": "4.4.6", "@vue/cli-plugin-eslint": "4.4.6", "@vue/cli-service": "4.4.6", "babel-eslint": "10.1.0", "babel-plugin-dynamic-import-node": "2.3.3", "chalk": "4.1.0", "compression-webpack-plugin": "5.0.2", "connect": "3.6.6", "eslint": "7.15.0", "eslint-plugin-vue": "7.2.0", "lint-staged": "10.5.3", "runjs": "4.4.2", "sass": "1.32.13", "sass-loader": "10.1.1", "script-ext-html-webpack-plugin": "2.1.5", "svg-sprite-loader": "5.1.1", "vue-template-compiler": "2.6.12", "wangeditor": "^4.7.15"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions"]}