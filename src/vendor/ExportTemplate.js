/* eslint-disable */
import { saveAs } from 'file-saver'
import XLSX from 'xlsx'
import XLSXS from 'xlsx-style'

// 创建拆零导出模板
export function createSplitTemplate({ data, filename, options = {} }) {
  const { useTo, hasTax } = options

  try {
    // 创建工作簿
    const wb = XLSX.utils.book_new()

    // 创建工作表数据
    const wsData = createSheetData(data, useTo, hasTax)    // 创建工作表
    const ws = XLSX.utils.aoa_to_sheet(wsData)

    // 设置列宽
    setColumnWidths(ws)

    // 设置工作表样式
    setSheetStyles(ws, wsData, useTo, hasTax)

    // 设置行高（在样式设置之后）
    setRowHeights(ws, wsData.length)

    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(wb, ws, 'Sheet1')

    // 使用xlsx-style导出以支持样式
    const wbout = XLSXS.write(wb, { bookType: 'xlsx', type: 'binary' })

    // 转换binary为ArrayBuffer
    function s2ab(s) {
      const buf = new ArrayBuffer(s.length)
      const view = new Uint8Array(buf)
      for (let i = 0; i < s.length; i++) view[i] = s.charCodeAt(i) & 0xff
      return buf
    }

    const blob = new Blob([s2ab(wbout)], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
    saveAs(blob, `${filename}.xlsx`)
  } catch (error) {
    console.error('导出失败:', error)
    throw new Error('导出失败: ' + error.message)
  }
}

// 创建工作表数据
function createSheetData(data, useTo, hasTax) {
  const rows = []
  // 计算总金额和总数量
  let totalAmount = 0
  let totalCount = 0
  data.forEach(item => {
    if (useTo === 2) {
      totalAmount += item.total || 0
      totalCount += item.count || 0
    } else {
      totalAmount += item.quotedPrice || 0
      totalCount += item.number || 0
    }
  })

  // 第一行：抗震支架工程
  rows.push(['抗震支架工程', '', '', '', '', '', ''])

  // 第二行：河北世盛金属制品有限公司清单及报价表
  rows.push(['河北世盛金属制品有限公司清单及报价表', '', '', '', '', '', ''])

  // 第三行：空行
  rows.push(['抗震支架明细表', '', '', '', '', '', ''])

  // 第四行：项目信息
  rows.push(['项目名称', '', '', '报价来源', '', '河北世盛金属制品有限公司', ''])
  // 第五行：项目性质
  rows.push(['项目性质', '居住建筑', '', '报价联系人', '', '', ''])
  // 第六行：项目地址
  rows.push(['项目地址', '', '', '日期', '', '', ''])

  // 第七行：设计依据
  rows.push(['设计依据', 'GB50981-2014 CJ/T476-2015', '', '', '备注', '', ''])

  // 第八行：设防烈度
  rows.push(['设防烈度', '按最低标准抗震设防烈度以 6 度计算', '', '', '13%增值税专用发票', '', ''])

  // 第九行：设计系统
  rows.push(['设计系统', '给排水，喷淋系统/防排烟系统/动力系统', '', '', '', '', ''])

  // 第十行：表面处理
  rows.push(['表面处理', '槽钢（电镀锌）+配件（电镀锌）', '', '', '', '', ''])
  // 第十一行：管线标高
  rows.push(['管线标高', '该报价标高按1米标高', '', '', '合计套数：', '', totalCount.toFixed(3)])

  // 第十二行：公司宗旨（含总金额）
  rows.push(['公司宗旨', '致力于提供客户安全、牢固、快速的设计及解决方案', '', '', '合计金额', '', `￥${totalAmount.toFixed(2)}`])

  // 第十三行：表头
  if (useTo === 2) {
    // 一键报价表头
    rows.push(['', '', '内容', '编号', '数量', '单价', '金额'])
  } else {
    // 拆零表头
    rows.push(['', '', '抗震支架名称', '抗震支架编号', '数量', '单价', '金额'])
  }
  // 数据行
  data.forEach((item, index) => {
    const row = ['', '']

    if (useTo === 2) {
      row.push(item.content || '')
      row.push('') // 编号为空
      row.push(item.count || 0)
      row.push(item.unitPrice || 0)
      row.push(item.total || 0)
    } else {
      row.push(item.materialName || '')
      row.push(item.specs || '')
      row.push(item.number || 0)
      row.push(item.unitPrice || 0)
      row.push(item.quotedPrice || 0)
    }

    rows.push(row)
  })
  // 合计行
  rows.push(['', '', '', '合计数量', totalCount.toFixed(3), '合计金额', totalAmount.toFixed(2)])
  // 尾部说明文字（6行合并成一个单元格）
  const footerText = `根据GB50981-2014《建筑机电工程抗震设计规范》1.0.4规定，抗震设防烈度为6度及6度以上地区的建筑机电工程必须进行抗震设计。
1、本工程报价范围仅消防系统：/给水系统/暖通系统/电气系统的抗震支吊架，如设计范围与要求范围不一致，请及时与我公司联系；
2、本报价已包含管线系统的布点设计、力学荷载计算、安装技术指导等服务；
3、深化设计方案以甲方最终确认的安装数量为准；
4、本报价以距离楼板1M高度进行确定，如超过1M需重新报价；
5、本报价是基于合同签署当日原材料市场基准价，当合同未履约或延迟履约时原材料涨幅超过3%时，甲方须按合同总额补充差价；
6、C型槽钢采用热浸锌41*2.0，配件为电镀锌；如有其他要求需重新报价；
7、以上价格为含税出厂价，含安装，含运输；
8、签订合同预付30%，货到现场付60%，安装验收后付10%（验收期为一个月，未验收视为验收通过）。`

  // 添加尾部说明文字（第一行包含所有内容，后续5行为空）
  rows.push([footerText, '', '', '', '', '', ''])
  for (let i = 0; i < 5; i++) {
    rows.push(['', '', '', '', '', '', ''])
  }

  return rows
}

// 设置工作表样式
function setSheetStyles(ws, wsData, useTo, hasTax) {
  const range = XLSX.utils.decode_range(ws['!ref'])

  // 设置合并单元格
  if (!ws['!merges']) ws['!merges'] = []

  // 第一行合并 A-G
  ws['!merges'].push({ s: { r: 0, c: 0 }, e: { r: 0, c: 6 } })

  // 第二行合并 A-G
  ws['!merges'].push({ s: { r: 1, c: 0 }, e: { r: 1, c: 6 } })

  // 第三行合并 A-G
  ws['!merges'].push({ s: { r: 2, c: 0 }, e: { r: 2, c: 6 } })
  // 第四行合并设置
  ws['!merges'].push({ s: { r: 3, c: 1 }, e: { r: 3, c: 2 } }) // B-C
  ws['!merges'].push({ s: { r: 3, c: 3 }, e: { r: 3, c: 4 } }) // D-E
  ws['!merges'].push({ s: { r: 3, c: 5 }, e: { r: 3, c: 6 } }) // F-G

  // 第五行合并
  ws['!merges'].push({ s: { r: 4, c: 1 }, e: { r: 4, c: 2 } }) // B-C
  ws['!merges'].push({ s: { r: 4, c: 3 }, e: { r: 4, c: 4 } }) // D-E
  ws['!merges'].push({ s: { r: 4, c: 5 }, e: { r: 4, c: 6 } }) // F-G

  // 第六行合并
  ws['!merges'].push({ s: { r: 5, c: 1 }, e: { r: 5, c: 2 } }) // B-C
  ws['!merges'].push({ s: { r: 5, c: 3 }, e: { r: 5, c: 4 } }) // D-E
  ws['!merges'].push({ s: { r: 5, c: 5 }, e: { r: 5, c: 6 } }) // F-G

  // 第七行合并
  ws['!merges'].push({ s: { r: 6, c: 1 }, e: { r: 6, c: 3 } }) // B-D
  ws['!merges'].push({ s: { r: 6, c: 5 }, e: { r: 6, c: 6 } }) // F-G

  // 第八到十行合并
  for (let i = 7; i <= 9; i++) {
    ws['!merges'].push({ s: { r: i, c: 1 }, e: { r: i, c: 3 } }) // B-D
    ws['!merges'].push({ s: { r: i, c: 4 }, e: { r: i, c: 6 } }) // E-G
  }

  // 第十一、十二行合并
  // 第十一行特殊处理
  ws['!merges'].push({ s: { r: 10, c: 1 }, e: { r: 10, c: 3 } }) // B-D
  ws['!merges'].push({ s: { r: 10, c: 4 }, e: { r: 10, c: 5 } }) // E-F (合计套数文字)
  
  // 第十二行
  ws['!merges'].push({ s: { r: 11, c: 1 }, e: { r: 11, c: 3 } }) // B-D
  ws['!merges'].push({ s: { r: 11, c: 4 }, e: { r: 11, c: 5 } }) // E-F

  // 尾部说明文字合并（6行A-G列合并成一个大单元格）
  const footerStartRow = wsData.length - 6
  ws['!merges'].push({ s: { r: footerStartRow, c: 0 }, e: { r: footerStartRow + 5, c: 6 } })

  // 应用样式到单元格
  for (let R = range.s.r; R <= range.e.r; ++R) {
    for (let C = range.s.c; C <= range.e.c; ++C) {
      const cellAddress = XLSX.utils.encode_cell({ r: R, c: C })
      if (!ws[cellAddress]) continue

      // 创建基础样式对象
      let cellStyle = {
        font: { name: '宋体', sz: 11 },
        alignment: { vertical: 'center', horizontal: 'center' },
        border: {
          top: { style: 'thin', color: { rgb: '000000' } },
          bottom: { style: 'thin', color: { rgb: '000000' } },
          left: { style: 'thin', color: { rgb: '000000' } },
          right: { style: 'thin', color: { rgb: '000000' } }
        }
      }      // 根据行号和列号应用特定样式
      if (R === 0) {
        // 第一行：抗震支架工程
        cellStyle.font = { name: '宋体', sz: 12, bold: true }
        cellStyle.alignment = { vertical: 'center', horizontal: 'center' }
      } else if (R === 1) {
        // 第二行：河北世盛金属制品有限公司清单及报价表
        cellStyle.font = { name: '宋体', sz: 22, bold: true }
        cellStyle.alignment = { vertical: 'center', horizontal: 'center' }
        cellStyle.fill = { fgColor: { rgb: '91AADF' } }
      } else if (R === 2) {
        // 第三行：抗震支架明细表
        cellStyle.font = { name: '宋体', sz: 12, bold: true }
        cellStyle.alignment = { vertical: 'center', horizontal: 'center' }
      } else if (R === 3) {
        // 第四行：项目信息
        cellStyle.font = { name: '宋体', sz: 11 }
        cellStyle.alignment = { vertical: 'center', horizontal: 'left' }
        
        if (C === 0) {
          // A列：项目名称
          cellStyle.font.bold = true
        } else if (C === 3) {
          // D列：报价来源
          cellStyle.font.bold = true
          cellStyle.alignment.horizontal = 'right'
        } else if (C === 5) {
          // F列：河北世盛金属制品有限公司
          cellStyle.font.bold = true
          cellStyle.alignment.horizontal = 'left'
        }
      } else if (R === 4) {
        // 第五行：项目性质
        cellStyle.font = { name: '宋体', sz: 11 }
        cellStyle.alignment = { vertical: 'center', horizontal: 'center' }
        
        if (C === 0) {
          // A列：项目性质
          cellStyle.font.bold = true
          cellStyle.alignment.horizontal = 'left'
        } else if (C === 1) {
          // B列：居住建筑 - 红色
          cellStyle.font.color = { rgb: 'FF0000' }
        } else if (C === 3) {
          // D列：报价联系人
          cellStyle.font.bold = true
          cellStyle.alignment.horizontal = 'right'
        } else if (C === 5) {
          // F列：空
          cellStyle.alignment.horizontal = 'left'
        }
      } else if (R === 5) {
        // 第六行：项目地址
        cellStyle.font = { name: '宋体', sz: 11 }
        cellStyle.alignment = { vertical: 'center', horizontal: 'left' }
        
        if (C === 0) {
          // A列：项目地址
          cellStyle.font.bold = true
        } else if (C === 3) {
          // D列：日期
          cellStyle.font.bold = true
          cellStyle.alignment.horizontal = 'right'
        } else if (C === 1 || C === 5) {
          // B列和F列：垂直居中
          cellStyle.alignment.horizontal = 'center'
        }
      } else if (R === 6) {
        // 第七行：设计依据
        cellStyle.font = { name: '宋体', sz: 11 }
        cellStyle.alignment = { vertical: 'center', horizontal: 'left' }
        
        if (C === 0) {
          // A列：设计依据
          cellStyle.font.bold = true
        } else if (C === 1) {
          // B列：GB50981-2014 CJ/T476-2015
          cellStyle.alignment.horizontal = 'center'
        } else if (C === 4) {
          // E列：备注
          cellStyle.font.bold = true
          cellStyle.alignment.horizontal = 'right'
        } else if (C === 5) {
          // F列：空
          cellStyle.alignment.horizontal = 'left'
        }
      } else if (R === 7) {
        // 第八行：设防烈度
        cellStyle.font = { name: '宋体', sz: 11 }
        cellStyle.alignment = { vertical: 'center', horizontal: 'center' }
        
        if (C === 0) {
          // A列：设防烈度
          cellStyle.font.bold = true
          cellStyle.alignment.horizontal = 'left'
        } else if (C >= 4 && C <= 6) {
          // E-G列：红色
          cellStyle.font.color = { rgb: 'FF0000' }
        }
      } else if (R === 8) {
        // 第九行：设计系统
        cellStyle.font = { name: '宋体', sz: 11 }
        cellStyle.alignment = { vertical: 'center', horizontal: 'center' }
        
        if (C === 0) {
          // A列：设计系统
          cellStyle.font.bold = true
          cellStyle.alignment.horizontal = 'left'
        } else if (C === 1) {
          // B列：给排水，喷淋系统/防排烟系统/动力系统 - 红色
          cellStyle.font.color = { rgb: 'FF0000' }
        }
      } else if (R === 9) {
        // 第十行：表面处理
        cellStyle.font = { name: '宋体', sz: 11 }
        cellStyle.alignment = { vertical: 'center', horizontal: 'center' }
        
        if (C === 0) {
          // A列：表面处理
          cellStyle.font.bold = true
          cellStyle.alignment.horizontal = 'left'
        } else if (C === 1) {
          // B列：槽钢（电镀锌）+配件（电镀锌） - 红色
          cellStyle.font.color = { rgb: 'FF0000' }
        }
      } else if (R === 10) {
        // 第十一行：管线标高
        cellStyle.font = { name: '宋体', sz: 11 }
        cellStyle.alignment = { vertical: 'center', horizontal: 'center' }
        
        if (C === 0) {
          // A列：管线标高
          cellStyle.font.bold = true
          cellStyle.alignment.horizontal = 'left'
        } else if (C === 1) {
          // B列：该报价标高按1米标高 - 红色
          cellStyle.font.color = { rgb: 'FF0000' }
        } else if (C === 4) {
          // E列：合计套数文字 - 加粗并居中
          cellStyle.alignment.horizontal = 'center'
        } else if (C === 6) {
          // G列：合计套数数值 - 加粗并居中
          cellStyle.alignment.horizontal = 'center'
        }
      } else if (R === 11) {
        // 第十二行：公司宗旨
        cellStyle.font = { name: '宋体', sz: 11 }
        cellStyle.alignment = { vertical: 'center', horizontal: 'center' }
        
        if (C === 0) {
          // A列：公司宗旨
          cellStyle.font.bold = true
          cellStyle.alignment.horizontal = 'left'
        } else if (C === 1) {
          // B列：致力于提供客户安全、牢固、快速的设计及解决方案 - 红色
          cellStyle.font.color = { rgb: 'FF0000' }
        } else if (C === 4) {
          // E列：合计金额
          // 保持默认样式
        } else if (C === 6) {
          // G列：￥0.00 - 红色加粗
          cellStyle.font.bold = true
          cellStyle.font.color = { rgb: 'FF0000' }
        }
      } else if (R === 12) {
        // 第十三行：表头行
        cellStyle.font = { name: '宋体', sz: 11, bold: true }
        cellStyle.alignment = { vertical: 'center', horizontal: 'center' }
      } else if (R >= 13 && R < wsData.length - 7) {
        // 数据行
        cellStyle.font = { name: '宋体', sz: 11 }
        cellStyle.alignment = { vertical: 'center', horizontal: 'center' }
      } else if (R === wsData.length - 7) {
        // 合计行
        cellStyle.font = { name: '宋体', sz: 11 }
        cellStyle.alignment = { vertical: 'center', horizontal: 'center' }      } else if (R >= wsData.length - 6) {
        // 尾部说明文字
        cellStyle.font = { name: '宋体', sz: 11 } // 稍微减小字体以容纳更多内容
        cellStyle.alignment = { 
          vertical: 'top', 
          horizontal: 'left', 
          wrapText: true,
          indent: 0
        }
        cellStyle.border = {
          top: { style: 'thin', color: { rgb: '000000' } },
          bottom: { style: 'thin', color: { rgb: '000000' } },
          left: { style: 'thin', color: { rgb: '000000' } },
          right: { style: 'thin', color: { rgb: '000000' } }
        }
      }

      ws[cellAddress].s = cellStyle
    }
  }
}

// 设置列宽
function setColumnWidths(ws) {
  ws['!cols'] = [
    { wch: 9 }, // A列
    { wch: 15.13 }, // B列
    { wch: 24 }, // C列
    { wch: 25.13 }, // D列
    { wch: 6.63 }, // E列
    { wch: 8.38 }, // F列
    { wch: 15.38 } // G列
  ]
}

// 设置行高
function setRowHeights(ws, totalRows) {
  if (!ws['!rows']) ws['!rows'] = []

  // 首先为所有行设置默认行高28
  for (let i = 0; i < totalRows; i++) {
    ws['!rows'][i] = { 
      hpt: 28,
      hpx: 28
    }
  }

  // 然后设置特定行的行高（注意：xlsx-style中行高单位是点，不是像素）
  const specificRowHeights = {
    0: 26,  // 第一行：抗震支架工程
    1: 36,  // 第二行：河北世盛金属制品有限公司清单及报价表（加大字体需要更高）
    2: 28,  // 第三行：抗震支架明细表
    3: 32,  // 第四行：项目信息
    4: 34,  // 第五行：项目性质
    5: 28,  // 第六行：项目地址
    6: 28,  // 第七行：设计依据
    7: 28,  // 第八行：设防烈度
    8: 28,  // 第九行：设计系统
    9: 28,  // 第十行：表面处理
    10: 28, // 第十一行：管线标高
    11: 28, // 第十二行：公司宗旨
    12: 28  // 第十三行：表头
  }

  // 应用特定行高
  for (const [rowIndex, height] of Object.entries(specificRowHeights)) {
    const index = parseInt(rowIndex)
    if (index < totalRows) {
      ws['!rows'][index] = { 
        hpt: height,
        hpx: height
      }
    }
  }

  // 合计行
  const footerStartRow = totalRows - 6
  const summaryRowIndex = footerStartRow - 1
  if (summaryRowIndex >= 0 && summaryRowIndex < totalRows) {
    ws['!rows'][summaryRowIndex] = { 
      hpt: 30,
      hpx: 30
    }
  }
  
  // 尾部说明文字行设置特殊行高
  for (let i = footerStartRow; i < totalRows; i++) {
    if (i === footerStartRow) {
      // 第一行设置超高行高以容纳所有文字
      ws['!rows'][i] = { 
        hpt: 30, // 超高行高以显示完整的文字内容
        hpx: 30
      }
    } else {
      // 其他行设置较小的行高
      ws['!rows'][i] = { 
        hpt: 28,
        hpx: 28
      }
    }
  }
}
