import store from '@/store'
// 保存好友列表
export function saveFriends(friends) {
  const userId = store.getters.userId || store.getters.name
  const fileName = `chat_${userId}`
  try {
    //获取本地的好友信息，如果没有生成{"friends": []}
    const friendsContent = localStorage.getItem(fileName) || '{"friends": []}'
    //将获取的数据
    const friendsList = JSON.parse(friendsContent)
    //将新的好友列表追加到原有的好友列表中
    friendsList.friends = friends
    //本地存储
    localStorage.setItem(fileName, JSON.stringify(friendsList))
  } catch (e) {
    console.error(e)
  }
}
// 获取好友列表
export function getFriends() {
  const userId = store.getters.userId || store.getters.name
  const fileName = `chat_${userId}`
  try {
    //获取信息,如果不存在,返回{"friends": []}
    const friendsContent = localStorage.getItem(fileName) || '{"friends": []}'
    return JSON.parse(friendsContent).friends || []
  } catch (e) {
    console.error(e)
    return []
  }
}
// 保存群组列表
export function saveGroups(groups) {
  const userId = store.getters.userId || store.getters.name
  const fileName = `chat_${userId}`
  try {
    //获取本地的群组信息，如果没有生成{"groups": []}
    const groupsContent = localStorage.getItem(fileName) || '{"groups": []}'
    //将获取的数据
    const groupsList = JSON.parse(groupsContent)
    //将新的群组列表追加到原有的群组列表中
    groupsList.groups = groups
    //本地存储
    localStorage.setItem(fileName, JSON.stringify(groupsList))
  } catch (e) {
    console.error(e)
  }
}
// 获取群组列表
export function getGroups() {
  const userId = store.getters.userId || store.getters.name
  const fileName = `chat_${userId}`
  try {
    //获取信息,如果不存在,返回{"groups": []}
    const groupsContent = localStorage.getItem(fileName) || '{"groups": []}'
    return JSON.parse(groupsContent).groups || []
  } catch (e) {
    console.error(e)
    return []
  }
}
// 保存临时好友列表
export function saveProvisionals(friends) {
  const userId = store.getters.userId || store.getters.name
  const fileName = `chat_${userId}`
  try {
    //获取本地的好友信息，如果没有生成{"provisionals": []}
    const friendsContent = localStorage.getItem(fileName) || '{"provisionals": []}'
    //将获取的数据
    const friendsList = JSON.parse(friendsContent)
    //将新的好友列表追加到原有的好友列表中
    friendsList.provisionals = friends
    //本地存储
    localStorage.setItem(fileName, JSON.stringify(friendsList))
  } catch (e) {
    console.error(e)
  }
}
// 获取临时好友列表
export function getProvisionals() {
  const userId = store.getters.userId || store.getters.name
  const fileName = `chat_${userId}`
  try {
    //获取信息,如果不存在,返回{"provisionals": []}
    const friendsContent = localStorage.getItem(fileName) || '{"provisionals": []}'
    return JSON.parse(friendsContent).provisionals || []
  } catch (e) {
    console.error(e)
    return []
  }
}
// 保存消息数量
export function saveChatNum(count) {
  const userId = store.getters.userId || store.getters.name
  const fileName = `chat_${userId}`
  try {
    //获取本地的未读消息数量，如果没有生成{"chatNum": 0}
    const countContent = localStorage.getItem(fileName) || '{"chatNum": 0}'
    //将获取的数据
    const countList = JSON.parse(countContent)
    //将新的未读消息数量追加到原有的未读消息数量中
    countList.chatNum = count
    //本地存储
    localStorage.setItem(fileName, JSON.stringify(countList))
  } catch (e) {
    console.error(e)
  }
}
// 获取消息数量
export function getChatNum() {
  const userId = store.getters.userId || store.getters.name
  const fileName = `chat_${userId}`
  try {
    //获取信息,如果不存在,返回{"unreadCount": 0}
    const countContent = localStorage.getItem(fileName) || '{"chatNum": 0}'
    return JSON.parse(countContent).chatNum || 0
  } catch (e) {
    console.error(e)
    return 0
  }
}
// 更新群组缓存的工具函数
export function updateGroupsInCache(updatedGroups, userId = null) {
  const currentUserId = userId || store.getters.userId || store.getters.name
  const chatFileName = `chat_${currentUserId}`
  try {
    const chatContent = localStorage.getItem(chatFileName)
    if (chatContent) {
      const chatData = JSON.parse(chatContent)
      chatData.groups = updatedGroups
      localStorage.setItem(chatFileName, JSON.stringify(chatData))
    }
  } catch (e) {
    console.error('更新群组缓存时出错:', e)
  }
}
