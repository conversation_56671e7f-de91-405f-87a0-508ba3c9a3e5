import { parseTime } from './ruoyi'
import emojiList from '@/components/emoji'

const OSS = require('ali-oss')
/**
 * 表格时间格式化
 */
export function formatDate(cellValue) {
  if (cellValue == null || cellValue == '') return ''
  var date = new Date(cellValue)
  var year = date.getFullYear()
  var month = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1
  var day = date.getDate() < 10 ? '0' + date.getDate() : date.getDate()
  var hours = date.getHours() < 10 ? '0' + date.getHours() : date.getHours()
  var minutes = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()
  var seconds = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds()
  return year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds
}
/**
 * @param {number} time
 * @param {string} option
 * @returns {string}
 */
export function formatTime(time, option) {
  if (('' + time).length === 10) {
    time = parseInt(time) * 1000
  } else {
    time = +time
  }
  const d = new Date(time)
  const now = Date.now()
  const diff = (now - d) / 1000
  if (diff < 30) {
    return '刚刚'
  } else if (diff < 3600) {
    // less 1 hour
    return Math.ceil(diff / 60) + '分钟前'
  } else if (diff < 3600 * 24) {
    return Math.ceil(diff / 3600) + '小时前'
  } else if (diff < 3600 * 24 * 2) {
    return '1天前'
  }
  if (option) {
    return parseTime(time, option)
  } else {
    return d.getMonth() + 1 + '月' + d.getDate() + '日' + d.getHours() + '时' + d.getMinutes() + '分'
  }
}
/**
 * @param {string} url
 * @returns {Object}
 */
export function getQueryObject(url) {
  url = url == null ? window.location.href : url
  const search = url.substring(url.lastIndexOf('?') + 1)
  const obj = {}
  const reg = /([^?&=]+)=([^?&=]*)/g
  search.replace(reg, (rs, $1, $2) => {
    const name = decodeURIComponent($1)
    let val = decodeURIComponent($2)
    val = String(val)
    obj[name] = val
    return rs
  })
  return obj
}
/**
 * @param {string} input value
 * @returns {number} output value
 */
export function byteLength(str) {
  // returns the byte length of an utf8 string
  let s = str.length
  for (var i = str.length - 1; i >= 0; i--) {
    const code = str.charCodeAt(i)
    if (code > 0x7f && code <= 0x7ff) s++
    else if (code > 0x7ff && code <= 0xffff) s += 2
    if (code >= 0xdc00 && code <= 0xdfff) i--
  }
  return s
}
/**
 * @param {Array} actual
 * @returns {Array}
 */
export function cleanArray(actual) {
  const newArray = []
  for (let i = 0; i < actual.length; i++) {
    if (actual[i]) {
      newArray.push(actual[i])
    }
  }
  return newArray
}
/**
 * @param {Object} json
 * @returns {Array}
 */
export function param(json) {
  if (!json) return ''
  return cleanArray(
    Object.keys(json).map(key => {
      if (json[key] === undefined) return ''
      return encodeURIComponent(key) + '=' + encodeURIComponent(json[key])
    })
  ).join('&')
}
/**
 * @param {string} url
 * @returns {Object}
 */
export function param2Obj(url) {
  const search = decodeURIComponent(url.split('?')[1]).replace(/\+/g, ' ')
  if (!search) {
    return {}
  }
  const obj = {}
  const searchArr = search.split('&')
  searchArr.forEach(v => {
    const index = v.indexOf('=')
    if (index !== -1) {
      const name = v.substring(0, index)
      const val = v.substring(index + 1, v.length)
      obj[name] = val
    }
  })
  return obj
}
/**
 * @param {string} val
 * @returns {string}
 */
export function html2Text(val) {
  const div = document.createElement('div')
  div.innerHTML = val
  return div.textContent || div.innerText
}
/**
 * Merges two objects, giving the last one precedence
 * @param {Object} target
 * @param {(Object|Array)} source
 * @returns {Object}
 */
export function objectMerge(target, source) {
  if (typeof target !== 'object') {
    target = {}
  }
  if (Array.isArray(source)) {
    return source.slice()
  }
  Object.keys(source).forEach(property => {
    const sourceProperty = source[property]
    if (typeof sourceProperty === 'object') {
      target[property] = objectMerge(target[property], sourceProperty)
    } else {
      target[property] = sourceProperty
    }
  })
  return target
}
/**
 * @param {HTMLElement} element
 * @param {string} className
 */
export function toggleClass(element, className) {
  if (!element || !className) {
    return
  }
  let classString = element.className
  const nameIndex = classString.indexOf(className)
  if (nameIndex === -1) {
    classString += '' + className
  } else {
    classString = classString.substr(0, nameIndex) + classString.substr(nameIndex + className.length)
  }
  element.className = classString
}
/**
 * @param {string} type
 * @returns {Date}
 */
export function getTime(type) {
  if (type === 'start') {
    return new Date().getTime() - 3600 * 1000 * 24 * 90
  } else {
    return new Date(new Date().toDateString())
  }
}
/**
 * @param {Function} func
 * @param {number} wait
 * @param {boolean} immediate
 * @return {*}
 */
export function debounce(func, wait, immediate) {
  let timeout, args, context, timestamp, result
  const later = function () {
    // 据上一次触发时间间隔
    const last = +new Date() - timestamp
    // 上次被包装函数被调用时间间隔 last 小于设定时间间隔 wait
    if (last < wait && last > 0) {
      timeout = setTimeout(later, wait - last)
    } else {
      timeout = null
      // 如果设定为immediate===true，因为开始边界已经调用过了此处无需调用
      if (!immediate) {
        result = func.apply(context, args)
        if (!timeout) context = args = null
      }
    }
  }
  return function (...args) {
    context = this
    timestamp = +new Date()
    const callNow = immediate && !timeout
    // 如果延时不存在，重新设定延时
    if (!timeout) timeout = setTimeout(later, wait)
    if (callNow) {
      result = func.apply(context, args)
      context = args = null
    }
    return result
  }
}
/**
 * This is just a simple version of deep copy
 * Has a lot of edge cases bug
 * If you want to use a perfect deep copy, use lodash's _.cloneDeep
 * @param {Object} source
 * @returns {Object}
 */
export function deepClone(source) {
  if (!source && typeof source !== 'object') {
    throw new Error('error arguments', 'deepClone')
  }
  const targetObj = source.constructor === Array ? [] : {}
  Object.keys(source).forEach(keys => {
    if (source[keys] && typeof source[keys] === 'object') {
      targetObj[keys] = deepClone(source[keys])
    } else {
      targetObj[keys] = source[keys]
    }
  })
  return targetObj
}
/**
 * @param {Array} arr
 * @returns {Array}
 */
export function uniqueArr(arr) {
  return Array.from(new Set(arr))
}
/**
 * @returns {string}
 */
export function createUniqueString() {
  const timestamp = +new Date() + ''
  const randomNum = parseInt((1 + Math.random()) * 65536) + ''
  return (+(randomNum + timestamp)).toString(32)
}
/**
 * Check if an element has a class
 * @param {HTMLElement} elm
 * @param {string} cls
 * @returns {boolean}
 */
export function hasClass(ele, cls) {
  return !!ele.className.match(new RegExp('(\\s|^)' + cls + '(\\s|$)'))
}
/**
 * Add class to element
 * @param {HTMLElement} elm
 * @param {string} cls
 */
export function addClass(ele, cls) {
  if (!hasClass(ele, cls)) ele.className += ' ' + cls
}
/**
 * Remove class from element
 * @param {HTMLElement} elm
 * @param {string} cls
 */
export function removeClass(ele, cls) {
  if (hasClass(ele, cls)) {
    const reg = new RegExp('(\\s|^)' + cls + '(\\s|$)')
    ele.className = ele.className.replace(reg, ' ')
  }
}
export function makeMap(str, expectsLowerCase) {
  const map = Object.create(null)
  const list = str.split(',')
  for (let i = 0; i < list.length; i++) {
    map[list[i]] = true
  }
  return expectsLowerCase ? val => map[val.toLowerCase()] : val => map[val]
}
export const exportDefault = 'export default '
export const beautifierConf = {
  html: {
    indent_size: '2',
    indent_char: ' ',
    max_preserve_newlines: '-1',
    preserve_newlines: false,
    keep_array_indentation: false,
    break_chained_methods: false,
    indent_scripts: 'separate',
    brace_style: 'end-expand',
    space_before_conditional: true,
    unescape_strings: false,
    jslint_happy: false,
    end_with_newline: true,
    wrap_line_length: '110',
    indent_inner_html: true,
    comma_first: false,
    e4x: true,
    indent_empty_lines: true
  },
  js: {
    indent_size: '2',
    indent_char: ' ',
    max_preserve_newlines: '-1',
    preserve_newlines: false,
    keep_array_indentation: false,
    break_chained_methods: false,
    indent_scripts: 'normal',
    brace_style: 'end-expand',
    space_before_conditional: true,
    unescape_strings: false,
    jslint_happy: true,
    end_with_newline: true,
    wrap_line_length: '110',
    indent_inner_html: true,
    comma_first: false,
    e4x: true,
    indent_empty_lines: true
  }
}
// 首字母大小
export function titleCase(str) {
  return str.replace(/( |^)[a-z]/g, L => L.toUpperCase())
}
// 下划转驼峰
export function camelCase(str) {
  return str.replace(/_[a-z]/g, str1 => str1.substr(-1).toUpperCase())
}
export function isNumberStr(str) {
  return /^[+-]?(0|([1-9]\d*))(\.\d+)?$/g.test(str)
}
// 时间倒计时（天、时、分、秒）
export function expireTimeFormat(val) {
  let result = ''
  const dateEnd = new Date(val)
  const dateBegin = new Date()
  const dateDiff = dateEnd.getTime() - dateBegin.getTime()
  if (dateDiff < 0) {
    result = '已过期'
  } else {
    // 计算相差天
    const days = Math.floor(dateDiff / (24 * 60 * 60 * 1000))
    // 计算相差小时
    const leave1 = dateDiff % (24 * 60 * 60 * 1000)
    const hours = Math.floor(leave1 / (60 * 60 * 1000))
    // 计算相差分
    const leave2 = leave1 % (60 * 60 * 1000)
    const minutes = Math.floor(leave2 / (60 * 1000))
    // 计算相差秒
    const leave3 = leave2 % (60 * 1000)
    const seconds = Math.round(leave3 / 1000)
    if (days > 0) result += days + '天'
    if (hours > 0) result += hours + '小时'
    if (minutes > 0) result += minutes + '分'
    // if (seconds > 0) result += seconds + '秒'
  }
  return result
}
// 数字金额转大写
export function lowerConversionUpper(money) {
  var cnNums = new Array('零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖')
  var cnIntRadice = new Array('', '拾', '佰', '仟')
  var cnIntUnits = new Array('', '万', '亿', '兆')
  var cnDecUnits = new Array('角', '分', '毫', '厘')
  var cnInteger = '整'
  var cnIntLast = '元'
  var maxNum = 999999999999999.9999
  var integerNum
  var decimalNum
  var chineseStr = ''
  var parts
  if (money == '') {
    return ''
  }
  money = parseFloat(money)
  if (money >= maxNum) {
    return ''
  }
  if (money == 0) {
    chineseStr = cnNums[0] + cnIntLast + cnInteger
    return chineseStr
  }
  money = money.toString()
  if (money.indexOf('.') == -1) {
    integerNum = money
    decimalNum = ''
  } else {
    parts = money.split('.')
    integerNum = parts[0]
    decimalNum = parts[1].substr(0, 4)
  }
  if (parseInt(integerNum, 10) > 0) {
    let zeroCount = 0
    let IntLen = integerNum.length
    for (let i = 0; i < IntLen; i++) {
      let n = integerNum.substr(i, 1)
      let p = IntLen - i - 1
      let q = p / 4
      let m = p % 4
      if (n == '0') {
        zeroCount++
      } else {
        if (zeroCount > 0) {
          chineseStr += cnNums[0]
        }
        zeroCount = 0
        chineseStr += cnNums[parseInt(n)] + cnIntRadice[m]
      }
      if (m == 0 && zeroCount < 4) {
        chineseStr += cnIntUnits[q]
      }
    }
    chineseStr += cnIntLast
  }
  if (decimalNum != '') {
    let decLen = decimalNum.length
    for (let i = 0; i < decLen; i++) {
      let n = decimalNum.substr(i, 1)
      if (n != '0') {
        chineseStr += cnNums[Number(n)] + cnDecUnits[i]
      }
    }
  }
  if (chineseStr == '') {
    chineseStr += cnNums[0] + cnIntLast + cnInteger
  } else if (decimalNum == '') {
    chineseStr += cnInteger
  }
  return chineseStr
}
/**
 * 图片旋转
 */
export function rotateBase64Img(src, edg, fileName, fileType, callback) {
  var canvas = document.createElement('canvas')
  var ctx = canvas.getContext('2d')
  var imgW // 图片宽度
  var imgH // 图片高度
  var size // canvas初始大小
  if (edg % 90 !== 0) {
    console.error('旋转角度必须是90的倍数!')
    return '旋转角度必须是90的倍数!'
  }
  edg < 0 && (edg = (edg % 360) + 360)
  const quadrant = (edg / 90) % 4 // 旋转象限
  const cutCoor = { sx: 0, sy: 0, ex: 0, ey: 0 } // 裁剪坐标
  var image = new Image()
  image.crossOrigin = 'Anonymous'
  image.src = src
  image.onload = () => {
    imgW = image.width
    imgH = image.height
    size = imgW > imgH ? imgW : imgH
    canvas.width = size * 2
    canvas.height = size * 2
    switch (quadrant) {
      case 0:
        cutCoor.sx = size
        cutCoor.sy = size
        cutCoor.ex = size + imgW
        cutCoor.ey = size + imgH
        break
      case 1:
        cutCoor.sx = size - imgH
        cutCoor.sy = size
        cutCoor.ex = size
        cutCoor.ey = size + imgW
        break
      case 2:
        cutCoor.sx = size - imgW
        cutCoor.sy = size - imgH
        cutCoor.ex = size
        cutCoor.ey = size
        break
      case 3:
        cutCoor.sx = size
        cutCoor.sy = size - imgW
        cutCoor.ex = size + imgH
        cutCoor.ey = size + imgW
        break
    }
    ctx.translate(size, size)
    ctx.rotate((edg * Math.PI) / 180)
    ctx.drawImage(image, 0, 0)
    var imgData = ctx.getImageData(cutCoor.sx, cutCoor.sy, cutCoor.ex, cutCoor.ey)
    if (quadrant % 2 === 0) {
      canvas.width = imgW
      canvas.height = imgH
    } else {
      canvas.width = imgH
      canvas.height = imgW
    }
    ctx.putImageData(imgData, 0, 0)
    callback(dataURLtoFile(canvas.toDataURL(), fileName, fileType))
    // callback(canvas.toDataURL())
  }
}
/**
 * 将 base64 转换为 file 对象
 *    dataURL：base64 格式
 *    fileName：文件名
 *    fileType：文件格式
 */
export function dataURLtoFile(dataURL, fileName, fileType) {
  const arr = dataURL.split(',')
  const mime = arr[0].match(/:(.*?);/)[1]
  const bstr = atob(arr[1])
  let n = bstr.length
  const u8arr = new Uint8Array(n)
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n)
  }
  return new File([u8arr], fileName, { type: fileType || 'image/jpg' })
}
// 正则去除html标签并截取字符串
export function removeHtmlTag(str, length = 100) {
  if (!str || str == '') return ''
  str = str.replace(/<[^>]+>/g, '')
  str = str.replace(/(\s|<br>)+/g, '')
  str = str.replace(/&nbsp;/g, '')
  str = str.replace(/&lt;/g, '<')
  str = str.replace(/&gt;/g, '>')
  str = str.replace(/&amp;/g, '&')
  str = str.replace(/&quot;/g, '"')
  str = str.replace(/&#39;/g, "'")
  if (str.length > length && length > 0) {
    str = str.substring(0, length) + '...'
  }
  return str
}
// 根据开始数量和结束数量隐藏中间的字符
export function hideMiddleStr(str, start = 3, end = 3) {
  if (!str || str == '') return ''
  if (str.length <= start + end) return str
  return str.substring(0, start) + '***' + str.substring(str.length - end)
}
// 回显评论内容表情
export function formatContent(content) {
  return content.replace(/\[.*?\]/g, function (emoji) {
    if (!emojiList.includes(emoji)) return emoji
    const src = require(`@/assets/emoji/${emoji}.png`)
    return `<img src="${src}" style="width: 20px; height: 20px; vertical-align: middle;" />`
  })
}
// 获取oss客户端
export function getOssClient() {
  return new OSS({
    endpoint: 'https://oss.ziyouke.net',
    accessKeyId: 'LTAI5tNZhEycHrXGLwKS1GfM',
    accessKeySecret: '******************************',
    region: 'oss-cn-beijing',
    authorizationV4: true,
    cname: true,
    bucket: 'zyk-pro'
  })
}
// 获取oss图片地址
export function getOssImgUrl(url) {
  if (!url || url == '' || url == null) return ''
  url = url.replace(/\/profile/g, '')
  return getOssClient().signatureUrl(url)
}
// 计算时时间是否是当年，如果是当年时间则不显示年份返回格式为MM-dd，否则显示年份，返回格式为yyyy-MM-dd，如果是当天时间则返回HH:mm
export function formatChatTime(time) {
  if (!time || time == '') return ''
  const now = new Date()
  const date = new Date(time)
  const year = date.getFullYear()
  const month = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1
  const day = date.getDate() < 10 ? '0' + date.getDate() : date.getDate()
  const hours = date.getHours() < 10 ? '0' + date.getHours() : date.getHours()
  const minutes = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()
  if (now.getFullYear() == date.getFullYear()) {
    if (now.getMonth() == date.getMonth() && now.getDate() == date.getDate()) {
      return hours + ':' + minutes
    } else {
      return month + '-' + day
    }
  } else {
    return year + '-' + month + '-' + day
  }
}
/**
 * 格式化聊天列表的时间，当年当天的显示时分，当年不同天的显示月日上下午时分，不同年的显示年月日上下午时分
 */
export function formatChatListTime(time) {
  if (!time || time == '') return ''
  const now = new Date()
  const date = new Date(time)
  const year = date.getFullYear()
  const month = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1
  const day = date.getDate() < 10 ? '0' + date.getDate() : date.getDate()
  const hours = date.getHours() < 10 ? '0' + date.getHours() : date.getHours()
  const minutes = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()
  // 上午、中午、下午、晚上
  let timeStr = ''
  if (hours >= 0 && hours < 6) {
    timeStr = '凌晨'
  } else if (hours >= 6 && hours < 12) {
    timeStr = '上午'
  } else if (hours == 12) {
    timeStr = '中午'
  } else if (hours > 12 && hours < 18) {
    timeStr = '下午'
  } else {
    timeStr = '晚上'
  }
  // 昨天、前天
  let dayStr = ''
  if (now.getFullYear() == date.getFullYear() && now.getMonth() == date.getMonth() && now.getDate() - date.getDate() == 1) {
    dayStr = '昨天'
  } else if (now.getFullYear() == date.getFullYear() && now.getMonth() == date.getMonth() && now.getDate() - date.getDate() == 2) {
    dayStr = '前天'
  } else {
    dayStr = ''
  }
  // 是当年
  if (now.getFullYear() == date.getFullYear()) {
    // 是当天
    if (now.getMonth() == date.getMonth() && now.getDate() == date.getDate()) {
      return hours + ':' + minutes
    } else {
      // 是昨天或者前天
      if (dayStr != '') {
        return dayStr + ' ' + hours + ':' + minutes
      } else {
        return month + '月' + day + '日 ' + timeStr + hours + ':' + minutes
      }
    }
  } else {
    return year + '年' + month + '月' + day + '日 ' + timeStr + hours + ':' + minutes
  }
}
// 判断聊天记录list中的时间是否需要显示，需要显示返回true，不需要显示返回false，第一个时间默认显示，紧后的时间与前一个时间相差5分钟以上显示，否则不显示
export function isShowChatTime(list, index) {
  if (index == 0) return true
  const time1 = new Date(list[index].createTime).getTime()
  const time2 = new Date(list[index - 1].createTime).getTime()
  if (time1 - time2 < 0) return true
  return time1 - time2 > 5 * 60 * 1000
}
// 正则匹配内容中所有的链接地址，判断是否为图片地址链接，如果是图片地址链接回显出图片，默认不缩放，如果需要缩放则传入宽度和高度,不是图片的添加a链接进行新窗口打开
export function formatContentUrl(content, width = 0, height = 0) {
  if (!content || content == '') return ''
  return content.replace(/http[s]?:\/\/[a-zA-Z0-9\.\-\/\?%=_&]+/g, function (url) {
    // if (/\.(jpg|jpeg|png|gif|JPG|JPEG|PNG|GIF)|(\?|\&)/.test(url)) {
    //   if (width != 0 && height != 0) {
    //     return `<img src="${url}" style="width: ${width}px; max-width: 100%; height: ${height}px;" />`
    //   }
    //   return `<img src="${url}" style="width: auto; max-width: 100%; height: auto;" />`
    // } else {
    return `<a href="${url}" target="_blank">${url}</a>`
    // }
  })
}
// 判断是否为图片，图片则显示为[图片]
export function formatContentImg(content) {
  if (!content || content == '') return ''
  return content.replace(/http[s]?:\/\/[a-zA-Z0-9\.\-\/\?%=_&]+/g, function (url) {
    // if (/\.(jpg|jpeg|png|gif|JPG|JPEG|PNG|GIF)|(\?|\&)/.test(url)) {
    //   return '[图片]'
    // } else {
    return url
    // }
  })
}
// 判断字符串是否为JSON格式
export function isJSON(str) {
  if (typeof str == 'string') {
    try {
      const obj = JSON.parse(str)
      return !!(typeof obj == 'object' && obj)
    } catch (e) {
      return false
    }
  }
}
