/**
 * @param {string} path
 * @returns {Boolean}
 */
export function isExternal(path) {
  return /^(https?:|mailto:|tel:)/.test(path)
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validUsername(str) {
  const valid_map = ['admin', 'editor']
  return valid_map.indexOf(str.trim()) >= 0
}

/**
 * @param {string} url
 * @returns {Boolean}
 */
export function validURL(url) {
  const reg = /^(https?|ftp):\/\/([a-zA-Z0-9.-]+(:[a-zA-Z0-9.&%$-]+)*@)*((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]?)(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}|([a-zA-Z0-9-]+\.)*[a-zA-Z0-9-]+\.(com|edu|gov|int|mil|net|org|biz|arpa|info|name|pro|aero|coop|museum|[a-zA-Z]{2}))(:[0-9]+)*(\/($|[a-zA-Z0-9.,?'\\+&%$#=~_-]+))*$/
  return reg.test(url)
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validLowerCase(str) {
  const reg = /^[a-z]+$/
  return reg.test(str)
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validUpperCase(str) {
  const reg = /^[A-Z]+$/
  return reg.test(str)
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validAlphabets(str) {
  const reg = /^[A-Za-z]+$/
  return reg.test(str)
}

/**
 * @param {string} email
 * @returns {Boolean}
 */
export function validEmail(email) {
  const reg = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
  return reg.test(email)
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function isString(str) {
  if (typeof str === 'string' || str instanceof String) {
    return true
  }
  return false
}

/**
 * @param {Array} arg
 * @returns {Boolean}
 */
export function isArray(arg) {
  if (typeof Array.isArray === 'undefined') {
    return Object.prototype.toString.call(arg) === '[object Array]'
  }
  return Array.isArray(arg)
}

// 是否几位小数
export function isNumberLength(length, rule, value, callback) {
  length = length ?? 2
  const reg = new RegExp(`^(-?\\d+)(.\\d{1,${length}})?$`)
  if (value == '' || value == undefined || value == null) {
    callback()
  } else {
    if (!reg.test(value) && value != '') {
      callback(new Error('小数点后最多只可以两位数字'))
    } else {
      callback()
    }
  }
}

// 是否为数字,不能为负数
export function isNumber(rule, value, callback) {
  const reg = /^(\d+)(\.\d+)?$/
  if (value == '' || value == undefined || value == null) {
    callback()
  } else {
    if (!reg.test(value) && value != '') {
      callback(new Error('请输入数字'))
    } else {
      callback()
    }
  }
}

// 是否为正整数
export function isInteger(rule, value, callback) {
  const reg = /^[1-9]\d*$/
  if (value == '' || value == undefined || value == null) {
    callback()
  } else {
    if (!reg.test(value) && value != '') {
      callback(new Error('请输入正整数'))
    } else {
      callback()
    }
  }
}

// 包括0的正整数
export function isIntegerIncludeZero(rule, value, callback) {
  const reg = /^[0-9]\d*$/
  if (value == '' || value == undefined || value == null) {
    callback()
  } else {
    if (!reg.test(value) && value != '') {
      callback(new Error('请输入正整数'))
    } else {
      callback()
    }
  }
}

// 校验统一社会信用代码
export function isCreditCode(rule, value, callback) {
  if (value == '' || value == undefined || value == null) {
    callback()
  } else {
    var firstarray = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z']
    var firstkeys = [3, 7, 9, 10, 5, 8, 4, 2]
    var secondarray = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'J', 'K', 'L', 'M', 'N', 'P', 'Q', 'R', 'T', 'U', 'W', 'X', 'Y']
    var secondkeys = [1, 3, 9, 27, 19, 26, 16, 17, 20, 29, 25, 13, 8, 24, 10, 30, 28]

    function calc(code, array1, array2, b) {
      var count = 0
      for (var i = 0; i < array2.length; i++) {
        var a = code[i]
        count += array2[i] * array1.indexOf(a)
      }
      var remainder = count % b
      return remainder === 0 ? 0 : b - remainder
    }

    var code = value.toUpperCase()
    if (code.length != 18) {
      callback(new Error('统一社会信用代码长度错误'))
    }
    var reg = /^\w\w\d{6}\w{9}\w$/
    if (!reg.test(code)) {
      callback(new Error('统一社会信用代码格式错误'))
    } else {
      let reg1 = /^[1,5,9,Y]\w\d{6}\w{9}\w$/
      if (!reg1.test(code)) {
        callback(new Error('统一社会信用代码格式错误'))
      } else {
        let reg2 = /^(11|12|13|19|21|31|32|33|34|35|41|51|52|53|54|55|61|62|59|71|72|81|91|92|93|A1|G1|J1|N1|N2|N3|Y1)\d{6}\w{9}\w$/
        if (!reg2.test(code)) {
          callback(new Error('统一社会信用代码格式错误'))
        } else {
          let reg3 = /^(11|12|13|19|21|31|32|33|34|35|41|51|52|53|54|55|61|62|59|71|72|81|91|92|93|A1|G1|J1|N1|N2|N3|Y1)\d{6}\w{9}\w$/
          if (!reg3.test(code)) {
            callback(new Error('统一社会信用代码格式错误'))
          } else {
            var firstkey = calc(code.substr(8), firstarray, firstkeys, 11)
            var firstword
            if (firstkey < 10) {
              firstword = firstkey
            }
            if (firstkey == 10) {
              firstword = 'X'
            } else if (firstkey == 11) {
              firstword = '0'
            }
            if (firstword != code.substr(16, 1)) {
              callback(new Error('统一社会信用代码校验错误'))
            } else {
              var secondkey = calc(code, secondarray, secondkeys, 31)
              var secondword = secondarray[secondkey]
              if (!secondword || secondword != code.substr(17, 1)) {
                callback(new Error('统一社会信用代码校验错误'))
              } else {
                var word = code.substr(0, 16) + firstword + secondword
                if (code != word) {
                  callback(new Error('统一社会信用代码校验错误'))
                } else {
                  callback()
                }
              }
            }
          }
        }
      }
    }
  }
}

// 校验身份证号(合法性验证、支持15位和18位、支持地址编码、出生日期、校验位验证)
export function isIdCard(rule, value, callback) {
  if (value == '' || value == undefined || value == null) {
    callback()
  } else {
    const city = {
      11: '北京',
      12: '天津',
      13: '河北',
      14: '山西',
      15: '内蒙古',
      21: '辽宁',
      22: '吉林',
      23: '黑龙江',
      31: '上海',
      32: '江苏',
      33: '浙江',
      34: '安徽',
      35: '福建',
      36: '江西',
      37: '山东',
      41: '河南',
      42: '湖北',
      43: '湖南',
      44: '广东',
      45: '广西',
      46: '海南',
      50: '重庆',
      51: '四川',
      52: '贵州',
      53: '云南',
      54: '西藏',
      61: '陕西',
      62: '甘肃',
      63: '青海',
      64: '宁夏',
      65: '新疆',
      71: '台湾',
      81: '香港',
      82: '澳门',
      91: '国外'
    }
    let tip = ''
    let pass = true
    if (!value || !/^\d{6}(18|19|20)?\d{2}(0[1-9]|1[012])(0[1-9]|[12]\d|3[01])\d{3}(\d|[xX])$/.test(value)) {
      tip = '身份证号格式错误'
      pass = false
    } else if (!city[value.substr(0, 2)]) {
      tip = '身份证号地址编码错误'
      pass = false
    } else {
      if (value.length == 18) {
        value = value.replace(/x$/, 'X')
        let code = value.split('')
        let factor = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
        let parity = [1, 0, 'X', 9, 8, 7, 6, 5, 4, 3, 2]
        let sum = 0
        let ai = 0
        let wi = 0
        for (let i = 0; i < 17; i++) {
          ai = code[i]
          wi = factor[i]
          sum += ai * wi
        }
        if (parity[sum % 11] != code[17]) {
          tip = '身份证号校验位错误'
          pass = false
        }
      }
    }
    if (!pass) {
      callback(new Error(tip))
    } else {
      callback()
    }
  }
}
