<template>
  <div class="voice" :class="{ self: isSelf }">
    <div class="voice-player" @click="handlePlay" :style="{ paddingLeft: isSelf ? `calc(${duration > 50 ? 50 : duration} * 3px)` : '0', paddingRight: isSelf ? '0' : `calc(${duration > 50 ? 50 : duration} * 3px)` }">
      <div class="voice-icon" :class="{ 'is-playing': isPlaying }" v-if="!isSelf"></div>
      <div class="voice-duration">{{ duration }}''</div>
      <div class="voice-icon" :class="{ 'is-playing': isPlaying }" v-if="isSelf"></div>
    </div>
  </div>
</template>

<script>
import Vue from 'vue'

// 创建事件总线
const EventBus = new Vue()

export default {
  name: 'VoicePlayer',
  props: {
    duration: {
      type: Number,
      required: true
    },
    audioUrl: {
      type: String,
      required: true
    },
    isSelf: {
      type: Boolean,
      required: false,
      default: false
    }
  },
  data() {
    return {
      isPlaying: false,
      audio: null,
      baseUrl: process.env.VUE_APP_BASE_API
    }
  },
  created() {
    const audioUrl = this.baseUrl + this.audioUrl

    // 创建音频实例
    this.audio = new Audio(audioUrl)

    // 监听音频播放结束事件
    this.audio.addEventListener('ended', () => {
      this.isPlaying = false
    })

    // 监听其他音频开始播放的事件
    EventBus.$on('audio-play', audioUrl => {
      if (audioUrl !== this.audioUrl && this.isPlaying) {
        this.audio.pause()
        this.isPlaying = false
      }
    })
  },
  beforeDestroy() {
    // 组件销毁前停止播放并移除事件监听
    if (this.audio) {
      this.audio.pause()
      this.audio.removeEventListener('ended', () => {})
      this.audio = null
    }
    // 移除事件监听
    EventBus.$off('audio-play')
  },
  methods: {
    handlePlay() {
      // 如果当前音频正在播放，则暂停
      if (this.isPlaying) {
        this.audio.pause()
        this.isPlaying = false
        return
      }

      // 如果当前音频未播放，则开始播放
      this.audio.currentTime = 0 // 从头开始播放
      this.audio.play()
      this.isPlaying = true

      // 通知其他音频组件停止播放
      EventBus.$emit('audio-play', this.audioUrl)
    }
  }
}
</script>

<style lang="scss" scoped>
.voice {
  font-size: 12px;
  line-height: 18px;
  padding: 6px 10px;
  color: #333333;
  background: #f2f4f8;
  border-radius: 0 10px 10px 10px;
  &.self {
    border-radius: 10px 0 10px 10px;
    .voice-duration {
      margin-right: 10px;
    }
    .voice-icon {
      background: url('~@/assets/images/voice-black.png') right center no-repeat;
      background-size: auto 100%;
    }
  }
  &-player {
    display: flex;
    align-items: center;
    cursor: pointer;
    .voice-duration {
      font-size: 16px;
      margin-left: 10px;
    }
    .voice-icon {
      width: 15px;
      height: 15px;
      background: url('~@/assets/images/voice-black.png') right center no-repeat;
      background-size: auto 100%;
      &.is-playing {
        animation: voicePlay 1s normal steps(3) infinite;
      }
    }
  }
}
@keyframes voicePlay {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 100% 0;
  }
}
</style>
