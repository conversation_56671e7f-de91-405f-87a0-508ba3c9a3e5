<template>
  <div class="containerBox">
    <el-tooltip effect="dark" content="需求" placement="bottom">
      <div class="chatfont chat-pingtai chatSend" :class="{ active: show }" @click.stop="handleOpen"></div>
    </el-tooltip>
    <el-collapse-transition>
      <div class="chatSend-box" v-show="show">
        <div class="chatSend-border">
          <div class="chatSend-search">
            <el-input v-model="queryParams.title" placeholder="请输入需求名称" style="width: 100%" @keyup.enter.native="handleSearch"></el-input>
          </div>
          <el-table v-loading="loading" :data="list" stripe class="custom-table custom-table-cell3" :height="maxHeight" v-el-table-infinite-scroll="handleMore">
            <el-table-column align="center" label="序号" type="index"></el-table-column>
            <el-table-column align="center" prop="title" label="标题" show-overflow-tooltip></el-table-column>
            <el-table-column align="center" prop="supplyEndTime" label="供货截止时间" show-overflow-tooltip></el-table-column>
            <el-table-column align="center" prop="expireTime" label="剩余有效时间" show-overflow-tooltip>
              <template slot-scope="{ row }">
                <Countdown :expireTime="row.expireTime" pattern="{d}{h}{m}" />
              </template>
            </el-table-column>
            <el-table-column align="center" prop="remark" label="备注" show-overflow-tooltip></el-table-column>
            <el-table-column align="center" prop="docUser" label="采购制单人" show-overflow-tooltip></el-table-column>
            <el-table-column align="center" width="105px">
              <template slot-scope="{ row }">
                <el-button type="primary" class="chatSend-button" @click="handleSend(row)">发送该需求</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-collapse-transition>
    <!--需求详情-->
    <el-dialog v-dialogDragBox title="需求详情" :visible.sync="open" width="920px" class="custom-dialog" append-to-body>
      <div style="padding: 0 20px">
        <el-descriptions :column="3" border labelClassName="descriptions-label">
          <el-descriptions-item contentClassName="descriptions-content">
            <template slot="label">标题</template>
            {{ info.title }}
          </el-descriptions-item>
          <el-descriptions-item contentClassName="descriptions-content">
            <template slot="label">供货截止时间</template>
            {{ info.supplyEndTime }}
          </el-descriptions-item>
          <el-descriptions-item contentClassName="descriptions-content">
            <template slot="label">剩余有效时间</template>
            <Countdown :expireTime="info.expireTime" pattern="{d}{h}{m}" />
          </el-descriptions-item>
          <el-descriptions-item contentClassName="descriptions-content">
            <template slot="label">创建时间</template>
            {{ info.createTime }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">备注</template>
            {{ info.remark }}
          </el-descriptions-item>
        </el-descriptions>
        <el-table :data="infoList" stripe class="custom-table custom-table-cell5">
          <el-table-column type="index" label="序号" align="center"></el-table-column>
          <el-table-column align="center" prop="productName" label="产品名称" show-overflow-tooltip>
            <template slot-scope="{ row }">
              <span class="table-link" @click="handleViewProduct(row)">
                <span v-if="row.source === 'common'">(公域)</span>
                <span style="color: #fe7f22" v-else>(私域)</span>
                {{ row.productName }}
              </span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="picture1" label="图片" width="75">
            <template slot-scope="{ row }">
              <image-preview :src="row.diagram" :width="50" :height="50" />
            </template>
          </el-table-column>
          <el-table-column align="center" prop="specs" label="规格" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="model" label="型号" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="weight" label="重量" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="quantity" label="计划采购数量" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="needQuantity" label="实际采购数量" show-overflow-tooltip></el-table-column>
        </el-table>
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn primary" @click="handleLink">我要报价</el-button>
      </div>
    </el-dialog>
    <!-- 产品详情 -->
    <product-dialog ref="productInfo" append-body></product-dialog>
  </div>
</template>
<script>
import { purchasingDemandDetail, purchasingDemandList, purchasingDemandReply3 } from '@/api/purchase'
import { expireTimeFormat } from '@/utils'
import ProductDialog from '@/views/public/product/dialog'

export default {
  components: { ProductDialog },
  data() {
    return {
      show: false,
      maxHeight: 100,
      isClickedOutside: false,
      loading: true,
      list: [],
      total: 0,
      isBottom: false,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        status: 1,
        filterExpire: true,
        title: ''
      },
      // 详情
      open: false,
      info: {},
      infoList: []
    }
  },
  mounted() {
    document.addEventListener('click', this.handleClickOutside, true)
    window.onresize = () => {
      return (() => {
        this.show = false
      })()
    }
  },
  beforeDestroy() {
    document.removeEventListener('click', this.handleClickOutside, true)
    window.onresize = null
  },
  methods: {
    expireTimeFormat,
    handleOpen() {
      this.maxHeight = window.innerHeight * 0.88 > 600 ? window.innerHeight * 0.88 - 150 : 450
      this.show = true
      this.radio = {}
      this.list = []
      this.total = 0
      this.queryParams.pageNum = 1
      this.queryParams.title = ''
      this.getList()
    },
    async getList() {
      this.loading = true
      purchasingDemandList(this.queryParams).then(async res => {
        const { code, msg, rows, total } = res
        if (code === 200) {
          await Promise.all(
            rows.map(async row => {
              row.show = false
              row.replyNum = 0
              const reply = await purchasingDemandReply3({ demandId: row.id })
              row.replyNum = reply.data.length
              row.products = []
              const result = await purchasingDemandDetail({ demandId: row.id })
              row.products = result.data.products
              row.sendCount = result.data.sendCount
            })
          )
          this.loading = false
          this.list = this.list.concat(rows)
          this.total = total
          this.isBottom = this.queryParams.pageNum * this.queryParams.pageSize >= total
        } else this.$modal.msgError(msg)
      })
    },
    // 滑动查看更多
    handleMore() {
      if (!this.isBottom) {
        this.queryParams.pageNum++
        this.getList()
      }
    },
    // 搜索
    handleSearch() {
      this.list = []
      this.total = 0
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 点击
    handleSend(item) {
      this.show = false
      this.$emit('handleSend', JSON.stringify({ doType: 'sendDemand', ...item }))
    },
    // 点击外部关闭
    handleClickOutside(event) {
      if (this.isClickedOutside && !this.$el.contains(event.target)) {
        this.show = false
      }
      this.isClickedOutside = true
    },
    // 查看需求详情
    handleView(data = {}) {
      this.isClickedOutside = false
      this.info = { ...data }
      this.infoList = (data.hasOwnProperty('products') && data.products.filter(item => item.productId)) || []
      this.open = true
    },
    // 查看产品详情
    handleViewProduct(item) {
      this.isClickedOutside = false
      this.$refs.productInfo.handleView(item)
    },
    // 跳转至报价
    handleLink() {
      const { href } = this.$router.resolve({ path: '/formFilling', query: { requestId: this.info.id, source: 'share' } })
      window.open(href, '_blank')
    }
  }
}
</script>

<style scoped lang="scss">
@import '~@/assets/chat/iconfont.css';
@import '~@/assets/styles/custom-new.scss';
.containerBox {
  position: relative;
}
.chatSend {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  font-size: 19px;
  cursor: pointer;
  color: #9fa3b2;
  transition: all 0.3s;
  &:hover,
  &.active {
    color: $blue;
  }
}
.chatSend-box {
  background-color: #ffffff;
  position: absolute;
  bottom: 30px;
  left: -355px;
  z-index: 99999;
  box-shadow: 0 1px 15px 0 rgba(0, 0, 0, 0.25);
  border-radius: 15px;
}
.chatSend-border {
  position: relative;
  width: 910px;
  border: 1px solid $blue;
  border-radius: 15px;
  overflow: hidden;
}
.chatSend-search {
  padding: 10px 15px;
}
.chatSend-button {
  padding: 0 10px;
  height: 30px;
  border-radius: 15px;
  background-color: transparent;
  border-color: $blue;
  color: $blue;
  font-size: 12px;
  &:hover {
    background-color: $blue;
    color: $white;
  }
}
::v-deep {
  .el-table {
    .el-table__cell {
      padding: 0;
      .el-image {
        box-shadow: none;
      }
    }
    .el-table__header-wrapper {
      col[name='gutter'] {
        width: 5px !important;
      }
    }
    .el-table__body-wrapper {
      .el-table__body {
        width: 100% !important;
      }
      &::-webkit-scrollbar {
        width: 5px;
      }
      &::-webkit-scrollbar-track {
        background-color: #f1f1f1;
        opacity: 0.15;
      }
      &::-webkit-scrollbar-thumb {
        background-color: #cccccc;
        border-radius: 5px;
      }
      &::-webkit-scrollbar-thumb:hover {
        background: #555555;
      }
    }
  }
  .el-descriptions {
    margin-bottom: 20px;
    .descriptions-label {
      width: 106px;
      text-align: right;
    }
    .descriptions-content {
      min-width: 130px;
      word-break: break-all;
    }
  }
}
</style>
