<template>
  <div class="containerBox">
    <el-tooltip effect="dark" content="产品" placement="bottom">
      <div class="chatfont chat-chanpin chatSend" :class="{ active: show }" @click.stop="handleOpen"></div>
    </el-tooltip>
    <el-collapse-transition>
      <div class="chatSend-box" v-show="show">
        <div class="chatSend-border">
          <div class="chatSend-search">
            <el-input class="chatSend-search-input" v-model="queryParams.keyword" placeholder="请输入产品名称" style="width: 100%" @keyup.enter.native="handleSearch"></el-input>
            <el-select ref="select" class="chatSend-search-select" size="mini" v-model="typeActive" placeholder="请选择" @change="handleChange" @click.native.stop="onSelectClick">
              <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </div>
          <el-table ref="table" v-loading="loading" :data="list" stripe class="custom-table custom-table-cell3" max-height="340px" row-key="id" @selection-change="handleSelectionChange" v-el-table-infinite-scroll="handleMore">
            <el-table-column :reserve-selection="true" align="center" type="selection" width="55"></el-table-column>
            <el-table-column align="center" label="产品名称" show-overflow-tooltip>
              <template slot-scope="{ row }">
                <span class="table-link" @click="handleView(row)">
                  <template v-if="typeActive === 'need'">
                    <span v-if="row.source === 'common'">(公域)</span>
                    <span style="color: #fe7f22" v-else>(私域)</span>
                  </template>
                  {{ row.product_name || row.productName }}
                </span>
              </template>
            </el-table-column>
            <el-table-column align="center" label="规格" show-overflow-tooltip>
              <template slot-scope="{ row }">{{ row.hasOwnProperty('product') ? row.product.specs : row.specs }}</template>
            </el-table-column>
            <el-table-column align="center" label="型号" show-overflow-tooltip>
              <template slot-scope="{ row }">{{ row.hasOwnProperty('product') ? row.product.model : row.model }}</template>
            </el-table-column>
            <el-table-column align="center" label="重量">
              <template slot-scope="{ row }">{{ row.hasOwnProperty('product') ? row.product.weight : row.weight }}</template>
            </el-table-column>
            <el-table-column align="center" width="105px">
              <template slot-scope="{ row }">
                <el-button type="primary" class="chatSend-button" @click="handleSend(row)">发送该产品</el-button>
              </template>
            </el-table-column>
          </el-table>
          <!-- 批量发送 -->
          <div class="collectAll" v-if="!!checkList.length">
            <div class="collectAll-box">
              <div class="collectAll-title">已选择 {{ checkList.length }} 项</div>
              <el-tooltip content="批量发送" placement="top" effect="dark">
                <div class="collectAll-btn">
                  <span @click="handleSendAll">批量发送</span>
                </div>
              </el-tooltip>
              <el-tooltip content="退出" placement="top" effect="dark">
                <div class="collectAll-close" @click="handleClose">
                  <i class="el-icon-close"></i>
                </div>
              </el-tooltip>
            </div>
          </div>
        </div>
      </div>
    </el-collapse-transition>
    <!-- 产品详情 -->
    <product-dialog ref="productInfo" append-body></product-dialog>
  </div>
</template>
<script>
import { purchasingGroupList } from '@/api/purchase'
import { listProduct } from '@/api/system/product'
import { listPrivateduct } from '@/api/system/privateduct'
import ProductDialog from '@/views/public/product/dialog'

export default {
  components: { ProductDialog },
  data() {
    return {
      typeActive: 'need',
      typeOptions: [
        { label: '采购产品', value: 'need' },
        { label: '公域产品', value: 'public' },
        { label: '私域产品', value: 'private' }
      ],
      isClickedOutside: false,
      show: false,
      loading: true,
      list: [],
      total: 0,
      isBottom: false,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        keyword: ''
      },
      checkList: []
    }
  },
  mounted() {
    document.addEventListener('click', this.handleClickOutside, true)
  },
  beforeDestroy() {
    document.removeEventListener('click', this.handleClickOutside, true)
  },
  methods: {
    // 打开产品列表
    handleOpen() {
      this.list = []
      this.total = 0
      this.queryParams.pageNum = 1
      this.queryParams.keyword = ''
      this.handleClose()
      this.getList()
    },
    // 切换tab
    handleChange(item) {
      this.typeActive = item
      this.queryParams.keyword = ''
      this.queryParams.pageNum = 1
      this.list = []
      this.total = 0
      this.handleClose()
      this.getList()
    },
    getList() {
      const active = this.typeActive
      switch (active) {
        case 'need':
          this.getNeedList()
          break
        case 'public':
          this.getPublicList()
          break
        case 'private':
          this.getPrivateList()
          break
      }
    },
    // 查询采购产品
    getNeedList() {
      this.loading = true
      purchasingGroupList(this.queryParams).then(res => {
        const { code, msg, rows, total } = res
        if (code === 200) {
          this.loading = false
          rows.map(item => (item.id = item.product_id))
          this.list = this.list.concat(rows)
          this.total = total
          this.isBottom = this.queryParams.pageNum * this.queryParams.pageSize >= total
          if (!this.show) this.show = true
        } else this.$modal.msgError(msg)
      })
    },
    // 查询公域产品
    getPublicList() {
      this.loading = true
      const query = { ...this.queryParams, productName: this.queryParams.keyword }
      listProduct(query).then(res => {
        const { code, msg, rows, total } = res
        if (code === 200) {
          this.loading = false
          this.list = this.list.concat(rows)
          this.total = total
          this.isBottom = this.queryParams.pageNum * this.queryParams.pageSize >= total
          if (!this.show) this.show = true
        } else this.$modal.msgError(msg)
      })
    },
    // 查询私域产品
    getPrivateList() {
      this.loading = true
      const query = { ...this.queryParams, productName: this.queryParams.keyword }
      listPrivateduct(query).then(res => {
        const { code, msg, rows, total } = res
        if (code === 200) {
          this.loading = false
          this.list = this.list.concat(rows)
          this.total = total
          this.isBottom = this.queryParams.pageNum * this.queryParams.pageSize >= total
          if (!this.show) this.show = true
        } else this.$modal.msgError(msg)
      })
    },
    // 滚动加载
    handleMore() {
      if (!this.isBottom) {
        this.queryParams.pageNum++
        this.getList()
      }
    },
    // 搜索产品
    handleSearch() {
      this.list = []
      this.total = 0
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 多选
    handleSelectionChange(selection) {
      this.checkList = selection
    },
    // 批量发送
    handleSendAll() {
      this.show = false
      const list = this.checkList
      if (list.length === 1) {
        this.handleSend(list[0])
        return
      }
      let data = []
      if (this.typeActive === 'need') {
        list.map(item => {
          data.push(item.product)
        })
      } else data = list
      this.$emit('handleSend', JSON.stringify({ doType: 'sendProducts', info: data, showMore: false }))
    },
    // 关闭多选
    handleClose() {
      this.checkList = []
      this.$refs.table.clearSelection()
    },
    // 点击
    handleSend(item) {
      this.show = false
      const product = this.typeActive === 'need' ? item.product : item
      this.$emit('handleSend', JSON.stringify({ doType: 'sendProduct', ...product }))
    },
    onSelectClick() {
      this.isClickedOutside = false
    },
    // 点击外部关闭
    handleClickOutside(event) {
      if (this.isClickedOutside && !this.$el.contains(event.target) && !this.$refs.select.$el.contains(event.target)) {
        this.show = false
      }
      this.isClickedOutside = true
    },
    // 查看产品详情
    handleView(item, type = 1) {
      this.isClickedOutside = false
      let product
      if (!!type) product = this.typeActive === 'need' ? item.product : item
      else product = item
      this.$refs.productInfo.handleView(product)
    }
  }
}
</script>

<style scoped lang="scss">
@import '~@/assets/chat/iconfont.css';
@import '~@/assets/styles/custom-new.scss';
.containerBox {
  position: relative;
}
.chatSend {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  font-size: 18px;
  cursor: pointer;
  color: #9fa3b2;
  transition: all 0.3s;
  &:hover,
  &.active {
    color: $blue;
  }
}
.chatSend-box {
  background-color: #ffffff;
  position: absolute;
  bottom: 30px;
  left: -165px;
  z-index: 99999;
  box-shadow: 0 1px 15px 0 rgba(0, 0, 0, 0.25);
  border-radius: 15px;
}
.chatSend-border {
  position: relative;
  width: 600px;
  border: 1px solid $blue;
  border-radius: 15px;
  overflow: hidden;
}
.chatSend-search {
  padding: 10px 15px;
  position: relative;
  &-input {
    ::v-deep {
      .el-input__inner {
        padding-right: 125px;
      }
    }
  }
  &-select {
    position: absolute;
    top: 16px;
    right: 16px;
    width: 110px;
    ::v-deep {
      .el-input__inner {
        border-radius: 0;
        border-width: 0 0 0 1px;
        border-color: #dcdfe6 !important;
      }
    }
  }
}
.chatSend-button {
  padding: 0 10px;
  height: 30px;
  border-radius: 15px;
  background-color: transparent;
  border-color: $blue;
  color: $blue;
  font-size: 12px;
  &:hover {
    background-color: $blue;
    color: $white;
  }
}
::v-deep {
  .collectAll {
    position: absolute;
  }
  .el-table {
    .el-table__cell {
      padding: 0;
    }
    .el-table__header-wrapper {
      col[name='gutter'] {
        width: 5px !important;
      }
    }
    .el-table__body-wrapper {
      .el-table__body {
        width: 100% !important;
      }
      &::-webkit-scrollbar {
        width: 5px;
      }
      &::-webkit-scrollbar-track {
        background-color: #f1f1f1;
        opacity: 0.15;
      }
      &::-webkit-scrollbar-thumb {
        background-color: #cccccc;
        border-radius: 5px;
      }
      &::-webkit-scrollbar-thumb:hover {
        background: #555555;
      }
    }
  }
}
</style>
