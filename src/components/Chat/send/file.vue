<template>
  <div class="containerBox">
    <el-upload ref="chatFile" :action="uploadUrl" accept="image/*" :headers="headers" :limit="1" :on-success="handleSend" :show-file-list="false">
      <el-tooltip effect="dark" content="图片">
        <i class="chatfont chat-tupian chatSend" />
      </el-tooltip>
    </el-upload>
    <el-image-viewer :zIndex="99999" v-show="showViewer" :on-close="closeViewer" :url-list="srcList"></el-image-viewer>
  </div>
</template>

<script>
import { getToken } from '@/utils/auth'
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'

export default {
  components: { ElImageViewer },
  data() {
    return {
      uploadUrl: process.env.VUE_APP_BASE_API + '/common/jim/upload/file', // 上传地址
      headers: {
        Authorization: 'Bearer ' + getToken()
      },
      showViewer: false,
      srcList: []
    }
  },
  mounted() {
    this.$nextTick(() => {
      let wrapper = document.getElementsByClassName('el-image-viewer__actions__inner')
      let downImg = document.createElement('i')
      downImg.setAttribute('class', 'el-icon-download')
      wrapper[0].appendChild(downImg)
      if (wrapper.length > 0) {
        this.wrapperElem = wrapper[0]
        this.cusClickHandler()
      }
    })
  },
  methods: {
    handleSend(response) {
      if (response.code === 200) {
        const imgUrl = response.data.thumbOss || response.data.oss || ''
        if (imgUrl) this.$emit('handleSend', JSON.stringify({ doType: 'image', info: imgUrl }))
        this.$refs.chatFile.clearFiles()
      }
    },
    handleView(data = '') {
      this.srcList = [data]
      this.showViewer = true
    },
    cusClickHandler() {
      this.wrapperElem.addEventListener('click', this.hideCusBtn)
    },
    hideCusBtn(e) {
      let className = e.target.className
      if (className === 'el-icon-download') {
        let imgUrl = document.getElementsByClassName('el-image-viewer__canvas')[0].children[0].src
        this.handleDownload(imgUrl, String(new Date().getTime()))
      }
    },
    handleDownload(src, fileName) {
      const image = new Image()
      image.setAttribute('crossOrigin', 'anonymous')
      image.onload = function () {
        const canvas = document.createElement('canvas')
        canvas.width = image.width
        canvas.height = image.height
        const context = canvas.getContext('2d')
        context?.drawImage(image, 0, 0, image.width, image.height)
        const url = canvas.toDataURL('image/png')
        let a = document.createElement('a')
        let event = new MouseEvent('click')
        a.download = fileName ?? String(new Date().getTime())
        a.href = url
        a.dispatchEvent(event)
      }
      image.src = src
    },
    closeViewer() {
      this.showViewer = false
    }
  }
}
</script>
<style scoped lang="scss">
@import '~@/assets/chat/iconfont.css';
.containerBox {
  position: relative;
}
.chatSend {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  font-size: 20px;
  cursor: pointer;
  color: #9fa3b2;
  transition: all 0.3s;
  &:hover,
  &.active {
    color: #2e73f3;
  }
}
</style>
