<template>
  <div class="containerBox">
    <el-tooltip effect="dark" content="报价" placement="bottom">
      <div class="chatfont chat-baojia chatSend" :class="{ active: show }" @click.stop="handleOpen"></div>
    </el-tooltip>
    <el-collapse-transition>
      <div class="chatSend-box" v-show="show">
        <div class="chatSend-border">
          <div class="chatSend-search">
            <el-input v-model="queryParams.name" placeholder="请输入报价名称" style="width: 100%" @keyup.enter.native="handleSearch"></el-input>
          </div>
          <el-table v-loading="loading" :data="list" stripe class="custom-table custom-table-cell3" :height="maxHeight" v-el-table-infinite-scroll="handleMore">
            <el-table-column align="center" label="序号" type="index"></el-table-column>
            <el-table-column align="center" label="报价编号" show-overflow-tooltip>
              <template slot-scope="{ row }">
                <i class="quick-serial" v-if="row.quoteType === 'quickly'"></i>
                <span>{{ row.serial }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="name" label="报价名称" show-overflow-tooltip></el-table-column>
            <el-table-column align="center" prop="totalPrice" label="未税总价" show-overflow-tooltip>
              <template slot-scope="{ row }">
                <b style="font-size: 14px; color: #ec2454" v-if="row.method !== 'ton'">{{ '¥' + row.totalPrice + '元' + (row.hasOwnProperty('product') && row.product.unit ? '/' + row.product.unit : '') }}</b>
                <b style="font-size: 14px; color: #ec2454" v-if="row.method === 'ton'">{{ '¥' + row.tonPrice + '元/吨' }}</b>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="name" label="含税总价" show-overflow-tooltip>
              <template slot-scope="{ row }">
                <b style="font-size: 14px; color: #ec2454" v-if="row.method !== 'ton'">{{ row.taxPrice ? '¥' + row.taxPrice + '元' + (row.hasOwnProperty('product') && row.product.unit ? '/' + row.product.unit : '') : '' }}</b>
                <b style="font-size: 14px; color: #ec2454" v-if="row.method === 'ton'">{{ row.tonTaxPrice ? '¥' + row.tonTaxPrice + '元/吨' : '' }}</b>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="grossWeight" label="毛总重" show-overflow-tooltip>
              <template slot-scope="{ row }">{{ row.grossWeight ? row.grossWeight + 'kg' : '' }}</template>
            </el-table-column>
            <el-table-column align="center" prop="netWeight" label="毛总重" show-overflow-tooltip>
              <template slot-scope="{ row }">{{ row.grossWeight ? row.grossWeight + 'kg' : '' }}</template>
            </el-table-column>
            <el-table-column align="center" prop="productName" label="报价产品" show-overflow-tooltip>
              <template slot-scope="{ row }">
                <span class="table-link" @click="handleViewProduct(row)" v-if="row.productId">{{ row.hasOwnProperty('product') ? (row.product.hasOwnProperty('productName') ? row.product.productName : '') : '' }}</span>
                <span v-else></span>
              </template>
            </el-table-column>
            <el-table-column align="center" label="报价产品" show-overflow-tooltip>
              <template slot-scope="{ row }">
                {{ row.hasOwnProperty('product') ? (row.product.hasOwnProperty('model') ? row.product.model : '') : '' }}
              </template>
            </el-table-column>
            <el-table-column align="center" width="105px">
              <template slot-scope="{ row }">
                <el-button type="primary" class="chatSend-button" @click="handleSend(row)">发送该报价</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-collapse-transition>
    <!-- 产品详情 -->
    <product-dialog ref="productInfo" append-body></product-dialog>
    <!--报价详情-->
    <el-dialog v-dialogDragBox title="报价详情" :visible.sync="open" width="920px" class="custom-dialog" append-to-body>
      <div style="padding: 0 20px">
        <div class="quoteTitle">
          <span>报价名称：</span>
          <i v-if="info.quoteType === 'quickly'">快</i>
          <b>{{ info.name }}</b>
        </div>
        <el-descriptions :column="3" border labelClassName="descriptions-label">
          <el-descriptions-item contentClassName="descriptions-content">
            <template slot="label">报价编号</template>
            {{ info.serial }}
          </el-descriptions-item>
          <el-descriptions-item contentClassName="descriptions-content">
            <template slot="label">未税总价</template>
            <span class="cf43f3f" v-if="info.method !== 'ton'">
              {{ '¥' + info.totalPrice + '元' + (hasKey(info, 'product') && hasKey(info.product, 'unit') && info.product.unit ? '/' + info.product.unit : '') }}
            </span>
            <span class="cf43f3f" v-if="info.method === 'ton'">
              {{ '¥' + info.tonPrice + '元/吨' }}
            </span>
          </el-descriptions-item>
          <el-descriptions-item contentClassName="descriptions-content">
            <template slot="label">含税总价</template>
            <span class="cf43f3f" v-if="info.method !== 'ton'">
              {{ info.taxPrice ? '¥' + info.taxPrice + '元' + (hasKey(info, 'product') && hasKey(info.product, 'unit') && info.product.unit ? '/' + info.product.unit : '') : '-' }}
            </span>
            <span class="cf43f3f" v-if="info.method === 'ton'">{{ info.tonTaxPrice ? '¥' + info.tonTaxPrice + '元/吨' : '-' }}</span>
          </el-descriptions-item>
          <el-descriptions-item contentClassName="descriptions-content">
            <template slot="label">毛总重</template>
            {{ info.grossWeight ? info.grossWeight + 'kg' : '-' }}
          </el-descriptions-item>
          <el-descriptions-item contentClassName="descriptions-content">
            <template slot="label">净总重</template>
            {{ info.netWeight ? info.netWeight + 'kg' : '-' }}
          </el-descriptions-item>
          <el-descriptions-item contentClassName="descriptions-content">
            <template slot="label">报价产品</template>
            <span class="c2e73f3" @click="handleViewProduct(info)">{{ info.hasOwnProperty('product') ? (info.product.hasOwnProperty('productName') ? info.product.productName + '>' : '') : '' }}</span>
          </el-descriptions-item>
          <el-descriptions-item contentClassName="descriptions-content">
            <template slot="label">创建时间</template>
            {{ parseTime(info.createTime, '{y}-{m}-{d}') }}
          </el-descriptions-item>
          <el-descriptions-item contentClassName="descriptions-content">
            <template slot="label">修改时间</template>
            {{ parseTime(info.updateTime, '{y}-{m}-{d}') }}
          </el-descriptions-item>
          <el-descriptions-item contentClassName="descriptions-content">
            <template slot="label">报单人</template>
            {{ info.createBy }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { list } from '@/api/houtai/formula'
import ProductDialog from '@/views/public/product/dialog'
import { parseTime } from '@/utils/ruoyi'

export default {
  components: { ProductDialog },
  data() {
    return {
      show: false,
      keyword: '',
      maxHeight: 100,
      isClickedOutside: false,
      loading: true,
      list: [],
      total: 0,
      isBottom: false,
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        isEditing: false,
        name: ''
      },
      open: false,
      info: {}
    }
  },
  mounted() {
    document.addEventListener('click', this.handleClickOutside, true)
    window.onresize = () => {
      return (() => {
        this.show = false
      })()
    }
  },
  beforeDestroy() {
    document.removeEventListener('click', this.handleClickOutside, true)
    window.onresize = null
  },
  methods: {
    parseTime,
    hasKey(obj, key) {
      return Object.prototype.hasOwnProperty.call(obj, key)
    },
    handleOpen() {
      this.maxHeight = window.innerHeight * 0.88 > 600 ? window.innerHeight * 0.88 - 150 : 450
      this.show = true
      this.radio = {}
      this.list = []
      this.total = 0
      this.queryParams.pageNum = 1
      this.queryParams.name = ''
      this.getList()
    },
    getList() {
      this.loading = true
      list(this.queryParams).then(res => {
        const { code, msg, rows, total } = res
        if (code === 200) {
          rows.forEach(item => (item.show = false))
          this.loading = false
          this.list = this.list.concat(rows)
          this.total = total
          this.isBottom = this.queryParams.pageNum * this.queryParams.pageSize >= total
          if (!this.show) this.show = true
        } else this.$modal.msgError(msg)
      })
    },
    // 滑动查看更多
    handleMore() {
      if (!this.isBottom) {
        this.queryParams.pageNum++
        this.getList()
      }
    },
    // 搜索
    handleSearch() {
      this.list = []
      this.total = 0
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 点击
    handleSend(item) {
      this.show = false
      this.$emit('handleSend', JSON.stringify({ doType: 'sendQuote', ...item }))
    },
    // 点击外部关闭
    handleClickOutside(event) {
      if (this.isClickedOutside && !this.$el.contains(event.target)) {
        this.show = false
      }
      this.isClickedOutside = true
    },
    // 查看产品详情
    handleViewProduct(item) {
      this.$refs.productInfo.handleView(item.product)
      this.isClickedOutside = false
    },
    // 查看报价详情
    handleView(data = {}) {
      this.open = true
      this.info = data
    }
  }
}
</script>

<style scoped lang="scss">
@import '~@/assets/chat/iconfont.css';
@import '~@/assets/styles/custom-new.scss';
.containerBox {
  position: relative;
}
.chatSend {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  font-size: 18px;
  cursor: pointer;
  color: #9fa3b2;
  transition: all 0.3s;
  &:hover,
  &.active {
    color: $blue;
  }
}
.chatSend-box {
  background-color: #ffffff;
  position: absolute;
  bottom: 30px;
  left: -396px;
  z-index: 99999;
  box-shadow: 0 1px 15px 0 rgba(0, 0, 0, 0.25);
  border-radius: 15px;
}
.chatSend-border {
  position: relative;
  width: 910px;
  border: 1px solid $blue;
  border-radius: 15px;
  overflow: hidden;
}
.chatSend-search {
  padding: 10px 15px;
}
.chatSend-button {
  padding: 0 10px;
  height: 30px;
  border-radius: 15px;
  background-color: transparent;
  border-color: $blue;
  color: $blue;
  font-size: 12px;
  &:hover {
    background-color: $blue;
    color: $white;
  }
}
::v-deep {
  .el-table {
    .el-table__cell {
      padding: 0;
    }
    .el-table__header-wrapper {
      col[name='gutter'] {
        width: 5px !important;
      }
    }
    .el-table__body-wrapper {
      .el-table__body {
        width: 100% !important;
      }
      &::-webkit-scrollbar {
        width: 5px;
      }
      &::-webkit-scrollbar-track {
        background-color: #f1f1f1;
        opacity: 0.15;
      }
      &::-webkit-scrollbar-thumb {
        background-color: #cccccc;
        border-radius: 5px;
      }
      &::-webkit-scrollbar-thumb:hover {
        background: #555555;
      }
    }
  }
  .quoteTitle {
    display: flex;
    align-items: center;
    font-size: 14px;
    line-height: 20px;
    color: $info;
    padding-bottom: 10px;
    span {
      color: $disabled;
    }
    b {
      font-weight: 500;
    }
    i {
      width: 20px;
      height: 20px;
      text-align: center;
      border-radius: 50px;
      background-color: $red;
      color: $white;
      margin-right: 5px;
      font-size: 12px;
      font-style: normal;
    }
  }
  .el-descriptions {
    margin-bottom: 20px;
    .descriptions-label {
      width: 106px;
      text-align: right;
    }
    .descriptions-content {
      min-width: 130px;
      word-break: break-all;
    }
  }
  .cf43f3f {
    color: $red;
  }
  .c2e73f3 {
    color: $blue;
    cursor: pointer;
  }
}
</style>
