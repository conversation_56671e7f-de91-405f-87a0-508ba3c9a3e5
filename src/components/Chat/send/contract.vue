<template>
  <div class="containerBox">
    <el-tooltip effect="dark" content="合同" placement="bottom">
      <div class="chatfont chat-hetong chatSend" :class="{ active: show }" @click.stop="show = !show"></div>
    </el-tooltip>
    <el-collapse-transition>
      <div class="chatSend-box" v-show="show">合同</div>
    </el-collapse-transition>
  </div>
</template>
<script>
export default {
  data() {
    return {
      show: false,
      list: []
    }
  },
  mounted() {
    document.addEventListener('click', this.handleClickOutside, true)
  },
  beforeDestroy() {
    document.removeEventListener('click', this.handleClickOutside, true)
  },
  methods: {
    // 点击
    handleSend(item) {
      this.$emit('handleSend', item)
    },
    // 点击外部关闭
    handleClickOutside(event) {
      if (!this.$el.contains(event.target)) {
        this.show = false
      }
    }
  }
}
</script>

<style scoped lang="scss">
@import '~@/assets/chat/iconfont.css';
.containerBox {
  position: relative;
}
.chatSend {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  font-size: 18px;
  cursor: pointer;
  color: #9fa3b2;
  transition: all 0.3s;
  &:hover,
  &.active {
    color: #2e73f3;
  }
}
.chatSend-box {
  position: absolute;
  bottom: 30px;
  background-color: #f8f8f8;
  box-shadow: 0 1px 18px 0 rgba(0, 0, 0, 0.2);
  border-radius: 15px;
  padding: 15px 5px 15px;
  z-index: 99999;
}
.chatSend-list {
  display: flex;
  flex-wrap: wrap;
  width: 410px;
  max-height: 280px;
  overflow-y: auto;
  &::-webkit-scrollbar {
    width: 5px;
  }
  &::-webkit-scrollbar-track {
    background-color: #f1f1f1;
    opacity: 0.15;
  }
  &::-webkit-scrollbar-thumb {
    background-color: #888888;
    border-radius: 5px;
  }
  &::-webkit-scrollbar-thumb:hover {
    background: #555555;
  }
}
</style>
