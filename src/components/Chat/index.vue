<template>
  <div>
    <div class="chatIcon" v-if="showBadge">
      <el-badge :value="chatNum + service_chatNum + applyFriends.length" :hidden="!chatNum && !service_chatNum">
        <i class="el-icon-bell pointer" @click="handleOpen" v-if="isconnect"></i>
      </el-badge>
    </div>
    <el-dialog v-dialogDragBox title="" :visible.sync="socketOpen" width="920px" append-to-body :before-close="handleHide">
      <div class="chatDialog">
        <div class="chatDialogLeft">
          <div class="leftTabs">
            <el-badge :value="service_chatNum" :hidden="!service_chatNum" v-if="!!service">
              <button class="leftTabsItem" :class="{ active: chatType === 'service' }" @click="handleTypeChange('service')" @dblclick="handleDoubleClick">客服消息</button>
            </el-badge>
            <el-badge :value="chatNum + applyFriends.length" :hidden="!chatNum && !applyFriends.length">
              <button class="leftTabsItem" :class="{ active: chatType === 'system' }" @click="handleTypeChange('system')" @dblclick="handleDoubleClick">我的消息</button>
            </el-badge>
          </div>
          <div class="leftSearch" :class="{ mySearch: chatType === 'system' }">
            <div class="mySearchTabs" v-if="chatType === 'system'">
              <div class="chatfont chat-liaotian mySearchTabsItem" :class="{ active: myType === 'chat' }" @click="handleMyTypeChange('chat')"></div>
              <div class="mySearchTabsItem" :class="{ active: myType === 'book' }" @click="handleMyTypeChange('book')">
                <el-badge is-dot :hidden="!applyFriends.length"><i class="chatfont chat-tongxunlu"></i></el-badge>
              </div>
            </div>
            <el-input v-model="keyword" size="mini" placeholder="搜索" clearable @change="handleSearch" @clear="handleSearchClear" v-if="myType === 'book'">
              <span class="el-icon-search input-suffix" slot="prefix"></span>
            </el-input>
            <el-input v-model="keyword" size="mini" placeholder="搜索" clearable @input="handleSearch" v-else>
              <span class="el-icon-search input-suffix" slot="prefix"></span>
            </el-input>
          </div>
          <el-scrollbar class="leftList">
            <template v-if="myType === 'book' ? isSearch : !!keyword">
              <template v-if="!!searchList.length">
                <div class="leftListItem" :class="{ active: activeId === item.userId }" v-for="item in searchList" :key="item.userId" @click="handleChat(item)">
                  <div class="itemAvatar">
                    <img :src="item.avatar" :alt="item.nick" />
                  </div>
                  <div class="itemContent">
                    <div class="itemContentTop">
                      <div class="itemContentTopName">
                        <span>{{ item.nick }}</span>
                        <em v-if="hasOwnProperty(item, 'ext') && item.ext.friendType == 'user' && item.ext.companyId != -1">{{ item.ext.companyName ? `(${item.ext.companyName})` : '' }}</em>
                      </div>
                      <div class="itemContentTopTime">{{ formatChatTime(item.createTime) }}</div>
                    </div>
                    <div class="itemContentBottom">
                      <span class="itemContentBottomText" v-html="formatContentImg(formatContent(removeHtmlTag(decrypt(item.msg))))"></span>
                    </div>
                  </div>
                </div>
              </template>
              <div style="padding: 0 10px" v-if="!searchList.length && keyword">
                <el-empty description="未找到相关用户，更换关键词重新尝试" />
              </div>
            </template>
            <template v-if="chatType === 'system'">
              <!-- 群组列表 -->
              <template v-if="!keyword && myType === 'chat'">
                <div class="leftListItem" :class="{ active: activeId === item.groupId }" v-for="item in groups" :key="item.groupId" @click="handleGroupChat(item.groupId)">
                  <div class="itemAvatar">
                    <img :src="item.avatar" :alt="item.name" />
                  </div>
                  <div class="itemContent">
                    <div class="itemContentTop">
                      <div class="itemContentTopName">
                        <span>{{ item.name }}</span>
                      </div>
                      <div class="itemContentTopTime">{{ formatChatTime(item.createTime) }}</div>
                    </div>
                    <div class="itemContentBottom">
                      <div class="itemContentBottomText json" v-html="listHtmlFormat(decrypt(item.msg))" v-if="isJSON(decrypt(item.msg))"></div>
                      <div class="itemContentBottomText string" v-html="formatContentImg(formatContent(removeHtmlTag(decrypt(item.msg))))" v-if="!isJSON(decrypt(item.msg))"></div>
                      <el-badge :value="item.num" :max="99" :hidden="!item.num" class="itemContentBottomNum"></el-badge>
                    </div>
                  </div>
                </div>
              </template>
              <template v-if="!keyword && myType === 'chat'">
                <div class="leftListItem" :class="{ active: activeId === item.userId }" v-for="item in mergedFriends" :key="item.userId" @click="handleChat(item)">
                  <div class="itemAvatar">
                    <img :src="item.avatar" :alt="item.nick" />
                  </div>
                  <div class="itemContent">
                    <div class="itemContentTop">
                      <div class="itemContentTopName">
                        <span>{{ item.nick }}</span>
                        <em v-if="hasOwnProperty(item, 'ext') && item.ext.friendType == 'user' && item.ext.companyId != -1">{{ item.ext.companyName ? `(${item.ext.companyName})` : '' }}</em>
                      </div>
                      <div class="itemContentTopTime">{{ formatChatTime(item.createTime) }}</div>
                    </div>
                    <div class="itemContentBottom">
                      <div class="itemContentBottomText json" v-html="listHtmlFormat(decrypt(item.msg))" v-if="isJSON(decrypt(item.msg))"></div>
                      <div class="itemContentBottomText string" v-html="formatContentImg(formatContent(removeHtmlTag(decrypt(item.msg))))" v-if="!isJSON(decrypt(item.msg))"></div>
                      <el-badge :value="item.num" :max="99" :hidden="!item.num" class="itemContentBottomNum"></el-badge>
                    </div>
                  </div>
                </div>
              </template>
              <template v-if="!isSearch && myType === 'book'">
                <div class="leftListItem" :class="{ active: hasAdd }" @click="handleAdd" v-if="!!applyFriends.length">
                  <div class="itemAvatar">
                    <img src="~@/assets/images/chatAdd.png" alt="好友申请" />
                  </div>
                  <div class="itemContent">
                    <div class="itemContentTop">
                      <div class="itemContentTopName">
                        <span>新的朋友</span>
                      </div>
                      <div class="itemContentTopTime">共 {{ applyFriends.length }} 条</div>
                    </div>
                    <div class="itemContentBottom">
                      <div class="itemContentBottomText">您有新的好友申请</div>
                    </div>
                  </div>
                </div>
                <template v-if="!!friends.length">
                  <div style="width: 100%" v-for="ite in formattedBookFriends" :key="ite.letter">
                    <div class="leftListLetter">{{ ite.letter }}</div>
                    <div class="leftListItem" :class="{ active: activeId === item.userId }" v-for="item in ite.data" :key="item.userId" @click="handleChat(item)">
                      <div class="itemAvatar">
                        <img :src="item.avatar" :alt="item.nick" />
                      </div>
                      <div class="itemContent">
                        <div class="itemContentTop">
                          <div class="itemContentTopName">
                            <span>{{ item.nick }}</span>
                            <em v-if="hasOwnProperty(item, 'ext') && item.ext.friendType == 'user' && item.ext.companyId != -1">{{ item.ext.companyName ? `(${item.ext.companyName})` : '' }}</em>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </template>
                <div style="padding: 0 10px" v-else>
                  <el-empty description="暂无好友，去搜索添加一些好友" />
                </div>
              </template>
            </template>
            <template v-if="!keyword && chatType === 'service'">
              <div class="leftListItem" :class="{ active: service_activeId === item.userId }" v-for="item in mergedServiceFriends" :key="item.userId" @click="handleChat(item)">
                <div class="itemAvatar">
                  <img :src="item.avatar" :alt="item.nick" />
                </div>
                <div class="itemContent">
                  <div class="itemContentTop">
                    <div class="itemContentTopName">
                      <span>{{ item.nick }}</span>
                      <em v-if="hasOwnProperty(item, 'ext') && item.ext.friendType == 'user' && item.ext.companyId != -1">{{ item.ext.companyName ? `(${item.ext.companyName})` : '' }}</em>
                    </div>
                    <div class="itemContentTopTime">{{ formatChatTime(item.createTime) }}</div>
                  </div>
                  <div class="itemContentBottom">
                    <div class="itemContentBottomText" v-html="listHtmlFormat(decrypt(item.msg))" v-if="isJSON(decrypt(item.msg))"></div>
                    <div class="itemContentBottomText" v-html="formatContentImg(formatContent(removeHtmlTag(decrypt(item.msg))))" v-if="!isJSON(decrypt(item.msg))"></div>
                    <el-badge :value="item.num" :max="99" :hidden="!item.num" class="itemContentBottomNum"></el-badge>
                  </div>
                </div>
              </div>
            </template>
          </el-scrollbar>
        </div>
        <div class="chatDialogRight" v-if="chatType === 'service' && service_activeId">
          <div class="rightTitle">
            <div class="rightTitleFlex">
              <span>{{ service_activeInfo.nick }}</span>
              <em v-if="hasOwnProperty(service_activeInfo, 'ext') && service_activeInfo.ext.friendType == 'user' && service_activeInfo.ext.companyId != -1">{{ service_activeInfo.ext.companyName ? `(${service_activeInfo.ext.companyName})` : '' }}</em>
            </div>
            <i class="el-icon-close" @click="handleHide"></i>
          </div>
          <div class="rightInfo right0">
            <div class="infoLeft">
              <div class="infoLeftList" :class="{ system: isInUserId(service_activeInfo) }">
                <el-scrollbar ref="msgList" style="width: 100%; height: 100%">
                  <!--系统消息-->
                  <template v-if="isInUserId(service_activeInfo)">
                    <div class="systemItem" v-for="(item, index) in service_msgList" :key="index">
                      <div class="systemItemTop">
                        <div class="systemItemFlex">
                          <div class="systemItemTopTitle">{{ backTitle(item) }}</div>
                        </div>
                        <div class="systemItemTopTime">{{ formatChatListTime(item.createTime) }}</div>
                      </div>
                      <div class="systemItemFlex start" v-html="htmlFormat(item, 'sys')"></div>
                    </div>
                  </template>
                  <!--会话-->
                  <template v-if="!isInUserId(service_activeInfo)">
                    <div class="infoLeftItem" :class="{ self: service === item.userId }" v-for="(item, index) in service_msgList" :key="index">
                      <div class="itemTime" v-if="isShowChatTime(service_msgList, index)">{{ formatChatListTime(item.createTime) }}</div>
                      <div class="itemFlex">
                        <div class="itemFlexImg" v-if="service !== item.userId">
                          <img :src="item.avatar" :alt="item.nick" />
                        </div>
                        <template v-if="isVoiceFormat(item)">
                          <voice-player :duration="sendContent(item, 'duration')" :audio-url="sendContent(item, 'url')" :is-self="isSelf(item)" />
                        </template>
                        <template v-else>
                          <div class="itemFlexInfo" v-if="!isMultiple(item)">
                            <div style="max-width: 100%" v-html="htmlFormat(item)" @click="handleDetail(item)"></div>
                          </div>
                          <div class="itemFlexInfo sendProducts" v-if="isMultiple(item)">
                            <div class="sendProductsItemBox">
                              <div class="sendProductsItem" v-for="ite in productsFormat(item)" :key="ite.id" @click="handleDetailProduct(ite)">
                                <el-image :src="formatProductImg(ite)" :alt="ite.productName" class="sendProductsImg" />
                                <div class="sendProductsInfo">
                                  <div class="sendProductsTitle">{{ ite.productName }}</div>
                                  <div class="sendProductsDesc">规格：{{ ite.specs }}</div>
                                  <div class="sendProductsDesc">型号：{{ ite.model }}</div>
                                </div>
                              </div>
                            </div>
                            <div class="sendProductsMore" :class="itemShowMore(item).showMore ? 'up' : 'down'" v-if="multipleInfoNum(item) > 3" @click="handleShowMore(item, index, 'service')">{{ itemShowMore(item).showMore ? '收起' : '展开查看' }}</div>
                          </div>
                        </template>
                        <div class="itemFlexImg" v-if="service === item.userId">
                          <img :src="item.avatar" :alt="item.nick" />
                        </div>
                      </div>
                    </div>
                  </template>
                </el-scrollbar>
              </div>
              <!--输入框-->
              <div class="infoLeftInput" v-if="!isInUserId(service_activeInfo)">
                <el-input v-model="msg" @blur="changeCaretPosition" type="textarea" maxlength="300" resize="none" placeholder="请输入内容" clearable class="input-textarea" spellcheck="false" @keyup.enter.native="handleSend()"></el-input>
                <div class="inputButton" style="padding-right: 10px">
                  <div class="inputButtonFlex">
                    <emoji location-top class="inputButtonIcon" @handleClickEmoji="handleClickEmoji" />
                    <send-file class="inputButtonIcon" ref="sendFile" @handleSend="handleSend" />
                    <send-product class="inputButtonIcon" ref="sendProduct" @handleSend="handleSend" style="display: none" />
                    <send-demand class="inputButtonIcon" ref="sendDemand" @handleSend="handleSend" style="display: none" />
                    <send-quote class="inputButtonIcon" ref="sendQuote" @handleSend="handleSend" style="display: none" />
                  </div>
                  <button class="inputButtonSubmit" :class="{ disabled: formatMsg() }" @click="handleSend()" :disabled="formatMsg()">发送</button>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="chatDialogRight" v-if="chatType === 'system' && activeId">
          <div class="rightTitle">
            <div class="rightTitleFlex">
              <span>{{ activeInfo.nick }}</span>
              <em v-if="hasOwnProperty(activeInfo, 'ext') && activeInfo.ext.friendType == 'user' && activeInfo.ext.companyId != -1">{{ activeInfo.ext.companyName ? `(${activeInfo.ext.companyName})` : '' }}</em>
            </div>
            <div style="display: flex; align-items: center">
              <el-tooltip effect="dark" content="一键已读" placement="top" v-if="isInUserId(activeInfo)">
                <span class="clean ssfont ss-diy-xiangpica" @click="handleRead"></span>
              </el-tooltip>
              <el-tooltip effect="dark" content="关闭" placement="top">
                <i class="el-icon-close" @click="handleHide"></i>
              </el-tooltip>
            </div>
          </div>
          <div class="rightInfo right0" v-if="isDetail">
            <div class="infoLeft">
              <div class="infoLeftList system">
                <el-scrollbar ref="msgList" style="width: 100%; height: 100%">
                  <div class="friendItem cloumn">
                    <div class="friendItemFlex">
                      <div class="friendItemImg">
                        <img :src="activeInfo.avatar" :alt="activeInfo.nick" />
                      </div>
                      <div class="friendItemName">{{ activeInfo.nick }}</div>
                      <div class="friendItemPlus error" v-if="isFriend(activeInfo)" @click="handleMinus(activeInfo)">
                        <i class="el-icon-close"></i>
                        <span>删除好友</span>
                      </div>
                      <div class="friendItemPlus primary" v-if="!isFriend(activeInfo)" @click="handlePlus(activeInfo)">
                        <i class="el-icon-plus"></i>
                        <span>添加好友</span>
                      </div>
                    </div>
                    <div class="friendItemInfo" v-if="hasOwnProperty(activeInfo, 'ext') && activeInfo.ext.friendType == 'user' && activeInfo.ext.companyId != -1">
                      <span>公司：</span>
                      <b>{{ activeInfo.ext.companyName }}</b>
                    </div>
                    <div class="friendItemSend" v-if="isFriend(activeInfo)" @click="handleSponsorChat(activeInfo)">
                      <i class="chatfont chat-liaotian"></i>
                      <span>发起聊天</span>
                    </div>
                  </div>
                </el-scrollbar>
              </div>
            </div>
          </div>
          <div class="rightInfo" :class="{ right0: isInUserId(activeInfo) }" v-if="!isDetail">
            <div class="infoLeft">
              <div class="infoLeftList" :class="{ system: isInUserId(activeInfo) }">
                <el-scrollbar ref="msgList" style="width: 100%; height: 100%">
                  <!--系统消息-->
                  <template v-if="isInUserId(activeInfo)">
                    <div class="systemItem" v-for="(item, index) in msgList" :key="index">
                      <template v-if="!isMultiple(item)">
                        <div class="systemItemTop" @click="handleDetail(item, index, activeInfo)">
                          <div class="systemItemFlex">
                            <div class="systemItemTopTitle">{{ backTitle(item) }}</div>
                            <div class="systemItemTopTip" :class="{ noRead: !itemReadable(item) }" v-if="itemHasOwnProperty(item)">{{ itemReadable(item) ? '已读' : '未读' }}</div>
                          </div>
                          <div class="systemItemTopTime">{{ formatChatListTime(item.createTime) }}</div>
                        </div>
                        <div class="systemItemFlex start" v-html="htmlFormat(item, 'sys')" @click="handleDetail(item, index, activeInfo)"></div>
                      </template>
                      <template v-if="isMultiple(item)">
                        <div class="systemItemTop">
                          <div class="systemItemFlex">
                            <div class="systemItemTopTitle">{{ backTitle(item) }}</div>
                            <div class="systemItemTopTip" :class="{ noRead: !itemReadable(item) }" v-if="itemHasOwnProperty(item)">{{ itemReadable(item) ? '已读' : '未读' }}</div>
                          </div>
                          <div class="systemItemTopTime">{{ formatChatListTime(item.createTime) }}</div>
                        </div>
                        <div class="systemItemPromotion">
                          <div class="systemItemPromotionItem" v-for="ite in multipleInfo(item)" :key="ite.productId" @click="handleDetail(item, index, activeInfo, ite)">
                            <div class="systemItemPromotionImg"><img :src="ite.image" :alt="ite.productName" /></div>
                            <div class="systemItemPromotionFlex">
                              <div class="systemItemPromotionTitle">{{ ite.productName }}</div>
                              <div class="systemItemPromotionInfo">
                                <span>规格</span>
                                <b>{{ ite.specs }}</b>
                                <span>库存</span>
                                <b>{{ ite.count + ite.unit }}</b>
                                <span>价格：</span>
                                <b class="price">{{ `￥${ite.price}元${ite.unit ? '/' + ite.unit : ''}` }}</b>
                              </div>
                            </div>
                          </div>
                          <div class="systemItemPromotionMore" :class="itemShowMore(item).showMore ? 'up' : 'down'" v-if="multipleInfoNum(item) > 1" @click="handleShowMore(item, index)">{{ itemShowMore(item).showMore ? '收起' : '展开查看' }}</div>
                        </div>
                      </template>
                    </div>
                  </template>
                  <!--会话-->
                  <template v-if="!isInUserId(activeInfo)">
                    <div class="infoLeftItem" :class="{ self: name === item.userId }" v-for="(item, index) in msgList" :key="index">
                      <div class="itemTime" v-if="isShowChatTime(msgList, index)">{{ formatChatListTime(item.createTime) }}</div>
                      <div class="itemFlex">
                        <div class="itemFlexImg" v-if="name !== item.userId">
                          <img :src="item.avatar" :alt="item.nick" />
                        </div>
                        <template v-if="isVoiceFormat(item)">
                          <voice-player :duration="sendContent(item, 'duration')" :audio-url="sendContent(item, 'url')" :is-self="isSelf(item)" />
                        </template>
                        <template v-else>
                          <div class="itemFlexInfo" v-if="!isMultiple(item)">
                            <div style="max-width: 100%" v-html="htmlFormat(item)" @click="handleDetail(item)"></div>
                          </div>
                          <div class="itemFlexInfo sendProducts" v-if="isMultiple(item)">
                            <div class="sendProductsItemBox">
                              <div class="sendProductsItem" v-for="ite in productsFormat(item)" :key="ite.id" @click="handleDetailProduct(ite)">
                                <el-image :src="formatProductImg(ite)" :alt="ite.productName" class="sendProductsImg" />
                                <div class="sendProductsInfo">
                                  <div class="sendProductsTitle">{{ ite.productName }}</div>
                                  <div class="sendProductsDesc">规格：{{ ite.specs }}</div>
                                  <div class="sendProductsDesc">型号：{{ ite.model }}</div>
                                </div>
                              </div>
                            </div>
                            <div class="sendProductsMore" :class="itemShowMore(item).showMore ? 'up' : 'down'" v-if="multipleInfoNum(item) > 3" @click="handleShowMore(item, index)">{{ itemShowMore(item).showMore ? '收起' : '展开查看' }}</div>
                          </div>
                        </template>
                        <div class="itemFlexImg" v-if="name === item.userId">
                          <img :src="item.avatar" :alt="item.nick" />
                        </div>
                      </div>
                    </div>
                  </template>
                </el-scrollbar>
              </div>
              <!--输入框-->
              <div class="infoLeftInput" v-if="!isInUserId(activeInfo)">
                <el-input v-model="msg" @blur="changeCaretPosition" type="textarea" maxlength="300" resize="none" placeholder="请输入内容" clearable class="input-textarea" spellcheck="false" @keyup.enter.native="handleSend()"></el-input>
                <div class="inputButton">
                  <div class="inputButtonFlex">
                    <emoji location-top class="inputButtonIcon" @handleClickEmoji="handleClickEmoji" />
                    <send-file class="inputButtonIcon" ref="sendFile" @handleSend="handleSend" />
                    <send-product class="inputButtonIcon" ref="sendProduct" @handleSend="handleSend" />
                    <send-demand class="inputButtonIcon" ref="sendDemand" @handleSend="handleSend" />
                    <send-quote class="inputButtonIcon" ref="sendQuote" @handleSend="handleSend" />
                  </div>
                  <button class="inputButtonSubmit" :class="{ disabled: formatMsg() }" @click="handleSend()" :disabled="formatMsg()">发送</button>
                </div>
              </div>
            </div>
            <!--会话右侧用户操作-->
            <!-- 当前是好友聊天 -->
            <div class="infoRight" v-if="!isInUserId(activeInfo) && !activeInfo.groupId">
              <img :src="activeInfo.avatar" :alt="activeInfo.nick" class="infoRightImg" />
              <div class="infoRightTitle" :title="activeInfo.nick">{{ activeInfo.nick }}</div>
              <div class="infoRightBtn primary" v-if="isProvisional(activeInfo)" @click="handlePlus(activeInfo, 0)">
                <i class="el-icon-plus"></i>
                <span>添加好友</span>
              </div>
              <div class="infoRightBtn danger" v-if="!isProvisional(activeInfo)" @click="handleMinus(activeInfo)">
                <i class="el-icon-close"></i>
                <span>删除好友</span>
              </div>
            </div>
            <!-- 当前是群组聊天 -->
            <el-scrollbar class="infoRightGroupScroll" v-if="activeInfo.groupId">
              <div class="infoRightGroup">
                <div class="infoRightGroupItem" :title="user.nick" v-for="user in activeInfo.users || []" :key="user.userId">
                  <img :src="user.avatar" :alt="user.nick" class="infoRightGroupItemImg" />
                  <span class="infoRightGroupItemName">{{ user.nick }}</span>
                </div>
                <div class="infoRightGroupBtn" @click="handleOpenPlusGroup">
                  <i class="el-icon-plus"></i>
                  <span>添加群成员</span>
                </div>
                <div class="infoRightGroupBtn danger" @click="handleQuitGroup">
                  <i class="el-icon-close"></i>
                  <span>退出群组</span>
                </div>
              </div>
            </el-scrollbar>
          </div>
        </div>
        <div class="chatDialogRight" v-if="chatType === 'system' && myType === 'book' && !activeId && hasAdd">
          <div class="rightTitle">
            <div class="rightTitleFlex">
              <span>好友申请列表</span>
            </div>
            <i class="el-icon-close" @click="handleHide"></i>
          </div>
          <div class="rightInfo right0">
            <div class="infoLeft">
              <div class="infoLeftList system">
                <el-scrollbar ref="msgList" style="width: 100%; height: 100%">
                  <div class="friendItem" v-for="item in applyFriends" :key="item.from">
                    <div class="friendItemFlex">
                      <div class="friendItemImg round">
                        <img :src="item.fromAvatar" :alt="item.fromNick" />
                      </div>
                      <div class="friendItemName">{{ item.fromNick }}</div>
                      <div class="friendItemDesc">请求添加为好友</div>
                    </div>
                    <div class="friendItemFlex">
                      <div class="friendItemAudit error" @click="handleApply(item, -1)">忽略</div>
                      <div class="friendItemAudit primary" @click="handleApply(item, 1)">同意</div>
                    </div>
                  </div>
                </el-scrollbar>
              </div>
            </div>
          </div>
        </div>
        <div class="chatDialogEmpty" v-if="!service_activeId && !activeId && !hasAdd">您还未选中或者发起聊天，快去跟好友聊一聊吧</div>
        <i class="el-icon-close" @click="handleHide" v-if="!service_activeId && !activeId && !hasAdd" style="margin-top: 10px; margin-right: 10px; cursor: pointer"></i>
      </div>
    </el-dialog>
    <!-- 添加群成员 -->
    <el-dialog v-dialogDragBox title="" :visible.sync="plusGroupOpen" width="796px" append-to-body :before-close="handleClosePlusGroup">
      <div class="plusGroupDialog">
        <div class="plusGroupDialogLeft">
          <div class="plusGroupDialogLeftSearch">
            <el-input v-model="groupSearchKeyword" placeholder="请输入人员名称" size="small" clearable @input="handleGroupMemberSearch">
              <template slot="suffix">
                <el-button type="primary" size="mini" icon="el-icon-search" @click="handleGroupMemberSearch">搜索</el-button>
              </template>
            </el-input>
          </div>
          <el-scrollbar class="plusGroupDialogLeftScroll">
            <div class="plusGroupDialogLeftList">
              <div class="plusGroupDialogLeftItem" :class="{ checked: member.checked }" v-for="member in filteredAvailableMembers" :key="member.userId" @click="handleToggleMember(member)">
                <el-checkbox v-model="member.checked" @click.stop></el-checkbox>
                <img :src="member.avatar" :alt="member.nick" class="plusGroupDialogLeftItemImg" />
                <div class="plusGroupDialogLeftItemInfo">
                  <span class="plusGroupDialogLeftItemName">{{ member.nick }}</span>
                  <span class="plusGroupDialogLeftItemDesc" v-if="member.ext && member.ext.companyName">{{ member.ext.companyName }}</span>
                  <span class="plusGroupDialogLeftItemDesc" v-else>{{ member.ext && member.ext.friendType === 'user' ? '系统用户' : '客户用户' }}</span>
                </div>
              </div>
            </div>
          </el-scrollbar>
        </div>
        <div class="plusGroupDialogRight">
          <div class="plusGroupDialogRightTitle">
            <b>添加群成员</b>
            <span>已选择 {{ selectedMembers.length }} 个联系人</span>
          </div>
          <el-scrollbar class="plusGroupDialogRightScroll">
            <div class="plusGroupDialogRightList">
              <div class="plusGroupDialogRightItem" v-for="member in selectedMembers" :key="member.userId">
                <img :src="member.avatar" :alt="member.nick" class="plusGroupDialogRightItemImg" />
                <span class="plusGroupDialogRightItemName" :title="member.nick">{{ member.nick }}</span>
                <i class="el-icon-close plusGroupDialogRightItemClose" @click="handleRemoveSelectedMember(member)"></i>
              </div>
            </div>
          </el-scrollbar>
          <div class="plusGroupDialogRightBtn">
            <el-button class="plusGroupDialogRightBtnItem" size="small" @click="handleCancelAddMembers">取消</el-button>
            <el-button class="plusGroupDialogRightBtnItem primary" size="small" @click="handleConfirmAddMembers" :disabled="selectedMembers.length === 0">确定</el-button>
          </div>
        </div>
      </div>
    </el-dialog>
    <!-- 提示音 -->
    <audio ref="audio" src="https://oss.ziyouke.net/miniProgram/audio/notification.mp3"></audio>
  </div>
</template>

<script>
import Cookies from 'js-cookie'
import emoji from '@/components/emoji/index.vue'
import SendFile from './send/file'
import SendDemand from './send/demand'
import SendProduct from './send/product'
import SendQuote from './send/quote'
import { getToken } from '@/utils/auth'
import { searchUser } from '@/api/system/user'
import { parseTime, uniqueJsonArrByField } from '@/utils/ruoyi'
import { expireTimeFormat, formatChatListTime, formatChatTime, formatContent, formatContentImg, formatContentUrl, isJSON, isShowChatTime, removeHtmlTag } from '@/utils'
import { saveChatNum, saveFriends, saveProvisionals, saveGroups, updateGroupsInCache } from '@/utils/record'
import zlib from 'zlib'
import VoicePlayer from './voice'

export default {
  name: 'ChatDialog',
  components: { emoji, SendFile, SendDemand, SendProduct, SendQuote, VoicePlayer },
  props: {
    showBadge: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      caretPosition: 0,
      hasFile: false,
      keyword: undefined,
      // 搜索结果
      searchList: [],
      // 发送消息
      msg: '',
      socketOpen: false,
      chatType: 'system',
      myType: 'chat',
      isDetail: false,
      isSearch: false,
      hasAdd: false,
      plusGroupOpen: false,
      // 添加群成员相关数据
      groupSearchKeyword: '',
      selectedMemberIds: [], // 存储选中的成员ID
      // 防重复打开群组的记录
      lastOpenedGroupId: null,
      lastOpenedTime: 0
    }
  },
  created() {
    if (!!getToken()) this.chatType = this.service ? 'service' : 'system'
    window.addEventListener('beforeunload', event => {
      Cookies.set('refreshAudio', 'yes')
    })
    // 监听自动打开新创建群组的事件
    window.addEventListener('openNewGroup', this.handleOpenNewGroup)
    this.refreshAudio()
  },
  destroyed() {
    // 移除事件监听器
    window.removeEventListener('openNewGroup', this.handleOpenNewGroup)
  },
  updated: function () {
    this.scrollDown()
  },
  computed: {
    isconnect() {
      return this.$store.getters.isconnect
    },
    // 默认头像
    defaultAvatar() {
      return require('@/assets/images/Avatar.png')
    },
    // 当前用户
    curUser() {
      return this.$store.getters.curUser
    },
    // 好友列表
    friends() {
      return this.$store.getters.friends
    },
    // 临时好友列表
    provisionals() {
      return this.$store.getters.provisionals
    },
    // 好友未读数量
    chatNum() {
      return this.$store.getters.chatNum
    },
    // 当前聊天对象id
    activeId() {
      return this.$store.getters.activeId
    },
    // 当前聊天对象信息
    activeInfo() {
      return this.$store.getters.activeInfo
    },
    // 当前聊天记录
    msgList() {
      return this.$store.getters.msgList
    },
    // 当前用户用户名
    name() {
      return this.$store.getters.name
    },
    // 客服
    service_curUser() {
      return this.$store.getters.service_curUser
    },
    // 客服好友列表
    service_friends() {
      return this.$store.getters.service_friends
    },
    // 客服临时好友列表
    service_provisionals() {
      return this.$store.getters.service_provisionals
    },
    // 客服未读数量
    service_chatNum() {
      return this.$store.getters.service_chatNum
    },
    // 客服当前聊天对象id
    service_activeId() {
      return this.$store.getters.service_activeId
    },
    // 客服当前聊天对象信息
    service_activeInfo() {
      return this.$store.getters.service_activeInfo
    },
    // 客服当前聊天记录
    service_msgList() {
      return this.$store.getters.service_msgList
    },
    // 当前客服ID
    service() {
      return this.$store.getters.service
    },
    // 好友申请列表
    applyFriends() {
      return this.$store.getters.applyFriends || []
    },
    // 群组列表
    groups() {
      return this.$store.getters.groups || []
    },
    // 合并好友列表、临时好友并去重，按照时间排序
    mergedFriends() {
      const arr = uniqueJsonArrByField([...this.friends, ...this.provisionals], 'userId')
      return arr.sort((a, b) => {
        return new Date(b.createTime || '2000-01-01').getTime() - new Date(a.createTime || '2000-01-01').getTime()
      })
    },
    // 合并客服好友列表、临时好友并去重，按照时间排序
    mergedServiceFriends() {
      const arr = uniqueJsonArrByField([...this.service_friends, ...this.service_provisionals], 'userId')
      return arr.sort((a, b) => {
        return new Date(b.createTime || '2000-01-01').getTime() - new Date(a.createTime || '2000-01-01').getTime()
      })
    },
    // 格式化好友通讯录
    formattedBookFriends() {
      if (!this.friends.length) return []
      let pinyin = require('js-pinyin')
      pinyin.setOptions({ checkPolyphone: false, charCase: 0 })
      let list = this.friends.filter(item => item.userId != '100' && item.userId != '200' && item.userId != '300')
      list = list.sort((a, b) => pinyin.getFullChars(a.nick).localeCompare(pinyin.getFullChars(b.nick)))
      const newArr = []
      list.map(item => {
        const reg = /^[A-Z]+$/
        let key = pinyin.getFullChars(item.nick).charAt(0)
        if (!reg.test(key)) key = '#'
        const index = newArr.findIndex(subItem => subItem.letter === key)
        if (index < 0) {
          newArr.push({
            letter: key,
            data: [item]
          })
        } else {
          newArr[index].data.push(item)
        }
      })
      return newArr
    },
    // 可选成员列表（当前群组的好友，排除当前群组已有成员）
    availableMembers() {
      const currentGroup = this.groups.find(g => g.groupId === this.activeId)
      const currentGroupMembers = currentGroup ? currentGroup.users.map(u => u.userId) : []
      // prettier-ignore
      return this.friends.filter(friend => !currentGroupMembers.includes(friend.userId) && !this.isInUserId(friend)).map(friend => ({ ...friend, checked: this.selectedMemberIds.includes(friend.userId)}))
    },
    // 过滤的可选成员列表
    filteredAvailableMembers() {
      if (!this.groupSearchKeyword) {
        return this.availableMembers
      }
      return this.availableMembers.filter(member => member.nick.includes(this.groupSearchKeyword) || (member.ext && member.ext.companyName && member.ext.companyName.includes(this.groupSearchKeyword)))
    },
    // 已选成员列表
    selectedMembers() {
      return this.availableMembers.filter(member => this.selectedMemberIds.includes(member.userId))
    }
  },
  watch: {
    chatNum: {
      handler(newVal, oldVal) {
        if (newVal > oldVal) {
          this.playSound()
        }
      },
      immediate: true
    },
    service_chatNum: {
      handler(newVal, oldVal) {
        if (newVal > oldVal) {
          this.playSound()
        }
      },
      immediate: true
    },
    applyFriends: {
      handler(newVal, oldVal) {
        if (newVal.length > oldVal.length) {
          this.playSound()
        }
      },
      deep: true
    }
  },
  methods: {
    formatChatListTime,
    formatChatTime,
    formatContent,
    isShowChatTime,
    removeHtmlTag,
    formatContentImg,
    isJSON,
    expireTimeFormat,
    // 处理自动打开新创建群组的事件
    handleOpenNewGroup(event) {
      const { groupId } = event.detail || {}
      // 检查参数有效性
      if (!groupId) return
      // 使用全局标志防止多个Chat组件实例同时处理
      const globalKey = `openingGroup_${groupId}`
      if (window[globalKey]) {
        return
      }
      window[globalKey] = true
      // 防重复打开：检查是否在3秒内已经打开过同一个群组
      const now = Date.now()
      if (this.lastOpenedGroupId === groupId && now - this.lastOpenedTime < 3000) {
        delete window[globalKey] // 清除全局标志
        return
      }
      // 记录本次打开的群组和时间
      this.lastOpenedGroupId = groupId
      this.lastOpenedTime = now
      // 确保聊天窗口是打开的
      this.socketOpen = true
      this.chatType = 'system'
      this.myType = 'chat'
      // 延迟一下确保界面已更新
      this.$nextTick(() => {
        setTimeout(() => {
          this.handleGroupChat(groupId)
          // 处理完成后清除全局标志
          delete window[globalKey]
        }, 500)
      })
    },
    refreshAudio() {
      let autoAudio = Cookies.get('refreshAudio')
      if (autoAudio === 'yes') {
        const handleClick = () => {
          Cookies.remove('refreshAudio')
          document.removeEventListener('click', handleClick)
        }
        document.addEventListener('click', handleClick, { once: true })
      }
    },
    // 提示音
    playSound() {
      const audio = this.$refs.audio
      if (audio) audio.play()
    },
    // 加密
    encrypt(str) {
      return zlib.deflateSync(str).toString('base64')
    },
    // 解密
    decrypt(str) {
      try {
        return zlib.inflateSync(new Buffer(str, 'base64')).toString()
      } catch (e) {
        return str
      }
    },
    // 打开聊天框
    handleOpen() {
      this.myType = 'chat'
      this.chatType = this.service ? 'service' : 'system'
      this.handleClean()
      this.socketOpen = true
    },
    // 判断json是否包含字段
    hasOwnProperty(obj, key) {
      return Object.prototype.hasOwnProperty.call(obj, key)
    },
    // 滚动到底部
    scrollDown() {
      if (this.$refs['msgList']) this.$refs['msgList'].wrap.scrollTop = this.$refs['msgList'].wrap.scrollHeight
    },
    // 主动发起聊天
    handleSponsorChat(data) {
      this.myType = 'chat'
      this.handleClean()
      const item = this.friends.find(friend => friend.userId === data.userId)
      this.handleChat(item)
    },
    // 点击好友申请
    handleAdd() {
      this.handleClean()
      this.hasAdd = true
    },
    // 切换聊天对象
    handleChat(item) {
      this.hasAdd = false
      if (this.myType === 'book') {
        this.isDetail = true
        this.$store.dispatch('WEBSOCKET_SETACTIVEINFO', item)
        this.$store.dispatch('WEBSOCKET_SETACTIVEID', item.userId)
        return
      }
      const beginTime = new Date(new Date().setHours(0, 0, 0, 0) - 3 * 24 * 60 * 60 * 1000).getTime()
      const endTime = new Date().getTime()
      if (this.chatType === 'system') {
        this.keyword = undefined
        if (!this.activeId || this.activeId !== item.userId) {
          let msgHistoryCmd = {
            cmd: 19,
            type: 1,
            userId: this.name,
            beginTime,
            endTime
          }
          if (item.userId == '100') msgHistoryCmd.groupId = item.userId
          else msgHistoryCmd.fromUserId = item.userId
          this.$store.dispatch('WEBSOCKET_SEND', msgHistoryCmd)
          // 创建更新后的item副本
          let updatedItem = { ...item }
          if (!this.isInUserId(item)) {
            const chatNum = this.chatNum - item.num < 0 ? 0 : this.chatNum - item.num
            updatedItem.num = 0
            this.$store.dispatch('WEBSOCKET_SETCHATNUM', chatNum)
            saveChatNum(chatNum)
          }
          this.msg = ''
          this.$store.dispatch('WEBSOCKET_SETACTIVEINFO', updatedItem)
          this.$store.dispatch('WEBSOCKET_SETACTIVEID', updatedItem.userId)
          this.$store.dispatch('WEBSOCKET_SETMSGLIST', updatedItem.msgList)
          if (!this.isProvisional(item)) {
            const idx = this.friends.findIndex(friend => friend.userId === item.userId)
            if (idx !== -1) {
              const newFriends = [...this.friends]
              newFriends[idx] = updatedItem
              this.$store.dispatch('WEBSOCKET_GETFRIENDS', newFriends)
              saveFriends(newFriends)
            }
          } else {
            const idx = this.provisionals.findIndex(provisional => provisional.userId === item.userId)
            if (idx !== -1) {
              const newProvisionals = [...this.provisionals]
              newProvisionals[idx] = updatedItem
              this.$store.dispatch('WEBSOCKET_GETPROVISIONALS', newProvisionals)
              saveProvisionals(newProvisionals)
            }
          }
        }
      } else {
        if (!this.service_activeId || this.service_activeId !== item.userId) {
          let msgHistoryCmd = {
            cmd: 19,
            type: 1,
            userId: this.service,
            beginTime,
            endTime
          }
          if (item.userId == '100') msgHistoryCmd.groupId = item.userId
          else msgHistoryCmd.fromUserId = item.userId
          this.$store.dispatch('SERVICE_WEBSOCKET_SEND', msgHistoryCmd)
          // 创建更新后的item副本
          let updatedItem = { ...item }
          const chatNum = this.service_chatNum - item.num < 0 ? 0 : this.service_chatNum - item.num
          updatedItem.num = 0
          this.msg = ''
          this.$store.dispatch('SERVICE_WEBSOCKET_SETCHATNUM', chatNum)
          this.$store.dispatch('SERVICE_WEBSOCKET_SETACTIVEINFO', updatedItem)
          this.$store.dispatch('SERVICE_WEBSOCKET_SETACTIVEID', updatedItem.userId)
          this.$store.dispatch('SERVICE_WEBSOCKET_SETMSGLIST', updatedItem.msgList)
          if (!this.isServiceProvisional(item)) {
            const idx = this.service_friends.findIndex(friend => friend.userId === item.userId)
            if (idx !== -1) {
              const newServiceFriends = [...this.service_friends]
              newServiceFriends[idx] = updatedItem
              this.$store.dispatch('SERVICE_WEBSOCKET_GETFRIENDS', newServiceFriends)
            }
          } else {
            const idx = this.service_provisionals.findIndex(provisional => provisional.userId === item.userId)
            if (idx !== -1) {
              const newServiceProvisionals = [...this.service_provisionals]
              newServiceProvisionals[idx] = updatedItem
              this.$store.dispatch('SERVICE_WEBSOCKET_GETPROVISIONALS', newServiceProvisionals)
            }
          }
        }
      }
    },
    // 点击群组聊天
    handleGroupChat(groupId) {
      const group = this.groups.find(g => g.groupId === groupId)
      if (!group) return
      const beginTime = new Date(new Date().setHours(0, 0, 0, 0) - 3 * 24 * 60 * 60 * 1000).getTime()
      const endTime = new Date().getTime()
      if (!this.activeId || this.activeId !== groupId) {
        // 查询群组历史消息 - 修正：使用groupId而不是fromUserId
        let msgHistoryCmd = {
          cmd: 19,
          type: 1,
          userId: this.name,
          groupId: groupId, // 群组历史消息使用groupId
          beginTime,
          endTime
        }
        this.$store.dispatch('WEBSOCKET_SEND', msgHistoryCmd)
        // 创建更新后的group副本，清除未读数量
        let updatedGroup = { ...group }
        const chatNum = this.chatNum - group.num < 0 ? 0 : this.chatNum - group.num
        updatedGroup.num = 0
        updatedGroup.userId = groupId // 设置userId为groupId以兼容现有逻辑
        this.msg = ''
        this.$store.dispatch('WEBSOCKET_SETCHATNUM', chatNum)
        saveChatNum(chatNum)
        this.$store.dispatch('WEBSOCKET_SETACTIVEINFO', updatedGroup)
        this.$store.dispatch('WEBSOCKET_SETACTIVEID', groupId)
        this.$store.dispatch('WEBSOCKET_SETMSGLIST', updatedGroup.msgList || [])
        // 更新群组列表中的未读数量
        const newGroups = [...this.groups]
        const groupIdx = newGroups.findIndex(g => g.groupId === groupId)
        if (groupIdx !== -1) {
          newGroups[groupIdx] = updatedGroup
          this.$store.dispatch('WEBSOCKET_GETGROUPS', newGroups)
          saveGroups(newGroups)
        }
      }
    },
    // 一键已读
    handleRead() {
      const { activeId, activeInfo } = this
      if (!activeId) return
      // 创建更新后的用户信息
      let updatedInfo = { ...activeInfo }
      const resNum = this.chatNum - (updatedInfo.num || 0) < 0 ? 0 : this.chatNum - (updatedInfo.num || 0)
      // 更新总的未读数量
      this.$store.dispatch('WEBSOCKET_SETCHATNUM', resNum)
      saveChatNum(resNum)
      // 清除当前用户的未读数量
      updatedInfo.num = 0
      // 更新消息列表中的已读状态
      let list = this.msgList
      list.map(item => {
        if (this.isJSON(this.decrypt(item.msg))) {
          let info = JSON.parse(this.decrypt(item.msg))
          info.readable = true
          item.msg = JSON.stringify(info)
        }
      })
      this.$store.dispatch('WEBSOCKET_SETMSGLIST', list)
      // 更新当前活跃用户信息
      this.$store.dispatch('WEBSOCKET_SETACTIVEINFO', updatedInfo)
      // 检查并更新群组列表
      if (activeInfo.groupId) {
        const newGroups = [...this.groups]
        const groupIdx = newGroups.findIndex(group => group.groupId == activeId)
        if (groupIdx !== -1) {
          newGroups[groupIdx] = { ...newGroups[groupIdx], num: 0 }
          this.$store.dispatch('WEBSOCKET_GETGROUPS', newGroups)
          saveGroups(newGroups)
        }
      } else {
        // 创建新的friends数组副本并更新对应的用户
        const newFriends = [...this.friends]
        const friendIdx = newFriends.findIndex(friend => friend.userId == activeId)
        if (friendIdx !== -1) {
          newFriends[friendIdx] = { ...newFriends[friendIdx], num: 0 }
          this.$store.dispatch('WEBSOCKET_GETFRIENDS', newFriends)
          saveFriends(newFriends)
        }
        // 同时检查并更新临时好友列表
        const newProvisionals = [...this.provisionals]
        const provisionalIdx = newProvisionals.findIndex(provisional => provisional.userId == activeId)
        if (provisionalIdx !== -1) {
          newProvisionals[provisionalIdx] = { ...newProvisionals[provisionalIdx], num: 0 }
          this.$store.dispatch('WEBSOCKET_GETPROVISIONALS', newProvisionals)
          saveProvisionals(newProvisionals)
        }
      }
    },
    // 判断是否是系统消息
    isInUserId(item) {
      return item.userId == 100 || item.userId == 200 || item.userId == 300
    },
    // 发送内容
    handleSend(str = '') {
      if (this.chatType === 'system') {
        let { name, activeId, msg, curUser } = this
        msg = str || msg.trim()
        if (!msg) return
        msg = this.encrypt(msg)
        const createTime = parseTime(new Date().getTime(), '{y}/{m}/{d} {h}:{i}:{s}')
        // 检查是否是群组聊天
        const isGroupChat = this.groups.some(g => g.groupId === activeId)
        const chatCmd = {
          from: name,
          to: activeId,
          cmd: 11,
          createTime,
          chatType: isGroupChat ? 1 : 2, // 群组聊天为1，好友聊天为2
          msgType: 0,
          content: msg
        }
        // 如果是群组聊天，添加groupId字段
        if (isGroupChat) {
          chatCmd.groupId = activeId
          console.log(`[Chat] 发送群组消息:`, {
            groupId: activeId,
            from: name,
            content: msg.substring(0, 50) + (msg.length > 50 ? '...' : ''),
            chatType: chatCmd.chatType
          })
        } else {
          console.log(`[Chat] 发送个人消息:`, {
            to: activeId,
            from: name,
            content: msg.substring(0, 50) + (msg.length > 50 ? '...' : ''),
            chatType: chatCmd.chatType
          })
        }
        this.$store.dispatch('WEBSOCKET_SEND', chatCmd)
        this.msg = ''
        this.caretPosition = 0
        // 更新本地消息列表
        const messageItem = { userId: name, nick: curUser.nick, avatar: curUser.avatar, msg, createTime }
        if (isGroupChat) {
          // 群组消息处理
          const gIdx = this.groups.findIndex(item => item.groupId === activeId)
          if (gIdx !== -1) {
            const newGroups = [...this.groups]
            newGroups[gIdx] = {
              ...newGroups[gIdx],
              msgList: [...(newGroups[gIdx].msgList || []), messageItem],
              msg: msg,
              createTime: createTime
            }
            this.$store.dispatch('WEBSOCKET_SETMSGLIST', newGroups[gIdx].msgList)
            this.$store.dispatch('WEBSOCKET_GETGROUPS', newGroups)
            saveGroups(newGroups)
          }
        } else {
          // 好友消息处理
          const idx = this.friends.findIndex(item => item.userId === activeId)
          const pidx = this.provisionals.findIndex(item => item.userId === activeId)
          if (idx !== -1) {
            const newFriends = [...this.friends]
            newFriends[idx] = {
              ...newFriends[idx],
              msgList: [...(newFriends[idx].msgList || []), messageItem],
              msg: msg,
              createTime: createTime
            }
            this.$store.dispatch('WEBSOCKET_SETMSGLIST', newFriends[idx].msgList)
            this.$store.dispatch('WEBSOCKET_GETFRIENDS', newFriends)
            saveFriends(newFriends)
          }
          if (pidx !== -1) {
            const newProvisionals = [...this.provisionals]
            newProvisionals[pidx] = {
              ...newProvisionals[pidx],
              msgList: [...(newProvisionals[pidx].msgList || []), messageItem],
              msg: msg,
              createTime: createTime
            }
            this.$store.dispatch('WEBSOCKET_SETMSGLIST', newProvisionals[pidx].msgList)
            this.$store.dispatch('WEBSOCKET_GETPROVISIONALS', newProvisionals)
            saveProvisionals(newProvisionals)
          }
        }
      } else {
        let { service, service_activeId, msg, service_curUser } = this
        msg = str || msg.trim()
        if (!msg) return
        const createTime = parseTime(new Date().getTime(), '{y}/{m}/{d} {h}:{i}:{s}')
        const chatCmd = {
          from: service,
          to: service_activeId,
          cmd: 11,
          createTime,
          chatType: 2,
          msgType: 0,
          content: msg
        }
        this.$store.dispatch('SERVICE_WEBSOCKET_SEND', chatCmd)
        this.msg = ''
        this.caretPosition = 0
        const idx = this.service_friends.findIndex(item => item.userId === service_activeId)
        const pidx = this.service_provisionals.findIndex(item => item.userId === service_activeId)
        if (idx !== -1) {
          const item = { userId: service, nick: service_curUser.nick, avatar: service_curUser.avatar, msg, createTime }
          const newServiceFriends = [...this.service_friends]
          newServiceFriends[idx] = {
            ...newServiceFriends[idx],
            msgList: [...(newServiceFriends[idx].msgList || []), item],
            msg: msg,
            createTime: createTime
          }
          this.$store.dispatch('SERVICE_WEBSOCKET_SETMSGLIST', newServiceFriends[idx].msgList)
          this.$store.dispatch('SERVICE_WEBSOCKET_GETFRIENDS', newServiceFriends)
        }
        if (pidx !== -1) {
          const item = { userId: service, nick: service_curUser.nick, avatar: service_curUser.avatar, msg, createTime }
          const newServiceProvisionals = [...this.service_provisionals]
          newServiceProvisionals[pidx] = {
            ...newServiceProvisionals[pidx],
            msgList: [...(newServiceProvisionals[pidx].msgList || []), item],
            msg: msg,
            createTime: createTime
          }
          this.$store.dispatch('SERVICE_WEBSOCKET_SETMSGLIST', newServiceProvisionals[pidx].msgList)
          this.$store.dispatch('SERVICE_WEBSOCKET_GETPROVISIONALS', newServiceProvisionals)
        }
      }
    },
    // 添加好友
    // prettier-ignore
    handlePlus(item, val = 1) {
      let userName
      let type = 'user'
      if (val === 1) {
        userName = item.userName
        type = item.type
      } else {
        userName = item.userId
        type = 'user'
        const regPhone = /^1[3-9]\d{9}$/
        if (!regPhone.test(item.userId) && item.userId.includes('-')) type = 'customer'
      }
      const addFriendCmd = {
        cmd: 21,
        type: 1,
        friendId: userName,
        friendType: type
      }
      this.$store.dispatch('WEBSOCKET_SEND', addFriendCmd)
      if (type === 'user') this.$message.success('添加好友申请已发送，请等待对方确认！')
    },
    // 删除好友
    // prettier-ignore
    handleMinus(item) {
      const { nick, ext, userId } = item
      const friendType = (ext && ext.friendType) || 'user'
      this.$confirm(`确定删除好友${nick}吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const delFriendCmd = {
          cmd: 21,
          type: 0,
          friendId: userId,
          friendType,
          userId: this.name
        }
        this.$store.dispatch('WEBSOCKET_SEND', delFriendCmd)
        this.$store.dispatch('WEBSOCKET_SETACTIVEID', undefined)
        this.$store.dispatch('WEBSOCKET_SETACTIVEINFO', {})
        this.$store.dispatch('WEBSOCKET_SETMSGLIST', [])
        const nowFriends = this.friends.filter(friend => friend.userId !== item.userId)
        this.$store.dispatch('WEBSOCKET_GETFRIENDS', nowFriends)
        this.msg = ''
      }).catch(() => {
      })
    },
    // 判断是否是临时好友
    isProvisional(item) {
      return this.provisionals.findIndex(provisionals => provisionals.userId === item.userId) !== -1
    },
    // 判断是否是好友
    isFriend(item) {
      return this.friends.findIndex(friends => friends.userId === item.userId) !== -1
    },
    // 判断是否是客服的临时好友
    isServiceProvisional(item) {
      return this.service_provisionals.findIndex(provisionals => provisionals.userId === item.userId) !== -1
    },
    // 假如msg不为undefined去除空格
    formatMsg() {
      if (this.msg) return this.msg.trim() === ''
      return true
    },
    // 隐藏对话框
    handleHide() {
      this.myType = 'chat'
      this.chatType = this.service ? 'service' : 'system'
      this.handleClean()
      this.socketOpen = false
    },
    changeCaretPosition(e) {
      this.caretPosition = e.target.selectionStart
    },
    // 点击表情
    handleClickEmoji(emoji) {
      let index = this.caretPosition
      let str = this.msg
      this.msg = str.slice(0, index) + emoji + str.slice(index)
      this.caretPosition += emoji.length
    },
    // 切换消息类型
    handleTypeChange(type) {
      this.chatType = type
      this.myType = 'chat'
      this.handleClean()
    },
    // 切换我的消息类型
    handleMyTypeChange(type) {
      this.myType = type
      this.handleClean()
    },
    // 搜索
    handleSearch() {
      const { chatType, myType } = this
      const list = chatType === 'service' ? this.mergedServiceFriends : this.mergedFriends
      if (myType === 'book') {
        if (this.keyword) {
          searchUser({ nickname: this.keyword }).then(res => {
            this.searchList = res.data.map(item => {
              item.avatar = this.imgPath + item.avatar || this.defaultAvatar
              item.nick = item.nickName
              item.userId = item.userName
              return item
            })
            this.isSearch = true
          })
          this.isSearch = false
        }
      } else {
        this.searchList = list.filter(item => item.nick.includes(this.keyword))
        this.isSearch = false
      }
    },
    // 清除搜索
    handleSearchClear() {
      this.isSearch = false
      this.keyword = ''
      this.searchList = []
    },
    // 清空会话信息
    handleClean() {
      this.hasAdd = false
      this.isSearch = false
      this.isDetail = false
      this.keyword = undefined
      this.searchList = []
      this.$store.dispatch('WEBSOCKET_SETACTIVEID', undefined)
      this.$store.dispatch('WEBSOCKET_SETACTIVEINFO', {})
      this.$store.dispatch('WEBSOCKET_SETMSGLIST', [])
      this.$store.dispatch('SERVICE_WEBSOCKET_SETACTIVEID', undefined)
      this.$store.dispatch('SERVICE_WEBSOCKET_SETACTIVEINFO', {})
      this.$store.dispatch('SERVICE_WEBSOCKET_SETMSGLIST', [])
      this.msg = ''
    },
    // 返回标题
    backTitle(item) {
      if (this.isJSON(this.decrypt(item.msg))) {
        const info = JSON.parse(this.decrypt(item.msg))
        if (info.doType === 'workHandover') return info.userName + '向您发起工作交接'
        return info.title
      }
      return this.decrypt(item.msg)
    },
    // 消息内容格式化
    htmlFormat(item, type = '') {
      if (this.isJSON(this.decrypt(item.msg))) {
        const info = JSON.parse(this.decrypt(item.msg))
        let html
        // 如果doType为article则为文章,添加文章链接articleId、标题title、描述desc及图片img,并添加点击事件
        if (info.doType === 'article' && type === 'sys') {
          html = `<div class="systemItemInfo">${info.info}</div>`
          html += `<div class="systemItemImg">`
          if (info.image) html += `<img src="${info.image}" alt="${info.title}">`
          else html += `<img src="${this.defaultAvatar}" alt="${info.title}">`
          html += '</div>'
          return html
        }
        if (info.doType === 'article' && !type) {
          html = `<div class="itemFlexSystem">`
          html += `<div class="systemItemTitle">${info.title}</div>`
          html += `<div class="systemItemFlex">`
          html += `<div class="systemItemInfo">${info.info}</div>`
          html += `<div class="systemItemImg">`
          if (info.image) html += `<img src="${info.image}" alt="${info.title}">`
          else html += `<img src="${this.defaultAvatar}" alt="${info.title}">`
          html += '</div>'
          html += '</div>'
          html += '</div>'
          return html
        }
        if (info.doType === 'tender') {
          html = `<div class="itemFlexSystem">`
          html += `<div class="systemItemTitle">${info.title}</div>`
          html += `<div class="systemItemInfo">${info.info}</div>`
          html += '</div>'
          return html
        }
        // 如果doType为promotion则为促销品
        if (info.doType === 'promotion') {
          html = `<div class="systemItemInfo">${info.info}</div>`
          html += `<div class="systemItemImg">`
          if (info.image) html += `<img src="${info.image}" alt="${info.title}">`
          else html += `<img src="${this.defaultAvatar}" alt="${info.title}">`
          html += '</div>'
          return html
        }
        // 如果doType为unsalable则为滞销品
        if (info.doType === 'unsalable') {
          html = `<div class="systemItemInfo">${info.info}</div>`
          html += `<div class="systemItemImg">`
          if (info.image) html += `<img src="${info.image}" alt="${info.title}">`
          else html += `<img src="${this.defaultAvatar}" alt="${info.title}">`
          html += '</div>'
          return html
        }
        // 如果doType为demand则为需求,添加需求链接demandId，并添加点击事件
        if (info.doType === 'demand') {
          html = `<div class="systemItemInfo">${info.info}</div>`
          return html
        }
        // 如果doType为customized则为产品定制,添加需求链接requestId，并添加点击事件
        if (info.doType === 'customized') {
          html = `<div class="systemItemInfo">平台向您发布了一条定制产品的询价，并期待您能提供详细的报价。</div>`
          return html
        }
        // 如果doType为contract则为合同,添加合同链接contractId，并添加点击事件
        if (info.doType === 'contract') {
          html = `<div class="systemItemInfo">${info.info}</div>`
          return html
        }
        // 如果doType为deliveryList为发货清单，点击跳转至发货清单详情
        if (info.doType === 'deliveryList') {
          html = `<div class="systemItemInfo">您有新的发货清单已发货，请及时查看。</div>`
          return html
        }
        // 如果doType为sendProduct则为产品,添加产品信息，并添加点击事件
        if (info.doType === 'sendProduct') {
          html = `<div class="itemFlexProduct">`
          html += `<div class="productImg">`
          html += `<img src="${(info.picture1 ? this.imgPath + info.picture1 : '') || (info.diagram ? this.imgPath + info.diagram : '') || this.defaultAvatar}" alt="${info.productName}">`
          html += `</div>`
          html += `<div class="productInfo">`
          html += `<div class="productName">${info.productName}</div>`
          html += `<div class="productItem">`
          html += `<span>规格：</span>`
          html += `<b>${info.specs}</b>`
          html += `</div>`
          html += `<div class="productItem">`
          html += `<span>型号：</span>`
          html += `<b>${info.model}</b>`
          html += `</div>`
          html += `</div>`
          html += `</div>`
          return html
        }
        // 如果doType为sendDemand则为需求,添加需求信息，并添加点击事件
        if (info.doType === 'sendDemand') {
          html = `<div class="itemFlexCard">`
          html += `<div class="cardTip">询价需求</div>`
          html += `<div class="cardTitle">`
          html += `<span>需求标题：</span>`
          html += `<b>${info.title}</b>`
          html += `</div>`
          html += `<div class="cardItem">`
          html += `<span>供货截止时间：</span>`
          html += `<b>${info.supplyEndTime}</b>`
          html += `</div>`
          html += `<div class="cardItem">`
          html += `<span>剩余有效时间：</span>`
          html += `<b>${this.expireTimeFormat(info.expireTime)}</b>`
          html += `</div>`
          html += `</div>`
          return html
        }
        // 如果doType为sendContract则为合同,添加合同信息，并添加点击事件
        if (info.doType === 'sendContract') {
          html = `<div class="itemFlexCard">`
          html += `<div class="cardTip">采购合同</div>`
          html += `<div class="cardTitle">`
          html += `<span>供应商：</span>`
          html += `<b>${info.sellerName}</b>`
          html += `</div>`
          html += `<div class="cardItem">`
          html += `<span>订单金额${info.isIncludingTax ? '(含税)' : '(不含税)'}：</span>`
          html += `<b class="price">${info.amount ? '￥' + info.amount : ''}</b>`
          html += `</div>`
          html += `<div class="cardItem">`
          html += `<span>签订时间：</span>`
          html += `<b>${info.signingTime || ''}</b>`
          html += `</div>`
          html += `</div>`
          return html
        }
        // 如果doType为sendQuote则为报价,添加报价信息，并添加点击事件
        if (info.doType === 'sendQuote') {
          html = `<div class="itemFlexCard">`
          html += `<div class="cardTip">产品报价</div>`
          html += `<div class="cardTitle">`
          html += `<span>报价名称：</span>`
          html += `<b>${info.name}</b>`
          html += `</div>`
          html += `<div class="cardItem">`
          html += `<span>未税总价：</span>`
          if (info.method !== 'ton') html += `<b class="price">${'¥' + info.totalPrice + '元' + (this.hasOwnProperty(info, 'product') && this.hasOwnProperty(info.product, 'unit') && info.product.unit ? '/' + info.product.unit : '')}</b>`
          if (info.method === 'ton') html += `<b class="price">${'¥' + info.tonPrice + '元/吨'}</b>`
          html += `</div>`
          html += `<div class="cardItem">`
          html += `<span>创建时间：</span>`
          html += `<b>${info.createTime || ''}</b>`
          html += `</div>`
          html += `</div>`
          return html
        }
        // 如果doType为image，则只显示图片
        if (info.doType === 'image') {
          html = `<div class="itemFlexImgfile">`
          html += `<img src="${info.info}">`
          html += `</div>`
          return html
        }
        if (info.doType === 'workHandover') {
          html = `<div class="itemFlexSystem">`
          html += `<div class="systemItemTitle">交接内容及详细描述：${info.content}</div>`
          html += `<div class="systemItemFlex">`
          html += `<div class="systemItemInfo">请假时间：${this.parseTime(info.leaveStartTime, '{y}-{m}-{d} {h}:{i}')} - ${this.parseTime(info.leaveEndTime, '{y}-{m}-{d} {h}:{i}')}</div>`
          html += `<div class="systemItemInfo">联系电话：${info.phone}</div>`
          html += '</div>'
          html += '</div>'
          html += '</div>'
          return html
        }
        return `<div class="itemFlexMsg">${formatContentUrl(formatContent(this.decrypt(item.msg)))}</div>`
      } else {
        return `<div class="itemFlexMsg">${formatContentUrl(formatContent(this.decrypt(item.msg)))}</div>`
      }
    },
    listHtmlFormat(msg) {
      msg = JSON.parse(msg)
      if (msg.doType === 'sendProduct') return '采购产品：' + msg.productName
      if (msg.doType === 'sendProducts') return '采购多个产品：' + msg.info.map(item => item.productName).join('、')
      if (msg.doType === 'sendDemand') return '采购需求：' + msg.title
      if (msg.doType === 'sendContract') return '合同：供应商：' + msg.sellerName
      if (msg.doType === 'sendQuote') return '产品报价：' + msg.name
      if (msg.doType === 'image') return '[图片]'
      if (msg.doType === 'voice') return '[语音] ' + msg.duration + '″'
      if (msg.doType === 'workHandover') return '工作交接：' + msg.userName + '向您发起工作交接'
      if (this.hasOwnProperty(msg, 'doType')) return msg.title
      return JSON.stringify(msg)
    },
    // 同意、拒绝好友申请
    handleApply(item, type) {
      const applyCmd = { cmd: 25, userId: this.name, applyId: item.from, type }
      this.$store.dispatch('WEBSOCKET_SEND', applyCmd)
      this.$nextTick(() => {
        if (type == 1) {
          const msg = this.encrypt('我通过了你的朋友验证请求，现在我们可以开始聊天了')
          const createTime = parseTime(new Date().getTime(), '{y}/{m}/{d} {h}:{i}:{s}')
          const chatCmd = {
            from: this.name,
            to: item.from,
            cmd: 11,
            createTime,
            chatType: 2,
            msgType: 0,
            content: msg
          }
          this.$store.dispatch('WEBSOCKET_SEND', chatCmd)
        }
        this.$modal.msgSuccess('操作成功')
      })
    },
    // 点击不是好友的发送消息
    send(data = {}) {
      this.chatType = 'system'
      this.socketOpen = true
      this.$nextTick(() => {
        const beginTime = new Date(new Date().setHours(0, 0, 0, 0) - 3 * 24 * 60 * 60 * 1000).getTime()
        const endTime = new Date().getTime()
        const { userId, nick, avatar } = data
        const userItem = {
          userId, // 点击时的用户ID
          nick, // 点击时的昵称
          avatar: avatar != 'logo' ? avatar : this.defaultAvatar, // 点击时的头像
          msg: '',
          createTime: '',
          msgList: []
        }
        const idx = this.friends.findIndex(item => item.userId === userId)
        const pidx = this.provisionals.findIndex(item => item.userId === userId)
        if (idx != -1 || pidx != -1) {
          const msgHistoryCmd = {
            cmd: 19,
            type: 1,
            fromUserId: userId, // 点击时的用户ID
            userId: this.name,
            beginTime,
            endTime
          }
          this.$store.dispatch('WEBSOCKET_SEND', msgHistoryCmd)
        } else {
          const addFriendCmd = {
            cmd: 21,
            type: 1,
            friendId: userId,
            friendType: 'customer',
            userId: this.name
          }
          this.$store.dispatch('WEBSOCKET_SEND', addFriendCmd)
          setTimeout(() => {
            const msgHistoryCmd = {
              cmd: 19,
              type: 1,
              fromUserId: userId, // 点击时的用户ID
              userId: this.name,
              beginTime,
              endTime
            }
            this.$store.dispatch('WEBSOCKET_SEND', msgHistoryCmd)
          }, 100)
        }
        this.$store.dispatch('WEBSOCKET_SETACTIVEINFO', userItem)
        this.$store.dispatch('WEBSOCKET_SETACTIVEID', userId)
      })
    },
    // 判断item是否包含字段
    itemHasOwnProperty(item) {
      if (!this.isJSON(this.decrypt(item.msg))) {
        const info = this.decrypt(item.msg)
        if (info.doType === 'workHandover') return true
        return this.hasOwnProperty(info, 'readable')
      }
      const info = JSON.parse(this.decrypt(item.msg))
      if (info.doType === 'workHandover') return true
      return this.hasOwnProperty(info, 'readable')
    },
    // 返回item中的readable的值
    itemReadable(item) {
      const info = JSON.parse(this.decrypt(item.msg))
      return info.readable
    },
    // 返回item中的showMore的值
    itemShowMore(item) {
      return JSON.parse(this.decrypt(item.msg))
    },
    // 查看更多
    handleShowMore(item, index, type = '') {
      let info = JSON.parse(this.decrypt(item.msg))
      info.showMore = !info.showMore
      if (type === 'service') this.service_msgList[index].msg = JSON.stringify(info)
      else this.msgList[index].msg = JSON.stringify(info)
      this.$forceUpdate()
    },
    // 判断消息类型是否为语音
    isVoiceFormat(item) {
      if (this.isJSON(this.decrypt(item.msg))) {
        const info = JSON.parse(this.decrypt(item.msg))
        return info.doType === 'voice'
      } else if (item.msg.includes('voice')) {
        return true
      }
      return false
    },
    // 判断是否是多个产品推送
    isMultiple(item) {
      if (this.isJSON(this.decrypt(item.msg))) {
        const info = JSON.parse(this.decrypt(item.msg))
        return info.doType === 'promotionlist' || info.doType === 'unsalablelist' || info.doType === 'sendProducts'
      }
      return false
    },
    // 返回多个产品推送的
    multipleInfo(item) {
      if (this.isJSON(this.decrypt(item.msg))) {
        const info = JSON.parse(this.decrypt(item.msg))
        return info.showMore ? info.info : info.info.slice(0, 1)
      }
      return ''
    },
    multipleInfoNum(item) {
      if (this.isJSON(this.decrypt(item.msg))) {
        const info = JSON.parse(this.decrypt(item.msg))
        return info.info.length
      }
      return 0
    },
    // 格式化多个产品
    productsFormat(item) {
      if (this.isJSON(this.decrypt(item.msg))) {
        const info = JSON.parse(this.decrypt(item.msg))
        return info.showMore ? info.info : info.info.slice(0, 3)
      }
      return []
    },
    // 点击会话查看详情
    handleDetail(item, index = -1, data = {}, ite = {}) {
      if (this.isJSON(this.decrypt(item.msg))) {
        let info = JSON.parse(this.decrypt(item.msg))
        if (index !== -1) {
          if (!info.readable) {
            info.readable = true
            // 创建更新后的data副本
            let updatedData = { ...data }
            if (updatedData.num) updatedData.num--
            let chatNum = this.chatNum
            if (chatNum) chatNum--
            this.$store.dispatch('WEBSOCKET_SETCHATNUM', chatNum)
            saveChatNum(chatNum)
            // 更新消息列表
            let updatedMsgList = [...this.msgList]
            updatedMsgList[index].msg = JSON.stringify(info)
            updatedData.msgList = updatedMsgList
            this.$store.dispatch('WEBSOCKET_SETMSGLIST', updatedMsgList)
            // 更新friends或provisionals数组
            if (!this.isProvisional(data)) {
              const idx = this.friends.findIndex(friend => friend.userId === data.userId)
              if (idx !== -1) {
                const newFriends = [...this.friends]
                newFriends[idx] = updatedData
                this.$store.dispatch('WEBSOCKET_GETFRIENDS', newFriends)
                saveFriends(newFriends)
              }
            } else {
              const idx = this.provisionals.findIndex(provisional => provisional.userId === data.userId)
              if (idx !== -1) {
                const newProvisionals = [...this.provisionals]
                newProvisionals[idx] = updatedData
                this.$store.dispatch('WEBSOCKET_GETPROVISIONALS', newProvisionals)
                saveProvisionals(newProvisionals)
              }
            }
          } else {
            // 如果已经是已读状态，只更新消息列表
            let updatedMsgList = [...this.msgList]
            updatedMsgList[index].msg = JSON.stringify(info)
            this.$store.dispatch('WEBSOCKET_SETMSGLIST', updatedMsgList)
          }
        }
        const { doType } = info
        // doType为article为文章，点击跳转至文章详情
        if (doType === 'article') {
          const { href } = this.$router.resolve({ path: '/new', query: { id: info.articleId } })
          window.open(href, '_blank')
        }
        // doType为tender为招投标
        if (doType === 'tender') {
          const { href } = this.$router.resolve({ path: '/tender/detail', query: { bidId: info.id, source: 'chat' } })
          window.open(href, '_blank')
        }
        // doType为promotion为促销品
        if (doType === 'promotion') {
          const { href } = this.$router.resolve({ path: '/sale/detail', query: { productId: info.productId } })
          window.open(href, '_blank')
        }
        if (doType === 'promotionlist') {
          const { href } = this.$router.resolve({ path: '/sale/detail', query: { productId: ite.productId } })
          window.open(href, '_blank')
        }
        // doType为unsalable为滞销品
        if (doType === 'unsalable') {
          const { href } = this.$router.resolve({ path: '/deadstock/view', query: { id: info.unsalableId } })
          window.open(href, '_blank')
        }
        if (doType === 'unsalablelist') {
          const { href } = this.$router.resolve({ path: '/deadstock/view', query: { id: ite.productId } })
          window.open(href, '_blank')
        }
        // doType为demand、customized为询价，点击跳转至报价
        if (doType === 'demand' || doType === 'customized') {
          const { href } = this.$router.resolve({ path: '/formFilling', query: { requestId: info.requestId, source: 'msg' } })
          window.open(href, '_blank')
        }
        // doType为contrac为合同，点击跳转至合同详情
        if (doType === 'contract') {
          const { href } = this.$router.resolve({ path: '/cght', query: { requestId: info.requestId, source: 'msg' } })
          window.open(href, '_blank')
        }
        // doType为deliveryList为发货清单，点击跳转至发货清单详情
        if (doType === 'deliveryList') {
          const { href } = this.$router.resolve({ path: '/delivery/list', query: { requestId: info.requestId } })
          window.open(href, '_blank')
        }
        // doType为sendProduct为产品，点击跳转至产品详情
        if (doType === 'sendProduct') {
          this.$refs.sendProduct.handleView(info, 0)
        }
        // doType为sendDemand为需求，点击跳转至需求详情
        if (doType === 'sendDemand') {
          this.$refs.sendDemand.handleView(info)
        }
        // doType为sendQuote为报价，点击跳转至报价详情
        if (doType === 'sendQuote') {
          this.$refs.sendQuote.handleView(info)
        }
        // doType为sendContract为报价，点击跳转至报价详情
        if (doType === 'sendContract') {
          console.log('"点击了合同"', '点击了合同')
        }
        // doType为image为图片，点击全屏查看图片，并可下载
        if (doType === 'image') {
          this.$refs.sendFile.handleView(info.info)
        }
      }
    },
    // 查看产品详情
    handleDetailProduct(item = {}) {
      this.$refs.sendProduct.handleView(item, 0)
    },
    // 双击我的消息
    handleDoubleClick() {
      if (this.chatType === 'system') {
        const obj = this.mergedFriends.find(item => item.num > 0)
        if (obj) this.handleChat(obj)
      }
      if (this.chatType === 'service') {
        const obj = this.mergedServiceFriends.find(item => item.num > 0)
        if (obj) this.handleChat(obj)
      }
    },
    // 传送指定内容
    sendContent(item, type) {
      const info = JSON.parse(this.decrypt(item.msg))
      return info[type]
    },
    // 判断是否为自定义消息
    isSelf(item) {
      if (this.chatType === 'service') {
        // 客服聊天，判断是否是当前客服发送的消息
        return item.userId === this.service
      } else {
        // 普通聊天，判断是否是当前用户发送的消息
        return item.userId === this.name
      }
    },
    // 打开添加群成员弹窗
    handleOpenPlusGroup() {
      this.plusGroupOpen = true
      this.groupSearchKeyword = ''
      this.selectedMemberIds = [] // 清空选择
    },
    // 关闭添加群成员弹窗
    handleClosePlusGroup() {
      this.plusGroupOpen = false
      this.groupSearchKeyword = ''
      this.selectedMemberIds = [] // 清空选择
    },
    // 搜索群成员
    handleGroupMemberSearch() {
      // 搜索逻辑在计算属性中处理
    },
    // 切换成员选择状态
    handleToggleMember(member) {
      const index = this.selectedMemberIds.indexOf(member.userId)
      if (index > -1) {
        this.selectedMemberIds.splice(index, 1)
      } else {
        this.selectedMemberIds.push(member.userId)
      }
    },
    // 从右侧删除已选成员
    handleRemoveSelectedMember(member) {
      const index = this.selectedMemberIds.indexOf(member.userId)
      if (index > -1) {
        this.selectedMemberIds.splice(index, 1)
      }
    },
    // 确认添加群成员
    handleConfirmAddMembers() {
      if (this.selectedMemberIds.length === 0) {
        this.$message.warning('请选择要添加的群成员')
        return
      }

      // 批量邀请用户加入群组
      this.selectedMemberIds.forEach((userId, index) => {
        setTimeout(() => {
          const inviteCmd = {
            cmd: 27,
            operType: 'i',
            userId: userId,
            groupId: this.activeId
          }
          this.$store.dispatch('WEBSOCKET_SEND', inviteCmd)
        }, index * 100) // 每个请求间隔100ms，避免并发问题
      })

      this.$message.success(`正在邀请 ${this.selectedMemberIds.length} 个好友加入群组`)
      this.handleClosePlusGroup()
    },
    // 取消添加群成员
    handleCancelAddMembers() {
      this.handleClosePlusGroup()
    },
    // 更新群组缓存
    updateGroupsInCache(updatedGroups) {
      updateGroupsInCache(updatedGroups)
    },
    // 创建群组
    handleCreateGroup(users, groupName) {
      if (typeof users === 'string') {
        const chatId = users
        if (!chatId) {
          this.$message.warning('请选择要添加到群组的用户')
          return
        }
        users = [this.name, chatId]
        groupName = '群组' + Math.random().toString(36).substring(2, 15)
      }
      // 验证参数
      if (!users || !Array.isArray(users) || users.length < 2) {
        this.$message.warning('创建群组至少需要2个用户')
        return
      }
      if (!groupName || typeof groupName !== 'string') {
        groupName = '群组' + Math.random().toString(36).substring(2, 15)
      }
      // 确保当前用户在用户列表中
      if (!users.includes(this.name)) {
        users = [this.name, ...users]
      }
      const createGroupCmd = {
        cmd: 27,
        operType: 'c',
        users: users,
        name: groupName
      }
      this.$store.dispatch('WEBSOCKET_SEND', createGroupCmd)
    },
    // 退出群组
    // prettier-ignore
    handleQuitGroup() {
      if (!this.activeInfo.groupId) {
        this.$message.warning('当前不在群组聊天中')
        return
      }
      const isLeader = this.activeInfo.leader === this.name
      const confirmMessage = isLeader ? '您是群主，退出后群组将被解散，确定退出吗？' : '确定退出当前群组吗？'
      this.$confirm(confirmMessage, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let quitGroupCmd = {}
        if (isLeader) {
          // 发送解散群组命令
          quitGroupCmd = {
            cmd: 27,
            operType: 'd',
            groupId: this.activeInfo.groupId
          }
        } else {
          // 发送退出群组命令
          quitGroupCmd = {
            cmd: 27,
            operType: 'q',
            groupId: this.activeInfo.groupId
          }
        }
        this.$store.dispatch('WEBSOCKET_SEND', quitGroupCmd)
        // 立即清除会话状态和群组信息
        const currentGroupId = this.activeInfo.groupId
        if (this.activeId === currentGroupId) {
          this.$store.dispatch('WEBSOCKET_SETACTIVEID', '')
          this.$store.dispatch('WEBSOCKET_SETACTIVEINFO', {})
          this.$store.dispatch('WEBSOCKET_SETMSGLIST', [])
          this.msg = ''
        }
                  // 从群组列表中移除并更新缓存
        const currentGroups = this.$store.getters.groups || []
        const updatedGroups = currentGroups.filter(group => group.groupId !== currentGroupId)
        this.$store.dispatch('WEBSOCKET_GETGROUPS', updatedGroups)
        this.updateGroupsInCache(updatedGroups)
        this.$message.success(isLeader ? '正在解散群组...' : '正在退出群组...')
      }).catch(() => {
        // 用户取消操作
      })
    }
  }
}
</script>

<style scoped lang="scss">
@import '~@/assets/chat/index.scss';
::v-deep {
  .el-scrollbar__view {
    display: flex;
    flex-direction: column;
  }
  .el-scrollbar {
    .el-scrollbar__wrap {
      overflow-x: hidden;
    }
    .is-horizontal {
      display: none;
    }
  }
  .el-upload-list__item {
    transition: none !important;
    -webkit-transition: nonne !important;
  }
  .el-upload-list__item-name {
    transition: none !important;
    -webkit-transition: nonne !important;
  }
  .el-dialog {
    border-radius: 10px;
  }
  .el-dialog__header {
    display: none;
  }
  .el-dialog__body {
    padding: 0;
  }
}
.infoRightGroupScroll {
  width: 150px;
  background-color: #eceef3;
  border-radius: 10px;
  margin-left: 10px;
}
.infoRightGroup {
  padding: 15px 15px 0;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  gap: 15px;
  &Item {
    display: flex;
    align-items: center;
    cursor: pointer;
    &Img {
      width: 52px;
      height: 52px;
      border-radius: 50%;
      margin-right: 5px;
    }
    &Name {
      flex: 1;
      font-weight: 500;
      font-size: 12px;
      color: #666666;
      height: 20px;
      line-height: 20px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  &Btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 30px;
    margin: 15px 0;
    color: $blue;
    cursor: pointer;
    transition: all 0.3s;
    &:hover {
      opacity: 0.8;
    }
  }
}
.plusGroupDialog {
  background: #ffffff;
  box-shadow: 0px 1px 15px 0px rgba(0, 0, 0, 0.25);
  border-radius: 5px;
  border: 1px solid #2e73f3;
  display: flex;
  min-height: 512px;
  height: 80vh;
  position: relative;
  &::after {
    display: block;
    content: '';
    position: absolute;
    top: 0;
    left: 371px;
    width: 1px;
    height: 100%;
    background-color: #e1e2e2;
  }
  &Left {
    width: 370px;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    &Search {
      height: 32px;
      margin-top: 15px;
      margin-bottom: 20px;
      display: flex;
      justify-content: flex-end;
      padding-left: 97px;
      padding-right: 15px;
      flex-shrink: 0;
      ::v-deep {
        .el-input__suffix {
          display: flex;
          align-items: center;
          right: 3px;
          .el-button.el-button--primary {
            background-color: #e1ebff;
            color: #2e73f3;
            border-color: transparent;
            &:hover {
              opacity: 0.8;
            }
          }
        }
      }
    }
    &Scroll {
      flex: 1;
      overflow: hidden;
    }
    &List {
      display: flex;
      flex-direction: column;
    }
    &Item {
      display: flex;
      align-items: center;
      padding: 6px 20px;
      position: relative;
      &::after {
        display: block;
        content: '';
        position: absolute;
        bottom: 0;
        left: 20px;
        width: calc(100% - 20px);
        height: 1px;
        background-color: #e1e2e3;
      }
      &Img {
        width: 38px;
        height: 38px;
        border-radius: 50%;
        flex-shrink: 0;
        margin: 0 15px;
      }
      &Info {
        flex: 1;
        display: flex;
        flex-direction: column;
      }
      &Name {
        font-weight: 500;
        font-size: 14px;
        color: #333333;
        line-height: 20px;
      }
      &Desc {
        font-weight: 400;
        font-size: 12px;
        color: #666666;
        line-height: 20px;
      }
      &:hover {
        background-color: #e3edff;
      }
      &.checked {
        background-color: #e3edff;
      }
    }
  }
  &Right {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    padding-left: 30px;
    padding-right: 15px;
    &Title {
      line-height: 20px;
      margin-top: 17px;
      margin-bottom: 20px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex-shrink: 0;
      b {
        font-weight: 500;
        font-size: 14px;
        color: #333333;
      }
      span {
        font-weight: 400;
        font-size: 12px;
        color: #999999;
      }
    }
    &Scroll {
      flex: 1;
      overflow: hidden;
    }
    &List {
      display: flex;
      flex-wrap: wrap;
      gap: 15px;
      padding-top: 6px;
      margin-bottom: 15px;
    }
    &Item {
      width: calc((100% - 15px * 4) / 5);
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 3px;
      position: relative;
      &Img {
        width: 52px;
        height: 52px;
        border-radius: 5px;
      }
      &Name {
        font-weight: 500;
        font-size: 14px;
        color: #333333;
        width: 100%;
        line-height: 20px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      &Close {
        font-size: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        position: absolute;
        top: -5px;
        right: 0;
        width: 16px;
        height: 16px;
        background-color: rgba(0, 0, 0, 0.5);
        border-radius: 50%;
        color: #ffffff;
        cursor: pointer;
        &:hover {
          background-color: #2e73f3;
        }
      }
    }
    &Btn {
      flex-shrink: 0;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      padding-top: 15px;
      padding-bottom: 20px;
      border-top: 1px solid #e1e2e3;
      &Item {
        background-color: #cbd3e2;
        font-size: 14px;
        color: #2e73f3;
        padding-left: 26px;
        padding-right: 26px;
        &.primary {
          background: linear-gradient(107deg, #39a8f9 0%, #2787f8 100%);
          color: #ffffff;
        }
        &:hover {
          opacity: 0.8;
          background-color: #cbd3e2;
        }
        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
          &:hover {
            opacity: 0.5;
          }
        }
      }
    }
  }
}
</style>
