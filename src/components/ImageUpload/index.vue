<template>
  <div class="component-upload-image" :class="{ isRow: isRow, risk_box: fileNameShow }">
    <el-upload multiple :action="uploadImgUrl" list-type="picture-card" :on-success="handleUploadSuccess" :before-upload="handleBeforeUpload" :before-remove="beforRemove" :limit="limit" :data="datalist" :on-error="handleUploadError" :on-exceed="handleExceed" ref="imageUpload" :on-remove="handleDelete" :show-file-list="true" :headers="headers" :file-list="fileList" :on-preview="handlePictureCardPreview" class="default" :class="{ hide: this.fileList.length >= this.limit || isDisabled }" :accept="fileType.map(item => '.' + item).join(',')">
      <i class="el-icon-plus"></i>
      <div slot="file" slot-scope="{ file }">
        <template v-if="typeFormat(file) === 'pdf' || typeFormat(file) === 'PDF'">
          <img class="el-upload-list__item-thumbnail" src="~@/assets/images/pdf.png" alt="" />
          <span class="el-upload-list__item-actions">
            <span class="el-upload-list__item-preview" @click="handlePDFPreview(file)"><i class="el-icon-zoom-in"></i></span>
            <span class="el-upload-list__item-delete" @click="handleDelete(file)" v-if="deleteShow">
              <i class="el-icon-delete"></i>
            </span>
          </span>
        </template>
        <template v-else-if="typeFormat(file) === 'mp4' || typeFormat(file) === 'MP4'">
          <video  class="upload-video" controls :src="file.url" type="video/mp4"></video>
          <span class="el-upload-list__item-actions">
            <span class="el-upload-list__item-preview" @click="handleVideoPreview(file)"><i class="el-icon-zoom-in"></i></span>
            <span class="el-upload-list__item-delete" @click="handleDelete(file)" v-if="deleteShow">
              <i class="el-icon-delete"></i>
            </span>
          </span>
        </template>
        <template v-else>
          <img class="el-upload-list__item-thumbnail" :src="file.url" alt="" />
          <span class="el-upload-list__item-actions">
            <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)"><i class="el-icon-zoom-in"></i></span>
            <span class="el-upload-list__item-delete" @click="handleDelete(file)" v-if="deleteShow">
              <i class="el-icon-delete"></i>
            </span>
          </span>
        </template>
        <el-form :model="file" ref="form" @submit.native.prevent v-if="fileNameShow">
          <el-form-item label="">
            <el-input v-model="file.remake" @change="fileRemakeChange(file)" placeholder="请填写备注" class="file_name_box"></el-input>
          </el-form-item>
        </el-form>
      </div>
    </el-upload>

    <!-- 上传提示 -->
    <div class="el-upload__tip" slot="tip" v-if="showTip">
      <div style="padding-left: 10px; line-height: 25px" v-if="isRow">
        <div>
          请上传
          <template v-if="fileSize">
            大小不超过
            <b style="color: #f56c6c">{{ fileSize }}MB</b>
          </template>
        </div>
        <div>
          <template v-if="fileType">
            格式为
            <b style="color: #f56c6c">{{ fileType.join('/') }}</b>
          </template>
          的文件
        </div>
      </div>
      <template v-else>
        请上传
        <template v-if="fileSize">
          大小不超过
          <b style="color: #f56c6c">{{ fileSize }}MB</b>
        </template>
        <template v-if="fileType">
          格式为
          <b style="color: #f56c6c">{{ fileType.join('/') }}</b>
        </template>
        的文件
      </template>
    </div>

    <el-dialog v-dialogDragBox :visible.sync="dialogVisible" title="预览" width="800" append-to-body>
      <img :src="dialogImageUrl" style="display: block; max-width: 100%; margin: 0 auto" />
    </el-dialog>

    <el-dialog v-dialogDragBox title="预览" :visible.sync="pdfOpen" width="800" append-to-body>
      <div style="display: flex; justify-content: space-between; align-items: center; padding-bottom: 10px" v-if="pdfCount > 1">
        <el-button type="primary" size="small" @click="pdfCurrent--" :disabled="pdfCurrent < 2">
          <i class="el-icon-arrow-left"></i>
          上一页
        </el-button>
        <span>{{ pdfCurrent }} / {{ pdfCount }}</span>
        <el-button type="primary" size="small" @click="pdfCurrent++" :disabled="pdfCurrent >= pdfCount">
          下一页
          <i class="el-icon-arrow-right"></i>
        </el-button>
      </div>
      <div class="page-pdf">
        <Pdf :src="pdfUrl" :page="pdfCurrent" @num-pages="pdfCount = $event" />
      </div>
    </el-dialog>
    <!-- 视频预览 -->
    <el-dialog v-dialogDragBox title="预览" :visible.sync="videoOpen" width="800" append-to-body>
      <video :src="videoUrl" controls style="display: block; max-width: 100%; margin: 0 auto"></video>
    </el-dialog>
  </div>
</template>

<script>
import { getToken } from '@/utils/auth'

export default {
  name: 'imageUpload',
  props: {
    value: [String, Object, Array, Boolean],
    // 图片数量限制
    limit: {
      type: Number,
      default: 500
    },
    compress: {
      type: Boolean,
      default: true
    },
    // 大小限制(MB)
    fileSize: {
      type: Number,
      default: 5
    },
    // 文件类型, 例如['png', 'jpg', 'jpeg']
    fileType: {
      type: Array,
      default: () => ['png', 'jpg', 'jpeg']
    },
    // 是否显示添加按钮
    isDisabled: {
      type: Boolean,
      default: false
    },
    // 是否显示提示
    isShowTip: {
      type: Boolean,
      default: true
    },
    // 是否左右布局
    isRow: {
      type: Boolean,
      default: false
    },
    // 是否显示文件名（补充信息模块使用）
    fileNameShow: {
      type: Boolean,
      default: false
    },
    // 是否显示删除
    deleteShow: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      // pdf预览
      pdfOpen: false,
      pdfCurrent: 1,
      pdfCount: 0,
      pdfUrl: '',
      number: 0,
      uploadList: [],
      dialogImageUrl: '',
      dialogVisible: false,
      hideUpload: false,
      baseUrl: process.env.VUE_APP_BASE_API,
      uploadImgUrl: process.env.VUE_APP_BASE_API + '/common/upload', // 上传的图片服务器地址
      headers: {
        Authorization: 'Bearer ' + getToken()
      },
      fileList: [],
      datalist: {
        // compress: this.compress
      },
      fileRemake: '',
      videoUrl: '',
      videoOpen: false
    }
  },
  watch: {
    value: {
      handler(val) {
        if (val) {
          // 首先将值转为数组
          const list = Array.isArray(val) ? val : this.value.split(',')
          // 然后将数组转为对象数组
          this.fileList = list.map(item => {
            if (typeof item === 'string') {
              if (item.indexOf(this.baseUrl) === -1) {
                item = { name: this.baseUrl + item, url: this.baseUrl + item }
              } else {
                item = { name: item, url: item }
              }
            }
            return item
          })
        } else {
          this.fileList = []
          return []
        }
      },
      deep: true,
      immediate: true
    }
  },
  computed: {
    // 是否显示提示
    showTip() {
      return this.isShowTip && (this.fileType || this.fileSize)
    }
  },
  methods: {
    // 上传前loading加载
    handleBeforeUpload(file) {
      if (!getToken()) {
        this.$message.error(`请先登录`)
        return false
      }
      this.datalist['compress'] = this.compress
      let isImg = false
      if (this.fileType.length) {
        let fileExtension = ''
        if (file.name.lastIndexOf('.') > -1) {
          fileExtension = file.name.slice(file.name.lastIndexOf('.') + 1)
        }
        isImg = this.fileType.some(type => {
          if (file.type.indexOf(type) > -1) return true
          if (fileExtension && fileExtension.indexOf(type) > -1) return true
          return false
        })
      } else {
        isImg = file.type.indexOf('image') > -1
      }
      if (!isImg) {
        this.$modal.msgError(`文件格式不正确, 请上传${this.fileType.join('/')}图片格式文件!`)
        return false
      }
      if (this.fileSize) {
        const isLt = file.size / 1024 / 1024 < this.fileSize
        if (!isLt) {
          this.$modal.msgError(`上传头像图片大小不能超过 ${this.fileSize} MB!`)
          return false
        }
      }
      this.$modal.loading('正在上传图片，请稍候...')
      this.number++
    },
    // 文件个数超出
    handleExceed() {
      this.$modal.msgError(`上传文件数量不能超过 ${this.limit} 个!`)
    },
    // 上传成功回调
    handleUploadSuccess(res, file) {
      if (res.code === 200) {
        this.uploadList.push({ name: res.fileName, url: res.fileName })
        this.uploadedSuccessfully()
      } else {
        this.number--
        this.$modal.closeLoading()
        this.$modal.msgError(res.msg)
        this.$refs.imageUpload.handleRemove(file)
        this.uploadedSuccessfully()
      }
    },
    // 删除图片前
    beforRemove(file, fileList) {
      if (this.fileNameShow) {
        return !window.event.keyCode === 8
      }
    },
    // 删除图片
    handleDelete(file) {
      const findex = this.fileList.map(f => f.name).indexOf(file.name)
      if (findex > -1) {
        this.fileList.splice(findex, 1)
        this.$emit('input', this.listToString(this.fileList))
      }
    },
    // 上传失败
    handleUploadError() {
      this.$modal.msgError('上传图片失败，请重试')
      this.$modal.closeLoading()
    },
    // 上传结束处理
    uploadedSuccessfully() {
      if (this.number > 0 && this.uploadList.length === this.number) {
        this.fileList = this.fileList.concat(this.uploadList)
        this.uploadList = []
        this.number = 0
        this.$emit('input', this.listToString(this.fileList))
        this.$modal.closeLoading()
      }
    },
    // 预览
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    // 对象转成指定字符串分隔
    listToString(list, separator) {
      let strs = ''
      separator = separator || ','
      for (let i in list) {
        if (list[i].url) {
          strs += list[i].url.replace(this.baseUrl, '') + separator
        }
      }
      return strs != '' ? strs.substr(0, strs.length - 1) : ''
    },
    // 判断文件类型
    typeFormat(file) {
      const fileStr = file.name || file.url
      let index = fileStr.lastIndexOf('.')
      return fileStr.substr(index + 1)
    },
    // 预览PDF
    handlePDFPreview(file) {
      this.pdfUrl = file.url
      this.pdfCurrent = 1
      this.pdfOpen = true
    },
    // 修改图片备注
    fileRemakeChange(file) {
      this.$emit('remake', this.fileList)
    },
    handleKeydown(event) {
      console.log(event)
    },
    // 预览视频
    handleVideoPreview(file) {
      this.videoUrl = file.url
      this.videoOpen = true
    }
  }
}
</script>
<style scoped lang="scss">
// .el-upload--picture-card 控制加号部分
.default {
  // 去掉动画效果
  ::v-deep .el-list-enter-active,
  ::v-deep .el-list-leave-active {
    transition: all 0s;
  }
  ::v-deep .el-list-enter,
  .el-list-leave-active {
    opacity: 0;
    transform: translateY(0);
  }
}
::v-deep {
  .default.hide .el-upload--picture-card {
    display: none;
  }
}
.upload-video{
  max-width: 148px;
  max-height: 148px;
  object-fit: cover;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}
.isRow {
  display: flex;
  align-items: center;
}
.risk_box {
  ::v-deep {
    .el-upload-list--picture-card .el-upload-list__item {
      border: none;
      height: 200px;
    }
    .el-upload-list--picture-card .el-upload-list__item-actions {
      height: 148px;
    }
  }
  .file_name_box {
    color: #000;
  }
}
</style>
