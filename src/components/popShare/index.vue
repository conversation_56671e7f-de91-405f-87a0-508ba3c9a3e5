<template>
  <!-- 最外层需要一个透明遮罩，监听点击事件 -->
  <div class="mo-mask" :style="{ 'z-index': zIndex }" @click="handleClose">
    <transition name="el-fade-in-linear">
      <div class="pop-share" :style="style">
        <div class="pop-share__item" @click.stop="handleClick('search')">
          <span class="pop-share__item__title" :title="title">{{ title }}</span>
          <i class="el-icon-search pop-share__item__button"></i>
        </div>
      </div>
    </transition>
  </div>
</template>
<script>
export default {
  name: 'PopShare',
  components: {},
  props: {
    title: {
      type: String,
      default: ''
    },
    top: {
      type: Number,
      default: 0
    },
    left: {
      type: Number,
      default: 0
    },
    // 点击事件回调
    onItemClick: {
      type: Function,
      default: null
    }
  },
  data() {
    return {
      zIndex: 0
    }
  },
  computed: {
    style() {
      return {
        top: this.top + 'px',
        left: this.left + 'px'
      }
    }
  },
  mounted() {
    this.zIndex = this.getMaxZIndex() + 1
  },
  created() {},
  methods: {
    handleClick(type = undefined) {
      if (this.onItemClick) {
        this.onItemClick({ value: type })
      }
      this.handleClose()
    },
    handleClose(e) {
      this.$parent.handleClose()
    },
    // js获取当前窗口最大z-index
    getMaxZIndex() {
      var elements = document.querySelectorAll('*')
      let maxZindex = 0
      for (var i = 0; i < elements.length; i++) {
        maxZindex = Math.max(maxZindex, elements[i].style.zIndex || 0)
      }
      return maxZindex
    }
  }
}
</script>

<style lang="scss" scoped>
// 遮罩层
.mo-mask {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0);
}
.pop-share {
  position: fixed;
  background-color: #ffffff;
  box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  line-height: 1.5;
  border-radius: 6px;
  transform: translate(-50%, -50px);
}
.pop-share__item {
  font-size: 12px;
  line-height: 32px;
  height: 32px;
  padding: 0 12px;
  box-sizing: border-box;
  color: #333333;
  position: relative;
  cursor: pointer;
  white-space: nowrap;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
.pop-share__item__title {
  font-family: Arial, MicrosoftYaHei;
  margin-right: 5px;
  font-size: 13px;
  line-height: 13px;
  max-width: 156px;
  white-space: nowrap;
  text-overflow: ellipsis;
  vertical-align: text-bottom;
  overflow: hidden;
}
.pop-share__item__button {
  color: #2e73f3;
  font-weight: bold;
  font-size: 16px;
}
</style>
