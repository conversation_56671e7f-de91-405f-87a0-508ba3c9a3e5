import Vue from 'vue'
import PopShare from './index.vue'
// 所有实例列表
const instances = []
// 显示
function show(props) {
  const instance = new Vue({
    methods: {
      handleClose() {
        close(instance)
      }
    },
    render(h) {
      return h(PopShare, { props })
    }
  })
  instance.$mount()
  document.body.appendChild(instance.$el)
  instances.push(instance)
}
// 关闭
function close(instance) {
  if (instance) {
    document.body.removeChild(instance.$el)
  }
  const index = instances.findIndex(item => item === instance)
  if (index > -1) {
    instances.splice(index, 1)
  }
}
function closeAll() {
  for (const instance of instances) {
    close(instance)
  }
}
export default {
  show,
  close,
  closeAll
}
