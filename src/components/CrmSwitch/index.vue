<template>
  <div class="custom-switch" :style="{ backgroundColor: value ? activeColor : inactiveColor }" @click="handleClick">
    <div class="switch-content">
      <span class="switch-text" :class="{ 'is-checked': value }">{{ value ? activeText : inactiveText }}</span>
      <span class="switch-core" :class="{ 'is-checked': value }">
        <span class="clean ssfont ss-diy-youjian switch-icon"></span>
      </span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CrmSwitch',
  props: {
    title: {
      type: String,
      default: ''
    },
    value: {
      type: [<PERSON><PERSON><PERSON>, String, Number],
      default: false
    },
    activeValue: {
      type: [Boolean, String, Number],
      default: true
    },
    activeText: {
      type: String,
      default: ''
    },
    activeColor: {
      type: String,
      default: '#2E73F3'
    },
    inactiveValue: {
      type: [<PERSON>olean, String, Number],
      default: false
    },
    inactiveText: {
      type: String,
      default: ''
    },
    inactiveColor: {
      type: String,
      default: '#D6DAE4'
    }
  },
  methods: {
    handleClick() {
      const newValue = this.value === this.activeValue ? this.inactiveValue : this.activeValue
      this.$emit('input', newValue)
      this.$emit('change', newValue)
    }
  }
}
</script>

<style lang="scss" scoped>
.custom-switch {
  display: inline-flex;
  align-items: center;
  position: relative;
  cursor: pointer;
  padding: 1px;
  border-radius: 20px;
  transition: background-color 0.3s;
  .switch-content {
    display: flex;
    align-items: center;
    position: relative;
    height: 26px;
  }
  .switch-text {
    color: #7d8191;
    font-size: 14px;
    line-height: 26px;
    margin-right: 10px;
    margin-left: 35px;
    transition: all 0.3s;
    white-space: nowrap;
    &.is-checked {
      color: #fff;
      margin-left: 10px;
      margin-right: 35px;
    }
  }
  .switch-core {
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    left: 0;
    transition: all 0.3s;
    &.is-checked {
      left: calc(100% - 26px);
    }
  }
  .switch-icon {
    font-size: 26px;
    color: #fff;
    transition: all 0.3s;
  }
}
</style>
