<template>
  <div>{{ countdown }}</div>
</template>

<script>
export default {
  name: 'Countdown',
  props: {
    expireTime: {
      type: String,
      required: true
    },
    pattern: {
      type: String,
      default: '{d}-{h}-{m}-{s}'
    }
  },
  data() {
    return {
      countdown: '',
      intervalId: null
    }
  },
  methods: {
    calculateCountdown() {
      const expireTime = new Date(this.expireTime.replace(/-/g, '/')).getTime()
      const now = new Date().getTime()
      const timeDiff = expireTime - now

      if (timeDiff > 0) {
        const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24))
        const hours = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
        const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60))
        const seconds = Math.floor((timeDiff % (1000 * 60)) / 1000)

      this.countdown = this.pattern.replace('{d}', days > 0 ? days + '天' : '').replace('{h}', (days > 0 || hours > 0) ? hours + '小时' : '').replace('{m}', (days > 0 || hours > 0 || minutes > 0) ? minutes + '分' : '').replace('{s}', seconds + '秒').trim();
      } else {
        this.countdown = '已过期'
      }
    },
    startCountdown() {
      this.calculateCountdown()
      this.intervalId = setInterval(this.calculateCountdown, 1000)
    },
    clearCountdown() {
      if (this.intervalId) {
        clearInterval(this.intervalId)
        this.intervalId = null
      }
    }
  },
  mounted() {
    this.startCountdown()
  },
  beforeDestroy() {
    this.clearCountdown()
  }
}
</script>
