<template>
  <div>
    <!-- 合同详情 -->
    <el-dialog v-dialogDragBox title="合同详情" :visible.sync="open" width="1150px" class="custom-dialog" append-to-body @close="handleClose">
      <div class="print" v-if="info.status !== -1">
        <div class="print-item" @click="handlePrint(info)">
          <i class="el-icon-printer"></i>
          打印
        </div>
        <div class="print-item" @click="handleDownload(info)">
          <i class="el-icon-download"></i>
          下载
        </div>
      </div>
      <div style="text-align: center" :style="{ zoom: zoom }">
        <img style="max-width: 100%" :src="info.file" v-if="isBase64Image(info.file)" />
        <show-template :certify="!!info.certify" :signet="signet" :signature="info.signature" :content="info.file" v-else-if="isJSON(info.file)" />
        <img style="max-width: 100%" :src="info.file" v-else />
      </div>
      <div slot="footer">
        <button type="button" class="custom-dialog-btn primary" @click="handleClose">关 闭</button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { contractDetail } from '@/api/purchase'
import { getConfigDetail2 } from '@/api/config'
import showTemplate from '@/views/purchase/contract/showTemplate'

export default {
  name: 'Contract',
  components: { showTemplate },
  data() {
    return {
      open: false,
      info: {},
      status: -1,
      zoom: 1,
      signet: false
    }
  },
  methods: {
    // 查看详情
    handleView(data = {}) {
      const { contractId } = data
      if (!contractId) {
        this.$message.error('参数错误，请稍后重试')
        return
      }
      contractDetail({ contractId }).then(res => {
        if (res.code === 200) {
          this.info = res.data
          if (this.isJSON(this.info.file)) {
            getConfigDetail2({ configKey: 'signatures', companyId: res.data.companyId, creator: res.data.creator }).then(res => {
              this.signet = res?.data?.configValue || ''
              this.open = true
              this.resizeFun()
            })
          } else {
            this.open = true
            this.resizeFun()
          }
        } else this.$message.error(res.msg)
      })
    },
    // 调整合同图片大小
    resizeFun() {
      const devicePixelRatio = window.devicePixelRatio
      if (devicePixelRatio !== 1) {
        if (!!window.ActiveXObject || 'ActiveXObject' in window) {
        } else this.zoom = 1 / devicePixelRatio
      }
    },
    // 打印
    handlePrint(data) {
      print({ printable: data.file, type: 'image', base64: true })
    },
    // 下载
    handleDownload(data) {
      const imgUrl = data.file
      const fileName = data.serial
      if (window.navigator.msSaveOrOpenBlob) {
        const bstr = atob(imgUrl.split(',')[1])
        let n = bstr.length
        const u8arr = new Uint8Array(n)
        while (n--) {
          u8arr[n] = bstr.charCodeAt(n)
        }
        const blob = new Blob([u8arr])
        window.navigator.msSaveOrOpenBlob(blob, fileName + '.' + 'png')
      } else {
        const a = document.createElement('a')
        a.href = imgUrl
        a.setAttribute('download', fileName)
        a.click()
      }
    },
    // 判断是不是json
    isJSON(str) {
      try {
        JSON.parse(str)
        return true
      } catch (e) {
        return false
      }
    },
    // 判断是不是base64
    isBase64Image(str) {
      if (typeof str === 'string') {
        return str.startsWith('data:image/') && str.includes(';base64,')
      }
      return false
    },
    // 关闭
    handleClose() {
      this.open = false
      this.$emit('callBack')
    }
  }
}
</script>
<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
.print {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 0 20px;
  &-item {
    padding: 0 50px;
    height: 50px;
    line-height: 50px;
    text-align: center;
    margin-left: 10px;
    border: 1px solid #cbd6e2;
    border-radius: 5px;
    font-size: 16px;
    color: $white;
    cursor: pointer;
    background-color: $blue;
    &:hover {
      opacity: 0.8;
    }
  }
}
</style>
