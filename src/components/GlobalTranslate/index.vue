<template>
  <div>
    <div class="translate" :class="{ fixed: fixed }" @click="handleClick">
      <template v-if="fixed">
        <img :src="filter('icon')" :alt="filter('name')" class="img" />
        <span class="text text-warp">
          切换
          <br />
          语言
        </span>
      </template>
      <template v-else>
        <span class="text text-primary" :class="{ 'text-up': showTranslateBox }">{{ filter('name') }}</span>
      </template>
      <div class="translate-box" v-if="showTranslateBox">
        <div class="translate-item" :class="{ active: currentLanguage === item.value }" v-for="item in languages" :key="item.id" @click="changeLanguage(item.value)">{{ item.name }}</div>
      </div>
    </div>
  </div>
</template>
<script>
import translate from 'i18n-jsautotranslate'
import zhUrl from '@/assets/images/language-zh.png'
import enUrl from '@/assets/images/language-en.png'
import koUrl from '@/assets/images/language-korean.png'
import ruUrl from '@/assets/images/language-russian.png'
import { getAllLanguage } from '@/api/language'

export default {
  name: 'GlobalTranslate',
  props: {
    fixed: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      languages: [
        { name: '中文', value: 'chinese_simplified', icon: zhUrl, iconTxt: '中文' },
        { name: 'English', value: 'english', icon: enUrl, iconTxt: 'EN' },
        { name: '한국어', value: 'korean', icon: koUrl, iconTxt: '한국어' },
        { name: 'Pусский', value: 'russian', icon: ruUrl, iconTxt: 'Pусский' }
      ],
      currentLanguage: '',
      showTranslateBox: false
    }
  },
  async mounted() {
    const language = await getAllLanguage()
    const { data: list = [] } = language
    translate.setAutoDiscriminateLocalLanguage() // 自动识别语言
    translate.service.use('client.edge') // 微软翻译
    translate.language.setUrlParamControl()
    translate.selectLanguageTag.show = false //不出现的select的选择语言
    translate.listener.start()
    if (!!list.length) {
      list.map(item => {
        if (item.english) {
          translate.nomenclature.append('chinese_simplified', 'english', `${item.chinese}=${item.english}`)
          translate.office.append('english', `${item.chinese}=${item.english}`)
        }
        if (item.korean) {
          translate.nomenclature.append('chinese_simplified', 'korean', `${item.chinese}=${item.korean}`)
          translate.office.append('korean', `${item.chinese}=${item.korean}`)
        }
        if (item.russian) {
          translate.nomenclature.append('chinese_simplified', 'russian', `${item.chinese}=${item.russian}`)
          translate.office.append('russian', `${item.chinese}=${item.russian}`)
        }
      })
    }
    translate.execute()
    this.currentLanguage = translate.language.getCurrent()

    // 添加点击外部关闭翻译框的事件监听
    document.addEventListener('click', this.handleOutsideClick)
  },
  beforeDestroy() {
    // 组件销毁前移除事件监听
    document.removeEventListener('click', this.handleOutsideClick)
  },
  methods: {
    changeLanguage(lang) {
      translate.changeLanguage(lang)
      this.currentLanguage = translate.language.getCurrent()
    },
    filter(key) {
      const obj = this.languages.find(item => item.value === this.currentLanguage)
      return obj ? obj[key] : ''
    },
    handleClick(event) {
      // 阻止事件冒泡，避免触发document的点击事件
      event.stopPropagation()
      this.showTranslateBox = !this.showTranslateBox
    },
    handleOutsideClick(event) {
      // 如果点击的不是翻译组件内的元素，则关闭翻译选项框
      if (this.showTranslateBox && !this.$el.contains(event.target)) {
        this.showTranslateBox = false
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.translate {
  min-width: 46px;
  padding: 2px 15px 2px 5px;
  border: 0 solid;
  display: flex;
  gap: 10px;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  border-radius: 100px;
  cursor: pointer;
  position: relative;
  z-index: 1999;
  margin-right: 20px;
  .img {
    width: 38px;
    height: 38px;
    border-radius: 50%;
    box-shadow: 0 0 13px 0 rgba(0, 0, 0, 0.25);
  }
  .icon-txt {
    background-color: #2e73f3;
    color: #fff;
    display: flex;
    width: 38px;
    height: 38px;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    box-shadow: 0 0 13px 0 rgba(0, 0, 0, 0.25);
    font-size: 12px;
  }
  .text {
    font-size: 12px;
    line-height: 14px;
    color: #333;
    position: relative;
    &.text-primary {
      font-size: 14px;
      color: #2e73f3;
      &::after {
        content: '';
        border-style: solid;
        border-width: 5px;
        border-color: #2e73f3 transparent transparent transparent;
        position: absolute;
        top: calc(50% + 2px);
        transform: translateY(-50%);
        left: calc(100% + 5px);
        transition: transform 0.3s;
      }
    }
    &.text-up {
      &::after {
        top: calc(50% - 2px);
        transform: translateY(-50%) rotate(180deg);
      }
    }
    &.text-warp {
      white-space: nowrap;
    }
  }
  .translate-box {
    position: absolute;
    top: calc(100% + 15px);
    display: flex;
    flex-direction: column;
    padding: 10px;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0px 1px 36px 0px rgba(0, 0, 0, 0.25);
    &::after {
      content: '';
      position: absolute;
      top: -20px;
      left: 50%;
      transform: translateX(-50%);
      border-style: solid;
      border-width: 10px;
      border-color: transparent transparent #fff transparent;
    }
  }
  .translate-item {
    cursor: pointer;
    padding: 0 10px;
    background: #fff;
    border-radius: 5px;
    transition: all 0.3s;
    line-height: 38px;
    &:hover,
    &.active {
      background-color: #ededed;
      color: #2e73f3;
    }
  }
  &.fixed {
    position: fixed;
    bottom: calc(40% + 20px);
    left: 51%;
    margin-left: 610px;
    flex-direction: column;
    padding: 5px 5px 15px;
    box-shadow: 0px 0px 13px 0px rgba(0, 0, 0, 0.15);
    margin-right: 0;
    .translate-box {
      top: unset;
      left: calc(100% + 15px);
      &::after {
        left: -20px;
        top: 50%;
        transform: translateY(-50%);
        border-color: transparent #fff transparent transparent;
      }
      @media screen and (max-width: 1366px) {
        left: auto;
        right: calc(100% + 15px);
        &::after {
          left: auto;
          right: -20px;
          border-color: transparent transparent transparent #fff;
        }
      }
    }
  }
}
</style>
