<!-- 签名组件 -->
<template>
  <div class="signContainer">
    <div class="signBox-tip">请您按照从左向右的顺序书写您的签名</div>
    <vue-esign ref="VueEsignRef" class="vue-esign" :width="width" :height="height" :lineWidth="lineWidth" :lineColor="lineColor" :bgColor="bgColor" :isCrop="isCrop" :isClearBgColor="isClearBgColor" :format="format" :quality="quality" />
    <div class="signBox-button">
      <div class="button-item primary" @click="sureHandler">
        <i class="el-icon-finished"></i>
        <span class="text">确定</span>
      </div>
      <div class="button-item success" @click="resetHandler">
        <i class="el-icon-refresh"></i>
        <span class="text">清空</span>
      </div>
      <div class="button-item" @click="backHandler">
        <i class="el-icon-back"></i>
        <span class="text">返回</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SignCanvas',
  components: {},
  props: {
    // 画布宽度，即导出图片的宽度
    width: {
      type: Number,
      default: () => {
        const dom = document.querySelector('#app')
        const width = dom && dom.offsetWidth
        return width ? width - 10 : 300 // 减去按钮区域的宽度
      }
    },
    // 画布高度，即导出图片的高度
    height: {
      type: Number,
      default: () => {
        const dom = document.querySelector('#app')
        return dom && dom.offsetHeight ? dom.offsetHeight - 80 : 800
      }
    },
    // 画笔粗细
    lineWidth: {
      type: Number,
      default: 6
    },
    // 画笔颜色
    lineColor: {
      type: String,
      default: '#000'
    },
    // 画布背景色，为空时画布背景透明，支持多种格式 '#ccc'，'#E5A1A1'，'rgb(229, 161, 161)'，'rgba(0,0,0,.6)'，'red'
    bgColor: {
      type: String,
      default: ''
    },
    // 是否裁剪，在画布设定尺寸基础上裁掉四周空白部分
    isCrop: {
      type: Boolean,
      default: false
    },
    // 清空画布时(reset)是否同时清空设置的背景色(bgColor)
    isClearBgColor: {
      type: Boolean,
      default: true
    },
    // 生成图片格式 image/jpeg(jpg格式下生成的图片透明背景会变黑色请慎用或指定背景色)、 image/webp
    format: {
      type: String,
      default: 'image/png'
    },
    // 生成图片质量；在指定图片格式为 image/jpeg 或 image/webp的情况下，可以从 0 到 1 的区间内选择图片的质量。如果超出取值范围，将会使用默认值 0.92。其他参数会被忽略。
    quality: {
      type: Number,
      default: 1
    },
    // 需要签名的姓名
    signName: {
      type: String,
      default: ''
    }
  },
  methods: {
    resetHandler() {
      this.$refs.VueEsignRef.reset() // 清空画布
    },
    // 返回
    backHandler() {
      this.$emit('clearHandler')
      this.resetHandler()
    },
    // prettier-ignore
    sureHandler() {
      this.$refs.VueEsignRef.generate().then(res => {
        this.$emit('sureHandler', res)
      }).catch(err => {
        this.$message.error('请签名')
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.signContainer {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #fff;
  position: relative;
  overflow: hidden;
  .vue-esign {
    z-index: 2;
    border: 1px solid #cccccc;
    margin-top: 5px;
  }
  .signBox {
    &-tip {
      width: 100%;
      height: 50px;
      line-height: 50px;
      font-size: 18px;
      color: #ec2454;
      transform: rotate(90deg);
      position: fixed;
      top: calc(50% - 60px);
      left: calc(50% - 25px);
      text-align: center;
    }
    &-button {
      display: flex;
      width: 100%;
      height: 80px;
      justify-content: space-around;
      align-items: center;
      .button-item {
        display: inline-flex;
        justify-content: center;
        width: 80px;
        height: calc(93vw / 3);
        flex-direction: column;
        transform: rotate(90deg);
        text-align: center;
        color: #ec2454;
        &.primary {
          color: #2e73f3;
        }
        &.success {
          color: #13ce66;
        }
        i {
          font-size: 30px;
        }
        span {
          font-size: 16px;
          line-height: 30px;
        }
      }
    }
  }
  @media screen and (min-width: 767px) {
    .signBox {
      &-tip {
        font-size: 35px;
      }
      &-button {
        height: 150px;
        .button-item {
          width: 150px;
          i {
            font-size: 40px;
          }
          span {
            font-size: 30px;
            line-height: 40px;
          }
        }
      }
    }
  }
}
</style>
