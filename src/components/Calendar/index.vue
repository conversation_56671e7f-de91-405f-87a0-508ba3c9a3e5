<template>
  <div class="calendar-container">
    <!-- 月份切换区 -->
    <div class="header">
      <div>
        <button @click="changeMonth(-1)">‹</button>
        <span style="margin: 0 15px;">{{ displayYear }}年{{ displayMonth }}月</span>
        <button @click="changeMonth(1)">›</button>
      </div>
      <div
        style="display: flex; flex-direction: column; align-items: flex-start; font-weight: 400; font-size: 16px; color: #666666;">
        <div>
          <span>今日共</span>
          <span style="margin: 0 10px; font-weight: 500; font-size: 20px; color: #F43F3F;">{{ days.find(item => item.isToday) && days.find(item => item.isToday).leaveInfo.length }}</span>
          <span>人请假</span>
        </div>
        <div>
          <span style="margin-right: 8px;">农历</span>
          <span>{{ lunarInfo.lunarMonthName }}{{ lunarInfo.lunarDayName }},{{ lunarInfo.GanZhiYear }}[{{
            lunarInfo.zodiac }}]年{{ lunarInfo.GanZhiMonth }}月{{ lunarInfo.GanZhiDay }}日</span>
        </div>
      </div>
      <div style="opacity: 0;">
        <el-select v-model="value" placeholder="请选择成员" size="small">
          <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </div>
    </div>

    <!-- 星期标题 -->
    <div class="week-header">
      <div v-for="day in weekDays" :key="day">{{ day }}</div>
    </div>

    <!-- 日期格子 -->
    <div class="days-grid">
      <div v-for="(day, index) in days" :key="index" :class="{
        'current-month': day.isCurrentMonth,
        'other-month': !day.isCurrentMonth,
        'today': day.isToday,
        'selected': day.isSelected
      }" @click="selectDay(day)">
        <div class="date-num">{{ day.date }}</div>
        <el-popover placement="bottom" width="900" trigger="click" popper-class="calendar_popper">
          <div>
            <div class="popper_header">
              <div class="popper_header_title">今日请假详细</div>
              <div class="popper_header_tips">
                <div class="popper_header_tips_item">
                  <img src="@/assets/images/day_g.png" alt="">
                  <span>1天内</span>
                </div>
                <div class="popper_header_tips_item">
                  <img src="@/assets/images/day_b.png" alt="">
                  <span>两天内</span>
                </div>
                <div class="popper_header_tips_item">
                  <img src="@/assets/images/day_y.png" alt="">
                  <span>三天以上五天以内</span>
                </div>
                <div class="popper_header_tips_item">
                  <img src="@/assets/images/day_p.png" alt="">
                  <span>五天以上</span>
                </div>
              </div>
            </div>
            <el-divider></el-divider>
            <div class="popper_concent">
              <div class="popper_concent_item" @click="handleTo" v-for="(item, i) in day.leaveInfo" :key="i" :class="(new Date(item.leaveEndTime).getTime() - new Date(item.leaveStartTime).getTime() > 86400000) ? 'pink' : ((new Date(item.leaveEndTime).getTime() - new Date(item.leaveStartTime).getTime() > 1728000) ? 'yellow' : ((new Date(item.leaveEndTime).getTime() - new Date(item.leaveStartTime).getTime() > 864000) ? 'blue' : 'green'))">
                <div class="popper_concent_item_left">{{ item.createBy }}</div>
                <div class="popper_concent_item_right">{{ item.content }}</div>
              </div>
            </div>
          </div>
          <div slot="reference">
            <div style="margin-top: 20px; display: flex; align-items: center; justify-content: center;">
              <span v-if="day.isCurrentMonth && day.leaveInfo && day.leaveInfo.length > 0">{{ day.isToday ? '今日请假' : '请假总数' }}</span>
              <span v-if="day.isCurrentMonth && day.leaveInfo && day.leaveInfo.length > 0" :class="day.isSelected ? 'white' : 'red'">{{ day.leaveInfo && day.leaveInfo.length }}</span>
            </div>
          </div>
        </el-popover>
      </div>
    </div>
  </div>
</template>

<script>
import LunarCalendar from 'lunar-calendar'
import { workHandoverTodayLeave } from '@/api/workHandover'
import { parseTime } from '@/utils/ruoyi'

export default {
  name: 'Calendar',
  props: {
    showLunar: {
      type: Boolean,
      default: false
    },
    startDay: {
      type: Number,
      default: 0 // 0-6 表示周日到周六
    },
    workHandoverList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      currentDate: new Date(),
      selectedDate: null,
      weekDays: ['日', '一', '二', '三', '四', '五', '六'],
      options: [
        { value: '选项1', label: '张三' },
        { value: '选项2', label: '李四' },
        { value: '选项3', label: '王五' },
      ],
      value: '',
      lunarInfo: {
        lunarMonthName: '',
        lunarDayName: '',
        GanZhiYear: '',
        zodiac: '',
        GanZhiMonth: '',
        GanZhiDay: ''
      }
    }
  },
  mounted() {
    // this.initCalendar()
  },
  watch: {
    currentDate(newVal) {
      // this.initCalendar()
    },
  },
  computed: {
    displayYear() {
      return this.currentDate.getFullYear()
    },
    displayMonth() {
      return this.currentDate.getMonth() + 1
    },
    days() {
      const year = this.displayYear
      const month = this.displayMonth
      const firstDay = new Date(year, month - 1, 1)
      const lastDay = new Date(year, month, 0)
      const daysInMonth = lastDay.getDate()
      const firstDayOfWeek = (firstDay.getDay() - this.startDay + 7) % 7
      const daysInPrevMonth = new Date(year, month - 1, 0).getDate()

      const today = new Date()
      const isToday = (d) => {
        return d === today.getDate() && month === (today.getMonth() + 1) && year === today.getFullYear()
      }

      let daysArray = []

      // 补上前个月末尾日期
      for (let i = firstDayOfWeek - 1; i >= 0; i--) {
        const date = daysInPrevMonth - i
        daysArray.push(this.createDayObj(
          date, false, false, false, year, month - 1
        ))
      }

      // 当前月日期
      for (let d = 1; d <= daysInMonth; d++) {
        const isSelected = this.selectedDate &&
          this.selectedDate.date === d &&
          this.selectedDate.isCurrentMonth
        daysArray.push(this.createDayObj(
          d, true, isToday(d), isSelected, year, month
        ))
      }

      // 补下个月开头日期
      const remaining = 42 - daysArray.length
      for (let d = 1; d <= remaining; d++) {
        daysArray.push(this.createDayObj(
          d, false, false, false, year, month + 1
        ))
      }

      daysArray.forEach((el, index) => {
        if (el.isCurrentMonth) {
           el.leaveInfo = this.workHandoverList[index - firstDayOfWeek]
        }
      })
      console.log(daysArray.find(item => item.isToday))
      return daysArray
    }
  },
  methods: {
    parseTime,
    // 获取请假信息
    async getLeaveInfo(date) {
      const res = await workHandoverTodayLeave({
        date: date,
      })
      console.log(res.data)
    },

    createDayObj(date, isCurrentMonth, isToday, isSelected, year, month) {
      const lunarInfo = this.showLunar ?
        LunarCalendar.solarToLunar(year, month, date) : null
      if (isToday && lunarInfo) {
        this.lunarInfo = lunarInfo
      }

      return {
        date,
        isCurrentMonth,
        isToday,
        isSelected,
        lunar: lunarInfo ? lunarInfo.lunarDayName : '',
        fullDate: `${year}-${month}-${date}`,
      }
    },
    changeMonth(step) {
      const newDate = new Date(this.currentDate)
      newDate.setMonth(newDate.getMonth() + step)
      this.currentDate = newDate
      this.$emit('month-change', {
        year: this.displayYear,
        month: this.displayMonth
      })
      this.days.forEach(el => {
        el.isSelected = false
      });
    },
    selectDay(day) {
      this.selectedDate = day
      this.$emit('date-select', day)
      if (!day.isCurrentMonth) {
        this.currentDate = new Date(
          day.fullDate.split('-')[0],
          day.fullDate.split('-')[1] - 1,
          day.date
        )
      }
    },
    handleTo() {
      this.$emit('toWorkHandover', true)
    }
  }
}
</script>

<style lang="scss" scoped>
.calendar-container {
  width: 100%;
  margin: 0 auto;
  font-family: Arial, sans-serif;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  font-size: 18px;
  font-weight: bold;
}

.header button {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
}

.week-header,
.days-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  text-align: center;
}

.week-header {
  font-weight: bold;
  padding: 10px 0;
  border-bottom: 1px solid #eee;
}

.days-grid>div {
  padding: 8px;
  height: 114px;
  border: 1px solid #f0f0f0;
  cursor: pointer;

  .red {
    margin-left: 10px;
    width: 30px;
    height: 20px;
    background: #FFB6B6;
    border-radius: 20px;
    color: #FFFFFF;
    text-align: center;
    line-height: 20px;
  }

  .white {
    margin-left: 10px;
    width: 30px;
    height: 20px;
    background: #FFFFFF;
    border-radius: 20px;
    color: #F43F3F;
    text-align: center;
    line-height: 20px;
  }
}

.current-month {
  background: white;
}

.other-month {
  color: #ccc;
  background: #f9f9f9;
}

.today {
  background-color: #e6f7ff;
  font-weight: bold;
}

.selected {
  background-color: #1890ff;
  color: white;
}

.date-num {
  font-size: 16px;
}

.lunar-date {
  font-size: 12px;
  margin-top: 2px;
}

.popper_header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .popper_header_title {
    font-weight: 500;
    font-size: 14px;
    color: #333333;
  }

  .popper_header_tips {
    display: flex;
    align-items: center;

    .popper_header_tips_item {
      display: flex;
      align-items: center;
      margin-left: 30px;

      img {
        width: 16px;
        height: 16px;
        margin-right: 9px;
      }

      span {
        font-size: 14px;
        color: #666666;
      }
    }
  }
}

.popper_concent {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: wrap;

  .popper_concent_item {
    display: flex;
    align-items: center;
    width: 200px;
    height: 50px;
    border-radius: 5px;
    padding: 10px 0;
    margin-bottom: 10px;
    cursor: pointer;
    margin-right: 20px;
    
    &:nth-child(4n) {
      margin-right: 0px;
    }


    .popper_concent_item_left {
      padding-left: 10px;
      padding-right: 15px;
      font-weight: 400;
      font-size: 14px;
      color: #666666;
      flex-shrink: 0;
      border-right: 1px solid #BEC5D7;
    }

    .popper_concent_item_right {
      font-weight: 400;
      font-size: 14px;
      color: #333333;
      padding: 0 10px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    &.blue {
      background: #F0F4FF;

      .popper_concent_item_left {
        border-left: 2px solid #2E73F3;
      }
    }

    &.yellow {
      background: #FFF6ED;

      .popper_concent_item_left {
        border-left: 2px solid #FFA90E;
      }
    }

    &.pink {
      background: #FFF1F4;

      .popper_concent_item_left {
        border-left: 2px solid #FF4A7A;
      }
    }

    &.green {
      background: #F0F8F3;

      .popper_concent_item_left {
        border-left: 2px solid #44BC71;
      }
    }
  }
}
</style>

<style>
.calendar_popper.el-popper[x-placement^=bottom],
.calendar_popper.el-popper[x-placement^=top] {
  margin-top: 55px;
}
</style>
