/* eslint-disable */
/* 浮尘/扬沙 */
import { checkColor } from './unit'

/* 填充方式-渐变填充/单色填充 */
const fillTemplate = (color) => {
  return color
    ? {
        ty: 'fl',
        c: {
          a: 0,
          k: color,
          ix: 3
        },
        o: {
          a: 0,
          k: 100,
          ix: 4
        },
        w: {
          a: 0,
          k: 20,
          ix: 5
        },
        lc: 2,
        lj: 1,
        ml: 10,
        bm: 0,
        hd: false
      }
    : {
        ty: 'gf',
        o: {
          a: 0,
          k: 100,
          ix: 10
        },
        r: 1,
        bm: 0,
        g: {
          p: 5,
          k: {
            a: 0,
            k: [
              0, 0.992, 0.902, 0.541, 0.225, 0.992, 0.902, 0.541, 0.45, 0.992,
              0.902, 0.541, 0.725, 0.992, 0.892, 0.492, 1, 0.992, 0.882, 0.443
            ],
            ix: 9
          }
        },
        s: {
          a: 0,
          k: [-7, -11],
          ix: 5
        },
        e: {
          a: 0,
          k: [5.002, 9.788],
          ix: 6
        },
        t: 1,
        hd: false
      }
}
/* 浮尘 */
export const floatDust = (color) => {
  const floatDustColor =
    color && checkColor(color)
      ? [color[0] / 255, color[1] / 255, color[2] / 255, 1]
      : null
  const floatDustFill = fillTemplate(floatDustColor)
  const floatDust_ks_p_k = [
    [424, 194, 0],
    [364, 194, 0],
    [392, 226, 0],
    [332, 226, 0],
    [360, 262, 0],
    [300, 262, 0],
    [328, 294, 0],
    [268, 294, 0],
    [296, 330, 0],
    [236, 330, 0],
    [264, 362, 0],
    [204, 362, 0]
  ]
  const floatDust_ks_o = [
    {
      a: 1,
      k: [
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 0,
          s: [61]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 15,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 35,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 55,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 75,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 95,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 115,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 135,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 155,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 175,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 195,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 215,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 235,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 255,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 275,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 295,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 315,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 335,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 355,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 359,
          s: [60.526]
        },
        {
          t: 374,
          s: [100]
        }
      ],
      ix: 11
    },
    {
      a: 1,
      k: [
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 0,
          s: [74]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 10,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 30,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 50,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 70,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 90,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 110,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 130,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 150,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 170,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 190,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 210,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 230,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 250,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 270,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 290,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 310,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 330,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 350,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 359,
          s: [73.684]
        },
        {
          t: 369,
          s: [100]
        }
      ],
      ix: 11
    },
    {
      a: 1,
      k: [
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 0,
          s: [87]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 5,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 25,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 45,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 65,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 85,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 105,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 125,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 145,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 165,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 185,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 205,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 225,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 245,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 265,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 285,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 305,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 325,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 345,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 359,
          s: [86.842]
        },
        {
          t: 364,
          s: [100]
        }
      ],
      ix: 11
    },
    {
      a: 1,
      k: [
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 0,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 20,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 40,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 60,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 80,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 100,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 120,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 140,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 160,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 180,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 200,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 220,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 240,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 260,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 280,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 300,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 320,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 340,
          s: [50]
        },
        {
          t: 359,
          s: [100]
        }
      ],
      ix: 11
    },
    {
      a: 1,
      k: [
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 0,
          s: [61]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 15,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 35,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 55,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 75,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 95,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 115,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 135,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 155,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 175,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 195,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 215,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 235,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 255,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 275,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 295,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 315,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 335,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 355,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 359,
          s: [60.526]
        },
        {
          t: 374,
          s: [100]
        }
      ],
      ix: 11
    },
    {
      a: 1,
      k: [
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 0,
          s: [74]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 10,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 30,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 50,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 70,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 90,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 110,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 130,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 150,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 170,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 190,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 210,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 230,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 250,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 270,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 290,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 310,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 330,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 350,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 359,
          s: [73.684]
        },
        {
          t: 369,
          s: [100]
        }
      ],
      ix: 11
    },
    {
      a: 1,
      k: [
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 0,
          s: [87]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 5,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 25,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 45,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 65,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 85,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 105,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 125,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 145,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 165,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 185,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 205,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 225,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 245,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 265,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 285,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 305,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 325,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 345,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 359,
          s: [86.842]
        },
        {
          t: 364,
          s: [100]
        }
      ],
      ix: 11
    },
    {
      a: 1,
      k: [
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 0,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 20,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 40,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 60,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 80,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 100,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 120,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 140,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 160,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 180,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 200,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 220,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 240,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 260,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 280,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 300,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 320,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 340,
          s: [50]
        },
        {
          t: 359,
          s: [100]
        }
      ],
      ix: 11
    },
    {
      a: 1,
      k: [
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 0,
          s: [61]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 15,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 35,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 55,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 75,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 95,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 115,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 135,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 155,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 175,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 195,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 215,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 235,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 255,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 275,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 295,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 315,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 335,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 355,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 359,
          s: [60.526]
        },
        {
          t: 374,
          s: [100]
        }
      ],
      ix: 11
    },
    {
      a: 1,
      k: [
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 0,
          s: [74]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 10,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 30,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 50,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 70,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 90,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 110,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 130,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 150,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 170,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 190,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 210,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 230,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 250,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 270,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 290,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 310,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 330,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 350,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 359,
          s: [73.684]
        },
        {
          t: 369,
          s: [100]
        }
      ],
      ix: 11
    },
    {
      a: 1,
      k: [
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 0,
          s: [87]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 5,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 25,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 45,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 65,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 85,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 105,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 125,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 145,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 165,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 185,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 205,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 225,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 245,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 265,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 285,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 305,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 325,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 345,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 359,
          s: [86.842]
        },
        {
          t: 364,
          s: [100]
        }
      ],
      ix: 11
    },
    {
      a: 1,
      k: [
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 0,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 20,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 40,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 60,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 80,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 100,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 120,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 140,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 160,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 180,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 200,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 220,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 240,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 260,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 280,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 300,
          s: [50]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 320,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 340,
          s: [50]
        },
        {
          t: 359,
          s: [100]
        }
      ],
      ix: 11
    }
  ]
  return floatDust_ks_p_k.map((item, index) => {
    return {
      ddd: 0,
      ind: index + 1,
      ty: 4,
      nm: 'dust',
      sr: 1,
      ks: {
        r: {
          a: 0,
          k: 0,
          ix: 10
        },
        p: {
          a: 0,
          k: item,
          ix: 2
        },
        a: {
          a: 0,
          k: [0, 0, 0],
          ix: 1
        },
        s: {
          a: 0,
          k: [100, 100, 100],
          ix: 6
        },
        o: floatDust_ks_o[index]
      },
      ao: 0,
      shapes: [
        {
          ind: 0,
          ty: 'sh',
          ix: 1,
          ks: {
            a: 0,
            k: {
              i: [
                [0, -6.627],
                [6.627, 0],
                [0, 6.627],
                [-6.627, 0]
              ],
              o: [
                [0, 6.627],
                [-6.627, 0],
                [0, -6.627],
                [6.627, 0]
              ],
              v: [
                [12, 0],
                [0, 12],
                [-12, 0],
                [0, -12]
              ],
              c: true
            },
            ix: 2
          },
          nm: 'Path 1',
          mn: 'ADBE Vector Shape - Group',
          hd: false
        },
        floatDustFill
      ],
      ip: 0,
      op: 360,
      st: 0,
      bm: 0
    }
  })
}
/* 扬沙 */
export const blowDust = (color) => {
  const blowDustColor =
    color && checkColor(color)
      ? [color[0] / 255, color[1] / 255, color[2] / 255, 1]
      : null
  const blowDustFill = fillTemplate(blowDustColor)
  const blowDust_ks_o = [
    {
      a: 1,
      k: [
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 5,
          s: [0]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 15,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 45,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 55,
          s: [0]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 125,
          s: [0]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 135,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 165,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 175,
          s: [0]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 245,
          s: [0]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 255,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 285,
          s: [100]
        },
        {
          t: 295,
          s: [0]
        }
      ],
      ix: 11
    },
    {
      a: 1,
      k: [
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 35,
          s: [0]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 45,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 75,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 85,
          s: [0]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 155,
          s: [0]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 165,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 195,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 205,
          s: [0]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 275,
          s: [0]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 285,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 315,
          s: [100]
        },
        {
          t: 325,
          s: [0]
        }
      ],
      ix: 11
    },
    {
      a: 1,
      k: [
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 65,
          s: [0]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 75,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 105,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 115,
          s: [0]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 185,
          s: [0]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 195,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 225,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 235,
          s: [0]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 305,
          s: [0]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 315,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 345,
          s: [100]
        },
        {
          t: 355,
          s: [0]
        }
      ],
      ix: 11
    },
    {
      a: 1,
      k: [
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 10,
          s: [0]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 20,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 50,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 60,
          s: [0]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 130,
          s: [0]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 140,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 170,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 180,
          s: [0]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 250,
          s: [0]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 260,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 290,
          s: [100]
        },
        {
          t: 300,
          s: [0]
        }
      ],
      ix: 11
    },
    {
      a: 1,
      k: [
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 40,
          s: [0]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 50,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 80,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 90,
          s: [0]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 160,
          s: [0]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 170,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 200,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 210,
          s: [0]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 280,
          s: [0]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 290,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 320,
          s: [100]
        },
        {
          t: 330,
          s: [0]
        }
      ],
      ix: 11
    },
    {
      a: 1,
      k: [
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 70,
          s: [0]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 80,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 110,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 120,
          s: [0]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 190,
          s: [0]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 200,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 230,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 240,
          s: [0]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 310,
          s: [0]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 320,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 350,
          s: [100]
        },
        {
          t: 359,
          s: [0]
        }
      ],
      ix: 11
    },
    {
      a: 1,
      k: [
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 0,
          s: [0]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 10,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 40,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 50,
          s: [0]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 120,
          s: [0]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 130,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 160,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 170,
          s: [0]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 240,
          s: [0]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 250,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 280,
          s: [100]
        },
        {
          t: 290,
          s: [0]
        }
      ],
      ix: 11
    },
    {
      a: 1,
      k: [
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 30,
          s: [0]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 40,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 70,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 80,
          s: [0]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 150,
          s: [0]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 160,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 190,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 200,
          s: [0]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 270,
          s: [0]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 280,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 310,
          s: [100]
        },
        {
          t: 320,
          s: [0]
        }
      ],
      ix: 11
    },
    {
      a: 1,
      k: [
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 60,
          s: [0]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 70,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 100,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 110,
          s: [0]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 180,
          s: [0]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 190,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 220,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 230,
          s: [0]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 300,
          s: [0]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 310,
          s: [100]
        },
        {
          i: {
            x: [0.833],
            y: [0.833]
          },
          o: {
            x: [0.167],
            y: [0.167]
          },
          t: 340,
          s: [100]
        },
        {
          t: 350,
          s: [0]
        }
      ],
      ix: 11
    }
  ]
  const blowDust_ks_p = [
    {
      a: 1,
      k: [
        {
          i: {
            x: 0.833,
            y: 0.833
          },
          o: {
            x: 0.167,
            y: 0.167
          },
          t: 5,
          s: [316, 316, 0],
          to: [8, 0, 0],
          ti: [0, 0, 0]
        },
        {
          i: {
            x: 0.833,
            y: 0.833
          },
          o: {
            x: 0.167,
            y: 0.167
          },
          t: 55,
          s: [364, 316, 0],
          to: [0, 0, 0],
          ti: [0, 0, 0]
        },
        {
          i: {
            x: 0.833,
            y: 0.833
          },
          o: {
            x: 0.167,
            y: 0.167
          },
          t: 125,
          s: [316, 316, 0],
          to: [0, 0, 0],
          ti: [0, 0, 0]
        },
        {
          i: {
            x: 0.833,
            y: 0.833
          },
          o: {
            x: 0.167,
            y: 0.167
          },
          t: 175,
          s: [364, 316, 0],
          to: [0, 0, 0],
          ti: [0, 0, 0]
        },
        {
          i: {
            x: 0.833,
            y: 0.833
          },
          o: {
            x: 0.167,
            y: 0.167
          },
          t: 245,
          s: [316, 316, 0],
          to: [0, 0, 0],
          ti: [-8, 0, 0]
        },
        {
          t: 295,
          s: [364, 316, 0]
        }
      ],
      ix: 2
    },
    {
      a: 1,
      k: [
        {
          i: {
            x: 0.833,
            y: 0.833
          },
          o: {
            x: 0.167,
            y: 0.167
          },
          t: 35,
          s: [232, 316, 0],
          to: [8, 0, 0],
          ti: [0, 0, 0]
        },
        {
          i: {
            x: 0.833,
            y: 0.833
          },
          o: {
            x: 0.167,
            y: 0.167
          },
          t: 85,
          s: [280, 316, 0],
          to: [0, 0, 0],
          ti: [0, 0, 0]
        },
        {
          i: {
            x: 0.833,
            y: 0.833
          },
          o: {
            x: 0.167,
            y: 0.167
          },
          t: 155,
          s: [232, 316, 0],
          to: [0, 0, 0],
          ti: [0, 0, 0]
        },
        {
          i: {
            x: 0.833,
            y: 0.833
          },
          o: {
            x: 0.167,
            y: 0.167
          },
          t: 205,
          s: [280, 316, 0],
          to: [0, 0, 0],
          ti: [0, 0, 0]
        },
        {
          i: {
            x: 0.833,
            y: 0.833
          },
          o: {
            x: 0.167,
            y: 0.167
          },
          t: 275,
          s: [232, 316, 0],
          to: [0, 0, 0],
          ti: [-8, 0, 0]
        },
        {
          t: 325,
          s: [280, 316, 0]
        }
      ],
      ix: 2
    },
    {
      a: 1,
      k: [
        {
          i: {
            x: 0.833,
            y: 0.833
          },
          o: {
            x: 0.167,
            y: 0.167
          },
          t: 65,
          s: [158, 316, 0],
          to: [8, 0, 0],
          ti: [0, 0, 0]
        },
        {
          i: {
            x: 0.833,
            y: 0.833
          },
          o: {
            x: 0.167,
            y: 0.167
          },
          t: 115,
          s: [206, 316, 0],
          to: [0, 0, 0],
          ti: [0, 0, 0]
        },
        {
          i: {
            x: 0.833,
            y: 0.833
          },
          o: {
            x: 0.167,
            y: 0.167
          },
          t: 185,
          s: [158, 316, 0],
          to: [0, 0, 0],
          ti: [0, 0, 0]
        },
        {
          i: {
            x: 0.833,
            y: 0.833
          },
          o: {
            x: 0.167,
            y: 0.167
          },
          t: 235,
          s: [206, 316, 0],
          to: [0, 0, 0],
          ti: [0, 0, 0]
        },
        {
          i: {
            x: 0.833,
            y: 0.833
          },
          o: {
            x: 0.167,
            y: 0.167
          },
          t: 305,
          s: [158, 316, 0],
          to: [0, 0, 0],
          ti: [-8, 0, 0]
        },
        {
          t: 355,
          s: [206, 316, 0]
        }
      ],
      ix: 2
    },
    {
      a: 1,
      k: [
        {
          i: {
            x: 0.833,
            y: 0.833
          },
          o: {
            x: 0.167,
            y: 0.167
          },
          t: 10,
          s: [364, 256, 0],
          to: [8, 0, 0],
          ti: [0, 0, 0]
        },
        {
          i: {
            x: 0.833,
            y: 0.833
          },
          o: {
            x: 0.167,
            y: 0.167
          },
          t: 60,
          s: [412, 256, 0],
          to: [0, 0, 0],
          ti: [0, 0, 0]
        },
        {
          i: {
            x: 0.833,
            y: 0.833
          },
          o: {
            x: 0.167,
            y: 0.167
          },
          t: 130,
          s: [364, 256, 0],
          to: [0, 0, 0],
          ti: [0, 0, 0]
        },
        {
          i: {
            x: 0.833,
            y: 0.833
          },
          o: {
            x: 0.167,
            y: 0.167
          },
          t: 180,
          s: [412, 256, 0],
          to: [0, 0, 0],
          ti: [0, 0, 0]
        },
        {
          i: {
            x: 0.833,
            y: 0.833
          },
          o: {
            x: 0.167,
            y: 0.167
          },
          t: 250,
          s: [364, 256, 0],
          to: [0, 0, 0],
          ti: [-8, 0, 0]
        },
        {
          t: 300,
          s: [412, 256, 0]
        }
      ],
      ix: 2
    },
    {
      a: 1,
      k: [
        {
          i: {
            x: 0.833,
            y: 0.833
          },
          o: {
            x: 0.167,
            y: 0.167
          },
          t: 40,
          s: [282, 256, 0],
          to: [8, 0, 0],
          ti: [0, 0, 0]
        },
        {
          i: {
            x: 0.833,
            y: 0.833
          },
          o: {
            x: 0.167,
            y: 0.167
          },
          t: 90,
          s: [330, 256, 0],
          to: [0, 0, 0],
          ti: [0, 0, 0]
        },
        {
          i: {
            x: 0.833,
            y: 0.833
          },
          o: {
            x: 0.167,
            y: 0.167
          },
          t: 160,
          s: [282, 256, 0],
          to: [0, 0, 0],
          ti: [0, 0, 0]
        },
        {
          i: {
            x: 0.833,
            y: 0.833
          },
          o: {
            x: 0.167,
            y: 0.167
          },
          t: 210,
          s: [330, 256, 0],
          to: [0, 0, 0],
          ti: [0, 0, 0]
        },
        {
          i: {
            x: 0.833,
            y: 0.833
          },
          o: {
            x: 0.167,
            y: 0.167
          },
          t: 280,
          s: [282, 256, 0],
          to: [0, 0, 0],
          ti: [-8, 0, 0]
        },
        {
          t: 330,
          s: [330, 256, 0]
        }
      ],
      ix: 2
    },
    {
      a: 1,
      k: [
        {
          i: {
            x: 0.833,
            y: 0.833
          },
          o: {
            x: 0.167,
            y: 0.167
          },
          t: 70,
          s: [198, 256, 0],
          to: [8, 0, 0],
          ti: [0, 0, 0]
        },
        {
          i: {
            x: 0.833,
            y: 0.833
          },
          o: {
            x: 0.167,
            y: 0.167
          },
          t: 120,
          s: [246, 256, 0],
          to: [0, 0, 0],
          ti: [0, 0, 0]
        },
        {
          i: {
            x: 0.833,
            y: 0.833
          },
          o: {
            x: 0.167,
            y: 0.167
          },
          t: 190,
          s: [198, 256, 0],
          to: [0, 0, 0],
          ti: [0, 0, 0]
        },
        {
          i: {
            x: 0.833,
            y: 0.833
          },
          o: {
            x: 0.167,
            y: 0.167
          },
          t: 240,
          s: [246, 256, 0],
          to: [0, 0, 0],
          ti: [0, 0, 0]
        },
        {
          i: {
            x: 0.833,
            y: 0.833
          },
          o: {
            x: 0.167,
            y: 0.167
          },
          t: 310,
          s: [198, 256, 0],
          to: [0, 0, 0],
          ti: [-8, 0, 0]
        },
        {
          t: 359,
          s: [246, 256, 0]
        }
      ],
      ix: 2
    },
    {
      a: 1,
      k: [
        {
          i: {
            x: 0.833,
            y: 0.833
          },
          o: {
            x: 0.167,
            y: 0.167
          },
          t: 0,
          s: [240, 196, 0],
          to: [8, 0, 0],
          ti: [0, 0, 0]
        },
        {
          i: {
            x: 0.833,
            y: 0.833
          },
          o: {
            x: 0.167,
            y: 0.167
          },
          t: 50,
          s: [288, 196, 0],
          to: [0, 0, 0],
          ti: [0, 0, 0]
        },
        {
          i: {
            x: 0.833,
            y: 0.833
          },
          o: {
            x: 0.167,
            y: 0.167
          },
          t: 120,
          s: [240, 196, 0],
          to: [0, 0, 0],
          ti: [0, 0, 0]
        },
        {
          i: {
            x: 0.833,
            y: 0.833
          },
          o: {
            x: 0.167,
            y: 0.167
          },
          t: 170,
          s: [288, 196, 0],
          to: [0, 0, 0],
          ti: [0, 0, 0]
        },
        {
          i: {
            x: 0.833,
            y: 0.833
          },
          o: {
            x: 0.167,
            y: 0.167
          },
          t: 240,
          s: [240, 196, 0],
          to: [0, 0, 0],
          ti: [-8, 0, 0]
        },
        {
          t: 290,
          s: [288, 196, 0]
        }
      ],
      ix: 2
    },
    {
      a: 1,
      k: [
        {
          i: {
            x: 0.833,
            y: 0.833
          },
          o: {
            x: 0.167,
            y: 0.167
          },
          t: 30,
          s: [156, 196, 0],
          to: [8, 0, 0],
          ti: [0, 0, 0]
        },
        {
          i: {
            x: 0.833,
            y: 0.833
          },
          o: {
            x: 0.167,
            y: 0.167
          },
          t: 80,
          s: [204, 196, 0],
          to: [0, 0, 0],
          ti: [0, 0, 0]
        },
        {
          i: {
            x: 0.833,
            y: 0.833
          },
          o: {
            x: 0.167,
            y: 0.167
          },
          t: 150,
          s: [156, 196, 0],
          to: [0, 0, 0],
          ti: [0, 0, 0]
        },
        {
          i: {
            x: 0.833,
            y: 0.833
          },
          o: {
            x: 0.167,
            y: 0.167
          },
          t: 200,
          s: [204, 196, 0],
          to: [0, 0, 0],
          ti: [0, 0, 0]
        },
        {
          i: {
            x: 0.833,
            y: 0.833
          },
          o: {
            x: 0.167,
            y: 0.167
          },
          t: 270,
          s: [156, 196, 0],
          to: [0, 0, 0],
          ti: [-8, 0, 0]
        },
        {
          t: 320,
          s: [204, 196, 0]
        }
      ],
      ix: 2
    },
    {
      a: 1,
      k: [
        {
          i: {
            x: 0.833,
            y: 0.833
          },
          o: {
            x: 0.167,
            y: 0.167
          },
          t: 60,
          s: [72, 196, 0],
          to: [8, 0, 0],
          ti: [0, 0, 0]
        },
        {
          i: {
            x: 0.833,
            y: 0.833
          },
          o: {
            x: 0.167,
            y: 0.167
          },
          t: 110,
          s: [120, 196, 0],
          to: [0, 0, 0],
          ti: [0, 0, 0]
        },
        {
          i: {
            x: 0.833,
            y: 0.833
          },
          o: {
            x: 0.167,
            y: 0.167
          },
          t: 180,
          s: [72, 196, 0],
          to: [0, 0, 0],
          ti: [0, 0, 0]
        },
        {
          i: {
            x: 0.833,
            y: 0.833
          },
          o: {
            x: 0.167,
            y: 0.167
          },
          t: 230,
          s: [120, 196, 0],
          to: [0, 0, 0],
          ti: [0, 0, 0]
        },
        {
          i: {
            x: 0.833,
            y: 0.833
          },
          o: {
            x: 0.167,
            y: 0.167
          },
          t: 300,
          s: [72, 196, 0],
          to: [0, 0, 0],
          ti: [-8, 0, 0]
        },
        {
          t: 350,
          s: [120, 196, 0]
        }
      ],
      ix: 2
    }
  ]
  return blowDust_ks_o.map((item, index) => {
    return {
      ddd: 0,
      ind: index + 1,
      ty: 4,
      nm: 'dust',
      sr: 1,
      ks: {
        r: {
          a: 0,
          k: 0,
          ix: 10
        },
        a: {
          a: 0,
          k: [0, 0, 0],
          ix: 1
        },
        s: {
          a: 0,
          k: [100, 100, 100],
          ix: 6
        },
        o: item,
        p: blowDust_ks_p[index]
      },
      ao: 0,
      shapes: [
        {
          ind: 0,
          ty: 'sh',
          ix: 1,
          ks: {
            a: 0,
            k: {
              i: [
                [0, -6.627],
                [6.627, 0],
                [0, 6.627],
                [-6.627, 0]
              ],
              o: [
                [0, 6.627],
                [-6.627, 0],
                [0, -6.627],
                [6.627, 0]
              ],
              v: [
                [12, 0],
                [0, 12],
                [-12, 0],
                [0, -12]
              ],
              c: true
            },
            ix: 2
          },
          nm: 'Path 1',
          mn: 'ADBE Vector Shape - Group',
          hd: false
        },
        blowDustFill
      ],
      ip: 0,
      op: 360,
      st: 0,
      bm: 0
    }
  })
}
