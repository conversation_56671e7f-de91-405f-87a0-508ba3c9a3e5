/* eslint-disable */
/* N/A */
import { checkColor } from './unit'

export const na = (color) => {
  const naColor = color && checkColor(color) ? [color[0] / 255, color[1] / 255, color[2] / 255, 1] : [0.215686276555, 0.258823543787, 0.317647069693, 1]
  return {
    ddd: 0,
    ind: 1,
    ty: 4,
    nm: 'N/A',
    sr: 1,
    ks: {
      o: {
        a: 0,
        k: 100,
        ix: 11
      },
      r: {
        a: 0,
        k: 0,
        ix: 10
      },
      p: {
        a: 0,
        k: [
          256,
          266,
          0
        ],
        ix: 2,
        l: 2
      },
      a: {
        a: 0,
        k: [
          0,
          0,
          0
        ],
        ix: 1,
        l: 2
      },
      s: {
        a: 0,
        k: [
          100,
          100,
          100
        ],
        ix: 6,
        l: 2
      }
    },
    ao: 0,
    shapes: [
      {
        ind: 0,
        ty: 'sh',
        ix: 1,
        ks: {
          a: 0,
          k: {
            i: [
              [
                0,
                0
              ],
              [
                0,
                0
              ],
              [
                0,
                0
              ],
              [
                0,
                0
              ],
              [
                0,
                0
              ],
              [
                0,
                0
              ],
              [
                0,
                0
              ],
              [
                0,
                0
              ],
              [
                0,
                0
              ],
              [
                0,
                0
              ]
            ],
            o: [
              [
                0,
                0
              ],
              [
                0,
                0
              ],
              [
                0,
                0
              ],
              [
                0,
                0
              ],
              [
                0,
                0
              ],
              [
                0,
                0
              ],
              [
                0,
                0
              ],
              [
                0,
                0
              ],
              [
                0,
                0
              ],
              [
                0,
                0
              ]
            ],
            v: [
              [
                -81.447,
                8.744
              ],
              [
                -81.447,
                -53.646
              ],
              [
                -53.719,
                -53.646
              ],
              [
                -53.719,
                53.646
              ],
              [
                -81.9,
                53.646
              ],
              [
                -119.119,
                -8.437
              ],
              [
                -119.119,
                53.646
              ],
              [
                -147,
                53.646
              ],
              [
                -147,
                -53.646
              ],
              [
                -119.119,
                -53.646
              ]
            ],
            c: true
          },
          ix: 2
        },
        hd: false
      },
      {
        ty: 'fl',
        c: {
          a: 0,
          k: naColor,
          ix: 4
        },
        o: {
          a: 0,
          k: 100,
          ix: 5
        },
        r: 1,
        bm: 0,
        hd: false
      },
      {
        ind: 2,
        ty: 'sh',
        ix: 3,
        ks: {
          a: 0,
          k: {
            i: [
              [
                0,
                0
              ],
              [
                0,
                0
              ],
              [
                0,
                0
              ],
              [
                0,
                0
              ]
            ],
            o: [
              [
                0,
                0
              ],
              [
                0,
                0
              ],
              [
                0,
                0
              ],
              [
                0,
                0
              ]
            ],
            v: [
              [
                -12.582,
                53.646
              ],
              [
                -39.702,
                53.646
              ],
              [
                4.899,
                -53.646
              ],
              [
                32.026,
                -53.646
              ]
            ],
            c: true
          },
          ix: 2
        },
        hd: false
      },
      {
        ty: 'fl',
        c: {
          a: 0,
          k: naColor,
          ix: 4
        },
        o: {
          a: 0,
          k: 100,
          ix: 5
        },
        r: 1,
        bm: 0,
        hd: false
      },
      {
        ind: 4,
        ty: 'sh',
        ix: 5,
        ks: {
          a: 0,
          k: {
            i: [
              [
                0,
                0
              ],
              [
                0,
                0
              ],
              [
                0,
                0
              ],
              [
                0,
                0
              ],
              [
                0,
                0
              ]
            ],
            o: [
              [
                0,
                0
              ],
              [
                0,
                0
              ],
              [
                0,
                0
              ],
              [
                0,
                0
              ],
              [
                0,
                0
              ]
            ],
            v: [
              [
                86.726,
                0
              ],
              [
                81.9,
                14.618
              ],
              [
                104.655,
                14.618
              ],
              [
                99.835,
                0
              ],
              [
                93.201,
                -22.454
              ]
            ],
            c: true
          },
          ix: 2
        },
        hd: false
      },
      {
        ind: 5,
        ty: 'sh',
        ix: 6,
        ks: {
          a: 0,
          k: {
            i: [
              [
                0,
                0
              ],
              [
                0,
                0
              ],
              [
                0,
                0
              ],
              [
                0,
                0
              ],
              [
                0,
                0
              ],
              [
                0,
                0
              ],
              [
                0,
                0
              ],
              [
                0,
                0
              ]
            ],
            o: [
              [
                0,
                0
              ],
              [
                0,
                0
              ],
              [
                0,
                0
              ],
              [
                0,
                0
              ],
              [
                0,
                0
              ],
              [
                0,
                0
              ],
              [
                0,
                0
              ],
              [
                0,
                0
              ]
            ],
            v: [
              [
                147,
                53.646
              ],
              [
                117.311,
                53.646
              ],
              [
                111.283,
                35.263
              ],
              [
                75.119,
                35.263
              ],
              [
                69.091,
                53.646
              ],
              [
                39.555,
                53.646
              ],
              [
                78.736,
                -53.646
              ],
              [
                108.426,
                -53.646
              ]
            ],
            c: true
          },
          ix: 2
        },
        hd: false
      },
      {
        ty: 'fl',
        c: {
          a: 0,
          k: naColor,
          ix: 4
        },
        o: {
          a: 0,
          k: 100,
          ix: 5
        },
        r: 1,
        bm: 0,
        hd: false
      }
    ],
    ip: 0,
    op: 360,
    st: 0,
    ct: 1,
    bm: 0
  }
}
