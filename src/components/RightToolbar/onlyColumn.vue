<template>
  <div class="top-right-btn" :style="style">
    <el-row>
      <el-tooltip class="item" effect="dark" content="显隐列" placement="top" v-if="!!columns.length">
        <el-button size="mini" circle icon="el-icon-menu" :disabled="false" @click="showColumn()" />
      </el-tooltip>
    </el-row>
    <el-dialog v-dialogDragBox :title="title" :visible.sync="open" append-to-body width="540px">
      <el-transfer :titles="['显示', '隐藏']" v-model="value" :data="columns" @change="dataChange"></el-transfer>
    </el-dialog>
  </div>
</template>
<script>
export default {
  name: 'RightToolbar',
  data() {
    return {
      // 显隐数据
      value: [],
      // 弹出层标题
      title: '显示/隐藏',
      // 是否显示弹出层
      open: false
    }
  },
  props: {
    columns: {
      type: Array,
      default: () => []
    },
    isSetitem: {
      type: <PERSON><PERSON><PERSON>,
      default: false
    }
  },
  computed: {
    style() {
      const ret = {}
      return ret
    }
  },
  created() {
    // 显隐列初始默认隐藏列
    for (let item in this.columns) {
      if (this.columns[item].visible === false) {
        this.value.push(parseInt(item))
      }
    }
  },
  methods: {
    // 右侧列表元素变化
    dataChange(data) {
      for (let item in this.columns) {
        const key = this.columns[item].key
        this.columns[item].visible = !data.includes(key)
      }
      if (this.isSetitem) this.$emit('updateColumns', this.columns)
    },
    // 打开显隐列dialog
    showColumn() {
      this.open = true
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-transfer__button {
  border-radius: 50%;
  padding: 12px;
  display: block;
  margin-left: 0px;
}
::v-deep .el-transfer__button:first-child {
  margin-bottom: 10px;
}
::v-deep .el-transfer__button:nth-child(2) {
  margin-left: 0;
}
</style>
