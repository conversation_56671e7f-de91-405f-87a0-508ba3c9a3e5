<template>
  <div class="emoji-container">
    <el-tooltip effect="dark" content="表情">
      <div class="chatfont chat-biaoqing emoji" :class="{ active: show }" @click.stop="handleClickEmojiButton"></div>
    </el-tooltip>
    <el-collapse-transition>
      <div class="emoji-box" :class="{ locationTop: locationTop }" v-show="show">
        <div class="emoji-list">
          <div class="emoji-item" v-for="(item, index) in list" :key="index" @click="handleClickEmoji(item)">
            <img class="emoji-img" :src="formatImg(item)" :alt="item" :title="formatTitle(item)" />
          </div>
        </div>
      </div>
    </el-collapse-transition>
  </div>
</template>
<script>
import emojiList from './index'

export default {
  props: {
    locationTop: {
      type: Boolean,
      default: false
    }
  },
  name: 'Emoji',
  data() {
    return {
      show: false,
      list: emojiList
    }
  },
  mounted() {
    document.addEventListener('click', this.handleClickOutside, true)
  },
  beforeDestroy() {
    document.removeEventListener('click', this.handleClickOutside, true)
  },
  methods: {
    formatImg(item) {
      return require(`@/assets/emoji/${item}.png`)
    },
    formatTitle(item) {
      return item.replace(/[\[\]]/g, '')
    },
    // 点击表情
    handleClickEmoji(item) {
      this.$emit('handleClickEmoji', item)
    },
    // 点击外部关闭表情框
    handleClickOutside(event) {
      if (!this.$el.contains(event.target)) {
        this.show = false
      }
    },
    handleClickEmojiButton() {
      this.show = !this.show
      this.$emit('callBack')
    }
  }
}
</script>

<style scoped lang="scss">
@import '~@/assets/chat/iconfont.css';
.emoji-container {
  position: relative;
}
.emoji {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  font-size: 18px;
  cursor: pointer;
  color: #9fa3b2;
  transition: all 0.3s;
  &:hover,
  &.active {
    color: #2e73f3;
  }
}
.emoji-box {
  position: absolute;
  top: 30px;
  background-color: #f8f8f8;
  box-shadow: 0 1px 18px 0 rgba(0, 0, 0, 0.2);
  border-radius: 15px;
  padding: 15px 5px 15px;
  z-index: 99999;
  &.locationTop {
    top: unset;
    bottom: 30px;
  }
}
.emoji-list {
  display: flex;
  flex-wrap: wrap;
  width: 410px;
  max-height: 280px;
  overflow-y: auto;
  &::-webkit-scrollbar {
    width: 5px;
  }
  &::-webkit-scrollbar-track {
    background-color: #f1f1f1;
    opacity: 0.15;
  }
  &::-webkit-scrollbar-thumb {
    background-color: #888888;
    border-radius: 5px;
  }
  &::-webkit-scrollbar-thumb:hover {
    background: #555555;
  }
  .emoji-item {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    margin: 10px;
    cursor: pointer;
    img {
      width: 100%;
      height: 100%;
      transition: all 0.3s;
      &:hover {
        transform: scale(1.4);
      }
    }
  }
}
</style>
