<template>
  <div style="display: inline-block">
    <el-form-item :label="label" v-if="showLabel">
      <div class="custom-search-select" :class="{ medium: size == 'medium', small: size == 'small', mini: size == 'mini' }">
        <el-select v-model="value" filterable remote :placeholder="placeholder" :remote-method="remoteMethod" :loading="dataLoading" @change="handleQuery" style="width: 100%">
          <el-option v-for="item in dataOptions" :key="item.FNumber" :label="item.FName" :value="item.FNumber">
            <span style="float: left">{{ item.FName }}</span>
            <span style="float: right; color: #8492a6; font-size: 13px">{{ item.FUseOrg }}</span>
          </el-option>
        </el-select>
        <el-button class="el-icon-search" @click="openStaff" />
      </div>
    </el-form-item>
    <div class="custom-search-select" :class="{ medium: size == 'medium', small: size == 'small', mini: size == 'mini' }" v-if="!showLabel">
      <el-select v-model="value" filterable remote :placeholder="placeholder" :remote-method="remoteMethod" :loading="dataLoading" @change="handleQuery" style="width: 100%">
        <el-option v-for="item in dataOptions" :key="item.FNumber" :label="item.FName" :value="item.FNumber">
          <span style="float: left">{{ item.FName }}</span>
          <span style="float: right; color: #8492a6; font-size: 13px">{{ item.FUseOrg }}</span>
        </el-option>
      </el-select>
      <el-button class="el-icon-search" @click="openStaff" />
    </div>
    <!-- 员工列表 -->
    <el-dialog v-dialogDragBox title="员工列表" :visible.sync="staffOpen" width="1150px" class="custom-dialog custom-staff-dialog" append-to-body>
      <staff ref="staff" :isPopup="true" :useOrg="useOrg" @selectStaff="selectStaff"></staff>
      <div slot="footer" class="dialog-footer">
        <el-button class="custom-dialog-btn" @click="staffOpen = false">取消</el-button>
        <el-button class="custom-dialog-btn primary" :class="{ disabled: !selectedStaff.FNumber }" :disabled="!selectedStaff.FNumber" @click="handleSelectStaff">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import staff from '@/views/kingdee/staff'
import { getStaffList } from '@/api/kingdee'

export default {
  name: 'StaffSearchSelect',
  components: { staff },
  props: {
    placeholder: {
      type: String,
      default: '请输入员工姓名'
    },
    label: {
      type: String,
      default: '员工姓名'
    },
    keyword: {
      type: String,
      default: ''
    },
    showLabel: {
      type: Boolean,
      default: true
    },
    isBack: {
      type: Boolean,
      default: false
    },
    useOrg: {
      type: String,
      default: ''
    },
    size: {
      type: String,
      default: ''
    },
    options: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      dataLoading: false,
      dataOptions: [],
      staffOpen: false,
      selectedStaff: {}
    }
  },
  computed: {
    value: {
      get() {
        return this.keyword
      },
      set(val) {
        this.$emit('update:keyword', val)
      }
    }
  },
  watch: {
    options: {
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          const hasValidData = newVal.some(item => item && (item.FNumber || item.FName))
          if (hasValidData) {
            this.dataOptions = [...newVal]
          }
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    // 打开员工列表
    openStaff() {
      this.staffOpen = true
      const staff = this.dataOptions.find(item => item.FName === this.value)
      this.$nextTick(() => {
        this.$refs.staff.handleResetQuery(staff)
      })
    },
    // 选择员工
    selectStaff(staff) {
      this.selectedStaff = staff
    },
    // 确定选择员工
    handleSelectStaff() {
      this.staffOpen = false
      this.dataOptions = [this.selectedStaff]
      this.value = this.selectedStaff.FNumber
      if (this.isBack) this.$emit('callBack', this.selectedStaff)
      else this.$emit('callBack')
      this.selectedStaff = {}
    },
    // 搜索
    handleQuery(value) {
      const data = this.dataOptions.find(item => item.FNumber === value)
      if (this.isBack) this.$emit('callBack', data)
      else this.$emit('callBack')
    },
    // 远程搜索
    remoteMethod(query) {
      getStaffList({ name: query, limit: 30 }).then(res => {
        const data = res.data?.data || []
        this.dataOptions = data.reduce((unique, item) => {
          const exists = unique.find(u => u.FNumber === item.FNumber)
          if (!exists) unique.push(item)
          return unique
        }, [])
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/custom-new.scss';
.custom-dialog.custom-staff-dialog ::v-deep {
  .el-dialog__body {
    padding: 0;
  }
}
.custom-search-select {
  display: flex;
  align-items: center;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
  &.medium {
    height: 36px;
  }
  &.small {
    height: 32px;
    .el-icon-search {
      padding-left: 10px !important;
      padding-right: 10px !important;
    }
  }
  &.mini {
    height: 28px;
    .el-icon-search {
      padding-left: 5px !important;
      padding-right: 5px !important;
    }
  }
  ::v-deep .el-input__inner {
    border: none;
  }
  ::v-deep .el-button {
    border: none;
    &:hover {
      background-color: transparent;
    }
  }
  .el-icon-search {
    color: #c0c4cc;
  }
}
</style>
