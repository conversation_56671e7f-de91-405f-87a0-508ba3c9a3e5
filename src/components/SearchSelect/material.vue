<template>
  <div style="display: inline-block">
    <el-form-item :label="label" v-if="showLabel">
      <div class="custom-search-select" :class="{ medium: size == 'medium', small: size == 'small', mini: size == 'mini' }">
        <el-select ref="materialSelect" :key="selectKey" v-model="value" placeholder="请选择" :loading="dataLoading" @change="handleChange" style="width: 100%" filterable remote :remote-method="remoteMethod" :size="size" :disabled="disabled">
          <el-option v-for="item in dataOptions" :key="item.Number" :label="item.Name" :value="item.Number">
            <span style="float: left">{{ item.Name }}</span>
            <span style="float: right; color: #999; font-size: 12px">{{ item.Specification }}</span>
          </el-option>
        </el-select>
        <el-button class="el-icon-search" :size="size" @click="openMaterial" :disabled="disabled" />
      </div>
    </el-form-item>
    <div class="custom-search-select" :class="{ medium: size == 'medium', small: size == 'small', mini: size == 'mini' }" v-if="!showLabel">
      <el-select ref="materialSelect" :key="selectKey" v-model="value" placeholder="请选择" :loading="dataLoading" @change="handleChange($event)" style="width: 100%" filterable remote :remote-method="remoteMethod" :size="size" :disabled="disabled">
        <el-option v-for="item in dataOptions" :key="item.Number" :label="item.Name" :value="item.Number">
          <span style="float: left">{{ item.Name }}</span>
          <span style="float: right; color: #999; font-size: 12px">{{ item.Specification }}</span>
        </el-option>
      </el-select>
      <el-button class="el-icon-search" :size="size" @click="openMaterial" :disabled="disabled" />
    </div>
    <!-- 物料列表 -->
    <el-dialog v-dialogDragBox title="物料列表" :visible.sync="materialOpen" width="1150px" class="custom-dialog custom-material-dialog" append-to-body>
      <material ref="material" :isPopup="true" :useOrg="useOrg" @selected="selectMaterial" :queryData="queryData" showUseOrg v-if="materialOpen"></material>
      <div slot="footer" class="dialog-footer">
        <el-button class="custom-dialog-btn" @click="materialOpen = false">取消</el-button>
        <el-button class="custom-dialog-btn primary" :class="{ disabled: !selectedMaterial.Number }" :disabled="!selectedMaterial.Number" @click="handleSelectMaterial">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import material from '@/views/kingdee/material'
import { getMaterialList } from '@/api/kingdee'

export default {
  name: 'SearchSelectMaterial',
  components: { material },
  props: {
    label: {
      type: String,
      default: '物料名称'
    },
    keyword: {
      type: String,
      default: ''
    },
    useOrg: {
      type: String,
      default: undefined
    },
    showLabel: {
      type: Boolean,
      default: true
    },
    size: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    },
    options: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      dataLoading: false,
      dataOptions: [],
      materialOpen: false,
      selectedMaterial: {},
      queryData: {}
    }
  },
  watch: {
    options: {
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          const hasValidData = newVal.some(item => item && item.Number)
          if (hasValidData) {
            this.dataOptions = [...newVal]
          }
        }
      },
      deep: true,
      immediate: true
    }
  },
  computed: {
    value: {
      get() {
        return this.keyword
      },
      set(val) {
        this.$emit('update:keyword', val)
      }
    },
    // 动态 key 用于强制重新渲染
    selectKey() {
      const currentOption = this.dataOptions.find(item => item.Number === this.keyword)
      return currentOption ? `${this.keyword}-${currentOption.Name}` : this.keyword
    }
  },
  methods: {
    // 打开物料列表
    openMaterial() {
      this.queryData = { useOrg: this.useOrg, selected: { Number: this.value } }
      this.materialOpen = true
      this.$nextTick(() => {
        this.$refs.material.handleResetQuery(this.queryData)
      })
    },
    // 选择物料
    selectMaterial(row) {
      this.selectedMaterial = row
    },
    // 确定选择物料
    handleSelectMaterial() {
      this.materialOpen = false
      this.dataOptions = [this.selectedMaterial]
      this.value = this.selectedMaterial.Number
      this.$emit('callBack', this.selectedMaterial)
    },
    // 搜索
    handleChange(value) {
      const obj = this.dataOptions.find(item => item.Number == value)
      this.$emit('callBack', obj)
    },
    // 远程搜索
    remoteMethod(query) {
      getMaterialList({ useOrg: this.useOrg, name: query, limit: 30 }).then(res => {
        const data = res.data?.data || []
        this.dataOptions = data.reduce((unique, item) => {
          const exists = unique.find(u => u.Number === item.Number)
          if (!exists) unique.push(item)
          return unique
        }, [])
      })
    }
  }
}
</script>
<style lang="scss" scoped>
@import '@/assets/styles/custom-new.scss';
.custom-dialog.custom-material-dialog ::v-deep {
  .el-dialog__body {
    padding: 0;
  }
}
.custom-search-select {
  display: flex;
  align-items: center;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
  &.medium {
    height: 36px;
  }
  &.small {
    height: 32px;
    .el-icon-search {
      padding-left: 10px !important;
      padding-right: 10px !important;
    }
  }
  &.mini {
    height: 28px;
    .el-icon-search {
      padding-left: 5px !important;
      padding-right: 5px !important;
    }
  }
  ::v-deep .el-input__inner {
    border: none;
    padding: 0 0 0 10px;
  }
  ::v-deep .el-button {
    border: none;
    &:hover {
      background-color: transparent;
    }
  }
  .el-icon-search {
    color: #c0c4cc;
    &:disabled {
      background-color: #f5f7fa;
    }
  }
}
</style>
