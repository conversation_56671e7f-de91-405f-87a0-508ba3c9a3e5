<template>
  <div style="display: inline-block">
    <el-form-item :label="label" v-if="showLabel">
      <div class="custom-search-select" :class="{ medium: size == 'medium', small: size == 'small', mini: size == 'mini' }">
        <el-select v-model="value" placeholder="请选择" :loading="dataLoading" @change="handleChange" style="width: 100%" filterable remote :remote-method="remoteMethod" :size="size" :disabled="disabled">
          <el-option v-for="item in dataOptions" :key="item.Number" :label="item.Name" :value="item.Number">
            <span style="float: left">{{ item.Name }}</span>
            <span style="float: right; color: #999; font-size: 12px">{{ item.Number }}</span>
          </el-option>
        </el-select>
        <el-button class="el-icon-search" :size="size" @click="openStock" :disabled="disabled" />
      </div>
    </el-form-item>
    <div class="custom-search-select" :class="{ medium: size == 'medium', small: size == 'small', mini: size == 'mini' }" v-if="!showLabel">
      <el-select v-model="value" placeholder="请选择" :loading="dataLoading" @change="handleChange($event)" style="width: 100%" filterable remote :remote-method="remoteMethod" :size="size" :disabled="disabled">
        <el-option v-for="item in dataOptions" :key="item.Number" :label="item.Name" :value="item.Number">
          <span style="float: left">{{ item.Name }}</span>
          <span style="float: right; color: #999; font-size: 12px">{{ item.Number }}</span>
        </el-option>
      </el-select>
      <el-button class="el-icon-search" :size="size" @click="openStock" :disabled="disabled" />
    </div>
    <!-- 仓库列表 -->
    <el-dialog v-dialogDragBox title="仓库列表" :visible.sync="stockOpen" width="1150px" class="custom-dialog custom-stock-dialog" append-to-body>
      <stock-list ref="stockList" :isPopup="true" :useOrg="useOrg" @selectStock="selectStock" :queryData="queryData" :showUseOrg="showUseOrg" v-if="stockOpen"></stock-list>
      <div slot="footer" class="dialog-footer">
        <el-button class="custom-dialog-btn" @click="stockOpen = false">取消</el-button>
        <el-button class="custom-dialog-btn primary" :class="{ disabled: !selectedStock.Number }" :disabled="!selectedStock.Number" @click="handleSelectStock">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import stockList from '@/views/kingdee/stock'
import { getStockList } from '@/api/kingdee'

export default {
  name: 'SearchSelectStock',
  components: { stockList },
  props: {
    label: {
      type: String,
      default: '仓库名称'
    },
    keyword: {
      type: String,
      default: ''
    },
    useOrg: {
      type: String,
      default: undefined
    },
    showLabel: {
      type: Boolean,
      default: true
    },
    size: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    },
    showUseOrg: {
      type: Boolean,
      default: true
    },
    options: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      dataLoading: false,
      dataOptions: [],
      stockOpen: false,
      selectedStock: {},
      queryData: {}
    }
  },
  computed: {
    value: {
      get() {
        return this.keyword
      },
      set(val) {
        this.$emit('update:keyword', val)
      }
    }
  },
  watch: {
    options: {
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          const hasValidData = newVal.some(item => item && (item.Number || item.Name))
          if (hasValidData) {
            this.dataOptions = [...newVal]
          }
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    // 打开仓库列表
    openStock() {
      this.queryData = { useOrg: this.useOrg, selected: { Number: this.value } }
      this.stockOpen = true
      this.$nextTick(() => {
        this.$refs.stockList.handleResetQuery(false, { Number: this.value })
      })
    },
    // 选择仓库
    selectStock(row) {
      this.selectedStock = row
    },
    // 确定选择仓库
    handleSelectStock() {
      this.stockOpen = false
      this.dataOptions = [this.selectedStock]
      this.value = this.selectedStock.Number
      this.$emit('callBack', this.selectedStock)
    },
    // 搜索
    handleChange(value) {
      const obj = this.dataOptions.find(item => item.Number == value)
      this.$emit('callBack', obj)
    },
    // 远程搜索
    remoteMethod(query) {
      this.dataLoading = true
      getStockList({ useOrg: this.useOrg, name: query, limit: 30 })
        .then(res => {
          const data = res.data?.data || []
          this.dataOptions = data.reduce((unique, item) => {
            const exists = unique.find(u => u.Number === item.Number)
            if (!exists) unique.push(item)
            return unique
          }, [])
        })
        .finally(() => {
          this.dataLoading = false
        })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/custom-new.scss';
.custom-dialog.custom-material-dialog ::v-deep {
  .el-dialog__body {
    padding: 0;
  }
}
.custom-search-select {
  display: flex;
  align-items: center;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
  &.medium {
    height: 36px;
  }
  &.small {
    height: 32px;
    .el-icon-search {
      padding-left: 10px !important;
      padding-right: 10px !important;
    }
  }
  &.mini {
    height: 28px;
    .el-icon-search {
      padding-left: 5px !important;
      padding-right: 5px !important;
    }
  }
  ::v-deep .el-input__inner {
    border: none;
    padding: 0 0 0 10px;
  }
  ::v-deep .el-button {
    border: none;
    &:hover {
      background-color: transparent;
    }
  }
  .el-icon-search {
    color: #c0c4cc;
    &:disabled {
      background-color: #f5f7fa;
    }
  }
}
</style>
