<template>
  <div style="display: inline-block">
    <el-form-item :label="label" v-if="showLabel">
      <div class="custom-search-select" :class="{ medium: size == 'medium', small: size == 'small', mini: size == 'mini' }">
        <el-select v-model="value" filterable remote placeholder="请输入客户名称" :remote-method="remoteMethod" :loading="dataLoading" @change="handleQuery" style="width: 100%">
          <el-option v-for="item in dataOptions" :key="item.Number" :label="item.Name" :value="item.Number"></el-option>
        </el-select>
        <el-button class="el-icon-search" @click="openCustomer" />
      </div>
    </el-form-item>
    <div class="custom-search-select" :class="{ medium: size == 'medium', small: size == 'small', mini: size == 'mini' }" v-if="!showLabel">
      <el-select v-model="value" filterable remote placeholder="请输入客户名称" :remote-method="remoteMethod" :loading="dataLoading" @change="handleQuery" style="width: 100%">
        <el-option v-for="item in dataOptions" :key="item.Number" :label="item.Name" :value="item.Number"></el-option>
      </el-select>
      <el-button class="el-icon-search" @click="openCustomer" />
    </div>
    <!-- 客户列表 -->
    <el-dialog v-dialogDragBox title="客户列表" :visible.sync="customerOpen" width="1150px" class="custom-dialog custom-customer-dialog" append-to-body>
      <customer ref="customer" :isPopup="true" :useOrg="useOrg" @selectCustomer="selectCustomer"></customer>
      <div slot="footer" class="dialog-footer">
        <el-button class="custom-dialog-btn" @click="customerOpen = false">取消</el-button>
        <el-button class="custom-dialog-btn primary" :class="{ disabled: !selectedCustomer.Number }" :disabled="!selectedCustomer.Number" @click="handleSelectCustomer">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import customer from '@/views/kingdee/customer'
import { getCustomerList } from '@/api/kingdee/customer'

export default {
  name: 'CustomerSearchSelect',
  components: { customer },
  props: {
    label: {
      type: String,
      default: '客户名称'
    },
    keyword: {
      type: String,
      default: ''
    },
    showLabel: {
      type: Boolean,
      default: true
    },
    isBack: {
      type: Boolean,
      default: false
    },
    useOrg: {
      type: String,
      default: ''
    },
    size: {
      type: String,
      default: ''
    },
    options: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      dataLoading: false,
      dataOptions: [],
      customerOpen: false,
      selectedCustomer: {}
    }
  },
  computed: {
    value: {
      get() {
        return this.keyword
      },
      set(val) {
        this.$emit('update:keyword', val)
      }
    }
  },
  watch: {
    options: {
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          const hasValidData = newVal.some(item => item && (item.Number || item.Name))
          if (hasValidData) {
            this.dataOptions = [...newVal]
          }
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    // 打开客户列表
    openCustomer() {
      this.customerOpen = true
      this.$nextTick(() => {
        this.$refs.customer.handleResetQuery()
      })
    },
    // 选择客户
    selectCustomer(customer) {
      this.selectedCustomer = customer
    },
    // 确定选择客户
    handleSelectCustomer() {
      this.customerOpen = false
      this.dataOptions = [this.selectedCustomer]
      this.value = this.selectedCustomer.Number
      if (this.isBack) this.$emit('callBack', this.selectedCustomer)
      else this.$emit('callBack')
      this.selectedCustomer = {}
    },
    // 搜索
    handleQuery(value) {
      const data = this.dataOptions.find(item => item.Number === value)
      if (this.isBack) this.$emit('callBack', data)
      else this.$emit('callBack')
    },
    // 远程搜索
    remoteMethod(query) {
      getCustomerList({ name: query, useOrg: this.useOrg, limit: 30 }).then(res => {
        const data = res.data?.data || []
        this.dataOptions = data.reduce((unique, item) => {
          const exists = unique.find(u => u.Number === item.Number)
          if (!exists) unique.push(item)
          return unique
        }, [])
      })
    }
  }
}
</script>
<style lang="scss" scoped>
@import '@/assets/styles/custom-new.scss';
.custom-dialog.custom-customer-dialog ::v-deep {
  .el-dialog__body {
    padding: 0;
  }
}
.custom-search-select {
  display: flex;
  align-items: center;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
  &.medium {
    height: 36px;
  }
  &.small {
    height: 32px;
    .el-icon-search {
      padding-left: 10px !important;
      padding-right: 10px !important;
    }
  }
  &.mini {
    height: 28px;
    .el-icon-search {
      padding-left: 5px !important;
      padding-right: 5px !important;
    }
  }
  ::v-deep .el-input__inner {
    border: none;
    padding: 0 0 0 10px;
  }
  ::v-deep .el-button {
    border: none;
    &:hover {
      background-color: transparent;
    }
  }
  .el-icon-search {
    color: #c0c4cc;
  }
}
</style>
