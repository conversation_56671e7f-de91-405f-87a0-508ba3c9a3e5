<template>
  <div style="display: inline-block">
    <el-form-item :label="label" v-if="showLabel">
      <div class="custom-search-select" :class="{ medium: size == 'medium', small: size == 'small', mini: size == 'mini' }">
        <el-select v-model="value" filterable remote placeholder="请输入供应商名称" :remote-method="remoteMethod" :loading="dataLoading" @change="handleQuery" style="width: 100%" :disabled="disabled">
          <el-option v-for="item in dataOptions" :key="item.FNumber" :label="item.FName" :value="item.FNumber"></el-option>
        </el-select>
        <el-button class="el-icon-search" @click="openSupplier" :disabled="disabled" />
      </div>
    </el-form-item>
    <div class="custom-search-select" :class="{ medium: size == 'medium', small: size == 'small', mini: size == 'mini' }" v-if="!showLabel">
      <el-select v-model="value" filterable remote placeholder="请输入供应商名称" :remote-method="remoteMethod" :loading="dataLoading" @change="handleQuery" style="width: 100%" :disabled="disabled">
        <el-option v-for="item in dataOptions" :key="item.FNumber" :label="item.FName" :value="item.FNumber"></el-option>
      </el-select>
      <el-button class="el-icon-search" @click="openSupplier" :disabled="disabled" />
    </div>
    <!-- 供应商列表 -->
    <el-dialog v-dialogDragBox title="供应商列表" :visible.sync="supplierOpen" width="1150px" class="custom-dialog custom-supplier-dialog" append-to-body>
      <supplier ref="supplier" :isPopup="true" :useOrg="useOrg" @selectSupplier="selectSupplier"></supplier>
      <div slot="footer" class="dialog-footer">
        <el-button class="custom-dialog-btn" @click="supplierOpen = false">取消</el-button>
        <el-button class="custom-dialog-btn primary" :class="{ disabled: !selectedSupplier.FNumber }" :disabled="!selectedSupplier.FNumber" @click="handleSelectSupplier">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import supplier from '@/views/kingdee/supplier'
import { getSupplierList } from '@/api/kingdee'

export default {
  name: 'SupplierSearchSelect',
  components: { supplier },
  props: {
    label: {
      type: String,
      default: '供应商名称'
    },
    keyword: {
      type: String,
      default: ''
    },
    useOrg: {
      type: String,
      default: undefined
    },
    showLabel: {
      type: Boolean,
      default: true
    },
    isBack: {
      type: Boolean,
      default: false
    },
    size: {
      type: String,
      default: ''
    },
    options: {
      type: Array,
      default: () => []
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dataLoading: false,
      dataOptions: [],
      supplierOpen: false,
      selectedSupplier: {}
    }
  },
  watch: {
    options: {
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          const hasValidData = newVal.some(item => item && (item.FNumber || item.FName))
          if (hasValidData) {
            this.dataOptions = [...newVal]
          }
        }
      },
      deep: true,
      immediate: true
    }
  },
  computed: {
    value: {
      get() {
        return this.keyword
      },
      set(val) {
        this.$emit('update:keyword', val)
      }
    }
  },
  methods: {
    // 打开供应商列表
    openSupplier() {
      this.supplierOpen = true
      this.$nextTick(() => {
        this.$refs.supplier.handleResetQuery(false, { FNumber: this.value })
      })
    },
    // 选择供应商
    selectSupplier(supplier) {
      this.selectedSupplier = supplier
    },
    // 确定选择供应商
    handleSelectSupplier() {
      this.supplierOpen = false
      this.dataOptions = [this.selectedSupplier]
      this.value = this.selectedSupplier.FNumber
      if (this.isBack) this.$emit('callBack', this.selectedSupplier)
      else this.$emit('callBack')
      this.selectedSupplier = {}
    },
    // 搜索
    handleQuery(value) {
      const data = this.dataOptions.find(item => item.FNumber === value)
      if (this.isBack) this.$emit('callBack', data)
      else this.$emit('callBack')
    },
    // 远程搜索
    remoteMethod(query) {
      getSupplierList({ name: query, limit: 30 }).then(res => {
        const data = res.data?.data || []
        this.dataOptions = data.reduce((unique, item) => {
          const exists = unique.find(u => u.FNumber === item.FNumber)
          if (!exists) unique.push(item)
          return unique
        }, [])
      })
    }
  }
}
</script>
<style lang="scss" scoped>
@import '@/assets/styles/custom-new.scss';
.custom-dialog.custom-supplier-dialog ::v-deep {
  .el-dialog__body {
    padding: 0;
  }
}
.custom-search-select {
  display: flex;
  align-items: center;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
  &.medium {
    height: 36px;
  }
  &.small {
    height: 32px;
    .el-icon-search {
      padding-left: 10px !important;
      padding-right: 10px !important;
    }
  }
  &.mini {
    height: 28px;
    .el-icon-search {
      padding-left: 5px !important;
      padding-right: 5px !important;
    }
  }
  ::v-deep .el-input__inner {
    border: none;
    padding: 0 0 0 10px;
  }
  ::v-deep .el-button {
    border: none;
    &:hover {
      background-color: transparent;
    }
  }
  .el-icon-search {
    color: #c0c4cc;
    &.is-disabled {
      background-color: #f5f7fa;
      color: #c0c4cc;
    }
  }
}
</style>
