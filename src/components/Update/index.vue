<template>
  <div>
    <el-dialog v-dialogDragBox title="" :visible.sync="isShow" width="1150px" custom-class="updateDialog">
      <div class="updateBox">
        <template v-if="isHistory">
          <div class="updateBox-title">更新历史</div>
          <el-timeline>
            <el-timeline-item :timestamp="item.createTime + ' ' + item.noticeTitle" placement="top" v-for="item in infoList" :key="item.noticeId">
              <div class="updateBox-con" v-html="item.noticeContent"></div>
            </el-timeline-item>
          </el-timeline>
        </template>
        <template v-else>
          <div class="updateBox-title">{{ info.noticeTitle }}</div>
          <div class="updateBox-tip">本次更新内容</div>
          <div class="updateBox-con" v-html="info.noticeContent"></div>
        </template>
      </div>
      <div slot="footer" class="updateBox-footer">
        <template v-if="initiative">
          <el-button type="primary" @click="isShow = false">关 闭</el-button>
        </template>
        <template v-else>
          <el-button @click="getUpdateList" v-if="!isHistory">更新历史</el-button>
          <el-button type="primary" @click="update">我知道了</el-button>
        </template>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { checkVersionLast, checkVersionList } from '@/api/shouye'

export default {
  name: 'Update',
  props: {
    initiative: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isShow: false,
      info: {},
      infoList: [],
      isHistory: false
    }
  },
  created() {},
  methods: {
    getUpdate() {
      checkVersionLast().then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          if (data) {
            this.info = data
            const createTime = localStorage.getItem('updateShow')
            if (createTime) {
              if (createTime !== data.createTime) this.isShow = true
              else this.isShow = false
            } else {
              this.isShow = true
            }
            this.isHistory = false
          } else this.isShow = false
        } else this.$message.error(msg)
      })
    },
    getUpdateList() {
      checkVersionList().then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          this.infoList = data
          this.isHistory = true
          if (this.initiative) this.isShow = true
        } else this.$message.error(msg)
      })
    },
    update() {
      window.location.reload()
      this.isShow = false
      localStorage.setItem('updateShow', this.info.createTime)
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep {
  .el-dialog__header {
    display: none;
  }
  .updateDialog {
    background: url(~@/assets/images/update-bg.png) center top no-repeat;
    background-size: 100% 100%;
  }
  .updateBox {
    padding: 0 30px;
    &-title {
      font-size: 32px;
      color: #333333;
      font-weight: 500;
      line-height: 32px;
      text-align: center;
      margin-top: 40px;
      margin-bottom: 100px;
    }
    &-tip {
      font-size: 20px;
      color: #666666;
      line-height: 20px;
      margin-bottom: 30px;
    }
    &-con {
      font-size: 20px !important;
      line-height: 1.5em;
      color: #333333;
      ul,
      li,
      p {
        list-style: none;
        padding: 0;
        margin: 0;
      }
    }
    &-footer {
      .el-button {
        width: 270px;
        height: 50px;
        &.el-button--default {
          background-color: transparent;
        }
      }
    }
  }
  .el-card {
    background: none !important;
  }
  .el-dialog__footer {
    padding-top: 0px;
    padding-bottom: 30px;
  }
}
</style>
