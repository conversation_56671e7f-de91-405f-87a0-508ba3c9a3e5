<template>
  <div>
    <!-- 生产工艺 -->
    <el-dialog v-dialogDragBox title="生产工艺" :visible.sync="open" width="1150px" class="custom-dialog" @close="handleClose" append-to-body>
      <div>
        <div class="productInfo">
          <image-preview :src="formatProductImg(info)" :width="52" :height="52" />
          <div class="productInfo_text">
            <div class="productInfo_text_title">{{ info.productName }}</div>
            <div class="productInfo_text_basic">
              <div class="item">
                <span class="item_title">规格</span>
                <span class="item_content">{{ info.model }}</span>
              </div>
              <div class="item">
                <span class="item_title">生产数量</span>
                <span class="item_content">{{ info.model }}</span>
              </div>
              <div class="item">
                <span class="item_title">来源合同</span>
                <span class="item_content">{{ info.model }}</span>
              </div>
              <div class="item">
                <span class="item_title">需求日期</span>
                <span class="item_content">{{ info.model }}</span>
              </div>
            </div>
          </div>
        </div>
        <div style="padding: 0 20px 12px" class="labelTitle">产品工艺</div>
        <div style="margin: 0 20px" class="craftBox">
          <div class="craftBox_list" v-for="(item, index) in list" :key="index">
            <div class="craftBox_list_title">{{ '工序 - ' + (index + 1) }}</div>
            <div class="craftBox_list_concent">
              <div class="concent_top">
                <div class="concent_item">
                  <span class="title">生产工艺</span>
                  <span class="text">{{ item.process && item.process.name }}</span>
                </div>
                <div class="concent_item">
                  <span class="title">工艺费用</span>
                  <span class="fee">{{ item.cost + '元' }}</span>
                </div>
                <div class="concent_item">
                  <span class="title">所用设备</span>
                  <div class="device">
                    <span class="text" v-for="(it, i) in item.process && item.process.equipments" :key="i">{{ it.equipment && it.equipment.name }}</span>
                  </div>
                </div>
              </div>
              <div class="concent_bottom">
                <div class="concent_item">
                  <span class="title">生产描述</span>
                  <span class="text">{{ item.description }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn primary" @click="handleClose">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getBomDetail } from '@/api/bom'

export default {
  name: 'Process',
  data() {
    return {
      open: false,
      info: {},
      list: []
    }
  },
  methods: {
    handleView(data = {}) {
      const { bomId } = data
      if (!bomId) {
        this.$message.error('参数错误，请稍后重试')
        return
      }
      getBomDetail({ bomId }).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          this.info = data?.product || {}
          this.list = data?.processGroupList?.find(item => item.isDefault === true)?.processList || []
          this.open = true
        } else this.$message.error(msg)
      })
    },
    // 关闭
    handleClose() {
      this.open = false
      this.$emit('callBack')
    }
  }
}
</script>
<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';
.productInfo {
  display: flex;
  width: 1150px;
  padding: 20px;
  background: #f0f3f9;
  .productInfo_img {
    width: 52px;
    height: 52px;
  }
  .productInfo_text {
    margin-left: 20px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .productInfo_text_title {
      font-weight: 500;
      font-size: 14px;
      color: #333333;
      line-height: 20px;
    }
    .productInfo_text_basic {
      display: flex;
      .item {
        margin-right: 30px;
        display: flex;
        align-items: center;
        .item_title {
          font-weight: 400;
          font-size: 12px;
          color: #999999;
          line-height: 20px;
          margin-right: 10px;
        }
        .item_content {
          font-weight: 500;
          font-size: 14px;
          color: #333333;
          line-height: 20px;
        }
      }
    }
  }
}
.labelTitle {
  font-size: 14px;
  padding-bottom: 12px;
  color: #999999;
}
.craftBox {
  background: #f8f9fb;
  border-radius: 5px;
  border: 1px solid #cbd6e2;
  .craftBox_list {
    border-bottom: 1px solid #cbd6e2;
    &:last-child {
      border-bottom: none;
    }
    .craftBox_list_title {
      width: 110px;
      height: 30px;
      background: #bec6d0;
      border-bottom-right-radius: 10px;
      padding-left: 16px;
      line-height: 30px;
      font-weight: 500;
      font-size: 14px;
      color: #ffffff;
      margin-bottom: 15px;
    }
    .craftBox_list_concent {
      padding: 0 15px;
      .concent_top {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        margin-bottom: 16px;
      }
      .concent_bottom {
        margin-bottom: 10px;
      }
      .concent_item {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        margin-right: 60px;
        .title {
          font-weight: 400;
          font-size: 12px;
          color: #999999;
          line-height: 20px;
          margin-right: 20px;
        }
        .text {
          font-weight: 500;
          font-size: 14px;
          color: #333333;
          line-height: 20px;
        }
        .fee {
          font-weight: 500;
          font-size: 14px;
          color: #f35d09;
          line-height: 20px;
        }
        .device {
          display: flex;
          flex-wrap: wrap;
          align-items: center;
          .text {
            display: block;
            background: #e1e4e8;
            border-radius: 5px;
            font-weight: 500;
            font-size: 14px;
            color: #333333;
            line-height: 20px;
            padding: 5px 10px;
            margin: 5px;
          }
        }
      }
    }
  }
}
</style>
