<template>
  <div class="header-title">
    <span class="title-name">{{ dialogTittle }}</span>
    <el-tooltip effect="dark" :content="fullscreen ? '窗口' : '全屏'" placement="bottom">
      <span class="title-icon" :class="flodIconClass" @click="() => isFullscreen"></span>
    </el-tooltip>
  </div>
</template>

<script>
export default {
  name: 'DialogHeader',
  props: {
    dialogTittle: {
      type: String,
      default: () => ''
    },
    fullscreen: {
      type: Boolean,
      default: () => false
    }
  },
  data() {
    return {
      header_box: {
        display: 'flex',
        'align-items': 'center'
      }
    }
  },
  computed: {
    flodIconClass() {
      return this.fullscreen ? 'el-icon-copy-document' : 'el-icon-full-screen'
    },
    isFullscreen() {
      this.$emit('is-fullscreen', !this.fullscreen)
    }
  }
}
</script>
<style lang="scss" scoped>
.header-title {
  height: 50px;
  width: 100%;
  display: inline-flex;
  align-items: center;
  justify-content: space-between;
  .title-name {
    line-height: 24px;
    font-size: 18px;
    color: #666666;
  }
  .title-icon {
    font-size: 16px;
    color: #909399;
    cursor: pointer;
    margin-right: 10px;
  }
}
</style>
