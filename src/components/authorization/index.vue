<template>
  <div>
    <!-- 授权信息 -->
    <el-dialog v-dialogDragBox title="授权信息" :visible.sync="open" width="800px" class="custom-dialog">
      <div style="padding: 0 20px">
        <el-form ref="form" :model="form" :rules="rules" label-width="8em">
          <el-form-item label="联系人姓名">
            <span>{{ form.nickName }}</span>
          </el-form-item>
          <el-form-item label="真实姓名" prop="realName">
            <el-input style="width: 60%" placeholder="请输入真实姓名" v-model="form.realName"></el-input>
          </el-form-item>
          <el-form-item label="身份证号" prop="idCard">
            <el-input style="width: 60%" placeholder="请输入身份证号" v-model="form.idCard"></el-input>
          </el-form-item>
          <el-form-item label="授权起止时间" prop="timeArr">
            <el-date-picker style="width: 60%" v-model="form.timeArr" value-format="yyyy-MM-dd HH:mm:ss" type="daterange" start-placeholder="开始日期" end-placeholder="结束日期" :default-time="['00:00:00', '23:59:59']" :picker-options="pickerOptions"></el-date-picker>
          </el-form-item>
          <el-form-item label="授权委托书" prop="authLetter">
            <image-upload :limit="1" v-model="form.authLetter" />
          </el-form-item>
          <el-form-item label="身份证复印件" prop="idCardImage">
            <image-upload :limit="1" v-model="form.idCardImage" />
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer">
        <button class="custom-dialog-btn" @click="open = false">取 消</button>
        <button class="custom-dialog-btn primary" @click="handleSubmit">确 定</button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { getSupplierContact, updateSupplierContact } from '@/api/system/user'

export default {
  name: 'authorization',
  data() {
    return {
      open: false,
      index: 0,
      form: {},
      rules: {
        realName: [{ required: true, message: '请输入真实姓名', trigger: 'blur' }],
        idCard: [
          { required: true, message: '请输入身份证号', trigger: 'blur' },
          { pattern: /^[1-9]\d{5}(18|19|([23]\d))\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}(\d|X|x)$/, message: '请输入正确的身份证号', trigger: 'blur' }
        ],
        timeArr: [{ required: true, message: '请选择授权起止时间', trigger: 'change' }],
        authLetter: [{ required: true, message: '请上传授权书', trigger: ['blur', 'change'] }],
        idCardImage: [{ required: true, message: '请上传身份证复印件', trigger: ['blur', 'change'] }]
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now()
        }
      }
    }
  },
  methods: {
    reset() {
      this.form = {
        realName: undefined,
        idCard: undefined,
        authTimeStart: undefined,
        authTimeEnd: undefined,
        timeArr: undefined,
        authLetter: undefined,
        idCardImage: undefined
      }
      this.resetForm('form')
    },
    // 打开
    handleOpen(row, index) {
      this.reset()
      const { uuid } = row
      getSupplierContact({ uuid }).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          this.form = data
          if (data.authTimeStart && data.authTimeEnd) {
            this.$set(this.form, 'timeArr', [data.authTimeStart, data.authTimeEnd])
          }
          this.index = index
          this.open = true
        } else this.$message.error(msg)
      })
    },
    // 提交
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          let { uuid, post, phone, nickName, realName, idCard, timeArr, authLetter, idCardImage } = this.form
          const [authTimeStart, authTimeEnd] = timeArr
          const data = { uuid, post, phone, nickName, realName, idCard, authTimeStart, authTimeEnd, authLetter, idCardImage }
          updateSupplierContact(data).then(res => {
            const { code, msg } = res
            if (code === 200) {
              this.$message.success('授权成功')
              this.open = false
              this.$emit('refresh', data, this.index)
            } else this.$message.error(msg)
          })
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';
</style>
