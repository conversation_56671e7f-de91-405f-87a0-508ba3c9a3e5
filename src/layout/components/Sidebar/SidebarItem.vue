<template>
  <div v-if="!item.hidden" class="leftmenu-item" :style="{'--theme': theme}">
    <template v-if="hasOneShowingChild(item.children,item) && (!onlyOneChild.children||onlyOneChild.noShowingChildren)&&!item.alwaysShow">
      <app-link v-if="onlyOneChild.meta" :to="resolvePath(onlyOneChild.path, onlyOneChild.query)">
        <el-menu-item :index="resolvePath(onlyOneChild.path)" :class="{'submenu-title-noDropdown':!isNest}">
          <item :icon="onlyOneChild.meta.icon||(item.meta&&item.meta.icon)" :title="onlyOneChild.meta.title" />
        </el-menu-item>
      </app-link>
    </template>

    <el-submenu v-else ref="subMenu" :index="resolvePath(item.path)" popper-append-to-body>
      <template slot="title" v-if="item.meta">
        <item :icon="item.meta && item.meta.icon" :title="item.meta.title" />
      </template>
      <sidebar-item
        v-for="child in item.children"
        :key="child.path"
        :is-nest="true"
        :item="child"
        :base-path="resolvePath(child.path)"
        class="nest-menu"
      />
    </el-submenu>
  </div>
</template>

<script>
import path from 'path'
import { isExternal } from '@/utils/validate'
import Item from './Item'
import AppLink from './Link'
import FixiOSBug from './FixiOSBug'

export default {
  name: 'SidebarItem',
  components: { Item, AppLink },
  mixins: [FixiOSBug],
  props: {
    // route object
    item: {
      type: Object,
      required: true
    },
    isNest: {
      type: Boolean,
      default: false
    },
    basePath: {
      type: String,
      default: ''
    }
  },
  data() {
    this.onlyOneChild = null
    return {}
  },
  computed: {
    theme() {
      return this.$store.state.settings.theme;
    }
  },
  methods: {
    hasOneShowingChild(children = [], parent) {
      if (!children) {
        children = [];
      }
      const showingChildren = children.filter(item => {
        if (item.hidden) {
          return false
        } else {
          // Temp set(will be used if only has one showing child)
          this.onlyOneChild = item
          return true
        }
      })

      // When there is only one child router, the child router is displayed by default
      if (showingChildren.length === 1) {
        return true
      }

      // Show parent if there are no child router to display
      if (showingChildren.length === 0) {
        this.onlyOneChild = { ... parent, path: '', noShowingChildren: true }
        return true
      }

      return false
    },
    resolvePath(routePath, routeQuery) {
      if (isExternal(routePath)) {
        return routePath
      }
      if (isExternal(this.basePath)) {
        return this.basePath
      }
      if (routeQuery) {
        let query = JSON.parse(routeQuery);
        return { path: path.resolve(this.basePath, routePath), query: query }
      }
      return path.resolve(this.basePath, routePath)
    }
  }
}
</script>
<style lang="scss">
.leftmenu-item {
  .is-active.submenu-title-noDropdown{
    padding-left: 17px !important;
    border-left: 3px solid #{'var(--theme)'} !important;
    background-color: #f3f5fa !important;
  }
  .is-active.is-opened {
    .el-submenu__title {
      padding-left: 17px !important;
      border-left: 3px solid #{'var(--theme)'} !important;
      background-color: #f3f5fa !important;
      .el-submenu__icon-arrow {
        color: #{'var(--theme)'} !important;
      }
    }
    .el-menu {
      background-color: #f8f9fb !important;
      .el-menu-item{
        background-color: #f8f9fb !important;
      }
    }    
    .nest-menu{
      .el-submenu__title {
        padding-left: 40px !important;
        border-left-width: 0 !important;
        background-color: #f3f5fa !important;
        .el-submenu__icon-arrow {
          color: #909398 !important;
        }
      }
      .is-active.is-opened {
        .el-submenu__title {
          padding-left: 37px !important;
          border-left: 3px solid #{'var(--theme)'} !important;
          background-color: #f3f5fa !important;
          .el-submenu__icon-arrow {
            color: #{'var(--theme)'} !important;
          }
        }
      }
    }
  }
}
</style>
