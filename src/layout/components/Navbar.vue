<template>
  <div class="navbar">
    <hamburger id="hamburger-container" :is-active="sidebar.opened" class="hamburger-container"
      @toggleClick="toggleSideBar" />

    <!-- <breadcrumb id="breadcrumb-container" class="breadcrumb-container" v-if="!topNav" /> -->
    <top-nav id="topmenu-container" class="topmenu-container" v-if="topNav" />
    <div class="right-menu" v-if="device !== 'mobile'" style="">
      <el-tooltip content="返回自由客首页" effect="dark" placement="bottom">
        <div class="right-menu-item right-menu-link home" @click="home">
          <svg-icon icon-class="back" />
          返回自由客首页
        </div>
      </el-tooltip>
    </div>

    <div class="right-menu" style="float: right;">      
      <!-- 全局翻译 -->
      <global-translate :fixed="false" />
      <template v-if="device !== 'mobile'">
        <el-tooltip content="消息" effect="dark" placement="bottom">
          <chat class="right-menu-item right-menu-msg right-menu-link" ref="chat" />
        </el-tooltip>
        <el-tooltip content="退出登录" effect="dark" placement="bottom">
          <div class="right-menu-item right-menu-link" @click="logout">
            <svg-icon icon-class="loginout" />
            退出登录
          </div>
        </el-tooltip>

        <!-- <search id="header-search" class="right-menu-item" /> -->

        <!--        <div  @click="logout" style="cursor: pointer;font-size: 12px; color:#999;position: absolute;right: 100px;" >-->
        <!--          <img src="../../../public/imgs/注销 1.png" style="transform: translateY(5px);">-->
        <!--          退出登录-->
        <!--        </div>-->

        <!-- <el-dropdown-item divided >
            <span>退出登录</span>
          </el-dropdown-item> -->

        <!-- <el-tooltip content="退出登录" effect="dark" placement="bottom" @click.native="logout">
          <ruo-yi-git id="ruoyi-git" class="right-menu-item hover-effect" />
        </el-tooltip> -->

        <!-- <el-tooltip content="文档地址" effect="dark" placement="bottom">
          <ruo-yi-doc id="ruoyi-doc" class="right-menu-item hover-effect" />
        </el-tooltip> -->

        <!-- <screenfull id="screenfull" class="right-menu-item hover-effect" /> -->

        <!-- <el-tooltip content="布局大小" effect="dark" placement="bottom">
          <size-select id="size-select" class="right-menu-item hover-effect" />
        </el-tooltip> -->

      </template>

      <el-dropdown class="avatar-container right-menu-item hover-effect" trigger="click">
        <div class="avatar-wrapper">
          <img :src="avatar" class="user-avatar">
          <i class="el-icon-caret-bottom" />
        </div>
        <el-dropdown-menu slot="dropdown">
          <router-link to="/user/profile">
            <el-dropdown-item>个人中心</el-dropdown-item>
          </router-link>
          <el-dropdown-item @click.native="setting = true">
            <span>布局设置</span>
          </el-dropdown-item>
          <el-dropdown-item divided @click.native="logout">
            <span>退出登录</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Breadcrumb from '@/components/Breadcrumb'
import TopNav from '@/components/TopNav'
import Hamburger from '@/components/Hamburger'
import Screenfull from '@/components/Screenfull'
import SizeSelect from '@/components/SizeSelect'
import Search from '@/components/HeaderSearch'
import RuoYiGit from '@/components/RuoYi/Git'
import RuoYiDoc from '@/components/RuoYi/Doc'
import Cookies from "js-cookie";
import Chat from '@/components/Chat/index'

export default {
  components: {
    Chat,
    Breadcrumb,
    TopNav,
    Hamburger,
    Screenfull,
    SizeSelect,
    Search,
    RuoYiGit,
    RuoYiDoc
  },
  computed: {
    ...mapGetters([
      'sidebar',
      'avatar',
      'device'
    ]),
    setting: {
      get() {
        return this.$store.state.settings.showSettings
      },
      set(val) {
        this.$store.dispatch('settings/changeSetting', {
          key: 'showSettings',
          value: val
        })
      }
    },
    topNav: {
      get() {
        return this.$store.state.settings.topNav
      }
    }
  },
  methods: {
    toggleSideBar() {
      this.$store.dispatch('app/toggleSideBar')
    },
    async logout() {
      Cookies.remove("token");
      this.$confirm('确定注销并退出系统吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$store.dispatch('LogOut').then(() => {
          location.href = '/';
        })
      }).catch(() => { });
    },
    home() {
      this.$router.push({ path: '/' });
    }
  }
}
</script>

<style lang="scss" scoped>
.navbar {
  height: 50px;
  // overflow: hidden;
  position: relative;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0, 21, 41, .08);

  .hamburger-container {
    line-height: 46px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background .3s;
    -webkit-tap-highlight-color: transparent;

    &:hover {
      background: rgba(0, 0, 0, .025)
    }
  }

  .breadcrumb-container {
    float: left;
  }

  .topmenu-container {
    position: absolute;
    left: 50px;
  }

  .errLog-container {
    display: inline-block;
    vertical-align: top;
  }

  .right-menu {
    // float: right;
    height: 100%;
    line-height: 50px;
    display: inline-flex;
    align-items: center;

    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #5a5e66;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background .3s;

        &:hover {
          background: rgba(0, 0, 0, .025)
        }
      }
    }

    .right-menu-msg {
      height: 25px;
      line-height: 25px;

      ::v-deep {
        .chat-icon {
          padding-right: 0 !important;
          height: 100%;
        }
      }
    }

    .right-menu-link {
      display: inline-flex;
      align-items: center;
      cursor: pointer;
      color: #999999;
      font-size: 12px;

      &.home {
        color: #2E73F3;
      }

      .svg-icon {
        font-size: 18px;
        margin-right: 5px;
      }
    }

    .avatar-container {
      margin-right: 30px;

      .avatar-wrapper {
        margin-top: 5px;
        position: relative;

        .user-avatar {
          cursor: pointer;
          width: 40px;
          height: 40px;
          border-radius: 10px;
        }

        .el-icon-caret-bottom {
          cursor: pointer;
          position: absolute;
          right: -20px;
          top: 25px;
          font-size: 12px;
        }
      }
    }
  }
}</style>
