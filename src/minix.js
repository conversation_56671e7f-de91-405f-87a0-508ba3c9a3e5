export const kingdee = {
  data() {
    return {
      // 单据状态
      DocumentStatusOptions: [
        { value: 'A', label: '创建' },
        { value: 'B', label: '审核中' },
        { value: 'C', label: '已审核' },
        { value: 'D', label: '重新审核' },
        { value: 'Z', label: '暂存' }
      ],
      // 组织
      ApplicationOrgNumber: [
        { value: '100071', label: '世盛总部' },
        { value: '100072', label: '世盛生产部' },
        { value: '100073', label: '世盛销售部' },
        { value: '100074', label: '市场部' },
        { value: '100075', label: '徽标厂' },
        { value: '100076', label: '镀锌厂' },
        { value: '100077', label: '模具厂' },
        { value: '100078', label: '冲压厂' }
      ],
      // 往来单位类型
      ContactUnitTypeOptions: [
        { value: 'BD_Customer', label: '客户' },
        { value: 'BD_Supplier', label: '供应商' }
      ]
    }
  },
  computed: {
    KInfo() {
      return this.$store.state.kingdee.info
    },
    // 部门
    ApplicationDeptId() {
      return this.KInfo.ApplicationDeptId || []
    },
    // 组织
    ApplicationOrgId() {
      const ApplicationOrgId = this.KInfo.ApplicationOrgId || []
      if (ApplicationOrgId.length === 0) return []
      return Object.entries(ApplicationOrgId[0]).map(([key, value]) => ({
        value: key,
        label: value
      }))
    },
    // 单据类型
    BillTypeID() {
      const BillTypeID = this.KInfo.BillTypeID || []
      if (BillTypeID.length === 0) return []
      return Object.entries(BillTypeID[0]).map(([key, value]) => ({
        value: key,
        label: value
      }))
    },
    // 币种
    CurrencyId() {
      const CurrencyId = this.KInfo.CurrencyId || []
      if (CurrencyId.length === 0) return []
      return Object.entries(CurrencyId[0]).map(([key, value]) => ({
        value: key,
        label: value
      }))
    },
    // 岗位
    OrgHrPost() {
      return this.KInfo.OrgHrPost || []
    },
    // 请求类型
    RequestType() {
      const RequestType = this.KInfo.RequestType || []
      if (RequestType.length === 0) return []
      return Object.entries(RequestType[0]).map(([key, value]) => ({
        value: key,
        label: value
      }))
    },
    // 单位
    UnitList() {
      return this.KInfo.UnitList || []
    }
  },
  methods: {
    // 回显单据状态
    getDocumentStatusLabel(value) {
      const obj = this.DocumentStatusOptions.find(item => item.value === value)
      return obj?.label || ''
    },
    // 回显数组的value值为string
    getString(arr, len = undefined) {
      if (!arr) return ''
      if (!Array.isArray(arr)) return ''
      if (len) return arr.slice(0, len).map(item => item.Value).join(',')
      return arr.slice(0, 1).map(item => item.Value).join(',')
    },
    // 计算合并行数
    calculateSpan(data, key) {
      let spanArr = []
      let pos = 0
      for (let i = 0; i < data.length; i++) {
        if (i === 0) {
          spanArr.push(1)
          pos = 0
        } else {
          if (data[i][key] === data[i - 1][key]) {
            spanArr[pos] += 1
            spanArr.push(0)
          } else {
            spanArr.push(1)
            pos = i
          }
        }
      }
      return spanArr
    },
    // 根据value回显Option的label
    getOptionLabel(arr, value) {
      if (!arr) return ''
      const obj = arr.find(item => item.value === value)
      return obj?.label || ''
    }
  }
}
