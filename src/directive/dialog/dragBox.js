/**
* v-dialogDragBox 弹窗拖拽和调整大小
*/

export default {
    bind(el) {
        const dialogHeaderEl = el.querySelector('.el-dialog__header');
        const dragDom = el.querySelector('.el-dialog');
        
        // 设置头部可拖拽
        dialogHeaderEl.style.cursor = 'move';
        dragDom.style.position = 'absolute';
        dragDom.style.marginTop = 0;

        // 获取原有属性
        const sty = dragDom.currentStyle || window.getComputedStyle(dragDom, null);
        let width = dragDom.style.width;
        
        // 转换为像素值
        let widthInPx;
        const MIN_WIDTH = 600; // 设置最小宽度
        const MAX_WIDTH = window.innerWidth - 40; // 最大宽度（屏幕宽度减去边距）
        
        // 获取实际宽度
        const getActualWidth = () => {
            const computedStyle = window.getComputedStyle(dragDom);
            const width = computedStyle.width;
            
            // 如果设置了具体宽度，优先使用设置的宽度
            if (dragDom.style.width && dragDom.style.width !== 'auto') {
                const setWidth = dragDom.style.width;
                if (setWidth.includes('%')) {
                    return Math.min(+window.innerWidth * (+setWidth.replace(/\%/g, '') / 100), MAX_WIDTH);
                }
                return Math.min(+setWidth.replace(/\px/g, ''), MAX_WIDTH);
            }
            
            // 如果没有设置宽度，使用最小宽度
            if (!dragDom.style.width || dragDom.style.width === 'auto') {
                return MIN_WIDTH;
            }
            
            // 处理计算样式中的宽度
            if (width.includes('%')) {
                return Math.min(+window.innerWidth * (+width.replace(/\%/g, '') / 100), MAX_WIDTH);
            }
            return Math.min(+width.replace(/\px/g, ''), MAX_WIDTH);
        };

        // 计算并应用居中位置
        const updatePosition = () => {
            widthInPx = getActualWidth();
            // 如果设置了宽度，确保使用设置的宽度
            if (dragDom.style.width && dragDom.style.width !== 'auto') {
                const setWidth = dragDom.style.width;
                if (setWidth.includes('%')) {
                    widthInPx = Math.min(+window.innerWidth * (+setWidth.replace(/\%/g, '') / 100), MAX_WIDTH);
                } else {
                    widthInPx = Math.min(+setWidth.replace(/\px/g, ''), MAX_WIDTH);
                }
            } else {
                // 如果没有设置宽度，使用最小宽度
                widthInPx = MIN_WIDTH;
            }
            const left = Math.max(20, (window.innerWidth - widthInPx) / 2);
            dragDom.style.width = `${widthInPx}px`;
            dragDom.style.left = `${left}px`;
        };

        // 初始化位置
        updatePosition();

        // 添加窗口大小改变时的重新居中
        const handleResize = () => {
            updatePosition();
        };

        // 确保在DOM完全加载后重新计算位置
        setTimeout(updatePosition, 0);

        window.addEventListener('resize', handleResize);
        window.addEventListener('orientationchange', () => {
            // 在移动端旋转屏幕时，等待旋转完成后再更新位置
            setTimeout(updatePosition, 100);
        });
        
        // 在组件销毁时移除事件监听
        el._handleResize = handleResize;
        el._updatePosition = updatePosition;

        // 头部拖拽功能
        dialogHeaderEl.onmousedown = (e) => {
            const disX = e.clientX - dialogHeaderEl.offsetLeft;
            const disY = e.clientY - dialogHeaderEl.offsetTop;

            let styL, styT;
            if (sty.left.includes('%')) {
                styL = +window.innerWidth * (+sty.left.replace(/\%/g, '') / 100);
                styT = +window.innerHeight * (+sty.top.replace(/\%/g, '') / 100);
            } else {
                styL = +sty.left.replace(/\px/g, '');
                styT = +sty.top.replace(/\px/g, '');
            }

            document.onmousemove = function(e) {
                const l = e.clientX - disX;
                const t = e.clientY - disY;
                dragDom.style.left = `${l + styL}px`;
                dragDom.style.top = `${t + styT}px`;
            };

            document.onmouseup = function() {
                document.onmousemove = null;
                document.onmouseup = null;
            };
        };

        // 创建调整手柄
        const createHandle = (position, cursor) => {
            const handle = document.createElement('div');
            handle.style = `
                position: absolute;
                z-index: 1;
                ${position}
            `;
            handle.style.cursor = cursor;
            return handle;
        };

        // 添加调整手柄（左右和底部）
        const edges = [
            { position: 'left: 0; top: 0; bottom: 0; width: 6px;', cursor: 'ew-resize' },
            { position: 'right: 0; top: 0; bottom: 0; width: 6px;', cursor: 'ew-resize' },
            { position: 'bottom: 0; left: 0; right: 0; height: 6px;', cursor: 'ns-resize' }
        ];

        // 添加边的手柄
        edges.forEach(({ position, cursor }) => {
            const handle = createHandle(position, cursor);
            handle.addEventListener('mousedown', function(e) {
                e.preventDefault();
                const isHorizontal = cursor === 'ew-resize';
                const isVertical = cursor === 'ns-resize';
                
                const rect = dragDom.getBoundingClientRect();
                const startX = e.clientX;
                const startY = e.clientY;
                const startWidth = rect.width;
                const startHeight = rect.height;
                const startLeft = rect.left;
                const startTop = rect.top;

                document.onmousemove = function(e) {
                    e.preventDefault();
                    const deltaX = e.clientX - startX;
                    const deltaY = e.clientY - startY;

                    if (isHorizontal) {
                        if (position.includes('right')) {
                            const newWidth = Math.min(Math.max(MIN_WIDTH, startWidth + deltaX), MAX_WIDTH);
                            dragDom.style.width = `${newWidth}px`;
                            updatePosition(); // 更新位置以保持居中
                        } else {
                            const newWidth = Math.min(Math.max(MIN_WIDTH, startWidth - deltaX), MAX_WIDTH);
                            if (newWidth > MIN_WIDTH) {
                                dragDom.style.width = `${newWidth}px`;
                                dragDom.style.left = `${startLeft + (startWidth - newWidth)}px`;
                            }
                        }
                    }

                    if (isVertical) {
                        const newHeight = Math.max(200, startHeight + deltaY);
                        dragDom.style.height = `${newHeight}px`;
                    }
                };

                document.onmouseup = function() {
                    document.onmousemove = null;
                    document.onmouseup = null;
                };
            });

            dragDom.appendChild(handle);
        });
    },
    unbind(el) {
        // 移除事件监听
        if (el._handleResize) {
            window.removeEventListener('resize', el._handleResize);
            window.removeEventListener('orientationchange', el._updatePosition);
            delete el._handleResize;
            delete el._updatePosition;
        }
    }
}
