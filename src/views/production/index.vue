<template>
  <div class="newBox bgcf9 vh-85">
    <!-- 搜索 -->
    <div class="custom-search flex" style="padding-top: 18px">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
        <el-form-item label="产品名称" prop="productName">
          <el-input v-model="queryParams.productName" placeholder="请输入产品名称" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
<!--        <el-form-item label="产品编码" prop="productCode">-->
<!--          <el-input v-model="queryParams.productCode" placeholder="请输入产品编码" clearable @keyup.enter.native="handleQuery" />-->
<!--        </el-form-item>-->
<!--        <el-form-item label="规格" prop="specs">-->
<!--          <el-input v-model="queryParams.specs" placeholder="请输入规格" clearable @keyup.enter.native="handleQuery" />-->
<!--        </el-form-item>-->
<!--        <el-form-item label="型号" prop="model">-->
<!--          <el-input v-model="queryParams.model" placeholder="请输入型号" clearable @keyup.enter.native="handleQuery" />-->
<!--        </el-form-item>-->
<!--        <el-form-item label="表面处理" prop="surface">-->
<!--          <el-input v-model="queryParams.surface" placeholder="请输入表面处理" clearable @keyup.enter.native="handleQuery" />-->
<!--        </el-form-item>-->
<!--        <el-form-item label="材质" prop="materialQuality">-->
<!--          <el-input v-model="queryParams.materialQuality" placeholder="请输入材质" clearable @keyup.enter.native="handleQuery" />-->
<!--        </el-form-item>-->
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" style="background-color: #2e73f3; border-color: #2e73f3" size="small" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="small" @click="handleResetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <!-- 列表 -->
    <div style="padding: 15px 20px 0">
      <el-table v-loading="loading" ref="allTable" stripe :data="list" row-key="id" style="width: 100%" class="custom-table">
        <el-table-column align="center" type="index" label="序号"></el-table-column>
        <el-table-column align="center" prop="productName" label="产品名称" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <span class="table-link" @click="handleProductView(row.product)">{{ row.productName }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="picture1" label="图片" width="75">
          <template slot-scope="{ row }">
            <el-image :src="formatProductImg(row.product)" fit="cover" @click="handleImgView(row)">
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline"></i>
              </div>
            </el-image>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="specs" label="规格" show-overflow-tooltip>
          <template slot-scope="{ row }">{{ row.product && row.product.specs }}</template>
        </el-table-column>
        <el-table-column align="center" prop="model" label="型号" show-overflow-tooltip>
          <template slot-scope="{ row }">{{ row.product && row.product.model }}</template>
        </el-table-column>
        <el-table-column align="center" prop="productCode" label="产品编码" show-overflow-tooltip>
          <template slot-scope="{ row }">{{ row.product && row.product.productCode }}</template>
        </el-table-column>
        <el-table-column align="center" prop="materialQuality" label="材质" show-overflow-tooltip>
          <template slot-scope="{ row }">{{ row.product && row.product.materialQuality }}</template>
        </el-table-column>
        <el-table-column align="center" prop="surface" label="表面处理" show-overflow-tooltip>
          <template slot-scope="{ row }">{{ row.product && row.product.surface }}</template>
        </el-table-column>
        <el-table-column align="center" prop="unit" label="单位" show-overflow-tooltip>
          <template slot-scope="{ row }">{{ row.unit }}</template>
        </el-table-column>
        <el-table-column align="center" prop="productName" label="所需数量" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" label="操作">
          <template slot-scope="{ row }">
            <el-button class="table-btn primary" @click="handleContractView(row)">查看合同</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="custom-pagination">
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
      </div>
    </div>
    <!-- 产品详情 -->
    <product-dialog ref="productInfo"></product-dialog>
    <!-- 合同详情 -->
    <el-dialog v-dialogDragBox title="合同详情" :visible.sync="contractOpen" width="1150px" class="custom-dialog">
      <div style="text-align: center" :style="{ zoom: zoom }"><img style="max-width: 100%" :src="contractInfo.file" /></div>
      <div slot="footer">
        <button type="button" class="custom-dialog-btn primary" @click="contractOpen = false">关 闭</button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { getProductionList } from '@/api/production'
import ProductDialog from '@/views/public/product/dialog'
import { contractDetail } from '@/api/purchase'

export default {
  components: { ProductDialog },
  data() {
    return {
      loading: true,
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        productName: undefined,
        productCode: undefined,
        specs: undefined,
        model: undefined,
        surface: undefined,
        materialQuality: undefined
      },
      list: [],
      // 合同详情
      contractOpen: false,
      contractInfo: {},
      zoom: 1
    }
  },
  created() {
    // 查询列表
    this.getList()
  },
  methods: {
    // 查询列表
    getList() {
      this.loading = true
      getProductionList(this.queryParams).then(res => {
        const { code, rows, total, msg } = res
        if (code === 200) {
          this.list = rows
          this.total = total
          this.loading = false
        } else this.$message.error(msg)
      })
    },
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 重置搜索
    handleResetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 查看产品详情
    handleProductView(row) {
      this.$refs.productInfo.handleView(row)
    },
    handleImgView(row) {
      this.$refs.productInfo.handleImgView(row)
    },
    // 查看合同
    handleContractView(row) {
      contractDetail({ contractId: row.contractId }).then(res => {
        if (res.code === 200) {
          this.contractInfo = res.data
          this.contractOpen = true
          this.resizeFun()
        } else this.$message.error(res.msg)
      })
    },
    // 调整合同图片大小
    resizeFun() {
      const devicePixelRatio = window.devicePixelRatio
      if (devicePixelRatio !== 1) {
        if (!!window.ActiveXObject || 'ActiveXObject' in window) {
        } else this.zoom = 1 / devicePixelRatio
      }
    }
  }
}
</script>
<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';
.custom-table {
  .el-button {
    padding: 0;
  }
}
</style>
