<template>
  <div class="register">
    <el-form ref="registerForm" :model="registerForm" :rules="registerRules" class="register-form">

      <!-- 左 -->
      <div class="form-left">
        <img src="../../public/imgs/<EMAIL>">
      </div>

      <!-- 右 -->
      <div class="form-right">

        <div style="text-align:right;">
          <router-link class="link-type" :to="'/login'">已有账号？去登录</router-link>
          </div>

          <div class="nav">
          <div>
          <router-link class="link-type" :to="'/register'">个人账号注册</router-link>
          </div>
          <div>
          企业账号注册
          </div>
    </div>

          <!-- 表单内容 -->
          <div style="width:430px;margin:55px auto;">
            <el-form-item prop="username"> 
              <el-input v-model="registerForm.username" type="text" auto-complete="off" placeholder="请输入您的手机号">
                <!-- <svg-icon slot="prefix" icon-class="user" class="el-input__icon input-icon" /> -->
              </el-input>
            </el-form-item>
            
            <el-form-item prop="password">
              <el-input
                v-model="registerForm.password"
                type="password"
                auto-complete="off"
                placeholder="密码"
                show-password
                @keyup.enter.native="handleRegister"
              >
                <!-- <svg-icon slot="prefix" icon-class="password" class="el-input__icon input-icon" /> -->
              </el-input>
            </el-form-item>
          
          <el-form-item prop="confirmPassword">
            <el-input
              v-model="registerForm.confirmPassword"
              type="password"
              auto-complete="off"
              placeholder="确认密码"
              show-password
              @keyup.enter.native="handleRegister"
            >
              <!-- <svg-icon slot="prefix" icon-class="password" class="el-input__icon input-icon" /> -->
            </el-input>
          </el-form-item>
          <el-form-item prop="code" v-if="captchaEnabled">
            <el-input
              v-model="registerForm.code"
              auto-complete="off"
              placeholder="验证码"
              style="width: 63%"
              @keyup.enter.native="handleRegister"
            >
              <svg-icon slot="prefix" icon-class="validCode" class="el-input__icon input-icon" />
            </el-input>
            <div class="register-code">
              <!-- <img :src="codeUrl" @click="getCode" class="register-code-img"/> -->
              <el-button
                :loading="loading"
                size="medium"
                type="button"
                style="width:100%;"
                :disabled="sending"
                @click.native.prevent="sendSms"
              >
              <span>{{lastTimeContent}}</span>
              </el-button>
            </div>
          </el-form-item>

          <el-form-item prop="companyName"> 
              <el-input v-model="registerForm.companyName" type="text" auto-complete="off" placeholder="公司名称">
              </el-input>
            </el-form-item>

            <el-form-item prop="uscCode"> 
              <el-input v-model="registerForm.uscCode" type="text" auto-complete="off" placeholder="统一社会信用代码">
              </el-input>
            </el-form-item>
          
          <div style="margin-top:32px;">
            <el-form-item style="width:100%;">
              <el-button
                :loading="loading"
                size="medium"
                type="primary"
                style="width:100%;"
                @click.native.prevent="handleRegister"
              >
                <span v-if="!loading">立 即 注 册</span>
                <span v-else>注 册 中...</span>
              </el-button>
            
            </el-form-item>
          </div>
          </div>
      </div>
    </el-form>
    <!--  底部  -->
    <div class="el-register-footer">
      <div>SHISHENG © 河北世盛金属制品有限公司 | 备案号：冀ICP备2020031367号</div>
    </div>
  </div>
</template>

<script>
import { getCodeImg, register } from "@/api/login";
import * as user from "@/api/system/user";


export default {
  name: "Register",
  data() {
    const equalToPassword = (rule, value, callback) => {
      if (this.registerForm.password !== value) {
        callback(new Error("两次输入的密码不一致"));
      } else {
        callback();
      }
    };
    return {
      codeUrl: "",
      registerForm: {
        username: "",
        password: "",
        confirmPassword: "",
        code: "",
        uuid: "",
        companyName:"",
        uscCode:""
      },
      registerRules: {
        username: [
          { required: true, trigger: "blur", message: "请输入正确的手机号" },
          { min: 11, max: 20, message: '请输入正确的手机号', trigger: 'blur' }
        ],
        password: [
          { required: true, trigger: "blur", message: "请输入您的密码" },
          { min: 5, max: 20, message: '用户密码长度必须介于 5 和 20 之间', trigger: 'blur' }
        ],
        confirmPassword: [
          { required: true, trigger: "blur", message: "请再次输入您的密码" },
          { required: true, validator: equalToPassword, trigger: "blur" }
        ],
        code: [{ required: true, trigger: "change", message: "请输入验证码" }]
      },
      loading: false,
      captchaEnabled: true,
      sending:false,
      lastTimeContent:"发送验证码"
    };
  },
  created() {
    //this.getCode();
  },
  methods: {
    //发送验证码
    sendSms(){
      if(this.registerForm.username=="")
      {
        this.$message.error("请输入手机号");
        return false;
      }

      this.sending=true;
      
      let lastTime = 29;
            let timer = setInterval(() => {
                if(lastTime>0){
                    this.lastTimeContent = lastTime + 's';
                    lastTime--;
                } else {
                    window.clearInterval(timer);
                    this.lastTimeContent = '发送验证码'
                }
            }, 1000);

            user.smsCode({"phone":this.registerForm.username}).then((response) => {
              this.$message.success("发送成功");
          });
    
      
    },

    getCode() {
      getCodeImg().then(res => {
        this.captchaEnabled = res.captchaEnabled === undefined ? true : res.captchaEnabled;
        if (this.captchaEnabled) {
          this.codeUrl = "data:image/gif;base64," + res.img;
          this.registerForm.uuid = res.uuid;
        }
      });
    },
    handleRegister() {     
     var data={
        phone:this.registerForm.username,
        password:this.registerForm.confirmPassword,
        smsCode:this.registerForm.code,
        companyName:this.registerForm.companyName,
        uscCode:this.registerForm.uscCode,
        address:"公司地址",
        licenseImg:"img",
        logo:"logo"
      }

      user.registerCompany(data).then((response) => {
        this.$alert(response.msg);
        this.$alert("<font color='red'>恭喜你，您的账号 " + phone + " 注册成功！</font>", '系统提示', {
              type: 'success'
            }).then(() => {
              this.$router.push("/login");
            }).catch(() => {});
      });
    
      
      // this.$refs.registerForm.validate(valid => {
      //   if (valid) {
      //     this.loading = true;
      //     register(this.registerForm).then(res => {

      //       const username = this.registerForm.username;
      //       this.$alert("<font color='red'>恭喜你，您的账号 " + username + " 注册成功！</font>", '系统提示', {
      //         dangerouslyUseHTMLString: true,
      //         type: 'success'
      //       }).then(() => {
      //         this.$router.push("/login");
      //       }).catch(() => {});
      //     }).catch(() => {
      //       this.loading = false;
      //       if (this.captchaEnabled) {
      //         this.getCode();
      //       }
      //     })
      //   }
      // });
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.register {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background: #F3F4F6;
  //background-image: url("../assets/images/login-background.jpg");
  background-size: cover;
}
.title {
  margin: 0px auto 30px auto;
  text-align: center;
  color: #707070;
}

.register-form {
  border-radius: 6px;
  background: #ffffff;
  width: 1050px;
  height: 612px;
  box-shadow: 0px 0px 36px 0px rgba(0,0,0,0.06);
  display: flex;
  .el-input {
    height: 38px;
    input {
      height: 38px;
    }
  }
  .input-icon {
    height: 39px;
    width: 14px;
    margin-left: 2px;
  }
}
.register-tip {
  font-size: 13px;
  text-align: center;
  color: #bfbfbf;
}
.register-code {
  width: 33%;
  height: 38px;
  float: right;
  img {
    cursor: pointer;
    vertical-align: middle;
  }
}
.el-register-footer {
  height: 60px;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: rgba(255,255,255,0.52);
  font-family: Arial;
  font-size: 12px;
  letter-spacing: 1px;
  background-color: #373F5E;
}
.el-register-footer>div{
  height:20px;
  margin-top: 10px;
}

.register-code-img {
  height: 38px;
}

.link-type{
  color: #999999;
  font-size: 12px;
}

.form-left{
  width: 350px;
  height: 100%;
}
.form-left>img{
  width: 100%;
  height: 100%;
}
.form-right{
  width: 700px;
  height: 100%;
  padding: 30px;
}
.nav{
  width: 430px;
  height: 40px;
  margin:20px auto;
  display: flex;
  border-bottom: 1px solid #E2E6F3;
}
.nav>div{
  width: 110px;
  height: 100%;
  text-align: center;
}

.nav-1{
  font-size: 16px;
  color: #333333;
}

.nav>div:last-child{
    border-bottom: 2px solid #2E73F3;
}
</style>
