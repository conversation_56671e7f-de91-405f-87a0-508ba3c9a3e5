<template>
    <div class="newBox bgcf9 vh-85">
        <div class="custom-search flex" style="padding-top: 18px">
            <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" @submit.native.prevent>
                <el-form-item label="分类名称" prop="name">
                    <el-input v-model="queryParams.name" placeholder="请输入设备分类名称" clearable
                        @keyup.enter.native="handleQuery" />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
                    <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
                </el-form-item>
                <el-form-item label="" prop="status">
                    <el-select v-model="status" placeholder="请选择状态" @change="handleQuery">
                        <el-option v-for="item in statusOptions" :key="item.value" :label="item.label"
                            :value="item.value"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" icon="el-icon-plus" size="small" @click="handleAdd">新增设备分类</el-button>
                </el-form-item>
            </el-form>
        </div>
        <div class="Box">
            <el-table v-loading="loading" ref="table" stripe :data="list" row-key="id" style="width: 100%"
                class="custom-table">
                <el-table-column align="center" type="index" label="序号"></el-table-column>
                <el-table-column label="分类名称" align="center" prop="name" show-overflow-tooltip></el-table-column>
                <el-table-column label="分类简介描述" align="center" prop="description" show-overflow-tooltip
                    min-width="130"></el-table-column>
                <el-table-column label="创建时间" align="center" prop="createTime"></el-table-column>
                <el-table-column label="创建人" align="center" prop="createBy"></el-table-column>
                <el-table-column label="状态" align="center" prop="status">
                    <template slot-scope="{ row }">
                        <span class="table-tag" :class="row.status === 1 ? 'primary' : 'danger'">{{ row.status === 1 ?
                            '启用' : '禁用' }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="操作" align="center" width="220px">
                    <template slot-scope="{ row }">
                        <el-button class="table-btn" @click="handleEdit(row)">修改</el-button>
                        <el-button class="table-btn primary" v-if="row.status != 1"
                            @click="handleStatus(row, 1)">启用</el-button>
                        <el-button class="table-btn danger" v-if="row.status == 1"
                            @click="handleStatus(row, 0)">禁用</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <div class="custom-pagination">
                <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
                    :limit.sync="queryParams.pageSize" @pagination="getList" />
            </div>
        </div>

        <!--新增/修改-->
        <el-dialog v-dialogDragBox :title="title" :visible.sync="open" width="1150px" class="custom-dialog">
            <div style="padding: 0 20px">
                <el-form ref="form" :model="form" :rules="rules" label-width="120px">
                    <el-row :gutter="10">
                        <el-col :span="12">
                            <el-form-item label="设备分类名称" prop="name">
                                <el-input v-model="form.name" placeholder="请输入设备分类名称"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label="设备分类描述" prop="desc">
                                <el-input v-model="form.desc" type="textarea"  :autosize="{ minRows: 3, maxRows: 9}" placeholder="请输入设备分类描述详细"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
            <div slot="footer">
                <el-button class="custom-dialog-btn" @click="open = false">取消</el-button>
                <el-button class="custom-dialog-btn primary" @click="submitForm">确定</el-button>
            </div>
        </el-dialog>
    </div>
</template>
<script>
import { getProcessEquipmentList, addProcessClass, editProcessClass } from '@/api/bom'

export default {
  name: 'DeviceClassify',
    data() {
        return {
            status: 1,
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                status: undefined,
                name: undefined
            },
            list: [],
            total: 0,
            loading: false,
            statusOptions: [
                { label: '启用', value: 1, type: 'success' },
                { label: '禁用', value: 0, type: 'danger' }
            ],
            open: false,
            title: '新增设备所属分类',
            form: {},
            rules: {
                name: [
                    { required: true, message: '请输入设备分类名称', trigger: 'blur' },
                ],
            }
        }
    },
    created() {
        this.getList()
    },
    methods: {
        // 搜索
        handleQuery() {
            this.queryParams.pageNum = 1
            this.getList()
        },
        // 重置搜索
        resetQuery() {
            this.resetForm('queryForm')
            this.handleQuery()
        },
        // 获取列表
        getList() {
            this.loading = true
            this.queryParams.status = this.status
            getProcessEquipmentList(this.queryParams).then(res => {
                const { code, msg, rows, total } = res
                if (code === 200) {
                    this.list = rows
                    this.total = total
                    this.loading = false
                } else this.$message.error(msg)
            })
        },
        // 刷新列表
        refreshList() {
            getProcessEquipmentList(this.queryParams).then(res => {
                const { code, msg, rows, total } = res
                if (code === 200) {
                    this.$set(this, 'list', rows)
                    this.$set(this, 'total', total)
                } else this.$message.error(msg)
            })
        },
        // 修改状态
        // prettier-ignore
        handleStatus(row, val = 0) {
            const title = `是否${val === 1 ? '启用' : '禁用'}该设备分类？`
            this.$confirm(title, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                editProcessClass({ processClaId: row.id, status: val }).then(res => {
                    const { code, msg } = res
                    if (code === 200) {
                        this.$message.success('操作成功')
                        this.refreshList()
                    } else this.$message.error(msg)
                })
            }).catch(() => { })
        },
        // 新增
        handleAdd() {
            this.form = {
                name: undefined,
                desc: undefined,
                cla: 'equipment'
            }
            this.title = '新增设备所属分类'
            this.open = true
        },
        // 修改
        handleEdit(row) {
            this.form = {
                processClaId: row.id,
                name: row.name,
                desc: row.description,
                cla: row.cla
            }
            this.title = '修改设备所属分类'
            this.open = true
        },
        submitForm() {
            this.$refs['form'].validate((valid) => {
                if (valid) {
                    if (this.form.processClaId != null) {
                        editProcessClass(this.form).then(res => {
                            const { code, msg } = res
                            if (code === 200) {
                                this.$message.success('修改成功')
                                this.open = false
                                this.getList()
                            } else this.$message.error(msg)
                        })
                    } else {
                        addProcessClass(this.form).then(res => {
                            if (res.code === 200) {
                                this.$message.success('新增成功')
                                this.open = false
                                this.getList()
                            } else this.$message.error(msg)
                        })
                    }
                } else {
                    console.log('error submit!!');
                    return false;
                }
            });
        }
    }
}
</script>

<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';

.Box {
    padding: 15px 20px;
}

.table-tag {
    &.primary {
        color: $blue;
    }

    &.danger {
        color: $red;
    }
}

::v-deep {
    .el-button.table-btn {
        padding: 0;
    }
}

.deviceForm {}
</style>
