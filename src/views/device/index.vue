<template>
    <div class="newBox bgcf9 vh-85">
        <div class="custom-search flex" style="padding-top: 18px">
            <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" @submit.native.prevent>
                <el-form-item label="设备名称" prop="name">
                    <el-input v-model="queryParams.name" placeholder="请输入设备名称" clearable
                        @keyup.enter.native="handleQuery" />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
                    <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" icon="el-icon-plus" size="small" @click="handleAdd">新增设备</el-button>
                </el-form-item>
            </el-form>
        </div>
        <div class="Box">
            <el-table v-loading="loading" ref="table" stripe :data="list" row-key="id" style="width: 100%"
                class="custom-table">
                <el-table-column align="center" type="index" label="序号"></el-table-column>
                <el-table-column label="设备名称" align="center" prop="name" show-overflow-tooltip></el-table-column>
                <el-table-column align="center" prop="picture1" label="设备图片" width="75">
                    <template slot-scope="{ row }">
                        <el-image :src="formatProductImg(row)" fit="cover"
                            :preview-src-list="imgList(row)">
                            <div slot="error" class="image-slot">
                                <i class="el-icon-picture-outline"></i>
                            </div>
                        </el-image>
                    </template>
                </el-table-column>
                <el-table-column label="设备用途简介" align="center" prop="description" show-overflow-tooltip
                    min-width="130"></el-table-column>
                <el-table-column label="设备品牌" align="center" prop="name" show-overflow-tooltip></el-table-column>
                <el-table-column label="所属部门" align="center" prop="name" show-overflow-tooltip></el-table-column>
                <el-table-column label="创建时间" align="center" prop="createTime"></el-table-column>
                <el-table-column label="创建人" align="center" prop="createBy"></el-table-column>
                <el-table-column label="操作" align="center" width="220px">
                    <template slot-scope="{ row }">
                        <el-button class="table-btn" @click="handleView(row)">查看详情</el-button>
                        <!-- <el-button class="table-btn danger">删除</el-button> -->
                        <el-button class="table-btn primary" @click="handleEdit(row)">修改</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <div class="custom-pagination">
                <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
                    :limit.sync="queryParams.pageSize" @pagination="getList" />
            </div>
        </div>

        <!--新增/修改-->
        <el-dialog v-dialogDragBox :title="title" :visible.sync="open" width="1150px" class="custom-dialog">
            <div style="padding: 0 20px">
                <el-form ref="form" :model="form" :rules="rules" label-width="120px">
                    <el-row :gutter="10">
                        <el-col :span="12">
                            <el-form-item label="设备名称" prop="name">
                                <el-input v-model="form.name" placeholder="请输入设备名称" :disabled="disabled"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="所属设备分类" prop="classId">
                                <el-select v-model="form.classId" filterable placeholder="请选择所属设备分类"
                                    style="width: 100%" :disabled="disabled">
                                    <el-option v-for="item in classOptions" :key="item.id" :label="item.name"
                                        :value="item.id"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label="设备用途简介" prop="desc">
                                <el-input v-model="form.desc" type="textarea" :autosize="{ minRows: 3, maxRows: 9 }"
                                    placeholder="请输入设备用途简介" :disabled="disabled"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="所属部门" prop="deptId">
                                <el-select v-model="form.deptId" filterable placeholder="请选择设备所属部门"
                                    style="width: 100%" :disabled="disabled">
                                    <el-option v-for="item in deptOptions" :key="item.deptId" :label="item.deptName"
                                        :value="item.deptId"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="设备品牌" prop="brand">
                                <el-input v-model="form.brand" placeholder="请输入设备品牌" :disabled="disabled"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label="设备图片" prop="img">
                                <image-upload :fileSize="5" :limit="9" :fileType="['png', 'jpg', 'jpeg']" v-model="form.img" :isDisabled="disabled" :deleteShow="!disabled"/>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
            <div slot="footer">
                <el-button class="custom-dialog-btn" @click="open = false">取消</el-button>
                <el-button class="custom-dialog-btn primary" @click="submitForm" :disabled="disabled">确定</el-button>
            </div>
        </el-dialog>
    </div>
</template>
<script>
import { getEquipmentList, addEquipment, editEquipment, getProcessEquipmentList } from '@/api/bom'
import { listDept } from '@/api/system/dept'
export default {
  name: 'Device',
    data() {
        return {
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                name: undefined
            },
            list: [],
            total: 0,
            loading: false,
            classOptions: [],
            deptOptions: [],
            open: false,
            title: '新增设备',
            form: {},
            rules: {
                name: [
                    { required: true, message: '请输入设备名称', trigger: 'blur' },
                ],
                classId: [
                    { required: true, message: '请选择设备所属分类', trigger: 'change' },
                ],
                deptId: [
                    { required: true, message: '请选择设备所属部门', trigger: 'change' },
                ],
                img: [
                    { required: true, message: '请上传设备图片', trigger: 'change' },
                ],
            },
            fileType: ['jpg', 'jpeg', 'png'],
            disabled: false
        }
    },
    created() {
        this.getList()
        this.init()
    },
    methods: {
        // 初始
        init() {
            getProcessEquipmentList({
                pageNum: 1,
                pageSize: 10000,
                status: 1,
            }).then(res => {
                const {
                    code,
                    rows,
                    msg
                } = res
                if (code == 200) {
                    this.classOptions = rows
                } else this.$message.error(msg)
            })
            listDept().then(res => {
                const {
                    code,
                    data,
                    msg
                } = res
                if (code == 200) {
                    this.deptOptions = data
                } else this.$message.error(msg)
            })
        },
        // 搜索
        handleQuery() {
            this.queryParams.pageNum = 1
            this.getList()
        },
        // 重置搜索
        resetQuery() {
            this.resetForm('queryForm')
            this.handleQuery()
        },
        // 获取列表
        getList() {
            this.loading = true
            this.queryParams.status = this.status
            getEquipmentList(this.queryParams).then(res => {
                const { code, msg, rows, total } = res
                if (code === 200) {
                    this.list = rows
                    this.total = total
                    this.loading = false
                } else this.$message.error(msg)
            })
        },
        // 刷新列表
        refreshList() {
            getEquipmentList(this.queryParams).then(res => {
                const { code, msg, rows, total } = res
                if (code === 200) {
                    this.$set(this, 'list', rows)
                    this.$set(this, 'total', total)
                } else this.$message.error(msg)
            })
        },
        // 新增
        handleAdd() {
            this.form = {
                name: undefined,
                desc: undefined,
                brand: undefined,
                classId: undefined,
                deptId: undefined,
                img: undefined,
            }
            this.title = '新增设备'
            this.disabled = false
            this.open = true
        },
        // 修改
        handleEdit(row) {
            this.form = {
                equipmentId: row.id,
                name: row.name,
                desc: row.description,
                brand: row.brand,
                classId: row.classId,
                deptId: row.deptId,
                img: row.img,
                img_oss: row.img_oss
            }
            this.title = '修改设备'
            this.disabled = false
            this.open = true
        },
        // 查看
        handleView(row) {
            this.form = {
                equipmentId: row.id,
                name: row.name,
                desc: row.description,
                brand: row.brand,
                classId: row.classId,
                deptId: row.deptId,
                img: row.img,
                img_oss: row.img_oss
            }
            this.title = '查看设备'
            this.disabled = true
            this.open = true
        },
        submitForm() {
            this.$refs['form'].validate((valid) => {
                if (valid) {
                    if (this.form.equipmentId != null) {
                        editEquipment(this.form).then(res => {
                            const { code, msg } = res
                            if (code === 200) {
                                this.$message.success('修改成功')
                                this.open = false
                                this.getList()
                            } else this.$message.error(msg)
                        })
                    } else {
                        addEquipment(this.form).then(res => {
                            if (res.code === 200) {
                                this.$message.success('新增成功')
                                this.open = false
                                this.getList()
                            } else this.$message.error(msg)
                        })
                    }
                } else {
                    console.log('error submit!!');
                    return false;
                }
            });
        },
        // 图片处理
        imgList(row) {
            if (row.img_oss) {
                return row.img_oss.split(',')
            } else if (row.img) {
                let arr = row.img.split(',')
                arr.forEach(el => {
                    el = this.imgPath + el
                })
                return arr
            } else {
                let arr = []
                return arr
            }
        }
    }
}
</script>

<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';

.Box {
    padding: 15px 20px;
}

.table-tag {
    &.primary {
        color: $blue;
    }

    &.danger {
        color: $red;
    }
}

::v-deep {
    .el-button.table-btn {
        padding: 0;
    }
}

.deviceForm {}
</style>
