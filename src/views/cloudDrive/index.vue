<template>
  <div class="newBox bgcf9 vh-85" v-if="checkPermi(['cloud:drive:view'])">
    <!-- 面包屑 -->
    <div class="netdisk_breadcrumb_box">
      <el-breadcrumb separator-class="el-icon-arrow-right">
        <el-breadcrumb-item style="cursor: pointer" v-for="(item, index) in titleList" :key="index">
          <span :class="index == titleList.length - 1 ? 'blue' : 'gray'" @click="handleTitle(item)">{{
            item.folderName }}</span>
        </el-breadcrumb-item>
      </el-breadcrumb>
    </div>
    <!-- 搜索 -->
    <div class="custom-search flex netdisk_search">
      <div class="flex">
        <div class="custom-search-form flex">
          <input type="text" v-model="queryParams.name" placeholder="请输入产品名称" class="custom-search-input"
            @keyup.enter="handleQuery" />
          <button type="button" class="custom-search-button pointer" @click="handleQuery">
            <i class="el-icon-search"></i>
            搜索
          </button>
        </div>
        <button type="button" class="custom-search-add pointer netdisk_add_files" @click="handleAddFiles"
          v-if="checkPermi(['cloud:drive:addFiles'])">
          <i class="el-icon-plus"></i>
          新建文件夹
        </button>
        <button type="button" class="custom-search-add pointer" @click="handleUpload"
          v-if="checkPermi(['cloud:drive:upload'])">
          <i class="el-icon-plus"></i>
          上传文件
        </button>
      </div>
      <div class="netdisk_search_right flex">
        <div style="margin-right: 10px;">已使用：{{ usedNum }}</div>
        <div class="netdisk_paste_box" v-if="pasteShow" @click="confirmMoveFiles">
          <img class="netdisk_paste_img" src="@/assets/images/files_paste.png" alt="" srcset="" />
          <span class="netdisk_paste_text">粘贴</span>
        </div>
        <img class="netdisk_refresh_img" src="@/assets/images/refresh.png" alt="" @click="refreshChange" />
        <div class="netdisk_btn_switch flex">
          <div :class="isFlex ? 'active' : ''" @click="changeisFlex(true)">
            <img src="@/assets/images/list.png" alt="" />
          </div>
          <div :class="!isFlex ? 'active' : ''" @click="changeisFlex(false)">
            <img src="@/assets/images/tile.png" alt="" />
          </div>
        </div>
      </div>
    </div>

    <div class="netdisk_concent">
      <div class="netdisk_check_box">
        <el-checkbox v-model="checkedAll" style="margin-right: 15px" size="medium"
          @change="handleCheckAll">全选</el-checkbox>
        已选
        <span style="color: #2e73f3">{{ multipleSelection.length }}</span>
        项
      </div>
      <!-- 列表数据 -->
      <div class="netdisk_list_box" v-if="isFlex">
        <el-table ref="multipleTable" :data="tableData" tooltip-effect="dark" style="width: 100%"
          @selection-change="handleSelectionChange" @cell-mouse-enter="cellMouseEnter"
          @cell-mouse-leave="cellMouseLeave">
          <el-table-column type="selection" width="55"></el-table-column>
          <el-table-column label="文件名">
            <template slot-scope="scope">
              <div v-if="scope.row.folderId" class="netdisk_list_fileName" @click="handleFiles(scope.row)"
                style="cursor: pointer">
                <img src="@/assets/images/files_small.png" alt="" />
                <span>{{ scope.row.folderName }}</span>
              </div>
              <div v-if="scope.row.fileId" class="netdisk_list_fileName" style="cursor: pointer">
                <image-preview :isNotUrl="true" :src="scope.row.url"
                  v-if="(getSuffix(scope.row.fileName) == 'jpg' || getSuffix(scope.row.fileName) == 'jpeg' || getSuffix(scope.row.fileName) == 'gif' || getSuffix(scope.row.fileName) == 'png' || getSuffix(scope.row.fileName) == 'bmp') && scope.row.url !== undefined" />
                <img src="@/assets/images/video.jpg" alt="" @click="handleFile(scope.row, 'video')"
                  v-else-if="getSuffix(scope.row.fileName) == 'mp4' || getSuffix(scope.row.fileName) == 'avi' || getSuffix(scope.row.fileName) == 'webm' || getSuffix(scope.row.fileName) == 'mov' || getSuffix(scope.row.fileName) == 'wmv' || getSuffix(scope.row.fileName) == 'mkv' || getSuffix(scope.row.fileName) == 'flv'" />
                <img src="@/assets/images/audio.jpg" alt="" @click="handleFile(scope.row, 'audio')"
                  v-else-if="getSuffix(scope.row.fileName) == 'mp3' || getSuffix(scope.row.fileName) == 'wav' || getSuffix(scope.row.fileName) == 'ogg'" />
                <img src="@/assets/images/pdf.png" alt="" @click="handleFile(scope.row, 'pdf')"
                  v-else-if="getSuffix(scope.row.fileName) == 'pdf'" />
                <img src="@/assets/images/file_small.png" @click="handleFile(scope.row, 'otherFile')" alt="" v-else />
                <span>{{ scope.row.fileName }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="name" label="大小" width="120">
            <template slot-scope="scope">
              <div v-if="scope.row.folderId">
                <span>— —</span>
              </div>
              <div v-if="scope.row.fileId">
                <span>{{ scope.row.file_size }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column align="right" label="操作" show-overflow-tooltip>
            <template slot-scope="scope">
              <div class="flex netdisk_list_btn" v-if="scope.row.hovered">
                <div @click="moveCheckFiles('COPY', scope.row)" v-if="checkPermi(['cloud:drive:copy'])">
                  <img src="@/assets/images/file_copy.png" alt="" />
                  <span>复制</span>
                </div>
                <div @click="moveCheckFiles('MOVE', scope.row)" v-if="checkPermi(['cloud:drive:move'])">
                  <img src="@/assets/images/file_shear.png" alt="" />
                  <span>剪切</span>
                </div>
                <div @click="handleDownload(scope.row)" v-if="checkPermi(['cloud:drive:download'])">
                  <img src="@/assets/images/file_download.png" alt="" />
                  <span>下载</span>
                </div>
                <div @click="handleRename(scope.row)" v-if="checkPermi(['cloud:drive:rname'])">
                  <img src="@/assets/images/file_rename.png" alt="" />
                  <span>重命名</span>
                </div>
                <div @click="handleDelete(scope.row)" v-if="checkPermi(['cloud:drive:delete'])">
                  <img src="@/assets/images/file_delete.png" alt="" />
                  <span>删除</span>
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!-- 平铺数据 -->
      <div class="netdisk_tile_box" v-if="!isFlex">
        <div class="netdisk_tile_item" v-for="(item, index) in tableData" :key="index">
          <el-tooltip class="item" effect="dark"
            :content="item.folderId ? item.folderName : (item.fileId ? item.fileName : '--')" placement="top-start">
            <div>
              <img src="@/assets/images/files_big.png" alt="" v-if="item.folderId" @click="handleFiles(item)" />
              <div v-if="item.fileId">
                <image-preview :isNotUrl="true" :src="item.url"
                  v-if="(getSuffix(item.fileName) == 'jpg' || getSuffix(item.fileName) == 'jpeg' || getSuffix(item.fileName) == 'gif' || getSuffix(item.fileName) == 'png' || getSuffix(item.fileName) == 'bmp') && item.url !== undefined" />
                <img src="@/assets/images/video.jpg" alt="" @click="handleFile(item, 'video')"
                  v-else-if="getSuffix(item.fileName) == 'mp4' || getSuffix(item.fileName) == 'avi' || getSuffix(item.fileName) == 'webm' || getSuffix(item.fileName) == 'mov' || getSuffix(item.fileName) == 'wmv' || getSuffix(item.fileName) == 'mkv' || getSuffix(item.fileName) == 'flv'" />
                <img src="@/assets/images/audio.jpg" alt="" @click="handleFile(item, 'audio')"
                  v-else-if="getSuffix(item.fileName) == 'mp3' || getSuffix(item.fileName) == 'wav' || getSuffix(item.fileName) == 'ogg'" />
                <img src="@/assets/images/pdf.png" alt="" @click="handleFile(item, 'pdf')"
                  v-else-if="getSuffix(item.fileName) == 'pdf'" />
                <img src="@/assets/images/file_small.png" @click="handleFile(item, 'otherFile')" alt="" v-else />
              </div>
              <div v-if="item.folderId">{{ item.folderName }}</div>
              <div v-if="item.fileId">{{ item.fileName }}</div>
              <span v-if="item.folderId">— —</span>
              <span v-if="item.fileId">{{ item.file_size }}</span>
              <el-checkbox v-model="item.checked" class="netdisk_tile_check" size="medium"
                @change="handleCheck(item)"></el-checkbox>
            </div>
          </el-tooltip>
        </div>
      </div>
    </div>

    <!-- 悬浮功能窗 -->
    <div class="suspension_box" v-if="suspensionShow">
      <div class="suspension_item" @click="handleDownloadFiles" v-if="checkPermi(['cloud:drive:download'])">
        <img class="img" src="@/assets/images/file_download_w.png" alt="" srcset="" />
        <span class="text">下载</span>
      </div>
      <div class="suspension_item" @click="moveCheckFiles('COPY')" v-if="checkPermi(['cloud:drive:copy'])">
        <img class="img" src="@/assets/images/file_copy_w.png" alt="" srcset="" />
        <span class="text">复制</span>
      </div>
      <div class="suspension_item" v-if="multipleSelection.length == 1 && checkPermi(['cloud:drive:rname'])"
        @click="handleRename()">
        <img class="img" src="@/assets/images/file_rename_w.png" alt="" srcset="" />
        <span class="text">重命名</span>
      </div>
      <div class="suspension_item" @click="moveCheckFiles('MOVE')" v-if="checkPermi(['cloud:drive:move'])">
        <img class="img" src="@/assets/images/file_shear_w.png" alt="" srcset="" />
        <span class="text">剪切</span>
      </div>
      <div class="suspension_item" @click="deleteCheck" v-if="checkPermi(['cloud:drive:delete'])">
        <img class="img" src="@/assets/images/file_delete_w.png" alt="" srcset="" />
        <span class="text">删除</span>
      </div>
    </div>

    <!-- 新建文件夹 -->
    <el-dialog v-dialogDragBox title="新建文件夹" :visible.sync="addFilesShow">
      <el-form :model="addFilesForm" :rules="addFilesRules" ref="addFilesForm" label-position="right"
        label-width="100px">
        <el-form-item label="文件夹名称" prop="folderName">
          <el-input v-model="addFilesForm.folderName"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="addFilesShow = false">取 消</el-button>
        <el-button type="primary" @click="submitAddFiles">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 重命名文件夹 -->
    <el-dialog v-dialogDragBox title="重命名文件夹" :visible.sync="reNameShow">
      <el-form :model="reNameForm" :rules="reNameRules" ref="reNameForm" label-position="right" label-width="100px">
        <el-form-item label="文件夹名称" prop="name">
          <el-input v-model="reNameForm.name"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="reNameShow = false">取 消</el-button>
        <el-button type="primary" @click="submitReName">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 粘贴重名处理 -->
    <el-dialog v-dialogDragBox :title="`提示：正在${moveType == 'MOVE' ? '移动' : moveType == 'COPY' ? '复制' : ''}，请稍候...`"
      :visible.sync="pasteTipShow">
      <div class="pasteTip_box">
        <div class="pasteTip_title">提示：存在同名文件！</div>
        <div class="pasteTip_text">您要移动的文件“{{ duplicationName }}”已存在于该路径下，您希望：</div>
        <div class="pasteTip_check">
          <el-checkbox v-model="pasteCheckAll" @change="handlePasteCheckAll">全部应用</el-checkbox>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="danger" @click="handlePaste('cover')">覆 盖</el-button>
        <el-button @click="handlePaste('skip')">跳 过</el-button>
        <el-button type="primary" @click="handlePaste('both')">保留两者</el-button>
      </div>
    </el-dialog>

    <!-- 视频查看 -->
    <el-dialog v-dialogDragBox :title="videoInfo.fileName" :visible.sync="videoShow" width="680px">
      <div class="video_box">
        <video ref="videoPlayer" width="640" height="480" controls :src="videoInfo.url" @play="onPlay"
          @pause="onPause"></video>
      </div>
    </el-dialog>

    <!-- 音频查看 -->
    <el-dialog v-dialogDragBox title="音频播放器" :visible.sync="audioShow" width="680px">
      <div class="video_box">
        <a-player :music="audioInfo[0]" :list="audioInfo" autoplay />

      </div>
    </el-dialog>

    <!-- 文件上传 -->
    <el-dialog v-dialogDragBox title="上传文件" :visible.sync="uploadShow">
      <div class="upload_box">
        <el-form label-width="70px" class="flex">
          <el-form-item label="选择文件" style="flex: 1">
            <el-input v-model="uploadFile" type="text" placeholder="请选择要上传的文件" readonly
              @click.native="checkpath(uploadType)">
              <el-select v-model="uploadType" slot="append" placeholder="请选择" @change="handleUploadType">
                <el-option label="文件" value="file"></el-option>
                <el-option label="文件夹" value="folder"></el-option>
              </el-select>
            </el-input>
            <input ref="uploadfile" type="file" style="display: none;" @change="getInputUpload()" multiple="multiple">
            <input ref="importfolder" type="file" style="display: none;" @change="getInputImport()" multiple="multiple"
              webkitdirectory="true">
          </el-form-item>
          <el-form-item style="width: 150px;">
            <el-button type="primary" @click="checkUploadFile">立即上传</el-button>
          </el-form-item>
        </el-form>
        <div>
          <div style="margin-bottom: 13px;">文件上传进度</div>
          <el-progress :text-inside="true" :stroke-width="17" :percentage="percentComplete"></el-progress>
        </div>
        <div class="uploadActiveFile">
          <div style="margin-bottom: 5px;">
            文件名：<span>{{ uploadActiveFile.name }}</span>
          </div>
          <div style="margin-bottom: 5px;">
            大小：<span>{{ uploadActiveFile.size ? convertFileSize(uploadActiveFile.size) : '0MB' }}</span>
          </div>
          <div>
            上传位置：<span>{{ activeFolder.folderName }}</span>
          </div>
        </div>
        <div class="uploadTips_box" v-if="repeShow">
          <div class="uploadTips_title">提示：存在同名文件！</div>
          <div class="uploadTips_text">您要上传的文件“{{ repeName }}”已存在于该路径下，您希望：</div>
          <div class="pasteTip_check" v-if="uploadType == 'file'">
            <el-checkbox v-model="uploadCheckAll" @change="handleUploadCheckAll">全部应用</el-checkbox>
          </div>
          <div style="margin-top: 10px;" v-if="uploadType == 'file'">
            <el-button size="mini" type="danger" @click="selectFileUpLoadModelEnd('cover')">覆 盖</el-button>
            <el-button size="mini" @click="selectFileUpLoadModelEnd('skip')">跳 过</el-button>
            <el-button size="mini" type="primary" @click="selectFileUpLoadModelEnd('both')">保留两者</el-button>
          </div>
          <div style="margin-top: 10px;" v-if="uploadType == 'folder'">
            <el-button size="mini" type="danger" @click="importAndCover()">覆 盖</el-button>
            <el-button size="mini" @click="abortImport()">取消上传</el-button>
            <el-button size="mini" type="primary" @click="importAndBoth()">保留两者</el-button>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { JSEncrypt } from 'jsencrypt'
import axios from 'axios'
import APlayer from 'vue-aplayer'
import $ from 'jquery';
import { checkPermi } from '@/utils/permission'

export default {
  name: 'BusinessRisk',
  components: {
    APlayer
  },
  data() {
    return {
      // 搜索条件
      queryParams: {},
      // 视图切换
      isFlex: false,
      // 列表数据
      tableData: [],
      // 列表选择
      multipleSelection: [],
      // 全选
      checkedAll: false,
      // 当前文件夹ID
      fid: 'root',
      // 路径域名
      baseUrl: '/netdiskApi',
      // 请求头类型
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      // 悬浮功能窗
      suspensionShow: false,
      // 面包屑
      titleList: [
        {
          folderName: '企业云盘',
          folderId: 'root'
        }
      ],
      // 当前文件夹
      activeFolder: {},
      // 新增文件夹
      addFilesShow: false,
      addFilesForm: {},
      addFilesRules: {
        folderName: [{ required: true, message: '请输入文件夹名称', trigger: 'blur' }]
      },
      // 重命名
      reNameShow: false,
      reNameForm: {},
      reNameRules: {
        name: [{ required: true, message: '请输入文件夹名称', trigger: 'blur' }]
      },
      // 复制/剪切
      moveType: '',
      moveFilesList: [],
      pasteShow: false,
      duplicationFiles: [],
      duplicationName: '',
      strOptMap: {},
      repeIndex: 0,
      pasteTipShow: false,
      pasteCheckAll: false,
      // 查看文件
      pictureViewList: [],
      videoShow: false,
      videoInfo: {},
      audioShow: false,
      audioInfo: [
        {
          title: '',
          artist: '',
          url: '', // 音乐文件链接
          pic: '', // 封面图片链接
          lrc: ''
        }
      ],
      // 上传文件/文件夹
      uploadShow: false,
      uploadType: 'file',
      uploadFile: '',
      fs: '',
      ifs: '',
      percentComplete: 0,
      uploadActiveFile: {},
      repeShow: false,
      repeList: [],
      repeIndexs: 0,
      uploadCheckAll: false,
      repeModelList: [],
      repeName: '',
      usedNum: '0KB',
    }
  },
  watch: {
    multipleSelection(newVal, oldVal) {
      if (newVal.length > 0) {
        this.suspensionShow = true
      } else if (newVal.length == 0) {
        this.suspensionShow = false
      }
    },
    moveFilesList(newVal, oldVal) {
      if (newVal.length > 0) {
        this.pasteShow = true
      } else {
        this.pasteShow = false
      }
    }
  },
  created() {
    this.getServerOS()
    this.getNoticeMD5()
    this.getPublicKey()
  },
  methods: {
    checkPermi,
    // 文件大小处理
    convertFileSize(fileSize) {
      let fileSizeToInt = parseFloat(fileSize / 1048576).toFixed(2) // 将文件体积（MB）数值转化为整型
      if (fileSizeToInt == 0) {
        // 文件体积小于1MB时
        fileSizeToInt = '< 1MB'
      } else if (fileSizeToInt < 1000) {
        // 文件体积大于1MB但小于1000MB时
        fileSizeToInt = fileSizeToInt + 'MB'
      } else if (fileSizeToInt < 1024000) {
        // 文件体积大于1000MB但小于1000GB时
        fileSizeToInt = (fileSizeToInt / 1024).toFixed(2) + 'GB'
      } else {
        // 文件体积大于1000GB
        fileSizeToInt = (fileSizeToInt / 1048576).toFixed(2) + 'TB'
      }
      return fileSizeToInt
    },
    // 获取服务器
    getServerOS() {
      let url = this.baseUrl + '/homeController/getServerOS.ajax'
      axios.post(url).then(res => {
        console.log(res.data)
      })
    },
    // 查询通知消息
    getNoticeMD5() {
      let url = this.baseUrl + '/resourceController/getNoticeMD5.ajax'
      axios.post(url).then(res => {
        console.log(res.data)
      })
    },
    // 获取密钥
    getPublicKey() {
      let accountId = 'zyk-company-' + this.$store.state.user.companyId
      // let accountId = '***********'
      let accountPwd = accountId + '.pwd'

      let url = this.baseUrl + '/homeController/getPublicKey.ajax'
      axios.post(url).then(res => {
        let publicKeyInfo = res.data
        let date = new Date() // 这个是客户浏览器上的当前时间
        let loginInfo = '{accountId:"' + accountId + '",accountPwd:"' + accountPwd + '",time:"' + publicKeyInfo.time + '"}'
        console.log('账户：' + accountId, '密码：' + accountPwd, loginInfo)
        let encrypt = new JSEncrypt() // 加密插件对象
        encrypt.setPublicKey(publicKeyInfo.publicKey) // 设置公钥
        let encrypted = encrypt.encrypt(loginInfo) // 进行加密
        this.doLogin(encrypted)
      })
    },
    // 登录
    doLogin(encrypted) {
      let url = this.baseUrl + '/homeController/doLogin.ajax'
      let formData = new FormData()
      formData.append('encrypted', encrypted)
      axios
        .post(url, formData, {
          headers: this.headers
        })
        .then(res => {
          if (res.data === 'permitlogin') {
            this.getFolderView()
            this.getUsed()
          }
        })
    },
    // 获取使用容量
    getUsed() {
      let url = this.baseUrl + '/homeController/getUsed.ajax'
      axios.get(url).then(res => {
        console.log(res.data)
        if (res.data === '设置无效，请联系管理员') {
          this.usedNum = '0KB'
        } else {
          this.usedNum = res.data
        }

      })
    },
    // 获取文件列表
    getFolderView() {
      const loading = this.$loading({
        lock: true,
        text: '加载中…',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      let url = this.baseUrl + '/homeController/getFolderView.ajax'
      let formData = new FormData()
      formData.append('fid', this.fid)
      axios
        .post(url, formData, {
          headers: this.headers
        })
        .then(res => {
          if (this.fid == 'root') {
            if (res.data.folderList.length == 0) {
              let urls = this.baseUrl + '/homeController/newFolder.ajax'
              let formData_Folder = new FormData()
              formData_Folder.append('parentId', this.fid)
              formData_Folder.append('folderName', 'zyk-company-' + this.$store.state.user.companyId)
              // formData_Folder.append('folderName', '***********')
              formData_Folder.append('folderConstraint', 1)
              axios
                .post(urls, formData_Folder, {
                  headers: this.headers
                })
                .then(res => {
                  if (res.data === 'createFolderSuccess') {
                    this.getFolderView()
                  } else if (res.data === 'noAuthorized') {
                    this.getPublicKey()
                  }
                })
            } else {
              this.fid = res.data.folderList[0].folderId
              this.getFolderView()
            }
          } else {
            this.activeFolder = res.data.folder
            this.tableData = []
            res.data.folderList.forEach(el => {
              el.checked = false
              el.hovered = false
              this.tableData.push(el)
            })
            res.data.fileList.forEach(el => {
              el.checked = false
              el.hovered = false
              let fileSizeToInt = parseFloat(el.fileSize / 1048576).toFixed(2) // 将文件体积（MB）数值转化为整型
              if (fileSizeToInt == 0) {
                // 文件体积小于1MB时
                el.file_size = '< 1MB'
              } else if (fileSizeToInt < 1000) {
                // 文件体积大于1MB但小于1000MB时
                el.file_size = fileSizeToInt + 'MB'
              } else if (fileSizeToInt < 1024000) {
                // 文件体积大于1000MB但小于1000GB时
                el.file_size = (fileSizeToInt / 1024).toFixed(2) + 'GB'
              } else {
                // 文件体积大于1000GB
                el.file_size = (fileSizeToInt / 1048576).toFixed(2) + 'TB'
              }
              this.tableData.push(el)
            })
            this.reCheck()
            this.getUsed()
            loading.close()
            this.getPrePicture()
          }
        })
    },
    getPrePicture() {
      let suffix = this.tableData.filter(item => item.fileName).find(item => this.getSuffix(item.fileName) == 'jpg' || this.getSuffix(item.fileName) == 'jpeg' || this.getSuffix(item.fileName) == 'gif' || this.getSuffix(item.fileName) == 'png' || this.getSuffix(item.fileName) == 'bmp')
      if (suffix) {
        let url_img = this.baseUrl + '/homeController/getPrePicture.ajax'
        let formData_img = new FormData()
        formData_img.append('fileId', suffix.fileId)
        axios
          .post(url_img, formData_img, {
            headers: this.headers
          })
          .then(async res_img => {
            this.previewAxios(res_img.data.pictureViewList, 1)
          })
      }
    },
    async previewAxios(arr, num) {
      let items = arr.splice(0, num);
      await Promise.all(items.map(async els => {
        els.url = this.baseUrl + '/' + els.url
        const response = await axios({
          method: 'get',
          url: els.url,
          responseType: 'blob',
        })
        if (this.tableData.find(item => item.fileName == els.fileName)) {
          this.$set(this.tableData.find(item => item.fileName == els.fileName), 'url', window.URL.createObjectURL(response.data))
        }
      }));
      if (arr.length > 0) {
        await this.previewAxios(arr, 1);
      }
    },
    // 搜索查询
    handleQuery() {
      if (this.queryParams.name) {
        let arr = []
        this.tableData.forEach(el => {
          if (el.folderId) {
            if (el.folderName.includes(this.queryParams.name)) {
              arr.push(el)
            }
          } else if (el.fileId) {
            if (el.fileName.includes(this.queryParams.name)) {
              arr.push(el)
            }
          }
        })
        this.tableData = arr
      } else {
        this.refreshChange()
      }
    },
    // 切换视图
    changeisFlex(bol) {
      this.isFlex = bol
      if (this.isFlex === true) {
        this.$nextTick(() => {
          console.log(this.multipleSelection)
          this.multipleSelection.forEach(el => {
            if (el.folderId) {
              this.$refs.multipleTable.toggleRowSelection(
                this.tableData.find(item => {
                  return item.folderId == el.folderId
                }),
                true
              )
            } else if (el.fileId) {
              this.$refs.multipleTable.toggleRowSelection(
                this.tableData.find(item => {
                  return item.fileId == el.fileId
                }),
                true
              )
            }
          })
        })
      }
    },
    // 刷新
    refreshChange() {
      this.reCheck()
      this.getFolderView()
    },
    // 表格hover进入
    cellMouseEnter(row) {
      row.hovered = true
    },
    // 表格hover离开
    cellMouseLeave(row) {
      row.hovered = false
    },
    // 全选
    handleCheckAll() {
      if (this.checkedAll == true) {
        this.tableData.forEach(el => {
          el.checked = true
          if (this.isFlex) {
            this.$refs.multipleTable.toggleAllSelection(el, true)
          }
        })
        this.multipleSelection = this.tableData
      } else if (this.checkedAll == false) {
        this.tableData.forEach(el => {
          el.checked = false
          if (this.isFlex) {
            this.$refs.multipleTable.toggleAllSelection(
              this.tableData.find(item => {
                return item.checked == true
              }),
              true
            )
          }
        })
        this.multipleSelection = []
      }
    },
    // 表格选择
    handleSelectionChange(val) {
      this.tableData.forEach(el => {
        el.checked = false
      })
      val.forEach(el => {
        el.checked = true
      })
      this.multipleSelection = val
      if (this.multipleSelection.length === this.tableData.length) {
        this.checkedAll = true
      } else {
        this.checkedAll = false
      }
    },
    // 平铺选择
    handleCheck() {
      this.multipleSelection = this.tableData.filter(item => item.checked === true)
      if (this.multipleSelection.length == this.tableData.length) {
        this.checkedAll = true
      } else {
        this.checkedAll = false
      }
    },
    // 重置选择
    reCheck() {
      this.suspensionShow = false
      this.tableData.forEach(el => {
        el.checked = false
      })
      this.multipleSelection = []
      this.checkedAll = false
    },
    // 新增文件夹
    handleAddFiles() {
      this.reCheck()
      this.addFilesForm = {}
      this.addFilesShow = true
    },
    // 提交新增文件夹
    submitAddFiles() {
      this.$refs.addFilesForm.validate(valid => {
        if (valid) {
          let url = this.baseUrl + '/homeController/newFolder.ajax'
          let formData = new FormData()
          formData.append('parentId', this.activeFolder.folderId)
          formData.append('folderName', this.addFilesForm.folderName)
          formData.append('folderConstraint', 1)
          axios
            .post(url, formData, {
              headers: this.headers
            })
            .then(res => {
              if (res.data === 'createFolderSuccess') {
                this.getFolderView()
                this.addFilesShow = false
              }
            })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    // 点击标题
    handleTitle(item) {
      console.log(item)
      this.fid = item.folderId
      this.titleList.splice(this.titleList.findIndex(ite => ite.folderId == item.folderId) + 1, this.titleList.length - this.titleList.findIndex(ite => ite.folderId == item.folderId) + 1)
      this.reCheck()
      this.getFolderView()
    },
    // 点击文件夹
    handleFiles(item) {
      this.titleList.push({
        folderName: item.folderName,
        folderId: item.folderId
      })
      this.fid = item.folderId
      this.reCheck()
      this.getFolderView()
    },
    // 点击文件
    handleFile(item, type) {
      if (type == 'video') {
        let suffix = this.getSuffix(item.fileName)
        if (suffix == 'mp4' || suffix == 'avi') {
          // item.url = 'https://kiftd.ziyouke.net/resourceController/getResource/' + item.fileId
          // this.videoInfo = item
          // this.videoShow = true
          window.open('https://kiftd.ziyouke.net/home.html?phone=zyk-company-' + this.$store.state.user.companyId + '&fileType=mp4&fileIds=' + item.fileId)
        } else {
          this.$message.warning('该文件格式暂时无法预览，请下载后查看')
        }
      } else if (type == 'audio') {
        let url = this.baseUrl + '/homeController/playAudios.ajax'
        let formData = new FormData()
        formData.append('fileId', item.fileId)
        axios.post(url, formData, {
          headers: this.headers
        }).then(res => {
          res.data.as.forEach(el => {
            el.pic = this.baseUrl + '/' + el.cover
            el.url = this.baseUrl + '/' + el.url
            el.title = el.name
          })
          this.audioInfo = res.data.as
          console.log(this.audioInfo)
          this.audioShow = true
        })
      } else if (type == 'pdf') {
        // window.open('https://kiftd.ziyouke.net/resourceController/getResource/' + item.fileId)
        window.open('https://kiftd.ziyouke.net/home.html?phone=zyk-company-' + this.$store.state.user.companyId + '&fileType=pdf&fileIds=' + item.fileId)
        // this.pdfShow = true
      } else if (type == 'otherFile') {
        let suffix = this.getSuffix(item.fileName)
        if (suffix == 'docx') {
          // window.open('https://kiftd.ziyouke.net/resourceController/getWordView/' + item.fileId)
          window.open('https://kiftd.ziyouke.net/home.html?phone=zyk-company-' + this.$store.state.user.companyId + '&fileType=docx&fileIds=' + item.fileId)
        } else if (suffix == 'txt') {
          // window.open('https://kiftd.ziyouke.net/resourceController/getTxtView/' + item.fileId)
          window.open('https://kiftd.ziyouke.net/home.html?phone=zyk-company-' + this.$store.state.user.companyId + '&fileType=txt&fileIds=' + item.fileId)
        } else if (suffix == 'ppt') {
          // window.open('https://kiftd.ziyouke.net/resourceController/getPPTView/' + item.fileId)
          window.open('https://kiftd.ziyouke.net/home.html?phone=zyk-company-' + this.$store.state.user.companyId + '&fileType=ppt&fileIds=' + item.fileId)
        } else {
          this.$message.warning('该文件格式暂时无法预览，请下载后查看')
        }
        // this.otherFile = true
      }
    },
    onPlay() {
      this.$refs.videoPlayer.play();
    },
    onPause() {
      this.$refs.videoPlayer.pause();
    },
    // 获取文件名的后缀名，以小写形式输出
    getSuffix(filename) {
      var index1 = filename.lastIndexOf('.')
      var index2 = filename.length
      var suffix = filename.substring(index1 + 1, index2)
      return suffix.toLowerCase()
    },
    // 文件打包下载
    handleDownloadFiles() {
      let url = this.baseUrl + '/homeController/downloadCheckedFiles.ajax'
      let fileIdList = []
      let folderIdList = []
      this.multipleSelection.forEach(el => {
        if (el.fileId) {
          fileIdList.push(el.fileId)
        }
        if (el.folderId) {
          folderIdList.push(el.folderId)
        }
      })

      let formData = new FormData()
      formData.append('strIdList', JSON.stringify(fileIdList))
      formData.append('strFidList', JSON.stringify(folderIdList))
      axios
        .post(url, formData, {
          headers: this.headers
        })
        .then(res => {
          if (res.data != 'ERROR') {
            var temp = document.createElement('form')
            temp.action = this.baseUrl + '/homeController/downloadCheckedFilesZip.do'
            temp.method = 'post'
            temp.style.display = 'none'
            var sl = document.createElement('input')
            sl.name = 'zipId'
            sl.value = res.data
            temp.appendChild(sl)
            document.body.appendChild(temp)
            temp.submit()
          }
        })
    },
    // 文件下载
    handleDownload(row) {
      if (row.folderId) {
        let url = this.baseUrl + '/homeController/downloadCheckedFiles.ajax'
        let fileIdList = []
        let folderIdList = [row.folderId]
        let formData = new FormData()
        formData.append('strIdList', JSON.stringify(fileIdList))
        formData.append('strFidList', JSON.stringify(folderIdList))
        axios
          .post(url, formData, {
            headers: this.headers
          })
          .then(res => {
            if (res.data != 'ERROR') {
              var temp = document.createElement('form')
              temp.action = this.baseUrl + '/homeController/downloadCheckedFilesZip.do'
              temp.method = 'post'
              temp.style.display = 'none'
              var sl = document.createElement('input')
              sl.name = 'zipId'
              sl.value = res.data
              temp.appendChild(sl)
              document.body.appendChild(temp)
              temp.submit()
            }
          })
      } else if (row.fileId) {
        window.location.href = 'https://kiftd.ziyouke.net/homeController/downloadFile.do?fileId=' + row.fileId
      }
    },
    // 重命名
    handleRename(item) {
      this.reNameForm = {}
      if (item) {
        if (item.fileId) {
          this.$set(this.reNameForm, 'fileId', item.fileId)
          this.$set(this.reNameForm, 'name', item.fileName)
        } else if (item.folderId) {
          this.$set(this.reNameForm, 'folderId', item.folderId)
          this.$set(this.reNameForm, 'name', item.folderName)
        }
        this.reCheck()
      } else {
        if (this.multipleSelection[0].fileId) {
          this.$set(this.reNameForm, 'fileId', this.multipleSelection[0].fileId)
          this.$set(this.reNameForm, 'name', this.multipleSelection[0].fileName)
        } else if (this.multipleSelection[0].folderId) {
          this.$set(this.reNameForm, 'folderId', this.multipleSelection[0].folderId)
          this.$set(this.reNameForm, 'name', this.multipleSelection[0].folderName)
        }
      }
      this.reNameShow = true
    },
    // 提交重命名文件夹
    submitReName() {
      this.$refs.reNameForm.validate(valid => {
        if (valid) {
          let url = ''
          let formData = new FormData()
          if (this.reNameForm.folderId) {
            url = this.baseUrl + '/homeController/renameFolder.ajax'
            formData.append('folderId', this.reNameForm.folderId)
            formData.append('newName', this.reNameForm.name)
            formData.append('folderConstraint', 1)
          } else if (this.reNameForm.fileId) {
            url = this.baseUrl + '/homeController/renameFile.ajax'
            formData.append('fileId', this.reNameForm.fileId)
            formData.append('newFileName', this.reNameForm.name)
          }
          axios
            .post(url, formData, {
              headers: this.headers
            })
            .then(res => {
              if (res.data === 'renameFolderSuccess' || res.data === 'renameFileSuccess') {
                this.getFolderView()
                this.reNameShow = false
              }
            })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    // 删除选中文件夹/文件
    deleteCheck() {
      this.$confirm(`此操作将永久删除选中的${this.multipleSelection.length}项文件, 是否继续?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          let url = this.baseUrl + '/homeController/deleteCheckedFiles.ajax'
          let fileIdList = []
          let folderIdList = []
          this.multipleSelection.forEach(el => {
            if (el.fileId) {
              fileIdList.push(el.fileId)
            }
            if (el.folderId) {
              folderIdList.push(el.folderId)
            }
          })
          let formData = new FormData()
          formData.append('strIdList', JSON.stringify(fileIdList))
          formData.append('strFidList', JSON.stringify(folderIdList))
          axios
            .post(url, formData, {
              headers: this.headers
            })
            .then(res => {
              if (res.data === 'deleteFileSuccess') {
                this.getFolderView()
                this.$message({
                  type: 'success',
                  message: '删除成功!'
                })
              }
            })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    // 删除文件夹/文件
    handleDelete(item) {
      if (item.folderId) {
        this.$confirm(`此操作将永久删除《${item.folderName}》该项文件夹, 是否继续?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            let url = this.baseUrl + '/homeController/deleteFolder.ajax'
            let formData = new FormData()
            formData.append('folderId', item.folderId)
            axios
              .post(url, formData, {
                headers: this.headers
              })
              .then(res => {
                if (res.data === 'deleteFolderSuccess') {
                  this.getFolderView()
                  this.$message({
                    type: 'success',
                    message: '删除成功!'
                  })
                }
              })
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消删除'
            })
          })
      } else if (item.fileId) {
        this.$confirm(`此操作将永久删除《${item.fileName}》该项文件, 是否继续?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            let url = this.baseUrl + '/homeController/deleteFile.ajax'
            let formData = new FormData()
            formData.append('fileId', item.fileId)
            axios
              .post(url, formData, {
                headers: this.headers
              })
              .then(res => {
                if (res.data === 'deleteFileSuccess') {
                  this.getFolderView()
                  this.$message({
                    type: 'success',
                    message: '删除成功!'
                  })
                }
              })
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消删除'
            })
          })
      }
    },
    // 复制/剪切文件/文件夹
    moveCheckFiles(type, row) {
      this.moveType = type
      if (row) {
        this.moveFilesList = [row]
      } else {
        this.moveFilesList = JSON.parse(JSON.stringify(this.multipleSelection))
      }
    },
    // 确认复制/剪切文件
    confirmMoveFiles() {
      let url = this.baseUrl + '/homeController/confirmMoveFiles.ajax'
      let fileIdList = []
      let folderIdList = []
      this.moveFilesList.forEach(el => {
        if (el.fileId) {
          fileIdList.push(el.fileId)
        }
        if (el.folderId) {
          folderIdList.push(el.folderId)
        }
      })
      let formData = new FormData()
      formData.append('strIdList', JSON.stringify(fileIdList))
      formData.append('strFidList', JSON.stringify(folderIdList))
      formData.append('locationpath', this.activeFolder.folderId)
      formData.append('method', this.moveType)
      axios
        .post(url, formData, {
          headers: this.headers
        })
        .then(res => {
          if (res.data.startsWith('duplicationFileName:')) {
            this.duplicationFiles = JSON.parse(res.data.substring(20)).repeFolders.concat(JSON.parse(res.data.substring(20)).repeNodes)
            if (this.duplicationFiles[0].folderId) {
              this.duplicationName = this.duplicationFiles[0].folderName
            }
            if (this.duplicationFiles[0].fileId) {
              this.duplicationName = this.duplicationFiles[0].fileName
            }
            this.strOptMap = {}
            this.repeIndex = 0
            this.pasteTipShow = true
          }
          if (res.data === 'confirmMoveFiles') {
            this.moveFilesEnd()
          }
        })
    },
    // 重名文件操作选择
    handlePasteCheckAll(e) {
      console.log(e, this.pasteCheckAll)
    },
    // 重名文件处理
    handlePaste(type) {
      if (type === 'cover') {
        this.$confirm('此操作将覆盖原文件, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.confirmCoverPaste(type)
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消覆盖'
            })
          })
      } else {
        this.confirmCoverPaste(type)
      }
    },
    // 确认重名文件处理
    confirmCoverPaste(type) {
      if (this.pasteCheckAll) {
        for (let i = this.repeIndex; i < this.duplicationFiles.length; i++) {
          if (this.duplicationFiles[i].fileId) {
            this.strOptMap[this.duplicationFiles[i].fileId] = type
          }
          if (this.duplicationFiles[i].folderId) {
            this.strOptMap[this.duplicationFiles[i].folderId] = type
          }
        }
        this.repeIndex = this.duplicationFiles.length
      } else {
        if (this.repeIndex < this.duplicationFiles.length) {
          if (this.duplicationFiles[this.repeIndex].fileId) {
            this.strOptMap[this.duplicationFiles[this.repeIndex].fileId] = type
          }
          if (this.duplicationFiles[this.repeIndex].folderId) {
            this.strOptMap[this.duplicationFiles[this.repeIndex].folderId] = type
          }
        }
        this.repeIndex++
        if (this.repeIndex < this.duplicationFiles.length) {
          if (this.duplicationFiles[this.repeIndex].folderId) {
            this.duplicationName = this.duplicationFiles[this.repeIndex].folderName
          }
          if (this.duplicationFiles[this.repeIndex].fileId) {
            this.duplicationName = this.duplicationFiles[this.repeIndex].fileName
          }
        }
      }
      if (this.repeIndex == this.duplicationFiles.length) {
        this.moveFilesEnd()
      }
    },
    // 提交复制/剪切文件
    moveFilesEnd() {
      let url = this.baseUrl + '/homeController/moveCheckedFiles.ajax'
      let fileIdList = []
      let folderIdList = []
      this.moveFilesList.forEach(el => {
        if (el.fileId) {
          fileIdList.push(el.fileId)
        }
        if (el.folderId) {
          folderIdList.push(el.folderId)
        }
      })
      let formData = new FormData()
      formData.append('strIdList', JSON.stringify(fileIdList))
      formData.append('strFidList', JSON.stringify(folderIdList))
      formData.append('strOptMap', JSON.stringify(this.strOptMap))
      formData.append('locationpath', this.activeFolder.folderId)
      formData.append('method', this.moveType)
      axios
        .post(url, formData, {
          headers: this.headers
        })
        .then(res => {
          if (res.data === 'moveFilesSuccess') {
            this.moveFilesList = []
            this.pasteCheckAll = false
            this.getFolderView()
            this.pasteTipShow = false
          }
        })
    },
    // 上传文件/文件夹
    handleUpload() {
      this.uploadType = 'file'
      this.uploadFile = ''
      this.fs = ''
      this.ifs = ''
      this.percentComplete = 0
      this.uploadActiveFile = {}
      this.uploadShow = true
    },
    // 选择上传类型
    handleUploadType() {
      this.uploadFile = ''
      this.fs = ''
      this.ifs = ''
      this.percentComplete = 0
      this.uploadActiveFile = {}
    },
    // 点击上传文本框
    checkpath(type) {
      if (type === 'file') {
        this.$refs.uploadfile.click()
      } else if (type === 'folder') {
        this.$refs.importfolder.click()
      }
    },
    // 选择文件
    getInputUpload() {
      this.fs = $(this.$refs.uploadfile).get(0).files
      this.showfilepath()
    },
    // 选择文件夹
    getInputImport() {
      this.ifs = $(this.$refs.importfolder)[0].files;
      this.showfilepath()
    },
    // 回显上传名称
    showfilepath() {
      if (this.uploadType == 'file') {
        let filename = "";
        for (let i = 0; i < this.fs.length; i++) {
          filename = filename + this.fs[i].name;
          if (i < (this.fs.length - 1)) {
            filename = filename + "、";
          }
        }
        this.uploadFile = filename
      } else if (this.uploadType == 'folder') {
        if (this.ifs.length > 0) {
          this.uploadFile = this.ifs[0].webkitRelativePath.substring(0,
            this.ifs[0].webkitRelativePath.indexOf("/"));
        }
      }
    },
    // 检查是否可以上传
    checkUploadFile() {
      if (this.uploadType == 'file') {
        let url = this.baseUrl + '/homeController/checkUploadFile.ajax'
        let filenames = new Array();
        let maxSize = 0;
        let maxFileIndex = 0;
        for (let i = 0; i < this.fs.length; i++) {
          filenames[i] = this.fs[i].name;
          if (this.fs[i].size > maxSize) {
            maxSize = this.fs[i].size;
            maxFileIndex = i;
          }
        }
        let formData = new FormData()
        formData.append('folderId', this.activeFolder.folderId)
        formData.append('namelist', JSON.stringify(filenames))
        formData.append('maxSize', maxSize)
        formData.append('maxFileIndex', maxFileIndex)
        axios.post(url, formData, {
          headers: this.headers
        }).then(res => {
          if (res.data.checkResult == "hasExistsNames") {
            this.repeList = res.data.pereFileNameList;
            this.repeIndexs = 0;
            this.selectFileUpLoadModelStart()
          } else if (res.data.checkResult == "permitUpload") {
            this.doupload(1);
          }
        })
      } else if (this.uploadType == 'folder') {
        if (this.ifs != null && this.ifs.length > 0) {// 必须选中文件
          let url = this.baseUrl + '/homeController/checkImportFolder.ajax'
          let maxSize = 0;
          let maxFileIndex = 0;
          // 找出最大体积的文件以便服务器进行效验
          for (let i = 0; i < this.ifs.length; i++) {
            if (this.ifs[i].size > maxSize) {
              maxSize = this.ifs[i].size;
              maxFileIndex = i;
            }
          }
          let formData = new FormData()
          formData.append('folderName', this.uploadFile)
          formData.append('maxSize', maxSize)
          formData.append('folderId', this.activeFolder.folderId)
          axios.post(url, formData, {
            headers: this.headers
          }).then(res => {
            if (res.data.result == 'permitUpload') {
              this.iteratorImport(0)
            } else if (res.data.result == 'repeatFolder_coverOrBoth') {
              this.repeName = this.uploadFile
              this.repeShow = true
            }
          })
        }
      }

    },
    // 显示重命名提醒
    selectFileUpLoadModelStart() {
      this.repeName = this.repeList[this.repeIndexs]
      this.repeShow = true
    },
    handleUploadCheckAll() { },
    // 上传重名文件处理
    selectFileUpLoadModelEnd(t) {
      if (this.repeModelList == null) {
        this.repeModelList = {};
      }
      if (t === 'cover') {
        this.$confirm('此操作将覆盖原文件, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.repeModelList[this.repeList[this.repeIndexs]] = t;
          if (this.uploadCheckAll) {
            for (let i = this.repeIndexs; i < this.repeList.length; i++) {
              this.repeModelList[this.repeList[i]] = t;
            }
            this.doupload(1);
          } else {
            this.repeIndexs++;
            if (this.repeIndexs < this.repeList.length) {
              this.selectFileUpLoadModelStart();
            } else {
              this.doupload(1);
            }
          }
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消覆盖'
          })
        })

      } else {
        this.repeModelList[this.repeList[this.repeIndexs]] = t;
        if (this.uploadCheckAll) {
          for (let i = this.repeIndexs; i < this.repeList.length; i++) {
            this.repeModelList[this.repeList[i]] = t;
          }
          this.doupload(1);
        } else {
          this.repeIndexs++;
          if (this.repeIndexs < this.repeList.length) {
            this.selectFileUpLoadModelStart();
          } else {
            this.doupload(1);
          }
        }
      }
    },
    // 开始上传文件
    doupload(count) {
      let that = this
      that.percentComplete = 0
      let fcount = that.fs.length;
      that.uploadActiveFile = that.fs[count - 1];// 获取要上传的文件
      if (that.uploadActiveFile != null) {
        let fname = that.uploadActiveFile.name;
        let xhr = new XMLHttpRequest();// 这东西类似于servlet里面的request

        let fd = new FormData();// 用于封装文件数据的对象
        fd.append("file", that.uploadActiveFile);// 将文件对象添加到FormData对象中，字段名为this.uploadActiveFile
        fd.append("fname", fname);
        fd.append("folderId", that.activeFolder.folderId);
        if (that.repeModelList != null && that.repeModelList[fname] != null) {
          if (that.repeModelList[fname] == 'skip') {
            if (count < fcount) {
              that.doupload(count + 1);
              return;
            } else {
              that.getFolderView()
              that.repeShow = false
              that.uploadShow = false
              return;
            }
          }
          fd.append("repeType", that.repeModelList[fname]);
        }
        xhr.open("POST", that.baseUrl + "/homeController/douploadFile.ajax", true);// 上传目标

        xhr.upload.addEventListener("progress", that.uploadProgress, false);// 这个是对上传进度的监听
        // 上面的三个参数分别是：事件名（指定名称）、回调函数、是否冒泡（一般是false即可）

        xhr.send(fd);// 上传FormData对象

        // 上传结束后执行的回调函数
        xhr.onloadend = function () {
          if (xhr.status === 200) {
            // TODO 上传成功
            let result = xhr.responseText;
            if (result == "uploadsuccess") {
              if (count < fcount) {
                that.doupload(count + 1);
              } else {
                that.getFolderView()
                that.repeShow = false
                that.uploadShow = false
              }
            }
          }
        };
      }
    },
    // 覆盖文件夹
    importAndCover() {
      this.$confirm('此操作将覆盖原文件夹, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let url = this.baseUrl + '/homeController/deleteFolderByName.ajax'
        let formData = new FormData()
        formData.append('parentId', this.activeFolder.folderId)
        formData.append('folderName', this.uploadFile)
        axios.post(url, formData, {
          headers: this.headers
        }).then(res => {
          if (res.data == 'deleteSuccess') {
            this.iteratorImport(0);// 若覆盖成功，则开始上传
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消覆盖'
        })
      })
    },
    // 保留两者文件夹
    importAndBoth() {
      let url = this.baseUrl + '/homeController/createNewFolderByName.ajax'
      let formData = new FormData()
      formData.append('parentId', this.activeFolder.folderId)
      formData.append('folderName', this.uploadFile)
      formData.append('folderConstraint', 1)
      axios.post(url, formData, {
        headers: this.headers
      }).then(res => {
        if (res.data.result == 'success') {
          this.iteratorImport(0, res.data.newName);
        }
      })
    },
    // 取消文件夹上传
    abortImport() {
      if (isImporting) {
        isImporting = false;
        if (xhr != null) {
          xhr.abort();
        }
      }
      $('#importFolderModal').modal('hide');
      showFolderView(locationpath);
    },
    // 开始上传文件夹
    iteratorImport(i, newFolderName) {
      let that = this
      that.percentComplete = 0
      that.uploadActiveFile = that.ifs[i];// 获取要上传的文件
      let fcount = that.ifs.length;
      if (that.uploadActiveFile != null) {
        let fname = that.uploadActiveFile.webkitRelativePath;
        let xhr = new XMLHttpRequest();// 这东西类似于servlet里面的request

        let fd = new FormData();// 用于封装文件数据的对象

        fd.append("file", that.uploadActiveFile);// 将文件对象添加到FormData对象中，字段名为uploadfile
        fd.append("folderId", that.activeFolder.folderId);
        fd.append("folderConstraint", 1);
        fd.append("originalFileName", fname);
        if (!!newFolderName) {
          fd.append("newFolderName", newFolderName);
        }
        xhr.open("POST", that.baseUrl + "/homeController/doImportFolder.ajax", true);// 上传目标

        xhr.upload.addEventListener("progress", that.uploadProgress, false);// 这个是对上传进度的监听
        // 上面的三个参数分别是：事件名（指定名称）、回调函数、是否冒泡（一般是false即可）

        xhr.send(fd);// 上传FormData对象

        // 上传结束后执行的回调函数
        xhr.onloadend = function () {
          if (xhr.status === 200) {
            // TODO 上传成功
            let result = xhr.responseText;
            if (result == "uploadsuccess") {
              let ni = i + 1;
              if (ni < fcount) {
                that.iteratorImport(ni, newFolderName);
              } else {
                that.getFolderView()
                that.uploadShow = false
              }
            }
          }
        };
      }
    },
    // 进度条
    uploadProgress(evt) {
      if (evt.lengthComputable) {
        // evt.loaded：文件上传的大小 evt.total：文件总的大小
        this.percentComplete = Math.round((evt.loaded) * 100 / evt.total);
      }
    },
  }
}
</script>

<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';

.netdisk_breadcrumb_box {
  padding: 10px 20px;

  .blue {
    color: #2e73f3;
    font-weight: 400;
    font-size: 12px;
  }

  .gray {
    color: #999999;
    font-weight: 500;
    font-size: 12px;
  }
}

.netdisk_search {
  background: #ffffff;
  box-shadow: 0px 1px 1px 0px rgba(0, 0, 0, 0.06);
  border-radius: 0px 0px 0px 0px;
  justify-content: space-between;
  align-items: center;

  .netdisk_add_files {
    border-radius: 5px 5px 5px 5px;
    border: 1px solid #2e73f3;
    background-color: #fff;
    color: #2e73f3;
  }

  .netdisk_search_right {
    align-items: center;

    .netdisk_paste_box {
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 30px;

      .netdisk_paste_img {
        width: 20px;
        height: 20px;
        margin-right: 5px;
      }

      .netdisk_paste_text {
        font-size: 12px;
      }
    }

    .netdisk_refresh_img {
      width: 20px;
      height: 20px;
      margin-right: 30px;
    }

    .netdisk_btn_switch {
      width: 70px;
      height: 36px;
      background: #e5e5e5;
      box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.05);
      border-radius: 5px 5px 5px 5px;
      border: 1px solid #d9d9d9;
      align-items: center;

      div {
        width: 34px;
        height: 34px;
        padding: 7px;

        img {
          width: 20px;
          height: 20px;
        }
      }

      .active {
        width: 34px;
        height: 34px;
        background: #ffffff;
        box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.05);
        border-radius: 5px 5px 5px 5px;
      }
    }
  }
}

.netdisk_concent {
  padding: 20px;

  ::v-deep .el-checkbox__inner {
    border-radius: 50%;
  }

  .netdisk_check_box {
    margin-bottom: 16px;
    font-size: 14px;
    color: #666666;
  }

  .netdisk_list_box {
    .netdisk_list_fileName {
      display: flex;
      align-items: center;

      img,
      .el-image {
        width: 52px;
        height: 52px;
        margin-right: 50px;
      }

      span {
        font-weight: 500;
        font-size: 14px;
        color: #333333;
      }
    }

    .netdisk_list_btn {
      justify-content: right;

      div {
        cursor: pointer;
        display: flex;
        align-items: center;
        margin-left: 50px;

        img {
          margin-right: 13px;
          width: 20px;
          height: 20px;
        }

        span {
          font-weight: 500;
          font-size: 12px;
          color: #333333;
        }
      }
    }
  }

  .netdisk_tile_box {
    display: flex;
    flex-wrap: wrap;

    .netdisk_tile_item {
      cursor: pointer;
      // width: 120px;
      height: 120px;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 15px 34px 10px;
      position: relative;
      text-align: center;

      img,
      .el-image {
        width: 52px;
        height: 52px;
        margin-bottom: 7px;
      }

      div {
        width: 70px;
        font-weight: 500;
        font-size: 14px;
        color: #333333;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        margin-bottom: 5px;
      }

      span {
        font-weight: 400;
        font-size: 10px;
        color: #666666;
      }

      .netdisk_tile_check {
        position: absolute;
        top: 5px;
        right: 5px;
      }
    }
  }
}

.suspension_box {
  position: fixed;
  z-index: 1000;
  bottom: 165px;
  right: 686px;
  width: 345px;
  height: 69px;
  background: #505153;
  box-shadow: 0px 1px 15px 0px rgba(0, 0, 0, 0.25);
  border-radius: 10px 10px 10px 10px;
  display: flex;
  align-items: center;
  justify-content: space-around;

  .suspension_item {
    cursor: pointer;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .img {
      width: 20px;
      height: 20px;
      margin-bottom: 5px;
    }

    .text {
      font-weight: 500;
      font-size: 12px;
      color: #ffffff;
    }
  }
}

.pasteTip_box {
  background: #ebedf3;
  border-radius: 10px 10px 10px 10px;
  padding: 15px 30px;

  .pasteTip_title {
    font-size: 16px;
    color: #f56c6c;
    font-weight: 550;
    margin-bottom: 20px;
  }

  .pasteTip_text {
    font-size: 14px;
    color: #f56c6c;
    margin-bottom: 10px;
  }
}

.upload_box {
  padding: 20px;

  ::v-deep .el-input-group__append {
    width: 100px;
  }

  .uploadActiveFile {
    height: 98px;
    background: #F0F1F2;
    border-radius: 10px 10px 10px 10px;
    padding: 15px;
    margin-top: 32px;
  }

  .uploadTips_box {
    background: #ebedf3;
    border-radius: 10px 10px 10px 10px;
    padding: 15px 30px;
    margin-top: 30px;
    background: rgba(255, 0, 0, 0.25);

    .uploadTips_title {
      font-size: 16px;
      color: #f56c6c;
      font-weight: 550;
      margin-bottom: 20px;
    }

    .uploadTips_text {
      font-size: 14px;
      color: #f56c6c;
      margin-bottom: 10px;
    }
  }
}

::v-deep .el-dialog__header {
  background: #EEF0F8;
}
</style>
