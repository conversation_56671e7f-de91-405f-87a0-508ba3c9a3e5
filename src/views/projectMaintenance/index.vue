<template>
  <div class="newBox bgcf9 vh-85">
    <!-- 搜索 -->
    <div class="custom-search" style="padding-top: 18px">
      <div class="flex" :style="{ justifyContent: showSearch ? 'space-between' : 'flex-end' }">
        <el-form :model="queryParams" ref="queryForm" size="small" v-show="showSearch" :inline="true">
          <el-form-item label="项目名称" prop="projectName">
            <el-input v-model="queryParams.projectName" placeholder="请输入项目名称" clearable @keyup.enter.native="handleQuery" />
          </el-form-item>
          <el-form-item label="报备人" prop="createBy">
            <el-input v-model="queryParams.createBy" placeholder="请输入报备人" clearable @keyup.enter.native="handleQuery" />
          </el-form-item>
          <el-form-item label="部门" prop="deptId" v-hasPermi="['system:dept:query']">
            <el-select v-model="queryParams.deptId" placeholder="请选择部门" @change="handleQuery">
              <el-option v-for="item in deptOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="省份地区" prop="projectArea">
            <el-cascader size="small" :options="pcTextArr" v-model="queryParams.projectArea" :props="{ checkStrictly: true }" clearable placeholder="请选择省份地区" @change="cascaderChange('search')"></el-cascader>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" style="background-color: #2e73f3; border-color: #2e73f3" size="small" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
            <button type="button" class="custom-search-add pointer" @click="handleAdd">
              <i class="el-icon-plus"></i>
              新增项目报备
            </button>
          </el-form-item>
        </el-form>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
      </div>
    </div>
    <!-- 状态 -->
    <div class="classify flex">
      <div class="classify-item" :class="{ active: !queryParams.status }" @click="handleChangeType('')">所有项目</div>
      <div class="classify-item" :class="{ active: item.value === queryParams.status }" v-for="item in dict.type.sales_slip_status" :key="item.value" @click="handleChangeType(item)">{{ item.label }}</div>
    </div>
    <!-- 列表 -->
    <div class="p20" v-if="total > 0">
      <el-table v-loading="loading" stripe :data="list" row-key="id" style="width: 100%" class="custom-table">
        <el-table-column align="center" type="index" label="序号" v-if="columns[0].visible"></el-table-column>
        <el-table-column align="center" prop="projectName" label="项目名称" show-overflow-tooltip v-if="columns[1].visible"></el-table-column>
        <el-table-column align="center" prop="deptName" label="部门" show-overflow-tooltip :filters="deptNameArr" :filter-method="deptNameFilter" v-if="columns[2].visible"></el-table-column>
        <el-table-column align="center" prop="createBy" label="报备人" show-overflow-tooltip :filters="createByArr" :filter-method="createByFilter" v-if="columns[3].visible"></el-table-column>
        <el-table-column align="center" label="项目地区" show-overflow-tooltip v-if="columns[4].visible">
          <template slot-scope="scope">
            <div>{{ scope.row.province + scope.row.region }}</div>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="address" label="地址" show-overflow-tooltip v-if="columns[5].visible"></el-table-column>
        <el-table-column align="center" prop="contact" label="联系人" show-overflow-tooltip v-if="columns[6].visible"></el-table-column>
        <el-table-column align="center" prop="phone" label="联系电话" show-overflow-tooltip v-if="columns[7].visible"></el-table-column>
        <el-table-column align="center" prop="createTime" label="报备时间" show-overflow-tooltip v-if="columns[8].visible"></el-table-column>
        <el-table-column align="center" prop="status" label="状态" show-overflow-tooltip v-if="columns[9].visible">
          <template slot-scope="scope">
            <el-tooltip :content="scope.row.remark" :disabled="scope.row.status != '3' || !scope.row.remark">
              <dict-tag :options="dict.type.sales_slip_status" :value="scope.row.status" />
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="attachment" label="附件" show-overflow-tooltip v-if="columns[10].visible">
          <template slot-scope="scope">
            <div v-if="scope.row.attachment" @click="handleExcelPreview(scope.row.attachment)" style="color: #2e73f3; cursor: pointer">查看详情 ></div>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="prop" label="操作" :width="getActionColumnWidth()">
          <template slot-scope="{ row }">
            <!-- 常用按钮 -->
            <template v-for="action in getCommonActions(row)">
              <el-button :key="action.key" :type="action.type" :plain="action.plain" @click="action.handler(row)" :size="action.size" v-if="action.show" style="margin-right: 10px">{{ action.label }}</el-button>
            </template>
            <!-- 更多操作 -->
            <el-popover trigger="hover" v-if="hasMoreActions(row)">
              <el-button type="primary" plain size="mini" slot="reference">
                更多操作
                <i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <div class="popover-button">
                <template v-for="action in getAllActions(row)">
                  <div :key="action.key" class="popover-button-item" v-if="action.show">
                    <el-button :type="action.type" :plain="action.plain" @click="action.handler(row)" :size="action.size">{{ action.label }}</el-button>
                    <i class="popover-button-icon" :class="[isCommonAction(action.key) ? 'el-icon-star-on' : 'el-icon-star-off', { active: isCommonAction(action.key) }]" @click="toggleCommonAction(action.key)" :title="isCommonAction(action.key) ? '取消常用' : '设置为常用'"></i>
                  </div>
                </template>
              </div>
            </el-popover>
          </template>
        </el-table-column>
      </el-table>
      <div class="custom-pagination">
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
      </div>
    </div>
    <el-empty v-else />

    <!-- 添加 -->
    <el-dialog v-dialogDragBox :title="title" :visible.sync="open" width="1000px" class="custom-dialog">
      <div class="p20">
        <el-alert title="项目名称和省份地区不可同时与已报备的项目重复" type="warning" show-icon style="margin-bottom: 10px" :closable="false"></el-alert>
        <el-form ref="form" :model="form" :rules="rules" label-width="6em">
          <el-row>
            <el-col :span="12">
              <el-form-item label="项目名称" prop="projectName">
                <el-input v-model="form.projectName" placeholder="请输入项目名称" @change="projectCheck"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="省份地区" prop="projectArea">
                <el-cascader size="large" :options="pcTextArr" v-model="form.projectArea" placeholder="请选择省份地区" @change="cascaderChange('projectArea')"></el-cascader>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="联系人" prop="contact">
                <el-input v-model="form.contact" placeholder="请输入项目联系人"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="联系电话" prop="phone">
                <el-input v-model="form.phone" placeholder="请输入项目联系人电话"></el-input>
              </el-form-item>
            </el-col>
            <!-- <el-col :span="12">
                <el-form-item label="省份地区" prop="province">
                    <el-cascader size="large" :options="pcTextArr" v-model="form.province" placeholder="请选择省份地区">
                    </el-cascader>
                </el-form-item>
            </el-col> -->
            <el-col :span="24">
              <el-form-item label="详细地址" prop="address">
                <el-input v-model="form.address" placeholder="请输入详细地址"></el-input>
              </el-form-item>
            </el-col>
            <!-- 备注 -->
            <el-col :span="24">
              <el-form-item label="备注" prop="notes">
                <el-input v-model="form.notes" type="textarea" :autosize="{ minRows: 3, maxRows: 5 }" resize="none" placeholder="请输入备注"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="附件" prop="attachment">
                <file-upload isShowTip :fileSize="100" v-model="form.attachment" :limit="1" :file-type="fileType" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <el-button @click="open = false" size="medium" style="width: 200px">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" size="medium" style="width: 200px" :disabled="projectBtn">{{ form.id ? '修改项目报备' : '提交项目报备' }}</el-button>
      </div>
    </el-dialog>
    <!-- 详情 -->
    <el-dialog v-dialogDragBox title="查看项目详情" :visible.sync="viewOpen" width="1000px" class="custom-dialog">
      <el-descriptions title="项目信息" class="project_detail">
        <el-descriptions-item label="项目名称">{{ info.projectName }}</el-descriptions-item>
        <el-descriptions-item label="联系人">{{ info.contact }}</el-descriptions-item>
        <el-descriptions-item label="联系电话">{{ info.phone }}</el-descriptions-item>
        <el-descriptions-item label="省份地区">{{ info.province + info.region }}</el-descriptions-item>
        <el-descriptions-item label="项目地址">{{ info.address }}</el-descriptions-item>
        <el-descriptions-item label="项目状态">
          <dict-tag :options="dict.type.sales_slip_status" :value="info.status" />
        </el-descriptions-item>
        <el-descriptions-item label="备注" :span="3">{{ info.notes }}</el-descriptions-item>
        <el-descriptions-item label="失败原因" v-if="info.status == '3' && info.remark" :span="3">{{ info.remark }}</el-descriptions-item>
      </el-descriptions>
      <div class="reporting_detail">
        <div class="reporting_detail_title">报备信息</div>
        <el-divider></el-divider>
        <div class="reporting_detail_list">
          <div>报备人：{{ info.createBy }}</div>
          <div>部门：{{ info.deptName }}</div>
          <div>报备时间：{{ info.createTime }}</div>
        </div>
        <el-divider></el-divider>
      </div>
    </el-dialog>
    <!-- 更改状态 -->
    <el-dialog v-dialogDragBox title="更改项目状态" :visible.sync="statusOpen" class="custom-dialog" width="500px">
      <div class="p20">
        <el-form ref="statusForm" :model="statusForm" :rules="statusRules" label-width="6em">
          <el-form-item label="项目状态" prop="status">
            <el-select v-model="statusForm.status" placeholder="请选择项目状态" clearable>
              <el-option v-for="dict in dict.type.sales_slip_status" :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="失败原因" prop="remark" v-if="statusForm.status === '3'">
            <el-input type="textarea" v-model="statusForm.remark" placeholder="请输入失败原因" resize="none" :autosize="{ minRows: 3, maxRows: 5 }"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer">
        <el-button @click="statusOpen = false" size="medium">取 消</el-button>
        <el-button type="primary" @click="statusSubmit" size="medium">确 定</el-button>
      </div>
    </el-dialog>

    <!--预览Excel-->
    <el-dialog v-dialogDragBox title="预览" :visible.sync="excelOpen" width="1150px" class="custom-dialog" append-to-body>
      <div class="page-pdf">
        <vue-office-excel :src="excelUrl" style="height: 75vh" />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { projectList, projectAdd, projectEdit, projectDel, statusEdit, projectCheck } from '@/api/projectMaintenance'
import { pcTextArr } from 'element-china-area-data'
import { listDept } from '@/api/system/dept'
import { checkPermi } from '@/utils/permission'
import VueOfficeExcel from '@vue-office/excel'
import '@vue-office/excel/lib/index.css'

export default {
  name: 'ProjectMaintenance',
  components: { VueOfficeExcel },
  dicts: ['sales_slip_status'],
  data() {
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectName: '',
        status: '',
        createBy: '',
        deptId: ''
      },
      total: 0,
      list: [],
      loading: true,
      deptOptions: [],
      title: '',
      open: false,
      pcTextArr,
      fileType: ['xlsx', 'xls'],
      form: {},
      rules: {
        projectName: [{ required: true, message: '请输入项目名称', trigger: 'blur' }],
        projectArea: [{ required: true, message: '请选择省份地区', trigger: 'change' }],
        contact: [{ required: true, message: '请输入项目联系人', trigger: 'blur' }],
        phone: [{ required: true, message: '请输入项目联系人电话', trigger: 'blur' }],
        address: [{ required: true, message: '请输入详细地址', trigger: 'blur' }]
      },
      checkProvince: '',
      checkRegion: '',
      checkProject: '',
      viewOpen: false,
      info: {},
      statusOpen: false,
      statusForm: {},
      statusRules: {
        status: [{ required: true, message: '请选择项目状态', trigger: 'change' }]
      },
      projectBtn: false,
      deptNameArr: [],
      createByArr: [],
      excelOpen: false,
      excelUrl: '',
      // 列表显隐
      showSearch: true,
      // 列信息
      columns: [
        { key: 0, label: `序号`, visible: true },
        { key: 1, label: `项目名称`, visible: true },
        { key: 2, label: `部门`, visible: true },
        { key: 3, label: `报备人`, visible: true },
        { key: 4, label: `项目地区`, visible: true },
        { key: 5, label: `地址`, visible: true },
        { key: 6, label: `联系人`, visible: true },
        { key: 7, label: `联系电话`, visible: true },
        { key: 8, label: `报备时间`, visible: true },
        { key: 9, label: `状态`, visible: true },
        { key: 10, label: `附件`, visible: true }
      ],
      // 常用按钮配置
      commonActions: []
    }
  },
  created() {
    this.getList()
    if (checkPermi(['system:dept:query'])) this.getDept()
    // 加载常用按钮配置
    const commonActions = localStorage.getItem(this.userId + '.projectMaintenanceCommonActions')
    if (commonActions) this.commonActions = JSON.parse(commonActions)
  },
  computed: {
    // 获取当前用户ID
    userId() {
      return this.$store.getters.info.userId
    }
  },
  methods: {
    // 获取部门
    getDept() {
      listDept().then(res => {
        const { code, data } = res
        if (code === 200) {
          const arr = data.filter(item => item.parentId !== 0)
          this.deptOptions = arr.map(item => {
            return { label: item.deptName, value: item.deptId }
          })
        }
      })
    },
    // 列表
    getList() {
      this.loading = true
      projectList(this.queryParams).then(res => {
        const { code, msg, rows, total } = res
        if (code === 200) {
          const deptNameArr = Array.from(new Set(rows.map(item => item.deptName)))
          this.deptNameArr = deptNameArr.map(item => {
            return { text: item, value: item }
          })
          const createByArr = Array.from(new Set(rows.map(item => item.createBy)))
          this.createByArr = createByArr.map(item => {
            return { text: item, value: item }
          })
          this.list = rows
          this.total = total
          this.loading = false
        } else this.$message.error(msg)
      })
    },
    // 部门筛选
    deptNameFilter(value, row) {
      return row.deptName === value
    },
    // 报备人筛选
    createByFilter(value, row) {
      return row.createBy === value
    },
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 重置搜索
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        projectName: '',
        status: ''
      }
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 改变分类
    handleChangeType(item) {
      this.queryParams.status = item.value
      this.handleQuery()
    },
    reset() {
      this.form = {
        address: undefined,
        contact: undefined,
        phone: undefined,
        projectName: undefined,
        province: undefined,
        region: undefined,
        projectArea: undefined,
        notes: undefined
        // salesSlipId: undefined,
      }
      this.resetForm('form')
    },
    // 添加
    handleAdd() {
      this.reset()
      this.checkProvince = ''
      this.checkRegion = ''
      this.checkProject = ''
      this.title = '新增项目报备'
      this.open = true
      this.projectBtn = false
    },
    // 修改
    handleUpdate(row) {
      this.reset()
      this.form = { ...row }
      this.form.projectArea = [this.form.province, this.form.region]
      this.checkProject = this.form.projectName
      this.checkProvince = ''
      this.checkRegion = ''
      this.title = '修改项目报备'
      this.open = true
      this.projectBtn = false
    },
    // 查看详情
    handleView(row) {
      this.info = { ...row }
      this.viewOpen = true
    },
    // 删除
    // prettier-ignore
    handleDelete(row) {
      const salesSlipId = row.id
      let title = row.projectName
      this.$confirm(`是否删除${title}此项目?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        projectDel({ salesSlipId }).then(res => {
          if (res.code === 200) {
            this.$message.success('删除成功')
            this.getList()
          } else {
            this.$message.error(res.msg)
          }
        })
      }).catch(() => {
      })
    },
    // 调整状态
    handleStatus(row) {
      this.statusForm = {
        salesSlipId: row.id,
        status: row.status.toString(),
        remark: row.remark || undefined
      }
      this.resetForm('statusForm')
      this.statusOpen = true
    },
    // 项目名称校验
    projectCheck() {
      projectCheck({
        projectName: this.form.projectName
      }).then(res => {
        if (res.code == 200) {
          if (res.data && this.checkProject != res.data.projectName) {
            this.checkProvince = res.data.province
            this.checkRegion = res.data.region
            this.cascaderChange('projectArea')
          } else {
            this.checkProvince = ''
            this.checkRegion = ''
            this.projectBtn = false
          }
        }
      })
    },
    // 级联选择
    cascaderChange(type) {
      if (type == 'projectArea') {
        if (this.form.projectArea) {
          if (this.checkProvince === this.form.projectArea[0] && this.checkRegion === this.form.projectArea[1]) {
            this.$message.error('该项目已存在')
            this.projectBtn = true
          } else {
            this.projectBtn = false
          }
          this.form.province = this.form.projectArea[0]
          this.form.region = this.form.projectArea[1]
        }
      } else if (type == 'search') {
        this.queryParams.province = (this.queryParams.projectArea && this.queryParams.projectArea[0]) || ''
        this.queryParams.region = (this.queryParams.projectArea && this.queryParams.projectArea[1]) || ''
        this.handleQuery()
      }
    },
    // 提交
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          if (!!this.form.id) {
            this.form.salesSlipId = this.form.id
            projectEdit(this.form).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('修改成功')
                this.getList()
                this.open = false
              } else this.$message.error(msg)
            })
          } else {
            projectAdd(this.form).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('新增成功')
                this.getList()
                this.open = false
              } else this.$message.error(msg)
            })
          }
        }
      })
    },
    // 更改状态
    statusSubmit() {
      this.$refs.statusForm.validate(valid => {
        if (valid) {
          statusEdit(this.statusForm).then(res => {
            const { code, msg } = res
            if (code === 200) {
              this.$message.success('更改成功')
              this.getList()
              this.statusOpen = false
            } else this.$message.error(msg)
          })
        }
      })
    },
    // 预览Excel
    handleExcelPreview(item) {
      this.excelUrl = this.imgPath + item
      this.excelOpen = true
    },
    // 获取所有操作按钮配置
    getAllActions(row) {
      return [
        {
          key: 'view',
          label: '查看详情',
          type: 'info',
          plain: true,
          size: 'mini',
          show: true,
          handler: this.handleView
        },
        {
          key: 'update',
          label: '修改项目',
          type: 'warning',
          plain: true,
          size: 'mini',
          show: true,
          handler: this.handleUpdate
        },
        {
          key: 'status',
          label: '更改状态',
          type: 'primary',
          plain: true,
          size: 'mini',
          show: true,
          handler: this.handleStatus
        },
        {
          key: 'delete',
          label: '删除项目',
          type: 'danger',
          plain: true,
          size: 'mini',
          show: true,
          handler: this.handleDelete
        }
      ]
    },
    // 获取常用按钮
    getCommonActions(row) {
      const allActions = this.getAllActions(row).filter(action => action.show)
      const commonActions = allActions.filter(action => this.isCommonAction(action.key))
      // 如果没有设置常用按钮，默认显示查看详情
      if (commonActions.length === 0) {
        const viewAction = allActions.find(action => action.key === 'view')
        return viewAction ? [viewAction] : []
      }
      // 常用按钮不限制数量
      return commonActions
    },
    // 判断是否为常用按钮
    isCommonAction(actionKey) {
      return this.commonActions.includes(actionKey)
    },
    // 判断是否有更多操作按钮
    hasMoreActions(row) {
      const allActions = this.getAllActions(row).filter(action => action.show)
      const commonActions = this.getCommonActions(row)
      // 如果显示的按钮数量大于常用按钮数量，则显示更多操作
      return allActions.length >= commonActions.length
    },
    // 切换常用按钮设置
    toggleCommonAction(actionKey) {
      const index = this.commonActions.indexOf(actionKey)
      if (index > -1) {
        this.commonActions.splice(index, 1)
      } else {
        // 允许设置多个常用按钮
        this.commonActions.push(actionKey)
      }
      localStorage.setItem(this.userId + '.projectMaintenanceCommonActions', JSON.stringify(this.commonActions))
    },
    // 计算操作列宽度
    getActionColumnWidth() {
      // 如果列表为空，返回默认宽度
      if (!this.list || this.list.length === 0) {
        return 220
      }
      // 取第一行数据来计算宽度
      const firstRow = this.list[0]
      const commonActions = this.getCommonActions(firstRow)
      const hasMoreActions = this.hasMoreActions(firstRow)
      // 常用按钮数量 + 更多操作按钮(如果有的话) * 110px
      const buttonCount = commonActions.length + (hasMoreActions ? 1 : 0)
      const width = buttonCount * 110
      // 设置最小宽度为 110px，最大宽度为 550px
      return Math.max(110, Math.min(550, width))
    }
  }
}
</script>

<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';

.p20 {
  padding: 20px;
}

.el-cascader {
  width: 100%;
}

.project_detail {
  padding: 0 20px;

  ::v-deep .el-descriptions__body {
    background: #f8f9fb;
    padding: 20px;
  }

  ::v-deep .el-descriptions__header {
    margin-bottom: 12px;
  }

  ::v-deep .el-descriptions__title {
    font-size: 14px;
    color: #666666;
    font-weight: 400;
  }

  ::v-deep .el-descriptions-item__container {
    align-items: center;
  }
}

.reporting_detail {
  padding: 20px;

  .reporting_detail_title {
    font-size: 14px;
    color: #666666;
    margin-bottom: 12px;
  }

  .el-divider--horizontal {
    margin: 0;
  }

  .reporting_detail_list {
    display: flex;
    justify-content: space-between;
    margin: 24px 0;
  }
}

// 弹窗按钮样式
.popover-button {
  .popover-button-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 5px 10px;

    .popover-button-icon {
      margin-left: 10px;
      color: #ccc;
      cursor: pointer;
      font-size: 16px;

      &.active {
        color: #f39c12;
      }

      &:hover {
        color: #f39c12;
      }
    }
  }
}
</style>
