<template>
  <div class="newBox bgcf9 vh-85">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" @submit.native.prevent>
      <el-form-item label="是否过滤无效" prop="filterExpire">
        <el-select v-model="queryParams.filterExpire" placeholder="请选择" @change="handleQuery">
          <el-option v-for="item in filterExpireOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" ref="table" stripe :data="list" row-key="id" style="width: 100%" class="custom-table" v-if="total > 0">
      <el-table-column align="center" type="index" label="序号"></el-table-column>
      <el-table-column align="center" label="采购标题" show-overflow-tooltip>
        <template slot-scope="{ row }">
          <span class="table-link" @click="handleDetail(row)">{{ row.buyingLeads.title }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="createBy" label="求助人" show-overflow-tooltip></el-table-column>
      <el-table-column align="center" prop="createTime" label="求助时间" show-overflow-tooltip></el-table-column>
    </el-table>
    <el-empty v-if="!loading && total === 0" />

    <div class="custom-pagination">
      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
    </div>
  </div>
</template>
<script>
import { platformHelpList } from '@/api/buy'

export default {
  data() {
    return {
      loading: true,
      list: [],
      total: 0,
      queryParams: {
        pageSize: 10,
        pageNum: 1,
        leadsId: undefined,
        filterExpire: false
      },
      filterExpireOptions: [
        { label: '是', value: true },
        { label: '否', value: false }
      ]
    }
  },
  created() {
    this.getList()
  },
  methods: {
    // 采购列表
    getList() {
      const loading = this.$loading({
        lock: true,
        text: '加载中',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      this.loading = true
      // prettier-ignore
      platformHelpList(this.queryParams).then(res => {
        const { code, msg, rows, total } = res
        if (code === 200) {
          this.list = rows
          this.total = total
        } else this.$message.error(msg)
      }).finally(() => {
        this.loading = false
        loading.close()
      })
    },
    // 查看详情
    handleDetail(row) {
      const routeData = this.$router.resolve({
        path: '/section/detail',
        query: { leadsId: row.buyingLeads.id, type: 1 }
      })
      window.open(routeData.href, '_blank')
    }
  }
}
</script>
<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
.newBox {
  padding: 20px 20px 0;
}
</style>
