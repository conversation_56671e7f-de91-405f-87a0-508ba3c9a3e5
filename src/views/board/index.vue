<template>
  <div class="newBox bgcf9 vh-85" :class="{ isIndex: isIndex }">
    <el-row :gutter="15" v-if="!showMore">
      <el-col :span="isIndex ? 24 : 8" v-if="boardShow.length && boardShow.includes('demand')">
        <div class="data">
          <div class="data-title" @click="showMoreInfo('demand')">
            <span>需求统计</span>
            <i class="el-icon-arrow-right" v-if="!isIndex"></i>
          </div>
          <div class="data-info">
            <div class="data-info-item">
              <div class="data-info-item-title"><span>需求总数(个)</span></div>
              <div class="data-info-item-num">
                <span>{{ demandData.totalDemand || 0 }}</span>
              </div>
            </div>
            <div class="data-info-item">
              <div class="data-info-item-title"><span>需求发布次数(次)</span></div>
              <div class="data-info-item-num">
                <span>{{ demandData.totalPublishCount || 0 }}</span>
              </div>
            </div>
          </div>
          <div class="data-chart">
            <div class="data-select border-none">
              <el-date-picker :disabled="isIndex" v-model="demandMonth" format="yyyy年MM月概览" value-format="yyyyMM" type="month" placeholder="请选择月份" style="width: 100%" :clearable="false" @change="getDemandStatistics" :picker-options="endDatePicker"></el-date-picker>
            </div>
            <div class="data-chart-info">
              <line-chart :chart-data="demandFormat(demandData)" />
            </div>
          </div>
        </div>
      </el-col>
      <el-col :span="isIndex ? 24 : 8" v-if="boardShow.length && boardShow.includes('contract')">
        <div class="data">
          <div class="data-title" @click="showMoreInfo('contract')">
            <span>合同统计</span>
            <i class="el-icon-arrow-right" v-if="!isIndex"></i>
          </div>
          <div class="data-info">
            <div class="data-info-item inline">
              <div class="data-info-item-title">合同总数</div>
              <div class="data-info-item-num">
                <span>{{ contractData.totalContract || 0 }}</span>
              </div>
            </div>
            <div class="data-info-item">
              <div class="data-info-item-title">
                {{ contractMonth.slice(0, 4) + '年' + contractMonth.slice(4, 6) + '月合同总金额' }}
              </div>
              <div class="data-info-item-num">
                <span>{{ Math.round(contractData.currentMonthContractAmount) || 0 }}</span>
              </div>
            </div>
          </div>
          <div class="data-chart">
            <div class="data-select border-none">
              <el-date-picker :disabled="isIndex" v-model="contractMonth" format="yyyy年MM月概览" value-format="yyyyMM" type="month" placeholder="请选择月份" style="width: 100%" :clearable="false" @change="getContractStatistics" :picker-options="endDatePicker"></el-date-picker>
            </div>
            <div class="data-chart-info">
              <line-chart :chart-data="contractFormat(contractData)" />
            </div>
          </div>
        </div>
      </el-col>
      <el-col :span="isIndex ? 24 : 8" v-if="boardShow.length && boardShow.includes('marketing')">
        <div class="data">
          <div class="data-title" @click="showMoreInfo('marketing')">
            <span>其他统计</span>
            <i class="el-icon-arrow-right" v-if="!isIndex"></i>
          </div>
          <div class="data-info">
            <div class="data-info-item inline">
              <div class="data-info-item-title">短信发送数量</div>
              <div class="data-info-item-num">
                <span>{{ marketingData.totalMarketing || 0 }}</span>
              </div>
            </div>
            <!-- <div class="data-info-item">
              <div class="data-info-item-title">
                本月数量
              </div>
              <div class="data-info-item-num">
                <span>{{ Math.round(marketingData.currentMonthMarketingAmount) || 0 }}</span>
              </div>
            </div> -->
          </div>
          <div class="data-chart">
            <div class="border-none" style="padding-right: 20px">
              <el-date-picker :disabled="isIndex" v-model="marketingMonth" format="yyyy年MM月dd日" value-format="yyyy-MM-dd" type="daterange" placeholder="请选择日期" style="width: 100%" :clearable="false" @change="getMarketingStatistics" :picker-options="endDatePicker"></el-date-picker>
            </div>
            <div class="data-chart-info">
              <mline-chart height="200px" :chart-data="marketingFormat(marketingData)" />
            </div>
          </div>
        </div>
      </el-col>
      <el-col :span="isIndex ? 24 : 8" v-if="boardShow.length && boardShow.includes('platform')">
        <div class="data height-520">
          <div class="data-title" @click="showMoreInfo('platform')">
            <span>成交统计</span>
            <i class="el-icon-arrow-right" v-if="!isIndex"></i>
          </div>
          <div class="data-info">
            <div class="data-info-item">
              <div class="data-info-item-title"><span>成交总数</span></div>
              <div class="data-info-item-num">
                <span>{{ platformData.totalDeal.total || 0 }}</span>
              </div>
            </div>
            <div class="data-info-item">
              <div class="data-info-item-title"><span>成交总金额</span></div>
              <div class="data-info-item-num orange">
                <span>{{ Math.round(platformData.totalDeal.amount) || 0 }}</span>
              </div>
            </div>
          </div>
          <div class="data-chart">
            <div class="data-select">
              <el-date-picker :disabled="isIndex" v-model="platformMonth" format="yyyy年MM月概览" value-format="yyyyMM" type="month" placeholder="请选择月份" style="width: 100%" :clearable="false" @change="getPlatformStatistics" :picker-options="endDatePicker"></el-date-picker>
            </div>
            <div class="data-chart-info">
              <ycross-bar-chart :chart-data="platformFormat(platformData)" />
            </div>
          </div>
        </div>
      </el-col>
      <el-col :span="isIndex ? 24 : 8" v-if="boardShow.length && boardShow.includes('user')">
        <div class="data height-520">
          <div class="data-title">
            <span>用户统计</span>
          </div>
          <div class="data-info">
            <div class="data-info-item">
              <div class="data-info-item-title"><span>部门总数</span></div>
              <div class="data-info-item-num">
                <span>{{ deptFormat(userData) }}</span>
              </div>
            </div>
            <div class="data-info-item">
              <div class="data-info-item-title">
                <span>用户总数</span>
              </div>
              <div class="data-info-item-num">
                <span>{{ userData.totalUser || 0 }}</span>
              </div>
            </div>
          </div>
          <div class="data-chart">
            <pie-chart height="345px" legend :chart-data="userFormat(userData)" />
          </div>
        </div>
      </el-col>
      <el-col :span="isIndex ? 24 : 8" v-if="boardShow.length && boardShow.includes('offer')">
        <div class="data height-520" :style="{ 'margin-bottom': isIndex ? '0' : '' }">
          <div class="data-title" @click="showMoreInfo('offer')">
            <span>报价统计</span>
            <i class="el-icon-arrow-right" v-if="!isIndex"></i>
          </div>
          <div class="data-info">
            <div class="data-info-item">
              <div class="data-info-item-title"><span>报价总数量</span></div>
              <div class="data-info-item-num">
                <span>{{ offerData.totalQuote }}</span>
              </div>
            </div>
            <div class="data-info-item">
              <div class="data-info-item-title">
                <span>{{ offerMonth.slice(0, 4) + '年' + offerMonth.slice(4, 6) + '月报价数量' }}</span>
              </div>
              <div class="data-info-item-num">
                <span>{{ offerData.currentMonthQuoteCount }}</span>
              </div>
            </div>
          </div>
          <div class="data-chart">
            <div class="data-select">
              <el-date-picker :disabled="isIndex" v-model="offerMonth" format="yyyy年MM月概览" value-format="yyyyMM" type="month" placeholder="请选择月份" style="width: 100%" :clearable="false" @change="getOfferStatistics" :picker-options="endDatePicker"></el-date-picker>
            </div>
            <div class="data-chart-info">
              <cross-bar-chart :chart-data="offerFormat(offerData)" />
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
    <demand-data type="user" ref="demandData" @callBack="hideMoreInfo" v-if="showMore && showType === 'demand'" />
    <contract-data ref="contractData" @callBack="hideMoreInfo" v-if="showMore && showType === 'contract'" />
    <marketing-data ref="marketingData" @callBack="hideMoreInfo" v-if="showMore && showType === 'marketing'" />
    <platform-data ref="platformData" @callBack="hideMoreInfo" v-if="showMore && showType === 'platform'" />
    <offer-data ref="offerData" @callBack="hideMoreInfo" v-if="showMore && showType === 'offer'" />
  </div>
</template>
<script>
import { privateContractStatistics, privateDealStatistics, privateDemandStatistics, privateQuoteStatistics, privateUserStatistics, privateMarketingStatistics } from '@/api/data'
import LineChart from '@/views/data/lineChart'
import MlineChart from '@/views/data/mlineChart'
import ycrossBarChart from '@/views/data/ycrossBarChart'
import crossBarChart from '@/views/data/crossBarChart'
import pieChart from '@/views/data/pieChart'
import demandData from '@/views/board/demand/index'
import contractData from '@/views/board/contract/index'
import marketingData from '@/views/board/marketing/index'
import platformData from '@/views/board/platform/index'
import offerData from '@/views/board/offer/index'

export default {
  components: { offerData, platformData, contractData, demandData, crossBarChart, pieChart, ycrossBarChart, LineChart, MlineChart, marketingData },
  props: {
    boardShow: {
      type: Array,
      default: () => ['es', 'demand', 'contract', 'marketing', 'platform', 'user', 'offer']
    },
    isIndex: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // 合同统计
      contractData: {},
      contractMonth: '',
      // 短信统计
      marketingData: {
        totalMarketing: 0,
        seriesName: [],
        xt: [],
        series: []
      },
      marketingMonth: [],
      // 成交统计
      platformData: {
        totalDeal: { total: 0, amount: 0 }
      },
      platformMonth: '',
      // 需求统计
      demandData: {},
      demandMonth: '',
      // 报价统计
      offerData: {},
      offerMonth: '',
      // 用户统计
      userData: {},
      showMore: false,
      showType: undefined,
      // 当前月份
      nowMonth: '',
      nowDay: '',
      endDatePicker: {
        disabledDate: time => {
          return time.getTime() > new Date().getTime()
        }
      }
    }
  },
  mounted() {
    this.nowMonth = this.parseTime(new Date(), '{y}{m}')
    this.nowDay = this.parseTime(new Date(), '{d}')
    this.init()
  },
  destroyed() {
    if (this.showMore && this.showType === 'marketing') localStorage.setItem('marketingCache', 'true')
    else localStorage.removeItem('marketingCache')
  },
  methods: {
    async init() {
      const marketingCache = localStorage.getItem('marketingCache')
      if (marketingCache) this.showMoreInfo('marketing')
      const now = this.parseTime(new Date(), '{y}{m}')
      this.contractMonth = this.platformMonth = this.demandMonth = this.offerMonth = now
      this.marketingMonth = [this.parseTime(new Date().getTime() - 30 * 24 * 60 * 60 * 1000, '{y}-{m}-{d}'), this.parseTime(new Date(), '{y}-{m}-{d}')]
      if (this.boardShow.includes('demand')) await this.getDemandStatistics()
      if (this.boardShow.includes('contract')) await this.getContractStatistics()
      if (this.boardShow.includes('marketing')) await this.getMarketingStatistics()
      if (this.boardShow.includes('platform')) await this.getPlatformStatistics()
      if (this.boardShow.includes('user')) await this.getUserStatistics()
      if (this.boardShow.includes('offer')) await this.getOfferStatistics()
    },
    // 需求统计
    async getDemandStatistics() {
      const { data } = await privateDemandStatistics({ month: this.demandMonth })
      this.demandData = data
    },
    // 合同统计
    async getContractStatistics() {
      const { data } = await privateContractStatistics({ month: this.contractMonth })
      this.contractData = data
    },
    // 短信统计
    async getMarketingStatistics() {
      const { data } = await privateMarketingStatistics({ start: this.marketingMonth[0], end: this.marketingMonth[1] })
      this.marketingData = {
        totalMarketing: 0,
        seriesName: [],
        xt: [],
        series: []
      }
      const seriesName = data.map(item => item.name)
      const xt = data.map(item => item.xt)
      this.marketingData.seriesName = [...new Set(seriesName)]
      this.marketingData.xt = [...new Set(xt)]
      data.forEach(el => {
        this.marketingData.totalMarketing = this.marketingData.totalMarketing + el.yt
      })
      this.marketingData.seriesName.forEach(el => {
        const obj = {
          type: 'line',
          name: el,
          itemStyle: {
            color: '#2e73f3'
          },
          lineStyle: {
            color: '#2e73f3',
            width: 2
          },
          data: [],
          animationDuration: 2800,
          animationEasing: 'quadraticOut'
        }
        for (let i = 0; i < this.marketingData.xt.length; i++) {
          obj.data.push(0)
          data.forEach(m => {
            if (el === m.name && this.marketingData.xt[i] === m.xt) {
              obj.data[i] = m.yt
            }
          })
        }
        this.marketingData.series.push(obj)
      })
    },
    // 成交统计
    async getPlatformStatistics() {
      const { data } = await privateDealStatistics({ month: this.platformMonth })
      this.platformData = data
    },
    // 用户统计
    async getUserStatistics() {
      const { data } = await privateUserStatistics()
      this.userData = data
    },
    // 报价统计
    async getOfferStatistics() {
      const { data } = await privateQuoteStatistics({ month: this.offerMonth })
      this.offerData = data
    },
    // 需求统计格式化
    demandFormat(data = {}) {
      let { demandMonthData = [], preDemandMonthData = [] } = data
      if (demandMonthData.length > preDemandMonthData.length) demandMonthData.pop()
      if (demandMonthData.length < preDemandMonthData.length) preDemandMonthData.pop()
      let oneData = { series: [], name: undefined }
      let twoData = { series: [], name: undefined }
      oneData.name = '本月需求'
      twoData.name = '上月需求'
      oneData.series = demandMonthData.map(item => item.yc) || []
      twoData.series = preDemandMonthData.map(item => item.yc) || []
      let xAxis = demandMonthData.map(item => item.day) || []
      const { demandMonth, nowMonth, nowDay } = this
      if (demandMonth == nowMonth) {
        xAxis = xAxis.slice(0, nowDay)
        oneData.series = oneData.series.slice(0, nowDay)
        twoData.series = twoData.series.slice(0, nowDay)
      }
      return { xAxis, oneData, twoData, legend: [oneData.name, twoData.name] }
    },
    // 合同统计格式化
    contractFormat(data = {}) {
      let { contractMonthData = [], preContractMonthData = [] } = data
      if (contractMonthData.length > preContractMonthData.length) contractMonthData.pop()
      if (contractMonthData.length < preContractMonthData.length) preContractMonthData.pop()
      let oneData = { series: [], name: undefined }
      let twoData = { series: [], name: undefined }
      oneData.name = '本月合同'
      twoData.name = '上月合同'
      oneData.series = contractMonthData.map(item => item.yc) || []
      twoData.series = preContractMonthData.map(item => item.yc) || []
      let xAxis = contractMonthData.map(item => item.day) || []
      const { contractMonth, nowMonth, nowDay } = this
      if (contractMonth == nowMonth) {
        xAxis = xAxis.slice(0, nowDay)
        oneData.series = oneData.series.slice(0, nowDay)
        twoData.series = twoData.series.slice(0, nowDay)
      }
      return { xAxis, oneData, twoData, legend: [oneData.name, twoData.name] }
    },
    // 短信统计格式化
    marketingFormat(data = {}) {
      let xAxis = data.xt || []
      const { series } = data
      return { xAxis, series, legend: data.seriesName }
    },
    // 成交统计格式化
    platformFormat(data = {}) {
      let { DealMonthData = [], preDealMonthData = [] } = data
      if (DealMonthData.length > preDealMonthData.length) DealMonthData.pop()
      if (DealMonthData.length < preDealMonthData.length) preDealMonthData.pop()
      let oneData = { series: [], name: undefined }
      let twoData = { series: [], name: undefined }
      oneData.name = '本月成交'
      twoData.name = '上月成交'
      oneData.series = DealMonthData.map(item => item.yc) || []
      twoData.series = preDealMonthData.map(item => item.yc) || []
      let yAxis = DealMonthData.map(item => item.day) || []
      const { platformMonth, nowMonth, nowDay } = this
      if (platformMonth == nowMonth) {
        yAxis = yAxis.slice(0, nowDay)
        oneData.series = oneData.series.slice(0, nowDay)
        twoData.series = twoData.series.slice(0, nowDay)
      }
      return { yAxis, oneData, twoData }
    },
    // 报价统计格式化
    offerFormat(data = {}) {
      let { quoteMonthData = [], preMonthData = [] } = data
      if (quoteMonthData.length > preMonthData.length) quoteMonthData.pop()
      if (quoteMonthData.length < preMonthData.length) preMonthData.pop()
      let oneData = { series: [], name: undefined }
      let twoData = { series: [], name: undefined }
      oneData.name = '本月报价'
      twoData.name = '上月报价'
      oneData.series = quoteMonthData.map(item => item.yc) || []
      twoData.series = preMonthData.map(item => item.yc) || []
      let yAxis = quoteMonthData.map(item => item.day) || []
      const { offerMonth, nowMonth, nowDay } = this
      if (offerMonth == nowMonth) {
        yAxis = yAxis.slice(0, nowDay)
        oneData.series = oneData.series.slice(0, nowDay)
        twoData.series = twoData.series.slice(0, nowDay)
      }
      return { yAxis, oneData, twoData }
    },
    // 部门格式化
    deptFormat(data = {}) {
      let { userData = [] } = data
      userData = userData.filter(item => item.dept_name)
      const dept = userData.map(item => item.dept_name)
      return Array.from(new Set(dept)).length
    },
    // 用户饼状图格式化
    userFormat(data = {}) {
      let { userData = [] } = data
      userData = userData.filter(item => item.dept_name)
      const dept = userData.map(item => item.dept_name)
      const deptArr = Array.from(new Set(dept))
      let series = []
      deptArr.forEach(item => {
        let obj = { value: 0, name: item }
        userData.forEach(user => {
          if (user.dept_name === item) obj.value++
        })
        series.push(obj)
      })
      return { series, seriesName: '用户统计', }
    },
    // 显示更多
    showMoreInfo(type = 'demand') {
      if (this.isIndex) return
      this.showMore = true
      this.showType = type
    },
    // 隐藏更多
    hideMoreInfo() {
      this.showMore = false
      this.showType = undefined
      localStorage.removeItem('marketingCache')
    }
  }
}
</script>
<style scoped lang="scss">
@import '~@/assets/styles/custom-chart.scss';
.newBox {
  padding: 20px 20px 0;
}
.isIndex {
  padding: 20px 0 0 !important;
  min-height: auto !important;
  background-color: transparent !important;
}
</style>
