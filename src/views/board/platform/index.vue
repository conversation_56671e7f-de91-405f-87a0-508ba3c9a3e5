<template>
  <div class="moreBox">
    <el-page-header @back="goBack" content="成交统计"></el-page-header>
    <el-row :gutter="0">
      <el-col :span="12">
        <div class="moreBox-info">
          <div class="moreBox-info-item">
            <div class="item-title">{{ platformMonth.slice(0, 4) + '年' + platformMonth.slice(4, 6) + '月成交数量' }}</div>
            <div class="item-num">{{ platformData.currentMonthDealCount || 0 }}</div>
            <div class="item-bg gray"></div>
            <div class="item-contrast">
              <div class="c666">
                <span>对比上个月</span>
                <i class="el-icon-caret-bottom"></i>
              </div>
              <div :class="platformIsIncrease(platformData) === 1 ? 'green' : platformIsIncrease(platformData) === -1 ? 'red' : 'orange'">
                <i class="el-icon-top" v-if="platformIsIncrease(platformData) === 1"></i>
                <i class="el-icon-bottom" v-if="platformIsIncrease(platformData) === -1"></i>
                <span>{{ platformContrast(platformData) === 0 ? '持平' : platformContrast(platformData) + '%' }}</span>
              </div>
            </div>
          </div>
          <div class="moreBox-info-item">
            <div class="item-title">{{ platformMonth.slice(0, 4) + '年' + platformMonth.slice(4, 6) + '月成交金额' }}</div>
            <div class="item-num orange">{{ Math.round(platformData.currentMonthDealAmount) || 0 }}</div>
            <div class="item-bg gray"></div>
            <div class="item-contrast">
              <div class="c666">
                <span>对比上个月</span>
                <i class="el-icon-caret-bottom"></i>
              </div>
              <div :class="platformIsIncrease(platformData, 'mount') === 1 ? 'green' : platformIsIncrease(platformData, 'mount') === -1 ? 'red' : 'orange'">
                <i class="el-icon-top" v-if="platformIsIncrease(platformData, 'mount') === 1"></i>
                <i class="el-icon-bottom" v-if="platformIsIncrease(platformData, 'mount') === -1"></i>
                <span>{{ platformContrast(platformData, 'mount') === 0 ? '持平' : platformContrast(platformData, 'mount') + '%' }}</span>
              </div>
            </div>
          </div>
        </div>
        <div class="data-chart">
          <div class="data-select border-none">
            <el-date-picker v-model="platformMonth" format="yyyy年MM月概览" value-format="yyyyMM" type="month" placeholder="请选择月份" style="width: 100%" :clearable="false" @change="getPlatformStatistics" :picker-options="endDatePicker"></el-date-picker>
          </div>
          <div class="data-chart-info">
            <ycross-bar-chart height="600px" :chart-data="platformFormat(platformData)" />
          </div>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="moreBox-list">
          <div class="moreBox-list-title">
            <div class="inline-flex">
              <span class="title">采购产品成交金额排名</span>
              <el-input v-model="productName" placeholder="请输入产品名称" size="small" style="max-width: 200px; margin-left: 10px; flex: 1; overflow: hidden" @change="handleQuery" clearable />
            </div>
            <div class="inline-flex" style="position: relative">
              <el-switch v-model="productQuery.ft" active-text="单月" active-value="s" inactive-text="区间" inactive-value="m" class="custom-switch"></el-switch>
              <div class="data-select border-none relative" v-show="productQuery.ft === 's'">
                <el-date-picker v-model="productQuery.month" format="yyyy年MM月概览" value-format="yyyyMM" type="month" placeholder="请选择月份" style="width: 100%" :clearable="false" @change="getProductStatistics" :picker-options="endDatePicker"></el-date-picker>
              </div>
              <div class="data-select data-selects border-none" style="position: unset" v-show="productQuery.ft === 'm'">
                <el-date-picker v-model="monthArr" type="monthrange" range-separator="至" start-placeholder="开始月份" format="yyyy年MM月" value-format="yyyyMM" end-placeholder="结束月份" @change="getProductStatistics" :picker-options="endDatePicker"></el-date-picker>
              </div>
            </div>
          </div>
          <div class="moreBox-list-top">
            <template v-if="productList.length">
              <div style="max-height: 778px; padding: 10px 20px; overflow: auto" v-infinite-scroll="getMore" infinite-scroll-disabled="disabled" infinite-scroll-immediate="false">
                <div class="top-item" v-for="(item, index) in productList" :key="index">
                  <b class="top-item-title link" @click="handleDetail(item)">{{ item.productName }}</b>
                  <span class="top-item-desc">
                    成交金额
                    <i style="color: #ec2454; font-style: normal; padding-left: 10px">￥{{ item.deal || 0 }}</i>
                  </span>
                </div>
                <div class="moreBox-more" v-if="loading">
                  <i class="el-icon-loading"></i>
                  <span>加载中...</span>
                </div>
                <div class="moreBox-more" v-if="noMore">
                  <i class="ssfont ss-diy-biaoqing"></i>
                  <span>我是有底线的</span>
                </div>
              </div>
              <div class="moreBox-list-total" v-if="productQuery.ft === 'm'">
                <span>成交金额汇总：</span>
                <i style="color: #ec2454; font-style: normal">￥{{ totalDealAmount(allProductList) }}</i>
              </div>
            </template>
            <el-empty v-else />
          </div>
        </div>
      </el-col>
    </el-row>
    <product-dialog ref="productInfo"></product-dialog>
  </div>
</template>
<script>
import { privateDealStatistics, privateProductDealStatistics } from '@/api/data'
import { parseTime, getPercent, getIsIncrease } from '@/utils/ruoyi'
import ycrossBarChart from '@/views/data/ycrossBarChart'
import ProductDialog from '@/views/public/product/dialog'
import { getPrivateduct } from '@/api/system/privateduct'

export default {
  components: { ProductDialog, ycrossBarChart },
  data() {
    return {
      platformMonth: parseTime(new Date(), '{y}{m}'),
      platformData: {
        totalDeal: { total: 0, amount: 0 }
      },
      topList: [],
      // 当前月份
      nowMonth: parseTime(new Date(), '{y}{m}'),
      nowDay: parseTime(new Date(), '{d}'),
      endDatePicker: {
        disabledDate: time => {
          return time.getTime() > new Date().getTime()
        }
      },
      // 产品成交统计
      productList: [],
      allProductList: [],
      productQuery: {
        ft: 's',
        pre: undefined,
        month: parseTime(new Date(), '{y}{m}')
      },
      monthArr: '',
      loading: false,
      historyList: [],
      productName: undefined
    }
  },
  created() {
    this.getPlatformStatistics()
    this.getProductStatistics()
  },
  computed: {
    noMore() {
      return this.productList.length >= this.allProductList.length
    },
    disabled() {
      return this.loading || this.noMore
    }
  },
  methods: {
    parseTime,
    getPercent,
    getIsIncrease,
    // 平台成交统计
    getPlatformStatistics() {
      privateDealStatistics({ month: this.platformMonth }).then(res => {
        const { code, msg, data } = res
        if (code === 200) this.platformData = data
        else this.$message.error(msg)
      })
    },
    // 查询产品成交统计
    getProductStatistics() {
      if (this.productQuery.ft === 'm') {
        this.productQuery.pre = this.monthArr[0]
        this.productQuery.month = this.monthArr[1]
      }
      privateProductDealStatistics(this.productQuery).then(res => {
        const { code, data, msg } = res
        if (code === 200) {
          this.historyList = data
          this.allProductList = data
          this.productList = data.length > 20 ? data.slice(0, 20) : data
        } else this.$message.error(msg)
      })
    },
    // 搜索产品
    handleQuery() {
      const list = [...this.historyList]
      if (this.productName) {
        this.allProductList = list.filter(item => item.productName.includes(this.productName))
        this.productList = this.allProductList.length > 20 ? this.allProductList.slice(0, 20) : this.allProductList
      } else {
        this.allProductList = list
        this.productList = list.length > 20 ? list.slice(0, 20) : list
      }
    },
    getMore() {
      this.loading = true
      setTimeout(() => {
        const list = this.allProductList.slice(0, this.productList.length + 20)
        this.$set(this, 'productList', list)
        this.loading = false
      }, 500)
    },
    // 格式化平台成交统计折线图数据
    platformFormat(data = {}) {
      let { DealMonthData = [], preDealMonthData = [] } = data
      if (DealMonthData.length > preDealMonthData.length) DealMonthData.pop()
      if (DealMonthData.length < preDealMonthData.length) preDealMonthData.pop()
      let oneData = { series: [], name: undefined }
      let twoData = { series: [], name: undefined }
      oneData.name = '本月成交'
      twoData.name = '上月成交'
      oneData.series = DealMonthData.map(item => item.yc) || []
      twoData.series = preDealMonthData.map(item => item.yc) || []
      let yAxis = DealMonthData.map(item => item.day) || []
      const { platformMonth, nowMonth, nowDay } = this
      if (platformMonth == nowMonth) {
        yAxis = yAxis.slice(0, nowDay)
        oneData.series = oneData.series.slice(0, nowDay)
        twoData.series = twoData.series.slice(0, nowDay)
      }
      return { yAxis, oneData, twoData }
    },
    // 平台成交统计对比上个月
    platformContrast(data = {}, type = 'count') {
      const { currentMonthDealCount = 0, preMonthDealCount = 0, currentMonthDealAmount = 0, preMonthDealAmount = 0 } = data
      if (type === 'count') {
        return this.getPercent(currentMonthDealCount, preMonthDealCount)
      } else {
        return this.getPercent(currentMonthDealAmount, preMonthDealAmount)
      }
    },
    platformIsIncrease(data = {}, type = 'count') {
      const { currentMonthDealCount = 0, preMonthDealCount = 0, currentMonthDealAmount = 0, preMonthDealAmount = 0 } = data
      if (type === 'count') {
        return this.getIsIncrease(currentMonthDealCount, preMonthDealCount)
      } else {
        return this.getIsIncrease(currentMonthDealAmount, preMonthDealAmount)
      }
    },
    goBack() {
      this.$emit('callBack')
    },
    // 产品详情
    handleDetail(item) {
      getPrivateduct(item.productId).then(res => {
        this.$refs.productInfo.handleView(res.data)
      })
    },
    // 成交金额汇总
    totalDealAmount(list = []) {
      return parseFloat(list.reduce((total, item) => total + Number(item.deal.replace(/,/g, '')), 0).toFixed(2))
    }
  }
}
</script>
<style scoped lang="scss">
@import '~@/assets/styles/custom-chart.scss';
.moreBox {
  .data-chart {
    width: 100%;
    height: 600px;
    margin-top: 30px;
    margin-left: 0;
  }
}
::v-deep {
  .data-selects {
    width: 300px;
    .el-range-editor.el-input__inner {
      padding: 0 0 0 10px;
    }
  }
  .moreBox-list-top {
    padding: 0;
    .top-item {
      height: auto;
      padding: 8px 0;
    }
    .link {
      color: #2e73f3;
      cursor: pointer;
      &:hover {
        text-decoration: underline;
      }
    }
    .moreBox-more {
      line-height: 38px;
      text-align: center;
      color: $disabled;
      cursor: pointer;
      font-size: 14px;
    }
  }
  .moreBox-list-total {
    display: flex;
    align-items: center;
    line-height: 50px;
    justify-content: center;
    padding: 0 20px;
    border-top: 1px solid #cbd6e2;
    span {
      font-size: 16px;
      color: #666666;
    }
  }
  .custom-switch {
    position: relative;
    margin: 0 10px;
    .el-switch__core {
      height: 24px;
      border-radius: 12px;
      min-width: 60px;
      &:after {
        left: 4px;
        top: 3px;
      }
    }
    &.el-switch {
      &.is-checked {
        .el-switch__core {
          &:after {
            margin-left: -20px;
            left: 100%;
          }
        }
      }
    }
    &.is-checked {
      .el-switch__label--left {
        opacity: 0;
      }
      .el-switch__label--right {
        opacity: 1;
      }
    }
    .el-switch__label {
      position: absolute;
      top: 0;
    }
    .el-switch__label--left {
      right: 0;
      color: #999999;
      z-index: 1;
      margin-right: 8px;
    }
    .el-switch__label--right {
      left: 0;
      color: #ffffff;
      opacity: 0;
      margin-left: 8px;
    }
  }
}
</style>
