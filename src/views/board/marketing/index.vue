<template>
  <div class="moreBox">
    <el-page-header @back="goBack" content="其他统计"></el-page-header>
    <el-row :gutter="0">
      <el-col :span="12">
        <div class="moreBox-info">
          <div class="moreBox-info-item" style="width: 100%">
            <div class="item-title">{{ marketingMonth[0] + '至' + marketingMonth[1] + '短信发送数量' }}</div>
            <div class="item-num">{{ marketingData.totalMarketing || 0 }}</div>
            <div class="item-bg gray"></div>
          </div>
        </div>
        <div class="data-chart">
          <div class="border-none" style="padding-right: 20px">
            <el-date-picker v-model="marketingMonth" format="yyyy年MM月dd日" value-format="yyyy-MM-dd" type="daterange" placeholder="请选择日期" style="width: 100%" :clearable="false" @change="getMarketingStatistics" :picker-options="endDatePicker"></el-date-picker>
          </div>
          <div class="data-chart-info">
            <mline-chart height="550px" :chart-data="marketingFormat(marketingData)" />
          </div>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="moreBox-list">
          <div class="moreBox-list-title">
            <span class="title">私域企业采购排名</span>
            <div class="inline-flex" style="position: relative">
              <el-switch v-model="supplierQuery.ft" active-text="单月" active-value="s" inactive-text="区间" inactive-value="m" class="custom-switch"></el-switch>
              <div class="data-select border-none relative" v-show="supplierQuery.ft === 's'">
                <el-date-picker v-model="supplierQuery.month" format="yyyy年MM月概览" value-format="yyyyMM" type="month" placeholder="请选择月份" style="width: 100%" :clearable="false" @change="getSupplierDeals" :picker-options="endDatePicker"></el-date-picker>
              </div>
              <div class="data-select data-selects border-none" style="position: unset" v-show="supplierQuery.ft === 'm'">
                <el-date-picker v-model="monthArr" type="monthrange" range-separator="至" start-placeholder="开始月份" format="yyyy年MM月" value-format="yyyyMM" end-placeholder="结束月份" @change="getSupplierDeals" :picker-options="endDatePicker"></el-date-picker>
              </div>
            </div>
          </div>
          <div class="moreBox-list-top">
            <template v-if="supplierList.length">
              <div style="max-height: 778px; padding: 10px 20px; overflow: auto" v-infinite-scroll="getMore" infinite-scroll-disabled="disabled" infinite-scroll-immediate="false">
                <div class="top-item" v-for="(item, index) in supplierList" :key="index">
                  <b class="top-item-title">{{ item.name }}</b>
                  <span class="top-item-desc">
                    成交金额
                    <i style="color: #ec2454; font-style: normal; padding-left: 10px">￥{{ item.deal || 0 }}</i>
                    <em class="top-item-btn" @click="handleViewContract(item)">查看合同</em>
                  </span>
                </div>
                <div class="moreBox-more" v-if="loading">
                  <i class="el-icon-loading"></i>
                  <span>加载中...</span>
                </div>
                <div class="moreBox-more" v-if="noMore">
                  <i class="ssfont ss-diy-biaoqing"></i>
                  <span>我是有底线的</span>
                </div>
              </div>
            </template>
            <el-empty v-else />
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import { privateMarketingStatistics, privateSupplierDeals } from '@/api/data'
import { parseTime, getPercent, getIsIncrease } from '@/utils/ruoyi'
import MlineChart from '@/views/data/mlineChart'

export default {
  components: { MlineChart },
  data() {
    return {
      marketingMonth: [],
      marketingData: {
        totalMarketing: 0,
        seriesName: [],
        xt: [],
        series: []
      },
      endDatePicker: {
        disabledDate: time => {
          return time.getTime() > new Date().getTime()
        }
      },
      // 产品成交统计
      supplierList: [],
      allSupplierList: [],
      supplierQuery: {
        ft: 's',
        pre: undefined,
        month: parseTime(new Date(), '{y}{m}')
      },
      monthArr: '',
      loading: false
    }
  },
  created() {
    this.getSupplierDeals()
    this.init()
  },
  computed: {
    noMore() {
      return this.supplierList.length >= this.allSupplierList.length
    },
    disabled() {
      return this.loading || this.noMore
    }
  },
  methods: {
    parseTime,
    getPercent,
    getIsIncrease,
    async init() {
      this.marketingMonth = [this.parseTime(new Date().getTime() - 30 * 24 * 60 * 60 * 1000, '{y}-{m}-{d}'), this.parseTime(new Date(), '{y}-{m}-{d}')]
      await this.getMarketingStatistics()
    },
    // 短信统计
    async getMarketingStatistics() {
      const { data } = await privateMarketingStatistics({ start: this.marketingMonth[0], end: this.marketingMonth[1] })
      this.marketingData = {
        totalMarketing: 0,
        seriesName: [],
        xt: [],
        series: []
      }
      const seriesName = data.map(item => item.name)
      const xt = data.map(item => item.xt)
      this.marketingData.seriesName = [...new Set(seriesName)]
      this.marketingData.xt = [...new Set(xt)]
      data.forEach(el => {
        this.marketingData.totalMarketing = this.marketingData.totalMarketing + el.yt
      })
      this.marketingData.seriesName.forEach(el => {
        const obj = {
          type: 'line',
          name: el,
          itemStyle: {
            color: '#2e73f3'
          },
          lineStyle: {
            color: '#2e73f3',
            width: 2
          },
          data: [],
          animationDuration: 2800,
          animationEasing: 'quadraticOut'
        }
        for (let i = 0; i < this.marketingData.xt.length; i++) {
          obj.data.push(0)
          data.forEach(m => {
            if (el === m.name && this.marketingData.xt[i] === m.xt) {
              obj.data[i] = m.yt
            }
          })
        }
        this.marketingData.series.push(obj)
      })
    },
    // 查询产品成交统计
    getSupplierDeals() {
      if (this.supplierQuery.ft === 'm') {
        this.supplierQuery.pre = this.monthArr[0]
        this.supplierQuery.month = this.monthArr[1]
      }
      privateSupplierDeals(this.supplierQuery).then(res => {
        const { code, data, msg } = res
        if (code === 200) {
          this.allSupplierList = data
          this.supplierList = data.length > 20 ? data.slice(0, 20) : data
        } else this.$message.error(msg)
      })
    },
    getMore() {
      this.loading = true
      setTimeout(() => {
        const list = this.allSupplierList.slice(0, this.supplierList.length + 20)
        this.$set(this, 'supplierList', list)
        this.loading = false
      }, 500)
    },
    // 短信统计格式化
    marketingFormat(data = {}) {
      let xAxis = data.xt || []
      const { series } = data
      return { xAxis, series, legend: data.seriesName }
    },
    goBack() {
      this.$emit('callBack')
    },
    // 查看合同
    handleViewContract(item) {
      this.$emit('viewContract')
      this.$router.push({ path: '/demand/contract', query: { keyword: item.name } })
    }
  }
}
</script>
<style scoped lang="scss">
@import '~@/assets/styles/custom-chart.scss';
.moreBox {
  .data-chart {
    width: 100%;
    height: 600px;
    margin-top: 30px;
    margin-left: 0;
  }
}
::v-deep {
  .data-selects {
    width: 300px;
    .el-range-editor.el-input__inner {
      padding: 0 0 0 10px;
    }
  }
  .moreBox-list-top {
    padding: 0;
    .top-item {
      height: auto;
      padding: 8px 0;
    }
    .link {
      color: #2e73f3;
      cursor: pointer;
      &:hover {
        text-decoration: underline;
      }
    }
    .moreBox-more {
      line-height: 38px;
      text-align: center;
      color: $disabled;
      cursor: pointer;
      font-size: 14px;
    }
  }
  .custom-switch {
    position: relative;
    margin: 0 10px;
    .el-switch__core {
      height: 24px;
      border-radius: 12px;
      min-width: 60px;
      &:after {
        left: 4px;
        top: 3px;
      }
    }
    &.el-switch {
      &.is-checked {
        .el-switch__core {
          &:after {
            margin-left: -20px;
            left: 100%;
          }
        }
      }
    }
    &.is-checked {
      .el-switch__label--left {
        opacity: 0;
      }
      .el-switch__label--right {
        opacity: 1;
      }
    }
    .el-switch__label {
      position: absolute;
      top: 0;
    }
    .el-switch__label--left {
      right: 0;
      color: #999999;
      z-index: 1;
      margin-right: 8px;
    }
    .el-switch__label--right {
      left: 0;
      color: #ffffff;
      opacity: 0;
      margin-left: 8px;
    }
  }
}
</style>
