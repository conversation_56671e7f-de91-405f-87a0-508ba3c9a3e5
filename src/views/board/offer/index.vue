<template>
  <div class="moreBox">
    <el-page-header @back="goBack" content="报价统计"></el-page-header>
    <el-row :gutter="0">
      <el-col :span="12">
        <div class="moreBox-info">
          <div class="moreBox-info-item">
            <div class="item-title">报价总数量</div>
            <div class="item-num">{{ offerData.totalQuote || 0 }}</div>
          </div>
          <div class="moreBox-info-item">
            <div class="item-title">{{ offerMonth.slice(0, 4) + '年' + offerMonth.slice(4, 6) + '月报价数量' }}</div>
            <div class="item-num">{{ Math.round(offerData.currentMonthQuoteCount) || 0 }}</div>
            <div class="item-bg gray"></div>
            <div class="item-contrast">
              <div class="c666">
                <span>对比上个月</span>
                <i class="el-icon-caret-bottom"></i>
              </div>
              <div :class="offerIsIncrease(offerData) === 1 ? 'green' : offerIsIncrease(offerData) === -1 ? 'red' : 'orange'">
                <i class="el-icon-top" v-if="offerIsIncrease(offerData) === 1"></i>
                <i class="el-icon-bottom" v-if="offerIsIncrease(offerData) === -1"></i>
                <span>{{ offerContrast(offerData) === 0 ? '持平' : offerContrast(offerData) + '%' }}</span>
              </div>
            </div>
          </div>
        </div>
        <div class="data-chart">
          <div class="data-select border-none">
            <el-date-picker v-model="offerMonth" format="yyyy年MM月概览" value-format="yyyyMM" type="month" placeholder="请选择月份" style="width: 100%" :clearable="false" @change="getOfferStatistics" :picker-options="endDatePicker"></el-date-picker>
          </div>
          <div class="data-chart-info">
            <ycross-bar-chart height="600px" :chart-data="offerFormat(offerData)" />
          </div>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="moreBox-list">
          <div class="moreBox-list-title">
            <span class="title">报价概览</span>
            <div class="data-select border-none relative">
              <el-date-picker v-model="offerMonth" format="yyyy年MM月概览" value-format="yyyyMM" type="month" placeholder="请选择月份" style="width: 100%" :clearable="false" @change="getOfferStatistics" :picker-options="endDatePicker"></el-date-picker>
            </div>
          </div>
          <div class="moreBox-list-chart flex align-center">
            <div class="chart-chart">
              <pie-chart :chart-data="offerPieFormat(offerData)" />
            </div>
            <div class="chart-info">
              <div class="chart-item border-bottom-0">
                <div class="chart-item-title color-b4ceff">一级报价</div>
                <div class="chart-item-info" :class="offerPublishIsIncrease(offerData) === 1 ? 'green' : offerPublishIsIncrease(offerData) === -1 ? 'red' : 'orange'">
                  <span class="title">同比上个月</span>
                  <i class="el-icon-top" v-if="offerPublishIsIncrease(offerData) === 1"></i>
                  <i class="el-icon-bottom" v-if="offerPublishIsIncrease(offerData) === -1"></i>
                  <span class="num">{{ offerPublishContrast(offerData) === 0 ? '持平' : offerPublishContrast(offerData) + '%' }}</span>
                </div>
              </div>
              <div class="chart-item border-bottom-0">
                <div class="chart-item-title color-ffe19b">二级报价</div>
                <div class="chart-item-info" :class="offerPublishIsIncrease(offerData, false) === 1 ? 'green' : offerPublishIsIncrease(offerData) === -1 ? 'red' : 'orange'">
                  <span class="title">同比上个月</span>
                  <i class="el-icon-top" v-if="offerPublishIsIncrease(offerData, false) === 1"></i>
                  <i class="el-icon-bottom" v-if="offerPublishIsIncrease(offerData, false) === -1"></i>
                  <span class="num">{{ offerPublishContrast(offerData, false) === 0 ? '持平' : offerPublishContrast(offerData, false) + '%' }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import { privateQuoteStatistics } from '@/api/data'
import { parseTime, getPercent, getIsIncrease } from '@/utils/ruoyi'
import ycrossBarChart from '@/views/data/ycrossBarChart'
import pieChart from '@/views/data/pieChart.vue'

export default {
  components: { pieChart, ycrossBarChart },
  data() {
    return {
      offerMonth: parseTime(new Date(), '{y}{m}'),
      offerData: {
        totalDeal: { total: 0, amount: 0 }
      },
      topList: [],
      // 当前月份
      nowMonth: parseTime(new Date(), '{y}{m}'),
      nowDay: parseTime(new Date(), '{d}'),
      endDatePicker: {
        disabledDate: time => {
          return time.getTime() > new Date().getTime()
        }
      }
    }
  },
  created() {
    this.getOfferStatistics()
  },
  methods: {
    parseTime,
    getPercent,
    getIsIncrease,
    // 平台成交统计
    getOfferStatistics() {
      privateQuoteStatistics({ month: this.offerMonth }).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          this.offerData = data
          this.topList = data.companyTop5Data || []
        } else this.$message.error(msg)
      })
    },
    // 格式化平台成交统计折线图数据
    offerFormat(data = {}) {
      let { quoteMonthData = [], preMonthData = [] } = data
      if (quoteMonthData.length > preMonthData.length) quoteMonthData.pop()
      if (quoteMonthData.length < preMonthData.length) preMonthData.pop()
      let oneData = { series: [], name: undefined }
      let twoData = { series: [], name: undefined }
      oneData.name = '本月报价'
      twoData.name = '上月报价'
      oneData.series = quoteMonthData.map(item => item.yc) || []
      twoData.series = preMonthData.map(item => item.yc) || []
      let yAxis = quoteMonthData.map(item => item.day) || []
      const { offerMonth, nowMonth, nowDay } = this
      if (offerMonth == nowMonth) {
        yAxis = yAxis.slice(0, nowDay)
        oneData.series = oneData.series.slice(0, nowDay)
        twoData.series = twoData.series.slice(0, nowDay)
      }
      return { yAxis, oneData, twoData }
    },
    // 平台成交统计对比上个月
    offerContrast(data = {}) {
      const { currentMonthQuoteCount = 0, preMonthCount = 0 } = data
      return this.getPercent(currentMonthQuoteCount, preMonthCount)
    },
    offerIsIncrease(data = {}) {
      const { currentMonthQuoteCount = 0, preMonthCount = 0 } = data
      return this.getIsIncrease(currentMonthQuoteCount, preMonthCount)
    },
    // 数量对比上个月百分比
    offerPublishContrast(data = {}, isInner = true) {
      const { monthQuoteData = [], preMonthQuoteData = [] } = data
      let current = 0
      let pre = 0
      const currentMonth = monthQuoteData.length && monthQuoteData.find(item => item.isInner === isInner)
      const preMonth = preMonthQuoteData.length && preMonthQuoteData.find(item => item.isInner === isInner)
      current = currentMonth ? currentMonth.ct : 0
      pre = preMonth ? preMonth.ct : 0
      return this.getPercent(current, pre)
    },
    // 数量对比上个月是否增加
    offerPublishIsIncrease(data = {}, isInner = true) {
      const { monthQuoteData = [], preMonthQuoteData = [] } = data
      let current = 0
      let pre = 0
      const currentMonth = monthQuoteData.length && monthQuoteData.find(item => item.isInner === isInner)
      const preMonth = preMonthQuoteData.length && preMonthQuoteData.find(item => item.isInner === isInner)
      current = currentMonth ? currentMonth.ct : 0
      pre = preMonth ? preMonth.ct : 0
      return this.getIsIncrease(current, pre)
    },
    // 格式化饼图数据
    offerPieFormat(data = {}) {
      const { monthQuoteData = [] } = data
      const noInner = monthQuoteData.find(item => !item.isInner) || { ct: 0 }
      const isInner = monthQuoteData.find(item => item.isInner) || { ct: 0 }
      return {
        series: [
          { name: '一级报价', value: noInner.ct || 0, itemStyle: { color: '#b4ceff' } },
          { name: '二级报价', value: isInner.ct || 0, itemStyle: { color: '#ffe19b' } }
        ]
      }
    },
    goBack() {
      this.$emit('callBack')
    }
  }
}
</script>
<style scoped lang="scss">
@import '~@/assets/styles/custom-chart.scss';
.moreBox {
  .data-chart {
    width: 100%;
    height: 600px;
    margin-top: 30px;
    margin-left: 0;
  }
}
</style>
