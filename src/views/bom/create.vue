<template>
  <div>
    <el-dialog v-dialogDragBox :title="title" :visible.sync="open" width="1150px" class="custom-dialog" append-to-body>
      <div class="box">
        <el-form ref="form" :model="form" :rules="rules" label-width="6em" :disabled="isDetail" label-position="left">
          <template v-if="!onlyChild">
            <el-row :gutter="10">
              <el-col :span="12">
                <el-form-item label="产品名称" prop="productName" style="padding: 0 20px;">
                  <el-input v-model="form.productName" readonly clearable style="width: 100%" placeholder="请选择产品">
                    <el-button slot="append" type="primary" icon="el-icon-search"
                      @click="handleOpenProduct(0)"></el-button>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="BOM名称" prop="bomName" style="padding: 0 20px;">
                  <el-input v-model="form.bomName" clearable placeholder="请输入BOM名称" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>
          </template>
          <div class="boxBg">
            <el-form-item label="" label-width="0" prop="child">
              <div class="title">产品BOM物料清单</div>
              <template v-if="showChild">
                <template v-if="!!form.child.length">
                  <el-table :data="form.child" stripe style="width: 100%" class="custom-table">
                    <el-table-column align="center" type="index" label="序号"></el-table-column>
                    <el-table-column label="产品名称" align="center">
                      <template slot-scope="{ row }">
                        <span class="table-link pointer" @click="handleDetail(row)">{{ row.productName }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column label="规格" prop="specs" align="center"></el-table-column>
                    <el-table-column label="型号" prop="model" align="center"></el-table-column>
                    <el-table-column label="单位" prop="unit" align="center"></el-table-column>
                    <el-table-column label="所需数量" prop="quantity" align="center"></el-table-column>
                    <el-table-column label="操作" align="center" v-if="!isDetail">
                      <template slot-scope="scope">
                        <button type="button" class="table-btn danger"
                          @click="handleDeleteChild(scope.$index)">删除</button>
                      </template>
                    </el-table-column>
                  </el-table>
                  <el-button type="primary" icon="el-icon-plus" size="small" @click="handleAddChild"
                    v-if="!isDetail">新增子项物料</el-button>
                </template>
                <div class="none" v-else>
                  <span>该产品暂无拆分物料，请点击右侧新增子项物料按钮增加产品BOM子项物料</span>
                  <el-button type="primary" icon="el-icon-plus" size="small" @click="handleAddChild">新增子项物料</el-button>
                </div>
              </template>
              <el-empty description="请选择产品" v-if="!showChild" />
            </el-form-item>
          </div>
          <div class="craftBox" v-if="form.productName">
            <div class="craft_title">
              <div class="title_text">产品工艺</div>
              <div class="add_btn" @click="addProcessGroup" v-if="!isDetail">
                <img src="@/assets/images/add_blue.png" alt="">
                添加工艺
              </div>
            </div>
            <div class="craft_concent">
              <div class="craft_process_group" v-for="(it, i) in form.processGroupList" :key="i">
                <div class="craft_process_group_title" v-if="i > 0">
                  <span>{{ it.name }}</span>
                </div>
                <div class="craft_process_group_del" @click="delProcessGroup(i)" v-if="i > 0 && !isDetail">
                  <img src="@/assets/images/bom_del.png" alt="">
                  <span>删除工艺</span>
                </div>
                <div class="craft_concent_form" v-for="(item, index) in it.processList" :key="index">
                  <div class="craft_concent_form_title">
                    <div class="title">
                      工序—{{ index + 1 }}
                      <span>请按照产品生产顺序添加工序</span>
                    </div>
                    <div class="del" v-if="index !== 0 && !isDetail" @click="delProcess(index, i)">
                      <img src="@/assets/images/bom_del.png" alt="">
                      <span>删除工序</span>
                    </div>
                  </div>
                  <div class="craft_concent_form_box">
                    <el-row :gutter="10">
                      <el-col :span="12">
                        <el-form-item label="生产工序" :prop="`processGroupList[${i}].processList[${index}].processId`"
                          :rules="rules.processList.processId">
                          <el-select v-model="item.processId" placeholder="请选择生产工序"
                            @change="handleChange(item.processId, index, i)" style="width: 100%;"
                            @input="() => $forceUpdate()">
                            <el-option v-for="ite in processOptions" :key="ite.id" :label="ite.name"
                              :value="ite.id"></el-option>
                          </el-select>
                        </el-form-item>
                      </el-col>
                      <el-col :span="12">
                        <el-form-item label="工序费用" :prop="`processGroupList[${i}].processList[${index}].cost`"
                          :rules="rules.processList.cost">
                          <el-input v-model="item.cost" clearable placeholder="请输入生产所涉及工序的费用"
                            @input="() => $forceUpdate()">
                            <template slot="append">元</template>
                          </el-input>
                        </el-form-item>
                      </el-col>
                      <el-col :span="24">
                        <el-form-item label="所用设备" prop="parentId">
                          <div class="flex">
                            <span class="item" v-for="(it, i) in item.process && item.process.equipments"
                              :key="'equ' + i">{{
                                it.equipment && it.equipment.name }}</span>
                          </div>
                        </el-form-item>
                      </el-col>
                      <el-col :span="24">
                        <el-form-item label="生产描述" prop="description">
                          <el-input v-model="item.description" type="textarea" :autosize="{ minRows: 3, maxRows: 6 }"
                            placeholder="请输入生产加工BOM添加产品所需子项物料的工艺顺序详细描述，请确保描述清晰简练"
                            @input="() => $forceUpdate()"></el-input>
                        </el-form-item>
                      </el-col>
                    </el-row>
                  </div>
                </div>
                <div class="craft_concent_form_add" @click="addProcess(i)" v-if="!isDetail">
                  <img src="@/assets/images/add_icon.png" alt="">
                  添加工序
                </div>
                <div class="craft_concent_checked">
                  <el-checkbox v-model="it.isDefault" :disabled="isDetail" @change="handleIsDefault($event, i, it)">设为默认工序</el-checkbox>
                </div>
              </div>

              <div class="craft_concent_upload">
                <el-form-item label="生产作业书" prop="operationBook">
                  <image-upload :fileSize="5" :fileType="['png', 'jpg', 'jpeg', 'PDF']" v-model="form.operationBook"
                    :isDisabled="isDetail" :deleteShow="!isDetail" />
                </el-form-item>
              </div>
            </div>
          </div>
        </el-form>
      </div>
      <div slot="footer">
        <template v-if="!isDetail">
          <el-button class="custom-dialog-btn" @click="open = false">取消</el-button>
          <el-button class="custom-dialog-btn primary" @click="handleSubmit">确定</el-button>
        </template>
        <el-button class="custom-dialog-btn primary" @click="open = false" v-if="isDetail">关闭</el-button>
      </div>
    </el-dialog>

    <!--添加子项物料-->
    <el-dialog v-dialogDragBox title="添加子项物料" :visible.sync="childOpen" width="750px" class="custom-dialog">
      <div style="padding: 0 20px">
        <el-form ref="childForm" :model="childForm" :rules="childRules" label-width="6em">
          <el-form-item label="产品名称" prop="productName">
            <el-input v-model="childForm.productName" readonly clearable style="width: 80%" placeholder="请选择产品">
              <el-button slot="append" type="primary" icon="el-icon-search" @click="handleOpenProduct(1)"></el-button>
            </el-input>
          </el-form-item>
          <el-form-item label="单位" prop="unit">
            <el-select v-model="childForm.unit" placeholder="请选择单位" filterable allow-create default-first-option
              style="width: 80%">
              <el-option v-for="(item, index) in unitOptions" :key="index" :label="item" :value="item"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="所需数量" prop="quantity">
            <el-input v-model="childForm.quantity" placeholder="请输入所需数量" style="width: 80%">
              <span slot="suffix">{{ childForm.unit }}</span>
            </el-input>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn" @click="childOpen = false">取消</el-button>
        <el-button class="custom-dialog-btn primary" @click="handleConfirmChild">确定</el-button>
      </div>
    </el-dialog>

    <!--选择产品-->
    <el-dialog v-dialogDragBox title="选择产品" :visible.sync="productOpen" width="1150px" class="custom-dialog">
      <div class="productBox">
        <el-form ref="productQuery" :model="productQuery" inline size="small" @submit.native.prevent>
          <el-form-item label="名称" prop="productName">
            <el-input v-model="productQuery.productName" placeholder="请输入产品名称" clearable
              @keyup.enter.native="handleProductQuery" style="width: auto" />
          </el-form-item>
          <el-form-item label="编码" prop="productCode">
            <el-input v-model="productQuery.productCode" placeholder="请输入产品编码" clearable
              @keyup.enter.native="handleProductQuery" />
          </el-form-item>
          <el-form-item label="规格" prop="specs">
            <el-input v-model="productQuery.specs" placeholder="请输入产品规格" clearable
              @keyup.enter.native="handleProductQuery" />
          </el-form-item>
          <el-form-item label="型号" prop="model">
            <el-input v-model="productQuery.model" placeholder="请输入产品型号" clearable
              @keyup.enter.native="handleProductQuery" />
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select v-model="productQuery.status" placeholder="请选择状态">
              <el-option v-for="item in statusOptions" :key="item.value" :label="item.label"
                :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="单位" prop="unit">
            <el-select v-model="productQuery.unit" placeholder="请选择单位">
              <el-option v-for="(item, index) in unitOptions" :key="index" :label="item" :value="item"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="small" @click="handleProductQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="small" @click="resetProductQuery">重置</el-button>
          </el-form-item>
        </el-form>
        <el-table v-loading="productLoading" ref="productTable" stripe :data="productList" row-key="id"
          style="width: 100%" @row-click="selectProduct" class="custom-table custom-table-cell5">
          <el-table-column width="44" label="" align="center">
            <template slot-scope="{ row }">
              <el-radio v-removeAriaHidden class="radio" v-model="productChecked.id"
                :label="row.id"><span></span></el-radio>
            </template>
          </el-table-column>
          <el-table-column align="center" type="index" label="序号"></el-table-column>
          <el-table-column align="center" prop="product_name" label="产品名称" show-overflow-tooltip>
            <template slot-scope="{ row }">
              <span class="table-link pointer" @click="handleDetail(row)">{{ row.productName }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="productCode" label="产品编码" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="picture1" label="图片" width="75">
            <template slot-scope="{ row }">
              <el-image :src="formatProductImg(row)" fit="cover" @click="handleImg(row)">
                <div slot="error" class="image-slot">
                  <i class="el-icon-picture-outline"></i>
                </div>
              </el-image>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="specs" label="规格" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="model" label="型号" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="materialQuality" label="材质" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="unit" label="单位" width="50"></el-table-column>
          <el-table-column align="center" label="操作">
            <template slot-scope="{ row }">
              <el-button type="text" size="mini" icon="el-icon-circle-check" @click="selectProduct(row)">选择</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="custom-pagination">
          <pagination v-show="productTotal > 0" :total="productTotal" :page.sync="productQuery.pageNum"
            :limit.sync="productQuery.pageSize" @pagination="getProductList" />
        </div>
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn" @click="productOpen = false">取消</el-button>
        <el-button :disabled="!productChecked.id" class="custom-dialog-btn primary"
          :class="{ disabled: !productChecked.id }" @click="handleConfirmProduct">确定</el-button>
      </div>
    </el-dialog>

    <!-- 产品详情 -->
    <product-dialog ref="productInfo"></product-dialog>
  </div>
</template>
<script>
import { listPrivateduct } from '@/api/system/privateduct'
import ProductDialog from '@/views/public/product/dialog'
import { addBom, getBomDetail, getBomDetailByCode, updateBom, getProcessInfoList } from '@/api/bom'
import { isNumber, isNumberLength } from '@/utils/validate'

export default {
  components: { ProductDialog },
  data() {
    return {
      title: '新增产品BOM',
      open: false,
      form: {},
      rules: {
        productName: [{ required: true, message: '请选择产品名称', trigger: ['change', 'blur'] }],
        bomName: [{ required: true, message: '请输入BOM名称', trigger: ['change', 'blur'] }],
        child: [{ required: true, message: '请添加产品BOM物料清单', trigger: ['change', 'blur'] }],
        processList: {
          processId: [
            { required: true, message: "请选择生产工艺", trigger: 'change' }
          ],
          cost: [
            { required: true, message: "请输入工艺费用", trigger: 'blur' }
          ],
        }
      },
      // 子项物料
      childOpen: false,
      childForm: {},
      childRules: {
        productName: [{ required: true, message: '请选择产品名称', trigger: ['change', 'blur'] }],
        unit: [{ required: true, message: '请选择单位', trigger: 'change' }],
        quantity: [
          { required: true, message: '请输入所需数量', trigger: 'blur' },
          { validator: isNumber, message: '输入有误请重新输入', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: 'blur' }
        ]
      },
      childLoading: false,
      // 选择产品
      productOpen: false,
      productChecked: {},
      productList: [],
      productLoading: true,
      productTotal: 0,
      productQuery: {
        pageNum: 1,
        pageSize: 10,
        productName: undefined,
        productCode: undefined,
        specs: undefined,
        unit: undefined,
        status: 1
      },
      statusOptions: [
        { label: '上架', value: 1 },
        { label: '下架', value: 0 }
      ],
      unitOptions: ['吨', '千克', '个', '件', '套', '米', '支', '根'],
      isChild: false, // 是否是子项物料
      showChild: false, // 是否显示产品BOM子项物料
      isDetail: false, // 是否是详情
      onlyChild: false, // 只显示子项物料
      // 生产工艺工序
      processOptions: [],

    }
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      getProcessInfoList({
        pageNum: 1,
        pageSize: 10000,
        status: 1,
      }).then(res => {
        const { code, msg, rows } = res
        if (code === 200) {
          this.processOptions = rows
        } else this.$message.error(msg)
      })
    },
    // 重置表单
    reset() {
      this.form = {
        bomName: undefined,
        child: [],
        productName: undefined,
        productId: undefined,
        processGroupList: [{
          processList: [],
          isDefault: true,
          name: '产品工艺'
        }],
        operationBook: undefined
      }
      this.resetForm('form')
    },
    // 新增
    async handleAdd(product = {}) {
      this.reset()
      this.title = '新增产品BOM'
      this.showChild = false
      this.isDetail = false
      this.onlyChild = false
      if (product && product.productCode) {
        const { data } = await getBomDetailByCode({ productCode: product.productCode })
        if (data && data.child && data.child.length) {
          this.form.child = data.child.map(item => {
            return Object.assign({}, item.product, item)
          })
        } else this.form.child = []
        this.form.productId = product.id
        this.form.productName = product.productName
        this.form.processGroupList = [{
          processList: [{
            cost: undefined,
            description: undefined,
            processId: undefined,
            process: {
              equipments: []
            }
          }],
          isDefault: true,
          name: '产品工艺'
        }]
        this.showChild = true
        if (!this.form.bomName) this.form.bomName = product.productName
      }
      this.open = true
    },
    // 远程搜索产品名称
    // 修改
    handleEdit(row) {
      this.reset()
      getBomDetail({ bomId: row.id }).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          const child = data.child.map(item => {
            return Object.assign({}, item.product, item)
          })
          if (!data.processGroupList) {
            data.processGroupList = [{
              processList: [{
                cost: undefined,
                description: undefined,
                processId: undefined,
                process: {
                  equipments: []
                }
              }],
              isDefault: true,
              name: '产品工艺'
            }]
          }
          const productName = data.product.productName
          this.form = { ...data, child, productName }
          this.showChild = true
          this.isDetail = false
          this.onlyChild = false
          this.title = '修改产品BOM'
          this.open = true
        } else this.$message.error(msg)
      })
    },
    // 详情
    handleView(row, val = 1) {
      this.reset()
      getBomDetail({ bomId: row.id }).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          const child = data.child.map(item => {
            return Object.assign({}, item.product, item)
          })
          if (!data.processGroupList) {
            data.processGroupList = [{
              processList: [{
                cost: undefined,
                description: undefined,
                processId: undefined,
                process: {
                  equipments: []
                }
              }],
              isDefault: true,
              name: '产品工艺'
            }]
          }
          const productName = data.product.productName
          this.form = { ...data, child, productName }
          this.showChild = true
          this.isDetail = true
          this.onlyChild = !val
          this.title = val ? '产品BOM详情' : '产品BOM物料清单'
          this.open = true
        } else this.$message.error(msg)
      })
    },
    // 提交
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          if (!!this.form.id) {
            this.form.bomId = this.form.id
            updateBom(this.form).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('操作成功')
                this.open = false
                this.$emit('refresh')
              } else this.$message.error(msg)
            })
          } else {
            addBom(this.form).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('操作成功')
                this.open = false
                this.$emit('refresh')
              } else this.$message.error(msg)
            })
          }
        }
      })
    },
    // 重置子项物料表单
    resetChild() {
      this.childForm = {
        productId: undefined,
        productName: undefined,
        specs: undefined,
        model: undefined,
        quantity: undefined,
        unit: undefined
      }
      this.resetForm('childForm')
    },
    // 新增子项物料
    handleAddChild() {
      this.resetChild()
      this.childOpen = true
    },
    // 确定新增子项物料
    handleConfirmChild() {
      this.$refs['childForm'].validate(valid => {
        if (valid) {
          this.form.child.push({ ...this.childForm })
          this.$refs.form.validateField('child')
          this.childOpen = false
        }
      })
    },
    // 删除子项物料
    // prettier-ignore
    handleDeleteChild(index) {
      this.$confirm('是否删除该子项物料？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.form.child.splice(index, 1)
        this.$refs.form.validateField('child')
      }).catch(() => { })
    },
    // 打开产品列表
    handleOpenProduct(val = 0) {
      this.isChild = !!val
      this.productChecked = {}
      this.resetProductQuery()
    },
    // 搜索产品列表
    handleProductQuery() {
      this.productQuery.pageNum = 1
      this.getProductList()
    },
    // 重置搜索产品列表
    resetProductQuery() {
      this.resetForm('productQuery')
      this.handleProductQuery()
    },
    // 产品列表
    getProductList() {
      this.productLoading = true
      listPrivateduct(this.productQuery).then(res => {
        const { code, msg, rows, total } = res
        if (code === 200) {
          this.productList = rows
          this.productTotal = total
          this.productLoading = false
          if (this.isChild) {
            const obj = this.productList.find(item => item.id === this.childForm.productId)
            if (obj) this.productChecked = obj
          } else {
            const obj = this.productList.find(item => item.id === this.form.productId)
            if (obj) this.productChecked = obj
          }
          if (!this.productOpen) this.productOpen = true
        } else this.$message.error(msg)
      })
    },
    // 选择产品
    selectProduct(row) {
      this.productChecked = row
    },
    // 确定选择产品
    async handleConfirmProduct() {
      if (this.isChild) {
        this.childForm = { ...this.productChecked }
        this.childForm.productId = this.productChecked.id
        this.childForm.productName = this.productChecked.productName
        this.childForm.specs = this.productChecked.specs
        this.childForm.model = this.productChecked.model
        this.childForm.unit = this.productChecked.unit
      } else {
        const { data } = await getBomDetailByCode({ productCode: this.productChecked.productCode })
        if (data && data.child && data.child.length) {
          this.form.child = data.child.map(item => {
            return Object.assign({}, item.product, item)
          })
        } else this.form.child = []
        this.form.productId = this.productChecked.id
        this.form.productName = this.productChecked.productName
        this.form.processGroupList = [{
          processList: [{
            cost: undefined,
            description: undefined,
            processId: undefined,
            process: {
              equipments: []
            }
          }],
          isDefault: true,
          name: '产品工艺'
        }]
        this.showChild = true
        if (!this.form.bomName) this.form.bomName = this.productChecked.productName
      }
      this.productOpen = false
    },
    // 产品详情
    handleDetail(item, type) {
      this.$refs.productInfo.handleView(item, type)
    },
    // 产品图片预览
    handleImg(row) {
      this.$refs.productInfo.handleImgView(row)
    },
    // 生产工艺工序
    // 选择工艺
    handleChange(id, index, i) {
      console.log(this.form.processGroupList[i])
      this.form.processGroupList[i].processList[index].process.equipments = this.processOptions.find(item => item.id == id).equipments
    },
    // 增加工序
    addProcess(i) {
      this.form.processGroupList[i].processList.push({
        cost: undefined,
        description: undefined,
        processId: undefined,
        process: {
          equipments: []
        }
      })
    },
    // 删除工序
    delProcess(index, i) {
      this.form.processGroupList[i].processList.splice(index, 1)
    },
    // 设置默认工艺
    handleIsDefault(e, index) {
      if (e === true) {
        this.form.processGroupList.forEach(el => {
          el.isDefault = false
          this.form.processGroupList[index].isDefault = true
        });
      }
    },
    // 新增工艺
    addProcessGroup() {
      if (this.form.processGroupList && this.form.processGroupList.length > 0) {
        console.log(1)
        this.form.processGroupList.push({
          processList: [{
            cost: undefined,
            description: undefined,
            processId: undefined,
            process: {
              equipments: []
            }
          }],
          isDefault: false,
          name: '产品工艺' + (this.form.processGroupList.length + 1)
        })
      } else {
        console.log(2)
        this.form.processGroupList.push({
          processList: [{
            cost: undefined,
            description: undefined,
            processId: undefined,
            process: {
              equipments: []
            }
          }],
          isDefault: true,
          name: '产品工艺'
        })
      }

    },
    // 删除工艺
    delProcessGroup(i) {
      this.form.processGroupList.splice(i, 1)
    }
  }
}
</script>

<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';

.box {
  &Bg {
    background-color: #f0f3f9;
    padding: 16px 20px;
    position: relative;

    &:before {
      content: '';
      position: absolute;
      top: -10px;
      left: 200px;
      width: 0;
      height: 0;
      border-style: solid;
      border-width: 0 10px 10px 10px;
      border-color: transparent transparent #f0f3f9 transparent;
    }

    .title {
      font-size: 14px;
      color: $info;
      margin-bottom: 10px;
      line-height: 16px;
    }

    .custom-table {
      margin-bottom: 20px;
    }

    .none {
      border: 1px solid #2e73f3;
      border-radius: 5px;
      background-color: #dce8ff;
      height: 50px;
      display: flex;
      align-items: center;
      padding: 0 20px;

      span {
        font-size: 12px;
        color: #2e73f3;
        margin-right: 20px;
      }
    }
  }

  .craftBox {
    padding: 16px 20px;

    .craft_title {
      font-size: 14px;
      color: #666666;
      line-height: 16px;
      margin-bottom: 7px;
      display: flex;
      align-items: center;

      .add_btn {
        display: flex;
        align-items: center;
        margin-left: 50px;
        font-weight: 500;
        font-size: 14px;
        color: #2E73F3;
        cursor: pointer;

        img {
          width: 20px;
          height: 20px;
          margin-right: 3px;
        }
      }
    }

    .craft_concent {
      .craft_process_group {
        background: #F8F9FB;
        border-radius: 5px 5px 5px 5px;
        border: 1px solid #CBD6E2;
        margin-bottom: 40px;
        position: relative;

        .craft_process_group_title {
          position: absolute;
          left: 0px;
          top: -27px;
        }

        .craft_process_group_del {
          position: absolute;
          right: 0px;
          top: -27px;
          font-weight: 500;
          font-size: 12px;
          color: #F43F3F;
          cursor: pointer;
          display: flex;
          align-items: center;

          img {
            width: 16px;
            height: 16px;
          }
        }

        .craft_concent_form {
          padding: 0 15px;

          .craft_concent_form_title {
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-bottom: 1px solid #CBD6E2;
            height: 43px;
            margin-bottom: 15px;

            .title {
              font-weight: 500;
              font-size: 14px;
              color: #666666;

              span {
                display: inline-block;
                font-weight: 400;
                font-size: 12px;
                color: #F35D09;
                line-height: 14px;
                margin-left: 15px;
                vertical-align: middle;
              }
            }

            .del {
              font-weight: 500;
              font-size: 14px;
              color: #F43F3F;
              line-height: 20px;
              cursor: pointer;
              display: flex;
              align-items: center;

              img {
                width: 24px;
                height: 24px;
              }
            }
          }

          .craft_concent_form_box {

            .flex {
              display: flex;
              flex-wrap: wrap;
              align-items: center;

              .item {
                padding: 0px 10px;
                height: 30px;
                background: #E1E4E8;
                border-radius: 5px;
                font-weight: 500;
                font-size: 14px;
                color: #333333;
                text-align: center;
                line-height: 30px;
                margin-right: 10px;
              }
            }
          }
        }

        .craft_concent_form_add {

          margin-left: 102px;
          width: 96px;
          height: 32px;
          background: #2E73F3;
          border-radius: 5px;
          border: 1px solid #2E73F3;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: 500;
          font-size: 12px;
          color: #FFFFFF;
          margin-bottom: 15px;

          img {
            width: 16px;
            height: 16px;
            margin-right: 4px;
          }
        }

        .craft_concent_checked {
          height: 48px;
          border-top: 1px solid #CBD6E2;
          padding: 0 20px;
          font-weight: 400;
          font-size: 14px;
          color: #666666;
          line-height: 48px;
        }
      }


      .craft_concent_upload {
        margin-top: 30px;
      }
    }
  }
}

::v-deep {
  .productBox {
    padding: 10px 20px;
  }
}

.hover:hover {
  transform: scale(1);
}
</style>
