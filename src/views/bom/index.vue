<template>
  <div class="newBox bgcf9 vh-85">
    <div class="custom-search" style="padding-top: 18px">
      <div class="flex" :style="{ justifyContent: showSearch ? 'space-between' : 'flex-end' }">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" @submit.native.prevent>
          <!--        <el-form-item label="产品材质" prop="materialQuality">-->
          <!--          <el-input v-model="queryParams.materialQuality" placeholder="请输入产品材质" clearable @keyup.enter.native="handleQuery" />-->
          <!--        </el-form-item>-->
          <!--        <el-form-item label="产品型号" prop="model">-->
          <!--          <el-input v-model="queryParams.model" placeholder="请输入产品型号" clearable @keyup.enter.native="handleQuery" />-->
          <!--        </el-form-item>-->
          <!--        <el-form-item label="产品编码" prop="productCode">-->
          <!--          <el-input v-model="queryParams.productCode" placeholder="请输入产品编码" clearable @keyup.enter.native="handleQuery" />-->
          <!--        </el-form-item>-->
          <el-form-item label="产品名称" prop="productName">
            <el-input v-model="queryParams.productName" placeholder="请输入产品名称" clearable
              @keyup.enter.native="handleQuery" />
          </el-form-item>
          <!--        <el-form-item label="产品规格" prop="specs">-->
          <!--          <el-input v-model="queryParams.specs" placeholder="请输入产品规格" clearable @keyup.enter.native="handleQuery" />-->
          <!--        </el-form-item>-->
          <!--        <el-form-item label="表面处理" prop="surface">-->
          <!--          <el-input v-model="queryParams.surface" placeholder="请输入表面处理" clearable @keyup.enter.native="handleQuery" />-->
          <!--        </el-form-item>-->
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
          </el-form-item>
          <el-form-item label="" prop="status">
            <el-select v-model="status" placeholder="请选择状态" @change="handleQuery">
              <el-option v-for="item in statusOptions" :key="item.value" :label="item.label"
                :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-plus" size="small" @click="handleAdd">新增产品BOM</el-button>
          </el-form-item>
        </el-form>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
      </div>
    </div>
    <div class="Box">
      <el-table v-loading="loading" ref="table" stripe :data="list" row-key="id" style="width: 100%"
        class="custom-table">
        <el-table-column align="center" type="index" label="序号" v-if="columns[0].visible"></el-table-column>
        <el-table-column label="BOM名称" align="center" prop="bomName" show-overflow-tooltip v-if="columns[1].visible">
          <template slot-scope="{ row }">
            <span class="table-link" @click="handleView(row)">{{ row.bomName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="产品名称" align="center" show-overflow-tooltip min-width="130" v-if="columns[2].visible">
          <template slot-scope="{ row }">
            <span class="table-link" @click="handleDetail(row.product)">{{ row.product && row.product.productName
              }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="picture1" label="图片" width="75" v-if="columns[3].visible">
          <template slot-scope="{ row }">
            <el-image :src="formatProductImg(row.product)" fit="cover" @click="handleImg(row.product)"
              v-if="row.product">
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline"></i>
              </div>
            </el-image>
          </template>
        </el-table-column>
        <el-table-column label="规格" align="center" show-overflow-tooltip v-if="columns[4].visible">
          <template slot-scope="{ row }">
            <span>{{ row.product && row.product.specs }}</span>
          </template>
        </el-table-column>
        <el-table-column label="型号" align="center" show-overflow-tooltip v-if="columns[5].visible">
          <template slot-scope="{ row }">
            <span>{{ row.product && row.product.model }}</span>
          </template>
        </el-table-column>
        <el-table-column label="材质" align="center" show-overflow-tooltip v-if="columns[6].visible">
          <template slot-scope="{ row }">
            <span>{{ row.product && row.product.materialQuality }}</span>
          </template>
        </el-table-column>
        <el-table-column label="表面处理" align="center" show-overflow-tooltip v-if="columns[7].visible">
          <template slot-scope="{ row }">
            <span>{{ row.product && row.product.surface }}</span>
          </template>
        </el-table-column>
        <el-table-column label="子物料单" align="center" prop="productCode" v-if="columns[8].visible">
          <template slot-scope="{ row }">
            <span class="table-link" @click="handleView(row, 0)">查看</span>
          </template>
        </el-table-column>
        <el-table-column label="创建人" align="center" prop="createBy" v-if="columns[9].visible"></el-table-column>
        <el-table-column label="创建时间" align="center" prop="createTime" v-if="columns[10].visible"></el-table-column>
        <el-table-column label="状态" align="center" prop="status" v-if="columns[11].visible">
          <template slot-scope="{ row }">
            <span class="table-tag" :class="row.status === 1 ? 'primary' : 'danger'">{{ row.status === 1 ? '启用' : '禁用'
              }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="220px">
          <template slot-scope="{ row }">
            <el-button class="table-btn" @click="handleEdit(row)">修改</el-button>
            <el-button class="table-btn primary" v-if="row.status != 1" @click="handleStatus(row, 1)">启用</el-button>
            <el-button class="table-btn danger" v-if="row.status == 1" @click="handleStatus(row, 0)">禁用</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="custom-pagination">
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize" @pagination="getList" />
      </div>
    </div>

    <!--新增/修改-->
    <product-bom ref="productBom" @refresh="refreshList" />
    <!-- 产品详情 -->
    <product-dialog ref="productInfo"></product-dialog>
  </div>
</template>
<script>
import productBom from '@/views/bom/create'
import { getBomList, updateBomStatus } from '@/api/bom'
import ProductDialog from '@/views/public/product/dialog'

export default {
  name: 'ProductBom',
  components: { ProductDialog, productBom },
  data() {
    return {
      status: 1,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        materialQuality: undefined,
        model: undefined,
        productCode: undefined,
        productName: undefined,
        specs: undefined,
        status: undefined,
        surface: undefined
      },
      list: [],
      total: 0,
      loading: false,
      statusOptions: [
        { label: '启用', value: 1, type: 'success' },
        { label: '禁用', value: 0, type: 'danger' }
      ],
      // 列表显隐
      showSearch: true,
      // 列信息
      columns: [
        { key: 0, label: `序号`, visible: true },
        { key: 1, label: `BOM名称`, visible: true },
        { key: 2, label: `产品名称`, visible: true },
        { key: 3, label: `图片`, visible: true },
        { key: 4, label: `规格`, visible: true },
        { key: 5, label: `型号`, visible: true },
        { key: 6, label: `材质`, visible: true },
        { key: 7, label: `表面处理`, visible: true },
        { key: 8, label: `子物料单`, visible: true },
        { key: 9, label: `创建人`, visible: true },
        { key: 10, label: `创建时间`, visible: true },
        { key: 11, label: `状态`, visible: true },
      ],
    }
  },
  created() {
    this.getList()
  },
  methods: {
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 重置搜索
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 获取列表
    getList() {
      this.loading = true
      this.queryParams.status = this.status
      getBomList(this.queryParams).then(res => {
        const { code, msg, rows, total } = res
        if (code === 200) {
          this.list = rows
          this.total = total
          this.loading = false
        } else this.$message.error(msg)
      })
    },
    // 刷新列表
    refreshList() {
      getBomList(this.queryParams).then(res => {
        const { code, msg, rows, total } = res
        if (code === 200) {
          this.$set(this, 'list', rows)
          this.$set(this, 'total', total)
        } else this.$message.error(msg)
      })
    },
    // 修改状态
    // prettier-ignore
    handleStatus(row, val = 0) {
      const title = `是否${val === 1 ? '启用' : '禁用'}该产品BOM？`
      this.$confirm(title, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        updateBomStatus({ id: row.id, status: val }).then(res => {
          const { code, msg } = res
          if (code === 200) {
            this.$message.success('操作成功')
            this.refreshList()
          } else this.$message.error(msg)
        })
      }).catch(() => { })
    },
    // 新增
    handleAdd() {
      this.$refs.productBom.handleAdd()
    },
    // 修改
    handleEdit(row) {
      this.$refs.productBom.handleEdit(row)
    },
    // 产品详情
    handleDetail(item, type) {
      this.$refs.productInfo.handleView(item, type)
    },
    // 产品图片预览
    handleImg(row) {
      this.$refs.productInfo.handleImgView(row)
    },
    // 查看BOM详情
    handleView(row, val = 1) {
      this.$refs.productBom.handleView(row, val)
    }
  }
}
</script>

<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';

.Box {
  padding: 15px 20px;
}

.table-tag {
  &.primary {
    color: $blue;
  }

  &.danger {
    color: $red;
  }
}

::v-deep {
  .el-button.table-btn {
    padding: 0;
  }
}
</style>
