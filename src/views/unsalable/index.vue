<template>
  <div class="newBox bgcf9 vh-85">
    <!-- 搜索 -->
    <div class="custom-search flex">
      <div class="flex">
        <div class="custom-search-form flex">
          <input type="text" v-model="queryParams.keyword" placeholder="请输入产品名称" class="custom-search-input" @keyup.enter="handleQuery" />
          <button type="button" class="custom-search-button pointer" @click="handleQuery">
            <i class="el-icon-search"></i>
            搜索
          </button>
        </div>
        <button type="button" class="custom-search-add pointer" @click="handleProduct">
          <i class="el-icon-plus"></i>
          引入滞销品
        </button>
      </div>
      <div class="custom-search-tip" v-if="isUpdate">
        <i class="el-icon-info"></i>
        <span>滞销品价格有变动，是否更新？</span>
        <el-button type="danger" plain size="small" @click="handleUpdatePrice('refresh')">更新</el-button>
        <el-button plain size="small" @click="handleUpdatePrice('ignore')">忽略</el-button>
      </div>
    </div>

    <!-- 分类 -->
    <div class="classify flex">
      <div class="classify-item" :class="{ active: item.id === queryParams.categoryId }" v-for="item in categoryList" :key="item.id" @click="handleCategory(item)">
        {{ item.name }}
      </div>
      <div class="classify-toolbar">
        <right-toolbar :search="false" @queryTable="getList" :columns="columns"></right-toolbar>
      </div>
    </div>

    <!-- 表格数据 -->
    <div class="tableBox">
      <el-table v-loading="loading" ref="table" stripe :data="list" row-key="id" :key="key" style="width: 100%" class="custom-table custom-table-cell5">
        <el-table-column align="center" type="index" label="序号" v-if="columns[0].visible"></el-table-column>
        <el-table-column align="center" prop="productName" label="产品名称" show-overflow-tooltip v-if="columns[1].visible" min-width="130"></el-table-column>
        <el-table-column align="center" prop="picture1" label="图片" width="75" v-if="columns[2].visible">
          <template slot-scope="{ row }">
            <el-image :src="formatProductImg(row)" fit="cover" @click="handleImg(row)">
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline"></i>
              </div>
            </el-image>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="specs" label="规格" show-overflow-tooltip v-if="columns[3].visible"></el-table-column>
        <el-table-column align="center" prop="productCode" label="产品编码" show-overflow-tooltip v-if="columns[4].visible" width="130"></el-table-column>
        <el-table-column align="center" prop="materialQuality" label="材质" show-overflow-tooltip v-if="columns[5].visible"></el-table-column>
        <el-table-column align="center" prop="surface" label="表面处理" show-overflow-tooltip v-if="columns[6].visible"></el-table-column>
        <el-table-column align="center" prop="unit" label="单位" width="50" v-if="columns[7].visible"></el-table-column>
        <el-table-column align="center" prop="industry" label="行业分类" show-overflow-tooltip v-if="columns[8].visible"></el-table-column>
        <el-table-column align="center" prop="weight" label="重量" width="80" v-if="columns[9].visible">
          <template slot-scope="{ row }">{{ row.weight ? parseFloat(row.weight) + 'Kg' : '' }}</template>
        </el-table-column>
        <el-table-column align="center" prop="stock" label="产品库存" show-overflow-tooltip v-if="columns[10].visible">
          <template slot-scope="{ row }">{{ parseFloat(row.stock) + row.unit }}</template>
        </el-table-column>
        <el-table-column align="center" prop="status" label="状态" width="130" v-if="columns[11].visible">
          <template slot-scope="{ row }">
            <template>
              <el-switch v-model="row.status" active-text="上架" inactive-text="下架" :active-value="1" :inactive-value="-1" @change="handlePutaway(row, $event)" class="table-switch"></el-switch>
            </template>
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作" width="220px">
          <template slot-scope="{ row }">
            <button type="button" class="table-btn" @click="handleUpdate(row)">修改</button>
            <button type="button" class="table-btn danger" @click="handleDelete(row)">删除</button>
          </template>
        </el-table-column>
      </el-table>

      <div class="custom-pagination">
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
      </div>
    </div>

    <!--    选择私域产品-->
    <el-dialog v-dialogDragBox title="选择引入滞销品" :visible.sync="productOpen" width="1150px" class="custom-dialog">
      <div style="padding: 0 20px">
        <el-form :model="productParams" ref="queryForm" size="small" :inline="true" @submit.native.prevent>
          <el-form-item label="产品名称" prop="productParams">
            <el-input v-model="productParams.productName" placeholder="请输入产品名称" clearable @keyup.enter.native="handleProductQuery" size="small" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="small" @click="handleProductQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="small" @click="resetProductQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <el-table v-loading="productLoading" ref="table" stripe :data="productList" row-key="id" style="width: 100%" class="custom-table custom-table-cell5">
          <el-table-column align="center" type="index" label="序号"></el-table-column>
          <el-table-column align="center" prop="productName" label="产品名称" show-overflow-tooltip min-width="130">
            <template slot-scope="{ row }">
              <span class="table-link pointer" @click="handleDetail(row)">{{ row.productName }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="productCode" label="产品编码" show-overflow-tooltip width="110"></el-table-column>
          <el-table-column align="center" prop="picture1" label="图片" width="75">
            <template slot-scope="{ row }">
              <el-image :src="formatProductImg(row)" fit="cover" @click="handleImg(row)">
                <div slot="error" class="image-slot">
                  <i class="el-icon-picture-outline"></i>
                </div>
              </el-image>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="specs" label="规格" show-overflow-tooltip width="100"></el-table-column>
          <el-table-column align="center" prop="model" label="型号" show-overflow-tooltip width="100"></el-table-column>
          <el-table-column align="center" prop="unit" label="单位" show-overflow-tooltip width="50"></el-table-column>
          <el-table-column align="center" prop="weight" label="重量" show-overflow-tooltip width="50"></el-table-column>
          <el-table-column align="center" prop="industry" label="行业分类" show-overflow-tooltip width="100"></el-table-column>
          <el-table-column align="center" prop="materialQuality" label="材质" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="surface" label="表面" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="status" label="状态" show-overflow-tooltip width="80">
            <template slot-scope="{ row }">
              <template>
                <el-switch v-model="row.status" active-text="上架" inactive-text="下架" :active-value="1" :inactive-value="0" disabled class="table-switch"></el-switch>
              </template>
            </template>
          </el-table-column>
          <el-table-column align="center" label="操作" width="80">
            <template slot-scope="{ row }">
              <el-button type="text" size="mini" icon="el-icon-edit" @click="handleAdd(row)">引入</el-button>
            </template>
          </el-table-column>
        </el-table>

        <div class="custom-pagination">
          <pagination v-show="productTotal > 0" :total="productTotal" :page.sync="productParams.pageNum" :limit.sync="productParams.pageSize" @pagination="getProductList" />
        </div>
      </div>
    </el-dialog>

    <!-- 详情 -->
    <product-dialog ref="productInfo"></product-dialog>

    <!-- 新增/修改产品 -->
    <create-dialog ref="create" />
  </div>
</template>

<script>
import * as category from '@/api/purchase/category'
import { listUnsalable, delUnsalable, changeUnsalableStatus, checkUnsalablePrice, updateUnsalablePrice } from '@/api/unsalable'
import createDialog from './create'
import { listPrivateduct } from '@/api/system/privateduct'
import ProductDialog from '@/views/public/product/dialog'

export default {
  name: 'Unsalablelist',
  components: { ProductDialog, createDialog },
  data() {
    return {
      key: 1,
      // 搜索条件
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        categoryId: undefined,
        keyword: undefined
      },
      // 加载
      loading: true,
      // 列表数据
      list: [],
      // 总条数
      total: 0,
      // 分类数据
      categoryList: [],
      // 显隐列
      columns: [
        { key: 0, label: `序号`, visible: true },
        { key: 1, label: `产品名称`, visible: true },
        { key: 2, label: `图片`, visible: true },
        { key: 3, label: `规格`, visible: true },
        { key: 4, label: `产品编码`, visible: true },
        { key: 5, label: `材质`, visible: true },
        { key: 6, label: `表面处理`, visible: true },
        { key: 7, label: `单位`, visible: true },
        { key: 8, label: `行业分类`, visible: true },
        { key: 9, label: `重量`, visible: true },
        { key: 10, label: `产品库存`, visible: true },
        { key: 11, label: `状态`, visible: true }
      ],
      productOpen: false,
      productLoading: true,
      productList: [],
      productTotal: 0,
      productParams: {
        pageNum: 1,
        pageSize: 10,
        productName: undefined
      },
      isUpdate: false
    }
  },
  created() {
    this.getCategory()
  },
  methods: {
    // 检查价格是否有变化
    handleCheckPrice() {
      checkUnsalablePrice().then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          this.isUpdate = !!data.length
        } else this.$message.error(msg)
      })
    },
    // 价格有变化更新价格或忽略价格
    handleUpdatePrice(key = 'refresh') {
      updateUnsalablePrice({ key }).then(res => {
        const { code, msg } = res
        if (code === 200) {
          this.isUpdate = false
          this.$message.success('操作成功')
        } else this.$message.error(msg)
      })
    },
    // 查询分类
    getCategory() {
      category.getlist().then(res => {
        if (res.code === 200) {
          const data = [...[{ id: -1, name: '全部' }], ...res.data]
          this.categoryList = data
          this.queryParams.categoryId = data[0].id
          this.getList()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 查询数据
    async getList() {
      this.loading = true
      const query = { ...this.queryParams }
      if (query.categoryId === -1) query.categoryId = undefined
      const res = await listUnsalable(query)
      if (res.code === 200) {
        this.list = res.rows
        this.loading = false
        this.total = res.total
        this.key = Math.random()
        if (!!res.total) this.handleCheckPrice()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 切换分类
    handleCategory(item) {
      this.queryParams.categoryId = item.id
      this.handleQuery()
    },
    // 删除产品
    // prettier-ignore
    handleDelete(row) {
      this.$modal.confirm('是否删除此产品?').then(function () {
        return delUnsalable({ ids: row.id })
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess('删除成功')
      }).catch(() => {
      })
    },
    // 上下架
    // prettier-ignore
    handlePutaway(row, status) {
      const that = this
      const data = { ids: [row.id], status }
      changeUnsalableStatus(data).then(res => {
        if (res.code === 200) {
          that.$message.success('操作成功')
        } else {
          that.$message.error(res.msg)
          row.status = status === -1 ? 1 : -1
        }
      })
    },
    // 打开私域产品列表
    async handleProduct() {
      await this.getProductList()
      this.productOpen = true
    },
    // 查询私域产品
    getProductList() {
      this.productLoading = true
      listPrivateduct(this.productParams).then(res => {
        if (res.code === 200) {
          this.productList = res.rows
          this.productTotal = res.total
          this.productLoading = false
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 搜索
    handleProductQuery() {
      this.queryParams.pageNum = 1
      this.getProductList()
    },
    // 重置搜索
    resetProductQuery() {
      this.resetForm('queryForm')
      this.handleProductQuery()
    },
    // 私域产品详情
    handleDetail(item, type) {
      this.$refs.productInfo.handleView(item, type)
    },
    // 私域产品图片预览
    handleImg(row) {
      this.$refs.productInfo.handleImgView(row)
    },
    // 新增产品
    handleAdd(row) {
      this.productOpen = false
      this.$refs.create.handleAdd(row)
    },
    // 修改产品
    handleUpdate(row) {
      this.$refs.create.handleUpdate(row)
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
.tableBox {
  padding: 20px;
}
.custom-search {
  align-items: center;
  justify-content: space-between;
}
.custom-search-tip {
  background-color: #fef0f0;
  color: #f56c6c;
  padding: 10px 20px;
  font-size: 20px;
  display: inline-flex;
  align-items: center;
  span {
    font-size: 14px;
    margin-left: 10px;
    margin-right: 30px;
  }
}
</style>
