<template>
  <div class="manager-container">
    <el-tabs v-model="activeTab" class="manager-container-tabs">
      <el-tab-pane label="订单管理" name="order">
        <order-manager />
      </el-tab-pane>
      <el-tab-pane label="合同管理" name="contract">
        <contract-manager />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import OrderManager from './order'
import ContractManager from './contract'

export default {
  name: 'UnsalableManager',
  components: {
    OrderManager,
    ContractManager
  },
  data() {
    return {
      activeTab: 'order'
    }
  }
}
</script>

<style lang="scss" scoped>
.manager-container {
  &-tabs {
    ::v-deep .el-tabs__nav-wrap {
      padding-left: 20px;
    }
  }
}
</style>
