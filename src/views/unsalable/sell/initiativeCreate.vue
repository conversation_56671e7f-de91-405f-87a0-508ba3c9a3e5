<template>
  <div>
    <el-dialog v-dialogDragBox :title="title" :visible.sync="open" width="1150px" class="custom-dialog">
      <div style="padding: 0 20px">
        <el-form ref="form" :model="form" :rules="rules" label-width="8em">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="买方" prop="buyerName">
                <el-input v-model="form.buyerName" placeholder="请输入买方" readonly @focus="handleChooseBuyer">
                  <el-button type="text" slot="suffix" icon="el-icon-search" @click="handleChooseBuyer"></el-button>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="卖方" prop="sellerName">
                <el-input v-model="form.sellerName" placeholder="请输入卖方" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="签订时间" prop="signingTime">
                <el-date-picker v-model="form.signingTime" type="date" placeholder="请选择日期" clearable style="width: 100%" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="签订地点" prop="address">
                <el-input v-model="form.address" placeholder="请输入签订地点" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="参与人" prop="participantUsers">
                <el-cascader style="width: 100%" v-model="form.participantUsers" :options="approvalsOptions" :props="approvalsProps" clearable></el-cascader>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-table :data="form.products" style="width: 100%" class="custom-table custom-table-cell0 custom-table-form">
                <el-table-column type="index" align="center" label="序号" width="50"></el-table-column>
                <el-table-column align="center" prop="productName" label="产品名称" show-overflow-tooltip>
                  <template slot-scope="{ row }">
                    <span class="table-link pointer" @click="handleDetail(row)">{{ row.productName }}</span>
                  </template>
                </el-table-column>
                <el-table-column align="center" prop="picture1" label="产品图片" show-overflow-tooltip>
                  <template slot-scope="{ row }">
                    <template v-if="row.picture1_oss || row.picture1">
                      <image-preview :src="row.picture1_oss || row.picture1" :width="50" :height="50" />
                    </template>
                    <span v-else>-</span>
                  </template>
                </el-table-column>
                <el-table-column align="center" prop="model" label="产品型号" show-overflow-tooltip></el-table-column>
                <!-- <el-table-column align="center" prop="materialQuality" label="产品材质" show-overflow-tooltip></el-table-column> -->
                <el-table-column align="center" prop="surface" label="表面处理" show-overflow-tooltip></el-table-column>
                <el-table-column align="center" prop="useQty" label="产品库存" show-overflow-tooltip></el-table-column>
                <el-table-column align="center" label="产品单价" min-width="200">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`products.${scope.$index}.amount`" :rules="rules.amount">
                      <div class="table-input-select">
                        <el-input v-model="scope.row.amount" size="small" placeholder="请输入产品单价"></el-input>
                        <el-select size="small" v-model="scope.row.unit" filterable>
                          <el-option v-for="item in unitOptions" :key="item" :label="`元/` + item" :value="item"></el-option>
                        </el-select>
                      </div>
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column align="center" label="采购数量" width="280">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`products.${scope.$index}.quantity`" :rules="rules.quantity">
                      <div class="inline-flex">
                        <el-input size="small" v-model="scope.row.quantity" placeholder="请输入采购数量" clearable>
                          <span slot="suffix" class="inline-flex">{{ scope.row.unit }}</span>
                        </el-input>
                        <el-button class="table-btn danger" @click="handleDelete(scope.$index)">删除</el-button>
                      </div>
                    </el-form-item>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
            <el-col :span="24">
              <div class="create-btn-box">
                <el-button class="create-btn" icon="el-icon-plus" @click="handleOpenProduct">添加合同产品</el-button>
              </div>
            </el-col>
            <el-col :span="24">
              <el-form-item label="合同照片" prop="file">
                <image-upload v-model="form.file" />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <div class="contactTitle">买方/卖方信息确认</div>
              <div class="contactInfo">
                <ul>
                  <li>
                    <span>卖方：</span>
                    <b>
                      <el-input v-model="form.sellerName" size="mini" class="info-input" style="width: 250px"></el-input>
                    </b>
                  </li>
                  <li>
                    <span>买方：</span>
                    <b>
                      <el-input v-model="form.buyerName" size="mini" class="info-input" style="width: 250px"></el-input>
                    </b>
                  </li>
                  <li class="inline-flex">
                    <span>法定代表人：</span>
                    <b>
                      <el-input v-model="form.sellerInfo.nickName" size="mini" class="info-input" style="width: 120px"></el-input>
                    </b>
                  </li>
                  <li class="inline-flex">
                    <span>法定代表人：</span>
                    <b>
                      <el-input v-model="form.buyerInfo.nickName" size="mini" class="info-input" style="width: 120px"></el-input>
                    </b>
                  </li>
                  <li class="inline-flex">
                    <span>委托代理人：</span>
                    <b>
                      <el-input v-model="form.sellerInfo.consignor" size="mini" class="info-input" style="width: 80px"></el-input>
                      <el-button icon="el-icon-search" circle size="mini" style="margin-left: 10px" @click="handleChoose('contact', 'seller')"></el-button>
                    </b>
                  </li>
                  <li class="inline-flex">
                    <span>委托代理人：</span>
                    <b>
                      <el-input v-model="form.buyerInfo.consignor" size="mini" class="info-input" style="width: 80px"></el-input>
                      <el-button icon="el-icon-search" circle size="mini" style="margin-left: 10px" @click="handleChoose('contact', 'buyer')"></el-button>
                    </b>
                  </li>
                  <li class="inline-flex">
                    <span>电话和微信号码：</span>
                    <b>
                      <el-input v-model="form.sellerInfo.phone" size="mini" class="info-input" style="width: 120px"></el-input>
                    </b>
                  </li>
                  <li class="inline-flex">
                    <span>电话和微信号码：</span>
                    <b>
                      <el-input v-model="form.buyerInfo.phone" size="mini" class="info-input" style="width: 120px"></el-input>
                    </b>
                  </li>
                  <li class="inline-flex">
                    <span>开户行：</span>
                    <b>
                      <el-input v-model="form.sellerInfo.bank" size="mini" class="info-input"></el-input>
                      <el-button icon="el-icon-search" circle size="mini" style="margin-left: 10px" @click="handleChoose('bank', 'seller')"></el-button>
                    </b>
                  </li>
                  <li class="inline-flex">
                    <span>开户行：</span>
                    <b>
                      <el-input v-model="form.buyerInfo.bank" size="mini" class="info-input"></el-input>
                      <el-button icon="el-icon-search" circle size="mini" style="margin-left: 10px" @click="handleChoose('bank', 'buyer')"></el-button>
                    </b>
                  </li>
                  <li class="inline-flex">
                    <span>账号：</span>
                    <b>
                      <el-input v-model="form.sellerInfo.account" size="mini" class="info-input" style="width: 250px"></el-input>
                    </b>
                  </li>
                  <li class="inline-flex">
                    <span>账号：</span>
                    <b>
                      <el-input v-model="form.buyerInfo.account" size="mini" class="info-input" style="width: 250px"></el-input>
                    </b>
                  </li>
                  <li class="inline-flex">
                    <span>地址：</span>
                    <b>
                      <el-input v-model="form.sellerInfo.address" size="mini" class="info-input" style="width: 350px"></el-input>
                    </b>
                  </li>
                  <li class="inline-flex">
                    <span>地址：</span>
                    <b>
                      <el-input v-model="form.buyerInfo.address" size="mini" class="info-input" style="width: 350px"></el-input>
                    </b>
                  </li>
                </ul>
              </div>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button class="custom-dialog-btn" @click="open = false">取 消</el-button>
        <el-tooltip effect="dark" content="请添加合同产品" placement="top" :disabled="!!form.products.length">
          <el-button class="custom-dialog-btn primary" :class="{ disabled: !form.products.length }" :disabled="!form.products.length" @click="handleSubmit">确 定</el-button>
        </el-tooltip>
      </span>
    </el-dialog>
    <!--  合同模板  -->
    <contract-tpl ref="contractTpl"></contract-tpl>
    <!--选择产品-->
    <el-dialog v-dialogDragBox title="选择产品" :visible.sync="productOpen" width="1150px" class="custom-dialog">
      <div style="padding: 0 20px">
        <el-form ref="productQuery" :model="productQuery" inline size="small" @submit.native.prevent>
          <el-form-item label="名称" prop="productName">
            <el-input v-model="productQuery.productName" placeholder="请输入产品名称" clearable @keyup.enter.native="handleProductQuery" style="width: auto" />
          </el-form-item>
          <el-form-item label="编码" prop="productCode">
            <el-input v-model="productQuery.productCode" placeholder="请输入产品编码" clearable @keyup.enter.native="handleProductQuery" />
          </el-form-item>
          <el-form-item label="物料编码" prop="materialCode">
            <el-input v-model="productQuery.materialCode" placeholder="请输入物料编码" clearable @keyup.enter.native="handleProductQuery" />
          </el-form-item>
          <el-form-item label="规格" prop="specs">
            <el-input v-model="productQuery.specs" placeholder="请输入产品规格" clearable @keyup.enter.native="handleProductQuery" />
          </el-form-item>
          <el-form-item label="型号" prop="model">
            <el-input v-model="productQuery.model" placeholder="请输入产品型号" clearable @keyup.enter.native="handleProductQuery" />
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select v-model="productQuery.status" placeholder="请选择状态">
              <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="单位" prop="unit">
            <el-select v-model="productQuery.unit" placeholder="请选择单位">
              <el-option v-for="(item, index) in unitOptions" :key="index" :label="item" :value="item"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="small" @click="handleProductQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="small" @click="resetProductQuery">重置</el-button>
          </el-form-item>
        </el-form>
        <el-table v-loading="productLoading" ref="productTable" stripe :data="productList" row-key="id" style="width: 100%" @row-click="selectProduct" @select="handleSelectProduct" @select-all="handleSelectProductAll" :reserve-selection="true" class="custom-table custom-table-cell5">
          <el-table-column align="center" type="selection" width="50"></el-table-column>
          <el-table-column align="center" type="index" label="序号"></el-table-column>
          <el-table-column align="center" prop="product_name" label="产品名称" show-overflow-tooltip>
            <template slot-scope="{ row }">
              <span class="table-link pointer" @click="handleDetail(row)">{{ row.productName }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="productCode" label="产品编码" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="materialCode" label="物料编码" show-overflow-tooltip v-if="companyId == 14">
            <template slot-scope="{ row }">{{ row.materialCode || '-' }}</template>
          </el-table-column>
          <el-table-column align="center" prop="picture1" label="图片" width="75">
            <template slot-scope="{ row }">
              <template v-if="row.picture1_oss || row.picture1">
                <image-preview :src="row.picture1_oss || row.picture1" :width="50" :height="50" />
              </template>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="specs" label="规格" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="model" label="型号" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="materialQuality" label="材质" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="unit" label="单位" width="50"></el-table-column>
          <el-table-column align="center" label="操作">
            <template slot-scope="{ row }">
              <el-button type="text" size="mini" icon="el-icon-circle-check" @click.stop="selectProduct(row)">选择</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="custom-pagination">
          <pagination v-show="productTotal > 0" :total="productTotal" :page.sync="productQuery.pageNum" :limit.sync="productQuery.pageSize" @pagination="getProductList" />
        </div>
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn" @click="productOpen = false">取消</el-button>
        <el-button :disabled="!productChecked.length" class="custom-dialog-btn primary" :class="{ disabled: !productChecked.length }" @click="handleConfirmProduct">确定</el-button>
      </div>
    </el-dialog>
    <!-- 产品详情 -->
    <product-dialog ref="productInfo"></product-dialog>
    <!--选择委托代理人、选择开户行-->
    <el-dialog v-dialogDragBox :title="chooseTitle" :visible.sync="chooseOpen" width="1150px" class="custom-dialog">
      <div style="padding: 0 20px">
        <el-table :data="chooseList" style="width: 100%" :key="chooseKey" class="custom-table" @row-click="handleChooseClick">
          <template v-if="chooseType === 'contact'">
            <el-table-column align="center" label="选择" width="55">
              <template slot-scope="{ row }">
                <el-radio v-model="chooseChecked.nickName" :label="row.nickName"><span /></el-radio>
              </template>
            </el-table-column>
            <el-table-column prop="nickName" label="联系人姓名" align="center"></el-table-column>
            <el-table-column prop="phone" label="联系人电话" align="center"></el-table-column>
            <el-table-column prop="post" label="联系人职务" align="center"></el-table-column>
          </template>
          <template v-if="chooseType === 'bank'">
            <el-table-column align="center" label="选择" width="55">
              <template slot-scope="{ row }">
                <el-radio v-model="chooseChecked.bankNo" :label="row.bankNo"><span /></el-radio>
              </template>
            </el-table-column>
            <el-table-column prop="bankName" label="开户行" align="center"></el-table-column>
            <el-table-column prop="bankUser" label="账户名" align="center"></el-table-column>
            <el-table-column prop="bankNo" label="银行账号" align="center"></el-table-column>
          </template>
        </el-table>
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn" @click="chooseOpen = false">取 消</el-button>
        <el-button class="custom-dialog-btn primary" :class="{ disabled: isEmptyObject(chooseChecked) }" :disabled="isEmptyObject(chooseChecked)" @click="handleChooseConfirm">确 定</el-button>
      </div>
    </el-dialog>
    <!--选择买方-->
    <el-dialog v-dialogDragBox :title="chooseBuyerTitle" :visible.sync="chooseBuyerOpen" width="1150px" class="custom-dialog">
      <div style="margin-top: -20px">
        <div style="padding: 0 20px">
          <el-tabs v-model="buyerType" @tab-click="handleBuyerType" v-if="companyId == 14">
            <el-tab-pane label="自由客客户" name="ziyouke"></el-tab-pane>
            <el-tab-pane label="金蝶客户" name="kingdee"></el-tab-pane>
          </el-tabs>
        </div>
        <client-list ref="client" :is-choose="true" :choose-checked.sync="chooseChecked" v-show="buyerType === 'ziyouke'" :show-tip="true" />
        <customer ref="customer" :isPopup="true" @selectCustomer="selectCustomer" showUseOrg v-show="buyerType === 'kingdee'"></customer>
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn" @click="chooseBuyerOpen = false">取 消</el-button>
        <el-button class="custom-dialog-btn primary" :class="{ disabled: isEmptyObject(chooseChecked) && isEmptyObject(kingdeeCustomer) }" :disabled="isEmptyObject(chooseChecked) && isEmptyObject(kingdeeCustomer)" @click="handleChooseBuyerConfirm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { supplier } from '@/api/system/user'
import contractTpl from '@/views/purchase/contract/template'
import { parseTime } from '@/utils/ruoyi'
import ProductDialog from '@/views/public/product/dialog.vue'
import { listPrivateduct } from '@/api/system/privateduct'
import ClientList from '@/views/client'
import { privateSupb } from '@/api/houtai/siyu/gongying'
import { createContract, createContractSerial, contractParticipantList } from '@/api/purchase'
import { isNumber, isNumberLength } from '@/utils/validate'
import customer from '@/views/kingdee/customer/index'
import { addlist, getlist } from '@/api/houtai/siyu/gongying'
import { inventoryList, inventoryQty } from '@/api/inventory'

export default {
  name: 'InitiativeCreate',
  components: { ClientList, ProductDialog, contractTpl, customer },
  data() {
    var checkNum = (rule, value, callback) => {
      if (value <= 0) {
        callback(new Error('采购量不正确'))
      } else {
        callback()
      }
    }
    return {
      title: '添加合同',
      open: false,
      form: {
        products: [],
        buyerInfo: {
          nickName: undefined,
          consignor: undefined,
          phone: undefined,
          bank: undefined,
          account: undefined,
          address: undefined
        },
        sellerInfo: {
          nickName: undefined,
          consignor: undefined,
          phone: undefined,
          bank: undefined,
          account: undefined,
          address: undefined
        }
      },
      rules: {
        buyerName: [{ required: true, message: '请选择买方', trigger: ['blur', 'change'] }],
        products: [{ required: true, message: '请添加合同产品', trigger: ['blur', 'change'] }],
        amount: [
          { required: true, message: '请输入产品单价', trigger: 'blur' },
          { validator: isNumber, message: '请输入正确的金额', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 10), message: '只可以填写十位小数', trigger: 'blur' }
        ],
        quantity: [
          { required: true, message: '请输入计划采购数量', trigger: 'blur' },
          { validator: checkNum, message: '采购量不正确', trigger: 'blur' },
          { validator: isNumber, message: '请输入正确的数量', trigger: 'blur' }
        ]
      },
      unitOptions: ['吨', '千克', '个', '件', '套', '米', '支', '根', '袋', '托'],
      sellerInfo: {}, // 卖方信息
      buyerInfo: {}, // 买方信息
      // 选择产品
      productOpen: false,
      productList: [],
      productChecked: [],
      productLoading: true,
      productTotal: 0,
      productQuery: {
        pageNum: 1,
        pageSize: 10,
        productName: undefined,
        productCode: undefined,
        specs: undefined,
        unit: undefined,
        status: 1
      },
      statusOptions: [
        { label: '上架', value: 1 },
        { label: '下架', value: 0 }
      ],
      // 选择委托代理人、选择开户行
      chooseTitle: '选择委托代理人',
      chooseOpen: false,
      chooseList: [],
      chooseType: 'contact',
      chooseCategory: 'seller',
      chooseChecked: {},
      chooseKey: 1,
      // 选择买方信息
      chooseBuyerTitle: '选择买方',
      chooseBuyerOpen: false,
      chooseBuyerChecked: {},
      buyerType: 'ziyouke',
      kingdeeCustomer: {},
      userList: [],
      approvalsOptions: [],
      approvalsProps: {
        expandTrigger: 'hover',
        multiple: true,
        emitPath: false
      }
    }
  },
  computed: {
    // 获取企业ID
    companyId() {
      return this.$store.state.user.companyId
    }
  },
  methods: {
    // 表单重置
    reset() {
      this.form = {
        buyerName: undefined, // 买方
        sellerName: undefined, // 卖方
        serial: undefined, // 合同编号
        address: undefined, // 签订地点
        signingTime: undefined, // 签订时间
        isIncludingTax: false, // 是否含税
        participantUsers: [],
        conTip: '不包含运费、安装费', // 合同备注
        transportText1: '买方',
        transportText2: '卖方',
        addressText1: '买方到卖方厂区自提',
        addressText2: '买方指定具体收货地点',
        addressName: '河北世盛金属制品有限公司',
        checkText: '收到产品后3日内',
        finalText1: '合同签订当日，买方支付产品全款',
        finalText2: '买方收到产品后1日内支付全款',
        disputeText: '合同签订地',
        effectiveText: '双方签字或盖章',
        source: 'private', // 合同来源
        products: [],
        file: undefined,
        demandId: -1,
        seller: undefined,
        sendUser: undefined,
        sendPhone: undefined,
        publishWay: 'private',
        buyerInfo: {
          nickName: undefined,
          consignor: undefined,
          phone: undefined,
          bank: undefined,
          account: undefined,
          address: undefined
        },
        sellerInfo: {
          nickName: undefined,
          consignor: undefined,
          phone: undefined,
          bank: undefined,
          account: undefined,
          address: undefined
        }
      }
      this.resetForm('form')
    },
    // 打开新增窗口
    async handleOpen(approvalsOptions = [], userList = []) {
      this.reset()
      this.approvalsOptions = JSON.parse(JSON.stringify(approvalsOptions))
      this.userList = JSON.parse(JSON.stringify(userList))
      if (this.companyId && this.companyId != -1) {
        const sellerInfo = await supplier({ id: this.companyId })
        this.sellerInfo = sellerInfo.data
        this.setSellerInfo(sellerInfo.data)
      }
      this.form.signingTime = parseTime(new Date(), '{y}-{m}-{d}')
      this.form.address = '河北省邯郸市永年区'
      const params = { type: 'manualInputContract' }
      contractParticipantList(params).then(res => {
        const { code, msg, data } = res
        if (code !== 200) return this.$message.error(msg)
        this.form.participantUsers = data?.map(item => item.userId) || []
        // 递归处理树形结构的 approvalsOptions
        const processOptions = options => {
          options.forEach(option => {
            const match = data.find(item => item.userId === option.id)
            if (match) {
              option.disabled = true
            }
            // 递归处理子节点
            if (option.children && option.children.length > 0) {
              processOptions(option.children)
            }
          })
        }
        if (this.approvalsOptions.length > 0) {
          processOptions(this.approvalsOptions)
        }
        this.open = true
      })
    },
    // 设置卖方信息
    setSellerInfo(data = {}) {
      if (!data) return
      this.form.sellerName = data.supplier.name
      const contact = data.contacts && data.contacts.length > 0 ? data.contacts.find(item => item.checked) || data.contacts[0] : {}
      const bank = data.supplier.bankList && data.supplier.bankList.length > 0 ? data.supplier.bankList.find(item => item.checked) || data.supplier.bankList[0] : {}
      this.form.sellerInfo = {
        nickName: data.supplier.legal,
        consignor: contact.nickName,
        phone: contact.phone,
        bank: bank.bankName,
        account: bank.bankNo,
        address: data.company.address
      }
    },
    // 切换合同模板
    handleChangeTpl(data) {
      this.$refs.contractTpl.getList(true, data, true)
    },
    // 选择合同模板
    handleSubmitTpl(data) {
      const { file } = data
      this.form.file = file
    },
    // 提交
    handleSubmit() {
      this.$refs.form.validate(async valid => {
        if (valid) {
          const contract = await createContractSerial()
          const { form } = this
          let data = {
            buyerName: form.sellerName,
            sellerName: form.buyerName,
            serial: contract.data.serial,
            address: form.address,
            signingTime: form.signingTime,
            isIncludingTax: form.isIncludingTax,
            participantUsers: [],
            conTip: form.conTip,
            transportText1: form.transportText1,
            transportText2: form.transportText2,
            addressText1: form.addressText1,
            addressText2: form.addressText2,
            addressName: form.addressName,
            checkText: form.checkText,
            finalText1: form.finalText1,
            finalText2: form.finalText2,
            disputeText: form.disputeText,
            effectiveText: form.effectiveText,
            sellerInfo: form.buyerInfo,
            buyerInfo: form.sellerInfo,
            source: form.source,
            products: form.products.map(item => {
              return {
                listId: -1,
                amount: Number(item.amount),
                originAmount: Number(item.amount),
                productId: item.id,
                materialNumber: item.materialCode,
                productName: item.productName,
                quantity: Number(item.quantity),
                sjNum: Number(item.quantity),
                source: 'private',
                unit: item.unit,
                replyUnit: item.unit
              }
            }),
            file: form.file || 'default.jpg',
            demandId: form.demandId,
            seller: this.buyerInfo.id,
            sendUser: form.sellerInfo.consignor,
            sendPhone: form.sellerInfo.phone,
            publishWay: form.publishWay,
            type: 'manualInput'
          }
          const participantUsers = []
          form.participantUsers.forEach(item => {
            const user = this.userList.find(user => user.userId === item)
            if (user) {
              participantUsers.push({
                userId: user?.userId || '',
                userName: user?.userName || ''
              })
            }
          })
          data.participantUsers = participantUsers
          createContract(data).then(res => {
            const { code, msg } = res
            if (code === 200) {
              this.$message.success('成功录入合同')
              this.open = false
              this.$emit('refresh')
            } else this.$message.error(msg)
          })
        }
      })
    },
    // 打开产品列表
    handleOpenProduct() {
      this.productChecked = [...this.form.products]
      this.resetProductQuery()
    },
    // 搜索产品列表
    handleProductQuery() {
      this.productQuery.pageNum = 1
      this.getProductList()
    },
    // 重置搜索产品列表
    resetProductQuery() {
      this.resetForm('productQuery')
      this.handleProductQuery()
    },
    // 产品列表
    getProductList() {
      this.productLoading = true
      listPrivateduct(this.productQuery).then(async res => {
        const { code, msg, rows, total } = res
        if (code === 200) {
          await Promise.all(
            rows.map(async item => {
              const res = await inventoryList({ number: item.materialCode, pageNum: 1, pageSize: 1 })
              const { code, rows, msg } = res
              if (code === 200 ) {
                item.useQty = rows[0] ? rows[0].useQty : '--'
              } else this.$message.error(msg)
            })
          )
          this.productList = rows
          this.productTotal = total
          this.productLoading = false
          if (!this.productOpen) this.productOpen = true
          this.echo()
        } else this.$message.error(msg)
      })
    },
    // 删除产品
    handleDelete(index = -1) {
      if (index == -1) return
      this.form.products.splice(index, 1)
    },
    // 选择产品
    selectProduct(row) {
      const idx = this.productChecked.findIndex(item => item.id === row.id)
      if (idx === -1) {
        this.productChecked.push(row)
      } else {
        this.productChecked.splice(idx, 1)
      }
      this.echo()
    },
    // 选择产品
    handleSelectProduct(selection, row) {
      const idx = this.productChecked.findIndex(item => item.id === row.id)
      if (idx === -1) {
        this.productChecked.push(row)
      } else {
        this.productChecked.splice(idx, 1)
      }
    },
    // 全选产品
    handleSelectProductAll(selection) {
      if (selection.length) {
        if (this.productChecked.length) {
          selection.map(ite => {
            const idx = this.productChecked.findIndex(item => item.id === ite.id)
            if (idx === -1) this.productChecked.push(ite)
          })
        } else {
          this.productChecked = [...selection]
        }
      } else {
        this.productList.map(ite => {
          const idx = this.productChecked.findIndex(item => item.id === ite.id)
          if (idx !== -1) this.productChecked.splice(idx, 1)
        })
      }
    },
    // 回显选中
    echo() {
      this.$nextTick(() => {
        if (this.$refs.productTable) this.$refs.productTable.clearSelection()
        this.productList.map(item => {
          this.productChecked.map(itt => {
            if (itt.id === item.id) this.$refs.productTable.toggleRowSelection(item, true)
          })
        })
      })
    },
    // 确定选择产品
    handleConfirmProduct() {
      this.form.products = [...this.productChecked]
      this.productOpen = false
    },
    // 产品详情
    handleDetail(item, type) {
      this.$refs.productInfo.handleView(item, type)
    },
    // 选择委托代理人、选择开户行
    handleChoose(type = 'contact', category = 'seller') {
      this.chooseType = type
      this.chooseCategory = category
      this.chooseTitle = type === 'contact' ? '选择委托代理人' : '选择开户行'
      const { sellerInfo, buyerInfo } = this.form
      let list = []
      let check = {}
      if (category === 'seller') {
        if (type === 'contact') {
          list = this.sellerInfo.contacts || []
          check = (this.sellerInfo.contacts && this.sellerInfo.contacts.find(item => item.nickName === sellerInfo.consignor && item.phone === sellerInfo.phone)) || {}
        } else {
          list = (this.sellerInfo.supplier && this.sellerInfo.supplier.bankList) || []
          check = (this.sellerInfo.supplier && this.sellerInfo.supplier.bankList.find(item => item.bankName === sellerInfo.bank && item.bankNo === sellerInfo.account)) || {}
        }
      } else {
        if (type === 'contact') {
          list = this.buyerInfo.contactList || []
          check = (this.buyerInfo.contactList && this.buyerInfo.contactList.find(item => item.nickName === buyerInfo.consignor && item.phone === buyerInfo.phone)) || {}
        } else {
          list = this.buyerInfo.bankList || []
          check = (this.buyerInfo.bankList && this.buyerInfo.bankList.find(item => item.bankName === buyerInfo.bank && item.bankNo === buyerInfo.account)) || {}
        }
      }
      this.chooseList = list
      this.chooseChecked = check
      this.chooseKey = Math.random()
      this.chooseOpen = true
    },
    // 判断json是否为空
    isEmptyObject(obj) {
      return JSON.stringify(obj) === '{}'
    },
    // 选择委托代理人、选择开户行
    handleChooseClick(row) {
      this.chooseChecked = row
    },
    // 确定选择委托代理人、选择开户行
    handleChooseConfirm() {
      const { chooseChecked, chooseType, chooseCategory } = this
      if (chooseCategory === 'seller') {
        if (chooseType === 'contact') {
          this.form.sellerInfo.consignor = chooseChecked.nickName
          this.form.sellerInfo.phone = chooseChecked.phone
        } else {
          this.form.sellerInfo.bank = chooseChecked.bankName
          this.form.sellerInfo.account = chooseChecked.bankNo
        }
      } else {
        if (chooseType === 'contact') {
          this.form.buyerInfo.consignor = chooseChecked.nickName
          this.form.buyerInfo.phone = chooseChecked.phone
        } else {
          this.form.buyerInfo.bank = chooseChecked.bankName
          this.form.buyerInfo.account = chooseChecked.bankNo
        }
      }
      this.chooseOpen = false
    },
    // 选择买方（客户）
    handleChooseBuyer() {
      this.buyerType = 'ziyouke'
      this.kingdeeCustomer = {}
      this.chooseChecked = this.buyerInfo || {}
      this.chooseBuyerOpen = true
      this.$nextTick(() => {
        if (this.$refs.client) this.$refs.client.handleResetQuery()
      })
    },
    // 确定选择买方
    handleChooseBuyerConfirm() {
      if (this.buyerType === 'ziyouke') {
        const { id } = this.chooseChecked
        if (!id) return
        this.handleChooseBuyerConfirmInfo(id)
      } else if (this.buyerType === 'kingdee') {
        const { Name } = this.kingdeeCustomer
        if (!Name) return
        getlist({ name: Name, type: 'kh' }).then(res => {
          const { code, rows } = res
          if (code === 200) {
            const obj = rows.find(item => item.name === Name)
            if (obj) this.handleChooseBuyerConfirmInfo(obj.id)
            else this.handleAddKingdeeToZiyouke(Name)
          }
        })
      }
    },
    // 金蝶客户新增至自由客
    handleAddKingdeeToZiyouke(name) {
      addlist({ name, type: 'kh' }).then(res => {
        const { code, data } = res
        if (code === 200) this.handleChooseBuyerConfirmInfo(data)
      })
    },
    // 确定选择买方并获取信息
    handleChooseBuyerConfirmInfo(id = undefined) {
      if (!id) return
      privateSupb({ id }).then(res => {
        const { code, data, msg } = res
        if (code === 200) {
          this.buyerInfo = data
          this.form.buyerName = data.name
          const { contactList, bankList } = data
          const contact = contactList && contactList.length > 0 ? contactList.find(item => item.checked) || contactList[0] : {}
          const bank = bankList && bankList.length > 0 ? bankList.find(item => item.checked) || bankList[0] : {}
          this.form.buyerInfo = {
            nickName: data.legal,
            consignor: contact.nickName,
            phone: contact.phone,
            bank: bank.bankName,
            account: bank.bankNo,
            address: data.address
          }
          this.chooseBuyerOpen = false
        } else this.$message.error(msg)
      })
    },
    // tab切换
    handleBuyerType(e) {
      this.buyerType = e.name
      this.chooseChecked = {}
      this.kingdeeCustomer = {}
      if (e.name === 'kingdee') {
        this.$nextTick(() => {
          if (this.$refs.customer) this.$refs.customer.handleResetQuery()
        })
      }
    },
    // 选择金蝶客户
    selectCustomer(row) {
      this.kingdeeCustomer = row
    }
  }
}
</script>
<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';
.custom-table-form {
  ::v-deep {
    tbody {
      .el-table__cell {
        .cell {
          padding-top: 15px;
          padding-bottom: 15px;
        }
      }
    }
    .el-form-item {
      margin-bottom: 0;
      .el-form-item__error {
        padding-top: 2px;
      }
    }
  }
}
::v-deep {
  .el-form-item {
    .el-form-item__label {
      display: inline-flex;
      align-items: center;
      min-height: 40px;
      font-weight: normal;
      line-height: 20px;
    }
  }
  .table-btn {
    padding: 0;
  }
  .is-error {
    .table-input-select {
      border-color: #f56c6c;
    }
  }
  .table-input-select {
    display: flex;
    align-items: center;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    line-height: 32px;
    .el-input {
      .el-input__inner {
        border: 0;
      }
    }
    .el-select {
      width: 110px;
      .el-input__inner {
        padding-left: 0;
      }
    }
    &:focus-within {
      border-color: #409eff;
    }
  }
  .create-btn-box {
    display: flex;
    padding: 20px 0;
    align-items: center;
    justify-content: space-between;
  }
  .create-btn {
    border: 1px solid $blue;
    background: #e4edff;
    color: $blue;
    &:hover {
      background-color: $blue;
      color: $white;
    }
  }
  .contactTitle {
    font-size: 14px;
    font-weight: 500;
    line-height: 3em;
    color: $info;
  }
  .contactInfo {
    width: 100%;
    ul {
      width: 100%;
      list-style: none;
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      padding: 0;
      margin: 0;
      border: 1px solid #cbd6e2;
      border-radius: 5px;
      border-bottom-width: 0;
      overflow: hidden;
    }
    li {
      width: 50%;
      line-height: 20px;
      padding: 15px 20px;
      border-right: 1px solid #cbd6e2;
      border-bottom: 1px solid #cbd6e2;
      font-size: 12px;
      &:nth-child(2n) {
        border-right-width: 0;
      }
      &:nth-child(4n + 1),
      &:nth-child(4n + 2) {
        background-color: #f8f9fb;
      }
      span {
        color: $disabled;
        margin-right: 20px;
      }
      b {
        display: inline-flex;
        align-items: center;
        color: $font;
        font-weight: 500;
      }
    }
    .info-input {
      .el-input__inner {
        border-width: 0;
        border-bottom-width: 1px;
        border-radius: 0;
        background-color: transparent;
        text-align: center;
        font-size: 14px;
        color: $font;
      }
      &.input-date {
        .el-input__inner {
          text-align: left;
        }
      }
      &.input-left {
        .el-input__inner {
          text-align: left;
        }
      }
    }
  }
}
</style>
