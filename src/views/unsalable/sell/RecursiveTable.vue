<template>
  <div>
    <div class="table-child">
      <div class="table-child-title">
        <div>
          产品BOM
          <br />
          物料清单
          <br />
        </div>
        <span style="color: #2e73f3; margin-top: 12px; cursor: pointer" @click="hanldeProcessView">查看工艺</span>
      </div>
      <el-table v-loading="loading" :ref="`child${Math.random()}`" :data="items" style="width: 100%" stripe
        :row-key="getRowKey" :row-class-name="getRowClass" @expand-change="expandChange"
        class="custom-table custom-table-cell5" :default-expand-all="false">
        <el-table-column align="left" type="expand">
          <template slot-scope="scope">
            <recursive-table v-if="scope.row.children && scope.row.children.length" :items="scope.row.children"
              :showCheckbox="showCheckbox" :bomId="scope.row.bomId" :purchasingProducts="childPurchasingProducts"
              @handleChooseProduct="handleChooseProduct" @handleCancelProduct="handleCancelProduct" />
          </template>
        </el-table-column>
        <el-table-column align="center" label="" width="50" v-if="showCheckbox">
          <template slot-scope="{ row }">
            <el-checkbox-group v-model="childPurchasingProducts">
              <el-checkbox :label="row" :disabled="row.hasChildren"
                @change="handleSelectProduct($event, row)"><span></span></el-checkbox>
            </el-checkbox-group>
          </template>
        </el-table-column>
        <el-table-column align="center" type="index" label="序号"></el-table-column>
        <el-table-column align="center" prop="product_name" label="物料名称" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <span class="table-link" @click="handleProductView(row)">{{ row.productName }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="specs" label="规格" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" prop="model" label="型号" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" prop="materialQuality" label="材质" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" prop="unit" label="单位" width="50"></el-table-column>
        <el-table-column align="center" label="所需数量">
          <template slot-scope="{ row }">{{ row.quantity + row.unit }}</template>
        </el-table-column>
        <el-table-column align="center" label="金蝶库存" prop="jdStock"></el-table-column>
        <el-table-column align="center" label="自由客库存" prop="stock">
          <!-- <template slot-scope="{ row }">
            <div class="table-stock" v-if="row.materialCode">
              <el-select v-model="row.orgIds" collapse-tags multiple size="mini" @change="handleRefreshStock(row)">
                <el-option v-for="item in orgIdsOptions" :key="item.value" :label="item.label"
                  :value="item.value"></el-option>
              </el-select>
              <el-tooltip effect="dark" content="点击查询库存">
                <el-button type="text" icon="el-icon-refresh" @click="handleRefreshStock(row)">{{ row.stock || 0
                  }}</el-button>
              </el-tooltip>
            </div>
            <span v-else>-</span>
          </template> -->
        </el-table-column>
        <el-table-column align="center" label="操作" width="100">
          <template slot-scope="{ row }">
            <el-dropdown @command="handleCommand($event, row)" v-if="!showCheckbox">
              <span class="el-dropdown-link" :class="row.doType === 'cg' ? 'dropdown-primary' : 'dropdown-orange'">
                {{ formatDo(row.doType) }}
                <i class="el-icon-caret-bottom"></i>
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item v-for="item in doOptions" :key="item.value" :command="item.value">{{ item.label
                  }}</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <el-button type="text" size="mini" icon="el-icon-circle-check" @click="handleChooseProduct(row)"
              v-if="showCheckbox" :disabled="row.hasChildren">采购</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 产品详情 -->
    <product-dialog ref="productInfo"></product-dialog>
    <!-- 工艺 -->
    <el-dialog v-dialogDragBox title="产品BOM详情" :visible.sync="processOpen" width="1150px" class="custom-dialog"
      :modal-append-to-body="false">
      <div class="productBox">
        <div class="labelTitle">物料清单</div>
        <el-table :data="productList" stripe class="custom-table custom-table-cell5 tables">
          <el-table-column align="center" type="index" label="序号"></el-table-column>
          <el-table-column align="center" prop="product_name" label="物料名称" show-overflow-tooltip>
            <template slot-scope="{ row }">
              <span class="table-link" @click="handleProductView(row.product)">{{ row.product && row.product.productName
                }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="specs" label="规格" show-overflow-tooltip>
            <template slot-scope="{ row }">
              <span>{{ row.product && row.product.specs }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="model" label="型号" show-overflow-tooltip>
            <template slot-scope="{ row }">
              <span>{{ row.product && row.product.model }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="materialQuality" label="材质" show-overflow-tooltip>
            <template slot-scope="{ row }">
              <span>{{ row.product && row.product.materialQuality }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="unit" label="单位" width="50"></el-table-column>
          <el-table-column align="center" label="所需数量">
            <template slot-scope="{ row }">{{ row.quantity + row.unit }}</template>
          </el-table-column>
        </el-table>
      </div>
      <div v-for="it in bomBox.processGroupList" :key="it.groupId" style="margin-bottom: 20px;">
        <div class="labelTitle">
          <span v-if="!it.editShow">{{ it.name }}</span>
          <el-form :inline="true" :model="it" class="formBox" v-if="it.editShow">
            <el-form-item label="">
              <el-input v-model="it.name" placeholder="请输入工艺名称" size="mini"></el-input>
            </el-form-item>
            <el-form-item label="" style="margin-left: 20px;">
              <el-checkbox-group v-model="it.isDefault" size="mini" @change="handleChecked(it, bomBox.processGroupList)">
                <el-checkbox label="设为默认工艺" name="isDefault"></el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-form>
          <el-button type="text" class="edit" v-if="!it.editShow" @click="it.editShow = true">编辑</el-button>
          <el-button type="text" class="edit" v-if="it.editShow" @click="handleSubmit(it)">提交</el-button>
          <el-button type="text" class="edit" v-if="it.editShow" @click="it.editShow = false">取消</el-button>
        </div>
        <div class="craftBox">
          <div class="default" v-if="it.isDefault">默认工艺</div>
          <div class="craftBox_list" v-for="(item, index) in it.processList" :key="item.id">
            <div class="craftBox_list_title">{{ '工序 - ' + (index + 1) }}</div>
            <div class="craftBox_list_concent">
              <div class="concent_top">
                <div class="concent_item">
                  <span class="title">生产工艺</span>
                  <span class="text">{{ item.process && item.process.name }}</span>
                </div>
                <div class="concent_item">
                  <span class="title">工艺费用</span>
                  <span class="fee">{{ item.cost + '元' }}</span>
                </div>
                <div class="concent_item">
                  <span class="title">所用设备</span>
                  <div class="device">
                    <span class="text" v-for="(it, i) in item.process && item.process.equipments" :key="i">{{
                      it.equipment
                      && it.equipment.name }}</span>
                  </div>
                </div>
              </div>
              <div class="concent_bottom">
                <div class="concent_item">
                  <span class="title">生产描述</span>
                  <span class="text">{{ item.description }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
    <!-- 拆分提示 -->
    <el-dialog v-dialogDragBox title="提示" :visible.sync="errOpen" width="748px" class="custom-dialog" append-to-body>
      <div class="errTips">
        <img class="img" src="@/assets/images/errTips.png" alt="" />
        <div class="title">当前产品尚未设置BOM与工艺不可标记为生产</div>
        <div class="text">如要更改请前往产品BOM为该产品增加BOM设置，完成后可以标记为生产</div>
        <div class="btn">
          <div class="cancel" @click="errOpen = false">取消</div>
          <div class="go" @click="handleSetBom">立即设置BOM</div>
        </div>
      </div>
    </el-dialog>
    <!-- 新增BOM -->
    <bom-create ref="bomCreate" @callBack="bomCreate = false" v-if="bomCreate" />
  </div>
</template>

<script>
import { getBomDetailByCode, getKingdeeStock, getBomDetail, updateBom, markProductBom } from '@/api/bom'
import ProductDialog from '@/views/public/product/dialog'
import { inventoryQty, inventoryList } from '@/api/inventory'
import BomCreate from '@/views/bom/create'

export default {
  name: 'RecursiveTable',
  props: {
    items: {
      type: Array,
      default: () => []
    },
    bomId: {
      type: Number,
      default: undefined
    },
    showCheckbox: {
      type: Boolean,
      default: false
    },
    purchasingProducts: {
      type: Array,
      default: () => []
    }
  },
  components: { ProductDialog, BomCreate },
  data() {
    return {
      orgIdsOptions: [
        { label: '世盛总部', value: 100071 },
        { label: '世盛生产部', value: 100072 },
        { label: '世盛销售部', value: 100073 },
        { label: '市场部', value: 100074 },
        { label: '徽标厂', value: 100075 },
        { label: '镀锌厂', value: 100076 },
        { label: '模具厂', value: 100077 },
        { label: '冲压厂', value: 100078 }
      ],
      doOptions: [
        { label: '采购产品', value: 'cg' },
        { label: '生产产品', value: 'sc' },
        { label: '委外产品', value: 'ww' }
      ],
      productList: [],
      bomBox: {},
      processOpen: false,
      errOpen: false,
      childPurchasingProducts: this.purchasingProducts,
      loading: false,
      bomCreate: false,
      bomProduct: {}
    }
  },
  methods: {
    // 展开行
    expandChange(row) {
      if (row.doType === 'sc' || row.doType === 'ww') {
        this.loading = true
        getBomDetailByCode({ productCode: row.productCode }).then(async child => {
          if (child.code === 200) {
            if (child.data && child.data.child && child.data.child.length) {
              const result = child.data.child.map(itt => ({ ...itt, ...itt.product, orgIds: this.orgIdsOptions.map(ittt => ittt.value), originAmount: Number(row.originAmount) * Number(itt.quantity), doType: this.doOptions[0].value, source: 'private' }))
              await Promise.all(
                result.map(async ite => {
                  if (ite.materialCode) {
                    const stock = await inventoryList({ number: ite.materialCode, pageNum: 1, pageSize: 1 })
                    if (stock.rows[0] && stock.rows[0].stocks) {
                      ite.jdStock = stock.rows[0].stocks ? stock.rows[0].stocks.reduce((total, item) => total + item.qty, 0) : 0
                    } else ite.jdStock = 0
                    ite.stock = stock.rows[0] ? (ite.jdStock - stock.rows[0].useQty) : 0
                  }
                  const bomBox = await getBomDetailByCode({ productCode: ite.productCode })
                  if (bomBox.code === 200 && bomBox.data) {
                    const markBox = await getBomDetail({ bomId: bomBox.data.id })
                    if (markBox.code === 200 && markBox.data) {
                      ite.doType = markBox.data.mark || this.doOptions[0].value
                      ite.hasChildren = ite.doType === this.doOptions[0].value ? false : true
                    } else {
                      ite.doType = this.doOptions[0].value
                      ite.hasChildren = false
                    }
                  } else {
                    ite.doType = this.doOptions[0].value
                    ite.hasChildren = false
                  }
                })
              )
              row.bomId = child.data.id
              this.$set(row, 'children', result)
              this.loading = false
            }
          }
        })
      }
    },
    // 回显操作类型
    formatDo(type) {
      const res = this.doOptions.find(item => item.value === type)
      return res ? res.label : '采购产品'
    },
    // 切换操作
    handleCommand(e, row) {
      row.doType = e
      if (row.doType === 'sc' || row.doType === 'ww') {
        getBomDetailByCode({ productCode: row.productCode }).then(async child => {
          if (child.code === 200) {
            if (child.data && child.data.child && child.data.child.length) {
              const result = child.data.child.map(itt => ({ ...itt, ...itt.product, orgIds: this.orgIdsOptions.map(ittt => ittt.value), doType: this.doOptions[0].value, source: 'private' }))
              await Promise.all(
                result.map(async ite => {
                  if (ite.materialCode) {
                    const stock = await inventoryList({ number: ite.materialCode, pageNum: 1, pageSize: 1 })
                    if (stock.rows[0] && stock.rows[0].stocks) {
                      ite.jdStock = stock.rows[0].stocks ? stock.rows[0].stocks.reduce((total, item) => total + item.qty, 0) : 0
                    } else ite.jdStock = 0
                    ite.stock = stock.rows[0] ? (ite.jdStock - stock.rows[0].useQty) : 0
                  }
                })
              )
              row.bomId = child.data.id
              row.hasChildren = !!child.data.child.length
              this.$set(row, 'children', result)
              this.markProduct(row)
            } else {
              row.doType = 'cg'
              row.hasChildren = false
              this.$set(row, 'children', [])
              this.bomProduct = row
              this.errOpen = true
              this.markProduct(row)
            }
          }
        })
      } else {
        row.hasChildren = false
        this.$set(row, 'children', [])
        this.markProduct(row)
      }
    },
    // 产品标记记录
    markProduct(item) {
      markProductBom([{ productId: item.id, mark: item.doType }]).then(res => { })
    },
    getRowKey(row) {
      return (row.source ? row.source : 'child') + row.id
    },
    // 表格行样式 当当前行没有子物料时，添加样式，使其展开按钮不可点击
    getRowClass(row, rowIndex) {
      if (!row.row.hasChildren) return 'row-expand-cover'
      else return ''
    },
    handleProductView(row) {
      this.$refs.productInfo.handleView(row)
    },
    handleImgView(row) {
      this.$refs.productInfo.handleImgView(row)
    },
    // 刷新库存
    handleRefreshStock(row) {
      const { orgIds, materialCode } = row
      if (materialCode) {
        inventoryQty({ number: materialCode, force: true }).then(res => {
          if (res.code === 200) {
            row.stock = 0
            res.data.forEach(el => {
              row.stock = row.stock + el.qty
            })
          }
        })
      }
    },
    hanldeProcessView() {
      getBomDetail({ bomId: this.bomId }).then(async response => {
        if (response.code === 200) {          
          this.productList = response.data.child
          if (response.data.processGroupList) {
            response.data.processGroupList.forEach(el => {
              el.editShow = false
            });
            this.bomBox = response.data
          }
          this.processOpen = true
        }
      })

    },
    // 计算是否选中
    checked(row) {
      return this.purchasingProducts.some(item => item.id === row.id)
    },
    // 判断是否可选择
    selectable(row, index) {
      return !row.hasChildren
    },
    // 选择产品
    handleSelectProduct(e, row) {
      if (e) this.$emit('handleChooseProduct', row, false)
      else this.$emit('handleCancelProduct', row, false)
      this.checked(row)
    },
    // 取消选择产品
    handleCancelProduct(row) {
      this.$emit('handleCancelProduct', row, false)
      this.checked(row)
    },
    // 采购产品
    handleChooseProduct(row) {
      if (!this.childPurchasingProducts.includes(row)) this.childPurchasingProducts.push(row)
      this.$emit('handleChooseProduct', row, false)
      this.checked(row)
    },
    // 前往Bom
    goBom() {
      this.$router.push({
        path: '/private/productBom'
      })
    },
    // 设置默认工艺
    handleChecked(it, processGroupList) {
      if (processGroupList.findIndex(item => item.isDefault === true) == -1) {
        this.$message.error('必须设置默认工艺')
        it.isDefault = true
        return
      } else {
        processGroupList.forEach(item => {
          item.isDefault = false
        })
        it.isDefault = true
      }
    },
    // 编辑工艺
    handleSubmit(it) {
      this.bomBox.bomId = this.bomBox.id
      updateBom(this.bomBox).then(res => {
        const { code, msg } = res
        if (code === 200) {
          this.$message.success('操作成功')
          it.editShow = false
        } else this.$message.error(msg)
      })
    },
    // 设置BOM
    handleSetBom() {
      const product = {
        productCode: this.bomProduct.productCode,
        productName: this.bomProduct.productName,
        id: this.bomProduct.id
      }
      this.errOpen = false
      this.bomCreate = true
      this.$nextTick(() => {
        this.$refs.bomCreate.handleAdd(product)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';

.table-child {
  margin: -5px 0;
  display: flex;
  align-items: center;
  background-color: #f0f4f9;

  &-title {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    width: 80px;
  }

  .custom-table {
    flex: 1;
    overflow: hidden;

    .el-table__header-wrapper {
      .el-checkbox__inner {
        display: none !important;
      }
    }
  }
}

.productBox {
  background: #f0f3f9;
  padding: 20px 0;
  margin-bottom: 20px;

  .tables {
    width: calc(100% - 40px);
    margin: 0 20px;
  }
}

.labelTitle {
  font-size: 14px;
  padding: 0 20px;
  padding-bottom: 12px;
  color: #999999;
  display: flex;
  align-items: center;

  .formBox {
    display: flex;
    align-items: center;

    ::v-deep .el-form-item {
      margin: 0;
    }
  }

  .edit {
    margin-left: 20px;
    color: #2e73f3;
  }
}

.craftBox {
  background: #f8f9fb;
  border-radius: 5px;
  border: 1px solid #cbd6e2;
  margin: 0 20px;
  position: relative;

  .default {
    position: absolute;
    right: 0;
    top: 0;
    width: 86px;
    height: 30px;
    background: #699EFF;
    border-radius: 0 5px 0 5px;
    font-weight: 500;
    font-size: 14px;
    color: #FFFFFF;
    text-align: center;
    line-height: 30px;
  }

  .craftBox_list {
    border-bottom: 1px solid #cbd6e2;

    &:last-child {
      border-bottom: none;
    }

    .craftBox_list_title {
      width: 110px;
      height: 30px;
      background: #bec6d0;
      border-bottom-right-radius: 10px;
      padding-left: 16px;
      line-height: 30px;
      font-weight: 500;
      font-size: 14px;
      color: #ffffff;
      margin-bottom: 15px;
    }



    .craftBox_list_concent {
      padding: 0 15px;

      .concent_top {
        display: flex;
        align-items: center;
        margin-bottom: 16px;
      }

      .concent_bottom {
        margin-bottom: 10px;
      }

      .concent_item {
        display: flex;
        align-items: center;
        margin-right: 60px;

        .title {
          font-weight: 400;
          font-size: 12px;
          color: #999999;
          line-height: 20px;
          margin-right: 20px;
        }

        .text {
          font-weight: 500;
          font-size: 14px;
          color: #333333;
          line-height: 20px;
        }

        .fee {
          font-weight: 500;
          font-size: 14px;
          color: #f35d09;
          line-height: 20px;
        }

        .device {
          display: flex;
          align-items: center;

          .text {
            display: block;
            background: #e1e4e8;
            border-radius: 5px;
            font-weight: 500;
            font-size: 14px;
            color: #333333;
            line-height: 20px;
            padding: 5px 10px;
            margin-right: 10px;
          }
        }
      }
    }
  }
}

.errTips {
  padding: 10px 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  .img {
    width: 36px;
    height: 36px;
    margin-bottom: 17px;
  }

  .title {
    font-weight: 500;
    font-size: 14px;
    color: #ed4040;
    line-height: 20px;
    margin-bottom: 10px;
  }

  .text {
    font-weight: 400;
    font-size: 12px;
    color: #666666;
    line-height: 20px;
    margin-bottom: 47px;
  }

  .btn {
    display: flex;
    align-items: center;
    justify-content: center;

    .cancel {
      width: 269px;
      height: 50px;
      border-radius: 5px 5px 5px 5px;
      border: 1px solid #cbd6e2;
      font-weight: 400;
      font-size: 16px;
      color: #999999;
      line-height: 50px;
      text-align: center;
      margin-right: 10px;
      cursor: pointer;
    }

    .go {
      width: 269px;
      height: 50px;
      background: #2e73f3;
      border-radius: 5px 5px 5px 5px;
      font-weight: 500;
      font-size: 16px;
      color: #ffffff;
      line-height: 50px;
      text-align: center;
      cursor: pointer;
    }
  }
}
</style>
