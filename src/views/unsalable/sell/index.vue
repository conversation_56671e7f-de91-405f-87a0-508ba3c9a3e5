<template>
  <div class="newBox bgcf9 vh-85">
    <div class="classify flex">
      <div class="classify-item" :class="{ active: item.value === classifyName }" v-for="item in classifyOptions" :key="item.value" @click="handleCategory(item)">
        {{ item.label }}
      </div>
    </div>
    <component :is="classifyName"></component>
  </div>
</template>
<script>
import sell from './sell'
import unsalable from './unsalable'
import initiative from './initiative'

export default {
  name: 'Unsalablesell',
  components: { sell, unsalable, initiative },
  data() {
    return {
      // 分类
      classifyName: 'sell',
      classifyOptions: [
        { value: 'initiative', label: '录入合同' },
        { value: 'sell', label: '销售合同' },
        { value: 'unsalable', label: '滞销品合同' }
      ]
    }
  },
  computed: {
    companyId() {
      return this.$store.state.user.companyId
    }
  },
  created() {
    this.classifyName = this.$route.query.classify || 'initiative'
    // if (this.companyId != 14) this.classifyOptions = this.classifyOptions.filter(item => item.value != 'initiative')
    // else this.classifyName = 'initiative'
  },
  methods: {
    // 切换分类
    handleCategory(item) {
      this.classifyName = item.value
    }
  }
}
</script>
<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';
</style>
