<template>
  <div>
    <el-dialog v-dialogDragBox title="新增采购产品" :visible.sync="open" :width="type === 'alertInventory' ? '1250px' : '1150px'" class="custom-dialog">
      <div class="addBox">
        <el-form ref="form" :model="form" :rules="rules" label-position="left" label-width="100px">
          <el-row :gutter="18">
            <el-col :span="12">
              <el-form-item label="订单编号" prop="serialNum">
                <el-input v-model="form.serialNum" placeholder="请输入订单编号"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="订单日期" prop="date">
                <el-date-picker v-model="form.date" type="date" placeholder="请选择订单日期" style="width: 100%" value-format="timestamp"></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="要求到厂时间" prop="requiredTime">
                <el-date-picker v-model="form.requiredTime" type="date" placeholder="请选择要求到厂时间" style="width: 100%" :picker-options="dateOption" value-format="timestamp"></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="备注" prop="remark">
                <el-input v-model="form.remark" placeholder="请输入备注"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-table ref="addSubTable" stripe :data="form.purchasingProducts" row-key="id" style="width: 100%" class="custom-table">
                <el-table-column align="center" prop="product_name" label="产品名称" show-overflow-tooltip>
                  <template slot-scope="{ row }">
                    <span class="table-link" @click="handleView(row)" v-if="row.productId">
                      <span style="color: #fe7f22">(私域)</span>
                      {{ row.productName }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column prop="image" label="图片" align="center" show-overflow-tooltip width="75">
                  <template slot-scope="{ row }">
                    <image-preview :src="row.product && formatProductImg(row.product)" :width="50" :height="50" />
                  </template>
                </el-table-column>
                <el-table-column align="center" prop="specs" label="规格" show-overflow-tooltip width="80">
                  <template slot-scope="{ row }">{{ row.product && row.product.specs }}</template>
                </el-table-column>
                <el-table-column align="center" prop="model" label="型号" show-overflow-tooltip width="80">
                  <template slot-scope="{ row }">{{ row.product && row.product.model }}</template>
                </el-table-column>
                <el-table-column align="center" prop="product_code" label="产品编码" show-overflow-tooltip width="110">
                  <template slot-scope="{ row }">{{ row.product && row.product.productCode }}</template>
                </el-table-column>
                <el-table-column align="center" prop="material_quality" label="材质" show-overflow-tooltip width="80">
                  <template slot-scope="{ row }">{{ row.product && row.product.materialQuality }}</template>
                </el-table-column>
                <el-table-column align="center" prop="unit" label="单位">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`purchasingProducts.${scope.$index}.unit`" :rules="rules.unit">
                      <el-select size="small" v-model="scope.row.unit" filterable allow-create default-first-option placeholder="请选择单位">
                        <el-option v-for="item in unitoptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                      </el-select>
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column align="center" prop="used" label="已采数量" v-if="showUsed"></el-table-column>
                <el-table-column align="center" prop="quantity" label="采购数量">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`purchasingProducts.${scope.$index}.quantity`" :rules="rules.quantity">
                      <el-input v-model="scope.row.quantity" size="small" placeholder="采购数量" />
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column align="center" prop="minQty" label="最小库存" v-if="type === 'alertInventory'">
                  <template slot-scope="scope">{{ scope.row.minQty }}</template>
                </el-table-column>
                <el-table-column align="center" prop="maxQty" label="最大库存" v-if="type === 'alertInventory'">
                  <template slot-scope="scope">{{ scope.row.maxQty }}</template>
                </el-table-column>
                <el-table-column align="center" prop="recommendPrice" label="建议采购价">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`purchasingProducts.${scope.$index}.recommendPrice`" :rules="rules.recommendPrice">
                      <el-input v-model="scope.row.recommendPrice" size="small" placeholder="建议采购价">
                        <span slot="prefix" class="inline-flex">￥</span>
                      </el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column align="center" prop="note" label="备注">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`purchasingProducts.${scope.$index}.note`" :rules="rules.note">
                      <el-input v-model="scope.row.note" size="small" placeholder="备注"></el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column align="center" label="操作" min-width="100" v-if="form.purchasingProducts && form.purchasingProducts.length > 1">
                  <template slot-scope="{ row }">
                    <button type="button" class="table-btn danger" @click="handleDeleteProduct(row)">删除</button>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <button type="button" class="custom-dialog-btn" @click="handleCancel">取 消</button>
        <button type="button" class="custom-dialog-btn primary" @click="handleSubmit">提 交</button>
      </div>
    </el-dialog>
    <!-- 产品详情 -->
    <product-dialog ref="productInfo"></product-dialog>
  </div>
</template>
<script>
import { isNumber, isNumberLength } from '@/utils/validate'
import { purchasingAdd } from '@/api/purchase'
import ProductDialog from '@/views/public/product/dialog'

export default {
  components: { ProductDialog },
  data() {
    return {
      contractId: undefined,
      dateOption: {
        disabledDate: time => {
          return time.getTime() < Date.now() - 24 * 60 * 60 * 1000
        }
      },
      unitoptions: [
        { value: '吨', label: '吨' },
        { value: '千克', label: '千克' },
        { value: '个', label: '个' },
        { value: '件', label: '件' },
        { value: '套', label: '套' },
        { value: '米', label: '米' },
        { value: '支', label: '支' }
      ],
      open: false,
      form: {},
      rules: {
        serialNum: [{ required: true, message: '请输入订单编号', trigger: 'blur' }],
        date: [{ required: true, message: '请选择订单日期', trigger: ['blur', 'change'] }],
        quantity: [
          { required: true, message: '请输入采购数量', trigger: 'blur' },
          { validator: isNumber, message: '请输入正确的数量', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 3), message: '只可以填写三位小数', trigger: 'blur' }
        ],
        recommendPrice: [
          { validator: isNumber, message: '请输入正确的金额', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 4), message: '只可以填写四位小数', trigger: 'blur' }
        ],
        unit: [{ required: true, message: '请选择单位', trigger: 'change' }]
      },
      windowHeight: 0,
      formHeight: 300,
      type: undefined,
      showUsed: false
    }
  },
  watch: {
    windowHeight(val) {
      this.formHeight = val * 0.94 - 367
    }
  },
  mounted() {
    this.windowHeight = document.documentElement.clientHeight
    window.onresize = () => {
      this.windowHeight = document.documentElement.clientHeight
    }
  },
  methods: {
    // 查看合同产品并弹出
    handleOpen(list = [], type = undefined) {
      this.showUsed = false
      if (!list.length) {
        this.$message.error('参数错误，请稍后再试！')
        return
      }
      this.type = type
      if (type === 'initiative') this.showUsed = true
      this.form = {
        date: new Date().getTime(),
        purchasingProducts: [],
        remark: undefined,
        requiredTime: undefined,
        serialNum: `CG${this.parseTime(new Date(), '{y}{m}{d}{h}{i}{s}{l}')}`
      }
      this.resetForm('form')
      list.map(item => {
        item.quantity = item.quantity
      })
      this.form.purchasingProducts = list
      this.open = true
    },
    // 产品详情
    handleView(row) {
      const product = row?.product || ''
      if (!product) return
      this.$refs.productInfo.handleView(product)
    },
    // 删除产品
    handleDeleteProduct(row) {
      const index = this.form.purchasingProducts.findIndex(item => item.id === row.id)
      this.form.purchasingProducts.splice(index, 1)
    },
    // 取消
    handleCancel() {
      this.open = false
      this.$emit('callback')
    },
    // 提交
    handleSubmit() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          const { serialNum, date, requiredTime, remark, purchasingProducts } = this.form
          const newPurchasingProducts = purchasingProducts.map(item => {
            return {
              detailId: item.detailId || undefined,
              note: item.note,
              productId: item.productId,
              productName: item.productName,
              quantity: item.quantity,
              recommendPrice: item.recommendPrice,
              source: 'private',
              unit: item.unit
            }
          })
          const data = { serialNum, date, requiredTime, remark, purchasingProducts: newPurchasingProducts }
          purchasingAdd(data).then(res => {
            const { code, msg } = res
            if (code === 200) {
              this.$message.success('添加成功')
              this.handleCancel()
            } else this.$message.error(msg)
          })
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
::v-deep .custom-dialog {
  .addBox {
    padding: 10px 20px;
  }
  .custom-table {
    .el-form-item {
      margin-top: 10px !important;
      margin-bottom: 10px !important;
      .el-form-item__error {
        top: 95%;
        padding-top: 0;
      }
    }
  }
}
</style>
