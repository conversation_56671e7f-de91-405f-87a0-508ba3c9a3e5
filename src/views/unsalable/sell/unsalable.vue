<template>
  <div class="newBox bgcf9 vh-85">
    <div class="Box">
      <el-table v-loading="loading" ref="allTable" stripe :data="list" row-key="id" :key="key" style="width: 100%" class="custom-table custom-table-cell10" v-if="loading || total > 0">
        <el-table-column align="center" type="index" label="序号" width="50"></el-table-column>
        <el-table-column align="center" prop="serial" label="合同编号" show-overflow-tooltip width="120"></el-table-column>
        <el-table-column align="center" prop="buyerName" label="买方" show-overflow-tooltip min-width="130"></el-table-column>
        <el-table-column align="center" prop="signingTime" label="签订时间" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" prop="address" label="签订地点" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" prop="amount" label="订单总金额" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <span class="table-orange" style="font-size: 14px">{{ row.amount ? '￥' + row.amount : '' }}{{ row.isIncludingTax ? '(含税)' : '(不含税)' }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="status" label="状态" show-overflow-tooltip width="60">
          <template slot-scope="{ row }">
            <div v-html="statusFormat(row)"></div>
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作" min-width="120">
          <template slot-scope="{ row }">
            <button type="button" class="table-btn" @click="handleView(row)">查看详情</button>
          </template>
        </el-table-column>
      </el-table>
      <el-empty v-if="total == 0 && !loading" />
      <div class="custom-pagination">
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
      </div>
    </div>

    <!-- 合同详情 -->
    <el-dialog v-dialogDragBox title="合同详情" :visible.sync="open" width="1150px" class="custom-dialog">
      <div class="print" v-if="contractStatus !== -1">
        <div class="print-item" @click="handlePrint(info)">
          <i class="el-icon-printer"></i>
          打印
        </div>
        <div class="print-item" @click="handleDownload(info)">
          <i class="el-icon-download"></i>
          下载
        </div>
      </div>
      <div style="text-align: center">
        <img style="max-width: 100%" :src="info.file" v-if="isBase64Image(info.file)" />
        <show-template :certify="!!info.certify" :signet="signet" :signature="info.signature" :content="info.file" v-else-if="isJSON(info.file)" />
      </div>
      <div slot="footer">
        <button type="button" class="custom-dialog-btn primary" @click="open = false">关 闭</button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { listUnsalableSupplierContract, getUnsalableContract } from '@/api/unsalable'
import print from 'print-js'
import ShowTemplate from '@/views/purchase/contract/showTemplate'
import { getConfigDetail2 } from '@/api/config'

export default {
  components: { ShowTemplate },
  name: 'sell',
  data() {
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        serial: undefined,
        keyword: undefined,
        status: undefined
      },
      status: [1, 2],
      statusOptions: [
        { label: '无效', value: -1, color: 'color-red' },
        { label: '正常', value: 1, color: 'color-disabled' },
        { label: '已签署', value: 2, color: 'color-success' },
        { label: '已完成', value: 3, color: 'color-blue' }
      ],
      key: 1,
      loading: true,
      list: [],
      total: 0,
      open: false,
      info: {},
      contractSource: undefined,
      contractId: undefined,
      contractStatus: undefined,
      seller: undefined,
      signet: ''
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.loading = true
      listUnsalableSupplierContract(this.queryParams).then(res => {
        if (res.code === 200) {
          this.list = res.rows
          this.key = Math.random()
          this.total = res.total
          this.loading = false
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 状态回显
    statusFormat(row) {
      const res = this.statusOptions.find(item => item.value === row.status)
      return res ? `<span class="${res.color}">${res.label}</span>` : ''
    },
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 重置搜索
    resetQuery() {
      this.status = [1, 2]
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 查看详情
    handleView(row) {
      this.signet = ''
      this.contractSource = row.source
      this.contractId = row.id
      this.contractStatus = row.status
      this.seller = row.seller
      const contractId = row.id
      getUnsalableContract({ contractId }).then(res => {
        if (res.code === 200) {
          this.info = res.data
          if (this.isJSON(this.info.file)) {
            getConfigDetail2({ configKey: 'signatures', companyId: res.data.companyId, creator: res.data.creator }).then(res => {
              this.signet = res?.data?.configValue || ''
              this.open = true
            })
          } else this.open = true
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 打印
    handlePrint(data) {
      print({ printable: data.file, type: 'image', base64: true })
    },
    // 下载
    handleDownload(data) {
      const imgUrl = data.file
      const fileName = data.serial
      if (window.navigator.msSaveOrOpenBlob) {
        const bstr = atob(imgUrl.split(',')[1])
        let n = bstr.length
        const u8arr = new Uint8Array(n)
        while (n--) {
          u8arr[n] = bstr.charCodeAt(n)
        }
        const blob = new Blob([u8arr])
        window.navigator.msSaveOrOpenBlob(blob, fileName + '.' + 'png')
      } else {
        const a = document.createElement('a')
        a.href = imgUrl
        a.setAttribute('download', fileName)
        a.click()
      }
    },
    // 判断是不是json
    isJSON(str) {
      try {
        JSON.parse(str)
        return true
      } catch (e) {
        return false
      }
    },
    // 判断是不是base64
    isBase64Image(str) {
      if (typeof str === 'string') {
        return str.startsWith('data:image/') && str.includes(';base64,')
      }
      return false
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
.Box {
  padding: 15px 20px;
}
.custom-dialog ::v-deep {
  .print {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 0 20px;
    &-item {
      padding: 0 50px;
      height: 50px;
      line-height: 50px;
      text-align: center;
      margin-left: 10px;
      border: 1px solid #cbd6e2;
      border-radius: 5px;
      font-size: 16px;
      color: $white;
      cursor: pointer;
      background-color: $blue;
      &:hover {
        opacity: 0.8;
      }
    }
  }
}
</style>
