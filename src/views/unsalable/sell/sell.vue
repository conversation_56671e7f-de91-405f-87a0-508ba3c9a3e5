<template>
  <div>
    <div class="Box" v-if="showList">
      <el-table v-loading="loading" ref="allTable" stripe :data="list" row-key="id" :key="key" style="width: 100%"
        class="custom-table custom-table-cell10" v-if="loading || total > 0">
        <el-table-column align="center" type="index" label="序号" width="50"></el-table-column>
        <el-table-column align="center" prop="serial" label="合同编号" show-overflow-tooltip width="120"></el-table-column>
        <el-table-column align="center" prop="buyerName" label="买方" show-overflow-tooltip
          min-width="130"></el-table-column>
        <el-table-column align="center" label="产品明细">
          <template slot-scope="{ row }">
            <el-button type="text" size="small" icon="el-icon-view" @click="handleProductDetail(row)">查看</el-button>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="signingTime" label="签订时间" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" prop="address" label="签订地点" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" prop="amount" label="订单总金额" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <span class="table-orange" style="font-size: 14px">{{ row.amount ? '￥' + row.amount : '' }}{{
              row.isIncludingTax ? '(含税)' : '(不含税)' }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="status" label="状态" show-overflow-tooltip width="60">
          <template slot-scope="{ row }">
            <div v-html="statusFormat(row)"></div>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="saleOrderNum" label="销售订单号" v-if="hasmaterialCode && companyId == 14">
          <template slot-scope="{ row }">
            <div class="flex flex-column" v-if="row.saleOrderStatus && row.saleOrderStatus.length > 0">
              <el-tooltip effect="dark" :content="'状态：' + formatSaleOrderStatus(item)"
                v-for="(item, index) in row.saleOrderStatus" :key="index">
                <span class="table-link" @click="handleSaleOrderDetail(item, row.id)">{{ item.saleOrderNum }}</span>
              </el-tooltip>
            </div>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="生产工单" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <el-tooltip effect="dark" content="点击下派生产任务单" :disabled="formatProduction(row.production, 'sc') == '-'"
              placement="top">
              <span class="table-link" @click="handleDispatchOrder(row, 'sc', row.id)">{{
                formatProduction(row.production, 'sc') }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column align="center" label="委外工单" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <el-tooltip effect="dark" content="点击下派生产任务单" :disabled="formatProduction(row.production, 'ww') == '-'"
              placement="top">
              <span class="table-link" @click="handleDispatchOrder(row, 'ww', row.id)">{{
                formatProduction(row.production, 'ww') }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column align="center" label="采购产品" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <el-tooltip effect="dark" content="点击添加采购" :disabled="formatProduction(row.production, 'cg') == '-'"
              placement="top">
              <span class="table-link">{{ formatProduction(row.production, 'cg') }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作" min-width="320">
          <template slot-scope="{ row }">
            <button type="button" class="table-btn primary" @click="handleSaleOrderCreate(row)"
              v-if="hasmaterialCode && companyId == 14 && (!row.saleOrderNum || !row.saleOrderStatus)">新建销售订单</button>
            <button type="button" class="table-btn" @click="handleView(row)">查看详情</button>
            <button type="button" class="table-btn primary" @click="handleLabel(row)"
              v-if="hasmaterialCode && row.saleOrderNum && row.saleOrderStatus">产品标记</button>
            <!-- <button type="button" class="table-btn primary" @click="handleLabel(row)">产品标记</button> -->
            <button type="button" class="table-btn primary" @click="handleSend(row)">去发货</button>
            <button type="button" class="table-btn success"
              v-if="new Date().getTime() > new Date(row.signingTime).getTime() + row.period * 24 * 60 * 60 * 1000"
              @click="handleForSellerApply(row)">申请付款</button>
          </template>
        </el-table-column>
      </el-table>
      <el-empty v-if="total == 0 && !loading" />
      <div class="custom-pagination">
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize" @pagination="getList" />
      </div>
    </div>

    <!-- 合同详情 -->
    <el-dialog v-dialogDragBox title="合同详情" :visible.sync="open" width="1150px" class="custom-dialog">
      <div class="print" v-if="contractStatus !== -1">
        <div class="print-item" @click="handlePrint(info)">
          <i class="el-icon-printer"></i>
          打印
        </div>
        <div class="print-item" @click="handleDownload(info)">
          <i class="el-icon-download"></i>
          下载
        </div>
      </div>
      <div style="text-align: center" :style="{ zoom: zoom }">
        <img style="max-width: 100%" :src="info.file" v-if="isBase64Image(info.file)" />
        <show-template :certify="!!info.certify" :signet="signet" :signature="info.signature" :content="info.file"
          v-else-if="isJSON(info.file)" />
      </div>
      <div slot="footer">
        <button type="button" class="custom-dialog-btn primary" @click="open = false">关 闭</button>
      </div>
    </el-dialog>

    <!--合同产品明细-->
    <contract-product ref="contractProduct" />

    <!--产品标记-->
    <el-dialog v-dialogDragBox title="产品标记" :visible.sync="labelOpen" width="1150px" class="custom-dialog">
      <div style="padding: 0 20px">
        <div class="labelTitle">合同信息</div>
        <el-row :gutter="10" class="labelInfo">
          <el-col :span="9">
            <span>买方</span>
            <b>{{ labelInfo.buyerName || '' }}</b>
          </el-col>
          <el-col :span="6">
            <span>签订时间</span>
            <b>{{ labelInfo.signingTime || '' }}</b>
          </el-col>
          <el-col :span="9">
            <span>签订地点</span>
            <b>{{ labelInfo.address || '' }}</b>
          </el-col>
          <el-col :span="24">
            <span>合同金额</span>
            <b class="price">{{ labelInfo.amount ? '￥' + labelInfo.amount : '' }}{{ labelInfo.isIncludingTax ? '(含税)' :
              '(不含税)' }}</b>
          </el-col>
        </el-row>
        <div class="labelTitle">合同产品列表</div>
        <el-table :key="key" ref="addTable" stripe :data="labelList" :row-key="getRowKey" :row-class-name="getRowClass"
          style="width: 100%; margin-bottom: 12px" class="custom-table custom-table-cell5 blue"
          @select="handleSelectProduct" @select-all="handleSelectProductAll" :default-expand-all="false"
          @expand-change="expandChange" v-loading="loadings">
          <el-table-column align="left" type="expand">
            <template slot-scope="scope">
              <recursive-table v-if="scope.row.children && scope.row.children.length" :items="scope.row.children"
                :showCheckbox="showCheckbox" :bomId="scope.row.bomId" @handleChooseProduct="handleChooseProduct"
                @handleCancelProduct="handleCancelProduct" :purchasingProducts="purchasingProducts" />
            </template>
          </el-table-column>
          <el-table-column align="center" type="selection" width="50" :reserve-selection="true" v-if="showCheckbox"
            :selectable="selectable"></el-table-column>
          <el-table-column align="center" type="index" label="序号"></el-table-column>
          <el-table-column align="center" prop="product_name" label="产品名称" show-overflow-tooltip>
            <template slot-scope="{ row }">
              <span class="table-link" @click="handleProductView(row)">{{ row.productName }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="picture1" label="图片" width="75">
            <template slot-scope="{ row }">
              <el-button type="text" size="mini" icon="el-icon-view" @click="handleImgView(row)">查看</el-button>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="specs" label="规格" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="model" label="型号" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="productCode" label="产品编码" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="materialQuality" label="材质" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="unit" label="单位" width="50"></el-table-column>
          <el-table-column align="center" label="采购量">
            <template slot-scope="{ row }">{{ row.quantity + row.unit }}</template>
          </el-table-column>
          <el-table-column align="center" label="金蝶库存" prop="jdStock"></el-table-column>
          <el-table-column align="center" label="自由客库存" prop="stock">
            <!-- <template slot-scope="{ row }">
              <div class="table-stock" v-if="row.materialCode">
                <el-select v-model="row.orgIds" collapse-tags multiple size="mini" @change="handleRefreshStock(row)">
                  <el-option v-for="item in orgIdsOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
                <el-tooltip effect="dark" content="点击查询库存">
                  <el-button type="text" icon="el-icon-refresh" @click="handleRefreshStock(row)">{{ row.stock || 0 }}</el-button>
                </el-tooltip>
              </div>
              <span v-else>-</span>
            </template> -->
          </el-table-column>
          <el-table-column align="center" label="操作">
            <template slot-scope="{ row }">
              <el-dropdown @command="handleCommand($event, row)" v-if="!showCheckbox">
                <span class="el-dropdown-link" :class="row.doType === 'cg' ? 'dropdown-primary' : 'dropdown-orange'">
                  {{ formatDo(row.doType) }}
                  <i class="el-icon-caret-bottom"></i>
                </span>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item v-for="item in doOptions" :key="item.value" :command="item.value">{{ item.label
                  }}</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
              <el-button type="text" size="mini" icon="el-icon-circle-check" @click="handleChooseProduct(row)"
                v-if="showCheckbox" :disabled="row.hasChildren">采购</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn" @click="labelOpen = false">取消</el-button>
        <el-button class="custom-dialog-btn primary" @click="labelOpen = false"
          v-if="!showCheckbox && !hasProduction()">关闭</el-button>
        <el-button class="custom-dialog-btn primary" @click="handleCreateProduction"
          v-if="!showCheckbox && hasProduction()">确定</el-button>
        <el-badge :value="purchasingProducts.length" :hidden="!purchasingProducts.length" v-if="showCheckbox">
          <el-button :disabled="!purchasingProducts.length" class="custom-dialog-btn primary"
            @click="handleNext">下一步</el-button>
        </el-badge>
      </div>
    </el-dialog>
    <!-- 产品详情 -->
    <product-dialog ref="productInfos"></product-dialog>
    <!--从销售合同添加采购产品-->
    <el-dialog v-dialogDragBox title="从销售合同添加采购产品" :visible.sync="listOpen" width="1150px" class="custom-dialog">
      <div style="padding: 0 20px">
        <div class="listSearch">
          <el-input v-model="queryParams.serial" placeholder="请输入合同编号"></el-input>
          <el-button type="primary" icon="el-icon-search" @click="handleListQuery">搜索</el-button>
        </div>
        <el-table v-loading="loading" ref="dialogTable" stripe :data="list" row-key="id" :key="key" style="width: 100%"
          class="custom-table custom-table-cell10">
          <el-table-column align="center" type="index" label="序号" width="50"></el-table-column>
          <el-table-column align="center" prop="serial" label="合同编号" show-overflow-tooltip
            width="120"></el-table-column>
          <el-table-column align="center" prop="buyerName" label="买方" show-overflow-tooltip
            min-width="130"></el-table-column>
          <el-table-column align="center" label="产品明细">
            <template slot-scope="{ row }">
              <el-button type="text" size="small" icon="el-icon-view" @click="handleProductDetail(row)">查看</el-button>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="signingTime" label="签订时间" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="address" label="签订地点" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="amount" label="订单总金额" show-overflow-tooltip>
            <template slot-scope="{ row }">
              <span class="table-orange" style="font-size: 14px">{{ row.amount ? '￥' + row.amount : '' }}{{
                row.isIncludingTax ? '(含税)' : '(不含税)' }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="操作" min-width="120">
            <template slot-scope="{ row }">
              <button type="button" class="table-btn primary" @click="handleLabel(row, true)">采购合同产品</button>
            </template>
          </el-table-column>
        </el-table>

        <div class="custom-pagination">
          <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize" @pagination="getList" />
        </div>
      </div>
    </el-dialog>
    <!-- 拆分提示 -->
    <el-dialog v-dialogDragBox title="提示" :visible.sync="errOpen" width="748px" class="custom-dialog"
      :modal-append-to-body="false">
      <div class="errTips">
        <img class="img" src="@/assets/images/errTips.png" alt="" />
        <div class="title">当前产品尚未设置BOM与工艺不可标记为生产</div>
        <div class="text">如要更改请前往产品BOM为该产品增加BOM设置，完成后可以标记为生产</div>
        <div class="btn">
          <div class="cancel" @click="errOpen = false">取消</div>
          <div class="go" @click="handleSetBom">立即设置BOM</div>
        </div>
      </div>
    </el-dialog>
    <!-- 交货日期 -->
    <el-dialog v-dialogDragBox title="交货日期" :visible.sync="deliveryTimeOpen" width="650px" class="custom-dialog"
      :modal-append-to-body="false">
      <div class="deliveryTimeBox">
        <el-form ref="form" :model="form" :rules="rules" label-width="6em" class="custom-form">
          <el-form-item label="交货日期" prop="deliveryTime">
            <el-date-picker v-model="form.deliveryTime" type="date" placeholder="选择交货日期"
              style="width: 100%"></el-date-picker>
          </el-form-item>
        </el-form>
        <div class="btn">
          <div class="cancel" @click="deliveryTimeOpen = false">取消</div>
          <div class="go" @click="submitForm">提交</div>
        </div>
      </div>
    </el-dialog>
    <!-- 新建销售订单 -->
    <sale-order-create ref="saleOrderCreate" @callBack="handleCallBack" v-if="saleOrderCreate" />
    <!-- 金蝶销售详情 -->
    <sale-order-detail ref="saleOrderDetail" @callBack="handleCallBack" v-if="showDetail" />
    <!-- 下派生产任务单 -->
    <dispatch-order ref="dispatchOrder" @callBack="handleDispatchCallBack" v-if="dispatchOrder" />
    <!-- 新增采购产品 -->
    <create-demand ref="createDemand" @callBack="createDemand = false" v-if="createDemand" />
    <!-- 去发货 -->
    <send-product ref="sendProduct" @callBack="sendProductBack" v-if="sendProduct" />
    <!-- 新增BOM -->
    <bom-create ref="bomCreate" @callBack="bomCreate = false" v-if="bomCreate" />
  </div>
</template>
<script>
import { contractDetail, contractPriceChange, contractSaleList } from '@/api/purchase'
import { forSellerApply } from '@/api/accountPeriod'
import print from 'print-js'
import contractProduct from '@/views/components/product/contact'
import { getBomDetailByCode, getKingdeeStock, getBomDetail, markProductBom } from '@/api/bom'
import { getProduct } from '@/api/system/product'
import ProductDialog from '@/views/public/product/dialog'
import { getConfigDetail } from '@/api/config'
import { addProduction } from '@/api/production'
import ShowTemplate from '@/views/purchase/contract/showTemplate'
import { getConfigDetail2 } from '@/api/config'
import RecursiveTable from './RecursiveTable.vue'
import { parseTime } from '@/utils/ruoyi'
import saleOrderCreate from '@/views/kingdee/purchase/saleOrder/create'
import SaleOrderDetail from '@/views/kingdee/purchase/saleOrder/detail'
import { getContractDetail } from '@/api/kingdee/purchase/saleOrder'
import { getProductionList } from '@/api/production'
import DispatchOrder from '@/views/produce/dispatch'
import CreateDemand from './createDemand'
import { deliveryAdd } from '@/api/delivery'
import SendProduct from '@/views/sendProduct/send'
import { inventoryList } from '@/api/inventory'
import BomCreate from '@/views/bom/create'

export default {
  props: {
    showCheckbox: {
      type: Boolean,
      default: false
    },
    showList: {
      type: Boolean,
      default: true
    }
  },
  name: 'unsalable',
  components: { ProductDialog, contractProduct, ShowTemplate, RecursiveTable, saleOrderCreate, SaleOrderDetail, DispatchOrder, CreateDemand, SendProduct, BomCreate },
  data() {
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        serial: undefined,
        keyword: undefined,
        status: undefined
      },
      status: [1, 2],
      statusOptions: [
        { label: '无效', value: -1, color: 'color-red' },
        { label: '正常', value: 1, color: 'color-disabled' },
        { label: '已签署', value: 2, color: 'color-success' },
        { label: '已完成', value: 3, color: 'color-blue' }
      ],
      key: 1,
      loading: true,
      loadings: false,
      list: [],
      total: 0,
      open: false,
      info: {},
      contractSource: undefined,
      contractId: undefined,
      contractStatus: undefined,
      seller: undefined,
      zoom: 1,
      // 产品标记
      labelOpen: false,
      labelInfo: {},
      labelList: [],
      orgIdsOptions: [
        { label: '世盛总部', value: 100071 },
        { label: '世盛生产部', value: 100072 },
        { label: '世盛销售部', value: 100073 },
        { label: '市场部', value: 100074 },
        { label: '徽标厂', value: 100075 },
        { label: '镀锌厂', value: 100076 },
        { label: '模具厂', value: 100077 },
        { label: '冲压厂', value: 100078 }
      ],
      doOptions: [
        { label: '采购产品', value: 'cg' },
        { label: '生产产品', value: 'sc' },
        { label: '委外产品', value: 'ww' }
      ],
      purchasingProducts: [],
      listOpen: false,
      hasmaterialCode: false,
      processList: [],
      signet: '',
      errOpen: false,
      deliveryTimeOpen: false,
      form: {
        deliveryTime: undefined
      },
      rules: {
        deliveryTime: [{ type: 'date', required: true, message: '请选择交货日期', trigger: 'change' }]
      },
      saleOrderCreate: false,
      showDetail: false,
      // 销售订单状态
      saleOrderStatusOptions: [
        { value: 'A', label: '创建' },
        { value: 'B', label: '审核中' },
        { value: 'C', label: '已审核' },
        { value: 'D', label: '重新审核' },
        { value: 'Z', label: '暂存' }
      ],
      dispatchOrder: false,
      createDemand: false,
      sendProduct: false,
      bomCreate: false,
      bomProduct: {}
    }
  },
  created() {
    if (this.showList) {
      this.getConfig()
      this.getList()
    }
  },
  watch: {
    sendOpen(newVal, oldVal) {
      if (newVal == false) {
        this.$refs.sendTable.clearSelection()
      }
    }
  },
  computed: {
    companyId() {
      return this.$store.state.user.companyId
    }
  },
  methods: {
    parseTime,
    // 查询企业参数是否有金蝶物料编码
    getConfig() {
      getConfigDetail({ configKey: 'kingdee.material.code' }).then(res => {
        const { code, data } = res
        if (code === 200) this.hasmaterialCode = data.hasOwnProperty('configValue') && data.configValue === 'true'
      })
    },
    // 格式话生产工单
    formatProduction(production, type) {
      if (!production || !production.length) return '-'
      const res = production.filter(item => item.mark === type)
      return res.length ? `${res.length}个` : '-'
    },
    getList() {
      this.loading = true
      contractSaleList(this.queryParams).then(async res => {
        if (res.code === 200) {
          await Promise.all(
            res.rows.map(async row => {
              if (row.saleOrderNum) {
                const saleOrderStatus = await getContractDetail({ contractId: row.id })
                const data = saleOrderStatus?.data
                if (Object.keys(data).length === 0) {
                  row.saleOrderStatus = undefined
                } else {
                  row.saleOrderStatus = Object.keys(data).map(key => {
                    return {
                      saleOrderNum: key,
                      ...data[key]
                    }
                  })
                }
              }
              const production = await getProductionList({ pageSize: 100, pageNum: 1, objId: row.id, type: 0, lower: false })
              if (production.code === 200 && production.rows.length) row.production = production.rows
            })
          )
          this.list = res.rows
          this.key = Math.random()
          this.total = res.total
          this.loading = false
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 刷新列表
    // prettier-ignore
    refreshList() {
      this.loading = true
      contractSaleList(this.queryParams).then(async res => {
        const { code, rows, total } = res
        if (code === 200) {
          await Promise.all(
            rows.map(async row => {
              if (row.saleOrderNum) {
                const saleOrderStatus = await getContractDetail({ contractId: row.id })
                const data = saleOrderStatus?.data
                if (Object.keys(data).length === 0) {
                  row.saleOrderStatus = undefined
                } else {
                  row.saleOrderStatus = Object.keys(data).map(key => {
                    return {
                      saleOrderNum: key,
                      ...data[key]
                    }
                  })
                }
              }
              const production = await getProductionList({ pageSize: 100, pageNum: 1, objId: row.id, type: 0, lower: false })
              if (production.code === 200 && production.rows.length) row.production = production.rows
            })
          )
          this.$set(this, 'list', rows)
          this.$set(this, 'total', total)
        }
      }).finally(() => {
        this.loading = false
      })
    },
    // 刷新单行
    async handleRowRefresh() {
      if (!this.contractId) return
      const saleOrderStatus = await getContractDetail({ contractId: this.contractId })
      const data = saleOrderStatus?.data
      const index = this.list.findIndex(item => item.id === this.contractId)
      if (index !== -1) {
        if (Object.keys(data).length === 0) {
          this.$set(this.list, index, {
            ...this.list[index],
            saleOrderStatus: undefined
          })
        } else {
          this.$set(this.list, index, {
            ...this.list[index],
            saleOrderStatus: Object.keys(data).map(key => {
              return {
                saleOrderNum: key,
                ...data[key]
              }
            })
          })
        }
      }
      const production = await getProductionList({ pageSize: 100, pageNum: 1, objId: this.contractId, type: 0, lower: false })
      if (production.code === 200 && production.rows.length) this.list[index].production = production.rows
      this.contractId = undefined
    },
    // 销售订单状态回显
    formatSaleOrderStatus(row) {
      const saleOrderStatus = row?.result?.result?.DocumentStatus
      if (!saleOrderStatus) return '-'
      const res = this.saleOrderStatusOptions.find(item => item.value === saleOrderStatus)
      return res?.label || (row.saleOrderNum ? '已删除' : '-')
    },
    // 状态回显
    statusFormat(row) {
      const res = this.statusOptions.find(item => item.value === row.status)
      return res ? `<span class="${res.color}">${res.label}</span>` : ''
    },
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 重置搜索
    resetQuery() {
      this.status = [1, 2]
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 查看详情
    handleView(row) {
      this.signet = ''
      this.contractSource = row.source
      this.contractId = row.id
      this.contractStatus = row.status
      this.seller = row.seller
      const contractId = row.id
      contractDetail({ contractId }).then(res => {
        if (res.code === 200) {
          this.info = res.data
          if (this.isJSON(this.info.file)) {
            getConfigDetail2({ configKey: 'signatures', companyId: res.data.companyId, creator: res.data.creator }).then(res => {
              this.signet = res?.data?.configValue || ''
              this.open = true
              this.resizeFun()
            })
          } else {
            this.open = true
            this.resizeFun()
          }
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 调整合同图片大小
    resizeFun() {
      const devicePixelRatio = window.devicePixelRatio
      if (devicePixelRatio !== 1) {
        if (!!window.ActiveXObject || 'ActiveXObject' in window) {
        } else this.zoom = 1 / devicePixelRatio
      }
    },
    // 打印
    handlePrint(data) {
      print({ printable: data.file, type: 'image', base64: true })
    },
    // 下载
    handleDownload(data) {
      const imgUrl = data.file
      const fileName = data.serial
      if (window.navigator.msSaveOrOpenBlob) {
        const bstr = atob(imgUrl.split(',')[1])
        let n = bstr.length
        const u8arr = new Uint8Array(n)
        while (n--) {
          u8arr[n] = bstr.charCodeAt(n)
        }
        const blob = new Blob([u8arr])
        window.navigator.msSaveOrOpenBlob(blob, fileName + '.' + 'png')
      } else {
        const a = document.createElement('a')
        a.href = imgUrl
        a.setAttribute('download', fileName)
        a.click()
      }
    },
    // 查看产品明细
    handleProductDetail(row) {
      contractPriceChange({ contractId: row.id }).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          if (data) this.$refs.contractProduct.handleView(data)
          else this.$message.info('暂无产品明细')
        } else this.$message.error(msg)
      })
    },
    // 弹框显示产品列表
    async handleDialogList() {
      await this.getList()
      this.listOpen = true
    },
    handleListQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 产品标记
    handleLabel(row, showCheckbox = false) {
      const loading = this.$loading({
        lock: true,
        text: '加载中…',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      this.purchasingProducts = []
      this.showCheckbox = showCheckbox
      if (showCheckbox) {
        this.purchasingProducts = []
        this.listOpen = false
      }
      this.labelInfo = { ...row }
      this.contractId = row.id
      contractPriceChange({ contractId: row.id }).then(async res => {
        if (res.data && res.data.length) {
          let arr = []
          const products = res.data
          await Promise.all(
            products.map(async item => {
              await getProduct(item.productId).then(async ress => {
                await getBomDetailByCode({ productCode: ress.data.productCode }).then(async child => {
                  if (child.code === 200 && child.data) {
                    const markBox = await getBomDetail({ bomId: child.data.id })
                    if (markBox.code === 200 && markBox.data) {
                      const doType = markBox.data.mark || this.doOptions[0].value
                      const hasChildren = doType === this.doOptions[0].value ? false : true
                      arr.push({ ...ress.data, children: [], productId: child.data.productId, hasChildren, doType, originAmount: item.originAmount, source: 'common', quantity: item.sjNum })
                    } else {
                      const hasChildren = false
                      arr.push({ ...ress.data, children: [], productId: child.data.productId, hasChildren, doType: this.doOptions[0].value, originAmount: item.originAmount, source: 'common', quantity: item.sjNum })
                    }
                  } else {
                    const hasChildren = false
                    arr.push({ ...ress.data, children: [], hasChildren, doType: this.doOptions[0].value, originAmount: item.originAmount, source: 'common', quantity: item.sjNum })
                  }
                })
              })
            })
          )
          this.labelList = arr
          this.key = Math.random()
          this.labelOpen = true
          loading.close()
        }
      })
    },
    // 切换操作
    handleCommand(e, row) {
      row.doType = e
      if (row.doType === 'sc' || row.doType === 'ww') {
        getBomDetailByCode({ productCode: row.productCode }).then(async child => {
          if (child.code === 200) {
            if (child.data && child.data.child && child.data.child.length) {
              const result = child.data.child.map(itt => ({ ...itt, ...itt.product, orgIds: this.orgIdsOptions.map(ittt => ittt.value), doType: this.doOptions[0].value, source: 'private' }))
              await Promise.all(
                result.map(async ite => {
                  if (ite.materialCode) {
                    const stock = await inventoryList({ number: ite.materialCode, pageNum: 1, pageSize: 1 })
                    if (stock.rows[0] && stock.rows[0].stocks) {
                      ite.jdStock = stock.rows[0].stocks ? stock.rows[0].stocks.reduce((total, item) => total + item.qty, 0) : 0
                    } else ite.jdStock = 0
                    ite.stock = stock.rows[0] ? (ite.jdStock - stock.rows[0].useQty) : 0
                  }
                })
              )
              row.bomId = child.data.id
              row.hasChildren = !!child.data.child.length
              this.$set(row, 'children', result)
              this.markProduct(row)
            } else {
              row.doType = 'cg'
              row.hasChildren = false
              this.$set(row, 'children', [])
              this.errOpen = true
              this.bomProduct = row
              this.markProduct(row)
            }
          }
        })
      } else {
        row.hasChildren = false
        this.$set(row, 'children', [])
        this.markProduct(row)
      }
    },
    // 产品标记记录
    markProduct(item) {
      markProductBom([{ productId: item.productId, mark: item.doType }]).then(res => { })
    },
    // 刷新库存
    handleRefreshStock(row) {
      const { orgIds, materialCode } = row
      if (materialCode) {
        getKingdeeStock({ orgIds, materialCode }).then(res => {
          if (res.code === 200) {
            row.stock = res.data
          }
        })
      }
    },
    // 展开行
    expandChange(row) {
      if (row.doType === 'sc' || row.doType === 'ww') {
        this.loadings = true
        getBomDetailByCode({ productCode: row.productCode }).then(async child => {
          if (child.code === 200) {
            if (child.data && child.data.child && child.data.child.length) {
              const result = child.data.child.map(itt => ({ ...itt, ...itt.product, orgIds: this.orgIdsOptions.map(ittt => ittt.value), originAmount: Number(row.originAmount) * Number(itt.quantity), doType: this.doOptions[0].value, source: 'private' }))
              await Promise.all(
                result.map(async ite => {
                  if (ite.materialCode) {
                    const stock = await inventoryList({ number: ite.materialCode, pageNum: 1, pageSize: 1 })
                    if (stock.rows[0] && stock.rows[0].stocks) {
                      ite.jdStock = stock.rows[0].stocks ? stock.rows[0].stocks.reduce((total, item) => total + item.qty, 0) : 0
                    } else ite.jdStock = 0
                    ite.stock = stock.rows[0] ? (ite.jdStock - stock.rows[0].useQty) : 0
                  }
                  const bomBox = await getBomDetailByCode({ productCode: ite.productCode })
                  if (bomBox.code === 200 && bomBox.data) {
                    const markBox = await getBomDetail({ bomId: bomBox.data.id })
                    if (markBox.code === 200 && markBox.data) {
                      ite.doType = markBox.data.mark || this.doOptions[0].value
                      ite.hasChildren = ite.doType === this.doOptions[0].value ? false : true
                    } else {
                      ite.doType = this.doOptions[0].value
                      ite.hasChildren = false
                    }
                  } else {
                    ite.doType = this.doOptions[0].value
                    ite.hasChildren = false
                  }
                })
              )
              row.bomId = child.data.id
              this.$set(row, 'children', result)
              this.loadings = false
            }
          }
        })
      }
    },
    // 回显操作类型
    formatDo(type) {
      const res = this.doOptions.find(item => item.value === type)
      return res ? res.label : '采购产品'
    },
    getRowKey(row) {
      return (row.source ? row.source : 'child') + row.id
    },
    // 表格行样式 当当前行没有子物料时，添加样式，使其展开按钮不可点击
    getRowClass(row, rowIndex) {
      if (!row.row.hasChildren) return 'row-expand-cover'
      else return ''
    },
    handleProductView(row) {
      this.$refs.productInfos.handleView(row)
    },
    handleImgView(row) {
      this.$refs.productInfos.handleImgView(row)
    },
    // 全选
    handleSelectProductAll(selection) {
      if (selection.length) {
        if (this.purchasingProducts.length) {
          selection.map(ite => {
            const idx = this.purchasingProducts.findIndex(item => item.id === ite.id)
            if (idx === -1) this.purchasingProducts.push(ite)
          })
        } else {
          this.purchasingProducts = [...selection]
        }
      } else {
        this.addList.map(ite => {
          const idx = this.purchasingProducts.findIndex(item => item.id === ite.id)
          if (idx !== -1) this.purchasingProducts.splice(idx, 1)
        })
      }
    },
    // 选择产品
    handleSelectProduct(selection, row) {
      const idx = this.purchasingProducts.findIndex(item => item.id === row.id)
      if (idx === -1) {
        this.purchasingProducts.push(row)
      } else {
        this.purchasingProducts.splice(idx, 1)
      }
    },
    // 点击采购
    handleChooseProduct(row, echo = true) {
      const idx = this.purchasingProducts.findIndex(item => item.id === row.id)
      if (idx !== -1) return
      this.purchasingProducts.push(row)
      if (echo) this.echo()
    },
    // 取消采购
    handleCancelProduct(row, echo = true) {
      const idx = this.purchasingProducts.findIndex(item => item.id === row.id)
      if (idx !== -1) this.purchasingProducts.splice(idx, 1)
      if (echo) this.echo()
    },
    // 判断是否可选择
    selectable(row, index) {
      return !row.hasChildren
    },
    // 回显选中
    echo() {
      this.$nextTick(() => {
        if (this.$refs.addTable) this.$refs.addTable.clearSelection()
        this.labelList.map(item => {
          this.purchasingProducts.map(itt => {
            if (itt.id === item.id) this.$refs.addTable.toggleRowSelection(item, true)
          })
        })
      })
    },
    // 下一步
    handleNext() {
      this.$emit('next', this.purchasingProducts)
      this.labelOpen = false
    },
    // 判断是否有选中为生产产品
    hasProduction() {
      let num = 0
      this.labelList.map(item => {
        const child = item.children.filter(itt => itt.doType == 'sc' || itt.doType == 'ww')
        if (child.length) num += 1
        if (item.doType == 'sc' || item.doType == 'ww') num += 1
      })
      return !!num
    },
    // 创建生产任务单
    handleCreateProduction() {
      this.deliveryTimeOpen = true
    },
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          let arr = []
          arr = this.handleArr(this.labelList)
          arr.forEach(el => {
            el.deliveryTime = this.parseTime(this.form.deliveryTime, '{y}-{m}-{d} 23:59:59')
          })
          Promise.all(
            arr.map(async item => {
              await addProduction(item)
            })
          )
          this.$message.success('创建生产任务单成功')
          this.labelOpen = false
          this.refreshList()
          this.deliveryTimeOpen = false
        } else {
          return false
        }
      })
    },
    // 递归数组
    handleArr(data) {
      let that = this
      // 初始化结果数组
      let result = []
      // 定义递归函数
      function recurse(items) {
        for (let item of items) {
          result.push({ bomId: item.bomId, objId: that.contractId, type: 0, productId: item.id, productName: item.productName, quantity: Number(item.quantity), remark: item.remark, unit: item.unit, mark: item.doType })

          // 如果对象有 children 属性且是数组，则递归调用
          if (Array.isArray(item.children)) {
            recurse(item.children)
          }
        }
      }
      // 循环
      recurse(data)

      // 返回最终结果
      return result
    },
    // 判断是不是json
    isJSON(str) {
      try {
        JSON.parse(str)
        return true
      } catch (e) {
        return false
      }
    },
    // 判断是不是base64
    isBase64Image(str) {
      if (typeof str === 'string') {
        return str.startsWith('data:image/') && str.includes(';base64,')
      }
      return false
    },
    // 前往Bom
    goBom() {
      this.$router.push({
        path: '/private/productBom'
      })
    },
    // 新建销售订单
    handleSaleOrderCreate(row) {
      this.saleOrderCreate = true
      this.$nextTick(() => {
        if (this.$refs.saleOrderCreate) this.$refs.saleOrderCreate.handleCreate(row)
      })
    },
    // 回调
    handleCallBack(refresh = false) {
      this.saleOrderCreate = false
      this.showDetail = false
      if (this.contractId) this.handleRowRefresh()
      else if (refresh) this.refreshList()
    },
    // 查看金蝶销售订单详情
    handleSaleOrderDetail(row, contractId = undefined) {
      if (!row.saleOrderNum) return
      this.contractId = contractId
      this.showDetail = true
      const data = { BillNo: row.saleOrderNum }
      this.$nextTick(() => {
        this.$refs.saleOrderDetail.getInfo(data)
      })
    },
    // 下派生产任务单
    handleDispatchOrder(row, type, id) {
      this.dispatchOrder = true
      this.contractId = id
      const list = row.production.filter(item => item.mark === type)
      if (!list.length) return
      this.$nextTick(() => {
        this.$refs.dispatchOrder.handleOpen(list)
      })
    },
    // 新增采购产品
    handleCreateDemand(row) {
      this.createDemand = true
      const list = row.production.filter(item => item.mark === 'cg')
      if (!list.length) return
      this.$nextTick(() => {
        this.$refs.createDemand.handleOpen(list)
      })
    },
    handleDispatchCallBack(refresh = false) {
      this.dispatchOrder = false
      if (refresh) this.handleRowRefresh()
    },
    // 去发货
    handleSend(row) {
      this.sendProduct = true
      this.$nextTick(() => {
        if (this.$refs.sendProduct) this.$refs.sendProduct.handleOpen(row)
      })
    },
    sendProductBack(refresh = false) {
      this.sendProduct = false
      this.getList()
    },
    handleForSellerApply(row) {
      forSellerApply({
        id: row.id
      }).then(res => {
        if (res.code == 200) {
          this.$message.success('付款申请已提交!')
          this.getList()
        }
      })
    },
    handleSetBom() {
      const product = {
        productCode: this.bomProduct.productCode,
        productName: this.bomProduct.productName,
        id: this.bomProduct.id
      }
      this.errOpen = false
      this.bomCreate = true
      this.$nextTick(() => {
        this.$refs.bomCreate.handleAdd(product)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';

.Box {
  padding: 15px 20px;
}

.custom-dialog ::v-deep {
  .print {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 0 20px;

    &-item {
      padding: 0 50px;
      height: 50px;
      line-height: 50px;
      text-align: center;
      margin-left: 10px;
      border: 1px solid #cbd6e2;
      border-radius: 5px;
      font-size: 16px;
      color: $white;
      cursor: pointer;
      background-color: $blue;

      &:hover {
        opacity: 0.8;
      }
    }
  }

  .labelTitle {
    font-size: 14px;
    padding-bottom: 12px;
    color: #999999;
  }

  .labelInfo {
    border-top: 1px solid #e2e6f3;
    border-bottom: 1px solid #e2e6f3;
    margin-bottom: 12px;
    padding: 10px 0;

    .el-col {
      line-height: 20px;
      padding-top: 6px;
      padding-bottom: 6px;

      span {
        display: inline-block;
        color: #666666;
        font-size: 12px;
        width: 4em;
        margin-right: 20px;
      }

      b {
        font-weight: normal;
        font-size: 14px;
        color: #333333;

        &.price {
          color: #f43f3f;
        }
      }
    }
  }

  .table-child {
    margin: -5px 0;
    display: flex;
    align-items: center;
    background-color: #f0f4f9;

    &-title {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      width: 80px;
    }

    .custom-table {
      flex: 1;
      overflow: hidden;

      .el-table__header-wrapper {
        .el-checkbox__inner {
          display: none !important;
        }
      }
    }
  }

  .table-stock {
    display: flex;
    align-items: center;
  }

  .row-expand-cover td .el-table__expand-icon {
    visibility: hidden;
  }

  .dropdown-primary {
    color: #2e73f3;
  }

  .dropdown-orange {
    color: #f35d09;
  }

  .listSearch {
    width: 650px;
    height: 46px;
    margin: 30px auto;
    border: 1px solid #cbd7e2;
    border-radius: 5px;
    padding: 3px;
    display: flex;
    align-items: center;

    .el-input__inner {
      border-width: 0;
    }
  }

  .craftBox {
    background: #f8f9fb;
    border-radius: 5px;
    border: 1px solid #cbd6e2;

    .craftBox_list {
      border-bottom: 1px solid #cbd6e2;

      &:last-child {
        border-bottom: none;
      }

      .craftBox_list_title {
        width: 110px;
        height: 30px;
        background: #bec6d0;
        border-bottom-right-radius: 10px;
        padding-left: 16px;
        line-height: 30px;
        font-weight: 500;
        font-size: 14px;
        color: #ffffff;
        margin-bottom: 15px;
      }

      .craftBox_list_concent {
        padding: 0 15px;

        .concent_top {
          display: flex;
          align-items: center;
          margin-bottom: 16px;
        }

        .concent_bottom {
          margin-bottom: 10px;
        }

        .concent_item {
          display: flex;
          align-items: center;
          margin-right: 60px;

          .title {
            font-weight: 400;
            font-size: 12px;
            color: #999999;
            line-height: 20px;
            margin-right: 20px;
          }

          .text {
            font-weight: 500;
            font-size: 14px;
            color: #333333;
            line-height: 20px;
          }

          .fee {
            font-weight: 500;
            font-size: 14px;
            color: #f35d09;
            line-height: 20px;
          }

          .device {
            display: flex;
            align-items: center;

            .text {
              display: block;
              background: #e1e4e8;
              border-radius: 5px;
              font-weight: 500;
              font-size: 14px;
              color: #333333;
              line-height: 20px;
              padding: 5px 10px;
              margin-right: 10px;
            }
          }
        }
      }
    }
  }
}

.errTips {
  padding: 10px 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  .img {
    width: 36px;
    height: 36px;
    margin-bottom: 17px;
  }

  .title {
    font-weight: 500;
    font-size: 14px;
    color: #ed4040;
    line-height: 20px;
    margin-bottom: 10px;
  }

  .text {
    font-weight: 400;
    font-size: 12px;
    color: #666666;
    line-height: 20px;
    margin-bottom: 47px;
  }

  .btn {
    display: flex;
    align-items: center;
    justify-content: center;

    .cancel {
      width: 269px;
      height: 50px;
      border-radius: 5px 5px 5px 5px;
      border: 1px solid #cbd6e2;
      font-weight: 400;
      font-size: 16px;
      color: #999999;
      line-height: 50px;
      text-align: center;
      margin-right: 10px;
      cursor: pointer;
    }

    .go {
      width: 269px;
      height: 50px;
      background: #2e73f3;
      border-radius: 5px 5px 5px 5px;
      font-weight: 500;
      font-size: 16px;
      color: #ffffff;
      line-height: 50px;
      text-align: center;
      cursor: pointer;
    }
  }
}

.deliveryTimeBox {
  padding: 10px 20px;

  .btn {
    display: flex;
    align-items: center;
    justify-content: flex-end;

    .cancel {
      width: 200px;
      height: 50px;
      border-radius: 5px 5px 5px 5px;
      border: 1px solid #cbd6e2;
      font-weight: 400;
      font-size: 16px;
      color: #999999;
      line-height: 50px;
      text-align: center;
      margin-right: 10px;
      cursor: pointer;
    }

    .go {
      width: 200px;
      height: 50px;
      background: #2e73f3;
      border-radius: 5px 5px 5px 5px;
      font-weight: 500;
      font-size: 16px;
      color: #ffffff;
      line-height: 50px;
      text-align: center;
      cursor: pointer;
    }
  }
}

.blue {
  ::v-deep .el-icon-arrow-right {
    color: #2e73f3;
    font-size: 14px;
  }

  ::v-deep .el-table__header-wrapper {
    .el-checkbox {
      display: none !important;
    }
  }
}

.amountTo {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 48px;
  background: #ecf3ff;
  border-radius: 5px;
  border: 1px solid #2e73f3;
  padding: 0 15px;

  .amountTo_item {
    font-weight: 400;
    font-size: 14px;
    color: #666666;
    display: flex;
    align-items: center;
    justify-content: center;

    .text {
      font-weight: 500;
      font-size: 18px;
      color: #2e73f3;
    }
  }
}

.tipsBox {
  border-bottom: 1px solid #e2e6f3;
  margin-bottom: 12px;

  .tipsTitle {
    font-weight: 400;
    font-size: 12px;
    color: #f35d09;
    line-height: 20px;
    margin-bottom: 10px;
  }
}
</style>
