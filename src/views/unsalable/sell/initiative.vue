<template>
  <div @click="handleClickOutside">
    <template v-if="showList">
      <div class="custom-search" style="padding-top: 18px">
        <div class="flex" :style="{ justifyContent: showSearch ? 'space-between' : 'flex-end' }">
          <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
            <el-form-item label="合同编号" prop="serial">
              <el-input v-model="queryParams.serial" placeholder="请输入合同编号" clearable @keyup.enter.native="handleQuery" />
            </el-form-item>
            <el-form-item label="关键词" prop="keyword">
              <el-input v-model="queryParams.keyword" placeholder="请输入关键词" clearable @keyup.enter.native="handleQuery" />
            </el-form-item>
            <el-form-item label="状态" prop="status">
              <el-select v-model="status" placeholder="请选择状态" clearable multiple style="min-width: 250px">
                <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
            <!-- 创建人 -->
            <el-form-item label="创建人" prop="createBy">
              <el-input v-model="queryParams.createBy" placeholder="请输入创建人" clearable @keyup.enter.native="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" style="background-color: #2e73f3; border-color: #2e73f3" size="small" @click="handleQuery">搜索</el-button>
              <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
              <el-button type="primary" icon="el-icon-plus" @click="handleCreate">添加合同</el-button>
              <div style="position: relative; width: 100px; margin-left: 10px; display: inline-block" v-hasPermi="['contract:participant:setting']">
                <el-button type="primary" plain icon="el-icon-s-tools" @click="handleSetting">参与设置</el-button>
                <div ref="participantSetting" class="participantSetting" v-if="participantSetting">
                  <b>请选择单个部门需要查看合同即默认参与人</b>
                  <span>此设置只可针对录入合同，正常合同不可设置</span>
                  <el-cascader-panel v-model="participantList" :options="approvalsOptions" :props="approvalsProps" @change="handleSettingChange"></el-cascader-panel>
                </div>
              </div>
            </el-form-item>
          </el-form>
          <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns" isSetitem @updateColumns="updateColumns"></right-toolbar>
        </div>
      </div>
      <el-tabs class="custom-tabs" v-model="queryParams.identity" @tab-click="handleTabClick" v-if="!checkPermi(['purchasing:contract:all'])">
        <el-tab-pane label="我录入的" name="creator"></el-tab-pane>
        <el-tab-pane label="我参与的" name="participant"></el-tab-pane>
      </el-tabs>
      <div style="padding: 15px 20px 0">
        <el-table v-loading="loading" ref="allTable" stripe :data="list" row-key="id" :key="key" style="width: 100%" class="custom-table custom-table-cell10" v-if="loading || total > 0">
          <el-table-column align="center" type="index" label="序号" width="50" v-if="columns[0].visible"></el-table-column>
          <el-table-column align="center" prop="serial" label="合同编号" show-overflow-tooltip width="120" v-if="columns[1].visible"></el-table-column>
          <el-table-column align="center" prop="sellerName" label="买方" show-overflow-tooltip min-width="130" v-if="columns[2].visible"></el-table-column>
          <el-table-column align="center" label="产品明细" v-if="columns[3].visible">
            <template slot-scope="{ row }">
              <el-button type="text" size="small" icon="el-icon-view" @click="handleProductDetail(row)">查看</el-button>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="signingTime" label="签订时间" show-overflow-tooltip v-if="columns[4].visible"></el-table-column>
          <el-table-column align="center" prop="address" label="签订地点" show-overflow-tooltip v-if="columns[5].visible"></el-table-column>
          <el-table-column align="center" prop="amount" label="订单总金额" show-overflow-tooltip v-if="columns[6].visible">
            <template slot-scope="{ row }">
              <span class="table-orange" style="font-size: 14px">{{ row.amount ? '￥' + row.amount : '￥0' }}{{ row.isIncludingTax ? '(含税)' : '(不含税)' }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="status" label="状态" show-overflow-tooltip v-if="columns[7].visible">
            <template slot-scope="{ row }">
              <div v-html="statusFormat(row)"></div>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="createBy" label="创建人" show-overflow-tooltip v-if="columns[8].visible">
            <template slot-scope="{ row }">
              <div>{{ row.userV2 ? (row.userV2.realName ? row.userV2.realName : row.userV2.nickName) : row.createBy }}</div>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="saleOrderNum" label="销售订单号" v-if="hasmaterialCode && companyId == 14 && columns[10].visible">
            <template slot-scope="{ row }">
              <i class="el-icon-loading" v-if="row.loading"></i>
              <template v-else>
                <div class="flex align-center justify-center" v-if="row.refreshingSaleOrder">
                  <el-tooltip effect="dark" :content="'状态：' + formatSaleOrderStatus(item)" v-for="(item, index) in row.saleOrderStatus" :key="index">
                    <span class="table-link" @click="handleSaleOrderDetail(item, row.id)">{{ item.saleOrderNum }}</span>
                  </el-tooltip>
                  <el-tooltip effect="dark" content="刷新中...">
                    <i class="pointer el-icon-loading" style="margin-left: 5px; color: #2e73f3; animation: rotate 1s linear infinite"></i>
                  </el-tooltip>
                </div>
                <div class="flex align-center justify-center" v-else-if="row.saleOrderStatus && row.saleOrderStatus.length > 0">
                  <el-tooltip effect="dark" :content="'状态：' + formatSaleOrderStatus(item)" v-for="(item, index) in row.saleOrderStatus" :key="index">
                    <span class="table-link" @click="handleSaleOrderDetail(item, row.id)">{{ item.saleOrderNum }}</span>
                  </el-tooltip>
                  <el-tooltip effect="dark" content="点击刷新">
                    <i class="pointer el-icon-refresh" style="margin-left: 5px; color: #2e73f3" @click="handleRefreshSaleOrderStatus(row)"></i>
                  </el-tooltip>
                </div>
                <div class="flex align-center justify-center" v-else>
                  <span>-</span>
                  <el-tooltip effect="dark" content="点击刷新" v-if="row.saleOrderNum">
                    <i class="pointer el-icon-refresh" style="margin-left: 5px; color: #2e73f3" @click="handleRefreshSaleOrderStatus(row)"></i>
                  </el-tooltip>
                </div>
              </template>
            </template>
          </el-table-column>
          <template v-if="companyId == 14">
            <el-table-column align="center" label="生产工单" show-overflow-tooltip v-if="columns[11].visible">
              <template slot-scope="{ row }">
                <el-tooltip effect="dark" content="点击下派生产任务单" :disabled="formatProduction(row.production, 'sc') == '-'" placement="top">
                  <span class="table-link" @click="handleDispatchOrder(row, 'sc', row.id)">{{ formatProduction(row.production, 'sc') }}</span>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column align="center" label="委外工单" show-overflow-tooltip v-if="columns[12].visible">
              <template slot-scope="{ row }">
                <el-tooltip effect="dark" content="点击下派生产任务单" :disabled="formatProduction(row.production, 'ww') == '-'" placement="top">
                  <span class="table-link" @click="handleDispatchOrder(row, 'ww', row.id)">{{ formatProduction(row.production, 'ww') }}</span>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column align="center" label="采购产品" show-overflow-tooltip v-if="columns[9].visible">
              <template slot-scope="{ row }">
                <el-tooltip effect="dark" content="点击添加采购" :disabled="formatProduction(row.production, 'cg') == '-'" placement="top" v-if="row.production && row.production.length">
                  <span class="table-link" @click="handleCreateDemand(row)">{{ formatProduction(row.production, 'cg') }}</span>
                </el-tooltip>
                <span v-else>
                  <el-tooltip effect="dark" content="点击添加采购" placement="top">
                    <span class="table-link" @click="handleCreateDemand(row)">新增采购产品</span>
                  </el-tooltip>
                </span>
              </template>
            </el-table-column>
          </template>
          <template v-else>
            <el-table-column align="center" label="采购产品" show-overflow-tooltip v-if="columns[9].visible">
              <template slot-scope="{ row }">
                <el-tooltip effect="dark" content="点击添加采购" placement="top">
                  <span class="table-link" @click="handleCreateDemand(row)">新增采购产品</span>
                </el-tooltip>
              </template>
            </el-table-column>
          </template>
          <el-table-column align="center" label="操作" :width="queryParams.status.toString() == '-1' ? 120 : getActionColumnWidth()">
            <template slot-scope="{ row }">
              <template v-if="queryParams.status.toString() == '-1'">
                <!-- 只有查看详情 -->
                <el-button class="table-btn" @click="handleDetail(row)">查看详情</el-button>
              </template>
              <template v-else>
                <!-- 常用按钮 -->
                <template v-for="action in getCommonActions(row)">
                  <el-button :key="action.key" :class="action.className" @click="action.handler(row)" v-if="action.show">
                    {{ action.label }}
                  </el-button>
                </template>
                <!-- 更多操作 -->
                <el-popover trigger="hover" v-if="hasMoreActions(row)">
                  <button type="button" class="table-btn primary" slot="reference">
                    更多操作
                    <i class="el-icon-arrow-down el-icon--right"></i>
                  </button>
                  <div class="popover-button">
                    <template v-for="action in getAllActions(row)">
                      <div :key="action.key" class="popover-button-item" v-if="action.show">
                        <el-button :class="action.className" @click="action.handler(row)">
                          {{ action.label }}
                        </el-button>
                        <i class="popover-button-icon" :class="[isCommonAction(action.key) ? 'el-icon-star-on' : 'el-icon-star-off', { active: isCommonAction(action.key) }]" @click="toggleCommonAction(action.key)" :title="isCommonAction(action.key) ? '取消常用' : '设置为常用'"></i>
                      </div>
                    </template>
                  </div>
                </el-popover>
              </template>
            </template>
          </el-table-column>
        </el-table>
        <el-empty v-if="total == 0 && !loading" />
        <div class="custom-pagination">
          <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
        </div>
      </div>
    </template>
    <!--添加合同-->
    <create ref="create" @refresh="getList" />
    <!--合同产品明细-->
    <contract-product ref="contractProduct" />
    <!-- 合同详情 -->
    <el-dialog v-dialogDragBox title="合同详情" :visible.sync="open" width="1150px" class="custom-dialog">
      <div style="text-align: center" :style="{ zoom: zoom }">
        <img style="max-width: 100%" :src="imgPath + item" v-for="(item, index) in fileFormat(info.file)" :key="index" />
      </div>
    </el-dialog>
    <!--产品标记-->
    <el-dialog v-dialogDragBox title="产品标记" :visible.sync="labelOpen" width="1150px" class="custom-dialog">
      <div style="padding: 0 20px">
        <div class="labelTitle">合同信息</div>
        <el-row :gutter="10" class="labelInfo">
          <el-col :span="9">
            <span>买方</span>
            <b>{{ labelInfo.buyerName || '' }}</b>
          </el-col>
          <el-col :span="6">
            <span>签订时间</span>
            <b>{{ labelInfo.signingTime || '' }}</b>
          </el-col>
          <el-col :span="9">
            <span>签订地点</span>
            <b>{{ labelInfo.address || '' }}</b>
          </el-col>
          <el-col :span="24">
            <span>合同金额</span>
            <b class="price">{{ labelInfo.amount ? '￥' + labelInfo.amount : '' }}{{ labelInfo.isIncludingTax ? '(含税)' : '(不含税)' }}</b>
          </el-col>
        </el-row>
        <div class="labelTitle">合同产品列表</div>
        <el-table :key="key" ref="addTable" stripe :data="labelList" :row-key="getRowKey" :row-class-name="getRowClass" style="width: 100%; margin-bottom: 12px" class="custom-table custom-table-cell5 blue" @select="handleSelectProduct" @select-all="handleSelectProductAll" :default-expand-all="false" @expand-change="expandChange" v-loading="loadings">
          <el-table-column align="left" type="expand">
            <template slot-scope="scope">
              <recursive-table v-if="scope.row.children && scope.row.children.length" :items="scope.row.children" :showCheckbox="showCheckbox" :bomId="scope.row.bomId" @handleChooseProduct="handleChooseProduct" @handleCancelProduct="handleCancelProduct" :purchasingProducts="purchasingProducts" />
            </template>
          </el-table-column>
          <el-table-column align="center" type="selection" width="50" :reserve-selection="true" v-if="showCheckbox" :selectable="selectable"></el-table-column>
          <el-table-column align="center" type="index" label="序号"></el-table-column>
          <el-table-column align="center" prop="product_name" label="产品名称" show-overflow-tooltip>
            <template slot-scope="{ row }">
              <span class="table-link" @click="handleProductView(row)">{{ row.productName }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="picture1" label="图片" width="75">
            <template slot-scope="{ row }">
              <el-button type="text" size="mini" icon="el-icon-view" @click="handleImgView(row)">查看</el-button>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="specs" label="规格" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="model" label="型号" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="productCode" label="产品编码" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="materialQuality" label="材质" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="unit" label="单位" width="50"></el-table-column>
          <el-table-column align="center" label="采购量">
            <template slot-scope="{ row }">{{ row.quantity + row.unit }}</template>
          </el-table-column>
          <el-table-column align="center" prop="jdStock" label="金蝶库存" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" label="自由客库存" prop="stock">
            <!-- <template slot-scope="{ row }">
              <div class="table-stock" v-if="row.materialCode">
                <el-select v-model="row.orgIds" collapse-tags multiple size="mini" @change="handleRefreshStock(row)">
                  <el-option v-for="item in orgIdsOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
                <el-tooltip effect="dark" content="点击查询库存">
                  <el-button type="text" icon="el-icon-refresh" @click="handleRefreshStock(row)">{{ row.stock || 0
                  }}</el-button>
                </el-tooltip>
              </div>
              <span v-else>-</span>
            </template> -->
          </el-table-column>
          <el-table-column align="center" label="操作">
            <template slot-scope="{ row }">
              <el-dropdown @command="handleCommand($event, row)" v-if="!showCheckbox">
                <span class="el-dropdown-link" :class="row.doType === 'cg' ? 'dropdown-primary' : 'dropdown-orange'">
                  {{ formatDo(row.doType) }}
                  <i class="el-icon-caret-bottom"></i>
                </span>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item v-for="item in doOptions" :key="item.value" :command="item.value">{{ item.label }}</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
              <el-button type="text" size="mini" icon="el-icon-circle-check" @click="handleChooseProduct(row)" v-if="showCheckbox" :disabled="row.hasChildren">采购</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn" @click="labelOpen = false">取消</el-button>
        <el-button class="custom-dialog-btn primary" @click="labelOpen = false" v-if="!showCheckbox && !hasProduction()">关闭</el-button>
        <el-button class="custom-dialog-btn primary" @click="handleCreateProduction" v-if="!showCheckbox && hasProduction()">确定</el-button>
        <el-badge :value="purchasingProducts.length" :hidden="!purchasingProducts.length" v-if="showCheckbox">
          <el-button :disabled="!purchasingProducts.length" class="custom-dialog-btn primary" @click="handleNext">下一步</el-button>
        </el-badge>
      </div>
    </el-dialog>
    <!-- 产品详情 -->
    <product-dialog ref="productInfos"></product-dialog>
    <!--从录入合同添加采购产品-->
    <el-dialog v-dialogDragBox title="从录入合同添加采购产品" :visible.sync="listOpen" width="1150px" class="custom-dialog">
      <div style="padding: 0 20px">
        <div class="listSearch">
          <el-input v-model="queryParams.serial" placeholder="请输入合同编号"></el-input>
          <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        </div>
        <el-table v-loading="loading" ref="dialogTable" stripe :data="list" row-key="id" :key="key" style="width: 100%" class="custom-table custom-table-cell10">
          <el-table-column align="center" type="index" label="序号" width="50"></el-table-column>
          <el-table-column align="center" prop="serial" label="合同编号" show-overflow-tooltip width="120"></el-table-column>
          <el-table-column align="center" prop="buyerName" label="买方" show-overflow-tooltip min-width="130"></el-table-column>
          <el-table-column align="center" label="产品明细">
            <template slot-scope="{ row }">
              <el-button type="text" size="small" icon="el-icon-view" @click="handleProductDetail(row)">查看</el-button>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="signingTime" label="签订时间" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="address" label="签订地点" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="amount" label="订单总金额" show-overflow-tooltip>
            <template slot-scope="{ row }">
              <span class="table-orange" style="font-size: 14px">{{ row.amount ? '￥' + row.amount : '' }}{{ row.isIncludingTax ? '(含税)' : '(不含税)' }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="操作" min-width="120">
            <template slot-scope="{ row }">
              <button type="button" class="table-btn primary" @click="handleLabel(row, true)">采购合同产品</button>
            </template>
          </el-table-column>
        </el-table>

        <div class="custom-pagination">
          <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
        </div>
      </div>
    </el-dialog>
    <!-- 拆分提示 -->
    <el-dialog v-dialogDragBox title="提示" :visible.sync="errOpen" width="748px" class="custom-dialog" :modal-append-to-body="false">
      <div class="errTips">
        <img class="img" src="@/assets/images/errTips.png" alt="" />
        <div class="title">当前产品尚未设置BOM与工艺不可标记为生产</div>
        <div class="text">如要更改请前往产品BOM为该产品增加BOM设置，完成后可以标记为生产</div>
        <div class="btn">
          <div class="cancel" @click="errOpen = false">取消</div>
          <div class="go" @click="handleSetBom">立即设置BOM</div>
        </div>
      </div>
    </el-dialog>
    <!-- 交货日期 -->
    <el-dialog v-dialogDragBox title="交货日期" :visible.sync="deliveryTimeOpen" width="650px" class="custom-dialog" :modal-append-to-body="false">
      <div class="deliveryTimeBox">
        <el-form ref="form" :model="form" :rules="rules" label-width="6em" class="custom-form">
          <el-form-item label="交货日期" prop="deliveryTime">
            <el-date-picker v-model="form.deliveryTime" type="date" placeholder="选择交货日期" style="width: 100%"></el-date-picker>
          </el-form-item>
        </el-form>
        <div class="btn">
          <div class="cancel" @click="deliveryTimeOpen = false">取消</div>
          <div class="go" @click="submitForm">提交</div>
        </div>
      </div>
    </el-dialog>
    <!-- 新建销售订单 -->
    <sale-order-create ref="saleOrderCreate" @callBack="handleCallBack" v-if="saleOrderCreate" />
    <!-- 金蝶销售订单详情 -->
    <sale-order-detail ref="saleOrderDetail" @callBack="handleCallBack" v-if="showDetail" />
    <!-- 下派生产任务单 -->
    <dispatch-order ref="dispatchOrder" @callBack="handleDispatchCallBack" v-if="dispatchOrder" />
    <!-- 新增采购产品 -->
    <create-demand ref="createDemand" @callBack="createDemand = false" v-if="createDemand" />
    <!-- 去发货 -->
    <send-product ref="sendProduct" @callBack="sendProductBack" v-if="sendProduct" />
    <!-- 新增BOM -->
    <bom-create ref="bomCreate" @callBack="bomCreate = false" v-if="bomCreate" />
  </div>
</template>
<script>
import create from './initiativeCreate'
import { contractDetail, contractList, contractPriceChange, contractDelete, contractParticipantList, contractParticipant } from '@/api/purchase'
import contractProduct from '@/views/components/product/contact'
import saleOrderCreate from '@/views/kingdee/purchase/saleOrder/create'
import SaleOrderDetail from '@/views/kingdee/purchase/saleOrder/detail'
import { getConfigDetail } from '@/api/config'
import RecursiveTable from './RecursiveTable.vue'
import { getPrivateduct } from '@/api/system/privateduct'
import { getBomDetailByCode, getKingdeeStock, getBomDetail, markProductBom } from '@/api/bom'
import { addProduction } from '@/api/production'
import ProductDialog from '@/views/public/product/dialog'
import { getContractDetail } from '@/api/kingdee/purchase/saleOrder'
import { getProductionList } from '@/api/production'
import DispatchOrder from '@/views/produce/dispatch'
import CreateDemand from './createDemand'
import SendProduct from '@/views/sendProduct/send'
import { deptTreeSelect, listUser } from '@/api/system/user'
import { checkPermi } from '@/utils/permission'
import { inventoryQty, inventoryList } from '@/api/inventory'
import BomCreate from '@/views/bom/create'

export default {
  props: {
    showCheckbox: {
      type: Boolean,
      default: false
    },
    showList: {
      type: Boolean,
      default: true
    }
  },
  components: { contractProduct, create, saleOrderCreate, RecursiveTable, SaleOrderDetail, ProductDialog, DispatchOrder, CreateDemand, SendProduct, BomCreate },
  data() {
    return {
      loading: true,
      list: [],
      total: 0,
      key: 1,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        serial: undefined,
        keyword: undefined,
        status: undefined,
        orderType: 'manualInput',
        identity: 'creator', // creator 创建人  participant 参与人
        createBy: undefined
      },
      status: [1, 2, 3],
      statusOptions: [
        { label: '无效', value: -1, color: 'color-red' },
        { label: '正常', value: 1, color: 'color-disabled' },
        { label: '已签署', value: 2, color: 'color-success' },
        { label: '已完成', value: 3, color: 'color-blue' }
      ],
      // 合同详情
      open: false,
      contractStatus: undefined,
      zoom: 1,
      info: {},
      // 创建销售订单
      saleOrderCreate: false,
      // 是否有金蝶物料编码
      hasmaterialCode: false,

      // 产品标记
      loadings: false,
      labelOpen: false,
      labelInfo: {},
      labelList: [],
      orgIdsOptions: [
        { label: '世盛总部', value: 100071 },
        { label: '世盛生产部', value: 100072 },
        { label: '世盛销售部', value: 100073 },
        { label: '市场部', value: 100074 },
        { label: '徽标厂', value: 100075 },
        { label: '镀锌厂', value: 100076 },
        { label: '模具厂', value: 100077 },
        { label: '冲压厂', value: 100078 }
      ],
      doOptions: [
        { label: '采购产品', value: 'cg' },
        { label: '生产产品', value: 'sc' },
        { label: '委外产品', value: 'ww' }
      ],
      purchasingProducts: [],
      listOpen: false,
      processList: [],
      signet: '',
      errOpen: false,
      deliveryTimeOpen: false,
      bomCreate: false,
      bomProduct: {},
      form: {
        deliveryTime: undefined
      },
      rules: {
        deliveryTime: [{ type: 'date', required: true, message: '请选择交货日期', trigger: 'change' }]
      },
      showDetail: false,
      // 销售订单状态
      saleOrderStatusOptions: [
        { value: 'A', label: '创建' },
        { value: 'B', label: '审核中' },
        { value: 'C', label: '已审核' },
        { value: 'D', label: '重新审核' },
        { value: 'Z', label: '暂存' }
      ],
      sendProduct: false,
      dispatchOrder: false,
      createDemand: false,
      // 参与设置
      userList: [],
      approvalsOptions: [],
      approvalsProps: {
        expandTrigger: 'hover',
        multiple: true,
        emitPath: false
      },
      participantList: [],
      participantSetting: false,
      showSearch: true,
      columns: [
        { key: 0, label: `序号`, visible: true },
        { key: 1, label: `合同编号`, visible: true },
        { key: 2, label: `买方`, visible: true },
        { key: 3, label: `产品明细`, visible: true },
        { key: 4, label: `签订时间`, visible: true },
        { key: 5, label: `签订地点`, visible: true },
        { key: 6, label: `订单总金额`, visible: true },
        { key: 7, label: `状态`, visible: true },
        { key: 8, label: `创建人`, visible: true },
        { key: 9, label: `采购产品`, visible: true },
        { key: 10, label: `生产工单`, visible: true },
        { key: 11, label: `委外工单`, visible: true },
        { key: 12, label: `销售订单号`, visible: true }
      ],
      // 常用按钮配置
      commonActions: []
    }
  },
  created() {
    this.getConfig()
    this.getList()
    this.getApprovalsOptions()
    const columns = localStorage.getItem(this.userId + '.initiativeContractColumns')
    if (columns) this.columns = JSON.parse(columns)
    if (this.companyId != 14) this.columns = this.columns.slice(0, 10)

    // 加载常用按钮配置
    const commonActions = localStorage.getItem(this.userId + '.initiativeContractCommonActions')
    if (commonActions) this.commonActions = JSON.parse(commonActions)
  },
  computed: {
    companyId() {
      return this.$store.state.user.companyId
    },
    // 获取当前用户ID
    userId() {
      return this.$store.getters.info.userId
    }
  },
  methods: {
    checkPermi,
    // 改变显隐列缓存
    updateColumns(data = []) {
      localStorage.setItem(this.userId + '.initiativeContractColumns', JSON.stringify(data))
    },
    // 查询企业参数是否有金蝶物料编码
    getConfig() {
      getConfigDetail({ configKey: 'kingdee.material.code' }).then(res => {
        const { code, data } = res
        if (code === 200) this.hasmaterialCode = data.hasOwnProperty('configValue') && data.configValue === 'true'
      })
    },
    // 格式话生产工单
    formatProduction(production, type) {
      if (!production || !production.length) return '-'
      const res = production.filter(item => item.mark === type)
      return res.length ? `${res.length}个` : '-'
    },
    // prettier-ignore
    getList() {
      this.loading = true
      this.queryParams.status = this.status.toString()
      // 先加载基础合同列表数据
      contractList(this.queryParams).then(res => {
        if (res.code === 200) {
          this.list = res.rows.map(item => ({ ...item, loading: true }))
          this.total = res.total
          this.key = Math.random()
          // 基础数据加载完成后，异步加载额外数据
          this.loadExtraData()
        } else {
          this.$message.error(res.msg)
        }
      }).finally(() => {
        this.loading = false
      })
    },
    // 加载额外数据的方法
    async loadExtraData() {
      if (!this.list.length) return
      // 使用 Promise.all 并行加载每个合同的额外数据
      await Promise.all(
        this.list.map(async (row, index) => {
          try {
            // 加载销售订单数据
            if (row.saleOrderNum) {
              const saleOrderStatus = await getContractDetail({ contractId: row.id })
              const data = saleOrderStatus?.data
              if (Object.keys(data).length === 0) {
                this.$set(this.list[index], 'saleOrderStatus', undefined)
              } else {
                this.$set(
                  this.list[index],
                  'saleOrderStatus',
                  Object.keys(data).map(key => ({ saleOrderNum: key, ...data[key] }))
                )
              }
            }
            // 加载生产工单数据
            const production = await getProductionList({ pageSize: 100, pageNum: 1, objId: row.id, type: 0, lower: false })
            if (production.code === 200 && production.rows.length) {
              this.$set(this.list[index], 'production', production.rows)
            }
            this.$set(this.list[index], 'loading', false)
          } catch (error) {
            console.error('加载额外数据失败:', error)
          }
        })
      )
    },
    // 刷新列表
    // prettier-ignore
    refreshList() {
      this.loading = true
      this.queryParams.status = this.status.toString()
      // 先加载基础合同列表数据
      contractList(this.queryParams).then(res => {
        if (res.code === 200) {
          this.list = res.rows.map(item => ({ ...item, loading: true }))
          this.total = res.total
          // 基础数据加载完成后，异步加载额外数据
          this.loadExtraData()
        }
      }).finally(() => {
        this.loading = false
      })
    },
    // 刷新单行
    async handleRowRefresh() {
      if (!this.contractId) return
      const saleOrderStatus = await getContractDetail({ contractId: this.contractId })
      const data = saleOrderStatus?.data
      const index = this.list.findIndex(item => item.id === this.contractId)
      if (index !== -1) {
        if (Object.keys(data).length === 0) {
          this.$set(this.list, index, {
            ...this.list[index],
            saleOrderStatus: undefined
          })
        } else {
          this.$set(this.list, index, {
            ...this.list[index],
            saleOrderStatus: Object.keys(data).map(key => {
              return {
                saleOrderNum: key,
                ...data[key]
              }
            })
          })
        }
      }
      const production = await getProductionList({ pageSize: 100, pageNum: 1, objId: this.contractId, type: 0, lower: false })
      if (production.code === 200 && production.rows.length) this.list[index].production = production.rows
      this.contractId = undefined
    },
    // 切换身份
    handleTabClick(tab, event) {
      this.queryParams.identity = tab.name
      this.getList()
    },
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 重置搜索
    resetQuery() {
      this.status = [1, 2, 3]
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 销售订单状态回显
    formatSaleOrderStatus(row) {
      const saleOrderStatus = row?.result?.result?.DocumentStatus
      if (!saleOrderStatus) return '-'
      const res = this.saleOrderStatusOptions.find(item => item.value === saleOrderStatus)
      return res?.label || (row.saleOrderNum ? '已删除' : '-')
    },
    // 状态回显
    statusFormat(row) {
      const res = this.statusOptions.find(item => item.value === row.status)
      return res ? `<span class="${res.color}">${res.label}</span>` : ''
    },
    // 添加合同
    handleCreate() {
      this.$refs.create.handleOpen(this.approvalsOptions, this.userList)
    },
    // 查看详情
    handleDetail(row) {
      this.contractStatus = row.status
      this.seller = row.seller
      const contractId = row.id
      contractDetail({ contractId }).then(res => {
        if (res.code === 200) {
          this.info = res.data
          if (res.data.file && res.data.file != 'default.jpg') {
            this.open = true
            this.resizeFun()
          } else this.$message.info('暂无合同附件')
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 调整合同图片大小
    resizeFun() {
      const devicePixelRatio = window.devicePixelRatio
      if (devicePixelRatio !== 1) {
        if (!!window.ActiveXObject || 'ActiveXObject' in window) {
        } else this.zoom = 1 / devicePixelRatio
      }
    },
    // 格式化附件
    fileFormat(file) {
      return file ? file.split(',') : []
    },
    // 查看产品明细
    handleProductDetail(row) {
      contractPriceChange({ contractId: row.id }).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          if (data) this.$refs.contractProduct.handleView(data)
          else this.$message.info('暂无产品明细')
        } else this.$message.error(msg)
      })
    },
    // 新建销售订单
    handleSaleOrderCreate(row) {
      this.saleOrderCreate = true
      this.$nextTick(() => {
        if (this.$refs.saleOrderCreate) this.$refs.saleOrderCreate.handleCreate(row)
      })
    },
    // 回调
    handleCallBack(refresh = false) {
      this.saleOrderCreate = false
      this.showDetail = false
      if (this.contractId) this.handleRowRefresh()
      else if (refresh) this.refreshList()
    },
    // 前往Bom
    goBom() {
      this.$router.push({
        path: '/private/productBom'
      })
    },
    // 弹框显示产品列表
    async handleDialogList() {
      await this.getList()
      this.listOpen = true
    },
    // 产品标记
    handleLabel(row, showCheckbox = false) {
      const loading = this.$loading({
        lock: true,
        text: '加载中…',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      this.purchasingProducts = []
      this.showCheckbox = showCheckbox
      if (showCheckbox) {
        this.purchasingProducts = []
        this.listOpen = false
      }
      this.labelInfo = { ...row }
      this.contractId = row.id
      contractPriceChange({ contractId: row.id }).then(async res => {
        if (res.data && res.data.length) {
          let arr = []
          const products = res.data
          await Promise.all(
            products.map(async item => {
              await getPrivateduct(item.productId).then(async ress => {
                await inventoryList({ number: ress.data.materialCode, pageNum: 1, pageSize: 1 }).then(stock => {
                  if (stock.rows[0] && stock.rows[0].stocks) {
                    ress.data.jdStock = stock.rows[0].stocks ? stock.rows[0].stocks.reduce((total, item) => total + item.qty, 0) : 0
                  } else ress.data.jdStock = 0
                  ress.data.stock = stock.rows[0] ? ress.data.jdStock - stock.rows[0].useQty : 0
                })
                await getBomDetailByCode({ productCode: ress.data.productCode }).then(async child => {
                  if (child.code === 200 && child.data) {
                    const markBox = await getBomDetail({ bomId: child.data.id })
                    if (markBox.code === 200 && markBox.data) {
                      const doType = markBox.data.mark || this.doOptions[0].value
                      const hasChildren = doType !== this.doOptions[0].value
                      arr.push({ ...ress.data, children: [], productId: child.data.productId, hasChildren, doType, originAmount: item.originAmount, source: 'common', quantity: item.sjNum })
                    } else {
                      const hasChildren = false
                      arr.push({ ...ress.data, children: [], productId: child.data.productId, hasChildren, doType: this.doOptions[0].value, originAmount: item.originAmount, source: 'common', quantity: item.sjNum })
                    }
                  } else {
                    const hasChildren = false
                    arr.push({ ...ress.data, children: [], hasChildren, doType: this.doOptions[0].value, originAmount: item.originAmount, source: 'common', quantity: item.sjNum })
                  }
                })
              })
            })
          )
          this.labelList = arr
          this.key = Math.random()
          this.labelOpen = true
          loading.close()
        }
      })
    },
    // 切换操作
    handleCommand(e, row) {
      row.doType = e
      if (row.doType === 'sc' || row.doType === 'ww') {
        getBomDetailByCode({ productCode: row.productCode }).then(async child => {
          if (child.code === 200) {
            if (child.data && child.data.child && child.data.child.length) {
              const result = child.data.child.map(itt => ({ ...itt, ...itt.product, orgIds: this.orgIdsOptions.map(ittt => ittt.value), doType: this.doOptions[0].value, source: 'private' }))
              await Promise.all(
                result.map(async ite => {
                  if (ite.materialCode) {
                    const stock = await inventoryList({ number: ite.materialCode, pageNum: 1, pageSize: 1 })
                    if (stock.rows[0] && stock.rows[0].stocks) {
                      ite.jdStock = stock.rows[0].stocks ? stock.rows[0].stocks.reduce((total, item) => total + item.qty, 0) : 0
                    } else ite.jdStock = 0
                    ite.stock = stock.rows[0] ? ite.jdStock - stock.rows[0].useQty : 0
                  }
                })
              )
              row.bomId = child.data.id
              row.hasChildren = !!child.data.child.length
              this.$set(row, 'children', result)
              this.markProduct(row)
            } else {
              row.doType = 'cg'
              row.hasChildren = false
              this.$set(row, 'children', [])
              this.bomProduct = row
              this.errOpen = true
              this.markProduct(row)
            }
          }
        })
      } else {
        row.hasChildren = false
        this.$set(row, 'children', [])
        this.markProduct(row)
      }
    },
    // 产品标记记录
    markProduct(item) {
      markProductBom([{ productId: item.productId, mark: item.doType }]).then(res => {})
    },
    // 刷新库存
    handleRefreshStock(row) {
      const { orgIds, materialCode } = row
      if (materialCode) {
        inventoryQty({ number: materialCode, force: true }).then(res => {
          if (res.code === 200) {
            row.stock = 0
            res.data.forEach(el => {
              row.stock = row.stock + el.qty
            })
          }
        })
      }
    },
    // 展开行
    expandChange(row) {
      if (row.doType === 'sc' || row.doType === 'ww') {
        this.loadings = true
        getBomDetailByCode({ productCode: row.productCode }).then(async child => {
          if (child.code === 200) {
            if (child.data && child.data.child && child.data.child.length) {
              const result = child.data.child.map(itt => ({ ...itt, ...itt.product, orgIds: this.orgIdsOptions.map(ittt => ittt.value), originAmount: Number(row.originAmount) * Number(itt.quantity), doType: this.doOptions[0].value, source: 'private' }))
              await Promise.all(
                result.map(async ite => {
                  if (ite.materialCode) {
                    const stock = await inventoryList({ pageSize: 1, pageNum: 1, number: ite.materialCode })
                    if (stock.rows[0] && stock.rows[0].stocks) {
                      ite.jdStock = stock.rows[0].stocks ? stock.rows[0].stocks.reduce((total, item) => total + item.qty, 0) : 0
                    } else ite.jdStock = 0
                    ite.stock = stock.rows[0] ? ite.jdStock - stock.rows[0].useQty : 0
                  }
                  const bomBox = await getBomDetailByCode({ productCode: ite.productCode })
                  if (bomBox.code === 200 && bomBox.data) {
                    const markBox = await getBomDetail({ bomId: bomBox.data.id })
                    if (markBox.code === 200 && markBox.data) {
                      ite.doType = markBox.data.mark || this.doOptions[0].value
                      ite.hasChildren = ite.doType !== this.doOptions[0].value
                    } else {
                      ite.doType = this.doOptions[0].value
                      ite.hasChildren = false
                    }
                  } else {
                    ite.doType = this.doOptions[0].value
                    ite.hasChildren = false
                  }
                })
              )
              row.bomId = child.data.id
              this.$set(row, 'children', result)
              this.loadings = false
            }
          }
        })
      }
    },
    // 回显操作类型
    formatDo(type) {
      const res = this.doOptions.find(item => item.value === type)
      return res ? res.label : '采购产品'
    },
    getRowKey(row) {
      return (row.source ? row.source : 'child') + row.id
    },
    // 表格行样式 当当前行没有子物料时，添加样式，使其展开按钮不可点击
    getRowClass(row, rowIndex) {
      if (!row.row.hasChildren) return 'row-expand-cover'
      else return ''
    },
    handleProductView(row) {
      this.$refs.productInfos.handleView(row)
    },
    handleImgView(row) {
      this.$refs.productInfos.handleImgView(row)
    },
    // 全选
    handleSelectProductAll(selection) {
      if (selection.length) {
        if (this.purchasingProducts.length) {
          selection.map(ite => {
            const idx = this.purchasingProducts.findIndex(item => item.id === ite.id)
            if (idx === -1) this.purchasingProducts.push(ite)
          })
        } else {
          this.purchasingProducts = [...selection]
        }
      } else {
        this.addList.map(ite => {
          const idx = this.purchasingProducts.findIndex(item => item.id === ite.id)
          if (idx !== -1) this.purchasingProducts.splice(idx, 1)
        })
      }
    },
    // 选择产品
    handleSelectProduct(selection, row) {
      const idx = this.purchasingProducts.findIndex(item => item.id === row.id)
      if (idx === -1) {
        this.purchasingProducts.push(row)
      } else {
        this.purchasingProducts.splice(idx, 1)
      }
    },
    // 点击采购
    handleChooseProduct(row, echo = true) {
      const idx = this.purchasingProducts.findIndex(item => item.id === row.id)
      if (idx !== -1) return
      this.purchasingProducts.push(row)
      if (echo) this.echo()
    },
    // 取消采购
    handleCancelProduct(row, echo = true) {
      const idx = this.purchasingProducts.findIndex(item => item.id === row.id)
      if (idx !== -1) this.purchasingProducts.splice(idx, 1)
      if (echo) this.echo()
    },
    // 判断是否可选择
    selectable(row, index) {
      return !row.hasChildren
    },
    // 回显选中
    echo() {
      this.$nextTick(() => {
        if (this.$refs.addTable) this.$refs.addTable.clearSelection()
        this.labelList.map(item => {
          this.purchasingProducts.map(itt => {
            if (itt.id === item.id) this.$refs.addTable.toggleRowSelection(item, true)
          })
        })
      })
    },
    // 下一步
    handleNext() {
      this.$emit('next', this.purchasingProducts)
      this.labelOpen = false
    },
    // 判断是否有选中为生产产品
    hasProduction() {
      let num = 0
      this.labelList.map(item => {
        const child = item.children.filter(itt => itt.doType == 'sc' || itt.doType == 'ww')
        if (child.length) num += 1
        if (item.doType == 'sc' || item.doType == 'ww') num += 1
      })
      return !!num
    },
    // 创建生产任务单
    handleCreateProduction() {
      this.deliveryTimeOpen = true
    },
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          let arr
          arr = this.handleArr(this.labelList)
          arr.forEach(el => {
            el.deliveryTime = this.parseTime(this.form.deliveryTime, '{y}-{m}-{d} 23:59:59')
          })
          Promise.all(
            arr.map(async item => {
              await addProduction(item)
            })
          )
          this.$message.success('创建生产任务单成功')
          this.labelOpen = false
          this.refreshList()
          this.deliveryTimeOpen = false
        } else {
          return false
        }
      })
    },
    // 递归数组
    handleArr(data) {
      let that = this
      // 初始化结果数组
      let result = []
      // 定义递归函数
      function recurse(items) {
        for (let item of items) {
          result.push({ bomId: item.bomId, objId: that.contractId, type: 0, productId: item.id, productName: item.productName, quantity: Number(item.quantity), remark: item.remark, unit: item.unit, mark: item.doType })

          // 如果对象有 children 属性且是数组，则递归调用
          if (Array.isArray(item.children)) {
            recurse(item.children)
          }
        }
      }
      // 循环
      recurse(data)

      // 返回最终结果
      return result
    },
    // 查看金蝶销售订单详情
    handleSaleOrderDetail(row, contractId = undefined) {
      if (!row.saleOrderNum) return
      this.contractId = contractId
      this.showDetail = true
      const data = { BillNo: row.saleOrderNum }
      this.$nextTick(() => {
        this.$refs.saleOrderDetail.getInfo(data)
      })
    },
    // 无效合同
    // prettier-ignore
    handleInvalidContract(row) {
      this.$confirm('是否要将该合同设置为无效合同?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 调用接口设置合同为无效
        contractDelete({ contractId: row.id }).then(res => {
          this.$message.success('操作成功!');
          this.refreshList();
        })
      }).catch(() => { })
    },
    // 下派生产任务单
    handleDispatchOrder(row, type, id) {
      this.dispatchOrder = true
      this.contractId = id
      const list = row.production.filter(item => item.mark === type)
      if (!list.length) return
      this.$nextTick(() => {
        this.$refs.dispatchOrder.handleOpen(list)
      })
    },
    // 新增采购产品
    handleCreateDemand(row) {
      if (row.production && row.production.length) {
        const cgList = row.production.filter(item => item.mark === 'cg')
        if (!cgList.length) return
        this.createDemand = true
        contractPriceChange({ contractId: row.id }).then(res => {
          const products = res.data
          const scList = row.production.filter(item => item.mark === 'sc')
          // 创建产品映射表以提高查找效率
          const productMap = new Map(products.map(p => [p.productId, p]))
          const scMap = new Map(scList.map(sc => [sc.bomId, sc]))
          const endList = cgList.map(item => {
            const product = productMap.get(item.productId)
            const scItem = scMap.get(item.bomId)
            const detailId = scItem ? productMap.get(scItem.productId)?.id || product?.id : product?.id
            const usedList = scItem ? productMap.get(scItem.productId)?.usedList?.filter(u => u.productId === item.productId) || product?.usedList : product?.usedList
            const used = usedList ? parseFloat(usedList.reduce((sum, u) => sum + u.used, 0).toFixed(5)) : item.used || 0
            return { ...item, detailId, usedList, used }
          })
          this.$nextTick(() => {
            this.$refs.createDemand.handleOpen(endList, 'initiative')
          })
        })
      } else {
        contractPriceChange({ contractId: row.id }).then(async res => {
          let arr = []
          const products = res.data
          await Promise.all(
            products.map(async item => {
              const productRes = await getPrivateduct(item.productId)
              const productData = productRes.data
              // 计算使用量
              const used = item.usedList ? parseFloat(item.usedList.reduce((sum, ite) => sum + ite.used, 0).toFixed(5)) : item.used || 0
              // 构建基础对象
              const baseObj = { ...productData, product: productData, children: [], originAmount: item.originAmount, source: 'common', quantity: item.sjNum, detailId: item.id, used }
              try {
                const bomRes = await getBomDetailByCode({ productCode: productData.productCode })
                if (bomRes.code === 200 && bomRes.data) {
                  // 有BOM数据，获取标记信息
                  const markRes = await getBomDetail({ bomId: bomRes.data.id })
                  const doType = markRes.code === 200 && markRes.data ? markRes.data.mark || this.doOptions[0].value : this.doOptions[0].value
                  arr.push({ ...baseObj, productId: bomRes.data.productId, hasChildren: doType !== this.doOptions[0].value, doType })
                } else {
                  // 无BOM数据
                  arr.push({ ...baseObj, productId: productData.id, hasChildren: false, doType: this.doOptions[0].value })
                }
              } catch (error) {
                console.error('处理BOM数据失败:', error)
                // 异常情况下的默认处理
                arr.push({ ...baseObj, productId: productData.id, hasChildren: false, doType: this.doOptions[0].value })
              }
            })
          )
          this.createDemand = true
          this.$nextTick(() => {
            this.$refs.createDemand.handleOpen(arr, 'initiative')
          })
        })
      }
    },
    handleDispatchCallBack(refresh = false) {
      this.dispatchOrder = false
      if (refresh) this.handleRowRefresh()
    },
    // 去发货
    handleSend(row) {
      this.sendProduct = true
      this.$nextTick(() => {
        if (this.$refs.sendProduct) this.$refs.sendProduct.handleOpen(row)
      })
    },
    sendProductBack(flag = false) {
      this.sendProduct = false
      if (flag) this.getList()
    },
    // 查询部门、查询用户构造树
    async getApprovalsOptions() {
      const dept = await deptTreeSelect()
      const user = await listUser()
      this.userList = user.rows
      const children = dept.data[0].children || []
      const deptData = [...children, ...[{ id: -1, label: '其他', value: -1 }]] || []
      const userData = user.rows || []
      const getChildren = data => {
        data.forEach(item => {
          item.value = item.id
          if (item.children) {
            getChildren(item.children)
          } else {
            item.children = []
          }
        })
      }
      getChildren(deptData)
      const addChildren = data => {
        data.forEach(item => {
          userData.forEach(user => {
            if (item.id === user.deptId && item.children) {
              item.children.push({
                id: user.userId,
                label: user.realName || user.nickName,
                value: user.userId,
                disabled: user.status == '1',
                userName: user.userName
              })
            }
            if (item.id === -1 && (!user.deptId || user.deptId === dept.data[0].id) && item.children) {
              item.children.push({
                id: user.userId,
                label: user.realName || user.nickName,
                value: user.userId,
                disabled: user.status == '1',
                userName: user.userName
              })
            }
          })
          if (item.children && item.children.length) {
            addChildren(item.children)
          }
        })
      }
      addChildren(deptData)
      this.approvalsOptions = deptData
    },
    // 参与设置
    handleSetting() {
      if (this.participantSetting) return (this.participantSetting = false)
      const params = { type: 'manualInputContract' }
      contractParticipantList(params).then(res => {
        const { code, msg, data } = res
        if (code !== 200) return this.$message.error(msg)
        this.participantList = data?.map(item => item.userId) || []
        this.participantSetting = true
      })
    },
    handleSettingChange(e) {
      const data = {
        type: 'manualInputContract',
        participants: []
      }
      e.map(item => {
        const info = this.userList.find(user => user.userId === item)
        if (info) {
          data.participants.push({
            deptId: info?.deptId || '',
            deptName: info?.dept?.deptName || '',
            userId: info?.userId || '',
            userName: info?.userName || ''
          })
        }
      })
      contractParticipant(data).then(res => {
        const { code, msg } = res
        if (code !== 200) return this.$message.error(msg)
        this.$message.success('操作成功!')
      })
    },
    handleClickOutside(event) {
      if (!this.$refs.participantSetting) return
      const participantSettingElement = this.$refs.participantSetting
      if (participantSettingElement && !participantSettingElement.contains(event.target)) {
        this.participantSetting = false
      }
    },
    // 获取所有操作按钮配置
    getAllActions(row) {
      return [
        {
          key: 'saleOrderCreate',
          label: '新建销售订单',
          className: 'table-btn primary',
          show: this.hasmaterialCode && this.companyId == 14 && !row.saleOrderStatus && row.status != -1,
          handler: this.handleSaleOrderCreate
        },
        {
          key: 'detail',
          label: '查看详情',
          className: 'table-btn',
          show: true,
          handler: this.handleDetail
        },
        {
          key: 'label',
          label: '产品标记',
          className: 'table-btn primary',
          show: this.hasmaterialCode && row.saleOrderStatus,
          handler: this.handleLabel
        },
        {
          key: 'invalid',
          label: '无效合同',
          className: 'table-btn danger',
          show: row.status != -1,
          handler: this.handleInvalidContract
        },
        {
          key: 'pushDown',
          label: '下推',
          className: 'table-btn primary',
          show: this.companyId == 14 && this.hasmaterialCode && row.saleOrderStatus && row.status != -1 && this.checkBillTypeId(row),
          handler: this.handlePushDown
        },
        {
          key: 'send',
          label: '去发货',
          className: 'table-btn primary',
          show: this.companyId == 14 ? this.hasmaterialCode && this.companyId == 14 && row.saleOrderStatus && row.status != -1 && this.checkSaleOrderStatus(row) && !this.checkBillTypeId(row) : row.status != -1,
          handler: this.companyId == 14 ? this.handlePushDown : this.handleSend
        }
      ]
    },
    // 获取常用按钮
    getCommonActions(row) {
      const allActions = this.getAllActions(row).filter(action => action.show)
      const commonActions = allActions.filter(action => this.isCommonAction(action.key))
      // 如果没有设置常用按钮，默认显示查看详情
      if (commonActions.length === 0) {
        const detailAction = allActions.find(action => action.key === 'detail')
        return detailAction ? [detailAction] : []
      }
      // 常用按钮不限制数量
      return commonActions
    },
    // 判断是否为常用按钮
    isCommonAction(actionKey) {
      return this.commonActions.includes(actionKey)
    },
    // 判断是否有更多操作按钮
    hasMoreActions(row) {
      const allActions = this.getAllActions(row).filter(action => action.show)
      const commonActions = this.getCommonActions(row)
      // 如果显示的按钮数量大于常用按钮数量，则显示更多操作
      return allActions.length >= commonActions.length
    },
    // 切换常用按钮设置
    toggleCommonAction(actionKey) {
      const index = this.commonActions.indexOf(actionKey)
      if (index > -1) {
        this.commonActions.splice(index, 1)
      } else {
        // 允许设置多个常用按钮
        this.commonActions.push(actionKey)
      }
      localStorage.setItem(this.userId + '.initiativeContractCommonActions', JSON.stringify(this.commonActions))
    },
    // 计算操作列宽度
    getActionColumnWidth() {
      // 如果列表为空，返回默认宽度
      if (!this.list || this.list.length === 0) {
        return 220
      }
      // 取第一行数据来计算宽度
      const firstRow = this.list[0]
      const commonActions = this.getCommonActions(firstRow)
      const hasMoreActions = this.hasMoreActions(firstRow)
      // 常用按钮数量 + 更多操作按钮(如果有的话) * 110px
      const buttonCount = commonActions.length + (hasMoreActions ? 1 : 0)
      const width = buttonCount * 110
      // 设置最小宽度为 110px，最大宽度为 550px
      return Math.max(110, Math.min(550, width))
    },
    // 检查销售订单状态是否允许发货
    checkSaleOrderStatus(row) {
      // 如果没有销售订单状态，则允许发货
      if (!row.saleOrderStatus || !Array.isArray(row.saleOrderStatus) || row.saleOrderStatus.length === 0) {
        return true
      }
      // 检查所有销售订单状态，只有当所有状态都为'C'(已审核)时才允许发货
      return row.saleOrderStatus.every(item => {
        const documentStatus = item.result?.result?.DocumentStatus
        return documentStatus === 'C'
      })
    },
    // 检查是否需要显示下推按钮（BillTypeId.Number=XSDD04_SYS）
    checkBillTypeId(row) {
      if (!row.saleOrderStatus || !Array.isArray(row.saleOrderStatus) || row.saleOrderStatus.length === 0) {
        return false
      }
      // 检查是否存在BillTypeId.Number=XSDD04_SYS的元素
      return row.saleOrderStatus.some(item => {
        const billTypeId = item.result?.result?.BillTypeId?.Number
        return billTypeId === 'XSDD04_SYS'
      })
    },
    // 处理下推操作
    handlePushDown(row) {
      if (!row.saleOrderNum) return
      this.showDetail = true
      const data = row?.saleOrderStatus?.[0]?.result?.result || {}
      this.$nextTick(() => {
        this.$refs.saleOrderDetail.handleSendPush(data, row, true)
      })
    },
    handleSetBom() {
      const product = {
        productCode: this.bomProduct.productCode,
        productName: this.bomProduct.productName,
        id: this.bomProduct.id
      }
      this.errOpen = false
      this.bomCreate = true
      this.$nextTick(() => {
        this.$refs.bomCreate.handleAdd(product)
      })
    },
    // 刷新销售订单状态
    async handleRefreshSaleOrderStatus(row) {
      if (row.saleOrderNum) {
        try {
          // 设置刷新状态，保持原有销售订单状态数据
          this.$set(row, 'refreshingSaleOrder', true)
          const saleOrderStatus = await getContractDetail({ contractId: row.id })
          const data = saleOrderStatus?.data
          if (Object.keys(data).length === 0) {
            this.$set(row, 'saleOrderStatus', undefined)
          } else {
            const saleOrderStatus = Object.keys(data).map(key => ({ saleOrderNum: key, ...data[key] }))
            this.$set(row, 'saleOrderStatus', saleOrderStatus)
          }
        } catch (error) {
          console.error('刷新销售订单状态失败:', error)
          this.$message.error('刷新失败，请稍后重试')
        } finally {
          // 移除刷新状态
          this.$set(row, 'refreshingSaleOrder', false)
        }
      }
    }
  }
}
</script>
<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';

.Box {
  padding: 15px 20px;
}

.custom-dialog ::v-deep {
  .print {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 0 20px;

    &-item {
      padding: 0 50px;
      height: 50px;
      line-height: 50px;
      text-align: center;
      margin-left: 10px;
      border: 1px solid #cbd6e2;
      border-radius: 5px;
      font-size: 16px;
      color: $white;
      cursor: pointer;
      background-color: $blue;

      &:hover {
        opacity: 0.8;
      }
    }
  }

  .labelTitle {
    font-size: 14px;
    padding-bottom: 12px;
    color: #999999;
  }

  .labelInfo {
    border-top: 1px solid #e2e6f3;
    border-bottom: 1px solid #e2e6f3;
    margin-bottom: 12px;
    padding: 10px 0;

    .el-col {
      line-height: 20px;
      padding-top: 6px;
      padding-bottom: 6px;

      span {
        display: inline-block;
        color: #666666;
        font-size: 12px;
        width: 4em;
        margin-right: 20px;
      }

      b {
        font-weight: normal;
        font-size: 14px;
        color: #333333;

        &.price {
          color: #f43f3f;
        }
      }
    }
  }

  .table-child {
    margin: -5px 0;
    display: flex;
    align-items: center;
    background-color: #f0f4f9;

    &-title {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      width: 80px;
    }

    .custom-table {
      flex: 1;
      overflow: hidden;

      .el-table__header-wrapper {
        .el-checkbox__inner {
          display: none !important;
        }
      }
    }
  }

  .table-stock {
    display: flex;
    align-items: center;
  }

  .row-expand-cover td .el-table__expand-icon {
    visibility: hidden;
  }

  .dropdown-primary {
    color: #2e73f3;
  }

  .dropdown-orange {
    color: #f35d09;
  }

  .listSearch {
    width: 650px;
    height: 46px;
    margin: 30px auto;
    border: 1px solid #cbd7e2;
    border-radius: 5px;
    padding: 3px;
    display: flex;
    align-items: center;

    .el-input__inner {
      border-width: 0;
    }
  }

  .craftBox {
    background: #f8f9fb;
    border-radius: 5px;
    border: 1px solid #cbd6e2;

    .craftBox_list {
      border-bottom: 1px solid #cbd6e2;

      &:last-child {
        border-bottom: none;
      }

      .craftBox_list_title {
        width: 110px;
        height: 30px;
        background: #bec6d0;
        border-bottom-right-radius: 10px;
        padding-left: 16px;
        line-height: 30px;
        font-weight: 500;
        font-size: 14px;
        color: #ffffff;
        margin-bottom: 15px;
      }

      .craftBox_list_concent {
        padding: 0 15px;

        .concent_top {
          display: flex;
          align-items: center;
          margin-bottom: 16px;
        }

        .concent_bottom {
          margin-bottom: 10px;
        }

        .concent_item {
          display: flex;
          align-items: center;
          margin-right: 60px;

          .title {
            font-weight: 400;
            font-size: 12px;
            color: #999999;
            line-height: 20px;
            margin-right: 20px;
          }

          .text {
            font-weight: 500;
            font-size: 14px;
            color: #333333;
            line-height: 20px;
          }

          .fee {
            font-weight: 500;
            font-size: 14px;
            color: #f35d09;
            line-height: 20px;
          }

          .device {
            display: flex;
            align-items: center;

            .text {
              display: block;
              background: #e1e4e8;
              border-radius: 5px;
              font-weight: 500;
              font-size: 14px;
              color: #333333;
              line-height: 20px;
              padding: 5px 10px;
              margin-right: 10px;
            }
          }
        }
      }
    }
  }
}

.errTips {
  padding: 10px 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  .img {
    width: 36px;
    height: 36px;
    margin-bottom: 17px;
  }

  .title {
    font-weight: 500;
    font-size: 14px;
    color: #ed4040;
    line-height: 20px;
    margin-bottom: 10px;
  }

  .text {
    font-weight: 400;
    font-size: 12px;
    color: #666666;
    line-height: 20px;
    margin-bottom: 47px;
  }

  .btn {
    display: flex;
    align-items: center;
    justify-content: center;

    .cancel {
      width: 269px;
      height: 50px;
      border-radius: 5px 5px 5px 5px;
      border: 1px solid #cbd6e2;
      font-weight: 400;
      font-size: 16px;
      color: #999999;
      line-height: 50px;
      text-align: center;
      margin-right: 10px;
      cursor: pointer;
    }

    .go {
      width: 269px;
      height: 50px;
      background: #2e73f3;
      border-radius: 5px 5px 5px 5px;
      font-weight: 500;
      font-size: 16px;
      color: #ffffff;
      line-height: 50px;
      text-align: center;
      cursor: pointer;
    }
  }
}

.deliveryTimeBox {
  padding: 10px 20px;

  .btn {
    display: flex;
    align-items: center;
    justify-content: flex-end;

    .cancel {
      width: 200px;
      height: 50px;
      border-radius: 5px 5px 5px 5px;
      border: 1px solid #cbd6e2;
      font-weight: 400;
      font-size: 16px;
      color: #999999;
      line-height: 50px;
      text-align: center;
      margin-right: 10px;
      cursor: pointer;
    }

    .go {
      width: 200px;
      height: 50px;
      background: #2e73f3;
      border-radius: 5px 5px 5px 5px;
      font-weight: 500;
      font-size: 16px;
      color: #ffffff;
      line-height: 50px;
      text-align: center;
      cursor: pointer;
    }
  }
}

.blue {
  ::v-deep .el-icon-arrow-right {
    color: #2e73f3;
    font-size: 14px;
  }

  ::v-deep .el-table__header-wrapper {
    .el-checkbox {
      display: none !important;
    }
  }
}

.amountTo {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 48px;
  background: #ecf3ff;
  border-radius: 5px;
  border: 1px solid #2e73f3;
  padding: 0 15px;

  .amountTo_item {
    font-weight: 400;
    font-size: 14px;
    color: #666666;
    display: flex;
    align-items: center;
    justify-content: center;

    .text {
      font-weight: 500;
      font-size: 18px;
      color: #2e73f3;
    }
  }
}

.participantSetting {
  min-width: 316px;
  // height: 428px;
  position: absolute;
  top: 36px;
  left: 0;
  background: #f8f9fb;
  border-radius: 5px;
  border: 1px solid #2e73f3;
  box-shadow: 0px 1px 15px 0px rgba(0, 0, 0, 0.25);
  z-index: 1000;
  display: flex;
  flex-direction: column;

  b {
    font-size: 14px;
    font-weight: 400;
    color: #333333;
    line-height: 20px;
    margin-top: 10px;
    margin-left: 20px;
  }

  span {
    font-size: 12px;
    color: #f35e23;
    line-height: 20px;
    margin-left: 20px;
    margin-bottom: 10px;
  }

  ::v-deep .el-cascader-menu__wrap {
    height: 300px !important;
    margin-bottom: 0 !important;
  }
}

.custom-tabs {
  ::v-deep .el-tabs__header {
    margin-bottom: 0 !important;
  }

  ::v-deep .el-tabs__nav-scroll {
    padding-left: 20px;
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
.justify-center {
  justify-content: center;
}
.flex.align-center {
  display: flex;
  align-items: center;
  line-height: 1;
}
.flex.align-center .table-link {
  line-height: 1;
  vertical-align: middle;
}
</style>
