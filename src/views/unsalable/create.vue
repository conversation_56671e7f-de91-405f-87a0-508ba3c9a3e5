<template>
  <el-dialog v-dialogDragBox :title="title" :visible.sync="open" width="1150px" class="custom-dialog">
    <div style="padding: 0 20px">
      <el-form ref="form" :model="form" :rules="rules" label-width="7em">
        <el-row :gutter="20" style="display: flex; flex-wrap: wrap">
          <el-col :span="12">
            <el-form-item label="产品名称" prop="productName">
              <el-input v-model="form.productName" placeholder="请输入产品名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="产品编码" prop="productCode">
              <el-input v-model="form.productCode" placeholder="请输入产品编码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="产品单位" prop="unit">
              <el-select v-model="form.unit" placeholder="请选择产品单位" style="width: 100%">
                <el-option v-for="(item, index) in options" :key="index" :label="item" :value="item"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="选择分类" prop="categoryId">
              <el-select v-model="form.categoryId" placeholder="请选择产品分类" style="width: 100%">
                <el-option v-for="(item, index) in categoryList" :key="index" :label="item.name" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!--          <el-col :span="12">-->
          <!--            <el-form-item label="产品属性" prop="attribute">-->
          <!--              <el-input v-model="form.attribute" placeholder="请输入产品属性" />-->
          <!--            </el-form-item>-->
          <!--          </el-col>-->
          <el-col :span="12">
            <el-form-item label="产品重量" prop="weight">
              <el-input v-model="form.weight" placeholder="请输入产品重量">
                <span slot="suffix">Kg</span>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="产品型号" prop="model">
              <el-input v-model="form.model" placeholder="请输入产品型号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="产品规格" prop="specs">
              <el-input v-model="form.specs" placeholder="请输入产品规格" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="产品材质" prop="materialQuality">
              <el-input v-model="form.materialQuality" placeholder="请输入产品材质" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="表面处理" prop="surface">
              <el-input v-model="form.surface" placeholder="请输入表面处理方式" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="产品库存" prop="stock">
              <el-input v-model="form.stock" placeholder="请输入产品库存">
                <span slot="suffix">{{ form.unit }}</span>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24"></el-col>
          <el-col :span="12">
            <el-form-item label="产品图片" prop="picture1">
              <image-upload v-model="form.picture1" :file-type="fileType" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="产品图纸" prop="draw">
              <file-upload v-model="form.draw" :file-type="fileType" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="工艺视频" prop="technology">
              <file-upload v-model="form.technology" :file-type="['mp4']" :file-size="200" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="检测报告" prop="report">
              <file-upload v-model="form.report" :file-type="fileType" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="产品价格" prop="price">
              <el-radio-group v-model="form.price" @change="handleChange" v-removeAriaHidden>
                <el-tooltip effect="dark" :content="item.remark" :disabled="!item.remark" v-for="item in priceOptions" :key="item.label">
                  <el-radio :label="item.label">{{ item.name }}</el-radio>
                </el-tooltip>
              </el-radio-group>
              <div class="price-rule">
                <el-checkbox v-model="priceRule"></el-checkbox>
                <span style="margin-left: 5px">我已阅读并同意</span>
                <el-tooltip popper-class="orange" placement="bottom">
                  <div slot="content">
                    根据自由客平台定价规则，用户在选择价格并发布产品时即视为同意以下
                    <br />
                    条款：平台保留根据市场价格波动调整产品价格的权利。一旦商品发布
                    <br />
                    后，如价格发生变动与市场价不符，平台有权下架该产品。用户可在调整
                    <br />
                    价格后再次发布。自由客平台享有对定价问题的最终解释权。用户在此承
                    <br />
                    认并同意上述规则，并同意遵守。
                  </div>
                  <el-button type="text">《自由客平台定价规则》</el-button>
                </el-tooltip>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div slot="footer">
      <button type="button" class="custom-dialog-btn" @click="handleCancel">取消</button>
      <button type="button" class="custom-dialog-btn primary" @click="handleSumit">立即发布</button>
    </div>
  </el-dialog>
</template>

<script>
import { getlistb } from '@/api/purchase/category'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import { isNumber, isNumberLength } from '@/utils/validate'
import { addUnsalable, updateUnsalable } from '@/api/unsalable'
import { getToken } from '@/utils/auth'

export default {
  name: 'unsalableCreate',
  components: { Treeselect },
  data() {
    return {
      title: '新增产品',
      options: ['吨', '千克', '个', '件', '套', '米', '支', '根'],
      fileType: ['png', 'PNG', 'jpg', 'JPG', 'jpeg', 'JPEG', 'pdf'],
      open: false,
      form: {},
      rules: {
        productName: [{ required: true, message: '请输入产品名称', trigger: 'blur' }],
        productCode: [{ required: true, message: '请输入产品编码', trigger: 'blur' }],
        unit: [{ required: true, message: '请选择产品单位', trigger: 'change' }],
        categoryId: [{ required: true, message: '请选择产品分类', trigger: ['blur', 'change'] }],
        // attribute: [{ required: true, message: '请输入产品属性', trigger: 'blur' }],
        weight: [
          { required: true, message: '请输入产品重量', trigger: 'blur' },
          { validator: isNumber, message: '输入有误请重新输入', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 10), message: '只可以填写十位小数', trigger: 'blur' }
        ],
        model: [{ required: true, message: '请输入产品型号', trigger: 'blur' }],
        materialQuality: [{ required: true, message: '请输入产品材质', trigger: 'blur' }],
        surface: [{ required: true, message: '请输入表面处理方式', trigger: 'blur' }],
        stock: [
          { required: true, message: '请输入产品库存', trigger: 'blur' },
          { validator: isNumber, message: '输入有误请重新输入', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 10), message: '只可以填写十位小数', trigger: 'blur' }
        ],
        minNum: [
          { required: true, message: '请输入最小采购量', trigger: 'blur' },
          { validator: isNumber, message: '输入有误请重新输入', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 10), message: '只可以填写十位小数', trigger: 'blur' }
        ],
        picture1: [{ required: true, message: '请添加产品图片', trigger: ['blur', 'change'] }],
        // draw: [{ required: true, message: '请添加产品图纸', trigger: ['blur', 'change'] }],
        price: [{ required: true, message: '请选择产品价格', trigger: ['blur', 'change'] }]
        // technology: [{ required: true, message: '请添加产品工艺视频', trigger: ['blur', 'change'] }],
        // report: [{ required: true, message: '请添加检测报告', trigger: ['blur', 'change'] }],
        // price: [{ required: true, message: '请选择产品价格', trigger: ['blur', 'change'] }],
        // minNum: [{ required: true, message: '请输入产品规格', trigger: ['blur', 'change'] }]
      },
      categoryList: [],
      priceOptions: [],
      priceRule: false
    }
  },
  created() {
    if (!!getToken()) {
      this.getConfigKey('unsalable:product:price').then(res => {
        const price = JSON.parse(res.msg)
        this.priceOptions = price.map(item => {
          return { optionId: Number(item.optionId), label: Number(item.value), name: `${item.value}/吨`, remark: item.remark }
        })
      })
    }
    this.getCategory()
  },
  methods: {
    getCategory() {
      const query = { pageNum: 1, pagesize: 999 }
      getlistb(query).then(res => {
        this.categoryList = res.data
      })
    },
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children
      }
      return {
        id: node.id,
        label: node.name,
        children: node.children
      }
    },
    reset() {
      this.form = {
        id: undefined,
        attribute: undefined,
        diagram: undefined,
        draw: undefined,
        formerName: undefined,
        labels: undefined,
        materialQuality: undefined,
        model: undefined,
        picture1: undefined,
        price: this.priceOptions[0].label,
        productCode: undefined,
        productName: undefined,
        minNum: undefined,
        stock: undefined,
        remark: undefined,
        report: undefined,
        specs: undefined,
        standard: undefined,
        supplierIds: undefined,
        surface: undefined,
        technology: undefined,
        unit: undefined,
        weight: undefined
      }
      this.resetForm('form')
    },
    handleAdd(data = {}) {
      this.priceRule = false
      this.reset()
      this.title = '引入滞销品'
      this.form = { ...data }
      delete this.form.categoryId
      delete this.form.id
      // this.form.unit = '吨'
      this.open = true
    },
    handleUpdate(data) {
      this.priceRule = true
      this.reset()
      this.title = '修改滞销品'
      this.form = { ...data }
      // this.form.unit = '吨'
      this.open = true
    },
    // 取消
    handleCancel() {
      this.open = false
      this.reset()
    },
    handleChange(val) {
      const data = this.priceOptions.find(item => item.label === val)
      this.form.refValId = data.optionId || ''
    },
    // 提交
    handleSumit() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (!this.priceRule) {
            this.$message.error('请先阅读并同意自由客平台定价规则')
            return
          }
          if (this.form.id != null) {
            this.form.productId = this.form.id
            updateUnsalable(this.form).then(res => {
              if (res.code === 200) {
                this.$message.success('修改成功')
                this.open = false
                this.$parent.getList()
              } else {
                this.$message.error(res.msg)
              }
            })
          } else {
            addUnsalable(this.form).then(res => {
              if (res.code === 200) {
                this.$message.success('新增成功')
                this.open = false
                this.$parent.getList()
              } else {
                this.$message.error(res.msg)
              }
            })
          }
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
</style>
<style>
.orange.el-tooltip__popper.is-dark {
  background-color: #ffebe0;
  color: #f35d09;
  border: 1px solid #f35d09;
}
.orange.el-tooltip__popper[x-placement^='top'] .popper__arrow:after,
.orange.el-tooltip__popper[x-placement^='top'] .popper__arrow {
  border-top-color: #f35d09;
}
.orange.el-tooltip__popper[x-placement^='bottom'] .popper__arrow:after,
.orange.el-tooltip__popper[x-placement^='bottom'] .popper__arrow {
  border-bottom-color: #f35d09;
}
.el-radio {
  line-height: 1.5 !important;
}
</style>
