<template>
  <div class="newBox bgcf9 vh-85">
    <div class="custom-back" v-if="isOrder">
      <div class="back-button" @click="handleClose">
        <i class="el-icon-back"></i>
        返回
      </div>
    </div>
    <!-- 搜索 -->
    <div class="custom-search flex" style="padding-top: 18px" v-if="!isOrder">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
        <el-form-item label="合同编号" prop="serial">
          <el-input v-model="queryParams.serial" placeholder="请输入合同编号" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="关键词" prop="keyword">
          <el-input v-model="queryParams.keyword" placeholder="请输入关键词" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="status" placeholder="请选择状态" clearable multiple>
            <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" style="background-color: #2e73f3; border-color: #2e73f3" size="small" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 列表 -->
    <div class="Box">
      <template v-if="total > 0">
        <el-table v-loading="loading" ref="allTable" stripe :data="list" row-key="id" :key="key" style="width: 100%" class="custom-table custom-table-cell10">
          <el-table-column align="center" type="index" label="序号" width="50"></el-table-column>
          <el-table-column align="center" prop="serial" label="合同编号" show-overflow-tooltip width="120"></el-table-column>
          <el-table-column align="center" label="供应商" show-overflow-tooltip min-width="130">
            <template slot-scope="{ row }">
              <span class="table-link" @click="handleViewSupplier(row)">{{ row.sellerName }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="产品明细">
            <template slot-scope="{ row }">
              <el-button type="text" size="small" icon="el-icon-view" @click="handleProductDetail(row)">查看</el-button>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="signingTime" label="签订时间" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="address" label="签订地点" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="amount" label="订单总金额" show-overflow-tooltip>
            <template slot-scope="{ row }">
              <span class="table-orange" style="font-size: 14px">{{ row.amount ? '￥' + row.amount : '' }}{{ row.isIncludingTax ? '(含税)' : '(不含税)' }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="status" label="状态" show-overflow-tooltip width="60">
            <template slot-scope="{ row }">
              <div v-html="statusFormat(row)"></div>
            </template>
          </el-table-column>
          <el-table-column align="center" label="操作" width="330">
            <template slot-scope="{ row }">
              <button type="button" class="table-btn" @click="handleView(row)">查看详情</button>
              <button type="button" class="table-btn danger" @click="handleDelete(row)" v-if="row.status === 1">无效</button>
              <button type="button" class="table-btn disabled" v-else>无效</button>
              <button type="button" class="table-btn primary" @click="handleRadar(row)" v-if="row.status === 2">收货并评价</button>
            </template>
          </el-table-column>
        </el-table>

        <div class="custom-pagination" v-if="!isOrder">
          <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" local="UnsalableContractPageSize" @pagination="getList" />
        </div>
      </template>
      <el-empty v-else />
    </div>

    <!-- 供应商详情 -->
    <supplier-dialog ref="supplier"></supplier-dialog>

    <!-- 合同详情 -->
    <el-dialog v-dialogDragBox title="合同详情" :visible.sync="open" width="1150px" class="custom-dialog">
      <div class="print" v-if="contractStatus !== -1">
        <div class="print-item" @click="handleSend(info)" v-if="contractStatus === 1">
          <i class="el-icon-position"></i>
          发送
        </div>
        <div class="print-item" @click="handlePrint(info)">
          <i class="el-icon-printer"></i>
          打印
        </div>
        <div class="print-item" @click="handleDownload(info)">
          <i class="el-icon-download"></i>
          下载
        </div>
      </div>
      <div style="text-align: center">
        <img style="max-width: 100%" :src="info.file" v-if="isBase64Image(info.file)" />
        <show-template :certify="!!info.certify" :signet="signet" :signature="info.signature" :content="info.file" v-else-if="isJSON(info.file)" />
      </div>
      <div slot="footer">
        <button type="button" class="custom-dialog-btn primary" @click="open = false">关 闭</button>
      </div>
    </el-dialog>

    <!-- 评价 -->
    <radar-dialog ref="radaDialog" />

    <!--  选择联系人  -->
    <el-dialog v-dialogDragBox title="发送合同" :visible.sync="userOpen" width="1150px" class="custom-dialog">
      <div style="padding: 0 20px">
        <div class="send-title">公司基本信息</div>
        <div class="send-box">
          <el-row>
            <el-col :span="12">
              <span>公司名称：</span>
              {{ userInfo.companyName }}
            </el-col>
            <el-col :span="12">
              <span>公司法人：</span>
              {{ userInfo.companyLegal }}
            </el-col>
            <el-col :span="24" class="border0">
              <span>公司地址：</span>
              {{ removeHtmlTag(userInfo.companyAddress, 300) }}
            </el-col>
          </el-row>
        </div>
        <div class="send-title">请选择发送合同接收人</div>
        <el-table v-loading="loading" ref="userTable" stripe :data="userList" row-key="id" style="width: 100%" class="custom-table custom-table-cell10" @row-click="handleUserChange">
          <el-table-column align="center" label="选择" width="55">
            <template slot-scope="{ row }">
              <el-radio v-model="userRadio.phone" :label="row.phone" v-if="!isChange"><span /></el-radio>
              <template v-else>
                <el-radio v-model="userRadio.phone" :label="row.phone" v-if="userRadio.phone === row.phone"><span /></el-radio>
              </template>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="nickName" label="联系人姓名" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="phone" label="联系人电话" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="post" label="联系人职务" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" label="站内通知">
            <template slot-scope="{ row }">
              <el-switch class="isSite" :disabled="!row.userId" v-model="row.isSite" active-text="开" inactive-text="关"></el-switch>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="nums" label="发送次数" show-overflow-tooltip>
            <template slot-scope="{ row }">{{ row.nums ? `已发送${row.nums}次` : '未发送' }}</template>
          </el-table-column>
        </el-table>
      </div>
      <div slot="footer">
        <button type="button" class="custom-dialog-btn" @click="userOpen = false">取消</button>
        <button type="button" class="custom-dialog-btn primary" :class="{ disabled: !userRadio.phone }" :disabled="!userRadio.phone" @click="handleSendSubmit">确认并发送</button>
      </div>
    </el-dialog>

    <!--合同产品明细-->
    <contract-product ref="contractProduct" />
  </div>
</template>

<script>
import { listUnsalableContract, listUnsalableOrderContract, delUnsalableContract, getUnsalableContract, sendUnsalableContract, listUnsalableContractLog } from '@/api/unsalable'
import supplierDialog from '@/views/purchase/demandForMe/supplier'
import { checkPermi } from '@/utils/permission'
import print from 'print-js'
import radarDialog from '@/views/components/radar'
import { supplier } from '@/api/system/user'
import { privateSupb } from '@/api/houtai/siyu/gongying'
import contractProduct from '@/views/components/product/contact'
import { removeHtmlTag } from '@/utils'
import ShowTemplate from '@/views/purchase/contract/showTemplate'
import { getConfigDetail2 } from '@/api/config'

// 滞销品合同发送
export default {
  name: 'Unsalablecontract',
  components: { contractProduct, supplierDialog, radarDialog, ShowTemplate },
  data() {
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        serial: undefined,
        keyword: undefined,
        status: undefined
      },
      status: [1, 2],
      statusOptions: [
        { label: '无效', value: -1, color: 'color-red' },
        { label: '正常', value: 1, color: 'color-disabled' },
        { label: '已签署', value: 2, color: 'color-success' },
        { label: '已完成', value: 3, color: 'color-blue' }
      ],
      key: 1,
      loading: true,
      list: [],
      total: 0,
      open: false,
      info: {},
      isOrder: false,
      radarOpen: false,
      radarInfo: {},
      contractSource: undefined,
      contractId: undefined,
      contractStatus: undefined,
      seller: undefined,
      userRadio: {},
      userOpen: false,
      userList: [],
      userInfo: { companyName: '', companyAddress: '', companyLegal: '' },
      isChange: false,
      signet: ''
    }
  },
  created() {
    this.getList()
  },
  methods: {
    removeHtmlTag,
    checkPermi,
    // 列表
    async getList() {
      this.loading = true
      let res
      const { orderId, seller } = this.$route.query
      if (orderId && seller) {
        this.isOrder = true
        const query = { orderId, seller }
        res = await listUnsalableOrderContract(query)
        if (res.code === 200) {
          this.list = res.data
          this.total = res.data.length
          this.key = Math.random()
          this.loading = false
        } else {
          this.$message.error(res.msg)
        }
      } else {
        this.isOrder = false
        const localPageSize = localStorage.getItem('UnsalableContractPageSize')
        if (localPageSize) this.queryParams.pageSize = parseInt(localPageSize)
        this.queryParams.status = this.status.toString()
        res = await listUnsalableContract(this.queryParams)
        if (res.code === 200) {
          this.list = res.rows
          this.key = Math.random()
          this.total = res.total
          this.loading = false
        } else {
          this.$message.error(res.msg)
        }
      }
    },
    // 状态回显
    statusFormat(row) {
      const res = this.statusOptions.find(item => item.value === row.status)
      return res ? `<span class="${res.color}">${res.label}</span>` : ''
    },
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 重置搜索
    resetQuery() {
      this.status = [1, 2]
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 查看供应商详情
    handleViewSupplier(row) {
      const id = row.seller
      this.$refs.supplier.getInfo(id, 'common')
    },
    // 评价
    handleRadar(row) {
      this.$refs.radaDialog.handleOpen(row, 'purchase')
    },
    // 查看详情
    handleView(row) {
      this.signet = ''
      this.contractSource = row.source
      this.contractId = row.id
      this.contractStatus = row.status
      this.seller = row.seller
      const contractId = row.id
      getUnsalableContract({ contractId }).then(res => {
        if (res.code === 200) {
          this.info = res.data
          if (this.isJSON(this.info.file)) {
            getConfigDetail2({ configKey: 'signatures', companyId: res.data.companyId, creator: res.data.creator }).then(res => {
              this.signet = res?.data?.configValue || ''
              this.open = true
            })
          } else this.open = true
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 无效合同
    // prettier-ignore
    handleDelete(row) {
      const data = { contractId: row.id }
      this.$modal.confirm('是否确认将选中的合同设为无效？').then(function () {
        return delUnsalableContract(data)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess('操作成功')
      }).catch(() => {
      })
    },
    // 打印
    handlePrint(data) {
      print({ printable: data.file, type: 'image', base64: true })
    },
    // 下载
    handleDownload(data) {
      const imgUrl = data.file
      const fileName = data.serial
      if (window.navigator.msSaveOrOpenBlob) {
        const bstr = atob(imgUrl.split(',')[1])
        let n = bstr.length
        const u8arr = new Uint8Array(n)
        while (n--) {
          u8arr[n] = bstr.charCodeAt(n)
        }
        const blob = new Blob([u8arr])
        window.navigator.msSaveOrOpenBlob(blob, fileName + '.' + 'png')
      } else {
        const a = document.createElement('a')
        a.href = imgUrl
        a.setAttribute('download', fileName)
        a.click()
      }
    },
    // 选择联系人
    handleUserChange(currentRow) {
      if (!this.isChange) this.userRadio = currentRow
    },
    // 发送
    handleSend(info) {
      info = info || {}
      let send = []
      listUnsalableContractLog({ contractId: this.contractId }).then(res => {
        if (res.code === 200) send = res.data
        else this.$message.error(res.msg)
      })
      this.isChange = !!info.sendPhone
      const id = this.seller
      const source = 'common'
      this.userInfo = {}
      if (source === 'common') {
        supplier({ id }).then(res => {
          if (res.code === 200) {
            this.userInfo.companyName = res.data.supplier.name
            this.userInfo.companyAddress = res.data.company ? res.data.company.address : ''
            this.userInfo.companyLegal = res.data.supplier.legal
            this.userList = res.data.contacts
            this.userOpen = true
            if (info.sendUser && info.sendPhone) this.userRadio = { nickName: info.sendUser, phone: info.sendPhone }
            else this.userRadio = res.data.contacts.find(item => item.checked) || {}
            res.data.contacts.map(item => {
              item.nums = send.filter(i => i.phone === item.phone).length
            })
          } else this.$message.error(res.msg)
        })
      } else {
        privateSupb({ id }).then(res => {
          if (res.code === 200) {
            this.userInfo.companyName = res.data.name
            this.userInfo.companyAddress = res.data.address
            this.userInfo.companyLegal = res.data.legal
            this.userList = res.data.contactList
            this.userOpen = true
            if (info.sendUser && info.sendPhone) this.userRadio = { nickName: info.sendUser, phone: info.sendPhone }
            else this.userRadio = res.data.contactList.find(item => item.checked) || {}
            res.data.contactList.map(item => {
              item.nums = send.filter(i => i.phone === item.phone).length
            })
          } else this.$message.error(res.msg)
        })
      }
    },
    // 发送提交
    handleSendSubmit() {
      const contractId = this.contractId
      const phone = this.userRadio.phone
      const uuid = this.userRadio.uuid
      const reg = /^1[3456789]\d{9}$/
      if (!reg.test(phone)) {
        this.$message.error('请输入正确的手机号')
        return
      }
      let data = { contractId, phone, uuid, userId: undefined, info: undefined, image: undefined }
      if (this.userRadio.isSite) data.userId = this.userRadio.userId
      data.info = `买方：${this.info.buyerName}给您发送了一份合同，需您查看并确认签署。`
      sendUnsalableContract({ contractId, phone }).then(res => {
        if (res.code === 200) {
          this.$message.success('发送成功')
          this.userOpen = false
          this.userRadio = {}
        } else this.$message.error(res.msg)
      })
    },
    // 返回按钮
    handleClose() {
      const obj = { path: '/demand/unsalableorder' }
      this.$tab.closeOpenPage(obj)
    },
    // 查看产品明细
    handleProductDetail(row) {
      const { products } = row
      if (products && products.length) {
        const list = JSON.parse(products)
        list.map(item => {
          item.sjNum = item.quantity
        })
        this.$refs.contractProduct.handleView(list, 'unsalable')
      } else this.$message.info('暂无产品明细')
    },
    // 判断是不是json
    isJSON(str) {
      try {
        JSON.parse(str)
        return true
      } catch (e) {
        return false
      }
    },
    // 判断是不是base64
    isBase64Image(str) {
      if (typeof str === 'string') {
        return str.startsWith('data:image/') && str.includes(';base64,')
      }
      return false
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
.Box {
  padding: 15px 20px;
}
.custom-dialog ::v-deep {
  .print {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 0 20px;
    &-item {
      padding: 0 50px;
      height: 50px;
      line-height: 50px;
      text-align: center;
      margin-left: 10px;
      border: 1px solid #cbd6e2;
      border-radius: 5px;
      font-size: 16px;
      color: $white;
      cursor: pointer;
      background-color: $blue;
      &:hover {
        opacity: 0.8;
      }
    }
  }
}
.custom-back {
  padding: 7px 20px;
  background-color: $white;
  box-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.06);
  .back-button {
    font-size: 16px;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    padding: 10px 0;
    color: $font;
    &:hover {
      color: $blue;
    }
  }
}
::v-deep {
  .formBox {
    padding: 0 20px;
    .el-form-item__label {
      font-weight: normal;
      color: #333333;
    }
    .el-textarea__inner {
      font-family: inherit;
    }
  }
  .radarBox {
    padding: 0 20px;
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 10px;
    &-item {
      width: 33.33%;
      display: inline-flex;
      flex-direction: row;
      font-size: 14px;
      color: #333333;
      margin: 7px 0;
      .el-rate {
        margin-left: 10px;
      }
    }
  }
  .send {
    &-title {
      font-size: 14px;
      margin-bottom: 10px;
      color: #666666;
    }
    &-box {
      padding: 0 30px;
      border: 1px solid #cbd6e2;
      border-radius: 5px;
      margin-bottom: 10px;
      background-color: #fafafb;
      .el-row {
        .el-col {
          border-bottom: 1px solid #cbd6e2;
          font-size: 14px;
          line-height: 55px;
          color: #333333;
          span {
            color: #666666;
          }
          &.border0 {
            border-width: 0;
          }
        }
      }
    }
  }
}
::v-deep .isSite {
  position: relative;
  .el-switch__core {
    height: 24px;
    border-radius: 12px;
    min-width: 50px;
    &:after {
      left: 4px;
      top: 3px;
    }
  }
  &.el-switch {
    &.is-checked {
      .el-switch__core {
        &:after {
          margin-left: -20px;
          left: 100%;
        }
      }
    }
  }
  &.is-checked {
    .el-switch__label--left {
      opacity: 0;
    }
    .el-switch__label--right {
      opacity: 1;
    }
  }
  .el-switch__label {
    position: absolute;
    top: 0;
  }
  .el-switch__label--left {
    right: 0;
    color: #999999;
    z-index: 1;
    margin-right: 8px;
  }
  .el-switch__label--right {
    left: 0;
    color: #ffffff;
    opacity: 0;
    margin-left: 8px;
  }
}
</style>
