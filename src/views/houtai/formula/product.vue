<template>
  <el-dialog v-dialogDragBox :visible.sync="internalVisible" width="1000px">
    <div class="pinfobox" style="height: auto;" v-if="info">
      <div class="pinfobox1">
        <div @click="pinfoindex = 1" :class="pinfoindex == 1 ? 'pinfobox1sel' : ''">产品详情</div>
        <div @click="drawPreView(info.draw);pinfoindex = 2" :class="pinfoindex == 2 ? 'pinfobox1sel' : ''">产品图纸</div>
      </div>
      <div class="pinfobox2" v-if="pinfoindex == 1">
        <div>
          <img style="width:305px;height:305px;" :src="formatProductImg(info)">
        </div>
        <div class="pinfobox2-2">
          <div class="pinfobox2-2-1" style=" border-top:1px solid #CBD6E2;">
            <div>产品名称</div>
            <div>{{ info.productName || info.product_name }}</div>
          </div>
          <div class="pinfobox2-2-1">
            <div>材料质量</div>
            <div>{{ info.materialQuality || info.material_quality }}</div>
          </div>
          <div class="pinfobox2-2-1">
            <div>产品模型</div>
            <div>{{ info.model }}</div>
          </div>
          <div class="pinfobox2-2-1">
            <div>产品规格</div>
            <div>{{ info.specs }}</div>
          </div>
          <div class="pinfobox2-2-1">
            <div>表面处理</div>
            <div>{{ info.surface }}</div>
          </div>
          <div class="pinfobox2-2-1">
            <div>产品重量</div>
            <div>{{ info.weight }}</div>
          </div>
          <div class="pinfobox2-2-1">
            <div>产品单位</div>
            <div>{{ info.unit }}</div>
          </div>
        </div>
      </div>
      <!-- 图纸 -->
      <div class="pinfobox2" style="height: auto;" v-if="pinfoindex == 2">
        <!-- <iframe :src="pdfUrl + '?page=hsn#toolbar=0'" style="width:100%;height: 500px" frameborder="0" id="ifm"></iframe> -->
        <template v-if="pdfUrl">
          <template v-if="pdfUrl.split('.').includes('pdf')">
            <div style="width: 100%;display: flex; justify-content: space-between; align-items: center; padding-bottom: 10px" v-if="pdfCount > 1">
              <el-button type="primary" size="small" @click="pdfCurrent--" :disabled="pdfCurrent < 2">
                <i class="el-icon-arrow-left"></i>
                上一页
              </el-button>
              <span>{{ pdfCurrent }} / {{ pdfCount }}</span>
              <el-button type="primary" size="small" @click="pdfCurrent++" :disabled="pdfCurrent >= pdfCount">
                下一页
                <i class="el-icon-arrow-right"></i>
              </el-button>
            </div>
            <Pdf :src="pdfUrl" :page="pdfCurrent" @num-pages="pdfCount = $event" style="width: 100%;" />
          </template>
          <template v-else>
            <div style="text-align: center">
              <img :src="pdfUrl" style="max-width: 100%" />
            </div>
          </template>
        </template>
        <el-empty v-else />
      </div>
    </div>
  </el-dialog>
</template>

<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    info: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      pinfoindex: 1,
      pdfUrl: 'https://example.com/path/to/your/pdf/file.pdf',      
      pdfCurrent: 1,
      pdfCount: 0,
    };
  },
  watch:{
    'info':{
      handler(newVal,oldVal){
        this.pinfoindex = 1
      },
      deep: true,
      immediate: true
    }
  },
  computed: {
    internalVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit('update:visible', val);
      },
    },
  },
  methods:{
       //预览ped
       drawPreView(e) {
      this.pdfshow = true;
      this.pdfUrl = process.env.VUE_APP_BASE_API + e.replace('https://www.ziyouke.net', '');
    },

  }
};
</script>

<style scoped>
.pinfobox {
  width: 100%;
  height: 490px;
  overflow: hidden;
}

.pinfobox1 {
  width: 100%;
  height: 50px;
  display: flex;
  background: #F2F2F8;
  box-shadow: 0px 1px 0px 0px rgba(0, 0, 0, 0.15);
}

.pinfobox1>div {
  width: 160px;
  height: 50px;
  line-height: 50px;
  text-align: center;
  cursor: pointer;
  transition: 0.2s;
}

.pinfobox1sel {
  background: #FFFFFF;
  border: 1px solid #F2F2F8;
  color: #2E73F3;
  transition: 0.2s;
}

.pinfobox2 {
  width: 100%;
  height: 290px;
  display: flex;
  margin-top: 30px;
  flex-wrap: wrap;
}

.pinfobox2-2 {
  height: 100%;
  margin-left: 20px;
}

.pinfobox2-2-1 {
  display: flex;
  border-bottom: 1px solid #CBD6E2;
  border-left: 1px solid #CBD6E2;
  border-right: 1px solid #CBD6E2;
}

.pinfobox2-2-1>div:first-child {
  width: 135px;
  height: 50px;
  text-align: center;
  line-height: 50px;
  background-color: #EFEFF3;
}

.pinfobox2-2-1>div:last-child {
  width: 480px;
  height: 50px;
  text-align: center;
  line-height: 50px;
  text-indent: 50px;
}
</style>
