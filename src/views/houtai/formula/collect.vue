<template>
  <div>
    <!-- 收藏夹管理 -->
    <el-dialog v-dialogDragBox title="收藏夹管理" :visible.sync="open" width="600px">
      <el-row :gutter="20" class="mb10" v-if="!isChange">
        <el-col :span="1.5">
          <el-button type="primary" plain size="small" icon="el-icon-plus" @click="handleAdd">新增收藏夹</el-button>
        </el-col>
      </el-row>
      <el-table :data="dataList" border v-if="dataList.length" :key="key" v-loading="loading">
        <el-table-column align="center" label="序号" type="index"></el-table-column>
        <el-table-column align="center" prop="dirName" label="收藏夹名称">
          <template slot-scope="{ row }">
            <span v-show="!row.isEdit">{{ row.dirName }}</span>
            <el-input v-show="row.isEdit" size="small" v-model="editData.dirName"></el-input>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="sort" label="排序" v-if="!isChange">
          <template slot-scope="{ row }">
            <span v-show="!row.isEdit">{{ row.sort }}</span>
            <el-input v-show="row.isEdit" size="small" type="number" placeholder="请输入排序序号" v-model="editData.sort"></el-input>
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作" width="120">
          <template slot-scope="{ row }">
            <template v-if="isChange">
              <el-button @click="submitChange(row)" type="text" size="small" icon="el-icon-check" :disabled="row.storeId === storeId">选择</el-button>
            </template>
            <template v-else>
              <template v-if="!row.isEdit">
                <el-button @click="handleClick(row)" type="text" size="small" icon="el-icon-edit">编辑</el-button>
                <el-button @click="handleDelete(row)" type="text" size="small" icon="el-icon-delete">删除</el-button>
              </template>
              <template v-if="row.isEdit">
                <el-button @click="handleUpdate" type="text" size="small" icon="el-icon-check">确定</el-button>
                <el-button @click="handleCancel(row)" type="text" size="small" icon="el-icon-close">取消</el-button>
              </template>
            </template>
          </template>
        </el-table-column>
      </el-table>
      <el-empty v-else />
      <span slot="footer">
        <el-button @click="open = false">取消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getlist, addlist, editlist, dellist } from '@/api/houtai/shoucang'
import { quoteTemplateDir, quoteTemplateDirAdd } from '@/api/houtai/formula'
export default {
  data() {
    return {
      loading: true,
      dataList: [],
      storeId: undefined,
      open: false,
      key: 1,
      editData: {},
      isChange: false,
      changData: undefined,
      type: 'quote.template.outsourcing'
    }
  },
  created() {
    this.getList()
  },
  methods: {
    // 收藏列表
    getList() {
      this.loading = true
      quoteTemplateDir().then(res => {
        this.key = Math.random()
        this.dataList = res.data
        this.dataList.map(item => {
          item.isEdit = false
        })
        this.loading = false
        this.$emit('getCollectList')
      })
    },
    // 收藏夹管理
    handleMange() {
      this.open = true
      this.isChange = false
      this.getList()
    },
    // 新增收藏名称
    // prettier-ignore
    handleAdd() {
      this.$prompt('请输入收藏夹名称', '新增收藏夹', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
      }).then(({ value }) => {
        if (!value) return
        quoteTemplateDirAdd({dirName: value}).then((res) => {
        this.$modal.msgSuccess('新增成功')
        this.getList()
        })
      }).catch(() => {})
    },
    // 删除收藏名称
    // prettier-ignore
    handleDelete(item) {
      const data = { storeId: item.storeId, type: this.type }
      this.$modal.confirm('是否确认删除此收藏？').then(function () {
        return dellist(data)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess('删除成功')
      }).catch(() => {})
    },
    // 修改收藏名称
    handleClick(item) {
      item.isEdit = true
      this.editData = { ...item }
      this.key = Math.random()
    },
    // 取消收藏名称
    handleCancel(item) {
      item.isEdit = false
      this.key = Math.random()
    },
    // 提交收藏名称
    handleUpdate() {
      const data = {
        storeId: this.editData.storeId,
        dirName: this.editData.dirName,
        sort: this.editData.sort,
        type: this.type
      }
      editlist(data).then(res => {
        this.getList()
        this.$modal.msgSuccess('修改成功')
      })
    },
    // 选择收藏夹
    handleChange(changData) {
      this.open = true
      this.isChange = true
      this.changData = changData
    },
    // 确定选择收藏夹
    submitChange(item) {
      const storeId = item.storeId
      this.$emit('colletSubmit', storeId, this.changData)
      this.open = false
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__body {
  padding: 10px 20px;
}
</style>
