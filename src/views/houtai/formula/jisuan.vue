<template>
  <div>
    <div class="jisuanbox" :class="[showa ? 'jisuanboxdw1' : 'jisuanboxdw2']">
      <div class="flex">
        <div class="flex-sub" style="padding: 10px;">
          <div class="round red" @click="yinc()">
            <i class="el-icon-minus"></i>
          </div>
        </div>
      </div>
      <div class="flex">
        <div class="flex-sub numa result" style="text-align: right">
          {{ display }}
        </div>
      </div>
      <div class="flex">
        <div class=" numb" @click="clearDisplay" style="flex:605">
          AC
        </div>
        <div class=" numc" @click="appendOperator('/')" style="flex:200">
          /
        </div>
      </div>
      <div class="flex">
        <div class="flex-sub numd" @click="appendNumber('7')">
          7
        </div>
        <div class="flex-sub numd" @click="appendNumber('8')">
          8
        </div>
        <div class="flex-sub numd" @click="appendNumber('9')">
          9
        </div>
        <div class="flex-sub numc" @click="appendOperator('*')">
          X
        </div>
      </div>
      <div class="flex">
        <div class="flex-sub numd" @click="appendNumber('4')">
          4
        </div>
        <div class="flex-sub numd" @click="appendNumber('5')">
          5
        </div>
        <div class="flex-sub numd" @click="appendNumber('6')">
          6
        </div>
        <div class="flex-sub numc" @click="appendOperator('-')">
          -
        </div>
      </div>
      <div class="flex">
        <div class="flex-sub numd" @click="appendNumber('1')">
          1
        </div>
        <div class="flex-sub numd" @click="appendNumber('2')">
          2
        </div>
        <div class="flex-sub numd" @click="appendNumber('3')">
          3
        </div>
        <div class="flex-sub numc" @click="appendOperator('+')">
          +
        </div>
      </div>
      <div class="flex">
        <div class=" numd" style="flex:405" @click="appendNumber('0')">
          0
        </div>
        <div class="flex-sub numd" style="flex:200" @click="appendDecimal('.')">
          .
        </div>
        <div class=" numc" style="flex:200" @click="calculateResult">
          =
        </div>
      </div>



    </div>

    <div v-if="!showa">
      <div class="jisuanboxdw3" @click="showa = true">
        <i class="el-icon-arrow-left"></i>
      </div>

    </div>
  </div>
</template>

<script>
export default {
  name: "home",
  data() {
    return {
      display: '0',
      calculation: '',
      showa: false,
    };
  },
  methods: {
    appendNumber(number) {
      if (this.display === '0') {
        this.display = number;
      } else {
        this.display += number;
      }
      this.calculation += number;
    },
    appendOperator(operator) {
      this.display = operator;
      this.calculation += operator;
    },
    appendDecimal() {
      if (!this.display.includes('.')) {
        this.display += '.';
        this.calculation += '.';
      }
    },
    clearDisplay() {
      this.display = '0';
      this.calculation = '';
    },
    calculateResult() {
      try {
        const result = eval(this.calculation);
        this.display = result.toString();
        this.calculation = result.toString();
      } catch (error) {
        this.display = 'Error';
      }
    },
    yinc() {
      this.showa = false;
    },
    xianshi() {
      this.showa = true;

    }
  }
}
</script>

<style scoped>
.jisuanbox {
  width: 400px;
  height: 600px;
  background: #5a5353;
  border-radius: 5px;
  transition: .5s;
}

.flex {
  display: flex;
}

.flex-sub {
  flex: 1
}

.round {
  border-radius: 50%;
  width: 25px;
  height: 25px;
  text-align: center;
  color: #fff;
}

.red {
  background: #f0695c;
}

.red:hover {
  cursor: pointer;
  background: #ed7a6f;
}

.numa {
  color: #fff;
  padding-right: 20px;
  font-size: 70px;
}

.numb {
  color: #fff;
  font-size: 40px;
  line-height: 90px;
  text-align: center;
  background: #6a6464;
  border: 1px solid #5a5353;
}

.numc {
  color: #fff;
  font-size: 40px;
  line-height: 90px;
  text-align: center;
  background: #f3a52e;
  border: 1px solid #5a5353;
}

.numd {
  color: #fff;
  font-size: 40px;
  line-height: 90px;
  text-align: center;
  background: #837e7e;
  border: 1px solid #5a5353;
}

.numc:hover {
  cursor: pointer;
  background: #c18222;
}

.numd:hover {
  cursor: pointer;
  background: #b4b1b1;
}

.numb:hover {
  cursor: pointer;
  background: #827e7d;
}

.jisuanboxdw1 {
  position: fixed;
  right: 10px;
  bottom: 10px;
  z-index: 999;
}

.jisuanboxdw2 {
  position: fixed;
  right: -400px;
  bottom: 10px;
  z-index: 999;
}

.jisuanboxdw3 {
  position: fixed;
  right: 0;
  top: 50%;
  height: 100px;
  margin-top: -50px;
  width: 40px;
  background: #5a5353;
  color: #fff;
  font-size: 40px;
  cursor: pointer;
  line-height: 100px;

}

.jisuanboxdw3:hover {
  background: #827e7d;
}</style>
