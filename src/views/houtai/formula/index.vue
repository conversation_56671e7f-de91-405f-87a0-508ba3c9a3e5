<template>
  <div class="container">
    <!-- 报价列表 -->
    <div class="list" :class="{ listHeight: showMore }">
      <div class="list-add" @click="addView = true;" title="新增报价">
        <img src="../../../../public/imgs/增加@2xblue.png">
        新增报价
      </div>
      <div v-for="(item, index) in list" @click="listinfo(index, item)" :key="index"
        :class="list_index == index ? 'list-item-sel' : 'list-item'">
        {{ item.name }}
        <img v-if="list_index == index" @click="ediInfo = item; ediinfoview = true;" class="list-item-img"
          src="../../../../public/imgs/编辑@2x.png">
      </div>
      <div class="list-more" @click="showMore = !showMore"><el-tooltip class="item" effect="dark"
          :content="showMore ? '点击收缩' : '点击展开'" placement="top-start"><i
            :class="showMore ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i></el-tooltip></div>
    </div>

    <!-- 顶部商品 -->
    <div class="box" v-if="list_index != -1">
      <div class="box-1">
        <div>报价关联产品</div>
        <div>报价编号：{{ list[list_index].serial }}</div>
      </div>
      <div class="box-2" v-if="info.product != null">
        <div>产品名称：</div>
        <div style="color: #333;">{{ info.product.productName }}
          <span style="color:#2E73F3;">{{ info.bindSource === 'common' ? '（公域产品）' : '' }}</span>
          <span style="color:#FE7F22;">{{ info.bindSource === 'private' ? '（私域产品）' : '' }}</span>
          <span>{{ info.bindSource === 'add' ? '（自定义）' : '' }}</span>
        </div>
        <div>产品编号：</div>
        <div style="color: #333;margin-left:0">{{ info.product.productCode }}</div>
        <div>产品规格：</div>
        <div style="color: #333;margin-left:0">{{ info.product.specs }}</div>
        <div>产品图片：</div>
        <div class="box-2-img">
          <img :src="info.product.picture1_oss || imgPath + info.product.picture1">
        </div>
        <div v-if="info.product.diagram">三维图片</div>
        <div class="box-2-img" v-if="info.product.diagram">
          <img :src="info.product.diagram_oss || imgPath + info.product.diagram">
        </div>
        <div class="box-2-a" style="color:#2E73F3;" @click="drawPreView(info.product, 'paper')">图纸</div>
        <div class="box-2-a" style="color:#2E73F3;" @click="drawPreView(info.product)">产品详情</div>
        <div class="box-2-a" style="color:#2E73F3;" @click="bindView = true;">更换产品</div>
      </div>
      <div v-else style="width: 100%;height:82px;display: flex;">
        <div class="btn3" @click="bindView = true;">
          请先关联产品
        </div>
      </div>
    </div>

    <!-- 内容 -->
    <div class="box2">
      <!-- 产品列表 -->
      <template v-if="info.fragments">
        <div class="goods" v-for="(item, index) in info.fragments" :key="index">
          <!--标题-->
          <div class="goods1">{{ item[0].stageName }}</div>
          <!-- 每一个历史阶段 -->
          <div class="goods2" v-for="(item2, index2) in item" :key="index2">
            <!-- 阶段标题 -->
            <div class="goods2-1">
              <div @click="handleView(item2)">{{ item2.seq }}. {{ item2.templateName }}</div>
              <div style="color: #999;width: 20%;" @click="handleView(item2)">
                备注: {{ item2.remark }}
              </div>
              <div v-for="(item3, index3) in item2.ext" style="width: 20%;position: relative;" :key="index3">
                {{ item3.itemName }}:{{
                item3.value }}
                {{ item3.itemUnit + '&nbsp;&nbsp;' }}<el-button v-clipboard:copy="item3.value"
                  v-clipboard:success="copySuccess" type="text" style="position: absolute;top:5px">复制</el-button></div>
              <div style="position:absolute; right:100px;" @click="handleView(item2)">小计 :<span
                  style="color: #EC2454;font-weight: bold;">¥
                  {{ item2.price }}</span></div>
              <img @click="handleView(item2)"
                :style="f_id == item2.fragmentId ? 'transform: rotate(180deg)' : 'transform: rotate(0deg)'"
                class="goods2-1-jt" src="../../../../public/imgs/下箭头.png">
            </div>
            <el-collapse-transition>
              <div class="goods2-2" v-if="f_id == editData.fragmentId && f_id === item2.fragmentId">
                <div class="tmpbox">
                  <div class="tmpbox-1" style="display: inline-flex;">
                    <div>
                      {{ editData.templateName }}
                      <span v-if="editData.innerQuote"> 已关联二级报价:{{ editData.innerQuote.name }}</span>
                      <span v-if="editData.product"> 已关联产品:{{ editData.product.productName }} / {{
                        editData.product.specs }}</span>
                      <span v-if="editData.k"> 已关联K线:{{ editData.k.name }} / {{ editData.k.price }}元 <template
                          v-if="editData.fragmentItems.k"><span v-if="editData.fragmentItems.k[0].value">附加运费：{{
                            editData.fragmentItems.k[0].value }}元</span></template></span>
                    </div>
                    <div class="glbox" v-if="editData.stage == 'outsourcing' && editData.fragmentId == pd_id"
                      @click="handleOneKey(item2)">
                      <img src="../../../../public/imgs/关联 <EMAIL>">
                      一键关联
                    </div>
                    <div class="glbox" v-if="editData.stage == 'inner' && editData.fragmentId == pd_id"
                      @click="handleOneKeyList()">
                      <img src="../../../../public/imgs/关联 <EMAIL>">
                      一键关联
                    </div>
                  </div>
                  <div class="show" v-if="editData" style="background-color: white;">
                    <div class="showitem">
                      <template v-for="(item3, index3) in editData.fragmentItems.show">
                        <template v-if="item3.itemType == 'select' && item3.display">
                          <div class="showitem2" :key="index3">
                            <div>
                              {{ item3.itemName }}
                            </div>
                            <div>
                              <el-select v-model="item3.value" placeholder="请选择" style="width: 100%;"
                                :disabled="editData.fragmentId != pd_id && !isEdit">
                                <el-option v-for="option in item3.values" :key="option.itemValueId"
                                  :label="option.valueName" :value="option.value"></el-option>
                              </el-select>
                            </div>
                          </div>
                        </template>
                        <template v-if="item3.itemType == 'input' && item3.display">
                          <div class="showitem2" :key="index3">
                            <div>
                              {{ item3.itemName }}
                            </div>
                            <div style="position:relative;">
                              <el-input v-model="item3.value" :placeholder="item3.placeholder"
                                :disabled="editData.fragmentId != pd_id && !isEdit"
                                :onkeyup="item3.pattern ? `this.value=this.value.replace(${item3.pattern},'')` : ''"
                                @blur="item3.value = $event.target.value">
                              </el-input>
                              <div class="unit">{{ item3.itemUnit }}</div>
                            </div>
                          </div>
                        </template>
                        <template v-if="item3.itemType == 'input-select' && item3.display">
                          <div class="showitem2" style="position:relative;" :key="index3">
                            <div>
                              {{ item3.itemName }}
                            </div>
                            <div>
                              <el-input v-model="item3.value" :placeholder="item3.placeholder"
                                :disabled="editData.fragmentId != pd_id && !isEdit"
                                :onkeyup="item3.pattern ? `this.value=this.value.replace(${item3.pattern},'')` : ''"
                                @blur="item3.value = $event.target.value"></el-input>
                              <div class="unit" v-for="(u, ui) in JSON.parse(item3.itemUnit)" :key="ui" v-if="u.convert == 1">
                                {{ u.name }}</div>
                            </div>
                            <div class="hsbox">
                              <div v-for="(u, ui) in JSON.parse(item3.itemUnit)" :key="ui" v-if="u.convert != 1"
                                style="margin-left: 20px;">
                                换算 <span style="color: #333;">{{ parseFloat((item3.value * u.convert).toFixed(12))
                                  }}</span> {{ u.name }}
                              </div>
                            </div>
                          </div>
                        </template>
                        <!-- 废料抵值 -->
                        <template v-if="item3.itemType == 'radio' && item3.display">
                          <div class="showitem2" style="position:relative; width: 100%;text-align: left;" :key="index3">
                            <div>
                              {{ item3.itemName }}
                            </div>
                            <div>
                              <el-radio :disabled="editData.fragmentId != pd_id && !isEdit"
                                @input="editData.displayItemIds = item4.displayItemIds; item3.value = item3.longVal"
                                v-for="(item4, index4) in item3.values" :key="index4" v-model="item3.longVal"
                                :label="item4.itemValueId">{{ item4.valueName }}</el-radio>
                            </div>
                          </div>
                        </template>
                        <!-- 废料抵值项  -->
                        <template
                          v-if="item3.itemType === 'input-radio' && editData.displayItemIds.some(id => id === item3.itemId)">
                          <div class="showitem2" style="position:relative;" :key="index3">
                            <div>
                              {{ item3.itemName }}
                            </div>
                            <el-input v-model="item3.value" :placeholder="item3.placeholder"
                              :disabled="editData.fragmentId != pd_id && !isEdit"
                              :onkeyup="item3.pattern ? `this.value=this.value.replace(${item3.pattern},'')` : ''"
                              @blur="item3.value = $event.target.value">
                            </el-input>
                            <div class="unit" :style="item3.itemId == 51 ? 'right:30px' : ''">{{ item3.itemUnit }}</div>
                            <div class="tsbox" style="right: 5px;top:13px" v-if="item3.itemId == 51">
                              <div class="tsbox2">
                                <div>此处成品重量引用自下方计算结果区净重</div>
                                <div>公式:毛重-净重=废料重</div>
                              </div>
                            </div>
                          </div>
                        </template>
                      </template>
                      <div class="btnbox" v-if="pd_id == editData.fragmentId && isEdit">
                        <div class="btn2" @click="jisuan_1(editData.fragmentItems)" v-if="editData.calculate">
                          立 即 计 算
                        </div>
                      </div>
                      <template v-if="editData.stage == 'inner' && editData.innerQuote">
                        <el-form ref="innerQuote" :model="editData.innerQuote" disabled label-width="5em"
                          class="innerQuote-form">
                          <el-row :gutter="30">
                            <el-col :span="12">
                              <el-form-item label="报价编号">
                                <el-input v-model="editData.innerQuote.serial"></el-input>
                              </el-form-item>
                            </el-col>
                            <el-col :span="12">
                              <el-form-item label="报价名称">
                                <el-input v-model="editData.innerQuote.name"></el-input>
                              </el-form-item>
                            </el-col>
                            <el-col :span="12">
                              <el-form-item label="总价">
                                <el-input v-model="editData.innerQuote.totalPrice">
                                  <template slot="prefix">￥</template>
                                  <template slot="suffix">元</template>
                                </el-input>
                              </el-form-item>
                            </el-col>
                            <el-col :span="12">
                              <el-form-item label="毛总重">
                                <el-input v-model="editData.innerQuote.grossWeight">
                                  <template slot="suffix">Kg</template>
                                </el-input>
                              </el-form-item>
                            </el-col>
                            <el-col :span="12">
                              <el-form-item label="净总重">
                                <el-input v-model="editData.innerQuote.netWeight">
                                  <template slot="suffix">Kg</template>
                                </el-input>
                              </el-form-item>
                            </el-col>
                            <el-col :span="12">
                              <el-form-item label="报价产品">
                                <span class="innerQuote-form-link" @click="drawPreView(editData.innerQuote.product)">{{
                                  editData.innerQuote.product.productName }}</span>
                              </el-form-item>
                            </el-col>
                          </el-row>
                        </el-form>
                      </template>
                    </div>
                    <div class="showimg">
                      <template v-if="editData.dynamicType == 'image'">
                        <img :src="imgPath + editData.dynamic">
                      </template>
                      <template v-else>
                        <div class="showimgremark" v-if="editData.stage == 'process'">工艺说明</div>
                        <div class="showimgremark" v-else-if="editData.stage == 'outsourcing'">产品说明</div>
                        <div class="showimgremark" v-else-if="editData.stage == 'outProcess'">加工手段</div>
                        <div class="showimgremark" v-else>费用说明</div>
                        <div style="width: 90%;margin:10px auto;font-size: 12px;color: #999;" v-html="editData.dynamic">
                        </div>
                      </template>
                    </div>
                  </div>
                  <div class="tmpbox-2" v-if="editData.stage != 'inner'">
                    计算结果
                  </div>
                  <div class="show" style="background: #F1F3F8;">
                    <div class="showitem">
                      <template v-if="editData.stage != 'inner'">
                        <template v-for="(item3, inde3x) in editData.fragmentItems.result">
                          <template v-if="item3.itemType == 'input'">
                            <div class="showitem2" :class="{ showitem100: item3.itemId == 6 && isEdit }" :key="inde3x">
                              <div>
                                {{ item3.itemName }}
                              </div>
                              <div style="position: relative;">
                                <el-input v-model="item3.value" :placeholder="item3.placeholder"
                                  :disabled="editData.fragmentId != pd_id && !isEdit"
                                  :onkeyup="item3.pattern ? `this.value=this.value.replace(${item3.pattern},'')` : ''">
                                </el-input>
                                <div class="unit">{{ item3.itemUnit }}</div>
                                <div class="glbtn" v-if="item3.itemId == 6 && isEdit"
                                  @click="handlePriceOneKey(editData)">
                                  一键关联
                                </div>
                              </div>
                              <template v-if="kCheckId">
                                <div v-if="gl_tmp3.product_name && item3.itemId == 6 && isEdit" class="glproduct">
                                  关联K线 : {{ gl_tmp3.product_name }} / {{ gl_tmp3.specs }}<span
                                    v-if="gl_tmp3.freightValue"> / 附加运费：{{ gl_tmp3.freightValue }}元</span>
                                </div>
                              </template>
                              <template v-else>
                                <div v-if="gl_tmp3.product_name && item3.itemId == 6 && isEdit" class="glproduct"
                                  @click="drawPreView(gl_tmp3)">
                                  关联产品 : {{ gl_tmp3.product_name }} / {{ gl_tmp3.specs }}<span
                                    v-if="gl_tmp3.freightValue"> / 附加运费：{{ gl_tmp3.freightValue }}元</span>
                                </div>
                              </template>
                            </div>
                            <!-- <div class="showitem2" v-if="item3.itemId==6 && isEdit"></div> -->
                          </template>
                          <template v-if="item3.itemType == 'input-expression'">
                            <div class="showitem2" :key="inde3x">
                              <div>
                                {{ item3.itemName }}
                              </div>
                              <div style="position: relative;">
                                <el-input v-model="item3.value" :placeholder="item3.placeholder"
                                  :disabled="editData.fragmentId != pd_id && !isEdit"
                                  :onkeyup="item3.pattern ? `this.value=this.value.replace(${item3.pattern},'')` : ''">
                                </el-input>
                                <div class="unit">{{ item3.itemUnit }}</div>
                              </div>
                            </div>
                          </template>
                          <template v-if="item3.itemType == 'input-reference'">
                            <div class="showitem2" :key="inde3x">
                              <div>
                                {{ item3.itemName }}
                              </div>
                              <div style="position: relative;">
                                <el-input v-model="item3.value" :placeholder="item3.placeholder"
                                  :disabled="editData.fragmentId != pd_id && !isEdit"
                                  :onkeyup="item3.pattern ? `this.value=this.value.replace(${item3.pattern},'')` : ''">
                                </el-input>
                                <div class="unit">{{ item3.itemUnit }}</div>
                              </div>
                            </div>
                          </template>
                        </template>
                        <div class="showitem2">
                          <div style="color: #999;">
                            备注
                          </div>
                          <div style="position: relative;">
                            <el-input v-model="editData.remark" :disabled="pd_id != editData.fragmentId && !isEdit">
                            </el-input>
                          </div>
                        </div>
                      </template>
                      <div class="btnbox">
                        <div class="btn2" @click="handleEdit(item2)" v-if="pd_id != editData.fragmentId && !isEdit">
                          修 改
                        </div>
                        <div class="btn2" v-if="pd_id == editData.fragmentId && isEdit" @click="savepd(editData)">
                          保 存
                        </div>
                        <div @click="jisuan2_1(editData.fragmentItems)" class="btn2"
                          v-if="pd_id === editData.fragmentId && editData.calculate && (editData.stage === 'material' || editData.stage === 'outsourcing') && isEdit">
                          获 取 小 计
                        </div>
                        <div class="btn2" @click="pddel(editData.fragmentId)"
                          v-if="pd_id == editData.fragmentId && isEdit">
                          删 除 该 条
                        </div>
                      </div>
                    </div>
                    <div class="showimg" style="border: 0;">
                    </div>
                  </div>
                </div>
              </div>
            </el-collapse-transition>
          </div>
        </div>
      </template>

      <!-- 阶段模板 -->
      <div class="tem-class" v-if="stage_index === 2">
        <div class="class-item class-item-add" title="收藏夹管理" @click="handleCollectMange"><i class="el-icon-menu"></i>
        </div>
        <div class="class-item" :class="{ active: !collectId }" @click="handleChangeCollect('all')">全部</div>
        <div class="class-item" :class="{ active: collectId === col.storeId }" v-for="col in collectList" :key="col.storeId"
          @click="handleChangeCollect(col)">{{ col.dirName }}</div>
      </div>
      <div class="tmp" v-if="stage_index != -1" style="position:relative;">
        <!-- 模板选项 -->
        <div class="tmplist" style="position:relative;overflow:inherit" v-if="collectId && stage_index === 2">
          <el-tooltip effect="dark" content="右击添加收藏" placement="top" v-for="(item, index) in collectstageitem"
            :key="'co' + index">
            <div @click="seltmp(index)" :class="tmp_index == index ? 'tmplist-item-sel' : 'tmplist-item'"
              @contextmenu.prevent="openMenu(item, $event)">
              <div><img :src="'data:image/png;base64,' + item.icon" /></div>
              <div>{{ item.name }}</div>
              <img v-if="tmp_index == index" class="tmplist-item-sel-icon"
                src="../../../../public/imgs/<EMAIL>">
            </div>
          </el-tooltip>
          <div v-if="stage_index != -1 && stages[stage_index].canCreateNew" class="tmplist-item"
            style="background-color: white;border: 1px solid#CBD7E2;">
            <div style="width: 100%;text-align: center;margin: 0;margin-left: 10px;"
              @click="addtmpview = true; cleartmp();">
              新增{{ stages[stage_index].name }}</div>
          </div>
        </div>
        <div class="tmplist" style="position:relative;overflow:inherit" v-else>
          <template v-if="stage_index === 2">
            <el-tooltip effect="dark" content="右击添加收藏" placement="top" v-for="(item, index) in stageitem"
              :key="'stage' + index">
              <div @click="seltmp(index)" :class="tmp_index == index ? 'tmplist-item-sel' : 'tmplist-item'"
                @contextmenu.prevent="openMenu(item, $event)">
                <div><img :src="'data:image/png;base64,' + item.icon" /></div>
                <div>{{ item.name }}</div>
                <img v-if="tmp_index == index" class="tmplist-item-sel-icon"
                  src="../../../../public/imgs/<EMAIL>">
                <img v-if="tmp_index == index && item.canDel" src="../../../../public/imgs/取消 <EMAIL>"
                  style="position: absolute;top: -10px;right: -10px;width: 20px;height: 20px;" @click="deltmp(item)">
              </div>
            </el-tooltip>
          </template>
          <template v-else>
            <div v-for="(item, index) in stageitem" @click="seltmp(index)"
              :class="tmp_index == index ? 'tmplist-item-sel' : 'tmplist-item'"
              @contextmenu.prevent="openMenu(item, $event)" :key="'stageitem' + index">
              <div><img :src="'data:image/png;base64,' + item.icon" /></div>
              <div>{{ item.name }}</div>
              <img v-if="tmp_index == index" class="tmplist-item-sel-icon"
                src="../../../../public/imgs/<EMAIL>">
              <img v-if="tmp_index == index && item.canDel" src="../../../../public/imgs/取消 <EMAIL>"
                style="position: absolute;top: -10px;right: -10px;width: 20px;height: 20px;" @click="deltmp(item)">
            </div>
          </template>
          <div v-if="stage_index != -1 && stages[stage_index].canCreateNew" class="tmplist-item"
            style="background-color: white;border: 1px solid#CBD7E2;">
            <div style="width: 100%;text-align: center;margin: 0;margin-left: 10px;"
              @click="addtmpview = true; cleartmp();">
              新增{{ stages[stage_index].name }}</div>
          </div>
        </div>
        <ul v-if="stage_index === 2" v-show="visible" :style="{ left: left + 'px', top: top + 'px' }" class="contextmenu">
          <li @click="handleCollet"><i class="el-icon-star-on"></i> 收藏到</li>
          <li v-if="collectId" @click="handleColletDelete"><i class="el-icon-delete"></i> 删除</li>
        </ul>
        <!-- 模板内容 -->
        <el-collapse-transition v-if="collectId && stage_index === 2">
          <div class="tmpbox" v-if="tmp_index != -1">

            <!-- 标题部分 -->
            <div class="tmpbox-1" style="display: flex;">
              <div>
                {{ collectstageitem[tmp_index].name }}
              </div>
              <div class="glbox" v-if="collectstageitem[tmp_index].stage == 'outsourcing'" @click="handleOneKey()">
                <img src="../../../../public/imgs/关联 <EMAIL>">
                一键关联
              </div>
              <div v-if="gl_tmp.product_name" style="cursor: pointer;" @click="drawPreView(gl_tmp)">
                关联产品:{{ gl_tmp.product_name }} / {{ gl_tmp.specs }}
                <span v-if="ys_is_bj">已关联报价</span>
              </div>
            </div>
            <!-- 显示部分 -->
            <div class="show" v-if="collectstageitem[tmp_index].items">
              <!-- 显示模块内容   -->
              <div class="showitem">
                <!-- 循环show里面的每一项 -->
                <template v-for="(item, index) in collectstageitem[tmp_index].items.show">
                  <template v-if="item.item.itemType == 'select' && item.item.display">
                    <div class="showitem2" :key="index">
                      <div>
                        {{ item.item.itemName }}
                      </div>
                      <div>
                        <el-select v-model="addData.needItems[index].value" placeholder="请选择" style="width: 100%;">
                          <el-option v-for="option in item.item.values" :key="option.itemValueId"
                            :label="option.valueName" :value="option.value"></el-option>
                        </el-select>
                      </div>
                    </div>
                  </template>
                  <!-- 显示按吨报价 -->
                  <template v-if="item.item.itemType == 'input' && item.item.display">
                    <template v-if="(item.item.itemId == 17 && collectstageitem[tmp_index].stage == 'process') ||
                      (item.item.itemId == 17 && collectstageitem[tmp_index].stage == 'outsourcing') ||
                      (item.item.itemId == 17 && collectstageitem[tmp_index].stage == 'outProcess') ||
                      (item.item.itemId == 17 && collectstageitem[tmp_index].stage == 'freight') ||
                      (item.item.itemId == 17 && collectstageitem[tmp_index].stage == 'laborCost') ||
                      (item.item.itemId == 17 && collectstageitem[tmp_index].stage == 'offsetLoss') ||
                      (item.item.itemId == 29 && collectstageitem[tmp_index].stage == 'process') ||
                      (item.item.itemId == 29 && collectstageitem[tmp_index].stage == 'outsourcing') ||
                      (item.item.itemId == 29 && collectstageitem[tmp_index].stage == 'outProcess') ||
                      (item.item.itemId == 29 && collectstageitem[tmp_index].stage == 'freight') ||
                      (item.item.itemId == 29 && collectstageitem[tmp_index].stage == 'laborCost') ||
                      (item.item.itemId == 29 && collectstageitem[tmp_index].stage == 'offsetLoss') ||
                      (item.item.itemId == 19 && collectstageitem[tmp_index].stage == 'process') ||
                      (item.item.itemId == 19 && collectstageitem[tmp_index].stage == 'outsourcing') ||
                      (item.item.itemId == 19 && collectstageitem[tmp_index].stage == 'outProcess') ||
                      (item.item.itemId == 19 && collectstageitem[tmp_index].stage == 'freight') ||
                      (item.item.itemId == 19 && collectstageitem[tmp_index].stage == 'laborCost') ||
                      (item.item.itemId == 19 && collectstageitem[tmp_index].stage == 'offsetLoss')
                    ">
                      <div class="showitem2" :class="{ process: collectstageitem[tmp_index].stage == 'process' }" :key="index">
                        <div>
                          {{ item.item.itemName }}
                        </div>
                        <div style="position:relative;width: 60%;">
                          <el-input v-model="addData.needItems[index].value" :placeholder="item.item.placeholder"
                            :onkeyup="item.item.pattern ? `this.value=this.value.replace(${item.item.pattern},'')` : ''"
                            @blur="addData.needItems[index].value = $event.target.value">
                          </el-input>
                          <div class="unit">{{ item.item.itemUnit }}</div>
                        </div>
                        <div class="dunjs" @click="handleTonCalc(addData.needItems, addData.needItems[index])">
                          <img src="../../../../public/imgs/吨位 <EMAIL>">
                          按吨计算
                        </div>
                      </div>
                    </template>
                    <template v-else>
                      <div class="showitem2" :key="index">
                        <div>
                          {{ item.item.itemName }}
                        </div>
                        <div style="position:relative;">
                          <el-input v-model="addData.needItems[index].value" :placeholder="item.item.placeholder"
                            :onkeyup="item.item.pattern ? `this.value=this.value.replace(${item.item.pattern},'')` : ''"
                            @blur="addData.needItems[index].value = $event.target.value">
                          </el-input>
                          <div class="unit">{{ item.item.itemUnit }}</div>
                        </div>
                      </div>
                    </template>
                  </template>
                  <template v-if="item.item.itemType == 'input-select' && item.item.display">
                    <div class="showitem2" style="position:relative;height: 60px;" :key="index">
                      <div>
                        {{ item.item.itemName }}
                      </div>
                      <div>
                        <el-input v-model="addData.needItems[index].value" :placeholder="item.item.placeholder"
                          :onkeyup="item.item.pattern ? `this.value=this.value.replace(${item.item.pattern},'')` : ''"
                          @blur="addData.needItems[index].value = $event.target.value"></el-input>
                        <div class="unit" v-for="(u, ui) in JSON.parse(item.item.itemUnit)" :key="ui" v-if="u.convert == 1">{{
                          u.name }}
                        </div>
                      </div>
                      <div class="hsbox">
                        <div v-for="(u, ui) in JSON.parse(item.item.itemUnit)" :key="ui" v-if="u.convert != 1"
                          style="margin-left: 20px;">
                          换算 <span style="color: #333;">{{ parseFloat((addData.needItems[index].value *
                            u.convert).toFixed(12)) }}</span> {{ u.name }}
                        </div>
                      </div>
                    </div>
                  </template>
                  <!-- 废料抵值 -->
                  <template v-if="item.item.itemType == 'radio' && item.item.display">
                    <div class="showitem2" style="position:relative; width: 100%;text-align: left;">
                      <div>
                        {{ item.item.itemName }}
                      </div>
                      <div>
                        <el-radio @input="feiliao = item2.displayItemIds" v-for="(item2, index2) in item.item.values"
                          :key="index2" v-model="addData.needItems[index].value" :label="item2.itemValueId">{{ item2.valueName
                          }}</el-radio>
                      </div>
                    </div>
                  </template>
                  <!-- 废料抵值项 -->
                  <template
                    v-if="item.item.itemType == 'input-radio' && !item.item.display && feiliao.some(id => id === item.item.itemId)">
                    <div class="showitem2" :key="index">
                      <div>
                        {{ item.item.itemName }}
                      </div>
                      <div style="position:relative;">
                        <el-input v-model="addData.needItems[index].value" :placeholder="item.item.placeholder"
                          :onkeyup="item.item.pattern ? `this.value=this.value.replace(${item.item.pattern},'')` : ''"
                          @blur="addData.needItems[index].value = $event.target.value">
                        </el-input>
                        <div class="unit" :style="item.item.itemId == 51 ? 'right: 30px' : ''">{{ item.item.itemUnit }}
                        </div>
                        <div class="tsbox" style="right: 5px;top: 13px" v-if="item.item.itemId == 51">
                          <div class="tsbox2">
                            <div>此处成品重量引用自下方计算结果区净重</div>
                            <div>公式:毛重-净重=废料重</div>
                          </div>

                        </div>
                      </div>
                    </div>
                  </template>
                </template>

                <div class="btnbox">
                  <div class="btn2" @click="jisuan()" v-if="collectstageitem[tmp_index].calculate">
                    立 即 计 算
                  </div>
                </div>
              </div>
              <div class="showimg">
                <template v-if="collectstageitem[tmp_index].dynamicType == 'image'">
                  <img :src="imgPath + collectstageitem[tmp_index].dynamic">
                </template>
                <template v-else>
                  <div class="showimgremark" v-if="collectstageitem[tmp_index].stage == 'process'">工艺说明
                    <img class="editicon" @click="dynamicview = true; dynamicData = collectstageitem[tmp_index]"
                      src="../../../../public/imgs/编辑@2x.png">
                  </div>
                  <div class="showimgremark" v-else-if="collectstageitem[tmp_index].stage == 'outsourcing'">产品说明
                    <img class="editicon" @click="dynamicview = true; dynamicData = collectstageitem[tmp_index]"
                      src="../../../../public/imgs/编辑@2x.png">
                  </div>
                  <div class="showimgremark" v-else-if="collectstageitem[tmp_index].stage == 'outProcess'">加工手段
                    <img class="editicon" @click="dynamicview = true; dynamicData = collectstageitem[tmp_index]"
                      src="../../../../public/imgs/编辑@2x.png">
                  </div>
                  <div class="showimgremark" v-else>费用说明
                    <img class="editicon" @click="dynamicview = true; dynamicData = collectstageitem[tmp_index]"
                      src="../../../../public/imgs/编辑@2x.png">
                  </div>
                  <div style="width: 90%;margin:10px auto;font-size: 12px;color: #999;"
                    v-html="collectstageitem[tmp_index].dynamic"></div>
                </template>
              </div>
            </div>
            <div class="tmpbox-2">
              计算结果
            </div>
            <!-- 计算部分 -->
            <div class="show" style="background: #F1F3F8;" v-if="collectstageitem[tmp_index].items">
              <!-- 显示模块内容   -->
              <div class="showitem">
                <!-- 循环result里面的每一项 -->
                <template v-for="(item, index) in collectstageitem[tmp_index].items.result">
                  <template v-if="item.item.itemType == 'input' && item.item.display">
                    <template v-if="item.itemId == 6 && collectstageitem[tmp_index].stage == 'material'">
                      <div class="showitem2 showitem100" :key="index">
                        <div>
                          {{ item.item.itemName }}
                        </div>
                        <div style="position:relative;">
                          <el-input v-model="resData.needItems[index].value" :placeholder="item.item.placeholder"
                            :onkeyup="item.item.pattern ? `this.value=this.value.replace(${item.item.pattern},'')` : ''">
                          </el-input>
                          <div class="unit">{{ item.item.itemUnit }}</div>
                          <div class="glbtn" @click="handlePriceOneKey()">
                            一键关联
                          </div>
                        </div>
                        <template v-if="kCheckId">
                          <div v-if="gl_tmp2.product_name" class="glproduct">
                            关联K线 : {{ gl_tmp2.product_name }} / {{ gl_tmp2.specs }} <span v-if="gl_tmp2.freightValue"> /
                              附加运费：{{ gl_tmp2.freightValue }}元</span>
                          </div>
                        </template>
                        <template v-else>
                          <div v-if="gl_tmp2.product_name" class="glproduct" @click="drawPreView(gl_tmp2)">
                            关联产品 : {{ gl_tmp2.product_name }} / {{ gl_tmp2.specs }} <span v-if="gl_tmp2.freightValue"> /
                              附加运费：{{ gl_tmp2.freightValue }}元</span>
                          </div>
                        </template>
                      </div>
                    </template>
                    <template v-else>
                      <div class="showitem2" :key="index">
                        <div>
                          {{ item.item.itemName }}
                        </div>
                        <div style="position: relative;">
                          <el-input v-model="resData.needItems[index].value" :placeholder="item.item.placeholder"
                            :onkeyup="item.item.pattern ? `this.value=this.value.replace(${item.item.pattern},'')` : ''">
                          </el-input>
                          <div class="unit">{{ item.item.itemUnit }}</div>
                        </div>
                      </div>
                    </template>
                  </template>
                  <template v-if="item.item.itemType == 'input-expression' && item.item.display">
                    <div class="showitem2" :key="index">
                      <div>
                        {{ item.item.itemName }}
                      </div>
                      <div style="position: relative;">
                        <el-input v-model="resData.needItems[index].value" :placeholder="item.item.placeholder"
                          :onkeyup="item.item.pattern ? `this.value=this.value.replace(${item.item.pattern},'')` : ''">
                        </el-input>
                        <div class="unit">{{ item.item.itemUnit }}</div>
                      </div>
                    </div>
                  </template>
                  <template v-if="item.item.itemType == 'input-reference' && item.item.display">
                    <div class="showitem2" :key="index">
                      <div>
                        {{ item.item.itemName }}
                      </div>
                      <div style="position: relative;">
                        <el-input v-model="resData.needItems[index].value" :placeholder="item.item.placeholder"
                          :onkeyup="item.item.pattern ? `this.value=this.value.replace(${item.item.pattern},'')` : ''">
                        </el-input>
                        <div class="unit">{{ item.item.itemUnit }}</div>
                      </div>
                    </div>
                  </template>
                </template>
                <div class="btnbox">
                  <div class="btn2" @click="jisuan2()"
                    v-if="collectstageitem[tmp_index].calculate &&
                      (collectstageitem[tmp_index].stage === 'material' || collectstageitem[tmp_index].stage === 'outsourcing')">
                    获 取 小 计
                  </div>
                </div>
              </div>
              <div class="showimg" style="border: 0;">
                <div class="showimgremark">请输入备注</div>
                <el-input type="textarea" v-model="collectstageitem[tmp_index].remark" placeholder="备注内容"></el-input>
              </div>
            </div>
          </div>
        </el-collapse-transition>
        <el-collapse-transition v-else>
          <div class="tmpbox" v-if="tmp_index != -1">
            <!-- 标题部分 -->
            <div class="tmpbox-1" style="display: flex;">
              <div>
                {{ stageitem[tmp_index].name }}
              </div>
              <div class="glbox" v-if="stageitem[tmp_index].stage == 'outsourcing'" @click="handleOneKey()">
                <img src="../../../../public/imgs/关联 <EMAIL>">
                一键关联
              </div>
              <div class="glbox" v-if="stageitem[tmp_index].stage == 'inner'" @click="handleOneKeyList()">
                <img src="../../../../public/imgs/关联 <EMAIL>">
                一键关联
              </div>
              <div v-if="gl_tmp.product_name" style="cursor: pointer;" @click="drawPreView(gl_tmp)">
                关联产品:{{ gl_tmp.product_name }} / {{ gl_tmp.specs }}
                <span v-if="ys_is_bj">已关联报价</span>
              </div>
              <div v-if="gl_tmp4.name">关联二级报价:{{ gl_tmp4.name }}</div>
            </div>
            <!-- 显示部分 -->
            <div class="show" v-if="stageitem[tmp_index].items">
              <!-- 显示模块内容   -->
              <div class="showitem">
                <!-- 循环show里面的每一项 -->
                <template v-for="(item, index) in stageitem[tmp_index].items.show">
                  <template v-if="item.item.itemType == 'select' && item.item.display">
                    <div class="showitem2">
                      <div>
                        {{ item.item.itemName }}
                      </div>
                      <div>
                        <el-select v-model="addData.needItems[index].value" placeholder="请选择" style="width: 100%;">
                          <el-option v-for="option in item.item.values" :key="option.itemValueId"
                            :label="option.valueName" :value="option.value"></el-option>
                        </el-select>
                      </div>
                    </div>
                  </template>
                  <!-- 显示按吨报价 -->
                  <template v-if="item.item.itemType == 'input' && item.item.display">
                    <template v-if="(item.item.itemId == 17 && stageitem[tmp_index].stage == 'process') ||
                      (item.item.itemId == 17 && stageitem[tmp_index].stage == 'outsourcing') ||
                      (item.item.itemId == 17 && stageitem[tmp_index].stage == 'outProcess') ||
                      (item.item.itemId == 17 && stageitem[tmp_index].stage == 'freight') ||
                      (item.item.itemId == 17 && stageitem[tmp_index].stage == 'laborCost') ||
                      (item.item.itemId == 17 && stageitem[tmp_index].stage == 'offsetLoss') ||
                      (item.item.itemId == 29 && stageitem[tmp_index].stage == 'process') ||
                      (item.item.itemId == 29 && stageitem[tmp_index].stage == 'outsourcing') ||
                      (item.item.itemId == 29 && stageitem[tmp_index].stage == 'outProcess') ||
                      (item.item.itemId == 29 && stageitem[tmp_index].stage == 'freight') ||
                      (item.item.itemId == 29 && stageitem[tmp_index].stage == 'laborCost') ||
                      (item.item.itemId == 29 && stageitem[tmp_index].stage == 'offsetLoss') ||
                      (item.item.itemId == 19 && stageitem[tmp_index].stage == 'process') ||
                      (item.item.itemId == 19 && stageitem[tmp_index].stage == 'outsourcing') ||
                      (item.item.itemId == 19 && stageitem[tmp_index].stage == 'outProcess') ||
                      (item.item.itemId == 19 && stageitem[tmp_index].stage == 'freight') ||
                      (item.item.itemId == 19 && stageitem[tmp_index].stage == 'laborCost') ||
                      (item.item.itemId == 19 && stageitem[tmp_index].stage == 'offsetLoss')
                    ">
                      <div class="showitem2" :class="{ process: stageitem[tmp_index].stage == 'process' }" :key="index">
                        <div>
                          {{ item.item.itemName }}
                        </div>
                        <div style="position:relative;width: 60%;">
                          <el-input v-model="addData.needItems[index].value" :placeholder="item.item.placeholder"
                            :onkeyup="item.item.pattern ? `this.value=this.value.replace(${item.item.pattern},'')` : ''"
                            @blur="addData.needItems[index].value = $event.target.value">
                          </el-input>
                          <div class="unit">{{ item.item.itemUnit }}</div>
                        </div>
                        <div class="dunjs"
                          @click="dun_obj = addData.needItems[index]; dunview = true; dun_data.a = 0; dun_data.b = 0;">
                          <img src="../../../../public/imgs/吨位 <EMAIL>">
                          按吨计算
                        </div>
                      </div>
                    </template>
                    <template v-else>
                      <div class="showitem2" :key="index">
                        <div>
                          {{ item.item.itemName }}
                        </div>
                        <div style="position:relative;">
                          <el-input v-model="addData.needItems[index].value" :placeholder="item.item.placeholder"
                            :onkeyup="item.item.pattern ? `this.value=this.value.replace(${item.item.pattern},'')` : ''"
                            @blur="addData.needItems[index].value = $event.target.value">
                          </el-input>
                          <div class="unit">{{ item.item.itemUnit }}</div>
                        </div>
                      </div>
                    </template>
                  </template>
                  <template v-if="item.item.itemType == 'input-select' && item.item.display">
                    <div class="showitem2" style="position:relative;height: 60px;" :key="index">
                      <div>
                        {{ item.item.itemName }}
                      </div>
                      <div>
                        <el-input v-model="addData.needItems[index].value" :placeholder="item.item.placeholder"
                          :onkeyup="item.item.pattern ? `this.value=this.value.replace(${item.item.pattern},'')` : ''"
                          @blur="addData.needItems[index].value = $event.target.value"></el-input>
                        <div class="unit" v-for="(u, ui) in JSON.parse(item.item.itemUnit)" :key="ui" v-if="u.convert == 1">{{
                          u.name }}
                        </div>
                      </div>
                      <div class="hsbox">
                        <div v-for="(u, ui) in JSON.parse(item.item.itemUnit)" :key="ui" v-if="u.convert != 1"
                          style="margin-left: 20px;">
                          换算 <span style="color: #333;">{{ parseFloat((addData.needItems[index].value *
                            u.convert).toFixed(12)) }}</span> {{ u.name }}
                        </div>
                      </div>
                    </div>
                  </template>
                  <!-- 废料抵值 -->
                  <template v-if="item.item.itemType == 'radio' && item.item.display">
                    <div class="showitem2" style="position:relative; width: 100%;text-align: left;" :key="index">
                      <div>
                        {{ item.item.itemName }}
                      </div>
                      <div>
                        <el-radio @input="feiliao = item2.displayItemIds" v-for="(item2, index2) in item.item.values"
                          :key="index2" v-model="addData.needItems[index].value" :label="item2.itemValueId">{{ item2.valueName
                          }}</el-radio>
                      </div>
                    </div>
                  </template>
                  <!-- 废料抵值项 -->
                  <template
                    v-if="item.item.itemType == 'input-radio' && !item.item.display && feiliao.some(id => id === item.item.itemId)">
                    <div class="showitem2" :key="index">
                      <div>
                        {{ item.item.itemName }}
                      </div>
                      <div style="position:relative;">
                        <el-input v-model="addData.needItems[index].value" :placeholder="item.item.placeholder"
                          :onkeyup="item.item.pattern ? `this.value=this.value.replace(${item.item.pattern},'')` : ''"
                          @blur="addData.needItems[index].value = $event.target.value">
                        </el-input>
                        <div class="unit" :style="item.item.itemId == 51 ? 'right: 30px' : ''">{{ item.item.itemUnit }}
                        </div>
                        <div class="tsbox" style="right: 5px;top: 13px" v-if="item.item.itemId == 51">
                          <div class="tsbox2">
                            <div>此处成品重量引用自下方计算结果区净重</div>
                            <div>公式:毛重-净重=废料重</div>
                          </div>

                        </div>
                      </div>
                    </div>
                  </template>
                </template>

                <template v-if="stageitem[tmp_index].stage == 'inner'">
                  <el-form ref="gl_tmp4" :model="gl_tmp4" disabled label-width="5em" class="innerQuote-form">
                    <el-row :gutter="30">
                      <el-col :span="12">
                        <el-form-item label="报价编号">
                          <el-input v-model="gl_tmp4.serial"></el-input>
                        </el-form-item>
                      </el-col>
                      <el-col :span="12">
                        <el-form-item label="报价名称">
                          <el-input v-model="gl_tmp4.name"></el-input>
                        </el-form-item>
                      </el-col>
                      <el-col :span="12">
                        <el-form-item label="总价">
                          <el-input v-model="gl_tmp4.totalPrice">
                            <template slot="prefix">￥</template>
                            <template slot="suffix">元</template>
                          </el-input>
                        </el-form-item>
                      </el-col>
                      <el-col :span="12">
                        <el-form-item label="毛总重">
                          <el-input v-model="gl_tmp4.grossWeight">
                            <template slot="suffix">Kg</template>
                          </el-input>
                        </el-form-item>
                      </el-col>
                      <el-col :span="12">
                        <el-form-item label="净总重">
                          <el-input v-model="gl_tmp4.netWeight">
                            <template slot="suffix">Kg</template>
                          </el-input>
                        </el-form-item>
                      </el-col>
                      <el-col :span="12">
                        <el-form-item label="报价产品">
                          <span class="innerQuote-form-link" @click="drawPreView(gl_tmp4.product)">{{
                            gl_tmp4.product.productName
                            }}</span>
                        </el-form-item>
                      </el-col>
                    </el-row>
                  </el-form>
                </template>

                <div class="btnbox">
                  <div class="btn2" @click="jisuan()" v-if="stageitem[tmp_index].calculate">
                    立 即 计 算
                  </div>
                </div>
              </div>
              <div class="showimg">
                <template v-if="stageitem[tmp_index].dynamicType == 'image'">
                  <img :src="imgPath + stageitem[tmp_index].dynamic">
                </template>
                <template v-else>
                  <div class="showimgremark" v-if="stageitem[tmp_index].stage == 'process'">工艺说明
                    <img class="editicon" @click="dynamicview = true; dynamicData = stageitem[tmp_index]"
                      src="../../../../public/imgs/编辑@2x.png">
                  </div>
                  <div class="showimgremark" v-else-if="stageitem[tmp_index].stage == 'outsourcing'">产品说明
                    <img class="editicon" @click="dynamicview = true; dynamicData = stageitem[tmp_index]"
                      src="../../../../public/imgs/编辑@2x.png">
                  </div>
                  <div class="showimgremark" v-else-if="stageitem[tmp_index].stage == 'outProcess'">加工手段
                    <img class="editicon" @click="dynamicview = true; dynamicData = stageitem[tmp_index]"
                      src="../../../../public/imgs/编辑@2x.png">
                  </div>
                  <div class="showimgremark" v-else>费用说明
                    <img class="editicon" @click="dynamicview = true; dynamicData = stageitem[tmp_index]"
                      src="../../../../public/imgs/编辑@2x.png">
                  </div>
                  <div style="width: 90%;margin:10px auto;font-size: 12px;color: #999;"
                    v-html="stageitem[tmp_index].dynamic"></div>
                </template>
              </div>
            </div>
            <div class="tmpbox-2" v-if="stageitem[tmp_index].stage != 'inner'">
              计算结果
            </div>
            <!-- 计算部分 -->
            <div class="show" style="background: #F1F3F8;"
              v-if="stageitem[tmp_index].items && stageitem[tmp_index].stage != 'inner'">
              <!-- 显示模块内容   -->
              <div class="showitem">
                <!-- 循环result里面的每一项 -->
                <template v-for="(item, index) in stageitem[tmp_index].items.result">
                  <template v-if="item.item.itemType == 'input' && item.item.display">
                    <template v-if="item.itemId == 6 && stageitem[tmp_index].stage == 'material'">
                      <div class="showitem2 showitem100" :key="index">
                        <div>
                          {{ item.item.itemName }}
                        </div>
                        <div style="position:relative;">
                          <el-input v-model="resData.needItems[index].value" :placeholder="item.item.placeholder"
                            :onkeyup="item.item.pattern ? `this.value=this.value.replace(${item.item.pattern},'')` : ''">
                          </el-input>
                          <div class="unit">{{ item.item.itemUnit }}</div>
                          <div class="glbtn" @click="handlePriceOneKey()">
                            一键关联
                          </div>
                        </div>
                        <template v-if="kCheckId">
                          <div v-if="gl_tmp2.product_name" class="glproduct">
                            关联K线 : {{ gl_tmp2.product_name }} / {{ gl_tmp2.specs }} <span v-if="gl_tmp2.freightValue"> /
                              附加运费：{{ gl_tmp2.freightValue }}元</span>
                          </div>
                        </template>
                        <template v-else>
                          <div v-if="gl_tmp2.product_name" class="glproduct" @click="drawPreView(gl_tmp2)">
                            关联产品 : {{ gl_tmp2.product_name }} / {{ gl_tmp2.specs }} <span v-if="gl_tmp2.freightValue"> /
                              附加运费：{{ gl_tmp2.freightValue }}元</span>
                          </div>
                        </template>
                      </div>
                    </template>
                    <template v-else>
                      <div class="showitem2" :key="index">
                        <div>
                          {{ item.item.itemName }}
                        </div>
                        <div style="position: relative;">
                          <el-input v-model="resData.needItems[index].value" :placeholder="item.item.placeholder"
                            :onkeyup="item.item.pattern ? `this.value=this.value.replace(${item.item.pattern},'')` : ''">
                          </el-input>
                          <div class="unit">{{ item.item.itemUnit }}</div>
                        </div>
                      </div>
                    </template>
                  </template>
                  <template v-if="item.item.itemType == 'input-expression' && item.item.display">
                    <div class="showitem2" :key="index">
                      <div>
                        {{ item.item.itemName }}
                      </div>
                      <div style="position: relative;">
                        <el-input v-model="resData.needItems[index].value" :placeholder="item.item.placeholder"
                          :onkeyup="item.item.pattern ? `this.value=this.value.replace(${item.item.pattern},'')` : ''">
                        </el-input>
                        <div class="unit">{{ item.item.itemUnit }}</div>
                      </div>
                    </div>
                  </template>
                  <template v-if="item.item.itemType == 'input-reference' && item.item.display">
                    <div class="showitem2" :key="index">
                      <div>
                        {{ item.item.itemName }}
                      </div>
                      <div style="position: relative;">
                        <el-input v-model="resData.needItems[index].value" :placeholder="item.item.placeholder"
                          :onkeyup="item.item.pattern ? `this.value=this.value.replace(${item.item.pattern},'')` : ''">
                        </el-input>
                        <div class="unit">{{ item.item.itemUnit }}</div>
                      </div>
                    </div>
                  </template>
                </template>
                <div class="btnbox">
                  <div class="btn2" @click="jisuan2()" v-if="stageitem[tmp_index].calculate &&
                    (stageitem[tmp_index].stage === 'material' || stageitem[tmp_index].stage === 'outsourcing')">
                    获 取 小 计
                  </div>
                </div>
              </div>
              <div class="showimg" style="border: 0;">
                <div class="showimgremark">请输入备注</div>
                <el-input type="textarea" v-model="stageitem[tmp_index].remark" placeholder="备注内容"></el-input>
              </div>
            </div>
          </div>
        </el-collapse-transition>
        <div class="btns" v-if="tmp_index != -1">
          <div class="btn" style="right: 10px;" @click="save()">
            {{ savebtn }}
          </div>
        </div>

      </div>
    </div>
    <!-- 所有阶段 -->
    <div class="box3" v-if="list_index != -1 && info.product != null">
      <div class="box3-sizer">
        <el-popover placement="bottom-start" width="150" trigger="click">
          <el-checkbox-group v-model="stageChecked" @change="handleSizer" :min="1">
            <el-checkbox v-for="item in stages" :key="item.stage" :label="item.stage">
              {{ item.name }}
            </el-checkbox>
          </el-checkbox-group>
          <div class="box3-sizer-title pointer" slot="reference">
            <el-tooltip class="item" effect="dark" content="显隐阶段" placement="top-start">
              <span><i class="el-icon-menu"></i>筛选</span>
            </el-tooltip>
          </div>
        </el-popover>
      </div>
      <div v-for="(item, index) in stages" :key="index" @click="gettemp(index, item); cleartmp()"
        :class="stage_index == index ? 'box3-1-sel' : 'box3-1'" v-show="stageChecked.includes(item.stage)">
        <div>
          {{ stage_index == index ? '修改' : '去设置' }}
        </div>
        <div>
          {{ item.name }}
        </div>
      </div>
    </div>
    <!-- 最少供应数量 -->
    <!-- <div class="box4">
      <el-form ref="amountForm" :model="amountForm" label-width="100px" style="width: 100%; margin-top: 20px;">
        <el-row :gutter="30">
          <el-col :span="12">
            <el-form-item label="最少供应量">
              <el-input v-model="amountForm.minQuantity">
                <template slot="suffix">个</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div> -->
    <!-- 总计 -->
    <div class="totalBox" v-if="list_index != -1 && info.product != null && info.totalPrice">
      <div class="totalBox-title">总价</div>
      <div class="totalBox-list">
        <div class="totalBox-item">成本价格：<span class="totalBox-item-price">¥ {{ info.totalPrice }}</span></div>
        <div class="totalBox-item">毛总重 <span>{{ info.grossWeight }}kg</span></div>
        <div class="totalBox-item">净总重 <span>{{ info.netWeight }}kg</span></div>
        <div class="totalBox-item"><el-switch v-model="baseInfo.isIncludingTax" active-text="含税" inactive-text="不含税"
            @change="handleChangeTaxSwitch"></el-switch></div>
        <template v-if="baseInfo.isIncludingTax">
          <div class="totalBox-item">
            含税比例(%)：
            <el-input v-model="baseInfo.taxPercent" size="mini" style="width:80px"
              onkeyup="value=value.replace(/[^\-\d.]/g, '').replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3')"
              @blur="baseInfo.taxPercent = $event.target.value" @input="taxActive = false">
              <span slot="suffix" class="totalBox-item-suffix pointer" :class="{ active: taxActive }"
                @click="handleChangeTax"><i class="el-icon-check"></i></span>
            </el-input>
          </div>
          <div class="totalBox-item">含税价格：<span class="totalBox-item-price">{{ baseInfo.taxPrice ? '¥ ' +
            baseInfo.taxPrice : '--' }}</span></div>
        </template>
      </div>
      <div class="totalBox-btn" :class="{ active: info.fragments && info.product }" @click="bj_fin()">完成报价</div>
    </div>

    <!-- 新增报价view -->
    <el-dialog v-dialogDragBox title="新增报价" :visible.sync="addView" width="30%" :close-on-click-modal="false">
      <el-form ref="bjform" :model="bjadd" :rules="bjRules" label-width="80px" @submit.native.prevent>
        <el-form-item label="报价名称" prop="name">
          <el-input v-model="bjadd.name"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="add()">新 增</el-button>
      </span>
    </el-dialog>

    <!-- 搜索关联产品view -->
    <el-dialog v-dialogDragBox :title="gstypeTitle" :visible.sync="glview" width="70%">
      <el-form ref="form" :label-width="gstype === 'sy' ? '' : '80px'" size="small" :inline="gstype === 'sy'"
        @submit.native.prevent>
        <el-row :gutter="10">
          <el-col :span="24">
            <el-form-item label="产品类型">
              <el-radio-group v-model="gstype" size="normal" @change="handleRadio">
                <el-radio label="sy">私域产品</el-radio>
                <el-radio label="gy">公域收藏</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <template v-if="gstype === 'sy'">
          <el-form-item label="名称" prop="productName">
            <el-input v-model="sglsearch.productName" placeholder="请输入产品名称" clearable
              @keyup.enter.native="sgl()"></el-input>
          </el-form-item>
          <el-form-item label="编码" prop="productCode">
            <el-input v-model="sglsearch.productCode" placeholder="请输入产品名称" clearable
              @keyup.enter.native="sgl()"></el-input>
          </el-form-item>
          <el-form-item label="规格" prop="specs">
            <el-input v-model="sglsearch.specs" placeholder="请输入规格" clearable @keyup.enter.native="sgl()"></el-input>
          </el-form-item>
          <el-form-item label="型号" prop="model">
            <el-input v-model="sglsearch.model" placeholder="请输入型号" clearable @keyup.enter.native="sgl()"></el-input>
          </el-form-item>
          <el-form-item label="表面" prop="surface">
            <el-input v-model="sglsearch.surface" placeholder="请输入表面" clearable @keyup.enter.native="sgl()"></el-input>
          </el-form-item>
          <el-form-item label="材质" prop="materialQuality">
            <el-input v-model="sglsearch.materialQuality" placeholder="请输入材质" clearable
              @keyup.enter.native="sgl()"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="small" @click="sgl()">搜索</el-button>
          </el-form-item>
        </template>
        <el-form-item label="搜索产品" v-if="gstype === 'gy'">
          <el-input v-model="search" style="width: 74%;"></el-input>
          <el-button type="primary" style="width: 20%;margin-left: 6%;" @click="sgl()">搜 索</el-button>
        </el-form-item>
      </el-form>
      <div class="sgoodbox" v-if="glgoods.total" style="border: 1px solid #CBD6E2;border-radius: 5px;">
        <div class="sgoodbox-tbox">
          <div style="width:10%;">序号</div>
          <div style="width:15%;">产品名称</div>
          <div style="width:68px;">图片</div>
          <div style="width:15%;">规格</div>
          <div style="width:15%;">产品编号</div>
          <div style="width:15%;" v-if="gstype === 'sy'">产品报价</div>
          <div style="width:15%;" v-if="gstype === 'gy'">报价历史</div>
          <div style="width:15%;">操作</div>
        </div>

        <div v-for="(item, index) in glgoods.rows" class="sgooditem2" :key="index">
          <div style="width: 10%;">{{ index + 1 }}</div>
          <div style="width: 15%;">
            <el-button size="mini" type="text" @click="drawPreView(item)">{{ item.product_name }}</el-button>
          </div>
          <div style="display: flex;width:68px;">
            <img :src="formatProductImg(item)">
          </div>
          <div style="width:15%;">{{ item.specs }}</div>
          <div style="width:15%;">{{ item.product_code }}</div>
          <div style="width: 15%;color: #EC2454;" v-if="gstype === 'sy'"> {{ item.price ? '¥' + item.price : '--' }}</div>
          <div style="width:15%;" v-if="gstype === 'gy'">
            <el-button size="mini" @click="item.slide = !item.slide">{{ item.slide ? '收起' : '展开' }}</el-button>
          </div>
          <div style="width:15%;">
            <div class="sgooditem2-btn" @click="yjgl(item)">关联该产品</div>
          </div>
          <el-collapse-transition>
            <div class="sgooditem2-2" v-if="item.list && item.slide == true">
              <div class="sgoodbox-tbox">
                <div>序号</div>
                <div>报价名称</div>
                <div>报价编号</div>
                <div>产品报价</div>
                <div>操作</div>
              </div>
              <div v-for="(item2, index2) in item.list" class="sgooditem2-2-1" :key="index2">
                <div>{{ index2 + 1 }}</div>
                <div>{{ item2.name }}</div>
                <div>{{ item2.serial }}</div>
                <div style="color: #EC2454;">¥ {{ item2.price }}</div>
                <div>
                  <div class="sgooditem2-btn2" @click="yjgl(item, item2)">关联该报价</div>
                </div>
              </div>
            </div>
          </el-collapse-transition>
        </div>
      </div>
      <el-pagination v-if="glgoods.total" layout="prev, pager, next" :page-size="10" :current-page.sync="stagePageNum"
        @current-change="sgl($event)" :total="glgoods.total">
      </el-pagination>
    </el-dialog>


    <!-- 关联产品view -->
    <el-dialog v-dialogDragBox title="关联产品" :visible.sync="bindView" width="70%">
      <el-form :model="newsearch" ref="newsearch" size="small" :inline="true" @submit.native.prevent>
        <el-form-item label="名称" prop="productName">
          <el-input v-model="newsearch.productName" placeholder="请输入产品名称" clearable
            @keyup.enter.native="sgood()"></el-input>
        </el-form-item>
        <el-form-item label="编码" prop="productCode">
          <el-input v-model="newsearch.productCode" placeholder="请输入产品名称" clearable
            @keyup.enter.native="sgood()"></el-input>
        </el-form-item>
        <el-form-item label="规格" prop="specs">
          <el-input v-model="newsearch.specs" placeholder="请输入规格" clearable @keyup.enter.native="sgood()"></el-input>
        </el-form-item>
        <el-form-item label="型号" prop="model">
          <el-input v-model="newsearch.model" placeholder="请输入型号" clearable @keyup.enter.native="sgood()"></el-input>
        </el-form-item>
        <el-form-item label="表面" prop="surface">
          <el-input v-model="newsearch.surface" placeholder="请输入表面" clearable @keyup.enter.native="sgood()"></el-input>
        </el-form-item>
        <el-form-item label="材质" prop="materialQuality">
          <el-input v-model="newsearch.materialQuality" placeholder="请输入材质" clearable
            @keyup.enter.native="sgood()"></el-input>
        </el-form-item>
        <el-form-item label="来源">
          <el-select v-model="newsearch.source" placeholder="请选择产品来源" @change="sgood()" clearable>
            <el-option label="公域产品" value="common"></el-option>
            <el-option label="私域产品" value="private"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="small" @click="sgood()">搜索</el-button>
        </el-form-item>
      </el-form>

      <el-collapse-transition>
        <div class="sgoodbox" v-if="sgoods.length">
          <el-table ref="sgoods" stripe :data="sgoods" height="600" :row-key="getRowKey" style="width: 100%"
            class="custom-table custom-table-cell5 custom-table-filter">
            <el-table-column align="center" type="index" label="序号"></el-table-column>
            <el-table-column align="center" prop="product_name" label="产品名称" show-overflow-tooltip min-width="130"
              :filters="nameFilters" :filter-method="filterName">
              <template slot-scope="{ row }">
                <span class="table-link" @click="drawPreView(row)">
                  <span style="color:#2E73F3;">{{ row.source === 'common' ? '(公域)' : '' }}</span>
                  <span style="color:#FE7F22;">{{ row.source === 'private' ? '(私域)' : '' }}</span>
                  <span>{{ row.source === 'add' ? '(自定义)' : '' }}</span>
                  {{ row.product_name }}
                </span>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="picture1" label="图片" width="75">
              <template slot-scope="{ row }">
                <el-image :src="formatProductImg(row)" fit="cover" @click="handleImgView(row)">
                  <div slot="error" class="image-slot">
                    <i class="el-icon-picture-outline"></i>
                  </div>
                </el-image>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="specs" label="规格" show-overflow-tooltip :filters="specsFilters"
              :filter-method="filterSpecs"></el-table-column>
            <el-table-column align="center" prop="model" label="型号" show-overflow-tooltip :filters="modelFilters"
              :filter-method="filterModel"></el-table-column>
            <el-table-column align="center" prop="product_code" label="产品编码" show-overflow-tooltip></el-table-column>
            <el-table-column align="center" prop="material_quality" label="材质" show-overflow-tooltip
              :filters="materialFilter" :filter-method="filterQuality"></el-table-column>
            <el-table-column align="center" prop="surface" label="表面" show-overflow-tooltip :filters="surfaceFilters"
              :filter-method="filterSurface"></el-table-column>
            <el-table-column align="center" prop="unit" label="单位" width="60" :filters="unitFilters"
              :filter-method="filterUnit"></el-table-column>
            <el-table-column align="center" label="操作">
              <template slot-scope="{ row }">
                <el-button type="text" size="mini" icon="el-icon-check" @click="gl(row)">关联</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-collapse-transition>
      <span slot="footer" class="dialog-footer">
        <el-button type="default" @click="addGoodView = true;">新增产品</el-button>
      </span>
    </el-dialog>

    <!-- 新增产品view -->
    <el-dialog v-dialogDragBox title="新增产品" :visible.sync="addGoodView" width="50%">
      <el-form ref="form" label-width="80px">
        <el-row>
          <el-col :span="8">
            <el-form-item label="产品名称">
              <el-input v-model="addGoodData.productName"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="编码">
              <el-input v-model="addGoodData.productCode"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="型号">
              <el-input v-model="addGoodData.model"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="规格">
              <el-input v-model="addGoodData.specs"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="属性">
              <el-input v-model="addGoodData.attribute"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="材质">
              <el-input v-model="addGoodData.materialQuality"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="重量">
              <el-input v-model="addGoodData.weight"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="单位">
              <el-input v-model="addGoodData.unit"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="表面">
              <el-input v-model="addGoodData.surface"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="产品图片">
              <image-upload v-model="addGoodData.picture" :limit="1" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="三维图片">
              <image-upload v-model="addGoodData.diagram" :limit="1" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="图纸">
              <image-upload v-model="addGoodData.draw" :limit="1" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="检测报告">
              <image-upload v-model="addGoodData.report" :limit="1" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="addgood()">新 增 产 品</el-button>
      </span>
    </el-dialog>

    <!-- 修改报价信息 -->
    <el-dialog v-dialogDragBox title="修改报价" :visible.sync="ediinfoview" width="30%">
      <el-form ref="form" label-width="80px">
        <el-form-item label="报价名称">
          <el-input v-model="ediInfo.name" style="width: 74%;"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="danger" @click="quotadel()">删除该报价</el-button>
        <el-button type="primary" @click="quotaedi()">修改</el-button>
      </span>
    </el-dialog>

    <!-- 新增阶段模板 -->
    <el-dialog v-dialogDragBox title="新增模板" :visible.sync="addtmpview" width="60%">
      <!-- 所有字段 -->
      <div class="zdbox">
        <div>请选择您需要添加的生产材料属性字段，选中后将出现在下方字段列表</div>
        <div class="zditems">
          <div v-for="(item, index) in zdlist.toBeSelect" style="position: relative;"
            :style="{ backgroundColor: item.istrue ? '#E0EBFF' : '', border: item.istrue ? '1px solid #2E73F3' : '', color: item.istrue ? '#2E73F3' : '' }" :key="index">
            <img @click="delzd(item.item)" v-if="item.item.canModify" src="../../../../public/imgs/取消 <EMAIL>"
              style="position: absolute;right: -10px;top:-5px;width: 20px;height: 20px;z-index: 99;">
            <div @click="addtmpitem(item, index)">{{ item.item.itemName }}</div>
          </div>
        </div>
        <div class="zditemsadd">
          <div><input placeholder="请输入字段名" v-model="addzdData.itemName"></div>
          <div><input placeholder="请输入单位" v-model="addzdData.itemUnit"></div>
          <div @click="addzd()">新增字段</div>
        </div>
      </div>
      <div style="margin: 20px auto;width: 95%; display: flex;flex-wrap: wrap;justify-content: space-between;">
        <div class="showitem3">
          <div style="color: #333;font-weight: bold;">
            新增名称
          </div>
          <div style="position:relative;">
            <el-input v-model="addtmpData.name">
            </el-input>
          </div>
        </div>
        <div class="showitem3" style="height: 100%;">
          <template v-if="stage_index != -1 && stages[stage_index].dynamicType != 'image'">
            <div>
              说明
            </div>
            <div style="position:relative;">
              <el-input type="textarea" :autosize="{ minRows: 3, maxRows: 6 }" placeholder="请输入内容"
                v-model="addtmpData.dynamic">
              </el-input>
            </div>
          </template>
          <template v-else>
            <div>
              图片
            </div>
            <div style="position:relative;">
              <image-upload v-model="addtmpData.dynamic" :limit="1" />
            </div>
          </template>
        </div>

        <div class="showitem3-title">显示区</div>

        <!-- 默认值 -->
        <template v-for="(item, index) in zdlist.show">
          <template v-if="item.item.itemType == 'input'">
            <div class="showitem3" :key="index">
              <div> {{ item.item.itemName }}</div>
              <div style="position:relative;">
                <el-input placeholder="默认" disabled>
                </el-input>
                <div class="unit">{{ item.item.itemUnit }}</div>
              </div>
            </div>
          </template>
          <template v-if="item.item.itemType == 'input-expression'">
            <div class="showitem3" :key="index">
              <div> {{ item.item.itemName }}</div>
              <div style="position:relative;">
                <el-input placeholder="默认" disabled>
                </el-input>
                <div class="unit">{{ item.item.itemUnit }}</div>
              </div>
            </div>
          </template>
          <template v-if="item.item.itemType == 'input-reference'">
            <div class="showitem3" :key="index">
              <div> {{ item.item.itemName }}</div>
              <div style="position:relative;">
                <el-input placeholder="默认" disabled>
                </el-input>
                <div class="unit">{{ item.item.itemUnit }}</div>
              </div>
            </div>
          </template>
          <template v-if="item.item.itemType == 'input-select'">
            <div class="showitem3" style="position:relative;" :key="index">
              <div>{{ item.item.itemName }}</div>
              <div>
                <el-input placeholder="默认" disabled> </el-input>
                <div class="unit" v-for="(u, ui) in JSON.parse(item.item.itemUnit)" :key="ui" v-if="u.convert == 1">{{ u.name }}
                </div>
              </div>
            </div>
          </template>
          <template v-if="item.item.itemType == 'radio'">
            <div class="showitem3" style="position:relative; width: 100%;text-align: left;justify-content:start" :key="index">
              <div style="width:10%">{{ item.item.itemName }}</div>
              <div>
                <el-radio disabled v-for="ite in item.item.values" :key="ite.itemValueId" :label="ite.itemValueId">{{
                  ite.valueName }}</el-radio>
              </div>
            </div>
          </template>
        </template>

        <template v-for="(item, index) in addtmpData.templateItems">

          <template v-if="item.itemType == 'input'">
            <div class="showitem3" :key="index">
              <div>
                <img v-if="item.def != true" @click="qxzd(index, item)" style="position: absolute;left: 0;top:12px;"
                  src="../../../../public/imgs/删除 (1) 1.png">
                {{ item.itemName }}
              </div>
              <div style="position:relative;">
                <el-input placeholder="参考框" disabled>
                </el-input>
                <div class="unit">{{ item.itemUnit }}</div>
              </div>
            </div>
          </template>
          <template v-if="item.itemType == 'input-radio'">
            <div class="showitem3" :key="index">
              <div>
                <img v-if="item.def != true" @click="qxzd(index, item)" style="position: absolute;left: 0;top:12px;"
                  src="../../../../public/imgs/删除 (1) 1.png">
                {{ item.itemName }}
              </div>
              <div style="position:relative;">
                <el-input placeholder="参考框" disabled>
                </el-input>
                <div class="unit">{{ item.itemUnit }}</div>
              </div>
            </div>
          </template>
          <template v-if="item.itemType == 'radio'">
            <div class="showitem3" :key="index">
              <div style="width:20%">
                <img v-if="item.def != true" @click="qxzd(index, item)" style="position: absolute;left: 0;top:12px;"
                  src="../../../../public/imgs/删除 (1) 1.png">
                {{ item.itemName }}
              </div>
              <div style="position:relative;width:80%">
                <el-radio-group>
                  <el-radio v-for="ite in item.values" :key="ite.iteValueId" :label="ite.valueName" disabled>
                    {{ ite.valueName }}
                  </el-radio>
                </el-radio-group>
              </div>
            </div>
          </template>
          <template v-if="item.itemType == 'input-expression'">
            <div class="showitem3" :key="index">
              <div>
                <img v-if="item.def != true" @click="qxzd(index, item)" style="position: absolute;left: 0;top:12px;"
                  src="../../../../public/imgs/删除 (1) 1.png">
                {{ item.itemName }}
              </div>
              <div style="position:relative;">
                <el-input placeholder="参考框" disabled>
                </el-input>
                <div class="unit">{{ item.itemUnit }}</div>
              </div>
            </div>
          </template>
          <template v-if="item.itemType == 'input-reference'">
            <div class="showitem3" :key="index">
              <div>
                <img v-if="item.def != true" @click="qxzd(index, item)" style="position: absolute;left: 0;top:12px;"
                  src="../../../../public/imgs/删除 (1) 1.png">
                {{ item.itemName }}
              </div>
              <div style="position:relative;">
                <el-input placeholder="参考框" disabled>
                </el-input>
                <div class="unit">{{ item.itemUnit }}</div>
              </div>
            </div>
          </template>
          <template v-if="item.itemType == 'input-select'">
            <div class="showitem3" style="position:relative;" :key="index">
              <div>
                <img v-if="item.def != true" @click="qxzd(index, item)" style="position: absolute;left: 0;top:12px;"
                  src="../../../../public/imgs/删除 (1) 1.png">
                {{ item.itemName }}
              </div>
              <div>
                <el-input placeholder="参考框" disabled> </el-input>
                <div class="unit" v-for="(u, ui) in JSON.parse(item.itemUnit)" v-if="u.convert == 1">{{ u.name }}</div>
              </div>
            </div>
          </template>
          <template v-if="item.itemType == 'select'">
            <div class="showitem3" style="position:relative;" :key="index">
              <div>
                <img v-if="item.def != true" @click="qxzd(index, item)" style="position: absolute;left: 0;top:12px;"
                  src="../../../../public/imgs/删除 (1) 1.png">
                {{ item.itemName }}
              </div>
              <div>
                <el-select v-model="item.defaultValue" disabled :placeholder="`请选择${item.itemName}`" style="width:100%">
                  <el-option v-for="ite in item.values" :key="ite.itemValueId" :label="ite.valueName"
                    :value="ite.itemValueId">
                  </el-option>
                </el-select>
              </div>
            </div>
          </template>
        </template>

        <div class="showitem3-title">计算区</div>

        <!-- 默认值 -->
        <template v-for="(item, index) in zdlist.result">
          <template v-if="item.item.itemType == 'input'">
            <div class="showitem3" :key="index">
              <div> {{ item.item.itemName }}</div>
              <div style="position:relative;">
                <el-input placeholder="默认" disabled>
                </el-input>
                <div class="unit">{{ item.item.itemUnit }}</div>
              </div>
            </div>
          </template>
          <template v-if="item.item.itemType == 'input-expression'">
            <div class="showitem3" :key="index">
              <div> {{ item.item.itemName }}</div>
              <div style="position:relative;">
                <el-input placeholder="默认" disabled>
                </el-input>
                <div class="unit">{{ item.item.itemUnit }}</div>
              </div>
            </div>
          </template>
          <template v-if="item.item.itemType == 'input-reference'">
            <div class="showitem3" :key="index">
              <div> {{ item.item.itemName }}</div>
              <div style="position:relative;">
                <el-input placeholder="默认" disabled>
                </el-input>
                <div class="unit">{{ item.item.itemUnit }}</div>
              </div>
            </div>
          </template>
          <template v-if="item.item.itemType == 'input-select'">
            <div class="showitem3" style="position:relative;" :key="index">
              <div>{{ item.item.itemName }}</div>
              <div>
                <el-input placeholder="默认" disabled> </el-input>
                <div class="unit" v-for="(u, ui) in JSON.parse(item.item.itemUnit)" :key="ui" v-if="u.convert == 1">{{ u.name }}
                </div>
              </div>
            </div>
          </template>
        </template>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="addtmp()">新 建 </el-button>
      </span>
    </el-dialog>

    <!-- 预览 -->
    <el-dialog v-dialogDragBox title="图纸" :visible.sync="pdfshow" width="900px" append-to-body style="z-index:999;"
      class="disable-context-menu">
      <iframe :src="pdfUrl + '?page=hsn#toolbar=0'" style="width:100%;height: 500px" frameborder="0" id="ifm"></iframe>
    </el-dialog>

    <!-- 计算器 -->
    <jisuan></jisuan>

    <!-- 选择阶段模板提示 -->
    <div v-if="list_index == -1" style="border: 0px solid;position: absolute;left: 50%;top:300px;">
      <el-empty :description="!list.length ? '请先点击新增报价' : '请先选择上方报价'"></el-empty>
    </div>

    <!-- 按吨计算view -->
    <el-dialog v-dialogDragBox title="按吨计算" :visible.sync="dunview" width="30%">
      <div>
        <div>总价/吨</div>
        <div style="margin-top:5px;">
          <el-input v-model="dun_data.a" placeholder="请输入每吨价格">
          </el-input>
        </div>
      </div>
      <div style="margin-top:20px;">
        <div>单重(kg)</div>
        <div style="margin-top:5px;">
          <el-input v-model="dun_data.b" placeholder="请输入单重(kg)">
          </el-input>
        </div>
      </div>
      <div style="margin-top:20px;" v-if="dun_obj">
        <div>计算结果</div>
        <div style="margin-top:5px;">
          {{ parseFloat((dun_data.a / 1000 * dun_data.b).toFixed(5)) }}元
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="dun_js()">写入计算结果</el-button>
      </span>
    </el-dialog>

    <!-- k线关联 -->
    <el-dialog v-dialogDragBox :title="gstypeTitle" :visible.sync="kview" width="70%">
      <el-form ref="form" size="small" :inline="true" @submit.native.prevent>
        <el-row :gutter="10">
          <el-col :span="24">
            <el-form-item label="产品类型">
              <el-radio-group v-model="gstype" size="normal" @change="handleRadio">
                <el-radio label="sy">私域产品</el-radio>
                <el-radio label="kline">k线</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <template v-if="gstype === 'sy'">
          <el-form-item label="名称" prop="productName">
            <el-input v-model="sglsearch.productName" placeholder="请输入产品名称" clearable
              @keyup.enter.native="sgl()"></el-input>
          </el-form-item>
          <el-form-item label="编码" prop="productCode">
            <el-input v-model="sglsearch.productCode" placeholder="请输入产品名称" clearable
              @keyup.enter.native="sgl()"></el-input>
          </el-form-item>
          <el-form-item label="规格" prop="specs">
            <el-input v-model="sglsearch.specs" placeholder="请输入规格" clearable @keyup.enter.native="sgl()"></el-input>
          </el-form-item>
          <el-form-item label="型号" prop="model">
            <el-input v-model="sglsearch.model" placeholder="请输入型号" clearable @keyup.enter.native="sgl()"></el-input>
          </el-form-item>
          <el-form-item label="表面" prop="surface">
            <el-input v-model="sglsearch.surface" placeholder="请输入表面" clearable @keyup.enter.native="sgl()"></el-input>
          </el-form-item>
          <el-form-item label="材质" prop="materialQuality">
            <el-input v-model="sglsearch.materialQuality" placeholder="请输入材质" clearable
              @keyup.enter.native="sgl()"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="small" @click="sgl()">搜索</el-button>
          </el-form-item>
        </template>
      </el-form>
      <div class="sgoodbox" v-if="glgoods.total" style="border: 1px solid #CBD6E2;border-radius: 5px;">
        <div class="sgoodbox-tbox">
          <div style="width:10%;">序号</div>
          <div style="width:15%;">产品名称</div>
          <div style="width: 68px;">图片</div>
          <div style="width:15%;">规格</div>
          <div style="width:15%;">产品编号</div>
          <div style="width:15%;">产品报价</div>
          <div style="width:15%;">操作</div>
        </div>
        <div v-for="(item, index) in glgoods.rows" class="sgooditem2" :key="index">
          <div style="width: 10%;">{{ index + 1 }}</div>
          <div style="width: 15%;">
            <el-button size="mini" type="text" @click="drawPreView(item)">{{ item.product_name }}</el-button>
          </div>
          <div style="display: flex;width:68px;">
            <img :src="formatProductImg(item)">
          </div>
          <div style="width:15%;">{{ item.specs }}</div>
          <div style="width:15%;">{{ item.product_code }}</div>
          <div style="width: 15%;color: #EC2454;"> {{ item.price ? '¥' + item.price : '--' }}</div>
          <!-- <div style="width:15%;">
            <el-button size="mini" @click="item.slide=!item.slide">{{item.slide?'收起':'展开'}}</el-button>
          </div> -->
          <div style="width: 15%;">
            <div class="sgooditem2-btn" @click="glkline(item, item, 'a')">关联该产品</div>
          </div>
          <el-collapse-transition>
            <div class="sgooditem2-2" v-if="item.list && item.slide == true">
              <div class="sgoodbox-tbox">
                <div>序号</div>
                <div>报价名称</div>
                <div>报价编号</div>
                <div>产品报价</div>
                <div>操作</div>
              </div>
              <div v-for="(item2, index2) in item.list" class="sgooditem2-2-1" :key="index2">
                <div>{{ index2 + 1 }}</div>
                <div>{{ item2.name }}</div>
                <div>{{ item2.serial }}</div>
                <div style="color: #EC2454;">¥ {{ item2.price }}</div>
                <div>
                  <div class="sgooditem2-btn2" @click="glkline(item, item2, 'b')">关联该报价</div>
                </div>
              </div>
            </div>
          </el-collapse-transition>
        </div>
      </div>
      <!-- k线产品 -->
      <div class="kbox" v-if="kData.length != 0">
        <div class="sgoodbox-tbox">
          <div>序号</div>
          <div>钢厂</div>
          <div>产品</div>
          <div>价格</div>
          <div>日期</div>
          <div>操作</div>
        </div>
        <div v-for="(item, index) in kData" class="sgooditem2-2-1" :class="{ 'sgooditem2-check': item.id === kCheckId }" :key="index">
          <div>{{ index + 1 }}</div>
          <div>{{ item.plantName }}</div>
          <div>{{ item.typeName }}</div>
          <div style="color: #EC2454;">¥ {{ item.price }}</div>
          <div>{{ item.date }}</div>
          <div>
            <div class="sgooditem2-btn2" @click="kCheckId = item.id; kCheckData = item">
              {{ kCheckId === item.id ? '已关联该K线' : '关联该K线' }}
            </div>
          </div>
        </div>
      </div>
      <div class="kbox-input" v-if="gstype === 'kline'">
        附加运费：
        <el-input v-model="freightValue" :disabled="!kCheckId" :placeholder="kCheckId ? '请输入附加运费' : '请先关联K线'"
          style="width:150px"
          onkeyup="value=value.replace(/[^\-\d.]/g, '').replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3')"
          @blur="freightValue = $event.target.value"><span slot="suffix">(元)</span></el-input>
        <el-button type="primary" :disabled="!kCheckId" @click="glkline(kCheckData, kCheckData, 'c')">确定</el-button>
      </div>
      <el-pagination v-if="glgoods.total" layout="prev, pager, next" :page-size="10" :current-page.sync="stagePageNum"
        @current-change="sgl($event)" :total="glgoods.total">
      </el-pagination>
    </el-dialog>

    <!--新建阶段元素 -->
    <el-dialog v-dialogDragBox title="创建报价元素" :visible.sync="gljgview" width="40%">
      <el-form ref="form" :model="ysdata" label-width="80px">
        <el-form-item label="材料名称">
          <el-input v-model="ysdata.name"></el-input>
        </el-form-item>
        <el-form-item label="价格">
          <el-input v-model="ysdata.price"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="ysadd()">创建报价元素</el-button>
      </span>
    </el-dialog>

    <!--修改描述模板 -->
    <el-dialog v-dialogDragBox title="修改内容" :visible.sync="dynamicview" width="40%">
      <el-form ref="form" :model="dynamicData" label-width="80px">
        <el-form-item label="新的内容">
          <el-input type="textarea" :autosize="{ minRows: 3, maxRows: 6 }" placeholder="请输入内容"
            v-model="dynamicData.dynamic">
          </el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="dynamicedi()">修改描述</el-button>
      </span>
    </el-dialog>

    <!-- 产品信息 -->
    <product :visible.sync="productView" :info="productData"></product>
    <!-- <div style="color: red;width: 100%;">
      {{ addData }}
    </div>
    <div style="color: green;width: 100%;">
      {{ resData }}
    </div>
    <div style="color: pink;width: 100%;">
      {{ ys_is_bj }}
    </div> -->


    <product-dialog ref="productInfo"></product-dialog>

    <!-- 收藏夹管理 -->
    <collect-dialog ref="collect" @getCollectList="getCollectList" @colletSubmit="handleColletSubmit"></collect-dialog>

    <!-- 关联二级报价 -->
    <el-dialog v-dialogDragBox title="关联二级报价" :visible.sync="listView" width="70%" class="custom-dialog">
      <div style="padding:0 20px">
        <el-form :model="listQuery" ref="listQueryForm" size="small" :inline="true" @submit.native.prevent>
          <el-form-item label="">
            <el-input v-model="listQuery.name" placeholder="请输入报价名称" clearable @keyup.enter.native="handleOneKeyQuery"
              size="small"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="small" @click="handleOneKeyQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="small" @click="handleOneKeyResetQuery">重置</el-button>
          </el-form-item>
        </el-form>
        <el-table :data="listData" style="width: 100%" stripe class="custom-table">
          <el-table-column label="序号" type="index" align="center"></el-table-column>
          <el-table-column label="报价编号" prop="serial" align="center"></el-table-column>
          <el-table-column label="报价名称" prop="name" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column label="总价" prop="totalPrice" align="center">
            <template slot-scope="{ row }">
              <b style="font-size: 14px; color: #ec2454" class="pointer">{{ '¥' + row.totalPrice }}</b>
            </template>
          </el-table-column>
          <el-table-column label="毛总重" prop="grossWeight" align="center">
            <template slot-scope="{ row }">{{ row.grossWeight + 'kg' }}</template>
          </el-table-column>
          <el-table-column label="净总重" prop="netWeight" align="center">
            <template slot-scope="{ row }">{{ row.netWeight + 'kg' }}</template>
          </el-table-column>
          <el-table-column label="报价产品" prop="productName" align="center" show-overflow-tooltip>
            <template slot-scope="{ row }">
              <span class="table-link" v-if="row.productId" @click="drawPreView(row.product)">{{ row.product.productName
                }}</span>
              <span v-else></span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center">
            <template slot-scope="{ row }">
              <button type="button" class="table-btn primary" @click="handleOneKeyListSubmit(row)">关联该报价</button>
            </template>
          </el-table-column>
        </el-table>
        <div class="custom-pagination">
          <pagination v-show="listTotal > 0" :total="listTotal" :page.sync="listQuery.pageNum"
            :limit.sync="listQuery.pageSize" @pagination="getListData" />
        </div>
      </div>
      <div slot="footer">
        <button type="button" class="custom-dialog-btn primary" @click="listView = false">关闭</button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import * as ff from "@/api/houtai/formula";
import * as sc from "@/api/houtai/shoucang";
import * as sy from "@/api/system/privateduct";
import jisuan from './jisuan'
import product from './product.vue';
import ProductDialog from '@/views/public/product/dialog'
import collectDialog from './collect';
export default {
  name: 'Addition',
  components: {
    jisuan, product, ProductDialog, collectDialog
  },
  data() {
    return {
      productView: false, //产品组件显示隐藏
      productData: {},     //产品组件数据
      dynamicview: false,
      dynamicData: {},
      kview: false, //k线窗口
      gl_tmp: {}, //关联临时数据
      gl_tmp2: {}, //关联临时数据
      gl_tmp3: {}, //关联临时数据
      gl_tmp4: {}, //关联临时数据
      feiliao: [], //废料radio item
      feiliao2: [],
      gstype: "sy", //公式产品类型
      //吨计算数据
      dun_obj: "",
      dunview: false,
      dun_data: {
        a: 0,
        b: 0
      },
      glview: false,                 //一键关联界面
      glgoods: [],                   //一键关联产品
      addzdData: {},                 //新增字段数据
      zdlist: [],                    //所有字段
      addtmpData: {
        templateItems: []
      },                            //新增模板选项
      addtmpview: false,             //新增阶段模板窗口
      pd_id: -1,                     //选中的片段id
      ediinfoview: false,            //修改信息窗口
      pdfshow: false,                //图纸
      pdfUrl: 'https://example.com/path/to/your/pdf/file.pdf',
      info: "",                      //报价详情
      goods: [],                     //产品列表
      bindView: false,               //关联产品窗口
      addGoodView: false,            //新增产品窗口
      addGoodData: {},               //新增产品数据
      search: "",                    //搜索产品关键字
      sgoods: [],                    //搜索到的产品列表
      addView: false,                //新增报价窗口
      bjadd: {},                     //新增报价数据
      bjRules: {
        name: [{ required: true, message: '请输入报价名称', trigger: 'blur' }, { max: 16, message: '请输入16个字符以内', trigger: 'blur' }]
      },
      stages: [],                    //存右侧所有阶段列表
      stage_index: -1,               //默认选择的下标
      stageitem: [],                 //存所有阶段模板内容
      tmp_index: -1,                 //存模板的下标
      list: [],                      //报价计算列表
      list_index: -1,                //报价计算下标
      addData: {},                   //要新增的数据
      resData: {},                   //返回计算结果数据
      oksave: true,                  //按钮倒计时
      savebtn: "保存设置",            //按钮文本
      f_id: -1,                      //历史阶段id,切换使用
      ediInfo: {},                   //修改窗口信息
      gljgview: false,               //创建报价元素
      ysdata: {},                    //新建元素数据
      ys_is_bj: false,               //是报价不是产品
      kData: [],                     //K线数据
      stageChecked: [],
      stagePageNum: 1,
      editData: {},
      isEdit: false,
      editStage: undefined,
      gstypeTitle: undefined,
      freightOpen: true,
      freightValue: undefined,
      baseInfo: {}, // 报价基础信息
      kCheckId: undefined,
      kCheckData: {},
      taxActive: false,
      showMore: false,
      collectList: [],
      collectId: undefined,
      collectstageitem: [],
      visible: false,
      top: 0,
      left: 0,
      selectedTag: {},
      // 二级报价
      listView: false,
      listData: [],
      listQuery: {
        pageNum: 1,
        pageSize: 10,
        isEditing: false,
        isInner: true,
        name: undefined
      },
      listTotal: 0,
      nameFilters: [],
      specsFilters: [],
      materialFilter: [],
      modelFilters: [],
      surfaceFilters: [],
      unitFilters: [],
      newsearch: {
        productName: undefined,
        productCode: undefined,
        specs: undefined,
        model: undefined,
        surface: undefined,
        materialQuality: undefined,
        source: 'common'
      },
      sglsearch: {
        productName: undefined,
        productCode: undefined,
        specs: undefined,
        model: undefined,
        surface: undefined,
        materialQuality: undefined,
      },
      // 最少供应数量
      amountForm: {
        amount: 0
      }
    };
  },
  watch: {
    visible(value) {
      if (value) {
        document.body.addEventListener('click', this.closeMenu)
      } else {
        document.body.removeEventListener('click', this.closeMenu)
      }
    }
  },
  mounted() {
    const id = this.$route.query.id;
    //获取默认产品
    ff.search().then((res) => {
      const name = this.uniqueJsonArrByField(res.data, 'product_name')
      const specs = this.uniqueJsonArrByField(res.data, 'specs')
      const model = this.uniqueJsonArrByField(res.data, 'model')
      const surface = this.uniqueJsonArrByField(res.data, 'surface')
      const unit = this.uniqueJsonArrByField(res.data, 'unit')
      const material = this.uniqueJsonArrByField(res.data, 'material_quality')
      this.specsFilters = specs.map(item => {
        return { text: item.specs, value: item.specs }
      })
      this.materialFilter = material.map(item => {
        return { text: item.material_quality, value: item.material_quality }
      })
      this.nameFilters = name.map(item => {
        return { text: item.product_name, value: item.product_name }
      })
      this.modelFilters = model.map(item => {
        return { text: item.model, value: item.model }
      })
      this.surfaceFilters = surface.map(item => {
        return { text: item.surface, value: item.surface }
      })
      this.unitFilters = unit.map(item => {
        return { text: item.unit, value: item.unit }
      })
      this.sgoods = res.data;
    });
    //所有阶段
    ff.stage().then((res) => {
      this.stages = res.data;
      const local = localStorage.getItem('stageChecked')
      const arr = local ? local.split(',') : []
      this.stageChecked = arr.length ? arr : res.data.map(item => item.stage)
    });
    //报价计算列表
    ff.list().then((res) => {
      this.list = res.rows;
      const idx = res.rows.findIndex(item => item.id == id)
      if (idx != -1) {
        this.listinfo(idx, this.list[idx]);
      } else {
        if (res.rows.length) this.listinfo(0, this.list[0]);
      }
    });

  },
  methods: {
    //修改描述
    dynamicedi() {
      let that = this;
      that.dynamicData.dynamic = that.dynamicData.dynamic.replaceAll('\n', '<br/>');
      ff.dynamic({
        dynamic: that.dynamicData.dynamic,
        templateId: that.dynamicData.templateId,
      }).then((res) => {
        this.$message.success("修改成功");
        this.dynamicview = false;
      });
    },
    // 按吨计算
    dun_js() {
      let that = this;
      that.dun_obj.value = parseFloat((that.dun_data.a / 1000 * that.dun_data.b).toFixed(5)); // 保留三位小数
      that.dunview = false;
    },
    // 一键关联产品
    handleOneKey() {
      this.glview = true
      this.gstype = 'sy'
      this.search = ''
      this.sgl(1)
      this.stagePageNum = 1
    },
    // 一键关联价格
    handlePriceOneKey() {
      this.kview = true;
      this.gstype = 'sy'
      this.sglsearch = {
        productName: undefined,
        productCode: undefined,
        specs: undefined,
        model: undefined,
        surface: undefined,
        materialQuality: undefined,
      }
      this.sgl(1)
      this.stagePageNum = 1
    },
    // 改变私域、公域
    handleRadio(e) {
      this.gstypeTitle = e === 'sy' ? '关联私域产品' : e === 'gy' ? '关联公域收藏' : '关联K线'
      this.gstype = e
      this.sgl(1)
      this.stagePageNum = 1
      this.freightOpen = true
      this.freightValue = undefined
      this.kCheckId = undefined
      this.kCheckData = {}
    },
    // 点击片段查看详情
    handleView(obj) {
      this.f_id = this.f_id == obj.fragmentId ? -1 : obj.fragmentId
      this.pd_id = -1;
      this.isEdit = false
      this.editStage = undefined
      this.editData = { ...obj }
      this.gl_tmp2 = {}
      this.gl_tmp3 = {}
      this.gl_tmp4 = {}
    },
    // 点击修改
    handleEdit(obj) {
      this.pd_id = obj.fragmentId
      this.editStage = obj.stage
      this.gl_tmp2 = {}
      this.gl_tmp3 = {}
      this.gl_tmp4 = {}
      this.isEdit = true
    },
    //搜索关联
    async sgl(pageNumber = 1) {
      let that = this;
      that.stagePageNum = pageNumber
      that.kData = [];
      that.glgoods = [];
      // 搜索公域
      if (that.gstype === 'gy') {
        const res = await sc.gylist({
          keyword: that.search,
          storeType: 'UserProduct',
          pageSize: 10,
          pageNum: pageNumber
        });

        if (res.rows.length == 0) {
          that.$message("暂无产品");
        } else {
          await Promise.all(res.rows.map(async (row) => {
            row.list = [];
            const gygsRes = await ff.gygs({ productId: row.id });
            row.list = gygsRes.data.quotes;
          }));
        }
        that.glgoods = res;
      } else if (that.gstype === 'sy') {   // 搜索私域
        const res = await sy.listPrivateduct2({
          ...that.sglsearch,
          pageSize: 10,
          pageNum: pageNumber
        });

        if (res.rows.length == 0) {
          that.$message("暂无产品");
        }
        await Promise.all(res.rows.map(async (row) => {
          row.list = [];

          const stage = that.editStage ? that.editStage : that.stages[that.stage_index].stage
          const gygsRes = await ff.reference_list({ productId: row.id, stage });
          row.list = gygsRes.data.quotes;
          row.price = gygsRes.data.price
          row.priceId = gygsRes.data.priceId

        }));
        that.glgoods = res;
      }
      else if (that.gstype === 'kline') {
        ff.k_s().then((res) => {
          that.kData = res.data;
        });
      }

      if (that.glgoods.rows) {
        for (var x = 0; x < that.glgoods.rows.length; x++) {
          that.$set(that.glgoods.rows[x], 'slide', false);
        }
      }
    },
    //一键关联搜索
    yjgl(e, e2 = "") {
      let that = this;
      that.gl_tmp = e;
      let data = e;
      let array_data
      if (that.isEdit) {
        array_data = that.editData.fragmentItems.show
        // 遍历数组数据，查找匹配的项
        array_data.map(item => {
          if (data[item.matchWord]) item.value = data[item.matchWord]
        })
        const productdata = { product: { productName: e.product_name, specs: e.specs } }
        that.editData = { ...that.editData, ...productdata }
        // 公式处理
        if (e2) {
          const index13 = array_data.findIndex((obj) => obj.itemId === 13);
          // if (index13 !== -1) array_data.splice(index13, 1);
          if (index13 !== -1) array_data[index13].value = null;
          const index16 = array_data.findIndex((obj) => obj.itemId === 16);
          // if (index16 !== -1) array_data.splice(index16, 1);
          if (index16 !== -1) array_data[index16].value = null;
          const item10 = array_data.find((needItem) => needItem.itemId === 10);
          if (item10) {
            item10.value = e.id
          } else {
            array_data.push({ itemId: 10, value: e.id })
          }
          const item14 = array_data.find((needItem) => needItem.itemId === 14);
          if (item14) {
            item14.value = e2.id
          } else {
            array_data.push({ itemId: 14, value: e.id })
          }

          that.ys_is_bj = true;
          if (e2.price) {
            // 对应id为17的处理
            let id17Item = array_data.find((needItem) => needItem.itemId === 17);
            if (id17Item) {
              id17Item.value = e2.price;
            } else {
              array_data.push({ itemId: 17, value: e2.price })
            }
          }
        } else {
          if (that.gstype === 'sy') {
            const index10 = array_data.findIndex((obj) => obj.itemId === 10);
            if (index10 !== -1) array_data[index10].value = null;
            const index14 = array_data.findIndex((obj) => obj.itemId === 14);
            if (index14 !== -1) array_data[index14].value = null;
            const item13 = array_data.find((needItem) => needItem.itemId === 13);
            if (item13) {
              item13.value = e.priceId
            } else {
              array_data.push({ itemId: 13, value: e.priceId })
            }
            const item16 = array_data.find((needItem) => needItem.itemId === 16);
            if (item16) {
              item16.value = e.id
            } else {
              array_data.push({ itemId: 16, value: e.id })
            }
            if (e.price) {
              let id17Item = array_data.find((needItem) => needItem.itemId === 17);
              if (id17Item) {
                id17Item.value = e.price;
              } else {
                array_data.push({ itemId: 17, value: e.price })
              }
            }
          } else {
            const index13 = array_data.findIndex((obj) => obj.itemId === 13);
            if (index13 !== -1) array_data[index13].value = null;
            const index16 = array_data.findIndex((obj) => obj.itemId === 16);
            if (index16 !== -1) array_data[index16].value = null;
            const index14 = array_data.findIndex((obj) => obj.itemId === 14);
            if (index14 !== -1) array_data[index14].value = null;
            const item10 = array_data.find((needItem) => needItem.itemId === 10);
            if (item10) {
              item10.value = e.id
            } else {
              array_data.push({ itemId: 10, value: e.id })
            }
          }
        }
      } else {
        array_data = that.stageitem[that.tmp_index].items.show;
        // 遍历数组数据，查找匹配的项
        for (let item of array_data) {
          let matchWord = item.item.matchWord;
          if (data.hasOwnProperty(matchWord)) {
            let matchedItem = that.addData.needItems.find((needItem) => needItem.itemId === item.itemId);
            if (matchedItem) {
              matchedItem.value = data[matchWord];
            }
          }
        }
        // 公式处理
        if (e2) {
          const index13 = that.addData.needItems.findIndex((obj) => obj.itemId === 13);
          if (index13 !== -1) that.addData.needItems[index13].value = null;
          const index16 = that.addData.needItems.findIndex((obj) => obj.itemId === 16);
          if (index16 !== -1) that.addData.needItems[index16].value = null;
          const item10 = that.addData.needItems.find((needItem) => needItem.itemId === 10);
          if (item10) {
            item10.value = e.id
          } else {
            that.addData.needItems.push({ itemId: 10, value: e.id })
          }
          const item14 = that.addData.needItems.find((needItem) => needItem.itemId === 14);
          if (item14) {
            item14.value = e2.id
          } else {
            that.addData.needItems.push({ itemId: 14, value: e.id })
          }
          that.ys_is_bj = true;
          if (e2.price) {
            // 对应id为17的处理
            let id17Item = that.addData.needItems.find((needItem) => needItem.itemId === 17);
            if (id17Item) {
              id17Item.value = e2.price;
            } else {
              that.addData.needItems.push({ itemId: 17, value: e2.price })
            }
          }
        } else {
          if (that.gstype === 'sy') {
            const index10 = that.addData.needItems.findIndex((obj) => obj.itemId === 10);
            if (index10 !== -1) that.addData.needItems[index10].value = null;
            const index14 = that.addData.needItems.findIndex((obj) => obj.itemId === 14);
            if (index14 !== -1) that.addData.needItems[index14].value = null;
            const item13 = that.addData.needItems.find((needItem) => needItem.itemId === 13);
            if (item13) {
              item13.value = e.priceId
            } else {
              that.addData.needItems.push({ itemId: 13, value: e.priceId })
            }
            const item16 = that.addData.needItems.find((needItem) => needItem.itemId === 16);
            if (item16) {
              item16.value = e.id
            } else {
              that.addData.needItems.push({ itemId: 16, value: e.id })
            }
            if (e.price) {
              let id17Item = that.addData.needItems.find((needItem) => needItem.itemId === 17);
              if (id17Item) {
                id17Item.value = e.price;
              } else {
                that.addData.needItems.push({ itemId: 17, value: e.price })
              }
            }
          } else {
            const index13 = that.addData.needItems.findIndex((obj) => obj.itemId === 13);
            if (index13 !== -1) that.addData.needItems[index13].value = null;
            const index16 = that.addData.needItems.findIndex((obj) => obj.itemId === 16);
            if (index16 !== -1) that.addData.needItems[index16].value = null;
            const index14 = that.addData.needItems.findIndex((obj) => obj.itemId === 14);
            if (index14 !== -1) that.addData.needItems[index14].value = null;
            const item10 = that.addData.needItems.find((needItem) => needItem.itemId === 10);
            if (item10) {
              item10.value = e.id
            } else {
              that.addData.needItems.push({ itemId: 10, value: e.id })
            }
          }
        }
      }
      that.glview = false;
      this.$message.success("关联成功");
    },
    // 附加运费
    handleFreight(e, e2, t) {
      this.$prompt('请输入附加运费(元)', '提示', {
        inputPlaceholder: '请输入附加运费(元)',
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /^((\d*(\.\d{1,5})$)|(\d{1,}$))/,
        inputErrorMessage: '附加运费输入格式不正确',
        showClose: false,
        closeOnClickModal: false
      }).then(({ value }) => {
        this.freightValue = value
        this.glkline(e, e2, t)
      }).catch(() => {
        this.glkline(e, e2, t)
      });
    },
    //关联k线和私域报价
    glkline(e, e2, t) {
      let that = this;
      that.freightOpen = false
      let array_data
      if (that.isEdit) {
        array_data = that.editData.fragmentItems.show
        if (t === 'c') {
          that.gl_tmp3 = { product_name: e.plantName, specs: e.typeName, freightValue: that.freightValue };
          that.ys_is_bj = false;
          const index13 = array_data.findIndex((obj) => obj.itemId === 13);
          if (index13 !== -1) array_data[index13].value = null;
          // if (index13 !== -1) array_data.splice(index13, 1);
          const index16 = array_data.findIndex((obj) => obj.itemId === 16);
          if (index16 !== -1) array_data[index16].value = null;
          // if (index16 !== -1) array_data.splice(index16, 1);
          const item18 = array_data.find((needItem) => needItem.itemId === 18);
          if (item18) {
            item18.value = e.id
          } else {
            array_data.push({ itemId: 18, value: e.id })
          }
          if (that.editData.fragmentItems.k) {
            const item23 = that.editData.fragmentItems.k.find((needItem) => needItem.itemId === 23);
            item23.value = that.freightValue
          } else {
            const k = { k: [{ itemId: 23, value: that.freightValue }] }
            that.editData.fragmentItems = { ...that.editData.fragmentItems, ...k }
          }
        } else {
          that.gl_tmp3 = e;
          that.ys_is_bj = true;
          const index18 = array_data.findIndex((obj) => obj.itemId === 18);
          // if (index18 !== -1) array_data.splice(index18, 1);
          if (index18 !== -1) array_data[index18].value = null;
          const item13 = array_data.find((needItem) => needItem.itemId === 13);
          if (item13) {
            item13.value = e.priceId
          } else {
            array_data.push({ itemId: 13, value: e.priceId })
          }
          const item16 = array_data.find((needItem) => needItem.itemId === 16);
          if (item16) {
            item16.value = e.id
          } else {
            array_data.push({ itemId: 16, value: e.id })
          }
        }
        if (e.price) {
          const price = Number(e.price) + Number(that.freightValue || 0)
          let id6Item = that.editData.fragmentItems.result.find((needItem) => needItem.itemId === 6);
          if (id6Item) {
            id6Item.value = price;
          } else {
            that.editData.fragmentItems.result.push({ itemId: 6, value: price })
          }
        }
      } else {
        if (t === 'c') {
          that.gl_tmp2 = { product_name: e.plantName, specs: e.typeName, freightValue: that.freightValue };
          that.ys_is_bj = false;
          const index13 = that.addData.needItems.findIndex((obj) => obj.itemId === 13);
          if (index13 !== -1) that.addData.needItems[index13].value = null;
          // if (index13 !== -1) that.addData.needItems.splice(index13, 1);
          const index16 = that.addData.needItems.findIndex((obj) => obj.itemId === 16);
          if (index16 !== -1) that.addData.needItems[index16].value = null;
          // if (index16 !== -1) that.addData.needItems.splice(index16, 1);
          const item18 = that.addData.needItems.find((needItem) => needItem.itemId === 18);
          if (item18) {
            item18.value = e.id
          } else {
            that.addData.needItems.push({ itemId: 18, value: e.id })
          }
          const item23 = that.addData.needItems.find((needItem) => needItem.itemId === 23);
          if (item23) {
            item23.value = that.freightValue
          } else {
            that.addData.needItems.push({ itemId: 23, value: that.freightValue })
          }
        } else {
          that.gl_tmp2 = e;
          that.ys_is_bj = true;
          const index18 = that.addData.needItems.findIndex((obj) => obj.itemId === 18);
          if (index18 !== -1) that.addData.needItems[index18].value = null;
          // if (index18 !== -1) that.addData.needItems.splice(index18, 1);
          const item13 = that.addData.needItems.find((needItem) => needItem.itemId === 13);
          if (item13) {
            item13.value = e.priceId
          } else {
            that.addData.needItems.push({ itemId: 13, value: e.priceId })
          }
          const item16 = that.addData.needItems.find((needItem) => needItem.itemId === 16);
          if (item16) {
            item16.value = e.id
          } else {
            that.addData.needItems.push({ itemId: 16, value: e.id })
          }
        }
        if (e.price) {
          const price = Number(e.price) + Number(that.freightValue || 0)
          let id6Item = that.resData.needItems.find((needItem) => needItem.itemId === 6);
          if (id6Item) {
            id6Item.value = price;
          } else {
            that.resData.needItems.push({ itemId: 6, value: price })
          }
        }
      }

      that.kview = false;
      this.$message.success("关联成功");
    },
    //删除模板
    deltmp(e) {
      let that = this;
      if (!confirm("确定删除该模板吗?"))
        return;

      ff.tmpdel({ templateId: e.templateId }).then((res) => {
        if (res.code == 200) {
          that.$message.success(res.msg);
          ff.stagetemp({ stage: e.stage }).then((res2) => {
            that.stageitem = res2.data;
          });
        }
      });
    },
    //清空字段
    cleartmp() {
      this.isEdit = false
      this.editStage = undefined
      this.gl_tmp2 = {}
      this.gl_tmp3 = {}
      this.gl_tmp4 = {}
      this.editData = {}
      for (var x = 0; x < this.zdlist.length; x++) {
        this.zdlist[x].istrue = false;
      }
    },
    //取消字段
    qxzd(index, e) {
      let that = this;
      that.addtmpData.templateItems.splice(index, 1);
      for (var x = 0; x < that.zdlist.toBeSelect.length; x++) {
        if (that.zdlist.toBeSelect[x].item.itemId == e.itemId) {
          that.zdlist.toBeSelect[x].item.istrue = false;
        }
      }
    },
    //删除字段
    delzd(e) {
      let that = this;
      ff.zddel({ itemId: e.itemId }).then((res) => {
        if (res.code == 200) {
          that.$message.success(res.msg);
          ff.zdlist({ stage: that.stages[that.stage_index].stage }).then((res2) => {
            that.zdlist = res2.data;
          });
        }
      });
    },
    //新增字段
    addzd() {
      let that = this;
      var data = {
        itemName: that.addzdData.itemName,
        itemUnit: that.addzdData.itemUnit,
        itemType: "input"
      }

      ff.zdadd(data).then((res) => {

        if (res.code == 200) {
          that.$message.success(res.msg);
          ff.zdlist({ stage: that.stages[that.stage_index].stage }).then((res2) => {
            that.zdlist = res2.data;
          });
        }

      });
    },
    //插入新增模板项目
    addtmpitem(e, i) {
      let that = this;
      that.addtmpData.templateItems.push(e.item);
      //e.istrue=true;
      //在这里解决重复项
      let tmp = that.addtmpData.templateItems;
      let uniqueValues = tmp.filter((item, index, array) => {
        return array.findIndex(obj => obj.itemId === item.itemId) === index;
      });
      // 仅保留唯一的needItems项
      that.addtmpData.templateItems = uniqueValues;
    },
    //新增阶段模板
    addtmp() {
      let that = this;

      if (that.addtmpData.name == "" || !that.addtmpData.name) {
        that.$message("请输入名称后提交");
        return false;
      }
      else if (!that.addtmpData.dynamic) {
        that.$message("请补充右侧图片或描述");
        return false;
      }

      for (var x = 0; x < that.addtmpData.templateItems.length; x++) {
        that.addtmpData.templateItems[x].expression = "";
        that.addtmpData.templateItems[x].module = "show";
        that.addtmpData.templateItems[x].position = 0;
      }

      that.addtmpData.dynamic = that.addtmpData.dynamic.replaceAll('\n', '<br/>');
      if (that.stage_index === 2) that.addtmpData.storeId = that.collectId
      ff.tmpadd(that.addtmpData).then((res) => {
        if (res.code == 200) {
          that.$message.success(res.msg);
          that.addtmpview = false;
          //返回所有字段
          ff.zdlist({ stage: that.stages[that.stage_index].stage }).then((res2) => {
            that.zdlist = res2.data;
          });

          // 返回所有模板
          if (that.stage_index === 2) {
            ff.quoteTemplateDirValue({ storeId: that.collectId }).then(res => {
              if (res.code === 200) {
                that.collectstageitem = res.data
                that.tmp_index = -1
                that.seltmp(res.data.length - 1);
              } else {
                that.$message.error(res.msg)
              }
            })
          } else {
            ff.stagetemp({ stage: that.addtmpData.stage }).then((res3) => {
              that.stageitem = res3.data;
              that.tmp_index = -1;
              that.addtmpData.templateItems = [];
              that.addtmpData.dynamic = "";
              that.addtmpData.name = "";
              for (var x = 0; x < that.zdlist.length; x++) {
                that.zdlist[x].istrue = false;
              }
              that.seltmp(that.stageitem.length - 1);
            });
          }
        }
      });
    },
    //完成报价
    bj_fin() {
      let that = this;
      if (that.info.product == null) {
        that.$message.error("请先关联产品");
        return false;
      }
      if (that.info.fragments == null) {
        that.$message("请先完成计算");
        return false;
      }
      ff.bj_fin({ quoteId: that.info.quoteId }).then((res) => {
        //报价计算列表
        this.$router.push("/offer/offerlist");
      });
    },
    //保存片段
    savepd(item2) {
      let that = this;
      that.isEdit = false
      that.editStage = undefined
      that.editData = {}
      that.gl_tmp2 = {}
      that.gl_tmp3 = {}
      that.gl_tmp4 = {}
      that.pd_id = -1;
      var values = [];
      if (item2.fragmentItems.k) {
        for (var x = 0; x < item2.fragmentItems.k.length; x++) {
          values.push({
            itemId: item2.fragmentItems.k[x].itemId,
            value: item2.fragmentItems.k[x].value
          });
        }
      }
      for (var x = 0; x < item2.fragmentItems.show.length; x++) {
        values.push({
          itemId: item2.fragmentItems.show[x].itemId,
          value: item2.fragmentItems.show[x].value
        });
      }
      for (var x = 0; x < item2.fragmentItems.result.length; x++) {
        values.push({
          itemId: item2.fragmentItems.result[x].itemId,
          value: item2.fragmentItems.result[x].value
        });
      }
      var data = {
        fragmentId: item2.fragmentId,
        itemValues: values,
        remark: item2.remark
      }
      ff.pdedi(data).then((res) => {
        if (res.code == 200) {
          that.$message.success(res.msg);
          ff.quotesel({ quoteId: that.list[that.list_index].id }).then((res) => {
            if (res.data.taxPercent === null) res.data.taxPercent = 0
            that.baseInfo = res.data
            that.info = res.data;
            that.tmp_index = -1;
            that.ys_is_bj = false;
            // that.stageitem[that.tmp_index].remark = "";
          });
        }
      });
    },
    //修改报价信息
    quotaedi() {
      let that = this;
      var data = {
        quoteId: that.ediInfo.id,
        name: that.ediInfo.name
      }
      ff.quoteedi(data).then((res) => {
        if (res.code == 200) {
          that.$message.success(res.msg);
          ff.quotesel({ quoteId: that.list[that.list_index].id }).then((res) => {
            if (res.data.taxPercent === null) res.data.taxPercent = 0
            that.baseInfo = res.data
            that.info = res.data;
            that.ediinfoview = false;
          });
        }
      });

    },
    //删除报价
    quotadel() {
      let that = this;
      var data = {
        quoteId: that.ediInfo.id
      }
      ff.quotedel(data).then((res) => {
        if (res.code == 200) {
          that.$message.success(res.msg);
          that.ediInfo = "";
          that.list_index = -1;
          that.ediinfoview = false;
          that.pd_id = -1;
          that.tmp_index = -1;
          that.goods = [];
          that.info = [];
          //报价计算列表
          ff.list().then((res2) => {
            that.list = res2.rows;
          });

        }
      });

    },
    // 删除片段
    pddel(id) {
      let that = this;
      if (!confirm("确定删除该条目吗?"))
        return;

      ff.pddel({ fragmentId: id }).then((res) => {
        if (res.code == 200) {
          that.$message.success(res.msg);
          ff.quotesel({ quoteId: that.list[that.list_index].id }).then((res) => {
            if (res.data.taxPercent === null) res.data.taxPercent = 0
            that.baseInfo = res.data
            that.info = res.data;
          });
        }
      });

    },
    // //预览ped
    // drawPreView(e) {
    //   this.pdfshow = true;
    //   this.pdfUrl = process.env.VUE_APP_BASE_API + e.replace('https://www.ziyouke.net', '');
    // },
    // 预览图片
    handleImgView(row) {
      this.$refs.productInfo.handleImgView(row)
    },
    // 查看详情
    drawPreView(item, val) {
      this.$refs.productInfo.handleView(item, val)
    },

    //创建元素
    ysadd() {
      let that = this;
      ff.quota_add(this.ysdata).then((res) => {
        if (res.code == 200) {
          this.gljgview = false;
          this.ysdata = {};
          this.gl_tmp = {};
          this.ys_is_bj = false;
          this.save(res.data);
          this.$message.success(res.msg);
        }
      });
    },
    //保存阶段
    save(id = 0) {
      let that = this;

      // if (that.gl_tmp.product_name && !that.ys_is_bj && that.gstype == 'sy') {
      //   that.gljgview = true;
      //   that.ysdata.stage = that.stages[that.stage_index].stage;
      //   that.ysdata.productId = that.gl_tmp.id;

      //   for (var x = 0; x < that.addData.needItems.length; x++) {
      //     if (that.addData.needItems[x].itemId == 17) {
      //       that.ysdata.price = that.addData.needItems[x].value;
      //     }
      //   }
      //   return false;
      // }

      //如果有id
      if (id != 0) {
        let array_data = that.stageitem[that.tmp_index].items.show;
        for (let item of array_data) {
          if (item.item.itemType == 'reference-price') {
            for (var x = 0; x < that.addData.needItems.length; x++) {
              if (that.addData.needItems[x].itemId == item.itemId) {
                that.addData.needItems[x].value = id;
                break;
              }
            }
          }
        }
      }
      that.addData.needItems.push.apply(that.addData.needItems, that.resData.needItems);

      //在这里解决重复项
      let tmp = that.addData.needItems;
      let uniqueValues = tmp.filter((item, index, array) => {
        return array.findIndex(obj => obj.itemId === item.itemId) === index;
      });
      // 仅保留唯一的needItems项
      that.addData.needItems = uniqueValues;

      let values = that.addData.needItems;
      const templateId = that.collectId && that.stage_index === 2 ? that.collectstageitem[that.tmp_index].templateId : that.stageitem[that.tmp_index].templateId;
      const remark = that.collectId && that.stage_index === 2 ? that.collectstageitem[that.tmp_index].remark : that.stageitem[that.tmp_index].remark;
      let data = {
        templateId: templateId,
        quoteId: that.list[that.list_index].id,
        itemValues: values,
        remark: remark
      };


      // 计时器倒计时代码
      if (!that.oksave) {
        return false;
      } else {
        that.oksave = false;
      }

      let countdownSeconds = 10;
      let countdownTimer = setInterval(() => {
        countdownSeconds--;
        that.savebtn = countdownSeconds + "s";
        if (countdownSeconds < 0) {
          clearInterval(countdownTimer);
          that.oksave = true;
          that.savebtn = "保存设置";
        }
      }, 1000);

      ff.pdadd(data).then((res) => {
        if (res.code == 200) {
          that.$message.success(res.msg);
          ff.quotesel({ quoteId: that.list[that.list_index].id }).then((res) => {
            if (res.data.taxPercent === null) res.data.taxPercent = 0
            that.baseInfo = res.data
            that.info = res.data;
            that.tmp_index = -1;
            that.ys_is_bj = false;
            // that.stageitem[that.tmp_index].remark = "";
          });
        }
      });
    },
    // 报价详情
    listinfo(i, e) {
      let that = this;
      that.pd_id = -1;
      that.list_index = i;
      that.tmp_index = -1;
      that.stage_index = -1;
      ff.quotesel({ quoteId: e.id }).then((res) => {
        if (res.data.taxPercent === null) res.data.taxPercent = 0
        that.baseInfo = res.data
        that.info = res.data;
      });
    },
    //关联产品
    gl(e) {
      let that = this;
      if (!confirm("确定关联" + e.product_name + "吗?"))
        return;
      var data = {
        quoteId: that.list[that.list_index].id,
        productId: e.id,
        source: e.source
      }
      ff.quoteedi(data).then((res) => {
        if (res.code == 200) {
          that.$message.success(res.msg);
          ff.quotesel({ quoteId: that.list[that.list_index].id }).then((res) => {
            if (res.data.taxPercent === null) res.data.taxPercent = 0
            that.info = res.data;
            that.bindView = false;
          });
        }
      });

    },
    //添加产品
    addgood() {
      let that = this;
      ff.product(that.addGoodData).then((res) => {
        if (res.code == 200) {
          that.$message.success(res.msg);
          that.addGoodData = {};
          that.addGoodView = false;
        }
      });
    },
    //搜索产品
    sgood() {
      let that = this;
      ff.search(this.newsearch).then((res) => {
        const name = this.uniqueJsonArrByField(res.data, 'product_name')
        const specs = this.uniqueJsonArrByField(res.data, 'specs')
        const model = this.uniqueJsonArrByField(res.data, 'model')
        const surface = this.uniqueJsonArrByField(res.data, 'surface')
        const unit = this.uniqueJsonArrByField(res.data, 'unit')
        const material = this.uniqueJsonArrByField(res.data, 'material_quality')
        this.specsFilters = specs.map(item => {
          return { text: item.specs, value: item.specs }
        })
        this.materialFilter = material.map(item => {
          return { text: item.material_quality, value: item.material_quality }
        })
        this.nameFilters = name.map(item => {
          return { text: item.product_name, value: item.product_name }
        })
        this.modelFilters = model.map(item => {
          return { text: item.model, value: item.model }
        })
        this.surfaceFilters = surface.map(item => {
          return { text: item.surface, value: item.surface }
        })
        this.unitFilters = unit.map(item => {
          return { text: item.unit, value: item.unit }
        })
        that.sgoods = res.data;
        if (res.data.length == 0) {
          that.$message("暂无产品");
        }
      });
    },
    resetsgood() {
      this.resetForm('newsearch')
      this.sgood()
    },
    // 数组去重
    uniqueJsonArrByField(jsonArr, field) {
      // 数组长度小于2 或 没有指定去重字段 或 不是json格式数据
      if (jsonArr.length < 2 || !field || typeof jsonArr[0] !== "object") return jsonArr;
      return jsonArr.reduce((all, next) => all.some((item) => item[field] === next[field]) ? all : [...all, next], []);
    },
    getRowKey(row) {
      return row.source + row.id
    },
    filterSpecs(value, row) {
      return row.specs === value
    },
    filterQuality(value, row) {
      return row.material_quality === value
    },
    filterName(value, row) {
      return row.product_name === value
    },
    filterModel(value, row) {
      return row.model === value
    },
    filterSurface(value, row) {
      return row.surface === value
    },
    filterUnit(value, row) {
      return row.unit === value
    },
    //公式计算
    jisuan() {
      let that = this;
      let data = { ...that.addData }
      data.needItems = [...data.needItems, ...that.resData.needItems]
      data = JSON.parse(JSON.stringify(data));
      // var data = JSON.parse(JSON.stringify(that.addData));
      ff.jisuan(data).then((res) => {
        that.$message.success("计算完成");
        for (var x = 0; x < that.resData.needItems.length; x++) {
          //循环返回结果
          for (var y = 0; y < res.data.length; y++) {
            if (res.data[y].itemId == that.resData.needItems[x].itemId) {
              that.resData.needItems[x].value = res.data[y].result;
            }
          }
        }
      });
    },
    //公式计算
    jisuan2() {
      let that = this;
      var data = JSON.parse(JSON.stringify(that.resData));
      let arr
      if (that.collectId && that.stageitem[that.tmp_index].stage === 'outsourcing') arr = that.collectstageitem[that.tmp_index]
      else arr = that.stageitem[that.tmp_index]
      for (var x = arr.items.result.length - 1; x > 0; x--) {
        if (arr.items.result[x].formula == true) {
          data.filedId = arr.items.result[x].templateItemId;
          break;
        }
      }

      for (var x = 0; x < that.addData.needItems.length; x++) {
        data.needItems.push({
          itemId: that.addData.needItems[x].itemId,
          value: that.addData.needItems[x].value
        });
      }

      ff.jisuan(data).then((res) => {
        that.$message.success("小计计算完成");
        for (var x = arr.items.result.length - 1; x > 0; x--) {

          if (arr.items.result[x].formula == true) {
            for (var y = 0; y < res.data.length; y++) {
              if (arr.items.result[x].itemId == res.data[y].itemId)
                that.resData.needItems[x].value = res.data[y].result;
              break;
            }
          }
        }
      }
      );
    },
    //公式计算(产品列表)
    jisuan_1(e) {
      let that = this;
      let needItems = [];
      let aa = 0;
      for (var x = 0; x < e.result.length; x++) {
        if (e.result[x].formula == true) {
          aa = e.result[x].templateItemId;
          break;
        }
      }
      for (var x = 0; x < e.show.length; x++) {
        needItems.push({
          itemId: e.show[x].itemId,
          value: e.show[x].value,
        })
      }
      e.result.map(item => {
        needItems.push({
          itemId: item.itemId,
          value: item.value,
        })
      })
      var data = {
        filedId: aa,
        needItems: needItems
      }

      ff.jisuan(data).then((res) => {
        that.$message.success("计算完成");
        for (var x = 0; x < e.result.length; x++) {
          for (var y = 0; y < res.data.length; y++) {
            if (res.data[y].itemId == e.result[x].itemId) {
              e.result[x].value = res.data[y].result;
            }
          }
        }


      });
    },
    //公式计算(已保存数据)
    jisuan2_1(e) {
      let that = this;
      let needItems = [];
      let aa = 0;
      for (var x = e.result.length - 1; x > 0; x--) {

        if (e.result[x].formula == true) {
          aa = e.result[x].templateItemId;
          break;
        }
      }
      for (var x = 0; x < e.result.length; x++) {
        needItems.push({
          itemId: e.result[x].itemId,
          value: e.result[x].value,
        })
      }

      for (var x = 0; x < e.show.length; x++) {
        needItems.push({
          itemId: e.show[x].itemId,
          value: e.show[x].value,
        })
      }

      var data = {
        filedId: aa,
        needItems: needItems
      }


      ff.jisuan(data).then((res) => {
        that.$message.success("小计计算完成");



        for (var x = e.result.length - 1; x > 0; x--) {

          if (e.result[x].formula == true) {
            for (var y = 0; y < res.data.length; y++) {
              if (e.result[x].itemId == res.data[y].itemId)
                e.result[x].value = res.data[y].result;
              break;
            }

          }
        }
      }
      );
    },
    //选择模版
    seltmp(index) {
      let that = this;
      that.isEdit = false
      that.editStage = undefined
      that.editData = {}
      that.gl_tmp2 = {}
      that.gl_tmp3 = {}
      that.gl_tmp4 = { product: { productName: undefined } }
      that.pd_id = -1;
      that.gl_tmp = {};
      that.f_id = -1;
      that.feiliao = [];
      that.addtmpData.templateItems = [];
      if (that.tmp_index == index) {
        that.tmp_index = -1;
        return;
      }

      that.tmp_index = index;
      // 初始化addData
      let arr = that.collectId && that.stage_index === 2 ? that.collectstageitem[index] : that.stageitem[index];
      //存resultid
      if (!arr.items)
        return;
      for (var x = 0; x < arr.items.result.length; x++) {
        if (arr.items.result[x].formula == true) {
          that.$set(that.addData, 'filedId', arr.items.result[x].templateItemId);
          break;
        }
      }
      //使用$set方法添加或修改addData对象的属性和值
      //that.$set(that.addData, 'filedId', arr.templateId);
      that.$set(that.addData, 'needItems', []);
      that.$set(that.resData, 'needItems', []);
      for (var x = 0; x < arr.items.show.length; x++) {
        if (arr.items.show[x].item.itemType == "select") {
          var a = arr.items.show[x].item.values[0].value;
          that.addData.needItems.push({ itemId: arr.items.show[x].itemId, value: a });
        }
        else {
          that.addData.needItems.push({ itemId: arr.items.show[x].itemId, value: arr.items.show[x].item.defaultValue });
        }
      }
      for (var x = 0; x < arr.items.result.length; x++) {
        that.resData.needItems.push({ itemId: arr.items.result[x].itemId, value: arr.items.result[x].item.defaultValue });
      }
    },
    // 获取阶段模板
    gettemp(i, item) {
      let that = this;
      that.isEdit = false
      that.editStage = undefined
      that.editData = {}
      that.gl_tmp2 = {}
      that.gl_tmp3 = {}
      that.gl_tmp4 = {}
      that.f_id = -1;
      if (that.stage_index == i) {
        that.stage_index = -1;
        return;
      }
      if (i === 2) this.getCollectList()
      ff.stagetemp({ stage: item.stage }).then((res) => {
        that.stage_index = i;
        that.stageitem = res.data;
        that.tmp_index = -1;
        that.addtmpData.stage = item.stage;
        that.addtmpData.templateItems = [];

        ff.zdlist({ stage: item.stage }).then((res) => {
          that.zdlist = res.data;
        });
      });
    },
    // 查询收藏夹列表
    getCollectList() {
      ff.quoteTemplateDir().then(res => {
        if (res.code === 200) {
          this.collectList = res.data
          this.collectId = res.data[0].storeId
          ff.quoteTemplateDirValue({ storeId: this.collectId }).then(res => {
            if (res.code === 200) {
              this.collectstageitem = res.data
              this.tmp_index = -1
            } else {
              this.$message.error(res.msg)
            }
          })
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 改变收藏
    handleChangeCollect(row) {
      if (row === 'all') {
        this.collectId = undefined
        this.tmp_index = -1
      } else {
        if (row) {
          const storeId = row.storeId
          this.collectId = storeId
        }
        ff.quoteTemplateDirValue({ storeId: this.collectId }).then(res => {
          if (res.code === 200) {
            this.collectstageitem = res.data
            this.tmp_index = -1
          } else {
            this.$message.error(res.msg)
          }
        })
      }
    },
    // 管理收藏夹
    handleCollectMange() {
      this.$refs.collect.handleMange()
    },
    // 打开右键菜单
    openMenu(tag, e) {
      this.left = e.clientX + 10
      this.top = e.clientY
      this.visible = true
      this.selectedTag = tag
    },
    // 关闭右键菜单
    closeMenu() {
      this.visible = false
    },
    // 收藏到
    handleCollet() {
      this.$refs.collect.handleChange(this.selectedTag.templateId)
    },
    // 提交收藏
    handleColletSubmit(storeId, data) {
      if (!Array.isArray(data)) data = [data]
      sc.shoucTo({ storeId, valueIdList: data }).then(res => {
        this.getCollectList()
        this.$modal.msgSuccess('操作成功')
      })
    },
    // 删除收藏
    handleColletDelete() {
      const data = { storeId: this.collectId, valueId: this.selectedTag.templateId }
      this.$modal.confirm('是否确认删除选中的收藏？').then(function () {
        return sc.delshouc(data)
      }).then(() => {
        this.handleChangeCollect()
        this.$modal.msgSuccess('删除成功')
      }).catch(() => { })
    },
    //新增报价
    add() {
      let that = this;
      that.$refs['bjform'].validate((valid) => {
        if (valid) {
          ff.quoteadd(that.bjadd).then((res) => {
            ff.list().then((res2) => {
              that.list = res2.rows;
              that.addView = false;
              that.listinfo(0, that.list[0]);
            });
          });
        }
      })
    },
    // 筛选显示
    handleSizer(e) {
      this.stageChecked = e
      localStorage.setItem('stageChecked', e.toString())
    },
    // 修改是否含税
    handleChangeTaxSwitch(e) {
      if (e) return
      if (!e) {
        let that = this;
        that.taxActive = false
        var data = {
          quoteId: that.baseInfo.quoteId,
          isIncludingTax: false,
        }
        ff.quoteedi(data).then((res) => {
          if (res.code == 200) {
            ff.getQuoteBase({ quoteId: that.list[that.list_index].id }).then((res) => {
              if (res.data.taxPercent === null) res.data.taxPercent = 0
              that.baseInfo = { ...res.data, ...{ quoteId: res.data.id } }
            });
          }
        });
      }
    },
    // 修改含税比例
    handleChangeTax() {
      let that = this;
      that.taxActive = true
      var data = {
        quoteId: that.baseInfo.quoteId,
        isIncludingTax: true,
        taxPercent: that.baseInfo.taxPercent
      }
      ff.quoteedi(data).then((res) => {
        if (res.code == 200) {
          ff.getQuoteBase({ quoteId: that.list[that.list_index].id }).then((res) => {
            if (res.data.taxPercent === null) res.data.taxPercent = 0
            that.baseInfo = { ...res.data, ...{ quoteId: res.data.id } }
          });
        }
      });
    },
    // 关联二级报价
    handleOneKeyList() {
      this.getListData()
      this.listView = true
    },
    // 二级报价搜索
    handleOneKeyQuery() {
      this.listQuery.pageNum = 1
      this.getListData()
    },
    // 重置搜索
    handleOneKeyResetQuery() {
      this.listQuery.name = undefined
      this.handleOneKeyQuery()
    },
    // 查询二级报价列表
    getListData() {
      ff.list(this.listQuery).then(res => {
        const { code, msg, rows, total } = res
        if (code === 200) {
          this.listData = rows
          this.listTotal = total
        } else this.$message.error(msg)
      })
    },
    // 确定关联二级报价
    handleOneKeyListSubmit(e) {
      let that = this;
      that.gl_tmp4 = e;
      let data = e;
      let array_data
      if (that.isEdit) {
        array_data = [...that.editData.fragmentItems.show, ...that.editData.fragmentItems.result]
        // 遍历数组数据，查找匹配的项
        array_data.map(item => {
          if (data[item.matchWord]) item.value = data[item.matchWord]
        })
        that.editData.innerQuote = { ...e }
        const item0 = array_data.find((needItem) => needItem.itemId === 0);
        if (item0) {
          item0.value = e.totalPrice
        } else {
          array_data.push({ itemId: 0, value: e.totalPrice })
        }
        const item5 = array_data.find((needItem) => needItem.itemId === 5);
        if (item5) {
          item5.value = e.netWeight
        } else {
          array_data.push({ itemId: 5, value: e.netWeight })
        }
        const item9 = array_data.find((needItem) => needItem.itemId === 9);
        if (item9) {
          item9.value = e.grossWeight
        } else {
          array_data.push({ itemId: 9, value: e.grossWeight })
        }
        const item30 = array_data.find((needItem) => needItem.itemId === 30);
        if (item30) {
          item30.value = e.id
        } else {
          array_data.push({ itemId: 30, value: e.id })
        }
      } else {
        array_data = that.stageitem[that.tmp_index].items.show;
        // 遍历数组数据，查找匹配的项
        for (let item of array_data) {
          let matchWord = item.item.matchWord;
          if (data.hasOwnProperty(matchWord)) {
            let matchedItem = that.addData.needItems.find((needItem) => needItem.itemId === item.itemId);
            if (matchedItem) {
              matchedItem.value = data[matchWord];
            }
          }
        }
        const item0 = that.addData.needItems.find((needItem) => needItem.itemId === 0);
        if (item0) {
          item0.value = e.totalPrice
        } else {
          that.addData.needItems.push({ itemId: 0, value: e.totalPrice })
        }
        const item5 = that.addData.needItems.find((needItem) => needItem.itemId === 5);
        if (item5) {
          item5.value = e.netWeight
        } else {
          that.addData.needItems.push({ itemId: 5, value: e.netWeight })
        }
        const item9 = that.addData.needItems.find((needItem) => needItem.itemId === 9);
        if (item9) {
          item9.value = e.grossWeight
        } else {
          that.addData.needItems.push({ itemId: 9, value: e.grossWeight })
        }

        const item30 = that.addData.needItems.find((needItem) => needItem.itemId === 30);
        if (item30) {
          item30.value = e.id
        } else {
          that.addData.needItems.push({ itemId: 30, value: e.id })
        }
      }
      that.listView = false;
      this.$message.success("关联成功");
    },
    copySuccess() {
      this.$message.success('复制成功')
    },
    // 按吨计算计算器
    handleTonCalc(data = [], obj = {}) {
      const b = data.find(item => item.itemId === 45).value || 0
      this.dun_obj = obj
      this.dun_data.a = 0
      this.dun_data.b = b
      this.dunview = true
    },
    hasKey(obj, key) {
      return Object.prototype.hasOwnProperty.call(obj, key)
    },
  },
};
</script>
<style scoped>
body {
  background: #F9F9F9;
}

.box {
  width: 98%;
  height: 125px;
  background-color: white;
  margin: 20px auto 0;
  box-shadow: 0px 0px 26px 0px rgba(0, 0, 0, 0.05);
  border-radius: 5px;
}

::-webkit-scrollbar {
  width: 3px;
}

::-webkit-scrollbar-thumb {
  width: 3px;
  background-color: #ccc;
}

.container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  padding-bottom: 50px;
  min-width: 1200px;
}

.box-1 {
  width: 100%;
  height: 42px;
  background: #F8F9FB;
  border: 1px solid #CBD6E2;
  line-height: 42px;
  font-size: 12px;
  color: #999;
  display: flex;
}

.box-1>div:nth-child(1) {
  margin-left: 20px;
}

.box-1>div:nth-child(2) {
  margin-left: 110px;
}

.box-2 {
  width: 100%;
  height: 82px;
  line-height: 82px;
  display: flex;
  flex-wrap: wrap;
  font-size: 14px;
}

.box-2>div {
  margin-left: 20px;
  color: #999;
  font-size: 12px;
}

.box-2-img {
  margin-left: 20px;
  width: 42px;
  height: 42px;
  margin-top: 20px;
  cursor: pointer;
}

.box-2-img img {
  width: 100%;
  height: 100%;
}

.box-2-a {
  margin-left: 20px;
  color: #2E73F3;
  font-weight: bold;
  cursor: pointer;
}

.box2 {
  width: 76%;
  margin: 0 auto;
  border-radius: 5px;
  min-height: 500px;
  overflow: auto;
  max-height: 110vh;
}

.box3 {
  width: 20%;
  margin: 0 auto 20px;
  border-radius: 5px;
}

.box3 .box3-sizer {
  padding: 20px 0;
}

.box3 .box3-sizer .box3-sizer-title {
  display: inline;
  color: #666;
}

.box3 .box3-sizer .box3-sizer-title i {
  font-size: 18px;
  margin-right: 5px;
}

.box3 .box3-sizer .box3-sizer-title:hover {
  color: #2E73F3;
}

.box3-1 {
  width: 100%;
  height: 70px;
  margin-bottom: 15px;
  background-color: #F2F2F2;
  border-radius: 5px;
  text-align: center;
  line-height: 70px;
  font-size: 18px;
  color: #333333;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  border: 1px solid transparent;
}

.box3-1 img,
.box3-1-sel img {
  width: 20px;
  height: 20x;
  vertical-align: middle;
}

.box3-1>div:nth-child(1) {
  font-size: 14px;
  color: #999999;
  margin-left: 25px;
}

.box3-1>div:nth-child(2) {
  font-weight: bold;
  margin-right: 20px;
}

.box3-1-sel {
  width: 100%;
  height: 70px;
  margin-bottom: 15px;
  background-color: #E9F6FF;
  border-radius: 5px;
  text-align: center;
  line-height: 70px;
  font-size: 18px;
  color: #2E73F3;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  border: 1px solid #39A8F9;
}

.box3-1-sel>div:nth-child(1) {
  font-size: 14px;
  color: #2E73F3;
  margin-left: 25px;

}

.box3-1-sel>div:nth-child(2) {
  font-weight: bold;
  margin-right: 20px;
}

.box4 {
  width: 76%;
  height: 60px;
  line-height: 60px;
  background-color: #fff;
  margin-left: 20px;
  box-shadow: 0 0 26px 0 rgba(0, 0, 0, .05);
  display: flex;
  align-items: center;
  position: relative;
  margin-top: 20px;
}

.list {
  width: 100%;
  height: 50px;
  background: #F1F3F8;
  display: flex;
  flex-wrap: wrap;
  overflow: hidden;
  position: relative;
}

.list.listHeight {
  height: auto;
}

.list .list-more {
  display: inline-block;
  line-height: 50px;
  width: 50px;
  text-align: center;
  cursor: pointer;
  position: absolute;
  top: 0;
  right: 0;
  transition: all 0.5s;
  color: #2E73F3;
  font-size: 16px;
  background-color: #F9F9F9;
}

.list-item {
  padding-left: 50px;
  padding-right: 50px;
  height: 50px;
  line-height: 50px;
  text-align: center;
  font-size: 14px;
  color: #333;
  cursor: pointer;
  position: relative;
}

.list-item-sel {
  padding-left: 50px;
  padding-right: 50px;
  height: 50px;
  line-height: 50px;
  text-align: center;
  font-size: 14px;
  color: #333;
  cursor: pointer;
  background: #F9F9F9;
  position: relative;
}

.list-item-img {
  width: 20px;
  height: 20px;
  position: absolute;
  top: 15px;
  left: 15px;
}

.list-add {
  width: 120px;
  height: 50px;
  line-height: 50px;
  text-align: center;
  color: #2E73F3;
  font-size: 14px;
  cursor: pointer;
}

.list-add img {
  width: 14px;
  height: 14px;
  vertical-align: middle;
  transform: translateY(-1px);
}

.goods {
  width: 99.9%;
  margin-top: 5px;
  margin-bottom: 10px;
  box-shadow: 0px 0px 26px 0px rgba(0, 0, 0, 0.05);
  background-color: white;
  border-radius: 5px;
  margin-bottom: 20px;
  padding-bottom: 20px;
}

.goods1 {
  width: 100%;
  height: 37px;
  border-bottom: 1px solid #E7E9EC;
  line-height: 37px;
  text-indent: 20px;
  font-size: 14px;
  color: #666666;
}

.goods2 {
  width: 98%;
  margin: 20px auto 0;
  background-color: #F1F1F3;
  border-radius: 5px;
  border: 1px solid #CBD6E2;
}

.goods2-1 {
  width: 100%;
  height: 50px;
  display: flex;
  line-height: 50px;
  font-size: 14px;
  cursor: pointer;
  position: relative;
}

.goods2-1>div:nth-child(1) {
  /* width: 10%; */
  min-width: 10%;
  margin-left: 20px;
  color: #333;
  font-weight: bold;
}

.goods2-1>div {
  color: #999;
  margin-left: 30px;
}

.goods2-1-jt {
  width: 20px;
  height: 15px;
  position: absolute;
  right: 10px;
  top: 12px;
  transition: 0.2s;
}



.tmp {
  width: 99.9%;
  box-shadow: 0px 0px 26px 0px rgba(0, 0, 0, 0.05);
  background-color: white;
  border-radius: 5px;
  margin-top: 10px;
  border: 1px solid #39A8F9;
  /* padding-bottom:20px ; */
}

.tmplist {
  display: flex;
  flex-wrap: wrap;
  /* margin-top: 20px; */
  border-bottom: 1px solid #CBD6E2;
  /* padding-bottom: 20px; */
  overflow: hidden;
}

.tmplist>div:last-child {
  margin-bottom: 20px;
}

.tmplist>div {
  margin-top: 20px;
}

.tmplist-item {
  padding-right: 10px;
  padding-left: 10px;
  height: 38px;
  background-color: #F2F2F2;
  text-align: center;
  line-height: 38px;
  border: 1px solid transparent;
  border-radius: 5px;
  margin-left: 20px;
  font-size: 16px;
  color: #666666;
  cursor: pointer;
  display: flex;
  position: relative;
  transition: 0.4s;
}

.tmplist-item>div:nth-child(1) {
  width: 26px;
  height: 26px;
  margin-top: 4px;
  margin-left: 10px;
}

.tmplist-item>div:nth-child(1)>img {
  width: 100%;
  height: 100%;
}

.tmplist-item>div:nth-child(2) {
  height: 100%;
  margin-left: 15px;
}

.tmplist-item-sel {
  /* width: 175px; */
  padding-right: 20px;
  padding-left: 10px;
  height: 38px;
  background: #E9F6FF;
  text-align: center;
  line-height: 38px;
  border: 1px solid #39A8F9;
  border-radius: 5px;
  margin-left: 20px;
  font-size: 16px;
  color: #2E73F3;
  cursor: pointer;
  display: flex;
  position: relative;
  transition: 0.4s;
}

.tmplist-item-sel-icon {
  width: 14px;
  height: 14px;
  position: absolute;
  bottom: 0;
  right: 0;
}

.tmplist-item-sel>div:nth-child(1) {
  width: 26px;
  height: 26px;
  margin-top: 4px;
  margin-left: 10px;
}

.tmplist-item-sel>div:nth-child(1)>img {
  width: 100%;
  height: 100%;
}

.tmplist-item-sel>div:nth-child(2) {
  height: 100%;
  margin-left: 15px;
}

.tmpbox {
  width: 98%;
  /* height: 900px; */
  margin: 20px auto;
  border: 1px solid #CBD6E2;
}

.tmpbox-1 {
  width: 100%;
  height: 46px;
  background: #ECF3FF;
  line-height: 46px;
  color: #2E73F3;
  font-size: 14px;
  text-indent: 39px;
}

.tmpbox-2 {
  width: 100%;
  height: 46px;
  background: #F1F3F8;
  line-height: 46px;
  color: #333;
  font-size: 14px;
  text-indent: 39px;
  border-top: 1px solid #CBD6E2;
  border-bottom: 1px solid #CBD6E2;
}

.show {
  width: 100%;
  display: flex;
  justify-content: space-around;
}

.showimg {
  width: 30%;
  border: 1px solid #CBD7E2;
  margin-top: 30px;
  margin-bottom: 30px;
  position: relative;
}

.showimg>img {
  width: 100%;
  height: 100%;
}

.showimgremark {
  font-size: 12px;
  position: absolute;
  top: -20px;
  color: #999;
}

.showitem {
  width: 60%;
  padding-top: 16px;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.showitem2 {
  width: 48%;
  height: 46px;
  margin-bottom: 20px;
  line-height: 46px;
  display: flex;
  justify-content: space-between;
  align-items: center;

}

.showitem2.showitem100 {
  width: 100%;
  padding-right: 52%;
  position: relative;
}

@media (max-width:1725px) {
  .showitem2.process {
    width: 60%;
  }

  .showitem2.process+.showitem2 {
    width: 38%;
  }

  .showitem2.process>div:nth-child(1) {
    width: 15%;
  }

  .showitem2.process+.showitem2>div:nth-child(1) {
    width: 15%;
  }
}

.showitem2>div:nth-child(1) {
  width: 20%;
  color: #999999;
  font-size: 12px;
  /* width: 60px; */
  line-height: 20px;
}

.showitem2>div:nth-child(2) {
  width: 80%;
}

.btn {
  width: 215px;
  height: 50px;
  background: #2E73F3;
  text-align: center;
  line-height: 50px;
  color: white;
  border-radius: 5px;
  position: absolute;
  top: 0;
  cursor: pointer;
}

.btnbox {
  width: 100%;
  border: 1px solid transparent;
  display: flex;
  flex-direction: row-reverse;
}

.btn2 {
  width: 22%;
  height: 40px;
  margin-bottom: 30px;
  margin-left: 5%;
  background-color: white;
  color: #2E73F3;
  border: 1px solid #2E73F3;
  text-align: center;
  line-height: 40px;
  margin-top: 30px;
  border-radius: 5px;
  font-size: 14px;
  cursor: pointer;
  transition: 0.2s;
}

.btn2:hover {
  background: #2E73F3;
  color: white;
}

.btn3 {
  width: 22%;
  height: 40px;
  background-color: white;
  color: #2E73F3;
  border: 1px solid #2E73F3;
  text-align: center;
  line-height: 40px;
  border-radius: 5px;
  font-size: 14px;
  cursor: pointer;
  transition: 0.2s;
  margin: auto;
}

.btn3:hover {
  background: #2E73F3;
  color: white;
}

.sumbox {
  width: 76%;
  height: 60px;
  background-color: white;
  margin-left: 20px;
  box-shadow: 0px 0px 26px 0px rgba(0, 0, 0, 0.05);
  display: flex;
  line-height: 60px;
  text-align: center;
  position: relative;
  margin-top: 20px;
}

.sumbox>div:nth-child(1) {
  font-size: 16px;
  height: 100%;
  font-weight: bold;
  margin-left: 20px;
  color: #333;
}

.sumbox>div:nth-child(2) {
  font-size: 12px;
  height: 100%;
  margin-left: 68px;
  color: #999;
}

.sumbox>div:nth-child(3) {
  font-size: 16px;
  height: 100%;
  font-weight: bold;
  color: #EC2454;
}

.sumbox>div:nth-child(4) {
  font-size: 12px;
  height: 100%;
  color: #999;
  margin-left: 30px;
}

.sumbox>div:nth-child(5) {
  font-size: 12px;
  height: 100%;
  color: #999;
  margin-left: 30px;
}

.sumbox-btn {
  width: 20%;
  height: 60px;
  background: #2E73F3;
  text-align: center;
  line-height: 60px;
  color: white;
  position: absolute;
  right: 0;
  cursor: pointer;
}

.sumbox-btn2 {
  width: 20%;
  height: 60px;
  background: #DCDCDC;
  text-align: center;
  line-height: 60px;
  color: white;
  position: absolute;
  right: 0;
  cursor: pointer;
}

.unit {
  position: absolute;
  top: 0;
  right: 20px;
  font-size: 14px;
  color: #999;
}

.btns {
  width: 100%;
  height: 50px;
  position: relative;
  margin-bottom: 20px;
}

.sgoodbox {
  width: 100%;
  max-height: 600px;
  /* border: 1px solid; */
  display: flex;
  flex-wrap: wrap;
  overflow: auto;
}

.sgooditem {
  width: 49%;
  height: 70px;
  /* border: 1px solid transparent; */
  margin-top: 1%;
  display: flex;
  justify-content: space-between;

}

.sgooditem:nth-of-type(even) {
  margin-left: 2%;
}

.sgooditem:nth-of-type(odd) {
  border-right: 1px solid #999;
}

.sgooditem img {
  width: 100%;
  height: 100%;
}

.sgooditem>div:nth-child(1) {
  width: 68px;
  height: 68px;
  cursor: pointer;
  border: 1px solid #999;
  border-radius: 5px;
  overflow: hidden;
}

.sgooditem>div:nth-child(2) {
  width: 40%;
  display: flex;
  flex-wrap: wrap;
  margin-left: 10px;
}

.sgooditem>div:nth-child(3) {
  width: 40%;
  display: flex;
  flex-wrap: wrap;
}

.sgooditem>div:nth-child(2)>div,
.sgooditem>div:nth-child(3)>div {
  width: 100%;
  margin: auto;
  font-size: 12px;
}

.sgoodbox-tbox {
  width: 100%;
  height: 37px;
  background: #F8F9FB;
  display: flex;
  line-height: 37px;
  font-size: 12px;
  color: #999;
  text-align: center;
  justify-content: space-around;
}

.sgoodbox-tbox>div {
  width: 18%;
  text-align: center;
}

.sgooditem2 {
  width: 100%;
  overflow: hidden;
  display: flex;
  line-height: 62px;
  flex-wrap: wrap;
  font-size: 14px;
  text-align: center;
  justify-content: space-around;
}

.sgooditem2>div:nth-child(2) {
  width: 62px;
  height: 62px;
}

.sgooditem2>div {
  text-align: center;
}


.sgooditem2 img {
  width: 60%;
  height: 60%;
  margin: auto;
}

.sgooditem2-btn {
  width: 101px;
  height: 30px;
  background: #E0EBFF #FFFFFF;
  border-radius: 5px 5px 5px 5px;
  opacity: 1;
  border: 1px solid #2E73F3;
  margin: 17px auto;
  line-height: 30px;
  color: #2E73F3;
  cursor: pointer;
  font-size: 12px;
  transition: 0.3s;

}

.sgooditem2-btn:hover {
  background: #2E73F3;
  color: white;
}

.sgooditem2-btn2 {
  width: 101px;
  height: 30px;
  background: #E0EBFF #FFFFFF;
  border-radius: 5px 5px 5px 5px;
  opacity: 1;
  border: 1px solid #31C776;
  margin: 17px auto;
  line-height: 30px;
  color: #31C776;
  cursor: pointer;
  font-size: 12px;
  transition: 0.3s;
}

.sgooditem2-btn2:hover {
  background: #31C776;
  color: white;
}

.sgooditem2-2 {
  width: 100%;
  border: 1px solid #2E73F3;
  overflow: hidden;
}

.sgooditem2-2>div:nth-child(odd) {
  background: #F0F3F9;
}

.sgooditem2-2>div:nth-child(even) {
  background: #F8F9FB;
}


.sgooditem2-2-1 {
  display: flex;
  justify-content: space-around;
}

.sgooditem2-2-1.sgooditem2-check {
  background-color: rgba(248, 249, 251, .7);
}

.sgooditem2-2-1.sgooditem2-check .sgooditem2-btn2 {
  background: #31C776;
  color: white;
}

.sgooditem2-2-1>div {
  width: 18%;
}

.zdbox {
  width: 100%;
  background: #F0F3F9;
  padding: 20px;
}

.zdbox>div:first-child {
  font-size: 12px;
  color: #999;
}

.zditems {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
}

.zditems>div {
  padding-left: 10px;
  padding-right: 10px;
  padding-top: 5px;
  padding-bottom: 5px;
  background: #E5E5E5;
  margin-right: 15px;
  margin-top: 15px;
  border-radius: 5px;
  cursor: pointer;
  transition: 0.2s;
  border: 1px solid transparent;
}

.zditems>div:hover {
  color: #2E73F3;
  background-color: #E0EBFF;
  border: 1px solid #2E73F3;
}

.zditemsadd {
  margin-top: 15px;
  height: 30px;
  line-height: 30px;
  display: flex;
}

.zditemsadd input {
  width: 100%;
  height: 100%;
  outline: none;
  border: none;
  transform: translateY(-2px);
  color: #999;
  text-align: center;
  font-size: 12px;
}

.zditemsadd>div:nth-child(1) {
  border: 1px solid #2E73F3;
  width: 105px;
  border-top-left-radius: 5px;
  border-bottom-left-radius: 5px;
  overflow: hidden;
}

.zditemsadd>div:nth-child(2) {
  border: 1px solid #2E73F3;
  width: 80px;
  overflow: hidden;
  border-top-right-radius: 5px;
  border-bottom-right-radius: 5px;
  border-left: 0;
}

.zditemsadd>div:nth-child(3) {
  border: 1px solid #2E73F3;
  width: 90px;
  margin-left: 12px;
  font-size: 12px;
  color: #2E73F3;
  border-radius: 5px;
  text-align: center;
  cursor: pointer;
}

.glbox {
  width: 98px;
  height: 28px;
  border-radius: 5px 5px 5px 5px;
  border: 1px solid #2E73F3;
  text-indent: 0;
  line-height: 28px;
  margin-top: 9px;
  margin-left: 30px;
  cursor: pointer;
  font-size: 14px;
}

.glbox>img {
  width: 18px;
  height: 18px;
  transform: translateY(3px);
  margin-left: 8px;
  font-size: 14px;
  color: #2E73F3;
}

.hsbox {
  position: absolute;
  top: 48px;
  right: 0px;
  border: 0px solid;
  height: 20px;
  display: flex;
  line-height: 20px;
  font-size: 12px;
  color: #999;
  overflow: hidden;
}

.dunjs {
  height: 40px;
  width: 100px;
  margin-left: 5px;
  margin-top: 3px;
  font-size: 14px;
  color: #2E73F3;
  text-align: center;
  border: 1px solid #2E73F3;
  border-radius: 5px;
  cursor: pointer;
  line-height: 40px;
}

.glbtn {
  height: 40px;
  width: 100px;
  margin-left: 5px;
  margin-top: 3px;
  font-size: 14px;
  color: #2E73F3;
  text-align: center;
  border: 1px solid #2E73F3;
  border-radius: 5px;
  cursor: pointer;
  line-height: 40px;
  position: absolute;
  right: -120px;
  top: 0px;
  background-color: white;
}

.glproduct {
  height: 40px;
  font-size: 14px;
  color: #2E73F3;
  text-align: left;
  border-radius: 5px;
  cursor: pointer;
  line-height: 40px;
  position: absolute;
  left: 0;
  padding-left: calc(48% * 0.2);
  top: 35px;
  width: 100%;
}

.dunjs>img {
  width: 20px;
  height: 20px;
  vertical-align: middle;
  transform: translateY(-1px);
}

.showitem3 {
  width: 48%;
  height: 46px;
  margin-bottom: 20px;
  line-height: 46px;
  display: flex;
  justify-content: space-around;
}

.showitem3>div:nth-child(1) {
  width: 40%;
  color: #999999;
  font-size: 12px;
  text-align: center;
  position: relative;
}

.showitem3>div:nth-child(2) {
  width: 60%;
}

.showitem3-title {
  width: 100%;
  color: #999;
  text-align: center;
  height: 30px;
  line-height: 30px;
  margin-bottom: 20px;
  background: #ECF3FF;
  color: #2E73F3;
}

.tsbox {
  width: 20px;
  height: 20px;
  position: absolute;
  right: -30px;
  top: 12px;
  cursor: pointer;
  background-image: url('../../../../public/imgs/疑问 2.png');
  transition: 0.3s;
}

.tsbox:hover {
  background-image: url('../../../../public/imgs/疑问 21.png');
}

.tsbox:hover>div {
  display: block;
}

.tsbox2 {
  width: 300px;
  height: 80px;
  border: 1px solid #39A8F9;
  position: absolute;
  left: 100%;
  top: -25px;
  z-index: 999;
  background-color: white;
  box-shadow: 0px 1px 12px 0px rgba(18, 39, 130, 0.15);
  transform: translateX(10px);
  display: none;
  transition: 0.3s;
  overflow: hidden;
}

.tsbox2>div:nth-child(1) {
  font-size: 12px;
  color: #333;
  font-weight: bold;
  line-height: 16px;
  margin-top: 16px;
  text-indent: 30px;
}

.tsbox2>div:nth-child(2) {
  font-size: 12px;
  color: #39A8F9;
  font-weight: bold;
  line-height: 16px;
  margin-top: 8px;
  text-indent: 30px;
}

.editicon {
  width: 15px;
  height: 15px;
  cursor: pointer;
  margin-left: 5px;
}

.kbox {
  width: 100%;
  text-align: center;
  line-height: 60px;
  border-radius: 5px;
  box-shadow: 0px 1px 12px 0px rgba(18, 39, 130, 0.15);
}
</style>
<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';

.sumbox ::v-deep {
  .tax {
    display: inline-flex;
    align-items: center;
    margin-left: 30px;
    font-size: 12px;
    color: #999;

    .el-switch__label * {
      font-size: 12px;
      // color: #999;
    }

    &-input {
      margin-left: 30px;

      .tax-check {
        padding: 2px 3px;
        border: 1px solid #ccc;
        border-radius: 50%;

        &:hover {
          border-color: #409EFF;
          background-color: #409EFF;
          color: #fff;
        }
      }
    }

    &-price {
      margin-left: 30px;
      font-size: 12px;
      color: #999;

      span {
        font-size: 16px;
        font-weight: bold;
        color: #EC2454;
      }
    }
  }
}

.kbox-input {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-top: 20px;

  ::v-deep .el-input {
    margin: 0 10px;

    .el-input__suffix {
      display: inline-flex;
      align-items: center;
    }
  }
}

.totalBox {
  width: 76%;
  height: 60px;
  line-height: 60px;
  background-color: #fff;
  margin-left: 20px;
  box-shadow: 0 0 26px 0 rgba(0, 0, 0, .05);
  display: flex;
  align-items: center;
  position: relative;
  margin-top: 20px;

  &-title {
    font-size: 16px;
    height: 100%;
    font-weight: 700;
    margin-left: 20px;
    color: #333;
  }

  &-list {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    font-size: 12px;
    color: #999;
  }

  &-item {
    margin-left: 20px;

    &-price {
      font-size: 16px;
      height: 100%;
      font-weight: 700;
      color: #ec2454;
    }

    ::v-deep .el-switch__label * {
      font-size: 12px;
    }

    &-suffix {
      padding: 2px 3px;
      border: 1px solid #ccc;
      border-radius: 50%;

      &:hover,
      &.active {
        border-color: #409EFF;
        background-color: #409EFF;
        color: #fff;
      }
    }
  }

  &-btn {
    background-color: #dcdcdc;
    color: #fff;
    cursor: pointer;
    padding: 0 50px;
    position: absolute;
    right: 0;

    &.active {
      background-color: #2e73f3;
    }
  }
}

@media(max-width:1499px) {
  .totalBox {
    &-btn {
      padding: 0 20px;
    }
  }
}

.tem-class {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  width: 98%;
  margin: 0 auto;

  .class-item {
    padding: 5px 20px;
    border: 1px solid #ccc;
    border-radius: 5px;
    font-size: 14px;
    cursor: pointer;
    position: relative;
    transition: background-color, border-color 0.3s;

    &:hover,
    &.active {
      background-color: #2e73f3;
      border-color: #2e73f3;
      color: white;
    }

    &.class-item-add {
      padding: 5px 6px;
      border-radius: 50%;
    }
  }

  .class-item+.class-item {
    margin-left: 10px;
  }
}

.contextmenu {
  margin: 0;
  background: #fff;
  z-index: 3000;
  position: fixed;
  list-style-type: none;
  padding: 5px 0;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 400;
  color: #333;
  box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, .3);

  li {
    margin: 0;
    padding: 7px 16px;
    cursor: pointer;

    &:hover {
      background: #eee;
    }
  }
}

.innerQuote-form {
  ::v-deep {
    .el-form-item__label {
      font-size: 12px;
      font-weight: normal;
    }
  }

  &-link {
    color: #2e73f3;
    cursor: pointer;

    &:hover {
      text-decoration: underline;
    }
  }
}

.custom-table-filter ::v-deep {
  .el-table__column-filter-trigger i {
    color: #2E73F3;
    transform: scale(1);
    font-size: 14px;
  }
}
</style>
