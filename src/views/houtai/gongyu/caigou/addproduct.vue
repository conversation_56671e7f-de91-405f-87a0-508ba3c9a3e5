<template>
  <el-dialog v-dialogDragBox :title="title" :visible.sync="open" :width="width">
    <el-form ref="form" :model="form" :rules="rules" label-width="80px">
      <el-row :gutter="10" style="display:flex;flex-wrap:wrap">
        <el-col :span="12">
          <el-form-item label="产品名称" prop="productName">
            <el-input v-model="form.productName" placeholder="请输入产品名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="其他名称" prop="formerName">
            <el-input v-model="form.formerName" placeholder="请输入其他名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="产品编码" prop="productCode">
            <el-input v-model="form.productCode" placeholder="请输入产品编码" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="规格" prop="specs">
            <el-input v-model="form.specs" placeholder="请输入规格" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="型号" prop="model">
            <el-input v-model="form.model" placeholder="请输入型号" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="执行标准" prop="standard">
            <el-input v-model="form.standard" placeholder="请输入执行标准"></el-input>
          </el-form-item>
        </el-col>
<!--        <el-col :span="12">-->
<!--          <el-form-item label="属性" prop="attribute">-->
<!--            <el-input v-model="form.attribute" placeholder="请输入行业属性" />-->
<!--          </el-form-item>-->
<!--        </el-col>-->
        <el-col :span="12">
          <el-form-item label="材质" prop="materialQuality">
            <el-input v-model="form.materialQuality" placeholder="请输入材质" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="表面" prop="surface">
            <el-input v-model="form.surface" placeholder="请输入表面处理方式" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="单位" prop="unit">
            <el-select v-model="form.unit" placeholder="请选择" style="width: 100%">
              <el-option v-for="(item, index) in options" :key="index" :label="item" :value="item"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="重量" prop="weight">
            <el-input v-model="form.weight" placeholder="请输入重量"><span slot="suffix">Kg</span></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="供应商">
            <treeselect multiple style="width: 100%" v-model="form.suppliersb" :options="tableDatab" :normalizer="normalizer" placeholder="请选择上级品类" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="产品图片" prop="picture1">
        <image-upload v-model="form.picture1" />
      </el-form-item>
      <el-form-item label="三维图片" prop="diagram">
        <image-upload v-model="form.diagram" />
      </el-form-item>
      <el-form-item label="产品图纸" prop="draw">
        <file-upload v-model="form.draw" :file-type="fileType" />
      </el-form-item>
      <el-form-item label="检测报告" prop="jcbg">
        <file-upload v-model="form.jcbg" :file-type="fileType" />
      </el-form-item>
      <el-form-item label="工艺视频" prop="technology">
        <file-upload v-model="form.technology" :file-type="['mp4']" :file-size="200" />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="cancel">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import { addPrivateduct } from '@/api/system/privateduct'
import { getlist } from '@/api/houtai/siyu/gongying'
import { isNumber } from '@/utils/validate'
export default {
  props: {
    width: {
      type: String,
      default: '900px'
    },
    isBack: {
      type: Boolean,
      default: false
    }
  },
  components: { Treeselect },
  data() {
    return {
      options: ['吨', '千克', '个', '件', '套', '米', '支', '根'],
      fileType: ['pdf', 'png', 'jpg', 'jpeg'],
      form: {},
      rules: {
        unit: [{ required: true, message: '请选择单位', trigger: 'change' }],
        productName: [{ required: true, message: '产品名称不能为空', trigger: 'blur' }],
        productCode: [{ required: true, message: '产品编码不能为空', trigger: 'blur' }],
        productType: [{ required: true, message: '产品类型不能为空', trigger: 'change' }],
        // attribute: [{ required: true, message: '请输入行业属性', trigger: 'blur' }],
        specs: [{ required: true, message: '规格不能为空', trigger: 'blur' }],
        model: [{ required: true, message: '型号不能为空', trigger: 'blur' }],
        materialQuality: [{ required: true, message: '材质不能为空', trigger: 'blur' }],
        surface: [{ required: true, message: '请输入表面处理方式', trigger: 'blur' }],
        // weight: [
        //   { required: true, message: '请输入产品重量', trigger: 'blur' },
        //   { validator: isNumber, message: '输入有误请重新输入', trigger: 'blur' }
        // ],
      },
      tableDatab: [],
      title: undefined,
      open: false
    }
  },
  created() {
    this.getSuppliersb()
  },
  methods: {
    // 供应商列表
    getSuppliersb() {
      getlist({ pageNum: 1, pageSize: 9999 }).then(res => {
        this.tableDatab = res.rows
      })
    },
    normalizer(node) {
      if (node.label === 'unknown') return ''
      if (node.children && !node.children.length) delete node.children
      return {
        id: node.id,
        label: node.name,
        children: node.children
      }
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        productName: null,
        productType: null,
        picture1: null,
        draw: null,
        attribute: null,
        specs: null,
        model: null,
        materialQuality: null,
        surface: null,
        weight: '0',
      }
      this.resetForm('form')
    },
    // 新增
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加产品'
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 提交按钮
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          let sendata = JSON.parse(JSON.stringify(this.form))
          sendata['supplierIds'] = sendata['suppliersb']
          sendata['product_code'] = sendata['productCode']
          sendata['product_name'] = sendata['productName']
          sendata['material_quality'] = sendata['materialQuality']
          if (this.isBack) this.$parent.handleAddAddform(sendata)
          addPrivateduct(sendata).then(response => {
            if (!this.isBack) this.$modal.msgSuccess('新增成功')
            this.open = false
          })
        }
      })
    }
  }
}
</script>
