<template>
  <div>
    <el-dialog v-dialogDragBox title="完成采购" :visible.sync="open" :width="width" class="custom-dialog" :show-close="false" :append-to-body="appendBody">
      <div style="padding: 0 20px">
        <el-descriptions class="desc" labelClassName="desc-label" title="采购清单信息" :column="2" border>
          <el-descriptions-item label="订单编号">{{ parent.serial_num }}</el-descriptions-item>
          <el-descriptions-item label="要求到场时间">{{ parent.required_time }}</el-descriptions-item>
          <el-descriptions-item label="提交时间">{{ parent.createTime }}</el-descriptions-item>
          <el-descriptions-item label="制单人">{{ parent.nick_name }}</el-descriptions-item>
          <el-descriptions-item label="合同编号">{{ form.serial }}</el-descriptions-item>
        </el-descriptions>
        <el-form ref="form" :model="form" :rules="rules" label-width="5em">
          <el-form-item label="供应商" prop="sellerName">
            <el-input v-model="form.sellerName" readonly placeholder="请输入供应商名称">
              <el-button type="primary" icon="el-icon-search" slot="append" @click="handleQuery"></el-button>
            </el-input>
          </el-form-item>
          <el-table ref="confirmTable" stripe :data="form.products" row-key="id" style="width: 100%" class="custom-table custom-table-cell0">
            <el-table-column align="center" type="index" label="序号"></el-table-column>
            <el-table-column align="center" prop="product" label="产品名称" show-overflow-tooltip>
              <template slot-scope="{ row }">
                <span class="table-link" @click="handleView(row.productId, row)">
                  <span v-if="row.source === 'common'">(公域)</span>
                  <span style="color: #fe7f22" v-else>(私域)</span>
                  {{ row.product.productName }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="unit" label="规格" align="center">
              <template slot-scope="{ row }">{{ row.hasOwnProperty('product') && row.product.hasOwnProperty('specs') ? row.product.specs : '' }}</template>
            </el-table-column>
            <el-table-column prop="unit" label="型号" align="center">
              <template slot-scope="{ row }">{{ row.hasOwnProperty('product') && row.product.hasOwnProperty('model') ? row.product.model : '' }}</template>
            </el-table-column>
            <el-table-column prop="unit" label="单位" align="center"></el-table-column>
            <el-table-column align="center" prop="quantity" label="采购数量">
              <template slot-scope="scope">
                <el-form-item label-width="0" :prop="`products.${scope.$index}.quantity`" :rules="rules.quantity">
                  <el-input v-model="scope.row.quantity" size="small" placeholder="采购数量" @change="quantityChange(scope.row)" />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="amount" label="采购价格">
              <template slot-scope="scope">
                <el-form-item label-width="0" :prop="`products.${scope.$index}.amount`" :rules="rules.amount">
                  <el-input v-model="scope.row.amount" size="small" placeholder="采购价格" @change="amountChange(scope.row)">
                    <span slot="prefix" class="inline-flex">￥</span>
                  </el-input>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="total" label="合计">
              <template slot-scope="scope">
                <el-form-item label-width="0">
                  <el-input v-model="scope.row.total" size="small" placeholder="合计" @change="totalChange(scope.row)">
                    <span slot="prefix" class="inline-flex">￥</span>
                  </el-input>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column align="center" label="操作" v-if="form.products.length !== 1">
              <template slot-scope="{ row }">
                <button type="button" class="table-btn danger" @click="handleConfirmDelete(row)">删除</button>
              </template>
            </el-table-column>
          </el-table>
        </el-form>
        <div class="confirm-total inline-flex">
          <span>
            共
            <b>{{ form.products.length }}</b>
            件产品
          </span>
          <span>
            完成日期：
            <b>{{ parseTime(new Date(), '{y}-{m}-{d}') }}</b>
          </span>
          <span>
            订单总金额：
            <b>{{ total }}</b>
          </span>
        </div>
      </div>
      <div slot="footer">
        <button type="button" class="custom-dialog-btn" @click="open = false">取消</button>
        <button type="button" class="custom-dialog-btn primary" @click="handleSubmit">确定提交</button>
      </div>
    </el-dialog>

    <!-- 产品详情 -->
    <product-dialog ref="productInfo"></product-dialog>
    <!--  选择供应商  -->
    <el-dialog v-dialogDragBox title="选择供应商" :visible.sync="supplierOpen" width="1150px">
      <div style="padding: 0 20px">
        <div class="custom-search-form flex" style="margin-top: -10px; margin-bottom: 10px">
          <input type="text" v-model="supplierParams.keyword" placeholder="请输入供应商名称或地址或联系人" class="custom-search-input" @keyup.enter="handleSupplierQuery" style="width: 250px" />
          <button type="button" class="custom-search-button pointer" @click="handleSupplierQuery">
            <i class="el-icon-search"></i>
            搜索
          </button>
        </div>
        <el-table ref="supplierTable" v-loading="supplierLoading" stripe :data="supplierList" row-key="id" style="width: 100%" class="custom-table" @row-click="clickDataType">
          <el-table-column width="60" label="单选" align="center">
            <template slot-scope="{ row }">
              <el-radio class="radio" v-model="supplierRadio" :label="row.id"><span></span></el-radio>
            </template>
          </el-table-column>
          <el-table-column align="center" type="index" label="序号"></el-table-column>
          <el-table-column align="center" prop="name" label="供应商名称" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" label="联系人" show-overflow-tooltip width="120">
            <template slot-scope="{ row }">
              {{ returnContactList(row, 'nickName') }}
            </template>
          </el-table-column>
          <el-table-column align="center" label="电话" show-overflow-tooltip width="120">
            <template slot-scope="{ row }">
              {{ returnContactList(row, 'phone') }}
            </template>
          </el-table-column>
          <el-table-column align="center" label="职务" show-overflow-tooltip width="120">
            <template slot-scope="{ row }">
              {{ returnContactList(row, 'post') }}
            </template>
          </el-table-column>
          <el-table-column align="center" prop="address" label="地址" show-overflow-tooltip>
            <template slot-scope="{ row }">{{ removeHtmlTag(row.address, 300) }}</template>
          </el-table-column>
        </el-table>
        <div class="custom-pagination">
          <pagination v-show="supplierTotal > 0" :total="supplierTotal" :page.sync="supplierParams.pageNum" :limit.sync="supplierParams.pageSize" @pagination="handleGetSupplier" />
        </div>
      </div>
      <div slot="footer">
        <button type="button" class="custom-dialog-btn" @click="supplierOpen = false">取消</button>
        <button type="button" class="custom-dialog-btn primary" :class="{ disabled: !supplierRadio }" :disabled="!supplierRadio" @click="handleSupplier">选择</button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { createContractSerial, createContractPurchasing } from '@/api/purchase'
import ProductDialog from '@/views/public/product/dialog'
import { getProduct } from '@/api/system/product'
import { getPrivateduct } from '@/api/system/privateduct'
import { isNumber, isNumberLength } from '@/utils/validate'
import { getlist } from '@/api/houtai/siyu/gongying'
import { removeHtmlTag } from '@/utils'

export default {
  components: { ProductDialog },
  props: {
    width: {
      type: String,
      default: '1150px'
    },
    appendBody: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      parent: {},
      open: false,
      form: { products: [] },
      rules: {
        sellerName: [{ required: true, message: '请输入供应商名称', trigger: ['blur', 'change'] }],
        quantity: [
          { required: true, message: '请输入采购数量', trigger: 'blur' },
          { validator: isNumber, message: '请输入正确的数量', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 10), message: '只可以填写十位小数', trigger: 'blur' }
        ],
        amount: [
          { required: true, message: '请输入采购价格', trigger: 'blur' },
          { validator: isNumber, message: '请输入正确的金额', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: 'blur' }
        ],
        total: [
          { validator: isNumber, message: '请输入正确的金额', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: 'blur' }
        ]
      },
      supplierOpen: false,
      supplierList: [],
      supplierTotal: 0,
      supplierParams: {
        pageNum: 1,
        pageSize: 10,
        keyword: undefined
      },
      supplierLoading: true,
      supplierRadio: undefined,
      supplierChecked: {}
    }
  },
  computed: {
    total() {
      let totalNum = this.form.products.reduce((total, item) => total + (Number(item.total) || 0), 0)
      return parseFloat(totalNum.toFixed(5)) || 0
    }
  },
  methods: {
    removeHtmlTag,
    reset() {
      this.form = {
        docUser: undefined,
        products: [],
        seller: undefined,
        sellerName: undefined,
        serial: undefined
      }
      this.resetForm('form')
    },
    handleOver(parent = {}) {
      this.reset()
      createContractSerial().then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          this.form.serial = data.serial
          this.form.docUser = parent.nick_name
          parent.list.map(item => {
            this.form.products.push({
              product: item.product,
              amount: undefined,
              productId: item.productId,
              purchasingListId: item.id,
              quantity: parseFloat((Number(item.quantity) - Number(item.purchasedQuantity)).toFixed(5)) || 0,
              source: item.source,
              unit: item.unit,
              total: undefined
            })
          })
          this.parent = parent
          this.open = true
        } else this.$modal.msgError(msg)
      })
    },
    // 批量
    handleOvers(arr = []) {
      this.reset()
      createContractSerial().then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          this.form.serial = data.serial
          const serial_num_arr = this.backArrayVal(arr, 'serialNum')
          const serial_num = Array.from(new Set(serial_num_arr)).toString()
          const required_time_arr = this.backArrayVal(arr, 'requiredTime')
          const required_time = Array.from(new Set(required_time_arr)).toString()
          const createTime_arr = this.backArrayVal(arr, 'createTime')
          const createTime = Array.from(new Set(createTime_arr)).toString()
          const nick_name_arr = this.backArrayVal(arr, 'nick_name')
          const nick_name = Array.from(new Set(nick_name_arr)).toString()
          this.form.docUser = nick_name
          arr.map(item => {
            this.form.products.push({
              product: item.product,
              amount: undefined,
              total: undefined,
              productId: item.productId,
              purchasingListId: item.id,
              quantity: parseFloat((Number(item.quantity) - Number(item.purchasedQuantity)).toFixed(5)) || 0,
              source: item.source,
              unit: item.unit,
              total: undefined
            })
          })
          this.parent = { serial_num, required_time, createTime, nick_name }
          this.open = true
        } else this.$modal.msgError(msg)
      })
    },
    // 返回数组指定字段的值
    backArrayVal(arr = [], str = '') {
      if (arr.length) return arr.map(item => item[str])
    },
    // 选择供应商
    handleQuery() {
      this.supplierRadio = this.form.seller || undefined
      this.supplierParams.keyword = undefined
      this.handleSupplierQuery()
      this.supplierOpen = true
    },
    // 查询供应商
    handleGetSupplier() {
      this.supplierLoading = true
      getlist(this.supplierParams).then(res => {
        const { code, msg, rows, total } = res
        if (code === 200) {
          this.supplierList = rows
          this.supplierTotal = total
          this.supplierLoading = false
        } else this.$message.error(msg)
      })
    },
    handleSupplierQuery() {
      this.supplierParams.pageNum = 1
      this.handleGetSupplier()
    },
    // 返回联系人信息
    returnContactList(row, val) {
      let index = row.contactList.findIndex(item => item.checked)
      if (index === -1) index = 0
      return row.contactList[index][val]
    },
    clickDataType(row) {
      this.supplierChecked = row
      this.supplierRadio = row.id
    },
    handleSupplier() {
      this.form.seller = this.supplierChecked.id
      this.form.sellerName = this.supplierChecked.name
      let index = this.supplierChecked.contactList.findIndex(item => item.checked)
      if (index === -1) index = 0
      this.form.sendUser = this.supplierChecked.contactList[index].nickName
      this.form.sendPhone = this.supplierChecked.contactList[index].phone
      this.supplierOpen = false
    },
    // 提交
    handleSubmit() {
      this.$refs['form'].validate((valid, errors) => {
        if (valid) {
          createContractPurchasing(this.form).then(res => {
            const { code, msg } = res
            if (code === 200) {
              this.$message.success('操作成功')
              this.open = false
              this.$emit('callback')
            } else this.$message.error(msg)
          })
        } else {
          let errorMsg = ''
          // 遍历错误对象
          for (let field in errors) {
            errors[field].forEach(error => {
              errorMsg += field.indexOf('.') > -1 ? '产品第' + (Number(field.split('.')[1]) + 1) + '行：' + error.message + '<br />' : error.message + '<br />'
            })
          }
          this.$alert(errorMsg, '错误提示', {
            confirmButtonText: '确定',
            dangerouslyUseHTMLString: true
          })
        }
      })
    },
    // 产品详情
    handleView(Id, item) {
      if (item.source === 'common') {
        getProduct(Id).then(res => {
          this.$refs.productInfo.handleView(res.data)
        })
      } else {
        getPrivateduct(Id).then(res => {
          this.$refs.productInfo.handleView(res.data)
        })
      }
    },
    handleConfirmDelete(row) {
      const idx = this.form.products.findIndex(item => item.productId === row.productId)
      this.form.products.splice(idx, 1)
    },
    // 采购数量
    quantityChange(row) {
      row.total = parseFloat((Number(row.amount) * Number(row.quantity)).toFixed(5)) || 0
      this.$set(row, 'total', row.total)
    },
    // 采购价格
    amountChange(row) {
      row.total = parseFloat((Number(row.amount) * Number(row.quantity)).toFixed(5)) || 0
      this.$set(row, 'total', row.total)
    },
    // 合计
    totalChange(row) {
      row.amount = parseFloat(Number(row.total / row.quantity).toFixed(5)) || 0
      this.$set(row, 'amount', row.amount)
    }
  }
}
</script>
<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';
.desc {
  margin: 0 0 20px;
  ::v-deep {
    .desc-label {
      width: calc(6.5em + 20px) !important;
    }
  }
}
.custom-table ::v-deep {
  .el-form-item {
    margin-top: 10px !important;
    margin-bottom: 10px !important;
    .el-form-item__error {
      top: 95%;
      padding-top: 0;
    }
  }
}
.confirm-total {
  margin-top: 10px;
  background-color: #fbe4ea;
  border: 1px solid $red;
  border-radius: 5px;
  width: 100%;
  height: 48px;
  padding: 0 20px;
  font-size: 14px;
  color: $font;
  span {
    margin-right: 50px;
  }
  b {
    font-size: 18px;
    font-weight: 500;
    color: $red;
  }
}
</style>
