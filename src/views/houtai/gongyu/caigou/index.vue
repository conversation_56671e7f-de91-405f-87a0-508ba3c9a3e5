<template>
  <div class="newBox bgcf9 vh-85">
    <!-- 搜索 -->
    <div class="custom-search flex">
      <div class="custom-search-form flex">
        <input type="text" v-model="queryParams.keyword" placeholder="请输入产品名称,规格,编号" class="custom-search-input" @keyup.enter="handleQuery" />
        <button type="button" class="custom-search-button pointer" @click="handleQuery">
          <i class="el-icon-search"></i>
          搜索
        </button>
      </div>
      <button type="button" class="custom-search-add pointer" @click="handleAdd" v-hasPermi="['purchasing:list:add']">
        <i class="el-icon-plus"></i>
        新增采购产品
      </button>
    </div>

    <!-- 分类 -->
    <div class="classify flex">
      <div class="classify-item" :class="{ active: item.value === classifyName }" v-for="item in classifyOptions" :key="item.value" @click="handleCategory(item)">
        {{ item.label }}
      </div>
      <div class="classify-select" v-if="classifyName === 'all'">
        <el-select v-model="tipRuleType" @change="handleQuery" size="small">
          <el-option v-for="item in tipRuleTypeOptions" :key="item.tipType" :label="item.label" :value="item.tipType"></el-option>
        </el-select>
      </div>
      <div class="classify-refresh">
        <el-tooltip content="刷新">
          <el-button plain circle size="mini" icon="el-icon-refresh" @click="handleRefresh"></el-button>
        </el-tooltip>
      </div>
    </div>

    <!-- 列表 -->
    <div class="tableBox">
      <el-row :gutter="10" class="ptb15">
        <el-col :span="1.5">
          <el-button type="info" plain icon="el-icon-sort" size="mini" @click="toggleExpandAll">展开/折叠</el-button>
        </el-col>
        <el-col :span="1.5" v-if="classifyName === 'all'">
          <el-button type="danger" :plain="!isPost" size="mini" icon="el-icon-alarm-clock" @click="getPastList">{{ isPost ? '全部订单' : '已过期订单' }}</el-button>
        </el-col>
      </el-row>
      <template v-if="total > 0">
        <el-table v-loading="loading" ref="groupTable" stripe :data="list" row-key="ids" :key="key" style="width: 100%" class="custom-table" @selection-change="handleAllSelect" :default-expand-all="isExpand" v-if="classifyName === 'group' && !isPost">
          <el-table-column type="expand" width="30">
            <template slot-scope="props">
              <div class="table-expand">
                <el-table :data="props.row.list" :key="'list' + key" style="width: 100%" class="custom-table">
                  <el-table-column align="center" type="index" label="序号"></el-table-column>
                  <el-table-column align="center" prop="serialNum" label="订单编号" show-overflow-tooltip></el-table-column>
                  <el-table-column align="center" prop="productName" label="产品名称" min-width="150" show-overflow-tooltip>
                    <template slot-scope="{ row }">
                      <span class="table-link" @click="handleView(row.productId, row)">
                        <span v-if="row.source === 'common'">(公域)</span>
                        <span style="color: #fe7f22" v-else>(私域)</span>
                        {{ row.productName }}
                      </span>
                    </template>
                  </el-table-column>
                  <el-table-column align="center" label="规格" show-overflow-tooltip>
                    <template slot-scope="{ row }">
                      <span v-if="props.row.product">{{ props.row.product.specs }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column align="center" label="型号" show-overflow-tooltip>
                    <template slot-scope="{ row }">
                      <span v-if="props.row.product">{{ props.row.product.model }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column align="center" prop="unit" label="单位" width="50" show-overflow-tooltip></el-table-column>
                  <el-table-column align="center" prop="quantity" label="计划采购数量" show-overflow-tooltip></el-table-column>
                  <el-table-column align="center" prop="requiredTime" label="要求到厂时间" show-overflow-tooltip></el-table-column>
                  <el-table-column align="center" prop="recommendPrice" label="建议价格" show-overflow-tooltip>
                    <template slot-scope="{ row }">
                      <span class="table-price">{{ row.recommendPrice ? '￥' + row.recommendPrice : '' }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column align="center" prop="note" label="备注" show-overflow-tooltip></el-table-column>
                  <el-table-column align="center" label="操作" width="220px" v-if="checkPermi(['purchasing:list:del'])">
                    <template slot-scope="{ row }">
                      <button type="button" class="table-btn danger" @click="handleDelete(row)" v-hasPermi="['purchasing:list:del']">删除</button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </template>
          </el-table-column>
          <el-table-column align="center" type="selection" width="50"></el-table-column>
          <el-table-column align="center" type="index" label="序号"></el-table-column>
          <el-table-column align="center" label="产品名称" min-width="150" show-overflow-tooltip>
            <template slot-scope="{ row }">
              <span class="table-link" @click="handleView(row.product_id, row)">
                <span v-if="row.source === 'common'">(公域)</span>
                <span style="color: #fe7f22" v-else>(私域)</span>
                {{ row.product_name }}
              </span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="历史供应商" show-overflow-tooltip>
            <template slot-scope="{ row }">
              <template v-if="hasOwn(row.product, 'suppliers') && row.product.suppliers.length">
                <el-tooltip effect="dark" :content="row.product.suppliers.map(item => item.supplierName || item.name).toString()" placement="top">
                  <el-button type="text" @click="handleSupllier(row)">{{ !!row.product.suppliers && row.product.suppliers.length ? row.product.suppliers.length + '家' : '' }}</el-button>
                </el-tooltip>
              </template>
            </template>
          </el-table-column>
          <el-table-column align="center" label="订单编号" show-overflow-tooltip>
            <template slot-scope="{ row }">{{ row.serials }}</template>
          </el-table-column>
          <el-table-column align="center" label="规格" show-overflow-tooltip>
            <template slot-scope="{ row }">
              <span v-if="row.product">{{ row.product.specs }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="型号" show-overflow-tooltip>
            <template slot-scope="{ row }">
              <span v-if="row.product">{{ row.product.model }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="单位" width="50" show-overflow-tooltip>
            <template slot-scope="{ row }">{{ row.unit }}</template>
          </el-table-column>
          <el-table-column align="center" prop="quantity" label="计划采购数量" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="recommendPrice" label="建议价格" show-overflow-tooltip>
            <template slot-scope="{ row }">
              <span class="table-price">{{ recommendFormat(row.recommends) }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="remarks" label="备注" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="createTime" label="提交时间" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" label="操作" width="220px" v-if="checkPermi(['purchasing:demand:add'])">
            <template slot-scope="{ row }">
              <button type="button" class="table-btn primary" @click="handleConfirm(row)" v-hasPermi="['purchasing:demand:add']">生成需求</button>
            </template>
          </el-table-column>
        </el-table>

        <el-table v-loading="loading" ref="allTable" stripe :data="list" :key="'other' + key" style="width: 100%; margin-bottom: 20px" class="custom-table" :row-class-name="getRowClassName" :header-row-class-name="getHeaderRowClassName" @select-all="mainSelectAll" @select="mainSelect" @expand-change="handleExpandChange" :default-expand-all="isExpand" v-if="isPost">
          <el-table-column type="expand" width="30">
            <template slot-scope="props">
              <div class="table-expand">
                <el-table :ref="`allprop${props.$index}`" :data="props.row.list" :key="'list' + key" style="width: 100%" class="custom-table" :header-cell-class-name="hideHeaderCheckBox" @select="selection => subSelect(props.$index, selection)" @select-all="selection => subSelectAll(props.$index, selection)">
                  <el-table-column type="selection" width="50" align="center"></el-table-column>
                  <el-table-column align="center" type="index" label="序号"></el-table-column>
                  <el-table-column align="center" prop="productName" label="产品名称" show-overflow-tooltip>
                    <template slot-scope="{ row }">
                      <span class="table-link" @click="handleView(row.productId, row)">
                        <span v-if="row.source === 'common'">(公域)</span>
                        <span style="color: #fe7f22" v-else>(私域)</span>
                        {{ row.productName }}
                      </span>
                    </template>
                  </el-table-column>
                  <el-table-column align="center" label="规格" show-overflow-tooltip>
                    <template slot-scope="{ row }">
                      <span v-if="row.product">{{ row.product.specs }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column align="center" label="型号" show-overflow-tooltip>
                    <template slot-scope="{ row }">
                      <span v-if="row.product">{{ row.product.model }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column align="center" prop="unit" label="单位" width="50" show-overflow-tooltip></el-table-column>
                  <el-table-column align="center" prop="quantity" label="计划采购数量" show-overflow-tooltip></el-table-column>
                  <el-table-column align="center" prop="requiredTime" label="要求到厂时间" show-overflow-tooltip></el-table-column>
                  <el-table-column align="center" prop="recommendPrice" label="建议价格" show-overflow-tooltip>
                    <template slot-scope="{ row }">
                      <span class="table-price">{{ row.recommendPrice ? '￥' + row.recommendPrice : '' }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column align="center" prop="note" label="备注" show-overflow-tooltip></el-table-column>
                  <el-table-column align="center" label="操作" width="220px" v-if="checkPermi(['purchasing:list:del'])">
                    <template slot-scope="{ row }">
                      <button type="button" class="table-btn danger" @click="handleDelete(row)" v-hasPermi="['purchasing:list:del']">删除</button>
                      <button type="button" class="table-btn primary" @click="handleConfirm(row, props.row)" v-hasPermi="['purchasing:demand:add']">生成需求</button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </template>
          </el-table-column>
          <el-table-column type="selection" width="50" align="center"></el-table-column>
          <el-table-column align="center" type="index" label="序号"></el-table-column>
          <el-table-column align="center" label="订单编号" prop="serial_num" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="required_time" label="要求到厂时间" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="createTime" label="提交时间" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="nick_name" label="制单人" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="createTime" label="订单状态" show-overflow-tooltip>
            <template slot-scope="{ row }">
              <div :style="{ color: rulesFormat(row, 'class') }" class="status">
                <i :style="{ background: rulesFormat(row, 'class') }"></i>
                <span>{{ rulesFormat(row) }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column align="center" label="操作" width="330" v-if="checkPermi(['purchasing:list:del']) || checkPermi(['purchasing:demand:add'])">
            <template slot-scope="{ row }">
              <button type="button" class="table-btn primary" @click="handleOverGroup(row)" v-hasPermi="['purchasing:demand:add']">其他采购</button>
              <button type="button" class="table-btn primary" @click="handleConfirmGroup(row)" v-hasPermi="['purchasing:demand:add']">生成需求</button>
              <button type="button" class="table-btn primary" @click="handleAdd(row)" v-hasPermi="['purchasing:list:add']">添加产品</button>
            </template>
          </el-table-column>
        </el-table>

        <el-table v-loading="loading" ref="allTable" stripe :data="list" :key="'group' + key" row-key="ids" style="width: 100%" class="custom-table" :row-class-name="getRowClassName" :header-row-class-name="getHeaderRowClassName" @select-all="mainSelectAll" @select="mainSelect" @expand-change="handleExpandChange" :default-expand-all="isExpand" v-if="classifyName === 'all' && !isPost">
          <el-table-column type="expand" width="30">
            <template slot-scope="props">
              <div class="table-expand">
                <el-table :ref="`allprop${props.$index}`" :data="props.row.list" :key="'list' + key" style="width: 100%" class="custom-table" :header-cell-class-name="hideHeaderCheckBox" @select="selection => subSelect(props.$index, selection)" @select-all="selection => subSelectAll(props.$index, selection)">
                  <el-table-column type="selection" width="50" align="center"></el-table-column>
                  <el-table-column align="center" type="index" label="序号"></el-table-column>
                  <el-table-column align="center" prop="productName" label="产品名称" show-overflow-tooltip>
                    <template slot-scope="{ row }">
                      <span class="table-link" @click="handleView(row.productId, row)">
                        <span v-if="row.source === 'common'">(公域)</span>
                        <span style="color: #fe7f22" v-else>(私域)</span>
                        {{ row.productName }}
                      </span>
                    </template>
                  </el-table-column>
                  <el-table-column align="center" label="规格" show-overflow-tooltip>
                    <template slot-scope="{ row }">
                      <span v-if="row.product">{{ row.product.specs }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column align="center" label="型号" show-overflow-tooltip>
                    <template slot-scope="{ row }">
                      <span v-if="row.product">{{ row.product.model }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column align="center" prop="unit" label="单位" width="50" show-overflow-tooltip></el-table-column>
                  <el-table-column align="center" prop="quantity" label="计划采购数量" show-overflow-tooltip></el-table-column>
                  <el-table-column align="center" prop="purchasedQuantity" label="已采购数量" show-overflow-tooltip></el-table-column>
                  <el-table-column align="center" prop="requiredTime" label="要求到厂时间" show-overflow-tooltip></el-table-column>
                  <el-table-column align="center" prop="recommendPrice" label="建议价格" show-overflow-tooltip>
                    <template slot-scope="{ row }">
                      <span class="table-price">{{ row.recommendPrice ? '￥' + row.recommendPrice : '' }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column align="center" prop="note" label="备注" show-overflow-tooltip></el-table-column>
                  <el-table-column align="center" label="操作" width="220px" v-if="checkPermi(['purchasing:list:del'])">
                    <template slot-scope="{ row }">
                      <button type="button" class="table-btn danger" @click="handleDelete(row)" v-hasPermi="['purchasing:list:del']">删除</button>
                      <button type="button" class="table-btn primary" @click="handleConfirm(row, props.row)" v-hasPermi="['purchasing:demand:add']">生成需求</button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </template>
          </el-table-column>
          <el-table-column type="selection" width="50" align="center"></el-table-column>
          <el-table-column align="center" type="index" label="序号"></el-table-column>
          <el-table-column align="center" label="订单编号" prop="serial_num" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="required_time" label="要求到厂时间" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="createTime" label="提交时间" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="nick_name" label="制单人" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="createTime" label="订单状态" show-overflow-tooltip>
            <template slot="header">
              <span style="padding-right: 5px">订单状态</span>
              <el-button plain size="mini" icon="el-icon-setting" @click="handleRules">自定义状态</el-button>
            </template>
            <template slot-scope="{ row }">
              <div :style="{ color: rulesFormat(row, 'class') }" class="status">
                <i :style="{ background: rulesFormat(row, 'class') }"></i>
                <span>{{ rulesFormat(row) }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column align="center" label="操作" width="330" v-if="checkPermi(['purchasing:list:del']) || checkPermi(['purchasing:demand:add'])">
            <template slot-scope="{ row }">
              <button type="button" class="table-btn primary" @click="handleOverGroup(row)" v-hasPermi="['purchasing:demand:add']">其他采购</button>
              <button type="button" class="table-btn primary" @click="handleConfirmGroup(row)" v-hasPermi="['purchasing:demand:add']">生成需求</button>
              <button type="button" class="table-btn primary" @click="handleAdd(row)" v-hasPermi="['purchasing:list:add']">添加产品</button>
            </template>
          </el-table-column>
        </el-table>

        <el-table v-loading="loading" ref="overTable" stripe :data="list" :key="'over' + key" style="width: 100%" class="custom-table" @selection-change="handleAllSelectGruop" :default-expand-all="isExpand" v-if="classifyName === 'over' && !isPost">
          <el-table-column type="expand" width="30">
            <template slot-scope="props">
              <div class="table-expand">
                <el-table :ref="`overprop${props.$index}`" :data="props.row.list" :key="'list' + key" style="width: 100%" class="custom-table">
                  <el-table-column align="center" type="index" label="序号"></el-table-column>
                  <el-table-column align="center" prop="productName" label="产品名称" show-overflow-tooltip>
                    <template slot-scope="{ row }">
                      <span class="table-link" @click="handleView(row.productId, row)">
                        <span v-if="row.source === 'common'">(公域)</span>
                        <span style="color: #fe7f22" v-else>(私域)</span>
                        {{ row.productName }}
                      </span>
                    </template>
                  </el-table-column>
                  <el-table-column align="center" label="规格" show-overflow-tooltip>
                    <template slot-scope="{ row }">
                      <span v-if="row.product">{{ row.product.specs }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column align="center" label="型号" show-overflow-tooltip>
                    <template slot-scope="{ row }">
                      <span v-if="row.product">{{ row.product.model }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column align="center" prop="unit" label="单位" width="50" show-overflow-tooltip></el-table-column>
                  <el-table-column align="center" prop="quantity" label="计划采购数量" show-overflow-tooltip></el-table-column>
                  <el-table-column align="center" prop="requiredTime" label="要求到厂时间" show-overflow-tooltip></el-table-column>
                  <el-table-column align="center" prop="recommendPrice" label="建议价格" show-overflow-tooltip>
                    <template slot-scope="{ row }">
                      <span class="table-price">{{ row.recommendPrice ? '￥' + row.recommendPrice : '' }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column align="center" prop="note" label="备注" show-overflow-tooltip></el-table-column>
                </el-table>
              </div>
            </template>
          </el-table-column>
          <el-table-column align="center" type="index" label="序号"></el-table-column>
          <el-table-column align="center" label="订单编号" prop="serial_num" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="required_time" label="要求到厂时间" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="createTime" label="提交时间" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="nick_name" label="制单人" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" label="合同状态" show-overflow-tooltip>
            <template slot-scope="{ row }">
              <div v-html="statusFormat(row)"></div>
            </template>
          </el-table-column>
          <el-table-column align="center" label="操作" width="330">
            <template slot-scope="{ row }">
              <button type="button" class="table-btn primary" @click="handleViewContract(row)" v-if="row.hasContract">查看合同</button>
            </template>
          </el-table-column>
        </el-table>

        <div class="custom-pagination">
          <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" local="OrderPageSize" @pagination="getList" />
        </div>
      </template>
      <el-empty :description="!loading && !total ? '暂无数据' : '加载中…'" v-else />
    </div>

    <!-- 产品详情 -->
    <product-dialog ref="productInfo"></product-dialog>

    <!-- 新增采购产品 -->
    <el-dialog v-dialogDragBox title="新增采购产品" :visible.sync="addOpen" width="1150px" class="custom-dialog" :fullscreen="isFullscreen">
      <dialog-header slot="title" dialog-tittle="新增采购产品" :fullscreen="isFullscreen" @is-fullscreen="onFullscreen"></dialog-header>
      <template v-if="addStep === 1">
        <div class="addBox">
          <div class="add-search flex" style="margin-bottom: 0">
            <el-form ref="addQueryForm" :model="addQuery" inline size="small" @submit.native.prevent>
              <el-form-item label="名称" prop="productName">
                <el-input v-model="addQuery.productName" placeholder="请输入产品名称" clearable @keyup.enter.native="handleAddQuery" style="width: auto" />
              </el-form-item>
              <el-form-item label="编码" prop="productCode">
                <el-input v-model="addQuery.productCode" placeholder="请输入产品编码" clearable @keyup.enter.native="handleAddQuery" />
              </el-form-item>
              <el-form-item label="规格" prop="specs">
                <el-input v-model="addQuery.specs" placeholder="请输入产品规格" clearable @keyup.enter.native="handleAddQuery" />
              </el-form-item>
              <el-form-item label="型号" prop="model">
                <el-input v-model="addQuery.model" placeholder="请输入产品型号" clearable @keyup.enter.native="handleAddQuery" />
              </el-form-item>
              <el-form-item label="材质" prop="materialQuality">
                <el-input v-model="addQuery.materialQuality" placeholder="请输入产品材质" clearable @keyup.enter.native="handleAddQuery" />
              </el-form-item>
              <el-form-item label="表面" prop="surface">
                <el-input v-model="addQuery.surface" placeholder="请输入表面处理" clearable @keyup.enter.native="handleAddQuery" />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="small" @click="handleAddQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="small" @click="resetAddQuery">重置</el-button>
                <el-button type="danger" icon="el-icon-plus" size="small" @click="handleAddProduct" v-hasPermi="['system:privateduct:adds']">新增产品</el-button>
                <el-button type="primary" icon="el-icon-plus" size="small" @click="handleAddContract">从销售合同添加</el-button>
                <el-button type="primary" icon="el-icon-plus" size="small" @click="handleAddInputContract">从录入合同添加</el-button>
              </el-form-item>
            </el-form>
          </div>
          <div class="table-tip" v-if="isNoaddQuery">
            <i class="el-icon-info"></i>
            <span>提示：当前显示为常用采购产品，如需采购其他产品，请输入名称关键词、编码关键词等进行搜索</span>
          </div>
          <el-table v-loading="addLoading" ref="addTable" stripe :data="addList" :row-key="getRowKey" style="width: 100%" :max-height="addHeight" class="custom-table custom-table-cell5" @select="handleSelectProduct" @select-all="handleSelectProductAll">
            <el-table-column align="center" type="selection" width="50" :reserve-selection="true"></el-table-column>
            <el-table-column align="center" type="index" label="序号"></el-table-column>
            <el-table-column align="center" prop="product_name" label="产品名称" show-overflow-tooltip>
              <template slot-scope="{ row }">
                <span class="table-link" @click="handleView(row.id, row)" v-if="row.id">
                  <span v-if="row.source === 'common'">(公域)</span>
                  <span style="color: #fe7f22" v-else>(私域)</span>
                  {{ row.product_name }}
                </span>
                <span class="table-link" v-else>
                  <span style="color: #fe7f22">(私域)</span>
                  {{ row.product_name }}
                </span>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="picture1" label="图片" width="75">
              <template slot-scope="{ row }">
                <el-button type="text" size="mini" icon="el-icon-view" @click="handleImgView(row)">查看</el-button>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="specs" label="规格" show-overflow-tooltip :filters="specsFilters" :filter-method="filterSpecs"></el-table-column>
            <el-table-column align="center" prop="model" label="型号" show-overflow-tooltip :filters="modelFilters" :filter-method="filterModel"></el-table-column>
            <el-table-column align="center" prop="product_code" label="产品编码" show-overflow-tooltip></el-table-column>
            <el-table-column align="center" prop="material_quality" label="材质" show-overflow-tooltip :filters="materialFilter" :filter-method="filterQuality"></el-table-column>
            <el-table-column align="center" prop="unit" label="单位" width="50"></el-table-column>
            <el-table-column align="center" label="操作">
              <template slot-scope="{ row }">
                <el-button type="text" size="mini" icon="el-icon-circle-check" @click="handleChooseProduct(row)">采购</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div slot="footer">
          <div class="inline-flex">
            <button type="button" class="custom-dialog-btn" @click="addOpen = false">取消</button>
            <el-badge :value="addForm.purchasingProducts.length" v-if="addForm.purchasingProducts.length">
              <button type="button" class="custom-dialog-btn primary" @click="handleNext">下一步</button>
            </el-badge>
          </div>
        </div>
      </template>
      <template v-if="addStep === 2">
        <div class="addBox">
          <el-form ref="addForm" :model="addForm" :rules="addRules" label-position="left" label-width="100px">
            <el-row :gutter="18">
              <el-col :span="12">
                <el-form-item label="订单编号" prop="serialNum">
                  <el-input v-model="addForm.serialNum" placeholder="请输入订单编号" :disabled="isAdd"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="订单日期" prop="date">
                  <el-date-picker v-model="addForm.date" type="date" placeholder="请选择订单日期" style="width: 100%" value-format="timestamp" :disabled="isAdd"></el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="要求到厂时间" prop="requiredTime">
                  <el-date-picker v-model="addForm.requiredTime" type="date" placeholder="请选择要求到厂时间" style="width: 100%" :picker-options="dateOption" value-format="timestamp" :disabled="isAdd"></el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="备注" prop="remark">
                  <el-input v-model="addForm.remark" placeholder="请输入备注" :disabled="isAdd"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-table ref="addSubTable" stripe :data="addForm.purchasingProducts" row-key="id" style="width: 100%" :max-height="addFormHeight" class="custom-table">
                  <el-table-column align="center" prop="product_name" label="产品名称" show-overflow-tooltip>
                    <template slot-scope="{ row }">
                      <span class="table-link" @click="handleView(row.id, row)" v-if="row.id">
                        <span v-if="row.source === 'common'">(公域)</span>
                        <span style="color: #fe7f22" v-else>(私域)</span>
                        {{ row.product_name }}
                      </span>
                      <span class="table-link" v-else>
                        <span style="color: #fe7f22">(私域)</span>
                        {{ row.product_name }}
                      </span>
                    </template>
                  </el-table-column>
                  <el-table-column align="center" prop="picture1" label="图片" width="75">
                    <template slot-scope="{ row }">
                      <el-image :src="formatProductImg(row)" fit="cover" @click="handleImgView(row)">
                        <div slot="error" class="image-slot">
                          <i class="el-icon-picture-outline"></i>
                        </div>
                      </el-image>
                    </template>
                  </el-table-column>
                  <el-table-column align="center" prop="specs" label="规格" show-overflow-tooltip width="80"></el-table-column>
                  <el-table-column align="center" prop="model" label="型号" show-overflow-tooltip width="80"></el-table-column>
                  <el-table-column align="center" prop="product_code" label="产品编码" show-overflow-tooltip width="110"></el-table-column>
                  <el-table-column align="center" prop="material_quality" label="材质" show-overflow-tooltip width="80"></el-table-column>
                  <el-table-column align="center" prop="unit" label="单位" width="100">
                    <template slot-scope="scope">
                      <el-form-item label-width="0" :prop="`purchasingProducts.${scope.$index}.unit`" :rules="addRules.unit">
                        <el-select size="small" v-model="scope.row.unit" filterable allow-create default-first-option placeholder="请选择单位">
                          <el-option v-for="item in unitoptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                        </el-select>
                      </el-form-item>
                    </template>
                  </el-table-column>
                  <el-table-column align="center" prop="quantity" label="采购数量" width="110">
                    <template slot-scope="scope">
                      <el-form-item label-width="0" :prop="`purchasingProducts.${scope.$index}.quantity`" :rules="addRules.quantity">
                        <el-input v-model="scope.row.quantity" size="small" placeholder="采购数量" />
                      </el-form-item>
                    </template>
                  </el-table-column>
                  <el-table-column align="center" prop="stock" label="库存" v-if="isContract">
                    <template slot-scope="{ row }">
                      <span v-if="row.materialCode">{{ row.stock }}</span>
                      <span v-else>-</span>
                    </template>
                  </el-table-column>
                  <el-table-column align="center" prop="recommendPrice" label="建议采购价" width="120">
                    <template slot-scope="scope">
                      <el-form-item label-width="0" :prop="`purchasingProducts.${scope.$index}.recommendPrice`" :rules="addRules.recommendPrice">
                        <el-input v-model="scope.row.recommendPrice" size="small" placeholder="建议采购价">
                          <span slot="prefix" class="inline-flex">￥</span>
                        </el-input>
                      </el-form-item>
                    </template>
                  </el-table-column>
                  <el-table-column align="center" prop="note" label="备注" width="120">
                    <template slot-scope="scope">
                      <el-form-item label-width="0" :prop="`purchasingProducts.${scope.$index}.note`" :rules="addRules.note">
                        <el-input v-model="scope.row.note" size="small" placeholder="备注"></el-input>
                      </el-form-item>
                    </template>
                  </el-table-column>
                  <el-table-column align="center" label="操作" width="110" v-if="addForm.purchasingProducts && addForm.purchasingProducts.length > 1">
                    <template slot-scope="{ row }">
                      <button type="button" class="table-btn danger" @click="handleDeleteProduct(row)">删除</button>
                    </template>
                  </el-table-column>
                </el-table>
              </el-col>
            </el-row>
          </el-form>
        </div>
        <div slot="footer">
          <button type="button" class="custom-dialog-btn" @click="handlePrev" v-if="!isContract">上一步</button>
          <button type="button" class="custom-dialog-btn primary" @click="handleSubmit">添加采购</button>
        </div>
      </template>
    </el-dialog>

    <!-- 确认需求 -->
    <el-dialog v-dialogDragBox title="确认需求" :visible.sync="confirmOpen" width="1150px" class="custom-dialog" @close="handleConfirmCancel">
      <div class="addBox">
        <el-form ref="confirmForm" :model="confirmForm" :rules="confirmRules" label-position="left" label-width="110px">
          <el-row :gutter="18">
            <el-col :span="24">
              <el-form-item label="采购标题" prop="title">
                <el-input v-model="confirmForm.title" placeholder="请输入采购标题"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="备注" prop="remark">
                <el-input v-model="confirmForm.remark" placeholder="请输入备注"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="供货截止时间" prop="supplyEndTime">
                <el-date-picker v-model="confirmForm.supplyEndTime" type="date" placeholder="请选择供货截止时间" style="width: 100%" :picker-options="dateOption" value-format="yyyy-MM-dd"></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="有效时间" prop="expireTime">
                <el-input-number v-model="confirmForm.expireTime" controls-position="right" step-strictly :min="1"></el-input-number>
                <span style="color: #999999">（小时）</span>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-table ref="confirmTable" stripe :data="confirmForm.products" row-key="id" style="width: 100%" :max-height="confirmFormHeight" class="custom-table custom-table-cell0">
                <el-table-column align="center" type="index" label="序号"></el-table-column>
                <el-table-column align="center" prop="productName" label="产品名称" show-overflow-tooltip min-width="150">
                  <template slot-scope="{ row }">
                    <span class="table-link" style="padding: 30px 0" @click="handleView(row.productId, row)">{{ row.productName }}</span>
                  </template>
                </el-table-column>
                <el-table-column align="center" prop="specs" label="规格">
                  <template slot-scope="{ row }">
                    <span v-if="row.hasOwnProperty('product')">{{ row.product.specs }}</span>
                  </template>
                </el-table-column>
                <el-table-column align="center" prop="model" label="型号">
                  <template slot-scope="{ row }">
                    <span v-if="row.hasOwnProperty('product')">{{ row.product.model }}</span>
                  </template>
                </el-table-column>
                <el-table-column align="center" prop="unit" label="单位" show-overflow-tooltip width="50">
                  <template slot-scope="scope">
                    <template v-if="scope.row.isEdit">
                      <el-form-item label-width="0" :prop="`products.${scope.$index}.unit`" :rules="confirmRules.unit">
                        <el-select size="small" v-model="scope.row.unit" filterable allow-create default-first-option placeholder="请选择单位" style="width: 100%">
                          <el-option v-for="item in unitoptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                        </el-select>
                      </el-form-item>
                    </template>
                    <span v-else>{{ scope.row.unit }}</span>
                  </template>
                </el-table-column>
                <el-table-column align="center" prop="quantity" label="计划采购数量" show-overflow-tooltip></el-table-column>
                <el-table-column align="center" prop="needQuantity" label="实际采购数量">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`products.${scope.$index}.needQuantity`" :rules="confirmRules.needQuantity">
                      <el-input v-model="scope.row.needQuantity" size="small" placeholder="请输入实际采购数量" />
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column align="center" prop="remark" label="备注">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`products.${scope.$index}.remark`" :rules="confirmRules.remark">
                      <el-input v-model="scope.row.remark" size="small" placeholder="请输入备注" />
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column align="center" label="操作" v-if="confirmForm.products.length !== 1">
                  <template slot-scope="{ row }">
                    <button type="button" class="table-btn danger" @click="handleConfirmDelete(row)">删除</button>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-row>
        </el-form>
        <div class="confirm-total inline-flex">
          <span>
            共
            <b>{{ confirmForm.products.length }}</b>
            件产品
          </span>
          <span v-if="confirmForm.supplyEndTime">
            截止日期：
            <b>{{ parseTime(confirmForm.supplyEndTime, '{y}-{m}-{d}') }}</b>
          </span>
        </div>
      </div>
      <div slot="footer">
        <button type="button" class="custom-dialog-btn small" @click="handleConfirmCancel">取消</button>
        <button type="button" class="custom-dialog-btn small primary" @click="handleConfirmSubmit">提交生成需求</button>
        <button type="button" class="custom-dialog-btn small primary" @click="handleConfirmPost" v-hasPermi="['purchasing:demand:send']">生成需求并发布</button>
      </div>
    </el-dialog>

    <!-- 编码产品 -->
    <product-code ref="productCode" @callparams="handleAddAddform"></product-code>

    <!-- 自定义状态 -->
    <el-dialog v-dialogDragBox title="自定义状态" :visible.sync="rulesOpen" width="1150px" class="custom-dialog">
      <div style="padding: 0 20px; margin-top: -20px">
        <div class="rules" v-for="(item, index) in rulesForm" :key="index">
          <div class="rules-item">
            <div class="rules-item-title">{{ item.tipName }}订单</div>
            <el-color-picker v-model="item.color"></el-color-picker>
          </div>
          <div class="rules-item">
            <div class="rules-item-title">自定义规则</div>
            <div class="rules-item-content">
              <div class="rules-item-select">
                <span>要求到厂时间剩余</span>
                <el-select v-model="item.requiredArrivalTime">
                  <el-option v-for="item in dayOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
                <span>天</span>
              </div>
              <div class="rules-item-select">
                <span>创建后</span>
                <el-select v-model="item.requiredCreateTime">
                  <el-option v-for="item in timeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
                <span>小时</span>
              </div>
              <div class="rules-item-tip">
                <i class="el-icon-warning-outline"></i>
                <span>{{ rulesTip(item) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div slot="footer">
        <button type="button" class="custom-dialog-btn" @click="rulesOpen = false">取消</button>
        <button type="button" class="custom-dialog-btn primary" @click="handleRulesSubmit">确定</button>
      </div>
    </el-dialog>
    <!--    完成采购-->
    <over-dialog ref="overDialog" @callback="backOver"></over-dialog>
    <!-- 批量收藏 -->
    <template v-if="confirmProducts.length">
      <div class="collectAll" v-hasPermi="['purchasing:demand:add']">
        <div class="collectAll-box">
          <div class="collectAll-title">已选择 {{ confirmProducts.length }} 项</div>
          <el-tooltip content="生成需求" placement="top" effect="dark">
            <div class="collectAll-btn">
              <span @click="handleConfirms">生成需求</span>
            </div>
          </el-tooltip>
          <el-tooltip content="其他采购" placement="top" effect="dark">
            <div class="collectAll-btn" v-hasPermi="['purchasing:demand:add']" v-if="classifyName === 'all'">
              <span @click="handleOverGroups">其他采购</span>
            </div>
          </el-tooltip>
          <el-tooltip content="退出" placement="top" effect="dark">
            <div class="collectAll-close" @click="handleClose">
              <i class="el-icon-close"></i>
            </div>
          </el-tooltip>
        </div>
      </div>
    </template>
    <!--  发布需求  -->
    <publish-tpl ref="publish" :has-add="false"></publish-tpl>
    <!--  查看合同  -->
    <el-dialog v-dialogDragBox title="查看合同" :visible.sync="contractOpen" width="80%" class="custom-dialog">
      <div style="padding: 0 20px">
        <contract-tpl :key="key" ref="contract" :contractList="contractList" isLook></contract-tpl>
      </div>
      <div slot="footer">
        <button type="button" class="custom-dialog-btn primary" @click="contractOpen = false">关闭</button>
      </div>
    </el-dialog>
    <!--   查看供应商   -->
    <el-dialog v-dialogDragBox title="查看供应商" :visible.sync="supllierOpen" width="1150px" class="custom-dialog">
      <div style="padding: 0 20px" :key="supplierKey">
        <el-button type="text" v-for="(item, index) in supplierList" :key="item.supplierId" @click="handleViewSupplier(item)">
          {{ index + 1 + '、' + (item.supplierName || item.name) }}
        </el-button>
      </div>
      <div slot="footer">
        <button type="button" class="custom-dialog-btn primary" @click="supllierOpen = false">关闭</button>
      </div>
    </el-dialog>

    <!-- 供应商详情 -->
    <supplier-dialog ref="supplier"></supplier-dialog>
    <!--从销售合同添加采购产品-->
    <contract-product ref="contractProduct" show-checkbox :show-list="false" @next="handleNextClick"></contract-product>
    <!-- 从录入合同添加采购产品 -->
    <input-contract ref="inputContract" show-checkbox :show-list="false" @next="handleNextClick"></input-contract>
  </div>
</template>

<script>
import ProductDialog from '@/views/public/product/dialog'
import { contractTemplateRule, contractTemplateRuleAdd, purchasingAdd, purchasingDelete, purchasingDemandAdd, purchasingDetails, purchasingGroupList, purchasingListComplete, purchasingListGroup, purchasingProduct, purchasingProductGroup } from '@/api/purchase'
import { getProduct } from '@/api/system/product'
import { getPrivateduct } from '@/api/system/privateduct'
import { isInteger, isNumber, isNumberLength } from '@/utils/validate'
import { checkPermi } from '@/utils/permission'
import productCode from '@/views/components/private'
import overDialog from './over'
import publishTpl from '@/views/purchase/publish'
import contractTpl from '@/views/purchase/contract'
import supplierDialog from '@/views/purchase/demandForMe/supplier'
import contractProduct from '@/views/unsalable/sell/sell'
import inputContract from '@/views/unsalable/sell/initiative'
import { getConfigDetail } from '@/api/config'

export default {
  name: 'Order',
  components: { contractProduct, supplierDialog, overDialog, ProductDialog, productCode, publishTpl, contractTpl, inputContract },
  data() {
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        keyword: undefined
      },
      key: 1,
      loading: true,
      total: 0,
      list: [],
      // 显隐列
      columns: [
        { key: 0, label: `序号`, visible: true },
        { key: 1, label: `订单编号`, visible: true },
        { key: 2, label: `日期`, visible: true },
        { key: 3, label: `产品名称`, visible: true },
        { key: 4, label: `单位`, visible: true },
        { key: 5, label: `计划采购数量`, visible: true },
        { key: 6, label: `要求到厂时间`, visible: true },
        { key: 7, label: `建议价格`, visible: true }
      ],
      // 显隐列
      groupColumns: [
        { key: 0, label: `序号`, visible: true },
        { key: 1, label: `订单编号`, visible: true },
        { key: 2, label: `产品名称`, visible: true },
        { key: 3, label: `单位`, visible: true },
        { key: 4, label: `计划采购数量`, visible: true },
        { key: 5, label: `建议价格`, visible: true },
        { key: 6, label: `备注`, visible: true }
      ],
      // 新增采购
      isFullscreen: false,
      addStep: 1,
      addPrev: true,
      addOpen: false,
      addList: [],
      addLoading: false,
      addQuery: {
        materialQuality: undefined,
        model: undefined,
        productCode: undefined,
        productName: undefined,
        specs: undefined,
        surface: undefined
      },
      isNoaddQuery: true,
      addForm: { purchasingProducts: [] },
      addRules: {
        serialNum: [{ required: true, message: '请输入订单编号', trigger: 'blur' }],
        date: [{ required: true, message: '请选择订单日期', trigger: ['blur', 'change'] }],
        quantity: [
          { required: true, message: '请输入采购数量', trigger: 'blur' },
          { validator: isNumber, message: '请输入正确的数量', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 3), message: '只可以填写三位小数', trigger: 'blur' }
        ],
        recommendPrice: [
          { validator: isNumber, message: '请输入正确的金额', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 4), message: '只可以填写四位小数', trigger: 'blur' }
        ],
        unit: [{ required: true, message: '请选择单位', trigger: 'change' }]
      },
      unitoptions: [
        { value: '吨', label: '吨' },
        { value: '千克', label: '千克' },
        { value: '个', label: '个' },
        { value: '件', label: '件' },
        { value: '套', label: '套' },
        { value: '米', label: '米' },
        { value: '支', label: '支' }
      ],
      // 确认需求
      confirmOpen: false,
      confirmForm: { products: [] },
      confirmProducts: [],
      confirmRules: {
        title: [{ required: true, message: '请输入采购标题', trigger: 'blur' }],
        supplyEndTime: [{ required: true, message: '请选择供货截止时间', trigger: ['blur', 'change'] }],
        expireTime: [
          { required: true, message: '请输入有效时间', trigger: 'blur' },
          { validator: isInteger, message: '请输入正确的有效时间', trigger: 'blur' }
        ],
        needQuantity: [
          { required: true, message: '请输入实际采购数量', trigger: 'blur' },
          { validator: isNumber, message: '请输入正确的数量', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 3), message: '只可以填写三位小数', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              let index = rule.field.split('.')[1]
              if (parseFloat(value) > parseFloat(this.confirmForm.products[index].quantity)) {
                callback(new Error('实际采购数量不能大于计划采购数量'))
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ],
        unit: [{ required: true, message: '请选择单位', trigger: 'change' }]
      },
      dateOption: {
        disabledDate: time => {
          return time.getTime() < Date.now() - 24 * 60 * 60 * 1000
        }
      },
      // 显示高度
      windowHeight: undefined,
      addHeight: undefined,
      addFormHeight: undefined,
      confirmFormHeight: undefined,
      isQuery: false,
      // 分类
      classifyName: 'group',
      classifyOptions: [
        { value: 'group', label: '采购产品' },
        { value: 'all', label: '采购清单' },
        { value: 'over', label: '已完成采购' }
      ],
      specsFilters: [],
      modelFilters: [],
      materialFilter: [],
      isExpand: false,
      isAdd: false,
      tipRuleType: undefined,
      tipRuleTypeOptions: [],
      dayOptions: [
        { value: 24, label: 1 },
        { value: 48, label: 2 },
        { value: 72, label: 3 },
        { value: 96, label: 4 }
      ],
      timeOptions: [
        { value: 0, label: 0 },
        { value: 1, label: 1 },
        { value: 12, label: 12 },
        { value: 24, label: 24 },
        { value: 36, label: 36 }
      ],
      rulesOpen: false,
      rulesData: [],
      rulesForm: [],
      docUser: undefined,
      otherList: [],
      isPost: false,
      contractOpen: false,
      contractList: [],
      supplierKey: 1,
      supllierOpen: false,
      supplierList: [],
      isContract: false,
      hasmaterialCode: false,
      isFirstVisit: true
    }
  },
  activated() {
    if (this.isFirstVisit) {
      this.isFirstVisit = false
      return
    }
    this.getList()
  },
  created() {
    const queryData = this.$route.query.data
    if (queryData) {
      const jsonData = JSON.parse(queryData)
      jsonData.productId = jsonData.id
      jsonData.source = 'common'
      jsonData.quantity = 99999
      this.isQuery = true
      this.handleConfirm(jsonData)
    }
    this.getRules()
    this.getConfig()
    this.getList()
  },
  computed: {
    companyId() {
      return this.$store.getters.info.companyId
    }
  },
  watch: {
    windowHeight(val) {
      this.addHeight = val * 0.94 - 305
      this.addFormHeight = val * 0.94 - 367
      this.confirmFormHeight = val * 0.94 - 485
    }
  },
  mounted() {
    this.windowHeight = document.documentElement.clientHeight
    window.onresize = () => {
      this.windowHeight = document.documentElement.clientHeight
    }
  },
  methods: {
    backOver() {
      this.handleClose()
      this.getList()
    },
    // 查询企业参数是否有金蝶物料编码
    getConfig() {
      getConfigDetail({ configKey: 'kingdee.material.code' }).then(res => {
        const { code, data } = res
        if (code === 200) this.hasmaterialCode = data.hasOwnProperty('configValue') && data.configValue === 'true'
      })
    },
    // 判断json是否包含某个字段
    hasOwn(obj, key) {
      if (obj) return Object.prototype.hasOwnProperty.call(obj, key)
      else return false
    },
    checkPermi,
    /** 展开/折叠操作 */
    toggleExpandAll() {
      this.isExpand = !this.isExpand
      this.key = Math.random()
      this.confirmProducts = []
    },
    // 建议价格格式化
    recommendFormat(val) {
      if (!val) return
      const arr = val.split(',').map(item => {
        return '￥' + parseFloat(item)
      })
      return arr.toString()
    },
    // 查询过期列表
    getPastList() {
      this.isPost = !this.isPost
      this.getList()
    },
    // 列表
    async getList() {
      const localPageSize = localStorage.getItem('OrderPageSize')
      if (localPageSize) this.queryParams.pageSize = parseInt(localPageSize)
      this.loading = true
      let res
      if (this.classifyName === 'all') {
        if (this.isPost) {
          res = await purchasingListGroup({ ...this.queryParams, ...{ tipRuleType: 'expired' } })
        } else {
          res = await purchasingListGroup({ ...this.queryParams, ...{ tipRuleType: this.tipRuleType } })
        }
      } else if (this.classifyName === 'group') {
        res = await purchasingGroupList(this.queryParams)
      } else if (this.classifyName === 'over') {
        res = await purchasingListComplete(this.queryParams)
      }
      if (res.code === 200) {
        const companyId = this.$store.getters.info.companyId
        res.rows.map(item => {
          if (this.hasOwn(item, 'product') && item.product) {
            if (this.hasOwn(item.product, 'suppliers') && item.product.suppliers) item.product.suppliers = item.product.suppliers.filter(ite => (ite.supplierId || ite.id) !== companyId)
            else item.product.suppliers = []
          }
        })
        this.list = res.rows
        if (this.list.length) {
          let needProduct = this.classifyName !== 'group'
          await Promise.all(
            this.list.map(async item => {
              item.isChecked = false
              if (this.classifyName === 'over' && item.hasOwnProperty('linkedContract')) item.hasContract = !!item.linkedContract.length
              item.list = []
              const list = await purchasingDetails({ ids: item.ids, needProduct })
              if (this.classifyName === 'all') {
                list.data.map(itt => {
                  itt.nick_name = item.nick_name
                  itt.isChecked = false
                })
              }
              item.list = list.data
            })
          )
        }
        this.total = res.total
        this.loading = false
        this.key = Math.random()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 合同状态回显
    statusFormat(row) {
      if (row.linkedContract) {
        let str = ''
        const red = row.linkedContract.filter(item => item.status === -1)
        const disabled = row.linkedContract.filter(item => item.status === 1)
        const success = row.linkedContract.filter(item => item.status === 2)
        const blue = row.linkedContract.filter(item => item.status === 3)
        if (red.length) str += `<span class="color-red">无效${red.length}份</span>`
        if (red.length && disabled.length) str += '，'
        if (disabled.length) str += `<span class="color-disabled">正常${disabled.length}份</span>`
        if ((red.length || disabled.length) && success.length) str += '，'
        if (success.length) str += `<span class="color-success">已签署${success.length}份</span>`
        if ((red.length || disabled.length || success.length) && blue.length) str += '，'
        if (blue.length) str += `<span class="color-blue">已完成${blue.length}份</span>`
        return str
      } else return ''
    },
    // 刷新列表
    async refreshList() {
      let res
      if (this.classifyName === 'all') {
        if (this.isPost) {
          res = await purchasingListGroup({ ...this.queryParams, ...{ tipRuleType: 'expired' } })
        } else {
          res = await purchasingListGroup({ ...this.queryParams, ...{ tipRuleType: this.tipRuleType } })
        }
      } else if (this.classifyName === 'group') {
        res = await purchasingGroupList(this.queryParams)
      } else if (this.classifyName === 'over') {
        res = await purchasingListComplete(this.queryParams)
      }
      if (res.code === 200) {
        if (res.rows.length) {
          let needProduct = this.classifyName !== 'group'
          await Promise.all(
            res.rows.map(async item => {
              item.list = []
              const list = await purchasingDetails({ ids: item.ids, needProduct })
              item.list = list.data
            })
          )
          const companyId = this.$store.getters.info.companyId
          res.rows.map(item => {
            if (this.hasOwn(item, 'product') && item.product) {
              if (this.hasOwn(item.product, 'suppliers') && item.product.suppliers) item.product.suppliers = item.product.suppliers.filter(ite => (ite.supplierId || ite.id) !== companyId)
              else item.product.suppliers = []
            }
          })
        }
        this.$set(this, 'list', res.rows)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 切换分类
    handleCategory(item) {
      this.confirmProducts = []
      this.isExpand = false
      this.queryParams.keyword = undefined
      this.classifyName = item.value
      this.isPost = false
      this.handleQuery()
    },
    // 查询
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 刷新列表
    handleRefresh() {
      this.getList()
    },
    // 新增
    handleAdd(data) {
      this.isAdd = false
      this.addForm = {
        date: new Date().getTime(),
        purchasingProducts: [],
        remark: undefined,
        requiredTime: undefined,
        serialNum: `CG${this.parseTime(new Date(), '{y}{m}{d}{h}{i}{s}{l}')}`
      }
      this.resetForm('addForm')
      this.addQuery = {
        materialQuality: undefined,
        model: undefined,
        productCode: undefined,
        productName: undefined,
        specs: undefined,
        surface: undefined
      }
      this.resetForm('addQueryForm')
      this.isNoaddQuery = true
      if (data.hasOwnProperty('list')) {
        data.purchasingProducts = data.list
        delete data.list
      }
      if (data.hasOwnProperty('serial_num')) {
        data.serialNum = data.serial_num
        delete data.serial_num
      }
      if (data.hasOwnProperty('required_time')) {
        data.requiredTime = new Date(data.required_time).getTime()
        delete data.required_time
      }
      if (data.hasOwnProperty('date')) {
        data.date = new Date(data.date).getTime()
      }
      if (data.hasOwnProperty('remarks')) {
        data.remark = data.remarks
        delete data.remarks
      }
      if (data.hasOwnProperty('ids')) delete data.ids
      if (data.hasOwnProperty('nick_name')) delete data.nick_name
      if (data.hasOwnProperty('creator')) delete data.creator
      if (data.hasOwnProperty('createTime')) delete data.createTime
      if (data.hasOwnProperty('serialNum')) {
        this.addForm = { ...data }
        this.isAdd = true
      }
      this.addStep = 1
      this.getProduct()
      this.isContract = false
      this.addOpen = true
      this.isFullscreen = false
    },
    // 新增采购产品全屏
    onFullscreen(val) {
      this.isFullscreen = val
      const winHeight = document.documentElement.clientHeight
      if (val) {
        this.addHeight = winHeight - 252
        this.addFormHeight = winHeight - 314
        this.confirmFormHeight = winHeight - 432
      } else {
        this.addHeight = winHeight * 0.94 - 305
        this.addFormHeight = winHeight * 0.94 - 367
        this.confirmFormHeight = winHeight * 0.94 - 485
      }
    },
    // 数组去重
    uniqueJsonArrByField(jsonArr, field) {
      // 数组长度小于2 或 没有指定去重字段 或 不是json格式数据
      if (jsonArr.length < 2 || !field || typeof jsonArr[0] !== 'object') return jsonArr
      return jsonArr.reduce((all, next) => (all.some(item => item[field] === next[field]) ? all : [...all, next]), [])
    },
    // 查询产品
    getProduct() {
      this.addLoading = true
      purchasingProduct(this.addQuery).then(res => {
        if (res.code === 200) {
          const specs = this.uniqueJsonArrByField(res.data, 'specs')
          const model = this.uniqueJsonArrByField(res.data, 'model')
          const material = this.uniqueJsonArrByField(res.data, 'material_quality')
          this.specsFilters = specs.map(item => {
            return { text: item.specs, value: item.specs }
          })
          this.modelFilters = model.map(item => {
            return { text: item.model, value: item.model }
          })
          this.materialFilter = material.map(item => {
            return { text: item.material_quality, value: item.material_quality }
          })
          this.addList = res.data
          this.addLoading = false
          this.isNoaddQuery = this.areAllValuesEmpty(this.addQuery)
          this.echo()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 判断json的字段值是否都为空
    areAllValuesEmpty(jsonObj) {
      for (let key in jsonObj) {
        if (jsonObj.hasOwnProperty(key) && jsonObj[key]) return false
      }
      return true
    },
    getRowKey(row) {
      return row.source + row.id
    },
    filterSpecs(value, row) {
      return row.specs === value
    },
    filterModel(value, row) {
      return row.model === value
    },
    filterQuality(value, row) {
      return row.material_quality === value
    },
    // 搜索产品
    handleAddQuery() {
      this.getProduct()
    },
    // 重置搜索产品
    resetAddQuery() {
      this.resetForm('addQueryForm')
      this.handleAddQuery()
    },
    // 全选
    handleSelectProductAll(selection) {
      if (selection.length) {
        if (this.addForm.purchasingProducts.length) {
          selection.map(ite => {
            const idx = this.addForm.purchasingProducts.findIndex(item => item.id === ite.id)
            if (idx === -1) this.addForm.purchasingProducts.push(ite)
          })
        } else {
          this.addForm.purchasingProducts = [...selection]
        }
      } else {
        this.addList.map(ite => {
          const idx = this.addForm.purchasingProducts.findIndex(item => item.id === ite.id)
          if (idx !== -1) this.addForm.purchasingProducts.splice(idx, 1)
        })
      }
    },
    // 选择产品
    handleSelectProduct(selection, row) {
      const idx = this.addForm.purchasingProducts.findIndex(item => item.id === row.id)
      if (idx === -1) {
        this.addForm.purchasingProducts.push(row)
      } else {
        this.addForm.purchasingProducts.splice(idx, 1)
      }
    },
    // 点击采购
    handleChooseProduct(row) {
      const idx = this.addForm.purchasingProducts.findIndex(item => item.id === row.id)
      if (idx !== -1) return
      this.addForm.purchasingProducts.push(row)
      this.echo()
    },
    // 上一步
    handlePrev() {
      this.addStep = 1
      this.echo()
    },
    // 下一步
    handleNext() {
      this.addStep = 2
    },
    // 从销售合同添加采购产品
    handleNextClick(data = []) {
      this.isContract = true
      if (data.length) {
        data.map(item => {
          item['product_code'] = item['productCode']
          item['product_name'] = item['productName']
          item['material_quality'] = item['materialQuality']
          item['quantity'] = parseFloat(Number(item['originAmount']).toFixed(3))
        })
      }
      this.addForm.purchasingProducts = data
      this.addOpen = true
      this.addStep = 2
    },
    // 回显选中
    echo() {
      this.$nextTick(() => {
        if (this.$refs.addTable) this.$refs.addTable.clearSelection()
        this.addList.map(item => {
          this.addForm.purchasingProducts.map(itt => {
            if (itt.id === item.id) this.$refs.addTable.toggleRowSelection(item, true)
          })
        })
      })
    },
    // 删除已选中的产品
    handleDeleteProduct(row) {
      const index = this.addForm.purchasingProducts.findIndex(item => item.id === row.id)
      this.addForm.purchasingProducts.splice(index, 1)
    },
    // 添加采购
    handleSubmit() {
      this.$refs['addForm'].validate(valid => {
        if (valid) {
          if (this.addForm.purchasingProducts) {
            this.addForm.purchasingProducts.map(item => {
              item.productId = item.id
              item.productName = item.product_name
            })
          }
          purchasingAdd(this.addForm).then(res => {
            if (res.code === 200) {
              this.$message.success('添加成功')
              this.getList()
              this.addOpen = false
            } else {
              this.$message.error(res.msg)
            }
          })
        }
      })
    },
    // 新增产品
    handleAddProduct() {
      this.$refs.productCode.handleAdd()
    },
    // 新增产品添加到已选中列表
    handleAddAddform(data) {
      data['supplierIds'] = data['suppliersb']
      data['product_code'] = data['productCode']
      data['product_name'] = data['productName']
      data['material_quality'] = data['materialQuality']
      this.addForm.purchasingProducts.push(data)
      this.addList = [...[data], ...this.addList]
      this.echo()
    },
    // 删除采购
    // prettier-ignore
    handleDelete(item) {
      const data = { ids: [item.id] }
      this.$modal.confirm('是否确认删除选中的需求？').then(function () {
        return purchasingDelete(data)
      }).then(() => {
        this.refreshList()
        // this.getList()
        this.$modal.msgSuccess('删除成功')
      }).catch(() => {
      })
    },
    // 产品详情
    handleView(Id, item) {
      if (item.source === 'common') {
        getProduct(Id).then(res => {
          this.$refs.productInfo.handleView(res.data)
        })
      } else {
        getPrivateduct(Id).then(res => {
          this.$refs.productInfo.handleView(res.data)
        })
      }
    },
    // 图片预览
    handleImgView(row) {
      this.$refs.productInfo.handleImgView(row)
    },
    // 重置生成需求表单
    resetConfirm() {
      this.confirmForm = {
        title: undefined,
        supplyEndTime: undefined,
        expireTime: 24,
        remark: undefined,
        products: []
      }
      this.resetForm('confirmForm')
    },
    // 全部列表批量选择
    handleAllSelect(selection) {
      this.confirmProducts = [...selection]
    },
    // 批量生成需求
    handleConfirms(row = {}) {
      let arr = []
      this.confirmProducts.map(item => {
        if (item.nick_name) arr.push(item.nick_name)
      })
      if (arr.length) this.docUser = [...new Set(arr)].toString()
      this.resetConfirm()
      const ids = this.confirmProducts.map(item => item.id || item.ids).toString() || row.ids
      purchasingProductGroup({ ids }).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          data.sort((a, b) => {
            const one = a.requiredTime ? new Date(a.requiredTime) : new Date()
            const two = b.requiredTime ? new Date(b.requiredTime) : new Date()
            return one - two
          })
          let title
          if (data.length === 1) title = '采购' + data[0].productName + `(${data[0].product.specs})` + data[0].quantity + data[0].unit + '于' + this.parseTime(new Date(), '{y}年{m}月{d}日')
          else title = '采购' + data[0].productName + '等' + data.length + '种产品' + '于' + this.parseTime(new Date(), '{y}年{m}月{d}日')
          this.confirmForm.title = title
          const start = this.parseTime(data[0].requiredTime, '{y}-{m}-{d}')
          const end = this.parseTime(new Date().getTime() + 24 * 60 * 60 * 1000, '{y}-{m}-{d}')
          this.confirmForm.supplyEndTime = new Date(start) > new Date(end) ? start : end
          if (this.confirmForm.supplyEndTime === this.parseTime(new Date(), '{y}-{m}-{d}')) this.confirmForm.expireTime = 23 - new Date().getHours() - 1
          else this.confirmForm.expireTime = 24
          data.map(item => {
            item.needQuantity = item.quantity
            item.listId = item.ids
          })
          this.confirmForm.products = [...data]
          this.confirmOpen = true
        } else this.$message.error(msg)
      })
    },
    hideHeaderCheckBox(row) {
      if (row.columnIndex === 0 && row.rowIndex === 0) {
        return 'hideHeaderCheckBox'
      }
    },
    // 采购清单列表批量选择
    handleAllSelectGruop(selection) {
      const arr = [...selection]
      let data = []
      arr.map(item => {
        data = [...data, ...item.list]
      })
      this.docUser = [...new Set(arr.map(item => item.nick_name))].toString()
      this.confirmProducts = [...data]
    },
    // 采购清单展开复选批量选择
    handleSelectGruopSon(selection, row) {
      const index = this.confirmProducts.findIndex(item => item.id == row.id && item.productId == row.productId && item.remark == row.remark)
      if (index < 0) this.confirmProducts.push(row)
      else this.confirmProducts.splice(index, 1)
    },
    // 采购清单生成需求
    handleConfirmGroup(row) {
      this.docUser = row.nick_name
      this.handleConfirms(row)
    },
    // 生成需求
    handleConfirm(row, rows = {}) {
      if (row.hasOwnProperty('nick_name')) this.docUser = row.nick_name
      if (rows.hasOwnProperty('nike_name')) this.docUser = rows.nick_name
      this.resetConfirm()
      const data = { ...row, ...{ listId: row.id || row.ids } }
      data.productName = data.productName ? data.productName : data.product_name
      data.productId = data.productId ? data.productId : data.product_id
      if (data.unit.split(',')) data.unit = data.unit.split(',')[0]
      if (!data.unit) data.isEdit = true
      data.needQuantity = data.quantity
      if (data.list) {
        let arr = data.list
        arr.map(item => {
          if (!item.requiredTime) item.requiredTime = this.parseTime(new Date(), '{y}-{m}-{d}')
        })
        arr.sort((a, b) => {
          const one = a.requiredTime ? new Date(a.requiredTime) : new Date()
          const two = b.requiredTime ? new Date(b.requiredTime) : new Date()
          return one - two
        })
        const start = this.parseTime(arr[0].requiredTime, '{y}-{m}-{d}')
        const end = this.parseTime(new Date().getTime() + 24 * 60 * 60 * 1000, '{y}-{m}-{d}')
        this.confirmForm.supplyEndTime = new Date(start) > new Date(end) ? start : end
        if (this.confirmForm.supplyEndTime === this.parseTime(new Date(), '{y}-{m}-{d}')) this.confirmForm.expireTime = 23 - new Date().getHours() - 1
        else this.confirmForm.expireTime = 24
      } else {
        this.confirmForm.supplyEndTime = data.requiredTime ? this.parseTime(data.requiredTime, '{y}-{m}-{d}') : this.parseTime(new Date().getTime() + 24 * 60 * 60 * 1000, '{y}-{m}-{d}')
        const start = this.parseTime(this.confirmForm.supplyEndTime, '{y}-{m}-{d}')
        const end = this.parseTime(new Date(), '{y}-{m}-{d}')
        if (start === end) this.confirmForm.expireTime = 23 - new Date().getHours() - 1
        else this.confirmForm.expireTime = 24
      }
      if (this.isQuery) {
        data.product = { ...row }
        this.confirmForm.title = '采购' + data.productName.replace(/\t|\n|\v|\r|\f/g, '') + `(${data.specs})` + '于' + this.parseTime(new Date(), '{y}年{m}月{d}日')
      } else {
        this.confirmForm.title = '采购' + data.productName + `(${data.product.specs})` + data.quantity + data.unit + '于' + this.parseTime(new Date(), '{y}年{m}月{d}日')
      }
      this.confirmForm.products.push(data)
      this.confirmOpen = true
    },
    // 删除需求产品
    handleConfirmDelete(row) {
      const idx = this.confirmForm.products.findIndex(item => item.productId === row.productId)
      this.confirmForm.products.splice(idx, 1)
    },
    // 取消生成需求
    handleConfirmCancel() {
      this.$nextTick(() => {
        if (this.$refs.allTable) this.$refs.allTable.clearSelection()
        if (this.$refs.groupTable) this.$refs.groupTable.clearSelection()
        if (this.$refs.overTable) this.$refs.overTable.clearSelection()
        for (let i = 0; i < 100; i++) {
          if (this.$refs['postprop' + i]) this.$refs['postprop' + i].clearSelection()
          if (this.$refs['allprop' + i]) this.$refs['allprop' + i].clearSelection()
          if (this.$refs['overprop' + i]) this.$refs['overprop' + i].clearSelection()
        }
      })
      this.confirmProducts = []
      this.confirmOpen = false
      if (this.isQuery) this.$router.push({ query: {} })
    },
    // 提交生成需求
    handleConfirmSubmit() {
      this.$refs['confirmForm'].validate(valid => {
        if (valid) {
          const data = { ...this.confirmForm }
          data.expireTime = new Date().getTime() + Number(data.expireTime) * 60 * 60 * 1000
          data.supplyEndTime = new Date(data.supplyEndTime + ' 23:59:59').getTime()
          data.docUser = this.docUser
          purchasingDemandAdd(data).then(res => {
            if (res.code === 200) {
              this.$message.success('成功生成需求')
              this.confirmProducts = []
              this.confirmOpen = false
              this.refreshList()
              // this.getList()
              if (this.isQuery) this.$router.push({ query: {} })
            } else {
              this.$message.error(res.msg)
            }
          })
        }
      })
    },
    // 提交生成需求并发布
    handleConfirmPost() {
      this.$refs['confirmForm'].validate(valid => {
        if (valid) {
          const data = { ...this.confirmForm }
          data.expireTime = new Date().getTime() + Number(data.expireTime) * 60 * 60 * 1000
          data.supplyEndTime = new Date(data.supplyEndTime + ' 23:59:59').getTime()
          data.docUser = this.docUser || this.$store.getters.info.realName || this.$store.getters.info.nickName
          purchasingDemandAdd(data).then(res => {
            if (res.code === 200) {
              this.confirmProducts = []
              this.confirmOpen = false
              if (this.isQuery) this.$router.push({ query: {} })
              this.$refs.publish.handlePublish(res.data)
            } else {
              this.$message.error(res.msg)
            }
          })
        }
      })
    },
    // 查询自定义状态规则
    getRules() {
      contractTemplateRule().then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          data.map(item => {
            if (item.tipType === 'advent') item.label = '只显示临期订单'
            else if (item.tipType === 'urgent') item.label = '只显示加急订单'
            else if (item.tipType === 'general') item.label = '只显示一般订单'
          })
          this.tipRuleTypeOptions = [...[{ tipType: undefined, label: '显示全部订单' }], ...data]
          this.rulesData = data
        } else this.$message.error(msg)
      })
    },
    handleRules() {
      this.rulesForm = JSON.parse(JSON.stringify(this.rulesData))
      this.rulesOpen = true
    },
    // 返回规则提示
    rulesTip(val) {
      const { tipType, requiredArrivalTime, requiredCreateTime } = val
      const day = this.dayOptions.find(item => item.value == requiredArrivalTime) || { label: '' }
      if (tipType === 'advent') return `注:临期订单规则默认为要求到厂时间剩余${day.label}天以内或创建超过${requiredCreateTime}小时的订单`
      else if (tipType === 'urgent') return `注:加急订单规则默认为要求到厂时间剩余${day.label}天以内或创建超过${requiredCreateTime}小时的订单`
      else if (tipType === 'general') return `注:一般订单规则默认为要求到厂时间剩余${day.label}天以上或创建后未超过${requiredCreateTime}小时的订单`
    },
    // 自定义状态提交
    handleRulesSubmit() {
      contractTemplateRuleAdd(this.rulesForm).then(res => {
        const { code, msg } = res
        if (code === 200) {
          this.$message.success('自定义状态设置成功')
          this.rulesOpen = false
          this.getRules()
          this.getList()
        } else this.$message.error(msg)
      })
    },
    // 订单状态返回
    rulesFormat(row, type = '') {
      const data = this.rulesData.find(item => item.tipType === row.tips) || { color: '#ff0000', tipName: '已过期' }
      if (type === 'class') return data.color
      else return data.tipName + '订单'
    },
    // 完成采购
    handleOverGroup(row) {
      this.$refs.overDialog.handleOver(row)
    },
    // 批量其他采购
    handleOverGroups() {
      this.$refs.overDialog.handleOvers(this.confirmProducts)
    },
    // 已过期采购清单列表批量选择
    handleOtherSelectGruop(selection) {
      const arr = [...selection]
      let data = []
      arr.map(item => {
        data = [...data, ...item.list]
      })
      this.docUser = [...new Set(arr.map(item => item.nick_name))].toString()
      this.confirmProducts = [...data]
    },
    // 关闭多选
    handleClose() {
      this.$nextTick(() => {
        if (this.$refs.allTable) this.$refs.allTable.clearSelection()
        if (this.$refs.groupTable) this.$refs.groupTable.clearSelection()
        if (this.$refs.overTable) this.$refs.overTable.clearSelection()
        for (let i = 0; i < 100; i++) {
          if (this.$refs['postprop' + i]) this.$refs['postprop' + i].clearSelection()
          if (this.$refs['allprop' + i]) this.$refs['allprop' + i].clearSelection()
          if (this.$refs['overprop' + i]) this.$refs['overprop' + i].clearSelection()
        }
      })
      this.list.map(item => {
        item.isChecked = false
        item.list.map(ite => {
          ite.isChecked = false
        })
      })
      this.isExpand = false
      this.confirmProducts = []
      if (this.isQuery) this.$router.push({ query: {} })
      this.key = Math.random()
      this.$forceUpdate()
    },
    // 查看合同
    handleViewContract(item) {
      this.contractList = item.linkedContract
      this.contractOpen = true
      this.key = Math.random()
    },
    // 打开供应商列表
    handleSupllier(row) {
      this.supplierKey = Math.random()
      const source = row.source
      const list = [...row.product.suppliers]
      list.map(item => (item.source = source))
      this.supplierList = list
      this.supllierOpen = row.product.suppliers.length > 0
    },
    // 查看供应商详情
    handleViewSupplier(row) {
      const id = row.supplierId || row.id
      const source = row.source
      this.$refs.supplier.getInfo(id, source)
    },
    mainSelectAll(selected) {
      this.list.forEach((item, index) => {
        this.$refs.allTable.toggleRowExpansion(item, true) // 展开子表
        item.isChecked = selected.length === this.list.length // 判断是否全选
        const subTable = this.$refs[`allprop${index}`]
        if (subTable) {
          subTable.clearSelection()
          // 判断是否全选
          if (selected.length === this.list.length) {
            subTable.toggleAllSelection()
            item.list.forEach(i => {
              i.isChecked = true
            })
          } else {
            item.list.forEach(item => {
              item.isChecked = false
            })
          }
        } else {
          item.list.forEach(item => {
            item.isChecked = selected.length === this.list.length
          })
        }
      })
      this.updateSelectedList()
      this.isExpand = true
    },
    mainSelect(selection, row) {
      this.$refs.allTable.toggleRowExpansion(row, true) // 展开子表
      row.isChecked = selection.includes(row)
      this.$nextTick(() => {
        const subTable = this.$refs[`allprop${this.list.indexOf(row)}`]
        if (subTable) {
          subTable.clearSelection()
          if (selection.includes(row)) {
            row.list.forEach(item => {
              item.isChecked = true
              subTable.toggleRowSelection(item, true)
            })
          } else {
            row.list.forEach(item => {
              item.isChecked = false
            })
          }
        } else {
          row.list.forEach(item => {
            item.isChecked = selection.includes(row)
          })
        }
        this.updateSelectedList()
      })
    },
    subSelectAll(index, selection) {
      const mainTable = this.$refs.allTable
      const mainItem = this.list[index]
      mainItem.isChecked = selection.length === mainItem.list.length
      mainItem.list.forEach(attachment => {
        attachment.isChecked = selection.includes(attachment)
      })
      mainTable.toggleRowSelection(mainItem, mainItem.isChecked)
      this.updateSelectedList()
    },
    subSelect(index, selection) {
      const mainItem = this.list[index]
      if (selection.length > 0 && selection.length === mainItem.list.length) {
        mainItem.isChecked = true
      } else if (selection.length > 0 && selection.length < mainItem.list.length) {
        mainItem.isChecked = ''
      } else {
        mainItem.isChecked = false
        this.$refs.allTable.toggleRowExpansion(mainItem, false)
      }
      this.toggleRowSelection(mainItem, mainItem.isChecked)
      mainItem.list.forEach(attachment => {
        attachment.isChecked = selection.includes(attachment)
      })
      this.updateSelectedList()
    },
    handleExpandChange(row, expandedRows) {
      if (expandedRows.includes(row)) {
        this.$nextTick(() => {
          const subTable = this.$refs[`allprop${this.list.indexOf(row)}`]
          if (subTable) {
            this.$nextTick(() => {
              row.list.forEach(item => {
                subTable.toggleRowSelection(item, item.isChecked === true)
              })
            })
          }
        })
      }
    },
    updateSelectedList() {
      const selectedData = this.list
        .filter(item => item.isChecked === true || item.isChecked === '')
        .map(it => {
          return {
            ...it,
            list: it.list.filter(item => item.isChecked)
          }
        })
      let data = []
      selectedData.map(item => {
        data = [...data, ...item.list]
      })
      this.docUser = [...new Set(selectedData.map(item => item.nick_name))].toString()
      this.confirmProducts = [...data]
    },
    // 设置当前行的选中态
    toggleRowSelection(row, flag) {
      if (row) {
        this.$nextTick(() => {
          this.$refs.allTable && this.$refs.allTable.toggleRowSelection(row, flag)
        })
      }
    },
    // 表格行样式 当当前行的状态为不明确状态时，添加样式，使其复选框为不明确状态样式
    getRowClassName({ row }) {
      if (row.isChecked === '') {
        return 'indeterminate'
      }
    },
    // 表格标题样式 当一级目录有为不明确状态时，添加样式，使其全选复选框为不明确状态样式
    getHeaderRowClassName({ row }) {
      const isIndeterminate = this.list.some(item => item.isChecked === '')
      if (isIndeterminate) {
        return 'indeterminate main-table-header'
      }
      return 'main-table-header'
    },
    // 从合同添加
    handleAddContract() {
      this.addOpen = false
      this.$refs.contractProduct.handleDialogList()
    },
    // 从录入合同添加
    handleAddInputContract() {
      this.addOpen = false
      this.$refs.inputContract.handleDialogList()
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
.custom-search {
  padding-top: 10px;
  padding-bottom: 10px;
  background-color: transparent;
}
.tableBox {
  margin: 0 10px 20px;
  padding: 0 10px 20px;
  background-color: $white;
  border-bottom-left-radius: 5px;
  border-bottom-right-radius: 5px;
}
.ptb15 {
  padding: 15px 0;
}
.batch-btn {
  padding: 0 20px;
  line-height: 30px;
  font-size: 12px;
  border-radius: 5px;
  border: 0;
  cursor: pointer;
  &:hover {
    opacity: 0.8;
  }
  &.primary {
    background-color: $blue;
    color: $white;
  }
  &:disabled {
    cursor: no-drop;
    background-color: #cdcdcd;
  }
}
.custom-table ::v-deep {
  .table-expand {
    padding-left: 80px;
    padding-bottom: 10px;
    margin: -13px 0;
    background-color: #eceef1;
  }
  .el-table__row.expanded {
    background-color: #eceef1;
    td.el-table__cell {
      background-color: #eceef1;
    }
  }
  .status {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    i {
      display: inline-block;
      width: 10px;
      height: 10px;
      border-radius: 8px;
      margin-right: 5px;
    }
  }
}
.custom-dialog {
  ::v-deep .addBox {
    padding: 10px 20px;
    .add-search {
      flex-direction: column;
      align-items: center;
      margin-bottom: 16px;
      &-tab {
        width: 780px;
        align-items: flex-end;
        .tab-item {
          font-size: 12px;
          line-height: 26px;
          background-color: #d9d9d9;
          color: #96a3b1;
          cursor: pointer;
          padding: 0 12px;
          &:nth-child(1) {
            border-top-left-radius: 5px;
          }
          &:nth-child(2) {
            border-top-right-radius: 5px;
          }
          &.active {
            background-color: $blue;
            color: $white;
            line-height: 30px;
            font-weight: 500;
            border-top-left-radius: 5px;
            border-top-right-radius: 5px;
          }
        }
      }
      &-input {
        width: 820px;
        justify-content: space-between;
        position: relative;
        input[type='text'] {
          width: 650px;
          height: 46px;
          line-height: 46px;
          border: 1px solid #cbd7e2;
          border-radius: 5px;
          outline: none;
          padding-left: 20px;
          padding-right: 130px;
          position: relative;
          &::-webkit-input-placeholder {
            color: #999999;
          }
        }
        button.search {
          height: 40px;
          line-height: 40px;
          font-size: 16px;
          color: $white;
          background-color: $blue;
          border: 0;
          padding: 0 29px;
          border-radius: 5px;
          position: absolute;
          top: 3px;
          right: 173px;
          cursor: pointer;
          &:hover {
            opacity: 0.8;
          }
        }
        button.plus {
          height: 46px;
          line-height: 46px;
          font-size: 14px;
          font-weight: 500;
          color: $white;
          background-color: $blue;
          border: 0;
          padding: 0 36px;
          border-radius: 5px;
          cursor: pointer;
          &:hover {
            opacity: 0.8;
          }
        }
      }
    }
  }
  .custom-table ::v-deep {
    .el-form-item {
      margin-top: 10px !important;
      margin-bottom: 10px !important;
      .el-form-item__error {
        top: 95%;
        padding-top: 0;
      }
    }
  }
  .confirm-total {
    margin-top: 10px;
    background-color: #ecf3ff;
    border: 1px solid $blue;
    border-radius: 5px;
    width: 100%;
    height: 48px;
    padding: 0 20px;
    font-size: 14px;
    color: $info;
    span {
      margin-right: 50px;
    }
    b {
      font-size: 18px;
      font-weight: 500;
      color: $blue;
    }
  }
}
.rules {
  padding: 15px 0;
  border-bottom: 1px solid #e2e6f3;
  &-item {
    display: flex;
    align-items: center;
    padding: 15px 0;
    &-title {
      width: 100px;
      font-size: 14px;
      color: #666666;
    }
    &-content {
      display: flex;
      align-items: center;
    }
    &-select {
      display: inline-flex;
      align-items: center;
      height: 46px;
      border: 1px solid #d7dadc;
      border-radius: 5px;
      padding: 0 15px;
      margin-right: 10px;
      ::v-deep {
        .el-input__inner {
          border: 0;
          width: 60px;
        }
      }
    }
  }
}
::v-deep {
  .table-tip {
    display: flex;
    align-items: center;
    padding-bottom: 10px;
    font-size: 14px;
    color: red;
    span {
      margin-left: 5px;
    }
  }
  .hideHeaderCheckBox {
    .el-checkbox {
      display: none;
    }
    .cell:before {
      content: '';
    }
  }
  .mark {
    height: 100vh;
    left: 0;
    position: fixed;
    top: 0;
    width: 100vw;
    z-index: 99999;
  }
  .collectAll {
    bottom: 10px;
    left: calc(50% - 200px);
    position: fixed;
    z-index: 50;
    transform: translateX(calc(-50% + 200px));
    -webkit-user-select: none;
    -ms-user-select: none;
    user-select: none;
    transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
    &-box {
      background: rgba(52, 54, 57, 1);
      border-radius: 12px;
      box-shadow: 0 -2px 8px rgba(38, 38, 38, 0.05), 0 10px 16px rgba(38, 38, 38, 0.08);
      height: 52px;
      padding: 7px 25px;
      position: relative;
      display: flex;
      align-items: center;
      color: #ffffff;
      font-size: 14px;
    }
    &-btn {
      margin-left: 15px;
      span {
        display: inline-block;
        padding: 0 10px;
        line-height: 36px;
        cursor: pointer;
        &:hover,
        &.active {
          background-color: rgba(255, 255, 255, 0.08) !important;
        }
      }
    }
    &-close {
      font-size: 20px;
      cursor: pointer;
      i {
        line-height: 36px;
        padding: 0 10px;
        &:hover {
          background-color: rgba(255, 255, 255, 0.08) !important;
        }
      }
    }
  }
}
::v-deep {
  .indeterminate .el-checkbox__input .el-checkbox__inner {
    background-color: #2e73f3 !important;
    border-color: #2e73f3 !important;
    color: #ffffff !important;
  }
  .indeterminate .el-checkbox__input.is-checked .el-checkbox__inner::after {
    transform: scale(0.5);
  }
  .indeterminate .el-checkbox__input .el-checkbox__inner {
    background-color: #f2f6fc;
    border-color: #dcdfe6;
  }
  .indeterminate .el-checkbox__input .el-checkbox__inner::after {
    border-color: #c0c4cc !important;
    background-color: #c0c4cc;
  }
  .indeterminate .el-checkbox__input .el-checkbox__inner::after {
    content: '';
    position: absolute;
    display: block;
    background-color: #ffffff;
    height: 2px;
    transform: scale(0.5);
    left: 0;
    right: 0;
    top: 5px;
    width: auto !important;
  }
}
.classify-refresh {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
</style>
