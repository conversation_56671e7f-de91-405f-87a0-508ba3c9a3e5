<template>
  <div class="app-container bgcf9 vh-85">
    <div class="custom-search">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" @submit.native.prevent>
        <el-form-item label="产品名称" prop="keyword">
          <el-input v-model="queryParams.keyword" placeholder="请输入产品名称" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
        </el-form-item>
        <el-form-item label="供应商">
          <el-select v-model="companyId" placeholder="请选择供应商" clearable @change="handleCompanyChange">
            <el-option v-for="item in companyList" :key="item.companyId" :label="item.companyName" :value="item.companyId" />
          </el-select>
        </el-form-item>
      </el-form>
    </div>

    <!-- 收藏管理 -->
    <collect-tpl ref="collect" :storeId.sync="storeId" :collectList.sync="collectList" @getList="getList" @colletSubmit="colletSubmit" :tabOptions="tabOptions" :tabName.sync="tabName" :isSearch="true" :search="false" :showSearch.sync="showSearch" @queryTable="getList" :columns="columns" />

    <template v-if="dataList.length && !loading">
      <el-table ref="table" v-loading="loading" :data="dataList" border row-key="id" @selection-change="handleSelectionChange" size="small" :key="key" :default-sort="dataSort" :class="{ drop: isPC() }">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column align="center" label="序号" type="index" v-if="columns[0].visible" />
        <el-table-column label="产品名称" align="center" prop="productName" v-if="columns[1].visible" min-width="130">
          <template slot-scope="{ row }">
            <div class="table-link pointer" @click="handleDetail(row)">{{ row.productName }}</div>
          </template>
        </el-table-column>
        <el-table-column label="产品编码" align="center" prop="productCode" v-if="columns[2].visible" />
        <template v-for="item in tabOptions">
          <el-table-column :key="item.value" class-name="table-badge" label="未税报价" align="center" :prop="item.value" v-if="columns[3].visible && tabName === item.value" width="120">
            <template slot-scope="{ row }">
              <el-badge :value="row.offerNum" v-if="row.offerNum">
                <span v-if="tabName === item.value" :class="`price ${item.value}`" @click="handleOffer(row)">￥{{ row[item.value] }}</span>
              </el-badge>
              <template v-else>
                <el-button type="text" size="mini" @click="handleOnekeyOffer(row)">一键报价</el-button>
              </template>
            </template>
          </el-table-column>
        </template>
        <el-table-column label="三维图片" align="center" prop="diagram" width="80" v-if="columns[4].visible">
          <template slot-scope="{ row }">
            <image-preview :src="row.diagram" :width="50" :height="50" />
          </template>
        </el-table-column>
        <el-table-column label="图纸" align="center" prop="draw" v-if="columns[5].visible" width="85">
          <template slot-scope="{ row }">
            <el-button type="text" size="mini" @click="handleDetail(row, 'paper')" icon="el-icon-view">图纸文件</el-button>
          </template>
        </el-table-column>
        <el-table-column label="行业分类" align="center" prop="industry" v-if="columns[6].visible" />
        <el-table-column label="规格" align="center" prop="specs" v-if="columns[7].visible" />
        <el-table-column label="型号" align="center" prop="model" v-if="columns[8].visible" />
        <el-table-column label="单位" align="center" prop="unit" v-if="columns[9].visible" width="55" />
        <el-table-column label="重量" align="center" prop="weight" v-if="columns[10].visible" />
        <el-table-column label="材质" align="center" prop="materialQuality" v-if="columns[11].visible" />
        <el-table-column label="状态" align="center" prop="status" v-if="columns[12].visible" width="55">
          <template slot-scope="scope">
            {{ scope.row.status == -1 ? '下架' : '上架' }}
          </template>
        </el-table-column>
        <el-table-column label="表面" align="center" prop="surface" v-if="columns[13].visible" />
        <el-table-column v-if="admin === 'true'" label="创建人" align="center" prop="createBy" width="95" />
        <el-table-column v-if="admin === 'true'" label="创建时间" align="center" prop="createTime" />
        <el-table-column v-if="admin === 'true'" label="更新时间" align="center" prop="updateTime" />
        <el-table-column align="center" label="操作" width="190">
          <template slot-scope="{ row }">
            <el-button type="text" size="mini" icon="el-icon-view" @click="handleDetail(row)">详情</el-button>
            <el-button type="text" size="mini" icon="el-icon-delete" @click="handleDelete(row)">删除</el-button>
            <el-button type="text" size="mini" icon="el-icon-paperclip" @click="handleCollet(row)">收藏到</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="custom-pagination">
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
      </div>
    </template>
    <el-empty :image-size="200" v-else />

    <!-- 详情 -->
    <product-dialog ref="productInfo"></product-dialog>

    <!-- 报价 -->
    <offer-dialog ref="offerInfo"></offer-dialog>

    <!-- 批量收藏 -->
    <template v-if="!multiple">
      <div class="mark" v-if="isCollect" @click="isCollect = !isCollect"></div>
      <div class="collectAll">
        <div class="collectAll-box">
          <div class="collectAll-title">已选择 {{ ids.length }} 项</div>
          <el-tooltip content="收藏到其他收藏夹" placement="top" effect="dark">
            <div class="collectAll-btn">
              <span :class="{ active: isCollect }" @click="isCollect = !isCollect">收藏到</span>
            </div>
          </el-tooltip>
          <el-tooltip content="退出" placement="top" effect="dark">
            <div class="collectAll-close" @click="handleClose">
              <i class="el-icon-close"></i>
            </div>
          </el-tooltip>
        </div>
      </div>
      <el-collapse-transition>
        <div class="collectAll-list-box" v-if="isCollect">
          <div class="collectAll-list-close">
            <span>请选择收藏夹</span>
            <i class="el-icon-close" @click="isCollect = !isCollect"></i>
          </div>
          <div class="collectAll-list">
            <div class="item" :class="{ disabled: item.storeId === storeId }" v-for="item in collectList" :key="item.storeId" @click="handleColletAll(item)">
              {{ item.dirName }}
            </div>
          </div>
        </div>
      </el-collapse-transition>
    </template>

    <!-- <div class="tipShow">按住可进行拖拽排序</div> -->
  </div>
</template>

<script>
import Sortable from 'sortablejs'
import { getlistb, shoucTo, delshouc, shoucGo } from '@/api/houtai/shoucang'
import { gygs, quoteadd, list, quoteedi } from '@/api/houtai/formula'
import ProductDialog from '@/views/public/product/dialog'
import OfferDialog from '@/views/public/product/offer'
import CollectTpl from '@/views/components/collect'

export default {
  name: 'Collect',
  components: { ProductDialog, OfferDialog, CollectTpl },
  data() {
    return {
      key: 1,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 列表数据
      dataList: [],
      historyDataList: [],
      // 总条数
      total: 0,
      // 显示搜索条件
      showSearch: true,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        keyword: undefined,
        storeId: undefined
      },
      // 列信息
      columns: [
        { key: 0, label: `序号`, visible: true },
        { key: 1, label: `产品名称`, visible: true },
        { key: 2, label: `产品编码`, visible: true },
        { key: 3, label: `未税报价`, visible: true },
        { key: 4, label: `三维图片`, visible: true },
        { key: 5, label: `图纸`, visible: true },
        { key: 6, label: `行业分类`, visible: true },
        { key: 7, label: `规格`, visible: true },
        { key: 8, label: `型号`, visible: true },
        { key: 9, label: `单位`, visible: true },
        { key: 10, label: `重量`, visible: true },
        { key: 11, label: `材质`, visible: true },
        { key: 12, label: `状态`, visible: true },
        { key: 13, label: `表面`, visible: true }
      ],
      // 是否管理员
      admin: false,
      // 收藏列表ID
      storeId: undefined,
      tabOptions: [
        { value: 'min', label: '最低价' },
        { value: 'max', label: '最高价' },
        { value: 'avg', label: '平均价' }
      ],
      tabName: 'min',
      dataSort: { prop: 'min', order: 'ascending' },
      sortable: null,
      // 收藏夹列表
      collectList: undefined,
      // 是否显示收藏夹列表
      isCollect: false,
      // 供应商列表
      companyList: [],
      // 供应商ID
      companyId: undefined
    }
  },
  created() {
    this.admin = localStorage.getItem('admin')
  },
  watch: {
    tabName: {
      handler(nv, ov) {
        this.dataSort.prop = nv
        this.dataSort.order = nv === 'min' ? 'ascending' : 'descending'
        this.key = Math.random()
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    // 判断当前设备是电脑、平板还是手机
    isPC() {
      let userAgentInfo = navigator.userAgent
      let Agents = ['Android', 'iPhone', 'SymbianOS', 'Windows Phone', 'iPad', 'iPod']
      let flag = true
      for (let v = 0; v < Agents.length; v++) {
        if (userAgentInfo.indexOf(Agents[v]) > 0) {
          flag = false
          break
        }
      }
      return flag
    },
    // 选择供应商
    handleCompanyChange(value) {
      this.companyId = value
      let list = JSON.parse(JSON.stringify(this.historyDataList))
      if (value) {
        list = list.filter(item => {
          return item.quoteCompanyId && item.quoteCompanyId.includes(value)
        })
      }
      this.$set(this, 'dataList', list)
    },
    // 列表
    async getList() {
      const loading = this.$loading({
        lock: true,
        text: '加载中',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      this.loading = true
      if (this.storeId) {
        this.queryParams.storeId = this.storeId
        let { code, msg, rows, total } = await getlistb(this.queryParams)
        if (code === 200) {
          if (rows.length) {
            let companyList = []
            await Promise.all(
              rows.map(async item => {
                item.offerNum = 0
                item.max = -1
                item.min = 999999
                item.avg = -1
                const { data } = await gygs({ productId: item.id })
                if (data.quotes.length) {
                  const companyId = data.quotes.map(item => item.companyId)
                  item.offerNum = data.quotes.length
                  item.max = data.max
                  item.min = data.min
                  item.avg = data.avg
                  item.quoteCompanyId = companyId
                  companyList = [...companyList, ...data.quotes]
                }
              })
            )
            // 根据companyId对companyList去重
            this.companyList = Array.from(new Set(companyList.map(item => item.companyId))).map(companyId => {
              return companyList.find(item => item.companyId === companyId)
            })
          }
          this.dataList = rows
          this.historyDataList = rows
          this.key = Math.random()
          this.total = total
          this.loading = false
          loading.close()
          if (this.isPC()) {
            this.$nextTick(() => {
              this.rowDrop()
            })
          }
        } else {
          this.$modal.msgError(msg)
          loading.close()
        }
      } else {
        this.loading = false
        loading.close()
      }
    },
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      this.$set(this, 'companyId', undefined)
      this.getList()
    },
    // 重置搜索
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length != 1
      this.multiple = !selection.length
    },
    // 详情
    handleDetail(item, type) {
      this.$refs.productInfo.handleView(item, type)
    },
    // 报价
    handleOffer(item) {
      this.$refs.offerInfo.handleView(item, true)
    },
    //行拖拽
    rowDrop() {
      const _this = this
      if (_this.dataList.length) {
        const tbody = document.querySelector('.el-table__body-wrapper tbody')
        this.sortable = Sortable.create(tbody, {
          ghostClass: 'sortable',
          animation: 180,
          delay: 0,
          onEnd({ newIndex, oldIndex }) {
            if (newIndex > oldIndex) {
              _this.handleMoveDown(newIndex, oldIndex)
            }
            if (newIndex < oldIndex) {
              _this.handleMoveUp(newIndex, oldIndex)
            }
          }
        })
      }
    },
    // 上移
    handleMoveUp(newIndex, oldIndex) {
      const valueId = this.dataList[oldIndex].id
      shoucGo({ index: newIndex, storeId: this.storeId, valueId }).then(res => {
        const oldItem = this.dataList[oldIndex]
        this.dataList.splice(oldIndex, 1)
        this.dataList.splice(newIndex, 0, oldItem)
      })
    },
    // 下移
    handleMoveDown(newIndex, oldIndex) {
      const valueId = this.dataList[oldIndex].id
      shoucGo({ index: newIndex, storeId: this.storeId, valueId }).then(res => {
        const oldItem = this.dataList[oldIndex]
        this.dataList.splice(oldIndex, 1)
        this.dataList.splice(newIndex, 0, oldItem)
      })
    },
    // 单个收藏
    handleCollet(item) {
      this.$refs.collect.handleChange(item.id)
    },
    // 批量收藏
    handleColletAll(item) {
      const storeId = item.storeId
      const valueIdList = this.ids
      if (storeId === this.storeId) return
      shoucTo({ storeId, valueIdList }).then(res => {
        // this.getList()
        this.$modal.msgSuccess('操作成功')
        this.isCollect = false
        this.multiple = false
        this.ids = []
        this.$refs.table.clearSelection()
      })
    },
    // 提交收藏
    colletSubmit(storeId, data) {
      if (!Array.isArray(data)) data = [data]
      shoucTo({ storeId, valueIdList: data }).then(res => {
        this.getList()
        this.$modal.msgSuccess('操作成功')
      })
    },
    // 删除
    // prettier-ignore
    handleDelete(item) {
      const data = { storeId: this.storeId, valueId: item.id }
      this.$modal.confirm('是否确认删除选中的收藏？').then(function () {
        return delshouc(data)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess('删除成功')
      }).catch(() => {
      })
    },
    // 关闭多选
    handleClose() {
      this.isCollect = false
      this.multiple = false
      this.ids = []
      this.$refs.table.clearSelection()
    },
    // 一键报价
    handleOnekeyOffer(row) {
      const name = row.productName + '报价'
      const productId = row.id
      const source = 'common'
      quoteadd({ name }).then(res => {
        list().then(res2 => {
          const quoteId = res2.rows[0].id
          const data = { quoteId, productId, source }
          quoteedi(data).then(res => {
            this.$router.push({ path: '/offer/addition', query: { id: quoteId } })
          })
        })
      })
    }
  }
}
</script>

<style>
.sortable {
  opacity: 0.8;
  color: #fff !important;
  background: #409eff !important;
}
</style>
<style lang="scss" scoped>
.table-link {
  color: #409eff;
  &:hover {
    text-decoration: underline;
  }
}
::v-deep .table-badge {
  .cell {
    overflow: inherit;
  }
}
.price {
  font-size: 16px;
  padding-right: 5px;
  cursor: pointer;
}
.max {
  color: #ec2454;
}
.min {
  color: #67c23a;
}
.avg {
  color: #409eff;
}
.drop {
  ::v-deep .el-table__row {
    position: relative;
    &:hover {
      cursor: move;
      &:after {
        transition: all 0.3s;
        content: '按住可进行拖拽排序';
        position: absolute;
        left: 50%;
        top: -40px;
        z-index: 200;
        background-color: #303133;
        border-radius: 4px;
        padding: 10px;
        font-size: 12px;
        min-width: 10px;
        word-wrap: break-word;
        color: #fff;
      }
    }
    .zindex {
      z-index: 2;
    }
  }
}
.tipShow {
  display: none;
  background-color: #303133;
  color: #fff;
  position: fixed;
  z-index: 1;
  border-radius: 4px;
  padding: 10px;
  // z-index: 2000;
  font-size: 12px;
  line-height: 1.2;
  min-width: 10px;
  word-wrap: break-word;
}
</style>
