<template>
  <div class="bodya">

    <el-row>
      <el-col :span="24">
        <el-row>
          <el-col :span="4">
            <el-input v-model="queryParams.productName" size="small" placeholder="请输入名称"></el-input>
          </el-col>
          <el-col :span="8">
            <el-button size="small" @click="shuaxinb()" style="margin-left: 10px;" type="primary">
              搜索
            </el-button>
            <el-button size="small" @click="cz()" style="margin-left: 10px;" type="">
              重置
            </el-button>
          </el-col>
        </el-row>
        <!-- <el-table :data="tableData" border
                  row-key="id"
                  @selection-change="handleSelectionChange"
                  size="small" style="width: 100%;margin-top: 10px;">
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column
            align="center"
            label="#"
            type="index">
          </el-table-column>
          <el-table-column label="产品名称" align="center" prop="productName"/>
          <el-table-column label="标签列表" align="center" prop="labels">
            <template slot-scope="scope">
              {{ scope.row.labels }}
            </template>

          </el-table-column>
          <el-table-column label="标签管理" align="center" prop="labels">
            <template slot-scope="scope">
              <el-button type="primary" size="mini" @click="showLabel(scope.row)">
                标签管理
              </el-button>
            </template>
          </el-table-column>
        </el-table> -->
        <el-table :data="tableData" border row-key="id" @selection-change="handleSelectionChange" size="small" style="width: 100%;margin-top: 20px">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column align="center" label="#" type="index" />
      <el-table-column label="产品名称" align="center" prop="productName" />
      <el-table-column label="标签列表" align="center">
        <template slot-scope="scope">
          <div class="drag-handle" @mousedown="startDrag(scope.$index)">
            <i class="fas fa-grip-vertical"></i>
          </div>
          {{ scope.row.labels }}
        </template>
      </el-table-column>
      <el-table-column label="标签管理" align="center" prop="labels">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" @click="showLabel(scope.row)">
            标签管理
          </el-button>
        </template>
      </el-table-column>
    </el-table>
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="ready"
        />
      </el-col>
    </el-row>
    <!-- 标签抽屉 -->
    <el-drawer
    :visible.sync="labelView"
    :with-header="false">
    <div class="labelbox">
      <div class="label-1">{{ pinfo.productName }} - 标签</div>
      <div class="label-title">预设标签</div>
      <div class="label-2" style="flex:none;">
         <template v-for="(item,index) in labellist.system">
            <div>
              <div class="labelitem" style="font-weight:bold;background-color: white;border-bottom: 1px solid;border-radius: 0;">
              {{ item.content }}标签
            </div>
              <div v-for="(item2,index2) in item.child" class="labelitem"  @click="setLebel(pinfo.id,item2.content)">
                {{ item2.content}}
              </div>
            </div>
          </template>

      </div>
      <div class="label-title">通用标签</div>
      <div class="label-2">
          <div v-for="(item,index) in labellist.custom" class="labelitem" @click="setLebel(pinfo.id,item.content)">
            {{item.content}}
          </div>
          <div  class="labelitem" @click="addlabel('custom')" style="background-color: white;">
            新增标签+
          </div>
      </div>
      <div class="label-title">当前产品已打标签</div>
      <div class="label-2">
          <div v-for="(item,index) in pinfo.labels" class="labelitem" style="position:relative;">
            <img class="labelicon" @click="delLabel(index)" src="../../../../../public/imgs/取消 <EMAIL>">
            {{item}}
          </div>
      </div>
      <div class="label-title">操作</div>
        <el-button type="primary" size="mini" @click="updatelabel(pinfo.id,pinfo.labels)">
              更新标签
        </el-button>
    </div>
  </el-drawer>

      <!-- 新增标签-->
      <el-dialog
      title="新增标签"
      :visible.sync="addview"
      width="30%"
      style="margin-top: 10%;"
      >

      <el-form ref="form" :model="addData" label-width="80px">
        <el-form-item label="所属上级">
          <el-select v-model="addData.parentId" placeholder="请选择上级">
            <el-option label="自定义标签" :value="Number(0)" checked></el-option>
          <el-option
            v-for="item in labellist.system"
            :key="item.id"
            :label="item.content"
            :value="Number(item.id)">
          </el-option>
        </el-select>
        </el-form-item>
        <el-form-item label="标签名">
          <el-input v-model="addData.content"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="add_c()">新建</el-button>
      </span>
    </el-dialog>

  </div>
</template>

<script>
import {getlist, dellist,shangxia,editlist} from "@/api/houtai/gongyu/chanpin";
import draggable from 'vuedraggable';
import add from "./add";
import edit from "./edit";
import Xq from "./xq";
import shoucang from "../../shoucang";
import {labelList,labelAdd,labelDel} from "@/api/purchase/category";
import {
  getlistb,
  shoucTo,
  delshouc,
  shoucGo
} from "@/api/houtai/shoucang";



export default {
  name: 'Commonproduct',
  components: {
    add,
    edit,
    shoucang,
    Xq,
    draggable,
  },
  data() {
    return {
      labelView:false,
      pinfo:{},
      labellist:[],
      addData:{
        content:"",
        labelType:"custom"
      },
      addview:false,
      tableData: [],
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 8,
        productName: '',
        phone: '',
        address: '',
        status: '',
      },
      show: false,
      pdfUrl: '',

      shoucangData:{
        type: 'UserProduct',
        // UserProduct,//个人公域产品收藏
        // UserPriProduct,//个人私域产品收藏
        // UserPriDemand,//个人私域需求收藏
        // UserPriSupplier,//个人私域供应商收藏
      },
      onegood: {},
      xzid: '',
      xztype: 1,
      typeshowa: false,

      goodslist: [],
    }
  },
  created() {
    this.ready();
    // this.initial();

    // shoucangData
  },
  mounted() {
   let goodid=  this.$route.query.goodid;
    let adda=  this.$route.query.add;
   let that = this;
   if(adda) {
     this.$refs.Refadd.showmodel();
   }
   if(goodid) {
    setTimeout(function (){
      that.shoucang({
        id: goodid,
      })
    }, 500)
     // shoucang(row){
     //   this.onegood = row;
     //   this.dx = 1;
     //   this.$refs.Refshoucang.showmodelb();
     //
     // },
   }
   labelList(this.queryParams).then((res) => {
        this.labellist=res.data;

      });
  },
  methods: {
    startDrag(index) {
      this.draggingIndex = index;
      document.addEventListener('mousemove', this.handleDrag);
      document.addEventListener('mouseup', this.stopDrag);
    },
    handleDrag(event) {
      // 处理拖拽过程中的事件
      // 在这里可以更新表格行的顺序
    },
    stopDrag() {
      this.draggingIndex = null;
      document.removeEventListener('mousemove', this.handleDrag);
      document.removeEventListener('mouseup', this.stopDrag);
    },
    //查看标签
    showLabel(row){
      this.labelView=true;
      this.pinfo=JSON.parse(JSON.stringify(row));

    },
    //预插入标签
    setLebel(id,content){
      this.pinfo.labels.push(content);
    },
      //预删除标签
      delLabel(index){
        this.pinfo.labels.splice(index, 1);
    },
    //上传标签
    updatelabel(id,labels){
        editlist(this.pinfo).then((res) => {
        this.$message.success("更新成功");
        this.ready();
      });
    },
    //新增标签
    addlabel(){
      this.addview=true;
    },
    //新增标签处理
    add_c()
    {
      labelAdd(this.addData).then((res) => {
        labelList(this.queryParams).then((res) => {
        this.labellist=res.data;
      });
        this.$message.success("添加成功");
        this.addview=false;
      });
    },
    piliang(){
      // this.onegood = row;
      this.dx = 2;
      this.$refs.Refshoucang.showmodelb();


    },
    sxjia(row,index){
      // /system/product/online/switch
      // shangxia
      shangxia({
        ids: row.id,
        status:( row.status ==  -1 ? 1 : -1),
      }).then((res) => {
        this.$message({
          message: '操作成功',
          type: 'success'
        });
        this.shuaxinb()
      });
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.goodslist = selection;


      // this.ids = selection.map((item) => item.id);
      // this.single = selection.length !== 1;
      // this.multiple = !selection.length;
    },
    shuaxinc(row) {
      this.xzid = row.storeId;

    },
    upa(row,indexa){
      // shoucGo
      // index
      // storeId
      // valueId
      let numa = ((((this.queryParams.pageNum-1) * this.queryParams.pageSize))+ indexa )-1;
      (numa <0 ? numa= 0 : '');
      shoucGo({
        index: numa ,
        storeId: this.xzid,
        valueId: row.id,
      }).then((res) => {
        this.$message({
          message: '操作成功',
          type: 'success'
        });
        this.shuaxinb();
      });
    },
    dowa(row,indexa){
      let numa = ((((this.queryParams.pageNum-1) * this.queryParams.pageSize))+ indexa )+1;
      (numa <0 ? numa= 0 : '');
      shoucGo({
        index: numa ,
        storeId: this.xzid,
        valueId: row.id,
      }).then((res) => {
        this.$message({
          message: '操作成功',
          type: 'success'
        });
        this.shuaxinb();
      });

    },
    initial(){
      // this.$refs.Refshoucang.ready();
    },
    cz() {
      this.queryParams.productName = '';
      this.shuaxinb();

    },
    ready() {
      getlist(this.queryParams).then((res) => {
        let lista = res.rows;
        this.tableData = lista;
        this.total = res.total;
      });
    },
    readyb() {
      this.queryParams['storeId'] = this.xzid;
      this.queryParams['pageNum'] = 1;

      getlistb(this.queryParams).then((res) => {
        let lista = res.rows;
        this.tableData = lista;
        this.total = res.total;
      });
    },

    shuaxina(row){
      if(row.type == 1) {
        this.ready();
        this.xzid = row.data.storeId;
      } else {
        this.xzid = row.data.storeId;
        this.readyb();
      }
      this.xztype = row.type;
    },
    shuaxinb(){
      if(this.xztype == 1) {
        this.ready();
        // this.xzid = row.data.storeId;
      } else {
        this.readyb();
        // this.xzid = row.data.storeId;
      }
    },
    add() {
      this.$refs.Refadd.showmodel();
    },
    edita(row) {
      this.$refs.Refedit.showmodel(row);
    },
    xqa(row){
      this.$refs.Refxq.showmodel(row);
    },
    drawPreView(e) {
      this.show = true;
      this.pdfUrl = process.env.VUE_APP_BASE_API + e.replace('http://localhost', '');
    },
    dela(row) {
      this.$confirm('是否删除此数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // delshouc
        if(this.xztype == 1) {
          dellist({
            ids: row.id,
          }).then((res) => {
            this.$message({
              message: '删除成功',
              type: 'success'
            });
            this.shuaxinb();
          });
        } else {
          delshouc({
            storeId: this.xzid,
            valueId: row.id,
          }).then((res) => {
            this.$message({
              message: '删除成功',
              type: 'success'
            });
            this.shuaxinb();
          });
        }


      }).catch(() => {
      });


    },
    // shuaxin() {
    //   this.ready();
    // },
    // 格式化url
    parseProfile(name) {
      if (!name) return;
      const newUrl = name.substring(name.lastIndexOf("/") + 1, name.length);
      const arr1 = newUrl.split("_");
      const arr2 = newUrl.split(".");
      return arr1[0] + "." + arr2[1];
    },


    //收藏到
    shoucang(row){
      this.onegood = row;
      this.dx = 1;
      this.$refs.Refshoucang.showmodelb();

    },
    xza(row){
     let lista = [];
     if(this.dx == 1) {
       lista =  [this.onegood.id];
     }
      if(this.dx == 2) {
        let listb = [];
        this.goodslist.forEach((item,index)=>{
          listb.push(item.id)
        })
        lista = listb
      }


      shoucTo({
        // index: 0,
        storeId: row.storeId,
        valueIdList: lista
      }).then((res) => {
        this.$message({
          message: '操作成功',
          type: 'success'
        });
        this.ready();
      });
    },
  }
}
</script>

<style scoped>
.bodya {
  padding: 20px;
}

.lab1 {
  text-align: right;
  height: 30px;
  line-height: 30px;
  padding-right: 10px;
}

.head {
  padding-top: 10px;
  text-align: right;
}

.labelbox{
  width: 90%;
  overflow: hidden;
  margin: auto;
}

.label-1{
  font-size: 16px;
  color: #333333;
  margin-top: 15px;
  border-bottom: 1px solid #F2F2F8;
  height: 50px;
  line-height: 50px;
}

.label-title{
  width: 100%;
  height: 40px;
  line-height: 40px;
  font-size:14px;
}

.label-2{
  width: 100%;
  min-height: 114px;
  border: 1px solid #CBD6E2;
  border-radius: 5px;
  display: flex;
  flex-wrap: wrap;
  padding-bottom: 20px;
}

.labelitem{
  height: 30px;
  line-height: 30px;
  text-align: center;
  padding-left: 13px;
  padding-right: 13px;
  font-size:12px;
  color: #333;
  background: #E5E5E5;
  margin-top: 20px;
  margin-left: 20px;
  border-radius: 5px;
  cursor: pointer;
  transition: 0.2s;
}

.labelitem:hover{
  background: #E0EBFF;
  color: #2E73F3;
}

.labelitem:hover>.labelicon{
  display: block;
}

.labelicon{
  width: 16px;height: 16px;position: absolute;right:-4px;top: -4px;z-index: 99;
  display: none;
  transition: 0.2s;
}
.drag-handle {
  cursor: move;
  padding: 5px;
}

.el-table__row.is-dragging {
  opacity: 0.6;
}
</style>
