<template>
  <div>
    <el-dialog
      title="新增产品"
      :visible.sync="dialogVisible"
      width="60%"
      :before-close="handleClose">
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row :gutter="10" style="display:flex;flex-wrap:wrap">
          <el-col :span="12">
            <el-form-item label="产品名称" prop="productName">
              <el-input v-model="form.productName" placeholder="请输入产品名称"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="其他名称" prop="formerName">
              <el-input v-model="form.formerName" placeholder="请输入其他名称"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="产品类目" prop="categoryId">
              <treeselect
                v-model="form.categoryId"
                :options="tableData"
                :normalizer="normalizer"
                placeholder="请选择上级品类"
              >
              <div slot="option-label" slot-scope="{ node }" class="category-flex">
                <span>{{ node.label }}</span>
                <span class="category-flex-desc">{{ node.raw.model ? `规格型号：${node.raw.model}` : '' }}</span>
              </div>
            </treeselect>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="产品编码" prop="productCode">
              <el-input v-model="form.productCode" placeholder="请输入产品编码"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="重量">
              <el-input v-model="form.weight" placeholder="请输入产品重量">
                <span slot="suffix">Kg</span>
              </el-input>
            </el-form-item>
          </el-col>
<!--          <el-col :span="12">-->
<!--            <el-form-item label="属性" prop="attribute">-->
<!--              <el-input v-model="form.attribute" placeholder="请输入行业属性"/>-->
<!--            </el-form-item>-->
<!--          </el-col>-->
          <el-col :span="12">
            <el-form-item label="规格" prop="specs">
              <el-input v-model="form.specs" placeholder="请输入规格"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="型号" prop="model">
              <el-input v-model="form.model" placeholder="请输入型号"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="执行标准" prop="standard">
              <el-input v-model="form.standard" placeholder="请输入执行标准"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="材质" prop="materialQuality">
              <el-input
                v-model="form.materialQuality"
                placeholder="请输入材质"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="表面" prop="surface">
              <el-input v-model="form.surface" placeholder="请输入表面处理方式"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="单位" prop="unit">
              <template slot-scope="scope">
                <el-select v-model="form.unit" placeholder="请选择" style="width:100%;">
                  <el-option
                    v-for="(item,index) in options"
                    :key="index"
                    :label="item"
                    :value="item">
                  </el-option>
                </el-select>
              </template>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="排序" prop="sort">
              <el-input v-model.number="form.sort" onkeyup="this.value=this.value.replace(/\D/g,'')" @blur="form.sort = $event.target.value" placeholder="请输入排序，数值越大显示越靠前" />
            </el-form-item>
          </el-col>
        </el-row>


        <el-form-item label="图片" prop="picture1">
          <image-upload v-model="form.picture1"/>
        </el-form-item>
        <el-form-item label="三维图片" prop="diagram">
          <image-upload v-model="form.diagram"/>
        </el-form-item>

        <el-form-item label="图纸" prop="draw">
          <file-upload v-model="form.draw" :file-type="fileType"/>
        </el-form-item>
        <el-form-item label="工艺视频" prop="technology">
          <file-upload v-model="form.technology" :file-type="['mp4']" :file-size="200" />
        </el-form-item>

        <!-- <el-form-item label="产品详情" >
          <div style="height: 500px;">
            <div id="editorContainer"></div>
          </div>
        </el-form-item> -->
      </el-form>


      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="dialogVisible = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>


<script>
import {getlistb} from "@/api/purchase/category";
import {addlist} from "@/api/houtai/gongyu/chanpin";
import {addText} from "@/api/purchase/gongyingshang";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import E from "wangeditor";
import { isNumber } from '@/utils/validate'

export default {
  name: 'add',
  components: {
    Treeselect
  },
  data() {
    return {
      options: ['吨', '千克', '个', '件', '套','米','支'],
      fileType: ["png", "jpg", "jpeg", "pdf"],
      dialogVisible: false,
      // 表单参数
      form: {},
      tableData: [],
      // 表单校验
      rules: {
        unit: [
          {required: true, message: "请选择单位", trigger: "change"},
        ],
        categoryId: [
          {required: true, message: "请选择产品类目", trigger: "change"},
        ],
        productName: [
          {required: true, message: "产品名称不能为空", trigger: "blur"},
        ],
        productCode: [
          {required: true, message: "产品编码不能为空", trigger: "blur"},
        ],
        productType: [
          {required: true, message: "产品类型不能为空", trigger: "change"},
        ],
        picture1: [
          {required: true, message: "图片不能为空", trigger: "blur"},
        ],
        draw: [{required: true, message: "图纸不能为空", trigger: "blur"}],
        // attribute: [
        //   {required: true, message: "请输入行业属性", trigger: "blur"},
        // ],
        specs: [{required: true, message: "规格不能为空", trigger: "blur"}],
        model: [{required: true, message: "型号不能为空", trigger: "blur"}],
        materialQuality: [
          {required: true, message: "材质不能为空", trigger: "blur"},
        ],
        surface: [{required: true, message: "请输入表面处理方式", trigger: "blur"}],
        weight: [
          { required: true, message: '请输入产品重量', trigger: 'blur' },
          { validator: isNumber, message: '输入有误请重新输入', trigger: 'blur' }
        ],
      },
    }
  },
  created() {
  },
  methods: {
    showmodel() {
      let that = this;
      this.ready();
      this.dialogVisible = true;
      // setTimeout(function(){
      //   that.mapinit();
      // }, 500)
    },
    mapinit(){
      let that = this;
      const editorElement = document.getElementById('editorContainer');
      // 销毁旧的编辑器实例
      if (editorElement.editor) {
        editorElement.editor.destroy();
      }
      // 创建富文本编辑器实例
      const editor = new E('#editorContainer');
      // 自定义配置
      editor.config.height = 400;
      // 配置全屏功能按钮是否展示
      editor.config.showFullScreen = false;
      editor.config.uploadImgShowBase64 = true
      // 自定义配置
      editor.config.menus = [
        'head',
        'bold',
        'fontSize',
        'fontName',
        'strikeThrough',
        'foreColor',
        'backColor',
        'link',
        'list',
        'justify',
        'italic',
        'quote',
        'underline',
        'justify',
        'quote',
        'undo',
        'redo',
        'image',
        'video',
        'table',
        'hr',
        'preview',
        // 'fullscreen',
        'print',
        'emoticon',
        'outline'
      ];
      // 初始化富文本编辑器
      editor.create();
      // 将编辑器实例保存到组件的变量中
      this.editor = editor;
    },
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.name,
        model: node.model,
        children: node.children,
      };
    },
    ready() {
      getlistb({
        pageNum: 1,
        pageSize: 999,
      }).then((res) => {
        let lista = res.data;
        this.tableData = lista;
      });
    },
    getContent() {
      // 获取编辑器的内容
      const content = this.editor.txt.html()
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.form.sort = this.form.sort || 0

          //
          // getContent

          addlist(this.form).then((res) => {
            this.$message('新增成功');
            this.dialogVisible = false;
            this.$emit('shuaxin')
            this.form = {};
            addText({
              objectId:res.data,
              type:'chanpin',
              textArea:this.editor.txt.html()}).then((res) => {
            });


          });
        }
      });
    },
    handleClose(done) {
      this.$confirm('确认关闭？')
      .then(_ => {
        done();
      })
      .catch(_ => {
      });
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep {
  .category-flex {
    display: flex;
    align-items: center;
    &-desc {
      font-size: 12px;
      color: #999999;
      margin-left: 10px;
    }
  }
}
</style>
