<template>
  <div class="bodya">
<!--供应商所有-->
    <el-row>
      <el-col :span="24">
        <el-row>
          <el-col :span="4">
            <el-input v-model="queryParams.name" size="small" placeholder="请输入名称"></el-input>
          </el-col>
          <el-col :span="8">
            <el-button size="small" @click="shuaxinb()" style="margin-left: 10px;" type="primary">
              搜索
            </el-button>
            <el-button size="small" @click="cz()" style="margin-left: 10px;" type="">
              重置
            </el-button>
          </el-col>
        </el-row>
        <!-- <div class="head">
          <el-button @click="add()" type="primary" size="small" plain>
            新增
          </el-button>
        </div> -->

        <!-- <shoucang  :typeshowa="typeshowa"   @shuaxina="shuaxina"  @shuaxinc="shuaxinc"  @xza="xza"  ref="Refshoucang" :shoucangType="shoucangData.type"></shoucang> -->



        <el-table :data="tableData" border
              row-key="id"
              :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
              size="small" style="width: 100%;margin-top: 10px;">
      <el-table-column
        align="center"
        label="#"
        type="index">
      </el-table-column>
      <el-table-column prop="name" label="供应商名称" align="center" show-overflow-tooltip></el-table-column>

      <el-table-column prop="slogan" label="联系人" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
               {{scope.row.contactList[0].nickName}}
            </template>
    </el-table-column>

    <el-table-column prop="slogan" label="电话" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
               {{scope.row.contactList[0].phone}}
            </template>
    </el-table-column>
    <el-table-column prop="slogan" label="职务" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
               {{scope.row.contactList[0].post}}
            </template>
    </el-table-column>

      <el-table-column prop="logoUrl" label="logo" align="center" show-overflow-tooltip>
        <template slot-scope="scope">
          <el-image
            style="width:50px; height: 50px"
            :src="up_file + scope.row.logoUrl"
            @click="xzimg(up_file+scope.row.logoUrl)"
            :preview-src-list="srcList">
          </el-image>
        </template>
      </el-table-column>

      <el-table-column prop="certificationUrl" label="资质文件" align="center" show-overflow-tooltip>
        <template slot-scope="scope">
          <template v-if="scope.row.certificationUrl.length > 0">
            <el-button size="small" type="primary" @click="xiaz(scope.row.certificationUrl)">
              下载
            </el-button>
          </template>
          <template v-if="scope.row.certificationUrl.length == 0">
            暂无文件
          </template>
        </template>
      </el-table-column>
      <el-table-column prop="address" label="地址" align="center" show-overflow-tooltip>
      </el-table-column>
      <el-table-column  label="公域状态" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
               {{scope.row.status==0?'未升级':"已升级"}}
            </template>
    </el-table-column>
          <el-table-column fixed="right" align="center" label="操作" width="200">
        <template slot-scope="scope">
          <el-button v-if="scope.row.status==0" type="text" @click="sj(scope.row)" size="small">
            升级到公域
          </el-button>
          <el-button  type="text" @click="showinfo(scope.row)" size="small">
            详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>


        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="ready"
        />
      </el-col>
    </el-row>
     <!-- <add ref="Refadd"></add> -->
     <!-- <edit ref="Refedit"></edit> -->

      <!-- 供应商详情 -->
      <el-drawer
  title="我是标题"
  :visible.sync="gysview"
  direction="ltr"
  size="30%"
  :with-header="false">

      <div class="infobox">
        <div class="infoitem">
            <div>公司名称</div>
            <div>{{gysinfo.name}}</div>
        </div>
        <template v-for="(item,index) in gysinfo.contactList">
        <div class="infoitem" >
            <div>第{{index+1}}联系人</div>
            <div>{{item.nickName}}</div>
        </div>
        <div class="infoitem" >
            <div>职务</div>
            <div>{{item.post}}</div>
        </div>
        <div class="infoitem" >
            <div>联系方式</div>
            <div>{{item.phone}}</div>
        </div>
      </template>
        <div class="infoitem">
            <div>公司地址</div>
            <div>{{gysinfo.address}}</div>
        </div>
        <div class="infoitem">
            <div>logo</div>
            <div>
              <el-image
                style="width:50px; height: 50px;text-indent: 0;"
                :src="up_file + gysinfo.logoUrl"
                @click="xzimg(up_file+gysinfo.logoUrl)"
                :preview-src-list="srcList">
              </el-image>
            </div>
        </div>
        <div class="infoitem">
            <div>资质文件</div>
            <div>
              <el-image
                style="width:200px; height: 300px;text-indent: 0;"
                :src="up_file + gysinfo.certificationUrl"
                @click="xzimg(up_file+gysinfo.certificationUrl)"
                :preview-src-list="srcList">
              </el-image>
            </div>
        </div>
      </div>


</el-drawer>

  <!-- 默认密码提示 -->
  <el-dialog
  title="温馨提示"
  :visible.sync="tsview"
  width="30%"
  >
  <span>请使用默认密码123456登录</span>
</el-dialog>

  </div>
</template>

<script>
import {getlist, dellist,tjlist,upgrade} from "@/api/houtai/siyu/gongying";

import {
  getlistb,
  shoucTo,
  delshouc,
  shoucGo
} from "@/api/houtai/shoucang";

export default {
  name: 'Recommended',
  components: {

  },
  data() {
    return {
      gysview:false,
      gysinfo:{},
      tsview:false,
      up_file: '',
      tableData: [],
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: '',
        phone: '',
        address: '',
        status: '',
      },
      audit_status: [],
      listClass: '',
      imgurl: '',
      srcList: [
        'https://fuss10.elemecdn.com/8/27/f01c15bb73e1ef3793e64e6b7bbccjpeg.jpeg',
      ],
      onedata: [],
      tableDatab: [],
      typeshowa: true,



      shoucangData:{
        type: 'UserPriSupplier',
        // UserProduct,//个人公域产品收藏
        // UserPriProduct,//个人私域产品收藏
        // UserPriDemand,//个人私域需求收藏
        // UserPriSupplier,//个人私域供应商收藏
      },
      onegood: {},
      xzid: '',
      xztype: 1,
    };
  },
  created() {
    this.up_file = process.env.VUE_APP_BASE_API;
    this.ready();

  },
  methods: {
    //升级到公域
    sj(row){
      let that=this;
      upgrade({recommendId:row.id,supplierId:row.supplier}).then(function(res){
        that.$message.success(res.msg);
        that.ready();
        that.tsview=true;
      });
    },
    //查看供应商详情
    showinfo(row)
    {
        this.gysview=true;
        this.gysinfo=row;
    },
    shuaxinc(row) {
      this.xzid = row.storeId;
    },
    upa(row,indexa){
      // shoucGo
      // index
      // storeId
      // valueId
      let numa = ((((this.queryParams.pageNum-1) * this.queryParams.pageSize))+ indexa )-1;
      (numa <0 ? numa= 0 : '');
      shoucGo({
        index: numa ,
        storeId: this.xzid,
        valueId: row.id,
      }).then((res) => {
        this.$message({
          message: '操作成功',
          type: 'success'
        });
        this.shuaxinb();
      });
    },
    dowa(row,indexa){
      let numa = ((((this.queryParams.pageNum-1) * this.queryParams.pageSize))+ indexa )+1;
      (numa <0 ? numa= 0 : '');
      shoucGo({
        index: numa ,
        storeId: this.xzid,
        valueId: row.id,
      }).then((res) => {
        this.$message({
          message: '操作成功',
          type: 'success'
        });
        this.shuaxinb();
      });

    },
    readyb() {
      this.queryParams['storeId'] = this.xzid;
      this.queryParams['pageNum'] = 1;
      let datalist = [];

      datalist = JSON.parse(JSON.stringify(this.queryParams));
      datalist['keyword'] = datalist['name'];
      getlistb(datalist).then((res) => {
        let lista = res.rows;
        this.tableData = lista;
        this.total = res.total;
      });
    },
    shuaxina(row){
      if(row.type == 1) {
        this.ready();
        this.xzid = row.data.storeId;
      } else {
        this.xzid = row.data.storeId;
        this.readyb();
      }
      this.xztype = row.type;
    },
    shuaxinb(){
      if(this.xztype == 1) {
        this.ready();
        // this.xzid = row.data.storeId;
      } else {
        this.readyb();
        // this.xzid = row.data.storeId;
      }
      this.$forceUpdate();
    },
    //收藏到
    shoucang(row){
      this.onegood = row;
      this.dx = 1;
      this.$refs.Refshoucang.showmodelb();

    },
    xza(row){
      let lista = [];
      if(this.dx == 1) {
        lista =  [this.onegood.id];
      }
      shoucTo({
        // index: 0,
        storeId: row.storeId,
        valueIdList: lista
      }).then((res) => {
        this.$message({
          message: '操作成功',
          type: 'success'
        });
        this.ready();
      });
    },

    xzimg(e) {
      this.srcList[0] = e;
    },
    reTreeValue(value) {
      return value?.split(',') || undefined
    },
    ready() {
      tjlist(this.queryParams).then((res) => {
        let lista = res.rows;
        this.tableData = lista;
        this.total = res.total;
      });
    },
    cz() {
      this.queryParams.name = '';
      this.queryParams.phone = '';
      this.queryParams.address = '';
      this.shuaxinb();
    },
    xiaz(e) {
      window.open(this.up_file + e);
    },
    shaixuan(data, value, name, retname, indexa) {
      for (let i = 0; i < data.length; i++) {
        let item = data[i];
        if (item[name] == value) {
          return item[retname];
        }
      }
    },
    shaixuanb(data, value, name, retname, indexa) {
      for (let i = 0; i < data.length; i++) {
        let item = data[i];
        if (item[name] == value) {
          return item['listClass'];
        }
      }
    },

    add() {
      this.$refs.Refadd.showmodel();
    },
    edita(row) {
      this.onedata = row;
      var a=JSON.parse(JSON.stringify(row));
      this.$refs.Refedit.showmodel(a);
    },
    dela(row) {
      this.$confirm('是否删除此数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // delshouc
        if(this.xztype == 1) {
          dellist({
            ids: row.id,
          }).then((res) => {
            this.$message({
              message: '删除成功',
              type: 'success'
            });
            this.shuaxinb();
          });
        } else {
          delshouc({
            storeId: this.xzid,
            valueId: row.id,
          }).then((res) => {
            this.$message({
              message: '删除成功',
              type: 'success'
            });
            this.shuaxinb();
          });
        }


      }).catch(() => {
      });

    },


  }
}
</script>

<style scoped>
.bodya {
  padding: 20px;
}

.lab1 {
  text-align: right;
  height: 30px;
  line-height: 30px;
  padding-right: 10px;
}

.head {
  padding-top: 10px;
  text-align: right;
}

.infobox{
  width: 95%;
  margin:20px auto;
}

.infoitem{
  width: 100%;
  height: 50px;
  display: flex;
  line-height: 50px;
}
.infoitem>div:nth-child(1){
  width: 20%;
  height: 100%;
  text-align: center;
  background-color:#CBD6E2 ;
  color:black;
  font-size: 14px;
}
.infoitem>div:nth-child(2){
  text-indent: 20px;
  color: #666666;
  font-size: 14px;
}

</style>
