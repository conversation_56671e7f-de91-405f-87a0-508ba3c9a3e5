<template>
  <div class="bodya">
    <el-row>
      <el-col :span="4">
        <el-input v-model="queryParams.productName" size="small" placeholder="请输入产品名称"></el-input>
      </el-col>
      <el-col :span="4">
        <el-select clearable v-model="queryParams.status" size="small" placeholder="请选择需求状态">
          <el-option
            label="待发布"
            value="0">
          </el-option>
          <el-option
            label="已发布"
            value="1">
          </el-option>
        </el-select>
      </el-col>
      <el-col :span="4">
        <el-input v-model="queryParams.title" size="small" placeholder="请输入需求名称"></el-input>
      </el-col>
      <el-col :span="4">
        <el-button size="small" @click="ready()" style="margin-left: 10px;" type="primary">
          搜索
        </el-button>
        <el-button size="small" @click="cz()" style="margin-left: 10px;" type="">
          重置
        </el-button>
      </el-col>
    </el-row>


    <div class="head">
      <el-button @click="add()" type="primary" size="small" plain>
        新增
      </el-button>
    </div>


    <el-table :data="tableData" border
              row-key="id"
              :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
              size="small" style="width: 100%;margin-top: 10px;">

      <el-table-column
        align="center"
        label="#"
      type="index"
      width="50">
    </el-table-column>
      <el-table-column prop="title" label="标题" align="center" show-overflow-tooltip></el-table-column>
      <!-- <el-table-column prop="budget" label="求购金额" align="center" show-overflow-tooltip></el-table-column> -->
      <el-table-column label="产品列表" align="center" show-overflow-tooltip>
        <template slot-scope="scope">
        <div v-for="product in JSON.parse(scope.row.products)" :key="product.productId">
          产品名称：{{ product.productName }} - 数量：{{ product.count }} - 单价：{{ product.price }} - 单位：{{ product.unit }}
        </div>
      </template>
      </el-table-column>
      <el-table-column prop="startTime" label="开始时间" align="center" show-overflow-tooltip></el-table-column>
      <el-table-column prop="endTime" label="供货截止" align="center" show-overflow-tooltip></el-table-column>
      <el-table-column prop="endTime" label="有效时间" align="center" show-overflow-tooltip>
      <template slot-scope="scope">
        <div v-if="scope.row.status == 1" style="color: red;">{{ countdownTimers[scope.$index]}}</div>
        <div v-else>-</div>
      </template>
    </el-table-column>
      <el-table-column prop="profile" label="备注" align="center" show-overflow-tooltip></el-table-column>
      <el-table-column prop="status" label="状态" align="center" show-overflow-tooltip>
        <template slot-scope="scope">
          {{ scope.row.status == 0 ? '未发布' : '' }}
          {{ scope.row.status == 1 ? '已发布' : '' }}
        </template>
      </el-table-column>


      <el-table-column prop="createTime" label="创建时间" align="center" show-overflow-tooltip></el-table-column>
      <el-table-column fixed="right" align="center" label="操作" width="200">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="xq(scope.row)">
              订单详情
            </el-button>
          <template v-if="scope.row.status == 0">
            <el-button type="text" @click="fabu(scope.row)" size="small">
              发布
            </el-button>
            <!--            <el-button type="text" @click="edita(scope.row)" size="small">-->
            <!--              修改-->
            <!--            </el-button>-->
            <el-button type="text" @click="dela(scope.row)" size="small">
              删除
            </el-button>
          </template>
          <template v-if="scope.row.status == 1">
            <el-button type="text" size="small" @click="ck(scope.row)">
              查看报价
            </el-button>
          </template>
        </template>
      </el-table-column>
    </el-table>


    <!--    <pagination-->
    <!--      v-show="total > 0"-->
    <!--      :total="total"-->
    <!--      :page.sync="queryParams.pageNum"-->
    <!--      :limit.sync="queryParams.pageSize"-->
    <!--      @pagination="ready" />-->


    <add ref="Refadd" @shuaxin="shuaxin"></add>
    <edit ref="Refedit" @shuaxin="shuaxin"></edit>
    <ck ref="Refck" @shuaxin="shuaxin"></ck>

    <!-- 订单详情 -->
    <el-dialog
      title="订单详情"
      :visible.sync="infoview"
     >
     <div class="ddbox">
        <div>订单名</div>
        <div>{{ ddinfo.title }}</div>
     </div>
     <div class="ddbox">
        <div>开始时间</div>
        <div>{{ ddinfo.startTime }}</div>
     </div>
     <div class="ddbox">
        <div>供货截止</div>
        <div>{{ ddinfo.endTime }}</div>
     </div>
     <!-- <div class="ddbox">
        <div>倒计时</div>
        <div>{{ ddinfo.endTime }}</div>
     </div> -->
     <template v-for="(item,index) in ddinfo.products">
     <div class="ddbox">
        <div>产品{{index+1}}</div>
        <div>
            <div>名称：</div>
            <div>{{ item.productName }}</div>
            <div>数量：</div>
            <div>{{ item.count }}</div>
            <div>单位：</div>
            <div>{{ item.unit }}</div>
        </div>
     </div>
    </template>
    <div class="ddbox">
        <div>备注</div>
        <div>{{ ddinfo.remark }}</div>
     </div>
    </el-dialog>
  </div>
</template>

<script>
import {getlist, dellist, fabu} from "@/api/houtai/gongyu/xuqiu";
import add from "./add";
import edit from "./edit";
import ck from "./ck";
import moment from 'moment';
export default {
  name: 'index',
  components: {
    add,
    edit,
    ck,
  },
  data() {
    return {
      countdownTimers: [], // 保存倒计时结果的数组
      infoview:false,
      ddinfo:{},
      up_file: '',
      tableData: [],
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        productName: '',
        title: '',
        status: '',
      },
      audit_status: [],
      listClass: '',
      imgurl: '',
      srcList: [
        'https://fuss10.elemecdn.com/8/27/f01c15bb73e1ef3793e64e6b7bbccjpeg.jpeg',
      ],
      onedata: [],
      tableDatab: [],

    };
  },
  created() {
    this.up_file = process.env.VUE_APP_BASE_API;
    this.ready();

  },
  mounted() {
    // var showtype = this.$route.query.showtype;
    // if(showtype) {
    //   this.$refs.Refadd.showmodel();
    // }
    this.startCountdown();
    const queryData = this.$route.query.data;
    if (queryData) {
      const jsonData = JSON.parse(queryData);
      // 使用 jsonData 做你需要的操作
      this.$refs.Refadd.showmodel(jsonData);

    }
  },
  methods: {
    startCountdown() {
      // 计算剩余时间并更新倒计时数组
      this.countdownTimers = this.tableData.map((item) => this.getCountdown(item.endTime));

      // 每秒更新一次倒计时
      setInterval(() => {
        this.countdownTimers = this.tableData.map((item) => this.getCountdown(item.endTime));
      }, 1000);
    },
    getCountdown(endTime) {
      const endTimeObj = moment(endTime); // 将截止时间转换为moment对象
      const now = moment(); // 当前时间的moment对象
      const remainingTime = Math.max(endTimeObj.diff(now), 0); // 剩余时间（毫秒），确保不为负数

      if (remainingTime > 0) {
        const duration = moment.duration(remainingTime);
        const hours = duration.hours();
        const minutes = duration.minutes();
        const seconds = duration.seconds();

        return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
      } else {
        return '';
      }
    },
        // 查看订单详情
        xq(row){
      this.infoview = true;
      const parsedProducts = JSON.parse(row.products);
      this.ddinfo = { ...row, products: parsedProducts };
    },
    plist(e){
      var x= JSON.parse(e);
      return x[0].count;
    },
    shuaxin() {
      this.ready();
    },
    xzimg(e) {
      this.srcList[0] = e;
    },
    reTreeValue(value) {
      return value?.split(',') || undefined
    },
    ready() {
      getlist(this.queryParams).then((res) => {
        let lista = res.rows;
        this.tableData = lista;
        this.total = res.total;
      });
    },
    cz() {
      this.queryParams.productName = '';
      this.queryParams.title = '';
      this.queryParams.status = '';
      this.ready();
    },
    xiaz(e) {
      window.open(this.up_file + e);
    },
    ck(e) {
      this.$refs.Refck.showmodel(e);
    },
    shaixuan(data, value, name, retname, indexa) {
      for (let i = 0; i < data.length; i++) {
        let item = data[i];
        if (item[name] == value) {
          return item[retname];
        }
      }
    },
    shaixuanb(data, value, name, retname, indexa) {
      for (let i = 0; i < data.length; i++) {
        let item = data[i];
        if (item[name] == value) {
          return item['listClass'];
        }
      }
    },

    add() {
      this.$refs.Refadd.showmodel();
    },
    fabu(row) {
      this.$confirm('是否发布[' + row.title + ']需求?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        fabu({
          id: row.id
        }).then((res) => {
          this.$message({
            type: 'success',
            message: '发布成功'
          });
          this.ready();
        });
      }).catch(() => {
      });
    },
    edita(row) {
      this.onedata = row;
      this.$refs.Refedit.showmodel(row);
    },
    dela(row) {
      this.$confirm('是否删除此数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        dellist({
          ids: row.id,
        }).then((res) => {
          this.$message({
            message: '删除成功',
            type: 'success'
          });
          this.ready();
        });
      }).catch(() => {
      });
    },


  }
}
</script>

<style scoped>
.bodya {
  padding: 20px;
}

.lab1 {
  text-align: right;
  height: 30px;
  line-height: 30px;
  padding-right: 10px;
}

.head {
  padding-top: 10px;
  text-align: right;
}

.ddbox{
  display: flex;
  line-height: 50px;
}

.ddbox>div:first-child{
  width: 160px;
  background-color: rgba(248, 248, 249);
  text-indent: 20px;
}
.ddbox>div:last-child{
  text-indent: 20px;
  display: flex;
}
</style>
