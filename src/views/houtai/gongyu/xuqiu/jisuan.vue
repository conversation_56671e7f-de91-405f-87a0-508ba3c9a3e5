<template>
  <div class="calculator" >
    <input type="text" v-model="display" readonly @focus="isFocused = true" @blur="isFocused = false">
    <div class="buttons">
      <div class="row">
        <button class="key" @click="appendToDisplay('7')">7</button>
        <button class="key" @click="appendToDisplay('8')">8</button>
        <button class="key" @click="appendToDisplay('9')">9</button>
        <button class="key" @click="appendToDisplay('/')">/</button>
      </div>
      <div class="row">
        <button class="key" @click="appendToDisplay('4')">4</button>
        <button class="key" @click="appendToDisplay('5')">5</button>
        <button class="key" @click="appendToDisplay('6')">6</button>
        <button class="key" @click="appendToDisplay('*')">*</button>
      </div>
      <div class="row">
        <button class="key" @click="appendToDisplay('1')">1</button>
        <button class="key" @click="appendToDisplay('2')">2</button>
        <button class="key" @click="appendToDisplay('3')">3</button>
        <button class="key" @click="appendToDisplay('-')">-</button>
      </div>
      <div class="row">
        <button class="key" @click="appendToDisplay('0')">0</button>
        <button class="key" @click="appendToDisplay('.')">.</button>
        <button class="key" @click="calculate()">=</button>
        <button class="key" @click="appendToDisplay('+')">+</button>
      </div>
      <div class="row">
        <button class="key delete" @click="clear()">C</button>
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    data() {
      return {
        display: '',
        isFocused: false,
      };
    },
    methods: {
      appendToDisplay(value) {
        this.display += value;
      },
      calculate() {
        try {
          const result = eval(this.display);
          this.display = `${this.display} = ${result}`;
        } catch (error) {
          this.display = 'Error';
        }
      },
      clear() {
        this.display = '';
      },
    },
  };
</script>

<style>
  .calculator {
    width: 200px;
    margin: 0 auto;
    text-align: center;
    font-family: Arial, sans-serif;
  }

  input[type="text"] {
    width: 100%;
    padding: 5px;
    margin-bottom: 10px;
  }

  .buttons {
    display: flex;
    flex-direction: column;
  }

  .row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
  }

  button.key {
    width: 45px;
    height: 30px;
    font-size: 16px;
    cursor: pointer;
    background-color: white;
    border: 1px solid black;
  }

  button.delete {
    background-color: #fff;
    color: #333;
  }
</style>
