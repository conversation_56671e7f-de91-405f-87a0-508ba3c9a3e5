<template>
  <div>
    <el-dialog
      title="修改采购需求"
      :visible.sync="dialogVisible"
      v-loading="loading"
      width="70%">
      <div>
        <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm">

          <el-form-item label="标题" prop="title">
            <el-input v-model="ruleForm.title"></el-input>
          </el-form-item>
<!--          <el-form-item label="产品类目" prop="categoryId">-->
<!--            <treeselect-->
<!--              v-model="ruleForm.categoryId"-->
<!--              :options="tableData"-->
<!--              :normalizer="normalizer"-->
<!--              placeholder="请选择上级品类"-->
<!--              @select="changea"-->
<!--            />-->
<!--          </el-form-item>-->

          <!--          <el-form-item label="预算金额" prop="budget">-->
          <!--            <el-input type="number" v-model="ruleForm.budget"></el-input>-->
          <!--          </el-form-item>-->
          <el-form-item label="	开始时间" prop="startTime">
            <!--          <el-input v-model="ruleForm.startTime"></el-input>-->
            <el-date-picker
              style="width:100%"
              v-model="ruleForm.startTime"
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="选择日期">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="结束时间" prop="endTime">
            <el-date-picker
              style="width:100%"
              v-model="ruleForm.endTime"
              type="datetime"
              :picker-options="pickerOptions"
              value-format="yyyy-MM-dd HH:mm:ss"
              placeholder="选择日期">
            </el-date-picker>
            <!--            <el-input  v-model="ruleForm.endTime"></el-input>-->
          </el-form-item>
          <el-form-item label="采购产品" prop="products">

            <el-select multiple
                       collapse-tags
                       filterable
                       @visible-change="visibleChange"
                       style="width:100%"
                       v-model="ruleForm.products" placeholder="请选择">
              <el-option
                v-for="(item,index) in chanpinlist"
                :key="item.id"
                :label="item.productName"
                :value="item.id">
                <span style="float: left">{{ item.productName }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">{{ item.model }}</span>
              </el-option>
            </el-select>


            <el-table
              :data="tableList"
              size="mini"
              style="width: 100%">
              <el-table-column
                prop="productName"
                label="名称">
              </el-table-column>
              <el-table-column
                prop="num"
                label="数量">
                <template slot-scope="scope">
                  <input class="input1" size="mini" v-model="scope.row.numa" type="number" />
                </template>
              </el-table-column>
              <el-table-column
                prop="price"
                label="价格">
                <template slot-scope="scope">
                  <input class="input1" size="mini" v-model="scope.row.price" type="number" />
                </template>
              </el-table-column>
              <el-table-column
                prop="price"
                label="单位">
                <template slot-scope="scope">
                  <!--                  <input class="input1" size="mini" v-model="scope.row.unit" type="number"></input>-->
                  <el-select v-model="scope.row.unit" placeholder="请选择">
                    <el-option
                      v-for="(item,index) in options"
                      :key="index"
                      :label="item"
                      :value="item">
                    </el-option>
                  </el-select>

                </template>
              </el-table-column>
              <el-table-column
                prop="address"
                label="操作">
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    type="danger"
                    @click="handleDelete(scope.$index, scope.row)" plain>删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <!--            <el-input v-model="ruleForm.products"></el-input>-->
          </el-form-item>


          <el-form-item label="备注" prop="remark">
            <el-input type="textarea" v-model="ruleForm.profile"></el-input>
            <!--            <el-input v-model="ruleForm.remark"></el-input>-->
          </el-form-item>


          <el-form-item>
            <el-button type="primary" @click="submitForm('ruleForm')">立即创建</el-button>
          </el-form-item>
        </el-form>
      </div>


      <!--      <span slot="footer" class="dialog-footer">-->
      <!--    <el-button @click="dialogVisible = false">取 消</el-button>-->
      <!--    <el-button type="primary" @click="dialogVisible = false">确 定</el-button>-->
      <!--  </span>-->
    </el-dialog>


    <el-dialog v-dialogDragBox title="" :visible.sync="centerDialogVisible" width="50%" center>
      <img :src="up_file+imgfile" style="width: 100%;height: 100%" alt=""/>
      <span slot="footer" class="dialog-footer">
        <el-button @click="centerDialogVisible = false">取 消</el-button>
      </span>
    </el-dialog>


  </div>
</template>

<script>
import Cookies from "js-cookie";
import {getlistb} from "@/api/houtai/gongyu/chanpin";
import {editlist} from "@/api/houtai/gongyu/xuqiu";

import {getlist} from "@/api/purchase/category";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

export default {
  name: 'add',
  components: {
    Treeselect,
  },
  data() {
    return {
      dialogVisible: false,
      tableData: [],
      ruleForm: {
        title: '',
        supplierIds: '',
        startTime: '',
        endTime: '',
        budget: '',
        profile: '',
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now(); // 禁用过去的时间
        }
      },
      rules: {
        title: [
          {required: true, message: '请输入标题', trigger: 'blur'},
        ],
        supplierIds: [
          {required: true, message: '请选择供应商', trigger: 'blur'}
        ],
        startTime: [
          {required: true, message: '请选择开始时间', trigger: 'change'}
        ],
        endTime: [
          {required: true, message: '请选择结束时间', trigger: 'change'}
        ],
        budget: [
          {required: false, message: '请输入预算金额', trigger: ' blur'}
        ],
      },
      up_file: '',
      headers: {},
      loading: false,
      imgfile: '',
      centerDialogVisible: false,
      gongyinglist: [],
      chanpinlist: [],
      tableList: [],
      uptable: [],
      options: ['吨', '千克', '个', '件', '套'],

      leimu: {},
      onedata: [],
    }
  },
  created() {
    this.up_file = process.env.VUE_APP_BASE_API;
    let token = Cookies.get("Admin-Token");
    this.headers = {
      Authorization: "Bearer " + token,
    };
    this.ready();
  },
  methods: {
    changea(value) {
      this.leimu = value;
      this.readyb();
    },
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.name,
        children: node.children,
      };
    },
    ready() {
      getlist({
        pageNum: 1,
        pageSize: 999,
      }).then((res) => {
        let lista = res.data;
        this.tableData = lista;
      });
    },
    readyb() {
      getlistb({
        categoryId: this.leimu.id
        // pageNum: 1,
        // pageSize: 999,
      }).then((res) => {
        let lista = res.data;
        this.chanpinlist = lista;
        this.ruleForm.products = [];
        this.tableList = [];


      });
    },
    visibleChange(e) {
      // let ea = JSON.parse(JSON.stringify(e));
      if (!e) {
        let listm = [];
        this.ruleForm.products.forEach((item, index) => {
          this.chanpinlist.forEach((itema, indexa) => {
            if (itema.id == item) {
              let lista = itema;
              lista['numa'] = 1;
              lista['price'] = 1;
              lista['unit'] = '个';
              listm.push(lista)
            }
          })
        });
        this.tableList = listm;
      }
    },
    xq() {
    },
    handleDelete(e, b) {
      b.id
      let numa;
      this.ruleForm.products.forEach((item, index) => {
        if (item == b.id) {
          numa = index;
        }
      })
      this.ruleForm.products.splice(numa, 1);
      this.tableList.splice(e, 1);
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.add();
        } else {
          return false;
        }
      });
    },
    add() {
      let that = this;
      // let parentId =  this.ruleForm.parentIdb;

      let lista = [];
      this.tableList.forEach((item, index) => [
        lista.push({
          'productName': item.productName,
          'productId': item.id,
          'count': item.numa,
          'price': item.price,
          'unit': item.unit,
        })
      ])


      let datalist = {
        title: this.ruleForm.title,
        // budget: this.ruleForm.budget,
        endTime: this.ruleForm.endTime,
        profile: this.ruleForm.profile,
        startTime: this.ruleForm.startTime,
        categoryId: this.ruleForm.categoryId,
        products: lista,
        id: this.onedata.id,
      }

      editlist(datalist).then((res) => {
        this.$message({
          message: '提交成功',
          type: 'success'
        });
        this.dialogVisible = false;
        this.$emit('shuaxin')
      });
    },
    showmodel(row) {
      // this.ready();
      this.onedata = row;
      this.ruleForm = JSON.parse(JSON.stringify(row));
      let lista = JSON.parse(JSON.stringify(JSON.parse(row.products)));
      lista.forEach((item, index) => {
        lista[index]['numa'] = item['count'];
      })
      this.tableList = lista;


      // lista['numa'] = 1;
      // lista['price'] = 1;
      // lista['unit'] = '个';

      this.dialogVisible = true;
    },
    handleChange(value) {
    }
  }
}
</script>

<style scoped>
.input1 {
  height: 30px;
  line-height: 30px;
  border: 1px solid #b4bccc;
  border-radius: 5px;
  padding-left: 10px;
  outline: none;
}
</style>
