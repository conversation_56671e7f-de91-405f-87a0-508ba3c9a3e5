
<template>
  <div>
    <!-- 顶部tabls -->
    <div class="tabs">
      <el-tabs v-model="editableTabsValue" type="card" @tab-click="getProduct()">
        <el-tab-pane
          v-for="(item, index) in editableTabs"
          :key="item.name"
          :label="item.name"
          :name="item.name"
        >
        </el-tab-pane>
      </el-tabs>
    </div>
    <!-- 产品列表 -->
    <div class="pbox">
      <div class="pbox1">
        <div style="margin-left: 20px;">报价关联产品</div>
        <div style="margin-left: 110px;">报价编号：54561651654654</div>
      </div>

      <div class="pbox2">
        <div style="margin-left: 20px;">产品名称：{{tableData.productName}}</div>
        <div>产品编号：{{tableData.productCode}}</div>
        <div>产品图片：
          <image :src="formatProductImg(tableData)"></image>
        </div>
        <div>产品标签：
          <template  v-for="(itema, indexa) in tableData.labels">
            <span>
              {{itema}} {{indexa == 0 ? '' :','}}
            </span>
          </template>
        </div>
      </div>
    </div>


    <div class="box">
      <!-- 左侧表格部分 -->
      <div class="left">
        <div class="caradbox" v-for="(itema,indexa) in quoteInfos">
          <div class="caradheader">
            {{itema.name}}
          </div>
          <template v-for="(item,index) in itema.item">
          <div class="caradbody" >
            <div>
              <el-row>
                <el-col :span="5">
                  <div class="carmsg1">
                    <span class="carmsg2">{{item.tabName}}</span>
                  </div>
                </el-col>
                <el-col :span="5">
                  <div class="carmsg1">
                    <span class="carmsg32">毛重：{{item.weight}}</span>
                  </div>
                </el-col>
                <el-col :span="5">
                  <div class="carmsg1">
                   <span class="carmsg32">净重：{{item.totalPrice}}</span>
                  </div>
                </el-col>
                <el-col :span="5">
                  <div class="carmsg1">
                   <span class="carmsg32">小计：{{item.price}}</span>
                  </div>
                </el-col>
                <!--<el-col :span="4">
                  <div class="icon1" @click="qha(indexa,index)">
                    <i class="el-icon-arrow-down"></i>
                  </div>
                </el-col>-->
                <div class="icon1" @click="qha(indexa,index)">
                  <i class="el-icon-arrow-down"></i>
                </div>
              </el-row>
            </div>
          </div>
          <div v-if="item.show">
            <div class="caradheaderb">
              <div v-if="item.tabName=='圆管计算'">
                <el-form ref="form1" :model="form1" label-width="80px">
                  <el-row>
                    <el-col :span="12">
                      <el-row>
                        <el-col :span="24">
                          <el-form-item label="材料密度" prop="density">
                            <el-select clearable size="small" placeholder="请选择材料密度" v-model="item.density" :disabled="item.distype">
                              <el-option
                                label="灰铸铁(≤HT200)#7.2"
                                value="7.2">
                              </el-option>
                              <el-option
                                label="灰铸铁(≥HT200)#7.35"
                                value="7.35">
                              </el-option>
                              <el-option
                                label="白口铸铁#7.4~7.7"
                                value="7.4">
                              </el-option>
                              <el-option
                                label="可锻铸铁#7.2~7.4"
                                value="7.2">
                              </el-option>
                              <el-option
                                label="工业纯铁#7.87"
                                value="7.87">
                              </el-option>
                              <el-option
                                label="铸钢#7.8"
                                value="7.8">
                              </el-option>
                              <el-option
                                label="钢材#7.85"
                                value="7.85">
                              </el-option>
                              <el-option
                                label="低碳钢(含碳0.1%)#7.85"
                                value="7.85">
                              </el-option>
                              <el-option
                                label="中碳钢(含碳0.4%)#7.82"
                                value="7.82">
                              </el-option>
                              <el-option
                                label="高碳钢(含碳1%)#7.81"
                                value="7.81">
                              </el-option>
                            </el-select>
                          </el-form-item>
                        </el-col>
                        <el-col :span="24">
                          <el-form-item label="属性选择" >
                            <el-radio-group v-model="item.radio">
                              <el-radio  label="nest:3,4" :disabled="item.distype">外直径+壁管厚</el-radio>
                              <el-radio  label="nest:3,10" :disabled="item.distype">外直径+内直径</el-radio>
                            </el-radio-group>
                          </el-form-item>
                        </el-col>
                      </el-row>
                      <el-row>
                        <el-col :span="24">
                          <el-form-item label="外直径(mm)" prop="diameter">
                            <el-input v-model="item.diameter" placeholder="请输入外直径" :disabled="item.distype"/>
                          </el-form-item>
                        </el-col>
                        <el-col :span="24">
                          <el-form-item label="管壁厚(mm)" prop="tube">
                            <el-input v-model="item.tube" placeholder="请输入壁管厚" :disabled="item.distype"/>
                          </el-form-item>
                        </el-col>
                      </el-row>
                      <el-row>
                        <el-col :span="24">
                          <el-form-item label="长度(mm)" prop="length">
                            <el-input v-model="item.length" placeholder="请输入长度" :disabled="item.distype"/>
                          </el-form-item>
                        </el-col>
                        <el-col :span="24">
                          <el-form-item label="管材报数" prop="pipenum">
                            <el-input v-model="item.pipeNum" placeholder="请输入管材报数" :disabled="item.distype"/>
                          </el-form-item>
                        </el-col>
                      </el-row>
                    </el-col>
                    <el-col :span="12">
                      <div style="text-align: center">
                        <img style="width:350px;" src="../../../../../public/imgs/guancai.png">
                      </div>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="24">
                      <el-form-item label="">
                        <el-button type="primary" size="mini" @click="change(item,indexa,index)" :disabled="item.distype">开始计算</el-button>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <h4 class="form-header h4">计算结果</h4>
                  <el-row>
                    <el-col :span="12">
                      <el-form-item label="毛重(kg)" prop="weight">
                        <el-input v-model="item.weight" :disabled="item.distype"></el-input>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="净重(kg)" prop="weight">
                        <el-input v-model="item.netWeight" :disabled="item.distype"></el-input>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="12">
                      <el-form-item label="单价" prop="price">
                        <el-input v-model="item.price" :disabled="item.distype"></el-input>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="小计" prop="totalprice">
                        <el-input v-model="item.totalPrice" :disabled="item.distype"></el-input>
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form>
                <div slot="footer" class="dialog-footer">
                  <div style="text-align: right">
                    <el-button type="primary" size="mini" :disabled="item.disabled" @click="editState(indexa,index)">修改设置</el-button>
                    <el-button type="primary"  size="mini" :disabled="item.distype" @click="submitForm(item,indexa,index)">保存设置</el-button>
                  </div>
                </div>
              </div>
              <div v-if="item.tabName=='圆钢计算'">
                <el-form ref="form2" :model="form" label-width="80px">
                  <el-row>
                    <el-col :span="12">
                      <el-form-item label="材料密度" prop="density">
                        <el-select clearable size="small" placeholder="请选择材料密度" v-model="item.density" :disabled="item.distype">
                          <el-option
                            label="灰铸铁(≤HT200)#7.2"
                            value="7.2">
                          </el-option>
                          <el-option
                            label="灰铸铁(≥HT200)#7.35"
                            value="7.35">
                          </el-option>
                          <el-option
                            label="白口铸铁#7.4~7.7"
                            value="7.4">
                          </el-option>
                          <el-option
                            label="可锻铸铁#7.2~7.4"
                            value="7.3">
                          </el-option>
                          <el-option
                            label="工业纯铁#7.87"
                            value="7.87">
                          </el-option>
                          <el-option
                            label="铸钢#7.8"
                            value="7.8">
                          </el-option>
                          <el-option
                            label="钢材#7.85"
                            value="7.85">
                          </el-option>
                          <el-option
                            label="低碳钢(含碳0.1%)#7.85"
                            value="7.85">
                          </el-option>
                          <el-option
                            label="中碳钢(含碳0.4%)#7.82"
                            value="7.82">
                          </el-option>
                          <el-option
                            label="高碳钢(含碳1%)#7.81"
                            value="7.81">
                          </el-option>
                        </el-select>

                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="直径(mm)" prop="diameter">
                        <el-input v-model="item.diameter" placeholder="请输入外直径" :disabled="item.distype"/>
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row>
                    <el-col :span="12">
                      <el-form-item label="长度(mm)" prop="length">
                        <el-input v-model="item.length" placeholder="请输入长度" :disabled="item.distype"/>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="">
                        <el-button type="primary"  size="mini" @click="changeyuan(item,indexa,index)" :disabled="item.distype">开始计算</el-button>
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <h4 class="form-header h4">计算结果</h4>
                  <el-row>
                    <el-col :span="12">
                      <el-form-item label="毛重(kg)" prop="weight">
                        <el-input v-model="item.weight" :disabled="item.distype" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="净重(kg)" prop="weight">
                        <el-input v-model="item.netWeight" :disabled="item.distype"></el-input>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="12">
                      <el-form-item label="单价" prop="price">
                        <el-input v-model="item.price" :disabled="item.distype"/>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="小计" prop="totalprice">
                        <el-input v-model="item.totalPrice" :disabled="item.distype"/>
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form>

                <div slot="footer" class="dialog-footer">
                   <div style="text-align: right">
                     <el-button type="primary" size="mini" :disabled="item.disabled" @click="editState(indexa,index)">修改设置</el-button>
                     <el-button type="primary"  size="mini" :disabled="item.distype" @click="submitForm(item,indexa,index)">保存设置</el-button>
                   </div>
                </div>
              </div>
              <div v-if="item.tabName=='板材计算'">
                <el-form ref="form3" :model="form" label-width="80px">
                  <el-row>
                    <el-col :span="12">
                      <el-form-item label="材料密度" prop="density">
                        <el-select clearable size="small" placeholder="请选择材料密度" v-model="item.density" :disabled="item.distype">
                          <el-option
                            label="灰铸铁(≤HT200)#7.2"
                            value="7.2">
                          </el-option>
                          <el-option
                            label="灰铸铁(≥HT200)#7.35"
                            value="7.35">
                          </el-option>
                          <el-option
                            label="白口铸铁#7.4~7.7"
                            value="7.4">
                          </el-option>
                          <el-option
                            label="可锻铸铁#7.2~7.4"
                            value="7.3">
                          </el-option>
                          <el-option
                            label="工业纯铁#7.87"
                            value="7.87">
                          </el-option>
                          <el-option
                            label="铸钢#7.8"
                            value="7.8">
                          </el-option>
                          <el-option
                            label="钢材#7.85"
                            value="7.85">
                          </el-option>
                          <el-option
                            label="低碳钢(含碳0.1%)#7.85"
                            value="7.85">
                          </el-option>
                          <el-option
                            label="中碳钢(含碳0.4%)#7.82"
                            value="7.82">
                          </el-option>
                          <el-option
                            label="高碳钢(含碳1%)#7.81"
                            value="7.81">
                          </el-option>
                        </el-select>

                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="厚度(mm)" prop="thk">
                        <el-input v-model="item.thk" placeholder="请输入壁厚度 mm" :disabled="item.distype"/>
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row>
                    <el-col :span="12">
                      <el-form-item label="长度(mm)" prop="length">
                        <el-input v-model="item.length" placeholder="请输入长度 m" :disabled="item.distype"/>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="宽度(mm)" prop="width">
                        <el-input v-model="item.width" placeholder="请输入宽度 m" :disabled="item.distype"/>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="12">
                      <el-form-item label="">
                        <el-button type="primary"  size="mini" @click="changeban(item,indexa,index)" :disabled="item.distype">开始计算</el-button>
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <h4 class="form-header h4">计算结果</h4>
                  <el-row>
                    <el-col :span="12">
                      <el-form-item label="毛重" prop="weight">
                        <el-input v-model="item.weight" placeholder="请输入毛重 千克" :disabled="item.distype"/>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="净重" prop="weight">
                        <el-input v-model="item.netWeight" placeholder="请输入净重 千克" :disabled="item.distype"/>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="12">
                      <el-form-item label="单价" prop="price">
                        <el-input v-model="item.price" placeholder="请输入单价 元/吨" :disabled="item.distype"/>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="小计" prop="totalprice">
                        <el-input v-model="item.totalPrice" placeholder="" :disabled="item.distype"/>
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form>

                <div slot="footer" class="dialog-footer">
                  <div style="text-align: right">
                    <el-button type="primary" size="mini" :disabled="item.disabled" @click="editState(indexa,index)">修改设置</el-button>
                    <el-button type="primary"  size="mini" :disabled="item.distype" @click="submitForm(item,indexa,index)">保存设置</el-button>
                  </div>
                </div>
              </div>
              <div v-if="item.tabName=='方管计算'">
                <el-form ref="form4" :model="form" label-width="80px">
                  <el-row>
                    <el-col :span="12">
                      <el-form-item label="材料密度" prop="density">
                        <el-select clearable size="small" placeholder="请选择材料密度" v-model="item.density" :disabled="item.distype">
                          <el-option
                            label="灰铸铁(≤HT200)#7.2"
                            value="7.2">
                          </el-option>
                          <el-option
                            label="灰铸铁(≥HT200)#7.35"
                            value="7.35">
                          </el-option>
                          <el-option
                            label="白口铸铁#7.4~7.7"
                            value="7.4">
                          </el-option>
                          <el-option
                            label="可锻铸铁#7.2~7.4"
                            value="7.3">
                          </el-option>
                          <el-option
                            label="工业纯铁#7.87"
                            value="7.87">
                          </el-option>
                          <el-option
                            label="铸钢#7.8"
                            value="7.8">
                          </el-option>
                          <el-option
                            label="钢材#7.85"
                            value="7.85">
                          </el-option>
                          <el-option
                            label="低碳钢(含碳0.1%)#7.85"
                            value="7.85">
                          </el-option>
                          <el-option
                            label="中碳钢(含碳0.4%)#7.82"
                            value="7.82">
                          </el-option>
                          <el-option
                            label="高碳钢(含碳1%)#7.81"
                            value="7.81">
                          </el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="规格" prop="density">
                        <el-select clearable size="small" placeholder="请选择材料规格" v-model="item.size" :disabled="item.distype">
                          <el-option
                            label="100*100*2.6"
                            value="100*100*2.6">
                          </el-option>
                          <el-option
                            label="100*100*3.2"
                            value="100*100*3.2">
                          </el-option>
                          <el-option
                            label="100*100*4"
                            value="100*100*4">
                          </el-option>
                          <el-option
                            label="100*100*5"
                            value="100*100*5">
                          </el-option>
                          <el-option
                            label="100*100*6"
                            value="100*100*6">
                          </el-option>
                          <el-option
                            label="100*100*8"
                            value="100*100*8">
                          </el-option>
                          <el-option
                            label="100*50*3"
                            value="100*50*3">
                          </el-option>
                          <el-option
                            label="100*50*4"
                            value="100*50*4">
                          </el-option>
                          <el-option
                            label="100*50*5"
                            value="100*50*5">
                          </el-option>
                          <el-option
                            label="100*60*2"
                            value="100*60*2">
                          </el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row>
                    <el-col :span="12">
                      <el-form-item label="长边宽(mm)" prop="diameter">
                        <el-input v-model="item.lengthWidth" placeholder="请输入长边宽" :disabled="item.distype"/>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="短边宽(mm)" prop="diameter">
                        <el-input v-model="item.shortWidth" placeholder="请输入长边宽" :disabled="item.distype"/>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="12">
                      <el-form-item label="壁厚(mm)" prop="tube">
                        <el-input v-model="item.tube" placeholder="请输入壁厚" :disabled="item.distype"/>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="长度(mm)" prop="length">
                        <el-input v-model="item.length" placeholder="请输入长度  m" :disabled="item.distype"/>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="12">
                      <el-form-item label="管材根数" prop="pipenum">
                        <el-input v-model="item.pipenum" placeholder="请输入管材根数" :disabled="item.distype"/>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="">
                        <el-button type="primary" size="mini" @click="changefang(item,indexa,index)" :disabled="item.distype">开始计算</el-button>
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <h4 class="form-header h4">计算结果</h4>
                  <el-row>
                    <el-col :span="12">
                      <el-form-item label="毛重" prop="weight">
                        <el-input v-model="item.weight" placeholder="请输入重量 千克" :disabled="item.distype"/>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="净重" prop="weight">
                        <el-input v-model="item.netWeight" placeholder="请输入重量 千克" :disabled="item.distype"/>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="12">
                      <el-form-item label="单价" prop="price">
                        <el-input v-model="item.price" placeholder="请输入单价 元/吨" :disabled="item.distype"/>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="小计" prop="totalprice">
                        <el-input v-model="item.totalPrice" placeholder="" :disabled="item.distype"/>
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form>

                <div slot="footer" class="dialog-footer">
                  <div style="text-align: right">
                    <el-button type="primary" size="mini" :disabled="item.disabled" @click="editState(indexa,index)">修改设置</el-button>
                    <el-button type="primary"  size="mini" :disabled="item.distype" @click="submitForm(item,indexa,index)">保存设置</el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
          </template>
        </div>
        <el-tabs v-model="countTabsValue" type="card" >
          <el-tab-pane
            v-for="(item, index) in countTabs"
            :key="item.id"
            :name="item.title"
            :label="item.title"
          >
          </el-tab-pane>
        </el-tabs>
        <!--<guancai ref="Refguancai" @form="jieshou" :quoteId="16"  v-if="countTabsValue=='圆管计算'"></guancai>-->
        <guancai ref="Refguancai" @form="jieshou" v-if="countTabsValue=='圆管计算'"></guancai>
        <yuangang ref="Refyuangang" @form="jieshou" v-if="countTabsValue=='圆钢计算'"></yuangang>
        <bancai ref="Refbancai" @form="jieshou" v-if="countTabsValue=='板材计算'"></bancai>
        <fangguan ref="Reffangguan" @form="jieshou" v-if="countTabsValue=='方管计算'"></fangguan>
        <!--<gunsi @form="jieshou" :quoteId="item.quoteId" :templateId="item.id" v-if="countTabsValue=='滚丝'"></gunsi>-->
        <!--<jisuan></jisuan>-->
      </div>

      <!-- 右侧 -->
      <div class="right">
        <div  :class="isSelected(index) ? 'ritemsel' : 'ritem'" v-for="(item, index) in items" :key="index" @click="showDiv(item.stage,index)">
          <div class="ritem1">{{item.state=='0'?'去设置':'修改'}}</div>
          <div class="ritem2">{{item.name}}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import {guancaijisuan,getstagetemplate,getalltemplate,getbaojiatabs,getproductbyid,savequoteinfo,getquoteInfos,updatequoteinfo} from "@/api/houtai/gongyu/xuqiu";
  import guancai from "@/views/houtai/jisuan/guancai";
  import yuangang from "@/views/houtai/jisuan/yuangang";
  import bancai from "@/views/houtai/jisuan/bancai";
  import fangguan from "@/views/houtai/jisuan/fangguan";
  import gunsi from "@/views/houtai/jisuan/gunsi";
  import jisuan from "@/views/houtai/gongyu/xuqiu/jisuan";
  export default {
    name: "baojia",
    components: {
      guancai,
      yuangang,
      bancai,
      fangguan,
      jisuan,
      gunsi,
    },
    data() {
      return {
        tableData: {},
        items:[],
        selectedItemIndex: 0,
        showhelp:false,
        editableTabsValue: '测试报价0001',
        editableTabs: [],
        countTabsValue: '管材计算',
        countTabs:[],
        productId:'',
        form: {},
        parm:'',
        quoteInfos:[],
        quoteStage:"",
        formUpdate: {
                  id:'',
                  name:'',
                  productId:'',
                  tabname:'',
                  density:'',
                  radio:'',
                  diameter:'',
                  tube:'',
                  length:'',
                  pipenum:'',
                  picture1:'C:\\Users\\<USER>\\Desktop\\timg.jpeg',
                  weight:'',
                  netweight:'',
                  price:'',
                  totalprice:'',
                  quoteinfoid:'',
                  quotetemplateid:'',
                  stage:''
        }
      };
    },
    watch: {

    },
    created() {
      this.ready();
    },
    mounted() {
    },
    methods: {
      jieshou(e){
        this.form = e;
        this.form['name'] = this.editableTabsValue;
        this.form['productId'] = this.productId;
        this.form['stage'] = this.quoteStage;
        this.form['tabname'] = this.countTabsValue;
        savequoteinfo(this.form).then((res) => {
          this.$message({
            message: '保存成功',
            type: 'success'
          });
          getalltemplate(this.queryParams).then((res) => {
            this.items = res.data;
            this.parm = res.data[0].stage;
            this.getQuoteInfos(res.data[0].stage);
            this.getTemplateByStage(res.data[0].stage);
          });
        });
      },
      showDiv(stage,index) {
        this.selectedItemIndex = index;
        this.parm = stage;
        getalltemplate(this.queryParams).then((res) => {
          this.items = res.data;
        });
        this.getQuoteInfos(stage);
        this.getTemplateByStage(stage);
        //this.$refs.Refguancai.showmodel(stage);
      },
      isSelected(index) {
        return this.selectedItemIndex === index;
      },
      ready() {
        getbaojiatabs(this.queryParams).then((res) => {
          this.editableTabs = res.rows;
          this.productId=res.rows[0].productId;
          this.getProductById('34')

        });
        getalltemplate(this.queryParams).then((res) => {
          this.items = res.data;
          this.parm = res.data[0].stage;
          this.getQuoteInfos(res.data[0].stage);
          this.getTemplateByStage(res.data[0].stage);
        });
      },
      getProductById(productId) {
        getproductbyid(productId).then((res) => {
          this.tableData = res.data;
          this.productId = res.data.id;
        });
      },
    getTemplateByStage(stage){
        getstagetemplate(stage).then((res) => {
           this.countTabsValue = res.data[0].title
           this.countTabs = res.data;
           this.quoteStage = res.data[0].stage;
        });
    },
    getQuoteInfos(stage){
        getquoteInfos(stage).then((res) => {
         let lista  =res.data;
          lista.forEach((item,index)=>{
              item['item'].forEach((itema,indexa)=>{
                lista[index]['item'][indexa]['distype'] = true;
                lista[index]['item'][indexa]['show'] = false;
                lista[index]['item'][indexa]['disabled'] = false;
              })
          })
          this.quoteInfos = lista
        });
    },
    submitForm(item,xiabiao1,xiabiao2){
      this.formUpdate.density=item.density,
      this.formUpdate.radio=item.radio,
      this.formUpdate.diameter=item.diameter,
      this.formUpdate.tube=item.tube,
      this.formUpdate.length=item.length,
      this.formUpdate.pipenum=item.pipeNum,
      this.formUpdate.picture1='C:\\Users\\<USER>\\Desktop\\timg.jpeg',
      this.formUpdate.weight=item.weight,
      this.formUpdate.netweight=item.netWeight,
      this.formUpdate.price=item.price,
      this.formUpdate.totalprice=item.totalPrice,
      this.formUpdate.stage=item.stage,

      this.formUpdate.id=item.id,
      this.formUpdate.name=item.name,
      this.formUpdate.productId=item.productId,
      this.formUpdate.tabname=item.tabName,
      updatequoteinfo(this.formUpdate).then((res) => {
        this.$message({
          message: '保存成功',
          type: 'success'
        });
        let quoteInfos =  this.quoteInfos;
        quoteInfos.forEach((item,index)=>{
          if(index == xiabiao1) {
            item['item'].forEach((itema,indexa)=>{
              if(indexa  == xiabiao2) {
                quoteInfos[index]['item'][indexa]['distype'] = true;
                quoteInfos[index]['item'][indexa]['disabled'] = false;
              }
            })
          }
        })
      });
    },
    editState(xiabiao1,xiabiao2){
      let quoteInfos =  this.quoteInfos;
      quoteInfos.forEach((item,index)=>{
        if(index == xiabiao1) {
            item['item'].forEach((itema,indexa)=>{
              if(indexa  == xiabiao2) {
                quoteInfos[index]['item'][indexa]['distype'] = false;
                quoteInfos[index]['item'][indexa]['disabled'] = true;
              }
            })
        }
      })
    },
    change(item,xiabiao1,xiabiao2) {
      var tabname = item.tabName;
      var diameter = item.diameter;
      var tube = item.tube;
      var length = item.length;
      if(diameter==''){
        this.$message({
          message: '外直径不能为空！',
          type: 'warning'
        });
        return;
      }else if(tube==''){
        this.$message({
          message: '壁厚不能为空！',
          type: 'warning'
        });
        return;
      }else if(length==''){
        this.$message({
          message: '长度不能为空！',
          type: 'warning'
        });
        return;
      }
      var requestParm = {"tabname":tabname,"diameter":diameter,"tube":tube,"length":length};
      guancaijisuan(requestParm).then((res) => {
        let quoteInfos =  this.quoteInfos;
        quoteInfos.forEach((item,index)=>{
          if(index == xiabiao1) {
            item['item'].forEach((itema,indexa)=>{
              if(indexa  == xiabiao2) {
                quoteInfos[index]['item'][indexa]['weight'] = res.data;
              }
            })
          }
        })
        this.$forceUpdate();
      });
    },
    changeyuan(item,xiabiao1,xiabiao2){
      var tabname = item.tabName;
      var diameter = item.diameter;
      var length = item.length;
      if(diameter==''){
        this.$message({
          message: '外直径不能为空！',
          type: 'warning'
        });
        return;
      }else if(length==''){
        this.$message({
          message: '长度不能为空！',
          type: 'warning'
        });
        return;
      }
      var requestParm = {"tabname":tabname,"diameter":diameter,"length":length};
      guancaijisuan(requestParm).then((res) => {
        let quoteInfos =  this.quoteInfos;
        quoteInfos.forEach((item,index)=>{
          if(index == xiabiao1) {
            item['item'].forEach((itema,indexa)=>{
              if(indexa  == xiabiao2) {
                quoteInfos[index]['item'][indexa]['weight'] = res.data;
              }
            })
          }
        })
        this.$forceUpdate();
      });
    },
    changeban(item,xiabiao1,xiabiao2){
      var tabname = item.tabName;
      var length = item.length;
      var width = item.width;
      var thk = item.thk;
      if(length==''){
        this.$message({
          message: '长度不能为空！',
          type: 'warning'
        });
        return;
      }else if(width==''){
        this.$message({
          message: '宽度不能为空！',
          type: 'warning'
        });
        return;
      }else if(thk==''){
        this.$message({
          message: '厚度不能为空！',
          type: 'warning'
        });
        return;
      }
      var requestParm = {"tabname":tabname,"length":length,"width":width,"thk":thk};
      guancaijisuan(requestParm).then((res) => {
        let quoteInfos =  this.quoteInfos;
        quoteInfos.forEach((item,index)=>{
          if(index == xiabiao1) {
            item['item'].forEach((itema,indexa)=>{
              if(indexa  == xiabiao2) {
                quoteInfos[index]['item'][indexa]['weight'] = res.data;
              }
            })
          }
        })
        this.$forceUpdate();
      });
    },
    changefang(item,xiabiao1,xiabiao2){
      var tabname = item.tabName;
      var lengthwidth = item.lengthWidth;
      var shortwidth = item.shortWidth;
      var length = item.length;
      var tube = item.tube;
      if(lengthwidth==''){
        this.$message({
          message: '长边宽不能为空！',
          type: 'warning'
        });
        return;
      }else if(shortwidth==''){
        this.$message({
          message: '短边宽不能为空！',
          type: 'warning'
        });
        return;
      }else if(length==''){
        this.$message({
          message: '长度不能为空！',
          type: 'warning'
        });
        return;
      }else if(tube==''){
        this.$message({
          message: '厚度不能为空！',
          type: 'warning'
        });
        return;
      }
      var requestParm = {"tabname":tabname,"lengthwidth":lengthwidth,"shortwidth":shortwidth,"length":length,"tube":tube};
      guancaijisuan(requestParm).then((res) => {
        let quoteInfos =  this.quoteInfos;
        quoteInfos.forEach((item,index)=>{
          if(index == xiabiao1) {
            item['item'].forEach((itema,indexa)=>{
              if(indexa  == xiabiao2) {
                quoteInfos[index]['item'][indexa]['weight'] = res.data;
              }
            })
          }
        })
        this.$forceUpdate();
      });
    },
    qha(xiabiao1,xiabiao2){
      let quoteInfos =  this.quoteInfos;
      quoteInfos.forEach((item,index)=>{
        if(index == xiabiao1) {
          item['item'].forEach((itema,indexa)=>{
            if(indexa  == xiabiao2) {
              quoteInfos[index]['item'][indexa]['show'] = !quoteInfos[index]['item'][indexa]['show'];
            }
          })
        }
      })
      this.$forceUpdate();
    }
   }
  };
</script>

<style rel="stylesheet/scss" lang="scss">
  body{
    background: #F9F9F9;
  }

  .tabs{
    width: 100%;
    height: 50px;
    background-color: #F1F3F8;
    display: flex;
    font-size: 14px;
  }
  .tabs-item-sel{
    width: 120px;
    height: 50px;
    background: #F9F9F9;
    text-align: center;
    line-height: 50px;
    cursor: pointer;
  }
  .tabs-item{
    width: 120px;
    height: 50px;
    text-align: center;
    line-height: 50px;
    cursor: pointer;
    color: #666666;
  }

  .pbox{
    width:97%;
    height: 124px;
    margin:20px auto;
    box-shadow: 0px 0px 26px 0px rgba(0,0,0,0.05);
    border-radius: 5px;
    background-color: white;
    overflow: hidden;
  }

  .pbox1{
    width: 100%;
    height: 42px;
    background: #F8F9FB;
    border: 1px solid #CBD6E2;
    border-top-right-radius: 5px;
    border-top-left-radius: 5px;
    display: flex;
    font-size: 12px;
    color: #999999;
    line-height: 42px;
  }

  .pbox2{
    width: 100%;
    height: 80px;
    display: flex;
    line-height: 80px;
    font-size: 12px;
    color: #999999;
  }

  .pbox2>div{
    margin-left: 50px;
  }



  .box{
    width: 97%;
    border:0px solid;
    margin:20px auto;
    display: flex;
    justify-content: space-between;
  }

  .left{
    width: 70%;
    min-height: 500px;
    border: 1px solid #39A8F9;
    background-color: white;
    border-radius: 5px;
    box-shadow: 0px 1px 0px 0px rgba(0,0,0,0.13);
  }

  .right{
    width: 28%;
    height: 100%;
  }

  .ritem{
    width: 100%;
    height: 70px;
    background: #F2F2F2;
    border: 1px solid transparent;
    border-radius: 5px;
    display: flex;
    justify-content: space-between;
    cursor: pointer;
    color: #333333;
  }

  .ritemsel{
    width: 100%;
    height: 70px;
    background: #E9F6FF;
    border: 1px solid #39A8F9;
    border-radius: 5px;
    display: flex;
    justify-content: space-between;
    cursor: pointer;
    color: #2E73F3;
  }

  .ritem1
  {
    margin-left: 25px;
    height: 100%;
    font-size: 18px;
    line-height: 70px;
  }
  .ritem2
  {
    font-weight: bold;
    margin-right: 20px;
    height: 100%;
    font-size: 18px;
    line-height: 70px;
  }



  .right>div+div{
    margin-top: 15px;
  }

  .tablebox{
    width: 100%;
    min-height: 720px;
    box-shadow: 0px 0px 26px 0px rgba(0,0,0,0.05);
    border: 1px solid #39A8F9;
    border-radius: 5px;
    overflow: hidden;
  }

  .table{
    width: 100%;
    border:0px solid;
    text-align: center;
  }

  .tr1{
    height: 57px;
    background: #F8F9FB;
    line-height: 57px;
    font-size: 12px;
    color: #999999;
  }

  .tr2{
    height: 57px;
    background: white;
    line-height: 57px;
    font-size: 12px;
    color: #333333;
  }

  .input{
    width: 128px;
    height: 36px;
    background: #F2F3F9;
    border: 1px solid #CBD6E2;
    text-align: center;
    outline: none;
    border-radius: 2px;
  }

  .help>img{
    vertical-align: middle;
    margin-left: 5px;
    margin-top: -3px;
    cursor: pointer;
  }

  .help{
    position: relative;
  }
  .helpinfo{
    width: 409px;
    height: 140px;
    background: #74849B;
    box-shadow: 0px 1px 12px 0px rgba(18,39,130,0.15);
    position: absolute;
    top: 42px;
    color: white;
    border-radius: 5px;
  }

  .jsbtn{
    width: 88px;
    height: 36px;
    background:#F2F3F9;
    border: 1px solid #CBD6E2;
    line-height: 36px;
    margin: auto;
    color: #999999;
    font-size: 14px;
    border-radius: 5px;
  }


  .okbtn{
    width: 220px;
    height: 38px;
    background:#39A8F9;
    color: white;
    text-align: center;
    line-height: 38px;
    margin-left: 1.5%;
    border-radius: 5px;
    cursor: pointer;
  }

  .tabbar{
    width: 100%;
    height: 46px;
    background: #F1F1F3;
    cursor: pointer;
    border-bottom: 1px solid lightgray;
    display: flex;
  }
  .tabbar>div{
    width: 125px;
    height: 46px;
    text-align: center;
    line-height: 46px;
    font-size: 14px;
    background: #FFFFFF;
    color: #333333;
    border-bottom: 1px solid lightgray;
  }

  .box1{
    width: 90%;
    height: 200px;
    background-color: white;
    margin:30px auto 0;
  }
  .box1-item{
    width: 100%;
    height: 46px;
    margin:5px auto 0;
    display: flex;
    line-height: 46px;
    font-size: 12px;
    color: #999999;

  }
  .box1-item>div:nth-child(1)
  {
    width: 50px;
  }
  .box1-item>div:nth-child(2)
  {
    margin-left: 50px;
    width: 500px;
  }

  .box2{
    width: 90%;
    height: 220px;
    margin:24px auto 0;
    border-radius: 5px;
    overflow:hidden;
    border: 1px solid #CBD6E2;
    position: relative;
  }
  .box2-1{
    width: 100%;
    height: 40px;
    background: #F8F9FB;
  }
  .box2-item{
    width: 100%;
    height: 40px;
    display: flex;
    line-height: 40px;
    font-size: 12px;
    color: #999999;

  }

  .box2-item2{
    width: 100%;
    height: 46px;
    margin:20px auto 0;
    display: flex;
    line-height: 46px;
    font-size: 12px;
    color: #999999;

  }

  .box2-item2>div:nth-child(1)
  {
    width: 50px;
    margin-left:20px;
  }
  .box2-item2>div:nth-child(2)
  {
    margin-left: 30px;
    width: 500px;
  }

  .box2-info{
    width: 300px;
    height: 100px;
    position: absolute;
    left: 650px;
    top: 60px;
    font-size: 12px;
    color: #9EAEC4;
  }

  .box2-info>div{
    margin-top: 8px;
  }

  .a1{
    height: 0px;
    margin-top: 0px;
    transition: 0.3s;
    background-color: lightgray;
    overflow: hidden;
  }

  .a2{
    height: 600px;
    margin-top: 0px;
    transition: 0.3s;
    background-color: #F1F3F8;
    overflow: hidden;
  }



  .headera{
    width: 100%;
    height: 51px;
    background: #F1F3F8;
    border: 1px solid rgba(0,0,0,0.13);
  }

  .headerbj{
    width: 120px;
    height: 52px;
    background: #fff;
    display: inline-block;
    text-align: center;
    line-height: 52px;
    font-size: 14px;
    cursor: pointer;
  }
  .headerbj2{
    width: 120px;
    height: 52px;
    font-size: 14px;
    font-weight: 400;
    color: #666666;
    display: inline-block;
    text-align: center;
    line-height: 52px;
    cursor: pointer;
  }
  .headerbj2:hover{
    background: #fff;
  }

  .headerbj3{
    width: 120px;
    height: 20px;
    font-size: 14px;
    font-weight: 400;
    color: #2E73F3;
    display: inline-block;
  }
  .cardbox{
    width: 100%;
    margin-top: 30px;
    border-radius: 20px;
    background: #aaa;

    box-shadow: 0px 0px 26px 0px rgba(0,0,0,0.05);
  }
  .cardHeader{
    height: 42px;
    background: #F8F9FB;
    border: 1px solid #CBD6E2;
  }
  .card1{
    display: inline-block;
    font-size: 12px;
    font-weight: 400;
    color: #999999;
    line-height: 42px;
    margin-left: 30px;
  }
  .cardHeader .card1:nth-child(2n){
    margin-left: 110px;
  }
  .cardBody{
    height: 82px;
    background: #fff;
  }
  .msg1{
    width: 60px;
    height: 20px;
    font-size: 12px;
    font-weight: 400;
    color: #999999;
    line-height: 82px;
    margin-left: 70px;
  }
  .msg2{
    width: 42px;
    height: 20px;
    font-size: 14px;
    font-weight: 500;
    color: #333333;
    line-height: 82px;
    margin-left: 5px;
  }
  .cardBody .msg1:nth-child(1){
    margin-left: 30px;
  }
  .msg4{
    font-size: 12px;
    font-weight: 400;
    color: #999999;
  }
  .msg5{
    font-size: 14px;
    font-weight: 500;
    color: #2E73F3;
    margin-left: 110px;
  }



  .asidelist1{
    display: flex;
    width: 315px;
    height: 70px;
    background: #F2F2F2;
    line-height: 70px;
    border-radius: 5px;
    margin-top: 15px;
    cursor: pointer;
    border: 1px solid #F2F2F2;
  }
  .asidelist11{
    flex:1;
    font-size: 14px;
    font-weight: 400;
    color: #999999;
    padding-left: 20px;
  }
  .asidelist12{
    flex:1;
    font-size: 18px;
    font-weight: 400;
    color: #333333;
    text-align: right;
    padding-right: 20px;
  }
  .asidelist2{
    background: #E9F6FF;
    border: 1px solid #39A8F9;
  }
  .asidelist21{
    color: #2E73F3 !important;
  }
  .asidelist1:hover{
    background: #E9F6FF;
    border: 1px solid #39A8F9;
  }
  .asidelist1:hover .asidelist11{
    color: #2E73F3 !important;
  }
  .asidelist1:hover .asidelist12{
    color: #2E73F3 !important;
  }

  .caradbox{
    background: #fff;
    margin-top: 20px;
    margin-right: 20px;
    padding-bottom: 20px;
  }
  .caradheader{
    height: 40px;
    font-size: 14px;
    font-weight: 400;
    color: #666666;
    line-height: 40px;
    padding-left: 20px;
    border-bottom: 1px solid #E7E9EC;
  }
  .caradbody{
    /*height: 60px;*/
    background: #F1F1F3;
    margin: 20px;
    border-radius: 5px;
    border: 1px solid #CBD6E2;
    line-height: 40px;
  }

  .carmsg1{
    margin-left: 30px;
    display: inline-block;
    width: 100px;
  }
  .carmsg2{
    font-size: 14px;
    font-weight: 500;
    color: #333333;
    line-height: 40px;
  }
  .carmsg3{
    font-size: 12px;
    font-weight: 400;
    color: #999999;
    /*margin-left: 14px;*/
  }
  .carmsg31{
    font-size: 12px;
    font-weight: 500;
    color: #666;
  }
  .carmsg32{
    font-size: 12px;
    font-weight: 400;
    color: #999999;
    margin-left: 0px;
  }

  .caradbody .carmsg1:nth-child(2n){
    margin-left: 40px;
  }

  .icon1{
    position: relative;
    text-align: right;
    padding-right: 20px;
    color: #CCCCCC;
    width: 140px;
    margin-left: auto;
  }

  .caradheaderb{
    background: #fff;
    padding-top: 20px;
    padding-left: 20px;
    padding-right: 30px;
    padding-bottom: 20px;
  }
</style>

