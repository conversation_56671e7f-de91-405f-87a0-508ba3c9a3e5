<template>
  <div>
    <button @click="generatePDF">生成PDF</button>
    <div class="word-content" contenteditable="true" v-html="extractedContent" ref="content"></div>
  </div>
</template>

<style>
.word-content {
  width: 60%;
  margin: auto;
}

.word-content table {
  border-collapse: collapse;
  width: 100%;
}

.word-content td,
.word-content th {
  border: 1px solid black;
  padding: 8px;
}
</style>

<script>
import mammoth from 'mammoth';
import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';

export default {
  data() {
    return {
      fileUrl: 'https://www.ziyouke.net/prod-api/profile/contract/SS1674958089163546624.docx',
      extractedContent: '',
      pageSize: { width: 595.28, height: 841.89 },                // A4纸张大小，单位为pt
      pageMargin: { top: 40, right: 40, bottom: 40, left: 40 },   // 页边距，单位为pt
    };
  },
  mounted() {
    this.loadWord();
  },
  methods: {
    generatePDF() {
      const content = this.$refs.content;

      const contentWidth = content.offsetWidth;
      const contentHeight = content.offsetHeight;

      const canvasOptions = {
        scale: 2, // 提高分辨率，避免模糊
        width: contentWidth,
        height: contentHeight,
        useCORS: true, // 跨域使用
        allowTaint: true,
      };

      const pdf = new jsPDF({
        orientation: 'portrait',
        unit: 'pt',
        format: 'a4',
      });

      let currentPosition = 0;
      const contentDataUrlList = [];

      const drawNextPage = () => {
        window.scrollTo(0, currentPosition);

        html2canvas(content, {
          ...canvasOptions,
          y: -currentPosition, // 设置偏移位置
        })
          .then(canvas => {
            contentDataUrlList.push(canvas.toDataURL('image/jpeg'));
            currentPosition += content.offsetHeight;

            if (currentPosition < content.scrollHeight) {
              // 绘制下一页
              drawNextPage();
            } else {
              // 生成PDF
              generateFinalPDF();
            }
          })
          .catch(error => {
            console.error('生成PDF出错:', error);
          });
      };

      const generateFinalPDF = () => {
        const { width, height } = this.pageSize;
        const { top, right, bottom, left } = this.pageMargin;

        contentDataUrlList.forEach((dataUrl, index) => {
          if (index > 0) {
            pdf.addPage();
          }

          const imgProps = pdf.getImageProperties(dataUrl);
          const pdfWidth = width - left - right;
          const pdfHeight = (imgProps.height * pdfWidth) / imgProps.width;
          const xOffset = left;
          const yOffset = top;

          pdf.addImage(dataUrl, 'JPEG', xOffset, yOffset, pdfWidth, pdfHeight);
        });

        pdf.save('generated.pdf');
      };

      drawNextPage();
    },
    loadWord() {
      if (!this.fileUrl) {
        return;
      }

      fetch(this.fileUrl)
        .then(response => response.blob())
        .then(blob => {
          const reader = new FileReader();

          reader.onload = (e) => {
            const arrayBuffer = e.target.result;

            const options = {
              arrayBuffer: arrayBuffer,
            };

            mammoth.extractRawText(options)
              .then((result) => {
                const rawText = result.value;
                return mammoth.convertToHtml(options);
              })
              .then((result) => {
                this.extractedContent = result.value;
              })
              .catch((error) => {
              });
          };

          reader.readAsArrayBuffer(blob);
        })
        .catch(error => {
        });
    },
  },
};
</script>
