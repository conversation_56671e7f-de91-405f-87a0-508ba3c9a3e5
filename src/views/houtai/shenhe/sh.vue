<template>
  <div class="bodya">
    <el-row>
      <el-col :span="4">
        <el-input v-model="queryParams.category" size="small" placeholder="请输入名称"></el-input>
      </el-col>
      <el-col :span="4">
        <el-select clearable size="small" style="width: 100%" v-model="queryParams.status" placeholder="请选择类型">
          <el-option
            v-for="item in audit_status"
            :key="item.dictValue"
            :label="item.dictLabel"
            :value="item.dictValue">
          </el-option>
        </el-select>
      </el-col>
      <el-col :span="8">
        <el-button size="small" @click="ready()" style="margin-left: 10px;" type="primary">
          搜索
        </el-button>
        <el-button size="small" @click="cz()" style="margin-left: 10px;" type="">
          重置
        </el-button>
      </el-col>
    </el-row>


    <el-table :data="tableData" border size="small" style="width: 100%;margin-top: 10px;">
      <el-table-column
        align="center"
        label="#"
        type="index">
      </el-table-column>
<!--      newCategory == true ? '新的类目-->
      <el-table-column prop="name" label="类目名称" align="center" show-overflow-tooltip></el-table-column>
      <el-table-column prop="industry" label="行业分类" align="center" show-overflow-tooltip></el-table-column>
      <el-table-column prop="draw" label="图纸" align="center" show-overflow-tooltip></el-table-column>
      <el-table-column prop="materialQuality" label="材质" align="center" show-overflow-tooltip></el-table-column>
      <el-table-column prop="model" label="型号" align="center" show-overflow-tooltip></el-table-column>
      <el-table-column prop="name" label="类目名称" align="center" show-overflow-tooltip></el-table-column>
      <el-table-column prop="newCategory" label="新增类目" align="center" show-overflow-tooltip>
        <template slot-scope="scope">
          {{scope.row.newCategory  ? '是' : '否'}}
        </template>
      </el-table-column>
      <el-table-column prop="photoAddr" label="类目图片" align="center" show-overflow-tooltip></el-table-column>
      <el-table-column prop="picture1" label="图片" align="center" show-overflow-tooltip></el-table-column>
      <el-table-column prop="productName" label="产品名称" align="center" show-overflow-tooltip></el-table-column>
      <el-table-column prop="report" label="检测报告" align="center" show-overflow-tooltip></el-table-column>
      <el-table-column prop="specs" label="规格" align="center" show-overflow-tooltip></el-table-column>
      <el-table-column prop="surface" label="表面" align="center" show-overflow-tooltip></el-table-column>
      <el-table-column prop="technology" label="工艺视频" align="center" show-overflow-tooltip></el-table-column>


<!--      <el-table-column prop="reason" label="驳回原因" align="center" show-overflow-tooltip></el-table-column>-->
<!--      <el-table-column prop="parentName" label="上级类目" align="center" show-overflow-tooltip></el-table-column>-->

      <el-table-column prop="createTime" label="创建时间" align="center" show-overflow-tooltip></el-table-column>


      <el-table-column fixed="right" align="center" label="操作" width="200">
        <template slot-scope="scope">
          <el-button type="text" @click="jujue(scope.row)" size="small">
            通过
          </el-button>
            <el-button type="text" @click="tongguo(scope.row)" size="small">
              驳回
            </el-button>

        </template>
      </el-table-column>
    </el-table>


    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="ready"/>



    <el-dialog
      title="审核操作"
      :visible.sync="showda"
      width="50%">


      <el-form ref="form" :model="form"  label-width="80px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="产品类目" prop="categoryId">
              <treeselect
                v-model="form.categoryId"
                :options="tableDatab"
                :normalizer="normalizer"
                placeholder="请选择上级品类"
              >
              <div slot="option-label" slot-scope="{ node }" class="category-flex">
                <span>{{ node.label }}</span>
                <span class="category-flex-desc">{{ node.raw.model ? `规格型号：${node.raw.model}` : '' }}</span>
              </div>
            </treeselect>
              <!--            <el-input v-model="form.productName" placeholder="请输入产品名称" />-->
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="产品编码" prop="productCode">
              <el-input v-model="form.productCode" placeholder="请输入产品编码"/>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>



      <span slot="footer" class="dialog-footer">
    <el-button @click="showda = false">取 消</el-button>
    <el-button type="primary" @click="tongga()">通过审核</el-button>
  </span>
    </el-dialog>





  </div>
</template>

<script>
import {getlist, sh, add} from "@/api/houtai/sh";
import {getlistb} from "@/api/purchase/category";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
export default {
  name: 'index',
  components: {
    Treeselect
  },
  data() {
    return {
      tableData: [],
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        category: '',
        status: '',
      },
      audit_status: [],
      listClass: '',
      form: {
      },
      tableDatab: [],
      showda: false,
      onedataa: [],

    };
  },
  created() {
    this.getDicts("audit_status").then((response) => {
      this.audit_status = response.data;
    });
    this.ready();
    this.getready();
  },
  methods: {
    normalizer(node) {
      if (node.label === 'unknown') {
        return '' // 返回空字符串或其他你想要显示的文本
      }
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.name,
        model: node.model,
        children: node.children,
      };
    },
    getready() {
      getlistb({
        pageNum: 1,
        pageSize: 999,
      }).then((res) => {
        let lista = res.data;
        this.tableDatab = lista;
      });
    },
    ready() {
      getlist(this.queryParams).then((res) => {
        let lista = res.data;
        lista.forEach((item, index) => {
          let bb = this.shaixuanb(this.audit_status, item.status, 'dictValue', 'dictLabel');
          lista[index]['statusa'] = bb;
        })

        this.tableData = lista;
        this.total = res.total;
      });
    },
    cz() {
      this.queryParams.category = '';
      this.queryParams.status = '';
      this.ready();
    },
    shaixuan(data, value, name, retname, indexa) {
      for (let i = 0; i < data.length; i++) {
        let item = data[i];
        if (item[name] == value) {
          return item[retname];
        }
      }
    },
    shaixuanb(data, value, name, retname, indexa) {
      for (let i = 0; i < data.length; i++) {
        let item = data[i];
        if (item[name] == value) {
          return item['listClass'];
        }
      }
    },

    tongguo(row) {
      this.$confirm('是否驳回审核?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        //  通过
        let datalist = {
          id: row.id,
          status: 2,
        }
        sh(datalist).then((res) => {
          this.$message({
            type: 'success',
            message: '操作成功!'
          });
          this.ready();
        });
      }).catch(() => {
      });
    },
    tongga(){
      //     //驳回
          let datalist =  this.onedataa;

          if(!this.form.productCode){
            this.$message({
              type: 'error',
              message: '请输入编码!'
            });
            return;
          }
      if(!this.form.categoryId){
        this.$message({
          type: 'error',
          message: '请选择类目!'
        });
        return;
      }

      datalist['status'] =  1;
      datalist['productCode'] = this.form.productCode;
      datalist['categoryId'] = this.form.categoryId;

          sh(datalist).then((res) => {
            this.$message({
              type: 'success',
              message: '操作成功!'
            });
            this.ready();
            this.showda = false;
          });

    },
    jujue(row) {
      this.showda = true;
      this.onedataa = row;
      //
      //
      // this.$confirm('是否通过审核?', '提示', {
      //   confirmButtonText: '确定',
      //   cancelButtonText: '取消',
      //   type: 'warning'
      // }).then(() => {
      //   this.$prompt('请输入产品编码', '提示', {
      //     confirmButtonText: '确定',
      //     cancelButtonText: '取消',
      //   }).then(({value}) => {
      //     if (!value) {
      //       this.$message({
      //         type: 'info',
      //         message: '请输入产品编码'
      //       });
      //       return;
      //     }

      //   }).catch(() => {
      //   });
      // }).catch(() => {
      // });
    }


  }
}
</script>

<style scoped>
.bodya {
  padding: 20px;
}

.lab1 {
  text-align: right;
  height: 30px;
  line-height: 30px;
  padding-right: 10px;
}
</style>
<style lang="scss" scoped>
::v-deep {
  .category-flex {
    display: flex;
    align-items: center;
    &-desc {
      font-size: 12px;
      color: #999999;
      margin-left: 10px;
    }
  }
}
</style>
