<template>
  <div class="newBox bgcf9 vh-85">
    <!-- 搜索 -->
    <div class="custom-search flex" v-if="showSearch">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" @submit.native.prevent>
        <el-form-item label="关键词" prop="keyword">
          <el-input v-model="queryParams.keyword" placeholder="请输入关键词" clearable @keyup.enter.native="handleQuery" size="small" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="tableBox">
      <!-- 收藏管理 -->
      <collect-tpl ref="collect" :hasCollect="false" :isSearch="true" :search="true" :showSearch.sync="showSearch" @queryTable="getList" :columns="columns" class="collectTpl" type="UserPriProduct" />

      <template v-if="list.length">
        <el-table v-loading="loading" ref="table" stripe :data="list" row-key="id" :key="key" style="width: 100%" class="custom-table custom-table-cell5">
          <el-table-column align="center" type="index" label="序号" v-if="columns[0].visible"></el-table-column>
          <el-table-column align="center" prop="productName" label="产品名称" show-overflow-tooltip v-if="columns[1].visible">
            <template slot-scope="{ row }">
              <span class="table-link pointer" @click="handleDetail(row)">{{ row.product.productName }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="productCode" label="产品编码" show-overflow-tooltip v-if="columns[2].visible">
            <template slot-scope="{ row }">{{ row.product.productCode }}</template>
          </el-table-column>
          <el-table-column align="center" prop="picture1" label="图片" width="75" v-if="columns[3].visible">
            <template slot-scope="{ row }">
              <el-image :src="formatProductImg(row.product)" fit="cover" @click="handleImg(row)">
                <div slot="error" class="image-slot">
                  <i class="el-icon-picture-outline"></i>
                </div>
              </el-image>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="specs" label="规格" show-overflow-tooltip v-if="columns[4].visible">
            <template slot-scope="{ row }">{{ row.product.specs }}</template>
          </el-table-column>
          <el-table-column align="center" prop="model" label="型号" show-overflow-tooltip v-if="columns[5].visible">
            <template slot-scope="{ row }">{{ row.product.model }}</template>
          </el-table-column>
          <el-table-column align="center" prop="unit" label="单位" show-overflow-tooltip v-if="columns[6].visible">
            <template slot-scope="{ row }">{{ row.product.unit }}</template>
          </el-table-column>
          <el-table-column align="center" prop="weight" label="重量" show-overflow-tooltip v-if="columns[7].visible">
            <template slot-scope="{ row }">{{ row.product.weight }}</template>
          </el-table-column>
          <el-table-column align="center" prop="industry" label="行业分类" show-overflow-tooltip v-if="columns[8].visible">
            <template slot-scope="{ row }">{{ row.product.industry }}</template>
          </el-table-column>
          <el-table-column align="center" prop="materialQuality" label="材质" width="80" show-overflow-tooltip v-if="columns[9].visible">
            <template slot-scope="{ row }">{{ row.product.materialQuality }}</template>
          </el-table-column>
          <el-table-column align="center" prop="surface" label="表面" show-overflow-tooltip v-if="columns[10].visible">
            <template slot-scope="{ row }">{{ row.product.surface }}</template>
          </el-table-column>
          <el-table-column align="center" prop="companyName" label="创建人" show-overflow-tooltip v-if="columns[11].visible">
            <template slot-scope="{ row }">
              <span class="table-link" @click="handleViewSupplier(row)">{{ row.companyName }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="createTime" label="创建时间" show-overflow-tooltip v-if="columns[12].visible"></el-table-column>
          <el-table-column align="center" label="操作" width="110" v-if="checkPermi(['system:verify:product'])">
            <template slot-scope="{ row }">
              <el-button type="text" icon="el-icon-check" @click="handlePass(row)" size="small">通过</el-button>
              <el-button type="text" icon="el-icon-close" @click="handleReject(row)" size="small">驳回</el-button>
            </template>
          </el-table-column>
        </el-table>

        <div class="custom-pagination">
          <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
        </div>
      </template>
      <el-empty :image-size="200" v-else />
    </div>

    <!-- 产品详情 -->
    <product-dialog ref="productInfo"></product-dialog>

    <!-- 供应商详情 -->
    <supplier-dialog ref="supplier"></supplier-dialog>

    <el-dialog v-dialogDragBox title="审核" :visible.sync="open" width="500px" class="custom-dialog">
      <div class="formBox">
        <el-form ref="form" :model="form" :rules="rules" label-width="6em">
          <el-form-item label="采购商品牌" prop="brand" v-if="form.brand">
            <el-checkbox v-model="form.isChecked" :label="form.brand" border></el-checkbox>
          </el-form-item>
          <el-form-item label="产品类目" prop="categoryId">
            <treeselect v-model="form.categoryId" :options="categoryOptions" :normalizer="normalizer" :max-height="200" placeholder="请选择产品类目" class='search-treeselect'>
              <div slot="option-label" slot-scope="{ node }" class="category-flex">
                <span>{{ node.label }}</span>
                <span class="category-flex-desc">{{ node.raw.model ? `规格型号：${node.raw.model}` : '' }}</span>
              </div>
            </treeselect>
          </el-form-item>
          <!--          <el-form-item label="产品编码" prop="productCode">-->
          <!--            <el-input v-model="form.productCode" placeholder="请输入产品编码" />-->
          <!--          </el-form-item>-->
          <el-form-item label="审核原因" prop="reason">
            <el-input type="textarea" :autosize="{ minRows: 3, maxRows: 5 }" resize="none" v-model="form.reason" placeholder="请输入审核原因" />
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer">
        <button type="button" class="custom-dialog-btn" @click="open = false">取消</button>
        <button type="button" class="custom-dialog-btn primary" @click="handleSumit">通过审核</button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { recommendProductList, auitPrivateProduct } from '@/api/houtai/sh'
import ProductDialog from '@/views/public/product/dialog'
import CollectTpl from '@/views/components/collect'
import supplierDialog from '@/views/purchase/demandForMe/supplier'
import { getlistb } from '@/api/purchase/category'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import { checkPermi } from '@/utils/permission'

export default {
  name: 'Auditlist',
  components: { ProductDialog, CollectTpl, supplierDialog, Treeselect },
  data() {
    return {
      showSearch: true,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        keyword: undefined
      },
      key: 1,
      loading: true,
      list: [],
      total: 0,
      // 显隐列
      columns: [
        { key: 0, label: `序号`, visible: true },
        { key: 1, label: `产品名称`, visible: true },
        { key: 2, label: `产品编码`, visible: true },
        { key: 3, label: `图片`, visible: true },
        { key: 4, label: `规格`, visible: true },
        { key: 5, label: `型号`, visible: true },
        { key: 6, label: `单位`, visible: true },
        { key: 7, label: `重量`, visible: true },
        { key: 8, label: `行业分类`, visible: true },
        { key: 9, label: `材质`, visible: true },
        { key: 10, label: `表面`, visible: true },
        { key: 11, label: `创建人`, visible: true },
        { key: 12, label: `创建时间`, visible: true }
      ],
      // 审核
      open: false,
      form: {},
      rules: {
        categoryId: [{ required: true, message: '请选择产品类目', trigger: ['blur', 'change'] }],
        productCode: [{ required: true, message: '请输入产品编码', trigger: 'blur' }]
      },
      categoryOptions: []
    }
  },
  created() {
    this.getCategory()
    this.getList()
  },
  methods: {
    checkPermi,
    normalizer(node) {
      if (node.label === 'unknown') {
        return '' // 返回空字符串或其他你想要显示的文本
      }
      if (node.children && !node.children.length) {
        delete node.children
      }
      return {
        id: node.id,
        label: node.name,
        model: node.model,
        children: node.children
      }
    },
    // 分类
    getCategory() {
      const query = { pageNum: 1, pageSize: 999 }
      getlistb(query).then(res => {
        if (res.code === 200) {
          this.categoryOptions = res.data
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 列表
    getList() {
      this.loading = true
      recommendProductList(this.queryParams).then(res => {
        if (res.code === 200) {
          this.list = res.rows
          this.total = res.total
          this.key = Math.random()
          this.loading = false
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 重置搜索
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 产品详情
    handleDetail(item, type) {
      const data = item.product
      this.$refs.productInfo.handleView(data, type)
    },
    // 图片预览
    handleImg(row) {
      const data = { picture1: this.formatProductImg(row.product) }
      this.$refs.productInfo.handleImgView(data)
    },
    // 查看供应商详情
    handleViewSupplier(row) {
      const id = row.companyId
      this.$refs.supplier.getInfo(id, 'common')
    },
    reset() {
      this.form = {
        isChecked: false,
        brand: undefined,
        categoryId: undefined,
        companyName: undefined,
        id: undefined,
        productCode: undefined,
        reason: undefined,
        status: 1
      }
      this.resetForm('form')
    },
    // 通过
    handlePass(row) {
      this.reset()
      this.form.brand = row.brand
      this.form.companyName = row.companyName
      this.form.id = row.recommendId
      this.open = true
    },
    // 提交
    handleSumit() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          const data = { ...this.form }
          if (!data.isChecked) delete data.brand
          auitPrivateProduct(data).then(res => {
            if (res.code === 200) {
              this.$message.success('审核成功')
              this.getList()
              this.open = false
            } else {
              this.$message.error(res.msg)
            }
          })
        }
      })
    },
    // 驳回
    // prettier-ignore
    handleReject(row) {
      const data = { id: row.recommendId, status: 2 }
      this.$confirm('是否驳回审核?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        auitPrivateProduct(data).then(res => {
          if (res.code === 200) {
            this.$message.success('驳回成功')
            this.getList()
          } else {
            this.$message.error(res.msg)
          }
        })
      }).catch(() => {
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
::v-deep {
  .category-flex {
    display: flex;
    align-items: center;
    &-desc {
      font-size: 12px;
      color: #999999;
      margin-left: 10px;
    }
  }
}
.custom-search {
  ::v-deep {
    .el-form-item--small.el-form-item {
      margin-bottom: 0;
    }
  }
}
.tableBox {
  padding: 10px 20px 20px;
}
.collectTpl {
  margin-bottom: 0;
  ::v-deep {
    .collect {
      margin-bottom: 0;
      background-color: transparent;
      border-color: transparent;
    }
  }
}
.custom-dialog ::v-deep {
  .formBox {
    padding: 10px 20px;
  }
  .custom-dialog-btn {
    width: 150px;
  }
  .el-textarea {
    textarea {
      font-family: inherit;
    }
  }
}
.search-treeselect {
  ::v-deep {
    .treeselect-main {
      width: 300px;
    }
    .vue-treeselect__control {
      height: 40px;
    }
    .vue-treeselect__input-container {
      height: 40px !important;
    }
    .vue-treeselect,
    .vue-treeselect__placeholder,
    .vue-treeselect__single-value {
      line-height: 38px !important;
      height: 38px !important;
    }
    .category-flex {
      display: flex;
      flex-wrap: nowrap;
      align-items: center;
      span {
        display: block;
        float: left;
      }
      &-desc {
        font-size: 12px;
        color: #999999;
        margin-left: 10px;
      }
    }
  }
}
</style>
