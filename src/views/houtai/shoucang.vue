<template>
<div class="bodybox">
    <div v-if="typeshowa" class="title1" @click="showmodel(1)">收藏夹管理</div>
  <div v-if="typeshowa"  class="bodyboxa">
    <div class="listTitle1">
      收藏夹列表
    </div>
    <div @click="tabHome(-1)" class="listTitle2" :class="showIndex == -1 ? 'acta' : ''">
      默认
    </div>
    <template v-for="(item,index) in tableData">
    <div @click="tabHome(index)" class="listTitle2" :class="showIndex == index ? 'acta' : ''">
       {{item.dirName}}
    </div>
    </template>
    <!--  <el-table-->
<!--      :data="tableData"-->
<!--      style="width: 100%">-->
<!--      <el-table-column-->
<!--        prop="dirName"-->
<!--        label="收藏夹名称">-->
<!--        <template slot-scope="scope">-->
<!--          <div class="title2 " :class="showIndex == scope.$index ? 'acta' : ''">-->
<!--            {{scope.row.dirName}}-->
<!--          </div>-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--    </el-table>-->
  </div>




  <el-dialog
    title="收藏夹管理"
    :visible.sync="dialogVisible"
    width="30%">
    <el-button v-if="showtype == 1" type="text"  size="small"  @click="addshoucang()">
      <i class="el-icon-plus"></i>  新增收藏夹
    </el-button>
    <el-table
      :data="tableData"
      style="width: 100%">
      <el-table-column
        align="center"
        label="#"
        type="index">
      </el-table-column>
      <el-table-column
        prop="dirName"
        label="收藏夹名称">
      </el-table-column>
      <el-table-column
        prop="sort"
        label="排序">
        <template slot-scope="scope">
          <el-input
          size="small"
          type="number"
          placeholder="请输入排序序号"
          @blur="setsort(scope.row)"
          v-model="scope.row.sort">
        </el-input>
        </template>
      </el-table-column>
      <el-table-column
        fixed="right"
        label="操作"
        width="130">
        <template slot-scope="scope">
          <el-button v-if="showtype == 2" @click="xza(scope.row)" type="text" size="small">选择</el-button>
          <el-button v-if="showtype == 1" @click="handleClick(scope.row)" type="text" size="small">编辑</el-button>
          <el-button  v-if="showtype == 1" @click="handleDel(scope.row)" type="text" size="small">删除</el-button>
        </template>
      </el-table-column>
    </el-table>



    <span slot="footer" class="dialog-footer">
    <el-button @click="dialogVisible = false">取 消</el-button>
  </span>
  </el-dialog>




</div>
</template>

<script>
import {getlist,addlist, editlist,dellist} from "@/api/houtai/shoucang";
export default {
  name: 'shoucang',
  props:{
    shoucangType:{
      type: String,
      // 定义是否必须传
      required: true,
      // 定义默认值
      default: 'UserProduct'
    },
  },
  data() {
    return {
      typeshowa:true,
      dialogVisible: false,
      showIndex: -1,
      tableData: [],
      showtype: 1,
      showNum: 1,
    }
  },
  created() {
    this.ready();
  },
  methods:{
    setsort(row){
      editlist({
          storeId: row.storeId,
          dirName: row.dirName,
          sort:row.sort,
          type: this.shoucangType,
        }).then((res) => {
          this.$message({
            type: 'success',
            message: '排序成功'
          });
          this.ready();
        });
    },
    tabHome(e){
      this.showIndex = e;
      let data;
     if(e == -1) {
         data = {
         data:  {},
          type: 1,
        };
      } else {
        data = {
          data: this.tableData[e],
          type: 2,
        };
      }
      // let data = {
      //   data: this.tableData[e],
      //   type: 2,
      // };
      this.$emit('shuaxina', data);
    },
    ready(row){
      getlist({
        type: this.shoucangType,
      }).then((res) => {
        this.tableData  = res.data;
        if(this.showNum == 1) {
          this.showNum = 2;
          this.$emit('shuaxinc', this.tableData[0]);
        }
      });
    },
    showmodel(){
      let that =this;
      this.dialogVisible = true;
      this.showtype = 1;
    },
    showmodelb(){
      let that =this;
      this.dialogVisible = true;
      this.showtype = 2;
    },
    xza(row){

      this.dialogVisible = false;
      this.$emit('xza',row);

    },
    addshoucang(){
      let that =this;
      // addlist
      this.$prompt('请输入收藏夹名称', '新增收藏夹', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
      }).then(({ value }) => {
        if(!value) {
            return;
        }
        addlist({
          dirName: value,
          type: this.shoucangType,
        }).then((res) => {
          this.$message({
            type: 'success',
            message: '新增成功'
          });
          this.ready();
        });
      }).catch(() => {
      });
    },

    handleClick(row){
      let that =this;
      // addlist
      this.$prompt('请输入新的收藏夹名称', '修改收藏夹', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValue: row.dirName,
      }).then(({ value }) => {
        if(!value) {
          return;
        }
        editlist({
          storeId: row.storeId,
          dirName: value,
          type: this.shoucangType,
        }).then((res) => {
          this.$message({
            type: 'success',
            message: '修改成功'
          });
          this.ready();
        });
      }).catch(() => {
      });


    },
    handleDel(row){
      let that =this;

      this.$confirm('此操作将永久删除该收藏夹, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
      dellist({
       storeId: row.storeId,
        type: this.shoucangType,
      }).then((res) => {
        this.$message({
          type: 'success',
          message: '删除成功'
        });
        this.ready();
      });
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });

    }
  }
}
</script>

<style scoped>

.bodybox{
  width: 100%;
  /*padding-right: 20px;*/
  position: relative;
  top: -10px;
}
.bodyboxa{
  background: #F8F8F8;
  border: 1px solid #f1f1f1;
  margin-bottom: 20px;
}
.title1{
  color: #409EFF;
  text-align: left;
  font-size: 12px;
  margin-bottom: 10px;
  cursor: pointer;
  display: inline-block;

}
.title2{
  cursor: pointer;
}
.acta{
  background: #409EFF !important;
  color: #fff;
  border: 1px solid #409EFF !important;
  border-bottom: none !important;
}
.listTitle1{
  background: #F8F8F8;
  color: #515a6e;
  padding: 10px;
  font-size: 13px;
  font-weight: bold;
  display: none;
}
.listTitle2{
  border: 1px solid #f1f1f1;
  border-top: none;
  padding: 10px;
  font-size: 13px;
  cursor: pointer;
  display: inline-block;
}
.listTitle2:hover{
  background: #409EFF;
  color: #fff;
  border: 1px solid #409EFF;
  border-bottom: none;

}
</style>
