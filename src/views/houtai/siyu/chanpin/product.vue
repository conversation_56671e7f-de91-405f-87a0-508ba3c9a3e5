<template>
  <div>
    <el-dialog v-dialogDragBox :title="title" :visible.sync="open" width="900px" class="custom-dialog">
      <div class="formBox">
        <el-form ref="form" :model="form" :rules="rules" label-width="80px">
          <el-row :gutter="10" style="display: flex; flex-wrap: wrap">
            <el-col :span="12">
              <el-form-item label="产品名称" prop="productName">
                <el-input v-model="form.productName" placeholder="请输入产品名称" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="其他名称" prop="formerName">
                <el-input v-model="form.formerName" placeholder="请输入其他名称" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="产品编码" prop="productCode">
                <el-input v-model="form.productCode" placeholder="请输入产品编码" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="规格" prop="specs">
                <el-input v-model="form.specs" placeholder="请输入规格" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="型号" prop="model">
                <el-input v-model="form.model" placeholder="请输入型号" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="执行标准" prop="standard">
                <el-input v-model="form.standard" placeholder="请输入执行标准"></el-input>
              </el-form-item>
            </el-col>
<!--            <el-col :span="12">-->
<!--              <el-form-item label="属性" prop="attribute">-->
<!--                <el-input v-model="form.attribute" placeholder="请输入行业属性" />-->
<!--              </el-form-item>-->
<!--            </el-col>-->
            <el-col :span="12">
              <el-form-item label="材质" prop="materialQuality">
                <el-input v-model="form.materialQuality" placeholder="请输入材质" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="表面" prop="surface">
                <el-input v-model="form.surface" placeholder="请输入表面处理方式" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="单位" prop="unit">
                <el-select v-model="form.unit" placeholder="请选择" style="width: 100%">
                  <el-option v-for="(item, index) in unitOptions" :key="index" :label="item" :value="item"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="重量" prop="weight">
                <el-input v-model="form.weight" placeholder="请输入重量"><span slot="suffix">Kg</span></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="供应商">
                <treeselect multiple style="width: 100%" v-model="form.suppliersb" :options="supplierOptions" :normalizer="normalizer" placeholder="请选择上级品类" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="产品图片" prop="picture1">
            <image-upload v-model="form.picture1" />
          </el-form-item>
          <el-form-item label="三维图片" prop="diagram">
            <image-upload v-model="form.diagram" />
          </el-form-item>
          <el-form-item label="产品图纸" prop="draw">
            <file-upload v-model="form.draw" :file-type="fileType" />
          </el-form-item>
          <el-form-item label="检测报告" prop="jcbg">
            <file-upload v-model="form.jcbg" :file-type="fileType" />
          </el-form-item>
          <el-form-item label="工艺视频" prop="technology">
            <file-upload v-model="form.technology" :file-type="['mp4']" :file-size="200" />
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer">
        <button type="button" class="custom-dialog-btn" @click="handleCancel">取消</button>
        <button type="button" class="custom-dialog-btn primary" @click="handleSumit">确定</button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getPrivateduct, addPrivateduct, updatePrivateduct } from '@/api/system/privateduct'
import { getlist } from '@/api/houtai/siyu/gongying'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import { isNumber } from '@/utils/validate'

export default {
  components: { Treeselect },
  data() {
    return {
      // 新增/修改
      title: '',
      open: false,
      form: {},
      rules: {
        unit: [{ required: true, message: '请选择单位', trigger: 'change' }],
        productName: [{ required: true, message: '产品名称不能为空', trigger: 'blur' }],
        productCode: [{ required: true, message: '产品编码不能为空', trigger: 'blur' }],
        productType: [{ required: true, message: '产品类型不能为空', trigger: 'change' }],
        // attribute: [{ required: true, message: '属性不能为空', trigger: 'blur' }],
        specs: [{ required: true, message: '规格不能为空', trigger: 'blur' }],
        model: [{ required: true, message: '型号不能为空', trigger: 'blur' }],
        materialQuality: [{ required: true, message: '材质不能为空', trigger: 'blur' }],
        surface: [{ required: true, message: '表面不能为空', trigger: 'blur' }],
        // weight: [
        //   { required: true, message: '请输入产品重量', trigger: 'blur' },
        //   { validator: isNumber, message: '输入有误请重新输入', trigger: 'blur' }
        // ]
      },
      unitOptions: ['吨', '千克', '个', '件', '套', '米', '支', '根'],
      supplierOptions: [],
      fileType: ['pdf', 'png', 'jpg', 'jpeg']
    }
  },
  created() {
    this.getSupplier()
  },
  methods: {
    // 供应商列表
    getSupplier() {
      const data = { pageNum: 1, pageSize: 9999 }
      getlist(data).then(res => {
        this.supplierOptions = res.rows
      })
    },
    normalizer(node) {
      if (node.label === 'unknown') {
        return '' // 返回空字符串或其他你想要显示的文本
      }
      if (node.children && !node.children.length) {
        delete node.children
      }
      return {
        id: node.id,
        label: node.name,
        children: node.children
      }
    }, // 表单重置
    reset() {
      this.form = {
        id: undefined,
        productName: undefined,
        formerName: undefined,
        productType: undefined,
        specs: undefined,
        model: undefined,
        attribute: undefined,
        materialQuality: undefined,
        surface: undefined,
        unit: undefined,
        weight: '0',
        suppliersb: undefined,
        picture1: undefined,
        diagram: undefined,
        draw: undefined,
        jcbg: undefined,
        technology: undefined
      }
      this.resetForm('form')
    },
    // 新增
    handleAdd() {
      this.reset()
      this.title = '添加私域产品'
      this.open = true
    },
    // 修改
    handleUpdate(row) {
      this.reset()
      const id = row.id || this.ids
      getPrivateduct(id).then(response => {
        let lista = response.data
        let listb = []
        lista.suppliers.forEach((item, index) => {
          listb.push(item['supplierId'])
        })
        lista['suppliersb'] = listb
        lista['supplierIds'] = listb
        this.form = lista
        this.open = true
        this.title = '修改私域产品'
      })
    },
    // 取消
    handleCancel() {
      this.open = false
      this.reset()
    },
    // 提交
    handleSumit() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            let sendata = JSON.parse(JSON.stringify(this.form))
            sendata['supplierIds'] = sendata['suppliersb']
            updatePrivateduct(sendata).then(res => {
              if (res.code === 200) {
                this.$message.success('修改成功')
                this.open = false
                this.$parent.getList()
              } else {
                this.$message.error(res.msg)
              }
            })
          } else {
            let sendata = JSON.parse(JSON.stringify(this.form))
            sendata['supplierIds'] = sendata['suppliersb']
            addPrivateduct(sendata).then(res => {
              if (res.code === 200) {
                this.$message.success('新增成功')
                this.open = false
                this.$parent.getList()
              } else {
                this.$message.error(res.msg)
              }
            })
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
.custom-dialog {
  ::v-deep {
    .formBox {
      padding: 20px;
    }
    .custom-dialog-btn.disabled {
      opacity: 0.5;
    }
  }
}
</style>
