<template>
  <div class="newBox bgcf9 vh-85">
    <!-- 搜索 -->
    <div class="custom-search flex" v-if="showSearch">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" @submit.native.prevent>
        <el-form-item label="关键词" prop="keyword">
          <el-input v-model="queryParams.keyword" placeholder="请输入关键词" clearable @keyup.enter.native="handleQuery" size="small" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="tableBox">
      <div class="custom-tab">
        <div class="custom-tab-list">
          <div class="custom-tab-item" :class="{ active: !isSelf }" @click="handleChangeTab(false)">已审核</div>
          <div class="custom-tab-item" :class="{ active: isSelf }" @click="handleChangeTab(true)">未审核</div>
        </div>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
      </div>

      <template v-if="list.length">
        <el-table v-loading="loading" ref="table" stripe :data="list" row-key="id" :key="key" style="width: 100%" class="custom-table custom-table-cell5" @selection-change="handleSelectionChange">
          <el-table-column align="center" type="index" label="序号" v-if="columns[0].visible"></el-table-column>
          <el-table-column align="center" prop="productName" label="产品名称" show-overflow-tooltip v-if="columns[1].visible">
            <template slot-scope="{ row }">
              <span class="table-link pointer" @click="handleDetail(row)">{{ row.productName }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="productCode" label="产品编码" show-overflow-tooltip v-if="columns[2].visible"></el-table-column>
          <el-table-column align="center" prop="picture1" label="图片" width="75" v-if="columns[3].visible">
            <template slot-scope="{ row }">
              <el-image :src="formatProductImg(row)" fit="cover" @click="handleImg(row)">
                <div slot="error" class="image-slot">
                  <i class="el-icon-picture-outline"></i>
                </div>
              </el-image>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="specs" label="规格" show-overflow-tooltip v-if="columns[4].visible"></el-table-column>
          <el-table-column align="center" prop="model" label="型号" show-overflow-tooltip v-if="columns[5].visible"></el-table-column>
          <el-table-column align="center" prop="unit" label="单位" show-overflow-tooltip v-if="columns[6].visible"></el-table-column>
          <el-table-column align="center" prop="weight" label="重量" show-overflow-tooltip v-if="columns[7].visible"></el-table-column>
          <el-table-column align="center" prop="industry" label="行业分类" show-overflow-tooltip v-if="columns[8].visible"></el-table-column>
          <el-table-column align="center" prop="materialQuality" label="材质" show-overflow-tooltip v-if="columns[9].visible"></el-table-column>
          <el-table-column align="center" prop="surface" label="表面" show-overflow-tooltip v-if="columns[10].visible"></el-table-column>
          <el-table-column align="center" prop="status" label="状态" show-overflow-tooltip v-if="columns[11].visible">
            <template slot-scope="{ row }">
              <template>
                <el-switch v-model="row.status" active-text="上架" inactive-text="下架" :active-value="1" :inactive-value="0" disabled class="table-switch"></el-switch>
              </template>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="createBy" label="创建人" show-overflow-tooltip v-if="columns[12].visible"></el-table-column>
          <el-table-column align="center" prop="createTime" label="创建时间" show-overflow-tooltip v-if="columns[13].visible"></el-table-column>
          <el-table-column align="center" label="操作" v-if="isSelf">
            <template slot-scope="{ row }">
              <el-button type="danger" icon="el-icon-refresh-left" size="mini" @click="handleRevocation(row)">撤回</el-button>
            </template>
          </el-table-column>
        </el-table>

        <div class="custom-pagination">
          <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
        </div>
      </template>
      <el-empty :image-size="200" v-else />

      <!-- 详情 -->
      <product-dialog ref="productInfo"></product-dialog>
    </div>
  </div>
</template>

<script>
import { recommendList } from '@/api/system/privateduct'
import { recommendProductList, revocationPrivateProduct } from '@/api/houtai/sh'
import ProductDialog from '@/views/public/product/dialog'
import CollectTpl from '@/views/components/collect'

export default {
  name: 'Recommend',
  components: { ProductDialog, CollectTpl },
  data() {
    return {
      showSearch: true,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        keyword: undefined,
        self: undefined
      },
      statusOptions: [
        { label: '上架', value: 1 },
        { label: '下架', value: 0 }
      ],
      key: 1,
      loading: true,
      list: [],
      // 显隐列
      columns: [
        { key: 0, label: `序号`, visible: true },
        { key: 1, label: `产品名称`, visible: true },
        { key: 2, label: `产品编码`, visible: true },
        { key: 3, label: `图片`, visible: true },
        { key: 4, label: `规格`, visible: true },
        { key: 5, label: `型号`, visible: true },
        { key: 6, label: `单位`, visible: true },
        { key: 7, label: `重量`, visible: true },
        { key: 8, label: `行业分类`, visible: true },
        { key: 9, label: `材质`, visible: true },
        { key: 10, label: `表面`, visible: true },
        { key: 11, label: `状态`, visible: true },
        { key: 12, label: `创建人`, visible: true },
        { key: 13, label: `创建时间`, visible: true }
      ],
      isSelf: false
    }
  },
  created() {
    this.getList()
  },
  methods: {
    // 列表
    getList() {
      this.loading = true
      if (this.isSelf) {
        recommendProductList(this.queryParams).then(res => {
          if (res.code === 200) {
            let list = []
            res.rows.map(item => {
              list.push({ ...item.product, recommendId: item.recommendId })
            })
            this.list = list
            this.total = res.total
            this.loading = false
            this.key = Math.random()
          } else {
            this.$message.error(res.msg)
          }
        })
      } else {
        recommendList(this.queryParams).then(res => {
          if (res.code === 200) {
            this.list = res.rows
            this.total = res.total
            this.loading = false
            this.key = Math.random()
          } else {
            this.$message.error(res.msg)
          }
        })
      }
    },
    // 改变tab
    handleChangeTab(tab) {
      this.isSelf = tab
      this.queryParams.self = tab ? true : undefined
      this.key = Math.random()
      this.getList()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.multiple = !selection.length
    },
    // 关闭多选
    handleClose() {
      this.isCollect = false
      this.multiple = false
      this.ids = []
      this.$refs.table.clearSelection()
    },
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 重置搜索
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 详情
    handleDetail(item, type) {
      this.$refs.productInfo.handleView(item, type)
    },
    // 图片预览
    handleImg(row) {
      this.$refs.productInfo.handleImgView(row)
    },
    // 撤回
    // prettier-ignore
    handleRevocation(row) {
      const recommendId = row.recommendId
      this.$confirm('是否确认撤回此推荐?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
         revocationPrivateProduct({ recommendId }).then(res => {
          if (res.code === 200) {
            this.$message.success('撤回成功')
            this.getList()
          } else {
            this.$message.error(res.msg)
          }
        })
      }).catch(() => {})
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
.tableBox {
  padding: 20px;
}
.custom-search {
  ::v-deep {
    .el-form-item--small.el-form-item {
      margin-bottom: 0;
    }
  }
}
.custom-dialog {
  ::v-deep {
    .formBox {
      padding: 20px;
    }
  }
}
.collectTpl {
  margin-bottom: 0;
  ::v-deep {
    .collect {
      margin-bottom: 0;
      background-color: transparent;
      border-color: transparent;
    }
  }
}
.custom-tab {
  margin-bottom: 18px;
  background-color: #f5f7fa;
  border: 1px solid #dcdfe6;
  display: flex;
  justify-content: space-between;
  align-items: center;
  &-list {
    display: inline-flex;
    line-height: 50px;
  }
  &-item {
    transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
    color: #666666;
    padding: 0 20px;
    margin: -1px 0;
    cursor: pointer;
    box-sizing: border-box;
    list-style: none;
    &:hover,
    &.active {
      background-color: #409eff;
      color: #fff;
    }
  }
}
</style>
