<template>
  <div class="newBox bgcf9 vh-85">
    <!-- 搜索 -->
    <div class="custom-search flex" v-if="showSearch">
      <el-form :model="queryParams" ref="queryForm" size="small" label-width="5em" :inline="true" @submit.native.prevent>
        <el-row :gutter="10">
          <el-col :xs="8" :sm="6" :lg="4">
            <el-form-item label="产品名称" prop="productName">
              <el-input v-model="queryParams.productName" placeholder="请输入产品名称" clearable @keyup.enter.native="handleQuery" size="small" />
            </el-form-item>
          </el-col>
          <el-col :xs="8" :sm="6" :lg="4">
            <el-form-item label="产品编码" prop="productCode">
              <el-input v-model="queryParams.productCode" placeholder="请输入产品编码" clearable @keyup.enter.native="handleQuery" size="small" />
            </el-form-item>
          </el-col>
          <el-col :xs="8" :sm="6" :lg="4">
            <el-form-item label="产品规格" prop="specs">
              <el-input v-model="queryParams.specs" placeholder="请输入产品规格" clearable @keyup.enter.native="handleQuery" size="small" />
            </el-form-item>
          </el-col>
          <el-col :xs="8" :sm="6" :lg="4">
            <el-form-item label="产品型号" prop="model">
              <el-input v-model="queryParams.model" placeholder="请输入产品型号" clearable @keyup.enter.native="handleQuery" size="small" />
            </el-form-item>
          </el-col>
          <el-col :xs="8" :sm="6" :lg="4">
            <el-form-item label="表面" prop="surface">
              <el-input v-model="queryParams.surface" placeholder="请输入表面" clearable @keyup.enter.native="handleQuery" size="small" />
            </el-form-item>
          </el-col>
          <el-col :xs="8" :sm="6" :lg="4">
            <el-form-item label="材质" prop="materialQuality">
              <el-input v-model="queryParams.materialQuality" placeholder="请输入材质" clearable @keyup.enter.native="handleQuery" size="small" />
            </el-form-item>
          </el-col>
          <el-col :xs="8" :sm="6" :lg="4" v-if="hasmaterialCode">
            <el-form-item label="物料编码" prop="materialCode">
              <el-input v-model="queryParams.materialCode" placeholder="请输入物料编码" clearable @keyup.enter.native="handleQuery" size="small" />
            </el-form-item>
          </el-col>
          <el-col :xs="8" :sm="6" :lg="4">
            <el-form-item label="状态" prop="status">
              <el-select v-model="queryParams.status" placeholder="请选择状态" @change="handleQuery">
                <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="8" :sm="6" :lg="4">
            <el-form-item label="单位" prop="unit">
              <el-select v-model="queryParams.unit" placeholder="请选择单位" @change="handleQuery" clearable>
                <el-option v-for="(item, index) in unitOptions" :key="index" :label="item" :value="item" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="8" :sm="6" :lg="4">
            <el-form-item label="行业分类" prop="industry">
              <el-select clearable filterable v-model="queryParams.industry" placeholder="请选择行业分类" style="width: 100%" @change="handleQuery">
                <el-option v-for="(item, index) in industryOptions" :key="index" :label="item.name" :value="item.name"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="1.5">
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
            </el-form-item>
            <el-form-item>
              <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
            </el-form-item>
            <el-form-item>
              <el-button type="success" icon="el-icon-download" size="small" :disabled="canExport() || list.length === 0" @click="handleExport">导出</el-button>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="el-icon-plus" size="small" @click="handleAdd" v-hasPermi="['system:privateduct:add']">新增</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <div class="tableBox">
      <!-- 收藏管理 -->
      <collect-tpl ref="collect" :isCollect="false" :storeId.sync="storeId" :collectList.sync="collectList" @getList="getList" @colletSubmit="colletSubmit" :isSearch="true" :search="true" :showSearch.sync="showSearch" @queryTable="getList" :columns="columns" class="collectTpl" type="UserPriProduct" />

      <template v-if="list.length">
        <el-table v-loading="loading" ref="table" stripe :data="list" row-key="id" :key="key" style="width: 100%" class="custom-table custom-table-cell5" @selection-change="handleSelectionChange" border @header-dragend="handleColChange" id="table">
          <el-table-column type="selection" min-width="55" align="center" :width="tableWidth[0]" />
          <el-table-column align="center" type="index" label="序号" v-if="columns[0].visible" min-width="50" :width="tableWidth[1]"></el-table-column>
          <el-table-column align="center" prop="productName" label="产品名称" show-overflow-tooltip v-if="columns[1].visible" :width="tableWidth[2]" min-width="130">
            <template slot-scope="{ row }">
              <span class="table-link pointer" @click="handleDetail(row)">{{ row.productName }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="productCode" label="产品编码" show-overflow-tooltip v-if="columns[2].visible" :width="tableWidth[3]"></el-table-column>
          <el-table-column align="center" prop="materialCode" label="物料编码" show-overflow-tooltip v-if="hasmaterialCode && columns[14].visible" :width="tableWidth[4]"></el-table-column>
          <el-table-column align="center" prop="picture1" label="图片" :width="tableWidth[5]">
            <template slot-scope="{ row }">
              <div style="display: flex; justify-content: center">
                <el-image :src="formatProductImg(row)" fit="cover" @click="handleImg(row)">
                  <div slot="error" class="image-slot">
                    <i class="el-icon-picture-outline"></i>
                  </div>
                </el-image>
              </div>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="specs" label="规格" show-overflow-tooltip v-if="columns[4].visible" :width="tableWidth[6]"></el-table-column>
          <el-table-column align="center" prop="model" label="型号" show-overflow-tooltip v-if="columns[5].visible" :width="tableWidth[7]"></el-table-column>
          <el-table-column align="center" prop="unit" label="单位" show-overflow-tooltip v-if="columns[6].visible" :width="tableWidth[8]" min-width="50"></el-table-column>
          <el-table-column align="center" prop="weight" label="重量" show-overflow-tooltip v-if="columns[7].visible" :width="tableWidth[9]" min-width="50"></el-table-column>
          <el-table-column align="center" prop="industry" label="行业分类" show-overflow-tooltip v-if="columns[8].visible" :width="tableWidth[10]"></el-table-column>
          <el-table-column align="center" prop="materialQuality" label="材质" show-overflow-tooltip v-if="columns[10].visible" :width="tableWidth[11]"></el-table-column>
          <el-table-column align="center" prop="surface" label="表面" show-overflow-tooltip v-if="columns[10].visible" :width="tableWidth[12]"></el-table-column>
          <el-table-column align="center" prop="status" label="状态" show-overflow-tooltip v-if="columns[11].visible" min-width="80" :width="tableWidth[13]">
            <template slot-scope="{ row }">
              <template>
                <el-switch v-model="row.status" active-text="上架" inactive-text="下架" :active-value="1" :inactive-value="0" @change="handlePutaway(row, $event)" class="table-switch"></el-switch>
              </template>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="createBy" label="创建人" show-overflow-tooltip v-if="columns[12].visible" min-width="100" :width="tableWidth[14]"></el-table-column>
          <el-table-column align="center" prop="createTime" label="创建时间" show-overflow-tooltip v-if="columns[13].visible" min-width="100" :width="tableWidth[15]"></el-table-column>
          <el-table-column align="center" label="操作" min-width="100" :width="tableWidth[16]">
            <template slot-scope="{ row }">
              <el-dropdown @command="handleDropdown">
                <span style="color: #2e73f3">
                  操作菜单
                  <i class="el-icon-arrow-down el-icon--right"></i>
                </span>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item icon="el-icon-edit" v-hasPermi="['system:privateduct:edit']" :command="handleCommand(row, 'edit')">修改</el-dropdown-item>
                  <el-dropdown-item icon="el-icon-view" v-hasPermi="['system:privateduct:remove']" :command="handleCommand(row, 'view')">查看</el-dropdown-item>
                  <!--                  <el-dropdown-item icon="el-icon-delete" v-hasPermi="['system:privateduct:remove']" :command="handleCommand(row, 'delete')">删除</el-dropdown-item>-->
                  <el-dropdown-item icon="el-icon-share" v-hasPermi="['system:privateduct:share']" :command="handleCommand(row, 'share')">共享</el-dropdown-item>
                  <el-dropdown-item icon="el-icon-paperclip" v-hasPermi="['system:privateduct:collect']" :command="handleCommand(row, 'collect')">收藏到</el-dropdown-item>
                  <el-dropdown-item icon="el-icon-edit" v-hasPermi="['system:privateduct:edit']" :command="handleCommand(row, 'file')">修改附件</el-dropdown-item>
                  <el-dropdown-item icon="el-icon-position" v-hasPermi="['system:privateduct:recommend']" :disabled="!row.canRecommend" :command="handleCommand(row, 'public')">转到公域</el-dropdown-item>
                  <el-dropdown-item icon="el-icon-s-release" :command="handleCommand(row, 'dead')">引至滞销品</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>

        <div class="custom-pagination">
          <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
        </div>
      </template>
      <el-empty :image-size="200" v-else />

      <!-- 新增/修改 -->
      <product-create ref="productCreate" :is-get-config="false" @callback="getList" @callCreate="callCreate"></product-create>

      <!-- 详情 -->
      <product-dialog ref="productInfo"></product-dialog>

      <!-- 批量收藏 -->
      <template v-if="!multiple">
        <template v-if="checkPermi(['system:privateduct:share']) || checkPermi(['system:privateduct:collect'])">
          <div class="mark" v-if="isCollect" @click="isCollect = !isCollect"></div>
          <div class="collectAll">
            <div class="collectAll-box">
              <div class="collectAll-title">已选择 {{ ids.length }} 项</div>
              <template v-if="checkPermi(['system:privateduct:collect'])">
                <el-tooltip content="收藏到其他收藏夹" placement="top" effect="dark">
                  <div class="collectAll-btn">
                    <span :class="{ active: isCollect }" @click="isCollect = !isCollect">收藏到</span>
                  </div>
                </el-tooltip>
              </template>
              <template v-if="checkPermi(['system:privateduct:share'])">
                <el-tooltip content="共享给其他成员" placement="top" effect="dark">
                  <div class="collectAll-btn">
                    <span @click="handleShare(ids)">共享</span>
                  </div>
                </el-tooltip>
              </template>
              <el-tooltip content="退出" placement="top" effect="dark">
                <div class="collectAll-close" @click="handleClose">
                  <i class="el-icon-close"></i>
                </div>
              </el-tooltip>
            </div>
          </div>
          <el-collapse-transition>
            <div class="collectAll-list-box" v-if="isCollect">
              <div class="collectAll-list-close">
                <span>请选择收藏夹</span>
                <i class="el-icon-close" @click="isCollect = !isCollect"></i>
              </div>
              <div class="collectAll-list">
                <div class="item" :class="{ disabled: item.storeId === storeId }" v-for="item in collectList" :key="item.storeId" @click="handleColletAll(item)">
                  {{ item.dirName }}
                </div>
              </div>
            </div>
          </el-collapse-transition>
        </template>
      </template>

      <!-- 共享 -->
      <el-dialog v-dialogDragBox title="共享产品" :visible.sync="shareOpen" width="1150px" class="custom-dialog" :before-close="handleShareCancel">
        <div class="formBox">
          <el-table :data="shareList" ref="shareList" style="width: 100%" stripe class="custom-table" @selection-change="handleSelectShare" row-key="userId">
            <el-table-column type="selection" width="50" align="center" reserve-selection />
            <el-table-column label="用户编号" align="center" prop="userId" width="100" />
            <el-table-column label="用户昵称" align="center" prop="nickName" :show-overflow-tooltip="true" />
            <el-table-column label="手机号码" align="center" prop="phonenumber" width="120" />
            <el-table-column label="备注" align="center" prop="remark" />
            <el-table-column label="创建时间" align="center" prop="createTime" width="160">
              <template slot-scope="scope">
                <span>{{ parseTime(scope.row.createTime) }}</span>
              </template>
            </el-table-column>
          </el-table>

          <div class="custom-pagination">
            <pagination v-show="shareTotal > 0" :total="shareTotal" :page.sync="shareParams.pageNum" :limit.sync="shareParams.pageSize" @pagination="getshareList" />
          </div>
        </div>
        <div slot="footer">
          <el-badge>
            <button type="button" class="custom-dialog-btn" @click="handleShareCancel">取消</button>
          </el-badge>
          <el-badge :value="shareChecked.length || ''">
            <button type="button" class="custom-dialog-btn primary" :class="{ disabled: !shareChecked.length }" :disabled="!shareChecked.length" @click="handleShareSubmit">确定</button>
          </el-badge>
        </div>
      </el-dialog>

      <!-- 推荐到公域 -->
      <el-dialog v-dialogDragBox title="转到公域" :visible.sync="recommendOpen" width="600px" class="custom-dialog">
        <div class="recommendBox">
          <!-- <template v-if="companyLevel > 0"> -->
          <template v-if="brandList.length">
            <div class="recommendBox-title">请选择品牌</div>
            <el-radio-group v-model="recommend.brandId" size="medium">
              <el-radio :label="item.id" border v-for="item in brandList" :key="item.id">{{ item.name }}</el-radio>
            </el-radio-group>
          </template>
          <template v-else>
            <div class="recommendBox-title" style="font-size: 14px">
              还没有添加品牌?去
              <el-button type="text" @click="handleAddBrand">添加</el-button>
            </div>
          </template>
          <!-- </template> -->
          <!-- <template v-else>
            <div class="recommendBox-title" style="font-size: 14px">
              还没有升级VIP?去
              <el-button type="text" @click="handleUpgradeVip">升级</el-button>
            </div>
          </template> -->
        </div>
        <div slot="footer">
          <button type="button" class="custom-dialog-btn recommendBox-btn" @click="recommendOpen = false">取消</button>
          <button type="button" class="custom-dialog-btn recommendBox-btn primary" @click="handleRecommendSubmit">确定</button>
        </div>
      </el-dialog>
    </div>

    <!--    引入滞销品-->
    <create-dialog ref="unsalable" />
    <!-- 引入金蝶物料创建 -->
    <kingdee-material-create ref="kingdeeMaterialCreate" @callCreateSuccess="kingdeeMaterialCreateCallBack" v-if="kingdeeMaterialCreateShow" />
    <!-- 查看金蝶物料详情 -->
    <kingdee-material-detail ref="kingdeeMaterialDetail" @callBack="kingdeeMaterialDetailShow = false" v-if="kingdeeMaterialDetailShow" />
  </div>
</template>

<script>
import { listPrivateduct, delPrivateduct, shangxia, recommendProduct, shareProduct } from '@/api/system/privateduct'
import { getUserProfile, supplier, chart, ediSupplier, qrcode, inviteHas, inviteVerify, smsCode, userUpgrade, userBrandList, userBrandAdd, userBrandDel, userUpgradeVip } from '@/api/system/user'
import { shoucTo } from '@/api/houtai/shoucang'
import { checkPermi } from '@/utils/permission'
import ProductDialog from '@/views/public/product/dialog'
import CollectTpl from '@/views/components/collect'
import { listUser } from '@/api/system/user'
import createDialog from '@/views/unsalable/create'
import productCreate from '@/views/components/private'
import { getConfigDetail } from '@/api/config'
import { codingRuleList } from '@/api/coding'
import KingdeeMaterialCreate from '@/views/kingdee/material/create'
import KingdeeMaterialDetail from '@/views/kingdee/material/detail'

export default {
  name: 'Allproduct',
  components: { ProductDialog, CollectTpl, productCreate, createDialog, KingdeeMaterialCreate, KingdeeMaterialDetail },
  data() {
    return {
      showSearch: true,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        productName: undefined,
        productCode: undefined,
        specs: undefined,
        model: undefined,
        surface: undefined,
        materialQuality: undefined,
        unit: undefined,
        industry: undefined,
        status: 1
      },
      statusOptions: [
        { label: '上架', value: 1 },
        { label: '下架', value: 0 }
      ],
      unitOptions: ['吨', '千克', '个', '件', '套', '米', '支', '根'],
      key: 1,
      loading: true,
      list: [],
      // 显隐列
      columns: [
        { key: 0, label: `序号`, visible: true },
        { key: 1, label: `产品名称`, visible: true },
        { key: 2, label: `产品编码`, visible: true },
        { key: 3, label: `图片`, visible: true },
        { key: 4, label: `规格`, visible: true },
        { key: 5, label: `型号`, visible: true },
        { key: 6, label: `单位`, visible: true },
        { key: 7, label: `重量`, visible: true },
        { key: 8, label: `行业分类`, visible: true },
        { key: 9, label: `材质`, visible: true },
        { key: 10, label: `表面`, visible: true },
        { key: 11, label: `状态`, visible: true },
        { key: 12, label: `创建人`, visible: true },
        { key: 13, label: `创建时间`, visible: true },
        { key: 14, label: `物料编码`, visible: true }
      ],
      // 收藏
      storeId: undefined,
      collectList: [],
      multiple: true,
      ids: [],
      isCollect: false,
      // 共享
      shareOpen: false,
      shareForm: {},
      shareList: [],
      shareTotal: 0,
      shareParams: {
        pageNum: 1,
        pageSize: 10,
        userName: undefined,
        phonenumber: undefined
      },
      shareChecked: [],
      // 品牌列表
      companyLevel: 0,
      recommend: {},
      recommendOpen: false,
      brandList: [],
      // 表格列宽
      tableWidth: [],
      hasmaterialCode: false,
      // 行业分类
      industryOptions: [],
      kingdeeMaterialCreateShow: false,
      kingdeeMaterialDetailShow: false
    }
  },
  created() {
    this.companyLevel = localStorage.getItem('companyLevel')
    this.getBrandList()
    this.getList()
    const queryData = this.$route.query.add
    if (queryData) {
      this.handleAdd()
    }
    this.getConfig()
    // 获取行业分类
    this.getIndustryList()
  },
  computed: {
    companyId() {
      return this.$store.getters.info.companyId
    }
  },
  methods: {
    // 查询企业参数是否有金蝶物料编码
    getConfig() {
      getConfigDetail({ configKey: 'kingdee.material.code' }).then(res => {
        const { code, data } = res
        if (code === 200) this.hasmaterialCode = data.hasOwnProperty('configValue') && data.configValue === 'true'
        if (!this.hasmaterialCode) this.columns.pop()
      })
    },
    // 获取行业分类
    getIndustryList() {
      codingRuleList().then(res => {
        const { code, msg, data } = res
        if (code === 200) this.industryOptions = data?.industry || []
        else this.$message.error(msg)
      })
    },
    checkPermi,
    // 查询品牌
    getBrandList() {
      userBrandList().then(res => {
        if (res.code === 200) {
          this.brandList = res.data
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 列表
    getList() {
      this.loading = true
      listPrivateduct(this.queryParams).then(res => {
        if (res.code === 200) {
          this.list = res.rows
          this.total = res.total
          this.loading = false
          this.key = Math.random()
          const tableWidth = localStorage.getItem('privateProductWidth')
          if (tableWidth) this.tableWidth = JSON.parse(tableWidth)
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 改变列宽
    handleColChange(newWidth, oldWidth, column, event) {
      setTimeout(() => {
        var TableColWidth = []
        var applyTable = document.getElementById('table')
        var applyTableColgroup = applyTable.getElementsByTagName('colgroup')[0]
        var applyTableCol = applyTableColgroup.getElementsByTagName('col')
        for (var i = 0; i < applyTableCol.length; i++) {
          TableColWidth.push(applyTableCol[i].width)
        }
        localStorage.setItem('privateProductWidth', JSON.stringify(TableColWidth))
      }, 100)
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.multiple = !selection.length
    },
    // 关闭多选
    handleClose() {
      this.isCollect = false
      this.multiple = true
      this.ids = []
      this.$refs.table.clearSelection()
    },
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 重置搜索
    resetQuery() {
      this.queryParams.status = 1
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 新增
    handleAdd() {
      this.$refs.productCreate.handleAdd(this.hasmaterialCode)
    },
    // 修改
    handleUpdate(row, type = '') {
      this.$refs.productCreate.handleUpdate(row, type, this.hasmaterialCode)
    },
    // 上下架
    handlePutaway(row, status) {
      const data = { ids: row.id, status }
      shangxia(data).then(res => {
        if (res.code === 200) {
          this.$message.success('操作成功')
          this.getList()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 详情
    handleDetail(item, type) {
      this.$refs.productInfo.handleView(item, type)
    },
    // 图片预览
    handleImg(row) {
      this.$refs.productInfo.handleImgView(row)
    },
    // 单个收藏
    handleCollet(item) {
      this.$refs.collect.handleChange(item.id)
    },
    // 批量收藏
    handleColletAll(item) {
      const storeId = item.storeId
      const valueIdList = this.ids
      if (storeId === this.storeId) return
      shoucTo({ storeId, valueIdList }).then(res => {
        // this.getList()
        this.$message.success('操作成功')
        this.isCollect = false
        this.multiple = false
        this.ids = []
        this.$refs.table.clearSelection()
      })
    },
    // 提交收藏
    colletSubmit(storeId, data) {
      if (!Array.isArray(data)) data = [data]
      shoucTo({ storeId, valueIdList: data }).then(res => {
        this.getList()
        this.$message.success('操作成功')
      })
    },
    // 删除
    // prettier-ignore
    handleDelete(row) {
      const ids = row.id
      this.$confirm('是否删除此数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        delPrivateduct(ids).then(res => {
          if (res.code === 200) {
            this.$message.success('删除成功')
            this.getList()
          } else {
            this.$message.error(res.msg)
          }
        })
      }).catch(() => {
      })
    },
    // 推荐到公域
    // prettier-ignore
    handleRecommend(row) {
      const productId = row.id
      this.$confirm('是否确认转到公域?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        recommendProduct({ productId }).then(res => {
          if (res.code === 200) {
            this.$message.success('操作成功,等待审核通过')
            this.recommendOpen = false
            this.getList()
          } else {
            this.$message.error(res.msg)
          }
        })
      }).catch(() => {
      })
      //   this.recommend = {
      //     productId: undefined,
      //     brandId: undefined
      //   }
      //   this.recommend.productId = row.id
      //   this.recommendOpen = true
    },
    // // 确定推荐到公域
    handleRecommendSubmit() {
      //   recommendProduct(this.recommend).then(res => {
      //     if (res.code === 200) {
      //       this.$message.success('操作成功,等待审核通过')
      //       this.recommendOpen = false
      //       this.getList()
      //     } else {
      //       this.$message.error(res.msg)
      //     }
      //   })
    },
    // 添加品牌
    // prettier-ignore
    handleAddBrand() {
      const companyId = localStorage.getItem('companyId')
      const that = this
      this.$prompt('请输入品牌名称', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false,
        inputPattern: /^.+$/,
        inputErrorMessage: '请输入品牌名称'
      }).then(({ value }) => {
        const data = { name: value, companyId }
        userBrandAdd(data).then(function (res) {
          if (res.code === 200) {
            that.getBrandList()
          } else {
            that.$message.error(res.msg)
          }
        })
      }).catch(() => {
      })
    },
    // 升级VIP
    // prettier-ignore
    handleUpgradeVip() {
      this.$confirm('是否确认升级VIP?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        userUpgradeVip().then(res => {
          if (res.code === 200) {
            this.$message.success('提交成功,请等待审核')
            this.recommendOpen = false
          } else {
            this.$message.error(res.msg)
          }
        })
      }).catch(() => {
      })
    },
    // 重置共享给他人
    resetShare() {
      this.shareForm = {
        productIds: [],
        userId: []
      }
      this.resetForm('shareForm')
    },
    // 共享给他人
    handleShare(data) {
      this.resetShare()
      if (!Array.isArray(data)) data = [data]
      this.shareForm.productIds = data
      this.getshareList()
      this.shareOpen = true
      this.$nextTick(() => {
        this.$refs.shareList.clearSelection()
      })
    },
    // 查询成员
    getshareList() {
      listUser(this.shareParams).then(res => {
        if (res.code === 200) {
          this.shareList = res.rows
          this.shareTotal = res.total
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 多选成员
    handleSelectShare(selection) {
      this.shareChecked = selection
    },
    // 取消共享
    handleShareCancel() {
      this.shareOpen = false
      this.handleClose()
    },
    // 确定共享
    handleShareSubmit() {
      const userId = this.shareChecked.map(item => item.userId)
      this.shareForm.userId = userId
      shareProduct(this.shareForm).then(res => {
        if (res.code === 200) {
          this.$message.success('操作成功')
          this.shareOpen = false
          this.getList()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 引入滞销品
    handleUnsalable(row) {
      this.$refs.unsalable.handleAdd(row)
    },
    handleDropdown(command) {
      const { row, type } = command
      switch (type) {
        case 'edit':
          this.handleUpdate(row)
          break
        case 'delete':
          this.handleDelete(row)
          break
        case 'share':
          this.handleShare(row.id)
          break
        case 'collect':
          this.handleCollet(row)
          break
        case 'file':
          this.handleUpdate(row, 'file')
          break
        case 'public':
          this.handleRecommend(row)
          break
        case 'dead':
          this.handleUnsalable(row)
          break
        case 'view':
          this.handleUpdate(row, 'view')
          break
      }
    },
    handleCommand(row, type) {
      return { row, type }
    },
    // 是否可以导出
    canExport() {
      const params = { ...this.queryParams }
      delete params.pageNum
      delete params.pageSize
      const hasValue = Object.values(params).some(val => val !== undefined && val !== '')
      return !hasValue
    },
    // 导出
    // prettier-ignore
    handleExport() {
      const params = { ...this.queryParams }
      delete params.pageNum
      delete params.pageSize
      // 检查queryParams中有值的参数个数
      const validParamCount = Object.values(params).filter(val => val !== undefined && val !== '').length
      if (validParamCount === 1) {
        this.$confirm('筛选条件较少，查询结果数量较多，可能会导出失败，是否继续导出？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.download('/system/privateduct/export', { ...params }, `私域产品_${new Date().getTime()}.xlsx`)
        }).catch(() => { })
      } else this.download('/system/privateduct/export', { ...params }, `私域产品_${new Date().getTime()}.xlsx`)
    },
    // 创建金蝶物料
    callCreate(material = {}) {
      if (this.companyId != '14') return
      this.kingdeeMaterialCreateShow = true
      this.$nextTick(() => {
        this.$refs.kingdeeMaterialCreate.handleCreate(material)
      })
    },
    // 金蝶物料创建回调
    kingdeeMaterialCreateCallBack(materialCode) {
      this.kingdeeMaterialDetailShow = true
      this.$nextTick(() => {
        this.$refs.kingdeeMaterialDetail.getInfo(materialCode)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
.tableBox {
  padding: 10px 20px 20px;
}
.custom-search {
  padding-top: 17px;
  ::v-deep {
    .el-form-item--small {
      display: inline-flex;
      .el-form-item__label {
        flex-shrink: 0;
      }
      .el-form-item {
        margin-bottom: 10px;
      }
    }
  }
}
.custom-dialog {
  ::v-deep {
    .formBox {
      padding: 20px;
    }
    .custom-dialog-btn.disabled {
      opacity: 0.5;
    }
  }
}
.collectTpl {
  margin-bottom: 0;
  ::v-deep {
    .collect {
      margin-bottom: 0;
      background-color: transparent;
      border-color: transparent;
    }
  }
}
::v-deep {
  .recommendBox {
    padding: 0 20px;
    &-title {
      font-size: 16px;
      line-height: 40px;
    }
    &-btn {
      width: 150px;
    }
  }
}
.custom-table {
  ::v-deep .el-table__body-wrapper {
    .el-table__row {
      td.el-table__cell {
        border-right-color: transparent;
      }
    }
  }
}
</style>
