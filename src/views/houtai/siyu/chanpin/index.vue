<template>
  <div class="app-container bgcf9 vh-85">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" @submit.native.prevent>
      <el-form-item label="产品名称" prop="keyword">
        <el-input v-model="queryParams.keyword" placeholder="请输入产品名称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
        <el-button type="primary" icon="el-icon-plus" size="small" @click="handleAdd" v-hasPermi="['system:privateduct:add']">新增</el-button>
      </el-form-item>
    </el-form>

    <!-- 收藏管理 -->
    <collect-tpl ref="collect" type="UserPriProduct" :storeId.sync="storeId" :collectList.sync="collectList" @getList="getList" @colletSubmit="colletSubmit" :isSearch="true" :search="true" :showSearch.sync="showSearch" @queryTable="getList" :columns="columns" />

    <template v-if="dataList.length">
      <el-table ref="table" v-loading="loading" :data="dataList" border row-key="id" @selection-change="handleSelectionChange" size="small" :key="key" :class="{ drop: isPC() }">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column align="center" label="序号" type="index" v-if="columns[0].visible" />
        <el-table-column label="产品名称" align="center" prop="productName" v-if="columns[1].visible">
          <template slot-scope="{ row }">
            <div class="table-link pointer" @click="handleDetail(row)">{{ row.productName }}</div>
          </template>
        </el-table-column>
        <el-table-column label="产品编码" align="center" prop="productCode" v-if="columns[2].visible" />
        <el-table-column label="图片" align="center" prop="diagram" width="80" v-if="columns[3].visible">
          <template slot-scope="{ row }">
            <image-preview :src="row.picture1_oss || imgPath + row.picture1" :width="50" :height="50" />
          </template>
        </el-table-column>
        <el-table-column label="三维图片" align="center" prop="diagram" width="80" v-if="columns[4].visible">
          <template slot-scope="{ row }">
            <image-preview :src="row.diagram_oss || imgPath + row.diagram" :width="50" :height="50" />
          </template>
        </el-table-column>
        <el-table-column label="图纸" align="center" prop="draw" v-if="columns[5].visible" width="85">
          <template slot-scope="{ row }">
            <el-button type="text" size="mini" @click="handleDetail(row, 'paper')" icon="el-icon-view">图纸文件</el-button>
          </template>
        </el-table-column>
        <el-table-column label="行业分类" align="center" prop="industry" v-if="columns[6].visible" />
        <el-table-column label="规格" align="center" prop="specs" v-if="columns[7].visible" />
        <el-table-column label="型号" align="center" prop="model" v-if="columns[8].visible" />
        <el-table-column label="单位" align="center" prop="unit" v-if="columns[9].visible" width="55" />
        <el-table-column label="重量" align="center" prop="weight" v-if="columns[10].visible" />
        <el-table-column label="材质" align="center" prop="materialQuality" v-if="columns[11].visible" />
        <el-table-column label="状态" align="center" prop="status" v-if="columns[12].visible" width="55">
          <template slot-scope="scope">
            {{ scope.row.status == -1 ? '下架' : '上架' }}
          </template>
        </el-table-column>
        <el-table-column label="表面" align="center" prop="surface" v-if="columns[13].visible" />
        <el-table-column v-if="admin === 'true'" label="创建人" align="center" prop="createBy" width="95" />
        <el-table-column v-if="admin === 'true'" label="创建时间" align="center" prop="createTime" />
        <el-table-column v-if="admin === 'true'" label="更新时间" align="center" prop="updateTime" />
        <el-table-column align="center" label="操作" width="190">
          <template slot-scope="{ row }">
            <el-button type="text" size="mini" icon="el-icon-view" @click="handleDetail(row)">详情</el-button>
            <el-button type="text" size="mini" icon="el-icon-delete" @click="handleDelete(row)">删除</el-button>
            <el-button type="text" size="mini" icon="el-icon-paperclip" @click="handleCollet(row)">收藏到</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="custom-pagination">
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
      </div>
    </template>
    <el-empty :image-size="200" v-else />

    <!-- 新增/修改 -->
    <product-create ref="productCreate" @callCreate="callCreate"></product-create>

    <!-- 详情 -->
    <product-dialog ref="productInfo"></product-dialog>

    <!-- 批量收藏 -->
    <template v-if="!multiple">
      <div class="mark" v-if="isCollect" @click="isCollect = !isCollect"></div>
      <div class="collectAll">
        <div class="collectAll-box">
          <div class="collectAll-title">已选择 {{ ids.length }} 项</div>
          <el-tooltip content="收藏到其他收藏夹" placement="top" effect="dark">
            <div class="collectAll-btn">
              <span :class="{ active: isCollect }" @click="isCollect = !isCollect">收藏到</span>
            </div>
          </el-tooltip>
          <el-tooltip content="退出" placement="top" effect="dark">
            <div class="collectAll-close" @click="handleClose">
              <i class="el-icon-close"></i>
            </div>
          </el-tooltip>
        </div>
      </div>
      <el-collapse-transition>
        <div class="collectAll-list-box" v-if="isCollect">
          <div class="collectAll-list-close">
            <span>请选择收藏夹</span>
            <i class="el-icon-close" @click="isCollect = !isCollect"></i>
          </div>
          <div class="collectAll-list">
            <div class="item" :class="{ disabled: item.storeId === storeId }" v-for="item in collectList" :key="item.storeId" @click="handleColletAll(item)">
              {{ item.dirName }}
            </div>
          </div>
        </div>
      </el-collapse-transition>
    </template>

    <!-- <div class="tipShow">按住可进行拖拽排序</div> -->
    <!-- 引入金蝶物料创建 -->
    <kingdee-material-create ref="kingdeeMaterialCreate" @callCreateSuccess="kingdeeMaterialCreateCallBack" v-if="kingdeeMaterialCreateShow" />
    <!-- 查看金蝶物料详情 -->
    <kingdee-material-detail ref="kingdeeMaterialDetail" @callBack="kingdeeMaterialDetailShow = false" v-if="kingdeeMaterialDetailShow" />
  </div>
</template>

<script>
import Sortable from 'sortablejs'
import { getlistb, shoucTo, delshouc, shoucGo } from '@/api/houtai/shoucang'
import ProductDialog from '@/views/public/product/dialog'
import CollectTpl from '@/views/components/collect'
import productCreate from '@/views/components/private'
import KingdeeMaterialCreate from '@/views/kingdee/material/create'
import KingdeeMaterialDetail from '@/views/kingdee/material/detail'

export default {
  name: 'Collects',
  components: { ProductDialog, CollectTpl, productCreate, KingdeeMaterialCreate, KingdeeMaterialDetail },
  data() {
    return {
      key: 1,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 列表数据
      dataList: [],
      // 总条数
      total: 0,
      // 显示搜索条件
      showSearch: true,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        keyword: undefined,
        storeId: undefined
      },
      // 列信息
      columns: [
        { key: 0, label: `序号`, visible: true },
        { key: 1, label: `产品名称`, visible: true },
        { key: 2, label: `产品编码`, visible: true },
        { key: 3, label: `未税报价`, visible: true },
        { key: 4, label: `三维图片`, visible: true },
        { key: 5, label: `图纸`, visible: true },
        { key: 6, label: `行业分类`, visible: true },
        { key: 7, label: `规格`, visible: true },
        { key: 8, label: `型号`, visible: true },
        { key: 9, label: `单位`, visible: true },
        { key: 10, label: `重量`, visible: true },
        { key: 11, label: `材质`, visible: true },
        { key: 12, label: `状态`, visible: true },
        { key: 13, label: `表面`, visible: true }
      ],
      // 是否管理员
      admin: false,
      // 收藏列表ID
      storeId: undefined,
      sortable: null,
      // 收藏夹列表
      collectList: undefined,
      // 是否显示收藏夹列表
      isCollect: false,
      kingdeeMaterialCreateShow: false,
      kingdeeMaterialDetailShow: false
    }
  },
  created() {
    this.admin = localStorage.getItem('admin')
  },
  computed: {
    companyId() {
      return this.$store.getters.info.companyId
    }
  },
  methods: {
    // 判断当前设备是电脑、平板还是手机
    isPC() {
      let userAgentInfo = navigator.userAgent
      let Agents = ['Android', 'iPhone', 'SymbianOS', 'Windows Phone', 'iPad', 'iPod']
      let flag = true
      for (let v = 0; v < Agents.length; v++) {
        if (userAgentInfo.indexOf(Agents[v]) > 0) {
          flag = false
          break
        }
      }
      return flag
    },
    // 列表
    async getList() {
      this.loading = true
      if (this.storeId) {
        this.queryParams.storeId = this.storeId
        let { code, msg, rows, total } = await getlistb(this.queryParams)
        if (code === 200) {
          this.dataList = rows
          this.key = Math.random()
          this.total = total
          this.loading = false
          if (this.isPC()) {
            this.$nextTick(() => {
              this.rowDrop()
            })
          }
        } else {
          this.$modal.msgError(msg)
        }
      } else {
        this.loading = false
      }
    },
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 重置搜索
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 新增
    handleAdd() {
      this.$refs.productCreate.handleAdd()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length != 1
      this.multiple = !selection.length
    },
    // 详情
    handleDetail(item, type) {
      this.$refs.productInfo.handleView(item, type)
    },
    //行拖拽
    rowDrop() {
      const _this = this
      if (_this.dataList.length) {
        const tbody = document.querySelector('.el-table__body-wrapper tbody')
        this.sortable = Sortable.create(tbody, {
          ghostClass: 'sortable',
          animation: 180,
          delay: 0,
          onEnd({ newIndex, oldIndex }) {
            if (newIndex > oldIndex) {
              _this.handleMoveDown(newIndex, oldIndex)
            }
            if (newIndex < oldIndex) {
              _this.handleMoveUp(newIndex, oldIndex)
            }
          }
        })
      }
    },
    // 上移
    handleMoveUp(newIndex, oldIndex) {
      const valueId = this.dataList[oldIndex].id
      shoucGo({ index: newIndex, storeId: this.storeId, valueId }).then(res => {
        const oldItem = this.dataList[oldIndex]
        this.dataList.splice(oldIndex, 1)
        this.dataList.splice(newIndex, 0, oldItem)
      })
    },
    // 下移
    handleMoveDown(newIndex, oldIndex) {
      const valueId = this.dataList[oldIndex].id
      shoucGo({ index: newIndex, storeId: this.storeId, valueId }).then(res => {
        const oldItem = this.dataList[oldIndex]
        this.dataList.splice(oldIndex, 1)
        this.dataList.splice(newIndex, 0, oldItem)
      })
    },
    // 单个收藏
    handleCollet(item) {
      this.$refs.collect.handleChange(item.id)
    },
    // 批量收藏
    handleColletAll(item) {
      const storeId = item.storeId
      const valueIdList = this.ids
      if (storeId === this.storeId) return
      shoucTo({ storeId, valueIdList }).then(res => {
        this.$modal.msgSuccess('操作成功')
        this.isCollect = false
        this.multiple = false
        this.ids = []
        this.$refs.table.clearSelection()
      })
    },
    // 提交收藏
    colletSubmit(storeId, data) {
      if (!Array.isArray(data)) data = [data]
      shoucTo({ storeId, valueIdList: data }).then(res => {
        this.getList()
        this.$modal.msgSuccess('操作成功')
      })
    },
    // 删除
    // prettier-ignore
    handleDelete(item) {
      const data = { storeId: this.storeId, valueId: item.id }
      this.$modal.confirm('是否确认删除选中的收藏？').then(function () {
        return delshouc(data)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess('删除成功')
      }).catch(() => {
      })
    },
    // 关闭多选
    handleClose() {
      this.isCollect = false
      this.multiple = false
      this.ids = []
      this.$refs.table.clearSelection()
    },
    // 创建金蝶物料
    callCreate(material = {}) {
      if (this.companyId != '14') return
      this.kingdeeMaterialCreateShow = true
      this.$nextTick(() => {
        this.$refs.kingdeeMaterialCreate.handleCreate(material)
      })
    },
    // 金蝶物料创建回调
    kingdeeMaterialCreateCallBack(materialCode) {
      this.kingdeeMaterialDetailShow = true
      this.$nextTick(() => {
        this.$refs.kingdeeMaterialDetail.getInfo(materialCode)
      })
    }
  }
}
</script>

<style>
.sortable {
  opacity: 0.8;
  color: #fff !important;
  background: #409eff !important;
}
</style>
<style lang="scss" scoped>
.table-link {
  color: #409eff;
  &:hover {
    text-decoration: underline;
  }
}
::v-deep .table-badge {
  .cell {
    overflow: inherit;
  }
}
.price {
  font-size: 16px;
  padding-right: 5px;
  cursor: pointer;
}
.max {
  color: #ec2454;
}
.min {
  color: #67c23a;
}
.avg {
  color: #409eff;
}
.drop {
  ::v-deep .el-table__row {
    position: relative;
    &:hover {
      cursor: move;
      &:after {
        transition: all 0.3s;
        content: '按住可进行拖拽排序';
        position: absolute;
        left: 50%;
        top: -40px;
        z-index: 200;
        background-color: #303133;
        border-radius: 4px;
        padding: 10px;
        font-size: 12px;
        min-width: 10px;
        word-wrap: break-word;
        color: #fff;
      }
    }
    .zindex {
      z-index: 2;
    }
  }
}
.tipShow {
  display: none;
  background-color: #303133;
  color: #fff;
  position: fixed;
  z-index: 1;
  border-radius: 4px;
  padding: 10px;
  // z-index: 2000;
  font-size: 12px;
  line-height: 1.2;
  min-width: 10px;
  word-wrap: break-word;
}
</style>
