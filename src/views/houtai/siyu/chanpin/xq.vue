<template>
  <div>
    <el-dialog
      title="产品详情"
      :visible.sync="dialogVisible"
      width="60%"
      :before-close="handleClose">
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row :gutter="10" style="display:flex;flex-wrap:wrap">
          <el-col :span="12">
            <el-form-item label="产品名称" prop="productName">
              <el-input  disabled v-model="form.productName" placeholder="请输入产品名称"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="其他名称" prop="formerName">
              <el-input  disabled v-model="form.formerName" placeholder="请输入其他名称"/>
            </el-form-item>
          </el-col>
          <!--        categoryId-->
          <el-col :span="12">
            <el-form-item label="产品编码" prop="productCode">
              <el-input disabled v-model="form.productCode" placeholder="请输入产品编码"/>
            </el-form-item>
          </el-col>
<!--          <el-col :span="12">-->
<!--            <el-form-item label="属性" prop="attribute">-->
<!--              <el-input disabled  v-model="form.attribute" placeholder="请输入行业属性"/>-->
<!--            </el-form-item>-->
<!--          </el-col>-->
          <el-col :span="12">
            <el-form-item label="规格" prop="specs">
              <el-input disabled  v-model="form.specs" placeholder="请输入规格"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="型号" prop="model">
              <el-input disabled  v-model="form.model" placeholder="请输入型号"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="执行标准" prop="standard">
              <el-input disabled  v-model="form.standard" placeholder="请输入执行标准"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="材质" prop="materialQuality">
              <el-input
                disabled  v-model="form.materialQuality"
                placeholder="请输入材质"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="表面" prop="surface">
              <el-input disabled  v-model="form.surface" placeholder="请输入表面处理方式"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="重量" prop="weight">
              <el-input disabled  v-model="form.weight" placeholder="请输入重量"><span slot="suffix">Kg</span></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="单位" prop="unit">
              <el-input disabled  v-model="form.unit" placeholder="请输入单位"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">

          </el-col>
        </el-row>

        <el-form-item label="图片" prop="picture1">
          <el-image style="width:200px;" :src="up_file+form.picture1"></el-image>
        </el-form-item>
        <el-form-item label="图纸" prop="draw">
          {{form.draw}}
          <el-button @click="showtz(form.draw)" type="primary" size="small" style="margin-left: 10px;">查看</el-button>
        </el-form-item>

      </el-form>


      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="dialogVisible = false">取 消</el-button>
      </div>
    </el-dialog>


    <el-dialog v-dialogDragBox title="预览" :visible.sync="show" width="900px" append-to-body>
      <iframe :src="pdfUrl" style="width:100%;height: 500px" frameborder="0"></iframe>
    </el-dialog>


  </div>
</template>


<script>
import {getlistb} from "@/api/purchase/category";
import {editlist} from "@/api/houtai/gongyu/chanpin";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import { searchText, addText } from "@/api/purchase/gongyingshang";
import { isNumber } from '@/utils/validate'
import E from "wangeditor";

export default {
  name: 'add',
  components: {
    Treeselect
  },
  data() {
    return {
      fileType: ["png", "jpg", "jpeg", "pdf"],
      dialogVisible: false,
      // 表单参数
      form: {},
      tableData: [],
      // 表单校验
      rules: {
        categoryId: [
          {required: true, message: "请选择产品类目", trigger: "change"},
        ],
        productName: [
          {required: true, message: "产品名称不能为空", trigger: "blur"},
        ],
        productCode: [
          {required: true, message: "产品编码不能为空", trigger: "blur"},
        ],
        productType: [
          {required: true, message: "产品类型不能为空", trigger: "change"},
        ],
        picture1: [
          {required: true, message: "图片一不能为空", trigger: "blur"},
        ],
        draw: [{required: true, message: "图纸不能为空", trigger: "blur"}],
        // attribute: [
        //   {required: true, message: "请输入行业属性", trigger: "blur"},
        // ],
        specs: [{required: true, message: "规格不能为空", trigger: "blur"}],
        model: [{required: true, message: "型号不能为空", trigger: "blur"}],
        materialQuality: [
          {required: true, message: "材质不能为空", trigger: "blur"},
        ],
        surface: [{required: true, message: "请输入表面处理方式", trigger: "blur"}],
        weight: [
          { required: true, message: '请输入产品重量', trigger: 'blur' },
          { validator: isNumber, message: '输入有误请重新输入', trigger: 'blur' }
        ],
      },
      onedata: [],

      editor: null,

      up_file: '',
      show: false,
      pdfUrl: '',
    }
  },
  created() {
    this.up_file = process.env.VUE_APP_BASE_API;
  },
  methods: {
    /** 转换类别数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.name,
        children: node.children,
      };
    },
    showtz(e) {
      this.show = true;
      this.pdfUrl = process.env.VUE_APP_BASE_API + e.replace('http://localhost', '');
    },
    mapinit(){
      let that = this;
      const editorElement = document.getElementById('editorContainerc');
      // 销毁旧的编辑器实例
      if (editorElement.editor) {
        editorElement.editor.destroy();
      }
      // 创建富文本编辑器实例
      const editor = new E('#editorContainerc');
      // 自定义配置
      editor.config.height = 400;
      // 配置全屏功能按钮是否展示
      editor.config.showFullScreen = false;
      editor.config.uploadImgShowBase64 = true
      editor.config.readonly = true;
      // 自定义配置
      editor.config.menus = [

      ];
      // 初始化富文本编辑器
      editor.create();
      editor.disable();
      // 将编辑器实例保存到组件的变量中
      this.editor = editor;



      // //查询供应商
      // searchText({ objectId: this.onedata.id, type: 'chanpin' }).then((res) => {
      //   let row = res.data.textArea
      //   that.editor.txt.html(row) // 重新设置编辑器内容
      // });



    },

    ready() {
      getlistb({
        pageNum: 1,
        pageSize: 999,
      }).then((res) => {
        let lista = res.data;
        this.tableData = lista;
      });
    },
    showmodel(row) {
      let that = this;
      this.ready();
      this.dialogVisible = true;
      this.onedata = row;
      let lista = JSON.parse(JSON.stringify(this.onedata));
      this.form = lista;


      setTimeout(function(){
        that.mapinit();
      }, 500)
      // mapinit






    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          let datalistb = JSON.parse(JSON.stringify(this.form));
          datalistb['id'] = this.onedata.id;
          editlist(datalistb).then((res) => {
            this.$message('修改成功');
            this.dialogVisible = false;
            addText({
              objectId:  this.onedata.id,
              type:'chanpin',
              textArea:this.editor.txt.html()}).then((res) => {
            });


            this.$emit('shuaxin')
          });
        }
      });
    },
    handleClose(done) {
      this.$confirm('确认关闭？')
        .then(_ => {
          done();
        })
        .catch(_ => {
        });
    }
  }
}
</script>

<style scoped>

</style>
