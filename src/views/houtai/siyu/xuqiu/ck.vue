<template>
  <div>
    <el-dialog
      title="提示"
      :visible.sync="dialogVisible"
      width="50%">
      <el-table
        :data="tableData"
        style="width: 100%">
        <el-table-column align="center" prop="supplierName" label="供应商名称" width="180"></el-table-column>
        <el-table-column align="center" prop="phone" label="电话" width="180"></el-table-column>
        <el-table-column align="center" prop="responseTime" label="回复时间"></el-table-column>
        <el-table-column prop="amount" align="center" label="金额"></el-table-column>
        <el-table-column fixed="right" label="操作" width="120">
          <template slot-scope="scope">
            <el-button
              @click="sc(scope.row)"
              type="text"
              size="small">
              生成订单
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
    <el-button @click="dialogVisible = false">关 闭</el-button>
  </span>
    </el-dialog>

  </div>
</template>

<script>

import {baojia, scorder} from "@/api/houtai/siyu/xuqiu";

export default {
  name: 'ck',
  data() {
    return {
      dialogVisible: false,
      tableData: [],
      onedata: [],

    }

  },
  methods: {
    ready() {
      let that = this;
      baojia({
        demandId: that.onedata.id
      }).then((res) => {
        this.tableData = res.rows;

      });
    },
    showmodel(e) {
      this.onedata = e;
      this.ready();
      this.dialogVisible = true;
    },
    sc(row) {
      this.$confirm('是否生成订单?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        scorder({
          responseId: row.id
        }).then((res) => {
          this.$message({
            type: 'success',
            message: '创建成功!'
          });
        });
      }).catch(() => {

      });

    }
  }
}
</script>

<style scoped>

</style>
