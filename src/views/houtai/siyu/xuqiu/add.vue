<template>
  <div>
    <el-dialog
      title="新增采购需求"
      :visible.sync="dialogVisible"
      v-loading="loading"
      width="70%">
      <div>
        <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm">
          <el-form-item label="标题" prop="title">
            <el-input v-model="ruleForm.title"></el-input>
          </el-form-item>
          <el-row>
            <el-col :span="18">
              <el-form-item label="供货截止" prop="endTime">
              <el-date-picker
                style="width:100%"
                v-model="ruleForm.endTime"
                type="date"
                :picker-options="pickerOptions"
                value-format="yyyy-MM-dd"
                placeholder="选择日期">
              </el-date-picker>
            </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="有效时间">
                <el-input  type="number" min="1" v-model="jztimeb" @input="jztimebInput">
                  <template slot="append">
                    小时
                  </template>
                </el-input>
            </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="采购产品" prop="products">
            <el-select multiple
                       filterable
                       @visible-change="visibleChange"
                       style="width:100%"
                       v-model="ruleForm.products" placeholder="请选择">
              <el-option
                v-for="(item,index) in chanpinlist"
                :key="item.id"
                :label="item.productName + item.specs"
                :value="item.id">
                <span style="float: left">{{ item.productName }} + {{ item.specs }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">{{ item.model }}</span>
              </el-option>
            </el-select>



            <el-table
              :data="tableList"
              size="mini"
              style="width: 100%">
              <el-table-column
                prop="productName"
                label="名称">
                <template slot-scope="scope">
                  {{scope.row.productName}}-{{scope.row.specs}}-{{scope.row.model}}
                </template>
              </el-table-column>
              <el-table-column
                prop="num"
                label="数量">
                <template slot-scope="scope">
                  <input class="input1" size="mini" min="1" v-model="scope.row.numa" type="number" @input="numaInput(scope.$index,$event)"/>
                </template>
              </el-table-column>
              <!-- <el-table-column
                prop="price"
                label="价格">
                <template slot-scope="scope">
                  <input class="input1" size="mini" v-model="scope.row.price" type="number"/>
                </template>
              </el-table-column> -->
              <el-table-column
                prop="price"
                label="单位">
                <template slot-scope="scope">
                  <el-select v-model="scope.row.unit" placeholder="请选择">
                    <el-option
                      v-for="(item,index) in options"
                      :key="index"
                      :label="item"
                      :value="item">
                    </el-option>
                  </el-select>

                </template>
              </el-table-column>
              <el-table-column
                prop="address"
                label="操作">
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    type="danger"
                    @click="handleDelete(scope.$index, scope.row)" plain>删除
                  </el-button>
                  <el-button
                    size="mini"
                    type="primary"
                    @click="huoquxq( scope.row)" plain>
                    产品详情
                  </el-button>
                </template>

              </el-table-column>

            </el-table>
          </el-form-item>

          <el-form-item label="供应商" prop="supplierIds">
            <el-select multiple
                      filterable
                       style="width:100%"
                       v-model="ruleForm.supplierIds" placeholder="请选择">
              <el-option
                v-for="item in gongyinglist"
                :key="item.id"
                :label="item.name"
                :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="备注" prop="remark">
            <el-input type="textarea" v-model="ruleForm.remark"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="submitForm('ruleForm')">立即创建</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-dialog>


    <el-dialog v-dialogDragBox title="" :visible.sync="centerDialogVisible" width="50%" center>
      <img :src="up_file+imgfile" style="width: 100%;height: 100%" alt=""/>
      <span slot="footer" class="dialog-footer">
        <el-button @click="centerDialogVisible = false">取 消</el-button>
      </span>
    </el-dialog>

        <!-- 产品详情 -->
        <el-dialog
      :visible.sync="pinfoview"
      width="1000px"
    >
      <div class="pinfobox">
        <div class="pinfobox1">
          <div @click="pinfoindex=1" :class="pinfoindex==1?'pinfobox1sel':''">产品详情</div>
<!--          <div @click="pinfoindex=2;drawPreView2(pinfocontent.draw)" :class="pinfoindex==2?'pinfobox1sel':''">产品图纸</div>-->
          <div @click="pinfoindex=3" :class="pinfoindex==3?'pinfobox1sel':''">检测报告</div>
        </div>
        <div class="pinfobox2" v-if="pinfoindex==1">
          <div>
            <img style="width:305px;height:305px;" :src="'http://www.ziyouke.net/prod-api'+pinfocontent.picture1">
          </div>
          <div class="pinfobox2-2">
            <div class="pinfobox2-2-1" style=" border-top:1px solid #CBD6E2;">
              <div>产品名称</div>
              <div>{{pinfocontent.productName}}</div>
            </div>
            <div class="pinfobox2-2-1">
              <div>所属分类</div>
              <div>{{pinfocontent.industry}}</div>
            </div>
            <div class="pinfobox2-2-1">
              <div>产品重量</div>
              <div>{{pinfocontent.weight}}</div>
            </div>
            <div class="pinfobox2-2-1">
              <div>产品模型</div>
              <div>{{pinfocontent.model}}</div>
            </div>
            <div class="pinfobox2-2-1">
              <div>产品规格</div>
              <div>{{pinfocontent.specs}}</div>
            </div>
            <div class="pinfobox2-2-1">
              <div>表面处理</div>
              <div>{{pinfocontent.surface}}</div>
            </div>
          </div>
          <!-- <div class="pinfobox2-3">
            <div>厂区照片</div>
          </div> -->
        </div>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import Cookies from "js-cookie";
import {getlist} from "@/api/houtai/siyu/gongying";
import {addlist, getlistb} from "@/api/houtai/siyu/xuqiu";
import {
  getPrivateduct,
} from "@/api/system/privateduct";

export default {
  name: 'add',
  components: {},
  data() {
    return {
      dialogVisible: false,
      centerDialogVisible: false,
      pinfoview: false,
      pinfoindex: 1,
      pinfocontent: [],
      tableData: [],
      ruleForm: {
        title: '',
        supplierIds: [],
        startTime: '',
        endTime: '',
        remark: '',
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now(); // 禁用过去的时间
        }
      },
      jztime:3600, //截止倒计时
      jztimeb: '1',
      jztimes:[{
          value: 3600,
          label: '1小时'
        },{
          value: 7200,
          label: '2小时'
        }, {
          value: 10800,
          label: '3小时'
        }, {
          value: 14400,
          label: '4小时'
        }, {
          value: 18000,
          label: '5小时'
        }],
      rules: {
        title: [
          {required: true, message: '请输入标题', trigger: 'blur'},
        ],
        supplierIds: [
          {required: true, message: '请选择供应商', trigger: 'blur'}
        ],
        startTime: [
          {required: true, message: '请选择开始时间', trigger: 'change'}
        ],
        endTime: [
          {required: true, message: '请选择结束时间', trigger: 'change'}
        ],
      },
      up_file: '',
      headers: {},
      loading: false,
      imgfile: '',
      centerDialogVisible: false,
      gongyinglist: [],
      chanpinlist: [],
      tableList: [],
      uptable: [],
      options: ['吨', '千克', '个', '件', '套','米','支'],
    }
  },
  created() {
    this.up_file = process.env.VUE_APP_BASE_API;
    let token = Cookies.get("Admin-Token");
    this.headers = {
      Authorization: "Bearer " + token,
    };
    this.ready();
  },
  methods: {
    ready() {
      getlist({
        pageNum: 1,
        pageSize: 999,
        status: 1
      }).then((res) => {
        let lista = res.rows;
        this.gongyinglist = lista;
      });
      getlistb({
        pageNum: 1,
        pageSize: 999,
      }).then((res) => {
        let lista = res.rows;
        this.chanpinlist = lista;
      });


    },

    visibleChange(e) {
      if (!e) {
        let listm = [];
        this.ruleForm.products.forEach((item, index) => {
          this.chanpinlist.forEach((itema, indexa) => {
            if (itema.id == item) {
              let lista = itema;
              lista['numa'] = 1;
              listm.push(lista);
            }
          })
        });
        this.tableList = listm;

        listm.forEach((item,index)=>{
          this.getxqa(item.id)
        })

      }
    },
    //获取供应详情
    getxqa(id){
      getPrivateduct(id).then((response) => {
        let lista = response.data;
        let listb = [];
        lista.suppliers.forEach((item,index)=>{
          listb.push(item['supplierId']);
        })
        lista['suppliersb'] = listb;
        this.fuzhiGy(lista.suppliersb);
        // suppliersb
        // lista['supplierIds'] =  listb;
        // this.form = lista;
        // this.open = true;
        // this.title = "修改私域产品";
      });
    },
    //赋值供应
    fuzhiGy(rowlist){
      let lista = this.ruleForm.supplierIds;
      rowlist.forEach((item, index) => {
        lista.push(item)
      });


      for (let i = 0; i < lista.length; i++) {
        for (let j = i + 1; j < lista.length; j++) {
          if (lista[i] === lista[j]) {
            lista.splice(j, 1);
            j--; // 调整内层循环的索引，使其不会漏掉下一个元素
          }
        }
      }

      this.ruleForm.supplierIds = lista;
      // {{ruleForm.supplierIds}}

    },
    xq() {
    },
    handleDelete(e, b) {
      b.id
      let numa;
      this.ruleForm.products.forEach((item, index) => {
        if (item == b.id) {
          numa = index;
        }
      })
      this.ruleForm.products.splice(numa, 1);
      this.tableList.splice(e, 1);
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.add();
        } else {
          return false;
        }
      });
    },
    add() {
      let that = this;
      // let parentId =  this.ruleForm.parentIdb;
      let lista = [];
      this.tableList.forEach((item, index) => [
        lista.push({
          'productName': item.productName,
          'productId': item.id,
          'count': item.numa,
          'price': item.price,
          'unit': item.unit,
        })
      ])
      var rawdate = this.stringToDate(this.ruleForm.endTime);
      // 日期加上11秒
      var addSecond = this.jztimeb * 3600;
      var resDate = new Date(rawdate.setSeconds(rawdate.getSeconds() + addSecond));
      var restime = this.format(resDate, "yyyy-MM-dd hh:mm:ss");


      let datalist = {
        endTime: Date.parse(this.ruleForm.endTime),
        //endTime: restime,
        remark: this.ruleForm.remark,
        startTime: this.ruleForm.startTime,
        supplierIds: this.ruleForm.supplierIds,
        title: this.ruleForm.title,
        products: lista,
        supplyEndTime:Date.parse(restime)
      }

      // return false;


      addlist(datalist).then((res) => {
        this.$message({
          message: '提交成功',
          type: 'success'
        });
        this.dialogVisible = false;
        this.jztime=3600;
        this.$emit('shuaxin')
      //   this.ruleForm={
      //   title: '',
      //   supplierIds: '',
      //   startTime: '',
      //   endTime: '',
      //   remark: '',
      // }
      // this.gongyinglist= []
      // this.chanpinlist=[]
      });
    },
    // js字符串转日期Date
// 字符串格式：2020-10-13 12:00:01
 stringToDate(strDate) {
    var tempStrs = strDate.split(" ");

    var dateStrs = tempStrs[0].split("-");
    var year = parseInt(dateStrs[0], 10);
    var month = parseInt(dateStrs[1], 10) - 1;
    var day = parseInt(dateStrs[2], 10);

    // var timeStrs = tempStrs[1].split(":");
    var hour =  '00';
    var minute =  '00';
    var second =  '00';

    var date = new Date(year, month, day, hour, minute, second);
    return date;
},

// js日期Date格式化为字符串
// 字符串格式：2020-10-13 12:00:01
 format(date, fmt) {
    var o = {
        "M+": date.getMonth() + 1, //月份
        "d+": date.getDate(), //日
        "h+": date.getHours(), //小时
        "m+": date.getMinutes(), //分
        "s+": date.getSeconds(), //秒
        "q+": Math.floor((date.getMonth() + 3) / 3), //季度
        "S": date.getMilliseconds() //毫秒
    };
    if (/(y+)/.test(fmt)) {
        fmt = fmt.replace(RegExp.$1, (date.getFullYear() + "").substr(4 - RegExp.$1.length));
    }
    for (var k in o) {
        if (new RegExp("(" + k + ")").test(fmt)) {
            fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ?
                (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
        }
    }
    return fmt;
},
    showmodel() {
      this.ready();
      this.dialogVisible = true;
    },
    handleChange(value) {
    },
    huoquxq(row){
      this.pinfocontent = row;
      this.pinfoview = true;
      this.pinfoindex = 1;
    },
    jztimebInput(value){
      if(value<0) this.jztimeb = 0 - value
    },
    numaInput(index,e){
      const value = e.currentTarget.value
      const item = {...this.tableList[index]}
      if(value<0){
        item.numa = 0 -value
        this.$set(this.tableList,index,item)
      }
    }
  }
}
</script>

<style scoped>
.input1 {
  height: 30px;
  line-height: 30px;
  border: 1px solid #b4bccc;
  border-radius: 5px;
  padding-left: 10px;
  outline: none;
}

.pinfobox{
  width: 100%;
  height: 450px;
  overflow: hidden;
}

.pinfobox1{
  width: 100%;
  height: 50px;
  display: flex;
  background: #F2F2F8;
  box-shadow: 0px 1px 0px 0px rgba(0,0,0,0.15);
}
.pinfobox1>div{
  width: 160px;
  height: 50px;
  line-height: 50px;
  text-align: center;
  cursor: pointer;
  transition: 0.2s;
}
.pinfobox1sel{
  background: #FFFFFF;
  border: 1px solid #F2F2F8;
  color: #2E73F3;
  transition: 0.2s;
}
.pinfobox2{
  width: 100%;
  height: 290px;
  display: flex;
  margin-top: 30px;
  flex-wrap: wrap;
}

.pinfobox2-2{
  height: 100%;
  margin-left: 20px;
}
.pinfobox2-2-1{
  display: flex;
  border-bottom:1px solid #CBD6E2;
  border-left:1px solid #CBD6E2;
  border-right:1px solid #CBD6E2;
}

.pinfobox2-2-1>div:first-child{
  width: 135px;
  height: 50px;
  text-align: center;
  line-height: 50px;
  background-color: #EFEFF3;
}

.pinfobox2-2-1>div:last-child{
  width: 480px;
  height: 50px;
  text-align: center;
  line-height: 50px;
  text-indent: 50px;
}

.pinfobox2-3{
  width: 100%;
  height: 50px;
  margin-top:20px;
  background: #F8F9FB;
  opacity: 1;
  border: 1px solid #CBD6E2;
}
.pinfobox2-3>div {
  width: 110px;
  height: 100%;
  background: #9FA3B2;
  opacity: 1;
  text-align: center;
  line-height: 50px;
  color: #FFFFFF;
  font-size: 16px;
}
</style>
