<template>
  <div class="bodya">
    <el-row>
      <el-col :span="4">
        <el-input v-model="queryParams.category" size="small" placeholder="请输入名称"></el-input>
      </el-col>
      <el-col :span="4">
        <el-select clearable size="small" style="width: 100%" v-model="queryParams.status" placeholder="请选择类型">
          <el-option
            v-for="item in audit_status"
            :key="item.dictValue"
            :label="item.dictLabel"
            :value="item.dictValue">
          </el-option>
        </el-select>
      </el-col>
      <el-col :span="8">
        <el-button size="small" @click="ready()" style="margin-left: 10px;" type="primary">
          搜索
        </el-button>
        <el-button size="small" @click="cz()" style="margin-left: 10px;" type="">
          重置
        </el-button>
      </el-col>
    </el-row>


    <el-table :data="tableData" border size="small" style="width: 100%;margin-top: 10px;">
      <el-table-column
        align="center"
        label="#"
        type="index">
      </el-table-column>
      <el-table-column prop="title" label="采购需求名称" align="center" show-overflow-tooltip></el-table-column>
      <el-table-column prop="supplierName" label="供应商名称" align="center" show-overflow-tooltip></el-table-column>
      <el-table-column prop="status" label="状态" align="center" show-overflow-tooltip>
        <template slot-scope="scope">
          {{ scope.row.status == 0 ? '未完成' : '' }}
          {{ scope.row.status == 1 ? '已完成' : '' }}
        </template>
      </el-table-column>

      <el-table-column prop="amount" label="成交金额" align="center" show-overflow-tooltip></el-table-column>
      <!-- <el-table-column prop="budget" label="预算金额" align="center" show-overflow-tooltip></el-table-column> -->
      <el-table-column prop="createTime" label="查看产品" align="center" show-overflow-tooltip>
        <template slot-scope="scope">
          <el-button size="mini" type="text" @click="zhanshi(scope.row)">
            查看产品
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="查看合同" align="center" show-overflow-tooltip>
        <template slot-scope="scope">
          <el-button v-if="!scope.row.contractNum" size="mini" type="text" @click="ht(scope.row)">
            生成合同
          </el-button>
          <el-button v-else size="mini" type="text" @click="ckht(scope.row)">
            查看合同
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" align="center" show-overflow-tooltip></el-table-column>

    </el-table>


    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="ready"/>
    <el-dialog
      title="提示"
      :visible.sync="dialogVisible"
      width="30%">
      <el-table
        :data="onedata"
        style="width: 100%">
        <el-table-column
          prop="productName"
          align="center"
          label="名称">
        </el-table-column>
        <el-table-column
          prop="count"
          align="center"
          label="数量">
        </el-table-column>
        <el-table-column
          prop="unit"
          align="center"
          label="单位">
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
      <el-button size="mini" @click="dialogVisible = false">关 闭</el-button>
    </span>
    </el-dialog>


    <!-- 预览合同 -->
    <el-dialog
      :visible.sync="htview"
      width="50%"
      style="text-align: center;"
     >
      <img :src="hturl">
    </el-dialog>


  </div>
</template>

<script>
import {getlist, editlist,getcontract} from "@/api/houtai/siyu/caigou";

export default {
  name: 'index',
  data() {
    return {
      htview:false,
      hturl:"",
      tableData: [],
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        category: '',
        status: '',
      },
      audit_status: [],
      listClass: '',
      onedata: [],

      dialogVisible: false,


    };
  },
  created() {
    this.getDicts("audit_status").then((response) => {
      this.audit_status = response.data;
    });
    this.ready();
  },
  methods: {
    ready() {
      getlist(this.queryParams).then((res) => {
        let lista = res.rows;
        lista.forEach((item, index) => {
          let bb = this.shaixuanb(this.audit_status, item.status, 'dictValue', 'dictLabel');
          lista[index]['statusa'] = bb;
        })
        this.tableData = lista;
        this.total = res.total;
      });
    },
    ht(row){
      this.fullscreenLoading = true;
      let that=this;
      getcontract(row.id).then((res) => {
        if(res.code==200)
        {
          this.$message.success("合同创建完成");
          that.ready();
          that.fullscreenLoading = false;
        }
      });
    },
    ckht(row){
      var x="https://www.ziyouke.net/prod-api/profile/contract/"+row.contractNum+".png";
      this.hturl=x;
      this.htview=true;
    },
    cz() {
      this.queryParams.category = '';
      this.queryParams.status = '';
      this.ready();
    },
    zhanshi(row) {
      this.onedata = JSON.parse(row.products);
      this.dialogVisible = true;

    },
    shaixuan(data, value, name, retname, indexa) {
      for (let i = 0; i < data.length; i++) {
        let item = data[i];
        if (item[name] == value) {
          return item[retname];
        }
      }
    },
    shaixuanb(data, value, name, retname, indexa) {
      for (let i = 0; i < data.length; i++) {
        let item = data[i];
        if (item[name] == value) {
          return item['listClass'];
        }
      }
    },

    tongguo(row) {
      this.$confirm('是否通过[' + row.name + ']审核?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        //  通过
        let datalist = {
          id: row.id,
          status: 1,
        }
        sh(datalist).then((res) => {
          this.$message({
            type: 'success',
            message: '操作成功!'
          });
          this.ready();
        });
      }).catch(() => {
      });
    },
    jujue(row) {
      this.$confirm('是否驳回[' + row.name + ']审核?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$prompt('请输入驳回原因', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
        }).then(({value}) => {
          if (!value) {
            this.$message({
              type: 'info',
              message: '请输入驳回原因'
            });
            return;
          }
          //驳回
          let datalist = {
            id: row.id,
            status: 2,
            reason: value,
          }
          sh(datalist).then((res) => {
            this.$message({
              type: 'success',
              message: '操作成功!'
            });
            this.ready();
          });
        }).catch(() => {
        });
      }).catch(() => {
      });
    }


  }
}
</script>

<style scoped>
.bodya {
  padding: 20px;
}

.lab1 {
  text-align: right;
  height: 30px;
  line-height: 30px;
  padding-right: 10px;
}
</style>
