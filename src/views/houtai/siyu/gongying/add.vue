<template>
  <div>

    <el-dialog
      title="新增私域供应商"
      :visible.sync="dialogVisible"
      v-loading="loading"
      width="60%">
      <div>
        <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="150px" class="demo-ruleForm">
          <el-form-item label="公司名称" prop="name">
            <el-input v-model="ruleForm.name"></el-input>
          </el-form-item>
<!--          <el-form-item label="公司电话" prop="phone">-->
<!--            <el-input v-model="ruleForm.phone"></el-input>-->
<!--          </el-form-item>-->
          <el-form-item label="联系人选项">
            <el-button type="primary" @click="xzaa()">新增联系人</el-button>
              （已经添加{{ datalist.length}}个联系人）
          </el-form-item>
          <template v-for="(item, index ) in datalist">
            <el-row>
            <el-col :span="12">
              <el-form-item :label="'第'+ (index + 1) +'联系人姓名'" prop="people1_name">
                <el-input v-model="item.nickName" placeholder="请输入姓名" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="'第'+ (index + 1) +'联系人电话'" prop="people1_phone">
                <el-input v-model="item.phone" placeholder="请输入电话" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="'第'+ (index + 1) +'联系人职务'" prop="people1_zw">
                <el-input v-model="item.post" placeholder="请输入职务" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="'第'+ (index + 1) +'联系人选项'" prop="people1_zw">
                <el-button type="info" @click="delc(index)" size="mini">
                   去除该联系人选项
                </el-button>
              </el-form-item>
            </el-col>
          </el-row>
          </template>
        
          <el-row>
        </el-row>
<!--        <el-row>-->
<!--          <el-col :span="8">-->
<!--            <el-form-item label="第二联系人" prop="people2_name">-->
<!--              <el-input v-model="ruleForm.people2_name" placeholder="请输入姓名" />-->
<!--            </el-form-item>-->
<!--          </el-col>-->
<!--          <el-col :span="8">-->
<!--            <el-form-item label="电话" prop="people2_phone">-->
<!--              <el-input v-model="ruleForm.people2_phone" placeholder="请输入电话" />-->
<!--            </el-form-item>-->
<!--          </el-col>-->
<!--          <el-col :span="8">-->
<!--            <el-form-item label="职务" prop="people2_zw">-->
<!--              <el-input v-model="ruleForm.people2_zw" placeholder="请输入职务" />-->
<!--            </el-form-item>-->
<!--          </el-col>-->
<!--        </el-row>-->
<!--          <el-form-item label="经度" prop="lng">-->
<!--            <el-input v-model="ruleForm.lng"></el-input>-->
<!--          </el-form-item>-->
<!--          <el-form-item label="纬度" prop="lat">-->
<!--            <el-input v-model="ruleForm.lat"></el-input>-->
<!--          </el-form-item>-->
{{ ruleForm.productIds }}

<!-- getchanpin -->
<el-form-item label="私域产品" prop="productIds">
<treeselect
              multiple
                style="width:95%"
                v-model="ruleForm.productIds"
                :options="chanpinlist"
                :normalizer="normalizer"
                placeholder="请选择上级品类"
              >
              <div slot="option-label" slot-scope="{ node }" class="category-flex">
                <span>{{ node.label }}</span>
                <span class="category-flex-desc">{{ node.raw.model ? `规格型号：${node.raw.model}` : '' }}</span>
              </div>
            </treeselect>
            </el-form-item>


<!--  -->
          <el-form-item label="资质文件" prop="certificationUrl">
            <!--            <el-input v-model="ruleForm.certificationUrl"></el-input>-->

            <el-upload
              :disabled="ruleForm.certificationUrl.len供应商类型gth == 1 ? true : false"
              class="upload-demo"
              :action="up_file + '/common/upload'"
              name="file"
              :before-upload="handleBeforeUpload"
              :on-progress="upfile"
              :on-success="handcgb"
              :show-file-list="false"
              :headers="headers"
              multiple>
              <el-button :disabled="ruleForm.certificationUrl.length == 1 ? true : false" size="small" type="primary">
                点击上传
              </el-button>
            </el-upload>
            <div class="" v-for="(item,index) in ruleForm.certificationUrl">
                <span @click="show_img(item.filePath)" style="cursor:pointer;">
                  {{ item.name }}
                </span>
              <el-button type="text" @click="delb(index)">删除</el-button>
            </div>


          </el-form-item>
          <el-form-item label="公司地址" prop="address">
            <el-input v-model="ruleForm.address"></el-input>
          </el-form-item>
          <el-form-item label="logo图片" prop="logoUrl">
            <el-upload
              :disabled="ruleForm.logoUrl.length == 1 ? true : false"
              class="upload-demo"
              :action="up_file + '/common/upload'"
              name="file"
              :before-upload="handleBeforeUpload"
              :on-progress="upfile"
              :on-success="handcg"
              :show-file-list="false"
              :headers="headers"
              multiple>
              <el-button :disabled="ruleForm.logoUrl.length == 1 ? true : false" size="small" type="primary">
                点击上传
              </el-button>
            </el-upload>
            <div class="" v-for="(item,index) in ruleForm.logoUrl">
                <span @click="show_img(item.filePath)" style="cursor:pointer;">
                  {{ item.name }}
                </span>
              <el-button type="text" @click="dela(index)">删除</el-button>
            </div>
            <!--            <el-input v-model="ruleForm.photoAddr"></el-input>-->
          </el-form-item>



          <el-form-item label="公司标语">
            <el-input v-model="ruleForm.slogan"></el-input>
          </el-form-item>
          <el-form-item label="场地图片" >
            <image-upload v-model="ruleForm.siteImg"/>
          </el-form-item>
          <el-form-item label="荣誉证书" >
            <image-upload v-model="ruleForm.certImg"/>
          </el-form-item>
          <el-form-item label="设备图片" >
            <image-upload v-model="ruleForm.deviceImg"/>
          </el-form-item>
          <el-form-item label="工艺图片/视频" >
            <image-upload v-model="ruleForm.process"/>
          </el-form-item>



          <el-form-item>
            <el-button type="primary" @click="submitForm('ruleForm')">立即创建</el-button>
          </el-form-item>
        </el-form>
      </div>


      <!--      <span slot="footer" class="dialog-footer">-->
      <!--    <el-button @click="dialogVisible = false">取 消</el-button>-->
      <!--    <el-button type="primary" @click="dialogVisible = false">确 定</el-button>-->
      <!--  </span>-->
    </el-dialog>


    <el-dialog v-dialogDragBox title="" :visible.sync="centerDialogVisible" width="50%" center>
      <img :src="up_file+imgfile" style="width: 100%;height: 100%" alt=""/>
      <span slot="footer" class="dialog-footer">
        <el-button @click="centerDialogVisible = false">取 消</el-button>
      </span>
    </el-dialog>


  </div>
</template>

<script>
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

import Cookies from "js-cookie";
import {addlist,getchanpin} from "@/api/houtai/siyu/gongying";
import { getToken } from "@/utils/auth";

export default {
  name: 'add',
  components:{
    Treeselect
  },
  data() {
    return {
      dialogVisible: false,
      tableData: [],
      datalist: [{
        nickName: '',
        phone: '',
        post: '',
        uuid: '',
      }],
      ruleForm: {
        name: '',
        phone: '',
        logoUrl: [],
        lat: '',
        lng: '',
        certificationUrl: [],
        categoryIds: [],
        address: '',
        slogan: '',
        siteImg: '',
        certImg: '',
        deviceImg: '',
        process: '',
        people1_name:"",
        people1_phone:"",
        people1_zw:"",
        people2_name:"",
        people2_phone:"",
        people2_zw:"",


        productIds: [],
      },
      rules: {
        name: [
          {required: true, message: '请输入类目名称', trigger: 'blur'},
        ],
        phone: [
          {required: true, message: '请输入联系人电话', trigger: 'blur'}
        ],
        // logoUrl: [
        //   { required: false, message: '请输入联系人电话', trigger: 'change' }
        // ],
        certificationUrl: [
          {required: true, message: '请上传资质文件', trigger: 'change'}
        ],
        categoryIds: [
          {required: true, message: '请选择供应商类型', trigger: 'change'}
        ],
        address: [
          {required: true, message: '请输入地址', trigger: 'blur'}
        ],
      },
      up_file: '',
      headers: {},
      loading: false,
      imgfile: '',
      centerDialogVisible: false,

      chanpinlist: [],
    }
  },
  created() {
    this.up_file = process.env.VUE_APP_BASE_API;
    let token = Cookies.get("Admin-Token");
    this.headers = {
      Authorization: "Bearer " + token,
    };
    this.ready();
    this.chanpin();
  },
  methods: {
    normalizer(node) {
      if (node.label === 'unknown') {
        return '' // 返回空字符串或其他你想要显示的文本
      }
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.productName + '-' + node.materialQuality + '-' + node.model,
        model: node.model,
        children: node.children,
      };
    },
    chanpin(){
      getchanpin({}).then((res) => {  
        this.chanpinlist = res.rows;
    
      });


    },
    delc(e){
      this.datalist.splice(e,1)
    },

    reTreeValue(value) {
      return value?.split(',') || undefined
    },
    ready() {
    },
    upfile() {
      this.loading = true;
    },
    show_img(e) {
      if (e) {
        this.imgfile = e;
        this.centerDialogVisible = true;
      }

    },
    xzaa(){
      this.datalist.push({
        nickName: '',
        phone: '',
        post: '',
        uuid: '',
      })
      this.$forceUpdate();
    },
    handleBeforeUpload(file) {
      if (!getToken()) {
        this.$message.error(`请先登录`);
        return false
      }
    },
    //上传成功
    handcg(file, fileList) {
      this.ruleForm.logoUrl.push({
        name: file.newFileName,
        filePath: file.fileName,
      });
      this.loading = false;
    },
    //删除上传文件
    dela(e) {
      this.ruleForm.logoUrl.splice(e, 1);
    },

    handcgb(file, fileList) {
      if (file.code == 500) {
        this.$message.error('请上传正确文件');
        this.loading = false;
        return;
      }

      this.ruleForm.certificationUrl.push({
        name: file.newFileName,
        filePath: file.fileName,
      });
      this.loading = false;
    },
    //删除上传文件
    delb(e) {
      this.ruleForm.certificationUrl.splice(e, 1);
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.add();
        } else {
          return false;
        }
      });
    },
    add() {
      let that = this;
      // let parentId =  this.ruleForm.parentIdb;

      let datalist = {
        name: this.ruleForm.name,
        phone: this.ruleForm.phone,
        lat: this.ruleForm.lat,
        lng: this.ruleForm.lng,
        certificationUrl: this.ruleForm.certificationUrl[0]['filePath'],
        // categoryIds:  this.ruleForm.categoryIds,
        address: this.ruleForm.address,
        logoUrl: (this.ruleForm.logoUrl.length == 0 ? '' : this.ruleForm.logoUrl[0]['filePath']),

        slogan: this.ruleForm.slogan,
        siteImg: this.ruleForm.siteImg,
        certImg: this.ruleForm.certImg,
        deviceImg: this.ruleForm.deviceImg,
        process: this.ruleForm.process,
        contacts: this.datalist,
        productIds: this.ruleForm.productIds,
      }
      

      addlist(datalist).then((res) => {
        this.$message({
          message: '提交成功',
          type: 'success'
        });
        this.dialogVisible = false;
        this.$emit('shuaxinb')
      });
    },
    showmodel() {
      this.ready();
      this.dialogVisible = true;
    },
    handleChange(value) {
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep {
  .category-flex {
    display: flex;
    align-items: center;
    &-desc {
      font-size: 12px;
      color: #999999;
      margin-left: 10px;
    }
  }
}
</style>
