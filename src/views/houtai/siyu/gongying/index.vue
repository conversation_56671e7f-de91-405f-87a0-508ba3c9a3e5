<template>
  <div class="app-container bgcf9 vh-85">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" @submit.native.prevent>
      <el-form-item label="" prop="keyword">
        <el-input v-model="queryParams.keyword" placeholder="请输入供应商名称或地址或联系人" style="width: 250px" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
        <el-button type="primary" icon="el-icon-plus" size="small" @click="handleAdd">新增</el-button>
      </el-form-item>
    </el-form>

    <!-- 收藏管理 -->
    <collect-tpl ref="collect" type="UserPriSupplier" :storeId.sync="storeId" :collectList.sync="collectList" @getList="getList" @colletSubmit="colletSubmit" :isSearch="true" :search="true" :showSearch.sync="showSearch" @queryTable="getList" :columns="columns" />

    <template v-if="dataList.length">
      <el-table ref="table" v-loading="loading" :data="dataList" border row-key="id" @selection-change="handleSelectionChange" size="small" :key="key" :class="{ drop: isPC() }">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column type="expand" width="30">
          <template slot-scope="props">
            <div class="table-expand">
              <el-table :data="props.row.contactList" :key="'list' + key" style="width: 100%" class="custom-table custom-table-cell10">
                <el-table-column align="center" type="index" label="序号"></el-table-column>
                <el-table-column align="center" prop="nickName" label="联系人"></el-table-column>
                <el-table-column align="center" prop="phone" label="电话"></el-table-column>
                <el-table-column align="center" prop="post" label="职务"></el-table-column>
              </el-table>
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" label="序号" type="index" v-if="columns[0].visible" />
        <el-table-column label="供应商名称" align="center" prop="name" show-overflow-tooltip v-if="columns[1].visible">
          <template slot-scope="{ row }">
            <span class="table-link" @click="handleCompanyView(row)">{{ row.name }}</span>
          </template>
        </el-table-column>
        <el-table-column label="联系人" align="center" prop="nickName" v-if="columns[2].visible" width="120">
          <template slot-scope="{ row }">
            {{ returnContactList(row, 'nickName') }}
          </template>
        </el-table-column>
        <el-table-column label="电话" align="center" prop="phone" v-if="columns[3].visible" width="120">
          <template slot-scope="{ row }">
            {{ returnContactList(row, 'phone') }}
          </template>
        </el-table-column>
        <el-table-column label="职务" align="center" prop="post" v-if="columns[4].visible" width="120">
          <template slot-scope="{ row }">
            {{ returnContactList(row, 'post') }}
          </template>
        </el-table-column>
        <el-table-column label="logo" align="center" prop="logoUrl" v-if="columns[5].visible" width="120">
          <template slot-scope="{ row }">
            <image-preview :src="imgPath + row.logoUrl" :width="50" :height="50" />
          </template>
        </el-table-column>
        <el-table-column label="资质文件" align="center" prop="certificationUrl" v-if="columns[6].visible" width="120">
          <template slot-scope="{ row }">
            <image-preview :src="imgPath + row.certificationUrl" :width="50" :height="50" />
          </template>
        </el-table-column>
        <el-table-column label="地址" align="center" prop="address" v-if="columns[7].visible">
          <template slot-scope="{ row }">{{ removeHtmlTag(row.address, 300) }}</template>
        </el-table-column>
        <el-table-column align="center" label="操作" width="190">
          <template slot-scope="{ row }">
            <el-button type="text" size="mini" icon="el-icon-view" @click="handleDetail(row)">详情</el-button>
            <el-button type="text" size="mini" icon="el-icon-delete" @click="handleDelete(row)">删除</el-button>
            <el-button type="text" size="mini" icon="el-icon-paperclip" @click="handleCollet(row)">收藏到</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="custom-pagination">
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
      </div>
    </template>
    <el-empty :image-size="200" v-else />

    <!-- 批量收藏 -->
    <template v-if="!multiple">
      <div class="mark" v-if="isCollect" @click="isCollect = !isCollect"></div>
      <div class="collectAll">
        <div class="collectAll-box">
          <div class="collectAll-title">已选择 {{ ids.length }} 项</div>
          <el-tooltip content="收藏到其他收藏夹" placement="top" effect="dark">
            <div class="collectAll-btn">
              <span :class="{ active: isCollect }" @click="isCollect = !isCollect">收藏到</span>
            </div>
          </el-tooltip>
          <el-tooltip content="退出" placement="top" effect="dark">
            <div class="collectAll-close" @click="handleClose">
              <i class="el-icon-close"></i>
            </div>
          </el-tooltip>
        </div>
      </div>
      <el-collapse-transition>
        <div class="collectAll-list-box" v-if="isCollect">
          <div class="collectAll-list-close">
            <span>请选择收藏夹</span>
            <i class="el-icon-close" @click="isCollect = !isCollect"></i>
          </div>
          <div class="collectAll-list">
            <div class="item" :class="{ disabled: item.storeId === storeId }" v-for="item in collectList" :key="item.storeId" @click="handleColletAll(item)">
              {{ item.dirName }}
            </div>
          </div>
        </div>
      </el-collapse-transition>
    </template>

    <!-- 新增/修改 -->
    <create-dialog ref="create"></create-dialog>
  </div>
</template>

<script>
import Sortable from 'sortablejs'
import { getlistb, shoucTo, delshouc, shoucGo } from '@/api/houtai/shoucang'
import { privateSupb } from '@/api/houtai/siyu/gongying'
import CollectTpl from '@/views/components/collect'
import createDialog from '@/views/houtai/siyu/gongying/create'
import { removeHtmlTag } from '@/utils'

export default {
  name: 'MySupplier',
  components: { CollectTpl, createDialog },
  data() {
    return {
      key: 1,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 列表数据
      dataList: [],
      // 总条数
      total: 0,
      // 显示搜索条件
      showSearch: true,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        keyword: undefined,
        storeId: undefined
      },
      // 列信息
      columns: [
        { key: 0, label: `序号`, visible: true },
        { key: 1, label: `供应商名称`, visible: true },
        { key: 2, label: `联系人`, visible: true },
        { key: 3, label: `电话`, visible: true },
        { key: 4, label: `职务`, visible: true },
        { key: 5, label: `公司标语`, visible: true },
        { key: 6, label: `logo`, visible: true },
        { key: 7, label: `资质文件`, visible: true },
        { key: 8, label: `地址`, visible: true }
      ],
      // 是否管理员
      admin: false,
      // 收藏列表ID
      storeId: undefined,
      sortable: null,
      // 收藏夹列表
      collectList: undefined,
      // 是否显示收藏夹列表
      isCollect: false
    }
  },
  created() {
    this.admin = localStorage.getItem('admin')
  },
  methods: {
    removeHtmlTag,
    // 判断当前设备是电脑、平板还是手机
    isPC() {
      let userAgentInfo = navigator.userAgent
      let Agents = ['Android', 'iPhone', 'SymbianOS', 'Windows Phone', 'iPad', 'iPod']
      let flag = true
      for (let v = 0; v < Agents.length; v++) {
        if (userAgentInfo.indexOf(Agents[v]) > 0) {
          flag = false
          break
        }
      }
      return flag
    },
    // 列表
    async getList() {
      this.loading = true
      if (this.storeId) {
        this.queryParams.storeId = this.storeId
        let { code, msg, rows, total } = await getlistb(this.queryParams)
        if (code === 200) {
          this.dataList = rows
          this.key = Math.random()
          this.total = total
          this.loading = false
          if (this.isPC()) {
            this.$nextTick(() => {
              this.rowDrop()
            })
          }
        } else {
          this.$modal.msgError(msg)
        }
      } else {
        this.loading = false
      }
    },
    // 返回联系人信息
    returnContactList(row, val) {
      let index = row.contactList.findIndex(item => item.checked)
      if (index === -1) index = 0
      return row.contactList[index][val]
    },
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 重置搜索
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 新增
    handleAdd() {
      this.$refs.create.handleAdd('新增私域供应商')
    },
    // 查看公司详情
    handleCompanyView(row) {
      const { name } = row
      this.$refs.create.handleSearch(1, name)
    },
    // 详情
    handleDetail(row) {
      privateSupb({ id: row.id }).then(res => {
        if (res.code === 200) {
          this.$refs.create.handleDetail(res.data, '私域供应商详情')
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length != 1
      this.multiple = !selection.length
    },
    //行拖拽
    rowDrop() {
      const _this = this
      if (_this.dataList.length) {
        const tbody = document.querySelector('.el-table__body-wrapper tbody')
        this.sortable = Sortable.create(tbody, {
          ghostClass: 'sortable',
          animation: 180,
          delay: 0,
          onEnd({ newIndex, oldIndex }) {
            if (newIndex > oldIndex) {
              _this.handleMoveDown(newIndex, oldIndex)
            }
            if (newIndex < oldIndex) {
              _this.handleMoveUp(newIndex, oldIndex)
            }
          }
        })
      }
    },
    // 上移
    handleMoveUp(newIndex, oldIndex) {
      const valueId = this.dataList[oldIndex].id
      shoucGo({ index: newIndex, storeId: this.storeId, valueId }).then(res => {
        const oldItem = this.dataList[oldIndex]
        this.dataList.splice(oldIndex, 1)
        this.dataList.splice(newIndex, 0, oldItem)
      })
    },
    // 下移
    handleMoveDown(newIndex, oldIndex) {
      const valueId = this.dataList[oldIndex].id
      shoucGo({ index: newIndex, storeId: this.storeId, valueId }).then(res => {
        const oldItem = this.dataList[oldIndex]
        this.dataList.splice(oldIndex, 1)
        this.dataList.splice(newIndex, 0, oldItem)
      })
    },
    // 单个收藏
    handleCollet(item) {
      this.$refs.collect.handleChange(item.id)
    },
    // 批量收藏
    handleColletAll(item) {
      const storeId = item.storeId
      const valueIdList = this.ids
      if (storeId === this.storeId) return
      shoucTo({ storeId, valueIdList }).then(res => {
        this.$modal.msgSuccess('操作成功')
        this.isCollect = false
        this.multiple = false
        this.ids = []
        this.$refs.table.clearSelection()
      })
    },
    // 提交收藏
    colletSubmit(storeId, data) {
      if (!Array.isArray(data)) data = [data]
      shoucTo({ storeId, valueIdList: data }).then(res => {
        this.getList()
        this.$modal.msgSuccess('操作成功')
      })
    },
    // 删除
    // prettier-ignore
    handleDelete(item) {
      const data = { storeId: this.storeId, valueId: item.id }
      this.$modal.confirm('是否确认删除选中的收藏？').then(function () {
        return delshouc(data)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess('删除成功')
      }).catch(() => {
      })
    },
    // 关闭多选
    handleClose() {
      this.isCollect = false
      this.multiple = false
      this.ids = []
      this.$refs.table.clearSelection()
    }
  }
}
</script>

<style>
.sortable {
  opacity: 0.8;
  color: #fff !important;
  background: #409eff !important;
}
</style>
<style lang="scss" scoped>
.table-link {
  color: #409eff;
  &:hover {
    text-decoration: underline;
  }
}
::v-deep .table-badge {
  .cell {
    overflow: inherit;
  }
}
.price {
  font-size: 16px;
  padding-right: 5px;
  cursor: pointer;
}
.max {
  color: #ec2454;
}
.min {
  color: #67c23a;
}
.avg {
  color: #409eff;
}
.drop {
  ::v-deep .el-table__row {
    position: relative;
    &:hover {
      cursor: move;
      &:after {
        transition: all 0.3s;
        content: '按住可进行拖拽排序';
        position: absolute;
        left: 50%;
        top: -40px;
        z-index: 200;
        background-color: #303133;
        border-radius: 4px;
        padding: 10px;
        font-size: 12px;
        min-width: 10px;
        word-wrap: break-word;
        color: #fff;
      }
    }
    .zindex {
      z-index: 2;
    }
  }
}
.tipShow {
  display: none;
  background-color: #303133;
  color: #fff;
  position: fixed;
  z-index: 1;
  border-radius: 4px;
  padding: 10px;
  // z-index: 2000;
  font-size: 12px;
  line-height: 1.2;
  min-width: 10px;
  word-wrap: break-word;
}
::v-deep {
  .table-expand {
    padding-left: 80px;
    padding-bottom: 10px;
    margin: -13px 0;
    background-color: #eceef1;
  }
  .el-table__row.expanded {
    background-color: #eceef1;
    td.el-table__cell {
      background-color: #eceef1;
    }
  }
}
</style>
