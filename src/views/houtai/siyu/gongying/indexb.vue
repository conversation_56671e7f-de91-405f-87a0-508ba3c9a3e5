<template>
  <div class="newBox bgcf9 vh-85">
    <div class="custom-search flex">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" @submit.native.prevent>
        <el-form-item label="" prop="keyword">
          <el-input v-model="queryParams.keyword" placeholder="请输入供应商名称或地址或联系人" style="width: 250px" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
          <el-button type="primary" size="small" icon="el-icon-plus" @click="handleAdd">新增</el-button>
          <el-select v-model="queryParams.tipRuleType" placeholder="请选择供应商类型" style="margin-left: 10px" @change="handleQuery">
            <el-option label="所有供应商" value=""></el-option>
            <el-option v-for="item in tipRuleTypeOptions" :key="item.tipType" :label="item.tipName + '供应商'" :value="item.tipType"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </div>

    <div class="tableBox">
      <div class="tableBox-tab">
        <div class="custom-tab-list">
          <div class="custom-tab-item" :class="{ active: queryParams.status === 1 }" @click="handleChangeTab(1)">全部列表</div>
          <div class="custom-tab-item" :class="{ active: queryParams.status === -1 }" @click="handleChangeTab(-1)">已停用</div>
        </div>
        <collect-tpl ref="collect" :isCollect="false" :storeId.sync="storeId" :collectList.sync="collectList" @getList="getList" @colletSubmit="colletSubmit" :isSearch="true" :search="true" :showSearch.sync="showSearch" @queryTable="getList" :columns="columns" class="collectTpl" type="UserPriSupplier" />
      </div>

      <template v-if="list.length">
        <el-table v-loading="loading" ref="table" stripe :data="list" row-key="id" :key="key" style="width: 100%" class="custom-table custom-table-cell5" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column type="expand" width="30">
            <template slot-scope="props">
              <div class="table-expand">
                <el-table :data="props.row.contactList" :key="'list' + key" style="width: 100%" class="custom-table custom-table-cell10">
                  <el-table-column align="center" type="index" label="序号"></el-table-column>
                  <el-table-column align="center" prop="nickName" label="联系人"></el-table-column>
                  <el-table-column align="center" prop="phone" label="电话"></el-table-column>
                  <el-table-column align="center" prop="post" label="职务"></el-table-column>
                </el-table>
              </div>
            </template>
          </el-table-column>
          <el-table-column align="center" type="index" label="序号" v-if="columns[0].visible"></el-table-column>
          <el-table-column align="center" prop="name" label="供应商名称" show-overflow-tooltip v-if="columns[1].visible" min-width="150">
            <template slot-scope="{ row }">
              <span class="table-link" @click="handleCompanyView(row)">{{ row.name }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="nickName" label="联系人" show-overflow-tooltip v-if="columns[2].visible" width="120">
            <template slot-scope="{ row }">
              {{ returnContactList(row, 'nickName') }}
            </template>
          </el-table-column>
          <el-table-column align="center" prop="phone" label="电话" show-overflow-tooltip v-if="columns[3].visible" width="120">
            <template slot-scope="{ row }">
              {{ returnContactList(row, 'phone') }}
            </template>
          </el-table-column>
          <el-table-column align="center" prop="post" label="职务" show-overflow-tooltip v-if="columns[4].visible" width="120">
            <template slot-scope="{ row }">
              {{ returnContactList(row, 'post') }}
            </template>
          </el-table-column>
          <el-table-column align="center" prop="logo" label="logo" v-if="columns[5].visible" width="120">
            <template slot-scope="{ row }">
              <image-preview :src="imgPath + row.logoUrl" :width="50" :height="50" style="margin: 0 auto" />
            </template>
          </el-table-column>
          <el-table-column align="center" prop="address" label="地址" show-overflow-tooltip v-if="columns[6].visible">
            <template slot-scope="{ row }">{{ removeHtmlTag(row.address, 300) }}</template>
          </el-table-column>
          <el-table-column align="center" prop="createTime" label="创建时间" show-overflow-tooltip v-if="columns[7].visible" width="90"></el-table-column>
          <el-table-column align="center" prop="updateTime" label="更新时间" show-overflow-tooltip v-if="columns[8].visible" width="90"></el-table-column>
          <el-table-column align="center" label="供应商类型" show-overflow-tooltip min-width="130">
            <template slot="header">
              <span style="padding-right: 5px">供应商类型</span>
              <el-button plain size="mini" icon="el-icon-setting" @click="handleRules">自定义规则</el-button>
            </template>
            <template slot-scope="{ row }">
              <el-dropdown trigger="click" @command="handleCommand($event, row)">
                <span class="el-dropdown-link" :style="{ color: rulesFormat(row, 'class') }">
                  {{ rulesFormat(row) }}
                  <i class="el-icon-arrow-down el-icon--right"></i>
                  <el-tooltip effect="dark" content="手动设置" placement="top" v-if="!!row.tipRule">
                    <i class="el-icon-info" style="font-size: 14px"></i>
                  </el-tooltip>
                </span>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item :command="item.tipType" :style="{ color: item.color }" v-for="item in tipRuleTypeOptions" :key="item.tipType">
                    {{ '设为' + item.tipName + '供应商' }}
                  </el-dropdown-item>
                  <el-dropdown-item command="" style="color: #333333">取消设置</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
          </el-table-column>
          <el-table-column align="center" label="操作" :width="getActionColumnWidth()">
            <template slot-scope="{ row }">
              <!-- 常用按钮 -->
              <template v-for="action in getCommonActions(row)">
                <el-button :key="action.key" :type="action.type" :size="action.size" :icon="action.icon" @click="action.handler(row)" v-if="action.show(row)" style="margin-right: 10px">
                  {{ action.label }}
                </el-button>
              </template>
              <!-- 更多操作 -->
              <el-popover trigger="hover" v-if="hasMoreActions(row)">
                <el-button type="text" size="mini" slot="reference">
                  更多操作
                  <i class="el-icon-arrow-down el-icon--right"></i>
                </el-button>
                <div class="popover-button">
                  <template v-for="action in getAllActions(row)">
                    <div :key="action.key" class="popover-button-item" v-if="action.show(row)">
                      <el-button :type="action.type" :size="action.size" :icon="action.icon" @click="action.handler(row)">
                        {{ action.label }}
                      </el-button>
                      <i class="popover-button-icon" :class="[isCommonAction(action.key) ? 'el-icon-star-on' : 'el-icon-star-off', { active: isCommonAction(action.key) }]" @click="toggleCommonAction(action.key)" :title="isCommonAction(action.key) ? '取消常用' : '设置为常用'"></i>
                    </div>
                  </template>
                </div>
              </el-popover>
            </template>
          </el-table-column>
        </el-table>

        <div class="custom-pagination">
          <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
        </div>
      </template>
      <el-empty :image-size="200" v-else />
    </div>

    <!-- 批量收藏 -->
    <template v-if="!multiple">
      <template>
        <div class="mark" v-if="isCollect" @click="isCollect = !isCollect"></div>
        <div class="collectAll">
          <div class="collectAll-box">
            <div class="collectAll-title">已选择 {{ ids.length }} 项</div>
            <template>
              <el-tooltip content="收藏到其他收藏夹" placement="top" effect="dark">
                <div class="collectAll-btn">
                  <span :class="{ active: isCollect }" @click="isCollect = !isCollect">收藏到</span>
                </div>
              </el-tooltip>
            </template>
            <template>
              <el-tooltip content="共享给其他成员" placement="top" effect="dark">
                <div class="collectAll-btn">
                  <span @click="handleShare(ids)">共享</span>
                </div>
              </el-tooltip>
            </template>
            <el-tooltip content="退出" placement="top" effect="dark">
              <div class="collectAll-close" @click="handleClose">
                <i class="el-icon-close"></i>
              </div>
            </el-tooltip>
          </div>
        </div>
        <el-collapse-transition>
          <div class="collectAll-list-box" v-if="isCollect">
            <div class="collectAll-list-close">
              <span>请选择收藏夹</span>
              <i class="el-icon-close" @click="isCollect = !isCollect"></i>
            </div>
            <div class="collectAll-list">
              <div class="item" :class="{ disabled: item.storeId === storeId }" v-for="item in collectList" :key="item.storeId" @click="handleColletAll(item)">
                {{ item.dirName }}
              </div>
            </div>
          </div>
        </el-collapse-transition>
      </template>
    </template>

    <el-dialog v-dialogDragBox :visible.sync="dialogVisible">
      <img style="display: block; margin: 0 auto; max-width: 100%" :src="dialogImageUrl" alt="" />
    </el-dialog>

    <!-- 新增/修改 -->
    <create-dialog ref="create"></create-dialog>

    <!-- 共享 -->
    <el-dialog v-dialogDragBox title="共享产品" :visible.sync="shareOpen" width="1150px" class="custom-dialog" :before-close="handleShareCancel">
      <div class="formBox">
        <el-table :data="shareList" ref="shareList" style="width: 100%" stripe class="custom-table" @selection-change="handleSelectShare" row-key="userId">
          <el-table-column type="selection" width="50" align="center" reserve-selection />
          <el-table-column label="用户编号" align="center" prop="userId" width="100" />
          <el-table-column label="用户昵称" align="center" prop="nickName" :show-overflow-tooltip="true" />
          <el-table-column label="手机号码" align="center" prop="phonenumber" width="120" />
          <el-table-column label="备注" align="center" prop="remark" />
          <el-table-column label="创建时间" align="center" prop="createTime" width="160">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.createTime) }}</span>
            </template>
          </el-table-column>
        </el-table>

        <div class="custom-pagination">
          <pagination v-show="shareTotal > 0" :total="shareTotal" :page.sync="shareParams.pageNum" :limit.sync="shareParams.pageSize" @pagination="getshareList" />
        </div>
      </div>
      <div slot="footer">
        <el-badge>
          <button type="button" class="custom-dialog-btn" @click="handleShareCancel">取消</button>
        </el-badge>
        <el-badge :value="shareChecked.length || ''">
          <button type="button" class="custom-dialog-btn primary" :class="{ disabled: !shareChecked.length }" :disabled="!shareChecked.length" @click="handleShareSubmit">确定</button>
        </el-badge>
      </div>
    </el-dialog>
    <!-- 自定义规则 -->
    <el-dialog v-dialogDragBox title="自定义规则" :visible.sync="rulesOpen" width="1150px" class="custom-dialog">
      <div style="padding: 0 20px; margin-top: -20px">
        <div class="rules-item">
          <div class="rules-item-title">请先选择时间</div>
          <el-select v-model="month" placeholder="请先选择时间">
            <el-option v-for="item in monthOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </div>
        <div class="rules" v-for="(item, index) in rulesForm" :key="index">
          <div class="rules-item">
            <div class="rules-item-title">{{ item.tipName }}供应商</div>
            <el-color-picker v-model="item.color"></el-color-picker>
          </div>
          <div class="rules-item">
            <div class="rules-item-title">自定义规则</div>
            <div class="rules-item-content">
              <el-input v-model="item.dealCount" placeholder="请输入" style="width: 180px; margin-right: 10px">
                <span class="inline-flex" slot="prefix">成交次数≥</span>
                <span class="inline-flex" slot="suffix">次</span>
              </el-input>
              <el-input v-model="item.dealAmount" placeholder="请输入" style="width: 210px; margin-right: 10px">
                <span class="inline-flex" slot="prefix">采购金额≥</span>
                <span class="inline-flex" slot="suffix">千(元)</span>
              </el-input>
              <div class="rules-item-tip">
                <i class="el-icon-warning-outline"></i>
                <span>{{ rulesTip(item) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div slot="footer">
        <button type="button" class="custom-dialog-btn" @click="rulesOpen = false">取消</button>
        <button type="button" class="custom-dialog-btn primary" @click="handleRulesSubmit">确定</button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import CollectTpl from '@/views/components/collect'
import { getlist, dellist, privateSup, privateSupb, shareSupplier, getSupplierRule, addSupplierRule, updateSupplierType, changeStatus } from '@/api/houtai/siyu/gongying'
import { shoucTo } from '@/api/houtai/shoucang'
import createDialog from '@/views/houtai/siyu/gongying/create'
import { listUser } from '@/api/system/user'
import { removeHtmlTag } from '@/utils'

export default {
  name: 'Suppliers',
  components: { CollectTpl, createDialog },
  data() {
    return {
      showSearch: true,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: undefined,
        address: undefined,
        keyword: undefined,
        tipRuleType: '',
        status: 1
      },
      key: 1,
      loading: true,
      total: 0,
      list: [],
      // 显隐列
      columns: [
        { key: 0, label: `序号`, visible: true },
        { key: 1, label: `供应商名称`, visible: true },
        { key: 2, label: `联系人`, visible: true },
        { key: 3, label: `电话`, visible: true },
        { key: 4, label: `职务`, visible: true },
        { key: 5, label: `logo`, visible: true },
        { key: 6, label: `地址`, visible: true },
        { key: 7, label: `创建时间`, visible: true },
        { key: 8, label: `更新时间`, visible: true }
      ],
      // 收藏
      storeId: undefined,
      collectList: [],
      multiple: true,
      ids: [],
      isCollect: false,
      // logo预览
      dialogVisible: false,
      dialogImageUrl: undefined,
      // 共享
      shareOpen: false,
      shareForm: {},
      shareList: [],
      shareTotal: 0,
      shareParams: {
        pageNum: 1,
        pageSize: 10,
        userName: undefined,
        phonenumber: undefined
      },
      shareChecked: [],
      tipRuleTypeOptions: [],
      rulesForm: [],
      rulesOpen: false,
      month: undefined,
      monthOptions: [
        { label: '按月筛选', value: 1 },
        { label: '按季度筛选', value: 3 },
        { label: '按半年筛选', value: 6 },
        { label: '按年筛选', value: 12 }
      ],
      // 常用按钮配置
      commonActions: []
    }
  },
  created() {
    this.getList()
    this.getRules()
    // 加载常用按钮配置
    const commonActions = localStorage.getItem(this.userId + '.supplierCommonActions')
    if (commonActions) this.commonActions = JSON.parse(commonActions)
  },
  computed: {
    // 获取当前用户ID
    userId() {
      return this.$store.getters.info.userId
    }
  },
  methods: {
    removeHtmlTag,
    // 列表
    getList() {
      this.loading = true
      getlist(this.queryParams).then(res => {
        if (res.code === 200) {
          this.list = res.rows
          this.total = res.total
          this.key = Math.random()
          this.loading = false
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 查询自定义状态规则
    getRules() {
      getSupplierRule().then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          this.tipRuleTypeOptions = data
          this.month = data[0].month
        } else this.$message.error(msg)
      })
    },
    handleRules() {
      this.rulesForm = JSON.parse(JSON.stringify(this.tipRuleTypeOptions))
      this.rulesOpen = true
    },
    // 自定义状态提交
    handleRulesSubmit() {
      this.rulesForm.map(item => (item.month = this.month))
      addSupplierRule(this.rulesForm).then(res => {
        const { code, msg } = res
        if (code === 200) {
          this.$message.success('自定义规则设置成功')
          this.rulesOpen = false
          this.getRules()
          this.getList()
        } else this.$message.error(msg)
      })
    },
    // 订单状态返回
    rulesFormat(row, type = '') {
      const data = this.tipRuleTypeOptions.find(item => item.tipType === row.filterRule)
      if (!!data) {
        if (type === 'class') return data.color
        else return data.tipName + '供应商'
      }
    },
    // 返回规则提示
    rulesTip(val) {
      const { tipType, dealCount, dealAmount } = val
      const price = parseFloat((Number(dealAmount) * 0.1).toFixed(5))
      if (tipType === 'strategy') return `注:战略供应商规则默认为每月成交次数≥${dealCount}次，采购金额≥${price}万`
      else if (tipType === 'common') return `注:常用供应商规则默认为每月成交次数≥${dealCount}次，采购金额≥${price}万`
      else if (tipType === 'general') return `注:一般供应商规则默认为每月成交次数≥${dealCount}次，采购金额≥${price}万`
    },
    handleCommand(command, row) {
      let data = {}
      if (command) data = { id: row.id, tipRule: command }
      else data = { id: row.id }
      updateSupplierType(data).then(res => {
        if (res.code === 200) this.getList()
        else this.$message.error(res.msg)
      })
    },
    // 返回联系人信息
    returnContactList(row, val) {
      let index = row.contactList.findIndex(item => item.checked)
      if (index === -1) index = 0
      return row.contactList[index][val]
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.multiple = !selection.length
    },
    // 关闭多选
    handleClose() {
      this.isCollect = false
      this.multiple = true
      this.ids = []
      this.$refs.table.clearSelection()
    },
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    handleChangeTab(val = 1) {
      this.queryParams.status = val
      this.handleQuery()
    },
    // 重置搜索
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 新增
    handleAdd() {
      this.$refs.create.handleAdd('新增私域供应商')
    },
    // 查看公司详情
    handleCompanyView(row) {
      const { name } = row
      this.$refs.create.handleSearch(1, name)
    },
    // 修改
    handleUpdate(row) {
      privateSupb({ id: row.id }).then(res => {
        if (res.code === 200) {
          this.$refs.create.handleUpdate(res.data, '修改私域供应商')
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 详情
    handleDetail(row) {
      privateSupb({ id: row.id }).then(res => {
        if (res.code === 200) {
          this.$refs.create.handleDetail(res.data, '私域供应商详情')
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 单个收藏
    handleCollet(item) {
      this.$refs.collect.handleChange(item.id)
    },
    // 批量收藏
    handleColletAll(item) {
      const storeId = item.storeId
      const valueIdList = this.ids
      if (storeId === this.storeId) return
      shoucTo({ storeId, valueIdList }).then(res => {
        // this.getList()
        this.$message.success('操作成功')
        this.isCollect = false
        this.multiple = false
        this.ids = []
        this.$refs.table.clearSelection()
      })
    },
    // 提交收藏
    colletSubmit(storeId, data) {
      if (!Array.isArray(data)) data = [data]
      shoucTo({ storeId, valueIdList: data }).then(res => {
        this.getList()
        this.$message.success('操作成功')
      })
    },
    // 停用
    // prettier-ignore
    handleDelete(row, type = 1) {
      const ids = row.id
      const status = type === 1 ? 1 : -1
      const title = type === 1 ? '是否停用此供应商?' : '是否启用此供应商?'
      const msg = type === 1 ? '停用成功' : '启用成功'
      this.$confirm(title, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        changeStatus({ ids, status }).then(res => {
          if (res.code === 200) {
            this.$message.success(msg)
            this.getList()
          } else {
            this.$message.error(res.msg)
          }
        })
      }).catch(() => {
      })
    },
    // 推荐到公域
    handleRecommend(row) {
      const ids = row.id
      privateSup({ ids }).then(res => {
        if (res.code === 200) {
          this.$message.success('推荐成功,等待审核通过')
          this.getList()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // logo预览
    handleImg(url) {
      this.dialogImageUrl = this.imgPath + url
      this.dialogVisible = true
    },
    // 重置共享给他人
    resetShare() {
      this.shareForm = {
        ids: [],
        userId: []
      }
      this.resetForm('shareForm')
    },
    // 共享给他人
    handleShare(data) {
      this.resetShare()
      if (!Array.isArray(data)) data = [data]
      this.shareForm.ids = data
      this.getshareList()
      this.shareOpen = true
      this.$nextTick(() => {
        this.$refs.shareList.clearSelection()
      })
    },
    // 查询成员
    getshareList() {
      listUser(this.shareParams).then(res => {
        if (res.code === 200) {
          this.shareList = res.rows
          this.shareTotal = res.total
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 多选成员
    handleSelectShare(selection) {
      this.shareChecked = selection
    },
    // 取消共享
    handleShareCancel() {
      this.shareOpen = false
      this.handleClose()
    },
    // 确定共享
    handleShareSubmit() {
      const userId = this.shareChecked.map(item => item.userId)
      this.shareForm.userId = userId
      shareSupplier(this.shareForm).then(res => {
        if (res.code === 200) {
          this.$message.success('操作成功')
          this.shareOpen = false
          this.getList()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 获取所有操作按钮配置
    getAllActions(row) {
      const normalActions = [
        {
          key: 'detail',
          label: '详情',
          type: 'text',
          size: 'mini',
          icon: 'el-icon-view',
          show: row => this.queryParams.status === 1,
          handler: this.handleDetail
        },
        {
          key: 'update',
          label: '修改',
          type: 'text',
          size: 'mini',
          icon: 'el-icon-edit',
          show: row => this.queryParams.status === 1,
          handler: this.handleUpdate
        },
        {
          key: 'delete',
          label: '停用',
          type: 'text',
          size: 'mini',
          icon: 'el-icon-delete',
          show: row => this.queryParams.status === 1,
          handler: row => this.handleDelete(row, -1)
        },
        {
          key: 'share',
          label: '共享',
          type: 'text',
          size: 'mini',
          icon: 'el-icon-share',
          show: row => this.queryParams.status === 1,
          handler: row => this.handleShare(row.id)
        },
        {
          key: 'recommend',
          label: '推荐到公域',
          type: 'text',
          size: 'mini',
          icon: 'el-icon-position',
          show: row => this.queryParams.status === 1 && row.recommend,
          handler: this.handleRecommend
        },
        {
          key: 'collect',
          label: '收藏到',
          type: 'text',
          size: 'mini',
          icon: 'el-icon-paperclip',
          show: row => this.queryParams.status === 1,
          handler: this.handleCollet
        },
        {
          key: 'enable',
          label: '启用',
          type: 'text',
          size: 'mini',
          icon: 'el-icon-circle-check',
          show: row => this.queryParams.status === -1,
          handler: row => this.handleDelete(row, 1)
        }
      ]
      return normalActions
    },
    // 获取常用按钮
    getCommonActions(row) {
      const allActions = this.getAllActions(row).filter(action => action.show(row))
      const commonActions = allActions.filter(action => this.isCommonAction(action.key))
      // 如果没有设置常用按钮，默认显示详情
      if (commonActions.length === 0) {
        const detailAction = allActions.find(action => action.key === 'detail')
        return detailAction ? [detailAction] : allActions.slice(0, 1)
      }
      // 返回所有常用按钮，不限制数量
      return commonActions
    },
    // 判断是否为常用按钮
    isCommonAction(actionKey) {
      return this.commonActions.includes(actionKey)
    },
    // 判断是否有更多操作按钮
    hasMoreActions(row) {
      const allActions = this.getAllActions(row).filter(action => action.show(row))
      const commonActions = this.getCommonActions(row)
      // 如果显示的按钮数量大于常用按钮数量，则显示更多操作
      return allActions.length >= commonActions.length
    },
    // 切换常用按钮设置
    toggleCommonAction(actionKey) {
      const index = this.commonActions.indexOf(actionKey)
      if (index > -1) {
        this.commonActions.splice(index, 1)
      } else {
        // 允许设置多个常用按钮
        this.commonActions.push(actionKey)
      }
      localStorage.setItem(this.userId + '.supplierCommonActions', JSON.stringify(this.commonActions))
    },
    // 计算操作列宽度
    getActionColumnWidth() {
      // 如果列表为空，返回默认宽度
      if (!this.list || this.list.length === 0) {
        return 180
      }
      // 取第一行数据来计算宽度
      const firstRow = this.list[0]
      const commonActions = this.getCommonActions(firstRow)
      const hasMoreActions = this.hasMoreActions(firstRow)
      // 常用按钮数量 + 更多操作按钮(如果有的话) * 110px
      const buttonCount = commonActions.length + (hasMoreActions ? 1 : 0)
      const width = buttonCount * 100
      // 设置最小宽度为 100px，最大宽度为 500px
      return Math.max(100, Math.min(500, width))
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
.tableBox {
  padding: 10px 20px 20px;
}
.custom-search {
  ::v-deep {
    .el-form-item--small.el-form-item {
      margin-bottom: 0;
    }
  }
}
.collectTpl {
  margin-bottom: 0;
  ::v-deep {
    .collect {
      margin-bottom: 0;
      background-color: transparent;
      border-color: transparent;
    }
  }
}
.custom-dialog {
  ::v-deep {
    .formBox {
      padding: 20px;
    }
    .custom-dialog-btn.disabled {
      opacity: 0.5;
    }
  }
}
.custom-table ::v-deep {
  .table-expand {
    padding-left: 80px;
    padding-bottom: 10px;
    margin: -13px 0;
    background-color: #eceef1;
  }
  .el-table__row.expanded {
    background-color: #eceef1;
    td.el-table__cell {
      background-color: #eceef1;
    }
  }
  .status {
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }
}
.rules {
  padding: 15px 0;
  border-bottom: 1px solid #e2e6f3;
  &-item {
    display: flex;
    align-items: center;
    padding: 15px 0;
    &-title {
      width: 6.5em;
      font-size: 14px;
      color: #666666;
    }
    &-content {
      display: flex;
      align-items: center;
      ::v-deep {
        .el-input__inner {
          padding-left: 5.2em;
        }
      }
    }
    &-tip {
      font-size: 12px;
      color: #9eaec4;
    }
  }
}
.tableBox-tab {
  margin-bottom: 18px;
  background-color: #f5f7fa;
  border: 1px solid #dcdfe6;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .custom-tab-list {
    display: inline-flex;
    line-height: 50px;
  }
  .custom-tab-item {
    transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
    color: #666666;
    padding: 0 20px;
    margin: -1px 0;
    cursor: pointer;
    box-sizing: border-box;
    list-style: none;
    &:hover,
    &.active {
      background-color: #409eff;
      color: #ffffff;
    }
  }
}
</style>
