<template>
  <div>
    <h4 class="form-header h4">滚丝</h4>
    <el-form ref="form" :model="form" label-width="80px">
      <el-form-item label="员工计件工资（元/根）" prop="pay">
        <el-input v-model="form.pay" placeholder="请输入员工计件工资"></el-input>
      </el-form-item>
      <el-form-item label="耗材费用（元）" prop="cost">
        <el-input v-model="form.cost" placeholder="请输入耗材费用"></el-input>
      </el-form-item>
      <h4 class="form-header h4">计算结果</h4>
      <el-form-item label="总价（元）" prop="totalprice">
        <el-input v-model="form.totalprice"></el-input>
      </el-form-item>
      <el-form-item label="">
        <el-button type="primary"  @click="count()">开始计算</el-button>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="submitForm">保存设置</el-button>
    </div>
  </div>
</template>

<script>
  import {guancaijisuan} from "@/api/houtai/gongyu/xuqiu";
  export default {
    data() {
      return {
        form: {
          pay:'',
          cost: '',
          totalprice:'',
        },
      };
    },
    created() {
    },
    methods: {
      handleClose(tag) {
        this.dynamicTags.splice(this.dynamicTags.indexOf(tag), 1);
      },

      showInput() {
        this.inputVisible = true;
        this.$nextTick(_ => {
          this.$refs.saveTagInput.$refs.input.focus();
        });
      },
      handleInputConfirm() {
        let inputValue = this.inputValue;
        if (inputValue) {
          this.dynamicTags.push(inputValue);
        }
        this.inputVisible = false;
        this.inputValue = '';
      },
      count() {
        var pay = this.form.pay;
        var cost = this.form.cost;
        if(pay==''){
          this.$message({
            message: '员工计件工资不能为空！',
            type: 'warning'
          });
          return;
        }else if(cost==''){
          this.$message({
            message: '耗材费用不能为空！',
            type: 'warning'
          });
          return;
        }
        var totalprice = Number(pay)+Number(cost);
        this.form.totalprice=totalprice;
        /*guancaijisuan(this.form).then((res) => {
          this.form.weight=res.data;
        });*/
      },
      submitForm(){
        alert("保存设置===="+this.form);
        this.$emit('form',this.form)
      }
    }
  }
</script>

<style scoped>
  .el-tag + .el-tag {
    margin-left: 10px;
  }
  .button-new-tag {
    margin-left: 10px;
    height: 32px;
    line-height: 30px;
    padding-top: 0;
    padding-bottom: 0;
  }
  .input-new-tag {
    width: 90px;
    margin-left: 10px;
    vertical-align: bottom;
  }
</style>
