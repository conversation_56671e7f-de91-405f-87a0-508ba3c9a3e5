<template>
  <div>
        <h4 class="form-header h4">方管计算</h4>
        <el-form ref="form" :model="form" label-width="80px">
          <el-row>
            <el-col :span="12">
              <el-form-item label="材料密度" prop="density">
                <el-select clearable size="small" placeholder="请选择材料密度" v-model="form.density">
                  <el-option
                    label="灰铸铁(≤HT200)#7.2"
                    value="7.2">
                  </el-option>
                  <el-option
                    label="灰铸铁(≥HT200)#7.35"
                    value="7.35">
                  </el-option>
                  <el-option
                    label="白口铸铁#7.4~7.7"
                    value="7.4">
                  </el-option>
                  <el-option
                    label="可锻铸铁#7.2~7.4"
                    value="7.3">
                  </el-option>
                  <el-option
                    label="工业纯铁#7.87"
                    value="7.87">
                  </el-option>
                  <el-option
                    label="铸钢#7.8"
                    value="7.8">
                  </el-option>
                  <el-option
                    label="钢材#7.85"
                    value="7.85">
                  </el-option>
                  <el-option
                    label="低碳钢(含碳0.1%)#7.85"
                    value="7.85">
                  </el-option>
                  <el-option
                    label="中碳钢(含碳0.4%)#7.82"
                    value="7.82">
                  </el-option>
                  <el-option
                    label="高碳钢(含碳1%)#7.81"
                    value="7.81">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="规格" prop="size">
                <el-select clearable size="small" placeholder="请选择材料规格" v-model="form.size">
                  <el-option
                    label="100*100*2.6"
                    value="100*100*2.6">
                  </el-option>
                  <el-option
                    label="100*100*3.2"
                    value="100*100*3.2">
                  </el-option>
                  <el-option
                    label="100*100*4"
                    value="100*100*4">
                  </el-option>
                  <el-option
                    label="100*100*5"
                    value="100*100*5">
                  </el-option>
                  <el-option
                    label="100*100*6"
                    value="100*100*6">
                  </el-option>
                  <el-option
                    label="100*100*8"
                    value="100*100*8">
                  </el-option>
                  <el-option
                    label="100*50*3"
                    value="100*50*3">
                  </el-option>
                  <el-option
                    label="100*50*4"
                    value="100*50*4">
                  </el-option>
                  <el-option
                    label="100*50*5"
                    value="100*50*5">
                  </el-option>
                  <el-option
                    label="100*60*2"
                    value="100*60*2">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="12">
              <el-form-item label="长边宽" prop="lengthwidth">
                <el-input v-model="form.lengthwidth" placeholder="请输入长边宽 mm"/>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="短边宽" prop="shortwidth">
                <el-input v-model="form.shortwidth" placeholder="请输入长边宽 mm"/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="壁厚" prop="tube">
                <el-input v-model="form.tube" placeholder="请输入壁厚 mm"/>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="长度" prop="length">
                <el-input v-model="form.length" placeholder="请输入长度  m"/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="管材根数" prop="pipenum">
                <el-input v-model="form.pipenum" placeholder="请输入管材根数"/>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="">
                <el-button type="primary" size="mini" @click="change()">开始计算</el-button>
              </el-form-item>
            </el-col>
          </el-row>

        <h4 class="form-header h4">计算结果</h4>
          <el-row>
            <el-col :span="12">
              <el-form-item label="毛重" prop="weight">
                <el-input v-model="form.weight" placeholder=""/>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="净重" prop="netweight">
                <el-input v-model="form.netweight" placeholder="请输入净重 千克"/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="单价" prop="price">
                <el-input v-model="form.price" placeholder="请输入单价 元/吨"/>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="小计" prop="totalprice">
                <el-input v-model="form.totalprice" placeholder="请输入总价 元"/>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div style="width: 500px;font-size: 14px;font-weight: 400;color: #999999;margin-left: 80px;padding-bottom: 20px">
          <div> 计算说明:</div>
          <div>
            1、计算重量:选填材料规格、长度、单价计算材料重量、总价等信息;
          </div>
          <div>
            2、计算长度:输入总重量、选填材料规格计算此批材料长度:
          </div>
          <div>
            3、计算单价: 选填材料规格、长度、总价计算出单吨价格。
          </div>
        </div>
        <div slot="footer" class="dialog-footer">
          <div style="text-align: right">
            <el-button type="primary" size="mini" @click="submitForm">保存设置</el-button>
          </div>
        </div>
  </div>
</template>

<script>
  import {guancaijisuan,getquoteInfobyid} from "@/api/houtai/gongyu/xuqiu";
  export default {
    data() {
      return {
        form: {
          density:'7.85',
          size:"100*100*2.6",
          lengthwidth:'',
          shortwidth:'',
          diameter:'',
          tube:'',
          length:'',
          pipenum:'',
          picture1:'C:\\Users\\<USER>\\Desktop\\timg.jpeg',
          volume: '',
          surface: '',
          weight: '',
          netweight:'',
          price:'',
          totalprice:'',
          quoteinfoid:'',
          quotetemplateid:'',
          stage:''
        },
      };
    },
    props:{
      quoteId:'',
      templateId:'',
    },
    created() {
    },
    mounted() {
    },
    methods: {
      cc(){
          this.form.title = ddd.title;
      },
      handleClose(tag) {
        this.dynamicTags.splice(this.dynamicTags.indexOf(tag), 1);
      },

      showInput() {
        this.inputVisible = true;
        this.$nextTick(_ => {
          this.$refs.saveTagInput.$refs.input.focus();
        });
      },
      handleInputConfirm() {
        let inputValue = this.inputValue;
        if (inputValue) {
          this.dynamicTags.push(inputValue);
        }
        this.inputVisible = false;
        this.inputValue = '';
      },
      change() {
        var lengthwidth = this.form.lengthwidth;
        var shortwidth = this.form.shortwidth;
        var length = this.form.length;
        var tube = this.form.tube;
        if(lengthwidth==''){
          this.$message({
            message: '长边宽不能为空！',
            type: 'warning'
          });
          return;
        }else if(shortwidth==''){
          this.$message({
            message: '短边宽不能为空！',
            type: 'warning'
          });
          return;
        } else if(tube==''){
          this.$message({
            message: '壁厚不能为空！',
            type: 'warning'
          });
          return;
        }else if(length==''){
          this.$message({
            message: '长度不能为空！',
            type: 'warning'
          });
          return;
        }
        this.form['tabname'] ='方管计算',
        guancaijisuan(this.form).then((res) => {
          this.form.weight=res.data;
        });
      },
      showmodel(id) {
      },
      submitForm(){
        this.$emit('form',this.form);
        this.form.density='7.85',
        this.form.radio='',
        this.form.diameter='',
        this.form.tube='',
        this.form.length='',
        this.form.pipenum='',
        this.form.picture1='C:\\Users\\<USER>\\Desktop\\timg.jpeg',
        this.form.volume='',
        this.form.surface='',
        this.form.weight='',
        this.form.netweight='',
        this.form.price='',
        this.form.totalprice='',
        this.form.quoteinfoid='',
        this.form.quotetemplateid='',
        this.form.stage=''
      }
    }
  }
</script>

<style scoped>
  .el-tag + .el-tag {
    margin-left: 10px;
  }
  .button-new-tag {
    margin-left: 10px;
    height: 32px;
    line-height: 30px;
    padding-top: 0;
    padding-bottom: 0;
  }
  .input-new-tag {
    width: 90px;
    margin-left: 10px;
    vertical-align: bottom;
  }
</style>
