<template>
  <div>
        <h4 class="form-header h4">圆管计算</h4>
        <el-form ref="form" :model="form" label-width="80px">

          <el-row>
            <el-col :span="12">
              <el-row>
                <el-col :span="24">
                  <el-form-item label="材料密度" prop="density">
                    <el-select clearable size="small" placeholder="请选择材料密度" v-model="form.density">
                      <el-option
                        label="灰铸铁(≤HT200)#7.2"
                        value="7.2">
                      </el-option>
                      <el-option
                        label="灰铸铁(≥HT200)#7.35"
                        value="7.35">
                      </el-option>
                      <el-option
                        label="白口铸铁#7.4~7.7"
                        value="7.4">
                      </el-option>
                      <el-option
                        label="可锻铸铁#7.2~7.4"
                        value="7.3">
                      </el-option>
                      <el-option
                        label="工业纯铁#7.87"
                        value="7.87">
                      </el-option>
                      <el-option
                        label="铸钢#7.8"
                        value="7.8">
                      </el-option>
                      <el-option
                        label="钢材#7.85"
                        value="7.85">
                      </el-option>
                      <el-option
                        label="低碳钢(含碳0.1%)#7.85"
                        value="7.85">
                      </el-option>
                      <el-option
                        label="中碳钢(含碳0.4%)#7.82"
                        value="7.82">
                      </el-option>
                      <el-option
                        label="高碳钢(含碳1%)#7.81"
                        value="7.81">
                      </el-option>
                    </el-select>

                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="属性选择" >
                    <el-radio-group v-model="form.radio">
                      <el-radio  label="nest:3,4" >外直径+壁管厚</el-radio>
                      <el-radio  label="nest:3,10">外直径+内直径</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <el-form-item label="外直径" prop="diameter">
                    <el-input v-model="form.diameter" placeholder="请输入外直径 mm"/>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="管壁厚" prop="tube">
                    <el-input v-model="form.tube" placeholder="请输入壁管厚 mm"/>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <el-form-item label="长度" prop="length">
                    <el-input v-model="form.length" placeholder="请输入长度 mm"/>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="管材报数" prop="pipenum">
                    <el-input v-model="form.pipenum" placeholder="请输入管材报数"/>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <el-form-item label="">
                    <el-button type="primary" size="mini" @click="change()">开始计算</el-button>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>
            <el-col :span="12">
                <!--<el-image style="width:200px;" :src="../../"></el-image>-->
               <div style="text-align: center">
                 <img style="width:350px;" src="../../../../public/imgs/guancai.png">
               </div>
            </el-col>
          </el-row>

        <h4 class="form-header h4">计算结果</h4>
          <!--<el-row>
            <el-col :span="12">
              <el-form-item label="体积" prop="volume">
                <el-input v-model="form.volume"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="外表面积" prop="surface">
                <el-input v-model="form.surface"></el-input>
              </el-form-item>
            </el-col>
          </el-row>-->
          <el-row>
            <el-col :span="12">
              <el-form-item label="毛重" prop="weight">
                <el-input v-model="form.weight" placeholder=""/>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="净重" prop="netweight">
                <el-input v-model="form.netweight" placeholder="请输入净重 千克"/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="单价" prop="price">
                <el-input v-model="form.price" placeholder="请输入单价 元/吨"/>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="小计" prop="totalprice">
                <el-input v-model="form.totalprice" placeholder="请输入总价 元"/>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div style="width: 500px;font-size: 14px;font-weight: 400;color: #999999;margin-left: 80px;padding-bottom: 20px">
          <div> 计算说明:</div>
          <div>
            1、内外径壁厚关系: 外径=内径+2*壁厚:
          </div>
          <div>
            2、计算重量:选填材料规格、长度、单价计算材料重量、总价等信息;
          </div>
          <div>
            3、计算长度:输入总重量、选填材料规格计算此批材料长度:
          </div>
          <div>
            4、计算单价: 选填材料规格、长度、总价计算出单吨价格。
          </div>
        </div>
        <div slot="footer" class="dialog-footer">
          <div style="text-align: right">
            <el-button type="primary" size="mini" @click="submitForm">保存设置</el-button>
          </div>
        </div>
  </div>
</template>

<script>
  import {guancaijisuan,getquoteInfobyid} from "@/api/houtai/gongyu/xuqiu";
  export default {
    data() {
      return {
        form: {
          density:'7.85',
          radio: 'nest:3,4',
          diameter:'',
          tube:'',
          length:'',
          pipenum:'',
          picture1:'C:\\Users\\<USER>\\Desktop\\timg.jpeg',
          volume: '',
          surface: '',
          weight: '',
          netweight:'',
          price:'',
          totalprice:'',
          quoteinfoid:'',
          quotetemplateid:'',
          stage:''
        },
      };
    },
    props:{
      quoteId:'',
      templateId:'',
    },
    created() {
    },
    mounted() {
    },
    methods: {
      cc(){
          this.form.title = ddd.title;
      },
      handleClose(tag) {
        this.dynamicTags.splice(this.dynamicTags.indexOf(tag), 1);
      },

      showInput() {
        this.inputVisible = true;
        this.$nextTick(_ => {
          this.$refs.saveTagInput.$refs.input.focus();
        });
      },
      handleInputConfirm() {
        let inputValue = this.inputValue;
        if (inputValue) {
          this.dynamicTags.push(inputValue);
        }
        this.inputVisible = false;
        this.inputValue = '';
      },
      change() {
        var diameter = this.form.diameter;
        var tube = this.form.tube;
        var length = this.form.length;
        if(diameter==''){
          this.$message({
            message: '外直径不能为空！',
            type: 'warning'
          });
          return;
        }else if(tube==''){
          this.$message({
            message: '壁厚不能为空！',
            type: 'warning'
          });
          return;
        }else if(length==''){
          this.$message({
            message: '长度不能为空！',
            type: 'warning'
          });
          return;
        }
        var weight = (diameter-tube)*tube*0.02466*length;
        this.form['tabname'] ='圆管计算',
        guancaijisuan(this.form).then((res) => {
          this.form.weight=res.data;
        });
      },
      showmodel(id) {
      },
      submitForm(){
        var diameter = this.form.diameter;
        var tube = this.form.tube;
        var length = this.form.length;
        var weight = this.form.weight;
        var price = this.form.price;
        var totalprice = this.form.totalprice;
        if(diameter==''){
          this.$message({
            message: '外直径不能为空！',
            type: 'warning'
          });
          return;
        }else if(tube==''){
          this.$message({
            message: '壁厚不能为空！',
            type: 'warning'
          });
          return;
        }else if(length==''){
          this.$message({
            message: '长度不能为空！',
            type: 'warning'
          });
          return;
        }else if(weight==''){
          this.$message({
            message: '重量不能为空！',
            type: 'warning'
          });
          return;
        }else if(price==''){
          this.$message({
            message: '单价不能为空！',
            type: 'warning'
          });
          return;
        }else if(totalprice==''){
          this.$message({
            message: '总价不能为空！',
            type: 'warning'
          });
          return;
        }
        this.$emit('form',this.form);
        this.form.density='7.85',
        this.form.radio='nest:3,4',
        this.form.diameter='',
        this.form.tube='',
        this.form.length='',
        this.form.pipenum='',
        this.form.picture1='C:\\Users\\<USER>\\Desktop\\timg.jpeg',
        this.form.volume='',
        this.form.surface='',
        this.form.weight='',
        this.form.netweight='',
        this.form.price='',
        this.form.totalprice='',
        this.form.quoteinfoid='',
        this.form.quotetemplateid='',
        this.form.stage=''
      }
    }
  }
</script>

<style scoped>
  .el-tag + .el-tag {
    margin-left: 10px;
  }
  .button-new-tag {
    margin-left: 10px;
    height: 32px;
    line-height: 30px;
    padding-top: 0;
    padding-bottom: 0;
  }
  .input-new-tag {
    width: 90px;
    margin-left: 10px;
    vertical-align: bottom;
  }
</style>
