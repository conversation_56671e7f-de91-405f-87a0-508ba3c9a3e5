<template>
  <div class="app-container bgcf9 vh-85">
    <!-- 草稿 -->
    <div class="cache" v-if="cache.material">
      <div class="cache-txt">
        <img style="vertical-align: middle" src="../../../../public/imgs/提醒 1.png" />
        您有未完成的价格更改，请尽快完成操作
      </div>
      <div class="cache-btn active" @click="handleComplete">去完成</div>
      <div class="cache-btn" @click="handleIgnore">忽略</div>
    </div>

    <!-- 搜索 -->
    <div class="header">
      <div class="search">
        <el-form :model="queryParams" ref="queryForm" size="small" label-width="5em" :inline="true" @submit.native.prevent>
          <div>
            <el-form-item label="报价名称" prop="keyword">
              <el-input v-model="queryParams.keyword" placeholder="请输入报价名称" @keyup.enter.native="handleQuery" clearable></el-input>
            </el-form-item>
            <el-form-item label="报单人" prop="createBy">
              <el-input v-model="queryParams.createBy" placeholder="请输入报单人" @keyup.enter.native="handleQuery" clearable></el-input>
            </el-form-item>
            <el-form-item label="报价编号" prop="serial">
              <el-input v-model="queryParams.serial" placeholder="请输入报价编号" @keyup.enter.native="handleQuery" clearable></el-input>
            </el-form-item>
            <el-form-item label="产品名称" prop="productName">
              <el-input v-model="queryParams.productName" placeholder="请输入产品名称" @keyup.enter.native="handleQuery" clearable></el-input>
            </el-form-item>
            <el-form-item label="">
              <el-button type="text" icon="el-icon-arrow-down" @click="showMore = !showMore" v-if="!showMore">展开</el-button>
              <el-button type="text" icon="el-icon-arrow-up" @click="showMore = !showMore" v-if="showMore">收起</el-button>
            </el-form-item>
          </div>
          <el-collapse-transition>
            <div v-show="showMore">
              <el-form-item label="产品编码" prop="productCode">
                <el-input v-model="queryParams.productCode" placeholder="请输入产品编码" @keyup.enter.native="handleQuery" clearable></el-input>
              </el-form-item>
              <el-form-item label="产品规格" prop="specs">
                <el-input v-model="queryParams.specs" placeholder="请输入产品规格" @keyup.enter.native="handleQuery" clearable></el-input>
              </el-form-item>
              <el-form-item label="产品型号" prop="model">
                <el-input v-model="queryParams.model" placeholder="请输入产品型号" @keyup.enter.native="handleQuery" clearable></el-input>
              </el-form-item>
              <el-form-item label="产品材质" prop="materialQuality">
                <el-input v-model="queryParams.materialQuality" placeholder="请输入产品材质" @keyup.enter.native="handleQuery" clearable></el-input>
              </el-form-item>
              <el-form-item label="表面处理" prop="surface">
                <el-input v-model="queryParams.surface" placeholder="请输入表面处理" @keyup.enter.native="handleQuery" clearable></el-input>
              </el-form-item>
              <el-form-item label="产品来源" prop="source">
                <el-select v-model="queryParams.source" placeholder="请选择产品来源" @change="handleQuery" clearable>
                  <el-option label="公域" value="common"></el-option>
                  <el-option label="私域" value="private"></el-option>
                </el-select>
              </el-form-item>
            </div>
          </el-collapse-transition>
          <el-form-item>
            <el-button type="primary" @click="handleQuery" plain icon="el-icon-search">搜索</el-button>
            <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
            <div class="handle-btn" style="display: inline-block;" :class="{ disabled: !ids.length }" @click="handleUpdate">
              <img style="vertical-align: middle; transform: translateY(-2px)" src="../../../../public/imgs/更改为正确 1.png" />
              更新报价
            </div>
            <div class="handle-btn" style="display: inline-block;" :class="{ disabled: !ids.length }" @click="handleDeleteAll">批量删除</div>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <!-- 收藏 -->
    <div class="collect-box">
      <collect-tpl ref="collect" :storeId.sync="storeId" :collectList.sync="collectList" @getList="handleQuery" @colletSubmit="handleColletSubmit" type="InnerQuote" :isAll="true" allTitle="全部列表" :isSearch="true" :search="false" @queryTable="getList" :columns="columns" :hasstoreId="storeId" />
    </div>

    <!-- 列表数据 -->
    <template v-if="list.length">
      <el-table id="table" ref="table" v-loading="loading" :data="list" stripe style="width: 100%" @selection-change="handleSelectionChange" :key="key" class="custom-table" border @header-dragend="handleColChange" row-key="id">
        <el-table-column :selectable="chckeSelect" type="selection" width="40" align="center"></el-table-column>
        <el-table-column label="序号" type="index" align="center" :width="tableWidth[1]" v-if="columns[0].visible"></el-table-column>
        <el-table-column label="报价编号" prop="serial" align="center" :width="tableWidth[2]" v-if="columns[1].visible" show-overflow-tooltip></el-table-column>
        <el-table-column label="报价名称" prop="name" align="center" :width="tableWidth[3]" v-if="columns[2].visible" show-overflow-tooltip class-name="cell-new">
          <template slot-scope="{ row }">
            <span class="table-link" :class="{ disabled: !row.mine }" @click="handleOffer(row)">{{ row.name }}</span>
            <span :class="{ 'table-new': row.new }"></span>
          </template>
        </el-table-column>
        <el-table-column label="未税总价" prop="totalPrice" align="center" :width="tableWidth[4]" v-if="columns[3].visible">
          <template slot-scope="{ row }">
            <b style="font-size: 14px; color: #ec2454" class="pointer" @click="handleHistory(row)">{{ '¥' + row.totalPrice }}</b>
          </template>
        </el-table-column>
        <el-table-column label="含税总价" prop="taxPrice" align="center" :width="tableWidth[5]" v-if="columns[4].visible">
          <template slot-scope="{ row }">
            <b style="font-size: 14px; color: #ec2454">{{ row.taxPrice ? '¥' + row.taxPrice : '' }}</b>
          </template>
        </el-table-column>
        <el-table-column label="毛总重" prop="grossWeight" align="center" :width="tableWidth[6]" v-if="columns[5].visible">
          <template slot-scope="{ row }">{{ row.grossWeight + 'kg' }}</template>
        </el-table-column>
        <el-table-column label="净总重" prop="netWeight" align="center" :width="tableWidth[7]" v-if="columns[6].visible">
          <template slot-scope="{ row }">{{ row.netWeight + 'kg' }}</template>
        </el-table-column>
        <el-table-column label="报价产品" prop="productName" align="center" :width="tableWidth[8]" v-if="columns[7].visible" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <span class="table-link" @click="handleProductInfo(row)" v-if="row.productId">{{ (hasOwn(row,'product') && hasOwn(row.product,'productName') && row.product['productName']) || '' }}</span>
            <span v-else></span>
          </template>
        </el-table-column>
        <el-table-column label="产品规格" prop="productName" align="center" :width="tableWidth[9]" v-if="columns[8].visible" show-overflow-tooltip>
          <template slot-scope="{ row }">
            {{ (hasOwn(row,'product') && hasOwn(row.product,'model') && row.product['model']) || '' }}
          </template>
        </el-table-column>
        <el-table-column label="创建时间" prop="createTime" align="center" min-width="85" v-if="columns[9].visible">
          <template slot-scope="{ row }">{{ parseTime(row.createTime, '{y}-{m}-{d}') }}</template>
        </el-table-column>
        <el-table-column label="修改时间" prop="updateTime" align="center" min-width="85" v-if="columns[10].visible">
          <template slot-scope="{ row }">{{ parseTime(row.updateTime, '{y}-{m}-{d}') }}</template>
        </el-table-column>
        <el-table-column label="报单人" prop="createBy" align="center" :width="tableWidth[12]" v-if="columns[11].visible" show-overflow-tooltip></el-table-column>
        <el-table-column label="操作" prop="111" align="center" min-width="260">
          <template slot-scope="{ row }">
            <el-button type="primary" size="small" @click="handleEdit(row)" :disabled="!row.mine">修改</el-button>
            <el-button type="success" size="small" @click="handleCollet(row)">收藏</el-button>
            <el-button type="warning" size="small" @click="handleCopy(row)" :disabled="!row.mine">复制</el-button>
            <el-button type="danger" size="small" @click="handleCollectDelete(row)" v-if="storeId">删除收藏</el-button>
            <el-button type="danger" size="small" @click="handleDelete(row)" :disabled="!row.mine" v-else>删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" :page-sizes="['10', '20', '50', '100']" />
    </template>
    <el-empty v-else />

    <!-- 更新报价 -->
    <el-dialog v-dialogDragBox title="更新报价" :visible.sync="updateOpen" width="60%" custom-class="update-dialog" :before-close="handleSaveCache">
      <div class="cache-tab">
        <template v-for="item in stageList">
          <div class="tab-item" :class="{ active: stageChecked === item.stage }" :key="item.stage" @click="handleChangeStage(item)">{{ item.name }}</div>
        </template>
      </div>
      <div class="cache-radio">
        <el-radio-group v-model="radioChcked" size="normal" @change="handleChangeRadio">
          <el-radio v-for="item in radioOptions" :key="item.key" :label="item.label">
            {{ item.title }}
          </el-radio>
        </el-radio-group>
      </div>
      <el-table :data="stageChecked === 'material' ? material.list : outsourcing.list" v-show="radioChcked === 'yes'" :default-expand-all="true">
        <el-table-column type="expand">
          <template slot-scope="{ row }">
            <el-table :data="row.quoteList" stripe class="tr-table">
              <el-table-column type="expand"></el-table-column>
              <el-table-column label="序号" type="index" align="center" width="60"></el-table-column>
              <el-table-column label="报价名称" prop="name" align="center"></el-table-column>
              <el-table-column label="产品名称" align="center">
                <template slot-scope="{ row }">
                  <span class="table-link" @click="handleProductInfo(row)" v-if="row.productId">{{ row.hasOwnProperty('product') ? (row.product.hasOwnProperty('productName') ? row.product.productName : '') : '' }}</span>
                  <span v-else></span>
                </template>
              </el-table-column>
              <el-table-column label="规格" prop="specs" align="center">
                <template slot-scope="{ row }">
                  {{ row.hasOwnProperty('product') ? row.product.specs : '' }}
                </template>
              </el-table-column>
              <el-table-column label="型号" prop="model" align="center">
                <template slot-scope="{ row }">
                  {{ row.hasOwnProperty('product') ? row.product.model : '' }}
                </template>
              </el-table-column>
              <el-table-column label="备注" prop="fragmentRemark" align="center"></el-table-column>
              <el-table-column label="编号" prop="serial" align="center"></el-table-column>
              <el-table-column label="模板" prop="templateName" align="center"></el-table-column>
              <el-table-column label="小计" prop="fragmentTotal" align="center">
                <template slot-scope="{ row }">{{ '¥ ' + row.fragmentTotal }}</template>
              </el-table-column>
              <el-table-column label="单价" prop="fragment" align="center">
                <template slot-scope="{ row }">
                  <span class="table-price">{{ '¥ ' + row.fragment }}</span>
                </template>
              </el-table-column>
            </el-table>
          </template>
        </el-table-column>
        <el-table-column label="序号" type="index" align="center" width="60"></el-table-column>
        <el-table-column label="名称" prop="name" align="center"></el-table-column>
        <el-table-column :label="stageChecked === 'material' ? '生产材料' : '外采半成品'" prop="stageName" align="center"></el-table-column>
        <el-table-column label="价格" prop="price" align="center">
          <template slot-scope="{ row }">
            <el-input v-model="row.price" size="mini" style="width: 100px" @blur="row.edi = !row.edi" v-if="row.edi"></el-input>
            <span class="table-price" v-else>{{ '¥ ' + row.price }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center">
          <template slot-scope="{ row }" v-if="row.stagePriceId">
            <el-button @click="row.edi = !row.edi" size="mini" type="primary" plain v-if="row.edi == false && row.oldprice == row.price">更改报价</el-button>
            <el-button @click="row.edi = !row.edi" size="mini" type="success" plain v-if="row.edi == true">完成修改</el-button>
            <el-button @click="row.edi = !row.edi" size="mini" type="primary" plain v-if="row.edi == false && row.oldprice != row.price">已更改</el-button>
            <el-button @click="row.price = row.oldprice" size="mini" type="danger" plain v-if="row.edi == false && row.oldprice != row.price">还原</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-table :data="stageChecked === 'material' ? material.noBindQuote : outsourcing.noBindQuote" v-show="radioChcked === 'no'">
        <el-table-column label="序号" type="index" align="center" width="60"></el-table-column>
        <el-table-column label="报价名称" prop="name" align="center"></el-table-column>
        <el-table-column label="产品名称" align="center">
          <template slot-scope="{ row }">
            <span class="table-link" @click="handleProductInfo(row)">{{ row.hasOwnProperty('product') ? row.product.productName : '' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="规格" prop="specs" align="center">
          <template slot-scope="{ row }">
            {{ row.hasOwnProperty('product') ? row.product.specs : '' }}
          </template>
        </el-table-column>
        <el-table-column label="型号" prop="model" align="center">
          <template slot-scope="{ row }">
            {{ row.hasOwnProperty('product') ? row.product.model : '' }}
          </template>
        </el-table-column>
        <el-table-column label="备注" prop="fragmentRemark" align="center"></el-table-column>
        <el-table-column label="编号" prop="serial" align="center"></el-table-column>
        <el-table-column label="模板" prop="templateName" align="center"></el-table-column>
        <el-table-column label="小计" prop="fragmentTotal" align="center">
          <template slot-scope="{ row }">{{ '¥ ' + row.fragmentTotal }}</template>
        </el-table-column>
        <el-table-column label="单价" prop="fragment" align="center">
          <template slot-scope="{ row }">
            <el-input v-model="row.fragment" size="mini" style="width: 100px" @blur="row.edi = !row.edi" v-if="row.edi"></el-input>
            <span class="table-price" v-else>{{ '¥ ' + row.fragment }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center">
          <template slot-scope="{ row }">
            <el-button @click="row.edi = !row.edi" size="mini" type="primary" plain v-if="row.edi == false && row.oldprice == row.fragment">更改报价</el-button>
            <el-button @click="row.edi = !row.edi" size="mini" type="success" plain v-if="row.edi == true">完成修改</el-button>
            <el-button @click="row.edi = !row.edi" size="mini" type="primary" plain v-if="row.edi == false && row.oldprice != row.fragment">已更改</el-button>
            <el-button @click="row.fragment = row.oldprice" size="mini" type="danger" plain v-if="row.edi == false && row.oldprice != row.fragment">还原</el-button>
          </template>
        </el-table-column>
      </el-table>

      <span slot="footer">
        <el-button @click="handleSaveCache">取消</el-button>
        <el-button type="primary" @click="handleSave">保存并更新价格</el-button>
      </span>
    </el-dialog>

    <!-- 产品详情 -->
    <product-tpl ref="productInfo" />

    <!-- 报价详情 -->
    <el-dialog v-dialogDragBox :visible.sync="bjview" width="1000px">
      <!-- 产品列表 -->
      <template v-if="info.fragments">
        <div class="goods" v-for="(item, index) in info.fragments" :key="index">
          <div class="goods1">{{ item[0].stageName }}</div>
          <div class="goods2" v-for="(item2, index2) in item" :key="`${index2}+'a'`">
            <div class="goods2-1" @click="f_id = f_id == item2.fragmentId ? -1 : item2.fragmentId">
              <div>{{ item2.templateName }}</div>
              <div v-for="(item3, index3) in item2.ext" :key="`${index3}+'b'`">{{ item3.itemName }}:{{ item3.value }} {{ item3.itemUnit }}</div>
              <div style="position: absolute; right: 100px">
                小计 :
                <span style="color: #ec2454; font-weight: bold">¥ {{ item2.price }}</span>
              </div>
              <img :style="f_id == item2.fragmentId ? 'transform: rotate(180deg)' : 'transform: rotate(0deg)'" class="goods2-1-jt" src="../../../../public/imgs/下箭头.png" />
            </div>
            <el-collapse-transition>
              <div class="goods2-2" v-if="f_id == item2.fragmentId">
                <div class="tmpbox">
                  <div class="tmpbox-1">
                    {{ item2.templateName }}
                    <span class="innerQuote-form-link" @click="handleProductInfo(item2)">{{ item2.hasOwnProperty('product') ? `关联产品：${item2.product.productName} / ${item2.product.specs}` : '' }}</span>
                    {{ item2.hasOwnProperty('k') ? `关联K线：${item2.k.name}` : '' }}
                  </div>
                  <div class="show" v-if="item2" style="background-color: white">
                    <div class="showitem">
                      <template v-for="(item3, inde3x) in item2.fragmentItems.show">
                        <template v-if="item3.itemType == 'select'">
                          <div class="showitem2" :key="`${inde3x}+'c'`">
                            <div>
                              {{ item3.itemName }}
                            </div>
                            <div>
                              <el-select v-model="item3.value" placeholder="请选择" style="width: 100%" disabled>
                                <el-option v-for="option in item3.values" :key="option.itemValueId" :label="option.valueName" :value="option.value"></el-option>
                              </el-select>
                            </div>
                          </div>
                        </template>

                        <template v-if="item3.itemType == 'input'">
                          <div class="showitem2" :key="`${inde3x}+'c'`">
                            <div>
                              {{ item3.itemName }}
                            </div>
                            <div style="position: relative">
                              <el-input v-model="item3.value" :placeholder="item3.placeholder" disabled></el-input>
                              <div class="unit">{{ item3.itemUnit }}</div>
                            </div>
                          </div>
                        </template>
                        <template v-if="item3.itemType == 'input-radio'">
                          <div class="showitem2" :key="`${inde3x}+'c'`">
                            <div>
                              {{ item3.itemName }}
                            </div>
                            <div style="position: relative">
                              <el-input v-model="item3.value" :placeholder="item3.placeholder" disabled></el-input>
                              <div class="unit">{{ item3.itemUnit }}</div>
                            </div>
                          </div>
                        </template>

                        <template v-if="item3.itemType == 'input-select'">
                          <div class="showitem2" style="position: relative" :key="`${inde3x}+'c'`">
                            <div>
                              {{ item3.itemName }}
                            </div>
                            <div>
                              <el-input v-model="item3.value" :placeholder="item3.placeholder" disabled></el-input>
                              <template v-for="(u, ui) in JSON.parse(item3.itemUnit)">
                                <div class="unit" v-if="u.convert == 1" :key="`${ui}+'d'`">{{ u.name }}</div>
                              </template>
                            </div>
                            <div style="position: absolute; top: 48px; right: 5px; border: 0px solid; height: 20px; display: flex; line-height: 20px; font-size: 12px; color: #999"></div>
                          </div>
                        </template>
                      </template>
                    </div>
                    <div class="showimg">
                      <template v-if="item2.dynamicType == 'image'">
                        <img :src="'http://www.ziyouke.net/prod-api' + item2.dynamic" />
                      </template>
                      <template v-else>
                        <div style="width: 90%; margin: 10px auto; font-size: 12px; color: #999" v-html="item2.dynamic"></div>
                      </template>
                    </div>
                  </div>
                  <div class="tmpbox-2">计算结果</div>
                  <div class="show" style="background: #f1f3f8">
                    <div class="showitem">
                      <template v-for="(item3, inde3x) in item2.fragmentItems.result">
                        <template v-if="item3.itemType == 'input'">
                          <div class="showitem2" :key="`${inde3x}+'e'`">
                            <div>
                              {{ item3.itemName }}
                            </div>
                            <div style="position: relative">
                              <el-input v-model="item3.value" :placeholder="item3.placeholder" disabled></el-input>
                              <div class="unit">{{ item3.itemUnit }}</div>
                            </div>
                          </div>
                        </template>
                        <template v-if="item3.itemType == 'input-expression'">
                          <div class="showitem2" :key="`${inde3x}+'e'`">
                            <div>
                              {{ item3.itemName }}
                            </div>
                            <div style="position: relative">
                              <el-input v-model="item3.value" :placeholder="item3.placeholder" disabled></el-input>
                              <div class="unit">{{ item3.itemUnit }}</div>
                            </div>
                          </div>
                        </template>
                        <template v-if="item3.itemType == 'input-reference'">
                          <div class="showitem2" :key="`${inde3x}+'e'`">
                            <div>
                              {{ item3.itemName }}
                            </div>
                            <div style="position: relative">
                              <el-input v-model="item3.value" :placeholder="item3.placeholder" disabled></el-input>
                              <div class="unit">{{ item3.itemUnit }}</div>
                            </div>
                          </div>
                        </template>
                      </template>
                    </div>
                    <div class="showimg" style="border: 0"></div>
                  </div>
                </div>
              </div>
            </el-collapse-transition>
          </div>
        </div>
      </template>
      <el-empty v-else />
    </el-dialog>

    <!-- 历史报价 -->
    <el-dialog v-dialogDragBox title="历史报价" :visible.sync="historyview" width="40%">
      <div class="history">
        <div class="history-item" :class="{ active: item.value === dayValue }" v-for="item in dayOptions" :key="item.value" @click="handleChangeDay(item)">{{ item.label }}</div>
      </div>
      <div v-if="history.length">
        <line-chart-history :chart-data="history" />
        <el-table :data="history" border style="width: 96%; margin: 20px auto">
          <el-table-column label="序号" width="180">
            <template slot-scope="scope">
              {{ scope.$index + 1 }}
            </template>
          </el-table-column>
          <el-table-column prop="price" label="历史报价"></el-table-column>
          <el-table-column prop="createTime" label="时间"></el-table-column>
        </el-table>
      </div>
      <el-empty v-else />
    </el-dialog>

    <!-- 批量收藏 -->
    <template v-if="ids.length">
      <div class="mark" v-if="isCollect" @click="isCollect = !isCollect"></div>
      <div class="collectAll">
        <div class="collectAll-box">
          <div class="collectAll-title">已选择 {{ ids.length }} 项</div>
          <el-tooltip content="收藏到其他收藏夹" placement="top" effect="dark">
            <div class="collectAll-btn">
              <span :class="{ active: isCollect }" @click="isCollect = !isCollect">收藏到</span>
            </div>
          </el-tooltip>
          <el-tooltip content="生成图表" placement="top" effect="dark">
            <div class="collectAll-btn">
              <span :class="{ active: isCollect }" @click="handleCharts">生成图表</span>
            </div>
          </el-tooltip>
          <el-tooltip content="退出" placement="top" effect="dark">
            <div class="collectAll-close" @click="handleClose">
              <i class="el-icon-close"></i>
            </div>
          </el-tooltip>
        </div>
      </div>
      <el-collapse-transition>
        <div class="collectAll-list-box" v-if="isCollect">
          <div class="collectAll-list-close">
            <span>请选择收藏夹</span>
            <i class="el-icon-close" @click="isCollect = !isCollect"></i>
          </div>
          <div class="collectAll-list">
            <div class="item" :class="{ disabled: item.storeId === storeId }" v-for="item in collectList" :key="item.storeId" @click="handleColletAll(item)">{{ item.dirName }}</div>
          </div>
        </div>
      </el-collapse-transition>
    </template>

    <!-- 饼图 -->
    <el-dialog v-dialogDragBox title="报价列表饼状图" :visible.sync="pieOpen" width="800px">
      <pie-chart-formula :chart-data="pieData" v-if="pieOpen" height="500px" title="报价列表饼状图" />
      <div slot="footer">
        <el-button type="primary" @click="pieOpen = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Sortable from 'sortablejs'
import { getlistb, shoucTo, delshouc, shoucGo } from '@/api/houtai/shoucang'
import { getProduct } from '@/api/system/product'
import { getPrivateduct } from '@/api/system/privateduct'
import { draft_save, draft_get, stage, quotedel, draft_del, list_with, quota_edi, list_with_add, list, fb, bj_fin, listItemCopy, new_price, quotesel, listHistoryList, createPieCharts, publishPreCheck, quoteInnerDraft, quoteInnerDraftSave, quoteInnerDraftIgnore } from '@/api/houtai/formula'
import ProductTpl from '@/views/public/product/dialog'
import CollectTpl from '@/views/components/collect'
import LineChartHistory from '@/views/dashboard/LineChartHistory'
import PieChartFormula from '@/views/dashboard/PieChartFormula'
export default {
  name: 'Offersonlist',
  components: { ProductTpl, CollectTpl, LineChartHistory, PieChartFormula },
  data() {
    return {
      key: 1,
      // 搜索条件
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        isEditing: false,
        keyword: undefined,
        isInner: true,
        createBy: undefined,
        serial: undefined,
        productName: undefined,
        productCode: undefined,
        specs: undefined,
        model: undefined,
        materialQuality: undefined,
        surface: undefined,
        source: undefined,
      },
      // 收藏夹ID
      storeId: undefined,
      // 是否显示收藏夹列表
      isCollect: false,
      // 收藏夹列表
      collectList: [],
      // 加载动画
      loading: true,
      // 选中数组
      ids: [],
      // 非多个禁用
      multiple: true,
      // 列表数据
      list: [],
      // 总条数
      total: 0,
      // 表格筛选
      columns: [
        { key: 0, label: `序号`, visible: true },
        { key: 1, label: `报价编号`, visible: true },
        { key: 2, label: `报价名称`, visible: true },
        { key: 3, label: `未税总价`, visible: true },
        { key: 4, label: `含税总价 `, visible: true },
        { key: 5, label: `毛总重`, visible: true },
        { key: 6, label: `净总重`, visible: true },
        { key: 7, label: `报价产品`, visible: true },
        { key: 8, label: `产品规格`, visible: true },
        { key: 9, label: `创建时间`, visible: true },
        { key: 10, label: `创建时间`, visible: true },
        { key: 11, label: `报单人`, visible: true }
      ],
      // 历史报价
      history: [],
      historyview: false,
      historyId: undefined,
      dayOptions: [
        { label: '7天', value: 7 },
        { label: '15天', value: 15 },
        { label: '30天', value: 30 },
        { label: '90天', value: 90 }
      ],
      dayValue: 7,
      // 报价详情
      bjview: false,
      info: {},
      f_id: -1,
      // 草稿数据
      cache: {},
      // 阶段数据
      stageList: [],
      stageChecked: undefined,
      radioOptions: [
        { label: 'yes', title: '已关联元素' },
        { label: 'no', title: '未关联元素' }
      ],
      radioChcked: 'yes',
      // 更新报价
      updateOpen: false,
      // 生产材料
      material: {},
      // 外采品成品
      outsourcing: {},
      // 表格列宽
      tableWidth: [],
      // 拖拽
      sortable: null,
      // 饼图
      pieOpen: false,
      pieData: [],
      showMore: false
    }
  },
  created() {
    this.getStageList()
    this.getCacheList()
  },
  methods: {
    // 判断json是否包含某个字段
    hasOwn(obj, key) {
      if (obj) return Object.prototype.hasOwnProperty.call(obj, key)
      else return false
    },
    // 判断当前设备是电脑、平板还是手机
    isPC() {
      let userAgentInfo = navigator.userAgent
      let Agents = ['Android', 'iPhone', 'SymbianOS', 'Windows Phone', 'iPad', 'iPod']
      let flag = true
      for (let v = 0; v < Agents.length; v++) {
        if (userAgentInfo.indexOf(Agents[v]) > 0) {
          flag = false
          break
        }
      }
      return flag
    },
    // 检查是否可以选中
    chckeSelect(row, index) {
      if (row.mine) {
        return true
      } else {
        return false
      }
    },
    // 所有阶段
    getStageList() {
      stage().then(res => {
        if (res.code === 200) {
          const arr = []
          res.data.map(item => {
            if (item.stage === 'material' || item.stage === 'outsourcing') arr.push(item)
          })
          this.stageList = arr
          this.stageChecked = arr[0].stage
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 草稿数据
    getCacheList() {
      quoteInnerDraft().then(res => {
        if (res.code === 200) {
          if (res.data) this.cache = res.data.json
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 列表数据
    async getList() {
      const pageNum = Number(localStorage.getItem('offersonlistpageNum'))
      if (pageNum) {
        this.queryParams.pageNum = pageNum
        localStorage.removeItem('offersonlistpageNum')
      }
      const storeId = localStorage.getItem('offersonliststoreId')
      if (storeId != null && storeId != 'undefined') {
        this.storeId = storeId
        localStorage.removeItem('offersonliststoreId')
      }
      if (this.storeId) {
        this.loading = true
        const query = { ...this.queryParams, ...{ storeId: this.storeId } }
        let { code, msg, rows, total } = await getlistb(query)
        if (code === 200) {
          if (rows.length) {
            await Promise.all(
              rows.map(async item => {
                item.check = false
                const price = await new_price({ quoteId: item.id })
                if (price.code === 200) item.totalPrice = price.data
                if (item.productId) {
                  if (item.bindSource === 'common') {
                    const info = await getProduct(item.productId)
                    if (info.code === 200) item.product = info.data || {}
                  }
                  if (item.bindSource === 'private') {
                    const info = await getPrivateduct(item.productId)
                    if (info.code === 200) item.product = info.data || {}
                  }
                }
              })
            )
          }
          this.key = Math.random()
          this.list = rows
          this.loading = false
          this.total = total
          if (total && this.isPC()) {
            this.$nextTick(() => {
              this.rowDrop()
            })
          }
        } else {
          this.$message.error(msg)
        }
      } else {
        this.loading = true
        const query = { ...this.queryParams, ...{ name: this.queryParams.keyword } }
        list(query).then(res => {
          if (res.code === 200) {
            for (let x = 0; x < res.rows.length; x++) {
              res.rows[x].check = false
              new_price({ quoteId: res.rows[x].id }).then(res2 => {
                if (res.code == 200) {
                  res.rows[x].totalPrice = res2.data
                }
              })
            }
            this.key = Math.random()
            this.list = res.rows
            this.loading = false
            this.total = res.total
          } else {
            this.$message.error(res.msg)
          }
        })
      }
      const tableWidth = localStorage.getItem('TableSonColWidth')
      if (tableWidth) this.tableWidth = JSON.parse(tableWidth)
      this.isCollect = false
      this.multiple = false
      this.ids = []
    },
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 重置搜索
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        isEditing: false,
        createBy: undefined,
        keyword: undefined,
        isInner: true,
        serial: undefined,
        productName: undefined,
        productCode: undefined,
        specs: undefined,
        model: undefined,
        materialQuality: undefined,
        surface: undefined,
        source: undefined,
      }
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 多选
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.multiple = !selection.length
    },
    // 改变列宽
    handleColChange(newWidth, oldWidth, column, event) {
      setTimeout(() => {
        var TableColWidth = []
        var applyTable = document.getElementById('table')
        var applyTableColgroup = applyTable.getElementsByTagName('colgroup')[0]
        var applyTableCol = applyTableColgroup.getElementsByTagName('col')
        for (var i = 0; i < applyTableCol.length; i++) {
          TableColWidth.push(applyTableCol[i].width)
        }
        localStorage.setItem('TableSonColWidth', JSON.stringify(TableColWidth))
      }, 100)
    },
    // 完成草稿
    handleComplete() {
      this.material = this.cache.material
      this.outsourcing = this.cache.outsourcing
      this.stageChecked = 'material'
      this.radioChcked = 'yes'
      this.updateOpen = true
    },
    // 忽略草稿
    handleIgnore() {
      quoteInnerDraftIgnore().then(res => {
        if (res.code === 200) {
          this.cache = {}
          this.getCacheList()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 更新报价
    handleUpdate() {
      if (this.ids.length === 0) return
      const quoteIds = this.ids.join(',')
      // 生产材料
      list_with({ quoteIds, stage: 'material' }).then(res => {
        if (res.code === 200) {
          if (res.data.list) {
            res.data.list.map(item => {
              item.edi = false
              item.oldprice = item.price
              if (item.quoteList) {
                item.quoteList.map(itt => {
                  const fragment = itt.fragItems.find(ite => ite.itemId === itt.unitPriceId)
                  itt.fragment = fragment.value
                })
              }
            })
          }
          if (res.data.noBindQuote) {
            res.data.noBindQuote.map(item => {
              item.edi = false
              const fragment = item.fragItems.find(ite => ite.itemId === item.unitPriceId)
              item.fragment = fragment.value
              item.oldprice = fragment.value
            })
          }
          this.material = res.data
        } else {
          this.$message.error(res.msg)
        }
      })
      // 外采半成品
      list_with({ quoteIds, stage: 'outsourcing' }).then(res => {
        if (res.code === 200) {
          if (res.data.list) {
            res.data.list.map(item => {
              item.edi = false
              item.oldprice = item.price
              if (item.quoteList) {
                item.quoteList.map(itt => {
                  const fragment = itt.fragItems.find(ite => ite.itemId === itt.unitPriceId)
                  itt.fragment = fragment.value
                })
              }
            })
          }
          if (res.data.noBindQuote) {
            res.data.noBindQuote.map(item => {
              item.edi = false
              const fragment = item.fragItems.find(ite => ite.itemId === item.unitPriceId)
              item.fragment = fragment.value
              item.oldprice = fragment.value
            })
          }
          this.outsourcing = res.data
        } else {
          this.$message.error(res.msg)
        }
      })
      this.stageChecked = 'material'
      this.radioChcked = 'yes'
      this.updateOpen = true
    },
    // 改变阶段
    handleChangeStage(item) {
      this.stageChecked = item.stage
      this.radioChcked = 'yes'
    },
    // 改变是否关联元素
    handleChangeRadio(e) {
      this.radioChcked = e
    },
    // 保存缓存
    handleSaveCache() {
      const material = this.material
      const outsourcing = this.outsourcing
      const list = this.ids
      quoteInnerDraftSave({ json: { material, outsourcing, list } }).then(res => {
        if (res.code === 200) {
          this.updateOpen = false
          this.getCacheList()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 保存并更新价格
    async handleSave() {
      let data = []
      const materialList = this.material.hasOwnProperty('list') ? this.material.list.filter(item => item.stagePriceId && item.price != item.oldprice) : []
      const materialnoBindQuote = this.material.hasOwnProperty('noBindQuote') ? this.material.noBindQuote.filter(item => item.fragment != item.oldprice) : []
      const outsourcingList = this.outsourcing.hasOwnProperty('list') ? this.outsourcing.list.filter(item => item.stagePriceId && item.price != item.oldprice) : []
      const outsourcingnoBindQuote = this.outsourcing.hasOwnProperty('noBindQuote') ? this.outsourcing.noBindQuote.filter(item => item.fragment != item.oldprice) : []

      for (var x = 0; x < materialList.length; x++) {
        for (var y = 0; y < materialList[x].quoteList.length; y++) {
          data.push({
            djItemId: materialList[x].quoteList[y].unitPriceId,
            fragmentId: materialList[x].quoteList[y].fragmentId,
            newPrice: materialList[x].price,
            stagePriceId: materialList[x].stagePriceId,
            quoteId: materialList[x].quoteList[y].quoteId
          })
        }
      }
      for (var x = 0; x < materialnoBindQuote.length; x++) {
        data.push({
          djItemId: materialnoBindQuote[x].unitPriceId,
          fragmentId: materialnoBindQuote[x].fragmentId,
          newPrice: materialnoBindQuote[x].fragment,
          quoteId: materialnoBindQuote[x].quoteId
        })
      }
      for (var x = 0; x < outsourcingList.length; x++) {
        for (var y = 0; y < outsourcingList[x].quoteList.length; y++) {
          data.push({
            djItemId: outsourcingList[x].quoteList[y].unitPriceId,
            fragmentId: outsourcingList[x].quoteList[y].fragmentId,
            newPrice: outsourcingList[x].price,
            stagePriceId: outsourcingList[x].stagePriceId,
            quoteId: outsourcingList[x].quoteList[y].quoteId
          })
        }
      }
      for (var x = 0; x < outsourcingnoBindQuote.length; x++) {
        data.push({
          djItemId: outsourcingnoBindQuote[x].unitPriceId,
          fragmentId: outsourcingnoBindQuote[x].fragmentId,
          newPrice: outsourcingnoBindQuote[x].fragment,
          quoteId: outsourcingnoBindQuote[x].quoteId
        })
      }
      const result = [...materialList, ...outsourcingList]
      result.map(async item => {
        const resData = { priceId: item.stagePriceId, price: item.price, position: 'stagePrice' }
        const quotaedi = await quota_edi(resData)
      })

      await list_with_add(data).then(res => {
        if (res.code == 200) {
          this.$message.success('一键更新成功')
          this.updateOpen = false
          this.getList()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 单个收藏
    handleCollet(row) {
      this.$refs.collect.handleChange(row.id)
    },
    // 提交收藏
    handleColletSubmit(storeId, data) {
      if (!Array.isArray(data)) data = [data]
      shoucTo({ storeId, valueIdList: data }).then(res => {
        this.getList()
        this.$modal.msgSuccess('操作成功')
      })
    },
    // 批量收藏
    handleColletAll(row) {
      const storeId = row.storeId
      const valueIdList = this.ids
      if (storeId === this.storeId) return
      shoucTo({ storeId, valueIdList }).then(res => {
        this.$modal.msgSuccess('操作成功')
        this.getList()
        this.handleClose()
      })
    },
    // 关闭多选
    handleClose() {
      this.isCollect = false
      this.multiple = false
      this.ids = []
      this.$refs.table.clearSelection()
    },
    // 产品详情
    handleProductInfo(row, type) {
      this.$refs.productInfo.handleView(row.product, type)
    },
    // 报价详情
    handleOffer(row) {
      if (row.mine) {
        quotesel({ quoteId: row.id }).then(res => {
          if (res.code === 200) {
            this.info = res.data
            this.bjview = true
          } else {
            this.$message.error(res.msg)
          }
        })
      }
    },
    // 历史报价
    handleHistory(row) {
      this.dayValue = 7
      this.historyId = row.id
      this.getHistoryInfo()
      this.historyview = true
    },
    // 历史报价数据
    getHistoryInfo() {
      const query = { quoteId: this.historyId, days: this.dayValue }
      listHistoryList(query).then(res => {
        if (res.data) this.history = res.data
      })
    },
    // 改变历史天数
    handleChangeDay(row) {
      this.dayValue = row.value
      this.getHistoryInfo()
    },
    // 修改
    handleEdit(row) {
      const pageNum = this.queryParams.pageNum
      localStorage.setItem('offersonlistpageNum', pageNum)
      const storeId = this.storeId
      localStorage.setItem('offersonliststoreId', storeId)
      bj_fin({ quoteId: row.id }).then(res => {
        this.$router.push({ path: '/offer/offerson/sonaddition', query: { id: row.id } })
      })
    },
    // 复制
    handleCopy(row) {
      const quoteId = row.id
      listItemCopy({ quoteId }).then(res => {
        if (res.code === 200) {
          this.$message.success('复制成功')
          this.getList()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 删除
    // prettier-ignore
    handleDelete(row) {
      const data = { quoteId: row.id }
      this.$modal.confirm('是否确认删除选中的数据？').then(function() {
        return quotedel(data)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess('删除成功')
      }).catch(() => {})
    },
    // 删除收藏
    // prettier-ignore
    handleCollectDelete(row) {
      const data = { storeId: this.storeId, valueId: row.id }
      this.$modal.confirm('是否确认删除选中的收藏？').then(function() {
        return delshouc(data)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess('删除成功')
      }).catch(() => {})
    },
    // 批量删除报价
    // prettier-ignore
    handleDeleteAll() {
      if(this.ids.length){
        this.$confirm('是否确认删除选中的数据?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          if (this.ids.length === 0) return
          for (let i = 0; i < this.ids.length; i++) {
            await quotedel({ quoteId: this.ids[i] })
          }
          this.$message.success('删除成功')
          await this.getList()
        }).catch(() => {})
      }
    },
    //行拖拽
    rowDrop() {
      const tbody = document.querySelector('.el-table__body-wrapper tbody')
      const _this = this
      this.sortable = Sortable.create(tbody, {
        ghostClass: 'sortable',
        animation: 180,
        delay: 0,
        onEnd({ newIndex, oldIndex }) {
          if (newIndex > oldIndex) {
            _this.handleMoveDown(newIndex, oldIndex)
          }
          if (newIndex < oldIndex) {
            _this.handleMoveUp(newIndex, oldIndex)
          }
        }
      })
    },
    // 上移
    handleMoveUp(newIndex, oldIndex) {
      const valueId = this.list[oldIndex].id
      shoucGo({ index: newIndex, storeId: this.storeId, valueId }).then(res => {
        const oldItem = this.list[oldIndex]
        this.list.splice(oldIndex, 1)
        this.list.splice(newIndex, 0, oldItem)
      })
    },
    // 下移
    handleMoveDown(newIndex, oldIndex) {
      const valueId = this.list[oldIndex].id
      shoucGo({ index: newIndex, storeId: this.storeId, valueId }).then(res => {
        const oldItem = this.list[oldIndex]
        this.list.splice(oldIndex, 1)
        this.list.splice(newIndex, 0, oldItem)
      })
    },
    // 生成饼状图
    handleCharts() {
      const quoteIds = this.ids.toString()
      createPieCharts({ quoteIds }).then(res => {
        if (res.code === 200) {
          this.pieData = res.data
          this.pieOpen = true
        } else {
          this.$message.error(res.msg)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  position: relative;
}
.cache {
  position: absolute;
  right: 20px;
  display: flex;
  align-items: center;
  height: 60px;
  background-color: #f9e1e1;
  border-radius: 5px;
  border: 1px solid #fea2a2;
  padding: 0 10px;
  z-index: 999;
  &-txt {
    display: inline-flex;
    align-items: center;
    color: #f50e0e;
    font-size: 14px;
  }
  &-btn {
    border-radius: 5px 5px 5px 5px;
    border: 1px solid #f50e0e;
    text-align: center;
    line-height: 38px;
    font-size: 12px;
    color: #f50e0e;
    cursor: pointer;
    padding: 0 25px;
    margin-left: 10px;
    &:hover,
    &.active {
      background-color: #f50e0e;
      color: #fff;
    }
  }
}
::v-deep .pagination-container {
  padding: 32px 0 0 !important;
  display: flex;
  align-items: center;
  background-color: transparent;
  .el-pagination {
    width: 100%;
    text-align: right;
    .el-pagination__total {
      float: left;
    }
  }
}
.header {
  display: flex;
  align-items: center;
  margin-bottom: 40px;
  .search {
    display: flex;
    align-items: center;
    .search-input {
      width: 170px;
      height: 32px;
      font-size: 12px;
      -webkit-box-shadow: 0 1px 9px 0 rgba(0, 0, 0, 0.07);
      box-shadow: 0 1px 9px 0 rgba(0, 0, 0, 0.07);
      border: 1px solid #cbd6e2;
      overflow: hidden;
      border-top-left-radius: 5px;
      border-bottom-left-radius: 5px;
      input {
        width: 100%;
        height: 100%;
        border: 0;
        outline: none;
        line-height: 32px;
        padding: 0 10px;
        font-size: 12px;
        color: #999;
      }
    }
    .search-btn {
      width: 65px;
      line-height: 32px;
      background-color: #2e73f3;
      color: #fff;
      text-align: center;
      font-size: 12px;
      cursor: pointer;
      border-top-right-radius: 5px;
      border-bottom-right-radius: 5px;
    }
  }
  .handle-btn {
    width: 100px;
    height: 32px;
    line-height: 32px;
    text-align: center;
    font-size: 12px;
    cursor: pointer;
    background: #fff;
    -webkit-box-shadow: 0 1px 9px 0 rgba(0, 0, 0, 0.07);
    box-shadow: 0 1px 9px 0 rgba(0, 0, 0, 0.07);
    border-radius: 5px;
    border: 1px solid #2e73f3;
    color: #2e73f3;
    font-weight: 700;
    margin-left: 15px;
    &.disabled {
      cursor: no-drop;
      opacity: 0.8;
    }
  }
}
.custom-table {
  box-shadow: 0px 0px 26px 0px rgba(0, 0, 0, 0.05);
  border-radius: 5px;
  overflow: hidden;
  font-size: 12px;
  ::v-deep .el-table__body-wrapper {
    .el-table__row--striped {
      td.el-table__cell {
        background-color: #f0f3f9;
      }
    }
    .el-table__row {
      td.el-table__cell {
        border-right-color: transparent;
        &.cell-new {
          position: relative;
        }
        &.table-btn-cell {
          .cell {
            text-overflow: unset;
          }
        }
      }
      &.el-table__row--striped {
        &:hover {
          td.el-table__cell {
            background-color: #f0f3f9;
          }
        }
      }
      &:hover {
        td.el-table__cell {
          background-color: transparent;
        }
      }
      .el-table__cell:first-child {
        .cell {
          text-overflow: unset;
        }
      }
    }
  }
  .el-button + .el-button,
  .el-checkbox.is-bordered + .el-checkbox.is-bordered {
    margin-left: 5px;
  }
  .table-new {
    display: inline-block;
    width: 28px;
    height: 14px;
    background: url(../../../assets/images/new.png) center no-repeat;
    position: absolute;
    top: 5px;
    right: 10px;
  }
  .table-btn {
    display: inline-block;
    padding: 0 10px;
    line-height: 30px;
    background-color: #e0ebff;
    border-radius: 5px;
    border: 1px solid #2e73f3;
    font-size: 12px;
    color: #2e73f3;
    cursor: pointer;
    transition: all 0.2s;
    &:hover,
    &.active {
      background-color: #2e73f3;
      color: #fff;
    }
  }
  &.drop-table {
    ::v-deep .el-table__row {
      position: relative;
      &:hover {
        cursor: move;
        &:after {
          transition: 0.3s;
          content: '按住可进行拖拽排序';
          position: absolute;
          left: 50%;
          top: -40px;
          z-index: 200;
          background-color: #303133;
          border-radius: 4px;
          padding: 10px;
          font-size: 12px;
          min-width: 10px;
          word-wrap: break-word;
          color: #fff;
        }
      }
      .zindex {
        z-index: 2;
      }
    }
  }
}
.table-link {
  color: #2e73f3;
  font-size: 14px;
  font-weight: 700;
  cursor: pointer;
  position: relative;
  &:hover {
    text-decoration: underline;
  }
  &.disabled {
    opacity: 0.8;
    cursor: no-drop;
    &:hover {
      text-decoration: none;
    }
  }
}
::v-deep .history {
  display: flex;
  align-items: center;
  padding: 0 2%;
  &-item {
    padding: 5px 20px;
    margin-right: 10px;
    border: 1px solid #cccccc;
    border-radius: 5px;
    cursor: pointer;
    &:hover,
    &.active {
      border-color: #2e73f3;
      color: #2e73f3;
    }
  }
}
::v-deep .update-dialog {
  .el-dialog__body {
    padding: 0 20px 20px;
    .cache-tab {
      width: 100%;
      display: flex;
      line-height: 50px;
      font-size: 14px;
      color: #999;
      border-bottom: 1px solid #cbd6e2;
      .tab-item {
        padding: 0 50px;
        cursor: pointer;
        &.active {
          border-bottom: 2px solid #2e73f3;
          margin-bottom: -1px;
        }
      }
    }
    .cache-radio {
      margin: 15px 20px;
    }
    .el-table {
      border: 1px solid #cbd6e2;
      border-radius: 5px;
      font-size: 12px;
      th.el-table__cell {
        background-color: #f8f9fb;
      }
      .table-price {
        font-size: 16px;
        color: #ec2454;
      }
    }
    .el-table th.el-table__cell.is-leaf,
    .el-table td.el-table__cell {
      border-color: #cbd6e2;
    }
    .tr-table {
      border-radius: 0;
      border: 0;
      margin: -12px 0;
      tr,
      th.el-table__cell,
      td.el-table__cell {
        background-color: #f3f3f3;
      }
      .el-table__expand-column {
        .cell {
          opacity: 0;
        }
      }
    }
  }
}
.goods {
  width: 99.9%;
  margin-top: 5px;
  margin-bottom: 10px;
  box-shadow: 0px 0px 26px 0px rgba(0, 0, 0, 0.05);
  background-color: white;
  border-radius: 5px;
  margin-bottom: 20px;
  padding-bottom: 20px;
}
.goods1 {
  width: 100%;
  height: 37px;
  border-bottom: 1px solid #e7e9ec;
  line-height: 37px;
  text-indent: 20px;
  font-size: 14px;
  color: #666666;
}
.goods2 {
  width: 98%;
  margin: 20px auto 0;
  background-color: #f1f1f3;
  border-radius: 5px;
  border: 1px solid #cbd6e2;
}
.goods2-1 {
  width: 100%;
  height: 60px;
  display: flex;
  line-height: 60px;
  font-size: 14px;
  cursor: pointer;
  position: relative;
}
.goods2-1 > div:nth-child(1) {
  min-width: 10%;
  width: auto;
  margin-left: 20px;
  color: #333;
  font-weight: bold;
}
.goods2-1 > div {
  color: #999;
  margin-left: 30px;
}
.goods2-1-jt {
  width: 20px;
  height: 15px;
  position: absolute;
  right: 10px;
  top: 22px;
  transition: 0.2s;
}
.tmpbox {
  width: 98%;
  /* height: 900px; */
  margin: 20px auto;
  border: 1px solid #cbd6e2;
}
.tmpbox-1 {
  width: 100%;
  height: 46px;
  background: #ecf3ff;
  line-height: 46px;
  color: #2e73f3;
  font-size: 14px;
  text-indent: 39px;
}
.tmpbox-2 {
  width: 100%;
  height: 46px;
  background: #f1f3f8;
  line-height: 46px;
  color: #333;
  font-size: 14px;
  text-indent: 39px;
  border-top: 1px solid #cbd6e2;
  border-bottom: 1px solid #cbd6e2;
}
.show {
  width: 100%;
  display: flex;
  justify-content: space-around;
}
.showimg {
  width: 35%;
  border: 1px solid #cbd7e2;
  margin-top: 30px;
  margin-bottom: 30px;
}
.showimg > img {
  width: 100%;
  height: 100%;
}
.showitem {
  width: 55%;
  padding-top: 16px;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
.showitem2 {
  width: 49%;
  height: 46px;
  margin-bottom: 30px;
  line-height: 46px;
  display: flex;
  justify-content: space-between;
}
.showitem2 > div:nth-child(1) {
  width: 30%;
  color: #999999;
  font-size: 12px;
}
.showitem2 > div:nth-child(2) {
  width: 70%;
}
.unit {
  position: absolute;
  top: 0;
  right: 20px;
  font-size: 14px;
  color: #999;
}
</style>
