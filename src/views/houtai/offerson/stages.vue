<template>
  <div class="app-container bgcf9 vh-85">
    <div v-if="syncs.length" style="background-color: white; padding-top: 20px; box-shadow: 0px 0px 26px 0px rgba(0, 0, 0, 0.05); border-radius: 5px 5px 5px 5px; padding-bottom: 10px">
      <div class="txbox">
        <div>
          以下报价涉及到圆钢的价格变动，是否需要更新圆钢价格？
          <el-button size="mini" type="danger" @click="sync_edi(false)" plain>忽略</el-button>
          <el-button size="mini" type="danger" @click="sync_edi(true)">立即更新</el-button>
        </div>
      </div>
      <div class="box">
        <el-table ref="syncs" :data="syncs" stripe style="width: 100%" class="custom-table">
          <el-table-column align="center" type="index" label="序号"></el-table-column>
          <el-table-column prop="name" label="关联元素名称" align="center" show-overflow-tooltip min-width="150"></el-table-column>
          <el-table-column prop="price" label="价格" align="center" show-overflow-tooltip>
            <template slot-scope="{ row }">
              <span class="table-price">{{ '¥ ' + row.price }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="stage" label="所属阶段" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column prop="product_name" label="关联产品" align="center" show-overflow-tooltip min-width="150">
            <template slot-scope="{ row }">
              <span class="table-link" @click="handleView(row)">{{ row.product_name }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="product_code" label="产品编码" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column prop="specs" label="规格" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column prop="model" label="型号" align="center" show-overflow-tooltip></el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 列表 -->
    <el-table v-loading="loading" ref="list" :data="list" stripe style="width: 100%" class="custom-table" :default-sort="{ prop: 'priceNum', order: 'descending' }">
      <el-table-column align="center" type="index" label="序号"></el-table-column>
      <el-table-column prop="name" label="关联元素名称" align="center" show-overflow-tooltip min-width="150"></el-table-column>
      <el-table-column prop="price" label="价格" align="center" show-overflow-tooltip>
        <template slot-scope="{ row }">
          <span class="table-price">{{ '¥ ' + row.price }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="stage" label="所属阶段" align="center" show-overflow-tooltip></el-table-column>
      <el-table-column prop="product_name" label="关联产品" align="center" show-overflow-tooltip min-width="150">
        <template slot-scope="{ row }">
          <span class="table-link" @click="handleView(row)">{{ row.product_name }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="priceNum" label="关联报价" align="center" show-overflow-tooltip>
        <template slot-scope="{ row }">
          <el-button type="text" size="mini" @click="handleOpenOffer(row)" v-if="row.priceNum">{{ row.priceNum }}</el-button>
          <span v-else>{{ row.priceNum }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="product_code" label="产品编码" align="center" show-overflow-tooltip></el-table-column>
      <el-table-column prop="specs" label="规格" align="center" show-overflow-tooltip></el-table-column>
      <el-table-column prop="model" label="型号" align="center" show-overflow-tooltip></el-table-column>
      <el-table-column label="操作" align="center" width="220">
        <template slot-scope="{ row }">
          <el-button size="mini" type="info" @click="info(row)" plain>历史</el-button>
          <el-button size="mini" type="primary" @click="edi(row)" plain :disabled="!row.mine || !row.priceNum">修改</el-button>
          <el-button size="mini" type="danger" @click="del(row)" plain :disabled="!row.mine || !!row.priceNum">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="custom-pagination">
      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
    </div>

    <!-- 历史 -->
    <el-dialog v-dialogDragBox title="修改历史" :visible.sync="historyview" width="40%">
      <div class="history">
        <div class="history-item" :class="{ active: item.value === dayValue }" v-for="item in dayOptions" :key="item.value" @click="handleChangeDay(item)">{{ item.label }}</div>
      </div>
      <div v-if="history.length">
        <line-chart-history :chart-data="history" />
        <el-table :data="history" border style="width: 96%; margin: 20px auto">
          <el-table-column label="序号" width="180">
            <template slot-scope="scope">
              {{ scope.$index + 1 }}
            </template>
          </el-table-column>
          <el-table-column prop="price" label="阶段价格"></el-table-column>
          <el-table-column prop="createTime" label="时间"></el-table-column>
        </el-table>
      </div>
      <el-empty v-else />
    </el-dialog>

    <!-- 修改窗口 -->
    <el-dialog v-dialogDragBox title="修改" :visible.sync="ediview" width="40%">
      <div>
        <el-form ref="form" :model="edidata" label-width="80px">
          <el-form-item label="阶段名称">
            <el-input v-model="edidata.name"></el-input>
          </el-form-item>
          <el-form-item label="价格">
            <el-input v-model="edidata.price"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="edi_c()">修 改</el-button>
      </span>
    </el-dialog>

    <!-- 关联报价列表 -->
    <el-dialog v-dialogDragBox :title="offerTitle" :visible.sync="offerOpen" width="40%">
      <el-table :data="offerData" border :key="offerKey">
        <el-table-column align="center" type="index" label="序号" />
        <el-table-column align="center" prop="serial" label="报价编号" />
        <el-table-column align="center" prop="name" label="报价名称" />
        <el-table-column align="center" label="报价详情">
          <template slot-scope="{ row }">
            <el-button type="text" size="small" @click="handleOfferView(row)" icon="el-icon-view">报价详情</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <!-- 报价详情 -->
    <el-dialog v-dialogDragBox :visible.sync="offerView" width="1000px">
      <!-- 产品列表 -->
      <template v-if="offerInfo.fragments">
        <div class="goods" v-for="(item, index) in offerInfo.fragments">
          <!-- 标题 -->
          <div class="goods1">{{ item[0].stageName }}</div>
          <!-- 每一个历史阶段 -->
          <div class="goods2" v-for="(item2, index2) in item">
            <!-- 阶段标题 -->
            <div class="goods2-1" @click="f_id = f_id == item2.fragmentId ? -1 : item2.fragmentId">
              <!-- <div>{{ item2.seq }}. {{item2.templateName}}</div> -->
              <div>{{ item2.templateName }}</div>
              <div v-for="(item3, index3) in item2.ext">{{ item3.itemName }}:{{ item3.value }} {{ item3.itemUnit }}</div>
              <div style="position: absolute; right: 100px">
                小计 :
                <span style="color: #ec2454; font-weight: bold">¥ {{ item2.price }}</span>
              </div>
              <img :style="f_id == item2.fragmentId ? 'transform: rotate(180deg)' : 'transform: rotate(0deg)'" class="goods2-1-jt" src="../../../../public/imgs/下箭头.png" />
            </div>
            <el-collapse-transition>
              <div class="goods2-2" v-if="f_id == item2.fragmentId">
                <div class="tmpbox">
                  <div class="tmpbox-1">
                    {{ item2.templateName }}
                  </div>
                  <div class="show" v-if="item2" style="background-color: white">
                    <div class="showitem">
                      <template v-for="(item3, inde3x) in item2.fragmentItems.show">
                        <template v-if="item3.itemType == 'select'">
                          <div class="showitem2">
                            <div>
                              {{ item3.itemName }}
                            </div>
                            <div>
                              <el-select v-model="item3.value" placeholder="请选择" style="width: 100%" disabled>
                                <el-option v-for="option in item3.values" :key="option.itemValueId" :label="option.valueName" :value="option.value"></el-option>
                              </el-select>
                            </div>
                          </div>
                        </template>

                        <template v-if="item3.itemType == 'input'">
                          <div class="showitem2">
                            <div>
                              {{ item3.itemName }}
                            </div>
                            <div style="position: relative">
                              <el-input v-model="item3.value" :placeholder="item3.placeholder" disabled></el-input>
                              <div class="unit">{{ item3.itemUnit }}</div>
                            </div>
                          </div>
                        </template>
                        <template v-if="item3.itemType == 'input-radio'">
                          <div class="showitem2">
                            <div>
                              {{ item3.itemName }}
                            </div>
                            <div style="position: relative">
                              <el-input v-model="item3.value" :placeholder="item3.placeholder" disabled></el-input>
                              <div class="unit">{{ item3.itemUnit }}</div>
                            </div>
                          </div>
                        </template>

                        <template v-if="item3.itemType == 'input-select'">
                          <div class="showitem2" style="position: relative">
                            <div>
                              {{ item3.itemName }}
                            </div>
                            <div>
                              <el-input v-model="item3.value" :placeholder="item3.placeholder" disabled></el-input>
                              <div class="unit" v-for="(u, ui) in JSON.parse(item3.itemUnit)" v-if="u.convert == 1">
                                {{ u.name }}
                              </div>
                            </div>
                            <div style="position: absolute; top: 48px; right: 5px; border: 0px solid; height: 20px; display: flex; line-height: 20px; font-size: 12px; color: #999"></div>
                          </div>
                        </template>
                      </template>
                    </div>
                    <div class="showimg">
                      <template v-if="item2.dynamicType == 'image'">
                        <img :src="'http://www.ziyouke.net/prod-api' + item2.dynamic" />
                      </template>
                      <template v-else>
                        <div style="width: 90%; margin: 10px auto; font-size: 12px; color: #999" v-html="item2.dynamic"></div>
                      </template>
                    </div>
                  </div>
                  <div class="tmpbox-2">计算结果</div>
                  <div class="show" style="background: #f1f3f8">
                    <div class="showitem">
                      <template v-for="(item3, inde3x) in item2.fragmentItems.result">
                        <template v-if="item3.itemType == 'input'">
                          <div class="showitem2">
                            <div>
                              {{ item3.itemName }}
                            </div>
                            <div style="position: relative">
                              <el-input v-model="item3.value" :placeholder="item3.placeholder" disabled></el-input>
                              <div class="unit">{{ item3.itemUnit }}</div>
                            </div>
                          </div>
                        </template>
                        <template v-if="item3.itemType == 'input-expression'">
                          <div class="showitem2">
                            <div>
                              {{ item3.itemName }}
                            </div>
                            <div style="position: relative">
                              <el-input v-model="item3.value" :placeholder="item3.placeholder" disabled></el-input>
                              <div class="unit">{{ item3.itemUnit }}</div>
                            </div>
                          </div>
                        </template>
                        <template v-if="item3.itemType == 'input-reference'">
                          <div class="showitem2">
                            <div>
                              {{ item3.itemName }}
                            </div>
                            <div style="position: relative">
                              <el-input v-model="item3.value" :placeholder="item3.placeholder" disabled></el-input>
                              <div class="unit">{{ item3.itemUnit }}</div>
                            </div>
                          </div>
                        </template>
                      </template>
                    </div>
                    <div class="showimg" style="border: 0"></div>
                  </div>
                </div>
              </div>
            </el-collapse-transition>
          </div>
        </div>
      </template>
    </el-dialog>

    <!-- 产品信息 -->
    <product-dialog ref="productInfo" append-body></product-dialog>
  </div>
</template>
<script>
import * as ff from '@/api/houtai/formula'
import ProductDialog from '@/views/public/product/dialog'
import LineChartHistory from '@/views/dashboard/LineChartHistory'
import { getPrivateduct } from '@/api/system/privateduct'
export default {
  name: 'Offersonitem',
  components: { ProductDialog, LineChartHistory },
  data() {
    return {
      productView: false, //产品组件显示隐藏
      productData: {}, //产品组件数据
      checked: false,
      list: [],
      addview: false,
      ediview: false,
      adddata: {},
      search: '',
      sgoods: [],
      edidata: {},
      history: [],
      historyview: false,
      syncs: [],
      loading: true,
      // 关联报价列表弹框
      offerTitle: undefined,
      offerOpen: false,
      offerData: [],
      offerKey: 1,
      // 报价详情
      offerInfo: {},
      offerView: false,
      f_id: -1,
      historyId: undefined,
      dayOptions: [
        { label: '7天', value: 7 },
        { label: '15天', value: 15 },
        { label: '30天', value: 30 },
        { label: '90天', value: 90 }
      ],
      dayValue: 7,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        isInner: true
      },
      total: 0
    }
  },
  mounted() {
    ff.quoteInnerList().then(res => {
      this.syncs = res.data
    })
    this.getList()
  },
  methods: {
    // 所有列表
    async getList() {
      this.loading = true
      const { total, rows } = await ff.quota_list(this.queryParams)
      if (rows.length) {
        await Promise.all(
          rows.map(async item => {
            const { data } = await ff.listPriceNum({ priceId: item.id })
            const num = data.length ? data.length : 0
            item.priceData = data
            item.priceNum = num
          })
        )
      }
      this.list = rows
      this.total = total
      this.loading = false
    },
    sync_edi(e) {
      const list = this.syncs.map(item => item.id)
      if (e) {
        ff.quoteInnerSync({ stagePriceIds: list }).then(res => {
          ff.quoteInnerList().then(res => {
            this.syncs = res.data
          })
        })
      } else {
        ff.quoteInnerCancelSync({ stagePriceIds: list }).then(res => {
          ff.quoteInnerList().then(res => {
            this.syncs = res.data
          })
        })
      }
    },
    //历史
    info(e) {
      this.dayValue = 7
      this.historyId = e.id
      this.getHistoryInfo()
      this.historyview = true
    },
    getHistoryInfo() {
      const query = { priceId: this.historyId, days: this.dayValue }
      ff.quota_history(query).then(res => {
        if (res.data) this.history = res.data
      })
    },
    // 改变历史天数
    handleChangeDay(item) {
      this.dayValue = item.value
      this.getHistoryInfo()
    },
    //新增
    add() {
      ff.quota_add(this.adddata).then(res => {
        if (res.code == 200) {
          this.addview = false
          this.adddata = {}
          this.search = ''
          this.sgoods = []
          this.$message.success(res.msg)
          this.getList()
        }
      })
    },
    //搜索产品
    sgood() {
      let that = this
      ff.search({ keyword: that.search }).then(res => {
        that.sgoods = res.data
        if (res.data.length == 0) {
          that.$message('暂无产品')
        }
      })
    },
    //关联
    gl(e) {
      this.$set(this.adddata, 'productId', e.id)
    },
    //修改
    edi(e) {
      this.$set(this.edidata, 'priceId', e.id)
      this.$set(this.edidata, 'name', e.name)
      this.$set(this.edidata, 'price', e.price)
      this.$set(this.edidata, 'position', 'stagePrice')
      this.ediview = true
    },
    //修改2
    edi_c() {
      ff.quota_edi(this.edidata).then(res => {
        if (res.code == 200) {
          this.$message.success(res.msg)
          this.getList()
          this.ediview = false
        }
      })
    },
    //删除
    del(e) {
      ff.quota_del({ priceId: e.id }).then(res => {
        if (res.code == 200) {
          this.$message.success(res.msg)
          this.getList()
        }
      })
    },
    // 打开关联报价
    handleOpenOffer(item) {
      if (item.mine) {
        this.offerTitle = item.name || '' + '关联的报价列表'
        this.offerData = item.priceData
        this.offerKey = Math.random()
        this.offerOpen = true
      }
    },
    // 打开报价详情
    handleOfferView(item) {
      ff.quotesel({ quoteId: item.quoteId }).then(res => {
        this.offerInfo = res.data
        this.offerView = true
      })
    },
    // 产品详情
    handleView(item) {
      getPrivateduct(item['product_id']).then(res => {
        const { code, msg, data } = res
        if (code == 200) this.$refs.productInfo.handleView(data)
        else this.$message.console.error(msg)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
body {
  background: #f9f9f9;
}

::-webkit-scrollbar {
  width: 3px;
}

::-webkit-scrollbar-thumb {
  width: 3px;
  background-color: #ccc;
}

.box {
  width: 100%;
  min-height: 300px;
  width: 98%;
  margin: 20px auto;
  box-shadow: 0px 0px 26px 0px rgba(0, 0, 0, 0.05);
  border-radius: 5px 5px 5px 5px;
  border: 1px solid #dce3eb;
  border-top: none;
}

.box-title {
  width: 100%;
  height: 42px;
  background: #f8f9fb;
  border: 1px solid #cbd6e2;
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
  border-left: 0;
  border-right: 0;
  display: flex;
  line-height: 42px;
  justify-content: space-between;
}

.box-title > div {
  width: 9%;
  font-size: 12px;
  color: #999;
  text-align: center;
}

.box-item {
  width: 100%;
  display: flex;
  min-height: 82px;
  background: #f0f3f9;
  justify-content: space-between;
  line-height: 82px;
}

.box-item > div {
  width: 9%;
  text-align: center;
  font-size: 12px;
  color: #333;
}

.box > .box-item:nth-child(even) {
  background-color: white;
}

.txbox {
  width: 98%;
  height: 60px;
  background: rgba(245, 14, 14, 0.15);
  margin: auto;
  display: flex;
}

.txbox > div {
  margin: auto;
  color: #f50e0e;
  text-align: center;
}
.goods {
  width: 99.9%;
  margin-top: 5px;
  margin-bottom: 10px;
  box-shadow: 0px 0px 26px 0px rgba(0, 0, 0, 0.05);
  background-color: white;
  border-radius: 5px;
  margin-bottom: 20px;
  padding-bottom: 20px;
}

.goods1 {
  width: 100%;
  height: 37px;
  border-bottom: 1px solid #e7e9ec;
  line-height: 37px;
  text-indent: 20px;
  font-size: 14px;
  color: #666666;
}

.goods2 {
  width: 98%;
  margin: 20px auto 0;
  background-color: #f1f1f3;
  border-radius: 5px;
  border: 1px solid #cbd6e2;
}

.goods2-1 {
  width: 100%;
  height: 60px;
  display: flex;
  line-height: 60px;
  font-size: 14px;
  cursor: pointer;
  position: relative;
}

.goods2-1 > div:nth-child(1) {
  width: 10%;
  margin-left: 20px;
  color: #333;
  font-weight: bold;
}

.goods2-1 > div {
  color: #999;
  margin-left: 30px;
}

.goods2-1-jt {
  width: 20px;
  height: 15px;
  position: absolute;
  right: 10px;
  top: 22px;
  transition: 0.2s;
}
.tmpbox {
  width: 98%;
  /* height: 900px; */
  margin: 20px auto;
  border: 1px solid #cbd6e2;
}

.tmpbox-1 {
  width: 100%;
  height: 46px;
  background: #ecf3ff;
  line-height: 46px;
  color: #2e73f3;
  font-size: 14px;
  text-indent: 39px;
}

.tmpbox-2 {
  width: 100%;
  height: 46px;
  background: #f1f3f8;
  line-height: 46px;
  color: #333;
  font-size: 14px;
  text-indent: 39px;
  border-top: 1px solid #cbd6e2;
  border-bottom: 1px solid #cbd6e2;
}
.show {
  width: 100%;
  display: flex;
  justify-content: space-around;
}

.showimg {
  width: 35%;
  border: 1px solid #cbd7e2;
  margin-top: 30px;
  margin-bottom: 30px;
}

.showimg > img {
  width: 100%;
  height: 100%;
}

.showitem {
  width: 55%;
  padding-top: 16px;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.showitem2 {
  width: 49%;
  height: 46px;
  margin-bottom: 30px;
  line-height: 46px;
  display: flex;
  justify-content: space-between;
}

.showitem2 > div:nth-child(1) {
  width: 30%;
  color: #999999;
  font-size: 12px;
  /* width: 60px; */
}

.showitem2 > div:nth-child(2) {
  width: 70%;
}
.unit {
  position: absolute;
  top: 0;
  right: 20px;
  font-size: 14px;
  color: #999;
}
</style>
<style lang="scss" scoped>
::v-deep .history {
  display: flex;
  align-items: center;
  padding: 0 2%;
  &-item {
    padding: 5px 20px;
    margin-right: 10px;
    border: 1px solid #cccccc;
    border-radius: 5px;
    cursor: pointer;
    &:hover,
    &.active {
      border-color: #2e73f3;
      color: #2e73f3;
    }
  }
}
</style>
