<template>
  <div class="bgcf9 vh-85">
    <div class="custom-search flex" style="padding-top: 18px">
      <el-form :model="queryParams" ref="query" size="small" label-width="5em" :inline="true" @submit.native.prevent>
        <el-row :gutter="10">
          <el-col :xs="8" :sm="6" :lg="4">
            <el-form-item label="产品名称" prop="productName">
              <el-input v-model="queryParams.productName" placeholder="请输入产品名称" clearable @clear="handleQuery" @keyup.enter.native="handleQuery" />
            </el-form-item>
          </el-col>
          <el-col :xs="8" :sm="6" :lg="4">
            <el-form-item label="产品编码" prop="productCode">
              <el-input v-model="queryParams.productCode" placeholder="请输入产品编码" clearable @clear="handleQuery" @keyup.enter.native="handleQuery" />
            </el-form-item>
          </el-col>
          <el-col :xs="8" :sm="6" :lg="4">
            <el-form-item label="产品规格" prop="specs">
              <el-input v-model="queryParams.specs" placeholder="请输入产品规格" clearable @clear="handleQuery" @keyup.enter.native="handleQuery" />
            </el-form-item>
          </el-col>
          <el-col :xs="8" :sm="6" :lg="4">
            <el-form-item label="产品型号" prop="model">
              <el-input v-model="queryParams.model" placeholder="请输入产品型号" clearable @clear="handleQuery" @keyup.enter.native="handleQuery" />
            </el-form-item>
          </el-col>
          <el-col :xs="8" :sm="6" :lg="4">
            <el-form-item label="产品材质" prop="materialQuality">
              <el-input v-model="queryParams.materialQuality" placeholder="请输入产品材质" clearable @clear="handleQuery" @keyup.enter.native="handleQuery" />
            </el-form-item>
          </el-col>
          <el-col :xs="8" :sm="6" :lg="4" v-if="showAll">
            <el-form-item label="表面处理" prop="surface">
              <el-input v-model="queryParams.surface" placeholder="请输入表面处理" clearable @clear="handleQuery" @keyup.enter.native="handleQuery" />
            </el-form-item>
          </el-col>
          <el-col :xs="8" :sm="6" :lg="8" v-if="showAll">
            <el-form-item label="分类" prop="categoryId">
              <treeselect v-model="queryParams.categoryId" :options="categoryList" :normalizer="normalizer" :show-count="true" @input="handleSelect" placeholder="请选择产品分类" class="search-treeselect">
                <div slot="option-label" slot-scope="{ node }" class="category-flex">
                  <span>{{ node.label }}</span>
                  <span class="category-flex-desc">{{ node.raw.model ? `规格型号：${node.raw.model}` : '' }}</span>
                </div>
              </treeselect>
            </el-form-item>
          </el-col>
          <el-col :span="1.5">
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
            </el-form-item>
            <el-form-item>
              <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
            </el-form-item>
            <el-form-item>
              <el-button type="text" size="mini" @click="clickSearch">
                {{ word }}
                <i :class="showAll ? 'el-icon-arrow-up ' : 'el-icon-arrow-down'"></i>
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="classify flex">
      <div class="classify-item" :class="{ active: item.value === tabActive }" v-for="item in tabOptions" :key="item.value" @click="handleTabChange(item)">
        {{ item.label }}
      </div>
    </div>
    <div class="tableBox">
      <el-table v-loading="loading" ref="table" stripe :data="list" :key="key" @selection-change="handleSelectionChange" class="custom-table custom-table-cell5">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="产品名称" prop="productName" align="center" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <span class="table-link" @click="handleProductInfo(row)">{{ row.productName }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="picture1" label="图片" width="75">
          <template slot-scope="{ row }">
            <el-image :src="formatProductImg(row)" fit="cover" @click="handleImg(row)">
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline"></i>
              </div>
            </el-image>
          </template>
        </el-table-column>
        <el-table-column label="产品编码" align="center" prop="productCode" show-overflow-tooltip></el-table-column>
        <el-table-column label="规格" align="center" prop="specs" show-overflow-tooltip></el-table-column>
        <el-table-column label="型号" align="center" prop="model" show-overflow-tooltip></el-table-column>
        <el-table-column label="材质" align="center" prop="materialQuality" show-overflow-tooltip></el-table-column>
        <el-table-column label="表面处理" align="center" prop="surface" show-overflow-tooltip></el-table-column>
        <template v-if="tabActive === 0">
          <el-table-column label="供应数量" align="center" show-overflow-tooltip>
            <template slot-scope="{ row }">
              <span class="table-primary pointer" @click="handleView(row)">{{ stockFormat(row) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="促销价格" align="center" show-overflow-tooltip>
            <template slot-scope="{ row }">
              <span class="table-price pointer" @click="handleView(row)">{{ priceFormat(row) }}</span>
            </template>
          </el-table-column>
        </template>
        <template v-if="tabActive === 1">
          <el-table-column label="产品库存" align="center" show-overflow-tooltip>
            <template slot-scope="{ row }">
              <span class="table-primary">{{ parseFloat(row.stock) + row.unit }}</span>
            </template>
          </el-table-column>
          <el-table-column label="产品价格" align="center" show-overflow-tooltip>
            <template slot-scope="{ row }">
              <span class="table-price">{{ row.price + '元/' + row.priceUnit }}</span>
            </template>
          </el-table-column>
        </template>
        <el-table-column align="center" label="操作" width="120px">
          <template slot-scope="{ row }">
            <button type="button" class="table-btn success" @click="handleSend(row)">推送</button>
          </template>
        </el-table-column>
      </el-table>

      <div class="custom-pagination">
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
      </div>
    </div>

    <!-- 产品详情 -->
    <product-dialog ref="productInfo"></product-dialog>
    <!--  查看已设置过的报价  -->
    <el-dialog v-dialogDragBox title="查看促销品价格" :visible.sync="viewOpen" width="1150px" class="custom-dialog">
      <div style="padding: 0 20px">
        <el-table ref="viewTable" stripe border :data="priceList" :span-method="priceSpanMethod" class="custom-table">
          <el-table-column label="产品名称" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
              <span class="table-link" @click="handleProductInfo(info)">{{ info.productName }}</span>
            </template>
          </el-table-column>
          <el-table-column label="规格" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ info.specs }}</template>
          </el-table-column>
          <el-table-column label="材质" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ info.materialQuality }}</template>
          </el-table-column>
          <el-table-column label="表面处理" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ info.surface }}</template>
          </el-table-column>
          <el-table-column label="供应数量" align="center" show-overflow-tooltip>
            <template slot-scope="{ row }">
              <span class="table-primary">{{ row.stock + (row.method === 'ton' ? '吨' : info.unit) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="促销价格" align="center" show-overflow-tooltip>
            <template slot-scope="{ row }">
              <span class="table-price">{{ '￥' + row.price + '元' + (row.method === 'ton' ? '/吨' : `/${info.unit}`) }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div slot="footer">
        <button type="button" class="custom-dialog-btn primary" @click="viewOpen = false">关闭</button>
      </div>
    </el-dialog>

    <!--  批量推送  -->
    <div class="collectAll" v-if="!!ids.length">
      <div class="collectAll-box">
        <div class="collectAll-title">已选择 {{ ids.length }} 项</div>
        <el-tooltip content="批量推送" placement="top" effect="dark">
          <div class="collectAll-btn">
            <span @click="handleSendAll">批量推送</span>
          </div>
        </el-tooltip>
        <el-tooltip content="退出" placement="top" effect="dark">
          <div class="collectAll-close" @click="handleClose">
            <i class="el-icon-close"></i>
          </div>
        </el-tooltip>
      </div>
    </div>
  </div>
</template>
<script>
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import { getlistb } from '@/api/purchase/category'
import { listHomeUnsalable } from '@/api/unsalable'
import { getPromotionIndexList } from '@/api/promotion'
import ProductDialog from '@/views/public/product/dialog'
import { pushArticle } from '@/api/article'

export default {
  name: 'Push',
  components: { ProductDialog, Treeselect },
  data() {
    return {
      tabActive: 0,
      tabOptions: [
        { label: '促销品', value: 0 },
        { label: '滞销品', value: 1 }
      ],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        categoryId: undefined,
        materialQuality: undefined,
        model: undefined,
        productCode: undefined,
        productName: undefined,
        specs: undefined,
        surface: undefined
      },
      showAll: false,
      word: '展开搜索',
      list: [],
      total: 0,
      loading: true,
      categoryList: [],
      key: 1,
      priceList: [],
      viewOpen: false,
      info: {},
      ids: []
    }
  },
  created() {
    this.getCategoryList()
    this.getList()
  },
  computed: {
    // 默认头像
    defaultAvatar() {
      return require('@/assets/images/Avatar.png')
    }
  },
  methods: {
    // 获取产品分类
    getCategoryList() {
      getlistb({ pageNum: 1, pageSize: 999 }).then(res => {
        this.categoryList = res.data
      })
    },
    // 格式化产品分类
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children
      }
      return {
        id: node.id,
        label: node.name,
        model: node.model,
        children: node.children
      }
    },
    // 切换tab
    handleTabChange(item) {
      this.list = []
      this.tabActive = item.value
      this.showAll = false
      this.word = '展开搜索'
      this.queryParams.pageSize = 10
      this.resetQuery()
    },
    // 查询列表数据
    getList() {
      this.key = Math.random()
      if (this.tabActive === 0) this.getPromotionList()
      else this.getUnsalableList()
    },
    // 查询促销品列表
    getPromotionList() {
      this.loading = true
      getPromotionIndexList(this.queryParams).then(res => {
        const { code, rows, total, msg } = res
        if (code === 200) {
          this.list = rows
          this.total = total
          this.loading = false
        } else this.$message.error(msg)
      })
    },
    // 格式化供应数量
    stockFormat(row) {
      const { promotions } = row
      if (!!promotions.length) {
        const arr = promotions.map(item => item.stock + (item.method === 'ton' ? '吨' : row.unit))
        return arr.join(',')
      }
    },
    // 格式化促销价格
    priceFormat(row) {
      const { promotions } = row
      if (!!promotions.length) {
        const arr = promotions.map(item => item.price + '元' + (item.method === 'ton' ? '/吨' : '/' + row.unit))
        return arr.join(',')
      }
    },
    // 查询滞销品列表
    getUnsalableList() {
      this.loading = true
      listHomeUnsalable(this.queryParams).then(res => {
        const { code, rows, total, msg } = res
        if (code === 200) {
          this.list = rows
          this.total = total
          this.loading = false
        } else this.$message.error(msg)
      })
    },
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    handleSelect(value, instanceId) {
      this.queryParams.categoryId = value
      this.handleQuery()
    },
    // 重置搜索
    resetQuery() {
      this.resetForm('query')
      this.handleQuery()
    },
    // 展开收起搜索
    clickSearch() {
      this.showAll = !this.showAll
      this.word = this.showAll ? '收起搜索' : '展开搜索'
    },
    // 产品详情
    handleProductInfo(row, type) {
      this.$refs.productInfo.handleView(row, type)
    },
    // 产品图片预览
    handleImg(row) {
      this.$refs.productInfo.handleImgView(row)
    },
    // 查看促销产品价格列表
    handleView(row) {
      this.info = { ...row }
      this.priceList = row.promotions || []
      this.viewOpen = true
    },
    // 合并单元格
    priceSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        const _row = this.priceList.length
        if (rowIndex % _row === 0) {
          return {
            rowspan: _row,
            colspan: 1
          }
        } else {
          return {
            rowspan: 0,
            colspan: 0
          }
        }
      }
    },
    // 多选
    handleSelectionChange(selection) {
      this.ids = selection
    },
    // 关闭多选
    handleClose() {
      this.ids = []
      this.$refs.table.clearSelection()
    },
    // 批量推送
    handleSendAll() {
      // this.ids.forEach(item => this.handleSend(item))
      let title = ''
      let payload = {
        doType: '',
        time: new Date().getTime(),
        title: undefined,
        info: undefined,
        readable: false,
        showMore: false
      }
      if (this.tabActive === 0) {
        let list = []
        this.ids.map(item => {
          const priceArr = item.promotions.map(ite => ite.price)
          const minPrice = Math.min(...priceArr)
          const index = item.promotions.findIndex(ite => ite.price === minPrice)
          const price = item.promotions[index].price
          const count = item.promotions[index].stock
          list.push({
            image: (item.picture1 ? this.imgPath + item.picture1 : '') || (item.diagram ? this.imgPath + item.diagram : '') || this.defaultAvatar,
            productId: item.id,
            productName: item.productName,
            specs: item.specs,
            price,
            count,
            unit: item.unit
          })
        })
        payload.title = title = '多款促销产品正在促销，价格优惠，实惠多多，快来看看吧'
        payload.doType = 'promotionlist'
        payload.info = list
      } else {
        let list = []
        this.ids.map(item => {
          list.push({
            image: (item.picture1 ? this.imgPath + item.picture1 : '') || (item.diagram ? this.imgPath + item.diagram : '') || this.defaultAvatar,
            productId: item.id,
            productName: item.productName,
            specs: item.specs,
            price: item.price,
            count: item.stock,
            unit: item.unit
          })
        })
        payload.title = title = '多款滞销产品正在促销，价格优惠，实惠多多，快来看看吧'
        payload.doType = 'unsalablelist'
        payload.info = list
      }
      const data = { title, info: title, payload: JSON.stringify(payload), push: false }
      pushArticle(data).then(res => {
        if (res.code === 200) this.$message.success('发送成功')
        else this.$message.error(res.msg)
      })
      this.handleClose()
    },
    // 推送
    handleSend(row) {
      if (this.tabActive === 0) this.handleSendPromotion(row)
      else this.handleSendUnsalable(row)
    },
    // 推送促销品
    handleSendPromotion(row) {
      const { promotions } = row
      const price = promotions.reduce((prev, next) => (prev.price < next.price ? prev : next))
      const image = (row.picture1 ? this.imgPath + row.picture1 : '') || (row.diagram ? this.imgPath + row.diagram : '') || this.defaultAvatar
      const title = `${row.productName}产品促销，价格优惠，快来看看吧！`
      const info = `${row.productName}产品促销，促销价低至${price.price}元${row.unit ? '/' + row.unit : ''}，数量有限，速来抢购！`
      const payload = {
        doType: 'promotion',
        productId: row.id,
        time: new Date().getTime(),
        title,
        info,
        image,
        readable: false
      }
      const data = { title, info, payload: JSON.stringify(payload), push: false }
      pushArticle(data).then(res => {
        if (res.code === 200) this.$message.success('发送成功')
        else this.$message.error(res.msg)
      })
    },
    // 推送滞销品
    handleSendUnsalable(row) {
      const image = (row.picture1 ? this.imgPath + row.picture1 : '') || (row.diagram ? this.imgPath + row.diagram : '') || this.defaultAvatar
      const title = `滞销品：${row.productName}，规格：${row.specs}正在促销中，请注意查看`
      const info = `滞销品：${row.productName}，规格：${row.specs}，价格：${row.price}/${row.priceUnit}，产品库存：${row.stock}${row.unit ? '/' + row.unit : ''}，请及时查看`
      const payload = {
        doType: 'unsalable',
        unsalableId: row.id,
        time: new Date().getTime(),
        title,
        info,
        image,
        readable: false
      }
      const data = { title, info, payload: JSON.stringify(payload), push: false }
      pushArticle(data).then(res => {
        if (res.code === 200) this.$message.success('发送成功')
        else this.$message.error(res.msg)
      })
    }
  }
}
</script>
<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';
.tableBox {
  padding: 20px;
}
.search-treeselect {
  ::v-deep {
    .treeselect-main {
      width: 300px;
    }
    .vue-treeselect__control {
      height: 30px;
    }
    .vue-treeselect__input-container {
      height: 30px !important;
    }
    .vue-treeselect,
    .vue-treeselect__placeholder,
    .vue-treeselect__single-value {
      line-height: 28px !important;
      height: 28px !important;
    }
    .category-flex {
      display: flex;
      flex-wrap: nowrap;
      align-items: center;
      span {
        display: block;
        float: left;
      }
      &-desc {
        font-size: 12px;
        color: #999999;
        margin-left: 10px;
      }
    }
  }
}
::v-deep {
  .custom-search {
    .el-form-item__content {
      width: calc(100% - 5em);
    }
  }
  .table-price {
    font-size: 12px !important;
  }
}
</style>
