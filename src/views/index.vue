<template>
  <div class="box">
    <el-row :gutter="20">
      <el-col :xs="24" :lg="14" :xl="16">
        <div class="box-left shadow" ref="boxLeft">
          <div class="about-evaluate">
            <div class="about">
              <div class="about-title">{{ usertype === 'gys' ? '供应商简介' : '采购商简介' }}</div>
              <div class="about-info" style="flex-direction: column" :class="{ jccenter: !level }" v-if="usertype === 'gys'">
                <div class="about-info-box">
                  <div class="about-info-avatar" :key="'logo' + key">
                    <logoAvatar :user="companyInfo" v-if="checkPermi(['company:info:update']) && companyInfo.id" />
                    <img :src="companyInfo.logoUrl_oss || imgPath + companyInfo.logoUrl" v-else />
                  </div>
                  <div class="about-info-item">
                    <div class="item-title">
                      <div style="display: inline-flex; flex-direction: row; flex-wrap: nowrap; align-items: center" :title="companyInfo.name">
                        {{ companyInfo.hasOwnProperty('name') && companyInfo.name.length > 5 ? companyInfo.name.substring(0, 5) + '...' : companyInfo.name }}
                        <span class="item-title-vip" v-if="level">认证采购商</span>
                      </div>
                      <span class="item-title-more" @click="handleMore" v-if="checkPermi(['company:info:update'])">
                        更多
                        <i class="el-icon-arrow-right"></i>
                      </span>
                    </div>
                    <div class="item-id pointer" @click="handleWallet" v-if="isWallet == 'true'">
                      账户余额:
                      <span>{{ userInfo.wallet }}</span>
                      <em><i class="el-icon-right"></i></em>
                    </div>
                    <div class="item-authentication">
                      <span class="yes" v-if="isCertified">认证企业</span>
                      <template v-if="!isCertified">
                        <span v-if="hasCertified">认证中…</span>
                        <span v-if="!hasCertified" @click="certifiedOpen = true">去认证></span>
                      </template>
                    </div>
                    <div class="item-desc">
                      {{ companyInfo.slogan }}
                      <i class="pointer ssfont ss-diy-bianji item-btn" @click="editAbout" v-if="checkPermi(['company:info:update'])">&nbsp; 编辑</i>
                    </div>
                    <div class="item-wechat">
                      <template v-if="!userInfo.openid">
                        <el-button type="danger" size="mini" @click="handleWechat">{{ wechaOpen ? '关闭二维码' : '绑定微信' }}</el-button>
                        <div class="item-qrcode shadow" v-if="wechaOpen">
                          <div id="qrcode" ref="qrcode"></div>
                        </div>
                      </template>
                      <template v-if="checkPermi(['company:info:update'])">
                        <template v-if="!level">
                          <el-button type="primary" size="mini" plain v-if="apply">升级为认证采购商审核中</el-button>
                          <el-button type="primary" size="mini" plain @click="vipOpen = true" v-else>升级为认证采购商</el-button>
                        </template>
                      </template>
                    </div>
                  </div>
                </div>
                <!-- <div class="about-info-brand" v-if="level"> -->
                <div class="about-info-brand">
                  <div class="about-info-brand-list">
                    <div class="about-info-brand-title">品牌：</div>
                    <div class="about-info-brand-item" v-for="item in brandList" :key="item.id">
                      {{ item.name }}
                      <i class="el-icon-error" @click="handleDelBrand(item)" v-if="checkPermi(['company:info:update'])"></i>
                    </div>
                  </div>
                  <div v-if="brandList.length < 3">
                    <el-button type="text" size="mini" icon="el-icon-plus" @click="handleAddBrand" v-if="checkPermi(['company:info:update'])">添加</el-button>
                  </div>
                </div>
              </div>
              <div class="about-info" v-else>
                <div class="about-info-avatar">
                  <img :src="avatar" />
                </div>
                <div class="about-info-item">
                  <div class="item-title">{{ userInfo.nickName }}</div>
                  <div class="item-id pointer" @click="handleWallet" v-if="isWallet == 'true'">
                    账户余额:
                    <span>{{ userInfo.wallet }}</span>
                    <em><i class="el-icon-right"></i></em>
                  </div>
                  <div class="item-wechat">
                    <template v-if="!userInfo.openid">
                      <el-button type="danger" size="mini" @click="handleWechat">{{ wechaOpen ? '关闭二维码' : '绑定微信' }}</el-button>
                      <div class="item-qrcode shadow" v-if="wechaOpen">
                        <div id="qrcode" ref="qrcode"></div>
                      </div>
                    </template>
                    <template v-if="!isCompany">
                      <el-button type="primary" size="mini" plain @click="handleUpgrade">升级为企业</el-button>
                    </template>
                  </div>
                </div>
              </div>
            </div>
            <div class="evaluate">
              <div class="evaluate-title">{{ usertype === 'gys' ? '供应商评价' : '采购商评价' }}</div>
              <div class="evaluate-info">
                <div class="evaluate-info-star">
                  <div class="star-item">
                    供货能力
                    <el-rate v-model="starData.capacity" :colors="colors" disabled></el-rate>
                  </div>
                  <div class="star-item">
                    时间管理
                    <el-rate v-model="starData.times" :colors="colors" disabled></el-rate>
                  </div>
                  <div class="star-item">
                    服务态度
                    <el-rate v-model="starData.service" :colors="colors" disabled></el-rate>
                  </div>
                  <div class="star-item">
                    售后服务
                    <el-rate v-model="starData.sales" :colors="colors" disabled></el-rate>
                  </div>
                  <div class="star-item">
                    发货速度
                    <el-rate v-model="starData.speed" :colors="colors" disabled></el-rate>
                  </div>
                </div>
                <div class="evaluate-info-chart">
                  <div id="main" style="width: 100%; height: 142px"></div>
                </div>
              </div>
            </div>
          </div>
          <div class="data" v-if="todoData && todoData.total > 0">
            <div class="data-title" @click="goTodo">
              <span>挂牌督办</span>
              <i class="el-icon-arrow-right"></i>
            </div>
            <div class="data-info">
              <div class="data-info-item">
                <div class="data-info-item-title"><span>待完成数量（个）</span></div>
                <div class="data-info-item-num">
                  <span>{{ todoData.rows.filter(item => item.status == 0).length || 0 }}</span>
                </div>
              </div>
              <div class="data-info-item">
                <div class="data-info-item-title"><span>挂牌督办总数（个）</span></div>
                <div class="data-info-item-num">
                  <span>{{ todoData.total || 0 }}</span>
                </div>
              </div>
            </div>
            <el-table ref="table" stripe :data="todoData.rows.filter(item => item.status == 0)" height="230" style="width: calc(100% - 40px); margin: 0 20px">
              <el-table-column align="center" type="index" label="序号"></el-table-column>
              <el-table-column align="left" prop="title" label="标题" show-overflow-tooltip></el-table-column>
              <el-table-column align="center" prop="deptNames" label="参与部门" show-overflow-tooltip></el-table-column>
              <el-table-column align="center" prop="priority" label="优先级" show-overflow-tooltip width="100">
                <template slot-scope="{ row }">
                  <div v-if="row.priority == 0" style="color: #2bcc75">一般</div>
                  <div v-if="row.priority == 1" style="color: #f39323">加急</div>
                  <div v-if="row.priority == 2" style="color: #f43f3f">紧急</div>
                </template>
              </el-table-column>
              <el-table-column align="center" prop="completionDate" label="完成时间" show-overflow-tooltip>
                <template slot-scope="{ row }">
                  <div class="finishTime_box">
                    <div class="time">{{ parseTime(row.completionDate, '{y}-{m}-{d}') }}</div>
                    <div class="tips" v-if="new Date(row.completionDate) < new Date() && row.status == 0">
                      <img src="~@/assets/images/remind.png" alt="" />
                      <span>已超期</span>
                    </div>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <template v-if="usertype == 'gys'">
            <div class="class" v-if="checkPermi(['company:info:update'])">
              <div class="class-title">
                供应类目
                <el-switch style="margin-left: 10px" v-model="categoryNameShow" active-text="显示" inactive-text="隐藏" class="custom-show-switch"></el-switch>
                <el-button style="margin-left: 10px" type="text" icon="el-icon-edit" @click="handleEdit" :disabled="hasNewCate" v-if="categoryNameShow">{{ hasNewCate ? '审核中…' : '编辑' }}</el-button>
              </div>
              <div class="class-tree-tag" v-if="categoryName.length && categoryNameShow">
                <div class="tag-item pointer" v-for="(item, index) in categoryName" :key="item.id">{{ item.name }}</div>
              </div>
              <div class="selectTips">供应国标甄选产品</div>
              <div class="flex" style="align-items: center; margin-top: 13px">
                <div class="selectBtn1" @click="setSelect('view')">
                  <span>查看供应国标甄选产品</span>
                </div>
                <div class="selectBtn2" @click="setSelect('add')">
                  <img src="@/assets/images/add_icon.png" alt="" srcset="" />
                  <span>添加国标甄选</span>
                </div>
              </div>
            </div>
            <div class="class" v-if="serviceOpen">
              <div class="class-title">指定企业客服</div>
              <div class="class-cascader-box">
                <el-cascader popper-class="class-cascader" v-model="service" :options="serviceOptions" filterable :props="{ expandTrigger: 'hover' }" @change="handleChangeService"></el-cascader>
                <span class="class-cascader-tip">当前客服为：{{ curService }}</span>
              </div>
            </div>
            <div class="photo">
              <el-tabs v-model="activeTab" class="photo-tabs" @tab-click="handleTabClick">
                <el-tab-pane label="场地图片" name="site">
                  <div class="photo-list">
                    <div class="photo-list-item" v-for="(item, index) in siteImg" :key="index">
                      <el-image :src="imgPath + item" :preview-src-list="siteImglist" fit="cover">
                        <div slot="error" class="image-slot">
                          <i class="el-icon-picture-outline"></i>
                        </div>
                      </el-image>
                      <i class="pointer el-icon-error photo-btn" @click="handleRemove(item)" v-if="checkPermi(['company:info:update'])"></i>
                    </div>
                    <el-upload ref="siteImg" class="photo-list-upload" :action="uploadUrl" list-type="picture-card" :before-upload="handleBeforeUpload" :on-success="handleSuccess" accept="image/*" v-if="checkPermi(['company:info:update'])">
                      <i class="el-icon-plus"></i>
                      <span>添加图片</span>
                    </el-upload>
                  </div>
                </el-tab-pane>
                <el-tab-pane label="设备照片" name="device">
                  <div class="photo-list">
                    <div class="photo-list-item" v-for="(item, index) in deviceImg" :key="index">
                      <el-image :src="imgPath + item" :preview-src-list="deviceImglist" fit="cover">
                        <div slot="error" class="image-slot">
                          <i class="el-icon-picture-outline"></i>
                        </div>
                      </el-image>
                      <i class="pointer el-icon-error photo-btn" @click="handleRemove2(item)" v-if="checkPermi(['company:info:update'])"></i>
                    </div>
                    <el-upload ref="deviceImg" class="photo-list-upload" :action="uploadUrl" list-type="picture-card" :before-upload="handleBeforeUpload" :on-success="handleSuccess2" accept="image/*" v-if="checkPermi(['company:info:update'])">
                      <i class="el-icon-plus"></i>
                      <span>添加图片</span>
                    </el-upload>
                  </div>
                </el-tab-pane>
                <el-tab-pane label="工艺照片/视频" name="process">
                  <div class="photo-list">
                    <div class="photo-list-item" v-for="(item, index) in process" :key="index">
                      <video class="item-video" :src="imgPath + item" type="video/mp4" controls muted preload="none" poster="../../public/imgs/logo.gif" v-if="item.split('.').includes('mp4')"></video>
                      <el-image :src="imgPath + item" :preview-src-list="processlist" fit="cover" v-else>
                        <div slot="error" class="image-slot">
                          <i class="el-icon-picture-outline"></i>
                        </div>
                      </el-image>
                      <i class="pointer el-icon-error photo-btn" @click="handleRemove4(item)" v-if="checkPermi(['company:info:update'])"></i>
                    </div>
                    <el-upload ref="process" class="photo-list-upload" :action="uploadUrl" list-type="picture-card" :before-upload="handleBeforeUpload" :on-success="handleSuccess4" accept="image/*,.mp4" v-if="checkPermi(['company:info:update'])">
                      <i class="el-icon-plus"></i>
                      <span>添加图片/视频</span>
                    </el-upload>
                  </div>
                </el-tab-pane>
                <el-tab-pane label="证书" name="cert">
                  <div class="photo-list">
                    <div class="photo-list-item" v-for="(item, index) in certImg" :key="index">
                      <el-image :src="imgPath + item" :preview-src-list="certImglist" fit="cover">
                        <div slot="error" class="image-slot">
                          <i class="el-icon-picture-outline"></i>
                        </div>
                      </el-image>
                      <i class="pointer el-icon-error photo-btn" @click="handleRemove3(item)" v-if="checkPermi(['company:info:update'])"></i>
                    </div>
                    <el-upload ref="certImg" class="photo-list-upload" :action="uploadUrl" list-type="picture-card" :before-upload="handleBeforeUpload" :on-success="handleSuccess3" accept="image/*" v-if="checkPermi(['company:info:update'])">
                      <i class="el-icon-plus"></i>
                      <span>添加图片</span>
                    </el-upload>
                  </div>
                </el-tab-pane>
              </el-tabs>
            </div>
          </template>
        </div>
        <index-menu ref="indexMenu" />
      </el-col>
      <el-col :xs="24" :lg="10" :xl="8">
        <div class="box-right">
          <div class="price-update" v-if="isUpdate">
            <i class="el-icon-info"></i>
            <span>滞销品价格有变动，是否更新？</span>
            <el-button type="danger" plain size="small" @click="handleUpdatePrice('refresh')">更新</el-button>
            <el-button plain size="small" @click="handleUpdatePrice('ignore')">忽略</el-button>
          </div>
          <weather-date ref="weatherDate" />
          <!-- 今日请假人数 -->
          <div class="notice-box shadow" v-if="workHandoverTodayLeaveData.length">
            <div class="notcie-number">
              <div class="notcie-number-title">今日请假人数</div>
              <div class="notcie-number-number">{{ workHandoverTodayLeaveData.length || 0 }}</div>
              <div class="notcie-number-tips">请假人有{{ workHandoverTodayLeaveString }}</div>
              <div class="notcie-number-btn" @click="workHandoverTodayLeaveOpen = true">查看详情</div>
              <!-- <div class="notcie-number-btn" @click="handleWorkHandover">查看详情</div> -->
            </div>
          </div>
          <!-- <div class="notice-box shadow">
            <div class="notcie-number">
              <div class="notcie-number-title">今日待办</div>
              <div class="notcie-number-number">{{ 19 }}</div>
              <div class="notcie-number-tips">今日待办：{{ '1、确认某某客户合同…' }}</div>
              <div class="notcie-number-btn" @click="handleToDo">查看详情</div>
            </div>
          </div> -->
          <div class="notice-box shadow">
            <div class="notice-title">消息中心</div>
            <div v-loading="noticeLoading">
              <div class="notice-item pointer" v-for="item in contractSaleList" :key="item.id" @click="handleSkip('sell')">
                <div class="notice-item-title">
                  <b>买方{{ item.buyerName }}向您签订了一份销售合同</b>
                  <span></span>
                </div>
                <div class="notice-item-desc">
                  <b>签订时间：{{ item.signingTime }}，订单总金额：{{ item.amount ? '￥' + item.amount : '' }}{{ item.isIncludingTax ? '(含税)' : '(不含税)' }}</b>
                  <span>查看更多</span>
                </div>
              </div>
              <div class="notice-item pointer" v-for="item in unsalableContractList" :key="item.id" @click="handleSkip('unsalable')">
                <div class="notice-item-title">
                  <b>买方{{ item.buyerName }}向您签订了一份滞销品合同</b>
                  <span></span>
                </div>
                <div class="notice-item-desc">
                  <b>签订时间：{{ item.signingTime }}，订单总金额：{{ item.amount ? '￥' + item.amount : '' }}{{ item.isIncludingTax ? '(含税)' : '(不含税)' }}</b>
                  <span>查看更多</span>
                </div>
              </div>
              <template v-if="noticeTotal">
                <div class="notice-item-box">
                  <div class="notice-item pointer" v-for="item in noticeList" :key="item.noticeId" @click="handleDetail(item)">
                    <div class="notice-item-title" :class="item.status === 1 ? 'unread' : ''">
                      <b>{{ item.noticeTitle }}</b>
                      <span>{{ item.createTime }}</span>
                    </div>
                    <div class="notice-item-desc">
                      <b>{{ item.noticeContent }}</b>
                      <span>查看详情</span>
                    </div>
                  </div>
                </div>
                <div class="notice-item-btn" @click="handleMoreNotice">
                  <template v-if="noticeTotal > noticeQuery.pageNum * noticeQuery.pageSize">
                    <i :class="moreNoticeLoading ? 'el-icon-loading' : 'el-icon-refresh'"></i>
                    <span>查看更多</span>
                  </template>
                  <template v-else>
                    <span>没有更多了</span>
                  </template>
                </div>
              </template>
              <el-empty v-if="!contractSaleList.length && !unsalableContractList.length && !noticeTotal" />
            </div>
          </div>
          <board ref="board" :board-show="boardShow" is-index v-if="usertype === 'gys'" />
        </div>
      </el-col>
    </el-row>
    <el-dialog v-dialogDragBox title="消息详情" :visible.sync="noticeOpen" width="50%" class="notice-dialog" @close="handleClose">
      <div class="dialog-title">{{ noticeInfo.noticeTitle }}</div>
      <div class="dialog-date">发布日期：{{ noticeInfo.createTime }}</div>
      <div class="dialog-con">{{ noticeInfo.noticeType === 'company_notice' ? formatWorkHandoverTime(noticeInfo.noticeContent) : noticeInfo.noticeContent }}</div>
      <div class="dialog-tip">Tips：该消息由自由客紧固件一站式采购平台发布</div>
    </el-dialog>

    <!-- 弹出邀请提示 -->
    <el-dialog v-dialogDragBox :title="`是否接受【${inviteInfo.companyName}】的邀请?`" :visible.sync="inviteOpen" width="600px" center :show-close="false" :close-on-click-modal="false" :close-on-press-escape="false" custom-class="centerDialog">
      <div class="invite-info">
        <div class="invite-info-logo">
          <img :src="imgPath + inviteInfo.logo" :alt="inviteInfo.companyName" />
        </div>
        <div>
          <div class="invite-info-item">名称：{{ inviteInfo.companyName }}</div>
          <div class="invite-info-item">电话：{{ inviteInfo.phone }}</div>
          <div class="invite-info-item">地址：{{ removeHtmlTag(inviteInfo.address, 300) }}</div>
        </div>
      </div>
      <span slot="footer">
        <el-button type="danger" @click="inviteSubmit(0)">拒 绝</el-button>
        <el-button type="primary" @click="inviteSubmit(1)">接 受</el-button>
      </span>
    </el-dialog>

    <!-- 升级为企业 -->
    <el-dialog v-dialogDragBox title="升级为企业" :visible.sync="upgradeOpen" width="600px" class="custom-dialog">
      <div style="padding: 0 20px">
        <el-form ref="upgradeForm" :model="upgradeForm" :rules="upgradeRules" label-width="0">
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item prop="companyName">
                <el-input v-model="upgradeForm.companyName" auto-complete="off" placeholder="请输入公司名称"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item prop="address">
                <el-input v-model="upgradeForm.address" auto-complete="off" placeholder="请输入公司地址"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item prop="uscCode">
                <el-input v-model="upgradeForm.uscCode" auto-complete="off" placeholder="请输入统一社会信用代码"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="18">
              <el-form-item prop="phone">
                <el-input v-model="upgradeForm.phone" auto-complete="off" placeholder="请输入您的手机号" readonly></el-input>
              </el-form-item>
            </el-col>
            <!-- <el-col :span="24"></el-col> -->
            <el-col :span="18">
              <el-form-item prop="smsCode">
                <el-input v-model="upgradeForm.smsCode" auto-complete="off" placeholder="请输入短信验证码">
                  <template slot="append">
                    <el-button :loading="sending" :disabled="sending" @click="handleSend">{{ sendTitle }}</el-button>
                  </template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item prop="licenseImg">
                <el-upload :class="{ hide: hideUpload }" :file-list="fileList" :action="uploadUrl" list-type="picture-card" :limit="1" :before-upload="handleBeforeUpload" :on-success="handlelicenseImgSuccess">
                  <i class="el-icon-plus"></i>
                  <span class="el-upload-picture-card-title">上传营业执照</span>
                  <div slot="file" slot-scope="{ file }">
                    <img class="el-upload-list__item-thumbnail" :src="imgPath + file.fileName" alt="" />
                    <span class="el-upload-list__item-actions">
                      <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
                        <i class="el-icon-zoom-in"></i>
                      </span>
                      <span class="el-upload-list__item-delete" @click="handlelicenseImgRemove(file)">
                        <i class="el-icon-delete"></i>
                      </span>
                    </span>
                  </div>
                </el-upload>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <button type="button" class="custom-dialog-btn" @click="upgradeOpen = false">取消</button>
        <button type="button" class="custom-dialog-btn primary" @click="handleSumit">确定</button>
      </div>
    </el-dialog>

    <el-dialog v-dialogDragBox :visible.sync="dialogVisible">
      <img width="100%" :src="dialogImageUrl" alt="" />
    </el-dialog>

    <!-- 升级VIP权益 -->
    <el-dialog v-dialogDragBox title="认证采购商特权" :visible.sync="vipOpen" width="1150px" class="custom-dialog">
      <div class="vipBox">
        <div class="vipBox-item">
          <div class="vipBox-item-title"><b>普通企业</b></div>
          <ul>
            <li>
              <b>首页内容</b>
              <span>企业信息、供应商类型、图片/视频展示、评价</span>
            </li>
            <li>
              <b>系统管理</b>
              <span>用户、角色、部门、权限管理</span>
            </li>
            <li>
              <b>平台管理</b>
              <span>平台需求、产品收藏、产品中心管理</span>
            </li>
            <li>
              <b>需求管理</b>
              <span>采购需求、订单、合同管理</span>
            </li>
            <li>
              <b>私域管理</b>
              <span>私域收藏、供应商、私域产品管理</span>
            </li>
            <li>
              <b>报价管理</b>
              <span>报价新增、历史报价、报价元素管理</span>
            </li>
            <li>
              <b>日志管理</b>
              <span>操作日志管理</span>
            </li>
          </ul>
        </div>
        <div class="vipBox-item active">
          <div class="vipBox-item-bg">
            <span class="vipBox-item-tip">推荐</span>
          </div>
          <div class="vipBox-item-title">
            <img src="~@/assets/images/vip-icon.png" alt="" />
            <b>认证采购商</b>
          </div>
          <ul>
            <li>
              <b>首页内容</b>
              <div>
                <span>企业信息、供应商类型、图片/视频展示、评价</span>
                <em>+首页品牌展示</em>
              </div>
            </li>
            <li>
              <b>系统管理</b>
              <span>用户、角色、部门、权限管理</span>
            </li>
            <li>
              <b>平台管理</b>
              <span>平台需求、产品收藏、产品中心管理</span>
            </li>
            <li>
              <b>需求管理</b>
              <span>采购需求、订单、合同管理</span>
            </li>
            <li>
              <b>私域管理</b>
              <span>私域收藏、供应商、私域产品管理</span>
            </li>
            <li>
              <b>报价管理</b>
              <span>报价新增、历史报价、报价元素管理</span>
            </li>
            <li>
              <b>日志管理</b>
              <span>操作日志管理</span>
            </li>
          </ul>
        </div>
      </div>
      <div slot="footer">
        <button type="button" class="custom-dialog-btn" @click="vipOpen = false">取消</button>
        <button type="button" class="custom-dialog-btn primary" @click="handleUpgradeVip">确认升级</button>
      </div>
    </el-dialog>

    <!-- 升级认证企业 -->
    <el-dialog v-dialogDragBox title="申请成为认证企业" :visible.sync="certifiedOpen" width="1150px" class="custom-dialog">
      <div class="vipBox">
        <div class="vipBox-item">
          <div class="vipBox-item-title"><b>普通企业</b></div>
          <ul>
            <li>
              <b>首页内容</b>
              <span>企业信息、供应商类型、图片/视频展示、评价</span>
            </li>
            <li>
              <b>系统管理</b>
              <span>用户、角色、部门、权限管理</span>
            </li>
            <li>
              <b>平台管理</b>
              <span>平台需求、产品收藏、产品中心管理</span>
            </li>
            <li>
              <b>需求管理</b>
              <span>采购需求、订单、合同管理</span>
            </li>
            <li>
              <b>私域管理</b>
              <span>私域收藏、供应商、私域产品管理</span>
            </li>
            <li>
              <b>报价管理</b>
              <span>报价新增、历史报价、报价元素管理</span>
            </li>
            <li>
              <b>日志管理</b>
              <span>操作日志管理</span>
            </li>
          </ul>
        </div>
        <div class="vipBox-item active">
          <div class="vipBox-item-title">
            <img src="~@/assets/images/vip-icon.png" alt="" />
            <b>认证企业</b>
          </div>
          <ul>
            <li>
              <b>首页内容</b>
              <div>
                <span>企业信息、供应商类型、图片/视频展示、评价</span>
                <em>+专属图标</em>
                <em>+企业信息展示</em>
              </div>
            </li>
            <li>
              <b>系统管理</b>
              <span>用户、角色、部门、权限管理</span>
            </li>
            <li>
              <b>平台管理</b>
              <span>平台需求、产品收藏、产品中心管理</span>
              <em>+专属图标</em>
              <em>+企业信息展示</em>
            </li>
            <li>
              <b>需求管理</b>
              <span>采购需求、订单、合同管理</span>
            </li>
            <li>
              <b>私域管理</b>
              <span>私域收藏、供应商、私域产品管理</span>
            </li>
            <li>
              <b>报价管理</b>
              <span>报价新增、历史报价、报价元素管理</span>
            </li>
            <li>
              <b>日志管理</b>
              <span>操作日志管理</span>
            </li>
          </ul>
        </div>
      </div>
      <div slot="footer">
        <button type="button" class="custom-dialog-btn" @click="certifiedOpen = false">取消</button>
        <button type="button" class="custom-dialog-btn primary" @click="handleUpgradeCertified">立即申请</button>
      </div>
    </el-dialog>

    <!--  补充企业信息  -->
    <edit ref="edit" @submit="getSupplier" />

    <!-- 编辑供应类型  -->
    <el-dialog v-dialogDragBox title="提交变更供应类目申请" :visible.sync="cateKG" width="1150px" class="custom-dialog">
      <div>
        <div class="class-tree-tag" v-if="newcategoryName.length">
          <div class="tag-item pointer" v-for="(item, index) in newcategoryName" :key="item.id">
            {{ item.name }}
            <i class="pointer el-icon-error tag-btn" @click="deleteTag(index)"></i>
          </div>
        </div>
        <el-cascader-panel ref="category" v-model="newcategoryData" :options="treeData" :props="categoryProps" @change="handleChangeCategory" class="category-cascader" :key="key">
          <template slot-scope="{ data }">
            <span>{{ data.name }}</span>
            <span v-if="data.model" style="font-size: 12px; color: red">(规格型号：{{ data.model }})</span>
          </template>
        </el-cascader-panel>
      </div>
      <div slot="footer">
        <button type="button" class="custom-dialog-btn" @click="cateKG = false">取消</button>
        <button type="button" class="custom-dialog-btn primary" @click="handleSubmitKG">提交</button>
      </div>
    </el-dialog>

    <!-- 设置国标甄选产品 -->
    <el-dialog v-dialogDragBox :title="selectType == 'view' ? '查看经营国标甄选产品' : '国标甄选产品经营申请'" :visible.sync="selectBoxShow" width="1150px" class="custom-dialog">
      <div class="select_box">
        <div class="select_box_title">{{ selectType == 'view' ? '已通过申请可经营国标甄选产品' : '可选国标甄选产品' }}</div>
        <div class="select_box_concent">
          <div class="selectBox_concent_itemBox" v-for="(item, index) in selectCategoryList" :key="index">
            <div class="selectBox_concent_itemBox_category">
              <div>所属类目：</div>
              <div>{{ item.categoryName }}</div>
            </div>
            <div class="selectBox_concent_itemBox_list">
              <div class="selectBox_concent_item" v-for="(ite, index) in item.selectList" :key="index">
                <el-checkbox v-if="selectType == 'add'" v-model="ite.checked" @change="selectItemChange(ite, item)" :disabled="ite.disabled"></el-checkbox>
                <div class="selectBox_concent_item_text" :class="ite.checked ? 'active' : ''">
                  <span class="selectBox_concent_item_tips">国标甄选</span>
                  <span class="selectBox_concent_item_title">{{ ite.productName }}</span>
                </div>
                <div class="selectBox_concent_item_modle">{{ ite.model }}</div>
              </div>
            </div>
            <div class="selectBox_concent_itemBox_checkAll" v-if="selectType == 'add'">
              <el-checkbox v-model="item.checkedALL" @change="selectAllChange(item)" :disabled="item.disabled">全选</el-checkbox>
            </div>
          </div>
        </div>
      </div>
      <div slot="footer">
        <button type="button" class="select_btn" @click="selectBoxShow = false" v-if="selectType == 'add'">取消</button>
        <button type="button" class="select_btn primary" @click="handleSubmitSelect" v-if="selectType == 'add'">提交申请</button>
        <button type="button" class="select_btn primary" @click="selectBoxShow = false" v-if="selectType == 'view'">我看完了</button>
      </div>
    </el-dialog>

    <!-- 报价申请 -->
    <el-dialog v-dialogDragBox title="报价申请" :visible.sync="applyTipShow" width="1150px" class="custom-dialog">
      <div class="applyTip_box">
        <div class="applyTip_top">
          <img src="@/assets/images/complete.png" alt="" />
          <span class="applyTip_top_title">国标产品经营申请已发出，请您耐心等待结果，请留意消息提醒</span>
          <span class="applyTip_top_text">国标产品经营平台需要审核您的企业经营产品类目是否符合平台要求</span>
        </div>
        <div class="applyTip_bottom">
          <p class="applyTip_bottom_tips">以下是您选择的国标产品</p>
          <div class="applyTip_bottom_list">
            <div class="applyTip_bottom_item" v-for="(item, index) in productList" :key="index">
              <div class="applyTip_bottom_item_img">
                <img :src="item.picture1_oss || imgPath + picture1" alt="" />
                <span>国标甄选</span>
              </div>
              <div class="applyTip_bottom_item_text">
                <p>
                  产品名称
                  <span>{{ item.productName }}</span>
                </p>
                <p>
                  产品编号
                  <span>{{ item.productCode }}</span>
                </p>
                <p>
                  产品型号
                  <span>{{ item.model }}</span>
                </p>
              </div>
              <div class="applyTip_bottom_item_text">
                <p>
                  产品材质
                  <span>{{ item.materialQuality }}</span>
                </p>
                <p>
                  表面处理
                  <span>{{ item.surface }}</span>
                </p>
                <p>
                  产品重量
                  <span>{{ item.weight }}</span>
                </p>
              </div>
              <div class="applyTip_bottom_item_text">
                <p>
                  计量单位
                  <span>{{ item.unit }}</span>
                </p>
                <p><span></span></p>
                <p><span></span></p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div slot="footer">
        <p class="applyTip_btn" @click="applyTipShow = false">我知道了</p>
      </div>
    </el-dialog>

    <!-- 请假详情 -->
    <el-dialog v-dialogDragBox title="请假详情" :visible.sync="workHandoverTodayLeaveOpen" width="1150px" class="custom-dialog">
      <div style="padding: 0 20px">
        <el-table :data="workHandoverTodayLeaveData" stripe class="custom-table">
          <el-table-column type="index" label="序号" width="50" align="center" />
          <el-table-column prop="createBy" label="请假人" align="center" width="100" />
          <el-table-column label="请假时间" align="center">
            <template slot-scope="scope">
              <span class="time-text">{{ parseTime(scope.row.leaveStartTime, '{y}-{m}-{d} {h}:{i}') }}</span>
              <span class="time-text">{{ parseTime(scope.row.leaveEndTime, '{y}-{m}-{d} {h}:{i}') }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="phone" label="联系电话" align="center" width="100" />
          <el-table-column prop="handoverUser" label="接手人" align="center" />
          <el-table-column prop="copyUser" label="抄送人" align="center" />
          <el-table-column prop="content" label="交接内容" align="center"></el-table-column>
        </el-table>
      </div>
    </el-dialog>

    <!-- 待办详情 -->
    <el-dialog v-dialogDrag title="待办详情" :visible.sync="toDoViewOpen" width="1150px" class="custom-dialog">
      <div class="todo_box">
        <div class="todo_header">
          <el-date-picker v-model="toDoDate" type="date" placeholder="选择日期"></el-date-picker>
          <div style="display: flex; flex-direction: column; align-items: flex-start; font-weight: 400; font-size: 16px; color: #666666">
            <div>
              <span>共</span>
              <span style="margin: 0 10px; font-weight: 500; font-size: 20px; color: #2e73f3">9</span>
              <span>项待处理工作</span>
            </div>
            <div>
              <span style="margin-right: 8px">农历</span>
              <span>{{ lunarInfo.lunarMonthName }}{{ lunarInfo.lunarDayName }},{{ lunarInfo.GanZhiYear }}[{{ lunarInfo.zodiac }}]年{{ lunarInfo.GanZhiMonth }}月{{ lunarInfo.GanZhiDay }}日</span>
            </div>
          </div>
          <div></div>
          <el-button type="primary" icon="el-icon-plus" @click="handleAddToDo">添加待办事项</el-button>
        </div>
        <div class="todo_table">
          <div class="todo_title">今日待办</div>
          <el-table :data="toDoTableData" stripe class="custom-table" style="width: 100%">
            <el-table-column type="index" label="序号" width="50" align="center"></el-table-column>
            <el-table-column prop="details" label="待办详情" align="center" show-overflow-tooltip></el-table-column>
            <el-table-column prop="createTime" label="创建日期" align="center" show-overflow-tooltip></el-table-column>
            <el-table-column prop="customer" label="关联客户" align="center" show-overflow-tooltip>
              <template slot-scope="scope">
                <span style="color: #2e73f3; cursor: pointer">{{ scope.row.customer }} ＞</span>
              </template>
            </el-table-column>
            <el-table-column prop="executeTime" label="执行日期" align="center" show-overflow-tooltip>
              <template slot-scope="scope">
                <span style="color: #f35d09">{{ scope.row.executeTime }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn primary" style="width: 270px" @click="toDoViewOpen = false">关闭</el-button>
      </div>
    </el-dialog>

    <!-- 新增待办 -->
    <el-dialog v-dialogDrag title="新增待办" :visible.sync="toDoAddOpen" width="1150px" class="custom-dialog">
      <div class="todo_add">
        <el-form ref="toDoAddForm" :model="toDoAddForm" label-width="100px" label-position="left">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="关联客户" prop="customer">
                <el-select v-model="toDoAddForm.customer" placeholder="请选择关联客户" style="width: 100%">
                  <el-option label="客户1" value="客户1"></el-option>
                  <el-option label="客户2" value="客户2"></el-option>
                  <el-option label="客户3" value="客户3"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="待办详情描述" prop="details">
                <el-input v-model="toDoAddForm.details" type="textarea" :rows="5" placeholder="请输入待办详情"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="待办执行时间" prop="executeTime">
                <el-date-picker v-model="toDoAddForm.executeTime" type="datetime" placeholder="选择日期时间" style="width: 100%"></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="重复周期" prop="cycleMode">
                <el-select v-model="toDoAddForm.cycleMode" placeholder="请选择重复周期" style="width: 100%">
                  <el-option label="不重复" :value="1"></el-option>
                  <el-option label="每周" :value="2"></el-option>
                  <el-option label="每月" :value="3"></el-option>
                  <el-option label="每年" :value="4"></el-option>
                  <el-option label="自定义" :value="5"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="toDoAddForm.cycleMode == '2' || toDoAddForm.cycleMode == '3' || toDoAddForm.cycleMode == '4'">
              <el-form-item label="选择开始时间" prop="supplyEndTime">
                <el-date-picker v-model="toDoAddForm.supplyEndTime" type="date" value-format="yyyy-MM-dd" placeholder="请选择选择开始时间" style="width: 100%"></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="toDoAddForm.cycleMode == '2'">
              <el-form-item label="每周重复日期" prop="supplyEndTime">
                <el-time-select
                  v-model="value"
                  :picker-options="{
                    selectableRange: '00:00:00 - 23:59:59'
                  }"
                  value-format="HH:mm:ss"></el-time-select>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="toDoAddForm.cycleMode == '3'">
              <el-form-item label="每月重复日期" prop="supplyEndTime">
                <el-time-select
                  v-model="value"
                  :picker-options="{
                    selectableRange: '00:00:00 - 23:59:59'
                  }"
                  value-format="HH:mm:ss"></el-time-select>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="toDoAddForm.cycleMode == '4'">
              <el-form-item label="每年重复日期" prop="supplyEndTime">
                <el-time-select
                  v-model="value"
                  :picker-options="{
                    selectableRange: '00:00:00 - 23:59:59'
                  }"
                  value-format="HH:mm:ss"></el-time-select>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="附件" prop="attachment">
                <image-upload v-model="toDoAddForm.attachment" :file-type="fileType" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn primary" style="width: 270px" @click="toDoAddOpen = false">取消</el-button>
        <el-button class="custom-dialog-btn primary" style="width: 270px" @click="handleAddToDo">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import CountTo from 'vue-count-to'
import { mapGetters } from 'vuex'
import { getUserProfile, supplier, chart, ediSupplier, qrcode, inviteHas, inviteVerify, smsCode, userUpgrade, userBrandList, userBrandAdd, userBrandDel, userUpgradeVip, companyAuth, deptTreeSelect, listUser, setService, getService } from '@/api/system/user'
import { getlist } from '@/api/purchase/category'
import { newslist } from '@/api/system/news'
import { getNotice, updateNotice } from '@/api/system/notice'
import { listProduct, productOptimalApply, productOptimalPassedIds, productOptimalPassedList } from '@/api/system/product'
import PieChart from './dashboard/PieChart.vue'
import LineChart from './dashboard/LineChart.vue'
import 'element-ui/lib/theme-chalk/display.css'
import edit from './gysedit'
import * as echarts from 'echarts'
import QRCode from 'qrcodejs2'
import { walletBalance } from '@/api/system/wallet'
import { isCreditCode } from '@/utils/validate'
import logoAvatar from './logoAvatar'
import { checkPermi } from '@/utils/permission'
import { checkUnsalablePrice, listUnsalable, listUnsalableSupplierContract, updateUnsalablePrice } from '@/api/unsalable'
import { contractSaleList } from '@/api/purchase'
import board from '@/views/board/index'
import { getToken } from '@/utils/auth'
import { removeHtmlTag } from '@/utils'
import { todoItemsList } from '@/api/todo'
import { parseTime } from '@/utils/ruoyi'
import weatherDate from '@/views/control/weatherDate'
import { getConfigDetail } from '@/api/config'
import { workHandoverTodayLeave } from '@/api/workHandover'
import LunarCalendar from 'lunar-calendar'
import indexMenu from './indexMenu'

export default {
  name: 'Index',
  components: { board, CountTo, LineChart, PieChart, edit, logoAvatar, weatherDate, indexMenu },
  data() {
    return {
      uploadUrl: process.env.VUE_APP_BASE_API + '/common/upload', // 上传地址
      userInfo: {}, // 用户信息
      qrcode: undefined, // 微信二维码
      wechaOpen: false,
      companyInfo: {}, // 供应商信息
      imgUrl: 'http://www.ziyouke.net/prod-api', // 图片地址
      siteImg: [], // 场地图片
      deviceImg: [], // 设备照片
      certImg: [], // 证书
      process: [], // 工艺照片/视频
      siteImglist: [], // 场地图片
      deviceImglist: [], // 设备照片
      certImglist: [], // 证书
      processlist: [], // 工艺照片/视频
      option: {
        radar: {
          indicator: [
            { name: '供货能力', max: 5 },
            { name: '售后服务', max: 5 },
            { name: '服务态度', max: 5 },
            { name: '发货速度', max: 5 },
            { name: '时间管理', max: 5 }
          ],
          center: ['50%', '50%'],
          radius: 35,
          shape: 'circle'
        },
        series: {
          type: 'radar',
          lineStyle: {
            opacity: 0
          },
          symbol: 'none',
          data: [
            {
              value: [1, 1, 1, 1, 1],
              areaStyle: {
                color: {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: 'rgba(46,115,243,0.36)'
                    },
                    {
                      offset: 1,
                      color: 'rgba(46,115,243,0.6)'
                    }
                  ],
                  global: false
                }
              }
            }
          ]
        }
      },
      value: 1,
      colors: ['#2e73f3', '#2e73f3', '#2e73f3'],
      // 供应商类型
      treeData: [],
      categoryData: [],
      oldcategoryName: [],
      categoryName: [],
      categoryProps: { multiple: true, label: 'name', value: 'id' },
      key: 1,
      usertype: undefined,
      cateKG: false,
      starData: {}, // 供应商
      noticeList: [],
      noticeLoading: true,
      noticeOpen: false,
      noticeInfo: {},
      inviteOpen: false,
      inviteInfo: {},
      isWallet: false,
      isCompany: true,
      // 升级供应商
      upgradeOpen: false,
      upgradeForm: {},
      upgradeRules: {
        companyName: [{ required: true, message: '请输入公司名称', trigger: 'blur' }],
        address: [{ required: true, message: '请输入公司地址', trigger: 'blur' }],
        uscCode: [
          { required: true, message: '请输入统一社会信用代码', trigger: 'blur' },
          { validator: isCreditCode, message: '统一社会信用代码输入有误', trigger: 'blur' }
        ],
        // licenseImg: [{ required: true, message: '请上传营业执照', trigger: 'change' }],
        phone: [
          { required: true, trigger: 'blur', message: '请输入您的手机号' },
          { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
        ],
        smsCode: [{ required: true, trigger: 'blur', message: '请输入短信验证码' }]
      },
      second: 60,
      sending: false,
      hideUpload: false,
      fileList: [],
      dialogImageUrl: '',
      dialogVisible: false,
      brandList: [],
      level: 0,
      apply: undefined,
      // VIP权益
      vipOpen: false,
      hasCertified: false,
      isCertified: false,
      certifiedOpen: false,
      isUpdate: false,
      newcategoryName: [],
      newcategoryData: [],
      hasNewCate: false,
      categoryNameShow: false,
      // 设置企业客服
      serviceOpen: false,
      service: [],
      serviceOptions: [],
      curService: null,
      contractSaleList: [], // 销售合同数量
      unsalableContractList: [], // 滞销品合同数量
      // 显示图表
      boardShow: ['demand', 'offer'],
      weatherKey: 1,
      weather: { time: '', weathercode: '', temp: '', sd: '', WD: '', WS: '' }, // 天气
      location: { province: '', city: '', area: '' },
      indexDate: {},
      // 国标甄选
      selectBoxShow: false,
      selectCategoryList: [],
      applyTipShow: false,
      productList: [],
      optimalIds: [],
      selectType: '',
      myChart: null,
      todoData: {},
      noticeQuery: {
        pageNum: 1,
        pageSize: 6
      },
      noticeTotal: 0,
      moreNoticeLoading: false,
      workHandoverTodayLeaveOpen: false,
      workHandoverTodayLeaveData: [],
      workHandoverTodayLeaveString: '',
      toDoViewOpen: false,
      toDoDate: new Date(),
      lunarInfo: {
        lunarMonthName: '',
        lunarDayName: '',
        GanZhiYear: '',
        zodiac: '',
        GanZhiMonth: '',
        GanZhiDay: ''
      },
      toDoTableData: [
        {
          details: '待办事项详细待办事项详细待办事项详细待办事项详细待办事项详细…',
          createTime: '2025-02-26  09:29:56',
          customer: '世盛金属制品有限公司',
          executeTime: '2025-06-18'
        }
      ],
      toDoAddOpen: false,
      toDoAddForm: {},
      fileType: ['png', 'PNG', 'jpg', 'JPG', 'jpeg', 'JPEG'],
      activeTab: 'site' // 默认显示场地图片
    }
  },
  created() {
    this.getConfigKey('wallet.switch').then(res => {
      this.isWallet = typeof res.msg
    })
    this.getTree()
    this.getUserInfo()
    this.getNoticelist()
    this.handleCheckPrice()
    this.getWorkHandoverTodayLeave() // 获取今日请假人数
    const roles = this.$store.getters.roles
    if (roles.includes('Register_Ordinary_User')) {
      this.isCompany = false
      this.getInviteHas()
    }
    getConfigDetail({ configKey: 'todo.items.supervise' }).then(res => {
      this.supplierConfig = res.data
      if (res.data.configValue) {
        let queryParams = {
          pageNum: 1,
          pageSize: 200,
          identity: ''
        }
        queryParams.identity = res.data.configValue.split(',').find(item => item == this.$store.state.user.info.userId) ? 'supervise' : ''
        todoItemsList(queryParams).then(res => {
          if (res.code === 200) {
            this.todoData = res
          } else {
            this.$message.error(res.msg)
          }
        })
      }
    })
  },
  computed: {
    ...mapGetters(['avatar']),
    sendTitle() {
      return this.sending ? `${this.second}s后重新获取` : '获取验证码'
    },
    userId() {
      return this.$store.getters.info.userId
    }
  },
  mounted() {
    const resizeTarget = this.$refs.boxLeft
    const resizeObserver = new ResizeObserver(entries => {
      for (const entry of entries) {
        const { width, height } = entry.contentRect
        this.refreshChart()
      }
    })
    resizeObserver.observe(resizeTarget)
    this.$once('hook:beforeDestroy', () => {
      resizeObserver.disconnect()
    })
    window.onresize = () => {
      this.refreshChart()
    }
  },
  beforeDestroy() {
    window.onresize = null
  },
  methods: {
    removeHtmlTag,
    checkPermi,
    parseTime,
    // 获取分类树
    getTree() {
      getlist().then(res => {
        if (res.code === 200) {
          this.treeData = res.data
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 用户信息
    getUserInfo() {
      getUserProfile().then(async res => {
        if (res.code === 200) {
          localStorage.setItem('admin', res.data.admin)
          this.userInfo = res.data
          const isWallet = await this.getConfigKey('wallet.switch')
          this.isWallet = isWallet.msg
          if (this.isWallet == 'true') {
            const { data } = await walletBalance()
            this.$set(this.userInfo, 'wallet', data.toFixed(2) + '元')
          }
        } else {
          this.$message.error(res.msg)
        }
        if (res.company.id > 0) {
          localStorage.setItem('role', 'gys')
          localStorage.setItem('companyId', res.company.id)
          await this.getSupplier(res.company.id) // 查询供应商信息
          this.getUserBrand()
          this.usertype = 'gys'
          this.handleContractSaleList()
          this.handleListUnsalableSupplierContract()
        } else {
          localStorage.setItem('role', 'cgs')
          localStorage.removeItem('companyId')
          localStorage.removeItem('companyLevel')
          this.usertype = 'cgs'
        }
        //查询评论图
        await this.getChart()
        if (checkPermi(['system:user:query']) && checkPermi(['jim:set:customer']) && !res.data.admin) {
          await this.getServiceOptions()
        }
      })
    },
    // 查询品牌
    getUserBrand() {
      userBrandList().then(res => {
        if (res.code === 200) {
          this.brandList = res.data
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    //获取供应商信息
    async getSupplier(id) {
      const res = await supplier({ id: id })
      const data = res.data.supplier
      // 回显场地图片
      this.siteImg = data.siteImg ? data.siteImg.split(',') : []
      this.siteImglist = this.siteImg.map(item => this.imgPath + item)
      // 回显设备照片
      this.deviceImg = data.deviceImg ? data.deviceImg.split(',') : []
      this.deviceImglist = this.deviceImg.map(item => this.imgPath + item)
      // 回显证书
      this.certImg = data.certImg ? data.certImg.split(',') : []
      this.certImglist = this.certImg.map(item => this.imgPath + item)
      // 工艺照片/视频
      this.process = data.process ? data.process.split(',') : []
      this.processlist = this.process.map(item => this.imgPath + item)
      this.companyInfo = data
      this.level = res.data.company.level
      this.isCertified = !!res.data.supplier.isCertified
      this.hasCertified = res.data.hasOwnProperty('certify') && res.data.certify
      this.apply = res.data.apply
      localStorage.setItem('companyLevel', res.data.company.level)
      const categoryList = data.categoryList || []
      this.categoryData = []
      categoryList.map(Number).forEach(item => {
        let selectId = [this.cascadeDisplay(this.treeData, item)]
        this.categoryData.push(...selectId)
      })
      this.oldcategoryName = this.getDepart(categoryList, this.treeData)
      this.categoryName = this.getDepart(categoryList, this.treeData)
      this.hasNewCate = res.data.hasOwnProperty('newCategoryIds') && !!res.data.newCategoryIds
      this.key = Math.random()
    },
    cascadeDisplay(object, value) {
      for (const key in object) {
        if (object[key].id == value) return [object[key].id]
        if (object[key].children && Object.keys(object[key].children).length > 0) {
          const temp = this.cascadeDisplay(object[key].children, value)
          if (temp) return [object[key].id, temp].flat()
        }
      }
    },
    getDepart(arr, data, department = []) {
      if (typeof data === 'object') {
        for (let i = 0; arr[i] !== undefined; i++) {
          for (let j = 0; data[j] !== undefined; j++) {
            if (Number(arr[i]) === Number(data[j].id) || Number(arr[i]) === Number(data[j].id)) {
              department.push(data[j])
            }
          }
        }
        for (let m = 0; data[m] !== undefined; m++) {
          this.getDepart(arr, data[m].children, department)
        }
      }
      return department
    },
    // 绑定微信
    handleWechat() {
      let that = this
      that.wechaOpen = !that.wechaOpen
      if (that.wechaOpen) {
        if (that.$refs.qrcode) that.$refs.qrcode.innerHTML = ''
        qrcode({ requestId: 'pes_user_' + that.userInfo.userId }).then(function (res) {
          that.qrcode = res.data.url
          new QRCode(that.$refs.qrcode, {
            width: 150, // 二维码宽度
            height: 150, // 二维码高度
            text: that.qrcode
          })
        })
      }
    },
    // 获取雷达图
    async getChart() {
      const res = await chart()
      if (this.usertype === 'gys') {
        const purchase = res.data.company_supplier
        this.starData = res.data.company_supplier
        if (purchase) this.option.series.data[0].value = [purchase.capacity, purchase.sales, purchase.service, purchase.speed, purchase.times]
      } else {
        const purchase = res.data.purchase
        this.starData = res.data.purchase
        if (purchase) this.option.series.data[0].value = [purchase.capacity, purchase.sales, purchase.service, purchase.speed, purchase.times]
      }
    },
    // 刷新图表
    refreshChart() {
      const chartDom = document.getElementById('main')
      if (!chartDom) return
      if (!!this.myChart) {
        this.myChart.dispose()
        this.$nextTick(() => {
          this.myChart = echarts.init(chartDom)
          this.myChart.setOption(this.option)
        })
      } else {
        this.myChart = echarts.init(chartDom)
        this.myChart.setOption(this.option)
      }
    },
    // 修改个人信息
    // prettier-ignore
    editAbout() {
      const that = this
      this.$prompt('请输入标语', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false,
        inputValue: that.companyInfo.slogan,
        inputPattern: /^.+$/,
        inputErrorMessage: '请输入标语'
      }).then(({ value }) => {
        const data = { slogan: value, supplierId: that.companyInfo.id }
        ediSupplier(data).then(function (res) {
          if (res.code === 200) {
            that.getSupplier(that.companyInfo.id)
          } else {
            that.$message.error(res.msg)
          }
        })
      }).catch(() => {
      })
    },
    handleBeforeUpload(file) {
      if (!getToken()) {
        this.$message.error(`请先登录`)
        return false
      }
    },
    // 场地照片
    handleSuccess(response) {
      if (response.code === 200) {
        let that = this
        const siteImgArr = [...that.siteImg]
        siteImgArr.push(response.fileName)
        const siteImgStr = siteImgArr.toString()
        const data = {
          siteImg: siteImgStr,
          supplierId: that.companyInfo.id
        }
        const res = ediSupplier(data).then(function (res2) {
          that.getSupplier(that.companyInfo.id)
        })
        this.$refs.siteImg.clearFiles()
      }
    },
    // prettier-ignore
    handleRemove(file) {
      let that = this
      const siteImgArr = [...that.siteImg]
      const siteIndex = siteImgArr.indexOf(file)
      siteImgArr.splice(siteIndex, 1)
      const siteImgStr = siteImgArr.toString()
      const data = {
        siteImg: siteImgStr,
        supplierId: that.companyInfo.id
      };
      this.$modal.confirm('是否确认删除场地图片？').then(function () {
        return ediSupplier(data)
      }).then(() => {
        that.getSupplier(that.companyInfo.id)
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      });
    },
    //设备照片
    handleSuccess2(response) {
      if (response.code === 200) {
        let that = this
        const deviceImgArr = [...that.deviceImg]
        deviceImgArr.push(response.fileName)
        const deviceImgStr = deviceImgArr.toString()
        const data = {
          deviceImg: deviceImgStr,
          supplierId: that.companyInfo.id
        }
        const res = ediSupplier(data).then(function (res2) {
          that.getSupplier(that.companyInfo.id)
        })
        this.$refs.deviceImg.clearFiles()
      }
    },
    // prettier-ignore
    handleRemove2(file) {
      let that = this
      const deviceImgArr = [...that.deviceImg]
      const deviceIndex = deviceImgArr.indexOf(file)
      deviceImgArr.splice(deviceIndex, 1)
      const deviceImgStr = deviceImgArr.toString()
      const data = {
        deviceImg: deviceImgStr,
        supplierId: that.companyInfo.id
      }
      this.$modal.confirm('是否确认删除设备照片？').then(function () {
        return ediSupplier(data)
      }).then(() => {
        that.getSupplier(that.companyInfo.id)
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      });
    },
    //设备照片
    handleSuccess3(response) {
      if (response.code === 200) {
        let that = this
        const certImgArr = [...that.certImg]
        certImgArr.push(response.fileName)
        const certImgStr = certImgArr.toString()
        const data = {
          certImg: certImgStr,
          supplierId: that.companyInfo.id
        }
        const res = ediSupplier(data).then(function (res2) {
          that.getSupplier(that.companyInfo.id)
        })
        this.$refs.certImg.clearFiles()
      }
    },
    // prettier-ignore
    handleRemove3(file) {
      let that = this
      const certImgArr = [...that.certImg]
      const deviceIndex = certImgArr.indexOf(file)
      certImgArr.splice(deviceIndex, 1)
      const certImgStr = certImgArr.toString()
      const data = {
        certImg: certImgStr,
        supplierId: that.companyInfo.id
      }
      this.$modal.confirm('是否确认删除证书？').then(function () {
        return ediSupplier(data)
      }).then(() => {
        that.getSupplier(that.companyInfo.id)
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      });
    },
    //工艺照片/视频
    handleSuccess4(response) {
      if (response.code === 200) {
        let that = this
        const processArr = [...that.process]
        processArr.push(response.fileName)
        const processStr = processArr.toString()
        const data = {
          process: processStr,
          supplierId: that.companyInfo.id
        }
        const res = ediSupplier(data).then(function (res2) {
          that.getSupplier(that.companyInfo.id)
        })
        this.$refs.process.clearFiles()
      }
    },
    // prettier-ignore
    handleRemove4(file) {
      let that = this
      const processArr = [...that.process]
      const deviceIndex = processArr.indexOf(file)
      processArr.splice(deviceIndex, 1)
      const processStr = processArr.toString()
      const data = {
        process: processStr,
        supplierId: that.companyInfo.id
      }
      this.$modal.confirm('是否确认删除工艺照片/视频？').then(function () {
        return ediSupplier(data)
      }).then(() => {
        that.getSupplier(that.companyInfo.id)
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      });
    },
    // 编辑供应类型
    handleEdit() {
      this.newcategoryName = [...this.categoryName]
      this.newcategoryData = [...this.categoryData]
      this.cateKG = true
    },
    // 选择供应商类型
    handleChangeCategory() {
      const arr = this.$refs['category'].getCheckedNodes()
      const categoryIds = arr.map(item => item.value)
      const categoryName = this.getDepart(categoryIds, this.treeData)
      this.$set(this, 'newcategoryName', categoryName)
    },
    // 删除供应商类型
    deleteTag(index) {
      this.newcategoryName.splice(index, 1)
      const categoryIds = this.newcategoryName.map(item => item.id)
      let categoryData = []
      categoryIds.map(Number).forEach(item => {
        let selectId = [this.cascadeDisplay(this.treeData, item)]
        categoryData.push(...selectId)
      })
      this.$set(this, 'newcategoryData', categoryData)
    },
    handleSubmitKG() {
      const categoryIds = this.newcategoryName.map(item => item.id)
      const supplierId = this.companyInfo.id
      const data = { categoryIds, supplierId }
      const newArr = this.newcategoryName.filter(item => !this.oldcategoryName.includes(item))
      const msg = !!newArr.length ? '提交成功，请等待审核' : '操作成功'
      ediSupplier(data).then(res => {
        this.$message.success(msg)
        this.cateKG = false
        this.getSupplier(supplierId)
      })
    },
    // 格式化JSON内容
    formatNoticeContent(content) {
      if (this.isJsonNotice(content)) {
        try {
          const contentObj = JSON.parse(content)
          return `${contentObj.userName}于${this.parseTime(contentObj.leaveStartTime, '{y}-{m}-{d} {h}:{i}')}至${this.parseTime(contentObj.leaveEndTime, '{y}-{m}-{d} {h}:{i}')}请假，工作交接给：${contentObj.handoverUser}，交接内容及详细描述：${contentObj.content}，联系电话${contentObj.phone}`
        } catch (e) {
          return content
        }
      }
      return this.formatWorkHandoverTime(content)
    },
    // 格式化工作交接消息内容中的时间
    formatWorkHandoverTime(content) {
      if (!this.isJsonNotice(content)) {
        // 匹配所有时间格式 YYYY-MM-DD HH:mm:ss
        const timeRegex = /\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2}/g
        const timeMatches = content.match(timeRegex)

        if (timeMatches && timeMatches.length >= 2) {
          // 格式化时间为年-月-日 时:分
          const formatTime = timeStr => {
            const date = new Date(timeStr)
            const year = date.getFullYear()
            const month = String(date.getMonth() + 1).padStart(2, '0')
            const day = String(date.getDate()).padStart(2, '0')
            const hours = String(date.getHours()).padStart(2, '0')
            const minutes = String(date.getMinutes()).padStart(2, '0')
            return `${year}-${month}-${day} ${hours}:${minutes}`
          }

          // 只替换前两个时间字符串为格式化后的时间，保留其他内容
          let replacedCount = 0
          const newContent = content.replace(timeRegex, match => {
            if (replacedCount < 2) {
              replacedCount++
              return formatTime(match)
            }
            return match
          })
          return newContent
        }
        return content
      }
    },
    // 消息中心列表
    async getNoticelist() {
      try {
        this.noticeLoading = true
        const res = await newslist(this.noticeQuery)
        if (res.code === 200) {
          // 格式化并过滤消息
          this.noticeList = res.rows.map(this.formatNotice)
          this.noticeTotal = res.total
        } else {
          this.$message.error(res.msg)
        }
      } catch (error) {
        this.$message.error('获取消息列表失败')
      } finally {
        this.noticeLoading = false
      }
    },
    // 判断公告内容是否为json
    isJsonNotice(noticeContent) {
      try {
        JSON.parse(noticeContent)
        return true
      } catch (error) {
        return false
      }
    },
    // 查询是否有销售合同
    handleContractSaleList() {
      contractSaleList({ pageNum: 1, pageSize: 1 }).then(res => {
        const { code, rows } = res
        if (code === 200) this.contractSaleList = rows
      })
    },
    // 查询滞销品合同数量
    handleListUnsalableSupplierContract() {
      listUnsalableSupplierContract({ pageNum: 1, pageSize: 1 }).then(res => {
        const { code, rows } = res
        if (code === 200) this.unsalableContractList = rows
      })
    },
    // 跳转至销售合同
    handleSkip(type = '') {
      this.$router.push({ path: '/unsalablesell', query: { classify: type } })
    },
    // 打开请假日历
    handleWorkHandover() {
      this.$refs.weatherDate.handleOpen('workHandover')
    },
    // 消息详情
    async handleDetail(item) {
      try {
        const res = await getNotice(item.noticeId)
        if (res.code === 200) {
          this.noticeInfo = res.data
          // if (this.noticeInfo.noticeTitle === '工作交接') {
          //   // const contentObj = JSON.parse(this.noticeInfo.noticeContent)
          //   this.$refs.weatherDate.handleOpen('workHandover')
          // } else {
          // 格式化消息内容
          if (this.isJsonNotice(this.noticeInfo.noticeContent)) {
            try {
              const contentObj = JSON.parse(this.noticeInfo.noticeContent)
              this.noticeInfo.noticeContent = `${contentObj.userName}于${this.parseTime(contentObj.leaveStartTime, '{y}-{m}-{d} {h}:{i}')}至${this.parseTime(contentObj.leaveEndTime, '{y}-{m}-{d} {h}:{i}')}请假，工作交接给：${contentObj.handoverUser}，交接内容及详细描述：${contentObj.content}，联系电话${contentObj.phone}`
            } catch (e) {}
          }
          this.noticeOpen = true
          // }
        } else {
          this.$message.error(res.msg)
        }
      } catch (error) {
        this.$message.error('获取消息详情失败')
      }
    },
    // 关闭消息详情
    handleClose() {
      const id = this.noticeInfo.noticeId
      if (this.noticeInfo.noticeType == 'company_notice') {
        const index = this.noticeList.findIndex(item => item.noticeId == id)
        this.noticeList[index].status = -1
        this.noticeOpen = false
        const noticeListRead = localStorage.getItem(this.userId + '.noticeListRead')
        if (noticeListRead) {
          // 检查当前id是否已存在
          const existingIds = noticeListRead.split(',')
          if (!existingIds.includes(id.toString())) {
            localStorage.setItem(this.userId + '.noticeListRead', noticeListRead + ',' + id)
          }
        } else {
          localStorage.setItem(this.userId + '.noticeListRead', id)
        }
        return
      }
      if (this.noticeInfo.status === 1) {
        const status = -1
        updateNotice({ id, status }).then(res => {
          if (res.code === 200) {
            this.getNoticelist()
            this.noticeOpen = false
          } else {
            this.$message.error(res.msg)
          }
        })
      } else {
        this.noticeOpen = false
      }
    },
    // 查询是否有邀请
    getInviteHas() {
      inviteHas().then(res => {
        if (res.code === 200) {
          this.inviteInfo = res.data
          if (this.inviteInfo.companyName) this.inviteOpen = true
        }
      })
    },
    // 是否接受邀请
    inviteSubmit(val) {
      let data
      val ? (data = { confirm: true }) : (data = { confirm: false })
      inviteVerify(data).then(res => {
        this.inviteOpen = false
      })
    },
    // 跳转至钱包页面
    handleWallet() {
      this.$router.push('/wallet/index')
    },
    // 升级为企业
    handleUpgrade() {
      this.upgradeForm = {
        address: undefined,
        companyName: undefined,
        licenseImg: undefined,
        logo: 'logo',
        smsCode: undefined,
        uscCode: undefined,
        phone: undefined
      }
      this.resetForm('upgradeForm')
      this.sending = false
      this.second = 60
      this.upgradeForm.phone = this.userInfo.phonenumber
      this.upgradeOpen = true
    },
    // 上传营业执照
    handlelicenseImgRemove(file) {
      this.upgradeForm.licenseImg = undefined
      this.hideUpload = false
      this.fileList = []
    },
    handlelicenseImgSuccess(res) {
      this.upgradeForm.licenseImg = res.fileName
      this.hideUpload = true
      this.fileList = [res]
      this.$refs.upgradeForm.clearValidate('licenseImg')
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = this.imgPath + file.fileName
      this.dialogVisible = true
    },
    handleSend() {
      this.$refs.upgradeForm.validateField('phone', valid => {
        if (!valid) {
          this.sending = true
          this.second = 60
          smsCode({ phone: this.upgradeForm.phone }).then(res => {
            this.$message.success('验证码发送成功')
            const timer = setInterval(() => {
              this.second--
              if (this.second <= 0) {
                clearInterval(timer)
                this.sending = false
              }
            }, 1000)
          })
        }
      })
    },
    handleSumit() {
      this.$refs.upgradeForm.validate(valid => {
        if (valid) {
          userUpgrade(this.upgradeForm).then(res => {
            if (res.code === 200) {
              this.$message.success('升级成功')
              this.upgradeOpen = false
              location.reload()
            } else {
              this.$message.error(res.msg)
            }
          })
        }
      })
    },
    // 升级VIP
    // prettier-ignore
    handleUpgradeVip() {
      this.$confirm('是否确认升级为认证采购商?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        userUpgradeVip().then(res => {
          if (res.code === 200) {
            this.$message.success('提交成功,请等待审核')
            this.vipOpen = false
            this.$set(this, 'apply', true)
          } else {
            this.$message.error(res.msg)
          }
        })
      }).catch(() => {
      })
    },
    // 申请认证企业
    // prettier-ignore
    handleUpgradeCertified() {
      this.$confirm('是否确认申请企业认证?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        companyAuth().then(res => {
          if (res.code === 200) {
            this.$message.success('提交成功,请等待审核')
            this.certifiedOpen = false
            this.$set(this, 'hasCertified', true)
          } else {
            this.$message.error(res.msg)
          }
        })
      }).catch(() => {
      })
    },
    // 添加品牌
    // prettier-ignore
    handleAddBrand() {
      const that = this
      this.$prompt('请输入品牌名称', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false,
        inputPattern: /^.+$/,
        inputErrorMessage: '请输入品牌名称'
      }).then(({ value }) => {
        const data = { name: value, companyId: that.companyInfo.id }
        userBrandAdd(data).then(function (res) {
          if (res.code === 200) {
            that.getUserBrand()
          } else {
            that.$message.error(res.msg)
          }
        })
      }).catch(() => {
      })
    },
    // 删除品牌
    // prettier-ignore
    handleDelBrand(item) {
      const name = item.name
      const data = { id: item.id }
      this.$modal.confirm(`是否确认删除品牌${name}？`).then(function () {
        return userBrandDel(data)
      }).then(() => {
        this.getUserBrand()
        this.$modal.msgSuccess('删除成功')
      }).catch(() => {
      })
    },
    // 供应商信息
    handleMore() {
      this.$refs.edit.getInfo(this.companyInfo)
    },
    // 检查价格是否有变化
    handleCheckPrice() {
      listUnsalable().then(res => {
        const { total } = res
        if (!!total) {
          checkUnsalablePrice().then(res => {
            const { code, msg, data } = res
            if (code === 200) {
              this.isUpdate = !!data.length
            } else this.$message.error(msg)
          })
        }
      })
    },
    // 价格有变化更新价格或忽略价格
    handleUpdatePrice(key = 'refresh') {
      updateUnsalablePrice({ key }).then(res => {
        const { code, msg } = res
        if (code === 200) {
          this.isUpdate = false
          this.$message.success('操作成功')
        } else this.$message.error(msg)
      })
    },
    // 查询部门用户数据
    async getServiceOptions() {
      this.serviceOpen = true
      const dept = await deptTreeSelect()
      const user = await listUser()
      const children = dept.data[0].children || []
      const deptData = [...children, ...[{ id: -1, label: '其他', value: -1 }]] || []
      const userData = user.rows || []
      const getChildren = data => {
        data.forEach(item => {
          item.value = item.id
          if (item.children) {
            getChildren(item.children)
          } else {
            item.children = []
          }
        })
      }
      getChildren(deptData)
      const addChildren = data => {
        data.forEach(item => {
          userData.forEach(user => {
            if (item.id === user.deptId && item.children) {
              item.children.push({
                id: user.userId,
                label: user.realName || user.nickName,
                value: user.userId,
                disabled: user.status == '1',
                userName: user.userName
              })
            }
            if (item.id === -1 && (!user.deptId || user.deptId === dept.data[0].id) && item.children) {
              item.children.push({
                id: user.userId,
                label: user.realName || user.nickName,
                value: user.userId,
                disabled: user.status == '1',
                userName: user.userName
              })
            }
          })
          if (item.children && item.children.length) {
            addChildren(item.children)
          }
        })
      }
      addChildren(deptData)
      this.serviceOptions = deptData
      this.getCurService()
    },
    // 查找当前设置的客服
    getCurService() {
      getService().then(res => {
        if (res.code === 200) {
          this.service = this.findParent(this.serviceOptions, res.data)
          const name = this.findParentName(this.serviceOptions, res.data)
          this.curService = name.join('/')
        }
      })
    },
    // prettier-ignore
    handleChangeService(value) {
      const assign = value[value.length - 1]
      const assignData = this.findInTree(this.serviceOptions, assign)
      const name = assignData.label || ''
      const userName = assignData.userName || ''
      if (userName) {
        this.$confirm(`是否将${name}设置为企业客服?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          setService({ assign: userName }).then(res => {
            if (res.code === 200) {
              this.$message.success('设置成功')
              this.getCurService()
            } else {
              this.$message.error(res.msg)
            }
          })
        }).catch(() => { })
      }
    },
    // 从树结构内找到相同id
    findInTree(tree, id) {
      for (let i = 0; i < tree.length; i++) {
        if (tree[i].id === id) {
          return tree[i]
        } else if (tree[i].children && tree[i].children.length) {
          const res = this.findInTree(tree[i].children, id)
          if (res) return res
        }
      }
    },
    // 找父级
    findParent(tree, data) {
      for (let i = 0; i < tree.length; i++) {
        if (tree[i].id === data.userId) {
          return [tree[i].id]
        } else if (tree[i].children && tree[i].children.length) {
          const res = this.findParent(tree[i].children, data)
          if (res) return [tree[i].id, ...res]
        }
      }
    },
    // 找父级名称
    findParentName(tree, data) {
      for (let i = 0; i < tree.length; i++) {
        if (tree[i].id === data.userId) {
          return [tree[i].label]
        } else if (tree[i].children && tree[i].children.length) {
          const res = this.findParentName(tree[i].children, data)
          if (res) return [tree[i].label, ...res]
        }
      }
    },
    // 树形数据扁平化
    treeToFlat(data) {
      let ary = []
      data.forEach(item => {
        if (item.children) {
          ary.push(item)
          if (item.children.length > 0) {
            ary.push(...this.treeToFlat(item.children))
          }
        } else {
          ary.push(item)
        }
        delete item.children
      })
      return ary
    },
    // 获取申请中的国标甄选
    async getOptimalIds(type) {
      let res = {}
      if (type == 'view') {
        res = await productOptimalPassedList()
        res.data.forEach(el => {
          this.optimalIds.push(el.id)
        })
      }
      if (type == 'add') {
        res = await productOptimalPassedIds()
        this.optimalIds = res.data
      }
      this.selectCategoryList = []
      let categoryData = []
      for (let i = 0; i < this.categoryData.length; i++) {
        if (categoryData.findIndex(arr => arr[1] === this.categoryData[i][1]) == -1 && this.categoryData[i][1]) {
          categoryData.push([this.categoryData[i][0], this.categoryData[i][1]])
        }
      }
      let treeData = this.treeToFlat(JSON.parse(JSON.stringify(this.treeData)))
      categoryData.forEach(el => {
        let obj = {
          pageNum: 1,
          pageSize: 10000,
          categoryId: el[1],
          isOptimal: true
        }
        listProduct(obj).then(res => {
          if (res.code == 200 && res.total > 0) {
            let categoryName = []
            let checkedALL
            let disabled
            categoryName.push(treeData.find(item => item.id == el[0]).name)
            categoryName.push(treeData.find(item => item.id == el[1]).name)
            res.rows.forEach(ite => {
              if (this.optimalIds.find(ids => ite.id == ids)) {
                ite.checked = true
                ite.disabled = true
              } else {
                ite.checked = false
                ite.disabled = false
              }
            })
            if (res.rows.filter(item => item.checked == false).length == 0) {
              checkedALL = true
              disabled = true
            } else {
              checkedALL = false
              disabled = false
            }
            this.selectCategoryList.push({
              categoryId: obj.categoryId,
              categoryName: categoryName.join('＞'),
              checkedAll: checkedALL,
              disabled: disabled,
              selectList: res.rows
            })
          }
        })
      })
    },
    // 设置国标甄选
    setSelect(type) {
      this.getOptimalIds(type)
      this.selectType = type
      this.selectBoxShow = true
    },
    // 国标甄选选择
    selectItemChange(data, obj) {
      if (data.checked == true) {
        obj.checkedALL = obj.selectList.filter(item => item.checked === false).length == 0
      } else if (data.checked == false) {
        obj.checkedALL = false
      }
    },
    selectAllChange(item) {
      if (item.checkedALL == true) {
        item.selectList.forEach(el => {
          if (el.disabled == false) {
            el.checked = true
          }
        })
      } else if (item.checkedALL == false) {
        item.selectList.forEach(el => {
          if (el.disabled == false) {
            el.checked = false
          }
        })
      }
    },
    handleSubmitSelect() {
      this.productList = []
      this.selectCategoryList.forEach(el => {
        el.selectList.forEach(ite => {
          if (ite.checked == true && ite.disabled == false) {
            ite.productId = ite.id
            this.productList.push(ite)
          }
        })
      })
      productOptimalApply({
        products: this.productList
      }).then(res => {
        if (res.code == 200) {
          this.$message.success('操作成功')
          setTimeout(() => {
            this.selectBoxShow = false
            this.applyTipShow = true
          }, 1000)
        }
      })
    },
    goTodo() {
      this.$router.push({
        path: '/oa/listing'
      })
    },
    // 跳转
    handleJump() {
      this.$router.push({ path: '/oa/workJoin', query: { identity: 'handover' } })
    },
    // 从noticeContent中提取第二个时间
    getSecondTimeFromContent(content) {
      if (!content) return null
      // 使用正则表达式匹配时间格式 YYYY-MM-DD HH:mm:ss或者YYYY-MM-DD HH:mm
      const timeRegex = /\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2}|\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}/g
      const timeMatches = content.match(timeRegex)
      // 如果找到了至少两个时间，返回第二个时间
      if (timeMatches && timeMatches.length >= 2) {
        return timeMatches[1] // 返回第二个时间
      }
      return null
    },
    // 格式化消息内容
    formatNotice(notice) {
      // 格式化noticeContent
      notice.noticeContent = this.formatNoticeContent(notice.noticeContent)
      // 检查消息是否已读
      const noticeListRead = localStorage.getItem(this.userId + '.noticeListRead')
      if (noticeListRead) {
        const readIds = noticeListRead.split(',')
        if (readIds.includes(notice.noticeId.toString())) {
          notice.status = -1
        }
      }
      return notice
    },
    // 过滤公司公告消息
    filterCompanyNotice(notice) {
      if (notice.noticeType === 'company_notice') {
        const secondTime = this.getSecondTimeFromContent(notice.noticeContent) || notice.createTime
        const createTime = new Date(secondTime)
        const currentTime = new Date()
        return createTime >= currentTime
      }
      return true
    },
    // 查看更多消息
    async handleMoreNotice() {
      // 检查是否还有更多消息
      if (this.noticeTotal <= this.noticeQuery.pageNum * this.noticeQuery.pageSize || this.moreNoticeLoading) {
        return
      }
      try {
        this.moreNoticeLoading = true
        this.noticeQuery.pageNum++
        const res = await newslist(this.noticeQuery)
        if (res.code === 200) {
          // 格式化并过滤新消息
          const newNotices = res.rows.map(this.formatNotice)
          this.noticeList = [...this.noticeList, ...newNotices]
          this.noticeTotal = res.total
        } else {
          this.$message.error(res.msg)
        }
      } catch (error) {
        this.$message.error('加载更多消息失败')
      } finally {
        this.moreNoticeLoading = false
      }
    },
    // 获取今日请假人数
    async getWorkHandoverTodayLeave() {
      const res = await workHandoverTodayLeave()
      if (res.code === 200) {
        this.workHandoverTodayLeaveData = res.data
        const arr = new Set(this.workHandoverTodayLeaveData.map(item => item.createBy))
        this.workHandoverTodayLeaveString = Array.from(arr).join(',')
      }
    },

    // 查看待办详情
    handleToDo() {
      let year = new Date().getFullYear()
      let month = new Date().getMonth() + 1
      let date = new Date().getDate()
      this.lunarInfo = LunarCalendar.solarToLunar(year, month, date)
      this.toDoViewOpen = true
    },
    // 新增待办
    handleAddToDo() {
      this.toDoAddOpen = true
    },
    // 处理tab切换
    handleTabClick(tab, event) {
      // 强制重新渲染图片列表
      this.$nextTick(() => {
        // 触发窗口resize事件，确保图片正确显示
        window.dispatchEvent(new Event('resize'))
      })
    }
  }
}
</script>
<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
@import '~@/assets/styles/custom-chart.scss';

::v-deep {
  .hide {
    height: 128px;

    .el-upload--picture-card {
      display: none;
    }

    .el-upload-list__item {
      margin-bottom: 0;
    }
  }

  .el-upload-list--picture-card {
    .el-upload-list__item {
      width: 128px;
      height: 128px;
    }
  }

  .el-upload--picture-card {
    width: 128px;
    height: 128px;
    line-height: 128px;
    display: inline-flex;
    flex-direction: column;
    justify-content: center;

    .el-upload-picture-card-title {
      font-size: 14px;
      color: #999999;
      line-height: 32px;
    }
  }

  .custom-dialog-btn {
    width: 180px;
  }

  .newBox.vh-85 {
    overflow: unset;
  }
}

.shadow {
  box-shadow: 0 0 26px 0 rgba(0, 0, 0, 0.05);
}

.pointer {
  cursor: pointer;
}

.box {
  padding: 20px;

  &-left {
    padding: 0 20px 20px;
    background: #ffffff;
  }
}

@media (max-width: 1700px) {
  .box {
    .num-box {
      margin-top: 20px;
    }
  }
}

@media (max-width: 1367px) {
  .about-evaluate {
    flex-direction: column;

    .about {
      width: 100% !important;
    }

    .evaluate {
      width: 100% !important;
      margin-top: 10px;
    }
  }
}

.about-evaluate {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  padding-bottom: 20px;
  width: 100%;

  .about {
    min-width: 48%;
    flex-shrink: 0;
    margin-right: 20px;

    &-title {
      font-size: 14px;
      margin-top: 20px;
      margin-bottom: 20px;
    }

    &-info {
      min-height: 162px;
      background-color: #f9f9fa;
      display: flex;
      align-items: center;

      &.jccenter {
        justify-content: center;
      }

      &-avatar {
        width: 90px;
        height: 90px;
        background: #ffffff;
        border: 1px solid #cbd6e2;
        border-radius: 50%;
        overflow: hidden;
        margin-right: 15px;
        margin-left: 20px;
        flex-shrink: 0;

        img {
          width: 100%;
          height: 100%;
        }
      }

      &-item {
        width: 100%;
        padding-right: 20px;

        .item-title {
          width: 100%;
          color: #333333;
          font-size: 18px;
          line-height: 30px;
          height: 30px;
          overflow: hidden;
          font-weight: 500;
          display: inline-flex;
          align-items: center;
          justify-content: space-between;

          &-vip {
            color: #7f4d03;
            display: inline-block;
            border: 1px solid #fdcc8b;
            background: linear-gradient(102deg, #fde4af 0%, #f6c576 100%);
            border-radius: 5px;
            line-height: 24px;
            height: 24px;
            padding: 0 15px;
            font-size: 12px;
            margin-left: 10px;
          }

          &-more {
            font-size: 12px;
            color: #666666;
            cursor: pointer;

            &:hover {
              color: #2e73f3;
            }
          }
        }

        .item-id {
          margin-top: 10px;
          color: #666666;
          font-size: 12px;
          line-height: 30px;
          margin-bottom: 8px;
          display: inline-flex;
          align-items: center;

          span {
            font-size: 16px;
            font-weight: 500;
            color: #f35d09;
            margin-left: 10px;
            margin-right: 5px;
          }

          em {
            border: 1px solid #f35d09;
            color: #f35d09;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            text-align: center;
            font-style: normal;
            font-size: 14px;
          }
        }

        .item-authentication {
          width: 100%;
          display: flex;
          justify-content: flex-start;
          padding: 3px 0;

          span {
            display: inline-block;
            width: 98px;
            height: 26px;
            line-height: 26px;
            font-size: 14px;
            color: $white;
            background: url('~@/assets/images/authentication_n.png') no-repeat;
            background-size: 100% 100%;
            padding-left: 30px;
            cursor: pointer;

            &.yes {
              background: url('~@/assets/images/authentication_y.png') no-repeat;
              background-size: 100% 100%;
            }
          }
        }

        .item-desc {
          font-size: 16px;
          line-height: 30px;
          color: #333333;
        }

        .item-btn {
          font-size: 14px;
          color: #2e73f3;
          margin-left: 15px;
        }

        .item-wechat {
          padding: 5px 0;
          position: relative;
          z-index: 1;

          .item-qrcode {
            width: 170px;
            padding: 10px;
            margin-top: 10px;
            background-color: white;
            border-radius: 5px;
          }
        }
      }

      &-box {
        width: 100%;
        min-height: 110px;
        display: flex;
        align-items: center;
        justify-content: flex-start;
      }

      &-brand {
        width: 100%;
        display: flex;
        justify-content: space-between;
        padding: 11px 20px;
        border-top: 1px solid #cbd6e2;
        line-height: 26px;
        font-size: 12px;

        &-list {
          display: flex;
          align-items: center;
        }

        &-title {
          font-size: 12px;
          color: #666666;
        }

        &-item {
          padding: 0 10px;
          cursor: pointer;
          position: relative;

          i {
            display: none;
            position: absolute;
            top: -8px;
            right: -8px;
            font-size: 16px;
          }

          &:hover {
            background-color: #dfebff;
            border-radius: 5px;
            color: #2e73f3;

            i {
              display: block;
              color: #333333;
            }
          }
        }
      }
    }
  }

  .evaluate {
    flex: 1;
    overflow: hidden;

    &-title {
      font-size: 14px;
      margin-top: 20px;
      margin-bottom: 20px;
    }

    &-info {
      padding: 9px 10px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border: 1px solid #cbd6e2;

      &-star {
        flex-shrink: 0;
        height: 142px;
        border-right: 1px solid #e9e9e9;
        padding-right: 15px;
        margin-right: 15px;

        .star-item {
          display: flex;
          flex-direction: row;
          font-size: 12px;
          color: #333333;
          margin: 7px 0;
        }
      }

      &-chart {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        flex: 1;
      }
    }
  }
}

.class {
  padding: 10px 0;

  &-title {
    font-size: 14px;
    color: #666666;
    margin-bottom: 8px;
  }

  &-tree {
    &-tag {
      padding: 10px;
      max-height: 170px;
      border: 1px solid #e4e7ed;
      border-radius: 4px;
      background: #f9f9fa;
      overflow-y: auto;
      display: flex;
      flex-wrap: wrap;
      align-content: flex-start;
      margin-bottom: 10px;

      .tag-item {
        padding: 0 13px;
        border-radius: 5px;
        background-color: #e5e5e5;
        font-size: 12px;
        line-height: 30px;
        color: #333333;
        margin-right: 7px;
        margin-bottom: 7px;
        position: relative;
        transition: all 0.3s;

        .tag-btn {
          display: none;
          position: absolute;
          top: -3px;
          right: -3px;
          color: #333333;
        }

        &:hover,
        &.item-active {
          background-color: #e0ebff;
          color: #2e73f3;

          .tag-btn {
            display: inline-block;
          }
        }
      }
    }
  }
}

::-webkit-scrollbar {
  width: 3px;
}

::-webkit-scrollbar-thumb {
  width: 3px;
  background-color: #cccccc;
}

.photo {
  padding: 10px 0;
  overflow: hidden;

  &-title {
    font-size: 14px;
    line-height: 22px;
    color: #666666;
    margin-bottom: 10px;
  }

  &-tabs {
    ::v-deep {
      .el-tabs__content {
        overflow: visible;
      }
    }
  }

  &-list {
    &-item {
      width: 145px;
      height: 106px;
      position: relative;
      float: left;
      margin-right: 10px;
      margin-bottom: 10px;
      overflow: visible;

      ::v-deep .el-image {
        width: 100%;
        height: 100%;

        .image-slot {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 100%;
          height: 100%;
          background: #f5f7fa;
          color: #909399;
          font-size: 30px;
        }
      }

      .item-video {
        width: 100%;
        height: 100%;
      }

      .photo-btn {
        display: none;
        font-size: 16px;
        position: absolute;
        top: -5px;
        right: -5px;
        z-index: 10;
        background: rgba(255, 255, 255, 0.8);
        border-radius: 50%;
        width: 20px;
        height: 20px;
        line-height: 20px;
        text-align: center;
      }

      &:hover {
        .photo-btn {
          display: inline-block;
        }
      }
    }

    &-upload {
      width: 145px;
      height: 106px;
      float: left;

      ::v-deep {
        .el-upload--picture-card {
          width: 100%;
          height: 100%;
          line-height: 1;

          span {
            display: block;
            font-size: 14px;
            color: #999999;
            margin-top: 8px;
          }
        }
      }
    }
  }
}

.price-update {
  display: flex;
  align-items: center;
  padding: 20px;
  background-color: #fef0f0;
  color: #f56c6c;
  font-size: 20px;
  margin-bottom: 20px;

  span {
    font-size: 14px;
    margin-left: 10px;
    margin-right: 30px;
  }
}

.num-box {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 210px;

  .num-item {
    display: flex;
    flex-direction: column;
    justify-content: center;
    width: 30%;
    height: 110px;
    text-align: center;
    border-right: 1px solid #cbd6e2;
    cursor: pointer;

    &:last-child {
      border-right: 0;
    }

    &-num {
      font-size: 24px;
      color: #666666;
      margin-bottom: 35px;
    }

    &-text {
      font-size: 14px;
      color: #666666;
    }

    &:hover {
      .num-item-num {
        color: #2e64da;
      }
    }
  }
}

.notice-box {
  padding: 20px;
  margin-top: 20px;

  .notice-title {
    font-size: 16px;
    color: #666666;
    margin-bottom: 5px;
  }

  .notice-item-box {
    max-height: 660px;
    padding-right: 10px;
    overflow-y: auto;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: #cbd6e2;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-track {
      background-color: #f5f7fa;
    }
  }

  .notice-item {
    padding: 18px 0;
    border-bottom: 1px solid #e5e9f1;

    &:hover {
      .notice-item {
        &-title {
          b {
            color: #2e64da;
          }
        }

        &-desc {
          b {
            color: #2e64da;
          }

          span {
            background-color: #2e64da;
            border-color: #2e64da;
            color: white;
          }
        }
      }
    }

    &-btn {
      padding-top: 10px;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #2e73f3;
      transition: all 0.3s;
      cursor: pointer;
      font-size: 14px;

      &:hover {
        opacity: 0.8;
      }
    }

    &-title {
      line-height: 20px;
      height: 20px;
      display: flex;
      justify-content: space-between;
      margin-bottom: 5px;

      b {
        font-weight: normal;
        font-size: 16px;
        color: #666666;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        transition: all 0.3s;
      }

      span {
        margin-left: 15px;
        font-size: 14px;
        color: #999999;
        flex-shrink: 0;
      }

      &.unread {
        b {
          color: #333333;

          &:after {
            content: '';
            display: inline-block;
            width: 7px;
            height: 7px;
            background-color: #ec2454;
            margin-left: 10px;
            border-radius: 50%;
          }
        }
      }
    }

    &-desc {
      line-height: 26px;
      height: 26px;
      display: flex;
      justify-content: space-between;

      b {
        font-weight: normal;
        font-size: 14px;
        color: #999999;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        transition: all 0.3s;
      }

      span {
        display: inline-block;
        width: 90px;
        height: 26px;
        margin-left: 15px;
        flex-shrink: 0;
        font-size: 12px;
        text-align: center;
        border: 1px solid #cccccc;
        border-radius: 5px;
        color: #999999;
        transition: all 0.3s;
      }
    }
  }

  .notcie-number {
    display: flex;
    align-items: center;

    &-title {
      font-size: 16px;
      color: #333;
      flex-shrink: 0;
    }

    &-number {
      font-size: 24px;
      color: #f43f3f;
      flex-shrink: 0;
      margin: 0 10px;
    }

    &-tips {
      font-size: 14px;
      color: #999;
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    &-btn {
      font-size: 14px;
      padding: 5px 20px;
      border: 1px solid #ccc;
      border-radius: 5px;
      color: #999999;
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        color: #2e73f3;
        border-color: #2e73f3;
      }
    }
  }
}

.notice-dialog {
  ::v-deep {
    .el-dialog__body {
      padding: 0;
    }
  }
}

.dialog-title {
  font-size: 20px;
  line-height: 30px;
  color: #333333;
  padding: 20px 20px 10px;
  background-color: #f9f9fa;
}

.dialog-date {
  font-size: 14px;
  line-height: 30px;
  padding: 10px 20px;
  background-color: #f9f9fa;
  border-bottom: 1px solid #cbd6e2;
}

.dialog-con {
  font-size: 16px;
  line-height: 28px;
  color: #333333;
  padding: 10px 20px 20px;
  min-height: 300px;
}

.dialog-tip {
  border-top: 1px solid #cbd6e2;
  padding: 10px 20px;
  font-size: 12px;
  line-height: 20px;
  color: #999999;
  text-align: right;
}

.invite-info {
  display: flex;
  align-items: center;
  justify-content: center;

  &-logo {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    box-shadow: 0 0 15px 0 rgba(0, 0, 0, 0.3);
    overflow: hidden;
    margin-right: 20px;
    flex-shrink: 0;

    img {
      width: 100%;
      height: 100%;
    }
  }

  &-item {
    font-size: 14px;
    line-height: 20px;
    margin: 10px 0;
  }
}

.category-cascader {
  height: 400px;

  ::v-deep {
    .el-cascader-menu__wrap {
      height: 100%;
    }
  }
}

.vipBox {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;

  &-item {
    width: 50%;
    border: 1px solid #cbd7e2;
    border-radius: 5px;
    overflow: hidden;
    position: relative;
    background-color: #ffffff;

    &-title {
      height: 90px;
      width: 100%;
      display: inline-flex;
      justify-content: center;
      align-items: center;

      b {
        font-size: 16px;
        font-weight: 500;
        color: #666666;
      }
    }

    ul,
    li {
      margin: 0;
      padding: 0;
      list-style: none;
    }

    li {
      width: 100%;
      height: 90px;
      display: inline-flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: #666666;
      line-height: 20px;

      b {
        font-size: 16px;
        font-weight: 500;
        margin-bottom: 10px;
      }

      span {
        font-size: 12px;
      }

      em {
        font-style: normal;
        color: #2e73f3;
      }

      &:nth-child(2n + 1) {
        background-color: #f9f9fa;
      }
    }

    &.active {
      width: calc(50% + 2px);
      padding-top: 3px;
      padding-bottom: 3px;
      margin-left: -2px;
      box-shadow: 0 1px 20px 0 rgba(46, 115, 243, 0.35);
      border-color: #2e73f3;
      background-color: #f0f6ff;

      li {
        &:nth-child(2n + 1) {
          background-color: #e4edff;
        }
      }
    }

    &-bg {
      position: absolute;
      top: 0;
      left: 0;
    }

    &-tip {
      display: inline-block;
      width: 105px;
      height: 28px;
      line-height: 28px;
      font-size: 14px;
      font-weight: 500;
      color: #ffffff;
      background-color: #2e73f3;
      text-align: center;
      transform: rotate(-45deg);
      position: absolute;
      top: 12px;
      left: -25px;
    }
  }
}

.class-title {
  display: flex;
  height: 40px;
  align-items: center;
}

::v-deep .custom-show-switch {
  position: relative;
  margin: 0 10px;

  .el-switch__core {
    height: 24px;
    border-radius: 12px;
    min-width: 66px;

    &:after {
      left: 4px;
      top: 3px;
    }
  }

  &.el-switch {
    &.is-checked {
      .el-switch__core {
        &:after {
          margin-left: -20px;
          left: 100%;
        }
      }
    }
  }

  &.is-checked {
    .el-switch__label--left {
      opacity: 0;
    }

    .el-switch__label--right {
      opacity: 1;
    }
  }

  .el-switch__label {
    position: absolute;
    top: 0;
  }

  .el-switch__label--left {
    right: 0;
    color: #999999;
    z-index: 1;
    margin-right: 8px;
  }

  .el-switch__label--right {
    left: 0;
    color: #ffffff;
    opacity: 0;
    margin-left: 8px;
  }
}

.class-cascader-box {
  display: flex;
  align-items: center;

  .class-cascader-tip {
    padding-left: 10px;
    font-size: 14px;
  }
}

.selectTips {
  font-weight: 400;
  font-size: 14px;
  color: #666666;
  line-height: 20px;
}

.selectBtn1 {
  cursor: pointer;
  min-width: 149px;
  padding: 0 10px;
  height: 32px;
  background: #ffffff;
  border-radius: 5px 5px 5px 5px;
  border: 1px solid #2e73f3;
  text-align: center;
  margin-right: 15px;

  span {
    font-weight: 500;
    font-size: 12px;
    line-height: 32px;
    color: #2e73f3;
  }
}

.selectBtn2 {
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 129px;
  padding: 0 10px;
  height: 32px;
  background: #2e73f3;
  border-radius: 5px 5px 5px 5px;
  border: 1px solid #2e73f3;

  img {
    width: 16px;
    height: 16px;
    margin-right: 10px;
  }

  span {
    font-weight: 400;
    font-size: 12px;
    color: #ffffff;
  }
}

.select_box {
  padding: 0 20px;

  .select_box_title {
    font-weight: 400;
    font-size: 14px;
    color: #666666;
    line-height: 16px;
    margin-bottom: 10px;
  }

  .select_box_concent {
    min-height: 411px;
    background: #ffffff;
    border: 1px solid #cbd6e2;
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 15px;

    .selectBox_concent_itemBox {
      width: 33.33%;
      border-right: 1px solid #cbd6e2;

      &:nth-child(3n) {
        border-right: none;
      }

      .selectBox_concent_itemBox_category {
        height: 68px;
        background: #eff0f2;
        padding: 11px 15px 14px;
      }

      .selectBox_concent_itemBox_list {
        height: 303px;
        padding: 20px 15px;
        overflow-y: scroll;

        .selectBox_concent_item {
          margin-bottom: 10px;
          display: flex;
          align-items: center;

          .selectBox_concent_item_text {
            width: 129px;
            height: 36px;
            background: linear-gradient(103deg, #f6c576 4%, #fff2d8 96%);
            border-radius: 5px 5px 5px 5px;
            border: 1px solid #a36509;
            margin-right: 15px;
            margin-left: 10px;
            position: relative;

            .selectBox_concent_item_tips {
              position: absolute;
              left: 10px;
              top: -7px;
              width: 50px;
              height: 14px;
              background: #ffffff;
              border-radius: 3px 3px 3px 3px;
              font-size: 10px;
              color: #a36509;
              text-align: center;
            }

            .selectBox_concent_item_title {
              font-size: 12px;
              line-height: 36px;
              color: #a36509;
              text-align: center;
              display: block;
            }

            &.active {
              background: linear-gradient(103deg, #ffcf53 4%, #ffe5a1 96%);
              box-shadow: 0 0 9px 0 #ff9d2b;
              border-radius: 5px 5px 5px 5px;
              border: 1px solid #fba628;

              .selectBox_concent_item_tips {
                background: #774906;
                border-radius: 3px 3px 3px 3px;
                color: #ffffff;
              }

              .selectBox_concent_item_title {
                color: #774906;
                font-weight: 550;
              }
            }
          }
        }
      }

      .selectBox_concent_itemBox_checkAll {
        height: 39px;
        background: #f2f4f6;
        border-top: 1px solid #cbd6e2;
        border-bottom: 1px solid #cbd6e2;
        padding: 10px 20px;
      }
    }
  }
}

.select_btn {
  cursor: pointer;
  width: 269px;
  height: 50px;
  border-radius: 5px 5px 5px 5px;
  border: 1px solid #cbd6e2;
  background: #ffffff;
  color: #999999;
  font-size: 16px;
  margin-bottom: 15px;

  &.primary {
    background: #2e73f3;
    color: #ffffff;
    margin-left: 10px;
  }
}

.applyTip_box {
  padding: 4px 20px 0;

  .applyTip_top {
    display: flex;
    align-items: center;
    flex-direction: column;
    margin-bottom: 22px;

    img {
      width: 42px;
      height: 42px;
      margin-bottom: 17px;
    }

    .applyTip_top_title {
      font-weight: 550;
      font-size: 14px;
      color: #333333;
      line-height: 20px;
      margin-bottom: 10px;
    }

    .applyTip_top_text {
      font-weight: 400;
      font-size: 12px;
      color: #2e73f3;
      line-height: 20px;
    }
  }

  .applyTip_bottom {
    .applyTip_bottom_tips {
      font-weight: 400;
      font-size: 12px;
      color: #999999;
      line-height: 20px;
      text-align: center;
      margin-bottom: 7px;
    }

    .applyTip_bottom_list {
      .applyTip_bottom_item {
        height: 156px;
        background: #f8f9fb;
        border-radius: 5px;
        padding: 15px;
        display: flex;

        .applyTip_bottom_item_img {
          width: 126px;
          height: 126px;
          position: relative;
          margin-right: 15px;

          img {
            width: 100%;
            height: 100%;
          }

          span {
            position: absolute;
            left: 5px;
            top: 5px;
            width: 55px;
            height: 20px;
            background: linear-gradient(103deg, #ef9d1a 4%, #fcd176 96%);
            border-radius: 3px;
            font-weight: 400;
            font-size: 12px;
            color: #7f510e;
            line-height: 20px;
            text-align: center;
          }
        }

        .applyTip_bottom_item_text {
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          justify-content: space-around;
          margin-right: 130px;

          p {
            font-weight: 400;
            font-size: 12px;
            color: #999999;
            margin: 0;
            height: 20px;

            span {
              font-weight: 550;
              font-size: 14px;
              color: #666666;
              margin-left: 20px;
              vertical-align: middle;
            }
          }
        }
      }
    }
  }
}

.applyTip_btn {
  cursor: pointer;
  width: 269px;
  height: 50px;
  background: #2e73f3;
  border-radius: 5px 5px 5px 5px;
  margin: 0 auto;
  line-height: 50px;
  text-align: center;
  font-weight: 500;
  font-size: 16px;
  color: #ffffff;
}
</style>
<style lang="scss">
.class-cascader {
  .el-cascader-panel {
    height: 225px;

    .el-cascader-menu {
      .el-cascader-menu__wrap {
        height: 100%;
        overflow-x: hidden;

        .el-cascader-menu__empty-text {
          color: #606266;
        }
      }

      &:nth-child(3n + 1) {
        background-color: #ffffff;

        .el-cascader-node:not(.is-disabled):focus,
        .el-cascader-node:not(.is-disabled):hover,
        .el-cascader-node:not(.is-disabled).in-active-path {
          background-color: #2e73f3;
          color: #ffffff;
        }
      }

      &:nth-child(3n + 2) {
        background-color: #f2f4f8;

        .el-cascader-node:not(.is-disabled):focus,
        .el-cascader-node:not(.is-disabled):hover,
        .el-cascader-node:not(.is-disabled).in-active-path {
          background-color: #d3dae7;
        }
      }

      &:nth-child(3n) {
        background-color: #d3dae7;

        .el-cascader-node:not(.is-disabled):focus,
        .el-cascader-node:not(.is-disabled):hover,
        .el-cascader-node:not(.is-disabled).in-active-path {
          background-color: #d3dae7;
          color: #2e73f3;
        }
      }
    }
  }
}

.finishTime_box {
  display: flex;
  align-items: center;
  justify-content: center;

  .time {
    margin-right: 7px;
  }

  .tips {
    flex-shrink: 0;
    width: 83px;
    background: url('~@/assets/images/listing_tips_bg.png') center no-repeat;
    background-size: 82.5px 22px;
    font-weight: 500;
    font-size: 12px;
    color: #f50e0e;
    display: flex;
    align-items: center;
    justify-content: center;

    img {
      width: 20px;
      height: 20px;
      margin-right: 7px;
    }
  }
}

.time-text {
  display: block;

  &:last-child {
    &::before {
      display: block;
      margin: -2px 0 -2px 0;
      height: 16px;
      line-height: 16px;
      content: '至';
      color: #999999;
    }
  }
}

.todo_box {
  padding: 0 20px;

  .todo_header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 30px;
  }

  .todo_table {
    .todo_title {
      font-weight: 400;
      font-size: 14px;
      color: #666666;
      margin-bottom: 12px;
    }
  }
}

.todo_add {
  padding: 0 20px;
}
</style>
