<template>
  <div class="newBox bgcf9 vh-85">
    <div style="padding: 20px 20px 0">
      <el-button type="primary" icon="el-icon-menu" size="small" @click="handleCategory">新闻分类</el-button>
      <el-button type="primary" plain icon="el-icon-plus" size="small" @click="handleAdd">新增新闻</el-button>
    </div>
    <!-- 分类 -->
    <!-- <div class="classify flex">
      <div class="classify-item" :class="{ active: item.value === active }" v-for="item in tabOptions" :key="item.steelTypeId" @click="handleType(item)">
        {{ item.label }}
      </div>
    </div> -->

    <div class="box">
      <el-table v-loading="loading" :key="key" ref="table" stripe :data="list" row-key="id" style="width: 100%" class="custom-table">
        <el-table-column align="center" type="index" label="序号"></el-table-column>
        <el-table-column align="center" prop="typeId" label="分类" show-overflow-tooltip width="100" v-if="!active">
          <template slot-scope="{ row }">{{ formatType(row) }}</template>
        </el-table-column>
        <el-table-column align="center" prop="title" label="标题" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" prop="imgUrl" label="图片" show-overflow-tooltip width="75">
          <template slot-scope="{ row }">
            <el-image referrerpolicy="no-referrer" lazy :src="row.imgUrl" fit="cover" :preview-src-list="[row.imgUrl]">
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline"></i>
              </div>
            </el-image>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="tags" label="标签" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" prop="url" label="内容来源" show-overflow-tooltip>
          <template slot="header" v-if="!!active">
            <el-dropdown trigger="click" @command="handleCommand">
              <el-button size="mini" type="text">
                {{ sourceOptions.find(item => item.value == queryParams.source).label || '全部来源' }}
                <i class="el-icon-arrow-down"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item v-for="(item, index) in sourceOptions" :key="index" :command="item.value">
                  {{ item.label }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
          <template slot-scope="{ row }">
            <a style="text-decoration: none" :href="row.url" target="_blank">
              <span class="table-link">{{ row.url }}</span>
            </a>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="publishTime" label="发布日期" show-overflow-tooltip width="150">
          <template slot-scope="{ row }">{{ row.publishTime ? row.publishTime : row.createTime }}</template>
        </el-table-column>
        <el-table-column align="center" prop="used" label="录入状态" v-if="!!active">
          <template slot-scope="{ row }">{{ row.used ? '已录入' : '未录入' }}</template>
        </el-table-column>
        <el-table-column align="center" label="操作">
          <template slot-scope="{ row }">
            <template v-if="active">
              <button type="button" class="table-btn primary" @click="handleView(row)">
                <i class="el-icon-view"></i>
                预览
              </button>
              <button type="button" class="table-btn success" @click="handleAdd(row)">
                <i class="el-icon-edit"></i>
                {{ row.used ? '已录入' : '未录入' }}
              </button>
            </template>
            <template v-else>
              <button type="button" class="table-btn small primary" @click="handleUpdate(row)">
                <i class="el-icon-edit"></i>
                修改
              </button>
              <button type="button" class="table-btn small danger" @click="handleDelete(row)">
                <i class="el-icon-delete"></i>
                删除
              </button>
              <button type="button" class="table-btn small success" :class="{ disabled: row.isPush }" :disabled="row.isPush" @click="handleSend(row)">
                <i class="el-icon-position"></i>
                推送
              </button>
              <el-badge is-dot :hidden="!row.comment">
                <button type="button" class="table-btn small orange" @click="handleComment(row)">
                  <i class="el-icon-chat-line-round"></i>
                  评论
                </button>
              </el-badge>
            </template>
          </template>
        </el-table-column>
      </el-table>
      <div class="custom-pagination">
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
      </div>
    </div>

    <!--  预览  -->
    <el-dialog v-dialogDragBox :visible.sync="viewOpen" :width="isPC ? '1150px' : '375px'">
      <el-row :gutter="10">
        <el-col :span="1.5">
          <el-button type="primary" icon="el-icon-s-platform" :plain="!isPC" @click="isPC = true" size="small">PC模式</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="primary" icon="el-icon-mobile-phone" :plain="isPC" @click="isPC = false" size="small">WAP模式</el-button>
        </el-col>
      </el-row>
      <h2>{{ viewInfo.title }}</h2>
      <p>发布日期：{{ viewInfo.publishTime }}</p>
      <div class="ql-container ql-snow">
        <div class="ql-editor" v-html="viewInfo.content" />
      </div>
    </el-dialog>

    <!--  新增/修改  -->
    <create-dialog ref="create" />

    <!--  新闻分类  -->
    <category-dialog ref="category" />
    <!--  评论  -->
    <el-dialog v-dialogDragBox :visible.sync="commentOpen" title="评论列表" width="1150px" class="custom-dialog">
      <div class="commenBox">
        <div class="evaluate-con-item" v-for="item in commentList" :key="item.id">
          <div class="evaluate-con-w100">
            <div class="evaluate-con-item-con">
              <div class="evaluate-con-item-con-title">评论人：{{ item.nickName }}</div>
              <div class="evaluate-con-item-con-con" v-html="formatContent(item.name)"></div>
              <div class="evaluate-con-item-con-time">
                <span>评论时间：{{ item.createTime }}</span>
                <div class="anwser" @click="handleDeleteComment(item)">
                  <i class="el-icon-delete"></i>
                  <span>删除</span>
                </div>
              </div>
              <div class="evaluate-con-item-con-open" @click="handleShowMore(item)" v-if="item.hasOwnProperty('children') && !!item.children">——展开所有评论</div>
            </div>
            <el-collapse-transition>
              <div v-if="showId === item.id">
                <template v-if="item.hasOwnProperty('children') && !!item.children">
                  <div class="evaluate-con-item children" v-for="ite in item.children" :key="ite.id">
                    <div class="evaluate-con-w100">
                      <div class="evaluate-con-item-con">
                        <div class="evaluate-con-item-con-title">评论人：{{ ite.nickName }}</div>
                        <div class="evaluate-con-item-con-con" v-html="formatContent(ite.name)"></div>
                        <div class="evaluate-con-item-con-time">
                          <span>评论时间：{{ ite.createTime }}</span>
                          <div class="anwser" @click="handleDeleteComment(ite)">
                            <i class="el-icon-delete"></i>
                            <span>删除</span>
                          </div>
                        </div>
                      </div>
                      <template v-if="ite.hasOwnProperty('children') && !!ite.children">
                        <div class="evaluate-con-item children" v-for="itt in ite.children" :key="itt.id">
                          <div class="evaluate-con-w100">
                            <div class="evaluate-con-item-con">
                              <div class="evaluate-con-item-con-title">评论人：{{ itt.nickName }}</div>
                              <div class="evaluate-con-item-con-con" v-html="formatContent(itt.name)"></div>
                              <div class="evaluate-con-item-con-time">
                                <span>评论时间：{{ itt.createTime }}</span>
                                <div class="anwser" @click="handleDeleteComment(itt)">
                                  <i class="el-icon-delete"></i>
                                  <span>删除</span>
                                </div>
                              </div>
                            </div>
                            <template v-if="itt.hasOwnProperty('children') && !!itt.children">
                              <div class="evaluate-con-item children" v-for="it in itt.children" :key="it.id">
                                <div class="evaluate-con-w100">
                                  <div class="evaluate-con-item-con">
                                    <div class="evaluate-con-item-con-title">评论人：{{ it.nickName }}</div>
                                    <div class="evaluate-con-item-con-con" v-html="formatContent(it.name)"></div>
                                    <div class="evaluate-con-item-con-time">
                                      <span>评论时间：{{ it.createTime }}</span>
                                      <div class="anwser" @click="handleDeleteComment(it)">
                                        <i class="el-icon-delete"></i>
                                        <span>删除</span>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </template>
                          </div>
                        </div>
                      </template>
                    </div>
                  </div>
                </template>
              </div>
            </el-collapse-transition>
          </div>
        </div>
      </div>
      <div slot="footer">
        <button type="button" class="custom-dialog-btn primary" @click="commentOpen = false">关闭</button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { deleteArticle, getArticleList, getArticleOuterList, getArticleTypeList, pushArticle, getArticleCommentList, deleteArticleComment, updateArticlePush } from '@/api/article'
import createDialog from './create'
import categoryDialog from './category'
import { formatContent, removeHtmlTag } from '@/utils'
import emoji from '@/components/emoji/index.vue'

export default {
  name: 'ArticleIndex',
  components: { emoji, createDialog, categoryDialog },
  data() {
    return {
      key: 1,
      loading: true,
      list: [],
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        source: undefined
      },
      active: 0,
      tabOptions: [
        { label: '新闻列表', value: 0 },
        { label: '外部列表', value: 1 }
      ],
      typeOptions: [],
      viewOpen: false,
      viewInfo: {},
      isPC: true,
      sourceOptions: [
        { label: '全部来源', value: undefined },
        { label: '我的钢铁网', value: '我的钢铁网' },
        { label: '西本钢铁网', value: '西本钢铁网' },
        { label: '华人螺丝网', value: '华人螺丝网' }
      ],
      // 评论
      commentList: [],
      commentOpen: false,
      showId: undefined
    }
  },
  created() {
    this.getTypeList()
    this.getList()
    // updateArticlePush({ newsId:2488 }).then(res => {
    //   console.log('res', res)
    // })
  },
  computed: {
    // 默认头像
    defaultAvatar() {
      return require('@/assets/images/Avatar.png')
    }
  },
  methods: {
    formatContent,
    removeHtmlTag,
    // 查询分类
    async getTypeList() {
      const res = await getArticleTypeList()
      const { code, msg, rows } = res
      if (code === 200) {
        this.typeOptions = rows
      } else this.$message.error(msg)
    },
    // 格式化分类
    formatType(row) {
      const data = this.typeOptions.find(item => item.id === row.typeId)
      return data ? data.name : ''
    },
    // 切换分类
    handleType(item) {
      this.queryParams.pageNum = 1
      this.queryParams.source = undefined
      this.active = item.value
      this.getList()
    },
    // 获取列表
    async getList() {
      this.loading = true
      let res
      if (this.active === 0) res = await getArticleList(this.queryParams)
      else res = await getArticleOuterList(this.queryParams)
      const { code, msg, rows, total } = res
      if (code === 200) {
        this.list = rows
        if (this.active === 0) this.getListComment()
        this.total = total
        this.loading = false
        this.key = Math.random()
      } else this.$message.error(msg)
    },
    // 获取评论
    getListComment() {
      const list = [...this.list]
      list.forEach(async item => {
        const res = await getArticleCommentList({ newsId: item.id })
        const { code, msg, data } = res
        if (code === 200) {
          this.$set(item, 'comment', !!data)
        } else this.$message.error(msg)
      })
    },
    handleCommand(val) {
      this.queryParams.source = val
      this.getList()
    },
    async refresh() {
      let res
      if (this.active === 0) res = await getArticleList(this.queryParams)
      else res = await getArticleOuterList(this.queryParams)
      const { code, msg, rows, total } = res
      if (code === 200) this.$set(this, 'list', rows)
      else this.$message.error(msg)
    },
    // 新增
    handleAdd(data = {}) {
      this.$refs.create.handleAdd(data)
    },
    // 修改
    handleUpdate(row) {
      this.$refs.create.handleUpdate(row)
    },
    // 删除
    // prettier-ignore
    handleDelete(row) {
      const newsId = row.id
      this.$confirm('是否删除此新闻?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteArticle({ newsId }).then(res => {
          if (res.code === 200) {
            this.$message.success('删除成功')
            this.getList()
          } else {
            this.$message.error(res.msg)
          }
        })
      }).catch(() => {
      })
    },
    // 预览
    handleView(row) {
      this.viewInfo = { ...row }
      this.isPC = true
      this.viewOpen = true
    },
    // 分类
    handleCategory() {
      this.$refs.category.getList()
    },
    // 发送消息通知
    handleSend(row) {
      const info = row.summary !== '无' ? this.removeHtmlTag(row.summary, 110) : this.removeHtmlTag(row.content, 110)
      const image = row.imgUrl !== '无' ? row.imgUrl : row.content.match(/<img.*?(?:>|\/>)/i) ? row.content.match(/<img.*?(?:>|\/>)/i)[0].match(/src=[\'\"]?([^\'\"]*)[\'\"]?/i)[1] : this.defaultAvatar
      const payload = {
        doType: 'article',
        articleId: row.id,
        time: new Date().getTime(),
        title: row.title,
        info,
        image,
        readable: false
      }
      const title = row.title
      const data = { title, info, payload: JSON.stringify(payload), push: true }
      pushArticle(data).then(res => {
        if (res.code === 200) {
          this.$message.success('发送成功')
          this.handleUpdatePush(row)
        } else this.$message.error(res.msg)
      })
    },
    // 更新推送状态
    handleUpdatePush(row) {
      const newsId = row.id
      updateArticlePush({ newsId }).then(res => {
        if (res.code === 200) this.refresh()
        else this.$message.error(res.msg)
      })
    },
    // 评论
    handleComment(row) {
      if (!row.comment) return
      const newsId = row.id
      getArticleCommentList({ newsId }).then(res => {
        if (res.code === 200) {
          function formatData(data) {
            data.forEach(item => {
              item.createTime = this.$moment(item.createTime).format('YYYY-MM-DD HH:mm:ss')
              if (item.children) formatData(item.children)
            })
          }
          this.commentList = res.data
          this.commentOpen = true
        } else this.$message.error(res.msg)
      })
    },
    // 展开评论
    handleShowMore(item) {
      this.showId = this.showId === item.id ? undefined : item.id
    },
    // 删除评论
    // prettier-ignore
    handleDeleteComment(row) {
      const commentId = row.id
      this.$confirm('是否删除此评论?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteArticleComment({ commentId }).then(res => {
          if (res.code === 200) {
            this.$message.success('删除成功')
            if (!this.commentList.length) {
              this.getListComment()
              this.commentOpen = false
            } else {
              // 刷新评论列表及回复的评论children
              function findAndDelete(list) {
                list.forEach((item, index) => {
                  if (item.id === commentId) {
                    list.splice(index, 1)
                    return
                  }
                  if (item.children) findAndDelete(item.children)
                })
              }
              findAndDelete(this.commentList)
            }
          } else {
            this.$message.error(res.msg)
          }
        })
      }).catch(() => {
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
.box {
  padding: 20px;
}
.commenBox {
  padding-right: 15px;
  margin-top: -20px;
}
.evaluate-con-w100 {
  width: 100%;
}
.evaluate-con-item {
  width: 100%;
  display: flex;
  align-items: flex-start;
  position: relative;
  padding: 15px 0 15px 15px;
  border-bottom: 1px solid #eeeeee;
  .evaluate-con-item {
    border-bottom: 0;
  }
  &.children {
    background-color: #f1f1f3;
    border-radius: 8px;
    margin-bottom: 10px;
  }
  &-img {
    width: 50px;
    height: 50px;
    margin-right: 15px;
    border-radius: 50%;
    border: 1px solid #c5cad9;
  }
  &-con {
    width: 100%;
    display: flex;
    flex-direction: column;
    padding-right: 15px;
    margin-bottom: 10px;
    &-title {
      line-height: 50px;
      font-size: 16px;
      color: #333333;
    }
    &-con {
      display: table-cell;
      vertical-align: middle;
      font-size: 14px;
      line-height: 1.75em;
      color: #666666;
      padding-bottom: 10px;
      &:before {
        content: '评论内容：';
        display: inline-block;
        color: #333333;
      }
    }
    &-time {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 12px;
      color: #999999;
      .anwser {
        margin-left: 10px;
        cursor: pointer;
        span {
          margin-left: 5px;
        }
        &:hover {
          color: #2e73f3;
        }
      }
    }
    &-open {
      font-size: 14px;
      line-height: 40px;
      color: #666666;
      cursor: pointer;
      &:hover {
        color: #2e73f3;
      }
    }
  }
}
</style>
