<template>
  <el-dialog v-dialogDragBox :title="title" :visible.sync="open" width="1150px" class="custom-dialog">
    <div style="padding: 0 20px">
      <el-form ref="form" :model="form" :rules="rules" label-width="5em">
        <el-row :gutter="20" style="display: flex; flex-wrap: wrap">
          <el-col :span="24">
            <el-form-item label="标题" prop="title">
              <el-input v-model="form.title" placeholder="请输入标题" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="置顶时间" prop="topTime">
              <span style="margin-right: 10px" v-if="form.topTimeString">{{ form.topTimeString }}</span>
              <el-input-number v-model="form.topTime" :min="0" :step="1" label="天" size="small"
                @change="handleChangeTop"></el-input-number>
              <span style="margin-left: 10px">天</span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="标签" prop="tags">
              <el-input v-model="form.tags" placeholder="请输入标签" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="分类" prop="typeId">
              <el-select v-model="form.typeId" placeholder="请选择分类" style="width: 100%">
                <el-option v-for="item in typeOptions" :key="item.id" :label="item.name" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="内容" prop="content">
              <editor v-model="form.content" :min-height="300" v-if="isAdd && !form.id" />
              <vue-ueditor-wrap class="editor" v-model="form.content" :config="editorConfig" v-else></vue-ueditor-wrap>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="免责声明" prop="disclaimers">
              <el-input type="textarea" placeholder="请输入免责声明" v-model="form.disclaimers" resize="none"
                :autosize="{ minRows: 4, maxRows: 6 }"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div slot="footer">
      <button type="button" class="custom-dialog-btn" @click="handleCancel">取消</button>
      <button type="button" class="custom-dialog-btn primary" @click="handleSumit">立即发布</button>
    </div>
  </el-dialog>
</template>
<script>
import { addArticle, updateArticle, getArticleTypeList } from '@/api/article'
import VueUeditorWrap from 'vue-ueditor-wrap'
import { parseTime } from '../../utils/ruoyi'
import { getToken } from "@/utils/auth";

export default {
  name: 'ArticleCreate',
  components: { VueUeditorWrap },
  data() {
    return {
      title: '',
      open: false,
      form: {},
      rules: {
        title: [{ required: true, message: '请输入标题', trigger: 'blur' }],
        typeId: [{ required: true, message: '请选择分类', trigger: 'change' }],
        content: [{ required: true, message: '请输入内容', trigger: 'blur' }]
      },
      typeOptions: [],
      isAdd: true,
      editorConfig: {
        initialFrameHeight: 250,
        UEDITOR_HOME_URL: '/UEditor/',
        serverUrl: process.env.VUE_APP_BASE_API + '/common/upload',
        toolbars: [['fullscreen', 'source', '|', 'undo', 'redo', '|', 'bold', 'italic', 'underline', 'fontborder', 'strikethrough', 'superscript', 'subscript', 'removeformat', 'formatmatch', 'autotypeset', 'blockquote', 'pasteplain', '|', 'forecolor', 'backcolor', 'insertorderedlist', 'insertunorderedlist', 'selectall', 'cleardoc', '|', 'rowspacingtop', 'rowspacingbottom', 'lineheight', '|', 'customstyle', 'paragraph', 'fontfamily', 'fontsize', '|', 'directionalityltr', 'directionalityrtl', 'indent', '|', 'justifyleft', 'justifycenter', 'justifyright', 'justifyjustify', '|', 'touppercase', 'tolowercase', '|', 'link', 'unlink', 'anchor', '|', 'imagenone', 'imageleft', 'imageright', 'imagecenter', '|', 'horizontal', 'date', 'time', 'spechars', 'snapscreen', 'wordimage', '|', 'inserttable', 'deletetable', 'insertparagraphbeforetable', 'insertrow', 'deleterow', 'insertcol', 'deletecol', 'mergecells', 'mergeright', 'mergedown', 'splittocells', 'splittorows', 'splittocols', 'charts', '|', 'preview', 'searchreplace', 'drafts']]
      }
    }
  },
  created() {
    this.getTypeList()
  },
  methods: {
    parseTime,
    // 查询分类
    async getTypeList() {
      const res = await getArticleTypeList()
      const { code, msg, rows } = res
      if (code === 200) {
        rows.sort((a, b) => a.sort - b.sort || a.id - b.id)
        this.typeOptions = rows
      } else this.$message.error(msg)
    },
    // 重置
    reset() {
      this.form = {
        content: undefined,
        id: undefined,
        imgUrl: undefined,
        publishTime: undefined,
        source: undefined,
        summary: undefined,
        tags: undefined,
        title: undefined,
        typeId: undefined,
        url: undefined,
        topTime: undefined,
        disclaimers: undefined
      }
      this.resetForm('form')
    },
    // 新增
    handleAdd(data = {}) {
      if (!getToken()) {
        this.$message.error('请先登录')
        return false
      }
      this.reset()
      this.isAdd = true
      this.title = '新增新闻'
      this.form = { ...data, ...{ topTime: 5 } }
      this.form.disclaimers = '自由客紧固件（ziyouke.net）发布的原创及转载内容，仅供客户参考，不作为决策建议。原创内容版权归自由客紧固件（ziyouke.net）所有，转载需取得自由客紧固件（ziyouke.net）书面授权，且自由客紧固件（ziyouke.net）保留对任何侵权行为和有悖原创内容原意的引用行为进行追究的权利。转载内容来源于网络，目的在于传递更多信息，方便学习与交流，并不代表自由客紧固件（ziyouke.net）赞同其观点及对其真实性、完整性负责。申请授权及投诉，请联系自由客紧固件（ziyouke.net）（15188838176）处理。'
      this.form.topTimeString = parseTime(new Date().getTime() + 5 * 24 * 60 * 60 * 1000)
      this.open = true
    },
    // 修改
    handleUpdate(data) {
      if (!getToken()) {
        this.$message.error('请先登录')
        return false
      }
      this.reset()
      this.isAdd = false
      this.title = '修改新闻'
      this.form = { ...data }
      let { topTime } = this.form
      this.form.topTimeString = topTime ? parseTime(topTime) : ''
      this.form.disclaimers = this.form.disclaimers || '自由客紧固件（ziyouke.net）发布的原创及转载内容，仅供客户参考，不作为决策建议。原创内容版权归自由客紧固件（ziyouke.net）所有，转载需取得自由客紧固件（ziyouke.net）书面授权，且自由客紧固件（ziyouke.net）保留对任何侵权行为和有悖原创内容原意的引用行为进行追究的权利。转载内容来源于网络，目的在于传递更多信息，方便学习与交流，并不代表自由客紧固件（ziyouke.net）赞同其观点及对其真实性、完整性负责。申请授权及投诉，请联系自由客紧固件（ziyouke.net）（15188838176）处理。'
      this.form.topTime = topTime > new Date().getTime() ? parseFloat(((topTime - new Date().getTime()) / 1000 / 60 / 60 / 24).toFixed(2)) : 0
      this.open = true
    },
    // 取消
    handleCancel() {
      this.open = false
      this.reset()
    },
    handleChangeTop(val) {
      this.form.topTimeString = val ? parseTime(new Date().getTime() + val * 24 * 60 * 60 * 1000) : ''
    },
    // 提交
    handleSumit() {
      // 当前时间加两天
      const date = new Date()
      date.setDate(date.getDate() + this.form.topTime)
      this.form.topTime = date.getTime()
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (!this.isAdd) {
            updateArticle(this.form).then(res => {
              if (res.code === 200) {
                const msg = '修改成功'
                this.$message.success(msg)
                this.open = false
                this.$parent.getList()
              } else {
                this.$message.error(res.msg)
              }
            })
          } else {
            if (!this.form.summary) this.form.summary = this.form.content.replace(/<[^>]+>/g, '').substring(0, 100)
            this.form.outerId = this.form.id
            if (!this.form.outerId) this.form.outerId = -1
            addArticle(this.form).then(res => {
              if (res.code === 200) {
                const msg = '新增成功'
                this.$message.success(msg)
                this.open = false
                this.$parent.refresh()
              } else {
                this.$message.error(res.msg)
              }
            })
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';

.box {
  padding: 20px;
}

::v-deep.editor {
  line-height: 1.5em;
}

::v-deep .el-textarea__inner {
  font-family: initial;
}
</style>
