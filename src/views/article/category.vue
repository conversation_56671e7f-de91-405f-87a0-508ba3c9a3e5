<template>
  <div>
    <el-dialog v-dialogDragBox title="新闻分类" :visible.sync="open" width="1150px" class="custom-dialog" :before-close="handleClose">
      <div style="padding: 0 20px">
        <el-button type="primary" size="small" icon="el-icon-plus" @click="handleAdd">添加类别</el-button>

        <el-table ref="table" stripe :data="list" row-key="ids" style="width: 100%; margin-top: 20px" class="custom-table">
          <el-table-column align="center" type="index" label="序号"></el-table-column>
          <el-table-column align="center" prop="name" label="类别名称" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="sort" label="排序" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" label="操作" width="220px">
            <template slot-scope="{ row }">
              <button type="button" class="table-btn primary" @click="handleUpdate(row)">
                <i class="el-icon-edit"></i>
                修改
              </button>
              <button type="button" class="table-btn danger" @click="handleDelete(row)">
                <i class="el-icon-delete"></i>
                删除
              </button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div slot="footer">
        <button type="button" class="custom-dialog-btn primary" @click="handleClose">关闭</button>
      </div>
    </el-dialog>

    <el-dialog v-dialogDragBox :title="title" :visible.sync="formOpen" width="700px" class="custom-dialog">
      <div style="padding: 0 20px">
        <el-form ref="form" :model="form" :rules="rules" label-width="6em">
          <el-form-item label="类别名称" prop="name">
            <el-input v-model="form.name" placeholder="请输入类别名称" />
          </el-form-item>
          <el-form-item label="排序" prop="sort">
            <el-input-number v-model="form.sort" :min="1" size="small"></el-input-number>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer">
        <button type="button" class="custom-dialog-btn" @click="formOpen = false">取消</button>
        <button type="button" class="custom-dialog-btn primary" @click="handleSumit">提交</button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { addArticleType, deleteArticleType, getArticleTypeList, updateArticleType } from '@/api/article'

export default {
  name: 'ArticleType',
  data() {
    return {
      open: false,
      list: [],
      formOpen: false,
      form: {},
      rules: {
        name: [{ required: true, message: '请输入类别名称', trigger: 'blur' }],
      },
      title: ''
    }
  },
  methods: {
    getList() {
      this.open = true
      getArticleTypeList().then(res => {
        res.rows.sort((a, b) => a.sort - b.sort || a.id - b.id)
        this.list = res.rows
      })
    },
    handleClose() {
      this.open = false
      this.$parent.getList()
    },
    reset() {
      this.form = {
        name: undefined,
        sort: 1,
        id: undefined
      }
      this.resetForm('form')
    },
    handleAdd() {
      this.reset()
      this.title = '添加类别'
      this.formOpen = true
    },
    handleUpdate(row) {
      this.reset()
      this.title = '修改类别'
      this.form = { ...row }
      this.formOpen = true
    },
    // 提交
    handleSumit() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateArticleType(this.form).then(res => {
              if (res.code === 200) {
                this.$message.success('修改成功')
                this.formOpen = false
                this.getList()
              } else {
                this.$message.error(res.msg)
              }
            })
          } else {
            addArticleType(this.form).then(res => {
              if (res.code === 200) {
                this.$message.success('新增成功')
                this.formOpen = false
                this.getList()
              } else {
                this.$message.error(res.msg)
              }
            })
          }
        }
      })
    },
    // prettier-ignore
    handleDelete(row) {
      const newsTypeId = row.id
      this.$confirm('是否删除此新闻分类?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteArticleType({ newsTypeId }).then(res => {
          if (res.code === 200) {
            this.$message.success('删除成功')
            this.getList()
          } else {
            this.$message.error(res.msg)
          }
        })
      }).catch(() => {
      })
    }
  }
}
</script>

<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';
</style>
