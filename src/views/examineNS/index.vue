<template>
    <div class="newBox bgcf9 vh-85">
        <!-- 搜索 -->
        <div>
            <div class="custom-search">
                <div class="custom-search-form flex">
                    <input type="text" v-model="queryParams.productName" placeholder="请输入产品名称" class="custom-search-input"
                        @keyup.enter="handleQuery" style="margin-right: 30px; border-radius: 5px;" />
                    <input type="text" v-model="queryParams.companyName" placeholder="请输入公司名称" class="custom-search-input"
                        @keyup.enter="handleQuery" style="margin-right: 30px; border-radius: 5px;" />
                    <button type="button" class="custom-search-button pointer" @click="handleQuery"
                        style="border-radius: 5px;">
                        <i class="el-icon-search"></i>
                        搜索
                    </button>
                </div>
            </div>
            <div class="custom_tabs">
                <el-tabs v-model="activeTabs" @tab-click="handleTabs" style="padding-top: 12px 0;">
                    <el-tab-pane label="全部" :name="'all'"></el-tab-pane>
                    <el-tab-pane label="已通过" :name="'pass'"></el-tab-pane>
                    <el-tab-pane label="未通过" :name="'refuse'"></el-tab-pane>
                    <el-tab-pane :name="'audit'">
                        <div slot="label">
                            <div class="flex" style="align-items: center;">
                                <span>待审核</span>
                                <div class="remind_box" v-if="auditList.length > 0">
                                    <img src="@/assets/images/remind.png" alt="">
                                    <span>您有未审核的国标申请</span>
                                </div>
                            </div>
                        </div>
                    </el-tab-pane>
                </el-tabs>
            </div>
        </div>
        <!-- 列表 -->
        <div class="examineNS_box" v-if="checkPermi(['system:verify:optimal'])">
            <div class="examineNS_list">
                <!-- <div class="examineNS_list_top">
                    <el-checkbox v-model="checkedAllExamineNS" @change="selectAllExamineNS">全选</el-checkbox>
                    <button type="button" class="examineNS_list_top_btn pointer">通过</button>
                </div> -->
                <div class="examineNS_list_concent">
                    <el-table :data="tableData" tooltip-effect="dark" style="width: 100%"
                        header-row-class-name="examineNS_list_header" row-class-name="examineNS_list_row">
                        <!-- <el-table ref="multipleTable" :data="tableData" tooltip-effect="dark" style="width: 100%"
                        @selection-change="handleSelectionChange" header-row-class-name="examineNS_list_header"
                        row-class-name="examineNS_list_row">
                        <el-table-column type="selection" width="55" :selectable="selectable">
                        </el-table-column> -->
                        <el-table-column label="序号" type="index" width="100" align="center">
                        </el-table-column>
                        <el-table-column label="报价产品" align="center">
                            <template slot-scope="scope">
                                <div @click="handleOptimalName(scope.row)" class="btn pointer" style="font-size: 12px;">共{{
                                    scope.row.count }}个产品</div>
                            </template>
                        </el-table-column>
                        <el-table-column label="申请企业" align="center">
                            <template slot-scope="scope">
                                <div @click="handleCompanyName(scope.row)" class="btn" style="color: #333;">{{ scope.row.companyName }}
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column prop="time" label="申请时间" align="center">
                        </el-table-column>
                        <!-- <el-table-column label="状态" align="center">
                            <template slot-scope="scope">
                                <div v-if="scope.row.status == '1'" style="color: #EC2454; font-weight: 500;">待审核</div>
                                <div v-if="scope.row.status == '2'" style="color: #999999;">已通过</div>
                                <div v-if="scope.row.status == '3'" style="color: #999999;">未通过</div>
                            </template>
                        </el-table-column> -->
                        <!-- <el-table-column label="操作" align="center">
                            <template slot-scope="scope">
                                <div v-if="scope.row.status == '1'">
                                    <button type="button" class="examineNS_list_concent_btn1 pointer"
                                        :class="scope.row.checked == true ? 'active' : ''">通过</button>
                                    <button type="button" class="examineNS_list_concent_btn2 pointer"
                                        :class="scope.row.checked == true ? 'active' : ''">拒绝</button>
                                </div>
                            </template>
                        </el-table-column> -->
                    </el-table>

                    <!-- <div class="custom-pagination">
                        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
                            :limit.sync="queryParams.pageSize" @pagination="getList" />
                    </div> -->
                </div>
            </div>
        </div>

        <!-- 国标列表弹窗查看 -->
        <el-dialog v-dialogDragBox title="国标甄选经营申请" :visible.sync="productListShow" width="1150px" class="custom-dialog">
            <div class="product_box">
                <div class="product_company">
                    <div class="product_company_item flex">
                        <div class="product_company_item_title">申请企业</div>
                        <div class="product_company_item_concent">
                            <span class="name">{{ companyData.company && companyData.company.companyName }}</span>
                            <span class="yes"
                                v-if="companyData.supplier && companyData.supplier.isCertified == 1">认证企业</span>
                        </div>
                    </div>
                    <div class="product_company_item flex">
                        <div class="product_company_item_title">公司地址</div>
                        <div class="product_company_item_concent">
                            <span class="address">{{ companyData.company && companyData.company.address }}</span>
                        </div>
                    </div>
                    <div class="product_company_item flex">
                        <div class="product_company_item_title">联系方式</div>
                        <div class="product_company_item_concent">
                            <div class="inline-flex">
                                <!-- <div class="table-phone pointer">
                                    <i class="ssfont ss-diy-liaotian"></i>
                                    <span>点我聊天</span>
                                </div> -->
                                <div class="table-phone pointer">
                                    <i class="ssfont ss-diy-dianhua"></i>
                                    <span>{{ companyData.company && companyData.company.phone }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="product_list">
                    <div class="product_list_tip">以下是该公司申请经营的国标甄选产品</div>
                    <el-divider></el-divider>
                    <div class="product_list_box">
                        <div class="product_list_box_top" v-if="checkPermi(['system:verify:optimal'])">
                            <el-checkbox v-model="checkedAllProduct" @change="selectAllProduct"
                                :disabled="productData.filter(item => item.status == 0).length == 0">全选</el-checkbox>
                            <button type="button" :disabled="productData.filter(item => item.checked == true).length == 0"
                                class="product_list_box_top_btn pass pointer" @click="verifyItem(1)">通过</button>
                            <button type="button" :disabled="productData.filter(item => item.checked == true).length == 0"
                                class="product_list_box_top_btn refuse pointer" @click="verifyItem(-1)">拒绝</button>
                        </div>
                        <div class="product_list_box_concent">
                            <el-table ref="multipleTable" :data="productData" tooltip-effect="dark" style="width: 100%"
                                @selection-change="handleProductChange" header-row-class-name="product_list_box_header"
                                row-class-name="product_list_box_row">
                                <el-table-column type="selection" width="55" :selectable="selectable" v-if="checkPermi(['system:verify:optimal'])">
                                </el-table-column>
                                <el-table-column label="序号" type="index" width="80" align="center">
                                </el-table-column>
                                <el-table-column label="产品名称" align="center">
                                    <template slot-scope="scope">
                                        <div @click="handleProdictName(scope.row)" class="btn pointer"
                                            style="font-size: 12px;">
                                            {{ scope.row.productName }}
                                        </div>
                                    </template>
                                </el-table-column>
                                <el-table-column label="图片" align="center">
                                    <template slot-scope="scope">
                                        <div class="pointer">
                                            <img :src="formatProductImg(scope.row.product)" :alt="scope.row.productName"
                                                class="img pointer" @click="handleImg(scope.row.product)" />
                                        </div>
                                    </template>
                                </el-table-column>
                                <el-table-column label="规格" align="center">
                                    <template slot-scope="scope">
                                        <div>{{ scope.row.product.specs }}</div>
                                    </template>
                                </el-table-column>
                                <el-table-column label="材质" align="center">
                                    <template slot-scope="scope">
                                        <div>{{ scope.row.product.materialQuality }}</div>
                                    </template>
                                </el-table-column>
                                <el-table-column label="表面处理" align="center">
                                    <template slot-scope="scope">
                                        <div>{{ scope.row.product.surface }}</div>
                                    </template>
                                </el-table-column>
                                <el-table-column label="属性" align="center">
                                    <template slot-scope="scope">
                                        <div>{{ scope.row.product.attribute }}</div>
                                    </template>
                                </el-table-column>
                                <el-table-column label="重量" align="center">
                                    <template slot-scope="scope">
                                        <div>{{ scope.row.product.weight }}</div>
                                    </template>
                                </el-table-column>
                                <el-table-column label="状态" align="center">
                                    <template slot-scope="scope">
                                        <div v-if="scope.row.status == 0" style="color: #EC2454; font-weight: 500;">待审核
                                        </div>
                                        <div v-if="scope.row.status == 1" style="color: #999999;">已通过</div>
                                        <div v-if="scope.row.status == -1" style="color: #999999;">未通过</div>
                                    </template>
                                </el-table-column>
                                <el-table-column label="操作" align="center" width="220" v-if="checkPermi(['system:verify:optimal'])">
                                    <template slot-scope="scope">
                                        <div v-if="scope.row.status == 0">
                                            <button type="button" class="product_list_box_concent_btn1 pointer"
                                                :class="scope.row.checked == true ? 'active' : ''"
                                                @click="verifyItem(1, scope.row)">通过</button>
                                            <button type="button" class="product_list_box_concent_btn2 pointer"
                                                :class="scope.row.checked == true ? 'active' : ''"
                                                @click="verifyItem(-1, scope.row)">拒绝</button>
                                        </div>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </div>
                    </div>
                </div>

            </div>
            <div slot="footer">
                <button type="button" class="product_btn" @click="productListShow = false">取消</button>
                <button type="button" class="product_btn primary" @click="productListShow = false">完成</button>
            </div>
        </el-dialog>

        <!-- 图片预览 -->
        <el-dialog v-dialogDragBox :visible.sync="imgOpen" width="750px" :append-to-body="appendBody">
            <div style="text-align: center" @contextmenu.prevent>
                <img style="max-width: 100%" :src="formatProductImg(productImg)" :alt="productImg.productName" />
            </div>
        </el-dialog>

        <!-- 产品详情 -->
        <product-dialog ref="productInfo"></product-dialog>

    </div>
</template>

<script>
import { listOptimal, getOptimalDetail, optimalVerify } from "@/api/system/examineNS";
import ProductDialog from '@/views/public/product/dialog'
import { supplier } from "@/api/system/company";
import { checkPermi } from '@/utils/permission'
export default {
  name: 'ExamineNS',
    components: { ProductDialog },
    data() {
        return {
            queryParams: {
                companyName: undefined,
                productName: undefined,
                status: undefined
            },
            activeTabs: 'all',
            tableData: [],
            multipleSelection: [],
            // checkedAllExamineNS: false,
            auditList: [],
            total: 3,
            productListShow: false,
            checkedAllProduct: false,
            productData: [],
            companyData: {},
            productImg: {},
            appendBody: false,
            imgOpen: false,
        }
    },
    created() {
        this.getList()
        listOptimal({
            status: 0
        }).then(res => {
            if (res.code == 200) {
                this.auditList = res.data
            }
        })
    },
    methods: {
        checkPermi,
        // 搜索
        handleQuery() {
            this.getList()
        },
        // 全选
        // selectAllExamineNS() {
        //     if (this.checkedAllExamineNS == true) {

        //     }
        // },
        handleTabs() {
            if (this.activeTabs === 'all') {
                this.queryParams.status = undefined
            }
            if (this.activeTabs === 'pass') {
                this.queryParams.status = 1
            }
            if (this.activeTabs === 'refuse') {
                this.queryParams.status = -1
            }
            if (this.activeTabs === 'audit') {
                this.queryParams.status = 0
            }
            this.getList()
        },
        // 列表选择
        // handleSelectionChange(val) {
        //     this.tableData.forEach(el => {
        //         el.checked = false
        //     })
        //     val.forEach(el => {
        //         el.checked = true
        //     });
        //     this.multipleSelection = val;
        //     if (this.multipleSelection.length == this.tableData.length) {
        //         this.checkedAllExamineNS = true
        //     } else {
        //         this.checkedAllExamineNS = false
        //     }
        // },
        // 查看企业
        handleCompanyName(row) {

        },
        selectable(row, index) {
            if (row.status == 0) {
                return true
            } else {
                return false
            }
        },
        // 审核申请列表查询
        getList() {
            listOptimal(this.queryParams).then(res => {
                if (res.code == 200) {
                    this.tableData = res.data
                }
            })
        },
        // 国标产品列表
        async handleOptimalName(row) {
            const company_res = await supplier({ id: row.companyId })
            if (company_res.code == 200) {
                this.companyData = company_res.data
            }
            const optimalDetail_res = await getOptimalDetail({ batchId: row.batchId })
            if (optimalDetail_res.code == 200) {
                this.productData = optimalDetail_res.data
            }
            this.checkedAllProduct = false
            this.productListShow = true
        },
        // 全选
        selectAllProduct() {
            if (this.checkedAllProduct == true) {
                this.productData.forEach(el => {
                    if (el.status === 0) {
                        el.checked = true
                    }
                })
                this.$refs.multipleTable.toggleAllSelection();
            } else if (this.checkedAllProduct == false) {
                this.productData.forEach(el => {
                    if (el.status === 0) {
                        el.checked = false
                    }
                })
                this.$refs.multipleTable.clearSelection();
            }
        },
        // 列表选择
        handleProductChange(val) {
            this.productData.forEach(el => {
                el.checked = false
            })
            val.forEach(el => {
                el.checked = true
            });
            this.multipleSelection = val;
            if (this.multipleSelection.length == this.productData.filter(item => item.status === 0).length) {
                this.checkedAllProduct = true
            } else {
                this.checkedAllProduct = false
            }
        },
        // 点击聊天
        handleContact(row) {

            // const { customer } = row
            // const data = {
            //     userId: customer.userId,
            //     nick: customer.nickName,
            //     avatar: this.imgPath + userInfo.avatar || this.defaultAvatar
            // }
            // this.$refs.chat.send(data)
        },
        // 图片放大
        handleImg(item) {
            this.productImg = item
            this.imgOpen = true
        },
        // 查看详情
        handleProdictName(item, val) {
            this.$refs.productInfo.handleView(item.product, val)
        },
        // 审核
        verifyItem(type, row) {
            let ids = []
            if (row) {
                ids = [row.id]
            } else {
                this.productData.filter(item => item.checked == true).forEach(el => {
                    ids.push(el.id)
                })
            }
            optimalVerify({
                ids: ids,
                status: type
            }).then(res => {
                if (res.code == 200) {
                    this.$message.success('操作成功')
                    getOptimalDetail({ batchId: row ? row.batchId : this.productData.find(item => item.checked == true).batchId }).then(response => {
                        if (response.code == 200) {
                            this.productData = response.data
                        }
                    })
                }
            })
        }
    }
}
</script>

<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';

.custom_tabs {
    height: 45px;
    border-bottom: 1px solid #CBD6E2;
    border-top: 1px solid #CBD6E2;
    padding-left: 20px;
    background: #fff;

    ::v-deep {
        .el-tabs__header {
            margin: 0;
        }

        .el-tabs__nav-scroll,
        .el-tabs__nav {
            height: 45px;
        }

        .el-tabs__item,
        .el-tabs__active-bar {
            width: 116px !important;
            text-align: center;
            padding: 0;
            font-weight: 550;
        }
    }

    .remind_box {
        margin-left: 10px;
        background: url('~@/assets/images/remind_bg.png') no-repeat;
        background-size: 170px 28px;
        background-position: center;
        display: flex;
        align-items: center;
        padding: 4px 12px;

        img {
            width: 20px;
            height: 20px;
        }

        span {
            margin-left: 7px;
            font-weight: 400;
            font-size: 12px;
            color: #F50E0E;
        }
    }
}



.examineNS_box {
    padding: 15px 20px;

    .examineNS_list {
        min-height: 685px;
        background: #fff;
        padding: 15px 20px;

        .examineNS_list_top {
            margin-bottom: 16px;

            ::v-deep {
                .el-checkbox__label {
                    font-weight: 400;
                    font-size: 12px;
                    color: #999999;
                    line-height: 20px
                }
            }

            .examineNS_list_top_btn {
                width: 90px;
                height: 30px;
                background: #2F74F3;
                border-radius: 5px 5px 5px 5px;
                margin-left: 20px;
                font-weight: 500;
                font-size: 12px;
                color: #FFFFFF;
                border: 0;
            }
        }

        .examineNS_list_concent {
            ::v-deep {
                .el-table::before {
                    height: 0;
                }

                .el-table__header-wrapper {
                    .el-table-column--selection {
                        .el-checkbox {
                            display: none;
                        }
                    }
                }

                .examineNS_list_header {
                    background: #F8F9FB;

                    .el-table__cell {
                        background: transparent;
                        height: 43px;
                        padding: 0;
                        font-weight: 400;
                        font-size: 12px;
                        color: #999999;

                        &.is-leaf {
                            border-top: 1px solid #CBD6E2;
                            border-bottom: 1px solid #CBD6E2;
                        }

                        // &:nth-child(1) {
                        //     border: none;
                        //     background: #fff;
                        // }

                        &:nth-child(1) {
                            border-top-left-radius: 6px;
                            border-left: 1px solid #CBD6E2;
                        }

                        &:nth-child(4) {
                            border-right: 1px solid #CBD6E2;
                            border-top-right-radius: 6px;
                        }

                    }
                }

                .examineNS_list_row {
                    background: #fff;

                    &:nth-child(2n) {
                        background: #F0F3F9;
                    }

                    &:last-child {
                        .el-table__cell {
                            border-bottom: 1px solid #CBD6E2;

                            &:nth-child(1) {
                                border-bottom-left-radius: 6px;
                            }

                            &:nth-child(4) {
                                border-bottom-right-radius: 6px;
                            }
                        }
                    }

                    .el-table__cell {
                        background: transparent;
                        font-weight: 400;
                        font-size: 12px;
                        color: #333333;

                        &.is-leaf {
                            border-top: 1px solid #CBD6E2;
                            border-bottom: 1px solid #CBD6E2;
                        }

                        // &:nth-child(1) {
                        //     border: none;
                        //     background: #fff;
                        // }

                        &:nth-child(1) {
                            border-left: 1px solid #CBD6E2;
                        }

                        &:nth-child(4) {
                            border-right: 1px solid #CBD6E2;
                        }

                    }
                }

            }

            .examineNS_list_concent_btn1 {
                width: 90px;
                height: 30px;
                border-radius: 5px 5px 5px 5px;
                border: 1px solid #2E73F3;
                background-color: #fff;
                font-weight: 500;
                font-size: 12px;
                color: #2E73F3;
                margin-right: 10px;

                &.active {
                    background: #2E73F3;
                    color: #FFFFFF;
                }
            }

            .examineNS_list_concent_btn2 {
                width: 90px;
                height: 30px;
                border-radius: 5px 5px 5px 5px;
                border: 1px solid #F43F3F;
                background-color: #fff;
                font-weight: 500;
                font-size: 12px;
                color: #F43F3F;

                &.active {
                    background: #F43F3F;
                    color: #FFFFFF;
                }
            }

            .btn {
                font-weight: 550;
                font-size: 14px;
                color: #2F74F3;
            }

        }
    }
}

.product_box {
    padding: 0 20px;

    .product_company {
        height: 186px;
        background: #F8F9FB;
        border-radius: 5px 5px 5px 5px;
        border: 1px solid #D1DFFA;

        .product_company_item {
            align-items: center;

            .product_company_item_title {
                width: 108px;
                height: 61px;
                background: #F8F9FB;
                border-right: 1px solid #CBD6E2;
                border-bottom: 1px solid #CBD6E2;
                font-weight: 400;
                font-size: 12px;
                color: #666666;
                line-height: 61px;
                text-align: center;
            }

            .product_company_item_concent {
                flex: 1;
                display: flex;
                align-items: center;
                padding-left: 60px;

                .name {
                    cursor: pointer;
                    font-weight: 500;
                    font-size: 12px;
                    // color: #2E73F3;
                    color: #666666;
                    line-height: 61px;
                    margin-right: 10px;
                }

                .yes {
                    width: 98px;
                    height: 26px;
                    line-height: 26px;
                    font-size: 14px;
                    color: $white;
                    background-size: 100% 100%;
                    padding-left: 30px;
                    cursor: pointer;
                    background: url('~@/assets/images/authentication_y.png') no-repeat;
                    background-size: 100% 100%;
                }

                .address {
                    font-weight: 500;
                    font-size: 14px;
                    color: #666666;
                    line-height: 61px;
                }

                .table-phone {
                    display: inline-flex;
                    flex-wrap: nowrap;
                    align-items: center;
                    justify-content: center;
                    width: 30px;
                    height: 30px;
                    border-radius: 30px;
                    background-color: #d7e5ff;
                    color: #2e73f3;

                    span {
                        display: none;
                    }

                    &:hover {
                        width: auto;
                        padding: 0 10px;
                        background-color: #2e73f3;
                        color: #ffffff;

                        span {
                            display: inline-block;
                        }
                    }
                }

                .table-phone+.table-phone {
                    margin-left: 10px;
                }
            }

            &:nth-child(2) {
                .product_company_item_concent {
                    background: #FFFFFF;
                }
            }

            &:nth-child(3) {
                .product_company_item_title {
                    border-bottom: none;
                }
            }
        }
    }

    .product_list {
        margin-top: 18px;

        .product_list_tip {
            font-weight: 500;
            font-size: 14px;
            color: #666666;
            line-height: 20px;
        }

        .el-divider--horizontal {
            margin-top: 7px;
            margin-bottom: 18px;
        }

        .product_list_box {

            .product_list_box_top {
                margin-bottom: 16px;

                ::v-deep {
                    .el-checkbox__label {
                        font-weight: 400;
                        font-size: 12px;
                        color: #999999;
                        line-height: 20px
                    }
                }

                .product_list_box_top_btn {
                    width: 90px;
                    height: 30px;
                    border-radius: 5px 5px 5px 5px;
                    margin-left: 20px;
                    font-weight: 500;
                    font-size: 12px;

                    &.pass {
                        border: 0;
                        background: #2F74F3;
                        color: #FFFFFF;
                    }

                    &.refuse {
                        border: 1px solid #F43F3F;
                        background: #FFFFFF;
                        color: #F43F3F;
                    }
                }
            }

            .product_list_box_concent {
                ::v-deep {
                    .el-table::before {
                        height: 0;
                    }

                    .el-table__header-wrapper {
                        .el-table-column--selection {
                            .el-checkbox {
                                display: none;
                            }
                        }
                    }

                    .product_list_box_header {
                        background: #F8F9FB;

                        .el-table__cell {
                            background: transparent;
                            height: 43px;
                            padding: 0;
                            font-weight: 400;
                            font-size: 12px;
                            color: #999999;

                            &.is-leaf {
                                border-top: 1px solid #CBD6E2;
                                border-bottom: 1px solid #CBD6E2;
                            }

                            &:nth-child(1) {
                                border: none;
                                background: #fff;
                            }

                            &:nth-child(2) {
                                border-top-left-radius: 6px;
                                border-left: 1px solid #CBD6E2;
                            }

                            &:nth-child(11) {
                                border-right: 1px solid #CBD6E2;
                                border-top-right-radius: 6px;
                            }

                        }
                    }

                    .product_list_box_row {
                        background: #fff;

                        &:nth-child(2n) {
                            background: #F0F3F9;
                        }

                        &:last-child {
                            .el-table__cell {
                                border-bottom: 1px solid #CBD6E2;

                                &:nth-child(2) {
                                    border-bottom-left-radius: 6px;
                                }

                                &:nth-child(11) {
                                    border-bottom-right-radius: 6px;
                                }
                            }
                        }

                        .el-table__cell {
                            background: transparent;
                            font-weight: 400;
                            font-size: 12px;
                            color: #333333;

                            &.is-leaf {
                                border-top: 1px solid #CBD6E2;
                                border-bottom: 1px solid #CBD6E2;
                            }

                            &:nth-child(1) {
                                border: none;
                                background: #fff;
                            }

                            &:nth-child(2) {
                                border-left: 1px solid #CBD6E2;
                            }

                            &:nth-child(11) {
                                border-right: 1px solid #CBD6E2;
                            }

                        }
                    }

                }

                .product_list_box_concent_btn1 {
                    width: 90px;
                    height: 30px;
                    border-radius: 5px 5px 5px 5px;
                    border: 1px solid #2E73F3;
                    background-color: #fff;
                    font-weight: 500;
                    font-size: 12px;
                    color: #2E73F3;
                    margin-right: 10px;

                    &.active {
                        background: #2E73F3;
                        color: #FFFFFF;
                    }
                }

                .product_list_box_concent_btn2 {
                    width: 90px;
                    height: 30px;
                    border-radius: 5px 5px 5px 5px;
                    border: 1px solid #F43F3F;
                    background-color: #fff;
                    font-weight: 500;
                    font-size: 12px;
                    color: #F43F3F;

                    &.active {
                        background: #F43F3F;
                        color: #FFFFFF;
                    }
                }

                .btn {
                    font-weight: 550;
                    font-size: 12px;
                    color: #2F74F3;
                }

                .img {
                    width: 52px;
                    height: 52px;
                }

            }
        }
    }
}

.product_btn {
    cursor: pointer;
    width: 269px;
    height: 50px;
    border-radius: 5px 5px 5px 5px;
    border: 1px solid #CBD6E2;
    background: #fff;
    color: #999999;
    border-radius: 5px 5px 5px 5px;
    font-size: 16px;
    margin-bottom: 15px;

    &.primary {
        background: #2E73F3;
        color: #FFFFFF;
        margin-left: 10px;
    }
}
</style>
