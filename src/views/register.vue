<template>
  <div>
    <div class="register">
      <div class="register-box">
        <div class="register-login">
          <router-link class="link-type" :to="'/login'">已有账号？去登录</router-link>
        </div>
        <div class="register-class">
          <div class="class-item" :class="{ active: componentTpl === 'companyTpl' }" @click="handleChange('companyTpl')">企业账号注册</div>
          <div class="class-item" :class="{ active: componentTpl === 'personalTpl' }" @click="handleChange('personalTpl')">个人账号注册</div>
          <div class="class-item-tip" v-if="componentTpl === 'personalTpl'">个人账号不支持产品报价，如有报价需求请注册企业账号，畅享更多优质服务</div>
        </div>
        <component :is="componentTpl"></component>
      </div>
    </div>
    <Footer ref="footer" />
  </div>
</template>

<script>
import companyTpl from './register/company'
import personalTpl from './register/personal'
import Footer from '@/views/components/footer'

export default {
  components: { personalTpl, companyTpl, Footer },
  data() {
    return {
      componentTpl: undefined
    }
  },
  created() {
    this.componentTpl = 'companyTpl'
  },
  methods: {
    // 切换组件
    handleChange(type) {
      this.componentTpl = type
    }
  }
}
</script>

<style lang="scss" scoped>
.register {
  display: flex;
  justify-content: center;
  align-items: center;
  height: calc(100vh - 60px);
  background-color: #f3f4f6;
  &-box {
    width: 1050px;
    height: 682px;
    padding: 30px 30px 30px 485px;
    background: url('~@/assets/global/register-bg.png') left center no-repeat #ffffff;
    background-size: 350px 100%;
  }
  &-login {
    cursor: pointer;
    text-align: right;
    a {
      color: #999999;
      font-size: 12px;
      &:hover {
        color: #2e73f3;
      }
    }
  }
  &-class {
    width: 430px;
    height: 40px;
    margin: 20px 0;
    display: flex;
    border-bottom: 1px solid #e2e6f3;
    position: relative;
    .class-item {
      width: 110px;
      height: 100%;
      text-align: center;
      color: #999;
      font-size: 14px;
      cursor: pointer;
      &.active {
        font-size: 16px;
        color: #333333;
        border-bottom: 2px solid #2e73f3;
      }
      &-tip {
        position: absolute;
        top: 45px;
        width: 100%;
        text-align: center;
        font-size: 12px;
        line-height: 24px;
        color: #f35d09;
        background-color: #ffe1d0;
        border: 1px solid rgba(243, 93, 9, 0.38);
        border-radius: 5px;
      }
    }
  }
}
</style>
