<template>
  <div>
    <el-dialog v-dialogDragBox title="合同产品明细" :visible.sync="open" width="1150px" class="custom-dialog">
      <div style="padding: 0 20px">
        <el-table :data="list" style="width: 100%" stripe class="custom-table">
          <el-table-column align="center" label="序号" type="index" width="50"></el-table-column>
          <el-table-column align="center" label="产品名称">
            <template slot-scope="{ row }">
              <div class="table-link" @click="handleDetail(row)" v-if="type == 'abnormal'">
                <template>
                  <span v-if="row.source === 'common'">(公域)</span>
                  <span style="color: #fe7f22" v-else>(私域)</span>
                </template>
                <span>{{ row.product && row.product.productName }}</span>
              </div>
              <div class="table-link" @click="handleDetail(row)" v-else>
                <template v-if="type !== 'unsalable'">
                  <span v-if="row.source === 'common'">(公域)</span>
                  <span style="color: #fe7f22" v-else>(私域)</span>
                </template>
                <span>{{ row.productName }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column align="center" label="产品单价">
            <template slot-scope="{ row }">
              <template v-if="type == 'abnormal'">
                <span class="table-orange">{{ row.amount ? '￥' + row.amount : '' }}{{ '元' + (row.unit ? '/' : '') +
                  (row.unit || '') }}</span>
              </template>
              <template v-else>
                <span class="table-orange" v-if="!type">{{ row.amount ? '￥' + row.amount : '' }}{{ '元' + (row.replyUnit
                  || row.unit ? '/' : '') + (row.replyUnit || row.unit || '') }}</span>
                <span class="table-orange" v-if="type === 'unsalable'">{{ row.amount ? '￥' + row.amount : ''
                }}元/吨</span>
              </template>
            </template>
          </el-table-column>
          <el-table-column align="center" label="采购数量">
            <template slot-scope="{ row }">
              <span v-if="type == 'abnormal'">{{ row.quantity }}</span>
              <span v-else>{{ row.sjNum + (row.replyUnit || row.unit) }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="金蝶库存" prop="jdStock"></el-table-column>
          <el-table-column align="center" label="自由客库存" prop="stock"></el-table-column>
          <el-table-column align="center" prop="remark" label="备注" v-if="type !== 'abnormal'"></el-table-column>
        </el-table>
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn primary" @click="open = false">关闭</el-button>
      </div>
    </el-dialog>
    <!-- 产品详情 -->
    <product-dialog ref="productInfo"></product-dialog>
  </div>
</template>
<script>
import { getProduct } from '@/api/system/product'
import { getPrivateduct } from '@/api/system/privateduct'
import ProductDialog from '@/views/public/product/dialog'
import { getUnsalable } from '@/api/unsalable'
import { inventoryList } from '@/api/inventory'

export default {
  components: { ProductDialog },
  data() {
    return {
      list: [],
      type: undefined,
      open: false
    }
  },
  methods: {
    handleView(list = [], type = undefined) {
      this.type = type
      if (!list.length) {
        this.$message.info('暂无产品明细')
        return
      }
      let arr = []
      list.map(async ite => {
        if (ite.materialNumber) {
          const stock = await inventoryList({ number: ite.materialNumber, pageNum: 1, pageSize: 1 })
          if (stock.rows[0] && stock.rows[0].stocks) {
            ite.jdStock = stock.rows[0].stocks ? stock.rows[0].stocks.reduce((total, item) => total + item.qty, 0) : 0
          } else ite.jdStock = 0
          ite.stock = stock.rows[0] ? (ite.jdStock - stock.rows[0].useQty) : 0
        } else {
          ite.jdStock = 0
          ite.stock = 0
        }
        arr.push(ite)
      })
      this.list = arr
      this.open = true
    },
    // 查看产品详情
    handleDetail(row) {
      if (this.type === 'abnormal') {
        this.$refs.productInfo.handleView(row.product)
      } else if (this.type === 'unsalable') {
        getUnsalable(row.productId).then(res => {
          this.$refs.productInfo.handleView(res.data)
        })
      } else {
        if (row.source === 'common') {
          getProduct(row.productId).then(res => {
            this.$refs.productInfo.handleView(res.data)
          })
        } else {
          getPrivateduct(row.productId).then(res => {
            this.$refs.productInfo.handleView(res.data)
          })
        }
      }
    }
  }
}
</script>

<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';
</style>
