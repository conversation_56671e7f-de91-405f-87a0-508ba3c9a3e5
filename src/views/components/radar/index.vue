<template>
  <el-dialog v-dialogDragBox title="评价" :visible.sync="radarOpen" width="800px" class="custom-dialog" :append-to-body="isLook">
    <div class="formBox">
      <el-form :model="radarInfo" ref="radarInfo" :rules="rules" label-width="5em" hide-required-asterisk :disabled="disabled">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="供货能力" prop="capacity">
              <el-rate v-model="radarInfo.capacity" show-text @change="handleRate('capacity')"></el-rate>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="时间管理" prop="times">
              <el-rate v-model="radarInfo.times" show-text @change="handleRate('times')"></el-rate>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="服务态度" prop="service">
              <el-rate v-model="radarInfo.service" show-text @change="handleRate('service')"></el-rate>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="售后服务" prop="sales">
              <el-rate v-model="radarInfo.sales" show-text @change="handleRate('sales')"></el-rate>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="发货速度" prop="speed">
              <el-rate v-model="radarInfo.speed" show-text @change="handleRate('speed')"></el-rate>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="图片" prop="urls" v-show="(disabled && radarInfo.urls) || !disabled">
          <image-upload :is-disabled="disabled" v-model="radarInfo.urls" />
        </el-form-item>
        <el-form-item label="内容" prop="comments">
          <el-input type="textarea" v-model="radarInfo.comments" :autosize="{ minRows: 3, maxRows: 6 }" resize="none" placeholder="请输入内容"></el-input>
        </el-form-item>
      </el-form>
    </div>
    <div slot="footer">
      <template v-if="disabled">
        <button type="button" class="custom-dialog-btn primary" @click="radarOpen = false">关闭</button>
      </template>
      <template v-else>
        <button type="button" class="custom-dialog-btn" @click="radarOpen = false">取消</button>
        <button type="button" class="custom-dialog-btn primary" @click="handleRadarSubmit">提交</button>
      </template>
    </div>
  </el-dialog>
</template>

<script>
import { score, viewScore } from '@/api/houtai/gongyu/caigou'

export default {
  props:{
    isLook: {
      type: Boolean,
      default: false
    }
  },
  data() {
    const checkNum = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('请选择评分'))
      } else {
        callback()
      }
    }
    return {
      disabled: false,
      radarOpen: false,
      radarInfo: {},
      rules: {
        capacity: [{ validator: checkNum, message: '请选择供货能力评分', trigger: 'change' }],
        times: [{ validator: checkNum, message: '请选择时间管理评分', trigger: 'change' }],
        service: [{ validator: checkNum, message: '请选择服务态度评分', trigger: 'change' }],
        sales: [{ validator: checkNum, message: '请选择售后服务评分', trigger: 'change' }],
        speed: [{ validator: checkNum, message: '请选择发货速度评分', trigger: 'change' }]
      }
    }
  },
  created() {},
  methods: {
    // 评价
    handleOpen(row, type) {
      if (row.hasOwnProperty('cjComment') && row.cjComment) {
        viewScore({ commentId: row.cjComment }).then(res => {
          const { code, msg, data } = res
          if (code === 200) {
            this.radarInfo = { ...data }
            this.disabled = true
          } else this.$message.error(msg)
        })
      } else {
        this.radarInfo = {
          capacity: undefined,
          comments: undefined,
          contractId: row.id,
          sales: undefined,
          seller: row.seller,
          service: undefined,
          speed: undefined,
          times: undefined,
          type: type,
          urls: undefined
        }
        this.resetForm('radarInfo')
        this.disabled = false
      }
      this.radarOpen = true
    },
    // 改变评分
    handleRate(name) {
      this.$refs['radarInfo'].clearValidate([name])
    },
    // 提交评价
    handleRadarSubmit() {
      this.$refs['radarInfo'].validate(valid => {
        if (valid) {
          score(this.radarInfo).then(res => {
            if (res.code === 200) {
              this.$message.success('评价成功')
              this.radarOpen = false
              this.$parent.getList()
            } else {
              this.$message.error(res.msg)
            }
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
::v-deep {
  .el-rate {
    display: inline-flex;
    align-items: center;
  }
  .el-form-item {
    margin-bottom: 10px;
    .el-form-item__error {
      top: 30px;
    }
  }
}
</style>
