<template>
  <el-dialog v-dialogDragBox :visible.sync="qrcodeOpen" :width="dialogWidth" :show-close="false" custom-class="qrcode-dialog">
    <div ref="qrCode"></div>
    <div class="qrcode-title">请用微信扫描二维码支付</div>
  </el-dialog>
</template>

<script>
import QRCode from 'qrcodejs2'
export default {
  props: {
    width: {
      type: Number,
      default: 200
    },
    height: {
      type: Number,
      default: 200
    },
    colorDark: {
      type: String,
      default: '#333333'
    },
    colorLight: {
      type: String,
      default: '#ffffff'
    },
    qrcodeText: {
      type: String,
      default: ''
    },
    qrcodeImage: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      qrcode: undefined,
      qrcodeOpen: false
    }
  },
  computed: {
    dialogWidth: function () {
      return this.width + 40 + 'px'
    }
  },
  watch: {
    qrcodeText() {
      this.createQrcode()
    }
  },
  mounted() {
    this.createQrcode()
  },
  methods: {
    // 生成二维码
    createQrcode() {
      this.qrcodeOpen = true
      this.$nextTick(() => {
        this.qrcode && (this.$refs.qrCode.innerHTML = '')
        this.qrcode = new QRCode(this.$refs.qrCode, {
          text: this.qrcodeText,
          width: this.width, // 二维码宽度 不支持100%
          height: this.height, // 二维码高度 不支持100%
          colorDark: this.colorDark, // 二维码颜色
          colorLight: this.colorLight, // 二维码背景色
          correctLevel: QRCode.CorrectLevel.H // 容错率L/M/H
        })
      })

      if (this.qrcodeImage) {
        const width = this.width
        const height = this.height
        let logo = new Image()
        logo.crossOrigin = 'Anonymous'
        logo.src = this.qrcodeImage
        logo.onload = () => {
          let qrImg = this.qrcode._el.getElementsByTagName('img')[0]
          let canvas = this.qrcode._el.getElementsByTagName('canvas')[0]
          this.qrcode._el.title = ''
          canvas.style.display = 'inline-block'
          let ctx = canvas.getContext('2d')
          ctx.drawImage(logo, (width - width / 4) / 2, (height - height / 4) / 2, width / 4, height / 4)
          qrImg.src = canvas.toDataURL()
          qrImg.style.display = 'none'
          // this.$refs.qrCode.appendChild(this.qrcode._el)
        }
      }
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .qrcode-dialog {
  .el-dialog__header {
    display: none;
  }
  .el-dialog__body {
    padding: 20px;
    .qrcode-title {
      text-align: center;
      font-size: 16px;
      line-height: 40px;
      margin-top: 20px;
    }
  }
}
</style>
