<template>
  <div>
    <div class="collect" v-if="show">
      <div class="collect-list" v-if="isCollect">
        <template v-if="hasCollect">
          <el-tooltip class="collect-list-menu" effect="dark" content="收藏夹管理" placement="top">
            <div @click="handleMange">
              <i class="el-icon-menu"></i>
              管理
            </div>
          </el-tooltip>
          <div class="collect-list-item" :class="{ 'item-active': !storeId }" @click="handleChangeTab" v-if="isAll">
            {{ allTitle }}
          </div>
          <template v-for="item in dataList">
            <div class="collect-list-item" :class="{ 'item-active': item.storeId == storeId }" :key="item.storeId" @click="handleChangeTab(item)">
              {{ item.dirName }}
            </div>
          </template>
        </template>
      </div>
      <div v-else></div>
      <div class="collect-bar">
        <div class="collect-sizer" v-if="tabOptions">
          <div class="collect-sizer-item" :class="{ 'item-active': item.value === tabName }" v-for="item in tabOptions" :key="item.value" @click="handleChangeSizer(item)">
            {{ item.label }}
          </div>
        </div>
        <div class="top-right-btn" :style="toolbarStyle" v-if="isSearch">
          <el-row>
            <el-tooltip class="item" effect="dark" :content="showSearch ? '隐藏搜索' : '显示搜索'" placement="top" v-if="search">
              <el-button size="mini" circle icon="el-icon-search" @click="toggleSearch()" />
            </el-tooltip>
            <el-tooltip class="item" effect="dark" content="刷新" placement="top">
              <el-button size="mini" circle icon="el-icon-refresh" @click="refresh()" />
            </el-tooltip>
            <el-tooltip class="item" effect="dark" content="显隐列" placement="top" v-if="columns">
              <el-button size="mini" circle icon="el-icon-menu" @click="showColumn()" />
            </el-tooltip>
          </el-row>
          <el-dialog v-dialogDragBox :title="toolbarTitle" :visible.sync="toolbarOpen" append-to-body width="540px">
            <el-transfer :titles="['显示', '隐藏']" v-model="toolbarValue" :data="columns" @change="dataChange"></el-transfer>
          </el-dialog>
        </div>
      </div>
    </div>

    <!-- 收藏夹管理 -->
    <el-dialog v-dialogDragBox title="收藏夹管理" :visible.sync="open" width="600px">
      <el-row :gutter="20" class="mb10" v-if="!isChange">
        <el-col :span="1.5">
          <el-button type="primary" plain size="small" icon="el-icon-plus" @click="handleAdd">新增收藏夹</el-button>
        </el-col>
      </el-row>
      <el-table :data="dataList" border v-if="dataList.length" :key="key" v-loading="loading">
        <el-table-column align="center" label="序号" type="index"></el-table-column>
        <el-table-column align="center" prop="dirName" label="收藏夹名称">
          <template slot-scope="{ row }">
            <span v-show="!row.isEdit">{{ row.dirName }}</span>
            <el-input v-show="row.isEdit" size="small" v-model="editData.dirName"></el-input>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="sort" label="排序" v-if="!isChange">
          <template slot-scope="{ row }">
            <span v-show="!row.isEdit">{{ row.sort }}</span>
            <el-input v-show="row.isEdit" size="small" type="number" placeholder="请输入排序序号" v-model="editData.sort"></el-input>
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作" width="120">
          <template slot-scope="{ row }">
            <template v-if="isChange">
              <el-button @click="submitChange(row)" type="text" size="small" icon="el-icon-check" :disabled="row.storeId === storeId">选择</el-button>
            </template>
            <template v-else>
              <template v-if="!row.isEdit">
                <el-button @click="handleClick(row)" type="text" size="small" icon="el-icon-edit">编辑</el-button>
                <el-button @click="handleDelete(row)" type="text" size="small" icon="el-icon-delete">删除</el-button>
              </template>
              <template v-if="row.isEdit">
                <el-button @click="handleUpdate" type="text" size="small" icon="el-icon-check">确定</el-button>
                <el-button @click="handleCancel(row)" type="text" size="small" icon="el-icon-close">取消</el-button>
              </template>
            </template>
          </template>
        </el-table-column>
      </el-table>
      <el-empty v-else />
      <span slot="footer">
        <el-button @click="open = false">取消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getlist, addlist, editlist, dellist } from '@/api/houtai/shoucang'

export default {
  props: {
    // 是否显示
    show: {
      type: Boolean,
      default: true
    },
    // 收藏夹类别
    type: {
      type: String,
      default: 'UserProduct'
    },
    // 筛选条件
    tabOptions: {
      type: Array,
      default: function () {
        return []
      }
    },
    // 选中的筛选条件
    tabName: {
      type: [String, Number],
      default: ''
    },
    // 是否需要显示全部查询
    isAll: {
      type: Boolean,
      default: false
    },
    // 全部标题
    allTitle: {
      type: String,
      default: '全部'
    },
    // 默认不显示toolbar
    isSearch: {
      type: Boolean,
      default: false
    },
    showSearch: {
      type: Boolean,
      default: true
    },
    columns: {
      type: Array
    },
    search: {
      type: Boolean,
      default: true
    },
    gutter: {
      type: Number,
      default: 10
    },
    // 是否显示收藏夹
    isCollect: {
      type: Boolean,
      default: true
    },
    // 是否查询收藏夹
    hasCollect: {
      type: Boolean,
      default: true
    },
    hasstoreId: {
      type: [String, Number],
      default: undefined
    }
  },
  data() {
    return {
      loading: true,
      dataList: [],
      storeId: undefined,
      open: false,
      key: 1,
      editData: {},
      isChange: false,
      changData: undefined,
      toolbarValue: [],
      toolbarTitle: '显示/隐藏',
      toolbarOpen: false
    }
  },
  computed: {
    toolbarStyle() {
      const ret = {}
      if (this.gutter) {
        ret.marginRight = `${this.gutter / 2}px`
      }
      return ret
    }
  },
  watch: {
    hasstoreId(val) {
      if (val) {
        this.storeId = val
      }
    }
  },
  created() {
    this.getList()
    // 显隐列初始默认隐藏列
    for (let item in this.columns) {
      if (this.columns[item].visible === false) {
        this.toolbarValue.push(parseInt(item))
      }
    }
  },
  methods: {
    // 收藏列表
    getList(val) {
      if (this.hasCollect) {
        const type = this.type
        this.loading = true
        getlist({ type }).then(res => {
          this.key = Math.random()
          this.dataList = res.data
          this.dataList.map(item => {
            item.isEdit = false
          })
          this.$emit('update:collectList', res.data)
          this.loading = false
          if (!this.storeId && res.data.length) {
            if (this.isCollect) {
              if (!this.isAll) {
                this.storeId = res.data[0].storeId
                this.$emit('update:storeId', res.data[0].storeId)
              }
              if (!val) this.$emit('getList')
            }
          }
        })
      }
    },
    // 收藏夹管理
    handleMange() {
      this.open = true
      this.isChange = false
      this.getList('no')
    },
    // 新增收藏名称
    // prettier-ignore
    handleAdd() {
      this.$prompt('请输入收藏夹名称', '新增收藏夹', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
      }).then(({ value }) => {
        if (!value) return
        addlist({ dirName: value, type: this.type }).then((res) => {
          this.$modal.msgSuccess('新增成功')
          this.getList('no')
        })
      }).catch(() => {
      })
    },
    // 删除收藏名称
    // prettier-ignore
    handleDelete(item) {
      const data = { storeId: item.storeId, type: this.type }
      this.$modal.confirm('是否确认删除此收藏？').then(function () {
        return dellist(data)
      }).then(() => {
        this.getList('no')
        this.$modal.msgSuccess('删除成功')
      }).catch(() => {
      })
    },
    // 修改收藏名称
    handleClick(item) {
      item.isEdit = true
      this.editData = { ...item }
      this.key = Math.random()
    },
    // 取消收藏名称
    handleCancel(item) {
      item.isEdit = false
      this.key = Math.random()
    },
    // 提交收藏名称
    handleUpdate() {
      const data = {
        storeId: this.editData.storeId,
        dirName: this.editData.dirName,
        sort: this.editData.sort,
        type: this.type
      }
      editlist(data).then(res => {
        this.getList()
        this.$modal.msgSuccess('修改成功')
      })
    },
    // 收藏列表切换
    handleChangeTab(item) {
      if (item) {
        const storeId = item.storeId
        this.storeId = storeId
        this.$emit('update:storeId', storeId)
      } else {
        this.storeId = undefined
      }
      this.$emit('getList')
    },
    // 选择收藏夹
    handleChange(changData) {
      this.open = true
      this.isChange = true
      this.changData = changData
    },
    // 确定选择收藏夹
    submitChange(item) {
      const storeId = item.storeId
      this.$emit('colletSubmit', storeId, this.changData)
      this.open = false
    },
    // 筛选切换
    handleChangeSizer(item) {
      const value = item.value
      this.$emit('update:tabName', value)
    },
    // 搜索
    toggleSearch() {
      this.$emit('update:showSearch', !this.showSearch)
    },
    // 刷新
    refresh() {
      this.$emit('queryTable')
    },
    // 右侧列表元素变化
    dataChange(data) {
      for (let item in this.columns) {
        const key = this.columns[item].key
        this.columns[item].visible = !data.includes(key)
      }
    },
    // 打开显隐列dialog
    showColumn() {
      this.toolbarOpen = true
    }
  }
}
</script>

<style lang="scss" scoped>
.collect {
  margin-bottom: 18px;
  background-color: #f5f7fa;
  border: 1px solid #dcdfe6;
  display: flex;
  justify-content: space-between;
  &-title {
    display: inline-block;
    font-size: 15px;
    line-height: 30px;
    margin-bottom: 5px;
    color: #409eff;
  }
  &-list {
    display: flex;
    flex-wrap: wrap;
    line-height: 50px;
    font-size: 14px;
    font-weight: 500;
    &-menu {
      padding: 0 10px;
      color: #333;
      cursor: pointer;
      border-right: 1px solid #f1f1f1;
      i {
        font-size: 16px;
      }
      &:hover {
        color: #409eff;
      }
    }
    &-item {
      transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
      color: #666666;
      padding: 0 20px;
      margin: -1px 0;
      cursor: pointer;
      box-sizing: border-box;
      list-style: none;
      &:hover,
      &.item-active {
        background-color: #409eff;
        color: #fff;
      }
    }
  }
  &-bar {
    display: flex;
    align-items: center;
    flex-shrink: 0;
  }
  &-sizer {
    display: inline-flex;
    height: 50px;
    align-items: center;
    margin-left: 20px;
    cursor: pointer;
    &-item {
      transition: all 0.3s;
      font-size: 14px;
      color: #666666;
      line-height: 30px;
      margin-right: 10px;
      border: 1px solid #cccccc;
      border-radius: 5px;
      padding: 0 20px;
      &:hover,
      &.item-active {
        color: #409eff;
        border-color: #409eff;
      }
    }
  }
}
::v-deep .el-dialog__body {
  padding: 10px 20px;
}
::v-deep .el-transfer__button {
  border-radius: 50%;
  padding: 12px;
  display: block;
  margin-left: 0px;
}
::v-deep .el-transfer__button:first-child {
  margin-bottom: 10px;
}
::v-deep .el-transfer__button:nth-child(2) {
  margin-left: 0;
}
</style>
