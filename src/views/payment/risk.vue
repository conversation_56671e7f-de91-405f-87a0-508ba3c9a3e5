<template>
  <div>
    <div class="classify flex">
      <div class="classify-item" :class="{ active: currentView === 'risk' }" @click="currentView = 'risk'">企业风控</div>
      <div class="classify-item" :class="{ active: currentView === 'collect' }" @click="currentView = 'collect'">关注企业</div>
    </div>
    <component :is="currentView" />
  </div>
</template>
<script>
import risk from './riskTpl'
import collect from './companyCollect'

export default {
  name: 'BusinessRisk',
  components: { risk, collect },
  data() {
    return {
      currentView: 'risk'
    }
  },
  created() {
    const query = this.$route.query
    if (query && query.view) {
      this.currentView = query.view
    }
  },
  methods: {}
}
</script>

<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';
</style>
