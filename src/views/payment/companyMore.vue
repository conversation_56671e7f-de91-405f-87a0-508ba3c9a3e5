<template>
  <div>
    <div v-loading="loading" v-if="show">
      <template v-if="hasMore">
        <div class="organizationBox">
          <el-tabs class="detailTab bigTab" v-model="activeOneName" @tab-click="handleOneSwitch">
            <el-tab-pane label="组织架构" name="manager">
              <el-table :key="key" v-loading="managerLoading" :data="managerList" stripe border style="width: 100%" class="custom-table organizationTable">
                <el-table-column type="index" label="序号" width="50"></el-table-column>
                <el-table-column prop="name" label="姓名"></el-table-column>
                <el-table-column prop="position" label="职务"></el-table-column>
              </el-table>
            </el-tab-pane>
            <el-tab-pane label="股份构成" name="holder">
              <el-table :key="key" v-loading="holderLoading" :data="holderList" stripe border style="width: 100%" class="custom-table organizationTable">
                <el-table-column type="index" label="序号" width="50"></el-table-column>
                <el-table-column prop="name" label="姓名"></el-table-column>
                <el-table-column label="持股数量/认缴金额">
                  <template slot-scope="{ row }">{{ row.captial + row.captialUnit }}</template>
                </el-table-column>
                <el-table-column label="持股比例">
                  <template slot-scope="{ row }">{{ row.percent ? row.percent + '%' : '' }}</template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
            <el-tab-pane label="分支机构" name="branch">
              <el-table :key="key" v-loading="branchLoading" :data="branchList" stripe border style="width: 100%" class="custom-table organizationTable">
                <el-table-column type="index" label="序号" width="50"></el-table-column>
                <el-table-column prop="name" label="分支机构名称"></el-table-column>
                <el-table-column prop="headName" label="负责人"></el-table-column>
                <el-table-column prop="createDate" label="成立日期"></el-table-column>
                <el-table-column prop="branchStatus" label="企业状态"></el-table-column>
                <el-table-column prop="province" label="所在省"></el-table-column>
                <el-table-column prop="city" label="所在市"></el-table-column>
                <el-table-column prop="county" label="所在区/县"></el-table-column>
              </el-table>
            </el-tab-pane>
            <el-tab-pane label="工商变更" name="change">
              <el-table :key="key" v-loading="changeLoading" :data="changeList" stripe border style="width: 100%" class="custom-table organizationTable">
                <el-table-column type="index" label="序号" width="50"></el-table-column>
                <el-table-column prop="changeDate" label="变更时间" width="120"></el-table-column>
                <el-table-column prop="changeItem" label="变更项目" width="150"></el-table-column>
                <el-table-column prop="beforeChange" label="变更前">
                  <template slot-scope="{ row }">
                    <div class="changeInfo" :class="{ none: !row.beforeChange }">
                      {{ row.beforeChange }}
                      <span class="changeInfoBtn" @click="handleChangeToggle(row)" v-if="row.beforeChange.length > 120">
                        {{ row.beforeChange ? '展开' : '收起' }}
                      </span>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="afterChange" label="变更后">
                  <template slot-scope="{ row }">
                    <div class="changeInfo" :class="{ none: !row.afterChange }">
                      {{ row.afterChange }}
                      <span class="changeInfoBtn" @click="handleChangeToggle(row)" v-if="row.afterChange.length > 120">
                        {{ row.afterChange ? '展开' : '收起' }}
                      </span>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
            <!--控股子公司-->
            <el-tab-pane label="控股子公司" name="child">
              <el-table :key="key" v-loading="childLoading" :data="childList" stripe border style="width: 100%" class="custom-table organizationTable">
                <el-table-column type="index" label="序号" width="50"></el-table-column>
                <el-table-column prop="name" label="企业名称"></el-table-column>
                <el-table-column prop="companyLevel" label="公司层级"></el-table-column>
                <el-table-column prop="cr0136_003" label="投资比例"></el-table-column>
                <el-table-column prop="regCapital" label="注册资本"></el-table-column>
                <el-table-column prop="legalPerson" label="法定代表人"></el-table-column>
                <el-table-column prop="region" label="地区"></el-table-column>
                <el-table-column prop="industry" label="行业"></el-table-column>
              </el-table>
              <pagination v-show="childTotal > 10" :total="childTotal" :page.sync="basicParams.pageNum" :limit.sync="basicParams.pageSize" @pagination="handleChildList()" />
            </el-tab-pane>
            <!--疑似关系-->
            <el-tab-pane label="疑似关系" name="suspicious">
              <el-table :key="key" v-loading="suspiciousLoading" :data="suspiciousList" stripe border style="width: 100%" class="custom-table organizationTable">
                <el-table-column type="index" label="序号" width="50"></el-table-column>
                <el-table-column prop="companyName" label="企业名称"></el-table-column>
                <el-table-column prop="suspectRelationTypeExport" label="关系类型"></el-table-column>
                <el-table-column prop="legalRepresent" label="法定代表人"></el-table-column>
                <el-table-column prop="establishDate" label="成立日期"></el-table-column>
                <el-table-column prop="companyState" label="企业状态"></el-table-column>
                <el-table-column prop="area" label="地区"></el-table-column>
                <el-table-column prop="industry" label="行业"></el-table-column>
              </el-table>
              <pagination v-show="suspiciousTotal > 10" :total="suspiciousTotal" :page.sync="basicParams.pageNum" :limit.sync="basicParams.pageSize" @pagination="handleSuspiciousList()" />
            </el-tab-pane>
          </el-tabs>
          <div class="flex align-center organizationRefresh">
            <span>更新时间：{{ createTime }}</span>
            <el-tooltip effect="dark" content="点击刷新" placement="top">
              <i class="el-icon-refresh" @click="handleRefresh"></i>
            </el-tooltip>
          </div>
        </div>
        <div class="otherBox">
          <el-tabs class="otherTab" type="border-card" v-model="activeTwoName" @tab-click="handleTwoSwitch">
            <el-tab-pane label="企业司法预警信息" name="judicial"></el-tab-pane>
            <el-tab-pane label="企业经营" name="run"></el-tab-pane>
            <el-tab-pane label="监管处罚" name="punish"></el-tab-pane>
            <el-tab-pane label="信用评级" name="credit"></el-tab-pane>
            <el-tab-pane label="新闻公告" name="news"></el-tab-pane>
          </el-tabs>
          <el-tabs class="detailTab smallTab" v-model="activeThreeName" @tab-click="handleThreeSwitch" v-if="activeTwoName === 'judicial'">
            <el-tab-pane label="开庭公告" name="courtList">
              <span slot="label">
                开庭公告
                <i style="color: #f30909; font-style: normal" v-if="courtTotal">{{ courtTotal }}</i>
              </span>
              <el-table :key="key" v-loading="courtLoading" border :data="courtList" stripe style="width: 100%" class="custom-table organizationTable">
                <el-table-column type="index" label="序号" width="50"></el-table-column>
                <el-table-column prop="courtAnnounceDate" label="开庭时间"></el-table-column>
                <el-table-column prop="caseNum" label="案号">
                  <template slot-scope="{ row }">
                    {{ row.caseNum }}
                    <el-tag type="warning" size="small" v-if="row.status == '1'">已结案</el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="caseReason" label="案由"></el-table-column>
                <el-table-column label="诉讼地位">
                  <template slot-scope="{ row }">
                    <template v-if="row.hasOwnProperty('litigantInfos') && row.litigantInfos">
                      <div class="courtItem" v-if="row.litigantInfos.filter(ite => ite.identity === 6).length">
                        <span>{{ row.litigantInfos.find(ite => ite.identity === 6).identityName + '：' }}</span>
                        <em v-for="(item, idx) in row.litigantInfos.filter(ite => ite.identity === 6)">{{ (idx ? '、' : '') + item.name }}</em>
                      </div>
                      <div class="courtItem" v-if="row.litigantInfos.filter(ite => ite.identity === 5).length">
                        <span>{{ row.litigantInfos.find(ite => ite.identity === 5).identityName + '：' }}</span>
                        <em v-for="(item, idx) in row.litigantInfos.filter(ite => ite.identity === 5)">{{ (idx ? '、' : '') + item.name }}</em>
                      </div>
                      <div class="courtItem" v-if="row.litigantInfos.filter(ite => ite.identity === 4).length">
                        <span>{{ row.litigantInfos.find(ite => ite.identity === 4).identityName + '：' }}</span>
                        <em v-for="(item, idx) in row.litigantInfos.filter(ite => ite.identity === 4)">{{ (idx ? '、' : '') + item.name }}</em>
                      </div>
                      <div class="courtItem" v-if="row.litigantInfos.filter(ite => ite.identity === 3).length">
                        <span>{{ row.litigantInfos.find(ite => ite.identity === 3).identityName + '：' }}</span>
                        <em v-for="(item, idx) in row.litigantInfos.filter(ite => ite.identity === 3)">{{ (idx ? '、' : '') + item.name }}</em>
                      </div>
                      <div class="courtItem" v-if="row.litigantInfos.filter(ite => ite.identity === 2).length">
                        <span>{{ row.litigantInfos.find(ite => ite.identity === 2).identityName + '：' }}</span>
                        <em v-for="(item, idx) in row.litigantInfos.filter(ite => ite.identity === 2)">{{ (idx ? '、' : '') + item.name }}</em>
                      </div>
                      <div class="courtItem" v-if="row.litigantInfos.filter(ite => ite.identity === 1).length">
                        <span>{{ row.litigantInfos.find(ite => ite.identity === 1).identityName + '：' }}</span>
                        <em v-for="(item, idx) in row.litigantInfos.filter(ite => ite.identity === 1)">{{ (idx ? '、' : '') + item.name }}</em>
                      </div>
                    </template>
                  </template>
                </el-table-column>
                <el-table-column align="center" label="详情" width="70">
                  <template slot-scope="{ row }">
                    <el-button type="text" @click="handleView(row, '开庭公告详情', 0)" size="small">查看</el-button>
                  </template>
                </el-table-column>
              </el-table>
              <pagination v-show="courtTotal > 10" :total="courtTotal" :page.sync="params.pageNum" :limit.sync="params.pageSize" @pagination="handleCourtList()" />
            </el-tab-pane>
            <el-tab-pane label="送达公告" name="deliveryList">
              <span slot="label">
                送达公告
                <i style="color: #f30909; font-style: normal" v-if="deliveryTotal">{{ deliveryTotal }}</i>
              </span>
              <el-table :key="key" v-loading="deliveryLoading" border :data="deliveryList" stripe style="width: 100%" class="custom-table organizationTable">
                <el-table-column type="index" label="序号" width="50"></el-table-column>
                <el-table-column prop="announceDate" label="发布日期">
                  <template slot-scope="{ row }">{{ parseTime(row.announceDate, '{y}-{m}-{d}') }}</template>
                </el-table-column>
                <el-table-column prop="caseNum" label="案号">
                  <template slot-scope="{ row }">
                    {{ row.caseNum }}
                    <el-tag type="warning" size="small" v-if="row.status == '1'">已结案</el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="caseReason" label="案由"></el-table-column>
                <el-table-column prop="court" label="法院"></el-table-column>
                <el-table-column label="诉讼地位">
                  <template slot-scope="{ row }">
                    <template v-if="row.hasOwnProperty('litigantInfos') && row.litigantInfos">
                      <div class="courtItem" v-if="row.litigantInfos.filter(ite => ite.identity === 6).length">
                        <span>{{ row.litigantInfos.find(ite => ite.identity === 6).identityName + '：' }}</span>
                        <em v-for="(item, idx) in row.litigantInfos.filter(ite => ite.identity === 6)">{{ (idx ? '、' : '') + item.name }}</em>
                      </div>
                      <div class="courtItem" v-if="row.litigantInfos.filter(ite => ite.identity === 5).length">
                        <span>{{ row.litigantInfos.find(ite => ite.identity === 5).identityName + '：' }}</span>
                        <em v-for="(item, idx) in row.litigantInfos.filter(ite => ite.identity === 5)">{{ (idx ? '、' : '') + item.name }}</em>
                      </div>
                      <div class="courtItem" v-if="row.litigantInfos.filter(ite => ite.identity === 4).length">
                        <span>{{ row.litigantInfos.find(ite => ite.identity === 4).identityName + '：' }}</span>
                        <em v-for="(item, idx) in row.litigantInfos.filter(ite => ite.identity === 4)">{{ (idx ? '、' : '') + item.name }}</em>
                      </div>
                      <div class="courtItem" v-if="row.litigantInfos.filter(ite => ite.identity === 3).length">
                        <span>{{ row.litigantInfos.find(ite => ite.identity === 3).identityName + '：' }}</span>
                        <em v-for="(item, idx) in row.litigantInfos.filter(ite => ite.identity === 3)">{{ (idx ? '、' : '') + item.name }}</em>
                      </div>
                      <div class="courtItem" v-if="row.litigantInfos.filter(ite => ite.identity === 2).length">
                        <span>{{ row.litigantInfos.find(ite => ite.identity === 2).identityName + '：' }}</span>
                        <em v-for="(item, idx) in row.litigantInfos.filter(ite => ite.identity === 2)">{{ (idx ? '、' : '') + item.name }}</em>
                      </div>
                      <div class="courtItem" v-if="row.litigantInfos.filter(ite => ite.identity === 1).length">
                        <span>{{ row.litigantInfos.find(ite => ite.identity === 1).identityName + '：' }}</span>
                        <em v-for="(item, idx) in row.litigantInfos.filter(ite => ite.identity === 1)">{{ (idx ? '、' : '') + item.name }}</em>
                      </div>
                    </template>
                  </template>
                </el-table-column>
                <el-table-column align="center" label="详情" width="70">
                  <template slot-scope="{ row }">
                    <el-button type="text" @click="handleView(row, '送达公告', 1)" size="small">查看</el-button>
                  </template>
                </el-table-column>
              </el-table>
              <pagination v-show="deliveryTotal > 10" :total="deliveryTotal" :page.sync="params.pageNum" :limit.sync="params.pageSize" @pagination="handleDeliveryList()" />
            </el-tab-pane>
            <el-tab-pane label="失信被执行人" name="dishonestList">
              <span slot="label">
                失信被执行人
                <i style="color: #f30909; font-style: normal" v-if="dishonestTotal">{{ dishonestTotal }}</i>
              </span>
              <el-table :key="key" v-loading="dishonestLoading" border :data="dishonestList" stripe style="width: 100%" class="custom-table organizationTable">
                <el-table-column type="index" label="序号" width="50"></el-table-column>
                <el-table-column prop="caseNum" label="案号"></el-table-column>
                <el-table-column prop="applicant" label="申请执行人"></el-table-column>
                <el-table-column prop="publishDate" label="发布日期"></el-table-column>
                <el-table-column prop="announceDate" label="立案日期"></el-table-column>
                <el-table-column align="center" label="详情" width="70">
                  <template slot-scope="{ row }">
                    <el-button type="text" @click="handleView(row, '失信被执行人详情', 2)" size="small">查看</el-button>
                  </template>
                </el-table-column>
              </el-table>
              <pagination v-show="dishonestTotal > 10" :total="dishonestTotal" :page.sync="params.pageNum" :limit.sync="params.pageSize" @pagination="handleDishonestList()" />
            </el-tab-pane>
            <el-tab-pane label="被执行人" name="executedList">
              <span slot="label">
                被执行人
                <i style="color: #f30909; font-style: normal" v-if="executedTotal">{{ executedTotal }}</i>
              </span>
              <el-table :key="key" v-loading="executedLoading" border :data="executedList" stripe style="width: 100%" class="custom-table organizationTable">
                <el-table-column type="index" label="序号" width="50"></el-table-column>
                <el-table-column prop="caseNum" label="案号"></el-table-column>
                <el-table-column prop="court" label="执行法院"></el-table-column>
                <el-table-column prop="executeTarget" label="执行标的(元)"></el-table-column>
                <el-table-column prop="applyExecutorDetail" label="申请执行人">
                  <template slot-scope="{ row }">
                    <div class="courtItem" v-if="row.applyExecutorDetail && row.applyExecutorDetail.length">
                      <em v-for="(item, idx) in row.applyExecutorDetail">{{ (idx ? '、' : '') + item.name }}</em>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="registerDate" label="立案日期"></el-table-column>
                <el-table-column align="center" label="详情" width="70">
                  <template slot-scope="{ row }">
                    <el-button type="text" @click="handleView(row, '被执行人详情', 3)" size="small">查看</el-button>
                  </template>
                </el-table-column>
              </el-table>
              <pagination v-show="executedTotal > 10" :total="executedTotal" :page.sync="params.pageNum" :limit.sync="params.pageSize" @pagination="handleExecutedList()" />
            </el-tab-pane>
            <el-tab-pane label="立案信息" name="filingList">
              <span slot="label">
                立案信息
                <i style="color: #f30909; font-style: normal" v-if="filingTotal">{{ filingTotal }}</i>
              </span>
              <el-table :key="key" v-loading="filingLoading" border :data="filingList" stripe style="width: 100%" class="custom-table organizationTable">
                <el-table-column type="index" label="序号" width="50"></el-table-column>
                <el-table-column prop="caseNum" label="案号">
                  <template slot-scope="{ row }">
                    {{ row.caseNum }}
                    <el-tag type="warning" size="small" v-if="row.status == '1'">已结案</el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="caseReason" label="案由"></el-table-column>
                <el-table-column label="诉讼地位">
                  <template slot-scope="{ row }">
                    <template v-if="row.hasOwnProperty('litigantInfos') && row.litigantInfos">
                      <div class="courtItem" v-if="row.litigantInfos.filter(ite => ite.identity === 6).length">
                        <span>{{ row.litigantInfos.find(ite => ite.identity === 6).identityName + '：' }}</span>
                        <em v-for="(item, idx) in row.litigantInfos.filter(ite => ite.identity === 6)">{{ (idx ? '、' : '') + item.name }}</em>
                      </div>
                      <div class="courtItem" v-if="row.litigantInfos.filter(ite => ite.identity === 5).length">
                        <span>{{ row.litigantInfos.find(ite => ite.identity === 5).identityName + '：' }}</span>
                        <em v-for="(item, idx) in row.litigantInfos.filter(ite => ite.identity === 5)">{{ (idx ? '、' : '') + item.name }}</em>
                      </div>
                      <div class="courtItem" v-if="row.litigantInfos.filter(ite => ite.identity === 4).length">
                        <span>{{ row.litigantInfos.find(ite => ite.identity === 4).identityName + '：' }}</span>
                        <em v-for="(item, idx) in row.litigantInfos.filter(ite => ite.identity === 4)">{{ (idx ? '、' : '') + item.name }}</em>
                      </div>
                      <div class="courtItem" v-if="row.litigantInfos.filter(ite => ite.identity === 3).length">
                        <span>{{ row.litigantInfos.find(ite => ite.identity === 3).identityName + '：' }}</span>
                        <em v-for="(item, idx) in row.litigantInfos.filter(ite => ite.identity === 3)">{{ (idx ? '、' : '') + item.name }}</em>
                      </div>
                      <div class="courtItem" v-if="row.litigantInfos.filter(ite => ite.identity === 2).length">
                        <span>{{ row.litigantInfos.find(ite => ite.identity === 2).identityName + '：' }}</span>
                        <em v-for="(item, idx) in row.litigantInfos.filter(ite => ite.identity === 2)">{{ (idx ? '、' : '') + item.name }}</em>
                      </div>
                      <div class="courtItem" v-if="row.litigantInfos.filter(ite => ite.identity === 1).length">
                        <span>{{ row.litigantInfos.find(ite => ite.identity === 1).identityName + '：' }}</span>
                        <em v-for="(item, idx) in row.litigantInfos.filter(ite => ite.identity === 1)">{{ (idx ? '、' : '') + item.name }}</em>
                      </div>
                    </template>
                  </template>
                </el-table-column>
                <el-table-column prop="announceDate" label="立案日期"></el-table-column>
                <el-table-column align="center" label="详情" width="70">
                  <template slot-scope="{ row }">
                    <el-button type="text" @click="handleView(row, '立案信息详情', 4)" size="small">查看</el-button>
                  </template>
                </el-table-column>
              </el-table>
              <pagination v-show="filingTotal > 10" :total="filingTotal" :page.sync="params.pageNum" :limit.sync="params.pageSize" @pagination="handleFilingList()" />
            </el-tab-pane>
            <el-tab-pane label="终本案件" name="finalList">
              <span slot="label">
                终本案件
                <i style="color: #f30909; font-style: normal" v-if="finalTotal">{{ finalTotal }}</i>
              </span>
              <el-table :key="key" v-loading="finalLoading" border :data="finalList" stripe style="width: 100%" class="custom-table organizationTable">
                <el-table-column type="index" label="序号" width="50"></el-table-column>
                <el-table-column prop="defaulter" label="被执行人"></el-table-column>
                <el-table-column label="申请执行人">
                  <template slot-scope="{ row }">
                    <div class="courtItem" v-if="row.hasOwnProperty('litigantInfos') && row.litigantInfos.length">
                      <em v-for="(item, idx) in row.litigantInfos">{{ (idx ? '、' : '') + item.name }}</em>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="registerDate" label="立案日期"></el-table-column>
                <el-table-column prop="finalDate" label="终本日期"></el-table-column>
                <el-table-column prop="caseNum" label="案号"></el-table-column>
                <el-table-column prop="money" label="执行标的(元)"></el-table-column>
                <el-table-column prop="noPayMoney" label="未履行金额(元)"></el-table-column>
                <el-table-column prop="court" label="执行法院"></el-table-column>
                <el-table-column align="center" label="详情" width="70">
                  <template slot-scope="{ row }">
                    <el-button type="text" @click="handleView(row, '终本案件详情', 5)" size="small">查看</el-button>
                  </template>
                </el-table-column>
              </el-table>
              <pagination v-show="finalTotal > 10" :total="finalTotal" :page.sync="params.pageNum" :limit.sync="params.pageSize" @pagination="handleFinalList()" />
            </el-tab-pane>
            <el-tab-pane label="法院公告" name="legalList">
              <span slot="label">
                法院公告
                <i style="color: #f30909; font-style: normal" v-if="legalTotal">{{ legalTotal }}</i>
              </span>
              <el-table :key="key" v-loading="legalLoading" border :data="legalList" stripe style="width: 100%" class="custom-table organizationTable">
                <el-table-column type="index" label="序号" width="50"></el-table-column>
                <el-table-column prop="punishDate" label="刊登日期"></el-table-column>
                <el-table-column prop="announcementType" label="公告类型"></el-table-column>
                <el-table-column prop="relateCaseNum" label="案号"></el-table-column>
                <el-table-column prop="caseReason" label="案由"></el-table-column>
                <el-table-column prop="court" label="法院"></el-table-column>
                <el-table-column label="诉讼地位">
                  <template slot-scope="{ row }">
                    <template v-if="row.hasOwnProperty('litigantInfos') && row.litigantInfos">
                      <div class="courtItem" v-if="row.litigantInfos.filter(ite => ite.identity === 6).length">
                        <span>{{ row.litigantInfos.find(ite => ite.identity === 6).identityName + '：' }}</span>
                        <em v-for="(item, idx) in row.litigantInfos.filter(ite => ite.identity === 6)">{{ (idx ? '、' : '') + item.name }}</em>
                      </div>
                      <div class="courtItem" v-if="row.litigantInfos.filter(ite => ite.identity === 5).length">
                        <span>{{ row.litigantInfos.find(ite => ite.identity === 5).identityName + '：' }}</span>
                        <em v-for="(item, idx) in row.litigantInfos.filter(ite => ite.identity === 5)">{{ (idx ? '、' : '') + item.name }}</em>
                      </div>
                      <div class="courtItem" v-if="row.litigantInfos.filter(ite => ite.identity === 4).length">
                        <span>{{ row.litigantInfos.find(ite => ite.identity === 4).identityName + '：' }}</span>
                        <em v-for="(item, idx) in row.litigantInfos.filter(ite => ite.identity === 4)">{{ (idx ? '、' : '') + item.name }}</em>
                      </div>
                      <div class="courtItem" v-if="row.litigantInfos.filter(ite => ite.identity === 3).length">
                        <span>{{ row.litigantInfos.find(ite => ite.identity === 3).identityName + '：' }}</span>
                        <em v-for="(item, idx) in row.litigantInfos.filter(ite => ite.identity === 3)">{{ (idx ? '、' : '') + item.name }}</em>
                      </div>
                      <div class="courtItem" v-if="row.litigantInfos.filter(ite => ite.identity === 2).length">
                        <span>{{ row.litigantInfos.find(ite => ite.identity === 2).identityName + '：' }}</span>
                        <em v-for="(item, idx) in row.litigantInfos.filter(ite => ite.identity === 2)">{{ (idx ? '、' : '') + item.name }}</em>
                      </div>
                      <div class="courtItem" v-if="row.litigantInfos.filter(ite => ite.identity === 1).length">
                        <span>{{ row.litigantInfos.find(ite => ite.identity === 1).identityName + '：' }}</span>
                        <em v-for="(item, idx) in row.litigantInfos.filter(ite => ite.identity === 1)">{{ (idx ? '、' : '') + item.name }}</em>
                      </div>
                    </template>
                  </template>
                </el-table-column>
                <el-table-column align="center" label="详情" width="70">
                  <template slot-scope="{ row }">
                    <el-button type="text" @click="handleView(row, '法院公告详情', 6)" size="small">查看</el-button>
                  </template>
                </el-table-column>
              </el-table>
              <pagination v-show="legalTotal > 10" :total="legalTotal" :page.sync="params.pageNum" :limit.sync="params.pageSize" @pagination="handleLegalList()" />
            </el-tab-pane>
            <el-tab-pane label="限制高消费" name="restrainingList">
              <span slot="label">
                限制高消费
                <i style="color: #f30909; font-style: normal" v-if="restrainingTotal">{{ restrainingTotal }}</i>
              </span>
              <el-table :key="key" v-loading="restrainingLoading" border :data="restrainingList" stripe style="width: 100%" class="custom-table organizationTable">
                <el-table-column type="index" label="序号" width="50"></el-table-column>
                <el-table-column prop="defaulter" label="限制对象"></el-table-column>
                <el-table-column prop="itName" label="企业名称"></el-table-column>
                <el-table-column prop="caseNum" label="案号"></el-table-column>
                <el-table-column prop="applicant" label="申请执行人"></el-table-column>
                <el-table-column prop="court" label="执行法院"></el-table-column>
                <el-table-column prop="announceDate" label="立案日期"></el-table-column>
                <el-table-column prop="publishDate" label="公告日期"></el-table-column>
                <el-table-column align="center" label="详情" width="70">
                  <template slot-scope="{ row }">
                    <el-button type="text" @click="handleView(row, '限制高消费详情', 7)" size="small">查看</el-button>
                  </template>
                </el-table-column>
              </el-table>
              <pagination v-show="restrainingTotal > 10" :total="restrainingTotal" :page.sync="params.pageNum" :limit.sync="params.pageSize" @pagination="handleRestrainingList()" />
            </el-tab-pane>
          </el-tabs>
          <el-tabs class="detailTab smallTab" v-model="activeFourName" @tab-click="handleFourSwitch" v-if="activeTwoName === 'run'">
            <el-tab-pane label="招投标" name="biddingList">
              <span slot="label">
                招投标
                <i style="color: #f30909; font-style: normal" v-if="biddingTotal">{{ biddingTotal }}</i>
              </span>
              <el-table :key="key" v-loading="biddingLoading" border :data="biddingList" stripe style="width: 100%" class="custom-table organizationTable">
                <el-table-column type="index" label="序号" width="50"></el-table-column>
                <el-table-column prop="publishDate" label="发布日期"></el-table-column>
                <el-table-column prop="title" label="标题">
                  <template slot-scope="{ row }">
                    <span class="table-link" @click="handleRunView(row, '招投标详情', 0)">{{ row.title }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="bidArea" label="招标地区"></el-table-column>
                <el-table-column prop="bidResult" label="投标结果"></el-table-column>
                <el-table-column prop="itemAmount" label="项目金额(元)"></el-table-column>
                <el-table-column prop="tendereeListStr" label="招标人"></el-table-column>
                <el-table-column prop="agentListStr" label="代理机构"></el-table-column>
                <el-table-column prop="noticeSource" label="公告来源"></el-table-column>
                <el-table-column label="详情" width="70">
                  <template slot-scope="{ row }">
                    <el-button type="text" @click="handleRunView(row, '招投标详情', 0)" size="small">查看</el-button>
                  </template>
                </el-table-column>
              </el-table>
              <pagination v-show="biddingTotal > 10" :total="biddingTotal" :page.sync="params.pageNum" :limit.sync="params.pageSize" @pagination="handleBiddingList()" />
            </el-tab-pane>
            <el-tab-pane label="客户" name="customerList">
              <span slot="label">
                客户
                <i style="color: #f30909; font-style: normal" v-if="customerTotal">{{ customerTotal }}</i>
              </span>
              <el-table :key="key" v-loading="customerLoading" border :data="customerList" stripe style="width: 100%" class="custom-table organizationTable">
                <el-table-column type="index" label="序号" width="50"></el-table-column>
                <el-table-column prop="companyName" label="客户名称">
                  <template slot-scope="{ row }">
                    {{ row.companyName }}
                    <template v-if="row.labels">
                      <el-tag type="warning" size="small" v-for="(item, index) in row.labels" :key="index">{{ item }}</el-tag>
                    </template>
                  </template>
                </el-table-column>
                <el-table-column prop="noticeDate" label="公告日期"></el-table-column>
                <el-table-column prop="reportDate" label="报告期"></el-table-column>
                <el-table-column prop="contractAmount" label="合同金额"></el-table-column>
                <el-table-column prop="dateSource" label="数据来源"></el-table-column>
                <el-table-column prop="area" label="地区"></el-table-column>
                <el-table-column prop="industry" label="行业"></el-table-column>
                <el-table-column label="历史明细">
                  <template slot-scope="{ row }">{{ row.historyDetail ? row.historyDetail + '次' : '' }}</template>
                </el-table-column>
              </el-table>
              <pagination v-show="customerTotal > 10" :total="customerTotal" :page.sync="params.pageNum" :limit.sync="params.pageSize" @pagination="handleCustomerList()" />
            </el-tab-pane>
            <el-tab-pane label="担保人" name="guarantorList">
              <span slot="label">
                担保人
                <i style="color: #f30909; font-style: normal" v-if="guarantorTotal">{{ guarantorTotal }}</i>
              </span>
              <el-table :key="key" v-loading="guarantorLoading" border :data="guarantorList" stripe style="width: 100%" class="custom-table organizationTable">
                <el-table-column type="index" label="序号" width="50"></el-table-column>
                <el-table-column prop="companyName" label="担保人">
                  <template slot-scope="{ row }">
                    {{ row.companyName }}
                    <template v-if="row.labels">
                      <el-tag type="warning" size="small" v-for="(item, index) in row.labels" :key="index">{{ item }}</el-tag>
                    </template>
                  </template>
                </el-table-column>
                <el-table-column prop="guaranteeAll" label="担保总次数">
                  <template slot-scope="{ row }">{{ row.guaranteeAll ? row.guaranteeAll + '次' : '' }}</template>
                </el-table-column>
                <el-table-column prop="underGuarantee" label="担保中">
                  <template slot-scope="{ row }">{{ row.underGuarantee ? row.underGuarantee + '次' : '' }}</template>
                </el-table-column>
                <el-table-column prop="terminate" label="已终止">
                  <template slot-scope="{ row }">{{ row.terminate ? row.terminate + '次' : '' }}</template>
                </el-table-column>
                <el-table-column prop="unknown" label="不详">
                  <template slot-scope="{ row }">{{ row.unknown ? row.unknown + '次' : '' }}</template>
                </el-table-column>
                <el-table-column prop="legalRepresent" label="法定代表人"></el-table-column>
                <el-table-column prop="establishDate" label="成立日期"></el-table-column>
                <el-table-column prop="companyState" label="企业状态"></el-table-column>
                <el-table-column prop="area" label="地区"></el-table-column>
                <el-table-column prop="industry" label="行业"></el-table-column>
              </el-table>
              <pagination v-show="guarantorTotal > 10" :total="guarantorTotal" :page.sync="params.pageNum" :limit.sync="params.pageSize" @pagination="handleGuarantorList()" />
            </el-tab-pane>
            <el-tab-pane label="担保企业" name="guaranteeList">
              <span slot="label">
                担保企业
                <i style="color: #f30909; font-style: normal" v-if="guaranteeTotal">{{ guaranteeTotal }}</i>
              </span>
              <el-table :key="key" v-loading="guaranteeLoading" border :data="guaranteeList" stripe style="width: 100%" class="custom-table organizationTable">
                <el-table-column type="index" label="序号" width="50"></el-table-column>
                <el-table-column prop="companyName" label="担保对象">
                  <template slot-scope="{ row }">
                    {{ row.companyName }}
                    <template v-if="row.labels">
                      <el-tag type="warning" size="small" v-for="(item, index) in row.labels" :key="index">{{ item }}</el-tag>
                    </template>
                  </template>
                </el-table-column>
                <el-table-column prop="guaranteeAll" label="担保总次数">
                  <template slot-scope="{ row }">{{ row.guaranteeAll ? row.guaranteeAll + '次' : '' }}</template>
                </el-table-column>
                <el-table-column prop="underGuarantee" label="担保中">
                  <template slot-scope="{ row }">{{ row.underGuarantee ? row.underGuarantee + '次' : '' }}</template>
                </el-table-column>
                <el-table-column prop="terminate" label="已终止">
                  <template slot-scope="{ row }">{{ row.terminate ? row.terminate + '次' : '' }}</template>
                </el-table-column>
                <el-table-column prop="unknown" label="不详">
                  <template slot-scope="{ row }">{{ row.unknown ? row.unknown + '次' : '' }}</template>
                </el-table-column>
                <el-table-column prop="legalRepresent" label="法定代表人"></el-table-column>
                <el-table-column prop="establishDate" label="成立日期"></el-table-column>
                <el-table-column prop="companyState" label="企业状态"></el-table-column>
                <el-table-column prop="area" label="地区"></el-table-column>
                <el-table-column prop="industry" label="行业"></el-table-column>
              </el-table>
              <pagination v-show="guaranteeTotal > 10" :total="guaranteeTotal" :page.sync="params.pageNum" :limit.sync="params.pageSize" @pagination="handleGuaranteeList()" />
            </el-tab-pane>
            <el-tab-pane label="信托融资担保" name="guaranteeOTList">
              <span slot="label">
                信托融资担保
                <i style="color: #f30909; font-style: normal" v-if="guaranteeOTTotal">{{ guaranteeOTTotal }}</i>
              </span>
              <el-table :key="key" v-loading="guaranteeOTLoading" border :data="guaranteeOTList" stripe style="width: 100%" class="custom-table organizationTable">
                <el-table-column type="index" label="序号" width="50"></el-table-column>
                <el-table-column prop="actualItname" label="担保方"></el-table-column>
                <el-table-column prop="guaranteed_name" label="被担保方"></el-table-column>
                <el-table-column prop="amountStr" label="担保规模"></el-table-column>
                <el-table-column label="担保期限">
                  <template slot-scope="{ row }">{{ (row.bdate ? row.bdate : '-') + '至' + (row.edate ? row.edate : '-') }}</template>
                </el-table-column>
              </el-table>
              <pagination v-show="guaranteeOTTotal > 10" :total="guaranteeOTTotal" :page.sync="params.pageNum" :limit.sync="params.pageSize" @pagination="handleGuaranteeOTList()" />
            </el-tab-pane>
            <el-tab-pane label="银行借款担保" name="provideList">
              <span slot="label">
                银行借款担保
                <i style="color: #f30909; font-style: normal" v-if="provideTotal">{{ provideTotal }}</i>
              </span>
              <el-table :key="key" v-loading="provideLoading" border :data="provideList" stripe style="width: 100%" class="custom-table organizationTable">
                <el-table-column type="index" label="序号" width="50"></el-table-column>
                <el-table-column prop="actualItname" label="担保方"></el-table-column>
                <el-table-column prop="guaranteed_name" label="被担保方"></el-table-column>
                <el-table-column prop="amountStr" label="担保规模"></el-table-column>
                <el-table-column label="担保期限">
                  <template slot-scope="{ row }">{{ (row.bdate ? row.bdate : '-') + '至' + (row.edate ? row.edate : '-') }}</template>
                </el-table-column>
              </el-table>
              <pagination v-show="provideTotal > 10" :total="provideTotal" :page.sync="params.pageNum" :limit.sync="params.pageSize" @pagination="handleProvideList()" />
            </el-tab-pane>
            <el-tab-pane label="获得债券担保" name="guaranteeZQList">
              <span slot="label">
                获得债券担保
                <i style="color: #f30909; font-style: normal" v-if="guaranteeZQTotal">{{ guaranteeZQTotal }}</i>
              </span>
              <el-table :key="key" v-loading="guaranteeZQLoading" border :data="guaranteeZQList" stripe style="width: 100%" class="custom-table organizationTable">
                <el-table-column type="index" label="序号" width="50"></el-table-column>
                <el-table-column label="债券简称（代码）">
                  <template slot-scope="{ row }">{{ row.bondName + `(${row.symbol})` }}</template>
                </el-table-column>
                <el-table-column prop="bondType" label="债券类型"></el-table-column>
                <el-table-column label="担保规模">
                  <template slot-scope="{ row }">{{ row.guarantorInfo ? row.guarantorInfo.map(item => item.guaranteeScale).toString() : '' }}</template>
                </el-table-column>
                <el-table-column label="担保方式">
                  <template slot-scope="{ row }">{{ row.guarantorInfo ? row.guarantorInfo.map(item => item.guaranteeMethod).toString() : '' }}</template>
                </el-table-column>
                <el-table-column label="担保期限">
                  <template slot-scope="{ row }">{{ (row.guarantorInfo ? row.guarantorInfo.map(item => item.guaranteeStartTime).toString() : '-') + '至' + (row.guarantorInfo ? row.guarantorInfo.map(item => item.guaranteeEndTime).toString() : '-') }}</template>
                </el-table-column>
                <el-table-column label="保证方式">
                  <template slot-scope="{ row }">{{ row.guarantorInfo ? row.guarantorInfo.map(item => item.ensureMethod).toString() : '' }}</template>
                </el-table-column>
                <el-table-column prop="bondMarket" label="市场"></el-table-column>
                <el-table-column prop="bondRating" label="债项评级"></el-table-column>
                <el-table-column prop="guarantorCode" label="担保人"></el-table-column>
                <el-table-column label="企业性质">
                  <template slot-scope="{ row }">{{ row.guarantorInfo ? row.guarantorInfo.map(item => item.guarantorEnterpriseNature).toString() : '' }}</template>
                </el-table-column>
                <el-table-column label="主体评级">
                  <template slot-scope="{ row }">{{ row.guarantorInfo ? row.guarantorInfo.map(item => item.guarantorMainRating).toString() : '' }}</template>
                </el-table-column>
              </el-table>
              <pagination v-show="guaranteeZQTotal > 10" :total="guaranteeZQTotal" :page.sync="params.pageNum" :limit.sync="params.pageSize" @pagination="handleGuaranteeZQList()" />
            </el-tab-pane>
            <el-tab-pane label="提供债券担保" name="provideZQList">
              <span slot="label">
                提供债券担保
                <i style="color: #f30909; font-style: normal" v-if="provideZQTotal">{{ provideZQTotal }}</i>
              </span>
              <el-table :key="key" v-loading="provideZQLoading" border :data="provideZQList" stripe style="width: 100%" class="custom-table organizationTable">
                <el-table-column type="index" label="序号" width="50"></el-table-column>
                <el-table-column label="债券简称（代码）">
                  <template slot-scope="{ row }">{{ row.bondName + `(${row.symbol})` }}</template>
                </el-table-column>
                <el-table-column prop="bondType" label="债券类型"></el-table-column>
                <el-table-column prop="guaranteeScale" label="担保规模"></el-table-column>
                <el-table-column prop="guaranteeMethod" label="担保方式"></el-table-column>
                <el-table-column label="担保期限">
                  <template slot-scope="{ row }">{{ (row.guaranteeStartTime ? row.guaranteeStartTime : '-') + '至' + (row.guaranteeEndTime ? row.guaranteeEndTime : '-') }}</template>
                </el-table-column>
                <el-table-column prop="ensureMethod" label="保证方式"></el-table-column>
                <el-table-column prop="bondMarket" label="市场"></el-table-column>
                <el-table-column prop="bondRating" label="债项评级"></el-table-column>
                <el-table-column prop="issuer" label="发债人"></el-table-column>
                <el-table-column prop="issuerEnterpriseNature" label="企业性质"></el-table-column>
                <el-table-column prop="issuerMainRating" label="主体评级"></el-table-column>
                <el-table-column prop="senWanIndustry" label="行业"></el-table-column>
              </el-table>
              <pagination v-show="provideZQTotal > 10" :total="provideZQTotal" :page.sync="params.pageNum" :limit.sync="params.pageSize" @pagination="handleProvideZQList()" />
            </el-tab-pane>
            <el-tab-pane label="土地信息" name="landList">
              <span slot="label">
                土地信息
                <i style="color: #f30909; font-style: normal" v-if="landTotal">{{ landTotal }}</i>
              </span>
              <el-table :key="key" v-loading="landLoading" border :data="landList" stripe style="width: 100%" class="custom-table organizationTable">
                <el-table-column type="index" label="序号" width="50"></el-table-column>
                <el-table-column prop="location" label="土地位置"></el-table-column>
                <el-table-column label="发布/签订日期">
                  <template slot-scope="{ row }">{{ row.agreedSubmitDate || row.signedContractDate }}</template>
                </el-table-column>
                <el-table-column prop="landNumber" label="宗地编号"></el-table-column>
                <el-table-column prop="area" label="土地面积(㎡)"></el-table-column>
                <el-table-column prop="price" label="成交价格(万元)"></el-table-column>
                <el-table-column prop="landSupply" label="供地方式"></el-table-column>
                <el-table-column prop="usage" label="土地用途"></el-table-column>
                <el-table-column prop="district" label="行政区"></el-table-column>
                <el-table-column prop="institutions" label="发布/批准单位"></el-table-column>
                <el-table-column prop="stage" label="所处阶段"></el-table-column>
              </el-table>
              <pagination v-show="landTotal > 10" :total="landTotal" :page.sync="params.pageNum" :limit.sync="params.pageSize" @pagination="handleLandList()" />
            </el-tab-pane>
            <el-tab-pane label="榜单资质" name="listQualification">
              <span slot="label">
                榜单资质
                <i style="color: #f30909; font-style: normal" v-if="listQualificationTotal">{{ listQualificationTotal }}</i>
              </span>
              <el-table :key="key" v-loading="listQualificationLoading" border :data="listQualification" stripe style="width: 100%" class="custom-table organizationTable">
                <el-table-column type="index" label="序号" width="50"></el-table-column>
                <el-table-column prop="declareDate" label="公布日期"></el-table-column>
                <el-table-column prop="year" label="年度"></el-table-column>
                <el-table-column prop="tagName" label="榜单排名"></el-table-column>
                <el-table-column prop="ranking" label="排名"></el-table-column>
                <el-table-column prop="appraiseOrg" label="认定单位"></el-table-column>
                <el-table-column prop="areaLevel" label="认定范围"></el-table-column>
                <el-table-column prop="source" label="数据来源"></el-table-column>
              </el-table>
              <pagination v-show="listQualificationTotal > 10" :total="listQualificationTotal" :page.sync="params.pageNum" :limit.sync="params.pageSize" @pagination="handleListQualification()" />
            </el-tab-pane>
            <el-tab-pane label="作品著作权" name="paperList">
              <span slot="label">
                作品著作权
                <i style="color: #f30909; font-style: normal" v-if="paperTotal">{{ paperTotal }}</i>
              </span>
              <el-table :key="key" v-loading="paperLoading" border :data="paperList" stripe style="width: 100%" class="custom-table organizationTable">
                <el-table-column type="index" label="序号" width="50"></el-table-column>
                <el-table-column prop="registerDateStr" label="登记日期"></el-table-column>
                <el-table-column prop="rightName" label="作品名称"></el-table-column>
                <el-table-column prop="rightType" label="作品类型"></el-table-column>
                <el-table-column prop="registerCode" label="登记号"></el-table-column>
                <el-table-column prop="createdDateStr" label="创作完成日期"></el-table-column>
                <el-table-column prop="publishDateStr" label="首次发表日期"></el-table-column>
                <el-table-column label="详情" width="70">
                  <template slot-scope="{ row }">
                    <el-button type="text" @click="handleRunView(row, '作品著作权详情', 1)" size="small">查看</el-button>
                  </template>
                </el-table-column>
              </el-table>
              <pagination v-show="paperTotal > 10" :total="paperTotal" :page.sync="params.pageNum" :limit.sync="params.pageSize" @pagination="handlePaperList()" />
            </el-tab-pane>
            <el-tab-pane label="财产线索" name="propertyList">
              <span slot="label">
                财产线索
                <i style="color: #f30909; font-style: normal" v-if="propertyTotal">{{ propertyTotal }}</i>
              </span>
              <el-table :key="key" v-loading="propertyLoading" border :data="propertyList" stripe style="width: 100%" class="custom-table organizationTable">
                <el-table-column type="index" label="序号" width="50"></el-table-column>
                <el-table-column prop="discoveryDate" label="发现日期"></el-table-column>
                <el-table-column prop="propertyFlowValue" label="财产流向"></el-table-column>
                <el-table-column prop="clueType" label="线索类型"></el-table-column>
                <el-table-column prop="clueContent" label="线索内容"></el-table-column>
                <el-table-column prop="estimateValue" label="预估价值(万元)"></el-table-column>
                <el-table-column prop="propertyType" label="财产类型"></el-table-column>
                <el-table-column prop="clueUnscramble" label="线索解读"></el-table-column>
              </el-table>
            </el-tab-pane>
            <el-tab-pane label="专利" name="rightList">
              <span slot="label">
                专利
                <i style="color: #f30909; font-style: normal" v-if="rightTotal">{{ rightTotal }}</i>
              </span>
              <el-table :key="key" v-loading="rightLoading" border :data="rightList" stripe style="width: 100%" class="custom-table organizationTable">
                <el-table-column type="index" label="序号" width="50"></el-table-column>
                <el-table-column prop="registerDateStr" label="申请日期"></el-table-column>
                <el-table-column prop="rightName" label="专利名称"></el-table-column>
                <el-table-column prop="rightType" label="专利类型"></el-table-column>
                <el-table-column prop="rightStatus" label="专利状态"></el-table-column>
                <el-table-column prop="registerCode" label="申请号"></el-table-column>
                <el-table-column prop="publishCode" label="公布(公告)号"></el-table-column>
                <el-table-column prop="publishDateStr" label="公布(公告)日"></el-table-column>
                <el-table-column prop="inventionMans" label="发明人">
                  <template slot-scope="{ row }">
                    {{ row.inventionMans ? row.inventionMans.map(ite => ite.name).join('、') : '' }}
                  </template>
                </el-table-column>
              </el-table>
              <pagination v-show="rightTotal > 10" :total="rightTotal" :page.sync="params.pageNum" :limit.sync="params.pageSize" @pagination="handleRightList()" />
            </el-tab-pane>
            <el-tab-pane label="软件著作权" name="softList">
              <span slot="label">
                软件著作权
                <i style="color: #f30909; font-style: normal" v-if="softTotal">{{ softTotal }}</i>
              </span>
              <el-table :key="key" v-loading="softLoading" border :data="softList" stripe style="width: 100%" class="custom-table organizationTable">
                <el-table-column type="index" label="序号" width="50"></el-table-column>
                <el-table-column prop="registerDateStr" label="登记日期"></el-table-column>
                <el-table-column prop="rightName" label="软件名称"></el-table-column>
                <el-table-column prop="alias" label="软件简称"></el-table-column>
                <el-table-column prop="version" label="版本号"></el-table-column>
                <el-table-column prop="registerCode" label="登记号"></el-table-column>
                <el-table-column prop="publishDateStr" label="首次发表日期"></el-table-column>
                <el-table-column label="详情" width="70">
                  <template slot-scope="{ row }">
                    <el-button type="text" @click="handleRunView(row, '软件著作权详情', 2)" size="small">查看</el-button>
                  </template>
                </el-table-column>
              </el-table>
              <pagination v-show="softTotal > 10" :total="softTotal" :page.sync="params.pageNum" :limit.sync="params.pageSize" @pagination="handleSoftList()" />
            </el-tab-pane>
            <el-tab-pane label="供应商" name="supplierList">
              <span slot="label">
                供应商
                <i style="color: #f30909; font-style: normal" v-if="supplierTotal">{{ supplierTotal }}</i>
              </span>
              <el-table :key="key" v-loading="supplierLoading" border :data="supplierList" stripe style="width: 100%" class="custom-table organizationTable">
                <el-table-column type="index" label="序号" width="50"></el-table-column>
                <el-table-column prop="companyName" label="供应商名称"></el-table-column>
                <el-table-column prop="noticeDate" label="公告日期"></el-table-column>
                <el-table-column prop="reportDate" label="报告期"></el-table-column>
                <el-table-column prop="contractAmount" label="合同金额"></el-table-column>
                <el-table-column prop="dateSource" label="数据来源"></el-table-column>
                <el-table-column prop="area" label="地区"></el-table-column>
                <el-table-column prop="industry" label="行业"></el-table-column>
                <el-table-column prop="historyDetail" label="历史明细">
                  <template slot-scope="{ row }">{{ row.historyDetail ? row.historyDetail + '次' : '' }}</template>
                </el-table-column>
              </el-table>
              <pagination v-show="supplierTotal > 10" :total="supplierTotal" :page.sync="params.pageNum" :limit.sync="params.pageSize" @pagination="handleSupplierList()" />
            </el-tab-pane>
          </el-tabs>
          <el-tabs class="detailTab smallTab" v-model="activeFiveName" @tab-click="handleFiveSwitch" v-if="activeTwoName === 'punish'">
            <el-tab-pane label="行政处罚" name="administrativeList">
              <span slot="label">
                行政处罚
                <i style="color: #f30909; font-style: normal" v-if="administrativeTotal">{{ administrativeTotal }}</i>
              </span>
              <el-table :key="key" v-loading="administrativeLoading" border :data="administrativeList" stripe style="width: 100%" class="custom-table organizationTable">
                <el-table-column type="index" label="序号" width="50"></el-table-column>
                <el-table-column prop="disclosuredate" label="披露日期">
                  <template slot-scope="{ row }">{{ parseTime(row.disclosuredate, '{y}-{m}-{d}') }}</template>
                </el-table-column>
                <el-table-column prop="punishdate" label="处罚日期">
                  <template slot-scope="{ row }">{{ parseTime(row.punishdate, '{y}-{m}-{d}') }}</template>
                </el-table-column>
                <el-table-column prop="punishtype" label="处罚类型"></el-table-column>
                <el-table-column prop="violationreason" label="违规原因"></el-table-column>
                <el-table-column prop="docnumber" label="文号"></el-table-column>
                <el-table-column prop="handler" label="处理人"></el-table-column>
                <el-table-column prop="legalbasis" label="法律依据"></el-table-column>
              </el-table>
              <pagination v-show="administrativeTotal > 10" :total="administrativeTotal" :page.sync="params.pageNum" :limit.sync="params.pageSize" @pagination="handleAdministrativeList()" />
            </el-tab-pane>
            <el-tab-pane label="行政监管措施" name="regulatoryList">
              <span slot="label">
                行政监管措施
                <i style="color: #f30909; font-style: normal" v-if="regulatoryTotal">{{ regulatoryTotal }}</i>
              </span>
              <el-table :key="key" v-loading="regulatoryLoading" border :data="regulatoryList" stripe style="width: 100%" class="custom-table organizationTable">
                <el-table-column type="index" label="序号" width="50"></el-table-column>
                <el-table-column prop="disclosuredate" label="披露日期">
                  <template slot-scope="{ row }">{{ parseTime(row.disclosuredate, '{y}-{m}-{d}') }}</template>
                </el-table-column>
                <el-table-column prop="punishdate" label="处罚日期">
                  <template slot-scope="{ row }">{{ parseTime(row.punishdate, '{y}-{m}-{d}') }}</template>
                </el-table-column>
                <el-table-column prop="punishtype" label="处罚类型"></el-table-column>
                <el-table-column prop="violationreason" label="违规原因"></el-table-column>
                <el-table-column prop="docnumber" label="文号"></el-table-column>
                <el-table-column prop="handler" label="处理人"></el-table-column>
                <el-table-column prop="legalbasis" label="法律依据"></el-table-column>
              </el-table>
              <pagination v-show="regulatoryTotal > 10" :total="regulatoryTotal" :page.sync="params.pageNum" :limit.sync="params.pageSize" @pagination="handleRegulatoryList()" />
            </el-tab-pane>
            <el-tab-pane label="纪律处分" name="disciplinaryList">
              <span slot="label">
                纪律处分
                <i style="color: #f30909; font-style: normal" v-if="disciplinaryTotal">{{ disciplinaryTotal }}</i>
              </span>
              <el-table :key="key" v-loading="disciplinaryLoading" border :data="disciplinaryList" stripe style="width: 100%" class="custom-table organizationTable">
                <el-table-column type="index" label="序号" width="50"></el-table-column>
                <el-table-column prop="disclosuredate" label="披露日期">
                  <template slot-scope="{ row }">{{ parseTime(row.disclosuredate, '{y}-{m}-{d}') }}</template>
                </el-table-column>
                <el-table-column prop="punishdate" label="处罚日期">
                  <template slot-scope="{ row }">{{ parseTime(row.punishdate, '{y}-{m}-{d}') }}</template>
                </el-table-column>
                <el-table-column prop="punishtype" label="处罚类型"></el-table-column>
                <el-table-column prop="violationreason" label="违规原因"></el-table-column>
                <el-table-column prop="docnumber" label="文号"></el-table-column>
                <el-table-column prop="handler" label="处理人"></el-table-column>
                <el-table-column prop="legalbasis" label="法律依据"></el-table-column>
              </el-table>
              <pagination v-show="disciplinaryTotal > 10" :total="disciplinaryTotal" :page.sync="params.pageNum" :limit.sync="params.pageSize" @pagination="handleDisciplinaryList()" />
            </el-tab-pane>
            <el-tab-pane label="金融监管" name="financialList">
              <span slot="label">
                金融监管
                <i style="color: #f30909; font-style: normal" v-if="financialTotal">{{ financialTotal }}</i>
              </span>
              <el-table :key="key" v-loading="financialLoading" border :data="financialList" stripe style="width: 100%" class="custom-table organizationTable">
                <el-table-column type="index" label="序号" width="50"></el-table-column>
                <el-table-column prop="disclosuredate" label="披露日期">
                  <template slot-scope="{ row }">{{ parseTime(row.disclosuredate, '{y}-{m}-{d}') }}</template>
                </el-table-column>
                <el-table-column prop="punishdate" label="处罚日期">
                  <template slot-scope="{ row }">{{ parseTime(row.punishdate, '{y}-{m}-{d}') }}</template>
                </el-table-column>
                <el-table-column prop="punishtype" label="处罚类型"></el-table-column>
                <el-table-column prop="violationreason" label="违规原因"></el-table-column>
                <el-table-column prop="docnumber" label="文号"></el-table-column>
                <el-table-column prop="handler" label="处理人"></el-table-column>
                <el-table-column prop="legalbasis" label="法律依据"></el-table-column>
              </el-table>
              <pagination v-show="financialTotal > 10" :total="financialTotal" :page.sync="params.pageNum" :limit.sync="params.pageSize" @pagination="handleFinancialList()" />
            </el-tab-pane>
            <el-tab-pane label="自律监管措施" name="selfRegulatoryList">
              <span slot="label">
                自律监管措施
                <i style="color: #f30909; font-style: normal" v-if="selfRegulatoryTotal">{{ selfRegulatoryTotal }}</i>
              </span>
              <el-table :key="key" v-loading="selfRegulatoryLoading" border :data="selfRegulatoryList" stripe style="width: 100%" class="custom-table organizationTable">
                <el-table-column type="index" label="序号" width="50"></el-table-column>
                <el-table-column prop="disclosuredate" label="披露日期">
                  <template slot-scope="{ row }">{{ parseTime(row.disclosuredate, '{y}-{m}-{d}') }}</template>
                </el-table-column>
                <el-table-column prop="punishdate" label="处罚日期">
                  <template slot-scope="{ row }">{{ parseTime(row.punishdate, '{y}-{m}-{d}') }}</template>
                </el-table-column>
                <el-table-column prop="punishtype" label="处罚类型"></el-table-column>
                <el-table-column prop="violationreason" label="违规原因"></el-table-column>
                <el-table-column prop="docnumber" label="文号"></el-table-column>
                <el-table-column prop="handler" label="处理人"></el-table-column>
                <el-table-column prop="legalbasis" label="法律依据"></el-table-column>
              </el-table>
              <pagination v-show="selfRegulatoryTotal > 10" :total="selfRegulatoryTotal" :page.sync="params.pageNum" :limit.sync="params.pageSize" @pagination="handleSelfRegulatoryList()" />
            </el-tab-pane>
          </el-tabs>
          <el-tabs class="detailTab smallTab" v-model="activeSixName" @tab-click="handleSixSwitch" v-if="activeTwoName === 'credit'">
            <el-tab-pane label="纳税信用等级" name="ratingTaxList">
              <span slot="label">
                纳税信用等级
                <i style="color: #f30909; font-style: normal" v-if="ratingTaxTotal">{{ ratingTaxTotal }}</i>
              </span>
              <el-table :key="key" v-loading="ratingTaxLoading" border :data="ratingTaxList" stripe style="width: 100%" class="custom-table organizationTable">
                <el-table-column type="index" label="序号" width="50"></el-table-column>
                <el-table-column prop="ratingDate" label="评价年度"></el-table-column>
                <el-table-column prop="rating" label="纳税信用等级"></el-table-column>
                <el-table-column prop="ratingOrg" label="评价单位"></el-table-column>
              </el-table>
            </el-tab-pane>
            <el-tab-pane label="其他领域信评" name="ratingOTList">
              <span slot="label">
                其他领域信评
                <i style="color: #f30909; font-style: normal" v-if="ratingOTTotal">{{ ratingOTTotal }}</i>
              </span>
              <el-table :key="key" v-loading="ratingOTLoading" border :data="ratingOTList" stripe style="width: 100%" class="custom-table organizationTable">
                <el-table-column type="index" label="序号" width="50"></el-table-column>
                <el-table-column prop="declareDate" label="披露日期"></el-table-column>
                <el-table-column prop="ratingDate" label="评价年度"></el-table-column>
                <el-table-column prop="ratingType" label="评价类型"></el-table-column>
                <el-table-column prop="rating" label="评价等级"></el-table-column>
                <el-table-column prop="ratingOrg" label="评价单位"></el-table-column>
              </el-table>
              <pagination v-show="ratingOTTotal > 10" :total="ratingOTTotal" :page.sync="params.pageNum" :limit.sync="params.pageSize" @pagination="handleRatingOTList()" />
            </el-tab-pane>
          </el-tabs>
          <div class="newsTable" v-if="activeTwoName === 'news'">
            <el-table :key="key" v-loading="newsLoading" border :data="newsList" stripe style="width: 100%" class="custom-table organizationTable">
              <el-table-column prop="date" label="日期">
                <template slot-scope="{ row }">{{ newDateFormat(row.date) }}</template>
              </el-table-column>
              <el-table-column prop="title" label="标题">
                <template slot-scope="{ row }">
                  <span class="table-link" @click="handleRunView(row, '新闻公告详情', 3)">{{ row.title }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="source" label="来源"></el-table-column>
              <el-table-column label="详情">
                <template slot-scope="{ row }">
                  <el-button type="text" @click="handleRunView(row, '新闻公告详情', 3)" size="small">查看</el-button>
                </template>
              </el-table-column>
            </el-table>
            <pagination v-show="newsTotal > 10" :total="newsTotal" :page.sync="params.pageNum" :limit.sync="params.pageSize" @pagination="handleNewsList()" />
          </div>
        </div>
      </template>
      <el-empty description="数据采集中。。。" v-if="!hasMore" />
    </div>

    <!--司法详情-->
    <company-judicial ref="companyJudicial" />
    <!--企业经营详情-->
    <company-run ref="companyRun" />
  </div>
</template>
<script>
import { administrativeList, annualList, biddingList, branchList, changeList, chattelList, childList, courtList, customerList, deliveryList, disciplinaryList, dishonestList, executedList, filingList, finalList, financialList, guaranteeList, guaranteeOTList, guaranteeZQList, guarantorList, holderList, infoList, landList, legalList, list, listQualification, login, managerList, newsList, paperList, penetrationList, propertyList, provideList, provideZQList, ratingOTList, ratingTaxList, regulatoryList, restrainingList, rightList, selfRegulatoryList, softList, supplierList, suspiciousList } from '@/api/guirong'
import Cookies from 'js-cookie'
import { parseTime } from '@/utils/ruoyi'
import companyJudicial from '@/views/payment/view/judicial'
import companyRun from '@/views/payment/view/run'
import { updateWarningInfo } from '@/api'

export default {
  props: {
    show: {
      type: Boolean,
      default: false
    }
  },
  components: { companyJudicial, companyRun },
  data() {
    return {
      loading: true,
      hasMore: false,
      activeOneName: 'manager', // 组织架构、股份构成切换
      activeTwoName: 'judicial',
      activeThreeName: 'courtList',
      activeFourName: 'biddingList',
      activeFiveName: 'administrativeList',
      activeSixName: 'ratingTaxList',
      key: 1,
      // 预警通信息
      createTime: '',
      companyName: '',
      taxId: '',
      token: '',
      basicParams: { pageNum: 1, pageSize: 10, searchValue: '' },
      params: { pageNum: 1, pageSize: 10, searchValue: '' },
      holderList: [], // 股东信息
      holderLoading: true,
      holderTotal: 0,
      annualList: [], // 企业年报
      annualLoading: true,
      annualTotal: 0,
      branchList: [], // 分支机构
      branchLoading: true,
      branchTotal: 0,
      changeList: [], // 工商变更
      changeLoading: true,
      changeTotal: 0,
      chattelList: [], // 动产抵押
      chattelLoading: true,
      chattelTotal: 0,
      childList: [], // 控股子公司
      childLoading: true,
      childTotal: 0,
      infoList: [], // 工商信息
      infoLoading: true,
      infoTotal: 0,
      penetrationList: [], // 股权穿透
      penetrationLoading: true,
      penetrationTotal: 0,
      managerList: [], // 高管信息
      managerLoading: true,
      managerTotal: 0,
      suspiciousList: [], // 疑似关系
      suspiciousLoading: true,
      suspiciousTotal: 0,
      administrativeList: [], // 行政处罚
      administrativeLoading: true,
      administrativeTotal: 0,
      regulatoryList: [], // 行政监管措施
      regulatoryLoading: true,
      regulatoryTotal: 0,
      disciplinaryList: [], // 纪律处分
      disciplinaryLoading: true,
      disciplinaryTotal: 0,
      financialList: [], // 金融监管
      financialLoading: true,
      financialTotal: 0,
      selfRegulatoryList: [], // 自律监管措施
      selfRegulatoryLoading: true,
      selfRegulatoryTotal: 0,
      biddingList: [], // 招投标
      biddingLoading: true,
      biddingTotal: 0,
      customerList: [], // 客户
      customerLoading: true,
      customerTotal: 0,
      guaranteeList: [], // 担保企业
      guaranteeLoading: true,
      guaranteeTotal: 0,
      guaranteeOTList: [], // 信托融资担保
      guaranteeOTLoading: true,
      guaranteeOTTotal: 0,
      guaranteeZQList: [], // 债券担保
      guaranteeZQLoading: true,
      guaranteeZQTotal: 0,
      guarantorList: [], // 担保人
      guarantorLoading: true,
      guarantorTotal: 0,
      landList: [], // 土地信息
      landLoading: true,
      landTotal: 0,
      listQualification: [], // 榜单资质
      listQualificationLoading: true,
      listQualificationTotal: 0,
      paperList: [], // 作品著作权
      paperLoading: true,
      paperTotal: 0,
      propertyList: [], // 财产线索
      propertyLoading: true,
      propertyTotal: 0,
      provideList: [], // 银行借款担保
      provideLoading: true,
      provideTotal: 0,
      provideZQList: [], // 提供债券担保
      provideZQLoading: true,
      provideZQTotal: 0,
      rightList: [], // 专利
      rightLoading: true,
      rightTotal: 0,
      softList: [], // 软件著作权
      softLoading: true,
      softTotal: 0,
      supplierList: [], // 供应商
      supplierLoading: true,
      supplierTotal: 0,
      courtList: [], // 开庭公告
      courtLoading: true,
      courtTotal: 0,
      deliveryList: [], // 送达公告
      deliveryLoading: true,
      deliveryTotal: 0,
      dishonestList: [], // 失信被执行人
      dishonestLoading: true,
      dishonestTotal: 0,
      executedList: [], // 被执行人
      executedLoading: true,
      executedTotal: 0,
      filingList: [], // 立案信息
      filingLoading: true,
      filingTotal: 0,
      finalList: [], // 终本案件
      finalLoading: true,
      finalTotal: 0,
      legalList: [], // 法院公告
      legalLoading: true,
      legalTotal: 0,
      restrainingList: [], // 限制高消费
      restrainingLoading: true,
      restrainingTotal: 0,
      newsList: [], // 新闻公告研报
      newsLoading: true,
      newsTotal: 0,
      ratingOTList: [], // 其他领域信评
      ratingOTLoading: true,
      ratingOTTotal: 0,
      ratingTaxList: [], // 纳税信用等级
      ratingTaxLoading: true,
      ratingTaxTotal: 0
    }
  },
  methods: {
    parseTime,

    getInfo(obj = {}, type = undefined) {
      if (!obj.companyCreditCode) {
        this.hasMore = false
        this.loading = false
        return
      }
      this.taxId = obj.companyCreditCode
      this.companyName = obj.companyName
      const token = Cookies.get('guirong-Token')
      if (token) {
        this.token = token
        this.handleList()
      } else {
        this.handleLogin()
      }
    },
    //刷新信息
    handleRefresh() {
      const query = { company_name: this.companyName, tax_id: this.taxId }
      updateWarningInfo(query).then(res => {
        if (res.status === 200) this.$message.success('更新指令已收到，请稍后刷新页面查看')
      })
    },
    // 登录
    handleLogin() {
      const data = { username: 'zyk', password: 'SS@ztb0621' }
      login(data).then(res => {
        const { code, msg, token } = res
        if (code === 200) {
          Cookies.set('guirong-Token', token, { expires: 1 / 48 })
          this.token = token
          this.handleList()
        } else this.$message.error(msg)
      })
    },
    // 重新登录
    // prettier-ignore
    handleReLogin() {
      return new Promise((resolve, reject) => {
        Cookies.remove('guirong-Token')
        const data = { username: 'zyk', password: 'SS@ztb0621' }
        login(data).then(res => {
          const { code, msg, token } = res
          if (code === 200) {
            Cookies.set('guirong-Token', token, { expires: 1 / 48 })
            this.token = token
          } else this.$message.error(msg)
          resolve()
        }).catch(err => {
          reject(err)
        })
      })
    },
    // 查询企业列表
    handleList() {
      list({ taxId: this.taxId }, this.token).then(res => {
        const { code, msg, rows } = res
        if (code === 200) {
          if (!!rows.length && rows[0].companyFid) {
            const obj = rows[0]
            this.createTime = obj?.updateTime || obj?.createTime || ''
            this.params.searchValue = obj?.companyFid || ''
            this.basicParams.searchValue = obj?.companyFid || ''
            this.handleInfo()
            this.hasMore = true
          } else {
            this.handleRefresh()
            this.$nextTick(() => {
              this.hasMore = false
            })
          }
          this.loading = false
        } else if (code === 401) {
          this.handleReLogin().then(() => {
            this.handleList()
          })
        } else this.$message.error(msg)
      })
    },
    // 查询企业详情
    async handleInfo() {
      this.activeOneName = 'manager'
      this.activeTwoName = 'judicial'
      this.activeThreeName = 'courtList'
      this.managerList = []
      this.courtList = []
      try {
        // 查询工商信息
        await this.handleInfoList()
        // 查询高管信息
        await this.handleManagerList()
        // 查询开庭公告
        await this.handleCourtList()
        // 查询送达公告
        await this.handleDeliveryList()
        // 查询失信被执行人
        await this.handleDishonestList()
        // 查询被执行人
        await this.handleExecutedList()
        // 查询立案信息
        await this.handleFilingList()
        // 查询终本案件
        await this.handleFinalList()
        // 查询法院公告
        await this.handleLegalList()
        // 查询限制高消费
        await this.handleRestrainingList()
      } catch (error) {
        console.log(error)
      }
    },
    // 查询股东信息
    async handleHolderList() {
      this.holderLoading = true
      try {
        const holderListRes = await holderList(this.basicParams, this.token)
        if (holderListRes.code === 0) {
          this.holderList = holderListRes.rows || []
          this.holderTotal = holderListRes.total
          this.key = Math.random()
        } else if (holderListRes.code === 401) {
          this.handleReLogin().then(() => {
            this.handleHolderList()
          })
        }
      } finally {
        this.holderLoading = false
      }
    },
    // 查询企业年报
    async handleAnnualList() {
      this.annualLoading = true
      try {
        const annualListRes = await annualList(this.params, this.token)
        if (annualListRes.code === 0) {
          this.annualList = annualListRes.rows || []
          this.annualTotal = annualListRes.total
          this.key = Math.random()
        } else if (annualListRes.code === 401) {
          this.handleReLogin().then(() => {
            this.handleAnnualList()
          })
        }
      } finally {
        this.annualLoading = false120
      }
    },
    // 查询分支机构
    async handleBranchList() {
      this.branchLoading = true
      try {
        const branchListRes = await branchList(this.basicParams, this.token)
        if (branchListRes.code === 0) {
          this.branchList = branchListRes.rows || []
          this.branchTotal = branchListRes.total
          this.key = Math.random()
        } else if (branchListRes.code === 401) {
          this.handleReLogin().then(() => {
            this.handleBranchList()
          })
        }
      } finally {
        this.branchLoading = false
      }
    },
    // 查询工商变更
    async handleChangeList() {
      this.changeLoading = true
      try {
        const changeListRes = await changeList(this.basicParams, this.token)
        if (changeListRes.code === 0) {
          if (!!changeListRes.rows.length) {
            const list = changeListRes.rows[0].changeDict || []
            list.map(item => {
              item.afterMore = item.afterChange.length > 120
              item.beforeMore = item.beforeChange.length > 120
            })
            this.changeList = list
          }
          this.changeTotal = changeListRes.total
          this.key = Math.random()
        } else if (changeListRes.code === 401) {
          this.handleReLogin().then(() => {
            this.handleChangeList()
          })
        }
      } finally {
        this.changeLoading = false
      }
    },
    // 查询动产抵押
    async handleChattelList() {
      this.chattelLoading = true
      try {
        const chattelListRes = await chattelList(this.params, this.token)
        if (chattelListRes.code === 0) {
          this.chattelList = chattelListRes.rows || []
          this.chattelTotal = chattelListRes.total
          this.key = Math.random()
        } else if (chattelListRes.code === 401) {
          this.handleReLogin().then(() => {
            this.handleChattelList()
          })
        }
      } finally {
        this.chattelLoading = false
      }
    },
    // 查询控股子公司
    async handleChildList() {
      this.childLoading = true
      try {
        const childListRes = await childList(this.basicParams, this.token)
        if (childListRes.code === 0) {
          this.childList = childListRes.rows || []
          this.childTotal = childListRes.total
          this.key = Math.random()
        } else if (childListRes.code === 401) {
          this.handleReLogin().then(() => {
            this.handleChildList()
          })
        }
      } finally {
        this.childLoading = false
      }
    },
    // 查询工商信息
    async handleInfoList() {
      this.infoLoading = true
      try {
        const infoListRes = await infoList(this.params, this.token)
        if (infoListRes.code === 0) {
          this.infoList = infoListRes.rows || []
          this.infoTotal = infoListRes.total
          this.key = Math.random()
          const obj = this.infoList[0]
          this.createTime = (obj?.updateTime && this.parseTime(obj?.updateTime)) || ''
        } else if (infoListRes.code === 401) {
          this.handleReLogin().then(() => {
            this.handleInfoList()
          })
        }
      } finally {
        this.infoLoading = false
      }
    },
    // 查询股权穿透
    async handlePenetrationList() {
      this.penetrationLoading = true
      try {
        const penetrationListRes = await penetrationList(this.params, this.token)
        if (penetrationListRes.code === 0) {
          this.penetrationList = penetrationListRes.rows || []
          this.penetrationTotal = penetrationListRes.total
          this.key = Math.random()
        } else if (penetrationListRes.code === 401) {
          this.handleReLogin().then(() => {
            this.handlePenetrationList()
          })
        }
      } finally {
        this.penetrationLoading = false
      }
    },
    // 查询高管信息
    async handleManagerList() {
      this.managerLoading = true
      try {
        const managerListRes = await managerList(this.basicParams, this.token)
        if (managerListRes.code === 0) {
          if (!!managerListRes.rows.length) {
            this.managerList = managerListRes.rows[0]['managerDict'] || []
            this.managerTotal = managerListRes.rows[0]['managerDict'].length
          }
          this.key = Math.random()
        } else if (managerListRes.code === 401) {
          this.handleReLogin().then(() => {
            this.handleManagerList()
          })
        }
      } finally {
        this.managerLoading = false
      }
    },
    // 查询疑似关系
    async handleSuspiciousList() {
      this.suspiciousLoading = true
      try {
        const suspiciousListRes = await suspiciousList(this.basicParams, this.token)
        if (suspiciousListRes.code === 0) {
          this.suspiciousList = suspiciousListRes.rows || []
          this.suspiciousTotal = suspiciousListRes.total
          this.key = Math.random()
        } else if (suspiciousListRes.code === 401) {
          this.handleReLogin().then(() => {
            this.handleSuspiciousList()
          })
        }
      } finally {
        this.suspiciousLoading = false
      }
    },
    // 查询行政处罚
    async handleAdministrativeList() {
      this.administrativeLoading = true
      try {
        const administrativeListRes = await administrativeList(this.params, this.token)
        if (administrativeListRes.code === 0) {
          this.administrativeList = administrativeListRes.rows || []
          this.administrativeTotal = administrativeListRes.total
          this.key = Math.random()
        } else if (administrativeListRes.code === 401) {
          this.handleReLogin().then(() => {
            this.handleAdministrativeList()
          })
        }
      } finally {
        this.administrativeLoading = false
      }
    },
    // 查询行政监管措施
    async handleRegulatoryList() {
      this.regulatoryLoading = true
      try {
        const regulatoryListRes = await regulatoryList(this.params, this.token)
        if (regulatoryListRes.code === 0) {
          this.regulatoryList = regulatoryListRes.rows || []
          this.regulatoryTotal = regulatoryListRes.total
          this.key = Math.random()
        } else if (regulatoryListRes.code === 401) {
          this.handleReLogin().then(() => {
            this.handleRegulatoryList()
          })
        }
      } finally {
        this.regulatoryLoading = false
      }
    },
    // 查询纪律处分
    async handleDisciplinaryList() {
      this.disciplinaryLoading = true
      try {
        const disciplinaryListRes = await disciplinaryList(this.params, this.token)
        if (disciplinaryListRes.code === 0) {
          this.disciplinaryList = disciplinaryListRes.rows || []
          this.disciplinaryTotal = disciplinaryListRes.total
          this.key = Math.random()
        } else if (disciplinaryListRes.code === 401) {
          this.handleReLogin().then(() => {
            this.handleDisciplinaryList()
          })
        }
      } finally {
        this.disciplinaryLoading = false
      }
    },
    // 查询金融监管
    async handleFinancialList() {
      this.financialLoading = true
      try {
        const financialListRes = await financialList(this.params, this.token)
        if (financialListRes.code === 0) {
          this.financialList = financialListRes.rows || []
          this.financialTotal = financialListRes.total
          this.key = Math.random()
        } else if (financialListRes.code === 401) {
          this.handleReLogin().then(() => {
            this.handleFinancialList()
          })
        }
      } finally {
        this.financialLoading = false
      }
    },
    // 查询自律监管措施
    async handleSelfRegulatoryList() {
      this.selfRegulatoryLoading = true
      try {
        const selfRegulatoryListRes = await selfRegulatoryList(this.params, this.token)
        if (selfRegulatoryListRes.code === 0) {
          this.selfRegulatoryList = selfRegulatoryListRes.rows || []
          this.selfRegulatoryTotal = selfRegulatoryListRes.total
          this.key = Math.random()
        } else if (selfRegulatoryListRes.code === 401) {
          this.handleReLogin().then(() => {
            this.handleSelfRegulatoryList()
          })
        }
      } finally {
        this.selfRegulatoryLoading = false
      }
    },
    // 查询招投标
    async handleBiddingList() {
      this.biddingLoading = true
      try {
        const biddingListRes = await biddingList(this.params, this.token)
        if (biddingListRes.code === 0) {
          this.biddingList = biddingListRes.rows || []
          this.biddingTotal = biddingListRes.total
          this.key = Math.random()
        } else if (biddingListRes.code === 401) {
          this.handleReLogin().then(() => {
            this.handleBiddingList()
          })
        }
      } finally {
        this.biddingLoading = false
      }
    },
    // 查询客户
    async handleCustomerList() {
      this.customerLoading = true
      try {
        const customerListRes = await customerList(this.params, this.token)
        if (customerListRes.code === 0) {
          this.customerList = customerListRes.rows || []
          this.customerTotal = customerListRes.total
          this.key = Math.random()
        } else if (customerListRes.code === 401) {
          this.handleReLogin().then(() => {
            this.handleCustomerList()
          })
        }
      } finally {
        this.customerLoading = false
      }
    },
    // 查询担保企业
    async handleGuaranteeList() {
      this.guaranteeLoading = true
      try {
        const guaranteeListRes = await guaranteeList(this.params, this.token)
        if (guaranteeListRes.code === 0) {
          this.guaranteeList = guaranteeListRes.rows || []
          this.guaranteeTotal = guaranteeListRes.total
          this.key = Math.random()
        } else if (guaranteeListRes.code === 401) {
          this.handleReLogin().then(() => {
            this.handleGuaranteeList()
          })
        }
      } finally {
        this.guaranteeLoading = false
      }
    },
    // 查询信托融资担保
    async handleGuaranteeOTList() {
      this.guaranteeOTLoading = true
      try {
        const guaranteeOTListRes = await guaranteeOTList(this.params, this.token)
        if (guaranteeOTListRes.code === 0) {
          this.guaranteeOTList = guaranteeOTListRes.rows || []
          this.guaranteeOTTotal = guaranteeOTListRes.total
          this.key = Math.random()
        } else if (guaranteeOTListRes.code === 401) {
          this.handleReLogin().then(() => {
            this.handleGuaranteeOTList()
          })
        }
      } finally {
        this.guaranteeOTLoading = false
      }
    },
    // 查询债券担保
    async handleGuaranteeZQList() {
      this.guaranteeZQLoading = true
      try {
        const guaranteeZQListRes = await guaranteeZQList(this.params, this.token)
        if (guaranteeZQListRes.code === 0) {
          this.guaranteeZQList = guaranteeZQListRes.rows || []
          this.guaranteeZQTotal = guaranteeZQListRes.total
          this.key = Math.random()
        } else if (guaranteeZQListRes.code === 401) {
          this.handleReLogin().then(() => {
            this.handleGuaranteeZQList()
          })
        }
      } finally {
        this.guaranteeZQLoading = false
      }
    },
    // 查询担保人
    async handleGuarantorList() {
      this.guarantorLoading = true
      try {
        const guarantorListRes = await guarantorList(this.params, this.token)
        if (guarantorListRes.code === 0) {
          this.guarantorList = guarantorListRes.rows || []
          this.guarantorTotal = guarantorListRes.total
          this.key = Math.random()
        } else if (guarantorListRes.code === 401) {
          this.handleReLogin().then(() => {
            this.handleGuarantorList()
          })
        }
      } finally {
        this.guarantorLoading = false
      }
    },
    // 查询土地信息
    async handleLandList() {
      this.landLoading = true
      try {
        const landListRes = await landList(this.params, this.token)
        if (landListRes.code === 0) {
          this.landList = landListRes.rows || []
          this.landTotal = landListRes.total
          this.key = Math.random()
        } else if (landListRes.code === 401) {
          this.handleReLogin().then(() => {
            this.handleLandList()
          })
        }
      } finally {
        this.landLoading = false
      }
    },
    // 查询榜单资质
    async handleListQualification() {
      this.listQualificationLoading = true
      try {
        const listQualificationRes = await listQualification(this.params, this.token)
        if (listQualificationRes.code === 0) {
          this.listQualification = listQualificationRes.rows || []
          this.listQualificationTotal = listQualificationRes.total
          this.key = Math.random()
        } else if (listQualificationRes.code === 401) {
          this.handleReLogin().then(() => {
            this.handleListQualification()
          })
        }
      } finally {
        this.listQualificationLoading = false
      }
    },
    // 查询作品著作权
    async handlePaperList() {
      this.paperLoading = true
      try {
        const paperListRes = await paperList(this.params, this.token)
        if (paperListRes.code === 0) {
          this.paperList = paperListRes.rows || []
          this.paperTotal = paperListRes.total
          this.key = Math.random()
        } else if (paperListRes.code === 401) {
          this.handleReLogin().then(() => {
            this.handlePaperList()
          })
        }
      } finally {
        this.paperLoading = false
      }
    },
    // 查询财产线索
    async handlePropertyList() {
      this.propertyLoading = true
      try {
        const propertyListRes = await propertyList(this.params, this.token)
        if (propertyListRes.code === 0) {
          if (!!propertyListRes.rows.length) {
            this.propertyList = propertyListRes.rows[0].listData.list || []
            this.propertyTotal = propertyListRes.rows[0].listData.list.length
          }
          this.key = Math.random()
        } else if (propertyListRes.code === 401) {
          this.handleReLogin().then(() => {
            this.handlePropertyList()
          })
        }
      } finally {
        this.propertyLoading = false
      }
    },
    // 查询银行借款担保
    async handleProvideList() {
      this.provideLoading = true
      try {
        const provideListRes = await provideList(this.params, this.token)
        if (provideListRes.code === 0) {
          this.provideList = provideListRes.rows || []
          this.provideTotal = provideListRes.total
          this.key = Math.random()
        } else if (provideListRes.code === 401) {
          this.handleReLogin().then(() => {
            this.handleProvideList()
          })
        }
      } finally {
        this.provideLoading = false
      }
    },
    // 查询提供债券担保
    async handleProvideZQList() {
      this.provideZQLoading = true
      try {
        const provideZQListRes = await provideZQList(this.params, this.token)
        if (provideZQListRes.code === 0) {
          this.provideZQList = provideZQListRes.rows || []
          this.provideZQTotal = provideZQListRes.total
          this.key = Math.random()
        } else if (provideZQListRes.code === 401) {
          this.handleReLogin().then(() => {
            this.handleProvideZQList()
          })
        }
      } finally {
        this.provideZQLoading = false
      }
    },
    // 查询专利
    async handleRightList() {
      this.rightLoading = true
      try {
        const rightListRes = await rightList(this.params, this.token)
        if (rightListRes.code === 0) {
          this.rightList = rightListRes.rows || []
          this.rightTotal = rightListRes.total
          this.key = Math.random()
        } else if (rightListRes.code === 401) {
          this.handleReLogin().then(() => {
            this.handleRightList()
          })
        }
      } finally {
        this.rightLoading = false
      }
    },
    // 查询软件著作权
    async handleSoftList() {
      this.softLoading = true
      try {
        const softListRes = await softList(this.params, this.token)
        if (softListRes.code === 0) {
          this.softList = softListRes.rows || []
          this.softTotal = softListRes.total
          this.key = Math.random()
        } else if (softListRes.code === 401) {
          this.handleReLogin().then(() => {
            this.handleSoftList()
          })
        }
      } finally {
        this.softLoading = false
      }
    },
    // 查询供应商
    async handleSupplierList() {
      this.supplierLoading = true
      try {
        const supplierListRes = await supplierList(this.params, this.token)
        if (supplierListRes.code === 0) {
          this.supplierList = supplierListRes.rows || []
          this.supplierTotal = supplierListRes.total
          this.key = Math.random()
        } else if (supplierListRes.code === 401) {
          this.handleReLogin().then(() => {
            this.handleSupplierList()
          })
        }
      } finally {
        this.supplierLoading = false
      }
    },
    // 查询开庭公告
    async handleCourtList() {
      this.courtLoading = true
      try {
        const courtListRes = await courtList(this.params, this.token)
        if (courtListRes.code === 0) {
          this.courtList = courtListRes.rows || []
          this.courtTotal = courtListRes.total
          this.key = Math.random()
        } else if (courtListRes.code === 401) {
          this.handleReLogin().then(() => {
            this.handleCourtList()
          })
        }
      } finally {
        this.courtLoading = false
      }
    },
    // 查询送达公告
    async handleDeliveryList() {
      this.deliveryLoading = true
      try {
        const deliveryListRes = await deliveryList(this.params, this.token)
        if (deliveryListRes.code === 0) {
          this.deliveryList = deliveryListRes.rows || []
          this.deliveryTotal = deliveryListRes.total
          this.key = Math.random()
        } else if (deliveryListRes.code === 401) {
          this.handleReLogin().then(() => {
            this.handleDeliveryList()
          })
        }
      } finally {
        this.deliveryLoading = false
      }
    },
    // 查询失信被执行人
    async handleDishonestList() {
      this.dishonestLoading = true
      try {
        const dishonestListRes = await dishonestList(this.params, this.token)
        if (dishonestListRes.code === 0) {
          this.dishonestList = dishonestListRes.rows || []
          this.dishonestTotal = dishonestListRes.total
          this.key = Math.random()
        } else if (dishonestListRes.code === 401) {
          this.handleReLogin().then(() => {
            this.handleDishonestList()
          })
        }
      } finally {
        this.dishonestLoading = false
      }
    },
    // 查询被执行人
    async handleExecutedList() {
      this.executedLoading = true
      try {
        const executedListRes = await executedList(this.params, this.token)
        if (executedListRes.code === 0) {
          this.executedList = executedListRes.rows || []
          this.executedTotal = executedListRes.total
          this.key = Math.random()
        } else if (executedListRes.code === 401) {
          this.handleReLogin().then(() => {
            this.handleExecutedList()
          })
        }
      } finally {
        this.executedLoading = false
      }
    },
    // 查询立案信息
    async handleFilingList() {
      this.filingLoading = true
      try {
        const filingListRes = await filingList(this.params, this.token)
        if (filingListRes.code === 0) {
          this.filingList = filingListRes.rows || []
          this.filingTotal = filingListRes.total
          this.key = Math.random()
        } else if (filingListRes.code === 401) {
          this.handleReLogin().then(() => {
            this.handleFilingList()
          })
        }
      } finally {
        this.filingLoading = false
      }
    },
    // 查询终本案件
    async handleFinalList() {
      this.finalLoading = true
      try {
        const finalListRes = await finalList(this.params, this.token)
        if (finalListRes.code === 0) {
          this.finalList = finalListRes.rows || []
          this.finalTotal = finalListRes.total
          this.key = Math.random()
        } else if (finalListRes.code === 401) {
          this.handleReLogin().then(() => {
            this.handleFinalList()
          })
        }
      } finally {
        this.finalLoading = false
      }
    },
    // 查询法院公告
    async handleLegalList() {
      this.legalLoading = true
      try {
        const legalListRes = await legalList(this.params, this.token)
        if (legalListRes.code === 0) {
          this.legalList = legalListRes.rows || []
          this.legalTotal = legalListRes.total
          this.key = Math.random()
        } else if (legalListRes.code === 401) {
          this.handleReLogin().then(() => {
            this.handleLegalList()
          })
        }
      } finally {
        this.legalLoading = false
      }
    },
    // 查询限制高消费
    async handleRestrainingList() {
      this.restrainingLoading = true
      try {
        const restrainingListRes = await restrainingList(this.params, this.token)
        if (restrainingListRes.code === 0) {
          this.restrainingList = restrainingListRes.rows || []
          this.restrainingTotal = restrainingListRes.total
          this.key = Math.random()
        } else if (restrainingListRes.code === 401) {
          this.handleReLogin().then(() => {
            this.handleRestrainingList()
          })
        }
      } finally {
        this.restrainingLoading = false
      }
    },
    // 查询新闻公告研报
    async handleNewsList() {
      this.newsLoading = true
      try {
        const newsListRes = await newsList(this.params, this.token)
        if (newsListRes.code === 0) {
          this.newsList = newsListRes.rows || []
          this.newsTotal = newsListRes.total
          this.key = Math.random()
        } else if (newsListRes.code === 401) {
          this.handleReLogin().then(() => {
            this.handleNewsList()
          })
        }
      } finally {
        this.newsLoading = false
      }
    },
    // 查询其他领域信评
    async handleRatingOTList() {
      this.ratingOTLoading = true
      try {
        const ratingOTListRes = await ratingOTList(this.params, this.token)
        if (ratingOTListRes.code === 0) {
          this.ratingOTList = ratingOTListRes.rows || []
          this.ratingOTTotal = ratingOTListRes.total
          this.key = Math.random()
        } else if (ratingOTListRes.code === 401) {
          this.handleReLogin().then(() => {
            this.handleRatingOTList()
          })
        }
      } finally {
        this.ratingOTLoading = false
      }
    },
    // 查询纳税信用等级
    async handleRatingTaxList() {
      this.ratingTaxLoading = true
      try {
        const ratingTaxListRes = await ratingTaxList(this.params, this.token)
        if (ratingTaxListRes.code === 0) {
          if (!!ratingTaxListRes.rows.length) {
            this.ratingTaxList = ratingTaxListRes.rows[0]['subCreditRatingBeans'] || []
            this.ratingTaxTotal = ratingTaxListRes.rows[0]['subCreditRatingBeans'].length
          }
          this.key = Math.random()
        } else if (ratingTaxListRes.code === 401) {
          this.handleReLogin().then(() => {
            this.handleRatingTaxList()
          })
        }
      } finally {
        this.ratingTaxLoading = false
      }
    },
    // 组织架构/股份构成切换
    handleOneSwitch(e) {
      this.params.pageNum = this.basicParams.pageNum = 1
      this.params.pageSize = this.basicParams.pageSize = 10
      this.activeOneName = e.name
      switch (e.name) {
        case 'holder':
          this.holderLoading = true
          this.handleHolderList().finally(() => {
            this.holderLoading = false
          })
          break
        case 'branch':
          this.branchLoading = true
          this.handleBranchList().finally(() => {
            this.branchLoading = false
          })
          break
        case 'manager':
          this.managerLoading = true
          this.handleManagerList().finally(() => {
            this.managerLoading = false
          })
          break
        case 'change':
          this.changeLoading = true
          this.handleChangeList().finally(() => {
            this.changeLoading = false
          })
          break
        case 'child':
          this.childLoading = true
          this.handleChildList().finally(() => {
            this.childLoading = false
          })
          break
        case 'suspicious':
          this.suspiciousLoading = true
          this.handleSuspiciousList().finally(() => {
            this.suspiciousLoading = false
          })
          break
      }
    },
    // 企业司法预警信息/招投标信息切换
    async handleTwoSwitch(e) {
      this.params.pageNum = this.basicParams.pageNum = 1
      this.params.pageSize = this.basicParams.pageSize = 10
      this.activeTwoName = e.name
      switch (e.name) {
        case 'judicial':
          this.activeThreeName = 'courtList'
          try {
            // 开庭公告
            await this.handleCourtList()
            // 送达公告
            await this.handleDeliveryList()
            // 失信被执行人
            await this.handleDishonestList()
            // 被执行人
            await this.handleExecutedList()
            // 立案信息
            await this.handleFilingList()
            // 终本案件
            await this.handleFinalList()
            // 法院公告
            await this.handleLegalList()
            // 限制高消费
            await this.handleRestrainingList()
          } catch (error) {
            console.log(error)
          }
          break
        case 'run':
          this.activeFourName = 'biddingList'
          try {
            // 招投标
            await this.handleBiddingList()
            // 客户
            await this.handleCustomerList()
            // 担保企业
            await this.handleGuaranteeList()
            // 信托融资担保
            await this.handleGuaranteeOTList()
            // 债券担保
            await this.handleGuaranteeZQList()
            // 担保人
            await this.handleGuarantorList()
            // 土地信息
            await this.handleLandList()
            // 榜单资质
            await this.handleListQualification()
            // 作品著作权
            await this.handlePaperList()
            // 财产线索
            await this.handlePropertyList()
            // 银行借款担保
            await this.handleProvideList()
            // 提供债券担保
            await this.handleProvideZQList()
            // 专利
            await this.handleRightList()
            // 软件著作权
            await this.handleSoftList()
            // 供应商
            await this.handleSupplierList()
          } catch (error) {
            console.log(error)
          }
          break
        case 'punish':
          this.activeFiveName = 'administrativeList'
          try {
            // 行政处罚
            await this.handleAdministrativeList()
            // 行政监管措施
            await this.handleRegulatoryList()
            // 纪律处分
            await this.handleDisciplinaryList()
            // 金融监管
            await this.handleFinancialList()
            // 自律监管措施
            await this.handleSelfRegulatoryList()
          } catch (error) {
            console.log(error)
          }
          break
        case 'credit':
          this.activeSixName = 'ratingTaxList'
          try {
            // 其他领域信评
            await this.handleRatingOTList()
            // 纳税信用等级
            await this.handleRatingTaxList()
          } catch (error) {
            console.log(error)
          }
          break
        case 'news':
          this.newsLoading = true
          this.handleNewsList().finally(() => {
            this.newsLoading = false
          })
          break
      }
    },
    // 开庭公告、送达公告等切换
    handleThreeSwitch(e) {
      this.params.pageNum = this.basicParams.pageNum = 1
      this.params.pageSize = this.basicParams.pageSize = 10
      this.activeThreeName = e.name
      switch (e.name) {
        case 'courtList':
          this.courtLoading = true
          this.handleCourtList().finally(() => {
            this.courtLoading = false
          })
          break
        case 'deliveryList':
          this.deliveryLoading = true
          this.handleDeliveryList().finally(() => {
            this.deliveryLoading = false
          })
          break
        case 'dishonestList':
          this.dishonestLoading = true
          this.handleDishonestList().finally(() => {
            this.dishonestLoading = false
          })
          break
        case 'executedList':
          this.executedLoading = true
          this.handleExecutedList().finally(() => {
            this.executedLoading = false
          })
          break
        case 'filingList':
          this.filingLoading = true
          this.handleFilingList().finally(() => {
            this.filingLoading = false
          })
          break
        case 'finalList':
          this.finalLoading = true
          this.handleFinalList().finally(() => {
            this.finalLoading = false
          })
          break
        case 'legalList':
          this.legalLoading = true
          this.handleLegalList().finally(() => {
            this.legalLoading = false
          })
          break
        case 'restrainingList':
          this.restrainingLoading = true
          this.handleRestrainingList().finally(() => {
            this.restrainingLoading = false
          })
          break
      }
    },
    // 企业经营内容切换
    handleFourSwitch(e) {
      this.params.pageNum = this.basicParams.pageNum = 1
      this.params.pageSize = this.basicParams.pageSize = 10
      this.activeFourName = e.name
      switch (e.name) {
        case 'biddingList':
          this.biddingLoading = true
          this.handleBiddingList().finally(() => {
            this.biddingLoading = false
          })
          break
        case 'customerList':
          this.customerLoading = true
          this.handleCustomerList().finally(() => {
            this.customerLoading = false
          })
          break
        case 'guaranteeList':
          this.guaranteeLoading = true
          this.handleGuaranteeList().finally(() => {
            this.guaranteeLoading = false
          })
          break
        case 'guaranteeOTList':
          this.guaranteeOTLoading = true
          this.handleGuaranteeOTList().finally(() => {
            this.guaranteeOTLoading = false
          })
          break
        case 'guaranteeZQList':
          this.guaranteeZQLoading = true
          this.handleGuaranteeZQList().finally(() => {
            this.guaranteeZQLoading = false
          })
          break
        case 'guarantorList':
          this.guarantorLoading = true
          this.handleGuarantorList().finally(() => {
            this.guarantorLoading = false
          })
          break
        case 'landList':
          this.landLoading = true
          this.handleLandList().finally(() => {
            this.landLoading = false
          })
          break
        case 'listQualification':
          this.listQualificationLoading = true
          this.handleListQualification().finally(() => {
            this.listQualificationLoading = false
          })
          break
        case 'paperList':
          this.paperLoading = true
          this.handlePaperList().finally(() => {
            this.paperLoading = false
          })
          break
        case 'propertyList':
          this.propertyLoading = true
          this.handlePropertyList().finally(() => {
            this.propertyLoading = false
          })
          break
        case 'provideList':
          this.provideLoading = true
          this.handleProvideList().finally(() => {
            this.provideLoading = false
          })
          break
        case 'provideZQList':
          this.provideZQLoading = true
          this.handleProvideZQList().finally(() => {
            this.provideZQLoading = false
          })
          break
        case 'rightList':
          this.rightLoading = true
          this.handleRightList().finally(() => {
            this.rightLoading = false
          })
          break
        case 'softList':
          this.softLoading = true
          this.handleSoftList().finally(() => {
            this.softLoading = false
          })
          break
        case 'supplierList':
          this.supplierLoading = true
          this.handleSupplierList().finally(() => {
            this.supplierLoading = false
          })
          break
      }
    },
    // 监管处罚切换
    handleFiveSwitch(e) {
      this.params.pageNum = this.basicParams.pageNum = 1
      this.params.pageSize = this.basicParams.pageSize = 10
      this.activeFiveName = e.name
      switch (e.name) {
        case 'administrativeList':
          this.administrativeLoading = true
          this.handleAdministrativeList().finally(() => {
            this.administrativeLoading = false
          })
          break
        case 'regulatoryList':
          this.regulatoryLoading = true
          this.handleRegulatoryList().finally(() => {
            this.regulatoryLoading = false
          })
          break
        case 'disciplinaryList':
          this.disciplinaryLoading = true
          this.handleDisciplinaryList().finally(() => {
            this.disciplinaryLoading = false
          })
          break
        case 'financialList':
          this.financialLoading = true
          this.handleFinancialList().finally(() => {
            this.financialLoading = false
          })
          break
        case 'selfRegulatoryList':
          this.selfRegulatoryLoading = true
          this.handleSelfRegulatoryList().finally(() => {
            this.selfRegulatoryLoading = false
          })
          break
      }
    },
    // 信用评级切换
    handleSixSwitch(e) {
      this.params.pageNum = this.basicParams.pageNum = 1
      this.params.pageSize = this.basicParams.pageSize = 10
      this.activeSixName = e.name
      switch (e.name) {
        case 'ratingOTList':
          this.ratingOTLoading = true
          this.handleRatingOTList().finally(() => {
            this.ratingOTLoading = false
          })
          break
        case 'ratingTaxList':
          this.ratingTaxLoading = true
          this.handleRatingTaxList().finally(() => {
            this.ratingTaxLoading = false
          })
          break
      }
    },
    // 格式化新闻日期
    newDateFormat(date = '') {
      const year = date.substring(0, 4)
      const month = date.substring(4, 6)
      const day = date.substring(6, 8)
      return year === new Date().getFullYear().toString() ? `${month}-${day}` : `${year}-${month}-${day}`
    },
    // 展开/收起工商变更内容
    handleChangeToggle(row) {
      row.afterMore = !row.afterMore
      row.beforeMore = !row.beforeMore
    },
    // 详情
    handleView(info = {}, title = '', type = 0) {
      this.$refs.companyJudicial.handleView(info, title, type)
    },
    // 企业经营详情
    handleRunView(info = {}, title = '', type = 0) {
      this.$refs.companyRun.handleView(info, title, type)
    }
  }
}
</script>

<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';
.detailBox {
  .detailTab {
    ::v-deep {
      .el-tabs__header {
        margin: 0;
        padding: 0 20px;
      }
      .el-tabs__nav-wrap::after {
        height: 0;
      }
    }
    &.bigTab {
      border-style: solid;
      border-color: #ebedf3;
      border-width: 1px;
      ::v-deep {
        .el-tabs__item {
          height: 50px;
          line-height: 50px;
        }
        .el-tabs__item.is-active {
          font-size: 16px;
        }
      }
    }
    &.smallTab {
      ::v-deep {
        .el-tabs__item {
          font-size: 12px;
          padding: 0 10px;
        }
      }
    }
  }
  .organization {
    &Box {
      margin-top: 20px;
      position: relative;
    }
    &Refresh {
      height: 50px;
      position: absolute;
      right: 20px;
      top: 0;
      cursor: pointer;
      color: $blue;
      span {
        font-size: 12px;
        color: $disabled;
        margin-right: 10px;
      }
    }
    &Table {
      &:after {
        width: 0 !important;
      }
      ::v-deep {
        border-radius: 0;
        border: 0;
        margin-bottom: 0;
        .el-table__header-wrapper th.el-table__cell {
          background-color: #f2f2f2 !important;
          color: $disabled;
        }
        .el-table__body-wrapper {
          .el-table__row--striped {
            td.el-table__cell {
              background-color: #f8faff !important;
            }
          }
          .el-table__row {
            &.el-table__row--striped {
              &:hover {
                td.el-table__cell {
                  background-color: $bgColor !important;
                }
              }
            }
            &:hover {
              td.el-table__cell {
                background-color: $bgColor !important;
              }
            }
          }
        }
        .tabelName {
          display: flex;
          align-items: center;
          .name {
            font-size: 14px;
            font-weight: 500;
            color: $font;
            margin-right: 20px;
          }
          .tag {
            display: flex;
            align-items: center;
            height: 20px;
            padding: 0 6px;
            font-size: 12px;
            color: $info;
            border-radius: 5px;
            border: 1px solid #ebedf3;
            cursor: pointer;
            span {
              color: $blue;
            }
            &.primary {
              background-color: #e2ecff;
              color: $blue;
            }
            &.orange {
              background-color: #f4e7d3;
              color: #c3821e;
            }
          }
          .tag + .tag {
            margin-left: 10px;
          }
        }
        .tableInfo {
          display: flex;
          align-items: center;
          justify-content: space-between;
          &Title {
            font-size: 14px;
            font-weight: 500;
            color: $font;
            margin-right: 20px;
          }
          &Date {
            font-size: 12px;
            color: $font;
          }
        }
      }
    }
  }
  .other {
    &Box {
      margin-top: 20px;
      .detailTab {
        border-style: solid;
        border-color: #ebedf3;
        border-width: 0 1px 1px;
      }
    }
    &Tab {
      box-shadow: unset;
      border-bottom: 0;
      ::v-deep {
        .el-tabs__content {
          display: none;
        }
      }
    }
  }
}
::v-deep {
  .pagination-container {
    height: 52px;
  }
  .courtItem {
    font-size: 12px;
    line-height: 20px;
    span {
      color: $font;
    }
    em {
      font-style: normal;
      color: $blue;
    }
  }
  .courtIcon {
    display: inline-block;
    font-size: 12px;
    line-height: 20px;
    padding: 0 5px;
    border: 1px solid $orange;
    color: $orange;
  }
  .changeInfo {
    max-height: 69px;
    overflow: hidden;
    position: relative;
    &.none {
      max-height: unset;
      .changeInfoBtn {
        position: relative;
      }
    }
    &Btn {
      color: $blue;
      cursor: pointer;
      position: absolute;
      right: 0;
      bottom: 0;
      background-color: $white;
      padding: 0 7px;
      line-height: 23px;
      display: inline-block;
    }
  }
  .newsTable {
    padding-top: 20px;
    border-color: #ebedf3;
    border-style: solid;
    border-width: 0 1px 1px;
  }
}
</style>
