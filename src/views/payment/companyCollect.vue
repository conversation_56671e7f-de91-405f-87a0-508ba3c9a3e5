<template>
  <div class="newBox bgcf9 vh-130">
    <!-- 搜索 -->
    <div class="custom-search flex" style="padding-top: 18px">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" @submit.native.prevent>
        <el-form-item label="企业名称" prop="companyName">
          <el-input v-model="queryParams.companyName" placeholder="请输入企业名称" clearable @keyup.enter.native="handleQuery" @clear="handleResetQuery" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="small" @click="handleResetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="classify flex" style="margin-top: 10px">
      <div class="classify-item" :class="{ active: item.value === tabName }" v-for="item in tabOptions" :key="item.value" @click="handleChange(item)">{{ item.label }}</div>
      <div class="classify-toolbar">
        <right-toolbar :search="false" @queryTable="getList" :columns="columns"></right-toolbar>
      </div>
    </div>
    <div class="tableBox">
      <el-table v-loading="loading" ref="table" stripe :data="list.slice((queryParams.pageNum - 1) * queryParams.pageSize, queryParams.pageNum * queryParams.pageSize)" row-key="id" style="width: 100%" class="custom-table">
        <el-table-column type="index" label="序号" align="center" v-if="columns[0].visible"></el-table-column>
        <el-table-column prop="companyName" label="企业名称" align="left" show-overflow-tooltip min-width="180" v-if="columns[1].visible">
          <template slot-scope="{ row }">
            <span class="table-link" :class="{ isTop: row.isTop }" @click="handleDetail(row)">{{ row.companyName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="电话" align="center" show-overflow-tooltip v-if="columns[2].visible">
          <template slot-scope="{ row }">{{ row.companyPhoneSourceList.length ? row.companyPhoneSourceList[0]['number'] : '' }}</template>
        </el-table-column>
        <el-table-column label="邮箱" align="center" show-overflow-tooltip v-if="columns[3].visible">
          <template slot-scope="{ row }">{{ row.companyEmailList.length ? row.companyEmailList[4] : '' }}</template>
        </el-table-column>
        <el-table-column label="法定代表人" align="center" show-overflow-tooltip v-if="columns[4].visible">
          <template slot-scope="{ row }">{{ row.companyLegalPersonName || '-' }}</template>
        </el-table-column>
        <el-table-column label="登记状态" align="center" show-overflow-tooltip v-if="columns[5].visible">
          <template slot-scope="{ row }">{{ row.companyRegStatus || '-' }}</template>
        </el-table-column>
        <el-table-column label="成立日期" align="center" show-overflow-tooltip v-if="columns[6].visible">
          <template slot-scope="{ row }">{{ parseTime(row.companyApprovedTime, '{y}-{m}-{d}') || '-' }}</template>
        </el-table-column>
        <el-table-column label="统一社会信用代码" align="center" show-overflow-tooltip min-width="120" v-if="columns[7].visible">
          <template slot-scope="{ row }">{{ row.companyCreditCode || '-' }}</template>
        </el-table-column>
        <el-table-column label="注册资本" align="center" show-overflow-tooltip v-if="columns[8].visible">
          <template slot-scope="{ row }">{{ row.companyRegCapital || '-' }}</template>
        </el-table-column>
        <el-table-column label="工商注册号" align="center" show-overflow-tooltip v-if="columns[9].visible">
          <template slot-scope="{ row }">{{ row.companyRegNumber || '-' }}</template>
        </el-table-column>
        <el-table-column label="组织机构代码" align="center" show-overflow-tooltip min-width="90" v-if="columns[10].visible">
          <template slot-scope="{ row }">{{ row.companyOrgNumber || '-' }}</template>
        </el-table-column>
        <el-table-column label="企业类型" align="center" show-overflow-tooltip v-if="columns[11].visible">
          <template slot-scope="{ row }">{{ row.companyType ? row.companyType.split(';').pop() : '' }}</template>
        </el-table-column>
        <el-table-column label="行业" align="center" show-overflow-tooltip v-if="columns[12].visible">
          <template slot-scope="{ row }">{{ row.companyCategory || '-' }}</template>
        </el-table-column>
        <el-table-column label="参保人数" align="center" show-overflow-tooltip v-if="columns[13].visible">
          <template slot-scope="{ row }">{{ row.companySocialStaffNum + '人' || '-' }}</template>
        </el-table-column>
        <el-table-column label="注册地址" align="center" show-overflow-tooltip v-if="columns[14].visible">
          <template slot-scope="{ row }">{{ removeHtmlTag(row.companyRegLocation, 300) || '-' }}</template>
        </el-table-column>
        <el-table-column label="操作" header-align="center" :align="tabName === 'now' ? 'right' : 'center'" :width="tabName === 'now' ? 280 : ''">
          <template slot-scope="{ row }">
            <template v-if="tabName === 'now'">
              <el-button type="text" size="small" icon="el-icon-upload2" @click="handleTop(row)" v-if="!row.isTop">置顶</el-button>
              <el-button type="text" size="small" icon="el-icon-download" @click="handleCancelTop(row)" v-if="row.isTop">取消置顶</el-button>
              <el-button type="text" size="small" icon="el-icon-view" @click="handleDetail(row)">查看</el-button>
              <el-button type="text" size="small" icon="el-icon-star-off" @click="handleCancel(row)">取消关注</el-button>
              <el-button type="text" size="small" icon="el-icon-edit-outline" @click="handleReplenish(row)">补充信息</el-button>
            </template>
            <template v-else>
              <el-button type="text" size="small" icon="el-icon-view" @click="handleDetail(row)">查看</el-button>
              <el-button type="text" size="small" icon="el-icon-star-off" @click="handleCollect(row)">关注</el-button>
            </template>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="custom-pagination">
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" />
      </div>
    </div>

    <!--企业详情-->
    <company ref="company" @callback="refreshList"></company>

    <!--补充信息-->
    <el-dialog v-dialogDragBox title="企业信息补充提交" :visible.sync="replenishOpen" width="1150px" class="custom-dialog">
      <div style="padding: 0 20px">
        <div class="diskInfo">
          <span>企业名称：</span>
          <b>{{ replenishInfo.companyName }}</b>
          <span>法定代表人：</span>
          <b>{{ replenishInfo.companyLegalPersonName }}</b>
          <span>联系电话：</span>
          <b>{{ replenishInfo.hasOwnProperty('companyPhoneSourceList') && replenishInfo.companyPhoneSourceList.length ? replenishInfo.companyPhoneSourceList[0]['number'] : '' }}</b>
        </div>
        <el-form ref="replenishForm" :model="replenishForm" :rules="replenishRules" label-width="100px">
          <el-form-item label="补充信息" prop="info">
            <el-input placeholder="请输入补充信息" v-model="replenishForm.info" type="textarea" :autosize="{ minRows: 4, maxRows: 6 }" resize="none" class="custom-textarea"></el-input>
          </el-form-item>
          <el-form-item label="照片提交" prop="archives">
            <image-upload :isShowTip="!replenishForm.archives" v-model="replenishForm.archives" :file-type="fileType" />
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button class="custom-dialog-btn" @click="replenishOpen = false">关闭</el-button>
        <el-button class="custom-dialog-btn primary" @click="handleSubmitReplenish">提交</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { enterpriseCollectionList, enterpriseCollectionCancel, enterpriseCollection, enterpriseCollectionHistory } from '@/api/payment'
import Company from '@/views/payment/company'
import { removeHtmlTag } from '@/utils'

export default {
  components: { Company },
  data() {
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        companyName: undefined
      },
      loading: true,
      allList: [],
      list: [],
      total: 0,
      // 补充信息
      replenishOpen: false,
      replenishInfo: {},
      replenishForm: {
        info: '',
        archives: ''
      },
      replenishRules: {},
      fileType: ['png', 'PNG', 'jpg', 'JPG', 'jpeg', 'JPEG'],
      // 表格列
      columns: [
        { key: 0, label: `序号`, visible: true },
        { key: 1, label: `企业名称`, visible: true },
        { key: 2, label: `电话`, visible: true },
        { key: 3, label: `邮箱`, visible: false },
        { key: 4, label: `法定代表人`, visible: true },
        { key: 5, label: `登记状态`, visible: false },
        { key: 6, label: `成立日期`, visible: true },
        { key: 7, label: `统一社会信用代码`, visible: false },
        { key: 8, label: `注册资本`, visible: true },
        { key: 9, label: `工商注册号`, visible: false },
        { key: 10, label: `组织机构代码`, visible: false },
        { key: 11, label: `企业类型`, visible: false },
        { key: 12, label: `行业`, visible: false },
        { key: 13, label: `参保人数`, visible: false },
        { key: 14, label: `注册地址`, visible: false }
      ],
      tabOptions: [
        { label: '关注列表', value: 'now' },
        { label: '历史关注', value: 'history' }
      ],
      tabName: 'now'
    }
  },
  created() {
    this.getList()
  },
  computed: {
    info() {
      return this.$store.getters.info
    }
  },
  methods: {
    removeHtmlTag,
    // 切换
    handleChange(item) {
      this.tabName = item.value
      this.getList()
    },
    // 列表
    getList() {
      if (this.tabName === 'now') this.getNowList()
      else this.getHistoryList()
    },
    // 查询现在关注列表
    getNowList() {
      this.loading = true
      enterpriseCollectionList().then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          if (data && data.length) {
            data.forEach((item, index) => {
              data[index] = JSON.parse(item)
            })
            data.sort((a, b) => {
              if (a.isTop && !b.isTop) return -1
              if (!a.isTop && b.isTop) return 1
              const timeA = a.updateTime || a.collectTime
              const timeB = b.updateTime || b.collectTime
              return timeB - timeA
            })
          }
          this.allList = data
          this.list = data
          this.total = data.length || 0
          this.loading = false
        } else this.$message.error(msg)
      })
    },
    // 查询历史关注列表
    getHistoryList() {
      this.loading = true
      enterpriseCollectionHistory().then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          if (data && data.length) {
            data.forEach((item, index) => {
              data[index] = JSON.parse(item)
            })
            data.sort((a, b) => {
              if (a.isTop && !b.isTop) return -1
              if (!a.isTop && b.isTop) return 1
              const timeA = a.updateTime || a.collectTime
              const timeB = b.updateTime || b.collectTime
              return timeB - timeA
            })
          }
          this.allList = data
          this.list = data
          this.total = data.length || 0
          this.loading = false
        } else this.$message.error(msg)
      })
    },
    // 刷新列表
    async refreshList() {
      let res
      if (this.tabName === 'now') res = await enterpriseCollectionList()
      else res = await enterpriseCollectionHistory()
      const { code, data } = res
      if (code === 200) {
        if (data && data.length) {
          data.forEach((item, index) => {
            data[index] = JSON.parse(item)
          })
          data.sort((a, b) => {
            if (a.isTop && !b.isTop) return -1
            if (!a.isTop && b.isTop) return 1
            const timeA = a.updateTime || a.collectTime
            const timeB = b.updateTime || b.collectTime
            return timeB - timeA
          })
        }
        this.allList = data
        this.total = data.length || 0
        if (data && data.length) this.$set(this, 'list', data)
        else this.list = []
      }
    },
    // 搜索
    handleQuery() {
      if (!this.queryParams.companyName) return
      const list = [...this.allList]
      const listArr = list.filter(item => item.companyName.includes(this.queryParams.companyName))
      this.total = listArr.length || 0
      this.list = listArr
    },
    // 重置搜索
    handleResetQuery() {
      this.queryParams.companyName = undefined
      this.list = this.allList
    },
    // 查看企业详情
    handleDetail(row) {
      this.$refs.company.getInfo(row, 'private', true, true)
    },
    // 取消关注
    // prettier-ignore
    handleCancel(row) {
      const { companyCreditCode } = row
      this.$confirm('确定取消关注该企业吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        enterpriseCollectionCancel({ taxNo: companyCreditCode }).then(res => {
          const { code, msg } = res
          if (code === 200) {
            this.$message.success('成功取消关注')
            this.refreshList()
          } else this.$message.error(msg)
        })
      }).catch(() => {})
    },
    // 补充信息
    handleReplenish(row) {
      this.replenishForm = {
        info: undefined,
        archives: undefined
      }
      this.resetForm('replenishForm')
      this.replenishInfo = row
      this.replenishOpen = true
    },
    // 提交补充信息
    handleSubmitReplenish() {
      const timestamp = new Date().getTime()
      const createTime = this.parseTime(timestamp, '{y}-{m}-{d} {h}:{i}:{s}')
      const { nickName, userId } = this.info
      let data = { ...this.replenishInfo, updateTime: timestamp }
      if (data.hasOwnProperty('followUps') && data.followUps.length) {
        data.followUps.push({ salesperson: nickName, salespersonId: userId, createTime, id: timestamp, ...this.replenishForm })
      } else {
        data.followUps = [{ salesperson: nickName, salespersonId: userId, createTime, id: timestamp, ...this.replenishForm }]
      }
      enterpriseCollection({ taxNo: data.companyCreditCode, data: JSON.stringify(data) }).then(res => {
        const { code, msg } = res
        if (code === 200) {
          this.$message.success('提交成功')
          this.replenishOpen = false
          this.refreshList()
        } else this.$message.error(msg)
      })
    },
    // 置顶
    // prettier-ignore
    handleTop(row) {
      const data = { ...row, isTop: true, updateTime: new Date().getTime() }
      this.$confirm('确定置顶该企业吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        enterpriseCollection({ taxNo: data.companyCreditCode, data: JSON.stringify(data) }).then(res => {
          const { code, msg } = res
          if (code === 200) {
            this.$message.success('置顶成功')
            this.refreshList()
          } else this.$message.error(msg)
        })
      }).catch(() => {})
    },
    // 取消置顶
    // prettier-ignore
    handleCancelTop(row) {
      const data = { ...row, isTop: false, updateTime: new Date().getTime() }
      this.$confirm('确定取消置顶该企业吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        enterpriseCollection({ taxNo: data.companyCreditCode, data: JSON.stringify(data) }).then(res => {
          const { code, msg } = res
          if (code === 200) {
            this.$message.success('取消置顶成功')
            this.refreshList()
          } else this.$message.error(msg)
        })
      }).catch(() => {})
    },
    // 关注
    // prettier-ignore
    handleCollect(info) {
      const { companyCreditCode } = info
      if (!companyCreditCode) return
      const data = JSON.stringify({ ...info, collectTime: new Date().getTime() })
      this.$confirm('确定关注该企业吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        enterpriseCollection({ taxNo: companyCreditCode, data }).then(res => {
          const { code, msg } = res
          if (code === 200) {
            this.$message.success('关注成功')
            this.refreshList()
          } else this.$message.error(msg)
        })
      }).catch(() => { })
    }
  }
}
</script>
<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';
.vh-130 {
  min-height: calc(100vh - 130px);
  overflow: hidden;
}
.tableBox {
  padding: 10px 20px 20px;
}
.diskInfo {
  display: flex;
  align-items: center;
  line-height: 1.5em;
  padding-bottom: 20px;
  span {
    font-size: 14px;
    color: $disabled;
  }
  b {
    font-size: 16px;
    font-weight: normal;
    padding-right: 30px;
  }
}
::v-deep {
  .classify-item {
    transition: all 0.3s;
  }
  .classify-item:hover,
  .classify-item.active {
    background-color: $blue;
    color: $white;
    border-radius: 5px 5px 0 0;
  }
  .table-link {
    &.isTop {
      &:before {
        content: 'Top';
        display: inline-block;
        padding: 0 5px;
        height: 20px;
        line-height: 20px;
        text-align: center;
        color: #ffffff;
        background-color: $red;
        border-radius: 3px;
        margin-right: 5px;
      }
    }
  }
}
</style>
