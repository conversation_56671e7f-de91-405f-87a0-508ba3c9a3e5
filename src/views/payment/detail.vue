<template>
  <div>
    <el-dialog v-dialogDragBox :title="title" :visible.sync="open" width="1150px" class="custom-dialog" append-to-body
      @close="handleClose">
      <div style="position: absolute; top: 15px; left: 150px;">
        <span :class="statusOptions.find(item => item.value === info.status) && statusOptions.find(item => item.value === info.status).class">{{ statusOptions.find(item => item.value === info.status) && statusOptions.find(item => item.value === info.status).label }}</span>
      </div>
      <div :key="key">
        <div class="followListTitle flex" style="margin-top: -20px" v-if="!type || type === 'info' || type === 'view'">
          <span>基本信息</span>
          <div class="followListTitleTip"
            v-if="companyId == 14 && (Object.keys(kingdeeInfo).length > 0 || kingdeeTitle)">
            <template v-if="Object.keys(kingdeeInfo).length > 0">
              金蝶应收汇总：
              <span v-if="kingdeeInfo.CurrencyForName">币别：{{ kingdeeInfo.CurrencyForName }}</span>
              <span style="margin-left: 10px" v-if="kingdeeInfo.InitAmount">期初余额：{{ kingdeeInfo.InitAmount }}</span>
              <span style="margin-left: 10px" v-if="kingdeeInfo.Amount">本期应收：{{ kingdeeInfo.Amount }}</span>
              <span style="margin-left: 10px" v-if="kingdeeInfo.RealAmount">本期收款：{{ kingdeeInfo.RealAmount }}</span>
              <span style="margin-left: 10px" v-if="kingdeeInfo.leftAmount">期末余额：{{ kingdeeInfo.leftAmount }}</span>
            </template>
            <template v-if="kingdeeTitle">{{ kingdeeTitle }}</template>
          </div>
          <el-select v-model="useOrg" placeholder="请选择使用组织" @change="handleUseOrg" size="small" multiple collapse-tags
            style="margin-left: 10px" v-if="companyId == 14 && (Object.keys(kingdeeInfo).length > 0 || kingdeeTitle) && customerNumber">
            <el-option v-for="org in ApplicationOrgNumber" :key="org.value" :label="org.label" :value="org.value" />
          </el-select>
          <el-button type="primary" icon="el-icon-view" size="small" style="margin-left: 10px" v-if="companyId == 14 && (Object.keys(kingdeeInfo).length > 0 || kingdeeTitle) && customerNumber" @click="handleView">查看应收明细</el-button>
        </div>
        <div class="infoBox" v-if="!type || type === 'info' || type === 'view'">
          <el-row :gutter="10">
            <el-col :span="12">
              <span>客户名称：</span>
              <b :class="{ isLink: info.customerId !== 0 }" @click="info.customerId !== 0 ? handleShow(info) : ''">{{
                info.customerName }}</b>
            </el-col>
            <el-col :span="12">
              <span>应收尾款：</span>
              <b class="price">{{ info.receivable + '元' }}</b>
            </el-col>
            <el-col :span="12">
              <span>回款数目：</span>
              <b class="price">{{ info.payment + '元' }}</b>
            </el-col>
            <el-col :span="12">
              <span>发货日期：</span>
              <b>{{ info.deliveryTime }}</b>
            </el-col>
            <el-col :span="12">
              <span>业务员：</span>
              <b>{{ info.salesperson }}</b>
            </el-col>
            <el-col :span="12">
              <span>欠款方式：</span>
              <b>{{ info.debtMode == 0 ? '材料欠款' : '工程欠款' }}</b>
            </el-col>
            <el-col :span="24" v-if="info.engineering">
              <span>工程名称：</span>
              <b>{{ info.engineering }}</b>
            </el-col>
            <el-col :span="24">
              <div class="inline-flex align-center">
                <span>合同：</span>
                <el-button type="text" icon="el-icon-view" size="small" :disabled="!info.contract"
                  @click="handleMore('contract')">点击查看</el-button>
              </div>
              <div class="inline-flex align-center">
                <span>签收单：</span>
                <el-button type="text" icon="el-icon-view" size="small" :disabled="!info.receipt"
                  @click="handleMore('receipt')">点击查看</el-button>
              </div>
              <div class="inline-flex align-center">
                <span>对账函：</span>
                <el-button type="text" icon="el-icon-view" size="small" :disabled="!info.reconciliation"
                  @click="handleMore('reconciliation')">点击查看</el-button>
              </div>
              <div class="inline-flex align-center">
                <span>录音：</span>
                <el-button type="text" icon="el-icon-view" size="small" :disabled="!info.tape"
                  @click="handleMore('tape')">点击查看</el-button>
              </div>
              <div class="inline-flex align-center">
                <span>聊天记录：</span>
                <el-button type="text" icon="el-icon-view" size="small" :disabled="!info.chatHistory"
                  @click="handleMore('chatHistory')">点击查看</el-button>
              </div>
            </el-col>
            <el-col :span="24">
              <div class="flex align-center">
                <span style="flex-shrink: 0">备注：</span>
                <b v-if="!isEditRemark">
                  {{ info.remark }}
                  <el-button type="text" icon="el-icon-edit" size="small" @click.stop="handleEditRemark"
                    v-if="!type || isAudit || type === 'view'">编辑</el-button>
                </b>
                <div class="remark-input" v-else>
                  <el-input type="textarea" ref="remarkInput" :autosize="{ minRows: 4, maxRows: 8 }" resize="none"
                    v-model="remark" placeholder="请输入备注"></el-input>
                  <el-button class="remark-save" type="primary" icon="el-icon-check" size="small"
                    @click.stop="handleSaveRemark" v-if="remark">保存</el-button>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
        <div style="padding: 0 20px" v-if="type === 'up'">
          <el-form ref="form" :model="form" :rules="rules" label-width="8em">
            <el-row :gutter="10">
              <el-col :span="24">
                <el-form-item label="跟进详细" prop="followInfo">
                  <el-input placeholder="请输入跟进详细" v-model="form.followInfo" type="textarea"
                    :autosize="{ minRows: 4, maxRows: 6 }" resize="none" class="custom-textarea"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="应收尾款" prop="receivable">
                  <el-input v-model="form.receivable" placeholder="请输入应收尾款">
                    <span slot="suffix">元</span>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="回款数目" prop="payment">
                  <el-input v-model="form.payment" placeholder="请输入回款数目">
                    <span slot="suffix">元</span>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="预计回款日期" prop="expectedPaymentDate">
                  <el-date-picker :picker-options="expireTimeOPtion" v-model="form.expectedPaymentDate" clearable
                    type="datetime" placeholder="请选择预计回款日期" value-format="yyyy-MM-dd HH:mm:ss"
                    style="width: 100%"></el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="24"></el-col>
              <el-col :span="12">
                <el-form-item label="档案提交" prop="archives">
                  <image-upload isRow :isShowTip="!form.archives" :fileSize="1024" v-model="form.archives"
                    :file-type="fileType" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="合同" prop="contract">
                  <image-upload isRow :isShowTip="!form.contract" :fileSize="1024" v-model="form.contract"
                    :file-type="fileType" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="录音" prop="tape">
                  <file-upload isRow :isShowTip="!form.tape" :fileSize="1024" v-model="form.tape"
                    :file-type="fileTypes" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="聊天记录" prop="chatHistory">
                  <file-upload isRow :isShowTip="!form.chatHistory" :fileSize="1024" v-model="form.chatHistory"
                    :file-type="filesType" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="签收单" prop="receipt">
                  <image-upload isRow :isShowTip="!form.receipt" :fileSize="1024" v-model="form.receipt"
                    :file-type="fileType" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="对账函" prop="reconciliation">
                  <image-upload isRow :isShowTip="!form.reconciliation" :fileSize="1024" v-model="form.reconciliation"
                    :file-type="fileType" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
        <div class="followListTitle" v-if="!!followUps.length && type !== 'up'">跟进记录</div>
        <div class="followList" v-if="!!followUps.length && type !== 'up'">
          <div class="followItem" v-for="item in followUps.slice(0, followUpShow)" :key="item.id">
            <div class="followInfo">
              <div class="followInfo-title">
                {{ item.salesperson }}
                <span style="font-size: 12px; color: #999999; margin-left: 10px">{{ item.createTime }}</span>
                <span style="font-size: 12px; color: #999999; margin-left: 10px" v-if="!!item.payment">回款数目：{{
                  item.payment
                  }}元</span>
                <span style="font-size: 12px; color: #999999; margin-left: 10px" v-if="!!item.payment">预计回款日期：{{
                  item.expectedPaymentDate }}</span>
              </div>
              <div class="followInfo-con">{{ item.followInfo || item.salesperson + '于' + item.createTime + '创建了该审批' }}
              </div>
              <div style="padding-top: 10px">
                <ul class="el-upload-list el-upload-list--picture-card">
                  <li tabindex="0" class="el-upload-list__item is-success"
                    v-for="(itt, index) in fileFormat(item, 'archives')" :key="index">
                    <template v-if="typeFormat(itt) === 'pdf' || typeFormat(itt) === 'PDF'">
                      <img class="el-upload-list__item-thumbnail" src="~@/assets/images/pdf.png" alt="" />
                      <span class="el-upload-list__item-actions">
                        <span class="el-upload-list__item-preview" @click="handlePDFPreview(itt)"><i
                            class="el-icon-zoom-in"></i></span>
                        <span class="el-upload-list__item-preview" @click="handleDownload(itt)"><i
                            class="el-icon-download"></i></span>
                      </span>
                    </template>
                    <template v-else>
                      <img class="el-upload-list__item-thumbnail" :src="itt" alt="" />
                      <span class="el-upload-list__item-actions">
                        <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(itt)"><i
                            class="el-icon-zoom-in"></i></span>
                      </span>
                    </template>
                  </li>
                </ul>
                <ul class="el-upload-list el-upload-list--picture-card">
                  <li tabindex="0" class="el-upload-list__item is-success"
                    v-for="(itt, index) in fileFormat(item, 'contract')" :key="index">
                    <template v-if="typeFormat(itt) === 'pdf' || typeFormat(itt) === 'PDF'">
                      <img class="el-upload-list__item-thumbnail" src="~@/assets/images/pdf.png" alt="" />
                      <span class="el-upload-list__item-actions">
                        <span class="el-upload-list__item-preview" @click="handlePDFPreview(itt)"><i
                            class="el-icon-zoom-in"></i></span>
                        <span class="el-upload-list__item-preview" @click="handleDownload(itt)"><i
                            class="el-icon-download"></i></span>
                      </span>
                    </template>
                    <template v-else>
                      <img class="el-upload-list__item-thumbnail" :src="itt" alt="" />
                      <span class="el-upload-list__item-actions">
                        <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(itt)"><i
                            class="el-icon-zoom-in"></i></span>
                      </span>
                    </template>
                  </li>
                </ul>
                <ul class="el-upload-list el-upload-list--picture-card">
                  <li tabindex="0" class="el-upload-list__item is-success"
                    v-for="(itt, index) in fileFormat(item, 'chatHistory')" :key="index">
                    <template v-if="typeFormat(itt) === 'mp4' || typeFormat(itt) === 'MP4'">
                      <video ref="videoPlayer" width="100%" height="100%" :src="itt" controls></video>
                    </template>
                    <template v-else>
                      <img class="el-upload-list__item-thumbnail" :src="itt" alt="" />
                      <span class="el-upload-list__item-actions">
                        <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(itt)"><i
                            class="el-icon-zoom-in"></i></span>
                      </span>
                    </template>
                  </li>
                </ul>
                <ul class="el-upload-list el-upload-list--picture-card">
                  <li tabindex="0" class="el-upload-list__item is-success"
                    v-for="(itt, index) in fileFormat(item, 'receipt')" :key="index">
                    <template v-if="typeFormat(itt) === 'pdf' || typeFormat(itt) === 'PDF'">
                      <img class="el-upload-list__item-thumbnail" src="~@/assets/images/pdf.png" alt="" />
                      <span class="el-upload-list__item-actions">
                        <span class="el-upload-list__item-preview" @click="handlePDFPreview(itt)"><i
                            class="el-icon-zoom-in"></i></span>
                        <span class="el-upload-list__item-preview" @click="handleDownload(itt)"><i
                            class="el-icon-download"></i></span>
                      </span>
                    </template>
                    <template v-else>
                      <img class="el-upload-list__item-thumbnail" :src="itt" alt="" />
                      <span class="el-upload-list__item-actions">
                        <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(itt)"><i
                            class="el-icon-zoom-in"></i></span>
                      </span>
                    </template>
                  </li>
                </ul>
                <ul class="el-upload-list el-upload-list--picture-card">
                  <li tabindex="0" class="el-upload-list__item is-success"
                    v-for="(itt, index) in fileFormat(item, 'reconciliation')" :key="index">
                    <template v-if="typeFormat(itt) === 'pdf' || typeFormat(itt) === 'PDF'">
                      <img class="el-upload-list__item-thumbnail" src="~@/assets/images/pdf.png" alt="" />
                      <span class="el-upload-list__item-actions">
                        <span class="el-upload-list__item-preview" @click="handlePDFPreview(itt)"><i
                            class="el-icon-zoom-in"></i></span>
                        <span class="el-upload-list__item-preview" @click="handleDownload(itt)"><i
                            class="el-icon-download"></i></span>
                      </span>
                    </template>
                    <template v-else>
                      <img class="el-upload-list__item-thumbnail" :src="itt" alt="" />
                      <span class="el-upload-list__item-actions">
                        <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(itt)"><i
                            class="el-icon-zoom-in"></i></span>
                      </span>
                    </template>
                  </li>
                </ul>
                <div class="followInfo-file-audio" style="display: inline-flex; align-items: center; height: 148px"
                  v-for="(itt, idx) in fileFormat(item, 'tape')" :key="idx">
                  <audio ref="audioPlayer" :src="itt" controls></audio>
                </div>
              </div>
            </div>
          </div>
          <div class="moreBtn" @click="followUpShow = followUpShow === followUps.length ? 1 : followUps.length"
            v-if="followUps.length > 1">
            <span>{{ followUpShow === followUps.length ? '收起跟进记录' : '查看更多跟进记录' }}</span>
            <i :class="followUpShow === followUps.length ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
          </div>
        </div>
        <div class="followListTitle"
          v-if="(!type || type === 'info') && approvals.filter(ite => ite.status === 1).length > 0">
          审批记录</div>
        <div class="followList"
          v-if="(!type || type === 'info') && approvals.filter(ite => ite.status === 1).length > 0">
          <div class="followItem" v-for="item in approvals
            .filter(ite => ite.status === 1)
            .sort((a, b) => b.id - a.id)
            .slice(0, approvalShow)" :key="item.id">
            <div class="followInfo">
              <div class="followInfo-title">
                {{ item.approvalName }}
                <span style="font-size: 12px; color: #999999; margin-left: 10px">{{ item.createTime }}</span>
              </div>
              <div class="followInfo-con">
                {{ item.approvalInfo }}
                <i class="el-icon-edit" style="color: #2e73f3; cursor: pointer" @click="handleEdit(item)"
                  v-if="item.paymentId == paymentId && item.approvalId == userId && isAudit"></i>
              </div>
            </div>
          </div>
          <div class="moreBtn"
            @click="approvalShow = approvalShow === approvals.filter(ite => ite.status === 1).length ? 1 : approvals.filter(ite => ite.status === 1).length"
            v-if="approvals.filter(ite => ite.status === 1).length > 1">
            <span>{{ approvalShow === approvals.filter(ite => ite.status === 1).length ? '收起审批记录' : '查看更多审批记录' }}</span>
            <i
              :class="approvalShow === approvals.filter(ite => ite.status === 1).length ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
          </div>
        </div>
        <div class="followListTitle" v-if="!type || type === 'info'">审批信息</div>
        <div style="padding: 0 20px">
          <div class="approvalsList" v-if="!type || type === 'info'">
            <template v-for="(item, index) in unique(approvals)">
              <div class="approvalsItem" :class="{ active: !!item.status }" :key="item.id">
                <span>{{ item.approvalName }}</span>
                <div class="approvalsItemFlex">
                  <span>{{ !!item.status ? '已审批' : '未审批' }}</span>
                  <i class="el-icon-check" v-if="!!item.status"></i>
                  <i v-if="!item.status"></i>
                </div>
              </div>
              <i class="approvalsIcon el-icon-right" :key="'i' + item.id"
                v-if="index + 1 !== unique(approvals).length"></i>
            </template>
          </div>
          <el-form ref="form" :model="form" :rules="rules" label-width="100px" v-if="!type">
            <el-form-item label="审批备注" prop="approvalInfo">
              <div class="approvals-textarea"
                :style="{ 'border-bottom-width': paymentCustom.length > 0 ? '1px' : '0px' }">
                <el-input placeholder="请输入审批备注" v-model="form.approvalInfo">
                  <template slot="suffix">
                    <span class="approvals-textarea-button" @click="handleAddCustom(form.approvalInfo)"
                      v-if="form.approvalInfo">添加至自定义回复</span>
                  </template>
                </el-input>
                <div class="approvals-textarea-title" v-if="paymentCustom.length > 0">
                  自定义回复
                  <i :class="isCustom ? 'el-icon-circle-check' : 'el-icon-edit'" @click="isCustom = !isCustom">{{
                    isCustom ?
                    '保存' : '编辑' }}</i>
                </div>
                <div class="approvals-textarea-list" v-if="paymentCustom.length > 0">
                  <div class="approvals-textarea-item" v-for="(item, index) in paymentCustom" :key="index">
                    <span @click="handleSelectCustom(item)">{{ item }}</span>
                    <i class="el-icon-error" @click="handleDeleteCustom(index)" v-if="isCustom"></i>
                  </div>
                </div>
              </div>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button class="custom-dialog-btn primary" @click="open = false"
          v-if="type === 'follow' || type === 'info' || type === 'view'">关闭</el-button>
        <el-button class="custom-dialog-btn" @click="open = false" v-if="!type || type === 'up'">取 消</el-button>
        <el-button class="custom-dialog-btn primary" @click="handleSubmit" v-if="!type">确定</el-button>
        <el-button class="custom-dialog-btn primary" @click="handleUp" v-if="type === 'up'">确定</el-button>
      </div>
    </el-dialog>

    <el-dialog v-dialogDragBox :visible.sync="imgOpen" title="预览" width="1150px" class="custom-dialog" append-to-body>
      <div style="padding: 0 20px">
        <img :src="imgUrl" style="display: block; max-width: 100%; margin: 0 auto" />
      </div>
    </el-dialog>

    <!-- 预览图纸 -->
    <el-dialog v-dialogDragBox title="预览" :visible.sync="pdfOpen" width="1150px" class="custom-dialog" append-to-body>
      <div style="padding: 0 20px">
        <div style="display: flex; justify-content: space-between; align-items: center; padding-bottom: 10px"
          v-if="pdfCount > 1">
          <el-button type="primary" size="small" @click="pdfCurrent--" :disabled="pdfCurrent < 2">
            <i class="el-icon-arrow-left"></i>
            上一页
          </el-button>
          <span>{{ pdfCurrent }} / {{ pdfCount }}</span>
          <el-button type="primary" size="small" @click="pdfCurrent++" :disabled="pdfCurrent >= pdfCount">
            下一页
            <i class="el-icon-arrow-right"></i>
          </el-button>
        </div>
        <div class="page-pdf">
          <Pdf :src="pdfUrl" :page="pdfCurrent" @num-pages="pdfCount = $event" />
        </div>
      </div>
    </el-dialog>
    <!--合同、签收单、对账函等信息-->
    <el-dialog v-dialogDragBox :title="moreTitle" :visible.sync="moreOpen" width="1150px">
      <div style="padding-top: 10px">
        <ul class="el-upload-list el-upload-list--picture-card" v-if="moreType === 'receipt'">
          <li tabindex="0" class="el-upload-list__item is-success" v-for="(item, index) in fileFormat(info, 'receipt')"
            :key="index">
            <template v-if="typeFormat(item) === 'pdf' || typeFormat(item) === 'PDF'">
              <img class="el-upload-list__item-thumbnail" src="~@/assets/images/pdf.png" alt="" />
              <span class="el-upload-list__item-actions">
                <span class="el-upload-list__item-preview" @click="handlePDFPreview(item)"><i
                    class="el-icon-zoom-in"></i></span>
                <span class="el-upload-list__item-preview" @click="handleDownload(item)"><i
                    class="el-icon-download"></i></span>
              </span>
            </template>
            <template v-else>
              <img class="el-upload-list__item-thumbnail" :src="item" alt="" />
              <span class="el-upload-list__item-actions">
                <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(item)"><i
                    class="el-icon-zoom-in"></i></span>
              </span>
            </template>
          </li>
        </ul>
        <ul class="el-upload-list el-upload-list--picture-card" v-if="moreType === 'reconciliation'">
          <li tabindex="0" class="el-upload-list__item is-success"
            v-for="(item, index) in fileFormat(info, 'reconciliation')" :key="index">
            <template v-if="typeFormat(item) === 'pdf' || typeFormat(item) === 'PDF'">
              <img class="el-upload-list__item-thumbnail" src="~@/assets/images/pdf.png" alt="" />
              <span class="el-upload-list__item-actions">
                <span class="el-upload-list__item-preview" @click="handlePDFPreview(item)"><i
                    class="el-icon-zoom-in"></i></span>
                <span class="el-upload-list__item-preview" @click="handleDownload(item)"><i
                    class="el-icon-download"></i></span>
              </span>
            </template>
            <template v-else>
              <img class="el-upload-list__item-thumbnail" :src="item" alt="" />
              <span class="el-upload-list__item-actions">
                <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(item)"><i
                    class="el-icon-zoom-in"></i></span>
              </span>
            </template>
          </li>
        </ul>
        <ul class="el-upload-list el-upload-list--picture-card" v-if="moreType === 'contract'">
          <li tabindex="0" class="el-upload-list__item is-success" v-for="(item, index) in fileFormat(info, 'contract')"
            :key="index">
            <template v-if="typeFormat(item) === 'pdf' || typeFormat(item) === 'PDF'">
              <img class="el-upload-list__item-thumbnail" src="~@/assets/images/pdf.png" alt="" />
              <span class="el-upload-list__item-actions">
                <span class="el-upload-list__item-preview" @click="handlePDFPreview(item)"><i
                    class="el-icon-zoom-in"></i></span>
                <span class="el-upload-list__item-preview" @click="handleDownload(item)"><i
                    class="el-icon-download"></i></span>
              </span>
            </template>
            <template v-else>
              <img class="el-upload-list__item-thumbnail" :src="item" alt="" />
              <span class="el-upload-list__item-actions">
                <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(item)"><i
                    class="el-icon-zoom-in"></i></span>
              </span>
            </template>
          </li>
        </ul>
        <ul class="el-upload-list el-upload-list--picture-card" v-if="moreType === 'chatHistory'">
          <li tabindex="0" class="el-upload-list__item is-success"
            v-for="(item, index) in fileFormat(info, 'chatHistory')" :key="index">
            <template v-if="typeFormat(item) === 'mp4' || typeFormat(item) === 'MP4'">
              <video ref="videoPlayer" width="100%" height="100%" :src="item" controls></video>
            </template>
            <template v-else>
              <img class="el-upload-list__item-thumbnail" :src="item" alt="" />
              <span class="el-upload-list__item-actions">
                <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(item)"><i
                    class="el-icon-zoom-in"></i></span>
              </span>
            </template>
          </li>
        </ul>
        <div class="followInfo-file-audio" style="display: inline-flex; align-items: center; height: 148px"
          v-for="(item, idx) in fileFormat(info, 'tape')" :key="idx">
          <audio ref="audioPlayer" :src="item" controls v-if="moreType === 'tape'"></audio>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button class="custom-dialog-btn primary" @click="moreOpen = false">关 闭</el-button>
      </span>
    </el-dialog>
    <!--企业详情-->
    <company ref="company" />
    <!-- 应收明细 -->
     <el-dialog v-dialogDragBox title="应收明细" :visible.sync="viewOpen" width="90%" class="custom-dialog body-none">
      <receivableDetail ref="receivableDetail" :isPopup="true" :propParams="viewParams" />
     </el-dialog>
  </div>
</template>
<script>
import { paymentAarchivesDetail, paymentAarchivesFollowup, paymentAarchivesApproval, paymentAarchivesModify, paymentAarchivesApprovalDetail, paymentAarchivesList, enterprisesDetail, searchByName } from '@/api/payment'
import { getRemoteCompanyInfo } from '@/api/tender'
import { isNumber, isNumberLength } from '@/utils/validate'
import { getCustomerList } from '@/api/kingdee/customer'
import { getReceivableSummary } from '@/api/kingdee/receivable'
import Company from './company'
import { kingdee } from '@/minix'
import receivableDetail from '@/views/kingdee/receivable/list.vue'

export default {
  mixins: [kingdee],
  components: { Company, receivableDetail },
  data() {
    return {
      info: {},
      type: undefined,
      title: '',
      open: false,
      form: {},
      rules: {
        followInfo: [{ required: true, message: '请输入跟进详细', trigger: 'blur' }],
        payment: [
          { required: true, message: '请输入回款数目', trigger: 'blur' },
          { validator: isNumber, message: '输入有误请重新输入', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: 'blur' }
        ],
        expectedPaymentDate: [{ required: true, message: '请选择预计回款日期', trigger: 'blur' }],
        // archives: [{ required: true, message: '请上传照片', trigger: ['blur', 'change'] }],
        approvalInfo: [{ required: true, message: '请输入审批备注', trigger: 'blur' }]
      },
      approvals: [],
      followUps: [],
      fileType: ['png', 'PNG', 'jpg', 'JPG', 'jpeg', 'JPEG', 'pdf', 'PDF'],
      filesType: ['png', 'PNG', 'jpg', 'JPG', 'jpeg', 'JPEG', 'mp4', 'MP4'],
      fileTypes: ['mp3', 'MP3'],
      num: 0,
      processed: 0,
      payment: 0,
      paymentId: '',
      isAudit: false,
      expireTimeOPtion: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7 //如果没有后面的-8.64e7就是不可以选择今天的
        }
      },
      // pdf预览
      pdfOpen: false,
      pdfCurrent: 1,
      pdfCount: 0,
      pdfUrl: '',
      imgOpen: false,
      imgUrl: '',
      paymentCustom: [],
      isCustom: false,
      // 跟进记录默认显示
      followUpShow: 1,
      // 审批记录默认显示
      approvalShow: 1,
      // 合同、签收单、对账函等信息预览
      moreTitle: '',
      moreType: '',
      moreOpen: false,
      // 是否修改备注
      remark: '',
      isEditRemark: false,
      arrData: [],
      kingdeeInfo: {},
      kingdeeTitle: undefined,
      key: 1,
      customerNumber: '',
      useOrg: '',
      statusOptions: [
        { value: 0, label: '初始', class: '' },
        { value: 1, label: '跟进中', class: 'color-orange' },
        { value: 2, label: '结款', class: 'color-info' },
        { value: -10, label: '停用', class: 'color-red' },
        { value: 3, label: '起诉中', class: 'color-pink' }
      ],
      viewOpen: false,
      viewParams: {}
    }
  },
  watch: {
    open(newVal, oldVal) {
      if (newVal == false) {
        this.approvals = []
        this.followUps = []
        this.arrData = []
        this.num = 0
      }
    }
  },
  computed: {
    userId() {
      return this.$store.getters.info.userId
    },
    companyId() {
      return this.$store.state.user.companyId
    }
  },
  mounted() {
    // 点击remarkInput之外的地方，取消编辑
    document.addEventListener('click', this.handleClickOutside)
  },
  methods: {
    // 判断文件类型
    typeFormat(file) {
      const index = file.lastIndexOf('?')
      if (index !== -1) file = file.slice(0, index)
      const index1 = file.lastIndexOf('.')
      return file.substr(index1 + 1)
    },
    // 预览PDF
    handlePDFPreview(file) {
      this.pdfUrl = file
      this.pdfCurrent = 1
      this.pdfOpen = true
    },
    // 下载
    handleDownload(item) {
      const filename = item.split('/').pop().split('?')[0]
      const xhr = new XMLHttpRequest()
      xhr.open('GET', item, true)
      xhr.responseType = 'blob'
      xhr.onload = () => {
        if (xhr.status === 200) {
          const blob = new Blob([xhr.response], { type: 'application/octet-stream' })
          const url = URL.createObjectURL(blob)
          const link = document.createElement('a')
          link.href = url
          link.download = filename
          link.click()
          URL.revokeObjectURL(url)
        }
      }
      xhr.send()
    },
    // 预览图片
    handlePictureCardPreview(file) {
      this.imgUrl = file
      this.imgOpen = true
    },
    // 表单重置
    reset() {
      this.form = {
        approvalId: undefined, // 审批ID
        approvalInfo: undefined, // 审批信息
        archives: undefined, // 档案（附件）
        followInfo: undefined, // 跟进信息
        payment: undefined, // 回款数目
        expectedPaymentDate: undefined, //预计回款日期
        receivable: undefined, // 应收尾款
        paymentId: undefined // 款项ID
      }
      this.resetForm('form')
    },
    // 查看详情
    async getInfo(row = {}, type, isAudit = false) {
      const paymentId = row.id
      this.info = {}
      this.remark = ''
      this.isEditRemark = false
      const res = await paymentAarchivesDetail({ paymentId })
      const { code, data, msg } = res
      if (code === 200) {
        this.num++
        this.arrData.push(data)
        this.info = this.arrData[0]
        if (this.num === 1) {
          this.key = Math.random()
          this.isAudit = isAudit
          this.paymentId = paymentId
          this.type = type
          this.title = type === 'follow' ? '跟进详细' : type === 'info' ? '查看详情' : type === 'view' ? '查看详情' : '审批提交'
          if (!type) {
            this.reset()
            const approvalId = this.$store.getters.info.userId || undefined
            const self = data.approvals.find(item => item.approvalId === approvalId)
            if (!self) {
              this.$message.error('您没有审批权限')
              return
            } else this.form.approvalId = self.id
          }
        }
        if (data.baseId) {
          this.getInfo({ id: data.baseId })
        } else {
          this.getCustom()
          this.followUpShow = this.approvalShow = 1
          this.open = true
          this.kingdeeInfo = {}
          this.kingdeeTitle = undefined
          if (this.companyId == 14) {
            this.useOrg = ['100073']
            this.$nextTick(() => {
              this.handleQueryKingdee()
            })
          }
        }
        this.approvals = this.approvals.concat(data.approvals)
        this.followUps = this.followUps.concat(data.followUps)
      } else this.$message.error(msg)
    },
    // 修改审批
    handleEdit(item) {
      this.type = ''
      this.title = '修改审批'
      this.reset()
      this.form.approvalId = item.id
      this.form.approvalInfo = item.approvalInfo
      const index = this.approvals.findIndex(ite => ite.id === item.id)
      this.approvals.splice(index, 1)
    },
    // 提交审批
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          paymentAarchivesApproval(this.form).then(async res => {
            const { code, msg } = res
            if (code === 200) {
              const query = { pageNum: 1, pageSize: 10, identity: 'approval', approvalStatus: 1 }
              const resTotal = await paymentAarchivesList(query)
              const newQuery = { ...query, pageSize: resTotal.total }
              const res = await paymentAarchivesList(newQuery)
              const { code, rows, total } = res
              if (code === 200) {
                if (!!rows.length) {
                  const userId = this.$store.getters.info.userId
                  await Promise.all(
                    rows.map(async item => {
                      const audit = await paymentAarchivesApprovalDetail({ paymentId: item.id })
                      const arr = audit.data
                      if (!!arr.length) {
                        arr.sort((a, b) => a.priority - b.priority)
                        const index = arr.findIndex(item => item.approvalId === userId)
                        if (index === 0) item.isAudit = arr[index].status === 0
                        else item.isAudit = arr[index - 1].status === 1 && arr[index].status === 0
                        item.approval = arr
                      }
                    })
                  )
                }
              }
              const newRows = rows.filter(item => item.isAudit) || []
              if (!!newRows.length) {
                const row = newRows?.[0]
                if (row) {
                  this.$message.info('你还有' + newRows.length + '条待审批')
                  this.approvals = []
                  this.followUps = []
                  this.arrData = []
                  this.num = 0
                  this.getInfo(row)
                } else {
                  this.$message.success('成功提交审批')
                  this.open = false
                  this.$parent.refreshList()
                }
              } else {
                this.$message.success('成功提交审批')
                this.open = false
                this.$parent.refreshList()
              }
            } else this.$message.error(msg)
          })
        }
      })
    },
    // 关闭弹窗
    handleClose() {
      this.open = false
      this.$parent.refreshList()
    },
    // 格式化跟进附件
    fileFormat(item, type) {
      if (type == 'archives') {
        const { archives_oss, archives } = item
        if (archives_oss || archives) {
          const obj = item.archives_oss || item.archives || ''
          const arr = obj.split(',') || []
          if (!item.archives_oss) {
            return arr.map(item => {
              return this.imgPath + item
            })
          } else return arr
        }
        return []
      } else if (type == 'contract') {
        const { contract_oss, contract } = item
        if (contract_oss || contract) {
          const obj = item.contract_oss || item.contract || ''
          const arr = obj.split(',') || []
          if (!item.contract_oss) {
            return arr.map(item => {
              return this.imgPath + item
            })
          } else return arr
        }
        return []
      } else if (type == 'receipt') {
        const { receipt_oss, receipt } = item
        if (receipt_oss || receipt) {
          const obj = item.receipt_oss || item.receipt || ''
          const arr = obj.split(',') || []
          if (!item.receipt_oss) {
            return arr.map(item => {
              return this.imgPath + item
            })
          } else return arr
        }
        return []
      } else if (type == 'reconciliation') {
        const { reconciliation_oss, reconciliation } = item
        if (reconciliation_oss || reconciliation) {
          const obj = item.reconciliation_oss || item.reconciliation || ''
          const arr = obj.split(',') || []
          if (!item.reconciliation_oss) {
            return arr.map(item => {
              return this.imgPath + item
            })
          } else return arr
        }
        return []
      } else if (type == 'chatHistory') {
        const { chatHistory_oss, chatHistory } = item
        if (chatHistory_oss || chatHistory) {
          const obj = item.chatHistory_oss || item.chatHistory || ''
          const arr = obj.split(',') || []
          if (!item.chatHistory_oss) {
            return arr.map(item => {
              return this.imgPath + item
            })
          } else return arr
        }
        return []
      } else if (type == 'tape') {
        const { tape_oss, tape } = item
        if (tape_oss || tape) {
          const obj = item.tape_oss || item.tape || ''
          const arr = obj.split(',') || []
          if (!item.tape_oss) {
            return arr.map(item => {
              return this.imgPath + item
            })
          } else return arr
        }
        return []
      }
    },
    // 跟进详细
    getUp(row = {}) {
      this.reset()
      this.type = 'up'
      this.title = '跟进详细'
      this.form.paymentId = row.id
      this.form.receivable = row.receivable
      this.payment = row.payment
      this.processed = parseFloat((parseFloat(row.receivable) - parseFloat(row.payment)).toFixed(5))
      this.kingdeeInfo = {}
      this.kingdeeTitle = undefined
      if (this.companyId == 14) {
        this.$nextTick(() => {
          this.handleQueryKingdee()
        })
      }
      this.open = true
    },
    // 提交跟进详细
    handleUp() {
      this.$refs.form.validate(valid => {
        if (valid) {
          paymentAarchivesFollowup(this.form).then(res => {
            const { code, msg } = res
            if (code === 200) {
              this.$message.success('成功添加跟进')
              this.open = false
              this.$emit('refresh')
            } else this.$message.error(msg)
          })
        }
      })
    },
    // 数组去重
    unique(arr) {
      let newArr = JSON.parse(JSON.stringify(arr))
      for (let i = 0; i < newArr.length; i++) {
        for (let j = i + 1; j < newArr.length; j++) {
          if (newArr[i].approvalId == newArr[j].approvalId) {
            newArr.splice(j, 1)
            j--
          }
        }
      }
      return newArr
    },
    // 回款数目
    handleChangePayment() {
      const { receivable, payment } = this.form
      if (receivable && payment) {
        if (parseFloat(receivable) < parseFloat(payment)) {
          this.form.receivable = ''
        } else {
          this.form.receivable = parseFloat((parseFloat(receivable) - parseFloat(payment)).toFixed(2))
        }
      }
    },
    // 添加至自定义回复缓存
    handleAddCustom(info) {
      const custom = localStorage.getItem('paymentCustom' + this.userId) || '[]'
      const customArr = JSON.parse(custom)
      customArr.push(info)
      localStorage.setItem('paymentCustom' + this.userId, JSON.stringify(customArr))
      this.paymentCustom = customArr
    },
    // 获取自定义回复缓存
    getCustom() {
      const custom = localStorage.getItem('paymentCustom' + this.userId) || '[]'
      this.paymentCustom = JSON.parse(custom)
    },
    // 删除自定义回复缓存
    handleDeleteCustom(index) {
      const custom = localStorage.getItem('paymentCustom' + this.userId) || '[]'
      const customArr = JSON.parse(custom)
      customArr.splice(index, 1)
      localStorage.setItem('paymentCustom' + this.userId, JSON.stringify(customArr))
      this.paymentCustom = customArr
    },
    // 选择自定义回复
    handleSelectCustom(item) {
      if (this.isCustom) return
      if (this.form.approvalInfo) this.form.approvalInfo += item
      else this.form.approvalInfo = item
    },
    // 查看更多
    handleMore(type) {
      this.moreTitle = type === 'contract' ? '合同' : type === 'receipt' ? '签收单' : type === 'reconciliation' ? '对账函' : type === 'tape' ? '录音' : '聊天记录'
      this.moreType = type
      this.moreOpen = true
    },
    // 编辑备注
    handleEditRemark() {
      this.isEditRemark = true
      this.remark = this.info.remark
      this.$nextTick(() => {
        this.$refs.remarkInput.focus()
      })
    },
    // 保存备注
    handleSaveRemark() {
      if (!this.remark) return
      const { customerId, customerName, deliveryTime, receivable, id } = this.info
      let dataJson = { customerId, enterpriseId: undefined, customerName, deliveryTime, receivable, paymentId: id, remark: this.remark }
      if (this.info.customerId == 0) {
        dataJson.enterpriseId = 0
        this.handleSubmitRemark(dataJson)
      } else {
        const options = {
          lock: true,
          text: '搜索中…',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        }
        let dataLoading = this.$loading(options)
        searchByName({ name: customerName }).then(res => {
          const { code, data, msg } = res
          if (code == 200) {
            if (data && data.gid) {
              this.$nextTick(() => {
                dataLoading.close()
              })
              dataJson.customerId = data.gid
              dataJson.customerName = data.name
              dataJson.refresh = false
              dataJson.enterpriseId = data.id
              this.handleSubmitRemark(dataJson)
            } else {
              dataJson.refresh = undefined
              dataJson.enterpriseId = undefined
              getRemoteCompanyInfo({ companyName: customerName }).then(res => {
                const { code, msg, data } = res
                if (code === 200) {
                  this.$nextTick(() => {
                    dataLoading.close()
                  })
                  if (data && data.hasOwnProperty('companyGid') && data.companyGid) {
                    dataJson.customerId = data.companyGid
                    dataJson.customerName = data.companyName
                    this.handleSubmitRemark(dataJson)
                  } else {
                    this.$nextTick(() => {
                      dataLoading.close()
                    })
                    this.$alert(`未查询到“<span style='color:#ff0000'>${input}</span>”相关公司信息！<br />请检查公司名称是否正确，请输入公司全称！`, '系统提示', { dangerouslyUseHTMLString: true })
                  }
                } else {
                  this.$nextTick(() => {
                    dataLoading.close()
                  })
                  this.$alert(`未查询到“<span style='color:#ff0000'>${input}</span>”相关公司信息！<br />请检查公司名称是否正确，请输入公司全称！`, '系统提示', { dangerouslyUseHTMLString: true })
                }
              })
            }
          }
        })
      }
    },
    // 提交备注
    handleSubmitRemark(data = {}) {
      if (!data) return
      paymentAarchivesModify(data).then(res => {
        const { code, msg } = res
        if (code === 200) {
          this.$message.success('保存成功')
          this.$set(this.info, 'remark', this.remark)
          this.isEditRemark = false
        } else this.$message.error(msg)
      })
    },
    // 点击remarkInput之外的地方，取消编辑,点击输入框不取消
    handleClickOutside(e) {
      if (!this.isEditRemark) return
      this.$nextTick(() => {
        const remarkInput = this.$refs.remarkInput.$el
        if (!remarkInput.contains(e.target) && e.target !== remarkInput) {
          this.isEditRemark = false
          this.remark = this.info.remark
        }
      })
    },
    // 金蝶应收汇总
    handleQueryKingdee() {
      const name2 = this.info.customerName
      getCustomerList({ name2 }).then(res => {
        const { code, data } = res
        if (code === 200) {
          const { data: customerList } = data
          if (!!customerList.length) {
            const Number = customerList[0].Number
            this.customerNumber = Number
            const queryParams = {
              beginDate: this.parseTime(new Date('2023-08-01'), '{y}-{m}-{d}'),
              endDate: this.parseTime(new Date(), '{y}-{m}-{d}'),
              customerNumber: Number,
              settleOrg: this.useOrg.join(',')
            }
            getReceivableSummary(queryParams).then(res => {
              const { code, data } = res
              if (code === 200) {
                const { data: list } = data
                if (!!list.length) {
                  const CurrencyForName = list[0].CurrencyForName
                  this.kingdeeInfo = list[list.length - 1]
                  this.kingdeeInfo.CurrencyForName = CurrencyForName
                } else this.kingdeeTitle = '金蝶内无此客户应收汇总数据'
              } else {
                this.$message.error(msg)
              }
            })
          } else this.kingdeeTitle = '金蝶内无此客户'
        } else {
          this.$message.error(msg)
        }
      })
    },
    // 选择使用组织
    handleUseOrg(value) {
      if (!value) return
      const loading = this.$loading({
        lock: true,
        text: '查询中…',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      this.kingdeeInfo = {}
      this.kingdeeTitle = undefined
      const queryParams = {
        beginDate: this.parseTime(new Date('2023-08-01'), '{y}-{m}-{d}'),
        endDate: this.parseTime(new Date(), '{y}-{m}-{d}'),
        customerNumber: this.customerNumber,
        settleOrg: value.join(',') || this.useOrg.join(',')
      }
      getReceivableSummary(queryParams).then(res => {
        const { code, data } = res
        if (code === 200) {
          const { data: list } = data
          if (!!list.length) {
            const CurrencyForName = list[0].CurrencyForName
            this.kingdeeInfo = list[list.length - 1]
            this.kingdeeInfo.CurrencyForName = CurrencyForName
          } else this.kingdeeTitle = '金蝶内无此客户应收汇总数据'
        } else {
          this.$message.error(msg)
        }
      }).finally(() => {
        loading.close()
      })
    },
    // 查看企业详情
    handleShow(row) {
      const enterpriseId = row.customerId
      enterprisesDetail({ enterpriseId }).then(res => {
        this.$refs.company.getInfo(res.data)
      })
    },
    // 查看应收明细
    handleView() {
      this.viewParams = {
        useOrg: this.useOrg,
        customerNumber: this.customerNumber
      }
      this.viewOpen = true
      this.$nextTick(() => {
        this.$refs.receivableDetail.handleResetQuery()
      })
    }
  }
}
</script>

<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';

.approvalsList {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  margin: 0 -10px 10px;

  .approvalsItem {
    display: inline-flex;
    align-items: center;
    justify-content: space-between;
    border-radius: 50px;
    min-width: 160px;
    height: 48px;
    background-color: #ecedf0;
    border: 1px solid #d8e1e9;
    padding: 3px 3px 3px 15px;
    overflow: hidden;
    margin: 10px;

    .approvalsImg {
      width: 42px;
      height: 42px;
      border-radius: 50%;
    }

    .approvalsItemFlex {
      display: inline-flex;
      align-items: center;
      padding-right: 12px;

      i {
        display: inline-flex;
        align-items: center;
        font-size: 18px;
        width: 18px;
        height: 18px;
        margin-left: 8px;
      }
    }

    &.active {
      background-color: #e5eeff;
      border-color: $blue;
      color: $blue;
    }
  }
}

.followList {
  padding: 0 20px;
  background-color: #f7f9fc;

  &Title {
    padding: 20px;
    font-size: 16px;

    &.flex {
      display: flex;
      align-items: center;
    }

    .followListTitleTip {
      font-size: 14px;
      color: #999999;

      &:before {
        content: '（';
      }

      &:after {
        content: '）';
      }
    }
  }

  .followItem {
    display: flex;
    padding-top: 15px;
    padding-bottom: 5px;
    border-bottom: 1px solid #e2e6f3;

    &:last-child {
      border-bottom: 0;
    }

    .followImg {
      width: 40px;
      height: 40px;
      border: 1px solid #f7f7f7;
      border-radius: 50%;
      flex-shrink: 0;
      margin-right: 10px;
    }

    .followInfo {
      width: 100%;
      display: flex;
      flex-direction: column;

      &-title {
        display: inline-block;
        width: 100%;
        line-height: 40px;
        color: $info;
      }

      &-con {
        font-weight: normal;
        line-height: 2em;
        color: $font;
      }

      &-file {
        display: inline-flex;
        align-items: center;
        flex-wrap: wrap;
        margin-top: 10px;

        &-img {
          width: 100px;
          height: 100px;
          margin-right: 10px;
          margin-bottom: 10px;
        }

        &-audio {
          width: 300px;
          height: 50px;
          margin-right: 10px;
          margin-bottom: 10px;
        }

        &-video {
          width: 300px;
          height: 300px;
          margin-right: 10px;
          margin-bottom: 10px;
        }
      }
    }
  }
}

::v-deep {
  .image-slot {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    background: #f5f7fa;
    color: #909399;
  }

  .el-form {
    .el-form-item__label {
      line-height: 20px;
      min-height: 40px;
      display: inline-flex;
      align-items: center;
      font-weight: normal;
      color: $disabled;
      text-align: left;
    }
  }
}

.infoBox {
  padding: 20px;
  background-color: #f7f9fc;
  font-size: 14px;
  line-height: 32px;

  span {
    color: $info;
  }

  b {
    font-weight: normal;
    color: $font;

    &.price {
      color: $red;
    }
  }

  .inline-flex {
    margin-right: 20px;
  }
}

.approvals-textarea {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  background-color: #f8f9fb;

  .el-input {
    width: calc(100% + 2px);
    margin: -1px -1px 0;
  }

  &-button {
    display: inline-block;
    font-size: 12px;
    line-height: 32px;
    padding: 0 10px;
    background-color: #e0ebff;
    border-radius: 5px;
    color: $blue;
    cursor: pointer;

    &:hover {
      background-color: $blue;
      color: #ffffff;
    }
  }

  &-title {
    display: flex;
    align-items: center;
    padding: 0 10px;
    font-size: 12px;
    line-height: 30px;
    color: $disabled;

    i {
      cursor: pointer;
      margin-left: 10px;

      &:hover {
        color: $blue;
      }
    }
  }

  &-list {
    display: flex;
    flex-wrap: wrap;
    padding: 0 10px 10px;
  }

  &-item {
    cursor: pointer;
    display: inline-block;
    font-size: 12px;
    line-height: 30px;
    padding: 0 10px;
    background-color: #e5e5e5;
    color: $font;
    border-radius: 5px;
    position: relative;

    &:hover {
      background-color: #e0ebff;
      color: $blue;

      i {
        color: $font;
      }
    }

    i {
      font-size: 16px;
      position: absolute;
      right: -5px;
      top: -5px;

      &:hover {
        color: $red;
      }
    }
  }

  &-item+&-item {
    margin-left: 10px;
  }
}

.moreBtn {
  padding: 20px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: $blue;

  i {
    font-size: 16px;
  }
}

.remark-input {
  flex: 1;
  overflow: hidden;
  position: relative;

  .remark-save {
    position: absolute;
    right: 10px;
    bottom: 10px;
  }
}

.isLink {
  color: $blue !important;
  cursor: pointer;

  &:hover {
    text-decoration: underline;
  }
}

.color-disabled {
  color: #999999;
}

.color-info {
  color: #666666;
}

.color-red {
  color: #ec2454;
}

.color-success {
  color: #67c23a;
}

.color-blue {
  color: #2e73f3;
}

.color-orange {
  color: #f35d09;
}

.color-pink {
  color: #f43f3f;
}
.body-none{
  ::v-deep .el-dialog__body{
    padding: 0 0 15px!important;
  }
}
</style>
