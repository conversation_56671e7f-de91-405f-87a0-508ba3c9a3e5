<template>
  <div class="newBox bgcf9 vh-85" v-if="checkPermi(['sys:payment:list'])">
    <div class="custom-search">
      <div class="flex" :style="{ justifyContent: showSearch ? 'space-between' : 'flex-end' }">
        <el-form :model="queryParams" ref="queryForm" size="small" v-show="showSearch" :inline="true" @submit.native.prevent>
          <el-form-item label="组织" prop="useOrg" v-if="companyId == 14">
            <el-select v-model="useOrg" placeholder="请选择使用组织" @change="handleUseOrg" size="small">
              <el-option v-for="org in ApplicationOrgNumber" :key="org.value" :label="org.label" :value="org.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="客户名称" prop="customerName">
            <el-input v-model="queryParams.customerName" placeholder="请输入客户名称" clearable @keyup.enter.native="handleQuery" size="small" style="width: 200px" />
          </el-form-item>
          <el-form-item label="业务员" prop="salesperson">
            <el-input v-model="queryParams.salesperson" placeholder="请输入业务员" clearable @keyup.enter.native="handleQuery" size="small" style="width: 200px" />
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select v-model="queryParams.status" placeholder="请选择状态" clearable @change="handleQuery" style="width: 200px">
              <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
            <el-button type="primary" icon="el-icon-plus" size="small" @click="handleAdd">新增</el-button>
          </el-form-item>
          <el-form-item>
            <el-select v-model="queryParams.identity" placeholder="请选择操作人身份" @change="handleChangeIdentity" style="width: 200px">
              <el-option v-for="item in identityOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="审批状态" prop="approvalStatus" v-if="queryParams.identity === 'approval'">
            <el-select v-model="queryParams.approvalStatus" placeholder="请选择审批状态" clearable @change="handleQuery" style="width: 200px">
              <el-option v-for="item in approvalStatusSelect" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-form>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
      </div>
    </div>

    <div class="tableBox">
      <el-table v-loading="loading" ref="table" :height="tableHeight" :key="key" stripe :data="list" row-key="id" class="custom-table" v-if="list.length">
        <el-table-column align="center" type="index" label="序号" v-if="columns[0].visible"></el-table-column>
        <el-table-column align="center" prop="customerName" label="客户名称" show-overflow-tooltip v-if="columns[1].visible">
          <template slot-scope="{ row }">
            <i class="quick-serial" v-if="row.customerId === 0"></i>
            <span class="default" :class="row.customerId !== 0 ? 'table-link' : ''" @click="row.customerId !== 0 ? handleShow(row) : ''">{{ row.customerName }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="debtMode" label="欠款方式" :formatter="debeModeFormat" show-overflow-tooltip v-if="columns[2].visible"></el-table-column>
        <el-table-column align="center" prop="salesperson" label="业务员" show-overflow-tooltip v-if="columns[3].visible"></el-table-column>
        <el-table-column align="center" prop="createTime" label="创建日期" show-overflow-tooltip v-if="columns[4].visible"></el-table-column>
        <el-table-column align="center" prop="receivable" label="应收尾款" show-overflow-tooltip v-if="columns[5].visible">
          <template slot-scope="{ row }">{{ row.receivable + '元' }}</template>
        </el-table-column>
        <el-table-column align="center" prop="kingdeeInfo" label="期末余额" show-overflow-tooltip v-if="columns[18].visible && companyId == 14">
          <template slot-scope="{ row }">
            <div class="kingdee-balance-wrapper">
              <div class="balance-content">
                <i class="el-icon-loading" v-if="row.loading"></i>
                <span v-else-if="!row.kingdeeRefreshing">{{ row.kingdeeInfo && row.kingdeeInfo.leftAmount ? row.kingdeeInfo.leftAmount + '元' : '--' }}</span>
                <span v-else>--</span>
              </div>
              <i :class="row.kingdeeRefreshing ? 'el-icon-loading refresh-loading' : 'el-icon-refresh refresh-icon'" @click="refreshKingdeeData(row)" :title="row.kingdeeRefreshing ? '刷新中...' : '刷新期末余额'"></i>
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="payment" label="回款数目" show-overflow-tooltip v-if="columns[6].visible">
          <template slot-scope="{ row }">{{ row.payment + '元' }}</template>
        </el-table-column>
        <el-table-column align="center" label="跟进详细" v-if="columns[7].visible">
          <template slot-scope="{ row }">
            <span class="table-link default" @click="handleDetail(row, 'follow')">查看详情&gt;</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="contract" label="合同" v-if="columns[8].visible">
          <template slot-scope="{ row }">
            <span class="table-link default" :class="{ disabled: !row.contract }" @click="handleView(row, 'contract')">查看详情&gt;</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="receipt" label="签收单" v-if="columns[9].visible">
          <template slot-scope="{ row }">
            <span class="table-link default" :class="{ disabled: !row.receipt }" @click="handleView(row, 'receipt')">查看详情&gt;</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="reconciliation" label="对账函" v-if="columns[10].visible">
          <template slot-scope="{ row }">
            <span class="table-link default" :class="{ disabled: !row.reconciliation }" @click="handleView(row, 'reconciliation')">查看详情&gt;</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="tape" label="录音" v-if="columns[11].visible">
          <template slot-scope="{ row }">
            <span class="table-link default" :class="{ disabled: !row.tape }" @click="handleTape(row, 'tape')">查看详情&gt;</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="chatHistory" label="聊天记录" v-if="columns[12].visible">
          <template slot-scope="{ row }">
            <span class="table-link default" :class="{ disabled: !row.chatHistory }" @click="handleTape(row, 'chatHistory')">查看详情&gt;</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="deliveryTime" label="发货日期" show-overflow-tooltip v-if="columns[13].visible"></el-table-column>
        <el-table-column align="center" prop="status" label="状态" width="85" v-if="columns[14].visible">
          <template slot-scope="{ row }">
            <div v-html="statusFormat(row)"></div>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="status" label="审批状态" width="85" v-if="columns[15].visible">
          <template slot-scope="{ row }">
            <el-tooltip class="item" effect="dark" :disabled="row.status == 0">
              <div slot="content">
                <div v-html="approvalFormat(row)"></div>
              </div>
              <div v-html="approvalStatusFormat(row)"></div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="updateTime" label="更新时间" show-overflow-tooltip width="100" v-if="columns[16].visible"></el-table-column>
        <el-table-column align="center" label="审批时间" width="100" show-overflow-tooltip v-if="columns[17].visible">
          <template slot-scope="{ row }">
            <!-- <span>{{ row.approval && row.approval.length > 1 ? row.approval[row.approval.length - 1].updateTime : '--'}}</span> -->
            <span>{{ row.approval && row.approval.length > 0 ? (row.approval.findLast(item => item.updateTime) ? row.approval.findLast(item => item.updateTime).updateTime : '--') : '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作" :width="queryParams.identity === 'creator' ? '330px' : hasManagerPermission ? '300px' : '220px'">
          <template slot-scope="{ row }">
            <template v-if="queryParams.identity === 'approval'">
              <template v-if="row.isAudit && row.approvalStatus !== 2">
                <el-button class="table-btn" @click="handleDetail(row, 'view')" v-if="row.status === 0">查看详情</el-button>
                <el-button class="table-btn primary" @click="handleDetail(row, '')" v-else>去审批</el-button>
              </template>
              <template v-if="!row.isAudit || row.approvalStatus === 2">
                <el-button class="table-btn" v-if="row.approvalStatus === 2" @click="handleDetail(row, 'info', true)">查看详情</el-button>
                <el-button class="table-btn primary" v-else-if="row.approvalStatus === 1" @click="handleDetail(row, '')">去审批</el-button>
                <el-button class="table-btn" v-else @click="handleDetail(row, 'info', true)">查看审批</el-button>
              </template>
              <!-- 管理操作按钮 -->
              <template v-if="hasManagerPermission">
                <el-button class="table-btn primary" v-if="row.approvalStatus === 2 && row.status === 1" @click="handleDone(row)">结清</el-button>
                <el-dropdown @command="handleDropdown" v-if="row.status != 2 && row.status != -10 && row.status != 3">
                  <el-button class="table-btn danger">
                    更多操作
                    <i class="el-icon-arrow-down el-icon--right"></i>
                  </el-button>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item :command="handleCommand(row, 'stop')">停用</el-dropdown-item>
                    <el-dropdown-item :command="handleCommand(row, 'prosecute')">起诉中</el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
                <el-button class="table-btn primary" v-if="row.status == -10 || row.status == 3" @click="handleDone(row, 0)">启用</el-button>
              </template>
            </template>
            <template v-if="queryParams.identity === 'sale'">
              <el-button class="table-btn primary" @click="handleUp(row)" v-if="row.status === 0 || row.approvalStatus === 2">提交跟进信息</el-button>
              <el-button class="table-btn" @click="handleDetail(row, 'info')" v-if="row.status !== 0">查看详情</el-button>
              <!-- 管理操作按钮 -->
              <template v-if="hasManagerPermission">
                <el-button class="table-btn primary" v-if="row.approvalStatus === 2 && row.status === 1" @click="handleDone(row)">结清</el-button>
                <el-dropdown @command="handleDropdown" v-if="row.status != 2 && row.status != -10 && row.status != 3">
                  <el-button class="table-btn danger">
                    更多操作
                    <i class="el-icon-arrow-down el-icon--right"></i>
                  </el-button>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item :command="handleCommand(row, 'stop')">停用</el-dropdown-item>
                    <el-dropdown-item :command="handleCommand(row, 'prosecute')">起诉中</el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
                <el-button class="table-btn primary" v-if="row.status == -10 || row.status == 3" @click="handleDone(row, 0)">启用</el-button>
              </template>
            </template>
            <template v-if="queryParams.identity === 'creator'">
              <el-button class="table-btn primary" @click="handleUp(row)" v-if="row.status === 0 || row.approvalStatus === 2">提交跟进信息</el-button>
              <el-button class="table-btn orange" v-if="row.creator == createName && row.approvalStatus !== 1" @click="handleUpdate(row, 'update')">修改信息</el-button>
              <el-button class="table-btn" v-if="row.status !== 0" @click="handleDetail(row, 'info')">查看详情</el-button>
              <el-button class="table-btn primary" v-if="(row.creator == createName || hasManagerPermission) && row.approvalStatus === 2 && row.status === 1" @click="handleDone(row)">结清</el-button>
              <el-dropdown @command="handleDropdown" v-if="(row.creator == createName || hasManagerPermission) && row.status != 2 && row.status != -10 && row.status != 3">
                <el-button class="table-btn danger">
                  更多操作
                  <i class="el-icon-arrow-down el-icon--right"></i>
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item :command="handleCommand(row, 'stop')">停用</el-dropdown-item>
                  <el-dropdown-item :command="handleCommand(row, 'prosecute')">起诉中</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
              <el-button class="table-btn primary" v-if="(row.creator == createName || hasManagerPermission) && (row.status == -10 || row.status == 3)" @click="handleDone(row, 0)">启用</el-button>
            </template>
            <template v-if="queryParams.identity === ''">
              <el-button class="table-btn danger" @click="handleDone(row, -10)" v-if="row.status != 2 && row.status != -10 && row.status != 3">停用</el-button>
              <el-button class="table-btn primary" @click="handleDone(row, 0)" v-if="row.status == -10">启用</el-button>
              <el-button class="table-btn" @click="handleDetail(row, 'view')">查看详情</el-button>
            </template>
          </template>
        </el-table-column>
      </el-table>
      <el-empty v-if="!list.length" description="暂无数据" />
      <div class="custom-pagination">
        <pagination v-show="total > 0" :total="total" :pageSizes.sync="pageSizes" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" local="paymentNewPageSize" @pagination="getList" />
      </div>
    </div>
    <!--新增-->
    <create ref="create" @refresh="refreshList" />
    <!--详情-->
    <detail ref="detail" @refresh="refreshList" />
    <!--查看合同、签收单、对账函详情-->
    <el-image-viewer v-if="imgViewerOpen" :on-close="closeImgViewer" :url-list="imgList" />
    <!--企业详情-->
    <company ref="company" />

    <el-dialog v-dialogDragBox :title="fileTitle" :visible.sync="fileOpen" width="1150px" class="custom-dialog" append-to-body>
      <div style="padding: 0 20px">
        <ul class="el-upload-list el-upload-list--picture-card">
          <li tabindex="0" class="el-upload-list__item is-success" v-for="(item, index) in fileList" :key="index">
            <template v-if="typeFormat(item) === 'pdf' || typeFormat(item) === 'PDF'">
              <img class="el-upload-list__item-thumbnail" src="~@/assets/images/pdf.png" alt="" />
              <span class="el-upload-list__item-actions">
                <span class="el-upload-list__item-preview" @click="handlePDFPreview(item)"><i class="el-icon-zoom-in"></i></span>
                <span class="el-upload-list__item-preview" @click="handleDownload(item)"><i class="el-icon-download"></i></span>
              </span>
            </template>
            <template v-else>
              <img class="el-upload-list__item-thumbnail" :src="item" alt="" />
              <span class="el-upload-list__item-actions">
                <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(item)"><i class="el-icon-zoom-in"></i></span>
              </span>
            </template>
          </li>
        </ul>
      </div>
    </el-dialog>

    <el-dialog v-dialogDragBox :visible.sync="imgOpen" title="预览" width="1150px" class="custom-dialog" append-to-body>
      <div style="padding: 0 20px">
        <img :src="imgUrl" style="display: block; max-width: 100%; margin: 0 auto" />
      </div>
    </el-dialog>

    <el-dialog v-dialogDragBox title="预览" :visible.sync="pdfOpen" width="1150px" class="custom-dialog" append-to-body>
      <div style="display: flex; justify-content: space-between; align-items: center; padding-bottom: 10px" v-if="pdfCount > 1">
        <el-button type="primary" size="small" @click="pdfCurrent--" :disabled="pdfCurrent < 2">
          <i class="el-icon-arrow-left"></i>
          上一页
        </el-button>
        <span>{{ pdfCurrent }} / {{ pdfCount }}</span>
        <el-button type="primary" size="small" @click="pdfCurrent++" :disabled="pdfCurrent >= pdfCount">
          下一页
          <i class="el-icon-arrow-right"></i>
        </el-button>
      </div>
      <div class="page-pdf">
        <Pdf :src="pdfUrl" :page="pdfCurrent" @num-pages="pdfCount = $event" />
      </div>
    </el-dialog>

    <el-dialog v-dialogDragBox :visible.sync="videoOpen" title="聊天记录详情" width="510px" class="custom-dialog" append-to-body>
      <div style="padding: 0 10px 0 20px">
        <ul class="el-upload-list el-upload-list--picture-card">
          <li tabindex="0" class="el-upload-list__item is-success" v-for="(item, index) in videoUrl" :key="index">
            <template v-if="typeFormat(item) === 'mp4' || typeFormat(item) === 'MP4'">
              <video ref="videoPlayer" width="100%" height="100%" :src="item" controls></video>
            </template>
            <template v-else>
              <img class="el-upload-list__item-thumbnail" :src="item" alt="" />
              <span class="el-upload-list__item-actions">
                <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(item)"><i class="el-icon-zoom-in"></i></span>
              </span>
            </template>
          </li>
        </ul>
      </div>
    </el-dialog>

    <el-dialog v-dialogDragBox :visible.sync="audioOpen" title="录音详情" width="350px" class="custom-dialog" append-to-body>
      <div style="padding: 10px 20px" v-for="(item, index) in audioSrc" :key="index">
        <audio ref="audioPlayer" :src="item" controls></audio>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { paymentAarchivesApprovalDetail, paymentAarchivesList, enterprisesDetail, paymentAarchivesSwitch } from '@/api/payment'
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
import Create from './create'
import Detail from './detail'
import Company from './company'
import { checkPermi } from '@/utils/permission'
import { kingdee } from '@/minix'
import { getCustomerList } from '@/api/kingdee/customer'
import { getReceivableSummary } from '@/api/kingdee/receivable'

export default {
  name: 'Payment',
  mixins: [kingdee],
  components: { Company, Detail, Create, ElImageViewer },
  data() {
    return {
      // pdf预览
      pdfOpen: false,
      pdfCurrent: 1,
      pdfCount: 0,
      pdfUrl: '',
      imgOpen: false,
      imgUrl: '',
      // 合同、签收单、对账函
      fileTitle: '',
      fileOpen: false,
      fileList: [],
      key: 1,
      loading: true,
      list: [],
      total: 0,
      useOrg: '100073',
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        identity: 'sale',
        customerName: undefined,
        salesperson: undefined,
        approvalStatus: undefined,
        status: undefined
      },
      identityOptions: [
        { value: 'sale', label: '业务员' },
        { value: 'approval', label: '审批人' },
        { value: 'creator', label: '创建人' }
      ],
      statusOptions: [
        { value: 0, label: '初始', class: '' },
        { value: 1, label: '跟进中', class: 'color-orange' },
        { value: 2, label: '结款', class: 'color-info' },
        { value: -10, label: '停用', class: 'color-red' },
        { value: 3, label: '起诉中', class: 'color-pink' }
      ],
      approvalStatusSelect: [
        { value: 0, label: '无需审批' },
        { value: 1, label: '待审批' },
        { value: 2, label: '审批完成' }
      ],
      approvalStatusOptions: [
        { value: 0, label: '暂未审批', class: '' },
        { value: 1, label: '未全部审批', class: 'color-orange' },
        { value: 2, label: '审批完成', class: 'color-blue' }
      ],
      imgViewerOpen: false,
      imgList: [],
      audioOpen: false,
      audioSrc: [],
      filesType: ['png', 'PNG', 'jpg', 'JPG', 'jpeg', 'JPEG'],
      videoOpen: false,
      videoUrl: '',
      tableHeight: 100,
      // 欠款方式
      debtModeOptions: [
        { label: '材料欠款', value: 0 },
        { label: '工程欠款', value: 1 }
      ],
      // 列表显隐
      showSearch: true,
      // 列信息
      columns: [
        { key: 0, label: `序号`, visible: true },
        { key: 1, label: `客户名称`, visible: true },
        { key: 2, label: `欠款方式`, visible: true },
        { key: 3, label: `业务员`, visible: true },
        { key: 4, label: `创建日期`, visible: true },
        { key: 5, label: `应收尾款`, visible: true },
        { key: 6, label: `回款数目`, visible: true },
        { key: 7, label: `跟进详细`, visible: true },
        { key: 8, label: `合同`, visible: true },
        { key: 9, label: `签收单`, visible: true },
        { key: 10, label: `对账函`, visible: true },
        { key: 11, label: `录音`, visible: true },
        { key: 12, label: `聊天记录`, visible: true },
        { key: 13, label: `发货日期`, visible: true },
        { key: 14, label: `状态`, visible: true },
        { key: 15, label: `审批状态`, visible: true },
        { key: 16, label: `更新时间`, visible: true },
        { key: 17, label: `审批时间`, visible: true }
      ],
      pageSizes: [10, 20, 30]
    }
  },
  created() {
    this.getList()
    if (this.checkPermi(['payment:archives:all'])) this.identityOptions.push({ value: '', label: '全部' })
    if (this.companyId == 14) this.columns.push({ key: 18, label: `期末余额`, visible: true })
  },
  computed: {
    createName() {
      return this.$store.getters.info.userId
    },
    companyId() {
      return this.$store.getters.info.companyId
    },
    // 是否有管理权限
    hasManagerPermission() {
      const hasPermission = this.checkPermi(['payment:archives:todo'])
      return hasPermission
    }
  },
  methods: {
    checkPermi,
    // 回显欠款方式
    debeModeFormat(row) {
      const obj = this.debtModeOptions.find(item => item.value == row.debtMode)
      return obj?.label || ''
    },
    // 状态格式化回显
    statusFormat(row) {
      const obj = this.statusOptions.find(item => item.value === row.status)
      return `<span class="${obj.class}">${obj.label}</span>`
    },
    // 审批状态格式化回显
    approvalStatusFormat(row) {
      if (row.status === 0) {
        return `<span class="">无需审批</span>`
      }
      const obj = this.approvalStatusOptions.find(item => item.value === row.approvalStatus)
      return `<span class="${obj.class}">${obj.label}</span>`
    },
    // 回显审批状态
    // prettier-ignore
    approvalFormat(row) {
      if (row.approval) {
        return row.approval.map(item => {
          return `<p>${ item.approvalName }：${ item.status === 1 ? '已审批' : '未审批' }</p>`
        }).join('')
      }
    },
    // 切换组织
    handleUseOrg(value) {
      this.useOrg = value
      this.getList()
    },
    // 列表
    getList() {
      const loading = this.$loading({ lock: true, text: '加载中...', spinner: 'el-icon-loading', background: 'rgba(0, 0, 0, 0.7)' })
      const localPageSize = localStorage.getItem('paymentNewPageSize')
      if (localPageSize) this.queryParams.pageSize = parseInt(localPageSize)
      const localIdentity = localStorage.getItem('paymentIdentity')
      if (localIdentity) this.queryParams.identity = localIdentity
      if (this.queryParams.identity !== 'approval') this.queryParams.approvalStatus = undefined
      // 先获取基本列表数据
      // prettier-ignore
      paymentAarchivesList(this.queryParams).then(res => {
        const { code, msg, rows, total } = res
        if (code === 200) {
          this.total = total
          this.list = rows.map(item => {
            return { ...item, loading: true, kingdeeRefreshing: false }
          })
          const tableHeight = rows.length > 10 ? 640 : rows.length * 59 + 50
          this.$set(this, 'tableHeight', tableHeight)
          this.loading = false
          this.key = Math.random()
          // 异步加载其他数据
          if (rows.length) {
            this.loadAdditionalData(rows)
          }
        } else {
          this.$message.error(msg)
        }
      }).finally(() => {
        loading.close()
      })
    },

    // 加载额外数据的方法
    async loadAdditionalData(rows) {
      const userId = this.$store.getters.info.userId
      await Promise.all(
        rows.map(async item => {
          // 加载审批详情
          const audit = await paymentAarchivesApprovalDetail({ paymentId: item.id })
          const arr = audit.data
          if (arr?.length) {
            if (this.queryParams.identity === 'approval') {
              arr.sort((a, b) => a.priority - b.priority)
              const index = arr.findIndex(item => item.approvalId === userId)
              if (index === 0) item.isAudit = arr[index].status === 0
              else item.isAudit = arr[index - 1].status === 1 && arr[index].status === 0
            }
            item.approval = arr
          }

          // 加载金蝶数据
          if (this.companyId == 14) {
            try {
              // 根据客户名称查询在金蝶内的客户id
              const kingdee = await getCustomerList({ name2: item.customerName })
              const { data: kingdeeData } = kingdee || { data: [] }
              const { data: kingdeeList } = kingdeeData || { data: [] }
              // 查询出来的数组结果去重只取一个ID
              const customerNumber = Array.from(new Set(kingdeeList.map(item => item.Number)))[0]
              if (customerNumber) {
                // 根据客户id查询客户的应收尾款
                const kindeeQuery = {
                  beginDate: this.parseTime(new Date('2023-08-01'), '{y}-{m}-{d}'),
                  endDate: this.parseTime(new Date(), '{y}-{m}-{d}'),
                  customerNumber: customerNumber,
                  settleOrg: this.useOrg
                }
                const kindeeRes = await getReceivableSummary(kindeeQuery)
                const { data: resData } = kindeeRes || { data: [] }
                const { data: list } = resData || []
                if (list?.length) {
                  item.kingdeeInfo = list[list.length - 1]
                  // 强制更新视图
                  this.$set(
                    this.list,
                    this.list.findIndex(row => row.id === item.id),
                    { ...item, loading: false }
                  )
                }
              }
            } catch (error) {
              console.error('加载金蝶数据失败:', error)
            }
          }
        })
      )
    },
    // 刷新列表
    async refreshList() {
      const loading = this.$loading({ lock: true, text: '加载中...', spinner: 'el-icon-loading', background: 'rgba(0, 0, 0, 0.7)' })
      const localPageSize = localStorage.getItem('paymentNewPageSize')
      if (localPageSize) this.queryParams.pageSize = parseInt(localPageSize)
      const localIdentity = localStorage.getItem('paymentIdentity')
      if (localIdentity) this.queryParams.identity = localIdentity
      if (this.queryParams.identity !== 'approval') this.queryParams.approvalStatus = undefined
      // prettier-ignore
      paymentAarchivesList(this.queryParams).then(res=>{
        const { code, msg, rows, total } = res
        if (code === 200) {
          const oldList = [ ...this.list ]
          const newList = rows.map(item => {
            return { ...item, loading: true, kingdeeRefreshing: false }
          })
          const oldId = oldList.map(item => item.id)
          const newId = newList.map(item => item.id)
          if (JSON.stringify(newId) === JSON.stringify(oldId)) {
            return
          }
          this.total = total
          this.list = newList
          const tableHeight = rows.length > 10 ? 640 : rows.length * 59 + 50
          this.$set(this, 'tableHeight', tableHeight)
          this.loading = false
          this.key = Math.random()          
          // 异步加载其他数据
          if (rows.length) {
            this.loadAdditionalData(rows)
          }
        } else {
          this.$message.error(msg)
        }
      }).finally(() => {
        loading.close()
      })
    },
    // 切换身份
    handleChangeIdentity() {
      localStorage.setItem('paymentIdentity', this.queryParams.identity)
      this.queryParams.customerName = undefined
      this.queryParams.salesperson = undefined
      this.queryParams.status = undefined
      this.queryParams.approvalStatus = undefined
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 重置搜索
    resetQuery() {
      this.resetForm('queryForm')
      if (this.companyId == 14) this.useOrg = '100073'
      this.handleQuery()
    },
    // 新增
    handleAdd() {
      this.$refs.create.create()
    },
    // 修改
    handleUpdate(row, type) {
      this.$refs.create.update(row, type)
    },
    handleRestart(row, type) {
      this.$refs.create.update(row, type)
    },
    // 查看详情
    handleDetail(row, type, isAudit) {
      this.$refs.detail.getInfo(row, type, isAudit)
    },
    // 跟进
    handleUp(row) {
      this.$refs.detail.getUp(row)
    },
    // 结清、停用、启用
    // prettier-ignore
    handleDone(row, status = 2) {
      let statusTitle
      if (status == 0) statusTitle = '是否启用?'
      else if (status == 2) statusTitle = '是否已结清？'
      else if (status == 3) statusTitle = '是否已起诉？'
      else if (status == -10) statusTitle = '是否停用？'
      const title = `客户名称：${ row.customerName + ' ' + statusTitle }`
      const paymentId = row.id
      this.$confirm(title, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        paymentAarchivesSwitch({ paymentId, status }).then(res => {
          const { code, msg } = res
          if (code === 200) {
            this.$message.success('操作成功')
            this.refreshList()
          } else this.$message.error(msg)
        })
      }).catch(() => {})
    },
    // 查看合同、签收单、对账函详情
    handleView(row, type) {
      if (!row[type]) return
      const obj = row[type + '_oss'] || row[type]
      const arr = obj.split(',') || []
      const newArr = row[type].split(',').filter(item => this.typeFormat(item) === 'pdf' || this.typeFormat(item) === 'PDF')
      if (!!newArr.length) {
        this.fileTitle = type === 'chatHistory' ? '聊天记录详情' : type === 'contract' ? '合同详情' : type === 'receipt' ? '签收单详情' : '对账单详情'
        if (!row[type + '_oss']) {
          this.fileList = arr.map(item => {
            return this.imgPath + item
          })
        } else this.fileList = arr
        this.fileOpen = true
      } else {
        if (!row[type + '_oss']) {
          this.imgList = arr.map(item => {
            return this.imgPath + item
          })
          this.imgViewerOpen = true
        } else {
          this.imgList = arr
          this.imgViewerOpen = true
        }
      }
    },
    closeImgViewer() {
      this.imgViewerOpen = false
    },
    // 下载
    handleDownload(item) {
      const filename = item.split('/').pop().split('?')[0]
      const xhr = new XMLHttpRequest()
      xhr.open('GET', item, true)
      xhr.responseType = 'blob'
      xhr.onload = () => {
        if (xhr.status === 200) {
          const blob = new Blob([xhr.response], { type: 'application/octet-stream' })
          const url = URL.createObjectURL(blob)
          const link = document.createElement('a')
          link.href = url
          link.download = filename
          link.click()
          URL.revokeObjectURL(url)
        }
      }
      xhr.send()
    },
    // 查看录音/聊天记录详情
    handleTape(row, type) {
      if (!row[type]) return
      const obj = row[type + '_oss'] || row[type]
      const arr = obj.split(',') || []
      let list = []
      if (!row[type + '_oss']) {
        list = arr.map(item => {
          return this.imgPath + item
        })
      } else {
        list = arr
      }
      if (type === 'tape') {
        this.audioSrc = list
        this.audioOpen = true
      } else if (type === 'chatHistory') {
        if (row.chatHistory) {
          let suffixArr = row.chatHistory.split('.')
          let suffix = suffixArr[suffixArr.length - 1]
          if (this.filesType.indexOf(suffix) != -1) {
            this.handleView(row, type)
          } else if (suffix === 'mp4' || suffix === 'MP4') {
            this.videoUrl = list
            this.videoOpen = true
          }
        }
      }
    },
    // 查看企业详情
    handleShow(row) {
      const enterpriseId = row.customerId
      enterprisesDetail({ enterpriseId }).then(res => {
        this.$refs.company.getInfo(res.data)
      })
    },
    // 判断文件类型
    typeFormat(file) {
      const index = file.lastIndexOf('?')
      if (index !== -1) file = file.slice(0, index)
      const index1 = file.lastIndexOf('.')
      return file.substr(index1 + 1)
    },
    // 预览图片
    handlePictureCardPreview(file) {
      this.imgUrl = file
      this.imgOpen = true
    },
    // 预览PDF
    handlePDFPreview(file) {
      this.pdfUrl = file
      this.pdfCurrent = 1
      this.pdfOpen = true
    },
    // 下拉操作
    handleDropdown(e) {
      const { row, type } = e
      switch (type) {
        case 'stop':
          this.handleDone(row, -10)
          break
        case 'prosecute':
          this.handleDone(row, 3)
          break
      }
    },
    handleCommand(row, type) {
      return { row, type }
    },
    // 刷新单行金蝶数据
    async refreshKingdeeData(row) {
      if (this.companyId != 14 || row.kingdeeRefreshing) return
      try {
        // 设置刷新状态
        const listIndex = this.list.findIndex(item => item.id === row.id)
        if (listIndex !== -1) {
          this.$set(this.list[listIndex], 'kingdeeRefreshing', true)
        }
        // 根据客户名称查询在金蝶内的客户id
        const kingdee = await getCustomerList({ name2: row.customerName })
        const { data: kingdeeData } = kingdee || { data: [] }
        const { data: kingdeeList } = kingdeeData || { data: [] }
        // 查询出来的数组结果去重只取一个ID
        const customerNumber = Array.from(new Set(kingdeeList.map(item => item.Number)))[0]
        if (customerNumber) {
          // 根据客户id查询客户的应收尾款
          const kindeeQuery = {
            beginDate: this.parseTime(new Date('2023-08-01'), '{y}-{m}-{d}'),
            endDate: this.parseTime(new Date(), '{y}-{m}-{d}'),
            customerNumber: customerNumber,
            settleOrg: this.useOrg
          }
          const kindeeRes = await getReceivableSummary(kindeeQuery)
          const { data: resData } = kindeeRes || { data: [] }
          const { data: list } = resData || []
          // 更新当前行的金蝶信息
          const finalIndex = this.list.findIndex(item => item.id === row.id)
          if (finalIndex !== -1) {
            if (list?.length) {
              this.$set(this.list[finalIndex], 'kingdeeInfo', list[list.length - 1])
              this.$message.success('期末余额刷新成功')
            } else {
              this.$set(this.list[finalIndex], 'kingdeeInfo', null)
              this.$message.warning('未查询到金蝶数据')
            }
            this.$set(this.list[finalIndex], 'kingdeeRefreshing', false)
          }
        } else {
          this.$message.warning('未找到对应的金蝶客户')
          const finalIndex = this.list.findIndex(item => item.id === row.id)
          if (finalIndex !== -1) {
            this.$set(this.list[finalIndex], 'kingdeeRefreshing', false)
          }
        }
      } catch (error) {
        console.error('刷新金蝶数据失败:', error)
        this.$message.error('刷新失败，请重试')
        // 出错时取消刷新状态
        const errorIndex = this.list.findIndex(item => item.id === row.id)
        if (errorIndex !== -1) {
          this.$set(this.list[errorIndex], 'kingdeeRefreshing', false)
        }
      }
    }
  }
}
</script>

<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';
.custom-search {
  padding-top: 17px;
  ::v-deep {
    .el-form-item--small {
      display: inline-flex;
      .el-form-item__label {
        flex-shrink: 0;
      }
      .el-form-item {
        margin-bottom: 10px;
      }
    }
  }
}
.tableBox {
  padding: 20px;
  ::v-deep {
    .table-btn {
      padding: 0;
    }
    .table-dropdown {
      color: #999999;
    }
  }
  .quick-serial {
    float: left;
    display: inline-block;
    width: 20px;
    height: 20px;
    line-height: 20px;
    border-radius: 50%;
    background-color: #f43f3f;
    color: #ffffff;
    text-align: center;
    margin-right: 5px;
    position: relative;
    &:after {
      font-style: normal;
      content: '个';
      display: block;
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
    }
  }
}

// 期末余额刷新样式
.kingdee-balance-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
  width: 100%;
  .balance-content {
    flex: 1;
    min-width: 0; // 允许flex子项缩小到内容宽度以下
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .refresh-icon,
  .refresh-loading {
    flex-shrink: 0; // 防止图标被压缩
    cursor: pointer;
    font-size: 14px;
    color: #409eff;
    transition: color 0.3s;
    width: 14px; // 固定宽度确保不被压缩
    &:hover {
      color: #66b1ff;
    }
  }
  .refresh-loading {
    cursor: not-allowed;
    color: #c0c4cc;
  }
}
</style>
