<template>
  <div>
    <el-dialog v-dialogDragBox :title="title" :visible.sync="open" width="1150px" class="custom-dialog" @close="handleClose">
      <div style="padding: 0 20px">
        <el-form ref="form" :model="form" :rules="rules" label-width="6em">
          <el-row :gutter="10">
            <el-col :span="12">
              <el-form-item label="客户名称" prop="inputCustomerName">
                <el-autocomplete ref="companyName" style="width: 100%" v-model="form.inputCustomerName" clearable placeholder="请输入客户名称" :disabled="false || formType == 'restart'" value-key="companyName" :fetch-suggestions="querySearchAsync" @select="handleSelect" :trigger-on-focus="false" @keyup.enter.native="handleQuery" @change="handleChangeName" v-if="customerType === 1">
                  <template slot="prepend">
                    <el-select v-model="customerType" placeholder="请选择" style="width: 80px" @change="handleChangeCustomerType">
                      <el-option label="企业" :value="1"></el-option>
                      <el-option label="个人" :value="2"></el-option>
                    </el-select>
                  </template>
                  <el-button slot="append" icon="el-icon-search" @click="handleQuery" v-if="customerType === 1"></el-button>
                </el-autocomplete>
                <el-input style="width: 100%" v-model="form.inputCustomerName" clearable placeholder="请输入客户名称" :disabled="false || formType == 'restart'" @change="handleChangeName" v-if="customerType === 2">
                  <template slot="prepend">
                    <el-select v-model="customerType" placeholder="请选择" style="width: 80px" @change="handleChangeCustomerType">
                      <el-option label="企业" :value="1"></el-option>
                      <el-option label="个人" :value="2"></el-option>
                    </el-select>
                  </template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="Object.keys(kingdeeInfo).length > 0 ? 24 : 12" v-if="companyId == 14">
              <div class="kingdeeBox">
                <el-button type="primary" @click="handleQueryKingdee" icon="el-icon-search" :loading="kingdeeLoading" :disabled="(customerType === 1 && !form.customerId) || (customerType === 2 && !form.inputCustomerName)">金蝶应收汇总</el-button>
                <div class="kingdeeInfo" v-if="Object.keys(kingdeeInfo).length > 0">
                  <span style="margin-left: 10px" v-if="kingdeeInfo.CurrencyForName">币别：{{ kingdeeInfo.CurrencyForName }}</span>
                  <span style="margin-left: 10px" v-if="kingdeeInfo.InitAmount">期初余额：{{ kingdeeInfo.InitAmount }}</span>
                  <span style="margin-left: 10px" v-if="kingdeeInfo.Amount">本期应收：{{ kingdeeInfo.Amount }}</span>
                  <span style="margin-left: 10px" v-if="kingdeeInfo.RealAmount">本期收款：{{ kingdeeInfo.RealAmount }}</span>
                  <span style="margin-left: 10px" v-if="kingdeeInfo.leftAmount">期末余额：{{ kingdeeInfo.leftAmount }}</span>
                </div>
              </div>
            </el-col>
            <el-col :span="24"></el-col>
            <el-col :span="12" v-if="customerType === 2">
              <el-form-item label="身份证号" prop="idCard">
                <el-input v-model="form.idCard" clearable :placeholder="form.idCardImage ? '请输入身份证号' : '请上传身份证照片'" :disabled="!(form.inputCustomerName && form.idCardImage)"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24" v-if="customerType === 2">
              <el-form-item label="请上传身份证照片" prop="idCardImage">
                <div style="display: inline-flex; align-items: center">
                  <el-upload class="idCardImage" :class="{ hide: fileList.length }" ref="idCardImage" :action="uploadImgUrl" :headers="headers" :data="idCardImageData" list-type="picture-card" :auto-upload="false" :limit="1" :file-list="fileList" :on-change="idCardOCR" :on-success="handleUploadSuccess" :accept="idCardImageType">
                    <i class="idCardImageBg"></i>
                    <div slot="file" slot-scope="{ file }">
                      <img class="el-upload-list__item-thumbnail" :src="file.url" alt="" />
                      <span class="el-upload-list__item-actions">
                        <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
                          <i class="el-icon-zoom-in"></i>
                        </span>
                        <span class="el-upload-list__item-delete" @click="handleRemove(file)">
                          <i class="el-icon-delete"></i>
                        </span>
                      </span>
                    </div>
                  </el-upload>
                  <div style="line-height: 30px; padding-left: 20px" v-if="!fileList.length">
                    <div>
                      请上传大小不超过
                      <b style="color: #f56c6c">1MB</b>
                    </div>
                    <div>
                      格式为
                      <b style="color: #f56c6c">{{ idCardImageType.replace(/,/g, '/') }}</b>
                      的文件
                    </div>
                  </div>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="应收尾款" prop="receivable">
                <el-input v-model="form.receivable" clearable placeholder="请输入应收尾款" :disabled="!form.customerId || formType == 'restart'">
                  <span slot="suffix">元</span>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="formType != 'update' && formType != 'restart'">
              <el-form-item label="回款数目" prop="payment">
                <el-input v-model="form.payment" clearable placeholder="请输入回款数目" :disabled="!form.customerId">
                  <span slot="suffix">元</span>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="发货日期" prop="deliveryTime">
                <el-date-picker v-model="form.deliveryTime" clearable type="datetime" placeholder="请选择发货日期" value-format="yyyy-MM-dd HH:mm:ss" style="width: 100%" :disabled="!form.customerId || formType == 'restart'"></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="业务员" prop="salespersonId">
                <el-select v-model="form.salespersonId" filterable clearable placeholder="请选择业务员" style="width: 100%" @change="handleChangeSalesperson" :disabled="!form.customerId || formType == 'restart'">
                  <el-option v-for="item in salespersonOptions" :key="item.userId" :label="item.realName || item.nickName" :value="item.userId">
                    <span style="float: left">{{ item.realName || item.nickName }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.dept && item.dept.deptName }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="formType != 'update' && formType != 'restart'">
              <el-form-item label="欠款方式" prop="debtMode">
                <el-select v-model="form.debtMode" placeholder="请选择欠款方式" :disabled="!form.customerId || formType == 'restart'" style="width: 100%">
                  <el-option v-for="item in debtModeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="formType != 'update' && formType != 'restart'">
              <el-form-item label="工程名称" prop="engineering">
                <el-input v-model="form.engineering" clearable placeholder="请输入工程名称" :disabled="!form.customerId"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24"></el-col>
            <el-col :span="12" v-if="formType != 'update'">
              <el-form-item label="签收单" prop="receipt">
                <image-upload isRow :isShowTip="!form.receipt" :fileSize="1024" v-model="form.receipt" :isDisabled="formType == 'restart'" :file-type="fileType" :deleteShow="formType == 'restart' ? false : true" />
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="formType != 'update'">
              <el-form-item label="对账函" prop="reconciliation">
                <image-upload isRow :isShowTip="!form.reconciliation" :fileSize="1024" v-model="form.reconciliation" :isDisabled="formType == 'restart'" :file-type="fileType" :deleteShow="formType == 'restart' ? false : true" />
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="formType != 'update'">
              <el-form-item label="合同" prop="contract">
                <image-upload isRow :isShowTip="!form.contract" :fileSize="1024" v-model="form.contract" :isDisabled="formType == 'restart'" :file-type="fileType" :deleteShow="formType == 'restart' ? false : true" />
              </el-form-item>
            </el-col>
            <el-col :span="24"></el-col>
            <el-col :span="12" v-if="formType != 'update'">
              <el-form-item label="录音" prop="tape">
                <file-upload isRow :isShowTip="!form.tape" :fileSize="1024" v-model="form.tape" :file-type="fileTypes" :isDisabled="formType == 'restart'" />
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="formType != 'update'">
              <el-form-item label="聊天记录" prop="chatHistory">
                <file-upload isRow :isShowTip="!form.chatHistory" :fileSize="1024" v-model="form.chatHistory" :isDisabled="formType == 'restart'" :file-type="filesType" />
              </el-form-item>
            </el-col>
            <el-col :span="24" v-if="formType != 'update' && companyId != 14">
              <el-form-item label="审批人" prop="approvals">
                <div class="cascaderBox">
                  <div class="cascaderItem" v-for="(item, index) in form.approvals" :key="index">
                    <el-checkbox class="cascaderCheck" :disabled="!item.approvalId || formType == 'restart'" v-model="item.checked"></el-checkbox>
                    <span class="cascaderNum">{{ index + 1 }}</span>
                    <el-cascader popper-class="cascaderInfo" v-model="item.approvalId" :options="approvalsOptions" :props="approvalsProps" filterable :show-all-levels="false" placeholder="请选择审批人" @change="handleChange(item, $event)" :disabled="!form.customerId || formType == 'restart'"></el-cascader>
                  </div>
                  <div v-if="formType != 'restart'" class="cascaderItem cascaderAdd" :class="{ disabled: !form.customerId }" @click="handleAddApprovals">
                    <i class="el-icon-plus"></i>
                    <span>增加审批人</span>
                  </div>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn" @click="handleClose">取 消</el-button>
        <el-button class="custom-dialog-btn primary" :class="{ disabled: !form.customerId }" :disabled="!form.customerId" @click="handleSubmit" v-if="formType != 'update' && formType != 'restart'">立即发布</el-button>
        <el-button class="custom-dialog-btn primary" :class="{ disabled: !form.customerId }" :disabled="!form.customerId" @click="handleUpdate" v-if="formType == 'update'">立即修改</el-button>
        <el-button class="custom-dialog-btn primary" :class="{ disabled: !form.id }" :disabled="!form.id" @click="handleRestart" v-if="formType == 'restart'">立即重启</el-button>
      </div>
    </el-dialog>

    <el-image-viewer :zIndex="99999" v-show="showViewer" :on-close="closeViewer" :url-list="srcList"></el-image-viewer>
  </div>
</template>
<script>
import { deptTreeSelect, listUser } from '@/api/system/user'
import { isIdCard, isNumber, isNumberLength } from '@/utils/validate'
import { paymentAarchives, paymentAarchivesModify, paymentAarchivesDetail, paymentRestart, searchByName } from '@/api/payment'
import { getCompanyList, getRemoteCompanyInfo } from '@/api/tender'
import { ocr, realName, upload } from '@/api/aliyun'
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
import { getToken } from '@/utils/auth'
import { getCustomerList } from '@/api/kingdee/customer'
import { getReceivableSummary } from '@/api/kingdee/receivable'

export default {
  components: { ElImageViewer },
  data() {
    const approvalsRules = (rule, value, callback) => {
      // 是否已经选择了审批人，是否有重复的审批人
      const hasApproval = value.some(item => item.approvalId)
      const approvalIds = value.filter(item => item.checked).map(item => item.approvalId)
      const approvalIdsSet = new Set(approvalIds)
      if (!hasApproval) {
        callback(new Error('请选择审批人'))
      } else if (approvalIds.length !== approvalIdsSet.size) {
        callback(new Error('审批人不能重复'))
      } else {
        callback()
      }
    }
    return {
      title: '新增审批流程',
      open: false,
      form: {},
      rules: {
        inputCustomerName: [{ required: true, message: '请输入客户名称', trigger: ['blur', 'change'] }],
        idCard: [
          { required: true, message: '请输入身份证号', trigger: ['blur', 'change'] },
          { validator: isIdCard, message: '请输入正确的身份证号', trigger: 'blur' }
        ],
        idCardImage: [{ required: true, message: '请上传身份证', trigger: ['blur', 'change'] }],
        debtMode: [{ required: true, message: '请选择欠款方式', trigger: ['blur', 'change'] }],
        receivable: [
          { required: true, message: '请输入应收尾款', trigger: 'blur' },
          { validator: isNumber, message: '输入有误请重新输入', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: 'blur' }
        ],
        payment: [
          { validator: isNumber, message: '输入有误请重新输入', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: 'blur' }
        ],
        deliveryTime: [{ required: true, message: '请选择发货日期', trigger: 'blur' }],
        salespersonId: [{ required: true, message: '请选择业务员', trigger: 'blur' }],
        approvals: [
          { required: true, message: '请选择审批人', trigger: 'blur' },
          { validator: approvalsRules, trigger: 'change' }
        ]
      },
      salespersonOptions: [],
      fileType: ['png', 'PNG', 'jpg', 'JPG', 'jpeg', 'JPEG', 'pdf', 'PDF'],
      filesType: ['png', 'PNG', 'jpg', 'JPG', 'jpeg', 'JPEG', 'mp4', 'MP4'],
      fileTypes: ['mp3', 'MP3'],
      approvalsOptions: [],
      approvalsProps: {
        expandTrigger: 'hover',
        emitPath: false
      },
      // 当前登录账号信息
      userInfo: {},
      // 来源
      formType: undefined,
      // 个人/企业
      customerType: 1,
      // 欠款方式
      debtModeOptions: [
        { label: '材料欠款', value: 0 },
        { label: '工程欠款', value: 1 }
      ],
      showViewer: false,
      srcList: [],
      uploadImgUrl: process.env.VUE_APP_BASE_API + '/common/upload', // 上传的图片服务器地址
      headers: {
        Authorization: 'Bearer ' + getToken()
      },
      idCardImageData: { compress: false },
      idCardImageType: '.png,.jpg,.jpeg,.PNG,.JPG,.JPEG',
      isUploading: false,
      fileList: [],
      kingdeeLoading: false,
      kingdeeInfo: {}
    }
  },
  computed: {
    companyId() {
      return this.$store.state.user.companyId
    }
  },
  created() {
    this.userInfo = this.$store.getters.info || {}
    if (this.companyId != 14) this.getApprovalsOptions()
    else this.getSalesperson()
  },
  methods: {
    // 表单重置
    reset() {
      this.form = {
        approvals: [{ approvalId: undefined, approvalName: 'undefined', checked: false }], // 审批人
        contract: undefined, // 合同
        debtMode: undefined, // 欠款方式
        tape: undefined, // 录音
        chatHistory: undefined, // 聊天记录
        customerId: undefined, // 客户ID
        refresh: undefined, // 是否刷新
        enterpriseId: undefined, //
        inputCustomerName: undefined, // 客户名称
        customerName: undefined, // 客户名称
        deliveryTime: undefined, // 发货日期
        payment: undefined, // 回款数目
        receipt: undefined, // 签收单
        receivable: undefined, // 应收尾款
        reconciliation: undefined, // 对账函
        engineering: undefined, // 工程名称
        salesperson: this.userInfo.nickName || undefined, // 业务员
        salespersonId: this.userInfo.userId || undefined, // 业务员ID
        idCard: undefined,
        idCardImage: undefined
      }
      this.resetForm('form')
      this.isUploading = false
      this.kingdeeLoading = false
      this.kingdeeInfo = {}
    },
    // 新增
    create(info = {}) {
      this.open = true
      this.reset()
      if (this.companyId == 14) this.getApprovals()
      this.title = '新增审批流程'
      this.formType = undefined
      if (Object.keys(info).length > 0) {
        this.form.inputCustomerName = (info.CUSTOMERID && info.CUSTOMERID.Name && this.getString(info.CUSTOMERID.Name)) || info.ContactUnitName
        this.customerType = 2
        this.form.receivable = info.FALLAMOUNTFOR || info.leftAmount
        this.form.deliveryTime = (info.DATE && this.parseTime(info.DATE, '{y}-{m}-{d} {h}:{i}:{s}')) || ''
      }
    },
    // 回显数组的value值为string
    getString(arr) {
      if (!arr) return ''
      return arr.map(item => item.Value).join(',')
    },
    // 查询用户
    getSalesperson() {
      listUser().then(res => {
        this.salespersonOptions = res.rows || []
      })
    },
    // 查询审批人
    getApprovals() {
      let arr = []
      arr.push(this.salespersonOptions.find(item => item.userId === 373))
      // arr.push(this.salespersonOptions.find(item => item.userId === 1401))
      arr.push(this.salespersonOptions.find(item => item.userId === 1476))
      arr.push(this.salespersonOptions.find(item => item.userId === 154))
      this.form.approvals = arr.map(item => ({ approvalId: item.userId, approvalName: item.nickName, checked: true }))
    },
    // 修改
    update(row = {}, type) {
      this.reset()
      const paymentId = row.id
      paymentAarchivesDetail({ paymentId }).then(res => {
        const { code, data, msg } = res
        if (code === 200) {
          data.inputCustomerName = data.customerName
          data.approvals.forEach(el => {
            el.checked = true
          })
          this.form = { ...data }
          if (type == 'restart') {
            this.form.receivable = data.receivable - data.payment
            this.form.payment = undefined
            this.title = '重启审批流程'
          } else {
            this.title = '修改审批流程'
            this.form.customerId = String(this.form.customerId)
            this.customerType = this.form.customerId === '0' ? 2 : 1
            if (this.customerType === 1) {
              const options = {
                lock: true,
                text: '搜索中…',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)'
              }
              let dataLoading = this.$loading(options)
              searchByName({ name: this.form.customerName }).then(res => {
                const { code, data, msg } = res
                if (code == 200) {
                  if (data && data.gid) {
                    this.$nextTick(() => {
                      dataLoading.close()
                    })
                    this.form.customerId = data.gid
                    this.form.customerName = data.name
                    this.form.refresh = false
                    this.form.enterpriseId = data.id
                  } else {
                    this.form.refresh = undefined
                    this.form.enterpriseId = undefined
                    getRemoteCompanyInfo({ companyName: this.form.customerName }).then(res => {
                      const { code, msg, data } = res
                      if (code === 200) {
                        this.$nextTick(() => {
                          dataLoading.close()
                        })
                        if (data && data.hasOwnProperty('companyGid') && data.companyGid) {
                          this.form.customerId = data.companyGid
                          this.form.customerName = data.companyName
                        } else {
                          this.$nextTick(() => {
                            dataLoading.close()
                          })
                          this.$alert(`未查询到“<span style='color:#ff0000'>${input}</span>”相关公司信息！<br />请检查公司名称是否正确，请输入公司全称！`, '系统提示', { dangerouslyUseHTMLString: true })
                        }
                      } else {
                        this.$nextTick(() => {
                          dataLoading.close()
                        })
                        this.$alert(`未查询到“<span style='color:#ff0000'>${input}</span>”相关公司信息！<br />请检查公司名称是否正确，请输入公司全称！`, '系统提示', { dangerouslyUseHTMLString: true })
                      }
                    })
                  }
                }
              })
            }
          }
          this.formType = type
          this.open = true
        } else this.$message.error(msg)
      })
    },
    // 查询部门、查询用户构造树
    async getApprovalsOptions() {
      const dept = await deptTreeSelect()
      const user = await listUser()
      this.salespersonOptions = user.rows || []
      const children = dept.data[0].children || []
      const deptData = [...children, ...[{ id: -1, label: '其他', value: -1 }]] || []
      const userData = user.rows || []
      const getChildren = data => {
        data.forEach(item => {
          item.value = item.id
          if (item.children) {
            getChildren(item.children)
          } else {
            item.children = []
          }
        })
      }
      getChildren(deptData)
      const addChildren = data => {
        data.forEach(item => {
          userData.forEach(user => {
            if (item.id === user.deptId && item.children) {
              item.children.push({
                id: user.userId,
                label: user.realName || user.nickName,
                value: user.userId,
                disabled: user.status == '1',
                userName: user.userName
              })
            }
            if (item.id === -1 && (!user.deptId || user.deptId === dept.data[0].id) && item.children) {
              item.children.push({
                id: user.userId,
                label: user.realName || user.nickName,
                value: user.userId,
                disabled: user.status == '1',
                userName: user.userName
              })
            }
          })
          if (item.children && item.children.length) {
            addChildren(item.children)
          }
        })
      }
      addChildren(deptData)
      this.approvalsOptions = deptData
    },
    // 选择业务员
    handleChangeSalesperson(e) {
      const salesperson = this.salespersonOptions.find(item => item.userId === e)
      this.form.salesperson = salesperson.nickName + '(' + salesperson.dept.deptName + ')'
    },
    // 增加审批人
    handleAddApprovals() {
      if (this.form.customerId && this.formType != 'restart') this.form.approvals.push({ approvalId: undefined, approvalName: undefined, checked: false })
    },
    // 选择审批人
    handleChange(item, e) {
      if (item.approvalId) {
        const approval = this.findInTree(this.approvalsOptions, e)
        item.approvalName = approval.label
        item.checked = true
      }
    },
    // 从树结构内找到相同id
    findInTree(tree, id) {
      for (let i = 0; i < tree.length; i++) {
        if (tree[i].id === id) {
          return tree[i]
        } else if (tree[i].children && tree[i].children.length) {
          const res = this.findInTree(tree[i].children, id)
          if (res) return res
        }
      }
    },
    // 改变企业/个人
    handleChangeCustomerType(e) {
      if (e === 2) {
        this.form.customerId = '0'
        this.form.customerName = ''
        this.form.inputCustomerName = ''
        this.form.refresh = false
        this.form.enterpriseId = 0
        this.form.idCard = ''
        this.form.idCardImage = ''
      } else {
        this.form.customerId = undefined
        this.form.customerName = ''
        if (!this.form.inputCustomerName) this.form.inputCustomerName = ''
        this.form.refresh = undefined
        this.form.enterpriseId = undefined
        this.form.idCard = ''
        this.form.idCardImage = ''
      }
    },
    // 模糊搜索企业
    async querySearchAsync(queryString, cb) {
      if (this.customerType === 2) return
      const list = (await getCompanyList({ companyName: queryString })) || []
      const results = queryString ? list.filter(this.createStateFilter(queryString)) : list
      cb(results)
    },
    createStateFilter(queryString) {
      return state => {
        return state.companyName.toLowerCase().indexOf(queryString.toLowerCase()) === 0
      }
    },
    // 选择企业
    handleSelect(item) {
      if (this.customerType === 2) return
      const { companyName } = item
      if (companyName) {
        this.form.name = companyName
        this.handleQuery()
      }
    },
    // 搜索公司
    // prettier-ignore
    handleQuery() {
      if (this.customerType === 2) return
      this.$refs.companyName.suggestions = []
      const input = this.form.inputCustomerName ? this.form.inputCustomerName.trim() : ''
      if (!input) return
      if (this.customerType === 1) {
        const options = {
          lock: true,
          text: '搜索中…',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        }
        let dataLoading = this.$loading(options)
        searchByName({ name: input }).then(res => {
          const { code, data, msg } = res
          if (code == 200) {
            if (data && data.gid) {
              this.$nextTick(() => {
                dataLoading.close()
              })
              this.form.customerId = data.gid
              this.form.customerName = data.name
              this.form.refresh = false
              this.form.enterpriseId = data.id
            } else {
              this.form.refresh = undefined
              this.form.enterpriseId = undefined
              getRemoteCompanyInfo({ companyName: input }).then(res => {
                const { code, msg, data } = res
                if (code === 200) {
                  this.$nextTick(() => {
                    dataLoading.close()
                  })
                  if (data && data.hasOwnProperty('companyGid') && data.companyGid) {
                    this.form.customerId = data.companyGid
                    this.form.customerName = data.companyName
                  } else {
                    this.$nextTick(() => {
                      dataLoading.close()
                    })
                    this.$alert(`未查询到“<span style='color:#ff0000'>${ input }</span>”相关公司信息！<br />请检查公司名称是否正确，请输入公司全称！`, '系统提示', { dangerouslyUseHTMLString: true })
                  }
                } else {
                  this.$nextTick(() => {
                    dataLoading.close()
                  })
                  this.$alert(`未查询到“<span style='color:#ff0000'>${ input }</span>”相关公司信息！<br />请检查公司名称是否正确，请输入公司全称！`, '系统提示', { dangerouslyUseHTMLString: true })
                }
              })
            }
          }
        })
      }
    },
    // 输入客户名称时清空客户ID
    handleChangeName() {
      if (this.customerType === 1) {
        let { inputCustomerName, customerName } = this.form
        inputCustomerName = inputCustomerName.trim() || ''
        if (inputCustomerName && inputCustomerName !== customerName) this.form.customerId = undefined
        if (!inputCustomerName) this.form.customerId = undefined
      } else {
        this.form.customerName = this.form.inputCustomerName
      }
    },
    // 提交审批流程
    handleSubmit() {
      this.$refs.form.validate(async valid => {
        if (valid) {
          let { approvals, tape, chatHistory, contract, customerId, customerName, debtMode, deliveryTime, payment, receipt, receivable, reconciliation, salesperson, salespersonId, refresh, enterpriseId, idCard, idCardImage, engineering } = this.form
          payment = payment || 0
          const data = {
            approvals: approvals.filter(item => item.checked),
            tape,
            chatHistory,
            contract,
            idCard,
            idCardImage,
            customerId: this.customerType === 2 ? Number(customerId) : customerId,
            customerName,
            debtMode,
            deliveryTime,
            payment,
            receipt,
            receivable,
            reconciliation,
            salesperson,
            salespersonId,
            refresh,
            enterpriseId,
            engineering
          }
          let isPass = true
          if (this.customerType === 2) isPass = await this.idCardVerify(customerName, idCard)
          if (isPass) {
            paymentAarchives(data).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('新增成功')
                this.open = false
                this.$emit('refresh')
              } else this.$message.error(msg)
            })
          } else {
            this.$message.error('请检查客户名称和身份证号是否正确')
            this.isUploading = false
          }
        }
      })
    },
    // 取消
    handleClose() {
      this.customerType = 1
      this.reset()
      this.open = false
    },
    handleUpdate() {
      this.$refs.form.validate(valid => {
        if (valid) {
          const { id, receivable, deliveryTime, salesperson, salespersonId, customerId, customerName, enterpriseId, idCard, idCardImage } = this.form
          const data = {
            paymentId: id,
            receivable,
            deliveryTime,
            salesperson,
            salespersonId,
            customerId,
            customerName,
            enterpriseId: enterpriseId || (customerId != '0' ? '' : '0'),
            idCard: customerId === '0' ? idCard : '-1',
            idCardImage: customerId === '0' ? idCardImage : '-1'
          }
          paymentAarchivesModify(data).then(res => {
            const { code, msg } = res
            if (code === 200) {
              this.$message.success('修改成功')
              this.open = false
              this.$emit('refresh')
            } else this.$message.error(msg)
          })
        }
      })
    },
    handleRestart() {
      const paymentId = this.form.id
      paymentRestart({ paymentId }).then(res => {
        const { code, msg } = res
        if (code === 200) {
          this.$message.success('重启审批成功')
          this.open = false
          this.$emit('refresh')
        } else this.$message.error(msg)
      })
    },
    async idCardOCR(file, fileList) {
      if (!this.isUploading) {
        this.isUploading = true
        const isLt2M = file.raw.size / 1024 / 1024 < 1
        if (!isLt2M) {
          this.$message.error('上传身份证照片大小不能超过 1MB!')
          this.$refs.idCardImage.clearFiles()
          this.isUploading = false
          return
        }
        this.fileList = fileList
        const image = await this.getBase64(file.raw)
        const url = 'https://idcardside.market.alicloudapi.com/idcard_ocr'
        const data = `image=${encodeURIComponent(image)}`
        const headers = {
          Authorization: 'APPCODE 9e54df78753c4984b94334fbe8ea6811',
          'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'
        }
        ocr(url, data, headers).then(res => {
          const { code, msg, data } = res
          if (code === 200) {
            if (data.hasOwnProperty('info')) {
              const { info } = data
              if (info && info.hasOwnProperty('name') && info.name && info.hasOwnProperty('number') && info.number) {
                this.form.inputCustomerName = info.name
                this.form.customerName = info.name
                this.form.idCard = info.number
                this.handleUpload()
              } else {
                this.$message.error('请上传正确的身份证人像照片')
                this.$refs.idCardImage.clearFiles()
                this.fileList = []
                this.isUploading = false
              }
            } else {
              this.$message.error('请上传正确的身份证人像照片')
              this.$refs.idCardImage.clearFiles()
              this.fileList = []
              this.isUploading = false
            }
          } else {
            this.$message.error('请上传正确的身份证人像照片')
            this.$refs.idCardImage.clearFiles()
            this.fileList = []
            this.isUploading = false
          }
        })
      }
    },
    // 身份证二要素验证
    idCardVerify(name = '', idcard = '') {
      return new Promise((resolve, reject) => {
        if (!name || !idcard) resolve(false)
        const url = 'https://kzidcardv1.market.alicloudapi.com/api-mall/api/id_card/check'
        const data = `idcard=${idcard}&name=${name}`
        const headers = {
          Authorization: 'APPCODE 9e54df78753c4984b94334fbe8ea6811',
          'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'
        }
        realName(url, data, headers).then(res => {
          const { code, msg, data } = res
          if (code === 200) {
            if (data && data.result == 0) {
              resolve(true)
            } else resolve(false)
          } else resolve(false)
        })
      })
    },
    handleUpload() {
      const data = new FormData()
      data.append('file', this.fileList[0].raw)
      data.append('compress', false)
      upload(data).then(res => {
        const { code, fileName } = res
        if (code === 200) {
          this.form.idCardImage = fileName
          this.isUploading = false
        } else {
          this.$message.error('上传失败')
          this.isUploading = false
        }
      })
    },
    // 图片转base64
    getBase64(file) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader()
        let fileResult = ''
        reader.readAsDataURL(file)
        // 开始转
        reader.onload = () => {
          fileResult = reader.result
        }
        // 转失败
        reader.onerror = error => {
          reject(error)
        }
        // 转结束
        reader.onloadend = () => {
          resolve(fileResult)
        }
      })
    },
    handleUploadSuccess(res) {
      this.form.idCardImage = res
    },
    // 图片预览
    handlePictureCardPreview(file) {
      if (!file) return
      this.srcList = [file.url]
      this.showViewer = true
    },
    closeViewer() {
      this.showViewer = false
    },
    // 删除图片
    handleRemove(file) {
      this.fileList = []
      this.form.idCardImage = undefined
      this.isUploading = false
      this.form.inputCustomerName = ''
      this.form.customerName = ''
      this.form.idCard = ''
    },
    // 身份证图片转数组
    idCardImageToArr(file) {
      if (!file) return []
      return [file]
    },
    // 金蝶应收汇总
    handleQueryKingdee() {
      this.kingdeeLoading = true
      const name2 = this.form.customerName
      getCustomerList({ name2 }).then(res => {
        const { code, data } = res
        if (code === 200) {
          const { data: customerList } = data
          if (!!customerList.length) {
            const Number = customerList[0].Number
            const queryParams = {
              beginDate: this.parseTime(new Date('2023-08-01'), '{y}-{m}-{d}'),
              endDate: this.parseTime(new Date(), '{y}-{m}-{d}'),
              customerNumber: Number
            }
            getReceivableSummary(queryParams).then(res => {
              const { code, data } = res
              if (code === 200) {
                const { data: list } = data
                if (!!list.length) {
                  const CurrencyForName = list[0].CurrencyForName
                  this.kingdeeInfo = list[list.length - 1]
                  this.kingdeeInfo.CurrencyForName = CurrencyForName
                } else this.$message.error('金蝶内无此客户应收汇总数据')
                this.kingdeeLoading = false
              } else {
                this.$message.error(msg)
                this.kingdeeLoading = false
              }
            })
          } else {
            this.$message.error('金蝶内无此客户')
            this.kingdeeLoading = false
          }
        } else {
          this.$message.error(msg)
          this.kingdeeLoading = false
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';
.cascaderBox {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  .cascaderItem {
    display: inline-flex;
    align-items: center;
    position: relative;
    margin-right: 20px;
    margin-bottom: 10px;
    .cascaderCheck {
      margin-right: 5px;
    }
    .cascaderNum {
      position: absolute;
      left: 19px;
      z-index: 2;
      display: inline-block;
      width: 24px;
      text-align: center;
      background-color: #cbd6e2;
      color: #ffffff;
      border-radius: 4px 0 0 4px;
    }
    &.cascaderAdd {
      justify-content: center;
      width: 125px;
      border: 1px dashed #2e73f3;
      background-color: #dbe8ff;
      border-radius: 4px;
      color: #2e73f3;
      cursor: pointer;
      span {
        padding-left: 10px;
      }
      &:hover {
        background-color: #2e73f3;
        color: #ffffff;
      }
      &.disabled {
        background-color: #f5f7fa;
        border-color: #e4e7ed;
        color: #c0c4cc;
        cursor: not-allowed;
      }
    }
    ::v-deep {
      .el-cascader {
        padding-left: 24px;
        .el-input__inner {
          border-radius: 0 4px 4px 0;
          width: 150px;
        }
      }
    }
    .cascaderInfo {
      .el-cascader-panel {
        height: 225px;
        .el-cascader-menu {
          .el-cascader-menu__wrap {
            height: 100%;
            overflow-x: hidden;
            .el-cascader-menu__empty-text {
              color: #606266;
            }
          }
          &:nth-child(3n + 1) {
            background-color: #ffffff;
            .el-cascader-node:not(.is-disabled):focus,
            .el-cascader-node:not(.is-disabled):hover,
            .el-cascader-node:not(.is-disabled).in-active-path {
              background-color: #2e73f3;
              color: #ffffff;
            }
          }
          &:nth-child(3n + 2) {
            background-color: #f2f4f8;
            .el-cascader-node:not(.is-disabled):focus,
            .el-cascader-node:not(.is-disabled):hover,
            .el-cascader-node:not(.is-disabled).in-active-path {
              background-color: #d3dae7;
            }
          }
          &:nth-child(3n) {
            background-color: #d3dae7;
            .el-cascader-node:not(.is-disabled):focus,
            .el-cascader-node:not(.is-disabled):hover,
            .el-cascader-node:not(.is-disabled).in-active-path {
              background-color: #d3dae7;
              color: #2e73f3;
            }
          }
        }
      }
    }
  }
}
::v-deep {
  .el-form {
    .el-form-item__label {
      line-height: 20px;
      min-height: 40px;
      display: inline-flex;
      align-items: center;
      font-weight: normal;
      color: $disabled;
      text-align: left;
    }
  }
  .idCardImage {
    .el-upload--picture-card {
      width: 240px;
    }
    .el-upload-list--picture-card .el-upload-list__item {
      width: 240px;
      margin-bottom: 0;
    }
    &Bg {
      display: flex;
      width: 100%;
      height: 100%;
      background: url('~@/assets/images/idCard.png') no-repeat center;
      background-size: 100% 100%;
    }
    &.hide {
      .el-upload.el-upload--picture-card {
        display: none;
      }
    }
  }
}
.kingdeeBox {
  display: flex;
  align-items: center;
  margin-bottom: 22px;
  .kingdeeInfo {
    flex: 1;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    font-size: 14px;
    line-height: 24px;
    padding: 8px 0;
  }
}
</style>
