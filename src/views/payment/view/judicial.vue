<template>
  <div>
    <el-dialog v-dialogDragBox :key="key" :title="title" :visible.sync="open" width="900px" class="custom-dialog" append-to-body :close-on-click-modal="false" :close-on-press-escape="false">
      <div class="info">
        <div class="infoTitle">{{ title }}</div>
        <!--开庭公告-->
        <el-descriptions class="infoDesc" :column="2" border v-if="type === 0">
          <el-descriptions-item label="开庭日期">{{ info.courtAnnounceDate || '-' }}</el-descriptions-item>
          <el-descriptions-item label="案号">
            {{ info.caseNum || '-' }}
            <el-tag type="warning" size="small" v-if="info.status == '1'">已结案</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="案由" :span="2">{{ info.caseReason || '-' }}</el-descriptions-item>
          <el-descriptions-item label="开庭法院">{{ info.court || '-' }}</el-descriptions-item>
          <el-descriptions-item label="法庭">{{ info.courtroom || '-' }}</el-descriptions-item>
          <el-descriptions-item label="诉讼地位" :span="2">
            <template v-if="info.hasOwnProperty('litigantInfos') && info.litigantInfos.length">
              <div class="courtItem" v-for="item in info.litigantInfos">
                <span>{{ item.identityName ? item.identityName + '：' : '' }}</span>
                <em>{{ item.name || '-' }}</em>
              </div>
            </template>
          </el-descriptions-item>
          <el-descriptions-item label="审判长">{{ info.presidingJudge || '-' }}</el-descriptions-item>
          <el-descriptions-item label="承办人">{{ info.underTaker || '-' }}</el-descriptions-item>
          <el-descriptions-item label="公告来源">{{ info.sourceTable }}</el-descriptions-item>
        </el-descriptions>
        <!--送达公告-->
        <el-descriptions class="infoDesc" :column="2" border v-if="type === 1">
          <el-descriptions-item label="发布日期">{{ info.announceDate ? parseTime(info.announceDate, '{y}-{m}-{d}') : '-' }}</el-descriptions-item>
          <el-descriptions-item label="案号">
            {{ info.caseNum || '-' }}
            <el-tag type="warning" size="small" v-if="info.status == '1'">已结案</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="案由">{{ info.caseReason || '-' }}</el-descriptions-item>
          <el-descriptions-item label="法院">{{ info.court || '-' }}</el-descriptions-item>
          <el-descriptions-item label="诉讼地位" :span="2">
            <template v-if="info.hasOwnProperty('litigantInfos') && info.litigantInfos.length">
              <div class="courtItem" v-for="item in info.litigantInfos">
                <span>{{ item.identityName ? item.identityName + '：' : '' }}</span>
                <em>{{ item.name || '-' }}</em>
              </div>
            </template>
          </el-descriptions-item>
          <el-descriptions-item label="内容" :span="2">
            <div v-html="info.content"></div>
          </el-descriptions-item>
        </el-descriptions>
        <!--失信被执行人-->
        <el-descriptions class="infoDesc" :column="2" border v-if="type === 2">
          <el-descriptions-item label="被执行人">{{ info.itName || '-' }}</el-descriptions-item>
          <el-descriptions-item label="案号">
            {{ info.caseNum || '-' }}
            <el-tag type="warning" size="small" v-if="info.status == '1'">已结案</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="执行法院" :span="2">{{ info.court || '-' }}</el-descriptions-item>
          <el-descriptions-item label="立案日期">{{ info.announceDate || '-' }}</el-descriptions-item>
          <el-descriptions-item label="发布日期">{{ info.publishDate || '-' }}</el-descriptions-item>
          <el-descriptions-item label="申请执行人" :span="2">{{ info.applicant || '-' }}</el-descriptions-item>
        </el-descriptions>
        <!--被执行人-->
        <el-descriptions class="infoDesc" :column="2" border v-if="type === 3">
          <el-descriptions-item label="被执行人">{{ info.executorName || '-' }}</el-descriptions-item>
          <el-descriptions-item label="组织机构代码">{{ info.organizationCode || '-' }}</el-descriptions-item>
          <el-descriptions-item label="案号">
            {{ info.caseNum || '-' }}
            <el-tag type="warning" size="small" v-if="info.status == '1'">已结案</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="执行法院">{{ info.court || '-' }}</el-descriptions-item>
          <el-descriptions-item label="立案日期">{{ info.registerDate || '-' }}</el-descriptions-item>
          <el-descriptions-item label="执行标的(元)">{{ info.executeTarget || '' }}</el-descriptions-item>
          <el-descriptions-item label="申请执行人" :span="2">{{ info.applyExecutorDetail ? info.applyExecutorDetail.map(item => item.name).join('、') : '-' }}</el-descriptions-item>
        </el-descriptions>
        <!--立案信息-->
        <el-descriptions class="infoDesc" :column="2" border v-if="type === 4">
          <el-descriptions-item label="案号">
            {{ info.caseNum || '-' }}
            <el-tag type="warning" size="small" v-if="info.status == '1'">已结案</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="立案日期">{{ info.announceDate || '-' }}</el-descriptions-item>
          <el-descriptions-item label="案由">{{ info.caseReason || '-' }}</el-descriptions-item>
          <el-descriptions-item label="法院">{{ info.court || '-' }}</el-descriptions-item>
          <el-descriptions-item label="诉讼地位" :span="2">
            <template v-if="info.hasOwnProperty('litigantInfos') && info.litigantInfos.length">
              <div class="courtItem" v-for="item in info.litigantInfos">
                <span>{{ item.identityName ? item.identityName + '：' : '' }}</span>
                <em>{{ item.name || '-' }}</em>
              </div>
            </template>
          </el-descriptions-item>
          <el-descriptions-item label="案件状态">{{ info.status == 1 ? '已结案' : '-' }}</el-descriptions-item>
          <el-descriptions-item label="开庭日期">{{ info.courtDate || '-' }}</el-descriptions-item>
          <el-descriptions-item label="承办人">{{ info.undertaker || '' }}</el-descriptions-item>
          <el-descriptions-item label="结案日期">{{ info.filingDate || '-' }}</el-descriptions-item>
          <el-descriptions-item label="承办部门">{{ info.undertakingDept || '-' }}</el-descriptions-item>
        </el-descriptions>
        <!--终本案件-->
        <el-descriptions class="infoDesc" :column="2" border v-if="type === 5">
          <el-descriptions-item label="被执行人">{{ info.defaulter || '-' }}</el-descriptions-item>
          <el-descriptions-item label="组织机构代码">{{ info.creditCode || '-' }}</el-descriptions-item>
          <el-descriptions-item label="案号">{{ info.caseNum || '-' }}</el-descriptions-item>
          <el-descriptions-item label="执行法院">{{ info.court || '-' }}</el-descriptions-item>
          <el-descriptions-item label="立案日期">{{ info.registerDate || '-' }}</el-descriptions-item>
          <el-descriptions-item label="终本日期">{{ info.finalDate || '-' }}</el-descriptions-item>
          <el-descriptions-item label="执行标的(元)">{{ info.money || '' }}</el-descriptions-item>
          <el-descriptions-item label="未履行金额(元)">{{ info.noPayMoney || '-' }}</el-descriptions-item>
          <el-descriptions-item label="申请执行人">{{ info.litigantInfos ? info.litigantInfos.map(item => item.name).join('、') : '-' }}</el-descriptions-item>
        </el-descriptions>
        <!--法院公告-->
        <el-descriptions class="infoDesc" :column="2" border v-if="type === 6">
          <el-descriptions-item label="刊登日期">{{ info.punishDate || '-' }}</el-descriptions-item>
          <el-descriptions-item label="公告类型">{{ info.announcementType || '-' }}</el-descriptions-item>
          <el-descriptions-item label="案由" :span="2">{{ info.caseReason || '-' }}</el-descriptions-item>
          <el-descriptions-item label="公告法院">{{ info.court || '-' }}</el-descriptions-item>
          <el-descriptions-item label="刊登版面">{{ info.publishMainPage || '-' }}</el-descriptions-item>
          <el-descriptions-item label="诉讼地位" :span="2">
            <template v-if="info.hasOwnProperty('litigantInfos') && info.litigantInfos.length">
              <div class="courtItem" v-for="item in info.litigantInfos">
                <span>{{ item.identityName ? item.identityName + '：' : '' }}</span>
                <em>{{ item.name || '-' }}</em>
              </div>
            </template>
          </el-descriptions-item>
          <el-descriptions-item label="相关案件号" :span="2">{{ info.caseNum || '-' }}</el-descriptions-item>
          <el-descriptions-item label="内容" :span="2">
            <div v-html="info.content"></div>
          </el-descriptions-item>
        </el-descriptions>
        <!--限制高消费-->
        <el-descriptions class="infoDesc" :column="2" border v-if="type === 7">
          <el-descriptions-item label="企业名称">{{ info.itName || '-' }}</el-descriptions-item>
          <el-descriptions-item label="限制对象">{{ info.defaulter || '-' }}</el-descriptions-item>
          <el-descriptions-item label="案号">{{ info.caseNum || '-' }}</el-descriptions-item>
          <el-descriptions-item label="执行法院">{{ info.court || '-' }}</el-descriptions-item>
          <el-descriptions-item label="立案日期">{{ info.announceDate || '-' }}</el-descriptions-item>
          <el-descriptions-item label="公告日期">{{ info.publishDate || '-' }}</el-descriptions-item>
          <el-descriptions-item label="限制消费令" :span="2">
            <img src="~@/assets/images/pdf.png" style="width: 20px; cursor: pointer" alt="限制消费令" @click="handleViewFile(info.fileList)" v-if="info.fileList" />
            <span v-else>-</span>
          </el-descriptions-item>
          <el-descriptions-item label="申请执行人" :span="2">{{ info.applicant || '-' }}</el-descriptions-item>
        </el-descriptions>
        <template v-if="info.caseInfo">
          <div class="infoTitle mt20">所属司法案件</div>
          <el-descriptions class="infoDesc" :column="2" border>
            <el-descriptions-item label="案件名称">{{ info.caseInfo.title }}</el-descriptions-item>
            <el-descriptions-item label="案件类型">{{ info.caseInfo.caseType }}</el-descriptions-item>
            <el-descriptions-item label="案由">{{ info.caseInfo.caseReason }}</el-descriptions-item>
            <el-descriptions-item label="案号">{{ info.caseInfo.caseNum ? info.caseInfo.caseNum.join('、') : '' }}</el-descriptions-item>
            <el-descriptions-item label="法院">{{ info.caseInfo.court ? info.caseInfo.court.join('、') : '' }}</el-descriptions-item>
            <el-descriptions-item label="最新进程">{{ info.caseInfo.lastProcedure }}</el-descriptions-item>
          </el-descriptions>
        </template>
      </div>
    </el-dialog>

    <el-dialog v-dialogDragBox title="预览" :visible.sync="pdfOpen" width="1150px" class="custom-dialog" append-to-body>
      <div style="display: flex; justify-content: space-between; align-items: center; padding-bottom: 10px" v-if="pdfCount > 1">
        <el-button type="primary" size="small" @click="pdfCurrent--" :disabled="pdfCurrent < 2">
          <i class="el-icon-arrow-left"></i>
          上一页
        </el-button>
        <span>{{ pdfCurrent }} / {{ pdfCount }}</span>
        <el-button type="primary" size="small" @click="pdfCurrent++" :disabled="pdfCurrent >= pdfCount">
          下一页
          <i class="el-icon-arrow-right"></i>
        </el-button>
      </div>
      <div class="page-pdf">
        <Pdf :src="pdfUrl" :page="pdfCurrent" @num-pages="pdfCount = $event" />
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { parseTime } from '@/utils/ruoyi'

export default {
  data() {
    return {
      title: '详情',
      open: false,
      info: {},
      type: 0,
      key: 1,
      // pdf预览
      pdfOpen: false,
      pdfCurrent: 1,
      pdfCount: 0,
      pdfUrl: ''
    }
  },
  methods: {
    parseTime,
    handleView(obj = {}, title = '详情', type = 0) {
      this.info = obj
      this.title = title
      this.type = type
      this.open = true
    },
    handleViewFile(file) {
      if (file) {
        this.pdfUrl = `https://wswnews.finchina.com/${file}`
        this.pdfCurrent = 1
        this.pdfOpen = true
      }
    }
  }
}
</script>
<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';
.mt20 {
  margin-top: 20px;
}
.info {
  padding: 0 20px;
  &Title {
    font-size: 16px;
    color: $blue;
    font-weight: 500;
    padding: 0 20px;
    line-height: 50px;
    border: 1px solid #e2e6f3;
    border-bottom-width: 0;
  }
  &Desc {
    ::v-deep {
      .el-descriptions__body {
        color: $font;
      }
      .is-bordered .el-descriptions-item__cell {
        border-color: #ebedf3;
      }
      .el-descriptions-item__label.is-bordered-label {
        font-size: 12px;
        color: $info;
        width: 100px;
        background-color: #f8faff;
      }
      .el-descriptions-item__content {
        width: 330px;
      }
    }
  }
  .courtItem {
    font-size: 12px;
    line-height: 20px;
    span {
      color: $font;
    }
    em {
      font-style: normal;
      color: $blue;
    }
  }
}
</style>
