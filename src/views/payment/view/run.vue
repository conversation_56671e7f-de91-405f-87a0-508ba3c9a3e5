<template>
  <div>
    <el-dialog v-dialogDragBox :key="key" :title="title" :visible.sync="open" width="900px" class="custom-dialog" append-to-body :close-on-click-modal="false" :close-on-press-escape="false">
      <div class="info">
        <template v-if="type === 0">
          <div class="infoTitle">{{ info.title || '-' }}</div>
          <div class="infoLabel">
            <span>发布日期：{{ info.publishDate || '-' }}</span>
            <span>来源：{{ info.noticeSource || '-' }}</span>
            <span>招标编号：{{ info.itemCourseID || '-' }}</span>
          </div>
          <el-divider><span style="color: #999999">公告正文</span></el-divider>
          <div class="infoContent" v-html="info.content"></div>
          <el-divider><span style="color: #999999">免责声明</span></el-divider>
          <div class="infoDeclaration">免责声明：该文章系转载，目的在于传递更多市场信息，并不代表本站赞同其观点和对其真实性负责。如涉及文章内容、版权和其它问题，请与本站联系，我们将在第一时间予以处理！请联系自由客紧固件（ziyouke.net）（15188838176）处理。</div>
        </template>
        <el-descriptions class="infoDesc" :column="2" border v-if="type === 1">
          <el-descriptions-item label="作品名称" :span="2">{{ info.rightName || '-' }}</el-descriptions-item>
          <el-descriptions-item label="登记日期">{{ info.registerDateStr || '-' }}</el-descriptions-item>
          <el-descriptions-item label="登记号">{{ info.registerCode || '-' }}</el-descriptions-item>
          <el-descriptions-item label="著作权人" :span="2">
            <template v-if="info.hasOwnProperty('applyMans') && info.applyMans.length">
              {{ info.applyMans.map(item => item.name).join('、') }}
            </template>
          </el-descriptions-item>
          <el-descriptions-item label="创作完成日期">{{ info.createdDateStr || '-' }}</el-descriptions-item>
          <el-descriptions-item label="首次发表日期">{{ info.publishDateStr || '-' }}</el-descriptions-item>
        </el-descriptions>
        <el-descriptions class="infoDesc" :column="2" border v-if="type === 2">
          <el-descriptions-item label="软件名称" :span="2">{{ info.rightName || '-' }}</el-descriptions-item>
          <el-descriptions-item label="软件简称">{{ info.alias || '-' }}</el-descriptions-item>
          <el-descriptions-item label="登记号">{{ info.registerCode || '-' }}</el-descriptions-item>
          <el-descriptions-item label="著作权人" :span="2">
            <template v-if="info.hasOwnProperty('applyMans') && info.applyMans.length">
              {{ info.applyMans.map(item => item.name).join('、') }}
            </template>
          </el-descriptions-item>
          <el-descriptions-item label="登记日期">{{ info.registerDateStr || '-' }}</el-descriptions-item>
          <el-descriptions-item label="首次发表日期">{{ info.publishDateStr || '-' }}</el-descriptions-item>
        </el-descriptions>
        <template v-if="type === 3">
          <div class="infoTitle">{{ info.title || '-' }}</div>
          <div class="infoLabel">
            <span>发布日期：{{ info.date ? dateFormat(info.date) : '-' }}</span>
            <span>来源：{{ info.source || '-' }}</span>
          </div>
          <el-divider><span style="color: #999999">公告正文</span></el-divider>
          <div class="infoContent" v-html="info.content"></div>
          <el-divider><span style="color: #999999">免责声明</span></el-divider>
          <div class="infoDeclaration">免责声明：该文章系转载，目的在于传递更多市场信息，并不代表本站赞同其观点和对其真实性负责。如涉及文章内容、版权和其它问题，请与本站联系，我们将在第一时间予以处理！请联系自由客紧固件（ziyouke.net）（15188838176）处理。</div>
        </template>
      </div>
    </el-dialog>
  </div>
</template>
<script>
export default {
  data() {
    return {
      title: '详情',
      open: false,
      info: {},
      type: 0,
      key: 1
    }
  },
  methods: {
    handleView(obj = {}, title = '详情', type = 0) {
      this.info = obj
      this.title = title
      this.type = type
      this.open = true
      this.$nextTick(() => {
        this.removeStyle()
      })
    },
    // 删除内容table、tr、td设置的style
    removeStyle() {
      const content = document.querySelector('.infoContent')
      if (content) {
        const tables = content.querySelectorAll('table')
        tables.forEach(table => {
          table.removeAttribute('style')
          const trs = table.querySelectorAll('tr')
          trs.forEach(tr => {
            tr.removeAttribute('style')
            const tds = tr.querySelectorAll('td')
            tds.forEach(td => {
              td.removeAttribute('style')
            })
          })
        })
      }
    },
    // 格式化日期
    dateFormat(date) {
      if (!date) return '-'
      const yaer = date.slice(0, 4)
      const month = date.slice(4, 6)
      const day = date.slice(6, 8)
      return `${yaer}-${month}-${day}`
    }
  }
}
</script>

<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';
.info {
  padding: 0 20px;
  &Title {
    word-break: break-all;
    font-size: 22px;
    font-weight: 500;
    margin-bottom: 10px;
    color: $font;
  }
  &Label {
    font-size: 14px;
    color: $info;
    span + span {
      margin-left: 20px;
    }
  }
  &Content {
    font-size: 14px;
    color: $font;
    margin-top: 10px;
    line-height: 1.8;
    ::v-deep {
      table {
        border-collapse: collapse;
        table-layout: fixed;
        word-break: break-all;
        overflow: auto;
        th,
        td {
          padding: 5px;
          word-break: break-all;
          white-space: normal;
          text-align: left;
        }
      }
    }
  }
  &Declaration {
    font-size: 12px;
    line-height: 1.5;
    color: $disabled;
    margin-top: 10px;
  }
}
</style>
