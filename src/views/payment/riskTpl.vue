<template>
  <div class="newBox bgcf9 vh-130">
    <div class="custom-search flex">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" @submit.native.prevent>
        <el-form-item label="企业名称" prop="name">
          <el-input v-model="queryParams.name" placeholder="请输入企业名称" clearable @keyup.enter.native="handleQuery" size="small" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="tableBox">
      <el-table v-loading="loading" ref="table" stripe :data="list" row-key="id" class="custom-table">
        <el-table-column align="center" type="index" label="序号"></el-table-column>
        <el-table-column align="left" prop="name" label="企业名称" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <span class="table-link" @click="handleDetail(row)">{{ row.name }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="companyRegLocation" label="所在地" show-overflow-tooltip>
          <template slot-scope="{ row }">{{ removeHtmlTag(row.companyRegLocation, 300) || '-' }}</template>
        </el-table-column>
        <el-table-column align="center" prop="specs" label="风险预警"></el-table-column>
        <el-table-column align="center" prop="specs" label="交易次数"></el-table-column>
        <el-table-column align="center" prop="companyLegalPersonName" label="联系人"></el-table-column>
        <el-table-column align="center" prop="companyPhone" label="联系方式"></el-table-column>
        <el-table-column align="center" prop="updateTime" label="更新日期"></el-table-column>
        <el-table-column align="center" label="操作" width="220">
          <template slot-scope="{ row }">
            <el-button class="table-btn primary" @click="handleDetail(row)">查看详情</el-button>
            <el-button class="table-btn primary" @click="handleReplenish(row)">补充信息</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!--  补充信息  -->
    <company ref="company" />
  </div>
</template>
<script>
import { enterprisesDetail, enterprisesList } from '@/api/payment'
import Company from './company'
import { removeHtmlTag } from '@/utils'

export default {
  components: { Company },
  data() {
    return {
      loading: true,
      list: [],
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: undefined
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    removeHtmlTag,
    // 列表
    getList() {
      this.loading = true
      enterprisesList(this.queryParams).then(res => {
        const { code, msg, rows } = res
        if (code === 200) {
          let obj
          rows.forEach(item => {
            obj = { ...this.isJSON(item.info) }
            console.log(obj)
            item.companyRegLocation = obj.companyRegLocation
            item.companyLegalPersonName = obj.companyLegalPersonName
            item.companyPhone = obj.companyPhoneSourceList && obj.companyPhoneSourceList.length > 0 ? obj.companyPhoneSourceList[0].number : ''
          })
          this.list = rows
          this.loading = false
        } else this.$message.error(msg)
      })
    },
    // 刷新列表
    async refreshList() {
      const res = await enterprisesList(this.queryParams)
      const { code, rows } = res
      if (code === 200) {
        let obj
        rows.forEach(item => {
          obj = { ...this.isJSON(item.info) }
          
          item.companyRegLocation = obj.companyRegLocation
          item.companyLegalPersonName = obj.companyLegalPersonName
          item.companyPhone = obj.companyPhoneSourceList  && obj.companyPhoneSourceList.length > 0 ? obj.companyPhoneSourceList[0].number : ''
        })
        this.$set(this, 'list', rows)
      }
    },
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 重置搜索
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 查看企业详情
    handleDetail(row) {
      enterprisesDetail({ enterpriseId: row.id }).then(res => {
        this.$refs.company.getInfo(res.data, 'risk')
      })
    },
    // 补充信息
    handleReplenish(row) {
      row.info = this.isJSON(row.info)
      this.$refs.company.handleReplenish(row)
    },
    isJSON(str) {
      try {
        JSON.parse(str)
      } catch (e) {
        // 转换出错，抛出异常
        return str
      }
      return JSON.parse(str)
    }
  }
}
</script>

<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';
.vh-130 {
  min-height: calc(100vh - 130px);
}
.custom-search {
  padding-top: 17px;
  ::v-deep {
    .el-form-item--small {
      display: inline-flex;
      .el-form-item__label {
        flex-shrink: 0;
      }
      .el-form-item {
        margin-bottom: 10px;
      }
    }
  }
}
.tableBox {
  padding: 20px;
  ::v-deep {
    .table-btn {
      padding: 0;
    }
  }
}
</style>
