<template>
    <div class="newBox bgcf9 vh-85" v-if="checkPermi(['sys:seal:list'])">
        <!-- 搜索 -->
        <div class="custom-search flex">
            <div class="flex">
                <div class="custom-search-form flex">
                    <input type="text" v-model="queryParams.userAt" placeholder="请输入用章人名称" class="custom-search-input"
                        @keyup.enter="handleQuery" />
                    <button type="button" class="custom-search-button pointer" @click="handleQuery">
                        <i class="el-icon-search"></i>
                        搜索
                    </button>
                </div>
                <button type="button" class="custom-search-add pointer"
                    style="background: #e0ebff; border-radius: 5px; border: 1px solid #2e73f3; color: #2e73f3"
                    @click="refreshList">
                    <i class="el-icon-refresh" style="color: #2e73f3"></i>
                    刷新重置
                </button>
                <button type="button" class="custom-search-add pointer" @click="handleAdd">
                    <i class="el-icon-plus"></i>
                    添加存档
                </button>

                <el-select size="small" style="margin-left: 15px;" v-model="amountType" @change="amountChange" v-if="queryParams.usingAt === 'HT-000' && activeName === 'HT-001'" placeholder="请选择合同金额范围">
                    <el-option v-for="item in amountOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
            </div>
            <div class="classify-toolbar">
                <right-toolbar :search="false" @queryTable="getList"
                    :columns="queryParams.usingAt == 'HT-000' ? contractColumns : (queryParams.usingAt == 'ZZ-001' ? credentialColumns : (queryParams.usingAt == 'DZ-001' ? reconciliationColumns : otherColumns))"></right-toolbar>
            </div>
        </div>

        <!-- 分类 -->
        <div class="classify flex">
            <div class="classify-item" :class="{ active: item.value == queryParams.usingAt }"
                v-for="item in statusOptions" :key="item.value" @click="handleChangeStatus(item)">{{ item.label }}</div>
        </div>

        <!-- 表格数据 -->
        <div class="tableBox" v-if="queryParams.usingAt == 'HT-000'">
            <el-tabs type="border-card" v-model="activeName" @tab-click="handleQuery">
                <el-tab-pane label="销售合同" name="HT-001">
                    <el-table v-loading="loading" ref="table" stripe :data="list" row-key="id" :key="key"
                        style="width: 100%" class="custom-table custom-table-cell5">
                        <el-table-column align="center" type="index" label="序号"
                            v-if="contractColumns[0].visible"></el-table-column>
                        <el-table-column align="center" label="合同编号" show-overflow-tooltip
                            v-if="contractColumns[1].visible">
                            <template slot-scope="{ row }">
                                <span>{{ row.usingName }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column align="center" label="客户" show-overflow-tooltip
                            v-if="contractColumns[2].visible">
                            <template slot-scope="{ row }">
                                <span class="table-link" @click="handleCompanyView(row)">{{ row.customerName }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column align="center" label="用章人" show-overflow-tooltip
                            v-if="contractColumns[3].visible">
                            <template slot-scope="{ row }">
                                <div>
                                    <span
                                        style="font-weight: 500; font-size: 12px; color: #333333; margin-right: 5px;">{{
                                            row.userAtDO && row.userAtDO.nickName }}</span>
                                    <span style="font-weight: 400; font-size: 12px; color: #666666;">{{ row.deptName
                                        }}</span>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column align="center" label="合同金额" show-overflow-tooltip
                            v-if="contractColumns[4].visible">
                            <template slot-scope="{ row }">
                                <span style="font-weight: 500; font-size: 14px; color: #F35D09;">￥ {{ row.amount
                                    }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column align="center" label="备注信息" show-overflow-tooltip
                            v-if="contractColumns[5].visible">
                            <template slot-scope="{ row }">
                                <span>{{ row.remark }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column align="center" label="录入时间" show-overflow-tooltip
                            v-if="contractColumns[6].visible">
                            <template slot-scope="{ row }">
                                <span>{{ row.createTime }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column align="center" label="操作">
                            <template slot-scope="{ row }">
                                <button type="button" class="table-btn primary" @click="handleEdit(row)"
                                    v-if="checkPermi(['sys:seal:edit'])">编辑</button>
                                <button type="button" class="table-btn" @click="handleView(row)">查看详情</button>
                                <!-- <button type="button" class="table-btn danger" @click="handleDelete(row)"
                                    v-if="checkPermi(['sys:seal:del'])">删除</button> -->
                            </template>
                        </el-table-column>
                    </el-table>
                </el-tab-pane>
                <el-tab-pane label="采购合同" name="HT-002">
                    <el-table v-loading="loading" ref="table" stripe :data="list" row-key="id" :key="key"
                        style="width: 100%" class="custom-table custom-table-cell5">
                        <el-table-column align="center" type="index" label="序号"
                            v-if="contractColumns[0].visible"></el-table-column>
                        <el-table-column align="center" label="合同编号" show-overflow-tooltip
                            v-if="contractColumns[1].visible">
                            <template slot-scope="{ row }">
                                <span>{{ row.usingName }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column align="center" label="客户" show-overflow-tooltip
                            v-if="contractColumns[2].visible">
                            <template slot-scope="{ row }">
                                <span class="table-link" @click="handleCompanyView(row)">{{ row.customerName }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column align="center" label="用章人" show-overflow-tooltip
                            v-if="contractColumns[3].visible">
                            <template slot-scope="{ row }">
                                <div>
                                    <span
                                        style="font-weight: 500; font-size: 12px; color: #333333; margin-right: 5px;">{{
                                            row.userAtDO && row.userAtDO.nickName }}</span>
                                    <span style="font-weight: 400; font-size: 12px; color: #666666;">{{ row.deptName
                                        }}</span>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column align="center" label="合同金额" show-overflow-tooltip
                            v-if="contractColumns[4].visible" width="100">
                            <template slot-scope="{ row }">
                                <span style="font-weight: 500; font-size: 14px; color: #F35D09;">￥ {{ row.amount
                                    }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column align="center" label="备注信息" show-overflow-tooltip
                            v-if="contractColumns[5].visible">
                            <template slot-scope="{ row }">
                                <span>{{ row.remark }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column align="center" label="录入时间" show-overflow-tooltip
                            v-if="contractColumns[6].visible">
                            <template slot-scope="{ row }">
                                <span>{{ row.createTime }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column align="center" label="操作">
                            <template slot-scope="{ row }">
                                <button type="button" class="table-btn primary" @click="handleEdit(row)"
                                    v-if="checkPermi(['sys:seal:edit'])">编辑</button>
                                <button type="button" class="table-btn" @click="handleView(row)">查看详情</button>
                                <!-- <button type="button" class="table-btn danger" @click="handleDelete(row)"
                                    v-if="checkPermi(['sys:seal:del'])">删除</button> -->
                            </template>
                        </el-table-column>
                    </el-table>
                </el-tab-pane>
            </el-tabs>
            <div class="custom-pagination">
                <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
                    :limit.sync="queryParams.pageSize" @pagination="getList" />
            </div>
        </div>
        <div class="tableBox" v-if="queryParams.usingAt == 'ZZ-001'">
            <el-table v-loading="loading" ref="table" stripe :data="list" row-key="id" :key="key" style="width: 100%"
                class="custom-table custom-table-cell5">
                <el-table-column align="center" type="index" label="序号"
                    v-if="contractColumns[0].visible"></el-table-column>
                <el-table-column align="center" label="项目名称" show-overflow-tooltip v-if="contractColumns[1].visible">
                    <template slot-scope="{ row }">
                        <span>{{ row.usingName }}</span>
                    </template>
                </el-table-column>
                <el-table-column align="center" label="客户" show-overflow-tooltip v-if="contractColumns[2].visible">
                    <template slot-scope="{ row }">
                        <span class="table-link" @click="handleCompanyView(row)">{{ row.customerName }}</span>
                    </template>
                </el-table-column>
                <el-table-column align="center" label="用章人" show-overflow-tooltip v-if="contractColumns[3].visible">
                    <template slot-scope="{ row }">
                        <div>
                            <span style="font-weight: 500; font-size: 12px; color: #333333; margin-right: 5px;">{{
                                row.userAtDO && row.userAtDO.nickName }}</span>
                            <span style="font-weight: 400; font-size: 12px; color: #666666;">{{ row.deptName }}</span>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column align="center" label="涉及金额" show-overflow-tooltip v-if="contractColumns[4].visible"
                    width="100">
                    <template slot-scope="{ row }">
                        <span style="font-weight: 500; font-size: 14px; color: #F35D09;">￥ {{ row.amount }}</span>
                    </template>
                </el-table-column>
                <el-table-column align="center" label="备注信息" show-overflow-tooltip v-if="contractColumns[5].visible">
                    <template slot-scope="{ row }">
                        <span>{{ row.remark }}</span>
                    </template>
                </el-table-column>
                <el-table-column align="center" label="录入时间" show-overflow-tooltip v-if="contractColumns[6].visible">
                    <template slot-scope="{ row }">
                        <span>{{ row.createTime }}</span>
                    </template>
                </el-table-column>
                <el-table-column align="center" label="操作">
                    <template slot-scope="{ row }">
                        <button type="button" class="table-btn primary" @click="handleEdit(row)"
                            v-if="checkPermi(['sys:seal:edit'])">编辑</button>
                        <button type="button" class="table-btn" @click="handleView(row)">查看详情</button>
                        <!-- <button type="button" class="table-btn danger" @click="handleDelete(row)"
                            v-if="checkPermi(['sys:seal:del'])">删除</button> -->
                    </template>
                </el-table-column>
            </el-table>
            <div class="custom-pagination">
                <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
                    :limit.sync="queryParams.pageSize" @pagination="getList" />
            </div>
        </div>
        <div class="tableBox" v-if="queryParams.usingAt == 'DZ-001'">
            <el-table v-loading="loading" ref="table" stripe :data="list" row-key="id" :key="key" style="width: 100%"
                class="custom-table custom-table-cell5">
                <el-table-column align="center" type="index" label="序号"
                    v-if="contractColumns[0].visible"></el-table-column>
                <el-table-column align="center" label="客户" show-overflow-tooltip v-if="contractColumns[1].visible">
                    <template slot-scope="{ row }">
                        <span class="table-link" @click="handleCompanyView(row)">{{ row.customerName }}</span>
                    </template>
                </el-table-column>
                <el-table-column align="center" label="用章人" show-overflow-tooltip v-if="contractColumns[2].visible">
                    <template slot-scope="{ row }">
                        <div>
                            <span style="font-weight: 500; font-size: 12px; color: #333333; margin-right: 5px;">{{
                                row.userAtDO && row.userAtDO.nickName }}</span>
                            <span style="font-weight: 400; font-size: 12px; color: #666666;">{{ row.deptName }}</span>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column align="center" label="账款金额" show-overflow-tooltip v-if="contractColumns[3].visible"
                    width="100">
                    <template slot-scope="{ row }">
                        <span style="font-weight: 500; font-size: 14px; color: #F35D09;">￥ {{ row.amount }}</span>
                    </template>
                </el-table-column>
                <el-table-column align="center" label="备注信息" show-overflow-tooltip v-if="contractColumns[4].visible">
                    <template slot-scope="{ row }">
                        <span>{{ row.remark }}</span>
                    </template>
                </el-table-column>
                <el-table-column align="center" label="录入时间" show-overflow-tooltip v-if="contractColumns[5].visible">
                    <template slot-scope="{ row }">
                        <span>{{ row.createTime }}</span>
                    </template>
                </el-table-column>
                <el-table-column align="center" label="操作">
                    <template slot-scope="{ row }">
                        <button type="button" class="table-btn primary" @click="handleEdit(row)"
                            v-if="checkPermi(['sys:seal:edit'])">编辑</button>
                        <button type="button" class="table-btn" @click="handleView(row)">查看详情</button>
                        <!-- <button type="button" class="table-btn danger" @click="handleDelete(row)"
                            v-if="checkPermi(['sys:seal:del'])">删除</button> -->
                    </template>
                </el-table-column>
            </el-table>
            <div class="custom-pagination">
                <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
                    :limit.sync="queryParams.pageSize" @pagination="getList" />
            </div>
        </div>
        <div class="tableBox" v-if="queryParams.usingAt == 'QT-001'">
            <el-table v-loading="loading" ref="table" stripe :data="list" row-key="id" :key="key" style="width: 100%"
                class="custom-table custom-table-cell5">
                <el-table-column align="center" type="index" label="序号"
                    v-if="contractColumns[0].visible"></el-table-column>
                <el-table-column align="center" label="客户" show-overflow-tooltip v-if="contractColumns[1].visible">
                    <template slot-scope="{ row }">
                        <span class="table-link" @click="handleCompanyView(row)">{{ row.customerName }}</span>
                    </template>
                </el-table-column>
                <el-table-column align="center" label="用章人" show-overflow-tooltip v-if="contractColumns[2].visible">
                    <template slot-scope="{ row }">
                        <div>
                            <span style="font-weight: 500; font-size: 12px; color: #333333; margin-right: 5px;">{{
                                row.userAtDO && row.userAtDO.nickName }}</span>
                            <span style="font-weight: 400; font-size: 12px; color: #666666;">{{ row.deptName }}</span>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column align="center" label="涉及金额" show-overflow-tooltip v-if="contractColumns[3].visible"
                    width="100">
                    <template slot-scope="{ row }">
                        <span style="font-weight: 500; font-size: 14px; color: #F35D09;">￥ {{ row.amount }}</span>
                    </template>
                </el-table-column>
                <el-table-column align="center" label="备注信息" show-overflow-tooltip v-if="contractColumns[4].visible">
                    <template slot-scope="{ row }">
                        <span>{{ row.remark }}</span>
                    </template>
                </el-table-column>
                <el-table-column align="center" label="录入时间" show-overflow-tooltip v-if="contractColumns[5].visible">
                    <template slot-scope="{ row }">
                        <span>{{ row.createTime }}</span>
                    </template>
                </el-table-column>
                <el-table-column align="center" label="操作">
                    <template slot-scope="{ row }">
                        <button type="button" class="table-btn primary" @click="handleEdit(row)"
                            v-if="checkPermi(['sys:seal:edit'])">编辑</button>
                        <button type="button" class="table-btn" @click="handleView(row)">查看详情</button>
                        <!-- <button type="button" class="table-btn danger" @click="handleDelete(row)"
                            v-if="checkPermi(['sys:seal:del'])">删除</button> -->
                    </template>
                </el-table-column>
            </el-table>
            <div class="custom-pagination">
                <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
                    :limit.sync="queryParams.pageSize" @pagination="getList" />
            </div>
        </div>

        <!--新增待办弹窗-->
        <el-dialog v-dialogDragBox :title="title" :visible.sync="addSealOpen" width="1150px" class="custom-dialog">
            <div class="p20">
                <el-form ref="form" :model="form" :rules="rules" label-width="8em" label-position="left"
                    :disabled="disabled">
                    <el-row :gutter="20">
                        <el-col :span="12">
                            <el-form-item label="客户" prop="customerId">
                                <el-select v-model="form.customerId" filterable placeholder="请选择客户" clearable
                                    @change="selectCustomer" style="width: 100%">
                                    <el-option v-for="(item, index) in customerList" :key="index" :label="item.name"
                                        :value="item.id" />
                                    <el-button type="text" icon="el-icon-plus" style="margin-left: 20px;"
                                        @click="addCustomer">新建客户</el-button>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="用章人" prop="userAt">
                                <el-select v-model="form.userAt" filterable placeholder="请选择用章人" clearable
                                    style="width: 100%">
                                    <el-option v-for="(item, index) in userList" :key="index"
                                        :label="item.realName || item.nickName" :value="item.userId" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item
                                :label="queryParams.usingAt == 'HT-000' ? '合同金额' : (queryParams.usingAt == 'DZ-001' ? '账款金额' : '涉及金额')"
                                prop="amount">
                                <el-input v-model="form.amount" placeholder="请输入金额">
                                    <template slot="append">元</template>
                                </el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="合同编号" prop="usingName" v-if="queryParams.usingAt == 'HT-000'">
                                <el-input v-model="form.usingName" placeholder="请输入合同编号">
                                    <el-select v-model="contractType" slot="append" placeholder="请选择"
                                        style="width: 120px;">
                                        <el-option label="销售合同" value="HT-001"></el-option>
                                        <el-option label="采购合同" value="HT-002"></el-option>
                                    </el-select>
                                </el-input>
                            </el-form-item>
                            <el-form-item label="项目" prop="usingName" v-if="queryParams.usingAt == 'ZZ-001'">
                                <el-input v-model="form.usingName" placeholder="请输入项目"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label="备注信息" prop="remark">
                                <el-input v-model="form.remark" type="textarea" rows="6"
                                    placeholder="请输入备注信息"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label="附件" prop="archiveFile">
                                <div style="display: flex; align-items: center; justify-content: flex-start;">
                                    <div @click="handleScannerOpen"
                                        style="width: 126px; height: 36px; background: #F8F9FB; border-radius: 5px; border: 1px solid #2E73F3; display: flex; align-items: center; justify-content: center; cursor: pointer;">
                                        <img style="width: 20px; height: 20px; margin-right: 10px;"
                                            src="@/assets/images/scanner.png" alt="">
                                        <span style="font-weight: 400; font-size: 14px; color: #2E73F3;">添加存档</span>
                                    </div>
                                    <div style="margin-left: 34px; font-weight: 400; font-size: 12px; color: #999999;">
                                        请将文件放在高拍仪正确位置上，点击左侧图标位置拍照添加</div>
                                </div>
                                <div class="suoluetu">
                                    <div v-for="(item, index) in form.archiveFile" :key="index"
                                        style="margin-right: 10px; margin-bottom: 10px; position: relative;">
                                        <image-preview :src="item" style="width: 80px; height: 80px;" />
                                        <!-- <i class="el-icon-error" @click="handleDelImg(index)"
                                            style="position: absolute; width: 18px; height: 18px; right: -9px; top: -5px; cursor: pointer;"></i> -->
                                    </div>
                                </div>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
            <div slot="footer">
                <el-button @click="addSealOpen = false" size="medium" style="width: 200px">取 消</el-button>
                <el-button type="primary" @click="handleSubmit" size="medium" style="width: 200px">确 定</el-button>
            </div>
        </el-dialog>

        <!--查看详情-->
        <el-dialog v-dialogDragBox :title="viewTitle" :visible.sync="viewOpen" width="1150px"
            class="custom-dialog"></el-dialog>

        <!--新增客户-->
        <create-dialog ref="create" type="kh" :currency-options="currencyOptions"></create-dialog>

        <!-- 高拍仪拍照 -->
        <el-dialog v-dialogDragBox :title="'高拍仪拍照'" :visible.sync="scannerOpen" width="1150px" class="custom-dialog">
            <div class="p20">
                <div id="view">
                    <img id="view1" :src="view1_src" alt="主摄像头">
                </div>
                <!-- 缩略图 -->
                <div class="suoluetu">
                    <div v-for="(item, index) in attachmentList" :key="index"
                        style="margin-right: 10px; margin-bottom: 10px; position: relative;">
                        <image-preview :src="item" style="width: 80px; height: 80px;" />
                        <i class="el-icon-error" @click="handleDelImg(index)"
                            style="position: absolute; width: 18px; height: 18px; right: -9px; top: -5px; cursor: pointer;"></i>
                    </div>
                </div>
                <!-- 功能按钮 -->
                <div class="myactive">
                    <el-button type="primary" size="small" @click="open_view1">打开主摄像头</el-button>
                    <el-button type="primary" size="small" @click="rotate(90)">左转</el-button>
                    <el-button type="primary" size="small" @click="rotate(270)">右转</el-button>
                    <el-button type="primary" size="small" @click="view1_scan">拍照</el-button>
                    <el-button type="primary" size="small" @click="flat_scan">展平拍照</el-button>
                </div>
            </div>
            <div slot="footer">
                <el-button @click="scannerOpen = false" size="medium" style="width: 200px">取 消</el-button>
                <el-button type="primary" @click="handleScan" size="medium" style="width: 200px">确 定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { listUser } from '@/api/system/user'
import { parseTime } from '@/utils/ruoyi'
import { checkPermi } from '@/utils/permission'
import createDialog from '@/views/houtai/siyu/gongying/create'
import { getlist } from '@/api/houtai/siyu/gongying'
import axios from 'axios'
import { signetList, signetAdd, signetEdit, signetDel } from '@/api/seal'

export default {
    name: 'Seal',
    components: { createDialog },
    data() {
        return {
            key: 1,
            // 搜索条件
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                userAt: undefined,
                usingAt: 'HT-000',
                startAmount: 0,
                endAmount: 99999999,
            },
            statusOptions: [
                { value: 'HT-000', label: '合同用章' },
                { value: 'ZZ-001', label: '资质用章' },
                { value: 'DZ-001', label: '对账用章' },
                { value: 'QT-001', label: '其他用章' }
            ],
            activeName: 'HT-001',
            // 加载
            loading: false,
            // 列表数据
            list: [],
            // 总条数
            total: 1,
            // 显隐列
            contractColumns: [
                { key: 0, label: `序号`, visible: true },
                { key: 1, label: `合同编号`, visible: true },
                { key: 2, label: `客户`, visible: true },
                { key: 3, label: `用章人`, visible: true },
                { key: 4, label: `合同金额`, visible: true },
                { key: 5, label: `备注信息`, visible: true },
                { key: 6, label: `录入时间`, visible: true }
            ],
            credentialColumns: [
                { key: 0, label: `序号`, visible: true },
                { key: 1, label: `项目名称`, visible: true },
                { key: 2, label: `客户`, visible: true },
                { key: 3, label: `用章人`, visible: true },
                { key: 4, label: `涉及金额`, visible: true },
                { key: 5, label: `备注信息`, visible: true },
                { key: 6, label: `录入时间`, visible: true }
            ],
            reconciliationColumns: [
                { key: 0, label: `序号`, visible: true },
                { key: 1, label: `客户`, visible: true },
                { key: 2, label: `用章人`, visible: true },
                { key: 3, label: `账款金额`, visible: true },
                { key: 4, label: `备注信息`, visible: true },
                { key: 5, label: `录入时间`, visible: true }
            ],
            otherColumns: [
                { key: 0, label: `序号`, visible: true },
                { key: 1, label: `客户`, visible: true },
                { key: 2, label: `用章人`, visible: true },
                { key: 3, label: `涉及金额`, visible: true },
                { key: 4, label: `备注信息`, visible: true },
                { key: 5, label: `录入时间`, visible: true }
            ],
            addSealOpen: false,
            title: '新建合同用章存档',
            form: {},
            rules: {
                // customerId: [
                //     { required: true, message: '请选择客户', trigger: 'change' }
                // ],
                // customerName: [
                //     { required: true, message: '请选择客户', trigger: 'change' }
                // ],
                userAt: [
                    { required: true, message: '请选择用章人', trigger: 'change' }
                ],
                usingAt: [
                    { required: true, message: '请选择用章类型', trigger: 'change' }
                ],
                amount: [
                    { required: true, message: '请输入金额', trigger: 'blur' }
                ],
                // archiveFile: [
                //     { required: true, message: '请添加存档附件', trigger: 'blur' }
                // ],
            },
            contractType: 'HT-001',
            addCustomerOpen: false,
            currencyOptions: [
                { id: 1, value: 'PRE001', en: 'CNY', zh: '人民币', symbol: '¥' },
                { id: 2, value: 'PRE002', en: 'HKD', zh: '香港元', symbol: 'HK$' },
                { id: 3, value: 'PRE003', en: 'EUR', zh: '欧元', symbol: '€' },
                { id: 4, value: 'PRE004', en: 'JPY', zh: '日本日圆', symbol: '￥' },
                { id: 5, value: 'PRE005', en: 'TWD', zh: '新台币元', symbol: 'NT$' },
                { id: 6, value: 'PRE006', en: 'GBP', zh: '英镑', symbol: '£' },
                { id: 7, value: 'PRE007', en: 'USD', zh: '美元', symbol: '$' }
            ],
            userList: [],
            customerList: [],
            viewTitle: '查看详情',
            viewOpen: false,
            scannerOpen: false,
            // 高拍仪相关设置
            view1_mode_selected: '1', // 主摄像头模式
            view1_mode_list: [],
            view1_resolution_selected: '',
            view1_resolution_list: [],
            view1_src: 'http://127.0.0.1:38088/video=stream&camidx=0', // 主摄像头视频流地址
            attachmentList: [], // 存档附件列表
            disabled: false,
            amountOptions: [{
                label: '不限',
                value: 0
            }, {
                label: '2万至7万',
                value: 1
            }, {
                label: '7万以上',
                value: 2
            }],
            amountType: 0,  // 金额区间选择

        }
    },
    watch: {
        addSealOpen(newVal) {
            if (newVal === false) {
                this.attachmentList = []
            }
        }
    },
    computed: {
        userId() {
            return this.$store.getters.info.userId
        }
    },
    created() {
        this.userInfo = this.$store.state.user.info
        this.getList()
        this.getUserList()
        this.getCustomer()
        // 初始化摄像头模式和分辨率
        this.initCameraSettings();
    },
    methods: {
        checkPermi,
        parseTime,
        // 获取人员
        async getUserList() {
            const user = await listUser()
            const userData = user.rows || []
            this.userList = userData
        },
        // 获取客户
        async getCustomer() {
            const customer = await getlist({
                pageNum: 1,
                pageSize: 10000,
                type: 'kh',
                status: 1
            })
            const customerData = customer.rows || []
            this.customerList = customerData
        },
        // 查询数据
        async getList() {
            if (this.queryParams.usingAt == 'HT-000') {
            }
            let queryParams = {
                pageNum: this.queryParams.pageNum,
                pageSize: this.queryParams.pageSize,
                usingAt: this.queryParams.usingAt == 'HT-000' ? this.activeName : this.queryParams.usingAt,
                userAt: this.queryParams.userAt,
                startAmount: this.queryParams.startAmount,
                endAmount: this.queryParams.endAmount,
            }
            this.loading = true
            const res = await signetList(queryParams)
            const { code, rows, total, msg } = res
            if (code === 200) {
                this.list = rows
                this.total = total
            } else this.$message.error(msg)
            this.loading = false
        },
        // 刷新列表
        refreshList() {
            this.queryParams.pageNum = 1
            this.queryParams.pageSize = 10
            this.queryParams.userAt = ''
            this.queryParams.startAmount = 0
            this.queryParams.endAmount = 99999999
            this.handleQuery()
        },
        // 切换分类
        handleChangeStatus(item) {
            this.queryParams.usingAt = item.value
            this.handleQuery()
        },
        // 合同金额区间筛选
        amountChange(value) {
            if (value === 0) {
                this.queryParams.startAmount = 0
                this.queryParams.endAmount = 99999999
            } else if (value === 1) {
                this.queryParams.startAmount = 20000
                this.queryParams.endAmount = 70000
            } else if (value === 2) {
                this.queryParams.startAmount = 70000
                this.queryParams.endAmount = 99999999
            }
            this.handleQuery()
        },
        // 搜索
        handleQuery() {
            this.queryParams.pageNum = 1
            this.getList()
        },
        // 新增
        handleAdd() {
            this.form = {
                amount: '',
                archiveFile: '',
                customerId: '',
                customerName: '',
                remark: '',
                userAt: '',
                usingAt: this.queryParams.usingAt == 'HT-000' ? this.contractType : this.queryParams.usingAt,
                usingName: '',
            }
            this.resetForm('form')
            this.title = this.queryParams.usingAt == 'HT-000' ? '新建合同用章存档' : (this.queryParams.usingAt == 'ZZ-001' ? '新建资质用章存档' : (this.queryParams.usingAt == 'DZ-001' ? '新建对账用章存档' : '新建其他用章存档'))
            this.addSealOpen = true
            this.disabled = false
        },
        // 编辑
        handleEdit(row) {
            this.form = { ...row }
            this.resetForm('form')
            if (this.queryParams.usingAt == 'HT-000') {
                this.contractType = this.form.usingAt
            }
            this.form.archiveFile = this.form.archiveFile ? this.form.archiveFile.split(',') : []
            this.title = this.queryParams.usingAt == 'HT-000' ? '编辑合同用章存档' : (this.queryParams.usingAt == 'ZZ-001' ? '编辑资质用章存档' : (this.queryParams.usingAt == 'DZ-001' ? '编辑对账用章存档' : '编辑其他用章存档'))
            this.addSealOpen = true
            this.disabled = false
        },
        // 查看详情
        handleView(row) {
            this.form = { ...row }
            this.resetForm('form')
            if (this.queryParams.usingAt == 'HT-000') {
                this.contractType = this.form.usingAt
            }
            this.form.archiveFile = this.form.archiveFile ? this.form.archiveFile.split(',') : []
            this.title = this.queryParams.usingAt == 'HT-000' ? '查看合同用章存档' : (this.queryParams.usingAt == 'ZZ-001' ? '查看资质用章存档' : (this.queryParams.usingAt == 'DZ-001' ? '查看对账用章存档' : '查看其他用章存档'))
            this.addSealOpen = true
            this.disabled = true
        },
        // 提交
        handleSubmit() {
            this.$refs.form.validate(valid => {
                if (valid) {
                    let data = {
                        ...this.form,
                        tableId: this.form.id,
                        usingAt: this.queryParams.usingAt == 'HT-000' ? this.contractType : this.queryParams.usingAt,
                        archiveFile: this.form.archiveFile.join(','),
                    }
                    if (this.form.id) {
                        signetEdit(data).then(res => {
                            const { code, msg } = res
                            if (code === 200) {
                                this.$message.success('修改成功')
                                this.handleQuery()
                                this.addSealOpen = false
                            } else this.$message.error(msg)
                        })
                    } else {
                        signetAdd(data).then(res => {
                            const { code, msg } = res
                            if (code === 200) {
                                this.$message.success('新增成功')
                                this.handleQuery()
                                this.addSealOpen = false
                            } else this.$message.error(msg)
                        })
                    }

                }
            })
        },

        // 新增客户
        addCustomer() {
            this.$refs.create.handleAdd('新增客户')
        },

        // 高拍仪
        handleScannerOpen() {
            if (this.disabled == true) return this.$message.error('查看详情时不可操作')
            if (this.form.id) {
                this.attachmentList = this.form.archiveFile
            } else {
                this.attachmentList = []
            }
            this.scannerOpen = true
        },
        // 初始化摄像头设置参数
        initCameraSettings() {
            // 加载主摄像头视频模式
            let view1_mode_data1 = { "camidx": "0", "mode": "0" };
            let view1_mode_data2 = { "camidx": "0", "mode": "1" };
            axios.post("http://127.0.0.1:38088/device=getresolution", JSON.stringify(view1_mode_data2)).then((res) => {
                if (res.data.data.split("|").length > 1) {
                    let data = new Object()
                    data.key = '1'
                    data.value = 'MJPG'
                    this.view1_mode_list.push(data)
                }
            })
            axios.post("http://127.0.0.1:38088/device=getresolution", JSON.stringify(view1_mode_data1)).then((res) => {
                if (res.data.data.split("|").length > 1) {
                    let data = new Object()
                    data.key = '0'
                    data.value = 'YUY2'
                    this.view1_mode_list.push(data)
                }
            })

            // 加载主摄像头分辨率
            let view1_resolution_power_data = { "camidx": "0", "mode": this.view1_mode_selected }
            axios.post("http://127.0.0.1:38088/device=getresolution", JSON.stringify(view1_resolution_power_data)).then((res) => {
                let resolution_list = res.data.data.split("|");
                this.view1_resolution_selected = resolution_list[0]
                for (var i = 0; i < resolution_list.length; i++) {
                    this.view1_resolution_list.push(resolution_list[i])
                }
            })
        },
        // 添加缩略图
        add_image(img_base64) {
            // 添加缩略图
            // this.$set(this.attachmentList, this.attachmentList.length, 'data:image/png;base64,' + img_base64)
            // this.attachmentList.push('data:image/png;base64,' + img_base64)
            this.attachmentList.push(img_base64)
            console.log(this.attachmentList)

        },
        open_view1() {
            // 打开主摄像头视频
            this.view1_src = "http://127.0.0.1:38088/video=stream&camidx=0?1"
        },
        rotate(degree) {
            // 旋转摄像头视频
            let data = { "camidx": '0', "rotate": String(degree) }
            axios.post("http://127.0.0.1:38088/video=rotate", JSON.stringify(data)).then((res) => {
            })
        },
        // 主头拍照
        view1_scan() {
            let data = {
                "filepath": "base64",
                "rotate": "0",
                "cutpage": "0",
                "camidx": "0",
                "ColorMode": "0",
                "quality": "3"
            }
            axios.post("http://127.0.0.1:38088/video=grabimage", JSON.stringify(data)).then((res) => {
                this.uploadBase64ToServer(res.data.photoBase64)
            })
        },
        // 展平拍照
        flat_scan() {
            let data = {
                "filepath": "",
                "rotate": "0",
                "camidx": "0",
                "cutpage": "0",
                "autoflat": {
                    "flat": "1",
                    "leftfilepath": "D://left.jpg",
                    "rightfilepath": "D://right.jpg",
                    "removefinger": "1",
                    "doublepage": "1"
                }
            }
            axios.post("http://127.0.0.1:38088/video=autoflat", JSON.stringify(data)).then((res) => {
                this.uploadBase64ToServer(res.data.photoBase64)
                this.uploadBase64ToServer(res.data.leftphotoBase64)
                this.uploadBase64ToServer(res.data.rightphotoBase64)
            })
        },
        handleScan() {
            this.form.archiveFile = [...this.attachmentList]
            this.scannerOpen = false
            this.$message.success('拍照成功，存档已添加！')
        },
        uploadBase64ToServer(base64String) {
            // 将base64转换为Blob
            const byteCharacters = atob(base64String); // 去掉data:image/jpeg;base64,这部分
            const byteNumbers = new Array(byteCharacters.length);
            for (let i = 0; i < byteCharacters.length; i++) {
                byteNumbers[i] = byteCharacters.charCodeAt(i);
            }
            const byteArray = new Uint8Array(byteNumbers);
            const blob = new Blob([byteArray], { type: 'image/jpeg' }); // 根据实际情况修改MIME类型

            // 创建FormData并添加Blob
            const formData = new FormData();
            formData.append('file', blob, 'filename.jpg'); // 'filename.jpg'可以根据需要修改文件名和后缀

            // 步骤2: 发送请求到后端
            this.sendFormDataToServer(formData);
        },
        sendFormDataToServer(formData) {
            axios.post(this.imgPath + "/common/upload", formData, {
                headers: {
                    'Content-Type': 'multipart/form-data'
                }
            })
                .then(response => {
                    console.log('上传成功', response);
                    this.add_image(response.data.fileName)
                })
                .catch(error => {
                    console.error('上传失败', error);
                });
        },
        handleDelImg(index) {
            this.attachmentList.splice(index, 1)
        },
        selectCustomer() {
            this.form.customerName = this.customerList.find(item => item.id === this.form.customerId).name
            console.log(this.form.customerId, this.form.customerName)
        },
        handleDelete(row) {
            const id = row.id
            this.$confirm('此操作将永久删除该用章记录, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                signetDel({ id }).then(res => {
                    if (res.code === 200) {
                        this.$message({
                            type: 'success',
                            message: '删除成功!'
                        });
                        this.handleQuery()
                    } else {
                        this.$message.error(res.msg)
                    }
                })

            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消删除'
                });
            });
        },
        // 查看公司详情
        handleCompanyView(row) {
            let name = row.customerName
            this.$refs.create.handleSearch(1, name)
        },
    }
}
</script>

<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';

.tableBox {
    padding: 20px;
}

.custom-search {
    align-items: center;
    justify-content: space-between;
}

.p20 {
    padding: 0 20px;
}

/* 视频预览 */
#view1 {
    width: 400px;
    height: 300px;
    border: 1px solid red;
    margin-right: 5px;
}

/* 缩略图 */
.suoluetu {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    align-items: center;
    margin-top: 10px;
}

/* 操作按钮 */
.myactive {
    margin-top: 10px;
}
</style>
