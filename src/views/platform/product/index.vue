<template>
  <div class="newBox bgcf9 vh-85">
    <div class="classify flex">
      <div class="classify-item" :class="{ active: activeName === 'list' }" @click="handleClick('list')">全部列表</div>
      <div class="classify-item" :class="{ active: activeName === 'supplier' }" @click="handleClick('supplier')">搜索供应商报价</div>
    </div>
    <component :is="activeName === 'list' ? 'AllList' : 'SupplierList'"></component>
  </div>
</template>
<script>
import AllList from './allList'
import SupplierList from './supplierList'

export default {
  name: 'Products',
  components: {
    AllList,
    SupplierList
  },
  data() {
    return {
      activeName: 'list'
    }
  },
  methods: {
    handleClick(name) {
      this.activeName = name
    }
  }
}
</script>
<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
</style>
