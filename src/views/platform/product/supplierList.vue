<template>
  <div class="newBox bgcf9 vh-85">
    <div class="custom-search">
      <el-input v-model="supplierName" placeholder="请输入供应商名称" @keyup.enter.native="getList" size="small" style="width: 300px">
        <template slot="suffix">
          <el-button icon="el-icon-search" type="primary" @click="getList" size="small">搜索</el-button>
        </template>
      </el-input>
    </div>
    <div class="tableBox">
      <el-table v-loading="loading" ref="table" stripe :data="list" row-key="id" style="width: 100%" class="custom-table custom-table-cell5" v-if="total > 0">
        <el-table-column align="center" type="index" label="序号"></el-table-column>
        <el-table-column align="center" prop="productName" label="产品名称" show-overflow-tooltip min-width="130">
          <template slot-scope="{ row }">
            <span class="table-link" @click="handleView(row)">{{ row.productName }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="picture1" label="图片" width="75">
          <template slot-scope="{ row }">
            <el-image :src="formatProductImg(row)" fit="cover" @click="handleImgView(row)">
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline"></i>
              </div>
            </el-image>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="specs" label="规格" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" prop="model" label="型号" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" prop="productCode" label="产品编码" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" label="产品报价">
          <template slot-scope="{ row }">
            <span class="table-price pointer" v-if="!!row.price" @click="handleOffer(row)">{{ '￥' + row.price + '元' }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="materialQuality" label="材质" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" prop="surface" label="表面" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" prop="unit" label="单位" width="60"></el-table-column>
      </el-table>
      <el-empty v-if="!loading && total === 0" :description="supplierName ? '暂无数据' : '请输入供应商名称搜索'" />
    </div>
    <!-- 产品信息 -->
    <product-dialog ref="productInfo" append-body />
    <!-- 报价信息 -->
    <offer-dialog ref="offerInfo" append-body />
  </div>
</template>
<script>
import { supplierList, getProductsQuote } from '@/api/system/company'
import { quotesel } from '@/api/houtai/formula'
import ProductDialog from '@/views/public/product/dialog'
import OfferDialog from '@/views/public/product/offer'

export default {
  name: 'SupplierList',
  components: { ProductDialog, OfferDialog },
  data() {
    return {
      supplierName: '',
      loading: false,
      total: 0,
      list: []
    }
  },
  methods: {
    // prettier-ignore
    async getList() {
      const loading = this.$loading({
        lock: true,
        text: '加载中',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      this.loading = true
      const supplierListData = await supplierList({ keyword: this.supplierName })
      const companyIds = supplierListData.rows.map(item => item?.company?.id)
      const productList = await Promise.all(
        companyIds.map(async id => {
          const productRes = await getProductsQuote({ companyId: id, pageSize: 30 })
          const productQuoteList = productRes.rows || []
          const productQuoteInfo = await Promise.all(
            productQuoteList.map(async ite => {
              const productQuoteInfo = await quotesel({ quoteId: ite.id })
              return productQuoteInfo?.data
            })
          )
          return productQuoteInfo
        })
      )
      // 将二维数组扁平化
      const flatProductList = productList.flat()      
      // 根据product.id去重
      const uniqueProductList = flatProductList.filter((item, index, self) => {
        return index === self.findIndex(p => p.product?.id === item.product?.id)
      }).map(item => {
        return {
          ...item.product,
          price: item.totalPrice
        }
      })
      this.list = uniqueProductList
      this.total = uniqueProductList.length
      this.loading = false
      loading.close()
    },
    // 查看详情
    handleView(item) {
      this.$refs.productInfo.handleView(item)
    },
    handleImgView(item) {
      this.$refs.productInfo.handleImgView(item)
    },
    // 查看报价
    handleOffer(item) {
      item.offerNum = 1
      this.$refs.offerInfo.handleView(item)
    }
  }
}
</script>
<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
.custom-search {
  ::v-deep .el-input__suffix {
    right: 2px;
    display: flex;
    align-items: center;
    .el-input__suffix-inner {
      display: flex;
      align-items: center;
      .el-button {
        padding: 7px 15px;
      }
    }
  }
}
.tableBox {
  padding: 20px;
}
</style>
