<template>
  <div class="newBox bgcf9 vh-85">
    <!-- 搜索 -->
    <div class="custom-search flex">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" @submit.native.prevent>
        <el-row :gutter="10">
          <el-col :xs="8" :sm="6" :lg="4">
            <el-form-item label="名称" prop="productName">
              <el-input v-model="queryParams.productName" placeholder="请输入产品名称" clearable @keyup.enter.native="handleQuery" size="small" />
            </el-form-item>
          </el-col>
          <el-col :xs="8" :sm="6" :lg="4">
            <el-form-item label="编码" prop="productCode">
              <el-input v-model="queryParams.productCode" placeholder="请输入产品编码" clearable @keyup.enter.native="handleQuery" size="small" />
            </el-form-item>
          </el-col>
          <el-col :xs="8" :sm="6" :lg="4">
            <el-form-item label="规格" prop="specs">
              <el-input v-model="queryParams.specs" placeholder="请输入产品规格" clearable @keyup.enter.native="handleQuery" size="small" />
            </el-form-item>
          </el-col>
          <el-col :xs="8" :sm="6" :lg="4">
            <el-form-item label="型号" prop="model">
              <el-input v-model="queryParams.model" placeholder="请输入产品型号" clearable @keyup.enter.native="handleQuery" size="small" />
            </el-form-item>
          </el-col>
          <el-col :xs="8" :sm="6" :lg="4">
            <el-form-item label="材质" prop="materialQuality">
              <el-input v-model="queryParams.materialQuality" placeholder="请输入产品材质" clearable @keyup.enter.native="handleQuery" size="small" />
            </el-form-item>
          </el-col>
          <el-col :xs="8" :sm="6" :lg="4" v-if="showAll || checkPermi(['system:product:add'])">
            <el-form-item label="表面" prop="surface">
              <el-input v-model="queryParams.surface" placeholder="请输入产品表面处理" clearable @keyup.enter.native="handleQuery" size="small" />
            </el-form-item>
          </el-col>
          <el-col :xs="8" :sm="6" :lg="4" v-if="showAll">
            <el-form-item label="状态" prop="status">
              <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
                <el-option label="上架" value="1" />
                <el-option label="下架" value="-1" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="8" :sm="6" :lg="4" v-if="showAll">
            <el-form-item label="是否甄选" prop="isOptimal">
              <el-select v-model="queryParams.isOptimal" placeholder="请选择是否甄选" clearable>
                <el-option label="是" :value="true" />
                <el-option label="否" :value="false" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="1.5">
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
            </el-form-item>
            <el-form-item>
              <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="el-icon-plus" size="small" @click="handleAdd" v-hasPermi="['system:product:add']">新增</el-button>
            </el-form-item>
            <el-form-item>
              <el-button type="text" size="mini" @click="clickSearch">
                {{ word }}
                <i :class="showAll ? 'el-icon-arrow-up ' : 'el-icon-arrow-down'"></i>
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <!-- 分类 -->
    <div class="classify flex">
      <div class="classify-item" :class="{ active: item.id === queryParams.categoryId }" v-for="item in categoryList" :key="item.id" @click="handleCategory(item)">
        {{ item.name }}
      </div>
      <div class="classify-toolbar">
        <right-toolbar :search="false" @queryTable="getList" :columns="columns" v-if="showMore"></right-toolbar>
      </div>
    </div>

    <!-- 表格数据 -->
    <div class="tableBox">
      <el-table v-loading="loading" ref="table" stripe :data="list" row-key="id" :key="key" style="width: 100%" class="custom-table custom-table-cell5" v-if="total > 0">
        <el-table-column align="center" type="index" label="序号" v-if="columns[0].visible"></el-table-column>
        <el-table-column align="center" prop="productName" label="产品名称" show-overflow-tooltip v-if="columns[1].visible" min-width="130">
          <template slot-scope="{ row }">
            <span class="table-link" @click="handleView(row)">{{ row.productName }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="picture1" label="图片" width="75" v-if="columns[2].visible">
          <template slot-scope="{ row }">
            <el-image :src="formatProductImg(row)" fit="cover" lazy @click="handleImg(row)">
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline"></i>
              </div>
            </el-image>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="specs" label="规格" show-overflow-tooltip v-if="columns[3].visible"></el-table-column>
        <el-table-column align="center" prop="model" label="型号" show-overflow-tooltip v-if="columns[4].visible"></el-table-column>
        <el-table-column align="center" prop="productCode" label="产品编码" show-overflow-tooltip v-if="columns[5].visible" width="130"></el-table-column>
        <el-table-column align="center" prop="materialQuality" label="材质" show-overflow-tooltip v-if="columns[6].visible"></el-table-column>
        <el-table-column align="center" prop="surface" label="表面处理" show-overflow-tooltip v-if="columns[7].visible"></el-table-column>
        <el-table-column align="center" prop="unit" label="单位" width="50" v-if="columns[8].visible"></el-table-column>
        <el-table-column align="center" prop="industry" label="行业分类" show-overflow-tooltip v-if="columns[9].visible"></el-table-column>
        <el-table-column align="center" prop="weight" label="重量" width="80" v-if="columns[10].visible">
          <template slot-scope="{ row }">{{ row.weight ? parseFloat(row.weight) + 'Kg' : '' }}</template>
        </el-table-column>
        <el-table-column align="center" prop="createTime" label="创建时间" show-overflow-tooltip v-if="columns[11].visible"></el-table-column>
        <el-table-column align="center" prop="status" label="是否热门产品" v-if="columns[12].visible && checkPermi(['system:product:hot'])">
          <template slot-scope="{ row }">
            <template>
              <el-switch v-model="row.isHot" active-color="#13ce66" @change="handleHot(row, $event)"></el-switch>
            </template>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="status" label="状态" width="130" v-if="columns[13].visible && checkPermi(['system:product:online']) && showMore">
          <template slot-scope="{ row }">
            <template>
              <el-switch v-model="row.status" active-text="上架" inactive-text="下架" :active-value="1" :inactive-value="-1" @change="handlePutaway(row, $event)" class="table-switch"></el-switch>
            </template>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="isOptimal" label="是否国标甄选" v-if="columns[14].visible && checkPermi(['system:product:edit'])">
          <template slot-scope="{ row }">
            <template>
              <el-switch v-model="row.isOptimal" active-color="#13ce66" @change="handleOptimal(row, $event)"></el-switch>
            </template>
          </template>
        </el-table-column>
        <el-table-column align="center" label="标签" show-overflow-tooltip v-if="showMore">
          <template slot-scope="{ row }">
            <span class="table-link pointer" @click="handleLabel(row)" v-if="checkPermi(['system:product:label'])">{{ row.labels.toString() || '点击添加标签' }}</span>
            <span v-else>{{ row.labels.toString() }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作" :width="showMore ? getActionColumnWidth() : '100px'">
          <template slot-scope="{ row }">
            <!-- 常用按钮 -->
            <template v-for="action in getCommonActions(row)">
              <el-button :key="action.key" :class="action.className" :disabled="action.disabled" @click="action.handler(row)" v-if="action.show">{{ action.label }}</el-button>
            </template>
            <!-- 更多操作 -->
            <el-popover trigger="hover" v-if="hasMoreActions(row)">
              <button type="button" class="table-btn primary" slot="reference">
                更多操作
                <i class="el-icon-arrow-down el-icon--right"></i>
              </button>
              <div class="popover-button">
                <template v-for="action in getAllActions(row)">
                  <div :key="action.key" class="popover-button-item" v-if="action.show">
                    <el-button :class="action.className" :disabled="action.disabled" @click="action.handler(row)">{{ action.label }}</el-button>
                    <i class="popover-button-icon" :class="[isCommonAction(action.key) ? 'el-icon-star-on' : 'el-icon-star-off', { active: isCommonAction(action.key) }]" @click="toggleCommonAction(action.key)" :title="isCommonAction(action.key) ? '取消常用' : '设置为常用'"></i>
                  </div>
                </template>
              </div>
            </el-popover>
          </template>
        </el-table-column>
      </el-table>
      <el-empty v-if="!loading && total === 0" />

      <div class="custom-pagination">
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
      </div>
    </div>

    <!-- 产品详情 -->
    <product-dialog ref="productInfo"></product-dialog>

    <!-- 产品新增 -->
    <add-tpl ref="add" @shuaxin="getList"></add-tpl>

    <!-- 产品修改 -->
    <update-tpl ref="update" @shuaxin="getList"></update-tpl>

    <!-- 引入至私域 -->
    <collectDialog ref="collect"></collectDialog>

    <!-- 标签管理 -->
    <el-drawer :title="labelTitle" :visible.sync="labelOpen" size="50%" custom-class="custom-drawer">
      <div class="lable-item">
        <div class="item-title">预设标签</div>
        <div class="item-box flex">
          <div class="item-select" v-for="item in labelList.system" :key="'s' + item.id">
            <span class="select-title">{{ item.content }}</span>
            <el-select :ref="'select' + item.id" v-model="item.checked" multiple collapse-tags @visible-change="v => visibleChange(v, item.id, 'select' + item.id)">
              <el-option v-for="item in item.child" :key="'c' + item.id" :label="item.content" :value="item.id"></el-option>
              <el-option value="" disabled v-if="isSelectAdd">
                <el-input v-model="labelContent" placeholder="请输入标签内容" size="small" style="width: 130px" @change="handleSubLabel('system')" @blur="hiddenSelect"></el-input>
              </el-option>
            </el-select>
          </div>
        </div>
      </div>
      <div class="lable-item">
        <div class="item-title">通用标签</div>
        <div class="item-box flex ptb20">
          <div class="item-tag" :class="{ active: labelChecked.includes(item.content) }" v-for="item in labelList.custom" :key="'t' + item.id">
            <span @click="handleCheckLabel(item)">{{ item.content }}</span>
            <i class="el-icon-close item-close" @click="handleCloseLabel(item)"></i>
          </div>
          <div class="item-input" v-if="isLabelAdd">
            <el-input ref="addLabel" v-model="labelContent" placeholder="请在此输入标签内容" size="small" @change="handleSubLabel()" @blur="hiddenInput"></el-input>
          </div>
          <div class="item-add" v-if="!isLabelAdd">
            <button type="button" @click="handleAddLabel()">
              <i class="el-icon-plus"></i>
              新增标签
            </button>
          </div>
        </div>
      </div>
      <div class="label-btn" @click="handleLabelSubmit">确定</div>
    </el-drawer>

    <!--  添加编码产品  -->
    <product-create ref="create" @callback="getList" />
    <!--  新增采购  -->
    <el-dialog v-dialogDragBox title="新增采购" :visible.sync="buyOpen" width="1150px" append-to-body class="custom-dialog">
      <div class="quote-product">
        <div class="quote-product-img">
          <img :src="formatProductImg(buyForm)" :alt="buyForm.productName" />
        </div>
        <div class="quote-product-info">
          <div class="quote-product-title">{{ buyForm.productName }}</div>
          <div class="quote-product-desc">
            <div class="item">
              <span>规格</span>
              {{ buyForm.specs }}
            </div>
            <div class="item">
              <span>产品编码</span>
              {{ buyForm.productCode }}
            </div>
            <div class="item">
              <span>材质</span>
              {{ buyForm.materialQuality }}
            </div>
            <div class="item">
              <span>表面处理</span>
              {{ buyForm.surface }}
            </div>
            <div class="item">
              <span>单位</span>
              {{ buyForm.unit }}
            </div>
            <div class="item">
              <span>属性</span>
              {{ buyForm.attribute }}
            </div>
            <div class="item">
              <span>重量</span>
              {{ buyForm.weight }}
            </div>
          </div>
        </div>
      </div>
      <div class="quote-form">
        <template v-if="!isAuthentication">
          <div class="quote-form-brand" style="font-size: 14px">
            还不是认证采购商，无法添加采购?去
            <el-button type="text" @click="handleUp">升级</el-button>
          </div>
        </template>
        <template v-else>
          <template v-if="brandList.length">
            <el-form ref="buyForm" :model="buyForm" :rules="buyRules" label-width="80px">
              <el-row :gutter="30">
                <el-col :span="8">
                  <el-form-item label="品牌名称" prop="brandId">
                    <el-select v-model="buyForm.brandId" placeholder="请选择品牌名称" style="width: 100%">
                      <el-option v-for="item in brandList" :key="item.id" :label="item.name" :value="item.id"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="采购数量" prop="quantity">
                    <el-input v-model="buyForm.quantity" placeholder="请输入采购数量" style="width: 70%">
                      <span slot="suffix" class="inline-flex">{{ buyForm.unit }}</span>
                    </el-input>
                    <el-select style="width: 30%" v-model="buyForm.unit" filterable allow-create>
                      <el-option v-for="item in unitOptions" :key="item" :label="item" :value="item"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="采购周期" prop="timeUnit">
                    <el-select v-model="buyForm.timeUnit" placeholder="请选择采购周期" style="width: 100%">
                      <el-option v-for="item in timeUnitOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </template>
          <template v-else>
            <div class="quote-form-brand" style="font-size: 14px">
              还没有添加品牌?去
              <el-button type="text" @click="handleAddBrand">添加</el-button>
            </div>
          </template>
        </template>
      </div>
      <div slot="footer">
        <button type="button" class="custom-dialog-btn" @click="buyOpen = false">取消</button>
        <button type="button" class="custom-dialog-btn primary" @click="handleBuySubmit">确定</button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import * as category from '@/api/purchase/category'
import { changeProductHot, isPublicToPrivate, changeProductOptimal } from '@/api/system/product'
import { dellist, editlist, getlist, shangxia } from '@/api/houtai/gongyu/chanpin'
import ProductDialog from '@/views/public/product/dialog'
import AddTpl from '@/views/houtai/gongyu/chanpin/add'
import UpdateTpl from '@/views/houtai/gongyu/chanpin/edit'
import { checkPermi } from '@/utils/permission'
import collectDialog from '@/views/platform/product/collect'
import productCreate from '@/views/components/product'
import { supplier, userBrandAdd, userBrandList } from '@/api/system/user'
import { isNumber, isNumberLength } from '@/utils/validate'
import { addBuy } from '@/api/buy'

export default {
  name: 'AllList',
  props: {
    showMore: {
      type: Boolean,
      default: true
    }
  },
  components: { ProductDialog, AddTpl, UpdateTpl, collectDialog, productCreate },
  data() {
    return {
      key: 1,
      showAll: false,
      word: '展开搜索',
      // 搜索条件
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        categoryId: undefined,
        productName: undefined,
        productCode: undefined,
        specs: undefined,
        model: undefined,
        materialQuality: undefined,
        surface: undefined,
        unit: undefined,
        status: undefined,
        isOptimal: undefined
      },
      // 加载
      loading: true,
      // 列表数据
      list: [],
      // 总条数
      total: 0,
      // 分类数据
      categoryList: [],
      // 显隐列
      columns: [
        { key: 0, label: `序号`, visible: true },
        { key: 1, label: `产品名称`, visible: true },
        { key: 2, label: `图片`, visible: true },
        { key: 3, label: `规格`, visible: true },
        { key: 4, label: `型号`, visible: true },
        { key: 5, label: `产品编码`, visible: true },
        { key: 6, label: `材质`, visible: true },
        { key: 7, label: `表面处理`, visible: true },
        { key: 8, label: `单位`, visible: true },
        { key: 9, label: `行业分类`, visible: true },
        { key: 10, label: `重量`, visible: true },
        { key: 11, label: `创建时间`, visible: true },
        { key: 12, label: `是否热门产品`, visible: true },
        { key: 13, label: `状态`, visible: true },
        { key: 14, label: `是否国标甄选`, visible: true }
      ],
      // 标签管理
      labelTitle: undefined,
      labelOpen: false,
      labelItem: {},
      labelChecked: [],
      labelContent: undefined,
      isLabelAdd: false,
      isSelectAdd: false,
      parentId: undefined,
      // 标签列表
      labelList: { custom: [], system: [] },
      collectType: undefined,
      collectTitle: undefined,
      collectOpen: false,
      // 新增采购
      buyOpen: false,
      buyForm: {},
      buyRules: {
        brandId: [{ required: true, message: '请选择品牌名称', trigger: 'change' }],
        quantity: [
          { required: true, message: '请输入采购数量', trigger: 'blur' },
          { validator: isNumber, message: '输入有误请重新输入', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: 'blur' }
        ],
        timeUnit: [{ required: true, message: '请选择采购周期', trigger: 'change' }]
      },
      unitOptions: ['吨', '千克', '个', '件', '套', '米', '支', '根', '袋', '托'],
      timeUnitOptions: [
        { label: '天', value: 'day' },
        { label: '周', value: 'week' },
        { label: '月', value: 'month' },
        { label: '季', value: 'quarter' },
        { label: '年', value: 'year' }
      ],
      brandList: [],
      isAuthentication: false,
      // 常用按钮配置
      commonActions: []
    }
  },
  computed: {
    roles() {
      return this.$store.getters.roles
    },
    // 获取当前用户ID
    userId() {
      return this.$store.getters.info.userId
    }
  },
  created() {
    this.getCategory()
    this.getBrandList()

    // 加载常用按钮配置
    const commonActions = localStorage.getItem(this.userId + '.productAllListCommonActions')
    if (commonActions) this.commonActions = JSON.parse(commonActions)
  },
  methods: {
    checkPermi,
    // 去升级
    handleUp() {
      this.$router.push('/index')
    },
    // 查询品牌
    getBrandList() {
      userBrandList().then(res => {
        const { code, data, msg } = res
        if (code === 200) this.brandList = data
        else this.$message.error(msg)
      })
    },
    // 添加品牌
    // prettier-ignore
    handleAddBrand() {
      const companyId = localStorage.getItem('companyId')
      const that = this
      this.$prompt('请输入品牌名称', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false,
        inputPattern: /^.+$/,
        inputErrorMessage: '请输入品牌名称'
      }).then(({ value }) => {
        const data = { name: value, companyId }
        userBrandAdd(data).then(function (res) {
          if (res.code === 200) {
            that.getBrandList()
          } else {
            that.$message.error(res.msg)
          }
        })
      }).catch(() => {
      })
    },
    // 查询分类
    getCategory() {
      category.getlist().then(res => {
        if (res.code === 200) {
          const data = [...[{ id: -1, name: '全部' }], ...res.data]
          this.categoryList = data
          this.queryParams.categoryId = data[0].id
          this.getList()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 查询数据
    async getList() {
      const loading = this.$loading({
        lock: true,
        text: '加载中',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      this.loading = true
      const query = { ...this.queryParams }
      if (query.categoryId === -1) query.categoryId = undefined
      const res = await getlist(query)
      if (res.code === 200) {
        await Promise.all(
          res.rows.map(async item => {
            const { data } = await isPublicToPrivate({ productId: item.id })
            item.isCollect = data
          })
        )
        this.list = res.rows
        this.loading = false
        this.total = res.total
        this.key = Math.random()
        loading.close()
      } else {
        this.$message.error(res.msg)
        loading.close()
      }
    },
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 重置搜索
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 展开收起搜索
    clickSearch() {
      this.showAll = !this.showAll
      this.word = this.showAll ? '收起搜索' : '展开搜索'
    },
    // 切换分类
    handleCategory(item) {
      this.queryParams.categoryId = item.id
      this.handleQuery()
    },
    // 新增产品
    handleAdd() {
      this.$refs.create.handleAdd()
    },
    // 修改产品
    handleUpdate(row, type = '') {
      this.$refs.create.handleUpdate(row, type)
    },
    // 删除产品
    // prettier-ignore
    handleDelete(row) {
      this.$modal.confirm('是否删除此产品?').then(function () {
        return dellist({ ids: row.id })
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess('删除成功')
      }).catch(() => {
      })
    },
    // 查看详情
    handleView(item, val) {
      this.$refs.productInfo.handleView(item, val)
    },
    // 图片预览
    handleImg(row) {
      this.$refs.productInfo.handleImgView(row)
    },
    // 上下架
    // prettier-ignore
    handlePutaway(row, status) {
      const that = this
      const data = { ids: [row.id], status }
      shangxia(data).then(res => {
        if (res.code === 200) {
          that.$message.success('操作成功')
        } else {
          that.$message.error(res.msg)
          row.status = status === -1 ? 1 : -1
        }
      })
    },
    // 是否热门
    // prettier-ignore
    handleHot(row, hot) {
      const that = this
      const data = { ids: [row.id], hot }
      changeProductHot(data).then(res => {
        if (res.code === 200) {
          that.$message.success('操作成功')
        } else {
          that.$message.error(res.msg)
          row.isHot = !hot
        }
      })
    },
    // 是否国标甄选
    handleOptimal(row, e) {
      const that = this
      const data = { ids: [row.id], hot: e }
      changeProductOptimal(data).then(res => {
        if (res.code === 200) {
          that.$message.success('操作成功')
        } else {
          that.$message.error(res.msg)
          row.isOptimal = !e
        }
      })
    },
    // 标签管理
    handleLabel(row) {
      this.labelTitle = row.productName + '-标签'
      this.labelChecked = [...row.labels]
      this.labelItem = { ...row }
      this.getLabelList()
      this.isLabelAdd = false
      this.isSelectAdd = false
      this.labelOpen = true
    },
    // 查询标签
    getLabelList() {
      category.labelList().then(res => {
        if (res.code === 200) {
          if (res.data.system) {
            res.data.system.map(item => {
              const checked = [...this.labelChecked]
              const arr = item.child.filter(ite => checked.indexOf(ite.content) > -1)
              item.checked = arr.map(it => it.id)
            })
          }
          this.labelList = res.data
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 选择通用标签
    handleCheckLabel(item) {
      if (!this.labelChecked.includes(item.content)) this.labelChecked.push(item.content)
    },
    // 删除通用标签
    handleCloseLabel(item) {
      const index = this.labelChecked.indexOf(item.content)
      if (index !== -1) this.labelChecked.splice(index, 1)
    },
    // 新增通用标签
    handleAddLabel() {
      this.isLabelAdd = !this.isLabelAdd
      this.labelContent = undefined
      this.$nextTick(() => {
        if (this.$refs.addLabel) this.$refs.addLabel.focus()
      })
    },
    // 提交新增标签
    handleSubLabel(type) {
      const labelType = type || 'custom'
      if (labelType === 'custom') {
        if (this.labelContent) {
          const data = { parentId: 0, labelType: type, content: this.labelContent }
          category.labelAdd(data).then(res => {
            if (res.code === 200) {
              this.isLabelAdd = !this.isLabelAdd
              this.labelContent = undefined
              this.getLabelList()
            } else {
              this.$message.error(res.msg)
            }
          })
        } else {
          this.isLabelAdd = false
          this.labelContent = undefined
        }
      } else {
        if (this.labelContent) {
          const data = { parentId: this.parentId, labelType: type, content: this.labelContent }
          category.labelAdd(data).then(res => {
            if (res.code === 200) {
              this.isSelectAdd = !this.isSelectAdd
              this.labelContent = undefined
              this.getLabelList()
            } else {
              this.$message.error(res.msg)
            }
          })
        } else {
          this.isSelectAdd = false
          this.labelContent = undefined
        }
      }
    },
    // 隐藏通用标签输入框
    hiddenInput() {
      this.isLabelAdd = false
      this.labelContent = undefined
    },
    // 隐藏下拉标签输入框
    hiddenSelect() {
      this.isSelectAdd = false
      this.labelContent = undefined
    },
    visibleChange(visible, Id, refName) {
      this.parentId = Id
      this.isSelectAdd = false
      this.labelContent = undefined
      if (visible) {
        const ref = this.$refs[refName][0]
        let popper = ref.$refs.popper
        if (popper.$el) popper = popper.$el
        if (!Array.from(popper.children).some(v => v.className === '')) {
          const el = document.createElement('ul')
          el.style = 'margin:0;border-top:1px solid rgb(219 225 241); padding:0; color:rgb(64 158 255);font-size: 13px'
          el.innerHTML = `<li class="el-cascader-node text-center" style="line-height: 35px;padding:0">
            <span class="el-cascader-node__label"><i class="font-blue el-icon-plus"></i> 新增标签</span>
            </li>`
          popper.appendChild(el)
          el.onclick = () => {
            this.isSelectAdd = !this.isSelectAdd
            this.labelContent = undefined
          }
        }
      }
    },
    handleLabelSubmit() {
      let result = []
      this.labelList.system.map(item => {
        const arr = item.child.filter(ite => item.checked.indexOf(ite.id) > -1)
        const title = arr.map(it => it.content)
        result = [...result, ...title]
      })
      let custom = []
      this.labelList.custom.map(item => {
        if (this.labelChecked.includes(item.content)) custom.push(item.content)
      })
      const res = [...result, ...custom]
      this.labelItem.labels = [...new Set(res)]
      editlist(this.labelItem).then(res => {
        if (res.code === 200) {
          this.labelChecked = []
          this.labelItem = {}
          this.labelOpen = false
          this.getList()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 产品收藏
    handleImport(row, type) {
      this.$refs.collect.handleImport(row, type)
    },
    // 新增采购
    async handleBuy(item) {
      const companyId = this.$store.state.user.companyId
      if (companyId === -1) {
        this.$message.error('个人无法进行采购操作，请升级为企业')
        return
      } else {
        const companyInfo = await supplier({ id: companyId })
        if (companyInfo.code === 200) {
          if (companyInfo.data.company.level == 0) {
            this.isAuthentication = false
          } else {
            this.isAuthentication = true
            const brandId = this.brandList.length ? this.brandList[0].id : undefined
            const buyForm = {
              brandId,
              productId: undefined,
              productName: undefined,
              quantity: undefined,
              timeUnit: undefined,
              unit: '吨'
            }
            this.buyForm = { ...buyForm, productId: item.id, ...item }
            this.resetForm('buyForm')
          }
        }
      }
      this.buyOpen = true
    },
    // 提交新增采购
    handleBuySubmit() {
      this.$refs.buyForm.validate(valid => {
        if (valid) {
          const { brandId, productId, productName, quantity, timeUnit, unit } = this.buyForm
          const data = { brandId, productId, productName, quantity, timeUnit, unit }
          addBuy(data).then(res => {
            if (res.code === 200) {
              this.buyOpen = false
              this.$message.success('新增采购成功')
              this.$emit('close')
              this.$emit('refresh')
            } else {
              this.$message.error(res.msg)
            }
          })
        }
      })
    },
    // 获取所有操作按钮配置
    getAllActions(row) {
      if (this.showMore) {
        return [
          {
            key: 'view',
            label: '查看详情',
            className: 'table-btn',
            show: true, // 查看详情无权限限制
            handler: this.handleView
          },
          {
            key: 'edit',
            label: '修改',
            className: 'table-btn',
            show: this.checkPermi(['system:product:edit']),
            handler: this.handleUpdate
          },
          {
            key: 'editFile',
            label: '修改附件',
            className: 'table-btn',
            show: this.checkPermi(['system:product:edit']),
            handler: row => this.handleUpdate(row, 'file')
          },
          {
            key: 'import',
            label: row.isCollect ? '已引至私域' : '引入至私域',
            className: 'table-btn primary',
            disabled: row.isCollect,
            show: this.checkPermi(['system:privateduct:introduce']),
            handler: row => this.handleImport(row, 1)
          },
          {
            key: 'collect',
            label: '添加收藏',
            className: 'table-btn primary',
            show: true, // 添加收藏无权限限制
            handler: row => this.handleImport(row, 0)
          },
          {
            key: 'delete',
            label: '删除',
            className: 'table-btn danger',
            show: this.checkPermi(['system:product:remove']),
            handler: this.handleDelete
          }
        ]
      } else {
        return [
          {
            key: 'buy',
            label: '添加采购',
            className: 'table-btn danger',
            show: true, // 添加采购无权限限制
            handler: this.handleBuy
          }
        ]
      }
    },
    // 获取常用按钮
    getCommonActions(row) {
      const allActions = this.getAllActions(row).filter(action => action.show)
      const commonActions = allActions.filter(action => this.isCommonAction(action.key))

      // 如果没有设置常用按钮，默认显示查看详情（如果有权限），否则显示第一个有权限的按钮
      if (commonActions.length === 0) {
        const viewAction = allActions.find(action => action.key === 'view')
        if (viewAction) {
          return [viewAction]
        }
        // 如果没有查看详情权限，返回第一个有权限的按钮
        return allActions.length > 0 ? [allActions[0]] : []
      }

      // 常用按钮不限制数量
      return commonActions
    },
    // 判断是否为常用按钮
    isCommonAction(actionKey) {
      return this.commonActions.includes(actionKey)
    },
    // 判断是否有更多操作按钮
    hasMoreActions(row) {
      const allActions = this.getAllActions(row).filter(action => action.show)
      const commonActions = this.getCommonActions(row)
      // 如果显示的按钮数量大于常用按钮数量，则显示更多操作
      return allActions.length >= commonActions.length
    },
    // 切换常用按钮设置
    toggleCommonAction(actionKey) {
      const index = this.commonActions.indexOf(actionKey)
      if (index > -1) {
        this.commonActions.splice(index, 1)
      } else {
        // 允许设置多个常用按钮
        this.commonActions.push(actionKey)
      }
      localStorage.setItem(this.userId + '.productAllListCommonActions', JSON.stringify(this.commonActions))
    },
    // 计算操作列宽度
    getActionColumnWidth() {
      // 如果列表为空，返回默认宽度
      if (!this.list || this.list.length === 0) {
        return 220
      }
      // 取第一行数据来计算宽度
      const firstRow = this.list[0]
      const commonActions = this.getCommonActions(firstRow)
      const hasMoreActions = this.hasMoreActions(firstRow)
      // 常用按钮数量 + 更多操作按钮(如果有的话) * 110px
      const buttonCount = commonActions.length + (hasMoreActions ? 1 : 0)
      const width = buttonCount * 110
      // 设置最小宽度为 110px，最大宽度为 550px
      return Math.max(110, Math.min(550, width))
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
.tableBox {
  padding: 20px;
}
.custom-search {
  padding-top: 17px;
  ::v-deep {
    .el-form-item--small {
      display: inline-flex;
      .el-form-item__label {
        flex-shrink: 0;
      }
      .el-form-item {
        margin-bottom: 10px;
      }
    }
  }
}
::v-deep .custom-drawer {
  border-top-left-radius: 5px;
  border-bottom-left-radius: 5px;
  overflow: unset;
  .el-drawer__header {
    margin-bottom: 10px;
    height: 50px;
    line-height: 50px;
    font-size: 16px;
    font-weight: 500;
    color: #333333;
    border-bottom: 1px solid #f2f2f8;
    padding: 0 30px;
    position: relative;
    .el-drawer__close-btn {
      position: absolute;
      top: 60px;
      left: -40px;
      width: 40px;
      height: 40px;
      background-color: $white;
      color: #999999;
      border-top-left-radius: 5px;
      border-bottom-left-radius: 5px;
    }
  }
  .el-drawer__body {
    padding: 0 30px;
    .lable-item {
      margin-bottom: 15px;
      .item-title {
        font-size: 14px;
        font-weight: 500;
        color: #333333;
        line-height: 35px;
      }
      .item-box {
        padding: 0 20px 20px;
        border: 1px solid #cbd6e2;
        border-radius: 5px;
        flex-wrap: wrap;
        .item-select {
          display: flex;
          flex-direction: column;
          padding-top: 5px;
          margin-right: 20px;
          max-width: calc(25% - 20px);
          .select-title {
            font-size: 12px;
            color: #999999;
            line-height: 3em;
          }
        }
        &.ptb20 {
          padding: 20px 20px 5px;
        }
        .item-tag {
          background-color: #e5e5e5;
          border-radius: 5px;
          line-height: 30px;
          position: relative;
          margin-right: 15px;
          margin-bottom: 15px;
          font-size: 12px;
          color: #333333;
          padding: 0 13px;
          cursor: pointer;
          .item-close {
            display: none;
          }
          &.active {
            background-color: #e0ebff;
            color: #2e73f3;
            .item-close {
              display: inline-block;
              width: 16px;
              height: 16px;
              line-height: 16px;
              text-align: center;
              color: $white;
              background-color: #525356;
              border-radius: 50%;
              position: absolute;
              top: -8px;
              right: -8px;
            }
          }
        }
        .item-input {
          width: 150px;
          height: 30px;
          margin-right: 15px;
          margin-bottom: 15px;
        }
        .item-add {
          margin-right: 15px;
          margin-bottom: 15px;
          button {
            border: 1px dashed #2e73f3;
            background-color: #dfebff;
            font-size: 14px;
            color: #2e73f3;
            height: 30px;
            line-height: 30px;
            padding: 0 16px;
            border-radius: 5px;
            cursor: pointer;
          }
        }
      }
    }
    .label-btn {
      width: 269px;
      height: 50px;
      line-height: 50px;
      text-align: center;
      border-radius: 5px;
      font-size: 16px;
      cursor: pointer;
      background-color: #2e73f3;
      color: $white;
    }
  }
}
::v-deep .option-btn {
  font-size: 14px;
  cursor: pointer;
  background-color: #2e73f3;
  color: $white;
  border-radius: 50%;
  padding: 2px 3px;
  &:hover {
    opacity: 0.8;
  }
}
</style>
