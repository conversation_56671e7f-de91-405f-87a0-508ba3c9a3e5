<template>
  <div>
    <el-dialog v-dialogDragBox :title="title" :visible.sync="open" width="1150px" class="custom-dialog">
      <div class="collect-title">请选择您要将该产品添加至</div>
      <div class="collect-list">
        <div class="collect-item" :class="{ active: item.storeId === storeId }" v-for="item in collectList" :key="item.storeId" @click="storeId = item.storeId">{{ item.dirName }}</div>
        <input ref="collectName" type="text" v-model="collectName" placeholder="请输入文件夹名称" class="collect-input" v-if="isAddCollect" @change="handleInputChange" @blur="hiddenCollect" />
        <div class="collect-btn" v-if="!isAddCollect">
          <button type="button" @click="handleAddCollect">
            <i class="el-icon-plus"></i>
            新建文件夹
          </button>
        </div>
      </div>
      <span slot="footer">
        <button type="button" class="custom-dialog-btn" @click="handleImportCancel">取消</button>
        <button type="button" class="custom-dialog-btn primary" :class="{ disabled: !storeId }" @click="handleImportSubmit" :disabled="!storeId" v-if="collectType === 'UserPriProduct'">确定</button>
        <button type="button" class="custom-dialog-btn primary" :class="{ disabled: !storeId }" @click="handleSubmit" :disabled="!storeId" v-if="collectType === 'UserProduct'">确定</button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import * as collect from '@/api/houtai/shoucang'
import { publicToPrivate } from '@/api/system/product'
export default {
  data() {
    return {
      title: undefined,
      open: false,
      collectType: undefined,
      collectList: [],
      storeId: undefined,
      productId: undefined,
      isAddCollect: false,
      collectName: undefined
    }
  },
  methods: {
    // 引入至私域
    handleImport(row, type) {
      this.collectType = type ? 'UserPriProduct' : 'UserProduct'
      this.title = type ? '引入至私域' : '添加到产品收藏'
      this.productId = row.id
      this.collectName = undefined
      this.isAddCollect = false
      this.getCollectList()
      this.open = true
    },
    // 查询私域收藏夹
    getCollectList() {
      const query = { type: this.collectType }
      collect.getlist(query).then(res => {
        if (res.code === 200) {
          this.collectList = res.data
          if (res.data.length) this.storeId = res.data[0].storeId
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 取消引入私域
    handleImportCancel() {
      this.productId = undefined
      this.storeId = undefined
      this.collectName = undefined
      this.isAddCollect = false
      this.open = false
    },
    // 确定引入私域
    handleImportSubmit() {
      const data = { productId: this.productId, storeId: this.storeId }
      publicToPrivate(data).then(res => {
        if (res.code === 200) {
          this.$message.success('引入成功')
          this.$parent.getList()
          this.open = false
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    handleSubmit() {
      const data = { valueIdList: [this.productId], storeId: this.storeId }
      collect.shoucTo(data).then(res => {
        if (res.code === 200) {
          this.$message.success('收藏成功')
          this.$parent.getList()
          this.open = false
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 新建文件夹
    handleAddCollect() {
      this.collectName = undefined
      this.isAddCollect = true
      this.$nextTick(() => {
        if (this.$refs.collectName) this.$refs.collectName.focus()
      })
    },
    // 确定新增文件夹
    handleInputChange() {
      if (this.collectName) {
        const data = { dirName: this.collectName, type: this.collectType }
        collect.addlist(data).then(res => {
          if (res.code === 200) {
            this.getCollectList()
            this.collectName = undefined
            this.isAddCollect = false
          } else {
            this.$message.error(res.msg)
          }
        })
      } else {
        this.collectName = undefined
        this.isAddCollect = false
      }
    },
    // 隐藏新增收藏夹
    hiddenCollect() {
      this.collectName = undefined
      this.isAddCollect = false
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
.custom-dialog {
  ::v-deep .el-dialog {
    .el-dialog__body {
      .collect {
        &-title {
          font-size: 14px;
          color: $info;
          line-height: 2em;
          text-align: center;
          padding-bottom: 8px;
          position: relative;
          &:after {
            content: '';
            width: 0;
            border-style: solid;
            border-width: 0 8px 8px 8px;
            border-color: transparent transparent #f0f3f9 transparent;
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            bottom: 0;
          }
        }
        &-list {
          padding: 20px 10px;
          background-color: #f0f3f9;
          display: flex;
          flex-wrap: wrap;
        }
        &-item {
          margin: 10px;
          min-width: 160px;
          height: 46px;
          line-height: 46px;
          border: 1px solid #b2bac3;
          background-color: transparent;
          border-radius: 5px;
          text-align: center;
          padding: 0 10px;
          cursor: pointer;
          position: relative;
          &:hover,
          &.active {
            background-image: url('~@/assets/images/subtract.png');
            background-position: right bottom;
            background-repeat: no-repeat;
            background-color: #dbe8ff;
            border-color: $blue;
            color: $blue;
          }
        }
        &-input {
          border: 1px solid #2e73f3;
          width: 160px;
          height: 46px;
          line-height: 46px;
          padding: 0 20px;
          background-color: $white;
          outline: none;
          border-radius: 5px;
          color: #2e73f3;
          margin: 10px;
          &::-webkit-input-placeholder {
            color: #2e73f3;
          }
        }
        &-btn {
          margin: 10px;
          button {
            width: 150px;
            height: 46px;
            line-height: 46px;
            border: 1px dashed #2e73f3;
            font-size: 14px;
            text-align: center;
            color: #2e73f3;
            background-color: #dfebff;
            cursor: pointer;
            border-radius: 5px;
          }
        }
      }
    }
  }
}
</style>
