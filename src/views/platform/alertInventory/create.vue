<template>
  <div>
    <el-dialog :title="title" :visible.sync="open" width="1150px" :before-close="beforeClose" class="custom-dialog">
      <el-form ref="form" :model="form" :rules="rules" label-width="80px" class="custom-form" label-position="left">
        <div style="padding: 0 20px">
          <el-row :gutter="10">
            <el-col :span="12">
              <el-form-item label="产品" prop="productName">
                <el-input v-model="form.productName" placeholder="请输入产品名称" @click.native="handleSearchProduct" :disabled="!!inventoryAlertId">
                  <i slot="suffix" class="el-icon-search pointer" @click="handleSearchProduct"></i>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <div class="stock-item" :class="{ first: index === 0 }" v-for="(item, index) in form.stocks" :key="index">
          <el-row :gutter="10" class="stock-item-row">
            <el-col :span="8">
              <el-form-item :prop="`stocks.${index}.stockOrg`" :rules="rules.stockOrg" label="组织">
                <el-select v-model="item.stockOrg" placeholder="请选择组织" style="width: 100%">
                  <el-option v-for="item in ApplicationOrgId" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :prop="`stocks.${index}.minQty`" :rules="rules.minQty" label="最小库存">
                <el-input v-model="item.minQty" placeholder="请输入最小库存" style="width: 100%"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :prop="`stocks.${index}.maxQty`" :rules="rules.maxQty" label="最大库存">
                <el-input v-model="item.maxQty" placeholder="请输入最大库存" style="width: 100%"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <div class="stock-item-btn">
            <el-button type="danger" icon="el-icon-delete" size="mini" :disabled="form.stocks.length === 1" @click="handleDeleteStock(index)">删除</el-button>
          </div>
        </div>
        <div class="stock-plus">
          <el-button type="primary" icon="el-icon-plus" size="small" @click="handleAddStock">添加组织警戒库存</el-button>
        </div>
      </el-form>
      <div slot="footer">
        <el-button class="custom-dialog-btn" @click="handleClose()">取 消</el-button>
        <el-button class="custom-dialog-btn primary" :loading="loading" @click="handleSubmit">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 选择产品 -->
    <search-product ref="searchProduct" @confirm="handleSearchProductConfirm" v-if="searchProductOpen" />
  </div>
</template>
<script>
import { kingdee } from '@/minix'
import { isNumber, isNumberLength } from '@/utils/validate'
import searchProduct from './searchProduct'
import { inventoryAlertCreate, inventoryAlertStock, inventoryAlertStockRefresh } from '@/api/kingdee/inventory'

export default {
  name: 'AlertInventoryCreate',
  components: { searchProduct },
  mixins: [kingdee],
  data() {
    return {
      open: false,
      title: '添加产品',
      loading: false,
      form: {},
      rules: {
        productName: [{ required: true, message: '请选择产品', trigger: ['blur', 'change'] }],
        stockOrg: [{ required: true, message: '请选择组织', trigger: ['blur', 'change'] }],
        maxQty: [
          { required: true, message: '请输入最大库存', trigger: 'blur' },
          { validator: isNumber, message: '输入有误请重新输入', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (value && this.form.stocks[rule.field.split('.')[1]].minQty) {
                if (Number(value) <= Number(this.form.stocks[rule.field.split('.')[1]].minQty)) {
                  callback(new Error('最大库存必须大于最小库存'))
                }
              }
              callback()
            },
            trigger: 'blur'
          }
        ],
        minQty: [
          { required: true, message: '请输入最小库存', trigger: 'blur' },
          { validator: isNumber, message: '输入有误请重新输入', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: 'blur' }
        ]
      },
      searchProductOpen: false,
      inventoryAlertId: undefined
    }
  },
  methods: {
    reset() {
      this.form = {
        productId: undefined,
        productName: undefined,
        stocks: [{ stockOrg: '', maxQty: '', minQty: '' }]
      }
      this.resetForm('form')
      this.inventoryAlertId = undefined
    },
    // 添加产品
    create() {
      this.reset()
      this.title = '添加产品'
      this.open = true
    },
    // 修改产品
    update(row) {
      this.reset()
      let stockList = []
      if (row.stockList && row.stockList.length) {
        stockList = row.stockList.map(item => {
          return {
            stockOrg: item.stockOrg,
            maxQty: item.maxQty,
            minQty: item.minQty
          }
        })
      } else stockList = [{ stockOrg: '', maxQty: '', minQty: '' }]
      this.form = {
        productId: row.product && row.product.id,
        productName: row.product && row.product.productName,
        stocks: stockList
      }
      this.inventoryAlertId = row.id
      this.title = '修改产品'
      if (!this.form.productId) return this.$message.warning('参数错误，请重试')
      this.open = true
    },
    // 添加库存
    handleAddStock() {
      this.form.stocks.push({ stockOrg: '', maxQty: '', minQty: '' })
    },
    // 删除库存
    handleDeleteStock(index) {
      this.form.stocks.splice(index, 1)
    },
    // 关闭
    beforeClose() {
      this.handleClose()
    },
    // 关闭
    handleClose(flag = false) {
      this.open = false
      this.$emit('callBack', flag)
    },
    // 提交
    // prettier-ignore
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true
          if(this.inventoryAlertId) {
            this.handleUpdate()
          } else {
            const data = { productId: this.form.productId, stocks: this.form.stocks }
            inventoryAlertCreate(data).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('添加成功')
                this.handleClose(true)
              } else this.$message.error(msg)
            }).finally(() => {
              this.loading = false
            })
          }
        }
      })
    },
    // 更新数据
    // prettier-ignore
    handleUpdate() {
      const stockOrgList = this.form.stocks.map(item => item.stockOrg)
      const data = { inventoryAlertId: this.inventoryAlertId, stockOrgList }
      inventoryAlertStockRefresh(data).then(res => {
        const { code, msg, data: resData } = res
        if (code === 200) {
          // 检查返回的 stockOrg 是否与当前选择的一致
          const returnedStockOrgs = resData.map(item => item.StockOrg)
          const isConsistent = this.checkStockOrgConsistency(stockOrgList, returnedStockOrgs)

          if (isConsistent) this.performUpdate()
          else {
            // 不一致则弹出提示
            const missingOrgs = stockOrgList.filter(org => !returnedStockOrgs.includes(org))
            const orgNames = missingOrgs.map(orgValue => {
              const orgItem = this.ApplicationOrgId.find(item => item.value === orgValue)
              return orgItem ? orgItem.label : orgValue
            }).join(', ')
            this.$confirm(`组织 ${orgNames} 无库存数据，是否继续添加？`, '提示', {
              confirmButtonText: '继续',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(() => {
              this.performUpdate()
            }).catch(() => {
              this.loading = false
            })
          }
        } else {
          this.$message.error(msg)
          this.loading = false
        }
      }).catch(error => {
        this.$message.error('更新失败')
        this.loading = false
      })
    },

    // 检查 stockOrg 一致性
    checkStockOrgConsistency(stockOrgList, returnedStockOrgs) {
      if (stockOrgList.length !== returnedStockOrgs.length) {
        return false
      }
      return stockOrgList.every(org => returnedStockOrgs.includes(org))
    },
    // 执行实际的更新操作
    // prettier-ignore
    performUpdate() {
      const updateData = { inventoryAlertId: this.inventoryAlertId, stocks: this.form.stocks }
      inventoryAlertStock(updateData).then(res => {
        const { code, msg } = res
        if (code === 200) {
          this.$message.success('更新成功')
          this.handleClose(true)
        } else {
          this.$message.error(msg)
        }
      }).finally(() => {
        this.loading = false
      })
    },
    // 选择产品
    handleSearchProduct() {
      this.searchProductOpen = true
      this.$nextTick(() => {
        this.$refs.searchProduct.openProduct()
      })
    },
    // 选择产品
    handleSearchProductConfirm(data) {
      this.form.productId = data.id
      this.form.productName = data.productName
      this.searchProductOpen = false
    }
  }
}
</script>
<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
.stock-item {
  padding: 0 20px;
  background-color: #f0f3f9;
  display: flex;
  &.first {
    padding-top: 20px;
  }
  &-row {
    flex: 1;
    overflow: hidden;
  }
  &-btn {
    flex-shrink: 0;
    margin-left: 20px;
    display: flex;
    align-items: center;
    padding-bottom: 20px;
  }
}
.stock-plus {
  padding: 20px;
  border-bottom: 1px solid #e2e6f3;
}
</style>
