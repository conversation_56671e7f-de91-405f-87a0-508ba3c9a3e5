<template>
  <div>
    <el-dialog title="查看库存详情" :visible.sync="open" width="1150px" class="custom-dialog" :before-close="beforeClose">
      <div style="padding: 0 20px">
        <div class="info-title">
          <span>共</span>
          <span class="number">{{ orgNum }}</span>
          <span>个组织库存</span>
          <span class="product">产品总库存</span>
          <span class="number">{{ total }}</span>
        </div>
        <div class="info-table">
          <div class="info-table-product">{{ product.productName }}</div>
          <el-table :data="info" stripe class="info-table-content">
            <el-table-column align="center" type="index" label="序号" width="60"></el-table-column>
            <el-table-column align="center" prop="materialName" label="物料名称" show-overflow-tooltip>
              <template slot-scope="scope">{{ scope.row.materialName || '-' }}</template>
            </el-table-column>
            <el-table-column align="center" prop="stockOrg" label="组织" show-overflow-tooltip>
              <template slot-scope="scope">{{ formatOrg(scope.row.stockOrg) }}</template>
            </el-table-column>
            <el-table-column align="center" prop="stockName" label="仓库" show-overflow-tooltip>
              <template slot-scope="scope">{{ scope.row.stockName || '-' }}</template>
            </el-table-column>
            <el-table-column align="center" prop="qty" label="库存" show-overflow-tooltip>
              <template slot-scope="scope">{{ scope.row.qty || '-' }}</template>
            </el-table-column>
            <el-table-column align="center" prop="minQty" label="最小库存" show-overflow-tooltip>
              <template slot-scope="scope">
                <span>{{ parseFloat(scope.row.minQty.toFixed(5)) || '-' }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="maxQty" label="最大库存" show-overflow-tooltip>
              <template slot-scope="scope">
                <span>{{ parseFloat(scope.row.maxQty.toFixed(5)) || '-' }}</span>
              </template>
            </el-table-column>
            <!-- 删除 -->
            <el-table-column align="center" label="操作" width="100">
              <template slot-scope="scope">
                <el-button type="danger" size="mini" icon="el-icon-delete" @click="handleDelete(scope.row, scope.$index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { kingdee } from '@/minix'
import { inventoryAlertStockDelete } from '@/api/kingdee/inventory'

export default {
  name: 'AlertInventoryDetail',
  mixins: [kingdee],
  data() {
    return {
      open: false,
      info: [],
      orgNum: 0,
      total: 0,
      product: {},
      inventoryAlertId: ''
    }
  },
  methods: {
    // 格式化采购组织
    formatOrg(value) {
      const obj = this.ApplicationOrgId.find(item => item.value == value)
      return obj?.label || ''
    },
    // 打开
    handleOpen(info = {}) {
      const { stockList = [], product, id: inventoryAlertId } = info
      if (stockList.length === 0) return this.$message.warning('暂无库存信息')
      this.info = stockList
      let total = 0
      stockList.forEach(item => {
        total += item.qty || 0
      })
      this.total = parseFloat(total.toFixed(5))
      const org = new Set(stockList.map(item => item.stockOrg))
      this.orgNum = org.size
      this.product = product
      this.inventoryAlertId = inventoryAlertId
      this.open = true
    },
    // 删除
    // prettier-ignore
    handleDelete(row,index) {
      this.$confirm('确定删除该库存组织吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        inventoryAlertStockDelete({ inventoryAlertId: this.inventoryAlertId, stockOrgList: [row.stockOrg] }).then(res => {
          if (res.code === 200) {
            this.$message.success('删除成功')
            this.info.splice(index, 1)
            if (!this.info.length) {
              this.open = false
              this.$emit('callBack', true)
            }
          } else this.$message.error(res.msg || '删除失败')
        })
      }).catch(() => {})
    },
    // 关闭
    beforeClose() {
      this.open = false
      this.$emit('callBack')
    }
  }
}
</script>
<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
.info-title {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  span {
    font-size: 12px;
    color: #999999;
    &.number {
      font-weight: 500;
      font-size: 16px;
      color: #2e73f3;
      margin: 0 15px;
    }
    &.product {
      margin-left: 50px;
    }
  }
}
.info-table {
  border: 1px solid #d1dffa;
  border-radius: 5px;
  overflow: hidden;
  &-product {
    background-color: #ecf3ff;
    padding: 10px 20px;
    font-weight: 500;
    font-size: 14px;
    color: #333333;
    line-height: 20px;
    border-bottom: 1px solid #d1dffa;
    &::after {
      content: '>';
      display: inline-block;
      margin-left: 5px;
    }
  }
  &-content {
    width: 100%;
    border: 0 !important;
    &::before {
      display: none !important;
    }
  }
}
</style>
