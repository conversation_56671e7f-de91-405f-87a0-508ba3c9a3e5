<template>
  <div>
    <el-dialog v-dialogDragBox title="产品标记" :visible.sync="labelOpen" width="1150px" class="custom-dialog" append-to-body>
      <div style="padding: 0 20px">
        <el-table :data="list" class="custom-table custom-table-cell5" :row-class-name="getRowClass" @expand-change="expandChange" v-loading="loadings">
          <el-table-column align="left" type="expand">
            <template slot-scope="scope">
              <recursive-table v-if="scope.row.children && scope.row.children.length" :items="scope.row.children" :bomId="scope.row.bomId" />
            </template>
          </el-table-column>
          <el-table-column align="center" prop="productName" label="产品名称" show-overflow-tooltip>
            <template slot-scope="{ row }">
              <span class="table-link" @click="handleProductInfo(row)">{{ (row.product && row.product.productName) || row.productName }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="productName" label="图片">
            <template slot-scope="scope">
              <image-preview :src="formatProductImg(scope.row.product || scope.row)" :width="50" :height="50" />
            </template>
          </el-table-column>
          <el-table-column align="center" prop="productName" label="规格" show-overflow-tooltip>
            <template slot-scope="scope">
              <span>{{ (scope.row.product && scope.row.product.specs) || scope.row.specs }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="productName" label="型号" show-overflow-tooltip>
            <template slot-scope="scope">
              <span>{{ (scope.row.product && scope.row.product.model) || scope.row.model }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="productName" label="产品编码" show-overflow-tooltip>
            <template slot-scope="scope">
              <span>{{ (scope.row.product && scope.row.product.productCode) || scope.row.productCode }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="productName" label="材质" show-overflow-tooltip>
            <template slot-scope="scope">
              <span>{{ (scope.row.product && scope.row.product.materialQuality) || scope.row.materialQuality }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="productName" label="单位" show-overflow-tooltip>
            <template slot-scope="scope">
              <span>{{ (scope.row.product && scope.row.product.unit) || scope.row.unit }}</span>
            </template>
          </el-table-column>
          <!-- <el-table-column align="center" prop="productName" label="当前库存(总量)" show-overflow-tooltip>
            <template slot-scope="scope">
              <span>{{ formatInventory(scope.row.stockList) || scope.row.stock || 0 }}</span>
            </template>
          </el-table-column> -->
          <el-table-column align="center" label="金蝶库存" prop="jdStock"></el-table-column>
          <el-table-column align="center" label="自由客库存" prop="stock"></el-table-column>
          <el-table-column align="center" label="生产数量" width="150">
            <template slot-scope="{ row }">
              <el-input v-model="row.productQuantity" style="width: 100%" @change="handleQuantityChange(row)"></el-input>
            </template>
          </el-table-column>
          <el-table-column align="center" label="操作">
            <template slot-scope="{ row }">
              <el-dropdown @command="handleCommand($event, row)">
                <span class="el-dropdown-link" :class="row.doType !== 'cg' ? 'dropdown-orange' : 'dropdown-primary'">
                  {{ formatDo(row.doType) }}
                  <i class="el-icon-caret-bottom"></i>
                </span>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item v-for="item in doOptions" :key="item.value" :command="item.value">{{ item.label }}</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn" @click="labelOpen = false">取消</el-button>
        <el-button class="custom-dialog-btn primary" @click="labelOpen = false" v-if="!hasProduction()">关闭</el-button>
        <el-button class="custom-dialog-btn primary" @click="handleCreateProduction" v-if="hasProduction()">确定</el-button>
      </div>
    </el-dialog>
    <!-- 拆分提示 -->
    <el-dialog v-dialogDragBox title="提示" :visible.sync="errOpen" width="748px" class="custom-dialog" :modal-append-to-body="false">
      <div class="errTips">
        <img class="img" src="@/assets/images/errTips.png" alt="" />
        <div class="title">当前产品尚未设置BOM与工艺不可标记为生产</div>
        <div class="text">如要更改请前往产品BOM为该产品增加BOM设置，完成后可以标记为生产</div>
        <div class="btn">
          <div class="cancel" @click="errOpen = false">取消</div>
          <div class="go" @click="handleSetBom">立即设置BOM</div>
        </div>
      </div>
    </el-dialog>
    <!-- 交货日期 -->
    <el-dialog v-dialogDragBox title="交货日期" :visible.sync="deliveryTimeOpen" width="650px" class="custom-dialog" :modal-append-to-body="false">
      <div class="deliveryTimeBox">
        <el-form ref="form" :model="form" :rules="rules" label-width="6em" class="custom-form">
          <el-form-item label="交货日期" prop="deliveryTime">
            <el-date-picker v-model="form.deliveryTime" type="date" placeholder="选择交货日期" style="width: 100%"></el-date-picker>
          </el-form-item>
        </el-form>
        <div class="btn">
          <div class="cancel" @click="deliveryTimeOpen = false">取消</div>
          <div class="go" @click="submitForm">提交</div>
        </div>
      </div>
    </el-dialog>
    <bom-create ref="bomCreate" @callBack="bomCreate = false" v-if="bomCreate" />
  </div>
</template>
<script>
import { getBomDetailByCode, getKingdeeStock, getBomDetail, markProductBom } from '@/api/bom'
import { addProduction } from '@/api/production'
import RecursiveTable from '@/views/unsalable/sell/RecursiveTable'
import { inventoryQty, inventoryList } from '@/api/inventory'
import BomCreate from '@/views/bom/create'

export default {
  name: 'AlertInventoryLabel',
  components: { RecursiveTable, BomCreate },
  data() {
    return {
      labelOpen: false,
      list: [],
      doOptions: [
        { label: '采购产品', value: 'cg' },
        { label: '生产产品', value: 'sc' },
        { label: '委外产品', value: 'ww' }
      ],
      orgIdsOptions: [
        { label: '世盛总部', value: 100071 },
        { label: '世盛生产部', value: 100072 },
        { label: '世盛销售部', value: 100073 },
        { label: '市场部', value: 100074 },
        { label: '徽标厂', value: 100075 },
        { label: '镀锌厂', value: 100076 },
        { label: '模具厂', value: 100077 },
        { label: '冲压厂', value: 100078 }
      ],
      loadings: false,
      // 拆分提示
      errOpen: false,
      // 交货日期
      deliveryTimeOpen: false,
      form: {
        deliveryTime: ''
      },
      rules: {
        deliveryTime: [{ type: 'date', required: true, message: '请选择交货日期', trigger: 'change' }]
      },
      // 新增生产任务单使用的objId
      objId: undefined,
      quantity: 0,
      bomCreate: false,
      bomProduct: {}
    }
  },
  methods: {
    // 打开产品标记弹窗
    async handleOpen(row) {
      this.objId = row.id
      if (!this.objId) {
        this.$message.error('缺少库存预警记录ID，请刷新页面重试')
        return
      }
      let quantity = 0
      if (row.stockList && row.stockList.length) {
        quantity = row.stockList.reduce((sum, item) => {
          const maxQty = parseFloat(item.maxQty) || 0
          const qty = parseFloat(item.qty) || 0
          return sum + (maxQty - qty)
        }, 0)
      }
      this.quantity = quantity
      const product = row?.product || row || {}
      const productCode = product.productCode
      if (!productCode) return this.$message.warning('参数错误，请刷新页面重试')
      let arr = []
      this.loadings = true
      await inventoryList({ number: product.materialCode, pageNum: 1, pageSize: 1 }).then(stock => {
        if (stock.rows[0] && stock.rows[0].stocks) {
          product.jdStock = stock.rows[0].stocks ? stock.rows[0].stocks.reduce((total, item) => total + item.qty, 0) : 0
        } else product.jdStock = 0
        product.stock = stock.rows[0] ? product.jdStock - stock.rows[0].useQty : 0
      })
      await getBomDetailByCode({ productCode }).then(async child => {
        if (child.code === 200 && child.data) {
          const markBox = await getBomDetail({ bomId: child.data.id })
          if (markBox.code === 200 && markBox.data) {
            const doType = markBox.data.mark || this.doOptions[0].value
            const hasChildren = doType !== this.doOptions[0].value
            const defaultQuantity = parseFloat(((Number(1) || 1) * this.quantity).toFixed(5))
            arr.push({ ...product, children: [], productId: child.data.productId, hasChildren, doType, productQuantity: defaultQuantity })
          } else {
            const hasChildren = false
            const defaultQuantity = parseFloat(((Number(1) || 1) * this.quantity).toFixed(5))
            arr.push({ ...product, children: [], productId: child.data.productId, hasChildren, doType: this.doOptions[0].value, productQuantity: defaultQuantity })
          }
        } else {
          const hasChildren = false
          const defaultQuantity = parseFloat(((Number(1) || 1) * this.quantity).toFixed(5))
          arr.push({ ...product, children: [], hasChildren, doType: this.doOptions[0].value, productQuantity: defaultQuantity })
        }
      })

      this.list = arr
      this.labelOpen = true
      this.loadings = false
    },
    // 回显操作类型
    formatDo(type) {
      const res = this.doOptions.find(item => item.value === type)
      return res ? res.label : '采购产品'
    },
    // 格式化回显库存和仓库
    formatInventory(stockList = [], type = undefined) {
      if (!Array.isArray(stockList) || !stockList.length) return type === 'name' ? '' : 0
      if (type === 'name') {
        const stockNameList = stockList.map(item => item.stockName || '')
        return stockNameList.filter(name => name).join(',')
      } else {
        // 计算总库存
        const total = stockList.reduce((sum, item) => {
          const qty = parseFloat(item.qty) || 0
          return sum + qty
        }, 0)
        return parseFloat(total.toFixed(5))
      }
    },
    // 切换操作
    handleCommand(e, row) {
      row.doType = e
      if (row.doType === 'sc' || row.doType === 'ww') {
        getBomDetailByCode({ productCode: row.productCode }).then(async child => {
          if (child.code === 200) {
            if (child.data && child.data.child && child.data.child.length) {
              const result = child.data.child.map(itt => {
                const defaultQuantity = parseFloat(((Number(itt.quantity) || 1) * this.quantity).toFixed(5))
                return { ...itt, ...itt.product, orgIds: this.orgIdsOptions.map(ittt => ittt.value), doType: this.doOptions[0].value, source: 'private', productQuantity: defaultQuantity }
              })
              await Promise.all(
                result.map(async ite => {
                  if (ite.materialCode) {
                    // const stock = await getKingdeeStock({ orgIds: this.orgIdsOptions.map(ittt => ittt.value), materialCode: ite.materialCode })
                    // ite.stock = stock.data
                    const stock = await inventoryList({ pageSize: 1, pageNum: 1, number: ite.materialCode })
                    if (stock.rows[0] && stock.rows[0].stocks) {
                      ite.jdStock = stock.rows[0].stocks ? stock.rows[0].stocks.reduce((total, item) => total + item.qty, 0) : 0
                    } else ite.jdStock = 0
                    ite.stock = stock.rows[0] ? ite.jdStock - stock.rows[0].useQty : 0
                  }
                })
              )
              row.bomId = child.data.id
              row.hasChildren = !!child.data.child.length
              this.$set(row, 'children', result)
              this.markProduct(row)
            } else {
              row.doType = 'cg'
              row.hasChildren = false
              this.$set(row, 'children', [])
              this.bomProduct = row
              this.errOpen = true
              this.markProduct(row)
            }
          }
        })
      } else {
        row.hasChildren = false
        this.$set(row, 'children', [])
        this.markProduct(row)
      }
    },
    // 展开行
    expandChange(row) {
      if (row.doType === 'sc' || row.doType === 'ww') {
        this.loadings = true
        getBomDetailByCode({ productCode: row.productCode }).then(async child => {
          if (child.code === 200) {
            if (child.data && child.data.child && child.data.child.length) {
              const result = child.data.child.map(itt => {
                const defaultQuantity = parseFloat(((Number(itt.quantity) || 1) * this.quantity).toFixed(5))
                return { ...itt, ...itt.product, orgIds: this.orgIdsOptions.map(ittt => ittt.value), originAmount: Number(row.originAmount) * Number(itt.quantity), doType: this.doOptions[0].value, source: 'private', productQuantity: defaultQuantity }
              })
              await Promise.all(
                result.map(async ite => {
                  if (ite.materialCode) {
                    const stock = await inventoryList({ pageSize: 1, pageNum: 1, number: ite.materialCode })
                    if (stock.rows[0] && stock.rows[0].stocks) {
                      ite.jdStock = stock.rows[0].stocks ? stock.rows[0].stocks.reduce((total, item) => total + item.qty, 0) : 0
                    } else ite.jdStock = 0
                    ite.stock = stock.rows[0] ? ite.jdStock - stock.rows[0].useQty : 0
                  }
                  const bomBox = await getBomDetailByCode({ productCode: ite.productCode })
                  if (bomBox.code === 200 && bomBox.data) {
                    const markBox = await getBomDetail({ bomId: bomBox.data.id })
                    if (markBox.code === 200 && markBox.data) {
                      ite.doType = markBox.data.mark || this.doOptions[0].value
                      ite.hasChildren = ite.doType !== this.doOptions[0].value
                    } else {
                      ite.doType = this.doOptions[0].value
                      ite.hasChildren = false
                    }
                  } else {
                    ite.doType = this.doOptions[0].value
                    ite.hasChildren = false
                  }
                })
              )
              row.bomId = child.data.id
              this.$set(row, 'children', result)
              this.loadings = false
            }
          }
        })
      }
    },
    // 产品标记记录
    async markProduct(row) {
      await markProductBom([{ productId: row.productId, mark: row.doType }])
    },
    // 产品详情
    handleProductInfo(row) {
      this.$parent.handleProductInfo(row)
    },
    // 表格行样式 当当前行没有子物料时，添加样式，使其展开按钮不可点击
    getRowClass(row, rowIndex) {
      if (!row.row.hasChildren) return 'row-expand-cover'
      else return ''
    },
    // 判断是否有选中为生产产品
    hasProduction() {
      let num = 0
      this.list.map(item => {
        const child = item.children.filter(itt => itt.doType == 'sc' || itt.doType == 'ww')
        if (child.length) num += 1
        if (item.doType == 'sc' || item.doType == 'ww') num += 1
      })
      return !!num
    },
    // 创建生产任务单
    handleCreateProduction() {
      this.form = {
        deliveryTime: ''
      }
      this.resetForm('form')
      this.deliveryTimeOpen = true
    },
    // 提交交货日期
    async submitForm() {
      this.$refs['form'].validate(async valid => {
        if (valid) {
          // 验证objId是否存在
          if (!this.objId) {
            this.$message.error('缺少必要的对象ID，请重新打开弹窗')
            return
          }
          // 在提交前确保所有需要展开的数据都已加载
          this.loadings = true
          try {
            await this.ensureAllDataLoaded(this.list)
          } catch (error) {
            console.error('加载数据失败:', error)
            this.$message.error('加载数据失败，请重试')
            this.loadings = false
            return
          }
          this.loadings = false
          let arr
          arr = this.handleArr(this.list)
          arr.forEach(el => {
            el.deliveryTime = this.parseTime(this.form.deliveryTime, '{y}-{m}-{d} 23:59:59')
          })
          Promise.all(
            arr.map(async (item, index) => {
              try {
                if (item.mark == 'cg' && item.quantity < 0) item.quantity = 0
                await addProduction(item)
                return { success: true }
              } catch (error) {
                return {
                  success: false,
                  message: error.message || '未知错误',
                  productName: item.productName,
                  index: index + 1
                }
              }
            })
          ).then(results => {
            const errors = results.filter(result => !result.success)
            if (errors.length > 0) {
              const errorMessages = errors.map(err => `第${err.index}个产品 ${err.productName || ''}: ${err.message}`).join('<br/>')
              this.$alert(errorMessages, '提交失败', {
                confirmButtonText: '确定',
                type: 'error',
                dangerouslyUseHTMLString: true
              })
            } else {
              this.$message.success('创建生产任务单成功')
              this.labelOpen = false
              this.deliveryTimeOpen = false
              this.$emit('callBack', true)
            }
          })
        } else {
          return false
        }
      })
    },
    // 确保所有需要展开的数据都已加载
    async ensureAllDataLoaded(data) {
      const processItems = async items => {
        const loadPromises = []
        for (let item of items) {
          // 如果是生产或委外产品，且还没有加载子数据
          if ((item.doType === 'sc' || item.doType === 'ww') && (!item.children || item.children.length === 0)) {
            // 创建加载子数据的Promise
            const loadPromise = this.loadChildrenData(item)
            loadPromises.push(loadPromise)
          }
        }
        // 等待当前层级的所有数据加载完成
        if (loadPromises.length > 0) {
          await Promise.all(loadPromises)
        }
        // 递归处理子项（在当前层级加载完成后）
        for (let item of items) {
          if (item.children && item.children.length > 0) {
            await processItems(item.children)
          }
        }
      }
      await processItems(data)
    },
    // 加载子数据的方法（从expandChange方法中提取的逻辑）
    async loadChildrenData(row) {
      if (row.doType === 'sc' || row.doType === 'ww') {
        try {
          const child = await getBomDetailByCode({ productCode: row.productCode })
          if (child.code === 200) {
            if (child.data && child.data.child && child.data.child.length) {
              const result = child.data.child.map(itt => {
                const defaultQuantity = parseFloat(((Number(itt.quantity) || 1) * this.quantity).toFixed(5))
                return {
                  ...itt,
                  ...itt.product,
                  orgIds: this.orgIdsOptions.map(ittt => ittt.value),
                  originAmount: Number(row.originAmount) * Number(itt.quantity),
                  doType: this.doOptions[0].value,
                  source: 'private',
                  productQuantity: defaultQuantity
                }
              })
              await Promise.all(
                result.map(async ite => {
                  if (ite.materialCode) {
                    // const stock = await getKingdeeStock({ orgIds: this.orgIdsOptions.map(ittt => ittt.value), materialCode: ite.materialCode })
                    // ite.stock = stock.data
                    const stock = await inventoryList({ pageSize: 1, pageNum: 1, number: ite.materialCode })
                    if (stock.rows[0] && stock.rows[0].stocks) {
                      ite.jdStock = stock.rows[0].stocks ? stock.rows[0].stocks.reduce((total, item) => total + item.qty, 0) : 0
                    } else ite.jdStock = 0
                    ite.stock = stock.rows[0] ? ite.jdStock - stock.rows[0].useQty : 0
                  }
                  const bomBox = await getBomDetailByCode({ productCode: ite.productCode })
                  if (bomBox.code === 200 && bomBox.data) {
                    const markBox = await getBomDetail({ bomId: bomBox.data.id })
                    if (markBox.code === 200 && markBox.data) {
                      ite.doType = markBox.data.mark || this.doOptions[0].value
                      ite.hasChildren = ite.doType !== this.doOptions[0].value
                    } else {
                      ite.doType = this.doOptions[0].value
                      ite.hasChildren = false
                    }
                  } else {
                    ite.doType = this.doOptions[0].value
                    ite.hasChildren = false
                  }
                })
              )
              row.bomId = child.data.id
              this.$set(row, 'children', result)
            }
          }
        } catch (error) {
          console.error('加载子数据失败:', error)
        }
      }
    },
    // 递归数组
    handleArr(data) {
      let that = this
      // 初始化结果数组
      let result = []
      // 定义递归函数
      function recurse(items) {
        for (let item of items) {
          const resultItem = {
            bomId: item.bomId,
            objId: that.objId,
            type: 1,
            productId: item.id,
            productName: item.productName,
            quantity: item.productQuantity || parseFloat(((Number(item.quantity) || 1) * that.quantity).toFixed(5)),
            remark: item.remark,
            unit: item.unit,
            mark: item.doType
          }
          result.push(resultItem)
          // 如果对象有 children 属性且是数组，则递归调用
          if (Array.isArray(item.children)) {
            recurse(item.children)
          }
        }
      }
      // 循环
      recurse(data)
      return result
    },
    // 前往Bom
    goBom() {
      this.$router.push({
        path: '/private/productBom'
      })
    },
    // 设置BOM
    handleSetBom() {
      const product = {
        productCode: this.bomProduct.productCode,
        productName: this.bomProduct.productName,
        id: this.bomProduct.id
      }
      this.errOpen = false
      this.bomCreate = true
      this.$nextTick(() => {
        this.$refs.bomCreate.handleAdd(product)
      })
    },
    // 处理生产数量变化
    handleQuantityChange(row) {
      // 确保数值为合法的正数
      if (row.productQuantity < 0) {
        row.productQuantity = 0
      }
      // 数据已经直接绑定到 row.productQuantity，无需额外处理
    }
  }
}
</script>
<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';

.custom-dialog ::v-deep {
  .table-child {
    margin: -5px 0;
    display: flex;
    align-items: center;
    background-color: #f0f4f9;

    &-title {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      width: 80px;
    }

    .custom-table {
      flex: 1;
      overflow: hidden;

      .el-table__header-wrapper {
        .el-checkbox__inner {
          display: none !important;
        }
      }
    }
  }

  .table-stock {
    display: flex;
    align-items: center;
  }

  .row-expand-cover td .el-table__expand-icon {
    visibility: hidden;
  }

  .dropdown-primary {
    color: #2e73f3;
  }

  .dropdown-orange {
    color: #f35d09;
  }
}

.errTips {
  padding: 10px 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  .img {
    width: 36px;
    height: 36px;
    margin-bottom: 17px;
  }

  .title {
    font-weight: 500;
    font-size: 14px;
    color: #ed4040;
    line-height: 20px;
    margin-bottom: 10px;
  }

  .text {
    font-weight: 400;
    font-size: 12px;
    color: #666666;
    line-height: 20px;
    margin-bottom: 47px;
  }

  .btn {
    display: flex;
    align-items: center;
    justify-content: center;

    .cancel {
      width: 269px;
      height: 50px;
      border-radius: 5px 5px 5px 5px;
      border: 1px solid #cbd6e2;
      font-weight: 400;
      font-size: 16px;
      color: #999999;
      line-height: 50px;
      text-align: center;
      margin-right: 10px;
      cursor: pointer;
    }

    .go {
      width: 269px;
      height: 50px;
      background: #2e73f3;
      border-radius: 5px 5px 5px 5px;
      font-weight: 500;
      font-size: 16px;
      color: #ffffff;
      line-height: 50px;
      text-align: center;
      cursor: pointer;
    }
  }
}

.deliveryTimeBox {
  padding: 10px 20px;

  .btn {
    display: flex;
    align-items: center;
    justify-content: flex-end;

    .cancel {
      width: 200px;
      height: 50px;
      border-radius: 5px 5px 5px 5px;
      border: 1px solid #cbd6e2;
      font-weight: 400;
      font-size: 16px;
      color: #999999;
      line-height: 50px;
      text-align: center;
      margin-right: 10px;
      cursor: pointer;
    }

    .go {
      width: 200px;
      height: 50px;
      background: #2e73f3;
      border-radius: 5px 5px 5px 5px;
      font-weight: 500;
      font-size: 16px;
      color: #ffffff;
      line-height: 50px;
      text-align: center;
      cursor: pointer;
    }
  }
}

.blue {
  ::v-deep .el-icon-arrow-right {
    color: #2e73f3;
    font-size: 14px;
  }

  ::v-deep .el-table__header-wrapper {
    .el-checkbox {
      display: none !important;
    }
  }
}
</style>
