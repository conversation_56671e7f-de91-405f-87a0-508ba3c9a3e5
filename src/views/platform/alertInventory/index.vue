<template>
  <div class="newBox bgcf9 vh-85">
    <div class="custom-search flex" :style="{ justifyContent: showSearch ? 'space-between' : 'flex-end' }">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" @submit.native.prevent v-show="showSearch">
        <el-form-item label="产品名称" prop="productName">
          <el-input v-model="queryParams.productName" placeholder="请输入产品名称" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="产品编码" prop="productCode">
          <el-input v-model="queryParams.productCode" placeholder="请输入产品编码" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
          <el-button type="primary" icon="el-icon-plus" size="small" @click="handleCreate" v-hasPermi="['product:alertInventory:add']">添加产品</el-button>
        </el-form-item>
      </el-form>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns" isSetitem @updateColumns="updateColumns"></right-toolbar>
    </div>

    <div style="padding: 20px">
      <el-table v-loading="loading" ref="table" stripe :data="list" class="custom-table custom-table-cell5">
        <el-table-column align="center" label="序号" v-if="columns[0].visible" width="100">
          <template slot-scope="scope">
            <div class="table-index">
              <el-tooltip effect="dark" :content="scope.row.topTime" placement="top">
                <span class="isTop" v-if="scope.row.topTime">置顶</span>
              </el-tooltip>
              <span class="isTop noTop" v-if="!scope.row.topTime"></span>
              <span>{{ scope.$index + 1 }}</span>
            </div>
          </template>
        </el-table-column>
        <!-- 产品名称 -->
        <el-table-column align="center" prop="productName" label="产品名称" show-overflow-tooltip v-if="columns[1].visible">
          <template slot-scope="{ row }">
            <span class="table-link" @click="handleProductInfo(row)">{{ row.product && row.product.productName }}</span>
          </template>
        </el-table-column>
        <!-- 图片 -->
        <el-table-column align="center" prop="image" label="图片" show-overflow-tooltip v-if="columns[2].visible">
          <template slot-scope="scope">
            <image-preview :src="formatProductImg(scope.row.product)" :width="50" :height="50" />
          </template>
        </el-table-column>
        <!-- 规格 -->
        <el-table-column align="center" prop="specification" label="规格" show-overflow-tooltip v-if="columns[3].visible">
          <template slot-scope="scope">
            <span>{{ scope.row.product && scope.row.product.specs }}</span>
          </template>
        </el-table-column>
        <!-- 产品编码 -->
        <el-table-column align="center" prop="productCode" label="产品编码" show-overflow-tooltip v-if="columns[4].visible">
          <template slot-scope="scope">
            <span>{{ scope.row.product && scope.row.product.productCode }}</span>
          </template>
        </el-table-column>
        <!-- 单位 -->
        <el-table-column align="center" prop="unit" label="单位" show-overflow-tooltip v-if="columns[5].visible">
          <template slot-scope="scope">
            <span>{{ scope.row.product && scope.row.product.unit }}</span>
          </template>
        </el-table-column>
        <!-- 当前库存(总量) -->
        <el-table-column align="center" label="当前库存(总量)" width="180" v-if="columns[6].visible">
          <template slot-scope="scope">
            <div class="table-number" :class="{ 'tip-min': tipFormat(scope.row.stockList) === 'min', 'tip-max': tipFormat(scope.row.stockList) === 'max' }">
              <span class="pointer" :class="{ link: checkPermi(['product:alertInventory:edit']) }" @click="handleOpen(scope.row)">{{ formatInventory(scope.row.stockList) }}</span>
              <div class="tip" v-if="tipFormat(scope.row.stockList)">
                <i class="el-icon-warning"></i>
                <span>产品库存{{ tipFormat(scope.row.stockList) === 'min' ? '不足' : '超出' }}请注意</span>
              </div>
            </div>
          </template>
        </el-table-column>
        <!-- 仓库名称 -->
        <el-table-column align="center" label="仓库名称" show-overflow-tooltip v-if="columns[7].visible">
          <template slot-scope="scope">
            <span>{{ formatInventory(scope.row.stockList, 'name') }}</span>
          </template>
        </el-table-column>
        <!-- 最后编辑时间 -->
        <el-table-column align="center" prop="updateTime" label="最后编辑时间" show-overflow-tooltip v-if="columns[8].visible" />
        <!-- 设置人 -->
        <el-table-column align="center" prop="createBy" label="设置人" show-overflow-tooltip v-if="columns[9].visible" />
        <el-table-column align="center" label="生产工单" show-overflow-tooltip v-if="columns[10].visible">
          <template slot-scope="{ row }">
            <el-tooltip effect="dark" content="点击下派生产任务单" :disabled="formatProduction(row.production, 'sc') == '-'" placement="top">
              <span class="table-link" @click="handleDispatchOrder(row, 'sc', row.id)">{{ formatProduction(row.production, 'sc') }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column align="center" label="委外工单" show-overflow-tooltip v-if="columns[11].visible">
          <template slot-scope="{ row }">
            <el-tooltip effect="dark" content="点击下派生产任务单" :disabled="formatProduction(row.production, 'ww') == '-'" placement="top">
              <span class="table-link" @click="handleDispatchOrder(row, 'ww', row.id)">{{ formatProduction(row.production, 'ww') }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column align="center" label="采购产品" show-overflow-tooltip v-if="columns[12].visible">
          <template slot-scope="{ row }">
            <el-tooltip effect="dark" content="点击添加采购" :disabled="formatProduction(row.production, 'cg') == '-'" v-if="row.production && row.production.length">
              <span class="table-link" @click="handleCreateDemand(row)">{{ formatProduction(row.production, 'cg') }}</span>
            </el-tooltip>
            <el-tooltip effect="dark" content="点击添加采购" v-else>
              <span class="table-link" @click="handleCreateDemand(row)">采购产品</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <!-- 操作 -->
        <el-table-column label="操作" :width="getActionColumnWidth()" align="center">
          <template slot-scope="{ row }">
            <!-- 常用按钮 -->
            <template v-for="action in getCommonActions(row)">
              <el-button :key="action.key" :class="action.className" :loading="action.loading && row.loading" :disabled="action.disabled" @click="action.handler(row)" v-if="action.show">{{ action.label }}</el-button>
            </template>
            <!-- 更多操作 -->
            <el-popover trigger="hover" v-if="hasMoreActions(row)">
              <button type="button" class="table-btn primary" slot="reference">
                更多操作
                <i class="el-icon-arrow-down el-icon--right"></i>
              </button>
              <div class="popover-button">
                <template v-for="action in getAllActions(row)">
                  <div :key="action.key" class="popover-button-item" v-if="action.show">
                    <el-button :class="action.className" :loading="action.loading && row.loading" :disabled="action.disabled" @click="action.handler(row)">{{ action.label }}</el-button>
                    <i class="popover-button-icon" :class="[isCommonAction(action.key) ? 'el-icon-star-on' : 'el-icon-star-off', { active: isCommonAction(action.key) }]" @click="toggleCommonAction(action.key)" :title="isCommonAction(action.key) ? '取消常用' : '设置为常用'"></i>
                  </div>
                </template>
              </div>
            </el-popover>
          </template>
        </el-table-column>
      </el-table>

      <div class="custom-pagination">
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
      </div>
    </div>
    <!-- 产品详情 -->
    <product-dialog ref="productInfo" />
    <!-- 库存详情 -->
    <alert-inventory-detail ref="detail" v-if="detailShow" @callBack="handleDetailBack" />
    <!-- 添加产品 -->
    <alert-inventory-create ref="create" v-if="createShow" @callBack="handleCreateBack" />
    <!-- 新增采购产品 -->
    <create-demand ref="createDemand" @callBack="createDemand = false" v-if="createDemand" />
    <!-- 下派生产任务单 -->
    <dispatch-order ref="dispatchOrder" @callBack="handleDispatchCallBack" v-if="dispatchOrder" />
    <!-- 产品标记 -->
    <product-label ref="productLabel" @callBack="handleLabelBack" @refresh="refreshList" />
  </div>
</template>
<script>
import { inventoryAlertList, inventoryAlertTop, inventoryAlertUnTop, inventoryAlertDelete } from '@/api/kingdee/inventory'
import AlertInventoryDetail from './detail'
import AlertInventoryCreate from './create'
import ProductDialog from '@/views/public/product/dialog'
import CreateDemand from '@/views/unsalable/sell/createDemand'
import DispatchOrder from '@/views/produce/dispatch'
import { kingdee } from '@/minix'
import { checkPermi } from '@/utils/permission'
import ProductLabel from './label'
import { getProductionList } from '@/api/production'
import { getBomDetailByCode } from '@/api/bom'

export default {
  name: 'AlertInventory',
  mixins: [kingdee],
  components: { AlertInventoryDetail, AlertInventoryCreate, ProductDialog, CreateDemand, DispatchOrder, ProductLabel },
  data() {
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        productName: '',
        productCode: ''
      },
      list: [],
      total: 0,
      loading: false,
      showSearch: true,
      columns: [
        { key: 0, label: `序号`, visible: true },
        { key: 1, label: `产品名称`, visible: true },
        { key: 2, label: `图片`, visible: true },
        { key: 3, label: `规格`, visible: true },
        { key: 4, label: `产品编码`, visible: true },
        { key: 5, label: `单位`, visible: true },
        { key: 6, label: `当前库存(总量)`, visible: true },
        { key: 7, label: `仓库名称`, visible: true },
        { key: 8, label: `最后编辑时间`, visible: true },
        { key: 9, label: `设置人`, visible: true },
        { key: 10, label: `生产工单`, visible: true },
        { key: 11, label: `委外工单`, visible: true },
        { key: 12, label: `采购产品`, visible: true }
      ],
      hasPer: true,
      // 库存详情
      detailShow: false,
      // 添加产品
      createShow: false,
      // 新增采购产品
      createDemand: false,
      // 下派生产任务单
      dispatchOrder: false,
      // 产品标记
      labelShow: false,
      // 常用按钮配置
      commonActions: []
    }
  },
  computed: {
    userId() {
      return this.$store.getters.info.userId
    }
  },
  created() {
    const columns = localStorage.getItem(this.userId + '.alertInventoryColumns')
    if (columns) this.columns = JSON.parse(columns)

    // 加载常用按钮配置
    const commonActions = localStorage.getItem(this.userId + '.alertInventoryCommonActions')
    if (commonActions) this.commonActions = JSON.parse(commonActions)

    this.getList()
  },
  methods: {
    checkPermi,
    // 改变显隐列缓存
    updateColumns(data = []) {
      localStorage.setItem(this.userId + '.alertInventoryColumns', JSON.stringify(data))
    },
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 重置
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 获取列表数据
    // prettier-ignore
    getList() {
      this.loading = true
      // 先加载基础库存预警数据
      inventoryAlertList(this.queryParams).then(res => {
        const { code, msg, rows, total } = res
        if (code === 200) {
          if (rows && rows.length > 0) {
            this.list = rows.map(item => ({ ...item, loading: true }))
            this.total = total
            this.loadProductionData()
          } else {
            this.list = []
            this.total = 0
          }
        } else {
          this.$message.error(msg)
          this.list = []
          this.total = 0
        }
      }).finally(() => {
        this.loading = false
      })
    },
    // 加载生产工单等额外数据的方法
    async loadProductionData() {
      if (!this.list.length) return
      // 逐步加载每个产品的生产工单数据
      for (let index = 0; index < this.list.length; index++) {
        const row = this.list[index]
        try {
          // 先查询BOM详情Code，如果存在则查询生产工单数据
          const productCode = row.product && row.product.productCode
          const bomDetail = await getBomDetailByCode({ productCode })
          if (bomDetail.code === 200 && bomDetail.data) {
            this.$set(this.list[index], 'hasBom', true)
            // 加载生产工单数据 - 使用产品ID作为objId
            const productionParams = { pageSize: 100, pageNum: 1, objId: row.id, type: 1, lower: false }
            const production = await getProductionList(productionParams)
            if (production.code === 200 && production.rows && production.rows.length > 0) {
              const productionData = production.rows.map(item => ({ ...item, mark: item.mark || 'cg' }))
              this.$set(this.list[index], 'production', productionData)
            } else {
              this.$set(this.list[index], 'production', [])
            }
            // 设置加载完成状态
            this.$set(this.list[index], 'loading', false)
          } else {
            this.$set(this.list[index], 'hasBom', false)
            this.$set(this.list[index], 'loading', false)
          }
        } catch (error) {
          console.error(`加载产品${row.productId || row.id}的生产数据失败:`, error)
          this.$set(this.list[index], 'loading', false)
          this.$set(this.list[index], 'production', [])
        }
      }
    },
    // 刷新列表方法
    // prettier-ignore
    refreshList() {
      this.loading = true
      // 先加载基础库存预警数据
      inventoryAlertList(this.queryParams).then(res => {
        const { code, msg, rows, total } = res
        if (code === 200) {
          if (rows && rows.length > 0) {
            const list = rows.map(item => ({ ...item, loading: true }))
            this.$set(this, 'list', list)
            this.$set(this, 'total', total)
            this.loadProductionData()
          } else {
            this.$set(this, 'list', [])
            this.$set(this, 'total', 0)
          }
        } else {
          this.$message.error(msg)
          this.$set(this, 'list', [])
          this.$set(this, 'total', 0)
        }
      }).finally(() => {
        this.loading = false
      })
    },
    // 格式化回显库存和仓库
    formatInventory(stockList = [], type = undefined) {
      if (!Array.isArray(stockList) || !stockList.length) return type === 'name' ? '' : 0
      if (type === 'name') {
        const stockNameList = stockList.map(item => item.stockName || '')
        return stockNameList.filter(name => name).join(',')
      } else {
        // 计算总库存
        const total = stockList.reduce((sum, item) => {
          const qty = parseFloat(item.qty) || 0
          return sum + qty
        }, 0)
        return parseFloat(total.toFixed(5))
      }
    },
    // 库存预警超出或不足
    tipFormat(stockList = []) {
      if (!Array.isArray(stockList) || !stockList.length) return ''
      // 使用reduce一次性计算所有数值，提高性能
      const { maxNum, minNum, qty } = stockList.reduce(
        (acc, item) => {
          const maxQty = parseFloat(item.maxQty) || 0
          const minQty = parseFloat(item.minQty) || 0
          const currentQty = parseFloat(item.qty) || 0
          return {
            maxNum: acc.maxNum + maxQty,
            minNum: acc.minNum + minQty,
            qty: acc.qty + currentQty
          }
        },
        { maxNum: 0, minNum: 0, qty: 0 }
      )
      // 使用数值精度处理，避免浮点数计算误差
      const totalQty = parseFloat(qty.toFixed(5))
      const totalMaxNum = parseFloat(maxNum.toFixed(5))
      const totalMinNum = parseFloat(minNum.toFixed(5))
      if (totalQty > totalMaxNum) return 'max'
      if (totalQty < totalMinNum) return 'min'
      return ''
    },
    // 置顶操作
    async handleTop(row) {
      const { id, topTime = null } = row
      // 添加加载状态防止重复点击
      this.$set(row, 'loading', true)
      try {
        if (topTime) {
          // 取消置顶
          const res = await inventoryAlertUnTop({ id })
          if (res.code === 200) {
            this.$set(row, 'topTime', null)
            // 更新时间应该使用当前时间
            this.$set(row, 'updateTime', this.parseTime(new Date(), '{y}-{m}-{d} {h}:{i}:{s}'))
            this.$message.success('取消置顶成功')
          } else {
            this.$message.error(res.msg || '取消置顶失败')
          }
        } else {
          // 置顶
          const res = await inventoryAlertTop({ id })
          if (res.code === 200) {
            const currentTime = new Date()
            const topTimeNew = this.parseTime(currentTime, '{y}-{m}-{d} {h}:{i}:{s}')
            this.$set(row, 'topTime', topTimeNew)
            this.$set(row, 'updateTime', topTimeNew)
            this.$message.success('置顶成功')
          } else {
            this.$message.error(res.msg || '置顶失败')
          }
        }
      } catch (error) {
        this.$message.error('操作失败，请重试')
      } finally {
        // 移除加载状态
        this.$set(row, 'loading', false)
      }
    },
    // 查看库存详情
    handleOpen(row) {
      this.detailShow = true
      this.$nextTick(() => {
        this.$refs.detail.handleOpen(row)
      })
    },
    // 删除
    // prettier-ignore
    handleDelete(row) {
      this.$confirm('确定删除该产品吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        inventoryAlertDelete({ id: row.id }).then(res => {
          if (res.code === 200) {
            this.$message.success('删除成功')
            const index = this.list.findIndex(item => item.id === row.id)
            this.list.splice(index, 1)
            this.total--
          } else this.$message.error(res.msg || '删除失败')
        })
      }).catch(() => {})
    },
    // 添加产品
    handleCreate() {
      this.createShow = true
      this.$nextTick(() => {
        this.$refs.create.create()
      })
    },
    // 添加产品回调
    handleCreateBack(flag = false) {
      this.createShow = false
      if (flag) this.getList()
    },
    // 编辑库存
    handleEdit(row) {
      this.createShow = true
      this.$nextTick(() => {
        this.$refs.create.update(row)
      })
    },
    // 库存详情回调
    handleDetailBack(flag = false) {
      this.detailShow = false
      if (flag) this.getList()
    },
    // 产品详情
    handleProductInfo(row) {
      const data = row.product || row
      this.$refs.productInfo.handleView(data)
    },
    // 补充库存
    handleAddStock(row) {
      let note = []
      let quantity = 0
      let minQty = 0
      let maxQty = 0
      row.stockList.forEach(item => {
        const stockOrgName = this.ApplicationOrgId.find(org => org.value == item.stockOrg)?.label || ''
        note.push(`${stockOrgName}:${parseFloat((item.maxQty - (item.qty || 0)).toFixed(5))}`)
        quantity += parseFloat((item.maxQty - (item.qty || 0)).toFixed(5))
        minQty += parseFloat(item.minQty.toFixed(5))
        maxQty += parseFloat(item.maxQty.toFixed(5))
      })
      const arr = [{ ...row, productName: row.product && row.product.productName, unit: row.product && row.product.unit, note: note.join(';'), quantity, minQty, maxQty }]
      this.createDemand = true
      this.$nextTick(() => {
        this.$refs.createDemand.handleOpen(arr, 'alertInventory')
      })
    },
    // 产品标记
    handleLabel(row) {
      this.labelShow = true
      this.$nextTick(() => {
        this.$refs.productLabel.handleOpen(row)
      })
    },
    // 产品标记回调
    handleLabelBack(flag = false) {
      this.labelShow = false
      if (flag) this.refreshList()
    },
    // 格式化生产工单显示
    // type: 'sc' - 生产工单, 'ww' - 委外工单, 'cg' - 采购产品
    formatProduction(production, type) {
      if (!production || !production.length) return '-'
      const res = production.filter(item => item.mark === type)
      return res.length ? `${res.length}个` : '-'
    },
    // 下派生产任务单
    handleDispatchOrder(row, type, id) {
      if (!row.production || !row.production.length) {
        this.$message.info('暂无生产工单数据')
        return
      }
      const list = row.production.filter(item => item.mark === type)
      if (!list.length) {
        const typeMap = { sc: '生产工单', ww: '委外工单', cg: '采购产品' }
        this.$message.info(`暂无${typeMap[type] || '相关'}数据`)
        return
      }
      this.dispatchOrder = true
      this.$nextTick(() => {
        this.$refs.dispatchOrder.handleOpen(list)
      })
    },
    // 下派生产任务单回调
    handleDispatchCallBack(refresh = false) {
      this.dispatchOrder = false
      if (refresh) {
        this.refreshList()
      }
    },
    // 新增采购产品
    handleCreateDemand(row) {
      // 如果已有生产工单数据，直接使用采购类型的工单
      if (row.production && row.production.length) {
        const list = row.production.filter(item => item.mark === 'cg')
        if (!list.length) {
          this.$message.info('暂无采购类型的产品')
          return
        }
        this.createDemand = true
        this.$nextTick(() => {
          this.$refs.createDemand.handleOpen(list)
        })
      } else {
        this.handleAddStock(row)
        // // 如果没有生产工单数据，从库存预警产品中创建采购需求
        // const product = row.product || row
        // if (!product) {
        //   this.$message.error('产品信息不完整')
        //   return
        // }
        // // 构造采购需求数据
        // const demandData = [{ productId: product.id, productName: product.productName, productCode: product.productCode, unit: product.unit, quantity: 1, note: '库存预警补充', source: 'private' }]
        // this.createDemand = true
        // this.$nextTick(() => {
        //   this.$refs.createDemand.handleOpen(demandData)
        // })
      }
    },
    // 获取所有操作按钮配置
    getAllActions(row) {
      return [
        {
          key: 'edit',
          label: '编辑库存',
          className: 'table-btn',
          show: this.checkPermi(['product:alertInventory:edit']),
          handler: this.handleEdit
        },
        {
          key: 'top',
          label: row.topTime ? '取消置顶' : '置顶产品',
          className: 'table-btn',
          loading: true,
          show: this.checkPermi(['product:alertInventory:edit']),
          handler: this.handleTop
        },
        {
          key: 'label',
          label: '产品标记',
          className: 'table-btn',
          show: true,
          handler: this.handleLabel
        },
        {
          key: 'delete',
          label: '删除',
          className: 'table-btn danger',
          show: this.checkPermi(['product:alertInventory:delete']),
          handler: this.handleDelete
        }
      ]
    },
    // 获取常用按钮
    getCommonActions(row) {
      const allActions = this.getAllActions(row).filter(action => action.show)
      const commonActions = allActions.filter(action => this.isCommonAction(action.key))

      // 如果没有设置常用按钮，默认显示编辑库存（如果有权限），否则显示第一个有权限的按钮
      if (commonActions.length === 0) {
        const editAction = allActions.find(action => action.key === 'edit')
        if (editAction) {
          return [editAction]
        }
        // 如果没有编辑权限，返回第一个有权限的按钮
        return allActions.length > 0 ? [allActions[0]] : []
      }

      // 常用按钮不限制数量
      return commonActions
    },
    // 判断是否为常用按钮
    isCommonAction(actionKey) {
      return this.commonActions.includes(actionKey)
    },
    // 判断是否有更多操作按钮
    hasMoreActions(row) {
      const allActions = this.getAllActions(row).filter(action => action.show)
      const commonActions = this.getCommonActions(row)
      // 如果显示的按钮数量大于常用按钮数量，则显示更多操作
      return allActions.length >= commonActions.length
    },
    // 切换常用按钮设置
    toggleCommonAction(actionKey) {
      const index = this.commonActions.indexOf(actionKey)
      if (index > -1) {
        this.commonActions.splice(index, 1)
      } else {
        // 允许设置多个常用按钮
        this.commonActions.push(actionKey)
      }
      localStorage.setItem(this.userId + '.alertInventoryCommonActions', JSON.stringify(this.commonActions))
    },
    // 计算操作列宽度
    getActionColumnWidth() {
      // 如果列表为空，返回默认宽度
      if (!this.list || this.list.length === 0) {
        return 220
      }
      // 取第一行数据来计算宽度
      const firstRow = this.list[0]
      const commonActions = this.getCommonActions(firstRow)
      const hasMoreActions = this.hasMoreActions(firstRow)
      // 常用按钮数量 + 更多操作按钮(如果有的话) * 110px
      const buttonCount = commonActions.length + (hasMoreActions ? 1 : 0)
      const width = buttonCount * 110
      // 设置最小宽度为 110px，最大宽度为 550px
      return Math.max(110, Math.min(550, width))
    }
  }
}
</script>
<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
.custom-search {
  align-items: center;
  min-height: 47px;
  ::v-deep {
    .el-form-item--small.el-form-item {
      margin-bottom: 0;
    }
  }
}
.table-index {
  display: flex;
  align-items: center;
  .isTop {
    background-color: #e2e6f3;
    border-radius: 5px;
    border: 1px solid #c5d0e3;
    color: #8b96ab;
    margin-right: 10px;
    width: 36px;
    height: 26px;
    text-align: center;
    &.noTop {
      background-color: transparent;
      border: none;
    }
  }
}
.table-number {
  display: flex;
  flex-direction: column;
  align-items: center;
  .link {
    color: #2e73f3;
    &::after {
      content: '>';
      display: inline-block;
      margin-left: 5px;
    }
  }
  .tip {
    width: 160px;
    display: flex;
    align-items: center;
    height: 28px;
    border-radius: 50px;
    font-size: 12px;
    padding-left: 7px;
    i {
      font-size: 18px;
      margin-right: 5px;
    }
  }
  &.tip-min {
    .link {
      color: #f50e0e;
    }
    .tip {
      background-color: rgba(245, 14, 14, 0.1);
      border: 1px solid #f50e0e;
      color: #f50e0e;
    }
  }
  &.tip-max {
    .link {
      color: #67c23a;
    }
    .tip {
      background-color: rgba(103, 194, 58, 0.1);
      border: 1px solid #67c23a;
      color: #67c23a;
    }
  }
}
</style>
