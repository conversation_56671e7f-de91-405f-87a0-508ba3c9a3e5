<template>
  <div class="newBox bgcf9 vh-85">
    <!-- 列表 -->
    <div class="Box">
      <template v-if="list.length > 0">
        <el-table v-loading="loading" ref="allTable" stripe :data="list" row-key="id" :key="key" style="width: 100%" class="custom-table custom-table-cell5">
          <el-table-column align="center" type="index" label="序号"></el-table-column>
          <el-table-column align="center" prop="title" label="标题" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="title" label="采购方" show-overflow-tooltip>
            <template slot-scope="{ row }">
              <span v-if="row.demandType === 'enterprise'">{{ row.supplier.name }}</span>
              <span v-if="row.demandType === 'person'">{{ row.createBy }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="supplyEndTime" label="供货截止时间" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="expireTime" label="剩余有效时间" show-overflow-tooltip>
            <template slot-scope="{ row }">
              <Countdown :expireTime="row.expireTime" pattern="{d}{h}{m}" />
            </template>
          </el-table-column>
          <el-table-column align="center" prop="remark" label="备注" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="releaseTime" label="发布时间" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="createTime" label="创建时间" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" label="操作" width="150">
            <template slot-scope="{ row }">
              <button type="button" class="table-btn primary" @click="handleView(row)">{{ row.isReply ? '修改' : '查看详情' }}</button>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <el-empty v-if="list.length === 0 && !loading" />
    </div>
  </div>
</template>

<script>
import { purchasingDemandSeller } from '@/api/purchase'
import { expireTimeFormat } from '@/utils'

export default {
  name: 'Demands',
  data() {
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 10
      },
      key: 1,
      loading: true,
      list: [],
      total: 0
    }
  },
  created() {
    this.getList()
  },
  methods: {
    expireTimeFormat,
    // 列表
    getList() {
      const loading = this.$loading({
        lock: true,
        text: '加载中',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      this.loading = true
      purchasingDemandSeller(this.queryParams).then(res => {
        if (res.code === 200) {
          this.list = res.data
          this.loading = false
        } else {
          this.$message.error(res.msg)
        }
      }).finally(() => {
        loading.close()
      })
    },
    // 查看详情
    handleView(row) {
      const requestId = row.requestId
      const url = this.$router.resolve({
        path: '/formFilling',
        query: {
          requestId
        }
      })
      window.open(url.href, '_blank')
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
.Box {
  padding: 15px 20px;
}
</style>
