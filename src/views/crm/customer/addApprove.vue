<template>
    <div>
        <el-dialog v-dialogDragBox :title="title" :visible.sync="open" width="1152px" class="custom-dialog">
            <div class="order_box">
                <div class="listInfo_tabs_box">
                    <div class="listInfo_tabs_left">
                        <el-tabs type="border-card" v-model="userTypeActive" @tab-click="handleTabItem">
                            <el-tab-pane label="新建外勤审批" name="1"></el-tab-pane>
                            <el-tab-pane label="客户拜访申请" name="2"></el-tab-pane>
                        </el-tabs>
                    </div>
                </div>
                <div v-if="userTypeActive == '1'">
                    <el-form ref="form" :model="form" :rules="rules" label-width="8em" label-position="left">
                        <el-row :gutter="20" style="display: flex; flex-wrap: wrap">
                            <el-col :span="24">
                                <div style="padding: 0 20px 20px;">
                                    <div
                                        style="display: flex; align-items: center; justify-content: space-between; padding-bottom: 8px; border-bottom: 1px solid #E1E2E2;">
                                        <div>基本信息</div>
                                        <div style="font-weight: 400; font-size: 14px; color: #666666; display: flex; align-items: center; cursor: pointer;"
                                            @click="handleFieldapprovalConfig">
                                            <img style="width: 20px; height: 20px; margin-right: 2px;"
                                                src="@/assets/images/setting.png" alt="">
                                            自定义
                                        </div>
                                    </div>
                                </div>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="关联客户" prop="categoryId">
                                    <el-select v-model="form.categoryId" placeholder="请选择客户" style="width: 100%">
                                        <el-option v-for="(item, index) in categoryList" :key="index" :label="item.name"
                                            :value="item.id"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="联系人" prop="categoryId">
                                    <el-select v-model="form.categoryId" placeholder="请选择客户联系人" style="width: 100%">
                                        <el-option v-for="(item, index) in categoryList" :key="index" :label="item.name"
                                            :value="item.id"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="24">
                                <el-form-item label="说明" prop="remark">
                                    <el-input v-model="form.remark" placeholder="请输入说明详细" type="textarea"
                                        :autosize="{ minRows: 4, maxRows: 4 }" resize="none" class="custom-textarea" />
                                </el-form-item>
                            </el-col>
                            <el-col :span="24">
                                <div style="padding: 0 20px 20px;">
                                    <div
                                        style="display: flex; align-items: center; justify-content: space-between; padding-bottom: 8px; border-bottom: 1px solid #E1E2E2;">
                                        <div>审批信息</div>
                                        <div style="font-weight: 400; font-size: 14px; color: #666666; display: flex; align-items: center; cursor: pointer;"
                                            @click="handleApprovalProcess">
                                            <img style="width: 20px; height: 20px; margin-right: 2px;"
                                                src="@/assets/images/setting.png" alt="">
                                            自定义
                                        </div>
                                    </div>
                                </div>
                            </el-col>
                            <el-col :span="24">
                                <el-form-item label="审批人" prop="categoryId">
                                    <el-tag closable :disable-transitions="false" @close="handleClose(tag)">某某某</el-tag>
                                    <el-button type="text" icon="el-icon-plus"
                                        style="margin-left: 15px;">选择人员</el-button>
                                </el-form-item>
                            </el-col>
                            <el-col :span="24">
                                <el-form-item label="抄送给" prop="categoryId">
                                    <el-tag closable :disable-transitions="false" @close="handleClose(tag)">某某某</el-tag>
                                    <el-button type="text" icon="el-icon-plus"
                                        style="margin-left: 15px;">选择人员</el-button>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                </div>
                <div v-if="userTypeActive == '2'">
                    <el-form ref="form" :model="form" :rules="rules" label-width="8em" label-position="left">
                        <el-row :gutter="20" style="display: flex; flex-wrap: wrap">
                            <el-col :span="24">
                                <div style="padding: 0 20px 20px;">
                                    <div
                                        style="display: flex; align-items: center; justify-content: space-between; padding-bottom: 8px; border-bottom: 1px solid #E1E2E2;">
                                        <div>基本信息</div>
                                        <div style="font-weight: 400; font-size: 14px; color: #666666; display: flex; align-items: center; cursor: pointer;"
                                            @click="handleFieldapprovalConfig">
                                            <img style="width: 20px; height: 20px; margin-right: 2px;"
                                                src="@/assets/images/setting.png" alt="">
                                            自定义
                                        </div>
                                    </div>
                                </div>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="拜访客户" prop="categoryId">
                                    <el-select v-model="form.categoryId" placeholder="请选择客户" style="width: 100%">
                                        <el-option v-for="(item, index) in categoryList" :key="index" :label="item.name"
                                            :value="item.id"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="拜访时间" prop="supplyEndTime">
                                    <el-date-picker v-model="form.supplyEndTime" type="datetime"
                                        value-format="yyyy-MM-dd HH:mm:ss" placeholder="请选择拜访时间"
                                        style="width: 100%"></el-date-picker>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="联系人" prop="categoryId">
                                    <el-select v-model="form.categoryId" placeholder="请选择客户联系人" style="width: 100%">
                                        <el-option v-for="(item, index) in categoryList" :key="index" :label="item.name"
                                            :value="item.id"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="24">
                                <el-form-item label="理由" prop="remark">
                                    <el-input v-model="form.remark" placeholder="请输入说明详细" type="textarea"
                                        :autosize="{ minRows: 4, maxRows: 4 }" resize="none" class="custom-textarea" />
                                </el-form-item>
                            </el-col>
                            <el-col :span="24">
                                <div>
                                    <div style="padding: 0 20px;">
                                        <span style="font-weight: 400; font-size: 12px; color: #999999; line-height: 20px;">关联业务</span>
                                        <el-button type="text" icon="el-icon-plus"
                                        style="margin-left: 55px;">添加</el-button>
                                    </div>
                                    <div style="height: 162px; background: #F0F3F9; margin-top: 10px; padding: 20px;">
                                        <div style="height: 114px; background: #FFFFFF; border-radius: 5px; border: 1px solid #CBD6E2;">
                                            <div></div>
                                        </div>
                                    </div>
                                </div>
                            </el-col>
                            <el-col :span="24">
                                <div style="padding: 0 20px 20px;">
                                    <div
                                        style="display: flex; align-items: center; justify-content: space-between; padding-bottom: 8px; border-bottom: 1px solid #E1E2E2;">
                                        <div>审批信息</div>
                                        <div style="font-weight: 400; font-size: 14px; color: #666666; display: flex; align-items: center; cursor: pointer;"
                                            @click="handleApprovalProcess">
                                            <img style="width: 20px; height: 20px; margin-right: 2px;"
                                                src="@/assets/images/setting.png" alt="">
                                            自定义
                                        </div>
                                    </div>
                                </div>
                            </el-col>
                            <el-col :span="24">
                                <el-form-item label="审批人" prop="categoryId">
                                    <el-tag closable :disable-transitions="false" @close="handleClose(tag)">某某某</el-tag>
                                    <el-button type="text" icon="el-icon-plus"
                                        style="margin-left: 15px;">选择人员</el-button>
                                </el-form-item>
                            </el-col>
                            <el-col :span="24">
                                <el-form-item label="抄送给" prop="categoryId">
                                    <el-tag closable :disable-transitions="false" @close="handleClose(tag)">某某某</el-tag>
                                    <el-button type="text" icon="el-icon-plus"
                                        style="margin-left: 15px;">选择人员</el-button>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                </div>
            </div>
            <div slot="footer">
                <button type="button" class="custom-dialog-btn" @click="handleCancel">取消</button>
                <button type="button" class="custom-dialog-btn primary" @click="handleSumit">确定</button>
            </div>
        </el-dialog>

        <fieldapprovalconfig-dialog ref="fieldapprovalConfig" />
        <approvalprocess-dialog ref="approvalProcess" />
    </div>
</template>

<script>
import fieldapprovalconfigDialog from './fieldapprovalConfig'
import approvalprocessDialog from './approvalProcess'

export default {
    components: { fieldapprovalconfigDialog, approvalprocessDialog },
    data() {
        return {
            title: '新建审批',
            form: {
                products: [{
                    productName: '某某产品',
                    model: 'DN150*168*40*40',
                    quantity: undefined
                }]
            },
            rules: {},
            open: false,
            loading: false,
            fileType: ['png', 'PNG', 'jpg', 'JPG', 'jpeg', 'JPEG', 'pdf'],
            key: 1,
            radio: undefined,
            options: [],
            activeName: 'second',
            value: undefined,
            unitOptions: [],
            userTypeActive: '1',
            timeType: 1,
            input1: '',
            categoryList: [],
        }
    },
    created() {

    },
    methods: {
        reset() {
            this.form = {
                products: [{
                    productName: '某某产品',
                    model: 'DN150*168*40*40',
                    quantity: undefined
                }]
            }
            this.resetForm('form')
        },
        handleAdd() {
            this.reset()
            this.title = '新建审批'
            this.open = true
        },
        // 取消
        handleCancel() {
            this.open = false
            this.reset()
        },
        // 提交
        handleSumit() {
            this.$refs['form'].validate(valid => {
                if (valid) {
                    if (this.form.id != null) {
                        this.form.productId = this.form.id
                        updateUnsalable(this.form).then(res => {
                            if (res.code === 200) {
                                this.$message.success('修改成功')
                                this.open = false
                                this.$parent.getList()
                            } else {
                                this.$message.error(res.msg)
                            }
                        })
                    } else {
                        addUnsalable(this.form).then(res => {
                            if (res.code === 200) {
                                this.$message.success('新增成功')
                                this.open = false
                                this.$parent.getList()
                            } else {
                                this.$message.error(res.msg)
                            }
                        })
                    }
                }
            })
        },
        handleAllSelect() { },
        handleClick() { },
        handleOpen() { },
        handleClose() { },
        handleTabItem() { },
        handleChange() { },
        handleFieldapprovalConfig() {
            this.$refs.fieldapprovalConfig.handleOpen()
        },
        handleApprovalProcess() {
            this.$refs.approvalProcess.handleOpen()
        },
    }
}
</script>
<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';

.ipt {
    ::v-deep {
        .el-input__inner {
            cursor: pointer;
        }
    }
}

.order_box {
    padding: 0;

    ::v-deep {
        .el-form-item {
            margin-left: 20px;
        }

        .el-form-item__content {
            padding-right: 20px;
        }

        .el-divider--vertical {
            height: 2em;
        }
    }

    .table_list {
        background: #F0F3F9;
        padding: 20px;
        margin-bottom: 20px;

        .table_title {
            display: flex;
            align-items: center;
            font-weight: 400;
            font-size: 12px;
            color: #999999;
            line-height: 20px;
        }

        .form_table {
            margin-top: 20px;
        }

        .table_total {
            margin-top: 10px;
            height: 48px;
            background: #EFF5FF;
            border-radius: 5px;
            border: 1px solid #2E73F3;
            display: flex;
            align-items: center;
            justify-content: space-between;

            .total_left {
                padding-left: 30px;
                display: flex;
                align-items: center;

                .left_item {
                    font-weight: 400;
                    font-size: 14px;
                    color: #666666;
                    margin-right: 90px;
                    display: flex;
                    align-items: center;

                    span {
                        font-weight: 500;
                        font-size: 18px;
                        color: #2E73F3;
                        margin: 0 10px;
                    }
                }
            }

            .total_right {
                margin-right: 145px;
                font-weight: 400;
                font-size: 14px;
                color: #666666;
                display: flex;
                align-items: center;

                span {
                    font-weight: 500;
                    font-size: 18px;
                    color: #F35D09;
                    margin-left: 10px;
                }
            }
        }

        .flex_box {
            display: flex;
            align-items: center;
            justify-content: space-around;

            span {
                margin-bottom: 22px;
                color: #2E73F3;
                font-weight: 400;
                font-size: 12px;
            }
        }
    }

    .border_no {
        ::v-deep {
            .el-input__inner {
                border: none;
            }
        }
    }
}

.customer_list_box {
    width: 444px;
    height: 538px;
    background: #FFFFFF;
    box-shadow: 0px 1px 15px 0px rgba(0, 0, 0, 0.25);
    border-radius: 5px;
    border: 1px solid #2E73F3;

    ::v-deep {
        .el-tabs__header {
            margin-bottom: 0;

            .el-tabs__nav-wrap {
                padding: 0 15px;
            }
        }
    }

    .dept_box {
        display: flex;

        .dept_left {
            // width: 164px;
        }

        .dept_right {
            // flex: 1;
        }
    }
}

.listInfo_tabs_box {
    background: #F2F3F9;
    position: relative;
    margin: 0 20px 20px;

    .listInfo_tabs_left {
        position: relative;
    }

    ::v-deep {
        .el-tabs--border-card {
            box-shadow: none;
            border: none;
        }

        .el-tabs__content {
            padding: 0;
        }

        .el-tabs--border-card>.el-tabs__header {
            background: #F2F3F9;
            border-bottom: 1px solid #CBD6E2;

            .el-tabs__item {
                color: #666666;

                &.is-active {
                    color: #2E73F3;
                    border-top-color: #CBD6E2;
                    border-right-color: #CBD6E2;
                    border-left-color: #CBD6E2;
                    margin-top: 0;
                    margin-left: 0;
                    border-top-left-radius: 5px;
                    border-top-right-radius: 5px;
                }
            }
        }
    }

    .listInfo_tabs_right {
        font-weight: 400;
        font-size: 14px;
        color: #666666;
        display: flex;
        align-items: center;
        cursor: pointer;
        position: absolute;
        right: 20px;
        top: 10px;

        img {
            width: 20px;
            height: 20px;
            margin-right: 2px;
        }
    }
}

.next_follow_box {
    background: #F0F3F9;
    padding: 20px;
    margin-bottom: 20px;
}

.time_box {
    ::v-deep {
        .el-select {
            .el-input--suffix {
                height: 32px;

                .el-input__inner {
                    height: 32px;
                }

                .el-input__icon {
                    line-height: 32px;
                }
            }
        }
    }
}
</style>
