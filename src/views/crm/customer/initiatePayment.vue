<template>
    <div>
        <el-dialog v-dialogDragBox :title="title" :visible.sync="open" width="1152px" class="custom-dialog">
            <div class="order_box">
                <el-form ref="form" :model="form" :rules="rules" label-width="8em" label-position="left">
                    <el-row :gutter="20" style="display: flex; flex-wrap: wrap">
                        <el-col :span="24">
                            <div style="padding: 0 20px 20px;">
                                <div
                                    style="display: flex; align-items: center; justify-content: space-between; padding-bottom: 8px; border-bottom: 1px solid #E1E2E2;">
                                    <div>基本信息</div>
                                </div>
                            </div>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label="负责人" prop="categoryId">
                                <div style="display: flex; align-items: center;">
                                    <el-radio-group v-model="radio" style="display: flex; align-items: center;">
                                        <el-radio :label="1">客户负责人</el-radio>
                                        <el-radio :label="2">指定人员</el-radio>
                                    </el-radio-group>
                                    <div style="margin-left: 20px;">
                                        <el-tag closable :disable-transitions="false"
                                            @close="handleClose(tag)">某某某</el-tag>
                                        <el-button type="text" icon="el-icon-plus"
                                            style="margin-left: 15px;">选择人员</el-button>
                                    </div>
                                </div>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="收款金额" prop="productName">
                                <el-input v-model="form.productName" placeholder="请输入收款金额" >
                                    <span slot="suffix" style="padding-right: 10px;">元</span>
                                </el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="收款账号" prop="productName">
                                <el-input v-model="form.productName" placeholder="请输入收款账号" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="客户名称" prop="categoryId">
                                <el-select v-model="form.categoryId" placeholder="请选择客户" style="width: 100%">
                                    <el-option v-for="(item, index) in categoryList" :key="index" :label="item.name"
                                        :value="item.id"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="联系人" prop="productName">
                                <div
                                    style="display: flex; align-items: center; border: 1px solid #CBD7E2; border-radius: 5px;">
                                    <el-select class="border_no" v-model="form.unit" placeholder="请选择客户联系人"
                                        style="width: 100%">
                                        <el-option v-for="(item, index) in options" :key="index" :label="item"
                                            :value="item"></el-option>
                                    </el-select>
                                    <el-divider direction="vertical"></el-divider>
                                    <div style="display: flex; align-items: center; width: 120px;">
                                        <img src="@/assets/images/add_blue.png" alt="" style="width: 20px; height: 20px;">
                                        <span>选择订单</span>
                                    </div>
                                </div>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label="成交订单/合同" prop="categoryId">
                                <el-tag closable :disable-transitions="false" @close="handleClose(tag)">某某某</el-tag>
                                <el-button type="text" icon="el-icon-plus" style="margin-left: 15px;">选择订单</el-button>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <div style="padding: 0 20px 20px;">
                                <div
                                    style="display: flex; align-items: center; justify-content: space-between; padding-bottom: 8px; border-bottom: 1px solid #E1E2E2;">
                                    <div>审批信息</div>
                                    <div
                                        style="font-weight: 400; font-size: 14px; color: #666666; display: flex; align-items: center; cursor: pointer;" @click="handleInitiateConfig">
                                        <img style="width: 20px; height: 20px; margin-right: 2px;"
                                            src="@/assets/images/setting.png" alt="">
                                        自定义
                                    </div>
                                </div>
                            </div>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="费用名称">
                                <div class="create-user-form-item">
                                    <el-select v-model="form.customerType" placeholder="请选择费用名称">
                                        <el-option v-for="item in customerTypeData" :key="item.name" :label="item.name"
                                            :value="item.name"></el-option>
                                    </el-select>
                                    <el-popover ref="customerTypePopover" trigger="click" popper-class="crm-popover"
                                        @after-leave="customerTypeShow = false">
                                        <div class="create-config" slot="reference" @click="customerTypeShow = true">
                                            <i class="el-icon-edit-outline"></i>
                                            <span>编辑</span>
                                        </div>
                                        <config-table title="费用名称" :config-data="customerTypeData"
                                            config-type="Customer_Type"
                                            @close="handleClosePopover('customerTypePopover')"
                                            @update="handleUpdateConfigData('customerTypeData', $event)"
                                            v-if="customerTypeShow"></config-table>
                                    </el-popover>
                                </div>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label="备注" prop="remark">
                                <el-input v-model="form.remark" placeholder="请输入备注详细" type="textarea"
                                    :autosize="{ minRows: 4, maxRows: 4 }" resize="none" class="custom-textarea" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label="相关文档" prop="picture1">
                                <image-upload v-model="form.picture1" :file-type="fileType" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label="图片上传" prop="picture1">
                                <image-upload v-model="form.picture1" :file-type="fileType" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
            <div slot="footer">
                <button type="button" class="custom-dialog-btn" @click="handleCancel">取消</button>
                <button type="button" class="custom-dialog-btn primary" @click="handleSumit">确定</button>
            </div>
        </el-dialog>

        <initiateconfig-dialog ref="initiateConfig" />
        <salesprocess-dialog ref="salesProcess" />
    </div>
</template>

<script>
import initiateconfigDialog from './initiateConfig'
import salesprocessDialog from './salesProcess'
import ConfigTable from './create/config'

export default {
    components: { initiateconfigDialog, salesprocessDialog, ConfigTable },
    data() {
        return {
            title: '发起收款请求',
            form: {
                products: [{
                    productName: '某某产品',
                    model: 'DN150*168*40*40',
                    quantity: undefined
                }]
            },
            rules: {},
            open: false,
            loading: false,
            fileType: ['png', 'PNG', 'jpg', 'JPG', 'jpeg', 'JPEG', 'pdf'],
            key: 1,
            radio: undefined,
            options: [],
            activeName: 'second',
            value: undefined,
            unitOptions: [],
            userTypeActive: '1',
            timeType: 1,
            input1: '',
            categoryList: [],
            customerTypeData: [],
            customerTypeShow: false,
        }
    },
    created() {
        this.customerTypeData = [
            {
                name: '咨询费',
                color: '#2E74F3',
                hidden: false,
                isActive: false
            },
            {
                name: '贷款',
                color: '#2E74F3',
                hidden: false,
                isActive: true
            },
            {
                name: '服务费',
                color: '#2E74F3',
                hidden: false,
                isActive: true
            }
        ]
    },
    methods: {
        reset() {
            this.form = {
                products: [{
                    productName: '某某产品',
                    model: 'DN150*168*40*40',
                    quantity: undefined
                }]
            }
            this.resetForm('form')
        },
        handleAdd() {
            this.reset()
            this.title = '发起收款请求'
            this.open = true
        },
        // 取消
        handleCancel() {
            this.open = false
            this.reset()
        },
        // 提交
        handleSumit() {
            this.$refs['form'].validate(valid => {
                if (valid) {
                    if (this.form.id != null) {
                        this.form.productId = this.form.id
                        updateUnsalable(this.form).then(res => {
                            if (res.code === 200) {
                                this.$message.success('修改成功')
                                this.open = false
                                this.$parent.getList()
                            } else {
                                this.$message.error(res.msg)
                            }
                        })
                    } else {
                        addUnsalable(this.form).then(res => {
                            if (res.code === 200) {
                                this.$message.success('新增成功')
                                this.open = false
                                this.$parent.getList()
                            } else {
                                this.$message.error(res.msg)
                            }
                        })
                    }
                }
            })
        },
        handleAllSelect() { },
        handleClick() { },
        handleOpen() { },
        handleClose() { },
        handleTabItem() { },
        handleChange() { },
        handleInitiateConfig() {
            this.$refs.initiateConfig.handleOpen()
        },
        handleSalesProcess() {
            this.$refs.salesProcess.handleOpen()
        },
        // 关闭Popover弹窗
        handleClosePopover(name) {
            this.$refs[name].doClose()
        },
        // 更新配置数据
        handleUpdateConfigData(name, data) {
            if (name === 'customerTypeData') {
                this.customerTypeData = JSON.parse(JSON.stringify(data))
                const obj = this.customerTypeData.find(item => item.name === this.form.customerType)
                if (!obj) this.form.customerType = ''
            } else if (name === 'customerStatusData') {
                this.customerStatusData = JSON.parse(JSON.stringify(data))
                const obj = this.customerStatusData.find(item => item.name === this.form.customerStatus)
                if (!obj) this.form.customerStatus = ''
            }
        },
    }
}
</script>
<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
@import '~@/assets/styles/crm.scss';

.ipt {
    ::v-deep {
        .el-input__inner {
            cursor: pointer;
        }
    }
}

.order_box {
    padding: 0;

    ::v-deep {
        .el-form-item {
            margin-left: 20px;
        }

        .el-form-item__content {
            padding-right: 20px;
        }

        .el-divider--vertical {
            height: 2em;
        }
    }

    .table_list {
        background: #F0F3F9;
        padding: 20px;
        margin-bottom: 20px;

        .table_title {
            display: flex;
            align-items: center;
            font-weight: 400;
            font-size: 12px;
            color: #999999;
            line-height: 20px;
        }

        .form_table {
            margin-top: 20px;
        }

        .table_total {
            margin-top: 10px;
            height: 48px;
            background: #EFF5FF;
            border-radius: 5px;
            border: 1px solid #2E73F3;
            display: flex;
            align-items: center;
            justify-content: space-between;

            .total_left {
                padding-left: 30px;
                display: flex;
                align-items: center;

                .left_item {
                    font-weight: 400;
                    font-size: 14px;
                    color: #666666;
                    margin-right: 90px;
                    display: flex;
                    align-items: center;

                    span {
                        font-weight: 500;
                        font-size: 18px;
                        color: #2E73F3;
                        margin: 0 10px;
                    }
                }
            }

            .total_right {
                margin-right: 145px;
                font-weight: 400;
                font-size: 14px;
                color: #666666;
                display: flex;
                align-items: center;

                span {
                    font-weight: 500;
                    font-size: 18px;
                    color: #F35D09;
                    margin-left: 10px;
                }
            }
        }

        .flex_box {
            display: flex;
            align-items: center;
            justify-content: space-around;

            span {
                margin-bottom: 22px;
                color: #2E73F3;
                font-weight: 400;
                font-size: 12px;
            }
        }
    }

    .border_no {
        ::v-deep {
            .el-input__inner {
                border: none;
            }
        }
    }
}

.customer_list_box {
    width: 444px;
    height: 538px;
    background: #FFFFFF;
    box-shadow: 0px 1px 15px 0px rgba(0, 0, 0, 0.25);
    border-radius: 5px;
    border: 1px solid #2E73F3;

    ::v-deep {
        .el-tabs__header {
            margin-bottom: 0;

            .el-tabs__nav-wrap {
                padding: 0 15px;
            }
        }
    }

    .dept_box {
        display: flex;

        .dept_left {
            // width: 164px;
        }

        .dept_right {
            // flex: 1;
        }
    }
}

.listInfo_tabs_box {
    background: #F2F3F9;
    position: relative;
    margin: 0 20px 20px;

    .listInfo_tabs_left {
        position: relative;
    }

    ::v-deep {
        .el-tabs--border-card {
            box-shadow: none;
            border: none;
        }

        .el-tabs__content {
            padding: 0;
        }

        .el-tabs--border-card>.el-tabs__header {
            background: #F2F3F9;
            border-bottom: 1px solid #CBD6E2;

            .el-tabs__item {
                color: #666666;

                &.is-active {
                    color: #2E73F3;
                    border-top-color: #CBD6E2;
                    border-right-color: #CBD6E2;
                    border-left-color: #CBD6E2;
                    margin-top: 0;
                    margin-left: 0;
                    border-top-left-radius: 5px;
                    border-top-right-radius: 5px;
                }
            }
        }
    }

    .listInfo_tabs_right {
        font-weight: 400;
        font-size: 14px;
        color: #666666;
        display: flex;
        align-items: center;
        cursor: pointer;
        position: absolute;
        right: 20px;
        top: 10px;

        img {
            width: 20px;
            height: 20px;
            margin-right: 2px;
        }
    }
}

.next_follow_box {
    background: #F0F3F9;
    padding: 20px;
    margin-bottom: 20px;
}

.time_box {
    ::v-deep {
        .el-select {
            .el-input--suffix {
                height: 32px;

                .el-input__inner {
                    height: 32px;
                }

                .el-input__icon {
                    line-height: 32px;
                }
            }
        }
    }
}
</style>
