<template>
    <div>
        <el-dialog v-dialogDragBox :visible.sync="open" width="1152px" class="custom-dialog">
            <div slot="title" style="display: flex; align-items: center;">
                <span style="font-weight: 400; font-size: 18px; color: #666666; line-height: 20px;">阶段完成后填写内容自定义设置</span>
                <span
                    style="margin-left: 50px; font-weight: 400; font-size: 12px; color: #999999; line-height: 20px;">注：支持拖动排序</span>
            </div>
            <div class="contactConfig_box">
                <div class="radio_box">
                    <span>可编辑此选项权限</span>
                    <el-radio-group v-model="radio">
                        <el-radio :label="3">仅管理员</el-radio>
                        <el-radio :label="6">所有人</el-radio>
                        <el-radio :label="9">指定人员</el-radio>
                    </el-radio-group>
                </div>
                <div class="app-container">
                    <el-table :data="list" border :header-cell-style="{ 'text-align': 'center' }"
                        :row-class-name="activeClass" class="tableDragBox">
                        <el-table-column label="序号/顺序" width="80" align="center" type="index"></el-table-column>
                        <el-table-column label="原始名称" align="center" show-overflow-tooltip>
                            <template slot-scope="{ row }">
                                <span>{{ row.data1 }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="自定义名称" align="center" show-overflow-tooltip>
                            <template slot-scope="{ row }">
                                <el-input type="text" v-model="row.data5"></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column label="开启/关闭" align="center" show-overflow-tooltip>
                            <template slot-scope="{ row }">
                                <el-switch v-model="row.data2" active-color="#2E73F3" inactive-color="#ff4949">
                                </el-switch>
                            </template>
                        </el-table-column>
                        <el-table-column label="输入框类型" align="center" show-overflow-tooltip>
                            <template slot-scope="{ row }">
                                <el-select v-model="row.data3">
                                    <el-option label="单行文字输入框" :value="1"></el-option>
                                    <el-option label="多行文字输入框" :value="2"></el-option>
                                </el-select>
                            </template>
                        </el-table-column>
                        <el-table-column label="是否必填" align="center" show-overflow-tooltip>
                            <template slot-scope="{ row }">
                                <el-switch v-model="row.data4" active-color="#2E73F3" inactive-color="#ff4949">
                                </el-switch>
                            </template>
                        </el-table-column>
                    </el-table>

                    <div style="margin-top: 20px;">
                        <el-button type="primary" icon="el-icon-plus" plain>新建录入信息</el-button>
                    </div>
                </div>
            </div>
            <div slot="footer">
                <button type="button" class="custom-dialog-btn" @click="open = false">恢复默认设置</button>
                <button type="button" class="custom-dialog-btn" @click="open = false">取消</button>
                <button type="button" class="custom-dialog-btn primary" @click="open = false">确定</button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
export default {
    data() {
        return {
            open: false,
            radio: 3,
            list: [
                {
                    dataId: "202404220001",
                    data1: "第一列数据1",
                    data2: true,
                    data3: 1,
                    data4: true,
                    data5: '1',
                },
                {
                    dataId: "202404220002",
                    data1: "第一列数据2",
                    data2: true,
                    data3: 1,
                    data4: true,
                    data5: '1',
                },
                {
                    dataId: "202404220003",
                    data1: "第一列数据3",
                    data2: true,
                    data3: 2,
                    data4: true,
                    data5: '1',
                },
                {
                    dataId: "202404220004",
                    data1: "第一列数据4",
                    data2: true,
                    data3: 1,
                    data4: true,
                    data5: '1',
                },
                {
                    dataId: "202404220005",
                    data1: "第一列数据5",
                    data2: true,
                    data3: 2,
                    data4: true,
                    data5: '1',
                },
            ],
            // 表格表头
            dragIndex: null,
            newDragIndex: null,
        }
    },
    created() { },
    mounted() { },
    watch: {
        list() {
            this.$nextTick(() => {
                this.watchTable();
            });
        },
    },
    methods: {
        // 监听表格
        watchTable() {
            const dragBox = document.querySelectorAll(".tableDragBox .tableTrBox");
            dragBox.forEach((i, idx) => {
                i.setAttribute("draggable", "true");
                i.ondragstart = () => this.dragStartItem(idx);
                i.ondragover = () => this.dragOverItem(idx);
                i.ondragend = () => this.dragEndItem();
            });
        },
        // 表格行添加class
        activeClass() {
            return "tableTrBox";
        },
        // 拖拽开始-记录起始index
        dragStartItem(idx) {
            this.dragIndex = idx;
        },
        // 拖拽结束-记录目标index
        dragOverItem(index) {
            this.newDragIndex = index;
        },
        // 拖拽完成-整理表格数据
        dragEndItem() {
            const data = this.list[this.dragIndex];
            this.list.splice(this.dragIndex, 1);
            this.list.splice(this.newDragIndex, 0, data);
        },
        handleOpen() {
            this.open = true
            this.$nextTick(() => {
                this.watchTable();
            });
        },
    },
}
</script>

<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';

.contactConfig_box {
    .radio_box {
        padding: 10px 20px;
        display: flex;
        align-items: center;

        span {
            font-weight: 400;
            font-size: 12px;
            color: #999999;
            line-height: 20px;
            margin-right: 20px;
        }
    }
}

.tableTrBox {
    cursor: move !important;
    position: relative !important;
}
</style>