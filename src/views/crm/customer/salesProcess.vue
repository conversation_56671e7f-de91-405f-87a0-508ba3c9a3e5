<template>
    <div>
        <el-dialog v-dialogDragBox :visible.sync="open" width="1152px" class="custom-dialog">
            <div slot="title" style="display: flex; align-items: center;">
                <span style="font-weight: 400; font-size: 18px; color: #666666; line-height: 20px;">销售流程自定义设置</span>
            </div>
            <div class="contactConfig_box">
                <div class="radio_box">
                    <div style="display: flex; align-items: center;">
                        <span>可编辑此选项权限</span>
                        <el-radio-group v-model="radio">
                            <el-radio :label="3">仅管理员</el-radio>
                            <el-radio :label="6">所有人</el-radio>
                            <el-radio :label="9">指定人员</el-radio>
                        </el-radio-group>
                    </div>
                    <el-button type="text" icon="el-icon-plus" @click="handleAddSales">新建销售流程</el-button>
                </div>
                <div class="app-container">
                    <el-table :data="initialList" border :header-cell-style="{ 'text-align': 'center' }" class="tableDragBox">
                        <el-table-column label="序号/顺序" width="80" align="center" type="index"></el-table-column>
                        <el-table-column label="销售流程名称" align="center" show-overflow-tooltip>
                            <template slot-scope="{ row }">
                                <span type="text"> {{ row.data1 }} </span>
                            </template>
                        </el-table-column>
                        <el-table-column label="生效范围" align="center" show-overflow-tooltip>
                            <template slot-scope="{ row }">
                                <span type="text"> {{ row.data2 }} </span>
                            </template>
                        </el-table-column>
                        <el-table-column label="创建人" align="center" show-overflow-tooltip>
                            <template slot-scope="{ row }">
                                <span type="text"> {{ row.data3 }} </span>
                            </template>
                        </el-table-column>
                        <el-table-column label="创建时间" align="center" show-overflow-tooltip>
                            <template slot-scope="{ row }">
                                <span type="text"> {{ row.data4 }} </span>
                            </template>
                        </el-table-column>
                        <el-table-column label="操作" align="center" show-overflow-tooltip width="240">
                            <template slot-scope="{ row }">
                                <el-button type="primary" plain v-if="row.status === 1" size="mini" disabled>默认状态</el-button>
                                <el-button type="text" v-else>设为默认</el-button>
                                <el-button type="text" icon="el-icon-edit">编辑</el-button>
                                <el-button type="text" icon="el-icon-delete">删除</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </div>
            <div slot="footer">
                <button type="button" class="custom-dialog-btn" @click="open = false">取消</button>
                <button type="button" class="custom-dialog-btn primary" @click="open = false">确定</button>
            </div>
        </el-dialog>

        <addsalesprocess-dialog ref="addSalesProcess" />
    </div>
</template>

<script>
import addsalesprocessDialog from './addSalesProcess'
export default {
    components: { addsalesprocessDialog },
    data() {
        return {
            open: false,
            radio: 3,
            initialList: [
                {
                    dataId: "202404220001",
                    data1: "第一列数据1",
                    data2: true,
                    data3: 1,
                    data4: true,
                    data5: true,
                    status: 1
                },
                {
                    dataId: "202404220002",
                    data1: "第一列数据2",
                    data2: true,
                    data3: 1,
                    data4: true,
                    data5: true,
                    status: 0
                },
                {
                    dataId: "202404220003",
                    data1: "第一列数据3",
                    data2: true,
                    data3: 2,
                    data4: true,
                    data5: true,
                    status: 0
                },
                {
                    dataId: "202404220004",
                    data1: "第一列数据4",
                    data2: true,
                    data3: 1,
                    data4: true,
                    data5: true,
                    status: 0
                },
                {
                    dataId: "202404220005",
                    data1: "第一列数据5",
                    data2: true,
                    data3: 2,
                    data4: true,
                    data5: true,
                    status: 0
                },
            ],
        }
    },
    created() { },
    mounted() { },
    
    methods: {
        handleOpen() {
            this.open = true
        },
        handleAddSales() {
            this.$refs.addSalesProcess.handleAdd()            
        },
    },
}
</script>

<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';

.contactConfig_box {
    .radio_box {
        padding: 10px 20px;
        display: flex;
        align-items: center;
        justify-content: space-between;

        span {
            font-weight: 400;
            font-size: 12px;
            color: #999999;
            line-height: 20px;
            margin-right: 20px;
        }
    }
}

.tableTrBox {
    cursor: move !important;
    position: relative !important;
}
</style>