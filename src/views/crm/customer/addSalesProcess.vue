<template>
    <div>
        <el-dialog v-dialogDragBox :title="title" :visible.sync="open" width="1152px" class="custom-dialog">
            <div class="order_box">
                <div class="listInfo_tabs_box">
                    <div class="listInfo_tabs_left">
                        <el-tabs v-model="userTypeActive" @tab-click="handleTabItem">
                            <el-tab-pane label="基本设置" name="1"></el-tab-pane>
                            <el-tab-pane label="阶段设置" name="2"></el-tab-pane>
                            <el-tab-pane label="高级设置" name="3"></el-tab-pane>
                        </el-tabs>
                    </div>
                </div>
                <el-form ref="form" :model="form" :rules="rules" label-width="8em" label-position="left"
                    v-if="userTypeActive === '1'">
                    <el-row :gutter="20" style="display: flex; flex-wrap: wrap">
                        <el-col :span="12">
                            <el-form-item label="流程名称" prop="productName">
                                <el-input v-model="form.productName" placeholder="请输入流程名称" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label="生效范围" prop="categoryId">
                                <div style="display: flex; align-items: center;">
                                    <el-radio-group v-model="radio" style="display: flex; align-items: center;">
                                        <el-radio :label="1">全公司</el-radio>
                                        <el-radio :label="2">指定部门</el-radio>
                                        <el-radio :label="3">指定人员</el-radio>
                                    </el-radio-group>
                                    <el-tag closable :disable-transitions="false" @close="handleClose(tag)"
                                        style="margin-left: 30px;">某某某</el-tag>
                                    <el-button type="text" icon="el-icon-plus"
                                        style="margin-left: 15px;">选择人员</el-button>
                                </div>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <div style="padding: 0 20px 20px;">
                                <div
                                    style="display: flex; align-items: center; justify-content: space-between; padding-bottom: 8px; border-bottom: 1px solid #E1E2E2;">
                                    <div>流程阶段</div>
                                    <div
                                        style="font-weight: 400; font-size: 14px; color: #2E73F3; display: flex; align-items: center; cursor: pointer;">
                                        <img style="width: 20px; height: 20px; margin-right: 3px;"
                                            src="@/assets/images/add_blue.png" alt="">
                                        添加流程阶段
                                    </div>
                                </div>
                            </div>
                        </el-col>
                        <el-col :span="24">
                            <div class="form_table">
                                <el-form-item label="" label-width="0" prop="products">
                                    <el-table :data="form.products" style="width: 100%;" class="custom-table">
                                        <el-table-column prop="productName" label="阶段" align="center"
                                            show-overflow-tooltip>
                                            <template slot-scope="{ row }">
                                                <span>阶段1</span>
                                            </template>
                                        </el-table-column>
                                        <el-table-column prop="model" label="阶段名称" align="center" show-overflow-tooltip>
                                            <template slot-scope="{ row }">
                                                <el-input v-model="productName" placeholder="请输入阶段名称" />
                                            </template>
                                        </el-table-column>
                                        <el-table-column prop="model" label="赢率" align="center" show-overflow-tooltip>
                                            <template slot-scope="{ row }">
                                                <el-input v-model="productName" placeholder="请输入赢率">
                                                    <span slot="suffix" style="line-height: 40px;">%</span>
                                                </el-input>
                                            </template>
                                        </el-table-column>
                                        <el-table-column prop="model" label="状态" align="center" show-overflow-tooltip>
                                            <template slot-scope="{ row }">
                                                <span>进行中</span>
                                            </template>
                                        </el-table-column>
                                        <el-table-column label="操作" align="center">
                                            <template slot-scope="scope">
                                                <el-button type="text" size="mini" icon="el-icon-edit"
                                                    @click="handleDelete(scope.row)">编辑</el-button>
                                                <el-button type="text" size="mini" icon="el-icon-delete"
                                                    @click="handleDelete(scope.row)">删除</el-button>
                                            </template>
                                        </el-table-column>
                                    </el-table>
                                </el-form-item>
                            </div>
                        </el-col>
                        <el-col :span="24">
                            <div style="padding: 0 20px 20px;">
                                <div
                                    style="display: flex; align-items: center; justify-content: space-between; padding-bottom: 8px; border-bottom: 1px solid #E1E2E2;">
                                    <div>结束阶段</div>
                                    <div
                                        style="font-weight: 400; font-size: 14px; color: #2E73F3; display: flex; align-items: center; cursor: pointer;">
                                        <img style="width: 20px; height: 20px; margin-right: 3px;"
                                            src="@/assets/images/add_blue.png" alt="">
                                        添加结束阶段
                                    </div>
                                </div>
                            </div>
                        </el-col>
                        <el-col :span="24">
                            <div class="form_table">
                                <el-form-item label="" label-width="0" prop="products">
                                    <el-table :data="form.products" style="width: 100%;" class="custom-table">
                                        <el-table-column prop="productName" label="阶段" align="center"
                                            show-overflow-tooltip>
                                            <template slot-scope="{ row }">
                                                <span>结束1</span>
                                            </template>
                                        </el-table-column>
                                        <el-table-column prop="model" label="阶段名称" align="center" show-overflow-tooltip>
                                            <template slot-scope="{ row }">
                                                <el-input v-model="productName" placeholder="请输入阶段名称" />
                                            </template>
                                        </el-table-column>
                                        <el-table-column prop="model" label="赢率" align="center" show-overflow-tooltip>
                                            <template slot-scope="{ row }">
                                                <el-input v-model="productName" placeholder="请输入赢率">
                                                    <span slot="suffix" style="line-height: 40px;">%</span>
                                                </el-input>
                                            </template>
                                        </el-table-column>
                                        <el-table-column prop="model" label="状态" align="center" show-overflow-tooltip>
                                            <template slot-scope="{ row }">
                                                <el-select v-model="status">
                                                    <el-option label="赢单" :value="1"></el-option>
                                                    <el-option label="输单" :value="2"></el-option>
                                                    <el-option label="无效" :value="3"></el-option>
                                                </el-select>
                                            </template>
                                        </el-table-column>
                                        <el-table-column label="操作" align="center">
                                            <template slot-scope="scope">
                                                <el-button type="text" size="mini" icon="el-icon-edit"
                                                    @click="handleDelete(scope.row)">编辑</el-button>
                                                <el-button type="text" size="mini" icon="el-icon-delete"
                                                    @click="handleDelete(scope.row)">删除</el-button>
                                            </template>
                                        </el-table-column>
                                    </el-table>
                                </el-form-item>
                            </div>
                        </el-col>
                    </el-row>
                </el-form>
                <div v-if="userTypeActive === '2'">
                    <el-table :data="list" style="width: 100%;" class="custom-table">
                        <el-table-column label="阶段" align="center" show-overflow-tooltip width="100">
                            <template slot-scope="{ row }">
                                <span>{{ row.stage }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="阶段名称" align="center" show-overflow-tooltip>
                            <template slot-scope="{ row }">
                                <span>{{ row.name }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="阶段负责人" align="center" show-overflow-tooltip>
                            <template slot-scope="{ row }">
                                <el-tag closable :disable-transitions="false" @close="handleClose(tag)">某某某</el-tag>
                                <el-button type="text" icon="el-icon-plus" style="margin-left: 15px;">选择人员</el-button>
                            </template>
                        </el-table-column>
                        <el-table-column label="自动完成" align="center" show-overflow-tooltip>
                            <template slot-scope="{ row }">
                                <div style="display: flex; align-items: center;">
                                    <el-radio-group v-model="radio" style="display: flex; align-items: center;">
                                        <el-radio :label="1">手动</el-radio>
                                        <el-radio :label="2">自动</el-radio>
                                    </el-radio-group>
                                    <div @click="handleRuleSetting(row)"
                                        style="font-weight: 400; font-size: 14px; color: #666666; display: flex; align-items: center; cursor: pointer; margin-left: 15px;">
                                        <img style="width: 20px; height: 20px; margin-right: 3px;"
                                            src="@/assets/images/setting.png" alt="">
                                        规则设置
                                    </div>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column label="操作" align="center">
                            <template slot-scope="scope">
                                <el-button type="text" size="mini" icon="el-icon-edit"
                                    @click="handleEdit(scope.row)">编辑</el-button>
                                <el-button type="text" size="mini">恢复默认</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </div>
            <div slot="footer">
                <button type="button" class="custom-dialog-btn" @click="handleCancel">取消</button>
                <button type="button" class="custom-dialog-btn primary" @click="handleSumit">确定</button>
            </div>
        </el-dialog>

        <contactconfig-dialog ref="contactConfig" />
        <rulesetting-dialog ref="ruleSetting" />
        <editcontent-dialog ref="editContent" />
    </div>
</template>

<script>
import contactconfigDialog from './contactConfig'
import rulesettingDialog from './ruleSetting'
import editcontentDialog from './editContent'
export default {
    name: 'AddSalesProcess',
    components: { contactconfigDialog, rulesettingDialog, editcontentDialog },
    data() {
        return {
            title: '新建销售流程',
            form: {
                products: [{
                    productName: '某某产品',
                    model: 'DN150*168*40*40',
                    quantity: undefined
                }]
            },
            rules: {},
            open: false,
            loading: false,
            fileType: ['png', 'PNG', 'jpg', 'JPG', 'jpeg', 'JPEG', 'pdf'],
            key: 1,
            radio: undefined,
            options: [],
            activeName: 'second',
            value: undefined,
            unitOptions: [],
            userTypeActive: '1',
            status: 1,
            input1: '',
            list: [{
                stage: '阶段1',
                name: '验证客户',

            }],
            productName: ''
        }
    },
    created() {

    },
    methods: {
        reset() {
            this.form = {
                products: [{
                    productName: '某某产品',
                    model: 'DN150*168*40*40',
                    quantity: undefined
                }]
            }
            this.resetForm('form')
        },
        handleAdd() {
            this.reset()
            this.title = '新建销售流程'
            this.open = true
        },
        // 取消
        handleCancel() {
            this.open = false
            this.reset()
        },
        // 提交
        handleSumit() {
            this.$refs['form'].validate(valid => {
                if (valid) {
                    if (this.form.id != null) {
                        this.form.productId = this.form.id
                        updateUnsalable(this.form).then(res => {
                            if (res.code === 200) {
                                this.$message.success('修改成功')
                                this.open = false
                                this.$parent.getList()
                            } else {
                                this.$message.error(res.msg)
                            }
                        })
                    } else {
                        addUnsalable(this.form).then(res => {
                            if (res.code === 200) {
                                this.$message.success('新增成功')
                                this.open = false
                                this.$parent.getList()
                            } else {
                                this.$message.error(res.msg)
                            }
                        })
                    }
                }
            })
        },
        handleAllSelect() { },
        handleClick() { },
        handleOpen() { },
        handleClose() { },
        handleTabItem() { },
        handleChange() { },
        handleContactConfig() {
            this.$refs.contactConfig.handleOpen()
        },
        handleSalesProcess() {
            this.$refs.salesProcess.handleOpen()
        },
        handleRuleSetting(row) {
            this.$refs.ruleSetting.handleOpen()
        },
        handleEdit(row) {
            this.$refs.editContent.handleOpen()
        },
    }
}
</script>
<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';

.ipt {
    ::v-deep {
        .el-input__inner {
            cursor: pointer;
        }
    }
}

.order_box {
    padding: 0;

    ::v-deep {
        .el-form-item {
            margin-left: 20px;
        }

        .el-form-item__content {
            padding-right: 20px;
        }

        .el-divider--vertical {
            height: 2em;
        }
    }

    .border_no {
        ::v-deep {
            .el-input__inner {
                border: none;
            }
        }
    }
}

.customer_list_box {
    width: 444px;
    height: 538px;
    background: #FFFFFF;
    box-shadow: 0px 1px 15px 0px rgba(0, 0, 0, 0.25);
    border-radius: 5px;
    border: 1px solid #2E73F3;

    ::v-deep {
        .el-tabs__header {
            margin-bottom: 0;

            .el-tabs__nav-wrap {
                padding: 0 15px;
            }
        }
    }

    .dept_box {
        display: flex;

        .dept_left {
            // width: 164px;
        }

        .dept_right {
            // flex: 1;
        }
    }
}

.listInfo_tabs_box {
    margin: 0 20px 20px;
}

.next_follow_box {
    background: #F0F3F9;
    padding: 20px;
    margin-bottom: 20px;
}

.time_box {
    ::v-deep {
        .el-select {
            .el-input--suffix {
                height: 32px;

                .el-input__inner {
                    height: 32px;
                }

                .el-input__icon {
                    line-height: 32px;
                }
            }
        }
    }
}
</style>
