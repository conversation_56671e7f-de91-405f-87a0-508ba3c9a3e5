<template>
  <div>
    <el-dialog v-dialogDragBox :title="title" :visible.sync="open" width="1150px" class="custom-dialog" :before-close="handleClose">
      <el-form ref="form" :model="form" :rules="rules" label-position="left" label-width="105px">
        <div class="pl-20 pr-20">
          <div class="create-tabs">
            <div class="create-tabs-info">
              <div class="create-tabs-item" :class="{ active: form.typesOf === item.value }" v-for="item in typesOfOptions"
                :key="item.value" @click="handleTabClick(item)">{{ item.label }}</div>
            </div>
            <el-popover trigger="click" popper-class="crm-popover">
              <template slot="reference">
                <div class="create-config">
                  <i class="el-icon-setting"></i>
                  <span>自定义</span>
                </div>
              </template>
              <div class="create-user-config-title">新增客户设置</div>
              <div class="create-user-config-item" v-for="item in typesOfOptions" :key="item.value">
                <span>{{ `显示${item.name}选项` }}</span>
                <el-switch v-model="item.isActive"></el-switch>
              </div>
              <div class="create-user-config-sort">
                <span>默认在前首位显示</span>
                <el-radio-group v-model="typesOfSort">
                  <el-radio v-for="item in typesOfOptions" :key="item.value" :label="item.sort">{{ item.name
                  }}</el-radio>
                </el-radio-group>
              </div>
            </el-popover>
          </div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="负责人" prop="linkUsers">
                <el-tag closable @close="handleCloseTag(item)" class="crm-tag border-none"
                  v-for="item in form.linkUsers.filter(i => i.typesOf === 'fz')" :key="item.userId">{{ item.realName ||
                    item.nickName }}</el-tag>
                <el-button type="text" icon="el-icon-plus" style="margin-left: 15px"
                  @click="handleContactShow('responsibility', form.linkUsers.filter(i => i.typesOf === 'fz'))">选择人员</el-button>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="参与人" prop="participants">
                <el-tag closable @close="handleCloseTag(item)" class="crm-tag border-none"
                  v-for="item in form.linkUsers.filter(i => i.typesOf === 'cy')" :key="item.userId">{{ item.realName ||
                    item.nickName }}</el-tag>
                <el-button type="text" icon="el-icon-plus" style="margin-left: 15px"
                  @click="handleContactShow('participate', form.linkUsers.filter(i => i.typesOf === 'cy'))">选择人员</el-button>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="客户获得时间" prop="obtainTime">
                <el-date-picker v-model="form.obtainTime" type="datetime" placeholder="选择日期"
                  value-format="yyyy-MM-dd HH:mm:ss" style="width: 100%"></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <div class="create-user-form-title">公司信息</div>
            </el-col>
            <el-col :span="12">
              <el-form-item label="客户名称" prop="name">
                <el-input v-model="form.name" placeholder="请输入客户名称" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="所在城市" prop="city">
                <el-cascader v-model="form.city" :options="cityOptions" placeholder="请选择所在城市"
                  :props="{ expandTrigger: 'hover' }" clearable style="width: 100%"></el-cascader>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="电话是否核实" prop="isVerifyPhone">
                <div class="flex align-center">
                  <el-radio-group v-model="form.isVerifyPhone">
                    <el-radio :label="1">已核实</el-radio>
                    <el-radio :label="0">未核实</el-radio>
                  </el-radio-group>
                  <div class="ml-30 flex1">
                    <el-input v-model="form.remark" placeholder="请输入备注信息" />
                  </div>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <div class="create-user-form-table">
          <div class="create-user-form-table-title">
            <span>成交产品</span>
            <el-button type="primary" plain size="small" icon="el-icon-plus" class="ml-50"
              @click="handleProductAdd">添加产品</el-button>
          </div>
          <el-table :data="form.linkProducts" stripe class="custom-table" style="width: 100%">
            <el-table-column type="index" label="序号" width="50" align="center"></el-table-column>
            <el-table-column prop="productName" label="产品名称" align="center" show-overflow-tooltip>
              <template slot-scope="{ row }">
                <span class="table-link" @click="handleView(row.id, row)">{{ row.productName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="图片" width="70" align="center">
              <template slot-scope="{ row }">
                <el-image :src="formatProductImg(row)" fit="cover" @click="handleImgView(row)" :width="50" :height="50">
                  <div slot="error" class="image-slot">
                    <i class="el-icon-picture-outline"></i>
                  </div>
                </el-image>
              </template>
            </el-table-column>
            <el-table-column prop="productCode" label="产品编码" align="center" show-overflow-tooltip></el-table-column>
            <el-table-column prop="model" label="产品型号" align="center" show-overflow-tooltip></el-table-column>
            <el-table-column prop="materialQuality" label="材质" align="center" show-overflow-tooltip></el-table-column>
            <el-table-column prop="unit" label="单位" align="center" width="80"></el-table-column>
            <el-table-column label="操作" align="center" width="120">
              <template slot-scope="{ $index }">
                <el-button class="table-btn danger" icon="el-icon-delete" @click="handleDelete($index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="crm-table-total">
            <div class="crm-table-total-item">
              <span>共</span>
              <span class="primary ml-5 mr-5">{{ form.linkProducts.length }}</span>
              <span>个产品</span>
            </div>
          </div>
          <div class="mt-15 pb-20">
            <el-button type="primary" size="small" icon="el-icon-plus" @click="handleProductAdd">添加产品</el-button>
          </div>
        </div>
        <div class="pl-20 pr-20">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="客户类别" prop="type">
                <div class="create-user-form-item">
                  <el-select v-model="form.type" placeholder="请选择客户类别">
                    <el-option v-for="item in customerTypeData" :key="item.name" :label="item.name"
                      :value="item.name"></el-option>
                  </el-select>
                  <el-popover ref="customerTypePopover" trigger="click" popper-class="crm-popover"
                    @after-leave="customerTypeShow = false">
                    <div class="create-config" slot="reference" @click="customerTypeShow = true">
                      <i class="el-icon-edit-outline"></i>
                      <span>编辑</span>
                    </div>
                    <config-table title="客户类别" :config-data="customerTypeData" config-type="Customer_Type"
                      @close="handleClosePopover('customerTypePopover')"
                      @update="handleUpdateConfigData('customerTypeData', $event)"
                      v-if="customerTypeShow"></config-table>
                  </el-popover>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="客户状态" prop="status">
                <div class="create-user-form-item">
                  <el-select v-model="form.status" placeholder="请选择客户状态">
                    <el-option v-for="item in customerStatusData" :key="item.name" :label="item.name"
                      :value="item.name"></el-option>
                  </el-select>
                  <el-popover ref="customerStatusPopover" trigger="click" popper-class="crm-popover"
                    @after-leave="customerStatusShow = false">
                    <template slot="reference">
                      <div class="create-config" @click="customerStatusShow = true">
                        <i class="el-icon-edit-outline"></i>
                        <span>编辑</span>
                      </div>
                    </template>
                    <config-table title="客户状态" :config-data="customerStatusData" config-type="Customer_Status"
                      @close="handleClosePopover('customerStatusPopover')"
                      @update="handleUpdateConfigData('customerStatusData', $event)"
                      v-if="customerStatusShow"></config-table>
                  </el-popover>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="客户等级" prop="level">
                <div class="create-user-form-item">
                  <el-select v-model="form.level" placeholder="请选择客户等级">
                    <el-option v-for="item in customerLevelData" :key="item.name" :label="item.name"
                      :value="item.name"></el-option>
                  </el-select>
                  <el-popover ref="customerLevelPopover" trigger="click" popper-class="crm-popover"
                    @after-leave="customerLevelShow = false">
                    <template slot="reference">
                      <div class="create-config" @click="customerLevelShow = true">
                        <i class="el-icon-edit-outline"></i>
                        <span>编辑</span>
                      </div>
                    </template>
                    <config-table title="客户等级" :config-data="customerLevelData" config-type="Customer_Level"
                      @close="handleClosePopover('customerLevelPopover')"
                      @update="handleUpdateConfigData('customerLevelData', $event)"
                      v-if="customerLevelShow"></config-table>
                  </el-popover>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="客户来源" prop="source">
                <div class="create-user-form-item">
                  <el-select v-model="form.source" placeholder="请选择客户来源">
                    <el-option v-for="item in customerSourceData" :key="item.name" :label="item.name"
                      :value="item.name"></el-option>
                  </el-select>
                  <el-popover ref="customerSourcePopover" trigger="click" popper-class="crm-popover"
                    @after-leave="customerSourceShow = false">
                    <template slot="reference">
                      <div class="create-config" @click="customerSourceShow = true">
                        <i class="el-icon-edit-outline"></i>
                        <span>编辑</span>
                      </div>
                    </template>
                    <config-table title="客户来源" :config-data="customerSourceData" config-type="Customer_Source"
                      @close="handleClosePopover('customerSourcePopover')"
                      @update="handleUpdateConfigData('customerSourceData', $event)"
                      v-if="customerSourceShow"></config-table>
                  </el-popover>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="公司地址" prop="address">
                <el-input v-model="form.address" type="textarea" resize="none" :rows="3"
                  placeholder="请输入公司地址"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="企业微信" prop="isAddWx">
                <div class="flex align-center">
                  <el-radio-group v-model="form.isAddWx">
                    <el-radio :label="1">已加</el-radio>
                    <el-radio :label="0">未加</el-radio>
                  </el-radio-group>
                  <div class="ml-30 flex1">
                    <el-input v-model="form.wxNumber" placeholder="请输入企业微信" />
                  </div>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <div class="create-user-form-title">
                <span>联系人信息</span>
                <div class="create-config" @click="handleContactConfig">
                  <i class="el-icon-setting"></i>
                  <span>自定义</span>
                </div>
              </div>
            </el-col>
            <el-col :span="12" v-for="(item, index) in form.filedValueList" :key="item.id">
              <el-form-item :label="item.filedName"
                :rules="[{ required: item.isRequired == 1 ? true : false, message: `请输入${item.filedName}`, trigger: 'blur' }]"
                :prop="`filedValueList[${index}].filedValue`">
                <el-input v-model="item.filedValue" :placeholder="`请输入${item.filedName}`">
                  <!-- <template slot="suffix" v-if="item.isRepeat">
                    <div class="create-user-form-suffix">
                      <el-button type="text" icon="el-icon-plus">{{ '添加' + item.filedName }}</el-button>
                    </div>
                  </template> -->
                </el-input>
              </el-form-item>
            </el-col>
            <!-- <el-col :span="12">
              <el-form-item label="联系人邮箱" prop="email">
                <el-input v-model="form.email" placeholder="请输入联系人邮箱" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="联系人手机" prop="contactPhone">
                <el-input v-model="form.contactPhone" placeholder="请输入联系人手机号">
                  <template slot="suffix">
                    <div class="create-user-form-suffix">
                      <el-button type="text" icon="el-icon-plus">添加手机号</el-button>
                    </div>
                  </template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="公司座机" prop="companyTel">
                <el-input v-model="form.companyTel" placeholder="请输入公司座机号码">
                  <template slot="suffix">
                    <div class="create-user-form-suffix">
                      <el-button type="text" icon="el-icon-plus">添加座机号</el-button>
                    </div>
                  </template>
                </el-input>
              </el-form-item>
            </el-col> -->
            <!-- <el-col :span="24">
              <div class="flex align-center mb-20">
                <el-switch v-model="model" active-color="#2E74F3" inactive-color="#D5DAE4"></el-switch>
                <span class="ml-15">同步创建跟进记录</span>
              </div>
            </el-col>
            <el-col :span="24">
              <div class="create-user-form-table triangle">
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="下次跟进时间" prop="nextFollowTime">
                      <el-date-picker v-model="form.nextFollowTime" type="datetime" placeholder="选择下次跟进时间"
                        style="width: 100%"></el-date-picker>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="负责人" prop="owner">
                      <el-tag closable @close="handleCloseTag(item)" class="crm-tag">某某某</el-tag>
                      <el-button type="text" icon="el-icon-plus" style="margin-left: 15px">选择人员</el-button>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="指定跟进人" prop="follower">
                      <el-radio-group v-model="form.follower">
                        <el-radio :label="1">客户负责人</el-radio>
                        <el-radio :label="2">创建人/我自己</el-radio>
                        <el-radio :label="3">指定人员</el-radio>
                      </el-radio-group>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="任务说明" prop="taskDesc">
                      <el-input type="textarea" v-model="form.taskDesc" resize="none" :rows="4"
                        placeholder="请输入任务说明"></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
            </el-col>
            <el-col :span="24">
              <div class="flex align-center mb-20">
                <el-switch v-model="model" active-color="#2E74F3" inactive-color="#D5DAE4"></el-switch>
                <span class="ml-15">为下次跟进创建任务</span>
              </div>
            </el-col>
            <el-col :span="24">
              <div class="create-user-form-table triangle">
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="下次跟进时间" prop="nextFollowTime">
                      <el-date-picker v-model="form.nextFollowTime" type="datetime" placeholder="选择下次跟进时间"
                        style="width: 100%"></el-date-picker>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="负责人" prop="owner">
                      <el-tag closable @close="handleCloseTag(item)" class="crm-tag">某某某</el-tag>
                      <el-button type="text" icon="el-icon-plus" style="margin-left: 15px">选择人员</el-button>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="指定跟进人" prop="follower">
                      <el-radio-group v-model="form.follower">
                        <el-radio :label="1">客户负责人</el-radio>
                        <el-radio :label="2">创建人/我自己</el-radio>
                        <el-radio :label="3">指定人员</el-radio>
                      </el-radio-group>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="任务说明" prop="taskDesc">
                      <el-input type="textarea" v-model="form.taskDesc" resize="none" :rows="4"
                        placeholder="请输入任务说明"></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
            </el-col> -->
            <el-col :span="24">
              <div class="flex align-center mb-20">
                <el-switch v-model="atShow" active-color="#2E74F3" inactive-color="#D5DAE4"></el-switch>
                <span class="ml-15">@其他成员</span>
              </div>
              <div class="create-user-form-table triangle" v-if="atShow">
                <el-row :gutter="20">
                  <el-col :span="24">
                    <el-form-item label="@成员" prop="other">
                      <el-tag closable @close="handleCloseTag(item)" class="crm-tag border-none"
                        v-for="item in form.linkUsers.filter(i => i.typesOf === 'at')" :key="item.userId">{{
                          item.realName ||
                          item.nickName }}</el-tag>
                      <el-button type="text" icon="el-icon-plus" style="margin-left: 15px"
                        @click="handleContactShow('other', form.linkUsers.filter(i => i.typesOf === 'at'))">选择人员</el-button>
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
            </el-col>
          </el-row>
        </div>
        <div class="ml-20 mr-20" style="height: 1px; background-color: #e2e6f3"></div>
      </el-form>
      <div slot="footer">
        <el-button class="custom-dialog-btn" @click="handleCancel">取消</el-button>
        <el-button class="custom-dialog-btn primary" @click="handleSubmit">确定</el-button>
      </div>
    </el-dialog>

    <customize-config ref="contactConfig" customize-type="Contacts_type"
      @update="handleUpdateContacts('contactsData')"></customize-config>

    <contacts :title="contactsTitle" :type="contactsType" ref="contactsView" @update="handleUpdateContactsList($event)">
    </contacts>

    <!--添加产品--选择产品-->
    <check-product ref="checkProduct" @confirm="handleConfirm" />
    <!-- 产品详情 -->
    <product-dialog ref="productInfo"></product-dialog>

  </div>
</template>
<script>
import ConfigTable from './config'
import { getCrmDict, getCrmDictContact } from '@/api/crm/index'
import { editCrmCustomer } from '@/api/crm/customer'
import CustomizeConfig from './customizeConfig'
import contacts from './contacts'
import CheckProduct from './product'
import ProductDialog from '@/views/public/product/dialog'
import { getProduct } from '@/api/system/product'
import { getPrivateduct } from '@/api/system/privateduct'


export default {
  components: { ConfigTable, CustomizeConfig, contacts, CheckProduct, ProductDialog },
  data() {
    return {
      open: false,
      title: '添加新客户',
      typesOfSort: 1,
      typesOfOptions: [
        { label: '新建企业客户', name: '企业客户', value: 'e', sort: 1, isActive: true },
        { label: '新建个人客户', name: '个人客户', value: 'p', sort: 2, isActive: false }
      ],
      form: {},
      rules: {
        city: [{ required: true, message: '请选择城市', trigger: ['blur', 'change'] }],
        filedValueList: [{ required: true, message: '联系人信息不能为空', trigger: ['blur', 'change'] }],
        level: [{ required: true, message: '请选择客户等级', trigger: ['blur', 'change'] }],
        linkUsers: [{ required: true, message: '请选择负责人', trigger: ['blur', 'change'] }],
        name: [{ required: true, message: '请输入客户名称', trigger: 'blur' }],
        source: [{ required: true, message: '请选择客户来源', trigger: ['blur', 'change'] }],
        status: [{ required: true, message: '请选择客户状态', trigger: ['blur', 'change'] }],
        type: [{ required: true, message: '请选择客户类别', trigger: ['blur', 'change'] }],
        typesOf: [{ required: true, message: '请选择客户类型', trigger: ['blur', 'change'] }]
      },
      cityOptions: [],
      model: false,
      // 客户类别设置
      customerTypeData: [],
      customerStatusData: [],
      customerLevelData: [],
      customerSourceData: [],
      customerTypeShow: false,
      customerStatusShow: false,
      customerLevelShow: false,
      customerSourceShow: false,
      configTypeList: ['Customer_Type', 'Customer_Status', 'Customer_Source', 'Customer_Level'],
      contactsFieldData: [],
      contactsTitle: '负责人',
      contactsType: 'fz',
      atShow: false
    }
  },
  created() {
    this.reset()
    this.getConfigList()
    this.getContactConfigList()
    this.cityOptions = this.$store.state.user.cityoptions
  },
  methods: {
    // 表单重置
    reset() {
      this.form = {
        filedValueList: [],
        linkUsers: [],
        linkProducts: [],
        // crmTask: {},
        isAddWx: 0,
        isClearProduct: 0,
        isVerifyPhone: 0,
        obtainTime: undefined,
        name: undefined,
        address: undefined,
        birthday: undefined,
        city: undefined,
        email: undefined,
        level: undefined,
        phone: undefined,
        remark: undefined,
        source: undefined,
        status: undefined,
        type: undefined,
        typesOf: 'e',
        workInfo: undefined,
        wxNumber: undefined,
      }
      this.resetForm('form')
    },
    // 获取字典列表
    getConfigList() {
      this.customerTypeData = []
      this.customerStatusData = []
      this.customerLevelData = []
      this.customerSourceData = []
      this.configTypeList.map(async item => {
        let res = await getCrmDict({ dictType: item })
        if (res.code == 200 && res.data) {
          if (res.data.dictType === 'Customer_Type') {
            this.customerTypeData = res.data.dataList
          }
          if (res.data.dictType === 'Customer_Status') {
            this.customerStatusData = res.data.dataList
          }
          if (res.data.dictType === 'Customer_Source') {
            this.customerSourceData = res.data.dataList
          }
          if (res.data.dictType === 'Customer_Level') {
            this.customerLevelData = res.data.dataList
          }
        }
      })
    },
    getContactConfigList() {
      getCrmDictContact().then(res => {
        if (res.code == 200 && res.data) {
          this.form.filedValueList = []
          res.data.liaisonDataList.filter(item => item.isEnable == 1).forEach(el => {
            let obj = {
              ...el,
              filedValue: '',
              liaisonId: el.id,
            }
            this.form.filedValueList.push(obj)
          })
          this.arraySort(this.form.filedValueList, 'sort')
        }
      })
    },
    // 打开新增
    handleOpen() {
      this.open = true
    },
    // 排序
    arraySort(arr, key) {
      return arr.sort((a, b) => a[key] - b[key])
    },
    // 切换
    handleTabClick(item) {
      this.form.typesOf = item.value
    },
    // 关闭窗口
    handleClose() {
      this.handleCancel()
    },
    // 取消操作
    handleCancel(flag = false) {
      this.open = false
      this.$emit('callback', false)
    },
    // 关闭Popover弹窗
    handleClosePopover(name) {
      this.$refs[name].doClose()
    },
    // 更新配置数据
    handleUpdateConfigData(name, data) {
      if (name === 'customerTypeData') {
        this.customerTypeData = JSON.parse(JSON.stringify(data))
        const obj = this.customerTypeData.find(item => item.name === this.form.type)
        if (!obj) this.form.type = ''
      } else if (name === 'customerStatusData') {
        this.customerStatusData = JSON.parse(JSON.stringify(data))
        const obj = this.customerStatusData.find(item => item.name === this.form.status)
        if (!obj) this.form.status = ''
      } else if (name === 'customerLevelData') {
        this.customerLevelData = JSON.parse(JSON.stringify(data))
        const obj = this.customerLevelData.find(item => item.name === this.form.level)
        if (!obj) this.form.level = ''
      } else if (name === 'customerSourceData') {
        this.customerSourceData = JSON.parse(JSON.stringify(data))
        const obj = this.customerSourceData.find(item => item.name === this.form.source)
        if (!obj) this.form.source = ''
      }
    },
    // 提交操作
    handleSubmit() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          console.log(this.form)
          let obj = {
            ...this.form,
            city: this.form.city.join('/'),
          }
          editCrmCustomer(obj).then(res => {
            if (res.code == 200) {
              this.$message({
                type: 'success',
                message: '编辑成功!'
              })
              // 关闭弹窗
              this.handleClose()
            }
          })
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    // 打开联系人自定义配置
    handleContactConfig() {
      this.$refs.contactConfig.handleOpen()
    },
    handleUpdateContacts() {
      this.getContactConfigList()
    },
    handleContactShow(type, arr) {
      if (type == 'responsibility') {
        this.contactsTitle = '负责人'
        this.contactsType = 'fz'
      }
      if (type == 'participate') {
        this.contactsTitle = '参与人'
        this.contactsType = 'cy'
      }
      if (type == 'other') {
        this.contactsTitle = '其他人'
        this.contactsType = 'at'
      }
      let checkList = []
      arr.forEach(el => {
        checkList.push(el.userId)
      });
      console.log(checkList)
      this.$refs.contactsView.handleOpen(checkList)
    },
    handleUpdateContactsList(data) {
      // console.log(data)
      let type = data && data[0].typesOf
      if (type === 'fz') {
        this.form.linkUsers = this.form.linkUsers.filter(item => item.typesOf !== 'fz').concat(data)
      }
      if (type === 'cy') {
        this.form.linkUsers = this.form.linkUsers.filter(item => item.typesOf !== 'cy').concat(data)
      }
      if (type === 'at') {
        this.form.linkUsers = this.form.linkUsers.filter(item => item.typesOf !== 'at').concat(data)
      }
    },
    handleCloseTag(item) {
      this.form.linkUsers.splice(this.form.linkUsers.findIndex(i => i.typesOf === item.typesOf && i.userId == item.userId), 1)
    },
    // 添加产品
    handleProductAdd() {
      const arr = this.form.linkProducts
      this.$refs.checkProduct.openProduct(arr)
    },
    // 确认选择产品
    handleConfirm(arr = []) {
      const newArr = arr.map(item => {
        return { ...item, productId: item.id, quantity: undefined }
      })
      const oldArr = this.form.linkProducts.filter(item => item.source !== newArr[0].source) || []
      this.form.linkProducts = [...oldArr, ...newArr]
    },
    delProduct(index) {
      this.form.linkProducts.splice(index, 1)
    },
    // 产品详情
    handleView(Id, row) {
      if (row.source === 'common') {
        getProduct(Id).then(res => {
          this.$refs.productInfo.handleView(res.data)
        })
      } else {
        getPrivateduct(Id).then(res => {
          this.$refs.productInfo.handleView(res.data)
        })
      }
    },
    // 图片详情
    handleImgView(row) {
      this.$refs.productInfo.handleImgView(row)
    },
  }
}
</script>
<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';
@import '~@/assets/styles/crm.scss';
.mt-15 {
  margin-top: 15px;
}
.pb-20 {
  padding-bottom: 20px;
}
.mr-5 {
  margin-right: 5px;
}
.mb-20 {
  margin-bottom: 20px;
}
.ml-30 {
  margin-left: 30px;
}
.ml-50 {
  margin-left: 50px;
}
.ml-5 {
  margin-left: 5px;
}
.ml-15 {
  margin-left: 15px;
}
.ml-20 {
  margin-left: 20px;
}
.pr-20 {
  padding-right: 20px;
}
.pl-20 {
  padding-left: 20px;
}
.mr-20 {
  margin-right: 20px;
}
</style>
