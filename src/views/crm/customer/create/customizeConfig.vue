<template>
    <div>
        <el-dialog v-dialogDragBox :visible.sync="open" width="1152px" class="custom-dialog">
            <div slot="title" style="display: flex; align-items: center;">
                <span style="font-weight: 400; font-size: 18px; color: #666666; line-height: 20px;">联系人录入信息自定义设置</span>
                <span
                    style="margin-left: 50px; font-weight: 400; font-size: 12px; color: #999999; line-height: 20px;">注：支持拖动排序</span>
            </div>
            <div class="contactConfig_box">
                <div class="radio_box">
                    <span>可编辑此选项权限</span>
                    <el-radio-group v-model="purview">
                        <el-radio label="admin">仅管理员</el-radio>
                        <el-radio label="all">所有人</el-radio>
                        <el-radio label="assign">指定人员</el-radio>
                    </el-radio-group>
                </div>
                <div class="app-container">
                    <el-table :data="liaisonDataList" border :header-cell-style="{ 'text-align': 'center' }"
                        :row-class-name="activeClass" class="tableDragBox">
                        <el-table-column label="序号/顺序" width="80" align="center" type="index"></el-table-column>
                        <el-table-column label="录入信息名称" align="center" show-overflow-tooltip>
                            <template slot-scope="{ row }">
                                <el-input type="text" v-model="row.filedName" placeholder="请输入录入信息名称"></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column label="开启/关闭" align="center" show-overflow-tooltip>
                            <template slot-scope="{ row }">
                                <el-switch v-model="row.isEnable" active-color="#2E73F3" inactive-color="#ff4949"
                                    :active-value="1" :inactive-value="0">
                                </el-switch>
                            </template>
                        </el-table-column>
                        <el-table-column label="输入框类型" align="center" show-overflow-tooltip>
                            <template slot-scope="{ row }">
                                <el-select v-model="row.filedType" placeholder="请选择输入框类型">
                                    <el-option label="小数点输入框(decimal)" value="decimal"></el-option>
                                    <el-option label="长文本输入框(multiText)" value="multiText"></el-option>
                                    <el-option label="数字输入框(number)" value="number"></el-option>
                                    <el-option label="短文本输入框(string)" value="string"></el-option>
                                    <el-option label="文本输入框(text)" value="text"></el-option>
                                </el-select>
                            </template>
                        </el-table-column>
                        <el-table-column label="是否必填" align="center" show-overflow-tooltip>
                            <template slot-scope="{ row }">
                                <el-switch v-model="row.isRequired" active-color="#2E73F3" inactive-color="#ff4949"
                                    :active-value="1" :inactive-value="0">
                                </el-switch>
                            </template>
                        </el-table-column>
                        <el-table-column label="可否重复" align="center" show-overflow-tooltip>
                            <template slot-scope="{ row }">
                                <el-switch v-model="row.isRepeat" active-color="#2E73F3" inactive-color="#ff4949"
                                    :active-value="1" :inactive-value="0">
                                </el-switch>
                            </template>
                        </el-table-column>
                    </el-table>

                    <div style="margin-top: 20px;">
                        <el-button type="primary" icon="el-icon-plus" plain @click="handleAdd">新建录入信息</el-button>
                    </div>
                </div>
            </div>
            <div slot="footer">
                <button type="button" class="custom-dialog-btn" @click="open = false">恢复默认设置</button>
                <button type="button" class="custom-dialog-btn" @click="handleClose">取消</button>
                <button type="button" class="custom-dialog-btn primary" @click="handleConfirm">确定</button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { getCrmDictContact, editCrmDictContact } from '@/api/crm/index'
export default {
    data() {
        return {
            open: false,
            liaisonDataList: [],
            // 表格表头
            dragIndex: null,
            newDragIndex: null,
            purview: '',
            authUserList: [],
            dictType: 'Customer_Liaison'
        }
    },
    created() { },
    mounted() { },
    watch: {
        liaisonDataList() {
            this.$nextTick(() => {
                this.watchTable();
            });
        },
    },
    methods: {
        // 表格默认数据
        setDefault() {
            this.liaisonDataList = [{
                filedName: '联系人姓名',
                filedType: 'string',
                isEnable: 1,
                isRepeat: 1,
                isRequired: 1,
                sort: 1
            }, {
                filedName: '联系人邮箱',
                filedType: 'string',
                isEnable: 1,
                isRepeat: 1,
                isRequired: 1,
                sort: 2
            }, {
                filedName: '联系人手机号',
                filedType: 'multiText',
                isEnable: 1,
                isRepeat: 1,
                isRequired: 1,
                sort: 3
            }, {
                filedName: '公司座机',
                filedType: 'multiText',
                isEnable: 1,
                isRepeat: 1,
                isRequired: 1,
                sort: 4
            },]
            this.purview = 'admin'
            this.authUserList = []
            this.dictType = 'Customer_Liaison'
        },
        // 监听表格
        watchTable() {
            const dragBox = document.querySelectorAll(".tableDragBox .tableTrBox");
            dragBox.forEach((i, idx) => {
                i.setAttribute("draggable", "true");
                i.ondragstart = () => this.dragStartItem(idx);
                i.ondragover = () => this.dragOverItem(idx);
                i.ondragend = () => this.dragEndItem();
            });
        },
        // 表格行添加class
        activeClass() {
            return "tableTrBox";
        },
        // 拖拽开始-记录起始index
        dragStartItem(idx) {
            this.dragIndex = idx;
        },
        // 拖拽结束-记录目标index
        dragOverItem(index) {
            this.newDragIndex = index;
        },
        // 拖拽完成-整理表格数据
        dragEndItem() {
            const data = this.liaisonDataList[this.dragIndex];
            this.liaisonDataList.splice(this.dragIndex, 1);
            this.liaisonDataList.splice(this.newDragIndex, 0, data);
        },
        handleOpen() {
            this.open = true
            this.$nextTick(() => {
                this.watchTable();
                getCrmDictContact().then(res => {
                    if (res.code == 200 && res.data) {
                        this.liaisonDataList = res.data.liaisonDataList
                        this.liaisonDataList.sort((a, b) => a.sort - b.sort)
                        this.purview = res.data.purview
                        this.authUserList = res.data.authUserList
                        this.dictType = res.data.dictType
                    } else {
                        this.setDefault()
                    }
                })
            });
        },
        handleAdd() {
            let num = this.liaisonDataList.length + 1
            this.liaisonDataList.push({
                filedName: '',
                filedType: '',
                isEnable: 0,
                isRepeat: 0,
                isRequired: 0,
                sort: num
            })
        },
        handleConfirm() {
            let obj = {
                authUserList: this.authUserList,
                dictType: this.dictType,
                liaisonDataList: this.liaisonDataList,
                purview: this.purview
            }
            editCrmDictContact(obj).then(res => {
                if (res.code == 200) {
                    this.$message({
                        type: 'success',
                        message: '编辑成功!'
                    })
                    this.$emit('update')
                    // 关闭弹窗
                    this.handleClose()
                }
            })
        },
        // 关闭
        handleClose() {
            this.open = false
        },
    },
}
</script>

<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';

.contactConfig_box {
    .radio_box {
        padding: 10px 20px;
        display: flex;
        align-items: center;

        span {
            font-weight: 400;
            font-size: 12px;
            color: #999999;
            line-height: 20px;
            margin-right: 20px;
        }
    }
}

.tableTrBox {
    cursor: move !important;
    position: relative !important;
}
</style>