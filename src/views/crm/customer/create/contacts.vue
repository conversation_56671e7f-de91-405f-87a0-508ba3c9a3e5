<template>
    <div>
        <el-dialog v-dialogDragBox :title="'设置' + title" :visible.sync="open" width="750px" class="custom-dialog"
            :before-close="handleClose">
            <div class="contacts">
                <div class="dept_box">
                    <el-tree :data="deptOptions" :props="defaultProps" :expand-on-click-node="false"
                        :filter-node-method="filterNode" ref="tree" node-key="id" default-expand-all highlight-current
                        @node-click="handleNodeClick" />
                </div>
                <div class="list_box">
                    <div class="list_condition">
                        <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll" @change="handleCheckAllChange"
                            class="check_all">全选</el-checkbox>
                        <el-input placeholder="请输入人员名称" v-model="queryParams.nickName" size="small"
                            style="width: 260px;" @keyup.enter.native="handleQuery">
                            <el-button slot="suffix" type="primary" plain icon="el-icon-search" size="mini"
                                style="margin-top: 2px;" @click="handleQuery">搜索</el-button>
                        </el-input>
                    </div>
                    <el-checkbox-group v-model="checkList" @change="handleCheckedCitiesChange" class="list_concent">
                        <el-checkbox v-for="item in userList" :label="item.userId" :key="item.userId" class="list_item">
                            <img :src="item.avatar_oss || imgPath + item.avatar" alt="" class="img">
                            <div class="item_text">
                                <div class="name">{{ item.realName || item.nickName }}</div>
                                <div class="dept_box">
                                    <span>{{ item.dept && item.dept.deptName }}</span>
                                    <span>{{ item.remark }}</span>
                                </div>
                            </div>
                        </el-checkbox>
                    </el-checkbox-group>
                </div>
            </div>
            <div slot="footer">
                <el-button class="custom-dialog-btn" @click="handleClose">取消</el-button>
                <el-button class="custom-dialog-btn primary" @click="handleSubmit">确定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { listUser, deptTreeSelect } from '@/api/system/user'
export default {
    props: {
        title: {
            type: String,
            default: '负责人'
        },
        type: {
            type: String,
            default: 'fz'
        },
    },
    data() {
        return {
            open: false,
            deptOptions: [],
            defaultProps: {
                children: 'children',
                label: 'label'
            },
            queryParams: {
                pageNum: 1,
                pageSize: 1000,
                userName: undefined,
                deptId: undefined
            },
            loading: false,
            userList: [],
            checkList: [],
            isIndeterminate: false,
            checkAll: false,
        }
    },
    created() {
        this.getDeptTree()
        this.getList()
    },
    methods: {
        /** 查询部门下拉树结构 */
        getDeptTree() {
            deptTreeSelect().then(response => {
                this.deptOptions = response.data
            })
        },
        // 筛选节点
        filterNode(value, data) {
            if (!value) return true
            return data.label.indexOf(value) !== -1
        },
        // 节点单击事件
        handleNodeClick(data) {
            this.queryParams.deptId = data.id
            this.checkList = []
            this.getList()
        },
        /** 查询用户列表 */
        getList() {
            this.loading = true
            listUser(this.queryParams).then(response => {
                this.userList = response.rows
                this.loading = false
            })
        },
        // 搜索
        handleQuery() {
            this.queryParams.deptId = undefined
            this.getList()
        },
        handleCheckAllChange(val) {
            this.checkList = [];
            if (val) {
                this.userList.forEach(el => {
                    this.checkList.push(el.userId)
                });
            }
            this.isIndeterminate = false;
            console.log(this.checkList)

        },
        handleCheckedCitiesChange(value) {
            console.log(value)
            let checkedCount = value.length;
            this.checkAll = checkedCount === this.userList.length;
            this.isIndeterminate = checkedCount > 0 && checkedCount < this.userList.length;
        },
        handleOpen(arr) {
            this.checkAll = arr.length > 0 ? true : false
            this.isIndeterminate = arr.length > 0 ? true : false;
            this.checkList = arr || []
            this.open = true
        },
        handleClose() {
            this.open = false
            this.checkList = []
        },
        handleSubmit() {
            let contactList = this.userList.filter(item => this.checkList.find(i => item.userId == i))
            contactList.forEach(el => {
                el.typesOf = this.type
            })
            console.log(contactList)
            this.$emit('update', contactList)
            // 关闭弹窗
            this.open = false
        }
    }
}
</script>

<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';

.contacts {
    display: flex;
    align-items: center;
    margin: 0 20px;
    height: 520px;
    border-bottom: 1px solid #E2E6F3;

    .dept_box {
        width: 265px;
        flex-shrink: 0;
        height: 100%;
        overflow-y: scroll;

        &::-webkit-scrollbar {
            width: 0px;
        }
    }

    .list_box {
        flex: 1;
        height: 100%;
        border-left: 1px solid #E2E6F3;

        .list_condition {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding-left: 20px;
            padding-bottom: 9px;
            border-bottom: 1px solid #E2E6F3;
        }

        .list_concent {
            display: flex;
            flex-direction: column;
            height: 465px;
            overflow-y: scroll;
            padding-left: 20px;

            &::-webkit-scrollbar {
                width: 0px;
            }

            .list_item {
                height: 49px;
                border-bottom: 1px solid #E2E6F3;
                flex-shrink: 0;
                display: flex;
                align-items: center;

                ::v-deep {
                    .el-checkbox__label {
                        display: flex;
                        align-items: center;
                        width: 380px;
                    }
                }

                .img {
                    width: 38px;
                    height: 38px;
                }

                .item_text {
                    margin-left: 15px;
                    width: 100%;

                    .name {
                        font-weight: 500;
                        font-size: 14px;
                        color: #333333;
                        line-height: 20px;
                    }

                    .dept_box {
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        font-weight: 400;
                        font-size: 12px;
                        color: #666666;
                        line-height: 20px;
                        width: 100%;
                    }
                }
            }
        }
    }
}
</style>