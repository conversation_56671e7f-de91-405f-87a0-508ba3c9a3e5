<template>
    <div>
        <el-dialog :visible.sync="open" width="1152px" class="custom-dialog por">
            <div slot="title">
                <div class="customper_title_box">
                    <div class="title_text">选择客户</div>
                    <div class="customer_ipt_box">
                        <el-input v-model="queryParams.name" placeholder="请输入客户名称">
                            <el-button style="margin-top: 2px;" slot="suffix" type="primary" icon="el-icon-search" plain
                                size="medium">搜索</el-button>
                        </el-input>
                    </div>
                </div>
            </div>
            <div class="customer_list_box">
                <div class="customer_list_tabs_left">
                    <el-tabs type="border-card" v-model="customerTabActive">
                        <el-tab-pane v-for="(item, index) in customerList" :key="index" :label="item.label"
                            :name="item.value">
                            <span slot="label" v-if="item.value == '1'">
                                {{ item.label }}
                            </span>
                            <el-popover slot="label" placement="bottom" trigger="hover" popper-class="btn_box" v-else>
                                <div class="customer_table_box">
                                    <div class="table_title">
                                        <span v-if="item.value == '3'">选择制定成员【客户负责人】</span>
                                        <span v-if="item.value == '4'">选择所在城市</span>
                                        <span v-if="item.value == '5'">选择客户类别</span>
                                        <span v-if="item.value == '6'">选择客户状态</span>
                                        <span v-if="item.value == '7'">选择客户等级</span>
                                        <span v-if="item.value == '8'">选择客户来源</span>
                                    </div>
                                    <div class="table_concent">
                                        <el-table stripe :data="list" :key="'customer_table_' + item.value"
                                            style="width: 100%" class="custom-table"
                                            @selection-change="handleAllSelect">
                                            <el-table-column align="center" type="selection"
                                                width="50"></el-table-column>
                                            <el-table-column align="center" type="index" label="序号"
                                                width="54"></el-table-column>
                                            <el-table-column align="center" prop="companyName" label="客户名称"
                                                show-overflow-tooltip v-if="item.value == '3'" width="260"></el-table-column>
                                            <el-table-column align="center" prop="city" label="城市名称"
                                                show-overflow-tooltip v-if="item.value == '4'" width="260">
                                            </el-table-column>
                                            <el-table-column align="center" label="类别" show-overflow-tooltip
                                                v-if="item.value == '5'" width="260">
                                                <template slot-scope="{ row }">
                                                    <span v-if="row.type === 1">企业</span>
                                                    <span v-if="row.type === 2">个人</span>
                                                </template>
                                            </el-table-column>
                                            <el-table-column align="center" label="状态" show-overflow-tooltip
                                                v-if="item.value == '6'" width="260">
                                                <template slot-scope="{ row }">
                                                    <span v-if="row.status === 1">成交客户</span>
                                                </template>
                                            </el-table-column>
                                            <el-table-column align="center" label="级别" show-overflow-tooltip
                                                v-if="item.value == '7'" width="260">
                                                <template slot-scope="{ row }">
                                                    <el-rate v-model="row.leavel" disabled>
                                                    </el-rate>
                                                </template>
                                            </el-table-column>
                                            <el-table-column align="center" label="来源" show-overflow-tooltip
                                                v-if="item.value == '8'" width="260">
                                                <template slot-scope="{ row }">
                                                    <span v-if="row.source === 1">朋友介绍</span>
                                                    <span v-if="row.source === 2">1688</span>
                                                    <span v-if="row.source === 3">抖音</span>
                                                </template>
                                            </el-table-column>

                                        </el-table>
                                    </div>
                                </div>
                                <span slot="reference">
                                    {{ item.label }} <i class="el-icon-caret-bottom"></i>
                                </span>
                            </el-popover>

                            <el-table v-loading="loading" ref="table" stripe :data="list" :key="item.value"
                                style="width: 100%" highlight-current-row @current-change="handleCurrentChange">
                                <el-table-column align="center" type="index" label="序号" width="50"></el-table-column>
                                <el-table-column align="center" prop="name" label="客户名称"
                                    show-overflow-tooltip></el-table-column>
                                <el-table-column align="center" prop="type" label="类型" show-overflow-tooltip>
                                </el-table-column>
                                <el-table-column align="center" prop="city" label="所在城市" show-overflow-tooltip>
                                </el-table-column>
                                <el-table-column align="center" prop="status" label="客户状态" show-overflow-tooltip>
                                </el-table-column>
                                <!-- <el-table-column align="center" label="负责人" prop="name"
                                    show-overflow-tooltip></el-table-column>
                                <el-table-column align="center" prop="phone" label="联系方式"
                                    show-overflow-tooltip></el-table-column> -->
                                <el-table-column align="center" label="操作">
                                    <template slot-scope="{ row }">
                                        <div class="table_btns_box">
                                            <el-button type="text" slot="reference">查看详情 ＞</el-button>
                                        </div>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </el-tab-pane>
                    </el-tabs>
                </div>
                <div class="customer_list_tabs_right">
                    <img src="@/assets/images/add_blue.png" alt="">
                    新建客户
                </div>
            </div>

            <div slot="footer">
                <button type="button" class="custom-dialog-btn" @click="handleCancel">关闭</button>
                <button type="button" class="custom-dialog-btn primary" @click="handleSubmit">确定</button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { getCrmCustomerList } from '@/api/crm/customer' 

export default {
    data() {
        return {
            open: false,
            queryParams: {
                name: '',
                pageNum: 1,
                pageSize: 1000
            },
            customerTabActive: '1',
            customerList: [{
                label: '全部客户',
                value: '1'
            }, 
            // {
            //     label: '我关注的',
            //     value: '2'
            // }, {
            //     label: '指定成员',
            //     value: '3'
            // }, {
            //     label: '所在城市',
            //     value: '4'
            // }, 
            // {
            //     label: '客户类别',
            //     value: '5'
            // }, {
            //     label: '客户状态',
            //     value: '6'
            // }, {
            //     label: '客户等级',
            //     value: '7'
            // }, {
            //     label: '客户来源',
            //     value: '8'
            // },
        ],
            loading: false,
            list: [],
            currentRow: null,
        }
    },
    created() {
        this.getCustomerList();
    },
    methods: {
        getCustomerList() {
            this.loading = true;
            getCrmCustomerList(this.queryParams).then(res => {
                const { code, msg, rows, total } = res
                if (code === 200) {
                    this.list = rows
                    this.total = total
                    this.loading = false
                } else {
                    this.$message.error(msg)
                    this.loading = false
                }
            })
        },
        handleOpen() {
            this.open = true
        },
        handleAllSelect() {

        },
        handleCurrentChange(val) {
            this.currentRow = val;
            console.log(this.currentRow);
        },
        handleCancel() {
            this.open = false
        },
        handleSubmit() {
            this.$emit('update', this.currentRow);
            this.open = false
        },
    }
}
</script>
<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
@import '~@/assets/styles/crm.scss';


.por {
    .customper_title_box {
        display: flex;
        align-items: center;

        .title_text {
            font-weight: 400;
            font-size: 18px;
            color: #666666;
            line-height: 20px;
            margin-right: 50px;
        }

        .customer_ipt_box {
            width: 303px;
        }
    }

    .customer_list_box {
        position: relative;
        padding: 0px 20px;

        .customer_list_tabs_left {
            ::v-deep {

                .el-tabs--border-card>.el-tabs__header {
                    background-color: #F2F3F9;
                }

                .el-tabs--border-card>.el-tabs__header .el-tabs__item.is-active {
                    background-color: #f8f9fb;
                }

                .el-tabs--border-card>.el-tabs__content {
                    padding: 0;
                }

                .el-table {
                    border: none !important;
                }
            }
        }

        .customer_list_tabs_right {
            position: absolute;
            top: 4px;
            right: 40px;
            width: 103px;
            height: 32px;
            background: #E7F0FF;
            border-radius: 5px;
            border: 1px solid #2E73F3;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 500;
            font-size: 12px;
            color: #2E73F3;
            cursor: pointer;

            img {
                width: 20px;
                height: 20px;
                margin-right: 5px;
            }
        }
    }
}

.customer_table_box {
    .table_title {
        height: 38px;
        background: #F2F3F9;
        font-weight: 400;
        font-size: 12px;
        color: #666666;
        line-height: 38px;
        text-align: center;
    }
}
</style>
