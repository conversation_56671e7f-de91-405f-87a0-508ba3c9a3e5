<template>
  <div>
    <div class="create-user-config-title">
      {{ title }}设置
      <span class="tips">注：支持拖动排序</span>
    </div>
    <el-table :data="tableData" border class="custom-table" style="width: 100%">
      <el-table-column type="index" label="序号" width="50" align="center"></el-table-column>
      <el-table-column prop="name" label="类别名称" align="center" width="160">
        <template slot-scope="{ row }">
          <el-input v-model="row.name" size="small" placeholder="请输入类别名称"></el-input>
        </template>
      </el-table-column>
      <el-table-column prop="color" label="类别颜色" width="70" align="center">
        <template slot-scope="{ row }">
          <el-color-picker v-model="row.color" size="small"></el-color-picker>
        </template>
      </el-table-column>
      <el-table-column prop="isHide" label="是否隐藏" width="70" align="center">
        <template slot-scope="{ row }">
          <el-switch v-model="row.isHide" :active-value="1" :inactive-value="0"></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="160">
        <template slot-scope="scope">
          <div class="flex align-center flex-justify-between">
            <div class="table-text-btn" :class="{ active: scope.row.isDefault }"
              @click="handleSetDefault(scope.$index)">{{ scope.row.isDefault ? '默认状态' : '设为默认' }}</div>
            <div class="table-text-btn danger" @click="handleDelete(scope.$index)">
              <i class="el-icon-delete"></i>
              <span>删除</span>
            </div>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <div class="create-user-config-sort border-zero">
      <span>可编辑此选项权限</span>
      <el-radio-group v-model="purview">
        <el-radio label="admin">仅管理员</el-radio>
        <el-radio label="all">所有人</el-radio>
        <el-radio label="assign">指定人员</el-radio>
      </el-radio-group>
    </div>
    <div class="crm-deliver-dropdown-btns flex-justify-between">
      <el-button type="primary" plain size="small" icon="el-icon-plus" @click="handleAdd">添加{{ title }}</el-button>
      <div class="flex align-center">
        <el-button size="small" @click="handleClose">关闭</el-button>
        <el-button type="primary" size="small" @click="handleConfirm">确定</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { editCrmDict, getCrmDict } from '@/api/crm/index'
export default {
  props: {
    title: {
      type: String,
      default: ''
    },
    configData: {
      type: Array,
      default: () => []
    },
    configType: {
      type: String,
      default: ''
    },
    configOpen: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      purview: 'admin',
      tableData: [],
      authUserList: []
    }
  },
  // watch: {
  //   configData: {
  //     handler(newVal) {
  //       this.tableData = JSON.parse(JSON.stringify(newVal))
  //     },
  //     deep: true,
  //     immediate: true
  //   }
  // },
  mounted() {
    this.getConfigList(this.configType)
  },
  methods: {
    // 获取列表
    getConfigList(type) {
      this.tableData = []
      let obj = {
        dictType: type
      }
      getCrmDict(obj).then(res => {
        if (res.code == 200 && res.data) {
          this.authUserList = res.data.authUserList
          this.tableData = res.data.dataList
          this.purview = res.data.purview
        }
      })
    },
    // 设置默认状态
    handleSetDefault(index) {
      this.tableData.forEach(item => {
        item.isDefault = 0
      })
      this.tableData[index].isDefault = 1
    },
    // 添加客户类别
    handleAdd() {
      this.tableData.push({
        name: '',
        color: '',
        isHide: 0,
        isDefault: 0
      })
    },
    // 删除客户类别
    handleDelete(index) {
      this.tableData.splice(index, 1)
    },
    // 关闭
    handleClose() {
      this.$emit('close')
    },
    // 确定
    handleConfirm() {
      // 更新配置数据
      editCrmDict({
        authUserList: [],
        dictDataList: this.tableData,
        purview: this.purview,
        dictType: this.configType
      }).then(res => {
        if (res.code == 200) {
          this.$message({
            type: 'success',
            message: '编辑成功!'
          })
          this.$emit('update', this.tableData)
          // 关闭弹窗
          this.handleClose()
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';
@import '~@/assets/styles/crm.scss';
</style>
