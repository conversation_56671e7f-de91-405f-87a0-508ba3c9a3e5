<template>
    <div>
        <el-dialog v-dialogDragBox :title="title" :visible.sync="open" width="1152px" class="custom-dialog">
            <div style="padding: 0 20px">
                <el-form ref="form" :model="form" :rules="rules" label-width="7em">
                    <el-row :gutter="20" style="display: flex; flex-wrap: wrap">
                        <!-- <el-col :span="12">
                            <el-form-item label="联系人" prop="categoryId">
                                <div @click="customerListOpen = true">
                                    <el-input v-model="form.productName" placeholder="请选择客户" readonly class="ipt">
                                        <i slot="suffix" class="el-input__icon el-icon-arrow-down"></i>
                                    </el-input>
                                </div>
                            </el-form-item>
                        </el-col> -->
                        <el-col :span="12">
                            <el-form-item label="联系人" prop="liaison">
                                <!-- <el-select v-model="form.categoryId" placeholder="请选择联系人" style="width: 100%">
                                    <el-option v-for="(item, index) in categoryList" :key="index" :label="item.name"
                                        :value="item.id"></el-option>
                                </el-select> -->
                                <el-input v-model="form.liaison" placeholder="请输入联系人" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="联系方式" prop="liaisonWay">
                                <el-input v-model="form.liaisonWay" placeholder="请输入联系方式" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="跟进阶段" prop="followUpStage">
                                <div class="create-user-form-item">
                                    <el-select v-model="form.followUpStage" placeholder="请选择跟进阶段">
                                        <el-option v-for="item in followUpStageData" :key="item.name" :label="item.name"
                                            :value="item.name"></el-option>
                                    </el-select>
                                    <el-popover ref="followUpStage" trigger="click" popper-class="crm-popover"
                                        @after-leave="followUpStageShow = false">
                                        <div class="create-config" slot="reference" @click="followUpStageShow = true">
                                            <i class="el-icon-edit-outline"></i>
                                            <span>编辑</span>
                                        </div>
                                        <config-table title="跟进阶段" :config-data="followUpStageData"
                                            config-type="Follow_Up_Stage" @close="handleClosePopover('followUpStage')"
                                            @update="handleUpdateConfigData('followUpStageData', $event)"
                                            v-if="followUpStageShow"></config-table>
                                    </el-popover>
                                </div>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label="跟进内容" prop="followUpContent">
                                <el-input v-model="form.followUpContent" placeholder="请输入跟进内容" type="textarea"
                                    :autosize="{ minRows: 4, maxRows: 4 }" resize="none" class="custom-textarea" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="下次跟进时间" prop="followUptime">
                                <el-date-picker v-model="form.followUptime" type="datetime"
                                    value-format="yyyy-MM-dd HH:mm:ss" placeholder="请选择下次跟进时间"
                                    :picker-options="pickerOptions" style="width: 100%"></el-date-picker>
                            </el-form-item>
                        </el-col>
                        <!-- <el-col :span="12">
                            <div class="checked_box">
                                <div class="checked_item">3天后</div>
                                <div class="checked_item">一周后</div>
                                <div class="checked_item">一个月后</div>
                                <div class="setting_box">
                                    <img src="@/assets/images/setting.png" alt="">
                                    <span>自定义</span>
                                </div>
                            </div>
                        </el-col> -->
                        <el-col :span="24">
                            <el-form-item label="图片" prop="image">
                                <image-upload v-model="form.image" :file-type="fileType" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label="附件" prop="attachment">
                                <image-upload v-model="form.attachment" :file-type="fileType" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
            <div slot="footer">
                <button type="button" class="custom-dialog-btn" @click="handleCancel">取消</button>
                <button type="button" class="custom-dialog-btn primary" @click="handleSumit">确定</button>
            </div>
        </el-dialog>

        <el-dialog v-dialogDragBox :visible.sync="customerListOpen" width="1152px" class="custom-dialog por">
            <div slot="title">
                <div class="customper_title_box">
                    <div class="title_text">选择客户</div>
                    <div class="customer_ipt_box">
                        <el-input v-model="form.productName" placeholder="请输入客户名称">
                            <el-button style="margin-top: 2px;" slot="suffix" type="primary" icon="el-icon-search" plain
                                size="medium">搜索</el-button>
                        </el-input>
                    </div>
                </div>
            </div>
            <div class="customer_list_box">
                <div class="customer_list_tabs_left">
                    <el-tabs type="border-card" v-model="customerTabActive">
                        <el-tab-pane v-for="(item, index) in customerList" :key="index" :label="item.label"
                            :name="item.value">
                            <span slot="label" v-if="index == 0 || index == 1">
                                {{ item.label }}
                            </span>
                            <el-popover slot="label" placement="bottom" trigger="hover" popper-class="btn_box" v-else>
                                <div class="customer_table_box">
                                    <div class="table_title">
                                        <span v-if="index == 2">选择制定成员【客户负责人】</span>
                                        <span v-if="index == 3">选择所在城市</span>
                                        <span v-if="index == 4">选择客户类别</span>
                                        <span v-if="index == 5">选择客户状态</span>
                                        <span v-if="index == 6">选择客户等级</span>
                                        <span v-if="index == 7">选择客户来源</span>
                                    </div>
                                    <div class="table_concent">
                                        <el-table stripe :data="list" :key="'customer_table_' + item.value"
                                            style="width: 100%" class="custom-table"
                                            @selection-change="handleAllSelect">
                                            <el-table-column align="center" type="selection"
                                                width="50"></el-table-column>
                                            <el-table-column align="center" type="index" label="序号"
                                                width="54"></el-table-column>
                                            <el-table-column align="center" prop="companyName" label="客户名称"
                                                show-overflow-tooltip v-if="index == 2" width="260"></el-table-column>
                                            <el-table-column align="center" prop="city" label="城市名称"
                                                show-overflow-tooltip v-if="index == 3" width="260">
                                            </el-table-column>
                                            <el-table-column align="center" label="类别" show-overflow-tooltip
                                                v-if="index == 4" width="260">
                                                <template slot-scope="{ row }">
                                                    <span v-if="row.type === 1">企业</span>
                                                    <span v-if="row.type === 2">个人</span>
                                                </template>
                                            </el-table-column>
                                            <el-table-column align="center" label="状态" show-overflow-tooltip
                                                v-if="index == 5" width="260">
                                                <template slot-scope="{ row }">
                                                    <span v-if="row.status === 1">成交客户</span>
                                                </template>
                                            </el-table-column>
                                            <el-table-column align="center" label="级别" show-overflow-tooltip
                                                v-if="index == 6" width="260">
                                                <template slot-scope="{ row }">
                                                    <el-rate v-model="row.leavel" disabled>
                                                    </el-rate>
                                                </template>
                                            </el-table-column>
                                            <el-table-column align="center" label="来源" show-overflow-tooltip
                                                v-if="index == 7" width="260">
                                                <template slot-scope="{ row }">
                                                    <span v-if="row.source === 1">朋友介绍</span>
                                                    <span v-if="row.source === 2">1688</span>
                                                    <span v-if="row.source === 3">抖音</span>
                                                </template>
                                            </el-table-column>

                                        </el-table>
                                    </div>
                                </div>
                                <span slot="reference">
                                    {{ item.label }} <i class="el-icon-caret-bottom"></i>
                                </span>
                            </el-popover>

                            <el-table v-loading="loading" ref="table" stripe :data="list" :key="item.value"
                                style="width: 100%">
                                <el-table-column align="center" type="index" label="序号" width="50"></el-table-column>
                                <el-table-column align="center" prop="companyName" label="客户名称"
                                    show-overflow-tooltip></el-table-column>
                                <el-table-column align="center" label="类型" show-overflow-tooltip>
                                    <template slot-scope="{ row }">
                                        <span v-if="row.type === 1">企业</span>
                                        <span v-if="row.type === 2">个人</span>
                                    </template>
                                </el-table-column>
                                <el-table-column align="center" prop="city" label="所在城市" show-overflow-tooltip>
                                </el-table-column>
                                <el-table-column align="center" label="客户状态" show-overflow-tooltip>
                                    <template slot-scope="{ row }">
                                        <span v-if="row.status === 1">成交客户</span>
                                    </template>
                                </el-table-column>
                                <el-table-column align="center" label="负责人" prop="name"
                                    show-overflow-tooltip></el-table-column>
                                <el-table-column align="center" prop="phone" label="联系方式"
                                    show-overflow-tooltip></el-table-column>
                                <el-table-column align="center" label="操作">
                                    <template slot-scope="{ row }">
                                        <div class="table_btns_box">
                                            <el-button type="text" slot="reference">查看详情 ＞</el-button>
                                        </div>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </el-tab-pane>
                    </el-tabs>
                </div>
                <div class="customer_list_tabs_right">
                    <img src="@/assets/images/add_blue.png" alt="">
                    新建客户
                </div>
            </div>

            <div slot="footer">
                <button type="button" class="custom-dialog-btn primary" @click="customerListOpen = false">关闭</button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { writeCrmCustomerFollow } from '@/api/crm/customer'
import ConfigTable from './create/config'
import { getCrmDict } from '@/api/crm/index'

export default {
    name: 'AddFollow',
    components: { ConfigTable },
    data() {
        return {
            title: '新建跟进信息',
            fileType: ['png', 'PNG', 'jpg', 'JPG', 'jpeg', 'JPEG'],
            open: false,
            form: {},
            rules: {
                customerId: [{ required: true, message: '客户不能为空', trigger: ['blur', 'change'] }],
                followUpContent: [{ required: true, message: '跟进内容不能为空', trigger: ['blur', 'change'] }],
                followUpStage: [{ required: true, message: '跟进类型不能为空', trigger: ['blur', 'change'] }],
                liaison: [{ required: true, message: '联系人不能为空', trigger: ['blur', 'change'] }],
                liaisonWay: [{ required: true, message: '联系方式不能为空', trigger: ['blur', 'change'] }],
            },
            // categoryList: [],
            customerListOpen: false,
            customerTabActive: '1',
            customerList: [{
                label: '全部客户',
                value: '1'
            }, {
                label: '我关注的',
                value: '2'
            }, {
                label: '指定成员',
                value: '3'
            }, {
                label: '所在城市',
                value: '4'
            }, {
                label: '客户类别',
                value: '5'
            }, {
                label: '客户状态',
                value: '6'
            }, {
                label: '客户等级',
                value: '7'
            }, {
                label: '客户来源',
                value: '8'
            },],
            loading: false,
            list: [{
                companyName: '世盛金属制品有限公司',
                type: 1,
                city: '河北省-邯郸市',
                status: 1,
                name: '张璐瑶',
                phone: '18686868886',
                leavel: 1,
                source: 1,
            }, {
                companyName: '世盛金属制品有限公司',
                type: 2,
                city: '河北省-邯郸市',
                status: 1,
                name: '张璐瑶',
                phone: '18686868886',
                leavel: 2,
                source: 2,
            }, {
                companyName: '世盛金属制品有限公司',
                type: 1,
                city: '河北省-邯郸市',
                status: 1,
                name: '张璐瑶',
                phone: '18686868886',
                leavel: 3,
                source: 3,
            },],
            followUpStageData: [],
            followUpStageShow: false,
            pickerOptions: {
                disabledDate(v) {
                    return v.getTime() < new Date().getTime();//  - 86400000是否包括当天
                }
            }
        }
    },
    created() {
        this.getConfigList()
    },
    methods: {
        // 获取字典列表
        getConfigList() {
            this.followUpStageData = []
            getCrmDict({ dictType: 'Follow_Up_Stage' }).then(res => {
                if (res.code == 200 && res.data) {
                    if (res.data.dictType === 'Follow_Up_Stage') {
                        this.followUpStageData = res.data.dataList
                    }
                }
            })
        },
        // 关闭Popover弹窗
        handleClosePopover(name) {
            this.$refs[name].doClose()
        },
        // 更新配置数据
        handleUpdateConfigData(name, data) {
            if (name === 'followUpStageData') {
                this.followUpStageData = JSON.parse(JSON.stringify(data))
                const obj = this.followUpStageData.find(item => item.name === this.form.type)
                if (!obj) this.form.type = ''
            }
        },
        reset() {
            this.form = {
                attachment: undefined,
                customerId: undefined,
                followUpContent: undefined,
                followUpStage: undefined,
                followUptime: undefined,
                image: undefined,
                liaison: undefined,
                liaisonWay: undefined,
            }
            this.resetForm('form')
        },
        handleAdd(id) {
            this.reset()
            this.form.customerId = id
            this.title = '新建跟进信息'
            this.open = true
        },
        // 取消
        handleCancel() {
            this.open = false
            this.reset()
        },
        // 提交
        handleSumit() {
            this.$refs['form'].validate(valid => {
                if (valid) {
                    writeCrmCustomerFollow(this.form).then(res => {
                        if (res.code === 200) {
                            this.$message.success('跟进填写完成')
                            this.open = false
                            this.$parent.getList()
                        } else {
                            this.$message.error(res.msg)
                        }
                    })
                }
            })
        },
        handleAllSelect() {

        }
    }
}
</script>
<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
@import '~@/assets/styles/crm.scss';

.ipt {
    ::v-deep {
        .el-input__inner {
            cursor: pointer;
        }
    }
}

.checked_box {
    display: flex;
    align-items: center;

    .checked_item {
        background: #DDE3ED;
        border-radius: 5px;
        height: 36px;
        font-weight: 400;
        font-size: 14px;
        color: #666666;
        line-height: 36px;
        padding: 0 15px;
        cursor: pointer;
        box-sizing: border-box;
        margin-right: 10px;
    }

    .setting_box {
        display: flex;
        align-items: center;
        font-weight: 400;
        font-size: 14px;
        color: #666666;
        line-height: 20px;

        img {
            width: 20px;
            height: 20px;
            margin-right: 4px;
        }
    }
}

.por {
    .customper_title_box {
        display: flex;
        align-items: center;

        .title_text {
            font-weight: 400;
            font-size: 18px;
            color: #666666;
            line-height: 20px;
            margin-right: 50px;
        }

        .customer_ipt_box {
            width: 303px;
        }
    }

    .customer_list_box {
        position: relative;
        padding: 0px 20px;

        .customer_list_tabs_left {
            ::v-deep {

                .el-tabs--border-card>.el-tabs__header {
                    background-color: #F2F3F9;
                }

                .el-tabs--border-card>.el-tabs__header .el-tabs__item.is-active {
                    background-color: #f8f9fb;
                }

                .el-tabs--border-card>.el-tabs__content {
                    padding: 0;
                }

                .el-table {
                    border: none !important;
                }
            }
        }

        .customer_list_tabs_right {
            position: absolute;
            top: 4px;
            right: 40px;
            width: 103px;
            height: 32px;
            background: #E7F0FF;
            border-radius: 5px;
            border: 1px solid #2E73F3;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 500;
            font-size: 12px;
            color: #2E73F3;
            cursor: pointer;

            img {
                width: 20px;
                height: 20px;
                margin-right: 5px;
            }
        }
    }
}

.customer_table_box {
    .table_title {
        height: 38px;
        background: #F2F3F9;
        font-weight: 400;
        font-size: 12px;
        color: #666666;
        line-height: 38px;
        text-align: center;
    }
}
</style>
