<template>
    <div>
        <el-dialog v-dialogDragBox :visible.sync="open" width="1152px" class="custom-dialog">
            <div slot="title" style="display: flex; align-items: center;">
                <span style="font-weight: 400; font-size: 18px; color: #666666; line-height: 20px;">自动转换规则设置</span>
            </div>
            <div class="ruleSetting_box">
                <div style="display: flex; align-items: center;">
                    <span style="margin-right: 20px;">可编辑此选项权限</span>
                    <div
                        style="font-weight: 400; font-size: 14px; color: #2E73F3; display: flex; align-items: center; cursor: pointer;">
                        <img style="width: 20px; height: 20px; margin-right: 3px;" src="@/assets/images/add_blue.png"
                            alt="">
                        添加规则项
                    </div>
                </div>
                <div class="ruleSetting_list">
                    <div class="ruleSetting_item">
                        <el-select v-model="status" style="width: 256px; margin-right: 10px;">
                            <el-option label="编号" :value="1"></el-option>
                            <el-option label="数据来源" :value="2"></el-option>
                            <el-option label="类型" :value="3"></el-option>
                        </el-select>
                        <el-select v-model="status"  style="width: 256px; margin-right: 10px;">
                            <el-option label="等于" :value="1"></el-option>
                            <el-option label="不等于" :value="2"></el-option>
                        </el-select>
                        <el-input v-model="productName" placeholder="请输入赢率"  style="width: 256px; margin-right: 10px;">
                            <template slot="prepend">-</template>
                            <template slot="append">+</template>
                        </el-input>
                        <el-button type="text" size="mini" icon="el-icon-delete">删除</el-button>
                    </div>
                    <div class="ruleSetting_item">
                        <el-select v-model="status" style="width: 256px; margin-right: 10px;">
                            <el-option label="编号" :value="1"></el-option>
                            <el-option label="数据来源" :value="2"></el-option>
                            <el-option label="类型" :value="3"></el-option>
                        </el-select>
                        <el-select v-model="status"  style="width: 256px; margin-right: 10px;">
                            <el-option label="等于" :value="1"></el-option>
                            <el-option label="不等于" :value="2"></el-option>
                        </el-select>
                        <el-select v-model="status"  style="width: 256px; margin-right: 10px;">
                            <el-option label="电脑网页输入" :value="1"></el-option>
                        </el-select>
                        <el-button type="text" size="mini" icon="el-icon-delete">删除</el-button>
                    </div>
                    <div class="ruleSetting_item">
                        <el-select v-model="status" style="width: 256px; margin-right: 10px;">
                            <el-option label="编号" :value="1"></el-option>
                            <el-option label="数据来源" :value="2"></el-option>
                            <el-option label="类型" :value="3"></el-option>
                        </el-select>
                        <el-select v-model="status"  style="width: 256px; margin-right: 10px;">
                            <el-option label="为个人客户" :value="1"></el-option>
                        </el-select>
                        <div  style="width: 256px; margin-right: 10px;"></div>
                        <el-button type="text" size="mini" icon="el-icon-delete">删除</el-button>
                    </div>
                </div>
            </div>
            <div slot="footer">
                <button type="button" class="custom-dialog-btn" @click="open = false">取消</button>
                <button type="button" class="custom-dialog-btn primary" @click="open = false">确定保存</button>
            </div>
        </el-dialog>

    </div>
</template>

<script>
export default {
    data() {
        return {
            open: false,
            status: '',
            productName: '',
        }
    },
    created() { },
    mounted() { },

    methods: {

        handleOpen() {
            this.open = true
        },
    },
}
</script>

<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';

.ruleSetting_box {
    padding: 10px 20px;
    .ruleSetting_list {
        .ruleSetting_item {
            display: flex;
            align-items: center;
            margin-top: 30px;
        }
    }
}
</style>