<!-- 废弃该页面 -->
<template>
    <div>
        <el-dialog v-dialogDragBox :title="title" :visible.sync="open" width="1152px" class="custom-dialog">
            <div class="order_box">
                <div class="listInfo_tabs_box">
                    <div class="listInfo_tabs_left">
                        <el-tabs type="border-card" v-model="userTypeActive" @tab-click="handleTabItem">
                            <el-tab-pane label="新建企业客户" name="1"></el-tab-pane>
                            <el-tab-pane label="新建个人客户" name="2"></el-tab-pane>
                        </el-tabs>
                    </div>
                    <div class="listInfo_tabs_right">
                        <img src="@/assets/images/setting.png" alt="">
                        自定义
                    </div>
                </div>
                <el-form ref="form" :model="form" :rules="rules" label-width="8em" label-position="left">
                    <el-row :gutter="20" style="display: flex; flex-wrap: wrap">
                        <el-col :span="12">
                            <el-form-item label="负责人" prop="categoryId">
                                <el-tag closable :disable-transitions="false" @close="handleClose(tag)">某某某</el-tag>
                                <el-button type="text" icon="el-icon-plus" style="margin-left: 15px;">选择人员</el-button>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="参与人" prop="categoryId">
                                <el-tag closable :disable-transitions="false" @close="handleClose(tag)">某某某</el-tag>
                                <el-button type="text" icon="el-icon-plus" style="margin-left: 15px;">选择人员</el-button>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="客户获得时间" prop="obtainTime">
                                <el-date-picker v-model="form.obtainTime" type="datetime" value-format="yyyy-MM-dd HH:mm:ss"
                                    placeholder="请选择客户获得时间" style="width: 100%"></el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="销售流程" prop="productName">
                                <div style="display: flex; align-items: center;">
                                    <div
                                        style="display: flex; align-items: center; border: 1px solid #CBD7E2; border-radius: 5px;">
                                        <el-select class="border_no" v-model="form.unit" placeholder="请选择销售流程"
                                            style="width: 100%">
                                            <el-option v-for="(item, index) in options" :key="index" :label="item"
                                                :value="item"></el-option>
                                        </el-select>
                                        <el-divider direction="vertical"></el-divider>
                                        <div style="display: flex; align-items: center; width: 85px; cursor: pointer;" @click="handleSalesProcess">
                                            <img src="@/assets/images/edit.png" alt=""
                                                style="width: 20px; height: 20px;">
                                            <span>编辑</span>
                                        </div>
                                    </div>
                                    <el-select v-model="form.unit" placeholder="请选择"
                                        style="width: 180px; margin-left: 10px; flex-shrink: 0;">
                                        <el-option v-for="(item, index) in options" :key="index" :label="item"
                                            :value="item"></el-option>
                                    </el-select>
                                </div>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <div style="padding: 0 20px 20px;">
                                <div
                                    style="display: flex; align-items: center; justify-content: space-between; padding-bottom: 8px; border-bottom: 1px solid #E1E2E2;">
                                    <div>公司信息</div>
                                    <div
                                        style="font-weight: 400; font-size: 14px; color: #666666; display: flex; align-items: center; cursor: pointer;">
                                        <img style="width: 20px; height: 20px; margin-right: 2px;"
                                            src="@/assets/images/setting.png" alt="">
                                        自定义
                                    </div>
                                </div>
                            </div>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="客户名称" prop="name">
                                <el-input v-model="form.name" placeholder="请输入客户名称" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="所在城市" prop="city">
                                <div
                                    style="display: flex; align-items: center; border: 1px solid #CBD7E2; border-radius: 5px; width: 100%;">
                                    <el-cascader v-model="form.city" class="border_no" :options="options"
                                        placeholder="请选择所在城市" :props="{ expandTrigger: 'hover' }"
                                        @change="handleChange" style="flex: 1;"></el-cascader>
                                    <el-divider direction="vertical"></el-divider>
                                    <div style="display: flex; align-items: center; width: 60px;">
                                        <img src="@/assets/images/edit.png" alt="" style="width: 20px; height: 20px;">
                                        <span>编辑</span>
                                    </div>
                                </div>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="电话是否核实" prop="isVerifyPhone">
                                <div style="display: flex; align-items: center; justify-content: space-around;">
                                    <el-radio-group v-model="form.isVerifyPhone" style="display: flex; align-items: center;">
                                        <el-radio :label="1">已核实</el-radio>
                                        <el-radio :label="2">未核实</el-radio>
                                    </el-radio-group>
                                    <el-input v-model="form.productName" placeholder="请输入备注信息"
                                        style="margin-left: 20px;" />
                                </div>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <div class="table_list">
                                <div class="table_title">
                                    <span style="margin-right: 54px;">成交产品</span>
                                    <el-button type="primary" icon="el-icon-plus" plain size="small">添加产品</el-button>
                                </div>
                                <div class="form_table">
                                    <el-form-item label="" label-width="0" prop="linkProducts">
                                        <el-table :data="form.linkProducts" style="width: 100%;" class="custom-table">
                                            <el-table-column type="index" label="序号" width="50"></el-table-column>
                                            <el-table-column prop="productName" label="产品名称" align="center"
                                                show-overflow-tooltip>
                                                <template slot-scope="{ row }">
                                                    <span class="table-link" @click="handleView(row.id, row)">{{
                                                        row.productName
                                                    }}</span>
                                                </template>
                                            </el-table-column>
                                            <el-table-column label="产品图片" align="center" show-overflow-tooltip>
                                                <template slot-scope="{ row }">
                                                    <div style="display: flex; justify-content: center">
                                                        <el-image :src="formatProductImg(row)" fit="cover"
                                                            @click="handleImgView(row)">
                                                            <div slot="error" class="image-slot">
                                                                <i class="el-icon-picture-outline"></i>
                                                            </div>
                                                        </el-image>
                                                    </div>
                                                </template>
                                            </el-table-column>
                                            <el-table-column prop="model" label="产品编码" align="center"
                                                show-overflow-tooltip></el-table-column>
                                            <el-table-column prop="model" label="产品型号" align="center"
                                                show-overflow-tooltip></el-table-column>
                                            <el-table-column prop="model" label="表面处理" align="center"
                                                show-overflow-tooltip></el-table-column>
                                            <el-table-column prop="model" label="材质" align="center"
                                                show-overflow-tooltip></el-table-column>
                                            <el-table-column prop="model" label="单位" align="center"
                                                show-overflow-tooltip></el-table-column>
                                            <el-table-column label="操作" align="center">
                                                <template slot-scope="scope">
                                                    <el-button type="text" size="mini" icon="el-icon-delete"
                                                        @click="handleDelete(scope.row)">删除</el-button>
                                                </template>
                                            </el-table-column>
                                        </el-table>
                                    </el-form-item>
                                </div>
                                <div class="table_total">
                                    <div class="total_left">
                                        <div class="left_item">
                                            共
                                            <span>8</span>
                                            件产品
                                        </div>
                                        <div class="left_item">
                                            成交总量
                                            <span>10000</span>
                                        </div>
                                    </div>
                                    <div class="total_right">
                                        成交总额
                                        <span>10000000</span>
                                    </div>
                                </div>
                            </div>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="客户类别" prop="type">
                                <div
                                    style="display: flex; align-items: center; border: 1px solid #CBD7E2; border-radius: 5px;">
                                    <el-select class="border_no" v-model="form.type" placeholder="请选择客户类别"
                                        style="width: 100%">
                                        <el-option v-for="(item, index) in options" :key="index" :label="item"
                                            :value="item"></el-option>
                                    </el-select>
                                    <el-divider direction="vertical"></el-divider>
                                    <div style="display: flex; align-items: center; width: 85px;">
                                        <img src="@/assets/images/edit.png" alt="" style="width: 20px; height: 20px;">
                                        <span>编辑</span>
                                    </div>
                                </div>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="客户状态" prop="status">
                                <div
                                    style="display: flex; align-items: center; border: 1px solid #CBD7E2; border-radius: 5px;">
                                    <el-select class="border_no" v-model="form.status" placeholder="请选择客户状态"
                                        style="width: 100%">
                                        <el-option v-for="(item, index) in options" :key="index" :label="item"
                                            :value="item"></el-option>
                                    </el-select>
                                    <el-divider direction="vertical"></el-divider>
                                    <div style="display: flex; align-items: center; width: 85px;">
                                        <img src="@/assets/images/edit.png" alt="" style="width: 20px; height: 20px;">
                                        <span>编辑</span>
                                    </div>
                                </div>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="客户等级" prop="level">
                                <div
                                    style="display: flex; align-items: center; border: 1px solid #CBD7E2; border-radius: 5px;">
                                    <el-select class="border_no" v-model="form.level" placeholder="请选择客户等级"
                                        style="width: 100%">
                                        <el-option v-for="(item, index) in options" :key="index" :label="item"
                                            :value="item"></el-option>
                                    </el-select>
                                    <el-divider direction="vertical"></el-divider>
                                    <div style="display: flex; align-items: center; width: 85px;">
                                        <img src="@/assets/images/edit.png" alt="" style="width: 20px; height: 20px;">
                                        <span>编辑</span>
                                    </div>
                                </div>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="客户来源" prop="source">
                                <div
                                    style="display: flex; align-items: center; border: 1px solid #CBD7E2; border-radius: 5px;">
                                    <el-select class="border_no" v-model="form.source" placeholder="请选择客户来源"
                                        style="width: 100%">
                                        <el-option v-for="(item, index) in options" :key="index" :label="item"
                                            :value="item"></el-option>
                                    </el-select>
                                    <el-divider direction="vertical"></el-divider>
                                    <div style="display: flex; align-items: center; width: 85px;">
                                        <img src="@/assets/images/edit.png" alt="" style="width: 20px; height: 20px;">
                                        <span>编辑</span>
                                    </div>
                                </div>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="公司备注" prop="remark">
                                <el-input v-model="form.remark" placeholder="请输入公司备注" type="textarea"
                                    :autosize="{ minRows: 4, maxRows: 4 }" resize="none" class="custom-textarea" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="企业微信" prop="isAddWx">
                                <div style="display: flex; align-items: center; justify-content: space-around;">
                                    <el-radio-group v-model="form.isAddWx" style="display: flex; align-items: center;">
                                        <el-radio :label="1">已加</el-radio>
                                        <el-radio :label="2">未加</el-radio>
                                    </el-radio-group>
                                    <div
                                        style="display: flex; align-items: center; border: 1px solid #CBD7E2; border-radius: 5px; margin-left: 20px;">
                                        <el-input class="border_no" v-model="form.wxNumber" placeholder="请输入企业微信" />
                                        <el-divider direction="vertical"></el-divider>
                                        <div style="display: flex; align-items: center; width: 85px;">
                                            <img src="@/assets/images/edit.png" alt=""
                                                style="width: 20px; height: 20px;">
                                            <span>编辑</span>
                                        </div>
                                    </div>
                                </div>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <div style="padding: 0 20px 20px;">
                                <div
                                    style="display: flex; align-items: center; justify-content: space-between; padding-bottom: 8px; border-bottom: 1px solid #E1E2E2;">
                                    <div>联系人信息</div>
                                    <div
                                        style="font-weight: 400; font-size: 14px; color: #666666; display: flex; align-items: center; cursor: pointer;" @click="handleContactConfig">
                                        <img style="width: 20px; height: 20px; margin-right: 2px;"
                                            src="@/assets/images/setting.png" alt="">
                                        自定义
                                    </div>
                                </div>
                            </div>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="联系人姓名" prop="unit">
                                <el-input v-model="form.productName" placeholder="请输入联系人姓名" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="联系人邮箱" prop="unit">
                                <el-input v-model="form.productName" placeholder="请输入联系人邮箱" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="联系人手机号" prop="unit">
                                <div
                                    style="display: flex; align-items: center; border: 1px solid #CBD7E2; border-radius: 5px; ">
                                    <el-input class="border_no" v-model="form.productName" placeholder="请输入联系人手机号" />
                                    <el-divider direction="vertical"></el-divider>
                                    <div style="display: flex; align-items: center; width: 135px;">
                                        <img src="@/assets/images/add_blue.png" alt=""
                                            style="width: 20px; height: 20px; margin-right: 5px;">
                                        <span
                                            style="font-weight: 500; font-size: 14px; color: #2E73F3; line-height: 20px;">添加手机号</span>
                                    </div>
                                </div>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="公司座机" prop="unit">
                                <div
                                    style="display: flex; align-items: center; border: 1px solid #CBD7E2; border-radius: 5px;">
                                    <el-input class="border_no" v-model="form.productName" placeholder="请输入公司座机" />
                                    <el-divider direction="vertical"></el-divider>
                                    <div style="display: flex; align-items: center; width: 135px;">
                                        <img src="@/assets/images/add_blue.png" alt=""
                                            style="width: 20px; height: 20px; margin-right: 5px;">
                                        <span
                                            style="font-weight: 500; font-size: 14px; color: #2E73F3; line-height: 20px;">添加座机号</span>
                                    </div>
                                </div>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label-width="0" prop="productName">
                                <el-switch style="margin-right: 5px;" v-model="value" active-color="#2E73F3"
                                    inactive-color="#ff4949">
                                </el-switch>
                                <span>同步创建跟进记录</span>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label-width="0" prop="productName">
                                <el-switch style="margin-right: 5px;" v-model="value" active-color="#2E73F3"
                                    inactive-color="#ff4949">
                                </el-switch>
                                <span>为下次跟进创建任务</span>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24" class="next_follow_box">
                            <el-col :span="12">
                                <el-form-item label="下次跟进时间" prop="supplyEndTime">
                                    <el-date-picker v-model="form.supplyEndTime" type="datetime" value-format="yyyy-MM-dd"
                                        placeholder="请选择下次跟进时间" style="width: 100%"></el-date-picker>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="负责人" prop="categoryId">
                                    <el-tag closable :disable-transitions="false" @close="handleClose(tag)">某某某</el-tag>
                                    <el-button type="text" icon="el-icon-plus"
                                        style="margin-left: 15px;">选择人员</el-button>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="指定跟进人" prop="categoryId">
                                    <el-radio-group v-model="radio">
                                        <el-radio :label="3">客户负责人</el-radio>
                                        <el-radio :label="6">创建人/我自己</el-radio>
                                        <el-radio :label="9">指定人员</el-radio>
                                    </el-radio-group>
                                </el-form-item>
                            </el-col>
                            <el-col :span="24">
                                <el-form-item label="任务说明" prop="remark">
                                    <el-input v-model="form.remark" placeholder="请输入任务说明详细" type="textarea"
                                        :autosize="{ minRows: 4, maxRows: 4 }" resize="none" class="custom-textarea" />
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="提醒执行人" prop="categoryId" class="time_box">
                                    <el-input placeholder="请输入" v-model="input1">
                                        <template slot="prepend">提前</template>                                        
                                        <div slot="suffix">
                                            <el-divider direction="vertical"></el-divider>
                                            <el-select class="border_no" v-model="timeType"
                                                style="width: 80px; height: 32px; margin-top: 3px;">
                                                <el-option label="分钟" :value="1"></el-option>
                                                <el-option label="小时" :value="2"></el-option>
                                                <el-option label="天" :value="3"></el-option>
                                            </el-select>
                                        </div>
                                        <template slot="append">提醒</template>
                                    </el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="提醒方式" prop="categoryId">
                                    <el-radio-group v-model="radio">
                                        <el-radio :label="3">站内提醒/通知</el-radio>
                                        <el-radio :label="6">短信</el-radio>
                                    </el-radio-group>
                                </el-form-item>
                            </el-col>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label-width="0" prop="productName">
                                <el-switch style="margin-right: 5px;" v-model="value" active-color="#2E73F3"
                                    inactive-color="#ff4949">
                                </el-switch>
                                <span>@其他成员</span>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24" class="next_follow_box">
                            <el-col :span="12">
                                <el-form-item label="@成员" prop="categoryId" style="margin-bottom: 0;">
                                    <el-tag closable :disable-transitions="false" @close="handleClose(tag)">某某某</el-tag>
                                    <el-button type="text" icon="el-icon-plus"
                                        style="margin-left: 15px;">选择人员</el-button>
                                </el-form-item>
                            </el-col>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
            <div slot="footer">
                <button type="button" class="custom-dialog-btn" @click="handleCancel">取消</button>
                <button type="button" class="custom-dialog-btn primary" @click="handleSumit">确定</button>
            </div>
        </el-dialog>

        <contactconfig-dialog ref="contactConfig" />
        <salesprocess-dialog ref="salesProcess" />
    </div>
</template>

<script>
import contactconfigDialog from './contactConfig'
import salesprocessDialog from './salesProcess'
export default {
    components: { contactconfigDialog, salesprocessDialog },
    data() {
        return {
            title: '添加新客户',
            form: {
                products: [{
                    productName: '某某产品',
                    model: 'DN150*168*40*40',
                    quantity: undefined
                }]
            },
            rules: {},
            open: false,
            loading: false,
            fileType: ['png', 'PNG', 'jpg', 'JPG', 'jpeg', 'JPEG', 'pdf'],
            key: 1,
            radio: undefined,
            options: [],
            activeName: 'second',
            value: undefined,
            unitOptions: [],
            userTypeActive: '1',
            timeType: 1,
            input1: '',
        }
    },
    created() {

    },
    methods: {
        reset() {
            this.form = {
                products: [{
                    productName: '某某产品',
                    model: 'DN150*168*40*40',
                    quantity: undefined
                }]
            }
            this.resetForm('form')
        },
        handleAdd() {
            this.reset()
            this.title = '添加新客户'
            this.open = true
        },
        // 取消
        handleCancel() {
            this.open = false
            this.reset()
        },
        // 提交
        handleSumit() {
            this.$refs['form'].validate(valid => {
                if (valid) {
                    if (this.form.id != null) {
                        this.form.productId = this.form.id
                        updateUnsalable(this.form).then(res => {
                            if (res.code === 200) {
                                this.$message.success('修改成功')
                                this.open = false
                                this.$parent.getList()
                            } else {
                                this.$message.error(res.msg)
                            }
                        })
                    } else {
                        addUnsalable(this.form).then(res => {
                            if (res.code === 200) {
                                this.$message.success('新增成功')
                                this.open = false
                                this.$parent.getList()
                            } else {
                                this.$message.error(res.msg)
                            }
                        })
                    }
                }
            })
        },
        handleAllSelect() { },
        handleClick() { },
        handleOpen() { },
        handleClose() { },
        handleTabItem() { },
        handleChange() { },
        handleContactConfig() {
            this.$refs.contactConfig.handleOpen()            
        },
        handleSalesProcess() {
            this.$refs.salesProcess.handleOpen()            
        },
    }
}
</script>
<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';

.ipt {
    ::v-deep {
        .el-input__inner {
            cursor: pointer;
        }
    }
}

.order_box {
    padding: 0;

    ::v-deep {
        .el-form-item {
            margin-left: 20px;
        }

        .el-form-item__content {
            padding-right: 20px;
        }

        .el-divider--vertical {
            height: 2em;
        }
    }

    .table_list {
        background: #F0F3F9;
        padding: 20px;
        margin-bottom: 20px;

        .table_title {
            display: flex;
            align-items: center;
            font-weight: 400;
            font-size: 12px;
            color: #999999;
            line-height: 20px;
        }

        .form_table {
            margin-top: 20px;
        }

        .table_total {
            margin-top: 10px;
            height: 48px;
            background: #EFF5FF;
            border-radius: 5px;
            border: 1px solid #2E73F3;
            display: flex;
            align-items: center;
            justify-content: space-between;

            .total_left {
                padding-left: 30px;
                display: flex;
                align-items: center;

                .left_item {
                    font-weight: 400;
                    font-size: 14px;
                    color: #666666;
                    margin-right: 90px;
                    display: flex;
                    align-items: center;

                    span {
                        font-weight: 500;
                        font-size: 18px;
                        color: #2E73F3;
                        margin: 0 10px;
                    }
                }
            }

            .total_right {
                margin-right: 145px;
                font-weight: 400;
                font-size: 14px;
                color: #666666;
                display: flex;
                align-items: center;

                span {
                    font-weight: 500;
                    font-size: 18px;
                    color: #F35D09;
                    margin-left: 10px;
                }
            }
        }

        .flex_box {
            display: flex;
            align-items: center;
            justify-content: space-around;

            span {
                margin-bottom: 22px;
                color: #2E73F3;
                font-weight: 400;
                font-size: 12px;
            }
        }
    }

    .border_no {
        ::v-deep {
            .el-input__inner {
                border: none;
            }
        }
    }
}

.customer_list_box {
    width: 444px;
    height: 538px;
    background: #FFFFFF;
    box-shadow: 0px 1px 15px 0px rgba(0, 0, 0, 0.25);
    border-radius: 5px;
    border: 1px solid #2E73F3;

    ::v-deep {
        .el-tabs__header {
            margin-bottom: 0;

            .el-tabs__nav-wrap {
                padding: 0 15px;
            }
        }
    }

    .dept_box {
        display: flex;

        .dept_left {
            // width: 164px;
        }

        .dept_right {
            // flex: 1;
        }
    }
}

.listInfo_tabs_box {
    background: #F2F3F9;
    position: relative;
    margin: 0 20px 20px;

    .listInfo_tabs_left {
        position: relative;
    }

    ::v-deep {
        .el-tabs--border-card {
            box-shadow: none;
            border: none;
        }

        .el-tabs__content {
            padding: 0;
        }

        .el-tabs--border-card>.el-tabs__header {
            background: #F2F3F9;
            border-bottom: 1px solid #CBD6E2;

            .el-tabs__item {
                color: #666666;

                &.is-active {
                    color: #2E73F3;
                    border-top-color: #CBD6E2;
                    border-right-color: #CBD6E2;
                    border-left-color: #CBD6E2;
                    margin-top: 0;
                    margin-left: 0;
                    border-top-left-radius: 5px;
                    border-top-right-radius: 5px;
                }
            }
        }
    }

    .listInfo_tabs_right {
        font-weight: 400;
        font-size: 14px;
        color: #666666;
        display: flex;
        align-items: center;
        cursor: pointer;
        position: absolute;
        right: 20px;
        top: 10px;

        img {
            width: 20px;
            height: 20px;
            margin-right: 2px;
        }
    }
}

.next_follow_box {
    background: #F0F3F9;
    padding: 20px;
    margin-bottom: 20px;
}
.time_box {
    ::v-deep {
        .el-select {
            .el-input--suffix {
                height: 32px;
                .el-input__inner {
                    height: 32px;
                }
                .el-input__icon {
                    line-height: 32px;
                }
            }
        }
    }
}
</style>
