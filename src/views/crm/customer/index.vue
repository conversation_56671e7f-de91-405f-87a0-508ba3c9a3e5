<template>
    <div class="customer_box">
        <div class="customer_box_left" v-if="leftTabsShow">
            <left-tabs :tabsData="tabsData" :activeTab="activeTab" @update:activeTab="activeTab = $event"></left-tabs>
        </div>
        <div class="customer_box_right">
            <all-customer v-if="activeTab === 'allCustomer'"></all-customer>
            <customer-category v-if="activeTab === 'customerCategory'"></customer-category>
            <customer-state v-if="activeTab === 'customerState'"></customer-state>
            <customer-responsible v-if="activeTab === 'customerResponsible'"></customer-responsible>
            <customer-participate v-if="activeTab === 'customerParticipate'"></customer-participate>
            <customer-public v-if="activeTab === 'customerPublic'"></customer-public>
            <customer-follow v-if="activeTab === 'customerFollow'"></customer-follow>
            <sale-order v-if="activeTab === 'saleOrder'"></sale-order>
            <payment-manage v-if="activeTab === 'paymentManage'"></payment-manage>
            <refund v-if="activeTab === 'refund'"></refund>
        </div>
    </div>
</template>
<script>
import LeftTabs from '@/views/crm/components/LeftTabs/index.vue'
import allCustomer from './allCustomer.vue'
import customerCategory from './customerCategory.vue'
import customerState from './customerState.vue'
import customerResponsible from './customerResponsible.vue'
import customerParticipate from './customerParticipate.vue' 
import customerPublic from './customerPublic.vue'
import customerFollow from './customerFollow.vue'
import saleOrder from './saleOrder.vue'
import paymentManage from './paymentManage.vue'
import refund from './refund.vue'

export default {
    name: 'Customer',
    components: { LeftTabs, allCustomer, customerCategory, customerState, customerResponsible, customerParticipate, customerPublic, customerFollow, saleOrder, paymentManage, refund },
    data() {
        return {
            tabsData: [
                { name: '全部客户', value: 'allCustomer' },
                { name: '客户类别', value: 'customerCategory' },
                { name: '客户状态', value: 'customerState' },
                { name: '我负责的客户', value: 'customerResponsible' },
                { name: '我参与的客户', value: 'customerParticipate' },
                { name: '公共客户', value: 'customerPublic' },
                { name: '联系跟进', value: 'customerFollow' },
                { name: '销售订单', value: 'saleOrder' },
                { name: '收款管理', value: 'paymentManage' },
                // { name: '产品管理', value: 'profitMargin' },
                { name: '退款管理', value: 'refund' },
            ],
            activeTab: 'allCustomer',
            leftTabsShow: true,

        }
    },
    created() { },

    methods: {

    }
}
</script>
<style lang="scss" scoped>
.customer_box {
    display: flex;
    justify-content: space-between;

    .customer_box_left {
        width: 160px;
        min-height: 90vh;
        background: #F0F3F9;
        border: 1px solid #CBD6E2;
        flex-shrink: 0;
    }

    .customer_box_right {
        flex: 1;
    }
}
</style>