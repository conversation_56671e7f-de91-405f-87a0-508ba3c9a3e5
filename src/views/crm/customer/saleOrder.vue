<template>
    <div class="newBox bgcf9 vh-85">
        <!-- 搜索 -->
        <div class="custom-search flex">
            <div class="custom-search-form flex">
                <input type="text" v-model="queryParams.keyword" placeholder="请输入客户名称" class="custom-search-input"
                    @keyup.enter="handleQuery" />
                <button type="button" class="custom-search-button pointer" @click="handleQuery">
                    <i class="el-icon-search"></i>
                    搜索
                </button>
            </div>

            <div class="custom-search-form select_box">
                <div @click="handleUserSelect">
                    <el-input class="ipt" v-model="queryParams.user" placeholder="请选择用户" readonly clearable>
                        <i slot="suffix" class="el-input__icon"
                            :class="userListShow ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"
                            style="line-height: 32px;"></i>
                    </el-input>
                </div>
                <template v-if="userListShow">
                    <div class="user_list_box">
                        <div class="tab_box">
                            <div class="tabs_item" :class="userActive == 1 ? 'avtive' : ''" @click="userActive = 1">指定人员
                            </div>
                            <div class="tabs_item" :class="userActive == 2 ? 'avtive' : ''" @click="userActive = 2">指定部门
                            </div>
                        </div>
                        <div class="tab_list flex">
                            <div class="tab_list_item" :class="{ active: item.value === tabsActive }"
                                v-for="item in tabListOptions" :key="item.value" @click="handleTabList(item)">
                                <span style="margin-bottom: 5px;">{{ item.label }}</span>
                                <span class="bbLine" v-if="item.value === tabsActive"></span>
                            </div>
                        </div>
                        <div class="user_list">
                            <div class="user_list_search">
                                <el-input class="ipt" v-model="userName" placeholder="请输入人员名称" clearable>
                                    <div slot="suffix" class="btn blue_">
                                        <img class="img" src="@/assets/images/search.png" alt="">
                                        搜索
                                    </div>
                                </el-input>
                            </div>
                            <div class="user_list_concent">
                                <div class="user_list_item" v-for="(item, index) in 10" :key="index">
                                    <img class="img" src="@/assets/images/listing_icon.png" alt="">
                                    <div class="text">
                                        <div class="name">陈娟</div>
                                        <div class="dept">
                                            <span>销售一部</span>
                                            <span>销售一部经理</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>
                        <div class="user_btn">
                            <div class="refresh">
                                <img class="img" src="@/assets/images/refresh2.png" alt="">
                                <span>刷新列表</span>
                            </div>
                            <div class="setting">
                                <img class="img" src="@/assets/images/setting.png" alt="">
                                <span>人员设置</span>
                            </div>
                        </div>
                    </div>
                </template>
            </div>

            <div>
                <el-popover placement="bottom" trigger="hover" popper-class="btn_box">
                    <div class="btn_list blue">
                        <div class="btn_item" @click="handleAddUser">新建客户</div>
                        <div class="btn_item">新建跟进记录</div>
                        <div class="btn_item">新建销售订单</div>
                        <div class="btn_item" @click="handleAddPayment">新建收款记录</div>
                    </div>
                    <div class="btn blue_all" slot="reference">
                        <img class="img" src="@/assets/images/add_b.png" alt="">
                        新增客户
                    </div>
                </el-popover>
            </div>
            <div>
                <el-popover placement="bottom" trigger="hover" popper-class="btn_box">
                    <div class="btn_list green">
                        <div class="btn_item">从Excel导入</div>
                        <div class="btn_item">从外部表单获取</div>
                    </div>
                    <div class="btn green" slot="reference">
                        <img class="img" src="@/assets/images/import.png" alt="">
                        导入客户
                    </div>
                </el-popover>
            </div>

            <div class="btn blue" @click="handleAdd">
                <img class="img" src="@/assets/images/msg2.png" alt="">
                发短信
            </div>
            <div class="btn blue" @click="handleAdd">
                <img class="img" src="@/assets/images/merge.png" alt="">
                客户合并
            </div>
            <div class="btn blue" @click="handleAdd">
                <img class="img" src="@/assets/images/deduplication.png" alt="">
                客户重复检索
            </div>
        </div>

        <!-- 分类 -->
        <div class="classify flex">
            <div class="classify-item" :class="{ active: item.value === classifyName }" v-for="item in classifyOptions"
                :key="item.value" @click="handleCategory(item)">
                {{ item.label }}
            </div>
        </div>

        <!-- 列表 -->
        <div class="tableBox">
            <template v-if="total > 0">
                <el-table v-loading="loading" ref="table" stripe :data="list" :key="key" style="width: 100%"
                    class="custom-table" @selection-change="handleAllSelect">
                    <el-table-column align="center" type="selection" width="50"></el-table-column>
                    <el-table-column align="center" type="index" label="序号" width="50"></el-table-column>
                    <el-table-column align="center" prop="companyName" label="客户名称" show-overflow-tooltip>
                        <template slot-scope="{ row }">
                            <el-button type="text" @click="handleView(row)">{{ row.companyName }}</el-button>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" label="负责人" prop="name" show-overflow-tooltip></el-table-column>
                    <el-table-column align="center" label="成交产品" show-overflow-tooltip>
                        <template slot-scope="{ row }">
                            <el-button type="text">某某产品 ＞</el-button>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" prop="lastTime" label="成交时间"
                        show-overflow-tooltip></el-table-column>
                    <el-table-column align="center" label="录入人" prop="name" show-overflow-tooltip></el-table-column>
                    <el-table-column align="center" prop="phone" label="成交总额(元)" show-overflow-tooltip>
                        <template slot-scope="{ row }">
                            <span style="font-weight: 500; font-size: 14px; color: #F35D09;">100.000.29</span>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" label="已收款(元)" show-overflow-tooltip>
                        <template slot-scope="{ row }">
                            <span style="font-weight: 500; font-size: 14px; color: #31C776;">+ 100.00.00</span>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" label="欠款(元)" show-overflow-tooltip>
                        <template slot-scope="{ row }">
                            <span style="font-weight: 500; font-size: 14px; color: #ED3131;">－ 1260.00</span>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" label="操作" width="220px"
                        v-if="checkPermi(['purchasing:demand:add'])">
                        <template slot-scope="{ row }">
                            <div class="table_btns_box">
                                <el-button type="primary" slot="reference" plain @click="handleOrderView(row)"
                                    size="small">查看详情</el-button>
                                <el-popover placement="bottom" trigger="click" popper-class="btn_box">
                                    <div class="more_btns_box">
                                        <div class="more_btns_left">
                                            <div class="btn_item" v-for="(item, index) in btnsLeftList" :key="index"
                                                :class="index == 1 && moreBtnsRightShow ? 'active' : ''"
                                                @click="handleLeftMoreBtns(index)">
                                                {{ item }}</div>
                                        </div>
                                        <div class="more_btns_right" v-if="moreBtnsRightShow">
                                            <div class="btn_item" v-for="(item, index) in btnsRightList" :key="index">{{
                                                item }}</div>
                                        </div>
                                    </div>
                                    <el-button type="primary" slot="reference" plain size="small">更多操作 ＞</el-button>
                                </el-popover>
                            </div>
                        </template>
                    </el-table-column>
                </el-table>

                <div class="custom-pagination">
                    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
                        :limit.sync="queryParams.pageSize" @pagination="getList" />
                </div>
            </template>
            <el-empty :description="!loading && !total ? '暂无数据' : '加载中…'" v-else />
        </div>

        <addfollow-dialog ref="addFollow" />
        <addorder-dialog ref="addOrder" />
        <adduser-dialog ref="addUser" />
        <addpayment-dialog ref="addPayment" />
        <addtask-dialog ref="addTask" />
        <addapprove-dialog ref="addApprove" />
        <initiatepayment-dialog ref="initiatePayment" />

        <!-- 批量收藏 -->
        <template v-if="multipleSelection.length">
            <div class="collectAll">
                <div class="collectAll-box">
                    <div style="position: relative;">
                        <div class="collectAll-item" @click="batchShow = !batchShow">
                            <img class="img" src="@/assets/images/batch.png" alt="">
                            <span>批量操作</span>
                            <img class="img" src="@/assets/images/down.png" alt="">
                        </div>
                        <div class="batch_box" :class="outBoundShow ? 'active' : ''" v-if="batchShow">
                            <div class="batch_left_list">
                                <div class="batch_item text">批量新建任务</div>
                                <div class="batch_item text">批量修改选中客户</div>
                                <div class="batch_item text">批量修改搜索结果</div>
                                <div class="batch_item text">批量修改全部客户</div>
                                <div class="batch_item" :class="outBoundShow ? 'active' : ''"
                                    @click="outBoundShow = !outBoundShow">
                                    <div class="item_flex">
                                        <span>外呼任务</span>
                                        <i class="el-icon-arrow-right"></i>
                                    </div>
                                </div>
                                <div class="batch_item">
                                    <div class="item_flex">
                                        <span>导出</span>
                                        <i class="el-icon-arrow-right"></i>
                                    </div>
                                </div>
                                <div class="batch_item">
                                    <div class="item_flex">
                                        <span>参与人</span>
                                        <i class="el-icon-arrow-right"></i>
                                    </div>
                                </div>
                                <div class="batch_item">
                                    <div class="item_flex">
                                        <span>销售流程</span>
                                        <i class="el-icon-arrow-right"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="batch_right_list" v-if="outBoundShow">
                                <div class="right_item">批量添加</div>
                                <div class="right_item">批量删除</div>
                            </div>
                        </div>
                    </div>
                    <div style="position: relative;">
                        <div class="collectAll-item" @click="passOnShow = !passOnShow">
                            <img class="img" src="@/assets/images/passOn.png" alt="">
                            <span>转交</span>
                            <img class="img" src="@/assets/images/down.png" alt="">
                        </div>
                        <div class="passOn_box" v-if="passOnShow">
                            <div class="passOn_list">
                                <div class="passOn_title">—— 转交负责人 ——</div>
                                <div class="passOn_item">转交给指定人员</div>
                                <div class="passOn_item">批量转交搜索结果</div>
                            </div>
                            <div class="passOn_list">
                                <div class="passOn_title">—— 转交到公客 ——</div>
                                <div class="passOn_item">转为公共客户</div>
                                <div class="passOn_item">批量转交搜索结果</div>
                            </div>
                        </div>
                    </div>
                    <div class="collectAll-item">
                        <img class="img" src="@/assets/images/delete.png" alt="">
                        <span @click="handleConfirms">删除</span>
                    </div>
                </div>
            </div>
        </template>

       <!-- 客户详情 -->
       <customer-view ref="customerView"></customer-view>

       <!-- 订单详情 -->
       <order-view ref="orderView"></order-view>
    </div>
</template>

<script>
import { checkPermi } from '@/utils/permission'
import addfollowDialog from './addFollow'
import addorderDialog from './addOrder'
import adduserDialog from './addUser'
import addpaymentDialog from './addPayments'
import addtaskDialog from './addTask'
import addapproveDialog from './addApprove'
import initiatepaymentDialog from './initiatePayment'
import customerView from './customerView'
import orderView from './orderView'

export default {
  name: 'CsaleOrder',
    components: { addfollowDialog, addorderDialog, adduserDialog, addpaymentDialog, addtaskDialog, addapproveDialog, initiatepaymentDialog, customerView, orderView },
    data() {
        return {
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                keyword: undefined,
                user: undefined
            },
            key: 1,
            loading: false,
            total: 3,
            list: [{
                companyName: '世盛金属制品有限公司',
                type: 1,
                city: '河北省-邯郸市',
                status: 1,
                name: '张璐瑶',
                phone: '18686868886',
                lastTime: '2024-12-29(周六)09点29分',
                source: 1,
            }, {
                companyName: '世盛金属制品有限公司',
                type: 2,
                city: '河北省-邯郸市',
                status: 1,
                name: '张璐瑶',
                phone: '18686868886',
                lastTime: '2024-12-29(周六)09点29分',
                source: 2,
            }, {
                companyName: '世盛金属制品有限公司',
                type: 1,
                city: '河北省-邯郸市',
                status: 1,
                name: '张璐瑶',
                phone: '18686868886',
                lastTime: '2024-12-29(周六)09点29分',
                source: 3,
            },],

            // 确认需求
            multipleSelection: [],

            // 显示高度
            windowHeight: undefined,
            addHeight: undefined,
            addFormHeight: undefined,
            confirmFormHeight: undefined,
            // 分类
            classifyName: '0',
            classifyOptions: [
                { value: '0', label: '全部' },
                { value: '1', label: '今日成交' },
                { value: '2', label: '已收款' },
                { value: '3', label: '未收款' },
            ],
            // 分类
            tabsActive: '1',
            tabListOptions: [
                { value: '1', label: '按人员查找' },
                { value: '2', label: '按部门查找' },
                { value: '3', label: '最近查看' },
            ],
            userListShow: false,
            userActive: 1,
            userName: '',
            passOnShow: false,
            batchShow: false,
            outBoundShow: false,


            btnsLeftList: ['工商信息', '快捷查看 ＞', '写跟进', '录订单', '新建收款', '新建任务', '发起审批', '关注', '发送', '设置参与人', '修改', '删除', '转交客户', '转为公共客户', '发起收款请求', '自定义'],
            btnsRightList: ['联系跟进 ＞', '客户资料 ＞', '联系人 ＞', '合同 ＞', '订单详情 ＞', '收款 ＞', '退款记录 ＞', '任务 ＞', '审批 ＞', '关联客户 ＞', '短信 ＞', '工单 ＞', '动态 ＞', '评论 ＞'],
            moreBtnsRightShow: false,

        }
    },

    computed: {
        companyId() {
            return this.$store.getters.info.companyId
        }
    },
    watch: {
        windowHeight(val) {
            this.addHeight = val * 0.94 - 305
            this.addFormHeight = val * 0.94 - 367
            this.confirmFormHeight = val * 0.94 - 485
        }
    },
    mounted() {
        this.windowHeight = document.documentElement.clientHeight
        window.onresize = () => {
            this.windowHeight = document.documentElement.clientHeight
        }
    },
    methods: {
        checkPermi,
        // 切换分类
        handleCategory(item) {
            this.queryParams.keyword = undefined
            this.classifyName = item.value
        },
        // 查询
        handleQuery() {
            this.queryParams.pageNum = 1
            this.getList()
        },
        getList() { },
        // 新增
        handleAdd(data) { },
        // 全部列表批量选择
        handleAllSelect(val) {
            this.multipleSelection = val;
        },
        // 批量生成需求
        handleConfirms(row = {}) { },
        // 生成需求
        handleMore(row) { },
        // 选择用户
        handleUserSelect() {
            this.userListShow = !this.userListShow
        },
        // 切换
        handleTabList(item) {
            this.tabsActive = item.value
        },
        // 查看详情
        handleView() {
            this.$refs.customerView.handleOpen()
        },
        // 查看详情
        handleOrderView() {
            this.$refs.orderView.handleOpen()
        },

        handleLeftMoreBtns(index) {
            if (index == 1) {
                this.moreBtnsRightShow = true
            } else {
                this.moreBtnsRightShow = false
                if (index == 2) {
                    this.$refs.addFollow.handleAdd()
                }
                if (index == 3) {
                    this.$refs.addOrder.handleAdd()
                }
                if (index == 4) {
                    this.$refs.addPayment.handleAdd()
                }
                if (index == 5) {
                    this.$refs.addTask.handleAdd()
                }
                if (index == 6) {
                    this.$refs.addApprove.handleAdd()
                }
                if (index == 14) {
                    this.$refs.initiatePayment.handleAdd()
                }
            }
        },
        handleAddUser() {
            this.$refs.addUser.handleAdd()
        },
        handleAddPayment() {
            this.$refs.addPayment.handleAdd()
        },
    }
}
</script>

<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
@import '~@/assets/styles/crm.scss';

.custom-search {
    padding-top: 10px;
    padding-bottom: 10px;
    background-color: transparent;
}

.select_box {
    margin-left: 15px;
    position: relative;

    .ipt {
        ::v-deep {
            .el-input__inner {
                cursor: pointer;
                height: 32px;
            }
        }
    }

    .user_list_box {
        position: absolute;
        left: 0;
        top: 35px;
        z-index: 999;
        width: 324px;
        height: 590px;
        background: #FFFFFF;
        box-shadow: 0px 1px 15px 0px rgba(0, 0, 0, 0.25);
        border-radius: 5px;
        border: 1px solid #2E73F3;

        .tab_box {
            background: #f5f5f5;
            padding: 5px 5px 0;
            height: 52px;
            display: flex;
            align-items: center;
            border-bottom: 1px solid #CBD6E2;

            .tabs_item {
                flex: 1;
                text-align: center;
                height: 45px;
                line-height: 45px;
                cursor: pointer;
                font-weight: 400;
                font-size: 12px;
                color: #666666;

                &.avtive {
                    background: #ffffff;
                    border: 1px solid #CBD6E2;
                    border-bottom: 1px solid #ffffff;
                    border-radius: 5px;
                    position: relative;
                    top: 2px;
                    z-index: 1;
                    font-weight: 500;
                    font-size: 16px;
                    color: #333333;
                }
            }
        }

        .tab_list {
            border-bottom: 1px solid #E1E2E3;
            padding: 12px 0 0px;
            display: flex;
            align-items: center;

            .tab_list_item {
                padding: 0 15px;
                font-weight: 400;
                font-size: 12px;
                color: #666666;
                cursor: pointer;
                display: flex;
                flex-direction: column;
                align-items: center;

                .bbLine {
                    width: 50px;
                    height: 2px;
                    background: #2E73F3;
                }

                &.active {
                    font-weight: 500;
                    font-size: 14px;
                    color: #2E73F3;
                }
            }
        }

        .user_list {
            padding: 0 10px;

            .user_list_search {
                padding-top: 10px;
                padding-bottom: 5px;

                .ipt {
                    ::v-deep {
                        .el-input__inner {
                            height: 32px;
                        }

                        .btn {
                            margin: 3px 0;
                            width: 66px;
                            height: 26px;
                            border-radius: 5px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            font-weight: 500;
                            font-size: 12px;
                            margin-left: 15px;
                            cursor: pointer;

                            .img {
                                width: 20px;
                                height: 20px;
                                margin-right: 5px;
                            }

                            &.blue_ {
                                color: #2E73F3;
                                background: #E1EBFF;
                                border-radius: 5px;
                            }
                        }
                    }
                }
            }

            .user_list_concent {
                height: 400px;
                overflow-y: scroll;

                .user_list_item {
                    padding: 6px 10px;
                    border-bottom: 1px solid #E1E2E3;
                    display: flex;
                    align-items: center;

                    .img {
                        width: 38px;
                        height: 38px;
                        margin-right: 10px;
                    }

                    .text {
                        flex: 1;

                        .name {
                            font-weight: 500;
                            font-size: 14px;
                            color: #333333;
                            line-height: 20px;
                        }

                        .dept {
                            display: flex;
                            align-items: center;
                            justify-content: space-between;
                            font-weight: 400;
                            font-size: 12px;
                            color: #666666;
                        }
                    }
                }
            }
        }

        .user_btn {
            background: #F5F5F5;
            display: flex;
            align-items: center;
            height: 50px;

            div {
                flex: 1;
                display: flex;
                align-items: center;
                justify-content: center;

                &.refresh {
                    font-weight: 500;
                    font-size: 12px;
                    color: #999999;
                    line-height: 20px;

                    .img {
                        width: 20px;
                        height: 20px;
                        margin-right: 4px;
                    }
                }

                &.setting {
                    font-weight: 500;
                    font-size: 12px;
                    color: #2E73F3;
                    line-height: 20px;

                    .img {
                        width: 18px;
                        height: 18px;
                        margin-right: 4px;
                    }
                }
            }
        }
    }
}

.btn {
    width: 124px;
    height: 32px;
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    font-size: 12px;
    margin-left: 15px;
    cursor: pointer;

    .img {
        width: 20px;
        height: 20px;
        margin-right: 5px;
    }

    &.blue_all {
        color: #FFFFFF;
        background: #2E73F3;
        border: 1px solid #2E73F3;
    }

    &.blue {
        color: #2E73F3;
        background: #FFFFFF;
        border: 1px solid #2E73F3;
    }

    &.green {
        color: #1DC86B;
        background: #FFFFFF;
        border: 1px solid #1DC86B;
    }
}


.btn_list {
    width: 124px;
    background: #FFFFFF;
    box-shadow: 0px 1px 15px 0px rgba(0, 0, 0, 0.25);
    border-radius: 5px;
    font-weight: 400;
    font-size: 14px;
    color: #666666;

    .btn_item {
        height: 36px;
        line-height: 36px;
        cursor: pointer;
    }

    &.blue {
        border: 1px solid #2E73F3;

        .btn_item {
            padding-left: 20px;

            &:hover {
                background: #2E73F3;
                color: #ffffff;
            }
        }
    }

    &.green {
        border: 1px solid #1DC86B;

        .btn_item {
            padding-left: 14px;

            &:hover {
                background: #1DC86B;
                color: #ffffff;
            }
        }
    }
}

.tableBox {
    margin: 15px 10px 20px;
    padding: 0 10px 20px;
    background-color: $white;
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
}

.table_btns_box {
    display: flex;
    align-items: center;
    justify-content: space-around;
}

.more_btns_box {
    width: 246px;
    height: 600px;
    background: #FFFFFF;
    box-shadow: 0px 1px 15px 0px rgba(0, 0, 0, 0.25);
    border-radius: 5px;
    border: 1px solid #2E73F3;
    display: flex;
    align-items: flex-start;

    .more_btns_left {
        flex: 1;
        padding: 10px 0;
        height: 100%;

        .btn_item {
            font-weight: 400;
            font-size: 14px;
            height: 36px;
            cursor: pointer;
            color: #666666;
            padding-left: 20px;
            line-height: 36px;

            &:hover {
                background: #2E73F3;
                color: #FFFFFF;
            }

            &.active {
                background: #2E73F3;
                color: #FFFFFF;
            }
        }
    }

    .more_btns_right {
        flex: 1;
        height: 100%;
        padding: 10px 0;
        background: #F0F3F9;

        .btn_item {
            font-weight: 400;
            font-size: 14px;
            height: 36px;
            cursor: pointer;
            color: #666666;
            padding-left: 20px;
            line-height: 36px;

            &:hover {
                background: #8B9DBE;
                color: #FFFFFF;
            }
        }
    }
}


::v-deep {
    .collectAll {
        bottom: 100px;
        left: calc(50% - 200px);
        position: fixed;
        z-index: 50;
        transform: translateX(calc(-50% + 200px));
        -webkit-user-select: none;
        -ms-user-select: none;
        user-select: none;
        transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;

        &-box {
            background: rgba(52, 54, 57, 1);
            border-radius: 12px;
            box-shadow: 0 -2px 8px rgba(38, 38, 38, 0.05), 0 10px 16px rgba(38, 38, 38, 0.08);
            height: 60px;
            padding: 5px 8px;
            position: relative;
            display: flex;
            align-items: center;
            color: #ffffff;
            font-size: 14px;
        }

        &-item {
            display: flex;
            align-items: center;
            padding: 15px 12px;
            cursor: pointer;

            &:hover {
                background: #555B73;
                border-radius: 10px;
            }

            .img {
                width: 20px;
                height: 20px;
                margin: 0 10px 0 3px;
            }
        }

        .batch_box {
            position: absolute;
            top: -323px;
            left: -12px;
            width: 180px;
            height: 308px;
            background: #292F48;
            box-shadow: 0px 1px 15px 0px rgba(0, 0, 0, 0.25);
            border-radius: 10px;
            padding: 15px 10px;
            padding-right: 0;
            display: flex;

            &::before {
                position: absolute;
                content: "";
                width: 0;
                height: 0;
                bottom: -5px;
                left: 90px;
                border-top: solid 5px #292F48;
                border-right: solid 5px transparent;
                border-left: solid 5px transparent;
            }

            .batch_left_list {
                // flex: 1;

                .batch_item {
                    font-weight: 400;
                    font-size: 12px;
                    color: #FFFFFF;
                    line-height: 14px;
                    cursor: pointer;
                    padding-right: 10px;

                    &.text {
                        padding: 10px;
                        width: 160px;
                        margin-right: 10px;

                        &:hover {
                            background: #FFFFFF;
                            box-shadow: 0px 1px 15px 0px rgba(0, 0, 0, 0.25);
                            border-radius: 5px;
                            font-weight: 500;
                            color: #292F48;
                        }
                    }

                    .item_flex {
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        padding: 10px;

                        &:hover {
                            background: #FFFFFF;
                            box-shadow: 0px 1px 15px 0px rgba(0, 0, 0, 0.25);
                            border-radius: 5px;
                            font-weight: 500;
                            color: #292F48;
                        }
                    }

                    &.active {
                        position: relative;
                        background: #FFFFFF;
                        box-shadow: none;
                        border-top-left-radius: 5px;
                        border-bottom-left-radius: 5px;
                        font-weight: 500;
                        color: #292F48;

                        .item_flex {
                            &:hover {
                                box-shadow: none;
                                border-radius: none;
                            }
                        }
                    }
                }
            }

            &.active {
                width: 360px;
                padding-right: 10px;

                .batch_right_list {
                    flex: 1;
                    height: 283px;
                    background: #ffffff;
                    border-radius: 10px;
                    box-shadow: 0px 1px 15px 0px rgba(0, 0, 0, 0.25);
                    padding: 10px;

                    .right_item {
                        font-weight: 400;
                        font-size: 12px;
                        color: #292F48;
                        line-height: 14px;
                        padding: 10px 0;
                        text-align: center;
                        cursor: pointer;

                        &:hover {
                            background: #2E73F3;
                            border-radius: 5px;
                            color: #FFFFFF;
                            font-weight: 500;
                        }
                    }
                }
            }
        }

        .passOn_box {
            position: absolute;
            top: -255px;
            left: -39px;
            width: 180px;
            height: 239px;
            background: #292F48;
            box-shadow: 0px 1px 15px 0px rgba(0, 0, 0, 0.25);
            border-radius: 10px;
            padding: 15px 10px;

            &::before {
                position: absolute;
                content: "";
                width: 0;
                height: 0;
                bottom: -5px;
                left: 90px;
                border-top: solid 5px #292F48;
                border-right: solid 5px transparent;
                border-left: solid 5px transparent;
            }

            .passOn_list {
                .passOn_title {
                    font-weight: 400;
                    font-size: 12px;
                    color: #ADB1C0;
                    line-height: 14px;
                    padding: 10px 0;
                    text-align: center;
                }

                .passOn_item {
                    font-weight: 400;
                    font-size: 12px;
                    color: #FFFFFF;
                    line-height: 14px;
                    padding: 10px 0;
                    text-align: center;
                    cursor: pointer;

                    &:hover {
                        background: #FFFFFF;
                        box-shadow: 0px 1px 15px 0px rgba(0, 0, 0, 0.25);
                        border-radius: 5px;
                        font-weight: 500;
                        color: #292F48;
                    }
                }
            }
        }
    }
}

</style>

<style>
.btn_box.el-popper[x-placement^=bottom] {
    min-width: 0;
    padding: 0 !important;
}
</style>
