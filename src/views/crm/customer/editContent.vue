<template>
    <div>
        <el-dialog v-dialogDragBox title="阶段完成后填写内容编辑设置" :visible.sync="open" width="1152px" class="custom-dialog">
            <div style="padding: 0 20px">
                <div
                    style="height: 32px; background: #FFEBE0; border-radius: 5px; font-weight: 400; font-size: 12px; color: #F35D09; line-height: 32px; padding-left: 15px;">
                    《验证客户》阶段已完成，请填写必填项内容</div>
                <el-form ref="form" :model="form" :rules="rules" label-width="8em" label-position="left" style="border-bottom: 1px solid #E2E6F3;">
                    <el-row :gutter="20" style="display: flex; flex-wrap: wrap">
                        <el-col :span="24">
                            <div style="padding: 20px 0;">
                                <div
                                    style="display: flex; align-items: center; justify-content: space-between; padding-bottom: 8px; border-bottom: 1px solid #E1E2E2;">
                                    <div>填写内容</div>
                                    <div @click="handleContactSetting"
                                        style="font-weight: 400; font-size: 14px; color: #666666; display: flex; align-items: center; cursor: pointer;">
                                        <img style="width: 20px; height: 20px; margin-right: 3px;"
                                            src="@/assets/images/setting.png" alt="">
                                        自定义
                                    </div>
                                </div>
                            </div>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label="说明" prop="remark">
                                <el-input v-model="form.remark" placeholder="请输入说明详细" type="textarea"
                                    :autosize="{ minRows: 4, maxRows: 4 }" resize="none" class="custom-textarea" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label="图片" prop="picture1">
                                <image-upload v-model="form.picture1" :file-type="fileType" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label="" prop="picture1">
                                <el-switch style="margin-right: 5px;" v-model="value" active-color="#2E73F3"
                                    inactive-color="#ff4949">
                                </el-switch>
                                <span>同步创建跟进记录</span>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
                <div slot="footer" style="margin-top: 20px; text-align: right;">
                    <button type="button" class="custom-dialog-btn" @click="handleCancel">取消</button>
                    <button type="button" class="custom-dialog-btn primary" @click="handleSumit">确定保存</button>
                </div>
            </div>
        </el-dialog>

        <concentsetting-dialog ref="contactSetting" />
    </div>
</template>

<script>
import concentsettingDialog from './concentSetting'
export default {
    components: { concentsettingDialog },
    data() {
        return {
            open: false,
            form: {},
            rules: {},
            fileType: ['png', 'PNG', 'jpg', 'JPG', 'jpeg', 'JPEG'],
            value: ''
        }
    },
    methods: {
        handleOpen() {
            this.open = true
        },
        handleContactSetting() {
            this.$refs.contactSetting.handleOpen()
        },
        handleCancel() {

        },
        handleSumit() {
            
        }
    }
}
</script>

<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
</style>