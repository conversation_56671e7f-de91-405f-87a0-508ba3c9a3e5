<template>
    <div>
        <el-dialog v-dialogDragBox :title="title" :visible.sync="open" width="1152px" class="custom-dialog">
            <div class="order_box">
                <el-form ref="form" :model="form" :rules="rules" label-width="8em" label-position="left">
                    <el-row :gutter="20" style="display: flex; flex-wrap: wrap">
                        <el-col :span="24">
                            <div
                                style="border-radius: 5px; border: 1px solid #F02323; margin: 0 20px; display: flex; align-items: center; flex-wrap: wrap;">
                                <div
                                    style="margin-bottom: 30px; width: 100%; height: 62px; background: #FFE3E3; border-bottom: 1px solid #F02323; line-height: 62px; text-align: center; font-weight: 500; font-size: 18px; color: #F02323;">
                                    退款</div>
                                <div style="width: 50%;">
                                    <el-form-item label="选择客户" prop="productName">
                                        <el-select v-model="form.unit" placeholder="请选择客户" style="width: 100%;">
                                            <el-option v-for="(item, index) in options" :key="index" :label="item"
                                                :value="item"></el-option>
                                        </el-select>
                                    </el-form-item>
                                </div>
                                <div style="width: 50%;">
                                    <el-form-item label="联系人" prop="productName">
                                        <el-select v-model="form.unit" placeholder="请选择联系人" style="width: 100%;">
                                            <el-option v-for="(item, index) in options" :key="index" :label="item"
                                                :value="item"></el-option>
                                        </el-select>
                                    </el-form-item>
                                </div>
                                <div style="width: 100%;">
                                    <el-form-item label="负责人" prop="categoryId">
                                        <div style="display: flex; align-items: center;">
                                            <el-radio-group v-model="radio" style="display: flex; align-items: center;">
                                                <el-radio :label="1">客户负责人</el-radio>
                                                <el-radio :label="2">订单负责人</el-radio>
                                                <el-radio :label="3">指定人员</el-radio>
                                            </el-radio-group>
                                            <div style="margin-left: 20px;">
                                                <el-tag closable :disable-transitions="false"
                                                    @close="handleClose(tag)">某某某</el-tag>
                                                <el-button type="text" icon="el-icon-plus"
                                                    style="margin-left: 15px;">选择人员</el-button>
                                            </div>
                                        </div>
                                    </el-form-item>
                                </div>
                                <div style="width: 50%;">
                                    <el-form-item label="退款时间" prop="supplyEndTime">
                                        <el-date-picker v-model="form.supplyEndTime" type="date"
                                            value-format="yyyy-MM-dd HH:mm:ss" placeholder="请选择退款时间"
                                            style="width: 100%"></el-date-picker>
                                    </el-form-item>
                                </div>
                                <div style="width: 50%;">
                                    <el-form-item label="退款金额" prop="productName">
                                        <el-input v-model="form.productName" placeholder="请输入退款金额" />
                                    </el-form-item>
                                </div>
                            </div>
                        </el-col>
                        <el-col :span="24">
                            <div style="font-weight: 400; font-size: 12px; color: #666666; margin: 17px 20px;">
                                发现此客户有
                                <span style="font-weight: 500; font-size: 14px; color: #2E73F3;">433</span>
                                笔未收款的记录，系统已将收款自动分配完成，以完成收款
                            </div>
                        </el-col>
                        <el-col :span="24">
                            <div class="table_list">
                                <div class="form_table">
                                    <el-form-item label="" label-width="0" prop="products">
                                        <el-table :data="form.products" style="width: 100%;" class="custom-table">
                                            <el-table-column type="index" label="序号" width="50"></el-table-column>
                                            <el-table-column prop="productName" label="销售订单" align="center"
                                                show-overflow-tooltip>
                                                <template slot-scope="{ row }">
                                                    <span class="table-link" @click="handleView(row.id, row)">{{
                                                        row.productName
                                                        }}</span>
                                                </template>
                                            </el-table-column>
                                            <!-- <el-table-column label="产品图片" align="center" show-overflow-tooltip>
                                                <template slot-scope="{ row }">
                                                    <div style="display: flex; justify-content: center">
                                                        <el-image :src="formatProductImg(row)" fit="cover"
                                                            @click="handleImgView(row)">
                                                            <div slot="error" class="image-slot">
                                                                <i class="el-icon-picture-outline"></i>
                                                            </div>
                                                        </el-image>
                                                    </div>
                                                </template>
                                            </el-table-column> -->
                                            <el-table-column prop="model" label="成交时间" align="center"
                                                show-overflow-tooltip></el-table-column>
                                            <el-table-column prop="model" label="成交总额(元)" align="center"
                                                show-overflow-tooltip></el-table-column>
                                            <el-table-column prop="model" label="欠款(元)" align="center"
                                                show-overflow-tooltip>
                                                <template slot-scope="{ row }">
                                                    <span style="color: #F35D09;">100.000.29</span>
                                                </template>
                                            </el-table-column>
                                            <el-table-column prop="model" label="本次退款(元)" align="center"
                                                show-overflow-tooltip>
                                                <template slot-scope="{ row }">
                                                    <el-input size="small" placeholder="请输入内容" v-model="input3">
                                                    </el-input>
                                                </template>
                                            </el-table-column>
                                            <el-table-column prop="model" label="备注" align="center"
                                                show-overflow-tooltip>
                                                <template slot-scope="{ row }">
                                                    <el-input size="small" placeholder="请输入内容" v-model="input3">
                                                    </el-input>
                                                </template>
                                            </el-table-column>
                                            <el-table-column label="操作" align="center">
                                                <template slot-scope="scope">
                                                    <el-button type="danger" icon="el-icon-delete" size="mini"
                                                        plain>删除</el-button>
                                                </template>
                                            </el-table-column>
                                        </el-table>
                                    </el-form-item>
                                </div>
                                <div class="table_total">
                                    <div class="total_left">
                                        <div class="left_item">
                                            共
                                            <span>1688</span>
                                            个订单
                                        </div>
                                        <div class="left_item">
                                            成交总额
                                            <span>100251,596.1345</span>
                                        </div>
                                    </div>
                                    <div class="total_right">
                                        欠款
                                        <span style="color: #F35D09;">10.165000.000.00</span>
                                    </div>
                                </div>
                            </div>
                        </el-col>
                        <el-col :span="24">
                            <div style="padding: 0 20px 20px;">
                                <div
                                    style="display: flex; align-items: center; justify-content: space-between; padding-bottom: 8px; border-bottom: 1px solid #E1E2E2;">
                                    <div>其他</div>
                                    <div
                                        style="display: flex; align-items: center; font-weight: 400; font-size: 14px; color: #666666;">
                                        <div style="display: flex; align-items: center; cursor: pointer; margin-right: 10px;"
                                            @click="handlePaymentApprove">
                                            <img style="width: 20px; height: 20px; margin-right: 2px;"
                                                src="@/assets/images/shenpi.png" alt="">
                                            审批设置
                                        </div>
                                        <div style="display: flex; align-items: center; cursor: pointer;"
                                            @click="handlePaymentConfig">
                                            <img style="width: 20px; height: 20px; margin-right: 2px;"
                                                src="@/assets/images/setting.png" alt="">
                                            自定义
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="付款方式">
                                <div class="create-user-form-item">
                                    <el-select v-model="form.customerType" placeholder="请选择付款方式">
                                        <el-option v-for="item in customerTypeData" :key="item.name" :label="item.name"
                                            :value="item.name"></el-option>
                                    </el-select>
                                    <el-popover ref="customerTypePopover" trigger="click" popper-class="crm-popover"
                                        @after-leave="customerTypeShow = false">
                                        <div class="create-config" slot="reference" @click="customerTypeShow = true">
                                            <i class="el-icon-edit-outline"></i>
                                            <span>编辑</span>
                                        </div>
                                        <config-table title="付款方式" :config-data="customerTypeData"
                                            config-type="Customer_Type"
                                            @close="handleClosePopover('customerTypePopover')"
                                            @update="handleUpdateConfigData('customerTypeData', $event)"
                                            v-if="customerTypeShow"></config-table>
                                    </el-popover>
                                </div>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label="公司备注" prop="remark">
                                <el-input v-model="form.remark" placeholder="请输入公司备注" type="textarea"
                                    :autosize="{ minRows: 4, maxRows: 4 }" resize="none" class="custom-textarea" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label="收款凭证" prop="categoryId">
                                <image-upload v-model="form.picture1" :file-type="fileType" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label-width="0" prop="productName">
                                <el-switch style="margin-right: 5px;" v-model="value" active-color="#2E73F3"
                                    inactive-color="#ff4949">
                                </el-switch>
                                <span>@其他成员</span>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24" class="next_follow_box">
                            <el-col :span="12">
                                <el-form-item label="@成员" prop="categoryId" style="margin-bottom: 0;">
                                    <el-tag closable :disable-transitions="false" @close="handleClose(tag)">某某某</el-tag>
                                    <el-button type="text" icon="el-icon-plus"
                                        style="margin-left: 15px;">选择人员</el-button>
                                </el-form-item>
                            </el-col>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
            <div slot="footer">
                <button type="button" class="custom-dialog-btn" @click="handleCancel">取消</button>
                <button type="button" class="custom-dialog-btn primary" @click="handleSumit">确定</button>
            </div>
        </el-dialog>

        <paymentsconfig-dialog ref="paymentsConfig" />
        <paymentsapprove-dialog ref="paymentsApprove" />
    </div>
</template>

<script>
import paymentsconfigDialog from './paymentsConfig'
import paymentsapproveDialog from './paymentsApprove'
import ConfigTable from './create/config'

export default {
    name: 'AddRefund',
    components: { paymentsconfigDialog, paymentsapproveDialog, ConfigTable },
    data() {
        return {
            title: '新建退款记录',
            form: {
                products: [{
                    productName: '某某产品',
                    model: 'DN150*168*40*40',
                    quantity: undefined
                }]
            },
            rules: {},
            open: false,
            loading: false,
            fileType: ['png', 'PNG', 'jpg', 'JPG', 'jpeg', 'JPEG', 'pdf'],
            key: 1,
            radio: undefined,
            options: [],
            activeName: 'second',
            value: undefined,
            unitOptions: [],
            userTypeActive: '1',
            timeType: 1,
            input1: '',
            customerTypeData: [],
            customerTypeShow: false,
        }
    },
    created() {
        this.customerTypeData = [
            {
                name: '现金支付',
                color: '#2E74F3',
                hidden: false,
                isActive: false
            },
            {
                name: '银行转账',
                color: '#2E74F3',
                hidden: false,
                isActive: true
            },
            {
                name: '其他',
                color: '#2E74F3',
                hidden: false,
                isActive: true
            }
        ]
    },
    methods: {
        reset() {
            this.form = {
                products: [{
                    productName: '某某产品',
                    model: 'DN150*168*40*40',
                    quantity: undefined
                }]
            }
            this.resetForm('form')
        },
        handleAdd() {
            this.reset()
            this.title = '新建退款记录'
            this.open = true
        },
        // 取消
        handleCancel() {
            this.open = false
            this.reset()
        },
        // 提交
        handleSumit() {
            this.$refs['form'].validate(valid => {
                if (valid) {
                    if (this.form.id != null) {
                        this.form.productId = this.form.id
                        updateUnsalable(this.form).then(res => {
                            if (res.code === 200) {
                                this.$message.success('修改成功')
                                this.open = false
                                this.$parent.getList()
                            } else {
                                this.$message.error(res.msg)
                            }
                        })
                    } else {
                        addUnsalable(this.form).then(res => {
                            if (res.code === 200) {
                                this.$message.success('新增成功')
                                this.open = false
                                this.$parent.getList()
                            } else {
                                this.$message.error(res.msg)
                            }
                        })
                    }
                }
            })
        },
        handleClose() { },
        handlePaymentConfig() {
            this.$refs.paymentsConfig.handleOpen()
        },
        handlePaymentApprove() {
            this.$refs.paymentsApprove.handleOpen()
        },
        // 关闭Popover弹窗
        handleClosePopover(name) {
            this.$refs[name].doClose()
        },
        // 更新配置数据
        handleUpdateConfigData(name, data) {
            if (name === 'customerTypeData') {
                this.customerTypeData = JSON.parse(JSON.stringify(data))
                const obj = this.customerTypeData.find(item => item.name === this.form.customerType)
                if (!obj) this.form.customerType = ''
            } else if (name === 'customerStatusData') {
                this.customerStatusData = JSON.parse(JSON.stringify(data))
                const obj = this.customerStatusData.find(item => item.name === this.form.customerStatus)
                if (!obj) this.form.customerStatus = ''
            }
        },
    }
}
</script>
<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
@import '~@/assets/styles/crm.scss';

.ipt {
    ::v-deep {
        .el-input__inner {
            cursor: pointer;
        }
    }
}

.order_box {
    padding: 0;

    ::v-deep {
        .el-form-item {
            margin-left: 20px;
        }

        .el-form-item__content {
            padding-right: 20px;
        }

        .el-divider--vertical {
            height: 2em;
        }
    }

    .table_list {
        padding: 20px;
        margin-bottom: 20px;

        .form_table {
            ::v-deep {
                .el-form-item {
                    margin-left: 0;
                }
            }
        }

        .table_total {
            margin-top: 10px;
            height: 48px;
            background: #EFF5FF;
            border-radius: 5px;
            border: 1px solid #2E73F3;
            display: flex;
            align-items: center;
            justify-content: space-between;

            .total_left {
                padding-left: 30px;
                display: flex;
                align-items: center;

                .left_item {
                    font-weight: 400;
                    font-size: 14px;
                    color: #666666;
                    margin-right: 90px;
                    display: flex;
                    align-items: center;

                    span {
                        font-weight: 500;
                        font-size: 18px;
                        color: #2E73F3;
                        margin: 0 10px;
                    }
                }
            }

            .total_right {
                margin-right: 145px;
                font-weight: 400;
                font-size: 14px;
                color: #666666;
                display: flex;
                align-items: center;

                span {
                    font-weight: 500;
                    font-size: 18px;
                    color: #F35D09;
                    margin-left: 10px;
                }
            }
        }

        .flex_box {
            display: flex;
            align-items: center;
            justify-content: space-around;

            span {
                margin-bottom: 22px;
                color: #2E73F3;
                font-weight: 400;
                font-size: 12px;
            }
        }
    }

    .border_no {
        ::v-deep {
            .el-input__inner {
                border: none;
            }
        }
    }
}

.customer_list_box {
    width: 444px;
    height: 538px;
    background: #FFFFFF;
    box-shadow: 0px 1px 15px 0px rgba(0, 0, 0, 0.25);
    border-radius: 5px;
    border: 1px solid #2E73F3;

    ::v-deep {
        .el-tabs__header {
            margin-bottom: 0;

            .el-tabs__nav-wrap {
                padding: 0 15px;
            }
        }
    }

    .dept_box {
        display: flex;

        .dept_left {
            // width: 164px;
        }

        .dept_right {
            // flex: 1;
        }
    }
}

.listInfo_tabs_box {
    background: #F2F3F9;
    position: relative;
    margin: 0 20px 20px;

    .listInfo_tabs_left {
        position: relative;
    }

    ::v-deep {
        .el-tabs--border-card {
            box-shadow: none;
            border: none;
        }

        .el-tabs__content {
            padding: 0;
        }

        .el-tabs--border-card>.el-tabs__header {
            background: #F2F3F9;
            border-bottom: 1px solid #CBD6E2;

            .el-tabs__item {
                color: #666666;

                &.is-active {
                    color: #2E73F3;
                    border-top-color: #CBD6E2;
                    border-right-color: #CBD6E2;
                    border-left-color: #CBD6E2;
                    margin-top: 0;
                    margin-left: 0;
                    border-top-left-radius: 5px;
                    border-top-right-radius: 5px;
                }
            }
        }
    }

    .listInfo_tabs_right {
        font-weight: 400;
        font-size: 14px;
        color: #666666;
        display: flex;
        align-items: center;
        cursor: pointer;
        position: absolute;
        right: 20px;
        top: 10px;

        img {
            width: 20px;
            height: 20px;
            margin-right: 2px;
        }
    }
}

.next_follow_box {
    background: #F0F3F9;
    padding: 20px;
    margin-bottom: 20px;
}

.time_box {
    ::v-deep {
        .el-select {
            .el-input--suffix {
                height: 32px;

                .el-input__inner {
                    height: 32px;
                }

                .el-input__icon {
                    line-height: 32px;
                }
            }
        }
    }
}
</style>
