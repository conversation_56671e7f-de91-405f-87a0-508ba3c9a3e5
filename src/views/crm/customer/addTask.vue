<template>
    <div>
        <el-dialog v-dialogDragBox :title="title" :visible.sync="open" width="1152px" class="custom-dialog">
            <div style="padding: 0 20px">
                <el-form ref="form" :model="form" :rules="rules" label-width="7em">
                    <el-row :gutter="20" style="display: flex; flex-wrap: wrap">
                        <el-col :span="12">
                            <el-form-item label="选择客户" prop="categoryId">
                                <div @click="handleChecked" class="ipt border_no">
                                    <el-input v-model="form.linkName" placeholder="请选择客户" readonly class="ipt">
                                        <i slot="suffix" class="el-input__icon el-icon-arrow-down"></i>
                                    </el-input>
                                </div>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="任务优先级" prop="priority">
                                <el-select v-model="form.priority" placeholder="请选择任务优先级" style="width: 100%">
                                    <el-option v-for="(item, index) in priorityList" :key="index" :label="item.label"
                                        :value="item.value"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="执行人" prop="exeUserId">
                                <el-cascader v-model="form.exeUserId" :options="approvalsOptions" style="width: 100%"
                                    :props="approvalsProps" filterable :show-all-levels="false"
                                    placeholder="请选择执行人"></el-cascader>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="任务执行时间" prop="executeDate">
                                <el-date-picker v-model="form.executeDate" type="datetime"
                                    value-format="yyyy-MM-dd HH:mm" placeholder="请选择任务执行时间"
                                    style="width: 100%"></el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label="任务内容" prop="taskContent">
                                <el-input v-model="form.taskContent" placeholder="请输入任务内容" type="textarea"
                                    :autosize="{ minRows: 4, maxRows: 4 }" resize="none" class="custom-textarea" />
                            </el-form-item>
                        </el-col>

                        <el-col :span="12">
                            <el-form-item label="任务截止时间" prop="deadline">
                                <el-date-picker v-model="form.deadline" type="datetime"
                                    value-format="yyyy-MM-dd HH:mm:ss" placeholder="请选择任务截止时间"
                                    style="width: 100%"></el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="重复周期" prop="cycleMode">
                                <el-select v-model="form.cycleMode" placeholder="请选择重复周期" style="width: 100%">
                                    <el-option v-for="(item, index) in cycleList" :key="index" :label="item.label"
                                        :value="item.value"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12"
                            v-if="form.cycleMode == '2' || form.cycleMode == '3' || form.cycleMode == '4'">
                            <el-form-item label="选择开始时间" prop="supplyEndTime">
                                <el-date-picker v-model="form.supplyEndTime" type="date" value-format="yyyy-MM-dd"
                                    placeholder="请选择选择开始时间" style="width: 100%"></el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12" v-if="form.cycleMode == '2'">
                            <el-form-item label="每周重复日期" prop="supplyEndTime">
                                <el-time-picker v-model="value" :picker-options="{
                                    selectableRange: '00:00:00 - 23:59:59'
                                }" value-format="HH:mm:ss"></el-time-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12" v-if="form.cycleMode == '3'">
                            <el-form-item label="每月重复日期" prop="supplyEndTime">
                                <el-time-picker v-model="value" :picker-options="{
                                    selectableRange: '00:00:00 - 23:59:59'
                                }" value-format="HH:mm:ss"></el-time-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12" v-if="form.cycleMode == '4'">
                            <el-form-item label="每年重复日期" prop="supplyEndTime">
                                <el-time-picker v-model="value" :picker-options="{
                                    selectableRange: '00:00:00 - 23:59:59'
                                }" value-format="HH:mm:ss"></el-time-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="提醒负责人">
                                <div class="create-user-form-item">
                                    <el-select v-model="form.tipsNum" placeholder="请选择提醒负责人">
                                        <el-option v-for="item in customerTypeData" :key="item.name" :label="item.name"
                                            :value="item.value"></el-option>
                                    </el-select>
                                    <!-- <el-popover ref="customerTypePopover" trigger="click" popper-class="crm-popover"
                                        @after-leave="customerTypeShow = false">
                                        <div class="create-config" slot="reference" @click="customerTypeShow = true">
                                            <i class="el-icon-setting"></i>
                                            <span>自定义</span>
                                        </div>
                                        <config-table title="提醒负责人" :config-data="customerTypeData"
                                            config-type="Customer_Type"
                                            @close="handleClosePopover('customerTypePopover')"
                                            @update="handleUpdateConfigData('customerTypeData', $event)"
                                            v-if="customerTypeShow"></config-table>
                                    </el-popover> -->
                                </div>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12"></el-col>
                        <el-col :span="12">
                            <el-form-item label="抄送给" prop="ccUsers">
                                <el-tag closable @close="handleCloseTag(item)" class="crm-tag border-none"
                                    v-for="item in form.ccUsers" :key="item.userId">{{ item.realName || item.nickName
                                    }}</el-tag>
                                <el-button type="text" icon="el-icon-plus" style="margin-left: 15px"
                                    @click="handleContactShow('cc', form.ccUsers)">选择人员</el-button>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12"></el-col>
                        <el-col :span="24">
                            <el-form-item label="附件" prop="attachment">
                                <image-upload v-model="form.attachment" :file-type="fileType" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
            <div slot="footer">
                <button type="button" class="custom-dialog-btn" @click="handleCancel">取消</button>
                <button type="button" class="custom-dialog-btn primary" @click="handleSubmit">确定</button>
            </div>
        </el-dialog>

        <customer-list ref="customerList" @update="handleUpdateCustomerList($event)" />

        <contacts :title="contactsTitle" :type="contactsType" ref="contactsView"
            @update="handleUpdateContactsList($event)"></contacts>
    </div>
</template>

<script>
import ConfigTable from './create/config'
import customerList from './create/customerList.vue'
import contacts from './create/contacts'
import { addCrmTask } from '@/api/crm/task'
import { deptTreeSelect, listUser } from '@/api/system/user'

export default {
    name: 'AddTask',
    components: { ConfigTable, customerList, contacts },
    data() {
        return {
            title: '新建任务',
            options: [],
            fileType: ['png', 'PNG', 'jpg', 'JPG', 'jpeg', 'JPEG'],
            open: false,
            form: {},
            rules: {},
            loading: false,
            priorityList: [
                {
                    value: 1,
                    label: '最高'
                },
                {
                    value: 2,
                    label: '高'
                },
                {
                    value: 3,
                    label: '中'
                },
                {
                    value: 4,
                    label: '低'
                },
                {
                    value: 5,
                    label: '最低'
                }
            ],
            cycleList: [
                {
                    value: 1,
                    label: '不重复'
                },
                {
                    value: 2,
                    label: '每周'
                },
                {
                    value: 3,
                    label: '每月'
                },
                {
                    value: 4,
                    label: '每年'
                },
                {
                    value: 5,
                    label: '自定义间隔'
                }
            ],

            customerTypeData: [],
            customerTypeShow: false,
            value: '',
            contactsType: 'cc',
            contactsTitle: '抄送人',
            approvalsOptions: [],
            approvalsProps: {
                expandTrigger: 'hover',
                emitPath: false
            }
        }
    },
    created() {
        this.customerTypeData = [
            {
                name: '不提醒',
                color: '#2E74F3',
                hidden: false,
                isActive: false,
                value: 0
            },
            {
                name: '任务开始时',
                color: '#2E74F3',
                hidden: false,
                isActive: true,
                value: 1
            },
            {
                name: '任务开始前5分钟',
                color: '#2E74F3',
                hidden: false,
                isActive: true,
                value: 2
            }
        ]
        this.getApprovalsOptions()
    },
    methods: {
        // 查询部门、查询用户构造树
        async getApprovalsOptions() {
            const dept = await deptTreeSelect()
            const user = await listUser()
            this.salespersonOptions = user.rows || []
            const children = dept.data[0].children || []
            const deptData = [...children, ...[{ id: -1, label: '其他', value: -1 }]] || []
            const userData = user.rows || []
            const getChildren = data => {
                data.forEach(item => {
                    item.value = item.id
                    if (item.children) {
                        getChildren(item.children)
                    } else {
                        item.children = []
                    }
                })
            }
            getChildren(deptData)
            const addChildren = data => {
                data.forEach(item => {
                    userData.forEach(user => {
                        if (item.id === user.deptId && item.children) {
                            item.children.push({
                                id: user.userId,
                                label: user.realName || user.nickName,
                                value: user.userId,
                                disabled: user.status == '1',
                                userName: user.userName
                            })
                        }
                        if (item.id === -1 && (!user.deptId || user.deptId === dept.data[0].id) && item.children) {
                            item.children.push({
                                id: user.userId,
                                label: user.realName || user.nickName,
                                value: user.userId,
                                disabled: user.status == '1',
                                userName: user.userName
                            })
                        }
                    })
                    if (item.children && item.children.length) {
                        addChildren(item.children)
                    }
                })
            }
            addChildren(deptData)
            this.approvalsOptions = deptData
        },
        // 从树结构内找到相同id
        findInTree(tree, id) {
            for (let i = 0; i < tree.length; i++) {
                if (tree[i].id === id) {
                    return tree[i]
                } else if (tree[i].children && tree[i].children.length) {
                    const res = this.findInTree(tree[i].children, id)
                    if (res) return res
                }
            }
        },
        reset() {
            this.form = {
                attachment: undefined,
                ccUsers: [],
                cycleConfig: undefined,
                cycleMode: undefined,
                deadline: undefined,
                exeUserId: undefined,
                executeDate: undefined,
                linkName: undefined,
                linkId: undefined,
                linkModel: 1,
                priority: undefined,
                taskContent: undefined,
                tipsNum: undefined,
                valid: undefined
            }
            this.resetForm('form')
        },
        handleAdd() {
            this.reset()
            this.title = '新建任务'
            this.open = true
        },
        // 取消
        handleCancel() {
            this.open = false
            this.reset()
        },
        // 提交
        handleSubmit() {
            this.$refs['form'].validate(valid => {
                if (valid) {
                    console.log(this.form)
                    addCrmTask(this.form).then(res => {
                        if (res.code === 200) {
                            this.$message.success('新增成功')
                            this.open = false
                            this.$parent.getList()
                        } else {
                            this.$message.error(res.msg)
                        }
                    })
                }
            })
        },
        handleChecked() {
            this.$refs.customerList.handleOpen()
        },
        handleUpdateCustomerList(data) {
            this.$set(this.form, 'linkId', data.id)
            this.$set(this.form, 'linkName', data.name)
            this.$message.success('客户信息已更新')
        },
        handleContactShow(type, arr) {
            if (type == 'cc') {
                this.contactsTitle = '抄送人'
                this.contactsType = 'cc'
            }
            let checkList = []
            arr.forEach(el => {
                checkList.push(el.userId)
            })
            console.log(checkList)
            this.$refs.contactsView.handleOpen(checkList)
        },
        handleUpdateContactsList(data) {
            // console.log(data)
            let type = data && data[0].typesOf
            if (type === 'cc') {
                this.form.ccUsers = this.form.ccUsers.filter(item => item.typesOf !== 'cc').concat(data)
            }
        },
        handleCloseTag(item) {
            this.form.ccUsers.splice(
                this.form.ccUsers.findIndex(i => i.typesOf === item.typesOf && i.userId == item.userId),
                1
            )
        }
    }
}
</script>
<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
@import '~@/assets/styles/crm.scss';

.ipt {
    ::v-deep {
        .el-input__inner {
            cursor: pointer;
        }
    }
}

.border_no {
    border: 1px solid #dcdfe6;
    border-radius: 5px;

    ::v-deep {
        .el-input__inner {
            border: none;
        }
    }
}

.checked_box {
    display: flex;
    align-items: center;

    .checked_item {
        background: #dde3ed;
        border-radius: 5px;
        height: 36px;
        font-weight: 400;
        font-size: 14px;
        color: #666666;
        line-height: 36px;
        padding: 0 15px;
        cursor: pointer;
        box-sizing: border-box;
        margin-right: 10px;
    }

    .setting_box {
        display: flex;
        align-items: center;
        font-weight: 400;
        font-size: 14px;
        color: #666666;
        line-height: 20px;

        img {
            width: 20px;
            height: 20px;
            margin-right: 4px;
        }
    }
}

.por {
    .customper_title_box {
        display: flex;
        align-items: center;

        .title_text {
            font-weight: 400;
            font-size: 18px;
            color: #666666;
            line-height: 20px;
            margin-right: 50px;
        }

        .customer_ipt_box {
            width: 303px;
        }
    }

    .customer_list_box {
        position: relative;
        padding: 0px 20px;

        .customer_list_tabs_left {
            ::v-deep {
                .el-tabs--border-card>.el-tabs__header {
                    background-color: #f2f3f9;
                }

                .el-tabs--border-card>.el-tabs__header .el-tabs__item.is-active {
                    background-color: #f8f9fb;
                }

                .el-tabs--border-card>.el-tabs__content {
                    padding: 0;
                }

                .el-table {
                    border: none !important;
                }
            }
        }

        .customer_list_tabs_right {
            position: absolute;
            top: 4px;
            right: 40px;
            width: 103px;
            height: 32px;
            background: #e7f0ff;
            border-radius: 5px;
            border: 1px solid #2e73f3;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 500;
            font-size: 12px;
            color: #2e73f3;
            cursor: pointer;

            img {
                width: 20px;
                height: 20px;
                margin-right: 5px;
            }
        }
    }
}

.customer_table_box {
    .table_title {
        height: 38px;
        background: #f2f3f9;
        font-weight: 400;
        font-size: 12px;
        color: #666666;
        line-height: 38px;
        text-align: center;
    }
}
</style>
