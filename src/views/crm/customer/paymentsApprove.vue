<template>
    <div>
        <el-dialog v-dialogDragBox :visible.sync="open" width="1152px" class="custom-dialog">
            <div slot="title" style="display: flex; align-items: center;">
                <span style="font-weight: 400; font-size: 18px; color: #666666; line-height: 20px;">收款记录审批设置</span>
            </div>
            <div style="display: flex; align-items: center; flex-wrap: wrap; background: #F8F8F8; padding: 10px;">
                <div class="tab_item" v-for="(item, index) in approveList" :key="index">{{ item.label }}</div>
            </div>
            <div class="contactConfig_box">
                <div style="display: flex; align-items: center; justify-content: space-between; padding-top: 20px;">
                    <div class="radio_box">
                        <span>收款审批功能</span>
                        <el-radio-group v-model="radio">
                            <el-radio :label="3">开启</el-radio>
                            <el-radio :label="6">关闭</el-radio>
                        </el-radio-group>
                    </div>
                    <div class="radio_box">
                        <span>可编辑此选项权限</span>
                        <el-radio-group v-model="radio">
                            <el-radio :label="3">仅管理员</el-radio>
                            <el-radio :label="6">所有人</el-radio>
                            <el-radio :label="9">指定人员</el-radio>
                        </el-radio-group>
                    </div>
                </div>

                <div class="app-container">
                    <div style="font-weight: 500; font-size: 14px; color: #666666; line-height: 20px; margin-bottom: 13px;">那些职位的人收款需要审批？</div>
                    <el-table :data="initialList" border :header-cell-style="{ 'text-align': 'center' }"
                        :row-class-name="activeClass" class="tableDragBox">
                        <el-table-column label="职务名称" align="center" show-overflow-tooltip>
                            <template slot-scope="{ row }">
                                <span>{{ row.data1 }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="录入审批" align="center" show-overflow-tooltip>
                            <template slot-scope="{ row }">
                                <el-switch v-model="row.data2" active-color="#2E73F3" inactive-color="#ff4949">
                                </el-switch>
                            </template>
                        </el-table-column>
                        <el-table-column label="修改审批" align="center" show-overflow-tooltip>
                            <template slot-scope="{ row }">
                                <el-switch v-model="row.data4" active-color="#2E73F3" inactive-color="#ff4949">
                                </el-switch>
                            </template>
                        </el-table-column>
                    </el-table>

                    <div style="margin-top: 20px;">
                        
                    </div>
                </div>
            </div>
            <div slot="footer">
                <button type="button" class="custom-dialog-btn" @click="open = false">恢复默认设置</button>
                <button type="button" class="custom-dialog-btn" @click="open = false">取消</button>
                <button type="button" class="custom-dialog-btn primary" @click="open = false">确定</button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
export default {
    data() {
        return {
            open: false,
            radio: 3,
            initialList: [
                {
                    dataId: "202404220001",
                    data1: "第一列数据1",
                    data2: true,
                    data3: 1,
                    data4: true,
                    data5: true,
                },
                {
                    dataId: "202404220002",
                    data1: "第一列数据2",
                    data2: true,
                    data3: 1,
                    data4: true,
                    data5: true,
                },
                {
                    dataId: "202404220003",
                    data1: "第一列数据3",
                    data2: true,
                    data3: 2,
                    data4: true,
                    data5: true,
                },
                {
                    dataId: "202404220004",
                    data1: "第一列数据4",
                    data2: true,
                    data3: 1,
                    data4: true,
                    data5: true,
                },
                {
                    dataId: "202404220005",
                    data1: "第一列数据5",
                    data2: true,
                    data3: 2,
                    data4: true,
                    data5: true,
                },
            ],
            approveList: [
                {
                    label: '客户审批',
                    value: '1'
                },
                {
                    label: '订单审批',
                    value: '2'
                },
                {
                    label: '收款审批',
                    value: '3'
                },
                {
                    label: '费用收支审批',
                    value: '4'
                },
                {
                    label: '退款审批',
                    value: '5'
                },
                {
                    label: '预存款审批',
                    value: '6'
                },
                {
                    label: '采购计划审批',
                    value: '7'
                },
                {
                    label: '采购退款审批',
                    value: '8'
                },
                {
                    label: '入库审批',
                    value: '9'
                },
                {
                    label: '出库审批',
                    value: '10'
                },
                {
                    label: '调拨审批',
                    value: '11'
                },
                {
                    label: '采购退货审批',
                    value: '12'
                },
                {
                    label: '装配出入库审批',
                    value: '13'
                },
                {
                    label: '库存盘点审批',
                    value: '14'
                },
            ]
        }
    },
    created() { },
    mounted() { },

    methods: {
        handleOpen() {
            this.open = true
        },
        // 表格行添加class
        activeClass() {
            return "tableTrBox";
        },
    },
}
</script>

<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';

.custom-dialog {
    ::v-deep {
        .el-dialog .el-dialog__body {
            padding: 0;
        }
    }
}

.contactConfig_box {
    .radio_box {
        padding: 10px 20px;
        display: flex;
        align-items: center;

        span {
            font-weight: 400;
            font-size: 12px;
            color: #999999;
            line-height: 20px;
            margin-right: 20px;
        }
    }
}

.tableTrBox {
    cursor: move !important;
    position: relative !important;
}

.tab_item {
    // width: 8.3%;
    font-weight: 400;
    font-size: 12px;
    color: #666666;
    padding: 8px 20px;
    cursor: pointer;
    text-align: center;

    &:hover {
        font-weight: 500;
        font-size: 14px;
        color: #FFFFFF;
        background: #2E73F3;
        border-radius: 50px;
    }
}
</style>