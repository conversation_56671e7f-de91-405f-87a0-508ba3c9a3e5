<template>
    <div>
        <el-dialog v-dialogDragBox :visible.sync="open" width="1152px" class="custom-dialog">
            <div slot="title" style="display: flex; align-items: center;">
                <span style="font-weight: 400; font-size: 18px; color: #666666; line-height: 20px;">添加流程设置</span>
            </div>
            <div class="contactConfig_box">
                <div style="padding: 0 20px;">
                    <el-form ref="form" :model="form" :rules="rules" label-width="6em" label-position="left">
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="审批类型" prop="categoryId">
                                    <el-select v-model="form.categoryId" placeholder="请选择审批类型" style="width: 100%">
                                        <el-option v-for="(item, index) in categoryList" :key="index" :label="item.name"
                                            :value="item.id"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="流程名称" prop="categoryId">
                                    <el-input v-model="form.productName" placeholder="请输入流程名称" />
                                </el-form-item>
                            </el-col>
                            <el-col :span="24">
                                <el-form-item label="生效范围" prop="categoryId">
                                    <el-radio-group v-model="form.radio">
                                        <el-radio :label="1">全公司</el-radio>
                                        <el-radio :label="2">部门</el-radio>
                                        <el-radio :label="3">人员</el-radio>
                                    </el-radio-group>
                                </el-form-item>
                            </el-col>
                            <el-col :span="24">
                                <el-form-item label="" prop="categoryId" class="form_box">
                                    <el-tag closable :disable-transitions="false" @close="handleClose(tag)">某某某</el-tag>
                                    <el-button type="text" icon="el-icon-plus"
                                        style="margin-left: 15px;">选择人员</el-button>
                                </el-form-item>
                            </el-col>
                            <el-col :span="24">
                                <el-form-item label="抄送给" prop="categoryId">
                                    <el-tag closable :disable-transitions="false" @close="handleClose(tag)">某某某</el-tag>
                                    <el-button type="text" icon="el-icon-plus"
                                        style="margin-left: 15px;">选择人员</el-button>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                </div>

                <div class="app-container">
                    <div style="font-weight: 500; font-size: 14px; color: #666666; line-height: 20px; margin-bottom: 7px;">流程顺序</div>
                    <el-table :data="initialList" :header-cell-style="{ 'text-align': 'center' }">
                        <el-table-column label="序号" align="center" type="index" width="50" show-overflow-tooltip></el-table-column>
                        <el-table-column label="审批人" align="center" show-overflow-tooltip width="400">
                            <template slot-scope="{ row }">
                                <div>
                                    <el-tag closable :disable-transitions="false" @close="handleClose(tag)">张三</el-tag>
                                    <el-tag closable :disable-transitions="false" @close="handleClose(tag)" style="margin-left: 15px;">李四</el-tag>
                                    <el-button type="text" icon="el-icon-plus"
                                        style="margin-left: 15px;">添加审批人</el-button>
                                </div>
                                <div style="margin-top: 20px; display: flex; align-items: center;">
                                    <span style="font-weight: 400; font-size: 12px; color: #999999; margin-right: 20px;">完成条件</span>
                                    <el-radio-group v-model="form.radio" style="font-weight: 400; font-size: 14px; color: #666666; line-height: 20px;" size="mini">
                                        <el-radio :label="1">全部完成</el-radio>
                                        <el-radio :label="2">任一完成</el-radio>
                                    </el-radio-group> 
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column label="抄送给" align="center" show-overflow-tooltip>
                            <template slot-scope="{ row }">
                                <div>
                                    <el-tag closable :disable-transitions="false" @close="handleClose(tag)">张三</el-tag>
                                    <el-button type="text" icon="el-icon-plus"
                                        style="margin-left: 15px;">添加抄送人</el-button>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column label="是否允许修改" align="center" show-overflow-tooltip>
                            <template slot-scope="{ row }">
                                <el-switch v-model="row.data4" active-color="#2E73F3" inactive-color="#ff4949">
                                </el-switch>
                            </template>
                        </el-table-column>
                        <el-table-column label="操作" align="center" show-overflow-tooltip width="200">
                            <template slot-scope="{ row }">
                                <el-button type="text" icon="el-icon-setting">自定义</el-button>
                                <el-button type="text" icon="el-icon-delete">删除</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                    <div style="padding: 20px 0; border-bottom: 1px solid #E2E6F3;">
                        <el-button type="primary" icon="el-icon-plus">添加流程顺序</el-button>
                    </div>
                </div>
                <div
                    style="font-weight: 400; font-size: 12px; color: #999999; line-height: 20px; display: flex; flex-direction: column; margin: 0 20px; padding-bottom: 20px; border-bottom: 1px solid #E2E6F3;">
                    <span>注</span>                  
                    <span>1、可以为不同的部门或人员设置不同的审批流程。</span>
                    <span>2、如果为一个员工指定了流程并且同时为其所在的部门指定了流程，则以员工指定流程优先</span>
                    <span>3、审批节点中如果选择多人审批，则需要每个人都审批同意后，才能进入下一步</span>
                </div>
            </div>
            <div slot="footer">
                <button type="button" class="custom-dialog-btn" @click="open = false">取消</button>
                <button type="button" class="custom-dialog-btn primary" @click="open = false">确定</button>
            </div>
        </el-dialog>

        <addsalesprocess-dialog ref="addSalesProcess" />
    </div>
</template>

<script>
import addsalesprocessDialog from './addSalesProcess'
export default {
    components: { addsalesprocessDialog },
    data() {
        return {
            open: false,
            initialList: [
                {
                    dataId: "202404220001",
                    data1: "第一列数据1",
                    data2: true,
                    data3: 1,
                    data4: true,
                    data5: true,
                    status: 1
                },
                {
                    dataId: "202404220002",
                    data1: "第一列数据2",
                    data2: true,
                    data3: 1,
                    data4: true,
                    data5: true,
                    status: 0
                },
                {
                    dataId: "202404220003",
                    data1: "第一列数据3",
                    data2: true,
                    data3: 2,
                    data4: true,
                    data5: true,
                    status: 0
                },
                {
                    dataId: "202404220004",
                    data1: "第一列数据4",
                    data2: true,
                    data3: 1,
                    data4: true,
                    data5: true,
                    status: 0
                },
                {
                    dataId: "202404220005",
                    data1: "第一列数据5",
                    data2: true,
                    data3: 2,
                    data4: true,
                    data5: true,
                    status: 0
                },
            ],
            form: {},
            rules: {},
            categoryList: [],
            input2: ''
        }
    },
    created() { },
    mounted() { },

    methods: {
        handleOpen() {
            this.open = true
        },
        handleAddSales() {
            this.$refs.addSalesProcess.handleAdd()
        },
    },
}
</script>

<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';

.tableTrBox {
    cursor: move !important;
    position: relative !important;
}

.append_cls {
    ::v-deep {
        .el-input-group__append {
            background: #E1EBFF;
            color: #2E73F3;
        }
    }
}

.form_box {
    background: #F0F3F9;
    height: 70px;
    display: flex;
    align-items: center;
    position: relative;

    &::before {
        position: absolute;
        content: "";
        width: 0;
        height: 0;
        top: -12px;
        left: 270px;
        border-bottom: solid 12px #F0F3F9;
        border-right: solid 12px transparent;
        border-left: solid 12px transparent;
    }
}
</style>