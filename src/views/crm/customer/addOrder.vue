<template>
    <div>
        <el-dialog v-dialogDragBox :title="title" :visible.sync="open" width="1152px" class="custom-dialog">
            <div class="order_box">
                <el-form ref="form" :model="form" :rules="rules" label-width="8em">
                    <el-row :gutter="20" style="display: flex; flex-wrap: wrap">
                        <el-col :span="12">
                            <el-form-item label="负责人" prop="categoryId">
                                <el-radio-group v-model="radio">
                                    <el-radio :label="1">客户负责人</el-radio>
                                    <el-radio :label="2">指定人员</el-radio>
                                </el-radio-group>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="参与人" prop="categoryId">
                                <div>
                                    <el-popover slot="label" placement="bottom" trigger="hover" popper-class="btn_box">
                                        <div class="customer_list_box">
                                            <el-tabs v-model="activeName" @tab-click="handleClick">
                                                <el-tab-pane label="按人员查找" name="first">按人员查找</el-tab-pane>
                                                <el-tab-pane label="按部门查找" name="second">
                                                    <div class="dept_box">
                                                        <div class="dept_left">
                                                            <el-menu default-active="1-2" class="el-menu-vertical-demo"
                                                                @open="handleOpen" @close="handleClose"
                                                                background-color="#E2E5F3" text-color="#666666"
                                                                active-text-color="#2E73F3" style="height: 497px;">
                                                                <el-submenu index="1">
                                                                    <template slot="title">
                                                                        <span>销售部</span>
                                                                    </template>
                                                                    <el-menu-item index="1-1">销售一部</el-menu-item>
                                                                    <el-menu-item index="1-2">销售二部</el-menu-item>
                                                                    <el-menu-item index="1-3">销售三部</el-menu-item>
                                                                </el-submenu>
                                                                <el-menu-item index="2">
                                                                    <span slot="title">保芯</span>
                                                                </el-menu-item>
                                                                <el-menu-item index="3">
                                                                    <span slot="title">行政部</span>
                                                                </el-menu-item>
                                                                <el-menu-item index="4">
                                                                    <span slot="title">产品部</span>
                                                                </el-menu-item>
                                                            </el-menu>
                                                        </div>
                                                        <div class="dept_right">
                                                            <div class="dept_right_search">
                                                                <el-input v-model="form.productName"
                                                                    placeholder="请输入客户名称">
                                                                    <el-button style="margin-top: 2px;" slot="suffix"
                                                                        type="primary" icon="el-icon-search" plain
                                                                        size="medium">搜索</el-button>
                                                                </el-input>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </el-tab-pane>
                                                <el-tab-pane label="最近查看" name="third">最近查看</el-tab-pane>
                                            </el-tabs>
                                        </div>
                                        <el-input slot="reference" v-model="form.productName" placeholder="请选择参与人"
                                            readonly class="ipt">
                                            <i slot="suffix" class="el-input__icon el-icon-arrow-down"></i>
                                        </el-input>
                                    </el-popover>

                                </div>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="发货日期" prop="supplyEndTime">
                                <el-date-picker v-model="form.supplyEndTime" type="date" value-format="yyyy-MM-dd"
                                    placeholder="请选择发货日期" style="width: 100%"></el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="主题" prop="productName">
                                <el-input v-model="form.productName" placeholder="请输入主题" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="企业微信" prop="categoryId">
                                <div style="display: flex; align-items: center; justify-content: space-around;">
                                    <el-radio-group v-model="radio" style="display: flex; align-items: center;">
                                        <el-radio :label="1">已加</el-radio>
                                        <el-radio :label="2">未加</el-radio>
                                    </el-radio-group>
                                    <el-input v-model="form.productName" placeholder="请输入企业微信"
                                        style="margin-left: 20px;" />
                                </div>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="合同编号" prop="productName">
                                <el-input v-model="form.productName" placeholder="请输入合同编号" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="成交客户" prop="unit">
                                <el-select v-model="form.unit" placeholder="请选择成交客户" style="width: 100%">
                                    <el-option v-for="(item, index) in options" :key="index" :label="item"
                                        :value="item"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="客户联系人" prop="unit">
                                <el-select v-model="form.unit" placeholder="请选择客户联系人" style="width: 100%">
                                    <el-option v-for="(item, index) in options" :key="index" :label="item"
                                        :value="item"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <div class="table_list">
                                <div class="table_title">
                                    <span style="margin-right: 54px;">成交产品</span>
                                    <el-button type="primary" icon="el-icon-plus" plain size="small">添加产品</el-button>
                                </div>
                                <div class="form_table">
                                    <el-form-item label="" label-width="0" prop="products">
                                        <el-table :data="form.products" style="width: 100%;" class="custom-table">
                                            <el-table-column type="index" label="序号" width="50"></el-table-column>
                                            <el-table-column prop="productName" label="产品名称" align="center"
                                                show-overflow-tooltip>
                                                <template slot-scope="{ row }">
                                                    <span class="table-link" @click="handleView(row.id, row)">{{
                                                        row.productName
                                                    }}</span>
                                                </template>
                                            </el-table-column>
                                            <el-table-column label="产品图片" align="center" show-overflow-tooltip>
                                                <template slot-scope="{ row }">
                                                    <div style="display: flex; justify-content: center">
                                                        <el-image :src="formatProductImg(row)" fit="cover"
                                                            @click="handleImgView(row)">
                                                            <div slot="error" class="image-slot">
                                                                <i class="el-icon-picture-outline"></i>
                                                            </div>
                                                        </el-image>
                                                    </div>
                                                </template>
                                            </el-table-column>
                                            <el-table-column prop="model" label="产品型号" align="center"
                                                show-overflow-tooltip></el-table-column>
                                            <el-table-column prop="quantity" label="产品成本(元)" align="center" width="150">
                                                <template slot-scope="scope">
                                                    <el-form-item label-width="0"
                                                        :prop="`products.${scope.$index}.quantity`"
                                                        :rules="rules.quantity">
                                                        <el-input v-model="scope.row.quantity" size="small"
                                                            placeholder="产品成本">
                                                        </el-input>
                                                    </el-form-item>
                                                </template>
                                            </el-table-column>
                                            <el-table-column prop="quantity" label="成交单价(元)" align="center" width="150">
                                                <template slot-scope="scope">
                                                    <el-form-item label-width="0"
                                                        :prop="`products.${scope.$index}.quantity`"
                                                        :rules="rules.quantity">
                                                        <el-input v-model="scope.row.quantity" size="small"
                                                            placeholder="成交单价">
                                                        </el-input>
                                                    </el-form-item>
                                                </template>
                                            </el-table-column>
                                            <el-table-column prop="quantity" label="成交数量" align="center" width="250px">
                                                <template slot-scope="scope">
                                                    <el-form-item label-width="0"
                                                        :prop="`products.${scope.$index}.quantity`"
                                                        :rules="rules.quantity">
                                                        <el-input v-model="scope.row.quantity" size="small"
                                                            placeholder="成交数量">
                                                            <el-select style="width: 90px;" v-model="scope.row.unit"
                                                                slot="append" placeholder="单位">
                                                                <el-option v-for="(unit, i) in unitOptions" :key="i"
                                                                    :label="unit" :value="unit"></el-option>
                                                            </el-select>
                                                        </el-input>
                                                    </el-form-item>
                                                </template>
                                            </el-table-column>
                                            <el-table-column prop="quantity" label="成交总价(元)" align="center" width="150">
                                                <template slot-scope="scope">
                                                    <el-form-item label-width="0"
                                                        :prop="`products.${scope.$index}.quantity`"
                                                        :rules="rules.quantity">
                                                        <el-input v-model="scope.row.quantity" size="small"
                                                            placeholder="成交总价">
                                                        </el-input>
                                                    </el-form-item>
                                                </template>
                                            </el-table-column>
                                            <el-table-column label="操作" align="center">
                                                <template slot-scope="scope">
                                                    <el-button type="text" size="mini" icon="el-icon-delete"
                                                        @click="handleDelete(scope.row)">删除</el-button>
                                                </template>
                                            </el-table-column>
                                        </el-table>
                                    </el-form-item>
                                </div>
                                <div class="table_total">
                                    <div class="total_left">
                                        <div class="left_item">
                                            共
                                            <span>8</span>
                                            件产品
                                        </div>
                                        <div class="left_item">
                                            成交总量
                                            <span>10000</span>
                                        </div>
                                    </div>
                                    <div class="total_right">
                                        成交总额
                                        <span>10000000</span>
                                    </div>
                                </div>
                            </div>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="成交总额" prop="productName">
                                <el-input v-model="form.productName" placeholder="请输入成交总额" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="折扣" prop="productName">
                                <el-input v-model="form.productName" placeholder="请输入折扣" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="是否含税" prop="productName">
                                <el-switch v-model="value" active-color="#13ce66" inactive-color="#ff4949">
                                </el-switch>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <div class="table_list" style="padding-bottom: 0;">
                                <div class="flex_box">
                                    <el-form-item label="总成本" prop="productName">
                                        <el-input v-model="form.productName" placeholder="请输入总成本" />
                                    </el-form-item>
                                    <span>=</span>
                                    <el-form-item label="产品成本" prop="productName">
                                        <el-input v-model="form.productName" placeholder="请输入产品成本" />
                                    </el-form-item>
                                    <span>+</span>
                                    <el-form-item label="运费" prop="productName">
                                        <el-input v-model="form.productName" placeholder="请输入运费" />
                                    </el-form-item>
                                    <span>+</span>
                                    <el-form-item label="其他成本" prop="productName">
                                        <el-input v-model="form.productName" placeholder="请输入其他成本" />
                                    </el-form-item>
                                </div>
                            </div>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="实际成交时间" prop="supplyEndTime" style="margin-left: 20px;">
                                <el-date-picker v-model="form.supplyEndTime" type="date" value-format="yyyy-MM-dd"
                                    placeholder="请选择实际成交时间" style="width: 100%"></el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="付款方式" prop="unit">
                                <el-select v-model="form.unit" placeholder="请选择付款方式" style="width: 100%">
                                    <el-option v-for="(item, index) in options" :key="index" :label="item"
                                        :value="item"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="有效期开始时间" prop="supplyEndTime" style="margin-left: 20px;">
                                <el-date-picker v-model="form.supplyEndTime" type="date" value-format="yyyy-MM-dd"
                                    placeholder="请选择有效期开始时间" style="width: 100%"></el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="有效期结束时间" prop="supplyEndTime">
                                <el-date-picker v-model="form.supplyEndTime" type="date" value-format="yyyy-MM-dd"
                                    placeholder="请选择有效期结束时间" style="width: 100%"></el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="订单状态" prop="unit">
                                <el-select v-model="form.unit" placeholder="请选择订单状态" style="width: 100%">
                                    <el-option v-for="(item, index) in options" :key="index" :label="item"
                                        :value="item"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label="说明/备注" prop="remark">
                                <el-input v-model="form.remark" placeholder="请输入跟进内容" type="textarea"
                                    :autosize="{ minRows: 4, maxRows: 4 }" resize="none" class="custom-textarea" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label="相关文档" prop="picture1">
                                <image-upload v-model="form.picture1" :file-type="fileType" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="是否工程" prop="unit">
                                <el-select v-model="form.unit" placeholder="请选择是否工程" style="width: 100%">
                                    <el-option v-for="(item, index) in options" :key="index" :label="item"
                                        :value="item"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="是否发货" prop="unit">
                                <el-select v-model="form.unit" placeholder="请选择是否发货" style="width: 100%">
                                    <el-option v-for="(item, index) in options" :key="index" :label="item"
                                        :value="item"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label="收款" prop="productName">
                                <el-switch v-model="value" active-color="#13ce66" inactive-color="#ff4949">
                                </el-switch>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label="其他费用" prop="productName">
                                <el-switch v-model="value" active-color="#13ce66" inactive-color="#ff4949">
                                </el-switch>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
            <div slot="footer">
                <button type="button" class="custom-dialog-btn" @click="handleCancel">取消</button>
                <button type="button" class="custom-dialog-btn primary" @click="handleSumit">确定</button>
            </div>
        </el-dialog>
    </div>
</template>

<script>

export default {
    data() {
        return {
            title: '新建销售订单',
            form: {
                products: [{
                    productName: '某某产品',
                    model: 'DN150*168*40*40',
                    quantity: undefined
                }]
            },
            rules: {},
            open: false,
            loading: false,
            fileType: ['png', 'PNG', 'jpg', 'JPG', 'jpeg', 'JPEG', 'pdf'],
            key: 1,
            radio: undefined,
            options: [],
            activeName: 'second',
            value: '',
            unitOptions: []
        }
    },
    created() {

    },
    methods: {
        reset() {
            this.form = {
                products: [{
                    productName: '某某产品',
                    model: 'DN150*168*40*40',
                    quantity: undefined
                }]
            }
            this.resetForm('form')
        },
        handleAdd() {
            this.priceRule = false
            this.reset()
            this.title = '新建销售订单'
            this.open = true
        },
        // 取消
        handleCancel() {
            this.open = false
            this.reset()
        },
        // 提交
        handleSumit() {
            this.$refs['form'].validate(valid => {
                if (valid) {
                    if (this.form.id != null) {
                        this.form.productId = this.form.id
                        updateUnsalable(this.form).then(res => {
                            if (res.code === 200) {
                                this.$message.success('修改成功')
                                this.open = false
                                this.$parent.getList()
                            } else {
                                this.$message.error(res.msg)
                            }
                        })
                    } else {
                        addUnsalable(this.form).then(res => {
                            if (res.code === 200) {
                                this.$message.success('新增成功')
                                this.open = false
                                this.$parent.getList()
                            } else {
                                this.$message.error(res.msg)
                            }
                        })
                    }
                }
            })
        },
        handleAllSelect() { },
        handleClick() { },
        handleOpen() { },
        handleClose() { },
    }
}
</script>
<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';

.ipt {
    ::v-deep {
        .el-input__inner {
            cursor: pointer;
        }
    }
}

.order_box {
    padding: 0;

    ::v-deep {
        .el-form-item__content {
            padding-right: 20px;

            .el-input-group {
                vertical-align: middle;
            }
        }
    }

    .table_list {
        background: #F0F3F9;
        padding: 20px;
        margin-bottom: 20px;

        .table_title {
            display: flex;
            align-items: center;
            font-weight: 400;
            font-size: 12px;
            color: #999999;
            line-height: 20px;
        }

        .form_table {
            margin-top: 20px;
        }

        .table_total {
            margin-top: 10px;
            height: 48px;
            background: #EFF5FF;
            border-radius: 5px;
            border: 1px solid #2E73F3;
            display: flex;
            align-items: center;
            justify-content: space-between;

            .total_left {
                padding-left: 30px;
                display: flex;
                align-items: center;

                .left_item {
                    font-weight: 400;
                    font-size: 14px;
                    color: #666666;
                    margin-right: 90px;
                    display: flex;
                    align-items: center;

                    span {
                        font-weight: 500;
                        font-size: 18px;
                        color: #2E73F3;
                        margin: 0 10px;
                    }
                }
            }

            .total_right {
                margin-right: 145px;
                font-weight: 400;
                font-size: 14px;
                color: #666666;
                display: flex;
                align-items: center;

                span {
                    font-weight: 500;
                    font-size: 18px;
                    color: #F35D09;
                    margin-left: 10px;
                }
            }
        }

        .flex_box {
            display: flex;
            align-items: center;
            justify-content: space-around;

            span {
                margin-bottom: 22px;
                color: #2E73F3;
                font-weight: 400;
                font-size: 12px;
            }
        }
    }
}

.customer_list_box {
    width: 444px;
    height: 538px;
    background: #FFFFFF;
    box-shadow: 0px 1px 15px 0px rgba(0, 0, 0, 0.25);
    border-radius: 5px;
    border: 1px solid #2E73F3;

    ::v-deep {
        .el-tabs__header {
            margin-bottom: 0;

            .el-tabs__nav-wrap {
                padding: 0 15px;
            }
        }
    }

    .dept_box {
        display: flex;

        .dept_left {
            // width: 164px;
        }

        .dept_right {
            // flex: 1;
        }
    }
}
</style>
