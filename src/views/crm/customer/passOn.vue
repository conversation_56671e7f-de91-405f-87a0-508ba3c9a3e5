<template>
    <div>
        <el-dialog v-dialogDragBox :title="title" :visible.sync="open" width="1152px" class="custom-dialog">
            <el-form ref="form" :model="form" :rules="rules" label-position="left" label-width="105px">
                <div class="pl-20 pr-20">
                    <el-row :gutter="20">
                        <el-col :span="24">
                            <el-form-item label="转交给" prop="userList">
                                <el-tag closable @close="handleCloseTag(item)" class="crm-tag border-none"
                                    v-for="item in form.userList" :key="item.userId">{{
                                        item.realName ||
                                        item.nickName }}</el-tag>
                                <el-button type="text" icon="el-icon-plus" style="margin-left: 15px"
                                    @click="handleContactShow(form.userList)">选择人员</el-button>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label="原因" prop="reason">
                                <el-input v-model="form.reason" type="textarea" resize="none" :rows="3"
                                    placeholder="请输入原因详细"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label="转交记录" prop="record">
                                <el-button type="text" @click="handleViewHandoverLog">查看转交历史记录</el-button>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label="同步转交" prop="syncHandover">
                                <div>
                                    <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll"
                                        @change="handleCheckAllChange">全选</el-checkbox>
                                    <div style="margin: 15px 0;"></div>
                                    <el-checkbox-group v-model="form.syncHandover" @change="handleCheckedCitiesChange">
                                        <el-checkbox v-for="(item, index) in syncHandList" :label="item.value"
                                            :key="index">{{ item.label
                                            }}</el-checkbox>
                                    </el-checkbox-group>
                                </div>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </div>
                <div class="ml-20 mr-20" style="height: 1px; background-color: #e2e6f3"></div>
            </el-form>
            <div slot="footer">
                <el-button class="custom-dialog-btn" @click="handleCancel">取消</el-button>
                <el-button class="custom-dialog-btn primary" @click="handleSubmit">确定</el-button>
            </div>
        </el-dialog>

        <contacts title="转交人" type="zj" ref="contactsView" @update="handleUpdateContactsList($event)">
        </contacts>
        <el-dialog v-dialogDragBox title="转交记录" :visible.sync="recordOpen" width="980px" class="custom-dialog">
            <el-table v-loading="handoverLoading" ref="contactTable" stripe :data="handoverList" style="width: 950px; margin: 0 15px;"
                class="custom-table">
                <el-table-column align="center" type="index" label="序号" width="50">
                    <template slot-scope="scope">
                        {{ scope.$index + 1 }}
                    </template>
                </el-table-column>
                <el-table-column align="center" label="原负责人" show-overflow-tooltip>
                    <template slot-scope="scope">
                        <el-tag class="crm-tag border-none" v-for="(item, index) in scope.row.before_"
                            :key="index">{{ item.userName }}</el-tag>
                    </template>
                </el-table-column>
                <el-table-column align="center" label="现负责人" show-overflow-tooltip>
                    <template slot-scope="scope">
                        <el-tag class="crm-tag border-none" v-for="(item, index) in scope.row.after_"
                            :key="index">{{ item.userName }}</el-tag>
                    </template>
                </el-table-column>
                <el-table-column align="center" label="转交原因" show-overflow-tooltip>
                    <template slot-scope="scope">{{ scope.row.reason }}</template>
                </el-table-column>
                <el-table-column align="center" label="同步转交" show-overflow-tooltip>
                    <template slot-scope="scope">
                        <el-tag v-for="(item, index) in (scope.row.syncHandover && scope.row.syncHandover.split(','))" :key="index"
                            class="crm-tag border-none">{{ item == 'A' ? '销售合同' : item == 'B' ? '销售订单' : item == 'C' ? '收款记录' : item == 'D' ? '退款记录' : '' }}</el-tag>
                    </template>
                </el-table-column>
            </el-table>
            <div slot="footer">
                <el-button class="custom-dialog-btn" @click="handleRecordCancel">取消</el-button>
                <el-button class="custom-dialog-btn primary" @click="handleRecordSubmit">确定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import contacts from './create/contacts'
import { editHandover, getHandoverLog } from '@/api/crm/customer'

export default {
    components: { contacts },
    data() {
        return {
            // 是否打开表单
            open: false,
            // 表单标题
            title: '转交客户给其他团队成员(变更负责人)',
            // 表单数据
            form: {},
            // 表单验证规则
            rules: {},
            // 是否全选
            checkAll: false,
            syncHandList: [{
                label: '销售合同',
                value: 'A'
            }, {
                label: '销售订单',
                value: 'B'
            }, {
                label: '收款记录',
                value: 'C'
            }, {
                label: '退款记录',
                value: 'D'
            }],
            // 是否为不确定状态
            isIndeterminate: false,
            recordOpen: false,
            handoverLoading: false,
            handoverList: [],
        }
    },
    methods: {
        // 表单重置
        reset(id) {
            this.form = {
                customerId: id,
                reason: undefined,
                syncHandover: [],
                userList: []
            }
            this.isIndeterminate = false
            this.checkAll = false
            this.$nextTick(() => {
                this.$refs.form.clearValidate()
            })
            this.resetForm('form')
        },
        // 打开新增
        handleOpen(id) {
            this.reset(id)
            console.log(id)
            this.open = true
        },
        handleCheckAllChange(val) {
            console.log(val)
            if (val) {
                this.form.syncHandover = this.syncHandList.map(item => item.value)
            } else {
                this.form.syncHandover = []
                
            }
            this.isIndeterminate = false;
        },
        handleCheckedCitiesChange(value) {
            console.log(value)
            let checkedCount = value.length;
            this.checkAll = checkedCount === this.syncHandList.length;
            this.isIndeterminate = checkedCount > 0 && checkedCount < this.syncHandList.length;
        },
        // 关闭窗口
        handleClose() {
            this.handleCancel()
        },
        // 取消操作
        handleCancel(flag = false) {
            this.open = false
            this.$emit('callback', false)
        },
        handleSubmit() {
            this.$refs.form.validate(valid => {
                if (valid) {
                    editHandover({
                        ...this.form,
                        syncHandover: this.form.syncHandover.join(','),
                    }).then(res => {
                        if (res.code === 200) {
                            this.$message.success('转交成功')
                            this.open = false
                            this.$emit('callback', true)
                        } else {
                            this.$message.error(res.msg)
                            return false
                        }
                    }).catch(err => {
                        this.$message.error(err.message)
                    })
                } else {
                    return false
                }
            })
        },

        handleContactShow(arr) {
            let checkList = []
            arr.forEach(el => {
                checkList.push(el.userId)
            });
            console.log(checkList)
            this.$refs.contactsView.handleOpen(checkList)
        },
        handleUpdateContactsList(data) {
            this.form.userList = data
        },
        handleCloseTag(item) {
            this.form.userList.splice(this.form.userList.findIndex(i => i.userId == item.userId), 1)
        },
        handleViewHandoverLog() {
            this.recordOpen = true
            this.getHandoverLog()
        },
        getHandoverLog() {
            this.handoverLoading = true
            getHandoverLog({
                customerId: this.form.customerId,
            }).then(res => {
                if (res.code === 200) {
                    this.handoverList = res.data
                } else {
                    this.$message.error(res.msg)
                    return false
                }
            }).catch(err => {
                this.$message.error(err.message)
            }).finally(() => {
                this.handoverLoading = false
            })
        },
        handleRecordCancel() {
            this.recordOpen = false
        },
        handleRecordSubmit() {
            this.recordOpen = false
        },

    }

}
</script>

<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';
@import '~@/assets/styles/crm.scss';
</style>