<template>
    <div>
        <el-dialog v-dialogDragBox :visible.sync="open" width="1152px" class="custom-dialog">
            <div slot="title" style="display: flex; align-items: center;">
                <span style="font-weight: 400; font-size: 18px; color: #666666; line-height: 20px;">自定义流程设置</span>
            </div>
            <div class="contactConfig_box">
                <div style="padding: 0 20px;">
                    <el-form ref="form" :model="form" label-width="6em" label-position="left">
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="审批流程" prop="categoryId">
                                    <el-select v-model="form.categoryId" placeholder="请选择审批流程" style="width: 100%">
                                        <el-option v-for="(item, index) in categoryList" :key="index" :label="item.name"
                                            :value="item.id"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                </div>
                <div
                    style="margin: 0 20px; padding: 0 20px; height: 62px; background: #F8F9FB; border-radius: 5px; border: 1px solid #CBD6E2; display: flex; align-items: center; justify-content: space-between;" @click="handleApprovalProcessSetting">
                    <div>
                        <el-button type="text" icon="el-icon-plus">添加《外勤审批》审批流程</el-button>
                    </div>
                    <div class="append_cls">
                        <el-input placeholder="请输入流程名称" v-model="input2">
                            <template slot="append">
                                <el-button type="primary" plain icon="el-icon-search">搜索</el-button>
                            </template>
                        </el-input>
                    </div>
                </div>
                <div class="app-container">
                    <el-table :data="initialList" :header-cell-style="{ 'text-align': 'center' }">
                        <el-table-column label="流程名称" align="center" show-overflow-tooltip>
                            <template slot-scope="{ row }">
                                <span type="text"> {{ row.data1 }} </span>
                            </template>
                        </el-table-column>
                        <el-table-column label="流程类型" align="center" show-overflow-tooltip>
                            <template slot-scope="{ row }">
                                <span type="text"> {{ row.data2 }} </span>
                            </template>
                        </el-table-column>
                        <el-table-column label="生效范围" align="center" show-overflow-tooltip width="400">
                            <template slot-scope="{ row }">
                                <div style="display: flex; align-items: center; flex-wrap: wrap; font-weight: 400; font-size: 14px; color: #2E73F3;">
                                    <span style="font-weight: 400; font-size: 12px; color: #999999; margin: 0 10px 10px 0;"> 指定部门 </span>
                                    <span style="padding: 6px 10px; background: #E5EEFF; border-radius: 5px; margin: 0 10px 10px 0;">韩国世星</span>
                                    <span style="padding: 6px 10px; background: #E5EEFF; border-radius: 5px; margin: 0 10px 10px 0;">商务部</span>
                                    <span style="padding: 6px 10px; background: #E5EEFF; border-radius: 5px; margin: 0 10px 10px 0;">南方分公司</span>
                                    <span style="padding: 6px 10px; background: #E5EEFF; border-radius: 5px; margin: 0 10px 10px 0;">韩国朴社长</span>
                                    <span style="padding: 6px 10px; background: #E5EEFF; border-radius: 5px; margin: 0 10px 10px 0;">销售一部</span>
                                    <span style="padding: 6px 10px; background: #E5EEFF; border-radius: 5px; margin: 0 10px 10px 0;">销售二部</span>
                                    <span style="padding: 6px 10px; background: #E5EEFF; border-radius: 5px; margin: 0 10px 10px 0;">销售三部</span>
                                    <span style="padding: 6px 10px; background: #E5EEFF; border-radius: 5px; margin: 0 10px 10px 0;">销售六部</span>
                                    <span style="padding: 6px 10px; background: #E5EEFF; border-radius: 5px; margin: 0 10px 10px 0;">销售五部</span>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column label="创建人" align="center" show-overflow-tooltip>
                            <template slot-scope="{ row }">
                                <span type="text"> {{ row.data3 }} </span>
                            </template>
                        </el-table-column>
                        <el-table-column label="创建时间" align="center" show-overflow-tooltip>
                            <template slot-scope="{ row }">
                                <span type="text"> {{ row.data4 }} </span>
                            </template>
                        </el-table-column>
                        <el-table-column label="操作" align="center" show-overflow-tooltip width="240">
                            <template slot-scope="{ row }">
                                <el-button type="text" icon="el-icon-copy-document">复制</el-button>
                                <el-button type="text" icon="el-icon-edit">编辑</el-button>
                                <el-button type="text" icon="el-icon-delete">删除</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
                <div style="font-weight: 400; font-size: 12px; color: #999999; line-height: 30px; display: flex; flex-direction: column; margin: 0 20px; padding-bottom: 20px; border-bottom: 1px solid #E2E6F3;">
                    <span>注</span>
                    <span>1、如果没有自定义流程，员工在录入的时候需要自行选择审批人</span>
                    <span>2、如果一个部门或一个员工设置了多个流程，那么员工在提交审批时可以自行选择一个流程</span>
                </div>
            </div>
            <div slot="footer">
                <button type="button" class="custom-dialog-btn" @click="open = false">取消</button>
                <button type="button" class="custom-dialog-btn primary" @click="open = false">确定</button>
            </div>
        </el-dialog>

        <approvalprocesssetting-dialog ref="approvalProcessSetting" />
    </div>
</template>

<script>
import approvalprocesssettingDialog from './approvalProcessSetting'
export default {
    components: { approvalprocesssettingDialog },
    data() {
        return {
            open: false,
            initialList: [
                {
                    dataId: "202404220001",
                    data1: "第一列数据1",
                    data2: true,
                    data3: 1,
                    data4: true,
                    data5: true,
                    status: 1
                },
                {
                    dataId: "202404220002",
                    data1: "第一列数据2",
                    data2: true,
                    data3: 1,
                    data4: true,
                    data5: true,
                    status: 0
                },
                {
                    dataId: "202404220003",
                    data1: "第一列数据3",
                    data2: true,
                    data3: 2,
                    data4: true,
                    data5: true,
                    status: 0
                },
                {
                    dataId: "202404220004",
                    data1: "第一列数据4",
                    data2: true,
                    data3: 1,
                    data4: true,
                    data5: true,
                    status: 0
                },
                {
                    dataId: "202404220005",
                    data1: "第一列数据5",
                    data2: true,
                    data3: 2,
                    data4: true,
                    data5: true,
                    status: 0
                },
            ],
            form: {},
            categoryList: [],
            input2: ''
        }
    },
    created() { },
    mounted() { },

    methods: {
        handleOpen() {
            this.open = true
        },
        handleApprovalProcessSetting() {
            this.$refs.approvalProcessSetting.handleOpen()
        },
    },
}
</script>

<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';

.tableTrBox {
    cursor: move !important;
    position: relative !important;
}
.append_cls {
    ::v-deep {
        .el-input-group__append {
            background: #E1EBFF;
            color: #2E73F3;
        }
    }
}
</style>