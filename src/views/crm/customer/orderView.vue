<template>
    <div>
        <el-drawer title="订单详情" :visible.sync="orderViewOpen" direction="rtl" size="60%" class="company_view">
            <div class="company_box">
                <div class="company_basic">
                    <div class="basic_info">
                        <div class="text_box">
                            <div style="display: flex; align-items: center;">
                                <span
                                    style="font-weight: 400; font-size: 14px; color: #666666; line-height: 16px;">订单金额(元)：</span>
                                <span
                                    style="font-weight: 500; font-size: 24px; color: #F35D09; line-height: 24px; margin-right: 20px;">5,137,941.52</span>
                                <el-tag effect="dark" type="success">
                                    已收款
                                </el-tag>
                            </div>
                            <div class="charge_person" style="display: flex; align-items: center;">
                                <span
                                    style="font-weight: 400; font-size: 14px; color: #666666; line-height: 16px;">负责人：</span>
                                <span
                                    style="font-weight: 400; font-size: 16px; color: #666666; line-height: 16px;">刘某某</span>
                            </div>
                            <div class="company_basic_btn_list">
                                <div class="btn_item">
                                    <img src="@/assets/images/edit.png" alt="">
                                    <span>修改</span>
                                </div>
                                <div class="btn_item">
                                    <img src="@/assets/images/pass_on.png" alt="">
                                    <span>转交</span>
                                </div>
                                <div class="btn_item">
                                    <img src="@/assets/images/collect.png" alt="">
                                    <span>关注</span>
                                </div>
                                <div class="btn_item">
                                    <img src="@/assets/images/add_blue.png" alt="">
                                    <span>新建</span>
                                </div>

                            </div>
                            <div class="label_list">
                                <span
                                    style="font-weight: 400; font-size: 14px; color: #666666; line-height: 16px;">参与人：</span>
                                <div style="display: flex; align-items: center;">
                                    <el-tag closable>
                                        某某某
                                    </el-tag>
                                    <div
                                        style="display: flex; align-items: center; margin-left: 15px; cursor: pointer;">
                                        <img src="@/assets/images/add_blue.png" alt=""
                                            style="width: 20px; height: 20px; margin-right: 5px;">
                                        <span
                                            style="font-weight: 500; font-size: 14px; color: #2E73F3; line-height: 20px;">选择人员</span>
                                    </div>
                                    <div style="margin:0 20px; width: 1px; height: 28px; background: #2E73F3;"></div>
                                    <div style="display: flex; align-items: center; cursor: pointer;">
                                        <img src="@/assets/images/setting.png" alt=""
                                            style="width: 20px; height: 20px; margin-right: 5px;">
                                        <span
                                            style="font-weight: 400; font-size: 14px; color: #666666; line-height: 20px;">参与人</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="key_nodes" style="display: flex; align-items: center; padding: 0;">
                        <div
                            style="width: 100px; height: 142px; background: #F8F9FB; display: flex; align-items: center; flex-direction: column; justify-content: center; font-weight: 400; font-size: 14px; color: #666666; line-height: 36px;">
                            <div>订单概览</div>
                            <div>订单流程</div>
                            <div>关键节点</div>
                        </div>
                        <div class="keyNodes_concent"
                            style="padding: 15px 20px; display: flex; align-items: center; justify-content: center; flex: 1;">
                            <div class="keyNodes_item">
                                <div>订单</div>
                                <el-progress class="m5" type="circle" :percentage="20" :width="38" :stroke-width="12"
                                    :show-text="false" color="#2E73F3" define-back-color="#E6ECF8"></el-progress>
                                <div>
                                    <span class="num">未添加订单</span>
                                    <img src="@/assets/images/add_c_grey.png" alt="">
                                </div>
                            </div>
                            <div class="keyNodes_line" style="width: 120px;"></div>
                            <div class="keyNodes_item">
                                <div>合同</div>
                                <el-progress class="m5" type="circle" :percentage="80" :width="38" :stroke-width="12"
                                    :show-text="false" color="#2E73F3" define-back-color="#E6ECF8"></el-progress>
                                <div>
                                    <span
                                        style="font-weight: 500; font-size: 14px; color: #2E73F3; line-height: 16px;">已完成签署
                                        ＞</span>
                                    <!-- <img src="@/assets/images/add_c_grey.png" alt=""> -->
                                </div>
                            </div>
                            <div class="keyNodes_line" style="width: 120px;"></div>
                            <div class="keyNodes_item">
                                <div>收款</div>
                                <el-progress class="m5" type="circle" :percentage="60" :width="38" :stroke-width="12"
                                    :show-text="false" color="#2E73F3" define-back-color="#E6ECF8"></el-progress>
                                <div>
                                    已收款：
                                    <span class="num"> 5,137,941.52 元(84.54%) </span>
                                    <img src="@/assets/images/add_c_grey.png" alt="">
                                </div>
                            </div>
                            <div class="keyNodes_line" style="width: 120px;"></div>
                            <div class="keyNodes_item">
                                <div>发货</div>
                                <el-progress class="m5" type="circle" :percentage="60" :width="38" :stroke-width="12"
                                    :show-text="false" color="#2E73F3" define-back-color="#E6ECF8"></el-progress>
                                <div>
                                    <span class="num"> 未发货 </span>
                                    <!-- <img src="@/assets/images/add_c_grey.png" alt=""> -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="company_listInfo">
                    <div class="listInfo_tabs_box">
                        <div class="listInfo_tabs_left">
                            <el-tabs type="border-card" v-model="companyTabActive">
                                <el-tab-pane v-for="(item, index) in orderTabsList" :key="index" :label="item.label"
                                    :name="item.value">
                                    <span slot="label">
                                        {{ item.label }}
                                    </span>
                                </el-tab-pane>
                            </el-tabs>
                        </div>
                        <div class="listInfo_tabs_right">
                            <img src="@/assets/images/setting1.png" alt="">
                            自定义显示
                        </div>
                    </div>
                    <div v-if="companyTabActive == '1'" class="company_list_info1">
                        <div class="company_tab_box">
                            <div class="company_left">
                                <div class="left_tab_item" :class="leftTabActive == 1 ? 'active' : ''"
                                    @click="leftTabActive = 1">基本信息</div>
                                <div class="left_tab_item" :class="leftTabActive == 2 ? 'active' : ''"
                                    @click="leftTabActive = 2">订单信息</div>
                                <div class="left_tab_item" :class="leftTabActive == 3 ? 'active' : ''"
                                    @click="leftTabActive = 3">业绩分配</div>
                            </div>
                            <div class="company_right">
                                <div class="right_tab_item" :class="rightTabActive == 1 ? 'active' : ''"
                                    @click="rightTabActive = 1">简报</div>
                                <div class="right_tab_item" :class="rightTabActive == 2 ? 'active' : ''"
                                    @click="rightTabActive = 2">表格</div>
                            </div>
                        </div>
                        <div class="company_concent" v-if="leftTabActive == 1">
                            <div class="company_concent1_card" v-if="rightTabActive == 1">
                                <div class="card_item">
                                    <div class="title">
                                        <span>编号</span>
                                    </div>
                                    <div class="concent">
                                        <span>16189</span>
                                    </div>
                                </div>
                                <div class="card_item">
                                    <div class="title">
                                        <span>录入时间</span>
                                    </div>
                                    <div class="concent">
                                        <span>2024-10-31 08点42分</span>
                                    </div>
                                </div>
                                <div class="card_item">
                                    <div class="title">
                                        <span>利润(净利)</span>
                                    </div>
                                    <div class="concent">
                                        <span class="red">60776601202</span>
                                    </div>
                                </div>
                                <div class="card_item">
                                    <div class="title">
                                        <span>负责人</span>
                                    </div>
                                    <div class="concent">
                                        <span>张三</span>
                                    </div>
                                </div>
                                <div class="card_item">
                                    <div class="title">
                                        <span>坏账</span>
                                    </div>
                                    <div class="concent">
                                        <span class="red">0.00</span>
                                    </div>
                                </div>
                                <div class="card_item">
                                    <div class="title">
                                        <span>已收款总额</span>
                                        <img src="@/assets/images/add_c_grey.png" alt="">
                                    </div>
                                    <div class="concent">
                                        <span class="red">60776601202</span>
                                    </div>
                                </div>
                                <div class="card_item">
                                    <div class="title">
                                        <span>已收款比例</span>
                                    </div>
                                    <div class="concent">
                                        <el-progress :stroke-width="15" :percentage="70" color="#A2C3FF"></el-progress>
                                    </div>
                                </div>
                                <div class="card_item">
                                    <div class="title">
                                        <span>总利润</span>
                                    </div>
                                    <div class="concent">
                                        <span>60776601202</span>
                                    </div>
                                </div>
                                <div class="card_item">
                                    <div class="title">
                                        <span>所属部门</span>
                                    </div>
                                    <div class="concent">
                                        <span>销售七部</span>
                                    </div>
                                </div>
                                <div class="card_item">
                                    <div class="title">
                                        <span>欠款</span>
                                    </div>
                                    <div class="concent">
                                        <span class="red">0.00</span>
                                    </div>
                                </div>
                                <div class="card_item">
                                    <div class="title">
                                        <span>录入人</span>
                                    </div>
                                    <div class="concent">
                                        <span>张三</span>
                                    </div>
                                </div>
                            </div>
                            <div class="company_concent1_table" v-if="rightTabActive == 2">
                                <div class="table_row bg1">
                                    <div class="table_item">
                                        <span class="title">编号</span>
                                        <div class="concent">
                                            <span>16189</span>
                                        </div>
                                    </div>
                                    <div class="table_item">
                                        <span class="title">录入时间</span>
                                        <div class="concent">
                                            <span>2024-10-31 08点42分</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="table_row bg2">
                                    <div class="table_item">
                                        <span class="title">利润(净利)</span>
                                        <div class="concent">
                                            <span class="red">60776601202</span>
                                        </div>
                                    </div>
                                    <div class="table_item">
                                        <span class="title">负责人</span>
                                        <div class="concent">
                                            <span>张三</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="table_row bg1">
                                    <div class="table_item">
                                        <span class="title">坏账</span>
                                        <div class="concent">
                                            <span class="red">0.00</span>
                                        </div>
                                    </div>
                                    <div class="table_item">
                                        <span class="title">已收款总额</span>
                                        <div class="concent">
                                            <span class="red">60776601202</span>
                                            <img src="@/assets/images/add_c_blue.png" alt="">
                                        </div>
                                    </div>
                                </div>
                                <div class="table_row bg2">
                                    <div class="table_item">
                                        <span class="title">已收款比例</span>
                                        <div class="concent">
                                            <el-progress :stroke-width="15" :percentage="70" color="#A2C3FF"
                                                style="width: 100%;"></el-progress>
                                        </div>
                                    </div>
                                    <div class="table_item">
                                        <span class="title">总利润</span>
                                        <div class="concent">
                                            <span class="red">60776601202</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="table_row bg1">
                                    <div class="table_item">
                                        <span class="title">所属部门</span>
                                        <div class="concent">
                                            <span>销售七部</span>
                                        </div>
                                    </div>
                                    <div class="table_item">
                                        <span class="title">欠款</span>
                                        <div class="concent">
                                            <span class="red">0.00</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="table_row bg2">
                                    <div class="table_item">
                                        <span class="title">录入人</span>
                                        <div class="concent">
                                            <span>张三</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="company_concent" v-show="leftTabActive == 2">
                            <!-- <div class="company_concent1_card" v-if="rightTabActive == 1">
                                <div class="card_item">
                                    <div class="title">
                                        <span>发货日期</span>
                                    </div>
                                    <div class="concent">
                                        <span>2022-8-15(周一)</span>
                                    </div>
                                </div>
                                <div class="card_item">
                                    <div class="title">
                                        <span>主题</span>
                                    </div>
                                    <div class="concent">
                                        <span>配件采购订单</span>
                                    </div>
                                </div>
                                <div class="card_item">
                                    <div class="title">
                                        <span>企业微信</span>
                                    </div>
                                    <div class="concent">
                                        <span>已加</span>
                                    </div>
                                </div>
                                <div class="card_item">
                                    <div class="title">
                                        <span>成交客户</span>
                                    </div>
                                    <div class="concent">
                                        <span>河北世盛金属制品有限公司</span>
                                    </div>
                                </div>
                                <div class="card_item">
                                    <div class="title">
                                        <span>合同编号</span>
                                    </div>
                                    <div class="concent">
                                        <span style="color: #2E73F3;">20250229001</span>
                                    </div>
                                </div>
                                <div class="card_item">
                                    <div class="title">
                                        <span>成交总额</span>
                                    </div>
                                    <div class="concent" style="display: flex; flex-direction: column;">
                                        <span style="font-weight: 500; font-size: 14px; color: #F02323;">5.137.941.52</span>
                                        <span style="font-weight: 400; font-size: 12px; color: #666666;">不含税 丨 折扣80%  </span>
                                    </div>
                                </div>
                                <div class="card_item">
                                    <div class="title">
                                        <span>总成本(元)</span>
                                    </div>
                                    <div class="concent">
                                        <span>
                                            --
                                        </span>
                                    </div>
                                </div>
                                <div class="card_item">
                                    <div class="title">
                                        <span></span>
                                    </div>
                                    <div class="concent">
                                        <span></span>
                                    </div>
                                </div>
                                <div class="card_item">
                                    <div class="title">
                                        <span>实际成交时间</span>
                                    </div>
                                    <div class="concent">
                                        <span>2024-10-31(周四)</span>
                                    </div>
                                </div>
                                <div class="card_item">
                                    <div class="title">
                                        <span>付款方式</span>
                                    </div>
                                    <div class="concent">
                                        <span>现款发货</span>
                                    </div>
                                </div>
                                <div class="card_item">
                                    <div class="title">
                                        <span>有效开始时间</span>
                                    </div>
                                    <div class="concent">
                                        <span>2024-10-31(周四)</span>
                                    </div>
                                </div>
                                <div class="card_item">
                                    <div class="title">
                                        <span>有效结束时间</span>
                                    </div>
                                    <div class="concent">
                                        <span>2024-10-31(周四)</span>
                                    </div>
                                </div>
                                <div class="card_item">
                                    <div class="title">
                                        <span>状态</span>
                                    </div>
                                    <div class="concent">
                                        <span>等待发货</span>
                                    </div>
                                </div>
                                <div class="card_item">
                                    <div class="title">
                                        <span>说明</span>
                                    </div>
                                    <div class="concent">
                                        <span>配件用纸箱打包，明天发走</span>
                                    </div>
                                </div>
                                <div class="card_item">
                                    <div class="title">
                                        <span>相关文档</span>
                                    </div>
                                    <div class="concent">
                                        <span>0</span>
                                    </div>
                                </div>
                                <div class="card_item">
                                    <div class="title">
                                        <span>是否工程</span>
                                    </div>
                                    <div class="concent">
                                        <span>材料单</span>
                                    </div>
                                </div>
                                <div class="card_item">
                                    <div class="title">
                                        <span>是否发货</span>
                                    </div>
                                    <div class="concent">
                                        <span>已发货</span>
                                    </div>
                                </div>
                                <div class="card_item">
                                    <div class="title">
                                        <span>审批状态</span>
                                    </div>
                                    <div class="concent">
                                        <span>审批通过</span>
                                    </div>
                                </div>
                            </div> -->
                            <!-- <div v-if="rightTabActive == 2"> -->
                            <!-- 描述数据 -->
                            <div class="crm-descriptions" :class="{ 'card': rightTabActive == 1 }">
                                <div class="crm-descriptions-item">
                                    <div class="crm-descriptions-item-label">
                                        负责人
                                    </div>
                                    <div class="crm-descriptions-item-content">
                                        李二愣子
                                    </div>
                                </div>
                                <div class="crm-descriptions-item">
                                    <div class="crm-descriptions-item-label">
                                        主题
                                    </div>
                                    <div class="crm-descriptions-item-content">
                                        配件采购订单
                                    </div>
                                </div>
                                <div class="crm-descriptions-item hasbg">
                                    <div class="crm-descriptions-item-label">
                                        企业微信
                                    </div>
                                    <div class="crm-descriptions-item-content">
                                        已加
                                    </div>
                                </div>
                                <div class="crm-descriptions-item hasbg">
                                    <div class="crm-descriptions-item-label">
                                        成交客户
                                    </div>
                                    <div class="crm-descriptions-item-content">
                                        某某金属制品有限公司
                                    </div>
                                </div>
                                <div class="crm-descriptions-item">
                                    <div class="crm-descriptions-item-label">
                                        合同编号
                                    </div>
                                    <div class="crm-descriptions-item-content">
                                        <span class="primary">126464222255</span>
                                    </div>
                                </div>
                                <div class="crm-descriptions-item">
                                    <div class="crm-descriptions-item-label">
                                        成交总额(元)
                                    </div>
                                    <div class="crm-descriptions-item-content">
                                        <span class="price">5,137,941.52</span>
                                        <span class="discount">不含税 | 折扣80%</span>
                                    </div>
                                </div>
                                <div class="crm-descriptions-item lang hasbg">
                                    <div class="crm-descriptions-item-label">
                                        总成本(元)
                                    </div>
                                    <div class="crm-descriptions-item-content">
                                        <span>44,740.07</span>
                                        <span class="product-cost">产品成本</span>
                                        <span>44,740.07</span>
                                        <span class="product-plus">运费</span>
                                        <span>0.00</span>
                                        <span class="product-plus">其他</span>
                                        <span>0.00</span>
                                    </div>
                                </div>
                                <div class="crm-descriptions-item">
                                    <div class="crm-descriptions-item-label">
                                        实际成交时间
                                    </div>
                                    <div class="crm-descriptions-item-content">
                                        2024-10-31(周四)
                                    </div>
                                </div>
                                <div class="crm-descriptions-item">
                                    <div class="crm-descriptions-item-label">
                                        付款方式
                                    </div>
                                    <div class="crm-descriptions-item-content">
                                        现款发货
                                    </div>
                                </div>
                                <div class="crm-descriptions-item hasbg">
                                    <div class="crm-descriptions-item-label">
                                        有效期开始时间
                                    </div>
                                    <div class="crm-descriptions-item-content">
                                        2024-10-31(周四)
                                    </div>
                                </div>
                                <div class="crm-descriptions-item hasbg">
                                    <div class="crm-descriptions-item-label">
                                        有效期结束时间
                                    </div>
                                    <div class="crm-descriptions-item-content">
                                        2024-10-31(周四)
                                    </div>
                                </div>
                                <div class="crm-descriptions-item">
                                    <div class="crm-descriptions-item-label">
                                        状态
                                    </div>
                                    <div class="crm-descriptions-item-content">
                                        等待发货
                                    </div>
                                </div>
                                <div class="crm-descriptions-item">
                                    <div class="crm-descriptions-item-label">
                                        说明
                                    </div>
                                    <div class="crm-descriptions-item-content">
                                        配件用纸箱打包；明天发走
                                    </div>
                                </div>
                                <div class="crm-descriptions-item hasbg">
                                    <div class="crm-descriptions-item-label">
                                        相关文档
                                    </div>
                                    <div class="crm-descriptions-item-content">
                                        0
                                    </div>
                                </div>
                                <div class="crm-descriptions-item hasbg">
                                    <div class="crm-descriptions-item-label">
                                        是否工程
                                    </div>
                                    <div class="crm-descriptions-item-content">
                                        材料单
                                    </div>
                                </div>
                                <div class="crm-descriptions-item no-border">
                                    <div class="crm-descriptions-item-label">
                                        是否发货
                                    </div>
                                    <div class="crm-descriptions-item-content">
                                        <div class="flex align-center">
                                            <span>已发货</span>
                                            <el-popover trigger="click" popper-class="crm-popover">
                                                <template slot="reference">
                                                    <div class="flex align-center ml-10 pointer">
                                                        <i class="el-icon-edit-outline primary"></i>
                                                        <span>修改</span>
                                                    </div>
                                                </template>
                                                    <!-- 是否发货状态修改 -->
                                                    <div class="crm-deliver-dropdown-title">是否发货状态修改</div>
                                                    <el-form :model="form" label-width="88px"
                                                        class="crm-deliver-dropdown-form">
                                                        <el-form-item label="是否发货">
                                                            <el-select v-model="form.deliver" placeholder=""
                                                                style="width: 100%;">
                                                                <el-option label="已发货" value="1"></el-option>
                                                                <el-option label="未发货" value="0"></el-option>
                                                                <el-option label="阶段发货" value="2"></el-option>
                                                            </el-select>
                                                        </el-form-item>
                                                    </el-form>
                                                    <div class="crm-deliver-dropdown-content">
                                                        <div class="crm-deliver-dropdown-content-title">修改日志</div>
                                                        <div class=crm-deliver-dropdown-content-list>
                                                            <div class="crm-deliver-dropdown-content-item"
                                                                v-for="item in 3" :key="item">
                                                                <el-avatar :size="30" src="~@/assets/images/avatar.png"
                                                                    class="avatar"></el-avatar>
                                                                <div class="content">
                                                                    <span class="info">张三</span>
                                                                    <span class="desc">于</span>
                                                                    <span class="info">2024-10-31(周四) 10:00:00</span>
                                                                    <span class="desc">使用</span>
                                                                    <span class="primary">批量修改</span>
                                                                    <span class="desc">功能修改是否发货状态为：</span>
                                                                    <span class="info">已发货</span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="crm-deliver-dropdown-btns">
                                                        <el-button>关闭</el-button>
                                                        <el-button type="primary">确定</el-button>
                                                    </div>
                                            </el-popover>
                                        </div>
                                    </div>
                                </div>
                                <div class="crm-descriptions-item no-border">
                                    <div class="crm-descriptions-item-label">
                                        审批状态
                                    </div>
                                    <div class="crm-descriptions-item-content">
                                        审批通过
                                    </div>
                                </div>
                            </div>
                            <div class="crm-table-title">成交产品</div>
                            <el-table :data="crmTableData" style="width: 100%" stripe class="crm-table">
                                <el-table-column type="index" label="序号" width="80" align="center" />
                                <el-table-column prop="name" label="产品名称" align="center" show-overflow-tooltip>
                                    <template slot-scope="scope">
                                        <span class="table-link">{{ scope.row.name }}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="image" label="图片" width="100" align="center">
                                    <template slot-scope="scope">
                                        <el-image :src="scope.row.image" fit="contain"
                                            style="width: 60px; height: 60px;"></el-image>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="model" label="产品型号" align="center" show-overflow-tooltip />
                                <el-table-column prop="cost" label="产品成本(元)" align="center" />
                                <el-table-column prop="price" label="成交单价(元)" align="center" />
                                <el-table-column prop="quantity" label="成交数量" align="center" />
                                <el-table-column prop="total" label="成交总价(元)" align="center">
                                    <template slot-scope="scope">
                                        <span class="color-orange">{{ scope.row.total }}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="remark" label="备注" align="center" show-overflow-tooltip />
                            </el-table>
                            <div class="crm-table-total">
                                <div class="flex align-center">
                                    <div class="crm-table-total-item">
                                        <span>共</span>
                                        <span class="primary ml-5 mr-5">{{ crmTableData.length }}</span>
                                        <span>个产品</span>
                                    </div>
                                    <div class="crm-table-total-item ml-100">
                                        <span>成交总量</span>
                                        <span class="primary ml-5">5,137,941.52</span>
                                    </div>
                                </div>
                                <div class="crm-table-total-item">
                                    <span>成交总额</span>
                                    <span class="orange ml-5">5,137,941.52</span>
                                </div>
                            </div>
                            <!-- </div> -->
                            <!-- <div class="company_concent1_table" v-if="rightTabActive == 2">
                                <div class="table_row bg1">
                                    <div class="table_item">
                                        <span class="title lfw160">负责人</span>
                                        <div class="concent">
                                            <span>李二愣子</span>
                                        </div>
                                    </div>
                                    <div class="table_item">
                                        <span class="title lfw160">获得客户时间</span>
                                        <div class="concent">
                                            <span>2022-8-15(周一)</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="table_row bg2">
                                    <div class="table_item">
                                        <span class="title lfw160">客户名称</span>
                                        <div class="concent">
                                            <span>河北世盛金属制品有限公司</span>
                                        </div>
                                    </div>
                                    <div class="table_item">
                                        <span class="title lfw160">所在城市</span>
                                        <div class="concent">
                                            <span>河北省邯郸市</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="table_row bg1">
                                    <div class="table_item">
                                        <span class="title lfw160">电话是否核实(总经办填写)</span>
                                        <div class="concent">
                                            <span>--</span>
                                        </div>
                                    </div>
                                    <div class="table_item">
                                        <span class="title lfw160">客户类别</span>
                                        <div class="concent">
                                            <span>--</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="table_row bg2">
                                    <div class="table_item">
                                        <span class="title lfw160">客户状态</span>
                                        <div class="concent">
                                            <span>--</span>
                                        </div>
                                    </div>
                                    <div class="table_item">
                                        <span class="title lfw160">客户等级</span>
                                        <div class="concent">
                                            <span>--</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="table_row bg1">
                                    <div class="table_item">
                                        <span class="title lfw160">客户来源</span>
                                        <div class="concent">
                                            <span>--</span>
                                        </div>
                                    </div>
                                    <div class="table_item">
                                        <span class="title lfw160">公司地址</span>
                                        <div class="concent">
                                            <span>河北省邯郸市永年区某某路188号世盛大厦</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="table_row bg2">
                                    <div class="table_item">
                                        <span class="title lfw160">咨询</span>
                                        <div class="concent">
                                            <span>--</span>
                                        </div>
                                    </div>
                                    <div class="table_item">
                                        <span class="title lfw160">企业微信是否已加</span>
                                        <div class="concent">
                                            <span>否</span>
                                        </div>
                                    </div>
                                </div>
                            </div> -->
                        </div>
                        <div class="company_concent" v-if="leftTabActive == 3">
                            <div class="company_concent1_card" v-if="rightTabActive == 1">
                                <div class="card_item">
                                    <div class="title">
                                        <span>数据来源</span>
                                    </div>
                                    <div class="concent">
                                        <span>电脑客户端录入</span>
                                    </div>
                                </div>
                                <div class="card_item">
                                    <div class="title">
                                        <span>成交产品总数</span>
                                    </div>
                                    <div class="concent">
                                        <span class="red">1299452</span>
                                    </div>
                                </div>
                                <div class="card_item">
                                    <div class="title">
                                        <span>最后收款金额</span>
                                    </div>
                                    <div class="concent">
                                        <span class="red">￥ 33088</span>
                                    </div>
                                </div>
                                <div class="card_item">
                                    <div class="title">
                                        <span>收款总次数</span>
                                    </div>
                                    <div class="concent">
                                        <span>47</span>
                                    </div>
                                </div>
                                <div class="card_item">
                                    <div class="title">
                                        <span>收款比例</span>
                                    </div>
                                    <div class="concent">
                                        <el-progress :stroke-width="15" :percentage="70" color="#A2C3FF"></el-progress>
                                    </div>
                                </div>
                                <div class="card_item">
                                    <div class="title">
                                        <span>退款总额</span>
                                        <img src="@/assets/images/add_c_grey.png" alt="">
                                    </div>
                                    <div class="concent">
                                        <span class="red">0</span>
                                    </div>
                                </div>
                                <div class="card_item">
                                    <div class="title">
                                        <span>退款总次数</span>
                                    </div>
                                    <div class="concent">
                                        <span>0</span>
                                    </div>
                                </div>
                                <div class="card_item">
                                    <div class="title">
                                        <span>投诉记录</span>
                                    </div>
                                    <div class="concent">
                                        <span class="red">0</span>
                                    </div>
                                </div>
                                <div class="card_item">
                                    <div class="title">
                                        <span>商机数</span>
                                    </div>
                                    <div class="concent">
                                        <span class="red">0</span>
                                    </div>
                                </div>
                                <div class="card_item">
                                    <div class="title">
                                        <span>费用收取总次数</span>
                                    </div>
                                    <div class="concent">
                                        <span class="red">47</span>
                                    </div>
                                </div>
                                <div class="card_item">
                                    <div class="title">
                                        <span>费用收取总金额</span>
                                    </div>
                                    <div class="concent">
                                        <span class="red">￥ 147107428</span>
                                    </div>
                                </div>
                                <div class="card_item">
                                    <div class="title">
                                        <span>费用支出总次数</span>
                                    </div>
                                    <div class="concent">
                                        <span class="red">0</span>
                                    </div>
                                </div>
                                <div class="card_item">
                                    <div class="title">
                                        <span>费用支出总金额</span>
                                    </div>
                                    <div class="concent">
                                        <span class="red">0</span>
                                    </div>
                                </div>
                                <div class="card_item">
                                    <div class="title">
                                        <span>开票比例</span>
                                    </div>
                                    <div class="concent">
                                        <el-progress :stroke-width="15" :percentage="10" color="#A2C3FF"></el-progress>
                                    </div>
                                </div>
                                <div class="card_item">
                                    <div class="title">
                                        <span>合同总数</span>
                                    </div>
                                    <div class="concent">
                                        <span>0</span>
                                    </div>
                                </div>
                                <div class="card_item">
                                    <div class="title">
                                        <span>合同总金额</span>
                                    </div>
                                    <div class="concent">
                                        <span>0</span>
                                    </div>
                                </div>
                                <div class="card_item">
                                    <div class="title">
                                        <span>合同最后有效结束时间</span>
                                    </div>
                                    <div class="concent">
                                        <span>--</span>
                                    </div>
                                </div>
                                <div class="card_item">
                                    <div class="title">
                                        <span>合同最后状态</span>
                                    </div>
                                    <div class="concent">
                                        <span>--</span>
                                    </div>
                                </div>
                                <div class="card_item">
                                    <div class="title">
                                        <span>最后修改时间</span>
                                    </div>
                                    <div class="concent">
                                        <span class="red">刚刚</span>
                                    </div>
                                </div>
                                <div class="card_item">
                                    <div class="title">
                                        <span>修改次数</span>
                                    </div>
                                    <div class="concent">
                                        <span>3</span>
                                    </div>
                                </div>
                                <div class="card_item">
                                    <div class="title">
                                        <span>最后成交时间</span>
                                    </div>
                                    <div class="concent">
                                        <span>2024-12-10(周二)</span>
                                    </div>
                                </div>
                                <div class="card_item">
                                    <div class="title">
                                        <span>订单到期时间</span>
                                    </div>
                                    <div class="concent">
                                        <span>2024-12-10(周二) 过期74天</span>
                                    </div>
                                </div>
                                <div class="card_item">
                                    <div class="title">
                                        <span>最后跟进阶段</span>
                                    </div>
                                    <div class="concent">
                                        <span>--</span>
                                    </div>
                                </div>
                                <div class="card_item">
                                    <div class="title">
                                        <span>剩余积分</span>
                                    </div>
                                    <div class="concent">
                                        <span class="red">0</span>
                                    </div>
                                </div>
                                <div class="card_item">
                                    <div class="title">
                                        <span>成交周期</span>
                                    </div>
                                    <div class="concent">
                                        <span>1天9小时</span>
                                    </div>
                                </div>
                                <div class="card_item">
                                    <div class="title">
                                        <span>首次成交时间</span>
                                    </div>
                                    <div class="concent">
                                        <span>2022-8-17(周三)</span>
                                    </div>
                                </div>
                                <div class="card_item">
                                    <div class="title">
                                        <span>首次跟进时间</span>
                                    </div>
                                    <div class="concent">
                                        <span>--</span>
                                    </div>
                                </div>
                                <div class="card_item">
                                    <div class="title">
                                        <span>首次跟进时长</span>
                                    </div>
                                    <div class="concent">
                                        <span>--</span>
                                    </div>
                                </div>
                                <div class="card_item">
                                    <div class="title">
                                        <span>首次跟进人</span>
                                    </div>
                                    <div class="concent">
                                        <span>--</span>
                                    </div>
                                </div>
                                <div class="card_item">
                                    <div class="title">
                                        <span>联系人总数</span>
                                    </div>
                                    <div class="concent">
                                        <span class="red">1</span>
                                    </div>
                                </div>
                                <div class="card_item">
                                    <div class="title">
                                        <span>客户模板</span>
                                    </div>
                                    <div class="concent">
                                        <span>--</span>
                                    </div>
                                </div>
                                <div class="card_item">
                                    <div class="title">
                                        <span>评论数</span>
                                    </div>
                                    <div class="concent">
                                        <span class="red">0</span>
                                    </div>
                                </div>
                            </div>
                            <div class="company_concent1_table" v-if="rightTabActive == 2">
                                <div class="table_row bg1">
                                    <div class="table_item">
                                        <span class="title lfw160">数据来源</span>
                                        <div class="concent">
                                            <span>电脑客户端录入</span>
                                        </div>
                                    </div>
                                    <div class="table_item">
                                        <span class="title lfw160">成交产品总数</span>
                                        <div class="concent">
                                            <span class="red">1299452</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="table_row bg2">
                                    <div class="table_item">
                                        <span class="title lfw160">最后收款金额</span>
                                        <div class="concent">
                                            <span class="red">￥ 33088</span>
                                        </div>
                                    </div>
                                    <div class="table_item">
                                        <span class="title lfw160">收款总次数</span>
                                        <div class="concent">
                                            <span>47</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="table_row bg1">
                                    <div class="table_item">
                                        <span class="title lfw160">收款比例</span>
                                        <div class="concent">
                                            <el-progress :stroke-width="15" :percentage="70" color="#A2C3FF"
                                                style="width: 100%;"></el-progress>
                                        </div>
                                    </div>
                                    <div class="table_item">
                                        <span class="title lfw160">退款总额</span>
                                        <div class="concent">
                                            <span class="red">0</span>
                                            <img src="@/assets/images/add_c_grey.png" alt="">
                                        </div>
                                    </div>
                                </div>
                                <div class="table_row bg2">
                                    <div class="table_item">
                                        <span class="title lfw160">退款总次数</span>
                                        <div class="concent">
                                            <span>0</span>
                                        </div>
                                    </div>
                                    <div class="table_item">
                                        <span class="title lfw160">投诉记录</span>
                                        <div class="concent">
                                            <span class="red">0</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="table_row bg1">
                                    <div class="table_item">
                                        <span class="title lfw160">商机数</span>
                                        <div class="concent">
                                            <span class="red">0</span>
                                        </div>
                                    </div>
                                    <div class="table_item">
                                        <span class="title lfw160">费用收取总次数</span>
                                        <div class="concent">
                                            <span class="red">47</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="table_row bg2">
                                    <div class="table_item">
                                        <span class="title lfw160">费用收取总金额</span>
                                        <div class="concent">
                                            <span class="red">￥ 147107428</span>
                                        </div>
                                    </div>
                                    <div class="table_item">
                                        <span class="title lfw160">费用支出总次数</span>
                                        <div class="concent">
                                            <span class="red">0</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="table_row bg1">
                                    <div class="table_item">
                                        <span class="title lfw160">费用支出总金额</span>
                                        <div class="concent">
                                            <span class="red">0</span>
                                        </div>
                                    </div>
                                    <div class="table_item">
                                        <span class="title lfw160">开票比例</span>
                                        <div class="concent">
                                            <el-progress :stroke-width="15" :percentage="10" color="#A2C3FF"
                                                style="width: 100%;"></el-progress>
                                        </div>
                                    </div>
                                </div>
                                <div class="table_row bg2">
                                    <div class="table_item">
                                        <span class="title lfw160">合同总数</span>
                                        <div class="concent">
                                            <span>0</span>
                                        </div>
                                    </div>
                                    <div class="table_item">
                                        <span class="title lfw160">合同总金额</span>
                                        <div class="concent">
                                            <span>0</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="table_row bg1">
                                    <div class="table_item">
                                        <span class="title lfw160">合同最后有效结束时间</span>
                                        <div class="concent">
                                            <span>--</span>
                                        </div>
                                    </div>
                                    <div class="table_item">
                                        <span class="title lfw160">合同最后状态</span>
                                        <div class="concent">
                                            <span>--</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="table_row bg2">
                                    <div class="table_item">
                                        <span class="title lfw160">最后修改时间</span>
                                        <div class="concent">
                                            <span class="red">刚刚</span>
                                        </div>
                                    </div>
                                    <div class="table_item">
                                        <span class="title lfw160">修改次数</span>
                                        <div class="concent">
                                            <span>3</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="table_row bg1">
                                    <div class="table_item">
                                        <span class="title lfw160">最后成交时间</span>
                                        <div class="concent">
                                            <span>2024-12-10(周二)</span>
                                        </div>
                                    </div>
                                    <div class="table_item">
                                        <span class="title lfw160">订单到期时间</span>
                                        <div class="concent">
                                            <span>2024-12-10(周二) 过期74天</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="table_row bg2">
                                    <div class="table_item">
                                        <span class="title lfw160">最后跟进阶段</span>
                                        <div class="concent">
                                            <span>--</span>
                                        </div>
                                    </div>
                                    <div class="table_item">
                                        <span class="title lfw160">剩余积分</span>
                                        <div class="concent">
                                            <span class="red">0</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="table_row bg1">
                                    <div class="table_item">
                                        <span class="title lfw160">成交周期</span>
                                        <div class="concent">
                                            <span>1天9小时</span>
                                        </div>
                                    </div>
                                    <div class="table_item">
                                        <span class="title lfw160">首次成交时间</span>
                                        <div class="concent">
                                            <span>2022-8-17(周三)</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="table_row bg2">
                                    <div class="table_item">
                                        <span class="title lfw160">首次跟进时间</span>
                                        <div class="concent">
                                            <span>--</span>
                                        </div>
                                    </div>
                                    <div class="table_item">
                                        <span class="title lfw160">首次跟进时长</span>
                                        <div class="concent">
                                            <span>--</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="table_row bg1">
                                    <div class="table_item">
                                        <span class="title lfw160">首次跟进人</span>
                                        <div class="concent">
                                            <span>--</span>
                                        </div>
                                    </div>
                                    <div class="table_item">
                                        <span class="title lfw160">联系人总数</span>
                                        <div class="concent">
                                            <span class="red">1</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="table_row bg2">
                                    <div class="table_item">
                                        <span class="title lfw160">客户模板</span>
                                        <div class="concent">
                                            <span>--</span>
                                        </div>
                                    </div>
                                    <div class="table_item">
                                        <span class="title lfw160">评论数</span>
                                        <div class="concent">
                                            <span class="red">0</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div v-show="companyTabActive == '2'" class="company_list_info3">
                        <div class="contacts_select_box">
                            <el-select v-model="contactsSort" placeholder="请选择排序方式" size="small">
                                <el-option label="排序1" :value="1"></el-option>
                                <el-option label="排序2" :value="2"></el-option>
                                <el-option label="排序3" :value="3"></el-option>
                            </el-select>
                            <el-button type="primary" icon="el-icon-plus" size="small" class="ml-10">新建收款</el-button>
                        </div>
                        <div class="contacts_table_box">
                            <el-table ref="contactTable" stripe :data="collectionData" class="custom-table">
                                <el-table-column align="center" type="index" label="序号" width="50"></el-table-column>
                                <el-table-column align="center" prop="creator" label="录入人"
                                    show-overflow-tooltip></el-table-column>
                                <el-table-column align="center" prop="createTime" label="录入时间"
                                    show-overflow-tooltip></el-table-column>
                                <el-table-column align="center" prop="owner" label="负责人"
                                    show-overflow-tooltip></el-table-column>
                                <el-table-column align="center" prop="paymentTime" label="收款时间"
                                    show-overflow-tooltip></el-table-column>
                                <el-table-column align="center" prop="amount" label="收款金额" show-overflow-tooltip>
                                    <template slot-scope="{ row }">
                                        <span class="table-price">￥ {{ row.amount }}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column align="center" prop="remark" label="备注"
                                    show-overflow-tooltip></el-table-column>
                            </el-table>
                        </div>
                    </div>

                    <div v-if="companyTabActive == '3'" class="company_list_info3">
                        <div class="contacts_select_box">
                            <el-select v-model="contactsSort" placeholder="请选择排序方式" size="small">
                                <el-option label="排序1" :value="1"></el-option>
                                <el-option label="排序2" :value="2"></el-option>
                                <el-option label="排序3" :value="3"></el-option>
                            </el-select>
                            <el-button type="primary" icon="el-icon-plus" size="small" class="ml-10" @click="handleRefund">新建退款</el-button>
                        </div>
                        <div class="contacts_table_box">
                            <el-table v-loading="contactsLoading" ref="contactTable" stripe :data="contactsList"
                                :key="'contacts' + key" style="width: 100%" max-height="657" class="custom-table"
                                @selection-change="handleAllSelects" @cell-mouse-enter="handleCellEnter"
                                @cell-mouse-leave="handleCellLeave">
                                <el-table-column align="center" type="selection" width="50"></el-table-column>
                                <el-table-column align="center" type="index" label="序号" width="50"></el-table-column>
                                <el-table-column align="center" prop="name" label="姓名"
                                    show-overflow-tooltip></el-table-column>
                                <el-table-column align="center" prop="phone" label="手机号" show-overflow-tooltip>
                                </el-table-column>
                                <el-table-column align="center" label="公司座机" prop="tel"
                                    show-overflow-tooltip></el-table-column>
                                <el-table-column align="center" prop="email" label="邮箱"
                                    show-overflow-tooltip></el-table-column>
                                <el-table-column align="center" label="操作" width="250px">
                                    <template slot-scope="{ row }">
                                        <div class="contacts_btn">
                                            <div class="edit_btn" :class="row.contactsBtnHover ? 'hovers' : ''">
                                                <img src="@/assets/images/edit1.png" alt=""
                                                    v-if="!row.contactsBtnHover">
                                                <img src="@/assets/images/edit2.png" alt="" v-if="row.contactsBtnHover">
                                                <span>编辑</span>
                                            </div>
                                            <div class="delete_btn" :class="row.contactsBtnHover ? 'hovers' : ''">
                                                <img src="@/assets/images/delete1.png" alt=""
                                                    v-if="!row.contactsBtnHover">
                                                <img src="@/assets/images/delete2.png" alt=""
                                                    v-if="row.contactsBtnHover">
                                                <span>删除</span>
                                            </div>
                                        </div>
                                    </template>
                                </el-table-column>
                            </el-table>
                            <div class="contacts_table_allCheck">
                                <el-checkbox v-model="contactsAllChecked" class="all_check">全选</el-checkbox>
                                <div class="delete_btn">
                                    <img src="@/assets/images/delete2.png" alt="">
                                    <span>删除</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div v-if="companyTabActive == '4'" class="company_list_info3">
                        <div class="contacts_select_box contract_box">
                            <div class="contract_tabs">
                                <div class="contract_tab_item" :class="contractTabActive == 1 ? 'active' : ''"
                                    @click="contractTabActive = 1">全部合同</div>
                                <div class="contract_tab_item" :class="contractTabActive == 2 ? 'active' : ''"
                                    @click="contractTabActive = 2">销售合同</div>
                                <div class="contract_tab_item" :class="contractTabActive == 3 ? 'active' : ''"
                                    @click="contractTabActive = 3">采购合同</div>
                            </div>
                            <el-select v-model="contactsSort" placeholder="请选择排序方式" size="small">
                                <el-option label="排序1" :value="1"></el-option>
                                <el-option label="排序2" :value="2"></el-option>
                                <el-option label="排序3" :value="3"></el-option>
                            </el-select>
                            <div class="contacts_add_btn">
                                <img class="img" src="@/assets/images/add_icon.png" alt="">
                                添加合同
                            </div>
                        </div>
                        <div class="contacts_table_box">
                            <el-table v-loading="contactsLoading" ref="contactTable" stripe :data="contactsList"
                                :key="'contract' + key" style="width: 100%" max-height="657" class="custom-table"
                                @selection-change="handleAllSelects" @cell-mouse-enter="handleCellEnter"
                                @cell-mouse-leave="handleCellLeave">
                                <el-table-column align="center" type="selection" width="50"></el-table-column>
                                <el-table-column align="center" type="index" label="序号" width="50"></el-table-column>
                                <el-table-column align="center" prop="name" label="合同编号" show-overflow-tooltip
                                    width="150"></el-table-column>
                                <el-table-column align="center" prop="phone" label="合同类型" show-overflow-tooltip
                                    width="150">
                                </el-table-column>
                                <el-table-column align="center" label="签订时间" prop="tel" show-overflow-tooltip
                                    width="150"></el-table-column>
                                <el-table-column align="center" prop="email" label="合同产品" show-overflow-tooltip
                                    width="150"></el-table-column>
                                <el-table-column align="center" prop="email" label="合同金额" show-overflow-tooltip
                                    width="150"></el-table-column>
                                <el-table-column align="center" label="操作" width="250px" fixed="right">
                                    <template slot-scope="{ row }">
                                        <div class="contacts_btn">
                                            <div class="edit_btn" :class="row.contactsBtnHover ? 'hovers' : ''">
                                                <span>查看详情</span>
                                            </div>
                                            <div class="delete_btn" :class="row.contactsBtnHover ? 'hovers' : ''">
                                                <img src="@/assets/images/delete1.png" alt=""
                                                    v-if="!row.contactsBtnHover">
                                                <img src="@/assets/images/delete2.png" alt=""
                                                    v-if="row.contactsBtnHover">
                                                <span>删除</span>
                                            </div>
                                        </div>
                                    </template>
                                </el-table-column>
                            </el-table>
                            <div class="contacts_table_allCheck">
                                <el-checkbox v-model="contactsAllChecked" class="all_check">全选</el-checkbox>
                                <div class="delete_btn">
                                    <img src="@/assets/images/delete2.png" alt="">
                                    <span>删除</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div v-if="companyTabActive == '5'" class="company_list_info3">
                        <div class="contacts_select_box contract_box">
                            <div class="contract_tabs">
                                <div class="contract_tab_item" :class="orderTabActive == 1 ? 'active' : ''"
                                    @click="orderTabActive = 1">全部订单
                                </div>
                                <div class="contract_tab_item" :class="orderTabActive == 2 ? 'active' : ''"
                                    @click="orderTabActive = 2">销售订单
                                </div>
                                <div class="contract_tab_item" :class="orderTabActive == 3 ? 'active' : ''"
                                    @click="orderTabActive = 3">采购订单
                                </div>
                            </div>
                            <el-select v-model="contactsSort" placeholder="请选择排序方式" size="small">
                                <el-option label="排序1" :value="1"></el-option>
                                <el-option label="排序2" :value="2"></el-option>
                                <el-option label="排序3" :value="3"></el-option>
                            </el-select>
                            <div class="contacts_add_btn">
                                <img class="img" src="@/assets/images/add_icon.png" alt="">
                                添加订单
                            </div>
                            <div class="contacts_add_btn">
                                <img class="img" src="@/assets/images/add_icon.png" alt="">
                                发起收款
                            </div>
                        </div>
                        <div class="contacts_table_box">
                            <el-table v-loading="contactsLoading" ref="contactTable" stripe :data="contactsList"
                                :key="'order' + key" style="width: 100%" max-height="657" class="custom-table"
                                @selection-change="handleAllSelects" @cell-mouse-enter="handleCellEnter"
                                @cell-mouse-leave="handleCellLeave">
                                <el-table-column align="center" type="selection" width="50"></el-table-column>
                                <el-table-column align="center" type="index" label="序号" width="50"></el-table-column>
                                <el-table-column align="center" prop="name" label="成交总数量" show-overflow-tooltip
                                    width="150"></el-table-column>
                                <el-table-column align="center" prop="phone" label="负责人" show-overflow-tooltip
                                    width="150">
                                </el-table-column>
                                <el-table-column align="center" label="成交时间" prop="tel" show-overflow-tooltip
                                    width="150"></el-table-column>
                                <el-table-column align="center" prop="email" label="成交产品" show-overflow-tooltip
                                    width="150"></el-table-column>
                                <el-table-column align="center" prop="email" label="成交总额" show-overflow-tooltip
                                    width="150"></el-table-column>
                                <el-table-column align="center" label="操作" width="250px" fixed="right">
                                    <template slot-scope="{ row }">
                                        <div class="contacts_btn">
                                            <div class="edit_btn" :class="row.contactsBtnHover ? 'hovers' : ''">
                                                <span>查看详情</span>
                                            </div>
                                            <div class="delete_btn" :class="row.contactsBtnHover ? 'hovers' : ''">
                                                <img src="@/assets/images/delete1.png" alt=""
                                                    v-if="!row.contactsBtnHover">
                                                <img src="@/assets/images/delete2.png" alt=""
                                                    v-if="row.contactsBtnHover">
                                                <span>删除</span>
                                            </div>
                                        </div>
                                    </template>
                                </el-table-column>
                            </el-table>
                            <div class="contacts_table_allCheck">
                                <el-checkbox v-model="contactsAllChecked" class="all_check">全选</el-checkbox>
                                <div style="display: flex;">
                                    <div class="export_btn">
                                        <img src="@/assets/images/export1.png" alt="">
                                        <span>导出</span>
                                    </div>
                                    <div class="delete_btn">
                                        <img src="@/assets/images/delete2.png" alt="">
                                        <span>删除</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div v-if="companyTabActive == '6'" class="company_list_info3">
                        <div class="contacts_select_box">
                            <el-select v-model="contactsSort" placeholder="请选择排序方式" size="small">
                                <el-option label="排序1" :value="1"></el-option>
                                <el-option label="排序2" :value="2"></el-option>
                                <el-option label="排序3" :value="3"></el-option>
                            </el-select>
                            <div class="contacts_add_btn">
                                <img class="img" src="@/assets/images/add_icon.png" alt="">
                                新建收款
                            </div>
                        </div>
                        <div class="contacts_table_box">
                            <el-table v-loading="contactsLoading" ref="contactTable" stripe :data="contactsList"
                                :key="'payment' + key" style="width: 100%" max-height="657" class="custom-table"
                                @selection-change="handleAllSelects" @cell-mouse-enter="handleCellEnter"
                                @cell-mouse-leave="handleCellLeave">
                                <el-table-column align="center" type="selection" width="50"></el-table-column>
                                <el-table-column align="center" type="index" label="序号" width="50"></el-table-column>
                                <el-table-column align="center" prop="name" label="销售合同" show-overflow-tooltip
                                    width="150"></el-table-column>
                                <el-table-column align="center" prop="phone" label="负责人" show-overflow-tooltip
                                    width="150">
                                </el-table-column>
                                <el-table-column align="center" label="收款时间" prop="tel" show-overflow-tooltip
                                    width="150"></el-table-column>
                                <el-table-column align="center" prop="email" label="录入时间" show-overflow-tooltip
                                    width="150"></el-table-column>
                                <el-table-column align="center" prop="email" label="收款金额" show-overflow-tooltip
                                    width="150"></el-table-column>
                                <el-table-column align="center" label="操作" width="250px" fixed="right">
                                    <template slot-scope="{ row }">
                                        <div class="contacts_btn">
                                            <div class="edit_btn" :class="row.contactsBtnHover ? 'hovers' : ''">
                                                <span>查看详情</span>
                                            </div>
                                            <div class="delete_btn" :class="row.contactsBtnHover ? 'hovers' : ''">
                                                <img src="@/assets/images/delete1.png" alt=""
                                                    v-if="!row.contactsBtnHover">
                                                <img src="@/assets/images/delete2.png" alt=""
                                                    v-if="row.contactsBtnHover">
                                                <span>删除</span>
                                            </div>
                                        </div>
                                    </template>
                                </el-table-column>
                            </el-table>
                            <div class="contacts_table_allCheck">
                                <el-checkbox v-model="contactsAllChecked" class="all_check">全选</el-checkbox>
                                <div style="display: flex;">
                                    <div class="export_btn">
                                        <img src="@/assets/images/export1.png" alt="">
                                        <span>导出</span>
                                    </div>
                                    <div class="delete_btn">
                                        <img src="@/assets/images/delete2.png" alt="">
                                        <span>删除</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div v-if="companyTabActive == '8'" class="company_list_info3">
                        <div class="contacts_select_box">
                            <el-select v-model="contactsSort" placeholder="请选择" size="small">
                                <el-option label="参与人1" :value="1"></el-option>
                                <el-option label="参与人2" :value="2"></el-option>
                                <el-option label="参与人3" :value="3"></el-option>
                            </el-select>
                            <div class="custom-search-form flex" style="margin-left: 20px;">
                                <input type="text" v-model="keyword" placeholder="请输入关键字"
                                    class="custom-search-input" @keyup.enter="handleQuery" />
                                <button type="button" class="custom-search-button pointer" @click="handleQuery">
                                    <i class="el-icon-search"></i>
                                    搜索
                                </button>
                            </div>
                            <div class="contacts_add_btn">
                                <img class="img" src="@/assets/images/add_icon.png" alt="">
                                添加跟进
                            </div>
                        </div>
                        <div class="follow_box">
                            <div class="follow_item">
                                <div class="follow_left">
                                    <img src="@/assets/images/listing_icon.png" alt="">
                                </div>
                                <div class="follow_right">
                                    <div class="right_top">
                                        <div class="right_top_name">
                                            <span>业务员-李二</span>
                                            <span class="top">置顶</span>
                                        </div>
                                        <div class="right_top_time">2024-09-29 (周六) 12:30</div>
                                    </div>
                                    <div class="right_contacts">
                                        <span class="right_contacts_title">对方联系人：</span>
                                        <span class="right_contacts_name">内个谁</span>
                                    </div>
                                    <div class="right_concent">关于 订单54645646164 原定回款周期为三个月，目前已经超期两周，约定三个月内完成结款</div>
                                    <div class="right_btns">
                                        <div class="right_btns_item">
                                            <img src="@/assets/images/comment.png" alt="">
                                            <span>评论</span>
                                        </div>
                                        <div class="right_btns_item">
                                            <img src="@/assets/images/comment.png" alt="">
                                            <span>置顶</span>
                                        </div>
                                        <div class="right_btns_item">
                                            <img src="@/assets/images/comment.png" alt="">
                                            <span>关注</span>
                                        </div>
                                        <div class="right_btns_item">
                                            <img src="@/assets/images/comment.png" alt="">
                                            <span>修改</span>
                                        </div>
                                        <div class="right_btns_item">
                                            <img src="@/assets/images/comment.png" alt="">
                                            <span>删除</span>
                                        </div>
                                    </div>
                                    <div class="right_reply_list">
                                        <div class="right_reply_item">
                                            <div class="reply_item_left">
                                                <img src="@/assets/images/listing_icon.png" alt="">
                                            </div>
                                            <div class="reply_item_right">
                                                <div class="reply_item_right_name">销售部-王大头</div>
                                                <div class="reply_item_right_concent">跟进及时，账期缩短了很多，点个赞</div>
                                                <div class="reply_item_right_time">2024-10-01 (周一) 09:29</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </el-drawer>

        <add-refund ref="addRefund" />
    </div>
</template>

<script>
import addRefund from './addRefund'
export default {
    components: { addRefund },
    data() {
        return {
            orderViewOpen: false,
            companyTabActive: '1',
            leftTabActive: 1,
            rightTabActive: 2,
            contactsSort: undefined,
            contactsLoading: false,
            contactsList: [{
                name: '王二麻子',
                phone: '18988889999',
                tel: '010-8651368',
                email: '<EMAIL>',
                contactsBtnHover: false
            }, {
                name: '王二麻子',
                phone: '18988889999',
                tel: '010-8651368',
                email: '<EMAIL>',
                contactsBtnHover: false
            }, {
                name: '王二麻子',
                phone: '18988889999',
                tel: '010-8651368',
                email: '<EMAIL>',
                contactsBtnHover: false
            }, {
                name: '王二麻子',
                phone: '18988889999',
                tel: '010-8651368',
                email: '<EMAIL>',
                contactsBtnHover: false
            }, {
                name: '王二麻子',
                phone: '18988889999',
                tel: '010-8651368',
                email: '<EMAIL>',
                contactsBtnHover: false
            }, {
                name: '王二麻子',
                phone: '18988889999',
                tel: '010-8651368',
                email: '<EMAIL>',
                contactsBtnHover: false
            },],
            contractTabActive: 1,
            orderTabActive: 1,
            moreActiveItem: '退款记录',
             // 成交产品
             crmTableData:[],
            // 是否发货状态修改
            form: {
                deliver: '1'
            },
            // 收款列表
            collectionData: [],
            orderTabsList: [{
                label: '订单',
                value: '1',
            }, {
                label: '收款记录',
                value: '2',
            }, {
                label: '退款记录',
                value: '3',
            }, {
                label: '收支记录',
                value: '4',
            }, {
                label: '合同',
                value: '5',
            }, {
                label: '日志/评论',
                value: '6',
            }, {
                label: '联系跟进',
                value: '8',
            },],
            key: 1,
            keyword: ''
        }
    },
    created() {
        this.crmTableData = [
            {
                name: '高速钢铣刀',
                image: 'https://example.com/image1.jpg',
                model: 'HSS-E-4F',
                cost: '580.00',
                price: '899.00',
                quantity: 50,
                total: '44,950.00',
                remark: '标准规格'
            },
            {
                name: '硬质合金刀片',
                image: 'https://example.com/image2.jpg', 
                model: 'CNMG120408',
                cost: '45.00',
                price: '68.00',
                quantity: 200,
                total: '13,600.00',
                remark: '通用型号'
            },
            {
                name: '数控刀具套装',
                image: 'https://example.com/image3.jpg',
                model: 'CNC-TS01',
                cost: '2,800.00',
                price: '3,999.00',
                quantity: 5,
                total: '19,995.00',
                remark: '含配件'
            },
            {
                name: '螺纹铣刀',
                image: 'https://example.com/image4.jpg',
                model: 'TM-25R',
                cost: '420.00',
                price: '699.00',
                quantity: 30,
                total: '20,970.00',
                remark: '精密加工'
            },
            {
                name: '钻头组合',
                image: 'https://example.com/image5.jpg',
                model: 'DB-120S',
                cost: '890.00',
                price: '1,299.00',
                quantity: 15,
                total: '19,485.00',
                remark: '20件套'
            },
            {
                name: '车刀杆',
                image: 'https://example.com/image6.jpg',
                model: 'SCLCR2020K09',
                cost: '360.00',
                price: '499.00',
                quantity: 40,
                total: '19,960.00',
                remark: '标准型'
            },
            {
                name: '立铣刀',
                image: 'https://example.com/image7.jpg',
                model: 'EMR-63',
                cost: '760.00',
                price: '1,099.00',
                quantity: 25,
                total: '27,475.00',
                remark: '高精度'
            },
            {
                name: '铰刀套装',
                image: 'https://example.com/image8.jpg',
                model: 'RMK-308',
                cost: '1,200.00',
                price: '1,899.00',
                quantity: 10,
                total: '18,990.00',
                remark: '10件套'
            }
        ]
        this.collectionData = [
            {
                creator: '张三',
                createTime: '2023-12-01(周五)',
                owner: '李四',
                paymentTime: '2023-12-05',
                amount: '12800.00',
                remark: '首付款'
            },
            {
                creator: '王五',
                createTime: '2023-12-02(周六)', 
                owner: '赵六',
                paymentTime: '2023-12-06',
                amount: '25600.00',
                remark: '二期款'
            },
            {
                creator: '孙七',
                createTime: '2023-12-03(周日)',
                owner: '周八',
                paymentTime: '2023-12-07', 
                amount: '18900.00',
                remark: '尾款'
            },
            {
                creator: '吴九',
                createTime: '2023-12-04(周一)',
                owner: '郑十',
                paymentTime: '2023-12-08',
                amount: '33000.00',
                remark: '预付款'
            },
            {
                creator: '冯一',
                createTime: '2023-12-05(周二)',
                owner: '陈二',
                paymentTime: '2023-12-09',
                amount: '45600.00',
                remark: '进度款'
            },
            {
                creator: '褚三',
                createTime: '2023-12-06(周三)',
                owner: '魏四',
                paymentTime: '2023-12-10',
                amount: '28900.00',
                remark: '质保金'
            },
            {
                creator: '蒋五',
                createTime: '2023-12-07(周四)',
                owner: '沈六',
                paymentTime: '2023-12-11',
                amount: '16800.00',
                remark: '定金'
            },
            {
                creator: '韩七',
                createTime: '2023-12-08(周五)',
                owner: '杨八',
                paymentTime: '2023-12-12',
                amount: '39800.00',
                remark: '中期款'
            },
            {
                creator: '朱九',
                createTime: '2023-12-09(周六)',
                owner: '秦十',
                paymentTime: '2023-12-13',
                amount: '22500.00',
                remark: '结算款'
            },
            {
                creator: '尤一',
                createTime: '2023-12-10(周日)',
                owner: '许二',
                paymentTime: '2023-12-14',
                amount: '41200.00',
                remark: '合同款'
            }
        ]
    },
    methods: {
        handleOpen() {
            this.orderViewOpen = true
        },
        handleCellEnter(row) {
            row.contactsBtnHover = true
        },
        handleCellLeave(row) {
            row.contactsBtnHover = false
        },
        handleAllSelects() { },
        contactsAllChecked() { },
        handleQuery() {},
        handleRefund() {
            this.$refs.addRefund.handleAdd()
        }
    }
}
</script>

<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
@import '~@/assets/styles/crm.scss';

.company_view {
    ::v-deep {
        .el-drawer__header {
            height: 50px;
            background: #EEF0F8;
            padding: 15px 20px;
            margin: 0;
        }
    }

    .company_box {
        padding: 20px;

        .company_basic {
            border: 1px solid #EBEDF3;
            background: #FFFFFF;
            margin-bottom: 20px;

            .basic_info {
                background: #F4F8FF;
                display: flex;
                align-items: self-start;
                padding: 30px 30px 20px;

                .img {
                    width: 90px;
                    height: 90px;
                    margin-right: 20px;
                }

                .text_box {
                    .name_box {
                        display: flex;
                        align-items: center;
                        margin-bottom: 15px;

                        .name {
                            font-weight: 500;
                            font-size: 24px;
                            color: #333333;
                            line-height: 24px;
                            margin-right: 30px;
                        }

                        .icon {
                            cursor: pointer;

                            img {
                                width: 30px;
                                height: 30px;
                            }

                            &:hover {
                                img {
                                    width: 120px;
                                    height: 30px;
                                }
                            }
                        }
                    }

                    .charge_person {
                        font-weight: 400;
                        font-size: 16px;
                        color: #666666;
                        line-height: 16px;
                        margin-bottom: 17px;
                    }

                    .company_basic_btn_list {
                        display: flex;
                        align-items: center;
                        margin-bottom: 15px;

                        .btn_item {
                            display: flex;
                            align-items: center;
                            font-weight: 400;
                            font-size: 14px;
                            color: #666666;
                            line-height: 20px;
                            margin-right: 30px;
                            cursor: pointer;

                            img {
                                width: 20px;
                                height: 20px;
                                margin-right: 3px;
                            }
                        }
                    }

                    .label_list {
                        display: flex;
                        align-items: center;

                        .label_item {
                            padding: 5px 15px;
                            font-weight: 400;
                            font-size: 14px;
                            color: #666666;
                            line-height: 20px;
                            background: #DDE3ED;
                            border-radius: 5px;
                            margin-right: 10px;
                        }

                        .add_btn {
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            font-weight: 400;
                            font-size: 14px;
                            color: #666666;
                            line-height: 20px;
                            border-radius: 5px;
                            border: 1px solid #C9CBCD;
                            width: 101px;
                            height: 30px;
                            cursor: pointer;
                            margin-right: 10px;

                            img {
                                width: 20px;
                                height: 20px;
                                margin-right: 5px;
                            }

                            &:hover {
                                background: #E0EBFF;
                                border: 1px solid #2E73F3;
                                font-weight: 500;
                                font-size: 14px;
                                color: #2E73F3;
                            }
                        }

                        .add_ipt {
                            .ipt {
                                width: 128px;
                                height: 30px;
                                background: #FFFFFF;
                                box-shadow: 0px 1px 15px 0px rgba(46, 115, 243, 0.35);
                                border-radius: 5px;
                                border: 1px solid #2E73F3;
                                padding-left: 15px;

                                &::placeholder {
                                    color: #2E73F3;
                                }
                            }
                        }
                    }
                }
            }

            .key_nodes {
                padding: 15px 20px 20px;

                .keyNodes_title {
                    font-weight: 500;
                    font-size: 16px;
                    color: #2E73F3;
                    line-height: 16px;
                    margin-bottom: 5px;
                }

                .keyNodes_concent {
                    display: flex;
                    align-items: center;
                    justify-content: center;

                    .keyNodes_item {
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        justify-content: center;
                        font-weight: 400;
                        font-size: 14px;
                        color: #999999;
                        line-height: 16px;

                        .m5 {
                            margin: 5px 0;
                        }

                        div {
                            display: flex;
                            align-items: center;
                            justify-content: center;

                            .num {
                                color: #333333;
                                font-weight: 500;
                            }

                            img {
                                width: 20px;
                                height: 20px;
                                margin-left: 10px;
                                cursor: pointer;
                            }
                        }


                    }

                    .keyNodes_line {
                        width: 260px;
                        height: 1px;
                        background: #D9D9D9;
                        margin: 0 10px;
                    }
                }
            }
        }

        .company_listInfo {
            .listInfo_tabs_box {
                // display: flex;
                // align-items: center;
                // justify-content: space-between;
                background: #F2F3F9;
                position: relative;

                // border-bottom: 1px solid #2E73F3;
                .listInfo_tabs_left {
                    position: relative;
                }

                ::v-deep {
                    .el-tabs--border-card {
                        box-shadow: none;
                        border: none;
                    }

                    .el-tabs__content {
                        padding: 0;
                    }

                    .el-tabs--border-card>.el-tabs__header {
                        background: #F2F3F9;
                        border-bottom: 1px solid #2E73F3;

                        .el-tabs__item {
                            color: #666666;

                            &.is-active {
                                color: #2E73F3;
                                border-top-color: #2E73F3;
                                border-right-color: #2E73F3;
                                border-left-color: #2E73F3;
                                margin-top: 0;
                                margin-left: 0;
                                border-top-left-radius: 5px;
                                border-top-right-radius: 5px;
                            }
                        }
                    }
                }

                .listInfo_tabs_right {
                    font-weight: 400;
                    font-size: 14px;
                    color: #666666;
                    display: flex;
                    align-items: center;
                    cursor: pointer;
                    position: absolute;
                    right: 20px;
                    top: 10px;

                    img {
                        width: 20px;
                        height: 20px;
                        margin-right: 2px;
                    }
                }
            }

            .company_list_info1 {
                margin-top: 15px;

                .company_tab_box {
                    height: 46px;
                    background: #F8F9FB;
                    border: 1px solid #EBEDF3;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;

                    .company_left {
                        display: flex;
                        padding-left: 20px;

                        .left_tab_item {
                            font-weight: 400;
                            font-size: 12px;
                            color: #666666;
                            line-height: 46px;
                            margin-right: 50px;
                            cursor: pointer;
                            height: 46px;

                            &.active {
                                border-bottom: 2px solid #2E73F3;
                                color: #2E73F3;
                                padding-bottom: 12px;
                                font-weight: 500;
                                font-size: 14px;
                            }
                        }
                    }

                    .company_right {
                        display: flex;
                        margin-right: 5px;
                        width: 160px;
                        height: 36px;
                        background: #E2E6F3;
                        border-radius: 5px;
                        line-height: 36px;
                        font-weight: 400;
                        font-size: 12px;
                        color: #666666;

                        .right_tab_item {
                            flex: 1;
                            text-align: center;
                            cursor: pointer;

                            &.active {
                                width: 68px;
                                height: 30px;
                                background: #FFFFFF;
                                box-shadow: 0px 1px 9px 0px rgba(0, 0, 0, 0.18);
                                border-radius: 5px;
                                margin: 3px 6px;
                                line-height: 30px;
                                font-weight: 500;
                                font-size: 12px;
                                color: #333333;
                            }
                        }
                    }
                }

                .company_concent {
                    margin-top: 15px;

                    .company_concent1_card {
                        display: flex;
                        flex-wrap: wrap;
                        align-items: center;

                        .card_item {
                            width: 192px;
                            height: 85px;
                            background: #F8FAFF;
                            border-radius: 5px;
                            border: 1px solid #EBEDF3;
                            margin-right: 20px;
                            margin-bottom: 20px;
                            padding: 9px 15px;
                            display: flex;
                            flex-direction: column;
                            justify-content: space-between;


                            .title {
                                display: flex;
                                align-items: center;
                                justify-content: space-between;
                                font-weight: 400;
                                font-size: 12px;
                                color: #666666;
                                line-height: 20px;

                                img {
                                    width: 20px;
                                    height: 20px;
                                }
                            }

                            .concent {
                                font-weight: 500;
                                font-size: 14px;
                                color: #333333;
                                line-height: 20px;

                                .red {
                                    color: #F02323;
                                }
                            }
                        }
                    }

                    .company_concent1_table {
                        border: 1px solid #EBEDF3;

                        .table_row {
                            display: flex;
                            align-items: center;
                            border-bottom: 1px solid #EBEDF3;
                            height: 40px;

                            &.bg1 {
                                background: #FFFFFF;
                            }

                            &.bg2 {
                                background: #CBD6E2;
                            }

                            &:last-child {
                                border-bottom: none;
                            }

                            .table_item {
                                flex: 1;
                                display: flex;
                                align-items: center;

                                .title {
                                    width: 92px;
                                    text-align: right;
                                    font-weight: 400;
                                    font-size: 12px;
                                    color: #666666;
                                    margin-right: 30px;

                                    &.lfw160 {
                                        width: 160px;
                                    }
                                }

                                .concent {
                                    font-weight: 500;
                                    font-size: 14px;
                                    color: #333333;
                                    display: flex;
                                    align-items: center;
                                    flex: 1;

                                    .red {
                                        color: #F02323;
                                    }

                                    img {
                                        width: 20px;
                                        height: 20px;
                                        margin-left: 14px;
                                        cursor: pointer;
                                    }
                                }
                            }
                        }
                    }

                }
            }

            .company_list_info2 {
                margin-top: 15px;
            }

            .company_list_info3 {
                margin-top: 15px;

                .contacts_select_box {
                    display: flex;
                    align-items: center;
                    margin-bottom: 15px;

                    .contacts_add_btn {
                        margin-left: 20px;
                        width: 115px;
                        height: 32px;
                        background: #2E73F3;
                        border-radius: 5px;
                        font-weight: 500;
                        font-size: 14px;
                        color: #FFFFFF;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        cursor: pointer;

                        .img {
                            width: 20px;
                            height: 20px;
                            margin-right: 5px;
                        }
                    }

                    &.contract_box {
                        height: 46px;
                        background: #F8F9FB;
                        border: 1px solid #EBEDF3;

                        .contract_tabs {
                            display: flex;
                            height: 46px;
                            line-height: 46px;
                            font-weight: 400;
                            font-size: 12px;
                            color: #666666;
                            padding-left: 20px;

                            .contract_tab_item {
                                margin-right: 50px;
                                cursor: pointer;

                                &.active {
                                    font-weight: 500;
                                    font-size: 14px;
                                    color: #2E73F3;
                                    border-bottom: 2px solid #2E73F3;
                                }
                            }
                        }
                    }
                }

                .contacts_table_box {
                    .contacts_btn {
                        display: flex;
                        align-items: center;
                        justify-content: space-around;

                        .edit_btn {
                            width: 101px;
                            height: 30px;
                            background: #FFFFFF;
                            border-radius: 5px;
                            border: 1px solid #2E73F3;
                            font-weight: 500;
                            font-size: 12px;
                            color: #2E73F3;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            cursor: pointer;

                            img {
                                width: 20px;
                                height: 20px;
                                margin-right: 4px;
                            }

                            &.hovers {
                                background: #2E73F3;
                                border: none;
                                color: #fff;
                            }
                        }

                        .delete_btn {
                            width: 101px;
                            height: 30px;
                            background: #FFFFFF;
                            border-radius: 5px;
                            border: 1px solid #F02323;
                            font-weight: 500;
                            font-size: 12px;
                            color: #F02323;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            cursor: pointer;

                            img {
                                width: 20px;
                                height: 20px;
                                margin-right: 4px;
                            }

                            &.hovers {
                                background: #F02323;
                                border: none;
                                color: #fff;
                            }
                        }
                    }

                    .contacts_table_allCheck {
                        margin-top: 10px;
                        height: 48px;
                        background: #F9FBFF;
                        border-radius: 5px;
                        border: 1px solid #2E73F3;
                        display: flex;
                        align-items: center;
                        justify-content: space-between;

                        .all_check {
                            padding-left: 14px;
                            font-weight: 400;
                            font-size: 12px;
                            color: #999999;
                            line-height: 20px;
                        }

                        .export_btn {
                            width: 100px;
                            height: 48px;
                            background: #F39109;
                            position: relative;
                            right: -1px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            font-weight: 500;
                            font-size: 12px;
                            color: #FFFFFF;
                            line-height: 20px;
                            cursor: pointer;

                            img {
                                width: 20px;
                                height: 20px;
                                margin-right: 4px;
                            }
                        }

                        .delete_btn {
                            width: 100px;
                            height: 48px;
                            background: #F02323;
                            border-top-right-radius: 5px;
                            border-bottom-right-radius: 5px;
                            position: relative;
                            right: -1px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            font-weight: 500;
                            font-size: 12px;
                            color: #FFFFFF;
                            line-height: 20px;
                            cursor: pointer;

                            img {
                                width: 20px;
                                height: 20px;
                                margin-right: 4px;
                            }
                        }
                    }

                }

                .dynamics_box {
                    .dynamics_item {
                        height: 68px;
                        background: #F7F8FC;
                        border-radius: 8px;
                        border: 1px solid #E8EDF2;
                        display: flex;
                        align-items: center;
                        justify-content: space-between;

                        .dynamics_item_left {
                            display: flex;
                            align-items: center;
                            margin-left: 5px;

                            .left_user {
                                width: 257px;
                                height: 58px;
                                background: #FFFFFF;
                                border: 1px solid #E8EDF2;
                                display: flex;
                                align-items: center;
                                border-radius: 5px;
                                padding-left: 15px;
                                position: relative;

                                &::before {
                                    content: "";
                                    position: absolute;
                                    width: 0;
                                    height: 0;
                                    top: 20px;
                                    right: -8px;
                                    border-top: 8px solid transparent;
                                    border-left: 8px solid #FFFFFF;
                                    border-bottom: 8px solid transparent;

                                }

                                img {
                                    width: 38px;
                                    height: 38px;
                                    margin-right: 10px;
                                }

                                .name {
                                    font-weight: 500;
                                    font-size: 16px;
                                    color: #333333;
                                    line-height: 20px;
                                }

                                .type {
                                    width: 68px;
                                    height: 26px;
                                    background: #ECF3FF;
                                    border-radius: 5px;
                                    font-weight: 500;
                                    font-size: 12px;
                                    color: #2E73F3;
                                    line-height: 26px;
                                    text-align: center;
                                    margin-left: 30px;
                                    cursor: pointer;
                                }
                            }

                            .left_order {
                                display: flex;
                                align-items: center;
                                margin-left: 30px;
                                font-weight: 500;
                                font-size: 14px;
                                line-height: 20px;

                                .order_number {
                                    color: #2E73F3;
                                }

                                .order_name {
                                    color: #333333;
                                }
                            }
                        }

                        .dynamics_item_right {
                            margin-right: 20px;
                            font-weight: 400;
                            font-size: 12px;
                            color: #666666;
                            line-height: 20px;
                        }
                    }
                }

                .follow_box {
                    background: #F7F9FC;
                    padding: 0 20px;

                    .follow_item {
                        padding: 15px 0;
                        display: flex;
                        align-items: self-start;
                        border-bottom: 1px solid #E2E6F3;
                        flex: 1;

                        &:last-child {
                            border-bottom: none;
                        }

                        .follow_left {
                            img {
                                width: 38px;
                                height: 38px;
                                margin-right: 10px;
                            }
                        }

                        .follow_right {
                            flex: 1;

                            .right_top {
                                display: flex;
                                height: 38px;
                                align-items: center;
                                justify-content: space-between;

                                .right_top_name {
                                    font-weight: 500;
                                    font-size: 16px;
                                    color: #333333;
                                    line-height: 20px;
                                    display: flex;
                                    align-items: center;

                                    .top {
                                        width: 44px;
                                        height: 26px;
                                        border-radius: 5px;
                                        border: 1px solid #78A7FF;
                                        margin-left: 10px;
                                        font-weight: 500;
                                        font-size: 12px;
                                        color: #78A7FF;
                                        line-height: 26px;
                                        text-align: center;
                                    }
                                }

                                .right_top_time {
                                    font-weight: 400;
                                    font-size: 12px;
                                    color: #666666;
                                    line-height: 20px;
                                }
                            }

                            .right_contacts {
                                display: flex;
                                align-items: center;
                                margin-top: 13px;

                                .right_contacts_title {
                                    font-weight: 400;
                                    font-size: 12px;
                                    color: #666666;
                                    line-height: 20px;
                                }

                                .right_contacts_name {
                                    font-weight: 500;
                                    font-size: 14px;
                                    color: #333333;
                                    line-height: 20px;
                                    margin-left: 15px;
                                }
                            }

                            .right_concent {
                                margin-top: 13px;
                                font-weight: 400;
                                font-size: 14px;
                                color: #333333;
                                line-height: 20px;
                            }

                            .right_btns {
                                display: flex;
                                align-items: center;
                                justify-content: flex-end;

                                .right_btns_item {
                                    display: flex;
                                    align-items: center;
                                    margin-left: 20px;
                                    font-weight: 400;
                                    font-size: 14px;
                                    color: #999999;
                                    line-height: 20px;
                                    cursor: pointer;

                                    img {
                                        width: 20px;
                                        height: 20px;
                                        margin-right: 3px;
                                    }
                                }
                            }

                            .right_reply_list {
                                background: #FFFFFF;
                                border: 1px solid rgba(197, 202, 217, 0.2);
                                border-radius: 10px;
                                margin-top: 15px;
                                padding: 0 15px;
                                position: relative;

                                &::before {
                                    content: "";
                                    position: absolute;
                                    width: 0;
                                    height: 0;
                                    top: -8px;
                                    left: 20px;
                                    border-left: 8px solid transparent;
                                    border-bottom: 8px solid #FFFFFF;
                                    border-right: 8px solid transparent;

                                }

                                .right_reply_item {
                                    padding-top: 15px;
                                    display: flex;
                                    align-items: flex-start;
                                    padding-bottom: 5px;

                                    .reply_item_left {
                                        img {
                                            width: 38px;
                                            height: 38px;
                                            margin-right: 15px;
                                        }
                                    }

                                    .reply_item_right {
                                        flex: 1;

                                        .reply_item_right_name {
                                            font-weight: 500;
                                            font-size: 16px;
                                            color: #333333;
                                            line-height: 38px;
                                        }

                                        .reply_item_right_concent {
                                            font-weight: 400;
                                            font-size: 14px;
                                            color: #333333;
                                            line-height: 16px;
                                            margin-top: 5px;
                                        }

                                        .reply_item_right_time {
                                            font-weight: 400;
                                            font-size: 12px;
                                            color: #999999;
                                            line-height: 18px;
                                            margin-top: 15px;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

        }
    }
}

.more_list {
    width: 124px;
    height: 298px;
    background: #FFFFFF;
    box-shadow: 0px 1px 15px 0px rgba(0, 0, 0, 0.25);
    border-radius: 5px;
    border: 1px solid #2E73F3;
    padding: 9px 0;

    .more_item {
        font-weight: 400;
        font-size: 14px;
        color: #666666;
        line-height: 35px;
        padding-left: 20px;
        height: 35px;
        cursor: pointer;

        &.active {
            color: #2E73F3;
        }

        &:hover {
            background: #2E73F3;
            color: #ffffff;
        }
    }
}
.mr-5 {
    margin-right: 5px;
}
.ml-10{
    margin-left: 10px;
}
.ml-5{
    margin-left: 5px;
}
.ml-100{
    margin-left: 100px;
}
</style>