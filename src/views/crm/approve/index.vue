<template>
    <div class="approve_box">
        <div class="approve_box_left" v-if="leftTabsShow">
            <left-tabs :tabsData="tabsData" :activeTab="activeTab" @update:activeTab="activeTab = $event"></left-tabs>
        </div>
        <div class="approve_box_right">
            <task v-if="activeTab === 'task'"></task>
            <approval v-if="activeTab === 'approval'"></approval>
            <approval-flow v-if="activeTab === 'flow'"></approval-flow>
            <outside v-if="activeTab === 'outside'"></outside>
        </div>
    </div>
</template>
<script>
import LeftTabs from '@/views/crm/components/LeftTabs/index.vue'
import task from './task/index.vue'
import approval from '../office/approval/index.vue'
import approvalFlow from '../office/approval/flow.vue'
import outside from '../office/outside/index.vue'

export default {
    name: 'Approve',
    components: { LeftTabs, task, approval, approvalFlow, outside },
    data() {
        return {
            tabsData: [
                { name: '任务管理', value: 'task' },
                { name: '审批管理', value: 'approval' },
                { name: '流程中心', value: 'flow' },
                { name: '外勤签到', value: 'outside' },
            ],
            activeTab: 'task',
            leftTabsShow: true,

        }
    },
    created() { },

    methods: {

    }
}
</script>
<style lang="scss" scoped>
.approve_box {
    display: flex;
    justify-content: space-between;

    .approve_box_left {
        width: 160px;
        min-height: 90vh;
        background: #F0F3F9;
        border: 1px solid #CBD6E2;
        flex-shrink: 0;
    }

    .approve_box_right {
        flex: 1;
    }
}
</style>