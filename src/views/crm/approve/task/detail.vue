<template>
    <div>
        <el-drawer title="任务详情" :visible.sync="companyViewOpen" direction="rtl" size="60%" class="company_view">
            <div class="company_box">
                <div class="company_basic">
                    <div class="basic_info">
                        <div class="text_box" style="">
                            <div class="charge_person"
                                style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 30px;">
                                <div>
                                    <span>任务状态：</span>
                                    <el-tag type="warning">进行中</el-tag>
                                </div>
                                <div
                                    style="display: flex; align-items: center; font-weight: 400; font-size: 14px; color: #666666; line-height: 20px;">
                                    <div style="margin-right: 30px; display: flex; align-items: center;">
                                        <img style="width: 20px; height: 20px; margin-right: 5px;"
                                            src="@/assets/images/handOver.png" alt="">
                                        <span>转交任务</span>
                                    </div>
                                    <div style="display: flex; align-items: center;">
                                        <img style="width: 20px; height: 20px; margin-right: 5px;"
                                            src="@/assets/images/delete1.png" alt="">
                                        <span style="color: #F02323;">删除任务</span>
                                    </div>
                                </div>
                            </div>
                            <div class="charge_person" style="display: flex; align-items: center;">
                                <div style="margin-right: 50px; display: flex; align-items: center;">
                                    <span>任务关联客户：</span>
                                    <el-button type="text">世盛金属制品有限公司</el-button>
                                </div>
                                <div style="margin-right: 50px; display: flex; align-items: center;">
                                    <span>任务关联客户：</span>
                                    <span style="font-weight: 500; font-size: 16px; color: #333333;">张三疯子</span>
                                </div>
                                <div style="display: flex; align-items: center;">
                                    <img style="width: 20px; height: 20px; margin-right: 5px;"
                                        src="@/assets/images/setting.png" alt="">
                                    <span>查看权限</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="key_nodes" style="display: flex; align-items: flex-start;">
                        <span
                            style="font-weight: 400; font-size: 14px; color: #666666; line-height: 16px; flex-shrink: 0;">任务内容：</span>
                        <div style="font-weight: 500; font-size: 16px; color: #333333; line-height: 16px;">
                            给客户回访，了解客户目前项目信息，采购需求等</div>
                    </div>
                </div>
                <div class="company_listInfo">
                    <div class="company_list_info1">
                        <div class="company_concent">
                            <div class="company_concent1_table">
                                <div class="table_row bg1">
                                    <div class="table_item">
                                        <span class="title">创建人</span>
                                        <div class="concent">
                                            <span>张三</span>
                                        </div>
                                    </div>
                                    <div class="table_item">
                                        <span class="title">创建时间</span>
                                        <div class="concent">
                                            <span>2024-10-31 08点42分</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="table_row bg2">
                                    <div class="table_item">
                                        <span class="title">执行时间</span>
                                        <div class="concent">
                                            <span>2024-10-31 08点42分</span>
                                        </div>
                                    </div>
                                    <div class="table_item">
                                        <span class="title">执行人</span>
                                        <div class="concent">
                                            <span>张三</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="table_row bg1">
                                    <div class="table_item">
                                        <span class="title">优先级</span>
                                        <div class="concent">
                                            <span class="red">高</span>
                                        </div>
                                    </div>
                                    <div class="table_item">
                                        <span class="title">抄送人</span>
                                        <div class="concent">
                                            <span>张三 李四</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="table_row bg2">
                                    <div class="table_item">
                                        <span class="title">附件</span>
                                        <div class="concent">
                                            <span style="color: #2E73F3; cursor: pointer;">某某某文件 ></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="company_list_info3">
                        <div class="contacts_select_box">
                            任务评论
                        </div>
                        <div class="follow_box">
                            <div class="follow_item">
                                <div class="follow_right">
                                    <div class="right_reply_list">
                                        <div class="right_reply_item">
                                            <div class="reply_item_left">
                                                <img src="@/assets/images/listing_icon.png" alt="">
                                            </div>
                                            <div class="reply_item_right">
                                                <div class="reply_item_right_name">销售部-王大头</div>
                                                <div class="reply_item_right_concent">跟进及时，账期缩短了很多，点个赞</div>
                                                <div class="reply_item_right_time">2024-10-01 (周一) 09:29</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </el-drawer>
    </div>
</template>

<script>
export default {
    data() {
        return {
            queryParams: {
                name
            },
            companyViewOpen: false,
            telShow: false,
            labelList: ['标签1', '标签2', '标签3'],
            labelAdd: false,
            labelAddShow: false,
            labelIpt: '',
            companyTabsList: [{
                label: '客户资料',
                value: '1',
            }, {
                label: '工商信息',
                value: '2',
            }, {
                label: '联系人',
                value: '3',
            }, {
                label: '合同',
                value: '4',
            }, {
                label: '订单',
                value: '5',
            }, {
                label: '收款',
                value: '6',
            }, {
                label: '更多',
                value: '7',
            }, {
                label: '联系跟进',
                value: '8',
            }, {
                label: '任务',
                value: '9',
            }, {
                label: '动态',
                value: '10',
            },],
            companyTabActive: '1',
            leftTabActive: 1,
            rightTabActive: 2,
            contactsSort: undefined,
            contactsLoading: false,
            contactsList: [{
                name: '王二麻子',
                phone: '18988889999',
                tel: '010-8651368',
                email: '<EMAIL>',
                contactsBtnHover: false
            }, {
                name: '王二麻子',
                phone: '18988889999',
                tel: '010-8651368',
                email: '<EMAIL>',
                contactsBtnHover: false
            }, {
                name: '王二麻子',
                phone: '18988889999',
                tel: '010-8651368',
                email: '<EMAIL>',
                contactsBtnHover: false
            }, {
                name: '王二麻子',
                phone: '18988889999',
                tel: '010-8651368',
                email: '<EMAIL>',
                contactsBtnHover: false
            }, {
                name: '王二麻子',
                phone: '18988889999',
                tel: '010-8651368',
                email: '<EMAIL>',
                contactsBtnHover: false
            }, {
                name: '王二麻子',
                phone: '18988889999',
                tel: '010-8651368',
                email: '<EMAIL>',
                contactsBtnHover: false
            },],
            checkBg: false,
            contractTabActive: 1,
            orderTabActive: 1,
            moreShow: false,
            moreActiveItem: '退款记录',
            key: 1,
        }
    },
    methods: {
        handleOpen() {
            this.companyViewOpen = true
        },
        handleAddlabel() {
            if (!this.labelIpt) {
                return
            }
            this.labelList.push(this.labelIpt)
            this.labelIpt = ''
            this.labelAddShow = false
        },
        handleCellEnter(row) {
            row.contactsBtnHover = true
        },
        handleCellLeave(row) {
            row.contactsBtnHover = false
        },
        handleAllSelects() { },
        contactsAllChecked() { },
        handleTabItem() {
            if (this.companyTabActive == '7') {
                this.moreShow = true
            } else {
                this.moreShow = false
            }
        },
        handleMoreItem(text) {
            this.companyTabsList[6].label = text
            this.companyTabActive = '7'
            this.moreActiveItem = text
            this.moreShow = false
        },
    }
}
</script>

<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';

.company_box {
    padding: 20px;

    .company_basic {
        border: 1px solid #EBEDF3;
        background: #FFFFFF;
        margin-bottom: 20px;

        .basic_info {
            background: #F4F8FF;
            padding: 20px;

            .img {
                width: 90px;
                height: 90px;
                margin-right: 20px;
            }

            .text_box {
                .name_box {
                    display: flex;
                    align-items: center;
                    margin-bottom: 15px;

                    .name {
                        font-weight: 500;
                        font-size: 24px;
                        color: #333333;
                        line-height: 24px;
                        margin-right: 30px;
                    }

                    .icon {
                        cursor: pointer;

                        img {
                            width: 30px;
                            height: 30px;
                        }

                        &:hover {
                            img {
                                width: 120px;
                                height: 30px;
                            }
                        }
                    }
                }

                .charge_person {
                    font-weight: 400;
                    font-size: 14px;
                    color: #666666;
                    line-height: 16px;
                }

                .company_basic_btn_list {
                    display: flex;
                    align-items: center;
                    margin-bottom: 15px;

                    .btn_item {
                        display: flex;
                        align-items: center;
                        font-weight: 400;
                        font-size: 14px;
                        color: #666666;
                        line-height: 20px;
                        margin-right: 30px;
                        cursor: pointer;

                        img {
                            width: 20px;
                            height: 20px;
                            margin-right: 3px;
                        }
                    }
                }

                .label_list {
                    display: flex;
                    align-items: center;

                    .label_item {
                        padding: 5px 15px;
                        font-weight: 400;
                        font-size: 14px;
                        color: #666666;
                        line-height: 20px;
                        background: #DDE3ED;
                        border-radius: 5px;
                        margin-right: 10px;
                    }

                    .add_btn {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        font-weight: 400;
                        font-size: 14px;
                        color: #666666;
                        line-height: 20px;
                        border-radius: 5px;
                        border: 1px solid #C9CBCD;
                        width: 101px;
                        height: 30px;
                        cursor: pointer;
                        margin-right: 10px;

                        img {
                            width: 20px;
                            height: 20px;
                            margin-right: 5px;
                        }

                        &:hover {
                            background: #E0EBFF;
                            border: 1px solid #2E73F3;
                            font-weight: 500;
                            font-size: 14px;
                            color: #2E73F3;
                        }
                    }

                    .add_ipt {
                        .ipt {
                            width: 128px;
                            height: 30px;
                            background: #FFFFFF;
                            box-shadow: 0px 1px 15px 0px rgba(46, 115, 243, 0.35);
                            border-radius: 5px;
                            border: 1px solid #2E73F3;
                            padding-left: 15px;

                            &::placeholder {
                                color: #2E73F3;
                            }
                        }
                    }
                }
            }
        }

        .key_nodes {
            padding: 15px 20px 20px;

            .keyNodes_title {
                font-weight: 500;
                font-size: 16px;
                color: #2E73F3;
                line-height: 16px;
                margin-bottom: 5px;
            }

            .keyNodes_concent {
                display: flex;
                align-items: center;
                justify-content: center;

                .keyNodes_item {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    font-weight: 400;
                    font-size: 14px;
                    color: #999999;
                    line-height: 16px;

                    .m5 {
                        margin: 5px 0;
                    }

                    div {
                        display: flex;
                        align-items: center;
                        justify-content: center;

                        .num {
                            color: #333333;
                            font-weight: 500;
                        }

                        img {
                            width: 20px;
                            height: 20px;
                            margin-left: 10px;
                            cursor: pointer;
                        }
                    }


                }

                .keyNodes_line {
                    width: 260px;
                    height: 1px;
                    background: #D9D9D9;
                    margin: 0 10px;
                }
            }
        }
    }

    .company_listInfo {
        .listInfo_tabs_box {
            // display: flex;
            // align-items: center;
            // justify-content: space-between;
            background: #F2F3F9;
            position: relative;

            // border-bottom: 1px solid #2E73F3;
            .listInfo_tabs_left {
                position: relative;
            }

            ::v-deep {
                .el-tabs--border-card {
                    box-shadow: none;
                    border: none;
                }

                .el-tabs__content {
                    padding: 0;
                }

                .el-tabs--border-card>.el-tabs__header {
                    background: #F2F3F9;
                    border-bottom: 1px solid #2E73F3;

                    .el-tabs__item {
                        color: #666666;

                        &.is-active {
                            color: #2E73F3;
                            border-top-color: #2E73F3;
                            border-right-color: #2E73F3;
                            border-left-color: #2E73F3;
                            margin-top: 0;
                            margin-left: 0;
                            border-top-left-radius: 5px;
                            border-top-right-radius: 5px;
                        }
                    }
                }
            }

            .listInfo_tabs_right {
                font-weight: 400;
                font-size: 14px;
                color: #666666;
                display: flex;
                align-items: center;
                cursor: pointer;
                position: absolute;
                right: 20px;
                top: 10px;

                img {
                    width: 20px;
                    height: 20px;
                    margin-right: 2px;
                }
            }
        }

        .company_list_info1 {
            margin-top: 15px;

            .company_tab_box {
                height: 46px;
                background: #F8F9FB;
                border: 1px solid #EBEDF3;
                display: flex;
                align-items: center;
                justify-content: space-between;

                .company_left {
                    display: flex;
                    padding-left: 20px;

                    .left_tab_item {
                        font-weight: 400;
                        font-size: 12px;
                        color: #666666;
                        line-height: 46px;
                        margin-right: 50px;
                        cursor: pointer;
                        height: 46px;

                        &.active {
                            border-bottom: 2px solid #2E73F3;
                            color: #2E73F3;
                            padding-bottom: 12px;
                            font-weight: 500;
                            font-size: 14px;
                        }
                    }
                }

                .company_right {
                    display: flex;
                    margin-right: 5px;
                    width: 160px;
                    height: 36px;
                    background: #E2E6F3;
                    border-radius: 5px;
                    line-height: 36px;
                    font-weight: 400;
                    font-size: 12px;
                    color: #666666;

                    .right_tab_item {
                        flex: 1;
                        text-align: center;
                        cursor: pointer;

                        &.active {
                            width: 68px;
                            height: 30px;
                            background: #FFFFFF;
                            box-shadow: 0px 1px 9px 0px rgba(0, 0, 0, 0.18);
                            border-radius: 5px;
                            margin: 3px 6px;
                            line-height: 30px;
                            font-weight: 500;
                            font-size: 12px;
                            color: #333333;
                        }
                    }
                }
            }

            .company_concent {
                margin-top: 15px;

                .company_concent1_card {
                    display: flex;
                    flex-wrap: wrap;
                    align-items: center;

                    .card_item {
                        width: 192px;
                        height: 85px;
                        background: #F8FAFF;
                        border-radius: 5px;
                        border: 1px solid #EBEDF3;
                        margin-right: 20px;
                        margin-bottom: 20px;
                        padding: 9px 15px;
                        display: flex;
                        flex-direction: column;
                        justify-content: space-between;


                        .title {
                            display: flex;
                            align-items: center;
                            justify-content: space-between;
                            font-weight: 400;
                            font-size: 12px;
                            color: #666666;
                            line-height: 20px;

                            img {
                                width: 20px;
                                height: 20px;
                            }
                        }

                        .concent {
                            font-weight: 500;
                            font-size: 14px;
                            color: #333333;
                            line-height: 20px;

                            .red {
                                color: #F02323;
                            }
                        }
                    }
                }

                .company_concent1_table {
                    border: 1px solid #EBEDF3;

                    .table_row {
                        display: flex;
                        align-items: center;
                        border-bottom: 1px solid #EBEDF3;
                        height: 40px;

                        &.bg1 {
                            background: #FFFFFF;
                        }

                        &.bg2 {
                            background: #EBEDF3;
                        }

                        &:last-child {
                            border-bottom: none;
                        }

                        .table_item {
                            flex: 1;
                            display: flex;
                            align-items: center;

                            .title {
                                width: 92px;
                                text-align: right;
                                font-weight: 400;
                                font-size: 12px;
                                color: #666666;
                                margin-right: 30px;

                                &.lfw160 {
                                    width: 160px;
                                }
                            }

                            .concent {
                                font-weight: 500;
                                font-size: 14px;
                                color: #333333;
                                display: flex;
                                align-items: center;
                                flex: 1;

                                .red {
                                    color: #F02323;
                                }

                                img {
                                    width: 20px;
                                    height: 20px;
                                    margin-left: 14px;
                                    cursor: pointer;
                                }
                            }
                        }
                    }
                }

            }
        }

        .company_list_info2 {
            margin-top: 15px;
        }

        .company_list_info3 {
            margin-top: 15px;

            .contacts_select_box {
                display: flex;
                align-items: center;
                margin-bottom: 15px;

                .contacts_add_btn {
                    margin-left: 20px;
                    width: 115px;
                    height: 32px;
                    background: #2E73F3;
                    border-radius: 5px;
                    font-weight: 500;
                    font-size: 14px;
                    color: #FFFFFF;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    cursor: pointer;

                    .img {
                        width: 20px;
                        height: 20px;
                        margin-right: 5px;
                    }
                }

                &.contract_box {
                    height: 46px;
                    background: #F8F9FB;
                    border: 1px solid #EBEDF3;

                    .contract_tabs {
                        display: flex;
                        height: 46px;
                        line-height: 46px;
                        font-weight: 400;
                        font-size: 12px;
                        color: #666666;
                        padding-left: 20px;

                        .contract_tab_item {
                            margin-right: 50px;
                            cursor: pointer;

                            &.active {
                                font-weight: 500;
                                font-size: 14px;
                                color: #2E73F3;
                                border-bottom: 2px solid #2E73F3;
                            }
                        }
                    }
                }
            }

            .contacts_table_box {
                .contacts_btn {
                    display: flex;
                    align-items: center;
                    justify-content: space-around;

                    .edit_btn {
                        width: 101px;
                        height: 30px;
                        background: #FFFFFF;
                        border-radius: 5px;
                        border: 1px solid #2E73F3;
                        font-weight: 500;
                        font-size: 12px;
                        color: #2E73F3;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        cursor: pointer;

                        img {
                            width: 20px;
                            height: 20px;
                            margin-right: 4px;
                        }

                        &.hovers {
                            background: #2E73F3;
                            border: none;
                            color: #fff;
                        }
                    }

                    .delete_btn {
                        width: 101px;
                        height: 30px;
                        background: #FFFFFF;
                        border-radius: 5px;
                        border: 1px solid #F02323;
                        font-weight: 500;
                        font-size: 12px;
                        color: #F02323;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        cursor: pointer;

                        img {
                            width: 20px;
                            height: 20px;
                            margin-right: 4px;
                        }

                        &.hovers {
                            background: #F02323;
                            border: none;
                            color: #fff;
                        }
                    }
                }

                .contacts_table_allCheck {
                    margin-top: 10px;
                    height: 48px;
                    background: #F9FBFF;
                    border-radius: 5px;
                    border: 1px solid #2E73F3;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;

                    .all_check {
                        padding-left: 14px;
                        font-weight: 400;
                        font-size: 12px;
                        color: #999999;
                        line-height: 20px;
                    }

                    .export_btn {
                        width: 100px;
                        height: 48px;
                        background: #F39109;
                        position: relative;
                        right: -1px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        font-weight: 500;
                        font-size: 12px;
                        color: #FFFFFF;
                        line-height: 20px;
                        cursor: pointer;

                        img {
                            width: 20px;
                            height: 20px;
                            margin-right: 4px;
                        }
                    }

                    .delete_btn {
                        width: 100px;
                        height: 48px;
                        background: #F02323;
                        border-top-right-radius: 5px;
                        border-bottom-right-radius: 5px;
                        position: relative;
                        right: -1px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        font-weight: 500;
                        font-size: 12px;
                        color: #FFFFFF;
                        line-height: 20px;
                        cursor: pointer;

                        img {
                            width: 20px;
                            height: 20px;
                            margin-right: 4px;
                        }
                    }
                }

            }

            .dynamics_box {
                .dynamics_item {
                    height: 68px;
                    background: #F7F8FC;
                    border-radius: 8px;
                    border: 1px solid #E8EDF2;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;

                    .dynamics_item_left {
                        display: flex;
                        align-items: center;
                        margin-left: 5px;

                        .left_user {
                            width: 257px;
                            height: 58px;
                            background: #FFFFFF;
                            border: 1px solid #E8EDF2;
                            display: flex;
                            align-items: center;
                            border-radius: 5px;
                            padding-left: 15px;
                            position: relative;

                            &::before {
                                content: "";
                                position: absolute;
                                width: 0;
                                height: 0;
                                top: 20px;
                                right: -8px;
                                border-top: 8px solid transparent;
                                border-left: 8px solid #FFFFFF;
                                border-bottom: 8px solid transparent;

                            }

                            img {
                                width: 38px;
                                height: 38px;
                                margin-right: 10px;
                            }

                            .name {
                                font-weight: 500;
                                font-size: 16px;
                                color: #333333;
                                line-height: 20px;
                            }

                            .type {
                                width: 68px;
                                height: 26px;
                                background: #ECF3FF;
                                border-radius: 5px;
                                font-weight: 500;
                                font-size: 12px;
                                color: #2E73F3;
                                line-height: 26px;
                                text-align: center;
                                margin-left: 30px;
                                cursor: pointer;
                            }
                        }

                        .left_order {
                            display: flex;
                            align-items: center;
                            margin-left: 30px;
                            font-weight: 500;
                            font-size: 14px;
                            line-height: 20px;

                            .order_number {
                                color: #2E73F3;
                            }

                            .order_name {
                                color: #333333;
                            }
                        }
                    }

                    .dynamics_item_right {
                        margin-right: 20px;
                        font-weight: 400;
                        font-size: 12px;
                        color: #666666;
                        line-height: 20px;
                    }
                }
            }

            .follow_box {
                padding: 0;

                .follow_item {
                    padding: 0;
                    display: flex;
                    align-items: self-start;
                    border-bottom: 1px solid #E2E6F3;
                    flex: 1;

                    &:last-child {
                        border-bottom: none;
                    }

                    .follow_right {
                        flex: 1;

                        .right_reply_list {
                            background: #F7F9FC;
                            border: 1px solid rgba(197, 202, 217, 0.2);
                            border-radius: 10px;
                            margin-top: 15px;
                            padding: 0 15px;
                            position: relative;

                            &::before {
                                content: "";
                                position: absolute;
                                width: 0;
                                height: 0;
                                top: -8px;
                                left: 20px;
                                border-left: 8px solid transparent;
                                border-bottom: 8px solid #F7F9FC;
                                border-right: 8px solid transparent;

                            }

                            .right_reply_item {
                                padding-top: 15px;
                                display: flex;
                                align-items: flex-start;
                                padding-bottom: 5px;

                                .reply_item_left {
                                    img {
                                        width: 38px;
                                        height: 38px;
                                        margin-right: 15px;
                                    }
                                }

                                .reply_item_right {
                                    flex: 1;

                                    .reply_item_right_name {
                                        font-weight: 500;
                                        font-size: 16px;
                                        color: #333333;
                                        line-height: 38px;
                                    }

                                    .reply_item_right_concent {
                                        font-weight: 400;
                                        font-size: 14px;
                                        color: #333333;
                                        line-height: 16px;
                                        margin-top: 5px;
                                    }

                                    .reply_item_right_time {
                                        font-weight: 400;
                                        font-size: 12px;
                                        color: #999999;
                                        line-height: 18px;
                                        margin-top: 15px;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

    }
}

.company_view {
    ::v-deep {
        .el-drawer__header {
            height: 50px;
            background: #EEF0F8;
            padding: 15px 20px;
            margin: 0;
        }
    }
}

.more_list {
    width: 124px;
    height: 298px;
    background: #FFFFFF;
    box-shadow: 0px 1px 15px 0px rgba(0, 0, 0, 0.25);
    border-radius: 5px;
    border: 1px solid #2E73F3;
    padding: 9px 0;

    .more_item {
        font-weight: 400;
        font-size: 14px;
        color: #666666;
        line-height: 35px;
        padding-left: 20px;
        height: 35px;
        cursor: pointer;

        &.active {
            color: #2E73F3;
        }

        &:hover {
            background: #2E73F3;
            color: #ffffff;
        }
    }
}
</style>