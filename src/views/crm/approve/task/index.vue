<template>
    <div class="newBox bgcf9 vh-85">
        <!-- 搜索 -->
        <div class="custom-search flex">

            <div class="custom-search-form ml15">
                <el-cascader v-model="queryParams.exeUserId" :options="approvalsOptions" :props="approvalsProps" filterable size="small"
                    :show-all-levels="false" placeholder="请选择执行人" @change="handleQuery"></el-cascader>
            </div>

            <div class="custom-search-form ml15">
                <el-select v-model="queryParams.status" placeholder="请选择任务状态" size="small" @change="handleQuery"
                    clearable>
                    <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                </el-select>
            </div>

            <div class="custom-search-form ml15">
                <el-date-picker v-model="queryParams.exeDate" size="small" type="date" placeholder="请选择下次执行日期" format="yyyy-MM-dd"
                    @change="handleQuery" value-format="yyyy-MM-dd" style="width: 200px;">
                </el-date-picker>
            </div>

            <div class=" ml15">
                <el-button icon="el-icon-search" type="primary" @click="handleQuery" size="small">搜索</el-button>
            </div>

            <div class="btn blue_all" @click="handleAddTask">
                <img class="img" src="@/assets/images/add_b.png" alt="">
                新建任务
            </div>

        </div>

        <!-- 分类 -->
        <div class="classify flex">
            <div class="classify-item" :class="{ active: item.value === queryParams.typesOfMe }"
                v-for="item in classifyOptions" :key="item.value" @click="handleCategory(item)">
                {{ item.label }}
            </div>
        </div>

        <!-- 列表 -->
        <div class="tableBox">
            <template v-if="total > 0">
                <el-table v-loading="loading" ref="table" stripe :data="list" :key="key" style="width: 100%"
                    class="custom-table" @selection-change="handleAllSelect">
                    <el-table-column align="center" type="selection" width="50"></el-table-column>
                    <el-table-column align="center" type="index" label="序号" width="50"></el-table-column>
                    <el-table-column align="center" prop="taskContent" label="任务内容"
                        show-overflow-tooltip></el-table-column>
                    <el-table-column align="center" label="客户名称" show-overflow-tooltip>
                        <template slot-scope="{ row }">
                            <el-button type="text">{{ customerList.length > 0 ? (customerList.find(item => item.id == row.linkId) ? customerList.find(item => item.id == row.linkId).name  + ' >' : '') : '' }}</el-button>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" prop="executeDate" label="执行时间" show-overflow-tooltip>
                    </el-table-column>
                    <el-table-column align="center" label="执行人" prop="exeUserId"
                        show-overflow-tooltip>
                        <template slot-scope="{ row }">
                            <span>{{ approvalsOptions.length > 0 ? findInTree(approvalsOptions, row.exeUserId).label : '' }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" label="下次执行时间" prop="nextExeTime"
                        show-overflow-tooltip></el-table-column>
                    <el-table-column align="center" prop="createTime" label="创建时间"
                        show-overflow-tooltip></el-table-column>
                    <el-table-column align="center" prop="createBy" label="创建人" show-overflow-tooltip></el-table-column>
                    <el-table-column align="center" label="优先级" show-overflow-tooltip>
                        <template slot-scope="{ row }">
                            <span v-if="row.priority === 5" style="color: #F02323;">最高</span>
                            <span v-if="row.priority === 4" style="color: #F35D09;">高</span>
                            <span v-if="row.priority === 3" style="color: #F3BC09;">中</span>
                            <span v-if="row.priority === 2" style="color: #31C776;">低</span>
                            <span v-if="row.priority === 1" style="color: #31C776;">最低</span>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" label="任务状态" show-overflow-tooltip>
                        <template slot-scope="{ row }">
                            <span v-if="row.status === 1" style="color: #F35D09;">进行中</span>
                            <span v-if="row.status === 2" style="color: #31C776;">已完成</span>
                            <span v-if="row.status === 3" style="color: #F02323;">未完成</span>
                            <span v-if="row.status === 0" style="color: #999999;">已取消</span>
                            <span v-if="row.status === 5" style="color: #F02323;">已超期</span>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" label="修改任务状态" show-overflow-tooltip width="200px">
                        <template slot-scope="{ row }">
                            <el-button type="text" @click="crmTaskDone(row)" v-if="row.status === 1">完成</el-button>
                            <el-button type="text" @click="crmTaskUndone(row)" v-if="row.status === 1">未完成</el-button>
                            <el-button type="text" @click="crmTaskCancel(row)" v-if="row.status === 1">取消</el-button>
                        </template>
                        <!-- <template slot-scope="{ row }">
                            <el-button type="text" @click="crmTaskDone(row)" >完成</el-button>
                            <el-button type="text" @click="crmTaskUndone(row)" >未完成</el-button>
                            <el-button type="text" @click="crmTaskCancel(row)" >取消</el-button>
                        </template> -->
                    </el-table-column>
                    <el-table-column align="center" label="操作" width="220px">
                        <template slot-scope="{ row }">
                            <div class="table_btns_box">
                                <el-button plain @click="handleView(row)" size="small">查看详情</el-button>
                                <el-button type="danger" @click="handleDelete(row)" size="small">删除任务</el-button>
                            </div>
                        </template>
                    </el-table-column>
                </el-table>

                <div class="custom-pagination">
                    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
                        :limit.sync="queryParams.pageSize" @pagination="getList" />
                </div>
            </template>
            <el-empty :description="!loading && !total ? '暂无数据' : '加载中…'" v-else />
        </div>

        <!-- 新建任务 -->
        <addtask ref="addTask" />

        <!-- 任务详情 -->
        <detail ref="detailView"></detail>
    </div>
</template>

<script>
import addtask from '../../customer/addTask'
import detail from './detail'
import { getTaskList, crmTaskDone, crmTaskUndone, crmTaskCancel, deletedTask } from '@/api/crm/task'
import { deptTreeSelect, listUser } from '@/api/system/user'
import { getCrmCustomerList } from '@/api/crm/customer'

export default {
    components: {
        addtask,
        detail,
        
    },
    data() {
        return {
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                exeUserId: undefined,
                exeDate: undefined,
                status: undefined,
                typesOfMe: 'cr',
            },
            key: 1,
            loading: false,
            total: 0,
            list: [],

            // 确认需求
            multipleSelection: [],

            // 分类
            classifyOptions: [
                // { value: undefined, label: '全部任务' },
                { value: 'cr', label: '我创建的' },
                { value: 'fz', label: '我负责的' },
                { value: 'cc', label: '抄送给我的' },
                // { value: '4', label: '我分配的' },
            ],
            options: [{
                value: '1',
                label: '进行中'
            }, {
                value: '2',
                label: '已完成'
            }, {
                value: '3',
                label: '未完成'
            }, {
                value: '4',
                label: '已取消'
            }, {
                value: '5',
                label: '已超期'
            }],
            approvalsOptions: [],
            approvalsProps: {
                expandTrigger: 'hover',
                emitPath: false
            },
            customerList: [],
            salespersonOptions: [],
        }
    },
    created() {
        this.getList()
        this.getApprovalsOptions()
        this.getCustomerList()
    }, 
    methods: {
        // 查询部门、查询用户构造树
        async getApprovalsOptions() {
            const dept = await deptTreeSelect()
            const user = await listUser()
            this.salespersonOptions = user.rows || []
            const children = dept.data[0].children || []
            const deptData = [...children, ...[{ id: -1, label: '其他', value: -1 }]] || []
            const userData = user.rows || []
            const getChildren = data => {
                data.forEach(item => {
                    item.value = item.id
                    if (item.children) {
                        getChildren(item.children)
                    } else {
                        item.children = []
                    }
                })
            }
            getChildren(deptData)
            const addChildren = data => {
                data.forEach(item => {
                    userData.forEach(user => {
                        if (item.id === user.deptId && item.children) {
                            item.children.push({
                                id: user.userId,
                                label: user.realName || user.nickName,
                                value: user.userId,
                                disabled: user.status == '1',
                                userName: user.userName
                            })
                        }
                        if (item.id === -1 && (!user.deptId || user.deptId === dept.data[0].id) && item.children) {
                            item.children.push({
                                id: user.userId,
                                label: user.realName || user.nickName,
                                value: user.userId,
                                disabled: user.status == '1',
                                userName: user.userName
                            })
                        }
                    })
                    if (item.children && item.children.length) {
                        addChildren(item.children)
                    }
                })
            }
            addChildren(deptData)
            this.approvalsOptions = deptData
        },
        // 从树结构内找到相同id
        findInTree(tree, id) {
            for (let i = 0; i < tree.length; i++) {
                if (tree[i].id === id) {
                    return tree[i]
                } else if (tree[i].children && tree[i].children.length) {
                    const res = this.findInTree(tree[i].children, id)
                    if (res) return res
                }
            }
        },
        
        getCustomerList() {
            this.loading = true;
            getCrmCustomerList({
                pageNum: 1,
                pageSize: 1000,
            }).then(res => {
                const { code, msg, rows, total } = res
                if (code === 200) {
                    this.customerList = rows
                    this.loading = false
                } else {
                    this.$message.error(msg)
                    this.loading = false
                }
            })
        },
        // 切换分类
        handleCategory(item) {
            this.queryParams.typesOfMe = item.value
            this.queryParams.pageNum = 1
            this.getList()
        },
        // 查询
        handleQuery() {
            this.queryParams.pageNum = 1
            this.getList()
        },
        getList() {
            this.loading = true
            getTaskList(this.queryParams).then(res => {
                const { rows, total, code } = res
                if (code !== 200) {
                    this.$message.error(res.msg || '获取数据失败')
                    return

                }
                this.list = rows
                this.total = total
                this.loading = false
            }).catch(() => {
                this.loading = false
            })
        },
        // 全部列表批量选择
        handleAllSelect(val) {
            this.multipleSelection = val;
        },
        // 查看详情
        handleView() {
            this.$refs.detailView.handleOpen()
        },

        handleAddTask() {
            this.$refs.addTask.handleAdd()
        },
        crmTaskDone(row) {
            crmTaskDone({
                id: row.id,
            }).then(res => {
                if (res.code === 200) {
                    this.$message.success('操作成功')
                    this.getList()
                } else {
                    this.$message.error(res.msg || '操作失败')
                }
            })
        },
        crmTaskUndone(row) {
            crmTaskUndone({
                id: row.id,
            }).then(res => {
                if (res.code === 200) {
                    this.$message.success('操作成功')
                    this.getList()
                } else {
                    this.$message.error(res.msg || '操作失败')
                }
            })
        },
        crmTaskCancel(row) {
            crmTaskCancel({
                id: row.id,
            }).then(res => {
                if (res.code === 200) {
                    this.$message.success('操作成功')
                    this.getList()
                } else {
                    this.$message.error(res.msg || '操作失败')
                }
            })
        },
        handleDelete(row) {
            this.$confirm('是否删除该任务？', '提示', {
                type: 'warning'
            }).then(() => {
                deletedTask({
                    id: row.id,
                }).then(res => {
                    if (res.code === 200) {
                        this.$message.success('操作成功')
                        this.getList()
                    } else {
                        this.$message.error(res.msg || '操作失败')
                    }
                })
            }).catch(() => { })
        },
    }
}
</script>

<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';

.custom-search {
    padding-top: 10px;
    padding-bottom: 10px;
    background-color: transparent;
}

.select_box {
    margin-left: 15px;
    position: relative;

    .ipt {
        ::v-deep {
            .el-input__inner {
                cursor: pointer;
                height: 32px;
            }
        }
    }

    .user_list_box {
        position: absolute;
        left: 0;
        top: 35px;
        z-index: 999;
        width: 324px;
        height: 590px;
        background: #FFFFFF;
        box-shadow: 0px 1px 15px 0px rgba(0, 0, 0, 0.25);
        border-radius: 5px;
        border: 1px solid #2E73F3;

        .tab_box {
            background: #f5f5f5;
            padding: 5px 5px 0;
            height: 52px;
            display: flex;
            align-items: center;
            border-bottom: 1px solid #CBD6E2;

            .tabs_item {
                flex: 1;
                text-align: center;
                height: 45px;
                line-height: 45px;
                cursor: pointer;
                font-weight: 400;
                font-size: 12px;
                color: #666666;

                &.avtive {
                    background: #ffffff;
                    border: 1px solid #CBD6E2;
                    border-bottom: 1px solid #ffffff;
                    border-radius: 5px;
                    position: relative;
                    top: 2px;
                    z-index: 1;
                    font-weight: 500;
                    font-size: 16px;
                    color: #333333;
                }
            }
        }

        .tab_list {
            border-bottom: 1px solid #E1E2E3;
            padding: 12px 0 0px;
            display: flex;
            align-items: center;

            .tab_list_item {
                padding: 0 15px;
                font-weight: 400;
                font-size: 12px;
                color: #666666;
                cursor: pointer;
                display: flex;
                flex-direction: column;
                align-items: center;

                .bbLine {
                    width: 50px;
                    height: 2px;
                    background: #2E73F3;
                }

                &.active {
                    font-weight: 500;
                    font-size: 14px;
                    color: #2E73F3;
                }
            }
        }

        .user_list {
            padding: 0 10px;

            .user_list_search {
                padding-top: 10px;
                padding-bottom: 5px;

                .ipt {
                    ::v-deep {
                        .el-input__inner {
                            height: 32px;
                        }

                        .btn {
                            margin: 3px 0;
                            width: 66px;
                            height: 26px;
                            border-radius: 5px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            font-weight: 500;
                            font-size: 12px;
                            margin-left: 15px;
                            cursor: pointer;

                            .img {
                                width: 20px;
                                height: 20px;
                                margin-right: 5px;
                            }

                            &.blue_ {
                                color: #2E73F3;
                                background: #E1EBFF;
                                border-radius: 5px;
                            }
                        }
                    }
                }
            }

            .user_list_concent {
                height: 400px;
                overflow-y: scroll;

                .user_list_item {
                    padding: 6px 10px;
                    border-bottom: 1px solid #E1E2E3;
                    display: flex;
                    align-items: center;

                    .img {
                        width: 38px;
                        height: 38px;
                        margin-right: 10px;
                    }

                    .text {
                        flex: 1;

                        .name {
                            font-weight: 500;
                            font-size: 14px;
                            color: #333333;
                            line-height: 20px;
                        }

                        .dept {
                            display: flex;
                            align-items: center;
                            justify-content: space-between;
                            font-weight: 400;
                            font-size: 12px;
                            color: #666666;
                        }
                    }
                }
            }
        }

        .user_btn {
            background: #F5F5F5;
            display: flex;
            align-items: center;
            height: 50px;

            div {
                flex: 1;
                display: flex;
                align-items: center;
                justify-content: center;

                &.refresh {
                    font-weight: 500;
                    font-size: 12px;
                    color: #999999;
                    line-height: 20px;

                    .img {
                        width: 20px;
                        height: 20px;
                        margin-right: 4px;
                    }
                }

                &.setting {
                    font-weight: 500;
                    font-size: 12px;
                    color: #2E73F3;
                    line-height: 20px;

                    .img {
                        width: 18px;
                        height: 18px;
                        margin-right: 4px;
                    }
                }
            }
        }
    }
}

.btn {
    width: 124px;
    height: 32px;
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    font-size: 12px;
    margin-left: 15px;
    cursor: pointer;

    .img {
        width: 20px;
        height: 20px;
        margin-right: 5px;
    }

    &.blue_all {
        color: #FFFFFF;
        background: #2E73F3;
        border: 1px solid #2E73F3;
    }

    &.blue {
        color: #2E73F3;
        background: #FFFFFF;
        border: 1px solid #2E73F3;
    }

    &.green {
        color: #1DC86B;
        background: #FFFFFF;
        border: 1px solid #1DC86B;
    }
}


.btn_list {
    width: 124px;
    background: #FFFFFF;
    box-shadow: 0px 1px 15px 0px rgba(0, 0, 0, 0.25);
    border-radius: 5px;
    font-weight: 400;
    font-size: 14px;
    color: #666666;

    .btn_item {
        height: 36px;
        line-height: 36px;
        cursor: pointer;
    }

    &.blue {
        border: 1px solid #2E73F3;

        .btn_item {
            padding-left: 20px;

            &:hover {
                background: #2E73F3;
                color: #ffffff;
            }
        }
    }

    &.green {
        border: 1px solid #1DC86B;

        .btn_item {
            padding-left: 14px;

            &:hover {
                background: #1DC86B;
                color: #ffffff;
            }
        }
    }
}




.tableBox {
    margin: 15px 10px 20px;
    padding: 0 10px 20px;
    background-color: $white;
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
}

.table_btns_box {
    display: flex;
    align-items: center;
    justify-content: space-around;
}

.more_btns_box {
    width: 246px;
    height: 600px;
    background: #FFFFFF;
    box-shadow: 0px 1px 15px 0px rgba(0, 0, 0, 0.25);
    border-radius: 5px;
    border: 1px solid #2E73F3;
    display: flex;
    align-items: flex-start;

    .more_btns_left {
        flex: 1;
        padding: 10px 0;
        height: 100%;

        .btn_item {
            font-weight: 400;
            font-size: 14px;
            height: 36px;
            cursor: pointer;
            color: #666666;
            padding-left: 20px;
            line-height: 36px;

            &:hover {
                background: #2E73F3;
                color: #FFFFFF;
            }

            &.active {
                background: #2E73F3;
                color: #FFFFFF;
            }
        }
    }

    .more_btns_right {
        flex: 1;
        height: 100%;
        padding: 10px 0;
        background: #F0F3F9;

        .btn_item {
            font-weight: 400;
            font-size: 14px;
            height: 36px;
            cursor: pointer;
            color: #666666;
            padding-left: 20px;
            line-height: 36px;

            &:hover {
                background: #8B9DBE;
                color: #FFFFFF;
            }
        }
    }
}

.custom-dialog {
    ::v-deep .addBox {
        padding: 10px 20px;

        .add-search {
            flex-direction: column;
            align-items: center;
            margin-bottom: 16px;

            &-tab {
                width: 780px;
                align-items: flex-end;

                .tab-item {
                    font-size: 12px;
                    line-height: 26px;
                    background-color: #d9d9d9;
                    color: #96a3b1;
                    cursor: pointer;
                    padding: 0 12px;

                    &:nth-child(1) {
                        border-top-left-radius: 5px;
                    }

                    &:nth-child(2) {
                        border-top-right-radius: 5px;
                    }

                    &.active {
                        background-color: $blue;
                        color: $white;
                        line-height: 30px;
                        font-weight: 500;
                        border-top-left-radius: 5px;
                        border-top-right-radius: 5px;
                    }
                }
            }

            &-input {
                width: 820px;
                justify-content: space-between;
                position: relative;

                input[type='text'] {
                    width: 650px;
                    height: 46px;
                    line-height: 46px;
                    border: 1px solid #cbd7e2;
                    border-radius: 5px;
                    outline: none;
                    padding-left: 20px;
                    padding-right: 130px;
                    position: relative;

                    &::-webkit-input-placeholder {
                        color: #999999;
                    }
                }

                button.search {
                    height: 40px;
                    line-height: 40px;
                    font-size: 16px;
                    color: $white;
                    background-color: $blue;
                    border: 0;
                    padding: 0 29px;
                    border-radius: 5px;
                    position: absolute;
                    top: 3px;
                    right: 173px;
                    cursor: pointer;

                    &:hover {
                        opacity: 0.8;
                    }
                }

                button.plus {
                    height: 46px;
                    line-height: 46px;
                    font-size: 14px;
                    font-weight: 500;
                    color: $white;
                    background-color: $blue;
                    border: 0;
                    padding: 0 36px;
                    border-radius: 5px;
                    cursor: pointer;

                    &:hover {
                        opacity: 0.8;
                    }
                }
            }
        }
    }

    .custom-table ::v-deep {
        .el-form-item {
            margin-top: 10px !important;
            margin-bottom: 10px !important;

            .el-form-item__error {
                top: 95%;
                padding-top: 0;
            }
        }
    }

    .confirm-total {
        margin-top: 10px;
        background-color: #ecf3ff;
        border: 1px solid $blue;
        border-radius: 5px;
        width: 100%;
        height: 48px;
        padding: 0 20px;
        font-size: 14px;
        color: $info;

        span {
            margin-right: 50px;
        }

        b {
            font-size: 18px;
            font-weight: 500;
            color: $blue;
        }
    }
}

.rules {
    padding: 15px 0;
    border-bottom: 1px solid #e2e6f3;

    &-item {
        display: flex;
        align-items: center;
        padding: 15px 0;

        &-title {
            width: 100px;
            font-size: 14px;
            color: #666666;
        }

        &-content {
            display: flex;
            align-items: center;
        }

        &-select {
            display: inline-flex;
            align-items: center;
            height: 46px;
            border: 1px solid #d7dadc;
            border-radius: 5px;
            padding: 0 15px;
            margin-right: 10px;

            ::v-deep {
                .el-input__inner {
                    border: 0;
                    width: 60px;
                }
            }
        }
    }
}

::v-deep {
    .table-tip {
        display: flex;
        align-items: center;
        padding-bottom: 10px;
        font-size: 14px;
        color: red;

        span {
            margin-left: 5px;
        }
    }

    .hideHeaderCheckBox {
        .el-checkbox {
            display: none;
        }

        .cell:before {
            content: '';
        }
    }

    .mark {
        height: 100vh;
        left: 0;
        position: fixed;
        top: 0;
        width: 100vw;
        z-index: 99999;
    }

    .collectAll {
        bottom: 100px;
        left: calc(50% - 200px);
        position: fixed;
        z-index: 50;
        transform: translateX(calc(-50% + 200px));
        -webkit-user-select: none;
        -ms-user-select: none;
        user-select: none;
        transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;

        &-box {
            background: rgba(52, 54, 57, 1);
            border-radius: 12px;
            box-shadow: 0 -2px 8px rgba(38, 38, 38, 0.05), 0 10px 16px rgba(38, 38, 38, 0.08);
            height: 60px;
            padding: 5px 8px;
            position: relative;
            display: flex;
            align-items: center;
            color: #ffffff;
            font-size: 14px;
        }

        &-item {
            display: flex;
            align-items: center;
            padding: 15px 12px;
            cursor: pointer;

            &:hover {
                background: #555B73;
                border-radius: 10px;
            }

            .img {
                width: 20px;
                height: 20px;
                margin: 0 10px 0 3px;
            }
        }

        .batch_box {
            position: absolute;
            top: -323px;
            left: -12px;
            width: 180px;
            height: 308px;
            background: #292F48;
            box-shadow: 0px 1px 15px 0px rgba(0, 0, 0, 0.25);
            border-radius: 10px;
            padding: 15px 10px;
            padding-right: 0;
            display: flex;

            &::before {
                position: absolute;
                content: "";
                width: 0;
                height: 0;
                bottom: -5px;
                left: 90px;
                border-top: solid 5px #292F48;
                border-right: solid 5px transparent;
                border-left: solid 5px transparent;
            }

            .batch_left_list {
                // flex: 1;

                .batch_item {
                    font-weight: 400;
                    font-size: 12px;
                    color: #FFFFFF;
                    line-height: 14px;
                    cursor: pointer;
                    padding-right: 10px;

                    &.text {
                        padding: 10px;
                        width: 160px;
                        margin-right: 10px;

                        &:hover {
                            background: #FFFFFF;
                            box-shadow: 0px 1px 15px 0px rgba(0, 0, 0, 0.25);
                            border-radius: 5px;
                            font-weight: 500;
                            color: #292F48;
                        }
                    }

                    .item_flex {
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        padding: 10px;

                        &:hover {
                            background: #FFFFFF;
                            box-shadow: 0px 1px 15px 0px rgba(0, 0, 0, 0.25);
                            border-radius: 5px;
                            font-weight: 500;
                            color: #292F48;
                        }
                    }

                    &.active {
                        position: relative;
                        background: #FFFFFF;
                        box-shadow: none;
                        border-top-left-radius: 5px;
                        border-bottom-left-radius: 5px;
                        font-weight: 500;
                        color: #292F48;

                        .item_flex {
                            &:hover {
                                box-shadow: none;
                                border-radius: none;
                            }
                        }
                    }
                }
            }

            &.active {
                width: 360px;
                padding-right: 10px;

                .batch_right_list {
                    flex: 1;
                    height: 283px;
                    background: #ffffff;
                    border-radius: 10px;
                    box-shadow: 0px 1px 15px 0px rgba(0, 0, 0, 0.25);
                    padding: 10px;

                    .right_item {
                        font-weight: 400;
                        font-size: 12px;
                        color: #292F48;
                        line-height: 14px;
                        padding: 10px 0;
                        text-align: center;
                        cursor: pointer;

                        &:hover {
                            background: #2E73F3;
                            border-radius: 5px;
                            color: #FFFFFF;
                            font-weight: 500;
                        }
                    }
                }
            }
        }

        .passOn_box {
            position: absolute;
            top: -255px;
            left: -39px;
            width: 180px;
            height: 239px;
            background: #292F48;
            box-shadow: 0px 1px 15px 0px rgba(0, 0, 0, 0.25);
            border-radius: 10px;
            padding: 15px 10px;

            &::before {
                position: absolute;
                content: "";
                width: 0;
                height: 0;
                bottom: -5px;
                left: 90px;
                border-top: solid 5px #292F48;
                border-right: solid 5px transparent;
                border-left: solid 5px transparent;
            }

            .passOn_list {
                .passOn_title {
                    font-weight: 400;
                    font-size: 12px;
                    color: #ADB1C0;
                    line-height: 14px;
                    padding: 10px 0;
                    text-align: center;
                }

                .passOn_item {
                    font-weight: 400;
                    font-size: 12px;
                    color: #FFFFFF;
                    line-height: 14px;
                    padding: 10px 0;
                    text-align: center;
                    cursor: pointer;

                    &:hover {
                        background: #FFFFFF;
                        box-shadow: 0px 1px 15px 0px rgba(0, 0, 0, 0.25);
                        border-radius: 5px;
                        font-weight: 500;
                        color: #292F48;
                    }
                }
            }
        }
    }
}

.ml15 {
    margin-left: 15px;
}
</style>

<style>
.btn_box.el-popper[x-placement^=bottom],
.btn_box.el-popper[x-placement^=top] {
    min-width: 0;
    padding: 0 !important;
}
</style>