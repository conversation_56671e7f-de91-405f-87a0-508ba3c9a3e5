<template>
    <div class="tabs_box" :class="{ 'activeTheme': theme === 'dark' }">
        <div class="tabs_item" :class="{ 'active': item.value === activeTab }" v-for="(item, index) in tabsData"
            :key="index" @click="handleTabClick(item.value)">
            <img :src="require(`@/assets/images/crm/${item.value}_g.png`)" alt="" v-if="item.value !== activeTab">
            <img :src="require(`@/assets/images/crm/${item.value}_b.png`)" alt="" v-if="item.value === activeTab">
            <span>{{ item.name }}</span>
        </div>
    </div>
</template>

<script>
export default {
    name: 'LeftTabs',
    props: {
        tabsData: {
            type: Array,
            default: () => []
        },
        activeTab: {
            type: String,
            default: 'dataScreen'
        },
        theme: {
            type: String,
            default: 'light'
        }
    },
    
    data() {
        return {

        }
    },
    watch: {
        theme(newVal) {
            console.log('Theme changed to:', newVal);
        }
    },
    methods: {
        handleTabClick(value) {
            console.log('Tab clicked:', value);
            if (value !== 'dataScreen') {
                this.$emit('update:theme', 'light')
            }
            this.$emit('update:activeTab', value);
        }
    }
}
</script>

<style lang="scss" scoped>
.tabs_box {
    padding: 20px 20px 0;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    /* Added alignment for tabs */

    .tabs_item {
        display: flex;
        width: 100%;
        align-items: center;  
        padding: 10px;
        font-weight: 400;
        font-size: 12px;
        color: #9FA3B2;
        margin-bottom: 10px;
        background-color: transparent;
        /* Light background for tabs */
        border-radius: 5px;
        /* Rounded corners for tabs */
        cursor: pointer;
        transition: background-color 0.3s ease;
        /* Smooth transition for hover effect */

        &:hover {
            background-color: #DAE7FF;
            /* Darker shade on hover */
        }
        img {
            width: 20px;
            height: 20px;
            margin-right: 5px;
        }
    }

    .tabs_item.active {
        background-color: #DAE7FF;
        /* Highlighted background for active tab */
        font-weight: 500;
        font-size: 13px;
        color: #2E73F3;
    }

    .tabs_item.active:hover {
        background-color: #DAE7FF;
        /* Darker shade on hover for active tab */
    }
}
</style>