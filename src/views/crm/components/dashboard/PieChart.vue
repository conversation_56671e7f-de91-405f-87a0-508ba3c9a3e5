<template>
    <div :style="{ height: height, width: width }" style="border-radius: 0 0 5px 5px; overflow: hidden" />
</template>

<script>
import * as echarts from 'echarts'

require('echarts/theme/macarons') // echarts theme
import resize from './mixins/resize'

export default {
    name: '<PERSON><PERSON><PERSON>',
    mixins: [resize],
    props: {
        width: {
            type: String,
            default: '100%'
        },
        height: {
            type: String,
            default: '350px'
        },
        autoResize: {
            type: Boolean,
            default: true
        },
        chartData: {
            type: [Object, Array],
            required: true
        },
        showToolbox: {
            type: Boolean,
            default: false
        },
        seriesType: {
            type: String,
            default: 'pie'
        },
        theme: {
            type: String,
            default: 'light'
        },
        radius: {
            type: Array,
            default: ['40%', '70%']
        },
        borderRadius: {
            type: [Number, String],
            default: 10
        },
        showLegend: {
            type: Boolean,
            default: false
        },
        showEmphasis: {
            type: Boolean,
            default: true
        },
    },
    data() {
        return {
            chart: null
        }
    },
    watch: {
        chartData: {
            deep: true,
            handler(val) {
                this.setOptions(val)
            }
        },
        theme(newVal) {
            this.$set(this.chartData, 'theme', newVal);
        }
    },
    mounted() {
        this.$nextTick(() => {
            this.initChart()
        })
    },
    beforeDestroy() {
        if (!this.chart) {
            return
        }
        this.chart.dispose()
        this.chart = null
    },
    methods: {
        initChart() {
            this.chart = echarts.init(this.$el, 'macarons')
            this.setOptions(this.chartData)
        },
        setOptions(data) {
            const seriesType = this.seriesType || 'pie' // 系列类型
            const showToolbox = this.showToolbox || false // 是否显示工具栏
            const theme = this.theme || 'light' // 主题
            this.chart.setOption({
                backgroundColor: theme === 'light' ? '#fff' : '#20184C',
                tooltip: {
                    show: showToolbox,
                },
                legend: {
                    show: this.showLegend,
                    top: 'center',
                    right: '20',
                    orient: 'vertical',
                    itemWidth: 10,
                },
                series: [
                    {
                        name: '新增客户',
                        type: seriesType,
                        radius: this.radius,
                        avoidLabelOverlap: false,
                        padAngle: 20,
                        itemStyle: {
                            borderRadius: this.borderRadius
                        },
                        label: {
                            show: false,
                            position: 'outside',
                            formatter: '{b}: {c} ({d}%)',
                        },
                        emphasis: {
                            label: {
                                show: this.showEmphasis,
                                fontSize: 20,
                                fontWeight: 'bold'
                            }
                        },
                        labelLine: {
                            show: false
                        },
                        data: data.seriesData || [],
                    }
                ]
            })
        }
    }
}
</script>