<template>
  <div :style="{ height: height, width: width }" style="border-radius: 0 0 5px 5px; overflow: hidden" />
</template>

<script>
import * as echarts from 'echarts'

require('echarts/theme/macarons') // echarts theme
import resize from './mixins/resize'

export default {
  mixins: [resize],
  props: {
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '350px'
    },
    autoResize: {
      type: Boolean,
      default: true
    },
    chartData: {
      type: [Object, Array],
      required: true
    },
    showToolbox: {
      type: Boolean,
      default: false
    },
    seriesType: {
      type: String,
      default: 'line'
    },
    theme: {
      type: String,
      default: 'light'
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        this.setOptions(val)
      }
    },
    theme(newVal) {
      this.$set(this.chartData, 'theme', newVal);
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el, 'macarons')
      this.setOptions(this.chartData)
    },
    setOptions(data) {
      const xAxisData = data.xAxis // x轴数据
      const yAxisData = data.yAxis // y轴数据
      const tooltipTitle = data.tooltipTitle || '订单数量' // 提示框标题
      const seriesType = this.seriesType || 'line' // 系列类型
      const showToolbox = this.showToolbox || false // 是否显示工具栏
      const smooth = data.smooth || false // 是否平滑曲线
      const theme = this.theme || 'light' // 主题
      const seriesData = data.seriesData || [
        {
          data: yAxisData || [15, 20, 25, 30, 12, 25, 32, 38],
          type: seriesType,
          smooth: smooth,
          symbol: 'circle',
          symbolSize: 10,
          showSymbol: false,
          emphasis: {
            scale: true,
            itemStyle: {
              color: '#2e73f3',
              borderColor: '#fff',
              borderWidth: 3
            }
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'rgba(46,115,243,1)'
              },
              {
                offset: 1,
                color: 'rgba(46,115,243,0)'
              }
            ]),
          },
          itemStyle: {
            color: '#2e73f3'
          },
          lineStyle: {
            width: 3,
            color: '#2e73f3'
          }
        },
      ] // 系列数据
      this.chart.setOption({
        backgroundColor: theme === 'light' ? '#fff' : '#20184C',
        toolbox: {
          show: showToolbox,
          feature: {
            magicType: {
              type: ['line', 'bar'],
              title: {
                line: '切换为折线图',
                bar: '切换为柱状图'
              }
            }
          },
          top: 0,
          right: '3%',
          itemSize: 15,
          itemStyle: {
            color: '#666'
          }
        },
        xAxis: {
          type: 'category',
          boundaryGap: seriesType === 'bar',
          data: xAxisData || ['01-01', '01-15', '02-01', '02-15', '03-01', '03-15', '04-01', '04-15'],
          axisLabel: {
            color: '#666'
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          }
        },
        grid: {
          left: '20',
          right: '20',
          bottom: '20',
          top: showToolbox ? '40' : '10',
          containLabel: true
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'none'
          },
          backgroundColor: 'rgba(0,0,0,0.5)',
          borderWidth: 0,
          textStyle: {
            color: '#fff',
            fontSize: 12
          },
          padding: [5, 20],
          formatter: function (params) {
            return `${tooltipTitle}<br/><span style="font-size: 14px">${params[0].value}</span><br/>${params[0].name}`
          }
        },
        yAxis: {
          type: 'value',
          splitLine: {
            show: true,
            lineStyle: {
              type: 'dashed',
              color: theme === 'light' ? '#E7E9EA' : '#4F4875'
            }
          },
          splitArea: {
            show: false
          },
          axisLabel: {
            color: '#666'
          }
        },
        series: seriesData
      })
    }
  }
}
</script>
