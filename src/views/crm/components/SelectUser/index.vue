<template>
  <div>
    <el-tabs v-model="activeName" @tab-click="handleClick" class="body-hidden" v-if="hasTab">
      <el-tab-pane v-for="item in tabOptions" :key="item.name" :label="item.label" :name="item.name"></el-tab-pane>
    </el-tabs>
    <el-container class="info" v-if="activeName == 'person'">
      <el-aside class="info-tree">
        <el-tree ref="personTree" :data="treeData" highlight-current node-key="id" accordion key="personTree">
          <span class="custom-tree-node" slot-scope="{ node, data }">
            <i v-if="node.level == 1" class="father" />
            <i v-else-if="node.level == 2" class="son" />
            <i v-else-if="node.level == 3" class="gson" />
            {{ data.label }}
            <i class="tree-icon el-icon-caret-bottom" v-if="data.children && data.children.length > 0" />
          </span>
        </el-tree>
      </el-aside>
      <el-container>
        <div style="display: flex; width: 100%">
          <div class="info-container">
            <el-header class="flex align-center" style="background-color: #f9f9f9">
              <el-checkbox :indeterminate="isIndeterminate" v-model="checked" @change="handleCheckAll">全选</el-checkbox>
              <div class="flex flex-justify-center" style="flex: 1">
                <el-input v-model="search" size="small" placeholder="请输入人员名称" style="width: 260px">
                  <template slot="append">
                    <el-button type="text" icon="el-icon-search" size="small"
                      style="background-color: #e1ebff; color: #2e73f3; padding-left: 10px; padding-right: 10px">搜索</el-button>
                  </template>
                </el-input>
              </div>
            </el-header>
            <el-main class="info-main">
              <el-table ref="table" :data="tableData" style="width: 100%" @selection-change="handleSelectionChange"
                @select-all="handleSelectAll" max-height="458" :show-header="false"
                class="custom-table-cell5 info-table">
                <el-table-column type="selection" width="55" align="left"></el-table-column>
                <el-table-column prop="avatar" label="头像" width="60">
                  <template slot-scope="scope">
                    <el-avatar :size="40" :src="scope.row.avatar" />
                  </template>
                </el-table-column>
                <el-table-column prop="name" label="姓名">
                  <template slot-scope="scope">
                    <div class="flex flex-column">
                      <div class="name">{{ scope.row.realName ? scope.row.realName : scope.row.nickName }}</div>
                      <div class="flex align-center flex-justify-between">
                        <span class="department">{{ scope.row.dept && scope.row.dept.deptName }}</span>
                        <!-- <span class="position">{{ scope.row.position }}</span> -->
                      </div>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
            </el-main>
          </div>
          <div class="info-select" v-show="selectedUsers.length > 0">
            <div class="info-select-box">
              <div class="info-select-item" v-for="item in selectedUsers" :key="item.id">
                <span class="title">{{ item.realName ? item.realName : item.nickName }}</span>
                <i class="icon el-icon-error" @click="handleDelete(item)" />
              </div>
            </div>
          </div>
        </div>
      </el-container>
    </el-container>
    <div class="flex flex-column" v-if="activeName == 'department'">
      <div class="info-checkall">
        <el-checkbox :indeterminate="indeterminateDepartment" v-model="checkedDepartment"
          @change="handleCheckAllDepartment">全选</el-checkbox>
      </div>
      <el-container class="info">
        <el-aside class="info-tree">
          <el-tree ref="departmentTree" :data="treeData" show-checkbox highlight-current node-key="id" accordion
            key="departmentTree" class="info-tree-checkbox" @check-change="handleCheckDepartment">
            <span class="custom-tree-node" slot-scope="{ node, data }">
              {{ data.label }}
              <i class="tree-icon el-icon-caret-bottom" v-if="data.children && data.children.length > 0" />
            </span>
          </el-tree>
        </el-aside>
        <el-container>
          <el-main class="info-main">
            <div class="info-select info-select-department" style="width: 100%">
              <div class="info-select-box">
                <div class="info-select-item" v-for="item in selectedDepartments" :key="item.id">
                  <span class="title">{{ item.label }}</span>
                  <i class="icon el-icon-error" @click="handleDeleteDepartment(item)" />
                </div>
              </div>
            </div>
          </el-main>
        </el-container>
      </el-container>
    </div>
  </div>
</template>
<script>
import { deptTreeSelect, listUser } from '@/api/system/user'
export default {
  name: 'SelectUser',
  props: {
    hasTab: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      treeData: [],
      tableData: [],
      checked: false,
      isIndeterminate: false,
      search: '',
      activeName: 'person',
      tabOptions: [
        { name: 'person', label: '指定人员' },
        { name: 'department', label: '指定部门' }
      ],
      selectedUsers: [], // 选中的用户
      selectedDepartments: [], // 选中的部门
      indeterminateDepartment: false,
      checkedDepartment: false,
      queryParams: {
        pageNum: 1,
        pageSize: 1000,
        userName: undefined,
        deptId: undefined
      },
    }
  },
  created() {
    this.getDeptOptions()
    this.getList()
  },
  methods: {
    // 获取部门列表
    async getDeptOptions() {
      const { data } = await deptTreeSelect()
      this.treeData = data[0] && data[0].children
      // console.log(data, 'aaaaaa')
    },
    /** 查询用户列表 */
    getList() {
      this.loading = true
      listUser(this.queryParams).then(response => {
        this.tableData = response.rows
        this.loading = false
      })
    },
    // 切换tab
    handleClick(tab, event) {
      this.activeName = tab.name
    },
    // 表格内的全选
    handleSelectAll(selection) {
      this.checked = !!selection.length
    },
    // 表格内的选中
    handleSelectionChange(selection) {
      this.selectedUsers = selection
      this.checked = selection.length > 0 && selection.length == this.tableData.length
      this.isIndeterminate = selection.length > 0 && selection.length < this.tableData.length
    },
    // 全选
    handleCheckAll() {
      if (!this.$refs.table) return
      if (this.checked) {
        this.tableData.forEach(row => {
          this.$refs.table.toggleRowSelection(row, true)
        })
      } else this.$refs.table.clearSelection()
    },
    // 删除选中用户
    handleDelete(item) {
      this.selectedUsers = this.selectedUsers.filter(user => user.id !== item.id)
      if (this.$refs.table) this.$refs.table.toggleRowSelection(item, false)
      this.isIndeterminate = this.selectedUsers.length > 0 && this.selectedUsers.length < this.tableData.length
    },
    // 部门树选中
    handleCheckDepartment() {
      this.selectedDepartments = this.getSelectedDepartments()
    },
    getSelectedDepartments() {
      if (this.$refs.departmentTree) {
        const checkedNodes = this.$refs.departmentTree.getCheckedNodes(true)
        const allDepartments = this.getAllDepartments(this.treeData)
        this.indeterminateDepartment = checkedNodes.length > 0 && checkedNodes.length < allDepartments.length
        this.checkedDepartment = checkedNodes.length > 0 && checkedNodes.length == allDepartments.length
        return checkedNodes
      }
      return []
    },
    // 删除选中部门
    handleDeleteDepartment(item) {
      const allDepartments = this.getAllDepartments(this.treeData)
      const newSelectedDepartments = this.selectedDepartments.filter(department => department.id !== item.id)
      this.$set(this, 'selectedDepartments', newSelectedDepartments)
      if (this.$refs.departmentTree) this.$refs.departmentTree.setCheckedNodes(newSelectedDepartments)
      this.indeterminateDepartment = newSelectedDepartments.length > 0 && newSelectedDepartments.length < allDepartments.length
      this.checkedDepartment = newSelectedDepartments.length > 0 && newSelectedDepartments.length == allDepartments.length
    },
    // 部门树全选
    handleCheckAllDepartment() {
      if (!this.$refs.departmentTree) return
      if (this.checkedDepartment) {
        const allDepartments = this.getAllDepartments(this.treeData)
        this.selectedDepartments = allDepartments
        this.$refs.departmentTree.setCheckedNodes(allDepartments)
      } else {
        this.selectedDepartments = []
        this.$refs.departmentTree.setCheckedNodes([])
      }
    },
    // 获取所有部门
    getAllDepartments(data) {
      const departments = []
      data.forEach(item => {
        if (item.children && item.children.length > 0) departments.push(...this.getAllDepartments(item.children))
        else departments.push(item)
      })
      return departments
    }
  }
}
</script>
<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';

.info {
  height: 520px;
  margin-bottom: 20px;
  border: 1px solid #e1e2e2;

  &-checkall {
    height: 46px;
    border: 1px solid #e1e2e2;
    border-bottom: none;
    display: flex;
    align-items: center;
    padding-left: 50px;
  }

  &-tree {
    width: 285px;
    height: 100%;
    border-right: 1px solid #e1e2e2;
    background-color: transparent;
    padding-left: 0;
    padding-right: 0;

    &::-webkit-scrollbar {
      width: 5px;
    }

    &::-webkit-scrollbar-thumb {
      border-radius: 5px;
      background-color: rgba(0, 0, 0, 0.18);
    }

    &::-webkit-scrollbar-track {
      display: none;
    }

    &-checkbox {
      ::v-deep {
        .el-tree-node__content {
          .el-checkbox {
            margin-left: 50px;
          }
        }
      }
    }

    ::v-deep {
      .el-tree-node__content {
        position: relative;
        height: 36px;
        display: flex;
      }

      .el-tree-node__expand-icon {
        display: none;
      }

      .father {
        width: 50px;
        display: inline-block;
      }

      .son {
        width: 62px;
        display: inline-block;
      }

      .gson {
        width: 74px;
        display: inline-block;
      }

      .tree-icon {
        color: #9fa3b2;
        transition: all 0.3s;
      }

      .el-tree-node.is-expanded>.el-tree-node__content .custom-tree-node {
        color: #333333;
      }

      .el-tree-node.is-expanded>.el-tree-node__content .icon {
        color: #2e73f3;
        transform: rotate(-180deg);
        -webkit-transform: rotate(-180deg);
      }

      .el-tree .el-tree-node__expand-icon.is-leaf::before {
        display: none;
      }

      .el-tree-node__content .custom-tree-node {
        font-size: 14px;
        color: #666;
        line-height: 36px;
        user-select: none;
      }

      .el-tree-node__children {
        background-color: #f2f3f9;

        .el-tree-node__children {
          background-color: #ebeef5;
        }
      }

      .el-tree-node .el-tree-node__content:hover {
        background-color: #e1ecff;

        span {
          color: #2e73f3;
        }
      }

      .el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content {
        background: #e1ecff !important;

        span {
          color: #2e73f3;
        }
      }
    }
  }

  &-table {
    border: 0 !important;
    box-shadow: none !important;

    &:before {
      display: none;
    }

    ::v-deep {
      .el-table__body-wrapper {
        background-color: #f9f9f9 !important;

        tr {
          background-color: #f9f9f9 !important;

          .name {
            font-weight: 500;
            font-size: 14px;
            color: #333;
            line-height: 20px;
          }

          .department {
            font-size: 12px;
            color: #666;
            line-height: 20px;
          }

          .position {
            font-size: 12px;
            color: #999;
            line-height: 20px;
          }
        }

        &::-webkit-scrollbar {
          width: 5px;
        }

        &::-webkit-scrollbar-thumb {
          border-radius: 5px;
          background-color: rgba(0, 0, 0, 0.18);
        }

        &::-webkit-scrollbar-track {
          display: none;
        }
      }
    }
  }

  &-main {
    background-color: #f9f9f9;
    padding-top: 0;
    padding-bottom: 0;
    padding-left: 6px;
    padding-right: 10px;
  }

  &-container {
    flex: 1;
  }

  &-select {
    width: 380px;
    height: 100%;
    background-color: #f9f9f9;
    padding: 10px 5px;
    border-left: 1px solid #e1e2e2;

    &.info-select-department {
      width: 100%;
      border-left: 0;
    }

    &-box {
      display: flex;
      flex-wrap: wrap;
      align-content: flex-start;
      justify-content: flex-start;
      gap: 15px;
      padding: 5px 10px;
      height: 100%;
      overflow-x: hidden;

      &::-webkit-scrollbar {
        width: 5px;
      }

      &::-webkit-scrollbar-thumb {
        border-radius: 5px;
        background-color: rgba(0, 0, 0, 0.18);
      }

      &::-webkit-scrollbar-track {
        display: none;
      }
    }

    &-item {
      display: inline-flex;
      align-items: center;
      height: 36px;
      padding: 0 30px;
      background-color: #dfeaff;
      border-radius: 5px;
      position: relative;

      // margin-bottom: 15px;
      .title {
        font-weight: 500;
        font-size: 14px;
        color: #2e73f3;
      }

      .icon {
        position: absolute;
        right: -5px;
        top: -5px;
        font-size: 18px;
        cursor: pointer;

        &:hover {
          color: #2e73f3;
        }
      }
    }
  }
}

::v-deep {
  .body-hidden {
    .el-tabs__header {
      margin-bottom: 0;

      .el-tabs__nav-wrap::after {
        height: 1px;
        background-color: #e1e2e2;
      }
    }

    .el-tabs__content {
      display: none;
    }
  }
}
</style>
