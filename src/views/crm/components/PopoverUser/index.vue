<template>
  <div class="popover">
    <el-popover ref="popoverUser" trigger="click" popper-class="crm-popover" :visible-arrow="visibleArrow" @show="handleShow" @hide="handleAfterLeave">
      <template slot="reference">
        <div class="popover-diyInput" :class="{ active: active }">
          <span>{{ label }}</span>
          <i class="el-icon-arrow-down"></i>
        </div>
      </template>
      <div class="popover-box">
        <div class="popover-fatherTab">
          <div class="popover-fatherTab-item active">指定人员</div>
          <div class="popover-fatherTab-item">指定部门</div>
        </div>
        <el-tabs v-model="activeName" @tab-click="handleClick" class="popover-sonTab">
          <el-tab-pane v-for="item in sonTabList" :key="item.name" :label="item.label" :name="item.name"></el-tab-pane>
        </el-tabs>
        <div class="popover-search">
          <el-input v-model="search" placeholder="请输入内容">
            <template slot="suffix">
              <div class="popover-search-suffix">
                <el-button size="mini" icon="el-icon-search">搜索</el-button>
              </div>
            </template>
          </el-input>
        </div>
        <div class="popover-userList">
          <div class="popover-userList-item" :class="{ active: label === item.name }" v-for="item in userList" :key="item.id" @click="handleSelectUser(item)">
            <el-avatar :src="item.avatar" class="avatar"></el-avatar>
            <div class="info">
              <div class="name">{{ item.name }}</div>
              <div class="flex align-center flex-justify-between">
                <div class="department">{{ item.department }}</div>
                <div class="position">{{ item.department }}</div>
              </div>
            </div>
          </div>
        </div>
        <div class="popover-footer">
          <div class="popover-footer-item">
            <i class="el-icon-refresh"></i>
            <span>刷新列表</span>
          </div>
          <div class="popover-footer-item primary-color">
            <i class="el-icon-setting"></i>
            <span>人员设置</span>
          </div>
        </div>
      </div>
    </el-popover>
  </div>
</template>
<script>
export default {
  props: {
    visibleArrow: {
      type: Boolean,
      default: false
    }
  },
  name: 'PopoverUser',
  data() {
    return {
      label: '所有人',
      search: '',
      activeName: 'person',
      sonTabList: [
        { name: 'person', label: '按人员查找' },
        { name: 'department', label: '按部门查找' },
        { name: 'recent', label: '最近查看' }
      ],
      userList: [],
      active: false
    }
  },
  created() {
    this.userList = [
      { id: 9, avatar: 'https://randomuser.me/api/portraits/men/1.jpg', name: '刘一', department: '市场部', position: '市场专员' },
      { id: 10, avatar: 'https://randomuser.me/api/portraits/women/2.jpg', name: '陈二', department: '技术部', position: '后端开发工程师' },
      { id: 11, avatar: 'https://randomuser.me/api/portraits/men/3.jpg', name: '张三丰', department: '产品部', position: '产品助理' },
      { id: 12, avatar: 'https://randomuser.me/api/portraits/women/4.jpg', name: '李思思', department: '财务部', position: '会计' },
      { id: 13, avatar: 'https://randomuser.me/api/portraits/men/5.jpg', name: '王小明', department: '人力资源部', position: '招聘专员' },
      { id: 14, avatar: 'https://randomuser.me/api/portraits/women/6.jpg', name: '赵丽娜', department: '行政部', position: '前台' },
      { id: 15, avatar: 'https://randomuser.me/api/portraits/men/7.jpg', name: '钱多多', department: '商务部', position: '商务经理' },
      { id: 16, avatar: 'https://randomuser.me/api/portraits/women/8.jpg', name: '孙小花', department: '总经办', position: '秘书' },
      { id: 17, avatar: 'https://randomuser.me/api/portraits/men/9.jpg', name: '周大壮', department: '技术部', position: '测试工程师' },
      { id: 18, avatar: 'https://randomuser.me/api/portraits/women/10.jpg', name: '吴小婷', department: '市场部', position: '市场分析师' },
      { id: 19, avatar: 'https://randomuser.me/api/portraits/men/11.jpg', name: '赵小强', department: '市场部', position: '市场经理' },
      { id: 20, avatar: 'https://randomuser.me/api/portraits/women/12.jpg', name: '孙小美', department: '技术部', position: '前端开发工程师' },
      { id: 21, avatar: 'https://randomuser.me/api/portraits/men/13.jpg', name: '王小明', department: '产品部', position: '产品经理' },
      { id: 22, avatar: 'https://randomuser.me/api/portraits/women/14.jpg', name: '赵六', department: '财务部', position: '财务主管' },
      { id: 23, avatar: 'https://randomuser.me/api/portraits/men/15.jpg', name: '钱七', department: '人力资源部', position: 'HR专员' },
      { id: 24, avatar: 'https://randomuser.me/api/portraits/women/16.jpg', name: '孙八', department: '行政部', position: '行政助理' },
      { id: 25, avatar: 'https://randomuser.me/api/portraits/men/17.jpg', name: '周九', department: '商务部', position: '商务代表' },
      { id: 26, avatar: 'https://randomuser.me/api/portraits/women/18.jpg', name: '吴十', department: '总经办', position: '总经理助理' }
    ]
  },
  methods: {
    handleClick(tab, event) {
      console.log(tab, event)
      this.activeName = tab.name
    },
    handleSelectUser(item) {
      this.label = item.name
      this.$emit('selectUser', item)
      if (this.$refs.popoverUser) this.$refs.popoverUser.doClose()
    },
    handleShow() {
      this.active = true
    },
    handleAfterLeave() {
      this.active = false
    }
  }
}
</script>
<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
@import '~@/assets/styles/custom-global.scss';
.popover {
  display: inline-block;
  width: 100%;
  min-width: 215px;
  &-diyInput {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    background-color: #fff;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 0 10px;
    height: 32px;
    line-height: 32px;
    cursor: pointer;
    span {
      font-size: 14px;
      color: #606266;
    }
    i {
      font-size: 14px;
      color: #c0c4cc;
      transition: all 0.3s;
    }
    &.active {
      border-color: #2e73f3;
      i {
        transform: rotate(-180deg);
        -webkit-transform: rotate(-180deg);
        -moz-transform: rotate(-180deg);
        -ms-transform: rotate(-180deg);
        -o-transform: rotate(-180deg);
        color: #2e73f3;
      }
    }
  }
  &-box {
    width: 320px;
  }
  &-fatherTab {
    height: 52px;
    display: flex;
    align-items: flex-end;
    justify-content: center;
    background-color: #f5f5f5;
    border-bottom: 1px solid #cbd6e2;
    border-radius: 5px 5px 0 0;
    &-item {
      width: 155px;
      height: 47px;
      line-height: 47px;
      text-align: center;
      font-size: 12px;
      color: #666;
      cursor: pointer;
      border: 1px solid transparent;
      transition: all 0.3s;
      border-radius: 5px 5px 0 0;
      margin-bottom: -1px;
      &:hover,
      &.active {
        background-color: #fff;
        border: 1px solid #cbd6e2;
        border-bottom: transparent;
        font-weight: 500;
        font-size: 16px;
        color: #333;
      }
    }
  }
  &-sonTab {
    ::v-deep {
      .el-tabs__header {
        margin-bottom: 0;
        .el-tabs__nav {
          margin-left: 10px;
        }
      }
      .el-tabs__content {
        display: none;
      }
    }
  }
  &-search {
    padding: 10px;
    ::v-deep {
      .el-input__inner {
        border-color: #2e73f3;
      }
      .el-input__suffix {
        right: 2px !important;
      }
    }
    &-suffix {
      display: flex;
      align-items: center;
      height: 100%;
      .el-button {
        background-color: #e1ebff;
        color: #2e73f3;
      }
    }
  }
  &-userList {
    max-height: 400px;
    overflow-y: auto;
    overflow-x: hidden;
    &-item {
      display: flex;
      align-items: center;
      padding: 6px 20px;
      cursor: pointer;
      position: relative;
      transition: all 0.3s;
      &:hover,
      &.active {
        background-color: #e3edff;
      }
      &:after {
        content: '';
        position: absolute;
        left: 10px;
        bottom: 0;
        width: calc(100% - 20px);
        height: 1px;
        background-color: #e1e2e3;
      }
      .avatar {
        width: 40px;
        height: 40px;
        margin-right: 10px;
      }
      .info {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        font-size: 12px;
        line-height: 20px;
        .name {
          font-size: 14px;
          font-weight: 500;
          color: #333;
        }
        .department {
          color: #666;
        }
        .position {
          color: #999;
        }
      }
      &:last-child {
        &:after {
          display: none;
        }
      }
    }
    &::-webkit-scrollbar {
      width: 5px;
    }
    &::-webkit-scrollbar-thumb {
      border-radius: 5px;
      background-color: rgba(0, 0, 0, 0.18);
    }
    &::-webkit-scrollbar-track {
      display: none;
    }
  }
  &-footer {
    background-color: #f5f5f5;
    border-top: 1px solid #e1e2e3;
    border-radius: 0 0 5px 5px;
    display: flex;
    align-items: center;
    justify-content: space-around;
    padding: 0 10px;
    height: 52px;
    &-item {
      display: flex;
      align-items: center;
      cursor: pointer;
      color: #999;
      transition: all 0.3s;
      i {
        font-size: 20px;
        margin-right: 5px;
      }
      span {
        font-size: 12px;
      }
      &:hover,
      &.primary-color {
        color: #2e73f3;
      }
    }
  }
}
</style>
