<template>
  <div>
    <el-dialog v-dialogDragBox :title="title" :visible.sync="relevanceOpen" width="1150px" :before-close="handleClose" class="custom-dialog">
      <div class="flex">
        <div class="relevance-classify">
          <div class="relevance-classify-item active">客户</div>
          <div class="relevance-classify-item">联系跟进</div>
          <div class="relevance-classify-item">成交订单</div>
          <div class="relevance-classify-item">收款计划</div>
          <div class="relevance-classify-item">收款</div>
          <div class="relevance-classify-item">退款管理</div>
          <div class="relevance-classify-item">费用收支</div>
          <div class="relevance-classify-item">销售合同</div>
          <div class="relevance-classify-item">入库单</div>
          <div class="relevance-classify-item">出库单</div>
          <div class="relevance-classify-item">发货记录</div>
          <div class="relevance-classify-item">退货记录</div>
          <div class="relevance-classify-item">供应商</div>
          <div class="relevance-classify-item">采购记录</div>
        </div>
        <div class="relevance-content">
          <div class="relevance-content-title">
            <span>客户查看规则</span>
            <el-radio-group v-model="viewRule">
              <el-radio label="sync">与客户资料查看权限同步</el-radio>
              <el-radio label="custom">自定义权限</el-radio>
            </el-radio-group>
            <span class="relevance-content-title-setting" v-if="viewRule == 'custom'">权限设置</span>
          </div>
          <el-table ref="table" :data="data" stripe class="custom-table" style="width: 100%" @select-all="handleSelectAll" @selection-change="handleSelectionChange">
            <el-table-column align="center" type="selection" width="55"></el-table-column>
            <el-table-column align="center" type="index" label="序号" width="55"></el-table-column>
            <el-table-column align="center" prop="name" label="客户名称" show-overflow-tooltip>
              <template slot-scope="{ row }">
                <span class="table-link" @click="handleCompanyView(row)">{{ row.name }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="type" label="类型" show-overflow-tooltip>
              <template slot-scope="{ row }">
                {{ row.type === 1 ? '企业客户' : '个人客户' }}
              </template>
            </el-table-column>
            <el-table-column align="center" prop="owner" label="负责人" show-overflow-tooltip></el-table-column>
          </el-table>
          <div class="custom-pagination">
            <el-checkbox :indeterminate="isIndeterminate" v-model="checked" @change="handleCheckAll">全选</el-checkbox>
            <pagination :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
          </div>
        </div>
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn" @click="handleCancel()">取 消</el-button>
        <el-button class="custom-dialog-btn primary" @click="handleSubmit">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
export default {
  name: 'Relevance',
  data() {
    return {
      relevanceOpen: false,
      title: '关联业务',
      viewRule: 'sync',
      data: [],
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10
      },
      checked: false,
      isIndeterminate: false
    }
  },
  methods: {
    // 初始化
    init() {
      this.relevanceOpen = true
      this.getList()
    },
    // 获取列表
    getList() {
      this.data = [
        {
          id: 1,
          name: '深圳市科技有限公司',
          type: 1,
          owner: '张三'
        },
        {
          id: 2,
          name: '广州市贸易有限公司',
          type: 1,
          owner: '李四'
        },
        {
          id: 3,
          name: '王五',
          type: 2,
          owner: '赵六'
        },
        {
          id: 4,
          name: '上海电子科技有限公司',
          type: 1,
          owner: '孙七'
        },
        {
          id: 5,
          name: '北京软件开发有限公司',
          type: 1,
          owner: '周八'
        },
        {
          id: 6,
          name: '陈九',
          type: 2,
          owner: '吴十'
        },
        {
          id: 7,
          name: '杭州网络科技有限公司',
          type: 1,
          owner: '郑十一'
        },
        {
          id: 8,
          name: '武汉信息技术有限公司',
          type: 1,
          owner: '王十二'
        },
        {
          id: 9,
          name: '刘十三',
          type: 2,
          owner: '李十四'
        },
        {
          id: 10,
          name: '成都互联网科技有限公司',
          type: 1,
          owner: '张十五'
        },
        {
          id: 11,
          name: '重庆软件开发有限公司',
          type: 1,
          owner: '赵十六'
        }
      ]
      this.total = this.data.length
    },
    // 表格内的全选
    handleSelectAll(selection) {
      this.checked = !!selection.length
    },
    // 表格内的选中
    handleSelectionChange(selection) {
      this.isIndeterminate = selection.length > 0 && selection.length < this.total
    },
    // 全选
    handleCheckAll() {
      if (!this.$refs.table) return
      if (this.checked) {
        this.data.forEach(row => {
          this.$refs.table.toggleRowSelection(row, true)
        })
      } else this.$refs.table.clearSelection()
    },
    // 关闭
    handleClose() {
      this.handleCancel()
    },
    // 取消
    handleCancel(flag = false) {
      this.relevanceOpen = false
      this.$emit('callBack', flag)
    },
    // 确定
    handleSubmit() {
      console.log('handleSubmit')
    }
  }
}
</script>
<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
::v-deep {
  .custom-dialog {
    .el-dialog__body {
      padding: 0 !important;
    }
  }
  .relevance-classify {
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: #f5f5f5;
    padding: 20px 35px;
    &-item {
      min-width: 100px;
      height: 35px;
      line-height: 35px;
      text-align: center;
      cursor: pointer;
      transition: all 0.3s;
      &:hover,
      &.active {
        background-color: #fff;
        color: #2e73f3;
        border-radius: 5px;
        box-shadow: 0px 1px 7px 0px rgba(0, 0, 0, 0.06);
      }
    }
  }
  .relevance-content {
    flex: 1;
    padding: 0 15px;
    &-title {
      display: flex;
      align-items: center;
      height: 76px;
      gap: 20px;
      &-setting {
        padding-left: 20px;
        border-left: 1px solid #2e73f3;
        color: #2e73f3;
        cursor: pointer;
      }
    }
  }
  .custom-pagination {
    height: 80px;
    display: flex;
    align-items: center;
    .pagination-container {
      flex: 1;
      margin-top: 0;
      margin-bottom: 0;
      margin-left: 30px;
      .el-pagination {
        position: relative;
      }
    }
  }
}
</style>
