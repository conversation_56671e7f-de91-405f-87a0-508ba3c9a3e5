<template>
  <div class="newBox bgcf9 vh-85">
    <!-- 搜索 -->
    <div class="custom-search crm-search">
      <el-form ref="queryParams" :model="queryParams" size="small" inline class="custom-form-inline"
        @submit.native.prevent>
        <el-form-item label="客户名称" prop="name">
          <el-input v-model="queryParams.name" placeholder="请输入客户名称" @change="handleQuery" clearable />
        </el-form-item>
        <el-form-item label="成员" prop="member">
          <el-select v-model="queryParams.member" placeholder="请选择成员" clearable @change="handleQuery">
            <el-option label="全部" value=""></el-option>
            <el-option label="参与" value="cy"></el-option>
            <el-option label="负责" value="fz"></el-option>
            <el-option label="创建" value="cr"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button icon="el-icon-search" type="primary" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
          <el-popover ref="createPopover" trigger="click" popper-class="crm-popover" style="margin-left: 10px">
            <template slot="reference">
              <el-button type="primary">
                新建审批
                <i class="el-icon-arrow-down"></i>
              </el-button>
            </template>
            <div class="crm-popover-more">
              <div class="crm-popover-more-item" v-for="item in typeOptions" :key="item.code"
                @click="handleCreate(item)">{{ item.name }}</div>
            </div>
          </el-popover>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="status" placeholder="请选择状态" clearable @change="handleStatusChange">
            <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <!-- 分类 -->
    <div class="classify flex bgwhite">
      <div class="classify-item" :class="{ active: item.code == queryParams.code }" v-for="item in typeOptions"
        :key="item.code" @click="handleCategory(item)">{{ item.name }}</div>
    </div>
    <!-- 列表 -->
    <div style="padding: 15px 20px">
      <template v-if="total > 0">
        <el-table v-loading="loading" ref="table" stripe :data="list" style="width: 100%" class="custom-table">
          <el-table-column align="center" type="index" label="序号" width="50"></el-table-column>
          <el-table-column label="审批内容" align="center" min-width="200" show-overflow-tooltip>
            <template slot-scope="{ row }">
              <span v-if="row.variables.template === '外勤审批'">外勤签到地址：{{ row.variables.parameters.address }}</span>
              <span v-if="row.variables.template === '客户拜访申请'">拜访客户：{{ row.variables.parameters.customerName }}</span>
              <span v-if="row.variables.template === '请假申请'">请假内容：{{ row.variables.parameters.type }}</span>
              <span v-if="row.variables.template === '退款管理'">退款客户：{{ row.variables.parameters.headUserName }}</span>
            </template>
          </el-table-column>
          <el-table-column label="审批类型" align="center" :formatter="typeFormatter">
            <template slot-scope="{ row }">
              {{ row.variables.template }}
            </template>
          </el-table-column>
          <el-table-column label="创建时间" align="center">
            <template slot-scope="{ row }">
              {{ row.variables.createTime }}
            </template>
          </el-table-column>
          <el-table-column label="状态" align="center" :formatter="statusFormatter"></el-table-column>
          <el-table-column align="center" label="操作" width="220px">
            <template slot-scope="{ row }">
              <el-button class="table-btn" @click="handleView(row)">查看详情</el-button>
              <el-button class="table-btn primary" @click="handleEdit(row)">
                更多操作
                <i class="el-icon-arrow-right"></i>
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="custom-pagination">
          <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize" @pagination="getList(queryParams)" />
        </div>
      </template>
      <el-empty :description="!loading && !total ? '暂无数据' : '加载中…'" v-else />
    </div>
    <!-- 新增 -->
    <approval-create ref="approvalCreate" @callBack="handleCreateCallBack" v-if="showCreate" />
  </div>
</template>

<script>
import ApprovalCreate from './create.vue'
import { getApprovalTemplateList, getAllTask, putApproveDeleteTask, getApprovalCopyTask, getApprovalHistoryTaskForMe, getApprovalOwnerTask, getApprovalTaskList } from '@/api/crm/approval'

export default {
  name: 'OfficeApproval',
  components: { ApprovalCreate },
  data() {
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: undefined, // 客户名称
        currentUser: '', // 当前用户
        code: 'visit', // 类型
        // status: undefined // 状态
      },
      loading: false,
      total: 0,
      list: [],
      // 审批类型
      typeOptions: [
        // { value: '', label: '全部审批' },
        // { code: 'visit', name: '客户拜访申请' },
        // { code: 'leave', name: '请假审批' },
        // { code: 'travel', name: '出差审批' },
        // { code: 'field', name: '外勤审批' },
        // { code: 'fieldCard', name: '外勤补卡审批' },
        // { code: 'reimburse', name: '报销审批' },
        // { code: 'customer', name: '客户审批' },
        // { code: 'receipt', name: '收款审批' },
        // { code: 'order', name: '订单审批' },
        // { code: 'refund', name: '退款审批' },
        // { code: 'expense', name: '费用收支审批' }
      ],
      status: '1', // 状态
      // 状态组
      statusOptions: [
        { value: '1', label: '全部' },
        { value: '2', label: '待办' },
        { value: '3', label: '已办' },
        { value: '4', label: '创建' },
        { value: '5', label: '抄送' },
      ],
      showCreate: false,

    }
  },
  created() {
    this.getApprovalTemplateList()
  },
  methods: {
    // 获取审批模板列表
    async getApprovalTemplateList() {
      const { rows, code, msg } = await getApprovalTemplateList()
      if (code === 200) {
        this.typeOptions = rows
        this.queryParams.code = rows[0].code
        this.getList(this.queryParams)
      } else {
        this.$message.error(msg)
      }
    },
    // 获取列表
    // prettier-ignore
    async getList(params) {
      this.loading = true
      if (this.status == '1') {
        const res = await getAllTask(params)
        this.list = res.rows
        this.total = res.total
        this.loading = false
      } else if (this.status == '2') {
        const res = await getApprovalTaskList(params)
        this.list = res.rows
        this.total = res.total
        this.loading = false
      } else if (this.status == '3') {
        const res = await getApprovalHistoryTaskForMe(params)
        this.list = res.rows
        this.total = res.total
        this.loading = false
      } else if (this.status == '4') {
        const res = await getApprovalOwnerTask(params)
        this.list = res.rows
        this.total = res.total
        this.loading = false
      } else if (this.status == '5') {
        const res = await getApprovalCopyTask(params)
        this.list = res.rows
        this.total = res.total
        this.loading = false
      }
    },
    handleStatusChange() {
      this.queryParams.pageNum = 1
      this.handleQuery()
    },
    // 审批类型格式化
    typeFormatter(row) {
      return this.typeOptions.find(item => item.code === row.code)?.name || '--'
    },
    // 状态格式化
    statusFormatter(row) {
      return this.statusOptions.find(item => item.value === row.status)?.label || '--'
    },
    // 切换分类
    handleCategory(item) {
      this.queryParams.code = item.code
      this.handleQuery()
    },
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList(this.queryParams)
    },
    // 重置搜索
    resetQuery() {
      this.resetForm('queryParams')
      this.getList(this.queryParams)
    },
    // 新增
    handleCreate(item) {
      if (this.$refs.createPopover) this.$refs.createPopover.doClose()
      this.showCreate = true
      this.$nextTick(() => {
        this.$refs.approvalCreate.create(item)
      })
    },
    // 查看详情
    handleView(row) { },
    // 更多操作
    handleEdit(row) { },
    // 新增回调
    handleCreateCallBack(flag) {
      this.showCreate = false
      if (flag) this.getList(this.queryParams)
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
@import '~@/assets/styles/crm.scss';
</style>
