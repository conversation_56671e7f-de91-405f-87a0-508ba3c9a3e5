<template>
  <div>
    <el-dialog v-dialogDragBox :title="title" :visible.sync="open" width="width" :before-close="beforeClose" class="custom-dialog">
      <el-form ref="form" :model="form" :rules="rules" label-position="left" label-width="105px">
        <!-- 出差、请假审批 -->
        <div style="padding: 0 20px" v-if="type == '出差申请' || type == '请假申请'">
          <el-row :gutter="20">
            <template v-if="type == 'travel'">
              <el-col :span="12">
                <el-form-item label="出差目的地" prop="destination">
                  <el-input v-model="form.destination" placeholder="请输入出差目的地" />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="出差事由" prop="reason">
                  <el-input type="textarea" v-model="form.reason" placeholder="请输入出差事由" resize="none" :autosize="{ minRows: 3, maxRows: 6 }" />
                </el-form-item>
              </el-col>
            </template>
            <template v-if="type == 'leave'">
              <el-col :span="12">
                <el-form-item label="请假类型" prop="leaveType">
                  <el-select v-model="form.leaveType" placeholder="请选择请假类型" style="width: 100%">
                    <el-option label="事假" value="1"></el-option>
                    <el-option label="病假" value="2"></el-option>
                    <el-option label="调休" value="3"></el-option>
                    <el-option label="年假" value="4"></el-option>
                    <el-option label="婚假" value="5"></el-option>
                    <el-option label="产检假" value="6"></el-option>
                    <el-option label="产假" value="7"></el-option>
                    <el-option label="陪产假" value="8"></el-option>
                    <el-option label="哺乳假" value="9"></el-option>
                    <el-option label="育儿假" value="10"></el-option>
                    <el-option label="丧假" value="11"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="请假事由" prop="leaveReason">
                  <el-input type="textarea" v-model="form.leaveReason" placeholder="请输入请假事由" resize="none" :autosize="{ minRows: 3, maxRows: 6 }" />
                </el-form-item>
              </el-col>
            </template>
            <el-col :span="12">
              <el-form-item label="开始时间" prop="startTime">
                <el-date-picker v-model="form.startTime" type="datetime" placeholder="选择开始时间" style="width: 100%" :picker-options="startOptions" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="结束时间" prop="endTime">
                <el-date-picker v-model="form.endTime" type="datetime" placeholder="选择结束时间" style="width: 100%" :picker-options="endOptions" />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="出差时长">
                <div class="flex align-center">
                  <el-input v-model="form.hours" placeholder="请输入出差时长" readonly class="border-right-none">
                    <template slot="append">小时</template>
                  </el-input>
                  <el-input v-model="form.minutes" placeholder="请输入出差时长" readonly class="border-left-none">
                    <template slot="append">分钟</template>
                  </el-input>
                  <i class="clean ssfont ss-diy-question" style="margin-left: 10px"></i>
                  <span style="margin-left: 5px; color: #999">系统会根据开始结束时间自动计算出差时长</span>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="审批人" prop="approver">
                <el-tag closable @close="handleClose(tag)" class="crm-tag border-none">某某某</el-tag>
                <el-button type="text" icon="el-icon-plus" style="margin-left: 15px">选择人员</el-button>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="抄送给" prop="cc">
                <el-tag closable @close="handleClose(tag)" class="crm-tag border-none">某某某</el-tag>
                <el-button type="text" icon="el-icon-plus" style="margin-left: 15px">选择人员</el-button>
                <span style="margin-left: 15px; color: #2e73f3">|</span>
                <el-button type="text" style="margin-left: 15px">默认设置</el-button>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="相关文件" prop="files">
                <image-upload :compress="false" :limit="1" isRow v-model="form.files" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <!-- 订单审批 -->
        <template v-else-if="type == '销售订单'">
          <div style="padding: 0 20px">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="负责人" prop="manager">
                  <el-radio-group v-removeAriaHidden v-model="form.manager">
                    <el-radio label="customer">客户负责人</el-radio>
                    <el-radio label="assign">指定人员</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="参与人" prop="participants">
                  <div class="create-user-form-item">
                    <el-select v-model="form.participants" placeholder="请选择参与人">
                      <el-option label="张三" value="zhangsan"></el-option>
                      <el-option label="李四" value="lisi"></el-option>
                      <el-option label="王五" value="wangwu"></el-option>
                      <el-option label="赵六" value="zhaoliu"></el-option>
                      <el-option label="孙七" value="sunqi"></el-option>
                    </el-select>
                    <div class="create-config">
                      <i class="el-icon-setting"></i>
                      <span>自定义</span>
                    </div>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="发货日期" prop="deliveryDate">
                  <el-date-picker v-model="form.deliveryDate" type="date" placeholder="请选择发货日期" value-format="yyyy-MM-dd" style="width: 100%"></el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="主题" prop="subject">
                  <el-input v-model="form.subject" placeholder="请输入主题"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="企业微信" prop="wxNumber">
                  <div class="flex align-center">
                    <el-radio-group v-model="form.isAddWx">
                      <el-radio :label="1">已加</el-radio>
                      <el-radio :label="0">未加</el-radio>
                    </el-radio-group>
                    <div class="flex1" style="margin-left: 30px">
                      <el-input v-model="form.wxNumber" placeholder="请输入企业微信" />
                    </div>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="合同编号" prop="contractNo">
                  <el-input v-model="form.contractNo" placeholder="请输入合同编号"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="成交客户" prop="customer">
                  <el-select v-model="form.customer" placeholder="请选择成交客户" style="width: 100%">
                    <el-option label="华为技术有限公司" value="huawei"></el-option>
                    <el-option label="腾讯科技有限公司" value="tencent"></el-option>
                    <el-option label="阿里巴巴集团" value="alibaba"></el-option>
                    <el-option label="百度在线网络技术有限公司" value="baidu"></el-option>
                    <el-option label="小米科技有限责任公司" value="xiaomi"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="客户联系人" prop="contact">
                  <el-select v-model="form.contact" placeholder="请选择客户联系人" style="width: 100%">
                    <el-option label="张经理" value="zhang"></el-option>
                    <el-option label="李总监" value="li"></el-option>
                    <el-option label="王主管" value="wang"></el-option>
                    <el-option label="赵工程师" value="zhao"></el-option>
                    <el-option label="刘经理" value="liu"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="create-user-form-table">
            <div class="create-user-form-table-title">
              <span>成交产品</span>
              <el-button type="primary" plain size="small" icon="el-icon-plus" class="ml-50">添加产品</el-button>
            </div>
            <el-table :data="form.products" stripe class="custom-table custom-table-cell0" style="width: 100%">
              <el-table-column type="index" label="序号" width="50" align="center"></el-table-column>
              <el-table-column prop="productName" label="产品名称" align="center" show-overflow-tooltip>
                <template slot-scope="{ row }">
                  <span class="table-link" @click="handleView(row)">{{ row.productName }}</span>
                </template>
              </el-table-column>
              <el-table-column label="图片" width="70" align="center">
                <template slot-scope="{ row }">
                  <image-preview :src="row.diagram" :width="50" :height="50" />
                </template>
              </el-table-column>
              <el-table-column prop="model" label="产品型号" align="center" show-overflow-tooltip></el-table-column>
              <el-table-column prop="cost" label="产品成本(元)" align="center">
                <template slot-scope="{ row }">
                  <el-form-item label="" label-width="0">
                    <el-input v-model="row.cost" size="small" placeholder="请输入产品成本">
                      <template slot="suffix">元</template>
                    </el-input>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column prop="price" label="成交单价(元)" align="center">
                <template slot-scope="{ row }">
                  <el-form-item label="" label-width="0">
                    <el-input v-model="row.price" size="small" placeholder="请输入成交单价">
                      <template slot="suffix">元</template>
                    </el-input>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column prop="unit" label="单位" align="center">
                <template slot-scope="{ row }">
                  <el-form-item label="" label-width="0">
                    <el-select v-model="row.unit" size="small" placeholder="请选择单位">
                      <el-option label="个" value="个"></el-option>
                      <el-option label="台" value="台"></el-option>
                      <el-option label="套" value="套"></el-option>
                      <el-option label="件" value="件"></el-option>
                      <el-option label="箱" value="箱"></el-option>
                    </el-select>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column prop="quantity" label="成交数量" align="center">
                <template slot-scope="{ row }">
                  <el-form-item label="" label-width="0">
                    <el-input v-model="row.quantity" size="small" placeholder="请输入成交数量">
                      <template slot="suffix">{{ row.unit }}</template>
                    </el-input>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column prop="totalPrice" label="成交总价(元)" align="center">
                <template slot-scope="{ row }">
                  <el-form-item label="" label-width="0">
                    <el-input v-model="row.totalPrice" size="small" placeholder="请输入成交总价">
                      <template slot="suffix">元</template>
                    </el-input>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column label="操作" align="center" width="120">
                <template slot-scope="{ $index }">
                  <el-button class="table-btn danger" icon="el-icon-delete" @click="handleDelete($index)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
            <div class="crm-table-total">
              <div class="crm-table-total-item">
                <span>共</span>
                <span class="primary ml-5 mr-5">8</span>
                <span>个产品</span>
              </div>
            </div>
            <div style="margin-top: 15px; padding-bottom: 20px">
              <el-button type="primary" size="small" icon="el-icon-plus">添加产品</el-button>
            </div>
          </div>
          <div style="padding: 0 20px">
            <el-row :gutter="20">
              <el-col :span="24">
                <div class="flex align-center">
                  <el-form-item label="成交金额">
                    <el-input v-model="form.amount" placeholder="请输入成交金额" style="width: 270px">
                      <template slot="suffix">元</template>
                    </el-input>
                  </el-form-item>
                  <el-form-item label="折扣" label-width="3em" class="ml-20">
                    <el-input v-model="form.discount" placeholder="请输入折扣" style="width: 120px">
                      <template slot="suffix">%</template>
                    </el-input>
                  </el-form-item>
                  <el-form-item label="" label-width="0" class="ml-20">
                    <crm-switch v-model="form.isOpen" active-text="含税" inactive-text="不含税" />
                  </el-form-item>
                </div>
              </el-col>
            </el-row>
          </div>
          <div class="total-price">
            <div class="total-price-title">
              总成本
              <el-tooltip effect="dark" content="总成本=产品成本+运费+其他成本" placement="top">
                <i class="el-icon-question"></i>
              </el-tooltip>
            </div>
            <el-input v-model="form.totalCost" placeholder="请输入总成本">
              <template slot="suffix">元</template>
            </el-input>
            <div class="total-price-title sum">产品成本</div>
            <el-input v-model="form.productCost" placeholder="请输入产品成本">
              <template slot="suffix">元</template>
            </el-input>
            <div class="total-price-title plus">运费</div>
            <el-input v-model="form.freight" placeholder="请输入运费">
              <template slot="suffix">元</template>
            </el-input>
            <div class="total-price-title plus">其他成本</div>
            <el-input v-model="form.otherCost" placeholder="请输入其他成本">
              <template slot="suffix">元</template>
            </el-input>
          </div>
          <div style="padding: 20px 20px 0">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="实际成交时间" prop="dealTime">
                  <el-date-picker v-model="form.dealTime" type="datetime" placeholder="请选择实际成交时间" style="width: 100%"></el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="付款方式" prop="paymentMethod">
                  <div class="create-user-form-item">
                    <el-select v-model="form.paymentMethod" placeholder="请选择付款方式">
                      <el-option label="现金" value="cash"></el-option>
                      <el-option label="银行转账" value="transfer"></el-option>
                      <el-option label="支票" value="check"></el-option>
                      <el-option label="承兑汇票" value="acceptance"></el-option>
                      <el-option label="分期付款" value="installment"></el-option>
                      <el-option label="货到付款" value="cod"></el-option>
                    </el-select>
                    <el-popover trigger="click" popper-class="crm-popover">
                      <template slot="reference">
                        <div class="create-config">
                          <i class="el-icon-edit-outline"></i>
                          <span>编辑</span>
                        </div>
                      </template>
                      编辑信息,占位
                    </el-popover>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="有效期开始" prop="startTime">
                  <el-date-picker v-model="form.startTime" type="datetime" placeholder="请选择有效期开始时间" :picker-options="startOptions" style="width: 100%"></el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="有效期结束" prop="endTime">
                  <el-date-picker v-model="form.endTime" type="datetime" placeholder="请选择有效期结束时间" :picker-options="endOptions" style="width: 100%"></el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="订单状态" prop="orderStatus">
                  <div class="create-user-form-item">
                    <el-select v-model="form.orderStatus" placeholder="请选择订单状态" style="width: 100%">
                      <el-option label="待付款" value="pending"></el-option>
                      <el-option label="待发货" value="pending"></el-option>
                      <el-option label="待收货" value="pending"></el-option>
                      <el-option label="已完成" value="pending"></el-option>
                      <el-option label="已取消" value="pending"></el-option>
                    </el-select>
                    <el-popover trigger="click" popper-class="crm-popover">
                      <template slot="reference">
                        <div class="create-config">
                          <i class="el-icon-edit-outline"></i>
                          <span>编辑</span>
                        </div>
                      </template>
                      编辑信息,占位
                    </el-popover>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="说明/备注" prop="remark">
                  <el-input type="textarea" v-model="form.remark" placeholder="请输入说明/备注" resize="none" :autosize="{ minRows: 3, maxRows: 6 }" />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="相关文档" prop="files">
                  <image-upload :compress="false" :limit="1" isRow v-model="form.files" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="是否工程" prop="isProject">
                  <div class="create-user-form-item">
                    <el-select v-model="form.isProject" placeholder="请选择是否工程" style="width: 100%">
                      <el-option label="是" value="1"></el-option>
                      <el-option label="否" value="0"></el-option>
                    </el-select>
                    <el-popover trigger="click" popper-class="crm-popover">
                      <template slot="reference">
                        <div class="create-config">
                          <i class="el-icon-edit-outline"></i>
                          <span>编辑</span>
                        </div>
                      </template>
                      编辑信息,占位
                    </el-popover>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="是否发货" prop="isDelivery">
                  <div class="create-user-form-item">
                    <el-select v-model="form.isDelivery" placeholder="请选择是否发货" style="width: 100%">
                      <el-option label="是" value="1"></el-option>
                      <el-option label="否" value="0"></el-option>
                    </el-select>
                    <el-popover trigger="click" popper-class="crm-popover">
                      <template slot="reference">
                        <div class="create-config">
                          <i class="el-icon-edit-outline"></i>
                          <span>编辑</span>
                        </div>
                      </template>
                      编辑信息,占位
                    </el-popover>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <div class="create-user-form-title">
                  <span>审批信息</span>
                </div>
              </el-col>
              <el-col :span="12">
                <el-form-item label="审批流程" prop="approvalFlow">
                  <el-select v-model="form.approvalFlow" placeholder="请选择审批流程" style="width: 100%">
                    <el-option label="总经理审批" value="1"></el-option>
                    <el-option label="部门主管审批" value="2"></el-option>
                    <el-option label="人事审批" value="3"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="抄送给" prop="cc">
                  <el-tag closable @close="handleClose(tag)" class="crm-tag border-none">某某某</el-tag>
                  <el-button type="text" icon="el-icon-plus" style="margin-left: 15px">选择人员</el-button>
                  <span style="margin-left: 15px; color: #2e73f3">|</span>
                  <el-button type="text" style="margin-left: 15px">默认设置</el-button>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="flex align-center" style="padding: 0 20px 15px">
            <crm-switch v-model="form.isOpen" active-text="收款" inactive-text="收款" />
            <div class="flex align-center" style="margin-left: 20px" v-if="form.isOpen">
              <i class="clean ssfont ss-diy-question"></i>
              <span style="font-size: 12px; color: #999; margin-left: 5px">如果有跟订单关联的收款和收款计划，可以同步创建收款以及收款计划</span>
            </div>
          </div>
          <div class="create-user-form-table triangle triangle-55">
            <el-table :data="form.otherCostTable" stripe class="custom-table custom-table-cell0" style="width: 100%">
              <el-table-column type="index" label="序号" width="50" align="center"></el-table-column>
              <el-table-column prop="period" label="期次" align="center"></el-table-column>
              <el-table-column prop="status" label="状态" align="center"></el-table-column>
              <el-table-column prop="amount" label="金额(元)" align="center">
                <template slot-scope="{ row }">
                  <el-form-item label="" label-width="0">
                    <el-input v-model="row.amount" size="small" placeholder="请输入金额">
                      <template slot="suffix">元</template>
                    </el-input>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column prop="voucher" label="收款凭证" align="center">
                <template slot-scope="{ row }">
                  <el-button type="text" icon="el-icon-plus">添加</el-button>
                </template>
              </el-table-column>
              <el-table-column prop="actualTime" label="实际收款时间" align="center">
                <template slot-scope="{ row }">
                  <el-form-item label="" label-width="0">
                    <el-date-picker v-model="row.actualTime" type="date" size="small" placeholder="选择日期" style="width: 100%"></el-date-picker>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column prop="remark" label="备注" align="center">
                <template slot-scope="{ row }">
                  <el-form-item label="" label-width="0">
                    <el-input v-model="row.remark" size="small" placeholder="请输入备注"></el-input>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column label="操作" align="center" width="120">
                <template slot-scope="{ $index }">
                  <el-button class="table-btn danger" icon="el-icon-delete">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
            <div class="crm-table-total crm-table-total-desc">
              <div class="flex align-center">
                <div class="crm-table-total-item">
                  <span>该订单应收金额(元)：</span>
                  <span class="default ml-5">1.546.000.00</span>
                </div>
                <div class="crm-table-total-item">
                  <span>已收金额(元)：</span>
                  <span class="yellow ml-5">126.000.00</span>
                </div>
                <div class="crm-table-total-item">
                  <span>未收金额(元)：</span>
                  <span class="yellow ml-5">1.420.000.00</span>
                </div>
              </div>
            </div>
          </div>
          <div class="flex align-center" style="padding: 0 20px 15px">
            <crm-switch v-model="form.isOpen" active-text="其他费用" inactive-text="其他费用" />
            <div class="flex align-center" style="margin-left: 20px" v-if="form.isOpen">
              <i class="clean ssfont ss-diy-question"></i>
              <span style="font-size: 12px; color: #999; margin-left: 5px">如果有跟订单关联的其他费用，可以同步创建费用收支记录</span>
            </div>
          </div>
          <div class="create-user-form-table triangle triangle-55">
            <el-table :data="form.others" stripe class="custom-table custom-table-cell0" style="width: 100%">
              <el-table-column type="index" label="序号" width="50" align="center"></el-table-column>
              <el-table-column prop="type" label="类型" align="center">
                <template slot="header">
                  <div class="flex align-center flex-justify-center">
                    <span style="margin-right: 10px">类型</span>
                    <el-popover trigger="click" popper-class="crm-popover">
                      <template slot="reference">
                        <div class="create-config">
                          <i class="el-icon-setting"></i>
                          <span>自定义</span>
                        </div>
                      </template>
                      自定义占位信息
                    </el-popover>
                  </div>
                </template>
                <template slot-scope="{ row }">
                  <el-popover trigger="click" :visible-arrow="false" popper-class="crm-popover">
                    <template slot="reference">
                      <div class="custom-table-type">
                        <b :class="row.type == 'transport' ? 'transport' : 'installation'">{{ row.type == 'transport' ? '收取' : '支出' }}</b>
                        <span>{{ row.typeName }}</span>
                        <i class="el-icon-arrow-down"></i>
                      </div>
                    </template>
                    <div class="custom-table-type-tab">
                      <span>收取</span>
                      <span class="active">支出</span>
                    </div>
                    <div class="custom-table-type-list">
                      <span class="custom-table-type-item active">咨询费</span>
                      <span class="custom-table-type-item">贷款</span>
                      <span class="custom-table-type-item">其他</span>
                    </div>
                  </el-popover>
                </template>
              </el-table-column>
              <el-table-column prop="amount" label="金额(元)" align="center">
                <template slot-scope="{ row }">
                  <el-form-item label="" label-width="0">
                    <el-input v-model="row.amount" size="small" placeholder="请输入金额">
                      <template slot="suffix">元</template>
                    </el-input>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column prop="voucher" label="凭证" align="center">
                <template slot-scope="{ row }">
                  <el-button type="text" icon="el-icon-plus">添加</el-button>
                </template>
              </el-table-column>
              <el-table-column prop="date" label="日期" align="center">
                <template slot-scope="{ row }">
                  <el-form-item label="" label-width="0">
                    <el-date-picker v-model="row.date" type="date" size="small" placeholder="选择日期" style="width: 100%"></el-date-picker>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column prop="description" label="说明" align="center">
                <template slot-scope="{ row }">
                  <el-form-item label="" label-width="0">
                    <el-input v-model="row.description" size="small" placeholder="请输入说明"></el-input>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column label="操作" align="center" width="120">
                <template slot-scope="{ $index }">
                  <el-button class="table-btn danger" icon="el-icon-delete">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
            <div class="crm-table-total crm-table-total-desc">
              <div class="flex align-center">
                <div class="crm-table-total-item">
                  <span>支出的费用将会记入订单成本</span>
                </div>
                <div class="crm-table-total-item">
                  <span>合计(元)：</span>
                  <span class="default ml-5">1.546.000.00</span>
                </div>
              </div>
            </div>
          </div>
        </template>
        <!-- 收款、退款审批 -->
        <template v-else-if="type == '收款审批' || type == '退款管理'">
          <div style="padding: 0 20px">
            <div class="price-form-info" :class="type">
              <div class="price-form-info-title">{{ type == 'receipt' ? '收款' : '退款' }}</div>
              <div style="padding: 20px 20px 0">
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="选择客户" prop="customer">
                      <el-select v-model="form.customer" placeholder="请选择客户" style="width: 100%">
                        <el-option label="华为技术有限公司" value="huawei"></el-option>
                        <el-option label="腾讯科技有限公司" value="tencent"></el-option>
                        <el-option label="阿里巴巴集团" value="alibaba"></el-option>
                        <el-option label="百度在线网络技术有限公司" value="baidu"></el-option>
                        <el-option label="小米科技有限责任公司" value="xiaomi"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="联系人" prop="contact">
                      <el-select v-model="form.contact" placeholder="请选择联系人" style="width: 100%">
                        <el-option label="张经理" value="zhang"></el-option>
                        <el-option label="李总监" value="li"></el-option>
                        <el-option label="王主管" value="wang"></el-option>
                        <el-option label="赵工程师" value="zhao"></el-option>
                        <el-option label="刘经理" value="liu"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="负责人" prop="manager">
                      <el-radio-group v-model="form.manager">
                        <el-radio label="self">客户负责人</el-radio>
                        <el-radio label="order">订单负责人</el-radio>
                        <el-radio label="other">指定人员</el-radio>
                        <el-tag closable @close="handleClose(tag)" class="crm-tag border-none">某某某</el-tag>
                        <el-button type="text" icon="el-icon-plus" style="margin-left: 15px">选择人员</el-button>
                      </el-radio-group>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="收款时间" prop="receiptTime">
                      <el-date-picker v-model="form.receiptTime" type="datetime" placeholder="请选择收款时间" style="width: 100%"></el-date-picker>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="收款金额" prop="receiptAmount">
                      <el-input v-model="form.receiptAmount" placeholder="请输入收款金额">
                        <template slot="suffix">元</template>
                      </el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
            </div>
            <div class="price-form-tips">
              <span>发现此客户有</span>
              <span class="primary">433</span>
              <span>笔未收款的记录，系统已将收款自动分配完成，以完成收款</span>
            </div>
            <el-table :data="form.orders" stripe class="custom-table custom-table-cell0" style="width: 100%">
              <el-table-column type="index" label="序号" width="50" align="center"></el-table-column>
              <el-table-column prop="orderNo" label="销售订单" align="center" show-overflow-tooltip>
                <template slot-scope="{ row }">
                  <span class="table-link">{{ row.orderNo }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="dealTime" label="成交时间" align="center" show-overflow-tooltip></el-table-column>
              <el-table-column prop="totalAmount" label="成交总额" align="center"></el-table-column>
              <el-table-column prop="debt" label="欠款" align="center">
                <template slot-scope="{ row }">
                  <span class="color-orange">{{ row.debt }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="currentPayment" label="本次收款" align="center">
                <template slot-scope="{ row }">
                  <el-form-item label="" label-width="0">
                    <el-input v-model="row.currentPayment" size="small" placeholder="请输入收款金额">
                      <template slot="suffix">元</template>
                    </el-input>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column prop="remark" label="备注" align="center">
                <template slot-scope="{ row }">
                  <el-form-item label="" label-width="0">
                    <el-input v-model="row.remark" size="small" placeholder="请输入备注"></el-input>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column label="操作" align="center" width="120">
                <template slot-scope="{ $index }">
                  <el-button class="table-btn danger" icon="el-icon-delete">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
            <div class="crm-table-total">
              <div class="flex align-center">
                <div class="crm-table-total-item">
                  <span>共</span>
                  <span class="primary ml-5 mr-5">8</span>
                  <span>个订单</span>
                </div>
                <div class="crm-table-total-item ml-20">
                  <span>成交总额：</span>
                  <span class="primary ml-5">126.000.00</span>
                </div>
                <div class="crm-table-total-item ml-20">
                  <span>欠款：</span>
                  <span class="yellow ml-5" style="font-size: 18px">1.420.000.00</span>
                </div>
              </div>
            </div>
            <div class="create-user-form-title mt-10">
              <span>其他</span>
              <div class="flex align-center">
                <div class="create-config">
                  <i class="el-icon-setting"></i>
                  <span>审批设置</span>
                </div>
                <div class="create-config ml-20">
                  <i class="el-icon-setting"></i>
                  <span>自定义</span>
                </div>
              </div>
            </div>
            <el-row :gutter="20" v-if="type == 'refund'">
              <el-col :span="12">
                <el-form-item label="付款方式" prop="paymentMethod">
                  <div class="create-user-form-item">
                    <el-select v-model="form.paymentMethod" placeholder="请选择付款方式">
                      <el-option label="现金" value="cash"></el-option>
                      <el-option label="银行转账" value="transfer"></el-option>
                      <el-option label="支票" value="check"></el-option>
                      <el-option label="承兑汇票" value="acceptance"></el-option>
                      <el-option label="分期付款" value="installment"></el-option>
                      <el-option label="货到付款" value="cod"></el-option>
                    </el-select>
                    <el-popover trigger="click" popper-class="crm-popover">
                      <template slot="reference">
                        <div class="create-config">
                          <i class="el-icon-edit-outline"></i>
                          <span>编辑</span>
                        </div>
                      </template>
                      编辑信息,占位
                    </el-popover>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item label="备注" prop="remarks">
              <el-input type="textarea" v-model="form.remarks" placeholder="请输入备注" resize="none" :autosize="{ minRows: 3, maxRows: 6 }" />
            </el-form-item>
            <el-form-item label="收款凭证" prop="vouchers">
              <image-upload :compress="false" :limit="3" isRow v-model="form.vouchers" />
            </el-form-item>
            <div class="create-user-form-title">
              <span>审批信息</span>
            </div>
            <el-form-item label="审批流程" prop="approvalFlow">
              <el-select v-model="form.approvalFlow" placeholder="请选择审批流程" style="width: 100%">
                <el-option label="总经理审批" value="1"></el-option>
                <el-option label="财务审批" value="2"></el-option>
                <el-option label="部门主管审批" value="3"></el-option>
                <el-option label="自定义审批" value="4"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="抄送给" prop="cc">
              <el-tag closable @close="handleClose(tag)" class="crm-tag border-none">某某某</el-tag>
              <el-button type="text" icon="el-icon-plus" style="margin-left: 15px">选择人员</el-button>
              <span style="margin-left: 15px; color: #2e73f3">|</span>
              <el-button type="text" style="margin-left: 15px">默认设置</el-button>
            </el-form-item>
            <div class="flex align-center mb-20">
              <el-switch v-model="model" active-color="#2E74F3" inactive-color="#D5DAE4"></el-switch>
              <span class="ml-15">@其他成员</span>
            </div>
          </div>
          <div class="create-user-form-table triangle">
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="@成员" prop="owner">
                  <el-tag closable @close="handleClose(tag)" class="crm-tag">某某某</el-tag>
                  <el-button type="text" icon="el-icon-plus" style="margin-left: 15px">选择人员</el-button>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </template>
        <!-- 报销审批 -->
        <template v-else-if="type == '报销审批'">
          <div style="padding: 0 20px">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="报销金额" prop="reimbursementAmount">
                  <el-input v-model="form.reimbursementAmount" placeholder="请输入报销金额">
                    <template slot="suffix">元</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="消费时间" prop="consumptionTime">
                  <el-date-picker v-model="form.consumptionTime" type="datetime" placeholder="请选择消费时间" style="width: 100%"></el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="报销理由" prop="reimbursementReason">
                  <el-input type="textarea" v-model="form.reimbursementReason" placeholder="请输入报销理由" resize="none" :autosize="{ minRows: 3, maxRows: 6 }" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="create-user-form-plus">
            <span>关联业务</span>
            <el-button type="text" icon="el-icon-plus">添加</el-button>
          </div>
          <div class="create-user-form-table triangle triangle-150 pb-20">
            <div class="create-user-form-custom">
              <div class="title">客户</div>
              <div class="content">
                <el-tag closable class="crm-tag" v-for="item in form.customers" :key="item.id">{{ item.name }}</el-tag>
              </div>
            </div>
          </div>
          <div style="padding: 0 20px">
            <el-form-item label="相关文件" prop="files">
              <image-upload :compress="false" :limit="1" isRow v-model="form.files" />
            </el-form-item>
          </div>
          <div class="create-user-form-plus">
            <span>费用明细</span>
            <el-button type="text" icon="el-icon-plus">添加</el-button>
          </div>
          <div class="create-user-form-table triangle triangle-150">
            <el-table :data="form.costDetails" stripe class="custom-table custom-table-cell0" style="width: 100%">
              <el-table-column type="index" label="序号" width="50" align="center"></el-table-column>
              <el-table-column prop="name" label="费用名称" align="center">
                <template slot-scope="{ row }">
                  <el-form-item label="" label-width="0">
                    <el-select v-model="row.name" size="small" placeholder="请选择费用名称">
                      <el-option label="差旅费" value="差旅费"></el-option>
                      <el-option label="餐饮费" value="餐饮费"></el-option>
                      <el-option label="交通费" value="交通费"></el-option>
                      <el-option label="住宿费" value="住宿费"></el-option>
                      <el-option label="其他" value="其他"></el-option>
                    </el-select>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column prop="amount" label="支出金额" align="center">
                <template slot-scope="{ row }">
                  <el-form-item label="" label-width="0">
                    <el-input v-model="row.amount" size="small" placeholder="请输入支出金额">
                      <template slot="suffix">元</template>
                    </el-input>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column prop="remark" label="备注" align="center">
                <template slot-scope="{ row }">
                  <el-form-item label="" label-width="0">
                    <el-input v-model="row.remark" size="small" placeholder="请输入备注"></el-input>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column label="操作" align="center" width="120">
                <template slot-scope="{ $index }">
                  <el-button class="table-btn danger" icon="el-icon-delete">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
            <div class="crm-table-total crm-table-total-desc">
              <div class="flex align-center">
                <div class="crm-table-total-item">
                  <span>合计(元)：</span>
                  <span class="yellow ml-5">1.546.000.00</span>
                </div>
              </div>
            </div>
          </div>
          <div style="padding: 0 20px">
            <div class="create-user-form-title mt-10">
              <span>审批信息</span>
            </div>
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="审批人" prop="approver">
                  <el-tag closable @close="handleClose(tag)" class="crm-tag border-none">某某某</el-tag>
                  <el-button type="text" icon="el-icon-plus" style="margin-left: 15px">选择人员</el-button>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="抄送给" prop="cc">
                  <el-tag closable @close="handleClose(tag)" class="crm-tag border-none">某某某</el-tag>
                  <el-button type="text" icon="el-icon-plus" style="margin-left: 15px">选择人员</el-button>
                  <span style="margin-left: 15px; color: #2e73f3">|</span>
                  <el-button type="text" style="margin-left: 15px">默认设置</el-button>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </template>
        <!-- 工作请示审批 -->
        <template v-else-if="type == '工作请示'">
          <div style="padding: 0 20px">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="优先级别" prop="priority">
                  <el-select v-model="form.priority" placeholder="请选择优先级别" style="width: 100%">
                    <el-option label="紧急" value="urgent"></el-option>
                    <el-option label="普通" value="normal"></el-option>
                    <el-option label="暂时不重要" value="low"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="选择时间" prop="requestTime">
                  <el-date-picker v-model="form.requestTime" type="datetime" placeholder="请选择时间" style="width: 100%"></el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="工作内容" prop="workContent">
                  <el-input type="textarea" v-model="form.workContent" placeholder="请输入工作内容" resize="none" :autosize="{ minRows: 3, maxRows: 6 }" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="create-user-form-plus">
            <span>关联业务</span>
            <el-button type="text" icon="el-icon-plus" @click="handleAddBusiness">添加</el-button>
          </div>
          <div class="create-user-form-table triangle triangle-150 pb-20">
            <div class="create-user-form-custom">
              <div class="title">客户</div>
              <div class="content">
                <el-tag closable class="crm-tag" v-for="item in form.customers" :key="item.id">{{ item.name }}</el-tag>
              </div>
            </div>
          </div>
          <div style="padding: 0 20px">
            <el-col :span="24">
              <el-form-item label="审批人" prop="approver">
                <el-tag closable @close="handleClose(tag)" class="crm-tag border-none">某某某</el-tag>
                <el-button type="text" icon="el-icon-plus" style="margin-left: 15px">选择人员</el-button>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="抄送给" prop="cc">
                <el-tag closable @close="handleClose(tag)" class="crm-tag border-none">某某某</el-tag>
                <el-button type="text" icon="el-icon-plus" style="margin-left: 15px">选择人员</el-button>
                <span style="margin-left: 15px; color: #2e73f3">|</span>
                <el-button type="text" style="margin-left: 15px">默认设置</el-button>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="相关文件" prop="files">
                <image-upload :compress="false" :limit="1" isRow v-model="form.files" />
              </el-form-item>
            </el-col>
          </div>
        </template>
      </el-form>
      <div slot="footer">
        <el-button class="custom-dialog-btn" @click="handleCancel()">取 消</el-button>
        <el-button class="custom-dialog-btn primary" @click="handleSubmit">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 关联业务 -->
    <relevance ref="relevance" @callBack="relevanceVisible = false" v-if="relevanceVisible" />
  </div>
</template>
<script>
import CrmSwitch from '@/components/CrmSwitch'
import Relevance from './relevance'

export default {
  name: 'ApprovalCreate',
  components: { CrmSwitch, Relevance },
  data() {
    return {
      model: false,
      type: '',
      open: false,
      title: '',
      form: {},
      rules: {},
      startOptions: {
        disabledDate: time => {
          return time.getTime() < Date.now() - 24 * 60 * 60 * 1000
        }
      },
      endOptions: {
        disabledDate: time => {
          if (!this.form.startTime) {
            return time.getTime() < Date.now() - 24 * 60 * 60 * 1000
          }
          return time.getTime() < new Date(this.form.startTime).getTime()
        }
      },
      relevanceVisible: false
    }
  },
  watch: {
    'form.startTime': {
      handler(val) {
        if (val && this.form.endTime) {
          this.calculateDuration()
          // 如果结束时间小于开始时间，清空结束时间
          if (new Date(this.form.endTime).getTime() <= new Date(val).getTime()) {
            this.form.endTime = ''
          }
        }
      }
    },
    'form.endTime': {
      handler(val) {
        if (val && this.form.startTime) {
          this.calculateDuration()
        }
      }
    }
  },
  methods: {
    // 表单重置
    reset() {
      this.form = {
        isOpen: false,
        products: [],
        otherCostTable: [],
        others: []
      }
      this.resetForm('form')
    },
    // 创建
    create(item = {}) {
      const { code, name } = item
      this.reset()
      this.form.products = [
        {
          productName: '高性能服务器',
          diagram: 'https://example.com/server.jpg',
          model: 'SVR-2000',
          cost: '15000',
          price: '20000',
          quantity: '2',
          totalPrice: '40000'
        },
        {
          productName: '企业级路由器',
          diagram: 'https://example.com/router.jpg',
          model: 'RT-500',
          cost: '3000',
          price: '4500',
          quantity: '5',
          totalPrice: '22500'
        },
        {
          productName: '网络交换机',
          diagram: 'https://example.com/switch.jpg',
          model: 'SW-1000',
          cost: '2000',
          price: '3000',
          quantity: '8',
          totalPrice: '24000'
        },
        {
          productName: '防火墙设备',
          diagram: 'https://example.com/firewall.jpg',
          model: 'FW-300',
          cost: '8000',
          price: '12000',
          quantity: '3',
          totalPrice: '36000'
        },
        {
          productName: '存储阵列',
          diagram: 'https://example.com/storage.jpg',
          model: 'SA-800',
          cost: '25000',
          price: '35000',
          quantity: '1',
          totalPrice: '35000'
        }
      ]
      this.form.otherCostTable = [
        {
          period: '首期款',
          status: '已付款',
          amount: '10000',
          voucher: '',
          actualTime: '2023-09-15',
          remark: '首付款已结清'
        },
        {
          period: '中期款',
          status: '部分付款',
          amount: '15000',
          voucher: '',
          actualTime: '2023-10-20',
          remark: '按合同约定支付'
        },
        {
          period: '尾款',
          status: '未付款',
          amount: '8000',
          voucher: '',
          actualTime: '',
          remark: '待验收后支付'
        }
      ]
      this.form.others = [
        {
          type: 'transport',
          typeName: '运输费用',
          amount: '3800',
          voucher: '',
          date: '2023-11-05',
          description: '产品运输费用'
        },
        {
          type: 'installation',
          typeName: '安装费用',
          amount: '2500',
          voucher: '',
          date: '2023-11-15',
          description: '设备安装调试费'
        }
      ]
      this.form.orders = [
        {
          orderNo: 'SO-2023-10001',
          dealTime: '2023-09-05',
          totalAmount: '56,800.00',
          debt: '15,000.00',
          currentPayment: '10,000.00',
          remark: ''
        },
        {
          orderNo: 'SO-2023-10025',
          dealTime: '2023-10-12',
          totalAmount: '78,500.00',
          debt: '25,000.00',
          currentPayment: '15,000.00',
          remark: ''
        },
        {
          orderNo: 'SO-2023-10118',
          dealTime: '2023-11-03',
          totalAmount: '32,450.00',
          debt: '12,000.00',
          currentPayment: '8,000.00',
          remark: ''
        }
      ]
      this.form.customers = [
        {
          name: '华为技术有限公司',
          id: '1'
        },
        {
          name: '腾讯科技有限公司',
          id: '2'
        },
        {
          name: '阿里巴巴集团',
          id: '3'
        },
        {
          name: '百度在线网络技术有限公司',
          id: '4'
        },
        {
          name: '小米科技有限责任公司',
          id: '5'
        },
        {
          name: '京东集团',
          id: '6'
        },
        {
          name: '深圳市中兴通讯股份有限公司技术研发中心',
          id: '7'
        },
        {
          name: '字节跳动（北京）科技有限公司研发总部',
          id: '8'
        },
        {
          name: '上海浦东新区人工智能创新发展有限公司',
          id: '9'
        },
        {
          name: '广州市云计算大数据产业技术研究院有限公司',
          id: '10'
        }
      ]
      this.form.costDetails = [
        {
          name: '差旅费',
          amount: '1000',
          remark: '差旅费'
        },
        {
          name: '餐饮费',
          amount: '1000',
          remark: '餐饮费'
        }
      ]
      console.log(item)
      this.title = `新建${name}`
      this.type = name
      this.open = true
    },
    // 关闭回调
    beforeClose() {
      this.handleCancel()
    },
    // 取消
    handleCancel(flag = false) {
      this.open = false
      this.$emit('callBack', flag)
    },
    // 确定
    handleSubmit() {
      console.log(this.form)
      // this.handleCancel(true)
    },
    // 计算出差时长
    calculateDuration() {
      const start = new Date(this.form.startTime).getTime()
      const end = new Date(this.form.endTime).getTime()
      const duration = end - start
      // 计算小时和分钟
      const totalMinutes = Math.floor(duration / (60 * 1000))
      const hours = Math.floor(totalMinutes / 60)
      const minutes = totalMinutes % 60
      this.form.hours = hours > 0 ? hours : 0
      this.form.minutes = minutes > 0 ? minutes : 0
    },
    handleAddBusiness() {
      this.relevanceVisible = true
      this.$nextTick(() => {
        this.$refs.relevance.init()
      })
    }
  }
}
</script>
<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
@import '~@/assets/styles/crm.scss';
::v-deep {
  .border-right-none {
    width: 200px;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    .el-input-group__append {
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
      border-right-width: 0;
    }
  }
  .border-left-none {
    width: 200px;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    .el-input__inner {
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }
  }
  .total-price {
    padding: 30px 20px;
    background-color: #f0f3f9;
    display: flex;
    align-items: center;
    &-title {
      display: flex;
      align-items: center;
      font-size: 12px;
      color: #999;
      flex-shrink: 0;
      margin-right: 15px;
      &.sum {
        &:before {
          content: '=';
          color: #2e73f3;
          margin-right: 5px;
          margin-left: 15px;
        }
      }
      &.plus {
        &:before {
          content: '+';
          color: #2e73f3;
          margin-right: 5px;
          margin-left: 15px;
        }
      }
    }
    .el-input__suffix {
      display: flex !important;
      align-items: center !important;
    }
  }
  .custom-table {
    border: 0;
    border-radius: 0;
    .el-form-item {
      margin-top: 10px !important;
      margin-bottom: 10px !important;
      .el-form-item__error {
        top: 95%;
        padding-top: 0;
      }
      .el-input__suffix-inner {
        .el-select {
          .el-input__inner {
            padding-left: 0;
            padding-right: 10px;
            border: 0;
            height: 30px;
            line-height: 30px;
          }
          .el-input__suffix {
            right: 0;
          }
        }
      }
    }
    .custom-table-type {
      display: flex;
      align-items: center;
      cursor: pointer;
      padding: 0 5px;
      height: 32px;
      border-radius: 4px;
      background-color: #fff;
      border: 1px solid #cbd7e2;
      font-size: 12px;
      b {
        display: inline-block;
        line-height: 26px;
        border-radius: 5px;
        padding: 0 5px;
        color: #fff;
        flex-shrink: 0;
        font-weight: normal;
        &.transport {
          background-color: #31c776;
        }
        &.installation {
          background-color: #ee3a22;
        }
      }
      span {
        flex: 1;
        padding: 0 5px;
      }
      i {
        font-size: 16px;
        color: #aaa;
        transition: all 0.5s;
      }
      &:focus,
      &:active {
        border-color: #2e73f3;
        color: #2e73f3;
        transition: all 0.3s;
        i {
          color: #2e73f3;
          transform: rotate(180deg);
        }
      }
    }
  }
  .price-form-info {
    border-radius: 5px;
    border-width: 1px;
    border-color: #cbd6e2;
    border-style: solid;
    overflow: hidden;
    &-title {
      line-height: 60px;
      text-align: center;
      font-size: 18px;
      font-weight: 500;
      color: #666;
    }
    &.receipt {
      border-color: #cbd6e2;
      .price-form-info-title {
        background-color: #f8f9fb;
        border-bottom: 1px solid #cbd6e2;
      }
    }
    &.refund {
      border-color: #f02323;
      .price-form-info-title {
        color: #f02323;
        background-color: #ffe3e3;
      }
    }
  }
  .price-form-tips {
    padding: 15px 0;
    font-size: 12px;
    color: #666;
    line-height: 20px;
    span {
      display: inline-block;
      &.primary {
        color: #2e73f3;
        padding: 0 10px;
      }
    }
  }
}
</style>
<style lang="scss">
.custom-table-type-tab {
  display: flex;
  align-items: center;
  background-color: #f1f1f3;
  border-radius: 5px 5px 0 0;
  overflow: hidden;
  span {
    width: 50%;
    display: flex;
    justify-content: center;
    line-height: 40px;
    font-size: 14px;
    color: #999;
    border: 1px solid #f1f1f3;
    border-bottom: 0;
    cursor: pointer;
    &:hover,
    &.active {
      border-color: #cbd6e2;
      border-bottom: 0;
      border-radius: 5px 5px 0 0;
      transition: all 0.3s;
      background-color: #fff;
    }
  }
}
.custom-table-type-list {
  display: flex;
  flex-direction: column;
  padding: 2px 0;
  background-color: #fff;
}
.custom-table-type-item {
  display: inline-flex;
  font-size: 14px;
  line-height: 36px;
  padding: 0 20px;
  cursor: pointer;
  transition: all 0.3s;
  color: #666;
  &:hover,
  &.active {
    background-color: #2e73f3;
    color: #fff;
  }
}
</style>
