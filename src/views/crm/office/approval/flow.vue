<template>
  <div class="newBox bgcf9 vh-85">
    <!-- 搜索 -->
    <div class="custom-search crm-search">
      <el-form ref="queryParams" :model="queryParams" size="small" inline class="custom-form-inline"
        @submit.native.prevent>
        <el-form-item label="客户名称" prop="name">
          <el-input v-model="queryParams.name" placeholder="请输入客户名称" @change="handleQuery" clearable />
        </el-form-item>
        <el-form-item>
          <el-button icon="el-icon-search" type="primary" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
        <el-form-item>
          <el-popover ref="createPopover" trigger="click" popper-class="crm-popover" style="margin-left: 10px">
            <template slot="reference">
              <el-button type="primary">
                <i class="el-icon-plus"></i>
                新建审批
                <i class="el-icon-arrow-down"></i>
              </el-button>
            </template>
            <div class="crm-popover-more">
              <div class="crm-popover-more-item" v-for="item in options" :key="item.code">{{
                item.name }}</div>
            </div>
          </el-popover>
        </el-form-item>
      </el-form>
    </div>
    <!-- 内容区域 -->
    <div class="classify flex bgwhite">
      <div class="classify-item" :class="{ active: classify === 'init' }" @click="handleClassify('init')">发起流程</div>
      <!-- <div class="classify-item" :class="{ active: classify === 'record' }" @click="handleClassify('record')">流程记录</div> -->
    </div>
    <!-- 发起流程 -->
    <div class="crm-approval-flow">
      <el-card shadow="hover" class="crm-approval-flow-card">
        <div class="crm-approval-flow-title">常用</div>
        <div class="crm-approval-flow-list">
          <div class="crm-approval-flow-item" v-for="(item, index) in options.filter(it => it.mark === 1)" :key="index">
            <i class="icon ssfont" :class="fontEcho(item)"></i>
            <span class="title">{{ item.name }}</span>
          </div>
        </div>
      </el-card>
      <el-card shadow="hover" class="crm-approval-flow-card">
        <div class="crm-approval-flow-title">业务流程</div>
        <div class="crm-approval-flow-list">
          <div class="crm-approval-flow-item">
            <i class="icon ssfont ss-diy-zhangdan"></i>
            <span class="title">销售合同</span>
          </div>
        </div>
      </el-card>
      <el-card shadow="hover" class="crm-approval-flow-card">
        <div class="crm-approval-flow-title">
          <span>审批</span>
          <div class="flex align-center">
            <div class="create-config">
              <i class="el-icon-setting"></i>
              <span>自定义</span>
            </div>
            <div class="create-config" @click="handleCreateConfig">
              <i class="ssfont ss-diy-shenpi"></i>
              <span>审批设置</span>
            </div>
          </div>
        </div>
        <div class="crm-approval-flow-list">
          <div class="crm-approval-flow-item" v-for="(item, index) in options" :key="index">
            <i class="icon ssfont" :class="fontEcho(item)"></i>
            <span class="title">{{ item.name }}</span>
          </div>
        </div>
      </el-card>
    </div>
    <flow-center ref="flowCenter" @callBack="handleCallBack" v-if="showCenter" />
  </div>
</template>
<script>
import FlowCenter from './flow/center.vue'
import { getApprovalTemplateList } from '@/api/crm/approval'

export default {
  name: 'OfficeApprovalFlow',
  components: { FlowCenter },
  data() {
    return {
      queryParams: {
        name: '',
        type: ''
      },
      typeOptions: [
        { value: '', label: '全部流程' },
        { value: 'business', label: '业务流程' },
        { value: 'approval', label: '审批' },
        { value: 'automation', label: '自动化流程' }
      ],
      options: [],
      classify: 'init',
      showCenter: false,
    }
  },
  mounted() {
    this.getApprovalTemplateList()
  },
  methods: {
    // 获取审批模板列表
    async getApprovalTemplateList() {
      const { rows, code, msg } = await getApprovalTemplateList()
      if (code === 200) {
        this.options = rows
      } else {
        this.$message.error(msg)
      }
    },
    
    // 分类切换
    handleClassify(type) {
      this.classify = type
    },
    // 搜索
    handleQuery() {
      console.log('handleQuery')
    },
    // 重置
    resetQuery() {
      console.log('resetQuery')
    },
    // 回调
    handleCallBack() {
      console.log('handleCallBack')
    },
    // 创建配置
    handleCreateConfig() {
      this.showCenter = true
      this.$nextTick(() => {
        this.$refs.flowCenter.handleOpen()
      })
    },
    // font回显
    fontEcho(item) {
      if (item.name === '请假申请') {
        return 'ss-diy-datetime'        
      }
      if (item.name === '出差申请') {
        return 'ss-diy-feiji'        
      }
      if (item.name === '外勤审批') {
        return 'ss-diy-position'        
      }
      if (item.name === '报销审批') {
        return 'ss-diy-baoxiao'        
      }
      if (item.name === '收款审批') {
        return 'ss-diy-shoukuandan'        
      }
      if (item.name === '订单审批') {
        return 'ss-diy-dingdan1'        
      }
      if (item.name === '退款审批') {
        return 'ss-diy-tuikuanshenqing'        
      }
      if (item.name === '工作请示') {
        return 'ss-diy-order-completed'        
      }
      if (item.name === '客户拜访申请') {
        return 'ss-diy-kehu'        
      }
      if (item.name === '客户审批') {
        return 'ss-diy-shenpi'        
      }
      return 'ss-diy-datetime'
    }
  }
}
</script>
<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';
@import '~@/assets/styles/crm.scss';
</style>
