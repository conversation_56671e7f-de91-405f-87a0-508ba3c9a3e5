<template>
  <div>
    <el-dialog v-dialogDragBox :title="title" :visible.sync="open" width="1150px" :before-close="handleClose" class="custom-dialog">
      <div style="padding: 0 20px">
        <el-form ref="form" :model="form" :rules="rules" label-width="80px">
          <el-row :gutter="10">
            <el-col :span="12">
              <el-form-item label="审批流程" prop="approvalFlow">
                <el-select v-model="form.approvalFlow" placeholder="请选择审批流程" style="width: 100%" @change="getApprovalProcessDefList">
                  <el-option v-for="item in approvalFlowList" :key="item.code" :label="item.name" :value="item.code"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <div class="crm-flow-setting-title" style="margin-bottom: 20px">
            <el-button type="text" icon="el-icon-plus" @click="handleAddFlow">添加《{{ formactFlow(form.approvalFlow) }}》审批流程</el-button>
            <el-input v-model="form.approvalFlowName" size="small" placeholder="请输入流程名称" style="width: 360px">
              <template slot="append">
                <el-button type="text" icon="el-icon-search" size="small" style="background-color: #e1ebff; color: #2e73f3; padding-left: 10px; padding-right: 10px">搜索</el-button>
              </template>
            </el-input>
          </div>
          <el-table :data="form.dataList" stripe class="custom-table" style="width: 100%">
            <el-table-column prop="flowName" label="流程名称" align="center" />
            <el-table-column prop="flowType" label="流程类型" align="center" />
            <el-table-column prop="effectiveRange" label="生效范围" align="center" min-width="320px">
              <template slot-scope="scope">
                <span class="table-tag-title">指定部门</span>
                <span class="table-tag-item" v-for="item in scope.row.effectiveRange.split(',')" :key="item">{{ item }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="creator" label="创建人" align="center" />
            <el-table-column prop="createTime" label="创建时间" align="center" />
            <el-table-column label="操作" align="center" width="220px">
              <template slot-scope="scope">
                <div class="flex align-center justify-center">
                  <div class="crm-table-btn">
                    <i class="ssfont ss-diy-fuzhi"></i>
                    <span>复制</span>
                  </div>
                  <div class="crm-table-btn">
                    <i class="el-icon-edit-outline"></i>
                    <span>编辑</span>
                  </div>
                  <div class="crm-table-btn danger">
                    <i class="el-icon-delete"></i>
                    <span>删除</span>
                  </div>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </el-form>
        <div class="crm-flow-setting-tips flex-column">
          <div class="flex flex-column" style="width: 100%">
            <span>注：</span>
            <span>1、如果没有自定义流程，员工在录入的时候需要自行选择审批人</span>
            <span>2、如果一个部门或一个员工设置了多个流程，那么员工在提交审批时可以自行选择一个流程</span>
          </div>
        </div>
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn" @click="handleCancel()">取 消</el-button>
        <el-button class="custom-dialog-btn primary" @click="handleSubmit">确 定</el-button>
      </div>
    </el-dialog>
    <flow-create ref="flowCreate" @callBack="handleCallBack" v-if="openCreate"/>
  </div>
</template>
<script>
import FlowCreate from './create'
import { getApprovalProcessDefList, deletedApprovalProcessDef } from '@/api/crm/approval'

export default {
  name: 'FlowSetting',
  components: { FlowCreate },
  props: {
    activeCode: {
      type: String,
      default: ''
    },
    approvalFlowList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      title: '审批设置',
      open: false,
      form: {
        approvalFlow: '',
        dataList: []
      },
      rules: {},
      
      openCreate: false
    }
  },
  created() {
   
  },
  methods: {
    // 打开
    handleOpen() {
      this.open = true
      this.form.approvalFlow = this.activeCode || ''
      this.getApprovalProcessDefList()
    },
    // 流程名称
    formactFlow(value) {
      const flow = this.approvalFlowList.find(item => item.code == value)
      return flow?.name || ''
    },
    // 获取审批流程列表
    async getApprovalProcessDefList() {
      const { rows, code, msg } = await getApprovalProcessDefList({ approvalCode: this.form.approvalFlow })
      if (code === 200) {
        this.form.dataList = rows
      } else {
        this.$message.error(msg)
      }
    },
    // 关闭
    handleClose() {
      this.handleCancel()
    },
    // 取消
    handleCancel(flag = false) {
      this.open = false
      this.$emit('callBack', flag)
    },
    // 确定
    handleSubmit() {
      console.log('handleSubmit')
    },
    // 回调
    handleCallBack() {
      console.log('handleCallBack')
    },
    // 打开创建
    handleAddFlow() {
      this.openCreate = true
      this.$nextTick(() => {
        this.$refs.flowCreate.handleOpen()
      })
    }
  }
}
</script>
<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
@import '~@/assets/styles/crm.scss';
.table-tag-title {
  display: inline-block;
  font-size: 12px;
  color: #999;
}
.table-tag-item {
  display: inline-block;
  color: #2e73f3;
  font-size: 14px;
  line-height: 20px;
  padding: 6px 10px;
  background-color: #e5eeff;
  border-radius: 5px;
  margin-left: 10px;
  margin-bottom: 10px;
}
</style>
