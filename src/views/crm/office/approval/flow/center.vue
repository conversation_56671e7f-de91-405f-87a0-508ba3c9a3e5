<template>
  <div>
    <el-dialog v-dialogDragBox :title="title" :visible.sync="open" width="1150px" :before-close="handleClose" class="custom-dialog">
      <div style="padding: 0 20px">
        <el-tabs v-model="activeName" @tab-click="handleClick" type="card" class="crm-flow-setting-tabs">
          <el-tab-pane v-for="item in tabList" :key="item.code" :label="item.name" :name="item.code"></el-tab-pane>
        </el-tabs>
        <div class="crm-flow-setting">
          <div class="flex align-center crm-flow-setting-switch">
            <span>是否开启订单审批功能</span>
            <el-switch v-model="value1" />
          </div>
          <div class="crm-flow-setting-title">指定人员/部门订单需审批</div>
          <select-user ref="selectUser" />
          <div class="crm-flow-setting-title">
            <span>设置审批流程</span>
            <div class="create-config" @click="handleOpenSetting">
              <i class="el-icon-setting"></i>
              <span>自定义</span>
            </div>
          </div>
          <div class="crm-flow-setting-tips">
            <i class="ssfont ss-diy-question"></i>
            <span>如果没有自定义流程，员工在录入的时候需要自行选择审批人</span>
          </div>
        </div>
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn" @click="handleCancel()">取 消</el-button>
        <el-button class="custom-dialog-btn primary" @click="handleSubmit">确 定</el-button>
      </div>
    </el-dialog>
    <flow-setting ref="flowSetting" @callBack="handleCallBack" v-if="openSetting" :activeCode="activeName" :approvalFlowList="tabList"/>
  </div>
</template>
<script>
import FlowSetting from './setting'
import SelectUser from '@/views/crm/components/SelectUser'
import { getApprovalTemplateList } from '@/api/crm/approval'

export default {
  name: 'FlowCenter',
  components: { FlowSetting, SelectUser },
  data() {
    return {
      title: '审批设置',
      open: false,
      activeName: '',
      tabList: [
        // { name: 'customer', label: '客户审批' },
        // { name: 'visit', label: '客户拜访申请' },
        // { name: 'order', label: '订单审批' },
        // { name: 'receipt', label: '收款审批' },
        // { name: 'refund', label: '退款审批' },
        // { name: 'field', label: '外勤审批' },
        // { name: 'leave', label: '请假审批' },
        // { name: 'travel', label: '出差审批' },
        // { name: 'work', label: '工作请示审批' },
        // { name: 'reimburse', label: '报销审批' }
      ],
      value1: false,
      openSetting: false
    }
  },
  mounted() {
    this.getApprovalTemplateList()
  },
  methods: {
    // 获取审批模板列表
    async getApprovalTemplateList() {
      const { rows, code, msg } = await getApprovalTemplateList()
      if (code === 200) {
        this.tabList = rows
        this.activeName = rows[0].code
        console.log('rows', rows)
      } else {
        this.$message.error(msg)
      }
    },
    handleOpen() {
      this.open = true
    },
    // 点击
    handleClick(tab, event) {
      // this.activeName = tab.code
    },
    // 关闭
    handleClose() {
      this.handleCancel()
    },
    // 取消
    handleCancel(flag = false) {
      this.open = false
      this.$emit('callBack', flag)
    },
    // 确定
    handleSubmit() {
      console.log('handleSubmit')
    },
    // 回调
    handleCallBack() {
      console.log('handleCallBack')
    },
    // 打开设置
    handleOpenSetting() {
      this.openSetting = true
      this.$nextTick(() => {
        this.$refs.flowSetting.handleOpen()
      })
    }
  }
}
</script>
<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
@import '~@/assets/styles/crm.scss';
</style>
