<template>
  <div>
    <el-dialog v-dialogDragBox :title="title" :visible.sync="open" width="1150px" :before-close="handleClose"
      class="custom-dialog">
      <el-form :model="form" ref="form" :rules="rules" label-width="100px" label-position="left">
        <div style="padding: 0 20px">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="审批类型" prop="approvalType">
                <el-select v-model="form.approvalType" placeholder="请选择审批类型" style="width: 100%">
                  <el-option v-for="item in approvalTypeOptions" :key="item.code" :label="item.name"
                    :value="item.code"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="流程名称" prop="flowName">
                <el-input v-model="form.flowName" placeholder="请输入流程名称" />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="生效范围" prop="effectiveRange">
                <el-radio-group v-model="form.effectiveRange">
                  <el-radio label="all">全公司</el-radio>
                  <el-radio label="department">部门</el-radio>
                  <el-radio label="person">人员</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <div class="effectiveRangeInfo" :class="form.effectiveRange">
          <el-tag closable @close="handleClose(tag)" class="crm-tag">某某某</el-tag>
          <el-tag closable @close="handleClose(tag)" class="crm-tag">某某某</el-tag>
          <el-tag closable @close="handleClose(tag)" class="crm-tag">某某某</el-tag>
          <el-tag closable @close="handleClose(tag)" class="crm-tag">某某某</el-tag>
          <el-tag closable @close="handleClose(tag)" class="crm-tag">某某某</el-tag>
          <el-tag closable @close="handleClose(tag)" class="crm-tag">某某某</el-tag>
          <el-tag closable @close="handleClose(tag)" class="crm-tag">某某某</el-tag>
          <el-tag closable @close="handleClose(tag)" class="crm-tag">某某某</el-tag>
          <el-tag closable @close="handleClose(tag)" class="crm-tag">某某某</el-tag>
          <el-tag closable @close="handleClose(tag)" class="crm-tag">某某某</el-tag>
          <el-tag closable @close="handleClose(tag)" class="crm-tag">某某某</el-tag>
          <el-tag closable @close="handleClose(tag)" class="crm-tag">某某某</el-tag>
          <el-tag closable @close="handleClose(tag)" class="crm-tag">某某某</el-tag>
          <el-tag closable @close="handleClose(tag)" class="crm-tag">某某某</el-tag>
          <el-tag closable @close="handleClose(tag)" class="crm-tag">某某某</el-tag>
          <el-tag closable @close="handleClose(tag)" class="crm-tag">某某某</el-tag>
          <el-tag closable @close="handleClose(tag)" class="crm-tag">某某某</el-tag>
          <el-button type="text" icon="el-icon-plus" size="small" style="margin-left: 15px">选择人员</el-button>
        </div>
        <div style="padding: 0 20px">
          <el-form-item label="抄送给" prop="cc">
            <el-tag closable @close="handleCloseTag(item)" class="crm-tag border-none" v-for="item in form.cc"
              :key="item.userId">{{ item.realName || item.nickName
              }}</el-tag>
            <el-button type="text" icon="el-icon-plus" style="margin-left: 15px"
              @click="handleContactShow('cc', form.cc)">选择人员</el-button>
            <span style="margin-left: 15px; color: #2e73f3">|</span>
            <el-button type="text" style="margin-left: 15px">默认设置</el-button>
          </el-form-item>
          <div class="desc-title">流程顺序</div>
          <el-table :data="form.flowOrder" class="custom-table" style="width: 100%">
            <el-table-column type="index" label="步骤顺序" align="center" width="80" />
            <el-table-column prop="approver" label="审批人" align="center">
              <template slot-scope="scope">
                <div class="flex align-center">
                  <!-- <el-tag closable class="crm-tag border-none" v-for="item in scope.row.approver.split(',')"
                    :key="item">{{ item }}</el-tag> -->
                  <el-tag closable @close="handleCloseTag(item, scope.row.approver)" class="crm-tag border-none"
                    v-for="item in scope.row.approver" :key="item.userId">{{ item.realName || item.nickName
                    }}</el-tag>
                  <el-button type="text" icon="el-icon-plus" style="margin-left: 15px"
                    @click="handleContactShow('approver', scope.row.approver)">添加审批人</el-button>
                </div>
                <div class="table-condition">
                  <span class="title">完成条件</span>
                  <el-radio v-model="scope.row.conditionType" label="all" class="radio">
                    <span style="margin-right: 5px">全部完成</span>
                    <el-tooltip content="全部审批人完成审批，当前审批流程才算完成">
                      <i class="ssfont ss-diy-question"></i>
                    </el-tooltip>
                  </el-radio>
                  <el-radio v-model="scope.row.conditionType" label="majority" class="radio">
                    <span style="margin-right: 5px">任一完成</span>
                    <el-tooltip content="任一审批人完成审批，当前审批流程就算完成">
                      <i class="ssfont ss-diy-question"></i>
                    </el-tooltip>
                  </el-radio>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="cc" label="抄送给" align="center">
              <template slot="header">
                <div class="flex align-center flex-justify-center">
                  <span style="margin-right: 5px">抄送给</span>
                  <el-tooltip content="到达当前节点时，选择抄送至不同人员">
                    <i class="ssfont ss-diy-question"></i>
                  </el-tooltip>
                </div>
              </template>
              <template slot-scope="scope">
                <div class="flex align-center">
                  <!-- <el-tag closable class="crm-tag border-none" v-for="item in scope.row.cc.split(',')" :key="item">{{
                    item }}</el-tag> -->
                  <el-tag closable @close="handleCloseTag(item, scope.row.cc)" class="crm-tag border-none"
                    v-for="item in scope.row.cc" :key="item.userId">{{ item.realName || item.nickName
                    }}</el-tag>
                  <el-button type="text" icon="el-icon-plus" style="margin-left: 15px"
                    @click="handleContactShow('cc', scope.row.cc)">选择人员</el-button>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="allowModify" label="是否允许修改" align="center" width="120">
              <template slot="header">
                <div class="flex align-center flex-justify-center">
                  <span style="margin-right: 5px">是否允许修改</span>
                  <el-tooltip content="是否允许当前节点审批人修改审批内容">
                    <i class="ssfont ss-diy-question"></i>
                  </el-tooltip>
                </div>
              </template>
              <template slot-scope="scope">
                <div class="flex align-center flex-justify-center">
                  <el-switch v-model="scope.row.allowModify"></el-switch>
                  <span style="margin-left: 5px">{{ scope.row.allowModify ? '开启' : '关闭' }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="120">
              <template slot-scope="scope">
                <div class="flex align-center flex-justify-center">
                  <div class="crm-table-btn danger">
                    <i class="el-icon-delete"></i>
                    <span>删除</span>
                  </div>
                </div>
              </template>
            </el-table-column>
          </el-table>
          <div class="table-more">
            <el-button type="primary" icon="el-icon-plus">添加流程顺序</el-button>
          </div>
        </div>
      </el-form>
      <div class="crm-flow-setting-tips flex-column" style="padding: 0 20px">
        <div class="flex flex-column" style="width: 100%">
          <span>注：</span>
          <span>1、如果没有自定义流程，员工在录入的时候需要自行选择审批人</span>
          <span>2、如果一个部门或一个员工设置了多个流程，那么员工在提交审批时可以自行选择一个流程</span>
        </div>
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn" @click="handleCancel()">取 消</el-button>
        <el-button class="custom-dialog-btn primary" @click="handleSubmit">确 定</el-button>
      </div>
    </el-dialog>

    <contacts :title="contactsTitle" :type="contactsType" ref="contactsView" @update="handleUpdateContactsList($event)">
    </contacts>
  </div>
</template>

<script>
import contacts from '@/views/crm/customer/create/contacts'
import { getApprovalTemplateList } from '@/api/crm/approval'

export default {
  name: 'FlowCreate',
  components: { contacts },
  data() {
    return {
      title: '创建审批流程',
      open: false,
      form: {
        cc: [],
      },
      rules: {},
      approvalTypeOptions: [],
      contactsType: 'cc',
      contactsTitle: '抄送人',
      approvalsOptions: [],
      approvalsProps: {
        expandTrigger: 'hover',
        emitPath: false
      }
    }
  },
  created() {
    this.form.flowOrder = [
      {
        approver: [],
        cc: [],
        allowModify: false,
        conditionType: 'all'
      },
      {
        approver: [],
        cc: [],
        allowModify: false,
        conditionType: 'majority'
      },
      {
        approver: [],
        cc: [],
        allowModify: false,
        conditionType: 'majority'
      }
    ]
    this.getApprovalTemplateList()
  },
  methods: {
    // 获取审批模板列表
    async getApprovalTemplateList() {
      const { rows, code, msg } = await getApprovalTemplateList()
      if (code === 200) {
        console.log(rows, 'aaaa')
        this.approvalTypeOptions = rows
      } else {
        this.$message.error(msg)
      }
    },
    handleOpen() {
      this.open = true
    },
    handleClose() {
      this.handleCancel()
    },
    handleCancel(flag = false) {
      this.open = false
      this.$emit('callBack', flag)
    },
    handleSubmit() {
      console.log('handleSubmit')
    },
    handleContactShow(type, arr) {
      if (type == 'cc') {
        this.contactsTitle = '抄送人'
        this.contactsType = 'cc'
      } else if (type == 'approver') {
        this.contactsTitle = '审批人'
        this.contactsType = 'approver'
      }
      let checkList = []
      arr.forEach(el => {
        checkList.push(el.userId)
      })
      console.log(checkList)
      this.$refs.contactsView.handleOpen(checkList)
    },
    handleUpdateContactsList(data) {
      // console.log(data)
      let type = data && data[0].typesOf
      if (type === 'cc') {
        this.form.cc = this.form.cc.filter(item => item.typesOf !== 'cc').concat(data)
      }
      if (type === 'approver') {
        this.form.flowOrder.forEach(item => {
          item.approver = item.approver.filter(i => i.typesOf !== 'approver').concat(data)
        })
      }
    },
    handleCloseTag(item, arr) {
      if (!arr) {
        this.form.cc.splice(
          this.form.cc.findIndex(i => i.typesOf === item.typesOf && i.userId == item.userId),
          1
        )
      } else {
        arr.splice(
          arr.findIndex(i => i.typesOf === item.typesOf && i.userId == item.userId),
          1
        )
      }
    }
  }
}
</script>
<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';
@import '~@/assets/styles/crm.scss';

.effectiveRangeInfo {
  margin-top: -20px;
  margin-bottom: 20px;
  background-color: #f0f3f9;
  padding: 12px 20px 20px 120px;
  position: relative;

  .crm-tag {
    display: inline-block;
    margin-top: 8px;
  }

  .crm-tag+.crm-tag {
    margin-left: 10px;
  }

  .el-button {
    margin-top: 8px;
  }

  &::before {
    content: '';
    display: inline-block;
    width: 0;
    height: 0;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-bottom: 6px solid #f0f3f9;
    position: absolute;
    top: -6px;
  }

  &.department {
    &::before {
      left: 250px;
    }
  }

  &.person {
    &::before {
      left: 330px;
    }
  }
}

.desc-title {
  font-weight: 500;
  font-size: 14px;
  color: #666;
  margin-bottom: 10px;
}

.table-condition {
  display: flex;
  align-items: center;
  padding-top: 10px;

  .title {
    font-size: 12px;
    color: #999;
    line-height: 16px;
    margin-right: 10px;
  }

  .radio {
    display: flex;
    align-items: center;

    ::v-deep {
      .el-radio__input {
        display: flex !important;
        align-items: center !important;
        line-height: 16px !important;
      }
    }
  }
}

.table-more {
  padding: 20px 0;
  border-bottom: 1px solid #e2e6f3;
}
</style>
