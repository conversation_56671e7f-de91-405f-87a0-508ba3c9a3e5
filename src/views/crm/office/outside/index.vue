<template>
  <div class="newBox bgcf9 vh-85">
    <!-- 搜索 -->
    <div class="custom-search crm-search">
      <el-form ref="queryParams" :model="queryParams" size="small" inline class="custom-form-inline"
        @submit.native.prevent>
        <el-form-item label="客户名称" prop="name">
          <el-input v-model="queryParams.name" placeholder="请输入客户名称" @change="handleQuery" clearable />
        </el-form-item>
        <el-form-item>
          <el-button icon="el-icon-search" type="primary" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
        <el-form-item label="">
          <popover-user ref="popoverUser" @selectUser="handleSelectUser" />
        </el-form-item>
        <el-form-item label="">
          <el-date-picker v-model="queryParams.date" type="daterange" range-separator="至" start-placeholder="开始日期"
            end-placeholder="结束日期" format="yyyy-MM-dd" value-format="yyyy-MM-dd" @change="handleDateChange"
            size="small"></el-date-picker>
        </el-form-item>
      </el-form>
    </div>
    <!-- 分类 -->
    <div class="classify flex bgwhite">
      <div class="classify-item" :class="{ active: item.value === active }" v-for="item in Options" :key="item.value"
        @click="handleCategory(item)">{{ item.label }}</div>
      <!-- <div class="flex align-center">
      </div> -->
    </div>
    <div style="padding: 15px 20px">
      <template v-if="total > 0">
        <el-table v-loading="loading" ref="table" stripe :data="list" style="width: 100%" class="custom-table">
          <el-table-column align="center" type="index" label="序号" width="50"></el-table-column>
          <el-table-column label="签到地点" align="center" show-overflow-tooltip>
            <template slot-scope="{ row }">
              <div>{{ row.variables.parameters.address }}</div>
            </template>
          </el-table-column>
          <el-table-column label="客户" align="center" show-overflow-tooltip>
            <template slot-scope="{ row }">
              <div>{{ row.variables.copyUsers }}</div>
            </template>
          </el-table-column>
          <el-table-column label="签到时间" align="center">
            <template slot-scope="{ row }">
              <div>{{ row.variables.parameters.createTime }}</div>
            </template>
          </el-table-column>
          <el-table-column label="签到人" align="center">
            <template slot-scope="{ row }">
              <div>{{ row.variables.createBy }}</div>
            </template>
          </el-table-column>
          <el-table-column label="备注说明" align="center" show-overflow-tooltip>
            <template slot-scope="{ row }">
              <div>{{ row.variables.parameters.explain }}</div>
            </template>
          </el-table-column>
          <el-table-column align="center" label="操作" width="220px">
            <template slot-scope="{ row }">
              <el-button class="table-btn" @click="handleDetail(row)">查看详情</el-button>
              <el-button class="table-btn danger" @click="handleDelete(row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="custom-pagination">
          <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize" @pagination="getAllTask(queryParams)" />
        </div>
      </template>
      <el-empty :description="!loading && !total ? '暂无数据' : '加载中…'" v-else />
    </div>
    <!-- 详情 -->
    <detail ref="detail" @callBack="hadleCallBack" v-if="detailShow" />
  </div>
</template>
<script>
import detail from './detail'
import PopoverUser from '@/views/crm/components/PopoverUser/index.vue'
import { getApprovalTemplateList, getAllTask, putApproveDeleteTask, getApprovalCopyTask, getApprovalHistoryTaskForMe, getApprovalOwnerTask, getApprovalTaskList } from '@/api/crm/approval'

export default {
  name: 'OfficeOutside',
  components: { detail, PopoverUser },
  data() {
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        currentUser: '',
        code: '',
      },
      active: '1',
      Options: [
        { value: '1', label: '全部' },
        { value: '2', label: '待办' },
        { value: '3', label: '已办' },
        { value: '4', label: '创建' },
        { value: '5', label: '抄送' },
      ],
      list: [],
      total: 0,
      loading: false,
      detailShow: false,
    }
  },
  created() {
    this.getApprovalTemplateList()
  },
  methods: {
    // 获取审批模板列表
    async getApprovalTemplateList() {
      const res = await getApprovalTemplateList({
        templateName: '外勤审批',
      })
      if (res.code !== 200) {
        this.$message.error(res.msg)
        return
      } else {
        this.queryParams.code = res.rows[0].code
        this.getAllTask(this.queryParams)
      }
    },
    // 获取所有任务
    async getAllTask(params) {
      this.loading = true
      if (this.active === '1') {
        const res = await getAllTask(params)
        this.list = res.rows
        this.total = res.total
        this.loading = false
      } else if (this.active === '2') {
        const res = await getApprovalTaskList(params)
        this.list = res.rows
        this.total = res.total
        this.loading = false
      } else if (this.active === '3') {
        const res = await getApprovalHistoryTaskForMe(params)
        this.list = res.rows
        this.total = res.total
        this.loading = false
      } else if (this.active === '4') {
        const res = await getApprovalOwnerTask(params)
        this.list = res.rows
        this.total = res.total
        this.loading = false
      } else if (this.active === '5') {
        const res = await getApprovalCopyTask(params)
        this.list = res.rows
        this.total = res.total
        this.loading = false
      }
    },
    // tab切换
    handleCategory(item) {
      this.active = item.value
      this.queryParams.pageNum = 1
      this.handleQuery()
    },

    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getAllTask(this.queryParams)
    },
    // 重置搜索
    resetQuery() {
      this.resetForm('queryParams')
      this.getAllTask(this.queryParams)
    },
    // 日期选择
    handleDateChange(date) {
      this.queryParams.date = date
      this.handleQuery()
    },
    // 详情
    handleDetail(row) {
      this.detailShow = true
      this.$nextTick(() => {
        this.$refs.detail.handleOpen(row)
      })
    },
    // 回调
    hadleCallBack(flag) {
      this.detailShow = false
      if (flag) this.getAllTask(this.queryParams)
    },
    // 选择人员
    handleSelectUser(item) {
      this.queryParams.checkInPerson = item.name
      this.handleQuery()
    },
    // 删除
    handleDelete(row) {
      this.$confirm('是否删除该条外勤签到记录？', '提示', {
        type: 'warning',
        showCancelButton: true,
        cancelButtonText: '取消',
        confirmButtonText: '确定'
      }).then(() => {
        putApproveDeleteTask({
          processInstanceId: row.processInstanceId,
        })
          .then(res => {
            if (res.code !== 200) {
              this.$message.error(res.msg)
              return
            }
            this.$message.success('删除成功')
            this.getAllTask(this.queryParams)
          })
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },

  }
}
</script>
<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
@import '~@/assets/styles/crm.scss';
</style>
