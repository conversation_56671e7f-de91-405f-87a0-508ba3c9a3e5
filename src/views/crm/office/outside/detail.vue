<template>
  <div>
    <el-dialog v-dialogDragBox :visible.sync="open" title="详情" width="1150px" :before-close="handleClose" class="custom-dialog">
      <div style="padding: 0 20px">
        <div class="info">
          <div class="info-item">
            <span class="label">客户：</span>
            <span class="value link">{{ data.variables.copyUsers }}</span>
          </div>
          <div class="info-item">
            <span class="label">签到人：</span>
            <span class="value">{{ data.variables.createBy }}</span>
          </div>
          <div class="info-item info-item-long">
            <span class="label">签到地点：</span>
            <span class="value">{{ data.variables.parameters.address }}</span>
          </div>
          <div class="info-item info-item-long">
            <span class="label">签到时间：</span>
            <span class="value">{{ data.variables.createTime }}</span>
          </div>
        </div>
        <div class="remark-title">备注说明</div>
        <div class="remark-item">
          <span class="label">详细</span>
          <span class="value">{{ data.variables.parameters.explain }}</span>
        </div>
        <!-- <div class="remark-item">
          <span class="label">收款凭证</span>
          <el-image style="width: 145px; height: 100px" :src="data.remarkImg" class="img"></el-image>
        </div> -->
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn" @click="handleCancel()">取 消</el-button>
        <el-button class="custom-dialog-btn primary" @click="handleSubmit">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
export default {
  name: 'OfficeOutsideDetail',
  data() {
    return {
      open: false,
      data: {}
    }
  },
  methods: {
    // 打开
    handleOpen(data) {
      this.open = true
      console.log(data)
      this.data = data
    },
    // 关闭
    handleClose() {
      this.handleCancel()
    },
    // 取消
    handleCancel(flag = false) {
      this.open = false
      this.$emit('callBack', flag)
    },
    // 确定
    handleSubmit() {
      console.log(this.data)
    }
  }
}
</script>
<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
@import '~@/assets/styles/crm.scss';
.info {
  padding: 20px;
  border: 1px solid #cbd6e2;
  border-radius: 5px;
  display: flex;
  flex-wrap: wrap;
  gap: 40px;
  &-item {
    display: flex;
    align-items: center;
    width: calc(50% - 20px);
    &-long {
      width: 100%;
    }
    .label {
      width: 70px;
      text-align: left;
      font-size: 12px;
      color: #999999;
    }
    .value {
      font-weight: 500;
      font-size: 14px;
      color: #333333;
      &.link {
        color: #2e73f3;
        cursor: pointer;
        &:hover {
          text-decoration: underline;
        }
      }
    }
  }
}
.remark {
  &-title {
    font-size: 14px;
    color: #666;
    line-height: 30px;
    border-bottom: 1px solid #e1e2e2;
    padding-top: 10px;
  }
  &-item {
    display: flex;
    line-height: 20px;
    padding: 10px 0;
    .label {
      width: 70px;
      text-align: left;
      font-size: 12px;
      color: #999999;
    }
    .value {
      font-size: 14px;
      color: #333333;
    }
    .img {
      width: 145px;
      height: 100px;
      border-radius: 5px;
    }
  }
}
</style>
