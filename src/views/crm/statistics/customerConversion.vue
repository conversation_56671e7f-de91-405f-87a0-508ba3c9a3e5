<template>
    <div class="newBox vh-85">
        <!-- 搜索 -->
        <div class="custom-search crm-search">
            <el-form ref="queryParams" :model="queryParams" size="small" inline class="custom-form-inline"
                @submit.native.prevent>
                <el-form-item label="客户名称" prop="name">
                    <el-input v-model="queryParams.name" placeholder="请输入客户名称" @change="handleQuery" clearable />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="handleQuery">搜索</el-button>
                    <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
                </el-form-item>
            </el-form>
        </div>
        <!-- 分类 -->
        <div class="classify flex bgwhite">
            <div class="classify-item" :class="{ active: item.value === activeType }" v-for="item in typeOptions"
                :key="item.value" @click="handleType(item)">{{ item.label }}</div>
        </div>
        <time-line v-if="activeType === 'timeLine'"></time-line>
        <ranking v-if="activeType === 'ranking'"></ranking>
        <retention v-if="activeType === 'retention'"></retention>
    </div>
</template>
<script>
import { listDeptExcludeChild } from '@/api/system/dept'
import timeLine from './timeLine.vue'
import ranking from './ranking.vue'
import retention from './retention.vue'

export default {
    name: 'performanceRanking',
    components: { timeLine, ranking, retention },
    data() {
        return {
            queryParams: {
                name: ''
            },
            activeType: 'timeLine',
            typeOptions: [
                { label: '按时间统计', value: 'timeLine' },
                { label: '排行榜', value: 'ranking' },
                { label: '客户留存分析', value: 'retention' },
            ],
            activeDate: 'month',
            dateOptions: [
                { label: '无时间限制', value: 'unlimited' },
                { label: '按天统计', value: 'day' },
                { label: '按周统计', value: 'week' },
                { label: '按月统计', value: 'month' },
                { label: '按季度统计', value: 'quarter' },
                { label: '按年统计', value: 'year' },
                { label: '自定义时间范围', value: 'custom' }
            ],
            activeBase: 'enter',
            baseOptions: [
                { label: '录入人', value: 'enter' },
                { label: '原负责人', value: 'originalResponsible' },
                { label: '转交人', value: 'passOn' },
                { label: '负责人', value: 'responsibility' },
                { label: '首次跟进人', value: 'firstFollow' },
                { label: '最后跟进人', value: 'lastFollow' },
                { label: '参与人', value: 'participant' },
                { label: '参与添加人', value: 'participantAdd' },

            ],
            activeDept: '',
            deptOptions: [],
            timing: '',
            moldOptions: [
                { label: '客户状态', value: 'status' },
                { label: '成熟度', value: 'maturity' },
                { label: '客户等级', value: 'grade' },
            ],
            activeMold: 'status',
            statusOptions: [
                { label: '成熟客户', value: 'cs' },
                { label: '普通客户', value: 'pt' },
                { label: '潜在客户', value: 'qz' },
                { label: '潜在成交客户', value: 'qzcj' },
                { label: '成交待增长客户', value: 'cjdzz' },
                { label: '市场调货客户', value: 'scdh' },
                { label: '流失客户', value: 'ls' },
                { label: '衰退客户', value: 'st' },
                { label: '信用不良客户', value: 'xybl' },
                { label: '未分类', value: 'wfl' },
                { label: '不限', value: 'bx' },
            ],
            activeStatus: 'cs',

        }
    },
    created() {
        // 获取部门列表
        this.getDeptOptions()
        console.log(this.$store.state.user.info.deptId)
    },
    methods: {
        // 获取部门列表
        async getDeptOptions() {
            const { data } = await listDeptExcludeChild(this.$store.state.user.info.deptId)
            if (data.length === 0) return
            this.deptOptions = data
        },
        handleQuery() {
            console.log(this.queryParams)
        },
        resetQuery() {
            this.queryParams = {}
            this.handleQuery()
        },
        // 切换分类
        handleType(item) {
            this.activeType = item.value
        },
        // 选择成员
        handleSelectUser(user) {
            console.log(user)
        },
    }
}
</script>
<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
@import '~@/assets/styles/crm-statistics.scss';

.custom-search {
    padding-top: 10px;
    padding-bottom: 0;
}
</style>