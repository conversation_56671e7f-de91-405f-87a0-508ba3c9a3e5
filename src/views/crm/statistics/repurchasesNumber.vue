<template>
    <div class="newBox vh-85">
        <!-- 搜索 -->
        <div class="custom-search crm-search">
            <el-form ref="queryParams" :model="queryParams" size="small" inline class="custom-form-inline"
                @submit.native.prevent>
                <el-form-item label="客户名称" prop="name">
                    <el-input v-model="queryParams.name" placeholder="请输入客户名称" @change="handleQuery" clearable />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="handleQuery">搜索</el-button>
                    <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
                </el-form-item>
            </el-form>
        </div>
        <!-- 分类 -->
        <div class="classify flex bgwhite">
            <div class="classify-item" :class="{ active: item.value === activeType }" v-for="item in typeOptions"
                :key="item.value" @click="handleType(item)">{{ item.label }}</div>
        </div>
        <repeat-customers-time-line v-if="activeType === 'repeatCustomersTimeLine'"></repeat-customers-time-line>
        <repeat-customers-ranking v-if="activeType === 'repeatCustomersRanking'"></repeat-customers-ranking>
        <repeat-products-time-line v-if="activeType === 'repeatProductsTimeLine'"></repeat-products-time-line>
        <repeat-products-ranking v-if="activeType === 'repeatProductsRanking'"></repeat-products-ranking>

    </div>
</template>
<script>
import { listDeptExcludeChild } from '@/api/system/dept'
import repeatCustomersTimeLine from './repeatCustomersTimeLine.vue'
import repeatCustomersRanking from './repeatCustomersRanking.vue'
import repeatProductsTimeLine from './repeatProductsTimeLine.vue'
import repeatProductsRanking from './repeatProductsRanking.vue'


export default {
    name: 'performanceRanking',
    components: { repeatCustomersTimeLine, repeatCustomersRanking, repeatProductsTimeLine, repeatProductsRanking },
    data() {
        return {
            queryParams: {
                name: ''
            },
            activeType: 'repeatCustomersTimeLine',
            typeOptions: [
                { label: '复购客户数统计', value: 'repeatCustomersTimeLine' },
                { label: '复购客户数排行榜', value: 'repeatCustomersRanking' },
                { label: '复购产品数统计', value: 'repeatProductsTimeLine' },
                { label: '复购产品数排行榜', value: 'repeatProductsRanking' },
            ],
            activeDept: '',
            deptOptions: [],
            timing: '',
            moldOptions: [
                { label: '客户状态', value: 'status' },
                { label: '成熟度', value: 'maturity' },
                { label: '客户等级', value: 'grade' },
            ],
            activeMold: 'status',
            statusOptions: [
                { label: '成熟客户', value: 'cs' },
                { label: '普通客户', value: 'pt' },
                { label: '潜在客户', value: 'qz' },
                { label: '潜在成交客户', value: 'qzcj' },
                { label: '成交待增长客户', value: 'cjdzz' },
                { label: '市场调货客户', value: 'scdh' },
                { label: '流失客户', value: 'ls' },
                { label: '衰退客户', value: 'st' },
                { label: '信用不良客户', value: 'xybl' },
                { label: '未分类', value: 'wfl' },
                { label: '不限', value: 'bx' },
            ],
            activeStatus: 'cs',

        }
    },
    created() {
        // 获取部门列表
        this.getDeptOptions()
        console.log(this.$store.state.user.info.deptId)
    },
    methods: {
        // 获取部门列表
        async getDeptOptions() {
            const { data } = await listDeptExcludeChild(this.$store.state.user.info.deptId)
            if (data.length === 0) return
            this.deptOptions = data
        },
        handleQuery() {
            console.log(this.queryParams)
        },
        resetQuery() {
            this.queryParams = {}
            this.handleQuery()
        },
        // 切换分类
        handleType(item) {
            this.activeType = item.value
        },
        // 选择成员
        handleSelectUser(user) {
            console.log(user)
        },
    }
}
</script>
<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
@import '~@/assets/styles/crm-statistics.scss';

.custom-search {
    padding-top: 10px;
    padding-bottom: 0;
}
</style>