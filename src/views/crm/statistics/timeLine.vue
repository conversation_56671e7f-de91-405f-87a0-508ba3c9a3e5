<template>
    <div class="statistics-container">
        <div class="search_criteria" style="background-color: #FFFFFF; padding: 0 15px;">
            <popover-user ref="popoverUser" @selectUser="handleSelectUser" style="width: 138px" />
            <el-select v-model="activeMold" placeholder="请选择" size="small" style="margin-left: 15px; width: 138px">
                <el-option :label="item.label" :value="item.value" v-for="item in moldOptions"
                    :key="item.value"></el-option>
            </el-select>
            <span style="margin: 0 10px; font-weight: 400; font-size: 12px; color: #999999; line-height: 20px;">从</span>
            <el-select v-model="activeStatus" placeholder="请选择" size="small" style="width: 138px">
                <el-option :label="item.label" :value="item.value" v-for="item in statusOptions"
                    :key="item.value"></el-option>
            </el-select>
            <span
                style="margin: 0 10px; font-weight: 400; font-size: 12px; color: #999999; line-height: 20px;">转化为</span>
            <el-select v-model="activeStatus" placeholder="请选择" size="small" style="width: 138px">
                <el-option :label="item.label" :value="item.value" v-for="item in statusOptions"
                    :key="item.value"></el-option>
            </el-select>
            <el-date-picker v-model="timing" v-if="activeName == 'moon'" style="margin-left: 15px;" type="year" size="small" format="yyyy"
                value-format="yyyy" placeholder="选择年"></el-date-picker>
                <el-date-picker v-model="timing" v-if="activeName === 'day'" type="month" size="small"
                            format="yyyy-MM" value-format="yyyy-MM" placeholder="选择月"></el-date-picker>
        </div>
        <div class="statistics-card">
            <div class="statistics-card-header">
                <el-tabs v-model="activeName" type="card" @tab-click="handleClick" class="statistics-card-tabs">
                    <el-tab-pane v-for="item in orderOptions" :key="item.value" :label="item.label"
                        :name="item.value"></el-tab-pane>
                </el-tabs>
            </div>
            <div class="statistics-card-orderChart">
                <div class="statistics-card-orderChart-header">
                    <div class="statistics-card-orderChart-header-type">
                        <span class="statistics-card-orderChart-header-type-item"
                            :class="{ active: chartType === 'bar' }" @click="handleChartType('bar')">柱状图</span>
                        <span class="statistics-card-orderChart-header-type-item"
                            :class="{ active: chartType === 'line' }" @click="handleChartType('line')">折线图</span>
                    </div>
                    <div class="statistics-card-orderChart-header-title"
                        style="display: flex; align-items: center; flex-direction: column;">
                        <div style="margin-bottom: 16px;">2025年《客户状态 从 不限 转化为 成熟客户》共93个</div>
                    </div>
                    <div class="flex align-center">
                        <!-- <span class="statistics-card-orderChart-header-tips">新增客户数量</span> -->
                    </div>
                </div>
                <div class="statistics-card-orderChart-content">
                    <line-chart :chart-data="chartData" height="400px" :theme="theme" v-if="chartType === 'line'"
                        :key="key" />
                    <line-chart :chart-data="chartData" series-type="bar" height="400px" :theme="theme"
                        v-if="chartType === 'bar'" :key="key" />
                </div>
            </div>
        </div>

    </div>
</template>

<script>
import PopoverUser from '@/views/crm/components/PopoverUser/index'
import LineChart from '@/views/crm/components/dashboard/LineChart'
export default {
    name: 'TimeLineChart',
    components: {
        PopoverUser,
        LineChart
    },
    props: {
        // 组件接收的属性
        theme: {
            type: String,
            default: 'light'
        },
    },
    watch: {
        theme(newVal) {
            this.$set(this.chartData, 'theme', newVal);
        }
    },
    data() {
        return {
            // 组件数据
            chartType: 'line',
            chartData: {
                xAxis: [],
                yAxis: [],
                tooltipTitle: '新增客户数量',
                smooth: false,
            },
            key: 1,
            orderOptions: [
                { label: '按月统计', value: 'moon' },
                { label: '按日统计', value: 'day' },
            ],
            activeName: 'moon',
            timing: '',
            moldOptions: [
                { label: '客户状态', value: 'status' },
                { label: '成熟度', value: 'maturity' },
                { label: '客户等级', value: 'grade' },
            ],
            activeMold: 'status',
            statusOptions: [
                { label: '成熟客户', value: 'cs' },
                { label: '普通客户', value: 'pt' },
                { label: '潜在客户', value: 'qz' },
                { label: '潜在成交客户', value: 'qzcj' },
                { label: '成交待增长客户', value: 'cjdzz' },
                { label: '市场调货客户', value: 'scdh' },
                { label: '流失客户', value: 'ls' },
                { label: '衰退客户', value: 'st' },
                { label: '信用不良客户', value: 'xybl' },
                { label: '未分类', value: 'wfl' },
                { label: '不限', value: 'bx' },
            ],
            activeStatus: 'cs',
        };  
    },
    methods: {
        // 组件方法
        // 切换tab
        handleClick(tab, event) {
            console.log(tab, event)
            this.activeName = tab.name
        },
        // 选择成员
        handleSelectUser(user) {
            console.log(user)
        },
        // 切换图表类型
        handleChartType(type) {
            this.chartType = type
            this.key = Math.random()
        },
    },
    created() {
        // 组件创建后执行的代码
        this.$set(this, 'chartData', {
            xAxis: ['01-01', '01-02', '01-03', '01-04', '01-05', '01-06', '01-07', '01-08', '01-09', '01-10', '01-11', '01-12', '01-13', '01-14', '01-15', '01-16', '01-17', '01-18', '01-19', '01-20', '01-21'],
            yAxis: [60, 80, 30, 50, 40, 60, 70, 90, 80, 100, 120, 100, 130, 150, 150, 190, 170, 180, 185, 181, 173],
            tooltipTitle: '新增客户数量',
            smooth: false,
        })
    }
};
</script>

<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
@import '~@/assets/styles/crm-statistics.scss';

.statistics-container {
    .statistics-card {
        margin-bottom: 20px;

    }
}
</style>