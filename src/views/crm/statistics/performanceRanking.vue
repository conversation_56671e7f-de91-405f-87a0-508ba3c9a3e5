<template>
    <div class="newBox vh-85">
        <!-- 搜索 -->
        <!-- <div class="custom-search crm-search">
            <el-form ref="queryParams" :model="queryParams" size="small" inline class="custom-form-inline"
                @submit.native.prevent>
                <el-form-item label="客户名称" prop="name">
                    <el-input v-model="queryParams.name" placeholder="请输入客户名称" @change="handleQuery" clearable />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="handleQuery">搜索</el-button>
                    <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
                </el-form-item>
            </el-form>
        </div> -->
        <!-- 分类 -->
        <div class="classify flex bgwhite">
            <div class="classify-item" :class="{ active: item.value === activeType }" v-for="item in typeOptions"
                :key="item.value" @click="handleType(item)">{{ item.label }}</div>
        </div>
        <div class="statistics-container">
            <div class="search_criteria">
                <div class="search_criteria_left">
                    <el-select v-model="activeDate" placeholder="请选择" size="small"
                        style="margin-left: 15px; width: 138px">
                        <el-option :label="item.label" :value="item.value" v-for="item in dateOptions"
                            :key="item.value"></el-option>
                    </el-select>
                </div>
                <div class="search_criteria_right">
                    <div class="right_item" v-if="activeDate !== 0">
                        <span class="title">选择时间：</span>
                        <el-date-picker v-model="timing" v-if="activeDate === 1" type="date" size="small"
                            format="yyyy-MM-dd" value-format="yyyy-MM-dd" placeholder="选择日"></el-date-picker>
                        <el-date-picker v-model="timing" v-if="activeDate === 2" type="week" size="small"
                            format="yyyy 第 WW 周" placeholder="选择周"></el-date-picker>
                        <el-date-picker v-model="timing" v-if="activeDate === 3" type="month" size="small"
                            format="yyyy-MM" value-format="yyyy-MM" placeholder="选择月"></el-date-picker>
                        <el-quarter-picker v-model="timing" v-if="activeDate === 4" placeholder="选择季度"
                            size="small" />
                        <el-date-picker v-model="timing" v-if="activeDate === 5" type="year" size="small"
                            format="yyyy" value-format="yyyy" placeholder="选择年"></el-date-picker>
                        <el-date-picker v-model="timing" v-if="activeDate === 6" type="daterange" size="small"
                            range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                            :default-time="['00:00:00', '23:59:59']" :picker-options="{
                                disabledDate(time) {
                                    return time.getTime() > Date.now()
                                }
                            }">
                        </el-date-picker>

                    </div>
                    <div class="right_item">
                        <el-select v-model="activeStaff" placeholder="请选择" size="small"
                            style="margin-left: 15px; width: 200px">
                            <el-option :label="item.label" :value="item.value" v-for="item in staffOptions"
                                :key="item.value"></el-option>
                        </el-select>
                    </div>
                    <div class="right_item">
                        <span class="title">统计根据：</span>
                        <el-select v-model="activeBase" placeholder="请选择" size="small"
                            style="margin-left: 15px; width: 200px">
                            <el-option :label="item.label" :value="item.value" v-for="item in baseOptions"
                                :key="item.value"></el-option>
                        </el-select>
                    </div>
                    <div class="right_item">
                        <span class="title">指定部门：</span>
                        <el-select v-model="activeDept" placeholder="请选择" size="small"
                            style="margin-left: 15px; width: 200px">
                            <el-option :label="item.deptName" :value="item.deptId" v-for="item in deptOptions"
                                :key="item.deptId"></el-option>
                        </el-select>
                        <!-- <popover-user ref="popoverUser" @selectUser="handleSelectUser" style="width: 150px" /> -->
                    </div>
                </div>
            </div>
            <div class="statistics_table">
                <el-table :data="list" stripe size="small" style="width: 100%">
                    <el-table-column type="index" label="序号" width="50px"></el-table-column>
                    <el-table-column prop="staffName" label="人员" width="200px"></el-table-column>
                    <el-table-column prop="spec" label="所属部门" width="200px"></el-table-column>
                    <el-table-column prop="amount" label="跟进次数" width="200px"></el-table-column>
                    <el-table-column prop="percentage" label="比例（总成交量占比）">
                        <template slot-scope="{ row }">
                            <el-progress :percentage="formatNumber(row.amount)" :text-inside="true" text-color="#fff"
                                :stroke-width="30" color="#2e73f3" :define-back-color="'#dee7f9'" />
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <!-- <div class="custom-pagination">
                <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
                    :limit.sync="queryParams.pageSize" @pagination="getList" />
            </div> -->
        </div>

    </div>
</template>
<script>
import PopoverUser from '@/views/crm/components/PopoverUser/index'
import ElQuarterPicker from '@/views/crm/components/ElQuarterPicker/index'
import { listDeptExcludeChild } from '@/api/system/dept'
import { getStatisticsPerformanceWithCustomer, getStatisticsPerformanceWithOther } from '@/api/crm/statistics'

export default {
    name: 'performanceRanking',
    components: { PopoverUser, ElQuarterPicker, },
    data() {
        return {
            queryParams: {
                name: ''
            },
            activeType: 0, // 默认按新增客户数统计
            typeOptions: [
                { label: '新增客户数', value: 0 },
                { label: '跟进次数', value: 1 },
                { label: '成交次数', value: 2 },
                { label: '成交总额', value: 3 },
                { label: '成交产品总数量', value: 4 },
                { label: '回款总数', value: 5 },
                { label: '欠款', value: 6 },
            ],
            activeDate: 0, // 默认按月统计
            // 时间选项列表
            dateOptions: [
                { label: '无时间限制', value: 0 },
                { label: '按天统计', value: 1 },
                { label: '按周统计', value: 2 },
                { label: '按月统计', value: 3 },
                { label: '按季度统计', value: 4 },
                { label: '按年统计', value: 5 },
                { label: '自定义时间范围', value: 6 }
            ],
            activeStaff: 0,
            staffOptions: [
                { label: '人员排行', value: 0 },
                { label: '部门排行', value: 1 },
            ],
            activeBase: 0,
            baseOptions: [
                { label: '录入人', value: 0 },
                // { label: '转交人', value: 1 },
                { label: '负责人', value: 1 },
                // { label: '首次跟进人', value: 3 },
                // { label: '最后跟进人', value: 4 },
                { label: '参与人', value: 2 },

            ],
            activeDept: '',
            deptOptions: [],
            timing: '',
            list: [],

        }
    },
    mounted() {
        // this.list = [
        //     { id: 1, productName: '扁钢', spec: 'TN41*41*2.0*6', amount: 40849200 },
        //     { id: 2, productName: '光伏地桩', spec: 'TN41*41*2.0*6', amount: 38430000 },
        //     { id: 3, productName: '扁钢', spec: 'TN41*41*2.0*6', amount: 40849200 },
        //     { id: 4, productName: '光伏地桩', spec: 'TN41*41*2.0*6', amount: 38430000 },
        //     { id: 5, productName: '扁钢', spec: 'TN41*41*2.0*6', amount: 40849200 },
        //     { id: 6, productName: '光伏地桩', spec: 'TN41*41*2.0*6', amount: 38430000 },
        //     { id: 7, productName: '扁钢', spec: 'TN41*41*2.0*6', amount: 40849200 },
        //     { id: 8, productName: '光伏地桩', spec: 'TN41*41*2.0*6', amount: 38430000 },
        //     { id: 9, productName: '扁钢', spec: 'TN41*41*2.0*6', amount: 40849200 },
        //     { id: 10, productName: '光伏地桩', spec: 'TN41*41*2.0*6', amount: 38430000 },
        // ]
        this.getDeptOptions()
        this.getList()
        console.log(this.$store.state.user.info.deptId)
    },
    methods: {
        // 获取部门列表
        async getDeptOptions() {
            const { data } = await listDeptExcludeChild(this.$store.state.user.info.deptId)
            if (data.length === 0) return
            this.deptOptions = data
        },
        // handleQuery() {
        //     console.log(this.queryParams)
        // },
        // resetQuery() {
        //     this.queryParams = {}
        //     this.handleQuery()
        // },
        // 切换分类
        handleType(item) {
            this.activeType = item.value
            this.getList()
        },
        // 选择成员
        handleSelectUser(user) {
            console.log(user)
        },
        // 格式化数字（百分比）
        formatNumber(value) {
            const max = this.list.reduce((max, item) => Math.max(max, item.amount), 0)
            const min = this.list.reduce((min, item) => Math.min(min, item.amount), Infinity)
            const total = max + min
            return Number(((value / total) * 100).toFixed(2))
        },
        // 分页查询
        getList() {
            if (this.activeType === 0) {
                let data = {
                    m: this.activeDate,
                    time: this.activeDate == 6 ? this.timing[0] : this.timing,
                    endTime: this.activeDate == 6 ? this.timing[1] : undefined,
                    item: this.activeStaff,
                    deptId: this.activeDept,
                    by: this.activeBase,
                }
                getStatisticsPerformanceWithCustomer(data).then(res => {
                    this.list = res.data
                })
            } else {
                let data = {
                    type: this.activeType,
                    m: this.activeDate,
                    time: this.activeDate == 6 ? this.timing[0] : this.timing,
                    endTime: this.activeDate == 6 ? this.timing[1] : undefined,
                    item: this.activeStaff,
                    deptId: this.activeDept,
                    by: this.activeBase,
                }
                getStatisticsPerformanceWithOther(data).then(res => {
                    this.list = res.data
                })
                
            }

        },
    }
}
</script>
<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
@import '~@/assets/styles/crm-statistics.scss';

.custom-search {
    padding-top: 10px;
    padding-bottom: 0;
}

</style>