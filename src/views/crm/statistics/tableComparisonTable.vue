<template>
    <div class="statistics-container">
        <div class="search_criteria" style="background-color: #FFFFFF; padding: 0 15px;">
            <el-select v-model="activeDept" placeholder="请选择" size="small" style=" width: 138px">
                <el-option :label="item.label" :value="item.value" v-for="item in deptOptions"
                    :key="item.value"></el-option>
            </el-select>
            <popover-user ref="popoverUser" @selectUser="handleSelectUser" style="width: 138px; margin-left: 15px;" />
            <el-select v-model="activeName" placeholder="请选择" size="small" style="width: 138px; margin: 0 15px;">
                <el-option :label="item.label" :value="item.value" v-for="item in orderOptions"
                    :key="item.value"></el-option>
            </el-select>
            
            <el-date-picker v-model="timing" v-if="activeName === 'day'" type="date" size="small" format="yyyy-MM-dd"
                value-format="yyyy-MM-dd" placeholder="选择日"></el-date-picker>
            <el-date-picker v-model="timing" v-if="activeName === 'week'" type="week" size="small" format="yyyy 第 WW 周"
                placeholder="选择周"></el-date-picker>
            <el-date-picker v-model="timing" v-if="activeName === 'month'" type="month" size="small" format="yyyy-MM"
                value-format="yyyy-MM" placeholder="选择月"></el-date-picker>
            <el-quarter-picker v-model="timing" v-if="activeName === 'quarter'" placeholder="选择季度" size="small" />
            <el-date-picker v-model="timing" v-if="activeName === 'year'" type="year" size="small" format="yyyy"
                value-format="yyyy" placeholder="选择年"></el-date-picker>
            <el-date-picker v-model="timing" v-if="activeName === 'custom'" type="daterange" size="small"
                range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                :default-time="['00:00:00', '23:59:59']" :picker-options="{
                    disabledDate(time) {
                        return time.getTime() > Date.now()
                    }
                }">
            </el-date-picker>
        </div>
        <div>
            <el-table :data="tableData" border :summary-method="getSummaries" show-summary style="width: 100%;">
                <el-table-column type="index" label="序号" width="100">
                </el-table-column>
                <el-table-column prop="name" label="人员">
                </el-table-column>
                <el-table-column prop="dept" label="所属部门">
                </el-table-column>
                <el-table-column prop="amount1" label="客户数">
                </el-table-column>
                <el-table-column prop="amount2" label="成交总额（元）">
                </el-table-column>
                <el-table-column prop="amount3" label="回款总额（元）">
                </el-table-column>
                <el-table-column prop="amount3" label="跟进次数">
                </el-table-column>
            </el-table>
        </div>

    </div>
</template>

<script>
import PopoverUser from '@/views/crm/components/PopoverUser/index'
import ElQuarterPicker from '@/views/crm/components/ElQuarterPicker/index'

export default {
    name: 'TimeLineChart',
    components: {
        PopoverUser,
        ElQuarterPicker
    },
    props: {
        // 组件接收的属性
        theme: {
            type: String,
            default: 'light'
        },
    },
    watch: {
        theme(newVal) {
            this.$set(this.chartData, 'theme', newVal);
        }
    },
    data() {
        return {
            // 组件数据
            key: 1,
            activeDept: '1',
            deptOptions: [
                { label: '人员', value: '1' },
                { label: '部门', value: '2' },
            ],
            activeType: 'enter',
            typeOptions: [
                { label: '录入人', value: 'enter' },
                { label: '原负责人', value: 'oldCurator' },
                { label: '转交人', value: 'passOn' },
                { label: '负责人', value: 'curator' },
                { label: '首次跟进人', value: 'firstFollower' },
                { label: '最后跟进人', value: 'lastFollower' },
            ],
            orderOptions: [
                { label: '按天统计', value: 'day' },
                { label: '按周统计', value: 'week' },
                { label: '按月统计', value: 'month' },
                { label: '按季度统计', value: 'quarter' },
                { label: '按年统计', value: 'year' },
                { label: '指定时间', value: 'custom' }
            ],
            activeName: 'year',
            timing: '',
            tableData: [{
                dept: '销售一部',
                name: '王小虎',
                amount1: 234,
                amount2: 32,
                amount3: 10
            }, {
                dept: '销售一部',
                name: '王小虎',
                amount1: 165,
                amount2: 443,
                amount3: 12
            }, {
                dept: '销售一部',
                name: '王小虎',
                amount1: 324,
                amount2: 190,
                amount3: 9
            }, {
                dept: '销售一部',
                name: '王小虎',
                amount1: 621,
                amount2: 220,
                amount3: 17
            }, {
                dept: '销售一部',
                name: '王小虎',
                amount1: 539,
                amount2: 410,
                amount3: 15
            }]
        };
    },
    methods: {
        // 组件方法
        // 切换tab
        handleClick(tab, event) {
            console.log(tab, event)
            this.activeName = tab.name
        },
        // 选择成员
        handleSelectUser(user) {
            console.log(user)
        },

        getSummaries(param) {
            const { columns, data } = param;
            const sums = [];
            columns.forEach((column, index) => {
                if (index === 0) {
                    sums[index] = '总计';
                    return;
                }
                const values = data.map(item => Number(item[column.property]));
                if (!values.every(value => isNaN(value))) {
                    sums[index] = values.reduce((prev, curr) => {
                        const value = Number(curr);
                        if (!isNaN(value)) {
                            return prev + curr;
                        } else {
                            return prev;
                        }
                    }, 0);
                    sums[index];
                } else {
                    sums[index] = '';
                }
            });

            return sums;
        }
    },
    created() {
    }
};
</script>

<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
@import '~@/assets/styles/crm-statistics.scss';

.statistics-container {
    .statistics-card {
        margin-bottom: 20px;

    }
}
</style>