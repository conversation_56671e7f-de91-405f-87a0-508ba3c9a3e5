<template>
    <div class="newBox vh-85">
        <!-- 搜索 -->
        <div class="custom-search crm-search">
            <el-form ref="queryParams" :model="queryParams" size="small" inline class="custom-form-inline"
                @submit.native.prevent>
                <el-form-item label="客户名称" prop="name">
                    <el-input v-model="queryParams.name" placeholder="请输入客户名称" @change="handleQuery" clearable />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="handleQuery">搜索</el-button>
                    <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
                </el-form-item>
            </el-form>
        </div>
        <!-- 分类 -->
        <div class="classify flex bgwhite">
            <div class="classify-item" :class="{ active: item.value === activeType }" v-for="item in typeOptions"
                :key="item.value" @click="handleType(item)">{{ item.label }}</div>
        </div>
        <combination-time-line></combination-time-line>

    </div>
</template>
<script>
import { listDeptExcludeChild } from '@/api/system/dept'
import combinationTimeLine from './combinationTimeLine.vue'

export default {
    name: 'Combination',
    components: { combinationTimeLine },
    data() {
        return {
            queryParams: {
                name: ''
            },
            activeType: '1',
            typeOptions: [
                { label: '成交数量与金额对比', value: '1' },
                { label: '成交总额与回款总额对比', value: '2' },
            ],
            activeDept: '',
            deptOptions: [],

        }
    },
    created() {
        // 获取部门列表
        this.getDeptOptions()
        console.log(this.$store.state.user.info.deptId)
    },
    methods: {
        // 获取部门列表
        async getDeptOptions() {
            const { data } = await listDeptExcludeChild(this.$store.state.user.info.deptId)
            if (data.length === 0) return
            this.deptOptions = data
        },
        handleQuery() {
            console.log(this.queryParams)
        },
        resetQuery() {
            this.queryParams = {}
            this.handleQuery()
        },
        // 切换分类
        handleType(item) {
            this.activeType = item.value
        },
        // 选择成员
        handleSelectUser(user) {
            console.log(user)
        },
    }
}
</script>
<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
@import '~@/assets/styles/crm-statistics.scss';

.custom-search {
    padding-top: 10px;
    padding-bottom: 0;
}
</style>