<template>
    <div class="statistics-container">
        <el-row :gutter="20">
            <el-col :xs="24" :lg="12">
                <div class="statistics-card">
                    <div class="statistics-card-header">
                        <div class="title">{{ title1 }}</div>
                        <!-- <popover-user ref="popoverUser" @selectUser="handleSelectUser" style="width: 150px" /> -->
                        <el-select v-model="orderChartTime" placeholder="请选择" size="small"
                            class="statistics-card-orderChart-header-time">
                            <el-option :label="item.label" :value="item.value" v-for="item in orderChartTimeOptions"
                                :key="item.value"></el-option>
                        </el-select>
                    </div>
                    <div class="statistics-card-orderChart">
                        <div class="statistics-card-orderChart-header">
                            <div class="statistics-card-orderChart-header-type">
                                <span class="statistics-card-orderChart-header-type-item"
                                    :class="{ active: chartType1 === 'bar' }"
                                    @click="handleChartType('bar', 'quotation')">柱状图</span>
                                <span class="statistics-card-orderChart-header-type-item"
                                    :class="{ active: chartType1 === 'line' }"
                                    @click="handleChartType('line', 'quotation')">折线图</span>
                            </div>
                            <div class="flex align-center">

                                <span class="statistics-card-orderChart-header-tips">订单数量</span>
                            </div>
                        </div>
                        <div class="statistics-card-orderChart-content">
                            <line-chart :chart-data="chartData1" height="400px" :theme="theme"
                                v-if="chartType1 === 'line'" :key="key" />
                            <line-chart :chart-data="chartData1" series-type="bar" height="400px" :theme="theme"
                                v-if="chartType1 === 'bar'" :key="key" />
                        </div>
                    </div>
                </div>
            </el-col>
            <el-col :xs="24" :lg="12">
                <div class="statistics-card">
                    <div class="statistics-card-header">
                        <div class="title">{{ title2 }}</div>
                        <!-- <popover-user ref="popoverUser" @selectUser="handleSelectUser" style="width: 150px" /> -->
                        <el-select v-model="orderChartTime" placeholder="请选择" size="small"
                            class="statistics-card-orderChart-header-time">
                            <el-option :label="item.label" :value="item.value" v-for="item in orderChartTimeOptions"
                                :key="item.value"></el-option>
                        </el-select>
                    </div>
                    <div class="statistics-card-orderChart">
                        <div class="statistics-card-orderChart-header">
                            <div class="statistics-card-orderChart-header-type">
                                <span class="statistics-card-orderChart-header-type-item"
                                    :class="{ active: chartType2 === 'bar' }"
                                    @click="handleChartType('bar', 'contract')">柱状图</span>
                                <span class="statistics-card-orderChart-header-type-item"
                                    :class="{ active: chartType2 === 'line' }"
                                    @click="handleChartType('line', 'contract')">折线图</span>
                            </div>
                            <div class="flex align-center">

                                <span class="statistics-card-orderChart-header-tips">{{ activeType === 'finance' ?
                                    '收款金额' : '订单数量' }}</span>
                            </div>
                        </div>
                        <div class="statistics-card-orderChart-content">
                            <line-chart :chart-data="chartData2" height="400px" :theme="theme"
                                v-if="chartType2 === 'line'" :key="key" />
                            <line-chart :chart-data="chartData2" series-type="bar" height="400px" :theme="theme"
                                v-if="chartType2 === 'bar'" :key="key" />
                        </div>
                    </div>
                </div>
            </el-col>
            <el-col :xs="24" :lg="12">
                <div class="statistics-card">
                    <div class="statistics-card-header">
                        <div class="title">{{ title3 }}</div>
                        <!-- <popover-user ref="popoverUser" @selectUser="handleSelectUser" style="width: 150px" /> -->
                        <el-select v-model="orderChartTime" placeholder="请选择" size="small"
                            class="statistics-card-orderChart-header-time">
                            <el-option :label="item.label" :value="item.value" v-for="item in orderChartTimeOptions"
                                :key="item.value"></el-option>
                        </el-select>
                    </div>
                    <div class="statistics-card-orderChart">
                        <div class="statistics-card-orderChart-header">
                            <div class="statistics-card-orderChart-header-type">
                                <span class="statistics-card-orderChart-header-type-item"
                                    :class="{ active: chartType3 === 'bar' }"
                                    @click="handleChartType('bar', 'salesOrder')">柱状图</span>
                                <span class="statistics-card-orderChart-header-type-item"
                                    :class="{ active: chartType3 === 'line' }"
                                    @click="handleChartType('line', 'salesOrder')">折线图</span>
                            </div>
                            <div class="flex align-center">
                                <span class="statistics-card-orderChart-header-tips">{{ activeType === 'finance' ?
                                    '销售订单金额' : '订单数量' }}</span>
                            </div>
                        </div>
                        <div class="statistics-card-orderChart-content">
                            <line-chart :chart-data="chartData3" height="400px" :theme="theme"
                                v-if="chartType3 === 'line'" :key="key" />
                            <line-chart :chart-data="chartData3" series-type="bar" height="400px" :theme="theme"
                                v-if="chartType3 === 'bar'" :key="key" />
                        </div>
                    </div>
                </div>
            </el-col>
            <el-col :xs="24" :lg="12">
                <div class="statistics-card">
                    <div class="statistics-card-header">
                        <div class="title">{{ title4 }}</div>
                        <!-- <popover-user ref="popoverUser" @selectUser="handleSelectUser" style="width: 150px" /> -->
                        <el-select v-model="orderChartTime" placeholder="请选择" size="small"
                            class="statistics-card-orderChart-header-time">
                            <el-option :label="item.label" :value="item.value" v-for="item in orderChartTimeOptions"
                                :key="item.value"></el-option>
                        </el-select>
                    </div>
                    <div class="statistics-card-orderChart">
                        <div class="statistics-card-orderChart-header">
                            <div class="statistics-card-orderChart-header-type">
                                <span class="statistics-card-orderChart-header-type-item"
                                    :class="{ active: chartType4 === 'bar' }"
                                    @click="handleChartType('bar', 'payment')">柱状图</span>
                                <span class="statistics-card-orderChart-header-type-item"
                                    :class="{ active: chartType4 === 'line' }"
                                    @click="handleChartType('line', 'payment')">折线图</span>
                            </div>
                            <div class="flex align-center">

                                <span class="statistics-card-orderChart-header-tips">{{ activeType === 'finance' ?
                                    '回款金额' : '回款客户数量' }}</span>
                            </div>
                        </div>
                        <div class="statistics-card-orderChart-content">
                            <line-chart :chart-data="chartData4" height="400px" :theme="theme"
                                v-if="chartType4 === 'line'" :key="key" />
                            <line-chart :chart-data="chartData4" series-type="bar" height="400px" :theme="theme"
                                v-if="chartType4 === 'bar'" :key="key" />
                        </div>
                    </div>
                </div>
            </el-col>
        </el-row>
    </div>
</template>

<script>
import PopoverUser from '@/views/crm/components/PopoverUser/index'
import LineChart from '@/views/crm/components/dashboard/LineChart'
import { getStatisticsScreenGroupOrder, getStatisticsScreenGroupRepay, getStatisticsScreenGroupOrderAmount, getStatisticsScreenGroupReceivePaymentAmount, getStatisticsScreenGroupRepayAmount, getStatisticsScreenGroupRepayOrder } from '@/api/crm/statistics'

export default {
    name: 'SaleData',
    props: {
        title1: {
            type: String,
            default: '报价'
        },
        title2: {
            type: String,
            default: '合同'
        },
        title3: {
            type: String,
            default: '销售订单'
        },
        title4: {
            type: String,
            default: '回款'
        },
        activeType: {
            type: String,
            default: 'sales'
        },
        theme: {
            type: String,
            default: 'light'
        }
    },
    components: {
        PopoverUser,
        LineChart
    },
    data() {
        return {
            orderChartTimeOptions: [
                { label: '今天', value: 'today' },
                { label: '本周', value: 'week' },
                { label: '本月', value: 'month' },
                { label: '本季度', value: 'quarter' },
                { label: '本年', value: 'year' }
            ],
            orderChartTime: 'month',
            chartType1: 'bar',
            chartType2: 'bar',
            chartType3: 'bar',
            chartType4: 'bar',
            chartData1: {
                xAxis: [],
                yAxis: [],
                tooltipTitle: '报价数量',
                smooth: true,
            },
            chartData2: {
                xAxis: [],
                yAxis: [],
                tooltipTitle: '合同数量',
                smooth: true,
            },
            chartData3: {
                xAxis: [],
                yAxis: [],
                tooltipTitle: '销售订单',
                smooth: true,
            },
            chartData4: {
                xAxis: [],
                yAxis: [],
                tooltipTitle: '回款客户数量',
                smooth: true,
            },
            key: 1

        };
    },
    watch: {
        activeType(newVal) {
            if (newVal === 'sales') {
                this.init()
            } else if (newVal === 'finance') {
                this.init()
            }
        },
        theme(newVal) {
            this.$set(this.chartData1, 'theme', newVal);
            this.$set(this.chartData2, 'theme', newVal);
            this.$set(this.chartData3, 'theme', newVal);
            this.$set(this.chartData4, 'theme', newVal);
        }
    },

    created() {
        this.init()
    },
    methods: {
        // 初始化
        init() {
            // 获取统计数据
            if (this.activeType === 'sales') {
                this.$set(this, 'chartData1', {
                    xAxis: ['01-01', '01-02', '01-03', '01-04', '01-05', '01-06', '01-07', '01-08', '01-09', '01-10', '01-11', '01-12', '01-13', '01-14', '01-15', '01-16', '01-17', '01-18', '01-19', '01-20', '01-21'],
                    yAxis: [60, 80, 30, 50, 40, 60, 70, 90, 80, 100, 120, 100, 130, 150, 150, 190, 170, 180, 185, 181, 173],
                    tooltipTitle: '报价数量',
                    smooth: true,
                })
                this.$set(this, 'chartData2', {
                    xAxis: ['01-01', '01-02', '01-03', '01-04', '01-05', '01-06', '01-07', '01-08', '01-09', '01-10', '01-11', '01-12', '01-13', '01-14', '01-15', '01-16', '01-17', '01-18', '01-19', '01-20', '01-21'],
                    yAxis: [60, 80, 30, 50, 40, 60, 70, 90, 80, 100, 120, 100, 130, 150, 150, 190, 170, 180, 185, 181, 173],
                    tooltipTitle: '合同数量',
                    smooth: true,
                })
                this.getSaleNumChartData()
                this.getRepayNumChartData()
            } else if (this.activeType === 'finance') {
                this.getRepayOrderChartData()
                this.getRepayAmountChartData()
                this.getReceivePaymentChartData()
                this.getSalesOrderAmountChartData()
            }
        },
        // 获取销售订单
        getSaleNumChartData() {
            getStatisticsScreenGroupOrder({
                sm: undefined,
                em: undefined,
            }).then(res => {
                let { code, data, msg } = res
                if (code === 200) {
                    let dataX = []
                    let dataY = []
                    data.forEach(item => {
                        dataX.push(item.d)
                        dataY.push(item.t)
                    })
                    this.$set(this, 'chartData3', {
                        xAxis: dataX,
                        yAxis: dataY,
                        tooltipTitle: '订单数量',
                        smooth: true,
                    })
                } else {
                    this.$message.error(msg || '获取销售数据失败')
                }
            }).catch(err => {
                console.error('获取统计数据失败:', err)
            })
        },
        // 获取回款数据
        getRepayNumChartData() {
            getStatisticsScreenGroupRepay({
                sm: undefined,
                em: undefined,
            }).then(res => {
                let { code, data, msg } = res
                if (code === 200) {
                    let dataX = []
                    let dataY = []
                    data.forEach(item => {
                        dataX.push(item.d)
                        dataY.push(item.t)
                    })
                    this.$set(this, 'chartData4', {
                        xAxis: dataX,
                        yAxis: dataY,
                        tooltipTitle: '回款金额',
                        smooth: true,
                    })
                } else {
                    this.$message.error(msg || '获取回款数据失败')
                }
            }).catch(err => {
                console.error('获取回款数据失败:', err)
            })
        },
        // 收款订单
        getRepayOrderChartData() {
            getStatisticsScreenGroupRepayOrder({
                sm: undefined,
                em: undefined,
            }).then(res => {
                let { code, data, msg } = res
                if (code === 200) {
                    let dataX = []
                    let dataY = []
                    data.forEach(item => {
                        dataX.push(item.d)
                        dataY.push(item.t)
                    })
                    this.$set(this, 'chartData1', {
                        xAxis: dataX,
                        yAxis: dataY,
                        tooltipTitle: '收款订单数量',
                        smooth: true,
                    })
                } else {
                    this.$message.error(msg || '获取收款订单数据失败')
                }
            }).catch(err => {
                console.error('获取收款订单数据失败:', err)
            })
        },
        // 收款金额
        getReceivePaymentChartData() {
            getStatisticsScreenGroupReceivePaymentAmount({
                sm: undefined,
                em: undefined,
            }).then(res => {
                let { code, data, msg } = res
                if (code === 200) {
                    let dataX = []
                    let dataY = []
                    data.forEach(item => {
                        dataX.push(item.d)
                        dataY.push(item.t)
                    })
                    this.$set(this, 'chartData2', {
                        xAxis: dataX,
                        yAxis: dataY,
                        tooltipTitle: '收款金额',
                        smooth: true,
                    })
                } else {
                    this.$message.error(msg || '获取收款金额数据失败')
                }
            }).catch(err => {
                console.error('获取收款金额数据失败:', err)
            })
        },
        // 销售订单金额
        getSalesOrderAmountChartData() {
            getStatisticsScreenGroupOrderAmount({
                sm: undefined,
                em: undefined,
            }).then(res => {
                let { code, data, msg } = res
                if (code === 200) {
                    let dataX = []
                    let dataY = []
                    data.forEach(item => {
                        dataX.push(item.d)
                        dataY.push(item.t)
                    })
                    this.$set(this, 'chartData3', {
                        xAxis: dataX,
                        yAxis: dataY,
                        tooltipTitle: '销售订单金额',
                        smooth: true,
                    })
                } else {
                    this.$message.error(msg || '获取销售订单金额数据失败')
                }
            }).catch(err => {
                console.error('获取销售订单金额数据失败:', err)
            })
        },
        // 回款金额
        getRepayAmountChartData() {
            getStatisticsScreenGroupRepayAmount({
                sm: undefined,
                em: undefined,
            }).then(res => {
                let { code, data, msg } = res
                if (code === 200) {
                    let dataX = []
                    let dataY = []
                    data.forEach(item => {
                        dataX.push(item.d)
                        dataY.push(item.t)
                    })
                    this.$set(this, 'chartData4', {
                        xAxis: dataX,
                        yAxis: dataY,
                        tooltipTitle: '回款金额',
                        smooth: true,
                    })
                } else {
                    this.$message.error(msg || '获取回款金额数据失败')
                }
            }).catch(err => {
                console.error('获取回款金额数据失败:', err)
            })
        },
        // 选择成员
        handleSelectUser(user) {
            console.log(user)
        },
        // 切换图表类型
        handleChartType(type, chartType) {
            if (chartType === 'quotation') {
                this.chartType1 = type
            } else if (chartType === 'contract') {
                this.chartType2 = type
            } else if (chartType === 'salesOrder') {
                this.chartType3 = type
            } else if (chartType === 'payment') {
                this.chartType4 = type
            }
            this.key = Math.random()
            console.log(this.chartType1, this.chartType2, this.chartType3, this.chartType4)
        },
    },
};
</script>

<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
@import '~@/assets/styles/crm-statistics.scss';

.statistics-container {
    .statistics-card {
        margin-bottom: 20px;

    }
}
</style>