<template>
    <div class="statistics-container">
        <el-row :gutter="20">
            <el-col :xs="24" :lg="10">
                <div class="statistics-card">
                    <div class="statistics-card-header">
                        <div class="title">客户分析</div>
                        <!-- <popover-user ref="popoverUser" @selectUser="handleSelectUser" style="width: 150px" /> -->
                        <el-select v-model="orderChartTime" placeholder="请选择" size="small"
                            class="statistics-card-orderChart-header-time">
                            <el-option :label="item.label" :value="item.value" v-for="item in orderChartTimeOptions"
                                :key="item.value"></el-option>
                        </el-select>
                    </div>
                    <div class="statistics-card-orderChart">
                        <div class="statistics-card-orderChart-header" style="height: 66px;">
                            <div class="statistics-card-orderChart-header-type">
                                <!-- <span class="statistics-card-orderChart-header-type-item"
                                    :class="{ active: chartType1 === 'bar' }"
                                    @click="handleChartType('bar', 'quotation')">柱状图</span>
                                <span class="statistics-card-orderChart-header-type-item"
                                    :class="{ active: chartType1 === 'line' }"
                                    @click="handleChartType('line', 'quotation')">折线图</span> -->
                            </div>
                            <div class="flex align-center">
                                <span class="statistics-card-orderChart-header-tips">新增数量</span>
                            </div>
                        </div>
                        <div class="statistics-card-orderChart-content">
                            <line-chart :chart-data="analysisChartData" height="400px" :theme="theme" :key="key" />
                            <!-- <line-chart :chart-data="chartData" series-type="bar" height="400px" :theme="theme"
                                v-if="chartType1 === 'bar'" :key="key" /> -->
                        </div>
                    </div>
                </div>
            </el-col>
            <el-col :xs="24" :lg="14">
                <div class="statistics-card">
                    <div class="statistics-card-header">
                        <div class="title">客户新增概览</div>
                        <!-- <popover-user ref="popoverUser" @selectUser="handleSelectUser" style="width: 150px" /> -->
                        <el-select v-model="orderChartTime" placeholder="请选择" size="small"
                            class="statistics-card-orderChart-header-time">
                            <el-option :label="item.label" :value="item.value" v-for="item in orderChartTimeOptions"
                                :key="item.value"></el-option>
                        </el-select>
                    </div>
                    <div class="statistics-card-orderChart">
                        <div class="statistics-card-orderChart-content" style="display: flex; align-items: start;">
                            <div style="width: 320px;">
                                <pie-chart :chart-data="pieChartData" series-type="pie" width="320px" height="366px"
                                    :radius="['40%', '70%']" :showEmphasis="false" :theme="theme" :key="key" />
                            </div>
                            <div style="display: flex; align-items: center; flex-wrap: wrap; padding: 80px 0 0;">
                                <div style="width: 180px;display: flex; align-items: start; margin-bottom: 50px;">
                                    <span
                                        style="width: 20px; height: 20px; border-radius: 50px; flex-shrink: 0; background-color: #B3CEFF; margin-right: 20px;"></span>
                                    <div>
                                        <div
                                            style="font-weight: 500; font-size: 14px; color: #333333; line-height: 20px; margin-bottom: 15px;">
                                            销售一部</div>
                                        <div
                                            style="font-weight: 400; font-size: 14px; color: #666666; line-height: 20px;">
                                            新增数量
                                            <span style="font-size: 16px; color: #2E73F3; margin-left: 15px;">16</span>
                                        </div>
                                    </div>
                                </div>
                                <div style="width: 180px;display: flex; align-items: start; margin-bottom: 50px;">
                                    <span
                                        style="width: 20px; height: 20px; border-radius: 50px; flex-shrink: 0; background-color: #FFB2C8; margin-right: 20px;"></span>
                                    <div>
                                        <div
                                            style="font-weight: 500; font-size: 14px; color: #333333; line-height: 20px; margin-bottom: 15px;">
                                            销售二部</div>
                                        <div
                                            style="font-weight: 400; font-size: 14px; color: #666666; line-height: 20px;">
                                            新增数量
                                            <span style="font-size: 16px; color: #2E73F3; margin-left: 15px;">16</span>
                                        </div>
                                    </div>
                                </div>
                                <div style="width: 180px;display: flex; align-items: start; margin-bottom: 50px;">
                                    <span
                                        style="width: 20px; height: 20px; border-radius: 50px; flex-shrink: 0; background-color: #D3FFE7; margin-right: 20px;"></span>
                                    <div>
                                        <div
                                            style="font-weight: 500; font-size: 14px; color: #333333; line-height: 20px; margin-bottom: 15px;">
                                            销售三部</div>
                                        <div
                                            style="font-weight: 400; font-size: 14px; color: #666666; line-height: 20px;">
                                            新增数量
                                            <span style="font-size: 16px; color: #2E73F3; margin-left: 15px;">16</span>
                                        </div>
                                    </div>
                                </div>
                                <div style="width: 180px;display: flex; align-items: start; margin-bottom: 50px;">
                                    <span
                                        style="width: 20px; height: 20px; border-radius: 50px; flex-shrink: 0; background-color: #DCC6FF; margin-right: 20px;"></span>
                                    <div>
                                        <div
                                            style="font-weight: 500; font-size: 14px; color: #333333; line-height: 20px; margin-bottom: 15px;">
                                            销售四部</div>
                                        <div
                                            style="font-weight: 400; font-size: 14px; color: #666666; line-height: 20px;">
                                            新增数量
                                            <span style="font-size: 16px; color: #2E73F3; margin-left: 15px;">16</span>
                                        </div>
                                    </div>
                                </div>
                                <div style="width: 180px;display: flex; align-items: start; margin-bottom: 50px;">
                                    <span
                                        style="width: 20px; height: 20px; border-radius: 50px; flex-shrink: 0; background-color: #FFE09C; margin-right: 20px;"></span>
                                    <div>
                                        <div
                                            style="font-weight: 500; font-size: 14px; color: #333333; line-height: 20px; margin-bottom: 15px;">
                                            销售五部</div>
                                        <div
                                            style="font-weight: 400; font-size: 14px; color: #666666; line-height: 20px;">
                                            新增数量
                                            <span style="font-size: 16px; color: #2E73F3; margin-left: 15px;">16</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div
                            style="height: 100px; background-color: #FFDD94; font-weight: 400; font-size: 12px; color: #333333; line-height: 20px; display: flex; align-items: center;">
                            <span style="margin: 0 20px;">销售五部客户新增数量</span>
                            <span
                                style="font-weight: 500; font-size: 16px; color: #333333; margin-right: 5px;">29</span>
                            <span>个</span>
                            <span style="margin: 0 20px 0 50px;">成交客户</span>
                            <span
                                style="font-weight: 500; font-size: 16px; color: #333333; margin-right: 5px;">13</span>
                            <span>个</span>
                        </div>
                    </div>
                </div>
            </el-col>
            <el-col :xs="24" :lg="12">
                <div class="statistics-card">
                    <div class="statistics-card-header">
                        <div class="title">成交客户数量</div>
                        <!-- <popover-user ref="popoverUser" @selectUser="handleSelectUser" style="width: 150px" /> -->
                        <el-select v-model="orderChartTime" placeholder="请选择" size="small"
                            class="statistics-card-orderChart-header-time">
                            <el-option :label="item.label" :value="item.value" v-for="item in orderChartTimeOptions"
                                :key="item.value"></el-option>
                        </el-select>
                    </div>
                    <div class="statistics-card-orderChart">
                        <div class="statistics-card-orderChart-header">
                            <div class="statistics-card-orderChart-header-type">
                                <span class="statistics-card-orderChart-header-type-item"
                                    :class="{ active: chartType3 === 'bar' }" @click="handleChartType('bar')">柱状图</span>
                                <span class="statistics-card-orderChart-header-type-item"
                                    :class="{ active: chartType3 === 'line' }"
                                    @click="handleChartType('line')">折线图</span>
                            </div>
                            <div class="flex align-center">
                                <span class="statistics-card-orderChart-header-tips">订单数量</span>
                            </div>
                        </div>
                        <div class="statistics-card-orderChart-content">
                            <line-chart :chart-data="dealChartData" height="400px" :theme="theme"
                                v-if="chartType3 === 'line'" :key="key" />
                            <line-chart :chart-data="dealChartData" series-type="bar" height="400px" :theme="theme"
                                v-if="chartType3 === 'bar'" :key="key" />
                        </div>
                    </div>
                </div>
            </el-col>
            <el-col :xs="24" :lg="12">
                <div class="statistics-card">
                    <div class="statistics-card-header">
                        <div class="title">回款客户数量</div>
                        <!-- <popover-user ref="popoverUser" @selectUser="handleSelectUser" style="width: 150px" /> -->
                        <el-select v-model="orderChartTime" placeholder="请选择" size="small"
                            class="statistics-card-orderChart-header-time">
                            <el-option :label="item.label" :value="item.value" v-for="item in orderChartTimeOptions"
                                :key="item.value"></el-option>
                        </el-select>
                    </div>
                    <div class="statistics-card-orderChart">
                        <div class="statistics-card-orderChart-header" style="height: 66px;">
                            <div class="statistics-card-orderChart-header-type">
                                <!-- <span class="statistics-card-orderChart-header-type-item"
                                    :class="{ active: chartType4 === 'bar' }"
                                    @click="handleChartType('bar', 'payment')">柱状图</span>
                                <span class="statistics-card-orderChart-header-type-item"
                                    :class="{ active: chartType4 === 'line' }"
                                    @click="handleChartType('line', 'payment')">折线图</span> -->
                            </div>
                            <div class="flex align-center">

                                <span class="statistics-card-orderChart-header-tips">回款客户数量</span>
                            </div>
                        </div>
                        <div class="statistics-card-orderChart-content">
                            <!-- <line-chart :chart-data="chartData" height="400px" :theme="theme"
                                v-if="chartType4 === 'line'" :key="key" /> -->
                            <line-chart :chart-data="paymentChartData" series-type="bar" height="400px" :theme="theme"
                                :key="key" />
                        </div>
                    </div>
                </div>
            </el-col>
        </el-row>
    </div>
</template>

<script>
import PopoverUser from '@/views/crm/components/PopoverUser/index'
import LineChart from '@/views/crm/components/dashboard/LineChart'
import PieChart from '../components/dashboard/PieChart.vue';
import { getStatisticsScreenCustomerAnalysis, getStatisticsScreenCustomerWithStatus } from '@/api/crm/statistics'

export default {
    name: 'CustomerAnalysisScreen',
    props: {
        title1: {
            type: String,
            default: '报价'
        },
        title2: {
            type: String,
            default: '合同'
        },
        title3: {
            type: String,
            default: '销售订单'
        },
        title4: {
            type: String,
            default: '回款'
        },
        activeType: {
            type: String,
            default: 'sales'
        },
        theme: {
            type: String,
            default: 'light'
        }
    },
    components: {
        PopoverUser,
        LineChart,
        PieChart
    },
    data() {
        return {
            orderChartTimeOptions: [
                { label: '今天', value: 'today' },
                { label: '本周', value: 'week' },
                { label: '本月', value: 'month' },
                { label: '本季度', value: 'quarter' },
                { label: '本年', value: 'year' }
            ],
            orderChartTime: 'month',
            // chartType1: 'line',
            // chartType2: 'bar',
            chartType3: 'line',
            chartType4: 'bar',
            analysisChartData: {
                xAxis: [],
                yAxis: [],
                tooltipTitle: '新增数量',
                smooth: true,
            },
            dealChartData: {
                xAxis: [],
                yAxis: [],
                tooltipTitle: '成交客户数量',
                smooth: true,
            },
            paymentChartData: {
                xAxis: [],
                yAxis: [],
                tooltipTitle: '回款客户数量',
                smooth: true,
            },
            key: 1,
            pieChartData: {
                seriesData: [],
            }

        };
    },
    watch: {
        theme(newVal) {
            this.$set(this.chartData, 'theme', newVal);
        }
    },

    created() {
        this.$set(this, 'pieChartData', {
            seriesData: [
                { name: '销售一部', value: 16 },
                { name: '销售二部', value: 26 },
                { name: '销售三部', value: 9 },
                { name: '销售四部', value: 19 },
                { name: '销售五部', value: 29 },
            ],
        })
        this.init()
    },
    methods: {
        // 初始化
        init() {
            // if (this.activeType === 'customer') {
            this.handleCustomerAnalysis()
            this.handleDealCustomerCount()
            this.handlePaymentCustomerCount()
            // }
        },
        // 客户分析
        handleCustomerAnalysis() {
            getStatisticsScreenCustomerAnalysis({
                sm: undefined,
                em: undefined,
            }).then(res => {
                const { code, data, msg } = res
                if (code === 200) {
                    let dataX = []
                    let dataY = []
                    data.forEach(item => {
                        dataX.push(item.d)
                        dataY.push(item.t)
                    })
                    this.$set(this, 'analysisChartData', {
                        xAxis: dataX,
                        yAxis: dataY,
                        tooltipTitle: '新增数量',
                        smooth: true,
                    })
                } else {
                    this.$message.error(msg || '获取客户分析数据失败')
                }
            })
        },
        // 成交客户数量
        handleDealCustomerCount() {
            getStatisticsScreenCustomerWithStatus({
                sm: undefined,
                em: undefined,
                status: 'deal',
            }).then(res => {
                const { code, data, msg } = res
                if (code === 200) {
                    let dataX = []
                    let dataY = []
                    data.forEach(item => {
                        dataX.push(item.d)
                        dataY.push(item.t)
                    })
                    this.$set(this, 'dealChartData', {
                        xAxis: dataX,
                        yAxis: dataY,
                        tooltipTitle: '成交客户数量',
                        smooth: true,
                    })
                } else {
                    this.$message.error(msg || '获取成交客户数量失败')
                }
            })
        },
        // 回款客户数量
        handlePaymentCustomerCount() {
            getStatisticsScreenCustomerWithStatus({
                sm: undefined,
                em: undefined,
                status: 'payment',
            }).then(res => {
                const { code, data, msg } = res
                if (code === 200) {
                    let dataX = []
                    let dataY = []
                    data.forEach(item => {
                        dataX.push(item.d)
                        dataY.push(item.t)
                    })
                    this.$set(this, 'paymentChartData', {
                        xAxis: dataX,
                        yAxis: dataY,
                        tooltipTitle: '回款客户数量',
                        smooth: true,
                    })
                } else {
                    this.$message.error(msg || '获取回款客户数量失败')
                }
            })
        },
        // 选择成员
        handleSelectUser(user) {
            console.log(user)
        },
        // 切换图表类型
        handleChartType(type, chartType) {
            // if (chartType === 'quotation') {
            //     this.chartType1 = type
            // } else if (chartType === 'contract') {
            //     this.chartType2 = type
            // } else if (chartType === 'salesOrder') {
            this.chartType3 = type
            // } else if (chartType === 'payment') {
            //     this.chartType4 = type
            // }
            this.key = Math.random()
        },
    },
};
</script>

<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
@import '~@/assets/styles/crm-statistics.scss';

.statistics-container {
    .statistics-card {
        margin-bottom: 20px;

    }
}
</style>