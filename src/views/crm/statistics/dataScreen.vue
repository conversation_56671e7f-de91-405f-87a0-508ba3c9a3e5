<template>
  <div class="newBox vh-85" :class="{ lightTheme: theme === 'light', darkTheme: theme === 'dark' }">
    <!-- 搜索 -->
    <div class="custom-search crm-search">
      <el-form ref="queryParams" :model="queryParams" size="small" inline class="custom-form-inline"
        @submit.native.prevent>
        <el-form-item label="客户名称" prop="name">
          <el-input v-model="queryParams.name" placeholder="请输入客户名称" @change="handleQuery" clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
        <div class="statistics-theme">
          <span class="statistics-theme-item" :class="{ active: theme === 'light' }"
            @click="handleTheme('light')">浅色模式</span>
          <span class="statistics-theme-item" :class="{ active: theme === 'dark' }"
            @click="handleTheme('dark')">深色模式</span>
        </div>
      </el-form>
    </div>
    <!-- 分类 -->
    <div class="classify flex bgwhite">
      <div class="classify-item" :class="{ active: item.value === activeType }" v-for="item in typeOptions"
        :key="item.value" @click="handleType(item)">{{ item.label }}</div>
      <div class="classify-time">
        <span>{{ currentTime }}</span>
        <span>{{ parseTime(new Date(), '{m}/{d}') }}</span>
        <span>{{ parseTime(new Date(), '{a}') }}</span>
      </div>
    </div>
    <div class="statistics-container" v-if="activeType === 'overview'">
      <el-row :gutter="20">
        <el-col :xs="24" :lg="16">
          <el-row :gutter="20">
            <el-col :xs="24" :md="12">
              <div class="statistics-card">
                <div class="statistics-card-header">
                  <span class="title">成交总额</span>
                  <div class="flex align-center flex1">
                    <popover-user ref="popoverUser" @selectUser="handleSelectUser"
                      style="width: calc(50% - 10px); min-width: unset" />
                    <el-select v-model="activeDate" placeholder="请选择" size="small"
                      style="margin-left: 15px; width: calc(50% - 10px)">
                      <el-option :label="item.label" :value="item.value" v-for="item in dateOptions"
                        :key="item.value"></el-option>
                    </el-select>
                  </div>
                </div>
                <div class="statistics-card-total">
                  <div class="statistics-card-total-progress">
                    <el-progress type="circle" :percentage="100" color="#2e73f3" :width="132" :show-text="false"
                      class="big-progress"></el-progress>
                    <el-progress type="circle" :percentage="86" color="#31C776" define-back-color="#e0fbec" :width="100"
                      :show-text="false" class="small-progress"></el-progress>
                    <div class="info">
                      <span class="text">总进度</span>
                      <span class="value">86%</span>
                    </div>
                  </div>
                  <div class="statistics-card-total-info">
                    <span class="title">业绩目标</span>
                    <div class="item">
                      <i class="point"></i>
                      <span class="text">本月目标</span>
                      <span class="value">99,999.99</span>
                      <span class="unit">元</span>
                    </div>
                    <div class="item success">
                      <i class="point"></i>
                      <span class="text">已完成</span>
                      <span class="value">98,688.88</span>
                      <span class="unit">元</span>
                    </div>
                  </div>
                </div>
                <div class="statistics-card-footer">
                  <span class="text">今日完成</span>
                  <span class="value">10000</span>
                  <span class="text">元</span>
                </div>
              </div>
            </el-col>
            <el-col :xs="24" :md="12">
              <div class="statistics-card">
                <div class="statistics-card-header">
                  <span class="title">我的简报</span>
                </div>
                <div class="statistics-card-notes">
                  <div class="statistics-card-notes-item">
                    <span class="value primary">2</span>
                    <span class="text">新增客户</span>
                  </div>
                  <div class="statistics-card-notes-item">
                    <span class="value">0</span>
                    <span class="text">联系跟进次数</span>
                  </div>
                  <div class="statistics-card-notes-item">
                    <span class="value primary">200,000,000.00</span>
                    <span class="text">成交总额(元)</span>
                  </div>
                  <div class="statistics-card-notes-item">
                    <span class="value primary">6</span>
                    <span class="text">成交单数</span>
                  </div>
                  <div class="statistics-card-notes-item">
                    <span class="value orange">20,000.00</span>
                    <span class="text">收款总额(元)</span>
                  </div>
                  <div class="statistics-card-notes-item">
                    <span class="value primary">0</span>
                    <span class="text">欠款(元)</span>
                  </div>
                </div>
                <div class="statistics-card-footer">
                  <span class="text">今日收款</span>
                  <span class="value">10000</span>
                  <span class="text">元</span>
                </div>
              </div>
            </el-col>
            <el-col :span="24">
              <div class="statistics-card">
                <div class="statistics-card-header">
                  <el-tabs v-model="activeName" type="card" @tab-click="handleClick" class="statistics-card-tabs">
                    <el-tab-pane v-for="item in orderOptions" :key="item.value" :label="item.label"
                      :name="item.value"></el-tab-pane>
                  </el-tabs>
                  <popover-user ref="popoverUser" @selectUser="handleSelectUser" style="width: 150px" />
                </div>
                <div class="statistics-card-orderChart">
                  <div class="statistics-card-orderChart-header">
                    <div class="statistics-card-orderChart-header-type">
                      <span class="statistics-card-orderChart-header-type-item" :class="{ active: chartType === 'bar' }"
                        @click="handleChartType('bar')">柱状图</span>
                      <span class="statistics-card-orderChart-header-type-item"
                        :class="{ active: chartType === 'line' }" @click="handleChartType('line')">折线图</span>
                    </div>
                    <div class="flex align-center">
                      <el-select v-model="orderChartTime" placeholder="请选择" size="small"
                        class="statistics-card-orderChart-header-time">
                        <el-option :label="item.label" :value="item.value" v-for="item in orderChartTimeOptions"
                          :key="item.value"></el-option>
                      </el-select>
                      <span class="statistics-card-orderChart-header-tips">订单数量</span>
                    </div>
                  </div>
                  <div class="statistics-card-orderChart-content">
                    <line-chart :chart-data="saleChartData" height="400px" :theme="theme" v-if="chartType === 'line'"
                      :key="key" />
                    <line-chart :chart-data="saleChartData" series-type="bar" height="400px" :theme="theme"
                      v-if="chartType === 'bar'" :key="key" />
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>
        </el-col>
        <el-col :xs="24" :lg="8">
          <div class="statistics-card">
            <div class="statistics-card-header">
              <div class="title">
                排行榜
                <span class="tips">前20名</span>
              </div>
              <el-select v-model="activeDate" placeholder="请选择" size="small">
                <el-option :label="item.label" :value="item.value" v-for="item in dateOptions"
                  :key="item.value"></el-option>
              </el-select>
            </div>
            <el-tabs v-model="rankingType" @tab-click="handleRankingClick" class="statistics-card-ranking-tabs">
              <el-tab-pane v-for="item in rankingOptions" :key="item.value" :label="item.label"
                :name="item.value"></el-tab-pane>
            </el-tabs>
            <div class="statistics-card-ranking-list">
              <div class="statistics-card-ranking-list-item" v-for="(item, index) in rankingList" :key="item.id">
                <span class="number"
                  :class="{ 'number-1': index === 0, 'number-2': index === 1, 'number-3': index === 2 }">{{ index > 2 ?
                    index + 1 : '' }}</span>
                <el-avatar :src="item.avatar" class="avatar" />
                <span class="name">{{ item.name }}</span>
                <el-progress :percentage="formatNumber(item.value)" :stroke-width="10" color="#2e73f3"
                  :define-back-color="theme === 'light' ? '#dee7f9' : '#30266B'" :show-text="false" class="progress" />
                <span class="value">{{ item.value }}</span>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
    <!-- 客户分析 -->
    <customer-analysis v-if="activeType === 'customer'" :theme="theme" />
    <!-- 销售数据总览/财务总览 -->
    <sale-data v-if="activeType === 'sales' || activeType === 'finance'" :theme="theme" :activeType="activeType"
      :title1="title1" :title2="title2" :title3="title3" :title4="title4" />
    <!-- 财务总览 -->
    <time-data v-if="activeType === 'times'" :theme="theme" />
  </div>
</template>
<script>
import PopoverUser from '@/views/crm/components/PopoverUser/index'
import LineChart from '@/views/crm/components/dashboard/LineChart'
import saleData from './saleData.vue'
import timeData from './timeData.vue'
import customerAnalysis from './customerAnalysis.vue'
import { getStatisticsScreenGroupOrder } from '@/api/crm/statistics'

export default {
  name: 'Statistics-nocache',
  components: { PopoverUser, LineChart, saleData, timeData, customerAnalysis },
  data() {
    return {
      theme: 'light', // light: 浅色模式, dark: 深色模式
      queryParams: {
        name: ''
      },
      currentTime: '',
      timer: null,
      activeType: 'overview',
      typeOptions: [
        { label: '总览', value: 'overview' },
        { label: '客户分析', value: 'customer' },
        { label: '销售数据总览', value: 'sales' },
        { label: '财务总览', value: 'finance' },
        { label: '按时间统计', value: 'times' },
      ],
      activeDate: 'currentMonth',
      dateOptions: [
        { label: '本月', value: 'currentMonth' },
        { label: '上个月', value: 'lastMonth' },
        { label: '本季度', value: 'currentQuarter' },
        { label: '上季度', value: 'lastQuarter' },
        { label: '本年度', value: 'currentYear' },
        { label: '上一年', value: 'lastYear' }
      ],
      activeName: 'all',
      orderOptions: [
        { label: '销售订单概览', value: 'all' },
        { label: '最新订单', value: 'latest' }
      ],
      rankingType: 'newCustomers',
      rankingOptions: [
        { label: '新增客户数', value: 'newCustomers' },
        { label: '联系跟进次数', value: 'followUps' },
        { label: '成交总额', value: 'totalSales' },
        { label: '成交单数', value: 'orderCount' },
        { label: '回款总额', value: 'totalPayments' },
        { label: '欠款', value: 'debt' }
      ],
      rankingList: [],
      chartType: 'line',
      orderChartTime: 'currentMonth',
      orderChartTimeOptions: [
        { label: '本月', value: 'currentMonth' },
        { label: '上个月', value: 'lastMonth' },
        { label: '近三个月', value: 'lastThreeMonths' },
        { label: '近半年', value: 'lastSixMonths' },
        { label: '近一年', value: 'lastYear' }
      ],
      chartData: {
        xAxis: [],
        yAxis: [],
        tooltipTitle: '订单数量',
        smooth: true,
      },
      saleChartData: {
        // 销售订单图表数据
        xAxis: [],
        yAxis: [],
        tooltipTitle: '销售订单金额',
        smooth: true,
      },
      key: 1,
      // 图标标题
      title1: '报价',
      title2: '合同',
      title3: '销售订单',
      title4: '回款'
    }
  },
  created() {
    this.updateTime()
    this.timer = setInterval(this.updateTime, 1000)
    this.rankingList = [
      { id: 1, avatar: 'https://randomuser.me/api/portraits/men/1.jpg', name: '张三', value: 29, department: '销售部', position: '销售经理' },
      { id: 2, avatar: 'https://randomuser.me/api/portraits/women/2.jpg', name: '李四', value: 22, department: '市场部', position: '市场总监' },
      { id: 3, avatar: 'https://randomuser.me/api/portraits/men/3.jpg', name: '王五', value: 18, department: '销售部', position: '销售顾问' },
      { id: 4, avatar: 'https://randomuser.me/api/portraits/women/4.jpg', name: '赵六', value: 16, department: '客户服务部', position: '客户经理' },
      { id: 5, avatar: 'https://randomuser.me/api/portraits/men/5.jpg', name: '钱七', value: 15, department: '销售部', position: '销售代表' },
      { id: 6, avatar: 'https://randomuser.me/api/portraits/women/6.jpg', name: '孙八', value: 13, department: '市场部', position: '市场专员' },
      { id: 7, avatar: 'https://randomuser.me/api/portraits/men/7.jpg', name: '周九', value: 12, department: '销售部', position: '销售顾问' },
      { id: 8, avatar: 'https://randomuser.me/api/portraits/women/8.jpg', name: '吴十', value: 11, department: '客户服务部', position: '客户专员' },
      { id: 9, avatar: 'https://randomuser.me/api/portraits/men/9.jpg', name: '郑十一', value: 10, department: '销售部', position: '销售经理' },
      { id: 10, avatar: 'https://randomuser.me/api/portraits/women/10.jpg', name: '王十二', value: 7, department: '市场部', position: '市场分析师' },
      { id: 11, avatar: 'https://randomuser.me/api/portraits/men/11.jpg', name: '冯十三', value: 7, department: '销售部', position: '销售代表' },
      { id: 12, avatar: 'https://randomuser.me/api/portraits/women/12.jpg', name: '陈十四', value: 7, department: '客户服务部', position: '客户经理' },
      { id: 13, avatar: 'https://randomuser.me/api/portraits/men/13.jpg', name: '楚十五', value: 6, department: '销售部', position: '销售顾问' },
      { id: 14, avatar: 'https://randomuser.me/api/portraits/women/14.jpg', name: '魏十六', value: 6, department: '市场部', position: '市场专员' },
      { id: 15, avatar: 'https://randomuser.me/api/portraits/men/15.jpg', name: '蒋十七', value: 6, department: '销售部', position: '销售代表' },
      { id: 16, avatar: 'https://randomuser.me/api/portraits/women/16.jpg', name: '沈十八', value: 6, department: '客户服务部', position: '客户专员' },
      { id: 17, avatar: 'https://randomuser.me/api/portraits/men/17.jpg', name: '韩十九', value: 5, department: '销售部', position: '销售顾问' },
      { id: 18, avatar: 'https://randomuser.me/api/portraits/women/18.jpg', name: '杨二十', value: 4, department: '市场部', position: '市场分析师' },
      { id: 19, avatar: 'https://randomuser.me/api/portraits/men/19.jpg', name: '朱二一', value: 5, department: '销售部', position: '销售经理' },
      { id: 20, avatar: 'https://randomuser.me/api/portraits/women/20.jpg', name: '秦二二', value: 4, department: '客户服务部', position: '客户经理' }
    ]
    this.chartData.xAxis = ['01-01', '01-02', '01-03', '01-04', '01-05', '01-06', '01-07', '01-08', '01-09', '01-10', '01-11', '01-12', '01-13', '01-14', '01-15', '01-16', '01-17', '01-18', '01-19', '01-20', '01-21']
    this.chartData.yAxis = [60, 80, 30, 50, 40, 60, 70, 90, 80, 100, 120, 100, 130, 150, 150, 190, 170, 180, 185, 181, 173]
    this.chartData.smooth = true
    this.init()
  },
  beforeDestroy() {
    if (this.timer) clearInterval(this.timer)
  },
  methods: {
    init() {
      this.getSalesOrderChartData()
    },
    handleQuery() {
      console.log(this.queryParams)
    },
    resetQuery() {
      this.queryParams = {}
      this.handleQuery()
    },
    // 时间自动刷新
    updateTime() {
      this.currentTime = this.parseTime(new Date(), '{h}:{i}:{s}')
    },
    // 切换分类
    handleType(item) {
      this.activeType = item.value
      if (item.value === 'finance') {
        this.title1 = '收款订单数量概览'
        this.title2 = '收款金额概览'
        this.title3 = '销售订单金额概览'
        this.title4 = '回款金额概览'
      } else if (item.value === 'sales') {
        this.title1 = '报价'
        this.title2 = '合同'
        this.title3 = '销售订单'
        this.title4 = '回款'
      }
    },
    // 选择成员
    handleSelectUser(user) {
      console.log(user)
    },
    // 切换tab
    handleClick(tab, event) {
      console.log(tab, event)
      this.activeName = tab.name
    },
    // 切换排行榜
    handleRankingClick(tab, event) {
      console.log(tab, event)
      this.rankingType = tab.name
    },
    // 格式化数字（百分比）
    formatNumber(value) {
      const max = this.rankingList.reduce((max, item) => Math.max(max, item.value), 0)
      const min = this.rankingList.reduce((min, item) => Math.min(min, item.value), Infinity)
      const total = max + min
      return (value / total) * 100
    },
    // 切换图表类型
    handleChartType(type) {
      this.chartType = type
      this.key = Math.random()
    },
    // 切换主题
    handleTheme(theme) {
      this.theme = theme
      this.$emit('changeTheme', theme)
      this.$nextTick(() => {
        this.key = Math.random()
      })
    },
    // 获取销售订单图表数据
    getSalesOrderChartData() {
      getStatisticsScreenGroupOrder({
        sm: undefined,
        em: undefined,
      }).then(response => {
        console.log(response)
        let { code, data, msg } = response
        if (code === 200) {
          let dataX = []
          let dataY = []
          data.forEach(item => {
            dataX.push(item.d)
            dataY.push(item.t)
          })
          this.$set(this, 'saleChartData', {
            xAxis: dataX,
            yAxis: dataY,
            tooltipTitle: '订单数量',
            smooth: true,
          })
        } else {
          this.$message.error(msg)
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
@import '~@/assets/styles/crm-statistics.scss';

.custom-search {
  padding-top: 10px;
  padding-bottom: 0;
}
</style>