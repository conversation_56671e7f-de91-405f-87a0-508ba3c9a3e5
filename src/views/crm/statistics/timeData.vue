<template>
    <div class="statistics-container">
        <el-row :gutter="20">
            <el-col :span="24">
                <div class="statistics-card">
                    <div class="statistics-card-header">
                        <el-tabs v-model="activeName" type="card" @tab-click="handleClick" class="statistics-card-tabs">
                            <el-tab-pane v-for="item in orderOptions" :key="item.value" :label="item.label"
                                :name="item.value"></el-tab-pane>
                        </el-tabs>
                    </div>
                    <div class="statistics-card-selected">
                        <div class="selected-box">
                            <div class="selected-item">
                                <span class="title">统计项：</span>
                                <el-select v-model="activeType" placeholder="请选择" size="small"
                                    style="margin-left: 15px; width: 200px">
                                    <el-option :label="item.label" :value="item.value" v-for="item in typeOptions"
                                        :key="item.value"></el-option>
                                </el-select>
                            </div>
                            <div class="selected-item">
                                <span class="title">指定成员：</span>
                                <popover-user ref="popoverUser" @selectUser="handleSelectUser" style="width: 150px" />
                            </div>
                            <div class="selected-item" v-if="activeName === 'q' || activeName === 'm' || activeName === 'd'">
                                <span class="title">{{ activeName === 'd' ? '选择月份：' : '选择年份：' }}</span>
                                <el-date-picker v-model="activeYear" :type="activeName === 'd' ? 'm' : 'y'" :placeholder="activeName === 'd' ? '选择月' : '选择年'" size="small" :format="activeName === 'd' ? 'yyyy-MM' : 'yyyy'"
                                    style="margin-left: 15px; width: 200px" @change="handleYearChange">
                                    <template #suffix>
                                        <i class="el-icon-date"></i>
                                    </template>
                                </el-date-picker>
                            </div>
                        </div>
                    </div>
                    <div class="statistics-card-orderChart">
                        <div class="statistics-card-orderChart-header">
                            <div class="statistics-card-orderChart-header-type">
                                <span class="statistics-card-orderChart-header-type-item"
                                    :class="{ active: chartType === 'bar' }" @click="handleChartType('bar')">柱状图</span>
                                <span class="statistics-card-orderChart-header-type-item"
                                    :class="{ active: chartType === 'line' }"
                                    @click="handleChartType('line')">折线图</span>
                            </div>
                            <div class="statistics-card-orderChart-header-title">{{ titleText }}</div>
                            <div class="flex align-center">
                                <span class="statistics-card-orderChart-header-tips">新增客户数量</span>
                            </div>
                        </div>
                        <div class="statistics-card-orderChart-content">
                            <line-chart :chart-data="chartData" height="400px" :theme="theme" 
                                v-if="chartType === 'line'" :key="key" />
                            <line-chart :chart-data="chartData" series-type="bar" height="400px" :theme="theme"
                                v-if="chartType === 'bar'" :key="key" />
                        </div>
                    </div>
                </div>
            </el-col>
        </el-row>
    </div>
</template>

<script>
import PopoverUser from '@/views/crm/components/PopoverUser/index'
import LineChart from '@/views/crm/components/dashboard/LineChart'
import { getStatisticsScreenGroupTime } from '@/api/crm/statistics'

export default {
    name: 'TimeData',
    components: {
        PopoverUser,
        LineChart
    },
    props: {
        // 组件接收的属性
        theme: {
            type: String,
            default: 'light'
        },
    },
    watch:{
        theme(newVal) {
            this.$set(this.chartData, 'theme', newVal);
        }
    },
    data() {
        return {
            // 组件数据
            chartType: 'bar',
            chartData: {
                xAxis: [],
                yAxis: [],
                tooltipTitle: '新增客户数量',
                smooth: true,
            },
            key: 1,
            orderOptions: [
                { label: '按年统计', value: 'y' },
                { label: '按季度统计', value: 'q' },
                { label: '按月份统计', value: 'm' },
                { label: '按天统计', value: 'd' },
            ],
            activeName: 'y',
            typeOptions: [
                { label: '新增客户数量', value: 0 },
                { label: '联系跟进次数', value: 1 },
                { label: '成交单数', value: 2 },
                { label: '成交总额', value: 3 },
                { label: '成交产品总数', value: 4 },
                { label: '销售利润', value: 5 },
                { label: '回款总额', value: 6 },
                { label: '欠款总额', value: 7 },
            ],
            activeType: 0,
            titleText: '2025年 所有成员 共新增1688个新客户',
            activeYear: null,
            activeDeptId: null, // 当前部门ID
            activeMonth: null,
        };
    },
    methods: {
        // 组件方法
        // 切换tab
        handleClick(tab, event) {
            console.log(tab, event)
            this.activeName = tab.name
            this.getChartData()
        },
        // 选择成员
        handleSelectUser(user) {
            console.log(user)
        },
        // 切换图表类型
        handleChartType(type) {
            this.chartType = type
            this.key = Math.random()
            this.getChartData()
        },
        // 获取图表数据
        async getChartData() {
            try {
                const response = await getStatisticsScreenGroupTime({
                    item: this.activeType,
                    creator: null,
                    deptId: this.activeDeptId || null,
                    method: this.activeName,
                    year: this.activeYear || null,
                    month: this.activeMonth || null,
                });
                if (response.code === 200) {
                    let dataX = []
                    let dataY = []
                    response.data.forEach(item => {
                        dataX.push(item.d)
                        dataY.push(item.t)
                    });
                    this.$set(this, 'chartData', {
                        xAxis: dataX,
                        yAxis: dataY,
                        tooltipTitle: '新增客户数量',
                        smooth: true,
                    });
                } else {
                    this.$message.error(response.message);
                }
            } catch (error) {
                console.error('获取图表数据失败:', error);
                this.$message.error('获取图表数据失败，请稍后重试');
            }
        },
        handleYearChange(year) {
            // this.activeYear = year;
            // this.getChartData();
        }
    },
    created() {
        // 组件创建后执行的代码
        this.getChartData();
    }
};
</script>

<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
@import '~@/assets/styles/crm-statistics.scss';

.statistics-container {
    .statistics-card {
        margin-bottom: 20px;

    }
}
</style>