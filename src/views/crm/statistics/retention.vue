<template>
    <div class="statistics-container">
        <div class="statistics-card">
            <div class="statistics-card-header">
                <el-tabs v-model="activeTypes" type="card" @tab-click="handleType" class="statistics-card-tabs">
                    <el-tab-pane v-for="item in typesOptions" :key="item.value" :label="item.label" :name="item.value">
                    </el-tab-pane>
                </el-tabs>
            </div>
            <div style="padding: 15px 0;">
                <el-select v-model="activeDate" placeholder="请选择" size="small" style="margin-left: 15px; width: 138px">
                    <el-option :label="item.label" :value="item.value" v-for="item in dateOptions"
                        :key="item.value"></el-option>
                </el-select>
                <span style="font-weight: 400; font-size: 12px; color: #999999; line-height: 20px; margin-left: 20px;">选择时间：</span>
                <el-date-picker v-model="timing" v-if="activeDate === 'day'" type="date" size="small"
                    format="yyyy-MM-dd" value-format="yyyy-MM-dd" placeholder="选择日"></el-date-picker>
                <el-date-picker v-model="timing" v-if="activeDate === 'week'" type="week" size="small"
                    format="yyyy 第 WW 周" placeholder="选择周"></el-date-picker>
                <el-date-picker v-model="timing" v-if="activeDate === 'month'" type="month" size="small"
                    format="yyyy-MM" value-format="yyyy-MM" placeholder="选择月"></el-date-picker>
                <el-quarter-picker v-model="timing" v-if="activeDate === 'quarter'" placeholder="选择季度" size="small" />
                <el-date-picker v-model="timing" v-if="activeDate === 'year'" type="year" size="small" format="yyyy"
                    value-format="yyyy" placeholder="选择年"></el-date-picker>
                <el-date-picker v-model="timing" v-if="activeDate === 'custom'" type="daterange" size="small"
                    range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                    :default-time="['00:00:00', '23:59:59']" :picker-options="{
                        disabledDate(time) {
                            return time.getTime() > Date.now()
                        }
                    }">
                </el-date-picker>
            </div>
        </div>
        <div class="statistics_table" style="margin-top: 15px;">
            <el-table :data="list" stripe size="small" style="width: 100%">
                <el-table-column type="index" label="序号" width="50px"></el-table-column>
                <el-table-column prop="productName" label="人员" width="200px"></el-table-column>
                <el-table-column prop="spec" label="所属部门" width="200px"></el-table-column>
                <el-table-column prop="amount" label="总数" width="200px"></el-table-column>
                <el-table-column prop="percentage" label="比例（总数占比）">
                    <template slot-scope="{ row }">
                        <el-progress :percentage="formatNumber(row.amount)" :text-inside="true" text-color="#fff"
                            :stroke-width="30" color="#2e73f3" :define-back-color="'#dee7f9'" />
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <div class="custom-pagination">
            <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
                :limit.sync="queryParams.pageSize" @pagination="getList" />
        </div>
    </div>
</template>
<script>
import ElQuarterPicker from '@/views/crm/components/ElQuarterPicker/index'
import { listDeptExcludeChild } from '@/api/system/dept'

export default {
    name: 'productSales',
    components: { ElQuarterPicker, },
    data() {
        return {
            queryParams: {
                name: ''
            },
            activeTypes: 'staff',
            typesOptions: [
                { label: '按人员统计', value: 'staff' },
                { label: '按部门统计', value: 'dept' },
            ],
            activeDate: 'month',
            dateOptions: [
                { label: '按天统计', value: 'day' },
                { label: '按周统计', value: 'week' },
                { label: '按月统计', value: 'month' },
                // { label: '按季度统计', value: 'quarter' },
                // { label: '按年统计', value: 'year' },
                { label: '自定义时间范围', value: 'custom' }
            ],

            timing: '',
            total: 10,
            list: [],
            queryParams: {
                pageNum: 1,
                pageSize: 10,
            },

        }
    },
    created() {
        this.list = [
            { id: 1, productName: '扁钢', spec: 'TN41*41*2.0*6', amount: 40849200 },
            { id: 2, productName: '光伏地桩', spec: 'TN41*41*2.0*6', amount: 38430000 },
            { id: 3, productName: '扁钢', spec: 'TN41*41*2.0*6', amount: 40849200 },
            { id: 4, productName: '光伏地桩', spec: 'TN41*41*2.0*6', amount: 38430000 },
            { id: 5, productName: '扁钢', spec: 'TN41*41*2.0*6', amount: 40849200 },
            { id: 6, productName: '光伏地桩', spec: 'TN41*41*2.0*6', amount: 38430000 },
            { id: 7, productName: '扁钢', spec: 'TN41*41*2.0*6', amount: 40849200 },
            { id: 8, productName: '光伏地桩', spec: 'TN41*41*2.0*6', amount: 38430000 },
            { id: 9, productName: '扁钢', spec: 'TN41*41*2.0*6', amount: 40849200 },
            { id: 10, productName: '光伏地桩', spec: 'TN41*41*2.0*6', amount: 38430000 },
        ]
        this.getDeptOptions()
    },
    methods: {
        // 获取部门列表
        async getDeptOptions() {
            const { data } = await listDeptExcludeChild(this.$store.state.user.info.deptId)
            if (data.length === 0) return
            this.deptOptions = data
        },
        handleQuery() {
            console.log(this.queryParams)
        },
        resetQuery() {
            this.queryParams = {}
            this.handleQuery()
        },
        // 切换分类
        handleType(item) {
            console.log(this.activeTypes)
            // this.activeTypes = item.value
        },
        // 选择成员
        handleSelectUser(user) {
            console.log(user)
        },
        // 格式化数字（百分比）
        formatNumber(value) {
            const max = this.list.reduce((max, item) => Math.max(max, item.amount), 0)
            const min = this.list.reduce((min, item) => Math.min(min, item.amount), Infinity)
            const total = max + min
            return Number(((value / total) * 100).toFixed(2))
        },
        // 分页查询
        getList() {


        },
    }
}
</script>
<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
@import '~@/assets/styles/crm-statistics.scss';

.custom-search {
    padding-top: 10px;
    padding-bottom: 0;
}
</style>