<template>
    <div class="statistics-container">
        <el-row :gutter="20">
            <el-col :span="24">
                <div class="statistics-card">
                    <div class="statistics-card-header">
                        <el-tabs v-model="activeName" type="card" @tab-click="handleClick" class="statistics-card-tabs">
                            <el-tab-pane v-for="item in orderOptions" :key="item.value" :label="item.label"
                                :name="item.value"></el-tab-pane>
                        </el-tabs>
                    </div>
                    <!-- <div class="statistics-card-selected">
                        <div class="selected-box">
                            <div class="selected-item">
                                <span class="title">统计项：</span>
                                <el-select v-model="activeType" placeholder="请选择" size="small"
                                    style="margin-left: 15px; width: 200px">
                                    <el-option :label="item.label" :value="item.value" v-for="item in typeOptions"
                                        :key="item.value"></el-option>
                                </el-select>
                            </div>
                            <div class="selected-item">
                                <span class="title">指定成员：</span>
                                <popover-user ref="popoverUser" @selectUser="handleSelectUser" style="width: 150px" />
                            </div>
                            <div class="selected-item" v-if="activeName === 'quarter' || activeName === 'month' || activeName === 'day'">
                                <span class="title">{{ activeName === 'day' ? '选择月份：' : '选择年份：' }}</span>
                                <el-date-picker v-model="activeYear" :type="activeName === 'day' ? 'month' : 'year'" :placeholder="activeName === 'day' ? '选择月' : '选择年'" size="small" :format="activeName === 'day' ? 'yyyy-MM' : 'yyyy'"
                                    style="margin-left: 15px; width: 200px" @change="handleYearChange">
                                    <template #suffix>
                                        <i class="el-icon-date"></i>
                                    </template>
</el-date-picker>
</div>
</div>
</div> -->
                    <div class="statistics-card-orderChart">
                        <div class="statistics-card-orderChart-header">
                            <div class="statistics-card-orderChart-header-type">
                                <!-- <span class="statistics-card-orderChart-header-type-item"
                                    :class="{ active: chartType === 'bar' }" @click="handleChartType('bar')">柱状图</span>
                                <span class="statistics-card-orderChart-header-type-item"
                                    :class="{ active: chartType === 'line' }"
                                    @click="handleChartType('line')">折线图</span> -->
                            </div>
                            <div class="statistics-card-orderChart-header-title"
                                style="display: flex; align-items: center; flex-direction: column;">
                                <div style="margin-bottom: 16px;">客户模块：根据《获得时间》按季度统计总数的增长率，以下为每季度分布图</div>
                                <div
                                    style="font-weight: 400; font-size: 12px; color: #666666; line-height: 20px; margin-bottom: 6px;">
                                    同比增长率：当前周期与去年同时期的对比增长率，如2019年3月成交总额150元，2018年3月成交总额为100元，则2019年3月同比增长率为：(150-100)/100*100%
                                    = 50%
                                </div>
                                <div style="font-weight: 400; font-size: 12px; color: #666666; line-height: 20px;">
                                    环比增长率：当前周期与上一周期的对比增长率，如2019年3月成交总额为150元，2019年2月成交总额为100元，则2019年3月环比增长率：(150-100)/100*100%
                                    = 50%
                                </div>
                            </div>
                            <div class="flex align-center">
                                <span class="statistics-card-orderChart-header-tips">新增客户数量</span>
                            </div>
                        </div>
                        <div class="statistics-card-orderChart-content">
                            <line-chart :chart-data="chartData" height="400px" :theme="theme" :key="key" />
                        </div>
                    </div>
                </div>
            </el-col>
        </el-row>
    </div>
</template>

<script>
import PopoverUser from '@/views/crm/components/PopoverUser/index'
import LineChart from '@/views/crm/components/dashboard/LineChart'
import * as echarts from 'echarts'
export default {
    name: 'TimeData',
    components: {
        PopoverUser,
        LineChart
    },
    props: {
        // 组件接收的属性
        theme: {
            type: String,
            default: 'light'
        },
    },
    watch: {
        theme(newVal) {
            this.$set(this.chartData, 'theme', newVal);
        }
    },
    data() {
        return {
            // 组件数据
            chartType: 'line',
            chartData: {
                xAxis: [],
                yAxis: [],
                tooltipTitle: '新增客户数量',
                smooth: false,
            },
            key: 1,
            orderOptions: [
                { label: '同比增长率', value: 'year' },
                { label: '环比增长率', value: 'quarter' },
            ],
            activeName: 'year',
            typeOptions: [
                { label: '新增客户数量', value: 'newCustomer' },
                { label: '联系跟进次数', value: 'followUp' },
                { label: '成交单数', value: 'transactionCount' },
                { label: '成交总额', value: 'totalSales' },
                { label: '成交产品总数', value: 'productCount' },
                { label: '销售利润', value: 'salesProfit' },
                { label: '回款总额', value: 'receivableTotal' },
                { label: '欠款总额', value: 'debtTotal' },
            ],
            activeType: 'newCustomer',
            titleText: '',

        };
    },
    methods: {
        // 组件方法
        // 切换tab
        handleClick(tab, event) {
            console.log(tab, event)
            this.activeName = tab.name
        },
        // 选择成员
        handleSelectUser(user) {
            console.log(user)
        },
        // 切换图表类型
        handleChartType(type) {
            this.chartType = type
            this.key = Math.random()
        },
    },
    created() {
        // 组件创建后执行的代码
        this.$set(this, 'chartData', {
            xAxis: ['1季度', '2季度', '3季度', '4季度'],
            yAxis: [20, -160, 170, 1300],
            tooltipTitle: '新增客户数量',
            smooth: false,
            seriesData: [
                {
                    data: [20, -160, 170, 1300],
                    type: this.chartType,
                    smooth: false,
                    itemStyle: {
                        color: '#A726EC',
                        borderColor: '#A726EC',
                        borderWidth: 2,
                        borderType: 'solid',
                    },
                    areaStyle: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {
                                offset: 0,
                                color: 'rgba(167, 38, 236, 0.18)'
                            },
                            {
                                offset: 1,
                                color: 'rgba(167, 38, 236, 0.18)'
                            }
                        ]),
                    },
                },
                {
                    data: [500, 120, -80, 1000],
                    type: this.chartType,
                    smooth: false,
                    itemStyle: {
                        color: '#F43F3F',
                        borderColor: '#F43F3F',
                        borderWidth: 2,
                        borderType: 'solid',
                    },
                    areaStyle: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {
                                offset: 0,
                                color: 'rgba(167, 38, 236, 0.18)'
                            },
                            {
                                offset: 1,
                                color: 'rgba(167, 38, 236, 0.18)'
                            }
                        ]),
                    },
                },
            ],
        })
    }
};
</script>

<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
@import '~@/assets/styles/crm-statistics.scss';

.statistics-container {
    .statistics-card {
        margin-bottom: 20px;

    }
}
</style>