<template>
    <div class="statistics-container">
        <div class="search_criteria" style="background-color: #FFFFFF; padding: 0 15px;">
            <popover-user ref="popoverUser" @selectUser="handleSelectUser" style="width: 138px" />
            <el-select v-model="activeName" placeholder="请选择" size="small" style="width: 138px; margin-left: 15px;">
                <el-option :label="item.label" :value="item.value" v-for="item in orderOptions"
                    :key="item.value"></el-option>
            </el-select>
            <el-date-picker v-model="timing" v-if="activeName == 'moon' || activeName == 'quarter'"
                style="margin-left: 15px;" type="year" size="small" format="yyyy" value-format="yyyy"
                placeholder="选择年"></el-date-picker>
            <el-date-picker v-model="timing" v-if="activeName === 'day'" type="month" size="small" format="yyyy-MM"
                value-format="yyyy-MM" style="margin-left: 15px;" placeholder="选择月"></el-date-picker>
        </div>
        <div class="statistics-card">
            <!-- <div class="statistics-card-header">
                <el-tabs v-model="activeName" type="card" @tab-click="handleClick" class="statistics-card-tabs">
                    <el-tab-pane v-for="item in orderOptions" :key="item.value" :label="item.label"
                        :name="item.value"></el-tab-pane>
                </el-tabs>
            </div> -->
            <div class="statistics-card-orderChart">
                <div class="statistics-card-orderChart-header">
                    <div class="statistics-card-orderChart-header-type">
                        <span class="statistics-card-orderChart-header-type-item"
                            :class="{ active: chartType === 'bar' }" @click="handleChartType('bar')">柱状图</span>
                        <span class="statistics-card-orderChart-header-type-item"
                            :class="{ active: chartType === 'line' }" @click="handleChartType('line')">折线图</span>
                        <span class="statistics-card-orderChart-header-type-item"
                            :class="{ active: chartType === 'pie' }" @click="handleChartType('pie')">饼状图</span>
                    </div>
                    <div class="statistics-card-orderChart-header-title"
                        style="display: flex; align-items: center; flex-direction: column;">
                        <div style="margin-bottom: 16px;">2025年《跟进客户数》共877个</div>
                    </div>
                    <div class="flex align-center">
                        <span class="statistics-card-orderChart-header-tips">跟进客户数</span>
                    </div>
                </div>
                <div class="statistics-card-orderChart-content">
                    <line-chart :chart-data="chartData" height="400px" :theme="theme" v-if="chartType === 'line'"
                        :key="key" />
                    <line-chart :chart-data="chartData" series-type="bar" height="400px" :theme="theme"
                        v-if="chartType === 'bar'" :key="key" />
                    <pie-chart :chart-data="pieChartData" series-type="pie" :radius="['0%', '70%']" :borderRadius="0"
                        :showLegend="true" height="400px" :theme="theme" v-if="chartType === 'pie'" :key="key" />
                </div>
            </div>
        </div>

    </div>
</template>

<script>
import PopoverUser from '@/views/crm/components/PopoverUser/index'
import LineChart from '@/views/crm/components/dashboard/LineChart'
import PieChart from '../components/dashboard/PieChart.vue';
export default {
    name: 'TimeLineChart',
    components: {
        PopoverUser,
        LineChart,
        PieChart
    },
    props: {
        // 组件接收的属性
        theme: {
            type: String,
            default: 'light'
        },
    },
    watch: {
        theme(newVal) {
            this.$set(this.chartData, 'theme', newVal);
        }
    },
    data() {
        return {
            // 组件数据
            chartType: 'line',
            chartData: {
                xAxis: [],
                yAxis: [],
                tooltipTitle: '新增客户数量',
                smooth: false,
            },
            key: 1,
            orderOptions: [
                // { label: '按年统计', value: 'year' },
                // { label: '按季度统计', value: 'quarter' },
                { label: '按月统计', value: 'moon' },
                { label: '按日统计', value: 'day' },
            ],
            activeName: 'moon',
            timing: '',
           
            pieChartData: {
                seriesData: [],
            }
        };
    },
    methods: {
        // 组件方法
        // 切换tab
        handleClick(tab, event) {
            console.log(tab, event)
            this.activeName = tab.name
        },
        // 选择成员
        handleSelectUser(user) {
            console.log(user)
        },
        // 切换图表类型
        handleChartType(type) {
            this.chartType = type
            // 组件创建后执行的代码
            if (this.chartType === 'line') {
                this.$set(this, 'chartData', {
                    xAxis: ['01-01', '01-02', '01-03', '01-04', '01-05', '01-06', '01-07', '01-08', '01-09', '01-10', '01-11', '01-12', '01-13', '01-14', '01-15', '01-16', '01-17', '01-18', '01-19', '01-20', '01-21'],
                    yAxis: [60, 80, 30, 50, 40, 60, 70, 90, 80, 100, 120, 100, 130, 150, 150, 190, 170, 180, 185, 181, 173],
                    tooltipTitle: '待办任务',
                    smooth: false,
                })
            }
            if (this.chartType === 'bar') {
                this.$set(this, 'chartData', {
                    xAxis: ['01-01', '01-02', '01-03', '01-04', '01-05', '01-06', '01-07', '01-08', '01-09', '01-10', '01-11', '01-12', '01-13', '01-14', '01-15', '01-16', '01-17', '01-18', '01-19', '01-20', '01-21'],
                    tooltipTitle: '待办任务',
                    smooth: false,
                    seriesData: [
                        
                        {
                            name: 'Email',
                            type: 'bar',
                            stack: 'Ad',
                            emphasis: {
                                focus: 'series'
                            },
                            data: [120, 132, 101, 134, 90, 230, 210, 245, 251, 335, 390, 330, 310, 320, 332, 301, 334, 390, 325, 300, 277]
                        },
                        {
                            name: 'Union Ads',
                            type: 'bar',
                            stack: 'Ad',
                            emphasis: {
                                focus: 'series'
                            },
                            data: [220, 182, 191, 234, 290, 330, 310, 125, 130, 110, 120, 150, 232, 261, 157, 147, 260, 287, 207, 237, 222]

                        },
                        {
                            name: 'Video Ads',
                            type: 'bar',
                            stack: 'Ad',
                            emphasis: {
                                focus: 'series'
                            },
                            data: [150, 232, 201, 154, 190, 330, 410, 335, 290, 320, 300, 277, 165, 170, 214, 231, 187, 269, 229, 218, 192]
                        },
                    ]
                })
            }

            if (this.chartType === 'pie') {
                this.$set(this, 'pieChartData', {
                    seriesData: [
                        { name: '2020年', value: 16 },
                        { name: '2021年', value: 26 },
                        { name: '2022年', value: 9 },
                        { name: '2023年', value: 19 },
                        { name: '2024年', value: 29 },
                    ],
                })
            }
            this.key = Math.random()
        },
    },
    created() {
        // 组件创建后执行的代码
        if (this.chartType === 'line') {
            this.$set(this, 'chartData', {
                xAxis: ['01-01', '01-02', '01-03', '01-04', '01-05', '01-06', '01-07', '01-08', '01-09', '01-10', '01-11', '01-12', '01-13', '01-14', '01-15', '01-16', '01-17', '01-18', '01-19', '01-20', '01-21'],
                yAxis: [60, 80, 30, 50, 40, 60, 70, 90, 80, 100, 120, 100, 130, 150, 150, 190, 170, 180, 185, 181, 173],
                tooltipTitle: '待办任务',
                smooth: false,
            })
        }
        if (this.chartType === 'bar') {
            this.$set(this, 'chartData', {
                xAxis: ['01-01', '01-02', '01-03', '01-04', '01-05', '01-06', '01-07', '01-08', '01-09', '01-10', '01-11', '01-12', '01-13', '01-14', '01-15', '01-16', '01-17', '01-18', '01-19', '01-20', '01-21'],
                tooltipTitle: '待办任务',
                smooth: false,
                series: [
                    {
                        name: 'Direct',
                        type: 'bar',
                        emphasis: {
                            focus: 'series'
                        },
                        data: [320, 332, 301, 334, 390, 330, 320]
                    },
                    {
                        name: 'Email',
                        type: 'bar',
                        stack: 'Ad',
                        emphasis: {
                            focus: 'series'
                        },
                        data: [120, 132, 101, 134, 90, 230, 210]
                    },
                    {
                        name: 'Union Ads',
                        type: 'bar',
                        stack: 'Ad',
                        emphasis: {
                            focus: 'series'
                        },
                        data: [220, 182, 191, 234, 290, 330, 310]
                    },
                    {
                        name: 'Video Ads',
                        type: 'bar',
                        stack: 'Ad',
                        emphasis: {
                            focus: 'series'
                        },
                        data: [150, 232, 201, 154, 190, 330, 410]
                    },
                    {
                        name: 'Search Engine',
                        type: 'bar',
                        data: [862, 1018, 964, 1026, 1679, 1600, 1570],
                        emphasis: {
                            focus: 'series'
                        },
                        markLine: {
                            lineStyle: {
                                type: 'dashed'
                            },
                            data: [[{ type: 'min' }, { type: 'max' }]]
                        }
                    },
                    {
                        name: 'Baidu',
                        type: 'bar',
                        barWidth: 5,
                        stack: 'Search Engine',
                        emphasis: {
                            focus: 'series'
                        },
                        data: [620, 732, 701, 734, 1090, 1130, 1120]
                    },
                    {
                        name: 'Google',
                        type: 'bar',
                        stack: 'Search Engine',
                        emphasis: {
                            focus: 'series'
                        },
                        data: [120, 132, 101, 134, 290, 230, 220]
                    },
                    {
                        name: 'Bing',
                        type: 'bar',
                        stack: 'Search Engine',
                        emphasis: {
                            focus: 'series'
                        },
                        data: [60, 72, 71, 74, 190, 130, 110]
                    },
                    {
                        name: 'Others',
                        type: 'bar',
                        stack: 'Search Engine',
                        emphasis: {
                            focus: 'series'
                        },
                        data: [62, 82, 91, 84, 109, 110, 120]
                    }
                ]
            })
        }

        this.$set(this, 'pieChartData', {
            seriesData: [
                { name: '2020年', value: 16 },
                { name: '2021年', value: 26 },
                { name: '2022年', value: 9 },
                { name: '2023年', value: 19 },
                { name: '2024年', value: 29 },
            ],
        })
    }
};
</script>

<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
@import '~@/assets/styles/crm-statistics.scss';

.statistics-container {
    .statistics-card {
        margin-bottom: 20px;

    }
}
</style>