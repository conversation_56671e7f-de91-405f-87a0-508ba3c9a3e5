<template>
  <div class="statistics_box">
    <div class="statistics_box_left" v-if="leftTabsShow" :class="{ 'activeTheme': theme === 'dark' }">
      <left-tabs :tabsData="tabsData" :activeTab="activeTab" @update:activeTab="activeTab = $event"
        @update:theme="theme = $event" :theme="theme"></left-tabs>
    </div>
    <div class="statistics_box_right">
      <data-screen v-if="activeTab === 'dataScreen'" @changeTheme="handleThemeChange"></data-screen>
      <productSales v-if="activeTab === 'productSales'"></productSales>
      <performanceRanking v-if="activeTab === 'performanceRanking'"></performanceRanking>
      <growth-rate v-if="activeTab === 'growthRate'"></growth-rate>
      <customerConversion v-if="activeTab === 'customerConversion'"></customerConversion>
      <taskStatistics v-if="activeTab === 'taskStatistics'"></taskStatistics>
      <followCustomers v-if="activeTab === 'followCustomers'"></followCustomers>
      <combinationComparison v-if="activeTab === 'combinationComparison'"></combinationComparison>
      <table-summary v-if="activeTab === 'tableSummary'"></table-summary>
      <table-comparison v-if="activeTab === 'tableComparison'"></table-comparison>
      <profit-margin v-if="activeTab === 'profitMargin'"></profit-margin>
      <repurchases-number v-if="activeTab === 'repurchasesNumber'"></repurchases-number>
    </div>
  </div>
</template>
<script>
import LeftTabs from '@/views/crm/components/LeftTabs/index.vue'
import dataScreen from './dataScreen.vue'
import productSales from './productSales.vue'
import performanceRanking from './performanceRanking.vue'
import growthRate from './growthRate.vue'
import customerConversion from './customerConversion.vue'
import taskStatistics from './taskStatistics.vue'
import followCustomers from './followCustomers.vue'
import combinationComparison from './combinationComparison.vue'
import tableSummary from './tableSummary.vue'
import tableComparison from './tableComparison.vue'
import profitMargin from './profitMargin.vue'
import repurchasesNumber from './repurchasesNumber.vue'

export default {
  components: {
    LeftTabs, dataScreen, productSales, performanceRanking, growthRate, customerConversion, taskStatistics, followCustomers, combinationComparison, tableSummary, tableComparison, profitMargin, repurchasesNumber
  },
  data() {
    return {
      tabsData: [
        { name: '数据大屏', value: 'dataScreen' },
        { name: '产品销量', value: 'productSales' },
        { name: '业绩排行', value: 'performanceRanking' },
        { name: '增长率统计', value: 'growthRate' },
        { name: '客户转化统计', value: 'customerConversion' },
        { name: '任务统计', value: 'taskStatistics' },
        { name: '跟进客户数', value: 'followCustomers' },
        { name: '组合对比统计', value: 'combinationComparison' },
        { name: '表格汇总统计', value: 'tableSummary' },
        { name: '表格对比统计', value: 'tableComparison' },
        { name: '利润率统计', value: 'profitMargin' },
        { name: '复购数统计', value: 'repurchasesNumber' },
      ],
      activeTab: 'dataScreen',
      leftTabsShow: true,
      theme: 'light',

    }
  },
  created() { },
  watch: {
    theme(newVal) {
      console.log('Theme changed to:', newVal);
    },
  },


  methods: {
    handleThemeChange(theme) {
      this.theme = theme
    }
  }
}
</script>
<style lang="scss" scoped>
.statistics_box {
  display: flex;
  justify-content: space-between;

  .statistics_box_left {
    width: 160px;
    min-height: 90vh;
    background: #F0F3F9;
    border: 1px solid #CBD6E2;
    flex-shrink: 0;

    &.activeTheme {
      background: #20184C;
      border: 1px solid #20184C;
    }
  }

  .statistics_box_right {
    flex: 1;
  }
}
</style>
