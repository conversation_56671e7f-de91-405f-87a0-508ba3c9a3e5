<template>
    <div class="newBox vh-85">
        <!-- 搜索 -->
        <div class="custom-search crm-search">
            <el-form ref="queryParams" :model="queryParams" size="small" inline class="custom-form-inline"
                @submit.native.prevent>
                <el-form-item label="客户名称" prop="name">
                    <el-input v-model="queryParams.name" placeholder="请输入客户名称" @change="handleQuery" clearable />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="handleQuery">搜索</el-button>
                    <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
                </el-form-item>
            </el-form>
        </div>
        <!-- 分类 -->
        <div class="classify flex bgwhite">
            <div class="classify-item" :class="{ active: item.value === activeType }" v-for="item in typeOptions"
                :key="item.value" @click="handleType(item)">{{ item.label }}</div>
        </div>
        <div class="statistics-container">
            <div class="search_criteria">
                <div class="search_criteria_left">
                    <el-select v-model="activeDate" placeholder="请选择" size="small"
                        style="margin-left: 15px; width: 138px">
                        <el-option :label="item.label" :value="item.value" v-for="item in dateOptions"
                            :key="item.value"></el-option>
                    </el-select>
                </div>
                <div class="search_criteria_right">
                    <div class="right_item">
                        <span class="title">选择时间：</span>
                        <el-date-picker v-model="timing" v-if="activeDate === 'day'" type="date" size="small"
                            format="yyyy-MM-dd" value-format="yyyy-MM-dd" placeholder="选择日"></el-date-picker>
                        <el-date-picker v-model="timing" v-if="activeDate === 'week'" type="week" size="small"
                            format="yyyy 第 WW 周" placeholder="选择周"></el-date-picker>
                        <el-date-picker v-model="timing" v-if="activeDate === 'month'" type="month" size="small"
                            format="yyyy-MM" value-format="yyyy-MM" placeholder="选择月"></el-date-picker>
                        <el-quarter-picker v-model="timing" v-if="activeDate === 'quarter'" placeholder="选择季度"
                            size="small" />
                        <el-date-picker v-model="timing" v-if="activeDate === 'year'" type="year" size="small"
                            format="yyyy" value-format="yyyy" placeholder="选择年"></el-date-picker>
                        <el-date-picker v-model="timing" v-if="activeDate === 'custom'" type="daterange" size="small"
                            range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                            :default-time="['00:00:00', '23:59:59']" :picker-options="{
                                disabledDate(time) {
                                    return time.getTime() > Date.now()
                                }
                            }">
                        </el-date-picker>

                    </div>
                   
                    <div class="right_item">
                        <span class="title">统计成员：</span>
                        <el-select v-model="activeBase" placeholder="请选择" size="small"
                            style="margin-left: 15px; width: 200px">
                            <el-option :label="item.label" :value="item.value" v-for="item in baseOptions"
                                :key="item.value"></el-option>
                        </el-select>
                    </div>
                    <div class="right_item">
                        <span class="title">是：</span>
                        <!-- <el-select v-model="activeDept" placeholder="请选择" size="small"
                            style="margin-left: 15px; width: 200px">
                            <el-option :label="item.deptName" :value="item.deptId" v-for="item in deptOptions"
                                :key="item.deptId"></el-option>
                        </el-select> -->
                        <popover-user ref="popoverUser" @selectUser="handleSelectUser" style="width: 150px" />
                    </div>
                </div>
            </div>
            <growthrate-data></growthrate-data>
        </div>
    </div>
</template>
<script>
import PopoverUser from '@/views/crm/components/PopoverUser/index'
import ElQuarterPicker from '@/views/crm/components/ElQuarterPicker/index'
import { listDeptExcludeChild } from '@/api/system/dept'
import growthrateData from './growthrateData.vue'

export default {
    name: 'performanceRanking',
    components: { PopoverUser, ElQuarterPicker, growthrateData },
    data() {
        return {
            queryParams: {
                name: ''
            },
            activeType: 'customer',
            typeOptions: [
                { label: '新增客户数增长率', value: 'customer' },
                { label: '成交总额增长率', value: 'amount' },
                { label: '回款总额增长率', value: 'payment' },
            ],
            activeDate: 'month',
            dateOptions: [
                { label: '无时间限制', value: 'unlimited' },
                { label: '按天统计', value: 'day' },
                { label: '按周统计', value: 'week' },
                { label: '按月统计', value: 'month' },
                { label: '按季度统计', value: 'quarter' },
                { label: '按年统计', value: 'year' },
                { label: '自定义时间范围', value: 'custom' }
            ],
            activeBase: 'enter',
            baseOptions: [
                { label: '录入人', value: 'enter' },
                { label: '原负责人', value: 'originalResponsible' },
                { label: '转交人', value: 'passOn' },
                { label: '负责人', value: 'responsibility' },
                { label: '首次跟进人', value: 'firstFollow' },
                { label: '最后跟进人', value: 'lastFollow' },
                { label: '参与人', value: 'participant' },
                { label: '参与添加人', value: 'participantAdd' },

            ],
            activeDept: '',
            deptOptions: [],
            timing: '',

        }
    },
    created() {
        // 获取部门列表
        this.getDeptOptions()
        console.log(this.$store.state.user.info.deptId)
    },
    methods: {
        // 获取部门列表
        async getDeptOptions() {
            const { data } = await listDeptExcludeChild(this.$store.state.user.info.deptId)
            if (data.length === 0) return
            this.deptOptions = data
        },
        handleQuery() {
            console.log(this.queryParams)
        },
        resetQuery() {
            this.queryParams = {}
            this.handleQuery()
        },
        // 切换分类
        handleType(item) {
            this.activeType = item.value
        },
        // 选择成员
        handleSelectUser(user) {
            console.log(user)
        },
    }
}
</script>
<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
@import '~@/assets/styles/crm-statistics.scss';

.custom-search {
    padding-top: 10px;
    padding-bottom: 0;
}

</style>