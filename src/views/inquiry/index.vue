<template>
  <div class="newBox bgcf9 vh-85">
    <!-- 搜索 -->
    <div class="custom-search" style="padding-top: 18px">
      <div class="flex"  :style="{ justifyContent: showSearch ? 'space-between' : 'flex-end' }">
        <el-form :model="queryParams" ref="queryForm" size="small" v-show="showSearch" :inline="true">
          <el-form-item label="产品名称" prop="productName">
            <el-input v-model="queryParams.productName" placeholder="请输入产品名称" clearable
              @keyup.enter.native="handleQuery" />
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select v-model="queryParams.status" placeholder="请选择状态" clearable @change="handleQuery">
              <el-option label="全部" :value="undefined" />
              <el-option label="待审核" :value="0" />
              <el-option label="已审核" :value="1" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" style="background-color: #2e73f3; border-color: #2e73f3"
              size="small" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
            <button type="button" class="custom-search-add pointer" @click="handleAdd">
              <i class="el-icon-plus"></i>
              新增内部询价
            </button>
          </el-form-item>
        </el-form>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
      </div>
    </div>

    <!-- 列表 -->
    <div class="p20">
      <el-tabs v-model="queryParams.identity" @tab-click="handleQuery">
        <el-tab-pane label="提交" :name="undefined"></el-tab-pane>
        <el-tab-pane label="审核" :name="'approval'"></el-tab-pane>
        <el-tab-pane label="抄送" :name="'cc'"></el-tab-pane>
      </el-tabs>
      <el-table v-loading="loading" stripe :data="list" row-key="id" style="width: 100%" class="custom-table"
        v-if="total > 0">
        <el-table-column align="center" type="index" label="序号" v-if="columns[0].visible"></el-table-column>
        <el-table-column align="center" prop="productName" label="产品名称" show-overflow-tooltip v-if="columns[1].visible"></el-table-column>
        <el-table-column align="center" prop="count" label="产品数量" show-overflow-tooltip v-if="columns[2].visible">
          <template slot-scope="{ row }">{{ row.count }}{{ row.unit }}</template>
        </el-table-column>
        <el-table-column align="center" prop="draw" label="图纸" show-overflow-tooltip v-if="columns[3].visible">
          <template slot-scope="{ row }">
            <el-button type="text" size="small" @click="handlePdf(row)" :disabled="!row.draw">查看详情</el-button>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="productParam" label="产品参数" show-overflow-tooltip v-if="columns[4].visible"></el-table-column>
        <el-table-column align="center" prop="requirement" label="包装要求" show-overflow-tooltip v-if="columns[5].visible"></el-table-column>
        <el-table-column align="center" prop="identities" label="审批人" show-overflow-tooltip v-if="columns[6].visible">
          <template slot-scope="{ row }">{{ handleReturnString(row, 'approval') }}</template>
        </el-table-column>
        <el-table-column align="center" prop="identities" label="抄送人" show-overflow-tooltip v-if="columns[7].visible">
          <template slot-scope="{ row }">{{ handleReturnString(row, 'cc') }}</template>
        </el-table-column>
        <el-table-column align="center" prop="customerName" label="客户名称" show-overflow-tooltip v-if="columns[8].visible"></el-table-column>
        <el-table-column align="center" prop="contacts" label="联系人" show-overflow-tooltip v-if="columns[9].visible"></el-table-column>
        <el-table-column align="center" prop="telephone" label="电话" show-overflow-tooltip
          v-if="queryParams.identity !== 'approval' && columns[10].visible"></el-table-column>
        <el-table-column align="center" prop="status" label="状态" show-overflow-tooltip v-if="columns[11].visible">
          <template slot-scope="{ row }">{{ row.status == 1 ? '已审核' : '待审核' }}</template>
        </el-table-column>
        <el-table-column align="center" prop="createBy" label="创建人" show-overflow-tooltip v-if="columns[12].visible"></el-table-column>
        <el-table-column align="center" prop="prop" label="操作" width="200">
          <template slot-scope="{ row }">
            <el-button type="info" plain @click="handleView(row)" size="mini">查看详情</el-button>
            <el-button type="primary" plain @click="handleReply(row)" size="mini"
              v-if="row.status == 0 && queryParams.identity == 'approval'">询价回复</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-empty v-else />
      <div class="custom-pagination">
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize" @pagination="getList" />
      </div>
    </div>

    <!-- 添加 -->
    <el-dialog v-dialogDragBox :title="title" :visible.sync="open" class="custom-dialog" width="1150px">
      <div class="p20">
        <el-form ref="form" :model="form" :rules="rules" label-width="6em">
          <el-row>
            <el-col :span="24">
              <el-form-item label="客户名称" prop="customerName">
                <el-input v-model="form.customerName" placeholder="请输入客户名称"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="联系人" prop="contacts">
                <el-input v-model="form.contacts" placeholder="请输入联系人"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="电话" prop="telephone">
                <el-input v-model="form.telephone" placeholder="请输入电话"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="产品名称" prop="productName">
                <el-input v-model="form.productName" placeholder="请输入产品名称"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="数量" prop="count">
                <el-input v-model="form.count" placeholder="请输入产品数量">
                  <template slot="suffix">
                    <el-select v-model="form.unit" filterable allow-create class="unitSelect">
                      <el-option v-for="item in unitOptions" :key="item" :label="item" :value="item"></el-option>
                    </el-select>
                  </template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="产品参数" prop="productParam">
                <el-input type="textarea" :rows="3" placeholder="请输入产品参数" v-model="form.productParam"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="包装要求" prop="requirement">
                <el-input type="textarea" :rows="3" placeholder="请输入包装要求" v-model="form.requirement"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="图纸" prop="draw">
                <file-upload isShowTip :fileSize="500" v-model="form.draw" :file-type="fileType" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="审核人" prop="approval">
                <el-cascader v-model="form.approval" :options="approvalsOptions" :props="approvalsProps" filterable
                  :show-all-levels="false" placeholder="请选择审批人" @change="handleChange('approval')"></el-cascader>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="抄送人" prop="cc">
                <el-cascader v-model="form.cc" :options="approvalsOptions" :props="ccProps" filterable
                  :show-all-levels="false" placeholder="请选择抄送人" @change="handleChange('cc')"
                  :disabled="companyId == '14'"></el-cascader>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <el-button @click="open = false" size="medium" style="width: 200px">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" size="medium" style="width: 200px"
          :disabled="projectBtn">新增内部询价</el-button>
      </div>
    </el-dialog>
    <!-- 详情 -->
    <el-dialog v-dialogDragBox title="查看询价详情" :visible.sync="viewOpen" class="custom-dialog">
      <el-descriptions title="询价产品信息" class="project_detail">
        <el-descriptions-item label="客户名称">{{ info.customerName }}</el-descriptions-item>
        <el-descriptions-item label="联系人" :span="queryParams.identity === 'approval' ? 2 : 1">{{ info.contacts
          }}</el-descriptions-item>
        <el-descriptions-item label="电话" v-if="queryParams.identity !== 'approval'">{{ info.telephone
          }}</el-descriptions-item>
        <el-descriptions-item label="产品名称">{{ info.productName }}</el-descriptions-item>
        <el-descriptions-item label="询价人">{{ info.createBy }}</el-descriptions-item>
        <el-descriptions-item label="数量">{{ info.count }}{{ info.unit }}</el-descriptions-item>
        <el-descriptions-item label="产品参数">{{ info.productParam }}</el-descriptions-item>
        <el-descriptions-item label="包装要求">{{ info.requirement }}</el-descriptions-item>
        <el-descriptions-item label="图纸">
          <el-link :type="info.draw ? 'primary' : 'info'" @click="handlePdf(info)" :disabled="!info.draw">查看详情</el-link>
        </el-descriptions-item>
      </el-descriptions>
      <div class="reporting_detail" v-if="info.status == 1">
        <div class="reporting_detail_title">审核回复信息</div>
        <el-divider></el-divider>
        <div class="reporting_detail_list">
          <div>审核人：{{ info.identities.find(item => item.identity == 'approval').identityName }}</div>
          <div>回复价格：{{ info.amount }}</div>
          <div>
            附件：
            <span @click="handleReplyFile(info)" style="color: #2e73f3; cursor: pointer">查看详情</span>
          </div>
          <div>回复时间：{{ info.updateTime }}</div>
        </div>
        <el-divider></el-divider>
      </div>
    </el-dialog>
    <!-- 更改状态 -->
    <el-dialog v-dialogDragBox title="询价回复" :visible.sync="replyOpen" class="custom-dialog" width="500px">
      <div class="p20">
        <el-form ref="replyForm" :model="replyForm" :rules="statusRules" label-width="5em">
          <el-form-item label="回复价格" prop="status">
            <el-input v-model="replyForm.amount" placeholder="请输入回复价格"></el-input>
          </el-form-item>
          <el-form-item label="附件" prop="attachment">
            <file-upload isShowTip :fileSize="500" v-model="replyForm.attachment" :file-type="fileType" />
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer">
        <el-button @click="replyOpen = false" size="medium">取 消</el-button>
        <el-button type="primary" @click="replySubmit" size="medium">确 定</el-button>
      </div>
    </el-dialog>

    <el-dialog v-dialogDragBox :title="fileTitle" :visible.sync="fileOpen" width="1150px" class="custom-dialog" append-to-body>
      <div style="padding: 0 20px">
        <ul class="el-upload-list el-upload-list--picture-card">
          <li tabindex="0" class="el-upload-list__item is-success" v-for="(item, index) in fileList" :key="index">
            <template v-if="typeFormat(item) === 'xlsx' || typeFormat(item) === 'xls' || typeFormat(item) === 'et'">
              <img class="el-upload-list__item-thumbnail" src="~@/assets/images/excel.jpg" alt="" />
              <span class="el-upload-list__item-actions">
                <span class="el-upload-list__item-preview" @click="handleExcelPreview(item)"
                  v-if="typeFormat(item) !== 'et'"><i class="el-icon-zoom-in"></i></span>
                <span class="el-upload-list__item-preview" @click="handleDownload(item)"><i
                    class="el-icon-download"></i></span>
              </span>
            </template>
            <template v-else-if="typeFormat(item) === 'pdf' || typeFormat(item) === 'PDF'">
              <img class="el-upload-list__item-thumbnail" src="~@/assets/images/pdf.png" alt="" />
              <span class="el-upload-list__item-actions">
                <span class="el-upload-list__item-preview" @click="handlePDFPreview(item)"><i
                    class="el-icon-zoom-in"></i></span>
                <span class="el-upload-list__item-preview" @click="handleDownload(item)"><i
                    class="el-icon-download"></i></span>
              </span>
            </template>
            <template v-else>
              <img class="el-upload-list__item-thumbnail" :src="item" alt="" />
              <span class="el-upload-list__item-actions">
                <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(item)"><i
                    class="el-icon-zoom-in"></i></span>
              </span>
            </template>
          </li>
        </ul>
      </div>
    </el-dialog>

    <el-dialog v-dialogDragBox :visible.sync="imgOpen" title="预览" width="1150px" class="custom-dialog" append-to-body>
      <div style="padding: 0 20px">
        <img :src="imgUrl" style="display: block; max-width: 100%; margin: 0 auto" />
      </div>
    </el-dialog>

    <!-- 预览图纸 -->
    <el-dialog v-dialogDragBox title="预览" :visible.sync="pdfOpen" width="1150px" class="custom-dialog" append-to-body>
      <div style="display: flex; justify-content: space-between; align-items: center; padding-bottom: 10px"
        v-if="pdfCount > 1">
        <el-button type="primary" size="small" @click="pdfCurrent--" :disabled="pdfCurrent < 2">
          <i class="el-icon-arrow-left"></i>
          上一页
        </el-button>
        <span>{{ pdfCurrent }} / {{ pdfCount }}</span>
        <el-button type="primary" size="small" @click="pdfCurrent++" :disabled="pdfCurrent >= pdfCount">
          下一页
          <i class="el-icon-arrow-right"></i>
        </el-button>
      </div>
      <div class="page-pdf">
        <Pdf :src="pdfUrl" :page="pdfCurrent" @num-pages="pdfCount = $event" />
      </div>
    </el-dialog>

    <!--预览Excel-->
    <el-dialog v-dialogDragBox title="预览" :visible.sync="excelOpen" width="1150px" class="custom-dialog" append-to-body
      :fullscreen="isFullscreen">
      <dialog-header slot="title" dialog-tittle="预览" :fullscreen="isFullscreen"
        @is-fullscreen="onFullscreen"></dialog-header>
      <div style="padding: 0; margin: -20px 0">
        <vue-office-excel :src="excelUrl" :style="excelStyle" :key="excelKey" />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { inquiryList, lastIdentity, inquiryAdd, inquiryReply } from '@/api/inquiry'
import { deptTreeSelect, listUser } from '@/api/system/user'
import VueOfficeExcel from '@vue-office/excel'
import '@vue-office/excel/lib/index.css'
import { isNumber, isNumberLength } from '@/utils/validate'

export default {
  name: 'Inquiry',
  components: { VueOfficeExcel },
  data() {
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        productName: '',
        status: undefined,
        identity: undefined
      },
      total: 0,
      list: [],
      loading: true,
      title: '',
      open: false,
      form: {},
      rules: {
        productName: [{ required: true, message: '请输入产品名称', trigger: 'blur' }],
        productParam: [{ required: true, message: '请输入产品参数', trigger: 'blur' }],
        // requirement: [{ required: true, message: '请输入包装要求', trigger: 'blur' }],
        // draw: [{ required: true, message: '请上传产品图纸', trigger: 'change' }],
        count: [
          { required: true, message: '请输入数量', trigger: 'blur' },
          { validator: isNumber, message: '请输入数字', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: 'blur' }
        ],
        approval: [{ required: true, message: '请选择审核人', trigger: 'change' }],
        telephone: [{ pattern: /^(1[3456789]\d{9}|0\d{2,3}-?\d{7,8})$/, message: '请输入正确的电话', trigger: 'blur' }]
      },
      viewOpen: false,
      info: {},
      replyOpen: false,
      replyForm: {},
      statusRules: {
        amount: [{ required: true, message: '请输入回复价格', trigger: 'blur' }]
      },
      projectBtn: false,
      fileType: ['pdf', 'xlsx', 'xls', 'et', 'jpg', 'jpeg', 'png'],
      unitOptions: ['吨', '千克', '个', '件', '套', '米', '支', '根', '袋', '托'],
      approvalsArr: [],
      approvalsOptions: [],
      approvalsProps: {
        multiple: true,
        expandTrigger: 'hover',
        emitPath: false
      },
      ccProps: {
        multiple: true,
        emitPath: false
      },
      ccArr: [],
      // pdf预览
      pdfOpen: false,
      pdfCurrent: 1,
      pdfCount: 0,
      pdfUrl: '',
      imgOpen: false,
      imgUrl: '',
      fileTitle: '',
      fileOpen: false,
      fileList: [],
      excelOpen: false,
      excelUrl: '',
      excelKey: 1,
      excelStyle: {
        width: '100%',
        height: 'calc(70vh - 50px)'
      },
      isFullscreen: false,
      // 列表显隐
      showSearch: true,
      // 列信息
      columns: [
        { key: 0, label: `序号`, visible: true },
        { key: 1, label: `产品名称`, visible: true },
        { key: 2, label: `产品数量`, visible: true },
        { key: 3, label: `图纸`, visible: true },
        { key: 4, label: `产品参数`, visible: true },
        { key: 5, label: `包装要求`, visible: true },
        { key: 6, label: `审批人`, visible: true },
        { key: 7, label: `抄送人`, visible: true },
        { key: 8, label: `客户名称`, visible: true },
        { key: 9, label: `联系人`, visible: true },
        { key: 10, label: `电话`, visible: true },
        { key: 11, label: `状态`, visible: true },
        { key: 12, label: `创建人`, visible: true },
      ],
    }
  },
  computed: {
    companyId() {
      return this.$store.state.user.companyId
    }
  },
  created() {
    this.getList()
    this.getApprovalsOptions()
  },
  methods: {
    // 回显审批人、抄送人
    handleReturnString(row, type = 'approval') {
      const arr = row.identities.filter(item => item.identity == type)
      if (!arr.length) return ''
      return arr.map(item => item.identityName).join(',')
    },
    // 查询部门、查询用户构造树
    async getApprovalsOptions() {
      const dept = await deptTreeSelect()
      const user = await listUser()
      this.salespersonOptions = user.rows || []
      const children = dept.data[0].children || []
      const deptData = [...children, ...[{ id: -1, label: '其他', value: -1 }]] || []
      const userData = user.rows || []
      const getChildren = data => {
        data.forEach(item => {
          item.value = item.id
          if (item.children) {
            getChildren(item.children)
          } else {
            item.children = []
          }
        })
      }
      getChildren(deptData)
      const addChildren = data => {
        data.forEach(item => {
          userData.forEach(user => {
            if (item.id === user.deptId && item.children) {
              item.children.push({
                id: user.userId,
                label: user.realName || user.nickName,
                value: user.userId,
                disabled: user.status == '1',
                userName: user.userName
              })
            }
            if (item.id === -1 && (!user.deptId || user.deptId === dept.data[0].id) && item.children) {
              item.children.push({
                id: user.userId,
                label: user.realName || user.nickName,
                value: user.userId,
                disabled: user.status == '1',
                userName: user.userName
              })
            }
          })
          if (item.children && item.children.length) {
            addChildren(item.children)
          }
        })
      }
      addChildren(deptData)
      this.approvalsOptions = deptData
    },
    // 列表
    getList() {
      this.loading = true
      const data = { ...this.queryParams }
      if (data.identity == 0) {
        data.identity = undefined
      }
      inquiryList(data).then(res => {
        const { code, msg, rows, total } = res
        if (code === 200) {
          this.list = rows
          this.total = total
          this.loading = false
        } else this.$message.error(msg)
      })
    },
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 重置搜索
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        productName: '',
        status: undefined,
        identity: undefined
      }
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 重置表单
    reset() {
      this.form = {
        count: undefined,
        draw: undefined,
        productName: undefined,
        productParam: undefined,
        requirement: undefined,
        unit: this.unitOptions[0],
        customerName: undefined,
        contacts: undefined,
        telephone: undefined,
        approval: [],
        cc: [],
        identities: []
      }
      this.resetForm('form')
    },
    // 查看图纸
    handlePdf(row) {
      this.fileTitle = '查看图纸'
      this.fileList = row.draw_oss && row.draw_oss.split(',')
      this.fileOpen = true
    },
    // 查看回复报价
    handleReplyFile(row) {
      this.fileTitle = '查看回复附件'
      this.fileList = []
      if (row.attachment) {
        row.attachment.split(',').forEach(el => {
          this.fileList.push(el.includes('http') || el.includes('https') ? el : this.imgPath + el)
        })
      }
      this.fileOpen = true
    },
    // 判断文件类型
    typeFormat(file) {
      const index = file.lastIndexOf('?')
      if (index !== -1) file = file.slice(0, index)
      const index1 = file.lastIndexOf('.')
      return file.substr(index1 + 1)
    },
    // 预览Excel
    handleExcelPreview(item) {
      this.excelUrl = item
      this.excelOpen = true
    },
    // 预览PDF
    handlePDFPreview(file) {
      this.pdfUrl = file
      this.pdfCurrent = 1
      this.pdfOpen = true
    },
    // 预览图片
    handlePictureCardPreview(file) {
      this.imgUrl = file
      this.imgOpen = true
    },
    // 添加
    handleAdd() {
      this.reset()
      this.title = '新增内部询价'
      lastIdentity().then(res => {
        const { code, data } = res
        if (code === 200) {
          if (data && data.length) {
            this.form.approval = data.filter(ite => ite.identity == 'approval').map(item => item.identityId)
            this.approvalsArr = data.filter(ite => ite.identity == 'approval')
            if (this.companyId != '14') {
              this.form.cc = data.filter(ite => ite.identity == 'cc').map(item => item.identityId)
              this.ccArr = data.filter(ite => ite.identity == 'cc')
            } else {
              this.form.cc = [155, 154]
              let arr = []
              arr.push(this.findInTree(this.approvalsOptions, 155))
              arr.push(this.findInTree(this.approvalsOptions, 154))
              this.ccArr = arr.map(item => {
                return {
                  identityId: item.id,
                  identityName: item.label,
                  identity: 'cc'
                }
              })
            }
          }
        }
      })
      this.open = true
    },
    // 选择审批人
    handleChange(type) {
      if (type == 'approval') {
        this.approvalsArr = []
        this.form.approval.forEach(el => {
          const approval = this.findInTree(this.approvalsOptions, el)
          let obj = {
            identityId: el,
            identityName: approval.label,
            identity: 'approval'
          }
          this.approvalsArr.push(obj)
        })
      } else if (type == 'cc') {
        this.ccArr = []
        this.form.cc.forEach(el => {
          const cc = this.findInTree(this.approvalsOptions, el)
          let obj = {
            identityId: el,
            identityName: cc.label,
            identity: 'cc'
          }
          this.ccArr.push(obj)
        })
      }
    },
    // 从树结构内找到相同id
    findInTree(tree, id) {
      for (let i = 0; i < tree.length; i++) {
        if (tree[i].id === id) {
          return tree[i]
        } else if (tree[i].children && tree[i].children.length) {
          const res = this.findInTree(tree[i].children, id)
          if (res) return res
        }
      }
    },
    // 查看详情
    handleView(row) {
      this.info = { ...row }
      this.viewOpen = true
    },
    // 询价回复
    handleReply(row) {
      this.replyForm = {
        inquiryId: row.id,
        amount: '',
        attachment: ''
      }
      this.replyOpen = true
    },
    // 提交
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.form.identities = [...this.approvalsArr, ...this.ccArr]
          inquiryAdd(this.form).then(res => {
            const { code, msg } = res
            if (code === 200) {
              this.$message.success('新增成功')
              this.getList()
              this.open = false
            } else this.$message.error(msg)
          })
        }
      })
    },
    // 回复询价
    replySubmit() {
      this.$refs.replyForm.validate(valid => {
        if (valid) {
          inquiryReply(this.replyForm).then(res => {
            const { code, msg } = res
            if (code === 200) {
              this.$message.success('更改成功')
              this.getList()
              this.replyOpen = false
            } else this.$message.error(msg)
          })
        }
      })
    },
    // 查看附件全屏
    onFullscreen(val) {
      this.isFullscreen = val
      this.excelStyle = {
        width: val ? '100vw' : '100%',
        height: val ? 'calc(100vh - 50px)' : 'calc(70vh - 50px)'
      }
      this.excelKey = Math.random()
    },
    // 下载
    handleDownload(item) {
      const filename = item.split('/').pop().split('?')[0]
      const xhr = new XMLHttpRequest()
      xhr.open('GET', item, true)
      xhr.responseType = 'blob'
      xhr.onload = () => {
        if (xhr.status === 200) {
          const blob = new Blob([xhr.response], { type: 'application/octet-stream' })
          const url = URL.createObjectURL(blob)
          const link = document.createElement('a')
          link.href = url
          link.download = filename
          link.click()
          URL.revokeObjectURL(url)
        }
      }
      xhr.send()
    }
  }
}
</script>

<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';

.p20 {
  padding: 20px;
}

.el-cascader {
  width: 100%;
}

.project_detail {
  padding: 0 20px;

  ::v-deep .el-descriptions__body {
    background: #f8f9fb;
    padding: 20px;
  }

  ::v-deep .el-descriptions__header {
    margin-bottom: 12px;
  }

  ::v-deep .el-descriptions__title {
    font-size: 14px;
    color: #666666;
    font-weight: 400;
  }

  ::v-deep .el-descriptions-item__container {
    align-items: center;
  }
}

.reporting_detail {
  padding: 20px;

  .reporting_detail_title {
    font-size: 14px;
    color: #666666;
    margin-bottom: 12px;
  }

  .el-divider--horizontal {
    margin: 0;
  }

  .reporting_detail_list {
    display: flex;
    justify-content: space-between;
    margin: 24px 0;
  }
}

.hover {
  transition: 0.2s;
  cursor: pointer;

  &:hover {
    transform: scale(1) !important;
  }
}

.unitSelect {
  width: 100px;
  margin: 1px 0;
  height: 38px;
  overflow: hidden;

  ::v-deep {
    .el-input__inner {
      border: 0 !important;
    }
  }
}
</style>
