<template>
  <div>
    <el-dialog v-dialogDragBox title="生产工艺" :visible.sync="open" width="800px" class="custom-dialog" @close="handleClose" append-to-body>
      <div style="padding: 0 20px">
        <el-form ref="form" :model="form" :rules="rules" label-width="6em" class="custom-form">
          <el-form-item label="生产数量" prop="quantity">
            <el-input v-model="form.quantity" placeholder="请输入数量">
              <template slot="suffix">{{ info.unit }}</template>
            </el-input>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn" @click="handleClose">关闭</el-button>
        <el-button class="custom-dialog-btn primary" @click="handleSubmit">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { isNumber, isNumberLength } from '@/utils/validate'
import { dispatchProduction } from '@/api/production'

export default {
  data() {
    return {
      open: false,
      info: {},
      form: {},
      rules: {
        quantity: [
          { required: true, message: '请输入生产数量', trigger: 'blur' },
          { validator: isNumber, message: '请输入正确的数量', trigger: ['change', 'blur'] },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: ['change', 'blur'] }
        ]
      }
    }
  },
  methods: {
    reset() {
      this.info = {}
      this.form = {
        quantity: undefined,
        taskOrderId: undefined
      }
      this.resetForm('form')
    },
    // 打开
    handleOpen(data = {}) {
      const { id, quantity, unit } = data
      if (!id) {
        this.$message.error('参数错误，请稍后重试')
        return
      }
      this.reset()
      this.info = { ...data }
      this.form = {
        taskOrderId: id,
        quantity
      }
      this.open = true
    },
    // 确定
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          dispatchProduction(this.form).then(res => {
            const { code, msg } = res
            if (code === 200) {
              this.$message.success('生产工单下发成功！')
              this.handleClose()
            } else this.$message.error(msg)
          })
        }
      })
    },
    // 关闭
    handleClose() {
      this.open = false
      this.$emit('callBack')
    }
  }
}
</script>
<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';
</style>
