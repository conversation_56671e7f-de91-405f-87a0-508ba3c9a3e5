<template>
  <div class="newBox bgcf9 vh-85">
    <!-- 搜索 -->
    <div class="custom-search flex" style="padding-top: 18px">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" @submit.native.prevent>
        <!-- 产品名称 -->
        <el-form-item label="产品名称" prop="productName">
          <el-input v-model="queryParams.productName" placeholder="请输入产品名称" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <!-- 产品编码 -->
        <el-form-item label="产品编码" prop="productCode">
          <el-input v-model="queryParams.productCode" placeholder="请输入产品编码" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <!-- 规格 -->
        <el-form-item label="规格" prop="specs">
          <el-input v-model="queryParams.specs" placeholder="请输入规格" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <!-- 型号 -->
        <el-form-item label="型号" prop="model">
          <el-input v-model="queryParams.model" placeholder="请输入型号" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <!-- 材质 -->
        <el-form-item label="材质" prop="materialQuality">
          <el-input v-model="queryParams.materialQuality" placeholder="请输入材质" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <!-- 表面 -->
        <el-form-item label="表面" prop="surface">
          <el-input v-model="queryParams.surface" placeholder="请输入表面" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <!-- 按钮 -->
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="small" @click="handleResetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="custom-card">
      <!-- 工单分类 -->
      <el-tabs class="custom-classify-tabs" v-model="queryParams.mark" @tab-click="handleClassifyChange">
        <el-tab-pane v-for="item in classifyOptions" :key="item.value" :label="item.label" :name="item.value"></el-tab-pane>
      </el-tabs>
      <!-- 下发状态 -->
      <el-tabs class="custom-status-tabs" v-model="queryParams.status" @tab-click="handleStatusChange">
        <el-tab-pane v-for="item in statusOptions" :key="item.value" :label="item.label" :name="item.value"></el-tab-pane>
      </el-tabs>
      <!-- 列表 -->
      <el-table v-loading="loading" ref="table" stripe :data="list" style="width: 100%" class="custom-table" v-if="total > 0">
        <!-- 序号 -->
        <el-table-column type="index" label="序号" width="50"></el-table-column>
        <!-- 产品名称 -->
        <el-table-column prop="productName" label="产品名称" align="center" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <span class="table-link" @click="handleProductView(row)">{{ row.productName }}</span>
          </template>
        </el-table-column>
        <!-- 图片 -->
        <el-table-column prop="image" label="图片" align="center" show-overflow-tooltip width="75">
          <template slot-scope="{ row }">
            <image-preview :src="row.product && formatProductImg(row.product)" :width="50" :height="50" />
          </template>
        </el-table-column>
        <!-- 规格 -->
        <el-table-column prop="specs" label="规格" align="center" show-overflow-tooltip>
          <template slot-scope="{ row }">{{ row.product && row.product.specs }}</template>
        </el-table-column>
        <!-- 材质 -->
        <el-table-column prop="materialQuality" label="材质" align="center" show-overflow-tooltip>
          <template slot-scope="{ row }">{{ row.product && row.product.materialQuality }}</template>
        </el-table-column>
        <!-- 表面处理 -->
        <el-table-column prop="surface" label="表面处理" align="center" show-overflow-tooltip>
          <template slot-scope="{ row }">{{ row.product && row.product.surface }}</template>
        </el-table-column>
        <!-- 单位 -->
        <el-table-column prop="unit" label="单位" align="center" show-overflow-tooltip></el-table-column>
        <!-- 生产工艺 -->
        <el-table-column prop="bomId" label="生产工艺" align="center" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <span class="table-link" @click="handleProcessView(row)">查看详情</span>
          </template>
        </el-table-column>
        <!-- 生产数量 -->
        <el-table-column prop="quantity" label="生产数量" align="center" show-overflow-tooltip></el-table-column>
        <!-- 合同来源 -->
        <el-table-column prop="serial" label="合同来源" align="center" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <span class="table-link" @click="handleContractSource(row)">{{ row.serial }}</span>
          </template>
        </el-table-column>
        <!-- 交货日期 -->
        <el-table-column prop="deliveryTime" label="交货日期" align="center" show-overflow-tooltip></el-table-column>
        <!-- 操作 -->
        <el-table-column label="操作" :width="queryParams.status === '0' ? 330 : 220" align="center">
          <template slot-scope="scope">
            <!-- 查看详情 -->
            <button type="button" class="table-btn" @click="handleDetail(scope.row)">查看详情</button>
            <!-- 更改标记 -->
            <button type="button" class="table-btn" @click="handleMarkChange(scope.row)">更改标记</button>
            <!-- 下发生产工单 -->
            <button type="button" class="table-btn primary" @click="handleIssueOrder(scope.row)" v-if="queryParams.status === '0'">下发生产工单</button>
          </template>
        </el-table-column>
      </el-table>
      <el-empty v-else :description="loading ? '加载中...' : '暂无数据'"></el-empty>
    </div>
    <!-- 分页 -->
    <div class="custom-pagination" style="padding: 0 20px" v-if="total > 0 && !loading">
      <pagination :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
    </div>
    <!-- 产品详情 -->
    <product-dialog ref="productInfo" />
    <!-- 生产工艺 -->
    <process-dialog ref="processInfo" @callBack="showProcess = false" v-if="showProcess" />
    <!-- 合同详情 -->
    <contract-dialog ref="contractInfo" @callBack="showContract = false" v-if="showContract" />
    <!-- 下发生产工单 -->
    <create ref="create" @callBack="handleBack" v-if="showCreate" />
    <!-- 更改标记 -->
    <change-mark ref="changeMark" @callBack="handleBack" v-if="showChangeMark" />
  </div>
</template>
<script>
import { getProductionList } from '@/api/production'
import ProductDialog from '@/views/public/product/dialog'
import ProcessDialog from '@/components/process'
import ContractDialog from '@/components/contract'
import Create from './create'
import ChangeMark from './changeMark'

export default {
  name: 'Produce',
  components: { ProductDialog, ContractDialog, ProcessDialog, Create, ChangeMark },
  data() {
    return {
      queryParams: {
        pageNum: 1, // 页码
        pageSize: 10, // 每页条数
        contractId: undefined, // 合同id
        isPush: 1, // 是否下发
        materialQuality: undefined, // 材质
        model: undefined, // 型号
        productCode: undefined, // 产品编码
        productName: undefined, // 产品名称
        specs: undefined, // 规格
        surface: undefined, // 表面
        status: '0', // 下发状态
        lower: false, // 下发状态
        mark: 'sc' // 标记
      },
      list: [], // 列表
      total: 0, // 总数
      loading: true, // 加载中
      // 工单分类
      classifyOptions: [
        { value: 'sc', label: '生产工单' },
        { value: 'ww', label: '委外工单' }
      ],
      // 下发状态
      statusOptions: [
        { value: '0', label: '未下发' },
        { value: '1', label: '已下发' }
      ],
      // 合同详情
      showContract: false,
      // 生产工艺
      showProcess: false,
      // 下发生产工单
      showCreate: false,
      // 更改标记
      showChangeMark: false
    }
  },
  created() {
    this.getList()
  },
  methods: {
    // 获取列表
    // prettier-ignore
    getList() {
      this.loading = true
      this.queryParams.lower = this.queryParams.status === '0' ? false : true
      const query = { ...this.queryParams }
      delete query.status
      getProductionList(query).then(res => {
        const { code, msg, rows } = res
        if (code === 200) {
          this.list = rows
          this.total = res.total
        } else this.$message.error(msg)
      }).finally(() => {
        this.loading = false
      })
    },
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 重置
    handleResetQuery() {
      this.resetForm('queryForm')
      this.getList()
    },
    // 工单分类切换
    handleClassifyChange(tab) {
      this.queryParams.mark = tab.name
      this.queryParams.status = '0'
      this.handleResetQuery()
    },
    // 下发状态切换
    handleStatusChange(tab) {
      this.queryParams.status = tab.name
      this.handleQuery()
    },
    // 查看产品详情
    handleProductView(row) {
      const product = row?.product || {}
      if (product.id) this.$refs.productInfo.handleView(product)
    },
    // 查看生产工艺
    handleProcessView(row) {
      this.showProcess = true
      this.$nextTick(() => {
        this.$refs.processInfo.handleView(row)
      })
    },
    // 查看合同来源
    handleContractSource(row) {
      this.showContract = true
      this.$nextTick(() => {
        this.$refs.contractInfo.handleView(row)
      })
    },
    // 查看详情
    handleDetail(row) {
      this.showProcess = true
      this.$nextTick(() => {
        this.$refs.processInfo.handleView(row)
      })
    },
    // 更改标记
    handleMarkChange(row) {
      this.showChangeMark = true
      this.$nextTick(() => {
        this.$refs.changeMark.handleOpen(row)
      })
    },
    // 下发生产工单
    handleIssueOrder(row) {
      this.showCreate = true
      this.$nextTick(() => {
        this.$refs.create.handleOpen(row)
      })
    },
    // 回调
    handleBack() {
      this.showCreate = false
      this.getList()
    }
  }
}
</script>
<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';
::v-deep {
  .custom-card {
    margin: 30px 20px 20px;
    border: 1px solid #dce3eb;
    border-radius: 5px;
    background-color: #fff;
  }
  .custom-classify-tabs {
    background-color: #ededed;
    display: flex;
    .el-tabs__header {
      margin-bottom: 0;
    }
    .el-tabs__nav-wrap {
      overflow: unset;
      .el-tabs__nav-scroll {
        overflow: unset;
      }
      .el-tabs__active-bar {
        display: none;
      }
      .el-tabs__item {
        padding: 0 58px;
        font-size: 14px;
        color: $font;
        height: 50px;
        line-height: 50px;
        &.is-active {
          background-color: #fff;
          font-size: 16px;
          height: 60px;
          line-height: 60px;
          border: 1px solid #dce3eb;
          border-bottom: none;
          border-radius: 5px 5px 0 0;
          margin-top: -10px;
        }
        &:nth-child(2) {
          margin-left: -1px;
        }
      }
      &::after {
        display: none;
      }
    }
  }
  .custom-status-tabs {
    .el-tabs__nav-wrap {
      .el-tabs__nav-scroll {
        padding-left: 20px;
      }
    }
  }
  .custom-table {
    border-radius: 0;
    margin-top: 10px;
    border-left: 0 !important;
    border-right: 0 !important;
    border-bottom: 0 !important;
  }
}
</style>
