<template>
  <div>
    <el-dialog v-dialogDragBox :title="title" :visible.sync="open" width="1150px" @close="handleClose()">
      <el-tabs v-model="activeName" @tab-click="handleClick" v-if="hasTabs">
        <el-tab-pane label="未下发" name="no"></el-tab-pane>
        <el-tab-pane label="已下发" name="yes"></el-tab-pane>
      </el-tabs>
      <el-table v-loading="loading" ref="table" stripe :data="list" style="width: 100%" class="custom-table">
        <!-- 序号 -->
        <el-table-column type="index" label="序号" width="50"></el-table-column>
        <!-- 产品名称 -->
        <el-table-column prop="productName" label="产品名称" align="center" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <span class="table-link" @click="handleProductView(row)">{{ row.productName }}</span>
          </template>
        </el-table-column>
        <!-- 图片 -->
        <el-table-column prop="image" label="图片" align="center" show-overflow-tooltip width="75">
          <template slot-scope="{ row }">
            <image-preview :src="row.product && formatProductImg(row.product)" :width="50" :height="50" />
          </template>
        </el-table-column>
        <!-- 规格 -->
        <el-table-column prop="specs" label="规格" align="center" show-overflow-tooltip>
          <template slot-scope="{ row }">{{ row.product && row.product.specs }}</template>
        </el-table-column>
        <!-- 材质 -->
        <el-table-column prop="materialQuality" label="材质" align="center" show-overflow-tooltip>
          <template slot-scope="{ row }">{{ row.product && row.product.materialQuality }}</template>
        </el-table-column>
        <!-- 表面处理 -->
        <el-table-column prop="surface" label="表面处理" align="center" show-overflow-tooltip>
          <template slot-scope="{ row }">{{ row.product && row.product.surface }}</template>
        </el-table-column>
        <!-- 单位 -->
        <el-table-column prop="unit" label="单位" align="center" show-overflow-tooltip></el-table-column>
        <!-- 生产工艺 -->
        <el-table-column prop="bomId" label="生产工艺" align="center" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <span class="table-link" @click="handleProcessView(row)">查看详情</span>
          </template>
        </el-table-column>
        <!-- 生产数量 -->
        <el-table-column prop="quantity" label="生产数量" align="center" show-overflow-tooltip></el-table-column>
        <!-- 交货日期 -->
        <el-table-column prop="deliveryTime" label="交货日期" align="center" show-overflow-tooltip></el-table-column>
      </el-table>
      <div slot="footer">
        <template v-if="hasTabs">
          <el-button class="custom-dialog-btn" @click="handleClose()">取 消</el-button>
          <el-button class="custom-dialog-btn primary" @click="handleSubmit()">下 派</el-button>
        </template>
        <template v-else>
          <el-button class="custom-dialog-btn primary" @click="handleClose()">关 闭</el-button>
        </template>
      </div>
    </el-dialog>
    <!-- 产品详情 -->
    <product-dialog ref="productInfo" />
    <!-- 生产工艺 -->
    <process-dialog ref="processInfo" @callBack="showProcess = false" v-if="showProcess" />
  </div>
</template>

<script>
import { dispatchOrder } from '@/api/production'
import ProductDialog from '@/views/public/product/dialog'
import ProcessDialog from '@/components/process'

export default {
  components: { ProductDialog, ProcessDialog },
  data() {
    return {
      open: false,
      loading: false,
      list: [],
      allList: [],
      showProcess: false,
      activeName: 'no',
      hasTabs: true,
      title: '下派生产任务单'
    }
  },
  methods: {
    handleClick(tab, event) {
      const { name } = tab
      this.activeName = name
      if (name === 'yes') this.list = this.allList.filter(item => item.isPush == 1)
      else this.list = this.allList.filter(item => item.isPush == 0)
    },
    // 打开
    handleOpen(list = []) {
      this.open = true
      this.loading = true
      this.allList = list
      const noList = list.filter(item => item.isPush == 0)
      const yesList = list.filter(item => item.isPush == 1)
      if (noList.length) {
        this.hasTabs = true
        this.list = noList
        this.title = '下派生产任务单'
      } else {
        this.hasTabs = false
        this.list = list
        this.title = '当前下单生产任务单已全部下发'
      }
      this.$nextTick(() => {
        this.loading = false
      })
    },
    // 关闭
    handleClose(refresh = false) {
      this.open = false
      this.$emit('callBack', refresh)
    },
    // 确定
    handleSubmit() {
      const ids = this.list.map(item => item.id) || []
      if (!ids.length) {
        this.$message.error('发生错误，请稍后重试')
        return
      }
      dispatchOrder({ ids }).then(res => {
        const { code, msg } = res
        if (code === 200) {
          this.$message.success('生产任务单下派成功！')
          this.handleClose(true)
        } else this.$message.error(msg)
      })
    },
    // 查看产品详情
    handleProductView(row) {
      const product = row?.product || {}
      if (product.id) this.$refs.productInfo.handleView(product)
    },
    // 查看生产工艺
    handleProcessView(row) {
      this.showProcess = true
      this.$nextTick(() => {
        this.$refs.processInfo.handleView(row)
      })
    }
  }
}
</script>
<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';
</style>
