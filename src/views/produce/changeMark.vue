<template>
  <div>
    <el-dialog v-dialogDragBox title="更改标记" :visible.sync="open" width="1150px" class="custom-dialog" @close="handleClose" append-to-body>
      <div style="padding: 0 20px">
        <div class="produce-product">
          <image-preview class="produce-product-img" :src="info.product && formatProductImg(info.product)" :width="52" :height="52" />
          <div class="produce-product-info">
            <div class="flex flex-wrap">
              <div class="produce-product-info-item">
                <span class="text link" @click="handleProductView()">{{ info.productName }}</span>
              </div>
              <div class="produce-product-info-item">
                <span class="title">生产数量</span>
                <span class="text">{{ info.quantity }}</span>
              </div>
              <div class="produce-product-info-item">
                <span class="title">合同来源</span>
                <span class="text link" @click="handleContractSource()">{{ info.serial }}</span>
              </div>
              <div class="produce-product-info-item">
                <span class="title">生产工艺</span>
                <span class="text link" @click="handleProcessView()">查看详情</span>
              </div>
            </div>
            <div class="flex flex-wrap">
              <div class="produce-product-info-item">
                <span class="title">规格</span>
                <span class="text">{{ info.product && info.product.specs }}</span>
              </div>
              <div class="produce-product-info-item">
                <span class="title">产品编码</span>
                <span class="text">{{ info.product && info.product.productCode }}</span>
              </div>
              <div class="produce-product-info-item">
                <span class="title">材质</span>
                <span class="text">{{ info.product && info.product.materialQuality }}</span>
              </div>
              <div class="produce-product-info-item">
                <span class="title">表面处理</span>
                <span class="text">{{ info.product && info.product.surface }}</span>
              </div>
              <div class="produce-product-info-item">
                <span class="title">单位</span>
                <span class="text">{{ info.unit }}</span>
              </div>
              <div class="produce-product-info-item" v-if="info.product && info.product.attribute">
                <span class="title">属性</span>
                <span class="text">{{ info.product && info.product.attribute }}</span>
              </div>
              <div class="produce-product-info-item" v-if="info.product && info.product.weight">
                <span class="title">重量</span>
                <span class="text">{{ info.product && info.product.weight + 'kg' }}</span>
              </div>
            </div>
          </div>
        </div>
        <el-form ref="form" :model="form" :rules="rules" label-width="6em" class="custom-form">
          <el-form-item label="产品标记" prop="mark">
            <el-radio-group v-model="form.mark">
              <el-radio v-removeAriaHidden v-for="item in markOptions" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn" @click="handleClose">关闭</el-button>
        <el-button class="custom-dialog-btn primary" @click="handleSubmit">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { changeMark } from '@/api/production'

export default {
  data() {
    return {
      open: false,
      info: {},
      form: {},
      rules: {
        mark: [{ required: true, message: '请选择产品标记', trigger: 'change' }]
      },
      markOptions: [
        { label: '生产工单', value: 'sc' },
        { label: '委外工单', value: 'ww' },
        { label: '采购产品', value: 'cg' }
      ]
    }
  },
  methods: {
    // 重置
    reset() {
      this.info = {}
      this.form = {
        mark: undefined,
        taskOrderId: undefined
      }
      this.resetForm('form')
    },
    // 打开
    handleOpen(data = {}) {
      const { id, mark } = data
      if (!id) {
        this.$message.error('参数错误，请稍后重试')
        return
      }
      this.reset()
      this.info = { ...data }
      this.form = {
        taskOrderId: id,
        mark
      }
      this.open = true
    },
    // 确定
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          changeMark(this.form).then(res => {
            const { code, msg } = res
            if (code === 200) {
              this.$message.success('标记更改成功！')
              this.handleClose()
            } else this.$message.error(msg)
          })
        }
      })
    },
    // 关闭
    handleClose() {
      this.open = false
      this.$emit('callBack')
    },
    // 产品详情
    handleProductView() {
      this.$parent.handleProductView(this.info)
    },
    // 合同来源
    handleContractSource() {
      this.$parent.handleContractSource(this.info)
    },
    // 生产工艺
    handleProcessView() {
      this.$parent.handleProcessView(this.info)
    }
  }
}
</script>

<style scoped lang="scss">
@import '@/assets/styles/custom-new.scss';
.produce-product {
  display: flex;
  align-items: flex-start;
  padding: 20px;
  background-color: #f0f3f9;
  border-radius: 5px;
  margin-bottom: 20px;
  &-img {
    flex: 0 0 52px;
    margin-right: 20px;
  }
  &-info {
    flex: 1;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    &-item {
      display: flex;
      align-items: center;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      .title {
        font-size: 12px;
        color: $disabled;
        margin-right: 5px;
      }
      .text {
        flex: 1;
        font-size: 14px;
        color: $font;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        &.link {
          color: $blue;
          cursor: pointer;
          &:hover {
            text-decoration: underline;
          }
        }
      }
    }
    &-item + &-item {
      margin-left: 30px;
    }
  }
}
</style>
