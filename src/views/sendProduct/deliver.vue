<template>
  <div>
    <el-dialog v-dialogDragBox title="选择联系人" :visible.sync="open" width="1150px" class="custom-dialog">
      <div style="padding: 0 20px">
        <el-form ref="form" :model="form" :rules="rules" label-width="80px">
          <el-row :gutter="10">
            <el-col :span="24" style="margin-bottom: 20px" v-if="list.length > 0 && !isCreate">
              <el-table :data="list" stripe class="custom-table" @row-click="selectContact">
                <!-- 选择 -->
                <el-table-column align="center" label="选择" width="55">
                  <template slot-scope="{ row }">
                    <el-radio v-removeAriaHidden v-model="form.phone" :label="row.phone"><span /></el-radio>
                  </template>
                </el-table-column>
                <!-- 昵称 -->
                <el-table-column prop="nickName" align="center" label="昵称" />
                <!-- 手机号 -->
                <el-table-column prop="phone" align="center" label="手机号" />
                <!-- 添加身份证号列 -->
                <el-table-column prop="idCard" align="center" label="身份证号">
                  <template slot-scope="{ row }">
                    {{ formatIdCard(row.idCard) }}
                  </template>
                </el-table-column>
              </el-table>
              <el-button type="primary" icon="el-icon-plus" size="small" style="margin-top: 20px" v-if="contactType == 'manualInput'" @click="handleAdd">添加联系人</el-button>
            </el-col>
            <template v-if="list.length === 0 || isCreate">
              <el-col :span="8">
                <el-form-item label="联系人" prop="nickName">
                  <el-input v-model="form.nickName" placeholder="请输入联系人"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="手机号" prop="phone">
                  <el-input v-model="form.phone" placeholder="请输入手机号"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="职务" prop="post">
                  <el-input v-model="form.post" placeholder="请输入职务"></el-input>
                </el-form-item>
              </el-col>
            </template>
            <el-col :span="4">
              <el-form-item label="实名认证" prop="certify">
                <el-switch v-model="form.certify" active-text="是" inactive-text="否"></el-switch>
              </el-form-item>
            </el-col>
            <template v-if="form.certify">
              <el-col :span="10">
                <el-form-item label="真实姓名" prop="realName">
                  <el-input v-model="form.realName" placeholder="请输入真实姓名"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="10">
                <el-form-item label="身份证号" prop="idCard">
                  <el-input v-model="form.idCard" placeholder="请输入身份证号"></el-input>
                </el-form-item>
              </el-col>
            </template>
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn" @click="handleCancle">取消</el-button>
        <el-button class="custom-dialog-btn primary" @click="handleSubmitContact" v-if="list.length === 0 || isCreate">{{ contactType == 'manualInput' ? '确定' : '确认并发送' }}</el-button>
        <el-button class="custom-dialog-btn primary" :class="{ disabled: !form.phone }" :disabled="!form.phone" @click="handleSubmit" v-if="!isCreate">确认并发送</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { contractDetail } from '@/api/purchase'
import { privateSupb, editlist } from '@/api/houtai/siyu/gongying'
import { supplier } from '@/api/system/company'
import { updateSupplierContact, addSupplierContact } from '@/api/system/user'
import { deliverySend } from '@/api/delivery'

export default {
  data() {
    return {
      open: false,
      list: [], // 联系人
      form: {}, // 选中的联系人
      rules: {
        nickName: [{ required: true, message: '请输入联系人', trigger: ['blur', 'change'] }],
        phone: [
          { required: true, message: '请输入手机号', trigger: ['blur', 'change'] },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号', trigger: ['blur', 'change'] }
        ],
        post: [{ required: true, message: '请输入职务', trigger: ['blur', 'change'] }],
        realName: [{ required: true, message: '请输入真实姓名', trigger: ['blur', 'change'] }],
        idCard: [
          { required: true, message: '请输入身份证号', trigger: ['blur', 'change'] },
          { pattern: /^[1-9]\d{5}(18|19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}(\d|X)$/, message: '请输入有效的身份证号', trigger: ['blur', 'change'] }
        ]
      }, // 验证规则
      contactType: '', // 联系人类型
      oldContact: {}, // 旧的联系人信息
      isCreate: false, // 是否是创建联系人
      companyId: undefined,
      deliveryId: undefined // 发货单ID
    }
  },
  methods: {
    reset() {
      this.form = {
        certify: false,
        uuid: undefined,
        nickName: undefined,
        phone: undefined,
        realName: undefined,
        idCard: undefined
      }
      this.resetForm('form')
      this.isCreate = false
    },
    // 打开
    async handleOpen(row = {}) {
      this.reset()
      const { contractId, id } = row
      this.deliveryId = id
      if (!contractId) return this.$message.error('参数错误，请刷新页面重试')
      // 1、获取合同信息，查询合同的客户是注册用户还是非注册用户
      const contractInfo = await contractDetail({ contractId })
      const { code, msg, data } = contractInfo
      if (code === 200) {
        const { contractType, seller, companyId } = data
        this.contactType = contractType
        // 这个是录入合同
        if (contractType == 'manualInput') {
          // 录入合同根据seller查找客户详情
          const customerInfo = await privateSupb({ id: seller })
          const { code: ccode, msg: cmsg, data: cdata } = customerInfo
          if (ccode === 200) {
            const { contactList = [] } = cdata
            this.list = contactList
            this.companyId = seller
            this.open = true
          } else this.$message.error(cmsg)
        } else {
          // 非录入合同根据companyId查找客户详情
          const customerInfo = await supplier({ id: companyId })
          const { code: ccode, msg: cmsg, data: cdata } = customerInfo
          if (ccode === 200) {
            const { contacts } = cdata
            this.list = contacts
            this.companyId = companyId
            this.open = true
          } else this.$message.error(cmsg)
        }
      } else this.$message.error(msg)
    },
    // 选择联系人
    selectContact(row) {
      this.form = { ...this.form, ...row }
      this.oldContact = { ...row }
    },
    // 添加联系人
    handleAdd() {
      this.form = {
        checked: false,
        invalid: false,
        nickName: undefined,
        phone: undefined,
        post: undefined,
        realName: undefined,
        idCard: undefined
      }
      this.resetForm('form')
      this.isCreate = true
    },
    // 取消
    handleCancle() {
      this.open = false
      this.$emit('callBack')
    },
    // 刷新
    async refresh() {
      this.reset()
      if (this.contactType == 'manualInput') {
        // 录入合同刷新客户联系人列表
        const customerInfo = await privateSupb({ id: this.companyId })
        const { code, msg, data } = customerInfo
        if (code === 200) {
          const { contactList } = data
          this.list = contactList
        } else this.$message.error(msg)
      } else {
        // 非录入合同刷新供应商联系人列表
        const customerInfo = await supplier({ id: this.companyId })
        const { code, msg, data } = customerInfo
        if (code === 200) {
          const { contacts } = data
          this.list = contacts
        } else this.$message.error(msg)
      }
    },
    // 确定
    handleSubmitContact() {
      this.$refs.form.validate(valid => {
        if (valid) {
          // 判断是客户新增联系人还是供应商新增联系人
          if (this.contactType == 'manualInput') {
            // 客户新增联系人
            const contacts = [...this.list]
            const obj = { checked: false, invalid: false, nickName: this.form.nickName, phone: this.form.phone, post: this.form.post }
            contacts.push(obj)
            const data = { id: this.companyId, contacts }
            editlist(data).then(res => {
              const { code, msg } = res
              if (code !== 200) return this.$message.error(msg)
              this.refresh()
            })
          } else {
            // 供应商新增联系人
            const { checked, invalid, nickName, phone, post, realName, idCard } = this.form
            const data = { checked, invalid, nickName, phone, post, realName, idCard }
            addSupplierContact(data).then(res => {
              const { code, msg } = res
              if (code !== 200) return this.$message.error(msg)
              this.refresh()
              this.form = { uuid: msg, ...data }
              this.handleSubmit()
            })
          }
        }
      })
    },
    // 确认并发送
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          const { certify, image, info, uuid } = this.form
          const formData = { deliveryId: this.deliveryId, certify, image, info, uuid }
          deliverySend(formData).then(res => {
            const { code, msg } = res
            if (code !== 200) return this.$message.error(msg)
            this.$message.success('发货成功')
            this.$emit('callBack')
            // 修改联系人
            if (this.form.realName !== this.oldContact.realName || this.form.idCard !== this.oldContact.idCard) {
              const { uuid, nickName, phone, realName, idCard } = this.form
              const data = { uuid, nickName, phone, realName, idCard }
              // 修改联系人
              updateSupplierContact(data).then(res => {
                const { code, msg } = res
                if (code !== 200) return this.$message.error(msg)
              })
            }
          })
        }
      })
    },
    // 添加身份证号脱敏方法
    formatIdCard(idCard) {
      if (!idCard) return ''
      return idCard.replace(/^(.{6})(.*)(.{4})$/, '$1********$3')
    }
  }
}
</script>
<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
</style>
