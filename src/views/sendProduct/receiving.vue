<template>
  <div class="newBox bgcf9 vh-85">
    <!-- 搜索 -->
    <div class="custom-search flex" style="padding-top: 18px">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" @submit.native.prevent>
        <!-- 合同编号 -->
        <el-form-item label="合同编号" prop="serial">
          <el-input v-model="queryParams.serial" placeholder="请输入合同编号" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <!-- 发货单编号 -->
        <el-form-item label="发货单编号" prop="deliveryNum">
          <el-input v-model="queryParams.deliveryNum" placeholder="请输入发货单编号" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <!-- 发货人 -->
        <el-form-item label="发货人" prop="createBy">
          <el-input v-model="queryParams.createBy" placeholder="请输入发货人" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <!-- 按钮 -->
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="small" @click="handleResetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="classify flex">
      <div class="classify-item" :class="{ active: item.value == queryParams.status }" v-for="item in options" :key="item.value" @click="handleStatus(item)">{{ item.label }}</div>
    </div>
    <!-- 表格数据 -->
    <div class="tableBox">
      <el-table v-loading="loading" ref="table" stripe :data="list" row-key="id" style="width: 100%" class="custom-table">
        <el-table-column align="center" type="index" label="序号" width="50"></el-table-column>
        <el-table-column align="center" prop="deliveryNum" label="发货单编号" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" label="合同来源" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <span class="table-link" @click="handleContractSource(row)">{{ row.contractNum }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="createBy" label="发货人" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" prop="createTime" label="发货时间"></el-table-column>
        <el-table-column align="center" prop="status" label="状态">
          <template slot-scope="{ row }"><div v-html="formatStatus(row.status)"></div></template>
        </el-table-column>
        <el-table-column align="center" label="操作">
          <template slot-scope="{ row }">
            <button type="button" class="table-btn" @click="handleView(row)">查看详情</button>
          </template>
        </el-table-column>
      </el-table>

      <div class="custom-pagination">
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
      </div>
    </div>
    <!-- 合同详情 -->
    <contract-dialog ref="contractInfo" @callBack="showContract = false" v-if="showContract" />
    <!-- 发货清单详情 -->
    <detail ref="detail" @callBack="showDetail = false" v-if="showDetail" />
  </div>
</template>
<script>
import { deliveryTakeList } from '@/api/delivery'
import ContractDialog from '@/components/contract'
import Detail from './detail'

export default {
  name: 'Receiving',
  components: { ContractDialog, Detail },
  data() {
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        createBy: undefined,
        serial: undefined,
        deliveryNum: undefined,
        buyerName: undefined,
        status: -1
      },
      list: [],
      total: 0,
      loading: false,
      options: [
        { label: '所有发货清单', value: -1, color: '#999' },
        { label: '待发货', value: 0, color: '#f65656' },
        { label: '已发货', value: 1, color: '#999' },
        { label: '已收货', value: 2, color: '#f35d09' },
        { label: '退货中', value: 3, color: '#f35d09' },
        { label: '已退货', value: 4, color: '#f35d09' }
      ],
      showContract: false,
      showDetail: false
    }
  },
  created() {
    this.getList()
  },
  methods: {
    // 获取列表
    // prettier-ignore
    getList() {
      this.loading = true
      const query = { ...this.queryParams, status: this.queryParams.status == -1 ? undefined : this.queryParams.status }
      deliveryTakeList(query).then(res => {
        const { code, msg, rows, total } = res
        if (code !== 200) return this.$message.error(msg)
        this.list = rows
        this.total = total
      }).finally(() => {
        this.loading = false
      })
    },
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 重置
    handleResetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 切换状态
    handleStatus(item) {
      this.queryParams.status = item.value
      this.handleResetQuery()
    },
    // 格式化状态
    formatStatus(status) {
      const obj = this.options.find(item => item.value === status)
      return obj ? `<span style="color: ${obj.color}">${obj.label}</span>` : ''
    },
    // 合同详情
    handleContractSource(row) {
      this.showContract = true
      this.$nextTick(() => {
        this.$refs.contractInfo.handleView(row)
      })
    },
    // 查看详情
    handleView(row) {
      this.showDetail = true
      this.$nextTick(() => {
        this.$refs.detail.handleOpen(row, 'receiving')
      })
    }
  }
}
</script>
<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
.tableBox {
  padding: 20px;
}
</style>
