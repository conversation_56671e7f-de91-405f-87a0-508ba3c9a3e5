<template>
  <div class="newBox bgcf9 vh-85">
    <!-- 搜索 -->
    <div class="custom-search flex">
      <div class="flex">
        <div class="custom-search-form flex">
          <input type="text" v-model="queryParams.keyword" placeholder="请输入合同编号/采购商名称" class="custom-search-input" @keyup.enter="handleQuery" />
          <button type="button" class="custom-search-button pointer" @click="handleQuery">
            <i class="el-icon-search"></i>
            搜索
          </button>
        </div>
      </div>
    </div>

    <!-- 分类 -->
    <div class="classify flex">
      <div class="classify-item" :class="{ active: item.id === queryParams.status }" v-for="item in categoryList" :key="item.id" @click="handleCategory(item)">
        {{ item.name }}
      </div>
    </div>

    <!-- 表格数据 -->
    <div class="tableBox">
      <el-table v-loading="loading" ref="table" stripe :data="list" row-key="id" :key="key" style="width: 100%" class="custom-table custom-table-cell5">
        <el-table-column align="center" type="index" label="序号" width="50"></el-table-column>
        <el-table-column align="center" prop="productName" label="发货产品" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <span class="table-link" @click="handleView(row)">查看详情</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="deliveryNum" label="发货单编号" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" label="合同来源" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <span class="table-link" @click="handleContractSource(row)">{{ row.contractNum }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="createBy" label="发货人" show-overflow-tooltip width="100"></el-table-column>
        <el-table-column align="center" prop="createTime" label="发货时间" width="120"></el-table-column>
        <el-table-column align="center" prop="status" label="状态" width="100">
          <template slot-scope="{ row }">
            <template>
              <span v-if="row.status == 0" style="color: #f65656">待发货</span>
              <span v-if="row.status == 1" style="color: #999">已发货</span>
              <span v-if="row.status == 2" style="color: #f35d09">已收货</span>
              <span v-if="row.status == 3" style="color: #f35d09">退货中</span>
              <span v-if="row.status == 4" style="color: #f35d09">已退货</span>
            </template>
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作">
          <template slot-scope="{ row }">
            <button type="button" class="table-btn" @click="handleView(row)">查看详情</button>
            <button type="button" class="table-btn primary hasbg" @click="handleCreateCar(row)">新建派车单</button>
            <button type="button" class="table-btn danger" @click="handleConfirmReturn(row)" v-if="row.status == 3">确认退货</button>
            <button type="button" class="table-btn danger hasbg" @click="handleDelete(row)" v-if="row.status != 1">删除</button>
          </template>
        </el-table-column>
      </el-table>

      <div class="custom-pagination">
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
      </div>
    </div>
    <!-- 发货清单详情 -->
    <detail ref="detail" @callBack="showDetail = false" v-if="showDetail" />
    <!-- 合同详情 -->
    <contract-dialog ref="contractInfo" @callBack="showContract = false" v-if="showContract" />
    <!-- 新增派车单 -->
    <create-dialog ref="createDialog" :isProps="false" @callBack="handleCallBack" v-if="isCreate" />
  </div>
</template>

<script>
import ContractDialog from '@/components/contract'
import { deliveryList, deliveryDelete, deliveryConfirmReturn } from '@/api/delivery'
import CreateDialog from '@/views/sendCar/create'
import Detail from './detail'

export default {
  name: 'SendProduct',
  components: { ContractDialog, CreateDialog, Detail },
  data() {
    return {
      key: 1,
      // 搜索条件
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        status: -1,
        createBy: undefined,
        serial: undefined,
        deliveryNum: undefined,
        buyerName: undefined,
        status: -1
      },
      // 加载
      loading: false,
      // 列表数据
      list: [],
      // 总条数
      total: 2,
      // 分类数据
      categoryList: [
        { id: -1, name: '所有发货清单' },
        { id: 0, name: '待发货' },
        { id: 1, name: '已发货' },
        { id: 2, name: '已收货' },
        { id: 3, name: '退货中' },
        { id: 4, name: '已退货' }
      ],
      // 详情
      showDetail: false,
      // 合同详情
      showContract: false,
      isCreate: false
    }
  },
  created() {
    this.getList()
  },
  methods: {
    // 查询数据
    async getList() {
      this.loading = true
      const query = { ...this.queryParams }
      if (query.status === -1) query.status = undefined
      const res = await deliveryList(query)
      if (res.code === 200) {
        this.list = res.rows
        this.loading = false
        this.total = res.total
        this.key = Math.random()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 切换分类
    handleCategory(item) {
      this.queryParams.status = item.id
      this.handleQuery()
    },
    // 打开
    async handleView(row) {
      this.showDetail = true
      this.$nextTick(() => {
        this.$refs.detail.handleOpen(row)
      })
    },
    // 查看合同来源
    handleContractSource(row) {
      this.showContract = true
      this.$nextTick(() => {
        this.$refs.contractInfo.handleView(row)
      })
    },
    // 新建派车单
    handleCreateCar(row) {
      this.isCreate = true
      this.$nextTick(() => {
        this.$refs.createDialog.handleOpen(row)
      })
    },
    // 回调
    handleCallBack() {
      this.isCreate = false
      this.getList()
    },
    // 确认退货
    handleConfirmReturn(row) {
      const { id } = row
      if (!id) return this.$message.error('参数错误，请稍后再试')
      this.$confirm('确认退货', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deliveryConfirmReturn({ id }).then(res => {
          const { code, msg } = res
          if (code !== 200) return this.$message.error(msg)
          this.$message.success('退货成功')
          this.getList()
        })
      })
    },
    // 删除
    handleDelete(row) {
      const { id } = row
      if (!id) return this.$message.error('参数错误，请稍后再试')
      this.$confirm('确认删除', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deliveryDelete({ id }).then(res => {
          const { code, msg } = res
          if (code !== 200) return this.$message.error(msg)
          this.$message.success('删除成功')
          this.getList()
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
.tableBox {
  padding: 20px;
}
.custom-search {
  align-items: center;
  justify-content: space-between;
}
.custom-search-tip {
  background-color: #fef0f0;
  color: #f56c6c;
  padding: 10px 20px;
  font-size: 20px;
  display: inline-flex;
  align-items: center;
  span {
    font-size: 14px;
    margin-left: 10px;
    margin-right: 30px;
  }
}
</style>
