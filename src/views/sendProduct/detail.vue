<template>
  <div>
    <el-dialog v-dialogDragBox title="发货清单详情" :visible.sync="open" width="1150px" class="custom-dialog" @close="handleCallBack">
      <div style="padding: 0 20px">
        <div class="print" v-if="!infoType">
          <div class="print-item" @click="handleSend(info)" v-if="info.status < 2">
            <i class="el-icon-position"></i>
            立即发货
          </div>
          <div class="print-item" @click="handleSendRecording(info)" v-if="info.status == 1">
            <i class="el-icon-chat-line-square"></i>
            发货记录
          </div>
          <div class="print-item" @click="handleAutonym(info)" v-if="info.status == 2 && info.certify">
            <i class="el-icon-s-check"></i>
            认证信息
          </div>
        </div>
        <div class="info">
          <div class="info_item">
            发货清单编号：
            <span>{{ info.deliveryNum }}</span>
          </div>
          <div class="info_item">
            发货时间：
            <span>{{ info.createTime }}</span>
          </div>
          <div class="info_item">
            发货人：
            <span>{{ info.createBy }}</span>
          </div>
          <div class="info_item">
            合同来源：
            <span class="pointer" style="color: #2e73f3" @click="handleContractSource(info)">{{ info.contractNum }}</span>
          </div>
        </div>
        <div class="table_tips">
          采购方/收货人：
          <span>{{ info.buyerName }}</span>
        </div>
        <el-table ref="table" stripe :data="info.details" row-key="id" style="width: 100%; border-radius: 0 0 5px 5px" class="custom-table custom-table-cell5">
          <el-table-column align="center" type="index" label="序号" width="50"></el-table-column>
          <el-table-column align="center" prop="productName" label="产品名称" show-overflow-tooltip>
            <template slot-scope="{ row }">
              <span class="table-link pointer" @click="handleDetail(row)">{{ row.productName }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="specs" label="规格" show-overflow-tooltip>
            <template slot-scope="{ row }">
              <span>{{ row.product.specs }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="materialQuality" label="材质" show-overflow-tooltip>
            <template slot-scope="{ row }">
              <span>{{ row.product.materialQuality }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="surface" label="表面处理" show-overflow-tooltip>
            <template slot-scope="{ row }">
              <span>{{ row.product.surface }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="productCode" label="产品编码" show-overflow-tooltip>
            <template slot-scope="{ row }">
              <span>{{ row.product.productCode }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="unit" label="产品单位" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="saleQuantity" label="合同销售数量" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="quantity" label="发货数量" show-overflow-tooltip></el-table-column>
        </el-table>
      </div>
    </el-dialog>
    <!-- 产品详情 -->
    <product-dialog ref="productInfo"></product-dialog>
    <!-- 立即发货 -->
    <deliver ref="deliver" @callBack="handleCallBack" v-if="deliverOpen"></deliver>
    <!-- 发货记录 -->
    <send-log ref="sendLog" @callBack="logOpen = false" v-if="logOpen"></send-log>
    <!--   认证信息 -->
    <el-dialog v-dialogDragBox title="认证信息" :visible.sync="certifyOpen" width="750px" class="custom-dialog">
      <div class="cretifyBox">
        <div class="cretifyImg">
          <image-preview is-list :src="certifyInfo.faceImage" :width="120" :height="120" />
        </div>
        <div class="cretifyInfo">
          <div class="cretifyItem">
            <span>姓名：</span>
            <b>{{ certifyInfo.certName }}</b>
          </div>
          <div class="cretifyItem">
            <span>电话：</span>
            <b>{{ certifyInfo.mobile }}</b>
          </div>
          <div class="cretifyItem">
            <span>身份证号：</span>
            <b>{{ certifyInfo.certNo }}</b>
          </div>
        </div>
      </div>
      <span slot="footer">
        <button class="custom-dialog-btn primary" @click="certifyOpen = false">关闭</button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import ProductDialog from '@/views/public/product/dialog'
import { deliveryDetail } from '@/api/delivery'
import deliver from './deliver'
import { contractDetail } from '@/api/purchase'
import sendLog from './log'

export default {
  components: { ProductDialog, deliver, sendLog },
  data() {
    return {
      deliveryId: undefined,
      info: {},
      open: false,
      deliverOpen: false,
      logOpen: false,
      infoType: undefined,
      // 认证信息
      certifyInfo: {},
      certifyOpen: false
    }
  },
  methods: {
    // 产品详情
    handleDetail(item, type) {
      const info = item?.product || item
      this.$refs.productInfo.handleView(info, type)
    },
    // 合同详情
    handleContractSource(row) {
      this.$parent.handleContractSource(row)
    },
    handleOpen(row, type = undefined) {
      if (!row.id) return this.$message.error('参数错误，请稍后重试')
      this.infoType = type
      const deliveryId = row.id
      deliveryDetail({ deliveryId }).then(async res => {
        const { data, code, msg } = res
        if (code == 200) {
          const contractInfo = await contractDetail({ contractId: data.contractId })
          const { data: cdata } = contractInfo
          const { contractType, sellerName, buyerName } = cdata
          if (contractType == 'manualInput') this.info = { ...data, buyerName: sellerName }
          else this.info = { ...data, buyerName }
          this.deliveryId = deliveryId
          this.open = true
        } else this.$message.error(msg)
      })
    },
    // 立即发货
    handleSend(row) {
      this.deliverOpen = true
      this.$nextTick(() => {
        this.$refs.deliver.handleOpen(row)
      })
    },
    // 发货记录
    handleSendRecording(row) {
      this.logOpen = true
      this.$nextTick(() => {
        this.$refs.sendLog.handleOpen(row)
      })
    },
    // 认证信息
    handleAutonym(info = {}) {
      this.certifyInfo = info.certify || {}
      this.certifyOpen = true
    },
    // 回调
    handleCallBack() {
      this.deliverOpen = false
      this.open = false
      this.$emit('callBack')
    }
  }
}
</script>
<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
.info {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-bottom: 20px;
  .info_item {
    margin-right: 48px;
    font-weight: 400;
    font-size: 14px;
    color: #666666;
    span {
      font-weight: 500;
      font-size: 14px;
      color: #333;
    }
  }
}
.table_tips {
  height: 36px;
  background: #e9edf7;
  border: 1px solid #cbd6e2;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  font-weight: 400;
  font-size: 12px;
  color: #666666;
  padding-left: 20px;
  border-radius: 5px 5px 0 0;
  border-bottom: none;
  span {
    font-weight: 500;
    font-size: 12px;
    color: #333333;
  }
}
.print {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 20px;
  &-item {
    padding: 0 50px;
    height: 50px;
    line-height: 50px;
    text-align: center;
    margin-left: 10px;
    border: 1px solid #cbd6e2;
    border-radius: 5px;
    font-size: 16px;
    color: $white;
    cursor: pointer;
    background-color: $blue;
    &:hover {
      opacity: 0.8;
    }
  }
}
</style>
