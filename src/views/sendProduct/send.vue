<template>
  <div>
    <el-dialog v-dialogDragBox title="生成发货单" :visible.sync="open" width="1150px" class="custom-dialog" append-to-body :before-close="beforeClose">
      <div style="padding: 0 20px">
        <div class="labelTitle">合同信息</div>
        <el-row :gutter="10" class="labelInfo">
          <el-col :span="9">
            <span>买方</span>
            <b>{{ info.sellerName || '' }}</b>
          </el-col>
          <el-col :span="6">
            <span>签订时间</span>
            <b>{{ info.signingTime || '' }}</b>
          </el-col>
          <el-col :span="9">
            <span>签订地点</span>
            <b>{{ info.address || '' }}</b>
          </el-col>
          <el-col :span="24">
            <span>合同金额</span>
            <b class="price">{{ info.amount ? '￥' + info.amount : '￥0' }}{{ info.isIncludingTax ? '(含税)' : '(不含税)' }}</b>
          </el-col>
        </el-row>
        <div class="labelTitle">合同产品列表</div>
        <el-table ref="sendTable" stripe :data="list" row-key="id" style="width: 100%; margin-bottom: 12px" class="custom-table custom-table-cell5" @select="handleCheck" @select-all="handleCheckAll" :row-class-name="getRowClassName">
          <el-table-column align="center" type="selection" width="50" :reserve-selection="true"></el-table-column>
          <el-table-column align="center" type="index" label="序号"></el-table-column>
          <el-table-column align="center" prop="product_name" label="产品名称" min-width="150">
            <template slot-scope="{ row }">
              <div class="product-name-wrapper">
                <el-tooltip :content="row.productName" placement="top" :disabled="!isTextOverflow(row.productName)">
                  <span class="table-link product-name-text" @click="handleProductView(row)">{{ row.productName }}</span>
                </el-tooltip>
                <div class="product-icons">
                  <el-tooltip v-if="row.hasWarning" content="该产品在金蝶明细中未找到匹配项" placement="top">
                    <i class="el-icon-warning warning-icon"></i>
                  </el-tooltip>
                  <el-tooltip v-if="row.hasKingdeeData" content="数据来源：金蝶系统" placement="top">
                    <i class="el-icon-success kingdee-icon"></i>
                  </el-tooltip>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="picture1" label="图片" width="75">
            <template slot-scope="{ row }">
              <el-button type="text" size="mini" icon="el-icon-view" @click="handleImgView(row)">查看</el-button>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="model" label="产品型号" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="productCode" label="产品编码" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="materialQuality" label="材质" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="surface" label="表面处理" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" label="产品数量" show-overflow-tooltip>
            <template slot-scope="{ row }">
              <div class="quantity-wrapper">
                <span>{{ row.sjNum + row.replyUnit }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column align="center" label="报价">
            <template slot-scope="{ row }">
              <div class="price-wrapper">
                <span>{{ row.amount + ' 元/' + row.replyUnit }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column align="center" label="发货数量" width="200">
            <template slot-scope="{ row }">
              <div class="table-stock">
                <el-input v-model="row.sendAmount" size="small" placeholder="请输入发货数量">
                  <span slot="suffix" class="inline-flex">{{ row.replyUnit }}</span>
                </el-input>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <div class="amountTo">
          <div class="amountTo_item">
            共
            <span class="text" style="margin: 0 5px">{{ checkedList.length }}</span>
            件产品
          </div>
          <div class="amountTo_item">
            发货日期：
            <span class="text">{{ parseTime(new Date(), '{y}-{m}-{d}') }}</span>
          </div>
          <div class="amountTo_item" v-if="mismatchWarnings.length > 0">
            <el-tooltip content="点击查看不一致详情" placement="top">
              <el-button type="text" size="mini" icon="el-icon-warning" @click="showMismatchDetails" style="color: #e6a23c">{{ mismatchWarnings.length }}个产品不一致</el-button>
            </el-tooltip>
          </div>
        </div>
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn" @click="handleCancle()">取消</el-button>
        <el-button class="custom-dialog-btn primary" :class="{ disabled: checkedList.length === 0 }" :disabled="checkedList.length === 0" @click="handleSubmit">立即发货</el-button>
      </div>
    </el-dialog>
    <!-- 发货通知单详情 -->
    <shipping-note-detail ref="shippingNoteDetail" />
    <!-- 产品详情 -->
    <product-dialog ref="productInfo" appendBody></product-dialog>
  </div>
</template>

<script>
import { contractPriceChange } from '@/api/purchase'
import { getProduct } from '@/api/system/product'
import { getPrivateduct } from '@/api/system/privateduct'
import { deliveryAdd } from '@/api/delivery'
import ShippingNoteDetail from '@/views/kingdee/sell/shippingNote/detail'
import { getPrivateProductByMaterialNumber } from '@/api/kingdee'
import ProductDialog from '@/views/public/product/dialog'

export default {
  name: 'SendProduct',
  components: { ShippingNoteDetail, ProductDialog },
  data() {
    return {
      open: false, // 打开
      contractId: undefined, // 合同ID
      info: {}, // 合同信息
      list: [], // 产品列表
      checkedList: [], // 已选中的产品
      kingdeeDetails: [], // 金蝶明细信息
      mismatchWarnings: [], // 不一致警告信息
      hasSuccess: false // 是否已成功提交
    }
  },
  methods: {
    // 打开
    // prettier-ignore
    handleOpen(row, kingdeeDetails = []) {
      if (!row.id) {
        this.$message.error('参数错误，请重试！')
        return
      }
      const loading = this.$loading({
        lock: true,
        text: '加载中…',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      this.info = { ...row }
      this.contractId = row.id
      this.kingdeeDetails = kingdeeDetails || []
      this.mismatchWarnings = []
      this.hasSuccess = false
      contractPriceChange({ contractId: row.id }).then(async res => {
        if (res.code === 200) {
          let arr = []
          const products = res.data
          // 处理金蝶明细信息
          const kingdeeProductMap = new Map()
          if (this.kingdeeDetails.length > 0) {
            await Promise.all(
              this.kingdeeDetails.map(async kingdeeItem => {
                try {
                  const kingdeeRes = await getPrivateProductByMaterialNumber({ materialCode: kingdeeItem.fmaterialid })
                  if (kingdeeRes.code === 200 && kingdeeRes.data && kingdeeRes.data.length > 0) {
                    kingdeeProductMap.set(kingdeeItem.fmaterialid, { products: kingdeeRes.data, kingdeeInfo: kingdeeItem })
                  }
                } catch (error) {
                  console.error('查询金蝶产品明细失败:', error)
                }
              })
            )
          }
          await Promise.all(
            products.map(async item => {
              item.sendAmount = item.sjNum
              item.detailId = item.id
              let productData = {}
              // 获取合同产品详情
              if (item.source == 'common') {
                await getProduct(item.productId).then(async ress => {
                  if (ress.code === 200) productData = ress.data
                })
              }
              if (item.source == 'private') {
                await getPrivateduct(item.productId).then(async ress => {
                  if (ress.code === 200) productData = ress.data
                })
              }
              // 检查金蝶明细是否有对应产品
              let kingdeeMatch = null
              let hasKingdeeData = false
              let kingdeeProductData = {}
              if (kingdeeProductMap.size > 0) {
                // 根据产品ID匹配
                for (const [materialId, kingdeeData] of kingdeeProductMap.entries()) {
                  const matchedProduct = kingdeeData.products.find(p => p.id === item.productId)
                  if (matchedProduct) {
                    kingdeeMatch = kingdeeData.kingdeeInfo
                    hasKingdeeData = true
                    kingdeeProductData = matchedProduct
                    break
                  }
                }
              }
              // 构建最终产品数据
              const finalProduct = { ...item, ...productData }
              // 如果有金蝶数据，优先使用金蝶的单价和数量
              if (hasKingdeeData && kingdeeMatch) {
                finalProduct.amount = kingdeeMatch.fscmjjsprice || finalProduct.amount
                finalProduct.sjNum = kingdeeMatch.fqty || finalProduct.sjNum
                finalProduct.sendAmount = kingdeeMatch.fqty || finalProduct.sendAmount
                finalProduct.hasKingdeeData = true
                finalProduct.kingdeeInfo = kingdeeMatch
              }
              // 检查产品一致性
              if (this.kingdeeDetails.length > 0) {
                if (!hasKingdeeData) {
                  this.mismatchWarnings.push({
                    productName: productData.productName || item.productName,
                    materialNumber: item.materialNumber || item.materialCode,
                    message: '该产品在金蝶明细中未找到匹配项'
                  })
                  finalProduct.hasWarning = true
                }
              }
              arr.push(finalProduct)
            })
          )
          this.list = arr
          this.checkedList = []
          this.open = true
          // 显示不一致警告
          if (this.mismatchWarnings.length > 0) {
            let warningMessage = '以下产品存在不一致情况：\n'
            this.mismatchWarnings.forEach(warning => {
              warningMessage += `• ${warning.productName} (${warning.materialNumber}): ${warning.message}\n`
            })
            this.$message.warning(warningMessage)
          }
          this.$nextTick(() => {
            if (this.$refs.sendTable) {
              this.$refs.sendTable.clearSelection()
              // 默认选中所有产品
              this.list.forEach(row => {
                this.$refs.sendTable.toggleRowSelection(row, true)
              })
              this.checkedList = [...this.list]
            }
          })
        }
      }).finally(() => {
        loading.close()
      })
    },
    // 全选
    handleCheckAll(selection) {
      this.checkedList = selection
    },
    // 选择产品
    handleCheck(selection) {
      this.checkedList = selection
    },
    beforeClose() {
      this.handleCancle()
    },
    // 取消
    handleCancle(refresh = false) {
      this.open = false
      this.$emit('callBack', refresh)
      if (!this.hasSuccess) this.$emit('delete')
    },
    // 立即发货
    handleSubmit() {
      if (!this.checkedList.length) return this.$message.error('请选择产品')
      const invalidAmount = this.checkedList.some(item => item.sendAmount <= 0)
      if (invalidAmount) {
        return this.$message.error('请检查所选产品发货数量')
      }
      const products = this.checkedList.map(item => {
        return {
          detailId: this.contractId,
          productId: item.productId,
          productName: item.productName,
          quantity: Number(item.sendAmount),
          saleQuantity: item.sjNum,
          source: item.source,
          unit: item.replyUnit,
          materialNumber: item.materialNumber || item.materialCode || 'NO001',
          detailId: item.detailId
        }
      })
      const data = { contractId: this.contractId, products }
      deliveryAdd(data).then(async res => {
        const { code, msg } = res
        if (code === 200) {
          this.$message.success('已添加至发货清单')
          this.open = false
          this.hasSuccess = true
          this.$nextTick(() => {
            this.$refs.shippingNoteDetail.getInfo({ BillNo: this.info.BillNo })
          })
        } else this.$message.error(msg)
      })
    },
    // 产品详情
    handleProductView(row = {}) {
      this.$refs.productInfo.handleView(row)
    },
    // 产品图片
    handleImgView(row = {}) {
      this.$refs.productInfo.handleImgView(row)
    },
    // 显示不一致详情
    showMismatchDetails() {
      if (this.mismatchWarnings.length > 0) {
        this.$alert('以下产品存在不一致情况：\n' + this.mismatchWarnings.map(warning => `• ${warning.productName} (${warning.materialNumber}): ${warning.message}`).join('\n'), '产品不一致警告', {
          type: 'warning',
          confirmButtonText: '确定'
        })
      }
    },
    // 获取表格行样式类名
    getRowClassName({ row, rowIndex }) {
      if (row.hasWarning) {
        return 'warning-row'
      }
      if (row.hasKingdeeData) {
        return 'kingdee-row'
      }
      return ''
    },
    // 检测文本是否可能超出省略
    isTextOverflow(text) {
      // 简单根据文本长度判断，超过10个字符认为可能被省略
      return text && text.length > 10
    }
  }
}
</script>
<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
.labelTitle {
  font-size: 14px;
  padding-bottom: 12px;
  color: #999999;
}
.labelInfo {
  border-top: 1px solid #e2e6f3;
  border-bottom: 1px solid #e2e6f3;
  margin-bottom: 12px;
  padding: 10px 0;
  .el-col {
    line-height: 20px;
    padding-top: 6px;
    padding-bottom: 6px;
    span {
      display: inline-block;
      color: #666666;
      font-size: 12px;
      width: 4em;
      margin-right: 20px;
    }
    b {
      font-weight: normal;
      font-size: 14px;
      color: #333333;
      &.price {
        color: #f43f3f;
      }
    }
  }
}
.amountTo {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 48px;
  background: #ecf3ff;
  border-radius: 5px;
  border: 1px solid #2e73f3;
  padding: 0 15px;
  .amountTo_item {
    font-weight: 400;
    font-size: 14px;
    color: #666666;
    display: flex;
    align-items: center;
    justify-content: center;
    .text {
      font-weight: 500;
      font-size: 18px;
      color: #2e73f3;
    }
  }
}
.tipsBox {
  border-bottom: 1px solid #e2e6f3;
  margin-bottom: 12px;
  .tipsTitle {
    font-weight: 400;
    font-size: 12px;
    color: #f35d09;
    line-height: 20px;
    margin-bottom: 10px;
  }
}

.product-name-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
  width: 100%;
  min-width: 0; // 允许flex子项收缩

  .product-name-text {
    flex: 1;
    min-width: 0; // 允许文本收缩
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-align: center;
  }

  .product-icons {
    display: flex;
    align-items: center;
    gap: 3px;
    flex-shrink: 0; // 图标区域不收缩
  }

  .warning-icon {
    color: #e6a23c;
    font-size: 14px;
    cursor: pointer;
  }

  .kingdee-icon {
    color: #67c23a;
    font-size: 14px;
    cursor: pointer;
  }
}

.quantity-wrapper,
.price-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
  flex-wrap: wrap;
}

.amountTo {
  .amountTo_item {
    &:last-child {
      .el-button {
        padding: 0;
        font-size: 12px;
        &:hover {
          color: #f56c6c !important;
        }
      }
    }
  }
}

// 表格行样式
:deep(.el-table) {
  .warning-row {
    background-color: #fef0e8 !important;
    border-left: 3px solid #e6a23c;

    &:hover > td {
      background-color: #fdf6ec !important;
    }
  }

  .kingdee-row {
    background-color: #f0f9ff !important;
    border-left: 3px solid #67c23a;

    &:hover > td {
      background-color: #e8f5e8 !important;
    }
  }
}
</style>
