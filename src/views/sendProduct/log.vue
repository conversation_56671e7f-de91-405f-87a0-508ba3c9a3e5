<template>
  <div>
    <el-dialog v-dialogDragBox title="发货记录" :visible.sync="open" width="1150px" class="custom-dialog">
      <div style="padding: 0 20px">
        <el-table :data="list" stripe class="custom-table">
          <!-- 序号 -->
          <el-table-column align="center" type="index" label="序号" width="50"></el-table-column>
          <!-- 手机号 -->
          <el-table-column prop="phone" align="center" label="手机号" />
          <!-- 实名认证 -->
          <el-table-column align="center" label="实名认证">
            <template slot-scope="{ row }">
              <el-switch v-model="row.certify" disabled></el-switch>
            </template>
          </el-table-column>
          <!-- 真实姓名 -->
          <el-table-column prop="realName" align="center" label="真实姓名">
            <template slot-scope="{ row }">{{ row.certify ? row.certName : '--' }}</template>
          </el-table-column>
          <!-- 身份证号 -->
          <el-table-column prop="idCard" align="center" label="身份证号">
            <template slot-scope="{ row }">{{ row.certify ? formatIdCard(row.certNo) : '--' }}</template>
          </el-table-column>
          <!-- 创建人 -->
          <el-table-column prop="createBy" align="center" label="创建人" />
          <!-- 发送时间 -->
          <el-table-column prop="createTime" align="center" label="发送时间" />
        </el-table>
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn primary" @click="handleClose">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { deliverySendLog } from '@/api/delivery'

export default {
  data() {
    return {
      open: false,
      deliveryId: '',
      list: []
    }
  },
  methods: {
    // 添加身份证号脱敏方法
    formatIdCard(idCard) {
      if (!idCard) return ''
      return idCard.replace(/^(.{6})(.*)(.{4})$/, '$1********$3')
    },
    // 打开
    handleOpen(row = {}) {
      const { id } = row
      if (!id) return this.$message.error('参数错误，请稍后再试')
      this.deliveryId = id
      deliverySendLog({ deliveryId: id }).then(res => {
        const { code, msg, data } = res
        if (code !== 200) return this.$message.error(msg)
        this.list = data
        this.open = true
      })
    },
    // 关闭
    handleClose() {
      this.open = false
      this.$emit('callBack')
    }
  }
}
</script>
<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
</style>
