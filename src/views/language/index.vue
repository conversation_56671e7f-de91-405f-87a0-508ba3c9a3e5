<template>
  <div class="newBox bgcf9 vh-85">
    <!-- 搜索 -->
    <div class="custom-search" style="padding-top: 18px">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" @submit.native.prevent>
        <el-form-item label="中文" prop="chinese">
          <el-input v-model="queryParams.chinese" placeholder="请输入中文" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="英文" prop="english">
          <el-input v-model="queryParams.english" placeholder="请输入英文" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="韩文" prop="korean">
          <el-input v-model="queryParams.korean" placeholder="请输入韩文" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="俄文" prop="russian">
          <el-input v-model="queryParams.russian" placeholder="请输入俄文" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="small" @click="handleResetQuery">重置</el-button>
          <el-button type="primary" icon="el-icon-plus" size="small" @click="handleAdd">新增</el-button>
        </el-form-item>
      </el-form>
    </div>
    <!-- 列表 -->
    <div style="padding: 15px 20px">
      <el-table v-loading="loading" ref="allTable" stripe :data="list" style="width: 100%" class="custom-table">
        <el-table-column align="center" type="index" label="序号"></el-table-column>
        <el-table-column align="center" prop="chinese" label="中文" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" prop="english" label="英文" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" prop="korean" label="韩文" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" prop="russian" label="俄文" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" prop="createTime" label="创建时间" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" label="操作" width="120">
          <template slot-scope="scope">
            <el-button type="text" size="small" icon="el-icon-edit" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button type="text" size="small" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="custom-pagination">
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
      </div>
    </div>
    <!-- 新增/修改 -->
    <el-dialog v-dialogDragBox title="语种配置" :visible.sync="open" width="750px" class="custom-dialog">
      <div style="padding: 0 20px">
        <el-form ref="form" :model="form" :rules="rules" label-width="60px" label-position="left">
          <el-form-item label="中文" prop="chinese">
            <el-input v-model="form.chinese" type="textarea" :autosize="{ minRows: 3 }" resize="none" placeholder="请输入中文" />
          </el-form-item>
          <el-form-item label="英文" prop="english">
            <el-input v-model="form.english" type="textarea" :autosize="{ minRows: 3 }" resize="none" placeholder="请输入英文" />
          </el-form-item>
          <el-form-item label="韩文" prop="korean">
            <el-input v-model="form.korean" type="textarea" :autosize="{ minRows: 3 }" resize="none" placeholder="请输入韩文" />
          </el-form-item>
          <el-form-item label="俄文" prop="russian">
            <el-input v-model="form.russian" type="textarea" :autosize="{ minRows: 3 }" resize="none" placeholder="请输入俄文" />
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn" @click="open = false">取 消</el-button>
        <el-button class="custom-dialog-btn primary" @click="handleSubmit">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { getLanguagePage, addLanguage, editLanguage, deleteLanguage } from '@/api/language'

export default {
  data() {
    return {
      // 搜索
      queryParams: {
        pageNum: 1, // 当前页码
        pageSize: 10, // 每页条数
        chinese: undefined, // 中文
        english: undefined, // 英文
        korean: undefined, // 韩文
        russian: undefined // 俄文
      },
      list: [],
      total: 0,
      loading: false,
      // 新增/修改
      open: false,
      form: {},
      rules: {
        chinese: [{ required: true, message: '请输入中文', trigger: 'blur' }],
        english: [{ required: true, message: '请输入英文', trigger: 'blur' }]
      }
    }
  },
  created() {
    // 获取列表
    this.getList()
  },
  methods: {
    // 获取列表
    // prettier-ignore
    getList() {
      this.loading = true
      getLanguagePage(this.queryParams).then(res => {
        const { code, rows, total } = res
        if (code === 200) {
          this.list = rows
          this.total = total
        } else this.$message.error(msg)
      }).finally(() => {
        this.loading = false
      })
    },
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 重置
    handleResetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 表单重置
    reset() {
      this.form = {
        chinese: undefined,
        english: undefined,
        korean: undefined,
        russian: undefined,
        textFiledMappingId: undefined
      }
      this.resetForm('form')
    },
    // 新增
    handleAdd() {
      this.reset()
      this.open = true
    },
    // 编辑
    handleEdit(row) {
      this.reset()
      this.form = { ...row, textFiledMappingId: row.id }
      this.open = true
    },
    // 删除
    // prettier-ignore
    handleDelete(row) {
      const { id: textFiledMappingId } = row
      this.$confirm('确定删除该语种配置吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteLanguage({ textFiledMappingId }).then(res => {
          const { code, msg } = res
          if (code === 200) {
            this.$message.success(msg)
            this.getList()
          } else this.$message.error(msg)
        })
      }).catch(() => {})
    },
    // 表单提交
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          const { textFiledMappingId } = this.form
          if (textFiledMappingId) {
            editLanguage(this.form).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success(msg)
                this.getList()
                this.open = false
              } else this.$message.error(msg)
            })
          } else {
            addLanguage(this.form).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success(msg)
                this.getList()
                this.open = false
              } else this.$message.error(msg)
            })
          }
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
@import '@/assets/styles/custom-new.scss';
</style>
