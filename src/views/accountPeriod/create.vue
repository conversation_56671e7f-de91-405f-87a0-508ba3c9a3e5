<template>
  <div>
    <el-dialog v-dialogDragBox :title="title" :visible.sync="open" width="1150px" @close="handleCancel()" class="custom-dialog"
      append-to-body>
      <div style="padding: 0 20px" v-if="!disabled">
        <el-form ref="form" :model="form" :rules="rules" label-width="6em" class="custom-form">
          <el-row :gutter="20">
            <!-- 客户名称 -->
            <el-col :span="12">
              <el-form-item label="客户名称" prop="customerName">
                <el-input v-model="form.customerName" placeholder="请输入客户名称" />
              </el-form-item>
            </el-col>
            <!-- 订单号 -->
            <el-col :span="12">
              <el-form-item label="订单号" prop="number">
                <el-input v-model="form.number" placeholder="请输入订单号" />
              </el-form-item>
            </el-col>
            <!-- 目的地 -->
            <!-- <el-col :span="12">
              <el-form-item label="目的地" prop="address">
                <el-autocomplete style="width: 100%" v-model="form.address" :fetch-suggestions="addressSearch" placeholder="请输入内容" @select="handleAddressSelect"></el-autocomplete>
              </el-form-item>
            </el-col> -->
            <el-col :span="24">
              <el-col :span="12" v-for="(item, index) in form.addressList" :key="index"
                :style="(index + 1) % 2 == 1 ? 'padding-left: 0' : 'padding-right: 0'">
                <el-form-item :label="(index > 0) ? '目的地' + (index + 1) : '目的地'" :prop="`addressList[${index}].address`"
                  :rules="rules.addressList.address">
                  <el-autocomplete style="width: 100%" v-model="item.address" :fetch-suggestions="addressSearch"
                    placeholder="请输入内容" @select="handleAddressSelect">
                    <i slot="suffix" class="el-input__icon el-icon-delete" @click="delAddress(index)" v-if="index > 0"></i>
                  </el-autocomplete>
                </el-form-item>
              </el-col>
            </el-col>
            <el-col :span="24">
              <el-form-item label="">
                <el-button type="primary" size="small" icon="el-icon-plus" @click="handleAddressAdd">新增目的地</el-button>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="装车建议">
                <el-input v-model="form.suggestion" type="textarea" :autosize="{ minRows: 3, maxRows: 6 }" resize="none"
                  placeholder="请输入装车建议" />
              </el-form-item>
            </el-col>
            <!-- 货物数量 -->
            <el-col :span="12">
              <el-form-item label="货物数量" prop="quantity">
                <el-input v-model="form.quantity" placeholder="请输入货物数量" />
              </el-form-item>
            </el-col>
            <!-- 是否计费 -->
            <el-col :span="12">
              <el-form-item label="是否计费" prop="isCharge">
                <el-select v-model="form.isCharge" placeholder="请选择是否计费" style="width: 100%">
                  <el-option label="是" :value="1"></el-option>
                  <el-option label="否" :value="0"></el-option>
                </el-select>
              </el-form-item>
            </el-col>

            <!-- 派车部门 -->
            <el-col :span="12">
              <el-form-item label="派车部门" prop="deptId">
                <el-select v-model="form.deptId" placeholder="请选择派车部门" style="width: 100%" @change="handleDeptChange">
                  <el-option v-for="(item, index) in deptDataOptions" :key="index" :label="item.deptName"
                    :value="item.deptId" />
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 派车原因 -->
            <el-col :span="12">
              <el-form-item label="派车原因" prop="reason">
                <el-select v-model="form.reason" placeholder="请选择派车原因" style="width: 100%">
                  <el-option v-for="item in reasonOptions" :key="item" :label="item" :value="item" />
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 需求方 -->
            <el-col :span="12">
              <el-form-item label="需求方" prop="demander">
                <el-input v-model="form.demander" placeholder="请输入需求方" />
              </el-form-item>
            </el-col>
            <!-- 派车时间 -->
            <el-col :span="12">
              <el-form-item label="派车时间" prop="time">
                <el-date-picker v-model="form.time" type="datetime" format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss" placeholder="请选择派车时间" style="width: 100%" />
              </el-form-item>
            </el-col>
            <!-- 派车人 -->
            <el-col :span="12">
              <el-form-item label="派车人" prop="dispatchPersonId">
                <el-cascader v-model="form.dispatchPersonId" :options="userDataOptions" :props="userProps" filterable
                  :show-all-levels="false" placeholder="请选择派车人" @change="handleUserChange"
                  style="width: 100%"></el-cascader>
              </el-form-item>
            </el-col>
            <!-- 派送车辆 -->
            <el-col :span="12">
              <el-form-item label="派送车辆" prop="carId">
                <el-select v-model="form.carId" filterable remote reserve-keyword placeholder="请选择派送车辆"
                  :remote-method="carRemoteMethod" :loading="carLoading" style="width: 100%">
                  <el-option v-for="item in carList" :key="item.id" :label="item.name" :value="item.id">
                    <span>{{ item.name }}</span>
                    <span style="float: right; color: #999; font-size: 13px">{{ item.plateNumber }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 截至到货时间 -->
            <el-col :span="12">
              <el-form-item label="截至到货时间" prop="deadlineTime">
                <el-date-picker v-model="form.deadlineTime" type="datetime" format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss" placeholder="请选择截至到货时间" style="width: 100%" />
              </el-form-item>
            </el-col>
            <!-- 备注 -->
            <el-col :span="24">
              <el-form-item label="备注" prop="remark">
                <el-input v-model="form.remark" type="textarea" :autosize="{ minRows: 3, maxRows: 6 }" resize="none"
                  placeholder="请输入备注" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div style="padding: 0 20px" v-if="disabled">
        <el-descriptions border :column="3" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }"
          :contentStyle="{ width: 'calc((1110px - 20px) / 3 - 7em - 22px)', color: '#333' }">
          <!-- 客户名称 -->
          <el-descriptions-item label="客户名称">{{ form.customerName }}</el-descriptions-item>
          <!-- 订单号 -->
          <el-descriptions-item label="订单号">{{ form.number }}</el-descriptions-item>
          <!-- 货物数量 -->
          <el-descriptions-item label="货物数量">{{ form.quantity }}</el-descriptions-item>
          <!-- 目的地 -->
          <el-descriptions-item label="目的地" :span="3">{{ form.address }}</el-descriptions-item>
          <!-- 是否计费 -->
          <el-descriptions-item label="是否计费">{{ form.isCharge ? '是' : '否' }}</el-descriptions-item>
          <!-- 派车部门 -->
          <el-descriptions-item label="派车部门">{{ form.deptName }}</el-descriptions-item>
          <!-- 派车原因 -->
          <el-descriptions-item label="派车原因">{{ form.reason }}</el-descriptions-item>
          <!-- 需求方 -->
          <el-descriptions-item label="需求方">{{ form.demander }}</el-descriptions-item>
          <!-- 派车时间 -->
          <el-descriptions-item label="派车时间">{{ form.time }}</el-descriptions-item>
          <!-- 派车人 -->
          <el-descriptions-item label="派车人">{{ form.dispatchPerson }}</el-descriptions-item>
          <!-- 派送车辆 -->
          <el-descriptions-item label="派送车辆">{{ form.carId && form.car && form.car.name }}</el-descriptions-item>
          <!-- 截至时间 -->
          <el-descriptions-item label="截至到货日期">{{ form.deadlineTime }}</el-descriptions-item>
          <!-- 优先级别 -->
          <el-descriptions-item label="优先级别" :contentStyle="{color: '#F43F3F'}" v-if="(new Date(form.deadlineTime).getTime() - new Date().getTime()) < 24*60*60*1000">{{ '紧急' }}</el-descriptions-item>
          <el-descriptions-item label="优先级别" :contentStyle="{color: '#F35D09'}" v-if="(new Date(form.deadlineTime).getTime() - new Date().getTime()) > 24*60*60*1000 && (new Date(form.deadlineTime).getTime() - new Date().getTime()) < 72*60*60*1000">{{ '优先' }}</el-descriptions-item>
          <el-descriptions-item label="优先级别" :contentStyle="{color: '#1DC86B'}" v-if="(new Date(form.deadlineTime).getTime() - new Date().getTime()) > 72*60*60*1000">{{ '一般' }}</el-descriptions-item>
          <!-- 装车建议 -->
          <el-descriptions-item label="装车建议" :span="3">{{ form.suggestion }}</el-descriptions-item>
          <!-- 备注 -->
          <el-descriptions-item label="备注" :span="3">{{ form.remark }}</el-descriptions-item>
        </el-descriptions>
      </div>
      <div slot="footer">
        <template v-if="!disabled">
          <el-button class="custom-dialog-btn" @click="handleCancel()">取 消</el-button>
          <el-button class="custom-dialog-btn primary" @click="handleSubmit">确 定</el-button>
        </template>
        <template v-if="disabled">
          <el-button class="custom-dialog-btn primary" @click="handleCancel()">关 闭</el-button>
        </template>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getCarList, addSendCar, updateSendCar } from '@/api/sendCar'
import { listDept } from '@/api/system/dept'
import { deptTreeSelect, listUser } from '@/api/system/user'

export default {
  name: 'accountPeriodCreate',
  props: {
    isProps: {
      type: Boolean,
      default: true
    },
    userList: {
      type: Array,
      default: () => []
    },
    userOptions: {
      type: Array,
      default: () => []
    },
    deptOptions: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      title: '新增派车单',
      open: false,
      form: {
        addressList: []
      },
      rules: {
        customerName: [{ required: true, message: '请输入客户名称', trigger: 'blur' }],
        // address: [{ required: true, message: '请输入目的地', trigger: ['blur', 'change'] }],
        quantity: [{ required: true, message: '请输入货物数量', trigger: 'blur' }],
        isCharge: [{ required: true, message: '请选择是否计费', trigger: 'blur' }],
        // number: [{ required: true, message: '请输入订单号', trigger: 'blur' }],
        deptId: [{ required: true, message: '请选择派车部门', trigger: 'blur' }],
        reason: [{ required: true, message: '请选择派车原因', trigger: 'blur' }],
        time: [{ required: true, message: '请选择派车时间', trigger: 'blur' }],
        deadlineTime: [{ required: true, message: '请选择到货截至日期', trigger: 'blur' }],
        dispatchPersonId: [{ required: true, message: '请选择派车人', trigger: 'blur' }],
        carId: [{ required: true, message: '请选择派送车辆', trigger: 'blur' }],
        addressList: {
          address: [
            { required: true, message: "请输入目的地", trigger: 'blur' }
          ],
        }
      },
      disabled: false,
      reasonOptions: ['正向订单运输', '逆向订单运输', '采购商品运输', '原材料运输', '外协加工运输'],
      userProps: {
        expandTrigger: 'hover', // 展开方式
        emitPath: false, // 是否返回路径
        multiple: false // 是否多选
      },
      carList: [],
      carLoading: false,
      userDataList: [],
      userDataOptions: [],
      deptDataOptions: [],
      addressCache: []
    }
  },
  computed: {
    userId() {
      return this.$store.getters.info.userId
    }
  },
  created() {
    this.addressCache = JSON.parse(localStorage.getItem(`sendCarAddressCache-${this.userId}`)) || []
    this.getCarList()
    if (this.isProps) {
      this.userDataList = this.userList
      this.userDataOptions = this.userOptions
      this.deptDataOptions = this.deptOptions
    } else this.getDeptUser()
  },
  methods: {
    // 部门和人员
    async getDeptUser() {
      const deptList = await listDept()
      this.deptDataOptions = deptList.data
      const dept = await deptTreeSelect()
      const user = await listUser()
      const children = dept.data[0].children || []
      const deptData = [...children, ...[{ id: -1, label: '其他', value: -1 }]] || []
      const userData = user.rows || []
      this.userDataList = userData
      const getChildren = data => {
        data.forEach(item => {
          item.value = item.id
          if (item.children) {
            getChildren(item.children)
          } else {
            item.children = []
          }
        })
      }
      getChildren(deptData)
      const addChildren = data => {
        data.forEach(item => {
          userData.forEach(user => {
            if (item.id === user.deptId && item.children) {
              item.children.push({
                id: user.userId,
                label: user.realName || user.nickName,
                value: user.userId,
                disabled: user.status == '1',
                userName: user.userName
              })
            }
            if (item.id === -1 && (!user.deptId || user.deptId === dept.data[0].id) && item.children) {
              item.children.push({
                id: user.userId,
                label: user.realName || user.nickName,
                value: user.userId,
                disabled: user.status == '1',
                userName: user.userName
              })
            }
          })
          if (item.children && item.children.length) {
            addChildren(item.children)
          }
        })
      }
      addChildren(deptData)
      this.userDataOptions = deptData
    },
    // 获取车辆列表
    getCarList() {
      getCarList().then(res => {
        const { code, rows } = res
        if (code === 200) this.carList = rows
      })
    },
    // 车辆搜索
    carRemoteMethod(query) {
      if (!query) return
      this.carLoading = true
      getCarList({ name: query }).then(res => {
        const { code, rows } = res
        if (code === 200) this.carList = rows
        this.carLoading = false
      })
    },
    // 重置表单
    reset() {
      this.form = {
        addressList: [
          {
            address: undefined,
          },
        ],
        address: undefined, // 目的地
        carId: undefined, // 派送车辆
        customerName: undefined, // 客户名称
        demander: undefined, // 需求方
        deptId: undefined, // 派车部门
        deptName: undefined, // 派车部门
        dispatchPerson: undefined, // 派车人
        dispatchPersonId: undefined, // 派车人
        documentId: undefined, // 派车单id
        isCharge: 0, // 是否计费
        number: undefined, // 订单号
        quantity: undefined, // 货物数量
        reason: undefined, // 派车原因
        remark: undefined, // 备注
        time: undefined, // 派车时间
        suggestion: undefined, //装车建议
        deadlineTime: undefined, //截至到货时间
      }
      this.resetForm('form')
    },
    // 新增
    handleOpen(row = {}) {
      this.reset()
      const { buyerName, contractNum, deliveryNum } = row
      if (buyerName) this.form.customerName = buyerName
      if (deliveryNum) this.form.number = '发货单编号:' + deliveryNum
      if (contractNum) this.form.remark = '合同编号:' + contractNum
      this.title = '新增派车单'
      this.disabled = false
      this.open = true
    },
    // 详情
    handleDetail(row = {}) {
      this.reset()
      this.title = '派车单详情'
      this.form = { ...row }
      this.disabled = true
      this.open = true
    },
    // 修改
    handleUpdate(row = {}) {
      this.reset()
      this.title = '修改派车单'
      this.disabled = false
      this.form = { ...row }
      this.form.addressList = []
      if (this.form.address) {
        for (let i = 0; i < this.form.address.split(',').length; i++) {
          this.form.addressList[i] = {
            address: this.form.address.split(',')[i]
          }          
        }
      }
      this.form.documentId = row.id
      this.open = true
    },
    // 取消
    handleCancel(refresh = false) {
      this.open = false
      this.$emit('callBack', refresh)
    },
    // 提交
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.form.number = this.form.number || `PCD${this.parseTime(new Date(), '{y}{m}{d}{h}{i}{s}{l}')}`
          this.form.address = ''
          this.form.addressList.forEach(el => {
            this.form.address = this.form.address ? this.form.address + ',' + el.address : el.address
          })
          if (this.form.documentId) {
            updateSendCar(this.form).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('派车单修改成功!')
                this.handleCancel()
              } else this.$message.error(msg)
            })
          } else {
            addSendCar(this.form).then(res => {
              const { code, msg } = res
              if (code === 200) {
                if (this.form.address) {
                  this.addressCache = this.addressCache.filter(address => address.value !== this.form.address)
                  this.addressCache.unshift({ value: this.form.address, label: this.form.address })
                  if (this.addressCache.length > 10) this.addressCache.pop()
                  localStorage.setItem(`sendCarAddressCache-${this.userId}`, JSON.stringify(this.addressCache))
                }
                this.$message.success('派车单新增成功!')
                this.handleCancel(true)
              } else this.$message.error(msg)
            })
          }
        }
      })
    },
    // 选择部门
    handleDeptChange(value) {
      const dept = this.deptDataOptions.find(item => item.deptId === value)
      this.form.deptName = dept?.deptName
      this.form.deptId = dept?.deptId
    },
    // 选择派车人
    handleUserChange(value) {
      const obj = this.userDataList.find(item => item.userId === value)
      this.form.dispatchPerson = obj?.realName || obj?.nickName
      this.form.dispatchPersonId = obj?.userId
    },
    // 目的地建议
    addressSearch(queryString, cb) {
      const results = queryString ? this.addressCache.filter(this.createFilter(queryString)) : this.addressCache
      cb(results)
    },
    createFilter(queryString) {
      return item => {
        return item.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0
      }
    },
    // 选择目的地
    handleAddressSelect(item) {
      this.form.address = item.value
    },
    // 新增目的地
    handleAddressAdd() {
      this.form.addressList.push({
        address: undefined
      })
    },
    delAddress(index) {
      this.form.addressList.splice(index, 1)
    },
  }
}
</script>
<style scoped lang="scss">
@import '@/assets/styles/custom-new.scss';
</style>
