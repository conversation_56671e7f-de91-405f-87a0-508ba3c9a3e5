<template>
    <div class="newBox bgcf9 vh-85">
        <!-- 搜索 -->
        <div class="custom-search flex">
            <div class="flex">
                <div class="custom-search-form flex">
                    <input type="text" v-model="queryParams.supplierName" placeholder="请输入公域供应商名称"
                        class="custom-search-input" @keyup.enter="getList" />
                    <button type="button" class="custom-search-button pointer" @click="getList">
                        <i class="el-icon-search"></i>
                        搜索
                    </button>
                </div>
            </div>
        </div>

        <!-- 分类 -->
        <div class="classify flex">
            <div class="classify-item" :class="{ active: item.id === type }" v-for="item in categoryList" :key="item.id"
                @click="handleCategory(item)">
                {{ item.name }}
            </div>
        </div>

        <!-- 表格数据 -->
        <div class="tableBox">
            <el-table v-loading="notLoading" ref="table1" stripe :data="notList" :key="notKey" style="width: 100%"
                class="custom-table custom-table-cell5" v-if="type == '1'">
                <el-table-column align="center" type="index" label="序号" width="50"></el-table-column>
                <el-table-column align="center" prop="productName" label="供应商名称" show-overflow-tooltip>
                    <template slot-scope="{ row }">
                        <span class="table-link" @click="handleView(row)">{{ row.supplier_name }}</span>
                    </template>
                </el-table-column>

                <el-table-column align="center" label="操作">
                    <template slot-scope="{ row }">
                        <button type="button" class="table-btn primary hasbg"
                            @click="handleSetPeriod(row)">设置账期</button>
                    </template>
                </el-table-column>
            </el-table>
            <el-table v-loading="loading" ref="table2" stripe :data="list" :key="key" style="width: 100%"
                class="custom-table custom-table-cell5" v-if="type == '2'">
                <el-table-column align="center" type="index" label="序号" width="50"></el-table-column>
                <el-table-column align="center" prop="productName" label="供应商名称" show-overflow-tooltip>
                    <template slot-scope="{ row }">
                        <span class="table-link" @click="handleView(row)">{{ row.companyDO && row.companyDO.companyName
                        }}</span>
                    </template>
                </el-table-column>
                <el-table-column align="center" label="账期" show-overflow-tooltip>
                    <template slot-scope="{ row }">
                        <span>{{ row.period + '天' }}</span>
                    </template>
                </el-table-column>
                <el-table-column align="center" label="操作">
                    <template slot-scope="{ row }">
                        <button type="button" class="table-btn primary hasbg"
                            @click="handleSetPeriod(row)">修改账期</button>
                    </template>
                </el-table-column>
            </el-table>
        </div>

        <!-- 设置账期 -->
        <el-dialog v-dialogDragBox :title="title" :visible.sync="createOpen" width="750px" @close="handleCancel()" class="custom-dialog"
            append-to-body>
            <div>
                <el-form ref="form" :model="form" :rules="rules" label-width="6em" class="custom-form">
                    <el-row :gutter="20">
                        <!-- 客户名称 -->
                        <el-col :span="24">
                            <el-form-item label="客户名称">
                                <div>{{ form.supplier_name }}</div>
                            </el-form-item>
                        </el-col>
                        <!-- 账期 -->
                        <el-col :span="12">
                            <el-form-item label="账期" prop="period">
                                <el-input v-model="form.period" placeholder="请输入账期" type="number" min="0">
                                    <span slot="suffix">天</span>
                                </el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
            <div slot="footer">
                <el-button class="custom-dialog-btn" @click="handleClose">取 消</el-button>
                <el-button class="custom-dialog-btn primary" @click="handleSubmit">确 定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { getSignedSupplierList, getReceiptPeriod, updateSetReceiptPeriod, addSetdRceiptPeriod } from '@/api/accountPeriod'

export default {
    name: 'AccountPeriod',
    data() {
        return {
            notKey: 1,
            key: 1,
            // 搜索条件
            queryParams: {
                supplierName: undefined,
            },
            // 加载
            notLoading: false,
            loading: false,
            // 列表数据
            notList: [],
            list: [],
            // 分类数据
            categoryList: [
                { id: '1', name: '待设置账期' },
                { id: '2', name: '已设置账期' },
            ],
            // 账期类型
            type: '1',
            // 详情
            createOpen: false,
            title: '设置账期',
            form: {},
            rules: {
                period: [{ required: true, message: '请输入账期', trigger: 'blur' }],
            }

        }
    },
    created() {
        this.getList()
    },
    methods: {
        // 查询数据
        async getList() {
            this.loading = true
            if (this.type == '1') {
                const res = await getSignedSupplierList(this.queryParams)
                if (res.code === 200) {
                    this.notList = res.data
                    this.notLoading = false
                    this.notKey = Math.random()
                } else {
                    this.$message.error(res.msg)
                }
            } else if (this.type == '2') {
                const res = await getReceiptPeriod(this.queryParams)
                if (res.code === 200) {
                    this.list = res.data
                    this.loading = false
                    this.key = Math.random()
                } else {
                    this.$message.error(res.msg)
                }
            }

        },
        // 切换分类
        handleCategory(item) {
            this.type = item.id
            this.getList()
        },


        // 打开
        async handleView(row) {
            this.showDetail = true
            this.$nextTick(() => {
                this.$refs.detail.handleOpen(row)
            })
        },
        // 新建/修改账期
        handleSetPeriod(row) {
            if (row.id) {
                row.supplier_name = row.companyDO.companyName
                this.title = '修改账期'
            } else {
                this.title = '设置账期'
            }
            this.form = row
            this.createOpen = true
        },
        // 取消
        handleClose() {
            this.form = {}
            this.createOpen = false
        },
        // 提交
        handleSubmit() {
            this.$refs.form.validate(valid => {
                if (valid) {
                    if (this.form.id) {
                        let obj = {
                            period: this.form.period,
                            receiptPeriodId: this.form.id
                        }
                        updateSetReceiptPeriod(obj).then(res => {
                            const { code, msg } = res
                            if (code === 200) {
                                this.$message.success('账期修改成功!')
                                this.getList()
                                this.createOpen = false
                            } else this.$message.error(msg)
                        })
                    } else {
                        let obj = {
                            period: this.form.period,
                            supplierId: this.form.supplier_id
                        }
                        addSetdRceiptPeriod(obj).then(res => {
                            const { code, msg } = res
                            if (code === 200) {
                                this.$message.success('账期设置成功!')
                                this.getList()
                                this.createOpen = false
                            } else this.$message.error(msg)
                        })
                    }
                }
            })
        },
    }
}
</script>

<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';

.tableBox {
    padding: 20px;
}

.custom-search {
    align-items: center;
    justify-content: space-between;
}

.custom-search-tip {
    background-color: #fef0f0;
    color: #f56c6c;
    padding: 10px 20px;
    font-size: 20px;
    display: inline-flex;
    align-items: center;

    span {
        font-size: 14px;
        margin-left: 10px;
        margin-right: 30px;
    }
}

.custom-dialog {
    .custom-form {
        padding: 20px;
    }
}
</style>