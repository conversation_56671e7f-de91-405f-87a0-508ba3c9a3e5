<template>
  <div class="newBox bgcf9 vh-85 p20">
    <!-- 搜索 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" @submit.native.prevent>
      <el-form-item label="产品名称" prop="keyword">
        <el-input v-model="queryParams.keyword" placeholder="请输入产品名称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
        <el-button type="primary" size="small" icon="el-icon-plus" @click="handleAdd">新增产品</el-button>
      </el-form-item>
    </el-form>

    <!-- 收藏管理 -->
    <collect-tpl ref="collect" @getList="getList" :isSearch="true" :search="false" :showSearch.sync="showSearch" @queryTable="getList" :columns="columns" :isCollect="false" :hasCollect="false" />

    <!--  列表  -->
    <el-table ref="table" v-loading="loading" :data="list" row-key="id" size="small" :key="key" class="custom-table custom-table-cell10">
      <el-table-column align="center" label="序号" type="index" v-if="columns[0].visible" />
      <el-table-column label="产品名称" align="center" prop="productName" min-width="130" v-if="columns[1].visible">
        <template slot-scope="{ row }">
          <div class="table-link pointer" @click="handleView(row)">{{ row.productName }}</div>
        </template>
      </el-table-column>
      <el-table-column label="产品编码" align="center" prop="productCode" v-if="columns[2].visible" />
      <el-table-column label="产品图片" align="center" prop="picture" width="80" v-if="columns[3].visible">
        <template slot-scope="{ row }">
          <image-preview :src="row.picture" :width="50" :height="50" />
        </template>
      </el-table-column>
      <el-table-column label="产品分类" align="center" prop="mainClass" :formatter="(row, column) => formatter(row, column, 'main')" v-if="columns[4].visible" />
      <el-table-column label="规格型号" align="center" prop="specsModel" v-if="columns[5].visible" />
      <el-table-column label="行业分类" align="center" prop="industry" :formatter="(row, column) => formatter(row, column, 'industry')" v-if="columns[6].visible" />
      <el-table-column label="产品材质" align="center" prop="materialQuality" :formatter="(row, column) => formatter(row, column, 'material')" v-if="columns[7].visible" />
      <el-table-column label="产品等级" align="center" prop="level" :formatter="(row, column) => formatter(row, column, 'level')" v-if="columns[8].visible" />
      <el-table-column label="表面处理" align="center" prop="surface" :formatter="(row, column) => formatter(row, column, 'surface')" v-if="columns[9].visible" />
      <el-table-column label="产品单位" align="center" prop="unit" v-if="columns[10].visible" />
      <el-table-column label="产品重量" align="center" prop="weight" v-if="columns[11].visible">
        <template slot-scope="{ row }">{{ `${row.weight}Kg` }}</template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="220">
        <template slot-scope="{ row }">
          <button type="button" class="table-btn" @click="handleUpdate(row)">
            <i class="el-icon-edit"></i>
            修改
          </button>
          <button type="button" class="table-btn danger" @click="handleDelete(row)">
            <i class="el-icon-delete"></i>
            删除
          </button>
        </template>
      </el-table-column>
    </el-table>
    <div class="custom-pagination">
      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
    </div>

    <!-- 新增/修改产品 -->
    <create-dialog ref="create" :rule-list="ruleList" />

    <!-- 产品详情 -->
    <product-dialog ref="productInfo" :rule-list="ruleList"></product-dialog>
  </div>
</template>
<script>
import { codingProductList, codingRuleList, codingProductDel } from '@/api/coding'
import CollectTpl from '@/views/components/collect/index'
import createDialog from './create'
import ProductDialog from '@/views/public/product/dialog'

export default {
  name: 'index',
  components: { createDialog, CollectTpl, ProductDialog },
  data() {
    return {
      list: [],
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10
      },
      loading: true,
      key: 1,
      showSearch: true,
      // 列信息
      columns: [
        { key: 0, label: `序号`, visible: true },
        { key: 1, label: `产品名称`, visible: true },
        { key: 2, label: `产品编码`, visible: true },
        { key: 3, label: `产品图片`, visible: true },
        { key: 4, label: `产品分类`, visible: true },
        { key: 5, label: `规格型号`, visible: true },
        { key: 6, label: `行业分类`, visible: true },
        { key: 7, label: `产品材质`, visible: true },
        { key: 8, label: `产品等级`, visible: true },
        { key: 9, label: `表面处理`, visible: true },
        { key: 10, label: `产品单位`, visible: true },
        { key: 11, label: `产品重量`, visible: true }
      ],
      industryOptions: [], // 行业分类
      levelOptions: [], // 等级
      mainClassOptions: [], // 主分类
      materialQualityOptions: [], // 材质
      surfaceOptions: [], // 表面
      ruleList: {}
    }
  },
  created() {
    this.getRuleList()
    this.getList()
  },
  methods: {
    // 树结构转扁平数组
    treeToArr(data, children = 'children', parentId, res = []) {
      data.forEach(v => {
        v.parentId = parentId
        res.push(v)
        if (v[children] && v[children].length) this.treeToArr(v[children], children, v.id, res)
      })
      return res
    },
    formatter(row, column, type) {
      let res
      switch (type) {
        case 'main':
          res = this.mainClassOptions.find(item => item.id == row.mainClass) || { name: '' }
          const ress = this.treeToArr(this.mainClassOptions).find(item => item.id == row.subClass) || { name: '' }
          return res.name + '/' + ress.name
        case 'material':
          res = this.materialQualityOptions.find(item => item.id == row.materialQuality) || { name: '' }
          return res.name
        case 'surface':
          res = this.surfaceOptions.find(item => item.id == row.surface) || { name: '' }
          return res.name
        case 'level':
          res = this.levelOptions.find(item => item.id == row.level) || { name: '' }
          return res.name
        case 'industry':
          res = this.industryOptions.find(item => item.id == row.industry) || { name: '' }
          return res.name
      }
    },
    getRuleList() {
      codingRuleList().then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          this.ruleList = data
          this.industryOptions = data.industry
          this.levelOptions = data.level
          this.mainClassOptions = data.main
          this.materialQualityOptions = data.material
          this.surfaceOptions = data.surface
        } else this.$message.error(msg)
      })
    },
    // 编码产品列表
    getList() {
      this.loading = true
      codingProductList().then(res => {
        const { code, rows, msg, total } = res
        if (code === 200) {
          this.list = rows
          this.total = total
          this.key = Math.random()
          this.loading = false
        } else this.$message.error(msg)
      })
    },
    // 查询
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 重置
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 新增
    handleAdd() {
      this.$refs.create.handleAdd()
    },
    // 修改
    handleUpdate(row) {
      this.$refs.create.handleUpdate(row)
    },
    // 删除
    // prettier-ignore
    handleDelete(row) {
      const productId = row.id
      this.$confirm('是否删除此产品?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        codingProductDel({ productId }).then(res => {
          if (res.code === 200) {
            this.$message.success('删除成功')
            this.getList()
          } else {
            this.$message.error(res.msg)
          }
        })
      }).catch(() => {
      })
    },
    // 查看详情
    handleView(item, val) {
      this.$refs.productInfo.handleView(item, val)
    }
  }
}
</script>

<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';
.p20 {
  padding: 20px;
}
</style>
