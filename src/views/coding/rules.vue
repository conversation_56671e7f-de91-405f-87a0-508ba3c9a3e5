<template>
  <div class="newBox bgcf9 vh-85">
    <!-- 搜索 -->
    <div class="custom-search flex" style="padding-top: 18px">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
        <el-form-item label="名称" prop="name">
          <el-input v-model="queryParams.name" placeholder="请输入名称" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="编码" prop="code">
          <el-input v-model="queryParams.code" placeholder="请输入编码" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" style="background-color: #2e73f3; border-color: #2e73f3" size="small" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
          <button type="button" class="custom-search-add pointer" @click="handleAdd">
            <i class="el-icon-plus"></i>
            新增规则
          </button>
        </el-form-item>
      </el-form>
    </div>
    <!-- 分类 -->
    <div class="classify flex">
      <div class="classify-item" :class="{ active: item.value === queryParams.type }" v-for="item in typeOptions" :key="item.value" @click="handleChangeType(item)">
        {{ item.label }}
      </div>
    </div>
    <!-- 列表 -->
    <div class="p20" v-if="total > 0">
      <el-table v-loading="loading" :ref="queryParams.type" stripe :data="list" row-key="id" style="width: 100%" class="custom-table">
        <el-table-column align="center" type="index" label="序号"></el-table-column>
        <el-table-column align="center" prop="main" label="编码主分类" show-overflow-tooltip v-if="queryParams.type === 'subclass'">
          <template slot-scope="{ row }">{{ row.hasOwnProperty('parent') ? row.parent.name : '' }}</template>
        </el-table-column>
        <el-table-column align="center" prop="name" label="名称" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" prop="code" label="编码" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" prop="prop" label="操作" width="150" v-if="queryParams.type !== 'combine_material'">
          <template slot-scope="{ row }">
            <el-button type="text" icon="el-icon-edit" @click="handleUpdate(row)" size="small">修改</el-button>
            <el-button type="text" icon="el-icon-delete" @click="handleDelete(row)" size="small">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="custom-pagination">
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
      </div>
    </div>
    <el-empty v-else />

    <!-- 添加 -->
    <el-dialog v-dialogDragBox :title="title" :visible.sync="open" width="500px" class="custom-dialog">
      <div class="p20">
        <el-form ref="form" :model="form" :rules="rules" label-width="4em">
          <el-form-item label="类目" prop="parentId" v-if="form.type === 'subclass'">
            <el-select v-model="form.parentId" placeholder="请选择类目" style="width: 100%" :disabled="!!this.form.id">
              <el-option v-for="item in parentOptions" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="名称" prop="name">
            <el-input v-model="form.name" placeholder="请输入名称"></el-input>
          </el-form-item>
          <el-form-item label="编码" prop="code">
            <el-input v-model="form.code" placeholder="请输入编码"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer">
        <el-button @click="open = false">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { codingRuleList, codingRuleAdd, codingRuleDel, codingRuleListAll, codingRuleEdit } from '@/api/coding'

export default {
  name: 'Rules',
  data() {
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        type: 'main',
        code: undefined,
        name: undefined
      },
      total: 0,
      list: [],
      loading: true,
      typeOptions: [
        { label: '编码主分类', value: 'main' },
        { label: '编码子分类', value: 'subclass' },
        { label: '行业分类', value: 'industry' },
        { label: '产品材质', value: 'material' },
        { label: '组合材质', value: 'combine_material' },
        { label: '产品等级', value: 'level' },
        { label: '表面处理', value: 'surface' }
      ],
      title: '',
      open: false,
      form: {},
      rules: {
        name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
        code: [{ required: true, message: '请输入编码', trigger: 'blur' }],
        parentId: [{ required: true, message: '请选择类目', trigger: 'change' }]
      },
      parentOptions: []
    }
  },
  created() {
    this.getRuleList()
    this.getList()
  },
  methods: {
    // 列表
    getList() {
      this.loading = true
      codingRuleListAll(this.queryParams).then(res => {
        const { code, msg, rows, total } = res
        if (code === 200) {
          this.list = rows
          this.total = total
          this.loading = false
        } else this.$message.error(msg)
      })
    },
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 重置搜索
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        type: 'main',
        code: undefined,
        name: undefined
      }
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 改变分类
    handleChangeType(item) {
      this.queryParams.type = item.value
      this.handleQuery()
    },
    getRuleList() {
      codingRuleList().then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          this.parentOptions = [...data.main]
        } else this.$message.error(msg)
      })
    },
    reset() {
      this.form = {
        code: undefined,
        name: undefined,
        parentId: undefined,
        type: undefined
      }
      this.resetForm('form')
    },
    // 添加
    handleAdd() {
      this.reset()
      const { type } = this.queryParams
      this.form.type = type
      if (type === 'main') this.title = '新增编码主分类'
      if (type === 'subclass') this.title = '新增编码子分类'
      if (type === 'industry') this.title = '新增行业分类'
      if (type === 'material') this.title = '新增产品材质'
      if (type === 'level') this.title = '新增产品等级'
      if (type === 'surface') this.title = '新增表面处理'
      this.open = true
    },
    // 修改
    handleUpdate(row) {
      this.reset()
      this.form = { ...row }
      const { type } = row
      if (type === 'main') this.title = '修改编码主分类'
      if (type === 'subclass') this.title = '修改编码子分类'
      if (type === 'industry') this.title = '修改行业分类'
      if (type === 'material') this.title = '修改产品材质'
      if (type === 'level') this.title = '修改产品等级'
      if (type === 'surface') this.title = '修改表面处理'
      this.open = true
    },
    // 删除
    // prettier-ignore
    handleDelete(row) {
      const rulesId = row.id
      let title = ''
      if (row.type === 'main' || row.type === 'subclass') title = '产品分类'
      if (row.type === 'industry') title = '行业分类'
      if (row.type === 'material') title = '产品材质'
      if (row.type === 'level') title = '产品等级'
      if (row.type === 'surface') title = '表面处理'
      this.$confirm(`是否删除此${title}?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        codingRuleDel({ rulesId }).then(res => {
          if (res.code === 200) {
            this.$message.success('删除成功')
            this.getList()
          } else {
            this.$message.error(res.msg)
          }
        })
      }).catch(() => {
      })
    },
    // 提交
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          if (!!this.form.id) {
            codingRuleEdit(this.form).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('修改成功')
                this.getList()
                this.open = false
              } else this.$message.error(msg)
            })
          } else {
            codingRuleAdd(this.form).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('新增成功')
                this.getList()
                this.open = false
              } else this.$message.error(msg)
            })
          }
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';
.p20 {
  padding: 20px;
}
</style>
