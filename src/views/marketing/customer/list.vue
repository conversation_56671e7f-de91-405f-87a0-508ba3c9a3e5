<template>
  <div class="newBox bgcf9 vh-85">
    <!-- 搜索 -->
    <div class="custom-search flex">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
        <el-form-item label="客户姓名" prop="name">
          <el-input v-model="queryParams.name" placeholder="请输入客户姓名" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="公司名称" prop="company">
          <el-input v-model="queryParams.company" placeholder="请输入公司名称" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="所属分类" prop="classId">
          <el-select v-model="queryParams.classId" placeholder="请选择客户所属分类" clearable @change="handleQuery">
            <el-option v-for="item in categoryOptions" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" plain icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
          <el-button type="primary" icon="el-icon-plus" size="small" @click="handleAdd">新增客户</el-button>
          <el-button type="success" icon="el-icon-upload2" size="small" @click="handleImport">导入客户</el-button>
        </el-form-item>
      </el-form>
    </div>
    <!-- 列表 -->
    <div class="tableBox">
      <template v-if="total">
        <el-table v-loading="loading" ref="table" stripe :data="list" row-key="id" style="width: 100%" class="custom-table">
          <el-table-column align="center" label="序号" type="index" />
          <el-table-column label="公司名称" align="center" prop="company" />
          <el-table-column label="所在地" align="center" prop="address" />
          <el-table-column label="客户姓名" align="center" prop="name" />
          <el-table-column label="联系方式" align="center" prop="phone" />
          <el-table-column label="所属分类" align="center" prop="classId" :formatter="categoryFormat" />
          <el-table-column label="备注" align="center" prop="remark" />
          <el-table-column label="创建时间" align="center" prop="createTime" />
          <el-table-column align="center" label="操作" width="220">
            <template slot-scope="{ row }">
              <button type="button" class="table-btn primary" @click="handleUpdate(row)">
                <i class="el-icon-edit"></i>
                修改
              </button>
              <button type="button" class="table-btn danger" @click="handleDelete(row)">
                <i class="el-icon-delete"></i>
                删除
              </button>
            </template>
          </el-table-column>
        </el-table>
        <div class="custom-pagination">
          <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
        </div>
      </template>
      <el-empty :image-size="200" v-else />
    </div>
    <!--  新增/修改  -->
    <el-dialog v-dialogDragBox :title="title" :visible.sync="open" width="1150px" class="custom-dialog">
      <div style="padding: 0 20px">
        <el-form ref="form" :model="form" :rules="rules" label-width="80px">
          <el-row :gutter="10">
            <el-col :span="12">
              <el-form-item label="公司名称" prop="company">
                <el-input v-model="form.company" placeholder="请输入公司名称"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="所在地" prop="address">
                <el-input v-model="form.address" placeholder="请输入所在地"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="客户姓名" prop="name">
                <el-input v-model="form.name" placeholder="请输入客户姓名"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="联系方式" prop="phone">
                <el-input v-model="form.phone" placeholder="请输入联系方式"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="所属分类" prop="classId">
                <el-select v-model="form.classId" filterable placeholder="请选择所属分类" style="width: 100%">
                  <el-option v-for="item in categoryOptions" :key="item.id" :label="item.name" :value="item.id"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="备注" prop="remark">
                <el-input v-model="form.remark" placeholder="请输入备注"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <button type="button" class="custom-dialog-btn" @click="open = false">取消</button>
        <button type="button" class="custom-dialog-btn primary" @click="handleSubmit">提交</button>
      </div>
    </el-dialog>
    <!-- 客户导入 -->
    <el-dialog v-dialogDragBox :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-select v-model="upload.classId" placeholder="请选择所属分类" style="width: 100%; margin-bottom: 10px" @change="upload.isUploading = false" size="small">
        <el-option v-for="item in categoryOptions" :key="item.id" :label="item.name" :value="item.id" />
      </el-select>
      <el-upload ref="upload" :limit="1" accept=".xlsx, .xls" :headers="upload.headers" :action="upload.url" :disabled="upload.isUploading" :data="{ classId: upload.classId }" :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess" :auto-upload="false" drag>
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">
          将文件拖到此处，或
          <em>点击上传</em>
        </div>
        <div class="el-upload__tip text-center" slot="tip">
          <span>请先选择所属分类，仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size: 12px; vertical-align: baseline" @click="importTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { customerList, customerAdd, customerEdit, customerDel, categoryList } from '@/api/marketing'
import { getToken } from '@/utils/auth'

export default {
  name: 'Mlist',
  data() {
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        company: undefined,
        name: undefined,
        classId: undefined
      },
      loading: true,
      list: [],
      total: 0,
      title: '',
      open: false,
      form: {},
      rules: {
        company: [{ required: true, message: '请输入公司名称', trigger: 'blur' }],
        address: [{ required: true, message: '请输入所在地', trigger: 'blur' }],
        name: [{ required: true, message: '请输入客户姓名', trigger: 'blur' }],
        phone: [
          { required: true, message: '请输入联系方式', trigger: 'blur' },
          {
            pattern: /^1[3-9][0-9]\d{8}$/,
            message: '请输入正确的手机号码',
            trigger: 'blur'
          }
        ],
        classId: [{ required: true, message: '请选择所属分类', trigger: 'change' }]
      },
      categoryOptions: [],
      // 客户导入
      upload: {
        open: false,
        title: '',
        classId: undefined,
        isUploading: true,
        headers: { Authorization: 'Bearer ' + getToken() },
        url: process.env.VUE_APP_BASE_API + '/marketing/custom/importData'
      }
    }
  },
  created() {
    this.getCategoryOptions()
    this.getList()
  },
  methods: {
    // 回显分类
    categoryFormat(row, column) {
      const data = this.categoryOptions.find(item => item.id === row.classId)
      return data.name || ''
    },
    // 查询分类
    getCategoryOptions() {
      categoryList().then(res => {
        const { code, msg, data } = res
        if (code === 200) this.categoryOptions = data
        else this.$message.error(msg)
      })
    },
    // 列表
    getList() {
      this.loading = true
      customerList(this.queryParams).then(res => {
        const { code, msg, rows, total } = res
        if (code === 200) {
          this.list = rows
          this.total = total
          this.loading = false
        } else this.$message.error(msg)
      })
    },
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 重置搜索
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 表单重置
    reset() {
      this.form = {
        address: undefined,
        classId: undefined,
        company: undefined,
        id: undefined,
        name: undefined,
        phone: undefined,
        remark: undefined
      }
      this.resetForm('form')
    },
    // 新增
    handleAdd() {
      this.reset()
      this.title = '新增客户'
      this.open = true
    },
    // 修改
    handleUpdate(row) {
      this.reset()
      this.form = { ...row }
      this.title = '修改客户'
      this.open = true
    },
    // 提交
    handleSubmit() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            customerEdit(this.form).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('修改成功')
                this.open = false
                this.getList()
              } else this.$message.error(msg)
            })
          } else {
            customerAdd(this.form).then(res => {
              if (res.code === 200) {
                this.$message.success('新增成功')
                this.open = false
                this.getList()
              } else this.$message.error(msg)
            })
          }
        }
      })
    },
    // 删除
    // prettier-ignore
    handleDelete(row) {
      const data = { customId: row.id }
      this.$modal.confirm('是否确认删除选中的客户？').then(function () {
        return customerDel(data)
      }).then(() => {
        this.getList()
        this.$message.success('删除成功')
      }).catch(() => {
      })
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = '导入客户'
      this.upload.classId = undefined
      this.upload.isUploading = true
      this.upload.open = true
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download('marketing/custom/importTemplate', {}, `custom_template_${new Date().getTime()}.xlsx`)
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false
      this.upload.isUploading = false
      this.$refs.upload.clearFiles()
      this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + '</div>', '导入结果', { dangerouslyUseHTMLString: true })
      this.getList()
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit()
    }
  }
}
</script>

<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';
.custom-search {
  padding-top: 18px;
  padding-bottom: 0;
  background-color: transparent;
}
.tableBox {
  margin: 20px 10px;
  padding: 20px 10px;
  background-color: $white;
  border-bottom-left-radius: 5px;
  border-bottom-right-radius: 5px;
}
</style>
