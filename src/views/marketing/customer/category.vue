<template>
  <div class="newBox bgcf9 vh-85">
    <!-- 搜索 -->
    <div class="custom-search flex">
      <div class="custom-search-form flex">
        <input type="text" v-model="name" placeholder="请输入分类名称" class="custom-search-input" @keyup.enter="getList" />
        <button type="button" class="custom-search-button pointer" @click="getList">
          <i class="el-icon-search"></i>
          搜索
        </button>
      </div>
      <button type="button" class="custom-search-add" @click="handleAdd">
        <i class="el-icon-plus"></i>
        新增分类
      </button>
    </div>

    <!-- 列表 -->
    <div class="tableBox">
      <template v-if="list.length">
        <el-table v-loading="loading" ref="table" stripe :data="list" row-key="id" style="width: 100%" class="custom-table">
          <el-table-column align="center" type="index" label="序号"></el-table-column>
          <el-table-column align="center" prop="name" label="分类名称" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="businessScope" label="分类详细" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="createBy" label="创建人" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="createTime" label="创建日期" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" label="操作">
            <template slot-scope="{ row }">
              <button type="button" class="table-btn primary" @click="handleView(row)">
                <i class="el-icon-view"></i>
                查看详情
              </button>
              <button type="button" class="table-btn danger" @click="handleDelete(row)">
                <i class="el-icon-delete"></i>
                删除
              </button>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <el-empty :image-size="200" v-else />
    </div>

    <!--  新增/修改  -->
    <el-dialog v-dialogDragBox :title="title" :visible.sync="open" width="1150px" class="custom-dialog">
      <div style="padding: 0 20px">
        <el-form ref="form" :model="form" :rules="rules" label-width="80px" :disabled="!isEdit && !!form.id">
          <el-form-item label="类别名称" prop="name">
            <el-input v-model="form.name" placeholder="请输入类别名称"></el-input>
          </el-form-item>
          <el-form-item label="产品范围" prop="productScope">
            <el-input v-model="form.productScope" placeholder="请输入产品范围" type="textarea" :autosize="{ minRows: 3, maxRows: 5 }" resize="none"></el-input>
          </el-form-item>
          <el-form-item label="经营产品" prop="businessScope">
            <el-input v-model="form.businessScope" placeholder="请输入经营产品" type="textarea" :autosize="{ minRows: 3, maxRows: 5 }" resize="none"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer">
        <button type="button" class="custom-dialog-btn" @click="open = false">取消</button>
        <button type="button" class="custom-dialog-btn primary" @click="isEdit = true" v-if="!isEdit && !!form.id">修改</button>
        <button type="button" class="custom-dialog-btn primary" @click="handleSubmit" v-else>提交</button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { categoryAdd, categoryDel, categoryEdit, categoryList } from '@/api/marketing'

export default {
  name: 'Mcategory',
  data() {
    return {
      loading: true,
      list: [],
      name: undefined,
      title: '',
      open: false,
      form: {},
      rules: {
        name: [{ required: true, message: '请输入类别名称', trigger: 'blur' }],
        productScope: [{ required: true, message: '请输入产品范围', trigger: 'blur' }],
        businessScope: [{ required: true, message: '请输入经营产品', trigger: 'blur' }]
      },
      isEdit: false
    }
  },
  created() {
    this.getList()
  },
  methods: {
    // 列表
    getList() {
      this.loading = true
      categoryList({ name: this.name }).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          this.list = data
          this.loading = false
        } else this.$message.error(msg)
      })
    },
    // 表单重置
    reset() {
      this.form = {
        businessScope: undefined,
        id: undefined,
        name: undefined,
        productScope: undefined
      }
      this.resetForm('form')
    },
    // 新增
    handleAdd() {
      this.reset()
      this.isEdit = false
      this.title = '新增分类'
      this.open = true
    },
    // 详情
    handleView(row) {
      this.reset()
      this.form = { ...row }
      this.isEdit = false
      this.title = '分类详情'
      this.open = true
    },
    // 提交
    handleSubmit() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            categoryEdit(this.form).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('修改成功')
                this.open = false
                this.getList()
              } else this.$message.error(msg)
            })
          } else {
            categoryAdd(this.form).then(res => {
              if (res.code === 200) {
                this.$message.success('新增成功')
                this.open = false
                this.getList()
              } else this.$message.error(msg)
            })
          }
        }
      })
    },
    // 删除
    // prettier-ignore
    handleDelete(row) {
      const data = { classId: row.id }
      this.$modal.confirm('是否确认删除选中的分类？').then(function () {
        return categoryDel(data)
      }).then(() => {
        this.getList()
        this.$message.success('删除成功')
      }).catch(() => {
      })
    }
  }
}
</script>

<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';
.custom-search {
  padding-top: 10px;
  padding-bottom: 10px;
  background-color: transparent;
}
.tableBox {
  margin: 20px 10px;
  padding: 20px 10px;
  background-color: $white;
  border-bottom-left-radius: 5px;
  border-bottom-right-radius: 5px;
}
.custom-dialog ::v-deep {
  textarea {
    font-family: inherit;
  }
}
</style>
