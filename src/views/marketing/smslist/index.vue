<template>
  <div class="newBox bgcf9 vh-85">
    <!-- 搜索 -->
    <div class="custom-search flex">
      <div class="custom-search-form flex">
        <input type="text" v-model="queryParams.name" placeholder="请输入短信模板名称" class="custom-search-input"
          @keyup.enter="getList" />
        <button type="button" class="custom-search-button pointer" @click="getList">
          <i class="el-icon-search"></i>
          搜索
        </button>
      </div>
      <button type="button" class="custom-search-add" @click="handleAdd">
        <i class="el-icon-plus"></i>
        新建短信
      </button>
    </div>
    <!-- 列表 -->
    <div class="tableBox">
      <template v-if="total">
        <el-table v-loading="loading" ref="table" stripe :data="list" row-key="id" style="width: 100%"
          class="custom-table">
          <el-table-column align="center" type="index" label="序号"></el-table-column>
          <el-table-column prop="content" label="短信内容概要" show-overflow-tooltip>
            <template slot-scope="{ row }">{{ row.template.content }}</template>
          </el-table-column>
          <el-table-column align="center" prop="createBy" label="创建人" width="100" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="createTime" label="创建时间" width="120"
            show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="sendTimes" label="发送次数" width="100"
            show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="status" label="状态" width="210" show-overflow-tooltip>
            <template slot-scope="{ row }">
              <el-statistic format="DD天HH小时mm分钟ss秒后发送" :value="new Date(row.sendTime).getTime()" time-indices title="待发送"
                v-if="row.status === 0 && new Date(row.sendTime).getTime() > new Date().getTime()"
                class="orange"></el-statistic>
              <span :class="statusFormat(row, 'class')" v-else>{{ statusFormat(row) }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="操作" width="330">
            <template slot-scope="{ row }">
              <button type="button" class="table-btn" :class="{ disabled: row.status !== 1 }" :disabled="row.status !== 1"
                @click="handleReceipt(row)">
                <i class="el-icon-document"></i>
                回执列表
              </button>
              <button type="button" class="table-btn" @click="handleUpdate(row)">
                <i class="el-icon-edit"></i>
                修改
              </button>
              <button type="button" class="table-btn primary" :class="{ disabled: row.status === 1 }"
                :disabled="row.status === 1" @click="handleSend(row)">
                <i class="el-icon-s-promotion"></i>
                发送
              </button>
            </template>
          </el-table-column>
        </el-table>
        <div class="custom-pagination">
          <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize" @pagination="getList" />
        </div>
      </template>
      <el-empty :image-size="200" v-else />
    </div>
    <!--  新增/修改  -->
    <el-dialog v-dialogDragBox :title="title" :visible.sync="open" width="1150px" class="custom-dialog">
      <div style="padding: 0 20px" v-if="setp === 1">
        <!-- 搜索 -->
        <el-form :model="userParams" ref="queryForm" size="small" :inline="true">
          <el-form-item label="客户姓名" prop="name">
            <el-input v-model="userParams.name" placeholder="请输入客户姓名" clearable @keyup.enter.native="handleUserQuery" />
          </el-form-item>
          <el-form-item label="公司名称" prop="company">
            <el-input v-model="userParams.company" placeholder="请输入公司名称" clearable
              @keyup.enter.native="handleUserQuery" />
          </el-form-item>
          <el-form-item label="所属分类" prop="classId">
            <el-select v-model="userParams.classId" placeholder="请选择客户所属分类" clearable @change="handleUserQuery">
              <el-option v-for="item in categoryOptions" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" plain icon="el-icon-search" size="small" @click="handleUserQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="small" @click="resetUserQuery">重置</el-button>
          </el-form-item>
        </el-form>
        <div v-if="userParams.classId">
          <el-radio-group v-model="radio" style="margin-bottom: 15px; margin-top: 20px;">
            <el-radio :label="userParams.classId">选择该分类下的所有人员</el-radio>
            <el-radio :label="undefined">自定义选择所需发送短信人员</el-radio>
          </el-radio-group>
        </div>
        <template v-if="userTotal">
          <el-table v-loading="userLoading" ref="userTable" stripe :data="userList" row-key="id" style="width: 100%"
            class="custom-table" @select="handleSelectUser" @select-all="handleSelectUserAll" :reserve-selection="true">
            <el-table-column align="center" type="selection" width="50" v-if="!radio || !userParams.classId"></el-table-column>
            <el-table-column align="center" label="序号" type="index" />
            <el-table-column label="公司名称" align="center" prop="company" />
            <el-table-column label="所在地" align="center" prop="address" />
            <el-table-column label="客户姓名" align="center" prop="name" />
            <el-table-column label="联系方式" align="center" prop="phone" />
            <el-table-column label="所属分类" align="center" prop="classId" :formatter="categoryFormat" />
            <el-table-column label="备注" align="center" prop="remark" />
          </el-table>
          <div class="custom-pagination">
            <pagination v-show="userTotal > 0" :total="userTotal" :page.sync="userParams.pageNum"
              :limit.sync="userParams.pageSize" @pagination="getUserList" />
          </div>
        </template>
        <el-empty :image-size="200" v-else />
      </div>
      <div style="padding: 0 20px" v-if="setp === 2">
        <el-form :model="form" ref="form" :rules="rules" label-width="100px">
          <el-form-item label="短信模板" prop="templateId" v-if="!isEdit">
            <el-select v-model="form.templateId" placeholder="请选择短信模板" filterable clearable style="width: 100%">
              <el-option v-for="item in templateOptions" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="短信签名" prop="signName" v-if="!isEdit">
            <el-select v-model="form.signName" placeholder="请选择短信签名" filterable clearable style="width: 100%">
              <el-option v-for="item in signNameOptions" :key="item" :label="item" :value="item" />
            </el-select>
          </el-form-item>
          <el-form-item label="计划发送时间" prop="sendTime">
            <el-date-picker v-model="form.sendTime" type="datetime" placeholder="请选择计划发送时间" clearable
              value-format="timestamp" style="width: 100%" />
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer">
        <template v-if="setp === 1">
          <el-badge>
            <button type="button" class="custom-dialog-btn" @click="open = false">取消</button>
          </el-badge>
          <el-badge :value="checkedList.length" :hidden="!checkedList.length">
            <button type="button" class="custom-dialog-btn primary"
              :class="{ disabled: !(checkedList.length || radio) }"
              :disabled="!(checkedList.length || radio)" @click="handleNext">下一步</button>
          </el-badge>
        </template>
        <template v-if="setp === 2">
          <button type="button" class="custom-dialog-btn" @click="open = false" v-if="isEdit">取消</button>
          <button type="button" class="custom-dialog-btn" @click="handlePrev" v-else>上一步</button>
          <button type="button" class="custom-dialog-btn primary" @click="handleSubmit">提交</button>
        </template>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { categoryList, customerList, smsList, smsAdd, smsSend, smsEdit, smsTemplateList } from '@/api/marketing'

export default {
  name: 'Smslist',
  data() {
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: undefined
      },
      list: [],
      total: 0,
      loading: true,
      statusOptions: [
        { label: '未设置', value: -1, class: 'info' },
        { label: '待发送', value: 0, class: 'orange' },
        { label: '已发送', value: 1, class: 'blue' }
      ],
      signNameOptions: ['世盛商城', '自由客紧固件', '世盛金属'],
      // 新增/修改
      open: false,
      title: '',
      form: {},
      rules: {
        templateId: [{ required: true, message: '请选择短信模板', trigger: 'change' }],
        url: [
          { required: true, message: '请输入推广链接', trigger: 'blur' },
          {
            pattern: /(http|https):\/\/([\w.]+\/?)\S*/,
            message: '推广链接输入不正确',
            trigger: ['blur', 'change']
          }
        ],
        signName: [{ required: true, message: '请选择短信签名', trigger: 'change' }]
      },
      userParams: {
        pageNum: 1,
        pageSize: 10,
        name: undefined,
        company: undefined,
        classId: undefined
      },
      userTotal: 0,
      userLoading: true,
      userList: [],
      checkedList: [],
      categoryOptions: [],
      setp: 1,
      isEdit: false,
      templateOptions: [],
      radio: undefined
    }
  },
  created() {
    this.getCategoryOptions()
    this.getTemplateOptions()
    this.getList()
  },
  methods: {
    // 查询短信模板列表
    getTemplateOptions() {
      const query = { pageNum: 1, pageSize: 1000, status: 1 }
      smsTemplateList(query).then(res => {
        const { code, msg, rows } = res
        if (code === 200) this.templateOptions = rows
        else this.$message.error(msg)
      })
    },
    // 回显状态
    statusFormat(row, type = 'label') {
      const data = this.statusOptions.find(item => item.value === row.status)
      return data[type] || ''
    },
    // 回显分类
    categoryFormat(row, column) {
      const data = this.categoryOptions.find(item => item.id === row.classId)
      return data.name || ''
    },
    // 查询分类
    getCategoryOptions() {
      categoryList().then(res => {
        const { code, msg, data } = res
        if (code === 200) this.categoryOptions = data
        else this.$message.error(msg)
      })
    },
    // 短信列表
    getList() {
      this.loading = true
      smsList(this.queryParams).then(res => {
        const { code, msg, rows, total } = res
        if (code === 200) {
          this.list = rows
          this.total = total
          this.loading = false
        } else this.$message.error(msg)
      })
    },
    // 新增短信
    handleAdd() {
      this.open = true
      this.resetForm('queryForm')
      this.checkedList = []
      this.getUserList()
      this.isEdit = false
      this.setp = 1
      this.title = '新增短信'
    },
    // 下一步
    handleNext() {
      this.form = {
        sendTime: undefined,
        templateId: undefined,
        url: undefined
      }
      this.resetForm('form')
      this.setp = 2
    },
    // 上一步
    handlePrev() {
      this.setp = 1
      this.echo()
    },
    // 发送
    handleSend(row) {
      const data = { msgId: row.id }
      smsSend(data).then(res => {
        const { code, msg } = res
        if (code === 200) {
          this.$message.success('新增成功')
          this.getList()
        } else this.$message.error(msg)
      })
    },
    // 用户列表
    getUserList() {
      this.userLoading = true
      customerList(this.userParams).then(res => {
        const { code, msg, rows, total } = res
        if (code === 200) {
          this.userList = rows
          this.userTotal = total
          this.userLoading = false
        } else this.$message.error(msg)
      })
    },
    // 搜索
    handleUserQuery() {
      this.userParams.pageNum = 1
      this.getUserList()
    },
    // 重置搜索
    resetUserQuery() {
      this.resetForm('queryForm')
      this.handleUserQuery()
    },
    // 选择客户
    handleSelectUser(selection, row) {
      const idx = this.checkedList.findIndex(item => item.id === row.id)
      if (idx === -1) {
        this.checkedList.push(row)
      } else {
        this.checkedList.splice(idx, 1)
      }
    },
    // 全选
    handleSelectUserAll(selection) {
      if (selection.length) {
        if (this.checkedList.length) {
          selection.map(ite => {
            const idx = this.checkedList.findIndex(item => item.id === ite.id)
            if (idx === -1) this.checkedList.push(ite)
          })
        } else {
          this.checkedList = [...selection]
        }
      } else {
        this.userList.map(ite => {
          const idx = this.checkedList.findIndex(item => item.id === ite.id)
          if (idx !== -1) this.checkedList.splice(idx, 1)
        })
      }
    },
    // 回显选中
    echo() {
      this.$nextTick(() => {
        if (this.$refs.userTable) this.$refs.userTable.clearSelection()
        this.userList.map(item => {
          this.checkedList.map(itt => {
            if (itt.id === item.id) this.$refs.userTable.toggleRowSelection(item, true)
          })
        })
      })
    },
    // 修改
    handleUpdate(row) {
      this.form = { ...row }
      this.form.sendTime = new Date(row.sendTime)
      this.title = '修改短信'
      this.setp = 2
      this.isEdit = true
      this.open = true
    },
    // 提交
    handleSubmit() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.isEdit) {
            const data = { msgId: this.form.id, sendTime: this.form.sendTime }
            smsEdit(data).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('修改成功')
                this.open = false
                this.getList()
              } else this.$message.error(msg)
            })
          } else {
            let sendData = []
            let data = {}
            if (this.checkedList.length > 0) {
              this.checkedList.map(item => {
                sendData.push({ id: item.id, name: item.name, phone: item.phone })
              })
              data = { ...this.form, ...{ sendData } }
            } else if (this.checkedList.length == 0) {
              this.form.classId = this.radio
              data = { ...this.form }
            }
            smsAdd(data).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success(msg)
                this.open = false
                this.getList()
              } else this.$message.error(msg)
            })
          }
        }
      })
    },
    // 打开回执列表
    handleReceipt(row) {
      this.$router.push({ path: '/marketing/receipt', query: { id: row.id } })
    }
  }
}
</script>

<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';

.custom-search {
  padding-top: 10px;
  padding-bottom: 10px;
  background-color: transparent;
}

.tableBox {
  margin: 20px 10px;
  padding: 20px 10px;
  background-color: $white;
  border-bottom-left-radius: 5px;
  border-bottom-right-radius: 5px;
}

.custom-dialog ::v-deep {
  textarea {
    font-family: inherit;
  }
}

.tabBox {
  padding: 0 20px;
  background-color: $white;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

::v-deep {
  .info {
    color: $disabled;
  }

  .orange {
    .head {
      height: auto;
      background: none;
      color: $orange;
      font-size: 12px;
    }

    .con {
      background-color: #f42328;
      color: $white;
      border-radius: 5px;

      .number {
        font-size: 12px;
        line-height: 22px;
      }
    }
  }

  .blue {
    color: $blue;
  }
}
</style>
