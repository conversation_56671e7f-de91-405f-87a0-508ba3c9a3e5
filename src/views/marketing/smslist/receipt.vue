<template>
  <div class="newBox bgcf9 vh-85">
    <!-- 搜索 -->
    <div class="custom-search flex" style="padding-top: 20px">
      <el-form :model="queryParams" ref="queryParams" size="small" :inline="true" @submit.native.prevent>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="queryParams.phone" placeholder="请输入手机号" clearable @keyup.enter.native="handleQuery"></el-input>
        </el-form-item>
        <el-form-item label="接收状态" prop="success">
          <el-select v-model="queryParams.success" clearable placeholder="请选择接收状态" @change="handleQuery">
            <el-option label="接收成功" value="true"></el-option>
            <el-option label="接收失败" value="false"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <!-- 列表 -->
    <div class="tableBox">
      <template v-if="total">
        <el-table v-loading="loading" ref="table" stripe :data="list" row-key="id" style="width: 100%" class="custom-table">
          <el-table-column align="center" type="index" label="序号"></el-table-column>
          <el-table-column align="center" prop="phoneNum" label="手机号" width="100" show-overflow-tooltip></el-table-column>
          <el-table-column prop="content" label="短信内容概要" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="status" label="接收状态" width="100" show-overflow-tooltip>
            <template slot-scope="{ row }">{{ row.errMsg }}</template>
          </el-table-column>
          <el-table-column align="center" prop="createBy" label="创建人" width="100" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="createTime" label="创建时间" width="150" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="sendDate" label="发送时间" width="150" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="receiveDate" label="接收时间" width="150" show-overflow-tooltip></el-table-column>
        </el-table>
        <div class="custom-pagination">
          <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
        </div>
      </template>
      <el-empty :image-size="200" v-else />
    </div>
  </div>
</template>
<script>
import { smsReceiptList, smsReceipt } from '@/api/marketing'

export default {
  data() {
    return {
      msgId: undefined,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        msgId: undefined,
        phone: undefined,
        success: undefined
      },
      loading: true,
      list: [],
      total: 0
    }
  },
  created() {
    const { id } = this.$route.query
    if (id) {
      this.msgId = id
      this.queryParams.msgId = id
      this.getList()
    } else {
      this.$alert('参数错误，请联系管理员', '系统提示', {
        type: 'error',
        confirmButtonText: '确定',
        callback: action => {
          this.$router.push('/')
        }
      })
    }
  },
  methods: {
    getList() {
      this.loading = true
      smsReceiptList(this.queryParams).then(res => {
        const { code, msg, rows, total } = res
        if (code === 200) {
          this.list = rows
          this.total = total
          this.loading = false
        } else this.$message.error(msg)
      })
    },
    // 查询
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 重置
    resetQuery() {
      this.resetForm('queryParams')
      this.handleQuery()
    }
  }
}
</script>

<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';
.tableBox {
  margin: 20px 10px;
  padding: 20px 10px;
  background-color: $white;
  border-bottom-left-radius: 5px;
  border-bottom-right-radius: 5px;
}
</style>
