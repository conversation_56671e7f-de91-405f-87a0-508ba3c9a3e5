<template>
  <div class="newBox bgcf9 vh-85">
    <!-- 搜索 -->
    <div class="custom-search flex">
      <div class="custom-search-form flex">
        <input type="text" v-model="queryParams.name" placeholder="请输入短信模板名称" class="custom-search-input" @keyup.enter="getList" />
        <button type="button" class="custom-search-button pointer" @click="getList">
          <i class="el-icon-search"></i>
          搜索
        </button>
      </div>
      <button type="button" class="custom-search-add" @click="handleAdd">
        <i class="el-icon-plus"></i>
        新增模板
      </button>
    </div>
    <div class="tabBox">
      <div class="tabBox-item" :class="{ active: !activeId }" @click="getList()">所有模板</div>
      <div class="tabBox-item" v-for="item in categoryOptions" :key="item.id" :class="{ active: item.id === activeId }" @click="getList(item.id)">
        {{ item.name }}
      </div>
    </div>

    <div class="tipDesc">
      <i class="el-icon-info"></i>
      审核时长一般2小时内完成，近期平均完成审核时长约
      <span>1小时</span>
      ，如遇升级核验、审核任务较多、非工作时间，审核时间可能会延长，请耐心等待，审核工作时间：周一至周日9:00-21:00(法定节假日顺延)
    </div>

    <!-- 列表 -->
    <div class="tableBox">
      <template v-if="total">
        <el-table v-loading="loading" ref="table" stripe :data="list" row-key="id" style="width: 100%" class="custom-table">
          <el-table-column align="center" type="index" label="序号"></el-table-column>
          <el-table-column align="center" prop="name" label="短信模板名称" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="content" label="短信内容概要" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="classId" label="所属分类" show-overflow-tooltip :formatter="categoryFormat"></el-table-column>
          <el-table-column align="center" prop="status" label="状态" show-overflow-tooltip>
            <template slot-scope="{ row }">
              <span>{{ statusFormat(row) }}</span>
              <el-button type="text" icon="el-icon-refresh" size="small" @click="handleRefresh(row)" v-if="row.status !== 1"></el-button>
              <el-tooltip class="item" effect="dark" :content="row.reason" placement="top-start" v-if="row.status === 2">
                <i class="el-icon-question"></i>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="remark" label="申请说明" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="createBy" label="创建人" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="createTime" label="创建时间" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" label="操作" width="220">
            <template slot-scope="{ row }">
              <button type="button" class="table-btn primary" @click="handleUpdate(row)">
                <i class="el-icon-edit"></i>
                修改内容
              </button>
              <button type="button" class="table-btn danger" @click="handleDelete(row)">
                <i class="el-icon-delete"></i>
                删除
              </button>
            </template>
          </el-table-column>
        </el-table>
        <div class="custom-pagination">
          <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
        </div>
      </template>
      <el-empty :image-size="200" v-else />
    </div>

    <!--  新增/修改  -->
    <el-dialog v-dialogDragBox :title="title" :visible.sync="open" width="1150px" class="custom-dialog">
      <div style="padding: 0 20px">
        <el-form ref="form" :model="form" :rules="rules" label-width="80px">
          <el-form-item label="模板名称" prop="name">
            <el-input v-model="form.name" placeholder="请输入模板名称"></el-input>
          </el-form-item>
          <el-form-item label="模板内容" prop="content">
            <el-input v-model="form.content" placeholder="请输入模板内容" type="textarea" :autosize="{ minRows: 3, maxRows: 5 }" resize="none"></el-input>
          </el-form-item>
          <el-form-item label="所属分类" prop="classId" v-if="!form.id">
            <el-select v-model="form.classId" filterable placeholder="请选择所属分类" style="width: 100%">
              <el-option v-for="item in categoryOptions" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="申请说明" prop="remark">
            <el-input v-model="form.remark" placeholder="短信模板申请说明，是模板审核的参考信息之一" maxlength="100"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer">
        <button type="button" class="custom-dialog-btn" @click="open = false">取消</button>
        <button type="button" class="custom-dialog-btn primary" @click="handleSubmit">提交</button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { smsTemplateAdd, smsTemplateDel, smsTemplateEdit, smsTemplateList, categoryList, smsTemplateRefresh } from '@/api/marketing'

export default {
  name: 'Template',
  data() {
    return {
      loading: true,
      list: [],
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: undefined
      },
      title: '',
      open: false,
      form: {},
      rules: {
        name: [{ required: true, message: '请输入模板名称', trigger: 'blur' }],
        content: [{ required: true, message: '请输入模板内容', trigger: 'blur' }],
        classId: [{ required: true, message: '请选择所属分类', trigger: 'change' }],
        remark: [{ required: true, message: '请输入申请说明', trigger: 'blur' }]
      },
      categoryOptions: [],
      activeId: undefined,
      statusOptions: [
        { label: ' 审核中', value: 0 },
        { label: ' 审核通过', value: 1 },
        { label: ' 审核未通过', value: 2 },
        { label: ' 取消审核', value: 10 }
      ]
    }
  },
  created() {
    this.getCategoryOptions()
    this.getList()
  },
  methods: {
    // 回显状态
    statusFormat(row) {
      const data = this.statusOptions.find(item => item.value === row.status)
      return data.label || ''
    },
    // 回显分类
    categoryFormat(row, column) {
      const data = this.categoryOptions.find(item => item.id === row.classId)
      return data.name || ''
    },
    // 查询分类
    getCategoryOptions() {
      categoryList().then(res => {
        const { code, msg, data } = res
        if (code === 200) this.categoryOptions = data
        else this.$message.error(msg)
      })
    },
    // 列表
    getList(id = undefined) {
      this.activeId = id
      this.queryParams.classId = id
      this.loading = true
      smsTemplateList(this.queryParams).then(async res => {
        const { code, msg, rows, total } = res
        if (code === 200) {
          await Promise.all(
            rows.map(async item => {
              if (item.status === 0) {
                const params = { templateCode: item.templateCode, force: false }
                const { data } = await smsTemplateRefresh(params)
                item.status = data.status
              }
            })
          )
          this.list = rows
          this.total = total
          this.loading = false
        } else this.$message.error(msg)
      })
    },
    // 表单重置
    reset() {
      this.form = {
        classId: undefined,
        content: undefined,
        id: undefined,
        name: undefined
      }
      this.resetForm('form')
    },
    // 新增
    handleAdd() {
      this.reset()
      this.title = '新增短信模板'
      this.open = true
    },
    // 修改
    handleUpdate(row) {
      this.reset()
      this.form = { ...row }
      this.title = '修改短信模板'
      this.open = true
    },
    // 提交
    handleSubmit() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            smsTemplateEdit(this.form).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('修改成功')
                this.open = false
                this.getList()
              } else this.$message.error(msg)
            })
          } else {
            smsTemplateAdd(this.form).then(res => {
              if (res.code === 200) {
                this.$message.success('新增成功')
                this.open = false
                this.getList()
              } else this.$message.error(msg)
            })
          }
        }
      })
    },
    // 删除
    // prettier-ignore
    handleDelete(row) {
      const data = { messageId: row.id }
      this.$modal.confirm('是否确认删除选中的短信模板？').then(function () {
        return true
      }).then(() => {
        this.$modal.confirm('短信模板审核比较繁重，请谨慎操作，请再次点击确定!').then(function () {
          return smsTemplateDel(data)
        }).then(() => {
          this.getList()
          this.$message.success('删除成功')
        }).catch(() => {
        })
      }).catch(() => {
      })
    },
    // 刷新状态
    // prettier-ignore
    handleRefresh(row) {
      const { templateCode } = row
      this.$modal.confirm('是否确认刷新选中的短信模板状态？').then(function () {
        return smsTemplateRefresh({ templateCode, force: true })
      }).then(() => {
        this.getList()
        this.$message.success('刷新成功')
      }).catch(() => {
      })
    }
  }
}
</script>

<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';
.custom-search {
  padding-top: 10px;
  padding-bottom: 10px;
  background-color: transparent;
}
.tableBox {
  margin: 20px 10px;
  padding: 20px 10px;
  background-color: $white;
  border-bottom-left-radius: 5px;
  border-bottom-right-radius: 5px;
}
.custom-dialog ::v-deep {
  textarea {
    font-family: inherit;
  }
}
.tabBox {
  padding: 0 20px;
  background-color: $white;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  &-item {
    padding: 0 30px;
    font-size: 14px;
    cursor: pointer;
    color: $info;
    height: 45px;
    line-height: 45px;
    &.active,
    &:hover {
      border-bottom: 2px solid $blue;
    }
  }
}
.tipDesc {
  margin: 10px;
  padding: 10px;
  background-color: #e5f3ff;
  font-size: 12px;
  border-radius: 5px;
  .el-icon-info {
    color: $blue;
  }
  span {
    color: $red;
  }
}
</style>
