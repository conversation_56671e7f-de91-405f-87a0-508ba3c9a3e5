<template>
  <div>
    <el-dialog v-dialogDragBox :title="title" :visible.sync="open" width="1150px" class="custom-dialog">
      <div slot="title">
        <div class="custom-dialog-title">
          {{ title }}
          <el-switch v-model="form.isOpen" active-text="公开产品" inactive-text="不公开" class="custom-dialog-switch"></el-switch>
          <el-tooltip placement="right">
            <div slot="content" style="line-height: 1.5em">选择公开，该产品可以被其他用户搜索并查看详情，不公开则反之</div>
            <i class="el-icon-question" style="font-size: 20px; color: #cacdd6"></i>
          </el-tooltip>
        </div>
      </div>
      <div style="padding: 0 20px">
        <div class="hasProduct" v-if="hasList.length">
          <div class="hasProduct-title">已根据您录入的产品信息为您匹配到下列产品，您可以直接引用它们</div>
          <div class="hasProduct-content">
            <div class="hasProduct-item" v-for="item in hasList" :key="item.id">
              <el-tooltip effect="dark">
                <div slot="content">
                  <template v-if="item.specs">
                    规格：{{ item.specs }}
                    <br />
                  </template>
                  <template v-if="item.model">
                    型号：{{ item.model }}
                    <br />
                  </template>
                  <template v-if="item.standard">
                    标准：{{ item.standard }}
                    <br />
                  </template>
                  <template v-if="item.industry">
                    行业：{{ item.industry }}
                    <br />
                  </template>
                  <template v-if="item.materialQuality">
                    材质：{{ item.materialQuality }}
                    <br />
                  </template>
                  <template v-if="item.level">
                    等级：{{ item.level }}
                    <br />
                  </template>
                  <template v-if="item.surface">
                    表面处理：{{ item.surface }}
                    <br />
                  </template>
                </div>
                <span @click="handleView(item)">{{ item.productName }}</span>
              </el-tooltip>
              <el-button type="primary" icon="el-icon-share" round size="small" class="hasProduct-button" @click="handleQuote(item)">引用</el-button>
            </div>
          </div>
        </div>
        <el-form ref="form" :model="form" :rules="formrules" label-width="6em">
          <el-row :gutter="10" style="display: flex; flex-wrap: wrap">
            <el-col :span="6">
              <el-form-item label="所属类目">
                {{ form.parentName }}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="是否新增">
                <el-checkbox v-model="form.newCategory"></el-checkbox>
              </el-form-item>
            </el-col>
            <template v-if="form.newCategory">
              <el-col :span="6">
                <el-form-item label="新增类目">
                  <el-input v-model="form.name" placeholder="新类目名称"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="类目图片" prop="photoAddr">
                  <image-upload v-model="form.photoAddr" />
                </el-form-item>
              </el-col>
            </template>
          </el-row>
          <el-row :gutter="20" style="display: flex; flex-wrap: wrap">
            <el-col :span="12">
              <el-form-item label="产品名称" prop="productName">
                <el-input v-model="form.productName" placeholder="请输入产品名称" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="其他名称" prop="formerName">
                <el-input v-model="form.formerName" placeholder="请输入其他名称" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="编码分类" prop="mainArr">
                <el-cascader filterable v-model="form.mainArr" placeholder="请选择编码分类" style="width: 100%" :options="mainClassOptions" :props="mainClassProps" clearable @change="handleChange"></el-cascader>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="产品规格" prop="specs">
                <el-input v-model="form.specs" placeholder="请输入产品规格" @change="handleChange">
                  <template slot="suffix">
                    <el-tooltip placement="right">
                      <div slot="content" style="line-height: 1.5em">
                        栓：直径规格×扣长×对边×帽厚
                        <br />
                        例子：M16*30大对边螺栓，表示为00163024
                        <br />
                        型钢：宽×高×厚×长
                        <br />
                        例子：41*41*1.95T*6米型钢，表示为41411956
                        <br />
                        圆钢：直径×长度
                        <br />
                        圆管：直径×长度×厚
                        <br />
                        垫类：外径×内径×厚
                      </div>
                      <i class="el-icon-question" style="font-size: 20px"></i>
                    </el-tooltip>
                  </template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="产品型号" prop="model">
                <el-input v-model="form.model" placeholder="请输入产品型号" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="执行标准" prop="standard">
                <el-input v-model="form.standard" placeholder="请输入执行标准" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="行业分类" prop="industry">
                <el-select multiple :multiple-limit="multipleLimit" clearable filterable v-model="form.industry" placeholder="请选择行业分类" style="width: 100%" @change="handleChange">
                  <el-option v-for="(item, index) in industryOptions" :key="index" :label="item.name" :value="item.name"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="产品材质" prop="materialQuality">
                <el-select multiple :multiple-limit="multipleLimit" clearable filterable v-model="form.materialQuality" placeholder="请选择产品材质" style="width: 100%" @change="handleChange">
                  <el-option v-for="(item, index) in materialQualityOptions" :key="index" :label="item.name" :value="item.name"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="产品等级" prop="level">
                <el-select clearable filterable v-model="form.level" placeholder="请选择产品等级" style="width: 100%" @change="handleChange">
                  <el-option v-for="(item, index) in levelOptions" :key="index" :label="item.name" :value="item.name"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="表面处理" prop="surface">
                <el-select clearable filterable v-model="form.surface" placeholder="请选择表面处理" style="width: 100%" @change="handleChange">
                  <el-option v-for="(item, index) in surfaceOptions" :key="index" :label="item.name" :value="item.name"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="产品单位" prop="unit">
                <el-select clearable filterable v-model="form.unit" placeholder="请选择产品单位" style="width: 100%">
                  <el-option v-for="(item, index) in unitOptions" :key="index" :label="item" :value="item"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="产品重量" prop="weight">
                <el-input v-model="form.weight" placeholder="请输入产品重量">
                  <span slot="suffix">Kg</span>
                </el-input>
              </el-form-item>
            </el-col>
            <!--            <el-col :span="12">-->
            <!--              <el-form-item label="产品属性" prop="attribute">-->
            <!--                <el-input v-model="form.attribute" placeholder="请输入产品属性" />-->
            <!--              </el-form-item>-->
            <!--            </el-col>-->
            <el-col :span="24">
              <el-form-item label="非标类产品备注" prop="remark">
                <el-input v-model="form.remark" placeholder="请输入详细备注" type="textarea" :autosize="{ minRows: 4, maxRows: 4 }" resize="none" class="custom-textarea" />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <div class="productTip" v-if="form.isOpen">
                当前选择
                <span>公开产品</span>
                ，该产品图片、图纸等内容可以被其他用户查看并使用
              </div>
              <div class="productTip" v-else>
                当前选择
                <span>不公开</span>
                ，该产品图片、图纸等内容不可以被其他用户查看并使用
              </div>
            </el-col>
            <el-col :span="12">
              <el-form-item label="产品图片" prop="picture1">
                <image-upload v-model="form.picture1" :file-type="fileType" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="三维图片" prop="diagram">
                <image-upload v-model="form.diagram" :file-type="fileType" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="产品图纸" prop="draw">
                <file-upload v-model="form.draw" :file-type="['pdf']" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="工艺视频" prop="technology">
                <file-upload v-model="form.technology" :file-type="['mp4']" :file-size="200" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="检测报告" prop="report">
                <file-upload v-model="form.report" :file-type="fileType" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <button type="button" class="custom-dialog-btn" @click="handleCancel">取消</button>
        <button type="button" class="custom-dialog-btn primary" @click="handleSumit">确定</button>
      </div>
    </el-dialog>

    <!-- 产品详情 -->
    <product-dialog ref="productInfo" :rule-list="ruleList"></product-dialog>
  </div>
</template>

<script>
import { codingProductCode, codingRuleList } from '@/api/coding'
import { isNumber, isNumberLength } from '@/utils/validate'
import ProductDialog from '@/views/public/product/dialog'
import { getlistb, review } from '@/api/purchase/category'
import { searchlist } from '@/api/houtai/gongyu/chanpin'

export default {
  components: { ProductDialog },
  data() {
    const validateArr = (rule, value, callback) => {
      if (!this.form.mainArr.length) {
        callback(new Error('请选择编码分类'))
      } else {
        callback()
      }
    }
    return {
      title: '新增产品',
      open: false,
      form: {},
      formrules: {
        productName: [{ required: true, message: '请输入产品名称', trigger: 'blur' }],
        categoryId: [{ required: true, message: '请选择产品类目', trigger: ['blur', 'change'] }],
        mainArr: [{ type: 'array', required: true, validator: validateArr, trigger: ['blur', 'change'] }],
        specs: [{ required: true, message: '请输入产品规格', trigger: ['blur', 'change'] }],
        materialQuality: [{ required: true, message: '请选择产品材质', trigger: ['blur', 'change'] }],
        level: [{ required: true, message: '请选择产品等级', trigger: ['blur', 'change'] }],
        surface: [{ required: true, message: '请选择表面处理', trigger: ['blur', 'change'] }],
        unit: [{ required: true, message: '请选择产品单位', trigger: ['blur', 'change'] }],
        // attribute: [{ required: true, message: '请输入产品属性', trigger: ['blur', 'change'] }],
        weight: [
          { required: true, message: '请输入产品重量', trigger: ['blur', 'change'] },
          { validator: isNumber, message: '输入有误请重新输入', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 10), message: '只可以填写十位小数', trigger: 'blur' }
        ],
        picture1: [{ required: true, message: '请上传产品图片', trigger: ['blur', 'change'] }],
        draw: [{ required: true, message: '请上传产品图纸', trigger: ['blur', 'change'] }]
      },
      industryOptions: [], // 行业分类
      levelOptions: [], // 等级
      mainClassOptions: [], // 主分类
      mainClassProps: { value: 'name', label: 'name' },
      materialQualityOptions: [], // 材质
      surfaceOptions: [], // 表面
      unitOptions: ['吨', '千克', '个', '件', '套', '米', '支', '根'], // 单位
      fileType: ['png', 'PNG', 'jpg', 'JPG', 'jpeg', 'JPEG', 'pdf'],
      isOpen: true,
      hasList: [],
      ruleList: [],
      info: {},
      multipleLimit: 3
    }
  },
  created() {
    this.createRuleList()
  },
  methods: {
    createRuleList() {
      codingRuleList().then(res => {
        const { code, msg, data } = res
        if (code === 200) this.ruleList = data
        else this.$message.error(msg)
      })
    },
    getRuleList() {
      this.industryOptions = this.ruleList.industry
      this.levelOptions = this.ruleList.level
      this.mainClassOptions = this.ruleList.main
      this.materialQualityOptions = this.ruleList.material
      this.surfaceOptions = this.ruleList.surface
    },
    // 重置表单
    reset() {
      this.form = {
        id: undefined,
        draw: undefined,
        formerName: undefined,
        categoryId: undefined,
        industry: [],
        isOpen: true,
        level: undefined,
        mainArr: [],
        mainClass: undefined,
        materialQuality: [],
        specs: undefined,
        model: undefined,
        picture1: undefined,
        productCode: undefined,
        productId: undefined,
        productName: undefined,
        remark: undefined,
        report: undefined,
        specsModel: undefined,
        subClass: undefined,
        surface: undefined,
        technology: undefined,
        unit: undefined,
        standard: undefined,
        attribute: undefined,
        sort: undefined,
        weight: '0'
      }
      this.resetForm('form')
    },
    handleAdd(info) {
      this.hasList = []
      this.getRuleList()
      this.reset()
      this.form = { ...this.form, ...info }
      this.info = info
      this.title = '新增产品'
      this.open = true
    },
    // 取消
    handleCancel() {
      this.open = false
      this.reset()
    },
    // 查询预生成编码
    handleChange() {
      const { level, mainArr, materialQuality, productName, specs, surface } = this.form
      const query = {
        level,
        mainClass: mainArr[0],
        materialQuality,
        productName,
        specs,
        subClass: mainArr[1],
        surface
      }
      let num = 0
      if (level) num = num + 1
      if (mainArr.length) num = num + 1
      if (materialQuality) num = num + 1
      if (productName) num = num + 1
      if (specs) num = num + 1
      if (surface) num = num + 1
      if (num > 2) {
        searchlist(query).then(res => {
          const { code, msg, data } = res
          if (code === 200) {
            if (data.length > 10) this.hasList = data.slice(0, 10)
            else this.hasList = data
          } else this.$message.error(msg)
        })
      } else this.hasList = []
    },
    // 引用
    handleQuote(item) {
      const { mainClass, subClass, isOpen } = item
      this.form = { ...this.form, ...this.info, ...item, ...{ isOpen: this.form.isOpen } }
      this.form.mainArr = mainClass && subClass ? [mainClass, subClass] : []
      this.form.id = undefined
      let industry = []
      this.form.industry.split(',').map(item => {
        const industryItem = this.industryOptions.find(item1 => item1.name === item) || {}
        industry.push(industryItem.hasOwnProperty('name') ? industryItem.name : '')
      })
      this.form.industry = industry
      let materialQuality = []
      this.form.materialQuality.split(',').map(item => {
        const materialQualityItem = this.materialQualityOptions.find(item1 => item1.name === item) || {}
        materialQuality.push(materialQualityItem.hasOwnProperty('name') ? materialQualityItem.name : '')
      })
      this.form.materialQuality = materialQuality
      const level = this.levelOptions.find(item => item.name === this.form.level) || {}
      this.form.level = level.hasOwnProperty('name') ? level.name : ''
      const surface = this.surfaceOptions.find(item => item.name === this.form.surface) || {}
      this.form.surface = surface.hasOwnProperty('name') ? surface.name : ''
      if (!isOpen) {
        this.form.picture1 = undefined
        this.form.diagram = undefined
        this.form.draw = undefined
        this.form.technology = undefined
        this.form.report = undefined
      }
    },
    // 树结构转扁平数组
    treeToArr(data, children = 'children', parentId, res = []) {
      data.forEach(v => {
        v.parentId = parentId
        res.push(v)
        if (v[children] && v[children].length) this.treeToArr(v[children], children, v.id, res)
      })
      return res
    },
    // 提交
    handleSumit() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          const data = { ...this.form }
          if (data.mainArr.length) {
            const mainClass = this.treeToArr(this.mainClassOptions).find(item => item.name === data.mainArr[0]) || {}
            data.mainClass = mainClass.hasOwnProperty('code') ? mainClass.code : ''
            const subClass = this.treeToArr(this.mainClassOptions).find(item => item.name === data.mainArr[1]) || {}
            data.subClass = subClass.hasOwnProperty('code') ? subClass.code : ''
          }
          let materialQuality = []
          data.materialQuality.map(item => {
            const materialQualityItem = this.materialQualityOptions.find(item1 => item1.name === item) || {}
            materialQuality.push(materialQualityItem.hasOwnProperty('code') ? materialQualityItem.code : '')
          })
          data.materialQuality = materialQuality.toString()
          const level = this.levelOptions.find(item => item.name === data.level) || {}
          data.level = level.hasOwnProperty('code') ? level.code : ''
          const surface = this.surfaceOptions.find(item => item.name === data.surface) || {}
          data.surface = surface.hasOwnProperty('code') ? surface.code : ''
          data.specs = data.model
          codingProductCode(data).then(res => {
            const { code, msg } = res
            if (code === 200) {
              this.form.productCode = msg
              if (this.form.mainArr) {
                this.form.mainClass = this.form.mainArr[0]
                this.form.subClass = this.form.mainArr[1]
              }
              review(this.form).then(res => {
                if (res.code === 200) {
                  this.$message.success('提交成功，请等待审核')
                  this.open = false
                } else {
                  this.$message.error(res.msg)
                }
              })
            } else this.$message.error(msg)
          })
        }
      })
    },
    // 查看详情
    handleView(item, val) {
      this.$refs.productInfo.handleView(item, val)
    }
  }
}
</script>
<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
.custom-dialog-title {
  display: flex;
  align-items: center;
}
::v-deep .custom-dialog-switch {
  position: relative;
  margin: 0 10px;
  .el-switch__core {
    height: 24px;
    border-radius: 12px;
    min-width: 88px;
    &:after {
      left: 4px;
      top: 3px;
    }
  }
  &.el-switch {
    &.is-checked {
      .el-switch__core {
        &:after {
          margin-left: -20px;
          left: 100%;
        }
      }
    }
  }
  &.is-checked {
    .el-switch__label--left {
      opacity: 0;
    }
    .el-switch__label--right {
      opacity: 1;
    }
  }
  .el-switch__label {
    position: absolute;
    top: 0;
  }
  .el-switch__label--left {
    right: 0;
    color: #999;
    z-index: 1;
    margin-right: 8px;
  }
  .el-switch__label--right {
    left: 0;
    color: #fff;
    opacity: 0;
    margin-left: 8px;
  }
}
.hover {
  transition: 0.2s;
  cursor: pointer;
  &:hover {
    transform: scale(1) !important;
  }
}
.hasProduct {
  padding-bottom: 15px;
  &-title {
    font-size: 14px;
    color: #666666;
    line-height: 20px;
    text-align: center;
  }
  &-content {
    background: #f0f3f9;
    padding: 15px 20px 30px;
    margin-top: 18px;
    position: relative;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    &:after {
      content: '';
      position: absolute;
      left: 50%;
      transform: scaleX(-50%);
      top: -16px;
      width: 0;
      height: 0;
      border-style: solid;
      border-width: 0 16px 16px 16px;
      border-color: transparent transparent #f0f3f9 transparent;
    }
  }
  &-item {
    display: inline-flex;
    align-items: center;
    padding-left: 20px;
    padding-right: 6px;
    height: 46px;
    border-radius: 25px;
    border: 1px solid #b2bac3;
    background: #ffffff;
    margin-right: 15px;
    margin-top: 15px;
    cursor: pointer;
    span {
      margin-right: 15px;
      &:hover {
        text-decoration: underline;
      }
    }
    .hasProduct-button {
      display: none;
      transition: all 0.3s;
    }
    transition: all 0.3s;
    &:hover {
      border: 1px solid #2e73f3;
      background: #dbe8ff;
      span {
        color: #2e73f3;
      }
      .hasProduct-button {
        display: block;
      }
    }
  }
}
.productTip {
  padding: 10px;
  background-color: #e5f3ff;
  font-size: 12px;
  line-height: 1.5em;
  margin-bottom: 20px;
  border-radius: 5px;
  span {
    color: $red;
  }
}
</style>
