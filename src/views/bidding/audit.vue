<template>
  <div class="newBox bgcf9 vh-85">
    <div class="custom-search flex" style="padding-top: 18px">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" @submit.native.prevent>
        <el-form-item label="招标/采购名称" prop="title">
          <el-input v-model="queryParams.title" placeholder="请输入招标/采购名称" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="企业名称" prop="companyName">
          <el-input v-model="queryParams.companyName" placeholder="请输入企业名称" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="Box">
      <el-table v-loading="loading" ref="table" stripe :data="list" row-key="id" style="width: 100%" class="custom-table">
        <el-table-column align="center" type="index" label="序号"></el-table-column>
        <el-table-column label="招标/采购名称" align="center" prop="title" show-overflow-tooltip></el-table-column>
        <el-table-column label="企业名称" align="center" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <span>{{ (row.company && row.company.companyName) || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="采购截止日期" align="center" prop="expireTime" show-overflow-tooltip width="120">
          <template slot-scope="{ row }">{{ parseTime(row.expireTime, '{y}-{m}-{d}') }}</template>
        </el-table-column>
        <el-table-column label="供货截止日期" align="center" prop="supplyEndTime" show-overflow-tooltip width="120">
          <template slot-scope="{ row }">{{ parseTime(row.supplyEndTime, '{y}-{m}-{d}') }}</template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="createTime" show-overflow-tooltip width="120"></el-table-column>
        <el-table-column label="创建人" align="center" prop="createBy" show-overflow-tooltip width="100"></el-table-column>
        <el-table-column label="状态" align="center" prop="status" width="90">
          <template slot-scope="{ row }">
            <el-tag size="small" :type="formatStatus(row.status, 'type')">{{ formatStatus(row.status, 'label') }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="330">
          <template slot-scope="{ row }">
            <el-button class="table-btn primary" @click="handleDetail(row)">查看详情</el-button>
            <el-button class="table-btn" :class="row.status != 0 ? 'disabled' : 'danger'" :disabled="row.status != 0" @click="handleAudit(row)">审核</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="custom-pagination">
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
      </div>
    </div>
    <!--新增/修改/查看详情招标采购-->
    <create-tpl ref="createTpl" is-dialog />
    <!--审核-->
    <el-dialog v-dialogDragBox title="审核" :visible.sync="auditOpen" width="600px" class="custom-dialog">
      <div style="padding: 0 20px">
        <el-form :model="auditForm" ref="auditForm" :rules="auditRules" label-width="5.7em">
          <el-form-item label="审核状态" prop="status">
            <el-select v-model="auditForm.status" placeholder="请选择审核状态">
              <el-option v-for="item in auditOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="审核意见" prop="reason" :rules="auditForm.status == -1 ? { required: true, message: '请输入审核意见', trigger: 'blur' } : {}">
            <el-input type="textarea" :autosize="{ minRows: 4, maxRows: 6 }" resize="none" v-model="auditForm.reason" placeholder="请输入审核意见" clearable />
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn" @click="auditOpen = false">取 消</el-button>
        <el-button class="custom-dialog-btn primary" @click="handleAuditSubmit">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { auditBidding, auditListBidding, detailBidding } from '@/api/bidding'
import CreateTpl from '@/views/bidding/createTpl'

export default {
  name: 'BiddingAudit',
  components: { CreateTpl },
  data() {
    return {
      queryParams: {
        title: undefined,
        companyName: undefined,
        pageNum: 1,
        pageSize: 10
      },
      list: [],
      total: 0,
      loading: false,
      statusOptions: [
        { label: '正常', value: 0, type: '' },
        { label: '审核通过', value: 1, type: 'success' },
        { label: '审核驳回', value: -1, type: 'warning' },
        { label: '已删除', value: -10, type: 'danger' }
      ],
      // 审核
      auditOpen: false,
      auditForm: {},
      auditRules: {
        status: [{ required: true, message: '请选择审核状态', trigger: 'change' }]
      },
      auditOptions: [
        { label: '通过', value: 1 },
        { label: '驳回', value: -1 }
      ]
    }
  },
  created() {
    this.getList()
  },
  methods: {
    // 格式化状态
    formatStatus(status, type = 'label') {
      const item = this.statusOptions.find(item => item.value === status)
      return item ? item[type] : ''
    },
    // 列表
    getList() {
      this.loading = true
      auditListBidding(this.queryParams).then(res => {
        const { code, msg, rows, total } = res
        if (code === 200) {
          this.list = rows
          this.total = total
          this.loading = false
        } else this.$message.error(msg)
      })
    },
    // 刷新列表
    handleRefresh() {
      auditListBidding(this.queryParams).then(res => {
        const { code, msg, rows, total } = res
        if (code === 200) {
          this.$set(this, 'list', rows)
          this.$set(this, 'total', total)
        } else this.$message.error(msg)
      })
    },
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 重置搜索
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 详情
    handleDetail(row) {
      detailBidding({ demandId: row.id }).then(res => {
        const { code, msg, data } = res
        if (code === 200) this.$refs.createTpl.handleDialogDetail(data)
        else this.$message.error(msg)
      })
    },
    // 审核
    handleAudit(row) {
      if (row.status != 0) return
      this.auditForm = { id: row.id, status: undefined, reason: undefined }
      this.resetForm('auditForm')
      this.auditOpen = true
    },
    // 审核提交
    handleAuditSubmit() {
      this.$refs.auditForm.validate(valid => {
        if (valid) {
          auditBidding(this.auditForm).then(res => {
            const { code, msg } = res
            if (code === 200) {
              this.$message.success('审核成功')
              this.auditOpen = false
              this.handleRefresh()
            } else this.$message.error(msg)
          })
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';
.Box {
  padding: 15px 20px;
}
.custom-table {
  .table-btn {
    padding: 0;
  }
}
::v-deep {
  .el-textarea__inner {
    font-family: inherit;
  }
}
</style>
