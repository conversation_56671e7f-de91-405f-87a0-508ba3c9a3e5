<template>
  <div>
    <!-- 添加招标/采购产品 -->
    <el-dialog v-dialogDragBox title="添加招标/采购产品" :visible.sync="open" width="1150px" class="custom-dialog" :fullscreen="isFullscreen">
      <dialog-header slot="title" dialog-tittle="添加招标/采购产品" :fullscreen="isFullscreen" @is-fullscreen="onFullscreen"></dialog-header>
      <div class="addBox">
        <div class="add-search flex" style="margin-bottom: 0">
          <el-form ref="queryForm" :model="query" inline size="small" @submit.native.prevent>
            <el-form-item label="名称" prop="productName">
              <el-input v-model="query.productName" placeholder="请输入产品名称" clearable @keyup.enter.native="getList" style="width: auto" />
            </el-form-item>
            <el-form-item label="编码" prop="productCode">
              <el-input v-model="query.productCode" placeholder="请输入产品编码" clearable @keyup.enter.native="getList" />
            </el-form-item>
            <el-form-item label="规格" prop="specs">
              <el-input v-model="query.specs" placeholder="请输入产品规格" clearable @keyup.enter.native="getList" />
            </el-form-item>
            <el-form-item label="型号" prop="model">
              <el-input v-model="query.model" placeholder="请输入产品型号" clearable @keyup.enter.native="getList" />
            </el-form-item>
            <el-form-item label="材质" prop="materialQuality">
              <el-input v-model="query.materialQuality" placeholder="请输入产品材质" clearable @keyup.enter.native="getList" />
            </el-form-item>
            <el-form-item label="表面" prop="surface">
              <el-input v-model="query.surface" placeholder="请输入表面处理" clearable @keyup.enter.native="getList" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" size="small" @click="getList">搜索</el-button>
              <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
              <el-button type="danger" icon="el-icon-plus" size="small" @click="handleAddProduct" v-hasPermi="['system:privateduct:adds']">新增产品</el-button>
            </el-form-item>
          </el-form>
        </div>
        <!--<div class="table-tip" v-if="isNoQuery">-->
        <!--<i class="el-icon-info"></i>-->
        <!--<span>提示：当前显示为常用采购产品，如需采购其他产品，请输入名称关键词、编码关键词等进行搜索</span>-->
        <!--</div>-->
        <el-table v-loading="loading" ref="addTable" stripe :data="list" :row-key="getRowKey" style="width: 100%" :max-height="height" class="custom-table custom-table-cell5" @select="handleSelectProduct" @select-all="handleSelectProductAll">
          <el-table-column align="center" type="selection" width="50" :reserve-selection="true"></el-table-column>
          <el-table-column align="center" type="index" label="序号"></el-table-column>
          <el-table-column align="center" prop="product_name" label="产品名称" show-overflow-tooltip>
            <template slot-scope="{ row }">
              <span class="table-link" @click="handleView(row.id, row)" v-if="row.id">
                <!--<span v-if="row.source === 'common'">(公域)</span>-->
                <!--<span style="color: #fe7f22" v-else>(私域)</span>-->
                {{ row.productName }}
              </span>
              <span class="table-link" v-else>
                <span style="color: #fe7f22">(私域)</span>
                {{ row.productName }}
              </span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="picture1" label="图片" width="75">
            <template slot-scope="{ row }">
              <el-button type="text" size="mini" icon="el-icon-view" @click="handleImgView(row)">查看</el-button>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="specs" label="规格" show-overflow-tooltip :filters="specsFilters" :filter-method="filterSpecs"></el-table-column>
          <el-table-column align="center" prop="model" label="型号" show-overflow-tooltip :filters="modelFilters" :filter-method="filterModel"></el-table-column>
          <el-table-column align="center" prop="productCode" label="产品编码" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="materialQuality" label="材质" show-overflow-tooltip :filters="materialFilters" :filter-method="filterQuality"></el-table-column>
          <el-table-column align="center" prop="unit" label="单位" width="50"></el-table-column>
          <el-table-column align="center" label="操作">
            <template slot-scope="{ row }">
              <el-button type="text" size="mini" icon="el-icon-circle-check" @click="handleChooseProduct(row)">采购</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="custom-pagination">
          <pagination v-show="total > 0" :total="total" :page.sync="query.pageNum" :limit.sync="query.pageSize" @pagination="getList" />
        </div>
      </div>
      <div slot="footer">
        <div class="inline-flex">
          <el-button type="button" class="custom-dialog-btn" @click="open = false">取消</el-button>
          <el-badge :value="selected.length" :hidden="!selected.length">
            <el-button type="button" class="custom-dialog-btn primary" @click="handleSubmit" :disabled="!selected.length">确定</el-button>
          </el-badge>
        </div>
      </div>
    </el-dialog>
    <!-- 产品详情 -->
    <product-dialog ref="productInfo"></product-dialog>
    <!-- 编码产品 -->
    <product-code ref="productCode" @callparams="handleAddAddform"></product-code>
  </div>
</template>
<script>
import { purchasingProduct } from '@/api/purchase'
import { getProduct } from '@/api/system/product'
import { getPrivateduct, listPrivateduct } from '@/api/system/privateduct'
import ProductDialog from '@/views/public/product/dialog'
import productCode from '@/views/components/private/index'

export default {
  components: { productCode, ProductDialog },
  data() {
    return {
      isFullscreen: false,
      open: false,
      query: {
        pageNum: 1,
        pageSize: 10,
        productName: undefined,
        productCode: undefined,
        specs: undefined,
        model: undefined,
        materialQuality: undefined,
        surface: undefined
      },
      isNoQuery: true,
      loading: true,
      list: [],
      total: 0,
      windowHeight: undefined,
      height: undefined,
      specsFilters: [],
      modelFilters: [],
      materialFilters: [],
      selected: []
    }
  },
  watch: {
    windowHeight(val) {
      this.height = val * 0.94 - 305
    }
  },
  mounted() {
    this.windowHeight = document.documentElement.clientHeight
    window.onresize = () => {
      this.windowHeight = document.documentElement.clientHeight
    }
  },
  methods: {
    // 打开产品列表
    openProduct(arr = []) {
      this.selected = [...arr]
      this.resetQueryForm()
      this.open = true
      this.isFullscreen = false
      this.getList()
    },
    // 全屏显示
    onFullscreen(val) {
      this.isFullscreen = val
      const winHeight = document.documentElement.clientHeight
      if (val) {
        this.addHeight = winHeight - 252
        this.addFormHeight = winHeight - 314
        this.confirmFormHeight = winHeight - 432
      } else {
        this.addHeight = winHeight * 0.94 - 305
        this.addFormHeight = winHeight * 0.94 - 367
        this.confirmFormHeight = winHeight * 0.94 - 485
      }
    },
    // 重置
    resetQuery() {
      this.resetQueryForm()
      this.getList()
    },
    // 重置搜索条件
    resetQueryForm() {
      this.query = {
        ageNum: 1,
        pageSize: 10,
        productName: undefined,
        productCode: undefined,
        specs: undefined,
        model: undefined,
        materialQuality: undefined,
        surface: undefined
      }
      this.resetForm('queryForm')
      this.isNoQuery = true
    },
    // 查询列表
    getList() {
      this.loading = true
      listPrivateduct(this.query).then(res => {
        const { code, msg, rows, total } = res
        if (code === 200) {
          const specs = this.uniqueJsonArrByField(rows, 'specs')
          const model = this.uniqueJsonArrByField(rows, 'model')
          const material = this.uniqueJsonArrByField(rows, 'materialQuality')
          this.specsFilters = specs.map(item => {
            return { text: item.specs, value: item.specs }
          })
          this.modelFilters = model.map(item => {
            return { text: item.model, value: item.model }
          })
          this.materialFilters = material.map(item => {
            return { text: item.materialQuality, value: item.materialQuality }
          })
          this.list = rows
          this.total = total
          this.loading = false
          // this.isNoQuery = this.areAllValuesEmpty(this.query)
          this.echo()
        } else this.$message.error(msg)
      })
    },
    // 数组去重
    uniqueJsonArrByField(jsonArr, field) {
      // 数组长度小于2 或 没有指定去重字段 或 不是json格式数据
      if (jsonArr.length < 2 || !field || typeof jsonArr[0] !== 'object') return jsonArr
      return jsonArr.reduce((all, next) => (all.some(item => item[field] === next[field]) ? all : [...all, next]), [])
    },
    // 判断json的字段值是否都为空
    areAllValuesEmpty(jsonObj) {
      for (let key in jsonObj) {
        if (jsonObj.hasOwnProperty(key) && jsonObj[key]) return false
      }
      return true
    },
    // 新增产品
    handleAddProduct() {
      this.$refs.productCode.handleAdd()
    },
    // 新增产品添加到已选中列表
    handleAddAddform(data) {
      this.selected.push(data)
      this.list = [...[data], ...this.list]
      this.echo()
    },
    // 获取Key
    getRowKey(row) {
      return row.id
    },
    // 选择产品
    handleSelectProduct(selection, row) {
      const idx = this.selected.findIndex(item => item.id === row.id)
      if (idx === -1) {
        this.selected.push(row)
      } else {
        this.selected.splice(idx, 1)
      }
    },
    // 点击采购选择产品
    handleChooseProduct(row) {
      const index = this.selected.findIndex(item => item.id === row.id)
      if (index !== -1) return
      this.selected.push(row)
      this.echo()
    },
    // 全选
    handleSelectProductAll(selection) {
      if (selection.length) {
        if (this.selected.length) {
          selection.map(ite => {
            const idx = this.selected.findIndex(item => item.id === ite.id)
            if (idx === -1) this.selected.push(ite)
          })
        } else this.selected = [...selection]
      } else {
        this.list.map(ite => {
          const idx = this.selected.findIndex(item => item.id === ite.id)
          if (idx !== -1) this.selected.splice(idx, 1)
        })
      }
    },
    // 回显选中
    echo() {
      this.$nextTick(() => {
        if (this.$refs.addTable) this.$refs.addTable.clearSelection()
        this.list.map(item => {
          this.selected.map(itt => {
            if (itt.id === item.id) this.$refs.addTable.toggleRowSelection(item, true)
          })
        })
      })
    },
    // 产品详情
    handleView(Id, row) {
      if (row.source === 'common') {
        getProduct(Id).then(res => {
          this.$refs.productInfo.handleView(res.data)
        })
      } else {
        getPrivateduct(Id).then(res => {
          this.$refs.productInfo.handleView(res.data)
        })
      }
    },
    // 图片详情
    handleImgView(row) {
      this.$refs.productInfo.handleImgView(row)
    },
    // 规格筛选
    filterSpecs(value, row) {
      return row.specs === value
    },
    // 型号筛选
    filterModel(value, row) {
      return row.model === value
    },
    // 材质筛选
    filterQuality(value, row) {
      return row.materialQuality === value
    },
    // 确认选择
    handleSubmit() {
      this.$emit('confirm', this.selected)
      this.open = false
    }
  }
}
</script>
<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';
.table-tip {
  display: flex;
  align-items: center;
  padding-bottom: 10px;
  font-size: 14px;
  color: red;
  span {
    margin-left: 5px;
  }
}
.addBox {
  padding: 0 20px;
}
</style>
