<template>
  <div>
    <el-dialog v-dialogDragBox :title="title" :visible.sync="open" width="1150px" class="custom-dialog">
      <div style="padding: 0 20px">
        <el-table :data="list" style="width: 100%" class="custom-table">
          <el-table-column align="center" type="index" label="序号" width="60"></el-table-column>
          <el-table-column align="center" label="报价企业">
            <template slot-scope="{ row }">{{ row.supplier && row.supplier.name }}</template>
          </el-table-column>
          <el-table-column align="center" prop="replyTime" label="报价日期" width="180"></el-table-column>
          <el-table-column label="" align="center" width="120">
            <template slot-scope="{ row }">
              <el-button class="table-btn primary small" @click="handleView(row)">查看报价详细</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn primary" @click="open = false">关 闭</el-button>
      </div>
    </el-dialog>

    <!--查看报价明细-->
    <el-dialog v-dialogDragBox title="报价明细" :visible.sync="viewOpen" width="1150px" class="custom-dialog">
      <div style="padding: 0 20px">
        <el-form ref="info" :model="info" :rules="rules" label-width="0">
          <div style="padding: 5px 0">
            <span style="font-size: 12px; color: #999999">报价企业：</span>
            <b style="font-weight: normal; font-size: 14px; color: #333333">{{ info.supplier && info.supplier.name }}</b>
          </div>
          <el-table :data="info.viewList" style="width: 100%" class="custom-table custom-table-cell0" @select="handleSelect" @select-all="handleSelectAll">
            <el-table-column align="center" type="selection" width="50"></el-table-column>
            <el-table-column align="center" type="index" label="序号" width="60"></el-table-column>
            <el-table-column align="center" prop="productName" label="产品名称" show-overflow-tooltip>
              <template slot-scope="{ row }">
                <span class="table-link" @click="hanleView(row)">{{ row.productName }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="specs" label="规格" show-overflow-tooltip></el-table-column>
            <el-table-column align="center" prop="model" label="型号" show-overflow-tooltip></el-table-column>
            <el-table-column align="center" prop="materialQuality" label="材质" show-overflow-tooltip></el-table-column>
            <el-table-column align="center" prop="surface" label="表面处理" show-overflow-tooltip></el-table-column>
            <el-table-column align="center" prop="unit" label="单位"></el-table-column>
            <el-table-column align="center" prop="quantity" label="采购数量" min-width="120">
              <template slot-scope="scope">
                <el-form-item label-width="0" :prop="`viewList.${scope.$index}.quantity`" :rules="rules.quantity">
                  <el-input v-model="scope.row.quantity" size="small" placeholder="采购数量" />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="remark" label="产品备注" show-overflow-tooltip></el-table-column>
            <el-table-column align="center" label="报价">
              <template slot-scope="{ row }">
                <span v-if="row.reply">{{ row.reply.amount + '元/' + row.reply.replyUnit }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" label="报价备注" show-overflow-tooltip>
              <template slot-scope="{ row }">
                <span v-if="row.reply">{{ row.reply.remark }}</span>
              </template>
            </el-table-column>
          </el-table>
        </el-form>
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn" @click="viewOpen = false">取 消</el-button>
        <el-badge :value="selection.length" :hidden="!selection.length">
          <el-button class="custom-dialog-btn primary" :class="{ disabled: !selection.length }" :disabled="!selection.length" @click="handleCreateContract">生成合同</el-button>
        </el-badge>
      </div>
    </el-dialog>

    <!--  生成合同  -->
    <offer-contract-dialog ref="contractDialog"></offer-contract-dialog>

    <!-- 产品详情 -->
    <product-dialog ref="productInfo"></product-dialog>
  </div>
</template>
<script>
import { detailBidding } from '@/api/bidding'
import { isNumber } from '@/utils/validate'
import offerContractDialog from '@/views/purchase/demandForMe/offer'
import FooterTpl from '@/views/public/components/foot.vue'
import ProductDialog from '@/views/public/product/dialog'

export default {
  components: { ProductDialog, FooterTpl, offerContractDialog },
  data() {
    return {
      open: false,
      title: '查看报价',
      list: [],
      replyList: [],
      products: [],
      // 报价明细
      viewOpen: false,
      info: {},
      rules: {
        quantity: [
          { required: true, message: '请输入采购数量', trigger: 'blur' },
          { validator: isNumber, message: '输入有误请重新输入', trigger: 'blur' }
        ]
      },
      selection: [] // 选中的数据
    }
  },
  methods: {
    handleOpen(id, reply = []) {
      detailBidding({ demandId: id }).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          this.products = data.details.map(item => {
            return { ...item.product, ...item }
          })
          this.list = this.unique(reply)
          this.replyList = reply
          this.open = true
        } else this.$message.error(msg)
      })
    },
    // 根据数组companyId去重
    unique(arr) {
      const res = new Map()
      return arr.filter(a => !res.has(a.companyId) && res.set(a.companyId, 1))
    },
    // 查看报价详细
    handleView(row) {
      this.info = row
      const reply = this.replyList.filter(item => item.companyId === row.companyId)
      this.info.viewList = this.products.map(item => {
        const product = reply.find(r => r.detailId === item.id)
        return { ...item, reply: product }
      })
      this.viewOpen = true
      this.open = false
    },
    // 选择
    handleSelect(selection, row) {
      this.selection = selection
    },
    // 全选
    handleSelectAll(selection) {
      this.selection = selection
    },
    // 生成合同
    handleCreateContract() {
      if (!this.selection.length) return
      const sellerUser = { companyId: this.info.supplier.id }
      const list = this.selection.map(item => {
        return {
          maxNum: item.reply.maxNum,
          source: item.source,
          productId: item.productId,
          productName: item.productName,
          productCode: item.productCode || '',
          specs: item.specs,
          model: item.model,
          unit: item.unit,
          quantity: item.quantity,
          needQuantity: item.quantity,
          listId: -1,
          remark: item.remark,
          amount: item.reply.amount,
          originAmount: item.reply.amount,
          replyUnit: item.reply.replyUnit,
          sjNum: item.quantity,
          replyRemark: item.reply.remark,
          endUnit: item.reply.replyUnit
        }
      })
      this.$refs.contractDialog.handleGetContract(sellerUser, list, 'yes', 'bidding')
      this.viewOpen = false
    },
    // 产品详情
    hanleView(row) {
      this.$refs.productInfo.handleView(row)
    }
  }
}
</script>
<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';
::v-deep {
  .custom-table {
    .el-form-item {
      margin-top: 10px !important;
      margin-bottom: 10px !important;
      .el-form-item__error {
        top: 95%;
        padding-top: 0;
      }
      .el-input__inner {
        text-align: left;
      }
    }
  }
}
</style>
