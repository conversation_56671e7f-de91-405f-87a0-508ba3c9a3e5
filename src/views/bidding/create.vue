<template>
  <div class="container">
    <header-tpl :is-login="isLogin" />
    <div class="container-box">
      <div class="containerBox">
        <create-tpl ref="createTpl" />
      </div>
    </div>
    <footer-tpl />
  </div>
</template>
<script>
import HeaderTpl from '@/views/public/components/head'
import FooterTpl from '@/views/public/components/foot'
import { getToken } from '@/utils/auth'
import CreateTpl from './createTpl'

export default {
  components: { CreateTpl, FooterTpl, HeaderTpl },
  data() {
    return {
      isLogin: !!getToken()
    }
  }
}
</script>
<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';
.container-box {
  min-width: 1200px;
}
.containerBox {
  width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  padding-top: 30px;
  padding-bottom: 30px;
}
</style>
