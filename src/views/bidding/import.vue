<template>
  <div>
    <el-dialog v-dialogDragBox title="批量添加招标/采购产品" :visible.sync="open" width="1150px" class="custom-dialog">
      <div style="padding: 0 20px">
        <el-upload ref="upload" accept=".xlsx, .xls" :headers="upload.headers" :action="upload.url" :disabled="upload.isUploading" :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess" drag class="importBox" v-if="!list.length">
          <div class="import">
            <div class="importIcon"><i class="el-icon-plus"></i></div>
            <div class="importTitle">批量添加采购产品/将文件拖拽到此处</div>
            <div class="importTip">请上传符合平台要求格式的产品列表文件</div>
          </div>
        </el-upload>
        <div class="importResult" v-if="list.length">导入结果如下：</div>
        <el-table :data="list" style="width: 100%" class="custom-table" v-if="list.length">
          <el-table-column type="index" label="序号" width="50"></el-table-column>
          <el-table-column prop="productName" label="产品名称" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column prop="specs" label="规格" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column prop="model" label="型号" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column prop="materialQuality" label="材质" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column prop="surface" label="表面处理" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column prop="unit" label="单位" align="center"></el-table-column>
          <el-table-column prop="weight" label="重量" align="center"></el-table-column>
          <el-table-column label="操作" align="center">
            <template slot-scope="{ row }">
              <el-button type="text" size="mini" icon="el-icon-delete" @click="handleDelete(row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn" @click="open = false">取 消</el-button>
        <el-button class="custom-dialog-btn primary plain" @click="handleDownload" icon="el-icon-download" v-if="showDownload">下载文件范本</el-button>
        <el-button class="custom-dialog-btn primary" @click="handleSubmit" v-if="list.length">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { getToken } from '@/utils/auth'

export default {
  data() {
    return {
      open: false,
      upload: {
        isUploading: false,
        headers: { Authorization: 'Bearer ' + getToken() },
        url: process.env.VUE_APP_BASE_API + '/bidding/demand/importData'
      },
      list: [],
      showDownload: true
    }
  },
  methods: {
    // 打开批量导入
    handleOpen() {
      this.list = []
      this.showDownload = true
      this.upload.isUploading = false
      this.open = true
      this.$nextTick(() => {
        this.$refs.upload.clearFiles()
      })
    },
    // 下载模板文件
    handleDownload() {
      this.download('/bidding/demand/importTemplate', {}, `批量添加招标/采购产品_${new Date().getTime()}.xlsx`)
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.isUploading = false
      this.$refs.upload.clearFiles()
      if (response.code === 200) {
        this.list = response.data.map(item => {
          return { ...item, source: 'bidding' }
        })
        this.showDownload = false
      } else {
        this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + '</div>', '导入结果', { dangerouslyUseHTMLString: true })
      }
    },
    // 删除
    handleDelete(row) {
      this.list.splice(this.list.indexOf(row), 1)
    },
    // 提交
    handleSubmit() {
      this.$emit('confirm', this.list)
      this.open = false
    }
  }
}
</script>
<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';
::v-deep {
  .el-upload {
    width: 100%;
    .el-upload-dragger {
      width: 100%;
      height: auto;
      border: 0;
    }
  }
}
.importBox {
  display: flex;
  flex-direction: column;
  margin: 0 60px;
}
.import {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 160px;
  border: 1px dashed $blue;
  border-radius: 5px;
  background-color: #f0f5ff;
  cursor: pointer;
  &Icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    background-color: $white;
    border-radius: 5px;
    color: $blue;
    font-size: 18px;
  }
  &Title {
    margin: 5px 0;
    font-size: 14px;
    color: $disabled;
    line-height: 20px;
  }
  &Tip {
    font-size: 12px;
    color: #f35d09;
    line-height: 18px;
  }
}
.importResult {
  font-size: 14px;
  font-weight: 500;
  color: $font;
  line-height: 20px;
  margin-bottom: 15px;
}
</style>
