<template>
  <div>
    <div class="biddingCreate" v-if="!isDialog">
      <div class="biddingCreateTitle">招标/采购需求</div>
      <div class="biddingCreateForm">
        <el-form ref="form" :model="form" :rules="rules" label-width="100px">
          <el-row :gutter="40">
            <el-col :span="24">
              <el-form-item label="招标/采购名称" prop="title">
                <el-input v-model="form.title" placeholder="请输入招标/采购名称"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="备注" prop="remark">
                <el-input v-model="form.remark" placeholder="请输入备注"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="供货截止日期" prop="supplyEndTime">
                <el-date-picker v-model="form.supplyEndTime" value-format="yyyy-MM-dd 23:59:59" type="date" placeholder="选择供货截止日期" style="width: 100%"></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="采购截止日期" prop="expireTime">
                <el-date-picker v-model="form.expireTime" value-format="yyyy-MM-dd 23:59:59" type="date" placeholder="选择采购截止日期" style="width: 100%"></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="" label-width="0" prop="products">
                <el-table :data="form.products" style="width: 100%; margin-bottom: 20px" class="custom-table custom-table-cell0" v-if="form.products.length">
                  <el-table-column type="index" label="序号" width="50"></el-table-column>
                  <el-table-column prop="productName" label="产品名称" align="center" show-overflow-tooltip>
                    <template slot-scope="{ row }">
                      <span class="table-link" @click="handleView(row)">{{ row.productName }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="specs" label="规格" align="center" show-overflow-tooltip></el-table-column>
                  <el-table-column prop="model" label="型号" align="center" show-overflow-tooltip></el-table-column>
                  <el-table-column prop="materialQuality" label="材质" align="center" show-overflow-tooltip></el-table-column>
                  <el-table-column prop="surface" label="表面处理" align="center" show-overflow-tooltip></el-table-column>
                  <el-table-column prop="unit" label="单位" align="center"></el-table-column>
                  <el-table-column prop="weight" label="重量" align="center"></el-table-column>
                  <el-table-column prop="quantity" label="采购数量" align="center" width="145">
                    <template slot-scope="scope">
                      <el-form-item label-width="0" :prop="`products.${scope.$index}.quantity`" :rules="rules.quantity">
                        <el-input v-model="scope.row.quantity" size="small" placeholder="请输入采购数量" />
                      </el-form-item>
                    </template>
                  </el-table-column>
                  <el-table-column prop="remark" label="备注" align="center" width="145">
                    <template slot-scope="scope">
                      <el-form-item label-width="0" :prop="`products.${scope.$index}.remark`" :rules="rules.remark">
                        <el-input v-model="scope.row.remark" size="small" placeholder="请输入备注" />
                      </el-form-item>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" align="center" width="120">
                    <template slot-scope="scope">
                      <el-button type="text" size="mini" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>
                      <el-button type="text" size="mini" icon="el-icon-edit" @click="handleUpdate(scope.row, scope.$index)" v-if="scope.row.source === 'bidding'">修改</el-button>
                    </template>
                  </el-table-column>
                </el-table>
                <div class="biddingCreateFormAdd" @click="handleAdd">
                  <i class="el-icon-plus"></i>
                  <span>添加产品</span>
                </div>
                <div class="biddingCreateFormAdd" style="margin-top: 20px" @click="handleImport">
                  <i class="el-icon-plus"></i>
                  <span>批量导入产品</span>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div class="biddingCreateFormSubmit" :class="{ disabled: !form.products.length }" @click="handleSubmit">提交招标/采购需求</div>
      </div>
    </div>
    <template v-if="isDialog">
      <el-dialog v-dialogDragBox :title="title" :visible.sync="open" width="1150px" class="custom-dialog">
        <div class="biddingCreateForm" style="padding: 0 20px">
          <template v-if="isDetail">
            <div class="detailAudit" v-if="form.status == -1">
              <div class="detailAuditItem">审核状态：审核驳回</div>
              <div class="detailAuditItem">审核意见：{{ form.reason }}</div>
            </div>
            <div class="detailInfo">
              <el-row :gutter="10">
                <el-col :span="24">
                  <span class="title">标题</span>
                  <b class="content">{{ form.title }}</b>
                </el-col>
                <el-col :span="12">
                  <span class="title">采购截止日期</span>
                  <b class="content">{{ parseTime(form.expireTime, '{y}-{m}-{d}') }}</b>
                </el-col>
                <el-col :span="12">
                  <span class="title">采购截止日期</span>
                  <b class="content">{{ parseTime(form.supplyEndTime, '{y}-{m}-{d}') }}</b>
                </el-col>
                <el-col :span="24">
                  <span class="title">备注</span>
                  <b class="content">{{ form.remark }}</b>
                </el-col>
              </el-row>
            </div>
            <div class="detailTip">产品列表</div>
            <el-table :data="form.products" style="width: 100%; margin-bottom: 20px" class="custom-table" v-if="form.products && form.products.length">
              <el-table-column type="index" label="序号" width="50"></el-table-column>
              <el-table-column prop="productName" label="产品名称" align="center" show-overflow-tooltip>
                <template slot-scope="{ row }">
                  <span class="table-link" @click="handleView(row)">{{ row.productName }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="specs" label="规格" align="center" show-overflow-tooltip></el-table-column>
              <el-table-column prop="model" label="型号" align="center" show-overflow-tooltip></el-table-column>
              <el-table-column prop="materialQuality" label="材质" align="center" show-overflow-tooltip></el-table-column>
              <el-table-column prop="surface" label="表面处理" align="center" show-overflow-tooltip></el-table-column>
              <el-table-column prop="unit" label="单位" align="center"></el-table-column>
              <el-table-column prop="weight" label="重量" align="center"></el-table-column>
              <el-table-column prop="quantity" label="采购数量" align="center" show-overflow-tooltip></el-table-column>
              <el-table-column prop="remark" label="备注" align="center" show-overflow-tooltip></el-table-column>
            </el-table>
          </template>
          <el-form ref="form" :model="form" :rules="rules" label-width="100px" v-if="!isDetail">
            <el-row :gutter="40">
              <el-col :span="24">
                <el-form-item label="招标/采购名称" prop="title">
                  <el-input v-model="form.title" placeholder="请输入招标/采购名称"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="备注" prop="remark">
                  <el-input v-model="form.remark" placeholder="请输入备注"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="供货截止日期" prop="supplyEndTime">
                  <el-date-picker v-model="form.supplyEndTime" value-format="yyyy-MM-dd 23:59:59" type="date" placeholder="选择供货截止日期" style="width: 100%"></el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="采购截止日期" prop="expireTime">
                  <el-date-picker v-model="form.expireTime" value-format="yyyy-MM-dd 23:59:59" type="date" placeholder="选择采购截止日期" style="width: 100%"></el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="" label-width="0" prop="products">
                  <el-table :data="form.products" style="width: 100%; margin-bottom: 20px" class="custom-table custom-table-cell0" v-if="form.products && form.products.length">
                    <el-table-column type="index" label="序号" width="50"></el-table-column>
                    <el-table-column prop="productName" label="产品名称" align="center" show-overflow-tooltip>
                      <template slot-scope="{ row }">
                        <span class="table-link" @click="handleView(row)">{{ row.productName }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column prop="specs" label="规格" align="center" show-overflow-tooltip></el-table-column>
                    <el-table-column prop="model" label="型号" align="center" show-overflow-tooltip></el-table-column>
                    <el-table-column prop="materialQuality" label="材质" align="center" show-overflow-tooltip></el-table-column>
                    <el-table-column prop="surface" label="表面处理" align="center" show-overflow-tooltip></el-table-column>
                    <el-table-column prop="unit" label="单位" align="center"></el-table-column>
                    <el-table-column prop="weight" label="重量" align="center"></el-table-column>
                    <el-table-column prop="quantity" label="采购数量" align="center" width="145">
                      <template slot-scope="scope">
                        <el-form-item label-width="0" :prop="`products.${scope.$index}.quantity`" :rules="rules.quantity" v-if="!!form.products.length">
                          <el-input v-model="scope.row.quantity" size="small" placeholder="请输入采购数量" />
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <el-table-column prop="remark" label="备注" align="center" width="145">
                      <template slot-scope="scope">
                        <el-form-item label-width="0" :prop="`products.${scope.$index}.remark`" :rules="rules.remark" v-if="!!form.products.length">
                          <el-input v-model="scope.row.remark" size="small" placeholder="请输入备注" />
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <el-table-column label="操作" align="center">
                      <template slot-scope="{ row }">
                        <el-button type="text" size="mini" icon="el-icon-delete" @click="handleDelete(row)">删除</el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                  <div class="biddingCreateFormAdd" @click="handleAdd">
                    <i class="el-icon-plus"></i>
                    <span>添加产品</span>
                  </div>
                  <div class="biddingCreateFormAdd" style="margin-top: 20px" @click="handleImport">
                    <i class="el-icon-plus"></i>
                    <span>批量导入产品</span>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
        <div slot="footer">
          <template v-if="isDetail">
            <el-button class="custom-dialog-btn primary" @click="open = false">关 闭</el-button>
          </template>
          <template v-if="!isDetail">
            <el-button class="custom-dialog-btn" @click="open = false">取 消</el-button>
            <el-button class="custom-dialog-btn primary" :class="{ disabled: form.products && !form.products.length }" :disabled="form.products && !form.products.length" @click="handleDialogSubmit">确 定</el-button>
          </template>
        </div>
      </el-dialog>
    </template>
    <!--添加产品--选择产品-->
    <check-product ref="checkProduct" @confirm="handleConfirm" />
    <!--批量导入产品-->
    <import-product ref="importProduct" @confirm="handleConfirmImport" />
    <!-- 产品详情 -->
    <product-dialog ref="productInfo"></product-dialog>
    <!--修改导入的产品-->
    <el-dialog v-dialogDragBox title="修改产品" :visible.sync="updateOpen" width="1150px" class="custom-dialog">
      <div style="padding: 0 20px">
        <el-form ref="updateForm" :model="updateForm" :rules="updateRules" label-width="80px">
          <el-row :gutter="10" style="display: flex; flex-wrap: wrap">
            <el-col :span="12">
              <el-form-item label="产品名称" prop="productName">
                <el-input v-model="updateForm.productName" placeholder="请输入产品名称" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="产品规格" prop="specs">
                <el-input v-model="updateForm.specs" placeholder="请输入产品规格" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="产品型号" prop="model">
                <el-input v-model="updateForm.model" placeholder="请输入产品型号" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="产品材质" prop="materialQuality">
                <el-input v-model="updateForm.materialQuality" placeholder="请输入产品材质" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="表面处理" prop="surface">
                <el-input v-model="updateForm.surface" placeholder="请输入表面处理" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="产品单位" prop="unit">
                <el-select v-model="updateForm.unit" filterable allow-create placeholder="请选择产品单位" style="width: 100%">
                  <el-option v-for="item in unitOptions" :key="item" :label="item" :value="item"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="产品重量" prop="weight">
                <el-input v-model="updateForm.weight" placeholder="请输入产品重量" />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="产品图片" prop="picture">
                <image-upload v-model="updateForm.picture" :file-type="fileType" />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="产品图纸" prop="draw">
                <file-upload v-model="updateForm.draw" :file-type="['pdf']" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn" @click="updateOpen = false">取 消</el-button>
        <el-button class="custom-dialog-btn primary" @click="handleUpdateSubmit">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import CheckProduct from './product'
import ImportProduct from './import'
import { isNumber, isNumberLength } from '@/utils/validate'
import ProductDialog from '@/views/public/product/dialog'
import { getProduct } from '@/api/system/product'
import { getPrivateduct } from '@/api/system/privateduct'
import { addBidding, detailBiddingProduct, updateBidding, updateBiddingProduct } from '@/api/bidding'
import { parseTime } from '@/utils/ruoyi'

export default {
  props: {
    isDialog: {
      type: Boolean,
      default: false
    }
  },
  components: { ProductDialog, ImportProduct, CheckProduct },
  data() {
    return {
      form: {
        title: undefined,
        remark: undefined,
        supplyEndTime: undefined,
        expireTime: undefined,
        products: []
      },
      rules: {
        title: [{ required: true, message: '请输入招标/采购名称', trigger: 'blur' }],
        supplyEndTime: [{ required: true, message: '请选择供货截止日期', trigger: 'change' }],
        expireTime: [{ required: true, message: '请选择采购截止日期', trigger: 'change' }],
        products: [{ required: true, message: '请添加产品', trigger: 'change' }],
        quantity: [
          { required: true, message: '请输入采购数量', trigger: 'blur' },
          { validator: isNumber, message: '请输入正确的数量', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 3), message: '只可以填写三位小数', trigger: 'blur' }
        ]
      },
      title: '新增招标/采购需求',
      open: false,
      isDetail: false,
      // 修改导入的产品
      updateOpen: false,
      updateIndex: undefined,
      updateForm: {},
      updateRules: {
        productName: [{ required: true, message: '请输入产品名称', trigger: 'blur' }],
        specs: [{ required: true, message: '请输入规格', trigger: ['blur', 'change'] }],
        model: [{ required: true, message: '请输入型号', trigger: ['blur', 'change'] }],
        materialQuality: [{ required: true, message: '请输入材质', trigger: ['blur', 'change'] }],
        surface: [{ required: true, message: '请输入表面处理', trigger: ['blur', 'change'] }],
        unit: [{ required: true, message: '请选择单位', trigger: 'change' }],
        weight: [
          { required: true, message: '请输入产品重量', trigger: ['blur', 'change'] },
          { validator: isNumber, message: '输入有误请重新输入', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 10), message: '只可以填写十位小数', trigger: 'blur' }
        ]
      },
      fileType: ['png', 'PNG', 'jpg', 'JPG', 'jpeg', 'JPEG', 'pdf'],
      unitOptions: ['吨', '千克', '个', '件', '套', '米', '支', '根', '袋', '托']
    }
  },
  created() {
    if (!this.isDialog) this.init()
  },
  methods: {
    // 初始化
    init() {
      this.form = {
        title: undefined,
        remark: undefined,
        supplyEndTime: undefined,
        expireTime: undefined,
        products: []
      }
      this.resetForm('form')
    },
    // 添加产品
    handleAdd() {
      const arr = this.form.products.filter(item => item.source !== 'bidding') || []
      this.$refs.checkProduct.openProduct(arr)
    },
    // 确认选择产品
    handleConfirm(arr = []) {
      const newArr = arr.map(item => {
        return { ...item, productId: item.id, quantity: undefined, source: 'private' }
      })
      const oldArr = this.form.products.filter(item => item.source === 'bidding') || []
      this.form.products = [...oldArr, ...newArr]
    },
    // 批量导入产品
    handleImport() {
      this.$refs.importProduct.handleOpen()
    },
    // 确认导入产品
    handleConfirmImport(arr = []) {
      const resArr = this.unique([...this.form.products, ...arr])
      this.form.products = resArr.map(item => {
        return { ...item, productId: item.id, quantity: undefined }
      })
    },
    // 数组合并,根据productName、specs、model、materialQuality、surface、unit、weight、source去重
    unique(arr) {
      const obj = {}
      return arr.reduce((cur, next) => {
        obj[next.productName + next.specs + next.model + next.materialQuality + next.surface + next.unit + next.weight + next.source] ? '' : (obj[next.productName + next.specs + next.model + next.materialQuality + next.surface + next.unit + next.weight + next.source] = true && cur.push(next))
        return cur
      }, [])
    },
    // 删除产品
    handleDelete(row) {
      this.form.products.splice(this.form.products.indexOf(row), 1)
    },
    // 重置修改产品表单
    resetUpdate() {
      this.updateForm = {
        draw: undefined,
        materialQuality: undefined,
        model: undefined,
        picture: undefined,
        productId: undefined,
        productName: undefined,
        specs: undefined,
        surface: undefined,
        unit: undefined,
        weight: undefined
      }
      this.resetForm('updateForm')
    },
    // 修改产品
    handleUpdate(row, index) {
      this.resetUpdate()
      this.updateForm = { ...row }
      this.updateIndex = index
      const idx = this.unitOptions.findIndex(item => item === row.unit)
      if (idx === -1) this.unitOptions.push(row.unit)
      this.updateOpen = true
    },
    // 提交修改产品
    handleUpdateSubmit() {
      this.$refs.updateForm.validate(valid => {
        if (valid) {
          const data = {
            draw: this.updateForm.draw,
            materialQuality: this.updateForm.materialQuality,
            model: this.updateForm.model,
            picture: this.updateForm.picture,
            productId: this.updateForm.productId,
            productName: this.updateForm.productName,
            specs: this.updateForm.specs,
            surface: this.updateForm.surface,
            unit: this.updateForm.unit,
            weight: this.updateForm.weight
          }
          updateBiddingProduct(data).then(res => {
            const { code, msg } = res
            if (code === 200) {
              this.$message.success('修改成功')
              this.$set(this.form.products, this.updateIndex, { ...this.updateForm })
              this.updateOpen = false
            } else this.$message.error(msg)
          })
        }
      })
    },
    // 产品详情
    handleView(row) {
      const Id = row.productId
      if (row.source === 'common') {
        getProduct(Id).then(res => {
          this.$refs.productInfo.handleView(res.data)
        })
      } else if (row.source === 'bidding') {
        detailBiddingProduct({ productId: Id }).then(res => {
          this.$refs.productInfo.handleView(res.data)
        })
      } else {
        getPrivateduct(Id).then(res => {
          this.$refs.productInfo.handleView(res.data)
        })
      }
    },
    // 提交
    handleSubmit() {
      if (!this.form.products.length) return
      this.$refs.form.validate(valid => {
        if (valid) {
          const loading = this.$loading({
            lock: true,
            text: '提交中…',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })
          const data = {
            title: this.form.title,
            remark: this.form.remark,
            supplyEndTime: parseTime(this.form.supplyEndTime, '{y}-{m}-{d} 23:59:59'),
            expireTime: parseTime(this.form.expireTime, '{y}-{m}-{d} 23:59:59'),
            products: this.form.products.map(item => {
              return {
                productId: item.productId,
                productName: item.productName,
                quantity: item.quantity,
                remark: item.remark,
                source: item.source,
                unit: item.unit
              }
            })
          }
          addBidding(data).then(res => {
            setTimeout(() => {
              loading.close()
              this.$message.success('提交成功')
              this.$router.push({ name: 'Bidding' })
            }, 2000)
          })
        }
      })
    },
    // 新增
    handleDialogAdd() {
      this.init()
      this.title = '新增招标/采购需求'
      this.isDetail = false
      this.$nextTick(() => {
        this.open = true
      })
    },
    // 编辑
    handleDialogEdit(row) {
      this.init()
      this.form = {
        id: row.id,
        title: row.title,
        remark: row.remark,
        supplyEndTime: parseTime(row.supplyEndTime, '{y}-{m}-{d}'),
        expireTime: parseTime(row.expireTime, '{y}-{m}-{d}'),
        products: row.details.map(item => {
          return { ...item, ...item.product }
        })
      }
      this.title = '修改招标/采购需求'
      this.isDetail = false
      this.$nextTick(() => {
        this.open = true
      })
    },
    // 详情
    handleDialogDetail(row) {
      this.init()
      this.form = {
        title: row.title,
        remark: row.remark,
        supplyEndTime: parseTime(row.supplyEndTime, '{y}-{m}-{d}'),
        expireTime: parseTime(row.expireTime, '{y}-{m}-{d}'),
        status: row.status,
        reason: row.reason,
        products: row.details.map(item => {
          return { ...item, ...item.product }
        })
      }
      this.title = '查看招标/采购需求'
      this.isDetail = true
      this.$nextTick(() => {
        this.open = true
      })
    },
    // 新增/修改提交
    handleDialogSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          if (!!this.form.id) {
            const data = {
              demandId: this.form.id,
              title: this.form.title,
              remark: this.form.remark,
              supplyEndTime: parseTime(this.form.supplyEndTime, '{y}-{m}-{d} 23:59:59'),
              expireTime: parseTime(this.form.expireTime, '{y}-{m}-{d} 23:59:59'),
              products: this.form.products.map(item => {
                return {
                  productId: item.productId,
                  productName: item.productName,
                  quantity: item.quantity,
                  remark: item.remark,
                  source: item.source,
                  unit: item.unit
                }
              })
            }
            updateBidding(data).then(res => {
              this.open = false
              this.$message.success('修改成功')
              this.$emit('refresh')
            })
          } else {
            const data = {
              title: this.form.title,
              remark: this.form.remark,
              supplyEndTime: parseTime(this.form.supplyEndTime, '{y}-{m}-{d} 23:59:59'),
              expireTime: parseTime(this.form.expireTime, '{y}-{m}-{d} 23:59:59'),
              products: this.form.products.map(item => {
                return {
                  productId: item.productId,
                  productName: item.productName,
                  quantity: item.quantity,
                  remark: item.remark,
                  source: item.source,
                  unit: item.unit
                }
              })
            }
            addBidding(data).then(res => {
              this.open = false
              this.$message.success('新增成功')
              this.$emit('refresh')
            })
          }
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';
.detailAudit {
  margin-bottom: 10px;
  &Item {
    font-size: 14px;
    color: $red;
    line-height: 25px;
  }
}
.detailInfo {
  background: #f0f3f9;
  border-radius: 5px;
  padding: 10px 20px;
  line-height: 20px;
  .el-col {
    padding: 6px 0;
    .title {
      font-size: 12px;
      color: $info;
      margin-right: 20px;
    }
    .content {
      font-size: 14px;
      font-weight: 500;
      color: $font;
    }
  }
}
.detailTip {
  font-size: 14px;
  line-height: 20px;
  color: $disabled;
  margin: 15px auto;
}
.biddingCreate {
  border: 1px solid #cbd6e2;
  border-radius: 5px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  &Title {
    height: 50px;
    line-height: 50px;
    background-color: #eef0f8;
    font-size: 18px;
    color: $font;
    padding-left: 20px;
  }
  &Form {
    padding: 30px 20px;
    ::v-deep {
      .el-form-item__label {
        text-align: left;
        font-weight: normal;
        font-size: 12px;
        color: $disabled;
      }
    }
    &Add {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 90px;
      background-color: #f0f5ff;
      border: 1px dashed $blue;
      border-radius: 5px;
      cursor: pointer;
      font-size: 14px;
      color: $blue;
      transition: all 0.3s;
      &:hover {
        background-color: #e4ebff;
      }
    }
    &Submit {
      float: right;
      width: 270px;
      height: 50px;
      line-height: 50px;
      font-size: 16px;
      color: $white;
      background-color: $blue;
      border-radius: 5px;
      text-align: center;
      cursor: pointer;
      transition: all 0.3s;
      &.disabled {
        background-color: #cccccc;
        cursor: not-allowed;
      }
      &:hover {
        opacity: 0.8;
      }
    }
  }
}
::v-deep {
  .custom-table {
    .el-table__header .el-table__cell {
      padding: 0;
    }
    .el-form-item {
      margin-top: 10px !important;
      margin-bottom: 10px !important;
      .el-form-item__error {
        top: 95%;
        padding-top: 0;
      }
    }
  }
}
</style>
