<template>
  <div class="container">
    <header-tpl :is-login="isLogin" />
    <div class="container-box">
      <div class="containerBox">
        <div class="biddingFilter">
          <!--          <div class="biddingFilterTitle">排序方式</div>-->
          <!--          <div class="biddingFilterInfo">-->
          <!--            <div class="biddingFilterItem active">-->
          <!--              <span>按发布日期</span>-->
          <!--              <i class="el-icon-sort-down"></i>-->
          <!--              <i class="el-icon-sort-up active"></i>-->
          <!--            </div>-->
          <!--            <div class="biddingFilterItem">-->
          <!--              <span>按截止日期</span>-->
          <!--              <i class="el-icon-sort-down active"></i>-->
          <!--              <i class="el-icon-sort-up"></i>-->
          <!--            </div>-->
          <!--          </div>-->
          <div></div>
          <div class="biddingFilterBtn" @click="handleAdd">
            <i class="el-icon-plus"></i>
            <span>发布招标/采购需求</span>
          </div>
        </div>
        <div class="biddingTable" v-if="total">
          <el-table v-loading="loading" :data="list" style="width: 100%" class="custom-table">
            <el-table-column prop="title" label="招标/采购需求名称"></el-table-column>
            <el-table-column label="采购截止日期" align="center" prop="expireTime" show-overflow-tooltip width="120">
              <template slot-scope="{ row }">{{ parseTime(row.expireTime, '{y}-{m}-{d}') }}</template>
            </el-table-column>
            <el-table-column label="供货截止日期" align="center" prop="supplyEndTime" show-overflow-tooltip width="120">
              <template slot-scope="{ row }">{{ parseTime(row.supplyEndTime, '{y}-{m}-{d}') }}</template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="150">
              <template slot-scope="{ row }">
                <el-button size="small" @click="handleDetail(row)">查看详情</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <el-empty v-else />
        <div class="custom-pagination">
          <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
        </div>
      </div>
    </div>
    <footer-tpl />
  </div>
</template>
<script>
import HeaderTpl from '@/views/public/components/head'
import FooterTpl from '@/views/public/components/foot'
import { getToken } from '@/utils/auth'
import { indexListBidding } from '@/api/bidding'

export default {
  components: { FooterTpl, HeaderTpl },
  data() {
    return {
      isLogin: !!getToken(),
      loading: true,
      list: [],
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    // 查询列表
    getList() {
      // this.list = [
      //   { title: '招标/采购需求名称1', product: '采购产品1', time1: '2020-01-01', time2: '2020-01-01' },
      //   { title: '招标/采购需求名称2', product: '采购产品2', time1: '2020-01-01', time2: '2020-01-01' },
      //   { title: '招标/采购需求名称3', product: '采购产品3', time1: '2020-01-01', time2: '2020-01-01' },
      //   { title: '招标/采购需求名称4', product: '采购产品4', time1: '2020-01-01', time2: '2020-01-01' },
      //   { title: '招标/采购需求名称5', product: '采购产品5', time1: '2020-01-01', time2: '2020-01-01' }
      // ]
      this.loading = true
      indexListBidding(this.queryParams).then(res => {
        const { code, msg, rows, total } = res
        if (code === 200) {
          this.list = rows
          this.total = total
          this.loading = false
        } else this.$message.error(msg)
      })
    },
    // 切换筛选条件
    handleQuery() {},
    // 新增
    handleAdd() {
      this.$router.push({ name: 'BiddingCreate' })
    },
    // 查看详情
    handleDetail(row) {
      const { id } = row
      this.$router.push({ path: `/biddingReply?id=${id}` })
    }
  }
}
</script>
<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';
.container-box {
  min-width: 1200px;
}
.containerBox {
  width: 1200px;
  min-height: 500px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  padding-bottom: 30px;
}
.biddingFilter {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 40px;
  box-shadow: 0 1px 9px 0 rgba(0, 0, 0, 0.07);
  border: 1px solid #cbd6e2;
  margin-top: 30px;
  margin-bottom: 15px;
  &Title {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100px;
    height: 100%;
    font-size: 12px;
    color: $disabled;
    background-color: #e7e9f0;
  }
  &Info {
    flex: 1;
    overflow: hidden;
    display: flex;
    height: 100%;
    align-items: center;
    padding-left: 25px;
  }
  &Item {
    display: inline-flex;
    align-items: center;
    padding: 0 20px;
    margin: 0 5px;
    cursor: pointer;
    font-size: 14px;
    color: $disabled;
    &.active {
      color: $blue;
    }
    span {
      margin-right: 5px;
    }
    i {
      color: $disabled;
      font-size: 20px;
      margin: 0 -5px;
      &.active {
        color: $blue;
      }
    }
  }
  &Btn {
    display: flex;
    align-items: center;
    padding: 0 30px;
    height: 100%;
    font-size: 14px;
    color: $white;
    background-color: $blue;
    cursor: pointer;
    transition: all 0.3s;
    &:hover {
      opacity: 0.8;
    }
  }
}
.biddingTable {
  ::v-deep {
    .custom-table {
      background-color: transparent;
    }
    .el-table__header-wrapper {
      margin-bottom: 10px;
    }
    .el-table__body-wrapper {
      .el-table__row {
        &:hover {
          td.el-table__cell {
            background-color: #f0f3f9 !important;
          }
        }
      }
    }
  }
}
</style>
