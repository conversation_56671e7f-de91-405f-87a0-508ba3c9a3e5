<template>
  <div class="newBox bgcf9 vh-85">
    <div class="custom-search flex" style="padding-top: 18px">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" @submit.native.prevent>
        <el-form-item label="招标/采购名称" prop="title">
          <el-input v-model="queryParams.title" placeholder="请输入招标/采购名称" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="" prop="status">
          <el-select v-model="queryParams.status" placeholder="请选择状态" @change="handleQuery">
            <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-plus" size="small" @click="handleAdd">新增招标/采购需求</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="Box">
      <el-table v-loading="loading" ref="table" stripe :data="list" row-key="id" style="width: 100%" class="custom-table">
        <el-table-column align="center" type="index" label="序号" width="60"></el-table-column>
        <el-table-column label="招标/采购名称" align="center" prop="title" show-overflow-tooltip></el-table-column>
        <el-table-column label="采购截止日期" align="center" prop="expireTime" show-overflow-tooltip width="120">
          <template slot-scope="{ row }">{{ parseTime(row.expireTime, '{y}-{m}-{d}') }}</template>
        </el-table-column>
        <el-table-column label="供货截止日期" align="center" prop="supplyEndTime" show-overflow-tooltip width="120">
          <template slot-scope="{ row }">{{ parseTime(row.supplyEndTime, '{y}-{m}-{d}') }}</template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="createTime" show-overflow-tooltip width="120"></el-table-column>
        <el-table-column label="创建人" align="center" prop="createBy" show-overflow-tooltip width="100"></el-table-column>
        <el-table-column label="状态" align="center" prop="status" width="90">
          <template slot-scope="{ row }">
            <el-tooltip effect="dark" :content="row.reason" :disabled="!row.reason">
              <el-tag size="small" :type="formatStatus(row.status, 'type')">{{ formatStatus(row.status, 'label') }}</el-tag>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="right" header-align="center" width="280">
          <template slot-scope="{ row }">
            <el-button class="table-btn success small" @click="handleReply(row)" v-if="row.status == 1">查看报价</el-button>
            <el-button class="table-btn primary small" @click="handleDetail(row)">查看详情</el-button>
            <el-button class="table-btn orange small" @click="handleEdit(row)">修改</el-button>
            <el-button class="table-btn red small" @click="handleDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="custom-pagination">
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
      </div>
    </div>
    <!--新增/修改/查看详情招标采购-->
    <create-tpl ref="createTpl" is-dialog @refresh="handleRefresh" />
    <!--查看报价-->
    <offer-tpl ref="offerTpl" />
  </div>
</template>
<script>
import { allReplyBidding, deleteBidding, detailBidding, listBidding } from '@/api/bidding'
import CreateTpl from './createTpl'
import { parseTime } from '@/utils/ruoyi'
import OfferTpl from './offer'

export default {
  name: 'BiddingMange',
  components: { OfferTpl, CreateTpl },
  data() {
    return {
      loading: true,
      list: [],
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        title: undefined,
        // fileterExpire: true,
        status: undefined
      },
      statusOptions: [
        { label: '正常', value: 0, type: '' },
        { label: '审核通过', value: 1, type: 'success' },
        { label: '审核驳回', value: -1, type: 'warning' },
        { label: '已删除', value: -10, type: 'danger' }
      ]
    }
  },
  created() {
    this.getList()
  },
  methods: {
    parseTime,
    // 格式化状态
    formatStatus(status, type = 'label') {
      const item = this.statusOptions.find(item => item.value === status)
      return item ? item[type] : ''
    },
    // 列表
    getList() {
      this.loading = true
      listBidding(this.queryParams).then(res => {
        const { code, msg, rows, total } = res
        if (code === 200) {
          this.list = rows
          this.total = total
          this.loading = false
        } else this.$message.error(msg)
      })
    },
    // 刷新
    handleRefresh() {
      listBidding(this.queryParams).then(res => {
        const { code, msg, rows, total } = res
        if (code === 200) {
          this.$set(this, 'list', rows)
          this.$set(this, 'total', total)
        } else this.$message.error(msg)
      })
    },
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 重置搜索
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 新增
    handleAdd() {
      this.$refs.createTpl.handleDialogAdd()
    },
    // 编辑
    handleEdit(row) {
      detailBidding({ demandId: row.id }).then(res => {
        const { code, msg, data } = res
        if (code === 200) this.$refs.createTpl.handleDialogEdit(data)
        else this.$message.error(msg)
      })
    },
    // 详情
    handleDetail(row) {
      detailBidding({ demandId: row.id }).then(res => {
        const { code, msg, data } = res
        if (code === 200) this.$refs.createTpl.handleDialogDetail(data)
        else this.$message.error(msg)
      })
    },
    // 删除
    // prettier-ignore
    handleDelete(row) {
      const demandId = row.id
      this.$confirm('是否删除该招标采购？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteBidding({ demandId }).then(res => {
          const { code, msg } = res
          if (code === 200) {
            this.$message.success('删除成功')
            this.handleRefresh()
          } else this.$message.error(msg)
        })
      })
    },
    // 查看报价
    handleReply(row) {
      allReplyBidding({ demandId: row.id }).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          if (!!data.length) this.$refs.offerTpl.handleOpen(row.id, data)
          else this.$message.warning('暂无报价')
        } else this.$message.error(msg)
      })
    }
  }
}
</script>
<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';
.Box {
  padding: 15px 20px;
}
.custom-table {
  .table-btn {
    padding: 0;
  }
}
</style>
