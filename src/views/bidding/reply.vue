<template>
  <div class="container">
    <header-tpl :is-login="isLogin" />
    <div class="container-box">
      <div class="containerBox">
        <el-form ref="form" :model="form" :rules="rules" label-width="0">
          <div class="replyTitle">招标/采购需求</div>
          <div class="replyTip">招标/采购详情</div>
          <div class="replyInfo">
            <el-row :gutter="10">
              <el-col :span="24">
                <span class="title">标题</span>
                <b class="content">{{ form.title }}</b>
              </el-col>
              <el-col :span="12">
                <span class="title">采购截止日期</span>
                <b class="content">{{ parseTime(form.expireTime, '{y}-{m}-{d}') }}</b>
              </el-col>
              <el-col :span="12">
                <span class="title">采购截止日期</span>
                <b class="content">{{ parseTime(form.supplyEndTime, '{y}-{m}-{d}') }}</b>
              </el-col>
              <el-col :span="24">
                <span class="title">备注</span>
                <b class="content">{{ form.remark }}</b>
              </el-col>
            </el-row>
          </div>
          <div class="replyTip">采购产品列表</div>
          <el-table v-loading="loading" ref="detailTable" stripe :data="form.products" row-key="id" style="width: 100%" class="custom-table" :class="{ 'custom-table-cell0': isReply }">
            <el-table-column align="center" type="index" label="序号"></el-table-column>
            <el-table-column align="center" prop="productName" label="产品名称" show-overflow-tooltip min-width="130">
              <template slot-scope="{ row }">
                <span class="table-link" @click="hanleView(row)">{{ row.productName }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="specs" label="规格" show-overflow-tooltip></el-table-column>
            <el-table-column align="center" prop="model" label="型号" show-overflow-tooltip width="70"></el-table-column>
            <el-table-column align="center" prop="materialQuality" label="材质" show-overflow-tooltip></el-table-column>
            <el-table-column align="center" prop="surface" label="表面处理" show-overflow-tooltip></el-table-column>
            <el-table-column align="center" prop="unit" label="单位" show-overflow-tooltip></el-table-column>
            <el-table-column align="center" prop="weight" label="重量" show-overflow-tooltip></el-table-column>
            <el-table-column align="center" prop="remark" label="产品备注" show-overflow-tooltip></el-table-column>
            <el-table-column align="center" prop="quantity" label="采购数量" show-overflow-tooltip>
              <template slot-scope="{ row }">{{ row.quantity + row.unit }}</template>
            </el-table-column>
            <template v-if="isReply">
              <el-table-column align="center" label="可供应数量" show-overflow-tooltip width="120">
                <template slot-scope="scope">
                  <el-form-item label-width="0" :prop="`products.${scope.$index}.maxNum`" :rules="rules.maxNum" class="primary">
                    <el-input v-model="scope.row.maxNum" size="small" placeholder="不限" />
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column align="center" label="报价" show-overflow-tooltip width="150">
                <template slot-scope="scope">
                  <el-form-item label-width="0" :prop="`products.${scope.$index}.amount`" :rules="rules.amount" class="price">
                    <el-input v-model="scope.row.amount" size="small" placeholder="">
                      <div slot="suffix" class="inline-flex">
                        <span>元/</span>
                        <el-select size="small" v-model="scope.row.replyUnit" filterable allow-create class="selectNone" placeholder="" :style="{ width: `calc(${scope.row.replyUnit.length || 1} * 15px)` }">
                          <el-option v-for="item in unitOptions" :key="item" :label="item" :value="item"></el-option>
                        </el-select>
                        <span class="el-icon-arrow-down"></span>
                      </div>
                    </el-input>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column align="center" label="备注" show-overflow-tooltip width="120">
                <template slot-scope="scope">
                  <el-form-item label-width="0" :prop="`products.${scope.$index}.replyremark`" :rules="rules.replyremark" class="primary">
                    <el-input v-model="scope.row.replyremark" size="small" placeholder="请输入备注" />
                  </el-form-item>
                </template>
              </el-table-column>
            </template>
          </el-table>
        </el-form>
        <div class="replyButton">
          <el-button type="primary" @click="handleReply" v-if="!isReply">我要报价</el-button>
          <template v-if="isReply">
            <el-button @click="isReply = false">取消</el-button>
            <el-button type="primary" @click="handleSubmit">提交</el-button>
          </template>
        </div>
      </div>
    </div>
    <footer-tpl />

    <!-- 产品详情 -->
    <product-dialog ref="productInfo"></product-dialog>
  </div>
</template>
<script>
import HeaderTpl from '@/views/public/components/head'
import FooterTpl from '@/views/public/components/foot'
import { getToken } from '@/utils/auth'
import { detailBidding, replyBidding, replyBiddingByUser } from '@/api/bidding'
import { isNumber } from '@/utils/validate'
import ProductDialog from '@/views/public/product/dialog'

export default {
  components: { ProductDialog, FooterTpl, HeaderTpl },
  data() {
    return {
      loading: true,
      isLogin: !!getToken(),
      form: {},
      rules: {
        amount: [{ validator: isNumber, message: '输入有误请重新输入', trigger: 'blur' }],
        maxNum: [{ validator: isNumber, message: '输入有误请重新输入', trigger: 'blur' }]
      },
      unitOptions: ['吨', '千克', '个', '件', '套', '米', '支', '根', '袋', '托'],
      isReply: false,
      demandId: undefined
    }
  },
  created() {
    this.getInfo()
  },
  methods: {
    back() {
      this.$alert('参数错误，请联系管理员', '系统提示', {
        type: 'error',
        confirmButtonText: '确定',
        callback: action => {
          this.$router.push('/bidding')
        }
      })
    },
    getInfo() {
      const { id } = this.$route.query
      if (!id) {
        this.back()
        return
      }
      this.loading = true
      detailBidding({ demandId: id }).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          if (data.status != 1) {
            this.back()
            return
          }
          this.demandId = id
          this.form = data
          this.form.products = data.details.map(item => {
            return { ...item.product, ...item, amount: undefined, replyUnit: item.product.unit, replyremark: undefined }
          })
          this.loading = false
        } else this.$message.error(msg)
      })
    },
    // 我要报价
    handleReply() {
      this.loading = true
      replyBiddingByUser({ demandId: this.demandId }).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          this.form.products.forEach(item => {
            data.forEach(ite => {
              if (item.id === ite.detailId) {
                item.amount = ite.amount
                item.replyUnit = ite.replyUnit
                item.replyremark = ite.remark
                item.replyId = ite.id
              }
            })
          })
          this.loading = false
          this.isReply = true
        } else this.$message.error(msg)
      })
    },
    // 提交报价
    // prettier-ignore
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          const len = this.form.products.filter(item => item.amount).length
          if (len === 0) {
            this.$message.error('请至少填写一个产品报价')
            return
          }
          const products = this.form.products.filter(item => item.amount)
          const reply = products.map(item => {
            return {
              amount: item.amount,
              detailId: item.id,
              maxNum: item.maxNum,
              remark: item.replyremark,
              replyId: item.replyId,
              replyUnit: item.replyUnit
            }
          })
          this.$confirm('确定提交报价吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            replyBidding({ demandId: this.demandId, reply }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('报价成功')
                this.isReply = false
              } else this.$message.error(msg)
            })
          }).catch(() => {})
        }
      })
    },
    // 产品详情
    hanleView(row) {
      this.$refs.productInfo.handleView(row)
    }
  }
}
</script>
<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';
.container-box {
  min-width: 1200px;
}
.containerBox {
  width: 1200px;
  min-height: 500px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  padding-bottom: 30px;
}
.replyTitle {
  background-color: #eef0f8;
  padding: 15px 30px;
  font-size: 18px;
  line-height: 20px;
  color: $info;
}
.replyTip {
  font-size: 14px;
  line-height: 20px;
  padding: 15px 30px;
  color: $disabled;
}
.replyInfo {
  padding: 10px 30px;
  background-color: #f0f3f9;
  .el-col {
    padding: 6px 0;
    .title {
      font-size: 12px;
      color: $info;
      margin-right: 20px;
    }
    .content {
      font-size: 14px;
      font-weight: 500;
      color: $font;
    }
  }
}
.replyButton {
  display: flex;
  justify-content: flex-end;
  margin: 30px 0;
  .el-button {
    width: 270px;
  }
}
::v-deep .custom-table {
  &.el-table .cell {
    padding: 0 3px;
  }
  box-shadow: none;
  .el-form-item {
    margin-top: 10px !important;
    margin-bottom: 10px !important;
    .el-form-item__error {
      top: 95%;
      padding-top: 0;
    }
    .el-input__inner {
      text-align: left;
    }
    &.primary {
      .el-input__inner:focus {
        color: $blue;
      }
    }
    &.price {
      .el-input {
        .el-input__inner:focus {
          border-color: #ec4545;
          color: #ec2454;
        }
      }
      .selectNone {
        .el-input {
          .el-input__inner {
            color: #c0c4cc;
            padding: 0;
            border: 0;
            background: transparent;
          }
          .el-input__inner:focus {
            color: #c0c4cc;
          }
          .el-input__suffix {
            display: none;
          }
        }
      }
    }
  }
}
</style>
