<template>
  <div>
    <el-form ref="registerForm" :model="registerForm" :rules="registerRules" class="register-form" :key="key">
      <el-steps :active="active" :space="80" class="register-step">
        <el-step></el-step>
        <el-step></el-step>
        <el-step></el-step>
      </el-steps>
      <template v-if="active === 0">
        <el-form-item prop="countryCode">
          <el-select v-model="registerForm.countryCode" placeholder="请选择国家" style="width: 100%" filterable>
            <el-option v-for="(item, index) in countryCodeList" :key="`${item.value}-${index}`" :label="`${item.country}`" :value="item.countryCode"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="companyName">
          <el-input v-model="registerForm.companyName" auto-complete="off" placeholder="请输入公司名称"></el-input>
        </el-form-item>
        <el-form-item prop="address">
          <el-input v-model="registerForm.address" auto-complete="off" placeholder="请输入公司地址"></el-input>
        </el-form-item>
        <el-form-item prop="uscCode">
          <el-input v-model="registerForm.uscCode" auto-complete="off" placeholder="请输入统一社会信用代码"></el-input>
        </el-form-item>
        <el-form-item prop="licenseImg">
          <el-upload :class="{ hide: hideUpload }" :file-list="fileList" :action="uploadUrl" list-type="picture-card" accept=".png,.jpg" :limit="1" :on-success="handleSuccess" :before-upload="beforeAvatarUpload">
            <i class="el-icon-plus"></i>
            <span class="el-upload-picture-card-title">上传营业执照</span>
            <div slot="file" slot-scope="{ file }">
              <img class="el-upload-list__item-thumbnail" :src="imgPath + file.fileName" alt="" />
              <span class="el-upload-list__item-actions">
                <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
                  <i class="el-icon-zoom-in"></i>
                </span>
                <span v-if="!disabled" class="el-upload-list__item-delete" @click="handleRemove(file)">
                  <i class="el-icon-delete"></i>
                </span>
              </span>
            </div>
          </el-upload>
        </el-form-item>
        <el-form-item style="width: 100%; margin-top: 50px">
          <el-button size="medium" type="primary" style="width: 100%" @click.native.prevent="handleNext">下一步</el-button>
        </el-form-item>
      </template>
      <template v-if="active === 1">
        <input type="text" class="input-hidden" />
        <input type="password" class="input-hidden" />
        <el-form-item prop="phone">
          <el-input v-model="registerForm.phone" auto-complete="off" placeholder="请输入您的邮箱"></el-input>
        </el-form-item>
        <el-form-item prop="smsCode">
          <el-input v-model="registerForm.smsCode" auto-complete="off" placeholder="请输入邮箱验证码">
            <template slot="append">
              <el-button :loading="sending" :disabled="sending" @click="handleSend">{{ sendTitle }}</el-button>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item prop="password">
          <el-input v-model="registerForm.password" type="password" auto-complete="off" placeholder="请设置6~20位字母与数字结合的登录密码" show-password></el-input>
        </el-form-item>
        <el-form-item prop="confirmPassword">
          <el-input v-model="registerForm.confirmPassword" type="password" auto-complete="off" placeholder="请输入确认密码" show-password></el-input>
        </el-form-item>
        <el-form-item style="width: 100%" class="register-deal" prop="checked">
          <el-checkbox v-model="registerForm.checked" @change="handleChecked"></el-checkbox>
          <span class="span">我已阅读并同意</span>
          <b class="b" @click="protocolOpen = true">《注册用户服务协议》</b>
        </el-form-item>
        <el-form-item style="width: 100%">
          <el-button :loading="loading" size="medium" type="primary" style="width: 100%" @click="handleRegister">
            <span v-if="!loading">立即注册</span>
            <span v-else>注 册 中...</span>
          </el-button>
          <div style="width: 100%; text-align: center"><span class="register-prev" @click="handlePrev">返回上一步</span></div>
        </el-form-item>
      </template>
      <template v-if="active === 2">
        <div style="text-align: center; margin-top: 80px">
          <i class="el-icon-success" style="font-size: 50px; color: #2e73f3"></i>
          <div style="font-size: 20px; color: #333333; margin-top: 20px">恭喜，注册成功！</div>
          <el-button size="medium" type="primary" style="width: 100%; margin-top: 50px" @click="$router.push('/login')">立即登录</el-button>
        </div>
      </template>
    </el-form>
    <el-dialog v-dialogDragBox :visible.sync="dialogVisible">
      <img width="100%" :src="dialogImageUrl" alt="" />
    </el-dialog>

    <el-dialog v-dialogDragBox title="注册用户服务协议" :visible.sync="protocolOpen" width="80%" append-to-body :show-close="false" :close-on-click-modal="false" :close-on-press-escape="false" center class="protocol">
      <div slot="title"></div>
      <iframe class="protocol-iframe" src="https://www.ziyouke.net/protocol.html"></iframe>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleAgree">我已阅读并同意用户协议</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { registerCompany, sendEmailCode } from '@/api/system/user'
import { validEmail } from '@/utils/validate'
import { countryCodeList, getCountryIndexByIP } from '@/utils/countryCode'

export default {
  data() {
    const equalToPassword = (rule, value, callback) => {
      if (this.registerForm.password !== value) {
        callback(new Error('两次输入的密码不一致'))
      } else {
        callback()
      }
    }
    const validateEmail = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入您的邮箱'))
      } else if (!validEmail(value)) {
        callback(new Error('请输入正确的邮箱'))
      } else {
        callback()
      }
    }

    return {
      uploadUrl: process.env.VUE_APP_BASE_API + '/common/upload',
      countryCodeList,
      active: 0,
      registerForm: {
        countryCode: 'CN', // 默认中国国家
        address: undefined,
        companyName: undefined,
        licenseImg: undefined,
        logo: undefined,
        password: undefined,
        phone: undefined,
        smsCode: undefined,
        uscCode: undefined,
        checked: [],
        t: 'email'
      },
      registerRules: {
        countryCode: [{ required: true, trigger: 'change', message: '请选择国家' }],
        companyName: [{ required: true, message: '请输入公司名称', trigger: 'blur' }],
        address: [{ required: true, message: '请输入公司地址', trigger: 'blur' }],
        uscCode: [
          { required: true, message: '请输入统一社会信用代码', trigger: 'blur' }
          // { validator: isCreditCode, message: '统一社会信用代码输入有误', trigger: 'blur' }
        ],
        licenseImg: [{ required: true, message: '请上传营业执照', trigger: 'change' }],
        phone: [{ required: true, validator: validateEmail, trigger: 'blur' }],
        password: [
          { required: true, trigger: 'blur', message: '请输入您的密码' },
          { min: 6, max: 20, message: '用户密码长度必须介于 6 和 20 之间', trigger: 'blur' }
        ],
        confirmPassword: [
          { required: true, trigger: 'blur', message: '请再次输入您的密码' },
          { required: true, validator: equalToPassword, trigger: 'blur' }
        ],
        smsCode: [{ required: true, trigger: 'blur', message: '请输入短信验证码' }],
        checked: [{ type: 'array', required: true, trigger: 'blur', message: '请阅读并同意注册用户服务协议' }]
      },
      dialogImageUrl: '',
      dialogVisible: false,
      disabled: false,
      hideUpload: false,
      loading: false,
      sending: false,
      second: 60,
      key: 1,
      fileList: [],
      protocolOpen: false
    }
  },
  computed: {
    sendTitle() {
      return this.sending ? `${this.second}s后重新获取` : '获取验证码'
    }
  },
  async mounted() {
    // 根据IP自动定位国家
    await this.autoLocateCountry()
  },
  methods: {
    // 根据IP自动定位国家
    async autoLocateCountry() {
      try {
        const countryIndex = await getCountryIndexByIP()
        if (countryIndex !== -1 && countryIndex < this.countryCodeList.length) {
          const countryInfo = this.countryCodeList[countryIndex]
          this.registerForm.countryCode = countryInfo.countryCode
        }
      } catch (error) {
        console.warn('自动定位失败，使用默认设置', error)
      }
    },
    handleRemove(file) {
      this.registerForm.licenseImg = undefined
      this.hideUpload = false
      this.fileList = []
    },
    handleSuccess(res) {
      this.registerForm.licenseImg = res.fileName
      this.hideUpload = true
      this.fileList = [res]
      this.$refs.registerForm.clearValidate('licenseImg')
    },
    handleChecked() {
      this.$refs.registerForm.clearValidate('checked')
    },
    handleNext() {
      const arr = ['countryCode', 'companyName', 'address', 'uscCode', 'licenseImg']
      let num = 0
      arr.map(item => {
        this.$refs.registerForm.validateField(item, valid => {
          if (!valid) num++
        })
      })
      if (num === arr.length) {
        this.active = 1
        this.key = Math.random()
      }
    },
    handlePrev() {
      this.active = 0
      this.$refs.registerForm.clearValidate()
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = this.imgPath + file.fileName
      this.dialogVisible = true
    },
    handleSend() {
      this.$refs.registerForm.validateField('phone', valid => {
        if (!valid) {
          this.sending = true
          this.second = 60
          sendEmailCode({ userEmail: this.registerForm.phone }).then(res => {
            this.$message.success('验证码发送成功')
            const timer = setInterval(() => {
              this.second--
              if (this.second <= 0) {
                clearInterval(timer)
                this.sending = false
                this.second = 60
              }
            }, 1000)
          })
        }
      })
    },
    // prettier-ignore
    handleRegister() {
      this.$refs.registerForm.validate(valid => {
        if (valid) {
          const { phone, countryCode, password, smsCode, companyName, uscCode, address, licenseImg, t } = this.registerForm
          this.loading = true
          registerCompany({ phone, countryCode, password, smsCode, companyName, uscCode, address, licenseImg, t }).then(res => {
            if (res.code === 200) {
              this.loading = false
              this.active = 2
              this.key = Math.random()
            } else {
              this.$message.error(res.msg)
              this.loading = false
            }
          }).catch(() => {
            this.loading = false
          })
        }
      })
    },
    // 同意协议
    handleAgree() {
      this.registerForm.checked = [undefined]
      this.$refs.registerForm.clearValidate('checked')
      this.protocolOpen = false
    },
    // 限制上传营业执照
    beforeAvatarUpload(file) {
      // if (!getToken()) {
      //   this.$message.error(`请先登录`);
      //   return false
      // }
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
      const isLt2M = file.size / 1024 / 1024 < 2
      if (!isJPG) {
        this.$message.error('营业执照图片只可以上传.jpg或.png格式!')
        return false
      }
      if (!isLt2M) {
        this.$message.error('营业执照图片大小不能超过 2MB!')
        return false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.register-form {
  width: 430px;
  .el-input {
    height: 38px;
    input {
      height: 38px;
    }
  }
  .input-icon {
    height: 39px;
    width: 14px;
    margin-left: 2px;
  }
  ::v-deep {
    .hide {
      height: 128px;
      .el-upload--picture-card {
        display: none;
      }
      .el-upload-list__item {
        margin-bottom: 0;
      }
    }
    .el-upload-list--picture-card {
      .el-upload-list__item {
        width: 128px;
        height: 128px;
      }
    }
    .el-upload--picture-card {
      width: 128px;
      height: 128px;
      line-height: 128px;
      display: inline-flex;
      flex-direction: column;
      justify-content: center;
      .el-upload-picture-card-title {
        font-size: 14px;
        color: #999999;
        line-height: 32px;
      }
    }
  }
  .register-step {
    justify-content: center;
    margin-bottom: 18px;
    ::v-deep {
      .el-step__line {
        background-color: transparent;
      }
      .is-finish {
        .el-step__line {
          border-bottom: 1px dashed #42d3ad;
        }
      }
      .is-process,
      .is-wait {
        .el-step__line {
          border-bottom: 1px dashed #dcdeea;
        }
      }
      .is-process,
      .is-finish {
        .el-step__icon.is-text {
          background-color: #42d3ad;
          border-color: #42d3ad;
          .el-step__icon-inner {
            color: #ffffff;
          }
        }
      }
      .is-wait {
        .el-step__icon.is-text {
          background-color: #dcdeea;
          border-color: #dcdeea;
          .el-step__icon-inner {
            color: #ffffff;
          }
        }
      }
    }
  }
  .register-deal {
    display: flex;
    margin-top: 60px;
    font-size: 12px;
    justify-content: center;
    ::v-deep .el-form-item__content {
      line-height: 1;
      .span {
        margin-left: 10px;
        color: #999999;
      }
      .b {
        font-weight: normal;
        color: #2405f2;
        cursor: pointer;
      }
    }
  }
  .register-prev {
    display: inline-block;
    margin: 0 auto;
    text-align: center;
    line-height: 40px;
    font-weight: 500;
    color: #2e73f3;
    font-size: 12px;
    cursor: pointer;
  }
}
.input-hidden {
  width: 0;
  height: 0;
  border: 0;
  padding: 0;
}
.protocol ::v-deep {
  .el-dialog__header {
    padding: 0;
  }
  .el-dialog__body {
    padding: 20px;
  }
  .protocol-iframe {
    width: 100%;
    height: calc(88vh - 110px);
    border: 1px solid #eeeeee;
  }
}
</style>
