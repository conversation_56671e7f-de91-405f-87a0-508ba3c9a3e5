<template>
  <div>
    <el-form ref="registerForm" :model="registerForm" :rules="registerRules" class="register-form">
      <input type="text" class="input-hidden" />
      <input type="password" class="input-hidden" />
      <el-form-item prop="countryCode">
        <el-select v-model="registerForm.countryCode" placeholder="请选择国家" style="width: 100%" filterable>
          <el-option v-for="(item, index) in countryCodeList" :key="`${item.value}-${index}`" :label="`${item.country}`" :value="item.countryCode"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="phone">
        <el-input v-model="registerForm.phone" auto-complete="off" placeholder="请输入您的邮箱"></el-input>
      </el-form-item>
      <el-form-item prop="smsCode">
        <el-input v-model="registerForm.smsCode" auto-complete="off" placeholder="请输入邮箱验证码">
          <template slot="append">
            <el-button :loading="sending" :disabled="sending" @click="handleSend">{{ sendTitle }}</el-button>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item prop="password">
        <el-input v-model="registerForm.password" type="password" auto-complete="off" placeholder="请设置6~20位字母与数字结合的登录密码" show-password></el-input>
      </el-form-item>
      <el-form-item prop="confirmPassword">
        <el-input v-model="registerForm.confirmPassword" type="password" auto-complete="off" placeholder="请输入确认密码" show-password></el-input>
      </el-form-item>
      <el-form-item style="width: 100%" class="register-deal" prop="checked">
        <el-checkbox v-model="registerForm.checked" @change="handleChecked"></el-checkbox>
        <span class="span">我已阅读并同意</span>
        <b class="b" @click="protocolOpen = true">《注册用户服务协议》</b>
      </el-form-item>
      <el-form-item style="width: 100%; margin-top: 60px">
        <el-button :loading="loading" size="medium" type="primary" style="width: 100%" @click="handleRegister">
          <span v-if="!loading">立即注册</span>
          <span v-else>注 册 中...</span>
        </el-button>
      </el-form-item>
    </el-form>

    <el-dialog v-dialogDragBox title="注册用户服务协议" :visible.sync="protocolOpen" width="80%" append-to-body :show-close="false" :close-on-click-modal="false" :close-on-press-escape="false" center class="protocol">
      <div slot="title"></div>
      <iframe class="protocol-iframe" src="https://www.ziyouke.net/protocol.html"></iframe>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleAgree">我已阅读并同意用户协议</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { registerUser, sendEmailCode } from '@/api/system/user'
import { countryCodeList, getCountryIndexByIP } from '@/utils/countryCode'
import { validEmail } from '@/utils/validate'

export default {
  name: 'Register',
  data() {
    const equalToPassword = (rule, value, callback) => {
      if (this.registerForm.password !== value) {
        callback(new Error('两次输入的密码不一致'))
      } else {
        callback()
      }
    }
    const validateEmail = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入您的邮箱'))
      } else if (!validEmail(value)) {
        callback(new Error('请输入正确的邮箱'))
      } else {
        callback()
      }
    }

    return {
      countryCodeList,
      registerForm: {
        countryCode: 'CN', // 默认设置为中国国家
        phone: '',
        password: '',
        t: 'email',
        confirmPassword: '',
        smsCode: '',
        checked: []
      },
      registerRules: {
        countryCode: [{ required: true, trigger: 'change', message: '请选择国家' }],
        phone: [{ required: true, validator: validateEmail, trigger: 'blur' }],
        password: [
          { required: true, trigger: 'blur', message: '请输入您的密码' },
          { min: 6, max: 20, message: '用户密码长度必须介于 6 和 20 之间', trigger: 'blur' }
        ],
        confirmPassword: [
          { required: true, trigger: 'blur', message: '请再次输入您的密码' },
          { required: true, validator: equalToPassword, trigger: 'blur' }
        ],
        smsCode: [{ required: true, trigger: 'blur', message: '请输入短信验证码' }],
        checked: [{ type: 'array', required: true, trigger: 'blur', message: '请阅读并同意注册用户服务协议' }]
      },
      loading: false,
      sending: false,
      second: 60,
      protocolOpen: false
    }
  },
  computed: {
    sendTitle() {
      return this.sending ? `${this.second}s后重新获取` : '获取验证码'
    }
  },
  async mounted() {
    // 根据IP自动定位国家
    await this.autoLocateCountryCode()
  },
  methods: {
    // 根据IP自动定位国家
    async autoLocateCountryCode() {
      try {
        const countryIndex = await getCountryIndexByIP()
        if (countryIndex !== -1 && countryIndex < this.countryCodeList.length) {
          const countryInfo = this.countryCodeList[countryIndex]
          this.registerForm.countryCode = countryInfo.countryCode
        }
      } catch (error) {
        console.warn('自动定位失败，使用默认设置', error)
      }
    },
    handleSend() {
      this.$refs.registerForm.validateField('phone', valid => {
        if (!valid) {
          this.sending = true
          this.second = 60
          sendEmailCode({ userEmail: this.registerForm.phone }).then(res => {
            this.$message.success('验证码发送成功')
            const timer = setInterval(() => {
              this.second--
              if (this.second <= 0) {
                clearInterval(timer)
                this.sending = false
                this.second = 60
              }
            }, 1000)
          })
        }
      })
    },
    handleChecked() {
      this.$refs.registerForm.clearValidate('checked')
    },
    // prettier-ignore
    handleRegister() {
      this.$refs.registerForm.validate(valid => {
        if (valid) {
          const { phone, password, smsCode, countryCode, t } = this.registerForm
          this.loading = true
          registerUser({ phone, countryCode, password, smsCode, t }).then(res => {
            this.$alert("<font color='red'>恭喜你，您的账号 " + ' ' + phone + ' 注册成功！</font>', '系统提示', {
              dangerouslyUseHTMLString: true,
              type: 'success'
            }).then(() => {
              this.$router.push('/login')
            })
          }).catch(() => {
            this.loading = false
          })
        }
      })
    },
    // 同意协议
    handleAgree() {
      this.registerForm.checked = [undefined]
      this.$refs.registerForm.clearValidate('checked')
      this.protocolOpen = false
    }
  }
}
</script>

<style lang="scss" scoped>
.register-form {
  width: 430px;
  padding-top: 35px;
  .el-input {
    height: 38px;
    input {
      height: 38px;
    }
  }
  .input-icon {
    height: 39px;
    width: 14px;
    margin-left: 2px;
  }
}
.input-hidden {
  width: 0;
  height: 0;
  border: 0;
  padding: 0;
}
.register-deal {
  display: flex;
  font-size: 12px;
  ::v-deep .el-form-item__content {
    line-height: 1;
    .span {
      margin-left: 10px;
      color: #999999;
    }
    .b {
      font-weight: normal;
      color: #2405f2;
      cursor: pointer;
    }
  }
}
.protocol ::v-deep {
  .el-dialog__header {
    padding: 0;
  }
  .el-dialog__body {
    padding: 20px;
  }
  .protocol-iframe {
    width: 100%;
    height: calc(88vh - 110px);
    border: 1px solid #eeeeee;
  }
}
</style>
