<template>
  <div class="newBox bgcf9 vh-85">
    <!-- 搜索 -->
    <div class="custom-search flex">
      <div class="flex">
        <div class="custom-search-form flex">
          <input type="text" v-model="queryParams.title" placeholder="请输入需求标题" class="custom-search-input"
            @keyup.enter="handleQuery" />
          <button type="button" class="custom-search-button pointer" @click="handleQuery">
            <i class="el-icon-search"></i>
            搜索
          </button>
        </div>
        <button type="button" class="custom-search-add pointer" @click="handleAdd">
          <i class="el-icon-plus"></i>
          新建挂牌督办
        </button>
        <el-select style="margin-left: 15px" v-model="queryParams.status" @change="handleQuery" clearable size="small"
          placeholder="请选择状态">
          <el-option v-for="item in statusOptions" :key="item.value" :label="item.label"
            :value="item.value"></el-option>
        </el-select>
      </div>
    </div>

    <!-- 分类 -->
    <div class="classify flex">
      <!-- <div class="classify-item" :class="{ active: item.value === queryParams.identity }"
          v-for="item in identityList" :key="item.value" @click="handleIdentity(item)">
          {{ item.label }}
      </div> -->
      <div class="classify-toolbar">
        <right-toolbar :search="false" @queryTable="getList" :columns="columns"></right-toolbar>
      </div>
    </div>

    <!-- 表格数据 -->
    <div class="tableBox">
      <el-table v-loading="loading" ref="table" stripe :data="list" row-key="id" :key="key" style="width: 100%"
        class="custom-table custom-table-cell5">
        <el-table-column align="center" type="index" label="序号" v-if="columns[0].visible"></el-table-column>
        <el-table-column align="left" prop="title" label="标题" show-overflow-tooltip
          v-if="columns[1].visible"></el-table-column>
        <el-table-column align="center" prop="content" label="任务详细" show-overflow-tooltip v-if="columns[2].visible">
          <!-- <template slot-scope="{ row }">
              <div @click="handleDetailView(row)" style="color: #2E73F3; cursor: pointer;">查看详情 ＞</div>
          </template> -->
        </el-table-column>
        <el-table-column align="center" prop="deptNames" label="参与部门" show-overflow-tooltip
          v-if="columns[3].visible"></el-table-column>
        <el-table-column align="center" prop="priority" label="优先级" show-overflow-tooltip v-if="columns[4].visible"
          width="100">
          <template slot-scope="{ row }">
            <div v-if="row.priority == 0" style="color: #2bcc75">一般</div>
            <div v-if="row.priority == 1" style="color: #f39323">加急</div>
            <div v-if="row.priority == 2" style="color: #f43f3f">紧急</div>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="superviseName" label="督办人" show-overflow-tooltip
          v-if="columns[5].visible"></el-table-column>
          <el-table-column align="center" prop="createTime" label="创建时间" show-overflow-tooltip
            v-if="columns[8].visible"></el-table-column>
        <el-table-column align="center" prop="completionDate" label="完成时间" show-overflow-tooltip
          v-if="columns[6].visible">
          <template slot-scope="{ row }">
            <div class="finishTime_box">
              <div class="time">{{ parseTime(row.completionDate, '{y}-{m}-{d}') }}</div>
              <div class="tips" v-if="new Date(row.completionDate) < new Date() && row.status == 0">
                <img src="~@/assets/images/remind.png" alt="" />
                <span>已超期</span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="status" label="完成状态" show-overflow-tooltip v-if="columns[7].visible">
          <template slot-scope="{ row }">
            <el-switch v-model="row.status" active-text="已完成" inactive-text="未完成" :active-value="1" :inactive-value="0"
              @change="handleStatusChange(row, $event)" class="table-switch"
              :disabled="userInfo.userId != row.creator"></el-switch>
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作" width="220px">
          <template slot-scope="{ row }">
            <button type="button" class="table-btn primary" @click="handleViwe(row)">查看详情</button>
            <button type="button" class="table-btn danger" @click="handleDelete(row)" v-if="queryParams.status != -10 && checkPermi(['oa:todo:delete'])">删除</button>
            <button type="button" class="table-btn primary" @click="handleUpdate(row)"
              v-if="userInfo.userId == row.creator">编辑</button>
          </template>
        </el-table-column>
      </el-table>

      <div class="custom-pagination">
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize" @pagination="getList" />
      </div>
    </div>

    <!--新增待办弹窗-->
    <el-dialog v-dialogDragBox title="新增待办任务" :visible.sync="addListingOpen" width="1150px" class="custom-dialog">
      <div class="p20">
        <el-form ref="form" :model="form" :rules="rules" label-width="8em">
          <el-row>
            <el-col :span="12">
              <el-form-item label="任务优先级" prop="priority">
                <el-select v-model="form.priority" placeholder="请选择任务优先级" clearable style="width: 100%">
                  <el-option v-for="(item, index) in priorityOptions" :key="index" :label="item.label"
                    :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="任务标题" prop="title">
                <el-input v-model="form.title" placeholder="请输入任务标题"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="任务详情" prop="content">
                <el-input type="textarea" :rows="3" placeholder="请输入任务详情" v-model="form.content"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <div>
                <el-form-item label="完成日期" prop="completionDate">
                  <el-date-picker v-model="form.completionDate" type="datetime" placeholder="选择日期时间"
                    value-format="yyyy-MM-dd HH:mm:ss" style="width: 100%"></el-date-picker>
                </el-form-item>
              </div>
            </el-col>
            <!-- <el-col :span="12">
                <el-form-item label="完成情况" prop="status">
                    <el-select v-model="form.status" placeholder="请选择完成情况" clearable style="width: 100%;">
                        <el-option v-for="(item, index) in statusOptions" :key="index" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
            </el-col> -->
            <el-col :span="12">
              <el-form-item label="参与部门" prop="deptIds">
                <el-select v-model="form.deptIds" filterable multiple placeholder="请选择参与部门" clearable
                  style="width: 100%" @change="selectDept">
                  <el-option v-for="(item, index) in deptOptions" :key="index" :label="item.deptName"
                    :value="String(item.deptId)" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="督办人" prop="supervise">
                <!-- <el-cascader v-model="form.supervise" :options="approvalsOptions"
                    :props="approvalsProps" filterable :show-all-levels="false" placeholder="请选择督办人"
                    @change="handleChange" style="width: 100%;"></el-cascader> -->
                <el-select v-model="form.supervise" filterable multiple placeholder="请选择督办人" clearable
                  style="width: 100%" disabled>
                  <el-option v-for="(item, index) in userList" :key="index" :label="item.nickName"
                    :value="String(item.userId)" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="上传附件" prop="attachment">
                <image-upload isRow :isShowTip="!form.attachment" :fileSize="1024" v-model="form.attachment" :file-type="fileType" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <el-button @click="addListingOpen = false" size="medium" style="width: 200px">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" size="medium" style="width: 200px">{{ form.id ? '修改待办事项' :
          '新增待办事项'
          }}</el-button>
      </div>
    </el-dialog>

    <!--新增待办弹窗-->
    <el-dialog v-dialogDragBox title="修改任务完成状态" :visible.sync="statusOpen" width="500px" class="custom-dialog">
      <div class="p20">
        <el-form ref="statusForm" :model="statusForm" :rules="statusRules" label-width="8em">
          <el-row>
            <el-col :span="24">
              <el-form-item label="完成情况" prop="status">
                <el-select v-model="statusForm.status" placeholder="请选择完成情况" clearable style="width: 100%">
                  <el-option v-for="(item, index) in statusOptions" :key="index" :label="item.label"
                    :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <el-button @click="statusOpen = false" size="medium" style="width: 200px">取 消</el-button>
        <el-button type="primary" @click="handleSubmitStatus" size="medium" style="width: 200px">确 定</el-button>
      </div>
    </el-dialog>

    <!--待办详情-->
    <el-dialog v-dialogDragBox title="任务详情" :visible.sync="viewOpen" width="1150px" class="custom-dialog">
      <div class="p20">
        <div class="view_box">
          <div class="view_detail">
            <el-descriptions title="" :column="2">
              <el-descriptions-item label="创建人">{{ info.user && info.user.realName || info.createBy }}</el-descriptions-item>
              <!-- <el-descriptions-item label="创建人">{{ info.createBy }}</el-descriptions-item> -->
              <el-descriptions-item label="标题">{{ info.title }}</el-descriptions-item>
              <el-descriptions-item label="参与部门">{{ info.deptNames }}</el-descriptions-item>
              <el-descriptions-item label="优先级" v-if="info.priority == 0">一般</el-descriptions-item>
              <el-descriptions-item label="优先级" v-if="info.priority == 1">加急</el-descriptions-item>
              <el-descriptions-item label="优先级" v-if="info.priority == 2">紧急</el-descriptions-item>
              <el-descriptions-item label="完成日期">{{ info.completionDate }}</el-descriptions-item>
              <el-descriptions-item label="督办人">{{ info.superviseName }}</el-descriptions-item>
              <el-descriptions-item label="任务详情">{{ info.content }}</el-descriptions-item>
              <el-descriptions-item label=""></el-descriptions-item>
              <el-descriptions-item label="附件">
                <div class="view_list_item_bottom_file">
                  <template v-for="(item, index) in info.attachment && formatArchives(info.attachment)">
                    <div class="view_list_item_bottom_file_pdf" :key="index" v-if="item.isPdf">
                      <img src="~@/assets/images/pdf.png" alt="" />
                      <div class="view_list_item_bottom_file_bg">
                        <span class="el-icon-view" @click="handlePDFPreview(item.url)"></span>
                        <span class="el-icon-download" @click="handleDownload(item.url)"></span>
                      </div>
                    </div>
                    <div class="view_list_item_bottom_file_img" :key="index" v-else>
                      <img :src="item.url" alt="" />
                      <div class="view_list_item_bottom_file_bg">
                        <span class="el-icon-view" @click="handlePictureCardPreview(item.url)"></span>
                      </div>
                    </div>
                  </template>
                </div>
              </el-descriptions-item>
            </el-descriptions>
          </div>
          <div class="view_list">
            <div class="view_list_title">任务动态</div>
            <div class="view_list_item" v-for="(item, index) in info.followUps" :key="index">
              <div class="view_list_item_top">
                <img :src="item.avatar_oss" alt="" />
                <span>{{ item.createBy }}</span>
                <span>{{ item.createTime }}</span>
              </div>
              <div class="view_list_item_bottom">
                <div class="view_list_item_bottom_text">{{ item.followInfo }}</div>
                <div class="view_list_item_bottom_file">
                  <template v-for="(item, index) in item.archives_oss && formatArchives(item.archives_oss)">
                    <div class="view_list_item_bottom_file_pdf" :key="index" v-if="item.isPdf">
                      <img src="~@/assets/images/pdf.png" alt="" />
                      <div class="view_list_item_bottom_file_bg">
                        <span class="el-icon-view" @click="handlePDFPreview(item.url)"></span>
                        <span class="el-icon-download" @click="handleDownload(item.url)"></span>
                      </div>
                    </div>
                    <div class="view_list_item_bottom_file_img" :key="index" v-else>
                      <img :src="item.url" alt="" />
                      <div class="view_list_item_bottom_file_bg">
                        <span class="el-icon-view" @click="handlePictureCardPreview(item.url)"></span>
                      </div>
                    </div>
                  </template>
                </div>
              </div>
            </div>
          </div>
        </div>
        <el-form ref="upForm" :model="upForm" :rules="upRules" label-width="8em">
          <el-row>
            <el-col :span="24">
              <el-form-item label="任务进度备注" prop="followInfo">
                <el-input type="textarea" :rows="3" placeholder="请输入任务进度描述备注" v-model="upForm.followInfo"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <div>
                <el-form-item label="上传附件" prop="archives">
                  <image-upload v-model="upForm.archives" :file-type="fileType" />
                </el-form-item>
              </div>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <el-button @click="viewOpen = false" size="medium" style="width: 200px">关 闭</el-button>
        <el-button type="primary" @click="handleSubmitUp" size="medium" style="width: 200px">提 交</el-button>
      </div>
    </el-dialog>

    <el-dialog v-dialogDragBox :visible.sync="imgOpen" title="预览" width="1150px" class="custom-dialog" append-to-body>
      <div style="padding: 0 20px">
        <img :src="imgUrl" style="display: block; max-width: 100%; margin: 0 auto" />
      </div>
    </el-dialog>

    <el-dialog v-dialogDragBox title="预览" :visible.sync="pdfOpen" width="1150px" class="custom-dialog" append-to-body>
      <div style="padding: 0 20px">
        <div style="display: flex; justify-content: space-between; align-items: center; padding-bottom: 10px"
          v-if="pdfCount > 1">
          <el-button type="primary" size="small" @click="pdfCurrent--" :disabled="pdfCurrent < 2">
            <i class="el-icon-arrow-left"></i>
            上一页
          </el-button>
          <span>{{ pdfCurrent }} / {{ pdfCount }}</span>
          <el-button type="primary" size="small" @click="pdfCurrent++" :disabled="pdfCurrent >= pdfCount">
            下一页
            <i class="el-icon-arrow-right"></i>
          </el-button>
        </div>
        <div class="page-pdf">
          <Pdf :src="pdfUrl" :page="pdfCurrent" @num-pages="pdfCount = $event" />
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { todoItemsAdd, todoItemsRevise, todoItemsDetail, todoItemsFollowup, todoItemsList, todoItemsStatus } from '@/api/todo'
import { listDept } from '@/api/system/dept'
import { deptTreeSelect, listUser } from '@/api/system/user'
import { parseTime } from '@/utils/ruoyi'
import { getConfigDetail } from '@/api/config'
// import { getUser } from '@/api/system/user'
import { checkPermi } from "@/utils/permission";

export default {
  name: 'Listing',
  data() {
    return {
      key: 1,
      // 搜索条件
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        title: undefined,
        status: 0,
        identity: ''
      },
      // 加载
      loading: true,
      // 列表数据
      list: [],
      // 总条数
      total: 0,
      // 显隐列
      columns: [
        { key: 0, label: `序号`, visible: true },
        { key: 1, label: `标题`, visible: true },
        { key: 2, label: `任务详细`, visible: true },
        { key: 3, label: `参与部门`, visible: true },
        { key: 4, label: `优先级`, visible: true },
        { key: 5, label: `督办`, visible: true },
        { key: 6, label: `完成时间`, visible: true },
        { key: 7, label: `完成状态`, visible: true },
        { key: 8, label: `创建时间`, visible: true }
      ],
      addListingOpen: false,
      // 状态
      statusOptions: [
        {
          value: 0,
          label: '未完成'
        },
        {
          value: 1,
          label: '已完成'
        }
      ],
      // 身份
      identityList: [
        {
          value: 'creator',
          label: '创建'
        },
        {
          value: 'supervise',
          label: '督办'
        }
      ],
      form: {},
      rules: {
        priority: [{ required: true, message: '请选择任务优先级', trigger: 'change' }],
        title: [{ required: true, message: '请输入待办任务标题', trigger: 'blur' }],
        // status: [{ required: true, message: '请选择完成类型', trigger: 'change' }],
        completionDate: [{ required: true, message: '请选择完成日期', trigger: 'change' }],
        content: [{ required: true, message: '请输入待办任务详情', trigger: 'blur' }],
        deptIds: [{ required: true, message: '请选择参与部门', trigger: ['blur', 'change'] }],
        supervise: [{ required: true, message: '请选择督办人', trigger: 'change' }]
      },
      approvalsOptions: [],
      approvalsProps: {
        expandTrigger: 'hover',
        emitPath: false,
        multiple: true
      },
      deptOptions: [],
      priorityOptions: [
        {
          value: 0,
          label: '一般'
        },
        {
          value: 1,
          label: '加急'
        },
        {
          value: 2,
          label: '紧急'
        }
      ],
      viewOpen: false,
      upForm: {
        followInfo: undefined,
        archives: undefined,
        todoItemId: undefined
      },
      upRules: {
        followInfo: [{ required: true, message: '请输入任务进度描述备注', trigger: 'blur' }]
      },
      fileType: ['png', 'PNG', 'jpg', 'JPG', 'jpeg', 'JPEG', 'pdf'],
      userList: [],
      statusForm: {
        status: undefined,
        itemId: undefined
      },
      statusRules: {
        status: [{ required: true, message: '请选择完成类型', trigger: 'change' }]
      },
      statusOpen: false,
      info: {},
      userInfo: {},
      supplierConfig: {},
      // pdf预览
      pdfOpen: false,
      pdfCurrent: 1,
      pdfCount: 0,
      pdfUrl: '',
      imgOpen: false,
      imgUrl: ''
    }
  },
  created() {
    if(checkPermi(['oa:todo:delete'])) {
      this.statusOptions.push({
        value: -10,
        label: '已删除'
      })
    }
    this.userInfo = this.$store.state.user.info
    this.getApprovalsOptions()
    this.getConfig()
  },
  methods: {
    checkPermi,
    parseTime,
    getConfig() {
      getConfigDetail({ configKey: 'todo.items.supervise' }).then(res => {
        this.supplierConfig = res.data
        if (this.supplierConfig.configValue) {
            this.queryParams.identity = this.supplierConfig.configValue.split(',').find(item => item == this.userInfo.userId) ? 'supervise' : ''
        }
        this.getList()
      })
    },
    // 部门和人员
    async getApprovalsOptions() {
      const deptList = await listDept()
      this.deptOptions = deptList.data
      const dept = await deptTreeSelect()
      const user = await listUser()
      const children = dept.data[0].children || []
      const deptData = [...children, ...[{ id: -1, label: '其他', value: -1 }]] || []
      const userData = user.rows || []
      this.userList = userData
      const getChildren = data => {
        data.forEach(item => {
          item.value = item.id
          if (item.children) {
            getChildren(item.children)
          } else {
            item.children = []
          }
        })
      }
      getChildren(deptData)
      const addChildren = data => {
        data.forEach(item => {
          userData.forEach(user => {
            if (item.id === user.deptId && item.children) {
              item.children.push({
                id: user.userId,
                label: user.realName || user.nickName,
                value: user.userId,
                disabled: user.status == '1',
                userName: user.userName
              })
            }
            if (item.id === -1 && (!user.deptId || user.deptId === dept.data[0].id) && item.children) {
              item.children.push({
                id: user.userId,
                label: user.realName || user.nickName,
                value: user.userId,
                disabled: user.status == '1',
                userName: user.userName
              })
            }
          })
          if (item.children && item.children.length) {
            addChildren(item.children)
          }
        })
      }
      addChildren(deptData)
      this.approvalsOptions = deptData
    },
    // 查询数据
    async getList() {
      this.loading = true
      // this.queryParams.identity = 'supervise'
      const query = { ...this.queryParams }
      const res = await todoItemsList(query)
      if (res.code === 200) {
        this.list = res.rows
        this.loading = false
        this.total = res.total
        this.key = Math.random()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 刷新列表
    refreshList() {
      todoItemsList(this.queryParams).then(res => {
        const { code, msg, rows, total } = res
        if (code === 200) {
          this.$set(this, 'list', rows)
          this.$set(this, 'total', total)
        } else this.$message.error(msg)
      })
    },
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 身份切换
    handleIdentity(item) {
      this.queryParams.identity = item.value
      this.handleQuery()
    },
    handleChange() { },
    // 新增
    handleAdd() {
      this.form = {
        priority: undefined,
        title: '',
        content: '',
        completionDate: undefined,
        status: undefined,
        deptIds: [],
        supervise: undefined,
        depts: [],
        supervises: [],
        attachment: ''
      }
      this.resetForm('form')
      this.form.supervise = this.supplierConfig.configValue && this.supplierConfig.configValue.split(',')
      this.form.supervise.forEach(el => {
        this.form.supervises.push({
          supervise: this.userList.find(item => item.userId == el).userId,
          superviseName: this.userList.find(item => item.userId == el).realName || this.userList.find(item => item.userId == el).nickName
        })
      })
      this.addListingOpen = true
    },
    // 修改
    handleUpdate(row) {
      todoItemsDetail({
        todoItemId: row.id
      }).then(res => {
        if (res.code == 200) {
          res.data.deptIds = res.data.deptIds && res.data.deptIds.split(',')
          res.data.supervise = res.data.supervise && res.data.supervise.split(',')
          res.data.supervises = []
          res.data.supervise.forEach(el => {
            res.data.supervises.push({
              supervise: this.userList.find(item => item.userId == el).userId,
              superviseName: this.userList.find(item => item.userId == el).nickName
            })
          })
          this.form = res.data
          this.addListingOpen = true
        }
      })
    },
    // 删除
    handleDelete(row) {
      this.$confirm('确定要删除该挂牌督办吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        todoItemsStatus({ itemId: row.id, status: -10 }).then(res => {
          if (res.code === 200) {
            this.$message.success('删除成功')
            this.getList()
          } else {
            this.$message.error(res.msg)
          }
        })
      }).catch(() => {})
    },
    // 修改状态
    handleUpStatus(row) {
      this.statusForm.itemId = row.id
      this.statusForm.status = row.status
      this.statusOpen = true
    },
    // 查看详情
    handleViwe(row) {
      todoItemsDetail({
        todoItemId: row.id
      }).then(res => {
        if (res.code == 200) {
          this.upForm = {
            followInfo: undefined,
            archives: undefined,
            todoItemId: undefined
          }
          if (res.data.attachment) {
            let arr = []
            res.data.attachment.split(',').forEach(m => {
              if (!m.includes('http')) {
                m = this.imgPath + m
              }
              arr.push(m)
            })
            res.data.attachment = arr.join(',')
          }
          this.info = res.data
          this.viewOpen = true
        }
      })
    },
    // 选择部门
    selectDept() {
      this.form.depts = []
      this.form.deptIds.forEach(el => {
        let obj = {
          deptId: this.deptOptions.find(item => item.deptId == el).deptId,
          deptName: this.deptOptions.find(item => item.deptId == el).deptName
        }
        this.form.depts.push(obj)
      })
    },
    // 提交
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          if (this.form.id) {
            let data = {
              priority: this.form.priority,
              title: this.form.title,
              content: this.form.content,
              completionDate: this.form.completionDate,
              status: this.form.status,
              depts: this.form.depts,
              supervises: this.form.supervises,
              todoItemId: this.form.id,
              attachment: this.form.attachment
            }
            todoItemsRevise(data).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('成功修改挂牌督办')
                this.getList()
                this.addListingOpen = false
              } else this.$message.error(msg)
            })
          } else {
            let data = {
              priority: this.form.priority,
              title: this.form.title,
              content: this.form.content,
              completionDate: this.form.completionDate,
              status: this.form.status,
              depts: this.form.depts,
              supervises: this.form.supervises,
              attachment: this.form.attachment
            }
            todoItemsAdd(data).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('成功新建挂牌督办')
                this.getList()
                this.addListingOpen = false
              } else this.$message.error(msg)
            })
          }
        }
      })
    },
    handleSubmitStatus() {
      this.$refs.statusForm.validate(valid => {
        if (valid) {
          if (this.statusForm.itemId) {
            todoItemsStatus(this.statusForm).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('成功修改完成状态')
                this.getList()
                this.statusOpen = false
              } else this.$message.error(msg)
            })
          }
        }
      })
    },
    handleSubmitUp() {
      this.$refs.upForm.validate(valid => {
        if (valid) {
          this.upForm.todoItemId = this.info.id
          if (this.upForm.todoItemId) {
            todoItemsFollowup(this.upForm).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('成功提交跟进进度')
                this.getList()
                this.viewOpen = false
              } else this.$message.error(msg)
            })
          }
        }
      })
    },
    // prettier-ignore
    handleStatusChange(row, val) {
      const title = val === 0 ? '确定要修改完成状态为未完成吗?' : '确定已经完成该任务了吗?'
      this.$confirm(title, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        todoItemsStatus({ status: val ? 1 : 0, itemId: row.id }).then(res => {
          if (res.code === 200) {
            this.$message.success('修改成功')
            this.refreshList()
          } else {
            this.$message.error(res.msg)
          }
        })
      }).catch(() => {
        row.status = val ? 0 : 1
      })
    },
    // 格式化附件内容，判断是否为pdf
    formatArchives(archives) {
      const arr = archives && archives.split(',')
      return arr.map(item => {
        return {
          url: item,
          isPdf: item.includes('.pdf')
        }
      })
    },
    // pdf预览
    handlePDFPreview(url) {
      this.pdfUrl = url
      this.pdfOpen = true
    },
    // 图片预览
    handlePictureCardPreview(url) {
      this.imgUrl = url
      this.imgOpen = true
    },
    // 下载
    handleDownload(item) {
      const filename = item && item.split('/').pop().split('?')[0]
      const xhr = new XMLHttpRequest()
      xhr.open('GET', item, true)
      xhr.responseType = 'blob'
      xhr.onload = () => {
        if (xhr.status === 200) {
          const blob = new Blob([xhr.response], { type: 'application/octet-stream' })
          const url = URL.createObjectURL(blob)
          const link = document.createElement('a')
          link.href = url
          link.download = filename
          link.click()
          URL.revokeObjectURL(url)
        }
      }
      xhr.send()
    },
    // 判断文件类型
    typeFormat(file) {
      const index = file.lastIndexOf('?')
      if (index !== -1) file = file.slice(0, index)
      const index1 = file.lastIndexOf('.')
      return file.substr(index1 + 1)
    },
    fileFormat(info) {
      if (!info) return []
      const arr = info.split(',') || []
      return arr.map(item => {
        return this.imgPath + item
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';

.tableBox {
  padding: 20px;
}

.custom-search {
  align-items: center;
  justify-content: space-between;
}

.custom-search-tip {
  background-color: #fef0f0;
  color: #f56c6c;
  padding: 10px 20px;
  font-size: 20px;
  display: inline-flex;
  align-items: center;

  span {
    font-size: 14px;
    margin-left: 10px;
    margin-right: 30px;
  }
}

.p20 {
  padding: 0 20px;
}

.finishTime_box {
  display: flex;
  align-items: center;
  justify-content: center;

  .time {
    margin-right: 7px;
  }

  .tips {
    flex-shrink: 0;
    width: 83px;
    background: url('~@/assets/images/listing_tips_bg.png') center no-repeat;
    background-size: 82.5px 22px;
    font-weight: 500;
    font-size: 12px;
    color: #f50e0e;
    display: flex;
    align-items: center;
    justify-content: center;

    img {
      width: 20px;
      height: 20px;
      margin-right: 7px;
    }
  }
}

.view_box {
  margin-bottom: 20px;
  border-radius: 5px;
  border: 1px solid #cbd6e2;

  .view_detail {
    background: #ffffff;
    border-radius: 5px;
    border-bottom: 1px solid #cbd6e2;
    padding: 10px 20px;
    box-sizing: border-box;

    .view_list_item_bottom {
      padding: 0 48px 10px;
      box-sizing: border-box;

      &_text {
        font-weight: 400;
        font-size: 14px;
        color: #333333;
        margin-bottom: 18px;
      }

      &_file {
        display: flex;
        flex-wrap: wrap;
        align-items: center;

        &_pdf,
        &_img {
          width: 145px;
          height: 145px;
          margin-right: 10px;
          position: relative;
          border-radius: 5px;
          overflow: hidden;

          img {
            width: 100%;
            height: 100%;
          }

          &:hover {
            .view_list_item_bottom_file_bg {
              display: flex;
            }
          }
        }

        &_bg {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: rgba(0, 0, 0, 0.5);
          display: none;
          align-items: center;
          justify-content: center;

          span {
            cursor: pointer;
            font-size: 20px;
            margin: 0 10px;
            color: #ffffff;
          }
        }
      }
    }
  }

  .view_list {
    background: #f0f3f9;
    padding: 10px 20px;
    box-sizing: border-box;

    .view_list_title {
      font-weight: 500;
      font-size: 16px;
      color: #666666;
      line-height: 16px;
      margin-top: 5px;
    }

    .view_list_item {
      margin-top: 20px;
      border-bottom: 1px solid #9fa3b2;

      .view_list_item_top {
        display: flex;
        align-items: center;
        margin-bottom: 12px;

        img {
          width: 38px;
          height: 38px;
          margin-right: 10px;
        }

        span {
          &:nth-child(2) {
            font-weight: 500;
            font-size: 12px;
            color: #666666;
            margin-right: 50px;
          }

          &:nth-child(3) {
            font-weight: 400;
            font-size: 12px;
            color: #999999;
          }
        }
      }

      .view_list_item_bottom {
        padding: 0 48px 10px;
        box-sizing: border-box;

        &_text {
          font-weight: 400;
          font-size: 14px;
          color: #333333;
          margin-bottom: 18px;
        }

        &_file {
          display: flex;
          flex-wrap: wrap;
          align-items: center;

          &_pdf,
          &_img {
            width: 145px;
            height: 145px;
            margin-right: 10px;
            position: relative;
            border-radius: 5px;
            overflow: hidden;

            img {
              width: 100%;
              height: 100%;
            }

            &:hover {
              .view_list_item_bottom_file_bg {
                display: flex;
              }
            }
          }

          &_bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            align-items: center;
            justify-content: center;

            span {
              cursor: pointer;
              font-size: 20px;
              margin: 0 10px;
              color: #ffffff;
            }
          }
        }
      }
    }
  }
}

.custom-table ::v-deep {
  .el-table__body-wrapper .table-switch {
    .el-switch__label {
      width: calc(3em + 20px) !important;
    }

    .el-switch__core {
      width: calc(3em + 20px) !important;
    }
  }
}
</style>
