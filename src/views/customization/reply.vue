<template>
  <div>
    <el-dialog v-dialogDragBox title="查看报价" :visible.sync="open" width="1150px" class="custom-dialog offer-dialog">
      <template v-if="list.length">
        <div class="offer-change">
          <span class="option-title" style="margin-left: 15px">回复时间</span>
          <span class="option-item" :class="{ active: !isAsc }" @click="handleOrderBy(false)">降序</span>
          <span class="option-item" :class="{ active: isAsc }" @click="handleOrderBy(true)">升序</span>
        </div>
        <el-table ref="list" stripe :data="list" row-key="id" style="width: 100%" class="custom-table">
          <el-table-column align="center" label="供应商" show-overflow-tooltip min-width="130">
            <template slot-scope="{ row }" v-if="hasOwnProperty(row, 'companyDO') && hasOwnProperty(row.companyDO, 'companyName')">
              <span class="table-link" v-if="hasOwnProperty(row, 'companyDO') && row.companyDO.companyId != -1" @click="handleViewSupplier(row.companyDO)">{{ row.companyDO.companyName }}</span>
              <span v-else>{{ row.companyDO.companyName }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="sendUser" label="联系人" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="sendPhone" label="联系电话" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="amount" label="产品报价" show-overflow-tooltip>
            <template slot-scope="{ row }">
              <span class="table-orange">{{ row.amount ? '￥' + row.amount + '元' : '' }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="replyTime" label="报价时间" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="remark" label="报价备注" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="maxNum" label="可供应数量" show-overflow-tooltip>
            <template slot-scope="{ row }">{{ row.maxNum === 2147483647 || !row.maxNum ? '不限量' : row.maxNum }}</template>
          </el-table-column>
        </el-table>
      </template>
      <el-empty :description="!list.length && !loading ? '暂无数据' : '加载中…'" v-else />
      <div slot="footer" v-if="!loading">
        <button type="button" class="custom-dialog-btn primary" @click="open = false">关闭</button>
      </div>
    </el-dialog>
    <!-- 供应商详情 -->
    <supplier-dialog ref="supplier"></supplier-dialog>
  </div>
</template>

<script>
import { getReply } from '@/api/customization'
import supplierDialog from '@/views/purchase/demandForMe/supplier'

export default {
  components: { supplierDialog },
  data() {
    return {
      customInfo: {},
      loading: true,
      open: false,
      list: [],
      isAsc: false
    }
  },
  methods: {
    hasOwnProperty(obj, key) {
      return Object.prototype.hasOwnProperty.call(obj, key)
    },
    init(data = {}) {
      this.open = true
      this.customInfo = { ...data }
      this.loading = true
      getReply({ demandId: data.listid, isAsc: this.isAsc }).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          this.list = data
          this.loading = false
        } else this.$message.error(msg)
      })
    },
    handleOrderBy(isAsc) {
      this.isAsc = isAsc
      this.init(this.customInfo)
    },
    // 查看供应商详情
    handleViewSupplier(row) {
      const id = row.companyId
      this.$refs.supplier.getInfo(id, 'common')
    }
  }
}
</script>

<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';
.offer-dialog ::v-deep {
  .el-dialog__body {
    padding: 0 20px !important;
  }
  .offer-change {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 15px 0;
    line-height: 28px;
    .option-title {
      font-size: 12px;
      color: #999999;
    }
    .option-item {
      display: inline-block;
      color: $info;
      border: 1px solid #cbd6e2;
      border-radius: 5px;
      margin-left: 15px;
      padding: 0 15px;
      cursor: pointer;
      &:hover,
      &.active {
        background-color: $blue;
        color: $white;
        border-color: $blue;
      }
    }
  }
}
</style>
