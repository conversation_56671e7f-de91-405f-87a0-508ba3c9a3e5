<template>
  <div>
    <el-dialog v-dialogDragBox :title="title" :visible.sync="open" width="1150px" :before-close="close" class="custom-dialog"
      append-to-body lock-scroll>
      <div class="formBox" v-if="!isLike">
        <el-form ref="form" :model="form" :rules="rules" label-width="6em">
          <el-row :gutter="10">
            <el-col :span="12">
              <el-form-item label="产品名称" prop="productName">
                <el-input v-model="form.productName" placeholder="请输入产品名称"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="产品分类" prop="categoryId">
                <treeselect v-model="form.categoryId" :options="categoryList" :normalizer="normalizer" :show-count="true"
                  placeholder="请选择产品分类">
                  <div slot="option-label" slot-scope="{ node }" class="category-flex">
                    <span>{{ node.label }}</span>
                    <span class="category-flex-desc">{{ node.raw.model ? `规格型号：${node.raw.model}` : '' }}</span>
                  </div>
                </treeselect>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="产品规格" prop="specs">
                <el-input v-model="form.specs" placeholder="请输入产品规格"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="产品型号" prop="model">
                <el-input v-model="form.model" placeholder="请输入产品型号"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="产品材质" prop="materialQuality">
                <el-input v-model="form.materialQuality" placeholder="请输入产品材质"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="表面处理" prop="surface">
                <el-input v-model="form.surface" placeholder="请输入表面处理"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="产品单位" prop="unit">
                <el-select v-model="form.unit" filterable allow-create default-first-option placeholder="请选择产品单位"
                  style="width: 100%">
                  <el-option v-for="(item, index) in unitOptions" :key="index" :label="item" :value="item"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="产品重量" prop="weight">
                <el-input v-model="form.weight" placeholder="请输入产品重量">
                  <span slot="suffix" style="padding-right: 5px">Kg</span>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="定制数量" prop="quantity" v-if="addsource === 'outer' || updateSoucrce === 'demand'">
                <el-input v-model="form.quantity" placeholder="请输入定制数量">
                  <span slot="suffix" style="padding-right: 5px">{{ form.unit || '' }}</span>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24"></el-col>
            <el-col :span="12">
              <el-form-item label="产品图片" prop="picture">
                <image-upload :compress="false" :limit="1" isRow v-model="form.picture" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="产品图纸" prop="draw">
                <file-upload v-model="form.draw" :limit="1" :file-type="['pdf']" />
              </el-form-item>
            </el-col>
            <el-col :span="24"></el-col>
            <el-col :span="12">
              <el-form-item label="供货截止" prop="supplyEndTime" v-if="addsource === 'outer'">
                <el-date-picker v-model="form.supplyEndTime" type="date" value-format="yyyy-MM-dd" placeholder="请选择供货截止日期"
                  style="width: 100%" :picker-options="endDatePicker"></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="有效时间" prop="expireTime" v-if="addsource === 'outer' || updateSoucrce === 'demand'">
                <el-input v-model="form.expireTime" placeholder="请输入有效时间">
                  <span slot="suffix" style="padding-right: 5px">小时</span>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="联系电话" prop="phone" v-if="addsource === 'outer' || updateSoucrce === 'demand'">
                <el-input v-model="form.phone" placeholder="请输入联系电话"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div class="formBox" v-if="isLike">
        <div class="likeProductTitle">产品信息</div>
        <div class="likeProductInfo">
          <el-image :src="form.picture_oss || imgPath + form.picture" lazy class="likeProductInfoImg"></el-image>
          <div class="likeProductInfoBox">
            <div class="likeProductInfoItem">
              <span>产品名称</span>
              <b>{{ form.productName }}</b>
            </div>
            <div class="likeProductInfoItem">
              <span>产品规格</span>
              <b>{{ form.specs }}</b>
            </div>
            <div class="likeProductInfoItem">
              <span>产品型号</span>
              <b>{{ form.model }}</b>
            </div>
            <div class="likeProductInfoItem">
              <span>产品材质</span>
              <b>{{ form.materialQuality }}</b>
            </div>
            <div class="likeProductInfoItem">
              <span>表面处理</span>
              <b>{{ form.surface }}</b>
            </div>
            <div class="likeProductInfoItem">
              <span>产品单位</span>
              <b>{{ form.unit }}</b>
            </div>
            <div class="likeProductInfoItem">
              <span>产品重量</span>
              <b>{{ form.weight ? form.weight + 'Kg' : '' }}</b>
            </div>
          </div>
        </div>
        <el-form ref="form" :model="form" :rules="rules" label-width="6em">
          <el-row :gutter="10">
            <el-col :span="12">
              <el-form-item label="定制数量" prop="quantity">
                <el-input v-model="form.quantity" placeholder="请输入定制数量">
                  <span slot="suffix" style="padding-right: 5px">{{ form.unit || '' }}</span>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="联系电话" prop="phone">
                <el-input v-model="form.phone" placeholder="请输入联系电话"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="供货截止" prop="supplyEndTime">
                <el-date-picker v-model="form.supplyEndTime" type="date" value-format="yyyy-MM-dd" placeholder="请选择供货截止日期"
                  style="width: 100%" :picker-options="endDatePicker"></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="有效时间" prop="expireTime">
                <el-input v-model="form.expireTime" placeholder="请输入有效时间">
                  <span slot="suffix" style="padding-right: 5px">小时</span>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <template v-if="!isLike">
          <el-button class="custom-dialog-btn" @click="close">取消</el-button>
          <el-button class="custom-dialog-btn primary" @click="submit">确定</el-button>
        </template>
        <template v-if="isLike">
          <el-button class="custom-dialog-btn" @click="close">取消</el-button>
          <el-button class="custom-dialog-btn primary" @click="submitLike">提交定制</el-button>
        </template>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import { getlist } from '@/api/purchase/dysplasia'
import * as category from '@/api/purchase/category'
import { getToken } from '@/utils/auth'
import { addCustomization, updateCustomization, addCustomizationDemand, updateCustomizationDemand, addCustomizationSys, updateCustomizationSys } from '@/api/customization'
import { isNumber, isNumberLength } from '@/utils/validate'

export default {
  props: {
    isRefresh: {
      type: Boolean,
      default: false
    },
    addsource: {
      type: String,
      default: ''
    },
  },
  components: { Treeselect },
  data() {
    return {
      open: false,
      title: '',
      form: {},
      rules: {
        productName: [{ required: true, message: '请输入产品名称', trigger: 'blur' }],
        // categoryId: [{ required: true, message: '请选择产品分类', trigger: ['blur', 'change'] }],
        // specs: [{ required: true, message: '请输入产品规格', trigger: 'blur' }],
        // model: [{ required: true, message: '请输入产品型号', trigger: 'blur' }],
        // materialQuality: [{ required: true, message: '请输入产品材质', trigger: 'blur' }],
        // surface: [{ required: true, message: '请输入表面处理', trigger: 'blur' }],
        weight: [
          { validator: isNumber, message: '产品重量输入有误请重新输入', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 5), message: '最多可以填写五位小数', trigger: 'blur' }
        ],
        quantity: [
          { required: true, message: '请输入定制数量', trigger: 'blur' },
          { validator: isNumber, message: '定制数量输入有误请重新输入', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 5), message: '最多可以填写五位小数', trigger: 'blur' }
        ],
        // picture: [{ required: true, message: '请上传产品图片', trigger: ['blur', 'change'] }],
        // draw: [{ required: true, message: '请上传产品图纸', trigger: ['blur', 'change'] }],
        // supplyEndTime: [{ required: true, message: '请选择供货截止日期', trigger: ['blur', 'change'] }],
        expireTime: [
          { required: true, message: '请输入有效时间', trigger: 'blur' },
          { validator: isNumber, message: '有效时间输入有误请重新输入', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 2), message: '最多可以填写两位小数', trigger: 'blur' }
        ],
        phone: [
          { required: true, message: '请输入联系电话', trigger: 'blur' },
          { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
        ]
      },
      categoryList: [],
      unitOptions: ['吨', '千克', '个', '件', '套', '米', '支', '根', '袋', '托'],
      endDatePicker: {
        disabledDate: time => {
          return time.getTime() < new Date().getTime()
        }
      },
      isLike: false,
      updateSoucrce: ''
    }
  },
  created() {
    if (!!getToken()) this.getCategoryList()
  },
  methods: {
    // 获取产品分类
    getCategoryList() {
      if (this.addsource === 'outer' || this.updateSoucrce === 'demand') {
        category.getlist({ pageNum: 1, pageSize: 999 }).then(res => {
          this.categoryList = res.data
        })
      } else if (this.addsource === 'sys' || this.updateSoucrce === 'sys') {
        getlist({ pageNum: 1, pageSize: 999 }).then(res => {
          this.categoryList = res.data
        })
      }

    },
    // 格式化产品分类
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children
      }
      return {
        id: node.id,
        label: node.name,
        model: node.model,
        children: node.children
      }
    },
    // 重置表单
    reset() {
      const phone = this.$store.state.user.info.phonenumber || ''
      this.form = {
        categoryId: undefined,
        demandId: undefined,
        draw: undefined,
        expireTime: 48,
        materialQuality: undefined,
        model: undefined,
        phone,
        picture: undefined,
        productName: undefined,
        quantity: undefined,
        specs: undefined,
        supplyEndTime: undefined,
        surface: undefined,
        unit: undefined,
        weight: undefined
      }
      this.resetForm('form')
    },
    // 新增
    create(data = {}) {
      this.reset()
      if (JSON.stringify(data) !== '{}') {
        this.form = { ...this.form, ...data }
        this.form.categoryId = null
        this.title = '定制类似产品'
      } else this.title = '新增产品定制'
      this.isLike = false
      this.open = true
    },
    // 新增类似
    createLike(info) {
      this.reset()
      let form = { ...info }
      form.id = undefined
      this.form = { ...this.form, ...form }
      this.isLike = true
      this.title = '定制相同产品'
      this.open = true
    },
    // 修改
    update(data = {}, updateSoucrce = '') {
      this.reset()
      this.form = { ...this.form, ...data }
      this.form.expireTime = 48
      if (updateSoucrce == 'sys') {
        this.form.categoryId = this.form.dysCategoryId
      }
      this.isLike = data.productSource === 'quote'
      this.title = '修改产品定制'
      this.updateSoucrce = updateSoucrce
      this.getCategoryList()
      this.open = true
    },
    // 提交表单
    submit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          if (this.addsource === 'outer' || this.updateSoucrce === 'demand') {
            let { categoryId, listid, draw, expireTime, materialQuality, model, phone, picture, productName, quantity, specs, supplyEndTime, surface, unit, weight } = this.form
            expireTime = new Date().getTime() + expireTime * 60 * 60 * 1000
            const demandId = listid
            const data = {
              categoryId,
              demandId,
              draw,
              expireTime: this.parseTime(expireTime, '{y}-{m}-{d} {h}:{i}:{s}'),
              materialQuality,
              model,
              phone,
              picture,
              productName,
              quantity,
              specs,
              supplyEndTime: supplyEndTime ? supplyEndTime + ' 23:59:59' : '',
              surface,
              unit,
              weight
            }
            if (!!demandId) {
              updateCustomization(data).then(res => {
                this.$message.success('修改成功')
                this.close()
                if (this.isRefresh) this.$emit('refresh')
              })
            } else {
              addCustomization(data).then(res => {
                this.$message.success('新增成功')
                this.close()
                if (this.isRefresh) this.$emit('refresh')
              })
            }
          } else if (this.addsource === 'sys' || this.updateSoucrce === 'sys') {
            let { categoryId, listid, draw, isOpen, materialQuality, model, picture, productName, specs, surface, unit, weight } = this.form
            const productId = listid
            const data = {
              categoryId,
              draw,
              isOpen,
              materialQuality,
              model,
              picture,
              productId,
              productName,
              specs,
              surface,
              unit,
              weight
            }
            if (!!productId) {
              updateCustomizationSys(data).then(res => {
                this.$message.success('修改成功')
                this.close()
                if (this.isRefresh) this.$emit('refresh')
              })
            } else {
              addCustomizationSys(data).then(res => {
                this.$message.success('新增成功')
                this.close()
                if (this.isRefresh) this.$emit('refresh')
              })
            }
          }
        }
      })
    },
    // 提交表单
    submitLike() {
      this.$refs.form.validate(valid => {
        if (valid) {
          let { expireTime, listid, phone, productId, quantity, supplyEndTime } = this.form
          expireTime = new Date().getTime() + expireTime * 60 * 60 * 1000
          const demandId = listid
          const data = {
            demandId,
            expireTime: this.parseTime(expireTime, '{y}-{m}-{d} {h}:{i}:{s}'),
            phone,
            productId,
            quantity,
            supplyEndTime: supplyEndTime ? supplyEndTime + ' 23:59:59' : ''
          }
          if (!!demandId) {
            updateCustomizationDemand(data).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('修改成功')
                this.close()
                if (this.isRefresh) this.$emit('refresh')
              } else this.$message.error(msg)
            })
          } else {
            addCustomizationDemand(data).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('定制成功')
                this.close()
              } else this.$message.error(msg)
            })
          }
        }
      })
    },
    // 关闭弹窗
    close() {
      this.open = false
      this.reset()
    }
  }
}
</script>
<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';

.hover {
  transition: 0.2s;
  cursor: pointer;

  &:hover {
    transform: scale(1) !important;
  }
}

::v-deep {

  .vue-treeselect,
  .vue-treeselect__placeholder,
  .vue-treeselect__single-value {
    line-height: 38px;
  }

  .category-flex {
    display: flex;
    align-items: center;

    &-desc {
      font-size: 12px;
      color: #999999;
      margin-left: 10px;
    }
  }
}

.formBox {
  padding: 0 20px;
}

.likeProductTitle {
  font-size: 14px;
  line-height: 20px;
  margin-bottom: 10px;
}

.likeProductInfo {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 10px;
  background-color: #f8f9fb;
  border-radius: 5px;
  padding: 15px;

  &Img {
    width: 126px;
    height: 126px;
    border: 1px solid #d9d9d9;
    border-radius: 5px;
    margin-right: 15px;
    flex-shrink: 0;
  }

  &Box {
    width: 100%;
    display: flex;
    align-items: flex-start;
    flex-wrap: wrap;
  }

  &Item {
    display: flex;
    width: 33.33%;
    padding: 10px 10px 10px 0;
    line-height: 20px;
    align-items: flex-start;
    font-size: 14px;

    span {
      display: inline-flex;
      flex-shrink: 0;
      width: 70px;
      color: #999999;
    }

    b {
      font-weight: 500;
      color: #666666;
    }
  }
}
</style>
