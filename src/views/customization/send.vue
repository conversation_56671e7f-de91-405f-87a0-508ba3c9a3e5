<template>
  <div>
    <el-dialog v-dialogDragBox title="产品定制详情" :visible.sync="open" width="1150px" class="custom-dialog" :before-close="handleClose">
      <div class="info">
        <div class="info-title">定制需求详情</div>
        <div class="info-con">
          <el-row :gutter="10">
            <el-col :span="8">
              <span>有效时间：</span>
              <b><Countdown :expireTime="info.expireTime" pattern="{d}{h}{m}" /></b>
            </el-col>
            <el-col :span="8">
              <span>需求方：</span>
              <b class="link pointer" v-if="info.companyDO && info.companyDO.companyId != -1" @click="handleViewSupplier(info.companyDO)">{{ info.companyDO && info.companyDO.companyName }}</b>
              <b v-else>{{ info.companyDO && info.companyDO.companyName }}</b>
            </el-col>
            <el-col :span="8">
              <span>定制数量：</span>
              <b>{{ info.quantity + '' + (info.unit || '') }}</b>
            </el-col>
            <el-col :span="8">
              <span>创建时间：</span>
              <b>{{ info.createTime }}</b>
            </el-col>
            <el-col :span="8">
              <span>供货截止：</span>
              <b>{{ info.supplyEndTime }}</b>
            </el-col>
          </el-row>
        </div>
        <div class="info-title">定制产品详情</div>
        <div class="info-product">
          <div class="info-product-image">
            <el-image :src="info.image" fit="contain" style="width: 100%" :preview-src-list="[info.image]"></el-image>
          </div>
          <div class="info-product-con">
            <el-row :gutter="10">
              <el-col :span="8">
                <span>产品名称：</span>
                <b>{{ info.productName }}</b>
              </el-col>
              <el-col :span="8">
                <span>产品材质：</span>
                <b>{{ info.materialQuality }}</b>
              </el-col>
              <el-col :span="8">
                <span>产品单位：</span>
                <b>{{ info.unit }}</b>
              </el-col>
              <el-col :span="8">
                <span>产品规格：</span>
                <b>{{ info.specs }}</b>
              </el-col>
              <el-col :span="8">
                <span>产品型号：</span>
                <b>{{ info.model }}</b>
              </el-col>
              <el-col :span="8">
                <span>产品重量：</span>
                <b>{{ info.weight ? `${info.weight}Kg` : '' }}</b>
              </el-col>
            </el-row>
          </div>
        </div>
        <div class="info-supplier">
          <div class="info-supplier-title">供应商</div>
          <div class="hasChecked-info">
            <el-tooltip effect="dark" :content="item.businessScope" :disabled="!item.businessScope" placement="top" v-for="item in commonChecked" :key="item.id">
              <div class="hasChecked-item">
                <span>{{ item.companyName }}</span>
                <i class="el-icon-close" @click="handleDeleteCommon(item)"></i>
              </div>
            </el-tooltip>
            <div class="hasChecked-more" @click="handleOpenCommon">
              <i class="el-icon-plus"></i>
              <span>{{ commonChecked.length ? '查看更多' : '选择供应商' }}</span>
            </div>
          </div>
        </div>
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn" @click="handleClose">取消</el-button>
        <el-button class="custom-dialog-btn primary" :class="{ disabled: !commonChecked.length }" :disabled="!commonChecked.length" @click="handleSubmit">确定</el-button>
      </div>
    </el-dialog>

    <el-dialog v-dialogDragBox title="更多供应商" :visible.sync="commonOpen" width="1150px" class="custom-dialog">
      <template v-if="!!commonChecked.length">
        <div class="hasChecked-title">已选择供应商</div>
        <div class="hasChecked-info more">
          <el-tooltip effect="dark" :content="item.businessScope" :disabled="!item.businessScope" placement="top" v-for="item in commonChecked" :key="item.id">
            <div class="hasChecked-item">
              {{ item.companyName }}
              <i class="el-icon-close" @click="handleDeleteCommon(item)"></i>
            </div>
          </el-tooltip>
        </div>
      </template>
      <div style="padding: 0 20px">
        <div class="hasChecked-search">
          <el-input v-model="commonQuery.companyName" placeholder="请输入供应商名称" clearable @keyup.enter="handleNameQuery" class="hasChecked-search-input" />
          <el-button type="primary" icon="el-icon-search" class="hasChecked-search-button" @click="handleNameQuery">搜索</el-button>
        </div>
        <el-table ref="commonTable" stripe :data="commonList" row-key="id" style="width: 100%" class="custom-table custom-expanded" @selection-change="handleCommonSelection" v-if="commonList.length">
          <el-table-column align="center" type="selection" width="50" reserve-selection></el-table-column>
          <el-table-column align="center" type="index" label="序号"></el-table-column>
          <el-table-column align="center" prop="companyName" label="供应商名称" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" label="联系人" prop="concat" />
          <el-table-column align="center" label="电话" prop="phone" />
        </el-table>
        <el-empty v-if="!commonList.length" description="暂无可供应的供应商，请修改搜索关键词再重新尝试" />
        <div class="custom-pagination" style="margin-top: 20px">
          <pagination v-show="commonTotal > 0" :total="commonTotal" :page.sync="commonQuery.pageNum" :limit.sync="commonQuery.pageSize" @pagination="handleGetCommonMore" />
        </div>
      </div>
      <div slot="footer">
        <el-badge>
          <button type="button" class="custom-dialog-btn" @click="commonOpen = false">取消</button>
        </el-badge>
        <el-badge :value="commonCheck.length" :hidden="!commonCheck.length">
          <button type="button" class="custom-dialog-btn primary" @click="handleSubmitCommon">确定</button>
        </el-badge>
      </div>
    </el-dialog>
    <!-- 供应商详情 -->
    <supplier-dialog ref="supplier"></supplier-dialog>
  </div>
</template>

<script>
import { expireTimeFormat } from '@/utils'
import supplierDialog from '@/views/purchase/demandForMe/supplier'
import { listCompany } from '@/api/system/company'
import { releaseCustomization } from '@/api/customization'

export default {
  components: { supplierDialog },
  data() {
    return {
      open: false,
      info: {},
      // 更多供应商
      commonOpen: false,
      commonChecked: [],
      commonList: [],
      commonCheck: [],
      commonQuery: {
        pageNum: 1,
        pageSize: 10,
        status: '1',
        companyName: ''
      },
      commonTotal: 0,
      commonPage: 1,
      commonSize: 10
    }
  },
  methods: {
    expireTimeFormat,
    getInfo(data = {}) {
      this.info = { ...data }
      this.info.draw = data.draw ? this.imgPath + data.draw : ''
      this.info.image = data.picture_oss || data.picture ? this.imgPath + data.picture : '' || ''
      this.commonList = []
      this.commonChecked = []
      this.commonCheck = []
      this.open = true
    },
    // 关闭
    handleClose() {
      this.info = {}
      this.commonList = []
      this.commonChecked = []
      this.commonCheck = []
      this.open = false
    },
    // 查看供应商详情
    handleViewSupplier(row) {
      const id = row.companyId
      this.$refs.supplier.getInfo(id, 'common')
    },
    // 打开公域供应商列表
    handleOpenCommon() {
      this.commonQuery.pageNum = 1
      this.commonOpen = true
      this.commonEcho()
      this.handleGetCommonMore()
    },
    // 回显选中
    commonEcho() {
      this.$nextTick(() => {
        this.commonList.map(item => {
          this.$refs.commonTable.toggleRowSelection(item, false)
          this.commonChecked.map(itt => {
            if (itt.id === item.id) this.$refs.commonTable.toggleRowSelection(item, true)
          })
        })
      })
    },
    handleNameQuery() {
      this.commonQuery.pageNum = 1
      this.handleGetCommonMore()
    },
    // 查询全部供应商
    async handleGetCommonMore() {
      const res = await listCompany(this.commonQuery)
      const { code, rows, total, msg } = res
      if (code === 200) {
        this.commonList = rows
        this.commonTotal = total
      } else this.$message.error(msg)
    },
    handleCommonSelection(selection) {
      this.commonCheck = selection
      this.$nextTick(() => {
        this.commonChecked = this.commonCheck
      })
    },
    checkStatus(row, index) {
      const idx = this.commonChecked.findIndex(item => item.id === row.id)
      if (idx !== -1) return true
      return this.commonCheck.length < 10
    },
    // 确定选择供应商
    handleSubmitCommon() {
      this.commonChecked = this.commonCheck
      this.commonOpen = false
    },
    // 删除已选择的供应商
    handleDeleteCommon(row) {
      const idx = this.commonChecked.findIndex(item => item.id === row.id)
      if (idx !== -1) this.commonChecked.splice(idx, 1)
      if (this.$refs.commonTable) this.commonEcho()
    },
    // 发布产品定制需求
    handleSubmit() {
      const demandId = this.info.listid
      const supplierIds = this.commonChecked.map(item => item.id)
      const data = { demandId, supplierIds }
      releaseCustomization(data).then(res => {
        const { code, msg } = res
        if (code === 200) {
          this.$message.success('发布成功')
          this.handleClose()
          this.$emit('refresh')
        } else this.$message.error(msg)
      })
    }
  }
}
</script>

<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';
.info {
  padding: 0 20px;
  &-title {
    font-size: 14px;
    line-height: 20px;
    color: $disabled;
  }
  &-con {
    padding: 11px 15px;
    line-height: 32px;
    background-color: #f0f3f9;
    border-radius: 5px;
    position: relative;
    margin: 17px 0;
    &:before {
      display: inline-block;
      content: '';
      width: 0;
      height: 0;
      border-color: transparent transparent #f0f3f9 transparent;
      border-width: 6px;
      border-style: solid;
      position: absolute;
      top: -12px;
      left: 40px;
    }
    span {
      font-size: 12px;
      color: $info;
    }
    b {
      font-size: 14px;
      font-weight: normal;
      color: $font;
      &.link {
        color: $blue;
        &:hover {
          text-decoration: underline;
        }
      }
    }
  }
  &-product {
    display: flex;
    align-items: center;
    height: 130px;
    border: 1px solid #cbd6e2;
    border-radius: 2px;
    margin-top: 11px;
    margin-bottom: 20px;
    &-image {
      display: inline-flex;
      align-items: center;
      width: 130px;
      height: 130px;
      border-right: 1px solid #cbd6e2;
    }
    &-con {
      padding-left: 15px;
      line-height: 40px;
      span {
        font-size: 12px;
        color: $disabled;
      }
      b {
        font-size: 14px;
        font-weight: normal;
        color: $font;
      }
    }
  }
  &-supplier {
    display: flex;
    align-items: flex-start;
    &-title {
      font-size: 14px;
      color: $disabled;
      line-height: 40px;
      flex-shrink: 0;
      margin-right: 15px;
    }
  }
}
.hasChecked-search {
  padding: 20px 0;
  width: 635px;
  position: relative;
  margin: 0 auto;
  &-input ::v-deep {
    position: relative;
    height: 46px;
    .el-input__inner {
      padding-left: 20px;
      padding-right: 130px;
      height: 46px;
      line-height: 46px;
    }
    .el-input__suffix {
      right: 120px;
    }
  }
  &-button {
    position: absolute;
    right: 3px;
    top: 23px;
    height: 40px;
    width: 117px;
  }
}
.hasChecked-title {
  text-align: center;
  font-size: 14px;
  color: #666666;
  margin-bottom: 10px;
}
.hasChecked-info {
  display: flex;
  flex-wrap: wrap;
  &.more {
    background-color: #f0f3f9;
    padding: 20px;
    position: relative;
    margin-top: 10px;
    &:before {
      content: '';
      width: 0;
      border-style: solid;
      border-width: 0 8px 8px 8px;
      border-color: transparent transparent #f0f3f9 transparent;
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      top: -8px;
    }
  }
  .hasChecked-item {
    border: 1px solid #2e73f3;
    background-color: #f1f6ff;
    font-size: 14px;
    color: #2e73f3;
    border-radius: 5px;
    margin: 5px;
    position: relative;
    padding: 0 40px 0 10px;
    height: 30px;
    line-height: 30px;
    i {
      background-color: #2e73f3;
      color: #ffffff;
      display: inline-block;
      height: 28px;
      line-height: 28px;
      position: absolute;
      right: 0;
      width: 30px;
      text-align: center;
      cursor: pointer;
    }
  }
  .hasChecked-more {
    width: 190px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    background-color: #dfebff;
    border: 1px dashed $blue;
    font-size: 14px;
    color: $blue;
    border-radius: 5px;
    margin: 5px;
    cursor: pointer;
    &:hover {
      background-color: $blue;
      color: $white;
    }
  }
}
</style>
