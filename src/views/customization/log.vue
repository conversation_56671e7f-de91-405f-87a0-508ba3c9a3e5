<template>
  <div>
    <el-dialog v-dialogDragBox title="产品定制发送记录" :visible.sync="open" width="1150px" class="custom-dialog">
      <div style="padding: 0 20px">
        <el-table ref="list" stripe :data="list" row-key="id" style="width: 100%" class="custom-table custom-table-cell10">
          <el-table-column align="center" type="index" label="序号"></el-table-column>
          <el-table-column align="center" prop="companyName" label="供应商" show-overflow-tooltip>
            <template slot-scope="{ row }" v-if="hasOwnProperty(row, 'companyDO') && hasOwnProperty(row.companyDO, 'companyName')">
              <span class="table-link" v-if="hasOwnProperty(row, 'companyDO') && row.companyDO.companyId != -1" @click="handleViewSupplier(row.companyDO)">{{ row.companyDO.companyName }}</span>
              <span v-else>{{ row.companyDO.companyName }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="sendUser" label="联系人" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="sendPhone" label="联系电话" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="createTime" label="发送时间" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="status" :formatter="recordFormat" label="发送状态" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="reply" :formatter="replyFormat" label="回复状态" show-overflow-tooltip></el-table-column>
        </el-table>
      </div>
      <div slot="footer">
        <button type="button" class="custom-dialog-btn primary" @click="open = false">关闭</button>
      </div>
    </el-dialog>
    <!-- 供应商详情 -->
    <supplier-dialog ref="supplier"></supplier-dialog>
  </div>
</template>

<script>
import { getSendRecord } from '@/api/customization'
import { getMsgDetail } from '@/api/record'
import supplierDialog from '@/views/purchase/demandForMe/supplier'

export default {
  components: { supplierDialog },
  data() {
    return {
      open: false,
      list: [],
      statusOptions: [
        { label: '未发送', value: '0' },
        { label: '已发送', value: '1' },
        { label: '发送失败', value: '2' }
      ]
    }
  },
  methods: {
    hasOwnProperty(obj, key) {
      return Object.prototype.hasOwnProperty.call(obj, key)
    },
    recordFormat(row) {
      if (row.status === 1) return '等待回执'
      if (row.status === 2) return '发送失败'
      if (row.status === 3) return '发送成功'
    },
    replyFormat(row) {
      return row.isReply ? '已回复' : '未回复'
    },
    init(data = {}) {
      getSendRecord({ demandId: data.listid }).then(async res => {
        const { code, msg, data } = res
        if (code === 200) {
          if (data.length) {
            await Promise.all(
              data.map(async item => {
                const statusData = await getMsgDetail({
                  phone: item.sendPhone,
                  day: this.parseTime(item.createTime, '{y}{m}{d}')
                })
                if (statusData.code === 200) {
                  if (statusData.data.length) {
                    item.status = statusData.data[0].sendStatus
                  } else item.status = 1
                }
              })
            )
          }
          this.list = data
          this.open = true
        } else this.$message.error(msg)
      })
    },
    // 查看供应商详情
    handleViewSupplier(row) {
      const id = row.companyId
      this.$refs.supplier.getInfo(id, 'common')
    }
  }
}
</script>

<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';
</style>
