<template>
  <div class="container" @click="clickHide">
    <header-tpl :is-login="!!getToken()" :showSearch="false" />
    <div class="webMain">
      <div class="webContainer">
        <div class="sale-categroy">
          <div class="sale-categroy-parent">
            <div class="inline-flex">
              <div class="parent-item" :class="{ active: item.id === parentId }" v-for="item in categoryList" :key="item.id" @click="getParentList(item)">
                {{ item.name }}
              </div>
            </div>
            <div class="parent-create" @click="handleAdd">
              <i class="el-icon-plus"></i>
              定制产品
            </div>
          </div>
          <div class="sale-categroy-children" v-if="!!childrenList.length">
            <div class="children-item" :class="{ active: item.id === childrenId }" v-for="item in childrenList" :key="item.id" @click="getChildrenList(item)">
              {{ item.name }}
            </div>
          </div>
        </div>
        <div class="minHeight">
          <template v-if="total">
            <div class="customZone-list" v-loading="loading">
              <el-row :gutter="20">
                <el-col :span="4" v-for="item in list" :key="item.id">
                  <div class="searchBox-item" @click.stop="productId = item.id">
                    <el-badge is-dot :hidden="!item.hasDot">
                      <el-image :src="formatProductImg(item)" lazy class="searchBox-item-img"></el-image>
                    </el-badge>
                    <b class="searchBox-item-name">{{ item.productName }}</b>
                    <template v-if="item.id === productId">
                      <item-tpl :desc-info="item" :view-num="6" is-custom @clickHide="clickHide" @handleLikeness="handleLikeness" @handleCustom="handleCustom" />
                    </template>
                  </div>
                </el-col>
              </el-row>
            </div>
            <div class="product-more" v-if="total > queryParams.pageSize">
              <div class="product-more-btn" @click="handleMoreNavProduct">
                <template v-if="isMore">
                  <i class="ssfont ss-diy-biaoqing"></i>
                  我是有底线的
                </template>
                <template v-else>
                  <i :class="moreLoading ? 'el-icon-loading' : 'el-icon-refresh'"></i>
                  加载更多
                </template>
              </div>
            </div>
          </template>
          <el-empty :description="!loading && !total ? '暂无数据' : '加载中…'" v-else />
        </div>
      </div>
    </div>
    <footer-tpl />

    <!--  新增定制  -->
    <customiZation ref="customiZation" :addsource="'outer'"></customiZation>
  </div>
</template>
<script>
import HeaderTpl from '@/views/public/components/head'
import FooterTpl from '@/views/public/components/foot'
import { getToken } from '@/utils/auth'
import { getHomeCustomization } from '@/api/customization'
import ItemTpl from '@/views/public/product/item'
import customiZation from '@/views/customization/create'
import { getlist } from '@/api/purchase/dysplasia'

export default {
  name: 'Customization',
  components: { customiZation, ItemTpl, FooterTpl, HeaderTpl },
  data() {
    return {
      categoryList: [], // 父级分类列表
      childrenList: [], // 子级分类列表
      parentId: '', // 父级分类id
      childrenId: '', // 子级分类id
      list: [], // 产品列表
      total: 0, // 产品总数
      loading: true, // 加载状态
      moreLoading: false, // 加载更多状态
      isMore: false, // 是否加载更多
      // 搜索条件
      queryParams: {
        pageNum: 1,
        pageSize: 24,
        dysCategoryId: undefined, // 类目ID
        materialQuality: undefined, // 材质
        model: undefined, // 型号
        productName: undefined, // 产品名称
        specs: undefined, // 规格
        surface: undefined // 表面处理
      },
      productId: undefined
    }
  },
  created() {
    this.getCategroyList()
    this.getList()
  },
  methods: {
    getToken,
    // 获取分类列表
    getCategroyList() {
      getlist().then(res => {
        const { code, msg, data } = res
        if (code === 200) this.categoryList = data
        else this.$message.error(msg)
      })
    },
    // 查询子分类及产品列表
    getParentList(item) {
      this.childrenList = item.children ? item.children : []
      this.parentId = item.id
      this.childrenId = undefined
      this.queryParams.dysCategoryId = item.id
      this.resetQuery()
    },
    // 根据子分类查询产品列表
    getChildrenList(item) {
      this.childrenId = item.id
      this.queryParams.dysCategoryId = item.id
      this.resetQuery()
    },
    // 重置查询条件
    resetQuery() {
      this.queryParams.pageNum = 1
      this.list = []
      this.getList()
    },
    // 获取促销品列表
    getList() {
      this.loading = true
      getHomeCustomization(this.queryParams).then(res => {
        const { code, msg, rows, total } = res
        if (code === 200) {
          rows.map(item => {
            item.picture1_oss = item.picture_oss || ''
            item.picture1 = item.picture || ''
          })
          this.list = [...this.list, ...rows]
          this.total = total
          this.loading = false
          this.moreLoading = false
          this.isMore = this.queryParams.pageNum * this.queryParams.pageSize >= total
        } else this.$message.error(msg)
      })
    },
    // 加载更多
    handleMoreNavProduct() {
      if (this.isMore) return
      this.queryParams.pageNum += 1
      this.moreLoading = true
      this.getList()
    },
    // 定制产品
    handleAdd() {
      if (!getToken()) {
        this.$confirm('为了可以更好的完成您的定制产品，请您登录。<br />是否前往登录？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          dangerouslyUseHTMLString: true,
          type: 'warning'
        }).then(() => {
          this.$router.push({ path: '/login' })
        })
        return
      }
      this.$refs.customiZation.create()
    },
    // 点击搜索结果产品以外区域隐藏产品弹框
    clickHide(event) {
      this.productId = undefined
    },
    // 定制类似产品
    handleLikeness(info) {
      if (!getToken()) {
        this.$confirm('您还未登录，是否前往登录？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$router.push({ path: '/login' })
        })
        return
      }
      info.categoryId = info.dysCategoryId
      this.$refs.customiZation.create({ ...info, productId: this.productId })
    },
    // 定制类似产品
    handleCustom(info) {
      if (!getToken()) {
        this.$confirm('您还未登录，是否前往登录？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$router.push({ path: '/login' })
        })
        return
      }
      this.$refs.customiZation.createLike({ ...info, productId: this.productId })
    }
  }
}
</script>

<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';
@import '~@/assets/styles/custom-public.scss';
.customZone-list {
  height: auto;
  margin: 20px auto;
}
.sale-categroy {
  margin-top: 15px;
  width: 100%;
  display: flex;
  flex-direction: column;
  box-shadow: 0 0 26px 0 rgba(0, 0, 0, 0.05);
  border-radius: 5px;
  overflow: hidden;
  &-parent {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 50px;
    border: 1px solid #e6e6e7;
    border-bottom-color: $blue;
    border-radius: 5px 5px 0 0;
    .parent-item {
      width: 130px;
      text-align: center;
      height: 50px;
      line-height: 50px;
      cursor: pointer;
      margin: -1px 0;
      font-size: 14px;
      &:hover,
      &.active {
        transition: all 0.1s;
        line-height: 48px;
        background-color: $white;
        color: $blue;
        border: 1px solid $blue;
        border-radius: 5px 5px 0 0;
        border-bottom-color: $white;
      }
    }
    .parent-create {
      display: flex;
      align-items: center;
      padding: 0 20px;
      cursor: pointer;
      color: $blue;
      font-size: 14px;
      i {
        margin-right: 5px;
      }
    }
  }
  &-children {
    background-color: $white;
    padding: 10px 20px;
    display: flex;
    flex-wrap: wrap;
    .children-item {
      width: calc(100% / 6);
      height: 40px;
      line-height: 40px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      font-size: 14px;
      color: $info;
      cursor: pointer;
      &:hover,
      &.active {
        color: $blue;
      }
    }
  }
}
.minHeight {
  min-height: 400px;
}
</style>
