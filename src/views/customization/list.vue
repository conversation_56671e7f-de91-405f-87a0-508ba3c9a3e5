<template>
  <div class="newBox bgcf9 vh-85">
    <div class="custom-search">
      <div class="flex">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" @submit.native.prevent>
          <el-row :gutter="10">
            <el-col :xs="8" :sm="6" :lg="4">
              <el-form-item label="名称" prop="productName">
                <el-input v-model="queryParams.productName" placeholder="请输入产品名称" clearable
                  @keyup.enter.native="handleQuery" size="small" />
              </el-form-item>
            </el-col>
            <el-col :xs="8" :sm="6" :lg="4">
              <el-form-item label="规格" prop="specs">
                <el-input v-model="queryParams.specs" placeholder="请输入产品规格" clearable @keyup.enter.native="handleQuery"
                  size="small" />
              </el-form-item>
            </el-col>
            <el-col :xs="8" :sm="6" :lg="4">
              <el-form-item label="型号" prop="model">
                <el-input v-model="queryParams.model" placeholder="请输入产品型号" clearable @keyup.enter.native="handleQuery"
                  size="small" />
              </el-form-item>
            </el-col>
            <el-col :xs="8" :sm="6" :lg="4">
              <el-form-item label="材质" prop="materialQuality">
                <el-input v-model="queryParams.materialQuality" placeholder="请输入产品材质" clearable
                  @keyup.enter.native="handleQuery" size="small" />
              </el-form-item>
            </el-col>
            <el-col :xs="8" :sm="6" :lg="4">
              <el-form-item label="表面" prop="surface">
                <el-input v-model="queryParams.surface" placeholder="请输入产品表面处理" clearable
                  @keyup.enter.native="handleQuery" size="small" />
              </el-form-item>
            </el-col>
            <el-col :xs="8" :sm="6" :lg="4">
              <el-form-item label="有效状态" prop="filterExpire">
                <el-select v-model="queryParams.filterExpire" placeholder="请选择有效状态" @change="handleQuery">
                  <el-option :value="false" label="已过期"></el-option>
                  <el-option :value="true" label="未过期"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :xs="8" :sm="6" :lg="4" v-if="types == 'demand'">
              <el-form-item label="状态" prop="status">
                <el-select v-model="queryParams.status" placeholder="请选择状态" clearable @change="handleQuery">
                  <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <!-- <el-col :xs="8" :sm="6" :lg="8" v-if="showAll">
            <el-form-item label="分类" prop="categoryId">
              <treeselect v-model="queryParams.categoryId" :options="categoryList" :normalizer="normalizer"
                :show-count="true" placeholder="请选择产品分类" class="search-treeselect">
                <div slot="option-label" slot-scope="{ node }" class="category-flex">
                  <span>{{ node.label }}</span>
                  <span class="category-flex-desc">{{ node.raw.model ? `规格型号：${node.raw.model}` : '' }}</span>
                </div>
              </treeselect>
            </el-form-item>
          </el-col> -->
            <el-col :span="1.5">
              <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
              </el-form-item>
              <el-form-item>
                <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" icon="el-icon-plus" size="small" @click="handleAdd"
                  v-if="checkPermi(['customized:product:sys'])">新增</el-button>
              </el-form-item>
              <!-- <el-form-item>
              <el-button type="text" size="mini" @click="clickSearch">
                {{ word }}
                <i :class="showAll ? 'el-icon-arrow-up ' : 'el-icon-arrow-down'"></i>
              </el-button>
            </el-form-item> -->
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div style="height: 30px;">
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
      </div>
    </div>

    <div class="Box">
      <!-- <el-tabs v-model="types" @tab-click="handleClick">
        <el-tab-pane label="定制产品" name="product"></el-tab-pane>
        <el-tab-pane label="定制产品需求" name="demand"></el-tab-pane>
      </el-tabs> -->
      <el-table v-loading="loading" ref="list" stripe :data="list" class="custom-table" :key="key">
        <el-table-column label="序号" align="center" v-if="columns[0].visible">
          <template slot-scope="scope">{{ scope.$index + 1 }}</template>
        </el-table-column>
        <el-table-column label="产品名称" align="center" prop="productName" show-overflow-tooltip
          v-if="columns[1].visible"></el-table-column>
        <el-table-column label="产品图片" align="center" width="70" v-if="columns[2].visible">
          <template slot-scope="{ row }">
            <div style="display: inline-flex; justify-content: center" v-if="row.picture_oss || row.picture">
              <image-preview :src="row.picture_oss || row.picture" :width="50" :height="50" />
            </div>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="产品规格" align="center" prop="specs" show-overflow-tooltip
          v-if="columns[3].visible"></el-table-column>
        <el-table-column label="产品型号" align="center" prop="model" show-overflow-tooltip
          v-if="columns[4].visible"></el-table-column>
        <el-table-column label="产品材质" align="center" prop="materialQuality" show-overflow-tooltip
          v-if="columns[5].visible"></el-table-column>
        <el-table-column label="表面处理" align="center" prop="surface" show-overflow-tooltip
          v-if="columns[6].visible"></el-table-column>
        <el-table-column label="产品单位" align="center" prop="unit" show-overflow-tooltip
          v-if="columns[7].visible"></el-table-column>
        <el-table-column label="产品重量" align="center" prop="weight" show-overflow-tooltip v-if="columns[8].visible">
          <template slot-scope="{ row }">{{ row.weight ? `${row.weight}Kg` : '' }}</template>
        </el-table-column>
        <el-table-column label="定制数量" align="center" prop="quantity" show-overflow-tooltip
          v-if="types === 'demand' && columns[9].visible">
          <template slot-scope="{ row }">{{ row.quantity + '' + (row.unit || '') }}</template>
        </el-table-column>
        <el-table-column label="供货截止" align="center" prop="supplyEndTime" show-overflow-tooltip
          v-if="types === 'demand' && columns[10].visible"></el-table-column>
        <el-table-column label="有效时间" align="center" prop="expireTime" show-overflow-tooltip
          v-if="types === 'demand' && columns[11].visible">
          <template slot-scope="{ row }">
            <Countdown :expireTime="row.expireTime" pattern="{d}{h}{m}" />
          </template>
        </el-table-column>
        <el-table-column label="来源" align="center" v-if="types == 'product' && columns[12].visible">
          <template slot-scope="{ row }">
            <div v-if="row.source == 'sys'">内部添加</div>
            <div v-if="row.source == 'outer'">用户申请</div>
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" v-if="columns[13].visible">
          <template slot-scope="{ row }">
            <el-tooltip effect="dark" :content="row.reason" placement="top" :disabled="row.liststatus != -1">
              <div v-html="statusFormat(row)"></div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="是否公开" align="center"
          v-if="checkPermi(['customized:product:sys']) && queryParams.status == 2 && columns[14].visible">
          <template slot-scope="{ row }">
            <template>
              <el-switch v-model="row.isOpen" active-text="是" inactive-text="否" :active-value="true"
                :inactive-value="false" :disabled="row.liststatus != 2" @change="handlePutaway(row, $event)"
                class="table-switch"></el-switch>
            </template>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" min-width="250">
          <template slot-scope="{ row }">
            <el-button class="table-btn small" @click="handleView(row)">查看详情</el-button>
            <template v-if="checkPermi(['customized:product:sys']) && types == 'product' && !row.isOpen">
              <el-button class="table-btn small hasbg red" @click="handleDelete(row, 'sys')"
                v-if="row.source == 'sys'">删除定制</el-button>
              <!-- <el-button class="table-btn small hasbg red" @click="handleDelete(row, 'outer')" v-if="row.source != 'sys'">退回定制</el-button> -->
              <el-button class="table-btn small primary" @click="handleUpdate(row, 'sys')">修改定制</el-button>
            </template>
            <template v-if="!checkPermi(['customized:product:sys']) && types == 'demand'">
              <el-button class="table-btn small hasbg" :class="isAcceptOrSend(row) ? 'disabled' : 'red'"
                :disabled="isAcceptOrSend(row)" @click="handleDelete(row, 'demand')"
                v-if="!isAcceptOrSend(row)">删除定制</el-button>
              <el-button class="table-btn small" :class="isAcceptOrSend(row) ? 'disabled' : 'primary'"
                :disabled="isAcceptOrSend(row)" @click="handleUpdate(row, 'demand')"
                v-if="row.liststatus == 0">修改定制</el-button>
            </template>
            <template v-if="checkPermi(['customized:product:sys']) && types == 'demand'">
              <el-button class="table-btn small primary" :class="{ disabled: row.liststatus != 0 }"
                :disabled="row.liststatus != 0" @click="handleAccept(row)" v-if="row.liststatus == 0">立即受理</el-button>
              <el-button class="table-btn small" :class="row.liststatus != 0 ? 'disabled' : 'orange'"
                :disabled="row.liststatus != 0" @click="handleReject(row)" v-if="row.liststatus == 0">驳回定制</el-button>
              <el-button class="table-btn small primary"
                :class="{ disabled: !(row.liststatus == 1 || row.liststatus == 2) }"
                :disabled="!(row.liststatus == 1 || row.liststatus == 2)" @click="handleSend(row)"
                v-if="row.liststatus == 1 || row.liststatus == 2">发布询价</el-button>
              <el-button class="table-btn small" :class="row.liststatus != 2 ? 'disabled' : 'orange'"
                :disabled="row.liststatus != 2" @click="handleLog(row)" v-if="row.liststatus == 2">发送记录</el-button>
              <el-badge is-dot class="custom-badge" :hidden="!row.reply" v-if="row.liststatus == 2">
                <el-button class="table-btn small" :class="row.liststatus != 2 || !row.reply ? 'disabled' : 'primary'"
                  :disabled="row.liststatus != 2 || !row.reply" @click="handleReply(row)"
                  v-if="row.liststatus == 2">查看报价</el-button>
              </el-badge>
            </template>
          </template>
        </el-table-column>
      </el-table>
      <div class="custom-pagination">
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize" @pagination="getList" />
      </div>
    </div>

    <!--  新增/修改产品定制  -->
    <create ref="create" @refresh="refresh" isRefresh :addsource="'sys'" />

    <!--  产品定制详情  -->
    <detail ref="view" :source="types" />

    <!--  发布产品定制  -->
    <send ref="send" @refresh="refresh" />

    <!--  发送记录  -->
    <log ref="log" />

    <!--  查看报价  -->
    <reply ref="reply" />

    <!-- 公开产品 -->
    <el-dialog v-dialogDragBox title="公开产品" :visible.sync="openStatus" width="30%">
      <el-form :model="openForm" :rules="rules" ref="openForm" :label-position="'right'" label-width="100px">
        <el-form-item label="异型件分类" prop="categoryId">
          <treeselect v-model="openForm.categoryId" :options="categoryList" :normalizer="normalizer" :show-count="true"
            placeholder="请选择异型件分类">
            <div slot="option-label" slot-scope="{ node }" class="category-flex">
              <span>{{ node.label }}</span>
              <span class="category-flex-desc">{{ node.raw.model ? `规格型号：${node.raw.model}` : '' }}</span>
            </div>
          </treeselect>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="openStatus = false">取 消</el-button>
        <el-button type="primary" @click="submitOpen('openForm')">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getCustomization, deleteCustomization, acceptCustomization, getReply, publicCustomization, closePublicCustomization, getDemandList, deleteCustomizationSys } from '@/api/customization'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import { getlist } from '@/api/purchase/dysplasia'
import { expireTimeFormat } from '@/utils'
import { checkPermi } from '@/utils/permission'
import create from '@/views/customization/create'
import detail from '@/views/customization/view'
import send from '@/views/customization/send'
import log from '@/views/customization/log'
import reply from '@/views/customization/reply'

export default {
  name: 'Custommade',
  components: { Treeselect, create, detail, send, log, reply },
  data() {
    return {
      showAll: false,
      word: '展开',
      loading: true,
      total: 0,
      list: [],
      queryParams: {
        pageSize: 10,
        pageNum: 1,
        categoryId: undefined,
        filterExpire: true,
        materialQuality: undefined,
        model: undefined,
        productName: undefined,
        specs: undefined,
        status: undefined,
        surface: undefined
      },
      categoryList: [],
      statusOptions: [
        { label: '已驳回', value: -1, color: '#ec4545' },
        { label: '待审核', value: 0, color: '#f35d09' },
        { label: '已受理', value: 1, color: '#2e73f3' },
        { label: '已发送', value: 2, color: '#31c776' }
      ],
      types: 'demand',
      openStatus: false,
      openForm: {
        categoryId: undefined
      },
      rules: {
        categoryId: [{ required: true, message: '请选择异型件分类', trigger: 'change' }]
      },
      key: 1,
      // 列表显隐
      showSearch: true,
      // 列信息
      columns: [
        { key: 0, label: `序号`, visible: true },
        { key: 1, label: `产品名称`, visible: true },
        { key: 2, label: `产品图片`, visible: true },
        { key: 3, label: `产品规格`, visible: true },
        { key: 4, label: `产品型号`, visible: true },
        { key: 5, label: `产品材质`, visible: true },
        { key: 6, label: `表面处理`, visible: true },
        { key: 7, label: `产品单位`, visible: true },
        { key: 8, label: `产品重量`, visible: true },
        { key: 9, label: `定制数量`, visible: true },
        { key: 10, label: `供货截止`, visible: true },
        { key: 11, label: `有效时间`, visible: true },
        { key: 12, label: `来源`, visible: true },
        { key: 13, label: `状态`, visible: true },
        { key: 14, label: `是否公开`, visible: true },
      ],
    }
  },
  created() {
    this.getCategoryList()
    this.getList()
  },
  methods: {
    checkPermi,
    expireTimeFormat,
    hasOwnProperty(obj, key) {
      return Object.prototype.hasOwnProperty.call(obj, key)
    },
    // 回显状态及颜色
    statusFormat(row) {
      const status = this.statusOptions.find(item => item.value === row.liststatus)
      return status ? `<span style="color: ${status.color}">${status.label}</span>` : ''
    },
    // 判断状态是否为已受理或已发送
    isAcceptOrSend(row) {
      return row.liststatus == 1 || row.liststatus == 2
    },
    // 获取产品分类
    getCategoryList() {
      getlist({ pageNum: 1, pageSize: 999 }).then(res => {
        this.categoryList = res.data
      })
    },
    // 格式化产品分类
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children
      }
      return {
        id: node.id,
        label: node.name,
        model: node.model,
        children: node.children
      }
    },
    getList() {
      this.loading = true
      if (this.types === 'demand') {
        getDemandList(this.queryParams).then(async res => {
          const { code, msg, rows, total } = res
          if (code === 200) {
            if (rows.length) {
              await Promise.all(
                rows.map(async item => {
                  item.listid = item.id
                  item.liststatus = item.status
                  const product = item.hasOwnProperty('product') && item.product ? item.product : {}
                  item = Object.assign(item, product)
                  const { data } = await getReply({ demandId: item.listid })
                  item.reply = !!data.length
                })
              )
            }
            this.list = rows
            this.total = total
            this.loading = false
          } else this.$message.error(msg)
        })
      } else if (this.types === 'product') {
        this.queryParams.status = 1
        getCustomization(this.queryParams).then(async res => {
          const { code, msg, rows, total } = res
          if (code === 200) {
            if (rows.length) {
              await Promise.all(
                rows.map(async item => {
                  item.listid = item.id
                  item.liststatus = item.status
                })
              )
            }
            this.list = rows
            this.total = total
            this.loading = false
          } else this.$message.error(msg)
        })
      }
    },
    // 刷新
    refresh() {
      if (this.types === 'demand') {
        getDemandList(this.queryParams).then(async res => {
          const { rows, total } = res
          if (rows.length) {
            await Promise.all(
              rows.map(async item => {
                item.listid = item.id
                item.liststatus = item.status
                const product = item.hasOwnProperty('product') && item.product ? item.product : {}
                item = Object.assign(item, product)
                const { data } = await getReply({ demandId: item.listid })
                item.reply = !!data.length
              })
            )
          }
          this.$set(this, 'list', rows)
          this.$set(this, 'total', total)
        })
      } else if (this.types === 'product') {
        getCustomization(this.queryParams).then(async res => {
          const { rows, total } = res
          if (rows.length) {
            await Promise.all(
              rows.map(async item => {
                item.listid = item.id
                item.liststatus = item.status
                const product = item.hasOwnProperty('product') && item.product ? item.product : {}
                item = Object.assign(item, product)
                const { data } = await getReply({ demandId: item.listid })
                item.reply = !!data.length
              })
            )
          }
          this.$set(this, 'list', rows)
          this.$set(this, 'total', total)
        })
      }
    },
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 重置搜索
    resetQuery() {
      this.resetForm('queryForm')
      this.queryParams.status = undefined
      this.handleQuery()
    },
    // 展开收起搜索
    clickSearch() {
      this.showAll = !this.showAll
      this.word = this.showAll ? '收起' : '展开'
    },
    // 切换标签页
    handleClick() {
      this.key = Math.random()
      this.resetQuery()
    },
    // 新增
    handleAdd() {
      this.$refs.create.create()
    },
    // 修改
    handleUpdate(row, updateSoucrce) {
      this.$refs.create.update(row, updateSoucrce)
    },
    // 查看详情
    handleView(row) {
      this.$refs.view.getInfo(row)
    },
    // 删除
    // prettier-ignore
    handleDelete(row, sources) {
      this.$confirm('确定删除该产品定制吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        if (this.types === 'demand' && sources === 'demand') {
          deleteCustomization({ demandIds: row.listid }).then(res => {
            const { code, msg } = res
            if (code === 200) {
              this.$message.success('删除成功')
              this.refresh()
            } else this.$message.error(msg)
          })
        } else if (this.types === 'product' && sources === 'sys') {
          deleteCustomizationSys({ productId: row.listid }).then(res => {
            const { code, msg } = res
            if (code === 200) {
              this.$message.success('删除成功')
              this.refresh()
            } else this.$message.error(msg)
          })
        } else if (this.types === 'product' && sources === 'outer') {

        }
      }).catch(() => { })
    },
    // 受理
    handleAccept(row) {
      const { listid } = row
      const data = { demandId: listid, status: 1 }
      acceptCustomization(data).then(res => {
        const { code, msg } = res
        if (code === 200) {
          this.$message.success('受理成功')
          this.refresh()
        } else this.$message.error(msg)
      })
    },
    // 驳回并输入驳回原因
    // prettier-ignore
    handleReject(row) {
      this.$prompt('请输入驳回原因', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /\S/,
        inputErrorMessage: '请输入驳回原因'
      }).then(({ value }) => {
        const { listid } = row
        const data = { demandId: listid, status: -1, reason: value }
        acceptCustomization(data).then(res => {
          const { code, msg } = res
          if (code === 200) {
            this.$message.success('驳回成功')
            this.refresh()
          } else this.$message.error(msg)
        })
      }).catch(() => { })
    },
    // 发布询价
    handleSend(row) {
      this.$refs.send.getInfo(row)
    },
    // 发送记录
    handleLog(row) {
      this.$refs.log.init(row)
    },
    // 查看报价
    handleReply(row) {
      this.$refs.reply.init(row)
    },
    // 是否公开
    // prettier-ignore
    handlePutaway(row, status) {
      const title = status === false ? '确定要取消公开该产品定制吗?' : '确定要公开该产品定制吗?'
      this.$confirm(title, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {

        let res
        if (status === false) res = await closePublicCustomization({ id: row.productId })
        else {
          this.openStatus = true
          this.openForm.id = row.productId
        }
        // else res = await publicCustomization({ id: row.listid, categoryId: row.categoryId ? row.categoryId : row.dysCategoryId })
        if (res.code === 200) {
          this.$message.success('操作成功')
          this.refresh()
        } else this.$message.error(res.msg)
      }).catch(() => {
        row.isOpen = !status
      })
    },
    submitOpen(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          publicCustomization(this.openForm).then(res => {
            if (res.code === 200) {
              this.$message.success('操作成功')
              this.openStatus = false
              this.refresh()
            } else this.$message.error(res.msg)
          })
        } else {
          return false
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';

.Box {
  padding: 15px 20px;
}

.custom-search {
  padding-top: 17px;

  ::v-deep {
    .el-form-item--small {
      display: inline-flex;

      .el-form-item__label {
        flex-shrink: 0;
      }

      .el-form-item {
        margin-bottom: 10px;
      }
    }
  }
}

.search-treeselect {
  ::v-deep {
    .treeselect-main {
      width: 300px;
    }

    .vue-treeselect__control {
      height: 30px;
    }

    .vue-treeselect__input-container {
      height: 30px !important;
    }

    .vue-treeselect,
    .vue-treeselect__placeholder,
    .vue-treeselect__single-value {
      line-height: 28px !important;
      height: 28px !important;
    }

    .category-flex {
      display: flex;
      flex-wrap: nowrap;
      align-items: center;

      span {
        display: block;
        float: left;
      }

      &-desc {
        font-size: 12px;
        color: #999999;
        margin-left: 10px;
      }
    }
  }
}

.custom-badge ::v-deep {
  .el-badge__content.is-fixed.is-dot {
    right: 10px;
    top: 2px;
  }
}

.custom-table ::v-deep {
  .el-table__body-wrapper .table-switch {
    .el-switch__label {
      width: 40px !important;
    }

    .el-switch__core {
      width: 40px !important;
    }
  }
}
</style>
