<template>
  <div>
    <el-dialog v-dialogDragBox title="产品定制详情" :visible.sync="open" width="1150px" class="custom-dialog">
      <div class="info">
        <div class="info-title" v-if="source === 'demand'">定制信息</div>
        <div class="info-con con-bg" v-if="source === 'demand'">
          <el-row :gutter="10">
            <el-col :span="8">
              <span>有效时间：</span>
              <b><Countdown :expireTime="info.expireTime" pattern="{d}{h}{m}" /></b>
            </el-col>
            <el-col :span="8">
              <span>需求方：</span>
              <template v-if='info.companyDO'>
                <b class="link pointer" v-if="info.companyDO.companyId != -1" @click="handleViewSupplier(info.companyDO)">{{ info.companyDO && info.companyDO.companyName }}</b>
                <b v-else>{{ nfo.companyDO.companyName }}</b>
              </template>
              <b v-else>{{ info.createBy + "(" + info.phone + ")" }}</b>
            </el-col>
            <el-col :span="8">
              <span>定制数量：</span>
              <b>{{ info.quantity + '' + (info.unit || '') }}</b>
            </el-col>
            <el-col :span="8">
              <span>创建时间：</span>
              <b>{{ info.createTime }}</b>
            </el-col>
            <el-col :span="12">
              <span>供货截止：</span>
              <b>{{ info.supplyEndTime }}</b>
            </el-col>
          </el-row>
        </div>
        <div class="info-title">定制信息</div>
        <div class="info-con">
          <el-row :gutter="10">
            <el-col :span="8">
              <span>产品名称：</span>
              <b>{{ info.productName }}</b>
            </el-col>
            <el-col :span="8">
              <span>产品材质：</span>
              <b>{{ info.materialQuality }}</b>
            </el-col>
            <el-col :span="8">
              <span>产品单位：</span>
              <b>{{ info.unit }}</b>
            </el-col>
            <el-col :span="8">
              <span>产品规格：</span>
              <b>{{ info.specs }}</b>
            </el-col>
            <el-col :span="8">
              <span>产品型号：</span>
              <b>{{ info.model }}</b>
            </el-col>
            <el-col :span="8">
              <span>产品重量：</span>
              <b>{{ info.weight ? `${ info.weight }Kg` : '' }}</b>
            </el-col>
          </el-row>
        </div>
        <div class="info-tab">
          <span :class="{ active: current === 0 }" @click="current = 0">产品图片</span>
          <span :class="{ active: current === 1 }" @click="current = 1">产品图纸</span>
        </div>
        <div class="info-pic" v-if="current === 0 && info.image">
          <el-image :src="info.image" fit="contain" style="width: 100%" :preview-src-list="[info.image]"></el-image>
        </div>
        <el-empty v-if="current === 0 && !info.image" />
        <div class="product-view-box" v-if="current === 1 && info.draw">
          <template v-if="info.draw.includes('pdf')">
            <div style="display: flex; justify-content: space-between; align-items: center; padding-bottom: 10px" v-if="pdfCount > 1">
              <el-button type="primary" size="small" @click="pdfCurrent--" :disabled="pdfCurrent < 2">
                <i class="el-icon-arrow-left"></i>
                上一页
              </el-button>
              <span>{{ pdfCurrent }} / {{ pdfCount }}</span>
              <el-button type="primary" size="small" @click="pdfCurrent++" :disabled="pdfCurrent >= pdfCount">
                下一页
                <i class="el-icon-arrow-right"></i>
              </el-button>
            </div>
            <div class="page-pdf">
              <Pdf :src="info.draw" :page="pdfCurrent" @num-pages="pdfCount = $event" />
            </div>
          </template>
          <template v-else>
            <div style="text-align: center">
              <img :src="info.draw" style="max-width: 100%" />
            </div>
          </template>
        </div>
        <el-empty v-if="current === 1 && !info.draw" />
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn primary" @click="open = false">关闭</el-button>
      </div>
    </el-dialog>

    <!-- 供应商详情 -->
    <supplier-dialog ref="supplier"></supplier-dialog>
  </div>
</template>

<script>
import { expireTimeFormat } from '@/utils'
import supplierDialog from '@/views/purchase/demandForMe/supplier'

export default {
  props: {
    source: {
      type: String,
      default: ''
    }
  },
  components: { supplierDialog },
  data() {
    return {
      open: false,
      info: {},
      pdfCount: 0,
      pdfCurrent: 1,
      draw: '',
      current: 0
    }
  },
  methods: {
    expireTimeFormat,
    getInfo(data = {}) {
      this.info = { ...data }
      this.info.draw = data.draw ? this.imgPath + data.draw : ''
      this.info.image = data.picture_oss || data.picture ? this.imgPath + data.picture : '' || ''
      this.current = 0
      this.open = true
    },
    // 查看供应商详情
    handleViewSupplier(row) {
      const id = row.companyId
      this.$refs.supplier.getInfo(id, 'common')
    }
  }
}
</script>

<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';
.info {
  padding: 0 20px;
  &-title {
    font-size: 14px;
    color: $info;
    line-height: 20px;
    padding-bottom: 12px;
  }
  &-con {
    padding: 8px 0;
    border-top: 1px solid #e2e6f3;
    border-bottom: 1px solid #e2e6f3;
    margin-bottom: 17px;
    line-height: 40px;
    &.con-bg {
      background: #f8f9fb;
      padding: 12px 20px;
      line-height: 32px;
      border-top: 0;
      border-bottom: 0;
      span {
        color: $info;
      }
    }
    span {
      font-size: 12px;
      color: $disabled;
    }
    b {
      font-size: 14px;
      font-weight: normal;
      color: $font;
      &.link {
        color: $blue;
        &:hover {
          text-decoration: underline;
        }
      }
    }
  }
  &-tab {
    display: flex;
    align-items: center;
    border-bottom: 1px solid #e2e6f3;
    margin-bottom: 20px;
    span {
      display: inline-flex;
      font-size: 14px;
      line-height: 20px;
      padding-bottom: 12px;
      color: $disabled;
      cursor: pointer;
      &:hover,
      &.active {
        color: $info;
        border-bottom: 2px solid $blue;
        margin-bottom: -2px;
      }
    }
    span + span {
      margin-left: 30px;
    }
  }
  &-pic {
    display: inline-flex;
    align-items: center;
    width: 126px;
    height: 126px;
    border: 1px solid #d9d9d9;
    border-radius: 5px;
  }
}
</style>
