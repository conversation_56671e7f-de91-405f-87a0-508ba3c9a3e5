<template>
  <div>
    <div class="shadow" style="padding: 20px">
      <div class="weatherDate">
        <div class="weatherBox" @click="getWeather" title="点击刷新">
          <div class="weatherBoxTime">{{ weather.time }} 实况</div>
          <div class="weatherBoxFlex">
            <v-mini-weather-icon :icon="weather.weathercode" class="weatherBoxIcon"
              :key="weatherKey"></v-mini-weather-icon>
            <div class="weatherBoxTemp">
              <b>{{ weather.temp }}</b>
              <span>℃</span>
            </div>
          </div>
          <div class="weatherBoxFlex weatherBoxBorder">
            <div class="weatherBoxSD">相对湿度 {{ weather.sd }}</div>
            <div class="weatherBoxWDE">{{ weather.WD + ' ' + weather.WS }}</div>
          </div>
          <div class="weatherBoxA">
            <span>{{ location.province }}</span>
            <span>{{ location.city }}</span>
            <span>{{ location.area }}</span>
          </div>
        </div>
        <div class="dateBox">
          <div class="dateBoxTime">
            <span class="year">{{ indexDate.year }}</span>
            <span class="day">.{{ indexDate.month }}.{{ indexDate.day }}</span>
          </div>
          <div class="dateBoxWeek">{{ indexDate.week }}</div>
          <div class="dateBoxChina">{{ indexDate.lunar.lunarYear + indexDate.lunar.dateStr }}</div>
        </div>
      </div>
      <div class="dateBoxToDo" v-if="hasPermission()">
        <div class="dateBoxToDoItem" v-if="checkPermi(['oa:meeting:list'])" @click="handleOpen('meeting')">
          <span>会议助手</span>
          <span class="num">{{ meetingNum }}</span>
        </div>
        <div class="dateBoxToDoItem" v-if="checkPermi(['private:workPlan:list'])" @click="handleOpen('workPlan')">
          <span>工作计划</span>
          <span class="num">{{ workPlanNum }}</span>
        </div>
        <div class="dateBoxToDoItem" v-if="checkPermi(['private:workJoin:list'])" @click="handleOpen('workHandover')">
          <span>工作交接</span>
          <span class="num">{{ workHandoverNum }}</span>
        </div>
        <div class="dateBoxToDoItem" v-if="checkPermi(['oa:todo:list'])" @click="handleOpen('todo')">
          <span>挂牌督办</span>
          <span class="num">{{ todoNum }}</span>
        </div>
        <div class="dateBoxToDoItem" v-if="checkPermi(['sys:payment:list'])" @click="handleOpen('archives')">
          <span>一款一档</span>
          <span class="num">{{ paymentNum }}</span>
        </div>
        <div class="dateBoxToDoItem" v-if="checkPermi(['sys:warranty:list'])" @click="handleOpen('warranty')">
          <span>报修报废</span>
          <span class="num">{{ warrantyNum }}</span>
        </div>
        <div class="dateBoxToDoItem" v-if="checkPermi(['product:alertInventory:list'])" @click="handleJump()">
          <span>警戒库存</span>
          <span class="num">{{ alertInventoryNum }}</span>
        </div>
      </div>
    </div>
    <!-- 弹框 -->
    <el-dialog v-dialogDragBox :visible.sync="open" width="1150px" class="custom-dialog" @close="handleClose">
      <div slot="title">
        <div class="custom-dialog-title">
          <img :src="avatar" class="avatar" />
          <div class="flex flex-column">
            <div class="name">{{ info.realName || info.nickName || info.userName }}</div>
            <div class="dept" v-if="info.dept">部门：{{ info.dept.deptName }}</div>
          </div>
        </div>
      </div>
      <div class="content" v-loading="loading">
        <div class="content-left">
          <div class="content-left-top">
            <div class="flex align-center flex-justify-center">
              <div class="canlendar-btn" @click="prevMonth"><i class="el-icon-arrow-left"></i></div>
              <span class="canlendar-date">{{ currentYear }}年{{ currentMonth }}月</span>
              <div class="canlendar-btn" @click="nextMonth"><i class="el-icon-arrow-right"></i></div>
            </div>
            <div class="canlendar-box">
              <div class="flex flex-justify-end">
                <div class="calendar-button" @click="selectToday">定位至今日</div>
              </div>
              <div class="calendar-body">
                <div class="day-header">一</div>
                <div class="day-header">二</div>
                <div class="day-header">三</div>
                <div class="day-header">四</div>
                <div class="day-header">五</div>
                <div class="day-header">六</div>
                <div class="day-header">日</div>
                <div v-for="(day, index) in daysInMonth" :key="index"
                  :class="{ 'current-day': isCurrent(day.date), 'disabled-day': isDisabled(day.date) }" class="day-cell"
                  @click="selectDay(day.date)">{{ day.day }}</div>
              </div>
              <div class="lunar-box">
                <div class="lunar-text normal">{{ getLunarText() }}</div>
              </div>
            </div>
          </div>
          <div class="content-left-bottom">
            <el-checkbox-group v-model="calendarScope" class="flex flex-column">
              <el-checkbox v-for="item in calendarOptions" :key="item.value" :label="item.value">{{ item.label
              }}</el-checkbox>
            </el-checkbox-group>
          </div>
        </div>
        <div class="content-right" :class="{ active: isCurrent(item.date) }" v-for="item in currentWeekList"
          :key="item.day" @click="selectDay(item.date)">
          <div class="content-right-title">
            <span>{{ item.weekDay }}</span>
            <b>{{ item.day }}</b>
          </div>
          <div class="content-right-list">
            <el-scrollbar style="height: 100%">
              <template v-if="calendarScope.includes('todo')">
                <el-tooltip effect="dark" placement="top" v-for="ite in item.todo" :key="'todo' + ite.id">
                  <div slot="content" style="max-width: 200px">{{ ite.content }}</div>
                  <div class="list-item primary" @click="handleTo('todo')">
                    <div class="list-item-title">{{ ite.title }}</div>
                    <div class="list-item-content">{{ ite.content }}</div>
                  </div>
                </el-tooltip>
              </template>
              <template v-if="calendarScope.includes('meeting')">
                <div class="list-item yellow" v-for="ite in item.meeting" :key="'meeting' + ite.id"
                  @click="handleTo('meeting')">
                  <div class="list-item-title">{{ ite.meetingName }}</div>
                  <div class="list-item-content">开始时间：{{ ite.startTime }}</div>
                </div>
              </template>
              <!-- <template v-if="calendarScope.includes('workHandover')">
                <el-tooltip effect="dark" placement="top" v-for="ite in item.workHandover" :key="'workHandover' + ite.id">
                  <div slot="content" style="max-width: 200px">{{ ite.content }}</div>
                  <div class="list-item danger" @click="handleTo('workHandover')">
                    <div class="list-item-title">工作交接</div>
                    <div class="list-item-content">{{ ite.content }}</div>
                    <div style="font-size: 12px; color: #999">{{ ite.createBy }} &gt;&gt; {{ ite.handoverUser }}</div>
                  </div>
                </el-tooltip>
              </template> -->
              <template v-if="calendarScope.includes('workPlan')">
                <el-tooltip effect="dark" placement="top" v-for="ite in item.workPlan" :key="'workPlan' + ite.id">
                  <div slot="content" style="max-width: 200px">{{ ite.target }}</div>
                  <div class="list-item green" @click="handleTo('workPlan')">
                    <div class="list-item-title">{{ ite.project }}</div>
                    <div class="list-item-content">{{ ite.target }}</div>
                  </div>
                </el-tooltip>
              </template>
              <template v-if="calendarScope.includes('archives')">
                <el-tooltip effect="dark" placement="top" v-for="ite in item.archives" :key="'archives' + ite.id">
                  <div slot="content" style="max-width: 200px">{{ ite.customerName }}</div>
                  <div class="list-item blue" @click="handleTo('archives')">
                    <div class="list-item-title">一款一档</div>
                    <div class="list-item-content">{{ ite.customerName }}</div>
                  </div>
                </el-tooltip>
              </template>
              <template v-if="calendarScope.includes('warranty')">
                <el-tooltip effect="dark" placement="top" v-for="ite in item.warranty" :key="'warranty' + ite.id">
                  <div slot="content" style="max-width: 200px">{{ ite.productName }}</div>
                  <div class="list-item blue" @click="handleTo('warranty')">
                    <div class="list-item-title">报修报废</div>
                    <div class="list-item-content">{{ ite.productName }}</div>
                  </div>
                </el-tooltip>
              </template>
            </el-scrollbar>
          </div>
        </div>
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn primary" @click="open = false">关闭日历</el-button>
      </div>
    </el-dialog>

    <el-dialog v-dialogDragBox :visible.sync="workHandoverOpen" title="工作交接历史" width="1150px" class="calendar-dialog"
      @close="handleWorkHandoverClose">
      <div>
        <calendar :show-lunar="true" :workHandoverList="workHandoverList" :start-day="1" @date-select="handleDateSelect"
          @month-change="handleMonthChange" @toWorkHandover="toWorkHandover"/>
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn primary" style="width: 270px;"
          @click="workHandoverOpen = false">我知道了</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { vMiniWeatherIcon } from '@/components/weather'
import axios from 'axios'
import { getLunar } from 'chinese-lunar-calendar'
import { mapGetters } from 'vuex'
import { Lunar } from 'lunar-javascript'
import { todoCalendarList } from '@/api/todo'
import { checkPermi } from '@/utils/permission'
import { paymentAarchivesList } from '@/api/payment'
import { warrantyScrapList, warrantyScrapApprovalUsers } from '@/api/warranty'
import { inventoryAlertList } from '@/api/kingdee/inventory'
import { workHandoverList, workHandoverTodayLeave } from '@/api/workHandover'
import Calendar from '@/components/Calendar/index.vue'


export default {
  components: { vMiniWeatherIcon, Calendar },
  data() {
    return {
      weatherKey: 1,
      weather: { time: '', weathercode: '', temp: '', sd: '', WD: '', WS: '' }, // 天气
      location: { province: '', city: '', area: '' },
      indexDate: {},
      open: false,
      checked: true,
      currentYear: new Date().getFullYear(),
      currentMonth: new Date().getMonth() + 1,
      daysInMonth: [],
      currentDay: new Date(),
      currentWeekList: [],
      calendarList: [],
      calendarOptions: [
        { label: '显示挂牌督办', value: 'todo' },
        { label: '显示会议助手', value: 'meeting' },
        // { label: '显示工作交接', value: 'workHandover' },
        { label: '显示工作计划', value: 'workPlan' },
        { label: '显示一款一档', value: 'archives' },
        { label: '显示报修报废', value: 'warranty' }
      ],
      calendarScope: [],
      // 数量
      meetingNum: 0,
      workHandoverNum: 0,
      workPlanNum: 0,
      todoNum: 0,
      paymentNum: 0,
      warrantyNum: 0,
      alertInventoryNum: 0,
      closeDay: '',
      loading: true,
      userInfo: {},
      approvalObj: {},
      workHandoverOpen: false,
      workHandoverList: [],
    }
  },
  computed: {
    ...mapGetters(['avatar', 'info'])
  },
  created() {
    // 登陆人信息
    this.userInfo = this.$store.state.user.info
    // 默认选中所有
    // this.calendarScope = this.calendarOptions.map(item => item.value)
    this.currentDay = new Date()
    this.currentYear = this.currentDay.getFullYear()
    this.currentMonth = this.currentDay.getMonth() + 1
    // 查询天气
    this.getWeather()
    // 查询年月日
    this.getIndexDate()
    // 查询日历列表
    if (this.hasPermission()) this.getCalendarList()
    // // 查询一款一档
    // this.getPayment()
    // // 查询报修报废
    this.getApprovalList()
    // 查询警戒库存
    this.getAlertInventory()
    // 查询工作交接历史
    this.getWorkHandover()
  },
  methods: {
    getLunar,
    checkPermi,
    // 跳转至警戒库存
    handleJump() {
      this.$router.push({ path: '/kingdee/alertInventory' })
    },
    // 查询警戒库存
    getAlertInventory() {
      inventoryAlertList({ isValid: false }).then(res => {
        const { code, msg, total } = res
        if (code === 200) this.alertInventoryNum = total || 0
        else this.$message.error(msg)
      })
    },
    // 查询工作交接历史
    getWorkHandover() {
      workHandoverList({ pageNum: 1, pageSize: 1000, identity: 'handover', status: 0 }).then(res => {
        const { code, msg, rows } = res
        if (code === 200) {
          const workHandoverNum = rows?.length || 0
          this.workHandoverNum = workHandoverNum > 99 ? '99+' : workHandoverNum
        } else this.$message.error(msg)
      })
    },
    // 是否有权限
    hasPermission() {
      return this.checkPermi(['private:workPlan:list']) || this.checkPermi(['private:workJoin:list']) || this.checkPermi(['oa:todo:list']) || this.checkPermi(['oa:meeting:list']) || this.checkPermi(['sys:payment:list']) || this.checkPermi(['sys:warranty:list'])
    },
    // 打开日历
    async handleOpen(type = '') {
      if (type === 'workHandover') {
        const loading = this.$loading({
          lock: true,
          text: 'Loading',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        })
        this.workHandoverList = []
        const year = new Date().getFullYear()
        const month = new Date().getMonth() + 1
        const lastDay = new Date(year, month, 0)
        const daysInMonth = lastDay.getDate()
        let arr = []
        for (let i = 1; i <= daysInMonth; i++) {
          arr = await workHandoverTodayLeave({ date: `${year}-${month}-${i}` })
          this.workHandoverList.push(arr.data)
        }
        loading.close()
        this.workHandoverOpen = true
      } else {
        this.currentDay = new Date()
        this.currentYear = this.currentDay.getFullYear()
        this.currentMonth = this.currentDay.getMonth() + 1
        // 生成日期
        this.generateDays()
        if (type) {
          this.calendarScope = [type]
        }
        // 查询周列表
        if (!this.currentWeekList.some(item => item.date.getTime() === this.closeDay.getTime())) this.getWeekList()
        this.$nextTick(() => {
          this.open = true
        })
      }
    },
    // 查询日历列表
    getCalendarList() {
      if (!this.checkPermi(['private:workPlan:list'])) this.calendarOptions = this.calendarOptions.filter(item => item.value !== 'workPlan')
      if (!this.checkPermi(['private:workJoin:list'])) this.calendarOptions = this.calendarOptions.filter(item => item.value !== 'workHandover')
      if (!this.checkPermi(['oa:meeting:list'])) this.calendarOptions = this.calendarOptions.filter(item => item.value !== 'meeting')
      if (!this.checkPermi(['oa:todo:list'])) this.calendarOptions = this.calendarOptions.filter(item => item.value !== 'todo')
      if (!this.checkPermi(['sys:payment:list'])) this.calendarOptions = this.calendarOptions.filter(item => item.value !== 'archives')
      if (!this.checkPermi(['sys:warranty:list'])) this.calendarOptions = this.calendarOptions.filter(item => item.value !== 'warranty')
      const scope = this.calendarOptions.map(item => item.value).join(',')
      const date = this.parseTime(this.currentDay, '{y}-{m}-{d}')
      const params = { scope, date }
      todoCalendarList(params).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          const meetingNum = data?.meeting?.length || 0
          this.meetingNum = meetingNum > 99 ? '99+' : meetingNum
          // const workHandoverNum = data?.workHandover?.length || 0
          // this.workHandoverNum = workHandoverNum > 99 ? '99+' : workHandoverNum
          const workPlanNum = data?.workPlan?.length || 0
          this.workPlanNum = workPlanNum > 99 ? '99+' : workPlanNum
          const todoNum = data?.todo?.length || 0
          this.todoNum = todoNum > 99 ? '99+' : todoNum
          const paymentNum = data?.archives?.length || 0
          this.paymentNum = paymentNum > 99 ? '99+' : paymentNum
          const warrantyNum = data?.warranty?.length || 0
          this.warrantyNum = warrantyNum > 99 ? '99+' : warrantyNum
        } else this.$message.error(msg)
      })
    },
    // 获取一款一档待审批数量
    getPayment() {
      paymentAarchivesList({
        pageNum: 1,
        pageSize: 100,
        identity: 'approval',
        approvalStatus: 1
      }).then(res => {
        const { code, msg, rows, total } = res
        if (code === 200) {
          this.paymentNum = total
        } else this.$message.error(msg)
      })
    },
    // 获取报修报废待审批数量
    getWarranty() {
      warrantyScrapList({
        pageNum: 1,
        pageSize: 200
      }).then(res => {
        const { code, msg, rows, total } = res
        if (code === 200) {
          this.warrantyNum = 0
          let arr = rows.filter(item => item.status == 0 || item.status == 1)
          arr.forEach(el => {
            if (el.stage === 'zj') {
              if (this.approvalObj.zj.find(item => item.approvalId == this.userInfo.userId)) {
                this.warrantyNum = this.warrantyNum + 1
              }
            }
            if (el.stage === 'bm') {
              if (this.approvalObj.bm.find(item => item.approvalId == this.userInfo.userId)) {
                this.warrantyNum = this.warrantyNum + 1
              }
            }
            if (el.stage === 'zjb') {
              if (this.approvalObj.zjb.find(item => item.approvalId == this.userInfo.userId)) {
                this.warrantyNum = this.warrantyNum + 1
              }
            }
            if (el.stage === 'dsz') {
              if (this.approvalObj.dsz.find(item => item.approvalId == this.userInfo.userId)) {
                this.warrantyNum = this.warrantyNum + 1
              }
            }
            if (el.stage === 'cw') {
              if (this.approvalObj.cw.find(item => item.approvalId == this.userInfo.userId)) {
                this.warrantyNum = this.warrantyNum + 1
              }
            }
          })
        } else this.$message.error(msg)
      })
    },
    // 查询审核人员
    async getApprovalList() {
      const res = await warrantyScrapApprovalUsers()
      if (res.code === 200) {
        this.approvalObj = res.data
        // this.getWarranty()
      }
    },
    // 获取天气
    getWeather() {
      axios.get('https://www.ziyouke.net/weather/').then(res => {
        if (res.status === 200) {
          const { weather, location } = res.data.data
          this.weather = weather
          this.location = location
          this.weatherKey = Math.random()
        }
      })
    },
    // 获取年月日
    getIndexDate() {
      const date = new Date()
      const year = date.getFullYear()
      let month = date.getMonth() + 1
      month = month < 10 ? '0' + month : month
      let day = date.getDate()
      day = day < 10 ? '0' + day : day
      const week = date.getDay()
      let weekStr
      switch (week) {
        case 0:
          weekStr = '星期日'
          break
        case 1:
          weekStr = '星期一'
          break
        case 2:
          weekStr = '星期二'
          break
        case 3:
          weekStr = '星期三'
          break
        case 4:
          weekStr = '星期四'
          break
        case 5:
          weekStr = '星期五'
          break
        case 6:
          weekStr = '星期六'
          break
      }
      const lunar = getLunar(year, month, day)
      this.indexDate = { year, month, day, week: weekStr, lunar }
    },
    // 上个月
    prevMonth() {
      if (this.currentMonth === 1) {
        this.currentYear -= 1
        this.currentMonth = 12
      } else {
        this.currentMonth -= 1
      }
      this.generateDays()
    },
    // 下个月
    nextMonth() {
      if (this.currentMonth === 12) {
        this.currentYear += 1
        this.currentMonth = 1
      } else {
        this.currentMonth += 1
      }
      this.generateDays()
    },
    // 生成日期
    generateDays() {
      const daysInMonth = new Date(this.currentYear, this.currentMonth, 0).getDate()
      this.daysInMonth = []
      // 不是周一补充上个月
      const firstDayOfMonth = new Date(this.currentYear, this.currentMonth - 1, 1)
      const firstWeekday = (firstDayOfMonth.getDay() + 6) % 7 // 调整为周一为第一天
      const daysToAdd = firstWeekday === 0 ? 0 : firstWeekday
      const lastMonth = this.currentMonth === 1 ? 12 : this.currentMonth - 1
      const lastYear = this.currentMonth === 1 ? this.currentYear - 1 : this.currentYear
      const lastMonthDays = new Date(lastYear, lastMonth, 0).getDate()
      for (let i = lastMonthDays - daysToAdd + 1; i <= lastMonthDays; i++) {
        this.daysInMonth.push({ date: new Date(lastYear, lastMonth - 1, i), day: i, week: (firstWeekday + i - lastMonthDays) % 7 })
      }
      // 本月
      for (let day = 1; day <= daysInMonth; day++) {
        const date = new Date(this.currentYear, this.currentMonth - 1, day)
        const week = (date.getDay() + 6) % 7 // 调整为周一为第一天
        this.daysInMonth.push({ date, day, week })
      }
      // 下个月
      const lastDay = this.daysInMonth[this.daysInMonth.length - 1].date
      const lastWeekday = (lastDay.getDay() + 6) % 7 // 调整为周一为第一天
      const LastdaysToAdd = lastWeekday === 6 ? 0 : 6 - lastWeekday
      for (let i = 1; i <= LastdaysToAdd; i++) {
        const nextMonthDate = new Date(this.currentYear, this.currentMonth, i)
        this.daysInMonth.push({ date: nextMonthDate, day: i, week: (lastWeekday + i) % 7 })
      }
    },
    // 是否是当前日期
    isCurrent(date) {
      const currentDay = this.currentDay
      return date.getFullYear() === currentDay.getFullYear() && date.getMonth() === currentDay.getMonth() && date.getDate() === currentDay.getDate()
    },
    // 是否是本月日期
    isDisabled(date) {
      return date.getMonth() !== this.currentMonth - 1
    },
    // 选择日期
    selectDay(date) {
      this.currentDay = date
      if (this.isDisabled(date)) {
        this.currentYear = date.getFullYear()
        this.currentMonth = date.getMonth() + 1
        this.generateDays()
      }
      if (!this.currentWeekList.some(item => item.date.getTime() === date.getTime())) {
        this.getWeekList()
      }
    },
    // 定位至今日
    selectToday() {
      const date = new Date()
      this.currentDay = date
      if (this.isDisabled(date)) {
        this.currentYear = date.getFullYear()
        this.currentMonth = date.getMonth() + 1
        this.generateDays()
      }
      if (!this.currentWeekList.some(item => item.date.getTime() === date.getTime())) {
        this.getWeekList()
      }
    },
    // 回显农历
    getLunarText() {
      const d = Lunar.fromDate(new Date(this.currentDay))
      return `农历 ${d.getMonthInChinese()}月${d.getDayInChinese()}日,${d.getYearInGanZhi()}[${d.getYearShengXiao()}]年${d.getMonthInGanZhi()}月${d.getDayInGanZhi()}日`
    },
    // 宜
    getLunarYi() {
      const d = Lunar.fromDate(new Date(this.currentDay))
      const l = d.getDayYi() || []
      return '宜：' + l.join(' ')
    },
    // 忌
    getLunarJi() {
      const d = Lunar.fromDate(new Date(this.currentDay))
      const l = d.getDayJi() || []
      return '忌：' + l.join(' ')
    },
    // 查询周列表
    async getWeekList() {
      const startOfWeek = new Date(this.currentDay)
      // 将周一设置为一周的第一天
      const day = startOfWeek.getDay() || 7 // 如果是周日，getDay() 返回 0，调整为 7
      startOfWeek.setDate(this.currentDay.getDate() - day + 1) // 获取本周的第一天（周一）
      const week = []
      for (let i = 0; i < 7; i++) {
        const date = new Date(startOfWeek)
        date.setDate(startOfWeek.getDate() + i)
        const scope = this.calendarOptions.map(item => item.value).join(',')
        const params = { scope, date: this.parseTime(date, '{y}-{m}-{d}') }
        const res = await todoCalendarList(params)
        week.push({
          date: date,
          day: date.getDate(),
          weekDay: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'][date.getDay() - 1],
          meeting: res?.data?.meeting || [],
          workHandover: res?.data?.workHandover || [],
          workPlan: res?.data?.workPlan || [],
          todo: res?.data?.todo || [],
          archives: res?.data?.archives || [],
          warranty: res?.data?.warranty || []
        })
      }
      this.currentWeekList = week
      this.loading = false
    },
    // 关闭
    handleClose() {
      this.closeDay = this.currentDay
    },
    handleWorkHandoverClose() {
      this.workHandoverOpen = false
    },
    // 跳转
    handleTo(type) {
      switch (type) {
        case 'todo':
          this.$router.push({ path: '/oa/listing' })
          this.open = false
          break
        case 'meeting':
          this.$router.push({ path: '/private/meeting' })
          this.open = false
          break
        case 'workHandover':
          this.$router.push({ path: '/oa/workJoin' })
          this.open = false
          break
        case 'workPlan':
          this.$router.push({ path: '/oa/workPlan' })
          this.open = false
          break
        case 'archives':
          this.$router.push({ path: '/private/payment' })
          this.open = false
          break
        case 'warranty':
          this.$router.push({ path: '/oa/warranty' })
          this.open = false
          break
      }
    },

    async handleDateSelect(day) {
      console.log(day)
      if (!day.isCurrentMonth) {
        this.workHandoverList = []
        const year = day.fullDate.split('-')[0]
        const month = day.fullDate.split('-')[1]
        const lastDay = new Date(year, month, 0)
        const daysInMonth = lastDay.getDate()
        let arr = []
        for (let i = 1; i <= daysInMonth; i++) {
          arr = await workHandoverTodayLeave({ date: `${year}-${month}-${i}` })
          this.workHandoverList.push(arr.data)
        }
      }
    },
    async handleMonthChange({ year, month }) {
      console.log('切换至:', year, '年', month, '月')
      this.workHandoverList = []
      const lastDay = new Date(year, month, 0)
      const daysInMonth = lastDay.getDate()
      let arr = []
      for (let i = 1; i <= daysInMonth; i++) {
        arr = await workHandoverTodayLeave({ date: `${year}-${month}-${i}` })
        this.workHandoverList.push(arr.data)
      }
    },
    toWorkHandover() {
      this.workHandoverOpen = false
      this.$router.push({ path: '/oa/workJoin' })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';

.shadow {
  box-shadow: 0 0 26px 0 rgba(0, 0, 0, 0.05);
}

.weatherDate {
  display: flex;
  align-items: center;

  .weatherBox {
    cursor: pointer;
    min-width: 58%;
    max-width: 60%;
    min-height: 208px;
    background: url('~@/assets/images/weather_bg.png') center no-repeat;
    background-size: 100% 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 20px;
    position: relative;

    &Time {
      font-size: 12px;
      color: #666666;
      position: absolute;
      top: 20px;
      left: 20px;
    }

    &Flex {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-wrap: wrap;
    }

    &Icon {
      height: 100px;
      overflow: hidden;
    }

    &Temp {
      display: flex;
      align-items: center;

      b {
        font-size: 50px;
        font-weight: normal;
        color: #333333;
      }

      span {
        font-size: 40px;
      }
    }

    &Border {
      border-top: 1px solid #cbd6e2;
      padding-top: 10px;
    }

    &SD {
      font-size: 14px;
      line-height: 30px;
      color: #666666;
      margin-right: 30px;
      padding-left: 20px;
      background: url('~@/assets/images/weather_sd.png') left center no-repeat;
    }

    &WDE {
      font-size: 14px;
      line-height: 30px;
      color: #666666;
      padding-left: 25px;
      background: url('~@/assets/images/weather_wde.png') left center no-repeat;
    }

    &A {
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      line-height: 30px;
      color: #666666;

      span {
        display: flex;
        align-items: center;

        &:after {
          content: '·';
          font-size: 30px;
          margin: 0 3px;
        }

        &:first-child {
          padding-left: 20px;
          background: url('~@/assets/images/weather_address.png') left center no-repeat;
        }

        &:last-child {
          &:after {
            content: '';
          }
        }
      }
    }
  }
}

.dateBox {
  width: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  &Time {
    font-size: 24px;
    line-height: 28px;

    .year {
      color: #666666;
    }

    .day {
      color: #2e73f3;
    }
  }

  &Week {
    font-size: 16px;
    line-height: 20px;
    margin-top: 10px;
    margin-bottom: 20px;
    color: #666666;
  }

  &China {
    font-size: 14px;
    line-height: 20px;
    color: #666666;
    padding: 0 20px;
  }

  &ToDo {
    background-color: #f2f4f6;
    border-radius: 5px;
    padding: 5px 10px;
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin: 20px 0px 0px;

    &Item {
      display: flex;
      align-items: center;
      //width: 25%;
      min-width: 75px;
      font-size: 12px;
      line-height: 30px;
      color: #666666;
      cursor: pointer;
      justify-content: center;

      &:hover {
        color: #2e73f3;
      }

      .num {
        line-height: 18px;
        background-color: #f43f3f;
        border-radius: 40px;
        color: #ffffff;
        padding: 0 5px;
        margin-left: 5px;
      }
    }
  }
}

::v-deep {
  .custom-dialog {
    .el-dialog {
      position: fixed !important;
      top: 50% !important;
      left: 50% !important;
      transform: translate(-50%, -50%) !important;
      margin-top: 0 !important;

      .el-dialog__header {
        height: 130px;

        .custom-dialog-title {
          padding: 20px 0;
          display: flex;
          align-items: center;

          .avatar {
            width: 90px;
            height: 90px;
            border-radius: 50%;
            margin-right: 15px;
            flex: 0 0 90px;
          }

          .name {
            font-size: 18px;
            line-height: 30px;
            color: #333333;
            font-weight: 500;
          }

          .dept {
            font-size: 14px;
            line-height: 30px;
            color: #666666;
          }
        }

        .el-dialog__headerbtn {
          position: absolute;
        }
      }

      .el-dialog__body {
        max-height: calc(98vh - 130px - 80px) !important;
        overflow-x: hidden;
        overflow-y: auto;
      }
    }
  }
}

.content {
  margin: 0 20px;
  border: 1px solid #cbd6e2;
  border-radius: 5px;
  display: flex;
  overflow: hidden;

  &-left {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    background-color: #f8f9fb;

    &-top {
      border-bottom: 1px solid #cbd6e2;
      display: flex;
      flex-direction: column;
      padding-top: 30px;

      .canlendar-btn {
        cursor: pointer;
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        background-color: #e1e4e8;
        color: #666666;
        font-size: 20px;

        &:hover {
          color: #2e73f3;
          background-color: #dfebff;
        }
      }

      .canlendar-date {
        font-size: 20px;
        line-height: 20px;
        font-weight: 500;
        color: #333333;
        margin: 0 15px;
      }

      .canlendar-text {
        font-size: 14px;
        line-height: 32px;
        color: #666666;
      }

      .canlendar-num {
        font-weight: 500;
        color: #2e73f3;
        margin: 0 5px;
      }

      .canlendar-box {
        margin: 10px 5px 20px;
        background-color: #ffffff;
        border-radius: 5px;
        border: 1px solid #cbd6e2;
      }

      .calendar-button {
        font-size: 14px;
        line-height: 20px;
        color: #999999;
        padding: 5px 15px;
        border: 1px solid #cbd6e2;
        border-radius: 5px;
        margin: 10px 20px 10px 0;
        cursor: pointer;

        &:hover {
          color: #2e73f3;
          border-color: #2e73f3;
        }
      }

      .calendar-body {
        display: grid;
        grid-template-columns: repeat(7, 1fr);
        grid-gap: 5px;
        border-top: 1px solid #cbd6e2;
        border-bottom: 1px solid #cbd6e2;
        padding: 0 5px 5px;
      }

      .day-header,
      .day-cell {
        text-align: center;
        line-height: 30px;
        box-sizing: border-box;
        cursor: pointer;
        border-radius: 50%;
      }

      .day-cell:hover,
      .current-day {
        background-color: #2e73f3;
        color: #ffffff;
      }

      .disabled-day {
        color: #cccccc;
      }

      .lunar-box {
        padding: 10px 15px 15px;
        display: flex;
        flex-direction: column;

        .lunar-text {
          font-size: 14px;
          line-height: 25px;
          color: #333333;

          &.normal {
            color: #666666;
          }

          &.danger {
            color: #f43f3f;
          }
        }
      }
    }

    &-bottom {
      padding: 15px;
      display: flex;
      flex-direction: column;

      .el-checkbox {
        margin: 8px 0;
      }
    }
  }

  &-right {
    width: 120px;
    display: flex;
    flex-direction: column;
    position: relative;
    border: 1px solid transparent;
    border-left-color: #cbd6e2;

    &-title {
      height: 130px;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      border-bottom: 1px solid #cbd6e2;

      span {
        font-weight: 500;
        font-size: 18px;
        line-height: 20px;
        color: #666666;
      }

      b {
        font-weight: 400;
        font-size: 24px;
        line-height: 28px;
        color: #999999;
        margin-top: 20px;
      }
    }

    &-list {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow-y: auto;
      padding: 5px;
      scrollbar-width: none;

      &::-webkit-scrollbar {
        display: none !important;
      }

      .list-item {
        cursor: pointer;
        display: flex;
        flex-direction: column;
        padding: 5px 10px 20px 10px;
        border-radius: 5px;
        position: relative;
        margin-bottom: 5px;

        &-title {
          font-size: 12px;
          color: #666666;
          line-height: 20px;
          margin-bottom: 5px;
        }

        &-content {
          font-size: 14px;
          line-height: 20px;
          color: #333333;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          /* 限制显示两行 */
          -webkit-box-orient: vertical;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        &::before {
          content: '';
          width: 1px;
          height: calc(100% - 20px);
          position: absolute;
          left: 0;
          top: 10px;
        }

        &.primary {
          background-color: #f0f4ff;

          &::before {
            background-color: #2e73f3;
          }
        }

        &.yellow {
          background-color: #fff6ed;

          &::before {
            background-color: #ffa90e;
          }
        }

        &.danger {
          background-color: #fff1f4;

          &::before {
            background-color: #ff4a7a;
          }
        }

        &.green {
          background-color: #f0f8f3;

          &::before {
            background-color: #44bc71;
          }
        }
      }
    }

    &.active {
      border: 1px solid #2e73f3;

      .content-right-title {
        border-bottom-color: #2e73f3;
        background-color: #dfebff;

        span {
          color: #2e73f3;
        }

        b {
          color: #2e73f3;
        }
      }
    }
  }
}

.margin-top-10 {
  margin-top: 10px;
}

.calendar-dialog {
  ::v-deep {
    .el-dialog__body {
      padding: 20px;
    }
  }
}
</style>
