<template>
    <div class="container" @click.self="handleClick">
      <div class="head">
        <div>
          <div style="width: 200px;">
            <img src="../../public/imgs/<EMAIL>" style="width: 52px;height: 50px;margin: auto;"/>
            <div style="margin: auto;font-size: 16px;color: white;"><span style="font-size:19px">自由客紧固件</span><br>一站式采购平台</div>
          </div>
          <div style="width: 800px;">
            <div
              style="width: 666px;height: 50px;background-color:white;margin: auto;border-radius: 100px;position:relative;overflow: hidden;">
              <div @click="search()"
                   style="cursor: pointer;width: 117px;height: 40px;background-color: #2E73F3;border-radius: 100px;position:absolute;right:5px;top: 5px;display: flex;">
                <img src="../../public/imgs/搜索 <EMAIL>"
                     style="width: 20px;height: 20px;margin-left: 29px;margin-top: 10px;">
                <div style="margin: auto;font-size: 16px;color: white;line-height: 20px;margin-left: 7px;">搜索</div>
              </div>
              <input v-model="stext" placeholder="请输入产品名称"
                     style="width: 100%;height: 100%;border: 0;outline: none;text-indent:25px;color: #999999;">
            </div>
          </div>

          <div style="width: 200px;" v-if="!islogin">
            <div style="margin: auto;font-size: 16px;color: white;cursor: pointer;" @click="login()">用户登录</div>
          </div>
          <div style="width: 200px;" v-else @click="toindex()">
            <img src="../../public/imgs/消息 (1) <EMAIL>" style="width: 22px;height: 22px;margin:auto;"/>
            <div style="width: 50px;height: 50px;border-radius: 100px;margin: auto;overflow: hidden;">
              <img src="https://img2.baidu.com/it/u=2421090168,324781765&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500" style="width:100%;height:100%;">
            </div>
            <div style="margin: auto;font-size: 16px;color: white;cursor: pointer;">管理中心</div>
            <!-- 	<img src="../../public/imgs/下拉展开@2x.png" style="width: 18px;height:18px;margin:auto;"/> -->
          </div>
        </div>
      </div>
      <!-- page -->
      <div
        style="width:1200px;height:36px;background: white;box-shadow: 0px 1px 5px 0px rgba(0,0,0,0.05);line-height: 36px;text-indent: 20px;text-align: left;display: flex;margin: auto;">
        <div style="width: 200px;height: 100%;color: #292F48;font-size: 12px;">自由客紧固件一站式采购平台</div>
      </div>

    <!-- banner -->
      <div class="banner">
        <div>关于我们</div>
      </div>

      <!-- 公司简介 -->
      <div class="jj"></div>

  </div>





  </template>
  <script>
  //导入类目
  import * as leimu from "@/api/purchase/category";
  import Cookies from 'js-cookie';
  import {getToken} from "@/utils/auth";
  import {
    getNewMessage,
    getRelevanceCompany,
    getLeftAd,
    getDownAd,
    getHotProduct,
    getNewProducts,
    getTypeProducts
  } from "@/api/system/product";

  export default {
    data() {
      return {
        islogin: false,
        stext:null
      };
    },
    mounted() {



    },
    created() {
      //判断用户是否存在
      this.islogin = getToken() ? true : false;
    },
    methods: {
      //搜索
      search() {
        this.getProductsByName({productName: this.stext});
      },



      login() {
        this.$router.push("/login");
      },
      toindex() {
        this.$router.push("/index");
      },
      navTo(boo) {
        if (!boo) {
          this.$router.push({
            path: "/product-pc-search",
            query: {
              productName: this.productName,
            },
          });
        } else {
          this.$router.push({
            path: "/login",
          });
        }
      },

      // 获取新消息
      async getNewMessage() {
        const res = await getNewMessage();
        this.messageList = res.data;
      },
    }
  };
  </script>
  <style scoped>

  body {
    min-width: 1200px;
    background: #F9F9F9;
    /* padding-bottom: 50px; */
  }

  .head {
    width: 100%;
    height: 112px;
    background: #2E73F3;
    overflow: hidden;
  }

  .head > div {
    margin: 0 auto;
    width: 1200px;
    height: 100%;
    display: flex;
    justify-content: space-between;
  }

  .head > div > div {
    margin: auto;
    border: 0px solid;
    height: 100%;
    display: flex;
  }

.banner{
    width: 100%;
    height: 380px;
    background-image: url("../../public/imgs/Rectangle 33.png");
    background-repeat: no-repeat;
    background-position: center;
    display: flex;
    flex-wrap: wrap;
}
.banner>div:first-child{
    width: 100%;
    margin: auto;
    font-size: 40px;
    color: white;
    text-align: center;
}

.jj{
    width: 100%;
    height: 380px;
    border: 1px solid;
}

  </style>
