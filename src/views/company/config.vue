<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px" @submit.native.prevent>
      <el-form-item label="参数名称" prop="configName">
        <el-input v-model="queryParams.configName" placeholder="请输入参数名称" clearable style="width: 240px"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">新增</el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :data="list" style="width: 100%" class="custom-table custom-table-cell10">
      <el-table-column label="序号" align="center" type="index" width="60" />
      <el-table-column label="参数名称" align="center" prop="configName" :show-overflow-tooltip="true" />
      <el-table-column label="参数键名" align="center" prop="configKey" :show-overflow-tooltip="true" />
      <el-table-column label="参数键值" align="center" prop="configValue" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span v-if="scope.row.configKey === 'todo.items.supervise' || scope.row.configKey === 'crm.admin.user'">{{ filterName(scope.row) }}</span>
          <span v-else>{{ scope.row.configValue }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" :disabled="scope.row.configKey === 'signatures'"
            @click="handleUpdate(scope.row)">修改</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改参数配置对话框 -->
    <el-dialog v-dialogDragBox :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="选择参数" v-if="isAdd">
          <el-select v-model="form.configKey" @change="handleChange" placeholder="请选择选择参数" style="width: 100%">
            <el-option v-for="(item, index) in canList" :key="index" :label="item.configName"
              :value="item.configKey"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="参数名称" prop="configName">
          <el-input v-model="form.configName" readonly placeholder="请输入参数名称" />
        </el-form-item>
        <el-form-item label="参数键名" prop="configKey">
          <el-input v-model="form.configKey" :readonly="isAdd" placeholder="请输入参数键名" />
        </el-form-item>
        <el-form-item label="参数键值" prop="configValue"
          v-if="form.valueType !== 'png' && form.configKey !== 'todo.items.supervise' && form.configKey !== 'crm.admin.user'">
          <el-input v-model="form.configValue" placeholder="请输入参数键值" />
        </el-form-item>
        <el-form-item label="参数键值" prop="configValue" v-if="form.configKey === 'todo.items.supervise' || form.configKey === 'crm.admin.user'">
          <el-select v-model="form.configValue" filterable multiple placeholder="请选择参数键值" clearable
            style="width: 100%;">
            <el-option v-for="(item, index) in userList" :key="index" :label="item.nickName"
              :value="String(item.userId)" />
          </el-select>
        </el-form-item>
        <el-form-item label="参数键值" prop="configValue" v-if="form.valueType === 'png'">
          <div class="imageBox" v-if="form.configValue">
            <img :src="form.configValue" />
            <i class="el-icon-error imageBox-delete" @click="form.configValue = undefined"></i>
          </div>
          <el-upload list-type="picture-card" ref="upload" accept=".png" :limit="1" action="#" :auto-upload="false"
            :on-change="getFile" v-if="!form.configValue">
            <i class="el-icon-plus"></i>
            <div slot="tip" class="el-upload__tip">只能上传png文件，且不超过2MB</div>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getConfigList, addConfig, updateConfig } from '@/api/config'
import { listUser } from '@/api/system/user'

export default {
  name: 'Config',
  dicts: ['sys_yes_no'],
  data() {
    return {
      loading: true,
      total: 0,
      list: [],
      canList: [],
      title: '',
      open: false,
      isAdd: true,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        configName: undefined
      },
      form: {},
      rules: {
        configName: [{ required: true, message: '参数名称不能为空', trigger: 'blur' }],
        configKey: [{ required: true, message: '参数键名不能为空', trigger: 'blur' }],
        configValue: [{ required: true, message: '参数键值不能为空', trigger: 'blur' }]
      },
      value: '',
      fileList: [],
      userList: []
    }
  },
  created() {
    this.getApprovalsOptions()
    this.getList()
  },
  methods: {
    // 部门和人员
    async getApprovalsOptions() {
      const user = await listUser()
      this.userList = user.rows || []

    },
    // 过滤姓名
    filterName(rows) {
      if (rows.configValue && typeof(rows.configValue) === 'string') {
        let arr = []
        rows.configValue.split(',').forEach(el => {
          arr.push(this.userList.find(item => item.userId == el).nickName)
        });
        return arr.join(',')
      }
    },
    /** 查询参数列表 */
    getList() {
      this.loading = true
      getConfigList(this.queryParams).then(res => {
        const { code, msg, rows, total, ext } = res
        if (code === 200) {
          this.list = rows
          this.canList = (ext.hasOwnProperty('CanAddConfig') && ext.CanAddConfig) || []
          this.total = total
          this.loading = false
        } else this.$message.error(msg)
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        configId: undefined,
        configName: undefined,
        configKey: undefined,
        configValue: undefined,
        valueType: undefined
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      if (this.canList.length > 0) {
        this.isAdd = true
        this.reset()
        this.open = true
        this.title = '添加参数'
      } else this.$message.error('没有可新增的参数')
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      this.isAdd = false
      const form = JSON.parse(JSON.stringify(row))
      if (form.configKey === 'todo.items.supervise' || form.configKey === 'crm.admin.user') {
        form.configValue = form.configValue && form.configValue.split(',')
      }
      this.form = form
      this.open = true
      this.title = '修改参数'
    },
    handleChange(e) {
      const item = this.canList.find(v => v.configKey === e)
      this.form.configName = item.configName || ''
      this.form.valueType = item.valueType || ''
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.configKey === 'todo.items.supervise') {
            this.form.configValue = this.form.configValue.join(',')
          }
          if(this.form.configKey === 'crm.admin.user') {
            this.form.configValue = this.form.configValue.toString()
          }
          if (this.form.configId != undefined) {
            updateConfig(this.form).then(res => {
              this.$modal.msgSuccess('修改成功')
              this.open = false
              this.getList()
            })
          } else {
            addConfig(this.form).then(res => {
              this.$modal.msgSuccess('新增成功')
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    getFile(file, fileList) {
      if (this.beforeAvatarUpload(file)) {
        this.getBase64(file.raw).then(res => {
          this.form.configValue = res
        })
      }
    },
    getBase64(file) {
      return new Promise(function (resolve, reject) {
        const reader = new FileReader()
        let imgResult = ''
        reader.readAsDataURL(file)
        reader.onload = function () {
          imgResult = reader.result
        }
        reader.onerror = function (error) {
          reject(error)
        }
        reader.onloadend = function () {
          resolve(imgResult)
        }
      })
    },
    beforeAvatarUpload(file) {
      const isLt2M = file.size / 1024 / 1024 < 2
      if (!isLt2M) {
        this.$message.error('上传电子章大小不能超过 2MB!')
      }
      return isLt2M
    }
  }
}
</script>
<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';

.p20 {
  padding: 20px;
}

.imageBox {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  width: 148px;
  height: 148px;
  cursor: pointer;
  line-height: 146px;
  vertical-align: top;
  position: relative;

  img {
    width: 100%;
    height: 100%;
    border-radius: 6px;
  }

  &-delete {
    position: absolute;
    right: -10px;
    top: -10px;
    font-size: 20px;
  }
}
</style>
