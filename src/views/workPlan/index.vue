<template>
    <div class="newBox bgcf9 vh-85">
        <!-- 搜索 -->
        <div class="custom-search flex" style="padding-top: 18px">
            <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
                <el-form-item label="项目名称" prop="project">
                    <el-input v-model="queryParams.project" placeholder="请输入项目名称" clearable
                        @keyup.enter.native="handleQuery" />
                </el-form-item>
                <el-form-item label="周期" prop="week" style="position: relative;">
                    <el-date-picker v-model="weekValue" type="week" format="yyyy 第 WW 周" placeholder="选择周"
                        :clearable="false" :picker-options="weekOptions" @change="changeWeek">
                    </el-date-picker>
                    <i :class="nextTotal > 0 ? 'el-icon-caret-bottom' : 'el-icon-caret-top'" class="weekIcon"></i>
                </el-form-item>
                <el-form-item label="完成情况" prop="completionStatus">
                    <el-select v-model="queryParams.completionStatus" placeholder="请选择完成情况" clearable
                        @change="handleQuery">
                        <el-option v-for="(item, index) in debtModeOptions" :key="index" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="部门" prop="deptName" v-if="queryParams.identity == 'approval'">
                    <el-select v-model="queryParams.deptName" placeholder="请选择部门" clearable @change="handleQuery">
                        <el-option v-for="(item, index) in deptOptions" :key="index" :label="item.label"
                            :value="item.label" />
                    </el-select>
                </el-form-item>
                <el-form-item label="负责人" prop="superintendent" v-if="queryParams.identity == 'approval'">
                    <el-select v-model="queryParams.superintendent" placeholder="请选择负责人" clearable
                        @change="handleQuery">
                        <el-option v-for="(item, index) in salespersonOptions" :key="index" :label="item.nickName"
                            :value="item.userId" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" icon="el-icon-search"
                        style="background-color: #2e73f3; border-color: #2e73f3" size="small"
                        @click="handleQuery">搜索</el-button>
                    <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
                    <button type="button" class="custom-search-add pointer" @click="handleThisAdd"
                        v-if="queryParams.identity == 'creator'">
                        <i class="el-icon-plus"></i>
                        补充本周工作计划
                    </button>
                    <button type="button" class="custom-search-add pointer" @click="handleNextAdd"
                        v-if="queryParams.identity == 'creator'">
                        <i class="el-icon-plus"></i>
                        新增下周工作计划
                    </button>
                </el-form-item>
                <el-form-item label="是否超期" prop="completionStatus">
                    <el-select v-model="queryParams.term" placeholder="请选择是否超期" clearable @change="termChange">
                        <el-option v-for="(item, index) in termOptions" :key="index" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
            </el-form>
        </div>

        <!-- 列表 -->
        <div class="p20">
            <el-tabs v-model="queryParams.identity" @tab-click="tabChange">
                <el-tab-pane label="创建" :name="'creator'"></el-tab-pane>
                <el-tab-pane label="负责" :name="'superintendent'"></el-tab-pane>
                <el-tab-pane label="审核" :name="'approval'" v-if="checkPermi(['work.plan.all'])"></el-tab-pane>
            </el-tabs>
            <el-table v-loading="loading" stripe :data="list" row-key="id" style="width: 100%" class="custom-table"
                v-if="total > 0">
                <el-table-column align="center" type="index" label="序号"></el-table-column>
                <el-table-column align="center" label="部门" prop="deptName" show-overflow-tooltip
                    width="100"></el-table-column>
                <el-table-column align="center" label="周期" show-overflow-tooltip width="150">
                    <template slot-scope="{ row }">
                        <div>{{ row.year + '年第' + row.weekOfYear + '周工作计划' }}</div>
                    </template>
                </el-table-column>
                <el-table-column align="center" prop="project" label="工作项目" show-overflow-tooltip></el-table-column>
                <el-table-column align="center" prop="target" label="工作内容详细" show-overflow-tooltip></el-table-column>
                <el-table-column align="center" label="计划完成日期" show-overflow-tooltip>
                    <template slot-scope="{ row }">
                        <!-- <div>
                            <span v-if="row.plannedType == 'specify'">{{ row.plannedDate }}</span>
                            <span v-if="row.plannedType == 'uncertain'">{{ '不定' }}</span>
                            <span v-if="row.plannedType == 'day'">{{ '每天' }}</span>
                            <span v-if="row.plannedType == 'week'">{{ '每周' }}</span>
                            <span v-if="row.plannedType == 'month'">{{ '每月' }}</span>
                        </div> -->
                        <div class="finishTime_box">
                            <div class="time">{{ row.plannedDate }}</div>
                            <div class="tips"
                                v-if="new Date(row.plannedDate) < new Date() && (row.completionStatus == 'continued' || row.completionStatus == 'part')">
                                <img src="~@/assets/images/remind.png" alt="">
                                <span>已超期</span>
                            </div>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column align="center" label="实际完成日期" show-overflow-tooltip
                    v-if="queryParams.completionStatus == 'done' || queryParams.completionStatus == 'done2'">
                    <template slot-scope="{ row }">
                        <div>
                            <span>{{ row.completionTime }}</span>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column align="center" label="完成情况" show-overflow-tooltip width="160">
                    <template slot-scope="{ row }">
                        <div class="select_textColor">
                            <el-select v-model="row.completionStatus" placeholder="请选择" size="mini"
                                :disabled="queryParams.identity != 'creator' || queryParams.week < weekOfYearInfo.week"
                                :class="row.completionStatus == 'continued' ? 'orange' : (row.completionStatus == 'part' ? 'blue' : (row.completionStatus == 'done' ? 'green' : (row.completionStatus == 'done2' ? 'yellow' : '')))"
                                @change="handleUpStatus(row, false)">
                                <el-option :label="'进行中'" :value="'continued'" style="color: #F35D09;"></el-option>
                                <el-option :label="'部分完成'" :value="'part'" style="color: #2E73F3;"></el-option>
                                <el-option :label="'已完成'" :value="'done'" style="color: #2BCC75;"></el-option>
                                <!-- <el-option :label="'已完成，未发放'" :value="'done2'" style="color: #ec9c01;"></el-option> -->
                            </el-select>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column align="center" prop="superintendentName" label="负责人" show-overflow-tooltip
                    width="100"></el-table-column>
                <el-table-column align="center" prop="remark" label="备注" show-overflow-tooltip></el-table-column>
                <el-table-column align="center" prop="prop" label="操作" width="250">
                    <template slot-scope="{ row }">
                        <el-button type="primary" plain @click="handleView(row, 'view')" size="mini">查看详情</el-button>
                        <el-button type="primary" plain @click="handleEdit(row)" size="mini"
                            v-if="queryParams.identity == 'creator' && (queryParams.year > weekOfYearInfo.year || (queryParams.year == weekOfYearInfo.year && queryParams.week >= weekOfYearInfo.week))">编辑</el-button>
                        <el-button type="danger" plain @click="handleDelete(row)" size="mini"
                            v-if="queryParams.identity == 'creator' || (queryParams.identity == 'approval' && checkPermi(['work:plan:del']))">删除</el-button>
                        <el-button type="primary" plain @click="handleView(row, 'approval', 'add')" size="mini"
                            v-if="queryParams.identity == 'approval' && checkPermi(['work.plan.all']) && row.approvalList.findIndex(item => item.approvalId == userInfor.userId) == -1">审批</el-button>
                        <el-button type="primary" plain @click="handleView(row, 'approval', 'edit')" size="mini"
                            v-if="queryParams.identity == 'approval' && checkPermi(['work.plan.all']) && row.approvalList.findIndex(item => item.approvalId == userInfor.userId) != -1">修改</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <el-empty v-else />
            <div class="custom-pagination" v-if="queryParams.term == '0'">
                <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
                    :limit.sync="queryParams.pageSize" @pagination="getList" />
            </div>
        </div>

        <!-- 复盘本周工作计划 -->
        <el-dialog v-dialogDragBox :title="'复盘本周计划'" :visible.sync="replayShow" class="replay_box" width="1152px">
            <div class="replay_dept">{{ userInfor.dept && userInfor.dept.deptName }}</div>

            <div class="replay_list">
                <div v-if="nextTotal > 0">已建下周工作计划</div>
                <el-table stripe :data="nextWorkList" row-key="id" style="width: 100%; margin-bottom: 20px"
                    max-height="300" class="custom-table" v-if="nextTotal > 0">
                    <el-table-column align="center" type="index" label="序号"></el-table-column>
                    <el-table-column align="center" label="周期" show-overflow-tooltip>
                        <template slot-scope="{ row }">
                            <div>{{ row.year + '年第' + row.weekOfYear + '周工作计划' }}</div>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" prop="project" label="工作项目" show-overflow-tooltip></el-table-column>
                    <el-table-column align="center" prop="target" label="工作内容详细"
                        show-overflow-tooltip></el-table-column>
                    <el-table-column align="center" label="完成日期" show-overflow-tooltip>
                        <template slot-scope="{ row }">
                            <div>
                                <span v-if="row.plannedType == 'specify'">{{ row.plannedDate }}</span>
                                <span v-if="row.plannedType == 'uncertain'">{{ '不定' }}</span>
                                <span v-if="row.plannedType == 'day'">{{ '每天' }}</span>
                                <span v-if="row.plannedType == 'week'">{{ '每周' }}</span>
                                <span v-if="row.plannedType == 'month'">{{ '每月' }}</span>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" label="完成情况" show-overflow-tooltip>
                        <template slot-scope="{ row }">
                            <div class="select_textColor">
                                <span v-if="row.completionStatus == 'continued'" style="color: #F35D09;">{{ '进行中'
                                    }}</span>
                                <span v-if="row.completionStatus == 'part'" style="color: #2E73F3;">{{ '部分完成' }}</span>
                                <span v-if="row.completionStatus == 'done'" style="color: #2BCC75;">{{ '已完成' }}</span>
                                <!-- <span v-if="row.completionStatus == 'done2'" style="color: #ec9c01;">{{ '已完成，未发放'
                                    }}</span> -->
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" prop="superintendentName" label="负责人"
                        show-overflow-tooltip></el-table-column>
                    <el-table-column align="center" prop="remark" label="备注" show-overflow-tooltip></el-table-column>
                </el-table>
                <div>复盘本周工作计划</div>
                <el-table ref="multipleTable" stripe :data="replayList" row-key="id" style="width: 100%"
                    :max-height="nextTotal > 0 ? '300' : '600'" class="custom-table" v-if="replayList.length > 0"
                    @selection-change="handleSelectionChange">
                    <el-table-column align="center" type="selection" width="55"
                        :selectable="selectable"></el-table-column>
                    <el-table-column align="center" type="index" label="序号"></el-table-column>
                    <el-table-column align="center" label="周期" show-overflow-tooltip>
                        <template slot-scope="{ row }">
                            <div>{{ row.year + '年第' + row.weekOfYear + '周工作计划' }}</div>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" prop="project" label="工作项目" show-overflow-tooltip></el-table-column>
                    <el-table-column align="center" prop="target" label="工作内容详细"
                        show-overflow-tooltip></el-table-column>
                    <el-table-column align="center" label="完成日期" show-overflow-tooltip>
                        <template slot-scope="{ row }">
                            <div>
                                <span v-if="row.plannedType == 'specify'">{{ row.plannedDate }}</span>
                                <span v-if="row.plannedType == 'uncertain'">{{ '不定' }}</span>
                                <span v-if="row.plannedType == 'day'">{{ '每天' }}</span>
                                <span v-if="row.plannedType == 'week'">{{ '每周' }}</span>
                                <span v-if="row.plannedType == 'month'">{{ '每月' }}</span>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" label="完成情况" show-overflow-tooltip>
                        <template slot-scope="{ row }">
                            <div class="select_textColor">
                                <el-select v-model="row.completionStatus" placeholder="请选择" size="mini"
                                    :disabled="queryParams.identity != 'creator'"
                                    :class="row.completionStatus == 'continued' ? 'orange' : (row.completionStatus == 'part' ? 'blue' : (row.completionStatus == 'done' ? 'green' : (row.completionStatus == 'done2' ? 'yellow' : '')))"
                                    @change="handleUpStatus(row, true)">
                                    <el-option :label="'进行中'" :value="'continued'" style="color: #F35D09;"></el-option>
                                    <el-option :label="'部分完成'" :value="'part'" style="color: #2E73F3;"></el-option>
                                    <el-option :label="'已完成'" :value="'done'" style="color: #2BCC75;"></el-option>
                                    <!-- <el-option :label="'已完成，未发放'" :value="'done2'" style="color: #ec9c01;"></el-option> -->
                                </el-select>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" prop="superintendentName" label="负责人"
                        show-overflow-tooltip></el-table-column>
                    <el-table-column align="center" prop="remark" label="备注" show-overflow-tooltip></el-table-column>
                    <!-- <el-table-column align="center" prop="prop" label="操作">
                        <template slot-scope="{ row }">
                            <el-button type="primary" plain @click="copySubmit(row)"
                                size="mini">复制到下周</el-button>
                        </template>
                    </el-table-column> -->
                </el-table>
                <el-empty v-else />
            </div>
            <div class="replay_copy">
                <div class="replay_copy_left">
                    <!-- <el-checkbox class="left_check" @change="checkedChange" v-model="checked">全选</el-checkbox> -->
                    <div class="left_text">
                        共
                        <span class="left_text_num">{{ multipleSelection.length }}</span>
                        个项目
                    </div>
                </div>
                <div class="replay_copy_right">复制到下周</div>
            </div>
            <div slot="footer">
                <el-button @click="replayShow = false" size="medium">取 消</el-button>
                <el-button type="primary" @click="copySubmitAll" size="medium">下一步</el-button>
            </div>
        </el-dialog>
        <!-- 新增工作计划 -->
        <el-dialog v-dialogDragBox :title="title" :visible.sync="open" class="custom-dialog" width="1152px">
            <div style="padding: 0 20px;">
                <div class="copy_box" v-if="copyList.length > 0">
                    <div class="copy_title">已复制工作计划</div>
                    <div class="copy_list">
                        <el-table stripe :data="copyList" row-key="id" style="width: 100%" max-height="500"
                            class="custom-table">
                            <el-table-column align="center" type="index" label="序号"></el-table-column>
                            <el-table-column align="center" label="周期" show-overflow-tooltip>
                                <template slot-scope="{ row }">
                                    <div>{{ row.year + '年第' + row.weekOfYear + '周工作计划' }}</div>
                                </template>
                            </el-table-column>
                            <el-table-column align="center" prop="project" label="工作项目"
                                show-overflow-tooltip></el-table-column>
                            <el-table-column align="center" prop="target" label="工作内容详细"
                                show-overflow-tooltip></el-table-column>
                            <el-table-column align="center" label="完成日期" show-overflow-tooltip>
                                <template slot-scope="{ row }">
                                    <div>
                                        <span v-if="row.plannedType == 'specify'">{{ row.plannedDate }}</span>
                                        <span v-if="row.plannedType == 'uncertain'">{{ '不定' }}</span>
                                        <span v-if="row.plannedType == 'day'">{{ '每天' }}</span>
                                        <span v-if="row.plannedType == 'week'">{{ '每周' }}</span>
                                        <span v-if="row.plannedType == 'month'">{{ '每月' }}</span>
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column align="center" label="完成情况" show-overflow-tooltip>
                                <template slot-scope="{ row }">
                                    <div>
                                        <span v-if="row.completionStatus == 'continued'" style="color: #F35D09;">{{
                                            '进行中'
                                        }}</span>
                                        <span v-if="row.completionStatus == 'part'" style="color: #2E73F3;">{{ '部分完成'
                                            }}</span>
                                        <span v-if="row.completionStatus == 'done'" style="color: #2BCC75;">{{ '已完成'
                                            }}</span>
                                        <!-- <span v-if="row.completionStatus == 'done2'" style="color: #ec9c01;">{{
                                            '已完成，未发放'
                                            }}</span> -->
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column align="center" prop="superintendentName" label="负责人"
                                show-overflow-tooltip></el-table-column>
                            <el-table-column align="center" prop="remark" label="备注"
                                show-overflow-tooltip></el-table-column>
                        </el-table>
                    </div>
                </div>
                <div class="add_form" v-for="(item, index) in formList" :key="index">
                    <div class="add_title">
                        <span>计划项目{{ index + 1 }}</span>
                        <i class="el-icon-delete" style="cursor: pointer;" v-if="index > 0"
                            @click="deleteFormItem(index)"></i>
                    </div>
                    <el-form :ref="'form' + (index + 1)" :model="item.form" :rules="item.rules" label-width="6em">
                        <el-row>
                            <el-col :span="12">
                                <el-form-item label="部门及周期">
                                    <div class="form_deptAndWeek">
                                        <div class="form_dept">{{ userInfor.dept && userInfor.dept.deptName }}</div>
                                        <div>{{ `${item.form.year}年第${item.form.week}周工作计划` }}
                                        </div>
                                    </div>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="工作项目" prop="project">
                                    <el-autocomplete v-model="item.form.project" :fetch-suggestions="querySearchProject"
                                        placeholder="请输入工作项目" style="width: 100%;"
                                        @select="handleSelect(item)"></el-autocomplete>
                                </el-form-item>
                            </el-col>
                            <el-col :span="24">
                                <el-form-item label="工作内容及目标" prop="target">
                                    <el-autocomplete v-model="item.form.target" type="textarea"
                                        :autosize="{ minRows: 3, maxRows: 12 }" :fetch-suggestions="querySearchTarget"
                                        placeholder="请输入工作内容及达到目标" style="width: 100%;"
                                        @select="handleSelect(item)"></el-autocomplete>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="完成日期" prop="plannedDate" style="width: 100%;">
                                    <el-date-picker v-model="item.form.plannedDate" type="datetime"
                                        value-format="yyyy-MM-dd HH:mm:ss" placeholder="选择日期时间" style="width: 100%;">
                                    </el-date-picker>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="负责人" prop="superintendent">
                                    <el-cascader v-model="item.form.superintendent" :options="approvalsOptions"
                                        :props="approvalsProps" filterable :show-all-levels="false" placeholder="请选择负责人"
                                        @change="handleChange($event, item.form)"></el-cascader>
                                </el-form-item>
                            </el-col>
                            <el-col :span="24">
                                <el-form-item label="备注" prop="remark">
                                    <el-input type="textarea" :rows="3" placeholder="请输入备注"
                                        v-model="item.form.remark"></el-input>
                                </el-form-item>
                            </el-col>

                        </el-row>
                    </el-form>
                </div>
                <div class="add_btn_box">
                    <div class="add_btn" @click="addForm">
                        <img src="~@/assets/images/add_blue.png" alt="">
                        <span>添加新项目</span>
                    </div>
                </div>
            </div>

            <div slot="footer">
                <el-button @click="open = false" size="medium" style="width: 200px">取 消</el-button>
                <el-button type="primary" @click="handleSubmit" size="medium" style="width: 200px">{{ '新增工作计划'
                    }}</el-button>
            </div>
        </el-dialog>
        <!-- 修改状态 -->
        <el-dialog v-dialogDragBox title="修改工作计划" :visible.sync="editStatusShow" class="custom-dialog" width="1152px">
            <div class="p20">
                <el-form ref="editForm" :model="editForm" :rules="editRules" label-width="6em">
                    <el-row>
                        <el-col :span="12">
                            <el-form-item label="部门及周期">
                                <div class="form_deptAndWeek">
                                    <div class="form_dept">{{ editForm.deptName }}</div>
                                    <div>{{ `${editForm.year}年第${editForm.weekOfYear}周工作计划` }}
                                    </div>
                                </div>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="工作项目" prop="project">
                                <el-input v-model="editForm.project" placeholder="请输入工作项目"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label="工作内容及目标" prop="target">
                                <el-input type="textarea" :autosize="{ minRows: 3, maxRows: 12 }"
                                    placeholder="请输入工作内容及达到目标" v-model="editForm.target"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="完成日期" prop="plannedDate" style="width: 100%;">
                                <el-date-picker v-model="editForm.plannedDate" type="datetime"
                                    value-format="yyyy-MM-dd HH:mm:ss" placeholder="选择日期时间" style="width: 100%;">
                                </el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="完成情况" prop="completionStatus">
                                <el-select v-model="editForm.completionStatus" placeholder="请选择完成情况" clearable
                                    style="width: 100%;">
                                    <el-option v-for="(item, index) in debtModeOptions" :key="index" :label="item.label"
                                        :value="item.value" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="负责人" prop="superintendent">
                                <el-cascader v-model="editForm.superintendent" :options="approvalsOptions"
                                    :props="approvalsProps" filterable :show-all-levels="false" placeholder="请选择负责人"
                                    @change="handleChange($event, editForm)"></el-cascader>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label="备注" prop="remark">
                                <el-input type="textarea" :rows="3" placeholder="请输入备注"
                                    v-model="editForm.remark"></el-input>
                            </el-form-item>
                        </el-col>

                    </el-row>
                </el-form>
            </div>
            <div slot="footer">
                <el-button @click="editStatusShow = false" size="medium">取 消</el-button>
                <el-button type="primary" @click="submitCompletion" size="medium">提 交</el-button>
            </div>
        </el-dialog>
        <!-- 详情 -->
        <el-dialog v-dialogDragBox :title="approvalTitle" :visible.sync="approvalShow" class="custom-dialog">
            <div :key="projectInfo.id">
                <el-descriptions title="工作计划项目信息" class="project_detail" :column="2">
                    <el-descriptions-item label="工作项目">{{ projectInfo.project }}</el-descriptions-item>
                    <el-descriptions-item label="完成日期" v-if="projectInfo.plannedType == 'specify'">{{
                        projectInfo.plannedDate
                    }}</el-descriptions-item>
                    <el-descriptions-item label="完成日期" v-if="projectInfo.plannedType == 'uncertain'">{{ '不定'
                        }}</el-descriptions-item>
                    <el-descriptions-item label="完成日期" v-if="projectInfo.plannedType == 'day'">{{ '每天'
                        }}</el-descriptions-item>
                    <el-descriptions-item label="完成日期" v-if="projectInfo.plannedType == 'week'">{{ '每周'
                        }}</el-descriptions-item>
                    <el-descriptions-item label="完成日期" v-if="projectInfo.plannedType == 'month'">{{ '每月'
                        }}</el-descriptions-item>
                    <el-descriptions-item label="工作内容详细">{{ projectInfo.target }}</el-descriptions-item>
                </el-descriptions>
                <el-descriptions title="" class="project_detail" :column="2">
                    <el-descriptions-item label="完成情况" v-if="projectInfo.completionStatus == 'continued'">{{ '进行中'
                        }}</el-descriptions-item>
                    <el-descriptions-item label="完成情况" v-if="projectInfo.completionStatus == 'part'">{{ '部分完成'
                        }}</el-descriptions-item>
                    <el-descriptions-item label="完成情况" v-if="projectInfo.completionStatus == 'done'">{{ '已完成'
                        }}</el-descriptions-item>
                    <!-- <el-descriptions-item label="完成情况" v-if="projectInfo.completionStatus == 'done2'">{{ '已完成，未发放'
                        }}</el-descriptions-item> -->
                    <el-descriptions-item label="负责人">{{ projectInfo.superintendentName }}</el-descriptions-item>
                    <el-descriptions-item label="备注">{{ projectInfo.remark }}</el-descriptions-item>
                </el-descriptions>
                <div class="reporting_detail" v-if="projectInfo.approvals && projectInfo.approvals.length > 0">
                    <div class="reporting_detail_title">审核回复信息</div>
                    <el-divider></el-divider>
                    <div v-for="(item, index) in projectInfo.approvals" :key="index">
                        <div class="reporting_detail_list">
                            <div>审核人：{{ item.realName || item.approvalName }}</div>
                            <div>审核时间：{{ item.createTime }}</div>
                            <div>回复内容：{{ item.approvalInfo }}</div>
                        </div>
                        <el-divider></el-divider>
                    </div>
                </div>
                <div v-if="approvalType == true && checkPermi(['work.plan.all'])">
                    <div class="p20">
                        <div class="reporting_detail_title" style="margin-bottom: 10px;">审核回复</div>
                        <el-form ref="approvalForm" :model="approvalForm" :rules="approvalRules">
                            <el-form-item label="" prop="approvalInfo">
                                <el-input type="textarea" :rows="3" placeholder="请输入审核信息"
                                    v-model="approvalForm.approvalInfo"></el-input>
                            </el-form-item>
                        </el-form>
                        <div style="text-align: right;">
                            <el-button type="primary" @click="submitApproval" size="medium">提 交</el-button>
                        </div>
                    </div>
                </div>
            </div>
        </el-dialog>

    </div>
</template>

<script>
import {
    workPlanProjectList,
    workPlanWeekOfYear,
    workPlanProjectApproval,
    workPlanProjectApprovalDetail,
    workPlanProjectStatusRevise,
    workPlanProjectAdd,
    workPlanProjectBatchAdd,
    workPlanProjectRevise,
    workPlanProjectDetail,
    workPlanProjectCommonly,
    workPlanProjectApprovalEdit,
    workPlanProjectDelete
} from '@/api/workPlan'
import { listDept } from '@/api/system/dept'
import { deptTreeSelect, listUser } from '@/api/system/user'
import { checkPermi } from '@/utils/permission'
import { parseTime } from '@/utils/ruoyi'

export default {
  name: 'WorkPlan',
    data() {
        return {
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                project: '',
                identity: 'creator',
                superintendent: '',
                deptName: '',
                completionStatus: 'continued',
                week: '',
                year: '',
                term: '0'
            },
            allTotal: 0,
            total: 0,
            list: [],
            loading: true,
            title: '',
            open: false,
            formList: [
                {
                    form: {
                        deptName: undefined,
                        project: '',
                        target: '',
                        plannedType: 'specify',
                        plannedDate: undefined,
                        completionStatus: undefined,
                        superintendents: [],
                        remark: '',
                        projectId: '',
                        week: '',
                        year: ''
                    },
                    rules: {
                        plannedType: [{ required: true, message: '请选择完成类型', trigger: 'change' }],
                        plannedDate: [{ required: true, message: '请选择完成日期', trigger: 'change' }],
                        project: [{ required: true, message: '请输入工作计划项目', trigger: 'blur' }],
                        superintendent: [{ required: true, message: '请选择负责人', trigger: 'change' }],
                        target: [{ required: true, message: '请输入工作内容以及目标', trigger: 'blur' }],

                    },
                }
            ],

            deptOptions: [],
            debtModeOptions: [{
                value: 'continued',
                label: '进行中'
            },
            {
                value: 'part',
                label: '部分完成'
            },
            {
                value: 'done',
                label: '已完成'
            },
                // {
                //     value: 'done2',
                //     label: '已完成，未发放'
                // }
            ],
            termOptions: [{
                value: '0',
                label: '全部'
            },
            {
                value: '1',
                label: '未超期'
            },
            {
                value: '2',
                label: '已超期'
            }
            ],
            plannedTypeList: [{
                value: 'specify',
                text: '指定时间'
            },
            {
                value: 'uncertain',
                text: '不定'
            },
            {
                value: 'day',
                text: '每天'
            },
            {
                value: 'week',
                text: '每周'
            },
            {
                value: 'month',
                text: '每月'
            }],
            addWorkShow: false,
            approvalsOptions: [],
            approvalsProps: {
                expandTrigger: 'hover',
                emitPath: false,
                multiple: true
            },
            userInfor: {},
            editStatusShow: false,
            editForm: {},
            editRules: {
                plannedType: [{ required: true, message: '请选择完成类型', trigger: 'change' }],
                plannedDate: [{ required: true, message: '请选择完成日期', trigger: 'change' }],
                project: [{ required: true, message: '请输入工作计划项目', trigger: 'blur' }],
                superintendent: [{ required: true, message: '请选择负责人', trigger: 'change' }],
                target: [{ required: true, message: '请输入工作内容以及目标', trigger: 'blur' }],

            },
            completionForm: {},
            completionRules: {
                completionStatus: [{ required: true, message: '请选择完成情况', trigger: 'change' }],
            },
            projectInfo: {},
            approvalShow: false,
            approvalTitle: '工作计划项目详情',
            approvalType: false,
            approvalForm: {},
            approvalRules: {
                approvalInfo: [{ required: true, message: '请输入审批内容', trigger: 'blur' }],
            },
            weekOfYearInfo: {},
            replayShow: false,
            replayList: [],
            multipleSelection: [],
            checked: false,
            copyList: [],
            salespersonOptions: [],
            weekValue: new Date(),
            // weekOptions: {
            //     firstDayOfWeek: 1,
            // },
            lastTime: '',
            restaurants: [],
            nextWorkList: [],
            nextTotal: 0,
            fridayDate: '',
            addOrEdit: undefined
        }
    },
    computed: {
        companyId() {
            return this.$store.state.user.companyId
        },
        weekOptions() {
            const that = this
            return {
                firstDayOfWeek: 1,
                disabledDate(time) {
                    return time.getTime() > that.lastTime
                }
            }
        }
    },
    created() {
        this.getWorkPlanWeekOfYear()
        this.getApprovalsOptions()
        this.getList()
        this.fridayDate = this.parseTime(this.getThisWeekFriday(), '{y}-{m}-{d}')
        this.userInfor = this.$store.state.user.info
        console.log(this.userInfor)
    },
    methods: {
        parseTime,
        checkPermi,
        // 查询当前所属周
        getWorkPlanWeekOfYear() {
            workPlanWeekOfYear().then(res => {
                const {
                    code,
                    data,
                } = res
                if (code == 200) {
                    this.weekOfYearInfo = res.data
                    this.queryParams.week = this.weekOfYearInfo.week
                    this.queryParams.year = this.weekOfYearInfo.year
                    this.getNextList()
                }
            })
        },
        // 查询部门、查询用户构造树
        async getApprovalsOptions() {
            const deptList = await listDept()
            deptList.data.forEach(el => {
                el.value = el.deptId
                el.label = el.deptName
            })
            this.deptOptions = deptList.data

            const dept = await deptTreeSelect()
            const user = await listUser()
            this.salespersonOptions = user.rows || []
            const children = dept.data[0].children || []
            const deptData = [...children, ...[{ id: -1, label: '其他', value: -1 }]] || []
            const userData = user.rows || []
            const getChildren = data => {
                data.forEach(item => {
                    item.value = item.id
                    if (item.children) {
                        getChildren(item.children)
                    } else {
                        item.children = []
                    }
                })
            }
            getChildren(deptData)
            const addChildren = data => {
                data.forEach(item => {
                    userData.forEach(user => {
                        if (item.id === user.deptId && item.children) {
                            item.children.push({
                                id: user.userId,
                                label: user.realName || user.nickName,
                                value: user.userId,
                                disabled: user.status == '1',
                                userName: user.userName
                            })
                        }
                        if (item.id === -1 && (!user.deptId || user.deptId === dept.data[0].id) && item.children) {
                            item.children.push({
                                id: user.userId,
                                label: user.realName || user.nickName,
                                value: user.userId,
                                disabled: user.status == '1',
                                userName: user.userName
                            })
                        }
                    })
                    if (item.children && item.children.length) {
                        addChildren(item.children)
                    }
                })
            }
            addChildren(deptData)
            this.approvalsOptions = deptData
        },
        // 下周工作计划列表
        getNextList() {
            let data = {
                pageNum: 1,
                pageSize: 100000,
                identity: this.queryParams.identity == 'approval' ? '' : this.queryParams.identity,
                week: this.weekOfYearInfo.nextWeek,
                year: this.weekOfYearInfo.nextWeekYear,
            }
            workPlanProjectList(data).then(async res => {
                const {
                    code,
                    rows,
                    total
                } = res

                if (code === 200) {
                    this.nextWorkList = rows
                    this.nextTotal = total
                    if (this.nextTotal > 0) {
                        this.lastTime = new Date(this.getNextFridayDate()).getTime()

                    } else {
                        this.lastTime = this.getThisWeekFriday()

                    }
                }
            })
        },
        // 列表
        getList() {
            this.queryParams.term = '0'
            this.loading = true
            if (this.queryParams.identity == 'approval' && !checkPermi(['work.plan.all'])) {
                this.list = []
                this.total = 0
                this.loading = false
                return
            }
            let data = {
                pageNum: this.queryParams.pageNum,
                pageSize: this.queryParams.pageSize,
                project: this.queryParams.project,
                identity: this.queryParams.identity == 'approval' ? '' : this.queryParams.identity,
                deptName: this.queryParams.deptName,
                week: this.queryParams.week,
                year: this.queryParams.year,
                completionStatus: this.queryParams.completionStatus,
                superintendent: this.queryParams.superintendent,
            }
            workPlanProjectList(data).then(async res => {
                const {
                    code,
                    rows,
                    total
                } = res

                if (code === 200) {
                    for (const el of rows) {
                        el.openMore = false
                        const approvalObj = await workPlanProjectApprovalDetail({
                            projectId: el.id
                        })
                        el.approvalList = approvalObj.data
                    }
                    this.list = rows
                    this.total = total
                    this.allTotal = total
                    this.loading = false
                }
            })
        },
        // 搜索
        handleQuery() {
            this.queryParams.term = '0'
            this.queryParams.pageNum = 1
            this.getList()
        },
        // 重置搜索
        resetQuery(identity = 'creator') {
            this.weekValue = new Date()
            this.queryParams = {
                pageNum: 1,
                pageSize: 10,
                project: '',
                identity,
                superintendent: '',
                deptName: '',
                completionStatus: '',
                week: this.weekOfYearInfo.week,
                year: this.weekOfYearInfo.year
            }
            this.resetForm('queryForm')
            this.handleQuery()
        },
        termChange() {
            console.log(this.queryParams.term)
            if (this.queryParams.term == '0') {
                this.handleQuery()
            } else if (this.queryParams.term == '1') {
                this.list = []
                this.loading = true
                if (this.queryParams.identity == 'approval' && !checkPermi(['work.plan.all'])) {
                    return
                }
                let data = {
                    pageNum: this.queryParams.pageNum,
                    pageSize: this.allTotal,
                    project: this.queryParams.project,
                    identity: this.queryParams.identity == 'approval' ? '' : this.queryParams.identity,
                    deptName: this.queryParams.deptName,
                    year: this.queryParams.year,
                    week: this.queryParams.week,
                    superintendent: this.queryParams.superintendent,
                    completionStatus: this.queryParams.completionStatus
                }
                workPlanProjectList(data).then(async res => {
                    const {
                        code,
                        msg,
                        rows,
                        total
                    } = res
                    if (code === 200) {
                        for (const el of rows) {
                            el.openMore = false
                            const approvalObj = await workPlanProjectApprovalDetail({
                                projectId: el.id
                            })
                            el.approvalList = approvalObj.data
                        }
                        this.list = rows.filter(item => !((item.completionStatus == 'continued' || item
                            .completionStatus == 'part') && (new Date() > new Date(item
                                .plannedDate))))
                        this.total = this.list.length
                        this.allTotal = total
                        this.loading = false
                    } else this.$modal.msgError(msg)
                })
            } else if (this.queryParams.term == '2') {
                this.loading = true
                this.list = []
                if (this.queryParams.identity == 'approval' && !checkPermi(['work.plan.all'])) {
                    return
                }
                let data = {
                    pageNum: this.queryParams.pageNum,
                    pageSize: this.allTotal,
                    project: this.queryParams.project,
                    identity: this.queryParams.identity == 'approval' ? '' : this.queryParams.identity,
                    deptName: this.queryParams.deptName,
                    year: this.queryParams.year,
                    week: this.queryParams.week,
                    superintendent: this.queryParams.superintendent,
                    completionStatus: this.queryParams.completionStatus
                }
                workPlanProjectList(data).then(async res => {
                    const {
                        code,
                        msg,
                        rows,
                        total
                    } = res
                    if (code === 200) {
                        for (const el of rows) {
                            el.openMore = false
                            const approvalObj = await workPlanProjectApprovalDetail({
                                projectId: el.id
                            })
                            el.approvalList = approvalObj.data
                        }
                        this.list = rows.filter(item => (item.completionStatus == 'continued' || item
                            .completionStatus == 'part') && (new Date() > new Date(item
                                .plannedDate)))
                        this.total = this.list.length
                        this.allTotal = total
                        this.loading = false
                    } else this.$modal.msgError(msg)
                })
            }

        },
        // tabs切换
        tabChange(e) {
            this.resetQuery(e.name)
            this.getNextList()
        },
        // 重置表单
        reset(type) {
            this.formList = [
                {
                    form: {
                        deptName: this.userInfor.dept && this.userInfor.dept.deptName,
                        project: '',
                        target: '',
                        plannedType: 'specify',
                        plannedDate: type == 'this' ? this.parseTime(this.getThisWeekFriday(), '{y}-{m}-{d}') + ' 18:00:00' : this.getNextFridayDate(),
                        completionStatus: 'continued',
                        superintendents: [],
                        remark: '',
                        week: type == 'this' ? this.weekOfYearInfo.week : this.weekOfYearInfo.nextWeek,
                        year: type == 'this' ? this.weekOfYearInfo.year : this.weekOfYearInfo.nextWeekYear
                    },
                    rules: {
                        plannedType: [{ required: true, message: '请选择完成类型', trigger: 'change' }],
                        plannedDate: [{ required: true, message: '请选择完成日期', trigger: 'change' }],
                        project: [{ required: true, message: '请输入工作计划项目', trigger: 'blur' }],
                        superintendent: [{ required: true, message: '请选择负责人', trigger: 'change' }],
                        target: [{ required: true, message: '请输入工作内容以及目标', trigger: 'blur' }],

                    },
                }
            ]
        },
        // 补充本周工作计划
        handleThisAdd() {
            if (!this.userInfor.dept) {
                this.$message.error('该账户未分配部门')
                return
            }
            this.reset('this')
            this.title = '补充本周工作计划'
            this.open = true
        },
        // 添加下周工作计划
        handleNextAdd() {
            if (!this.userInfor.dept) {
                this.$message.error('该账户未分配部门')
                return
            }
            let data = {
                pageNum: 1,
                pageSize: 10000,
                project: undefined,
                identity: 'creator',
                completionStatus: '',
                deptName: this.userInfor.dept && this.userInfor.dept.deptName,
                week: this.weekOfYearInfo.week,
                year: this.weekOfYearInfo.year,
            }
            workPlanProjectList(data).then(async res => {
                const {
                    code,
                    rows,
                    total
                } = res

                if (code === 200) {
                    console.log(rows)
                    if (total > 0) {
                        this.multipleSelection = []
                        this.copyList = []
                        this.replayList = []
                        rows.forEach(el => {
                            el.copyStaus = true
                            if (this.nextWorkList.find(item => item.copyId == el.id)) {
                                el.copyStaus = false
                            }
                        })
                        this.replayList = rows.filter(item => item.copyStaus == true)
                        let that = this
                        this.replayList.forEach(el => {
                            if (el.completionStatus == 'continued' || el.completionStatus == 'part') {
                                el.disabled = true
                                that.$nextTick(() => {
                                    that.$refs.multipleTable.toggleRowSelection(el, true)
                                })
                            } else {
                                el.disabled = false
                            }
                        })
                        this.replayShow = true
                    } else {
                        this.reset('next')
                        this.title = '新建下周工作计划'
                        this.open = true
                    }
                }
            })
        },
        selectable(row, index) {
            if (row.disabled == true) {
                return false
            } else {
                return true
            }
        },
        handleSelectionChange(val) {
            this.multipleSelection = val;
            console.log(this.multipleSelection)
        },
        checkedChange() {
            console.log(this.checked)
            if (this.checked === true) {
                this.$refs.multipleTable.toggleAllSelection()
            } else if (this.checked === false) {
                this.$refs.multipleTable.clearSelection();
            }
        },
        copySubmit(row) {
            if (this.multipleSelection.findIndex(item => item.id == row.id) == -1) {
                this.multipleSelection.push(row)
            }

        },
        copySubmitAll() {
            if (this.multipleSelection.length > 0) {
                this.copyList = this.multipleSelection
                // this.$message.success('已复制')
            }
            this.nextStep()
        },
        nextStep() {
            this.reset('next')
            this.title = '新建下周工作计划'
            this.open = true
        },

        querySearchProject(queryString, cb) {
            let that = this
            workPlanProjectCommonly({
                projectOrTarget: 'project'
            }).then(res => {
                const {
                    code,
                    data,
                } = res
                if (code == 200) {
                    let restaurants = []
                    data.forEach(el => {
                        let obj = {
                            value: el
                        }
                        restaurants.push(obj)
                    })
                    let results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants;
                    // 调用 callback 返回建议列表的数据
                    cb(results);
                }
            })
        },
        querySearchTarget(queryString, cb) {
            let that = this
            workPlanProjectCommonly({
                projectOrTarget: 'target'
            }).then(res => {
                const {
                    code,
                    data,
                } = res
                if (code == 200) {
                    let restaurants = []
                    data.forEach(el => {
                        let obj = {
                            value: el
                        }
                        restaurants.push(obj)
                    })
                    let results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants;
                    // 调用 callback 返回建议列表的数据
                    cb(results);
                }
            })
        },
        createFilter(queryString) {
            return (restaurant) => {
                return (restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0);
            };
        },
        handleSelect(item) {
            console.log(item.form);
        },
        // 编辑
        handleEdit(row) {
            workPlanProjectDetail({
                projectId: row.id,
            }).then(async res => {
                const {
                    code,
                    data,
                } = res
                if (code === 200) {
                    console.log(data)
                    data.superintendent = data.superintendent.split(',')
                    this.editForm = data
                    this.editStatusShow = true
                }
            })
        },
        // 修改完成情况
        handleUpStatus(rows, bol) {
            this.completionForm.projectId = rows.id
            this.completionForm.completionStatus = rows.completionStatus
            console.log(this.completionForm.completionStatus)
            workPlanProjectStatusRevise(this.completionForm).then(res => {
                const {
                    code
                } = res
                if (code === 200) {
                    this.$message.success('修改成功')
                    if (rows.completionStatus == 'done') {
                        rows.disabled = false
                    } else {
                        rows.disabled = true
                    }
                    this.getList()
                    if (bol === true) {
                        this.handleNextAdd()
                    }
                }
            })
        },
        submitCompletion() {
            this.$refs.editForm.validate(valid => {
                if (valid) {
                    this.editForm.projectId = this.editForm.id
                    workPlanProjectRevise(this.editForm).then(res => {
                        const {
                            code,
                            msg
                        } = res
                        if (code === 200) {
                            this.$message.success('修改成功')
                            this.getList()
                            this.editStatusShow = false
                        } else this.$message.error(msg)
                    })
                }
            })
        },
        // 新增工作计划
        addWork() {
            this.addWorkShow = true
        },

        // changePlannedType() {
        //     if (this.form.plannedType === 'specify') {
        //         this.rules.plannedDate[0].required = true
        //     } else {
        //         this.rules.plannedDate[0].required = false
        //     }
        // },
        // 选择审批人
        handleChange(data, formData) {
            console.log(data, formData)
            formData.superintendents = []
            data.forEach(el => {
                const approval = this.findInTree(this.approvalsOptions, el)
                formData.superintendents.push({
                    superintendent: approval.value,
                    superintendentName: approval.label
                })
                // this.form.superintendentName = approval.label
            })
            console.log(formData)

        },
        // 从树结构内找到相同id
        findInTree(tree, id) {
            for (let i = 0; i < tree.length; i++) {
                if (tree[i].id === id) {
                    return tree[i]
                } else if (tree[i].children && tree[i].children.length) {
                    const res = this.findInTree(tree[i].children, id)
                    if (res) return res
                }
            }
        },
        addForm() {
            let obj = {
                form: {
                    deptName: this.userInfor.dept && this.userInfor.dept.deptName,
                    project: '',
                    target: '',
                    plannedType: 'specify',
                    plannedDate: this.title == '补充本周工作计划' ? this.parseTime(this.getThisWeekFriday(), '{y}-{m}-{d}') + ' 18:00:00' : this.getNextFridayDate(),
                    completionStatus: 'continued',
                    superintendents: [],
                    remark: '',
                    week: this.title == '补充本周工作计划' ? this.weekOfYearInfo.week : this.weekOfYearInfo.nextWeek,
                    year: this.title == '补充本周工作计划' ? this.weekOfYearInfo.year : this.weekOfYearInfo.nextWeekYear
                },
                rules: {
                    plannedType: [{ required: true, message: '请选择完成类型', trigger: 'change' }],
                    plannedDate: [{ required: true, message: '请选择完成日期', trigger: 'change' }],
                    project: [{ required: true, message: '请输入工作计划项目', trigger: 'blur' }],
                    superintendent: [{ required: true, message: '请选择负责人', trigger: 'change' }],
                    target: [{ required: true, message: '请输入工作内容以及目标', trigger: 'blur' }],

                },
            }
            this.formList.push(obj)
        },
        deleteFormItem(i) {
            this.formList.splice(i, 1)
        },
        resetapprovalForm() {
            this.approvalForm = { approvalInfo: '' }
            this.resetForm('approvalForm')
        },
        // 查看详情
        handleView(row, type, addOrEdit) {
            workPlanProjectDetail({
                projectId: row.id
            }).then(res => {
                const {
                    code,
                    data,
                    msg
                } = res
                if (code == 200) {
                    this.projectInfo = data
                    if (type == 'view') {
                        this.approvalTitle = '工作计划项目详情'
                        this.approvalType = false
                    } else if (type == 'approval') {
                        this.resetapprovalForm()
                        this.approvalTitle = '工作计划项目审批'
                        this.approvalType = true
                        this.addOrEdit = addOrEdit
                        if (this.addOrEdit == 'add') {
                            this.approvalForm.approvalInfo = ''
                        } else if (this.addOrEdit == 'edit') {
                            this.approvalForm.approvalInfo = this.projectInfo.approvals.find(it => it.approvalId == this
                                .userInfor.userId).approvalInfo
                        }
                    }
                    this.approvalShow = true
                }
            })
        },

        // 提交
        handleSubmit() {
            console.log(this.copyList, this.formList)
            let projectList = []

            if (this.copyList.length > 0) {
                this.copyList.forEach(el => {
                    el.superintendents = []
                    el.superintendent.split(',').forEach(m => {
                        let approval = this.findInTree(this.approvalsOptions, Number(m))
                        console.log(approval)
                        el.superintendents.push({
                            superintendent: approval.value,
                            superintendentName: approval.label
                        })
                    })
                    projectList.push({
                        deptName: this.userInfor.dept && this.userInfor.dept.deptName,
                        project: el.project,
                        target: el.target,
                        plannedType: 'specify',
                        plannedDate: el.plannedDate || this.getNextFridayDate(),
                        completionStatus: el.completionStatus == 'part' ? el.completionStatus : 'continued',
                        superintendents: el.superintendents,
                        remark: el.remark,
                        week: this.weekOfYearInfo.nextWeek,
                        year: this.weekOfYearInfo.nextWeekYear,
                        copyId: el.id
                    })
                })
            }
            this.formList.forEach(el => {
                if (el.form.project && el.form.target && el.form.superintendents) {
                    projectList.push({
                        deptName: el.form.deptName,
                        project: el.form.project,
                        target: el.form.target,
                        plannedType: 'specify',
                        plannedDate: el.form.plannedDate,
                        completionStatus: el.form.completionStatus,
                        superintendents: el.form.superintendents,
                        remark: el.form.remark,
                        week: el.form.week,
                        year: el.form.year
                    })
                }
            })
            if (projectList.length > 0) {
                workPlanProjectBatchAdd({ projectList }).then(res => {
                    const {
                        code,
                        msg
                    } = res
                    if (code === 200) {
                        this.$message.success('添加成功')
                        this.getList()
                        this.getNextList()
                        this.replayShow = false
                        this.open = false
                    } else this.$message.error(msg)
                })
            } else {
                this.$message.error('请填写工作计划')
            }
        },
        // 提交审批
        submitApproval() {
            this.$refs.approvalForm.validate(valid => {
                if (valid) {
                    if (this.addOrEdit == 'add') {
                        this.approvalForm.projectId = this.projectInfo.id
                        workPlanProjectApproval(this.approvalForm).then(async res => {
                            const {
                                code,
                                msg
                            } = res
                            if (code === 200) {
                                const index = this.list.findIndex(el => el.id != this.projectInfo.id &&
                                    el.approvalList.findIndex(it => it.approvalId == this.userInfor
                                        .userId) == -1)
                                let noApproval = 0
                                noApproval = this.list.filter(el => el.approvalList.findIndex(it => it
                                    .approvalId == this.userInfor.userId) == -1).length - 1
                                console.log(noApproval);
                                if (noApproval > 0) {
                                    this.$message.info('你还有' + noApproval + '条待审批')
                                    const row = this.list[index]
                                    const projectInfo = await workPlanProjectDetail({ projectId: row.id })
                                    this.resetapprovalForm()
                                    this.projectInfo = projectInfo.data
                                    this.approvalTitle = '工作计划项目审批'
                                    this.getList()
                                    this.approvalType = true
                                } else {
                                    this.$message.success('成功提交审批')
                                    this.approvalShow = false
                                    this.getList()
                                }
                            } else this.$message.error(msg)
                        })
                    } else if (this.addOrEdit == 'edit') {
                        this.approvalForm.approvalTableId = this.projectInfo.approvals.find(it => it.approvalId == this
                            .userInfor.userId).id
                        workPlanProjectApprovalEdit(this.approvalForm).then(async res => {
                            const {
                                code,
                                msg
                            } = res
                            if (code === 200) {
                                this.$message.success('成功修改审批')
                                this.approvalShow = false
                                this.getList()
                            } else this.$message.error(msg)
                        })
                    }

                }
            })
        },
        // 删除项目
        handleDelete(row) {
            console.log(row)
            // workPlanProjectDelete
            const data = { projectId: row.id }
            this.$modal.confirm('是否确认删除该工作计划？').then(function () {
                return workPlanProjectDelete(data)
            }).then(() => {
                this.getList()
                this.$message.success('操作成功')
            }).catch(() => {
            })
        },
        getThisWeekFriday() {
            const today = new Date();
            const dayOfWeek = today.getDay() === 0 ? 7 : today.getDay(); // 获取今天是周几 (0-6, 0是周日)
            const daysUntilFriday = 5 - dayOfWeek; // 计算今天到周五的天数，如果今天是周五则返回0，需要加7
            const nextFriday = new Date(today.getTime() + daysUntilFriday * 24 * 60 * 60 * 1000); // 计算下周五的日期
            return nextFriday;
        },
        getNextFridayDate() {
            const today = new Date();
            const dayOfWeek = today.getDay() === 0 ? 7 : today.getDay(); // 0 表示周日，1 到 6 表示周一到周六
            const daysUntilFriday = 5 - dayOfWeek; // 如果今天是周五，则返回0，否则返回到下周五的天数
            const nextFriday = new Date(today);
            nextFriday.setDate(today.getDate() + daysUntilFriday + 7); // 下一个周五的日期
            const year = nextFriday.getFullYear();
            // 使用两位数字的月份和日期
            const month = ('0' + (nextFriday.getMonth() + 1)).slice(-2);
            const day = ('0' + nextFriday.getDate()).slice(-2);
            return `${year}-${month}-${day} 18:00:00`;
        },
        changeWeek(val) {
            console.log(this.weekValue)
            const year = val.getFullYear();
            const firstDayOfYear = new Date(year, 0, 1);
            const pastDaysOfYear = Math.floor((val - firstDayOfYear) / (24 * 60 * 60 * 1000));
            const week = Math.ceil((pastDaysOfYear + firstDayOfYear.getDay()) / 7);
            let startTime = new Date(val.getTime()); //开始时间
            let endTime = new Date(val.getTime() + (24 * 60 * 60 * 1000) * 6); //结束时间
            let timeArr = [startTime.toISOString().slice(0, 10), endTime.toISOString().slice(0, 10)];
            if (week > 52) {
                this.queryParams.year = year + 1
                this.queryParams.week = week - 52
            } else {
                this.queryParams.year = year
                this.queryParams.week = week
            }
            console.log('周的起止日期', timeArr)
            console.log('year', year)
            console.log('week', week)
            this.handleQuery()
        }
    }
}
</script>

<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';

.p20 {
    padding: 20px;
}

.select_textColor {
    .orange {
        ::v-deep {
            .el-input__inner {
                color: #F35D09;
            }
        }
    }

    .blue {
        ::v-deep {
            .el-input__inner {
                color: #2E73F3;
            }
        }
    }

    .green {
        ::v-deep {
            .el-input__inner {
                color: #2BCC75;
            }
        }
    }

    .yellow {
        ::v-deep {
            .el-input__inner {
                color: #ec9c01;
            }
        }
    }
}

.el-cascader {
    width: 100%;
}

.project_detail {
    padding: 0 20px;

    ::v-deep .el-descriptions__body {
        background: #f8f9fb;
        padding: 20px;
    }

    ::v-deep .el-descriptions__header {
        margin-bottom: 12px;
    }

    ::v-deep .el-descriptions__title {
        font-size: 14px;
        color: #666666;
        font-weight: 400;
    }

    ::v-deep .el-descriptions-item__container {
        align-items: center;
    }
}

.reporting_detail {
    padding: 20px;

    .reporting_detail_title {
        font-size: 14px;
        color: #666666;
        margin-bottom: 12px;
    }

    .el-divider--horizontal {
        margin: 0;
    }

    .reporting_detail_list {
        display: flex;
        justify-content: space-between;
        margin: 24px 0;
    }
}

.hover {
    transition: 0.2s;
    cursor: pointer;

    &:hover {
        transform: scale(1) !important;
    }
}

.planned {
    display: flex;
    align-items: center;

    .planned_date {
        ::v-deep {
            .el-input__inner {
                border-radius: 0;
                border-color: #CBD6E2;
                border-right: none;
            }
        }
    }

    .planned_type {
        height: 40px;
        padding-left: 10px;
        padding-right: 10px;
        box-sizing: border-box;
        border: 1px solid #CBD6E2;
        border-left: none;
        position: relative;

        &::before {
            position: absolute;
            width: 1px;
            height: 24px;
            top: 8px;
            bottom: 0;
            left: 0;
            background: #CBD6E2;
        }

        ::v-deep {
            .el-form-item__content {
                margin-left: 0 !important;
            }

            .el-radio {
                margin-right: 10px;

                .el-radio__inner {
                    width: 12px;
                    height: 12px;
                }

                .el-radio__label {
                    font-size: 12px;
                }
            }
        }
    }
}

.replay_box {
    .replay_dept {
        position: absolute;
        top: 13px;
        left: 157px;
        font-weight: 500;
        font-size: 16px;
        color: #2E73F3;
    }

    .replay_list {}

    .replay_copy {
        margin-top: 20px;
        height: 48px;
        background: #EFF5FF;
        border-radius: 5px;
        border: 1px solid #2E73F3;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .replay_copy_left {
            display: inline-flex;
            align-items: center;
            justify-content: flex-start;

            .left_check {
                margin-left: 15px;
                margin-right: 80px;
                font-size: 12px;
                color: #666666;
            }

            .left_text {
                font-size: 14px;
                margin-left: 15px;
                color: #666666;
                display: inline-flex;
                align-items: center;

                .left_text_num {
                    font-weight: 500;
                    font-size: 18px;
                    color: #2E73F3;
                    margin: 0 10px;
                }
            }
        }

        .replay_copy_right {
            width: 136px;
            height: 48px;
            background: #2E73F3;
            font-weight: 500;
            font-size: 14px;
            color: #FFFFFF;
            line-height: 48px;
            text-align: center;
        }
    }
}

.copy_box {
    .copy_title {
        font-weight: 500;
        font-size: 14px;
        color: #333333;
        line-height: 32px;
    }
}

.add_form {
    border-bottom: 1px solid #E2E6F3;
    margin-top: 11px;

    .add_title {
        font-weight: 500;
        font-size: 14px;
        color: #333333;
        line-height: 32px;
        border-bottom: 1px solid #E2E6F3;
        margin-bottom: 15px;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .form_deptAndWeek {
        display: flex;
        align-items: center;

        .form_dept {
            margin-right: 30px;
            font-weight: 500;
            font-size: 14px;
            color: #333333;
            line-height: 16px;
        }
    }
}

.add_btn_box {
    border-bottom: 1px solid #E2E6F3;
    padding: 20px 0 30px;

    .add_btn {
        width: 160px;
        height: 50px;
        background: #E4EDFF;
        border-radius: 5px 5px 5px 5px;
        border: 1px solid #2E73F3;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;

        img {
            width: 16px;
            height: 16px;
            margin-right: 4px;
        }

        span {
            font-weight: 500;
            font-size: 14px;
            color: #2E73F3;
            line-height: 20px;
        }
    }
}

.finishTime_box {
    display: flex;
    align-items: center;
    justify-content: center;

    .time {
        margin-right: 7px;
    }

    .tips {
        flex-shrink: 0;
        width: 83px;
        background: url('~@/assets/images/listing_tips_bg.png') center no-repeat;
        background-size: 82.5px 22px;
        font-weight: 500;
        font-size: 12px;
        color: #F50E0E;
        display: flex;
        align-items: center;
        justify-content: center;

        img {
            width: 20px;
            height: 20px;
            margin-right: 7px;
        }
    }
}

.weekIcon {
    position: absolute;
    right: 10px;
    top: 10px;

    &.el-icon-caret-bottom {
        color: #F50E0E;
    }

    &.el-icon-caret-top {
        color: #2E73F3;
    }
}
</style>
