<template>
  <div>
    <div class="contractView">
      <img :src="srcUrl" />
    </div>
  </div>
</template>

<script>
import { createQrcode } from '@/api/purchase'

export default {
  data() {
    return {
      srcUrl: undefined,
      type: undefined,
      requestId: undefined
    }
  },
  created() {
    const { query } = this.$route
    this.type = query.type
    this.requestId = query.requestId
    if (!this.type || !this.requestId) {
      this.$alert('参数错误，请联系管理员', '系统提示', {
        type: 'error',
        confirmButtonText: '确定',
        callback: action => {
          this.$router.push('/')
        }
      })
      return
    }
    this.getInfo()
  },
  methods: {
    getInfo() {
      const data = { type: this.type, requestId: this.requestId }
      createQrcode(data).then(res => {
        if (res.code === 200) {
          let str = res.msg
          this.srcUrl = str.replace(/\"/g, '')
        } else {
          this.$alert('系统错误，请联系管理员', '系统提示', {
            type: 'error',
            confirmButtonText: '确定',
            callback: action => {
              this.$router.push('/')
            }
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.contractView {
  max-width: 1200px;
  margin: 0 auto;
  padding: 10px;
  img{
    max-width: 100%;
  }
}
</style>
