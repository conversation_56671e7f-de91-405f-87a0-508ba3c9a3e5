<template>
  <div class="signBox">
    <div class="signBox-write" v-if="isWrite">
      <SignCanvas ref="SignCanvasRef" :isCrop="isCrop" :lineWidth="lineWidth" :lineColor="lineColor" :bgColor="bgColor" :quality="quality" signName="法人或委托代理人" @sureHandler="handleGenerate" :height="height" @clearHandler="handleBack" />
    </div>
    <div class="signBox-view" :style="{ 'max-width': !isMobile ? '1200px' : '' }" v-if="!isWrite && !showProduct">
      <el-image :src="contractImg" :preview-src-list="[contractImg]" v-if="isBase64Image(contractImg)"></el-image>
      <show-template isView :certify="certify" :signet="signet" :signature="signature" :content="contractImg" v-else-if="isJSON(contractImg)" />
      <template v-if="!isSign">
        <el-button type="primary" @click="handleDownload" v-if="hasCertify">下载自由客App</el-button>
        <template v-else>
          <el-button style="width: 100%" type="primary" @click="handleDownload" v-if="source">为了您更好的签署体验和合同安全，请您下载自由客紧固件App进行合同签署。</el-button>
          <el-button style="width: 100%" type="primary" @click="showProduct = true" v-if="!source">点此签名</el-button>
        </template>
      </template>
      <el-button type="primary" v-if="isSign" @click="showProduct = true" style="margin-top: 10px">查看产品明细</el-button>
    </div>
    <div class="signBox-product" :style="{ 'max-width': !isMobile ? '1200px' : '' }" v-if="showProduct">
      <el-table :data="products" style="width: 100%" stripe class="custom-table">
        <el-table-column align="center" label="产品名称">
          <template slot-scope="{ row }">
            <div class="table-link" @click="handleDetail(row)">
              <span v-if="row.source === 'common'">(公域)</span>
              <span style="color: #fe7f22" v-else>(私域)</span>
              <span>{{ row.productName }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" label="产品单价">
          <template slot-scope="{ row }">
            <span class="table-orange">{{ row.amount ? '￥' + row.amount : '' }}{{ '元' + (row.replyUnit || row.unit ? '/' : '') + (row.replyUnit || row.unit || '') }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="采购数量">
          <template slot-scope="{ row }">{{ row.sjNum + (row.replyUnit || row.unit) }}</template>
        </el-table-column>
        <el-table-column align="center" prop="remark" label="备注"></el-table-column>
      </el-table>
      <div style="margin-top: 10px; width: 100%">
        <template v-if="!isSign">
          <el-button type="primary" @click="handleDownload" v-if="hasCertify">下载自由客App</el-button>
          <template v-else>
            <el-button style="width: 100%" type="primary" @click="handleDownload" v-if="source">为了您更好的签署体验和合同安全，请您下载自由客紧固件App进行合同签署。</el-button>
            <el-button style="width: 100%" type="primary" @click="handleWrite" v-if="!source">点此签名</el-button>
          </template>
        </template>
        <el-button style="width: 100%" type="primary" @click="showProduct = false" v-else>返回</el-button>
      </div>
    </div>
    <div class="certifyBoxBg" v-if="isCertify & !isSign">
      <div class="certifyBox">
        <img src="~@/assets/images/error.png" alt="" class="certifyBox-icon" />
        <div class="certifyBox-title">签署该合同对方要求身份认证，请使用APP签署</div>
        <div class="certifyBox-tip">请前往手机APP>消息>合同签署</div>
        <div class="certifyBox-code">
          <img src="~@/assets/images/qrcode-app.png" alt="" />
          <span>扫码下载APP</span>
        </div>
        <div class="certifyBox-button" @click="isCertify = false">我知道了</div>
      </div>
    </div>
    <div class="signBox-loading" v-if="loading">
      <img src="~@/assets/images/loading.gif" class="signBox-loading-img" alt="" />
      <span>提交中…</span>
    </div>
    <!-- 产品详情 -->
    <product-dialog ref="productInfo"></product-dialog>
  </div>
</template>

<script>
import { contractView, contractSign } from '@/api/purchase'
import SignCanvas from '@/components/SignCanvas'
import { getUnsalable } from '@/api/unsalable'
import { getProduct } from '@/api/system/product'
import { getPrivateduct } from '@/api/system/privateduct'
import { getToken } from '@/utils/auth'
import ProductDialog from '@/views/public/product/dialog'
import showTemplate from '@/views/purchase/contract/showTemplate'
import { getConfigDetail2 } from '@/api/config'

export default {
  name: 'esign',
  components: { ProductDialog, SignCanvas, showTemplate },
  data() {
    return {
      certify: false,
      signet: '',
      signature: '',
      contractImg: '',
      isWrite: false,
      requestId: undefined,
      // 手签字
      height: undefined, // 画布高度，即导出图片的高度
      lineWidth: 6, // 画笔粗细
      lineColor: '#000000', // 画笔颜色
      bgColor: '', // 画布背景色
      isCrop: true, // 是否裁剪
      quality: 1, // 生成图片质量(0-1)
      isSign: false,
      isMobile: false,
      isCertify: false,
      hasCertify: false,
      loading: false,
      products: [],
      showProduct: false
    }
  },
  created() {
    const requestId = this.$route.query.requestId
    if (requestId) this.getInfo(requestId)
    else {
      this.$confirm('参数有误，点击确定返回首页', '提示', {
        confirmButtonText: '确定',
        showCancelButton: false,
        type: 'warning'
      }).then(() => {
        this.$router.push({ path: '/' })
      })
    }
  },
  mounted() {
    window.onresize = () => {
      // 判断是否是移动端
      this.isMobile = document.body.offsetWidth <= 767
    }
  },
  beforeDestroy() {
    window.onresize = null
  },
  computed: {
    source() {
      return this.$route.query.source
    }
  },
  methods: {
    // 获取合同详情
    getInfo(requestId) {
      this.requestId = requestId
      contractView({ requestId }).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          this.isSign = data.status === 2
          this.certify = !!data.certify
          this.contractImg = data.file
          this.signature = data.signature
          this.isCertify = this.hasCertify = data.certify
          this.products = data.products || []
          if (this.isJSON(this.contractImg)) {
            getConfigDetail2({ configKey: 'signatures', companyId: data.companyId, creator: data.creator }).then(res => {
              this.signet = res?.data?.configValue || ''
            })
          }
        } else this.$message.error(msg)
      })
    },
    // 签名
    async handleWrite() {
      const dom = document.querySelector('#app')
      if (dom && dom.offsetWidth > 767) {
        if (dom && dom.offsetHeight) this.height = dom.offsetHeight - 150
      } else {
        if (dom && dom.offsetHeight) this.height = dom.offsetHeight - 80
      }
      this.isWrite = true
      this.showProduct = false
    },
    // 返回
    handleBack() {
      this.isWrite = false
    },
    // 确定
    // prettier-ignore
    handleGenerate(res) {
      const autoSynthesis = this.isBase64Image(this.contractImg)
      this.loading = true
      contractSign({ requestId: this.requestId, signature: res, autoSynthesis }).then(response => {
        const { code, msg } = response
        if (code === 200) {
          this.$message.success('签名成功')
          this.isWrite = false
          this.getInfo(this.requestId)
          document.exitFullscreen()
          localStorage.removeItem('signId')
          localStorage.removeItem('signWrite')
        } else this.$message.error(msg)
        this.loading = false
      })
      setTimeout(() => {
        if (this.loading) this.loading = false
      }, 5000)
    },
    // 下载APP
    handleDownload() {
      // 点击进行下载App
      window.location.href = 'https://oss.ziyouke.net/apk/ziyoukeV.apk'
    },
    // 查看产品详情
    handleDetail(row) {
      if (!getToken()) {
        this.$message.info('如需查询产品详情，请先登录，如无账号请先注册')
        return
      }
      if (row.source === 'common') {
        getProduct(row.productId).then(res => {
          this.$refs.productInfo.handleView(res.data)
        })
      } else {
        getPrivateduct(row.productId).then(res => {
          this.$refs.productInfo.handleView(res.data)
        })
      }
    },
    // 判断是不是json
    isJSON(str) {
      try {
        JSON.parse(str)
        return true
      } catch (e) {
        return false
      }
    },
    // 判断是不是base64
    isBase64Image(str) {
      if (typeof str === 'string') {
        return str.startsWith('data:image/') && str.includes(';base64,')
      }
      return false
    }
  }
}
</script>
<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
.signBox {
  margin: 0 auto;
  &-view {
    width: 100%;
    margin: 0 auto;
    height: auto;
    display: flex;
    flex-direction: column;
    padding: 10px;
    img {
      width: 100%;
      height: auto;
      margin-bottom: 10px;
    }
  }
  &-product {
    width: 100%;
    margin: 0 auto;
    height: auto;
    display: flex;
    flex-direction: column;
    padding: 10px;
  }
  &-loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    z-index: 99999;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    &-img {
      width: 300px;
      height: 300px;
    }
    span {
      color: #ffffff;
      font-size: 19px;
      line-height: 40px;
    }
  }
}
.certifyBoxBg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 99999;
  display: flex;
  justify-content: center;
  align-items: center;
  .certifyBox {
    width: 750px;
    padding: 30px 0;
    background: #ffffff;
    border-radius: 10px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    &-icon {
      width: 36px;
      height: 36px;
    }
    &-title {
      font-size: 14px;
      line-height: 20px;
      margin-top: 17px;
      color: #333333;
    }
    &-tip {
      font-size: 12px;
      line-height: 20px;
      margin-top: 10px;
      color: #2e73f3;
    }
    &-code {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 150px;
      height: 150px;
      border: 1px solid #f6f6f6;
      border-radius: 5px;
      margin-top: 10px;
      img {
        width: 110px;
        height: 110px;
      }
      span {
        font-size: 12px;
        line-height: 20px;
        color: #666666;
      }
    }
    &-button {
      width: 250px;
      line-height: 50px;
      background-color: #2e73f3;
      border-radius: 5px;
      color: #ffffff;
      font-size: 16px;
      margin-top: 20px;
      text-align: center;
    }
  }
}
// 移动端样式
@media screen and (max-width: 819px) {
  .certifyBoxBg {
    .certifyBox {
      width: 90%;
    }
  }
}
</style>
