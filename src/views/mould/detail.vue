<template>
  <div>
    <el-dialog v-dialogDragBox :visible.sync="open" width="1150px" class="custom-dialog noHeader" :show-close="false" append-to-body>
      <div class="product-view-tab">
        <div class="tab-box">
          <div class="tab-item" :class="{ 'tab-active': tabName === 'index' }" @click="handleTab('index')">详情</div>
          <div class="tab-item" :class="{ 'tab-active': tabName === 'code' }" @click="handleTab('code')">二维码</div>
        </div>
        <i class="el-icon-close tab-close pointer" @click="open = !open"></i>
      </div>
      <div class="product-view-box" v-show="tabName === 'index'">
        <div class="product-view">
          <div class="product-img-box" v-if="imgUrlList.length">
            <div class="product-img-show">
              <img :src="mainImgUrl" />
            </div>
            <div class="product-img-list">
              <i class="el-icon-arrow-left product-img-btn" :class="{ 'product-img-none': imgActiveIndex === 0 }" @click="imgLeft()"></i>
              <ul class="product-img-ul">
                <li v-for="(item, index) in imgUrlList" :key="index" class="product-img-li" :class="{ 'product-img-active': index === imgActiveIndex }" :style="imgStyle" @click="changeImg(item, index)">
                  <img :src="item" />
                </li>
              </ul>
              <i class="el-icon-arrow-right product-img-btn" :class="{ 'product-img-none': imgActiveIndex === imgUrlList.length - 1 }" @click="imgRight()"></i>
            </div>
          </div>
          <el-descriptions :column="1" border labelClassName="product-desc-label" content-class-name="product-desc-content">
            <el-descriptions-item>
              <template slot="label">模具名称</template>
              {{ info.name }}
            </el-descriptions-item>
            <el-descriptions-item v-if="info.brand">
              <template slot="label">模具品牌</template>
              {{ info.brand }}
            </el-descriptions-item>
            <el-descriptions-item v-if="info.model">
              <template slot="label">模具型号</template>
              {{ info.model }}
            </el-descriptions-item>
            <el-descriptions-item v-if="info.specs">
              <template slot="label">模具规格</template>
              {{ info.specs }}
            </el-descriptions-item>
            <el-descriptions-item v-if="info.attributes">
              <template slot="label">模具属性</template>
              {{ info.attributes }}
            </el-descriptions-item>
            <el-descriptions-item v-if="info.surface">
              <template slot="label">表面处理</template>
              {{ info.surface }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
        <div class="factory-box" v-if="videoList.length">
          <div class="factory-title"><span>视频</span></div>
          <div class="factory-img">
            <ul class="factory-img-ul" ref="factory">
              <video v-for="(item, index) in videoList" :key="index" class="factory-img-li" :style="{ height: height }" :src="item" type="video/mp4" controls muted></video>
            </ul>
          </div>
        </div>
      </div>
      <div style="padding: 30px 0" v-show="tabName === 'code'">
        <div class="qrcode-box">
          <div id="code">
            <div id="qrcode" ref="qrcode"></div>
          </div>
          <div class="qrcode-btn">
            <el-button type="primary" icon="el-icon-download" @click="handleDownload">下载二维码</el-button>
            <el-button type="primary" icon="el-icon-edit" @click="handleEdit">下载其他样式</el-button>
          </div>
        </div>
      </div>
    </el-dialog>
    <!-- 美化二维码 -->
    <el-dialog v-dialogDragBox title="修改样式" :visible.sync="editQrcode" width="1150px" class="custom-dialog" append-to-body>
      <div class="edit-qrcode-box">
        <div class="edit-qrcode-img">
          <div id="edit-qrcode">
            <div id="editQrcode" ref="editQrcode"></div>
          </div>
        </div>
        <div class="edit-qrcode-edit">
          <el-row :gutter="10">
            <el-col :span="8">
              <div class="edit-qrcode-edit-item">
                <span>前景色</span>
                <el-color-picker v-model="editInfo.colorDark" @change="handleCreateQrcode" />
              </div>
            </el-col>
            <el-col :span="8">
              <div class="edit-qrcode-edit-item">
                <span>背景色</span>
                <el-color-picker v-model="editInfo.colorLight" @change="handleCreateQrcode" />
              </div>
            </el-col>
            <el-col :span="24">
              <div class="edit-qrcode-edit-item">
                <span>Logo尺寸</span>
                <el-radio-group v-model="editInfo.logoSize" size="small" @input="handleCreateQrcode">
                  <el-radio-button :label="0.3">默认</el-radio-button>
                  <el-radio-button :label="0.25">中等</el-radio-button>
                  <el-radio-button :label="0.2">小型</el-radio-button>
                  <el-radio-button :label="0.16">超小</el-radio-button>
                </el-radio-group>
              </div>
            </el-col>
            <el-col :span="24">
              <div class="edit-qrcode-edit-item">
                <span>尺寸</span>
                <el-input-number v-model="editInfo.width" controls-position="right" :min="200" :max="2000"></el-input-number>
              </div>
            </el-col>
          </el-row>
          <el-button type="primary" icon="el-icon-download" style="width: 225px" @click="handleDownload">下载</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mouldDetail } from '@/api/mould'
import { isExternal } from '@/utils/validate'
import QRCode from 'qrcodejs2'

export default {
  data() {
    return {
      open: false,
      info: {},
      tabName: 'index',
      // 图片
      mainImgUrl: undefined,
      imgUrlList: [],
      imgActiveIndex: 0,
      imgDistance: 0,
      // 视频
      videoList: [],
      height: 0,
      qrcodeUrl: undefined,
      // 美化二维码
      editQrcode: false,
      editInfo: {}
    }
  },
  computed: {
    imgStyle() {
      return {
        transform: `translate3d(${this.imgDistance}px, 0, 0)`
      }
    }
  },
  mounted() {
    window.addEventListener('resize', this.getFactoryImgUlWidth)
  },
  methods: {
    getFactoryImgUlWidth() {
      this.$nextTick(() => {
        const factoryImgUl = this.$refs.factory
        if (factoryImgUl) {
          const width = factoryImgUl.offsetWidth
          this.height = `${(width - 45) / 4}px`
        }
      })
    },
    // 创建二维码
    createCode() {
      this.$nextTick(() => {
        const text = 'https://www.ziyouke.net/mouldView?id=' + this.info.uniqueId
        const w = 500
        const lw = 500 * 0.3
        let container = this.$refs.qrcode
        if (container.innerHTML != '') container.innerHTML = ''
        let qrcode = new QRCode(container, {
          width: w, // 二维码宽度
          height: w, // 二维码高度
          text: text,
          colorDark: '#000000',
          colorLight: '#ffffff',
          correctLevel: QRCode.CorrectLevel.H
        })
        qrcode._el.title = '赋码'
        let logo = new Image()
        logo.crossOrigin = 'Anonymous'
        logo.src = require('@/assets/logo/logo_outline.png')
        logo.onload = () => {
          let qrImg = qrcode._el.getElementsByTagName('img')[0]
          let canvas = qrcode._el.getElementsByTagName('canvas')[0]
          canvas.style.display = 'none'
          let ctx = canvas.getContext('2d')
          // img,x,y,width,height
          ctx.drawImage(logo, (w - lw) / 2, (w - lw) / 2, lw, lw)
          qrImg.src = canvas.toDataURL()
          qrImg.style.display = 'block'
          const code = document.getElementById('code')
          code.appendChild(qrcode._el)
          this.qrcodeUrl = canvas.toDataURL()
        }
        qrcode.clear()
        qrcode.makeCode(text)
      })
    },
    // 切换tab
    handleTab(name) {
      this.tabName = name
    },
    // 获取详情
    getDetail(data = {}) {
      const { uniqueId } = data
      if (!uniqueId) return
      mouldDetail({ uniqueId }).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          this.info = { ...data }
          this.imgActiveIndex = 0
          this.imgDistance = 0
          const picture = (data?.picture_oss && data?.picture_oss.split(',')) || (data?.picture && data?.picture.split(',')) || []
          this.mainImgUrl = picture[0] || ''
          this.imgUrlList = picture.length ? picture.map(item => (isExternal(item) ? item : this.imgPath + item)) : []
          const video = (data?.video_oss && data?.video_oss.split(',')) || (data?.video && data?.video.split(',')) || []
          this.videoList = video.length ? video.map(item => (isExternal(item) ? item : this.imgPath + item)) : []
          this.tabName = 'index'
          this.open = true
          this.createCode()
          this.getFactoryImgUlWidth()
        } else this.$message.error(msg)
      })
    },
    // 改变图片显示
    changeImg(item, idx) {
      this.mainImgUrl = item
      this.imgActiveIndex = idx
      if (idx < 4) {
        let index = 0
        const temp = window.setInterval(() => {
          if (index < 25) {
            if (this.imgDistance < 0) this.imgDistance += 2
            index++
            return false
          } else {
            window.clearInterval(temp)
          }
        }, 10)
      }
    },
    // 左滚动图片
    imgLeft() {
      let index
      if (this.imgActiveIndex > 0) {
        this.imgActiveIndex--
        this.imgUrlList.forEach((item, index) => {
          if (this.imgActiveIndex === index) {
            this.mainImgUrl = item
          }
        })
      }
      if (this.imgActiveIndex >= 4) {
        index = 0
        const temp = window.setInterval(() => {
          if (index < 25) {
            this.imgDistance += 2
            index++
            return false
          } else {
            window.clearInterval(temp)
          }
        }, 10)
      } else {
        index = 0
        const temp = window.setInterval(() => {
          if (index < 25) {
            if (this.imgDistance < 0) this.imgDistance += 2
            index++
            return false
          } else {
            window.clearInterval(temp)
          }
        }, 10)
      }
    },
    // 右滚动图片
    imgRight() {
      if (this.imgActiveIndex < this.imgUrlList.length - 1) {
        this.imgActiveIndex++
        this.imgUrlList.forEach((item, index) => {
          if (this.imgActiveIndex === index) {
            this.mainImgUrl = item
          }
        })
        if (this.imgActiveIndex >= 5 && this.imgDistance > -50 * (this.imgActiveIndex - 4)) {
          let index = 0
          const temp = window.setInterval(() => {
            if (index < 25) {
              this.imgDistance -= 2
              index++
              return false
            } else {
              window.clearInterval(temp)
            }
          }, 10)
        }
      }
    },
    // 下载二维码
    handleDownload() {
      const img = new Image()
      img.src = this.qrcodeUrl
      img.setAttribute('crossOrigin', 'Anonymous')
      img.onload = () => {
        const canvas = document.createElement('canvas')
        canvas.width = this.editInfo.width || img.width
        canvas.height = this.editInfo.width || img.height

        const ctx = canvas.getContext('2d')
        ctx.drawImage(img, 0, 0, canvas.width, canvas.height)
        canvas.toBlob(blob => {
          let blobUrl = URL.createObjectURL(blob)
          const a = document.createElement('a')
          a.href = blobUrl
          a.download = this.info.name + '二维码.png'
          a.click()
        })
      }
    },
    // 美化二维码
    handleEdit() {
      this.editInfo = {
        colorDark: '#000000',
        colorLight: '#ffffff',
        width: 500,
        height: 500,
        logoSize: 0.3
      }
      this.editQrcode = true
      this.$nextTick(() => {
        this.handleCreateQrcode()
      })
    },
    // 创建美化二维码
    handleCreateQrcode() {
      if (!this.editInfo.colorDark) {
        this.editInfo.colorDark = '#000000'
        return
      }
      if (!this.editInfo.colorLight) {
        this.editInfo.colorLight = '#ffffff'
        return
      }
      const text = 'https://www.ziyouke.net/mouldView?id=' + this.info.uniqueId
      const w = 500
      const lw = w * this.editInfo.logoSize
      let container = this.$refs.editQrcode
      if (container.innerHTML != '') container.innerHTML = ''
      let qrcode = new QRCode(container, {
        width: w, // 二维码宽度
        height: w, // 二维码高度
        text: text,
        colorDark: this.editInfo.colorDark,
        colorLight: this.editInfo.colorLight,
        correctLevel: QRCode.CorrectLevel.H
      })
      qrcode._el.title = '赋码'
      let logo = new Image()
      logo.crossOrigin = 'Anonymous'
      logo.src = require('@/assets/logo/logo_outline.png')
      logo.onload = () => {
        let qrImg = qrcode._el.getElementsByTagName('img')[0]
        let canvas = qrcode._el.getElementsByTagName('canvas')[0]
        canvas.style.display = 'none'
        let ctx = canvas.getContext('2d')
        // img,x,y,width,height
        ctx.drawImage(logo, (w - lw) / 2, (w - lw) / 2, lw, lw)
        qrImg.src = canvas.toDataURL()
        qrImg.style.display = 'block'
        const code = document.getElementById('edit-qrcode')
        code.appendChild(qrcode._el)
        this.qrcodeUrl = canvas.toDataURL()
      }
      qrcode.clear()
      qrcode.makeCode(text)
    }
  }
}
</script>
<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
.custom-dialog.noHeader ::v-deep {
  .el-dialog {
    border-radius: 5px;
    overflow: hidden;
    .el-dialog__header {
      display: none;
    }
    .el-dialog__body {
      padding: 0;
    }
  }
}
.el-descriptions ::v-deep {
  flex: 1;
  overflow: hidden;
  padding-left: 20px;
  .product-desc-label {
    width: 135px !important;
    text-align: center;
  }
  .product-desc-content {
    padding-left: 85px;
    .brand {
      color: #7f4d03;
      display: inline-block;
      border: 1px solid #fdcc8b;
      background: linear-gradient(102deg, #fde4af 0%, #f6c576 100%);
      border-radius: 5px;
      height: 24px;
      padding: 0 15px;
    }
  }
}
.factory-img-ul {
  width: 100% !important;
  gap: 15px;
  padding-top: 20px !important;
  .factory-img-li {
    width: calc((100% - 45px) / 4) !important;
    margin: 0 !important;
  }
}
.qrcode-box {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.qrcode-btn {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  gap: 20px;
}
@media (max-width: 1199px) {
  .el-descriptions ::v-deep {
    .product-desc-label {
      width: 8em !important;
      text-align: center;
    }
    .product-desc-content {
      padding-left: 10px;
    }
  }
}
.edit-qrcode {
  &-box {
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    gap: 30px;
  }
  &-img {
    width: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  &-edit {
    &-item {
      display: flex;
      align-items: center;
      padding-bottom: 15px;
      span {
        flex-shrink: 0;
        margin-right: 15px;
      }
    }
  }
}
</style>
