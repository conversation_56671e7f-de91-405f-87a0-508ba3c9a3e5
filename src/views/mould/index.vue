<template>
  <div class="newBox bgcf9 vh-85">
    <!-- 搜索 -->
    <div class="custom-search flex" style="padding-top: 18px">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
        <el-form-item label="名称" prop="name">
          <el-input v-model="queryParams.name" placeholder="请输入模具名称" @keyup.enter.native="handleQuery" clearable></el-input>
        </el-form-item>
        <el-form-item label="属性" prop="attribute">
          <el-input v-model="queryParams.attribute" placeholder="请输入属性" @keyup.enter.native="handleQuery" clearable></el-input>
        </el-form-item>
        <el-form-item label="品牌" prop="brand">
          <el-input v-model="queryParams.brand" placeholder="请输入品牌" @keyup.enter.native="handleQuery" clearable></el-input>
        </el-form-item>
        <el-form-item label="型号" prop="model">
          <el-input v-model="queryParams.model" placeholder="请输入型号" @keyup.enter.native="handleQuery" clearable></el-input>
        </el-form-item>
        <el-form-item label="规格" prop="specs">
          <el-input v-model="queryParams.specs" placeholder="请输入规格" @keyup.enter.native="handleQuery" clearable></el-input>
        </el-form-item>
        <el-form-item label="表面" prop="surface">
          <el-input v-model="queryParams.surface" placeholder="请输入表面" @keyup.enter.native="handleQuery" clearable></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
          <el-button type="primary" icon="el-icon-plus" size="small" @click="handleCreate">新增</el-button>
        </el-form-item>
      </el-form>
    </div>
    <!-- tab切换 -->
    <div class="classify flex">
      <div class="classify-item" :class="{ active: queryParams.status == 1 }" @click="handleQuery('status', 1)">已启用</div>
      <div class="classify-item" :class="{ active: queryParams.status == 0 }" @click="handleQuery('status', 0)">未启用</div>
    </div>
    <!-- 列表 -->
    <div style="padding: 15px 20px">
      <el-empty v-if="total === 0 && !loading" />
      <el-table v-loading="loading" ref="table" stripe :data="list" class="custom-table" v-if="total || loading">
        <el-table-column align="center" type="index" label="序号"></el-table-column>
        <el-table-column align="center" prop="name" label="模具名称" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" label="图片" width="75">
          <template slot-scope="{ row }">
            <image-preview :src="row.picture_oss || row.picture" :width="50" :height="50" />
          </template>
        </el-table-column>
        <el-table-column align="center" prop="specs" label="规格" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" prop="model" label="型号" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" prop="brand" label="品牌" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" prop="surface" label="表面处理" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" prop="attribute" label="属性" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" prop="createBy" label="创建人" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" prop="createTime" label="创建时间" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" prop="status" label="状态">
          <template slot-scope="{ row }">
            <el-switch v-model="row.status" active-text="启用" inactive-text="停用" :active-value="1" :inactive-value="0" @change="handleStatus(row)" class="table-switch"></el-switch>
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作" width="220">
          <template slot-scope="{ row }">
            <el-button class="table-btn" @click="handleDetail(row)">查看详情</el-button>
            <el-button class="table-btn primary" @click="handleEdit(row)">修改</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="custom-pagination" v-if="total > 0 && !loading">
        <pagination :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
      </div>
    </div>
    <!-- 新增模具 -->
    <create ref="create" @refresh="refreshList" />
    <!-- 模具详情 -->
    <detail ref="detail" />
  </div>
</template>

<script>
import { mouldList, mouldUpdateStatus } from '@/api/mould'
import create from './create.vue'
import detail from './detail.vue'

export default {
  name: 'Mould',
  components: { create, detail },
  data() {
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        attribute: undefined,
        brand: undefined,
        model: undefined,
        name: undefined,
        specs: undefined,
        status: 1,
        surface: undefined
      },
      loading: true,
      list: [],
      total: 0
    }
  },
  created() {
    // 获取列表
    this.getList()
  },
  methods: {
    // 获取列表
    // prettier-ignore
    getList() {
      this.loading = true
      mouldList(this.queryParams).then(res => {
        const { code, msg, rows, total } = res
        if (code === 200) {
          this.list = rows
          this.total = total
          this.loading = false
        } else this.$message.error(msg)
      }).finally(() => {
        this.loading = false
      })
    },
    // 刷新列表
    refreshList() {
      mouldList(this.queryParams).then(res => {
        const { code, msg, rows, total } = res
        if (code === 200) {
          this.$set(this, 'list', rows)
          this.$set(this, 'total', total)
        } else this.$message.error(msg)
      })
    },
    // 搜索
    handleQuery(key = null, value = null) {
      if (key) this.queryParams[key] = value
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 重置搜索
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 修改状态
    // prettier-ignore
    handleStatus(row) {
      const { uniqueId, status } = row
      const text = status === 0 ? '停用' : '启用'
      this.$modal.confirm('确认要"' + text + '"该模具吗？').then(function () {
        return mouldUpdateStatus({ uniqueId, status })
      }).then(() => {
        this.$modal.msgSuccess(text + '成功')
        this.refreshList()
      }).catch(function () {
        row.status = status === 0 ? 1 : 0
      })
    },
    // 新增模具
    handleCreate() {
      this.$refs.create.handleCreate()
    },
    // 修改模具
    handleEdit(row) {
      this.$refs.create.handleUpdate(row)
    },
    // 查看详情
    handleDetail(row) {
      this.$refs.detail.getDetail(row)
    }
  }
}
</script>
<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
.custom-table {
  .el-button {
    padding: 0;
  }
}
</style>
