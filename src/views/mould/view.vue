<template>
  <div>
    <div class="viewBox" v-if="!isMobile">
      <div class="viewBox-header">
        <div class="viewBox-container">
          <div class="viewBox-logo">
            <img src="~@/assets/logo/<EMAIL>" class="viewBox-logo-img" />
            <div class="viewBox-logo-text">
              <b>自由客紧固件</b>
              一站式采购平台
            </div>
          </div>
        </div>
      </div>
      <div class="viewBox-container">
        <div class="product-view-box">
          <div class="product-view">
            <div class="product-img-box" v-if="imgUrlList.length">
              <div class="product-img-show">
                <img :src="mainImgUrl" />
              </div>
              <div class="product-img-list">
                <i class="el-icon-arrow-left product-img-btn" :class="{ 'product-img-none': imgActiveIndex === 0 }" @click="imgLeft()"></i>
                <ul class="product-img-ul">
                  <li v-for="(item, index) in imgUrlList" :key="index" class="product-img-li" :class="{ 'product-img-active': index === imgActiveIndex }" :style="imgStyle" @click="changeImg(item, index)">
                    <img :src="item" />
                  </li>
                </ul>
                <i class="el-icon-arrow-right product-img-btn" :class="{ 'product-img-none': imgActiveIndex === imgUrlList.length - 1 }" @click="imgRight()"></i>
              </div>
            </div>
            <el-descriptions :column="1" border labelClassName="product-desc-label" content-class-name="product-desc-content">
              <el-descriptions-item>
                <template slot="label">模具名称</template>
                {{ info.name }}
              </el-descriptions-item>
              <el-descriptions-item v-if="info.brand">
                <template slot="label">模具品牌</template>
                {{ info.brand }}
              </el-descriptions-item>
              <el-descriptions-item v-if="info.model">
                <template slot="label">模具型号</template>
                {{ info.model }}
              </el-descriptions-item>
              <el-descriptions-item v-if="info.specs">
                <template slot="label">模具规格</template>
                {{ info.specs }}
              </el-descriptions-item>
              <el-descriptions-item v-if="info.attribute">
                <template slot="label">模具属性</template>
                {{ info.attribute }}
              </el-descriptions-item>
              <el-descriptions-item v-if="info.surface">
                <template slot="label">表面处理</template>
                {{ info.surface }}
              </el-descriptions-item>
            </el-descriptions>
          </div>
          <div class="factory-box" v-show="videoList.length">
            <div class="factory-title"><span>视频</span></div>
            <div class="factory-img">
              <ul class="factory-img-ul" ref="factory">
                <video v-for="(item, index) in videoList" :key="index" class="factory-img-li" :style="{ height: height }" :src="item" type="video/mp4" controls muted></video>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="mobile" v-if="isMobile">
      <div class="mobile-tabs">
        <div class="mobile-tabs-item" :class="{ active: tabName === 'detail' }" @click="tabName = 'detail'">模具详情</div>
        <div class="mobile-tabs-item" :class="{ active: tabName === 'video' }" @click="tabName = 'video'">模具视频</div>
      </div>
      <div v-show="tabName === 'detail'">
        <div class="mobile-title">模具图片</div>
        <div class="mobile-image">
          <i class="el-icon-arrow-left mobile-image-btn" :class="{ 'mobile-image-none': imgActiveIndex === 0 }" @click="imgLeft()"></i>
          <img class="mobile-image-img" :src="mainImgUrl" />
          <i class="el-icon-arrow-right mobile-image-btn" :class="{ 'mobile-image-none': imgActiveIndex === imgUrlList.length - 1 }" @click="imgRight()"></i>
        </div>
        <ul class="mobile-image-ul">
          <li v-for="(item, index) in imgUrlList" :key="index" class="mobile-image-li" :class="{ 'mobile-image-active': index === imgActiveIndex }" :style="imgStyle" @click="changeImg(item, index)">
            <img :src="item" />
          </li>
        </ul>
        <div class="mobile-title">模具详情</div>
        <div class="mobile-desc">
          <div class="mobile-desc-item">
            <div class="mobile-desc-label">模具名称</div>
            <div class="mobile-desc-content">{{ info.name }}</div>
          </div>
          <div class="mobile-desc-item">
            <div class="mobile-desc-label">模具品牌</div>
            <div class="mobile-desc-content">{{ info.brand }}</div>
          </div>
          <div class="mobile-desc-item">
            <div class="mobile-desc-label">模具型号</div>
            <div class="mobile-desc-content">{{ info.model }}</div>
          </div>
          <div class="mobile-desc-item">
            <div class="mobile-desc-label">模具规格</div>
            <div class="mobile-desc-content">{{ info.specs }}</div>
          </div>
          <div class="mobile-desc-item">
            <div class="mobile-desc-label">模具属性</div>
            <div class="mobile-desc-content">{{ info.attribute }}</div>
          </div>
          <div class="mobile-desc-item">
            <div class="mobile-desc-label">表面处理</div>
            <div class="mobile-desc-content">{{ info.surface }}</div>
          </div>
        </div>
      </div>
      <div class="mobile-video" v-show="tabName === 'video'">
        <el-empty description="暂无视频" v-if="!videoList.length" />
        <template v-if="videoList.length">
          <video v-for="(item, index) in videoList" :key="index" :src="item" type="video/mp4" controls muted class="mobile-video-item"></video>
        </template>
      </div>
    </div>
  </div>
</template>
<script>
import { createQrcode } from '@/api/purchase'
import { isExternal } from '@/utils/validate'

export default {
  data() {
    return {
      info: {},
      // 图片
      mainImgUrl: undefined,
      imgUrlList: [],
      imgActiveIndex: 0,
      imgDistance: 0,
      // 视频
      videoList: [],
      height: 0,
      imgPath: process.env.VUE_APP_BASE_API,
      isMobile: false,
      tabName: 'detail'
    }
  },
  computed: {
    imgStyle() {
      return {
        transform: `translate3d(${this.imgDistance}px, 0, 0)`
      }
    }
  },
  created() {
    this.checkDeviceType()
    const { id } = this.$route.query
    // const id = '1868480650328961024'
    if (id) this.getInfo(id)
    else {
      this.$alert('参数错误', '提示', {
        confirmButtonText: '确定',
        callback: () => {
          this.$router.push('/')
        }
      })
    }
  },
  mounted() {
    window.addEventListener('resize', this.getFactoryImgUlWidth)
    window.addEventListener('resize', this.checkDeviceType)
  },
  methods: {
    checkDeviceType() {
      const isMobile = /android|iphone|ipad|ipod|opera mini|iemobile|wpdesktop/i.test(navigator.userAgent.toLowerCase())
      this.isMobile = isMobile || window.innerWidth < 992
    },
    getInfo(id) {
      const query = { requestId: id, type: 'mould' }
      createQrcode(query).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          this.info = { ...data }
          this.imgActiveIndex = 0
          this.imgDistance = 0
          const picture = (data?.picture_oss || data?.picture || '').split(',')
          this.mainImgUrl = isExternal(picture[0]) ? picture[0] : this.imgPath + picture[0]
          this.imgUrlList = picture.map(item => (isExternal(item) ? item : this.imgPath + item))
          const video = (data?.video_oss || data?.video || '').split(',')
          this.videoList = video.map(item => (isExternal(item) ? item : this.imgPath + item))
          this.getFactoryImgUlWidth()
        } else this.$message.error(msg)
      })
    },
    getFactoryImgUlWidth() {
      this.$nextTick(() => {
        const factoryImgUl = this.$refs.factory
        if (factoryImgUl) {
          const width = factoryImgUl.offsetWidth
          this.height = `${(width - 45) / 4}px`
        }
      })
    },
    // 改变图片显示
    changeImg(item, idx) {
      this.mainImgUrl = item
      this.imgActiveIndex = idx
      if (idx < 4) {
        let index = 0
        const temp = window.setInterval(() => {
          if (index < 25) {
            if (this.imgDistance < 0) this.imgDistance += 2
            index++
            return false
          } else {
            window.clearInterval(temp)
          }
        }, 10)
      }
    },
    // 左滚动图片
    imgLeft() {
      let index
      if (this.imgActiveIndex > 0) {
        this.imgActiveIndex--
        this.imgUrlList.forEach((item, index) => {
          if (this.imgActiveIndex === index) {
            this.mainImgUrl = item
          }
        })
      }
      if (this.imgActiveIndex >= 4) {
        index = 0
        const temp = window.setInterval(() => {
          if (index < 25) {
            this.imgDistance += 2
            index++
            return false
          } else {
            window.clearInterval(temp)
          }
        }, 10)
      } else {
        index = 0
        const temp = window.setInterval(() => {
          if (index < 25) {
            if (this.imgDistance < 0) this.imgDistance += 2
            index++
            return false
          } else {
            window.clearInterval(temp)
          }
        }, 10)
      }
    },
    // 右滚动图片
    imgRight() {
      if (this.imgActiveIndex < this.imgUrlList.length - 1) {
        this.imgActiveIndex++
        this.imgUrlList.forEach((item, index) => {
          if (this.imgActiveIndex === index) {
            this.mainImgUrl = item
          }
        })
        if (this.imgActiveIndex >= 5 && this.imgDistance > -50 * (this.imgActiveIndex - 4)) {
          let index = 0
          const temp = window.setInterval(() => {
            if (index < 25) {
              this.imgDistance -= 2
              index++
              return false
            } else {
              window.clearInterval(temp)
            }
          }, 10)
        }
      }
    }
  }
}
</script>
<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
.viewBox {
  min-height: 100vh;
  background-color: #f9f9f9;
  &-container {
    max-width: 1200px;
    margin: 0 auto;
  }
  &-header {
    height: 90px;
    background-color: $blue;
    padding-left: 14px;
  }
  &-logo {
    display: inline-flex;
    height: 90px;
    align-items: center;
    &-img {
      width: 52px;
      height: 50px;
      margin-right: 10px;
    }
    &-text {
      display: inline-flex;
      flex-direction: column;
      color: $white;
      b {
        font-size: 20px;
        font-weight: 500;
        line-height: 23px;
      }
      span {
        font-size: 20px;
        font-weight: 500;
        line-height: 19px;
      }
    }
  }
}
.el-descriptions ::v-deep {
  flex: 1;
  overflow: hidden;
  padding-left: 20px;
  .product-desc-label {
    width: 135px !important;
    text-align: center;
  }
  .product-desc-content {
    padding-left: 85px;
    .brand {
      color: #7f4d03;
      display: inline-block;
      border: 1px solid #fdcc8b;
      background: linear-gradient(102deg, #fde4af 0%, #f6c576 100%);
      border-radius: 5px;
      height: 24px;
      padding: 0 15px;
    }
  }
}
.factory-img-ul {
  width: 100% !important;
  gap: 15px;
  padding-top: 20px !important;
  .factory-img-li {
    width: calc((100% - 45px) / 4) !important;
    margin: 0 !important;
  }
}
@media (max-width: 1199px) {
  .el-descriptions ::v-deep {
    .product-desc-label {
      width: 8em !important;
      text-align: center;
    }
    .product-desc-content {
      padding-left: 10px;
    }
  }
}
.mobile {
  padding: 15px;
  &-tabs {
    margin: 0 auto 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    &-item {
      font-size: 14px;
      padding: 0 20px;
      height: 30px;
      line-height: 28px;
      background-color: #fff;
      color: #2e73f3;
      border: 1px solid #2e73f3;
      &.active {
        background-color: #2e73f3;
        color: #fff;
      }
      &:first-child {
        border-radius: 10px 0 0 10px;
      }
      &:last-child {
        border-radius: 0 10px 10px 0;
      }
    }
  }
  &-title {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    line-height: 30px;
  }
  &-image {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 10px 0;
    &-img {
      width: 60vw;
      height: 60vw;
      object-fit: cover;
      border-radius: 5px;
      margin: 0 10px;
    }
    &-btn {
      font-size: 40px;
      cursor: pointer;
      color: #2e73f3;
      &.mobile-image-none {
        color: #ccc;
      }
    }
    &-ul {
      width: 60vw;
      display: flex;
      list-style: none;
      height: 100%;
      padding: 0;
      margin: 0 auto;
      overflow: hidden;
      .mobile-image-li {
        float: left;
        width: 45px;
        height: 45px;
        flex-shrink: 0;
        border: 1px solid #cbd6e2;
        margin: 0 2.5px;
        cursor: pointer;
        img {
          width: 100%;
          height: 100%;
        }
        &.mobile-image-active {
          border-color: #2e73f3;
        }
      }
    }
  }
  &-desc {
    margin: 10px 0;
    border: 1px solid #d6d9e4;
    border-radius: 10px;
    overflow: hidden;
    &-item {
      display: flex;
      border-bottom: 1px solid #d6d9e4;
      &:last-child {
        border-bottom: none;
      }
    }
    &-label {
      flex-shrink: 0;
      padding: 5px 20px;
      background-color: #f1f2f6;
      color: #999999;
      font-size: 12px;
      border-right: 1px solid #d6d9e4;
      line-height: 25px;
    }
    &-content {
      flex: 1;
      overflow: hidden;
      padding: 5px 20px;
      background-color: #fff;
      color: #333;
      font-size: 14px;
      line-height: 25px;
      text-align: right;
    }
  }
  &-video {
    display: flex;
    flex-direction: column;
    gap: 10px;
    &-item {
      width: 100%;
      max-height: 100vw;
    }
  }
}
</style>
