<template>
  <div>
    <el-dialog v-dialogDragBox :title="title" :visible.sync="open" width="1150px" class="custom-dialog" append-to-body>
      <div style="padding: 0 20px">
        <el-form ref="form" :model="form" :rules="rules" label-width="6em">
          <el-row :gutter="10">
            <el-col :span="12">
              <el-form-item label="模具名称" prop="name">
                <el-input v-model="form.name" placeholder="请输入模具名称" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="表面处理" prop="surface">
                <el-input v-model="form.surface" placeholder="请输入表面处理" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="模具型号" prop="model">
                <el-input v-model="form.model" placeholder="请输入模具型号" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="模具品牌" prop="brand">
                <el-input v-model="form.brand" placeholder="请输入模具品牌" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="模具属性" prop="attribute">
                <el-input v-model="form.attribute" placeholder="请输入模具属性" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="模具规格" prop="specs">
                <el-input v-model="form.specs" placeholder="请输入模具规格" />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="模具图片" prop="picture">
                <image-upload v-model="form.picture" :file-type="fileType" />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="模具视频" prop="video">
                <image-upload v-model="form.video" :file-type="['mp4']" :file-size="200" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button class="custom-dialog-btn" @click="handleCancel">取消</el-button>
        <el-button class="custom-dialog-btn primary" @click="handleSubmit" v-if="!form.uniqueId">确定添加并生成二维码</el-button>
        <el-button class="custom-dialog-btn primary" @click="handleSubmit" v-if="form.uniqueId">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mouldCreate, mouldUpdate, mouldDetail } from '@/api/mould'

export default {
  data() {
    return {
      title: '新建模具',
      open: false,
      form: {},
      rules: {
        name: [{ required: true, message: '请输入模具名称', trigger: 'blur' }]
      },
      fileType: ['png', 'PNG', 'jpg', 'JPG', 'jpeg', 'JPEG'],
      // 详情
      info: {}
    }
  },
  methods: {
    // 表单重置
    reset() {
      this.form = {
        attribute: undefined,
        brand: undefined,
        model: undefined,
        name: undefined,
        picture: undefined,
        specs: undefined,
        surface: undefined,
        uniqueId: undefined,
        video: undefined
      }
      this.resetForm('form')
    },
    // 新建模具
    handleCreate() {
      this.reset()
      this.title = '新建模具'
      this.open = true
    },
    // 修改模具
    handleUpdate(data = {}) {
      this.reset()
      const uniqueId = data.uniqueId
      if (!uniqueId) return
      mouldDetail({ uniqueId }).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          this.form = { ...data }
          this.title = '修改模具'
          this.open = true
        } else this.$message.error(msg)
      })
    },
    // 详情
    handleDetail(data = {}) {
      const uniqueId = data.uniqueId
      mouldDetail({ uniqueId }).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          this.info = { ...data }
          this.title = '模具详情'
          this.open = true
        } else this.$message.error(msg)
      })
    },
    // 取消
    handleCancel() {
      this.reset()
      this.open = false
    },
    // 提交
    // prettier-ignore
    handleSubmit() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (!this.form.picture && !this.form.video) {
            this.$confirm('未添加图片或视频，是否继续？', '提示', {
              confirmButtonText: '继续',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(() => {
              this.submitForm()
            }).catch(() => {})
          } else this.submitForm()
        }
      })
    },
    // prettier-ignore
    submitForm() {
      const loading = this.$loading({
        lock: true,
        text: '提交中…',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      const api = this.form.uniqueId ? mouldUpdate : mouldCreate
      api(this.form).then(res => {
        const { code, msg } = res
        if (code === 200) {
          this.$message.success(msg)
          this.handleCancel()
          this.$emit('refresh')
        } else this.$message.error(msg)
      }).finally(() => {
        loading.close()
      })
    }
  }
}
</script>
<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
</style>
