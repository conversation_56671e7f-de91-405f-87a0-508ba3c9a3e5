<template>
  <div class="webBox">
    <head-tpl :is-login="isLogin" is-promotion @handleQuery="handleQuery"></head-tpl>
    <div class="webMain">
      <div class="webContainer">
        <div class="sale-categroy">
          <div class="sale-categroy-parent">
            <div class="inline-flex">
              <div class="parent-item" :class="{ active: item.id === parentId }" v-for="item in categoryList" :key="item.id" @click="getParentList(item)">
                {{ item.name }}
              </div>
            </div>
            <div class="parent-create" @click="handleAdd">
              <i class="el-icon-plus"></i>
              新增促销品
            </div>
          </div>
          <div class="sale-categroy-children" v-if="!!childrenList.length && showChildren">
            <div class="children-item" :class="{ active: item.id === childrenId }" v-for="item in childrenList" :key="item.id" @click="getChildrenList(item)">
              {{ item.name }}
            </div>
          </div>
        </div>
        <div class="minHeight">
          <template v-if="total">
            <div class="promotional-product-list" v-loading="loading">
              <div class="promotional-product-position" v-for="item in list" :key="item.id" @click="handleView(item)">
                <div class="promotional-product-item">
                  <div class="item-image-box">
                    <div class="tip">hot</div>
                    <el-image :src="formatProductImg(item)" class="image" lazy></el-image>
                    <div class="num" v-if="item.promotions.length">
                      <span>共</span>
                      <b>{{ item.promotions.length }}</b>
                      <span>家供应商促销</span>
                    </div>
                  </div>
                  <div class="padding-10">
                    <div class="item-title">{{ item.productName }}</div>
                    <div class="item-brand">规格：{{ item.specs }}</div>
                    <div class="item-num">供应数量：{{ getPromotion(item, 'num') }}</div>
                    <div class="item-price">
                      <span>￥</span>
                      <b>{{ getPromotion(item, 'price') }}</b>
                      <span>/{{ getPromotion(item) }}</span>
                    </div>
                    <div class="item-original">
                      <div class="item-original-tip">最低促销价</div>
                    </div>
                    <div class="item-button" @click.stop="handleBuy(item)">我要采购</div>
                  </div>
                </div>
              </div>
            </div>
            <div class="product-more" v-if="total > queryParams.pageSize">
              <div class="product-more-btn" @click="handleMoreNavProduct">
                <template v-if="isMore">
                  <i class="ssfont ss-diy-biaoqing"></i>
                  我是有底线的
                </template>
                <template v-else>
                  <i :class="moreLoading ? 'el-icon-loading' : 'el-icon-refresh'"></i>
                  加载更多
                </template>
              </div>
            </div>
          </template>
          <el-empty :description="!loading && !total ? '暂无数据' : '加载中…'" v-else />
        </div>
      </div>
    </div>
    <foot-tpl />
    <!-- 产品详情 -->
    <product-dialog ref="productInfo" append-body @callback="showProduct = false" v-if="showProduct"></product-dialog>
  </div>
</template>
<script>
import headTpl from '@/views/public/components/head'
import footTpl from '@/views/public/components/foot'
import { getToken } from '@/utils/auth'
import { getPromotionIndexList } from '@/api/promotion'
import * as category from '@/api/purchase/category'
import ProductDialog from '@/views/public/product/dialog'

export default {
  components: { ProductDialog, footTpl, headTpl },
  data() {
    return {
      isLogin: false,
      queryParams: {
        pageNum: 1,
        pageSize: 24,
        categoryId: undefined,
        productName: undefined,
        specs: undefined,
        model: undefined,
        surface: undefined,
        companyName: undefined
      },
      list: [],
      total: 0,
      isMore: false,
      moreLoading: false,
      loading: true,
      categoryList: [],
      childrenList: [],
      parentId: undefined,
      childrenId: undefined,
      showProduct: false,
      showChildren: true // 是否显示子分类
    }
  },
  created() {
    this.isLogin = !!getToken()
    this.getCategroyList()
    this.getList()
  },
  methods: {
    // 获取分类列表
    getCategroyList() {
      category.getlist().then(res => {
        const { code, msg, data } = res
        if (code === 200) this.categoryList = data
        else this.$message.error(msg)
      })
    },
    // 查询子分类及促销品列表
    getParentList(item) {
      this.childrenList = item.children
      this.parentId = item.id
      this.childrenId = undefined
      this.queryParams.categoryId = item.id
      this.resetQuery()
    },
    // 查询促销品列表
    getChildrenList(item) {
      this.childrenId = item.id
      this.queryParams.categoryId = item.id
      this.resetQuery()
    },
    // 搜索
    handleQuery(obj = {}) {
      const { type, keyword } = obj
      this.queryParams.productName = undefined
      this.queryParams.specs = undefined
      this.queryParams.model = undefined
      this.queryParams.surface = undefined
      this.queryParams.companyName = undefined
      if (type && keyword) {
        this.queryParams[type] = keyword
      }
      this.resetQuery()
    },
    // 重置查询条件
    resetQuery() {
      this.queryParams.pageNum = 1
      this.list = []
      this.getList()
    },
    // 获取促销品列表
    getList() {
      this.loading = true
      getPromotionIndexList(this.queryParams).then(res => {
        const { code, msg, rows, total } = res
        if (code === 200) {
          this.list = [...this.list, ...rows]
          this.total = total
          this.loading = false
          this.moreLoading = false
          this.isMore = this.queryParams.pageNum * this.queryParams.pageSize >= total
          this.showChildren = this.list.length || (!this.list.length && this.childrenId)
        } else this.$message.error(msg)
      })
    },
    // 加载更多
    handleMoreNavProduct() {
      if (this.isMore) return
      this.queryParams.pageNum += 1
      this.moreLoading = true
      this.getList()
    },
    // 查询促销品供应信息
    getPromotion(item, type = '') {
      const priceArr = item.promotions.map(ite => ite.price)
      const minPrice = Math.min(...priceArr)
      const index = item.promotions.findIndex(ite => ite.price === minPrice)
      let unit = ''
      if (item.promotions[index].method === 'ton') unit = '吨'
      else unit = item.unit
      if (type === 'num') return item.promotions[index].stock + unit
      else if (type === 'price') return minPrice
      else return unit
    },
    // 新增促销品跳转至管理中心
    handleAdd() {
      this.$router.push({ name: 'Promotion' })
    },
    // 我要采购跳转新页面
    handleBuy(item) {
      const { href } = this.$router.resolve({ path: '/sale/detail', query: { productId: item.id } })
      window.open(href, '_blank')
    },
    // 查看产品详情
    handleView(item) {
      this.showProduct = true
      this.$nextTick(() => {
        this.$refs.productInfo.handleView(item)
      })
    }
  }
}
</script>
<style scoped lang="scss">
@import '~@/assets/styles/custom-public.scss';
.promotional-product-list {
  height: auto;
  margin: 20px 0 20px -12px;
  .promotional-product-position {
    height: 375px;
    position: relative;
    margin-left: 12px;
    margin-bottom: 12px;
    .promotional-product-item:hover {
      z-index: 3;
    }
  }
}
.sale-categroy {
  margin-top: 15px;
  width: 100%;
  display: flex;
  flex-direction: column;
  box-shadow: 0 0 26px 0 rgba(0, 0, 0, 0.05);
  border-radius: 5px;
  overflow: hidden;
  &-parent {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 50px;
    border: 1px solid #e6e6e7;
    border-bottom-color: $blue;
    border-radius: 5px 5px 0 0;
    .parent-item {
      width: 130px;
      text-align: center;
      height: 50px;
      line-height: 50px;
      cursor: pointer;
      margin: -1px 0;
      font-size: 14px;
      &:hover,
      &.active {
        transition: all 0.1s;
        line-height: 48px;
        background-color: $white;
        color: $blue;
        border: 1px solid $blue;
        border-radius: 5px 5px 0 0;
        border-bottom-color: $white;
      }
    }
    .parent-create {
      display: flex;
      align-items: center;
      padding: 0 20px;
      cursor: pointer;
      color: $blue;
      font-size: 14px;
      i {
        margin-right: 5px;
      }
    }
  }
  &-children {
    background-color: $white;
    padding: 10px 20px;
    display: flex;
    flex-wrap: wrap;
    .children-item {
      width: calc(100% / 6);
      height: 40px;
      line-height: 40px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      font-size: 14px;
      color: $info;
      cursor: pointer;
      &:hover,
      &.active {
        color: $blue;
      }
    }
  }
}
.minHeight {
  min-height: 400px;
}
.padding-10 {
  padding: 10px;
}
</style>
