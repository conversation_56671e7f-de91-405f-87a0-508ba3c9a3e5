<template>
  <div class="webBox">
    <head-tpl :is-login="isLogin" :isSearch="false"></head-tpl>
    <div class="webMain">
      <div class="webContainer" v-if="!isOrder">
        <div class="deadView-title">生成采购合同</div>
        <div class="deadView-order">
          <span>创建时间：{{ createTime }}</span>
        </div>
        <div class="deadView-info">
          <div style="overflow: hidden">
            <div class="deadView-info-img" @click="handleView(info)">
              <el-image :src="formatProductImg(info)" lazy>
                <div slot="error" class="image-slot">
                  <i class="el-icon-picture-outline"></i>
                </div>
              </el-image>
            </div>
            <div class="deadView-info-con">
              <div class="deadtitle" @click="handleView(info)">{{ info.productName }}</div>
              <div class="deadnum">编码：{{ info.productCode }}</div>
              <div class="deadnum">规格：{{ info.specs }}</div>
              <div class="deadnum">型号：{{ info.model }}</div>
              <div class="deadprice"></div>
              <div style="margin-top: 15px">
                <el-table ref="priceList" stripe :data="priceList" :show-header="false" class="custom-table"
                  :row-style="{ cursor: 'pointer' }" @row-click="handleRowClick">
                  <el-table-column align="center" label="选择" width="55">
                    <template slot-scope="{ row }">
                      <el-radio v-model="radio.companyId" :label="row.companyId"><span /></el-radio>
                    </template>
                  </el-table-column>
                  <el-table-column label="供应商" align="center" show-overflow-tooltip>
                    <template slot-scope="{ row }">
                      <span class="table-link" @click="handleViewCompany(row)">{{ row.company.companyName }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column label="供应商" align="center" show-overflow-tooltip>
                    <template slot-scope="{ row }">
                      <div class="inline-flex">
                        <div class="table-phone pointer" v-if="!!row.customer" @click="handleContact(row)">
                          <i class="ssfont ss-diy-liaotian" />
                          <span>点我聊天</span>
                        </div>
                        <div class="table-phone pointer">
                          <i class="ssfont ss-diy-dianhua"></i>
                          <span>{{ row.company.phone }}</span>
                        </div>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="供应数量" align="center">
                    <template slot-scope="{ row }">
                      <span class="table-primary">{{ row.stock + (row.method === 'ton' ? '吨' : info.unit) }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column label="促销价格" align="center">
                    <template slot-scope="{ row }">
                      <span class="table-price">{{ '￥' + row.price + '元' + (row.method === 'ton' ? '/吨' :
                        `/${info.unit}`) }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column label="采购数量" align="center">
                    <template slot-scope="{ row }">
                      <el-input-number size="small" v-model="row.num" :min="0" :max="row.stock"></el-input-number>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </div>
          <div class="deadView-info-total">
            <div class="text" style="display: inline-flex">
              <div>
                共
                <b>{{ handleTotal('num') }}</b>
                {{ radio.method === 'ton' ? '吨' : info.unit }}
              </div>
            </div>
            <div class="text">
              总金额：
              <b>{{ handleTotal('price') }}</b>
            </div>
          </div>
        </div>
        <div class="deadView-button">
          <el-button @click="handleClose">取消</el-button>
          <el-button type="primary" :disabled="!radio.num" @click="handleSubmit">提交</el-button>
        </div>
      </div>
      <div class="webContainer" v-else>
        <div class="order-success">
          <i class="el-icon-success order-success-img"></i>
          <div class="order-success-title">采购合同提交成功</div>
          <div class="order-success-tip">提交成功后请尽快完成商品购买流程，超时操作可能造成订单商品库存不足或订单失效</div>
          <div class="order-success-info">有任何疑问请您及时联系客服。</div>
          <div class="order-success-button">
            <el-button type="primary" plain @click="handleLink('/sale')">继续逛逛</el-button>
            <el-button type="primary" @click="handleLink('/demand/contract')">查看合同</el-button>
          </div>
        </div>
      </div>
    </div>
    <foot-tpl />
    <!-- 公司信息 -->
    <offer-dialog ref="offerInfo" append-body></offer-dialog>
    <!--  生成合同  -->
    <offer-contract-dialog ref="contractDialog" @refresh="isOrder = true"></offer-contract-dialog>
    <!-- 产品详情 -->
    <product-dialog ref="productInfo" append-body></product-dialog>

    <chat ref="chat" :showBadge="false" />
  </div>
</template>
<script>
import headTpl from '@/views/public/components/head'
import footTpl from '@/views/public/components/foot'
import { getToken } from '@/utils/auth'
import { getPromotion, getPromotionProductDetail } from '@/api/promotion'
import OfferDialog from '@/views/public/product/offer'
import offerContractDialog from '@/views/purchase/demandForMe/offer'
import ProductDialog from '@/views/public/product/dialog'
import { getProduct } from '@/api/system/product'
import Chat from '@/components/Chat/index'

export default {
  components: { ProductDialog, offerContractDialog, OfferDialog, footTpl, headTpl, Chat },
  data() {
    return {
      isLogin: false,
      productId: undefined,
      info: {},
      createTime: new Date().toLocaleString(),
      priceList: [],
      isOrder: false,
      value: undefined,
      radio: {}
    }
  },
  async created() {
    this.isLogin = !!getToken()
    const { productId } = this.$route.query
    this.productId = productId
    if (this.productId) {
      await this.getInfo()
      await this.getPriceList()
    } else {
      this.$alert('参数错误，请联系管理员', '系统提示', {
        type: 'error',
        confirmButtonText: '确定',
        callback: action => {
          this.$router.push('/sale')
        }
      })
    }
  },
  methods: {
    // 查询产品详情
    getInfo() {
      const { productId } = this
      getPromotionProductDetail(productId).then(res => {
        const { code, msg, data } = res
        if (code === 200) this.info = data
        else this.$message.error(msg)
      })
    },
    // 查询供应商报价
    getPriceList() {
      const { productId } = this
      getPromotion({ productId }).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          if (!!data.length) data.map(item => (item.num = 0))
          this.priceList = data
        } else this.$message.error(msg)
      })
    },
     // 点击聊天
     handleContact(row) {
      const { customer } = row
      const data = {
        userId: customer.userId,
        nick: customer.nickName,
        avatar: this.imgPath + customer.avatar || this.defaultAvatar
      }
      this.$refs.chat.send(data)
    },
    // 选择供应商
    handleRowClick(row) {
      this.value = undefined
      this.radio = row
    },
    // 获取总采购及总金额
    handleTotal(type = 'price') {
      const { num, price } = this.radio
      if (type === 'num') {
        return num || '--'
      } else {
        const total = parseFloat((num * price).toFixed(5))
        return total ? `￥${total}元` : '--'
      }
    },
    // 关闭
    handleClose() {
      window.close()
    },
    // 提交
    handleSubmit() {
      const info = {
        maxNum: 999999999,
        source: 'common',
        productId: this.productId,
        productName: this.info.productName,
        productCode: this.info.productCode,
        specs: this.info.specs,
        model: this.info.model,
        unit: this.info.unit,
        quantity: 0,
        needQuantity: 0,
        listId: -1,
        remark: this.info.remark,
        amount: this.radio.price,
        originAmount: this.radio.price,
        replyUnit: this.radio.method === 'ton' ? '吨' : this.info.unit,
        sjNum: this.radio.num,
        replyRemark: ''
      }
      const sellerUser = { ...this.radio }
      this.$refs.contractDialog.handleGetContract(sellerUser, info, 'no', 'promotion')
      // this.isOrder = true
    },
    // 查看供应商信息
    handleViewCompany(item) {
      this.$refs.offerInfo.handleInfo(item)
    },
    handleLink(url) {
      this.$router.push(url)
    },
    // 查看产品详情
    async handleView(item) {
      const { id } = item
      const { data } = await getProduct(id)
      this.$refs.productInfo.handleView(data)
    }
  }
}
</script>
<style scoped lang="scss">
@import '~@/assets/styles/custom-public.scss';
@import '~@/assets/styles/custom-new.scss';

.deadView-info-img:hover {
  cursor: pointer;

  ::v-deep {
    img {
      transition: all 0.3s;
      transform: scale(1.2);
    }
  }
}

.table-phone {
  display: inline-flex;
  flex-wrap: nowrap;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  border-radius: 30px;
  background-color: #d7e5ff;
  color: #2e73f3;

  span {
    display: none;
  }

  &:hover {
    width: auto;
    padding: 0 10px;
    background-color: #2e73f3;
    color: #ffffff;

    span {
      display: inline-block;
    }
  }
}

.table-phone+.table-phone {
  margin-left: 10px;
}

.deadtitle:hover {
  color: $blue;
  cursor: pointer;
}
</style>
