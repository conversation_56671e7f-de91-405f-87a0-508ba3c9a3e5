<template>
  <div>
    <el-dialog title="绑定手机号码" :visible.sync="open" width="750px" class="custom-dialog" top="0 !important" :close-on-click-modal="false" :show-close="false">
      <div class="noPhone">
        <img src="@/assets/images/noPhoneIcon.png" class="noPhoneIcon" alt="绑定手机号码" />
        <span class="noPhoneText">为了保证您可以正常使用自由客，请先绑定您的手机号码</span>
      </div>
      <el-form ref="form" :model="form" :rules="rules" class="form" label-width="100px" label-position="left" hide-required-asterisk>
        <el-form-item label="国家" prop="phoneCode">
          <el-select v-model="form.phoneCode" placeholder="请选择国家" style="width: 100%" filterable>
            <el-option v-for="(item, index) in countryCodeList" :key="`${item.value}-${index}`" :label="`${item.country} (+${item.value})`" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="form.phone" auto-complete="off" placeholder="请输入要绑定的手机号码" @change="checkPhone"></el-input>
        </el-form-item>
        <el-form-item label="验证码" prop="code">
          <el-input v-model="form.code" :disabled="!isNext" auto-complete="off" placeholder="请输入短信验证码">
            <template slot="append">
              <el-button :class="{ primary: !!form.phone && isNext }" :loading="sending && !!form.phone" :disabled="!sending && !form.phone && !isNext" @click="handleSend">{{ sendTitle }}</el-button>
            </template>
          </el-input>
        </el-form-item>
        <template v-if="isNew">
          <el-form-item label="登录密码" prop="password">
            <el-input v-model="form.password" type="password" auto-complete="off" placeholder="请输入登录密码" show-password></el-input>
          </el-form-item>
          <el-form-item label="确认密码" prop="confirmPassword">
            <el-input v-model="form.confirmPassword" type="password" auto-complete="off" placeholder="请再次输入登录密码" show-password></el-input>
          </el-form-item>
        </template>
      </el-form>
      <div slot="footer">
        <div class="footerButton">
          <button type="button" class="custom-dialog-btn small" @click="handleClose">取消</button>
          <button type="button" class="custom-dialog-btn small primary" @click="handleSubmit">立即绑定</button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { smsCode } from '@/api/system/user'
import { putAuthbindPhone, searchUsePhone } from '@/api/login'
import { countryCodeList, getCountryIndexByIP } from '@/utils/countryCode'
import router from '@/router'

export default {
  data() {
    const equalToPassword = (rule, value, callback) => {
      if (this.form.password !== value) {
        callback(new Error('两次输入的密码不一致'))
      } else {
        callback()
      }
    }
    // 手机号验证规则
    const validatePhone = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入您的手机号'))
        return
      }
      // 根据国家进行不同的验证
      const phoneCode = this.form.phoneCode
      if (phoneCode === '86') {
        // 中国手机号验证
        if (!/^1[3-9]\d{9}$/.test(value)) {
          callback(new Error('请输入正确的中国手机号'))
        } else {
          callback()
        }
      } else {
        // 其他国家简单验证（6-15位数字）
        if (!/^\d{6,15}$/.test(value)) {
          callback(new Error('请输入正确的手机号码'))
        } else {
          callback()
        }
      }
    }
    return {
      countryCodeList,
      open: false,
      form: {},
      rules: {
        phoneCode: [{ required: true, trigger: 'change', message: '请选择国家' }],
        phone: [{ required: true, validator: validatePhone, trigger: 'blur' }],
        password: [
          { required: true, trigger: 'blur', message: '请输入您的密码' },
          { min: 6, max: 20, message: '用户密码长度必须介于 6 和 20 之间', trigger: 'blur' }
        ],
        confirmPassword: [
          { required: true, trigger: 'blur', message: '请再次输入您的密码' },
          { required: true, validator: equalToPassword, trigger: 'blur' }
        ],
        code: [{ required: true, trigger: 'blur', message: '请输入短信验证码' }]
      },
      sending: false,
      second: 0,
      isNext: false,
      isNew: false
    }
  },
  computed: {
    sendTitle() {
      return this.sending ? `${this.second}s后重新获取` : this.second ? '重新发送' : '获取验证码'
    }
  },
  methods: {
    // 根据IP自动定位国家
    async autoLocateCountry() {
      try {
        const countryIndex = await getCountryIndexByIP()
        if (countryIndex !== -1 && countryIndex < this.countryCodeList.length) {
          const countryInfo = this.countryCodeList[countryIndex]
          this.form.phoneCode = countryInfo.value
        }
      } catch (error) {
        console.warn('自动定位失败，使用默认设置', error)
      }
    },
    // 表单重置
    reset() {
      this.form = {
        phoneCode: '86', // 默认设置为中国
        phone: undefined,
        code: undefined,
        password: undefined,
        confirmPassword: undefined
      }
      this.resetForm('form')
    },
    async handleOpen() {
      this.reset()
      this.open = true
      // 自动定位国家
      await this.autoLocateCountry()
    },
    // 检查手机号是否已注册
    checkPhone() {
      this.$refs.form.validateField('phone', valid => {
        if (!valid) {
          searchUsePhone({ phone: this.form.phone }).then(res => {
            const { code, msg, data } = res
            if (code === 200) {
              if (data) {
                if (data.status == '1') {
                  this.$confirm('该手机号已注册,已停用，请联系管理员。是否继续绑定其他手机号？', '提示', {
                    confirmButtonText: '继续',
                    cancelButtonText: '取消',
                    type: 'warning'
                  }).then(() => {
                    this.form.phone = ''
                    this.form.code = ''
                    this.form.password = ''
                    this.form.confirmPassword = ''
                  })
                } else {
                  this.$confirm('该手机号已注册，是否继续绑定？', '提示', {
                    confirmButtonText: '继续',
                    cancelButtonText: '取消',
                    type: 'warning'
                  }).then(() => {
                    this.isNext = true
                    this.isNew = false
                    this.form.code = ''
                    this.form.password = ''
                    this.form.confirmPassword = ''
                  })
                }
              } else {
                this.isNext = true
                this.isNew = true
                this.form.code = ''
                this.form.password = ''
                this.form.confirmPassword = ''
              }
            } else this.$message.error(msg)
          })
        }
      })
    },
    // 发送短信验证码
    handleSend() {
      this.$refs.form.validateField('phone', valid => {
        if (!valid) {
          this.sending = true
          this.second = 60
          smsCode({ phone: this.form.phone, phoneCode: this.form.phoneCode }).then(res => {
            this.$message.success('验证码发送成功')
            const bindingTimer = setInterval(() => {
              this.second--
              if (this.second <= 0) {
                clearInterval(bindingTimer)
                this.sending = false
                this.second = 60
              }
            }, 1000)
          })
        }
      })
    },
    // 提交绑定手机号
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          const data = {
            phone: this.form.phone ? this.form.phone : undefined,
            phoneCode: this.form.phoneCode,
            code: this.form.code,
            password: this.form.password
          }
          putAuthbindPhone(data).then(res => {
            const { code, msg } = res
            if (code === 200) {
              this.$message.success('绑定成功')
              this.$store.dispatch('RefreshToken', msg).then(() => {
                this.$store.dispatch('GetInfo').then(info => {
                  this.$store.dispatch('GenerateRoutes').then(accessRoutes => {
                    // 根据roles权限生成可访问的路由表
                    router.addRoutes(accessRoutes) // 动态添加可访问路由表
                    next({ ...to, replace: true }) // hack方法 确保addRoutes已完成
                  })
                  this.$store.dispatch('GetTenderTime').then(tender => {
                    this.$store.dispatch('GetIsCustomerService').then(service => {
                      this.open = false
                    })
                  })
                })
              })
            } else this.$message.error(msg)
          })
        }
      })
    },
    // 取消绑定手机号添加确认框提示
    // prettier-ignore
    handleClose() {
      this.$confirm('跳过绑定手机号将无法使用部分功能，是否继续？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.open = false
      }).catch(() => {})
    }
  }
}
</script>
<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';
::v-deep {
  .el-dialog {
    position: absolute;
    top: 45%;
    left: 50%;
    font-size: 18px;
    transform: translate(-50%, -50%);
    .el-dialog__body {
      padding-top: 0 !important;
      padding-bottom: 0 !important;
    }
    .el-dialog__footer {
      padding-top: 0 !important;
      padding-right: 50px;
      padding-left: 50px;
      .footerButton {
        padding-left: 100px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .custom-dialog-btn {
          margin: 0;
          width: 270px;
        }
      }
    }
  }
  .form {
    padding: 0 50px;
    .el-form-item__label {
      font-weight: normal;
      line-height: 46px;
    }
    .el-form-item__content {
      line-height: 46px;
      .el-input__inner {
        height: 46px;
        line-height: 46px;
      }
      .el-button {
        line-height: 22px;
        background-color: #cccccc;
        color: #ffffff;
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
        &.primary {
          background-color: #2e73f3;
        }
      }
    }
  }
  .custom-dialog-btn {
    background-color: transparent;
  }
  .noPhone {
    width: 100%;
    height: 138px;
    background: url('~@/assets/images/noPhone.png') no-repeat center;
    background-size: 100% 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    &Icon {
      width: 42px;
      height: 42px;
    }
    &Text {
      font-size: 14px;
      line-height: 20px;
      font-weight: 500;
      color: $font;
      margin-top: 15px;
    }
  }
}
</style>
