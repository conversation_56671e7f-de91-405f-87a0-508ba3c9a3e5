<template>
  <div class="container" @contextmenu.prevent>
    <header-tpl :is-login="isLogin" :showSearch="false" />
    <div class="tender-detail-tip">
      <i class="el-icon-info"></i>
      <span>鼠标选中正文内容的完整公司名称即可查询公司信息</span>
    </div>
    <div class="tender-container" style="min-width: 1200px">
      <!-- 面包屑 -->
      <div class="tender-detail-breadcrumb">
        <span class="breadcrumb-item">招投标</span>
        <span class="breadcrumb-item">查看详情</span>
      </div>
      <!-- 选中的关键词 -->
      <div class="tender-detail-keyword">
        <span class="tip">关键词：</span>
        <span class="keyword-item" v-for="keyword in keywords" :key="keyword" @click="handleTenderSearch(keyword)">{{ keyword }}</span>
      </div>
      <!-- 标题 -->
      <div class="tender-card tender-padding">
        <div class="tender-detail-title">{{ tenderInfo.title }}</div>
        <div class="tender-detail-tag">
          <span class="tag-item">{{ tenderInfo.area }}{{ tenderInfo.area && tenderInfo.city ? '-' : '' }}{{ tenderInfo.city }}{{ tenderInfo.city && tenderInfo.district ? '-' : '' }}{{ tenderInfo.district }}</span>
          <span class="tag-item" v-if="tenderInfo.topType">{{ tenderInfo.topType }}</span>
          <span class="tag-item" v-if="tenderInfo.buyerClass">{{ tenderInfo.buyerClass }}</span>
        </div>
        <div class="tender-detail-time-collect">
          <span class="time">日期：{{ tenderInfo.publishTime }}</span>
          <div style="display: inline-flex; align-items: center">
            <div class="collect" :class="{ 'is-collected': tenderInfo.isStore }" @click="handleCollect">
              <i :class="tenderInfo.isStore ? 'el-icon-star-on' : 'el-icon-star-off'"></i>
              <span>{{ tenderInfo.isStore ? '已收藏' : '收藏' }}</span>
            </div>
            <div class="collect" style="margin-left: 15px" @click="handleShare">
              <i class="el-icon-share"></i>
              <span>分享</span>
            </div>
          </div>
        </div>
      </div>
      <!-- 内容 -->
      <div class="tender-card">
        <div class="tender-detail-tabs">
          <span class="tabs-item">公告摘要</span>
          <span class="tabs-item" @click="scrollToContent">公告正文</span>
        </div>
        <div class="tender-padding" id="pdfDom">
          <div class="tender-detail-htitle">公告摘要</div>
          <el-descriptions :column="2" border class="tender-detail-description">
            <el-descriptions-item label="采购单位" v-if="tenderInfo.buyer">
              <span class="link" @click="handleSearch(tenderInfo.buyer)">{{ tenderInfo.buyer }} &gt;</span>
            </el-descriptions-item>
            <el-descriptions-item label="采购联系人/电话" v-if="tenderInfo.buyerPerson || tenderInfo.buyerTel">
              <div class="flex">
                <span>{{ tenderInfo.buyerPerson }} {{ tenderInfo.buyerPerson && tenderInfo.buyerTel ? '/' : '' }} {{ tenderInfo.buyerTel }}</span>
                <!-- <span class="link">更多联系人 &gt;</span> -->
              </div>
            </el-descriptions-item>
            <el-descriptions-item label="代理机构" v-if="tenderInfo.agency">
              <span>{{ tenderInfo.agency }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="代理机构联系人/电话" v-if="tenderInfo.agencyPerson || tenderInfo.agencyTel">
              <div class="flex">
                <span>{{ tenderInfo.agencyPerson }} {{ tenderInfo.agencyPerson && tenderInfo.agencyTel ? '/' : '' }} {{ tenderInfo.agencyTel }}</span>
              </div>
            </el-descriptions-item>
            <el-descriptions-item label="报名截止日期" v-if="tenderInfo.signEndTime">
              <span>{{ parseTime(tenderInfo.signEndTime, '{y}-{m}-{d}') }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="投标截止日期" v-if="tenderInfo.signEndTime">
              <span>{{ parseTime(tenderInfo.signEndTime, '{y}-{m}-{d}') }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="中标单位" v-if="tenderInfo.winnerInfo && tenderInfo.winnerInfo.length > 0">
              <div class="link" v-for="(item, index) in JSON.parse(tenderInfo.winnerInfo)" :key="index">
                <span class="link-symbol">{{ index > 0 ? '，' : '' }}</span>
                <span @click="handleSearch(item.winner)">{{ item.winner }} &gt;</span>
              </div>
            </el-descriptions-item>
            <el-descriptions-item label="中标金额(元)" v-if="tenderInfo.bidAmount">
              <span>{{ tenderInfo.bidAmount }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="预算金额(元)" v-if="tenderInfo.budget">
              <span>{{ tenderInfo.budget }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="项目地区" v-if="tenderInfo.area || tenderInfo.city || tenderInfo.district">
              <span>{{ tenderInfo.area }}{{ tenderInfo.area && tenderInfo.city ? '-' : '' }}{{ tenderInfo.city }}{{ tenderInfo.city && tenderInfo.district ? '-' : '' }}{{ tenderInfo.district }}</span>
            </el-descriptions-item>
          </el-descriptions>
          <div class="tender-detail-htitle padding" id="content">公告正文</div>
          <div class="tender-detail-content padding" v-if="!!tenderInfo.detail" v-html="tenderInfo.detail" @mouseup="handleMouseUp"></div>
          <div class="tender-detail-content padding" v-else>暂无内容</div>
        </div>
      </div>
      <!-- 上一条、下一条、返回列表 -->
      <div class="tender-detail-pagination" v-if="source !== 'chat'">
        <div class="pagination-box">
          <div class="pagination-item" @click="handleTenderDetail(prevTender)" v-if="prevTender.jyId">上一条：{{ prevTender.title }}</div>
          <div class="pagination-item" @click="handleTenderDetail(nextTender)" v-if="nextTender.jyId">下一条：{{ nextTender.title }}</div>
        </div>
        <div style="display: inline-flex; flex-shrink: 0">
          <div class="pagination-back" style="margin-right: 20px" @click="getPdf(tenderInfo.title)">下载</div>
          <div class="pagination-back" @click="handleBack">返回列表</div>
        </div>
      </div>
      <!-- 评论 -->
      <div class="tender-detail-comment" v-if="isLogin">
        <div class="comment-title">
          评论 {{ (commentList && commentList.length) || '' }}
          <span class="comment-title-tip">请先选择评论对象后再输入评论内容，评论对象选择请在输入框上方标题处勾选对象即可</span>
        </div>
        <div class="comment-con">
          <div class="comment-con-answer" v-if="!parentId">
            <img :src="avatar" class="comment-con-answer-img" alt="" />
            <div class="comment-con-answer-input">
              <!-- 标签复选框 - 只在发表新评论时显示 -->
              <div class="comment-checkboxes">
                <el-checkbox-group v-model="selectedTabs" :min="1">
                  <el-checkbox label="nr">当前招标项目/内容</el-checkbox>
                  <el-checkbox label="pt">发布平台</el-checkbox>
                  <el-checkbox label="zab">招标单位</el-checkbox>
                  <el-checkbox label="zob">中标单位</el-checkbox>
                </el-checkbox-group>
              </div>
              <el-input @blur="changeCaretPosition" type="textarea" placeholder="请输入评论内容" v-model="comment" resize="none" maxlength="255" show-word-limit></el-input>
              <div class="comment-con-answer-button">
                <div class="left-section">
                  <emoji @handleClickEmoji="handleClickEmoji" @callBack="showQuickReply = false" />
                  <!-- 自定义快捷回复 -->
                  <div class="quick-reply-widget" ref="quickReplyWidget">
                    <!-- 快捷回复触发按钮 -->
                    <el-tooltip content="自定义快捷回复" effect="dark">
                      <i class="ssfont ss-diy-biaoqian quick-reply-trigger" @click="toggleQuickReply" :style="{ color: showQuickReply ? '#2E73F3' : '#9fa3b2' }"></i>
                    </el-tooltip>
                    <!-- 快捷回复面板 -->
                    <div class="quick-reply-panel" v-if="showQuickReply">
                      <!-- 头部操作栏 -->
                      <div class="panel-header">
                        <div class="header-left">
                          <span class="panel-title">自定义快捷回复</span>
                          <div class="header-actions" v-if="quickReplies.length > 0">
                            <div class="edit-btn" @click="toggleEditMode" :class="{ active: isEditMode }">
                              <i class="el-icon-edit"></i>
                              <span>编辑</span>
                            </div>
                          </div>
                        </div>
                        <div class="header-right">
                          <div class="add-btn" @click="toggleAddMode">
                            <i class="el-icon-plus"></i>
                            <span>添加快捷回复内容</span>
                          </div>
                        </div>
                      </div>
                      <!-- 快捷回复标签列表 -->
                      <div class="reply-tags" v-if="quickReplies.length > 0">
                        <div class="reply-tag" v-for="(reply, index) in quickReplies" :key="index" @click="insertQuickReply(reply)">
                          <span class="tag-text">{{ reply }}</span>
                          <i class="el-icon-error delete-icon" v-if="isEditMode" @click.stop.prevent="deleteQuickReply(reply)"></i>
                        </div>
                      </div>
                      <!-- 添加新回复表单 -->
                      <div class="add-reply-form" v-if="showAddForm">
                        <el-input v-model="newReplyContent" type="textarea" placeholder="请输入快捷回复内容" :rows="4" maxlength="200" show-word-limit></el-input>
                        <div class="form-actions">
                          <el-button type="primary" size="small" @click="saveQuickReply" :disabled="!newReplyContent.trim()">添加至快捷回复</el-button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="button-group">
                  <span class="button" :class="{ disabled: !comment.trim() || isSubmitting }" @click="addComment">{{ isSubmitting ? '发表中...' : '发表' }}</span>
                </div>
              </div>
            </div>
          </div>
          <div class="comment-con-item" v-for="item in commentList" :key="item.id">
            <img :src="imgPath + item.avatar" class="comment-con-item-img" alt="" />
            <div class="comment-con-w100">
              <div class="comment-con-item-con">
                <div class="comment-con-item-con-title">
                  {{ hideMiddleStr(item.nickName) }}
                  <span v-if="item.deptName" class="dept-name">{{ item.deptName }}</span>
                  <span v-if="item.objective && item.objective !== 'answer'" class="objective-name">{{ formatObjective(item.objective) }}</span>
                </div>
                <div class="comment-con-item-con-con" v-html="formatContent(item.name)"></div>
                <div class="comment-con-item-con-time">
                  <span>{{ formatTime(new Date(item.createTime).getTime(), '') }}</span>
                  <div class="inline-flex">
                    <div class="anwser" @click="handleToggleLike(item)">
                      <i class="ssfont" :class="getLikeStatus(item).isLiked ? 'ss-diy-yidianzan' : 'ss-diy-dianzan'" :style="{ color: getLikeStatus(item).isLiked ? '#2e73f3' : '#666666' }"></i>
                      <span :style="{ color: getLikeStatus(item).isLiked ? '#2e73f3' : '#666666' }">{{ getLikeStatus(item).count || '赞' }}</span>
                    </div>
                    <div class="anwser" @click="handleDeleteComment(item)" v-if="userId === item.userId || checkPermi(['system:tender:delete'])">
                      <i class="el-icon-delete"></i>
                      <span>删除</span>
                    </div>
                    <div class="anwser" @click="setParentid(item)">
                      <i class="el-icon-chat-line-square"></i>
                      <span>回复 {{ item.hasOwnProperty('children') && !!item.children ? `(${item.children.length})` : '' }}</span>
                    </div>
                  </div>
                </div>
                <div class="comment-con-item-con-open" @click="handleShowMore(item)" v-if="item.hasOwnProperty('children') && !!item.children && item.children.length > 0">——{{ showId === item.id ? '收起所有评论' : '展开所有评论' }}</div>
              </div>
              <div class="comment-con-answer" v-if="parentId && parentId === item.id">
                <img :src="avatar" class="comment-con-answer-img" alt="" />
                <div class="comment-con-answer-input reply-input">
                  <el-input @blur="changeCaretPosition" type="textarea" placeholder="请输入回复内容" v-model="comment" resize="none" maxlength="255" show-word-limit></el-input>
                  <div class="comment-con-answer-button">
                    <div class="left-section">
                      <emoji @handleClickEmoji="handleClickEmoji" @callBack="showQuickReply = false" />
                      <!-- 自定义快捷回复 -->
                      <div class="quick-reply-widget">
                        <!-- 快捷回复触发按钮 -->
                        <el-tooltip content="自定义快捷回复" effect="dark">
                          <i class="ssfont ss-diy-biaoqian quick-reply-trigger" @click="toggleQuickReply" :style="{ color: showQuickReply ? '#2E73F3' : '#9fa3b2' }"></i>
                        </el-tooltip>
                        <!-- 快捷回复面板 -->
                        <div class="quick-reply-panel" v-if="showQuickReply">
                          <!-- 头部操作栏 -->
                          <div class="panel-header">
                            <div class="header-left">
                              <span class="panel-title">自定义快捷回复</span>
                              <div class="header-actions" v-if="quickReplies.length > 0">
                                <div class="edit-btn" @click="toggleEditMode" :class="{ active: isEditMode }">
                                  <i class="el-icon-edit"></i>
                                  <span>编辑</span>
                                </div>
                              </div>
                            </div>
                            <div class="header-right">
                              <div class="add-btn" @click="toggleAddMode">
                                <i class="el-icon-plus"></i>
                                <span>添加快捷回复内容</span>
                              </div>
                            </div>
                          </div>
                          <!-- 快捷回复标签列表 -->
                          <div class="reply-tags" v-if="quickReplies.length > 0">
                            <div class="reply-tag" v-for="(reply, index) in quickReplies" :key="index" @click="insertQuickReply(reply)">
                              <span class="tag-text">{{ reply }}</span>
                              <i class="el-icon-error delete-icon" v-if="isEditMode" @click.stop.prevent="deleteQuickReply(reply)"></i>
                            </div>
                          </div>
                          <!-- 添加新回复 -->
                          <div class="add-reply-form" v-if="showAddForm">
                            <el-input v-model="newReplyContent" type="textarea" placeholder="请输入快捷回复内容" :rows="3" maxlength="200" show-word-limit></el-input>
                            <div class="form-actions">
                              <el-button type="primary" size="small" @click="saveQuickReply" :disabled="!newReplyContent.trim()">添加至快捷回复</el-button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="button-group">
                      <span class="button cancel-button" @click="cancelReply">取消</span>
                      <span class="button" :class="{ disabled: !comment.trim() || isSubmitting }" @click="addComment">{{ isSubmitting ? '发表中...' : '发表' }}</span>
                    </div>
                  </div>
                </div>
              </div>
              <el-collapse-transition>
                <div v-if="showId === item.id">
                  <div class="comment-con-item children" v-if="item.hasOwnProperty('children') && !!item.children" v-for="ite in item.children" :key="ite.id">
                    <img :src="imgPath + ite.avatar" class="comment-con-item-img" alt="" />
                    <div class="comment-con-w100">
                      <div class="comment-con-item-con">
                        <div class="comment-con-item-con-title">
                          {{ hideMiddleStr(ite.nickName) }}
                          <span v-if="ite.deptName" class="dept-name">{{ ite.deptName }}</span>
                          <span v-if="ite.objective && ite.objective !== 'answer'" class="objective-name">{{ formatObjective(ite.objective) }}</span>
                        </div>
                        <div class="comment-con-item-con-con" v-html="formatContent(ite.name)"></div>
                        <div class="comment-con-item-con-time">
                          <span>{{ formatTime(new Date(ite.createTime).getTime(), '') }}</span>
                          <div class="inline-flex">
                            <div class="anwser" @click="handleToggleLike(ite)">
                              <i class="ssfont" :class="getLikeStatus(ite).isLiked ? 'ss-diy-yidianzan' : 'ss-diy-dianzan'" :style="{ color: getLikeStatus(ite).isLiked ? '#2e73f3' : '#666666' }"></i>
                              <span :style="{ color: getLikeStatus(ite).isLiked ? '#2e73f3' : '#666666' }">{{ getLikeStatus(ite).count || '赞' }}</span>
                            </div>
                            <div class="anwser" @click="handleDeleteComment(ite)" v-if="userId === ite.userId || checkPermi(['system:tender:delete'])">
                              <i class="el-icon-delete"></i>
                              <span>删除</span>
                            </div>
                            <div class="anwser" @click="setParentid(ite)">
                              <i class="el-icon-chat-line-square"></i>
                              <span>回复 {{ ite.hasOwnProperty('children') && !!ite.children ? `(${ite.children.length})` : '' }}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="comment-con-answer" v-if="parentId && parentId === ite.id">
                        <img :src="avatar" class="comment-con-answer-img" alt="" />
                        <div class="comment-con-answer-input reply-input">
                          <el-input @blur="changeCaretPosition" type="textarea" placeholder="请输入回复内容" v-model="comment" resize="none" maxlength="255" show-word-limit></el-input>
                          <div class="comment-con-answer-button">
                            <div class="left-section">
                              <emoji @handleClickEmoji="handleClickEmoji" @callBack="showQuickReply = false" />
                              <!-- 自定义快捷回复 -->
                              <div class="quick-reply-widget" ref="quickReplyWidget">
                                <!-- 快捷回复触发按钮 -->
                                <el-tooltip content="自定义快捷回复" effect="dark">
                                  <i class="ssfont ss-diy-biaoqian quick-reply-trigger" @click="toggleQuickReply" :style="{ color: showQuickReply ? '#2E73F3' : '#9fa3b2' }"></i>
                                </el-tooltip>
                                <!-- 快捷回复面板 -->
                                <div class="quick-reply-panel" v-if="showQuickReply">
                                  <!-- 头部操作栏 -->
                                  <div class="panel-header">
                                    <div class="header-left">
                                      <span class="panel-title">自定义快捷回复</span>
                                      <div class="header-actions" v-if="quickReplies.length > 0">
                                        <div class="edit-btn" @click="toggleEditMode" :class="{ active: isEditMode }">
                                          <i class="el-icon-edit"></i>
                                          <span>编辑</span>
                                        </div>
                                      </div>
                                    </div>
                                    <div class="header-right">
                                      <div class="add-btn" @click="toggleAddMode">
                                        <i class="el-icon-plus"></i>
                                        <span>添加快捷回复内容</span>
                                      </div>
                                    </div>
                                  </div>
                                  <!-- 快捷回复标签列表 -->
                                  <div class="reply-tags" v-if="quickReplies.length > 0">
                                    <div class="reply-tag" v-for="(reply, index) in quickReplies" :key="index" @click="insertQuickReply(reply)">
                                      <span class="tag-text">{{ reply }}</span>
                                      <i class="el-icon-error delete-icon" v-if="isEditMode" @click.stop.prevent="deleteQuickReply(reply)"></i>
                                    </div>
                                  </div>
                                  <!-- 添加新回复表单 -->
                                  <div class="add-reply-form" v-if="showAddForm">
                                    <el-input v-model="newReplyContent" type="textarea" placeholder="请输入快捷回复内容" :rows="4" maxlength="200" show-word-limit></el-input>
                                    <div class="form-actions">
                                      <el-button type="primary" size="small" @click="saveQuickReply" :disabled="!newReplyContent.trim()">添加至快捷回复</el-button>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                            <div class="button-group">
                              <span class="button cancel-button" @click="cancelReply">取消</span>
                              <span class="button" :class="{ disabled: !comment.trim() || isSubmitting }" @click="addComment">{{ isSubmitting ? '发表中...' : '发表' }}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="comment-con-item children" v-if="ite.hasOwnProperty('children') && !!ite.children" v-for="itt in ite.children" :key="itt.id">
                        <img :src="imgPath + itt.avatar" class="comment-con-item-img" alt="" />
                        <div class="comment-con-w100">
                          <div class="comment-con-item-con">
                            <div class="comment-con-item-con-title">
                              {{ hideMiddleStr(itt.nickName) }}
                              <span v-if="itt.deptName" class="dept-name">{{ itt.deptName }}</span>
                              <span v-if="itt.objective && itt.objective !== 'answer'" class="objective-name">{{ formatObjective(itt.objective) }}</span>
                            </div>
                            <div class="comment-con-item-con-con" v-html="formatContent(itt.name)"></div>
                            <div class="comment-con-item-con-time">
                              <span>{{ formatTime(new Date(itt.createTime).getTime(), '') }}</span>
                              <div class="inline-flex">
                                <div class="anwser" @click="handleToggleLike(itt)">
                                  <i class="ssfont" :class="getLikeStatus(itt).isLiked ? 'ss-diy-yidianzan' : 'ss-diy-dianzan'" :style="{ color: getLikeStatus(itt).isLiked ? '#2e73f3' : '#666666' }"></i>
                                  <span :style="{ color: getLikeStatus(itt).isLiked ? '#2e73f3' : '#666666' }">{{ getLikeStatus(itt).count || '赞' }}</span>
                                </div>
                                <div class="anwser" @click="handleDeleteComment(itt)" v-if="userId === itt.userId || checkPermi(['system:tender:delete'])">
                                  <i class="el-icon-delete"></i>
                                  <span>删除</span>
                                </div>
                                <div class="anwser" @click="setParentid(itt)">
                                  <i class="el-icon-chat-line-square"></i>
                                  <span>回复 {{ itt.hasOwnProperty('children') && !!itt.children ? `(${itt.children.length})` : '' }}</span>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div class="comment-con-answer" v-if="parentId && parentId === itt.id">
                            <img :src="avatar" class="comment-con-answer-img" alt="" />
                            <div class="comment-con-answer-input reply-input">
                              <el-input @blur="changeCaretPosition" type="textarea" placeholder="请输入回复内容" v-model="comment" resize="none" maxlength="255" show-word-limit></el-input>
                              <div class="comment-con-answer-button">
                                <div class="left-section">
                                  <emoji @handleClickEmoji="handleClickEmoji" @callBack="showQuickReply = false" />
                                  <!-- 自定义快捷回复 -->
                                  <div class="quick-reply-widget" ref="quickReplyWidget">
                                    <!-- 快捷回复触发按钮 -->
                                    <el-tooltip content="自定义快捷回复" effect="dark">
                                      <i class="ssfont ss-diy-biaoqian quick-reply-trigger" @click="toggleQuickReply" :style="{ color: showQuickReply ? '#2E73F3' : '#9fa3b2' }"></i>
                                    </el-tooltip>
                                    <!-- 快捷回复面板 -->
                                    <div class="quick-reply-panel" v-if="showQuickReply">
                                      <!-- 头部操作栏 -->
                                      <div class="panel-header">
                                        <div class="header-left">
                                          <span class="panel-title">自定义快捷回复</span>
                                          <div class="header-actions" v-if="quickReplies.length > 0">
                                            <div class="edit-btn" @click="toggleEditMode" :class="{ active: isEditMode }">
                                              <i class="el-icon-edit"></i>
                                              <span>编辑</span>
                                            </div>
                                          </div>
                                        </div>
                                        <div class="header-right">
                                          <div class="add-btn" @click="toggleAddMode">
                                            <i class="el-icon-plus"></i>
                                            <span>添加快捷回复内容</span>
                                          </div>
                                        </div>
                                      </div>
                                      <!-- 快捷回复标签列表 -->
                                      <div class="reply-tags" v-if="quickReplies.length > 0">
                                        <div class="reply-tag" v-for="(reply, index) in quickReplies" :key="index" @click="insertQuickReply(reply)">
                                          <span class="tag-text">{{ reply }}</span>
                                          <i class="el-icon-error delete-icon" v-if="isEditMode" @click.stop.prevent="deleteQuickReply(reply)"></i>
                                        </div>
                                      </div>
                                      <!-- 添加新回复表单 -->
                                      <div class="add-reply-form" v-if="showAddForm">
                                        <el-input v-model="newReplyContent" type="textarea" placeholder="请输入快捷回复内容" :rows="4" maxlength="200" show-word-limit></el-input>
                                        <div class="form-actions">
                                          <el-button type="primary" size="small" @click="saveQuickReply" :disabled="!newReplyContent.trim()">添加至快捷回复</el-button>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div class="button-group">
                                  <span class="button cancel-button" @click="cancelReply">取消</span>
                                  <span class="button" :class="{ disabled: !comment.trim() || isSubmitting }" @click="addComment">{{ isSubmitting ? '发表中...' : '发表' }}</span>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div class="comment-con-item children" v-if="itt.hasOwnProperty('children') && !!itt.children" v-for="it in itt.children" :key="it.id">
                            <img :src="imgPath + it.avatar" class="comment-con-item-img" alt="" />
                            <div class="comment-con-w100">
                              <div class="comment-con-item-con">
                                <div class="comment-con-item-con-title">
                                  {{ hideMiddleStr(it.nickName) }}
                                  <span v-if="it.deptName" class="dept-name">{{ it.deptName }}</span>
                                  <span v-if="it.objective && it.objective !== 'answer'" class="objective-name">{{ formatObjective(it.objective) }}</span>
                                </div>
                                <div class="comment-con-item-con-con" v-html="formatContent(it.name)"></div>
                                <div class="comment-con-item-con-time">
                                  <span>{{ formatTime(new Date(it.createTime).getTime(), '') }}</span>
                                  <div class="inline-flex">
                                    <div class="anwser" @click="handleToggleLike(it)">
                                      <i class="ssfont" :class="getLikeStatus(it).isLiked ? 'ss-diy-yidianzan' : 'ss-diy-dianzan'" :style="{ color: getLikeStatus(it).isLiked ? '#2e73f3' : '#666666' }"></i>
                                      <span :style="{ color: getLikeStatus(it).isLiked ? '#2e73f3' : '#666666' }">{{ getLikeStatus(it).count || '赞' }}</span>
                                    </div>
                                    <div class="anwser" @click="handleDeleteComment(it)" v-if="userId === it.userId || checkPermi(['system:tender:delete'])">
                                      <i class="el-icon-delete"></i>
                                      <span>删除</span>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </el-collapse-transition>
            </div>
          </div>
        </div>
      </div>
    </div>
    <footer-tpl />
    <!--企业信息-->
    <company ref="company" />
  </div>
</template>
<script>
import HeaderTpl from '@/views/public/components/head'
import FooterTpl from '@/views/public/components/foot'
import { getToken } from '@/utils/auth'
import { getTenderListV2, getTenderDetailV2, getRemoteCompanyInfo, getTenderComment, addTenderComment, deleteTenderComment, getTenderLabel, addTenderLabel, deleteTenderLabel, addTenderLike } from '@/api/tender'
import { getlist, shoucTo, delshouc, getlistb } from '@/api/houtai/shoucang'
import popShare from '@/components/popShare'
import Company from '@/views/payment/company'
import zlib from 'zlib'
import { mapGetters } from 'vuex'
import { formatTime, hideMiddleStr, formatContent } from '@/utils'
import emoji from '@/components/emoji/index.vue'
import { checkPermi } from '@/utils/permission'

export default {
  name: 'TenderNewDetail',
  components: { HeaderTpl, FooterTpl, Company, emoji },
  data() {
    return {
      source: '',
      tenderId: undefined,
      tenderInfo: {},
      isLogin: false,
      keywords: [],
      collectList: [],
      params: {},
      parsedParams: {},
      prevTender: {},
      nextTender: {},
      commentList: [], // 评论列表
      showComment: false, // 是否显示评论
      comment: '', // 评论内容
      parentId: 0, // 父级id
      showId: 0, // 显示id
      caretPosition: 0, // 光标位置
      selectedTabs: ['nr', 'pt', 'zab', 'zob'], // 选中的标签数组，默认选中当前招标项目/内容
      showQuickReply: false, // 是否显示快捷回复
      isEditMode: false, // 是否编辑模式
      showAddForm: false, // 是否显示添加表单
      quickReplies: [], // 快捷回复
      newReplyContent: '', // 新快捷回复内容
      isSubmitting: false, // 防止重复提交
      isLoadingComments: false, // 评论加载状态
      isDeletingQuickReply: false // 正在删除快捷回复标记
    }
  },
  computed: {
    ...mapGetters(['avatar']),
    userId() {
      return (this.$store.getters && this.$store.getters.info && this.$store.getters.info.userId) || -1
    },
    companyId() {
      return (this.$store.getters && this.$store.getters.info && this.$store.getters.info.companyId) || -1
    }
  },
  mounted() {
    this.isLogin = !!getToken()
    // 从缓存加载用户的标签选择
    this.loadTabsFromCache()
    // 添加点击事件监听器，用于点击空白区域隐藏快捷回复
    document.addEventListener('click', this.handleDocumentClick)
  },
  beforeDestroy() {
    // 移除事件监听器
    document.removeEventListener('click', this.handleDocumentClick)
  },
  created() {
    const { bidId, source, params } = this.$route.query
    if (!bidId || isNaN(bidId)) {
      this.$alert('参数错误，请联系管理员或稍后再试', '系统提示', {
        type: 'error',
        confirmButtonText: '确定',
        callback: action => {
          this.$router.push('/')
        }
      })
      return
    }
    this.tenderId = bidId
    this.source = source
    this.params = params
    if (!!getToken()) this.getCollectList()
    this.$nextTick(() => {
      this.getTenderDetail()
    })
  },
  methods: {
    // 加密
    encrypt(str) {
      return zlib.deflateSync(str).toString('base64')
    },
    // 解密
    decrypt(str) {
      try {
        return zlib.inflateSync(new Buffer(str, 'base64')).toString()
      } catch (e) {
        return str
      }
    },
    // 获取详情
    // prettier-ignore
    getTenderDetail() {
      if (this.source === 'chat' && !getToken()) {
        this.$alert('请先登录', '系统提示', {
          type: 'error',
          confirmButtonText: '确定',
          callback: action => {
            this.$router.push('/login')
          }
        })
        return
      }
      const loading = this.$loading({
        lock: true,
        text: '加载中',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      getTenderDetailV2({ id: this.tenderId }).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          this.tenderInfo = data
          this.keywords = data.keywords ? [...new Set(data.keywords.split(','))] : []
          if (this.params) {
            const decryptParams = this.decrypt(this.params).replaceAll('%2B', '+')
            const decodedParams = decodeURIComponent(decryptParams)
            this.parsedParams = JSON.parse(decodedParams)
            this.$nextTick(() => {
              this.getPrevAndNextTender()
            })
          }
          // 获取评论列表
          if (this.isLogin) {
            this.getCommentList()
            this.getQuickReply()
          }
        } else {
          this.$alert('请求失败，请联系管理员或稍后再试', '系统提示', {
            type: 'error',
            confirmButtonText: '确定',
            callback: action => {
              this.$router.push('/')
            }
          })
        }
      }).finally(() => {
        loading.close()
      })
    },
    // 请求上一条、下一条数据
    getPrevAndNextTender() {
      const { tabType } = this.parsedParams
      let parsedParams = { ...this.parsedParams }
      switch (tabType) {
        case 'all':
          // 删除tabType参数
          delete parsedParams.tabType
          // 请求列表获取上一条下一条
          getTenderListV2(parsedParams).then(res => {
            const { code, rows, total } = res
            if (code === 200 && rows) {
              const currentIndex = rows.findIndex(item => item.jyId == this.tenderId)
              if (currentIndex > 0) {
                this.prevTender = rows[currentIndex - 1]
              } else if (currentIndex === 0 && parsedParams.pageNum && parsedParams.pageNum > 1) {
                // 当前是第一条，且不是第一页时，请求上一页的最后一条
                const prevPageParams = { ...parsedParams, pageNum: parsedParams.pageNum - 1 }
                getTenderListV2(prevPageParams).then(prevRes => {
                  const { code: prevCode, rows: prevRows } = prevRes
                  if (prevCode === 200 && prevRows && prevRows.length > 0) {
                    // 上一页的最后一条作为上一条
                    this.prevTender = prevRows[prevRows.length - 1]
                    // 更新prevTender中的pageNum，用于跳转时保持分页正确
                    this.prevTender.pageNum = prevPageParams.pageNum
                  }
                })
              }
              if (currentIndex < rows.length - 1 && currentIndex !== -1) {
                this.nextTender = rows[currentIndex + 1]
              } else if (currentIndex !== -1 && total > rows.length) {
                // 当前页没有下一条数据，且还有下一页数据时，请求下一页
                const nextPageParams = { ...parsedParams, pageNum: (parsedParams.pageNum || 1) + 1 }
                getTenderListV2(nextPageParams).then(nextRes => {
                  const { code: nextCode, rows: nextRows } = nextRes
                  if (nextCode === 200 && nextRows && nextRows.length > 0) {
                    // 下一页的第一条数据作为下一条
                    this.nextTender = nextRows[0]
                    // 更新nextTender中的pageNum，用于跳转时保持分页正确
                    this.nextTender.pageNum = nextPageParams.pageNum
                  }
                })
              }
            }
          })
          break
        case 'ranking':
          // 使用parsedParams的items获取上一条下一条
          if (this.parsedParams.items && Array.isArray(this.parsedParams.items)) {
            const items = this.parsedParams.items
            const currentIndex = items.findIndex(item => item.jyId == this.tenderId)
            if (currentIndex > 0) {
              this.prevTender = items[currentIndex - 1]
            }
            if (currentIndex < items.length - 1 && currentIndex !== -1) {
              this.nextTender = items[currentIndex + 1]
            }
          }
          break
        case 'collect':
          // 获取收藏列表
          delete parsedParams.tabType
          getlistb(parsedParams).then(res => {
            const { code, rows, total } = res
            if (code === 200 && rows) {
              const currentIndex = rows.findIndex(item => item.jyId == this.tenderId)
              if (currentIndex > 0) {
                this.prevTender = rows[currentIndex - 1]
              } else if (currentIndex === 0 && parsedParams.pageNum && parsedParams.pageNum > 1) {
                // 当前是第一条，且不是第一页时，请求上一页的最后一条
                const prevPageParams = { ...parsedParams, pageNum: parsedParams.pageNum - 1 }
                getlistb(prevPageParams).then(prevRes => {
                  const { code: prevCode, rows: prevRows } = prevRes
                  if (prevCode === 200 && prevRows && prevRows.length > 0) {
                    // 上一页的最后一条作为上一条
                    this.prevTender = prevRows[prevRows.length - 1]
                    // 更新prevTender中的pageNum，用于跳转时保持分页正确
                    this.prevTender.pageNum = prevPageParams.pageNum
                  }
                })
              }
              if (currentIndex < rows.length - 1 && currentIndex !== -1) {
                this.nextTender = rows[currentIndex + 1]
              } else if (currentIndex !== -1 && total > rows.length) {
                // 当前页没有下一条数据，且还有下一页数据时，请求下一页
                const nextPageParams = { ...parsedParams, pageNum: (parsedParams.pageNum || 1) + 1 }
                getlistb(nextPageParams).then(nextRes => {
                  const { code: nextCode, rows: nextRows } = nextRes
                  if (nextCode === 200 && nextRows && nextRows.length > 0) {
                    // 下一页的第一条数据作为下一条
                    this.nextTender = nextRows[0]
                    // 更新nextTender中的pageNum，用于跳转时保持分页正确
                    this.nextTender.pageNum = nextPageParams.pageNum
                  }
                })
              }
            }
          })
          break
      }
    },
    // 跳转详情
    handleTenderDetail(item) {
      // 如果有页码信息，更新parsedParams中的pageNum
      if (item.pageNum) {
        this.parsedParams.pageNum = item.pageNum
      }
      const paramsStr = JSON.stringify(this.parsedParams)
      const encodedParams = encodeURIComponent(paramsStr)
      const encryptedParams = this.encrypt(encodedParams)
      const finalParams = encryptedParams.replace(/\+/g, '%2B')
      // 修改为当前窗口打开，并更新历史记录
      this.$router.push(`/tender/detail?bidId=${item.jyId}&params=${finalParams}`)
      // 更新当前页面数据
      this.tenderId = item.jyId
      this.tenderInfo = {}
      this.prevTender = {}
      this.nextTender = {}
      this.getTenderDetail()
    },
    // 查询收藏夹列表
    getCollectList() {
      getlist({ type: 'biddingDataWithJy' }).then(res => {
        const { code, msg, data } = res
        if (code === 200) this.collectList = data
        else this.$message.error(msg)
      })
    },
    // 收藏
    handleCollect() {
      const { jyId, isStore } = this.tenderInfo
      if (!jyId) return
      if (!getToken()) {
        this.$alert('请先登录', '系统提示', {
          type: 'error',
          confirmButtonText: '确定',
          callback: action => {
            this.$router.push('/login')
          }
        })
        return
      }
      if (this.collectList.length > 0) {
        if (!isStore) {
          const storeId = this.collectList[0]['storeId']
          const data = { storeId, valueIdList: [jyId] }
          shoucTo(data).then(res => {
            const { code, msg } = res
            if (code === 200) this.tenderInfo.isStore = true
            else this.$message.error(msg)
          })
        } else {
          const storeId = this.collectList[0]['storeId']
          const data = { storeId, valueId: jyId }
          delshouc(data).then(res => {
            const { code, msg } = res
            if (code === 200) this.tenderInfo.isStore = false
            else this.$message.error(msg)
          })
        }
      }
    },
    // 滚动到内容
    scrollToContent() {
      const contentElement = document.getElementById('content')
      if (contentElement) {
        contentElement.scrollIntoView({ behavior: 'smooth' })
      }
    },
    // 返回列表
    handleBack() {
      this.$router.push('/tender')
    },
    // 鼠标选中正文内容的完整公司名称即可查询公司信息
    handleMouseUp(e) {
      const selection = window.getSelection()
      const text = selection.toString()
      if (text) {
        popShare.show({
          title: text,
          top: e.clientY,
          left: e.clientX,
          onItemClick: e => {
            if (e.value === 'search') this.handleSearch(text)
          }
        })
      }
    },
    // 查询公司信息
    // prettier-ignore
    handleSearch(text = undefined) {
      const options = {
        lock: true,
        text: '搜索中…',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      }
      let dataLoading = this.$loading(options)
      this.mapShow = false
      getRemoteCompanyInfo({ companyName: text }).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          this.$nextTick(() => {
            dataLoading.close()
          })
          if (data && data.hasOwnProperty('companyCreditCode') && data.companyCreditCode) {
            this.$refs.company.getInfo(data, 'private')
          } else {
            this.$nextTick(() => {
              dataLoading.close()
            })
            this.$alert(`未查询到"<span style='color:#ff0000'>${text}</span>"相关公司信息！<br />请检查公司名称是否正确，请输入公司全称！`, '系统提示', { dangerouslyUseHTMLString: true })
          }
        } else {
          this.$nextTick(() => {
            dataLoading.close()
          })
          this.$alert(`未查询到"<span style='color:#ff0000'>${text}</span>"相关公司信息！<br />请检查公司名称是否正确，请输入公司全称！`, '系统提示', { dangerouslyUseHTMLString: true })
        }
      })
    },
    // 关键词搜索
    handleTenderSearch(keyword) {
      this.$router.push({ name: 'Tender', params: { keyword } })
    },
    // 分享--复制当前地址栏地址
    handleShare() {
      // 获取当前URL
      const currentUrl = window.location.href
      // 移除params参数，只保留bidId参数
      const baseUrl = currentUrl.replace(/(&params=)(.*)$/, '')
      // 复制处理后的URL
      navigator.clipboard.writeText(baseUrl)
      this.$message.success('已复制链接，快去粘贴吧~')
    },
    // 工具函数
    formatTime,
    hideMiddleStr,
    checkPermi,
    formatContent,
    // 格式化目标
    formatObjective(objective) {
      if (!objective) return ''
      const objectives = objective.split(',')
      const labelMap = {
        pt: '发布平台',
        zab: '招标单位',
        zob: '中标单位',
        nr: '当前招标项目/内容'
      }
      const labels = objectives.map(obj => labelMap[obj.trim()] || obj.trim()).filter(Boolean)
      return labels.join('、')
    },
    // 查询快捷回复
    getQuickReply() {
      getTenderLabel().then(res => {
        const { code, data } = res
        if (code === 200) this.quickReplies = data
      })
    },
    // 获取评论列表
    // prettier-ignore
    getCommentList() {
      if (this.isLoadingComments) return
      this.isLoadingComments = true
      getTenderComment({ bidId: this.tenderId }).then(res => {
        const { code, data } = res
        if (code === 200) {
          this.showComment = true
          this.commentList = data
        } else this.showComment = false
      }).catch(error => {
        this.showComment = false
        if (this.commentList.length === 0) this.$message.error('获取评论失败，请稍后重试')
      }).finally(() => {
        this.isLoadingComments = false
      })
    },
    // 点击回复
    setParentid(item) {
      this.caretPosition = 0
      this.comment = ''
      this.parentId = item.id
    },
    // 取消回复
    cancelReply() {
      this.caretPosition = 0
      this.comment = ''
      this.parentId = 0
    },
    changeCaretPosition(e) {
      this.caretPosition = e.target.selectionStart
    },
    // 点击表情
    handleClickEmoji(emoji) {
      let index = this.caretPosition
      let str = this.comment
      this.comment = str.slice(0, index) + emoji + str.slice(index)
      this.caretPosition += emoji.length
    },
    // 新增评论
    addComment() {
      if (this.isSubmitting) {
        this.$message.warning('正在提交中，请稍候...')
        return
      }
      if (!this.comment.trim()) return
      const { parentId } = this
      // 如果是回复他人评论，不需要验证标签选择
      if (!parentId && this.selectedTabs.length === 0) {
        this.$message.warning('请至少选择一个标签')
        return
      }
      // 输入验证
      const trimmedComment = this.comment.trim()
      if (trimmedComment.length > 255) {
        this.$message.warning('评论内容不能超过255个字符')
        return
      }
      const sanitizedComment = trimmedComment.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      if (sanitizedComment !== trimmedComment) {
        this.$message.warning('评论内容包含不安全的内容，请重新输入')
        return
      }
      // 只有在发表新评论时才保存用户的标签选择到缓存
      if (!parentId) this.saveTabsToCache()
      const requestData = {
        bidId: this.tenderId,
        comment: sanitizedComment,
        objective: parentId ? 'answer' : this.selectedTabs.join(','),
        parentId: parentId
      }
      // 设置提交状态
      this.isSubmitting = true
      // prettier-ignore
      addTenderComment(requestData).then(res => {
        const { code, msg } = res
        if (code === 200) {
          this.$message.success('发表成功')
          this.comment = ''
          this.parentId = 0
          this.getCommentList()
        } else {
          this.$message.error(msg || '发表失败')
        }
      }).catch(error => {
        this.$message.error('发表失败，请稍后重试')
      }).finally(() => {
        // 重置提交状态
        this.isSubmitting = false
      })
    },
    // 展开所有评论
    handleShowMore(item) {
      this.showId = this.showId === item.id ? 0 : item.id
    },
    // 删除评论
    // prettier-ignore
    handleDeleteComment(item) {
      const commentId = item.id
      this.$confirm('确定要删除这条评论吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteTenderComment({ commentId }).then(res => {
          const { code, msg } = res
          if (code === 200) {
            this.$message.success('删除成功')
            this.getCommentList()
          } else {
            this.$message.error(msg || '删除失败')
          }
        }).catch(error => {
          this.$message.error('删除失败，请稍后重试')
        })
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },
    // 获取点赞状态
    getLikeStatus(comment) {
      if (!comment.likeData || !Array.isArray(comment.likeData)) {
        return { count: 0, isLiked: false }
      }
      const likeData = comment.likeData.find(item => item.action === 'like')
      return {
        count: likeData ? likeData.count : 0,
        isLiked: likeData ? likeData.withMe : false
      }
    },
    // 切换点赞状态
    handleToggleLike(comment) {
      const requestData = {
        commentId: comment.id,
        action: 'like'
      }
      // prettier-ignore
      addTenderLike(requestData).then(res => {
        const { code, msg } = res
        if (code === 200) {
          // 重新获取评论列表以更新点赞状态
          this.getCommentList()
        } else {
          this.$message.error(msg || '操作失败')
        }
      }).catch(error => {
        this.$message.error('点赞操作失败，请稍后重试')
      })
    },
    // 切换快捷回复显示状态
    toggleQuickReply() {
      this.showQuickReply = !this.showQuickReply
      // 重置其他状态
      if (this.showQuickReply) {
        this.isEditMode = false
        this.showAddForm = false
        this.newReplyContent = ''
      }
    },
    // 处理点击文档事件（用于点击空白区域隐藏快捷回复）
    handleDocumentClick(event) {
      // 检查点击的元素是否在快捷回复组件内
      const quickReplyWidgets = document.querySelectorAll('.quick-reply-widget')
      let isClickInside = false
      quickReplyWidgets.forEach(widget => {
        if (widget.contains(event.target)) {
          isClickInside = true
        }
      })
      // 检查是否是确认对话框或消息提示相关的元素
      const isDialogClick = event.target.closest('.el-message-box') || event.target.closest('.el-message') || event.target.closest('.el-dialog') || event.target.closest('.el-overlay')
      // 如果点击在快捷回复组件外面且不是对话框相关元素，且不在删除过程中，隐藏快捷回复
      if (!isClickInside && !isDialogClick && this.showQuickReply && !this.isDeletingQuickReply) {
        this.showQuickReply = false
        this.isEditMode = false
        this.showAddForm = false
        this.newReplyContent = ''
      }
    },
    // 切换编辑模式
    toggleEditMode() {
      this.isEditMode = !this.isEditMode
      // 如果进入编辑模式，关闭添加表单
      if (this.isEditMode) {
        this.showAddForm = false
        this.newReplyContent = ''
      }
    },
    // 切换添加模式
    toggleAddMode() {
      this.showAddForm = !this.showAddForm
      // 如果显示添加表单，退出编辑模式
      if (this.showAddForm) {
        this.isEditMode = false
      } else {
        // 如果关闭添加表单，清空内容
        this.newReplyContent = ''
      }
    },
    // 插入快捷回复
    insertQuickReply(title) {
      this.comment += title
    },
    // 保存快捷回复
    saveQuickReply() {
      if (!this.newReplyContent.trim()) return
      const reply = {
        comment: this.newReplyContent.trim()
      }
      addTenderLabel(reply)
        .then(res => {
          const { code, msg } = res
          if (code === 200) {
            // 添加成功后，将新回复内容推入数组（作为字符串）
            this.quickReplies.push(this.newReplyContent.trim())
            // 清空输入框并关闭添加表单，保持快捷回复面板打开
            this.newReplyContent = ''
            this.showAddForm = false
            this.$message.success('快捷回复保存成功')
          } else {
            this.$message.error(msg || '保存失败')
          }
        })
        .catch(error => {
          this.$message.error('保存失败，请稍后重试')
        })
    },
    // 删除快捷回复
    // prettier-ignore
    deleteQuickReply(reply) {
      this.isDeletingQuickReply = true
      this.$confirm('确定要删除这个快捷回复吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteTenderLabel({ comment: reply }).then(res => {
          const { code, msg } = res
          if (code === 200) {
            // 从数组中移除删除的快捷回复，保持快捷回复面板打开
            const index = this.quickReplies.indexOf(reply)
            if (index > -1) {
              this.quickReplies.splice(index, 1)
            }
            this.$message.success('删除成功')
          } else {
            this.$message.error(msg || '删除失败')
          }
        }).catch(error => {
          this.$message.error('删除失败，请稍后重试')
        }).finally(() => {
          // 重置删除标记
          this.isDeletingQuickReply = false
        })
      }).catch(() => {
        // 用户取消删除，重置标记
        this.isDeletingQuickReply = false
        this.$message.info('已取消删除')
      })
    },
    // 保存标签选择到缓存
    saveTabsToCache() {
      try {
        const cacheKey = `${this.userId}.tenderCommentTabs`
        const cacheData = {
          selectedTabs: this.selectedTabs,
          updateTime: new Date().toISOString()
        }
        localStorage.setItem(cacheKey, JSON.stringify(cacheData))
      } catch (error) {
        console.warn('保存标签选择到缓存失败:', error)
      }
    },
    // 从缓存加载标签选择
    loadTabsFromCache() {
      try {
        const cacheKey = `${this.userId}.tenderCommentTabs`
        const cacheData = localStorage.getItem(cacheKey)
        if (cacheData) {
          const parsedData = JSON.parse(cacheData)
          if (parsedData.selectedTabs && Array.isArray(parsedData.selectedTabs) && parsedData.selectedTabs.length > 0) {
            this.selectedTabs = parsedData.selectedTabs
          }
        }
      } catch (error) {
        console.warn('从缓存加载标签选择失败:', error)
        // 如果加载失败，保持默认值
        this.selectedTabs = ['pt']
      }
    }
  }
}
</script>
<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
@import '~@/assets/styles/custom-tender.scss';
</style>
