<template>
  <div>
    <el-dialog v-dialogDragBox title="中标明细" :visible.sync="open" width="1200px" :before-close="beforeClose">
      <div class="tender-item" v-for="item in tenderList" :key="item.id">
        <div class="tender-item-head">
          <div class="tender-item-title" @click="handleDetail(item)">{{ item.title }}</div>
          <div class="tender-item-time">{{ parseTime(item.publishTime, '{y}-{m}-{d}') }}</div>
        </div>
        <div class="tender-item-foot">
          <div class="tender-item-tag">
            <span class="tag-item">{{ item.area }}{{ item.area && item.city ? '-' : '' }}{{ item.city }}</span>
            <span class="tag-item" v-if="item.subType">{{ item.subType }}</span>
            <span class="tag-item" v-if="item.buyerClass">{{ item.buyerClass }}</span>
            <span class="tag-item" v-if="item.site">{{ item.site }}</span>
          </div>
          <div class="tender-item-collect" :class="{ 'is-collected': item.isStore }" @click="handleCollect(item)">
            <i :class="item.isStore ? 'el-icon-star-on' : 'el-icon-star-off'"></i>
            <span>{{ item.isStore ? '已收藏' : '收藏' }}</span>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { getToken } from '@/utils/auth'
import { shoucTo, delshouc } from '@/api/houtai/shoucang'

export default {
  name: 'TenderNewItem',
  data() {
    return {
      open: false,
      tenderList: [],
      collectList: []
    }
  },
  methods: {
    // 显示明细
    show(list = [], collectList = []) {
      this.tenderList = list
      this.collectList = collectList
      this.open = true
    },
    // 关闭弹窗
    beforeClose() {
      this.open = false
      this.$emit('callback')
    },
    // 收藏/取消收藏
    handleCollect(item) {
      if (getToken() && this.collectList.length > 0) {
        if (!item.isStore) {
          const storeId = this.collectList[0]['storeId']
          const data = { storeId, valueIdList: [item.jyId] }
          shoucTo(data).then(res => {
            const { code, msg } = res
            if (code === 200) {
              this.$set(item, 'isStore', true)
              this.$forceUpdate()
            } else this.$message.error(msg)
          })
        } else {
          const storeId = this.collectList[0]['storeId']
          const data = { storeId, valueId: item.jyId }
          delshouc(data).then(res => {
            const { code, msg } = res
            if (code === 200) {
              this.$set(item, 'isStore', false)
              this.$forceUpdate()
            } else this.$message.error(msg)
          })
        }
      } else {
        this.$alert('请先登录', '系统提示', {
          confirmButtonText: '登录',
          showCancelButton: false,
          showClose: false,
          showConfirmButton: true,
          showCancelButton: false,
          callback: () => {
            this.$router.push('/login')
          }
        })
      }
    },
    // 查看详情
    handleDetail(item) {
      const list = this.tenderList.map(item => {
        return {
          jyId: item.jyId,
          title: item.title
        }
      })
      this.$parent.handleDetail(item, list)
    }
  }
}
</script>
<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
@import '~@/assets/styles/custom-tender.scss';
</style>
