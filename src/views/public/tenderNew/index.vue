<template>
  <div class="container" @contextmenu.prevent>
    <header-tpl :is-login="isLogin" :showSearch="false" />
    <div class="tender-container">
      <div class="expirationTime" v-if="expirationShow && usertype === 'gys'">
        <div class="expirationTime-close" @click="handleExpiration">
          <i class="el-icon-error"></i>
        </div>
        <div class="expirationTime-title" v-if="expirationTime">
          <span>您的使用权限还有</span>
          <b>{{ expirationTime && expirationTime.toString()[0] }}</b>
          <b v-if="expirationTime && expirationTime.toString()[1]">{{ expirationTime.toString()[1] }}</b>
          <span>天到期</span>
        </div>
        <div class="expirationTime-title" v-if="!expirationTime">您的使用权限已到期</div>
        <div class="expirationTime-tip">请及时补充权限使用时间</div>
        <div class="expirationTime-desc">获得权限方式：单月平台(公域)成交一次即可获取一个月的使用权限</div>
      </div>
      <div class="tender-card">
        <!-- 全部招标、我收藏的切换 -->
        <div class="tender-tabs">
          <span class="tender-tabs-item" :class="{ active: activeName === 'all' }" @click="handleClick('all')">全部招投标</span>
          <span class="tender-tabs-item" :class="{ active: activeName === 'ranking' }" @click="handleClick('ranking')">排行榜</span>
          <span class="tender-tabs-item" :class="{ active: activeName === 'collect' }" @click="handleClick('collect')" v-if="isLogin">已收藏</span>
        </div>
        <!-- 搜索条件 -->
        <template v-if="activeName !== 'collect'">
          <!-- 全部招投标 -->
          <div class="tender-search-bar" v-if="activeName === 'all'">
            <div class="tender-search-inputs">
              <el-input v-model="queryParams.keyword" placeholder="请输入产品名称/项目名称等关键词，例如：医疗设备" class="tender-search-input" clearable @keyup.enter.native="handleSearch" @clear="handleClearKeyword" />
            </div>
            <el-select v-model="queryParams.term" placeholder="请选择" class="tender-search-select" @change="handleSearch">
              <el-option v-for="item in termOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
            <el-checkbox v-model="multiKeyword" class="multi-keyword-new" @change="handleMultiKeywordChange" :disabled="!queryParams.keyword">多个关键词</el-checkbox>
            <el-button type="primary" class="tender-search-btn" icon="el-icon-search" @click="handleSearch">搜索</el-button>
          </div>
          <!-- 排行榜 -->
          <div class="tender-search-bar" v-if="activeName === 'ranking'">
            <div class="tender-search-inputs">
              <el-input v-model="rankingParams.keyword" placeholder="请输入产品名称/项目名称等关键词，例如：医疗设备" class="tender-search-input" clearable @keyup.enter.native="handleSearch" @clear="handleClearKeyword" />
            </div>
            <el-select v-model="rankingParams.term" placeholder="请选择" class="tender-search-select" @change="handleSearch">
              <el-option v-for="item in termOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
            <el-checkbox v-model="multiKeyword" class="multi-keyword-new" @change="handleMultiKeywordChange" :disabled="!rankingParams.keyword">多个关键词</el-checkbox>
            <el-button type="primary" class="tender-search-btn" icon="el-icon-search" @click="handleSearch">搜索</el-button>
          </div>
          <!-- 添加多个关键词 -->
          <div class="tender-keyword-wrapper" v-show="multiKeyword">
            <div class="tender-keyword-input">
              <el-input v-model="keyword" placeholder="请输入关键词" class="input" size="small" @keyup.enter.native="handleAddKeyword">
                <template slot="suffix">
                  <el-button type="primary" size="mini" @click="handleAddKeyword">添加</el-button>
                </template>
              </el-input>
              <div class="tender-keyword-mode" v-if="activeName === 'all'">
                <span class="title">关键词筛选条件：</span>
                <el-radio-group v-model="queryParams.containsAll" @change="handleKeywordModeChange">
                  <el-radio v-for="item in keywordModeOptions" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
                </el-radio-group>
              </div>
              <div class="tender-keyword-mode" v-if="activeName === 'ranking'">
                <span class="title">关键词筛选条件：</span>
                <el-radio-group v-model="rankingParams.containsAll" @change="handleKeywordModeChange">
                  <el-radio v-for="item in keywordModeOptions" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
                </el-radio-group>
              </div>
            </div>
            <div class="tender-keyword-list" v-if="keywords.length > 0">
              <div class="tender-keyword-item" v-for="(item, index) in keywords" :key="index">
                <span class="text">{{ item }}</span>
                <i class="el-icon-close icon" @click="handleRemoveKeyword(index)"></i>
              </div>
            </div>
          </div>
          <!-- 常用关键词 -->
          <div class="tender-keyword-common" v-if="keywordList.length > 0">
            <div class="tender-keyword-common-title">常用关键词</div>
            <div class="tender-keyword-item" v-for="item in keywordList" :key="item.id">
              <span class="text" @click="handleSelectKeyword(item)">{{ item }}</span>
              <i class="el-icon-close icon" @click="handleRemoveCommonKeyword(item)"></i>
            </div>
          </div>
          <template v-if="showMoreFilter">
            <div class="tender-search-items">
              <span class="tender-form-title">搜索范围：</span>
              <el-checkbox-group v-model="selectTypes" @change="handleSelectTypeChange" :min="1" v-if="activeName !== 'ranking'">
                <el-checkbox v-for="item in selectTypeOptions" :key="item.value" :label="item.value">{{ item.label }}</el-checkbox>
              </el-checkbox-group>
              <el-checkbox-group v-model="selectTypes" @change="handleSelectTypeChange" :min="1" v-if="activeName === 'ranking'">
                <el-checkbox v-for="item in selectTypeOptions.filter(item => item.value !== 'winner' && item.value !== 'buyer')" :key="item.value" :label="item.value">{{ item.label }}</el-checkbox>
              </el-checkbox-group>
            </div>
            <!-- 信息类型 -->
            <div class="tender-search-items" v-if="activeName !== 'ranking'">
              <span class="tender-form-title">信息类型：</span>
              <div class="tender-info-type">
                <span class="tender-info-type-item" :class="{ active: !queryParams.subType && !queryParams.topType }" @click="handleAllTypesClick">全部</span>
                <!-- 招标预告 -->
                <div class="tender-info-type-item-wrapper">
                  <el-popover trigger="hover" :visible-arrow="false" popper-class="tender-info-popover" v-model="popoverVisible.forecast">
                    <div slot="reference" class="tender-info-type-item" :class="{ 'is-active': popoverVisible.forecast, active: checkAllForecast, 'active-text': isIndeterminateForecast }" @click="handleForecastItemClick">
                      <span>招标预告</span>
                      <span>{{ isIndeterminateForecast ? selectedForecastTypes.length + '个' : '' }}</span>
                      <i class="el-icon-caret-bottom"></i>
                    </div>
                    <div class="tender-info-check">
                      <el-checkbox :indeterminate="isIndeterminateForecast" v-model="checkAllForecast" @change="handleCheckAllForecast">全部</el-checkbox>
                      <el-checkbox-group v-model="selectedForecastTypes" @change="handleForecastTypeChange">
                        <el-checkbox v-for="item in forecastTypeOptions" :key="item" :label="item">{{ item }}</el-checkbox>
                      </el-checkbox-group>
                    </div>
                  </el-popover>
                </div>
                <!-- 招标公告 -->
                <div class="tender-info-type-item-wrapper">
                  <el-popover trigger="hover" :visible-arrow="false" popper-class="tender-info-popover" v-model="popoverVisible.notice">
                    <div slot="reference" class="tender-info-type-item" :class="{ 'is-active': popoverVisible.notice, active: checkAllNotice, 'active-text': isIndeterminateNotice }" @click="handleNoticeItemClick">
                      <span>招标公告</span>
                      <span>{{ isIndeterminateNotice ? selectedNoticeTypes.length + '个' : '' }}</span>
                      <i class="el-icon-caret-bottom"></i>
                    </div>
                    <div class="tender-info-check">
                      <el-checkbox :indeterminate="isIndeterminateNotice" v-model="checkAllNotice" @change="handleCheckAllNotice">全部</el-checkbox>
                      <el-checkbox-group v-model="selectedNoticeTypes" @change="handleNoticeTypeChange">
                        <el-checkbox v-for="item in noticeTypeOptions" :key="item" :label="item">{{ item }}</el-checkbox>
                      </el-checkbox-group>
                    </div>
                  </el-popover>
                </div>
                <!-- 招标结果 -->
                <div class="tender-info-type-item-wrapper">
                  <el-popover trigger="hover" :visible-arrow="false" popper-class="tender-info-popover" v-model="popoverVisible.result">
                    <div slot="reference" class="tender-info-type-item" :class="{ 'is-active': popoverVisible.result, active: checkAllResult, 'active-text': isIndeterminateResult }" @click="handleResultItemClick">
                      <span>招标结果</span>
                      <span>{{ isIndeterminateResult ? selectedResultTypes.length + '个' : '' }}</span>
                      <i class="el-icon-caret-bottom"></i>
                    </div>
                    <div class="tender-info-check">
                      <el-checkbox :indeterminate="isIndeterminateResult" v-model="checkAllResult" @change="handleCheckAllResult">全部</el-checkbox>
                      <el-checkbox-group v-model="selectedResultTypes" @change="handleResultTypeChange">
                        <el-checkbox v-for="item in resultTypeOptions" :key="item" :label="item">{{ item }}</el-checkbox>
                      </el-checkbox-group>
                    </div>
                  </el-popover>
                </div>
                <!-- 招标信用信息 -->
                <div class="tender-info-type-item-wrapper">
                  <el-popover trigger="hover" :visible-arrow="false" popper-class="tender-info-popover" v-model="popoverVisible.credit">
                    <div slot="reference" class="tender-info-type-item" :class="{ 'is-active': popoverVisible.credit, active: checkAllCredit, 'active-text': isIndeterminateCredit }" @click="handleCreditItemClick">
                      <span>招标信用信息</span>
                      <span>{{ isIndeterminateCredit ? selectedCreditTypes.length + '个' : '' }}</span>
                      <i class="el-icon-caret-bottom"></i>
                    </div>
                    <div class="tender-info-check">
                      <el-checkbox :indeterminate="isIndeterminateCredit" v-model="checkAllCredit" @change="handleCheckAllCredit">全部</el-checkbox>
                      <el-checkbox-group v-model="selectedCreditTypes" @change="handleCreditTypeChange">
                        <el-checkbox v-for="item in creditTypeOptions" :key="item" :label="item">{{ item }}</el-checkbox>
                      </el-checkbox-group>
                    </div>
                  </el-popover>
                </div>
              </div>
            </div>
            <!-- 更多筛选 -->
            <div class="tender-search-items">
              <span class="tender-form-title">更多筛选：</span>
              <!-- 地区 -->
              <div class="tender-info-type-item-wrapper none">
                <el-popover trigger="hover" :visible-arrow="false" popper-class="tender-info-popover none" placement="bottom-start" v-model="popoverVisible.region">
                  <div slot="reference" class="tender-info-type-item" :class="{ 'is-active': popoverVisible.region, 'active-text': selectedRegionCount }">
                    <span>地区</span>
                    <span v-if="selectedRegionCount > 0">{{ selectedRegionCount }}个</span>
                    <i class="el-icon-caret-bottom"></i>
                  </div>
                  <el-cascader-panel class="tender-info-cascader" v-model="region" :options="regionOptions" :props="regionProps" @change="handleRegionChange" />
                </el-popover>
              </div>
              <!-- 行业 -->
              <div class="tender-info-type-item-wrapper">
                <el-popover trigger="hover" :visible-arrow="false" popper-class="tender-info-popover none" placement="bottom-start" v-model="popoverVisible.industry">
                  <div slot="reference" class="tender-info-type-item" :class="{ 'is-active': popoverVisible.industry, 'active-text': selectedIndustryCount }">
                    <span>行业</span>
                    <span v-if="selectedIndustryCount > 0">{{ selectedIndustryCount }}个</span>
                    <i class="el-icon-caret-bottom"></i>
                  </div>
                  <el-cascader-panel class="tender-info-cascader" v-model="industry" :options="industryOptionsWithAll" :props="industryProps" @change="handleIndustryChange" />
                </el-popover>
              </div>
              <!-- 金额 -->
              <div class="tender-info-type-item-wrapper" v-if="activeName != 'ranking'">
                <el-popover ref="amountPopover" trigger="hover" :visible-arrow="false" popper-class="tender-info-popover none">
                  <div slot="reference" class="tender-info-type-item" :class="{ 'is-active': popoverVisible.amount, 'active-text': selectedAmount && selectedAmount !== '全部金额' && selectedAmount !== '金额' }">
                    <span>{{ selectedAmount }}</span>
                    <i class="el-icon-caret-bottom"></i>
                  </div>
                  <div class="tender-info-amount">
                    <div class="tender-info-amount-item" :class="{ active: selectedAmount == item.value && selectedAmount != '金额' }" v-for="item in amountOptions" :key="item.value" @click="handleAmountChange(item)">{{ item.label }}</div>
                    <!-- 自定义金额范围 -->
                    <div class="tender-info-amount-custom">
                      <div class="title">自定义 &gt;</div>
                      <div class="tender-info-amount-custom-wrapper">
                        <div class="tender-info-amount-custom-input">
                          <span>从</span>
                          <el-input v-model="customAmountMin" size="small" class="input">
                            <span slot="suffix">万</span>
                          </el-input>
                          <span>至</span>
                          <el-input v-model="customAmountMax" size="small" class="input">
                            <span slot="suffix">万</span>
                          </el-input>
                          <span class="confirm" @click="handleCustomAmountConfirm">确定</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </el-popover>
              </div>
            </div>
            <!-- 排行根据 -->
            <div class="tender-search-items" v-if="activeName === 'ranking'">
              <span class="tender-form-title">排行根据：</span>
              <span class="tender-info-type-item" :class="{ active: rankingParams.order === 1 }" @click="handleOrderChange(1)">中标次数</span>
              <span class="tender-info-type-item" :class="{ active: rankingParams.order === 2 }" @click="handleOrderChange(2)">中标金额</span>
            </div>
          </template>
          <div class="tender-collapse-btn">
            <div class="tender-collapse-btn-item" @click="toggleMoreFilter">
              <span>{{ showMoreFilter ? '收起' : '展开' }}</span>
              <i :class="showMoreFilter ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
            </div>
          </div>
        </template>
        <template v-if="activeName === 'collect'">
          <div class="tender-search-bar">
            <div class="tender-search-inputs">
              <el-input v-model="collectParams.keyword" placeholder="请输入产品名称/项目名称等关键词，例如：医疗设备" class="tender-search-input" clearable @keyup.enter.native="handleSearch" @clear="handleClearKeyword" />
              <el-button type="primary" class="tender-search-btn" icon="el-icon-search" @click="handleSearch">搜索</el-button>
            </div>
          </div>
        </template>
      </div>
      <template v-if="activeName === 'all'">
        <div class="tender-card" v-loading="tenderLoading">
          <div class="tender-item" v-for="item in tenderList" :key="item.id">
            <div class="tender-item-head">
              <div class="tender-item-title" @click="handleDetail(item)">{{ item.title }}</div>
              <div class="tender-item-time">{{ parseTime(item.publishTime, '{y}-{m}-{d}') }}</div>
            </div>
            <div class="tender-item-foot">
              <div class="tender-item-tag">
                <span class="tag-item">{{ item.area }}{{ item.area && item.city ? '-' : '' }}{{ item.city }}</span>
                <span class="tag-item" v-if="item.subType">{{ item.subType }}</span>
                <span class="tag-item" v-if="item.buyerClass">{{ item.buyerClass }}</span>
                <span class="tag-item" v-if="item.site">{{ item.site }}</span>
                <div class="tag-comment" v-if="item.commentData && item.commentData.num">
                  <span class="tag-comment-title">评论数量：</span>
                  <span class="tag-comment-info">{{ item.commentData.num }}条</span>
                  <span class="tag-comment-title">评论对象：</span>
                  <span class="tag-comment-info">{{ formatCommentObjective(item.commentData.objective) }}</span>
                  <span class="tag-comment-title">评论内容：</span>
                  <span class="tag-comment-info is-ellipsis">{{ item.commentData.content }}</span>
                </div>
              </div>
              <div class="tender-item-collect" :class="{ 'is-collected': item.isStore }" @click="handleCollect(item)">
                <i :class="item.isStore ? 'el-icon-star-on' : 'el-icon-star-off'"></i>
                <span>{{ item.isStore ? '已收藏' : '收藏' }}</span>
              </div>
            </div>
          </div>
          <el-empty description="暂无数据" v-if="!tenderLoading && tenderTotal === 0"></el-empty>
        </div>
        <!-- 全部分页 -->
        <div class="tender-pagination">
          <pagination v-show="tenderTotal > 0" :total="tenderTotal" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="handlePagination" />
        </div>
      </template>
      <template v-if="activeName === 'ranking'">
        <div class="tender-ranking" v-loading="rankingLoading">
          <template v-if="rankingList.length > 0">
            <div class="tender-ranking-item" v-for="(item, index) in rankingList" :key="index">
              <div class="tender-ranking-item-number" :class="item.indexNum < 4 ? `item-number-${item.indexNum}` : ''">{{ item.indexNum }}</div>
              <div class="tender-ranking-item-info">
                <div class="tender-ranking-item-title" @click="handleCompanySearch(item.winner)">{{ item.winner }}</div>
                <div class="tender-ranking-item-desc">
                  <span class="text">中标金额：</span>
                  <span class="amount">{{ item.totalAmount }}</span>
                  <div style="display: inline-flex; align-items: center" :style="{ cursor: item.count > 0 ? 'pointer' : 'default' }" @click="handleCompanyDetail(item)">
                    <span class="text">中标次数：</span>
                    <span class="count">{{ item.count }}</span>
                  </div>
                </div>
              </div>
            </div>
          </template>
          <el-empty :description="queryParams.keyword ? '暂无数据' : '请输入搜索关键词'" v-else></el-empty>
        </div>
        <!-- 排行榜分页 -->
        <div class="tender-pagination">
          <pagination v-show="rankingTotal > 0" :total="rankingTotal" :page.sync="rankingParams.pageNum" :limit.sync="rankingParams.pageSize" @pagination="handleRankingPagination" />
        </div>
      </template>
      <template v-if="activeName === 'collect'">
        <div class="tender-card" v-loading="collectLoading">
          <div class="tender-item" v-for="item in collectList" :key="item.id">
            <div class="tender-item-head">
              <div class="tender-item-title" @click="handleDetail(item)">{{ item.title }}</div>
              <div class="tender-item-time">{{ parseTime(item.publishTime, '{y}-{m}-{d}') }}</div>
            </div>
            <div class="tender-item-foot">
              <div class="tender-item-tag">
                <span class="tag-item">{{ item.area }}{{ item.area && item.city ? '-' : '' }}{{ item.city }}</span>
                <span class="tag-item" v-if="item.subType">{{ item.subType }}</span>
                <span class="tag-item" v-if="item.buyerClass">{{ item.buyerClass }}</span>
                <span class="tag-item" v-if="item.site">{{ item.site }}</span>
              </div>
              <div class="tender-item-collect" :class="{ 'is-collected': item.isStore }" @click="handleCollect(item)">
                <i :class="item.isStore ? 'el-icon-star-on' : 'el-icon-star-off'"></i>
                <span>{{ item.isStore ? '已收藏' : '收藏' }}</span>
              </div>
            </div>
          </div>
          <el-empty description="暂无数据" v-if="!collectLoading && collectTotal === 0"></el-empty>
        </div>
        <!-- 收藏分页 -->
        <div class="tender-pagination">
          <pagination v-show="collectTotal > 0" :total="collectTotal" :page.sync="collectParams.pageNum" :limit.sync="collectParams.pageSize" @pagination="handleCollectPagination" />
        </div>
      </template>
    </div>
    <footer-tpl />

    <!-- 使用权限到期提醒 -->
    <el-dialog v-dialogDragBox title="使用权限到期提醒" :visible.sync="tenderOpen" width="750px" class="custom-dialog">
      <div class="tenderExpire">
        <div class="tenderExpire-icon">
          <i class="el-icon-warning"></i>
        </div>
        <div class="tenderExpire-title">您的使用权限已到期，请获取权限后再次尝试</div>
        <div class="tenderExpire-desc">获得权限方式：单月平台(公域)成交一次即可获取一个月的使用权限</div>
        <div class="tenderExpire-button" @click="tenderOpen = false">我知道了</div>
      </div>
    </el-dialog>

    <!-- 暂无权限查看 -->
    <el-dialog v-dialogDragBox title="暂无权限查看" :visible.sync="powerOpen" width="750px" class="custom-dialog">
      <div class="tenderExpire">
        <div class="tenderExpire-icon">
          <i class="el-icon-warning"></i>
        </div>
        <div class="tenderExpire-title">个人用户暂时无法查看招投标详情，请升级为企业方可查看</div>
        <div class="tenderExpire-desc">升级方法：登录至管理中心点击升级为企业</div>
        <div class="tenderExpire-button" @click="powerOpen = false">我知道了</div>
      </div>
    </el-dialog>
    <!--企业信息-->
    <company ref="company" />
    <!-- 企业中标明细 -->
    <company-item ref="companyItem" @callback="showCompanyItem = false" v-if="showCompanyItem" />
  </div>
</template>
<script>
import HeaderTpl from '@/views/public/components/head'
import FooterTpl from '@/views/public/components/foot'
import { getToken } from '@/utils/auth'
import { pcaTextArr } from 'element-china-area-data'
import { addTenderKeyword, getRemoteCompanyInfo, getTenderListV2, getTenderRanking, getTenderKeywordExist, getTenderComment } from '@/api/tender'
import { getTenderTime } from '@/api/login'
import { delshouc, getlist, getlistb, shoucTo } from '@/api/houtai/shoucang'
import Company from '@/views/payment/company'
import CompanyItem from '@/views/public/tenderNew/item'
import zlib from 'zlib'

export default {
  name: 'TenderNew',
  components: { HeaderTpl, FooterTpl, Company, CompanyItem },
  data() {
    return {
      isLogin: false, // 是否登录
      activeName: 'all', // 当前选中的标签
      multiKeyword: false, // 是否多关键词搜索
      showMoreFilter: true, // 是否显示更多筛选条件
      // 控制各个popover的显示状态
      popoverVisible: {
        forecast: false,
        notice: false,
        result: false,
        credit: false,
        region: false,
        industry: false,
        amount: false
      },
      // 全部招投标查询参数
      queryParams: {
        area: [], // 省份
        buyer: undefined, // 招标单位
        city: [], // 城市
        containsAll: true, // 多个关键词是否包含全部
        district: [], // 区县
        endAmount: undefined, // 结束金额
        industry: [], // 行业
        pageNum: 1, // 当前页
        scope: [], // 搜索范围
        keyword: undefined, // 搜索关键词(查询时不用传)
        search: [], // 搜索关键词
        pageSize: 10, // 每页条数
        startAmount: undefined, // 开始金额
        subType: [], // 招标公告
        term: 1, // 时间范围
        topType: [], // 招标结果
        winner: undefined // 中标单位
      },
      // 排行榜查询参数
      rankingParams: {
        area: [], // 省份
        city: [], // 城市
        containsAll: true, // 多个关键词是否包含全部
        district: [], // 区县
        industry: [], // 行业
        order: 1, // 排序  1-成交次数 2-金额,可用值:1,2
        pageNum: 1, // 当前页
        pageSize: 10, // 每页条数
        scope: [], // 搜索范围
        keyword: undefined, // 搜索关键词(查询时不用传)
        search: [], // 搜索关键词
        term: 1 // 时间范围
      },
      // 收藏查询参数
      collectParams: {
        storeId: undefined,
        keyword: undefined,
        pageNum: 1,
        pageSize: 10 // 默认值，会被缓存值覆盖
      },
      // 时间范围
      termOptions: [
        { label: '一个月内', value: 1 },
        { label: '三个月内', value: 3 },
        { label: '半年内', value: 6 },
        { label: '一年内', value: 12 }
      ],
      // 关键词搜索模式
      keywordModeOptions: [
        { label: '包含所有关键词', value: true },
        { label: '包含任意关键词', value: false }
      ],
      // 搜索范围
      selectTypes: ['title', 'content'],
      selectTypeOptions: [
        { label: '标题', value: 'title' },
        { label: '正文', value: 'content' },
        { label: '中标单位', value: 'winner' },
        { label: '招标单位', value: 'buyer' }
      ],
      // 招标预告类型选项
      forecastTypeOptions: ['预告', '预审', '预审结果', '论证意见', '需求公示'],
      // 招标公告类型选项
      noticeTypeOptions: ['招标', '邀标', '询价', '竞谈', '单一', '竞价', '变更'],
      // 招标结果类型选项
      resultTypeOptions: ['中标', '成交', '废标', '流标'],
      // 招标信用信息类型选项
      creditTypeOptions: ['合同', '验收', '违规'],
      // 其他筛选条件
      region: [], // 地区
      industry: [], // 行业
      amount: '', // 金额
      // 多个关键词
      keyword: '', // 关键词
      keywords: [], // 多个关键词
      // 常用关键词
      keywordList: [], // 常用关键词
      // 招标预告全选相关
      checkAllForecast: false, // 招标预告全选
      isIndeterminateForecast: false, // 招标预告全选
      selectedForecastTypes: [], // 招标预告类型
      // 招标公告全选相关
      checkAllNotice: false, // 招标公告全选
      isIndeterminateNotice: false, // 招标公告全选
      selectedNoticeTypes: [], // 招标公告类型
      // 招标结果全选相关
      checkAllResult: false, // 招标结果全选
      isIndeterminateResult: false, // 招标结果全选
      selectedResultTypes: [], // 招标结果类型
      // 招标信用信息全选相关
      checkAllCredit: false, // 招标信用信息全选
      isIndeterminateCredit: false, // 招标信用信息全选
      selectedCreditTypes: [], // 招标信用信息类型
      // 地区相关
      pcaTextArr, // 地区数据
      regionProps: {
        multiple: true, // 是否多选
        expandTrigger: 'hover' // 展开方式
      },
      selectedRegionCount: 0, // 地区数量
      // 行业相关
      industryOptions: [
        {
          value: '建筑工程',
          label: '建筑工程',
          children: [
            { value: '工程施工', label: '工程施工' },
            { value: '监理咨询', label: '监理咨询' },
            { value: '材料设备', label: '材料设备' },
            { value: '机电安装', label: '机电安装' },
            { value: '其他', label: '其他' }
          ]
        },
        {
          value: '水利水电',
          label: '水利水电',
          children: [
            { value: '水利工程', label: '水利工程' },
            { value: '发电工程', label: '发电工程' },
            { value: '航运工程', label: '航运工程' },
            { value: '其他工程', label: '其他工程' }
          ]
        },
        {
          value: '能源化工',
          label: '能源化工',
          children: [
            { value: '原材料', label: '原材料' },
            { value: '仪器仪表', label: '仪器仪表' },
            { value: '新能源', label: '新能源' },
            { value: '设备物资', label: '设备物资' },
            { value: '化工产品', label: '化工产品' },
            { value: '设备', label: '设备' },
            { value: '其他', label: '其他' }
          ]
        },
        {
          value: '弱电安防',
          label: '弱电安防',
          children: [
            { value: '综合布线', label: '综合布线' },
            { value: '智能系统', label: '智能系统' },
            { value: '智能家居', label: '智能家居' },
            { value: '其他', label: '其他' }
          ]
        },
        {
          value: '信息技术',
          label: '信息技术',
          children: [
            { value: '系统集成及安全', label: '系统集成及安全' },
            { value: '软件开发', label: '软件开发' },
            { value: '运维服务', label: '运维服务' },
            { value: '其他', label: '其他' }
          ]
        },
        {
          value: '行政办公',
          label: '行政办公',
          children: [
            { value: '办公家具', label: '办公家具' },
            { value: '通用办公设备', label: '通用办公设备' },
            { value: '专业设备', label: '专业设备' },
            { value: '办公用品', label: '办公用品' },
            { value: '生活用品', label: '生活用品' },
            { value: '其他', label: '其他' }
          ]
        },
        {
          value: '机械设备',
          label: '机械设备',
          children: [
            { value: '矿山机械', label: '矿山机械' },
            { value: '工程机械', label: '工程机械' },
            { value: '机械零部件', label: '机械零部件' },
            { value: '机床相关', label: '机床相关' },
            { value: '车辆', label: '车辆' },
            { value: '其他机械设备', label: '其他机械设备' }
          ]
        },
        {
          value: '交通工程',
          label: '交通工程',
          children: [
            { value: '道路', label: '道路' },
            { value: '轨道', label: '轨道' },
            { value: '桥梁', label: '桥梁' },
            { value: '隧道', label: '隧道' },
            { value: '其他', label: '其他' }
          ]
        },
        {
          value: '医疗卫生',
          label: '医疗卫生',
          children: [
            { value: '设备', label: '设备' },
            { value: '耗材', label: '耗材' },
            { value: '药品', label: '药品' },
            { value: '其他', label: '其他' }
          ]
        },
        {
          value: '市政设施',
          label: '市政设施',
          children: [
            { value: '道路', label: '道路' },
            { value: '绿化', label: '绿化' },
            { value: '线路管网', label: '线路管网' },
            { value: '综合项目', label: '综合项目' },
            { value: '其他', label: '其他' }
          ]
        },
        {
          value: '服务采购',
          label: '服务采购',
          children: [
            { value: '法律咨询', label: '法律咨询' },
            { value: '会计', label: '会计' },
            { value: '物业', label: '物业' },
            { value: '审计', label: '审计' },
            { value: '安保', label: '安保' },
            { value: '仓储物流', label: '仓储物流' },
            { value: '广告宣传印刷', label: '广告宣传印刷' },
            { value: '其他', label: '其他' }
          ]
        },
        {
          value: '农林牧渔',
          label: '农林牧渔',
          children: [
            { value: '生产物资', label: '生产物资' },
            { value: '生产设备', label: '生产设备' },
            { value: '相关服务', label: '相关服务' },
            { value: '其他', label: '其他' }
          ]
        },
        {
          value: '其他',
          label: '其他'
        }
      ],
      industryProps: {
        multiple: true, // 是否多选
        expandTrigger: 'hover' // 展开方式
      },
      selectedIndustryCount: 0, // 行业数量
      // 金额相关
      amountOptions: [
        { label: '全部金额', value: '金额' },
        { label: '0-10万', value: '0-10' },
        { label: '10-20万', value: '10-20' },
        { label: '20-50万', value: '20-50' },
        { label: '50-100万', value: '50-100' },
        { label: '100-200万', value: '100-200' },
        { label: '200-500万', value: '200-500' },
        { label: '500-1000万', value: '500-1000' },
        { label: '1000万以上', value: '1000-999999' }
      ],
      customAmountMin: '', // 自定义金额最小值
      customAmountMax: '', // 自定义金额最大值
      selectedAmount: '金额', // 当前选中的金额范围
      // 招标项目列表
      tenderList: [], // 全部招投标列表
      tenderLoading: false, // 招标项目列表加载中
      tenderTotal: 0, // 招标项目列表总数
      // 排行榜列表
      rankingList: [], // 排行榜列表
      rankingListAll: [], // 存储所有排行榜数据
      rankingLoading: false, // 排行榜列表加载中
      rankingTotal: 0, // 排行榜列表总数
      // 已收藏列表
      collectList: [], // 已收藏列表
      collectLoading: false, // 已收藏列表加载中
      collectTotal: 0, // 已收藏列表总数
      // 到期提醒
      tenderTime: undefined, // 招标到期时间
      expirationShow: false, // 到期提醒显示
      isBidding: undefined, // 是否开启招标
      expirationTime: undefined, // 到期时间
      usertype: undefined, // 用户类型
      tenderOpen: false, // 招标开启
      powerOpen: false, // 权限开启
      // 收藏夹列表
      collectTypeList: [], // 收藏夹列表
      // 记录每个标签页的搜索条件
      allSearchRecord: {
        params: {},
        multiKeyword: false,
        keywords: [],
        selectTypes: ['title', 'content'],
        checkAllForecast: false,
        isIndeterminateForecast: false,
        selectedForecastTypes: [],
        checkAllNotice: false,
        isIndeterminateNotice: false,
        selectedNoticeTypes: [],
        checkAllResult: false,
        isIndeterminateResult: false,
        selectedResultTypes: [],
        checkAllCredit: false,
        isIndeterminateCredit: false,
        selectedCreditTypes: [],
        region: [],
        selectedRegionCount: 0,
        industry: [],
        selectedIndustryCount: 0,
        amount: '',
        selectedAmount: '金额'
      },
      commonSearchRecord: {
        params: {},
        multiKeyword: false,
        keywords: [],
        selectTypes: ['title', 'content'],
        region: [],
        selectedRegionCount: 0,
        industry: [],
        selectedIndustryCount: 0,
        amount: '',
        selectedAmount: '金额'
      },
      collectSearchRecord: {
        params: {}
      },
      // 是否显示企业中标明细
      showCompanyItem: false,
      loadingInstance: null // 添加全局loading实例
    }
  },
  mounted() {
    this.isLogin = !!getToken()
    // 初始化每个标签页的参数
    this.initSearchRecords()
  },
  computed: {
    userId() {
      return this.$store.getters.info.userId
    },
    companyId() {
      return (this.$store.getters && this.$store.getters.info && this.$store.getters.info.companyId) || -1
    },
    // 添加全部地区选项到树结构顶端
    regionOptions() {
      return [{ value: 'all', label: '全部地区' }, ...this.pcaTextArr]
    },
    // 添加全部行业选项到树结构顶端
    industryOptionsWithAll() {
      return [{ value: 'all', label: '全部行业' }, ...this.industryOptions]
    }
  },
  created() {
    const { companyId } = this.$store.state.user || -1
    if (companyId > 0) this.usertype = 'gys'
    else this.usertype = 'cgs'
    const { keyword } = this.$route.params
    if (keyword) this.queryParams.keyword = keyword
    // 从缓存中读取分页大小
    this.initPageSizeFromCache()
    // 获取招标项目列表
    this.getTenderList()
    // 获取招标项目限制
    this.getConfigKey('bidding.limit').then(res => {
      this.isBidding = res.msg
      if (res.msg == 'true') this.getTenderShow()
    })
    // 获取收藏夹列表
    if (!!getToken()) this.getCollectList()
    // 获取常用关键词缓存
    const keywordList = localStorage.getItem(this.userId + '.keywordList')
    if (keywordList) this.keywordList = JSON.parse(keywordList)
  },
  methods: {
    // 初始化各标签页的搜索记录
    initSearchRecords() {
      // 全部招投标搜索记录初始化
      this.allSearchRecord.params = { ...this.queryParams }
      // 排行榜搜索记录初始化
      this.commonSearchRecord.params = { ...this.rankingParams }
      // 已收藏搜索记录初始化
      this.collectSearchRecord.params = { ...this.collectParams }
    },
    // 保存当前标签页的搜索条件
    saveCurrentSearchCondition() {
      switch (this.activeName) {
        case 'all':
          this.allSearchRecord = {
            params: JSON.parse(JSON.stringify(this.queryParams)),
            multiKeyword: this.multiKeyword,
            keywords: [...this.keywords],
            selectTypes: [...this.selectTypes],
            checkAllForecast: this.checkAllForecast,
            isIndeterminateForecast: this.isIndeterminateForecast,
            selectedForecastTypes: [...this.selectedForecastTypes],
            checkAllNotice: this.checkAllNotice,
            isIndeterminateNotice: this.isIndeterminateNotice,
            selectedNoticeTypes: [...this.selectedNoticeTypes],
            checkAllResult: this.checkAllResult,
            isIndeterminateResult: this.isIndeterminateResult,
            selectedResultTypes: [...this.selectedResultTypes],
            checkAllCredit: this.checkAllCredit,
            isIndeterminateCredit: this.isIndeterminateCredit,
            selectedCreditTypes: [...this.selectedCreditTypes],
            region: this.region ? [...this.region] : [],
            selectedRegionCount: this.selectedRegionCount,
            industry: this.industry ? [...this.industry] : [],
            selectedIndustryCount: this.selectedIndustryCount,
            amount: this.amount,
            selectedAmount: this.selectedAmount
          }
          break
        case 'ranking':
          this.commonSearchRecord = {
            params: JSON.parse(JSON.stringify(this.rankingParams)),
            multiKeyword: this.multiKeyword,
            keywords: [...this.keywords],
            selectTypes: [...this.selectTypes],
            region: this.region ? [...this.region] : [],
            selectedRegionCount: this.selectedRegionCount,
            industry: this.industry ? [...this.industry] : [],
            selectedIndustryCount: this.selectedIndustryCount,
            amount: this.amount,
            selectedAmount: this.selectedAmount
          }
          break
        case 'collect':
          this.collectSearchRecord = {
            params: JSON.parse(JSON.stringify(this.collectParams))
          }
          break
      }
    },
    // 查询收藏夹列表
    getCollectList() {
      getlist({ type: 'biddingDataWithJy' }).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          this.collectTypeList = data
          this.collectParams.storeId = data.length > 0 ? data[0].storeId : undefined
        } else this.$message.error(msg)
      })
    },
    // 获取招标项目限制
    getTenderShow() {
      const endTime = localStorage.getItem('tenderEndTime') || ''
      const startTime = this.parseTime(new Date(), '{y}-{m}-{d}')
      if (endTime) {
        if (startTime != endTime) {
          localStorage.removeItem('tenderEndTime')
          this.getTenderTimeFun()
        } else this.expirationShow = false
      } else this.getTenderTimeFun()
    },
    // 获取招标到期时间
    getTenderTimeFun() {
      const tenderTime = this.$store.getters && this.$store.getters.tenderTime
      const nowTime = new Date().getTime()
      let diffInMilliseconds
      let daysDiff
      if (tenderTime) {
        this.tenderTime = tenderTime
        if (tenderTime > nowTime) {
          diffInMilliseconds = Math.abs(tenderTime - nowTime)
          daysDiff = Math.ceil(diffInMilliseconds / (1000 * 60 * 60 * 24))
          this.expirationTime = daysDiff > 99 ? 99 : daysDiff
        } else {
          this.expirationTime = 0
        }
        this.expirationShow = true
      } else {
        getTenderTime().then(res => {
          const { code, msg, data } = res
          if (code === 200) {
            this.tenderTime = data
            if (data > nowTime) {
              diffInMilliseconds = Math.abs(data - nowTime)
              daysDiff = Math.ceil(diffInMilliseconds / (1000 * 60 * 60 * 24))
              this.expirationTime = daysDiff > 99 ? 99 : daysDiff
            } else {
              this.expirationTime = 0
            }
            this.expirationShow = true
          } else {
            this.$message.error(msg)
          }
        })
      }
    },
    // 切换tab
    handleClick(value) {
      // 保存当前标签页的筛选条件
      this.saveCurrentSearchCondition()
      this.activeName = value
      // 根据切换的标签页加载对应的筛选条件
      switch (value) {
        case 'all':
          if (Object.keys(this.allSearchRecord.params).length > 0) {
            this.queryParams = JSON.parse(JSON.stringify(this.allSearchRecord.params))
            this.multiKeyword = this.allSearchRecord.multiKeyword
            this.keywords = [...this.allSearchRecord.keywords]
            this.selectTypes = [...this.allSearchRecord.selectTypes]
            this.checkAllForecast = this.allSearchRecord.checkAllForecast
            this.isIndeterminateForecast = this.allSearchRecord.isIndeterminateForecast
            this.selectedForecastTypes = [...this.allSearchRecord.selectedForecastTypes]
            this.checkAllNotice = this.allSearchRecord.checkAllNotice
            this.isIndeterminateNotice = this.allSearchRecord.isIndeterminateNotice
            this.selectedNoticeTypes = [...this.allSearchRecord.selectedNoticeTypes]
            this.checkAllResult = this.allSearchRecord.checkAllResult
            this.isIndeterminateResult = this.allSearchRecord.isIndeterminateResult
            this.selectedResultTypes = [...this.allSearchRecord.selectedResultTypes]
            this.checkAllCredit = this.allSearchRecord.checkAllCredit
            this.isIndeterminateCredit = this.allSearchRecord.isIndeterminateCredit
            this.selectedCreditTypes = [...this.allSearchRecord.selectedCreditTypes]
            this.region = this.allSearchRecord.region ? [...this.allSearchRecord.region] : []
            this.selectedRegionCount = this.allSearchRecord.selectedRegionCount
            this.industry = this.allSearchRecord.industry ? [...this.allSearchRecord.industry] : []
            this.selectedIndustryCount = this.allSearchRecord.selectedIndustryCount
            this.amount = this.allSearchRecord.amount
            this.selectedAmount = this.allSearchRecord.selectedAmount
          }
          break
        case 'ranking':
          if (Object.keys(this.commonSearchRecord.params).length > 0) {
            this.rankingParams = JSON.parse(JSON.stringify(this.commonSearchRecord.params))
            this.multiKeyword = this.commonSearchRecord.multiKeyword
            this.keywords = [...this.commonSearchRecord.keywords]
            this.selectTypes = [...this.commonSearchRecord.selectTypes]
            this.region = this.commonSearchRecord.region ? [...this.commonSearchRecord.region] : []
            this.selectedRegionCount = this.commonSearchRecord.selectedRegionCount
            this.industry = this.commonSearchRecord.industry ? [...this.commonSearchRecord.industry] : []
            this.selectedIndustryCount = this.commonSearchRecord.selectedIndustryCount
            this.amount = this.commonSearchRecord.amount
            this.selectedAmount = this.commonSearchRecord.selectedAmount
          }
          break
        case 'collect':
          if (Object.keys(this.collectSearchRecord.params).length > 0) {
            this.collectParams = JSON.parse(JSON.stringify(this.collectSearchRecord.params))
          }
          break
      }
      this.handleSearch()
    },
    // 清空关键词
    handleClearKeyword() {
      this.multiKeyword = false
      this.keywords = []
      this.queryParams.keyword = ''
      if (this.activeName !== 'ranking') this.handleSearch()
      if (this.activeName === 'ranking') {
        this.collectParams.keyword = ''
        this.getTenderRanking()
      }
    },
    // 多个关键词
    handleMultiKeywordChange() {
      if (this.keywords.length > 0) this.handleSearch()
    },
    // 切换排行依据
    handleOrderChange(value) {
      this.rankingParams.order = value
      this.handleSearch()
    },
    // 搜索
    async handleSearch() {
      const { activeName } = this
      let queryWord = ''
      let searchParams = {}
      // 根据不同标签页获取搜索关键词和参数
      switch (activeName) {
        case 'all':
          queryWord = this.queryParams.keyword
          searchParams = { ...this.queryParams }
          break
        case 'ranking':
          queryWord = this.rankingParams.keyword
          searchParams = { ...this.rankingParams }
          break
        case 'collect':
          queryWord = this.collectParams.keyword
          searchParams = { ...this.collectParams }
          break
      }
      if (queryWord && queryWord.trim() === '') return
      // 如果有关键词，添加到常用关键词
      if (queryWord && activeName !== 'collect') {
        // 检查是否已存在
        const { data: isHas } = await getTenderKeywordExist({ keyword: queryWord })
        if (!isHas) {
          // 不存在则添加到服务器
          const data = { keyword: queryWord }
          try {
            await addTenderKeyword(data)
          } catch (error) {
            console.error('添加关键词失败', error)
          }
        }
        // 检查是否已存在于keywordList中
        const existsInList = this.keywordList.findIndex(item => item === queryWord)
        if (existsInList === -1) {
          // 不存在则添加到本地列表
          if (this.keywordList.length >= 10) {
            this.keywordList.shift()
          }
          this.keywordList.push(queryWord)
          // 更新本地缓存
          localStorage.setItem(this.userId + '.keywordList', JSON.stringify(this.keywordList))
        }
      }
      // 如果多个关键词
      if (this.keywords.length > 0 && activeName !== 'collect') {
        // 检查this.keywords是否已存在
        for (const keyword of this.keywords) {
          const { data: isHas } = await getTenderKeywordExist({ keyword: keyword })
          if (!isHas) {
            // 不存在则添加到服务器
            const data = { keyword }
            try {
              await addTenderKeyword(data)
            } catch (error) {
              console.error('添加关键词失败', error)
            }
          }
        }
        // 检查this.keywords是否已存在于keywordList中
        this.keywords.forEach(keyword => {
          const existsInList = this.keywordList.findIndex(item => item === keyword)
          if (existsInList === -1) {
            if (this.keywordList.length >= 10) {
              this.keywordList.shift()
            }
            this.keywordList.push(keyword)
            // 更新本地缓存
            localStorage.setItem(this.userId + '.keywordList', JSON.stringify(this.keywordList))
          }
        })
      }
      // 根据不同的标签页处理搜索
      switch (activeName) {
        case 'ranking':
          await this.getTenderRanking(searchParams)
          break
        case 'collect':
          await this.getTenderCollect(searchParams)
          break
        default:
          await this.getTenderList(searchParams)
      }
    },
    // 点击"全部"按钮，清空所有已选择的类型
    handleAllTypesClick() {
      this.queryParams.topType = []
      this.queryParams.subType = []
      // 清空招标预告
      this.selectedForecastTypes = []
      this.checkAllForecast = false
      this.isIndeterminateForecast = false
      // 清空招标公告
      this.selectedNoticeTypes = []
      this.checkAllNotice = false
      this.isIndeterminateNotice = false
      // 清空招标结果
      this.selectedResultTypes = []
      this.checkAllResult = false
      this.isIndeterminateResult = false
      // 清空招标信用信息
      this.selectedCreditTypes = []
      this.checkAllCredit = false
      this.isIndeterminateCredit = false
      this.handleSearch()
    },
    // 招标预告相关方法
    handleForecastItemClick() {
      this.handleItemClickCommon(this.selectedForecastTypes, this.forecastTypeOptions, 'checkAllForecast', 'isIndeterminateForecast')
    },
    handleCheckAllForecast(val) {
      this.selectedForecastTypes = val ? [...this.forecastTypeOptions] : []
      this.isIndeterminateForecast = false
      this.updateForecastType()
    },
    handleForecastTypeChange(value) {
      const checkedCount = value.length
      this.checkAllForecast = checkedCount === this.forecastTypeOptions.length
      this.isIndeterminateForecast = checkedCount > 0 && checkedCount < this.forecastTypeOptions.length
      this.updateForecastType()
    },
    updateForecastType() {
      this.updateTypeCommon('预告', this.forecastTypeOptions, this.selectedForecastTypes, this.checkAllForecast, this.checkAllNotice, this.checkAllResult, this.checkAllCredit)
    },
    // 招标公告相关方法
    handleNoticeItemClick() {
      this.handleItemClickCommon(this.selectedNoticeTypes, this.noticeTypeOptions, 'checkAllNotice', 'isIndeterminateNotice')
    },
    handleCheckAllNotice(val) {
      this.selectedNoticeTypes = val ? [...this.noticeTypeOptions] : []
      this.isIndeterminateNotice = false
      this.updateNoticeType()
    },
    handleNoticeTypeChange(value) {
      const checkedCount = value.length
      this.checkAllNotice = checkedCount === this.noticeTypeOptions.length
      this.isIndeterminateNotice = checkedCount > 0 && checkedCount < this.noticeTypeOptions.length
      this.updateNoticeType()
    },
    updateNoticeType() {
      this.updateTypeCommon('招标', this.noticeTypeOptions, this.selectedNoticeTypes, this.checkAllNotice, this.checkAllForecast, this.checkAllResult, this.checkAllCredit)
    },
    // 招标结果相关方法
    handleResultItemClick() {
      this.handleItemClickCommon(this.selectedResultTypes, this.resultTypeOptions, 'checkAllResult', 'isIndeterminateResult')
    },
    handleCheckAllResult(val) {
      this.selectedResultTypes = val ? [...this.resultTypeOptions] : []
      this.isIndeterminateResult = false
      this.updateResultType()
    },
    handleResultTypeChange(value) {
      const checkedCount = value.length
      this.checkAllResult = checkedCount === this.resultTypeOptions.length
      this.isIndeterminateResult = checkedCount > 0 && checkedCount < this.resultTypeOptions.length
      this.updateResultType()
    },
    updateResultType() {
      this.updateTypeCommon('结果', this.resultTypeOptions, this.selectedResultTypes, this.checkAllResult, this.checkAllForecast, this.checkAllNotice, this.checkAllCredit)
    },
    // 招标信用信息相关方法
    handleCreditItemClick() {
      this.handleItemClickCommon(this.selectedCreditTypes, this.creditTypeOptions, 'checkAllCredit', 'isIndeterminateCredit')
    },
    handleCheckAllCredit(val) {
      this.selectedCreditTypes = val ? [...this.creditTypeOptions] : []
      this.isIndeterminateCredit = false
      this.updateCreditType()
    },
    handleCreditTypeChange(value) {
      const checkedCount = value.length
      this.checkAllCredit = checkedCount === this.creditTypeOptions.length
      this.isIndeterminateCredit = checkedCount > 0 && checkedCount < this.creditTypeOptions.length
      this.updateCreditType()
    },
    updateCreditType() {
      this.updateTypeCommon('其他', this.creditTypeOptions, this.selectedCreditTypes, this.checkAllCredit, this.checkAllForecast, this.checkAllNotice, this.checkAllResult)
    },
    // 搜索范围相关方法
    handleSelectTypeChange(value) {
      if (value.includes('winner') && value[0] !== 'winner') {
        this.selectTypes = ['winner']
      } else if (value.includes('buyer') && value[0] !== 'buyer') {
        this.selectTypes = ['buyer']
      } else {
        this.selectTypes = value.filter(item => item !== 'winner' && item !== 'buyer')
      }
      this.handleSearch()
    },
    // 展开/收起更多筛选条件
    toggleMoreFilter() {
      this.showMoreFilter = !this.showMoreFilter
    },
    // 添加统一的loading处理方法
    showLoading(text = '努力加载中…') {
      this.loadingInstance = this.$loading({
        lock: true,
        text,
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.5)'
      })
    },
    hideLoading() {
      if (this.loadingInstance) {
        this.loadingInstance.close()
        this.loadingInstance = null
      }
    },
    // 统一的错误处理方法
    handleError(error, defaultMsg = '操作失败，请稍后重试') {
      console.error(error)
      const message = error?.msg || error?.message || defaultMsg
      this.$message.error(message)
    },
    // 优化获取招标项目列表方法
    async getTenderList(params = this.queryParams) {
      try {
        this.showLoading()
        this.tenderLoading = true
        let query = { ...params }
        // 根据搜索范围设置搜索参数
        if (this.selectTypes.includes('winner')) {
          query.scope = ['winner']
          query.winner = query.keyword
          query.buyer = undefined
          query.search = []
        } else if (this.selectTypes.includes('buyer')) {
          query.scope = ['buyer']
          query.buyer = query.keyword
          query.winner = undefined
          query.search = []
        } else {
          query.scope = this.selectTypes.filter(item => item !== 'winner' && item !== 'buyer')
          query.winner = undefined
          query.buyer = undefined
          query.search = this.multiKeyword && this.keywords.length > 0 ? (query.keyword ? [query.keyword, ...this.keywords] : [...this.keywords]) : query.keyword ? [query.keyword] : []
        }
        delete query.keyword
        // 确保分页参数存在
        query.pageNum = query.pageNum || this.queryParams.pageNum
        query.pageSize = query.pageSize || this.queryParams.pageSize
        // 保存当前"全部招投标"的搜索条件
        this.saveCurrentSearchCondition()
        const res = await getTenderListV2(query)
        const { code, msg, rows, total } = res
        if (code === 200) {
          this.tenderList = rows
          this.tenderTotal = total
          // 等待评论数据加载完成再结束
          if (total) await this.getTenderCommentList(rows)
        } else {
          this.handleError({ msg })
        }
      } catch (error) {
        this.handleError(error)
      } finally {
        this.tenderLoading = false
        this.hideLoading()
      }
    },
    // 加载招投标评论
    async getTenderCommentList(rows) {
      if (!rows.length) return
      try {
        // 使用 Promise.all 来并发处理所有请求
        const commentPromises = rows.map(async item => {
          try {
            const res = await getTenderComment({ bidId: item.jyId })
            const { code, data } = res
            if (code === 200) {
              if (data && data.length > 0) {
                const lastRow = data[data.length - 1]
                const commentData = {
                  num: data.length || 0,
                  objective: lastRow.objective,
                  content: lastRow.name
                }
                // 使用 $set 确保 Vue 响应式系统能检测到变化
                this.$set(item, 'commentData', commentData)
              } else {
                this.$set(item, 'commentData', {})
              }
            }
          } catch (itemError) {
            console.error(`获取评论失败 bidId: ${item.jyId}`, itemError)
            this.$set(item, 'commentData', {})
          }
        })
        // 等待所有评论请求完成
        await Promise.all(commentPromises)
        // 强制更新视图，确保数据变化能立即显示
        this.$forceUpdate()
      } catch (error) {
        this.handleError(error)
      }
    },
    formatCommentObjective(objective) {
      if (!objective) return ''
      // 处理多个标签的情况（用逗号分隔）
      const objectives = objective.split(',')
      const labelMap = {
        pt: '发布平台',
        zab: '招标单位',
        zob: '中标单位',
        nr: '当前招标项目/内容'
      }
      const labels = objectives.map(obj => labelMap[obj.trim()] || obj.trim()).filter(Boolean)
      return labels.join('、')
    },
    // 优化获取收藏列表方法
    async getTenderCollect(params = this.collectParams) {
      if (!this.collectTypeList.length) {
        this.$message.error('获取收藏夹列表失败，请稍后重新尝试')
        return
      }
      try {
        this.showLoading()
        this.collectLoading = true
        const query = {
          ...params,
          storeId: this.collectTypeList[0].storeId,
          pageNum: params.pageNum || this.collectParams.pageNum,
          pageSize: params.pageSize || this.collectParams.pageSize
        }
        const res = await getlistb(query)
        const { code, msg, rows, total } = res
        if (code === 200) {
          this.collectList = rows.map(item => ({ ...item, isStore: true }))
          this.collectTotal = total
        } else {
          this.handleError({ msg })
        }
      } catch (error) {
        this.handleError(error)
      } finally {
        this.collectLoading = false
        this.hideLoading()
      }
    },
    // 优化获取排行榜方法
    async getTenderRanking(params = this.rankingParams) {
      if (!params.keyword) {
        this.rankingList = []
        this.rankingListAll = []
        this.rankingTotal = 0
        this.rankingLoading = false
        return
      }
      try {
        this.showLoading()
        this.rankingLoading = true
        let query = { ...params }
        if (this.selectTypes.includes('winner')) {
          query.scope = ['winner']
          query.winner = query.keyword
          query.buyer = undefined
          query.search = []
        } else if (this.selectTypes.includes('buyer')) {
          query.scope = ['buyer']
          query.buyer = query.keyword
          query.winner = undefined
          query.search = []
        } else {
          query.scope = this.selectTypes.filter(item => item !== 'winner' && item !== 'buyer')
          query.winner = undefined
          query.buyer = undefined
          query.search = this.multiKeyword && this.keywords.length > 0 ? (query.keyword ? [query.keyword, ...this.keywords] : [...this.keywords]) : query.keyword ? [query.keyword] : []
        }
        delete query.keyword
        delete query.pageNum
        delete query.pageSize
        if (!query.search?.length) {
          this.rankingList = []
          this.rankingListAll = []
          this.rankingTotal = 0
          return
        }
        // 保存当前"排行榜"的搜索条件
        this.saveCurrentSearchCondition()
        const res = await getTenderRanking(query)
        const { code, msg, data } = res
        if (code === 200) {
          const rankedData = data.map((item, index) => ({ ...item, indexNum: index + 1 }))
          this.rankingListAll = rankedData
          this.rankingTotal = rankedData.length
          // 更新当前页数据
          const start = (this.rankingParams.pageNum - 1) * this.rankingParams.pageSize
          const end = start + this.rankingParams.pageSize
          this.rankingList = this.rankingListAll.slice(start, end)
        } else {
          this.handleError({ msg })
        }
      } catch (error) {
        this.handleError(error)
      } finally {
        this.rankingLoading = false
        this.hideLoading()
      }
    },
    // 优化收藏/取消收藏方法
    async handleCollect(item) {
      if (!getToken()) {
        await this.$alert('请先登录', '系统提示', {
          confirmButtonText: '登录',
          showCancelButton: false,
          showClose: false,
          showConfirmButton: true,
          callback: () => {
            this.$router.push('/login')
          }
        })
        return
      }
      if (!this.collectTypeList.length) {
        this.$message.error('获取收藏夹列表失败，请稍后重新尝试')
        return
      }
      try {
        const storeId = this.collectTypeList[0].storeId
        const collectData = !item.isStore ? { storeId, valueIdList: [item.jyId] } : { storeId, valueId: item.jyId }
        const res = await (!item.isStore ? shoucTo(collectData) : delshouc(collectData))
        const { code, msg } = res
        if (code === 200) {
          item.isStore = !item.isStore
          if (this.activeName === 'collect') {
            if (item.isStore) {
              this.collectTotal += 1
            } else {
              const index = this.collectList.findIndex(i => i.jyId === item.jyId)
              if (index > -1) {
                this.collectList.splice(index, 1)
                this.collectTotal -= 1
                if (this.collectList.length === 0 && this.collectParams.pageNum > 1) {
                  this.collectParams.pageNum -= 1
                  await this.getTenderCollect()
                }
              }
            }
          }
        } else {
          this.handleError({ msg })
        }
      } catch (error) {
        this.handleError(error)
      }
    },
    // 优化关键词处理方法
    async handleAddKeyword() {
      if (!this.keyword?.trim()) {
        this.$message.warning('请输入有效的关键词')
        return
      }
      if (this.keywords.includes(this.keyword)) {
        this.$message.warning('关键词已存在')
        this.keyword = ''
        return
      }
      try {
        // 检查是否已存在于
        const { data: isHas } = await getTenderKeywordExist({ keyword: this.keyword })
        if (!isHas) {
          await addTenderKeyword({ keyword: this.keyword })
        }
        // 检查是否已存在于keywordList中
        const existsInList = this.keywordList.findIndex(item => item === this.keyword) === -1
        if (existsInList) {
          if (this.keywordList.length >= 10) {
            this.keywordList.shift()
          }
          this.keywordList.push(this.keyword)
          localStorage.setItem(this.userId + '.keywordList', JSON.stringify(this.keywordList))
        }
        this.keywords.push(this.keyword)
        this.keyword = ''
        await this.handleSearch()
      } catch (error) {
        this.handleError(error, '添加关键词失败')
      }
    },
    // 地区选择变化
    handleRegionChange(value) {
      // 检查是否选择了"全部地区"
      if (Array.isArray(value) && value.some(path => path[0] === 'all')) {
        // 选择了全部地区，清空所有选择
        this.region = []
        if (this.activeName === 'ranking') {
          this.rankingParams.area = []
          this.rankingParams.city = []
          this.rankingParams.district = []
        } else {
          this.queryParams.area = []
          this.queryParams.city = []
          this.queryParams.district = []
        }
        this.selectedRegionCount = 0
      } else {
        this.region = value
        // 处理地区选择，更新查询参数
        const areas = new Set()
        const cities = new Set()
        const districts = new Set()
        if (Array.isArray(value)) {
          value.forEach(path => {
            if (path && path.length > 0) {
              // 添加省份，处理特殊省份名称
              if (path[0]) {
                let provinceName = path[0]
                // 特殊处理自治区名称
                if (provinceName === '广西壮族自治区') {
                  provinceName = '广西'
                } else if (provinceName === '宁夏回族自治区') {
                  provinceName = '宁夏'
                } else if (provinceName === '新疆维吾尔自治区') {
                  provinceName = '新疆'
                } else if (provinceName === '内蒙古自治区') {
                  provinceName = '内蒙古'
                } else if (provinceName === '西藏自治区') {
                  provinceName = '西藏'
                } else {
                  // 去掉其他省份中的"省"、"市"、"自治区"字样
                  provinceName = provinceName.replace(/(省|市|自治区)$/, '')
                }
                areas.add(provinceName)
              }
              // 添加城市（如果不是"市辖区"）
              if (path.length > 1 && path[1] && path[1] !== '市辖区') {
                cities.add(path[1])
              }
              // 添加区县
              if (path.length > 2 && path[2]) {
                districts.add(path[2])
              }
            }
          })
        }
        // 更新查询参数
        const areaArray = Array.from(areas)
        const cityArray = Array.from(cities)
        const districtArray = Array.from(districts)
        if (this.activeName === 'ranking') {
          this.rankingParams.area = areaArray
          this.rankingParams.city = cityArray
          this.rankingParams.district = districtArray
        } else {
          this.queryParams.area = areaArray
          this.queryParams.city = cityArray
          this.queryParams.district = districtArray
        }
        // 计算顶级父类数量
        const topLevelRegions = new Set()
        if (Array.isArray(value)) {
          value.forEach(path => {
            if (path && path.length > 0) {
              topLevelRegions.add(path[0])
            }
          })
        }
        this.selectedRegionCount = topLevelRegions.size
      }
      this.handleSearch()
    },
    // 行业选择变化
    handleIndustryChange(value) {
      // 检查是否选择了"全部行业"
      if (Array.isArray(value) && value.some(path => path[0] === 'all')) {
        // 选择了全部行业，清空所有选择
        this.industry = []
        if (this.activeName === 'ranking') {
          this.rankingParams.industry = []
        } else {
          this.queryParams.industry = []
        }
        this.selectedIndustryCount = 0
      } else {
        this.industry = value
        const industryArray = value.map(item => item.join('_'))
        if (this.activeName === 'ranking') {
          this.rankingParams.industry = industryArray
        } else {
          this.queryParams.industry = industryArray
        }
        // 计算子类数量
        let childCount = 0
        if (Array.isArray(value)) {
          value.forEach(path => {
            if (path && path.length > 1) {
              // 如果选择了子类，计数+1
              childCount++
            }
          })
        }
        this.selectedIndustryCount = childCount
      }
      this.handleSearch()
    },
    // 金额选择变化
    handleAmountChange(item) {
      const { value, label } = item
      this.selectedAmount = label
      this.$refs.amountPopover.doClose()
      const amount = value.split('-').map(Number)
      const startAmount = parseFloat((Number(amount[0]) * 10000).toFixed(0)) || undefined
      const endAmount = parseFloat((Number(amount[1]) * 10000).toFixed(0)) || undefined
      if (this.activeName === 'ranking') {
        this.rankingParams.startAmount = startAmount
        this.rankingParams.endAmount = endAmount
      } else {
        this.queryParams.startAmount = startAmount
        this.queryParams.endAmount = endAmount
      }
      this.handleSearch()
    },
    // 自定义金额范围确定
    handleCustomAmountConfirm() {
      this.selectedAmount = this.customAmountMin + '-' + this.customAmountMax + '万'
      this.$refs.amountPopover.doClose()
      const startAmount = parseFloat((Number(this.customAmountMin) * 10000).toFixed(0)) || undefined
      const endAmount = parseFloat((Number(this.customAmountMax) * 10000).toFixed(0)) || undefined
      if (this.activeName === 'ranking') {
        this.rankingParams.startAmount = startAmount
        this.rankingParams.endAmount = endAmount
      } else {
        this.queryParams.startAmount = startAmount
        this.queryParams.endAmount = endAmount
      }
      this.handleSearch()
    },
    // 删除关键词
    handleRemoveKeyword(index) {
      this.keywords.splice(index, 1)
      this.handleSearch()
    },
    // 关键词筛选模式变化
    handleKeywordModeChange() {
      this.handleSearch()
    },
    // 选中关键词
    handleSelectKeyword(item) {
      // 保存当前的筛选条件
      const currentParams = this.activeName === 'ranking' ? { ...this.rankingParams } : this.activeName === 'collect' ? { ...this.collectParams } : { ...this.queryParams }
      // 更新关键词
      if (this.activeName === 'ranking') {
        this.rankingParams = { ...currentParams, keyword: item }
      } else if (this.activeName === 'collect') {
        this.collectParams = { ...currentParams, keyword: item }
      } else {
        this.queryParams = { ...currentParams, keyword: item }
      }
      // 触发搜索
      this.handleSearch()
    },
    // 删除常用关键词
    handleRemoveCommonKeyword(item) {
      const index = this.keywordList.indexOf(item)
      if (index !== -1) {
        this.keywordList.splice(index, 1)
        // 更新本地缓存
        localStorage.setItem(this.userId + '.keywordList', JSON.stringify(this.keywordList))
      }
    },
    // 关闭到期提醒
    handleExpiration() {
      this.expirationShow = false
      localStorage.setItem(this.userId + '.tenderEndTime', this.parseTime(new Date(), '{y}-{m}-{d}'))
    },
    // 加密
    encrypt(str) {
      return zlib.deflateSync(str).toString('base64')
    },
    // 查看详情
    handleDetail(item, items = []) {
      // if (this.usertype !== 'gys') {
      //   this.powerOpen = true
      //   return
      // }
      const now = new Date().getTime()
      if (this.tenderTime && now > this.tenderTime && this.isBidding == 'true') {
        this.tenderOpen = true
        return
      }
      let params = {}
      if (this.activeName == 'collect') {
        params = { ...this.collectParams, tabType: this.activeName }
      } else if (this.activeName == 'ranking') {
        params = { items, tabType: this.activeName }
      } else {
        let query = { ...this.queryParams }
        if (this.keywords.length > 0) {
          query.search = query.keyword ? [query.keyword, ...this.keywords] : [...this.keywords]
        } else {
          query.search = query.keyword ? [query.keyword] : []
        }
        delete query.keyword
        delete query.order
        params = { ...query, tabType: this.activeName }
      }
      const paramsStr = JSON.stringify(params)
      const encodedParams = encodeURIComponent(paramsStr)
      const encryptedParams = this.encrypt(encodedParams)
      const finalParams = encryptedParams.replace(/\+/g, '%2B')
      window.open(`/tender/detail?bidId=${item.jyId}&params=${finalParams}`)
    },
    // 通用的类型更新方法
    updateTypeCommon(typeName, typeOptions, selectedTypes, isCurrentAllChecked, ...otherCheckAllFlags) {
      // 检查是否所有类型全选
      if (isCurrentAllChecked && otherCheckAllFlags.every(flag => flag)) {
        this.handleAllTypesClick()
        return
      }
      // 处理topType参数
      if (selectedTypes.length > 0) {
        // 如果topType为空，直接设置为typeName
        if (!this.queryParams.topType.length) {
          this.queryParams.topType = [typeName]
        }
        // 如果topType不为空且不包含typeName，则添加
        else if (!this.queryParams.topType.includes(typeName)) {
          this.queryParams.topType.push(typeName)
        }
      } else {
        // 从topType中移除typeName
        if (this.queryParams.topType.length) {
          const index = this.queryParams.topType.indexOf(typeName)
          if (index > -1) {
            this.queryParams.topType.splice(index, 1)
          }
        }
      }
      // 处理subType参数
      if (selectedTypes.length === 0) {
        // 如果没有选择任何类型，清空subType中所有相关类型
        if (this.queryParams.subType.length) {
          this.queryParams.subType = this.queryParams.subType.filter(type => !typeOptions.includes(type))
        }
      } else {
        // 如果subType已经存在，需要先移除所有相关类型，再重新添加选中的类型
        if (this.queryParams.subType.length) {
          // 过滤掉所有typeOptions中的类型
          const filteredTypes = this.queryParams.subType.filter(type => !typeOptions.includes(type))
          // 将选中的类型添加到过滤后的数组中
          this.queryParams.subType = [...filteredTypes, ...selectedTypes]
        } else {
          // 如果subType为空，直接设置
          this.queryParams.subType = [...selectedTypes]
        }
      }
      this.handleSearch()
    },
    // 通用的点击处理方法
    handleItemClickCommon(selectedTypes, typeOptions, checkAllProperty, indeterminateProperty) {
      if (selectedTypes.length === 0 || (selectedTypes.length > 0 && selectedTypes.length < typeOptions.length)) {
        // 未选择任何项或部分选择时，全选
        this[checkAllProperty] = true
        this[indeterminateProperty] = false
        // 清空数组并添加所有选项
        selectedTypes.splice(0, selectedTypes.length, ...typeOptions)
      } else if (this[checkAllProperty]) {
        // 全选时，取消全选
        this[checkAllProperty] = false
        this[indeterminateProperty] = false
        // 清空数组
        selectedTypes.splice(0, selectedTypes.length)
      }
      // 根据不同的checkAllProperty调用对应的更新方法
      if (checkAllProperty === 'checkAllForecast') {
        this.updateForecastType()
      } else if (checkAllProperty === 'checkAllNotice') {
        this.updateNoticeType()
      } else if (checkAllProperty === 'checkAllResult') {
        this.updateResultType()
      } else if (checkAllProperty === 'checkAllCredit') {
        this.updateCreditType()
      }
    },
    // 查询公司信息
    // prettier-ignore
    handleCompanySearch(text = undefined) {
      if (!text) return
      const options = {
        lock: true,
        text: '搜索中…',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      }
      let dataLoading = this.$loading(options)
      this.mapShow = false
      getRemoteCompanyInfo({ companyName: text }).then(res => {
        const { code, data } = res
        if (code === 200) {
          this.$nextTick(() => {
            dataLoading.close()
          })
          if (data && data.hasOwnProperty('companyCreditCode') && data.companyCreditCode) {
            this.$refs.company.getInfo(data, 'private')
          } else {
            this.$nextTick(() => {
              dataLoading.close()
            })
            this.$alert(`未查询到"<span style='color:#ff0000'>${text}</span>"相关公司信息！<br />请检查公司名称是否正确，请输入公司全称！`, '系统提示', { dangerouslyUseHTMLString: true })
          }
        } else {
          this.$nextTick(() => {
            dataLoading.close()
          })
          this.$alert(`未查询到"<span style='color:#ff0000'>${text}</span>"相关公司信息！<br />请检查公司名称是否正确，请输入公司全称！`, '系统提示', { dangerouslyUseHTMLString: true })
        }
      })
    },
    // 点击查看中标明细
    handleCompanyDetail(item) {
      const { count, items } = item
      if (!count) return
      this.showCompanyItem = true
      this.$nextTick(() => {
        this.$refs.companyItem.show(items, this.collectList)
      })
    },
    // 初始化分页大小从缓存
    initPageSizeFromCache() {
      const userId = this.userId
      // 读取全部招投标分页大小
      const allPageSize = localStorage.getItem(`${userId}.allPageSize`)
      if (allPageSize) {
        this.queryParams.pageSize = parseInt(allPageSize)
      }
      // 读取排行榜分页大小
      const rankingPageSize = localStorage.getItem(`${userId}.rankingPageSize`)
      if (rankingPageSize) {
        this.rankingParams.pageSize = parseInt(rankingPageSize)
      }
      // 读取已收藏分页大小
      const collectPageSize = localStorage.getItem(`${userId}.collectPageSize`)
      if (collectPageSize) {
        this.collectParams.pageSize = parseInt(collectPageSize)
      }
    },
    // 保存分页大小到缓存
    savePageSizeToCache(type, size) {
      const userId = this.userId
      switch (type) {
        case 'all':
          localStorage.setItem(`${userId}.allPageSize`, size.toString())
          break
        case 'ranking':
          localStorage.setItem(`${userId}.rankingPageSize`, size.toString())
          break
        case 'collect':
          localStorage.setItem(`${userId}.collectPageSize`, size.toString())
          break
      }
    },
    // 修改分页处理方法
    handlePagination(val) {
      this.queryParams.pageNum = val.page
      this.queryParams.pageSize = val.limit
      this.savePageSizeToCache('all', val.limit)
      this.getTenderList(this.queryParams)
    },
    // 修改排行榜分页处理方法
    handleRankingPagination(val) {
      this.rankingParams.pageNum = val.page
      this.rankingParams.pageSize = val.limit
      this.savePageSizeToCache('ranking', val.limit)
      // 从完整数据中获取当前页数据
      const start = (val.page - 1) * val.limit
      const end = start + val.limit
      this.rankingList = this.rankingListAll.slice(start, end)
    },
    // 修改收藏分页处理方法
    handleCollectPagination(val) {
      this.collectParams.pageNum = val.page
      this.collectParams.pageSize = val.limit
      this.savePageSizeToCache('collect', val.limit)
      this.getTenderCollect(this.collectParams)
    }
  },
  destroyed() {
    // 页面销毁时清除所有记录的筛选条件
    this.allSearchRecord = {
      params: {},
      multiKeyword: false,
      keywords: [],
      selectTypes: ['title', 'content'],
      checkAllForecast: false,
      isIndeterminateForecast: false,
      selectedForecastTypes: [],
      checkAllNotice: false,
      isIndeterminateNotice: false,
      selectedNoticeTypes: [],
      checkAllResult: false,
      isIndeterminateResult: false,
      selectedResultTypes: [],
      checkAllCredit: false,
      isIndeterminateCredit: false,
      selectedCreditTypes: [],
      region: [],
      selectedRegionCount: 0,
      industry: [],
      selectedIndustryCount: 0,
      amount: '',
      selectedAmount: '金额'
    }
    this.commonSearchRecord = {
      params: {},
      multiKeyword: false,
      keywords: [],
      selectTypes: ['title', 'content'],
      region: [],
      selectedRegionCount: 0,
      industry: [],
      selectedIndustryCount: 0,
      amount: '',
      selectedAmount: '金额'
    }
    this.collectSearchRecord = {
      params: {}
    }
  }
}
</script>
<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
@import '~@/assets/styles/custom-tender.scss';
.tender-container {
  min-width: 1200px;
}
</style>
<style lang="scss">
.tender-info-popover {
  border-color: #2e73f3 !important;
  padding-top: 8px !important;
  padding-bottom: 8px !important;
  box-shadow: 0 1px 36px 0 rgba(0, 0, 0, 0.25);
  margin-top: 5px !important;
  border-radius: 10px !important;
  &.el-popper {
    .popper__arrow {
      width: 10px !important;
      height: 10px !important;
      transform: rotate(45deg) !important;
      background-color: #f2f4f6 !important;
      margin: 0 !important;
      &:after {
        display: none;
      }
    }
  }
  &.el-popper[x-placement^='top'] {
    .popper__arrow {
      border-top: 0 !important;
      border-right: 1px solid #2e73f3 !important;
      border-bottom: 1px solid #2e73f3 !important;
      border-left: 0 !important;
    }
  }
  &.el-popper[x-placement^='bottom'] {
    .popper__arrow {
      border-top: 1px solid #2e73f3 !important;
      border-right: 0 !important;
      border-bottom: 0 !important;
      border-left: 1px solid #2e73f3 !important;
    }
  }
  &.el-popper[x-placement^='left'] {
    .popper__arrow {
      border-top: 1px solid #2e73f3 !important;
      border-right: 1px solid #2e73f3 !important;
      border-bottom: 0 !important;
      border-left: 0 !important;
    }
  }
  &.el-popper[x-placement^='right'] {
    .popper__arrow {
      border-top: 0 !important;
      border-right: 0 !important;
      border-bottom: 1px solid #2e73f3 !important;
      border-left: 1px solid #2e73f3 !important;
    }
  }
}
.tender-info-popover.none {
  padding: 0 !important;
  min-width: 100px !important;
}
</style>
