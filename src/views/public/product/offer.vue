<template>
  <div>
    <el-dialog v-dialogDragBox :title="offerTitle" :visible.sync="offerOpen" :width="width" custom-class="offerView-dialog" :append-to-body="appendBody">
      <div @contextmenu.prevent>
        <div class="offerView-title">产品信息</div>
        <el-descriptions title="" :column="3" border style="margin-bottom: 20px" :labelStyle="{ width: 'calc(4em + 22px)' }" :contentStyle="{ width: `calc((${width} - 280px)/3)` }">
          <el-descriptions-item label="产品名称" :span="3">
            <div class="table-link pointer" @click="handleDetail">{{ info.productName }}</div>
          </el-descriptions-item>
          <el-descriptions-item label="产品规格" :span="3">{{ info.specs }}</el-descriptions-item>
          <el-descriptions-item label="产品图片" :span="3">
            <img :src="formatProductImg(info)" :alt="info.productName" class="table-img pointer" @click="handleImg(info)" />
          </el-descriptions-item>
          <el-descriptions-item label="最高报价">
            <span class="red">￥{{ data.max }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="最低报价">
            <span class="green">￥{{ data.min }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="平均报价">
            <span class="info">￥{{ data.avg }}</span>
          </el-descriptions-item>
        </el-descriptions>

        <div class="offerView-title">报价信息</div>
        <el-table :data="offerList" stripe style="width: 100%" @selection-change="handleCheck" ref="offerTable">
          <el-table-column type="selection" width="45" align="center" v-if="isOrder" />
          <el-table-column type="index" label="序号" align="center" />
          <el-table-column prop="companyName" label="公司名称" align="center">
            <template slot-scope="{ row }">
              <el-tooltip class="item" effect="dark" :content="`${row.companyName}(${!!row.isCertified ? '已认证企业' : '未认证企业'})`" placement="top">
                <div class="company-name" v-if="!!row.isCertified">
                  <i class="isCertified"></i>
                  <span class="link" @click="handleInfo(row)">{{ row.companyName }}</span>
                </div>
                <div class="company-name" v-else>
                  <i class="isCertified none"></i>
                  <span>{{ row.companyName }}</span>
                </div>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column prop="address" show-overflow-tooltip label="公司地址" align="center">
            <template slot-scope="{ row }">{{ removeHtmlTag(row.address, 300) }}</template>
          </el-table-column>
          <el-table-column prop="phone" label="联系电话" min-width="130" align="center">
            <template slot-scope="scope">
              <div class="inline-flex">
                <div class="table-phone pointer" v-if="!!scope.row.customer" @click="handleContact(scope.row)">
                  <i class="ssfont ss-diy-liaotian" />
                  <span>点我聊天</span>
                </div>
                <div class="table-phone pointer">
                  <i class="ssfont ss-diy-dianhua"></i>
                  <span>{{ scope.row.phone }}</span>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="name" label="所属报价" align="center" />
          <el-table-column prop="effectiveTime" label="有效时间" align="center">
            <template slot-scope="{ row }">{{ row.effectiveTime || '长期' }}</template>
          </el-table-column>
          <el-table-column prop="price" label="未税报价" align="center">
            <template slot-scope="{ row }">
              <span class="red">{{ row.method === 'ton' ? '¥' + row.tonPrice + '元/吨' : '¥' + row.price + '元/' + info.unit }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="taxPrice" label="含税报价" align="center">
            <template slot-scope="{ row }">
              <span class="red" v-if="row.method === 'one'">{{ row.taxPrice ? '¥' + row.taxPrice + '元/' + info.unit : '' }}</span>
              <span class="red" v-if="row.method === 'ton'">{{ row.tonTaxPrice ? '¥' + row.tonTaxPrice + '元/吨' : '' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="effectiveTime" label="最少供应" align="center">
            <template slot-scope="{ row }">{{ row.minQuantity + info.unit }}</template>
          </el-table-column>
          <el-table-column prop="publishRemark" label="报价备注" align="center" />
        </el-table>

        <div class="offerView-title" style="margin-top: 20px">历史报价</div>
        <el-table :data="historyOfferList" stripe style="width: 100%" @selection-change="handleCheck" ref="offerTable">
          <el-table-column type="index" label="序号" align="center" />
          <el-table-column prop="companyName" label="报价企业" align="center">
            <template slot-scope="{ row }">
              <el-tooltip class="item" effect="dark" :content="`${row.companyName}(${!!row.isCertified ? '已认证企业' : '未认证企业'})`" placement="top">
                <div class="company-name" v-if="!!row.isCertified">
                  <i class="isCertified"></i>
                  <span class="link" @click="handleInfo(row)">{{ row.companyName }}</span>
                </div>
                <div class="company-name" v-else>
                  <i class="isCertified none"></i>
                  <span>{{ row.companyName }}</span>
                </div>
              </el-tooltip>
            </template>
          </el-table-column>
          <!-- <el-table-column prop="address" show-overflow-tooltip label="公司地址" align="center" /> -->
          <el-table-column prop="phone" label="联系电话" min-width="130" align="center">
            <template slot-scope="scope">
              <div class="inline-flex">
                <div class="table-phone pointer" v-if="!!scope.row.customer" @click="handleContact(scope.row)">
                  <i class="ssfont ss-diy-liaotian" />
                  <span>点我聊天</span>
                </div>
                <div class="table-phone pointer">
                  <i class="ssfont ss-diy-dianhua"></i>
                  <span>{{ scope.row.phone }}</span>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="name" label="所属报价" align="center" />
          <el-table-column prop="effectiveTime" label="报价状态" align="center">
            <template slot-scope="{ row }">{{ row.effectiveTime || '长期' }}</template>
          </el-table-column>
          <el-table-column prop="price" label="未税报价" align="center">
            <template slot-scope="{ row }">
              <span class="red">{{ row.method === 'ton' ? '¥' + row.tonPrice + '元/吨' : '¥' + row.price + '元/' + info.unit }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="taxPrice" label="含税报价" align="center">
            <template slot-scope="{ row }">
              <span class="red" v-if="row.method === 'one'">{{ row.taxPrice ? '¥' + row.taxPrice + '元/' + info.unit : '' }}</span>
              <span class="red" v-if="row.method === 'ton'">{{ row.tonTaxPrice ? '¥' + row.tonTaxPrice + '元/吨' : '' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="publishRemark" label="报价备注" align="center" />
        </el-table>
      </div>
      <div slot="footer" v-if="isOrder">
        <!-- <button type="button" class="offerView-dialog-button" style="cursor: pointer;"
          @click="handleHistory(info)">查看历史报价</button> -->
        <button type="button" class="offerView-dialog-button" :class="{ primary: orderList.length === 1 }" :disabled="orderList.length !== 1" @click="handleContarct">生成合同</button>
      </div>
    </el-dialog>

    <!-- 生成订单 -->
    <el-dialog v-dialogDragBox title="生成订单" :visible.sync="orderOpen" width="1150px" class="custom-dialog offer-dialog" :before-close="handleCancel" :append-to-body="appendBody">
      <div @contextmenu.prevent>
        <div class="offer-title">
          <span>订单编号：{{ orderInfo.serial }}</span>
          <span>创建时间：{{ orderInfo.createTime }}</span>
        </div>
        <div class="offer-table">
          <el-form ref="orderForm" :model="orderInfo" :rules="orderRules" label-width="0">
            <div class="offer-table-title">{{ info.productName }}</div>
            <el-table ref="orderTable" stripe :data="orderInfo.list" row-key="id" style="width: 100%" class="custom-table custom-table-cell0">
              <el-table-column align="center" prop="companyName" label="供应商" show-overflow-tooltip></el-table-column>
              <el-table-column align="center" prop="price" label="产品报价">
                <template slot-scope="{ row }">
                  <span class="table-orange">{{ row.price ? '￥' + row.price : '' }}</span>
                </template>
              </el-table-column>
              <el-table-column align="center" prop="unit" label="单位" width="170" show-overflow-tooltip>
                <template slot-scope="scope">
                  <template v-if="scope.row.isEdit">
                    <el-form-item label-width="0" :prop="`list.${scope.$index}.unit`" :rules="orderRules.unit">
                      <el-select size="small" v-model="scope.row.unit" filterable allow-create default-first-option placeholder="请选择单位" style="width: 100%">
                        <el-option v-for="item in unitoptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                      </el-select>
                    </el-form-item>
                  </template>
                  <span v-else>{{ scope.row.unit }}</span>
                </template>
              </el-table-column>
              <el-table-column align="center" prop="quantity" label="采购数量" width="170">
                <template slot-scope="scope">
                  <el-form-item label-width="0" :prop="`list.${scope.$index}.quantity`" :rules="orderRules.quantity">
                    <el-input v-model="scope.row.quantity" size="small" placeholder="请输入采购数量" @change="getTotal" />
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column align="center" label="操作">
                <template slot-scope="scope">
                  <button type="button" class="table-btn danger" @click="handleOrderDelete(scope.$index)">删除</button>
                </template>
              </el-table-column>
            </el-table>
          </el-form>
        </div>
        <div class="offer-account" :key="key">
          <div class="inline-flex">
            <span class="inline-flex">
              共
              <b>1</b>
              件产品
            </span>
            <span class="inline-flex">
              <b>{{ orderInfo.list.length }}</b>
              家供应商
            </span>
          </div>
          <span class="inline-flex" style="margin-right: 0">
            订单总金额：
            <b>￥{{ orderInfo.total }}</b>
          </span>
        </div>
      </div>
      <div slot="footer">
        <button type="button" class="custom-dialog-btn" @click="handleCancel">取消</button>
        <template v-if="offerList.length">
          <button type="button" class="custom-dialog-btn primary" @click="handleOrderSubmit">确认订单</button>
        </template>
      </div>
    </el-dialog>

    <!-- 图片预览 -->
    <el-dialog v-dialogDragBox :visible.sync="imgOpen" width="750px" :append-to-body="appendBody">
      <div style="text-align: center" @contextmenu.prevent>
        <img style="max-width: 100%" :src="productImg" :alt="productName" />
      </div>
    </el-dialog>

    <product-dialog ref="productInfo"></product-dialog>

    <!--  生成合同  -->
    <offer-contract-dialog ref="contractDialog"></offer-contract-dialog>

    <!--  企业信息  -->
    <el-dialog v-dialogDragBox title="企业信息" :visible.sync="companyOpen" width="1150px" class="custom-dialog">
      <div style="padding: 0 20px">
        <div class="photo" v-if="companyInfo.siteImg.length">
          <div class="photo-title">场地图片</div>
          <div class="photo-list">
            <div class="photo-list-item" v-for="(item, index) in companyInfo.siteImg" :key="index">
              <el-image :src="item" :preview-src-list="companyInfo.siteImglist">
                <div slot="error" class="image-slot">
                  <i class="el-icon-picture-outline"></i>
                </div>
              </el-image>
            </div>
          </div>
        </div>
        <div class="photo" v-if="companyInfo.deviceImg.length">
          <div class="photo-title">设备照片</div>
          <div class="photo-list">
            <div class="photo-list-item" v-for="(item, index) in companyInfo.deviceImg" :key="index">
              <el-image :src="item" :preview-src-list="companyInfo.deviceImglist">
                <div slot="error" class="image-slot">
                  <i class="el-icon-picture-outline"></i>
                </div>
              </el-image>
            </div>
          </div>
        </div>
        <div class="photo" v-if="companyInfo.process.length">
          <div class="photo-title">工艺照片/视频</div>
          <div class="photo-list">
            <div class="photo-list-item" v-for="(item, index) in companyInfo.process" :key="index">
              <video class="item-video" :src="item" type="video/mp4" controls muted poster="~@/assets/images/logo.gif" v-if="item.indexOf('mp4') !== -1"></video>
              <el-image :src="item" :preview-src-list="companyInfo.processlist" v-else>
                <div slot="error" class="image-slot">
                  <i class="el-icon-picture-outline"></i>
                </div>
              </el-image>
            </div>
          </div>
        </div>
        <div class="photo" v-if="companyInfo.certImg.length">
          <div class="photo-title">证书</div>
          <div class="photo-list">
            <div class="photo-list-item" v-for="(item, index) in companyInfo.certImg" :key="index">
              <el-image :src="item" :preview-src-list="companyInfo.certImglist">
                <div slot="error" class="image-slot">
                  <i class="el-icon-picture-outline"></i>
                </div>
              </el-image>
            </div>
          </div>
        </div>
      </div>
      <div slot="footer">
        <button type="button" class="custom-dialog-btn primary" @click="companyOpen = false">关闭</button>
      </div>
    </el-dialog>
    <chat ref="chat" :showBadge="false" />

    <!-- 历史报价 -->
    <!-- <el-dialog v-dialogDragBox title="产品报价查看" :visible.sync="historyOpen" :width="width" custom-class="offerView-dialog"
      :append-to-body="appendBody">
      <div @contextmenu.prevent>
        <div class="offerView-title">产品信息</div>
        <el-descriptions title="" :column="3" border style="margin-bottom: 20px"
          :labelStyle="{ width: 'calc(4em + 22px)' }" :contentStyle="{ width: `calc((${width} - 280px)/3)` }">
          <el-descriptions-item label="产品名称" :span="3">
            <div class="table-link pointer" @click="handleDetail">{{ info.productName }}</div>
          </el-descriptions-item>
          <el-descriptions-item label="产品规格" :span="3">{{ info.specs }}</el-descriptions-item>
          <el-descriptions-item label="产品图片" :span="3">
            <img :src="formatProductImg(info)" :alt="info.productName" class="table-img pointer"
              @click="handleImg(info)" />
          </el-descriptions-item>
          <el-descriptions-item label="最高报价">
            <span class="red">￥{{ data.max }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="最低报价">
            <span class="green">￥{{ data.min }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="平均报价">
            <span class="info">￥{{ data.avg }}</span>
          </el-descriptions-item>
        </el-descriptions>

        <div class="offerView-title">历史报价</div>
        <el-table :data="historyOfferList" stripe style="width: 100%" @selection-change="handleCheck" ref="offerTable">
          <el-table-column type="index" label="序号" align="center" />
          <el-table-column prop="companyName" label="报价企业" align="center">
            <template slot-scope="{ row }">
              <el-tooltip class="item" effect="dark"
                :content="`${row.companyName}(${!!row.isCertified ? '已认证企业' : '未认证企业'})`" placement="top">
                <div class="company-name" v-if="!!row.isCertified">
                  <i class="isCertified"></i>
                  <span class="link" @click="handleInfo(row)">{{ row.companyName }}</span>
                </div>
                <div class="company-name" v-else>
                  <i class="isCertified none"></i>
                  <span>{{ row.companyName }}</span>
                </div>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column prop="phone" label="联系电话" min-width="130" align="center">
            <template slot-scope="scope">
              <div class="inline-flex">
                <div class="table-phone pointer" v-if="!!scope.row.customer" @click="handleContact(scope.row)">
                  <i class="ssfont ss-diy-liaotian" />
                  <span>点我聊天</span>
                </div>
                <div class="table-phone pointer">
                  <i class="ssfont ss-diy-dianhua"></i>
                  <span>{{ scope.row.phone }}</span>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="name" label="报价日期" align="center" />
          <el-table-column prop="name" label="所属报价" align="center" />
          <el-table-column prop="effectiveTime" label="报价状态" align="center">
            <template slot-scope="{ row }">{{ row.effectiveTime || '长期' }}</template>
          </el-table-column>
          <el-table-column prop="price" label="未税报价" align="center">
            <template slot-scope="{ row }">
              <span class="red">{{ row.method === 'ton' ? '¥' + row.tonPrice + '元/吨' : '¥' + row.price + '元/' + info.unit
              }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="taxPrice" label="含税报价" align="center">
            <template slot-scope="{ row }">
              <span class="red" v-if="row.method === 'one'">{{ row.taxPrice ? '¥' + row.taxPrice + '元/' + info.unit : ''
              }}</span>
              <span class="red" v-if="row.method === 'ton'">{{ row.tonTaxPrice ? '¥' + row.tonTaxPrice + '元/吨' : ''
              }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="publishRemark" label="报价备注" align="center" />
        </el-table>
      </div>
      <div slot="footer">
        <button type="button" class="offerView-dialog-button primary" @click="historyOpen = false">关 闭</button>
      </div>
    </el-dialog> -->
  </div>
</template>

<script>
import { getToken } from '@/utils/auth'
import { gygs } from '@/api/houtai/formula'
import ProductDialog from './dialog'
import { isNumber, isNumberLength } from '@/utils/validate'
import { purchasingOrderNumber, purchasingOrderSave } from '@/api/purchase'
import offerContractDialog from '@/views/purchase/demandForMe/offer'
import { supplier } from '@/api/system/user'
import Chat from '@/components/Chat/index'
import { listHistoryList } from '@/api/houtai/formula'
import { removeHtmlTag } from '@/utils'

export default {
  props: {
    width: {
      type: String,
      default: '1150px'
    },
    appendBody: {
      type: Boolean,
      default: false
    }
  },
  components: { Chat, offerContractDialog, ProductDialog },
  data() {
    return {
      imgUrl: 'http://www.ziyouke.net/prod-api',
      imgOpen: false,
      productImg: undefined,
      productName: undefined,
      historyOfferList: [],
      offerList: [],
      offerOpen: false,
      offerTitle: undefined,
      info: {},
      data: {},
      isOrder: false,
      orderList: [],
      orderInfo: { list: [] },
      orderOpen: false,
      orderRules: {
        quantity: [
          { required: true, message: '请输入采购数量', trigger: 'blur' },
          { validator: isNumber, message: '请输入正确的数量', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 3), message: '只可以填写三位小数', trigger: 'blur' }
        ],
        unit: [{ required: true, message: '请选择单位', trigger: 'change' }]
      },
      unitoptions: [
        { value: '吨', label: '吨' },
        { value: '千克', label: '千克' },
        { value: '个', label: '个' },
        { value: '件', label: '件' },
        { value: '套', label: '套' },
        { value: '米', label: '米' },
        { value: '支', label: '支' }
      ],
      key: 1,
      companyOpen: false,
      companyInfo: {
        siteImg: [],
        siteImglist: [],
        deviceImg: [],
        deviceImglist: [],
        certImg: [],
        certImglist: [],
        process: [],
        processlist: []
      },
      historyOpen: false,
      companyData: {},
      historyList: []
    }
  },
  computed: {
    // 默认头像
    defaultAvatar() {
      return require('@/assets/images/Avatar.png')
    }
  },
  methods: {
    removeHtmlTag,
    handleView(item, val) {
      this.isOrder = true
      this.info = { ...item }
      const isToken = !!getToken()
      if (isToken) {
        const productId = item.id
        const productName = item.productName
        gygs({ productId }).then(async res => {
          if (res.code === 200) {
            this.data = res.data
            if (res.data.quotes) {
              this.offerList = res.data.quotes.filter(item => !(item.effectiveTime && new Date(item.effectiveTime) < new Date()))
              this.historyOfferList = res.data.quotes.filter(item => item.effectiveTime && new Date(item.effectiveTime) < new Date())
              this.offerTitle = productName + '产品报价查看'
              this.offerOpen = true
            } else {
              this.$message.warning('暂无报价信息')
            }
          } else {
            this.$message.error(res.msg)
          }
        })
      }
    },
    // 格式化日期(报价编号中获取日期)
    formatDate(value) {
      const date = value.split('-')[0]
      return date.slice(0, 4) + '-' + date.slice(4, 6) + '-' + date.slice(-2)
    },
    // 图片放大
    handleImg(item) {
      this.productImg = this.formatProductImg(item)
      this.productName = item.productName
      this.imgOpen = true
    },
    // 产品详情
    handleDetail() {
      this.$refs.productInfo.handleView(this.info)
    },
    // 选择供应商
    handleCheck(selection) {
      selection.map(item => {
        item.quantity = ''
        item.unit = this.info.unit || ''
        item.isEdit = this.info.unit ? false : true
      })
      this.orderList = [...selection]
    },
    //  生成订单
    handleOrder() {
      purchasingOrderNumber().then(res => {
        if (res.code === 200) {
          this.orderInfo = res.data
          this.orderInfo.list = JSON.parse(JSON.stringify(this.orderList))
          this.orderInfo.total = 0
          this.orderOpen = true
          this.key = Math.random()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 删除供应商
    handleOrderDelete(index) {
      const list = this.orderInfo.list
      if (list.length === 1) {
        this.$message.error('至少需要保留一个供应商')
        return
      }
      list.splice(index, 1)
      this.getTotal()
    },
    // 获取订单金额
    getTotal() {
      const data = this.orderInfo.list
      let total = 0
      data.map(item => {
        total += Number(item.price) * Number(item.quantity)
      })
      this.orderInfo.total = parseFloat(total.toFixed(5))
      this.key = Math.random()
    },
    // 取消
    handleCancel() {
      this.orderOpen = false
      this.orderList = []
      this.$refs.offerTable.clearSelection()
    },
    // 确认订单
    handleOrderSubmit() {
      this.$refs.orderForm.validate(valid => {
        if (valid) {
          let products = []
          this.orderInfo.list.map(item => {
            const obj = {
              productName: this.info.productName,
              unit: item.unit,
              amount: item.price,
              productId: this.info.id,
              publishWay: 'common',
              quantity: item.quantity,
              source: 'common',
              supplierId: item.companyId
            }
            products.push(obj)
          })
          const data = { orderId: this.orderInfo.id, products }
          purchasingOrderSave(data).then(res => {
            if ((res.code = 200)) {
              this.$message.success('成功生成订单')
              this.handleCancel()
            } else {
              this.$message.error(res.msg)
            }
          })
        }
      })
    },
    // 生成合同
    handleContarct() {
      const info = {
        maxNum: *********,
        source: 'common',
        productId: this.info.id,
        productName: this.info.productName,
        productCode: this.info.productCode,
        specs: this.info.specs,
        model: this.info.model,
        unit: this.info.unit,
        quantity: 0,
        needQuantity: 0,
        listId: -1,
        remark: this.info.remark,
        amount: this.orderList[0].taxPrice || this.orderList[0].price || 0,
        originAmount: this.orderList[0].taxPrice || this.orderList[0].price || 0,
        replyUnit: this.orderList[0].method === 'ton' ? '吨' : this.orderList[0].unit || this.info.unit,
        sjNum: this.orderList[0].minQuantity || 0,
        replyRemark: '',
        isIncludingTax: this.orderList[0].isIncludingTax
      }
      const sellerUser = { ...this.orderList[0] }
      this.$refs.contractDialog.handleGetContract(sellerUser, info)
      this.offerOpen = false
    },
    // 供应商信息
    handleInfo(row) {
      this.companyInfo = {
        siteImg: [],
        siteImglist: [],
        deviceImg: [],
        deviceImglist: [],
        certImg: [],
        certImglist: [],
        process: [],
        processlist: []
      }
      const id = row.companyId
      supplier({ id }).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          const { supplier } = data
          const siteImg = supplier.certImg_oss ? supplier.certImg_oss.split(',') : supplier.siteImg ? supplier.siteImg.split(',').map(item => this.imgPath + item) : []
          if (siteImg.length > 0) {
            this.companyInfo.siteImg = siteImg
            this.companyInfo.siteImglist = siteImg
          }
          const deviceImg = supplier.deviceImg_oss ? supplier.deviceImg_oss.split(',') : supplier.deviceImg ? supplier.deviceImg.split(',').map(item => this.imgPath + item) : []
          if (deviceImg.length > 0) {
            this.companyInfo.deviceImg = deviceImg
            this.companyInfo.deviceImglist = deviceImg
          }
          const certImg = supplier.certImg_oss ? supplier.certImg_oss.split(',') : supplier.certImg ? supplier.certImg.split(',').map(item => this.imgPath + item) : []
          if (certImg.length > 0) {
            this.companyInfo.certImg = certImg
            this.companyInfo.certImglist = certImg
          }
          const process = supplier.process_oss ? supplier.process_oss.split(',') : supplier.process ? supplier.process.split(',').map(item => this.imgPath + item) : []
          if (process.length > 0) {
            this.companyInfo.process = process
            this.companyInfo.processlist = process.filter(item => item.indexOf('.mp4') !== -1)
          }
          this.companyOpen = true
        } else this.$message.error(msg)
      })
    },
    // 点击聊天
    handleContact(row) {
      const { customer } = row
      const data = {
        userId: customer.userId,
        nick: customer.nickName,
        avatar: this.imgPath + customer.avatar || this.defaultAvatar
      }
      this.$refs.chat.send(data)
      this.$nextTick(() => {
        const product = JSON.stringify({ doType: 'sendProduct', ...this.info })
        this.$refs.chat.handleSend(product)
      })
    }
    // 历史报价
    // handleHistory(row) {
    //   this.companyData = row
    //   const query = { quoteId: row.id, days: 28 }
    //   listHistoryList(query).then(res => {
    //     if (res.data) {
    //       res.data.map(item => {
    //         item.createTime = this.parseTime(item.createTime, '{y}-{m}-{d} {h}:{i}:{s}')
    //       })
    //       this.historyList = res.data
    //       console.log(this.historyList)
    //     }
    //   })
    //   this.historyOpen = true
    // }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';

.red {
  font-size: 16px;
  color: #ec2454;
}

.green {
  font-size: 16px;
  color: #67c23a;
}

.info {
  font-size: 16px;
  color: #409eff;
}

.offerView-title {
  font-size: 16px;
  font-weight: bold;
  color: #333333;
  margin-bottom: 15px;
}

.table-img {
  display: inline-flex;
  width: 42px;
  height: 42px;
  border-radius: 5px;
  border: 1px solid #ededed;
}

.table-link {
  color: #409eff;

  &:hover {
    text-decoration: underline;
  }
}

::v-deep .offerView-dialog {
  border-radius: 5px;
  overflow: hidden;

  .el-dialog__header {
    height: 50px;
    background: #eef0f8;
    padding-top: 0;
    padding-bottom: 0;

    .el-dialog__title {
      color: #666666;
      line-height: 50px;
    }
  }

  .el-dialog__body {
    padding: 20px;

    .el-table {
      border: 1px solid #ebeef5;
      border-bottom: 0;
      border-radius: 5px;
      font-size: 14px;
      font-weight: 500;
      color: #333333;

      .el-table__header-wrapper {
        th.el-table__cell {
          background-color: #f8f9fb;
          font-size: 12px;
          color: #999999;
          font-weight: 400;
        }
      }

      //.cell {
      //  display: inline-flex;
      //  justify-content: center;
      //}
    }

    .table-phone {
      text-wrap: nowrap;
      display: inline-flex;
      flex-wrap: nowrap;
      align-items: center;
      justify-content: center;
      width: 30px;
      height: 30px;
      border-radius: 30px;
      background-color: #d7e5ff;
      color: #2e73f3;

      span {
        display: none;
      }

      &:hover {
        width: auto;
        padding: 0 10px;
        background-color: #2e73f3;
        color: #ffffff;

        span {
          display: inline-block;
        }
      }
    }

    .table-phone + .table-phone {
      margin-left: 10px;
    }

    .el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell {
      background-color: #f0f3f9;
    }
  }

  &-button {
    width: 269px;
    height: 50px;
    line-height: 50px;
    text-align: center;
    margin-left: 10px;
    border: 1px solid #cbd6e2;
    border-radius: 5px;
    font-size: 16px;
    color: #999999;
    cursor: no-drop;

    &.primary {
      background-color: #2e73f3;
      color: #ffffff;
      cursor: pointer;
    }

    &:hover {
      opacity: 0.8;
    }
  }
}

.offer-dialog ::v-deep {
  .el-dialog__body {
    padding: 0 20px !important;
  }

  .offer-change {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    line-height: 28px;

    .offer-total {
      font-size: 12px;
      color: #999999;

      b {
        color: $blue;
        font-weight: 500;
        font-size: 16px;
        margin: 0 5px;
      }
    }

    .option-title {
      font-size: 12px;
      color: #999999;
    }

    .option-item {
      display: inline-block;
      color: $info;
      border: 1px solid #cbd6e2;
      border-radius: 5px;
      margin-left: 15px;
      padding: 0 15px;
      cursor: pointer;

      &:hover,
      &.active {
        background-color: $blue;
        color: $white;
        border-color: $blue;
      }
    }
  }

  .offer-table {
    border: 1px solid #d1dffa;
    border-radius: 5px;
    margin-bottom: 15px;
    overflow: hidden;

    &-title {
      font-size: 14px;
      font-weight: 500;
      color: $font;
      padding: 0 20px;
      line-height: 38px;
      background-color: #ecf3ff;
    }

    .custom-table {
      border: 0;
      border-radius: 0;

      .el-form-item {
        margin-top: 10px !important;
        margin-bottom: 10px !important;

        .el-form-item__error {
          top: 95%;
          padding-top: 0;
        }
      }
    }

    &-total {
      width: 100%;
      line-height: 48px;
      font-size: 12px;
      color: #999999;
      padding: 0 20px;
      background-color: #f8f9fb;

      b {
        font-size: 16px;
        font-weight: 500;
        color: $blue;
        margin: 0 15px;
      }
    }
  }

  .offer-title {
    font-size: 14px;
    color: $info;
    padding: 18px 0;

    span {
      margin-right: 120px;
    }
  }

  .offer-account {
    display: flex;
    justify-content: space-between;
    align-items: center;
    line-height: 48px;
    padding: 0 20px;
    background-color: #fff5f5;
    border: 1px solid #ec4545;
    border-radius: 5px;
    margin-bottom: 10px;
    font-size: 14px;
    color: $info;

    span {
      margin-right: 30px;
    }

    b {
      font-size: 18px;
      font-weight: 500;
      color: #f35d09;
      margin: 0 10px;
    }
  }
}

.company-name {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;

  .link {
    color: $blue;
    cursor: pointer;

    &:hover {
      text-decoration: underline;
    }
  }

  .isCertified {
    float: left;
    display: inline-block;
    width: 20px;
    height: 20px;
    background: url('~@/assets/images/authenticationY.png') left center no-repeat;
    background-size: 100% 100%;
    border-radius: 0 5px 5px 0;
    color: $white;
    font-style: normal;
    font-size: 12px;
    margin-right: 5px;

    &.none {
      background: url('~@/assets/images/authenticationN.png') no-repeat;
      background-size: auto 100%;
    }
  }
}

.photo {
  padding: 10px 0;
  overflow: hidden;

  &-title {
    font-size: 14px;
    line-height: 22px;
    color: #666666;
    margin-bottom: 10px;
  }

  &-list {
    &-item {
      width: 145px;
      height: 106px;
      position: relative;
      float: left;
      margin-right: 10px;
      margin-bottom: 10px;

      ::v-deep .el-image {
        width: 100%;
        height: 100%;

        .image-slot {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 100%;
          height: 100%;
          background: #f5f7fa;
          color: #909399;
          font-size: 30px;
        }
      }

      .item-video {
        width: 100%;
        height: 100%;
      }
    }
  }
}
</style>
