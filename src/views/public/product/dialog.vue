<template>
  <div>
    <el-dialog v-dialogDragBox :visible.sync="open" :width="width" class="custom-dialog" :show-close="false" :append-to-body="appendBody" :modal-append-to-body="false" :before-close="handleClose">
      <div @contextmenu.prevent>
        <div class="product-view-tab">
          <div class="tab-box">
            <div class="tab-item" :class="{ 'tab-active': tabName === 'view' }" data-name="view" @click="handleTab($event)">产品详情</div>
            <div class="tab-item" :class="{ 'tab-active': tabName === 'paper' }" data-name="paper" @click="handleTab($event)">产品图纸</div>
            <div class="tab-item" :class="{ 'tab-active': tabName === 'report' }" data-name="report" @click="handleTab($event)">检测报告</div>
          </div>
          <i class="el-icon-close tab-close pointer" @click="open = !open"></i>
        </div>
        <div class="product-view-box" v-show="tabName === 'view'">
          <div class="product-view">
            <div class="product-img-box" v-if="imgUrlList.length">
              <div class="product-img-show">
                <img :src="mainImgUrl" />
              </div>
              <div class="product-img-list">
                <i class="el-icon-arrow-left product-img-btn" :class="{ 'product-img-none': imgActiveIndex === 0 }" @click="imgLeft()"></i>
                <ul class="product-img-ul">
                  <li v-for="(item, index) in imgUrlList" :key="index" class="product-img-li" :class="{ 'product-img-active': index === imgActiveIndex }" :style="imgStyle" @click="changeImg(item, index)">
                    <img :src="item" />
                  </li>
                </ul>
                <i class="el-icon-arrow-right product-img-btn" :class="{ 'product-img-none': imgActiveIndex === imgUrlList.length - 1 }" @click="imgRight()"></i>
              </div>
            </div>
            <el-descriptions :column="1" border labelClassName="product-desc-label" content-class-name="product-desc-content">
              <el-descriptions-item>
                <template slot="label">产品名称</template>
                {{ info.productName }}
                <span style="font-size: 12px; color: #999; margin-left: 5px" v-if="info.formerName">({{ info.formerName }})</span>
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label">产品编码</template>
                {{ info.productCode }}
              </el-descriptions-item>
              <el-descriptions-item v-if="info.materialCode">
                <template slot="label">物料编码</template>
                {{ info.materialCode }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label">产品规格</template>
                {{ info.specs || '未标注' }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label">产品型号</template>
                {{ info.model || '未标注' }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label">执行标准</template>
                <span :style="{ color: info.standard ? '' : '#999', textDecoration: info.standard ? '' : 'line-through' }">{{ info.standard || '未标注' }}</span>
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label">行业分类</template>
                {{ info.industry || '未标注' }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label">产品材质</template>
                {{ info.materialQuality || '未标注' }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label">产品等级</template>
                {{ info.level || '未标注' }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label">表面处理</template>
                {{ info.surface || '未标注' }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label">产品单位</template>
                {{ info.unit || '未标注' }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label">产品重量</template>
                <span :style="{ color: info.weight ? '' : '#999', textDecoration: info.weight ? '' : 'line-through' }">{{ info.weight ? info.weight + 'kg' : '未标注' }}</span>
              </el-descriptions-item>
              <el-descriptions-item v-if="info.remark">
                <template slot="label">产品备注</template>
                {{ info.remark }}
              </el-descriptions-item>
              <el-descriptions-item v-if="info.brand">
                <template slot="label">采购商品牌</template>
                <div class="brand">{{ info.brand }}</div>
              </el-descriptions-item>
            </el-descriptions>
          </div>
          <div class="factory-box" v-if="scrollList.length">
            <div class="factory-title"><span>工艺视频</span></div>
            <div class="factory-img">
              <ul class="factory-img-ul" ref="factory">
                <video v-for="(item, index) in scrollList" :key="index" class="factory-img-li" :src="imgPath + item" type="video/mp4" controls muted poster="../../../../public/imgs/logo.gif"></video>
              </ul>
            </div>
          </div>
        </div>
        <div class="product-view-box" style="padding-top: 20px" v-show="tabName === 'paper'">
          <template v-if="info.draw">
            <template v-if="info.draw.includes('.pdf')">
              <div style="display: flex; justify-content: space-between; align-items: center; padding-bottom: 10px" v-if="pdfCount > 1">
                <el-button type="primary" size="small" @click="pdfCurrent--" :disabled="pdfCurrent < 2">
                  <i class="el-icon-arrow-left"></i>
                  上一页
                </el-button>
                <span>{{ pdfCurrent }} / {{ pdfCount }}</span>
                <el-button type="primary" size="small" @click="pdfCurrent++" :disabled="pdfCurrent >= pdfCount">
                  下一页
                  <i class="el-icon-arrow-right"></i>
                </el-button>
              </div>
              <div class="page-pdf" v-if="draw && typeof draw === 'object'">
                <Pdf ref="drawPdf" :src="draw" :page="pdfCurrent" :key="`draw-${info.productCode || ''}-${pdfKey}`" @num-pages="pdfCount = $event" @error="handlePdfError" />
              </div>
              <el-empty v-else description="PDF 文件加载失败" />
            </template>
            <template v-else>
              <div style="text-align: center">
                <img :src="info.draw" style="max-width: 100%" />
              </div>
            </template>
          </template>
          <el-empty :image-size="300" v-else />
        </div>
        <div class="product-view-box" style="padding-top: 20px" v-show="tabName === 'report'">
          <template v-if="info.report">
            <template v-if="info.report.includes('.pdf')">
              <div style="display: flex; justify-content: space-between; align-items: center; padding-bottom: 10px" v-if="pdfCount > 1">
                <el-button type="primary" size="small" @click="pdfCurrent--" :disabled="pdfCurrent < 2">
                  <i class="el-icon-arrow-left"></i>
                  上一页
                </el-button>
                <span>{{ pdfCurrent }} / {{ pdfCount }}</span>
                <el-button type="primary" size="small" @click="pdfCurrent++" :disabled="pdfCurrent >= pdfCount">
                  下一页
                  <i class="el-icon-arrow-right"></i>
                </el-button>
              </div>
              <div class="page-pdf" v-if="report && typeof report === 'object'">
                <Pdf ref="reportPdf" :src="report" :page="pdfCurrent" :key="`report-${info.productCode || ''}-${pdfKey}`" @num-pages="pdfCount = $event" @error="handlePdfError" />
              </div>
              <el-empty v-else description="PDF 文件加载失败" />
            </template>
            <template v-else>
              <div style="text-align: center">
                <img :src="info.report" style="max-width: 100%" />
              </div>
            </template>
          </template>
          <el-empty :image-size="300" v-else />
        </div>
      </div>
    </el-dialog>

    <!-- 图片预览 -->
    <el-dialog v-dialogDragBox :visible.sync="imgOpen" width="750px" :append-to-body="appendBody">
      <div style="text-align: center" @contextmenu.prevent>
        <img style="max-width: 100%" :src="productImg" :alt="productName" />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import pdf from 'vue-pdf'

export default {
  props: {
    width: {
      type: String,
      default: '1040px'
    },
    appendBody: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      info: {},
      open: false,
      tabName: 'view',
      // 产品图片切换
      mainImgUrl: undefined,
      imgUrlList: [],
      imgActiveIndex: 0,
      imgDistance: 0,
      // 工艺视频
      scrollList: [],
      pdfCurrent: 1,
      pdfCount: 0,
      pdfKey: 0, // 用于强制PDF组件重新渲染
      // 图片预览
      imgOpen: false,
      productImg: undefined,
      productName: undefined,
      draw: '',
      report: ''
    }
  },
  computed: {
    imgStyle() {
      return {
        transform: `translate3d(${this.imgDistance}px, 0, 0)`
      }
    }
  },
  methods: {
    // 查看详情
    handleView(item, val) {
      const info = { ...item }
      this.imgActiveIndex = 0
      this.imgDistance = 0
      const arr = info.diagram_oss ? [info.diagram_oss] : info.diagram ? [this.imgPath + info.diagram] : []
      const pics = info.picture1_oss ? info.picture1_oss.split(',') : info.picture1 ? info.picture1.split(',').map(item => this.imgPath + item) : []
      this.mainImgUrl = pics[0] || arr[0]
      this.imgUrlList = [...pics, ...arr]
      info.draw = info.draw_oss || (info.draw ? this.imgPath + info.draw.replace('https://www.ziyouke.net', '') : '')
      info.report = info.report_oss || (info.report ? this.imgPath + info.report.replace('https://www.ziyouke.net', '') : '')

      // 重置PDF相关状态
      this.pdfCurrent = 1
      this.pdfCount = 0
      this.draw = ''
      this.report = ''
      this.pdfKey = Date.now() // 更新key以强制组件重新渲染

      // 延迟创建PDF loading task，确保DOM更新完成
      this.$nextTick(() => {
        if (info.draw) {
          try {
            const drawTask = pdf.createLoadingTask({
              url: info.draw,
              cMapUrl: 'https://cdn.jsdelivr.net/npm/pdfjs-dist@2.6.347/cmaps/',
              cMapPacked: true
            })
            // 确保 createLoadingTask 返回了有效的 Promise
            if (drawTask && typeof drawTask.promise === 'object') {
              this.draw = drawTask
            } else {
              console.error('PDF draw task creation failed:', info.draw)
              this.draw = ''
            }
          } catch (error) {
            console.error('Error creating PDF draw task:', error)
            this.draw = ''
          }
        }
        if (info.report) {
          try {
            const reportTask = pdf.createLoadingTask({
              url: info.report,
              cMapUrl: 'https://cdn.jsdelivr.net/npm/pdfjs-dist@2.6.347/cmaps/',
              cMapPacked: true
            })
            // 确保 createLoadingTask 返回了有效的 Promise
            if (reportTask && typeof reportTask.promise === 'object') {
              this.report = reportTask
            } else {
              console.error('PDF report task creation failed:', info.report)
              this.report = ''
            }
          } catch (error) {
            console.error('Error creating PDF report task:', error)
            this.report = ''
          }
        }
      })

      this.scrollList = info.technology ? info.technology.split(',') : []
      this.tabName = val ? val : 'view'
      this.info = info
      this.open = true
    },
    // tab切换
    handleTab(e) {
      const name = e.target.dataset.name
      this.tabName = name
      if (name === 'view') {
        this.imgActiveIndex = 0
        this.imgDistance = 0
        this.mainImgUrl = this.imgUrlList[0]
      }
    },
    // 改变图片显示
    changeImg(item, idx) {
      this.mainImgUrl = item
      this.imgActiveIndex = idx
      if (idx < 4) {
        let index = 0
        const temp = window.setInterval(() => {
          if (index < 25) {
            if (this.imgDistance < 0) this.imgDistance += 2
            index++
            return false
          } else {
            window.clearInterval(temp)
          }
        }, 10)
      }
    },
    // 左滚动图片
    imgLeft() {
      let index
      if (this.imgActiveIndex > 0) {
        this.imgActiveIndex--
        this.imgUrlList.forEach((item, index) => {
          if (this.imgActiveIndex === index) {
            this.mainImgUrl = item
          }
        })
      }
      if (this.imgActiveIndex >= 4) {
        index = 0
        const temp = window.setInterval(() => {
          if (index < 25) {
            this.imgDistance += 2
            index++
            return false
          } else {
            window.clearInterval(temp)
          }
        }, 10)
      } else {
        index = 0
        const temp = window.setInterval(() => {
          if (index < 25) {
            if (this.imgDistance < 0) this.imgDistance += 2
            index++
            return false
          } else {
            window.clearInterval(temp)
          }
        }, 10)
      }
    },
    // 右滚动图片
    imgRight() {
      if (this.imgActiveIndex < this.imgUrlList.length - 1) {
        this.imgActiveIndex++
        this.imgUrlList.forEach((item, index) => {
          if (this.imgActiveIndex === index) {
            this.mainImgUrl = item
          }
        })
        if (this.imgActiveIndex >= 5 && this.imgDistance > -50 * (this.imgActiveIndex - 4)) {
          let index = 0
          const temp = window.setInterval(() => {
            if (index < 25) {
              this.imgDistance -= 2
              index++
              return false
            } else {
              window.clearInterval(temp)
            }
          }, 10)
        }
      }
    },
    // 图片预览
    handleImgView(row) {
      if (row.hasOwnProperty('product')) {
        this.productImg = this.formatProductImg(row.product)
        this.productName = row.product.productName
        this.imgOpen = true
      } else {
        this.productImg = this.formatProductImg(row)
        this.productName = row.productName
      }
      this.imgOpen = true
    },
    // 关闭弹窗
    handleClose() {
      // 重置PDF状态，防止下次打开时出现问题
      this.resetPdfState()
      this.$emit('callback')
      this.open = false
    },
    // PDF 错误处理
    handlePdfError(error) {
      console.error('PDF loading error:', error)
      this.$message.error('PDF 文件加载失败，请检查文件是否正确')
    },
    // 新增：重置PDF状态的方法
    resetPdfState() {
      this.pdfCurrent = 1
      this.pdfCount = 0
      // 安全地重置 PDF loading tasks
      if (this.draw && typeof this.draw === 'object') {
        try {
          if (this.draw.destroy && typeof this.draw.destroy === 'function') {
            this.draw.destroy()
          }
        } catch (error) {
          console.warn('Error destroying draw PDF task:', error)
        }
      }
      this.draw = ''
      if (this.report && typeof this.report === 'object') {
        try {
          if (this.report.destroy && typeof this.report.destroy === 'function') {
            this.report.destroy()
          }
        } catch (error) {
          console.warn('Error destroying report PDF task:', error)
        }
      }
      this.report = ''
      this.pdfKey = 0 // 重置key
      // 安全地销毁 PDF 组件实例
      this.$nextTick(() => {
        try {
          if (this.$refs.drawPdf && this.$refs.drawPdf.$destroy) {
            this.$refs.drawPdf.$destroy()
          }
        } catch (error) {
          console.warn('Error destroying drawPdf component:', error)
        }
        try {
          if (this.$refs.reportPdf && this.$refs.reportPdf.$destroy) {
            this.$refs.reportPdf.$destroy()
          }
        } catch (error) {
          console.warn('Error destroying reportPdf component:', error)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.custom-dialog ::v-deep {
  .el-dialog {
    border-radius: 5px;
    overflow: hidden;
    .el-dialog__header {
      display: none !important;
    }
    .el-dialog__body {
      padding: 0 !important;
    }
  }
}
.el-descriptions ::v-deep {
  width: 618px;
  .product-desc-label {
    width: 135px !important;
    text-align: center;
  }
  .product-desc-content {
    padding-left: 85px;
    .brand {
      color: #7f4d03;
      display: inline-block;
      border: 1px solid #fdcc8b;
      background: linear-gradient(102deg, #fde4af 0%, #f6c576 100%);
      border-radius: 5px;
      height: 24px;
      padding: 0 15px;
    }
  }
}
@media (max-width: 1199px) {
  .el-descriptions ::v-deep {
    width: calc(100% - 300px);
    margin-left: 10px;
    .product-desc-label {
      width: 8em !important;
      text-align: center;
    }
    .product-desc-content {
      padding-left: 10px;
    }
  }
}
</style>
