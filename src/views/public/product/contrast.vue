<template>
  <div>
    <div class="item-desc-box" :style="{ left: leftX ? `-${(990 / 7) * (leftX % 7) + 10}px` : '-10px' }">
      <div class="item-desc-icon" :class="`desc-icon-${viewNum}`" :style="{ left: `${(990 / 7) * (leftX % 7) + 20}px` }">
      </div>
      <div class="navBox" v-loading="loading">
        <div class="item-table">
          <el-table :data="productList" style="width: 100%" height="360" class="custom-table custom-table-cell10"
            :row-class-name="tableRowClassName" @row-click="handleRowClick">
            <el-table-column prop="productName" align="center" label="产品名称" show-overflow-tooltip></el-table-column>
            <el-table-column prop="specs" align="center" label="规格" show-overflow-tooltip></el-table-column>
            <el-table-column prop="model" align="center" label="型号" min-width="120px"
              show-overflow-tooltip></el-table-column>
            <el-table-column prop="surface" align="center" label="表面处理" show-overflow-tooltip
              width="80px"></el-table-column>
            <el-table-column prop="level" align="center" label="产品等级" show-overflow-tooltip></el-table-column>
            <el-table-column prop="max" align="center">
              <template slot="header" slot-scope="scope">
                <span @click="showMax = !showMax" style="cursor: pointer"
                  :style="{ color: showMax ? '#ec2424' : '#31c776' }">{{ showMax ? '最高价' : '最低价' }}</span>
                <i :class="showMax ? 'el-icon-caret-top' : 'el-icon-caret-bottom'"
                  :style="{ color: showMax ? '#ec2424' : '#31c776' }"></i>
              </template>
              <template slot-scope="{ row }">
                <span style="color: #ec2424" v-if="showMax">{{ row.max ? `￥${row.max}` : '-' }}</span>
                <span style="color: #31c776" v-if="!showMax">{{ row.min ? `￥${row.min}` : '-' }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="item-desc">
          <div class="item-desc-top">
            <div class="top-img" @click="handleImg(info)">
              <img :src="formatProductImg(info)" :alt="info.productName" />
              <i class="ssfont ss-diy-fangda"></i>
            </div>
            <div class="top-item" @click="handleDraw(info, 'paper')">
              <i class="ssfont ss-diy-tuzhi"></i>
              <span>图纸</span>
            </div>
            <div class="top-item" @click="handleOffer(info)">
              <i class="ssfont ss-diy-baojia"></i>
              <span>报价</span>
              <em v-if="info.offerNum">{{ info.offerNum }}</em>
            </div>
            <div class="top-item" @click="handleQuote(info, 'project')"
              v-if="!isShowBrand">
              <i class="ssfont ss-diy-baojia"></i>
              <span>去报价</span>
            </div>
          </div>
          <div class="item-desc-ul">
            <div class="ul-item">
              <span>产品名称</span>
              {{ info.productName }}
            </div>
            <div class="ul-item">
              <span>所属分类</span>
              {{ info.industry }}
            </div>
            <div class="ul-item">
              <span>材料质量</span>
              {{ info.materialQuality }}
            </div>
            <div class="ul-item">
              <span>产品型号</span>
              {{ info.model }}
            </div>
            <template v-if="!info.offerNum">
              <div class="ul-item">
                <span>产品规格</span>
                {{ info.specs }}
              </div>
              <div class="ul-item">
                <span>表面处理</span>
                {{ info.surface }}
              </div>
            </template>
            <template v-if="info.offerNum">
              <div class="ul-item">
                <span>最低价</span>
                <span style="color: #31c776">{{ '￥' + info.min }}</span>
              </div>
              <div class="ul-item">
                <span>最高价</span>
                <span style="color: #ec2424">{{ '￥' + info.max }}</span>
              </div>
            </template>
            <div class="ul-item" v-if="info.inquiry">
              <span>询价次数</span>
              {{ info.inquiry }}次
            </div>
            <div class="buyers" v-if="isShowBrand">
              <div class="ul-item">
                <span style="margin-right: 8px">认证采购商</span>
                <div class="brand">{{ info.brand }}</div>
              </div>
              <div class="ul-item">
                <span style="margin-right: 32px">采购量</span>
                {{ quantityFormat(info) }}
              </div>
            </div>
            <div class="ul-item" v-if="!isShowBrand && info.brand">
              <span style="margin-right: 8px">采购商品牌</span>
              <div class="brand">{{ info.brand }}</div>
            </div>
            <div class="ul-link" @click="handleView(info)" v-if="!isChild">查看产品详情></div>
          </div>
          <div class="item-desc-btn" :class="{ gold: isGold }">
            <div class="btn-item" :class="{ disabled: info.collect }" @click="handleCollect(info)">
              <template v-if="info.collect">
                <i class="ssfont ss-diy-yishoucang"></i>
                已收藏
              </template>
              <template v-else>
                <i class="ssfont ss-diy-shoucang"></i>
                收藏该产品
              </template>
            </div>
            <div class="btn-item btn-quote" :class="{ disabled: usertype == 'cgs' }" @click="handleQuote(info, 'brand')"
              v-if="isShowBrand">
              <i class="ssfont ss-diy-baojia"></i>
              我要报价
            </div>
            <div class="btn-item btn-send" @click="handleSend(info)" v-else>
              <i class="ssfont ss-diy-fabu"></i>
              发布询价
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 产品详情 -->
    <product-dialog ref="productInfo" append-body @callback="showProduct = false" v-if="showProduct"></product-dialog>
    <!-- 报价信息 -->
    <offer-dialog ref="offerInfo" append-body></offer-dialog>
    <!--  发布询价  -->
    <el-dialog v-dialogDragBox title="我要报价" :visible.sync="quoteOpen" width="1150px" class="custom-dialog">
      <div class="quote-product">
        <div class="quote-product-img"><img :src="formatProductImg(info)" :alt="info.productName" /></div>
        <div class="quote-product-info">
          <div class="quote-product-title">{{ info.productName }}</div>
          <div class="quote-product-desc">
            <div class="item">
              <span>规格</span>
              {{ info.specs }}
            </div>
            <div class="item">
              <span>产品编码</span>
              {{ info.productCode }}
            </div>
            <div class="item">
              <span>材质</span>
              {{ info.materialQuality }}
            </div>
            <div class="item">
              <span>表面处理</span>
              {{ info.surface }}
            </div>
            <div class="item">
              <span>单位</span>
              {{ info.unit }}
            </div>
            <div class="item">
              <span>属性</span>
              {{ info.attribute }}
            </div>
            <div class="item">
              <span>重量</span>
              {{ info.weight }}
            </div>
          </div>
        </div>
      </div>
      <div class="quote-form">
        <el-form ref="quoteForm" :model="quoteForm" :rules="quoteRules" label-width="80px" :disabled="hasQUote">
          <el-row :gutter="10">
            <el-col :span="8">
              <el-form-item label="产品报价" prop="amount" class="lineHeight40">
                <el-input v-model="quoteForm.amount" placeholder="" style="width: 70%">
                  <span slot="suffix" class="inline-flex">元/{{ quoteForm.replyUnit }}</span>
                </el-input>
                <el-select style="width: 30%" v-model="quoteForm.replyUnit" filterable allow-create>
                  <el-option v-for="item in unitOptions" :key="item" :label="item" :value="item"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="报价时效" prop="expire" class="lineHeight40">
                <el-select v-model="quoteForm.expire" placeholder="报价时效" style="width: 100%">
                  <el-option v-for="item in expireOptions" :key="item.value" :label="item.label"
                    :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="手机号码" prop="phone" class="lineHeight40">
                <el-input v-model="quoteForm.phone" placeholder="请输入手机号码"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="报价单位备注" prop="remark">
                <el-input v-model="quoteForm.remark" placeholder="请输入报价单位备注； 例如： 每捆20根、每吨20000根…"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div class="quote-orange">
        <i class="el-icon-warning-outline"></i>
        当前为快速报价模式，当前报价只针对本次有效，如需要报价长期有效，请前往报价模块设置
        <span @click="handleSetting">去设置</span>
      </div>
      <div slot="footer">
        <button type="button" class="custom-dialog-btn" @click="quoteOpen = false">取消</button>
        <button type="button" class="custom-dialog-btn primary" @click="handleQuoteCancel" v-if="hasQUote">撤销</button>
        <button type="button" class="custom-dialog-btn primary" @click="handleQuoteSubmit" v-else>确定</button>
      </div>
    </el-dialog>

    <!--  快速报价  -->
    <el-dialog v-dialogDragBox :title="quickTitle" :visible.sync="quickOpen" width="1150px" class="custom-dialog">
      <el-form ref="quickForm" :model="quickForm" :rules="quickRules" label-width="80px"
        style="margin-left: 20px; margin-right: 20px">
        <el-row :gutter="10">
          <template>
            <el-col :span="24">
              <el-form-item label="关联产品" prop="productName">
                <el-input v-model="quickForm.productName" readonly placeholder="请选择关联产品"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24" v-if="quickForm.productName">
              <div class="product-detail">
                <div class="product-detail-item">
                  <span>产品名称：</span>
                  <span class="c333">{{ quickForm.product.productName || '' }}</span>
                </div>
                <div class="product-detail-item">
                  <span>产品编号：</span>
                  <span class="c333">{{ quickForm.product.productCode || '' }}</span>
                </div>
                <div class="product-detail-item">
                  <span>产品图片：</span>
                  <img :src="formatProductImg(quickForm.product)" :alt="quickForm.product.productName" />
                </div>
                <div class="product-detail-link" @click="handleProductInfos(quickForm.product)">查看详情</div>
              </div>
            </el-col>
          </template>
          <el-col :span="12">
            <el-form-item label="报价名称" prop="name">
              <el-input v-model="quickForm.name" placeholder="请输入报价名称"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="未税价格" prop="totalPrice">
              <el-input v-model="quickForm.totalPrice" placeholder="请输入未税价格">
                <span slot="suffix">元</span>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否含税" prop="isIncludingTax">
              <el-switch v-model="quickForm.isIncludingTax" active-text="含税" inactive-text="不含税"></el-switch>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="quickForm.isIncludingTax">
            <el-form-item label="含税比例" prop="taxPercent">
              <el-input v-model="quickForm.taxPercent" placeholder="请输入含税比例" style="width: 100%"
                @change="handleCalculate">
                <span slot="suffix">%</span>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="quickForm.isIncludingTax">
            <el-form-item label="含税价格" prop="totalPrices">
              <el-input v-model="quickForm.totalPrices" readonly placeholder="请输入含税比例和含税价格" style="width: 100%">
                <span slot="suffix">元</span>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="报价备注" prop="publishRemark">
              <el-input type="textarea" :autosize="{ minRows: 4, maxRows: 6 }" resize="none"
                v-model="quickForm.publishRemark" placeholder="请输入报价备注" style="width: 100%"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div class="quote-orange">
        <i class="el-icon-warning-outline"></i>
        当前为快速报价模式，如需要报价长期有效，请前往报价模块设置
        <span @click="handleSetting">去设置</span>
      </div>
      <div slot="footer">
        <button type="button" class="custom-dialog-btn" @click="quickOpen = false">取消</button>
        <button type="button" class="custom-dialog-btn primary" @click="handleSubmitQuick">确定</button>
      </div>
    </el-dialog>
    <!--  绑定手机  -->
    <binding-dialog ref="bindingInfo" append-body></binding-dialog>
    <!-- 检查是否企业用户 -->
    <check-company ref="checkCompany"></check-company>
  </div>
</template>

<script>
import { getToken } from '@/utils/auth'
import ProductDialog from './dialog'
import OfferDialog from './offer'
import { getTypeProducts, productCollect } from '@/api/system/product'
import { gygs, list, quoteadd, quoteedi, AddQuickQuote } from '@/api/houtai/formula'
import { shoucTo } from '@/api/houtai/shoucang'
import { cancelReply, indexReplyBuy, replyBuy } from '@/api/buy'
import { isNumber, isNumberLength } from '@/utils/validate'
import { supplier } from '@/api/system/company'
import BindingDialog from '@/views/public/binding/index'
import CheckCompany from '@/components/CheckCompany'

export default {
  components: { BindingDialog, ProductDialog, OfferDialog, CheckCompany },
  props: {
    viewNum: {
      type: Number,
      default: 5
    },
    descInfo: {
      type: Object,
      required: true
    },
    isChild: {
      type: Boolean,
      default: false
    },
    collectList: {
      type: Array,
      default: () => []
    },
    isShowBrand: {
      type: Boolean,
      default: false
    },
    leftX: {
      type: Number,
      default: 0
    },
    xcoord: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      isToken: false,
      info: {},
      quoteOpen: false,
      quoteForm: {},
      quoteRules: {
        amount: [
          { required: true, message: '请输入产品报价', trigger: 'blur' },
          { validator: isNumber, message: '输入有误请重新输入', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: 'blur' }
        ],
        phone: [
          { required: true, message: '请输入手机号码', trigger: 'blur' },
          { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
        ]
      },
      unitOptions: ['吨', '千克', '个', '件', '套', '米', '支', '根', '袋', '托'],
      timeUnitOptions: [
        { label: '天', value: 'day' },
        { label: '周', value: 'week' },
        { label: '月', value: 'month' },
        { label: '季', value: 'quarter' },
        { label: '年', value: 'year' }
      ],
      expireOptions: [
        { label: '1天', value: 1 },
        { label: '2天', value: 2 },
        { label: '3天', value: 3 },
        { label: '4天', value: 4 },
        { label: '5天', value: 5 },
        { label: '6天', value: 6 },
        { label: '7天', value: 7 }
      ],
      hasQUote: false,
      usertype: undefined,
      productList: [],
      showMax: false,
      loading: true,
      categoryList: [],
      quickForm: {},
      quickRules: {
        productName: [{ required: true, message: '请选择关联产品', trigger: 'change' }],
        name: [{ required: true, message: '请输入报价名称', trigger: 'blur' }],
        taxPercent: [
          { required: true, message: '请输入含税比例', trigger: 'blur' },
          { validator: isNumber, message: '请输入正确的含税比例', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 2), message: '只可以填写两位小数', trigger: 'blur' }
        ],
        totalPrice: [
          { required: true, message: '请输入未税价格', trigger: 'blur' },
          { validator: isNumber, message: '请输入正确的未税价格', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: 'blur' }
        ],
        publishRemark: [{ required: true, message: '请输入发布备注', trigger: 'blur' }]
      },
      quickOpen: false,
      quickTitle: undefined,
      isGold: false,
      showProduct: false
    }
  },
  computed: {
    userType() {
      return this.$store.getters.userType || '00'
    }
  },
  created() {
    const companyId = this.$store.getters.info.companyId
    this.usertype = companyId > 0 ? 'gys' : 'cgs'
    this.isToken = !!getToken()
    this.handleNewPorduct(this.descInfo)
    if (!!getToken()) this.getCompany(companyId)
  },
  methods: {
    // 获取当前登陆人的供应类目
    getCompany(companyId) {
      supplier({ id: companyId }).then(res => {
        if (res.code == 200) {
          this.categoryList = res.data.supplier ? res.data.supplier.categoryList : []
        }
      })
    },
    // 产品详情
    handleProductInfos(row) {
      this.showProduct = true
      this.$nextTick(() => {
        this.$refs.productInfo.handleView(row)
      })
    },
    // 图片放大
    handleImg(item) {
      this.showProduct = true
      this.$nextTick(() => {
        this.$refs.productInfo.handleImgView(item)
      })
    },
    // 查看图纸
    handleDraw(item) {
      this.showProduct = true
      this.$nextTick(() => {
        this.$refs.productInfo.handleView(item, 'paper')
      })
    },
    // 查看详情
    handleView(item, val) {
      this.showProduct = true
      this.$nextTick(() => {
        this.$refs.productInfo.handleView(item, val)
      })
    },
    // 查看报价
    handleOffer(item) {
      if (!!getToken() && (this.userType !== '00' && this.userType !== '05')) {
        this.$refs.bindingInfo.handleOpen()
        return
      }
      this.$refs.offerInfo.handleView(item)
    },
    // 收藏
    handleCollect(item) {
      if (!!getToken() && (this.userType !== '00' && this.userType !== '05')) {
        this.$refs.bindingInfo.handleOpen()
        return
      }
      if (this.isToken) {
        if (this.collectList.length) {
          if (!item.collect) {
            const data = { storeId: this.collectList[0]['storeId'], valueIdList: [item.id] }
            shoucTo(data).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('收藏成功')
                this.$emit('clickHide')
              }
            })
          }
        } else {
          this.$message.error('出现错误，请稍后再试')
        }
      } else {
        this.$router.push({
          path: '/login'
        })
      }
    },
    // 发布询价
    handleSend(item) {
      if (!this.isToken) {
        this.$router.push({
          path: '/login'
        })
      } else {
        this.$router.push({
          path: '/demand/order',
          query: {
            data: JSON.stringify(item)
          }
        })
      }
    },
    // 我要报价
    handleQuote(row, type) {
      if (!!getToken() && (this.userType !== '00' && this.userType !== '05')) {
        this.$refs.bindingInfo.handleOpen()
        return
      }
      if(this.usertype !== 'gys') {
        this.$refs.checkCompany.showDialog()
        return
      }
      if (type === 'brand') {
        if (this.usertype === 'cgs') return
        const { buyingLeadId } = row
        const phone = this.$store.getters.info.phonenumber || ''
        this.quoteForm = {
          amount: undefined,
          expire: 3,
          leadsId: row.buyingLeadId || undefined,
          phone,
          replyUnit: row.unit,
          remark: undefined
        }
        this.resetForm('quoteForm')
        this.hasQUote = false
        indexReplyBuy({ buyingLeadId }).then(res => {
          const { code, msg, data } = res
          if (code === 200) {
            if (data) {
              this.quoteForm = data
              this.hasQUote = true
              this.quoteOpen = true
            } else this.quoteOpen = true
          } else this.$message.error(msg)
        })
      } else if (type === 'project') {
        this.quickForm = {
          isIncludingTax: false,
          name: undefined,
          productName: row.productName,
          product: row,
          productId: row.id || undefined,
          publishRemark: undefined,
          source: 'common',
          taxPercent: undefined,
          totalPrice: undefined,
          totalPrices: undefined
        }
        this.resetForm('quickForm')
        this.quickTitle = '新增快速报价'
        this.quickOpen = true
      }
    },
    // 提交我要报价
    handleQuoteSubmit() {
      this.$refs.quoteForm.validate(valid => {
        if (valid) {
          const { amount, leadsId, phone, replyUnit, remark } = this.quoteForm
          const data = { amount, leadsId, phone, replyUnit, remark }
          replyBuy(data).then(res => {
            const { code, msg } = res
            if (code === 200) {
              this.$message.success('发布成功')
              this.quoteOpen = false
            } else this.$message.error(msg)
          })
        }
      })
    },
    // 取消我要报价
    handleQuoteCancel() {
      const leadsReplyId = this.quoteForm.id
      this.$confirm('是否撤销本次报价？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        cancelReply({ leadsReplyId }).then(res => {
          const { code, msg } = res
          if (code === 200) {
            this.$message.success('撤销成功')
            this.quoteOpen = false
          } else this.$message.error(msg)
        })
      })
    },
    handleNewPorduct(item) {
      const categoryId = item.id
      this.loading = true
      getTypeProducts({ categoryId }).then(async res => {
        const { code, msg, rows } = res
        if (code === 200) {
          if (rows.length) {
            if (this.isToken) {
              await Promise.all(
                rows.map(async item => {
                  if (!item.collect) {
                    const collect = await productCollect({ productId: item.id })
                    if (collect.data) item.collect = true
                  }
                  const offer = await gygs({ productId: item.id })
                  if (offer.data.quotes) {
                    item.max = offer.data.max || ''
                    item.min = offer.data.min || ''
                    item.offerNum = offer.data.quotes.length
                  }
                })
              )
            } else {
              await Promise.all(
                rows.map(async item => {
                  item.max = ''
                  item.min = ''
                })
              )
            }
            let newRows = this.moveElementsToPosition(rows, rows.filter(item => item.isOptimal == true), 0)
            this.loading = false
            this.productList = newRows
            await this.handleProductInfo(newRows[0])
          }
        } else this.$message.error(msg)
      })
      this.info = item
    },
    async handleProductInfo(item) {
      const data = { ...item }
      if (this.isToken) {
        if (!data.collect) {
          const collect = await productCollect({ productId: data.id })
          if (collect.data) data.collect = true
        }
      }
      this.info = data
      if (this.info.isOptimal == true) {
        this.isGold = true
      } else {
        this.isGold = false
      }
    },
    quantityFormat(row) {
      const { quantity, unit, timeUnit } = row
      const time = this.timeUnitOptions.find(item => item.value === timeUnit) || { label: '' }
      return `${quantity}${unit}/${time.label}`
    },
    // 去设置（后台报价）
    handleSetting() {
      const name = this.info.productName + '报价'
      const productId = this.info.id
      const source = 'common'
      quoteadd({ name }).then(res => {
        list().then(res2 => {
          const quoteId = res2.rows[0].id
          const data = { quoteId, productId, source }
          quoteedi(data).then(res => {
            this.$router.push({ path: '/offer/addition', query: { id: quoteId } })
          })
        })
      })
    },
    tableRowClassName({ row, rowIndex }) {
      if (row.isOptimal === true) return 'row-gold'
      if (row.id === this.info.id) {
        return 'row-selected'
      }
    },
    handleRowClick(row) {
      this.handleProductInfo(row)
    },
    // 计算含税价格
    handleCalculate() {
      const { totalPrice, taxPercent } = this.quickForm
      if (totalPrice && taxPercent) {
        this.quickForm.totalPrices = parseFloat((totalPrice * (1 + taxPercent / 100)).toFixed(5))
      }
    },
    // 提交修改快速报价
    handleSubmitQuick() {
      this.$refs['quickForm'].validate(valid => {
        if (valid) {
          const { isIncludingTax, name, productId, publishRemark, source, taxPercent, totalPrice } = this.quickForm
          const data = { isIncludingTax, name, productId, publishRemark, source, taxPercent, totalPrice }
          AddQuickQuote(data).then(res => {
            const { code, msg } = res
            if (code === 200) {
              this.$message.success('添加成功')
              this.quickOpen = false
            } else this.$message.error(msg)
          })
        }
      })
    },
    // 移动数组元素位置
    moveElementsToPosition(arr, elements, targetIndex) {
      // 确保目标索引在数组长度范围内
      if (targetIndex < 0 || targetIndex > arr.length) return arr;

      // 过滤出不需要移动的元素，并保留其原始顺序
      const filtered = arr.filter(el => !elements.includes(el));

      // 将需要移动的元素按原始顺序插入到目标索引位置
      const moved = [...filtered.slice(0, targetIndex), ...elements, ...filtered.slice(targetIndex)];

      return moved;
    }
  }
}
</script>
<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';

.navBox {
  width: 990px;
  height: 360px;
  background-color: #f2f4f8;
  box-shadow: 0 1px 15px 0 rgba(0, 0, 0, 0.25);
  border: 1px solid #2e73f3;
  border-radius: 8px;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  overflow: hidden;

  .item-table {
    width: calc(100% - 303px);
  }

  .item-desc {
    border: 0;
    border-radius: 0;
    box-shadow: none;
  }
}

::v-deep {
  .custom-table .el-table__body-wrapper {
    overflow: unset;
  }

  .item-table {
    .el-table__cell {
      border-top: 1px solid transparent;
    }
  }

  .row-selected {
    background-color: #e5eeff !important;

    .el-table__cell {
      border-top: 1px solid #2e73f3;
      border-bottom: 1px solid #2e73f3;

      &:first-child {
        border-radius: 5px 0 0 5px;
        border-left: 1px solid #2e73f3;
      }

      &:last-child {
        border-radius: 0 5px 5px 0;
        border-right: 1px solid #2e73f3;
      }
    }

    .cell {
      color: #2e73f3;
    }
  }

  .row-gold {
    background: linear-gradient(103deg, #ffcf53 4%, #ffe5a1 96%);
    border-radius: 5px;

    .el-table__cell {
      border-top: 1px solid #fba628;
      border-bottom: 1px solid #fba628;
      color: #6c4409 !important;

      &:first-child {
        border-radius: 5px 0 0 5px;
        border-left: 1px solid #fba628;
        position: relative;

        &:before {
          display: inline-block;
          content: '国标甄选';
          font-size: 10px;
          line-height: 14px;
          padding: 0 5px;
          background-color: #774906;
          border-radius: 3px;
          color: #ffffff;
          position: absolute;
          left: 10px;
          top: -5px;
        }
      }

      &:last-child {
        border-radius: 0 5px 5px 0;
        border-right: 1px solid #fba628;
        margin-right: 10px;
      }
    }
  }

  .item-desc-btn.gold {
    .btn-item {
      border-color: #7e5513;
      color: #7e5513;

      &.btn-quote,
      &.btn-send {
        background: linear-gradient(103deg, #ffcf53 4%, #ffe5a1 96%);
        border: 1px solid #fba628;
      }
    }
  }
}

.item-desc-box {
  left: 0;
  top: 98px;
}

.brand {
  color: #7f4d03;
  display: inline-block;
  border: 1px solid #fdcc8b;
  background: linear-gradient(102deg, #fde4af 0%, #f6c576 100%);
  border-radius: 5px;
  height: 24px;
  padding: 0 15px;
}

.buyers {
  background-color: #e6e9ee;
  margin: 0 -30px;
  padding: 10px 30px;
}

.quote-form {
  ::v-deep {
    .el-form-item__label {
      line-height: 20px;
    }

    .lineHeight40 .el-form-item__label {
      line-height: 40px;
    }
  }
}

.product-detail {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  padding-left: 10px;
  padding-bottom: 20px;

  &-item {
    display: inline-flex;
    align-items: center;
    padding: 5px 0;
    margin-right: 10px;

    span {
      color: #999999;
      margin-right: 5px;

      &.c333 {
        color: #333333;
      }
    }

    img {
      width: 42px;
      height: 42px;
      border-radius: 5px;
      border: 1px solid #ededed;
    }
  }

  &-link {
    color: #2e73f3;
    cursor: pointer;
  }
}
</style>
