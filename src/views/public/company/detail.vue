<template>
  <div class="container" @contextmenu.prevent>
    <header-tpl ref="header" :is-login="islogin" />
    <div class="container-box supplier-detail" v-loading="loading">
      <div class="supplier-detail-breadcrumb">
        <span @click="goBack">品牌墙</span>
        <span>企业详情</span>
      </div>
      <div class="supplier-detail-info">
        <div style="flex: 1; display: flex; flex-direction: column">
          <div class="supplier-detail-info-title">
            <span>Company Introduction</span>
            <b>{{ info.name }}</b>
          </div>
          <div class="supplier-detail-info-content">
            <div class="content-item">
              <span>公司法人</span>
              <b>{{ info.legal }}</b>
            </div>
            <div class="content-item">
              <span>公司地址</span>
              <b>{{ address }}</b>
            </div>
            <div class="content-item">
              <span>公司标语</span>
              <b>{{ info.slogan }}</b>
            </div>
            <div class="content-item">
              <span>经营产品</span>
              <b>{{ info.businessScope }}</b>
            </div>
            <div class="content-contact">
              <el-table :data="contacts" style="width: 100%" stripe>
                <el-table-column align="center" label="联系人">
                  <template slot-scope="scope">{{ scope.row.userName || scope.row.nickName }}</template>
                </el-table-column>
                <el-table-column align="center" prop="post" label="职务">
                  <template slot-scope="scope">{{ scope.row.post || '--' }}</template>
                </el-table-column>
                <el-table-column align="center" prop="phone" label="联系电话">
                  <template slot-scope="scope">{{ maskPhone(scope.row.phone) }}</template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </div>
        <div class="supplier-detail-info-img">
          <span class="supplier-detail-info-img-tip1"></span>
          <span class="supplier-detail-info-img-tip2"></span>
          <img src="../../../../public/imgs/Rectangle1337.jpg" :alt="info.name" />
        </div>
      </div>
      <div class="supplier-detail-product" v-if="productList.length">
        <div class="product-title">{{ info.name }}—报价产品</div>
        <el-table :data="productList" style="width: 100%" class="product-table">
          <el-table-column align="center" label="序号" width="80">
            <template slot-scope="scope">
              {{ scope.$index + 1 }}
            </template>
          </el-table-column>
          <el-table-column align="center" label="产品图片" width="100">
            <template slot-scope="{ row }">
              <el-image :src="formatProductImg(row)" fit="cover" @click="handleImgView(row)">
                <div slot="error" class="image-slot">
                  <i class="el-icon-picture-outline"></i>
                </div>
              </el-image>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="name" label="产品名称" show-overflow-tooltip>
            <template slot-scope="{ row }">
              <span class="table-link" @click="handleView(row)">{{ row.productName }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="specs" label="规格" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="model" label="型号" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="productCode" label="产品编码" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" label="产品报价">
            <template slot-scope="{ row }">
              <span class="table-price pointer" v-if="!!row.price" @click="handleOffer(row)">{{ '￥' + row.price + '元' }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="materialQuality" label="材质" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="surface" label="表面" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="unit" label="单位" width="60"></el-table-column>
        </el-table>
        <div class="product-more" v-if="showMoreProduct" @click="handleMoreProduct" :class="{ disabled: loadingProduct }">
          <i class="el-icon-more"></i>
          <span>{{ loadingProduct ? '加载中...' : '查看更多报价产品' }}</span>
          <i class="el-icon-more"></i>
        </div>
      </div>
      <div class="supplier-detail-photo photo-bg">
        <div class="supplier-detail-photo-title">
          <span>Company Album</span>
          <b>厂区照片</b>
          <em></em>
        </div>
        <div class="supplier-detail-photo-list" v-if="siteImg.length">
          <div class="supplier-detail-photo-list-item" v-for="(item, index) in siteImg" :key="index"><img :src="imgUrl + item" :alt="info.name" /></div>
        </div>
        <el-empty :image-size="300" v-else />
      </div>
      <div class="supplier-detail-photo">
        <div class="supplier-detail-photo-title">
          <span>Equipment photos</span>
          <b>设备照片</b>
          <em></em>
        </div>
        <div class="supplier-detail-photo-list" v-if="deviceImg.length">
          <div class="supplier-detail-photo-list-item" v-for="(item, index) in deviceImg" :key="index"><img :src="imgUrl + item" :alt="info.name" /></div>
        </div>
        <el-empty :image-size="300" v-else />
      </div>
      <div class="supplier-detail-photo photo-bg">
        <div class="supplier-detail-photo-title">
          <span>production process</span>
          <b>生产工艺</b>
          <em></em>
        </div>
        <div class="supplier-detail-photo-list" v-if="process.length">
          <div class="supplier-detail-photo-list-item" v-for="(item, index) in process" :key="index">
            <!-- 如果是视频文件 -->
            <div v-if="isVideoFile(item)" class="video-container">
              <video :src="imgUrl + item" controls preload="metadata" @error="onVideoError" class="process-video">您的浏览器不支持 video 标签。</video>
              <div class="video-overlay" v-if="!isVideoPlaying(index)">
                <i class="el-icon-video-play play-icon" @click="playVideo(index)"></i>
              </div>
            </div>
            <!-- 如果是图片文件 -->
            <img v-else :src="imgUrl + item" :alt="info.name" class="process-image" />
          </div>
        </div>
        <el-empty :image-size="300" v-else />
      </div>
      <div class="supplier-detail-photo">
        <div class="supplier-detail-photo-title">
          <span>Equipment photos</span>
          <b>荣誉证书</b>
          <em></em>
        </div>
        <div class="supplier-detail-photo-list" v-if="certImg.length">
          <div class="supplier-detail-photo-list-item" v-for="(item, index) in certImg" :key="index"><img :src="imgUrl + item" :alt="info.name" /></div>
        </div>
        <el-empty :image-size="300" v-else />
      </div>
      <div class="supplier-detail-photo photo-bg">
        <div class="supplier-detail-photo-title">
          <span>qualification documents</span>
          <b>资质文件</b>
          <em></em>
        </div>
        <div class="supplier-detail-photo-list" v-if="certificationUrl.length">
          <div class="supplier-detail-photo-list-item" v-for="(item, index) in certificationUrl" :key="index"><img :src="imgUrl + item" :alt="info.name" /></div>
        </div>
        <el-empty :image-size="300" v-else />
      </div>
    </div>
    <footer-tpl />
  </div>
</template>

<script>
import HeaderTpl from '@/views/components/header'
import FooterTpl from '@/views/components/footer'
import { supplier } from '@/api/system/company'
import { getProductsQuote } from '@/api/system/company'
import { quotesel } from '@/api/houtai/formula'
import { getToken } from '@/utils/auth'

export default {
  name: 'CompanyDetail',
  components: { HeaderTpl, FooterTpl },
  data() {
    return {
      loading: true,
      companyId: undefined,
      info: {},
      siteImg: [],
      deviceImg: [],
      process: [],
      certImg: [],
      certificationUrl: [],
      imgUrl: 'http://www.ziyouke.net/prod-api',
      address: '',
      contacts: [],
      productList: [],
      // 分页相关
      productQuery: {
        pageNum: 1,
        pageSize: 10
      },
      productTotal: 0,
      showMoreProduct: false,
      loadingProduct: false,
      islogin: false,
      videoPlayingStates: {} // 记录视频播放状态
    }
  },
  created() {
    this.islogin = !!getToken()
    if (!this.islogin) {
      this.$router.push('/login')
      return
    }
    const { id } = this.$route.query
    if (!id) {
      this.$confirm('参数有误，点击确定返回', '提示', {
        confirmButtonText: '确定',
        showCancelButton: false,
        type: 'warning'
      }).then(() => {
        this.goBack()
      })
      return
    }
    this.companyId = id
    this.getDetail()
    this.getProductList()
  },
  beforeDestroy() {
    // 清理视频播放状态
    this.videoPlayingStates = {}
  },
  methods: {
    // 手机号脱敏
    maskPhone(phone) {
      if (!phone) return '--'
      const phoneStr = phone.toString()
      if (phoneStr.length < 7) return phoneStr
      // 保留前3位和后4位，中间用*替代
      return phoneStr.slice(0, 3) + '****' + phoneStr.slice(-4)
    },
    // 返回
    goBack() {
      this.$store.dispatch('tagsView/delView', this.$route)
      this.$router.go(-1)
    },
    // 查询详情
    getDetail() {
      const loading = this.$loading({
        lock: true,
        text: '加载中...',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      this.loading = true
      const query = { id: this.companyId }
      supplier(query).then(res => {
        if (res.code === 200) {
          const data = res.data.supplier
          // 场地图片
          if (data.siteImg) this.siteImg = data.siteImg.split(',')
          // 设备照片
          if (data.deviceImg) this.deviceImg = data.deviceImg.split(',')
          // 生产工艺
          if (data.process) this.process = data.process.split(',')
          // 荣誉证书
          if (data.certImg) this.certImg = data.certImg.split(',')
          // 资质证书
          if (data.certificationUrl) this.certificationUrl = data.certificationUrl.split(',')
          this.info = data
          this.address = res.data.company?.address || ''
          this.contacts = res.data?.contacts || []
          this.loading = false
        } else {
          this.$message.error(res.msg)
        }
      }).finally(() => {
        loading.close()
      })
    },
    // 查看报价产品
    async getProductList() {
      if (!this.islogin) return
      this.loadingProduct = true
      try {
        const query = {
          companyId: this.companyId,
          pageNum: this.productQuery.pageNum,
          pageSize: this.productQuery.pageSize
        }
        const res = await getProductsQuote(query)
        if (res.code === 200) {
          const productQuoteList = res.rows || []
          this.productTotal = res.total || 0
          // 获取报价详情
          const productQuoteInfo = await Promise.all(
            productQuoteList.map(async item => {
              try {
                const quoteInfo = await quotesel({ quoteId: item.id })
                return {
                  ...quoteInfo?.data?.product,
                  price: quoteInfo?.data?.totalPrice || 0,
                  quoteId: item.id,
                  companyName: item.companyName
                }
              } catch (error) {
                console.error(`获取报价详情失败, quoteId: ${item.id}`, error)
                return null
              }
            })
          )
          // 过滤掉空值并根据产品id去重
          const uniqueProductList = productQuoteInfo.filter(Boolean).filter((item, index, self) => {
            return index === self.findIndex(p => p.id === item.id)
          })
          if (this.productQuery.pageNum === 1) {
            this.productList = uniqueProductList
          } else {
            this.productList = [...this.productList, ...uniqueProductList]
          }
          this.showMoreProduct = this.productList.length < this.productTotal
        } else {
          this.$message.error(res.msg || '查询失败')
        }
      } catch (error) {
        console.error('获取报价产品失败', error)
        // this.$message.error('获取报价产品失败，请稍后重试')
      } finally {
        this.loadingProduct = false
      }
    },
    // 加载更多报价产品
    async handleMoreProduct() {
      if (this.loadingProduct) return
      this.productQuery.pageNum++
      await this.getProductList()
    },
    // 数组去重
    uniqueJsonArrByField(jsonArr, field) {
      // 数组长度小于2 或 没有指定去重字段 或 不是json格式数据
      if (jsonArr.length < 2 || !field || typeof jsonArr[0] !== 'object') return jsonArr
      return jsonArr.reduce((all, next) => (all.some(item => item[field] === next[field]) ? all : [...all, next]), [])
    },
    // 格式化产品图片
    formatProductImg(row) {
      if (!row.picture1) return ''
      return row.picture1.startsWith('http') ? row.picture1 : this.imgUrl + row.picture1
    },
    // 查看产品详情
    handleView(row) {
      this.$refs.header.handleView(row)
    },
    // 查看产品图片
    handleImgView(row) {
      this.$refs.header.handleImgView(row)
    },
    // 查看报价
    handleOffer(row) {
      this.$refs.header.handleOffer(row)
    },
    // 判断是否为视频文件
    isVideoFile(fileName) {
      if (!fileName) return false
      const videoExtensions = ['.mp4', '.webm', '.ogg', '.avi', '.mov', '.wmv', '.flv', '.mkv', '.3gp']
      const lowerFileName = fileName.toLowerCase()
      return videoExtensions.some(ext => lowerFileName.includes(ext))
    },
    // 判断视频是否正在播放
    isVideoPlaying(index) {
      return this.videoPlayingStates[index] || false
    },
    // 播放视频
    playVideo(index) {
      // 暂停所有正在播放的视频
      this.pauseAllVideos()
      const videoElement = document.querySelectorAll('.process-video')[index]
      if (videoElement) {
        videoElement.play()
        this.$set(this.videoPlayingStates, index, true)
        // 监听视频播放和暂停事件
        videoElement.addEventListener('play', () => {
          this.$set(this.videoPlayingStates, index, true)
        })
        videoElement.addEventListener('pause', () => {
          this.$set(this.videoPlayingStates, index, false)
        })
        videoElement.addEventListener('ended', () => {
          this.$set(this.videoPlayingStates, index, false)
        })
      }
    },
    // 暂停所有视频
    pauseAllVideos() {
      const videoElements = document.querySelectorAll('.process-video')
      videoElements.forEach((video, index) => {
        if (!video.paused) {
          video.pause()
          this.$set(this.videoPlayingStates, index, false)
        }
      })
    },
    // 视频加载错误事件
    onVideoError(event) {
      console.error('视频加载失败:', event.target.src)
      this.$message.error('视频加载失败，请稍后重试')
    }
  }
}
</script>
<style lang="scss" scoped>
.supplier-detail-info-content {
  margin-top: 40px;
  .content-item {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    line-height: 20px;
    span {
      width: 60px;
      color: #999999;
      font-size: 12px;
    }
    b {
      font-size: 12px;
      color: #333333;
      font-weight: 500;
    }
  }
}
.supplier-detail-product {
  width: 1200px;
  margin: 20px auto 100px;
  .product-title {
    line-height: 36px;
    background-color: #e9edf7;
    border: 1px solid #cbd6e2;
    border-bottom: 0;
    border-radius: 5px 5px 0 0;
    font-size: 12px;
    color: #333;
    padding: 0 20px;
  }
  .el-table.product-table {
    border-radius: 0;
  }
  .product-more {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 40px;
    background-color: #e9edf7;
    border: 1px solid #cbd6e2;
    border-top: 0;
    border-radius: 0 0 5px 5px;
    font-size: 12px;
    color: #333;
    padding: 0 20px;
    cursor: pointer;
    span {
      margin: 0 5px;
    }
    &:hover {
      color: #2e73f3;
    }
    &.disabled {
      cursor: not-allowed;
      opacity: 0.6;
      &:hover {
        color: #333;
      }
    }
  }
}

// 视频相关样式 - 保持与原图片样式一致
.supplier-detail-photo-list-item {
  float: left;
  width: calc(25% - 14px);
  height: 214px;
  border: 1px solid #cbcbcb;
  border-radius: 8px;
  margin: 0 7px 14px;
  overflow: hidden;
  position: relative;
  .video-container {
    position: relative;
    width: 100%;
    height: 100%;
    background: #f5f5f5;
    .process-video {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    .video-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(0, 0, 0, 0.3);
      cursor: pointer;
      transition: all 0.3s ease;
      &:hover {
        background: rgba(0, 0, 0, 0.5);
        .play-icon {
          transform: scale(1.2);
        }
      }
      .play-icon {
        font-size: 40px;
        color: #ffffff;
        transition: transform 0.3s ease;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
      }
    }
  }
  .process-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.3s;
  }
  &:hover {
    .process-image {
      transform: scale(1.2);
    }
  }
}
</style>
