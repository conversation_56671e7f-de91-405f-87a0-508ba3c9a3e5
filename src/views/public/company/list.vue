<template>
  <div class="container">
    <header-tpl :is-login="isLogin" :showSearch="false" />
    <div class="container-box">
      <div class="containerBox">
        <div class="companySearchBox">
          <div class="product-search">
            <el-form ref="queryParams" label-width="0" @submit.native.prevent>
              <!-- <el-input v-model="companyName" @select="handleQuery" clearable placeholder="请输入企业名称" @keyup.enter.native="handleQuery"></el-input> -->
              <el-autocomplete v-model="companyName" :fetch-suggestions="querySearch" placeholder="请输入企业名称" @select="handleQuery" @keyup.enter.native="handleQuery">
                <template slot-scope="{ item }">
                  <div class="custom-autocomplete">
                    <span>{{ item.value }}</span>
                    <i class="el-icon-close" @click.stop="handleDelete(item)"></i>
                  </div>
                </template>
              </el-autocomplete>
              <el-button type="primary" size="default" icon="el-icon-search" @click="handleQuery">搜索</el-button>
            </el-form>
          </div>
          <div class="companyCollect" v-if="collectNum" @click="handleGo">查看已关注企业</div>
        </div>
        <div class="enterprise" v-loading="loading" v-if="list.length">
          <div class="enterpriseItem" v-for="(info, index) in list" :key="index" @click="handleDetail(info)">
            <img class="enterpriseLogo" :src="info.companyLogo || require('@/assets/images/company_icon1.png')" :alt="info.companyName" />
            <div class="enterpriseInfo">
              <div class="enterpriseName">
                <span>{{ info.companyName }}</span>
                <em>{{ info.companyRegStatus }}</em>
              </div>
              <div class="enterpriseFlex">
                <div class="enterpriseInfoItem" v-if="info.hasOwnProperty('companyPhoneSourceList') && info.companyPhoneSourceList.length">
                  <img src="@/assets/images/detail_01.png" alt="电话" />
                  <span>电话：{{ info.companyPhoneSourceList.length ? info.companyPhoneSourceList[0]['number'] : '' }}</span>
                </div>
                <div class="enterpriseInfoItem" v-if="info.hasOwnProperty('companyEmailList') && info.companyEmailList.length">
                  <img src="@/assets/images/detail_02.png" alt="邮箱" />
                  <span>邮箱：{{ info.companyEmailList.length ? info.companyEmailList[0] : '' }}</span>
                </div>
                <div class="enterpriseInfoItem" v-if="info.companyWebSite">
                  <img src="@/assets/images/detail_03.png" alt="官网" />
                  <span>官网：{{ info.companyWebSite }}</span>
                </div>
              </div>
              <div class="enterpriseFlex">
                <div class="enterpriseDescItem">
                  <span>法定代表人：</span>
                  <b class="primary">{{ info.companyLegalPersonName }}</b>
                </div>
                <div class="enterpriseDescItem">
                  <span>注册资本：</span>
                  <b>{{ info.companyRegCapital }}</b>
                </div>
                <div class="enterpriseDescItem">
                  <span>成立日期：</span>
                  <b>{{ parseTime(info.companyApprovedTime, '{y}-{m}-{d}') }}</b>
                </div>
                <div class="enterpriseDescItem">
                  <span>统一社会信用代码：</span>
                  <b>{{ info.companyCreditCode }}</b>
                </div>
              </div>
              <div class="enterpriseDescItem">
                <span>地址：</span>
                <b>{{ removeHtmlTag(info.companyRegLocation, 300) }}</b>
              </div>
            </div>
            <i class="el-icon-arrow-right enterpriseMore"></i>
            <el-button class="enterpriseCollect" size="small" type="primary" icon="el-icon-star-on" @click.stop="handleCancel(info, index)" v-if="info.isCollect">取消关注</el-button>
            <el-button class="enterpriseCollect" size="small" icon="el-icon-star-off" @click.stop="handleCollect(info, index)" v-if="!info.isCollect && !!info.companyCreditCode">关注</el-button>
          </div>
        </div>
        <el-empty :description="''" v-if="!list.length" :image-size="200">
          <span v-if="!isSearch">请输入企业名称进行搜索</span>
          <span v-if="isSearch && loading">加载中...</span>
          <span v-if="isSearch && !loading">
            抱歉，没有找到相关结果…
            <br />
            请尝试输入更准确的关键词并重新搜索
          </span>
        </el-empty>
        <template v-if="!isSearch">
          <div class="enterpriseIconTitle">我们可为您提供的查询服务</div>
          <div class="enterpriseIconBox">
            <div class="enterpriseIconItem">
              <img src="~@/assets/images/enterprise-01.png" alt="风险预警" />
              <span>风险预警</span>
            </div>
            <div class="enterpriseIconItem">
              <img src="~@/assets/images/enterprise-02.png" alt="司法信用" />
              <span>司法信用</span>
            </div>
            <div class="enterpriseIconItem">
              <img src="~@/assets/images/enterprise-03.png" alt="商标专利" />
              <span>商标专利</span>
            </div>
            <div class="enterpriseIconItem">
              <img src="~@/assets/images/enterprise-04.png" alt="招标投标" />
              <span>招标投标</span>
            </div>
          </div>
        </template>
      </div>
    </div>
    <footer-tpl />
    <!--关注成功提示-->
    <el-dialog v-dialogDragBox title="提示" :visible.sync="open" width="750px" class="custom-dialog" :close-on-click-modal="false" :close-on-press-escape="false">
      <div class="collectBox">
        <i class="el-icon-success collectBoxIcon"></i>
        <span class="collectBoxDesc">关注成功，关注后企业信息变更可以随时查看</span>
        <span class="collectBoxTip">查看企业请打开用户管理后台>企业风控>关注企业</span>
        <span class="collectBoxBtn" @click="handleGo">去看看</span>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import HeaderTpl from '@/views/components/header'
import FooterTpl from '@/views/components/footer'
import { getToken } from '@/utils/auth'
import { getCompanyList } from '@/api/tender'
import { removeHtmlTag } from '@/utils'
import { enterpriseCollection, enterpriseCollectionDetail, enterpriseCollectionCancel, enterpriseCollectionExist } from '@/api/payment'

export default {
  components: { FooterTpl, HeaderTpl },
  data() {
    return {
      isLogin: !!getToken(),
      isSearch: false,
      companyName: undefined,
      loading: true,
      list: [],
      collectNum: 0,
      open: false,
      historySearch: []
    }
  },
  created() {
    // 查询历史搜索缓存
    this.historySearch = JSON.parse(localStorage.getItem('searchHistory')) || []
    enterpriseCollectionExist().then(res => {
      const { code, data } = res
      if (code === 200 && data) this.collectNum = !!data
    })
  },
  methods: {
    removeHtmlTag,
    // 查询
    handleQuery() {
      if (!this.companyName) {
        this.isSearch = false
        return
      }
      let fullscreenLoading
      if (!this.list.length) {
        fullscreenLoading = this.$loading({
          lock: true,
          text: '搜索中...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        })
      }
      this.loading = true
      getCompanyList({ companyName: this.companyName }).then(async res => {
        if (res.length) {
          await Promise.all(
            res.map(async item => {
              const { companyCreditCode } = item
              if (companyCreditCode) {
                const { code, data } = await enterpriseCollectionDetail({ taxNo: companyCreditCode })
                if (code === 200) item.isCollect = data
              }
            })
          )
        }
        this.list = res
        this.isSearch = true
        const searchHistory = JSON.parse(localStorage.getItem('searchHistory')) || []
        if (searchHistory.length >= 20) searchHistory.pop()
        if (!searchHistory.find(item => item.value === this.companyName)) {
          searchHistory.unshift({ value: this.companyName })
          localStorage.setItem('searchHistory', JSON.stringify(searchHistory))
        }
        this.historySearch = searchHistory
        this.loading = false
        fullscreenLoading.close()
      }).finally(() => {
        this.loading = false
        fullscreenLoading.close()
      })
    },
    // 新窗口查看详情
    handleDetail(item) {
      const companyName = item.companyName
      const url = this.$router.resolve({
        path: '/enterprise/detail',
        query: {
          companyName,
          isDialog: false
        }
      })
      window.open(url.href, '_blank')
    },
    // 收藏企业
    // prettier-ignore
    handleCollect(info, index) {
      const { companyCreditCode } = info
      if (!companyCreditCode) return
      const data = JSON.stringify({ ...info, collectTime: new Date().getTime() })
      this.$confirm('确定关注该企业吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        enterpriseCollection({ taxNo: companyCreditCode, data }).then(res => {
          const { code, msg } = res
          if (code === 200) {
            this.collectNum++
            if (this.collectNum > 1) this.$message.success('关注成功')
            else this.open = true
            this.$set(this.list, index, { ...info, isCollect: true })
          } else this.$message.error(msg)
        })
      }).catch(() => { })
    },
    // 取消关注
    // prettier-ignore
    handleCancel(info, index) {
      const { companyCreditCode } = info
      this.$confirm('确定取消关注该企业吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        enterpriseCollectionCancel({ taxNo: companyCreditCode }).then(res => {
          const { code, msg } = res
          if (code === 200) {
            this.$message.success('成功取消关注')
            this.collectNum--
            this.$set(this.list, index, { ...info, isCollect: false })
          } else this.$message.error(msg)
        })
      }).catch(() => { })
    },
    // 关注成功提示
    handleGo() {
      this.open = false
      this.$router.push({ path: '/oa/businessRisk', query: { view: 'collect' } })
    },
    querySearch(queryString, cb) {
      const historySearch = this.historySearch
      const results = queryString ? historySearch.filter(this.createFilter(queryString)) : historySearch
      cb(results)
    },
    createFilter(queryString) {
      return historySearch => {
        return historySearch.value.toLowerCase().indexOf(queryString.toLowerCase()) > -1
      }
    },
    // 删除历史搜索
    handleDelete(row) {
      const searchHistory = this.historySearch
      const index = searchHistory.findIndex(item => item.value === row.value)
      if (index > -1) searchHistory.splice(index, 1)
      localStorage.setItem('searchHistory', JSON.stringify(searchHistory))
      this.historySearch = searchHistory
    }
  }
}
</script>
<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';
.container-box {
  min-width: 1200px;
}
.containerBox {
  width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
}
.product-search {
  margin: 30px auto;
}
.enterprise {
  padding-bottom: 30px;
  &Item {
    cursor: pointer;
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    background-color: #ffffff;
    border: 1px solid #ebedf3;
    padding: 30px 30px 20px;
    transition: all 0.3s;
    position: relative;
    z-index: 1;
    &:hover {
      box-shadow: 0 1px 25px 0 rgba(0, 0, 0, 0.28);
      z-index: 2;
      .enterpriseMore {
        background-color: #2e73f3;
        color: #ffffff;
      }
    }
  }
  &Logo {
    width: 90px;
    height: 90px;
    border-radius: 10px;
    margin-right: 20px;
  }
  &Info {
    width: 100%;
    flex: 1;
    overflow: hidden;
  }
  &Name {
    display: flex;
    align-items: center;
    font-size: 24px;
    font-weight: 500;
    line-height: 44px;
    color: #333333;
    margin-bottom: 5px;
    & em {
      display: inline-block;
      padding: 0 17px;
      background-color: #31c776;
      color: #ffffff;
      border-radius: 5px;
      margin-left: 20px;
      font-style: normal;
      font-size: 14px;
      line-height: 30px;
    }
  }
  &Flex {
    display: flex;
    align-items: center;
  }
  &InfoItem {
    display: flex;
    align-items: center;
    font-size: 16px;
    color: #666666;
    margin-right: 20px;
    padding: 5px 0;
    & img {
      height: 18px;
      margin-right: 5px;
    }
  }
  &DescItem {
    display: flex;
    align-items: center;
    margin-right: 20px;
    padding: 5px 0;
    & span {
      font-size: 12px;
      color: #666666;
    }
    & b {
      font-size: 14px;
      font-weight: 400;
      color: #333333;
      &.primary {
        color: #2e73f3;
      }
    }
  }
  &More {
    font-size: 24px;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #ebedf3;
    color: #a9a9aa;
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    transition: all 0.3s;
  }
  &Collect {
    position: absolute;
    right: 20px;
    top: 20px;
  }
}
::v-deep {
  .el-empty__description {
    display: none;
  }
  .el-empty__bottom {
    margin: 0;
    font-size: 14px;
    color: #909399;
  }
}
.companySearchBox {
  width: 100%;
  position: relative;
  .companyCollect {
    position: absolute;
    right: 0;
    top: 37px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 15px;
    height: 36px;
    background-color: #ffffff;
    color: #2e73f3;
    border: 1px solid #2e73f3;
    font-size: 14px;
    cursor: pointer;
    border-radius: 50px;
    transition: all 0.3s;
    &:hover {
      background-color: #2e73f3;
      color: #ffffff;
      padding-right: 4px;
      &:after {
        content: '';
        display: inline-block;
        width: 28px;
        height: 28px;
        margin-left: 10px;
        background: url('~@/assets/images/companyCollect.png') no-repeat center;
        background-size: 100% 100%;
      }
    }
  }
}
.collectBox {
  padding: 0 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  &Icon {
    font-size: 50px;
    color: #2e73f3;
    margin-top: 10px;
  }
  &Desc {
    font-size: 14px;
    line-height: 20px;
    color: #333333;
    margin-top: 16px;
  }
  &Tip {
    font-size: 12px;
    line-height: 20px;
    color: #2e73f3;
    margin-top: 10px;
  }
  &Btn {
    width: 270px;
    height: 50px;
    line-height: 50px;
    background-color: #2f74f3;
    color: #ffffff;
    font-size: 16px;
    border-radius: 5px;
    text-align: center;
    margin-top: 45px;
    margin-bottom: 10px;
    cursor: pointer;
    transition: all 0.3s;
    &:hover {
      opacity: 0.8;
    }
  }
}
.enterpriseIconTitle {
  font-size: 18px;
  line-height: 21px;
  margin: 18px auto;
  color: $info;
  text-align: center;
}
.enterpriseIconBox {
  display: flex;
  align-items: center;
  justify-content: space-around;
  background-color: $white;
  border-radius: 5px;
  margin-bottom: 30px;
  .enterpriseIconItem {
    width: 25%;
    height: 180px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    img {
      max-width: 42px;
      max-height: 42px;
    }
    span {
      font-size: 24px;
      line-height: 28px;
      color: $font;
      margin-top: 15px;
    }
  }
}
.custom-autocomplete {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
</style>
