<template>
  <div>
    <div class="webHeader">
      <div class="webContainer flex">
        <div class="webHeader-logo" @click="handleToIndex">
          <i class="ssfont ss-diy-logo"></i>
          <div>
            <b>自由客紧固件</b>
            <span>一站式采购平台</span>
          </div>
        </div>
        <div class="webHeader-search" v-if="showSearch">
          <el-select v-model="searchType" class="webHeader-search-select" v-if="isPromotion">
            <el-option v-for="item in searchOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
          <el-input v-model="searchQuery.keyword" class="webHeader-search-input" :placeholder="isPromotion ? formatPlaceholder : '请输入产品名称/型号/规格/材质/表面处理 用逗号隔开 例如：螺丝,304,六角'" @keyup.enter.native="handleQuery"></el-input>
          <button type="button" class="webHeader-search-btn" @click="handleQuery">
            <i class="el-icon-search"></i>
            搜索
          </button>
        </div>
        <div class="webHeader-login">
          <div class="webHeader-app">
            <img src="~@/assets/images/icon-app.png" width="22" height="22" alt="" />
            <div class="webHeader-app-info">
              <img src="~@/assets/images/qrcode-app.png" width="110" height="110" alt="" />
              <span>扫码下载APP</span>
            </div>
          </div>
          <template v-if="(userType === '00' || userType === '05') && isLogin">
            <chat ref="chat" />
            <el-image :src="avatar" fit="cover" class="webHeader-login-img"></el-image>
          </template>
          <span class="webHeader-login-name" @click="handleToLogin()" v-if="userType === '00' || userType === '05'">{{ isLogin ? '管理中心' : '用户登录' }}</span>
          <span class="webHeader-login-name" @click="handleBinding()" v-if="userType === '01'">{{ isLogin ? '绑定手机' : '用户登录' }}</span>
          <template v-if="userType === '01' && isLogin">
            <span class="webHeader-login-name" style="margin: 0 10px">|</span>
            <span class="webHeader-login-name" @click="handleLoginOut()">退出登录</span>
          </template>
        </div>
      </div>
    </div>
    <div class="webNav">
      <div class="webContainer flex">
        <div class="webNav-item" :class="{ active: path === '/' }" @click="toJump()">首页</div>
        <div class="webNav-item" :class="{ active: path.includes('news') }" @click="toJump('news')">资讯</div>
        <div class="webNav-item" :class="{ active: path.includes('company') }" @click="toJump('company')">品牌墙</div>
        <div class="webNav-item" :class="{ active: path.includes('product') }" @click="toJump('product')">产品墙</div>
        <div class="webNav-item" :class="{ active: path.includes('tender') }" @click="toJump('tender')">招投标</div>
        <div class="webNav-item" :class="{ active: path.includes('deadstock') }" @click="toJump('deadstock')">滞销品</div>
        <div class="webNav-item" :class="{ active: path.includes('sale') }" @click="toJump('sale')">促销品</div>
        <div class="webNav-item" :class="{ active: path.includes('customZone') }" @click="toJump('customZone')">定制区</div>
        <div class="webNav-item" :class="{ active: path.includes('enterprise') }" @click="toJump('enterprise')">查企业</div>
        <div class="webNav-item" :class="{ active: path.includes('bidding') }" @click="toJump('bidding')">招标/采购需求</div>
        <div class="webNav-item" :class="{ active: path.includes('procurementZone') }" @click="toJump('procurementZone')">求购专区</div>
        <div class="webNav-item" :class="{ active: path.includes('standard') }" @click="toJump('standard')" v-if="isStandard || (!isStandard && companyId == 14)">查标准</div>
        <div class="webNav-item" :class="{ active: path.includes('dismantle') }" @click="toJump('dismantle')">拆零工具</div>
      </div>
    </div>
    <!-- 搜索结果 -->
    <el-dialog v-dialogDragBox :visible.sync="searchOpen" width="85%" :custom-class="'searchBox'" lock-scroll>
      <div class="slot-title" slot="title">
        <div class="webHeader-search searchBox-input">
          <el-input v-model="searchQuery.keyword" placeholder="请输入产品名称/型号/规格/材质/表面处理 用逗号隔开 例如：螺丝,304,六角" @keyup.enter.native="handleQuery"></el-input>
          <button type="button" class="webHeader-search-btn" @click="handleQuery">
            <i class="el-icon-search"></i>
            搜索
          </button>
        </div>
        <right-toolbar :search="false" @queryTable="getSearchList" :columns="columns" isSetitem @updateColumns="updateColumns"></right-toolbar>
      </div>
      <div @click="clickHide">
        <el-table ref="searchList" :max-height="searchHeight" stripe :data="searchList" row-key="id" style="width: 100%; border-radius: 0 !important" class="custom-table custom-table-filter" :class="{ 'custom-table-cell5': columns[1].visible }" @selection-change="handleSelectionChange">
          <el-table-column align="center" type="selection" width="55" :selectable="selectable" v-if="supplierInfo && supplierInfo.company && supplierInfo.company.id"></el-table-column>
          <el-table-column align="center" type="index" label="序号"></el-table-column>
          <el-table-column align="center" prop="company" label="供应商" show-overflow-tooltip v-if="isLogin && showSupplierList"></el-table-column>
          <el-table-column align="center" prop="productName" label="产品名称" show-overflow-tooltip min-width="130" :filters="nameFilters" :filter-method="filterName" v-if="columns[0].visible">
            <template slot-scope="{ row }">
              <span class="table-link" @click="handleView(row)">{{ row.productName }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="picture1" label="图片" width="75" v-if="columns[1].visible">
            <template slot-scope="{ row }">
              <el-image :src="formatProductImg(row)" fit="cover" @click="handleImgView(row)">
                <div slot="error" class="image-slot">
                  <i class="el-icon-picture-outline"></i>
                </div>
              </el-image>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="specs" label="规格" show-overflow-tooltip :filters="specsFilters" :filter-method="filterSpecs" v-if="columns[2].visible"></el-table-column>
          <el-table-column align="center" prop="model" label="型号" show-overflow-tooltip :filters="modelFilters" :filter-method="filterModel" v-if="columns[3].visible"></el-table-column>
          <el-table-column align="center" prop="productCode" label="产品编码" show-overflow-tooltip v-if="columns[4].visible"></el-table-column>
          <el-table-column align="center" label="产品报价" v-if="isLogin">
            <template slot-scope="{ row }">
              <span class="table-price pointer" v-if="!!row.price" @click="handleOffer(row)">{{ '￥' + row.price + '元' }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="materialQuality" label="材质" show-overflow-tooltip :filters="materialFilter" :filter-method="filterQuality" v-if="columns[5].visible"></el-table-column>
          <el-table-column align="center" prop="surface" label="表面" show-overflow-tooltip :filters="surfaceFilters" :filter-method="filterSurface" v-if="columns[6].visible"></el-table-column>
          <el-table-column align="center" prop="unit" label="单位" width="60" :filters="unitFilters" :filter-method="filterUnit" v-if="columns[7].visible"></el-table-column>
        </el-table>
        <div class="search-more" v-if="showMoreQuery">
          <div class="search-more-btn" @click="handleMoreQuery">
            <i class="el-icon-more"></i>
            <span>点击查看更多供应商报价产品</span>
            <i class="el-icon-more"></i>
          </div>
        </div>
      </div>
    </el-dialog>

    <div class="collectAll" v-if="selectedList.length && showContract">
      <div class="collectAll-box">
        <div class="collectAll-title">已选择 {{ selectedList.length }} 项</div>
        <el-tooltip content="点击生成合同" placement="top" effect="dark">
          <div class="collectAll-btn">
            <span @click="handleGenerateContract">生成合同</span>
          </div>
        </el-tooltip>
        <el-tooltip content="退出" placement="top" effect="dark">
          <div class="collectAll-close" @click="handleClear">
            <i class="el-icon-close"></i>
          </div>
        </el-tooltip>
      </div>
    </div>

    <product-dialog ref="productInfo" append-body></product-dialog>
    <!-- 报价信息 -->
    <offer-dialog ref="offerInfo" append-body></offer-dialog>
    <!--  绑定手机  -->
    <binding-dialog ref="bindingInfo" append-body></binding-dialog>
    <!--  生成合同  -->
    <offer-contract-dialog ref="contractDialog"></offer-contract-dialog>
    <!-- 全局翻译 -->
    <global-translate />
  </div>
</template>

<script>
import ItemTpl from '@/views/public/product/item'
import { mapGetters } from 'vuex'
import { getProductsByName } from '@/api/system/product'
import ProductDialog from '@/views/public/product/dialog.vue'
import { gygs } from '@/api/houtai/formula'
import OfferDialog from '@/views/public/product/offer'
import Chat from '@/components/Chat'
import BindingDialog from '@/views/public/binding'
import { getToken } from '@/utils/auth'
import Cookies from 'js-cookie'
import { supplierList, getProductsQuote } from '@/api/system/company'
import { quotesel } from '@/api/houtai/formula'
import offerContractDialog from '@/views/purchase/demandForMe/offer'

export default {
  props: {
    isLogin: {
      type: Boolean,
      default: false
    },
    collectList: {
      type: Array,
      default: () => []
    },
    isSearch: {
      type: Boolean,
      default: true
    },
    showSearch: {
      type: Boolean,
      default: true
    },
    isPromotion: {
      type: Boolean,
      default: false
    }
  },
  components: { BindingDialog, OfferDialog, ProductDialog, ItemTpl, Chat, offerContractDialog },
  data() {
    return {
      // 头部搜索
      searchQuery: {
        pageNum: 1,
        pageSize: 200,
        keyword: undefined
      },
      searchTotal: 0,
      searchList: [],
      searchOpen: false,
      searchPageSizes: [72, 144, 216, 288],
      // 当前选中产品ID
      productId: undefined,
      // 当前地址栏地址
      path: undefined,
      nameFilters: [],
      specsFilters: [],
      modelFilters: [],
      materialFilter: [],
      surfaceFilters: [],
      unitFilters: [],
      windowHeight: undefined,
      searchHeight: undefined,
      isStandard: false,
      supplierInfo: {}, // 供应商信息
      selectedList: [], // 选中产品
      showContract: false, // 是否显示生成合同
      // 是否显示供应商列表
      showSupplierList: false,
      // 显隐列
      columns: [
        { key: 0, label: `产品名称`, visible: true },
        { key: 1, label: `图片`, visible: true },
        { key: 2, label: `规格`, visible: true },
        { key: 3, label: `型号`, visible: true },
        { key: 4, label: `产品编码`, visible: true },
        { key: 5, label: `材质`, visible: true },
        { key: 6, label: `表面`, visible: true },
        { key: 7, label: `单位`, visible: true }
      ],
      // 查询供应商报价产品条件
      showMoreQuery: false,
      supplierQuery: {
        companyId: undefined,
        pageNum: 1,
        pageSize: 20
      },
      // 促销品分类查询
      searchType: 'productName',
      searchOptions: [
        { label: '产品名称', value: 'productName' },
        { label: '产品规格', value: 'specs' },
        { label: '产品型号', value: 'model' },
        { label: '表面处理', value: 'surface' },
        { label: '供应商', value: 'companyName' }
      ]
    }
  },
  watch: {
    windowHeight(val) {
      this.searchHeight = val * 0.88 - 100
    }
  },
  mounted() {
    this.windowHeight = document.documentElement.clientHeight
    window.onresize = () => {
      this.windowHeight = document.documentElement.clientHeight
    }
    if (!!getToken() && this.userType !== '00') this.handleBinding()
  },
  beforeDestroy() {
    window.onresize = null
  },
  created() {
    const columns = localStorage.getItem(this.userId + '.searchColumns')
    if (columns) {
      this.columns = JSON.parse(columns)
    }
    this.getConfigKey('standard.switch').then(res => {
      this.isStandard = res.msg == 'true'
    })
    this.path = this.$route.path
  },
  computed: {
    ...mapGetters(['avatar']),
    userType() {
      return this.$store.getters.userType || '00'
    },
    companyId() {
      return this.$store.getters.info.companyId
    },
    // 获取当前用户ID
    userId() {
      return this.$store.getters.info.userId
    },
    formatPlaceholder() {
      return '请输入' + this.searchOptions.find(item => item.value === this.searchType)?.label || '产品名称/型号/规格/材质/表面处理 用逗号隔开 例如：螺丝,304,六角'
    }
  },
  methods: {
    // 更新列
    updateColumns(columns) {
      localStorage.setItem(this.userId + '.searchColumns', JSON.stringify(columns))
    },
    // 去首页
    handleToIndex() {
      this.$router.push('/')
    },
    // 去登录
    handleToLogin() {
      if (this.isLogin) {
        this.$router.push('/index')
      } else {
        this.$router.push('/login')
      }
    },
    // 搜索
    handleQuery() {
      if (!!getToken() && (this.userType !== '00' && this.userType !== '05')) {
        this.handleBinding()
        return
      }
      if (this.isPromotion) {
        this.$emit('handleQuery', { keyword: this.searchQuery.keyword, type: this.searchType })
      } else {
        if (this.isSearch) {
          this.searchQuery.pageNum = 1
          this.supplierQuery.pageNum = 1
          this.searchList = []
          this.getSearchList()
        } else {
          this.$parent.handleQuery(this.searchQuery.keyword)
        }
      }
    },
    // 搜索结果列表
    // prettier-ignore
    async getSearchList() {
      if (!this.searchQuery.keyword) {
        return false
      }
      this.showSupplierList = false
      this.showMoreQuery = false
      const loading = this.$loading({
        lock: true,
        text: '搜索中…',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      
      try {
        const getProductsByNameData = await getProductsByName(this.searchQuery)
        const { rows: getProductsByNameList } = getProductsByNameData
        if (getProductsByNameList.length && !!getToken()) {
          try {
            await Promise.all(
              getProductsByNameList.map(async item => {
                try {
                  const price = await gygs({ productId: item.id })
                  item.price = price.data?.min || 0
                } catch (error) {
                  console.error(`获取产品${item.id}价格失败`, error)
                  item.price = 0
                }
              })
            )
          } catch (error) {
            console.error('获取产品价格列表失败', error)
          }
        }
        
        let list = []
        if(this.isLogin){
          try {
            const companyList = await supplierList({ keyword: this.searchQuery.keyword })
            this.showSupplierList = companyList.total > 0
            this.showMoreQuery = companyList.total == 1
            const companyIds = companyList.rows.map(item => item?.company?.id).filter(Boolean)
            const productList = await Promise.all(companyIds.map(async id => {
              let query = {}
              if(companyList.rows.length == 1){
                this.supplierQuery.companyId = id
                query = this.supplierQuery
              }
              else query = {companyId:id,pageSize:30}
              const productRes = await getProductsQuote(query)
              const productQuoteList = productRes.rows || []
              try {
                const productQuoteInfo = await Promise.all(
                  productQuoteList.map(async ite => {
                    try {
                      const productQuoteInfo = await quotesel({ quoteId: ite.id })
                      return {...productQuoteInfo?.data, company: ite.companyName}
                    } catch (error) {
                      console.error(`获取报价详情失败, quoteId: ${ite.id}`, error)
                      return null
                    }
                  })
                )
                return productQuoteInfo.filter(Boolean)
              } catch (error) {
                console.error('处理产品报价列表失败', error)
                return []
              }
            })).then(results => results.flat())
            // 根据product.id去重
            const uniqueProductList = productList.filter(item => item && item.product).filter((item, index, self) => {
              return index === self.findIndex(p => p.product?.id === item.product?.id)
            }).map(item => {
              return {
                ...item.product,
                price: item.totalPrice || 0,
                company: item.company || '未知供应商'
              }
            })
            if(companyList.rows.length == 1) this.supplierInfo = companyList.rows[0]
            else this.supplierInfo = {}
            list = [...getProductsByNameList, ...uniqueProductList]
            this.showContract = false
          } catch (error) {
            console.error('获取产品报价列表失败', error)
          }
        } else list = getProductsByNameList
        const supplierId = this.supplierInfo?.company?.id
        if(supplierId) {
          try {
            // 循环请求gygs，判断结果quotes中的companyId的effectiveTime是否过期
            await Promise.all(
              list.map(async item => {
                try {
                  const price = await gygs({ productId: item.id })
                  const productQuoteInfo = price.data?.quotes || []
                  const supplierQuote = productQuoteInfo.find(quote => quote.companyId == supplierId)
                  item.checkPrice = supplierQuote?.price || 0
                  const effectiveTime = supplierQuote?.effectiveTime || undefined
                  if (effectiveTime) {
                    const effectiveTimeDate = new Date(effectiveTime)
                    const now = new Date()
                    item.isCheck = effectiveTimeDate > now
                  } else item.isCheck = true
                } catch (error) {
                  console.error(`获取产品${item.id}报价失败`, error)
                  item.checkPrice = 0
                  item.isCheck = false
                }
              })
            )
          } catch (error) {
            console.error('获取产品报价列表失败', error)
          }
        }

        // 根据id去重
        const uniqueList = list.reduce((all, next) => (all.some(item => item.id === next.id) ? all : [...all, next]), [])
        this.searchList = uniqueList
        this.searchTotal = uniqueList.length
        this.searchPageSizes.push(uniqueList.length)
        this._updateTableFilters(this.searchList)
        this.searchOpen = true
        this.productId = undefined
      } catch (error) {
        console.error('搜索列表获取失败', error)
      } finally {
        loading.close()
      }
    },
    // 点击加载更多供应商报价
    async handleMoreQuery() {
      this.supplierQuery.pageNum++
      const loading = this.$loading({
        lock: true,
        text: '加载中...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      try {
        const res = await getProductsQuote(this.supplierQuery)
        if (res.code === 200) {
          const productQuoteList = res.rows || []
          try {
            const productQuoteInfo = await Promise.all(
              productQuoteList.map(async ite => {
                try {
                  const productQuoteInfo = await quotesel({ quoteId: ite.id })
                  return { ...productQuoteInfo?.data, company: ite.companyName }
                } catch (error) {
                  console.error(`获取报价详情失败, quoteId: ${ite.id}`, error)
                  return null
                }
              })
            )
            // 根据product.id去重，过滤掉空值
            const uniqueProductList = productQuoteInfo
              .filter(Boolean)
              .filter(item => item.product)
              .filter((item, index, self) => {
                return index === self.findIndex(p => p.product?.id === item.product?.id)
              })
              .map(item => {
                return {
                  ...item.product,
                  price: item.totalPrice || 0,
                  company: item.company || '未知供应商'
                }
              })

            const supplierId = this.supplierQuery?.companyId
            try {
              await Promise.all(
                uniqueProductList.map(async item => {
                  try {
                    const price = await gygs({ productId: item.id })
                    const productQuoteInfo = price.data?.quotes || []
                    const supplierQuote = productQuoteInfo.find(quote => quote.companyId == supplierId)
                    item.checkPrice = supplierQuote?.price || 0
                    const effectiveTime = supplierQuote?.effectiveTime || undefined
                    if (effectiveTime) {
                      const effectiveTimeDate = new Date(effectiveTime)
                      const now = new Date()
                      item.isCheck = effectiveTimeDate > now
                    } else item.isCheck = true
                  } catch (error) {
                    console.error(`获取产品${item.id}报价失败`, error)
                    item.checkPrice = 0
                    item.isCheck = false
                  }
                })
              )
            } catch (error) {
              console.error('检查产品报价有效期失败', error)
            }
            // 合并现有数据并根据id去重
            if (uniqueProductList.length > 0) {
              const mergedList = [...this.searchList, ...uniqueProductList]
              this.searchList = this.uniqueJsonArrByField(mergedList, 'id')
              this._updateTableFilters(this.searchList)
              this.searchTotal = this.searchList.length
              this.showMoreQuery = res.total > this.searchList.length
            } else {
              this.$message.info('没有更多数据')
              this.showMoreQuery = false
            }
          } catch (error) {
            console.error('处理产品报价数据失败', error)
            this.$message.error('处理数据失败，请稍后重试')
          }
        } else {
          this.$message.error(res.msg || '查询失败')
        }
      } catch (error) {
        console.error('加载更多产品报价失败', error)
        this.$message.error('加载失败，请稍后重试')
      } finally {
        loading.close()
      }
    },
    // 数组去重
    uniqueJsonArrByField(jsonArr, field) {
      // 数组长度小于2 或 没有指定去重字段 或 不是json格式数据
      if (jsonArr.length < 2 || !field || typeof jsonArr[0] !== 'object') return jsonArr
      return jsonArr.reduce((all, next) => (all.some(item => item[field] === next[field]) ? all : [...all, next]), [])
    },
    // 更新表格筛选器
    _updateTableFilters(list) {
      const productName = this.uniqueJsonArrByField(list, 'productName')
      const specs = this.uniqueJsonArrByField(list, 'specs')
      const model = this.uniqueJsonArrByField(list, 'model')
      const materialQuality = this.uniqueJsonArrByField(list, 'materialQuality')
      const surface = this.uniqueJsonArrByField(list, 'surface')
      const unit = this.uniqueJsonArrByField(list, 'unit')
      this.nameFilters = productName.map(item => ({ text: item.productName, value: item.productName }))
      this.specsFilters = specs.map(item => ({ text: item.specs, value: item.specs }))
      this.modelFilters = model.map(item => ({ text: item.model, value: item.model }))
      this.materialFilter = materialQuality.map(item => ({
        text: item.materialQuality,
        value: item.materialQuality
      }))
      this.surfaceFilters = surface.map(item => ({ text: item.surface, value: item.surface }))
      this.unitFilters = unit.map(item => ({ text: item.unit, value: item.unit }))
    },
    // 点击搜索结果产品以外区域隐藏产品弹框
    clickHide() {
      this.productId = undefined
    },
    // 页面跳转
    toJump(val) {
      if (!!getToken() && (this.userType !== '00' && this.userType !== '05')) {
        this.handleBinding()
        return
      }
      val = val || ''
      this.$router.push(`/${val}`)
    },
    // 产品名称筛选
    filterName(value, row) {
      return row.productName === value
    },
    // 产品规格筛选
    filterSpecs(value, row) {
      return row.specs === value
    },
    // 产品型号筛选
    filterModel(value, row) {
      return row.model === value
    },
    // 产品材质筛选
    filterQuality(value, row) {
      return row.materialQuality === value
    },
    // 产品表面筛选
    filterSurface(value, row) {
      return row.surface === value
    },
    // 产品单位筛选
    filterUnit(value, row) {
      return row.unit === value
    },
    // 查看详情
    handleView(item) {
      this.$refs.productInfo.handleView(item)
    },
    handleImgView(item) {
      this.$refs.productInfo.handleImgView(item)
    },
    // 查看报价
    handleOffer(item) {
      item.offerNum = 1
      this.$refs.offerInfo.handleView(item)
    },
    // 绑定手机
    handleBinding() {
      if (!this.isLogin) {
        this.$router.push('/login')
      } else {
        this.$refs.bindingInfo.handleOpen()
      }
    },
    // 退出登录
    // prettier-ignore
    async handleLoginOut() {
      Cookies.remove('token')
      this.$confirm('是否确定注销当前登录，重新登录', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$store.dispatch('LogOut').then(() => {
          location.href = '/login'
        })
      }).catch(() => {})
    },
    // 是否可选择
    selectable(row, index) {
      if (this.supplierInfo?.company?.id) {
        return row.isCheck && row.checkPrice > 0
      }
      return false
    },
    // 选择
    handleSelectionChange(val) {
      this.selectedList = val
      this.showContract = this.selectedList.length > 0
    },
    // 生成合同
    handleGenerateContract() {
      const { company } = this.supplierInfo
      const sellerUser = {
        companyId: company.id,
        companyName: company.companyName,
        concat: company.contact,
        phone: company.phone
      }
      const product = this.selectedList.map(item => {
        return {
          maxNum: *********,
          source: 'common',
          productId: item.id,
          productName: item.productName,
          productCode: item.productCode,
          specs: item.specs,
          model: item.model,
          unit: item.unit,
          quantity: 0,
          needQuantity: 0,
          listId: -1,
          remark: item.remark,
          amount: item.checkPrice,
          originAmount: item.checkPrice,
          replyUnit: item.unit,
          sjNum: 0,
          replyRemark: '',
          isIncludingTax: false,
          endUnit: item.unit
        }
      })
      this.$refs.contractDialog.handleGetContract(sellerUser, product)
      this.showContract = false
    },
    // 清空
    handleClear() {
      this.selectedList = []
      this.showContract = false
      if (this.$refs.searchList) this.$refs.searchList.clearSelection()
    }
  }
}
</script>
<style lang="scss" scoped>
@import '~@/assets/styles/custom-public.scss';
@import '~@/assets/styles/custom-new.scss';
.collectAll {
  z-index: 3000;
}
::v-deep {
  .slot-title {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-right: 10px;
  }
  .search-more {
    text-align: center;
    &-btn {
      display: inline-block;
      padding-top: 20px;
      cursor: pointer;
      span {
        margin: 0 5px;
      }
      &:hover {
        color: #2e73f3;
      }
    }
  }
  .table-price {
    &:hover {
      text-decoration: underline;
    }
  }
  .searchBox {
    margin-bottom: 0 !important;
    &-input.webHeader-search {
      height: 40px;
      .el-input__inner {
        height: 40px;
      }
      .webHeader-search-btn {
        height: 30px;
        line-height: 30px;
        width: auto;
        padding: 0 15px;
      }
    }
  }
}
.custom-table-filter ::v-deep {
  .el-table__column-filter-trigger i {
    color: #2e73f3;
    transform: scale(1);
    font-size: 14px;
  }
}
.webHeader-login {
  margin-left: 20px;
  width: auto;
  flex-shrink: 0;
}
.webHeader-app {
  display: inline-flex;
  align-items: center;
  margin-right: 10px;
  cursor: pointer;
  position: relative;
  &-info {
    display: none;
    position: absolute;
    top: 40px;
    left: 50%;
    z-index: 99999;
    transform: translateX(-50%);
    padding: 15px 10px;
    border-radius: 10px;
    background: #ffffff;
    box-shadow: 0 1px 15px 0 rgba(0, 0, 0, 0.25);
    span {
      font-size: 12px;
      color: #333333;
    }
    &:before {
      content: '';
      width: 0;
      height: 0;
      border-width: 0 10px 10px 10px;
      border-color: transparent transparent #ffffff transparent;
      border-style: solid;
      position: absolute;
      top: -10px;
      left: 50%;
      transform: translateX(-50%);
    }
  }
  &:hover {
    .webHeader-app-info {
      display: block;
    }
  }
}
</style>
