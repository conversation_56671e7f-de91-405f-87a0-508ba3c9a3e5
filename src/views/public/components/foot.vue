<template>
  <div class="webFooter">
    <div class="webContainer">
      <div class="webFooter-company">
        <div class="webFooter-title">关于我们</div>
        <div class="webFooter-company-logo">
          <img src="~@/assets/logo/logo.png" alt="自由客紧固件" />
          <div class="webFooter-company-logo-flex">
            <b>自由客紧固件</b>
            <span>一站式采购平台</span>
          </div>
        </div>
        <div class="webFooter-company-t1">自由客紧固件致力于服务紧固件行业从业客户，集供应、采购于一身的一站式服务平台。</div>
        <div class="webFooter-company-t2">产品齐全、厂家众多、一手资源、报价透明</div>
        <div class="webFooter-company-t3">现诚招入驻、就等你来；</div>
        <div class="webFooter-company-t4">买光伏支架，就上自由客！</div>
      </div>
      <div class="webFooter-nav">
        <div class="webFooter-title">功能特点</div>
        <div style="display: flex; margin-top: 5px">
          <div class="webFooter-nav-list">
            <span class="webFooter-nav-item" @click="toTop">找产品</span>
            <span class="webFooter-nav-item" @click="toJump('news')">行业资讯K线图</span>
            <span class="webFooter-nav-item" @click="toJump('tender')">招投标</span>
            <span class="webFooter-nav-item" @click="toJump('company')">品牌墙</span>
            <span class="webFooter-nav-item" @click="toJump('product')">产品墙</span>
            <span class="webFooter-nav-item" @click="toJump('deadstock')">滞销品</span>
          </div>
          <div class="webFooter-nav-list">
            <span class="webFooter-nav-item" @click="toJump('sale')">促销品</span>
            <span class="webFooter-nav-item" @click="toJump('customZone')">定制产品</span>
            <span class="webFooter-nav-item" @click="toJump('enterprise')">企业查询</span>
            <span class="webFooter-nav-item" @click="toJump('bidding')">招标/采购需求</span>
            <span class="webFooter-nav-code">
              <img src="~@/assets/images/qrcode-app.png" alt="" />
            </span>
          </div>
        </div>
      </div>
      <div class="webFooter-about">
        <div class="webFooter-title">关于</div>
        <div class="webFooter-about-info">
          <div class="webFooter-about-info-icon">
            <img src="~@/assets/images/foot-service.png" alt="关于" />
          </div>
          <div class="webFooter-about-info-box">
            <span class="webFooter-about-info-item">
              <span style="color: #999999">电话：</span>
              15188838176
            </span>
            <span class="webFooter-about-info-item">
              <span style="color: #999999">&nbsp;&nbsp; Q Q：</span>
              121822918
            </span>
            <span class="webFooter-about-info-item">
              <span style="color: #999999">邮箱：</span>
              <EMAIL>
            </span>
          </div>
        </div>
        <div class="webFooter-about-edit">
          <span>给我们建议</span>
          <button type="button" @click="handleFeedback">
            <i class="el-icon-edit"></i>
            写点啥
          </button>
        </div>
      </div>
    </div>
    <div class="webFooter-copyright">
      <p>
        自由客紧固件 | 备案号：
        <a href="https://beian.miit.gov.cn/" target="_blank">冀ICP备2020031367号-5</a>
        <span @click="handleHistory" class="webFooter-copyright-link">更新历史</span>
      </p>
    </div>

    <div class="navigation">
      <div class="navigation-item">
        <span @click="toSkip('collect')">收藏</span>
        <span @click="toSkip('product')">产品</span>
        <span @click="toSkip('demand')">采购</span>
        <span @click="toSkip('addition')">报价</span>
      </div>
      <div class="navigation-backtop" @click="toTop()">
        <img src="~@/assets/images/top.png" />
      </div>
    </div>

    <Update ref="update" initiative v-if="isUpdate" />

    <div class="service" v-if="serviceOpen">
      <i class="el-icon-error service-icon" @click="closeService"></i>
      <img src="~@/assets/images/qrcode.png" width="100" height="100" alt="" />
      <b>在线客服</b>
      <span>周一至周日</span>
      <span>早08:30—晚18:30</span>
    </div>
    <!--    产品定制-->
    <customiZation ref="customiZation"></customiZation>
    <!--  绑定手机  -->
    <binding-dialog ref="bindingInfo" append-body></binding-dialog>
    <!--反馈意见-->
    <el-dialog v-dialogDragBox title="给我们建议" :visible.sync="feedbackOpen" width="750px" class="custom-dialog">
      <div style="padding: 0 20px">
        <el-form :model="feedbackForm" :rules="feedbackRules" ref="feedbackForm">
          <el-form-item label="" prop="suggestion">
            <el-input type="textarea" v-model="feedbackForm.suggestion" :autosize="{ minRows: 5, maxRows: 8 }" resize="none" placeholder="请输入您的建议"></el-input>
          </el-form-item>
          <el-form-item label="" prop="captcha">
            <div class="captchaBox">
              <el-input class="captchaBoxInput" v-model="feedbackForm.captcha" placeholder="请输入验证码"></el-input>
              <div class="captchaBoxBtn" @click="refreshCaptcha" title="点击刷新验证码">
                <captcha ref="verify" @getCode="getCode" :strict="false" :width="100" :height="40" />
              </div>
            </div>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button class="custom-dialog-btn" @click="feedbackOpen = false">取 消</el-button>
        <el-button class="custom-dialog-btn primary" @click="handleFeedbackSubmit">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import Cookies from 'js-cookie'
import customiZation from '@/views/customization/create'
import { getToken } from '@/utils/auth'
import BindingDialog from '@/views/public/binding/index'
import Captcha from '@/components/captcha/index'
import { addSuggestion } from '@/api/system/suggestion'

export default {
  components: { Captcha, BindingDialog, customiZation },
  data() {
    return {
      isUpdate: false,
      feedbackOpen: false,
      feedbackForm: {},
      feedbackRules: {
        suggestion: [{ required: true, message: '请输入您的建议', trigger: 'blur' }],
        captcha: [
          { required: true, message: '请输入验证码', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (this.captcha !== value) {
                callback(new Error('请输入正确的验证码'))
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ]
      },
      captcha: undefined,
      serviceOpen: true
    }
  },
  computed: {
    userType() {
      return this.$store.getters.userType || '00'
    }
  },
  created() {
    this.serviceOpen = Cookies.get('serviceOpen') != 'false'
  },
  methods: {
    toSkip(val) {
      if (!!getToken() && (this.userType !== '00' && this.userType !== '05')) {
        this.$refs.bindingInfo.handleOpen()
        return
      }
      switch (val) {
        case 'collect':
          this.$router.push({
            path: '/platform/collect'
          })
          break
        case 'product':
          this.$router.push({
            path: '/platform/products'
          })
          break
        case 'demand':
          this.$router.push({
            path: '/demand/order'
          })
          break
        case 'addition':
          this.$router.push({
            path: '/offer/offerlist'
          })
          break
      }
    },
    toTop() {
      document.documentElement.scrollTop = 0
    },
    handleHistory() {
      this.isUpdate = true
      this.$nextTick(() => {
        this.$refs.update.getUpdateList()
      })
    },
    // 产品定制
    handleAddProduct() {
      this.$refs.customiZation.create()
    },
    // 页面跳转
    toJump(val) {
      if (!!getToken() && (this.userType !== '00' && this.userType !== '05')) {
        this.handleBinding()
        return
      }
      val = val || ''
      this.$router.push(`/${val}`)
    },
    // 新增意见
    handleFeedback() {
      if (!getToken()) {
        this.$router.push('/login')
        return
      }
      this.feedbackForm = { suggestion: undefined, captcha: undefined }
      this.resetForm('feedbackForm')
      this.feedbackOpen = true
      this.$nextTick(() => {
        this.refreshCaptcha()
      })
    },
    // 获取验证码
    getCode(code) {
      this.captcha = code
    },
    // 刷新验证码
    refreshCaptcha() {
      this.$refs.verify.display()
    },
    // 提交意见
    handleFeedbackSubmit() {
      this.$refs.feedbackForm.validate(valid => {
        if (valid) {
          addSuggestion({ suggestion: this.feedbackForm.suggestion }).then(res => {
            this.$message.success('提交成功')
            this.feedbackOpen = false
          })
        }
      })
    },
    // 关闭客服
    closeService() {
      Cookies.set('serviceOpen', 'false', { path: '/' })
      this.serviceOpen = false
    }
  }
}
</script>
<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
@import '~@/assets/styles/custom-public.scss';
.service {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 999;
  background-color: white;
  padding: 10px;
  border-radius: 5px;
  box-shadow: 0 1px 18px 0 rgba(0, 0, 0, 0.29);
  b {
    font-size: 16px;
    line-height: 21px;
    font-weight: normal;
    color: #666666;
  }
  span {
    font-size: 13px;
    line-height: 17px;
    color: #cccccc;
  }
  &-icon {
    font-size: 20px;
    position: absolute;
    top: -10px;
    right: -10px;
    cursor: pointer;
  }
}
::v-deep {
  .el-textarea__inner {
    font: inherit;
    line-height: 1.5 !important;
  }
  .captchaBox {
    display: flex;
    align-items: center;
    &Input {
      width: 200px;
    }
    &Btn {
      display: inline-flex;
      align-items: center;
      height: 40px;
      margin-left: 10px;
      cursor: pointer;
    }
  }
}
@media screen and (max-width: 1200px) {
  .service {
    display: none;
  }
}
</style>
