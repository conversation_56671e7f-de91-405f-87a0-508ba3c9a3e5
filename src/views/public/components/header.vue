<template>
  <div>
    <div class="head">
      <div>
        <div style="width: 277px; flex-shrink: 0">
          <img src="../../../../public/imgs/<EMAIL>" style="width: 52px; height: 50px; margin: auto" />
          <div style="margin: auto; font-size: 16px; color: white"><span style="font-size: 19px;">自由客紧固件</span><br>一站式采购平台</div>
        </div>
        <div style="width: 800px" :style="path.includes('product')||path.includes('company') ? 'display:none' : ''">
          <div style="width: 666px; height: 50px; background-color: white; margin: auto; border-radius: 100px; position: relative; overflow: hidden">
            <div @click="search()" style="cursor: pointer; width: 117px; height: 40px; background-color: #2e73f3; border-radius: 100px; position: absolute; right: 5px; top: 5px; display: flex">
              <img src="../../../../public/imgs/搜索 <EMAIL>" style="width: 20px; height: 20px; margin-left: 29px; margin-top: 10px" />
              <div style="margin: auto; font-size: 16px; color: white; line-height: 20px; margin-left: 7px">搜索</div>
            </div>
            <input v-model="searchQuery.keyword" placeholder="请输入产品名称/型号/规格/材质/表面处理 用逗号隔开 例如：螺丝,304,六角" style="width: 100%; height: 100%; border: 0; outline: none; text-indent: 25px; color: #999999" />
          </div>
        </div>
        <div style="width: 185px; flex-shrink: 0" v-if="!islogin">
          <div style="margin: auto; font-size: 16px; color: white; cursor: pointer" @click="login()">用户登录</div>
        </div>
        <div style="width: 185px; flex-shrink: 0" v-else @click="toindex()">
          <img src="../../../../public/imgs/消息 (1) <EMAIL>" style="width: 22px; height: 22px; margin: auto" />
          <div style="width: 50px; height: 50px; border-radius: 100px; margin: auto; overflow: hidden">
            <img :src="avatar" style="width:100%;height:100%;">
          </div>
          <div style="margin: auto; font-size: 16px; color: white; cursor: pointer">管理中心</div>
        </div>
      </div>
      <!-- 搜索结果 -->
      <el-dialog v-dialogDragBox :visible.sync="isOpen" width="85%" top="30vh" append-to-body>
        <div class="productbox" @click.self="handleClick()" @contextmenu.prevent>
          <div class="productbox2" @click.self="handleClick()">
            <div v-for="item in searchList" :key="item.id" class="productItem pointer" style="width: 104px">
              <div class="productItem-img" @click="product_id = product_id == item.id ? null : item.id">
                <img :src="formatProductImg(item)" class="hover" />
              </div>
              <div class="productItem-name">{{ item.productName }}</div>
              <div class="productItem-name2">{{ item.specs }}</div>
              <template v-if="item.id == product_id">
                <item-tpl :desc-info="item" :view-num="12" :is-child="true" />
              </template>
            </div>
            <div class="pagination-box">
              <pagination v-show="searchTotal > 0" :total="searchTotal" :pageSizes.sync="pageSizes" :page.sync="searchQuery.pageNum" :limit.sync="searchQuery.pageSize" @pagination="getProductsByName" />
            </div>
          </div>
        </div>
      </el-dialog>
      <!-- 图纸预览 -->
      <el-dialog v-dialogDragBox title="图纸" :visible.sync="pdfshow" width="900px" append-to-body style="z-index: 999" class="disable-context-menu">
        <!-- <iframe :src="pdfUrl + '?page=hsn#toolbar=0'" style="width: 100%; height: 500px" frameborder="0" id="ifm"></iframe> -->
        <div style="display: flex; justify-content: space-between; align-items: center; padding-bottom: 10px" v-if="pdfCount > 1">
          <el-button type="primary" size="small" @click="pdfCurrent--" :disabled="pdfCurrent < 2">
            <i class="el-icon-arrow-left"></i>
            上一页
          </el-button>
          <span>{{ pdfCurrent }} / {{ pdfCount }}</span>
          <el-button type="primary" size="small" @click="pdfCurrent++" :disabled="pdfCurrent >= pdfCount">
            下一页
            <i class="el-icon-arrow-right"></i>
          </el-button>
        </div>
        <div @contextmenu.prevent>
        <Pdf :src="pdfUrl" :page="pdfCurrent" @num-pages="pdfCount = $event" /></div>
      </el-dialog>
      <!-- 收藏 -->
      <shoucang v-if="islogin" :typeshowa="typeshowa" @shuaxina="shuaxina" @shuaxinc="shuaxinc" @xza="xza" ref="Refshoucang" :shoucangType="shoucangData.type"></shoucang>
    </div>
    <div class="header-nav">
      <div class="nav-box">
        <div class="nav-item" :class="path === '/' ? 'nav-active' : ''" @click="toJump()">首页</div>
        <div class="nav-item" :class="path.includes('news') ? 'nav-active' : ''" @click="toJump('news')">资讯</div>
        <div class="nav-item" :class="path.includes('company') ? 'nav-active' : ''" @click="toJump('company')">品牌墙</div>
        <div class="nav-item" :class="path.includes('product') ? 'nav-active' : ''" @click="toJump('product')">产品墙</div>
        <div class="nav-item" :class="path.includes('tender') ? 'nav-active' : ''" @click="toJump('tender')">招投标</div>
        <div class="nav-item" :class="path.includes('deadstock') ? 'nav-active' : ''" @click="toJump('deadstock')">滞销品</div>
        <div class="nav-item" :class="path.includes('sale') ? 'nav-active' : ''" @click="toJump('sale')">滞销品</div>
      </div>
    </div>

    <!-- 图片预览 -->
    <el-dialog v-dialogDragBox :visible.sync="imgOpen" width="750px">
      <div style="text-align: center"><img style="max-width: 100%" :src="productImg" :alt="productName"  @contextmenu.prevent /></div>
    </el-dialog>
  </div>
</template>

<script>
import { getToken } from '@/utils/auth'
import { getProductsByName } from '@/api/system/product'
import { shoucTo } from '@/api/houtai/shoucang'
import shoucang from '@/views/houtai/shoucangb'
import ItemTpl from '../product/item'
import Cookies from 'js-cookie'
import { mapGetters } from 'vuex'
export default {
  components: { shoucang, ItemTpl },
  data() {
    return {
      islogin: false, // 是否登录
      stext: undefined, // 搜索内容
      isOpen: false, // 搜索结果是否展示
      searchList: [], // 搜索结果
      product_id: undefined,
      pdfshow: false, // 是否展示图纸预览
      pdfUrl: undefined, // 图纸预览地址
      typeshowa: false,
      shoucangData: {
        type: 'UserProduct'
      },
      onegood: {},
      dx: undefined,
      path: undefined,
      searchQuery: {
        pageNum: 1,
        pageSize: 72,
        keyword: undefined
      },
      pageSizes: [],
      searchTotal: 0,
      pdfCurrent: 1,
      pdfCount: 0,
      imgOpen: false,
      productImg: undefined,
      productName: undefined
    }
  },
  created() {
    this.islogin = getToken() ? true : false
    this.path = this.$route.path
  },
  computed: {
    ...mapGetters(['avatar'])
  },
  methods: {
    login() {
      this.$router.push('/login')
    },
    // 点击消息
    toindex() {
      this.$router.push('/index')
    },
    //搜索
    search() {
      this.searchQuery.pageNum = 1
      this.getProductsByName()
    },
    //搜索产品
    async getProductsByName(e) {
      const res = await getProductsByName(this.searchQuery)
      if (res.code === 200) {
        this.searchList = res.rows
        this.isOpen = true
        this.searchTotal = res.total
        const page = [72, 144, 216, 288]
        this.pageSizes = [...page, ...[res.total]]
      } else {
        this.$message.error(res.msg)
      }
    },
    // 点击其他位置关闭产品详情
    handleClick() {
      this.product_id = undefined
    },
    // 关闭按钮关闭产品详情
    closeProduct() {
      this.product_id = undefined
    },
    // 预览图纸
    drawPreView(e) {
      this.pdfshow = true
      this.pdfCurrent = 1
      this.pdfUrl = process.env.VUE_APP_BASE_API + e.replace('https://www.ziyouke.net', '')
    },
    //收藏
    sc(row) {
      const username = Cookies.get('token')
      if (username) {
        //收藏到
        this.onegood = row
        this.dx = 1
        this.$refs.Refshoucang.showmodelc()
      } else {
        this.$router.push({
          path: '/login'
        })
      }
    },
    shuaxina() {},
    shuaxinc() {},
    xza(row) {
      let lista = []
      if (this.dx == 1) {
        lista = [this.onegood]
      }
      shoucTo({
        // index: 0,
        storeId: row.storeId,
        valueIdList: lista
      }).then((res) => {
        this.$message({
          message: '操作成功',
          type: 'success'
        })
      })
    },
    //设置询价
    setxj(e) {
      if (!this.islogin) {
        this.$router.push({
          path: '/login'
        })
        return false
      } else {
        this.$router.push({
          path: '/demand/order',
          query: {
            data: JSON.stringify(e)
          }
        })
        return false
      }
    },
    // 页面跳转
    toJump(val) {
      val = val || ''
      this.$router.push(`/${val}`)
    },
    // 图片放大
    handleImg(item) {
      this.productImg = this.formatProductImg(item)
      this.productName = item.productName
      this.imgOpen = true
    },
  }
}
</script>

<style lang="scss" scoped>
.pagination-box {
  width: 100%;
  margin: 30px auto 20px;
}
</style>
