<template>
  <div>
    <!-- page -->
    <div style="width: 100%; height: 320px; background-color: white; margin-top: 20px">
      <div style="width: 1200px; height: 100%; display: flex; justify-content: space-between; margin: auto">
        <div style="width: 150px; height: 270px; margin-top: 15px; display: flex; flex-wrap: wrap; justify-content: space-between; text-align: center">
          <div style="width: 100%; height: 20px; text-align: left; font-size: 14px">更多工具</div>
          <div style="width: 70px; height: 70px; background-color: #f8faff">
            <img src="../../../../public/imgs/产品定制@2x.png" style="width: 26px; height: 26px; margin: 10px auto 0" />
            <div style="font-size: 12px; color: #666666">产品订制</div>
          </div>
          <div style="width: 70px; height: 70px; background-color: #f8faff">
            <img src="../../../../public/imgs/批量下单@2x.png" style="width: 26px; height: 26px; margin: 10px auto 0" />
            <div style="font-size: 12px; color: #666666">快速下单</div>
          </div>
          <div style="width: 70px; height: 70px; background-color: #f8faff">
            <img src="../../../../public/imgs/铝模辅材@2x.png" style="width: 26px; height: 26px; margin: 10px auto 0" />
            <div style="font-size: 12px; color: #666666">模具地址</div>
          </div>
          <div style="width: 70px; height: 70px; background-color: #f8faff">
            <img src="../../../../public/imgs/拆零工具@2x.png" style="width: 26px; height: 26px; margin: 10px auto 0" />
            <div style="font-size: 12px; color: #666666">拆零工具</div>
          </div>
          <div style="width: 70px; height: 70px; background-color: #f8faff">
            <img src="../../../../public/imgs/安装需求@2x.png" style="width: 26px; height: 26px; margin: 10px auto 0" />
            <div style="font-size: 12px; color: #666666">安装需求</div>
          </div>
          <div style="width: 70px; height: 70px; background-color: #f8faff">
            <img src="../../../../public/imgs/深化服务@2x.png" style="width: 26px; height: 26px; margin: 10px auto 0" />
            <div style="font-size: 12px; color: #666666">深化服务</div>
          </div>
        </div>
        <div style="width: 450px; height: 270px; border: 0px solid; margin-top: 15px">
          <div style="width: 100%; height: 20px; text-align: left; font-size: 14px">最新消息</div>
          <template v-for="(item, index) in messageList">
            <div :key="index" style="display: flex; height: 18px; font-size: 14px; margin-top: 7px">
              <div style="width: 18px; height: 18px; background: #3ec5ff; border-radius: 100px; color: white; line-height: 18px; text-align: center">
                {{ index + 1 }}
              </div>
              <div style="margin-left: 5px">{{ item }}</div>
            </div>
          </template>
        </div>
        <div style="width: 300px; height: 270px; border: 0px solid; margin-top: 15px">
          <div style="width: 100%; height: 20px; text-align: left; font-size: 14px">关于</div>
          <div style="width: 100%; height: 90px; margin-top: 5px; display: flex">
            <div style="width: 60px; height: 100%; background-color: #9fe2ff; display: flex">
              <img src="../../../../public/imgs/客服 <EMAIL>" style="width: 20px; height: 20px; margin: auto" />
            </div>
            <div style="width: 240px; height: 100%; background-color: #ededed; font-size: 14px;display: flex;flex-direction: column;justify-content: center;">
              <div style="text-align: left; text-indent: 20px">电话：15188838176</div>
              <div style="text-align: left; text-indent: 20px; margin-top: 8px">邮箱：<EMAIL></div>
            </div>
          </div>
          <div style="width: 100%; height: 20px; text-align: left; font-size: 14px; margin-top: 16px; display: flex">
            给我们建议
            <div style="width: 80px; height: 24px; background-color: #3ec5ff; text-align: center; line-height: 24px; margin-left: 10px; color: white; border-radius: 5px; display: flex">
              <img src="../../../../public/imgs/留言 <EMAIL>" style="width: 18px; height: 18px; margin: auto" />
              <div style="margin: auto">写点啥</div>
            </div>
          </div>
          <div style="width: 100%; height: 20px; text-align: left; font-size: 14px; margin-top: 16px">公司简介</div>
        </div>
      </div>
    </div>
    <div style="width: 100%; height: 60px; background-color: #292f48; font-size: 12px; overflow: hidden; text-align: center">
      <div style="color: #ffffff; margin-top: 18px">自由客紧固件 | 备案号：<a href="https://beian.miit.gov.cn/" target="_blank">冀ICP备2020031367号-5</a> </div>
    </div>
    <div id="sidebar">
      <div class="sidebarnav">
        <div @click="toSkip('product')">产品</div>
        <div @click="toSkip('demand')">采购</div>
        <div @click="toSkip('addition')">报价</div>
      </div>
      <!-- 返回顶部 -->
      <div class="backtop" @click="toTop()">
        <img src="../../../../public/imgs/返回顶部 1.png" />
      </div>
    </div>

    <div class="service">
      <img src="~@/assets/images/qrcode.png" width="100" height="100" alt="" />
      <b>在线客服</b>
      <span>周一至周日</span>
      <span>早08:30—晚18:30</span>
    </div>
  </div>
</template>

<script>
import { getNewMessage } from '@/api/system/product'
export default {
  data() {
    return {
      messageList: [] // 最新消息列表
    }
  },
  created() {
    this.getNewMessage() // 获取最新消息
  },
  methods: {
    //跳转到我的产品
    toProduct() {
      this.$router.push({
        path: '/platform/products'
      })
    },
    //跳转到我的供应商
    toGys() {
      this.$router.push({
        path: '/private/mySupplier'
      })
    },
    toSkip(val){
      switch (val){
        case 'product':
          this.$router.push({
            path: "/platform/products",
          });
          break;
        case 'demand':
          this.$router.push({
            path: "/demand/order",
          });
          break;
        case 'addition':
          this.$router.push({
            path: "/offer/offerlist",
          });
          break;
      }
    },
    // 返回头部
    toTop() {
      document.documentElement.scrollTop = 0
    },
    // 获取新消息
    async getNewMessage() {
      const res = await getNewMessage()
      this.messageList = res.data
    }
  }
}
</script>

<style lang="scss" scoped>

.service {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 999;
  background-color: white;
  padding: 10px;
  border-radius: 5px;
  box-shadow: 0 1px 18px 0 rgba(0, 0, 0, 0.29);
  b {
    font-size: 16px;
    line-height: 21px;
    font-weight: normal;
    color: #666666;
  }
  span {
    font-size: 13px;
    line-height: 17px;
    color: #cccccc;
  }
}
</style>
