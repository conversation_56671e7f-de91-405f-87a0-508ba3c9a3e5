<template>
  <div class="container" @contextmenu.prevent>
    <header-tpl :is-login="isLogin" :showSearch="false" />
    <div class="tenderTip">
      <i class="el-icon-info"></i>
      <span>鼠标选中正文内容的完整公司名称即可查询公司信息</span>
    </div>
    <div class="container-box" style="min-width: 1200px">
      <div class="containerBox">
        <div class="tenderInfo">
          <div class="tenderInfo-title">{{ current.title }}</div>
          <div class="tenderInfo-date" v-if="current.open_date">日期：{{ parseTime(current.open_date, '{y}-{m}-{d}') }}</div>
          <div class="tenderInfo-content" id="pdfDom" v-if="!!current.content" v-html="current.content" @mouseup="handleMouseUp"></div>
          <div class="tenderInfo-content" v-else>暂无内容</div>
          <div class="tenderInfo-keyword">关键词：{{ keyword || current.keywords }}</div>
        </div>
        <div class="tenderInfo-other" v-if="this.source !== 'chat'">
          <div class="tenderInfo-other-list">
            <div class="tenderInfo-other-list-item" @click="handleOpen(previous)" v-if="!!previous">上一条：{{ previous.title }}</div>
            <div class="tenderInfo-other-list-item" @click="handleOpen(next)" v-if="!!next">下一条：{{ next.title }}</div>
          </div>
          <div style="display: inline-flex">
            <div class="tenderInfo-other-button" style="margin-right: 20px" @click="getPdf(current.title)">下载</div>
            <div class="tenderInfo-other-button" @click="handleBack">返回列表</div>
          </div>
        </div>
        <div class="tenderInfo-other" v-if="this.source === 'chat'"></div>
      </div>
    </div>
    <footer-tpl />

    <el-dialog v-dialogDragBox title="使用权限到期提醒" :visible.sync="tenderOpen" width="750px" class="custom-dialog">
      <div class="tenderExpire">
        <div class="tenderExpire-icon">
          <i class="el-icon-warning"></i>
        </div>
        <div class="tenderExpire-title">您的使用权限已到期，请获取权限后再次尝试</div>
        <div class="tenderExpire-desc">获得权限方式：单月成交一次即可获取一个月的使用权限</div>
        <div class="tenderExpire-button" @click="tenderOpen = false">我知道了</div>
      </div>
    </el-dialog>

    <!--企业信息-->
    <company ref="company" />
  </div>
</template>

<script>
import HeaderTpl from '../components/head'
import FooterTpl from '../components/foot'
import { getToken } from '@/utils/auth'
import { getRemoteCompanyInfo, getTenderDetail, getTenderDetailNoLogin } from '@/api/tender'
import popShare from '@/components/popShare'
import BaiduMap from 'vue-baidu-map/components/map/Map'
import Company from '@/views/payment/company'

export default {
  components: { Company, HeaderTpl, FooterTpl, BaiduMap },
  data() {
    return {
      isLogin: false,
      bidId: undefined,
      keyword: undefined,
      pageNum: undefined,
      region: undefined,
      state: undefined,
      current: {},
      previous: {},
      next: {},
      tenderOpen: false,
      tenderTime: undefined,
      isBidding: undefined,
      companyOpen: false,
      companyInfo: {},
      center: { lng: undefined, lat: undefined },
      zoom: 15,
      mapShow: false,
      mangerList: [],
      source: ''
    }
  },
  created() {
    this.getConfigKey('bidding.limit').then(res => {
      this.isBidding = res.msg
    })
    this.tenderTime = this.$store.getters && this.$store.getters.tenderTime
    const { bidId, keyword, region, state, pageNum, source } = this.$route.query
    this.keyword = keyword
    this.region = region
    this.state = state
    this.pageNum = pageNum
    this.source = source
    if (bidId) {
      this.bidId = bidId
      this.getDetail()
    } else {
      this.$alert('参数错误，请联系管理员', '系统提示', {
        type: 'error',
        confirmButtonText: '确定',
        callback: action => {
          this.$router.push('/')
        }
      })
      return
    }
    this.isLogin = !!getToken()
  },
  methods: {
    getDetail() {
      const params = { bidId: this.bidId, keyword: this.keyword, region: this.region, state: this.state }
      if (this.source === 'chat') {
        getTenderDetailNoLogin(params).then(res => {
          const { code, msg, data } = res
          if (code === 200) {
            let content = data.content
            if (content.toLowerCase().indexOf('https://') >= 0) {
              content = content.replace(/(^|[^<=""])(https:(\/\/|\\\\)(([\w\/\\\+\-~`@:%])+\.)+([\w\/\\\.\=\?\+\-~`@\':!%#]|(&amp;)|&)+)/g, '$1<a target="_blank" href="$2">$2</a>')
            }
            if (content.toLowerCase().indexOf('http://') >= 0) {
              content = content.replace(/(^|[^<=""])(http:(\/\/|\\\\)(([\w\/\\\+\-~`@:%])+\.)+([\w\/\\\.\=\?\+\-~`@\':!%#]|(&amp;)|&)+)/g, '$1<a target="_blank" href="$2">$2</a>')
            }
            if (content.toLowerCase().indexOf('www.') >= 0 || content.toLowerCase().indexOf('bbs.') >= 0) {
              content = content.replace(/(^|[^\/\\\w\=])((www|bbs)\.(\w)+\.([\w\/\\\.\=\?\+\-~`@\'!%#]|(&amp;))+)/g, '$1<a target="_blank" href=http://$2>$2</a>')
            }
            this.current = data
            this.current.content = content
          } else this.$message.error(msg)
        })
      } else {
        getTenderDetail(params).then(res => {
          const { code, msg, data } = res
          if (code === 200) {
            const { current, previous, next } = data
            let content = current.content
            if (content.toLowerCase().indexOf('https://') >= 0) {
              content = content.replace(/(^|[^<=""])(https:(\/\/|\\\\)(([\w\/\\\+\-~`@:%])+\.)+([\w\/\\\.\=\?\+\-~`@\':!%#]|(&amp;)|&)+)/g, '$1<a target="_blank" href="$2">$2</a>')
            }
            if (content.toLowerCase().indexOf('http://') >= 0) {
              content = content.replace(/(^|[^<=""])(http:(\/\/|\\\\)(([\w\/\\\+\-~`@:%])+\.)+([\w\/\\\.\=\?\+\-~`@\':!%#]|(&amp;)|&)+)/g, '$1<a target="_blank" href="$2">$2</a>')
            }
            if (content.toLowerCase().indexOf('www.') >= 0 || content.toLowerCase().indexOf('bbs.') >= 0) {
              content = content.replace(/(^|[^\/\\\w\=])((www|bbs)\.(\w)+\.([\w\/\\\.\=\?\+\-~`@\'!%#]|(&amp;))+)/g, '$1<a target="_blank" href=http://$2>$2</a>')
            }
            this.current = current
            this.current.content = content
            this.previous = previous
            this.next = next
          } else this.$message.error(msg)
        })
      }
    },
    // 返回列表
    handleBack() {
      const query = {
        keyword: this.keyword,
        region: this.region,
        state: this.state,
        pageNum: this.pageNum
      }
      this.$router.push({ path: '/tender', query })
    },
    // 打开其他公告
    handleOpen(item) {
      const now = new Date().getTime()
      if (this.tenderTime && now > this.tenderTime && this.isBidding == 'true') {
        this.tenderOpen = true
        return
      }
      const { keyword, region, state, pageNum } = this
      this.$router.replace({ path: '/tender/detail', query: { bidId: item.id, keyword, region, state, pageNum } }, () => {
        this.bidId = item.id
        this.getDetail()
      })
    },
    handleMouseUp(e) {
      const selection = window.getSelection()
      const text = selection.toString()
      if (text) {
        popShare.show({
          title: text,
          top: e.clientY,
          left: e.clientX,
          onItemClick: e => {
            if (e.value === 'search') this.handleSearch(text)
          }
        })
      }
    },
    // prettier-ignore
    handleSearch(text = undefined) {
      const options = {
        lock: true,
        text: '搜索中…',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      }
      let dataLoading = this.$loading(options)
      this.mapShow = false
      getRemoteCompanyInfo({ companyName: text }).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          this.$nextTick(() => {
            dataLoading.close()
          })
          if (data && data.hasOwnProperty('companyCreditCode') && data.companyCreditCode) {
            this.$refs.company.getInfo(data, 'private')
          } else {
            this.$nextTick(() => {
              dataLoading.close()
            })
            this.$alert(`未查询到“<span style='color:#ff0000'>${ text }</span>”相关公司信息！<br />请检查公司名称是否正确，请输入公司全称！`, '系统提示', { dangerouslyUseHTMLString: true })
          }
        } else {
          this.$nextTick(() => {
            dataLoading.close()
          })
          this.$alert(`未查询到“<span style='color:#ff0000'>${ text }</span>”相关公司信息！<br />请检查公司名称是否正确，请输入公司全称！`, '系统提示', { dangerouslyUseHTMLString: true })
        }
      })
    },
    // 电话数组转字符串
    phoneListToString(list = []) {
      return list.map(item => item.number).join(',')
    },
    // 高管转数组
    managerToArray(obj = {}) {
      let str = obj.companyManagerPersonName
      const reg = /\t:#\d\t;\t/g
      str = str.replace(reg, ',')
      const arr = str.split(',')
      const newArr = Array.from(new Set(arr.filter(item => item)))
      return newArr.filter(item => item !== obj.companyLegalPersonName)
    },
    handlerMap({ BMap, map }) {
      const { jingdu, weidu } = this.companyInfo
      this.center.lng = jingdu
      this.center.lat = weidu
      this.zoom = 15
    },
    syncCenterAndZoom(e) {
      const { lng, lat } = e.target.getCenter()
      this.center.lng = lng
      this.center.lat = lat
      this.zoom = e.target.getZoom()
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
.containerBox {
  max-width: 1200px;
  margin: 0 auto;
}
.tenderTip {
  background-color: #ffefe5;
  color: #f35d09;
  display: flex;
  width: 100%;
  height: 65px;
  font-size: 14px;
  align-items: center;
  justify-content: center;
}
.tender {
  &Info {
    margin-top: 20px;
    background-color: #ffffff;
    border: 1px solid #d5dde6;
    border-radius: 10px;
    box-shadow: 0 1px 9px 0 rgba(0, 0, 0, 0.07);
    &-title {
      font-size: 14px;
      line-height: 20px;
      color: #333333;
      padding: 20px 30px;
      text-align: center;
    }
    &-date {
      color: #666666;
      font-size: 12px;
      line-height: 14px;
      text-align: center;
      margin-bottom: 10px;
    }
    &-content {
      padding: 20px 30px;
      font-size: 14px;
      line-height: 30px;
      color: #333333;
      border-top: 1px solid #d5dde6;
      ::v-deep {
        p {
          margin: 0;
          padding: 0;
        }
        img {
          max-width: 100%;
        }
        a {
          color: #2e73f3;
        }
        table {
          width: 100%;
          border-collapse: collapse;
          overflow-x: auto;
          display: block;
          white-space: nowrap;
          th,
          td {
            border: 1px solid #cccccc;
            padding: 5px 10px;
            font-size: 14px;
            color: #333333;
          }
        }
      }
    }
    &-keyword {
      padding: 20px 30px;
      font-size: 14px;
      line-height: 30px;
      color: #999999;
      border-top: 1px solid #d5dde6;
    }
    &-other {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 20px;
      margin-bottom: 20px;
      &-list {
        width: 70%;
        &-item {
          font-size: 16px;
          color: #666666;
          height: 28px;
          line-height: 28px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          cursor: pointer;
          &:hover {
            color: #2e73f3;
          }
        }
      }
      &-button {
        padding: 13px 25px;
        background-color: #2e73f3;
        border-radius: 5px;
        color: #ffffff;
        font-size: 16px;
        cursor: pointer;
        &:hover {
          opacity: 0.8;
        }
      }
    }
  }
}
.tenderExpire {
  display: flex;
  width: 100%;
  flex-direction: column;
  align-items: center;
  padding: 10px 30px;
  &-icon {
    font-size: 36px;
    color: #ed4040;
  }
  &-title {
    font-size: 14px;
    line-height: 20px;
    font-weight: 500;
    color: #333333;
    margin: 20px 0 10px;
  }
  &-desc {
    font-size: 12px;
    line-height: 20px;
    color: #2e73f3;
    margin-bottom: 45px;
  }
  &-button {
    width: 270px;
    height: 50px;
    line-height: 50px;
    text-align: center;
    border-radius: 5px;
    background-color: #2e73f3;
    color: #ffffff;
    cursor: pointer;
  }
}
.mouseSearch {
  font-size: 20px;
  box-shadow: 0 1px 9px 0 rgba(0, 0, 0, 0.07);
  border-radius: 5px;
  padding: 2px;
  cursor: pointer;
  &:hover {
    color: #2e73f3;
  }
}
.company {
  margin: 0 20px;
  border: 1px solid #ebedf3;
  .flex-column {
    display: inline-flex;
    flex-direction: column;
    justify-content: center;
  }
  &-head {
    padding: 30px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #ebedf3;
    &-icon {
      width: 90px;
      height: 90px;
      border-radius: 10px;
      margin-right: 20px;
    }
    &-title {
      font-size: 20px;
      color: #333333;
      margin-bottom: 20px;
    }
    &-desc {
      font-size: 16px;
      line-height: 20px;
      color: #666666;
      span {
        margin-right: 50px;
      }
    }
  }
  &-title {
    display: flex;
    align-items: center;
    font-size: 16px;
    line-height: 30px;
    color: #666666;
    &:before {
      content: '';
      display: inline-block;
      width: 5px;
      height: 30px;
      background-color: #2e73f3;
      margin-right: 10px;
    }
  }
  &-info {
    padding-bottom: 20px;
    border-bottom: 1px solid #ebedf3;
    &-item {
      display: inline-flex;
      align-items: flex-start;
      width: 47%;
      padding: 10px 20px;
      font-size: 14px;
      color: #333333;
      span:first-child {
        flex-shrink: 0;
        display: inline-block;
        width: 75px;
        color: #666666;
      }
      &.lang {
        width: 94%;
      }
    }
  }
  &-contact {
    padding: 30px 0;
    border-bottom: 1px solid #ebedf3;
    &-user {
      display: flex;
      flex-wrap: wrap;
      padding: 25px 20px 25px 10px;
      &-item {
        display: inline-flex;
        padding: 6px 10px;
        min-width: 150px;
        background-color: #f8faff;
        margin: 0 10px 10px;
      }
      &-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        margin-right: 10px;
      }
      &-posts {
        font-size: 12px;
        color: #999999;
        margin-bottom: 5px;
      }
      &-name {
        font-size: 14px;
        color: #333333;
      }
    }
    &-item {
      display: flex;
      align-items: center;
      padding: 10px 20px;
      font-size: 14px;
      color: #333333;
      span:first-child {
        flex-shrink: 0;
        display: inline-block;
        width: 75px;
        color: #666666;
      }
    }
  }
  &-other {
    padding: 30px 0;
    &-item {
      display: flex;
      align-items: center;
      padding: 10px 20px;
      font-size: 14px;
      color: #333333;
      span:first-child {
        flex-shrink: 0;
        display: inline-block;
        width: 75px;
        color: #666666;
      }
    }
  }
}
.baiduMap {
  width: 100%;
  height: 420px;
  padding: 20px 20px 0;
}
</style>
