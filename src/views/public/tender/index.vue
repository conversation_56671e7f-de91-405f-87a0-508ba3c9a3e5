<template>
  <div class="container" @contextmenu.prevent @click="clickHide">
    <header-tpl :is-login="isLogin" :showSearch="false" />
    <div class="expirationTime" v-if="expirationShow && usertype === 'gys'">
      <div class="expirationTime-close" @click="handleExpiration">
        <i class="el-icon-error"></i>
      </div>
      <div class="expirationTime-title" v-if="!!expirationTime">
        <span>您的使用权限还有</span>
        <b>{{ expirationTime && expirationTime.toString()[0] }}</b>
        <b v-if="expirationTime && expirationTime.toString()[1]">{{ expirationTime && expirationTime.toString()[1] }}</b>
        <span>天到期</span>
      </div>
      <div class="expirationTime-title" v-if="!expirationTime">您的使用权限已到期</div>
      <div class="expirationTime-tip">请及时补充权限使用时间</div>
      <div class="expirationTime-desc">获得权限方式：单月平台(公域)成交一次即可获取一个月的使用权限</div>
    </div>
    <div class="container-box" style="min-width: 1200px">
      <div class="containerBox">
        <div class="product-search" style="margin-top: 10px">
          <el-form :model="queryParams" ref="queryParams" label-width="0" @submit.native.prevent>
            <el-input v-model="queryParams.search" clearable placeholder="请输入标题关键字" @keyup.enter.native="handleSearchQuery" @clear="handleSearchQuery"></el-input>
            <el-select v-model="startTime" class="product-search-select" popper-class="product-search-option" placeholder="选择时间" style="width: 120px" @change="handleSearchQuery">
              <el-option v-for="item in startTimeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
            <el-button type="primary" size="default" icon="el-icon-search" @click="handleSearchQuery">搜索</el-button>
          </el-form>
        </div>
        <!--关键词筛选-->
        <div class="tenderKeyword">
          <div class="tenderKeyword-title">关键词筛选</div>
          <div class="tenderKeyword-con">
            <span class="tenderKeyword-item" :class="{ active: !queryParams.keyword }" @click="handleQuery()">全部</span>
            <span class="tenderKeyword-item" :class="{ active: queryParams.keyword == item.keyword }" @click="handleQuery(item.keyword)" v-for="item in keywordList" :key="item.id">
              {{ item.keyword }}
              <i class="el-icon-error" @click.stop="handleDelete(item)" v-if="item.canModify"></i>
            </span>
          </div>
          <div class="tenderKeyword-plus">
            <template v-if="usertype === 'gys'">
              <el-button type="text" icon="el-icon-plus" size="small" @click="handleAdd">新增关键词</el-button>
              <span class="tenderKeyword-edit" @click.stop="isEdit = true">编辑</span>
            </template>
            <div class="tenderKeyword-list" ref="list" v-if="isEdit">
              <div class="tenderKeyword-list-title">自定义选择关键词</div>
              <div class="tenderKeyword-list-box">
                <el-checkbox-group v-model="keywordList" @change="handleCheckedCitiesChange">
                  <div class="tenderKeyword-list-item" v-for="item in keywordData" :key="item.id">
                    <el-checkbox :label="item">{{ item.keyword }}</el-checkbox>
                  </div>
                </el-checkbox-group>
              </div>
            </div>
          </div>
        </div>
        <div class="tenderKeyword">
          <div class="tenderKeyword-title">区域</div>
          <div class="tenderKeyword-region" :class="{ close: !isOpen }">
            <span class="tenderKeyword-item" :class="{ active: !queryParams.region }" @click="handleQuery('', 'region')">全部</span>
            <span class="tenderKeyword-item" :class="{ active: queryParams.region == item }" @click="handleQuery(item, 'region')" v-for="(item, index) in regionOptions" :key="index">
              {{ item }}
            </span>
          </div>
          <div class="tenderKeyword-open">
            <el-button type="text" icon="el-icon-arrow-down" size="small" @click="isOpen = !isOpen" v-if="!isOpen">展开</el-button>
            <el-button type="text" icon="el-icon-arrow-up" size="small" @click="isOpen = !isOpen" v-if="isOpen">收起</el-button>
          </div>
        </div>
        <div class="tenderKeyword">
          <div class="tenderKeyword-title">状态</div>
          <div class="tenderKeyword-state">
            <span class="tenderKeyword-item" :class="{ active: !queryParams.state }" @click="handleQuery('', 'state')">全部</span>
            <span class="tenderKeyword-item" :class="{ active: queryParams.state == item }" @click="handleQuery(item, 'state')" v-for="(item, index) in stateOptions" :key="index">
              {{ item }}
            </span>
          </div>
        </div>
        <!--表格数据-->
        <template v-if="total">
          <el-table v-loading="loading" :data="list" style="width: 100%" stripe class="custom-table custom-table-cell5 tenderTable">
            <el-table-column prop="title" label="招标标题" show-overflow-tooltip min-width="31%" />
            <el-table-column prop="keywords" align="center" label="关键词" show-overflow-tooltip min-width="14%" />
            <el-table-column prop="bid_area" align="center" label="区域" show-overflow-tooltip min-width="10%" />
            <el-table-column prop="open_date" align="center" label="日期" show-overflow-tooltip min-width="13%">
              <template slot-scope="{ row }">{{ parseTime(row.open_date, '{y}-{m}-{d}') }}</template>
            </el-table-column>
            <el-table-column prop="status" align="center" label="联系人" show-overflow-tooltip min-width="12%">
              <template slot-scope="{ row }">{{ row.contact ? JSON.parse(row.contact)[0].name : '' }}</template>
            </el-table-column>
            <el-table-column prop="action" align="center" label="联系方式" show-overflow-tooltip min-width="10%">
              <template slot-scope="{ row }">{{ row.contact ? JSON.parse(row.contact)[0].phone : '' }}</template>
            </el-table-column>
            <el-table-column prop="bid_type" align="center" label="状态" show-overflow-tooltip min-width="10%" />
            <el-table-column align="center" label="操作" min-width="10%">
              <template slot-scope="{ row }">
                <el-button size="small" @click="handleDetail(row)">查看详情</el-button>
              </template>
            </el-table-column>
          </el-table>
        </template>
        <el-empty description="计算中..." v-else></el-empty>
        <!--分页-->
        <div style="padding-bottom: 50px">
          <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
        </div>
      </div>
    </div>
    <footer-tpl />

    <el-dialog v-dialogDragBox title="使用权限到期提醒" :visible.sync="tenderOpen" width="750px" class="custom-dialog">
      <div class="tenderExpire">
        <div class="tenderExpire-icon">
          <i class="el-icon-warning"></i>
        </div>
        <div class="tenderExpire-title">您的使用权限已到期，请获取权限后再次尝试</div>
        <div class="tenderExpire-desc">获得权限方式：单月平台(公域)成交一次即可获取一个月的使用权限</div>
        <div class="tenderExpire-button" @click="tenderOpen = false">我知道了</div>
      </div>
    </el-dialog>

    <el-dialog v-dialogDragBox title="暂无权限查看" :visible.sync="powerOpen" width="750px" class="custom-dialog">
      <div class="tenderExpire">
        <div class="tenderExpire-icon">
          <i class="el-icon-warning"></i>
        </div>
        <div class="tenderExpire-title">个人用户暂时无法查看招投标详情，请升级为企业方可查看</div>
        <div class="tenderExpire-desc">升级方法：登录至管理中心点击升级为企业</div>
        <div class="tenderExpire-button" @click="powerOpen = false">我知道了</div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import HeaderTpl from '../components/head'
import FooterTpl from '../components/foot'
import { getToken } from '@/utils/auth'
import { getTenderList, getTenderKeyword, addTenderKeyword, deleteTenderKeyword, setTenderShow } from '@/api/tender'
import { getTenderTime } from '@/api/login'
import { parseTime } from '@/utils/ruoyi'

export default {
  components: { HeaderTpl, FooterTpl },
  data() {
    return {
      loading: true,
      isLogin: false,
      list: [],
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        search: undefined,
        keyword: undefined,
        region: undefined,
        state: undefined,
        startTime: undefined,
        endTime: undefined
      },
      startTime: 1,
      startTimeOptions: [
        { label: '一个月内', value: 1 },
        { label: '三个月内', value: 3 },
        { label: '半年内', value: 6 },
        { label: '一年内', value: 12 }
      ],
      keywordList: [],
      keywordData: [],
      isEdit: false,
      regionOptions: ['河北', '山西', '辽宁', '吉林', '黑龙江', '江苏', '浙江', '安徽', '福建', '江西', '山东', '河南', '湖北', '湖南', '广东', '海南', '四川', '贵州', '云南', '陕西', '甘肃', '青海', '台湾', '内蒙古', '广西', '西藏', '宁夏', '新疆', '北京', '天津', '上海', '重庆', '香港', '澳门'],
      // stateOptions: ['招标', '中标', '比选', '其他', '资审', '变更', '废标', '询价', '成交', '合同', '竞谈', '其它', '竞价', '预审', '预告', '流标', '验收', '需求公示', '结果', '单一', '结果变更', '邀标', '违规', '论证意见', '预审结果', '招标变更', '单一来源', '竞磋', '终止'],
      stateOptions: ['预告', '预审', '预审结果', '论证意见', '需求公示', '招标', '邀标', '询价', '竞谈', '单一', '变更', '成交', '中标', '废标', '流标', '结果变更', '合同', '验收', '违规'],
      isOpen: false,
      tenderOpen: false,
      tenderTime: undefined,
      expirationTime: undefined,
      expirationShow: false,
      isBidding: undefined,
      isAdd: false,
      usertype: undefined,
      powerOpen: false
    }
  },
  created() {
    const { keyword, region, state, pageNum } = this.$route.query
    this.queryParams.keyword = keyword || (this.$store.getters && this.$store.getters.tender.find(item => item.key === 'keyword') && this.$store.getters.tender.find(item => item.key === 'keyword').value) || undefined
    this.queryParams.region = region || (this.$store.getters && this.$store.getters.tender.find(item => item.key === 'region') && this.$store.getters.tender.find(item => item.key === 'region').value) || undefined
    this.queryParams.state = state || (this.$store.getters && this.$store.getters.tender.find(item => item.key === 'state') && this.$store.getters.tender.find(item => item.key === 'state').value) || undefined
    this.queryParams.pageNum = Number(pageNum) || 1
    this.isLogin = !!getToken()
    this.getKeyword()
    this.getList()
    this.getConfigKey('bidding.limit').then(res => {
      this.isBidding = res.msg
      if (res.msg == 'true') this.getTenderShow()
    })
    const { companyId } = this.$store.state.user || -1
    if (companyId > 0) this.usertype = 'gys'
    else this.usertype = 'cgs'
  },
  methods: {
    getTenderShow() {
      const endTime = localStorage.getItem('tenderEndTime') || ''
      const startTime = parseTime(new Date(), '{y}-{m}-{d}')
      if (endTime) {
        if (startTime != endTime) {
          localStorage.removeItem('tenderEndTime')
          this.getTenderTimeFun()
        } else this.expirationShow = false
      } else this.getTenderTimeFun()
    },
    getTenderTimeFun() {
      const tenderTime = this.$store.getters && this.$store.getters.tenderTime
      const nowTime = new Date().getTime()
      let diffInMilliseconds
      let daysDiff
      if (tenderTime) {
        this.tenderTime = tenderTime
        if (tenderTime > nowTime) {
          diffInMilliseconds = Math.abs(tenderTime - nowTime)
          daysDiff = Math.ceil(diffInMilliseconds / (1000 * 60 * 60 * 24))
          this.expirationTime = daysDiff > 99 ? 99 : daysDiff
        } else this.expirationTime = 0
        this.expirationShow = true
      } else {
        getTenderTime().then(res => {
          const { code, msg, data } = res
          if (code === 200) {
            this.tenderTime = data
            if (tenderTime > nowTime) {
              diffInMilliseconds = Math.abs(data - nowTime)
              daysDiff = Math.ceil(diffInMilliseconds / (1000 * 60 * 60 * 24))
              this.expirationTime = daysDiff > 99 ? 99 : daysDiff
            } else this.expirationTime = 0
            this.expirationShow = true
          } else this.$message.error(msg)
        })
      }
    },
    // 查询关键词
    getKeyword() {
      getTenderKeyword().then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          this.keywordData = data
          let tenderKeyword
          if (this.isAdd) {
            const last = data[data.length - 1]
            tenderKeyword = localStorage.getItem('tenderKeyword') + ',' + last.id
            localStorage.setItem('tenderKeyword', tenderKeyword)
            this.isAdd = false
          } else tenderKeyword = localStorage.getItem('tenderKeyword')
          if (tenderKeyword) {
            const arr = tenderKeyword.split(',')
            let list = []
            arr.forEach(item => {
              const obj = data.find(i => i.id === Number(item))
              if (obj) list.push(obj)
            })
            this.keywordList = list
          } else {
            this.keywordList = data.filter(item => item.checked)
            localStorage.setItem('tenderKeyword', this.keywordList.map(item => item.id).toString())
          }
        } else this.$message.error(msg)
      })
    },
    // 查询列表数据
    getList() {
      this.loading = true
      getTenderList(this.queryParams).then(res => {
        const { code, msg, rows, total } = res
        if (code === 200) {
          this.list = rows
          this.total = total
          this.loading = false
        } else this.$message.error(msg)
      })
    },
    // 筛选
    handleQuery(val = undefined, type = 'keyword') {
      this.queryParams.pageNum = 1
      this.queryParams[type] = val
      this.$store.dispatch('tender/removeTender', type)
      this.$nextTick(() => {
        this.$store.dispatch('tender/setTender', { key: type, value: val })
      })
      this.getList()
    },
    // 标题搜索
    handleSearchQuery() {
      if (this.queryParams.search && this.queryParams.search.trim()) {
        this.queryParams.startTime = parseTime(new Date(new Date().setMonth(new Date().getMonth() - this.startTime)), '{y}-{m}-{d}')
        this.queryParams.endTime = parseTime(new Date(), '{y}-{m}-{d}')
        this.queryParams.pageNum = 1
        this.getList()
      } else {
        this.queryParams.startTime = undefined
        this.queryParams.endTime = undefined
        this.queryParams.pageNum = 1
        this.getList()
      }
    },
    // 删除关键词
    // prettier-ignore
    handleDelete(item) {
      const data = { keywordId: item.id }
      this.$modal.confirm('是否确认删除选中的关键词？').then(function () {
        return deleteTenderKeyword(data)
      }).then(() => {
        this.getKeyword()
        this.handleQuery()
        this.$modal.msgSuccess('删除成功')
      }).catch(() => {
      })
    },
    // 新增关键词
    // prettier-ignore
    handleAdd() {
      const that = this
      this.$prompt('请输入关键词名称', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false,
        inputErrorMessage: '关键词名称为空'
      }).then(({ value }) => {
        const data = { keyword: value }
        addTenderKeyword(data).then(function (res) {
          const { code, msg } = res
          if (code === 200) {
            that.$message.success('新增成功')
            that.isAdd = true
            that.getKeyword()
          } else that.$message.error(msg)
        })
      }).catch(() => {
      })
    },
    handleCheckedCitiesChange(val) {
      const ids = val.map(item => item.id)
      const data = { ids }
      setTenderShow(data).then(res => {})
      localStorage.setItem('tenderKeyword', ids)
    },
    // 点击搜索结果产品以外区域隐藏产品弹框
    clickHide(event) {
      const dom = this.$refs.list
      if (dom && !dom.contains(event.target)) {
        this.isEdit = false
      }
    },
    // 查看详情
    handleDetail(item) {
      // if (this.usertype !== 'gys') {
      //   this.powerOpen = true
      //   return
      // }
      const now = new Date().getTime()
      if (this.tenderTime && now > this.tenderTime && this.isBidding == 'true') {
        this.tenderOpen = true
        return
      }
      const { keyword, region, state, pageNum } = this.queryParams
      let params = `bidId=${item.id}`
      if (keyword) params += `&keyword=${keyword}`
      if (region) params += `&region=${region}`
      if (state) params += `&state=${state}`
      if (pageNum) params += `&pageNum=${pageNum}`
      window.open(`/tender/detail?${params}`)
    },
    // 关闭到期提醒
    handleExpiration() {
      this.expirationShow = false
      localStorage.setItem('tenderEndTime', parseTime(new Date(), '{y}-{m}-{d}'))
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
.container-box {
  overflow: unset;
}
.containerBox {
  width: 1200px;
  margin: 0 auto;
  padding-top: 10px;
}
.tender {
  &Tab {
    display: flex;
    align-items: center;
    margin-top: 20px;
    &-item {
      padding: 0 20px;
      margin-right: 40px;
      font-size: 14px;
      line-height: 32px;
      cursor: pointer;
      &.active,
      &:hover {
        line-height: 30px;
        font-size: 18px;
        border-bottom: 2px solid #292f48;
      }
    }
  }
  &Keyword {
    width: 100%;
    border: 1px solid #cbd6e2;
    box-shadow: 0 1px 9px 0 rgba(0, 0, 0, 0.07);
    background: #ffffff;
    margin: 20px 0;
    display: flex;
    align-items: center;
    position: relative;
    &:before {
      content: '';
      width: 100px;
      height: 100%;
      position: absolute;
      z-index: 1;
      top: 0;
      left: 0;
      background-color: #e7e9f0;
    }
    &-title {
      width: 100px;
      color: #999999;
      font-size: 12px;
      text-align: center;
      position: relative;
      z-index: 2;
    }
    &-con {
      width: calc(100% - 190px);
      display: inline-flex;
      align-items: center;
      flex-wrap: wrap;
    }
    &-region {
      width: calc(100% - 150px);
      display: inline-flex;
      align-items: center;
      flex-wrap: wrap;
      &.close {
        height: 46px;
        overflow: hidden;
      }
    }
    &-state {
      width: calc(100% - 100px);
      display: inline-flex;
      align-items: center;
      flex-wrap: wrap;
    }
    &-open {
      padding-right: 10px;
    }
    &-item {
      display: inline-block;
      padding: 0 14px;
      line-height: 26px;
      margin: 10px;
      font-size: 14px;
      color: #666666;
      cursor: pointer;
      position: relative;
      i {
        display: none;
        position: absolute;
        top: -6px;
        right: -6px;
      }
      &.active,
      &:hover {
        background-color: #2e73f3;
        border-radius: 5px;
        color: #ffffff;
      }
      &:hover {
        i {
          color: #333333;
          display: block;
        }
      }
    }
    &-plus {
      position: absolute;
      top: 7px;
      right: 10px;
    }
    &-edit {
      font-size: 12px;
      color: #999999;
      padding-left: 10px;
      border-left: 1px solid #999999;
      margin-left: 10px;
      cursor: pointer;
    }
    &-list {
      min-width: 180px;
      background-color: #ffffff;
      box-shadow: 0 1px 36px 0 rgba(0, 0, 0, 0.25);
      position: absolute;
      top: 40px;
      right: -10px;
      z-index: 2;
      border-radius: 10px;
      &:before {
        content: '';
        width: 0;
        height: 0;
        border-width: 15px;
        border-style: solid;
        border-color: transparent transparent #f2f4f6 transparent;
        position: absolute;
        top: -30px;
        right: 8px;
      }
      &-title {
        font-size: 12px;
        color: #666666;
        background-color: #f2f4f6;
        padding-left: 20px;
        line-height: 30px;
        border-top-left-radius: 10px;
        border-top-right-radius: 10px;
      }
      &-box {
        max-height: 600px;
        overflow-y: auto;
      }
      &-item {
        padding: 0 20px;
        font-size: 14px;
        color: #666666;
        line-height: 30px;
        border-bottom: 1px solid #f2f4f6;
        &:hover {
          background-color: #f2f4f6;
        }
      }
    }
  }
}
::v-deep {
  .pagination-container {
    background: transparent;
  }
  .product-search {
    .el-input {
      flex: 1;
    }
    .product-search-select {
      color: #999999;
      border-left: 1px solid #cbd6e2;
      .el-icon-arrow-up.is-reverse {
        color: #2e73f3;
      }
      .el-input__inner {
        color: #999999;
      }
    }
  }
}
.tenderExpire {
  display: flex;
  width: 100%;
  flex-direction: column;
  align-items: center;
  padding: 10px 30px;
  &-icon {
    font-size: 36px;
    color: #ed4040;
  }
  &-title {
    font-size: 14px;
    line-height: 20px;
    font-weight: 500;
    color: #333333;
    margin: 20px 0 10px;
  }
  &-desc {
    font-size: 12px;
    line-height: 20px;
    color: #2e73f3;
    margin-bottom: 45px;
  }
  &-button {
    width: 270px;
    height: 50px;
    line-height: 50px;
    text-align: center;
    border-radius: 5px;
    background-color: #2e73f3;
    color: #ffffff;
    cursor: pointer;
  }
}
.expirationTime {
  width: 1200px;
  height: 70px;
  background-color: #ff8d4d;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  position: relative;
  margin: 0 auto;
  color: $white;
  padding: 0 50px;
  &-close {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    right: 15px;
    width: 24px;
    height: 24px;
    cursor: pointer;
    i {
      font-size: 24px;
      color: #a65c32;
    }
  }
  &-title {
    display: inline-flex;
    align-items: center;
    font-size: 20px;
    margin-right: 50px;
    b {
      display: inline-block;
      width: 50px;
      line-height: 50px;
      border-radius: 5px;
      border: 1px solid #cbd7e2;
      font-size: 32px;
      color: #f35d09;
      text-align: center;
      background-color: $white;
      margin: 0 8px;
    }
  }
  &-tip {
    font-size: 14px;
    font-weight: 500;
    margin-right: 15px;
  }
  &-desc {
    font-size: 14px;
    opacity: 0.9;
  }
}
.product-search-option {
  .el-select-dropdown__item.selected {
    background-color: #2e73f3 !important;
    color: #ffffff;
  }
}
</style>
