<template>
  <div class="newBox bgcf9 vh-85">
    <!-- 搜索 -->
    <div class="custom-search flex" style="padding-top: 18px">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" @submit.native.prevent
        class="query-form">
        <!-- 客户名称 -->
        <el-form-item label="客户名称" prop="customerName">
          <el-input v-model="queryParams.customerName" placeholder="请输入客户名称" clearable
            @keyup.enter.native="handleQuery" />
        </el-form-item>
        <!-- 目的地 -->
        <el-form-item label="目的地" prop="address">
          <el-input v-model="queryParams.address" placeholder="请输入目的地" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <!-- 订单号 -->
        <el-form-item label="订单号" prop="number">
          <el-input v-model="queryParams.number" placeholder="请输入订单号" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <!-- 批次 -->
        <el-form-item label="批次" prop="batchNo">
          <el-select v-model="queryParams.batchNo" placeholder="请选择批次" style="width: 100%">
            <el-option label="不分批" value="不分批"></el-option>
            <el-option label="第一批" value="第一批"></el-option>
            <el-option label="第二批" value="第二批"></el-option>
            <el-option label="第三批" value="第三批"></el-option>
            <el-option label="第四批" value="第四批"></el-option>
            <el-option label="第五批" value="第五批"></el-option>
          </el-select>
        </el-form-item>
        <!-- 需求方 -->
        <!-- <el-form-item label="需求方" prop="demander">
          <el-input v-model="queryParams.demander" placeholder="请输入需求方" clearable @keyup.enter.native="handleQuery" />
        </el-form-item> -->
        <!-- 派车部门 -->
        <el-form-item label="派车部门" prop="deptId">
          <el-select v-model="queryParams.deptId" placeholder="请选择派车部门" clearable @change="handleQuery">
            <el-option v-for="item in deptOptions" :key="item.deptId" :label="item.deptName"
              :value="item.deptId"></el-option>
          </el-select>
        </el-form-item>
        <!-- 派车人 -->
        <!-- <el-form-item label="派车人" prop="dispatchPersonId">
          <el-cascader v-model="queryParams.dispatchPersonId" :options="userOptions" :props="userProps" filterable :show-all-levels="false" placeholder="请选择派车人" @change="handleQuery" clearable></el-cascader>
        </el-form-item> -->
        <!-- 状态 -->
        <el-form-item label="状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="请选择状态" @change="handleQuery">
            <el-option label="已取消" value="0"></el-option>
            <el-option label="待接单" value="1"></el-option>
            <el-option label="已接单" value="2"></el-option>
            <el-option label="已完成" value="3"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item class="flex0">
          <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="small" @click="handleResetQuery">重置</el-button>
          <!-- 新增派车单 -->
          <el-button type="primary" size="small" icon="el-icon-plus" @click="handleAdd">新增派车单</el-button>
          <!-- 导出 -->
          <el-button class="organge" size="small" icon="el-icon-upload2" @click="handleExport">导出列表</el-button>
          <!-- 车辆管理 -->
          <el-button type="primary" size="small" icon="el-icon-shopping-cart-1" @click="handleCar">车辆管理</el-button>
        </el-form-item>
      </el-form>
    </div>
    <!-- 列表 -->
    <div style="padding: 15px 20px">
      <el-table v-loading="loading" ref="allTable" stripe :data="list" row-key="id" style="width: 100%"
        class="custom-table" v-if="total > 0">
        <el-table-column align="center" type="index" label="序号"></el-table-column>
        <!-- 客户名称 -->
        <el-table-column align="center" prop="customerName" label="客户名称" show-overflow-tooltip></el-table-column>
        <!-- 目的地 -->
        <el-table-column align="center" prop="address" label="目的地" show-overflow-tooltip></el-table-column>
        <!-- 货物数量 -->
        <el-table-column align="center" prop="quantity" label="货物数量" show-overflow-tooltip></el-table-column>
        <!-- 是否计费 -->
        <el-table-column align="center" prop="isCharge" label="是否计费" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <span>{{ row.isCharge === 1 ? '是' : '否' }}</span>
          </template>
        </el-table-column>
        <!-- 订单号 -->
        <el-table-column align="center" prop="number" label="订单号" show-overflow-tooltip></el-table-column>
        <!-- 批次 -->
        <el-table-column align="center" prop="batchNo" label="批次" show-overflow-tooltip></el-table-column>
        <!-- 优先级 -->
        <el-table-column align="center" prop="status" label="优先级" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <span style="color: #F43F3F;"
              v-if="priority.emergency.start > 0 ? (new Date(row.deadlineTime).getTime() - new Date().getTime()) > priority.emergency.start * 60 * 60 * 1000 && (new Date(row.deadlineTime).getTime() - new Date().getTime()) < priority.emergency.end * 60 * 60 * 1000 : (new Date(row.deadlineTime).getTime() - new Date().getTime()) < priority.emergency.end * 60 * 60 * 1000">{{
                '紧急' }}</span>
            <span style="color: #F35D09"
              v-if="(new Date(row.deadlineTime).getTime() - new Date().getTime()) > priority.priority.start * 60 * 60 * 1000 && (new Date(row.deadlineTime).getTime() - new Date().getTime()) < priority.priority.end * 60 * 60 * 1000">{{
                '优先' }}</span>
            <span style="color: #1DC86B"
              v-if="priority.general.end !== null ? (new Date(row.deadlineTime).getTime() - new Date().getTime()) > priority.general.start * 60 * 60 * 1000 && (new Date(row.deadlineTime).getTime() - new Date().getTime()) < priority.general.end * 60 * 60 * 1000 : (new Date(row.deadlineTime).getTime() - new Date().getTime()) > priority.general.start * 60 * 60 * 1000">{{
                '一般' }}</span>
          </template>
        </el-table-column>
        <!-- 需求方 -->
        <el-table-column align="center" prop="demander" label="需求方" show-overflow-tooltip></el-table-column>
        <!-- 派车部门 -->
        <el-table-column align="center" prop="deptName" label="派车部门" show-overflow-tooltip></el-table-column>
        <!-- 派车人 -->
        <el-table-column align="center" prop="dispatchPerson" label="派车人" show-overflow-tooltip></el-table-column>
        <!-- 派车时间 -->
        <el-table-column align="center" prop="time" label="派车时间" show-overflow-tooltip></el-table-column>
        <!-- 派送车辆 -->
        <el-table-column align="center" prop="carId" label="派送车辆" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <span>{{ row.carId && row.car && row.car.name }}</span>
          </template>
        </el-table-column>
        <!-- 状态 -->
        <el-table-column align="center" prop="status" label="状态" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <span v-if="row.status == '0'" style="color: #333333;">{{ '已取消' }}</span>
            <span v-if="row.status == '1'" style="color: #F35D09;">{{ '待接单' }}</span>
            <span v-if="row.status == '2'" style="color: #2E73F3;">{{ '已接单' }}</span>
            <span v-if="row.status == '3'" style="color: #999999;">{{ '已完成' }}</span>
          </template>
        </el-table-column>
        <!-- 操作 -->
        <el-table-column align="center" label="操作" :width="doWidth">
          <template slot-scope="{ row }">
            <el-button class="table-btn" @click="handleDetail(row)">查看详情</el-button>
            <el-button class="table-btn danger" @click="handleCancel(row)" v-if="row.status == '1'">取消</el-button>
            <el-button class="table-btn primary" @click="handleUpdate(row)" v-hasPermi="['sendCar:update']"
              v-if="row.status == '1'">修改</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-empty v-if="total == 0 && !loading" />
      <!-- 分页 -->
      <div class="custom-pagination">
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize" @pagination="getList" />
      </div>
    </div>
    <!-- 新增派车单 -->
    <create-dialog ref="createDialog" :userList="userList" :userOptions="userOptions" :deptOptions="deptOptions"
      @callBack="handleCallBack" v-if="isCreate" />
    <!-- 导出 -->
    <export-dialog ref="exportDialog" @callBack="isExport = false" v-if="isExport" />
    <!-- 派送车辆 -->
    <car-dialog ref="carDialog" v-if="isCar" />
  </div>
</template>
<script>
import { listDept } from '@/api/system/dept'
import { deptTreeSelect, listUser } from '@/api/system/user'
import { getSendCarList, updateSendCarStatus } from '@/api/sendCar'
import createDialog from './create'
import exportDialog from './export'
import carDialog from './car'
import { checkPermi } from '@/utils/permission'
import { getConfigDetail } from '@/api/config'

export default {
  name: 'SendCar',
  components: { createDialog, exportDialog, carDialog },
  data() {
    return {
      // 搜索
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        address: undefined, // 目的地
        carId: undefined, // 派送车辆
        customerName: undefined, // 客户名称
        deptId: undefined, // 派车部门
        dispatchPersonId: undefined, // 派车人
        endTime: undefined, // 结束时间
        isCharge: undefined, // 是否计费
        number: undefined, // 订单号
        startTime: undefined, // 开始时间
        status: '1' // 状态
      },
      // 列表
      list: [],
      total: 0,
      loading: false,
      isCreate: false,
      isExport: false,
      isCar: false,
      userList: [],
      userOptions: [],
      userProps: {
        expandTrigger: 'hover', // 展开方式
        emitPath: false, // 是否返回路径
        multiple: false // 是否多选
      },
      deptOptions: [],
      priority: { emergency: { start: 0, end: 24 }, priority: { start: 24, end: 72 }, general: { start: 72, end: null } }
    }
  },
  created() {
    // 获取列表
    this.getList()
    // 获取部门和人员
    this.getApprovalsOptions()
    // 获取企业参数
    this.getConfig()
  },
  computed: {
    doWidth() {
      return checkPermi(['sendCar:update']) && this.queryParams.status == '1' ? 330 : this.queryParams.status == '1' ? 220 : 130
    }
  },
  methods: {
    getConfig() {
      getConfigDetail({ configKey: 'dispatch.car.priority' }).then(res => {
        this.priority = JSON.parse(res.data.configValue)
      })
    },
    // 部门和人员
    async getApprovalsOptions() {
      const deptList = await listDept()
      this.deptOptions = deptList.data
      const dept = await deptTreeSelect()
      const user = await listUser()
      const children = dept.data[0].children || []
      const deptData = [...children, ...[{ id: -1, label: '其他', value: 'dept-0' }]] || []
      const userData = user.rows || []
      this.userList = userData
      const getChildren = data => {
        data.forEach(item => {
          item.value = 'dept-' + item.id
          if (item.children) {
            getChildren(item.children)
          } else {
            item.children = []
          }
        })
      }
      getChildren(deptData)
      const addChildren = data => {
        data.forEach(item => {
          userData.forEach(user => {
            if (item.id === user.deptId && item.children) {
              item.children.push({
                userId: user.userId,
                label: user.realName || user.nickName,
                value: user.userId,
                disabled: user.status == '1',
                userName: user.userName
              })
            }
            if (item.id === -1 && (!user.deptId || user.deptId === dept.data[0].id) && item.children) {
              item.children.push({
                userId: user.userId,
                label: user.realName || user.nickName,
                value: user.userId,
                disabled: user.status == '1',
                userName: user.userName
              })
            }
          })
          if (item.children && item.children.length) {
            addChildren(item.children)
          }
        })
      }
      addChildren(deptData)
      this.userOptions = deptData
    },
    // 列表
    // prettier-ignore
    getList() {
      this.loading = true
      getSendCarList(this.queryParams).then(res => {
        const { code, rows, total, msg } = res
        if (code === 200) {
          this.list = rows
          this.total = total
        } else this.$message.error(msg)
      }).finally(() => {
        this.loading = false
      })
    },
    // 刷新列表
    refreshList() {
      getSendCarList(this.queryParams).then(res => {
        const { code, rows, total } = res
        if (code === 200) {
          this.$set(this, 'list', rows)
          this.$set(this, 'total', total)
        }
      })
    },
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 重置搜索
    handleResetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 新增派车单
    handleAdd() {
      this.isCreate = true
      this.$nextTick(() => {
        this.$refs.createDialog.handleOpen()
      })
    },
    // 修改
    handleUpdate(row) {
      this.isCreate = true
      this.$nextTick(() => {
        this.$refs.createDialog.handleUpdate(row)
      })
    },
    // 导出列表
    handleExport() {
      this.isExport = true
      this.$nextTick(() => {
        this.$refs.exportDialog.handleOpen()
      })
    },
    // 查看详情
    handleDetail(row) {
      this.isCreate = true
      this.$nextTick(() => {
        this.$refs.createDialog.handleDetail(row)
      })
    },
    // 取消
    // prettier-ignore
    handleCancel(row) {
      this.$confirm('确定要取消吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        updateSendCarStatus({ id: row.id, status: 0 }).then(res => {
          if (res.code === 200) {
            this.$message.success('取消成功')
            this.refreshList()
          } else this.$message.error(res.msg)
        })
      }).catch(() => { })
    },
    // 回调
    handleCallBack(refresh = false) {
      this.isCreate = false
      if (refresh) this.getList()
      else this.refreshList()
    },
    // 车辆管理
    handleCar() {
      this.isCar = true
      this.$nextTick(() => {
        this.$refs.carDialog.getList()
      })
    }
  }
}
</script>
<style scoped lang="scss">
@import '@/assets/styles/custom-new.scss';

.el-button.organge {
  background-color: #f35d09;
  border-color: #f35d09;
  color: #fff;

  &:hover {
    opacity: 0.8;
  }
}

.el-button.table-btn {
  padding: 0;
}

.query-form ::v-deep {
  display: flex;
  align-items: center;

  .el-form-item {
    display: flex;
    align-items: center;

    .el-form-item__label {
      flex-shrink: 0;
    }
  }

  .flex0 {
    flex-shrink: 0;
  }
}
</style>
