<template>
  <div>
    <el-dialog v-dialogDragBox title="车辆管理" :visible.sync="open" width="1150px" @close="handleCancel"
      class="custom-dialog" append-to-body>
      <div style="padding: 0 20px">
        <div class="flex">
          <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" @submit.native.prevent>
            <!-- 客户名称 -->
            <el-form-item label="名称" prop="name">
              <el-input v-model="queryParams.name" placeholder="名称" clearable @keyup.enter.native="handleQuery" />
            </el-form-item>
            <!-- 目的地 -->
            <el-form-item label="车牌" prop="plateNumber">
              <el-input v-model="queryParams.plateNumber" placeholder="请输入车牌" clearable
                @keyup.enter.native="handleQuery" />
            </el-form-item>
            <!-- 状态 -->
            <el-form-item label="状态" prop="status">
              <el-select v-model="queryParams.status" placeholder="请选择状态" @change="handleQuery">
                <el-option label="启用" value="1"></el-option>
                <el-option label="停用" value="0"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
              <el-button icon="el-icon-refresh" size="small" @click="handleResetQuery">重置</el-button>
              <el-button type="primary" icon="el-icon-plus" size="small" @click="handleAdd">新增</el-button>
            </el-form-item>
          </el-form>
        </div>
        <el-table v-loading="loading" ref="allTable" stripe :data="list" row-key="id" style="width: 100%"
          class="custom-table" v-if="total > 0">
          <el-table-column align="center" type="index" label="序号" width="50"></el-table-column>
          <!-- 名称 -->
          <el-table-column align="center" prop="name" label="名称" show-overflow-tooltip></el-table-column>
          <!-- 车牌 -->
          <el-table-column align="center" prop="plateNumber" label="车牌" show-overflow-tooltip></el-table-column>
          <!-- 图片 -->
          <el-table-column align="center" label="图片">
            <template slot-scope="{ row }">
              <el-image style="width: 100px; height: 100px" v-if="row.imgArr" :src="row.imgArr[0]"
                :preview-src-list="row.imgArr">
              </el-image>
              <div v-else>--</div>
            </template>
          </el-table-column>
          <!-- 车辆品牌 -->
          <el-table-column align="center" prop="brand" label="品牌" show-overflow-tooltip></el-table-column>
          <!-- 型号 -->
          <el-table-column align="center" prop="model" label="车型" show-overflow-tooltip></el-table-column>
          <!-- GPS设备号 -->
          <el-table-column align="center" prop="gpsNumber" label="GPS设备号" show-overflow-tooltip></el-table-column>
          <!-- 状态 -->
          <el-table-column align="center" prop="status" label="状态" show-overflow-tooltip width="80">
            <template slot-scope="{ row }">
              <el-switch v-model="row.status" active-text="启用" inactive-text="停用" :active-value="1" :inactive-value="0"
                @change="handleStatusChange(row)" class="table-switch"></el-switch>
            </template>
          </el-table-column>
          <!-- 操作 -->
          <el-table-column align="center" label="操作" show-overflow-tooltip width="260">
            <template slot-scope="{ row }">
              <el-button type="text" icon="el-icon-edit" @click="handleEdit(row)">修改</el-button>
              <el-button type="text" icon="el-icon-location-information" @click="handleLocation(row)">查看定位</el-button>
              <el-button type="text" icon="el-icon-map-location" @click="handleTrajectory(row)">历史轨迹</el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-empty v-if="total == 0 && !loading" />
        <!-- 分页 -->
        <div class="custom-pagination">
          <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize" />
        </div>
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn primary" @click="handleCancel">关 闭</el-button>
      </div>
    </el-dialog>
    <!-- 新增车辆 -->
    <el-dialog v-dialogDragBox title="新增车辆" :visible.sync="addOpen" width="800px" class="custom-dialog" append-to-body>
      <div style="padding: 0 20px">
        <el-form ref="form" :model="form" :rules="rules" label-width="4em" class="custom-form">
          <el-row :gutter="20">
            <!-- 名称 -->
            <el-col :span="12">
              <el-form-item label="名称" prop="name">
                <el-input v-model="form.name" placeholder="请输入名称" clearable />
              </el-form-item>
            </el-col>
            <!-- 车辆品牌 -->
            <el-col :span="12">
              <el-form-item label="品牌" prop="brand">
                <el-input v-model="form.brand" placeholder="请输入车辆品牌" clearable />
              </el-form-item>
            </el-col>
            <!-- 车型 -->
            <el-col :span="12">
              <el-form-item label="车型" prop="model">
                <el-input v-model="form.model" placeholder="请输入车型" clearable />
              </el-form-item>
            </el-col>
            <!-- 车牌 -->
            <el-col :span="12">
              <el-form-item label="车牌" prop="plateNumber">
                <el-input v-model="form.plateNumber" placeholder="请输入车牌" clearable />
              </el-form-item>
            </el-col>
            <!-- 车架号 -->
            <el-col :span="12">
              <el-form-item label="车架号" prop="brand">
                <el-input v-model="form.frameNum" placeholder="请输入车架号" clearable />
              </el-form-item>
            </el-col>
            <!-- GPS设备号 -->
            <el-col :span="12">
              <el-form-item label="设备号" prop="gpsNumber">
                <el-input v-model="form.gpsNumber" placeholder="请输入GPS设备号" clearable />
              </el-form-item>
            </el-col>
            <!-- 车辆图片 -->
            <el-col :span="24">
              <el-form-item label="车辆图片" prop="image">
                <image-upload v-model="form.image" :file-type="fileType" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn" @click="addOpen = false">取 消</el-button>
        <el-button class="custom-dialog-btn primary" @click="handleSubmit">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 修改车辆 -->
    <el-dialog v-dialogDragBox title="车辆信息修改" :visible.sync="editOpen" width="800px" class="custom-dialog"
      append-to-body>
      <div style="padding: 0 20px">
        <el-form ref="editForm" :model="editForm" :rules="editRules" label-width="6em" class="custom-form">
          <el-row :gutter="20">
            <!-- 名称 -->
            <el-col :span="12">
              <el-form-item label="名称：" prop="name">
                <div>{{ editForm.name }}</div>
              </el-form-item>
            </el-col>
            <!-- 车辆品牌 -->
            <el-col :span="12">
              <el-form-item label="品牌" prop="brand">
                <el-input v-model="editForm.brand" placeholder="请输入车辆品牌" clearable />
              </el-form-item>
            </el-col>
            <!-- 车架号 -->
            <el-col :span="12">
              <el-form-item label="车架号" prop="brand">
                <el-input v-model="editForm.frameNum" placeholder="请输入车架号" clearable />
              </el-form-item>
            </el-col>
            <!-- GPS设备号 -->
            <el-col :span="12">
              <el-form-item label="设备号" prop="gpsNumber">
                <el-input v-model="editForm.gpsNumber" placeholder="请输入GPS设备号" clearable />
              </el-form-item>
            </el-col>
            <!-- 车辆图片 -->
            <el-col :span="24">
              <el-form-item label="车辆图片" prop="image">
                <image-upload v-model="editForm.image" :file-type="fileType" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn" @click="editOpen = false">取 消</el-button>
        <el-button class="custom-dialog-btn primary" @click="handleSubmitGpsNumber">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 轨迹回放 -->
    <el-dialog v-dialogDragBox :title="mapType === '折线' ? '轨迹回放' : '位置查看'" :visible.sync="locationOpen" width="1150px"
      class="custom-dialog" append-to-body>
      <div>
        <div style="padding:0 20px 20px; display: flex; align-items: center;" v-if="mapType === '折线'">
          <span style="margin-right: 20px;">轨迹起止时间：</span>
          <el-date-picker v-model="times" type="datetimerange" value-format="yyyyMMddHHmmss"
            :picker-options="pickerOptions" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
            align="right" @change="handleTimes">
          </el-date-picker>
        </div>
        <!-- 百度地图 -->
        <bmap :width="'100%'" :height="'500px'" :position="position" :positionContent="positionContent" :polygonPath="polygonPath" :mapType="mapType"></bmap>
        <!-- <baidu-map class="map" :center="position" :zoom="15" :scroll-wheel-zoom="true">
          <bm-navigation anchor="BMAP_ANCHOR_TOP_RIGHT"></bm-navigation>

          <bm-polyline :path="polygonPath" stroke-color="green" :stroke-opacity="0.5" :stroke-weight="2"
            v-if="mapType === '折线'" />
          <bm-point-collection :points="polygonPath" shape="BMAP_POINT_SHAPE_WATERDROP" @click="clickHandler"
            v-if="mapType === '折线'"></bm-point-collection>

          <bm-marker :position="position" animation="BMAP_ANIMATION_BOUNCE" @click="infoWindowOpen"
            v-if="mapType === '点'">
            <bm-info-window :show="show" @close="infoWindowClose" @open="infoWindowOpen">{{ positionContent
            }}</bm-info-window>
          </bm-marker>
        </baidu-map> -->
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn primary" @click="locationOpen = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { addCar, getCarList, updateCarStatus, updateCarGpsNumber } from '@/api/sendCar'
import { getGps } from '@/api/gps'
import SparkMD5 from 'spark-md5';
import { parseTime } from '@/utils/ruoyi'
import bmap from './bMap.vue'

export default {
  components: {
    bmap
  },
  data() {
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: undefined, // 车辆名称
        plateNumber: undefined, // 车牌号
        status: '1' // 状态
      },
      list: [],
      loading: false,
      total: 0,
      open: false,
      // 新增
      addOpen: false,
      form: {},
      rules: {
        name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
        model: [{ required: true, message: '请输入车型', trigger: 'blur' }],
        plateNumber: [{ required: true, message: '请输入车牌', trigger: 'blur' }]
      },
      fileType: ['png', 'PNG', 'jpg', 'JPG', 'jpeg', 'JPEG'],
      // gps
      gpsToken: '',
      deviceId: '',
      locationOpen: false,
      mapType: '点',
      position: {},
      positionContent: '',
      show: false,
      polygonPath: [],
      editOpen: false,
      editForm: {
        carId: '',
        name: '',
        gpsNumber: '',
        brand: '',
        frameNum: '',
        image: ''
      },
      editRules: {
        gpsNumber: [{ required: true, message: '请输入GPS设备号', trigger: 'blur' }],
      },
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
      times: [parseTime(new Date(), '{y}{m}{d}') + '000000', parseTime(new Date(), '{y}{m}{d}') + '235959'],
      stime: parseTime(new Date(), '{y}{m}{d}') + '000000',
      etime: parseTime(new Date(), '{y}{m}{d}') + '235959'
    }
  },
  methods: {
    parseTime,
    // 查询列表
    // prettier-ignore
    getList() {
      this.loading = true
      getCarList(this.queryParams).then(res => {
        const { code, rows, total, msg } = res
        if (code === 200) {
          rows.forEach(el => {
            if (el.image) {
              el.imgArr = []
              el.image.split(',').forEach(m => {
                m = this.imgPath + m
                console.log(m)
                el.imgArr.push(m)
              })
            }
          })
          this.list = rows
          console.log(this.list)
          this.total = total
          this.open = true
        } else this.$message.error(msg)
      }).finally(() => {
        this.loading = false
      })
    },
    // 刷新列表
    refreshList() {
      getCarList(this.queryParams).then(res => {
        const { code, rows, total } = res
        if (code === 200) {
          this.$set(this, 'list', rows)
          this.$set(this, 'total', total)
        }
      })
    },
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 重置
    handleResetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 关闭
    handleCancel(refresh = false) {
      this.open = false
      this.$emit('callBack', refresh)
    },
    // 状态修改
    // prettier-ignore
    handleStatusChange(row) {
      const { id, status } = row
      const confirmMessage = status === 1 ? '是否要启用?' : '是否要禁用?';
      this.$confirm(confirmMessage, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        updateCarStatus({ id, status }).then(res => {
          if (res.code === 200) {
            this.$message.success('操作成功')
            this.refreshList()
          } else this.$message.error(res.msg)

        })
      }).catch(() => {
        row.status = row.status === 1 ? 0 : 1
      })
    },
    // 新增车辆
    handleAdd() {
      this.form = {
        model: undefined,
        name: undefined,
        plateNumber: undefined,
        gpsNumber: undefined,
        brand: undefined,
        image: undefined,
        frameNum: undefined,
      }
      this.resetForm('form')
      this.addOpen = true
    },
    // 提交
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          addCar(this.form).then(res => {
            if (res.code === 200) {
              this.$message.success('新增成功')
              this.refreshList()
              this.addOpen = false
            } else this.$message.error(res.msg)
          })
        }
      })
    },
    // GPS
    // 轨迹
    handleTrajectory(row) {
      let data = {
        apicode: 1200,
        account: 'sskj',
        password: SparkMD5.hash(SparkMD5.hash('123456') + SparkMD5.hash('sskj')),
        apitoken: 'a4475031-d2af-4695-a722-8f6128d90581'
      }
      getGps(data).then(res => {
        if (res.code == 1) {
          this.gpsToken = res.token
          this.deviceId = row.gpsNumber
          this.getTrajectory()
        } else {
          this.$message.error(res.message)
        }
      })
    },
    getTrajectory() {
      this.locationOpen = true
      this.mapType = '折线'
      let data = {
        apicode: 1202,
        deviceId: this.deviceId,
        stime: this.stime,
        etime: this.etime,
        static: true,
        invalid: true,
        lingxtoken: this.gpsToken
      }
      getGps(data).then(res => {
        if (res.code == 1) {
          if (res.data.length > 0) {
            this.polygonPath = []
            this.position = {
              lng: res.data[0].lng_baidu,
              lat: res.data[0].lat_baidu
            }
            res.data.forEach(el => {
              let obj = {
                lng: el.lng_baidu,
                lat: el.lat_baidu
              }
              this.polygonPath.push(obj)
            });
          } else {
            this.$message.error('该时间段没有行车轨迹')
          }
        } else {
          this.$message.error(res.message)
        }
      })
    },
    clickHandler(e) {
      // alert(`单击点的坐标为：${e.point.lng}, ${e.point.lat}`);
    },
    // 定位
    handleLocation(row) {
      let data = {
        apicode: 1200,
        account: 'sskj',
        password: SparkMD5.hash(SparkMD5.hash('123456') + SparkMD5.hash('sskj')),
        apitoken: 'a4475031-d2af-4695-a722-8f6128d90581'
      }
      getGps(data).then(res => {
        if (res.code == 1) {
          this.gpsToken = res.token
          this.deviceId = row.gpsNumber
          this.getLocation()
        } else {
          this.$message.error(res.message)
        }
      })
    },
    getLocation() {
      let data = {
        apicode: 1201,
        deviceId: this.deviceId,
        lingxtoken: this.gpsToken
      }
      getGps(data).then(res => {
        if (res.code == 1) {
          this.position = {
            lng: res.data[0].lng_baidu,
            lat: res.data[0].lat_baidu
          }
          this.positionContent = res.data[0].address
          this.mapType = '点'
          this.locationOpen = true
        } else {
          this.$message.error(res.message)
        }
      })
    },
    infoWindowClose() {
      this.show = false
    },
    infoWindowOpen() {
      this.show = true
    },
    handleEdit(row) {
      this.editForm = {
        name: row.name,
        brand: row.brand,
        frameNum: row.frameNum,
        gpsNumber: row.gpsNumber,
        image: row.image,
        carId: row.id
      }
      this.editOpen = true
    },
    handleSubmitGpsNumber() {
      this.$refs.editForm.validate(valid => {
        if (valid) {
          updateCarGpsNumber(this.editForm).then(res => {
            if (res.code === 200) {
              this.$message.success('绑定成功')
              this.refreshList()
              this.editOpen = false
            } else this.$message.error(res.msg)
          })
        }
      })
    },
    handleTimes() {
      this.stime = this.times[0]
      this.etime = this.times[1]
      this.getTrajectory()
    }
  }
}
</script>
<style scoped lang="scss">
@import '@/assets/styles/custom-new.scss';

.custom-table ::v-deep {
  .el-table__body-wrapper .table-switch {
    .el-switch__label {
      width: 50px !important;
    }

    .el-switch__core {
      width: 50px !important;
    }
  }
}

.map {
  width: 100%;
  height: 500px;
}
</style>
