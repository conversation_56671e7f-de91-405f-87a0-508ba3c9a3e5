<template>
  <div>
    <el-dialog v-dialogDragBox :title="title" :visible.sync="open" width="1150px" @close="handleCancel" class="custom-dialog" append-to-body>
      <div style="padding: 0 20px">
        <div class="export-desc">
          <img src="~@/assets/images/export.png" alt="" class="img" />
          <div class="flex1 flex flex-column">
            <span class="title">您正在导出派车单数据，为保证数据正确请正确操作</span>
            <span class="desc">请无比确认根据需要选择时间范围、单据状态以及车辆，完成后点击导出按钮即可</span>
          </div>
        </div>
        <el-form ref="form" :model="form" :rules="rules" label-width="6em" class="custom-form">
          <el-row :gutter="20">
            <!-- 时间范围 -->
            <el-col :span="12">
              <el-form-item label="时间范围" prop="timeRange">
                <el-date-picker v-model="form.timeRange" type="daterange" start-placeholder="开始日期" end-placeholder="结束日期" format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss" style="width: 100%" />
              </el-form-item>
            </el-col>
            <!-- 选择状态 -->
            <el-col :span="12">
              <el-form-item label="选择状态" prop="status">
                <el-select v-model="form.status" placeholder="请选择状态" style="width: 100%">
                  <el-option label="正常" :value="1"></el-option>
                  <el-option label="已取消" :value="0"></el-option>
                  <el-option label="全部状态" value="all"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24"></el-col>
            <!-- 派送车辆 -->
            <el-col :span="12">
              <el-form-item label="派送车辆" prop="carId">
                <el-select v-model="form.carId" filterable remote reserve-keyword placeholder="请选择派送车辆" :loading="carLoading" style="width: 100%" clearable>
                  <el-option label="全部车辆" value="all"></el-option>
                  <el-option v-for="item in carList" :label="item.name" :value="item.id" :key="item.id">
                    <span>{{ item.name }}</span>
                    <span style="float: right; color: #999; font-size: 13px">{{ item.plateNumber }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn" @click="handleCancel">取 消</el-button>
        <el-button class="custom-dialog-btn primary" @click="handleSubmit">立即导出</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { exportSendCar, getCarList } from '@/api/sendCar'

export default {
  data() {
    return {
      title: '导出派车单',
      open: false,
      form: {},
      rules: {
        timeRange: [{ required: true, message: '请选择时间范围', trigger: 'change' }],
        status: [{ required: true, message: '请选择状态', trigger: 'change' }],
        carId: [{ required: true, message: '请选择派送车辆', trigger: 'change' }]
      },
      carList: [],
      carLoading: false
    }
  },
  created() {
    this.getCarList()
  },
  methods: {
    // 获取车辆列表
    getCarList() {
      getCarList().then(res => {
        const { code, rows } = res
        if (code === 200) this.carList = rows
      })
    },
    // 重置导出表单
    reset() {
      this.form = {
        carId: undefined,
        status: 1,
        timeRange: []
      }
      this.resetForm('form')
    },
    // 打开导出
    handleOpen() {
      this.reset()
      this.title = '导出派车单'
      this.open = true
    },
    // 确定导出
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          let { carId, status, timeRange } = this.form
          const [startTime, endTime] = timeRange
          if (status === 'all') status = undefined
          if (carId === 'all') carId = undefined
          const data = { carId, status, startTime, endTime }
          this.download('dispatch/car/document/export', data, `${startTime}至${endTime}派车单.xlsx`)
        }
      })
    },
    // 取消导出
    handleCancel() {
      this.open = false
      this.$emit('callBack')
    }
  }
}
</script>
<style scoped lang="scss">
@import '@/assets/styles/custom-new.scss';
.export-desc {
  display: flex;
  align-items: center;
  padding: 0 50px;
  height: 128px;
  background-color: #edf3ff;
  border-radius: 5px;
  margin-bottom: 20px;
  .img {
    width: 90px;
    height: 90px;
    flex: 0 0 90px;
    margin-right: 90px;
  }
  .title {
    font-size: 18px;
    font-weight: 300;
    line-height: 20px;
    margin-bottom: 10px;
    color: $blue;
  }
  .desc {
    font-size: 14px;
    line-height: 20px;
    color: $blue;
  }
}
</style>
