<template>
    <div id="allmap" :style="{ width: width, height: height }"></div>
</template>
<script>
import { BMapLoader } from '@/utils/mapLoader'
export default {
    name: 'BMap',
    data() {
        return {
            map: null,
            BMap: null
        }
    },
    props: {
        // 传入的经纬度
        position: {
            type: Object,
            default: () => ({ lng: 114.5394580885402, lat: 36.82023282959307 }) // 默认经纬度
        },
        // 位置信息
        positionContent: {
            type: String,
            default: '河北世盛金属制品有限公司'
        },
        // 轨迹
        polygonPath: {
            type: Array,
            default: () => []
        },
        // 类型
        mapType: {
            type: String,
            default: '点' // 默认使用百度地图
        },
        // 地图容器的宽度和高度
        width: {
            type: String,
            default: '100%'
        },
        height: {
            type: String,
            default: '500px'
        }
    },
    created() {
        this.initMap()
    },
    destroyed() {
        this.map.destroy()
    },
    watch: {
        position: {
            handler(newVal) {
                if (this.map) {
                    this.map.clearOverlays() // 清除之前的标记
                    this.initMap() // 重新初始化地图
                    // this.addMarker(this.BMap, this.map) // 重新添加标记
                }
            },
            deep: true
        }
    },
    methods: {
        initMap() {
            this.$nextTick(() => {
                BMapLoader('q5wRzbxZbdpCLrxOcgCWXN1QnKYGnHm2').then(BMap => {
                    const map = new BMap.Map('allmap', { enableMapClick: false })
                    this.map = map
                    map.disableKeyboard() // 禁用键盘操作地图
                    map.enableScrollWheelZoom(true) // 开启鼠标滚轮缩放
                    // 添加中心点和缩放等级，如果不添加，初始化出来页面为白屏
                    const point = new BMap.Point(this.position.lng, this.position.lat)
                    map.centerAndZoom(point, 15)
                    this.BMap = BMap
                    if (this.mapType === '折线') {
                        this.addPolyline(BMap, map)
                    } else if (this.mapType === '点') {
                        this.addMarker(BMap, map)
                        
                    }
                })
            })
        },
        addMarker(BMap, map) {
            const point = new BMap.Point(this.position.lng, this.position.lat)
            map.addOverlay(new BMap.Marker(point))
            // 添加信息窗口
            const infoWindow = new BMap.InfoWindow(this.positionContent, { width: 200, height: 100 })
            map.openInfoWindow(infoWindow, point)
            // 添加地图事件监听
            map.addEventListener('click', () => {
                map.openInfoWindow(infoWindow, point)
            })
        },
        // 添加轨迹线（折线）
        addPolyline(BMap, map) {
            if (this.polygonPath.length > 0) {
                // 清除之前的折线
                map.clearOverlays()
                // 轨迹视角动画
                map.setViewport(this.polygonPath)
                // 添加起点标记
                const startPoint = new BMap.Point(this.polygonPath[0].lng, this.polygonPath[0].lat)
                let startPointMarker = new BMap.Marker(startPoint)
                map.addOverlay(startPointMarker)
                // 添加起点信息窗口
                const startInfoWindow = new BMap.InfoWindow('起点', { width: 50, height: 30 })
                map.openInfoWindow(startInfoWindow, startPoint)
                // 添加起点点击事件
                startPointMarker.addEventListener('click', () => {
                    console.log('起点信息窗口')
                    map.openInfoWindow(startInfoWindow, startPoint)
                })
                // 添加终点标记
                const endPoint = new BMap.Point(this.polygonPath[this.polygonPath.length - 1].lng, this.polygonPath[this.polygonPath.length - 1].lat)
                let endPointMarker = new BMap.Marker(endPoint)
                map.addOverlay(endPointMarker)
                // 添加终点信息窗口
                const endInfoWindow = new BMap.InfoWindow('终点', { width: 50, height: 30 })
                map.openInfoWindow(endInfoWindow, endPoint)
                // 添加终点点击事件
                endPointMarker.addEventListener('click', () => {
                    console.log('终点信息窗口')
                    map.openInfoWindow(endInfoWindow, endPoint)
                })
                // 轨迹动画效果
                this.TrackAnimation(map, this.polygonPath)
                // 添加折线
                // 将经纬度转换为BMap.Point对象数组
                let polygonPath = this.polygonPath.map(item => ({ lng: item.lng, lat: item.lat }))
                // 创建折线
                polygonPath = this.polygonPath.map(item => new BMap.Point(item.lng, item.lat))
                // 添加折线到地图
                const polyline = new BMap.Polyline(polygonPath, { strokeColor: 'red', strokeWeight: 2, strokeOpacity: 0.5 })
                map.addOverlay(polyline)
            }
        },
        // 轨迹动画
        TrackAnimation(map, path) {
            if (path.length > 0) {
                let index = 0
                const marker = new this.BMap.Marker(new this.BMap.Point(path[0].lng, path[0].lat))
                map.addOverlay(marker)
                const interval = setInterval(() => {
                    if (index < path.length) {
                        marker.setPosition(new this.BMap.Point(path[index].lng, path[index].lat))
                        index++
                    } else {
                        clearInterval(interval)
                    }
                }, 100) // 每秒移动到下一个点
            }
        }
    }
}
</script>
