<template>
  <div class="newBox bgcf9 vh-85">
    <!-- 搜索 -->
    <div class="custom-search flex" style="padding-top: 18px">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" @submit.native.prevent>
        <el-form-item label="会议名称" prop="meetingName">
          <el-input v-model="queryParams.meetingName" placeholder="请输入会议名称" clearable
            @keyup.enter.native="handleQuery" />
        </el-form-item>
        <!--状态-->
        <!-- <el-form-item label="状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="请选择状态" clearable @change="handleQuery">
            <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item> -->
        <el-form-item label="部门" prop="deptId" v-if="checkPermi(['meeting:data:all'])">
          <el-select v-model="queryParams.deptId" placeholder="请选择部门" clearable @change="handleQuery">
            <el-option v-for="(item, index) in deptOptions" :key="index" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="small" @click="handleResetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <!-- 列表 -->
    <div style="padding: 15px 20px">
      <el-table v-loading="loading" ref="allTable" stripe
        :data="list.slice((queryParams.pageNum - 1) * queryParams.pageSize, queryParams.pageNum * queryParams.pageSize)"
        row-key="id" style="width: 100%" class="custom-table">
        <el-table-column align="center" type="index" label="序号"></el-table-column>
        <el-table-column align="center" prop="meetingName" label="会议名称" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" prop="startTime" label="开始时间" show-overflow-tooltip>
          <template slot-scope="{ row }">{{ row.beginTime ? parseTime(row.beginTime) : '-' }}</template>
        </el-table-column>
        <el-table-column align="center" prop="endTime" label="结束时间" show-overflow-tooltip>
          <template slot-scope="{ row }">{{ row.finishTime ? parseTime(row.finishTime) : '-' }}</template>
        </el-table-column>
        <el-table-column align="center" label="会议地点" show-overflow-tooltip>
          <template slot-scope="{ row }">{{ row.room && row.room.roomName }}</template>
        </el-table-column>
        <el-table-column align="center" prop="deptNames" label="参会部门" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" label="会议时长" show-overflow-tooltip>
          <template slot-scope="{ row }">
            {{ row.status === 2 ? calculateTimeDifference(row.beginTime, row.finishTime) : '-' }}
          </template>
        </el-table-column>
        <el-table-column align="center" prop="createBy" label="发起人" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" prop="createTime" label="开始时间" show-overflow-tooltip>
          <template slot-scope="{ row }">{{ parseTime(row.createTime) }}</template>
        </el-table-column>
        <!-- <el-table-column align="center" prop="status" label="状态" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <div v-html="statusFormat(row)"></div>
          </template>
        </el-table-column> -->
        <el-table-column align="center" label="操作">
          <template slot-scope="{ row }">
            <div style="display: flex">
              <el-button class="table-btn primary" @click="handleDetail(row)">查看详情</el-button>
              <el-button class="table-btn primary" @click="handleEdit(row)"
                v-if="checkPermi(['meeting:edit']) && row.status != 0">补充纪要</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="custom-pagination">
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize" />
      </div>
    </div>
    <!-- 详情 -->
    <el-dialog v-dialogDragBox title="会议详情" :visible.sync="open" width="1150px" class="custom-dialog">
      <div class="infoBox">
        <el-descriptions title="会议详情" :column="3" border>
          <el-descriptions-item label="会议名称">{{ info.meetingName }}</el-descriptions-item>
          <el-descriptions-item label="会议地点" :span="2">{{ info.room && info.room.roomName }}</el-descriptions-item>
          <el-descriptions-item label="开始时间">{{ info.beginTime ? parseTime(info.beginTime) : '-'
            }}</el-descriptions-item>
          <el-descriptions-item label="结束时间">{{ info.finishTime ? parseTime(info.finishTime) : '-'
            }}</el-descriptions-item>
          <el-descriptions-item label="会议时长">{{ info.status === 2 ? calculateTimeDifference(info.beginTime,
            info.finishTime)
            : '-' }}</el-descriptions-item>
          <el-descriptions-item label="参会部门">{{ info.deptNames }}</el-descriptions-item>
        </el-descriptions>
        <template v-if="info.summary">
          <div class="infoBoxTitle">会议纪要</div>
          <div class="summaryItem" v-for="(item, index) in JSON.parse(info.summary)" :key="'summary' + index">{{ index +
            1 + '、' + item }}</div>
        </template>
        <template v-if="info.soundRecording">
          <div class="infoBoxTitle">会议录音</div>
          <div class="soundList">
            <div class="soundItem" v-for="(item, index) in info.soundRecording.split(',')" :key="'sound' + index">
              <audio :src="imgPath + item" controls></audio>
            </div>
          </div>
        </template>
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn primary" @click="open = false">关闭</el-button>
      </div>
    </el-dialog>
    <!-- 详情 -->
    <el-dialog v-dialogDragBox title="补充会议纪要" :visible.sync="editOpen" width="1150px" class="custom-dialog">
      <div class="summaryBox infoBox">
        <div class="summary_item" v-for="(item, index) in summaryData" :key="index">
          <el-input class="ipt" v-model="item.summary" placeholder="请输入会议纪要">
            <span slot="prefix" class="num"> {{ index + 1 }}、</span>
            <i slot="suffix" class="el-input__icon el-icon-delete" @click="handleDelete(item, index)"></i>
          </el-input>
        </div>
        <el-button type="text" @click="addSummary">+ 新增纪要</el-button>
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn primary" @click="editOpen = false">关闭</el-button>
        <el-button class="custom-dialog-btn primary" @click="submitForm">提交</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { getMeetingList, meetingReserveSummary } from '@/api/meeting'
import { listDept } from '@/api/system/dept'
import { checkPermi } from '@/utils/permission'

export default {
  name: 'Meeting',
  data() {
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        deptId: undefined,
        roomName: undefined,
        meetingName: undefined,
        date: undefined,
        status: 2
      },
      statusOptions: [
        { label: '未开始', value: 0 },
        { label: '进行中', value: 1, class: 'color-orange' },
        { label: '已结束', value: 2, class: 'color-red' }
      ],
      loading: true,
      list: [],
      total: 0,
      // 详情
      open: false,
      info: {},
      deptOptions: [],
      editOpen: false,
      summaryData: [],
    }
  },
  created() {
    this.getList()
    this.getApprovalsOptions()
  },
  methods: {
    checkPermi,
    // 查询部门、查询用户构造树
    async getApprovalsOptions() {
      const deptList = await listDept()
      deptList.data.forEach(el => {
        el.value = el.deptId
        el.label = el.deptName
      })
      this.deptOptions = deptList.data
    },
    // 状态格式化回显
    statusFormat(row) {
      const obj = this.statusOptions.find(item => item.value === row.status)
      return `<span class="${obj.class}">${obj.label}</span>`
    },
    // 获取会议列表
    getList() {
      this.loading = true
      getMeetingList(this.queryParams).then(res => {
        const { code, data, msg } = res
        if (code === 200) {
          this.list = data
          this.total = data.length || 0
        } else this.$message.error(msg)
        this.loading = false
      })
    },
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 重置搜索
    handleResetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 查看详情
    handleDetail(row) {
      this.info = { ...row }
      this.open = true
    },
    // 补充纪要
    handleEdit(row) {
      this.summaryData = []
      this.info = { ...row }
      this.info.summary && JSON.parse(this.info.summary).forEach(el => {
        let obj = {
          summary: '',
        }
        obj.summary = el
        this.summaryData.push(obj)
      })
      this.summaryData.push({
        summary: '',
      })
      this.editOpen = true
    },
    handleDelete(item, index) {
      this.summaryData.splice(index, 1)
    },
    addSummary() {
      this.summaryData.push({
        summary: '',
      })
    },
    submitForm() {
      let arr = []
      this.summaryData && this.summaryData.filter(item => !!item.summary).forEach(el => {
        arr.push(el.summary)
      })
      let data = {
        reserveId: this.info.id,
        summary: JSON.stringify(arr)
      }
      meetingReserveSummary(data).then(res => {
        if (res.code == 200) {
          this.$message({
            type: 'success',
            message: '保存成功'
          });
          this.handleQuery()
          this.editOpen = false
        }
      })

    },
    // 计算时间差
    calculateTimeDifference(startTime, endTime) {
      // 将时间字符串转换为 Date 对象
      const start = new Date(startTime)
      const end = new Date(endTime)
      // 计算时间差（以毫秒为单位）
      const diffInMilliseconds = end - start
      // 将毫秒转换为分钟和秒
      const minutes = Math.floor(diffInMilliseconds / 60000)
      const seconds = Math.floor((diffInMilliseconds % 60000) / 1000)
      if (minutes > 0 && seconds > 0) {
        return minutes + '分钟' + seconds + '秒'
      } else if (minutes > 0) {
        return minutes + '分钟'
      } else {
        return seconds + '秒'
      }
    },
  }
}
</script>
<style scoped lang="scss">
@import '@/assets/styles/custom-new.scss';

.custom-table {
  .el-button {
    padding: 0;
  }
}

.infoBox {
  padding: 0 20px;

  ::v-deep {
    .el-descriptions__title {
      font-weight: normal;
    }

    .el-descriptions-item__label {
      width: calc(5em + 24px);
    }
  }

  &Title {
    padding: 20px 20px 10px 0;
    font-size: 16px;
    color: #303133;
  }

  .summaryItem {
    padding: 10px 0;
    font-size: 14px;
    color: #606266;
    line-height: 20px;
    text-align: left;
    border-bottom: 1px solid #e4e7ed;

    &:last-child {
      border-bottom: none;
    }
  }

  .soundList {
    display: flex;
    flex-wrap: wrap;

    .soundItem {
      margin: 0 10px 10px 0;
    }
  }
}

.summaryBox {
  .summary_item {
    margin-bottom: 10px;

    .ipt {
      .num {
        margin-top: 10px;
        display: block;
        margin-left: 5px;
      }
    }
  }
}
</style>
