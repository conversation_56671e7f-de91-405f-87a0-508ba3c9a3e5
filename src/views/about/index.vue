<template>
  <div class="body-gary">
    <Header ref="header" :is-login="isLogin" />
    <div class="container">
      <div class="aboutUs-icon"></div>
      <div class="flex justify-between">
        <div class="aboutUs-left">
          <el-image :src="require('@/assets/global/logo.png')" class="aboutUs-left-logo"></el-image>
          <div class="aboutUs-left-title">ABOUT libero</div>
          <div class="aboutUs-left-line"></div>
        </div>
        <div class="aboutUs-right">
          <div class="aboutUs-right-title">ABOUT libero</div>
          <div class="aboutUs-right-info">Libero is dedicated to serving clients in the fastener industry, providing a one-stop service platform that integrates both supply and procurement. With a wide range of products, numerous manufacturers, direct resources, and transparent pricing. We are now open for partnerships – waiting for you to join! For photovoltaic brackets, choose Libero!</div>
        </div>
      </div>
    </div>
    <div class="aboutUs-download">
      <div class="container flex flex-column items-center justify-center height-full">
        <el-image :src="require('@/assets/global/about-app.png')" class="aboutUs-download-image"></el-image>
        <div class="aboutUs-download-btn" @click="showAppDialog">Get the App</div>
      </div>
    </div>
    <div class="aboutUs-branch">
      <div class="container">
        <div class="aboutUs-branch-title">
          <span>Dedicated Service Personnel</span>
          <b>Countries Covered</b>
          <i></i>
        </div>
        <div class="aboutUs-branch-content">
          <div class="service-timeline">
            <div class="timeline-container">
              <div v-for="(center, index) in serviceCenters" :key="index" class="timeline-item" :class="{ active: activeItem === index }" @mouseenter="setActiveItem(index)" @mouseleave="setActiveItem(-1)">
                <div class="timeline-country">{{ center.country }}</div>
                <div class="timeline-dot"></div>
                <div class="timeline-description">{{ center.description }}</div>
                <!-- 悬停显示的详细信息卡片 -->
                <div v-if="activeItem === index" class="service-card">
                  <el-image :src="center.image" class="service-card-image"></el-image>
                  <div class="service-card-content">
                    <div class="service-card-phone">Tel: {{ center.phone }}</div>
                    <div class="service-card-hours">{{ center.serviceHours }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- APP下载二维码弹窗 -->
    <el-dialog title="扫码下载APP" :visible.sync="appDialogVisible" width="400px" center class="app-download-dialog">
      <div class="app-download-content">
        <el-image :src="appQrCodeUrl" class="app-qrcode-image"></el-image>
        <div class="app-download-tips">
          <p>使用手机扫描上方二维码</p>
          <p>即可下载Libero APP</p>
        </div>
      </div>
    </el-dialog>
    <Footer ref="footer" />
  </div>
</template>

<script>
import Header from '@/views/components/header'
import Footer from '@/views/components/footer'
import { getToken } from '@/utils/auth'

export default {
  name: 'About',
  components: { Header, Footer },
  data() {
    return {
      isLogin: !!getToken(),
      activeItem: -1,
      // APP下载弹窗控制
      appDialogVisible: false,
      appQrCodeUrl: require('@/assets/global/qrcode.png'),
      serviceCenters: [
        {
          country: 'South Korea',
          description: 'South Korea Official Service Center',
          image: require('@/assets/global/service-et.png'),
          phone: '18999999999',
          serviceHours: 'Service Hours: 9:00 AM - 6:30 PM'
        },
        {
          country: 'Vietnam',
          description: 'Vietnam Official Service Center',
          image: require('@/assets/global/service-et.png'),
          phone: '18888888888',
          serviceHours: 'Service Hours: 9:00 AM - 6:30 PM'
        },
        {
          country: 'Kazakhstan',
          description: 'Kazakhstan Official Service Center',
          image: require('@/assets/global/service-et.png'),
          phone: '18777777777',
          serviceHours: 'Service Hours: 9:00 AM - 6:30 PM'
        },
        {
          country: 'Uzbekistan',
          description: 'Uzbekistan Official Service Center',
          image: require('@/assets/global/service-et.png'),
          phone: '18666666666',
          serviceHours: 'Service Hours: 9:00 AM - 6:30 PM'
        },
        {
          country: 'Kenya',
          description: 'Kenya Official Service Center',
          image: require('@/assets/global/service-et.png'),
          phone: '18555555555',
          serviceHours: 'Service Hours: 9:00 AM - 6:30 PM'
        },
        {
          country: 'Ethiopia',
          description: 'Ethiopia Official Service Center',
          image: require('@/assets/global/service-et.png'),
          phone: '18444444444',
          serviceHours: 'Service Hours: 9:00 AM - 6:30 PM'
        },
        {
          country: 'Saudi Arabia',
          description: 'Saudi Arabia Official Service Center',
          image: require('@/assets/global/service-et.png'),
          phone: '18999999999',
          serviceHours: 'Service Hours: 9:00 AM - 6:30 PM'
        }
      ]
    }
  },
  methods: {
    setActiveItem(index) {
      this.activeItem = index
    },
    showAppDialog() {
      this.appDialogVisible = true
    }
  }
}
</script>
<style lang="scss" scoped>
@import '@/assets/global/custom.scss';
@import '@/assets/global/public.scss';
.aboutUs {
  &-icon {
    width: 100%;
    height: 168px;
    background: url('~@/assets/global/about-icon.png') no-repeat center center;
    background-size: 1100px 100%;
    margin: 100px 0;
  }
  &-left {
    display: flex;
    flex-direction: column;
    &-logo {
      width: 52px;
      height: 50px;
    }
    &-title {
      font-weight: 400;
      font-size: 24px;
      color: #2d7cff;
      line-height: 26px;
      margin-top: 67px;
    }
    &-line {
      width: 312px;
      height: 1px;
      background: #c9c9c9;
      margin-top: 40px;
    }
  }
  &-right {
    width: 560px;
    display: flex;
    flex-direction: column;
    &-title {
      font-weight: 500;
      font-size: 48px;
      color: #333333;
      line-height: 72px;
    }
    &-info {
      font-weight: 400;
      font-size: 14px;
      color: #333333;
      line-height: 26px;
    }
  }
  &-download {
    width: 100%;
    height: 1298px;
    background: linear-gradient(180deg, rgba(20, 115, 233, 0) 0%, #2e73f3 100%);
    background-image: url('~@/assets/global/about-down.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center top;
    margin-top: 100px;
    &-image {
      width: 663px;
      height: 848px;
    }
    &-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 368px;
      height: 86px;
      cursor: pointer;
      background: #ffffff;
      box-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 0.25);
      border-radius: 60px;
      font-weight: 500;
      font-size: 24px;
      color: #2e73f3;
      margin-top: 46px;
      transition: all 0.3s;
      &:hover {
        background: #2e73f3;
        color: #ffffff;
      }
    }
  }
  &-branch {
    width: 100%;
    background: url('~@/assets/global/about-bg.png') no-repeat center top #f5f5f5;
    background-size: 100% auto;
    padding-top: 100px;
    padding-bottom: 100px;
    min-height: 1050px;
    &-title {
      display: flex;
      flex-direction: column;
      span {
        font-weight: 400;
        font-size: 24px;
        color: #2d7cff;
        line-height: 26px;
        text-transform: uppercase;
      }
      b {
        font-weight: 500;
        font-size: 48px;
        color: #333333;
        line-height: 72px;
      }
      i {
        display: inline-block;
        width: 190px;
        height: 3px;
        background-color: #2e73f3;
        margin-top: 7px;
      }
    }
    &-content {
      margin-top: 100px;
      .service-timeline {
        position: relative;
        .timeline-container {
          display: flex;
          flex-wrap: wrap;
          justify-content: space-between;
          align-items: flex-start;
          position: relative;
          gap: 0;
          &::before {
            content: '';
            position: absolute;
            left: 0;
            right: 0;
            top: 95px;
            height: 2px;
            background: #969696;
          }
          &::after {
            content: '';
            position: absolute;
            left: 0;
            right: 0;
            top: calc(268px + 95px);
            height: 2px;
            background: #969696;
          }
        }
        .timeline-item {
          position: relative;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: flex-start;
          cursor: pointer;
          transition: all 0.3s ease;
          width: calc(25% - 10px);
          margin-bottom: 100px;
          &:nth-child(n + 5) {
            width: calc(33.333% - 10px);
            z-index: 2;
          }
          .timeline-country {
            font-weight: 600;
            font-size: 36px;
            color: #333333;
            line-height: 48px;
            text-align: center;
            white-space: nowrap;
            margin-bottom: 12px;
          }
          .timeline-dot {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 70px;
            position: relative;
            &::before {
              display: inline-block;
              content: '';
              width: 12px;
              height: 12px;
              border-radius: 50%;
              background: #333333;
              border: 2px solid #ffffff;
              transition: all 0.3s ease;
            }
          }
          .timeline-description {
            font-weight: 400;
            font-size: 12px;
            color: #666666;
            height: 30px;
            line-height: 30px;
            text-align: center;
            max-width: 240px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            margin-top: 8px;
          }
          &.active,
          &:hover {
            .timeline-dot {
              &::before {
                width: 20px;
                height: 20px;
                background: #125ad3;
                box-shadow: 0px 1px 14px 0px rgba(27, 79, 177, 0.39);
              }
            }
            .timeline-country {
              color: #2e73f3;
            }
          }
          .service-card {
            position: absolute;
            top: calc(100% + 10px);
            left: 50%;
            transform: translateX(-50%);
            width: 280px;
            background: #ffffff;
            border-radius: 12px;
            border: 1px solid #2e73f3;
            box-shadow: 0px 8px 32px 0px rgba(0, 0, 0, 0.12);
            padding: 15px;
            z-index: 1000;
            animation: fadeInUp 0.3s ease;
            .service-card-image {
              width: 100%;
              height: 140px;
              object-fit: cover;
              border-radius: 8px;
              margin-bottom: 16px;
            }
            .service-card-content {
              .service-card-phone {
                font-weight: 600;
                font-size: 16px;
                color: #2e73f3;
                line-height: 22px;
                margin-bottom: 8px;
              }
              .service-card-hours {
                font-weight: 400;
                font-size: 14px;
                color: #666666;
                line-height: 20px;
              }
            }
          }
        }
      }
    }
  }
}
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px) translateX(-50%);
  }
  to {
    opacity: 1;
    transform: translateY(0) translateX(-50%);
  }
}
// APP下载弹窗样式
::v-deep .app-download-dialog {
  .el-dialog__header {
    text-align: center;
    padding: 20px 20px 10px;
    .el-dialog__title {
      font-size: 18px;
      font-weight: 500;
      color: #333333;
    }
  }
  .el-dialog__body {
    padding: 10px 20px 30px;
  }
}
.app-download-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  .app-qrcode-image {
    width: 200px;
    height: 200px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
  }
  .app-download-tips {
    text-align: center;
    p {
      margin: 5px 0;
      font-size: 14px;
      color: #666666;
      line-height: 22px;
      &:first-child {
        font-weight: 500;
        color: #333333;
      }
    }
  }
}
</style>
