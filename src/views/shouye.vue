<template>
  <div class="webBox" @click="clickHide">
    <head-tpl :is-login="isLogin" :collect-list="collectList"></head-tpl>
    <div class="webMain">
      <div class="webContainer">
        <div class="webMain-header" :class="{ 'flex-end': isFixed }">
          <div class="webMain-left" :class="{ 'left-fixed': isFixed }" :style="{ height: leftHeight, maxHeight: '100%' }" id="webMainLeft">
            <div class="webMain-left-ad" v-for="item in asideList" :key="'leftad' + item.id" @click="handleClickAd(item)">
              <el-image :src="foramtAdImg(item)"></el-image>
            </div>
          </div>
          <div class="webMain-right" ref="mainRight">
            <el-collapse-transition>
              <div class="webMain-right-kline" v-if="showline">
                <div class="webMain-right-kline-title">
                  <div class="kline-title-item">
                    <span :class="{ active: klineIndex === 0 }" @click="handleClickLine(0)">现货</span>
                    <span :class="{ active: klineIndex === 1 }" @click="handleClickLine(1)">期货</span>
                  </div>
                  <div class="kline-title-close" @click="handleCloseLine"><i class="el-icon-error"></i></div>
                </div>
                <div class="webMain-right-kline-box">
                  <div class="kline-chart" @click="toNews" v-loading="chartLoading">
                    <k-line-charts :chart-data="chartData" :is-zoom="false" is-index height="270px" v-if="klineIndex === 0 && showChart" />
                    <k-line-chartn :chart-data="chartData" :is-zoom="false" is-index height="270px" v-if="klineIndex === 1 && showChart" />
                  </div>
                  <el-scrollbar wrap-class="kline-scrollbar">
                    <div class="kline-box" v-if="klineIndex === 0">
                      <div class="kline-box-item" v-for="item in chartData.seriesData" :key="item.id" @click="changeKline(item)">
                        <i class="item-checkbox checked" v-if="item.show"></i>
                        <i class="item-checkbox" v-if="!item.show"></i>
                        <span>{{ item.name }}</span>
                      </div>
                    </div>
                    <div class="kline-box" v-if="klineIndex === 1">
                      <div class="kline-box-item" v-for="(item, index) in blackOptions" :key="index" @click="changeKline(item)">
                        <i class="el-icon-success" v-if="blackActive === item"></i>
                        <i class="circle" v-else></i>
                        <span>{{ item }}</span>
                      </div>
                    </div>
                  </el-scrollbar>
                </div>
              </div>
            </el-collapse-transition>
            <div class="productNav">
              <div class="productNav-item flex1">
                <el-tabs class="productNav-item-tabs flex1" v-model="navId" @tab-click="tabSecondNavList">
                  <el-tab-pane :label="item.name" :name="String(item.id)" v-for="item in navList" :key="item.id"></el-tab-pane>
                </el-tabs>
                <div class="productNav-item-add flex-shrink" @click="handleAddProduct(0)" v-if="isLogin">
                  <i class="el-icon-plus"></i>
                  我要定制
                </div>
                <div class="productNav-item-add flex-shrink" @click="handleDrag" v-if="isLogin">
                  <i class="el-icon-setting"></i>
                  编辑
                </div>
              </div>
              <!-- <div class="productNav-vip flex-shrink" @click="handleBrand">
                <img src="../../public/imgs/category-open.png" alt="认证采购商" />
                <span>我要卖货</span>
              </div> -->
            </div>
            <div class="productSecondNav" ref="productSecondNav">
              <template v-if="isShowBrand">
                <div class="brand-list" v-if="brandList.length">
                  <div class="brand-list-title">认证采购商</div>
                  <div class="brand-list-item" :class="{ active: !!brandId && brandId === item.id }" v-for="item in brandList" :key="item.id" @click="handleBrandCategory(item)">
                    {{ item.name }}
                  </div>
                </div>
                <div class="brand-none" v-else>
                  暂无认证采购商,我要去
                  <el-button type="text" @click="handleUp">升级</el-button>
                </div>
              </template>
              <template v-if="!isShowBrand && secondNavList.length">
                <div class="levelBox" v-for="item in secondNavList" :key="item.id">
                  <div class="levelBox-name" :class="{ active: item.id === levelBoxId }" @click="getProductList(item, 1)">
                    {{ item.name }}
                  </div>
                </div>
                <div class="levelBox" v-if="!isShowBrand && isLogin">
                  <div class="levelBox-name" @click="handleAddProduct(0)">
                    <i class="el-icon-plus">我要定制</i>
                  </div>
                </div>
              </template>
            </div>
            <div class="navProductList diy" @click="clickHideNav">
              <el-row :gutter="20">
                <template v-if="isShowBrand">
                  <template v-if="brandProduct.length">
                    <el-col :lg="70" :md="3" :sm="4" :xs="6" v-for="item in brandProduct" :key="'new' + item.id">
                      <div class="searchBox-item" @click.stop="navProductId = item.id">
                        <el-image :src="formatProductImg(item)" lazy class="searchBox-item-img"></el-image>
                        <b class="searchBox-item-name">{{ item.productName }}</b>
                        <span class="searchBox-item-spec">{{ item.specs }}</span>
                        <template v-if="item.id === navProductId">
                          <item-tpl :desc-info="item" :view-num="12" :collect-list="collectList" isShowBrand @clickHide="clickHide" />
                        </template>
                      </div>
                    </el-col>
                  </template>
                  <el-empty :description="!productLoading && !brandTotal ? '暂无数据' : '加载中…'" v-else />
                </template>
                <template v-if="!isShowBrand && !hasChildren">
                  <template v-if="navProductList.length">
                    <el-col :lg="70" :md="3" :sm="4" :xs="6" v-for="item in navProductList" :key="'new' + item.id">
                      <div class="searchBox-item" @click.stop="navProductId = item.id">
                        <el-badge is-dot :hidden="!item.hasDot">
                          <el-image :src="formatProductImg(item)" lazy class="searchBox-item-img"></el-image>
                        </el-badge>
                        <b class="searchBox-item-name">{{ item.productName }}</b>
                        <span class="searchBox-item-spec">{{ item.specs }}</span>
                        <template v-if="item.id === navProductId">
                          <item-tpl :desc-info="item" :view-num="12" :collect-list="collectList" @clickHide="clickHide" />
                        </template>
                      </div>
                    </el-col>
                  </template>
                  <el-empty :description="!productLoading && !navTotal ? '暂无数据' : '加载中…'" v-else />
                </template>
                <template v-if="!isShowBrand && hasChildren">
                  <template v-if="navProductList.length">
                    <el-col :lg="70" :md="3" :sm="4" :xs="6" v-for="(item, index) in navProductList" :key="'new' + item.id">
                      <div class="searchBox-item" @click.stop="handleNavProduct(item, $event, index)">
                        <el-badge :value="item.dot" :max="99" :hidden="!item.dot">
                          <el-image :src="item.oss_image || imgPath + item.image" lazy class="searchBox-item-img"></el-image>
                        </el-badge>
                        <b class="searchBox-item-name">{{ item.name }}</b>
                        <span class="searchBox-item-spec">{{ item.model }}</span>
                        <template v-if="item.id === navProductId">
                          <contrast-tpl :desc-info="item" :view-num="12" :collect-list="collectList" :leftX="leftX" :xcoord="xcoord" @clickHide="clickHide" />
                        </template>
                      </div>
                    </el-col>
                  </template>
                  <el-empty :description="!productLoading && !navTotal ? '暂无数据' : '加载中…'" v-else />
                </template>
                <el-col :lg="70" :md="3" :sm="4" :xs="6" v-if="!isShowBrand && !!navTotal && !productLoading">
                  <div class="searchBox-item" @click="handleAddProduct(2)" v-if="isLogin">
                    <div class="searchBox-item-icon"><i class="el-icon-plus" style="font-size: 22px"></i></div>
                    <b class="searchBox-item-name">我要定制</b>
                    <span class="searchBox-item-spec">点击添加</span>
                  </div>
                </el-col>
                <template v-if="isShowBrand">
                  <el-col :lg="70" :md="3" :sm="4" :xs="6" v-if="brandParams.pageNum * brandParams.pageSize < brandTotal">
                    <div class="searchBox-item" @click="handleMoreNavProduct">
                      <div class="searchBox-item-icon"><i class="el-icon-arrow-down" style="font-size: 30px"></i></div>
                      <b class="searchBox-item-name">{{ productLoading ? '加载中…' : '加载更多' }}</b>
                    </div>
                  </el-col>
                </template>
                <template v-if="!isShowBrand && !hasChildren && !!navTotal">
                  <el-col :lg="70" :md="3" :sm="4" :xs="6" v-if="navParams.pageNum * navParams.pageSize < navTotal">
                    <div class="searchBox-item" @click="handleMoreNavProduct">
                      <div class="searchBox-item-icon"><i class="el-icon-arrow-down" style="font-size: 30px"></i></div>
                      <b class="searchBox-item-name">{{ productLoading ? '加载中…' : '加载更多' }}</b>
                    </div>
                  </el-col>
                </template>
              </el-row>
            </div>
          </div>
        </div>
        <div class="webMain-promotional-product" v-if="!!promotionList.length">
          <div class="promotional-product-title">
            <div class="title-info">
              <b>促销产品</b>
              <span>hot</span>
            </div>
            <div class="title-more" @click="handlePromotion">查看更多 ></div>
          </div>
          <div class="promotional-product-list">
            <div class="promotional-product-item" v-for="item in promotionList" :key="item.id" @click="handleView(item)">
              <div class="item-image-box">
                <div class="tip">hot</div>
                <el-image :src="formatProductImg(item)" class="image" lazy></el-image>
                <div class="num" v-if="item.promotions.length">
                  <span>共</span>
                  <b>{{ item.promotions.length }}</b>
                  <span>家供应商促销</span>
                </div>
              </div>
              <div class="padding-10">
                <div class="item-title">{{ item.productName }}</div>
                <div class="item-brand">规格：{{ item.specs }}</div>
                <div class="item-num">供应数量：{{ getPromotion(item, 'num') }}</div>
                <div class="item-price">
                  <span>￥</span>
                  <b>{{ getPromotion(item, 'price') }}</b>
                  <span>/{{ getPromotion(item) }}</span>
                </div>
                <div class="item-original">
                  <div class="item-original-tip">最低促销价</div>
                </div>
                <div class="item-button" @click.stop="handleBuy(item)">我要采购</div>
              </div>
            </div>
          </div>
        </div>
        <div class="webMain-newProduct">
          <div class="webMain-newProduct-title">
            <span>新增产品</span>
            <img src="../../public/imgs/new.png" />
          </div>
          <el-row :gutter="20" class="webMain-newProduct-list diy">
            <el-col :lg="90" :md="3" :sm="4" :xs="6" v-for="item in newProductList" :key="'new' + item.id">
              <div class="searchBox-item" @click.stop="productId = item.id">
                <el-image :src="formatProductImg(item)" lazy class="searchBox-item-img"></el-image>
                <b class="searchBox-item-name">{{ item.productName }}</b>
                <span class="searchBox-item-spec">{{ item.specs }}</span>
                <template v-if="item.id === productId">
                  <item-tpl :desc-info="item" :view-num="12" :collect-list="collectList" @clickHide="clickHide" />
                </template>
              </div>
            </el-col>
          </el-row>
        </div>
        <div class="webMain-left-product">
          <div class="webMain-left-product-title">热门产品</div>
          <div class="webMain-left-product-list">
            <template v-if="productHotList.length">
              <span v-for="(item, index) in productHotList" :key="'hot' + index" @click="handleView(item)">{{ item.productName }}</span>
            </template>
            <template v-else>暂无热门产品</template>
          </div>
        </div>
        <div class="webMain-adBox">
          <div class="webMain-adBox-item" v-for="item in mainAdList" :key="'mainad' + item.id">
            <a :href="item.url" target="_blank">
              <el-image :src="foramtAdImg(item)" lazy></el-image>
            </a>
          </div>
        </div>
      </div>
    </div>
    <foot-tpl></foot-tpl>

    <!-- 新增产品/类目 -->
    <index-add ref="indexAdd"></index-add>

    <!-- 产品详情 -->
    <product-dialog ref="productInfo" append-body @callback="showProduct = false" v-if="showProduct"></product-dialog>

    <!-- 广告弹框 -->
    <el-dialog v-dialogDragBox title="" :visible.sync="adOpen" width="50%" center>
      <el-image v-for="(item, index) in adImg" :key="index" :src="item" lazy style="width: 100%"></el-image>
    </el-dialog>

    <!--  我要定制  -->
    <customiZation ref="customiZation" :addsource="'outer'"></customiZation>
    <!--  分类排序  -->
    <el-dialog v-dialogDragBox title="提示" :visible.sync="dragOpen" width="1150px" class="custom-dialog">
      <div class="navSetting-title">当前排序</div>
      <div class="navSetting-info">
        <span v-for="item in navList" :key="item.id">{{ item.name }}</span>
      </div>
      <div class="navSetting-list">
        <div class="navSetting-list-title">调整排序为</div>
        <div class="navSetting-list-box">
          <draggable v-model="dragList" draggable=".navSetting-list-item" forceFallback="true" chosenClass="chosenClass" dragClass="dragClass">
            <div class="navSetting-list-item" v-for="item in dragList" :key="item.id">
              <span>{{ item.name }}</span>
              <i></i>
            </div>
          </draggable>
        </div>
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn" @click="dragOpen = false">取消</el-button>
        <el-button class="custom-dialog-btn primary" @click="handleDragSubmit">确定</el-button>
      </div>
    </el-dialog>
    <!--  绑定手机  -->
    <binding-dialog ref="bindingInfo" append-body></binding-dialog>
  </div>
</template>
<script>
import draggable from 'vuedraggable'
import { getToken, removeToken } from '@/utils/auth'
import { getlist } from '@/api/houtai/shoucang'
import { getDownAd, getHotProduct, getLeftAd, getNewProducts, getTypeProducts } from '@/api/system/product'
import * as category from '@/api/purchase/category'
import headTpl from '@/views/public/components/head'
import footTpl from '@/views/public/components/foot'
import KLineChartn from '@/views/dashboard/KLineChartn'
import KLineCharts from '@/views/dashboard/KLineCharts'
import axios from 'axios'
import ItemTpl from '@/views/public/product/item'
import contrastTpl from '@/views/public/product/contrast'
import ProductDialog from '@/views/public/product/dialog'
import indexAdd from './shouyeAdd'
import { getHomeKline, getMetalMarket } from '@/api/kline'
import { homeBrandList, homePurchaseList } from '@/api/shouye'
import { gygs } from '@/api/houtai/formula'
import { getPromotionIndexList } from '@/api/promotion'
import customiZation from '@/views/customization/create'
import { updateSort } from '@/api/system/user'
import BindingDialog from '@/views/public/binding/index'

export default {
  components: { BindingDialog, contrastTpl, ItemTpl, KLineChartn, KLineCharts, headTpl, footTpl, indexAdd, ProductDialog, customiZation, draggable },
  data() {
    return {
      // 选中的产品
      productId: undefined,
      navProductId: undefined,
      // 是否登录
      isLogin: false,
      // 收藏夹列表
      collectList: [],
      // 左侧广告
      asideList: [],
      // 热门产品
      productHotList: [],
      // K线数据
      klineList: [],
      timelist: [],
      chartData: { xAxis: [], min: 0, max: 0, color: '', seriesData: [] },
      klineIndex: 0,
      showline: sessionStorage.getItem('klineShow') === 'false' ? false : true,
      // 新增产品
      newProductList: [],
      // 中部广告
      mainAdList: [],
      // 分类
      navList: [],
      navId: undefined,
      secondNavList: [],
      navProductList: [],
      levelBoxId: undefined,
      levelSonBoxId: undefined,
      levelGrandsonBoxId: undefined,
      navParams: {
        pageNum: 1,
        pageSize: 49,
        categoryId: undefined
      },
      navTotal: 0,
      // 是否展开VIP
      isShowBrand: false,
      brandList: [],
      brandId: undefined,
      brandCategoryId: undefined,
      brandProduct: [],
      brandParams: {
        pageNum: 1,
        pageSize: 49,
        name: undefined,
        brandId: undefined
      },
      brandTotal: 0,
      hasChildren: false,
      leftX: 0,
      xcoord: 0,
      blackActive: undefined,
      blackOptions: ['螺纹钢', '热轧卷板', '不锈钢', '焦炭', '铁矿石', '硅铁', '锰硅', '线材'],
      showChart: true,
      chartLoading: true,
      adOpen: false,
      adImg: [],
      isFixed: false,
      hasMore: false,
      promotionList: [],
      leftHeight: '100%',
      productLoading: true,
      dragOpen: false,
      dragList: [],
      showProduct: false
    }
  },
  computed: {
    userType() {
      return this.$store.getters.userType || '00'
    }
  },
  created() {
    // 获取地址栏参数
    const query = this.$route.query
    if (Object.prototype.hasOwnProperty.call(query, 'type')) {
      if (query.type === 'remove') {
        removeToken()
        this.isLogin = false
      } else this.isLogin = !!getToken()
    } else this.isLogin = !!getToken()
    if (this.isLogin) {
      this.getCollectList()
    }
    this.getAsideList()
    this.getProductHotList()
    this.getKlineList()
    this.getNavList()
    this.getNewProductList()
    this.getMainAdList()
    this.getPromotionList()
  },
  mounted() {
    window.addEventListener('scroll', this.handleScroll)
  },
  beforeDestroy() {
    window.removeEventListener('scroll', this.handleScroll)
  },
  methods: {
    handleScroll() {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop
      const offsetTop = document.querySelector('#webMainLeft').offsetTop
      this.isFixed = scrollTop > offsetTop + (this.asideList.length - 1) * 200 && this.hasMore
      const rightHeight = this.$refs.mainRight.offsetHeight
      if (this.hasMore) this.leftHeight = rightHeight - scrollTop > 0 ? rightHeight - scrollTop + 162 + 'px' : '0px'
    },
    // 查询收藏夹列表
    getCollectList() {
      getlist({ type: 'UserProduct' }).then(res => {
        const { code, msg, data } = res
        if (code === 200) this.collectList = data
        else this.$message.error(msg)
      })
    },
    // 左侧广告列表
    getAsideList() {
      getLeftAd().then(res => {
        const { code, msg, data } = res
        if (code === 200) this.asideList = data
        else this.$message.error(msg)
      })
    },
    foramtAdImg(item) {
      const img = item.photoAddress_oss ? item.photoAddress_oss.split(',') : item.photoAddress ? item.photoAddress.split(',').map(item => this.imgPath + item) : []
      return img[0]
    },
    // 热门产品
    getProductHotList() {
      getHotProduct().then(res => {
        const { code, msg, data } = res
        if (code === 200) this.productHotList = data
        else this.$message.error(msg)
      })
    },
    // 获取k线
    getKlineList() {
      getHomeKline({ limit: 180 }).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          let xAxisData = []
          let seriesData = []
          let xAxis = []
          let min = 0
          let max = 0
          const colorArr = ['#37a2ff', '#00ddff', '#80ffa5', '#ff0087', '#ffbf00']
          let showNum = 0
          data.map((item, idx) => {
            showNum += idx
            if (item.types) {
              item.color = colorArr[idx]
              item.types.map(ite => {
                if (ite.id === 7) {
                  // item.color = ite.color
                  item.data = ite.steelPrices
                  xAxisData = [...xAxisData, ...ite.steelPrices]
                  xAxis = Array.from(new Set(xAxisData.map(item => item.date))) || []
                }
              })
              const price = xAxisData.map(item => item.price)
              min = Math.min(...price)
              max = Math.max(...price)
              if (item.data.length !== xAxis.length) {
                let data = []
                for (let i = 0; i < xAxis.length; i++) {
                  data[i] = min
                  item.data.map(ite => {
                    if (ite.date === xAxis[i]) data[i] = ite.price
                  })
                }
                seriesData.push({ id: item.id, name: item.name, color: item.color, data, show: showNum === 0 })
              } else {
                seriesData.push({
                  id: item.id,
                  name: item.name,
                  color: item.color,
                  data: item.data.map(item => item.price),
                  show: showNum === 0
                })
              }
            }
          })
          this.chartData.min = min - 100
          this.chartData.max = max + 100
          this.chartData.xAxis = xAxis
          this.chartData.seriesData = seriesData
          this.showChart = !!seriesData.length
          this.chartLoading = false
        } else this.$message.error(msg)
      })
    },
    // 改变K线分类
    changeKline(row) {
      if (this.klineIndex === 0) row.show = !row.show
      else {
        this.blackActive = row
        this.getBlackList(row)
      }
    },
    handleClickLine(val) {
      this.showChart = false
      this.chartLoading = true
      this.klineIndex = val
      if (!!val) this.getBlackList()
      else this.getKlineList()
    },
    getBlackList(name = undefined) {
      const chnName = name || this.blackOptions[0]
      this.blackActive = chnName
      getMetalMarket({ chnName, limit: 730 }).then(res => {
        const { code, data, msg } = res
        if (code === 200) {
          const newData = data.length > 730 ? data.slice(-730) : data
          this.chartData.xAxis = newData.map(item => item.dateTime)
          this.chartData.color = '#74a3e6'
          const dataValue = newData.map(item => parseFloat(item.varietyValue || 0))
          this.chartData.min = Math.min(...dataValue) - 100
          this.chartData.max = Math.max(...dataValue) + 100
          this.chartData.seriesData = [{ name: chnName, color: '#74a3e6', data: dataValue }]
          this.showChart = true
          this.chartLoading = false
        } else this.$message.error(msg)
      })
    },
    // 查询中部广告
    getMainAdList() {
      getDownAd().then(res => {
        const { code, msg, data } = res
        if (code === 200) this.mainAdList = data
        else this.$message.error(msg)
      })
    },
    // 新增产品
    getNewProductList() {
      getNewProducts().then(res => {
        const { code, msg, data } = res
        if (code === 200) this.newProductList = data
        else this.$message.error(msg)
      })
    },
    // 点击搜索结果产品以外区域隐藏产品弹框
    clickHide(event) {
      this.productId = undefined
      this.navProductId = undefined
      this.levelSonBoxId = undefined
      this.levelGrandsonBoxId = undefined
    },
    // 点击其他地方隐藏分类
    clickHideNav() {
      // this.levelBoxId = this.levelSonBoxId = this.levelGrandsonBoxId = undefined
    },
    // 去升级
    handleUp() {
      if (!!getToken() && (this.userType !== '00' && this.userType !== '05')) {
        this.$refs.bindingInfo.handleOpen()
        return
      }
      this.$router.push('/index')
    },
    // 查询分类
    getNavList() {
      category.getlist().then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          this.navList = data
          if (data.length) {
            this.getSecondNavList(data[0])
          }
        } else this.$message.error(msg)
      })
    },
    tabSecondNavList(e) {
      const row = this.navList.find(item => item.id == e.name)
      this.getSecondNavList(row)
    },
    // 查询二级分类
    getSecondNavList(row) {
      this.navId = String(row.id)
      this.brandPrentId = row.id
      this.isShowBrand = false
      this.brandProduct = []
      this.brandId = undefined
      const data = this.navList.find(item => item.id === row.id)
      this.secondNavList = data['children'] || []
      this.levelBoxId = this.levelSonBoxId = this.levelGrandsonBoxId = undefined
      this.getProductList(row)
    },
    // 树结构转扁平数组
    treeToArr(data, children = 'children', parentId, res = []) {
      data.forEach(v => {
        v.parentId = parentId
        res.push(v)
        if (v[children] && v[children].length) this.treeToArr(v[children], children, v.id, res)
      })
      return res
    },
    // 查询产品
    async getProductList(row, val) {
      this.productLoading = true
      this.navTotal = 0
      this.hasMore = false
      this.leftHeight = 'auto'
      this.brandParams.pageNum = 1
      this.navParams.pageNum = 1
      this.hasChildren = true
      const Id = row.id
      this.levelBoxId = Id
      const findData = this.treeToArr(this.secondNavList).find(item => item.id === Id) || {}
      const hasChildren = findData.children && findData.children.length
      this.hasChildren = hasChildren
      this.navProductList = []
      if (this.isShowBrand) {
        this.brandParams.categoryId = row.id
        this.brandParams.pageSize = 49
        // this.handleBrandProduct()
      } else {
        if (hasChildren) {
          this.navParams.categoryId = row.id
          this.navParams.pageSize = 49
          let navData = []
          findData.children.forEach(item => {
            navData.push(item.id)
          })
          if (findData.children && this.isLogin) {
            await Promise.all(
              findData.children.map(async item => {
                item.dot = 0
                const product = await getTypeProducts({ categoryId: item.id })
                if (product.code === 200) {
                  if (product.rows.length) {
                    await Promise.all(
                      product.rows.map(async itt => {
                        const offer = await gygs({ productId: itt.id })
                        if (offer.data.quotes.length) item.dot += offer.data.quotes.length
                      })
                    )
                  }
                } else this.$message.error(product.msg)
              })
            )
          }
          this.navProductList = findData.children
          this.productLoading = false
          this.navTotal = findData.children.length
        } else {
          this.navParams.categoryId = row.id
          this.navParams.pageSize = 49
          getTypeProducts(this.navParams).then(async res => {
            const { code, msg, rows, total } = res
            if (code === 200) {
              if (rows.length) {
                await Promise.all(
                  rows.map(async item => {
                    item.hasDot = false
                    if (this.isLogin) {
                      const offer = await gygs({ productId: item.id })
                      if (offer.data.quotes.length) item.hasDot = true
                    }
                  })
                )
              }
              this.navProductList = rows
              this.productLoading = false
              this.navTotal = total
            } else this.$message.error(msg)
          })
        }
      }
    },
    handleNavProduct(item, event, index) {
      const categoryId = item.id
      getTypeProducts({ categoryId }).then(res => {
        const { code, msg, rows } = res
        if (code === 200) {
          if (rows.length) {
            if (!this.navProductId || this.navProductId !== item.id) {
              this.navProductId = item.id
              this.xcoord = event.clientX
              this.leftX = index
            }
          } else this.$message.warning('暂无数据')
        } else this.$message.error(msg)
      })
    },
    // 加载更多
    handleMoreNavProduct() {
      this.productLoading = true
      if (this.isShowBrand) {
        this.brandParams.pageNum += 1
        // this.handleBrandProduct()
      } else {
        this.navParams.pageNum += 1
        getTypeProducts(this.navParams).then(async res => {
          const { code, msg, rows, total } = res
          if (code === 200) {
            if (rows.length) {
              await Promise.all(
                rows.map(async item => {
                  item.hasDot = false
                  if (this.isLogin) {
                    const offer = await gygs({ productId: item.id })
                    if (offer.data.quotes.length) item.hasDot = true
                  }
                })
              )
            }
            this.navProductList = this.navProductList.concat(rows)
            this.hasMore = true
            this.leftHeight = '100%'
            this.productLoading = false
            this.navTotal = total
          } else this.$message.error(msg)
        })
      }
    },
    // 点击展开/收起VIP
    handleBrand() {
      homeBrandList().then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          this.hasMore = false
          this.leftHeight = 'auto'
          this.isShowBrand = true
          this.brandList = data
          this.secondNavList = []
          this.brandProduct = []
          this.brandId = undefined
          this.navId = undefined
          homePurchaseList(this.brandParams).then(res => {
            const { code, msg, rows, total } = res
            if (code === 200) {
              rows.forEach(item => {
                item.buyingLeadId = item.id || ''
                item.unit = item.unit || item.product.unit || ''
                item.product = item.product || {}
                Object.assign(item, item.product, { unit: item.unit })
              })
              this.brandProduct = rows
              this.brandTotal = total
              this.productLoading = false
            } else this.$message.error(msg)
          })
        } else this.$message.error(msg)
      })
    },
    // 点击品牌查询分类
    handleBrandCategory(item) {
      this.brandId = item.id
      this.brandParams.name = item.name
      this.brandParams.brandId = item.id
      homePurchaseList(this.brandParams).then(res => {
        const { code, msg, rows, total } = res
        if (code === 200) {
          rows.forEach(item => {
            item.buyingLeadId = item.id || ''
            item.unit = item.unit || item.product.unit || ''
            item.product = item.product || {}
            Object.assign(item, item.product, { unit: item.unit })
          })
          this.brandProduct = rows
          this.brandTotal = total
        } else this.$message.error(msg)
      })
    },
    toNews() {
      if (!!getToken() && (this.userType !== '00' && this.userType !== '05')) {
        this.$refs.bindingInfo.handleOpen()
        return
      }
      this.$router.push({ name: 'news' })
    },
    // 新增产品/类目
    handleAddProduct(val) {
      if (!!getToken() && (this.userType !== '00' && this.userType !== '05')) {
        this.$refs.bindingInfo.handleOpen()
        return
      }
      let data = {}
      if (val === 0) {
        data.categoryId = this.navId
      } else if (val === 1) {
        data.categoryId = this.levelBoxId
      } else if (val === 2) {
        data.categoryId = this.navParams.categoryId
      } else {
        data.categoryId = val.id
      }
      this.$refs.customiZation.create(data)
    },
    // 查看详情
    handleView(item) {
      this.showProduct = true
      this.$nextTick(() => {
        this.$refs.productInfo.handleView(item)
      })
    },
    // 点击广告
    handleClickAd(item) {
      if (item.url) window.open(item.url)
      else {
        this.adOpen = true
        const img = item.photoAddress_oss ? item.photoAddress_oss.split(',') : item.photoAddress ? item.photoAddress.split(',').map(item => this.imgPath + item) : []
        img.map((item, index) => {
          img[index] = item
        })
        if (img.length > 1) this.adImg = img.slice(1)
        else this.adImg = img
      }
    },
    // 查询促销品列表
    getPromotionList() {
      getPromotionIndexList({ pageNum: 1, pageSize: 6 }).then(res => {
        const { code, msg, rows } = res
        if (code === 200) this.promotionList = rows
        else this.$message.error(msg)
      })
    },
    // 查询促销品供应信息
    getPromotion(item, type = '') {
      const priceArr = item.promotions.map(ite => ite.price)
      const minPrice = Math.min(...priceArr)
      const index = item.promotions.findIndex(ite => ite.price === minPrice)
      let unit = ''
      if (item.promotions[index].method === 'ton') unit = '吨'
      else unit = item.unit
      if (type === 'num') return item.promotions[index].stock + unit
      else if (type === 'price') return minPrice
      else return unit
    },
    // 跳转至促销品列表
    handlePromotion() {
      this.$router.push({ name: 'Sale' })
    },
    // 我要采购跳转新页面
    handleBuy(item) {
      if (!!getToken() && (this.userType !== '00' && this.userType !== '05')) {
        this.$refs.bindingInfo.handleOpen()
        return
      }
      const { href } = this.$router.resolve({ path: '/sale/detail', query: { productId: item.id } })
      window.open(href, '_blank')
    },
    // 编辑产品分类排序
    handleDrag() {
      if (!!getToken() && (this.userType !== '00' && this.userType !== '05')) {
        this.$refs.bindingInfo.handleOpen()
        return
      }
      this.dragList = [...this.navList]
      this.dragOpen = true
    },
    // 提交产品分类排序
    handleDragSubmit() {
      const configValue = this.dragList.map(item => item.id).toString()
      updateSort({ configValue }).then(res => {
        const { code, msg } = res
        if (code === 200) {
          this.$message.success('操作成功')
          this.dragOpen = false
          this.$set(this, 'navList', this.dragList)
        } else this.$message.error(msg)
      })
    },
    // 关闭K线切签
    handleCloseLine() {
      this.showline = false
      sessionStorage.setItem('klineShow', 'false')
    }
  }
}
</script>
<style lang="scss" scoped>
@import '~@/assets/styles/custom-public.scss';
@import '~@/assets/styles/custom-new.scss';
.diy {
  ::v-deep {
    .el-col-lg-70 {
      width: 14.285%;
    }
    .el-col-lg-90 {
      width: 11.11%;
    }
  }
}
.brand {
  &-btn {
    position: absolute;
    top: 0;
    right: 0;
    height: 53px;
    display: inline-flex;
    align-items: center;
    cursor: pointer;
    img {
      width: 20px;
      height: 20px;
    }
    span {
      display: inline-block;
      font-size: 14px;
      color: $disabled;
      margin-left: 10px;
    }
  }
  &-list {
    width: 100%;
    height: 52px;
    line-height: 32px;
    display: flex;
    align-items: center;
    background: url(../../public/imgs/category-bg.png) center no-repeat;
    background-size: 100% 100%;
    &-title {
      display: inline-block;
      font-size: 14px;
      font-weight: 500;
      color: $white;
      width: 95px;
      text-align: center;
    }
    &-item {
      display: inline-block;
      padding: 0 15px;
      margin-left: 15px;
      font-size: 12px;
      color: #1e5ac9;
      text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.64);
      cursor: pointer;
      &:hover,
      &.active {
        background-color: $white;
        border-radius: 5px;
      }
    }
  }
  &-none {
    width: 100%;
    font-size: 14px;
    line-height: 50px;
    text-align: center;
  }
}
.webMain-right-kline-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 5px;
  .kline-chart {
    width: calc(100% - 115px);
  }
  ::v-deep {
    .is-horizontal {
      display: none;
    }
    .kline-scrollbar {
      width: 130px !important;
      height: 285px !important;
    }
  }
  .kline-box {
    display: inline-flex;
    flex-direction: column;
    flex-shrink: 0;
    width: 100%;
    min-height: 265px;
    background-color: #f2f2f2;
    border-radius: 5px;
    margin-top: 5px;
    padding: 7px 15px;
    overflow-x: hidden;
    overflow-y: auto;
    &-item {
      display: inline-flex;
      align-items: center;
      font-size: 12px;
      line-height: 35px;
      color: $info;
      cursor: pointer;
      i {
        font-size: 20px;
        margin-right: 5px;
      }
      i.item-checkbox {
        width: 16px;
        height: 16px;
        margin-right: 7px;
        margin-left: 2px;
        border-radius: 2px;
        border: 1px solid #2f74f3;
        &.checked {
          background: url('~@/assets/images/checkboxed.png') center no-repeat;
          background-size: 100% 100%;
          border: 0;
        }
      }
      i.el-icon-success {
        color: #2f74f3;
      }
      i.circle {
        display: inline-block;
        width: 16px;
        height: 16px;
        margin-right: 7px;
        margin-left: 2px;
        border-radius: 50%;
        border: 1px solid #2f74f3;
      }
    }
  }
}
.webMain-promotional-product {
  margin: 20px 0 10px;
  .promotional-product-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    .title-info {
      display: inline-flex;
      align-items: center;
      b {
        font-size: 14px;
        font-weight: 500;
        color: $info;
      }
      span {
        font-size: 12px;
        color: $white;
        background-color: #f43f3f;
        padding: 2px 5px;
        text-transform: uppercase;
        border-radius: 8px 0 8px 0;
        margin-left: 5px;
      }
    }
    .title-more {
      font-size: 12px;
      color: $disabled;
      cursor: pointer;
      &:hover {
        color: $blue;
      }
    }
  }
}
.navSetting {
  padding: 0 20px;
  &-title {
    padding: 0 24px;
    color: $info;
    font-size: 14px;
    line-height: 20px;
  }
  &-info {
    background-color: #f0f3f9;
    padding: 18px 0;
    font-size: 14px;
    line-height: 20px;
    position: relative;
    margin: 15px 0;
    span {
      display: inline-block;
      color: $font;
      margin: 0 20px;
    }
    &:before {
      content: '';
      position: absolute;
      top: -16px;
      left: 40px;
      width: 0;
      height: 0;
      border-color: transparent transparent #f0f3f9 transparent;
      border-style: solid;
      border-width: 8px;
    }
  }
  &-list {
    display: inline-flex;
    flex-direction: column;
    width: auto;
    margin-left: 20px;
    border: 1px solid #cbd6e2;
    border-radius: 5px;
    overflow: hidden;
    &-title {
      display: inline-flex;
      height: 38px;
      background-color: #f8f9fb;
      align-items: center;
      font-size: 12px;
      color: $info;
      padding: 0 20px;
      border-bottom: 1px solid #cbd6e2;
    }
    &-box {
      width: 330px;
      padding: 12px 20px;
      display: inline-flex;
      flex-direction: column;
    }
    &-item {
      display: inline-flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      height: 30px;
      padding: 0 15px;
      border: 1px solid #cbd7e2;
      border-radius: 5px;
      font-size: 12px;
      color: $info;
      cursor: move;
      margin: 3px 0;
      i {
        display: inline-block;
        width: 20px;
        height: 20px;
        background: url('~@/assets/images/drag.png') center no-repeat;
        background-size: 100% 100%;
      }
      &.chosenClass {
        border-color: $blue !important;
        background: none !important;
      }
      &.dragClass,
      &:hover {
        background-color: #ecf3ff !important;
        color: $blue !important;
        border-color: $blue !important;
        box-shadow: 0 0 7px 0 rgba(46, 115, 243, 0.39) !important;
        i {
          background: url('~@/assets/images/dragHover.png') center no-repeat;
          background-size: 100% 100%;
        }
      }
    }
  }
}
.padding-10 {
  padding: 10px;
}
</style>
