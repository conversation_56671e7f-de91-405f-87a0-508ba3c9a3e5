<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" @submit.native.prevent>
      <el-form-item label="关键词" prop="keyword">
        <el-input v-model="queryParams.keyword" placeholder="请输入关键词" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAdd">新增</el-button>
      </el-form-item>
    </el-form>
    <!-- 关键词列表 -->
    <el-table v-loading="loading" :data="keywordList" stripe fit class="custom-table">
      <el-table-column label="关键词" prop="keyword" />
      <el-table-column label="类型" prop="canModify" align="center">
        <template slot-scope="scope">{{ scope.row.canModify ? '用户自定义' : '系统内置' }}</template>
      </el-table-column>
      <el-table-column label="创建时间" prop="createTime" align="center" />
      <el-table-column label="创建人" prop="creatorName" align="center" />
      <el-table-column label="操作" prop="action" width="120" align="center">
        <template slot-scope="scope">
          <el-button type="text" size="small" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <div class="custom-pagination">
      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
    </div>
  </div>
</template>
<script>
import { getTenderKeywordList, deleteTenderKeyword, addTenderKeyword, getTenderKeywordExist } from '@/api/tender'

export default {
  name: 'TenderKeyword',
  data() {
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        keyword: ''
      },
      loading: false,
      keywordList: [],
      total: 0
    }
  },
  created() {
    this.getList()
  },
  methods: {
    // 查询列表
    // prettier-ignore
    getList() {
      this.loading = true
      getTenderKeywordList(this.queryParams).then(res => {
        const { code, msg, rows, total } = res
        if (code === 200) {
          this.keywordList = rows
          this.total = total
        } else {
          this.$message.error(msg)
        }
      }).finally(() => {
        this.loading = false
      })
    },
    handleQuery() {
      this.getList()
    },
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 删除
    // prettier-ignore
    handleDelete(row) {
      const { id: keywordId, keyword } = row
      this.$modal.confirm(`确定删除关键词"${keyword}"吗？`).then(() => {
        return deleteTenderKeyword({ keywordId })
      }).then(() => {
        this.$message.success('删除成功')
        this.getList()
      }).catch(() => {})
    },
    // 添加
    // prettier-ignore
    handleAdd() {
      const that = this
      this.$prompt('请输入关键词', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false,
        inputErrorMessage: '关键词为空'
      }).then(async ({ value }) => {
        if (!value || ((value && value.trim() === ''))) return
        const data = { keyword: value }
        const { data: isHas } = await getTenderKeywordExist({keyword:value})
        if(!isHas){
          addTenderKeyword(data).then(function (res) {
            const { code, msg } = res
            if (code === 200) {
              that.$message.success('新增成功')
              that.getList()
            } else that.$message.error(msg)
          })
        } else this.$message.warning('关键词已存在')
      }).catch(() => {})
    }
  }
}
</script>
<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
</style>
