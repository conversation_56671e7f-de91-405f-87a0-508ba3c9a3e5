<template>
  <div class="newBox vh-85 p20">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" @submit.native.prevent>
      <el-form-item label="标题" prop="title">
        <el-input v-model="queryParams.title" placeholder="请输入标题" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
        <el-button type="primary" size="small" icon="el-icon-plus" @click="handleAdd" v-hasPermi="['system:news:add']">新增</el-button>
      </el-form-item>
    </el-form>

    <el-tabs v-model="activeName" @tab-click="handleClick" type="card">
      <el-tab-pane label="系统公告" name="inform"></el-tab-pane>
      <el-tab-pane label="系统消息" name="message"></el-tab-pane>
    </el-tabs>

    <el-table ref="list" :data="list" style="width: 100%" class="custom-table">
      <el-table-column type="index" label="序号" width="55" align="center"></el-table-column>
      <el-table-column prop="noticeTitle" width="300" show-overflow-tooltip label="消息标题"></el-table-column>
      <el-table-column prop="noticeContent" label="消息内容">
        <template slot-scope="{ row }">
          <div v-html="row.noticeContent"></div>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="status" label="状态" width="130">
        <template slot-scope="{ row }">
          <template>
            <el-switch v-model="row.status" active-text="启用" inactive-text="停用" :active-value="1" :inactive-value="-1" @change="handleStatus(row, $event)" class="table-switch"></el-switch>
          </template>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="120" align="center">
        <template slot-scope="{ row }">
          <el-button size="mini" type="text" icon="el-icon-view" @click="handleUpdate(row)" v-hasPermi="['system:news:query']">详情</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(row)" v-hasPermi="['system:news:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加或修改公告对话框 -->
    <el-dialog v-dialogDragBox :title="title" :visible.sync="open" width="780px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px" :disabled="isEdit">
        <el-row>
          <el-col :span="12">
            <el-form-item label="标题" prop="noticeTitle">
              <el-input v-model="form.noticeTitle" placeholder="请输入标题" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="类型" prop="noticeType">
              <el-select v-model="form.noticeType" placeholder="请选择类型" style="width: 100%">
                <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="状态">
              <el-radio-group v-model="form.status">
                <el-radio v-for="item in statusOptions" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="内容">
              <editor :key="key" v-model="form.noticeContent" :read-only="isEdit" :min-height="192" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <template v-if="isEdit">
          <el-button type="primary" @click="open = false">关 闭</el-button>
        </template>
        <template v-else>
          <el-button type="primary" @click="handleSubmit">确 定</el-button>
          <el-button @click="open = false">取 消</el-button>
        </template>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listNotice, getNotice, delNotices, addNotice, updateNotice } from '@/api/system/notice'

export default {
  name: 'News',
  data() {
    return {
      activeName: 'inform',
      options: [
        { label: '系统公告', value: 'inform' },
        { label: '系统消息', value: 'message' }
      ],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        title: undefined,
        noticeType: 'inform'
      },
      loading: true,
      total: 0,
      list: [],
      open: false,
      title: '',
      form: {},
      rules: {
        noticeTitle: [{ required: true, message: '公告标题不能为空', trigger: 'blur' }],
        noticeType: [{ required: true, message: '公告类型不能为空', trigger: 'change' }]
      },
      statusOptions: [
        { label: '启用', value: 1 },
        { label: '停用', value: -1 }
      ],
      isEdit: false,
      key: 1
    }
  },
  created() {
    this.getList()
  },
  methods: {
    handleClick(e) {
      this.queryParams.noticeType = e.name
      this.handleQuery()
    },
    // 查询列表
    getList() {
      this.loading = true
      listNotice(this.queryParams).then(res => {
        const { code, msg, rows, total } = res
        if (code === 200) {
          this.list = rows
          this.total = total
          this.loading = false
        } else {
          this.$message.error(msg)
        }
      })
    },
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 重置搜索
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 表单重置
    reset() {
      this.form = {
        noticeId: undefined,
        noticeTitle: undefined,
        noticeType: undefined,
        noticeContent: undefined,
        status: 1
      }
      this.key = Math.random()
      this.resetForm('form')
    },
    // 新增
    handleAdd() {
      this.isEdit = false
      this.reset()
      this.open = true
      this.title = '添加信息'
    },
    // 修改状态
    // prettier-ignore
    handleStatus(row, status) {
      const that = this
      const data = { id: row.noticeId, status }
      updateNotice(data).then(res=>{
        if (res.code === 200) {
          that.$message.success('操作成功')
        } else {
          that.$message.error(res.msg)
          row.status = status === -1 ? 1 : -1
        }
      })
    },
    // 修改
    handleUpdate(row) {
      this.isEdit = true
      this.reset()
      const noticeId = row.noticeId
      getNotice(noticeId).then(response => {
        this.form = response.data
        this.open = true
        this.title = '修改信息'
      })
    },
    // 提交
    handleSubmit() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          this.form.id = this.form.noticeId
          if (this.form.noticeId != undefined) {
            updateNotice(this.form).then(response => {
              this.$modal.msgSuccess('修改成功')
              this.open = false
              this.getList()
            })
          } else {
            addNotice(this.form).then(response => {
              this.$modal.msgSuccess('新增成功')
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    // 删除
    // prettier-ignore
    handleDelete(row) {
      const noticeIds = row.noticeId || this.ids
      this.$modal.confirm('是否确认删除选中的信息？').then(function() {
        return delNotices({noticeIds});
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
.p20 {
  padding: 20px;
}
</style>
