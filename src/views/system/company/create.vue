<template>
  <div>
    <el-dialog v-dialogDragBox :title="title" :visible.sync="open" width="1150px" class="custom-dialog">
      <el-row style="margin-left: 20px" v-if="isShow">
        <el-col :span="1.5">
          <el-button type="primary" icon="el-icon-edit-outline" size="small" @click="handleChange">选择潜在供应商</el-button>
        </el-col>
      </el-row>
      <div class="formBox">
        <el-form ref="form" :model="form" :rules="rules" label-width="10em" :disabled="disabled || readonly">
          <el-row :gutter="10">
            <el-col :span="12">
              <el-form-item label="公司名称" prop="name">
                <div style="display: flex; align-items: center">
                  <el-autocomplete ref="companyName" style="width: 100%" v-model="form.name" placeholder="请输入公司名称" value-key="companyName" :fetch-suggestions="querySearchAsync" @select="handleSelect" :trigger-on-focus="false" @keyup.enter.native="handleSearch(0)"></el-autocomplete>
                  <el-switch style="margin-left: 10px" class="custom-show-switch" v-model="isRelevancy" active-text="关联" inactive-text="不关联" v-if="formGid"></el-switch>
                  <el-button type="text" icon="el-icon-view" size="small" style="margin-left: 10px" :disabled="!hasView" @click="handleSearch(1)">查看详情</el-button>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="公司法人" prop="legal">
                <el-input v-model="form.legal" placeholder="请输入公司法人"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="统一社会信用代码" prop="uscCode">
                <el-input v-model="form.uscCode" placeholder="请输入统一社会信用代码"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="公司地址" prop="address">
                <el-input v-model="form.address" placeholder="请输入公司地址"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="公司标语" prop="slogan">
                <el-input v-model="form.slogan" placeholder="请输入公司标语"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="Logo简称" prop="showName">
                <el-input v-model="form.showName" maxlength="5" placeholder="请输入Logo简称"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="经营产品" prop="businessScope">
                <el-input v-model="form.businessScope" type="textarea" :autosize="{ minRows: 3, maxRows: 6 }" resize="none" placeholder="请输入经营产品"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-table :data="form.banks" style="width: 100%" stripe class="custom-table custom-table-cell0">
                <el-table-column align="center" prop="bankName" label="开户行">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`banks.${scope.$index}.bankName`" :rules="rules.bankName">
                      <el-input v-model="scope.row.bankName" size="small" placeholder="请输入开户行" />
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column align="center" prop="bankUser" label="账户名">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`banks.${scope.$index}.bankUser`" :rules="rules.bankUser">
                      <el-input v-model="scope.row.bankUser" size="small" placeholder="请输入账户名" />
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column align="center" prop="bankNo" label="银行账号">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`banks.${scope.$index}.bankNo`" :rules="rules.bankNo">
                      <el-input v-model="scope.row.bankNo" size="small" placeholder="请输入银行账号" />
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column align="center" prop="bankNo" label="默认选中" width="80">
                  <template slot-scope="scope">
                    <el-form-item label-width="0">
                      <el-checkbox v-model="scope.row.checked" :disabled="form.banks.find(item => item.checked) && scope.$index !== form.banks.findIndex(item => item.checked)" />
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column align="center" label="操作" v-if="!disabled">
                  <template slot-scope="scope">
                    <el-button type="primary" size="small" icon="el-icon-plus" @click="handlePlusBank">添加</el-button>
                    <el-button type="danger" size="small" icon="el-icon-delete" :disabled="form.banks.length === 1" @click="handleDelBank(scope.$index)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
            <el-col :span="24">
              <el-table :data="form.contacts" style="width: 100%" stripe class="custom-table custom-table-cell0">
                <el-table-column align="center" prop="bankNo" label="默认选中" width="80">
                  <template slot-scope="scope">
                    <el-form-item label-width="0">
                      <el-checkbox v-model="scope.row.checked" :disabled="form.contacts.find(item => item.checked) && scope.$index !== form.contacts.findIndex(item => item.checked)" />
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column align="center" prop="nickName" label="联系人姓名">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`contacts.${scope.$index}.nickName`" :rules="rules.nickName">
                      <el-input v-model="scope.row.nickName" size="small" placeholder="请输入联系人姓名" />
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column align="center" prop="phone" label="联系人电话">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`contacts.${scope.$index}.phone`" :rules="rules.phone">
                      <el-input v-model="scope.row.phone" size="small" placeholder="请输入联系人电话" />
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column align="center" prop="post" label="联系人职务">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`contacts.${scope.$index}.post`" :rules="rules.post">
                      <el-input v-model="scope.row.post" size="small" placeholder="请输入联系人职务" />
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column align="center" prop="post" label="授权有效期">
                  <template slot-scope="scope">
                    <el-tooltip class="item" effect="dark" :content="`有效期至：${parseTime(scope.row.authTimeEnd, '{y}年{m}月{d}日')}`" placement="top" v-if="!!scope.row.authTimeEnd">
                      <el-button type="text" icon="el-icon-edit" size="small" @click="handleAddImpower(scope.row, scope.$index)">修改授权</el-button>
                    </el-tooltip>
                    <el-button type="text" icon="el-icon-plus" size="small" @click="handleAddImpower(scope.row, scope.$index)" v-else>添加授权</el-button>
                  </template>
                </el-table-column>
                <el-table-column align="center" label="操作" v-if="!disabled">
                  <template slot-scope="scope">
                    <el-button type="primary" size="small" icon="el-icon-plus" @click="handlePlus">添加</el-button>
                    <el-button type="danger" size="small" icon="el-icon-delete" :disabled="form.contacts.length === 1" @click="handleDel(scope.$index)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
            <el-col :span="24">
              <el-form-item label="供应类目" prop="process">
                <div class="class-tree-tag" key="tree" v-if="form.categoryName.length">
                  <div class="tag-item pointer" v-for="(item, index) in form.categoryName" :key="item.id">
                    {{ item.name }}
                    <i class="pointer el-icon-error tag-btn" @click="deleteTag(index)" v-if="!disabled"></i>
                  </div>
                </div>
                <el-cascader-panel ref="category" v-model="form.categoryData" :options="tree" :props="categoryProps" @change="handleChangeCategory" class="category-cascader" v-if="!disabled">
                  <template slot-scope="{ data }">
                    <span>{{ data.name }}</span>
                    <span v-if="data.model" style="font-size: 12px; color: red">(规格型号：{{ data.model }})</span>
                  </template>
                </el-cascader-panel>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="备注" prop="remark">
                <div class="remark" v-html="form.remark" v-if="disabled || readonly"></div>
                <editor v-model="form.remark" :min-height="300" v-else />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <template v-if="disabled || readonly">
          <button type="button" class="custom-dialog-btn primary" @click="open = false">关闭</button>
        </template>
        <template v-else>
          <button type="button" class="custom-dialog-btn" @click="open = false">取消</button>
          <button type="button" class="custom-dialog-btn primary" @click="handleSubmit">确定</button>
        </template>
      </div>
    </el-dialog>

    <el-dialog v-dialogDragBox title="选择潜在供应商" :visible.sync="listOpen" width="80%" class="custom-dialog" @before-close="handleClose">
      <div style="padding: 0 20px">
        <el-form :model="listQuery" ref="listQuery" size="small" :inline="true" @submit.native.prevent>
          <el-form-item label="" prop="name">
            <el-input v-model="listQuery.name" placeholder="请输入公司名称" clearable @keyup.enter.native="handleListQuery" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="small" @click="handleListQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="small" @click="resetListQuery">重置</el-button>
          </el-form-item>
        </el-form>
        <el-table ref="list" :data="listData" stripe style="width: 100%" @selection-change="handleSelectionChange" :key="key" class="custom-table" row-key="id">
          <el-table-column :selectable="chckeSelect" type="selection" width="50" align="center"></el-table-column>
          <el-table-column prop="companyName" label="公司名称" align="center" show-overflow-tooltip min-width="150"></el-table-column>
          <el-table-column prop="legal" label="法人" align="center" show-overflow-tooltip width="60"></el-table-column>
          <el-table-column prop="phone" label="联系电话" align="center" show-overflow-tooltip width="110"></el-table-column>
          <el-table-column prop="headUser" label="负责人" align="center" show-overflow-tooltip width="60"></el-table-column>
          <el-table-column prop="headPhone" label="负责人电话" align="center" show-overflow-tooltip width="110"></el-table-column>
          <el-table-column prop="headPost" label="负责人职位" align="center" show-overflow-tooltip width="80"></el-table-column>
          <el-table-column prop="address" label="地址" align="center" show-overflow-tooltip>
            <template slot-scope="{ row }">{{ removeHtmlTag(row.address, 300) }}</template>
          </el-table-column>
          <el-table-column prop="uscCode" label="统一社会信用代码" align="center" show-overflow-tooltip width="180"></el-table-column>
          <el-table-column prop="uscCode" label="状态" align="center" show-overflow-tooltip width="80">
            <template slot-scope="{ row }">
              <span class="color-info" v-if="row.status === 1">已添加</span>
              <span class="color-blue" v-if="row.status === 0">未添加</span>
              <span class="color-success" v-if="row.status === 2">添加成功</span>
              <span class="color-red" v-if="row.status === -1">添加失败</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="180">
            <template slot-scope="{ row }">
              <el-button type="primary" plain size="mini" :disabled="row.status > 0" icon="el-icon-plus" @click="handleAdd('', row)">添加</el-button>
              <el-button type="danger" plain size="mini" icon="el-icon-delete" @click="handleDeletePotential(row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="custom-pagination">
          <pagination v-show="listTotal > 0" :total="listTotal" :page.sync="listQuery.pageNum" :limit.sync="listQuery.pageSize" @pagination="getlistCompanyPotential" />
        </div>
      </div>
      <div slot="footer">
        <el-badge>
          <button type="button" class="custom-dialog-btn" @click="listOpen = false">取消</button>
        </el-badge>
        <el-badge :value="listSelection.length ? listSelection.length : ''">
          <button type="button" class="custom-dialog-btn primary" @click="handleSubmitPotential">批量新增</button>
        </el-badge>
      </div>
    </el-dialog>
    <!-- 授权信息 -->
    <el-dialog v-dialogDragBox title="授权信息" :visible.sync="impowerOpen" width="800px" class="custom-dialog">
      <div style="padding: 0 20px">
        <el-form ref="impowerForm" :model="impowerForm" :rules="impowerRules" label-width="8em">
          <el-form-item label="联系人姓名">
            <span>{{ impowerForm.nickName }}</span>
          </el-form-item>
          <el-form-item label="真实姓名" prop="realName">
            <el-input style="width: 60%" placeholder="请输入真实姓名" v-model="impowerForm.realName"></el-input>
          </el-form-item>
          <el-form-item label="身份证号" prop="idCard">
            <el-input style="width: 60%" placeholder="请输入身份证号" v-model="impowerForm.idCard"></el-input>
          </el-form-item>
          <el-form-item label="授权起止时间" prop="timeArr">
            <el-date-picker style="width: 60%" v-model="impowerForm.timeArr" value-format="yyyy-MM-dd HH:mm:ss" type="daterange" start-placeholder="开始日期" end-placeholder="结束日期" :default-time="['00:00:00', '23:59:59']" :picker-options="pickerOptions"></el-date-picker>
          </el-form-item>
          <el-form-item label="授权委托书" prop="authLetter">
            <image-upload :limit="1" v-model="impowerForm.authLetter" />
          </el-form-item>
          <el-form-item label="身份证复印件" prop="idCardImage">
            <image-upload :limit="1" v-model="impowerForm.idCardImage" />
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer">
        <button class="custom-dialog-btn" @click="impowerOpen = false">取 消</button>
        <button class="custom-dialog-btn primary" @click="handleSubmitImpower">确 定</button>
      </span>
    </el-dialog>

    <!--  添加/修改授权信息  -->
    <authorization ref="authorization" @refresh="handleRefresh" />
    <!--  企业信息详情  -->
    <company ref="company" />
  </div>
</template>

<script>
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import { addCompanyPotential, listCompanyPotential, checkCompanyPotential, deleteCompanyPotential } from '@/api/system/company'
import { isCreditCode } from '@/utils/validate'
import { getlist } from '@/api/purchase/category'
import { supplier, ediSupplier } from '@/api/system/user'
import { getCompanyList, getRemoteCompanyInfo } from '@/api/tender'
import authorization from '@/components/authorization/index'
import Company from '@/views/payment/company'
import { removeHtmlTag } from '@/utils'

export default {
  components: { Company, authorization, Treeselect },
  data() {
    return {
      title: '',
      open: false,
      hasView: false,
      form: { categoryName: [] },
      rules: {
        name: [{ required: true, message: '请输入公司名称', trigger: 'blur' }],
        address: [{ required: true, message: '请输入公司地址', trigger: 'blur' }],
        // uscCode: [
        //   { required: true, message: '请输入统一社会信用代码', trigger: 'blur' },
        //   { validator: isCreditCode, message: '统一社会信用代码输入有误', trigger: 'blur' }
        // ],
        nickName: [{ required: true, message: '请输入联系人姓名', trigger: 'blur' }],
        phone: [
          { required: true, message: '请输入联系人电话', trigger: 'blur' },
          { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的电话号码', trigger: 'blur' }
        ]
      },
      disabled: false,
      productList: [],
      tree: [],
      categoryProps: { multiple: true, label: 'name', value: 'id' },
      key: 1,
      // 临时供应商列表
      listOpen: false,
      listData: [],
      listTotal: 0,
      listQuery: {
        pageNum: 1,
        pageSize: 10,
        name: undefined
      },
      listSelection: [],
      isShow: true,
      readonly: false,
      impowerOpen: false,
      impowerIndex: 0,
      impowerForm: {},
      impowerRules: {
        realName: [{ required: true, message: '请输入真实姓名', trigger: 'blur' }],
        idCard: [
          { required: true, message: '请输入身份证号', trigger: 'blur' },
          { pattern: /^[1-9]\d{5}(18|19|([23]\d))\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}(\d|X|x)$/, message: '请输入正确的身份证号', trigger: 'blur' }
        ],
        timeArr: [{ required: true, message: '请选择授权起止时间', trigger: 'change' }],
        authLetter: [{ required: true, message: '请上传授权书', trigger: ['blur', 'change'] }],
        idCardImage: [{ required: true, message: '请上传身份证复印件', trigger: ['blur', 'change'] }]
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now()
        }
      },
      isRelevancy: false, // 是否关联gid
      formGid: undefined
    }
  },
  created() {
    getlist().then(res => {
      if (res.code === 200) {
        this.tree = res.data
      } else {
        this.$message.error(res.msg)
      }
    })
  },
  methods: {
    removeHtmlTag,
    reset() {
      this.form = {
        name: undefined,
        gid: undefined,
        legal: undefined,
        phone: undefined,
        lat: undefined,
        lng: undefined,
        businessScope: undefined,
        certificationUrl: undefined,
        address: undefined,
        logoUrl: undefined,
        slogan: undefined,
        showName: undefined,
        siteImg: undefined,
        certImg: undefined,
        deviceImg: undefined,
        process: undefined,
        contacts: [{ nickName: undefined, phone: undefined, post: undefined, checked: false, uuid: undefined }],
        banks: [{ bankName: undefined, bankNo: undefined, bankUser: undefined, checked: false }],
        productIds: [],
        categoryName: [],
        categoryIds: [],
        remark: undefined,
        categoryData: []
      }
      this.resetForm('form')
      this.isRelevancy = false
    },
    // 新增
    handleAdd(title, data) {
      title = title || '新增潜在供应商'
      this.reset()
      this.hasView = false
      if (data) {
        const reg = /^1[3456789]\d{9}$/
        this.isShow = false
        this.form.name = data.companyName
        this.form.legal = data.legal
        this.form.phone = data.phone
        this.form.address = removeHtmlTag(data.address, 300)
        this.form.uscCode = data.uscCode
        const legal = { nickName: data.legal, phone: data.phone, post: '法人', checked: false, uuid: undefined }
        const contact = {
          nickName: data.headUser,
          phone: data.headPhone,
          post: data.headPost,
          checked: false,
          uuid: undefined
        }
        if ((legal.phone !== '-' && reg.test(data.phone)) || (contact.phone !== '-' && reg.test(data.headPhone))) {
          this.form.contacts = []
          if (legal.phone !== '-' && reg.test(data.phone)) this.form.contacts.push(legal)
          if (contact.phone !== '-' && reg.test(data.headPhone)) this.form.contacts.push(contact)
        }
      } else {
        this.isShow = true
      }
      this.title = title
      this.disabled = false
      this.readonly = false
      this.open = true
    },
    // 修改
    handleUpdate(row, title, readonly = false) {
      this.readonly = readonly
      this.reset()
      this.hasView = true
      supplier({ id: row.id }).then(res => {
        const { code, data } = res
        if (code === 200) {
          const banks = data.supplier.bankList.length ? data.supplier.bankList : [{ bankName: undefined, bankNo: undefined, bankUser: undefined, checked: false }]
          this.form = {
            ...this.form,
            ...{
              id: data.company.id,
              name: data.company.companyName,
              legal: data.supplier.legal,
              slogan: data.supplier.slogan,
              showName: data.supplier.showName,
              businessScope: data.supplier.businessScope,
              contacts: data.contacts,
              banks,
              address: data.company.address,
              uscCode: data.company.uscCode,
              remark: data.supplier.remark
            }
          }
          const categoryList = data.supplier.categoryList || []
          this.form.categoryData = []
          categoryList.map(Number).forEach(item => {
            let selectId = [this.cascadeDisplay(this.tree, item)]
            this.form.categoryData.push(...selectId)
          })
          this.form.categoryName = this.getDepart(categoryList, this.tree)
          this.title = title
          this.disabled = false
          this.open = true
        } else this.$message.error(msg)
      })
    },
    // 添加联系人
    handlePlus() {
      this.form.contacts.push({
        nickName: undefined,
        phone: undefined,
        post: undefined,
        checked: false,
        uuid: undefined
      })
    },
    // 删除联系人
    handleDel(index) {
      if (this.form.contacts.length === 1) return
      this.form.contacts.splice(index, 1)
    },
    // 添加银行
    handlePlusBank() {
      this.form.banks.push({ bankName: undefined, bankNo: undefined, checked: false })
    },
    // 删除银行
    handleDelBank(index) {
      if (this.form.banks.length === 1) return
      this.form.banks.splice(index, 1)
    },
    // 提交
    handleSubmit() {
      if (this.isRelevancy) this.form.gid = this.formGid
      this.$refs.form.validate(valid => {
        if (valid) {
          this.form.categoryIds = this.form.categoryName.map(item => item.id)
          this.form.supplierId = this.form.id
          if (this.form.id != null) {
            if (!this.form.banks[0].hasOwnProperty('bankNo') || !this.form.banks[0].bankNo) delete this.form.banks
            ediSupplier(this.form).then(res => {
              if (res.code === 200) {
                this.$message.success('修改成功')
                this.open = false
                this.$emit('refresh')
              } else {
                this.$message.error(res.msg)
              }
            })
          } else {
            if (!this.form.banks[0].hasOwnProperty('bankNo') || !this.form.banks[0].bankNo) delete this.form.banks
            addCompanyPotential(this.form).then(res => {
              if (res.code === 200) {
                this.$message.success('创建成功')
                this.open = false
                this.$parent.getList()
              } else {
                this.$message.error(res.msg)
              }
            })
          }
        }
      })
    },
    handleChangeCategory() {
      const arr = this.$refs['category'].getCheckedNodes()
      const categoryIds = arr.map(item => item.value)
      this.form.categoryName = this.getDepart(categoryIds, this.tree)
      this.key = Math.random()
    },
    deleteTag(index) {
      const arr = [...this.form.categoryName]
      arr.splice(index, 1)
      const categoryIds = arr.map(item => item.id)
      this.form.categoryData = []
      categoryIds.map(Number).forEach(item => {
        let selectId = [this.cascadeDisplay(this.tree, item)]
        this.form.categoryData.push(...selectId)
      })
      this.form.categoryName = this.getDepart(categoryIds, this.tree)
      this.key = Math.random()
    },
    cascadeDisplay(object, value) {
      for (const key in object) {
        if (object[key].id == value) return [object[key].id]
        if (object[key].children && Object.keys(object[key].children).length > 0) {
          const temp = this.cascadeDisplay(object[key].children, value)
          if (temp) return [object[key].id, temp].flat()
        }
      }
    },
    getDepart(arr, data, department = []) {
      if (typeof data === 'object') {
        for (let i = 0; arr[i] !== undefined; i++) {
          for (let j = 0; data[j] !== undefined; j++) {
            if (Number(arr[i]) === Number(data[j].id) || Number(arr[i]) === Number(data[j].id)) {
              department.push(data[j])
            }
          }
        }
        for (let m = 0; data[m] !== undefined; m++) {
          this.getDepart(arr, data[m].children, department)
        }
      }
      return department
    },
    // 选择潜在供应商
    handleChange() {
      this.listSelection = []
      this.listOpen = true
      this.open = false
      this.getlistCompanyPotential()
    },
    // 获取潜在供应商列表
    getlistCompanyPotential() {
      listCompanyPotential(this.listQuery).then(async res => {
        if (res.code === 200) {
          await Promise.all(
            res.rows.map(async item => {
              item.status = 0
              const { code, msg, data } = await checkCompanyPotential({ uscCode: item.uscCode })
              if (code === 200) item.status = data ? 1 : 0
              else this.$message.error(msg)
            })
          )
          this.listData = res.rows
          this.listTotal = res.total
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 检查是否可以选中
    chckeSelect(row, index) {
      return row.status === 0 || row.status === -1
    },
    // 搜索
    handleListQuery() {
      this.listQuery.pageNum = 1
      this.getlistCompanyPotential()
    },
    // 重置搜索
    resetListQuery() {
      this.resetForm('listQuery')
      this.handleListQuery()
    },
    // 选择潜在供应商
    handleSelectionChange(selection) {
      this.listSelection = selection
    },
    // 删除潜在供应商
    handleDeletePotential(row) {
      const ids = this.listSelection.length ? this.listSelection.map(item => item.id) : [row.id]
      this.$confirm('是否删除该潜在供应商？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteCompanyPotential({ ids: row.id }).then(res => {
          if (res.code === 200) {
            this.$message.success('删除成功')
            this.getlistCompanyPotential()
          } else {
            this.$message.error(res.msg)
          }
        })
      })
    },
    // 批量新增
    async handleSubmitPotential() {
      const arr = this.listSelection
      await Promise.all(
        arr.map(item => {
          // 手机号码正则验证
          const reg = /^1[3456789]\d{9}$/
          if ((item.phone !== '-' && reg.test(item.phone)) || (item.headPhone !== '-' && reg.test(item.headPhone))) {
            let data = {}
            data.name = item.companyName
            data.leagl = item.legal
            data.phone = item.phone
            data.address = item.address
            data.uscCode = item.uscCode
            const legal = { nickName: item.legal, phone: item.phone, post: '法人', checked: false, uuid: undefined }
            const contact = {
              nickName: item.headUser,
              phone: item.headPhone,
              post: item.headPost,
              checked: false,
              uuid: undefined
            }
            data.contacts = []
            if (legal.phone !== '-' && reg.test(item.phone)) data.contacts.push(legal)
            if (contact.phone !== '-' && reg.test(item.headPhone)) data.contacts.push(contact)
            item.status = -1
            addCompanyPotential(data).then(res => {
              if (res.code === 200) {
                item.status = 2
                this.$refs.list.toggleRowSelection(item)
              }
            })
          } else {
            item.status = -1
            this.$refs.list.toggleRowSelection(item)
          }
        })
      )
      await this.$forceUpdate()
    },
    handleClose() {
      this.$parent.getList()
    },
    // 模糊搜索企业
    async querySearchAsync(queryString, cb) {
      const list = (await getCompanyList({ companyName: queryString })) || []
      const results = queryString ? list.filter(this.createStateFilter(queryString)) : list
      cb(results)
    },
    createStateFilter(queryString) {
      return state => {
        return state.companyName.toLowerCase().indexOf(queryString.toLowerCase()) === 0
      }
    },
    // 选择企业
    handleSelect(item) {
      const { companyName, companyGid } = item
      if (companyName) {
        this.formGid = companyGid
        this.form.name = companyName
        this.handleSearch()
      }
    },
    // 搜索企业信息
    // prettier-ignore
    handleSearch(type = 0, name = '') {
      if (this.$refs.companyName) this.$refs.companyName.suggestions = []
      let input
      if (name) input = name
      else input = this.form.name ? this.form.name.trim() : ''
      if (input) {
        const options = {
          lock: true,
          text: '搜索中…',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        }
        let dataLoading = this.$loading(options)
        getRemoteCompanyInfo({ companyName: input }).then(res => {
          const { code, msg, data } = res
          if (code === 200) {
            this.$nextTick(() => {
              dataLoading.close()
            })
            if (data && data.hasOwnProperty('companyCreditCode') && data.companyCreditCode) {
              if (type === 1) this.$refs.company.getInfo(data, 'private')
              else {
                this.hasView = true
                this.form.legal = data.companyLegalPersonName
                this.form.uscCode = data.companyCreditCode
                this.form.address = removeHtmlTag(data.companyRegLocation,300)
                this.formGid = data.companyGid
              }
            } else {
              this.$nextTick(() => {
                dataLoading.close()
              })
              if (type === 1) this.$alert(`未查询到“<span style='color:#f00'>${ input }</span>”相关公司信息！<br />请检查公司名称是否正确，请输入公司全称！`,'系统提示', { dangerouslyUseHTMLString: true })
            }
          } else {
            this.$nextTick(() => {
              dataLoading.close()
            })
            if (type === 1) this.$alert(`未查询到“<span style='color:#f00'>${ input }</span>”相关公司信息！<br />请检查公司名称是否正确，请输入公司全称！`,'系统提示', { dangerouslyUseHTMLString: true })
          }
        })
      }
    },
    // 授权信息初始化
    handleInitImpower() {
      this.impowerForm = {
        realName: undefined,
        idCard: undefined,
        authTimeStart: undefined,
        authTimeEnd: undefined,
        timeArr: undefined,
        authLetter: undefined,
        idCardImage: undefined
      }
      this.resetForm('impowerForm')
    },
    // 打开授权信息窗口
    handleAddImpower(data = {}, index = 0) {
      if (data.uuid) this.$refs.authorization.handleOpen(data, index)
      else {
        this.impowerOpen = true
        this.handleInitImpower()
        this.impowerIndex = index
        this.impowerForm = { ...data }
        if (data.authTimeStart && data.authTimeEnd) {
          this.$set(this.impowerForm, 'timeArr', [data.authTimeStart, data.authTimeEnd])
        }
      }
    },
    // 授权信息保存
    handleSubmitImpower() {
      this.$refs.impowerForm.validate(valid => {
        if (valid) {
          this.impowerForm.authTimeStart = this.impowerForm.timeArr[0]
          this.impowerForm.authTimeEnd = this.impowerForm.timeArr[1]
          const arr = [...this.form.contacts]
          arr[this.impowerIndex] = { ...arr[this.impowerIndex], ...this.impowerForm }
          this.form.contacts = [...arr]
          this.impowerOpen = false
        }
      })
    },
    // 刷新
    handleRefresh(data, index) {
      let arr = [...this.form.contacts]
      arr[index] = { ...arr[index], ...data }
      this.form.contacts = [...arr]
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
.formBox {
  padding: 10px 20px 0;
}
::v-deep {
  .custom-show-switch {
    position: relative;
    margin: 0 10px;
    .el-switch__core {
      height: 24px;
      border-radius: 12px;
      min-width: 75px;
      &:after {
        left: 4px;
        top: 3px;
      }
    }
    &.el-switch {
      &.is-checked {
        .el-switch__core {
          &:after {
            margin-left: -20px;
            left: 100%;
          }
        }
      }
    }
    &.is-checked {
      .el-switch__label--left {
        opacity: 0;
      }
      .el-switch__label--right {
        opacity: 1;
      }
    }
    .el-switch__label {
      position: absolute;
      top: 0;
    }
    .el-switch__label--left {
      right: 0;
      color: #999999;
      z-index: 1;
      margin-right: 8px;
    }
    .el-switch__label--right {
      left: 0;
      color: #ffffff;
      opacity: 0;
      margin-left: 8px;
    }
  }
  .remark {
    padding: 0;
    margin: 0;
    * {
      padding: 0;
      margin: 0;
    }
  }
  .custom-table {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    border-bottom: 0;
    margin-bottom: 20px;
    .el-form-item {
      margin-top: 10px !important;
      margin-bottom: 10px !important;
      .el-form-item__error {
        top: 95%;
        padding-top: 0;
      }
    }
  }
  .component-upload-image.disabled {
    .el-upload {
      display: none;
    }
  }
  .class-tree-tag {
    padding: 10px;
    max-height: 170px;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    background: #f9f9fa;
    overflow-y: auto;
    display: flex;
    flex-wrap: wrap;
    align-content: flex-start;
    margin-bottom: 10px;
    .tag-item {
      padding: 0 13px;
      border-radius: 5px;
      background-color: #e5e5e5;
      font-size: 12px;
      line-height: 30px;
      color: #333333;
      margin-right: 7px;
      margin-bottom: 7px;
      position: relative;
      transition: all 0.3s;
      .tag-btn {
        display: none;
        position: absolute;
        top: -3px;
        right: -3px;
        color: #333333;
      }
      &:hover,
      &.item-active {
        background-color: #e0ebff;
        color: #2e73f3;
        .tag-btn {
          display: inline-block;
        }
      }
    }
  }
  .category-cascader {
    height: 400px;
    .el-cascader-menu__wrap {
      height: 100%;
    }
  }
}
</style>
