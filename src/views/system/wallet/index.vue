<template>
  <div class="app-container bgcf9 vh-85">
    <div class="wallte">
      <div class="wallte-header">
        当前余额
        <div class="wallte-header-num">
          {{ balance }}
          <span style="font-size: 14px">元</span>
        </div>
        <div class="wallte-header-btn pointer" @click="handleRecharge">我要充值</div>
      </div>
      <div class="wallte-main">
        <div class="wallte-main-tab">
          <div class="tab-item" :class="{ active: queryParams.logType === item.value }" v-for="item in tabOptions" :key="item.value" @click="handleChange(item)">{{ item.label }}</div>
        </div>
        <template v-for="item in tabOptions">
          <div class="wallte-main-table" :key="item.value" v-if="queryParams.logType === item.value">
            <el-table v-loading="loading" :data="dataList" stripe header-row-class-name="table-header" row-class-name="table-row" v-if="dataList.length">
              <el-table-column label="序号" align="center" width="50">
                <template slot-scope="scope">
                  <span class="table-cl333">{{ scope.$index + 1 }}</span>
                </template>
              </el-table-column>
              <el-table-column label="订单号" prop="serialNum" align="center">
                <template slot-scope="{ row }">
                  <span class="table-cl999">{{ row.serialNum }}</span>
                </template>
              </el-table-column>
              <el-table-column label="充值时间" prop="createTime" align="center">
                <template slot-scope="{ row }">
                  <span class="table-cl999">{{ row.createTime }}</span>
                </template>
              </el-table-column>
              <el-table-column label="充值方式" prop="type" align="center">
                <template slot-scope="{ row }">
                  <span class="table-cl999">{{ row.type }}</span>
                </template>
              </el-table-column>
              <el-table-column label="状态" prop="logState" align="center" width="140">
                <template slot-scope="{ row }">
                  <span class="table-cl333">{{ row.logState === 'success' ? '支付成功' : '取消支付' }}</span>
                </template>
              </el-table-column>
              <el-table-column label="充值金额(元)" prop="amount" align="center">
                <template slot-scope="{ row }">
                  <span class="table-orange">{{ '+ ￥' + row.amount }}</span>
                </template>
              </el-table-column>
              <el-table-column label="操作" align="center">
                <template slot-scope="{ row }">
                  <el-button type="text" size="mini" icon="el-icon-view" @click="handleView(row)">详情</el-button>
                </template>
              </el-table-column>
            </el-table>
            <el-empty v-else />
            <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
          </div>
        </template>
      </div>
    </div>

    <!-- 充值 -->
    <el-dialog v-dialogDragBox title="充值" :visible.sync="open" width="600px" custom-class="price-dialog">
      <div class="price-title">充值金额</div>
      <el-radio-group v-model="amountChecked" size="medium" @change="handleChangeAmount" class="price-list">
        <el-radio v-for="item in amountOptions" :key="item.label" :label="item.label" border>
          {{ item.title }}
        </el-radio>
        <div class="price-input" v-show="amountChecked === -1">
          <el-input ref="amount" v-model="amount" placeholder="请输入充值金额" onkeyup="this.value=this.value.replace(/^\D*([0-9]\d*\.?\d{0,2})?.*$/,'$1','')">
            <span slot="suffix" class="price-input-tip">元</span>
          </el-input>
        </div>
      </el-radio-group>
      <div class="price-title">支付方式</div>
      <el-radio-group v-model="payChecked" size="medium">
        <el-radio v-for="item in payOptions" :key="item.label" :label="item.label" border>{{ item.title }}</el-radio>
      </el-radio-group>
      <span slot="footer">
        <el-button @click="handleCancle">取消</el-button>
        <el-button type="primary" @click="handleGetQRCode" :disabled="!amount">确认支付</el-button>
      </span>
    </el-dialog>

    <!-- 支付二维码 -->
    <qr-code ref="qrCode" :qrcodeText="qrcodeText" :qrcodeImage="qrcodeImage" v-if="qrcodeOpen" :width="400" :height="400" />

    <!-- 充值详情 -->
    <el-dialog v-dialogDragBox title="充值详情" :visible.sync="viewOpen" width="750px">
      <el-descriptions title="" :column="1" size="medium" border>
        <el-descriptions-item label="订单号">{{ viewInfo.outTradeNo }}</el-descriptions-item>
        <el-descriptions-item label="支付单号">{{ viewInfo.transactionId }}</el-descriptions-item>
        <el-descriptions-item label="交易状态">{{ viewInfo.tradeStateDesc }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ viewInfo.createTime }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ viewInfo.updateTime }}</el-descriptions-item>
        <el-descriptions-item label="完成时间">{{ parseTime(viewInfo.successTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</el-descriptions-item>
        <el-descriptions-item label="充值金额" :contentStyle="{ color: '#f35d09' }">{{ viewInfo.amount }}元</el-descriptions-item>
      </el-descriptions>
      <div slot="footer">
        <el-button type="success" @click="handleRefundList(viewInfo)" plain v-if="viewInfo.refund">退款明细</el-button>
        <el-button type="danger" @click="handleOpenRefund(viewInfo)" plain :disabled="parseFloat((viewInfo.amount - viewInfo.refund).toFixed(12)) > balance ? true : false" v-if="viewInfo.amount > viewInfo.refund">申请退款</el-button>
        <el-button type="primary" @click="viewOpen = false">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 退款明细 -->
    <el-dialog v-dialogDragBox title="退款明细" :visible.sync="refundOpen" width="1300px">
      <el-table v-loading="refundLoading" :data="refundData" style="width: 100%">
        <el-table-column prop="outRefundNo" label="退款单号" align="center" width="170"></el-table-column>
        <el-table-column prop="outTradeNo" label="充值单号" align="center" width="170"></el-table-column>
        <el-table-column prop="total" label="充值金额" align="center">
          <template slot-scope="{ row }">
            <span style="color: #f35d09">￥{{ row.total }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="refund" label="退款金额" align="center">
          <template slot-scope="{ row }">
            <span style="color: #f35d09">￥{{ row.refund }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="channel" label="退款渠道" align="center" :formatter="channelFormat" width="90"></el-table-column>
        <el-table-column prop="status" label="退款状态" align="center" :formatter="statusFormat" width="90"></el-table-column>
        <el-table-column prop="createTime" label="创建时间" align="center" width="150"></el-table-column>
        <el-table-column prop="successTime" label="成功时间" align="center" width="150">
          <template slot-scope="{ row }">{{ parseTime(row.successTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</template>
        </el-table-column>
        <el-table-column prop="updateTime" label="更新时间" align="center" width="150"></el-table-column>
      </el-table>
      <div slot="footer">
        <el-button type="primary" @click="refundOpen = false">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 申请退款 -->
    <el-dialog v-dialogDragBox title="申请退款" :visible.sync="applyOpen" width="500px">
      <el-form ref="applyForm" :model="applyForm" :rules="applyRules" label-width="0">
        <el-form-item label="" prop="refund">
          <el-input v-model="applyForm.refund" placeholder="请输入退款金额">
            <span slot="suffix">元</span>
          </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="applyOpen = false">取 消</el-button>
        <el-button type="primary" @click="handleRefund" :loading="applyLoading">{{ applyLoading ? '退款中' : '确 定' }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { walletBalance, walletLogList, walletRecharge, walletRechargeOrder, walletRefund, walletRefundOrder, walletOrderDetail, walletRefundList } from '@/api/system/wallet'
import QrCode from '@/views/components/qrcode'
export default {
  components: { QrCode },
  data() {
    return {
      balance: undefined,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        logType: 'recharge'
      },
      loading: true,
      dataList: [],
      total: 0,
      tabOptions: [
        { label: '充值记录', value: 'recharge' },
        { label: '交易记录', value: 'consume' }
      ],
      open: false,
      amount: undefined,
      wechatInfo: {},
      // 充值金额选项
      amountChecked: undefined,
      amountOptions: [
        { label: 100, title: '充值100元' },
        { label: 500, title: '充值500元' },
        { label: 1000, title: '充值1000元' },
        { label: 2000, title: '充值2000元' },
        { label: 5000, title: '充值5000元' },
        { label: -1, title: '自定义' }
      ],
      payChecked: 1,
      payOptions: [{ label: 1, title: '微信' }],
      // 生成二维码内容
      qrcodeOpen: false,
      qrcodeText: undefined,
      qrcodeImage: undefined,
      timers: undefined,
      // 充值详情
      viewOpen: false,
      viewInfo: {},
      // 退款明细
      refundOpen: false,
      refundLoading: true,
      refundData: [],
      // 申请退款
      applyLoading: false,
      applyOpen: false,
      applyForm: {},
      applyRules: {
        refund: [
          { required: true, message: '退款金额不能为空', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (value == '') {
                callback(new Error('退款金额不能为空'))
              } else {
                const reg = /^(([1-9]{1}\d*)|(0{1}))(\.\d{1,2})?$/
                if (!reg.test(value) && value != '') {
                  callback(new Error('请输入正确的退款金额，最多两位小数'))
                } else {
                  if (value > this.balance) {
                    callback(new Error(`当前余额为${this.balance}元，请输入不大于余额的金额`))
                  } else if (value > parseFloat((this.viewInfo.amount - this.viewInfo.refund).toFixed(12))) {
                    callback(new Error(`当前订单可退金额为${parseFloat((this.viewInfo.amount - this.viewInfo.refund).toFixed(12))}元`))
                  } else {
                    callback()
                  }
                }
              }
            },
            trigger: 'blur'
          }
        ]
      }
    }
  },
  created() {
    this.getBalance()
    this.getList()
  },
  methods: {
    // 退款渠道格式化
    channelFormat(row) {
      let channel
      switch (row.channel) {
        case 'ORIGINAL':
          channel = '原路退款'
          break
        case 'BALANCE':
          channel = '退回到余额'
          break
        case 'OTHER_BALANCE':
          channel = '原账户异常退到其他余额账户'
          break
        case 'OTHER_BANKCARD':
          channel = '原银行卡异常退到其他银行卡'
          break
        default:
          channel = '原路退款'
          break
      }
      return channel
    },
    // 退款状态格式化
    statusFormat(row) {
      let status
      switch (row.status) {
        case 'SUCCESS':
          status = '退款成功'
          break
        case 'CLOSED':
          status = '退款关闭'
          break
        case 'PROCESSING':
          status = '退款处理中'
          break
        case 'ABNORMAL':
          status = '退款异常'
          break
        default:
          status = '退款成功'
          break
      }
      return status
    },
    // 查询余额
    getBalance() {
      walletBalance().then((res) => {
        const { code, msg, data } = res
        if (code === 200) {
          this.balance = data.toFixed(2)
        } else {
          this.$message.error(msg)
        }
      })
    },
    // 查询列表
    getList() {
      this.loading = true
      walletLogList(this.queryParams).then((res) => {
        const { code, msg, rows, total } = res
        if (code === 200) {
          this.dataList = rows
          this.total = total
          this.loading = false
          if (this.timers) clearInterval(this.timers)
        } else {
          this.$message.error(msg)
        }
      })
    },
    // 改变选项
    handleChange(e) {
      this.queryParams.logType = e.value
      this.getList()
    },
    // 打开充值
    handleRecharge() {
      this.amount = 100
      this.amountChecked = 100
      this.open = true
    },
    // 取消充值
    handleCancle() {
      this.open = false
    },
    // 选中充值金额
    handleChangeAmount(e) {
      this.amount = e
      if (e < 0) {
        this.amount = undefined
        this.$refs.amount.focus()
      }
    },
    // 请求充值接口
    handleGetQRCode() {
      if (this.amount) {
        walletRecharge({ amount: this.amount }).then((res) => {
          if (res.code === 200) {
            this.qrcodeText = res.data.codeUrl
            this.qrcodeImage = require('@/assets/logo/Frame.png')
            this.qrcodeOpen = true
            if (this.timers) clearInterval(this.timers)
            const serialNum = res.data.serialNum
            this.getOrderState(serialNum)
          } else {
            this.$message.error(res.msg)
          }
        })
      }
    },
    // 查询充值是否成功
    getOrderState(serialNum) {
      let num = 0
      this.timers = setInterval(() => {
        num++
        walletRechargeOrder({ serialNum }).then((res) => {
          if (res.code === 200) {
            if (res.data === 'success') {
              this.$message.success('充值成功')
              this.qrcodeOpen = false
              this.open = false
              this.getList()
              this.getBalance()
              clearInterval(this.timers)
            }
          } else {
            this.$message.error(res.msg)
          }
        })
        if (num == 500) {
          this.$message.info('时间超时，请重新进行充值！')
          this.qrcodeOpen = false
          clearInterval(this.timers)
        }
      }, 1500)
    },
    // 查看详情
    handleView(row) {
      const serialNum = row.serialNum
      walletOrderDetail({ serialNum }).then((res) => {
        if (res.code === 200) {
          this.viewInfo = res.data
          this.viewOpen = true
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 申请退款
    handleOpenRefund(row) {
      this.applyForm = {
        serialNum: row.outTradeNo,
        refund: undefined
      }
      this.resetForm('applyForm')
      this.applyLoading = false
      this.applyOpen = true
    },
    // 申请退款提交
    handleRefund() {
      this.$refs['applyForm'].validate((valid) => {
        if (valid) {
          this.applyLoading = true
          walletRefund(this.applyForm).then((res) => {
            if (res.code === 200) {
              if (this.timers) clearInterval(this.timers)
              const outRefundNo = res.data.outRefundNo
              this.getRefundState(outRefundNo)
            } else {
              this.$message.error(res.msg)
            }
          })
        }
      })
    },
    // 查询退款是否成功
    getRefundState(outRefundNo) {
      let num = 0
      this.timers = setInterval(() => {
        num++
        walletRefundOrder({ outRefundNo }).then((res) => {
          if (res.code === 200) {
            if (res.msg === 'success') {
              this.$message.success('退款成功，已原路返回，具体到账时间以支付机构为准。')
              this.applyOpen = false
              this.viewOpen = false
              this.getList()
              this.getBalance()
              clearInterval(this.timers)
            }
          } else {
            this.$message.error(res.msg)
          }
        })
        if (num == 500) {
          this.$message.info('退款成功，已原路返回，具体到账时间以支付机构为准。')
          this.applyOpen = false
          this.viewOpen = false
          this.getList()
          this.getBalance()
          clearInterval(this.timers)
        }
      }, 1500)
    },
    // 退款明细
    handleRefundList(row) {
      const serialNum = row.outTradeNo
      this.refundOpen = true
      this.refundLoading = true
      walletRefundList({ serialNum }).then((res) => {
        if (res.code === 200) {
          this.refundData = res.data
          this.refundLoading = false
        } else {
          this.$message.error(res.msg)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .table-header {
  background-color: #f8f9fb;
  border: 1px solid #cbd6e2;
  th.el-table__cell {
    background-color: #f8f9fb;
  }
}
::v-deep .table-row {
  .table-cl333 {
    font-size: 14px;
    color: #333;
  }
  .table-cl999 {
    font-size: 12px;
    color: #999;
  }
  .table-orange {
    font-size: 14px;
    color: #f35d09;
  }
}
::v-deep .pagination-container {
  padding: 32px 20px !important;
  display: flex;
  align-items: center;
  .el-pagination {
    width: 100%;
    text-align: right;
    .el-pagination__total {
      float: left;
    }
  }
}
::v-deep .price-dialog {
  .el-dialog__body {
    padding: 10px 20px 30px;
    .price-title {
      font-size: 16px;
      padding-bottom: 10px;
      border-bottom: 1px solid #cbd6e2;
      margin-bottom: 10px;
    }
    .price-list {
      display: inline-flex;
      flex-wrap: wrap;
      justify-content: flex-start;
      .el-radio {
        margin-right: 20px;
        margin-bottom: 20px;
        margin-left: 0;
        width: calc((100% - 60px) / 4);
        &:nth-child(4n) {
          margin-right: 0;
        }
      }
      .price-input {
        &-tip {
          height: 100%;
          display: inline-flex;
          align-items: center;
        }
      }
    }
  }
}
</style>
