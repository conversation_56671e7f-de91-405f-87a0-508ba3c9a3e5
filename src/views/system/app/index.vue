<template>
  <div class="newBox bgcf9 vh-85">
    <div class="custom-search flex" style="padding-top: 18px">
      <el-button type="primary" icon="el-icon-plus" size="small" @click="handleAdd" v-hasPermi="['system:banner:add']">新增</el-button>
    </div>
    <div class="Box">
      <el-table v-loading="loading" ref="list" stripe :data="list" class="custom-table">
        <el-table-column label="图片地址" align="center" prop="photoAddress">
          <template slot-scope="scope">
            <div style="display: inline-flex; justify-content: center">
              <image-preview :src="scope.row.picture_oss || scope.row.picture" :width="50" :height="50" />
            </div>
          </template>
        </el-table-column>
        <el-table-column label="排序" align="center" prop="sort">
          <template slot-scope="{ row }">
            <el-input-number v-model="row.sort" @change="handleSortChange(row)" size="small" style="width: 100px" v-hasPermi="['system:banner:update']" />
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:banner:update']">修改</el-button>
            <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)" v-hasPermi="['system:banner:del']">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="custom-pagination">
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
      </div>
    </div>

    <el-dialog v-dialogDragBox :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="form.sort" :min="0" size="small"></el-input-number>
        </el-form-item>
        <el-form-item label="图片地址" prop="picture">
          <image-upload :compress="false" :limit="1" v-model="form.picture" />
        </el-form-item>
      </el-form>
      <div slot="footer">
        <button type="button" class="custom-dialog-btn small" @click="open = false">取消</button>
        <button type="button" class="custom-dialog-btn small primary" @click="submitForm">提交</button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getBannerList, addBanner, editBanner, deleteBanner } from '@/api/system/app'

export default {
  name: 'AppAd',
  data() {
    return {
      loading: true,
      total: 0,
      list: [],
      title: '',
      open: false,
      queryParams: {
        pageNum: 1,
        pageSize: 10
      },
      form: {},
      rules: {
        picture: [{ required: true, message: '请上传图片', trigger: 'blur' }]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.loading = true
      getBannerList(this.queryParams).then(res => {
        const { code, msg, rows, total } = res
        if (code === 200) {
          this.list = rows
          this.total = total
          this.loading = false
        } else this.$message.error(msg)
      })
    },
    // 刷新
    refresh() {
      getBannerList(this.queryParams).then(res => {
        const { code, msg, rows } = res
        if (code === 200) this.$set(this, 'list', rows)
        else this.$message.error(msg)
      })
    },
    // 表单重置
    reset() {
      // 取最后一项的sort值+1
      const sort = this.list.length > 0 ? this.list[this.list.length - 1].sort + 1 : 0
      this.form = {
        bannerId: undefined,
        picture: undefined,
        sort
      }
      this.resetForm('form')
    },
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加APP广告'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      this.form = { ...row }
      this.open = true
      this.title = '修改APP广告'
    },
    handleSortChange(row) {
      const data = { bannerId: row.id, sort: row.sort }
      editBanner(data).then(res => {
        this.$modal.msgSuccess('修改成功')
        this.refresh()
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            const { id, picture, sort } = this.form
            const data = { bannerId: id, picture, sort }
            editBanner(data).then(res => {
              this.$modal.msgSuccess('修改成功')
              this.open = false
              this.getList()
            })
          } else {
            addBanner(this.form).then(res => {
              this.$modal.msgSuccess('新增成功')
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    // prettier-ignore
    handleDelete(row) {
      const bannerId = row.id
      this.$modal.confirm('是否确认删除选中的数据项？').then(function () {
        return deleteBanner({ bannerId })
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess('删除成功')
      }).catch(() => {})
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
.Box {
  padding: 15px 20px;
}
</style>
