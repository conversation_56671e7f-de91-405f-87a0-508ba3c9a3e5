<template>
  <div class="newBox bgcf9 vh-85">
    <el-row :gutter="15" v-if="!showMore">
      <el-col :span="24">
        <div class="data" style="height: auto">
          <div class="data-title">预警通统计</div>
          <div class="esContainer">
            <div class="esBox">
              <el-popover trigger="click" popper-class="customPopover" class="esItem" v-for="item in esList" :key="item.index">
                <el-table :data="esLog" class="custom-table custom-table-cell5" stripe :show-header="false" max-height="180">
                  <el-table-column width="90" align="center" property="date" label="日期"></el-table-column>
                  <el-table-column property="count" align="center" label="数量"></el-table-column>
                </el-table>
                <div class="esItemBox" slot="reference" @click="getEsLog(item.index)">
                  <span class="esTitle">{{ formatEs(item.index) }}</span>
                  <b class="esNum">{{ item.count }}</b>
                </div>
              </el-popover>
            </div>
          </div>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="data">
          <div class="data-title" @click="showMoreInfo('demand')">
            <span>需求统计</span>
            <i class="el-icon-arrow-right"></i>
          </div>
          <div class="data-info">
            <div class="data-info-item">
              <div class="data-info-item-title"><span>需求总数(个)</span></div>
              <div class="data-info-item-num">
                <span>{{ demandData.totalDemand || 0 }}</span>
              </div>
            </div>
            <div class="data-info-item">
              <div class="data-info-item-title"><span>需求发布次数(次)</span></div>
              <div class="data-info-item-num">
                <span>{{ demandData.totalPublishCount || 0 }}</span>
              </div>
            </div>
          </div>
          <div class="data-chart">
            <div class="data-select border-none">
              <el-date-picker v-model="demandMonth" format="yyyy年MM月概览" value-format="yyyyMM" type="month" placeholder="请选择月份" style="width: 100%" :clearable="false" @change="getDemandStatistics" :picker-options="endDatePicker"></el-date-picker>
            </div>
            <div class="data-chart-info">
              <line-chart :chart-data="demandFormat(demandData)" />
            </div>
          </div>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="data">
          <div class="data-title" @click="showMoreInfo('contract')">
            <span>合同统计</span>
            <i class="el-icon-arrow-right"></i>
          </div>
          <div class="data-info">
            <div class="data-info-item inline">
              <div class="data-info-item-title">合同总数</div>
              <div class="data-info-item-num">
                <span>{{ contractData.totalContract || 0 }}</span>
              </div>
            </div>
            <div class="data-info-item">
              <div class="data-info-item-title">
                {{ contractMonth.slice(0, 4) + '年' + contractMonth.slice(4, 6) + '月合同总金额' }}
              </div>
              <div class="data-info-item-num">
                <span>{{ contractData.currentMonthContractAmount || 0 }}</span>
              </div>
            </div>
          </div>
          <div class="data-chart">
            <div class="data-select border-none">
              <el-date-picker v-model="contractMonth" format="yyyy年MM月概览" value-format="yyyyMM" type="month" placeholder="请选择月份" style="width: 100%" :clearable="false" @change="getContractStatistics" :picker-options="endDatePicker"></el-date-picker>
            </div>
            <div class="data-chart-info">
              <line-chart :chart-data="contractFormat(contractData)" />
            </div>
          </div>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="data height-520" @click="showMoreInfo('platform')">
          <div class="data-title">
            <span>平台成交统计</span>
            <i class="el-icon-arrow-right"></i>
          </div>
          <div class="data-info">
            <div class="data-info-item">
              <div class="data-info-item-title"><span>成交总数</span></div>
              <div class="data-info-item-num">
                <span>{{ platformData.totalDeal.total }}</span>
              </div>
            </div>
            <div class="data-info-item">
              <div class="data-info-item-title"><span>成交总金额</span></div>
              <div class="data-info-item-num orange">
                <span>{{ Math.round(platformData.totalDeal.amount) }}</span>
              </div>
            </div>
          </div>
          <div class="data-chart">
            <div class="data-select">
              <el-date-picker v-model="platformMonth" format="yyyy年MM月概览" value-format="yyyyMM" type="month" placeholder="请选择月份" style="width: 100%" :clearable="false" @change="getPlatformStatistics" :picker-options="endDatePicker"></el-date-picker>
            </div>
            <div class="data-chart-info">
              <ycross-bar-chart :chart-data="platformFormat(platformData)" />
            </div>
          </div>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="data height-520">
          <div class="data-title" @click="showMoreInfo('user')">
            <span>注册用户统计</span>
            <i class="el-icon-arrow-right"></i>
          </div>
          <div class="data-info">
            <div class="data-info-item position-relative inline">
              <div class="data-info-item-tab">
                <span @click="userActive = 'user'" :class="{ active: userActive === 'user' }">普通用户</span>
                <span @click="userActive = 'company'" :class="{ active: userActive === 'company' }">企业用户</span>
                <span @click="userActive = 'personal'" :class="{ active: userActive === 'personal' }">个人用户</span>
              </div>
              <div class="data-info-item-title"><span>用户总数</span></div>
              <div class="data-info-item-num">
                <span>{{ userActive === 'user' ? registerData.totalUser : userActive === 'company' ? registerData.totalCompany : registerData.totalPersonalUser }}</span>
              </div>
            </div>
            <div class="data-info-item inline" v-if="userActive !== 'personal'">
              <template v-if="userActive === 'user'">
                <div class="data-info-item-title">
                  <span>比上月增长</span>
                  <i class="el-icon-caret-bottom"></i>
                </div>
                <div class="data-info-item-num" :class="getIsIncrease(registerData.currentMonthUserCount || 0, registerData.preMonthUserCount || 0) === 1 ? 'green' : getIsIncrease(registerData.currentMonthUserCount || 0, registerData.preMonthUserCount || 0) === -1 ? 'red' : 'orange'">
                  <i :class="getIsIncrease(registerData.currentMonthUserCount || 0, registerData.preMonthUserCount || 0) === 1 ? 'el-icon-top' : getIsIncrease(registerData.currentMonthUserCount || 0, registerData.preMonthUserCount || 0) === -1 ? 'el-icon-bottom' : ''"></i>
                  <span>{{ getPercent(registerData.currentMonthUserCount || 0, registerData.preMonthUserCount || 0) > 0 ? getPercent(registerData.currentMonthUserCount || 0, registerData.preMonthUserCount || 0) + '%' : '持平' }}</span>
                </div>
              </template>
              <template v-if="userActive === 'company'">
                <div class="data-info-item-title">
                  <span>比上月增长</span>
                  <i class="el-icon-caret-bottom"></i>
                </div>
                <div class="data-info-item-num" :class="getIsIncrease(registerData.currentMonthCompanyCount || 0, registerData.preMonthCompanyCount || 0) === 1 ? 'green' : getIsIncrease(registerData.currentMonthCompanyCount || 0, registerData.preMonthCompanyCount || 0) === -1 ? 'red' : 'orange'">
                  <i :class="getIsIncrease(registerData.currentMonthCompanyCount || 0, registerData.preMonthCompanyCount || 0) === 1 ? 'el-icon-top' : getIsIncrease(registerData.currentMonthCompanyCount || 0, registerData.preMonthCompanyCount || 0) === -1 ? 'el-icon-bottom' : ''"></i>
                  <span>{{ getPercent(registerData.currentMonthCompanyCount || 0, registerData.preMonthCompanyCount || 0) > 0 ? getPercent(registerData.currentMonthCompanyCount || 0, registerData.preMonthCompanyCount || 0) + '%' : '持平' }}</span>
                </div>
              </template>
            </div>
          </div>
          <div class="data-chart">
            <div class="data-select">
              <el-date-picker v-model="registerMonth" format="yyyy年MM月概览" value-format="yyyyMM" type="month" placeholder="请选择月份" style="width: 100%" :clearable="false" @change="getRegisterStatistics" :picker-options="endDatePicker"></el-date-picker>
            </div>
            <div class="data-chart-info">
              <portrait-bar-chart :chart-data="registerFormat(registerData)" />
            </div>
          </div>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="data height-520">
          <div class="data-title" @click="showMoreInfo('offer')">
            <span>报价统计</span>
            <i class="el-icon-arrow-right"></i>
          </div>
          <div class="data-info">
            <div class="data-info-item">
              <div class="data-info-item-title"><span>报价总数量</span></div>
              <div class="data-info-item-num">
                <span>{{ offerData.totalQuote }}</span>
              </div>
            </div>
            <div class="data-info-item">
              <div class="data-info-item-title">
                <span>{{ offerMonth.slice(0, 4) + '年' + offerMonth.slice(4, 6) + '月报价数量' }}</span>
              </div>
              <div class="data-info-item-num">
                <span>{{ offerData.currentMonthQuoteCount }}</span>
              </div>
            </div>
          </div>
          <div class="data-chart">
            <div class="data-select">
              <el-date-picker v-model="offerMonth" format="yyyy年MM月概览" value-format="yyyyMM" type="month" placeholder="请选择月份" style="width: 100%" :clearable="false" @change="getOfferStatistics" :picker-options="endDatePicker"></el-date-picker>
            </div>
            <div class="data-chart-info">
              <cross-bar-chart :chart-data="offerFormat(offerData)" />
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
    <demand-data ref="demandData" @callBack="hideMoreInfo" v-if="showMore && showType === 'demand'" />
    <contract-data ref="contractData" @callBack="hideMoreInfo" v-if="showMore && showType === 'contract'" />
    <platform-data ref="platformData" @callBack="hideMoreInfo" v-if="showMore && showType === 'platform'" />
    <user-data ref="userData" @callBack="hideMoreInfo" v-if="showMore && showType === 'user'" />
    <offer-data ref="offerData" @callBack="hideMoreInfo" v-if="showMore && showType === 'offer'" />
  </div>
</template>
<script>
import LineChart from '@/views/data/lineChart'
import crossBarChart from '@/views/data/crossBarChart'
import ycrossBarChart from '@/views/data/ycrossBarChart'
import portraitBarChart from '@/views/data/portraitBarChart'
import { contractStatistics, dealStatistics, demandStatistics, quoteStatistics, userStatistics } from '@/api/data'
import { parseTime, getPercent, getIsIncrease } from '@/utils/ruoyi'
import demandData from '@/views/data/demand/index'
import contractData from '@/views/data/contract/index'
import platformData from '@/views/data/platform/index'
import userData from '@/views/data/user/index'
import offerData from '@/views/data/offer/index'
import { countIndex, indexHistory } from '@/api/es'

export default {
  name: 'Sysdata',
  components: { LineChart, crossBarChart, ycrossBarChart, portraitBarChart, demandData, contractData, platformData, userData, offerData },
  data() {
    return {
      // 需求统计
      demandData: {},
      demandMonth: undefined,
      // 合同统计
      contractData: {},
      contractMonth: undefined,
      // 平台成交统计
      platformData: {
        totalDeal: { total: 0, amount: 0 }
      },
      platformMonth: undefined,
      // 注册用户统计
      registerData: {},
      registerMonth: undefined,
      userActive: 'user',
      // 报价统计
      offerData: {},
      offerMonth: undefined,
      // 显示更多
      showMore: false,
      showType: undefined,
      nowMonth: parseTime(new Date(), '{y}{m}'),
      nowDay: parseTime(new Date(), '{d}'),
      endDatePicker: {
        disabledDate: time => {
          return time.getTime() > new Date().getTime()
        }
      },
      esList: [],
      esLog: [],
      esOptions: [
        { index: 'executed_person', label: '被执行人' },
        { index: 'bidding', label: '招投标' },
        { index: 'financial_penalties', label: '金融监管' },
        { index: 'guaranteed_other', label: '获得担保' },
        { index: 'company_info', label: '工商信息' },
        { index: 'guarantor', label: '担保人' },
        { index: 'provide_guarantee_other', label: '提供担保' },
        { index: 'court_announcement', label: '开庭公告' },
        { index: 'supplier', label: '供应商' },
        { index: 'delivery_announcement', label: '送达公告' },
        { index: 'annual', label: '企业年报' },
        { index: 'other_rating_list', label: '其他领域信评' },
        { index: 'restraining_orders', label: '限制高消费' },
        { index: 'right_list', label: '专利' },
        { index: 'disciplinary_action', label: '纪律处分' },
        { index: 'manager_info', label: '高管信息' },
        { index: 'paper_list', label: '作品著作权' },
        { index: 'guaranteed_zhaiquan', label: '债权担保' },
        { index: 'administrative_penalties', label: '行政处罚' },
        { index: 'provide_guarantee_zhaiquan', label: '提供债券担保' },
        { index: 'filing_information', label: '立案信息' },
        { index: 'chattel_mortgage', label: '动产抵押' },
        { index: 'administrative_regulatory_measures', label: '行政监管措施' },
        { index: 'child_company', label: '控股子公司' },
        { index: 'regulatory_action', label: '自律监管措施' },
        { index: 'land_data', label: '土地信息' },
        { index: 'suspicious_relationship', label: '疑似关系' },
        { index: 'list_qualifications', label: '榜单资质' },
        { index: 'final_case', label: '终本案件' },
        { index: 'news', label: '新闻公告' },
        { index: 'legal_announcement', label: '法院公告' },
        { index: 'soft_list', label: '软件著作权' },
        { index: 'holder', label: '股东信息' },
        { index: 'equity_penetration', label: '股权穿透' },
        { index: 'business_change', label: '工商变更' },
        { index: 'guarantee_company', label: '担保企业' },
        { index: 'property_clues', label: '财产线索' },
        { index: 'branch_info', label: '分支机构' },
        { index: 'main_business', label: '未知' },
        { index: 'tax_rating', label: '纳税信用等级' },
        { index: 'dishonest_debtor', label: '失信被执行人' },
        { index: 'customer', label: '客户' }
      ]
    }
  },
  created() {
    this.init()
    this.getEsData()
  },
  methods: {
    // 查询es数据
    getEsData() {
      countIndex().then(res => {
        const { code, data, msg } = res
        if (code === 200) this.esList = data
        else this.$message.error(msg)
      })
    },
    // 查询es历史
    getEsLog(index) {
      this.esLog = []
      indexHistory({ index }).then(res => {
        const { code, data, msg } = res
        if (code === 200) this.esLog = data
        else this.$message.error(msg)
      })
    },
    // 回显es
    formatEs(index) {
      const obj = this.esOptions.find(item => item.index === index)
      return obj?.label || ''
    },
    parseTime,
    getPercent,
    getIsIncrease,
    init() {
      const now = parseTime(new Date(), '{y}{m}')
      this.demandMonth = this.contractMonth = this.platformMonth = this.registerMonth = this.offerMonth = now
      this.getDemandStatistics()
      this.getContractStatistics()
      this.getPlatformStatistics()
      this.getRegisterStatistics()
      this.getOfferStatistics()
    },
    // 格式化需求统计折线图数据
    demandFormat(data = {}) {
      let { demandMonthData = [], preDemandMonthData = [] } = data
      if (demandMonthData.length > preDemandMonthData.length) demandMonthData.pop()
      if (demandMonthData.length < preDemandMonthData.length) preDemandMonthData.pop()
      let oneData = { series: [], name: undefined }
      let twoData = { series: [], name: undefined }
      oneData.name = '本月需求'
      twoData.name = '上月需求'
      oneData.series = demandMonthData.map(item => item.yc) || []
      twoData.series = preDemandMonthData.map(item => item.yc) || []
      let xAxis = demandMonthData.map(item => item.day) || []
      const { demandMonth, nowMonth, nowDay } = this
      if (demandMonth == nowMonth) {
        xAxis = xAxis.slice(0, nowDay)
        oneData.series = oneData.series.slice(0, nowDay)
        twoData.series = twoData.series.slice(0, nowDay)
      }
      return { xAxis, oneData, twoData, legend: [oneData.name, twoData.name] }
    },
    // 格式化合同统计折线图数据
    contractFormat(data = {}) {
      let { contractMonthData = [], preContractMonthData = [] } = data
      if (contractMonthData.length > preContractMonthData.length) contractMonthData.pop()
      if (contractMonthData.length < preContractMonthData.length) preContractMonthData.pop()
      let oneData = { series: [], name: undefined }
      let twoData = { series: [], name: undefined }
      oneData.name = '本月合同'
      twoData.name = '上月合同'
      oneData.series = contractMonthData.map(item => item.yc) || []
      twoData.series = preContractMonthData.map(item => item.yc) || []
      let xAxis = contractMonthData.map(item => item.day) || []
      const { contractMonth, nowMonth, nowDay } = this
      if (contractMonth == nowMonth) {
        xAxis = xAxis.slice(0, nowDay)
        oneData.series = oneData.series.slice(0, nowDay)
        twoData.series = twoData.series.slice(0, nowDay)
      }
      return { xAxis, oneData, twoData, legend: [oneData.name, twoData.name] }
    },
    // 格式化平台成交统计柱状图数据
    platformFormat(data = {}) {
      let { DealMonthData = [], preDealMonthData = [] } = data
      if (DealMonthData.length > preDealMonthData.length) DealMonthData.pop()
      if (DealMonthData.length < preDealMonthData.length) preDealMonthData.pop()
      let oneData = { series: [], name: undefined }
      let twoData = { series: [], name: undefined }
      oneData.name = '本月成交'
      twoData.name = '上月成交'
      oneData.series = DealMonthData.map(item => item.yc) || []
      twoData.series = preDealMonthData.map(item => item.yc) || []
      let yAxis = DealMonthData.map(item => item.day) || []
      const { platformMonth, nowMonth, nowDay } = this
      if (platformMonth == nowMonth) {
        yAxis = yAxis.slice(0, nowDay)
        oneData.series = oneData.series.slice(0, nowDay)
        twoData.series = twoData.series.slice(0, nowDay)
      }
      return { yAxis, oneData, twoData }
    },
    // 格式化注册用户统计柱状图数据
    registerFormat(data = {}) {
      let { userData = [], companyData = [] } = data
      if (this.userActive === 'user') {
        let chartData = { series: [], name: undefined }
        chartData.name = '普通用户'
        chartData.series = userData.map(item => item.yc) || []
        let xAxis = userData.map(item => item.day) || []
        const { registerMonth, nowMonth, nowDay } = this
        if (registerMonth == nowMonth) {
          xAxis = xAxis.slice(0, nowDay)
          chartData.series = chartData.series.slice(0, nowDay)
        }
        return { xAxis, chartData }
      } else {
        let chartData = { series: [], name: undefined }
        chartData.name = '企业用户'
        chartData.series = companyData.map(item => item.yc) || []
        let xAxis = companyData.map(item => item.day) || []
        const { registerMonth, nowMonth, nowDay } = this
        if (registerMonth == nowMonth) {
          xAxis = xAxis.slice(0, nowDay)
          chartData.series = chartData.series.slice(0, nowDay)
        }
        return { xAxis, chartData }
      }
    },
    // 格式化报价统计柱状图数据
    offerFormat(data = {}) {
      let { quoteMonthData = [], preMonthData = [] } = data
      if (quoteMonthData.length > preMonthData.length) quoteMonthData.pop()
      if (quoteMonthData.length < preMonthData.length) preMonthData.pop()
      let oneData = { series: [], name: undefined }
      let twoData = { series: [], name: undefined }
      oneData.name = '本月报价'
      twoData.name = '上月报价'
      oneData.series = quoteMonthData.map(item => item.yc) || []
      twoData.series = preMonthData.map(item => item.yc) || []
      let yAxis = quoteMonthData.map(item => item.day) || []
      const { offerMonth, nowMonth, nowDay } = this
      if (offerMonth == nowMonth) {
        yAxis = yAxis.slice(0, nowDay)
        oneData.series = oneData.series.slice(0, nowDay)
        twoData.series = twoData.series.slice(0, nowDay)
      }
      return { yAxis, oneData, twoData }
    },
    // 需求统计
    getDemandStatistics() {
      demandStatistics({ month: this.demandMonth }).then(res => {
        const { code, msg, data } = res
        if (code === 200) this.demandData = data
        else this.$message.error(msg)
      })
    },
    // 合同统计
    getContractStatistics() {
      contractStatistics({ month: this.contractMonth }).then(res => {
        const { code, msg, data } = res
        if (code === 200) this.contractData = data
        else this.$message.error(msg)
      })
    },
    // 平台成交统计
    getPlatformStatistics() {
      dealStatistics({ month: this.platformMonth }).then(res => {
        const { code, msg, data } = res
        if (code === 200) this.platformData = data
        else this.$message.error(msg)
      })
    },
    // 注册用户统计
    getRegisterStatistics() {
      userStatistics({ month: this.registerMonth }).then(res => {
        const { code, msg, data } = res
        if (code === 200) this.registerData = data
        else this.$message.error(msg)
      })
    },
    // 报价统计
    getOfferStatistics() {
      quoteStatistics({ month: this.offerMonth }).then(res => {
        const { code, msg, data } = res
        if (code === 200) this.offerData = data
        else this.$message.error(msg)
      })
    },
    // 显示更多
    showMoreInfo(type = 'demand') {
      this.showMore = true
      this.showType = type
    },
    // 隐藏更多
    hideMoreInfo() {
      this.showMore = false
      this.showType = undefined
    }
  }
}
</script>
<style scoped lang="scss">
@import '~@/assets/styles/custom-chart.scss';
.newBox {
  padding: 20px 20px 0;
}
.esContainer {
  padding: 15px 20px 0;
}
.esBox {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  .esItem {
    width: calc(100% / 14);
    border: 1px solid #eeeeee;
    margin: 0 -1px -1px 0;
    padding: 20px 0;
    transition: all 0.3s;
    cursor: pointer;
    &:hover {
      background-color: #f9f9f9;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
    }
    .esItemBox {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      .esTitle {
        font-size: 12px;
        color: #999999;
      }
      .esNum {
        font-weight: normal;
        font-size: 16px;
        margin-top: 10px;
      }
    }
  }
}
.custom-table {
  border: none;
  .el-table__body-wrapper {
    border: none;
  }
}
</style>
<style>
.customPopover {
  padding: 0;
}
</style>
