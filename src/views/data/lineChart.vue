<template>
  <div :class="className" :style="{ height: height, width: width }" />
</template>

<script>
import * as echarts from 'echarts'

require('echarts/theme/macarons')
import resize from '@/views/dashboard/mixins/resize'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '240px'
    },
    autoResize: {
      type: Boolean,
      default: true
    },
    chartData: {
      type: Object,
      required: true
    },
    gridRight:{
      type: Number,
      default: 20
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        this.setOptions(val)
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el, 'macarons')
      this.setOptions(this.chartData)
    },
    setOptions(data = {}) {
      if (!this.chart) return
      const { xAxis = [], oneData = { name: undefined, series: [] }, twoData = { name: undefined, series: [] }, legend = [] } = data
      this.chart.setOption({
        xAxis: {
          data: xAxis || ['10-01', '10-05', '10-10', '10-15', '10-20', '10-25', '10-30'],
          boundaryGap: false,
          axisLine: {
            lineStyle: {
              color: '#666'
            }
          },
          axisTick: {
            show: false
          }
        },
        grid: {
          left: 0,
          right: this.gridRight,
          bottom: 0,
          top: 50,
          containLabel: true
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          },
          padding: [5, 10]
        },
        yAxis: {
          axisLine: {
            lineStyle: {
              color: '#666'
            }
          },
          axisTick: {
            show: false
          }
        },
        legend: {
          top: '6',
          data: legend || ['本月数据', '上月数据']
        },
        series: [
          {
            type: 'line',
            name: oneData.name || '本月数据',
            itemStyle: {
              color: '#2E73F3'
            },
            lineStyle: {
              color: '#2E73F3',
              width: 2,
              type: 'dashed'
            },
            data: oneData.series || [820, 932, 901, 934, 1290, 1330, 1320],
            animationDuration: 2800,
            animationEasing: 'cubicInOut'
          },
          {
            type: 'line',
            name: twoData.name || '上月数据',
            itemStyle: {
              color: '#2E73F3'
            },
            lineStyle: {
              color: '#2E73F3',
              width: 2
            },
            data: twoData.series || [750, 830, 1080, 844, 1240, 1380, 1345],
            animationDuration: 2800,
            animationEasing: 'quadraticOut'
          }
        ]
      })
    }
  }
}
</script>
