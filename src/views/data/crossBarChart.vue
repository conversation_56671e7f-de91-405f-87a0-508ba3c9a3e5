<template>
  <div :class="className" :style="{ height: height, width: width }" />
</template>

<script>
import * as echarts from 'echarts'

require('echarts/theme/macarons')
import resize from '@/views/dashboard/mixins/resize'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '345px'
    },
    autoResize: {
      type: Boolean,
      default: true
    },
    chartData: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        this.setOptions(val)
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el, 'macarons')
      this.setOptions(this.chartData)
    },
    setOptions(data = {}) {
      if (!this.chart) return
      const { yAxis = [], oneData = { name: undefined, series: [] }, twoData = { name: undefined, series: [] } } = data
      this.chart.setOption({
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          top: 6
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          show: false
        },
        yAxis: {
          type: 'category',
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            color: '#666666'
          },
          data: yAxis || ['10-01', '10-02', '10-03', '10-04', '10-05', '10-06', '10-07', '10-08', '10-09', '10-10', '10-11', '10-12', '10-13', '10-14', '10-15', '10-16', '10-17', '10-18', '10-19', '10-20', '10-21', '10-22', '10-23', '10-24', '10-25', '10-26', '10-27', '10-28', '10-29', '10-30', '10-31']
        },
        series: [
          {
            name: oneData.name || '本月报价',
            type: 'bar',
            itemStyle: {
              color: '#2E73F3',
              borderRadius: 5
            },
            showBackground: true,
            backgroundStyle: {
              color: '#EAF0FC',
              borderWidth: 5
            },
            data: oneData.series || [18203, 23489, 29034, 104970, 131744, 630230, 18203, 23489, 29034, 104970, 131744, 630230, 18203, 23489, 29034, 104970, 131744, 630230, 18203, 23489, 29034, 104970, 131744, 630230, 18203, 23489, 29034, 104970, 131744, 630230, 18203]
          },
          {
            name: twoData.name || '上月报价',
            type: 'bar',
            itemStyle: {
              color: '#93B8FD',
              borderRadius: 5
            },
            showBackground: true,
            backgroundStyle: {
              color: '#EAF0FC',
              borderWidth: 5
            },
            data: twoData.series || [19325, 23438, 31000, 121594, 134141, 681807, 19325, 23438, 31000, 121594, 134141, 681807, 19325, 23438, 31000, 121594, 134141, 681807, 19325, 23438, 31000, 121594, 134141, 681807, 19325, 23438, 31000, 121594, 134141, 681807, 19325]
          }
        ]
      })
    }
  }
}
</script>
