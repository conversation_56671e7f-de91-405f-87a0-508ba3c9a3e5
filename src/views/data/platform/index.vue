<template>
  <div class="moreBox">
    <el-page-header @back="goBack" content="平台成交统计"></el-page-header>
    <el-row :gutter="0">
      <el-col :span="12">
        <div class="moreBox-info">
          <div class="moreBox-info-item">
            <div class="item-title">{{ platformMonth.slice(0, 4) + '年' + platformMonth.slice(4, 6) + '月成交数量' }}</div>
            <div class="item-num">{{ platformData.currentMonthDealCount || 0 }}</div>
            <div class="item-bg gray"></div>
            <div class="item-contrast">
              <div class="c666">
                <span>对比上个月</span>
                <i class="el-icon-caret-bottom"></i>
              </div>
              <div :class="platformIsIncrease(platformData) === 1 ? 'green' : platformIsIncrease(platformData) === -1 ? 'red' : 'orange'">
                <i class="el-icon-top" v-if="platformIsIncrease(platformData) === 1"></i>
                <i class="el-icon-bottom" v-if="platformIsIncrease(platformData) === -1"></i>
                <span>{{ platformContrast(platformData) === 0 ? '持平' : platformContrast(platformData) + '%' }}</span>
              </div>
            </div>
          </div>
          <div class="moreBox-info-item">
            <div class="item-title">{{ platformMonth.slice(0, 4) + '年' + platformMonth.slice(4, 6) + '月成交金额' }}</div>
            <div class="item-num orange">{{ Math.round(platformData.currentMonthDealAmount) || 0 }}</div>
            <div class="item-bg gray"></div>
            <div class="item-contrast">
              <div class="c666">
                <span>对比上个月</span>
                <i class="el-icon-caret-bottom"></i>
              </div>
              <div :class="platformIsIncrease(platformData, 'mount') === 1 ? 'green' : platformIsIncrease(platformData, 'mount') === -1 ? 'red' : 'orange'">
                <i class="el-icon-top" v-if="platformIsIncrease(platformData, 'mount') === 1"></i>
                <i class="el-icon-bottom" v-if="platformIsIncrease(platformData, 'mount') === -1"></i>
                <span>{{ platformContrast(platformData, 'mount') === 0 ? '持平' : platformContrast(platformData, 'mount') + '%' }}</span>
              </div>
            </div>
          </div>
        </div>
        <div class="data-chart">
          <div class="data-select border-none">
            <el-date-picker v-model="platformMonth" format="yyyy年MM月概览" value-format="yyyyMM" type="month" placeholder="请选择月份" style="width: 100%" :clearable="false" @change="getPlatformStatistics" :picker-options="endDatePicker"></el-date-picker>
          </div>
          <div class="data-chart-info">
            <ycross-bar-chart height="600px" :chart-data="platformFormat(platformData)" />
          </div>
        </div>
      </el-col>
      <el-col :span="12" v-if="topList.length">
        <div class="moreBox-list">
          <div class="moreBox-list-title">
            <span class="title">成交量TOP5</span>
            <div class="data-select border-none relative">
              <el-date-picker v-model="platformMonth" format="yyyy年MM月概览" value-format="yyyyMM" type="month" placeholder="请选择月份" style="width: 100%" :clearable="false" @change="getPlatformStatistics" :picker-options="endDatePicker"></el-date-picker>
            </div>
          </div>
          <div class="moreBox-list-top">
            <div class="top-item" v-for="(item, index) in topList" :key="index">
              <b class="top-item-title">{{ item.companyName }}</b>
              <span class="top-item-desc">{{ '成交量 ' + (item.count || 0) }}</span>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import { dealStatistics } from '@/api/data'
import { parseTime, getPercent, getIsIncrease } from '@/utils/ruoyi'
import ycrossBarChart from '@/views/data/ycrossBarChart'

export default {
  components: { ycrossBarChart },
  data() {
    return {
      platformMonth: parseTime(new Date(), '{y}{m}'),
      platformData: {
        totalDeal: { total: 0, amount: 0 }
      },
      topList: [],
      nowMonth: parseTime(new Date(), '{y}{m}'),
      nowDay: parseTime(new Date(), '{d}'),
      endDatePicker: {
        disabledDate: time => {
          return time.getTime() > new Date().getTime()
        }
      }
    }
  },
  created() {
    this.getPlatformStatistics()
  },
  methods: {
    parseTime,
    getPercent,
    getIsIncrease,
    // 平台成交统计
    getPlatformStatistics() {
      dealStatistics({ month: this.platformMonth }).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          this.platformData = data
          this.topList = data.companyTop5Data || []
        } else this.$message.error(msg)
      })
    },
    // 格式化平台成交统计折线图数据
    platformFormat(data = {}) {
      let { DealMonthData = [], preDealMonthData = [] } = data
      if (DealMonthData.length > preDealMonthData.length) DealMonthData.pop()
      if (DealMonthData.length < preDealMonthData.length) preDealMonthData.pop()
      let oneData = { series: [], name: undefined }
      let twoData = { series: [], name: undefined }
      oneData.name = '本月成交'
      twoData.name = '上月成交'
      oneData.series = DealMonthData.map(item => item.yc) || []
      twoData.series = preDealMonthData.map(item => item.yc) || []
      let yAxis = DealMonthData.map(item => item.day) || []
      const { platformMonth, nowMonth, nowDay } = this
      if (platformMonth == nowMonth) {
        yAxis = yAxis.slice(0, nowDay)
        oneData.series = oneData.series.slice(0, nowDay)
        twoData.series = twoData.series.slice(0, nowDay)
      }
      return { yAxis, oneData, twoData }
    },
    // 平台成交统计对比上个月
    platformContrast(data = {}, type = 'count') {
      const { currentMonthDealCount = 0, preMonthDealCount = 0, currentMonthDealAmount = 0, preMonthDealAmount = 0 } = data
      if (type === 'count') {
        return this.getPercent(currentMonthDealCount, preMonthDealCount)
      } else {
        return this.getPercent(currentMonthDealAmount, preMonthDealAmount)
      }
    },
    platformIsIncrease(data = {}, type = 'count') {
      const { currentMonthDealCount = 0, preMonthDealCount = 0, currentMonthDealAmount = 0, preMonthDealAmount = 0 } = data
      if (type === 'count') {
        return this.getIsIncrease(currentMonthDealCount, preMonthDealCount)
      } else {
        return this.getIsIncrease(currentMonthDealAmount, preMonthDealAmount)
      }
    },
    goBack() {
      this.$emit('callBack')
    }
  }
}
</script>
<style scoped lang="scss">
@import '~@/assets/styles/custom-chart.scss';
.moreBox {
  .data-chart {
    width: 100%;
    height: 600px;
    margin-top: 30px;
    margin-left: 0;
  }
}
</style>
