<template>
  <div class="moreBox">
    <el-page-header @back="goBack" content="需求统计"></el-page-header>
    <el-row :gutter="0">
      <el-col :span="12">
        <div class="moreBox-info">
          <div class="moreBox-info-item">
            <div class="item-title">{{ demandMonth.slice(0, 4) + '年' + demandMonth.slice(4, 6) }}需求个数</div>
            <div class="item-num">{{ demandData.currentMonthDemandCount || 0 }}</div>
            <div class="item-bg gray"></div>
            <div class="item-contrast">
              <div class="c666">
                <span>对比上个月</span>
                <i class="el-icon-caret-bottom"></i>
              </div>
              <div :class="demandIsIncrease(demandData) === 1 ? 'green' : demandIsIncrease(demandData) === -1 ? 'red' : 'orange'">
                <i class="el-icon-top" v-if="demandIsIncrease(demandData) === 1"></i>
                <i class="el-icon-bottom" v-if="demandIsIncrease(demandData) === -1"></i>
                <span>{{ demandContrast(demandData) === 0 ? '持平' : demandContrast(demandData) + '%' }}</span>
              </div>
            </div>
          </div>
          <div class="moreBox-info-item">
            <div class="item-title">{{ demandMonth.slice(0, 4) + '年' + demandMonth.slice(4, 6) }}需求发布次数</div>
            <div class="item-num">{{ totalCurrentMonthPublishCount || 0 }}</div>
            <div class="item-bg gray"></div>
            <div class="item-contrast">
              <div class="c666">
                <span>对比上个月</span>
                <i class="el-icon-caret-bottom"></i>
              </div>
              <div :class="demandIsIncrease(demandData, 'publish') === 1 ? 'green' : demandIsIncrease(demandData, 'publish') === -1 ? 'red' : 'orange'">
                <i class="el-icon-top" v-if="demandIsIncrease(demandData, 'publish') === 1"></i>
                <i class="el-icon-bottom" v-if="demandIsIncrease(demandData, 'publish') === -1"></i>
                <span>{{ demandContrast(demandData, 'publish') === 0 ? '持平' : demandContrast(demandData, 'publish') + '%' }}</span>
              </div>
            </div>
          </div>
        </div>
        <div class="data-chart">
          <div class="data-select border-none">
            <el-date-picker v-model="demandMonth" format="yyyy年MM月概览" value-format="yyyyMM" type="month" placeholder="请选择月份" style="width: 100%" :clearable="false" @change="getDemandStatistics" :picker-options="endDatePicker"></el-date-picker>
          </div>
          <div class="data-chart-info">
            <line-chart height="600px" :grid-right="20" :chart-data="demandFormat(demandData)" />
          </div>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="moreBox-list isTop">
          <div class="moreBox-list-title">
            <span class="title">公域/私域需求统计</span>
            <div class="data-select border-none relative">
              <el-date-picker v-model="demandMonth" format="yyyy年MM月概览" value-format="yyyyMM" type="month" placeholder="请选择月份" style="width: 100%" :clearable="false" @change="getDemandStatistics" :picker-options="endDatePicker"></el-date-picker>
            </div>
          </div>
          <div class="moreBox-list-chart flex align-center">
            <div class="chart-chart">
              <pie-chart :chart-data="demandPieFormat(demandData)" />
            </div>
            <div class="chart-info">
              <div class="chart-item border-bottom-0">
                <div class="chart-item-title color-b3ceff">公域需求</div>
                <div class="chart-item-info" :class="demandPublishIsIncrease(demandData) === 1 ? 'green' : demandPublishIsIncrease(demandData) === -1 ? 'red' : 'orange'">
                  <span class="title">同比上个月</span>
                  <i class="el-icon-top" v-if="demandPublishIsIncrease(demandData) === 1"></i>
                  <i class="el-icon-bottom" v-if="demandPublishIsIncrease(demandData) === -1"></i>
                  <span class="num">{{ demandPublishContrast(demandData) === 0 ? '持平' : demandPublishContrast(demandData) + '%' }}</span>
                </div>
              </div>
              <div class="chart-item border-bottom-0">
                <div class="chart-item-title color-ffe09c">私域需求</div>
                <div class="chart-item-info" :class="demandPublishIsIncrease(demandData, 'private') === 1 ? 'green' : demandPublishIsIncrease(demandData) === -1 ? 'red' : 'orange'">
                  <span class="title">同比上个月</span>
                  <i class="el-icon-top" v-if="demandPublishIsIncrease(demandData, 'private') === 1"></i>
                  <i class="el-icon-bottom" v-if="demandPublishIsIncrease(demandData, 'private') === -1"></i>
                  <span class="num">{{ demandPublishContrast(demandData, 'private') === 0 ? '持平' : demandPublishContrast(demandData, 'private') + '%' }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="moreBox-list isBottom">
          <div class="moreBox-list-title">
            <span class="title">需求总量TOP5</span>
            <div class="data-select border-none relative">
              <el-date-picker v-model="demandMonth" format="yyyy年MM月概览" value-format="yyyyMM" type="month" placeholder="请选择月份" style="width: 100%" :clearable="false" @change="getDemandStatistics" :picker-options="endDatePicker"></el-date-picker>
            </div>
          </div>
          <div class="moreBox-list-top">
            <div class="top-item" v-for="(item, index) in topList" :key="index">
              <b class="top-item-title">{{ item.companyName }}</b>
              <span class="top-item-desc">{{ '需求数量 ' + (item.count || 0) }}</span>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import lineChart from '@/views/data/lineChart'
import pieChart from '@/views/data/pieChart'
import { demandStatistics } from '@/api/data'
import { parseTime, getPercent, getIsIncrease } from '@/utils/ruoyi'

export default {
  components: { lineChart, pieChart },
  data() {
    return {
      demandMonth: parseTime(new Date(), '{y}{m}'),
      demandData: {},
      topList: [],
      nowMonth: parseTime(new Date(), '{y}{m}'),
      nowDay: parseTime(new Date(), '{d}'),
      endDatePicker: {
        disabledDate: time => {
          return time.getTime() > new Date().getTime()
        }
      }
    }
  },
  created() {
    this.getDemandStatistics()
  },
  computed: {
    totalCurrentMonthPublishCount() {
      const currentMonthPublishData = this.demandData.currentMonthPublishData || []
      return currentMonthPublishData.reduce((total, item) => total + item.count, 0)
    }
  },
  methods: {
    parseTime,
    getPercent,
    getIsIncrease,
    // 需求统计
    getDemandStatistics() {
      demandStatistics({ month: this.demandMonth }).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          this.demandData = data
          this.topList = data.companyTop5Data || []
        } else this.$message.error(msg)
      })
    },
    // 格式化需求统计折线图数据
    demandFormat(data = {}) {
      let { demandMonthData = [], preDemandMonthData = [] } = data
      if (demandMonthData.length > preDemandMonthData.length) demandMonthData.pop()
      if (demandMonthData.length < preDemandMonthData.length) preDemandMonthData.pop()
      let oneData = { series: [], name: undefined }
      let twoData = { series: [], name: undefined }
      oneData.name = '本月需求'
      twoData.name = '上月需求'
      oneData.series = demandMonthData.map(item => item.yc) || []
      twoData.series = preDemandMonthData.map(item => item.yc) || []
      let xAxis = demandMonthData.map(item => item.day) || []
      const { demandMonth, nowMonth, nowDay } = this
      if (demandMonth == nowMonth) {
        xAxis = xAxis.slice(0, nowDay)
        oneData.series = oneData.series.slice(0, nowDay)
        twoData.series = twoData.series.slice(0, nowDay)
      }
      return { xAxis, oneData, twoData, legend: [oneData.name, twoData.name] }
    },
    // 需求统计对比上个月
    demandContrast(data = {}, type = 'count') {
      const { currentMonthDemandCount = 0, preMonthDemandCount = 0, currentMonthPublishData = [], preMonthPublishData = [] } = data
      if (type === 'count') {
        return this.getPercent(currentMonthDemandCount, preMonthDemandCount)
      } else {
        let current = 0
        let pre = 0
        currentMonthPublishData.length && currentMonthPublishData.map(item => (current += item.count))
        preMonthPublishData.length && preMonthPublishData.map(item => (pre += item.count))
        return this.getPercent(current, pre)
      }
    },
    demandIsIncrease(data = {}, type = 'count') {
      const { currentMonthDemandCount = 0, preMonthDemandCount = 0, currentMonthPublishData = [], preMonthPublishData = [] } = data
      if (type === 'count') {
        return this.getIsIncrease(currentMonthDemandCount, preMonthDemandCount)
      } else {
        let current = 0
        let pre = 0
        currentMonthPublishData.length && currentMonthPublishData.map(item => (current += item.count))
        preMonthPublishData.length && preMonthPublishData.map(item => (pre += item.count))
        return this.getIsIncrease(current, pre)
      }
    },
    // 需求数量对比上个月百分比
    demandPublishContrast(data = {}, type = 'common') {
      const { currentMonthPublishData = [], preMonthPublishData = [] } = data
      let current = 0
      let pre = 0
      const currentMonth = currentMonthPublishData.length && currentMonthPublishData.find(item => item.publishWay === type)
      const preMonth = preMonthPublishData.length && preMonthPublishData.find(item => item.publishWay === type)
      current = currentMonth ? currentMonth.count : 0
      pre = preMonth ? preMonth.count : 0
      return this.getPercent(current, pre)
    },
    // 需求数量对比上个月是否增加
    demandPublishIsIncrease(data = {}, type = 'common') {
      const { currentMonthPublishData = [], preMonthPublishData = [] } = data
      let current = 0
      let pre = 0
      const currentMonth = currentMonthPublishData.length && currentMonthPublishData.find(item => item.publishWay === type)
      const preMonth = preMonthPublishData.length && preMonthPublishData.find(item => item.publishWay === type)
      current = currentMonth ? currentMonth.count : 0
      pre = preMonth ? preMonth.count : 0
      return this.getIsIncrease(current, pre)
    },
    // 格式化需求统计饼图数据
    demandPieFormat(data = {}) {
      const { currentMonthPublishData = [] } = data
      const commonData = currentMonthPublishData.find(item => item.publishWay === 'common') || { count: 0 }
      const privateData = currentMonthPublishData.find(item => item.publishWay === 'private') || { count: 0 }
      return {
        series: [
          { name: '公域需求', value: commonData.count || 0, itemStyle: { color: '#b3ceff' } },
          { name: '私域需求', value: privateData.count || 0, itemStyle: { color: '#FFE09C' } }
        ]
      }
    },
    goBack() {
      this.$emit('callBack')
    }
  }
}
</script>
<style scoped lang="scss">
@import '~@/assets/styles/custom-chart.scss';
.moreBox {
  .data-chart {
    width: 100%;
    height: 600px;
    margin-top: 30px;
    margin-left: 0;
  }
}
</style>
