<template>
  <div class="moreBox">
    <el-page-header @back="goBack" content="注册用户统计"></el-page-header>
    <el-row :gutter="0">
      <el-col :span="12">
        <div class="moreBox-info">
          <div class="moreBox-info-item">
            <div class="item-title">注册用户总数(个)</div>
            <div class="item-num">{{ userData.totalUser || 0 }}</div>
            <div class="item-bg gray"></div>
          </div>
          <div class="moreBox-info-item">
            <div class="item-title">注册企业总数(个)</div>
            <div class="item-num">{{ userData.totalCompany || 0 }}</div>
            <div class="item-bg gray"></div>
          </div>
        </div>
        <div class="data-chart">
          <div class="data-tab">
            <div class="data-tab-title">
              <span :class="{ active: userActive === 'user' }" @click="userActive = 'user'">普通用户</span>
              <span :class="{ active: userActive === 'company' }" @click="userActive = 'company'">企业用户</span>
              <span :class="{ active: userActive === 'personal' }" @click="userActive = 'personal'">个人用户</span>
            </div>
            <div class="data-tab-info">
              <div class="data-info-item" :style="{width:userActive === 'personal'?'100%':''}">
                <div class="data-info-item-title"><span>用户总数</span></div>
                <div class="data-info-item-num">
                  <span>{{ userActive === 'user' ? userData.totalUser : userData.totalCompany }}</span>
                </div>
              </div>
              <div class="data-info-item inline" v-if="userActive !== 'personal'">
                <template v-if="userActive === 'user'">
                  <div class="data-info-item-title">
                    <span>比上月增长</span>
                    <i class="el-icon-caret-bottom"></i>
                  </div>
                  <div class="data-info-item-num" :class="getIsIncrease(userData.currentMonthUserCount || 0, userData.preMonthUserCount || 0) === 1 ? 'green' : getIsIncrease(userData.currentMonthUserCount || 0, userData.preMonthUserCount || 0) === -1 ? 'red' : 'orange'">
                    <i :class="getIsIncrease(userData.currentMonthUserCount || 0, userData.preMonthUserCount || 0) === 1 ? 'el-icon-top' : getIsIncrease(userData.currentMonthUserCount || 0, userData.preMonthUserCount || 0) === -1 ? 'el-icon-bottom' : ''"></i>
                    <span>{{ getPercent(userData.currentMonthUserCount || 0, userData.preMonthUserCount || 0) > 0 ? getPercent(userData.currentMonthUserCount || 0, userData.preMonthUserCount || 0) + '%' : '持平' }}</span>
                  </div>
                </template>
                <template v-if="userActive === 'company'">
                  <div class="data-info-item-title">
                    <span>比上月增长</span>
                    <i class="el-icon-caret-bottom"></i>
                  </div>
                  <div class="data-info-item-num" :class="getIsIncrease(userData.currentMonthCompanyCount || 0, userData.preMonthCompanyCount || 0) === 1 ? 'green' : getIsIncrease(userData.currentMonthCompanyCount || 0, userData.preMonthCompanyCount || 0) === -1 ? 'red' : 'orange'">
                    <i :class="getIsIncrease(userData.currentMonthCompanyCount || 0, userData.preMonthCompanyCount || 0) === 1 ? 'el-icon-top' : getIsIncrease(userData.currentMonthCompanyCount || 0, userData.preMonthCompanyCount || 0) === -1 ? 'el-icon-bottom' : ''"></i>
                    <span>{{ getPercent(userData.currentMonthCompanyCount || 0, userData.preMonthCompanyCount || 0) > 0 ? getPercent(userData.currentMonthCompanyCount || 0, userData.preMonthCompanyCount || 0) + '%' : '持平' }}</span>
                  </div>
                </template>
              </div>
            </div>
          </div>
          <div style="position: relative">
            <div class="data-select border-none">
              <el-date-picker v-model="userMonth" format="yyyy年MM月概览" value-format="yyyyMM" type="month" placeholder="请选择月份" style="width: 100%" :clearable="false" @change="getUsersStatistics" :picker-options="endDatePicker"></el-date-picker>
            </div>
            <div class="data-chart-info">
              <portrait-bar-chart height="450px" :chart-data="userFormat(userData)" />
            </div>
          </div>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="moreBox-list isTop">
          <div class="moreBox-list-title">
            <span class="title">用户构成概览</span>
            <div class="data-select border-none relative">
              <el-date-picker v-model="userMonth" format="yyyy年MM月概览" value-format="yyyyMM" type="month" placeholder="请选择月份" style="width: 100%" :clearable="false" @change="getUsersStatistics" :picker-options="endDatePicker"></el-date-picker>
            </div>
          </div>
          <div class="moreBox-list-chart flex align-center">
            <div class="chart-chart">
              <pie-chart :chart-data="userPieFormat(userData)" />
            </div>
            <div class="chart-info">
              <div class="chart-item border-bottom-0">
                <div class="chart-item-title color-ffe09c">普通用户</div>
                <div class="chart-item-info" :class="usersIsIncrease(userData) === 1 ? 'green' : usersIsIncrease(userData) === -1 ? 'red' : 'orange'">
                  <span class="title">同比上个月</span>
                  <i class="el-icon-top" v-if="usersIsIncrease(userData) === 1"></i>
                  <i class="el-icon-bottom" v-if="usersIsIncrease(userData) === -1"></i>
                  <span class="num">{{ usersContrast(userData) === 0 ? '持平' : usersContrast(userData) + '%' }}</span>
                </div>
              </div>
              <div class="chart-item border-bottom-0">
                <div class="chart-item-title color-ffb2c8">企业用户</div>
                <div class="chart-item-info" :class="usersIsIncrease(userData, 'company') === 1 ? 'green' : usersIsIncrease(userData, 'company') === -1 ? 'red' : 'orange'">
                  <span class="title">同比上个月</span>
                  <i class="el-icon-top" v-if="usersIsIncrease(userData, 'company') === 1"></i>
                  <i class="el-icon-bottom" v-if="usersIsIncrease(userData, 'company') === -1"></i>
                  <span class="num">{{ usersContrast(userData, 'company') === 0 ? '持平' : usersContrast(userData, 'company') + '%' }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="moreBox-list isBottom">
          <div class="moreBox-list-title">
            <span class="title">最新企业用户</span>
            <div class="data-select border-none relative">
              <el-date-picker v-model="userMonth" format="yyyy年MM月概览" value-format="yyyyMM" type="month" placeholder="请选择月份" style="width: 100%" :clearable="false" @change="getUsersStatistics" :picker-options="endDatePicker"></el-date-picker>
            </div>
          </div>
          <div class="moreBox-list-top">
            <div class="top-item" v-for="(item, index) in topList" :key="index">
              <b class="top-item-title">{{ item.companyName }}</b>
              <span class="top-item-desc">{{ item.createTime }}</span>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import lineChart from '@/views/data/lineChart'
import pieChart from '@/views/data/pieChart'
import { userStatistics } from '@/api/data'
import { parseTime, getPercent, getIsIncrease } from '@/utils/ruoyi'
import portraitBarChart from '@/views/data/portraitBarChart.vue'

export default {
  components: { portraitBarChart, lineChart, pieChart },
  data() {
    return {
      userMonth: parseTime(new Date(), '{y}{m}'),
      userData: {},
      topList: [],
      userActive: 'user',
      nowMonth: parseTime(new Date(), '{y}{m}'),
      nowDay: parseTime(new Date(), '{d}'),
      endDatePicker: {
        disabledDate: time => {
          return time.getTime() > new Date().getTime()
        }
      }
    }
  },
  created() {
    this.getUsersStatistics()
  },
  methods: {
    parseTime,
    getPercent,
    getIsIncrease,
    // 需求统计
    getUsersStatistics() {
      userStatistics({ month: this.userMonth }).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          this.userData = data
          this.topList = data.newCompany || []
        } else this.$message.error(msg)
      })
    },
    // 格式化需求统计饼图数据
    userPieFormat(data = {}) {
      const { currentMonthUserCount = 0, currentMonthCompanyCount = 0 } = data
      return {
        series: [
          { name: '普通用户', value: currentMonthUserCount, itemStyle: { color: '#ffe09c' } },
          { name: '企业用户', value: currentMonthCompanyCount, itemStyle: { color: '#ffb2c8' } }
        ]
      }
    },
    // 格式化注册用户统计柱状图数据
    userFormat(data = {}) {
      let { userData = [], companyData = [] } = data
      if (this.userActive === 'user') {
        let chartData = { series: [], name: undefined }
        chartData.name = '普通用户'
        chartData.series = userData.map(item => item.yc) || []
        let xAxis = userData.map(item => item.day) || []
        const { userMonth, nowMonth, nowDay } = this
        if (userMonth == nowMonth) {
          xAxis = xAxis.slice(0, nowDay)
          chartData.series = chartData.series.slice(0, nowDay)
        }
        return { xAxis, chartData }
      } else {
        let chartData = { series: [], name: undefined }
        chartData.name = '企业用户'
        chartData.series = companyData.map(item => item.yc) || []
        let xAxis = companyData.map(item => item.day) || []
        const { userMonth, nowMonth, nowDay } = this
        if (userMonth == nowMonth) {
          xAxis = xAxis.slice(0, nowDay)
          chartData.series = chartData.series.slice(0, nowDay)
        }
        return { xAxis, chartData }
      }
    },
    // 数量对比上个月百分比
    usersContrast(data = {}, type = 'user') {
      const { currentMonthUserCount = 0, preMonthUserCount = 0, currentMonthCompanyCount = 0, preMonthCompanyCount = 0 } = data
      if (type === 'user') {
        return this.getPercent(currentMonthUserCount, preMonthUserCount)
      } else {
        return this.getPercent(currentMonthCompanyCount, preMonthCompanyCount)
      }
    },
    // 数量对比上个月是否增加
    usersIsIncrease(data = {}, type = 'user') {
      const { currentMonthUserCount = 0, preMonthUserCount = 0, currentMonthCompanyCount = 0, preMonthCompanyCount = 0 } = data
      if (type === 'user') {
        return this.getIsIncrease(currentMonthUserCount, preMonthUserCount)
      } else {
        return this.getIsIncrease(currentMonthCompanyCount, preMonthCompanyCount)
      }
    },
    goBack() {
      this.$emit('callBack')
    }
  }
}
</script>
<style scoped lang="scss">
@import '~@/assets/styles/custom-chart.scss';
.moreBox {
  .data-chart {
    width: 100%;
    height: 600px;
    margin-top: 30px;
    margin-left: 0;
  }
}
</style>
