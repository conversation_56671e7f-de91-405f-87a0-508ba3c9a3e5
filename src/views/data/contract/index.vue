<template>
  <div class="moreBox">
    <el-page-header @back="goBack" content="合同统计"></el-page-header>
    <el-row :gutter="0">
      <el-col :span="12">
        <div class="moreBox-info">
          <div class="moreBox-info-item">
            <div class="item-title">{{ contractMonth.slice(0, 4) + '年' + contractMonth.slice(4, 6) + '合同数量' }}</div>
            <div class="item-num">{{ contractData.currentMonthContractCount || 0 }}</div>
            <div class="item-bg gray"></div>
            <div class="item-contrast">
              <div class="c666">
                <span>对比上个月</span>
                <i class="el-icon-caret-bottom"></i>
              </div>
              <div :class="contractIsIncrease(contractData) === 1 ? 'green' : contractIsIncrease(contractData) === -1 ? 'red' : 'orange'">
                <i class="el-icon-top" v-if="contractIsIncrease(contractData) === 1"></i>
                <i class="el-icon-bottom" v-if="contractIsIncrease(contractData) === -1"></i>
                <span>{{ contractContrast(contractData) === 0 ? '持平' : contractContrast(contractData) + '%' }}</span>
              </div>
            </div>
          </div>
          <div class="moreBox-info-item">
            <div class="item-title">{{ contractMonth.slice(0, 4) + '年' + contractMonth.slice(4, 6) + '月合同总金额' }}</div>
            <div class="item-num">{{ contractData.currentMonthContractAmount || 0 }}</div>
            <div class="item-bg gray"></div>
            <div class="item-contrast">
              <div class="c666">
                <span>对比上个月</span>
                <i class="el-icon-caret-bottom"></i>
              </div>
              <div :class="contractIsIncrease(contractData, 'mount') === 1 ? 'green' : contractIsIncrease(contractData, 'mount') === -1 ? 'red' : 'orange'">
                <i class="el-icon-top" v-if="contractIsIncrease(contractData, 'mount') === 1"></i>
                <i class="el-icon-bottom" v-if="contractIsIncrease(contractData, 'mount') === -1"></i>
                <span>{{ contractContrast(contractData, 'mount') === 0 ? '持平' : contractContrast(contractData, 'mount') + '%' }}</span>
              </div>
            </div>
          </div>
        </div>
        <div class="data-chart">
          <div class="data-select border-none">
            <el-date-picker v-model="contractMonth" format="yyyy年MM月概览" value-format="yyyyMM" type="month" placeholder="请选择月份" style="width: 100%" :clearable="false" @change="getContractStatistics" :picker-options="endDatePicker"></el-date-picker>
          </div>
          <div class="data-chart-info">
            <line-chart height="600px" :grid-right="20" :chart-data="contractFormat(contractData)" />
          </div>
        </div>
      </el-col>
      <el-col :span="12" v-if="topList.length">
        <div class="moreBox-list">
          <div class="moreBox-list-title">
            <span class="title">合同总量TOP5</span>
            <div class="data-select border-none relative">
              <el-date-picker v-model="contractMonth" format="yyyy年MM月概览" value-format="yyyyMM" type="month" placeholder="请选择月份" style="width: 100%" :clearable="false" @change="getContractStatistics" :picker-options="endDatePicker"></el-date-picker>
            </div>
          </div>
          <div class="moreBox-list-top">
            <div class="top-item" v-for="(item, index) in topList" :key="index">
              <b class="top-item-title">{{ item.companyName }}</b>
              <span class="top-item-desc">{{ '合同数量 ' + (item.count || 0) }}</span>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import lineChart from '@/views/data/lineChart'
import { contractStatistics } from '@/api/data'
import { parseTime, getPercent, getIsIncrease } from '@/utils/ruoyi'

export default {
  components: { lineChart },
  data() {
    return {
      contractMonth: parseTime(new Date(), '{y}{m}'),
      contractData: {},
      topList: [],
      nowMonth: parseTime(new Date(), '{y}{m}'),
      nowDay: parseTime(new Date(), '{d}'),
      endDatePicker: {
        disabledDate: time => {
          return time.getTime() > new Date().getTime()
        }
      }
    }
  },
  created() {
    this.getContractStatistics()
  },
  methods: {
    parseTime,
    getPercent,
    getIsIncrease,
    // 合同统计
    getContractStatistics() {
      contractStatistics({ month: this.contractMonth }).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          this.contractData = data
          this.topList = data.companyTop5Data || []
        } else this.$message.error(msg)
      })
    },
    // 格式化合同统计折线图数据
    contractFormat(data = {}) {
      let { contractMonthData = [], preContractMonthData = [] } = data
      if (contractMonthData.length > preContractMonthData.length) contractMonthData.pop()
      if (contractMonthData.length < preContractMonthData.length) preContractMonthData.pop()
      let oneData = { series: [], name: undefined }
      let twoData = { series: [], name: undefined }
      oneData.name = '本月合同'
      twoData.name = '上月合同'
      oneData.series = contractMonthData.map(item => item.yc) || []
      twoData.series = preContractMonthData.map(item => item.yc) || []
      let xAxis = contractMonthData.map(item => item.day) || []
      const { contractMonth, nowMonth, nowDay } = this
      if (contractMonth == nowMonth) {
        xAxis = xAxis.slice(0, nowDay)
        oneData.series = oneData.series.slice(0, nowDay)
        twoData.series = twoData.series.slice(0, nowDay)
      }
      return { xAxis, oneData, twoData, legend: [oneData.name, twoData.name] }
    },
    // 合同统计对比上个月
    contractContrast(data = {}, type = 'count') {
      const { currentMonthContractCount = 0, preMonthContractCount = 0, currentMonthContractAmount = 0, preMonthContractAmount = 0 } = data
      if (type === 'count') {
        return this.getPercent(currentMonthContractCount, preMonthContractCount)
      } else {
        return this.getPercent(currentMonthContractAmount, preMonthContractAmount)
      }
    },
    contractIsIncrease(data = {}, type = 'count') {
      const { currentMonthContractCount = 0, preMonthContractCount = 0, currentMonthContractAmount = 0, preMonthContractAmount = 0 } = data
      if (type === 'count') {
        return this.getIsIncrease(currentMonthContractCount, preMonthContractCount)
      } else {
        return this.getIsIncrease(currentMonthContractAmount, preMonthContractAmount)
      }
    },
    goBack() {
      this.$emit('callBack')
    }
  }
}
</script>
<style scoped lang="scss">
@import '~@/assets/styles/custom-chart.scss';
.moreBox {
  .data-chart {
    width: 100%;
    height: 600px;
    margin-top: 30px;
    margin-left: 0;
  }
}
</style>
