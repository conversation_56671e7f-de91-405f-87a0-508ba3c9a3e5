<template>
  <div class="newBox bgcf9 vh-85" v-if="checkPermi(['sys:warranty:list'])">
    <!-- 搜索 -->
    <div class="custom-search flex">
      <div class="flex">
        <div class="custom-search-form flex">
          <input type="text" v-model="queryParams.productName" placeholder="请输入物料名称" class="custom-search-input" @keyup.enter="handleQuery" />
          <button type="button" class="custom-search-button pointer" @click="handleQuery">
            <i class="el-icon-search"></i>
            搜索
          </button>
        </div>
        <button type="button" class="custom-search-add pointer" style="background: #e0ebff; border-radius: 5px; border: 1px solid #2e73f3; color: #2e73f3" @click="refreshList">
          <i class="el-icon-refresh" style="color: #2e73f3"></i>
          刷新重置
        </button>
        <button type="button" class="custom-search-add pointer" @click="handleAdd" v-if="checkPermi(['sys:warranty:add'])">
          <i class="el-icon-plus"></i>
          新建报告单
        </button>
        <button type="button" class="custom-search-add pointer" @click="handleSetting" v-if="checkPermi(['sys:warranty:approve'])">
          <i class="el-icon-setting"></i>
          维护审批人
        </button>
        <el-button class="organge" size="small" icon="el-icon-upload2" @click="handleExport" style="margin-left: 10px">导出</el-button>
      </div>
      <div class="classify-toolbar">
        <right-toolbar :search="false" @queryTable="getList" :columns="columns"></right-toolbar>
      </div>
    </div>

    <!-- 分类 -->
    <div class="classify flex">
      <div class="classify-item" :class="{ active: item.value == queryParams.status }" v-for="item in statusOptions" :key="item.value" @click="handleChangeStatus(item)">{{ item.label }}</div>
    </div>

    <!-- 表格数据 -->
    <div class="tableBox">
      <el-table v-loading="loading" ref="table" stripe :data="list" row-key="id" style="width: 100%" class="custom-table custom-table-cell5" @selection-change="handleSelectionChange">
        <!-- 复选框 -->
        <el-table-column type="selection" width="55" v-if="queryParams.status == 2" align="center" :reserve-selection="true" />
        <el-table-column align="center" type="index" label="序号" v-if="columns[0].visible"></el-table-column>
        <el-table-column align="left" label="物料名称" show-overflow-tooltip v-if="columns[1].visible">
          <template slot-scope="{ row }">
            <span class="table-link" @click="handleProductView(row)">{{ row.productName }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="图片" show-overflow-tooltip v-if="columns[2].visible">
          <template slot-scope="{ row }">
            <div style="display: inline-flex; justify-content: center" v-if="row.product && (row.product.picture1_oss || row.product.picture1)">
              <image-preview :src="row.product.picture1_oss || imgPath + row.product.picture1" :width="50" :height="50" />
            </div>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="规格" show-overflow-tooltip v-if="columns[3].visible">
          <template slot-scope="{ row }">
            <span>{{ row.product && row.product.specs }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="单位" show-overflow-tooltip v-if="columns[4].visible" width="100">
          <template slot-scope="{ row }">
            <span>{{ row.product && row.product.unit }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="包装数量" show-overflow-tooltip v-if="columns[5].visible">
          <template slot-scope="{ row }">
            <span>{{ row.packaged }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="残坏数量" show-overflow-tooltip v-if="columns[6].visible">
          <template slot-scope="{ row }">
            <span>{{ row.damaged }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="重量" show-overflow-tooltip v-if="columns[7].visible">
          <template slot-scope="{ row }">
            <span>{{ row.product && row.product.weight ? (row.product && row.product.weight) + 'kg' : '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="status" label="折合重量" show-overflow-tooltip v-if="columns[8].visible">
          <template slot-scope="{ row }">
            <span>{{ row.product && row.product.weight ? parseFloat(row.damaged) * parseFloat(row.product.weight) + 'kg' : '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="reason" label="残坏报废原因" show-overflow-tooltip v-if="columns[9].visible"></el-table-column>
        <el-table-column align="center" prop="createTime" label="报告日期" show-overflow-tooltip v-if="columns[10].visible"></el-table-column>
        <el-table-column align="center" prop="supplier" label="供应商" show-overflow-tooltip v-if="columns[12].visible"></el-table-column>
        <el-table-column align="center" label="质检处理意见" show-overflow-tooltip v-if="columns[11].visible">
          <template slot-scope="{ row }">
            <span>{{ (row.details && row.details.approvals.length > 0 && row.details.approvals.find(item => item.stage == 'zj').approvalInfo) || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="状态" show-overflow-tooltip v-if="columns[13].visible">
          <template slot-scope="{ row }">
            <span v-if="row.status == 0" style="color: #f35d09">待审核</span>
            <span v-if="row.status == 1" style="color: #2e73f3">审核中</span>
            <span v-if="row.status == 2" style="color: #999999">已审核</span>
            <span v-if="row.status == -10" style="color: #f65656">已删除</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作" width="320px">
          <template slot-scope="{ row }">
            <button type="button" class="table-btn" v-if="checkPermi(['sys:warranty:view'])" @click="handleView(row)">查看详情</button>
            <button type="button" class="table-btn primary" @click="handleQuality(row)" v-if="checkPermi(['sys:warranty:quality']) && row.stage == 'zj' && approvalObj.zj.find(item => item.approvalId == userInfo.userId) && row.status == 0">质检审核</button>
            <button type="button" class="table-btn primary" @click="handleApprove(row, 'bm')" v-if="checkPermi(['sys:warranty:approve']) && row.stage == 'bm' && approvalObj.bm.find(item => item.approvalId == userInfo.userId) && row.status == 1">部门审核</button>
            <!-- <button type="button" class="table-btn primary" @click="handleApprove(row, 'zjb')"
              v-if="checkPermi(['sys:warranty:approve']) && row.stage == 'zjb' && approvalObj.zjb.find(item => item.approvalId == userInfo.userId) && row.status == 1">总经办审核</button> -->
            <button type="button" class="table-btn primary" @click="handleApprove(row, 'dsz')" v-if="checkPermi(['sys:warranty:approve']) && row.stage == 'dsz' && approvalObj.dsz.find(item => item.approvalId == userInfo.userId) && row.status == 1">总经理审核</button>
            <button type="button" class="table-btn danger" @click="handleDelete(row)" v-if="checkPermi(['sys:warranty:del']) && row.status != -10 && row.creator == userInfo.userId">删除</button>
          </template>
        </el-table-column>
      </el-table>

      <div class="custom-pagination">
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
      </div>
    </div>

    <!--新增待办弹窗-->
    <el-dialog v-dialogDragBox :title="title" :visible.sync="addListingOpen" width="1150px" class="custom-dialog">
      <div class="p20 addBox">
        <el-form ref="form" :model="form" :rules="rules" label-width="8em">
          <el-row>
            <el-col :span="12">
              <el-form-item label="物料名称" prop="productName">
                <div @click="hadnleProductAdd">
                  <el-input v-model="form.productName" placeholder="请输入物料名称" readonly suffix-icon="el-icon-search"></el-input>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="包装数量" prop="packaged">
                <el-input v-model="form.packaged" placeholder="请输入包装数量"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="残坏数量" prop="damaged">
                <el-input v-model="form.damaged" placeholder="请输入残坏数量"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="残坏报废原因" prop="reason">
                <el-input v-model="form.reason" placeholder="请输入残坏报废原因"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="报告单位/部门" prop="deptId">
                <el-select v-model="form.deptId" filterable placeholder="请选择报告单位/部门" clearable style="width: 100%" @change="selectDept">
                  <el-option v-for="(item, index) in deptOptions" :key="index" :label="item.deptName" :value="String(item.deptId)" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="实物负责人" prop="headId">
                <el-select v-model="form.headId" filterable placeholder="请选择实物负责人" clearable style="width: 100%" @change="selectHead">
                  <el-option v-for="(item, index) in userList" :key="index" :label="item.realName || item.nickName" :value="String(item.userId)" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="供应商" prop="supplier">
                <el-input v-model="form.supplier" placeholder="请输入供应商"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="镀锌厂家" prop="manufacturer">
                <el-input v-model="form.manufacturer" placeholder="请输入镀锌厂家"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="上传附件" prop="attachment">
                <image-upload isRow :isShowTip="!form.attachment" :fileSize="1024" v-model="form.attachment" :file-type="fileType" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div class="approvalBox">
          <div class="approvalTitle">审核流程</div>
          <div class="approvalList">
            <span class="approvalItem">质检审核</span>
            <span class="approvalIcon">→</span>
            <span class="approvalItem">部门负责人</span>
            <span class="approvalIcon">→</span>
            <!-- <span class="approvalItem">总经办审核</span>
            <span class="approvalIcon">→</span> -->
            <span class="approvalItem">总经理</span>
          </div>
        </div>
      </div>
      <div slot="footer">
        <el-button @click="addListingOpen = false" size="medium" style="width: 200px">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" size="medium" style="width: 200px">确 定</el-button>
      </div>
    </el-dialog>

    <!--待办详情-->
    <el-dialog v-dialogDragBox :title="viewTitle" :visible.sync="viewOpen" width="1150px" class="custom-dialog">
      <div class="p20">
        <div class="view_box">
          <div class="view_detail">
            <div class="product_info">
              <div class="product_info_img">
                <img :src="info.product && (info.product.picture1_oss || imgPath + info.product.picture1)" alt="" />
              </div>
              <div class="product_info_text">
                <div class="product_info_text_title">{{ info.productName }}</div>
                <div class="product_info_text_list">
                  <div class="product_info_text_list_item">
                    规格
                    <span>{{ info.product && info.product.specs }}</span>
                  </div>
                  <div class="product_info_text_list_item">
                    产品编码
                    <span>{{ info.product && info.product.productCode }}</span>
                  </div>
                  <div class="product_info_text_list_item">
                    材质
                    <span>{{ info.product && info.product.materialQuality }}</span>
                  </div>
                  <div class="product_info_text_list_item">
                    表面处理
                    <span>{{ info.product && info.product.surface }}</span>
                  </div>
                  <div class="product_info_text_list_item">
                    单位
                    <span>{{ info.product && info.product.unit }}</span>
                  </div>
                  <div class="product_info_text_list_item">
                    重量
                    <span>{{ info.product && info.product.weight }}</span>
                  </div>
                </div>
              </div>
            </div>
            <div class="number_info">
              <div class="number_info_item">
                包装数量
                <span>{{ info.packaged }}</span>
              </div>
              <div class="number_info_item">
                残坏数量
                <span>{{ info.damaged }}</span>
              </div>
              <div class="number_info_item">
                折合重量
                <span>{{ info.product && info.product.weight ? parseFloat(info.damaged) * parseFloat(info.product.weight) + 'kg' : '--' }}</span>
              </div>
            </div>
            <div class="imperfect_info">
              <div class="imperfect_info_list">
                <div class="imperfect_info_list_item">
                  残坏报废原因
                  <span>{{ info.reason }}</span>
                </div>
                <div class="imperfect_info_list_item">
                  供应商
                  <span>{{ info.supplier }}</span>
                </div>
                <div class="imperfect_info_list_item">
                  镀锌厂家
                  <span>{{ info.manufacturer }}</span>
                </div>
                <div class="imperfect_info_list_item">
                  实物负责人
                  <span>{{ info.head }}</span>
                </div>
                <div class="imperfect_info_list_item">
                  创建日期
                  <span>{{ info.createTime }}</span>
                </div>
              </div>
              <div class="imperfect_info_img" v-if="info.attachment_oss">
                <img v-for="(item, index) in info.attachment_oss.split(',')" :key="index" :src="item" alt="" />
              </div>
              <div class="imperfect_info_img" v-else-if="info.attachment">
                <img v-for="(item, index) in info.attachment.split(',')" :key="index" :src="imgPath + item" alt="" />
              </div>
              <div class="imperfect_info_img" v-else></div>
            </div>
          </div>
          <div class="quality_form" v-if="viewType == 'quality'">
            <el-form ref="qualityForm" :model="qualityForm" :rules="qualityRules" label-width="8em">
              <el-row>
                <el-col :span="12">
                  <el-form-item label="质检处理意见" prop="approvalInfo">
                    <el-input v-model="qualityForm.approvalInfo" placeholder="请输入质检处理意见"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="常用处理意见" prop="radioGroup">
                    <el-radio-group v-model="radioGroup" class="radio_box" @change="handleRadio">
                      <el-radio-button v-for="city in cities" :label="city" :key="city">{{ city }}</el-radio-button>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </div>
          <div class="quality_box" v-if="viewType != 'quality' && info.approvals && info.approvals.length > 0 && info.approvals.find(item => item.stage == 'zj')">
            <div class="quality_box_item">
              质检处理意见
              <span>{{ info.approvals && info.approvals.length > 0 && info.approvals.find(item => item.stage == 'zj').approvalInfo }}</span>
            </div>
            <div class="quality_box_item">
              质检审核
              <span>{{ info.approvals && info.approvals.length > 0 && info.approvals.find(item => item.stage == 'zj').approvalName }}</span>
            </div>
            <div class="quality_box_item">
              质检日期
              <span>{{ info.approvals && info.approvals.length > 0 && info.approvals.find(item => item.stage == 'zj').createTime }}</span>
            </div>
          </div>
          <div class="view_list" v-if="viewType != 'quality'">
            <div class="approvalBox">
              <div class="approvalTitle">审核流程</div>
              <div class="approvalList">
                <span class="approvalItem" :class="info.approvals && info.approvals.length > 0 && info.approvals.find(item => item.stage == 'zj') ? 'active' : ''">
                  质检审核
                  <i class="el-icon-check" v-if="info.approvals && info.approvals.length > 0 && info.approvals.find(item => item.stage == 'zj')"></i>
                </span>
                <span class="approvalIcon">→</span>
                <span class="approvalItem" :class="info.approvals && info.approvals.length > 0 && info.approvals.find(item => item.stage == 'bm') ? 'active' : ''">
                  部门负责人
                  <i class="el-icon-check" v-if="info.approvals && info.approvals.length > 0 && info.approvals.find(item => item.stage == 'bm')"></i>
                </span>
                <span class="approvalIcon">→</span>
                <!-- <span class="approvalItem"
                  :class="info.approvals && info.approvals.length > 0 && info.approvals.find(item => item.stage == 'zjb') ? 'active' : ''">
                  总经办审核
                  <i class="el-icon-check"
                    v-if="info.approvals && info.approvals.length > 0 && info.approvals.find(item => item.stage == 'zjb')"></i>
                </span>
                <span class="approvalIcon">→</span> -->
                <span class="approvalItem" :class="info.approvals && info.approvals.length > 0 && info.approvals.find(item => item.stage == 'dsz') ? 'active' : ''">
                  总经理
                  <i class="el-icon-check" v-if="info.approvals && info.approvals.length > 0 && info.approvals.find(item => item.stage == 'dsz')"></i>
                </span>
              </div>
            </div>
            <div class="view_list_item" v-for="(item, index) in info.approvals && info.approvals.length > 0 && info.approvals.filter(it => it.stage != 'zj')" :key="index">
              <div class="view_list_item_top">
                <img :src="item.avatar_oss || imgPath + item.avatar" alt="" />
                <span>{{ item.realName || item.approvalName }}</span>
                <span>{{ item.createTime }}</span>
              </div>
              <div class="view_list_item_bottom">
                <div class="view_list_item_bottom_text">{{ item.approvalInfo }}</div>
              </div>
            </div>
          </div>
        </div>
        <el-form ref="upForm" :model="upForm" label-width="8em" v-if="viewType == 'approval'">
          <el-row>
            <el-col :span="24">
              <el-form-item label="审核备注" prop="approvalInfo">
                <div style="display: flex; flex-direction: column">
                  <el-input type="textarea" :rows="3" placeholder="请输入审核备注" v-model="upForm.approvalInfo"></el-input>
                  <div class="inCommonUse" v-if="inCommonUse.length > 0">
                    <div class="inCommonUse_item" v-for="(item, index) in inCommonUse" :key="index">
                      <span class="inCommonUse_item_text" @click="handleInCommonUse(item)">{{ item }}</span>
                      <i class="el-icon-error inCommonUse_item_close" @click="handleDeleteInCommonUse(item)"></i>
                    </div>
                  </div>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <el-button @click="viewOpen = false" size="medium" style="width: 269px; height: 50px">关 闭</el-button>
        <el-button type="primary" @click="handleSubmitUp('qualityForm')" size="medium" style="width: 269px; height: 50px" v-if="viewType == 'quality'">提 交</el-button>
        <el-button type="primary" @click="handleSubmitUp('upForm')" size="medium" style="width: 269px; height: 50px" v-if="viewType == 'approval'">提 交</el-button>
      </div>
    </el-dialog>

    <!-- 审核人维护 -->
    <el-dialog v-dialogDragBox title="审核人维护" :visible.sync="approvalOpen" width="1150px" class="custom-dialog">
      <div class="p20 addBox">
        <el-form ref="approvalForm" :model="approvalForm" :rules="approvalRules" label-width="8em">
          <el-row>
            <el-col :span="12">
              <el-form-item label="质检人员" prop="zj">
                <el-cascader popper-class="cascaderInfo" v-model="approvalForm.zj" :options="approvalsOptions" :props="approvalsProps" filterable :show-all-levels="false" placeholder="请选择质检人员" @change="handleChange('zj', $event)" style="width: 100%"></el-cascader>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="部门负责人" prop="bm">
                <el-cascader popper-class="cascaderInfo" v-model="approvalForm.bm" :options="approvalsOptions" :props="approvalsProps" filterable :show-all-levels="false" placeholder="请选择部门负责人" @change="handleChange('bm', $event)" style="width: 100%"></el-cascader>
              </el-form-item>
            </el-col>
            <!-- <el-col :span="12">
              <el-form-item label="总经办" prop="zjb">
                <el-cascader popper-class="cascaderInfo" v-model="approvalForm.zjb" :options="approvalsOptions"
                  :props="approvalsProps" filterable :show-all-levels="false" placeholder="请选择总经办"
                  @change="handleChange('zjb', $event)" style="width: 100%"></el-cascader>
              </el-form-item>
            </el-col> -->
            <el-col :span="12">
              <el-form-item label="总经理" prop="dsz">
                <el-cascader popper-class="cascaderInfo" v-model="approvalForm.dsz" :options="approvalsOptions" :props="approvalsProps" filterable :show-all-levels="false" placeholder="请选择总经理" @change="handleChange('dsz', $event)" style="width: 100%"></el-cascader>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <el-button @click="approvalOpen = false" size="medium" style="width: 200px">取 消</el-button>
        <el-button type="primary" @click="handleApprovalSubmit" size="medium" style="width: 200px">确 定</el-button>
      </div>
    </el-dialog>

    <el-dialog v-dialogDragBox :visible.sync="imgOpen" title="预览" width="1150px" class="custom-dialog" append-to-body>
      <div style="padding: 0 20px">
        <img :src="imgUrl" style="display: block; max-width: 100%; margin: 0 auto" />
      </div>
    </el-dialog>

    <el-dialog v-dialogDragBox title="预览" :visible.sync="pdfOpen" width="1150px" class="custom-dialog" append-to-body>
      <div style="padding: 0 20px">
        <div style="display: flex; justify-content: space-between; align-items: center; padding-bottom: 10px" v-if="pdfCount > 1">
          <el-button type="primary" size="small" @click="pdfCurrent--" :disabled="pdfCurrent < 2">
            <i class="el-icon-arrow-left"></i>
            上一页
          </el-button>
          <span>{{ pdfCurrent }} / {{ pdfCount }}</span>
          <el-button type="primary" size="small" @click="pdfCurrent++" :disabled="pdfCurrent >= pdfCount">
            下一页
            <i class="el-icon-arrow-right"></i>
          </el-button>
        </div>
        <div class="page-pdf">
          <Pdf :src="pdfUrl" :page="pdfCurrent" @num-pages="pdfCount = $event" />
        </div>
      </div>
    </el-dialog>

    <!-- 已选择操作栏 -->
    <template v-if="selectChecked.length">
      <div class="collectAll">
        <div class="collectAll-box">
          <div class="collectAll-title">已选择 {{ selectChecked.length }} 项</div>
          <el-tooltip content="新建其他出库单" placement="top" effect="dark">
            <div class="collectAll-btn">
              <span @click="handleCreateOutboundOrder">新增其他出库单</span>
            </div>
          </el-tooltip>
          <el-tooltip content="退出" placement="top" effect="dark">
            <div class="collectAll-close" @click="handleCloseSelection">
              <i class="el-icon-close"></i>
            </div>
          </el-tooltip>
        </div>
      </div>
    </template>

    <!-- 产品详情 -->
    <product-dialog ref="productInfo"></product-dialog>
    <!--添加产品--选择产品-->
    <check-product ref="checkProduct" @confirm="handleConfirm" />
    <!-- 其他出库单 -->
    <outbound-create ref="outboundCreate" @callBack="handleCallBack" v-if="showOutboundCreate" />
  </div>
</template>

<script>
import { warrantyScrapAdd, warrantyScrapApproval, warrantyScrapApprovalUsersAdd, warrantyScrapApprovalUsers, warrantyScrapList, warrantyScrapDetail, warrantyScrapDelete, warrantyScrapExport } from '@/api/warranty'
import { listDept } from '@/api/system/dept'
import { deptTreeSelect, listUser } from '@/api/system/user'
import { parseTime } from '@/utils/ruoyi'
import { checkPermi } from '@/utils/permission'
import ProductDialog from '@/views/public/product/dialog'
import CheckProduct from './product'
import OutboundCreate from '@/views/kingdee/inventory/outbound/create'

export default {
  name: 'Warranty',
  components: { ProductDialog, CheckProduct, OutboundCreate },
  data() {
    return {
      showOutboundCreate: false,
      // 搜索条件
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        productName: undefined,
        status: '0'
      },
      statusOptions: [
        { value: '0', label: '待审核' },
        { value: '1', label: '审核中' },
        { value: '2', label: '已审核' }
      ],
      // 加载
      loading: true,
      // 列表数据
      list: [],
      // 总条数
      total: 0,
      // 显隐列
      columns: [
        { key: 0, label: `序号`, visible: true },
        { key: 1, label: `物料名称`, visible: true },
        { key: 2, label: `图片`, visible: true },
        { key: 3, label: `规格`, visible: true },
        { key: 4, label: `单位`, visible: true },
        { key: 5, label: `包装数量`, visible: true },
        { key: 6, label: `残坏数量`, visible: true },
        { key: 7, label: `重量`, visible: true },
        { key: 8, label: `折合重量`, visible: true },
        { key: 9, label: `残坏报废原因`, visible: true },
        { key: 10, label: `报告日期`, visible: true },
        { key: 11, label: `质检处理意见`, visible: true },
        { key: 12, label: `供应商`, visible: true },
        { key: 13, label: `状态`, visible: true }
      ],
      addListingOpen: false,
      title: '新增报告单',
      form: {},
      rules: {
        damaged: [{ required: true, message: '请输入残坏数量', trigger: 'blur' }],
        deptId: [{ required: true, message: '请选择报告单位/部门', trigger: 'change' }],
        headId: [{ required: true, message: '请选择实物负责人', trigger: 'change' }],
        packaged: [{ required: true, message: '请输入包装数量', trigger: 'blur' }],
        productName: [{ required: true, message: '请选择物料', trigger: 'change' }],
        reason: [{ required: true, message: '请输入报废原因', trigger: 'blur' }]
      },
      approvalOpen: false,
      approvalsOptions: [],
      approvalsProps: {
        expandTrigger: 'hover',
        emitPath: false,
        multiple: true
      },
      deptOptions: [],
      priorityOptions: [
        {
          value: 0,
          label: '一般'
        }
      ],
      viewTitle: '查看报告单详情',
      viewOpen: false,
      upForm: {
        approvalInfo: ''
      },
      fileType: ['png', 'PNG', 'jpg', 'JPG', 'jpeg', 'JPEG', 'pdf'],
      userList: [],
      info: {},
      userInfo: {},
      // pdf预览
      pdfOpen: false,
      pdfCurrent: 1,
      pdfCount: 0,
      pdfUrl: '',
      imgOpen: false,
      imgUrl: '',

      viewType: 'view',
      qualityForm: {
        approvalInfo: ''
      },
      qualityRules: {
        approvalInfo: [{ required: true, message: '请输入审批信息', trigger: 'blur' }]
      },
      cities: ['报修', '报废'],
      radioGroup: [],
      approvalObj: {},
      approvalForm: {
        zj: [],
        bm: [],
        // zjb: [],
        dsz: [],
        cw: []
      },
      approvalRules: {
        zj: [{ required: true, message: '请选择质检人员', trigger: 'change' }],
        bm: [{ required: true, message: '请选择部门负责人', trigger: 'change' }],
        // zjb: [{ required: true, message: '请选择总经办', trigger: 'change' }],
        dsz: [{ required: true, message: '请选择总经理', trigger: 'change' }]
      },
      // 审核常用
      inCommonUse: [],
      // 选中的数据
      selectChecked: []
    }
  },
  computed: {
    userId() {
      return this.$store.getters.info.userId
    }
  },
  created() {
    this.userInfo = this.$store.state.user.info
    this.getApprovalsOptions()
    this.getApprovalList()
    this.getList()
    const inCommonUse = localStorage.getItem(this.userId + '.warrantyCommonUse')
    if (inCommonUse) this.inCommonUse = JSON.parse(inCommonUse)
  },
  methods: {
    checkPermi,
    parseTime,
    // 部门和人员
    async getApprovalsOptions() {
      const deptList = await listDept()
      this.deptOptions = deptList.data
      const dept = await deptTreeSelect()
      const user = await listUser()
      const children = dept.data[0].children || []
      const deptData = [...children, ...[{ id: -1, label: '其他', value: 'dept-0' }]] || []
      const userData = user.rows || []
      this.userList = userData
      const getChildren = data => {
        data.forEach(item => {
          item.value = 'dept-' + item.id
          if (item.children) {
            getChildren(item.children)
          } else {
            item.children = []
          }
        })
      }
      getChildren(deptData)
      const addChildren = data => {
        data.forEach(item => {
          userData.forEach(user => {
            if (item.id === user.deptId && item.children) {
              item.children.push({
                userId: user.userId,
                label: user.realName || user.nickName,
                value: user.userId,
                disabled: user.status == '1',
                userName: user.userName
              })
            }
            if (item.id === -1 && (!user.deptId || user.deptId === dept.data[0].id) && item.children) {
              item.children.push({
                userId: user.userId,
                label: user.realName || user.nickName,
                value: user.userId,
                disabled: user.status == '1',
                userName: user.userName
              })
            }
          })
          if (item.children && item.children.length) {
            addChildren(item.children)
          }
        })
      }
      addChildren(deptData)
      this.approvalsOptions = deptData
    },
    // 查询数据
    async getList() {
      this.loading = true
      const query = { ...this.queryParams }
      const { total, rows } = await warrantyScrapList(query)
      if (rows.length) {
        await Promise.all(
          rows.map(async item => {
            const { data } = await warrantyScrapDetail({ warrantyScrapId: item.id })
            item.details = data
          })
        )
      }
      this.list = rows
      this.loading = false
      this.total = total
    },
    // 查询审核人员
    async getApprovalList() {
      const res = await warrantyScrapApprovalUsers()
      if (res.code === 200) {
        this.approvalObj = res.data
      }
    },
    // 刷新列表
    refreshList() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10
      }
      warrantyScrapList(this.queryParams).then(res => {
        const { code, msg, rows, total } = res
        if (code === 200) {
          this.$set(this, 'list', rows)
          this.$set(this, 'total', total)
        } else this.$message.error(msg)
      })
    },
    // 切换分类
    handleChangeStatus(item) {
      this.queryParams.status = item.value
      // 清空选中数据
      this.selectChecked = []
      this.list = []
      this.total = 0
      this.handleQuery()
    },
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    handleSetting() {
      this.approvalForm = {
        zj: [],
        bm: [],
        // zjb: [],
        dsz: [],
        cw: []
      }
      if (this.approvalObj.zj && this.approvalObj.zj.length > 0) {
        this.approvalObj.zj.map(el => {
          this.approvalForm.zj.push(el.approvalId)
        })
      }
      if (this.approvalObj.bm && this.approvalObj.bm.length > 0) {
        this.approvalObj.bm.map(el => {
          this.approvalForm.bm.push(el.approvalId)
        })
      }
      // if (this.approvalObj.zjb && this.approvalObj.zjb.length > 0) {
      //   this.approvalObj.zjb.map(el => {
      //     this.approvalForm.zjb.push(el.approvalId)
      //   })
      // }
      if (this.approvalObj.dsz && this.approvalObj.dsz.length > 0) {
        this.approvalObj.dsz.map(el => {
          this.approvalForm.dsz.push(el.approvalId)
        })
      }
      if (this.approvalObj.cw && this.approvalObj.cw.length > 0) {
        this.approvalObj.cw.map(el => {
          this.approvalForm.cw.push(el.approvalId)
        })
      }
      this.approvalOpen = true
    },
    // 查看详情
    handleProductView(item) {
      this.$refs.productInfo.handleView(item.product)
    },
    // 图片预览
    handleImg(row) {
      this.$refs.productInfo.handleImgView(row.product)
    },
    // 新增
    handleAdd() {
      this.form = {
        damaged: undefined,
        deptId: undefined,
        deptName: undefined,
        head: undefined,
        headId: undefined,
        manufacturer: undefined,
        packaged: undefined,
        productId: undefined,
        productName: undefined,
        reason: undefined,
        supplier: undefined,
        attachment: ''
      }
      this.resetForm('form')
      this.title = '新增报告单'
      this.addListingOpen = true
    },
    // 选择物料
    hadnleProductAdd() {
      this.$refs.checkProduct.openProduct([])
    },
    // 确认选择产品
    handleConfirm(selected) {
      this.form.productId = selected.productId
      this.form.productName = selected.productName
    },
    // 删除
    handleDelete(row) {
      this.$confirm('确定要删除该报告单吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          warrantyScrapDelete({ warrantyScrapId: row.id }).then(res => {
            if (res.code === 200) {
              this.$message.success('删除成功')
              this.getList()
            } else {
              this.$message.error(res.msg)
            }
          })
        })
        .catch(() => {})
    },
    // 查看详情
    handleView(row) {
      warrantyScrapDetail({
        warrantyScrapId: row.id
      }).then(res => {
        if (res.code == 200) {
          if (res.data.attachment) {
            let arr = []
            res.data.attachment.split(',').forEach(m => {
              if (!m.includes('http')) {
                m = this.imgPath + m
              }
              arr.push(m)
            })
            res.data.attachment = arr.join(',')
          }
          this.info = res.data
          this.viewTitle = '查看报告单详情'
          this.viewOpen = true
          this.viewType = 'view'
        }
      })
    },
    // 质检审核
    handleQuality(row) {
      warrantyScrapDetail({
        warrantyScrapId: row.id
      }).then(res => {
        if (res.code == 200) {
          if (res.data.attachment) {
            let arr = []
            res.data.attachment.split(',').forEach(m => {
              if (!m.includes('http')) {
                m = this.imgPath + m
              }
              arr.push(m)
            })
            res.data.attachment = arr.join(',')
          }
          this.info = res.data
          this.qualityForm.approvalInfo = ''
          this.radioGroup = ''
          this.viewTitle = '质检审核'
          this.viewOpen = true
          this.viewType = 'quality'
        }
      })
    },
    // 去审核
    handleApprove(row, type) {
      warrantyScrapDetail({
        warrantyScrapId: row.id
      }).then(res => {
        if (res.code == 200) {
          if (res.data.attachment) {
            let arr = []
            res.data.attachment.split(',').forEach(m => {
              if (!m.includes('http')) {
                m = this.imgPath + m
              }
              arr.push(m)
            })
            res.data.attachment = arr.join(',')
          }
          this.info = res.data
          this.upForm.approvalInfo = ''
          if (type == 'bm') {
            this.viewTitle = '部门审核'
            // } else if (type == 'zjb') {
            //   this.viewTitle = '总经办审核'
          } else if (type == 'dsz') {
            this.viewTitle = '总经理审核'
          }
          this.viewOpen = true
          this.viewType = 'approval'
        }
      })
    },
    // 选择部门
    selectDept() {
      this.form.deptName = this.deptOptions.find(item => item.deptId == this.form.deptId).deptName
    },
    // 选择部门
    selectHead() {
      this.form.head = this.userList.find(item => item.userId == this.form.headId).realName || this.userList.find(item => item.userId == this.form.headId).nickName
    },
    // 提交
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          warrantyScrapAdd(this.form).then(res => {
            const { code, msg } = res
            if (code === 200) {
              this.$message.success('成功新建报修报废')
              this.getList()
              this.addListingOpen = false
            } else this.$message.error(msg)
          })
        }
      })
    },
    handleRadio() {
      this.$set(this.qualityForm, 'approvalInfo', this.radioGroup)
      // this.radioGroup = ''
    },
    handleSubmitUp(form) {
      const index = this.inCommonUse.findIndex(item => item == this.upForm.approvalInfo)
      this.$refs[form].validate(valid => {
        if (valid) {
          let data = {
            warrantyScrapId: this.info.id,
            approvalInfo: ''
          }
          if (this.viewType == 'quality') {
            data.approvalInfo = this.qualityForm.approvalInfo
          } else if (this.viewType == 'approval') {
            data.approvalInfo = this.upForm.approvalInfo
          }
          warrantyScrapApproval(data).then(res => {
            const { code, msg } = res
            if (code === 200) {
              this.$message.success('成功提交审核')
              this.getList()
              this.viewOpen = false
              this.$nextTick(() => {
                if (index == -1 && this.viewType == 'approval') {
                  this.$confirm('是否将该审核备注添加到常用审核备注中？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                  }).then(() => {
                    if (this.inCommonUse.length > 10) this.inCommonUse.shift()
                    this.inCommonUse.push(this.upForm.approvalInfo)
                    localStorage.setItem(this.userId + '.warrantyCommonUse', JSON.stringify(this.inCommonUse))
                  })
                }
              })
            } else this.$message.error(msg)
          })
        }
      })
    },
    // 点击常用
    handleInCommonUse(item) {
      if (this.upForm.approvalInfo) {
        this.upForm.approvalInfo = this.upForm.approvalInfo + ',' + item
      } else {
        this.upForm.approvalInfo = item
      }
    },
    // 删除常用
    handleDeleteInCommonUse(item) {
      this.inCommonUse = this.inCommonUse.filter(el => el != item)
      localStorage.setItem(this.userId + '.warrantyCommonUse', JSON.stringify(this.inCommonUse))
    },
    // 选择审核人
    handleChange(type, e) {
      console.log(type, e, this.approvalForm)
    },
    // 维护审核人
    handleApprovalSubmit() {
      this.$refs.approvalForm.validate(valid => {
        if (valid) {
          let data = []
          this.approvalForm.zj.map(el => {
            let obj = {
              approvalId: el,
              stage: 'zj'
            }
            data.push(obj)
          })
          this.approvalForm.bm.map(el => {
            let obj = {
              approvalId: el,
              stage: 'bm'
            }
            data.push(obj)
          })
          // this.approvalForm.zjb.map(el => {
          //   let obj = {
          //     approvalId: el,
          //     stage: 'zjb'
          //   }
          //   data.push(obj)
          // })
          this.approvalForm.dsz.map(el => {
            let obj = {
              approvalId: el,
              stage: 'dsz'
            }
            data.push(obj)
          })
          warrantyScrapApprovalUsersAdd(data).then(res => {
            const { code, msg } = res
            if (code === 200) {
              this.$message.success('设置成功')
              this.getApprovalList()
              this.getList()
              this.approvalOpen = false
            } else this.$message.error(msg)
          })
        }
      })
    },
    // 格式化附件内容，判断是否为pdf
    formatArchives(archives) {
      const arr = archives && archives.split(',')
      return arr.map(item => {
        return {
          url: item,
          isPdf: item.includes('.pdf')
        }
      })
    },
    // pdf预览
    handlePDFPreview(url) {
      this.pdfUrl = url
      this.pdfOpen = true
    },
    // 图片预览
    handlePictureCardPreview(url) {
      this.imgUrl = url
      this.imgOpen = true
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('warranty/scrap/export', { ...this.queryParams }, `报修报废_${new Date().getTime()}.xlsx`)
    },
    handleSelectionChange(selected) {
      this.selectChecked = selected
    },
    // 新增其他出库单
    handleCreateOutboundOrder() {
      if (this.selectChecked.length === 0) {
        this.$message.warning('请选择需要出库的报修报废物料')
        return
      }
      const productArr = this.selectChecked.map(item => {
        return { ...item.product, fqty: item.damaged, fentrynote: item.reason }
      })
      this.showOutboundCreate = true
      this.$nextTick(() => {
        this.$refs.outboundCreate.handleCreate(productArr)
      })
    },
    // 关闭选择操作栏
    handleCloseSelection() {
      this.selectChecked = []
      this.$nextTick(() => {
        if (this.$refs.table) {
          this.$refs.table.clearSelection()
        }
      })
    },
    handleCallBack() {
      this.showOutboundCreate = false
      this.handleCloseSelection()
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';

.el-button.organge {
  background-color: #f35d09;
  border-color: #f35d09;
  color: #fff;

  &:hover {
    opacity: 0.8;
  }
}

.tableBox {
  padding: 20px;
}

.custom-search {
  align-items: center;
  justify-content: space-between;
}

.custom-search-tip {
  background-color: #fef0f0;
  color: #f56c6c;
  padding: 10px 20px;
  font-size: 20px;
  display: inline-flex;
  align-items: center;

  span {
    font-size: 14px;
    margin-left: 10px;
    margin-right: 30px;
  }
}

.p20 {
  padding: 0 20px;
}

.addBox {
  .approvalBox {
    background: #f1f1f3;
    border-radius: 5px;
    padding: 20px;

    .approvalTitle {
      font-weight: 400;
      font-size: 14px;
      color: #999999;
      line-height: 20px;
      padding-bottom: 10px;
    }

    .approvalList {
      display: flex;
      align-items: center;

      .approvalItem {
        width: 120px;
        height: 36px;
        background: #f8f9fb;
        border-radius: 50px;
        border: 1px solid #cbd6e2;
        text-align: center;
        line-height: 36px;
        font-weight: 400;
        font-size: 14px;
        color: #666666;
      }

      .approvalIcon {
        font-weight: 400;
        font-size: 14px;
        color: #666666;
        margin: 0 10px;
      }
    }
  }
}

.finishTime_box {
  display: flex;
  align-items: center;
  justify-content: center;

  .time {
    margin-right: 7px;
  }

  .tips {
    flex-shrink: 0;
    width: 83px;
    background: url('~@/assets/images/listing_tips_bg.png') center no-repeat;
    background-size: 82.5px 22px;
    font-weight: 500;
    font-size: 12px;
    color: #f50e0e;
    display: flex;
    align-items: center;
    justify-content: center;

    img {
      width: 20px;
      height: 20px;
      margin-right: 7px;
    }
  }
}

.view_box {
  margin-bottom: 20px;

  .view_detail {
    border-radius: 5px;
    background: #f0f3f9;
    padding: 15px;
    box-sizing: border-box;

    .product_info {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      border-bottom: 1px solid #d9d9d9;
      padding-bottom: 17px;

      .product_info_img {
        width: 52px;
        height: 52px;
        border-radius: 5px;
        border: 1px solid #ededed;
        margin-right: 20px;

        img {
          width: 100%;
          height: 100%;
        }
      }

      .product_info_text {
        .product_info_text_title {
          font-weight: 500;
          font-size: 12px;
          color: #333333;
          line-height: 20px;
          margin-bottom: 15px;
        }

        .product_info_text_list {
          display: flex;
          align-items: center;
          justify-content: flex-start;

          .product_info_text_list_item {
            margin-right: 50px;
            display: flex;
            align-items: center;
            font-weight: 400;
            font-size: 12px;
            color: #999999;

            span {
              font-weight: 500;
              font-size: 14px;
              color: #333333;
              margin-left: 5px;
            }
          }
        }
      }
    }

    .number_info {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      padding: 17px 0;
      border-bottom: 1px solid #d9d9d9;

      .number_info_item {
        margin-right: 60px;
        display: flex;
        align-items: center;
        font-size: 12px;
        color: #999999;
        line-height: 20px;

        span {
          font-weight: 500;
          font-size: 14px;
          color: #333333;
          line-height: 20px;
          margin-left: 14px;
        }
      }
    }

    .imperfect_info {
      padding: 17px 0 5px;

      .imperfect_info_list {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        margin-bottom: 15px;

        .imperfect_info_list_item {
          display: flex;
          align-items: center;
          margin-right: 60px;
          font-weight: 400;
          font-size: 12px;
          color: #999999;
          line-height: 20px;

          span {
            font-weight: 500;
            font-size: 14px;
            color: #333333;
            line-height: 20px;
            margin-left: 15px;
          }
        }
      }

      .imperfect_info_img {
        display: flex;
        align-items: center;

        img {
          width: 145px;
          height: 99px;
          border-radius: 5px;
          margin-right: 10px;
        }
      }
    }
  }

  .quality_form {
    margin-top: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e2e6f3;

    .radio_box {
      .el-radio-button {
        margin-right: 10px;

        ::v-deep .el-radio-button__inner {
          padding: 8px 31px;
          background: #ecedf0;
          border: none;
          border-radius: 50px;
        }

        &.is-active {
          ::v-deep .el-radio-button__inner {
            padding: 8px 31px;
            background: #e9f0ff;
            border-radius: 50px;
            border: 1px solid #2e73f3;
            color: #2e73f3;
          }
        }
      }
    }
  }

  .quality_box {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 17px 0;

    .quality_box_item {
      display: flex;
      align-items: center;
      margin-right: 60px;
      font-weight: 400;
      font-size: 12px;
      color: #999999;
      line-height: 20px;

      span {
        font-weight: 500;
        font-size: 14px;
        color: #333333;
        line-height: 20px;
        margin-left: 15px;
      }
    }
  }

  .view_list {
    background: #f0f3f9;
    padding: 10px 20px;
    box-sizing: border-box;

    .approvalBox {
      background: #f1f1f3;
      border-radius: 5px;

      .approvalTitle {
        font-weight: 400;
        font-size: 14px;
        color: #999999;
        line-height: 20px;
        padding-bottom: 10px;
      }

      .approvalList {
        display: flex;
        align-items: center;

        .approvalItem {
          width: 120px;
          height: 36px;
          background: #f8f9fb;
          border-radius: 50px;
          border: 1px solid #cbd6e2;
          text-align: center;
          line-height: 36px;
          font-weight: 400;
          font-size: 14px;
          color: #666666;

          &.active {
            background: #e5eeff;
            border-radius: 50px;
            border: 1px solid #2e73f3;
            color: #2e73f3;
          }
        }

        .approvalIcon {
          font-weight: 400;
          font-size: 14px;
          color: #666666;
          margin: 0 10px;
        }
      }
    }

    .view_list_item {
      margin-top: 20px;
      border-bottom: 1px solid #9fa3b2;

      .view_list_item_top {
        display: flex;
        align-items: center;
        margin-bottom: 12px;

        img {
          width: 38px;
          height: 38px;
          margin-right: 10px;
        }

        span {
          &:nth-child(2) {
            font-weight: 500;
            font-size: 12px;
            color: #666666;
            margin-right: 50px;
          }

          &:nth-child(3) {
            font-weight: 400;
            font-size: 12px;
            color: #999999;
          }
        }
      }

      .view_list_item_bottom {
        padding: 0 48px 10px;
        box-sizing: border-box;

        &_text {
          font-weight: 400;
          font-size: 14px;
          color: #333333;
          margin-bottom: 18px;
        }

        &_file {
          display: flex;
          flex-wrap: wrap;
          align-items: center;

          &_pdf,
          &_img {
            width: 145px;
            height: 145px;
            margin-right: 10px;
            position: relative;
            border-radius: 5px;
            overflow: hidden;

            img {
              width: 100%;
              height: 100%;
            }

            &:hover {
              .view_list_item_bottom_file_bg {
                display: flex;
              }
            }
          }

          &_bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            align-items: center;
            justify-content: center;

            span {
              cursor: pointer;
              font-size: 20px;
              margin: 0 10px;
              color: #ffffff;
            }
          }
        }
      }
    }
  }
}

.custom-table ::v-deep {
  .el-table__body-wrapper .table-switch {
    .el-switch__label {
      width: calc(3em + 20px) !important;
    }

    .el-switch__core {
      width: calc(3em + 20px) !important;
    }
  }
}

.inCommonUse {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  margin-top: 20px;

  &_item {
    position: relative;
    margin-right: 10px;
    margin-bottom: 10px;
    line-height: normal;

    &:hover {
      .inCommonUse_item_close {
        display: block;
      }
    }

    &_close {
      font-size: 16px;
      display: none;
      position: absolute;
      right: -5px;
      top: -10px;
      cursor: pointer;

      &:hover {
        color: #2e73f3;
      }
    }

    &_text {
      line-height: 20px;
      padding: 8px 20px 8px 15px;
      background: #ecedf0;
      border-radius: 50px;
      font-size: 12px;
      color: #333333;
      cursor: pointer;

      &:hover {
        background: #2e73f3;
        color: #ffffff;
      }
    }
  }
}

.cascaderInfo {
  .el-cascader-panel {
    height: 225px;

    .el-cascader-menu {
      .el-cascader-menu__wrap {
        height: 100%;
        overflow-x: hidden;

        .el-cascader-menu__empty-text {
          color: #606266;
        }
      }

      &:nth-child(3n + 1) {
        background-color: #ffffff;

        .el-cascader-node:not(.is-disabled):focus,
        .el-cascader-node:not(.is-disabled):hover,
        .el-cascader-node:not(.is-disabled).in-active-path {
          background-color: #2e73f3;
          color: #ffffff;
        }
      }

      &:nth-child(3n + 2) {
        background-color: #f2f4f8;

        .el-cascader-node:not(.is-disabled):focus,
        .el-cascader-node:not(.is-disabled):hover,
        .el-cascader-node:not(.is-disabled).in-active-path {
          background-color: #d3dae7;
        }
      }

      &:nth-child(3n) {
        background-color: #d3dae7;

        .el-cascader-node:not(.is-disabled):focus,
        .el-cascader-node:not(.is-disabled):hover,
        .el-cascader-node:not(.is-disabled).in-active-path {
          background-color: #d3dae7;
          color: #2e73f3;
        }
      }
    }
  }
}

::v-deep {
  .collectAll {
    bottom: 10px;
    left: calc(50% - 200px);
    position: fixed;
    z-index: 50;
    transform: translateX(calc(-50% + 200px));
    -webkit-user-select: none;
    -ms-user-select: none;
    user-select: none;
    transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
    &-box {
      background: rgba(52, 54, 57, 1);
      border-radius: 12px;
      box-shadow: 0 -2px 8px rgba(38, 38, 38, 0.05), 0 10px 16px rgba(38, 38, 38, 0.08);
      height: 52px;
      padding: 7px 25px;
      position: relative;
      display: flex;
      align-items: center;
      color: #ffffff;
      font-size: 14px;
    }
    &-btn {
      margin-left: 15px;
      span {
        display: inline-block;
        padding: 0 10px;
        line-height: 36px;
        cursor: pointer;
        &:hover {
          background-color: rgba(255, 255, 255, 0.08) !important;
        }
      }
    }
    &-close {
      font-size: 20px;
      cursor: pointer;
      i {
        line-height: 36px;
        padding: 0 10px;
        &:hover {
          background-color: rgba(255, 255, 255, 0.08) !important;
        }
      }
    }
  }
}
</style>
