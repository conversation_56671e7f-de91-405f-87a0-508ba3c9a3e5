<template>
  <div>
    <div class="viewBox">
      <div class="viewBox-header">
        <div class="viewBox-container">
          <div class="viewBox-logo">
            <img src="../../public/imgs/<EMAIL>" class="viewBox-logo-img" />
            <div class="viewBox-logo-text">
              <b>自由客紧固件</b>
              一站式采购平台
            </div>
          </div>
        </div>
      </div>
      <div class="viewBox-container" v-if="!isOver">
        <el-form ref="info" :model="info" :rules="rules" label-width="0">
          <div class="viewBox-title">采购需求</div>
          <div class="viewBox-item-title">采购详情</div>
          <div class="viewBox-item-info">
            <div class="info-item" v-if="!isCustom">
              <span>标题</span>
              {{ info.title }}
            </div>
            <div class="info-item" v-if="!isCustom">
              <span>采购方</span>
              {{ info.demandType === 'enterprise' ? info.supplier.name : info.createBy }}
            </div>
            <div class="info-item">
              <span>有效时间</span>
              <Countdown :expireTime="info.expireTime" pattern="{d}{h}{m}" />
            </div>
            <div class="info-item">
              <span>创建时间</span>
              {{ info.createTime }}
            </div>
            <div class="info-item" v-if="!isCustom">
              <span>发布时间</span>
              {{ info.releaseTime }}
            </div>
            <div class="info-item">
              <span>供货截止</span>
              {{ info.supplyEndTime }}
            </div>
            <div class="info-item" v-if="!isCustom">
              <span>备注</span>
              {{ info.remark }}
            </div>
          </div>
          <div class="viewBox-item-title">采购产品列表</div>
          <el-table ref="detailTable" stripe :data="info.products" row-key="id" :key="key" style="width: 100%" class="custom-table custom-table-cell0">
            <el-table-column align="center" type="index" label="序号"></el-table-column>
            <el-table-column align="center" prop="productName" label="产品名称" show-overflow-tooltip min-width="130">
              <template slot-scope="{ row }">
                <span class="table-link" @click="hanleView(row)">{{ row.productName }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="picture1" label="图片" width="70">
              <template slot-scope="{ row }">
                <el-image :src="formatProductImg(row)" fit="cover" @click="handleImgView(row)" v-if="formatProductImg(row)">
                  <div slot="error" class="image-slot">
                    <i class="el-icon-picture-outline"></i>
                  </div>
                </el-image>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="specs" label="产品规格" show-overflow-tooltip></el-table-column>
            <el-table-column align="center" prop="model" label="产品型号" show-overflow-tooltip width="70"></el-table-column>
            <el-table-column align="center" prop="productCode" label="产品编码" show-overflow-tooltip v-if="!isCustom"></el-table-column>
            <el-table-column align="center" prop="materialQuality" label="材质" show-overflow-tooltip></el-table-column>
            <el-table-column align="center" prop="surface" label="表面处理" show-overflow-tooltip></el-table-column>
            <el-table-column align="center" prop="remark1" label="产品备注" show-overflow-tooltip v-if="!isCustom"></el-table-column>
            <el-table-column align="center" prop="quantity" label="采购数量" show-overflow-tooltip>
              <template slot-scope="{ row }">{{ row.quantity + row.unit }}</template>
            </el-table-column>
            <el-table-column align="center" label="可供应数量" show-overflow-tooltip width="80">
              <template slot-scope="scope">
                <el-form-item label-width="0" :prop="`products.${scope.$index}.maxNum`" :rules="rules.maxNum" class="primary">
                  <el-input v-model="scope.row.maxNum" size="small" placeholder="不限" disabled v-if="scope.row.isReply && !scope.row.edit">
                    <i slot="suffix" title="点击修改" class="el-icon-edit pointer" @click="scope.row.edit = !scope.row.edit"></i>
                  </el-input>
                  <el-input v-model="scope.row.maxNum" size="small" placeholder="不限" v-else />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column align="center" label="报价" show-overflow-tooltip width="170">
              <template slot-scope="scope">
                <el-form-item label-width="0" :prop="`products.${scope.$index}.amount`" :rules="rules.amount" class="price">
                  <el-input v-model="scope.row.amount" size="small" placeholder="" disabled v-if="scope.row.isReply && !scope.row.edit">
                    <i slot="suffix" title="点击修改" class="el-icon-edit" @click="scope.row.edit = !scope.row.edit"></i>
                  </el-input>
                  <template v-else>
                    <el-input v-model="scope.row.amount" size="small" placeholder="" :style="{ width: isCustom ? '100%' : '50%' }">
                      <span slot="suffix" class="inline-flex" v-if="!isCustom">元/{{ scope.row.replyUnit }}</span>
                      <span slot="suffix" class="inline-flex" v-if="isCustom">元</span>
                    </el-input>
                    <el-select style="width: 40%" size="small" v-model="scope.row.replyUnit" filterable allow-create v-if="!isCustom">
                      <el-option v-for="item in unitOptions" :key="item" :label="item" :value="item"></el-option>
                    </el-select>
                  </template>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column align="center" label="备注" show-overflow-tooltip width="130">
              <template slot-scope="scope">
                <el-form-item label-width="0" :prop="`products.${scope.$index}.remark`" :rules="rules.remark" class="primary">
                  <el-input v-model="scope.row.remark" size="small" placeholder="请输入备注" />
                </el-form-item>
              </template>
            </el-table-column>
          </el-table>
          <div class="viewBox-button">
            <button type="button" class="primary" @click="handleSubmit" v-if="!isReply">{{ isReply ? '修改报价' : '提交报价' }}</button>
          </div>
        </el-form>
      </div>
      <el-empty v-if="isOver" description="需方采购已结束，感谢您的支持！" />
    </div>
    <div class="wapBox-header">
      <div class="wapBox-logo">
        <img src="../../public/imgs/<EMAIL>" class="wapBox-logo-img" />
        <div class="wapBox-logo-text">
          <b>自由客紧固件</b>
          一站式采购平台
        </div>
      </div>
    </div>
    <div class="wapBox" v-if="!isOver">
      <el-form ref="wapInfo" :model="info" :rules="wapRules" label-width="0">
        <div class="wapBox-title">需求详情</div>
        <el-descriptions class="margin-top" :column="1" border :labelStyle="{ width: 'calc(5em + 22px)' }">
          <el-descriptions-item v-if="!isCustom">
            <template slot="label">需求标题</template>
            {{ info.title }}
          </el-descriptions-item>
          <el-descriptions-item v-if="!isCustom">
            <template slot="label">采购方</template>
            {{ info.demandType === 'enterprise' ? info.supplier.name : info.createBy }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">有效时间</template>
            <Countdown :expireTime="info.expireTime" pattern="{d}{h}{m}" />
          </el-descriptions-item>
          <el-descriptions-item v-if="!isCustom">
            <template slot="label">发布时间</template>
            {{ info.releaseTime }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">创建时间</template>
            {{ info.createTime }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">供货截止</template>
            {{ info.supplyEndTime }}
          </el-descriptions-item>
          <el-descriptions-item v-if="!isCustom">
            <template slot="label">备注</template>
            {{ info.remark }}
          </el-descriptions-item>
        </el-descriptions>
        <div class="wapBox-title">需求产品</div>
        <el-collapse @change="handleChange">
          <template v-for="(item, index) in info.products">
            <el-collapse-item :title="`需求产品${index + 1}：${item.productName}`" :name="item.productId" :key="item.productId + index">
              <el-descriptions :column="1" border :key="item.productId + index" :labelStyle="{ width: 'calc(5em + 22px)' }">
                <el-descriptions-item>
                  <template slot="label">产品名称</template>
                  {{ item.productName }}
                </el-descriptions-item>
                <el-descriptions-item>
                  <template slot="label">图片</template>
                  <el-image style="width: 100px; height: 100px" :src="formatProductImg(item)" fit="cover" :preview-src-list="[formatProductImg(item)]" v-if="formatProductImg(item)">
                    <div slot="error" class="image-slot">
                      <i class="el-icon-picture-outline"></i>
                    </div>
                  </el-image>
                </el-descriptions-item>
                <el-descriptions-item>
                  <template slot="label">产品规格</template>
                  {{ item.specs }}
                </el-descriptions-item>
                <el-descriptions-item>
                  <template slot="label">产品型号</template>
                  {{ item.model }}
                </el-descriptions-item>
                <el-descriptions-item v-if="!isCustom">
                  <template slot="label">产品编码</template>
                  {{ item.productCode }}
                </el-descriptions-item>
                <el-descriptions-item>
                  <template slot="label">材质</template>
                  {{ item.materialQuality }}
                </el-descriptions-item>
                <el-descriptions-item>
                  <template slot="label">表面处理</template>
                  {{ item.surface }}
                </el-descriptions-item>
                <el-descriptions-item>
                  <template slot="label">单位</template>
                  {{ item.unit }}
                </el-descriptions-item>
                <el-descriptions-item v-if="!isCustom">
                  <template slot="label">产品备注</template>
                  {{ item.remark1 }}
                </el-descriptions-item>
                <template v-if="active.includes(item.productId)">
                  <el-descriptions-item>
                    <template slot="label">产品规格</template>
                    {{ item.specs }}
                  </el-descriptions-item>
                  <el-descriptions-item>
                    <template slot="label">采购数量</template>
                    {{ item.quantity + item.unit }}
                  </el-descriptions-item>
                  <el-descriptions-item>
                    <template slot="label">可供应数量</template>
                    <el-form-item label-width="0" :prop="`products.${index}.maxNum`" :rules="wapRules.maxNum" class="price">
                      <el-input size="mini" type="number" v-model="item.maxNum" placeholder="不限" style="width: 80%" disabled v-if="item.isReply && !item.edit">
                        <i slot="suffix" title="点击修改" class="el-icon-edit pointer" @click="item.edit = !item.edit"></i>
                      </el-input>
                      <el-input size="mini" type="number" v-model="item.maxNum" placeholder="不限" style="width: 80%" v-else></el-input>
                    </el-form-item>
                  </el-descriptions-item>
                  <el-descriptions-item>
                    <template slot="label">您的报价</template>
                    <el-form-item label-width="0" :prop="`products.${index}.amount`" :rules="wapRules.amount" class="price">
                      <el-input size="mini" type="number" v-model="item.amount" placeholder="" style="width: 80%" disabled v-if="item.isReply && !item.edit">
                        <i slot="suffix" title="点击修改" class="el-icon-edit pointer" @click="item.edit = !item.edit"></i>
                      </el-input>
                      <el-input size="mini" type="number" v-model="item.amount" placeholder="" style="width: 50%" v-else>
                        <span slot="suffix" class="inline-flex" v-if="!isCustom">元/{{ item.replyUnit }}</span>
                        <span slot="suffix" class="inline-flex" v-if="isCustom">元</span>
                      </el-input>
                      <el-select style="width: 40%" size="mini" v-model="item.replyUnit" filterable allow-create v-if="!isCustom">
                        <el-option v-for="ite in unitOptions" :key="ite" :label="ite" :value="ite"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-descriptions-item>
                  <el-descriptions-item>
                    <template slot="label">备注</template>
                    <el-input size="mini" v-model="item.remark" placeholder="请输入备注" style="width: 80%"></el-input>
                  </el-descriptions-item>
                </template>
              </el-descriptions>
            </el-collapse-item>
            <el-descriptions :column="1" border :labelStyle="{ width: 'calc(5em + 22px)' }" :key="'n' + item.productId + index" v-if="!active.includes(item.productId)">
              <el-descriptions-item>
                <template slot="label">产品规格</template>
                {{ item.specs }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label">采购数量</template>
                {{ item.quantity + item.unit }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label">可供应数量</template>
                <el-form-item label-width="0" :prop="`products.${index}.maxNum`" :rules="wapRules.maxNum" class="price">
                  <el-input size="mini" type="number" v-model="item.maxNum" placeholder="不限" style="width: 80%" disabled v-if="item.isReply && !item.edit">
                    <i slot="suffix" title="点击修改" class="el-icon-edit pointer" @click="item.edit = !item.edit"></i>
                  </el-input>
                  <el-input size="mini" type="number" v-model="item.maxNum" placeholder="不限" style="width: 80%" v-else></el-input>
                </el-form-item>
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label">您的报价</template>
                <el-form-item label-width="0" :prop="`products.${index}.amount`" :rules="wapRules.amount" class="price">
                  <el-input size="mini" type="number" v-model="item.amount" placeholder="" style="width: 80%" disabled v-if="item.isReply && !item.edit">
                    <i slot="suffix" title="点击修改" class="el-icon-edit pointer" @click="item.edit = !item.edit"></i>
                  </el-input>
                  <el-input size="mini" type="number" v-model="item.amount" placeholder="" style="width: 50%" v-else>
                    <span slot="suffix" class="inline-flex" v-if="!isCustom">元/{{ item.replyUnit }}</span>
                    <span slot="suffix" class="inline-flex" v-if="isCustom">元</span>
                  </el-input>
                  <el-select style="width: 40%" size="mini" v-model="item.replyUnit" filterable allow-create v-if="!isCustom">
                    <el-option v-for="ite in unitOptions" :key="ite" :label="ite" :value="ite"></el-option>
                  </el-select>
                </el-form-item>
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label">备注</template>
                <el-input size="mini" v-model="item.remark" placeholder="请输入备注" style="width: 80%"></el-input>
              </el-descriptions-item>
            </el-descriptions>
          </template>
        </el-collapse>
        <div class="wapBox-button">
          <button type="button" class="primary" @click="handleWapSubmit" v-if="!isReply">{{ isReply ? '修改报价' : '提交报价' }}</button>
        </div>
      </el-form>
    </div>
    <div class="wapBox">
      <el-empty v-if="isOver" description="需方采购已结束，感谢您的支持！" />
    </div>

    <!-- 产品详情 -->
    <product-dialog ref="productInfo" :width="width"></product-dialog>
  </div>
</template>

<script>
import { supplierViewPurchasing, supplierReplyPruchasing, purchasingDemandShareDetail, purchasingDemandShareReply } from '@/api/purchase'
import { expireTimeFormat } from '@/utils'
import ProductDialog from '@/views/public/product/dialog'
import { isNumber } from '@/utils/validate'
import { detailCustomization, submitCustomization } from '@/api/customization'

export default {
  components: { ProductDialog },
  data() {
    return {
      requestId: undefined,
      info: {},
      rules: {
        amount: [{ validator: isNumber, message: '输入有误请重新输入', trigger: 'blur' }],
        maxNum: [{ validator: isNumber, message: '输入有误请重新输入', trigger: 'blur' }]
      },
      wapRules: {
        amount: [{ validator: isNumber, message: '输入有误请重新输入', trigger: 'blur' }],
        maxNum: [{ validator: isNumber, message: '输入有误请重新输入', trigger: 'blur' }]
      },
      key: 1,
      active: [],
      width: undefined,
      isOver: true,
      isReply: false,
      isLogin: false,
      unitOptions: ['吨', '千克', '个', '件', '套', '米', '支', '根', '袋', '托'],
      isCustom: true,
      source: ''
    }
  },
  created() {
    const token = this.$store.state.user.token
    if (token) this.isLogin = true
    this.source = this.$route.query.source
    let requestId = this.$route.query.requestId
    if (!requestId) {
      return
    }
    this.getInfo(requestId)
  },
  mounted() {
    const width = document.documentElement.clientWidth
    if (width > 1199) {
      this.width = '1040px'
    } else {
      this.width = '80%'
    }
    window.onresize = () => {
      return (() => {
        const width = document.documentElement.clientWidth
        if (width > 1199) {
          this.width = '1040px'
        } else {
          this.width = '80%'
        }
      })()
    }
  },
  beforeDestroy() {
    window.onresize = null
  },
  methods: {
    expireTimeFormat,
    // 判断是否已过有效时间
    isOverTime(time) {
      const start = new Date().getTime()
      const end = new Date(time).getTime()
      return start > end
    },
    async getInfo(requestId) {
      this.requestId = requestId
      const prefix = requestId.slice(0, 3)
      let res
      if (this.source === 'share') {
        const shareData = await purchasingDemandShareDetail({ demandId: requestId })
        res = { code: shareData.code, msg: shareData.msg, data: shareData.data.demand }
        this.isCustom = false
      } else {
        if (prefix == 100) {
          res = await detailCustomization({ requestId })
          this.isCustom = true
        } else {
          res = await supplierViewPurchasing({ requestId })
          this.isCustom = false
        }
      }
      const { code, data, msg } = res
      if (code === 200) {
        let isReply = 0
        if (prefix == 100) {
          let productData = { ...data }
          if (productData.hasOwnProperty('sendDO') && productData.sendDO.hasOwnProperty('replyTime') && productData.sendDO.replyTime) {
            productData.isReply = productData.sendDO.isReply
            productData.edit = false
            productData.maxNum = productData.sendDO.maxNum == '2147483647' ? '不限量' : productData.sendDO.maxNum
            productData.amount = productData.sendDO.amount
            productData.remark = productData.sendDO.remark
            isReply = 1
          }
          let products = data.hasOwnProperty('product') ? { ...data.product, ...productData } : { ...productData }
          products.quantity = data.quantity || ''
          products.picture1_oss = products.picture_oss || ''
          products.picture1 = products.picture || ''
          data.products = [products]
        } else {
          data.products.map(item => {
            item.replyUnit = item.unit
            item.edit = false
            isReply += item.isReply ? 1 : 0
          })
        }
        this.isReply = !!isReply
        this.isOver = false
        this.info = data
        this.key = Math.random()
        // prettier-ignore
        if (this.isOverTime(data.expireTime)) {
          this.$alert('该需求已过期，请您下次尽快回复，期待您的报价，期待与您的合作。点击确定返回首页。', '系统提示', {
            type: 'info'
          }).then(() => {
            this.$router.push('/')
          }).catch(() => {})
        }
      } else this.$message.error(msg)
    },
    // 产品详情
    hanleView(row) {
      this.$refs.productInfo.handleView(row)
    },
    // 图片预览
    handleImgView(row) {
      this.$refs.productInfo.handleImgView(row)
    },
    handleChange(val) {
      this.active = val
    },
    // 提交报价（PC）
    handleSubmit() {
      this.$refs['info'].validate(valid => {
        if (valid) {
          if (this.source === 'share') {
            let data = { demandId: this.requestId, reply: [] }
            this.info.products.map(item => {
              if (item.amount) {
                data.reply.push({
                  amount: item.amount,
                  maxNum: item.maxNum,
                  demandProductId: item.demandProductId,
                  remark: item.remark,
                  replyUnit: item.replyUnit
                })
              }
            })
            // prettier-ignore
            purchasingDemandShareReply(data).then(res => {
              if (res.code === 200) {
                this.$alert('需方已经收到贵公司提供的报价单，有异议的地方会及时与贵公司进行商洽，恭祝您合作愉快！', '系统提示', {
                  type: 'success'
                }).then(() => {
                  if (this.source) this.$router.push('/')
                  else this.$router.push('/?type=remove')
                }).catch(() => {})
              } else {
                this.$message.error(res.msg)
              }
            })
          } else {
            let data = { requestId: this.requestId, reply: [] }
            this.info.products.map(item => {
              if (item.amount) {
                data.reply.push({
                  amount: item.amount,
                  maxNum: item.maxNum,
                  flowingId: item.flowingId,
                  remark: item.remark,
                  replyUnit: item.replyUnit
                })
              }
            })
            if (this.isCustom) {
              const newData = { requestId: this.requestId, amount: data.reply[0].amount, maxNum: data.reply[0].maxNum, remark: data.reply[0].remark }
              // prettier-ignore
              submitCustomization(newData).then(res => {
                if (res.code === 200) {
                  this.$alert('需方已经收到贵公司提供的报价单，有异议的地方会及时与贵公司进行商洽，恭祝您合作愉快！', '系统提示', {
                    type: 'success'
                  }).then(() => {
                    if (this.source) this.$router.push('/')
                    else this.$router.push('/?type=remove')
                  }).catch(() => {})
                } else {
                  this.$message.error(res.msg)
                }
              })
            } else {
              // prettier-ignore
              supplierReplyPruchasing(data).then(res => {
                if (res.code === 200) {
                  this.$alert('需方已经收到贵公司提供的报价单，有异议的地方会及时与贵公司进行商洽，恭祝您合作愉快！', '系统提示', {
                    type: 'success'
                  }).then(() => {
                    if (this.source) this.$router.push('/')
                    else this.$router.push('/?type=remove')
                  }).catch(() => {})
                } else {
                  this.$message.error(res.msg)
                }
              })
            }
          }
        }
      })
    },
    // 提交报价（移动端）
    handleWapSubmit() {
      this.$refs['wapInfo'].validate(valid => {
        if (valid) {
          if (this.source === 'share') {
            let data = { demandId: this.requestId, reply: [] }
            this.info.products.map(item => {
              if (item.amount) {
                data.reply.push({
                  amount: item.amount,
                  maxNum: item.maxNum,
                  demandProductId: item.demandProductId,
                  remark: item.remark,
                  replyUnit: item.replyUnit
                })
              }
            })
            // prettier-ignore
            purchasingDemandShareReply(data).then(res => {
              if (res.code === 200) {
                this.$alert('需方已经收到贵公司提供的报价单，有异议的地方会及时与贵公司进行商洽，恭祝您合作愉快！', '系统提示', {
                  type: 'success'
                }).then(() => {
                  location.reload()
                }).catch(() => {})
              } else {
                this.$message.error(res.msg)
              }
            })
          } else {
            let data = { requestId: this.requestId, reply: [] }
            this.info.products.map(item => {
              if (item.amount)
                data.reply.push({
                  amount: item.amount,
                  maxNum: item.maxNum,
                  flowingId: item.flowingId,
                  remark: item.remark,
                  replyUnit: item.replyUnit
                })
            })
            if (this.isCustom) {
              const newData = { requestId: this.requestId, amount: data.reply[0].amount, maxNum: data.reply[0].maxNum, remark: data.reply[0].remark }
              // prettier-ignore
              submitCustomization(newData).then(res => {
                if (res.code === 200) {
                  this.$alert('需方已经收到贵公司提供的报价单，有异议的地方会及时与贵公司进行商洽，恭祝您合作愉快！', '系统提示', {
                    type: 'success'
                  }).then(() => {
                    location.reload()
                  }).catch(() => {})
                } else {
                  this.$message.error(res.msg)
                }
              })
            } else {
              // prettier-ignore
              supplierReplyPruchasing(data).then(res => {
                if (res.code === 200) {
                  this.$alert('需方已经收到贵公司提供的报价单，有异议的地方会及时与贵公司进行商洽，恭祝您合作愉快！', '系统提示', {
                    type: 'success'
                  }).then(() => {
                    location.reload()
                  }).catch(() => {})
                } else {
                  this.$message.error(res.msg)
                }
              })
            }
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
.viewBox {
  min-height: 100vh;
  background-color: #f9f9f9;
  &-container {
    max-width: 1200px;
    margin: 0 auto;
    ::v-deep .custom-table {
      box-shadow: none;
      .el-form-item {
        margin-top: 10px !important;
        margin-bottom: 10px !important;
        .el-form-item__error {
          top: 95%;
          padding-top: 0;
        }
        .el-input__inner {
          text-align: center;
        }
        &.primary {
          .el-input__inner:focus {
            color: $blue;
          }
        }
        &.price {
          //.el-input__inner {
          //  border-color: #ec4545;
          //  color: #ec2454;
          //}
          //.el-input__suffix {
          //  cursor: pointer;
          //  color: #ec2454;
          //}
        }
      }
    }
  }
  &-header {
    height: 90px;
    background-color: $blue;
    padding-left: 14px;
  }
  &-logo {
    display: inline-flex;
    height: 90px;
    align-items: center;
    &-img {
      width: 52px;
      height: 50px;
      margin-right: 10px;
    }
    &-text {
      display: inline-flex;
      flex-direction: column;
      color: $white;
      b {
        font-size: 20px;
        font-weight: 500;
        line-height: 23px;
      }
      span {
        font-size: 20px;
        font-weight: 500;
        line-height: 19px;
      }
    }
  }
  &-title {
    font-size: 18px;
    color: $info;
    background-color: #eef0f8;
    line-height: 50px;
    padding-left: 30px;
  }
  &-item {
    &-title {
      font-size: 14px;
      color: #999999;
      line-height: 50px;
      padding-left: 30px;
    }
    &-info {
      padding: 11px 30px;
      background-color: #f0f3f9;
      overflow: hidden;
      .info-item {
        line-height: 32px;
        float: left;
        min-width: 320px;
        padding-right: 15px;
        font-size: 14px;
        font-weight: 500;
        color: $font;
        span {
          display: inline-block;
          width: 68px;
          font-size: 12px;
          color: $info;
        }
        &:nth-child(2n + 1) {
          content: '';
          clear: both;
        }
      }
    }
  }
  &-button {
    display: flex;
    justify-content: flex-end;
    padding: 30px 0;
    .primary {
      width: 270px;
      height: 50px;
      line-height: 50px;
      text-align: center;
      font-size: 16px;
      font-weight: 500;
      color: $white;
      border: 0;
      border-radius: 5px;
      cursor: pointer;
      background-color: $blue;
      &:hover {
        opacity: 0.8;
        background-color: $blue;
      }
    }
  }
}
@media (max-width: 1199px) {
  .viewBox-container {
    padding: 0 10px;
  }
}
@media (max-width: 767px) {
  .viewBox {
    display: none;
  }
}
@media (min-width: 768px) {
  .wapBox {
    display: none;
    &-header {
      display: none;
    }
  }
}
.wapBox {
  padding: 10px;
  &-header {
    height: 80px;
    background-color: $blue;
    padding-left: 10px;
  }
  &-logo {
    display: inline-flex;
    height: 100%;
    align-items: center;
    &-img {
      width: 52px;
      height: 50px;
      margin-right: 10px;
    }
    &-text {
      display: inline-flex;
      flex-direction: column;
      color: $white;
      b {
        font-size: 20px;
        font-weight: 500;
        line-height: 23px;
      }
      span {
        font-size: 20px;
        font-weight: 500;
        line-height: 19px;
      }
    }
  }
  &-title {
    line-height: 3;
  }
  .price {
    margin-bottom: 5px;
  }
  &-button {
    text-align: center;
    margin: 30px 0;
    .primary {
      width: 270px;
      height: 50px;
      line-height: 50px;
      text-align: center;
      font-size: 16px;
      font-weight: 500;
      color: $white;
      border: 0;
      border-radius: 5px;
      cursor: pointer;
      background-color: $blue;
    }
  }
  ::v-deep {
    .el-collapse-item__content {
      padding-bottom: 10px;
    }
  }
}
</style>
