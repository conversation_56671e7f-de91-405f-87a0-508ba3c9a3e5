<template>
  <div class="form-box">
    <el-collapse v-model="activeNames">
      <el-collapse-item name="1">
        <template slot="title"> 需求详情</template>
        <el-descriptions class="margin-top" :column="1" border v-if="form2">
          <el-descriptions-item>
            <template slot="label"> 需求 </template>
            {{ form2.title }}
          </el-descriptions-item>

          <el-descriptions-item>
            <template slot="label"> 金额 </template>
            {{ form2.budget }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 开始时间 </template>
            <el-tag size="small">{{
                parseTime(form2.startTime, "{y}-{m}-{d}:{h}:{m}:{s}")
              }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 结束时间 </template>
            <el-tag size="small">{{
                parseTime(form2.endTime, "{y}-{m}-{d}:{h}:{m}:{s}")
              }}</el-tag>
          </el-descriptions-item>
          <!-- <el-descriptions-item>
            <template slot="label"> 概况 </template>
            {{ parseProfile(form2.profile) }}
            <el-button type="text" @click="downloadF(form2.profile)"
              >下载</el-button
            >
          </el-descriptions-item> -->
        </el-descriptions>
      </el-collapse-item>
    </el-collapse>

    <template v-for="(item,index) in tableData">
      <el-descriptions :column="1" border style="margin-top: 10px;">
        <el-descriptions-item>
            <template slot="label">需求序号 </template>
            第{{ index+1 }}个
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 产品名称 </template>
            {{ item.productName }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 规格 </template>
            {{ item.specs }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 型号 </template>
            {{ item.model }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 材质 </template>
            {{ item.materialQuality }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 表面 </template>
            {{ item.surface }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 图片 </template>
            <img style="width:100px;height: 100px;" :src="up_file+item.picture1" alt="">
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 图纸 </template>
            <a :href="up_file+item.draw"><el-button size="mini">点击查看</el-button></a>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 属性 </template>
            {{ item.attribute }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 价格 </template>
            {{ item.price }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 单位 </template>
            {{ item.unit }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 数量 </template>
            {{ item.count }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 您的报价 </template>
            <input :disabled="form2.isReply"  v-model="item.amount" type="number" />元
          </el-descriptions-item>
        </el-descriptions>

    </template>

    <!-- <el-table
      :data="tableData"
      style="width: 100%">
      <el-table-column
        prop="productName"
        label="产品名称">
      </el-table-column>
      <el-table-column
        prop="specs"
        label="规格">
      </el-table-column>
      <el-table-column
        prop="model"
        label="型号">
        <template slot-scope="scope">
          {{scope.row.model}}
        </template>
      </el-table-column>
      <el-table-column
        prop="materialQuality"
        label="材质">
      </el-table-column>
      <el-table-column
        prop="surface"
        label="表面">
      </el-table-column>
      <el-table-column
        prop="picture1"
        label="图片"
        width="180">
        <template slot-scope="scope">
          <img style="width:100px;height: 100px;" :src="up_file+scope.row.picture1" alt="">
        </template>
      </el-table-column>

      <el-table-column
        prop="attribute"
        label="属性"
        width="180">
      </el-table-column>

      <el-table-column
        prop="price"
        label="价格"
        width="180">
      </el-table-column>

      <el-table-column
        prop="unit"
        label="单位"
        width="180">
      </el-table-column>
      <el-table-column
        prop="attribute"
        label="您的报价"
        width="180">
        <template slot-scope="scope">
          <input :disabled="form2.isReply"  v-model="scope.row.amount" />
        </template>
      </el-table-column>
    </el-table> -->


    <div v-if="form2.isReply == false" style="width: 200px;float: right;margin-top: 20px;">
      <el-button type="primary" class="btn" @click="suba"
      >提 交</el-button>
    </div>

    <div style="height:50px;"></div>

  </div>
</template>
<script>
import { getDemand,formAdd } from "@/api/purchase/demand";
// import { formAdd } from "@/api/system/response";
// import download from "@/plugins/download";
export default {
  name: 'formFilling',
  data() {
    return {
      activeNames: ["1"],
      // 验证规则
      rules: {
        supplie: [
          {
            required: true,
            message: " ",
            pattern: "[^ \x22]+",
            trigger: "blur",
          },
        ],

        phone: [
          {
            required: true,
            message: " ",
            pattern: "[^ \x22]+",
            trigger: "blur",
          },
        ],

        title: [
          {
            required: true,
            message: " ",
            pattern: "[^ \x22]+",
            trigger: "blur",
          },
        ],

        amount: [
          {
            required: true,
            message: " ",
            pattern: "[^ \x22]+",
            trigger: "blur",
          },
        ],

        remark: [
          {
            required: true,
            message: " ",
            pattern: "[^ \x22]+",
            trigger: "blur",
          },
        ],
      },
      // 表单
      form: {
        supplie: null,
        phone: null,
        title: null,
        amount: null,
        remark: null,
        createTime: null,
      },
      requestId:'',

      form2: null,

      tableData: [],
      up_file:  process.env.VUE_APP_BASE_API,
    };
  },
  created() {
    this.requestId = this.$route.query.requestId;
    // // this.form.did = id;
    // // this.form.supplierId = supplierId;
    // if (!id) {
    //   return;
    // }
    this.ready();
    // const { total, data } = await getDemand({
    //   demandId: requestId
    // });
    // data.picture1 += process.env.VUE_APP_BASE_API;
    // this.form2 = data;
    // this.form.title = this.form2.title;
  },
  methods: {
    ready(){
      getDemand({
        requestId: this.requestId,
      }).then((res) => {
        this.form2 = res.data;
        this.tableData = res.data.productList;
        // this.form.title = this.form2.title;
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          formAdd(this.form).then((response) => {
            this.$modal.msgSuccess("提交成功");
          });
        }
      });
    },
    suba(){
      let that = this;
      let lista = JSON.parse(JSON.stringify(this.tableData));
      let listb = [];
      lista.forEach((item,index)=>{
        listb.push({
          count:  item['count'],
          amount:  item['amount'],
          price:  item['price'],
          productId:  item['productId'],
          productName:  item['productName'],
          unit:  item['unit'],
        })
      })
      formAdd({
        requestId:  this.requestId,
        products: listb
      }).then(() => {
        this.$modal.msgSuccess('提交成功')
        this.ready();
      })
    },
    // 格式化url
    parseProfile(name) {
      if (!name) return;
      const newUrl = name.substring(name.lastIndexOf("/") + 1, name.length);
      const arr1 = newUrl.split("_");
      const arr2 = newUrl.split(".");
      return arr1[0] + "." + arr2[1];
    },
  },

};
</script>
<style scoped lang="scss">
.form-box {
  height: 100%;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 20px;
  .btn {
    width: 100%;
  }
}
.el-collapse {
  width: 100%;
  margin-bottom: 50px;
}

.box{
  width:100%;
  height: 300px;
  border:1px solid;
}
</style>
