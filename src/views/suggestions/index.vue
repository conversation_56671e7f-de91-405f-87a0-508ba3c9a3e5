<template>
  <div class="newBox bgcf9 vh-85">
    <div style="padding: 20px">
      <el-table v-loading="loading" :data="list" class="custom-table" stripe style="width: 100%">
        <el-table-column type="index" label="序号" align="center" width="50"></el-table-column>
        <el-table-column prop="createBy" label="用户昵称" align="center" width="180"></el-table-column>
        <el-table-column prop="suggestion" label="建议" align="center">
          <template slot-scope="{ row }">
            <div v-html="row.suggestion"></div>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="提交时间" align="center" width="150"></el-table-column>
      </el-table>
      <div class="custom-pagination">
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
      </div>
    </div>
  </div>
</template>
<script>
import { getSuggestionList } from '@/api/system/suggestion'

export default {
  data() {
    return {
      loading: true,
      list: [],
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.loading = true
      getSuggestionList(this.queryParams).then(res => {
        const { code, msg, rows, total } = res
        if (code === 200) {
          this.list = rows
          this.total = total
          this.loading = false
        } else this.$message.error(msg)
      })
    }
  }
}
</script>
<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';
</style>
