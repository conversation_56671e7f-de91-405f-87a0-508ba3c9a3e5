<template>
  <div>
    <el-dialog :title="title" :visible.sync="open" width="1150px" :before-close="beforeClose" class="custom-dialog">
      <div style="padding: 0 20px">
        <el-form ref="form" :model="form" label-width="100px" class="custom-form">
          <el-row :gutter="20" style="display: flex; flex-wrap: wrap" v-if="!isFinish">
            <el-col :span="12">
              <el-form-item label="客户名称" prop="customerName">
                <customer-search-select :showLabel="false" :keyword.sync="form.customerName" style="width: 100%" isBack @callBack="handleCustomerSearchSelect($event)" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="项目名称" prop="projectName">
                <el-input v-model="form.projectName" placeholder="请输入项目名称" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="项目地址" prop="projectAddress">
                <el-input v-model="form.projectAddress" placeholder="请输入项目地址" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="合同编号" prop="contractNumber">
                <el-input v-model="form.contractNumber" placeholder="请输入合同编号" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="项目备注" prop="projectRemark">
                <el-input type="textarea" v-model="form.projectRemark" placeholder="请输入项目备注" :autosize="{ minRows: 4, maxRows: 6 }" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="项目开工时间" prop="startDate">
                <el-date-picker v-model="form.startDate" type="date" placeholder="请选择项目开工时间" value-format="yyyy-MM-dd" style="width: 100%" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="contractEndDate" class="two-line">
                <span slot="label">
                  合同约定
                  <br />
                  完成时间
                </span>
                <el-date-picker v-model="form.contractEndDate" type="date" placeholder="请选择合同约定完成时间" value-format="yyyy-MM-dd" style="width: 100%" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="合同价款" prop="contractAmount">
                <el-input v-model="form.contractAmount" placeholder="请输入合同价款" clearabl>
                  <span slot="suffix">元</span>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="合同产品数量" prop="contractProductQuantity">
                <el-input v-model="form.contractProductQuantity" placeholder="请输入产品数量" type="number" min="0" style="width: 70%" />
                <el-select v-model="form.contractProductUnit" placeholder="单位" style="width: 30%">
                  <el-option label="台" value="台"></el-option>
                  <el-option label="套" value="套"></el-option>
                  <el-option label="件" value="件"></el-option>
                  <el-option label="个" value="个"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="expectedIncomeType" class="two-line">
                <span slot="label">
                  预计结算
                  <br />
                  收入方式
                </span>
                <el-input v-model="form.expectedIncomeType" placeholder="请选择预计结算收入方式" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="预计成本" prop="expectedCost">
                <el-input v-model="form.expectedCost" placeholder="请输入预计成本" clearable>
                  <span slot="suffix">元</span>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="成本利润率" prop="costProfitRate">
                <el-input v-model="form.costProfitRate" placeholder="请输入成本利润率" clearable>
                  <span slot="suffix">%</span>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="已安装数量" prop="installedQuantity">
                <el-input v-model="form.installedQuantity" placeholder="请输入已安装数量" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="预计居间费" prop="expectedAgencyFee">
                <el-input v-model="form.expectedAgencyFee" placeholder="请输入预计居间费" clearable>
                  <span slot="suffix">元</span>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="项目状态" prop="projectStatus">
                <el-select v-model="form.projectStatus" placeholder="请选择项目状态" clearable style="width: 100%">
                  <el-option label="未开工" value="未开工"></el-option>
                  <el-option label="进行中" value="进行中"></el-option>
                  <el-option label="已完工" value="已完工"></el-option>
                  <el-option label="已结算" value="已结算"></el-option>
                  <el-option label="已终止" value="已终止"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="审核人" prop="auditUser">
                <div class="audit-user">
                  <div class="audit-user-item" :class="{ active: auditUserActiveStates[index] }" v-for="(item, index) in form.auditUser" :key="index">
                    <span class="audit-user-label">{{ index + 1 }}</span>
                    <el-cascader v-model="item.value" :options="tempAuditUser" placeholder="请选择审核人" clearable :show-all-levels="false" class="audit-user-cascader" @visible-change="handleAuditUserVisibleChange($event, index)"></el-cascader>
                    <i class="audit-user-icon el-icon-close" @click="handleRemoveAuditUser(index)"></i>
                  </div>
                  <div class="audit-user-add" @click="handleAddAuditUser">
                    <i class="el-icon-plus"></i>
                    <span>增加审批人</span>
                  </div>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="抄送人" prop="ccUser">
                <el-tag closable @close="handleClose(tag)" class="crm-tag border-none">某某某</el-tag>
                <el-tag closable @close="handleClose(tag)" class="crm-tag border-none">某某某</el-tag>
                <el-button type="text" icon="el-icon-plus" style="margin-left: 15px">选择人员</el-button>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-row class="statement-create-info">
                <el-col :span="12" class="statement-create-info-item statement-create-info-item-border">
                  <span class="statement-create-info-item-label">本月收款：</span>
                  <span class="statement-create-info-item-value">100000元</span>
                </el-col>
                <el-col :span="12" class="statement-create-info-item">
                  <span class="statement-create-info-item-label">累计收款：</span>
                  <span class="statement-create-info-item-icon">
                    <i class="el-icon-search"></i>
                    <span>去查询</span>
                  </span>
                </el-col>
                <el-col :span="12" class="statement-create-info-item statement-create-info-item-border statement-create-info-item-background">
                  <span class="statement-create-info-item-label">本月成本：</span>
                  <span class="statement-create-info-item-value">100000元</span>
                </el-col>
                <el-col :span="12" class="statement-create-info-item statement-create-info-item-background">
                  <span class="statement-create-info-item-label">累计成本：</span>
                  <span class="statement-create-info-item-icon">
                    <i class="el-icon-search"></i>
                    <span>去查询</span>
                  </span>
                </el-col>
                <el-col :span="24" class="statement-create-info-item">
                  <span class="statement-create-info-item-label">预计应收款：</span>
                  <span class="statement-create-info-item-icon">
                    <i class="el-icon-plus"></i>
                    <span>添加</span>
                  </span>
                </el-col>
              </el-row>
            </el-col>
          </el-row>
          <el-row :gutter="20" style="display: flex; flex-wrap: wrap" v-if="isFinish">
            <el-col :span="24">
              <el-form-item label="项目完成详细" prop="projectCompletionDetail">
                <el-input type="textarea" v-model="form.projectCompletionDetail" placeholder="请输入项目完成详细" :autosize="{ minRows: 4, maxRows: 6 }" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="附件提交" prop="attachment">
                <image-upload isShowTip :fileSize="5" v-model="form.attachment" :file-type="fileType" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn" @click="handleCancel()">取 消</el-button>
        <el-button class="custom-dialog-btn primary" @click="handleSubmit" :loading="loading">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import CustomerSearchSelect from '@/components/SearchSelect/customer'

export default {
  name: 'StatementCreate',
  components: { CustomerSearchSelect },
  data() {
    return {
      open: false,
      title: '新增项目结算',
      form: {},
      rules: {},
      loading: false,
      // 审核人cascader开启状态
      auditUser1Active: false,
      auditUser2Active: false,
      // 审核人cascader激活状态跟踪
      auditUserActiveStates: [],
      // 临时审核人
      tempAuditUser: [
        {
          label: '研发部',
          value: '研发部',
          children: [
            { label: '张伟', value: '张伟' },
            { label: '李强', value: '李强' },
            { label: '王磊', value: '王磊' },
            { label: '赵敏', value: '赵敏' },
            { label: '陈思', value: '陈思' }
          ]
        },
        {
          label: '销售部',
          value: '销售部',
          children: [
            { label: '刘洋', value: '刘洋' },
            { label: '孙婷', value: '孙婷' },
            { label: '周杰', value: '周杰' },
            { label: '吴昊', value: '吴昊' },
            { label: '郑爽', value: '郑爽' }
          ]
        },
        {
          label: '财务部',
          value: '财务部',
          children: [
            { label: '钱坤', value: '钱坤' },
            { label: '孙丽', value: '孙丽' },
            { label: '李娜', value: '李娜' },
            { label: '周宇', value: '周宇' },
            { label: '郑伟', value: '郑伟' }
          ]
        },
        {
          label: '运营部',
          value: '运营部',
          children: [
            { label: '王芳', value: '王芳' },
            { label: '李明', value: '李明' },
            { label: '赵云', value: '赵云' },
            { label: '陈晨', value: '陈晨' },
            { label: '刘敏', value: '刘敏' }
          ]
        },
        {
          label: '仓储部',
          value: '仓储部',
          children: [
            { label: '张鹏', value: '张鹏' },
            { label: '李雪', value: '李雪' },
            { label: '王刚', value: '王刚' },
            { label: '赵雷', value: '赵雷' },
            { label: '陈静', value: '陈静' }
          ]
        },
        {
          label: '总经办',
          value: '总经办',
          children: [
            { label: '李总', value: '李总' },
            { label: '王总', value: '王总' },
            { label: '陈总', value: '陈总' },
            { label: '赵总', value: '赵总' },
            { label: '孙总', value: '孙总' }
          ]
        }
      ],
      fileType: ['pdf', 'jpg', 'png', 'jpeg'],
      isFinish: false // 是否完成结算
    }
  },
  methods: {
    // 表单重置
    reset() {
      this.form = {
        auditUser: [{ value: ['总经办', '王总'] }, { value: ['财务部', '李娜'] }, { value: ['销售部', '刘洋'] }]
      }
      this.resetForm('form')
      this.auditUserActiveStates = []
      this.isFinish = false
    },
    // 打开弹窗
    handleCreate() {
      this.reset()
      this.title = '新增项目结算'
      this.open = true
      this.auditUserActiveStates = new Array(this.form.auditUser.length).fill(false)
    },
    // 修改
    handleEdit(row) {
      this.reset()
      this.form = row
      this.title = '修改项目结算'
      this.open = true
    },
    // 完成结算
    handleFinish(row) {
      this.reset()
      this.form = row
      this.isFinish = true
      this.title = '完成结算'
      this.open = true
    },
    // 客户搜索
    handleCustomerSearchSelect(data) {
      this.form.customerId = data.Number
      this.form.customerName = data.Name
    },
    // 关闭弹窗
    beforeClose() {
      this.handleCancel()
    },
    // 取消
    handleCancel(flag = false) {
      this.open = false
      this.$emit('callBack', flag)
    },
    // 提交
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.handleCancel(true)
          }, 1000)
        }
      })
    },
    // 增加审核人
    handleAddAuditUser() {
      this.form.auditUser.push({ value: null })
      this.auditUserActiveStates.push(false)
    },
    // 删除审核人
    handleRemoveAuditUser(index) {
      this.form.auditUser.splice(index, 1)
      this.auditUserActiveStates.splice(index, 1)
    },
    // 处理审核人cascader显示状态变化
    handleAuditUserVisibleChange(visible, index) {
      this.$set(this.auditUserActiveStates, index, visible)
    }
  }
}
</script>
<style scoped lang="scss">
@import '@/assets/styles/custom-new.scss';
::v-deep {
  .two-line {
    .el-form-item__label {
      padding-top: 0 !important;
      padding-bottom: 0 !important;
    }
  }
  .audit-user {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
    &-item {
      display: flex;
      align-items: center;
      border: 1px solid #cbd7e2;
      border-radius: 5px;
      position: relative;
      &.active {
        border-color: #2e73f3;
        .audit-user-label {
          background-color: #2e73f3;
        }
      }
    }
    &-label {
      font-size: 14px;
      color: #fff;
      background-color: #cbd6e2;
      border-radius: 5px 0 0 5px;
      padding: 0 8px;
      overflow: hidden;
    }
    &-cascader {
      width: 125px;
      .el-input__inner {
        border: 0 !important;
        background-color: transparent !important;
        box-shadow: none !important;
      }
    }
    &-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 16px;
      height: 16px;
      border-radius: 50%;
      background-color: rgba(0, 0, 0, 0.8);
      color: #fff;
      position: absolute;
      top: -8px;
      right: -8px;
      cursor: pointer;
      &:hover {
        background-color: #2e73f3;
      }
    }
    &-add {
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      gap: 10px;
      border: 1px dashed #2e73f3;
      border-radius: 5px;
      padding: 0 15px;
      background-color: #dbe8ff;
      color: #2e73f3;
      font-size: 14px;
      &:hover {
        background-color: #2e73f3;
        color: #fff;
      }
    }
  }
  .crm-tag {
    border-color: #bfd6ff;
    position: relative;
    padding-right: 15px;
    margin-right: 10px;
    &.border-none {
      border-color: transparent;
    }
    .el-icon-close {
      display: flex;
      align-items: center;
      justify-content: center;
      position: absolute;
      top: -5px;
      right: -5px;
      background-color: rgba(0, 0, 0, 0.8);
      color: #fff;
      &:hover {
        background-color: #2e73f3;
      }
    }
  }
  .statement-create-info {
    border: 1px solid #cbd6e2;
    border-radius: 5px;
    overflow: hidden;
    &-item {
      display: flex;
      align-items: center;
      padding: 15px 20px;
      line-height: 20px;
      border-bottom: 1px solid #cbd6e2;
      &-label {
        font-size: 12px;
        color: #999;
        margin-right: 15px;
      }
      &-value {
        font-weight: 500;
        font-size: 12px;
        color: #333333;
      }
      &-icon {
        display: flex;
        align-items: center;
        gap: 5px;
        cursor: pointer;
        i {
          font-size: 18px;
          color: #2e73f3;
        }
        span {
          font-size: 12px;
          color: #666;
        }
        &:hover {
          i,
          span {
            color: #2e73f3;
          }
        }
      }
      &:last-child {
        border-bottom: none !important;
      }
      &.statement-create-info-item-border {
        border-right: 1px solid #cbd6e2;
      }
      &.statement-create-info-item-background {
        background-color: #f8f9fb;
      }
    }
  }
}
</style>
