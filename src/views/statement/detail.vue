<template>
  <div>
    <el-dialog :title="title" :visible.sync="open" width="1150px" :before-close="beforeClose" class="custom-dialog">
      <div style="padding: 0 20px">
        <div class="statement-detail-title">
          <div class="statement-detail-title-title">项目基本信息</div>
          <div class="statement-detail-title-collapse" v-if="showMore" @click="showMore = !showMore">
            <span>收起</span>
            <i class="el-icon-arrow-up"></i>
          </div>
          <div class="statement-detail-title-collapse" v-else @click="showMore = !showMore">
            <span>展开查看</span>
            <i class="el-icon-arrow-down"></i>
          </div>
        </div>
        <el-row :gutter="10" v-if="showMore">
          <el-col :span="8" class="statement-detail-item">
            <div class="statement-detail-item-label">客户名称</div>
            <div class="statement-detail-item-value">河北世盛金属制品有限公司</div>
          </el-col>
          <el-col :span="8" class="statement-detail-item">
            <div class="statement-detail-item-label">项目名称</div>
            <div class="statement-detail-item-value">智能办公系统开发</div>
          </el-col>
          <el-col :span="8" class="statement-detail-item">
            <div class="statement-detail-item-label">项目地址</div>
            <div class="statement-detail-item-value">河北省石家庄市高新区长江大道88号</div>
          </el-col>
          <el-col :span="8" class="statement-detail-item">
            <div class="statement-detail-item-label">合同编号</div>
            <div class="statement-detail-item-value"><span class="link">HT2024005</span></div>
          </el-col>
          <el-col :span="8" class="statement-detail-item">
            <div class="statement-detail-item-label">合同价款</div>
            <div class="statement-detail-item-value"><span class="price">2,100,000</span></div>
          </el-col>
          <el-col :span="8" class="statement-detail-item">
            <div class="statement-detail-item-label">合同产品数量</div>
            <div class="statement-detail-item-value">1200</div>
          </el-col>
          <el-col :span="8" class="statement-detail-item">
            <div class="statement-detail-item-label">项目开工时间</div>
            <div class="statement-detail-item-value">2024-04-01</div>
          </el-col>
          <el-col :span="8" class="statement-detail-item">
            <div class="statement-detail-item-label">合同约定完工时间</div>
            <div class="statement-detail-item-value">2024-09-30</div>
          </el-col>
          <el-col :span="8" class="statement-detail-item">
            <div class="statement-detail-item-label">预计结算收入方式</div>
            <div class="statement-detail-item-value">分期收款</div>
          </el-col>
          <el-col :span="8" class="statement-detail-item">
            <div class="statement-detail-item-label">预计成本</div>
            <div class="statement-detail-item-value"><span class="price">1,500,000</span></div>
          </el-col>
          <el-col :span="8" class="statement-detail-item">
            <div class="statement-detail-item-label">成本利润率</div>
            <div class="statement-detail-item-value"><span class="price">40%</span></div>
          </el-col>
          <el-col :span="8" class="statement-detail-item">
            <div class="statement-detail-item-label">已安装数量</div>
            <div class="statement-detail-item-value">800</div>
          </el-col>
          <el-col :span="24" class="statement-detail-item">
            <div class="statement-detail-item-label">项目完成详细</div>
            <div class="statement-detail-item-value">项目状态详细描述；筑建局已验收，后面等消防验收，然后再结算；项目状态详细描述；筑建局已验收，后面等消防验收，然后再结算；项目状态详细描述；筑建局已验收，后面等消防验收，然后再结算；项目状态详细描述；筑建局已验收，后面等消防验收，然后再结算；项目状态详细描述；筑建局已验收，后面等消防验收，然后再结算；项目状态详细描述；筑建局已验收，后面等消防验收，然后再结算；项目状态详细描述；筑建局已验收，后面等消防验收，然后再结算；</div>
          </el-col>
          <el-col :span="24">
            <el-row class="statement-create-info">
              <el-col :span="12" class="statement-create-info-item statement-create-info-item-border">
                <span class="create-info-item-label">本月收款：</span>
                <span class="create-info-item-value">100000元</span>
              </el-col>
              <el-col :span="12" class="statement-create-info-item">
                <span class="statement-create-info-item-label">累计收款：</span>
                <span class="statement-create-info-item-icon">
                  <i class="el-icon-search"></i>
                  <span>去查询</span>
                </span>
              </el-col>
              <el-col :span="12" class="statement-create-info-item statement-create-info-item-border statement-create-info-item-background">
                <span class="statement-create-info-item-label">本月成本：</span>
                <span class="statement-create-info-item-value">100000元</span>
              </el-col>
              <el-col :span="12" class="statement-create-info-item statement-create-info-item-background">
                <span class="statement-create-info-item-label">累计成本：</span>
                <span class="statement-create-info-item-icon">
                  <i class="el-icon-search"></i>
                  <span>去查询</span>
                </span>
              </el-col>
              <el-col :span="24" class="statement-create-info-item">
                <span class="statement-create-info-item-label">预计应收款：</span>
                <span class="statement-create-info-item-icon">
                  <i class="el-icon-plus"></i>
                  <span>添加</span>
                </span>
              </el-col>
            </el-row>
          </el-col>
          <el-col :span="24" v-if="showMore">
            <div class="statement-detail-collapse" @click="showMore = !showMore">
              <span>收起</span>
              <i class="el-icon-arrow-up"></i>
            </div>
          </el-col>
        </el-row>
        <div class="statement-audit">
          <template v-for="(item, index) in auditList">
            <div class="statement-audit-item" :class="{ active: item.status === 1 }" :key="index">
              <el-image :src="item.image" fit="cover" class="statement-audit-item-image"></el-image>
              <div class="flex align-center">
                <span class="statement-audit-item-title">{{ item.status === 1 ? '已审批' : '未审批' }}</span>
                <i class="el-icon-check statement-audit-item-icon"></i>
              </div>
            </div>
            <i class="statement-audit-icon el-icon-right" :key="`${index}-icon`" v-if="index !== auditList.length - 1"></i>
          </template>
        </div>
        <el-form ref="form" :model="form" :rules="rules" label-width="100px" class="custom-form" @submit.native.prevent>
          <el-form-item label="审批备注" prop="remark">
            <div class="statement-audit-remark">
              <el-input v-model="form.remark" placeholder="请输入审批备注" class="statement-audit-remark-input">
                <template slot="suffix">
                  <el-button type="primary" size="mini" v-if="!!form.remark">添加至自定义回复</el-button>
                </template>
              </el-input>
              <div class="statement-audit-remark-title">
                <span class="statement-audit-remark-title-text">自定义回复</span>
                <span class="statement-audit-remark-title-icon" :class="{ active: isEdit }" @click="isEdit = !isEdit">
                  <i class="el-icon-edit"></i>
                  <span>编辑</span>
                </span>
              </div>
              <div class="statement-audit-remark-list">
                <div class="statement-audit-remark-item">
                  <span>抓紧推进完成</span>
                  <i class="el-icon-error" v-if="isEdit"></i>
                </div>
                <div class="statement-audit-remark-item">
                  <span>抓紧推进完成</span>
                  <i class="el-icon-error" v-if="isEdit"></i>
                </div>
              </div>
            </div>
          </el-form-item>
        </el-form>
      </div>
      <div class="statement-audit-action">
        <div class="statement-audit-action-item">
          <div class="statement-audit-action-item-title">
            <el-image src="https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg" fit="cover" class="title-image"></el-image>
            <span class="title-text">财务部-李二</span>
          </div>
          <div class="statement-audit-action-item-content">该项目接下来进入尾期，抓紧质量，确保项目验收不出问题</div>
        </div>
        <div class="statement-audit-action-item">
          <div class="statement-audit-action-item-title">
            <el-image src="https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg" fit="cover" class="title-image"></el-image>
            <span class="title-text">财务部-李二</span>
          </div>
          <div class="statement-audit-action-item-content">该项目接下来进入尾期，抓紧质量，确保项目验收不出问题</div>
        </div>
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn" @click="handleCancel()">取 消</el-button>
        <el-button class="custom-dialog-btn primary" @click="handleSubmit" :loading="loading">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'StatementDetail',
  data() {
    return {
      open: false,
      title: '项目结算详情',
      form: {},
      rules: {},
      loading: false,
      auditList: [
        { image: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg', status: 1 },
        { image: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg', status: 1 },
        { image: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg', status: 2 }
      ],
      showMore: true,
      isEdit: false
    }
  },
  methods: {
    reset() {
      this.form = {}
      this.resetForm('form')
      this.isEdit = false
      this.showMore = true
    },
    // 打开弹窗
    handleOpen() {
      this.reset()
      this.title = '项目结算详情'
      this.open = true
    },
    // 关闭弹窗
    beforeClose() {
      this.handleCancel()
    },
    // 取消
    handleCancel(flag = false) {
      this.open = false
      this.$emit('callBack', flag)
    },
    // 提交
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true
          this.$emit('callBack', true)
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
@import '@/assets/styles/custom-new.scss';
.statement-detail {
  &-title {
    display: flex;
    align-items: center;
    border-bottom: 1px solid #e2e6f3;
    line-height: 20px;
    padding-bottom: 12px;
    margin-bottom: 10px;
    &-title {
      font-size: 14px;
      color: #999999;
    }
    &-collapse {
      display: flex;
      align-items: center;
      cursor: pointer;
      color: #999999;
      margin-left: 35px;
      font-size: 12px;
      i {
        margin-left: 5px;
        font-size: 18px;
        color: #2e73f3;
      }
    }
  }
  &-item {
    display: flex;
    align-items: center;
    min-height: 40px;
    &-label {
      width: 60px;
      font-size: 12px;
      color: #666666;
      line-height: 16px;
      margin-right: 20px;
      flex-shrink: 0;
    }
    &-value {
      font-weight: 500;
      font-size: 14px;
      line-height: 32px;
      color: #333333;
      .link {
        color: #2e73f3;
        cursor: pointer;
      }
      .price {
        color: #f43f3f;
      }
    }
  }
  &-collapse {
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: #2e73f3;
    margin-top: 20px;
    font-size: 18px;
    span {
      font-size: 12px;
      margin-right: 10px;
    }
  }
}
.statement-audit {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  margin-top: 30px;
  margin-bottom: 20px;
  &-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #ecedf0;
    border-radius: 50px;
    border: 1px solid #cbd6e2;
    padding: 3px;
    width: 160px;
    &-image {
      width: 42px;
      height: 42px;
      border-radius: 50%;
    }
    &-title {
      font-size: 14px;
      color: #666666;
    }
    &-icon {
      font-size: 18px;
      color: #ecedf0;
      margin-left: 10px;
      margin-right: 10px;
    }
    &.active {
      background: #e5eeff;
      border: 1px solid #2e73f3;
      .statement-audit-item-title,
      .statement-audit-item-icon {
        color: #2e73f3;
      }
    }
  }
  &-icon {
    margin: 0 10px;
    font-size: 14px;
    color: #666;
  }
  &-remark {
    background-color: #f8f9fb;
    border-radius: 5px;
    border: 1px solid #cbd7e2;
    display: flex;
    flex-direction: column;
    &-input {
      width: calc(100% + 2px);
      margin-top: -1px;
      margin-left: -1px;
    }
    &-title {
      display: flex;
      align-items: center;
      padding: 5px 10px;
      line-height: 20px;
      &-text {
        font-size: 12px;
        color: #999999;
      }
      &-icon {
        display: flex;
        align-items: center;
        gap: 5px;
        cursor: pointer;
        margin-left: 20px;
        font-size: 12px;
        color: #999999;
        &:hover,
        &.active {
          color: #2e73f3;
        }
      }
    }
    &-list {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      padding: 0 10px 10px;
      line-height: 20px;
    }
    &-item {
      position: relative;
      padding: 5px 10px;
      background-color: #e5e5e5;
      font-size: 12px;
      color: #333333;
      line-height: 20px;
      border-radius: 5px;
      cursor: pointer;
      i {
        position: absolute;
        right: -5px;
        top: -5px;
        font-size: 16px;
        color: rgba(0, 0, 0, 0.6);
        cursor: pointer;
        &:hover {
          color: #2e73f3;
        }
      }
      &:hover {
        background-color: #e0ebff;
        color: #2e73f3;
      }
    }
  }
}
.statement-create-info {
  border: 1px solid #cbd6e2;
  border-radius: 5px;
  overflow: hidden;
  &-item {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    line-height: 20px;
    border-bottom: 1px solid #cbd6e2;
    &-label {
      font-size: 12px;
      color: #999;
      margin-right: 15px;
    }
    &-value {
      font-weight: 500;
      font-size: 12px;
      color: #333333;
    }
    &-icon {
      display: flex;
      align-items: center;
      gap: 5px;
      cursor: pointer;
      i {
        font-size: 18px;
        color: #2e73f3;
      }
      span {
        font-size: 12px;
        color: #666;
      }
      &:hover {
        i,
        span {
          color: #2e73f3;
        }
      }
    }
    &:last-child {
      border-bottom: none !important;
    }
    &.statement-create-info-item-border {
      border-right: 1px solid #cbd6e2;
    }
    &.statement-create-info-item-background {
      background-color: #f8f9fb;
    }
  }
}
.statement-audit-action {
  margin-top: 20px;
  padding: 0 20px;
  background-color: #f7f9fc;
  &-item {
    padding: 15px 0;
    border-bottom: 1px solid #e2e6f3;
    &:last-child {
      border-bottom: none;
    }
    &-title {
      display: flex;
      align-items: center;
      .title-image {
        width: 38px;
        height: 38px;
        border-radius: 50%;
        margin-right: 10px;
      }
      .title-text {
        font-size: 12px;
        color: #666666;
        line-height: 20px;
      }
    }
    &-content {
      font-size: 14px;
      color: #333333;
      line-height: 20px;
      margin-top: 10px;
    }
  }
}
</style>
