<template>
  <div class="newBox bgcf9 vh-85">
    <!-- 搜索 -->
    <div class="custom-search flex" v-show="showSearch">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" @submit.native.prevent>
        <el-form-item label="项目名称" prop="title">
          <el-input v-model="queryParams.title" placeholder="请输入项目名称" clearable @keyup.enter.native="handleQuery" size="small" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
          <el-button type="primary" icon="el-icon-plus" size="small" @click="handleAdd">新增项目结算</el-button>
        </el-form-item>
      </el-form>
    </div>
    <!-- 分类 -->
    <div class="classify flex">
      <div class="classify-item" :class="{ active: item.value === queryParams.status }" v-for="item in statusOptions" :key="item.value" @click="handleCategory(item)">
        {{ item.label }}
        <el-badge :value="item.badge" :hidden="item.badge === 0"></el-badge>
      </div>
      <div class="classify-toolbar">
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns" isSetitem @updateColumns="updateColumns"></right-toolbar>
      </div>
    </div>
    <!-- 列表 -->
    <div style="padding: 15px 20px" v-loading="loading">
      <el-table ref="allTable" stripe :data="list" row-key="id" style="width: 100%" class="custom-table" v-if="total > 0">
        <!-- 序号 -->
        <el-table-column type="index" label="序号" width="55" align="center" />
        <el-table-column v-if="columns[1].visible" prop="title" label="项目名称" align="center" show-overflow-tooltip />
        <el-table-column v-if="columns[2].visible" prop="customerName" label="客户名称" align="center" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <span class="table-link">{{ row.customerName }}</span>
          </template>
        </el-table-column>
        <el-table-column v-if="columns[3].visible" prop="contractNo" label="合同编号" align="center" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <span class="table-link">{{ row.contractNo }}</span>
          </template>
        </el-table-column>
        <el-table-column v-if="columns[4].visible" prop="contractAmount" label="合同价款(元)" align="center" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <span class="table-orange">{{ row.contractAmount }}</span>
          </template>
        </el-table-column>
        <el-table-column v-if="columns[5].visible" prop="startDate" label="项目开工时间" align="center" show-overflow-tooltip>
          <template slot-scope="{ row }">{{ parseTime(row.startDate, '{y}-{m}-{d}') }}</template>
        </el-table-column>
        <el-table-column v-if="columns[6].visible" prop="endDate" label="项目约定完成时间" align="center" show-overflow-tooltip>
          <template slot-scope="{ row }">{{ parseTime(row.endDate, '{y}-{m}-{d}') }}</template>
        </el-table-column>
        <el-table-column v-if="columns[7].visible" prop="status" label="项目状态" align="center" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <span v-html="getProjectStatusLabel(row.status)"></span>
          </template>
        </el-table-column>
        <el-table-column v-if="columns[8].visible" prop="remark" label="项目备注" align="center" show-overflow-tooltip />
        <el-table-column v-if="columns[9].visible" prop="approvalStatus" label="审批状态" align="center" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <span v-html="getApprovalStatusLabel(row.approvalStatus)"></span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="220">
          <template slot-scope="{ row }">
            <el-button class="table-btn" @click="handleDetail(row)">查看详情</el-button>
            <el-button class="table-btn primary" v-if="row.approvalStatus === 2" @click="handleEdit(row)">修改内容</el-button>
            <el-button class="table-btn primary hasbg" v-if="row.approvalStatus === 1" @click="handleFinish(row)">完成结算</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-empty v-else description="暂无数据" />
      <!-- 分页 -->
      <div class="custom-pagination">
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" :local="userId + '.statementPage'" />
      </div>
    </div>
    <!-- 新增项目结算 -->
    <statement-create ref="statementCreate" @callBack="handleCallBack" v-if="showCreate" />
    <!-- 项目结算详情 -->
    <statement-detail ref="statementDetail" @callBack="handleCallBack" v-if="showDetail" />
  </div>
</template>
<script>
import StatementCreate from './create'
import StatementDetail from './detail'

export default {
  name: 'Statement',
  components: { StatementCreate, StatementDetail },
  data() {
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        title: undefined,
        status: 0
      },
      statusOptions: [
        { value: 0, label: '所有项目', badge: 0 },
        { value: 1, label: '我的项目', badge: 0 },
        { value: 2, label: '待审批', badge: 10 },
        { value: 3, label: '已审批', badge: 0 }
      ],
      showSearch: true,
      columns: [
        { key: 0, label: '序号', visible: true },
        { key: 1, label: '项目名称', visible: true },
        { key: 2, label: '客户名称', visible: true },
        { key: 3, label: '合同编号', visible: true },
        { key: 4, label: '合同价款', visible: true },
        { key: 5, label: '项目开工时间', visible: true },
        { key: 6, label: '项目约定完成时间', visible: true },
        { key: 7, label: '项目状态', visible: true },
        { key: 8, label: '项目备注', visible: true },
        { key: 9, label: '审批状态', visible: true }
      ],
      list: [],
      total: 0,
      loading: false,
      // 项目状态
      projectStatus: [
        { label: '进行中', value: 1, color: '#2E73F3' },
        { label: '未开始', value: 2, color: '#31C776' },
        { label: '暂停中', value: 3, color: '#F35D09' },
        { label: '已完成', value: 4, color: '#666666' }
      ],
      approvalStatus: [
        { label: '已审批', value: 1, color: '#999999' },
        { label: '待审批', value: 2, color: '#F35D09' }
      ],
      showCreate: false,
      showDetail: false
    }
  },
  computed: {
    userId() {
      return this.$store.getters.info.userId
    }
  },
  created() {
    const columns = localStorage.getItem(this.userId + '.statementColumns')
    if (columns) this.columns = JSON.parse(columns)
    // 获取列表
    this.getList()
  },
  methods: {
    // 改变显隐列缓存
    updateColumns(data = []) {
      localStorage.setItem(this.userId + '.statementColumns', JSON.stringify(data))
    },
    // 获取列表
    getList() {
      this.loading = true
      // 模拟数据
      const list = [
        {
          id: 1,
          title: '智慧城市综合管理系统',
          customerName: '北京科技有限公司',
          contractNo: 'HT2024001',
          contractAmount: '2,680,000',
          startDate: '2024-01-15',
          endDate: '2024-06-30',
          status: 1, // 进行中
          remark: '项目进展顺利，按计划执行',
          approvalStatus: 1 // 已审批
        },
        {
          id: 2,
          title: '企业ERP系统升级改造',
          customerName: '上海制造集团',
          contractNo: 'HT2024002',
          contractAmount: '1,850,000',
          startDate: '2024-03-01',
          endDate: '2024-08-15',
          status: 2, // 未开始
          remark: '等待客户确认需求细节',
          approvalStatus: 2 // 待审批
        },
        {
          id: 3,
          title: '移动端APP开发项目',
          customerName: '深圳互联网公司',
          contractNo: 'HT2024003',
          contractAmount: '980,000',
          startDate: '2024-02-10',
          endDate: '2024-05-20',
          status: 3, // 暂停中
          remark: '客户需求变更，暂时停止开发',
          approvalStatus: 1 // 已审批
        },
        {
          id: 4,
          title: '数据分析平台建设',
          customerName: '广州大数据公司',
          contractNo: 'HT2023015',
          contractAmount: '3,200,000',
          startDate: '2023-10-01',
          endDate: '2024-01-31',
          status: 4, // 已完成
          remark: '项目已顺利完成并通过验收',
          approvalStatus: 1 // 已审批
        },
        {
          id: 5,
          title: '电商平台优化升级',
          customerName: '杭州电商有限公司',
          contractNo: 'HT2024004',
          contractAmount: '1,450,000',
          startDate: '2024-01-20',
          endDate: '2024-04-30',
          status: 1, // 进行中
          remark: '前端页面改版完成60%',
          approvalStatus: 2 // 待审批
        },
        {
          id: 6,
          title: '智能办公系统开发',
          customerName: '成都科技园区',
          contractNo: 'HT2024005',
          contractAmount: '2,100,000',
          startDate: '2024-04-01',
          endDate: '2024-09-30',
          status: 2, // 未开始
          remark: '项目启动会待安排',
          approvalStatus: 1 // 已审批
        },
        {
          id: 7,
          title: '供应链管理系统',
          customerName: '武汉物流集团',
          contractNo: 'HT2024006',
          contractAmount: '1,780,000',
          startDate: '2024-02-15',
          endDate: '2024-07-15',
          status: 3, // 暂停中
          remark: '技术方案需要重新评估',
          approvalStatus: 2 // 待审批
        },
        {
          id: 8,
          title: '在线教育平台开发',
          customerName: '西安教育科技公司',
          contractNo: 'HT2023020',
          contractAmount: '950,000',
          startDate: '2023-11-01',
          endDate: '2024-02-28',
          status: 4, // 已完成
          remark: '项目交付完成，客户满意度高',
          approvalStatus: 1 // 已审批
        },
        {
          id: 9,
          title: '财务管理系统集成',
          customerName: '天津财务咨询公司',
          contractNo: '*********',
          contractAmount: '1,320,000',
          startDate: '2024-03-15',
          endDate: '2024-06-15',
          status: 1, // 进行中
          remark: '数据库设计阶段',
          approvalStatus: 1 // 已审批
        }
      ]
      setTimeout(() => {
        this.list = list
        this.total = list.length
        this.loading = false
      }, 1000)
    },
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 重置搜索
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 分类
    handleCategory(item) {
      this.queryParams.status = item.value
      this.handleQuery()
    },
    // 格式化项目状态
    getProjectStatusLabel(status) {
      const statusInfo = this.projectStatus.find(item => item.value === status)
      return `<span style="color: ${statusInfo.color}">${statusInfo.label}</span>`
    },
    // 格式化审批状态
    getApprovalStatusLabel(status) {
      const statusInfo = this.approvalStatus.find(item => item.value === status)
      return `<span style="color: ${statusInfo.color}">${statusInfo.label}</span>`
    },
    // 新增项目结算
    handleAdd() {
      this.showCreate = true
      this.$nextTick(() => {
        this.$refs.statementCreate.handleCreate()
      })
    },
    // 新增项目结算回调
    handleCallBack(flag = false) {
      this.showCreate = false
      if (flag) this.getList()
    },
    // 查看详情
    handleDetail(row) {
      this.showDetail = true
      this.$nextTick(() => {
        this.$refs.statementDetail.handleOpen(row)
      })
    },
    // 修改内容
    handleEdit(row) {
      this.showCreate = true
      this.$nextTick(() => {
        this.$refs.statementCreate.handleEdit(row)
      })
    },
    // 完成结算
    handleFinish(row) {
      this.showCreate = true
      this.$nextTick(() => {
        this.$refs.statementCreate.handleFinish(row)
      })
    }
  }
}
</script>
<style scoped lang="scss">
@import '@/assets/styles/custom-new.scss';
</style>
