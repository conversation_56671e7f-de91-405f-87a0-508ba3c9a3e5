<template>
  <div :class="{ newBox: !isChoose, bgcf9: !isChoose, 'vh-85': !isChoose }">
    <!-- 搜索 -->
    <div class="custom-search flex" style="padding-top: 18px">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" @submit.native.prevent>
        <el-form-item label="客户名称" prop="name">
          <el-input v-model="queryParams.name" placeholder="请输入客户名称" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="small" @click="handleResetQuery">重置</el-button>
          <el-button type="primary" icon="el-icon-plus" size="small" @click="handleAdd" v-if="!isChoose">新增客户</el-button>
        </el-form-item>
      </el-form>
    </div>
    <!-- 分类 -->
    <div class="classify flex" v-if="!isChoose">
      <div class="classify-item" :class="{ active: queryParams.status === 1 }" @click="handleChangeStatus(1)">所有客户</div>
      <div class="classify-item" :class="{ active: queryParams.status === -1 }" @click="handleChangeStatus(-1)">已停用</div>
    </div>
    <!-- 列表 -->
    <div style="padding: 15px 20px 0">
      <el-empty v-if="total === 0 && !loading" :description="showTip ? '请前往客户管理新增客户！' : '暂无数据'" />
      <el-table v-loading="loading" ref="allTable" stripe :data="list" row-key="id" style="width: 100%" class="custom-table" @row-click="handleChoose" v-else>
        <el-table-column align="center" label="选择" width="55" v-if="isChoose">
          <template slot-scope="{ row }">
            <el-radio v-model="chooseChecked.id" :label="row.id"><span /></el-radio>
          </template>
        </el-table-column>
        <el-table-column align="center" type="index" label="序号"></el-table-column>
        <el-table-column align="center" prop="name" label="客户名称" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <span class="table-link" @click="handleCompanyView(row)">{{ row.name }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="address" label="地址" show-overflow-tooltip>
          <template slot-scope="{ row }">{{ removeHtmlTag(row.address, 300) }}</template>
        </el-table-column>
        <el-table-column align="center" prop="currency" label="结算币别" :formatter="currencyFormat" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" prop="createTime" label="创建时间" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" label="操作" v-if="!isChoose">
          <template slot-scope="{ row }">
            <el-button class="table-btn primary" @click="handleDetail(row)">查看详情</el-button>
            <template v-if="queryParams.status === 1">
              <el-button class="table-btn primary hasbg" @click="handleUpdate(row)">编辑</el-button>
              <el-button class="table-btn orange" @click="handleDelete(row, -1)">停用</el-button>
            </template>
            <el-button class="table-btn success" @click="handleDelete(row, 1)" v-if="queryParams.status === -1">启用</el-button>
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作" v-if="isChoose">
          <template slot-scope="{ row }">
            <el-button type="text" size="mini" icon="el-icon-circle-check" @click.stop="handleChoose(row)">选择</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="custom-pagination" v-if="total > 0 && !loading">
        <pagination :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
      </div>
    </div>
    <!--新增/修改-->
    <create-dialog ref="create" type="kh" :currency-options="currencyOptions"></create-dialog>
  </div>
</template>
<script>
import { changeStatus, getlist, privateSupb } from '@/api/houtai/siyu/gongying'
import { removeHtmlTag } from '@/utils'
import createDialog from '@/views/houtai/siyu/gongying/create'

export default {
  name: 'Client',
  props: {
    isChoose: {
      type: Boolean,
      default: false
    },
    chooseChecked: {
      type: Object,
      default: () => ({})
    },
    showTip: {
      type: Boolean,
      default: false
    }
  },
  components: { createDialog },
  data() {
    return {
      loading: true,
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: undefined,
        address: undefined,
        keyword: undefined,
        type: 'kh',
        status: 1
      },
      list: [],
      currencyOptions: [
        { id: 1, value: 'PRE001', en: 'CNY', zh: '人民币', symbol: '¥' },
        { id: 2, value: 'PRE002', en: 'HKD', zh: '香港元', symbol: 'HK$' },
        { id: 3, value: 'PRE003', en: 'EUR', zh: '欧元', symbol: '€' },
        { id: 4, value: 'PRE004', en: 'JPY', zh: '日本日圆', symbol: '￥' },
        { id: 5, value: 'PRE005', en: 'TWD', zh: '新台币元', symbol: 'NT$' },
        { id: 6, value: 'PRE006', en: 'GBP', zh: '英镑', symbol: '£' },
        { id: 7, value: 'PRE007', en: 'USD', zh: '美元', symbol: '$' }
      ]
    }
  },
  created() {
    this.getList()
  },
  methods: {
    removeHtmlTag,
    currencyFormat(row) {
      return this.currencyOptions.find(item => item.value === row.currency)?.zh || '-'
    },
    // 列表
    getList() {
      this.loading = true
      getlist(this.queryParams).then(res => {
        const { code, msg, rows, total } = res
        if (code === 200) {
          this.list = rows
          this.total = total
          this.loading = false
        } else this.$message.error(msg)
      })
    },
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 重置搜索
    handleResetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 切换分类（状态）
    handleChangeStatus(status) {
      this.queryParams.status = status
      this.handleQuery()
    },
    // 查看公司详情
    handleCompanyView(row) {
      const { name } = row
      this.$refs.create.handleSearch(1, name)
    },
    // 新增客户
    handleAdd() {
      this.$refs.create.handleAdd('新增客户')
    },
    // 详情
    handleDetail(row) {
      privateSupb({ id: row.id }).then(res => {
        if (res.code === 200) {
          this.$refs.create.handleDetail(res.data, '客户详情')
        } else this.$message.error(res.msg)
      })
    },
    // 修改
    handleUpdate(row) {
      privateSupb({ id: row.id }).then(res => {
        if (res.code === 200) {
          this.$refs.create.handleUpdate(res.data, '修改客户')
        } else this.$message.error(res.msg)
      })
    },
    // 停用
    // prettier-ignore
    handleDelete(row, type = 1) {
      const ids = row.id
      const status = type === 1 ? 1 : -1
      const title = type === 1 ? '是否停用此客户?' : '是否启用此客户?'
      const msg = type === 1 ? '停用成功' : '启用成功'
      this.$confirm(title, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        changeStatus({ ids, status }).then(res => {
          if (res.code === 200) {
            this.$message.success(msg)
            this.getList()
          } else this.$message.error(res.msg)
        })
      }).catch(() => { })
    },
    // 选择客户
    handleChoose(row) {
      this.$emit('update:chooseChecked', row)
    }
  }
}
</script>
<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';
.custom-table {
  .el-button {
    padding: 0;
  }
}
</style>
