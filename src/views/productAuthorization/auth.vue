<template>
    <div class="newBox bgcf9 vh-85">
        <!-- 搜索 -->
        <div class="custom-search">
            <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" @submit.native.prevent>
                <el-row :gutter="10">
                    <el-col :xs="8" :sm="6" :lg="4">
                        <el-form-item label="名称" prop="companyName">
                            <el-input v-model="queryParams.companyName" placeholder="请输入公司名称" clearable
                                @keyup.enter.native="handleQuery" size="small" />
                        </el-form-item>
                    </el-col>
                    <el-col :xs="8" :sm="6" :lg="4">
                        <el-form-item label="名称" prop="productName">
                            <el-input v-model="queryParams.productName" placeholder="请输入产品名称" clearable
                                @keyup.enter.native="handleQuery" size="small" />
                        </el-form-item>
                    </el-col>
                    <el-col :xs="8" :sm="6" :lg="4">
                        <el-form-item label="状态" prop="status">
                            <el-select v-model="queryParams.status" placeholder="请选择状态" clearable @change="handleQuery">
                                <el-option label="待审核" :value="0" />
                                <el-option label="已通过" :value="1" />
                                <el-option label="驳回" :value="-1" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="1.5">
                        <el-form-item>
                            <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
                        </el-form-item>
                        <el-form-item>
                            <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </div>

        <!-- 表格数据 -->
        <div class="tableBox">
            <el-table v-loading="loading" ref="table" stripe :data="list" row-key="id" :key="key" style="width: 100%"
                class="custom-table custom-table-cell5">
                <el-table-column align="center" type="index" label="序号" v-if="columns[0].visible"></el-table-column>
                <el-table-column align="center" prop="productName" label="产品名称" show-overflow-tooltip
                    v-if="columns[1].visible" min-width="130">
                    <template slot-scope="{ row }">
                        <span class="table-link" @click="handleView(row.product)">{{ row.productName }}</span>
                    </template>
                </el-table-column>
                <el-table-column align="center" prop="picture1" label="图片" width="75" v-if="columns[2].visible">
                    <template slot-scope="{ row }">
                        <el-image :src="formatProductImg(row.product)" fit="cover" @click="handleImg(row.product)">
                            <div slot="error" class="image-slot">
                                <i class="el-icon-picture-outline"></i>
                            </div>
                        </el-image>
                    </template>
                </el-table-column>
                <el-table-column align="center" prop="specs" label="规格" show-overflow-tooltip v-if="columns[3].visible">
                    <template slot-scope="{ row }">
                        <span>{{ row.product.specs }}</span>
                    </template>
                </el-table-column>
                <el-table-column align="center" prop="productCode" label="产品编码" show-overflow-tooltip
                    v-if="columns[4].visible" width="130">
                    <template slot-scope="{ row }">
                        <span>{{ row.product.productCode }}</span>
                    </template>
                </el-table-column>
                <el-table-column align="center" prop="materialQuality" label="材质" show-overflow-tooltip
                    v-if="columns[5].visible">
                    <template slot-scope="{ row }">
                        <span>{{ row.product.materialQuality }}</span>
                    </template>
                </el-table-column>
                <el-table-column align="center" prop="surface" label="表面处理" show-overflow-tooltip v-if="columns[6].visible">
                    <template slot-scope="{ row }">
                        <span>{{ row.product.surface }}</span>
                    </template>
                </el-table-column>
                <el-table-column align="center" prop="unit" label="单位" width="50" v-if="columns[7].visible">
                    <template slot-scope="{ row }">
                        <span>{{ row.product.unit }}</span>
                    </template>
                </el-table-column>
                <el-table-column align="center" prop="industry" label="属性" show-overflow-tooltip v-if="columns[8].visible">
                    <template slot-scope="{ row }">
                        <span>{{ row.product.industry }}</span>
                    </template>
                </el-table-column>
                <el-table-column align="center" prop="weight" label="重量" width="80" v-if="columns[9].visible">
                    <template slot-scope="{ row }">{{ row.product.weight ? parseFloat(row.product.weight) + 'Kg' : ''
                    }}</template>
                </el-table-column>
                <el-table-column align="center" prop="companyName" label="申请公司" show-overflow-tooltip
                    v-if="columns[10].visible">
                    <template slot-scope="{ row }">
                        <span class="table-link" @click="handleDetail(row.companyName)">{{ row.companyName }}</span>
                    </template>
                </el-table-column>
                <el-table-column align="center" prop="createTime" label="申请时间" show-overflow-tooltip
                    v-if="columns[11].visible">
                </el-table-column>
                <el-table-column align="center" prop="status" label="状态" width="130" v-if="columns[12].visible">
                    <template slot-scope="{ row }">
                        <template>
                            <span v-if="row.status === 0">待审核</span>
                            <span v-if="row.status === 1">已通过</span>
                            <span v-if="row.status === -1">驳回</span>
                        </template>
                    </template>
                </el-table-column>
                <el-table-column align="center" prop="remark" label="审核原因" show-overflow-tooltip
                    v-if="columns[13].visible && queryParams.status !== 0">
                </el-table-column>
                <el-table-column align="center" label="操作" width="250px" v-if="queryParams.status === 0">
                    <template slot-scope="{ row }">
                        <button type="button" class="table-btn small primary" style="width: 100px;"
                            v-hasPermi="['system:auth:product']" @click="handlePass(row)"
                            v-if="row.status === 0">通过</button>
                        <button type="button" class="table-btn small danger" style="width: 100px;"
                            v-hasPermi="['system:auth:product']" @click="handleReject(row)"
                            v-if="row.status === 0">驳回</button>
                    </template>
                </el-table-column>
            </el-table>

            <div class="custom-pagination">
                <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
                    :limit.sync="queryParams.pageSize" @pagination="getList" />
            </div>
        </div>

        <!-- 产品详情 -->
        <product-dialog ref="productInfo"></product-dialog>

        <!--企业详情-->
        <company ref="company"></company>

    </div>
</template>
  
<script>

import { AuthProductApplyList, authProductVerify } from '@/api/productAuthorization'
import ProductDialog from '@/views/public/product/dialog'
import Company from '@/views/payment/company'
import { checkPermi } from '@/utils/permission'
import { getRealCompanyDetail } from '@/api/tender'

export default {

    components: { ProductDialog, Company },
    data() {
        return {
            key: 1,
            // 搜索条件
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                companyName: undefined,
                productName: undefined,
                status: 0,
            },
            // 加载
            loading: true,
            // 列表数据
            list: [],
            // 总条数
            total: 0,
            // 显隐列
            columns: [
                { key: 0, label: `序号`, visible: true },
                { key: 1, label: `产品名称`, visible: true },
                { key: 2, label: `图片`, visible: true },
                { key: 3, label: `规格`, visible: true },
                { key: 4, label: `产品编码`, visible: true },
                { key: 5, label: `材质`, visible: true },
                { key: 6, label: `表面处理`, visible: true },
                { key: 7, label: `单位`, visible: true },
                { key: 8, label: `属性`, visible: true },
                { key: 9, label: `重量`, visible: true },
                { key: 10, label: `申请公司`, visible: true },
                { key: 11, label: `申请时间`, visible: true },
                { key: 12, label: `状态`, visible: true },
                { key: 13, label: `审核原因`, visible: true },
            ],
        }
    },
    created() {
        if (this.checkPermi(['system:auth:product'])) {
            this.getList()
        }
    },
    methods: {
        checkPermi,
        // 查询数据
        async getList() {
            this.loading = true
            const query = { ...this.queryParams }
            const res = await AuthProductApplyList(query)
            if (res.code === 200) {
                this.list = res.rows
                this.loading = false
                this.total = res.total
                this.key = Math.random()
            } else {
                this.$message.error(res.msg)
            }
        },
        // 搜索
        handleQuery() {
            this.queryParams.pageNum = 1
            this.getList()
        },
        // 重置搜索
        resetQuery() {
            this.resetForm('queryForm')
            this.handleQuery()
        },
        // 查看详情
        handleView(item, val) {
            this.$refs.productInfo.handleView(item, val)
        },
        // 图片预览
        handleImg(row) {
            this.$refs.productInfo.handleImgView(row)
        },
        // 查看企业详情
        handleDetail(companyName) {
            getRealCompanyDetail({ companyName }).then(res => {
                if (res) {
                    this.$refs.company.getInfo(res, 'private', true, false)
                } else {
                    this.$message({
                        type: 'error',
                        message: '未查询到相关企业信息'
                    });
                }
            })

        },
        // 通过审核
        handleReject(row) {
            this.$prompt('请输入驳回该审核原因', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                inputPattern: /^[\u4E00-\u9FA5A-Za-z0-9_]+$/,
                inputErrorMessage: '审核原因不能为空'
            }).then(({ value }) => {
                authProductVerify({
                    id: row.id,
                    reason: value,
                    status: -1
                }).then(response => {
                    if (response.code == 200) {
                        this.$message({
                            type: 'success',
                            message: '已驳回该产品的经营授权申请'
                        });
                        this.handleQuery()
                    } else {
                        this.$message({
                            type: 'error',
                            message: response.msg
                        });
                    }
                })
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消该操作'
                });
            });
        },
        // 驳回审核
        handlePass(row) {
            this.$prompt('请输入通过该审核原因', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                inputPattern: /^[\u4E00-\u9FA5A-Za-z0-9_]+$/,
                inputErrorMessage: '审核原因不能为空'
            }).then(({ value }) => {
                authProductVerify({
                    id: row.id,
                    reason: value,
                    status: 1
                }).then(response => {
                    if (response.code == 200) {
                        this.$message({
                            type: 'success',
                            message: '已通过该产品的经营授权申请'
                        });
                        this.handleQuery()
                    } else {
                        this.$message({
                            type: 'error',
                            message: response.msg
                        });
                    }
                })
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消该操作'
                });
            });
        },
    }
}
</script>
  
<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';

.tableBox {
    padding: 20px;
}

.custom-search {
    padding-top: 17px;

    ::v-deep {
        .el-form-item--small {
            display: inline-flex;

            .el-form-item__label {
                flex-shrink: 0;
            }

            .el-form-item {
                margin-bottom: 10px;
            }
        }
    }
}

::v-deep .custom-drawer {
    border-top-left-radius: 5px;
    border-bottom-left-radius: 5px;
    overflow: unset;

    .el-drawer__header {
        margin-bottom: 10px;
        height: 50px;
        line-height: 50px;
        font-size: 16px;
        font-weight: 500;
        color: #333333;
        border-bottom: 1px solid #f2f2f8;
        padding: 0 30px;
        position: relative;

        .el-drawer__close-btn {
            position: absolute;
            top: 60px;
            left: -40px;
            width: 40px;
            height: 40px;
            background-color: $white;
            color: #999999;
            border-top-left-radius: 5px;
            border-bottom-left-radius: 5px;
        }
    }

    .el-drawer__body {
        padding: 0 30px;

        .lable-item {
            margin-bottom: 15px;

            .item-title {
                font-size: 14px;
                font-weight: 500;
                color: #333333;
                line-height: 35px;
            }

            .item-box {
                padding: 0 20px 20px;
                border: 1px solid #cbd6e2;
                border-radius: 5px;
                flex-wrap: wrap;

                .item-select {
                    display: flex;
                    flex-direction: column;
                    padding-top: 5px;
                    margin-right: 20px;
                    max-width: calc(25% - 20px);

                    .select-title {
                        font-size: 12px;
                        color: #999999;
                        line-height: 3em;
                    }
                }

                &.ptb20 {
                    padding: 20px 20px 5px;
                }

                .item-tag {
                    background-color: #e5e5e5;
                    border-radius: 5px;
                    line-height: 30px;
                    position: relative;
                    margin-right: 15px;
                    margin-bottom: 15px;
                    font-size: 12px;
                    color: #333333;
                    padding: 0 13px;
                    cursor: pointer;

                    .item-close {
                        display: none;
                    }

                    &.active {
                        background-color: #e0ebff;
                        color: #2e73f3;

                        .item-close {
                            display: inline-block;
                            width: 16px;
                            height: 16px;
                            line-height: 16px;
                            text-align: center;
                            color: $white;
                            background-color: #525356;
                            border-radius: 50%;
                            position: absolute;
                            top: -8px;
                            right: -8px;
                        }
                    }
                }

                .item-input {
                    width: 150px;
                    height: 30px;
                    margin-right: 15px;
                    margin-bottom: 15px;
                }

                .item-add {
                    margin-right: 15px;
                    margin-bottom: 15px;

                    button {
                        border: 1px dashed #2e73f3;
                        background-color: #dfebff;
                        font-size: 14px;
                        color: #2e73f3;
                        height: 30px;
                        line-height: 30px;
                        padding: 0 16px;
                        border-radius: 5px;
                        cursor: pointer;
                    }
                }
            }
        }

        .label-btn {
            width: 269px;
            height: 50px;
            line-height: 50px;
            text-align: center;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            background-color: #2e73f3;
            color: $white;
        }
    }
}

::v-deep .option-btn {
    font-size: 14px;
    cursor: pointer;
    background-color: #2e73f3;
    color: $white;
    border-radius: 50%;
    padding: 2px 3px;

    &:hover {
        opacity: 0.8;
    }
}
</style>
  