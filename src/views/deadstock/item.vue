<template>
  <div :class="`deadPorduct-box${num}`">
    <div :class="`deadPorduct-item item${num}`" v-for="item in list" :key="item.id" @click="handleBuy(item)">
      <div class="item-image">
        <el-image :src="formatProductImg(item)" lazy>
          <div slot="error" class="image-slot">
            <i class="el-icon-picture-outline"></i>
          </div>
        </el-image>
      </div>
      <div class="item-title" :title="item.productName">{{ item.productName }}</div>
      <div class="item-brand">规格：{{ item.specs }}</div>
      <div class="item-price">
        <b>￥{{ item.price }}</b>
        <span>/吨</span>
      </div>
      <div class="item-num">
        <span style="display: block; float: left">库存</span>
        <b class="nums" :title="item.stock + item.unit">{{ item.stock + item.unit }}</b>
        <em>立即抢购</em>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    list: {
      type: Array,
      default: () => []
    },
    num: {
      type: [String, Number],
      default: 1
    }
  },
  name: 'DeadStockItem',
  methods: {
    handleBuy(query) {
      const url = this.$router.resolve({
        path: `/deadstock/view?id=${query.id}`
      })
      window.open(url.href, '_blank')
    },
  }
}
</script>

<style lang="scss" scoped>
@import '~@/assets/styles/custom-public.scss';
.nums {
  display: inline-block;
  max-width: calc(100% - 110px);
  height: 30px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>
