<template>
  <div class="webBox">
    <head-tpl :is-login="isLogin" :isSearch="false"></head-tpl>
    <div class="webMain">
      <div class="webContainer">
        <el-descriptions :column="1" border class="deadNav" labelClassName="deadNav-label" contentClassName="deadNav-content">
          <el-descriptions-item label="分类">
            <span :class="{ active: navParams.categoryId === item.id }" v-for="item in navList" :key="item.id">
              <b @click="getSearchNav(item)">{{ item.name }}</b>
              <i class="el-icon-error" @click="deleteNav" v-if="navParams.categoryId === item.id"></i>
            </span>
          </el-descriptions-item>
        </el-descriptions>
        <div class="deadPorduct" v-if="navTotal">
          <item-tpl :list="navProductList" :num="num" />
          <div class="product-more" v-if="navTotal > 24">
            <div class="product-more-btn" @click="handleMoreNavProduct">
              <template v-if="isMore">
                <i class="ssfont ss-diy-biaoqing"></i>
                我是有底线的
              </template>
              <template v-else>
                <i :class="moreLoading ? 'el-icon-loading' : 'el-icon-refresh'"></i>
                加载更多
              </template>
            </div>
          </div>
        </div>
        <el-empty :image-size="300" v-else />
      </div>
    </div>
    <foot-tpl />

    <!-- 新增/修改产品 -->
    <create-dialog ref="create" />
  </div>
</template>
<script>
import headTpl from '@/views/public/components/head'
import footTpl from '@/views/public/components/foot'
import { getToken } from '@/utils/auth'
import * as category from '@/api/purchase/category'
import { getTypeProducts } from '@/api/system/product'
import itemTpl from './item'
import { listHomeUnsalable } from '@/api/unsalable'
import createDialog from '@/views/unsalable/create'

export default {
  name: 'Deadstock',
  components: { createDialog, footTpl, headTpl, itemTpl },
  data() {
    return {
      isLogin: false,
      // 分类
      navList: [],
      navId: undefined,
      secondNavList: [],
      levelBoxId: undefined,
      levelSonBoxId: undefined,
      levelGrandsonBoxId: undefined,
      navParams: {
        pageNum: 1,
        pageSize: 24,
        categoryId: undefined
      },
      navProductList: [],
      navTotal: 0,
      num: 6,
      isMore: false,
      moreLoading: false
    }
  },
  created() {
    this.isLogin = !!getToken()
    this.getNavList()
    this.getProductList()
  },
  methods: {
    // 查询分类
    getNavList() {
      category.getlist().then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          this.navList = data
        } else this.$message.error(msg)
      })
    },
    // 查询产品
    getProductList() {
      listHomeUnsalable(this.navParams).then(res => {
        const { code, msg, rows, total } = res
        if (code === 200) {
          this.navProductList = rows
          this.navTotal = total
        } else this.$message.error(msg)
      })
    },
    // 加载更多
    handleMoreNavProduct() {
      if (this.navParams.pageNum * this.navParams.pageSize >= this.navTotal) {
        this.isMore = true
      } else {
        this.navParams.pageNum = ++this.navParams.pageNum
        this.moreLoading = true
        listHomeUnsalable(this.navParams).then(res => {
          const { code, msg, rows, total } = res
          if (code === 200) {
            this.navProductList = [...this.navProductList, ...rows]
            this.navTotal = total
            this.moreLoading = false
          } else this.$message.error(msg)
        })
      }
    },
    // 产品搜索
    handleQuery(keyword) {
      this.navParams.pageNum = 1
      if (keyword) {
        this.navParams.categoryId = undefined
        this.navParams.keyword = keyword
        this.getProductList()
      } else {
        this.navParams.categoryId = undefined
        this.navParams.keyword = undefined
        this.getProductList()
      }
    },
    // 选择分类
    getSearchNav(item) {
      this.navParams.categoryId = item.id
      this.getProductList()
    },
    // 删除分类
    deleteNav() {
      this.navParams.categoryId = undefined
      this.handleQuery()
    },
    // 新增产品
    handleAdd() {
      this.$refs.create.handleAdd()
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/assets/styles/custom-public.scss';
.productNav {
  border: 1px solid #e6e7e7;
  border-bottom-color: $blue;
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
  margin-top: 16px;
  &-item {
    span {
      margin: 0;
      padding: 0 30px;
      &.active {
        border: 1px solid $blue;
        background-color: $white;
        border-bottom-width: 0;
        height: 52px;
        line-height: 51px;
        margin: -1px;
        border-top-left-radius: 5px;
        border-top-right-radius: 5px;
        font-size: 14px;
        color: $blue;
      }
    }
  }
}
.productSecondNav {
  margin-top: 0;
  border-bottom-left-radius: 5px;
  border-bottom-right-radius: 5px;
}
</style>
