<template>
  <div class="webBox">
    <head-tpl :is-login="isLogin" :isSearch="false"></head-tpl>
    <div class="webMain">
      <div class="webContainer" v-if="!isOrder">
        <div class="deadView-title">生成订单</div>
        <div class="deadView-order">
          <span>创建时间：{{ createTime }}</span>
        </div>
        <div class="deadView-info">
          <div style="overflow: hidden">
            <div class="deadView-info-img">
              <el-image :src="formatProductImg(info)" lazy>
                <div slot="error" class="image-slot">
                  <i class="el-icon-picture-outline"></i>
                </div>
              </el-image>
            </div>
            <div class="deadView-info-con">
              <div class="deadtitle">{{ info.productName }}</div>
              <div class="deadprice">
                <b>￥{{ info.price }}</b>
                <span>/吨</span>
              </div>
              <div class="deadnum">
                <span>规格</span>
                <span>：{{ info.specs }}</span>
              </div>
              <div class="deadnum">
                <span>单重</span>
                <span>：{{ info.weight }}Kg</span>
              </div>
              <div class="deadnum">
                <span>库存</span>
                <span>：{{ info.stock + info.unit }}</span>
              </div>
              <div class="deadnum">
                <span>采购数量</span>
                <span>
                  ：
                  <el-input v-model="value" placeholder="请输入采购数量" style="width: 150px" size="small" @change="changeValue">
                    <em slot="suffix" style="display: inline-flex; height: 100%; font-style: normal">{{ info.unit }}</em>
                  </el-input>
                </span>
                <span class="deadnum" v-if="info.unit != '吨'" style="color: #f43f3f; margin-left: 10px">注：1吨约{{ parseFloat((1000 / Number(info.weight || 1)).toFixed(5)) + info.unit }}</span>
              </div>
            </div>
          </div>
          <div class="deadView-info-total">
            <div class="text" style="display: inline-flex">
              <div>
                共
                <b>{{ totalNum || '--' }}</b>
                {{ info.unit }}
              </div>
              <div style="margin-left: 20px" v-if="info.unit != '吨'">
                共
                <b>{{ totalValue || '--' }}</b>
                吨
              </div>
            </div>
            <div class="text">
              订单总金额：
              <b>{{ totalValue ? `￥${totalPrice}` : '--' }}</b>
            </div>
          </div>
        </div>
        <div class="deadView-button">
          <el-button @click="handleClose">取消</el-button>
          <el-button type="primary" :disabled="isSubmit" @click="handleSubmit">提交订单</el-button>
        </div>
      </div>
      <div class="webContainer" v-else>
        <div class="order-success">
          <i class="el-icon-success order-success-img"></i>
          <div class="order-success-title">订单提交成功</div>
          <div class="order-success-tip">订单提交成功后请尽快生成合同完成商品购买流程，超时操作可能造成订单商品库存不足或订单失效</div>
          <div class="order-success-info">有任何疑问请您及时联系客服。</div>
          <div class="order-success-button">
            <el-button type="primary" plain @click="handleLink('/deadstock')">继续逛逛</el-button>
            <el-button type="primary" @click="handleLink('/demand/unsalableorder')">查看订单</el-button>
          </div>
        </div>
      </div>
    </div>
    <foot-tpl />
  </div>
</template>
<script>
import headTpl from '@/views/public/components/head'
import footTpl from '@/views/public/components/foot'
import { getToken } from '@/utils/auth'
import { getUnsalable, createUnsalableOrder, payUnsalableOrder } from '@/api/unsalable'

export default {
  name: 'detail',
  components: { footTpl, headTpl },
  data() {
    return {
      isLogin: false,
      value: undefined,
      totalValue: undefined,
      totalPrice: undefined,
      productId: undefined,
      info: {},
      orderId: undefined,
      serial: undefined,
      createTime: new Date().toLocaleString(),
      isSubmit: true,
      totalNum: undefined,
      isOrder: false
    }
  },
  created() {
    this.isLogin = !!getToken()
    const query = this.$route.query
    if (query.id) {
      this.productId = query.id
      this.getInfo()
      if (this.isLogin) this.getNum()
    } else {
      this.$alert('参数错误，请联系管理员', '系统提示', {
        type: 'error',
        confirmButtonText: '确定',
        callback: action => {
          this.$router.push('/deadstock')
        }
      })
    }
  },
  methods: {
    getInfo() {
      getUnsalable(this.productId).then(res => {
        if (res.code === 200) {
          this.isOrder = false
          this.info = res.data
        } else {
          this.$alert('系统错误，请联系管理员', '系统提示', {
            type: 'error',
            confirmButtonText: '确定',
            callback: action => {
              this.$router.push('/')
            }
          })
        }
      })
    },
    getNum() {
      createUnsalableOrder().then(res => {
        if (res.code === 200) {
          this.orderId = res.data.id
          this.serial = res.data.serial
        } else this.$message.error(res.msg)
      })
    },
    changeValue() {
      const reg = /^\d+(\.\d{1,6})?$/
      const value = this.info.unit == '吨' ? this.value : parseFloat(((this.value * this.info.weight) / 1000).toFixed(5))
      if (!reg.test(value)) {
        this.$message.error('请输入正确的采购数量')
        this.value = undefined
        this.isSubmit = true
        return false
      } else {
        this.totalNum = this.value
        this.isSubmit = false
        this.totalValue = value
        this.totalPrice = parseFloat((Number(this.info.price) * Number(value)).toFixed(5))
      }
    },
    handleClose() {
      window.close()
    },
    // 提交订单
    handleSubmit() {
      if (this.isLogin) {
        const data = {
          amount: this.totalPrice,
          orderId: this.orderId,
          productId: this.info.id,
          productName: this.info.productName,
          quantity: this.totalNum,
          unitPrice: this.info.price,
        }
        payUnsalableOrder(data).then(res => {
          if (res.code === 200) {
            this.$message.success('提交成功')
            this.isOrder = true
          } else this.$message.error(res.msg)
        })
      } else {
        this.$alert('请先登录', '系统提示', {
          type: 'error',
          confirmButtonText: '确定',
          callback: action => {
            this.$router.push('/login')
          }
        })
      }
    },
    // 跳转链接
    handleLink(url) {
      this.$router.push(url)
    },
  }
}
</script>

<style scoped lang="scss">
@import '~@/assets/styles/custom-public.scss';
</style>
