<template>
  <div>
    <el-dialog v-dialogDragBox title="修改信息" :visible.sync="open" width="1150px" class="custom-dialog">
      <div class="formBox">
        <el-form ref="form" :model="form" :rules="rules" label-width="8em">
          <el-row :gutter="10">
            <el-col :span="12">
              <el-form-item label="公司名称" prop="name">
                <el-input v-model="form.name" placeholder="请输入公司名称" readonly></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="公司法人" prop="legal">
                <el-input v-model="form.legal" placeholder="请输入公司法人"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="公司地址" prop="address">
                <el-input v-model="form.address" placeholder="请输入公司地址" readonly></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="公司标语" prop="slogan">
                <el-input v-model="form.slogan" placeholder="请输入公司标语"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="Logo简称" prop="showName">
                <el-input v-model="form.showName" maxlength="5" placeholder="请输入Logo简称"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="经营产品" prop="businessScope">
                <el-input v-model="form.businessScope" :autosize="{ minRows: 3, maxRows: 6 }" resize="none" placeholder="请输入经营产品"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-table :data="form.banks" style="width: 100%" stripe class="custom-table custom-table-cell0">
                <el-table-column align="center" prop="bankName" label="开户行">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`banks.${scope.$index}.bankName`" :rules="rules.bankName">
                      <el-input v-model="scope.row.bankName" size="small" placeholder="请输入开户行" />
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column align="center" prop="bankUser" label="账户名">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`banks.${scope.$index}.bankUser`" :rules="rules.bankUser">
                      <el-input v-model="scope.row.bankUser" size="small" placeholder="请输入账户名" />
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column align="center" prop="bankNo" label="银行账号">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`banks.${scope.$index}.bankNo`" :rules="rules.bankNo">
                      <el-input v-model="scope.row.bankNo" size="small" placeholder="请输入银行账号" />
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column align="center" prop="bankNo" label="默认选中" width="80">
                  <template slot-scope="scope">
                    <el-form-item label-width="0">
                      <el-checkbox v-model="scope.row.checked" :disabled="form.banks.find(item => item.checked) && scope.$index !== form.banks.findIndex(item => item.checked)" />
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column align="center" label="操作">
                  <template slot-scope="scope">
                    <el-button type="primary" size="small" icon="el-icon-plus" @click="handlePlusBank">添加</el-button>
                    <el-button type="danger" size="small" icon="el-icon-delete" :disabled="form.banks.length === 1" @click="handleDelBank(scope.$index)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
            <el-col :span="24">
              <el-table :data="form.contacts" style="width: 100%" stripe class="custom-table custom-table-cell0">
                <el-table-column align="center" prop="bankNo" label="默认选中" width="80">
                  <template slot-scope="scope">
                    <el-form-item label-width="0">
                      <el-checkbox v-model="scope.row.checked" :disabled="form.contacts.find(item => item.checked) && scope.$index !== form.contacts.findIndex(item => item.checked)" />
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column align="center" prop="nickName" label="联系人姓名">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`contacts.${scope.$index}.nickName`" :rules="rules.nickName">
                      <el-input v-model="scope.row.nickName" size="small" placeholder="请输入联系人姓名" />
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column align="center" prop="phone" label="联系人电话">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`contacts.${scope.$index}.phone`" :rules="rules.phone">
                      <el-input v-model="scope.row.phone" size="small" placeholder="请输入联系人电话" />
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column align="center" prop="post" label="联系人职务">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`contacts.${scope.$index}.post`" :rules="rules.post">
                      <el-input v-model="scope.row.post" size="small" placeholder="请输入联系人职务" />
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column align="center" prop="post" label="授权有效期">
                  <template slot-scope="scope">
                    <el-tooltip class="item" effect="dark" :content="`有效期至：${parseTime(scope.row.authTimeEnd, '{y}年{m}月{d}日')}`" placement="top" v-if="!!scope.row.authTimeEnd">
                      <el-button type="text" icon="el-icon-edit" size="small" @click="handleAddImpower(scope.row, scope.$index)">修改授权</el-button>
                    </el-tooltip>
                    <el-button type="text" icon="el-icon-plus" size="small" @click="handleAddImpower(scope.row, scope.$index)" v-else>添加授权</el-button>
                  </template>
                </el-table-column>
                <el-table-column align="center" label="操作">
                  <template slot-scope="scope">
                    <el-button type="primary" size="small" icon="el-icon-plus" @click="handlePlus">添加</el-button>
                    <el-button type="danger" size="small" icon="el-icon-delete" :disabled="form.contacts.length === 1" @click="handleDel(scope.$index)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <button type="button" class="custom-dialog-btn" @click="open = false">取消</button>
        <button type="button" class="custom-dialog-btn primary" @click="handleSubmit">确定</button>
      </div>
    </el-dialog>
    <!-- 授权信息 -->
    <el-dialog v-dialogDragBox title="授权信息" :visible.sync="impowerOpen" width="800px" class="custom-dialog">
      <div style="padding: 0 20px">
        <el-form ref="impowerForm" :model="impowerForm" :rules="impowerRules" label-width="8em">
          <el-form-item label="联系人姓名">
            <span>{{ impowerForm.nickName }}</span>
          </el-form-item>
          <el-form-item label="真实姓名" prop="realName">
            <el-input style="width: 60%" placeholder="请输入真实姓名" v-model="impowerForm.realName"></el-input>
          </el-form-item>
          <el-form-item label="身份证号" prop="idCard">
            <el-input style="width: 60%" placeholder="请输入身份证号" v-model="impowerForm.idCard"></el-input>
          </el-form-item>
          <el-form-item label="授权起止时间" prop="timeArr">
            <el-date-picker style="width: 60%" v-model="impowerForm.timeArr" value-format="yyyy-MM-dd HH:mm:ss" type="daterange" start-placeholder="开始日期" end-placeholder="结束日期" :default-time="['00:00:00', '23:59:59']" :picker-options="pickerOptions"></el-date-picker>
          </el-form-item>
          <el-form-item label="授权委托书" prop="authLetter">
            <image-upload :limit="1" v-model="impowerForm.authLetter" />
          </el-form-item>
          <el-form-item label="身份证复印件" prop="idCardImage">
            <image-upload :limit="1" v-model="impowerForm.idCardImage" />
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer">
        <button class="custom-dialog-btn" @click="impowerOpen = false">取 消</button>
        <button class="custom-dialog-btn primary" @click="handleSubmitImpower">确 定</button>
      </span>
    </el-dialog>

    <!--  添加/修改授权信息  -->
    <authorization ref="authorization" @refresh="handleRefresh" />
  </div>
</template>

<script>
import { ediSupplier, supplier } from '@/api/system/user'
import authorization from '@/components/authorization/index'

export default {
  components: { authorization },
  props: {
    isLatent: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      open: false,
      form: {},
      rules: {
        // name: [{ required: true, message: '请输入公司名称', trigger: 'blur' }],
        // address: [{ required: true, message: '请输入公司地址', trigger: 'blur' }],
        // slogan: [
        //   { required: true, message: '请输入公司标语', trigger: 'blur' },
        //   { min: 1, max: 16, message: '长度在 1 到 16 个字符', trigger: 'blur' }
        // ],
        nickName: [{ required: true, message: '请输入联系人姓名', trigger: 'blur' }],
        phone: [
          { required: true, message: '请输入联系人电话', trigger: 'blur' },
          { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的电话号码', trigger: 'blur' }
        ],
        bankName: [{ required: true, message: '请输入开户行', trigger: 'blur' }],
        bankNo: [
          { required: true, message: '请输入银行账号', trigger: 'blur' }
          // { pattern: /^\d{9,29}$/, message: '请输入正确的银行账号', trigger: 'blur' }
        ]
      },
      impowerOpen: false,
      impowerIndex: 0,
      impowerForm: {},
      impowerRules: {
        realName: [{ required: true, message: '请输入真实姓名', trigger: 'blur' }],
        idCard: [
          { required: true, message: '请输入身份证号', trigger: 'blur' },
          { pattern: /^[1-9]\d{5}(18|19|([23]\d))\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}(\d|X|x)$/, message: '请输入正确的身份证号', trigger: 'blur' }
        ],
        timeArr: [{ required: true, message: '请选择授权起止时间', trigger: 'change' }],
        authLetter: [{ required: true, message: '请上传授权书', trigger: ['blur', 'change'] }],
        idCardImage: [{ required: true, message: '请上传身份证复印件', trigger: ['blur', 'change'] }]
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now()
        }
      }
    }
  },
  created() {
    if (this.isLatent) {
      delete this.rules.bankName
      delete this.rules.bankNo
    }
  },
  methods: {
    reset() {
      this.form = {
        name: undefined,
        legal: undefined,
        phone: undefined,
        lat: undefined,
        lng: undefined,
        businessScope: undefined,
        certificationUrl: undefined,
        address: undefined,
        logoUrl: undefined,
        slogan: undefined,
        showName: undefined,
        siteImg: undefined,
        certImg: undefined,
        deviceImg: undefined,
        process: undefined,
        contacts: [{ nickName: undefined, phone: undefined, post: undefined, checked: false, uuid: undefined }],
        banks: [{ bankName: undefined, bankNo: undefined, bankUser: undefined, checked: false }],
        productIds: []
      }
      this.resetForm('form')
    },
    getInfo(data) {
      this.reset()
      supplier({ id: data.id }).then(res => {
        const { code, data } = res
        if (code === 200) {
          let form = { ...data.company, ...data.supplier, ...{ contacts: data.contacts } }
          form.banks = form.bankList.length ? form.bankList : [{ bankName: undefined, bankNo: undefined, bankUser: undefined, checked: false }]
          delete form.bankList
          this.form = form
          this.open = true
        } else this.$message.error(msg)
      })
    },
    // 添加联系人
    handlePlus() {
      this.form.contacts.push({
        nickName: undefined,
        phone: undefined,
        post: undefined,
        checked: false,
        uuid: undefined
      })
    },
    // 删除联系人
    handleDel(index) {
      if (this.form.contacts.length === 1) return
      this.form.contacts.splice(index, 1)
    },
    // 添加银行
    handlePlusBank() {
      this.form.banks.push({ bankName: undefined, bankNo: undefined, checked: false })
    },
    // 删除银行
    handleDelBank(index) {
      if (this.form.banks.length === 1) return
      this.form.banks.splice(index, 1)
    },
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          if (this.isLatent) {
            if (!this.form.banks[0].hasOwnProperty('bankNo') || !this.form.banks[0].bankNo) delete this.form.banks
          }
          this.form.companyName = this.form.name
          this.form.supplierId = this.form.id
          ediSupplier(this.form).then(res => {
            const { code, msg } = res
            if (code === 200) {
              this.$message.success('修改成功')
              this.$emit('submit', this.form.id)
              this.open = false
            } else this.$message.error(msg)
          })
        }
      })
    },
    // 授权信息初始化
    handleInitImpower() {
      this.impowerForm = {
        realName: undefined,
        idCard: undefined,
        authTimeStart: undefined,
        authTimeEnd: undefined,
        timeArr: undefined,
        authLetter: undefined,
        idCardImage: undefined
      }
      this.resetForm('impowerForm')
    },
    // 打开授权信息窗口
    handleAddImpower(data = {}, index = 0) {
      if (data.uuid) this.$refs.authorization.handleOpen(data, index)
      else {
        this.impowerOpen = true
        this.handleInitImpower()
        this.impowerIndex = index
        this.impowerForm = { ...data }
        if (data.authTimeStart && data.authTimeEnd) {
          this.$set(this.impowerForm, 'timeArr', [data.authTimeStart, data.authTimeEnd])
        }
      }
    },
    // 授权信息保存
    handleSubmitImpower() {
      this.$refs.impowerForm.validate(valid => {
        if (valid) {
          this.impowerForm.authTimeStart = this.impowerForm.timeArr[0]
          this.impowerForm.authTimeEnd = this.impowerForm.timeArr[1]
          const arr = [...this.form.contacts]
          arr[this.impowerIndex] = { ...arr[this.impowerIndex], ...this.impowerForm }
          this.form.contacts = [...arr]
          this.impowerOpen = false
        }
      })
    },
    // 刷新
    handleRefresh(data, index) {
      let arr = [...this.form.contacts]
      arr[index] = { ...arr[index], ...data }
      this.form.contacts = [...arr]
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
.formBox {
  padding: 10px 20px 0;
}
::v-deep {
  .custom-table {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    border-bottom: 0;
    margin-bottom: 20px;
    .el-form-item {
      margin-top: 10px !important;
      margin-bottom: 10px !important;
      .el-form-item__error {
        top: 95%;
        padding-top: 0;
      }
    }
  }
}
</style>
