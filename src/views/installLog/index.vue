<template>
  <div class="newBox bgcf9 vh-85">
    <!-- 搜索 -->
    <div class="custom-search flex" style="padding-top: 18px">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" @submit.native.prevent>
        <el-form-item label="项目名称" prop="projectName">
          <el-input v-model="queryParams.projectName" placeholder="请输入项目名称" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <!--状态-->
        <el-form-item label="状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
            <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="small" @click="handleResetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <!-- 分类 -->
    <div class="classify flex">
      <div class="classify-item" :class="{ active: queryParams.identity === item.value }" v-for="item in identityOptions" :key="item.value" @click="handleIdentityChange(item)">{{ item.label }}</div>
    </div>
    <!-- 列表 -->
    <div style="padding: 15px 20px 0">
      <el-table v-loading="loading" ref="allTable" stripe :data="list" row-key="id" style="width: 100%" class="custom-table">
        <el-table-column align="center" type="index" label="序号"></el-table-column>
        <el-table-column align="center" prop="projectName" label="项目名称" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" prop="firstParty" label="甲方" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" prop="firstPartyContact" label="甲方负责人" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" prop="firstPartyPhone" label="负责人电话" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" label="地址" show-overflow-tooltip>
          <template slot-scope="{ row }">{{ row.province }}-{{ row.region }}{{ row.address }}</template>
        </el-table-column>
        <el-table-column align="center" prop="startTime" label="开始时间" show-overflow-tooltip>
          <template slot-scope="{ row }">{{ parseTime(row.startTime, '{y}-{m}-{d}') }}</template>
        </el-table-column>
        <el-table-column align="center" prop="endTime" label="结束时间" show-overflow-tooltip>
          <template slot-scope="{ row }">{{ parseTime(row.endTime, '{y}-{m}-{d}') }}</template>
        </el-table-column>
        <el-table-column align="center" prop="createTime" label="创建时间" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" prop="status" label="状态" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <div v-html="statusFormat(row)"></div>
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作" :width="queryParams.identity === 'creator' ? '220px' : '120px'">
          <template slot-scope="{ row }">
            <template v-if="queryParams.identity === 'creator'">
              <el-button class="table-btn primary" @click="handleDetail(row, 'creator')">查看详情</el-button>
              <el-button class="table-btn danger" v-if="row.status == 0" @click="handleDone(row)">完成/结束项目</el-button>
            </template>
            <el-button class="table-btn primary" v-if="queryParams.identity === 'approval'" @click="handleDetail(row, 'approval')">审批</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="custom-pagination">
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
      </div>
    </div>
    <!--详情-->
    <detail ref="detail" />
  </div>
</template>
<script>
import { endInstallLog, listInstallLog } from '@/api/installLog'
import { parseTime } from '@/utils/ruoyi'
import Detail from './detail'

export default {
  name: 'Installlog',
  components: { Detail },
  data() {
    return {
      // 类别
      identityOptions: [
        { label: '创建人', value: 'creator' },
        { label: '审批人', value: 'approval' }
      ],
      // 状态
      statusOptions: [
        { label: '未完成', value: 0, class: 'color-orange' },
        { label: '已完成', value: 1, class: 'color-blue' }
      ],
      loading: true,
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        identity: undefined,
        projectName: undefined,
        firstParty: undefined,
        firstPartyContact: undefined,
        firstPartyPhone: undefined,
        province: undefined,
        region: undefined,
        address: undefined,
        operator: undefined,
        status: undefined,
        approvalStatus: undefined
      },
      list: []
    }
  },
  created() {
    this.queryParams.identity = this.identityOptions[0].value
    this.getList()
  },
  methods: {
    parseTime,
    // 状态格式化回显
    statusFormat(row) {
      const obj = this.statusOptions.find(item => item.value === row.status)
      return `<span class="${obj.class}">${obj.label}</span>`
    },
    // 列表
    getList() {
      this.loading = true
      listInstallLog(this.queryParams).then(res => {
        const { code, msg, rows, total } = res
        if (code === 200) {
          this.list = rows
          this.total = total
          this.loading = false
        } else this.$message.error(msg)
      })
    },
    // 刷新列表
    refreshList() {
      this.loading = true
      listInstallLog(this.queryParams).then(res => {
        const { code, msg, rows, total } = res
        if (code === 200) {
          this.$set(this, 'list', rows)
          this.total = total
          this.loading = false
        }
      })
    },
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 重置搜索
    handleResetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 切换身份
    handleIdentityChange(item) {
      this.queryParams.identity = item.value
      this.handleResetQuery()
    },
    // 查看详情
    handleDetail(row, type) {
      this.$refs.detail.getInfo(row.id, type === 'approval')
    },
    // 完成/结束项目
    // prettier-ignore
    handleDone(row) {
      this.$confirm('是否完成/结束项目？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        endInstallLog({ id: row.id }).then(res => {
          const { code, msg } = res
          if (code === 200) {
            this.$message.success('操作成功')
            this.refreshList()
          } else this.$message.error(msg)
        })
      }).catch(() => {})
    }
  }
}
</script>
<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';
.custom-table {
  .el-button {
    padding: 0;
  }
}
</style>
