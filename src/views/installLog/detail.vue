<template>
  <div>
    <el-dialog v-dialogDragBox :title="title" :visible.sync="open" width="1150px" class="custom-dialog">
      <div class="infoBox">
        <el-descriptions title="项目详情" :column="3" border>
          <el-descriptions-item label="项目名称" :span="3">{{ info.projectName }}</el-descriptions-item>
          <el-descriptions-item label="地址" :span="3">{{ info.province }}-{{ info.region }}{{ info.address }}</el-descriptions-item>
          <el-descriptions-item label="甲方">{{ info.firstParty }}</el-descriptions-item>
          <el-descriptions-item label="甲方负责人">{{ info.firstPartyContact }}</el-descriptions-item>
          <el-descriptions-item label="负责人电话">{{ info.firstPartyPhone }}</el-descriptions-item>
          <el-descriptions-item label="备注" :span="3">{{ info.remark }}</el-descriptions-item>
          <el-descriptions-item label="起始日期" :span="3">{{ parseTime(info.startTime, '{y}-{m}-{d}') }} 至 {{ parseTime(info.endTime, '{y}-{m}-{d}') }}</el-descriptions-item>
        </el-descriptions>
        <el-tabs class="custom-tabs" v-model="activeName" @tab-click="handleTabChange" v-if="!!installationLogs.length">
          <el-tab-pane :name="item.id + ''" v-for="item in installationLogs" :key="item.id">
            <div class="tabTitle" slot="label">
              <span>{{ parseTime(item.createTime, '{y}-{m}') }}</span>
              <b>{{ parseTime(item.createTime, '{d}') }}</b>
            </div>
            <el-descriptions title="作业信息" :column="2" border>
              <div class="descSlot" slot="title">
                <div class="descTitle">作业信息</div>
                <div class="descSlot" v-if="info.workQuantity || info.totalAmount">
                  （
                  <div class="descItem" v-if="info.workQuantity">
                    累计作业量：
                    <span>{{ info.workQuantity + '套' }}</span>
                  </div>
                  <div class="descItem" v-if="info.totalAmount">
                    累计报销金额：
                    <span class="price">{{ '￥' + info.totalAmount + '元' }}</span>
                  </div>
                  ）
                </div>
              </div>
              <el-descriptions-item label="作业日期">{{ parseTime(item.createTime, '{y}-{m}-{d}') }}</el-descriptions-item>
              <el-descriptions-item label="作业量">{{ item.workQuantity + '套' }}</el-descriptions-item>
              <el-descriptions-item label="作业人员" :span="2">{{ item.operator }}</el-descriptions-item>
              <el-descriptions-item label="作业内容" :span="2">{{ item.content }}</el-descriptions-item>
              <el-descriptions-item label="补充信息" :span="2" v-if="item.supplement">{{ item.supplement }}</el-descriptions-item>
              <el-descriptions-item label="现场照片" :span="2">
                <div style="display: inline-flex" v-if="item.onSitePhotos_oss || item.onSitePhotos">
                  <image-preview is-list :src="item.onSitePhotos_oss || item.onSitePhotos" :width="50" :height="50" />
                </div>
                <span v-else>-</span>
              </el-descriptions-item>
              <el-descriptions-item label="报销金额" :span="2" v-if="item.amount">
                <span class="infoBoxPrice" v-if="item.amount">{{ '￥' + item.amount + '元' }}</span>
                <span v-else>-</span>
              </el-descriptions-item>
              <el-descriptions-item label="报销凭证" :span="2" v-if="item.amount">
                <div style="display: inline-flex" v-if="item.reimbursement_oss || item.reimbursement">
                  <image-preview is-list :src="item.reimbursement_oss || item.reimbursement" :width="50" :height="50" />
                </div>
                <span v-else>-</span>
              </el-descriptions-item>
            </el-descriptions>
          </el-tab-pane>
        </el-tabs>
        <template v-if="approvalLogs.length">
          <div class="followListTitle">审批记录</div>
          <div class="followList">
            <div class="followItem" v-for="item in approvalLogs">
              <div class="followInfo">
                <div class="followInfo-title">
                  {{ item.approvalName }}
                  <span style="font-size: 12px; color: #999999; margin-left: 10px">{{ item.createTime }}</span>
                </div>
                <div class="followInfo-con">{{ item.approvalInfo }}</div>
              </div>
            </div>
          </div>
        </template>
      </div>
      <div style="padding: 0 20px" v-if="isApproval">
        <div class="followListTitle">新增审批</div>
        <el-form ref="form" :model="form" :rules="rules" label-width="100px">
          <el-form-item label="审批备注" prop="approvalInfo">
            <el-input placeholder="请输入审批备注" v-model="form.approvalInfo" type="textarea" :autosize="{ minRows: 4, maxRows: 6 }" resize="none" class="custom-textarea"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer">
        <template v-if="isApproval">
          <el-button class="custom-dialog-btn" @click="open = false">取 消</el-button>
          <el-button class="custom-dialog-btn primary" @click="handleSubmit">确 定</el-button>
        </template>
        <el-button class="custom-dialog-btn primary" @click="open = false" v-else>关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { detailInstallLog, approvalInstallLog } from '@/api/installLog'
import { parseTime } from '@/utils/ruoyi'

export default {
  data() {
    return {
      projectId: undefined,
      isApproval: false,
      title: '查看详情',
      open: false,
      info: {},
      form: {},
      rules: {
        approvalInfo: [{ required: true, message: '请输入审批备注', trigger: 'blur' }]
      },
      activeName: '',
      installationLogs: [], // 作业记录
      approvalLogs: [] // 审批记录
    }
  },
  created() {},
  computed: {
    // 获取当前用户ID
    userId() {
      return this.$store.getters.info.userId
    }
  },
  methods: {
    parseTime,
    // 重置表单
    reset() {
      this.form = {
        projectId: undefined,
        approvalInfo: undefined
      }
      this.resetForm('form')
    },
    // 详情
    getInfo(projectId = undefined, isApproval = false) {
      if (!projectId) {
        this.$message.error('参数错误，请重试')
        return
      }
      this.projectId = projectId
      this.isApproval = isApproval
      this.title = isApproval ? '审批' : '查看详情'
      detailInstallLog({ projectId }).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          this.info = data
          const installationLogs = data.installationLogs.sort((a, b) => b.id - a.id) || []
          this.installationLogs = installationLogs || []
          this.activeName = installationLogs.length ? installationLogs[0].id + '' : ''
          this.approvalLogs = data.approvalLogs || []
          this.open = true
          this.reset()
        } else this.$message.error(msg)
      })
    },
    // 切换tab
    handleTabChange(tab) {
      this.activeName = tab.name
    },
    // 提交审批
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.form.projectId = this.projectId
          approvalInstallLog(this.form).then(res => {
            const { code, msg } = res
            if (code === 200) {
              this.$message.success('成功提交审批')
              this.open = false
              this.$parent.refreshList()
            } else this.$message.error(msg)
          })
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';
.infoBox {
  padding: 0 20px;
  ::v-deep {
    .el-descriptions__title {
      font-weight: normal;
    }
    .el-descriptions-item__label {
      width: calc(5em + 24px);
    }
  }
  &Price {
    font-size: 16px;
    font-weight: 500;
    color: $red;
  }
}
.followList {
  padding: 0 20px;
  background-color: #f7f9fc;
  &Title {
    padding: 20px 20px 10px 0;
    font-size: 16px;
    color: #303133;
  }
  .followItem {
    display: flex;
    padding-top: 15px;
    padding-bottom: 5px;
    border-bottom: 1px solid #e2e6f3;
    &:last-child {
      border-bottom: 0;
    }
    .followImg {
      width: 40px;
      height: 40px;
      border: 1px solid #f7f7f7;
      border-radius: 50%;
      flex-shrink: 0;
      margin-right: 10px;
    }
    .followInfo {
      width: 100%;
      display: flex;
      flex-direction: column;
      &-title {
        display: inline-block;
        width: 100%;
        line-height: 40px;
        color: $info;
      }
      &-con {
        font-weight: normal;
        line-height: 2em;
        color: $font;
      }
      &-file {
        display: inline-flex;
        align-items: center;
        flex-wrap: wrap;
        margin-top: 10px;
        &-img {
          width: 100px;
          height: 100px;
          margin-right: 10px;
          margin-bottom: 10px;
        }
        &-audio {
          width: 300px;
          height: 50px;
          margin-right: 10px;
          margin-bottom: 10px;
        }
        &-video {
          width: 300px;
          height: 300px;
          margin-right: 10px;
          margin-bottom: 10px;
        }
      }
    }
  }
}
.descSlot {
  display: flex;
  align-items: center;
  .descItem {
    font-size: 14px;
    margin: 0 5px;
    color: $info;
    span {
      color: $font;
      &.price {
        color: $red;
      }
    }
  }
}
::v-deep {
  .image-slot {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    background: #f5f7fa;
    color: #909399;
  }
  .el-form {
    .el-form-item__label {
      font-weight: normal;
      color: $disabled;
      text-align: left;
    }
  }
  .custom-tabs {
    margin-top: 20px;
    .el-tabs__nav-wrap {
      &:after {
        display: none;
      }
    }
    .el-tabs__nav-next,
    .el-tabs__nav-prev {
      line-height: 75px;
    }
    .el-tabs__active-bar {
      display: none;
    }
    .el-tabs__item {
      height: auto;
      line-height: normal;
      padding: 0 5px;
      .tabTitle {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 10px;
        background-color: #eaecf3;
        border-radius: 5px;
        span {
          font-size: 12px;
          line-height: 25px;
          color: $info;
        }
        b {
          font-weight: normal;
          font-size: 20px;
          line-height: 30px;
          color: $font;
        }
      }
      &.is-active {
        .tabTitle {
          background-color: $blue;
          span,
          b {
            color: $white;
          }
        }
      }
    }
  }
}
</style>
