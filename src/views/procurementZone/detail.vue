<template>
    <div class="container">
        <header-tpl :is-login="isLogin" />
        <div class="container-box">
            <div class="containerBox">
                <div class="detailBox">
                    <div class="tipsTime">
                        <span>
                            {{ new Date().getTime() >= new Date(info.effectiveTime).getTime() ? '已失效' :
                                expireTimeFormat(info) }}
                        </span>
                        <div class="closedBtn" v-if="formType == 2" @click="handleClosed">
                            <img src="@/assets/images/qg_closed.png" alt="">
                            <span>关闭求购</span>
                        </div>
                    </div>
                    <div class="infoBox">
                        <div class="info_title">{{ info.title }}</div>
                        <div class="info_basic">
                            <div class="info_basic_item">
                                <span class="basic_item_title">发布者：</span>
                                <div class="basic_item_concent">
                                    <!-- <img class="basic_item_concent_img" :src="info.avatar_oss || imgPath + info.avatar" alt=""> -->
                                    <!-- <img class="basic_item_concent_img" src="@/assets/images/qgzqtx.png" alt=""> -->
                                    <div class="basic_item_concent_text">
                                        <span class="nickName">{{ info.nickName }}</span>
                                        <span class="companyName">{{ info.companyName }}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="info_basic_item">
                                <span class="basic_item_title">发布时间：</span>
                                <span class="basic_item_text">{{ info.createTime }}</span>
                            </div>
                            <div class="info_basic_item">
                                <span class="basic_item_title">求购详细：</span>
                                <span class="basic_item_text">{{ info.detail }}</span>
                            </div>
                        </div>
                        <div class="info_list" v-if="info.type === 'm0'">
                            <div class="info_list_item" v-for="(item, index) in info.products" :key="index">
                                <div class="itemData">
                                    <span class="itemData_title">求购产品：</span>
                                    <span class="itemData_concent">{{ item.productName }}</span>
                                </div>
                                <div class="itemData">
                                    <span class="itemData_title">求购数量：</span>
                                    <span class="itemData_concent">{{ item.quantity + ' ' + item.unit }}</span>
                                </div>
                                <div class="itemData">
                                    <span class="itemData_title">产品图片：</span>
                                    <div class="itemData_imgBox">
                                        <el-image class="img" v-for="it in productImg(item)" :key="it" :src="it"
                                            :preview-src-list="productImg(item)"></el-image>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="info_list" v-if="info.type !== 'm0'">
                            <el-table :data="info.products" style="width: 100%; margin-bottom: 20px"
                                class="custom-table custom-table-cell0" v-if="info.products && info.products.length">
                                <el-table-column type="index" label="序号" width="50"></el-table-column>
                                <el-table-column prop="productName" label="产品名称" align="center" show-overflow-tooltip>
                                    <template slot-scope="{ row }">
                                        <span class="table-link" @click="handleView(row.productId, row)">{{
                                            row.productName
                                        }}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="specs" label="产品图片" align="center" show-overflow-tooltip>
                                    <template slot-scope="{ row }">
                                        <div style="display: flex; justify-content: center">
                                            <el-image :src="formatProductImg(row)" fit="cover"
                                                @click="handleImgView(row)">
                                                <div slot="error" class="image-slot">
                                                    <i class="el-icon-picture-outline"></i>
                                                </div>
                                            </el-image>
                                        </div>
                                    </template>
                                </el-table-column>
                                <el-table-column align="center" prop="specs" label="规格"
                                    show-overflow-tooltip></el-table-column>
                                <el-table-column prop="model" label="产品型号" align="center"
                                    show-overflow-tooltip></el-table-column>
                                <el-table-column align="center" prop="productCode" label="产品编号"
                                    show-overflow-tooltip></el-table-column>
                                <el-table-column align="center" prop="materialQuality" label="材质"></el-table-column>
                                <el-table-column align="center" prop="surface" label="表面处理"></el-table-column>
                                <el-table-column align="center" prop="unit" label="单位"></el-table-column>
                                <el-table-column prop="quantity" label="采购数量" align="center">
                                    <template slot-scope="scope">
                                        {{ scope.row.quantity + scope.row.unit }}
                                    </template>
                                </el-table-column>
                            </el-table>
                        </div>
                        <div class="info_reply">
                            <div class="info_reply_item" v-for="(item, index) in replyList" :key="index">
                                <div class="item_info">
                                    <img class="item_avatar" :src="item[0].jcAvatar_oss || imgPath + item[0].jcAvatar"
                                        alt="">
                                    <el-tooltip effect="dark" :content="item[0].nickName" placement="top">
                                        <span class="item_nickName">{{ item[0].nickName }}</span>
                                    </el-tooltip>
                                    <el-tooltip effect="dark" :content="item[0].companyName" placement="top">
                                        <span class="item_companyName">{{ item[0].companyName }}</span>
                                    </el-tooltip>
                                    <span class="item_replyTime">{{ item[0].replyTime }}</span>
                                </div>
                                <div class="item_btn" v-if="formType == 2">
                                    <div class="viewBtn" @click="viewQuotation(index)">查看报价</div>
                                    <div class="msgBtn" @click="handleContact(item[0])">
                                        <img class="msgImg" src="@/assets/images/msg.png" alt="">
                                        <span class="msgText">在线聊天</span>
                                    </div>
                                    <div class="telBtn" @mouseenter="item[0].showPhone = true"
                                        @mouseleave="item[0].showPhone = false">
                                        <img class="telImg" src="@/assets/images/tel.png" alt="">
                                        <span class="telText">{{ item[0].showPhone ? item[0].phone : '电话联系' }} </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="info_replyBtn" v-if="formType == 1 && !info.isReply">
                            <div class="info_replyBtn_top" @click="replyQuotation">我要报价</div>
                            <div class="info_replyBtn_bottom" @click="replyQuotation">我要报价</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <footer-tpl />

        <!-- 回复报价 -->
        <el-dialog v-dialogDragBox title="回复报价" :visible.sync="replyOpen" width="1150px" class="custom-dialog">
            <div class="replyBox">
                <div class="reply_info">
                    <div class="reply_info_item">
                        <div class="item">
                            <span class="item_title">求购标题</span>
                            <span class="item_text">{{ info.title }}</span>
                        </div>
                        <div class="item">
                            <span class="item_title">求购详细</span>
                            <span class="item_text">{{ info.detail }}</span>
                        </div>
                    </div>
                    <div class="reply_info_item">
                        <div class="item">
                            <span class="item_title">求购方</span>
                            <span class="item_text">{{ info.companyName }}</span>
                        </div>
                        <div class="item">
                            <span class="item_title">发布时间</span>
                            <span class="item_text">{{ info.createTime }}</span>
                        </div>
                        <div class="item">
                            <span class="item_title">有效时间</span>
                            <span class="item_text" style="color: #F35D09;">{{ new Date().getTime() >= new
                                Date(info.effectiveTime).getTime() ? '已失效' :
                                expireTimeFormat(info) }}</span>
                        </div>
                    </div>
                </div>
                <el-form ref="form" :model="info" :rules="rules" label-width="100px">
                    <el-table ref="addTable" stripe :data="info.products" style="width: 100%"
                        class="custom-table custom-table-cell5">
                        <el-table-column align="center" type="index" label="序号"></el-table-column>
                        <el-table-column align="center" prop="productName" label="产品名称" show-overflow-tooltip>
                            <template slot-scope="{ row }">
                                <span :class="info.type !== 'm0' ? 'table-link' : ''"
                                    @click="info.type !== 'm0' ? handleView(row.productId, row) : ''">
                                    {{ row.productName }}
                                </span>
                            </template>
                        </el-table-column>
                        <el-table-column align="center" prop="picture1" label="图片" width="75">
                            <template slot-scope="{ row }">
                                <div style="display: flex; justify-content: center">
                                    <el-image :src="formatProductImg(row)" fit="cover" @click="handleImgView(row)">
                                        <div slot="error" class="image-slot">
                                            <i class="el-icon-picture-outline"></i>
                                        </div>
                                    </el-image>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column align="center" prop="quantity" label="采购数量">
                            <template slot-scope="{ row }">
                                {{ row.quantity + row.unit }}
                            </template>
                        </el-table-column>
                        <el-table-column align="center" label="报价" width="300">
                            <template slot-scope="scope">
                                <el-form-item label-width="0" :prop="`products[${scope.$index}].amount`"
                                    :rules="rules.amount">
                                    <el-input v-model="scope.row.amount" size="small" placeholder="请输入报价">
                                        <template slot="append">
                                            <div class="flex" style="align-items: center;">
                                                <div style="margin-right: 5px; height: 30px; line-height: 30px;">元 /
                                                </div>
                                                <el-select style="width: 90px;" v-model="scope.row.replyUnit"
                                                    placeholder="请选择报价单位">
                                                    <el-option v-for="(replyUnit, i) in unitOptions" :key="i"
                                                        :label="replyUnit" :value="replyUnit"></el-option>
                                                </el-select>
                                            </div>
                                        </template>
                                    </el-input>
                                </el-form-item>
                            </template>
                        </el-table-column>
                        <el-table-column align="center" label="备注" width="300">
                            <template slot-scope="scope">
                                <el-form-item label-width="0" :prop="`products[${scope.$index}].remark`"
                                    :rules="rules.remark">
                                    <el-input v-model="scope.row.remark" size="small" placeholder="请输入备注">
                                    </el-input>
                                </el-form-item>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-form>
            </div>
            <div slot="footer">
                <div class="inline-flex">
                    <el-button type="button" class="custom-dialog-btn" @click="replyOpen = false">取消</el-button>
                    <el-button type="button" class="custom-dialog-btn primary" @click="handleSubmit">确定</el-button>
                </div>
            </div>
        </el-dialog>

        <!-- 查看报价 -->
        <el-dialog v-dialogDragBox title="查看报价" :visible.sync="viewReplyOpen" width="1150px" class="custom-dialog">
            <div class="viewReply">
                <div class="viewReply_company" v-for="(item, index) in replyList" :key="index">
                    <div class="companyName" @click="openReplyList(index)">
                        <span>{{ item[0].companyName }}</span>
                        <i :class="index === openReplyIndex ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
                    </div>
                    <el-table ref="addTable" stripe :data="item" style="width: 100%"
                        class="custom-table custom-table-cell5" v-if="index === openReplyIndex"
                        @selection-change="handleSelectionChange">
                        <el-table-column align="center" type="selection" width="50"
                            v-if="item[0].source !== 'none'"></el-table-column>
                        <el-table-column align="center" type="index" label="序号"></el-table-column>
                        <el-table-column align="center" prop="productName" label="产品名称" show-overflow-tooltip>
                            <template slot-scope="{ row }">
                                <span :class="info.type !== 'm0' ? 'table-link' : ''"
                                    @click="info.type !== 'm0' ? handleView(row.productId, row) : ''">
                                    {{ row.productName }}
                                </span>
                            </template>
                        </el-table-column>
                        <el-table-column align="center" prop="picture1" label="图片" width="75">
                            <template slot-scope="{ row }">
                                <div style="display: flex; justify-content: center">
                                    <el-image :src="formatProductImg(row)" fit="cover" @click="handleImgView(row)">
                                        <div slot="error" class="image-slot">
                                            <i class="el-icon-picture-outline"></i>
                                        </div>
                                    </el-image>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column align="center" prop="specs" label="规格" show-overflow-tooltip
                            v-if="item[0].source !== 'none'"></el-table-column>
                        <el-table-column align="center" prop="materialQuality" label="材质"
                            v-if="item[0].source !== 'none'"></el-table-column>
                        <el-table-column align="center" prop="surface" label="表面处理"
                            v-if="item[0].source !== 'none'"></el-table-column>
                        <el-table-column align="center" prop="amount" label="产品报价">
                            <template slot-scope="{ row }">
                                {{ row.amount + '元 / ' + row.replyUnit }}
                            </template>
                        </el-table-column>
                        <el-table-column align="center" prop="quantity" label="采购数量" width="300">
                            <template slot-scope="scope">
                                <el-input v-model="scope.row.quantity" size="small"
                                    :disabled="scope.row.source === 'none'" placeholder="请输入采购数量">
                                    <template slot="append">
                                        <div>{{ scope.row.replyUnit }}</div>
                                    </template>
                                </el-input>
                            </template>
                        </el-table-column>
                        <el-table-column align="center" prop="remark" label="备注"
                            show-overflow-tooltip></el-table-column>
                    </el-table>
                    <div class="viewReplyBtn" v-if="index === openReplyIndex">
                        <div class="viewReplyBtn_left">
                            <div v-if="item[0].source !== 'none'">
                                共选择
                                <span>{{ multipleSelection.length }}</span>
                                个产品
                            </div>
                        </div>
                        <div class="viewReplyBtn_right" v-if="item[0].source !== 'none'" @click="handleContarct">生成采购合同
                        </div>
                        <div class="viewReplyBtn_right1" v-if="item[0].source === 'none'">
                            <div class="msgBtn" @click="handleContact(item[0])">
                                <img class="msgImg" src="@/assets/images/msg.png" alt="">
                                <span class="msgText">在线聊天</span>
                            </div>
                            <div class="telBtn" @mouseenter="item[0].showPhone = true"
                                @mouseleave="item[0].showPhone = false">
                                <img class="telImg" src="@/assets/images/tel.png" alt="">
                                <span class="telText">{{ item[0].showPhone ? item[0].phone : '电话联系' }} </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </el-dialog>

        <!-- 产品详情 -->
        <product-dialog ref="productInfo"></product-dialog>

        <chat ref="chat" :showBadge="false" />

        <!--  生成合同  -->
        <offer-contract-dialog ref="contractDialog"></offer-contract-dialog>
        <!-- 检查是否企业用户 -->
         <check-company ref="checkCompany"></check-company>
    </div>
</template>

<script>
import HeaderTpl from '@/views/public/components/head'
import FooterTpl from '@/views/public/components/foot'
import { getToken } from '@/utils/auth'
import { detailBuy, replyBuy, indexReplyBuy, getBuyReplyList, deleteBuy } from '@/api/buy'
import { getProduct } from '@/api/system/product'
import { getPrivateduct } from '@/api/system/privateduct'
import ProductDialog from '@/views/public/product/dialog'
import Chat from '@/components/Chat/index'
import offerContractDialog from '@/views/purchase/demandForMe/offer'
import CheckCompany from '@/components/CheckCompany'

export default {
    components: { FooterTpl, HeaderTpl, ProductDialog, Chat, offerContractDialog, CheckCompany },
    data() {
        return {
            isLogin: !!getToken(),
            info: {},
            replyList: [],
            formType: 1,
            replyOpen: false,
            unitOptions: ['吨', '千克', '个', '件', '套', '米', '支', '根'], // 单位
            rules: {
                remark: [
                    { required: true, message: "请输入备注", trigger: 'blur' }
                ],
                amount: [
                    { required: true, message: "请输入回复报价", trigger: 'blur' }
                ],
            },
            viewReplyOpen: false,
            openReplyIndex: 0,
            multipleSelection: []
        }
    },
    created() {
        if (!this.isLogin) {
            this.$router.replace('/login')
        }
        if (this.$route.query.leadsId) {
            this.getInfo(this.$route.query.leadsId)
        } else {
            this.$message.error('缺少求购信息ID')
            this.$router.replace(`/`)
        }
        this.formType = this.$route.query.type

    },
    computed: {
        companyId() {
            return this.$store.state.user.companyId || -1
        }
    },
    methods: {
        // 获取详情
        getInfo(leadsId) {
            detailBuy({
                leadsId: leadsId
            }).then(res => {
                const { code, msg, data } = res
                if (code === 200) {
                    data.countdown = undefined
                    if(data.products && data.products.length>0){
                        data.products.forEach(el => {
                            el.replyUnit = el.unit
                            el.remark = ''
                        })
                    }
                    indexReplyBuy({
                        leadsId: leadsId
                    }).then(response => {
                        if (response.code === 200) {
                            data.isReply = !!response.data.length
                            this.info = data
                            this.getReplyList(leadsId)
                        } else this.$message.error(response.msg)
                    })

                } else this.$message.error(msg)
            })
        },
        // 获取回复
        getReplyList(leadsId) {
            this.replyList = []
            getBuyReplyList({
                leadsId: leadsId
            }).then(res => {
                const { code, msg, data } = res
                if (code == 200) {
                    for (const key in data) {
                        if (Object.prototype.hasOwnProperty.call(data, key)) {
                            let element = data[key];
                            element.forEach(el => {
                                el.showPhone = false
                                if (this.info.products.find(item => item.productId == el.productId)) {
                                    el.specs = this.info.products.find(item => item.productId == el.productId).specs
                                    el.model = this.info.products.find(item => item.productId == el.productId).model
                                    el.materialQuality = this.info.products.find(item => item.productId == el.productId).materialQuality
                                    el.surface = this.info.products.find(item => item.productId == el.productId).surface
                                    el.quantity = this.info.products.find(item => item.productId == el.productId).quantity
                                    el.img = this.info.products.find(item => item.productId == el.productId).img
                                    el.img_oss = this.info.products.find(item => item.productId == el.productId).img_oss
                                    el.picture1 = this.info.products.find(item => item.productId == el.productId).picture1
                                    el.picture1_oss = this.info.products.find(item => item.productId == el.productId).picture1_oss
                                    el.draw = this.info.products.find(item => item.productId == el.productId).draw
                                    el.draw_oss = this.info.products.find(item => item.productId == el.productId).draw_oss
                                    el.diagram = this.info.products.find(item => item.productId == el.productId).diagram
                                    el.diagram_oss = this.info.products.find(item => item.productId == el.productId).diagram_oss
                                    el.productCode = this.info.products.find(item => item.productId == el.productId).productCode
                                    el.unit = this.info.products.find(item => item.productId == el.productId).unit
                                }
                            })
                            this.replyList.push(element)
                        }
                    }
                } else this.$message.error(msg)
            })
        },
        // 我要报价
        replyQuotation() {
            if(this.companyId<0){
                this.$refs.checkCompany.showDialog()
                return
            }
            this.replyOpen = true
        },
        // 倒计时
        expireTimeFormat(item) {
            function fn() {
                const dateEnd = new Date(item.effectiveTime)
                const dateBegin = new Date()
                const dateDiff = dateEnd.getTime() - dateBegin.getTime()
                if (dateDiff > 0) {
                    if (dateDiff <= 0) {
                        clearInterval(timer) // 停止定时器
                        return
                    }
                    // 将剩余时间转换为天、小时、分钟和秒
                    var days = Math.floor(dateDiff / (1000 * 60 * 60 * 24))
                    var hours = Math.floor((dateDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
                    var minutes = Math.floor((dateDiff % (1000 * 60 * 60)) / (1000 * 60))
                    var seconds = Math.floor((dateDiff % (1000 * 60)) / 1000)
                    if (days === 0 && hours === 0 && minutes === 0) {
                        item.countdown = seconds + '秒后失效'
                    } else if (days === 0 && hours === 0) {
                        item.countdown = minutes + '分钟后失效'
                    } else if (days === 0) {
                        item.countdown = hours + '小时' + minutes + '分钟后失效'
                    } else {
                        item.countdown = days + '天' + hours + '小时' + minutes + '分钟后失效'
                    }
                    return item.countdown
                }
            }
            // 第一次调用
            fn()
            // 每秒执行一次
            var timer = setInterval(fn, 1000)
            return item.countdown
        },
        // 图片处理
        productImg(img) {
            if (JSON.stringify(img) === '{}') {
                return ''
            }
            if (img.picture_oss) {
                if (img.picture_oss.includes(',')) {
                    return img.picture_oss.split(',')
                }
                return [img.picture_oss]
            }
            if (img.picture1_oss) {
                if (img.picture1_oss.includes(',')) {
                    return img.picture1_oss.split(',')
                }
                return [img.picture1_oss]
            }
            if (img.diagram_oss) {
                if (img.diagram_oss.includes(',')) {
                    return img.diagram_oss.split(',')
                }
                return [img.diagram_oss]
            }
            if (img.picture) {
                if (img.picture.includes(',')) {
                    let arr = img.picture.split(',').forEach(el => {
                        el = this.imgPath + el
                    });
                    return arr
                }
                return [this.imgPath + img.picture]
            }
            if (img.picture1) {
                if (img.picture1.includes(',')) {
                    let arr = img.picture1.split(',').forEach(el => {
                        el = this.imgPath + el
                    });
                    return arr
                }
                return [this.imgPath + img.picture1]
            }
            if (img.diagram) {
                if (img.diagram.includes(',')) {
                    let arr = img.diagram.split(',').forEach(el => {
                        el = this.imgPath + el
                    });
                    return arr
                }
                return [this.imgPath + img.diagram]
            }
            if (img.img_oss) {
                if (img.img_oss.includes(',')) {
                    return img.img_oss.split(',')
                }
                return [img.img_oss]
            }
            if (img.img) {
                if (img.img.includes(',')) {
                    let arr = img.img.split(',').forEach(el => {
                        el = this.imgPath + el
                    });
                    return arr
                }
                return [this.imgPath + img.img]
            }
            return []
        },
        // 产品详情
        handleView(Id, row) {
            if (row.source === 'common') {
                getProduct(Id).then(res => {
                    this.$refs.productInfo.handleView(res.data)
                })
            } else {
                getPrivateduct(Id).then(res => {
                    this.$refs.productInfo.handleView(res.data)
                })
            }
        },
        // 图片详情
        handleImgView(row) {
            this.$refs.productInfo.handleImgView(row)
        },
        // 回复报价
        handleSubmit() {
            this.$refs.form.validate(valid => {
                if (valid) {
                    let data = {
                        leadsId: this.info.id,
                        reply: []
                    }
                    this.info.products.forEach(el => {
                        let obj = {
                            amount: el.amount,
                            productId: el.productId,
                            productName: el.productName,
                            remark: el.remark,
                            replyUnit: el.replyUnit,
                            source: el.source,
                        }
                        data.reply.push(obj)
                    })
                    replyBuy(data).then(res => {
                        const { code, msg } = res
                        if (code === 200) {
                            this.$message.success('回复成功')
                            this.replyOpen = false
                        } else this.$message.error(msg)
                    })
                }
            })
        },
        // 查看报价
        viewQuotation(index) {
            this.openReplyIndex = index
            this.viewReplyOpen = true
        },
        openReplyList(index) {
            this.multipleSelection = []
            this.openReplyIndex = index
        },
        handleSelectionChange(val) {
            this.multipleSelection = val;
            console.log(this.multipleSelection)
        },
        // 点击聊天
        handleContact(row) {
            const data = {
                userId: row.userId,
                nick: row.nickName,
                avatar: row.jcAvatar_oss || this.imgPath + row.jcAvatar
            }
            this.$refs.chat.send(data)
        },
        // 生成合同
        handleContarct() {
            if (!this.multipleSelection.length) return
            const sellerUser = { companyId: this.multipleSelection[0].companyId }
            const list = this.multipleSelection.map(item => {
                return {
                    maxNum: item.maxNum,
                    source: item.source,
                    productId: item.productId,
                    productName: item.productName,
                    productCode: item.productCode || '',
                    specs: item.specs,
                    model: item.model,
                    unit: item.unit,
                    quantity: item.quantity,
                    needQuantity: item.quantity,
                    listId: -1,
                    remark: item.remark,
                    amount: item.amount,
                    originAmount: item.amount,
                    replyUnit: item.replyUnit,
                    sjNum: item.quantity,
                    replyRemark: item.remark,
                    endUnit: item.replyUnit
                }
            })
            this.$refs.contractDialog.handleGetContract(sellerUser, list, 'yes', '')
            this.viewReplyOpen = false
        },
        handleClosed() {
            this.$confirm('是否关闭该求购？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                deleteBuy({ leadsId: this.info.id }).then(res => {
                    const { code, msg } = res
                    if (code === 200) {
                        this.$message.success('关闭成功')
                        this.$router.go(-1)
                    } else this.$message.error(msg)
                })
            })
        },
    }
}
</script>

<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';

.container-box {
    min-width: 1200px;
}

.containerBox {
    width: 1200px;
    min-height: 500px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    padding-bottom: 50px;

    .detailBox {
        margin-top: 20px;
        background: #FFFFFF;
        box-shadow: 0px 0px 26px 0px rgba(0, 0, 0, 0.05);
        border-radius: 5px;

        .tipsTime {
            height: 38px;
            background: #FFE7E6;
            text-align: center;
            font-weight: 500;
            font-size: 16px;
            color: #FC372A;
            line-height: 38px;
            position: relative;
            cursor: pointer;

            .closedBtn {
                position: absolute;
                right: 0;
                top: 0;
                width: 120px;
                height: 38px;
                background: #FC372A;
                border-radius: 5px 5px 5px 5px;
                display: flex;
                align-items: center;
                justify-content: center;

                img {
                    width: 20px;
                    height: 20px;
                    margin-right: 5px;
                }

                span {
                    font-weight: 500;
                    font-size: 14px;
                    color: #FFFFFF;
                }
            }
        }

        .infoBox {
            padding: 20px;
            position: relative;

            .info_title {
                width: 850px;
                font-weight: 500;
                font-size: 32px;
                color: #333333;
                line-height: 38px;
                margin-bottom: 10px;
            }

            .info_basic {
                .info_basic_item {
                    margin-bottom: 27px;
                    display: flex;
                    align-items: center;

                    .basic_item_title {
                        width: 70px;
                        font-weight: 400;
                        font-size: 14px;
                        color: #999999;
                        line-height: 20px;
                        margin-right: 15px;
                    }

                    .basic_item_concent {
                        flex: 1;
                        display: flex;
                        align-items: center;

                        .basic_item_concent_img {
                            width: 46px;
                            height: 46px;
                            margin-right: 15px;
                        }

                        .basic_item_concent_text {
                            display: flex;
                            flex-direction: column;

                            .nickName {
                                font-weight: 500;
                                font-size: 16px;
                                color: #333333;
                                line-height: 20px;
                            }

                            .companyName {
                                font-weight: 400;
                                font-size: 12px;
                                color: #666666;
                                line-height: 14px;
                            }
                        }
                    }

                    .basic_item_text {
                        flex: 1;
                    }

                    &:nth-child(3) {
                        align-items: flex-start;
                    }
                }
            }

            .info_list {
                .info_list_item {
                    background: #FFFFFF;
                    border-radius: 5px;
                    border: 1px solid #CBD6E2;
                    padding: 17px 15px 0;
                    margin-bottom: 15px;

                    .itemData {
                        display: flex;
                        align-items: center;
                        margin-bottom: 24px;

                        &:nth-child(3) {
                            align-items: flex-start;
                        }

                        .itemData_title {
                            font-weight: 400;
                            font-size: 14px;
                            color: #999999;
                            line-height: 20px;
                            margin-right: 15px;
                        }

                        .itemData_concent {
                            flex: 1;
                            font-weight: 500;
                            font-size: 14px;
                            color: #333333;
                            line-height: 20px;
                        }

                        .itemData_imgBox {
                            flex: 1;
                            display: flex;
                            flex-wrap: wrap;

                            .img {
                                width: 136px;
                                height: 136px;
                                margin: 0 10px 10px 0;
                            }
                        }
                    }
                }
            }

            .info_reply {
                .info_reply_item {
                    height: 76px;
                    background: #F8F8F8;
                    border-radius: 5px;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    padding: 0 15px;
                    margin-bottom: 10px;

                    .item_info {
                        display: flex;
                        align-items: center;

                        .item_avatar {
                            width: 46px;
                            height: 46px;
                            margin-right: 20px;
                        }

                        .item_nickName {
                            width: 200px;
                            font-weight: 500;
                            font-size: 16px;
                            color: #333333;
                            margin-right: 10px;
                            white-space: nowrap;
                            text-overflow: ellipsis;
                            overflow: hidden;
                        }

                        .item_companyName {
                            width: 200px;
                            font-weight: 400;
                            font-size: 12px;
                            color: #999999;
                            margin-right: 90px;
                            white-space: nowrap;
                            text-overflow: ellipsis;
                            overflow: hidden;
                        }

                        .item_replyTime {
                            font-weight: 400;
                            font-size: 12px;
                            color: #999999;
                        }
                    }

                    .item_btn {
                        display: flex;
                        align-items: center;

                        .viewBtn {
                            width: 104px;
                            height: 36px;
                            background: #2E73F3;
                            border-radius: 5px 5px 5px 5px;
                            font-weight: 500;
                            font-size: 12px;
                            color: #FFFFFF;
                            line-height: 36px;
                            text-align: center;
                            cursor: pointer;
                        }

                        .msgBtn {
                            width: 104px;
                            height: 36px;
                            background: #FFFFFF;
                            border-radius: 5px;
                            border: 1px solid #2E73F3;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            margin-left: 15px;
                            cursor: pointer;

                            .msgImg {
                                width: 19px;
                                height: 19px;
                                margin-right: 5px;
                            }

                            .msgText {
                                font-weight: 500;
                                font-size: 12px;
                                color: #2E73F3;
                            }
                        }

                        .telBtn {
                            width: 104px;
                            height: 36px;
                            background: #FFFFFF;
                            border-radius: 5px;
                            border: 1px solid #2E73F3;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            margin-left: 15px;
                            cursor: pointer;

                            .telImg {
                                width: 19px;
                                height: 19px;
                                margin-right: 5px;
                            }

                            .telText {
                                font-weight: 500;
                                font-size: 12px;
                                color: #2E73F3;
                            }
                        }
                    }
                }
            }

            .info_replyBtn {
                min-height: 50px;

                .info_replyBtn_top {
                    position: absolute;
                    right: 20px;
                    top: 20px;
                    width: 269px;
                    height: 50px;
                    background: #2E73F3;
                    border-radius: 5px;
                    font-weight: 500;
                    font-size: 16px;
                    color: #FFFFFF;
                    text-align: center;
                    line-height: 50px;
                    cursor: pointer;
                }

                .info_replyBtn_bottom {
                    position: absolute;
                    right: 20px;
                    bottom: 20px;
                    width: 269px;
                    height: 50px;
                    background: #2E73F3;
                    border-radius: 5px;
                    font-weight: 500;
                    font-size: 16px;
                    color: #FFFFFF;
                    text-align: center;
                    line-height: 50px;
                    cursor: pointer;
                }
            }
        }
    }

}

.replyBox {
    padding: 0 20px;

    .reply_info {
        display: flex;
        background: #F0F3F9;
        padding: 10px 20px;
        margin-bottom: 20px;
        padding-right: 10px;

        .reply_info_item {
            flex: 1;

            .item {
                margin-bottom: 20px;
                display: flex;

                .item_title {
                    font-weight: 400;
                    font-size: 12px;
                    color: #666666;
                    margin-right: 20px;
                    width: 48px;
                    flex-shrink: 0;
                }

                .item_text {
                    font-weight: 500;
                    font-size: 14px;
                    color: #333333;
                    padding-right: 10px;
                }
            }
        }
    }

    .custom-table {
        ::v-deep .el-form-item {
            margin: 0;

            .el-form-item__content {
                display: flex;
                align-items: center;
            }
        }
    }
}

.viewReply {
    padding: 0 20px;

    .viewReply_company {
        margin-bottom: 10px;

        .companyName {
            padding: 0 20px;
            height: 36px;
            background: #E9EDF7;
            border-radius: 5px 5px 5px 5px;
            border: 1px solid #CBD6E2;
            display: flex;
            align-items: center;
            cursor: pointer;

            span {
                font-weight: 400;
                font-size: 12px;
                color: #333333;
                margin-right: 15px;
            }

            i {
                color: #CCCCCC;
                font-size: 20px;

                &.el-icon-arrow-up {
                    color: #2E73F3;
                }
            }
        }

        .viewReplyBtn {
            height: 48px;
            background: #ECF3FF;
            border: 1px solid #2E73F3;
            display: flex;
            align-items: center;
            justify-content: space-between;

            .viewReplyBtn_left {
                padding-left: 15px;
                font-weight: 400;
                font-size: 12px;
                color: #999999;

                span {
                    font-weight: 500;
                    font-size: 16px;
                    color: #2E73F3;
                    margin: 0 10px;
                }
            }

            .viewReplyBtn_right {
                width: 196px;
                height: 48px;
                background: #2E73F3;
                border: 1px solid #2E73F3;
                font-weight: 500;
                font-size: 16px;
                color: #FFFFFF;
                text-align: center;
                line-height: 48px;
                cursor: pointer;
            }

            .viewReplyBtn_right1 {
                display: flex;
                align-items: center;
                padding-right: 15px;

                .msgBtn {
                    width: 104px;
                    height: 36px;
                    background: #FFFFFF;
                    border-radius: 5px;
                    border: 1px solid #2E73F3;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin-left: 15px;
                    cursor: pointer;

                    .msgImg {
                        width: 19px;
                        height: 19px;
                        margin-right: 5px;
                    }

                    .msgText {
                        font-weight: 500;
                        font-size: 12px;
                        color: #2E73F3;
                    }
                }

                .telBtn {
                    width: 104px;
                    height: 36px;
                    background: #FFFFFF;
                    border-radius: 5px;
                    border: 1px solid #2E73F3;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin-left: 15px;
                    cursor: pointer;

                    .telImg {
                        width: 19px;
                        height: 19px;
                        margin-right: 5px;
                    }

                    .telText {
                        font-weight: 500;
                        font-size: 12px;
                        color: #2E73F3;
                    }
                }
            }
        }

    }
}
</style>
