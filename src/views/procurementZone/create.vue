<template>
    <div class="container">
        <header-tpl :is-login="isLogin" />
        <div class="container-box">
            <div class="containerBox">
                <div class="nav_box">
                    <span @click="toJump()">首页</span>/
                    <span @click="toJump('procurementZone')">求购专区</span>/
                    <span class="blue">发布求购</span>
                </div>
                <div class="formBox">
                    <div class="form_tabbar">
                        <div class="tabbar_item" :class="tabActive === 1 ? 'tabActive' : ''" @click="tabActive = 1">
                            非平台产品求购</div>
                        <div class="tabbar_item" :class="tabActive === 2 ? 'tabActive' : ''" @click="tabActive = 2">
                            平台产品求购</div>
                    </div>
                    <div class="form_info">
                        <div class="form_info_title">
                            <span class="title_text">求购信息</span>
                            <div class="tips" v-if="tabActive === 1">
                                <img class="tips_img" src="@/assets/images/tips.png" alt="">
                                <span class="tips_text">注意：非平台产品不可直接选取添加产品，需要用户录入产品名称、产品图片，且不可生成采购合同</span>
                            </div>
                        </div>
                        <div class="form_concent" v-if="tabActive === 1">
                            <el-form ref="form" :model="form" :rules="rules" label-width="100px">
                                <el-row :gutter="20">
                                    <el-col :span="12">
                                        <el-form-item label="求购标题" prop="title">
                                            <el-input v-model="form.title" placeholder="请输入求购标题"></el-input>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="有效时间" prop="effectiveTime">
                                            <el-date-picker v-model="form.effectiveTime"
                                                value-format="yyyy-MM-dd HH:mm:ss" type="datetime" placeholder="请选择有效时间"
                                                style="width: 100%"></el-date-picker>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="24">
                                        <el-form-item label="求购详细" prop="detail">
                                            <el-input v-model="form.detail" type="textarea"
                                                :autosize="{ minRows: 4, maxRows: 6 }" placeholder="请输入求购详细"></el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <div class="form_itemBox">
                                    <div class="form_itemBox_title">求购产品</div>
                                    <div class="form_item" v-for="(item, index) in form.products" :key="index">
                                        <div class="flex">
                                            <el-form-item label="产品名称" :prop="`products[${index}].productName`"
                                                :rules="rules.productsList.productName">
                                                <el-input v-model="item.productName" placeholder="请输入产品名称"
                                                    style="width: 458px;"></el-input>
                                            </el-form-item>
                                            <el-form-item label="求购数量" :prop="`products[${index}].quantity`"
                                                :rules="rules.productsList.quantity">
                                                <el-input v-model="item.quantity" placeholder="请输入求购数量"
                                                    style="width: 458px;">
                                                    <el-select style="width: 90px;" v-model="item.unit" slot="append"
                                                        placeholder="请选择单位">
                                                        <el-option v-for="(unit, i) in unitOptions" :key="i"
                                                            :label="unit" :value="unit"></el-option>
                                                    </el-select>
                                                </el-input>
                                            </el-form-item>
                                        </div>
                                        <el-form-item label="产品图片" :prop="`products[${index}].img`"
                                            :rules="rules.productsList.img">
                                            <image-upload :fileSize="5" :fileType="['png', 'jpg', 'jpeg']"
                                                v-model="item.img" />
                                        </el-form-item>
                                        <div class="removeItem" v-if="index > 0" @click="delProduct(index)">
                                            <img class="removeImg" src="@/assets/images/remove.png" alt="">
                                        </div>
                                    </div>
                                    <div class="form_add" @click="addProduct">
                                        <!-- <img class="add_img" src="@/assets/images/add_blue.png" alt=""> -->
                                        <span class="form_add_text">添加求购产品</span>
                                    </div>
                                </div>

                            </el-form>
                        </div>
                        <div class="form_concent" v-if="tabActive === 2">
                            <el-form ref="form" :model="form" :rules="rules" label-width="100px">
                                <el-row :gutter="20">
                                    <el-col :span="12">
                                        <el-form-item label="求购标题" prop="title">
                                            <el-input v-model="form.title" placeholder="请输入求购标题"></el-input>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="有效时间" prop="effectiveTime">
                                            <el-date-picker v-model="form.effectiveTime"
                                                value-format="yyyy-MM-dd HH:mm:ss" type="datetime" placeholder="请选择有效时间"
                                                style="width: 100%"></el-date-picker>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="24">
                                        <el-form-item label="求购详细" prop="detail">
                                            <el-input v-model="form.detail" type="textarea"
                                                :autosize="{ minRows: 4, maxRows: 6 }" placeholder="请输入求购详细"></el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <div class="form_itemBox">
                                    <div class="form_itemBox_title" v-if="form.products.length">求购产品</div>
                                    <div class="form_table">
                                        <el-form-item label="" label-width="0" prop="products">
                                            <el-table :data="form.products" style="width: 100%; margin-bottom: 20px"
                                                class="custom-table custom-table-cell0" v-if="form.products.length">
                                                <el-table-column type="index" label="序号" width="50"></el-table-column>
                                                <el-table-column prop="productName" label="产品名称" align="center"
                                                    show-overflow-tooltip>
                                                    <template slot-scope="{ row }">
                                                        <span class="table-link" @click="handleView(row.id, row)">{{
                                                            row.productName
                                                        }}</span>
                                                    </template>
                                                </el-table-column>
                                                <el-table-column prop="specs" label="产品图片" align="center"
                                                    show-overflow-tooltip>
                                                    <template slot-scope="{ row }">
                                                        <div style="display: flex; justify-content: center">
                                                            <el-image :src="formatProductImg(row)" fit="cover"
                                                                @click="handleImgView(row)">
                                                                <div slot="error" class="image-slot">
                                                                    <i class="el-icon-picture-outline"></i>
                                                                </div>
                                                            </el-image>
                                                        </div>
                                                    </template>
                                                </el-table-column>
                                                <el-table-column prop="model" label="产品型号" align="center"
                                                    show-overflow-tooltip></el-table-column>
                                                <el-table-column prop="quantity" label="采购数量" align="center"
                                                    width="300">
                                                    <template slot-scope="scope">
                                                        <el-form-item label-width="0"
                                                            :prop="`products.${scope.$index}.quantity`"
                                                            :rules="rules.quantity">
                                                            <el-input v-model="scope.row.quantity" size="small"
                                                                placeholder="请输入采购数量">
                                                                <el-select style="width: 90px;" v-model="scope.row.unit"
                                                                    slot="append" placeholder="请选择单位">
                                                                    <el-option v-for="(unit, i) in unitOptions" :key="i"
                                                                        :label="unit" :value="unit"></el-option>
                                                                </el-select>
                                                            </el-input>
                                                        </el-form-item>
                                                    </template>
                                                </el-table-column>
                                                <el-table-column label="操作" align="center" width="120">
                                                    <template slot-scope="scope">
                                                        <el-button type="text" size="mini" icon="el-icon-delete"
                                                            @click="handleDelete(scope.row)">删除</el-button>
                                                    </template>
                                                </el-table-column>
                                            </el-table>
                                        </el-form-item>
                                    </div>
                                    <div class="flex">
                                        <div class="form_add" @click="handleAdd('common')">
                                            <span class="form_add_text">添加求购产品(公域)</span>
                                        </div>
                                        <div class="form_add" @click="handleAdd('private')">
                                            <span class="form_add_text">添加求购产品(私域)</span>
                                        </div>
                                    </div>
                                </div>

                            </el-form>
                        </div>
                        <div class="form_btn">
                            <div class="form_closed" @click="toJump('procurementZone')">取消</div>
                            <div class="form_submit" @click="submitForm">立即发布</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <footer-tpl />

        <!--添加产品--选择产品-->
        <check-product ref="checkProduct" @confirm="handleConfirm" />
        <!-- 产品详情 -->
        <product-dialog ref="productInfo"></product-dialog>
    </div>
</template>

<script>
import HeaderTpl from '@/views/public/components/head'
import FooterTpl from '@/views/public/components/foot'
import { getToken } from '@/utils/auth'
import { addBuy } from '@/api/buy'
import CheckProduct from './product'
import ProductDialog from '@/views/public/product/dialog'
import { getProduct } from '@/api/system/product'
import { getPrivateduct } from '@/api/system/privateduct'

export default {
    components: { FooterTpl, HeaderTpl, CheckProduct, ProductDialog },
    data() {
        return {
            isLogin: !!getToken(),
            loading: true,
            list: [],
            total: 0,
            queryParams: {
                pageNum: 1,
                pageSize: 10
            },
            tabActive: 1,
            form: {
                title: undefined,
                effectiveTime: undefined,
                detail: undefined,
                products: []
            },
            rules: {
                title: [{ required: true, message: '请输入求购标题', trigger: 'blur' }],
                effectiveTime: [{ required: true, message: '请选择有效时间', trigger: 'change' }],
                detail: [{ required: true, message: '请输入求购详细', trigger: 'blur' }],
                products: [{ required: true, message: '请添加产品', trigger: 'change' }],
                productsList: {
                    productName: [
                        { required: true, message: "请输入产品名称", trigger: ['change', 'blur'] }
                    ],
                    quantity: [
                        { required: true, message: "请输入求购数量", trigger: 'blur' }
                    ],
                    img: [
                        { required: true, message: "请选择产品图片", trigger: 'change' }
                    ],
                }
            },
            unitOptions: ['吨', '千克', '个', '件', '套', '米', '支', '根'], // 单位
        }
    },
    watch: {
        tabActive(newVal, oldVal) {
            if (newVal === 1) {
                this.form.products = [{
                    productName: undefined,
                    quantity: undefined,
                    img: undefined,
                    unit: '吨',
                    source: 'none'
                }]
            } else if (newVal === 2) {
                this.form.products = []
            }
        }
    },
    created() {
        if (!this.isLogin) {
            this.$router.replace('/login')
        }
        if (this.tabActive === 1) {
            this.form.products = [{
                productName: undefined,
                quantity: undefined,
                img: undefined,
                source: 'none',
                unit: '吨'
            }]
        }
    },
    methods: {
        // 页面跳转
        toJump(val) {
            if (val) {
                this.$router.go(-1)
            } else {
                this.$router.replace(`/`)
            }
        },
        // 添加产品
        addProduct() {
            this.form.products.push({
                productName: undefined,
                quantity: undefined,
                img: undefined,
                unit: '吨',
                source: 'none'
            })
        },
        // 添加产品
        handleAdd(source) {
            const arr = this.form.products.filter(item => item.source === source) || []
            this.$refs.checkProduct.openProduct(arr, source)
        },
        // 确认选择产品
        handleConfirm(arr = []) {
            const newArr = arr.map(item => {
                return { ...item, productId: item.id, quantity: undefined }
            })
            const oldArr = this.form.products.filter(item => item.source !== newArr[0].source) || []
            this.form.products = [...oldArr, ...newArr]
        },
        delProduct(index) {
            this.form.products.splice(index, 1)
        },
        // 产品详情
        handleView(Id, row) {
            if (row.source === 'common') {
                getProduct(Id).then(res => {
                    this.$refs.productInfo.handleView(res.data)
                })
            } else {
                getPrivateduct(Id).then(res => {
                    this.$refs.productInfo.handleView(res.data)
                })
            }
        },
        // 图片详情
        handleImgView(row) {
            this.$refs.productInfo.handleImgView(row)
        },
        // 提交form
        submitForm() {
            this.$refs.form.validate(valid => {
                if (valid) {
                    if (this.tabActive === 1) {
                        this.form.type = 'm0'
                    } else if (this.tabActive === 2) {
                        this.form.type = 'm1'
                    }
                    addBuy(this.form).then(res => {
                        const { code, msg } = res
                        if (code === 200) {
                            this.$message.success('操作成功')
                            this.toJump('procurementZone')
                        } else this.$message.error(msg)
                    })
                }
            })
        },
    }
}
</script>

<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';

.container-box {
    min-width: 1200px;
}

.containerBox {
    width: 1200px;
    min-height: 500px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    padding-bottom: 30px;

    .nav_box {
        padding-top: 27px;
        padding-bottom: 10px;
        font-weight: 400;
        font-size: 16px;
        color: #999999;
        line-height: 19px;
        cursor: pointer;

        .blue {
            color: #2E73F3;
        }
    }

    .formBox {
        .form_tabbar {
            display: flex;
            align-items: flex-end;

            .tabbar_item {
                width: 170px;
                height: 45px;
                background: #F1F1F3;
                font-weight: 400;
                font-size: 14px;
                color: #666666;
                text-align: center;
                line-height: 45px;
                cursor: pointer;

                &.tabActive {
                    height: 50px;
                    background: #fff;
                    color: #333333;
                    line-height: 50px;
                    font-weight: 500;
                    border-top-left-radius: 5px;
                    border-top-right-radius: 5px;
                }
            }
        }

        .form_info {
            background: #fff;
            padding: 20px;

            .form_info_title {
                display: flex;
                align-items: center;
                padding-bottom: 6px;
                border-bottom: 1px solid #E2E6F3;

                .title_text {
                    font-weight: 500;
                    font-size: 14px;
                    color: #666666;
                    line-height: 20px;
                    margin-right: 30px;
                }

                .tips {
                    display: flex;
                    align-items: center;

                    .tips_img {
                        width: 20px;
                        height: 20px;
                        margin-right: 5px;
                    }

                    .tips_text {
                        font-weight: 500;
                        font-size: 12px;
                        color: #F35D09;
                        line-height: 20px;
                    }
                }
            }

            .form_concent {
                padding-top: 20px;
                border-bottom: 1px solid #E2E6F3;

                .form_itemBox {
                    .form_itemBox_title {
                        font-weight: 500;
                        font-size: 14px;
                        color: #666666;
                        line-height: 20px;
                        margin-bottom: 5px;
                    }

                    .form_item {
                        border-radius: 5px;
                        border: 1px solid #CBD6E2;
                        padding: 20px 0;
                        padding-right: 15px;
                        position: relative;
                        margin-bottom: 15px;

                        .flex {
                            display: flex;
                            align-items: center;
                            justify-content: space-between;
                        }

                        .removeItem {
                            position: absolute;
                            right: -8px;
                            top: -8px;
                            cursor: pointer;

                            .removeImg {
                                width: 16px;
                                height: 16px;
                            }
                        }
                    }

                    .form_add {
                        width: 160px;
                        height: 32px;
                        background: #F7FAFF;
                        border-radius: 5px;
                        border: 1px dashed #2E73F3;
                        font-weight: 500;
                        font-size: 12px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        margin-bottom: 20px;
                        cursor: pointer;
                        background-image: url('../../assets/images/add_blue.png');
                        background-repeat: no-repeat;
                        background-size: 20px;
                        background-position: 12px 6px;
                        margin-right: 10px;

                        .form_add_text {
                            margin-left: 22px;
                            color: #2E73F3;
                        }

                        &:hover {
                            background: #2E73F3;
                            background-image: url('../../assets/images/add_icon.png');
                            background-repeat: no-repeat;
                            background-size: 20px;
                            background-position: 12px 6px;

                            .form_add_text {
                                margin-left: 22px;
                                color: #fff;
                            }
                        }
                    }
                }
            }

            .form_btn {
                padding-top: 30px;
                display: flex;
                align-items: center;
                justify-content: flex-end;

                .form_closed {
                    width: 269px;
                    height: 50px;
                    border-radius: 5px;
                    border: 1px solid #CBD6E2;
                    text-align: center;
                    font-weight: 400;
                    font-size: 16px;
                    color: #999999;
                    line-height: 50px;
                    cursor: pointer;
                }

                .form_submit {
                    width: 269px;
                    height: 50px;
                    border-radius: 5px;
                    background: #2E73F3;
                    text-align: center;
                    font-weight: 400;
                    font-size: 16px;
                    color: #FFFFFF;
                    line-height: 50px;
                    cursor: pointer;
                    margin-left: 10px;
                }
            }
        }
    }
}
</style>
