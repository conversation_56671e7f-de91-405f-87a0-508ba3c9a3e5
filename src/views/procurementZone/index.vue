<template>
    <div class="container">
        <header-tpl :is-login="isLogin" />
        <div class="container-box">
            <div class="containerBox">
                <div class="procurementZone_top">
                    <div class="procurementZone_top_nav">
                        <div class="procurementZone_tabbar">
                            <span class="tabbar_item" :class="tabActive === 1 ? 'tabActive' : ''"
                                @click="handleTab(1)">求购专区</span>
                            <span class="tabbar_item" :class="tabActive === 2 ? 'tabActive' : ''"
                                @click="handleTab(2)">我发布的</span>
                        </div>
                        <div class="procurementZone_navItem">
                            <span class="navItem" :class="navActive === 0 ? 'navActive' : ''"
                                @click="handleNav(0)">求购中</span>
                            <!-- <span class="navItem" :class="navActive === -1 ? 'navActive' : ''" @click="handleExpire"
                                v-if="tabActive === 2">未失效</span> -->
                            <span class="navItem" :class="navActive === 1 ? 'navActive' : ''"
                                @click="handleNav(1)">已完成</span>
                            <span class="navItem" :class="navActive === -10 ? 'navActive' : ''" @click="handleNav(-10)"
                                v-if="tabActive === 2">已关闭</span>
                        </div>
                    </div>
                    <div class="procurementZone_top_add" @click="handleAdd">
                        <img class="img" src="@/assets/images/qgzq_add.png" alt="" srcset="">
                        <span class="text">我要发布</span>
                    </div>
                </div>
                <div class="procurementZone_concent">
                    <div class="procurementZone_concent_list" v-if="total">
                        <div class="list_item" v-for="item in list" :key="item.id">
                            <!-- <div class="list_item_info" @click="!(new Date().getTime() >= new Date(item.effectiveTime).getTime()) && item.status != -10 ? goDetail(item) : ''"> -->
                            <div class="list_item_info" @click="goDetail(item)">
                                <div class="info_concent">
                                    <img class="img_tx" :src="item.avatar_oss || imgPath + item.avatar" alt="">
                                    <!-- <img class="img_tx" src="@/assets/images/qgzqtx.png" alt=""> -->
                                    <div class="text_box">
                                        <div class="text_title">{{ item.title }}</div>
                                        <div class="text_info">
                                            <div class="product_item" v-for="(it, i) in item.products" :key="i">
                                                <div class="item">
                                                    <span class="f12">产品名称</span>
                                                    <span class="f14">{{ it.productName }}</span>
                                                </div>
                                                <div class="item">
                                                    <span class="f12">求购数量</span>
                                                    <span class="f14">{{ it.quantity + it.unit }}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="info_tips">
                                <div class="tips_reply" v-if="item.reply > 0">
                                    <span class="tips_reply_num">{{ item.reply }}</span>
                                    <span class="tips_reply_text">个报价 ></span>
                                </div>
                                <div class="text_time">{{ item.createTime }}</div>
                            </div>
                            <div style="display: flex; flex-direction: column; align-items: end;">
                                <div>
                                    <div class="tips_closed" v-if="item.status == -10">已关闭</div>
                                    <div class="tips_closed" v-else-if="item.status == 1">已完成</div>
                                    <div class="tips_closed"
                                        v-else-if="new Date().getTime() >= new Date(item.effectiveTime).getTime() && item.status == 0">
                                        已失效</div>
                                    <div class="tips_countdown" v-else>{{ expireTimeFormat(item) }}</div>
                                </div>
                                <div>
                                    <div class="list_item_btn" :class="item.isReply ? 'nobg' : ''"
                                        v-if="tabActive === 1"
                                        @click="!item.isReply ? replyQuotation(item) : goDetail(item)">

                                        <span class="btn_text">{{ item.isReply ? '已回复' : '立即去报价' }}</span>
                                    </div>
                                    <!-- <div class="list_item_btn nobg" v-if="tabActive === 2" @click="!(new Date().getTime() >= new Date(item.effectiveTime).getTime()) ? goDetail(item) : ''">                                -->
                                    <div class="list_item_btn nobg" v-if="tabActive === 2" @click="goDetail(item)">
                                        <span class="btn_text">查看报价</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <el-empty v-else />
                    <div class="custom-pagination">
                        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
                            :limit.sync="queryParams.pageSize" @pagination="getList" />
                    </div>
                </div>
            </div>
        </div>
        <footer-tpl />

        <!-- 回复报价 -->
        <el-dialog v-dialogDragBox title="回复报价" :visible.sync="replyOpen" width="1150px" class="custom-dialog">
            <div class="replyBox">
                <div class="reply_info">
                    <div class="reply_info_item">
                        <div class="item">
                            <span class="item_title">求购标题</span>
                            <span class="item_text">{{ info.title }}</span>
                        </div>
                        <div class="item">
                            <span class="item_title">求购详细</span>
                            <span class="item_text">{{ info.detail }}</span>
                        </div>
                    </div>
                    <div class="reply_info_item">
                        <div class="item">
                            <span class="item_title">求购方</span>
                            <span class="item_text">{{ info.companyName }}</span>
                        </div>
                        <div class="item">
                            <span class="item_title">发布时间</span>
                            <span class="item_text">{{ info.createTime }}</span>
                        </div>
                        <div class="item">
                            <span class="item_title">有效时间</span>
                            <span class="item_text" style="color: #F35D09;">{{ new Date().getTime() >= new
                                Date(info.effectiveTime).getTime() ? '已失效' :
                                expireTimeFormat(info) }}</span>
                        </div>
                    </div>
                </div>
                <el-form ref="form" :model="info" :rules="rules" label-width="100px">
                    <el-table ref="addTable" stripe :data="info.products" style="width: 100%"
                        class="custom-table custom-table-cell5">
                        <el-table-column align="center" type="index" label="序号"></el-table-column>
                        <el-table-column align="center" prop="productName" label="产品名称" show-overflow-tooltip>
                            <template slot-scope="{ row }">
                                <span :class="info.type !== 'm0' ? 'table-link' : ''"
                                    @click="info.type !== 'm0' ? handleView(row.productId, row) : ''">
                                    {{ row.productName }}
                                </span>
                            </template>
                        </el-table-column>
                        <el-table-column align="center" prop="picture1" label="图片" width="75">
                            <template slot-scope="{ row }">
                                <div style="display: flex; justify-content: center">
                                    <el-image :src="formatProductImg(row)" fit="cover" @click="handleImgView(row)">
                                        <div slot="error" class="image-slot">
                                            <i class="el-icon-picture-outline"></i>
                                        </div>
                                    </el-image>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column align="center" prop="quantity" label="采购数量">
                            <template slot-scope="{ row }">
                                {{ row.quantity + row.unit }}
                            </template>
                        </el-table-column>
                        <el-table-column align="center" prop="model" label="报价" width="300">
                            <template slot-scope="scope">
                                <el-form-item label-width="0" :prop="`products[${scope.$index}].amount`"
                                    :rules="rules.amount">
                                    <el-input v-model="scope.row.amount" size="small" placeholder="请输入报价">
                                        <template slot="append">
                                            <div class="flex" style="align-items: center;">
                                                <div style="margin-right: 5px; height: 30px; line-height: 30px;">元 /
                                                </div>
                                                <el-select style="width: 90px;" v-model="scope.row.replyUnit"
                                                    placeholder="请选择报价单位">
                                                    <el-option v-for="(replyUnit, i) in unitOptions" :key="i"
                                                        :label="replyUnit" :value="replyUnit"></el-option>
                                                </el-select>
                                            </div>
                                        </template>
                                    </el-input>
                                </el-form-item>
                            </template>
                        </el-table-column>
                        <el-table-column align="center" prop="productCode" label="备注" width="300">
                            <template slot-scope="scope">
                                <el-form-item label-width="0" :prop="`products[${scope.$index}].remark`"
                                    :rules="rules.remark">
                                    <el-input v-model="scope.row.remark" size="small" placeholder="请输入备注">
                                    </el-input>
                                </el-form-item>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-form>
            </div>
            <div slot="footer">
                <div class="inline-flex">
                    <el-button type="button" class="custom-dialog-btn" @click="replyOpen = false">取消</el-button>
                    <el-button type="button" class="custom-dialog-btn primary" @click="handleSubmit">确定</el-button>
                </div>
            </div>
        </el-dialog>

        <!-- 产品详情 -->
        <product-dialog ref="productInfo"></product-dialog>
        <!-- 检查是否企业用户 -->
         <check-company ref="checkCompany"></check-company>
    </div>
</template>

<script>
import HeaderTpl from '@/views/public/components/head'
import FooterTpl from '@/views/public/components/foot'
import { getToken } from '@/utils/auth'
import { getBuyList, getBuyReplyList, detailBuy, replyBuy, indexReplyBuy } from '@/api/buy'
import { homePurchaseList } from '@/api/shouye'
import { parseTime } from '@/utils/ruoyi'
import { getProduct } from '@/api/system/product'
import { getPrivateduct } from '@/api/system/privateduct'
import ProductDialog from '@/views/public/product/dialog'
import CheckCompany from '@/components/CheckCompany'

export default {
    name: 'ProcurementZone',
    components: { FooterTpl, HeaderTpl, ProductDialog, CheckCompany },
    data() {
        return {
            isLogin: !!getToken(),
            loading: true,
            list: [],
            total: 0,
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                title: undefined,
                status: 0,
                filterExpire: true
            },
            tabActive: 1,
            navActive: 0,
            replyOpen: false,
            info: {},
            unitOptions: ['吨', '千克', '个', '件', '套', '米', '支', '根'], // 单位
            rules: {
                remark: [
                    { required: true, message: "请输入备注", trigger: 'blur' }
                ],
                amount: [
                    { required: true, message: "请输入回复报价", trigger: 'blur' }
                ],
            },
        }
    },
    created() {
        if (this.tabActive === 1) {
            this.queryParams.status = 0
        }
        this.getList()
    },    
    computed: {
        companyId() {
            return this.$store.state.user.companyId || -1
        }
    },
    methods: {
        parseTime,
        // 列表
        getList() {
            this.loading = true
            if (this.tabActive === 1) {
                homePurchaseList(this.queryParams).then(async res => {
                    const { code, msg, rows, total } = res
                    if (code === 200) {
                        this.loading = false
                        await Promise.all(
                            rows.map(async item => {
                                item.countdown = undefined
                                const { data } = await getBuyReplyList({ leadsId: item.id })
                                item.reply = Object.keys(data).length
                                const obj = await indexReplyBuy({ leadsId: item.id })
                                item.isReply = !!obj.data.length
                                const products = await detailBuy({ leadsId: item.id })
                                if (products.data.products.length > 3) {
                                    item.products = products.data.products.filter((item, index) => index < 3)
                                } else {
                                    item.products = products.data.products
                                }

                            })
                        )
                        this.list = rows
                        this.total = total
                    } else this.$message.error(msg)
                })
            } else if (this.tabActive === 2) {
                getBuyList(this.queryParams).then(async res => {
                    const { code, msg, rows, total } = res
                    if (code === 200) {
                        this.loading = false
                        await Promise.all(
                            rows.map(async item => {
                                item.countdown = undefined
                                const { data } = await getBuyReplyList({ leadsId: item.id })
                                item.reply = Object.keys(data).length
                            })
                        )
                        this.list = rows
                        this.total = total
                    } else this.$message.error(msg)
                })
            }

        },
        // 切换tab
        handleTab(type) {
            this.tabActive = type
            this.navActive = 0
            this.queryParams.status = 0
            this.getList()
        },
        // 切换nav
        handleNav(nav) {
            this.navActive = nav
            this.queryParams.status = nav
            this.getList()
        },
        // 过滤失效
        // handleExpire() {
        //     this.navActive = -1
        //     this.queryParams.filterExpire = true
        //     this.getList()
        // },
        // 新增
        handleAdd() {
            this.$router.push({ name: 'ProcurementCreate' })
        },
        // 查看详情
        goDetail(row) {
            const { id } = row
            this.$router.push({
                path: `/procurementDetail`,
                query: {
                    leadsId: id,
                    type: this.tabActive
                }
            })
        },
        // 我要报价
        replyQuotation(item) {
            if(this.companyId<0){
                this.$refs.checkCompany.showDialog()
                return
            }
            detailBuy({
                leadsId: item.id
            }).then(res => {
                const { code, msg, data } = res
                if (code === 200) {
                    data.countdown = undefined
                    if(data.products && data.products.length>0){
                        data.products.forEach(el => {
                            el.replyUnit = el.unit
                            el.remark = ''
                        })
                    }
                    this.info = data
                    this.replyOpen = true
                } else this.$message.error(msg)
            })
        },
        // 倒计时
        expireTimeFormat(item) {
            function fn() {
                const dateEnd = new Date(item.effectiveTime)
                const dateBegin = new Date()
                const dateDiff = dateEnd.getTime() - dateBegin.getTime()
                if (dateDiff > 0) {
                    if (dateDiff <= 0) {
                        clearInterval(timer) // 停止定时器
                        return
                    }
                    // 将剩余时间转换为天、小时、分钟和秒
                    var days = Math.floor(dateDiff / (1000 * 60 * 60 * 24))
                    var hours = Math.floor((dateDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
                    var minutes = Math.floor((dateDiff % (1000 * 60 * 60)) / (1000 * 60))
                    var seconds = Math.floor((dateDiff % (1000 * 60)) / 1000)
                    if (days === 0 && hours === 0 && minutes === 0) {
                        item.countdown = seconds + '秒后失效'
                    } else if (days === 0 && hours === 0) {
                        item.countdown = minutes + '分钟后失效'
                    } else if (days === 0) {
                        item.countdown = hours + '小时' + minutes + '分钟后失效'
                    } else {
                        item.countdown = days + '天' + hours + '小时' + minutes + '分钟后失效'
                    }
                    return item.countdown
                }
            }
            // 第一次调用
            fn()
            // 每秒执行一次
            var timer = setInterval(fn, 1000)
            return item.countdown
        },
        // 图片处理
        productImg(img) {
            if (JSON.stringify(img) === '{}') {
                return ''
            }
            if (img.picture_oss) {
                if (img.picture_oss.includes(',')) {
                    return img.picture_oss.split(',')
                }
                return [img.picture_oss]
            }
            if (img.picture1_oss) {
                if (img.picture1_oss.includes(',')) {
                    return img.picture1_oss.split(',')
                }
                return [img.picture1_oss]
            }
            if (img.diagram_oss) {
                if (img.diagram_oss.includes(',')) {
                    return img.diagram_oss.split(',')
                }
                return [img.diagram_oss]
            }
            if (img.picture) {
                if (img.picture.includes(',')) {
                    let arr = img.picture.split(',').forEach(el => {
                        el = this.imgPath + el
                    });
                    return arr
                }
                return [this.imgPath + img.picture]
            }
            if (img.picture1) {
                if (img.picture1.includes(',')) {
                    let arr = img.picture1.split(',').forEach(el => {
                        el = this.imgPath + el
                    });
                    return arr
                }
                return [this.imgPath + img.picture1]
            }
            if (img.diagram) {
                if (img.diagram.includes(',')) {
                    let arr = img.diagram.split(',').forEach(el => {
                        el = this.imgPath + el
                    });
                    return arr
                }
                return [this.imgPath + img.diagram]
            }
            if (img.img_oss) {
                if (img.img_oss.includes(',')) {
                    return img.img_oss.split(',')
                }
                return [img.img_oss]
            }
            if (img.img) {
                if (img.img.includes(',')) {
                    let arr = img.img.split(',').forEach(el => {
                        el = this.imgPath + el
                    });
                    return arr
                }
                return [this.imgPath + img.img]
            }
            return ''
        },
        // 产品详情
        handleView(Id, row) {
            if (row.source === 'common') {
                getProduct(Id).then(res => {
                    this.$refs.productInfo.handleView(res.data)
                })
            } else {
                getPrivateduct(Id).then(res => {
                    this.$refs.productInfo.handleView(res.data)
                })
            }
        },
        // 图片详情
        handleImgView(row) {
            this.$refs.productInfo.handleImgView(row)
        },
        // 回复报价
        handleSubmit() {
            this.$refs.form.validate(valid => {
                if (valid) {
                    let data = {
                        leadsId: this.info.id,
                        reply: []
                    }
                    this.info.products.forEach(el => {
                        let obj = {
                            amount: el.amount,
                            productId: el.productId,
                            productName: el.productName,
                            remark: el.remark,
                            replyUnit: el.replyUnit,
                            source: el.source,
                        }
                        data.reply.push(obj)
                    })
                    replyBuy(data).then(res => {
                        const { code, msg } = res
                        if (code === 200) {
                            this.$message.success('回复成功')
                            this.getList()
                            this.replyOpen = false
                        } else this.$message.error(msg)
                    })
                }
            })
        },
    }
}
</script>

<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';

.container-box {
    min-width: 1200px;
}

.containerBox {
    width: 1200px;
    min-height: 500px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    padding-bottom: 30px;

    .procurementZone_top {
        padding-top: 16px;
        display: flex;

        .procurementZone_top_nav {
            flex: 1;

            .procurementZone_tabbar {
                display: flex;
                align-items: center;
                border-bottom: 1px solid #e3e3e3;

                .tabbar_item {
                    font-weight: 400;
                    font-size: 14px;
                    color: #666666;
                    line-height: 20px;
                    width: 56px;
                    height: 20px;
                    margin-right: 56px;
                    margin-bottom: 14px;
                    cursor: pointer;

                    &.tabActive {
                        font-weight: 500;
                        font-size: 18px;
                        color: #333333;
                        line-height: 26px;
                        width: 80px;
                        height: 26px;
                        text-align: center;
                        margin-bottom: 11px;

                        &::after {
                            content: '';
                            width: 80px;
                            height: 2px;
                            background: #292F48;
                            display: block;
                            margin: 0 auto;
                            margin-top: 10px;
                        }
                    }
                }
            }

            .procurementZone_navItem {
                display: flex;
                align-items: center;
                padding: 20px 0;

                .navItem {
                    font-weight: 400;
                    font-size: 14px;
                    color: #999999;
                    line-height: 20px;
                    margin-right: 45px;
                    cursor: pointer;

                    &.navActive {
                        font-weight: 500;
                        font-size: 16px;
                        color: #2E73F3;
                        line-height: 32px;
                        width: 97px;
                        height: 32px;
                        text-align: center;
                        background: #EBF2FF;
                        border-radius: 5px;
                        border: 1px solid #2E73F3;
                    }
                }
            }
        }

        .procurementZone_top_add {
            margin-left: 20px;
            width: 60px;
            height: 60px;
            background: #2E73F3;
            box-shadow: 0px 0px 13px 0px rgba(0, 0, 0, 0.25);
            border-radius: 5px 5px 5px 5px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            cursor: pointer;

            .img {
                width: 20px;
                height: 20px;
            }

            .text {
                font-weight: 400;
                font-size: 12px;
                color: #FFFFFF;
                margin-top: 5px;
            }
        }
    }

    .procurementZone_concent {
        .procurementZone_concent_list {
            .list_item {
                margin-bottom: 15px;
                padding: 25px 30px;
                display: flex;
                align-items: center;
                justify-content: space-between;
                // height: 152px;
                background: #FFFFFF;
                border-radius: 10px;
                border: 1px solid #E0E0E0;

                .list_item_info {
                    cursor: pointer;

                    .info_concent {
                        width: 452px;
                        // margin-right: 74px;
                        display: flex;
                        align-items: center;

                        .img_tx {
                            width: 52px;
                            height: 52px;
                            margin-right: 25px;
                        }

                        .text_box {
                            .text_title {
                                width: 374px;
                                font-weight: 500;
                                font-size: 20px;
                                color: #333333;
                                line-height: 20px;
                                overflow: hidden;
                                text-overflow: ellipsis;
                                white-space: nowrap;
                                margin-bottom: 7px;
                            }

                            .text_info {
                                .product_item {
                                    width: 374px;
                                    display: flex;
                                    align-items: center;

                                    .item {
                                        width: 127px;
                                        margin-right: 30px;
                                        display: inline-flex;
                                        align-items: center;

                                        .f12 {
                                            width: 50px;
                                            font-weight: 400;
                                            font-size: 12px;
                                            color: #666666;
                                            line-height: 20px;
                                            margin-right: 10px;
                                            flex-shrink: 0;
                                        }

                                        .f14 {
                                            width: 67px;
                                            font-weight: 500;
                                            font-size: 14px;
                                            color: #333333;
                                            line-height: 20px;
                                            overflow: hidden;
                                            text-overflow: ellipsis;
                                            white-space: nowrap;
                                        }
                                    }
                                }
                            }
                        }
                    }


                }

                .info_tips {
                    .tips_reply {
                        width: 90px;
                        height: 28px;
                        background: rgba(244, 63, 63, 0.26);
                        border-radius: 50px;
                        display: flex;
                        align-items: center;
                        margin-bottom: 45px;

                        .tips_reply_num {
                            width: 28px;
                            height: 28px;
                            background: #F43F3F;
                            border-radius: 20px;
                            font-weight: 500;
                            font-size: 14px;
                            color: #FFFFFF;
                            line-height: 28px;
                            text-align: center;
                            margin-right: 5px;
                        }

                        .tips_reply_text {
                            font-weight: 500;
                            font-size: 12px;
                            color: #F43F3F;
                            line-height: 28px;
                        }
                    }

                    .text_time {
                        font-weight: 400;
                        font-size: 12px;
                        color: #999999;
                        line-height: 14px;
                    }
                }

                .tips_closed {
                    width: 80px;
                    height: 32px;
                    // background: linear-gradient(171deg, #FB4C4C 0%, #F82828 100%);
                    background: #F82828;
                    margin-bottom: 10px;
                    font-weight: 500;
                    font-size: 14px;
                    color: #FFFFFF;
                    line-height: 32px;
                    padding-left: 16px;
                    position: relative;

                    &::before {
                        content: '';
                        position: absolute;
                        top: 0;
                        left: -32px;
                        width: 0;
                        height: 0;
                        border-left: 16px solid transparent;
                        border-right: 16px solid #F82828;
                        border-top: 16px solid transparent;
                        border-bottom: 16px solid transparent;
                    }
                }

                .tips_countdown {
                    padding: 8px 15px;
                    margin-bottom: 10px;
                    background: #FFE7E6;
                    border-radius: 50px;
                    font-weight: 400;
                    font-size: 14px;
                    color: #FC372A;
                    line-height: 20px;
                    position: relative;
                    left: -16px;
                }

                .list_item_btn {
                    min-width: 151px;
                    height: 36px;
                    border-radius: 30px;
                    border: 1px solid #2E73F3;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    cursor: pointer;
                    background-image: url('../../assets/images/qgbj_b.png');
                    background-repeat: no-repeat;
                    background-size: 20px;
                    background-position: 20px 10px;
                    position: relative;
                    left: -16px;

                    &.nobg {
                        background-image: none;

                        .btn_text {
                            margin-left: 0;
                        }
                    }

                    // .btn_img {
                    //     width: 20px;
                    //     height: 20px;
                    //     margin-right: 10px;
                    // }

                    .btn_text {
                        margin-left: 40px;
                        font-weight: 500;
                        font-size: 16px;
                        color: #2E73F3;
                    }
                }

                &:hover {
                    box-shadow: 0px 2px 19px 0px rgba(0, 0, 0, 0.1);
                    border: 1px solid #2E73F3;

                    .list_item_btn {
                        background: #2E73F3;
                        border: none;
                        background-image: url('../../assets/images/qgbj_w.png');
                        background-repeat: no-repeat;
                        background-size: 20px;
                        background-position: 20px 10px;

                        &.nobg {
                            background-image: none
                        }

                        .btn_text {
                            color: #fff;
                        }

                    }
                }
            }
        }
    }
}

.replyBox {
    padding: 0 20px;

    .reply_info {
        display: flex;
        background: #F0F3F9;
        padding: 10px 20px;
        margin-bottom: 20px;
        padding-right: 10px;

        .reply_info_item {
            flex: 1;

            .item {
                margin-bottom: 20px;
                display: flex;

                .item_title {
                    font-weight: 400;
                    font-size: 12px;
                    color: #666666;
                    margin-right: 20px;
                    width: 48px;
                    flex-shrink: 0;
                }

                .item_text {
                    font-weight: 500;
                    font-size: 14px;
                    color: #333333;
                    padding-right: 10px;
                }
            }
        }
    }

    .custom-table {
        ::v-deep .el-form-item {
            margin: 0;

            .el-form-item__content {
                display: flex;
                align-items: center;
            }
        }
    }
}
</style>
