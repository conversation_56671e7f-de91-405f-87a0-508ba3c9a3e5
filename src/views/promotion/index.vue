<template>
  <div class="bgcf9 vh-85">
    <div class="custom-search flex" style="padding-top: 18px" v-show="tabActive === 1">
      <el-form :model="queryParams" ref="query" size="small" label-width="5em" :inline="true" @submit.native.prevent>
        <el-row :gutter="10">
          <el-col :xs="8" :sm="6" :lg="4">
            <el-form-item label="产品名称" prop="productName">
              <el-input v-model="queryParams.productName" placeholder="请输入产品名称" clearable @keyup.enter.native="handleQuery" />
            </el-form-item>
          </el-col>
          <el-col :xs="8" :sm="6" :lg="4">
            <el-form-item label="产品编码" prop="productCode">
              <el-input v-model="queryParams.productCode" placeholder="请输入产品编码" clearable @keyup.enter.native="handleQuery" />
            </el-form-item>
          </el-col>
          <el-col :xs="8" :sm="6" :lg="4">
            <el-form-item label="产品规格" prop="specs">
              <el-input v-model="queryParams.specs" placeholder="请输入产品规格" clearable @keyup.enter.native="handleQuery" />
            </el-form-item>
          </el-col>
          <el-col :xs="8" :sm="6" :lg="4">
            <el-form-item label="产品型号" prop="model">
              <el-input v-model="queryParams.model" placeholder="请输入产品型号" clearable @keyup.enter.native="handleQuery" />
            </el-form-item>
          </el-col>
          <el-col :xs="8" :sm="6" :lg="4">
            <el-form-item label="产品材质" prop="materialQuality">
              <el-input v-model="queryParams.materialQuality" placeholder="请输入产品材质" clearable @keyup.enter.native="handleQuery" />
            </el-form-item>
          </el-col>
          <el-col :xs="8" :sm="6" :lg="4" v-if="showAll">
            <el-form-item label="表面处理" prop="surface">
              <el-input v-model="queryParams.surface" placeholder="请输入表面处理" clearable @keyup.enter.native="handleQuery" />
            </el-form-item>
          </el-col>
          <el-col :span="1.5">
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
            </el-form-item>
            <el-form-item>
              <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
            </el-form-item>
            <el-form-item>
              <el-button type="text" size="mini" @click="clickSearch">
                {{ word }}
                <i :class="showAll ? 'el-icon-arrow-up ' : 'el-icon-arrow-down'"></i>
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="custom-search flex" style="padding-top: 18px" v-show="tabActive === 0">
      <el-form :model="quoteParams" ref="quoteParams" size="small" label-width="5em" :inline="true" @submit.native.prevent>
        <el-row :gutter="10">
          <el-col :xs="8" :sm="6" :lg="4">
            <el-form-item label="报价名称" prop="name">
              <el-input v-model="quoteParams.name" placeholder="请输入报价名称" clearable @keyup.enter.native="handleQuoteQuery" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :xs="8" :sm="6" :lg="4">
            <el-form-item label="报单人" prop="createBy">
              <el-input v-model="quoteParams.createBy" placeholder="请输入报单人" clearable @keyup.enter.native="handleQuoteQuery" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :xs="8" :sm="6" :lg="4">
            <el-form-item label="报价编号" prop="serial">
              <el-input v-model="quoteParams.serial" placeholder="请输入报价编号" clearable @keyup.enter.native="handleQuoteQuery" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :xs="8" :sm="6" :lg="4">
            <el-form-item label="报价产品" prop="productName">
              <el-input v-model="quoteParams.productName" placeholder="请输入报价产品" clearable @keyup.enter.native="handleQuoteQuery" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :xs="8" :sm="6" :lg="4">
            <el-form-item label="产品编码" prop="productCode">
              <el-input v-model="quoteParams.productCode" placeholder="请输入产品编码" clearable @keyup.enter.native="handleQuoteQuery" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :xs="8" :sm="6" :lg="4" v-if="showAll">
            <el-form-item label="产品规格" prop="specs">
              <el-input v-model="quoteParams.specs" placeholder="请输入产品规格" clearable @keyup.enter.native="handleQuoteQuery" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :xs="8" :sm="6" :lg="4" v-if="showAll">
            <el-form-item label="产品型号" prop="model">
              <el-input v-model="quoteParams.model" placeholder="请输入产品型号" clearable @keyup.enter.native="handleQuoteQuery" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :xs="8" :sm="6" :lg="4" v-if="showAll">
            <el-form-item label="产品材质" prop="materialQuality">
              <el-input v-model="quoteParams.materialQuality" placeholder="请输入产品材质" clearable @keyup.enter.native="handleQuoteQuery" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :xs="8" :sm="6" :lg="4" v-if="showAll">
            <el-form-item label="表面处理" prop="surface">
              <el-input v-model="quoteParams.surface" placeholder="请输入表面处理" clearable @keyup.enter.native="handleQuoteQuery" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="1.5">
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuoteQuery">搜索</el-button>
            </el-form-item>
            <el-form-item>
              <el-button icon="el-icon-refresh" size="small" @click="resetQuoteQuery">重置</el-button>
            </el-form-item>
            <el-form-item>
              <el-button type="text" size="mini" @click="clickSearch">
                {{ word }}
                <i :class="showAll ? 'el-icon-arrow-up ' : 'el-icon-arrow-down'"></i>
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <div class="classify flex">
      <div class="classify-item" :class="{ active: item.value === tabActive }" v-for="item in tabOptions" :key="item.value" @click="handleTabChange(item)">
        {{ item.label }}
      </div>
    </div>

    <div class="tableBox">
      <el-table v-loading="loading" ref="table" stripe :data="list" :key="key" class="custom-table">
        <el-table-column align="center" type="index" label="序号"></el-table-column>
        <template v-if="tabActive === 1">
          <el-table-column label="产品名称" prop="productName" align="center" show-overflow-tooltip>
            <template slot-scope="{ row }">
              <span class="table-link" @click="handleProductInfo(row)" v-if="row.productId">{{ row.hasOwnProperty('product') ? (row.product.hasOwnProperty('productName') ? row.product.productName : '') : '' }}</span>
              <span v-else></span>
            </template>
          </el-table-column>
          <el-table-column label="规格" align="center" show-overflow-tooltip>
            <template slot-scope="{ row }">{{ row.product.specs }}</template>
          </el-table-column>
          <el-table-column label="材质" align="center" show-overflow-tooltip>
            <template slot-scope="{ row }">{{ row.product.materialQuality }}</template>
          </el-table-column>
          <el-table-column label="表面处理" align="center" show-overflow-tooltip>
            <template slot-scope="{ row }">{{ row.product.surface }}</template>
          </el-table-column>
          <el-table-column label="供应数量" align="center" show-overflow-tooltip>
            <template slot-scope="{ row }">
              <span class="table-primary">{{ row.stock + (row.product.unit ? row.product.unit : '') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="促销价格" align="center" show-overflow-tooltip>
            <template slot-scope="{ row }">
              <span class="table-price pointer" @click="handleView(row)">{{ '￥' + row.price + '元' + (row.product.unit ? '/' + row.product.unit : '') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="排序" align="center" prop="sort" show-overflow-tooltip />
        </template>
        <template v-if="tabActive === 0">
          <el-table-column label="报价编号" prop="serial" align="center" show-overflow-tooltip class-name="cell-new">
            <template slot-scope="{ row }">
              <i class="quick-serial" v-if="row.quoteType === 'quickly'"></i>
              <span>{{ row.serial }}</span>
            </template>
          </el-table-column>
          <el-table-column label="报价名称" prop="name" align="center" show-overflow-tooltip class-name="cell-new">
            <template slot-scope="{ row }">
              <div style="position: relative" @click="handleOffer(row)">
                <span class="table-link">{{ row.name }}</span>
                <span :class="{ 'table-new': row.new }"></span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="毛总重" prop="grossWeight" align="center">
            <template slot-scope="{ row }">{{ row.grossWeight + 'kg' }}</template>
          </el-table-column>
          <el-table-column label="净总重" prop="netWeight" align="center">
            <template slot-scope="{ row }">{{ row.netWeight + 'kg' }}</template>
          </el-table-column>
          <el-table-column label="报价产品" prop="productName" align="center" show-overflow-tooltip>
            <template slot-scope="{ row }">
              <span class="table-link" @click="handleProductInfo(row)" v-if="row.productId">{{ row.hasOwnProperty('product') ? (row.product.hasOwnProperty('productName') ? row.product.productName : '') : '' }}</span>
              <span v-else></span>
            </template>
          </el-table-column>
          <el-table-column label="未税总价" prop="totalPrice" show-overflow-tooltip align="center">
            <template slot-scope="{ row }">
              <b style="font-size: 14px; color: #ec2454" class="pointer" @click="handleHistory(row)" v-if="row.method !== 'ton'">{{ '¥' + row.totalPrice + '元' + (row.product.unit ? '/' + row.product.unit : '') }}</b>
              <b style="font-size: 14px; color: #ec2454" class="pointer" @click="handleHistory(row)" v-if="row.method === 'ton'">{{ '¥' + row.tonPrice + '元/吨' }}</b>
            </template>
          </el-table-column>
          <el-table-column label="含税总价" prop="taxPrice" show-overflow-tooltip align="center">
            <template slot-scope="{ row }">
              <b style="font-size: 14px; color: #ec2454" v-if="row.method !== 'ton'">{{ row.taxPrice ? '¥' + row.taxPrice + '元' + (row.product.unit ? '/' + row.product.unit : '') : '' }}</b>
              <b style="font-size: 14px; color: #ec2454" v-if="row.method === 'ton'">{{ row.tonTaxPrice ? '¥' + row.tonTaxPrice + '元/吨' : '' }}</b>
            </template>
          </el-table-column>
        </template>
        <!--        <el-table-column label="状态" prop="status" align="center">-->
        <!--          <template slot-scope="{ row }">-->
        <!--            <span class="isStatus">已设促销品</span>-->
        <!--            <span>未设为促销品</span>-->
        <!--          </template>-->
        <!--        </el-table-column>-->
        <el-table-column label="操作" align="center">
          <template slot-scope="{ row }">
            <template v-if="tabActive === 1">
              <button class="promotion-button" @click="handleDelete(row)">删除</button>
              <button class="promotion-button primary" @click="handleUpdate(row)">修改</button>
            </template>
            <template v-if="tabActive === 0">
              <button class="promotion-button primary" @click="handleAdd(row)">设为促销品</button>
            </template>
          </template>
        </el-table-column>
      </el-table>

      <div class="custom-pagination">
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" v-if="tabActive === 1" />
        <pagination v-show="total > 0" :total="total" :page.sync="quoteParams.pageNum" :limit.sync="quoteParams.pageSize" @pagination="getQuotationList" v-if="tabActive === 0" />
      </div>
    </div>
    <!-- 产品详情 -->
    <product-dialog ref="productInfo"></product-dialog>
    <!--  历史报价/报价详细  -->
    <dialog-tpl ref="dialogTpl"></dialog-tpl>
    <!--  设置促销品/修改促销品  -->
    <el-dialog v-dialogDragBox :title="title" :visible.sync="open" width="1150px" class="custom-dialog">
      <div class="product-info" v-if="!form.promotionId">
        <el-image :src="info.product.picture1_oss || imgPath + info.product.picture1 || imgPath + info.product.diagram" class="product-info-image"></el-image>
        <div class="product-info-box">
          <div class="product-info-item">
            <span>报价产品</span>
            <b class="link pointer" @click="handleProductInfo(info)">{{ info.hasOwnProperty('product') && info.product.productName }}</b>
          </div>
          <div class="product-info-item">
            <span>报价编号</span>
            <b>{{ info.serial }}</b>
          </div>
          <div class="product-info-item">
            <span>含税总价</span>
            <b class="price" v-if="info.method !== 'ton'">{{ info.taxPrice ? '¥' + info.taxPrice + '元' + (info.product.unit ? '/' + info.product.unit : '') : '' }}</b>
            <b class="price" v-if="info.method === 'ton'">{{ info.tonTaxPrice ? '¥' + info.tonTaxPrice + '元/吨' : '' }}</b>
          </div>
          <div class="product-info-item">
            <span>创建时间</span>
            <b>{{ info.createTime }}</b>
          </div>
          <div class="product-info-item">
            <span>净总重</span>
            <b>{{ info.netWeight && `${info.netWeight}kg` }}</b>
          </div>
          <div class="product-info-item">
            <span>毛总重</span>
            <b>{{ info.grossWeight && `${info.grossWeight}kg` }}</b>
          </div>
          <div class="product-info-item">
            <span>未税总价</span>
            <b class="price pointer" @click="handleHistory(info)" v-if="info.method !== 'ton'">{{ info.totalPrice ? '¥' + info.totalPrice + '元' + (info.product.unit ? '/' + info.product.unit : '') : '' }}</b>
            <b class="price pointer" @click="handleHistory(info)" v-if="info.method === 'ton'">{{ info.tonPrice ? '¥' + info.tonPrice + '元/吨' : '' }}</b>
          </div>
          <div class="product-info-item">
            <span>报单人</span>
            <b>{{ info.createBy }}</b>
          </div>
        </div>
      </div>
      <div class="product-form">
        <el-form ref="form" :model="form" :rules="rules" label-width="80px">
          <el-row :gutter="10">
            <el-col :span="12">
              <el-form-item label="促销价格" prop="price">
                <el-input v-model="form.price" placeholder="请输入促销价格">
                  <template slot="suffix">
                    <div class="suffix">
                      <div class="unit">元{{ (info.method === 'ton' && '吨') || (info.product.unit && `/${info.product.unit}`) }}</div>
                      <div class="now" v-if="!!priceList.length">
                        <span>当前最低促销价格：</span>
                        <b>{{ getMiniNum() }}</b>
                      </div>
                    </div>
                  </template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="促销周期" prop="timeArr">
                <el-date-picker style="width: 100%" v-model="form.timeArr" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd" :picker-options="timeArrOptions"></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="促销数量" prop="stock">
                <el-input v-model="form.stock" placeholder="请输入促销数量">
                  <span slot="suffix">{{ (info.method === 'ton' && '吨') || (info.hasOwnProperty('product') && info.product.unit) }}</span>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="排序" prop="sort">
                <el-input v-model="form.sort" placeholder="请输入排序">
                  <template slot="suffix">
                    <span>排序越小越靠前</span>
                  </template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div class="product-tip">
        <i class="el-icon-warning-outline"></i>
        <span>设置促销品价格后会根据价格高低顺序优先展示价格最低的产品，请知悉；</span>
        <template v-if="!!priceList.length">
          <span>如果您想查看此产品的所有促销价格请点击右侧按钮查看</span>
          <b @click="viewOpen = true">去查看</b>
        </template>
      </div>
      <div slot="footer">
        <button type="button" class="custom-dialog-btn" @click="open = false">取消</button>
        <button type="button" class="custom-dialog-btn primary" @click="handleSubmit">确定</button>
      </div>
    </el-dialog>
    <!--  查看已设置过的报价  -->
    <el-dialog v-dialogDragBox title="查看促销品价格" :visible.sync="viewOpen" width="1150px" class="custom-dialog">
      <div style="padding: 0 20px">
        <el-table ref="viewTable" stripe border :data="priceList" :span-method="priceSpanMethod" class="custom-table">
          <el-table-column label="产品名称" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
              <span class="table-link" @click="handleProductInfo(info)">{{ info.product.productName }}</span>
            </template>
          </el-table-column>
          <el-table-column label="规格" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ info.product.specs }}</template>
          </el-table-column>
          <el-table-column label="材质" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ info.product.materialQuality }}</template>
          </el-table-column>
          <el-table-column label="表面处理" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ info.product.surface }}</template>
          </el-table-column>
          <el-table-column label="供应数量" align="center" show-overflow-tooltip>
            <template slot-scope="{ row }">
              <span class="table-primary">{{ row.stock + (row.method === 'ton' ? '吨' : info.product.unit) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="促销价格" align="center" show-overflow-tooltip>
            <template slot-scope="{ row }">
              <span class="table-price">{{ '￥' + row.price + '元' + (row.method === 'ton' ? '/吨' : `/${info.product.unit}`) }}</span>
            </template>
          </el-table-column>          
        </el-table>
      </div>
      <div slot="footer">
        <button type="button" class="custom-dialog-btn primary" @click="viewOpen = false">关闭</button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import ProductDialog from '@/views/public/product/dialog'
import DialogTpl from './dialog'
import { getPromotionList, addPromotion, deletePromotion, updatePromotion, getPromotion } from '@/api/promotion'
import { list } from '@/api/houtai/formula'
import { isNumber, isNumberLength } from '@/utils/validate'

export default {
  name: 'Promotion',
  components: { ProductDialog, DialogTpl },
  data() {
    return {
      key: 1,
      // 查询促销品条件
      queryParams: {
        categoryId: undefined, // 分类
        materialQuality: undefined, // 材质
        model: undefined, // 型号
        productCode: undefined, // 产品编码
        productName: undefined, // 产品名称
        specs: undefined, // 规格
        surface: undefined, // 表面处理
        status: undefined, // 状态
        expire: undefined, // 有效期
        pageNum: 1,
        pageSize: 10
      },
      // 查询报价产品条件
      quoteParams: {
        name: undefined, // 报价名称
        createBy: undefined, // 报单人
        serial: undefined, // 报价编号
        productName: undefined, // 报价产品
        productCode: undefined, // 产品编码
        specs: undefined, // 产品规格
        model: undefined, // 产品型号
        materialQuality: undefined, // 产品材质
        surface: undefined, // 表面处理
        isPublish: true, // 报价状态
        isEditing: false, // 修改状态
        source: 'common', // 产品来源
        pageNum: 1,
        pageSize: 10
      },
      // 表格数据
      list: [],
      total: 0,
      loading: true,
      // tab切换
      tabOptions: [
        { label: '所有报价产品', value: 0 },
        { label: '促销品', value: 1 }
      ],
      tabActive: 0,
      // 表单处理
      title: '',
      open: false,
      form: {},
      rules: {
        price: [
          { required: true, message: '请输入促销价格', trigger: 'blur' },
          { validator: isNumber, message: '请输入正确的促销价格', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: 'blur' }
        ],
        timeArr: [{ required: true, message: '请选择促销周期', trigger: 'change' }],
        stock: [
          { required: true, message: '请输入促销数量', trigger: 'blur' },
          { validator: isNumber, message: '请输入正确的促销数量', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: 'blur' }
        ],
        sort: [
          { required: true, message: '请输入排序', trigger: 'blur' },
          { validator: isNumber, message: '请输入正确的排序', trigger: 'blur' }
        ]
      },
      info: { product: { picture1_oss: '' } },
      // 查看促销品价格
      priceList: [],
      viewOpen: false,
      showAll: false,
      word: '展开搜索',
      timeArrOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 24 * 3600 * 1000 || time.getTime() > Date.now() + 10 * 24 * 3600 * 1000
        }
      }
    }
  },
  created() {
    this.getQuotationList()
  },
  methods: {
    // 切换tab
    handleTabChange(item) {
      this.list = []
      this.tabActive = item.value
      if (this.tabActive === 1) this.resetQuery()
      if (this.tabActive === 0) this.resetQuoteQuery()
    },
    // 查询报价产品
    getQuotationList() {
      this.loading = true
      list(this.quoteParams).then(res => {
        const { code, msg, rows, total } = res
        if (code === 200) {
          this.list = rows
          this.total = total
          this.key = Math.random()
          this.loading = false
        } else this.$message.error(msg)
      })
    },
    // 列表
    getList() {
      this.loading = true
      getPromotionList(this.queryParams).then(res => {
        const { code, msg, rows, total } = res
        if (code === 200) {
          this.list = rows
          this.total = total
          this.key = Math.random()
          this.loading = false
        } else this.$message.error(msg)
      })
    },
    // 报价搜索
    handleQuoteQuery() {
      this.quoteParams.pageNum = 1
      this.getQuotationList()
    },
    // 重置报价搜索
    resetQuoteQuery() {
      this.resetForm('quoteParams')
      this.handleQuoteQuery()
    },
    // 产品搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 重置产品搜索
    resetQuery() {
      this.resetForm('query')
      this.handleQuery()
    },
    // 展开收起搜索
    clickSearch() {
      this.showAll = !this.showAll
      this.word = this.showAll ? '收起搜索' : '展开搜索'
    },
    // 查看报价明细
    handleOffer(row) {
      this.$refs.dialogTpl.handleQuote(row)
    },
    // 查看历史报价
    handleHistory(row) {
      this.$refs.dialogTpl.handleHistory(row)
    },
    // 产品详情
    handleProductInfo(row, type) {
      this.$refs.productInfo.handleView(row.product, type)
    },
    // 表单重置
    reset() {
      this.form = {
        endTime: undefined,
        method: undefined,
        price: undefined,
        promotionId: undefined,
        productId: undefined,
        remark: undefined,
        startTime: undefined,
        stock: undefined,
        status: undefined,
        sort: 999,
        timeArr: []
      }
      this.resetForm('form')
    },
    // 设为促销品
    handleAdd(row) {
      const start = this.parseTime(new Date(), '{y}-{m}-{d}')
      const end = this.parseTime(new Date().getTime() + 3600 * 1000 * 24 * 10, '{y}-{m}-{d}')
      this.reset()
      this.form.timeArr = [start, end]
      this.info = { ...row }
      const { productId } = row
      getPromotion({ productId }).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          this.priceList = data
          this.title = '设为促销品'
          this.open = true
        } else this.$message.error(msg)
      })
    },
    // 获取最低价
    getMiniNum() {
      if (!!this.priceList.length) {
        const priceArr = this.priceList.map(ite => ite.price)
        const minPrice = Math.min(...priceArr)
        const index = this.priceList.findIndex(ite => ite.price === minPrice)
        let unit = ''
        if (this.priceList[index].method === 'ton') unit = '吨'
        else unit = this.info.product.unit
        return `${minPrice}元/${unit}`
      } else return ''
    },
    // 修改促销产品
    handleUpdate(row) {
      this.reset()
      this.info = { ...row }
      const { price, endTime, method, id, remarak, startTime, stock, productId, status, sort } = row
      this.form = {
        price,
        method,
        promotionId: id,
        productId,
        remarak,
        stock,
        endTime,
        startTime,
        status,
        sort,
        timeArr: [startTime, endTime]
      }
      getPromotion({ productId }).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          this.priceList = data
          this.title = '修改促销品'
          this.open = true
        } else this.$message.error(msg)
      })
    },
    // 查看促销产品价格列表
    handleView(row) {
      this.info = { ...row }
      const { productId } = row
      getPromotion({ productId }).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          this.priceList = data
          this.viewOpen = true
        } else this.$message.error(msg)
      })
    },
    // 提交
    handleSubmit() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.promotionId != null) {
            const startTime = this.form.timeArr[0] + ' 23:59:59'
            const endTime = this.form.timeArr[1] + ' 23:59:59'
            const { price, method, remark, promotionId, status, stock, sort } = this.form
            const data = { startTime, endTime, price, method, remark, promotionId, status, stock, sort }
            updatePromotion(data).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('修改成功')
                this.open = false
                this.getList()
              } else this.$message.error(msg)
            })
          } else {
            const { method, productId } = this.info
            const startTime = this.form.timeArr[0] + ' 23:59:59'
            const endTime = this.form.timeArr[1] + ' 23:59:59'
            const { price, remark, stock, sort } = this.form
            const data = { endTime, method, price, productId, remark, startTime, stock, sort }
            addPromotion(data).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('新增成功')
                this.open = false
                this.getQuotationList()
              } else this.$message.error(msg)
            })
          }
        }
      })
    },
    // 合并单元格
    priceSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        const _row = this.priceList.length
        if (rowIndex % _row === 0) {
          return {
            rowspan: _row,
            colspan: 1
          }
        } else {
          return {
            rowspan: 0,
            colspan: 0
          }
        }
      }
    },
    // 删除促销产品
    // prettier-ignore
    handleDelete(row) {
      const promotionIds = row.id
      this.$confirm('是否删除此促销产品?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deletePromotion({ promotionIds }).then(res => {
          if (res.code === 200) {
            this.$message.success('删除成功')
            this.getList()
          } else {
            this.$message.error(res.msg)
          }
        })
      }).catch(() => {
      })
    }
  }
}
</script>
<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
.tableBox {
  padding: 20px;
}
::v-deep {
  .custom-search {
    .el-form-item__content {
      width: calc(100% - 5em);
    }
  }
  .quick-serial {
    float: left;
    display: inline-block;
    width: 20px;
    height: 20px;
    line-height: 20px;
    border-radius: 50%;
    background-color: #f43f3f;
    color: #ffffff;
    text-align: center;
    margin-right: 5px;
    position: relative;
    &:after {
      font-style: normal;
      content: '快';
      display: block;
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
    }
  }
  .el-textarea .el-textarea__inner {
    font-family: inherit;
  }
  .cell-new {
    .cell {
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
  .table-new {
    display: inline-block;
    width: 28px;
    height: 14px;
    background: url(~@/assets/images/new.png) center no-repeat;
    position: absolute;
    top: 0;
    right: -28px;
  }
  .isStatus {
    color: $success;
    &:before {
      content: '';
      display: inline-block;
      width: 10px;
      height: 10px;
      border-radius: 50%;
      background-color: $success;
      margin-right: 10px;
    }
  }
  .promotion-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 90px;
    height: 30px;
    border: 1px solid #cbd7e2;
    border-radius: 5px;
    font-size: 12px;
    color: $info;
    cursor: pointer;
    background: transparent;
    outline: none;
    &.small {
      width: 60px;
    }
    &:hover {
      transition: all 0.3s;
      color: $font;
      border-color: $font;
    }
    &.primary {
      color: $blue;
      border-color: $blue;
      background-color: #e0ebff;
      &:hover {
        transition: all 0.3s;
        background-color: $blue;
        color: $white;
      }
    }
    &.success {
      color: $success;
      border-color: $success;
      background-color: #f0f9eb;
      &:hover {
        transition: all 0.3s;
        background-color: $success;
        color: $white;
      }
    }
  }
  .promotion-button + .promotion-button {
    margin-left: 10px;
  }
  .product {
    &-info {
      display: flex;
      align-items: center;
      padding: 20px;
      &-image {
        width: 52px;
        height: 52px;
        border: 1px solid #ededed;
        border-radius: 5px;
      }
      &-box {
        margin-left: 15px;
        width: calc(100% - 67px);
        display: flex;
        flex-wrap: wrap;
      }
      &-item {
        display: inline-flex;
        align-items: center;
        width: 20%;
        height: 35px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        span {
          display: inline-block;
          width: 4.5em;
          font-size: 12px;
          color: $disabled;
        }
        b {
          font-weight: 500;
          font-size: 14px;
          color: $font;
          &.link {
            color: $blue;
            &:hover {
              text-decoration: underline;
            }
          }
          &.price {
            color: #ec2454;
          }
        }
      }
    }
    &-form {
      padding: 20px 20px 0;
      background-color: #f0f3f9;
      position: relative;
      &:before {
        position: absolute;
        top: -16px;
        left: 50%;
        transform: translateX(-50%);
        content: '';
        display: inline-block;
        width: 0;
        height: 0;
        border: 8px solid transparent;
        border-bottom-color: #f0f3f9;
      }
      .suffix {
        display: inline-flex;
        align-items: center;
        .unit {
          color: $disabled;
          margin-right: 5px;
        }
        .now {
          line-height: 30px;
          padding: 0 15px;
          background-color: #ffefef;
          border-radius: 5px;
          font-size: 12px;
          span {
            color: #f35d09;
          }
          b {
            font-weight: normal;
            color: #ec4545;
          }
        }
      }
    }
    &-tip {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 65px;
      font-size: 14px;
      background-color: #ffefe5;
      color: #f35d09;
      b {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        margin-left: 10px;
        width: 105px;
        height: 32px;
        border: 1px solid #f35d09;
        border-radius: 22px;
        cursor: pointer;
        font-weight: normal;
        &:hover {
          opacity: 0.8;
        }
      }
    }
  }
}
</style>
