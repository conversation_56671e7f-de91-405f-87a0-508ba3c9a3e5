<template>
  <div>
    <el-dialog v-dialogDragBox title="报价明细" :visible.sync="quoteShow" width="1150px" class="custom-dialog">
      <template v-if="quoteInfo.fragments">
        <div class="goods" v-for="(item, index) in quoteInfo.fragments" :key="index">
          <div class="goods1">{{ item[0].stageName }}</div>
          <div class="goods2" v-for="(item2, index2) in item" :key="`${index2}+'a'`">
            <div class="goods2-1" @click="quoteActiveId = quoteActiveId == item2.fragmentId ? -1 : item2.fragmentId">
              <div>{{ item2.templateName }}</div>
              <div v-for="(item3, index3) in item2.ext" :key="`${index3}+'b'`">
                {{ item3.itemName }}:{{ item3.value }}
                {{ item3.itemUnit }}
              </div>
              <div style="position: absolute; right: 100px">
                小计 :
                <span style="color: #ec2454; font-weight: bold">¥ {{ item2.price }}</span>
              </div>
              <img :style="quoteActiveId == item2.fragmentId ? 'transform: rotate(180deg)' : 'transform: rotate(0deg)'" class="goods2-1-jt" src="~@/assets/images/downIcon.png" />
            </div>
            <el-collapse-transition>
              <div class="goods2-2" v-if="quoteActiveId == item2.fragmentId">
                <div class="tmpbox">
                  <div class="tmpbox-1">
                    {{ item2.templateName }}
                    <span class="innerQuote-form-link" @click="handleProductInfo(item2)">{{ item2.hasOwnProperty('product') ? `关联产品：${item2.product.productName} / ${item2.product.specs}` : '' }}</span>
                    {{ item2.hasOwnProperty('k') ? `关联K线：${item2.k.name}` : '' }}{{ item2.hasOwnProperty('innerQuote') ? `关联二级报价：${item2.innerQuote.name} 编号：${item2.innerQuote.serial}` : '' }}
                  </div>
                  <div class="show" v-if="item2" style="background-color: white">
                    <div class="showitem">
                      <template v-for="(item3, inde3x) in item2.fragmentItems.show">
                        <template v-if="item3.itemType == 'select'">
                          <div class="showitem2" :key="`${inde3x}+'c'`">
                            <div>
                              {{ item3.itemName }}
                            </div>
                            <div>
                              <el-select v-model="item3.value" placeholder="请选择" style="width: 100%" disabled>
                                <el-option v-for="option in item3.values" :key="option.itemValueId" :label="option.valueName" :value="option.value"></el-option>
                              </el-select>
                            </div>
                          </div>
                        </template>

                        <template v-if="item3.itemType == 'input'">
                          <div class="showitem2" :key="`${inde3x}+'c'`">
                            <div>
                              {{ item3.itemName }}
                            </div>
                            <div style="position: relative">
                              <el-input v-model="item3.value" :placeholder="item3.placeholder" disabled></el-input>
                              <div class="unit">{{ item3.itemUnit }}</div>
                            </div>
                          </div>
                        </template>
                        <template v-if="item3.itemType == 'input-radio'">
                          <div class="showitem2" :key="`${inde3x}+'c'`">
                            <div>
                              {{ item3.itemName }}
                            </div>
                            <div style="position: relative">
                              <el-input v-model="item3.value" :placeholder="item3.placeholder" disabled></el-input>
                              <div class="unit">{{ item3.itemUnit }}</div>
                            </div>
                          </div>
                        </template>

                        <template v-if="item3.itemType == 'input-select'">
                          <div class="showitem2" style="position: relative" :key="`${inde3x}+'c'`">
                            <div>
                              {{ item3.itemName }}
                            </div>
                            <div>
                              <el-input v-model="item3.value" :placeholder="item3.placeholder" disabled></el-input>
                              <template v-for="(u, ui) in JSON.parse(item3.itemUnit)">
                                <div class="unit" v-if="u.convert == 1" :key="`${ui}+'d'`">{{ u.name }}</div>
                              </template>
                            </div>
                            <div style="position: absolute; top: 48px; right: 5px; border: 0px solid; height: 20px; display: flex; line-height: 20px; font-size: 12px; color: #999"></div>
                          </div>
                        </template>
                      </template>
                      <template v-if="item2.stage == 'inner' && item2.innerQuote">
                        <el-form ref="item2.innerQuote" :model="item2.innerQuote" disabled label-width="5em" class="innerQuote-form">
                          <el-row :gutter="30">
                            <el-col :span="12">
                              <el-form-item label="报价编号">
                                <el-input v-model="item2.innerQuote.serial"></el-input>
                              </el-form-item>
                            </el-col>
                            <el-col :span="12">
                              <el-form-item label="报价名称">
                                <el-input v-model="item2.innerQuote.name"></el-input>
                              </el-form-item>
                            </el-col>
                            <el-col :span="12">
                              <el-form-item label="总价">
                                <el-input v-model="item2.innerQuote.totalPrice">
                                  <template slot="prefix">￥</template>
                                  <template slot="suffix">元</template>
                                </el-input>
                              </el-form-item>
                            </el-col>
                            <el-col :span="12">
                              <el-form-item label="毛总重">
                                <el-input v-model="item2.innerQuote.grossWeight">
                                  <template slot="suffix">Kg</template>
                                </el-input>
                              </el-form-item>
                            </el-col>
                            <el-col :span="12">
                              <el-form-item label="净总重">
                                <el-input v-model="item2.innerQuote.netWeight">
                                  <template slot="suffix">Kg</template>
                                </el-input>
                              </el-form-item>
                            </el-col>
                            <el-col :span="12">
                              <el-form-item label="报价产品">
                                <span class="innerQuote-form-link" @click="handleProductInfo(item2.innerQuote)">{{ item2.innerQuote.product.productName }}</span>
                              </el-form-item>
                            </el-col>
                          </el-row>
                        </el-form>
                      </template>
                    </div>
                    <div class="showimg">
                      <template v-if="item2.dynamicType == 'image'">
                        <img :src="imgPath + item2.dynamic" />
                      </template>
                      <template v-else>
                        <div style="width: 90%; margin: 10px auto; font-size: 12px; color: #999" v-html="item2.dynamic"></div>
                      </template>
                    </div>
                  </div>
                  <div class="tmpbox-2" v-if="item2.stage != 'inner'">计算结果</div>
                  <div class="show" style="background: #f1f3f8" v-if="item2.stage != 'inner'">
                    <div class="showitem">
                      <template v-for="(item3, inde3x) in item2.fragmentItems.result">
                        <template v-if="item3.itemType == 'input'">
                          <div class="showitem2" :key="`${inde3x}+'e'`">
                            <div>
                              {{ item3.itemName }}
                            </div>
                            <div style="position: relative">
                              <el-input v-model="item3.value" :placeholder="item3.placeholder" disabled></el-input>
                              <div class="unit">{{ item3.itemUnit }}</div>
                            </div>
                          </div>
                        </template>
                        <template v-if="item3.itemType == 'input-expression'">
                          <div class="showitem2" :key="`${inde3x}+'e'`">
                            <div>
                              {{ item3.itemName }}
                            </div>
                            <div style="position: relative">
                              <el-input v-model="item3.value" :placeholder="item3.placeholder" disabled></el-input>
                              <div class="unit">{{ item3.itemUnit }}</div>
                            </div>
                          </div>
                        </template>
                        <template v-if="item3.itemType == 'input-reference'">
                          <div class="showitem2" :key="`${inde3x}+'e'`">
                            <div>
                              {{ item3.itemName }}
                            </div>
                            <div style="position: relative">
                              <el-input v-model="item3.value" :placeholder="item3.placeholder" disabled></el-input>
                              <div class="unit">{{ item3.itemUnit }}</div>
                            </div>
                          </div>
                        </template>
                      </template>
                    </div>
                    <div class="showimg" style="border: 0"></div>
                  </div>
                </div>
              </div>
            </el-collapse-transition>
          </div>
        </div>
      </template>
      <el-empty v-else />
      <div slot="footer">
        <button type="button" class="custom-dialog-btn primary" @click="quoteShow = false">关闭</button>
      </div>
    </el-dialog>

    <el-dialog v-dialogDragBox title="历史报价" :visible.sync="historyShow" width="40%" class="custom-dialog">
      <div class="history">
        <div class="history-item" :class="{ active: item.value === dayValue }" v-for="item in dayOptions" :key="item.value" @click="handleChangeDay(item)">
          {{ item.label }}
        </div>
      </div>
      <div v-if="history.length">
        <line-chart-history :chart-data="history" />
        <el-table :data="history" border style="width: 96%; margin: 20px auto">
          <el-table-column label="序号" width="180">
            <template slot-scope="scope">{{ scope.$index + 1 }}</template>
          </el-table-column>
          <el-table-column prop="price" label="历史报价"></el-table-column>
          <el-table-column prop="createTime" label="时间"></el-table-column>
        </el-table>
      </div>
      <el-empty v-else />
    </el-dialog>
    <product-tpl ref="productInfo" />
  </div>
</template>
<script>
import LineChartHistory from '@/views/dashboard/LineChartHistory'
import { listHistoryList, quotesel } from '@/api/houtai/formula'
import ProductTpl from '@/views/public/product/dialog'

export default {
  components: { ProductTpl, LineChartHistory },
  data() {
    return {
      // 历史报价
      history: [],
      historyShow: false,
      historyId: undefined,
      dayOptions: [
        { label: '7天', value: 7 },
        { label: '15天', value: 15 },
        { label: '30天', value: 30 },
        { label: '90天', value: 90 }
      ],
      dayValue: 7,
      // 报价明细
      quoteShow: false,
      quoteInfo: {},
      quoteActiveId: -1
    }
  },
  methods: {
    // 打开历史报价
    handleHistory(row) {
      this.dayValue = 7
      this.historyId = row.id
      this.getHistoryInfo()
      this.historyShow = true
    },
    // 切换历史报价
    handleChangeDay(item) {
      this.dayValue = item.value
      this.getHistoryInfo()
    },
    // 历史报价数据
    getHistoryInfo() {
      const query = { quoteId: this.historyId, days: this.dayValue }
      listHistoryList(query).then(res => {
        if (res.data) {
          res.data.map(item => {
            item.createTime = this.parseTime(item.createTime, '{y}-{m}-{d} {h}:{i}:{s}')
          })
          this.history = res.data
        }
      })
    },
    // 报价明细
    handleQuote(row) {
      quotesel({ quoteId: row.id }).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          this.quoteInfo = data
          this.quoteShow = true
        } else this.$message.error(msg)
      })
    },
    // 产品详情
    handleProductInfo(row, type) {
      this.$refs.productInfo.handleView(row.product, type)
    }
  }
}
</script>
<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';
::v-deep {
  .history {
    display: flex;
    align-items: center;
    padding: 0 2%;
    &-item {
      padding: 5px 20px;
      margin-right: 10px;
      border: 1px solid #cccccc;
      border-radius: 5px;
      cursor: pointer;
      &:hover,
      &.active {
        border-color: #2e73f3;
        color: #2e73f3;
      }
    }
  }
  .goods {
    width: 99.9%;
    margin-top: 5px;
    margin-bottom: 10px;
    box-shadow: 0 0 26px 0 rgba(0, 0, 0, 0.05);
    background-color: white;
    border-radius: 5px;
    padding-bottom: 20px;
    .goods1 {
      width: 100%;
      height: 37px;
      border-bottom: 1px solid #e7e9ec;
      line-height: 37px;
      text-indent: 20px;
      font-size: 14px;
      color: #666666;
    }
    .goods2 {
      width: 98%;
      margin: 20px auto 0;
      background-color: #f1f1f3;
      border-radius: 5px;
      border: 1px solid #cbd6e2;
      .goods2-1 {
        width: 100%;
        height: 60px;
        display: flex;
        line-height: 60px;
        font-size: 14px;
        cursor: pointer;
        position: relative;
        & > div {
          color: #999;
          margin-left: 30px;
          &:nth-child(1) {
            min-width: 10%;
            width: auto;
            margin-left: 20px;
            color: #333;
            font-weight: bold;
          }
        }
        .goods2-1-jt {
          width: 20px;
          height: 15px;
          position: absolute;
          right: 10px;
          top: 22px;
          transition: 0.2s;
        }
      }
      .tmpbox {
        width: 98%;
        margin: 20px auto;
        border: 1px solid #cbd6e2;
        .tmpbox-1 {
          width: 100%;
          height: 46px;
          background: #ecf3ff;
          line-height: 46px;
          color: #2e73f3;
          font-size: 14px;
          text-indent: 39px;
        }
        .tmpbox-2 {
          width: 100%;
          height: 46px;
          background: #f1f3f8;
          line-height: 46px;
          color: #333;
          font-size: 14px;
          text-indent: 39px;
          border-top: 1px solid #cbd6e2;
          border-bottom: 1px solid #cbd6e2;
        }
      }
      .show {
        width: 100%;
        display: flex;
        justify-content: space-around;
      }
      .showimg {
        width: 35%;
        border: 1px solid #cbd7e2;
        margin-top: 30px;
        margin-bottom: 30px;
      }
      .showimg > img {
        width: 100%;
        height: 100%;
      }
      .showitem {
        width: 55%;
        padding-top: 16px;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
      }
      .showitem2 {
        width: 49%;
        height: 46px;
        margin-bottom: 30px;
        line-height: 46px;
        display: flex;
        justify-content: space-between;
      }
      .showitem2 > div:nth-child(1) {
        width: 30%;
        color: #999999;
        font-size: 12px;
      }
      .showitem2 > div:nth-child(2) {
        width: 70%;
      }
      .unit {
        position: absolute;
        top: 0;
        right: 20px;
        font-size: 14px;
        color: #999;
      }
      .innerQuote-form {
        ::v-deep {
          .el-form-item__label {
            font-size: 12px;
            font-weight: normal;
          }
        }
        &-link {
          color: #2e73f3;
          cursor: pointer;
          &:hover {
            text-decoration: underline;
          }
        }
      }
    }
  }
}
</style>
