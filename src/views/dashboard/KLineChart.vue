<template>
  <div :class="className" :style="{ height: height, width: width }" />
</template>

<script>
import * as echarts from 'echarts'

require('echarts/theme/macarons') // echarts theme
import resize from './mixins/resize'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '350px'
    },
    autoResize: {
      type: Boolean,
      default: true
    },
    chartData: {
      type: [Object, Array],
      required: true
    },
    isZoom: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        if (this.isZoom) {
          val.dataZoom = [
            { type: 'inside', start: 60, end: 100 },
            { start: 60, end: 100 }
          ]
        } else {
          val.dataZoom = []
        }
        this.setOptions(val)
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el, 'macarons')
      this.setOptions(this.chartData)
    },
    setOptions({ series, yMin, yMax, dataZoom }) {
      this.chart.clear()
      if (series) {
        let xAxis = []
        series.steelPrices.map(ite => {
          xAxis.push(ite.date)
        })
        let seriesData = []
        let data = []
        series.steelPrices.map(ite => {
          data.push(ite.price)
        })
        seriesData.push({
          name: series.name,
          type: 'line',
          smooth: true,
          symbol: 'none',
          itemStyle: {
            color: series.color
          },
          tooltip: {
            valueFormatter: function (value) {
              return parseFloat(value) + ' 元/吨' || 0 + ' 元/吨'
            }
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: series.color
              },
              {
                offset: 1,
                color: 'rgba(255, 255, 255, 0.1)'
              }
            ])
          },
          data: data
        })
        this.chart.setOption({
          backgroundColor: 'rgb(255,255,255)',
          grid: {
            top: '10%',
            left: '5%',
            right: '8%',
            bottom: '10%',
            containLabel: true
          },
          tooltip: {
            trigger: 'axis'
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            axisLabel: {
              textStyle: {
                color: '#333'
              }
            },
            axisLine: {
              lineStyle: {
                color: '#D9D9D9'
              }
            },
            data: xAxis
          },
          yAxis: {
            min: yMin || function (value) { return value.min - 100 },
            max: yMax || function (value) { return value.max + 100 },
            type: 'value',
            axisLabel: {
              textStyle: {
                color: '#666'
              }
            },
            nameTextStyle: {
              color: '#666',
              fontSize: 12,
              lineHeight: 40
            },
            splitLine: {
              lineStyle: {
                type: 'dashed',
                color: '#E9E9E9'
              }
            },
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            }
          },
          dataZoom: dataZoom,
          series: seriesData
        })
      }
    }
  }
}
</script>
