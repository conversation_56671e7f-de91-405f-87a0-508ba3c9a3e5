<template>
  <div :class="className" :style="{ height: height, width: width }" />
</template>

<script>
import * as echarts from 'echarts'

require('echarts/theme/macarons') // echarts theme
import resize from './mixins/resize'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '350px'
    },
    autoResize: {
      type: Boolean,
      default: true
    },
    chartData: {
      type: [Object, Array],
      required: true
    },
    isZoom: {
      type: Boolean,
      default: true
    },
    isIndex: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        if (this.isZoom) {
          val.dataZoom = [
            {
              type: 'slider',
              height: 8,
              handleIcon: 'path://M30.9,53.2C16.8,53.2,5.3,41.7,5.3,27.6S16.8,2,30.9,2C45,2,56.4,13.5,56.4,27.6S45,53.2,30.9,53.2z M30.9,3.5M36.9,35.8h-1.3z M27.8,35.8 h-1.3H27L27.8,35.8L27.8,35.8z',
              handleSize: '200%',
              start: 60,
              end: 100
            },
            { type: 'inside' }
          ]
        } else {
          val.dataZoom = []
        }
        this.setOptions(val)
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el, 'macarons')
      this.setOptions(this.chartData)
    },
    setOptions({ xAxis, min = 0, max = 0, seriesData, dataZoom }) {
      this.chart.clear()
      const isZoom = this.isZoom
      const isIndex = this.isIndex
      if (seriesData) {
        const color = seriesData.map(item => item.color)
        let series = []
        seriesData.map(item => {
          if (item.show) {
            series.push({
              name: item.name,
              type: 'line',
              // stack: 'Total',
              smooth: true,
              smoothMonotone: 'x',
              lineStyle: {
                color: item.color || '#aecefa'
              },
              showSymbol: false,
              areaStyle: {
                opacity: isIndex ? 0 : 0.8,
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: item.color || '#aecefa'
                  },
                  {
                    offset: 1,
                    color: '#e5f3fe'
                  }
                ])
              },
              emphasis: {
                focus: 'series'
              },
              data: item.data
            })
          }
        })
        this.chart.setOption({
          color: color,
          tooltip: {
            trigger: 'axis',
            valueFormatter: function (value) {
              return parseFloat(value) + ' 元/吨' || 0 + ' 元/吨'
            }
          },
          grid: {
            top: '3%',
            left: '2%',
            right: '2%',
            bottom: isZoom ? '50' : '1%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            axisLabel: {
              textStyle: {
                color: '#333333'
              }
            },
            axisLine: {
              lineStyle: {
                color: '#333333'
              }
            },
            data: xAxis
          },
          yAxis: {
            min: min || 'dataMin',
            max: max || 'dataMax',
            type: 'value',
            axisLabel: {
              textStyle: {
                color: '#333333'
              }
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: '#333333'
              }
            },
            splitArea:{
              show: false
            },
            splitLine: {
              show: false
            },
            axisTick: {
              show: true
            }
          },
          dataZoom: dataZoom,
          series: series
        })
      }
    }
  }
}
</script>
