<template>
  <div :class="className" :style="{ height: height, width: width }" />
</template>

<script>
import * as echarts from 'echarts'

require('echarts/theme/macarons') // echarts theme
import resize from './mixins/resize'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '350px'
    },
    autoResize: {
      type: Boolean,
      default: true
    },
    chartData: {
      type: [Object, Array]
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        this.setOptions(val)
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el, 'macarons')
      this.setOptions(this.chartData)
    },
    setOptions(data) {
      this.chart.setOption({
        radar: {
          indicator: [
            { name: '供货能力', max: 5 },
            { name: '售后服务', max: 5 },
            { name: '服务态度', max: 5 },
            { name: '发货速度', max: 5 },
            { name: '时间管理', max: 5 }
          ],
          center: ['50%', '50%'],
          radius: '75%',
          shape: 'circle'
        },
        series: {
          type: 'radar',
          lineStyle: {
            opacity: 0
          },
          symbol: 'none',
          data: [
            {
              value: [1, 1, 1, 1, 1],
              areaStyle: {
                color: {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: 'rgba(46,115,243,0.36)'
                    },
                    {
                      offset: 1,
                      color: 'rgba(46,115,243,0.6)'
                    }
                  ],
                  global: false
                }
              }
            }
          ]
        }
      })
    }
  }
}
</script>
