<template>
  <div>
    <head-tpl :is-login="isLogin" />
    <div class="standard-box">
      <div class="standard-container">
        <div class="standard-header">
          <el-tabs v-model="activeName" @tab-click="handleClick" class="standard-header-tabs">
            <el-tab-pane label="产品标准" name="product"></el-tab-pane>
            <el-tab-pane label="常用标准" name="collect"></el-tab-pane>
          </el-tabs>
          <div class="standard-header-search" v-if="activeName == 'product'">
            <el-select v-model="searchType" placeholder="请选择" size="small" @change="handleQuery">
              <el-option label="按标准" value="code"></el-option>
              <el-option label="按名称" value="name"></el-option>
            </el-select>
            <el-input v-model="serachKeyword" size="small" placeholder="请输入标准名称" @keyup.enter.native="handleQuery" />
            <el-button icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
          </div>
          <div class="standard-header-search" v-else>
            <el-input v-model="collectQuery.keyword" size="small" placeholder="请输入标准名称" @keyup.enter.native="handleQuery" />
            <el-button icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
          </div>
        </div>
        <div class="standard-content" v-loading="loading">
          <div class="standard-content-left" :key="key">
            <el-menu :default-active="queryParams.categoryId + ''" unique-opened>
              <el-submenu v-for="item in standardCategory" :key="item.id" :index="item.id + ''">
                <span slot="title">{{ item.name }}</span>
                <el-menu-item-group>
                  <el-menu-item v-for="subItem in item.children" :key="subItem.id" :index="subItem.id + ''" @click="handleCategoryClick(subItem.id)">
                    <span>{{ subItem.name }}</span>
                  </el-menu-item>
                </el-menu-item-group>
              </el-submenu>
            </el-menu>
          </div>
          <div class="standard-list">
            <div class="standard-location" v-if="activeName == 'product' || (activeName == 'collect' && searchName)">
              <div class="standard-location-box" v-if="activeName == 'product'">
                <span class="standard-location-box-title">已选标准</span>
                <div class="standard-location-box-item" :class="{ active: showChild }" @click="handleLocationClick">
                  <span>{{ formatType(queryParams.typeId) }}</span>
                  <i :class="showChild ? 'el-icon-caret-bottom' : 'el-icon-caret-top'"></i>
                </div>
              </div>
              <div ref="standardLocationList" class="standard-location-box-list" v-show="showChild">
                <div class="list-item" :class="{ active: item.id == queryParams.typeId }" v-for="item in standardTypeInfo" :key="item.id" @click="handleTypeClick(item.id)">
                  <b>{{ item.code }}</b>
                  <span>{{ item.name }}</span>
                </div>
              </div>
              <div class="standard-location-text" v-if="searchName">
                <span>共为您找到</span>
                <span class="primary">“{{ searchName }}”</span>
                <span>的相关结果</span>
                <span class="primary">{{ total }}</span>
                <span>条</span>
              </div>
            </div>
            <div class="standard-list-item" v-for="(item, index) in standardList" :key="item.id">
              <el-image class="standard-list-item-img" :src="item.ossImage_oss || item.image" @click="handleDetail(item)"></el-image>
              <div class="flexCloumn">
                <div class="standard-list-item-title">
                  <b @click="handleDetail(item)">{{ item.code + ' ' + item.name }}</b>
                  <template v-if="activeName == 'collect'">
                    <el-tooltip effect="dark" content="取消置顶" placement="bottom" v-if="index == 0">
                      <i class="top-icon active pointer" @click="handleTop(item, index)"></i>
                    </el-tooltip>
                    <div class="top" @click="handleTop(item)" v-else>
                      <i class="top-icon"></i>
                      <span>置顶</span>
                    </div>
                    <el-tooltip effect="dark" content="取消常用" placement="bottom">
                      <i class="el-icon-star-on pointer" @click="handleCollect(item)"></i>
                    </el-tooltip>
                  </template>
                  <template v-if="activeName == 'product'">
                    <el-tooltip effect="dark" content="取消常用" placement="bottom" v-if="item.hasCollect">
                      <i class="el-icon-star-on pointer" @click="handleCollect(item)"></i>
                    </el-tooltip>
                    <div class="star" @click="handleCollect(item)" v-if="!item.hasCollect">
                      <i class="el-icon-star-off"></i>
                      <span>设为常用</span>
                    </div>
                  </template>
                </div>
                <div class="standard-list-item-desc">
                  <span>规格</span>
                  <b>{{ item.dameters }}</b>
                </div>
                <div class="standard-list-item-btn">
                  <div class="standard-list-item-btn-left">
                    <span @click="handleDetail(item)">产品图纸</span>
                    <span @click="handleDetail(item, 'spec')">规格参数</span>
                    <span @click="handleDetail(item, 'reference')">引用标准</span>
                  </div>
                  <div class="standard-list-item-btn-right" @click="handleDetail(item)">
                    <span>查看详情</span>
                    <i class="el-icon-right"></i>
                  </div>
                </div>
              </div>
            </div>
            <!-- 没有数据 -->
            <el-empty v-if="total == 0 && !loading" description="暂无数据" />
            <!-- 分页 -->
            <div class="custom-pagination" v-if="total > 0 && !loading">
              <pagination :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="handleGetStandardList" v-if="activeName == 'product'" />
              <pagination :total="total" :page.sync="collectQuery.pageNum" :limit.sync="collectQuery.pageSize" @pagination="handleGetCollectList" v-else />
            </div>
          </div>
        </div>
      </div>
    </div>
    <foot-tpl />
  </div>
</template>

<script>
import { getToken } from '@/utils/auth'
import headTpl from '@/views/public/components/head'
import footTpl from '@/views/public/components/foot'
import { getStandardCategory, getStandardClassify, getStandardList, getStandardCollect } from '@/api/standard'
import { getlist, getlistb, shoucTo, delshouc, shoucGo } from '@/api/houtai/shoucang'

export default {
  components: { headTpl, footTpl },
  data() {
    return {
      isLogin: false,
      activeName: 'product',
      showChild: false,
      searchName: undefined,
      serachKeyword: undefined,
      searchType: 'code',
      standardCategory: [], // 标准产品分类
      standardTypeInfo: [], // 标准分类
      standardProperties: [], // 标准筛选（未使用）
      loading: true,
      standardList: [], // 标准列表
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: undefined,
        code: undefined,
        categoryId: undefined, // 产品分类id
        typeId: undefined // 标准分类id
      },
      collectQuery: {
        pageNum: 1,
        pageSize: 10,
        keyword: undefined,
        storeId: undefined
      },
      key: 1
    }
  },
  async created() {
    this.isLogin = !!getToken()
    this.handleGetCollectStandard()
    this.handleGetStandardCategory()
    this.handleGetStandardClassify()
    const { categoryId, typeId, name, type, searchType } = this.$route.query
    this.activeName = type || 'product'
    this.queryParams.categoryId = categoryId
    this.queryParams.typeId = typeId
    this.searchName = name
    this.serachKeyword = name
    this.searchType = searchType || 'code'
    this.$nextTick(() => {
      if (this.activeName == 'product') this.handleGetStandardList()
    })
  },
  methods: {
    // 获取标准产品分类
    handleGetStandardCategory() {
      getStandardCategory().then(res => {
        const { code, data } = res
        if (code === 200) this.standardCategory = data
      })
    },
    // 获取标准分类
    handleGetStandardClassify() {
      getStandardClassify().then(res => {
        const { code, data } = res
        if (code === 200) {
          const { standardTypeInfo, standardProperties } = data
          this.standardTypeInfo = standardTypeInfo
          this.standardProperties = standardProperties
        }
      })
    },
    // 获取标准列表
    // prettier-ignore
    handleGetStandardList() {
      this.activeName = 'product'
      this.loading = true
      if (this.searchType == 'code') {
        this.queryParams.name = undefined
        this.queryParams.code = this.serachKeyword
      } else {
        this.queryParams.name = this.serachKeyword
        this.queryParams.code = undefined
      }
      getStandardList(this.queryParams).then(async res => {
        const { code, msg, rows, total } = res
        if (code === 200) {
          if (rows.length) {
            await Promise.all(
              rows.map(async item => {
                const collect = await getStandardCollect({ standardId: item.id })
                if (collect.code === 200) this.$set(item, 'hasCollect', collect.data)
              })
            )
          }
          this.standardList = rows
          this.total = total
        } else this.$message.error(msg)
      }).finally(() => {
        this.loading = false
      })
    },
    // 格式化标准分类
    formatType(id) {
      const item = this.standardTypeInfo.find(item => item.id == id)
      return item ? item.name + item.code : '未选择'
    },
    // 查询收藏的标准收藏夹ID
    handleGetCollectStandard() {
      const type = 'fastenStandard'
      getlist({ type }).then(res => {
        const { code, data } = res
        if (code === 200) {
          this.collectQuery.storeId = data[0].storeId
          if (this.activeName == 'collect') this.handleGetCollectList()
        }
      })
    },
    // 搜索
    handleQuery() {
      if (this.activeName == 'product') {
        this.queryParams.pageNum = 1
        this.searchName = this.queryParams.name
        let query = {}
        if (this.queryParams.categoryId) query.categoryId = this.queryParams.categoryId
        if (this.queryParams.typeId) query.typeId = this.queryParams.typeId
        query.name = this.serachKeyword
        query.searchType = this.searchType
        this.$router.push({ query })
        this.handleGetStandardList()
      } else {
        this.searchName = this.collectQuery.keyword
        this.collectQuery.pageNum = 1
        this.searchType = 'code'
        this.serachKeyword = undefined
        let query = { type: this.activeName }
        if (this.collectQuery.keyword) query.keyword = this.collectQuery.keyword
        this.$router.push({ query })
        this.handleGetCollectList()
      }
    },
    // 切换产品分类
    handleCategoryClick(id) {
      this.showChild = false
      this.queryParams.categoryId = id
      let query = {}
      if (this.queryParams.typeId) query.typeId = this.queryParams.typeId
      query.name = this.serachKeyword
      query.searchType = this.searchType
      if (id) query.categoryId = id
      this.$router.push({ query })
      this.queryParams.pageNum = 1
      this.handleGetStandardList()
    },
    // 切换标准类型
    handleTypeClick(id) {
      this.showChild = false
      this.queryParams.typeId = id
      let query = {}
      if (this.queryParams.categoryId) query.categoryId = this.queryParams.categoryId
      query.name = this.serachKeyword
      query.searchType = this.searchType
      if (id) query.typeId = id
      this.$router.push({ query })
      this.queryParams.pageNum = 1
      this.handleGetStandardList()
    },
    // 点击详情
    handleDetail(item, type = 'product') {
      const { id } = item
      this.$router.push({ path: `/standard/detail/${id}`, query: { type } })
    },
    // 收藏
    handleCollect(item) {
      const { hasCollect, id } = item
      const { storeId } = this.collectQuery
      if (hasCollect) {
        delshouc({ storeId, valueId: id }).then(res => {
          const { code, msg } = res
          if (code === 200) {
            if (this.activeName == 'product') this.$set(item, 'hasCollect', false)
            else this.handleRefreshCollectList()
          } else this.$message.error(msg)
        })
      } else {
        shoucTo({ storeId, valueIdList: [id] }).then(res => {
          const { code, msg } = res
          if (code === 200) this.$set(item, 'hasCollect', true)
          else this.$message.error(msg)
        })
      }
    },
    // 获取收藏列表
    // prettier-ignore
    handleGetCollectList() {
      this.loading = true
      getlistb(this.collectQuery).then(res => {
        const { code, msg, rows, total } = res
        if (code === 200) {
          rows.forEach(item => {
            item.hasCollect = true
          })
          this.standardList = rows
          this.total = total
        } else this.$message.error(msg)
      }).finally(() => {
        this.loading = false
      })
    },
    // 刷新收藏列表
    handleRefreshCollectList() {
      getlistb(this.collectQuery).then(res => {
        const { code, msg, rows, total } = res
        if (code === 200) {
          rows.forEach(item => {
            item.hasCollect = true
          })
          this.$set(this, 'standardList', rows)
          this.$set(this, 'total', total)
        } else this.$message.error(msg)
      })
    },
    // 切换tab
    handleClick(tab) {
      this.key = Math.random()
      const { name } = tab
      this.activeName = name
      this.queryParams.categoryId = undefined
      this.queryParams.typeId = undefined
      this.searchName = undefined
      this.serachKeyword = undefined
      this.searchType = 'code'
      if (name == 'collect') {
        this.collectQuery.pageNum = 1
        this.collectQuery.keyword = undefined
        this.$router.push({ query: { type: name } })
        this.handleGetCollectList()
      } else {
        this.queryParams.pageNum = 1
        this.queryParams.name = undefined
        this.$router.push({ query: {} })
        this.handleGetStandardList()
      }
    },
    // 置顶
    handleTop(item, index = this.total) {
      const { id } = item
      let data = { storeId: this.collectQuery.storeId, valueId: id, index }
      shoucGo(data).then(res => {
        const { code, msg } = res
        if (code === 200) this.handleRefreshCollectList()
        else this.$message.error(msg)
      })
    },
    // 点击显示
    handleLocationClick() {
      this.showChild = !this.showChild
      if (this.showChild) {
        this.$nextTick(() => {
          document.addEventListener('click', this.handleOutsideClick)
        })
      }
    },
    // 点击以外地方隐藏
    handleOutsideClick(e) {
      const locationList = this.$refs.standardLocationList
      const locationBox = e.target.closest('.standard-location-box-item')
      if (locationList && !locationList.contains(e.target) && !locationBox) {
        this.showChild = false
        document.removeEventListener('click', this.handleOutsideClick)
      }
    }
  },
  beforeDestroy() {
    document.removeEventListener('click', this.handleOutsideClick)
  }
}
</script>
<style lang="scss" scoped>
@import '~@/assets/styles/standard.scss';
::v-deep .el-scrollbar__bar.is-horizontal {
  display: none;
}
</style>
