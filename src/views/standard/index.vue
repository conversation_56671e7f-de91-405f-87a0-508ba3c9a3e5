<template>
  <div>
    <head-tpl :is-login="isLogin" />
    <div class="standard-box standard-bg">
      <div class="standard-container">
        <div class="standard-header standard-header-bg">
          <el-tabs v-model="activeName" @tab-click="handleClick" class="standard-header-tabs">
            <el-tab-pane label="产品标准" name="product"></el-tab-pane>
            <el-tab-pane label="常用标准" name="collect"></el-tab-pane>
          </el-tabs>
          <div class="standard-header-search">
            <el-select v-model="searchType" placeholder="请选择" size="small">
              <el-option label="按标准" value="code"></el-option>
              <el-option label="按名称" value="name"></el-option>
            </el-select>
            <el-input v-model="search" size="small" :placeholder="searchType == 'name' ? '请输入名称' : '请输入标准'" @keyup.enter.native="handleSearch" />
            <el-button icon="el-icon-search" size="small" @click="handleSearch">搜索</el-button>
          </div>
        </div>
        <div class="standard-content">
          <el-scrollbar wrap-style="overflow-x:hidden;">
            <div class="standard-content-left standard-content-left-height">
              <el-menu default-active="1-1" unique-opened>
                <el-submenu :index="item.id + ''" v-for="item in standardCategory" :key="item.id">
                  <span slot="title">{{ item.name }}</span>
                  <el-menu-item-group>
                    <el-menu-item :index="ite.id + ''" v-for="ite in item.children" :key="ite.id" @click="handleCategoryClick(ite.id)">
                      <span>{{ ite.name }}</span>
                    </el-menu-item>
                  </el-menu-item-group>
                </el-submenu>
              </el-menu>
            </div>
          </el-scrollbar>
          <div class="standard-content-right">
            <div class="standard-content-right-item" v-for="item in standardTypeInfo" :key="item.id" @click="handleTypeClick(item.id)">
              <b>{{ item.code }}</b>
              <span>{{ item.name }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <foot-tpl />
  </div>
</template>

<script>
import { getToken } from '@/utils/auth'
import headTpl from '@/views/public/components/head'
import footTpl from '@/views/public/components/foot'
import { getStandardCategory, getStandardClassify } from '@/api/standard'

export default {
  components: { headTpl, footTpl },
  data() {
    return {
      isLogin: false,
      activeName: 'product',
      activeNavId: '1',
      searchType: 'code',
      search: '',
      standardCategory: [], // 标准产品分类
      standardTypeInfo: [], // 标准分类
      standardProperties: [], // 标准筛选（未使用）
      loading: ''
    }
  },
  created() {
    this.isLogin = !!getToken()
    this.loading = this.$loading({
      lock: true,
      text: '加载中...',
      background: 'rgba(0, 0, 0, 0.7)'
    })
    this.handleGetStandardCategory()
  },
  methods: {
    // 切换tab
    handleClick(tab) {
      const { name } = tab
      this.activeName = name
      if (name === 'collect') this.$router.push({ name: 'StandardList', query: { type: 'collect' } })
    },
    // 获取标准产品分类
    handleGetStandardCategory() {
      getStandardCategory().then(res => {
        const { code, data } = res
        if (code === 200) {
          this.standardCategory = data
          this.handleGetStandardClassify()
        }
      })
    },
    // 获取标准分类
    handleGetStandardClassify() {
      getStandardClassify()
        .then(res => {
          const { code, data } = res
          if (code === 200) {
            const { standardTypeInfo, standardProperties } = data
            this.standardTypeInfo = standardTypeInfo
            this.standardProperties = standardProperties
          }
        })
        .finally(() => {
          this.loading.close()
        })
    },
    // 切换标准分类
    handleCategoryClick(id) {
      this.$router.push({ name: 'StandardList', query: { categoryId: id } })
    },
    // 切换标准类型
    handleTypeClick(id) {
      this.$router.push({ name: 'StandardList', query: { typeId: id } })
    },
    // 搜索
    handleSearch() {
      this.$router.push({ name: 'StandardList', query: { name: this.search, searchType: this.searchType } })
    }
  }
}
</script>
<style lang="scss" scoped>
@import '~@/assets/styles/standard.scss';
::v-deep .el-scrollbar__bar.is-horizontal {
  display: none;
}
</style>
