<template>
  <div>
    <head-tpl :is-login="isLogin" />
    <div class="standard-box">
      <div class="standard-container">
        <div class="standard-header">
          <div class="standard-header-location">
            <span @click="handleList">查标准</span>
            <span>标准详情</span>
          </div>
          <div class="standard-header-search">
            <el-select v-model="searchType" placeholder="请选择" size="small">
              <el-option label="按标准" value="code"></el-option>
              <el-option label="按名称" value="name"></el-option>
            </el-select>
            <el-input v-model="search" size="small" :placeholder="searchType == 'name' ? '请输入名称' : '请输入标准'" @keyup.enter.native="handleSearch" />
            <el-button icon="el-icon-search" size="small" @click="handleSearch">搜索</el-button>
          </div>
        </div>
        <div class="standard-detail-title">{{ (info.code || '') + ' ' + (info.name || '') }}</div>
        <div class="standard-detail-desc" v-if="info.code">
          <span>产品标准：</span>
          <b>{{ info.code }}</b>
        </div>
        <div class="standard-detail-tabs" v-if="activeOptions.length > 0">
          <span class="item" :class="{ active: active === item.value }" v-for="item in activeOptions" :key="item.value" @click="handleChange(item)">{{ item.label }}</span>
        </div>
        <div class="standard-detail-content" v-if="activeOptions.length > 0">
          <div class="standard-drawing" v-if="active === 'product'">
            <div class="standard-drawing-title" v-if="modelOptions.length > 0">
              <div class="standard-drawing-title-select">
                <span class="tip">产品直径</span>
                <el-select v-model="model" placeholder="请选择" @change="handleModelChange">
                  <el-option v-for="(item, index) in modelOptions" :key="index" :label="item" :value="item"></el-option>
                </el-select>
              </div>
              <span class="standard-drawing-title-tip">单位：毫米(mm)</span>
            </div>
            <div class="standard-drawing-content" v-html="updatedSvg"></div>
          </div>
          <div class="standard-html" ref="spec" @click="handleSpecItemClick" v-html="spec" v-if="active === 'spec'"></div>
          <div class="standard-html" ref="replace" v-html="replace" v-if="active === 'replace'"></div>
          <div class="standard-html" ref="condition" v-html="condition" v-if="active === 'condition'"></div>
          <div class="standard-html" ref="reference" v-html="reference" v-if="active === 'reference'"></div>
        </div>
      </div>
    </div>
    <foot-tpl />
  </div>
</template>

<script>
import { getToken } from '@/utils/auth'
import headTpl from '@/views/public/components/head'
import footTpl from '@/views/public/components/foot'
import { getStandardDetail } from '@/api/standard'

export default {
  components: { headTpl, footTpl },
  data() {
    return {
      isLogin: false,
      search: '',
      searchType: 'code',
      info: {}, // 标准详情
      model: '', // 选中的下拉
      modelOptions: [], // 下拉配置
      modelAll: [], // 全部下拉项
      svgData: {}, // 图纸参数
      svg: '', // 产品图纸源
      updatedSvg: '', // 更新后的图纸
      active: '', // 当前选中的选项
      activeOptions: [
        // { label: '产品图纸', value: 'product' },
        // { label: '规格参数', value: 'spec' },
        // { label: '替代标准', value: 'replace' },
        // { label: '技术条件和引入标准', value: 'condition' },
        // { label: '引用标准', value: 'reference' }
      ],
      spec: '', // 规格参数
      replace: '', // 替代标准
      condition: '', // 技术条件和引入标准
      reference: '', // 引用标准
      standardId: '', // 标准ID
      type: '' // 传参选中的选项
    }
  },
  created() {
    this.isLogin = !!getToken()
    const { type } = this.$route.query
    if (type) this.type = type
    const { id } = this.$route.params
    if (!id) {
      this.$alert('参数错误，请联系管理员', '系统提示', {
        type: 'error',
        confirmButtonText: '确定',
        callback: action => {
          this.$router.push('/standard')
        }
      })
      return
    }
    this.standardId = id
    this.getInfo()
  },
  methods: {
    compressHTML(html) {
      if (!html) return ''
      html = html.replace(/data-v-[a-zA-Z0-9]+/g, '')
      // 去除多余的空白字符
      html = html.replace(/>\s+</g, '><')
      html = html.replace(/\s+/g, ' ')
      // 去除多余的空格（如果它们在开始或结束位置，或者紧邻标签）
      html = html.replace(/\s*<\s*/g, '<')
      html = html.replace(/\s*>\s*/g, '>')
      // 去除注释
      html = html.replace(/<!--.*?-->/g, '')
      // （可选）去除不必要的属性，比如多余的class、id等
      // html = html.replace(/class="".*?"""/g, ''); // 根据需要自定义
      // 最后，去除多余的空格（如果它们在标签内部并且不是必要的）
      // 注意：这一步可能会影响到某些依赖空格的inline样式或脚本，所以请谨慎使用
      // html = html.replace(/>\s+</g, '><');
      return html.trim()
    },
    // 查询数据
    // prettier-ignore
    getInfo() {
      const loading = this.$loading({
        lock: true,
        text: '加载中',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      getStandardDetail({ id: this.standardId }).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          this.info = data
          const { conditionAndStandard, referenceStandard, replaceStandard, specTable, svg } = data
          if (svg) this.activeOptions.push({ label: '产品图纸', value: 'product' })
          if (specTable) this.activeOptions.push({ label: '规格参数', value: 'spec' })
          if (replaceStandard) this.activeOptions.push({ label: '替代标准', value: 'replace' })
          if (conditionAndStandard) this.activeOptions.push({ label: '技术条件和引入标准', value: 'condition' })
          if (referenceStandard) this.activeOptions.push({ label: '引用标准', value: 'reference' })
          if (this.type) this.active = this.type
          else this.active = this.activeOptions[0].value

          this.spec = this.compressHTML(JSON.parse(JSON.stringify(specTable)))
          this.replace = this.compressHTML(JSON.parse(JSON.stringify(replaceStandard)))
          this.condition = this.compressHTML(JSON.parse(JSON.stringify(conditionAndStandard)))
          this.reference = this.compressHTML(JSON.parse(JSON.stringify(referenceStandard)))
          this.svg = this.compressHTML(JSON.parse(JSON.stringify(svg)))
          this.replace = this.replace.replace(/https:\/\/standard\.fastencloud\.com\/detail-(\d+)\.html/g, '/standard/detail/$1')
          if(this.spec) this.handleGetSpec()
          else this.handleGetSvg()
        } else this.$message.error(msg)
      }).finally(() => {
        loading.close()
      })
    },
    // 切换产品图纸、规格参数、替代标准、技术条件和引入标准、引用标准
    handleChange(item) {
      const { value } = item
      this.active = value
      this.$router.push({ query: { type: value } })
    },
    // 获取所有规格参数
    handleGetSpec() {
      const leftBox = this.spec.match(/<div[^>]*class="left-box"[^>]*>(.*?)<\/div><\/div>/)[0]
      const itemNames = leftBox.match(/<div[^>]*class="item-name"[^>]*>(.*?)<\/div>/g)
      const jsonResult = {}
      itemNames.forEach(item => {
        const key = item.match(/>([^<]+)</)[1].trim()
        jsonResult[key] = ''
      })
      this.svgData = jsonResult

      const rightBoxes = this.spec.match(/<div[^>]*class="right-box"[^>]*>.*?<\/div><\/div>/g)
      const options = rightBoxes.map(item => this.formateSpec(item))
      this.modelOptions = [...new Set(options)]
      this.modelAll = rightBoxes
      this.model = this.modelOptions[0]
      this.handleModelChange(this.model)
    },
    // 格式化规格参数
    formateSpec(spec) {
      const title = spec.match(/<div[^>]*class="item-name item-title"[^>]*>(.*?)<\/div>/)[1].trim()
      return title
    },
    // 改变产品直径
    handleModelChange(value) {
      this.model = value
      const result = this.modelAll.find(item => item.includes(`class="item-name item-title">${value}`))
      const itemNames = result.match(/<div[^>]*class="item-name item-name"[^>]*>(.*?)<\/div>/g)
      let svgData = Object.keys(this.svgData)
      svgData.forEach((key, index) => {
        svgData[key] = itemNames[index].match(/>([^<]+)</)[1].trim()
      })
      const hasValues = Object.values(svgData).some(value => value !== '')
      if (hasValues) {
        svgData.d = value
        this.handleGetSvg(svgData)
      }
    },
    // 切换svg
    handleGetSvg(obj = {}) {
      let updatedSvg = JSON.parse(JSON.stringify(this.svg))
      Object.keys(obj).forEach(key => {
        const regex = new RegExp(`${key}=\\d+\\.?\\d*`, 'g')
        updatedSvg = updatedSvg.replace(regex, `${key}=${obj[key]}`)
      })
      updatedSvg = updatedSvg.replace(/(max|min)=(88888|8888|888)/g, '')
      updatedSvg = updatedSvg.replace(/[a-zA-Z0-9]=(88888|8888|888)/g, '')
      updatedSvg = updatedSvg.replace(/d=88888/g, 'd')
      this.updatedSvg = updatedSvg
    },
    // 规格参数存在多个时切换
    handleSpecItemClick(event) {
      // 处理规格参数 A/B 级切换
      if (event.target.classList.contains('item')) {
        const items = this.$refs.spec.querySelectorAll('.model-box .item')
        const tableList = this.$refs.spec.querySelectorAll('.standard-table-list')
        items.forEach(item => {
          item.classList.remove('spec-active')
        })
        event.target.classList.add('spec-active')
        tableList.forEach(item => {
          item.style.display = item.style.display === 'none' ? 'block' : 'none'
        })
      }
      // 处理点击规格参数右侧内容
      if (event.target.classList.contains('item-title')) {
        const currentRightBox = event.target.closest('.right-box')
        const rightBoxes = this.$refs.spec.querySelectorAll('.right-box')
        rightBoxes.forEach(box => {
          box.classList.remove('backBox')
        })
        currentRightBox.classList.add('backBox')
        const itemTitle = currentRightBox.querySelector('.item-name.item-title').textContent.trim()
        this.handleModelChange(itemTitle)
      }
    },
    // 搜索
    handleSearch() {
      this.$router.push({ name: 'StandardList', query: { name: this.search, searchType: this.searchType } })
    },
    // 返回列表
    handleList() {
      this.$router.push('/standard/list')
    }
  }
}
</script>
<style lang="scss" scoped>
@import '~@/assets/styles/standard.scss';
::v-deep .el-scrollbar__bar.is-horizontal {
  display: none;
}
</style>
