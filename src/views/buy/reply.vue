<template>
  <div>
    <el-dialog v-dialogDragBox title="查看回复" :visible.sync="offerOpen" width="1150px" class="custom-dialog offer-dialog">
      <div style="padding: 0 20px">
        <el-table ref="list" stripe :data="list" row-key="id" style="width: 100%" class="custom-table">
          <el-table-column align="center" label="供应商" show-overflow-tooltip min-width="130">
            <template slot-scope="{ row }">
              <span class="table-link" @click="handleViewSupplier(row)">
                {{ '(公域)' + row.supplier.name }}
              </span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="replyUser" label="业务员" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="phone" label="业务员电话" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="amount" label="产品报价" show-overflow-tooltip>
            <template slot-scope="{ row }">
              <span class="table-orange">{{ row.amount ? '￥' + row.amount : '' }}{{ '元' + (row.replyUnit ? '/' : '') + (row.replyUnit || '') }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="replyTime" label="报价时间" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="remark" label="报价备注" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" label="操作">
            <template slot-scope="{ row }">
              <button type="button" class="table-btn small primary" @click="handleContarct(row)">生成合同</button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div slot="footer">
        <button type="button" class="custom-dialog-btn primary" @click="offerOpen = false">关闭</button>
      </div>
    </el-dialog>
    <!-- 供应商信息 -->
    <supplier-dialog ref="supplier"></supplier-dialog>
    <!--  生成合同  -->
    <offer-contract-dialog ref="contractDialog"></offer-contract-dialog>
  </div>
</template>
<script>
import { getBuyReplyList } from '@/api/buy'
import supplierDialog from '@/views/purchase/demandForMe/supplier'
import offerContractDialog from '@/views/purchase/demandForMe/offer'

export default {
  components: { offerContractDialog, supplierDialog },
  data() {
    return {
      offerOpen: false,
      list: [],
      info: {}
    }
  },
  created() {},
  methods: {
    handleView(row = {}) {
      this.info = row
      const { buyingLeadId } = row
      getBuyReplyList({ leadsId:buyingLeadId }).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          if (data.length === 0) {
            this.$message.warning('暂无报价')
            return
          }
          this.list = data
          this.offerOpen = true
        } else this.$message.error(msg)
      })
    },
    // 查看供应商详情
    handleViewSupplier(row) {
      const id = row.supplier.id
      this.$refs.supplier.getInfo(id, 'common')
    },
    // 生成合同
    handleContarct(row) {
      const info = {
        maxNum: 999999999,
        source: 'common',
        productId: this.info.product.id,
        productName: this.info.product.productName,
        productCode: this.info.product.productCode,
        specs: this.info.product.specs,
        model: this.info.product.model,
        unit: this.info.product.unit,
        quantity: 0,
        needQuantity: 0,
        listId: -1,
        remark: row.remark,
        amount: row.amount || 0,
        replyUnit: row.replyUnit || this.info.product.unit,
        sjNum: 0,
        replyRemark: row.remark || ''
      }
      const sellerUser = { ...row.supplier, ...{ companyId: row.supplier.id } }
      this.$refs.contractDialog.handleGetContract(sellerUser, info)
    }
  }
}
</script>

<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';
</style>
