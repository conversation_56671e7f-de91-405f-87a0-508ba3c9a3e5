<template>
  <div class="newBox bgcf9 vh-85">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" @submit.native.prevent>
      <el-form-item label="标题名称" prop="title">
        <el-input v-model="queryParams.title" placeholder="请输入标题名称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" @change="handleQuery">
          <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="是否过滤无效" prop="filterExpire">
        <el-select v-model="queryParams.filterExpire" placeholder="请选择" @change="handleQuery">
          <el-option v-for="item in filterExpireOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
        <el-button type="primary" icon="el-icon-plus" @click="addOpen = true" v-if="hasPermission">新增采购</el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" ref="table" stripe :data="list" row-key="id" style="width: 100%" class="custom-table custom-table-cell5" v-if="total > 0">
      <el-table-column align="center" type="index" label="序号"></el-table-column>
      <el-table-column align="center" prop="title" label="标题名称" show-overflow-tooltip></el-table-column>
      <el-table-column align="center" prop="detail" label="描述详细" show-overflow-tooltip></el-table-column>
      <el-table-column align="center" prop="status" label="状态" show-overflow-tooltip></el-table-column>
      <el-table-column align="center" prop="type" label="产品类型" show-overflow-tooltip></el-table-column>
      <el-table-column align="center" prop="effectiveTime" label="截止时间"></el-table-column>
      <el-table-column align="center" prop="createTime" label="创建时间" show-overflow-tooltip></el-table-column>
      <el-table-column align="center" label="操作" min-width="120">
        <template slot-scope="{ row }">
          <button type="button" class="table-btn small danger" @click="handleDelte(row)">删除</button>
          <button type="button" class="table-btn small primary" @click="handleDetail(row)">查看</button>
          <button type="button" class="table-btn small orange" @click="handleOff(row)" v-hasPermi="['buying:leads:all']">下架</button>
        </template>
      </el-table-column>
    </el-table>
    <el-empty v-if="!loading && total === 0" />

    <div class="custom-pagination">
      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
    </div>

    <!-- 产品详情 -->
    <product-dialog ref="productInfo"></product-dialog>
    <!--  查看报价  -->
    <reply-dialog ref="buyReply"></reply-dialog>

    <!-- 新增采购   -->
    <el-dialog v-dialogDragBox title="新增采购" :visible.sync="addOpen" width="1150px" class="custom-dialog">
      <div style="margin: -20px -20px 0">
        <product-center :showMore="false" @refresh="handleRefresh" @close="handleClose" ref="productCenter" />
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn primary" @click="addOpen = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { getBuyList, deleteBuy, offBuy } from '@/api/buy'
import ProductDialog from '@/views/public/product/dialog'
import { supplier, userBrandList } from '@/api/system/user'
import replyDialog from './reply'
import productCenter from '@/views/platform/product/allList'

export default {
  components: { ProductDialog, replyDialog, productCenter },
  data() {
    return {
      loading: true,
      list: [],
      total: 0,
      queryParams: {
        pageSize: 10,
        pageNum: 1,
        title: undefined,
        status: 0,
        filterExpire: true
      },
      statusOptions: [
        { label: '求购中', value: 0 },
        { label: '已完成', value: 1 },
        { label: '已关闭', value: -10 }
      ],
      filterExpireOptions: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
      timeUnitOptions: [
        { label: '天', value: 'day' },
        { label: '周', value: 'week' },
        { label: '月', value: 'month' },
        { label: '季', value: 'quarter' },
        { label: '年', value: 'year' }
      ],
      hasPermission: false,
      addOpen: false
    }
  },
  created() {
    this.getList()
    this.checkPermission()
  },
  methods: {
    // 检查权限
    checkPermission() {
      const companyId = this.$store.state.user.companyId
      if (companyId === -1) {
        this.hasPermission = false
      } else {
        supplier({ id: companyId }).then(res => {
          this.hasPermission = res.data.company.level == 1
        })
      }
    },
    // 格式化采购量
    quantityFormat(row) {
      const { quantity, unit, timeUnit } = row
      const time = this.timeUnitOptions.find(item => item.value === timeUnit) || { label: '' }
      return `${quantity}${unit}/${time.label}`
    },
    // 采购列表
    getList() {
      const loading = this.$loading({
        lock: true,
        text: '加载中',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      this.loading = true
      getBuyList(this.queryParams).then(res => {
        const { code, msg, rows, total } = res
        if (code === 200) {
          this.loading = false
          rows.forEach(item => {
            item.buyingLeadId = item.id || ''
          })
          this.list = rows
          this.total = total
        } else this.$message.error(msg)
      }).finally(() => {
        loading.close()
      })
    },
    // 刷新
    handleRefresh() {
      getBuyList(this.queryParams).then(res => {
        const { code, msg, rows, total } = res
        if (code === 200) {
          rows.forEach(item => {
            item.buyingLeadId = item.id || ''
          })
          this.$set(this, 'list', rows)
          this.$set(this, 'total', total)
        } else this.$message.error(msg)
      })
    },
    // 查看详情
    handleView(item, val) {
      this.$refs.productInfo.handleView(item, val)
    },
    // 图片预览
    handleImg(row) {
      this.$refs.productInfo.handleImgView(row)
    },
    // 查询
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 重置
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 删除采购
    // prettier-ignore
    handleDelte(row) {
      const { buyingLeadId } = row
      this.$confirm('是否删除该采购？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteBuy({ buyingLeadId }).then(res => {
          const { code, msg } = res
          if (code === 200) {
            this.$message.success('删除成功')
            this.handleRefresh()
          } else this.$message.error(msg)
        })
      })
    },
    // 下架
    // prettier-ignore
    handleOff(row) {
      const { id } = row
      this.$confirm('是否下架该采购？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        offBuy({ id }).then(res => {
          const { code, msg } = res
          if (code === 200) {
            this.$message.success('下架成功')
            this.handleRefresh()
          } else this.$message.error(msg)
        })
      })
    },
    // 查看回复
    handleDetail(row) {
      this.$refs.buyReply.handleView(row)
    },
    // 关闭窗口
    handleClose() {
      this.addOpen = false
    }
  }
}
</script>
<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
.newBox {
  padding: 20px 20px 0;
}
</style>
