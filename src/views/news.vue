<template>
  <div>
    <head-tpl :is-login="islogin"></head-tpl>
    <div class="box mb20">
      <div class="chart-type">
        <span :class="{ active: chartActive === 0 }" @click="handleClickLine(0)">现货</span>
        <span :class="{ active: chartActive === 1 }" @click="handleClickLine(1)">期货</span>
      </div>
      <div class="chart-container">
        <el-scrollbar wrap-class="chart-scrollbar">
          <div class="chart-box" v-if="chartActive === 0">
            <div class="chart-box-item" v-for="item in chartData.seriesData" :key="item.id" @click="changeKline(item)">
              <i class="item-checkbox checked" v-if="item.show"></i>
              <i class="item-checkbox" v-if="!item.show"></i>
              <span>{{ item.name }}</span>
            </div>
          </div>
          <div class="chart-box" v-if="chartActive === 1">
            <div class="chart-box-item" v-for="(item, index) in blackOptions" :key="index" @click="changeKline(item)">
              <i class="el-icon-success" v-if="blackActive === item"></i>
              <i class="circle" v-else></i>
              <span>{{ item }}</span>
            </div>
          </div>
        </el-scrollbar>
        <div class="chart-right" v-loading="chartLoading">
          <k-line-charts :chart-data="chartData" :is-zoom="isZoom" is-index height="350px" v-if="chartActive === 0 && showChart" />
          <k-line-chartn :chart-data="chartData" :is-zoom="isZoom" height="350px" v-if="chartActive === 1 && showChart" />
        </div>
      </div>
    </div>
    <div class="box" v-loading="newsLoading">
      <div class="news-type">
        <span :class="{ active: queryParams.newsTypeId === item.id }" v-for="item in newsType" :key="item.id" @click="handleChageType(item)">{{ item.name }}</span>
      </div>
      <div class="news-item" v-for="item in newsList" :key="item.id" @click="tonew(item)">
        <div class="news-item-left">
          <div class="news-item-title">
            <span v-if="isTop(item)">置顶</span>
            <b>{{ item.title }}</b>
          </div>
          <div class="news-item-desc">{{ item.summary !== '无' ? removeHtmlTag(item.summary) : removeHtmlTag(item.content, 200) }}</div>
        </div>
        <div class="news-item-right">
          <b>{{ parseTime(item.publishTime,'{d}') }}</b>
          <span>{{ parseTime(item.publishTime,'{y}-{m}') }}</span>
        </div>
      </div>
      <div class="product-more">
        <div class="product-more-btn" @click="handleMore">
          <template v-if="isMore">
            <i class="ssfont ss-diy-biaoqing"></i>
            我是有底线的
          </template>
          <template v-else>
            <i :class="moreLoading ? 'el-icon-loading' : 'el-icon-refresh'"></i>
            加载更多
          </template>
        </div>
      </div>
    </div>
    <foot-tpl />
  </div>
</template>
<script>
import { getToken } from '@/utils/auth'
import KLineChart from '@/views/dashboard/KLineChart'
import headTpl from '@/views/public/components/head'
import footTpl from '@/views/public/components/foot'
import { removeHtmlTag } from '@/utils'
import { getArticleHomeList, getArticleTypeList } from '@/api/article'
import KLineCharts from '@/views/dashboard/KLineCharts'
import KLineChartn from '@/views/dashboard/KLineChartn'
import { getHomeKline, getMetalMarket } from '@/api/kline'

export default {
  components: { KLineChartn, KLineCharts, KLineChart, headTpl, footTpl },
  data() {
    return {
      islogin: false,
      newsLoading: true,
      newsType: [],
      newsList: [],
      total: 0,
      queryParams: {
        newsTypeId: undefined,
        pageNum: 1,
        pageSize: 10
      },
      isMore: false,
      moreLoading: false,
      chartActive: 0,
      chartData: { xAxis: [], min: 0, max: 0, color: '', seriesData: [] },
      blackOptions: ['螺纹钢', '热轧卷板', '不锈钢', '焦炭', '铁矿石', '硅铁', '锰硅', '线材'],
      blackActive: undefined,
      isZoom: false,
      showChart: true,
      chartLoading: true
    }
  },
  created() {
    this.islogin = !!getToken()
    this.getNewsType()
    this.getKlineList()
  },
  methods: {
    removeHtmlTag,
    // 获取新闻类型
    getNewsType() {
      getArticleTypeList().then(res => {
        const { code, rows, msg } = res
        if (code === 200) {
          rows.sort((a, b) => a.sort - b.sort || a.id - b.id)
          this.newsType = rows
          if (rows.length) {
            this.queryParams.newsTypeId = rows[0].id
            this.getNewsList()
          }
        } else {
          this.$message.error(msg)
        }
      })
    },
    // 获取新闻列表
    getNewsList() {
      this.newsLoading = true
      getArticleHomeList(this.queryParams).then(res => {
        const { code, rows, total, msg } = res
        if (code === 200) {
          rows.map(item => {
            item.publishTime = item.publishTime || item.createTime
          })
          this.newsList = rows
          this.total = total
          this.isMore = false
          this.moreLoading = false
          this.newsLoading = false
          setTimeout(() => {
            this.isMore = this.queryParams.pageNum * this.queryParams.pageSize >= this.total
          }, 1)
        } else this.$message.error(msg)
      })
    },
    // 改变新闻类型
    handleChageType(item) {
      this.queryParams.newsTypeId = item.id
      this.queryParams.pageNum = 1
      this.getNewsList()
    },
    // 是否置顶
    isTop(item) {
      const topTime = item.topTime || 0
      const now = new Date().getTime()
      return now < topTime
    },
    // 加载更多
    handleMore() {
      if (this.queryParams.pageNum * this.queryParams.pageSize >= this.total) return
      this.isMore = false
      this.queryParams.pageNum = ++this.queryParams.pageNum
      this.newsLoading = true
      this.moreLoading = true
      getArticleHomeList(this.queryParams).then(res => {
        const { code, msg, rows, total } = res
        if (code === 200) {
          rows.map(item => {
            item.publishTime = item.publishTime || item.createTime
          })
          this.newsList = this.newsList.concat(rows)
          this.total = total
          this.isMore = false
          this.moreLoading = false
          this.newsLoading = false
          setTimeout(() => {
            this.isMore = this.queryParams.pageNum * this.queryParams.pageSize >= this.total
          }, 1)
        } else this.$message.error(msg)
      })
    },
    // 跳转详情
    tonew(item) {
      this.$router.push({ path: '/new', query: { id: item.id } })
    },
    // 获取k线
    getKlineList() {
      getHomeKline({ limit: 9999 }).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          let xAxisData = []
          let seriesData = []
          let xAxis = []
          let min = 0
          let max = 0
          const colorArr = ['#37A2FF', '#00DDFF', '#80FFA5', '#FF0087', '#FFBF00']
          let showNum = 0
          data.map((item, idx) => {
            showNum += idx
            item.color = colorArr[idx]
            item.types.map(ite => {
              if (ite.id === 7) {
                // item.color = ite.color
                item.data = ite.steelPrices
                xAxisData = [...xAxisData, ...ite.steelPrices]
                xAxis = Array.from(new Set(xAxisData.map(item => item.date))) || []
              }
            })
            const price = xAxisData.map(item => item.price)
            min = Math.min(...price)
            max = Math.max(...price)
            if (item.data.length !== xAxis.length) {
              let data = []
              for (let i = 0; i < xAxis.length; i++) {
                data[i] = min
                item.data.map(ite => {
                  if (ite.date === xAxis[i]) data[i] = ite.price
                })
              }
              seriesData.push({ id: item.id, name: item.name, color: item.color, data, show: showNum === 0 })
            } else {
              seriesData.push({
                id: item.id,
                name: item.name,
                color: item.color,
                data: item.data.map(ite => ite.price),
                show: true
              })
            }
          })
          if (xAxis.length > 30) this.isZoom = true
          this.chartData.min = min - 100
          this.chartData.max = max + 100
          this.chartData.xAxis = xAxis
          this.chartData.seriesData = seriesData
          this.showChart = true
          this.chartLoading = false
        } else this.$message.error(msg)
      })
    },
    // 改变K线分类
    changeKline(row) {
      if (this.chartActive === 0) row.show = !row.show
      else {
        this.blackActive = row
        this.getBlackList(row)
      }
    },
    handleClickLine(val) {
      this.showChart = false
      this.chartLoading = true
      this.chartActive = val
      if (!!val) this.getBlackList()
      else this.getKlineList()
    },
    getBlackList(name = undefined) {
      const chnName = name || this.blackOptions[0]
      this.blackActive = chnName
      getMetalMarket({ chnName, limit: 9999 }).then(res => {
        const { code, data, msg } = res
        if (code === 200) {
          this.chartData.xAxis = data.map(item => item.dateTime)
          const dataValue = data.map(item => parseFloat(item.varietyValue || 0))
          this.chartData.min = Math.min(...dataValue) - 100
          this.chartData.max = Math.max(...dataValue) + 100
          this.chartData.seriesData = [{ name: chnName, data: dataValue }]
          this.showChart = true
          this.chartLoading = false
          if (data.length > 30) this.isZoom = true
        } else this.$message.error(msg)
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.mb20 {
  margin-bottom: 20px !important;
}
.box {
  width: 1200px;
  margin: 10px auto;
  box-shadow: 0 0 26px 0 rgba(0, 0, 0, 0.05);
  border-radius: 6px;
  overflow: hidden;
  .chart-type {
    display: flex;
    align-items: center;
    height: 50px;
    line-height: 50px;
    border-bottom: 1px solid #d8deea;
    span {
      display: inline-block;
      width: 70px;
      height: 100%;
      text-align: center;
      margin: 0 50px;
      font-size: 14px;
      color: #999999;
      cursor: pointer;
      position: relative;
      &:hover,
      &.active {
        color: #2e73f3;
        &:after {
          display: inline-block;
          content: '';
          width: 100%;
          height: 2px;
          background-color: #2e73f3;
          position: absolute;
          left: 0;
          bottom: -1px;
        }
      }
    }
  }
  .chart-container {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    padding: 15px 20px 20px;
    ::v-deep {
      .is-horizontal {
        display: none;
      }
      .chart-scrollbar {
        width: 225px !important;
        height: 365px !important;
      }
    }
    .chart-box {
      display: inline-flex;
      flex-direction: column;
      flex-shrink: 0;
      width: 100%;
      min-height: 345px;
      background-color: #f2f2f2;
      border-radius: 5px;
      margin-top: 5px;
      padding: 7px 15px;
      overflow-x: hidden;
      overflow-y: auto;
      &-item {
        display: inline-flex;
        align-items: center;
        font-size: 12px;
        line-height: 35px;
        color: #666666;
        cursor: pointer;
        i {
          font-size: 20px;
          margin-right: 5px;
        }
        i.item-checkbox {
          width: 16px;
          height: 16px;
          margin-right: 7px;
          margin-left: 2px;
          border-radius: 2px;
          border: 1px solid #2f74f3;
          &.checked {
            background: url('~@/assets/images/checkboxed.png') center no-repeat;
            background-size: 100% 100%;
            border: 0;
          }
        }
        i.el-icon-success {
          color: #2f74f3;
        }
        i.circle {
          display: inline-block;
          width: 16px;
          height: 16px;
          margin-right: 7px;
          margin-left: 2px;
          border-radius: 50%;
          border: 1px solid #2f74f3;
        }
      }
    }
    .chart-right {
      width: calc(100% - 205px);
    }
  }
  .news-type {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    span {
      font-size: 20px;
      line-height: 100px;
      margin-left: 30px;
      cursor: pointer;
      color: #666;
      &.active {
        font-size: 24px;
        color: #000;
      }
    }
  }
  .news-item {
    display: flex;
    align-items: center;
    justify-content: space-around;
    margin-bottom: 20px;
    cursor: pointer;
    &-left {
      width: 910px;
    }
    &-right {
      width: 180px;
      height: 130px;
      background-color: #f2f2f2;
      display: inline-flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      b {
        font-size: 38px;
        color: #999;
        font-weight: 700;
      }
      span {
        font-size: 16px;
        color: #999;
        margin-top: 15px;
      }
    }
    &-title {
      display: inline-flex;
      align-items: center;
      width: 100%;
      height: 25px;
      line-height: 25px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      span {
        font-size: 14px;
        line-height: normal;
        padding: 2px 8px;
        border-radius: 5px;
        border: 1px solid #ec2454;
        color: #ec2454;
        margin-right: 5px;
      }
      b {
        font-size: 18px;
        color: #000;
        font-weight: normal;
      }
    }
    &-desc {
      text-indent: 2em;
      font-size: 14px;
      color: #666;
      margin-top: 10px;
      line-height: 24px;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 3;
      -webkit-box-orient: vertical;
      white-space: normal;
    }
    &:hover {
      .news-item-title {
        b {
          color: #2e73f3;
        }
      }
    }
  }
}
</style>
