<template>
  <div>

    <div style="margin:20px auto;">
      <el-button type="primary" size="small" @click="addView = true">新增采购需求</el-button>
    </div>

    <!-- 弹窗 -->
    <el-dialog
      title="新增采购需求"
      :visible.sync="addView"
      width="80%"
    >

      <!-- 下拉窗口 -->
      <el-cascader
        v-model="selectedCategoryId"
        :options="categories"
        :props="props"
        placeholder="请选择"
        @change="handleCategoryChange"
        style="width:100%"
      />
      <!-- 产品列表 -->
      <div style="width:100%;margin-top:30px;">
        <el-row>
          <el-col
            v-for="product in products"
            :key="product.id"
            :span="3"
            :style="{ marginRight: '10px', marginBottom: '10px' }"
          >
            <div style="position: relative;">
              <img src="https://img2.baidu.com/it/u=1250551608,2180019998&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500"
                   style="width:80px;height:80px;">
              <el-checkbox
                v-model="product.selected"
                style="position: absolute; top: 5px; left: 5px;"
                @change="handleProductSelect(product)"
              ></el-checkbox>
              <div>{{ product.productName }}</div>
              <el-input
                v-model.number="product.count"
                type="number"
                :min="1"
                style="margin-top: 10px;width:80px;"
              ></el-input>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 表单 -->
      <div style="margin-top:20px;">
        <el-form label-width="80px">
          <el-form-item label="	标题">
            <el-input v-model="addData.title"></el-input>
          </el-form-item>
          <!-- <el-form-item label="	预算金额">
            <el-input v-model="addData.budget"></el-input>
          </el-form-item> -->
          <el-form-item label="开始时间">
            <el-col>
              <el-date-picker type="datetime" placeholder="选择日期" v-model="addData.startTime"
                              :value-format="'yyyy-MM-dd HH:mm:ss'" style="width: 100%;"></el-date-picker>
            </el-col>
          </el-form-item>
          <el-form-item label="结束时间">
            <el-col>
              <el-date-picker type="datetime" placeholder="选择日期" v-model="addData.endTime"
                              :value-format="'yyyy-MM-dd HH:mm:ss'" style="width: 100%;"></el-date-picker>
            </el-col>
          </el-form-item>
          <el-form-item label="	描述">
            <el-input v-model="addData.profile" type="textarea"></el-input>
          </el-form-item>
        </el-form>

      </div>

      <span slot="footer" class="dialog-footer">
          <el-button @click="addView = false">取 消</el-button>
          <el-button type="primary" @click="add()">确 定</el-button>
        </span>
    </el-dialog>


    <!-- 表格 -->
    <el-table
      :data="list"
      border
      style="width: 100%">
      <el-table-column
        prop="title"
        label="标题"
        width="180">
      </el-table-column>
      <el-table-column
        prop="status"
        label="状态"
        width="180">
        <template slot-scope="scope">{{ scope.row.status == 1 ? '未发布' : "已发布" }}</template>
      </el-table-column>
      <!-- <el-table-column prop="products" label="产品列表">
        <template slot-scope="scope">
          <div v-for="productName in scope.row.products" :key="productName">
            {{ productName }}
          </div>
        </template>
      </el-table-column> -->
      <el-table-column
        prop="budget"
        label="采购预算"
        width="180">
      </el-table-column>
      <el-table-column
        prop="createTime"
        label="创建时间">
      </el-table-column>
      <el-table-column
        prop="startTime"
        label="开始时间">
      </el-table-column>
      <el-table-column
        prop="endTime"
        label="结束时间">
      </el-table-column>

    </el-table>


  </div>
</template>

<script>
//导入所有js 命名api框架
import * as api from "@/api/purchase/demand";
import * as leimu from "@/api/purchase/category";

export default {
  data() {
    return {
      //新增弹窗
      addView: false,
      //类目树
      categories: [],
      //选择类目的id
      selectedCategoryId: null,
      //类目树组件
      props: {
        expandTrigger: "hover",
        value: "id",
        label: "name",
        children: "children",
      },
      //产品列表
      products: [],
      //选择到的列表
      selectedProducts: [],
      //新增需求列表
      addData: {
        budget: "",
        categoryId: "",
        endTime: "",
        profile: "",
        startTime: "",
        title: ""
      },
      //采购需求列表
      list: []

    };
  },
  methods: {
    //选择类目树
    handleCategoryChange(value) {
      var id = value[value.length - 1]

      // 根据类目树查询产品
      leimu.getProductByCategory({"categoryId": id}).then((response) => {
        response.data.forEach((item) => {
          item.count = 1;
        });
        this.products = response.data
        this.selectedProducts = []
      });
    },

    //多选产品
    handleProductSelect(product) {
      if (product.selected) {
        this.selectedProducts.push({
          count: product.count,
          productId: product.id,
          productName: product.productName,
        });
      } else {
        const index = this.selectedProducts.findIndex(
          (item) => item.productId === product.id
        );
        if (index !== -1) {
          this.selectedProducts.splice(index, 1);
        }
      }
    },

    // 新增需求
    add() {
      let data = this.addData;
      data.categoryId = this.selectedCategoryId[this.selectedCategoryId.length - 1];
      data.products = JSON.stringify(this.selectedProducts);
      data.products = JSON.parse(data.products);
      // api.addlist(data).then((response) => {
      // });

    },
  },
  mounted() {
    // 获取分类树
    leimu.getlist().then((response) => {
      this.categories = response.data;
    });

    // 获取采购需求列表数据
    // api.getlist().then((response) => {
    //   this.list = response.rows;
    // });
  }
};
</script>

<style scoped>

</style>
