<template>
  <div>
    <el-dialog v-dialogDragBox :title="title" :visible.sync="open" width="680px" class="custom-dialog">
      <div style="padding: 0 20px">
        <el-form ref="form" :model="form" :rules="rules" label-width="6em">
          <el-form-item label="类目名称" prop="name">
            <el-input v-model="form.name" placeholder="请输入类目名称"></el-input>
          </el-form-item>
          <el-form-item label="规格型号" prop="model">
            <el-input v-model="form.model" placeholder="请输入规格型号"></el-input>
          </el-form-item>
          <el-form-item label="父类目" prop="parentId">
            <el-cascader style="width: 100%" v-model="form.parentId" :props="{ checkStrictly: true, label: 'name', value: 'id' }" clearable :options="options" v-removeAriaHidden>
              <template slot-scope="{ node, data }">
                <span>
                  {{ data.name }}
                  <em style="font-style: normal; font-size: 12px; color: #999999">{{ data.model ? `(${data.model})` : '' }}</em>
                </span>
              </template>
            </el-cascader>
          </el-form-item>
          <el-form-item label="图片" prop="photoAddr">
            <image-upload v-model="form.photoAddr" :limit="1" is-row />
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn" @click="open = false">取消</el-button>
        <el-button class="custom-dialog-btn primary" @click="handleSubmit">提交</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { addlist, editlist } from '@/api/purchase/category'

export default {
  props: {
    options: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      open: false,
      title: '新增产品类别',
      form: {},
      rules: {
        name: [{ required: true, message: '请输入类目名称', trigger: 'blur' }],
        photoAddr: [{ required: true, message: '请上传图片', trigger: ['blur', 'change'] }]
      }
    }
  },
  methods: {
    // 重置表单
    reset() {
      this.form = {
        name: undefined,
        model: undefined,
        parentId: undefined,
        photoAddr: undefined
      }
      this.resetForm('form')
    },
    // 新增
    handleAdd() {
      this.title = '新增产品类别'
      this.reset()
      this.open = true
    },
    // 树结构转扁平数组
    treeToArr(data, children = 'children', parentId, res = []) {
      data.forEach(v => {
        v.parentId = parentId
        res.push(v)
        if (v[children] && v[children].length) this.treeToArr(v[children], children, v.id, res)
      })
      return res
    },
    // 获取父级id
    getParentId(id) {
      let parentId = []
      let parent = this.treeToArr(this.options).find(item => item.id === id)
      if (parent) {
        parentId.push(parent.id)
        if (parent.parentId) parentId = this.getParentId(parent.parentId).concat(parentId)
      }
      return parentId
    },
    // 编辑
    handleEdit(row) {
      this.title = '修改产品类别'
      this.form = { ...row }
      this.form.photoAddr = row.image
      this.form.parentId = this.getParentId(row.parentId)
      this.open = true
    },
    // 提交表单
    handleSubmit() {
      const { id, name, model, parentId, photoAddr } = this.form
      this.$refs.form.validate(valid => {
        if (valid) {
          if (id) {
            const data = { id, name, model, parentId: (parentId && parentId.pop()) || 0, picUrl: photoAddr }
            editlist(data).then(res => {
              if (res.code === 200) {
                this.$message.success('修改成功')
                this.open = false
                this.$emit('refresh', data.parentId)
              } else this.$message.error(res.msg)
            })
          } else {
            const data = { name, model, parentId: (parentId && parentId.pop()) || 0, photoAddr }
            addlist(data).then(res => {
              if (res.code === 200) {
                this.$message.success('新增成功')
                this.open = false
                this.$emit('refresh', data.parentId)
              } else this.$message.error(res.msg)
            })
          }
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';
</style>
