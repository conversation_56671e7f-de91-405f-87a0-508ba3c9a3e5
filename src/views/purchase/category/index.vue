<template>
  <div class="newBox bgcf9 vh-85">
    <div class="custom-search flex" style="padding-top: 18px">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" @submit.native.prevent>
        <el-form-item label="名称" prop="name">
          <el-input v-model="queryParams.name" placeholder="请输入名称" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" style="background-color: #2e73f3; border-color: #2e73f3" size="small" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
          <el-button type="primary" icon="el-icon-plus" size="small" @click="handleAdd">新增</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="tableBox">
      <el-table v-loading="loading" ref="categoryTable" :data="list" row-key="id" style="width: 100%" class="custom-table custom-table-cell5" lazy :load="load">
        <el-table-column align="center" label="序号" type="index" width="50"></el-table-column>
        <el-table-column prop="name" class-name="productNum" label="类目名称" align="left" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <span v-if="row.children">
              {{ row.name }}
              <i style="color: #999999; font-style: normal; margin-left: 10px">{{ row.model ? `规格型号：${row.model}` : '' }}</i>
            </span>
            <span class="pointer table-link" @click="handleProduct(row)" title="点击产品排序" v-else>
              <el-badge :value="row.productNum" :hidden="!row.productNum">
                <i class="el-icon-folder-opened"></i>
                {{ row.name }}
              </el-badge>
              <i style="color: #999999; font-style: normal; margin-left: 10px">{{ row.model ? `规格型号：${row.model}` : '' }}</i>
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="image" label="图片" align="center" width="70">
          <template slot-scope="{ row }">
            <el-image style="width: 50px; height: 50px" :src="row.oss_image || imgPath + row.image" @click="handleViewImg(row)">
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline"></i>
              </div>
            </el-image>
          </template>
        </el-table-column>
        <el-table-column prop="weight" label="权重(排序)" align="center" width="200">
          <template slot-scope="{ row }">
            <el-input size="small" type="number" placeholder="请输入内容" @blur="handleSort(row)" v-model="row.weight" style="width: 150px"></el-input>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" align="center" show-overflow-tooltip width="200"></el-table-column>
        <el-table-column align="center" label="操作" width="200">
          <template slot-scope="{ row }">
            <el-button type="text" @click="handleUpdate(row)" size="small">修改</el-button>
            <el-button type="text" @click="handleDelete(row)" size="small">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <el-dialog v-dialogDragBox title="产品排序" :visible.sync="open" width="1150px" custom-class="custom-dialog">
      <el-table :data="product" row-key="id" stripe style="width: 98%; margin: 0 auto" class="custom-table custom-table-cell5" :default-sort="{ prop: 'sort', order: 'ascending' }" :key="'prodcut' + key">
        <el-table-column align="center" prop="productName" label="产品名称" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <span class="table-link" @click="handleView(row)">{{ row.productName }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="picture1" label="图片" width="75">
          <template slot-scope="{ row }">
            <el-image :src="formatProductImg(row)" fit="cover" @click="handleImgView(row)">
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline"></i>
              </div>
            </el-image>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="specs" label="规格" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" prop="model" label="型号" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" prop="productCode" label="产品编码" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" prop="materialQuality" label="材质" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" prop="surface" label="表面处理" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" prop="unit" label="单位" width="50"></el-table-column>
        <el-table-column align="center" prop="industry" label="行业分类" show-overflow-tooltip></el-table-column>
        <el-table-column prop="sort" label="排序" align="center" width="200">
          <template slot-scope="{ row }">
            <el-input size="small" type="number" placeholder="请输入排序" @blur="handleSortProduct(row)" min="0" v-model="row.sort" style="width: 150px"></el-input>
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer">
        <button type="button" class="custom-dialog-btn primary" @click="open = false">关闭</button>
      </div>
    </el-dialog>

    <!-- 产品详情 -->
    <product-dialog ref="productInfo"></product-dialog>

    <!--新增/修改类目-->
    <create-dialog ref="CreateDialog" :options="list" @refresh="refreshList" />
  </div>
</template>

<script>
import { dellist, getlist, sort } from '@/api/purchase/category'
import * as product from '@/api/houtai/gongyu/chanpin'
import ProductDialog from '@/views/public/product/dialog'
import CreateDialog from './create'

export default {
  name: 'Class',
  components: { CreateDialog, ProductDialog },
  data() {
    return {
      queryParams: {
        name: undefined
      },
      loading: true,
      list: [],
      key: 1,
      srcList: [],
      product: [],
      open: false,
      maps: new Map()
    }
  },
  created() {
    this.getList()
  },
  methods: {
    // 列表
    getList() {
      function hasChildren(data) {
        data.forEach(item => {
          if (item.children && item.children.length > 0) {
            item.hasChildren = true
            hasChildren(item.children)
          } else {
            item.hasChildren = false
          }
        })
      }
      this.loading = true
      getlist(this.queryParams).then(res => {
        if (res.code === 200) {
          hasChildren(res.data)
          this.list = res.data
          this.loading = false
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    async load(tree, treeNode, resolve, num = false) {
      const arr = tree.children
      if (!num) {
        await Promise.all(
          arr.map(async item => {
            item.productNum = 0
            const { data } = await product.getnum({ categoryId: item.id })
            item.productNum = data
          })
        )
      }
      await resolve(arr)
      this.maps.set(tree.id, { tree, treeNode, resolve })
    },
    // 搜索
    handleQuery() {
      this.getList()
    },
    // 重置搜索
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 排序
    handleSort(row) {
      sort({ id: row.id, sort: row.weight }).then(res => {
        this.$message.success('排序成功')
        this.refreshList(row.parentId)
      })
    },
    // 新增
    handleAdd() {
      this.$refs.CreateDialog.handleAdd()
    },
    // 修改
    handleUpdate(row) {
      this.$refs.CreateDialog.handleEdit(row)
    },
    // 删除
    // prettier-ignore
    handleDelete(row) {
      this.$confirm('是否删除此数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        dellist({ id: row.id }).then(res => {
          this.$message.success('删除成功')
          this.refreshList(row.parentId)
        })
      }).catch(() => {})
    },
    // 产品管理
    handleProduct(row) {
      product.getlist({ categoryId: row.id }).then(res => {
        this.product = res.rows
        this.open = true
      })
    },
    // 产品详情
    handleView(row) {
      this.$refs.productInfo.handleView(row)
    },
    // 图片预览
    handleImgView(row) {
      this.$refs.productInfo.handleImgView(row)
    },
    handleViewImg(row) {
      const data = { picture1: row.image }
      this.$refs.productInfo.handleImgView(data)
    },
    // 产品排序
    handleSortProduct(row) {
      product.sortlist({ id: row.id, sort: row.sort }).then(res => {
        this.$message.success('排序成功')
        this.key = Math.random()
      })
    },
    // 刷新列表
    refreshList(parentId) {
      if (!parentId) {
        this.getList()
        return
      }
      function hasChildren(data) {
        data.forEach(item => {
          if (item.children && item.children.length > 0) {
            item.hasChildren = true
            hasChildren(item.children)
          } else {
            item.hasChildren = false
          }
        })
      }
      getlist(this.queryParams).then(res => {
        if (res.code === 200) {
          hasChildren(res.data)
          const tree = this.treeToArr(res.data).find(item => item.id === parentId)
          const { treeNode, resolve } = this.maps.get(parentId)
          this.$set(this.$refs.categoryTable.store.states.lazyTreeNodeMap, parentId, [])
          if (tree) this.load(tree, treeNode, resolve, true)
        }
      })
    },
    // 树结构转扁平数组
    treeToArr(data, children = 'children', parentId, res = []) {
      data.forEach(v => {
        v.parentId = parentId
        res.push(v)
        if (v[children] && v[children].length) this.treeToArr(v[children], children, v.id, res)
      })
      return res
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
.tableBox {
  margin: 15px 20px;
  border-bottom-left-radius: 5px;
  border-bottom-right-radius: 5px;
}
::v-deep {
  .productNum {
    overflow: visible;
    .cell {
      overflow: visible;
    }
  }
}
</style>
