<template>
  <div>

    <el-dialog
      title="新增类别"
      :visible.sync="dialogVisible"
      v-loading="loading"
      width="30%">
      <div>
        <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm">
          <el-form-item label="类目名称" prop="name">
            <el-input v-model="ruleForm.name"></el-input>
          </el-form-item>
          <el-form-item label="规格型号" prop="model">
            <el-input v-model="ruleForm.model"></el-input>
          </el-form-item>
          <el-form-item label="父类目" prop="parentIdb">
            <el-cascader
              style="width:100%"
              v-model="ruleForm.parentIdb"
              :props="{ checkStrictly: true, 'label': 'name','value': 'id' }"
              @change="handleChange"
              clearable
              :options="tableData">
              <template slot-scope="{ node, data }">
                <span>{{ data.name }}</span>
              </template>
            </el-cascader>


            <!--            <el-select v-model="ruleForm.parentId" placeholder="请选择活动区域">-->
            <!--              <el-option label="区域一" value="shanghai"></el-option>-->
            <!--              <el-option label="区域二" value="beijing"></el-option>-->
            <!--            </el-select>-->
          </el-form-item>
          <el-form-item label="图片" prop="fileList">
            <el-upload
              :disabled="ruleForm.fileList.length == 1 ? true : false"
              class="upload-demo"
              :action="up_file + '/common/upload'"
              name="file"
              :before-upload="handleBeforeUpload"
              :on-progress="upfile"
              :on-success="handcg"
              :show-file-list="false"
              :headers="headers"
              multiple>
              <el-button :disabled="ruleForm.fileList.length == 1 ? true : false" size="small" type="primary">
                点击上传
              </el-button>
            </el-upload>

            <div class="" v-for="(item,index) in ruleForm.fileList">
                <span @click="show_img(item.filePath)" style="cursor:pointer;">
                  {{ item.name }}
                </span>
              <el-button type="text" @click="dela(index)">删除</el-button>
            </div>

            <!--            <el-input v-model="ruleForm.photoAddr"></el-input>-->
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="submitForm('ruleForm')">立即创建</el-button>
          </el-form-item>
        </el-form>
      </div>


      <!--      <span slot="footer" class="dialog-footer">-->
      <!--    <el-button @click="dialogVisible = false">取 消</el-button>-->
      <!--    <el-button type="primary" @click="dialogVisible = false">确 定</el-button>-->
      <!--  </span>-->
    </el-dialog>


    <el-dialog v-dialogDragBox title="" :visible.sync="centerDialogVisible" width="50%" center>
      <img :src="up_file+imgfile" style="width: 100%;height: 100%" alt=""/>
      <span slot="footer" class="dialog-footer">
        <el-button @click="centerDialogVisible = false">取 消</el-button>
      </span>
    </el-dialog>


  </div>
</template>

<script>
import Cookies from "js-cookie";
import {getlist, addlist} from "@/api/purchase/dysplasia";
import { getToken } from "@/utils/auth";

export default {
  name: 'add_dysplasia',
  props:{
    tableData:{
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      dialogVisible: false,
      // tableData: [],
      ruleForm: {
        name: '',
        model: '',
        parentIdb: '',
        parentId: '',
        photoAddr: '',
        fileList: [],
      },
      rules: {
        name: [
          {required: true, message: '请输入类目名称', trigger: 'blur'},
        ],
        parentIdb: [
          {required: false, message: '请选择父类目', trigger: 'change'}
        ],
        fileList: [
          {required: true, message: '请上传图片', trigger: 'change'}
        ],
      },
      up_file: '',
      headers: {},
      loading: false,
      imgfile: '',
      centerDialogVisible: false,
    }
  },
  created() {
    this.up_file = process.env.VUE_APP_BASE_API;
    let token = Cookies.get("Admin-Token");
    this.headers = {
      Authorization: "Bearer " + token,
    };
    // this.ready();
  },
  methods: {
    ready() {
      getlist({}).then((res) => {
        let lista = res.data;
        this.tableData = lista;
      });
    },
    upfile() {
      this.loading = true;
    },
    show_img(e) {
      if (e) {
        this.imgfile = e;
        this.centerDialogVisible = true;
      }

    },
    handleBeforeUpload(file) {
      if (!getToken()) {
        this.$message.error(`请先登录`);
        return false
      }
    },
    //上传成功
    handcg(file, fileList) {
      this.ruleForm.fileList.push({
        name: file.newFileName,
        filePath: file.fileName,
      });
      this.loading = false;
    },
    //删除上传文件
    dela(e) {
      this.ruleForm.fileList.splice(e, 1);
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.add();
        } else {
          return false;
        }
      });
    },
    add() {
      let that = this;
      let parentId = this.ruleForm.parentIdb;

      let datalist = {
        name: this.ruleForm.name,
        model: this.ruleForm.model,
        parentId: parentId[parentId.length - 1],
        photoAddr: this.ruleForm['fileList'][0]['filePath']
      }
      addlist(datalist).then((res) => {
        this.$message({
          message: '提交成功',
          type: 'success'
        });
        this.dialogVisible = false;
        this.$emit('shuaxin')
      });
    },
    showmodel() {
      // this.ready();
      this.dialogVisible = true;
    },
    handleChange(value) {
    }
  }
}
</script>

<style scoped>

</style>
