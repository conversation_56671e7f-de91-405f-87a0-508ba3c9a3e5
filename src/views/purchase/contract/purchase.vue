<template>
  <div>
    <el-dialog v-dialogDragBox title="无效合同设置" :visible.sync="open" width="1150px" class="custom-dialog">
      <div class="addBox">
        <el-form ref="form" :model="form" :rules="rules" label-position="left" label-width="100px">
          <el-row :gutter="18">
            <el-col :span="12">
              <el-form-item label="订单编号" prop="serialNum">
                <el-input v-model="form.serialNum" placeholder="请输入订单编号"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="订单日期" prop="date">
                <el-date-picker v-model="form.date" type="date" placeholder="请选择订单日期" style="width: 100%" value-format="timestamp"></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="要求到厂时间" prop="requiredTime">
                <el-date-picker v-model="form.requiredTime" type="date" placeholder="请选择要求到厂时间" style="width: 100%" :picker-options="dateOption" value-format="timestamp"></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="备注" prop="remark">
                <el-input v-model="form.remark" placeholder="请输入备注"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-table ref="addSubTable" stripe :data="form.purchasingProducts" row-key="id" style="width: 100%" :max-height="formHeight" class="custom-table">
                <el-table-column align="center" prop="product_name" label="产品名称" show-overflow-tooltip>
                  <template slot-scope="{ row }">
                    <template v-if="type === 'abnormal'">
                      <span class="table-link" @click="handleView(row.product, row, 'abnormal')" v-if="row.productId">
                        <span v-if="row.source === 'common'">(公域)</span>
                        <span style="color: #fe7f22" v-else>(私域)</span>
                        {{ row.product && row.product.productName }}
                      </span>
                      <span class="table-link" v-else>
                        <span style="color: #fe7f22">(私域)</span>
                        {{ row.product && row.product.productName }}
                      </span>
                    </template>
                    <template v-else>
                      <span class="table-link" @click="handleView(row.productId, row)" v-if="row.productId">
                        <span v-if="row.source === 'common'">(公域)</span>
                        <span style="color: #fe7f22" v-else>(私域)</span>
                        {{ row.productName }}
                      </span>
                      <span class="table-link" v-else>
                        <span style="color: #fe7f22">(私域)</span>
                        {{ row.productName }}
                      </span>
                    </template>
                  </template>
                </el-table-column>
                <el-table-column align="center" prop="unit" label="单位">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`purchasingProducts.${scope.$index}.unit`" :rules="rules.unit">
                      <el-select size="small" v-model="scope.row.unit" filterable allow-create default-first-option placeholder="请选择单位">
                        <el-option v-for="item in unitoptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                      </el-select>
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column align="center" prop="quantity" label="采购数量">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`purchasingProducts.${scope.$index}.quantity`" :rules="rules.quantity">
                      <el-input v-model="scope.row.quantity" size="small" placeholder="采购数量" />
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column align="center" prop="recommendPrice" label="建议采购价">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`purchasingProducts.${scope.$index}.recommendPrice`" :rules="rules.recommendPrice">
                      <el-input v-model="scope.row.recommendPrice" size="small" placeholder="建议采购价">
                        <span slot="prefix" class="inline-flex">￥</span>
                      </el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column align="center" prop="note" label="备注">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`purchasingProducts.${scope.$index}.note`" :rules="rules.note">
                      <el-input v-model="scope.row.note" size="small" placeholder="备注"></el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column align="center" label="操作" v-if="form.purchasingProducts && form.purchasingProducts.length > 1">
                  <template slot-scope="{ row }">
                    <button type="button" class="table-btn danger" @click="handleDeleteProduct(row)">删除</button>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <button type="button" class="custom-dialog-btn danger" @click="handleCancel">只设置无效</button>
        <button type="button" class="custom-dialog-btn primary" @click="handleSubmit">设置无效并添加采购</button>
      </div>
    </el-dialog>
    <!-- 产品详情 -->
    <product-dialog ref="productInfo"></product-dialog>
  </div>
</template>
<script>
import { isNumber, isNumberLength } from '@/utils/validate'
import { contractDelete, contractPriceChange, purchasingAdd } from '@/api/purchase'
import { getProduct } from '@/api/system/product'
import { getPrivateduct } from '@/api/system/privateduct'
import ProductDialog from '@/views/public/product/dialog'

export default {
  components: { ProductDialog },
  data() {
    return {
      contractId: undefined,
      dateOption: {
        disabledDate: time => {
          return time.getTime() < Date.now() - 24 * 60 * 60 * 1000
        }
      },
      unitoptions: [
        { value: '吨', label: '吨' },
        { value: '千克', label: '千克' },
        { value: '个', label: '个' },
        { value: '件', label: '件' },
        { value: '套', label: '套' },
        { value: '米', label: '米' },
        { value: '支', label: '支' }
      ],
      open: false,
      form: {},
      rules: {
        serialNum: [{ required: true, message: '请输入订单编号', trigger: 'blur' }],
        date: [{ required: true, message: '请选择订单日期', trigger: ['blur', 'change'] }],
        quantity: [
          { required: true, message: '请输入采购数量', trigger: 'blur' },
          { validator: isNumber, message: '请输入正确的数量', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 3), message: '只可以填写三位小数', trigger: 'blur' }
        ],
        recommendPrice: [
          { validator: isNumber, message: '请输入正确的金额', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 4), message: '只可以填写四位小数', trigger: 'blur' }
        ],
        unit: [{ required: true, message: '请选择单位', trigger: 'change' }]
      },
      windowHeight: 0,
      formHeight: 300,
      type: ''
    }
  },
  watch: {
    windowHeight(val) {
      this.formHeight = val * 0.94 - 367
    }
  },
  mounted() {
    this.windowHeight = document.documentElement.clientHeight
    window.onresize = () => {
      this.windowHeight = document.documentElement.clientHeight
    }
  },
  methods: {
    // 查看合同产品并弹出
    async handleOpen(row = {}, type = '') {
      const contractId = row.id
      this.type = type
      if (!contractId) {
        this.$message.error('参数错误，请稍后再试！')
        return
      }
      this.contractId = contractId
      this.form = {
        date: undefined,
        purchasingProducts: [],
        remark: undefined,
        requiredTime: undefined,
        serialNum: undefined
      }
      this.resetForm('form')
      if (type === 'abnormal') {
        let data = JSON.parse(row.products) || []
        if (data.length > 0) {
          await Promise.all(
            data.map(async item => {
              if (item.source === 'common') {
                const res = await getProduct(item.productId)
                item.product = res.data
                item.productName = res.data.productName
                item.recommendPrice = item.amount
              } else {
                const res_1 = await getPrivateduct(item.productId)
                item.product = res_1.data
                item.productName = res_1.data.productName
                item.recommendPrice = item.amount
              }
            })
          )
          this.form.purchasingProducts = data
          this.open = true
        } else this.$message.error('没有产品信息')
      } else {
        contractPriceChange({ contractId }).then(res => {
          const { code, msg, data } = res
          if (code === 200) {
            data.map(item => {
              item.recommendPrice = item.amount
              item.quantity = item.sjNum
            })
            this.form.purchasingProducts = data
            this.open = true
          } else this.$message.error(msg)
        })
      }
    },
    // 产品详情
    handleView(Id, item, type) {
      if (type === 'abnormal') {
        this.$refs.productInfo.handleView(item.product)
      } else {
        if (item.source === 'common') {
          getProduct(Id).then(res => {
            this.$refs.productInfo.handleView(res.data)
          })
        } else {
          getPrivateduct(Id).then(res => {
            this.$refs.productInfo.handleView(res.data)
          })
        }
      }
    },
    // 删除产品
    handleDeleteProduct(row) {
      const index = this.form.purchasingProducts.findIndex(item => item.id === row.id)
      this.form.purchasingProducts.splice(index, 1)
    },
    // 只设置无效
    handleCancel() {
      contractDelete({ contractId: this.contractId }).then(res => {
        const { code, msg } = res
        if (code === 200) {
          this.$message.success('操作成功')
          this.open = false
          this.$emit('callback')
        } else this.$message.error(msg)
      })
    },
    // 设置无效并添加采购
    handleSubmit() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          const { serialNum, date, requiredTime, remark, purchasingProducts } = this.form
          const newPurchasingProducts = purchasingProducts.map(item => {
            return {
              note: item.note,
              productId: item.productId,
              productName: item.productName,
              quantity: item.quantity,
              recommendPrice: item.recommendPrice,
              source: item.source,
              unit: item.unit
            }
          })
          const data = { serialNum, date, requiredTime, remark, purchasingProducts: newPurchasingProducts }
          Promise.all([contractDelete({ contractId: this.contractId }), purchasingAdd(data)]).then(res => {
            const code = res.every(item => item.code === 200)
            if (code) {
              this.$message.success('操作成功')
              this.open = false
              this.$emit('callback')
            } else this.$message.error(res.find(item => item.code !== 200).msg)
          })
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
::v-deep .custom-dialog {
  .addBox {
    padding: 10px 20px;
  }
  .custom-table {
    .el-form-item {
      margin-top: 10px !important;
      margin-bottom: 10px !important;
      .el-form-item__error {
        top: 95%;
        padding-top: 0;
      }
    }
  }
}
</style>
