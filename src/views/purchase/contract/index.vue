<template>
  <div :class="isLook ? '' : 'newBox bgcf9 vh-85'">
    <div class="custom-back" v-if="isOrder">
      <div class="back-button" @click="handleClose">
        <i class="el-icon-back"></i>
        返回
      </div>
    </div>
    <!-- 搜索 -->
    <div class="custom-search flex flex-column" style="padding-top: 18px" v-if="!isOrder && !isLook && queryParams.orderType !== 'unsign'">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
        <el-form-item label="合同编号" prop="serial">
          <el-input v-model="queryParams.serial" placeholder="请输入合同编号" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="关键词" prop="keyword">
          <el-input v-model="queryParams.keyword" placeholder="请输入关键词" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="status" placeholder="请选择状态" clearable multiple style="min-width: 250px">
            <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="制单人" prop="createBy" v-hasPermi="['purchasing:contract:all']">
          <el-input v-model="queryParams.createBy" placeholder="请输入制单人" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="审批状态" prop="createBy" v-if="companyId === 14">
          <el-select v-model="queryParams.approvalStatus" placeholder="请选择审批状态" clearable>
            <el-option v-for="item in approvalOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <!--开始时间-->
        <el-form-item label="签订时间" prop="createTime">
          <el-date-picker v-model="timeFormat" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" style="width: 250px" value-format="yyyy-MM-dd" format="yyyy-MM-dd" @change="handleTimeChange" clearable :picker-options="totalPickerOptions" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" style="background-color: #2e73f3; border-color: #2e73f3" size="small" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
          <el-button icon="el-icon-s-tools" size="small" @click="handleRules" type="primary" plain v-hasPermi="['uoc:config']">合同规则设置</el-button>
        </el-form-item>
      </el-form>
      <div class="kindeeButton flex align-center" v-if="companyId === 14">
        <div style="margin-right: 10px">
          <el-checkbox v-model="batchOpen" @change="handleBatchOpen">金蝶批量操作</el-checkbox>
        </div>
        <el-button type="primary" size="small" :disabled="batchDisabled()" @click="handleKingdeeSubmit">提交</el-button>
        <el-button type="warning" size="small" :disabled="batchDisabled()" @click="handleKingdeeAudit">审核</el-button>
        <el-button type="info" size="small" :disabled="batchDisabled()" @click="handleKingdeeRevoke">撤销</el-button>
        <el-button type="danger" size="small" :disabled="batchDisabled()" @click="handleKingdeeDelete">删除</el-button>
        <el-button type="warning" size="small" :disabled="batchDisabled()" @click="handleKingdeeUnAudit">反审</el-button>
        <el-tooltip effect="dark" content="金蝶批量下推仅支持全部下推" :disabled="batchDisabled()">
          <el-button type="success" size="small" :disabled="batchDisabled()" @click="handleKingdeePush">下推</el-button>
        </el-tooltip>
      </div>
    </div>
    <!-- 未签署搜索 -->
    <div class="custom-search flex" style="padding-top: 18px" v-if="!isOrder && !isLook && queryParams.orderType === 'unsign'">
      <el-form ref="unsignQuery" size="small" :inline="true">
        <el-form-item label="合同编号" prop="serial">
          <el-input v-model="unsignSerial" placeholder="请输入合同编号" clearable @keyup.enter.native="handleUnsignQuery" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" style="background-color: #2e73f3; border-color: #2e73f3" size="small" @click="handleUnsignQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="small" @click="resetUnsignQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="orderNumber" v-if="!!orderNumber">
      当前查看的是：采购订单号为
      <span style="color: #2e73f3">{{ orderNumber }}</span>
      的相关合同
    </div>
    <!-- 分类 -->
    <div class="classify flex" v-if="!isLook">
      <div class="classify-item" :class="{ active: item.value === queryParams.orderType }" v-for="item in classifyOptions" :key="item.value" @click="handleCategory(item)">
        {{ item.label }}
      </div>
      <div class="classify-tip" v-if="queryParams.orderType !== 'unsign' && !!unsignList.length">
        <i class="el-icon-info"></i>
        <span>您有{{ unsignList.length }}份已发送还未签署的合同</span>
      </div>
      <div class="classify-toolbar">
        <right-toolbar :search="false" @queryTable="getList" @updateColumns="updateColumns" :columns="queryParams.orderType !== 'abnormal' ? columns : []" isSetitem></right-toolbar>
      </div>
    </div>

    <!-- 列表 -->
    <div :class="{ Box: !isLook }" v-if="queryParams.orderType !== 'unsign'">
      <template v-if="total > 0">
        <el-table v-loading="loading" ref="allTable" stripe :data="list" row-key="id" :key="key" style="width: 100%" class="custom-table custom-table-cell10" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="50" align="center" :label-class-name="timeFormat ? '' : 'disabledSelection'" :selectable="selectable" />
          <el-table-column align="center" type="index" label="序号" width="50" v-if="columns[0].visible"></el-table-column>
          <el-table-column align="center" prop="serial" label="合同编号" show-overflow-tooltip width="120" v-if="columns[1].visible"></el-table-column>
          <el-table-column align="center" label="供应商" show-overflow-tooltip min-width="130" v-if="columns[2].visible">
            <template slot-scope="{ row }">
              <span class="table-link" @click="handleViewSupplier(row)" v-if="queryParams.orderType === 'purchase' || queryParams.orderType === 'bidding' || queryParams.orderType === 'promotion'">
                <span v-if="row.source === 'common'">(公域)</span>
                <span style="color: #fe7f22" v-else>(私域)</span>
                {{ row.sellerName }}
              </span>
              <span v-if="queryParams.orderType === 'abnormal'">{{ row.sellerName }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="产品明细">
            <template slot-scope="{ row }">
              <el-button type="text" size="small" icon="el-icon-view" @click="handleProductDetail(row)">查看</el-button>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="sendUser" label="业务员" show-overflow-tooltip v-if="(columns[3].visible && queryParams.orderType === 'purchase') || queryParams.orderType === 'bidding' || queryParams.orderType === 'promotion'"></el-table-column>
          <el-table-column align="center" prop="sendPhone" label="业务员电话" show-overflow-tooltip v-if="(columns[4].visible && queryParams.orderType === 'purchase') || queryParams.orderType === 'bidding' || queryParams.orderType === 'promotion'"></el-table-column>
          <el-table-column align="center" prop="docUser" label="采购制单人" show-overflow-tooltip v-if="columns[5].visible">
            <template slot-scope="{ row }">{{ row.docUser || row.createBy }}</template>
          </el-table-column>
          <el-table-column align="center" prop="createTime" label="创建时间" show-overflow-tooltip width="100" v-if="queryParams.orderType === 'abnormal'"></el-table-column>
          <el-table-column align="center" prop="signingTime" label="签订时间" show-overflow-tooltip width="100" v-if="(columns[6].visible && queryParams.orderType === 'purchase') || queryParams.orderType === 'bidding' || queryParams.orderType === 'promotion'"></el-table-column>
          <el-table-column align="center" prop="address" label="签订地点" show-overflow-tooltip v-if="(columns[7].visible && queryParams.orderType === 'purchase') || queryParams.orderType === 'bidding' || queryParams.orderType === 'promotion'"></el-table-column>
          <el-table-column align="center" prop="amount" label="订单总金额" show-overflow-tooltip min-width="120" v-if="columns[8].visible">
            <template slot-scope="{ row }">
              <el-badge class="custom-badge" is-dot :hidden="!row.isChange">
                <span class="table-orange" :class="{ 'table-cursor': row.isChange }" style="font-size: 14px" @click="handleAmount(row)">{{ row.amount ? '￥' + row.amount : '' }}{{ row.isIncludingTax ? '(含税)' : '(不含税)' }}</span>
              </el-badge>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="status" label="状态" show-overflow-tooltip width="60" v-if="columns[9].visible">
            <template slot-scope="{ row }">
              <div v-html="statusFormat(row)"></div>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="applyTime" label="付款申请时间" show-overflow-tooltip width="100" v-if="columns[11].visible && queryParams.orderType === 'purchase'"></el-table-column>
          <el-table-column align="center" prop="approvalStatus" label="审批状态" show-overflow-tooltip v-if="columns[10].visible && companyId === 14">
            <template slot-scope="{ row }">
              <div v-html="approvalStatusFormat(row)"></div>
            </template>
          </el-table-column>
          <!--企业ID为14的才显示-->
          <template v-if="companyId === 14">
            <el-table-column align="center" prop="requisitionNum" label="金蝶采购申请单编码" show-overflow-tooltip v-if="queryParams.orderType === 'purchase' || queryParams.orderType === 'abnormal'">
              <!-- 点击跳转 -->
              <template slot-scope="{ row }">
                <span class="table-link" @click="handleKingdeeDetail(row)">{{ row.requisitionNum }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="requisitionStatus" label="金蝶采购申请单状态" show-overflow-tooltip v-if="queryParams.orderType === 'purchase' || queryParams.orderType === 'abnormal'">
              <template slot-scope="scope">
                <div style="display: inline-flex; align-items: center" v-if="scope.row.requisitionStatus">
                  <span>{{ requisitionStatusFormat(scope.row) }}</span>
                  <el-tooltip effect="dark" content="点击刷新状态" placement="top">
                    <el-button size="small" type="text" icon="el-icon-refresh" @click="handleRefresh(scope.row)" />
                  </el-tooltip>
                </div>
              </template>
            </el-table-column>
          </template>
          <el-table-column align="center" label="操作" :width="getActionColumnWidth()" v-if="checkPermi(['purchasing:contract:detail', 'purchasing:contract:del'])">
            <template slot-scope="{ row }">
              <template v-if="queryParams.status.toString() == '-1'">
                <!-- 只有查看详情 -->
                <el-button class="table-btn" @click="handleView(row)" v-hasPermi="['purchasing:contract:detail']">查看详情</el-button>
              </template>
              <template v-else>
                <!-- 常用按钮 -->
                <template v-for="action in getCommonActions(row)">
                  <el-button :key="action.key" :class="action.className" @click="action.handler(row)" v-if="action.show">{{ action.label }}</el-button>
                </template>
                <!-- 更多操作 -->
                <el-popover trigger="hover" v-if="hasMoreActions(row)">
                  <button type="button" class="table-btn primary" slot="reference">
                    更多操作
                    <i class="el-icon-arrow-down el-icon--right"></i>
                  </button>
                  <div class="popover-button">
                    <template v-for="action in getAllActions(row)">
                      <div :key="action.key" class="popover-button-item" v-if="action.show">
                        <el-button :class="action.className" @click="action.handler(row)">{{ action.label }}</el-button>
                        <i class="popover-button-icon" :class="[isCommonAction(action.key) ? 'el-icon-star-on' : 'el-icon-star-off', { active: isCommonAction(action.key) }]" @click="toggleCommonAction(action.key)" :title="isCommonAction(action.key) ? '取消常用' : '设置为常用'"></i>
                      </div>
                    </template>
                  </div>
                </el-popover>
              </template>
            </template>
          </el-table-column>
        </el-table>

        <div class="custom-pagination" v-if="!isOrder && !isLook">
          <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" local="ContractPageSize" @pagination="getList" />
        </div>
      </template>
      <el-empty v-else />
    </div>
    <div :class="{ Box: !isLook }" v-if="queryParams.orderType === 'unsign'">
      <template v-if="unsignList.length > 0">
        <el-table ref="unsignTable" stripe :data="unsignList" style="width: 100%" class="custom-table custom-table-cell10">
          <el-table-column align="center" type="index" label="序号" width="50"></el-table-column>
          <el-table-column align="center" prop="serial" label="合同编号" show-overflow-tooltip width="120"></el-table-column>
          <el-table-column align="center" label="供应商" show-overflow-tooltip min-width="130">
            <template slot-scope="{ row }">
              <span class="table-link" @click="handleViewSupplier(row)" v-if="queryParams.orderType === 'unsign'">
                <span v-if="row.source === 'common'">(公域)</span>
                <span style="color: #fe7f22" v-else>(私域)</span>
                {{ row.sellerName }}
              </span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="sendPhone" label="业务员电话" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="signingTime" label="签订时间" show-overflow-tooltip width="100"></el-table-column>
          <el-table-column align="center" prop="address" label="签订地点" show-overflow-tooltip width="150"></el-table-column>
          <el-table-column align="center" prop="amount" label="订单总金额" show-overflow-tooltip width="200">
            <template slot-scope="{ row }">
              <span class="table-orange" style="font-size: 14px">{{ row.amount ? '￥' + row.amount : '' }}{{ row.isIncludingTax ? '(含税)' : '(不含税)' }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="操作" width="330" v-if="checkPermi(['purchasing:contract:detail', 'purchasing:contract:del'])">
            <template slot-scope="{ row }">
              <button type="button" class="table-btn" @click="handleView(row)" v-hasPermi="['purchasing:contract:detail']">查看详情</button>
              <button type="button" class="table-btn danger" @click="handleDelete(row)" v-hasPermi="['purchasing:contract:del']">无效</button>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <el-empty v-else />
    </div>

    <!-- 供应商详情 -->
    <supplier-dialog ref="supplier" :isLook="isLook"></supplier-dialog>

    <!-- 合同详情 -->
    <el-dialog v-dialogDragBox title="合同详情" :visible.sync="open" width="1150px" class="custom-dialog" :append-to-body="isLook">
      <div class="print" v-if="contractStatus !== -1">
        <div class="print-item" @click="handleSend(info)" v-if="contractStatus === 1">
          <i class="el-icon-position"></i>
          发送
        </div>
        <div class="print-item" @click="handleSendRecording(info)" v-if="contractStatus === 1">
          <i class="el-icon-position"></i>
          发送记录
        </div>
        <div class="print-item" @click="handlePrint(info)" v-hasPermi="['purchasing:contract:print']">
          <i class="el-icon-printer"></i>
          打印
        </div>
        <div class="print-item" @click="handleDownload(info)" v-hasPermi="['purchasing:contract:download']">
          <i class="el-icon-download"></i>
          下载
        </div>
        <template v-if="info.hasOwnProperty('certify') && info.certify">
          <div class="print-item" @click="handleAutonym(info)" v-hasPermi="['purchasing:contract:autonym']">
            <i class="el-icon-s-check"></i>
            认证信息
          </div>
        </template>
      </div>
      <div style="text-align: center" :style="{ zoom: zoom }">
        <img style="max-width: 100%" :src="info.file" v-if="isBase64Image(info.file)" />
        <show-template :certify="!!info.certify" :signet="signet" :signature="info.signature" :content="info.file" v-else-if="isJSON(info.file)" />
      </div>
      <div slot="footer">
        <button type="button" class="custom-dialog-btn primary" @click="open = false">关 闭</button>
      </div>
    </el-dialog>

    <!-- 评价 -->
    <radar-dialog ref="radaDialog" :isLook="isLook" />

    <!--  选择联系人  -->
    <el-dialog v-dialogDragBox title="发送合同" :visible.sync="userOpen" width="1150px" class="custom-dialog" :append-to-body="isLook">
      <div style="padding: 0 20px">
        <div class="send-title">公司基本信息</div>
        <div class="send-box">
          <el-row>
            <el-col :span="12">
              <span>公司名称：</span>
              {{ userInfo.companyName }}
            </el-col>
            <el-col :span="12">
              <span>公司法人：</span>
              {{ userInfo.companyLegal }}
            </el-col>
            <el-col :span="24" class="border0">
              <span>公司地址：</span>
              {{ removeHtmlTag(userInfo.companyAddress, 300) }}
            </el-col>
          </el-row>
        </div>
        <div class="send-title">
          <span style="color: #333333; font-size: 16px; margin-right: 10px">请选择发送合同接收人</span>
          <span>是否需要实名认证：</span>
          <el-switch v-model="forceCertify" @change="handleForceCertify" active-text="是" inactive-text="否" class="isSite"></el-switch>
        </div>
        <el-table v-loading="loading" ref="userTable" stripe :data="userList" row-key="id" style="width: 100%" class="custom-table custom-table-cell10">
          <el-table-column align="center" label="选择" width="55">
            <template slot-scope="{ row }">
              <el-radio v-model="userRadio.phone" :label="row.phone" @click.native.prevent="handleUserChange(row)" v-if="isChange"><span /></el-radio>
              <template v-else>
                <el-tooltip class="item" effect="dark" :content="row.authLetter && !timeOver(row.authTimeEnd) ? '授权书已过期' : '需补充授权书'" placement="top" v-if="(!row.authLetter || !timeOver(row.authTimeEnd)) && ((ruleLetter.configValue === 'true' && Number(rulePrice.configValue) <= info.amount) || Number(rulePrice.configValue) <= info.amount)">
                  <el-radio v-model="userRadio.phone" :label="row.phone" disabled><span /></el-radio>
                </el-tooltip>
                <template v-else>
                  <el-tooltip class="item" effect="dark" :content="row.authLetter && !timeOver(row.authTimeEnd) && forceCertify ? '授权书已过期' : '需补充授权书'" placement="top" v-if="(!row.authLetter || !timeOver(row.authTimeEnd)) && forceCertify">
                    <el-radio v-model="userRadio.phone" :label="row.phone" disabled><span /></el-radio>
                  </el-tooltip>
                  <el-radio v-model="userRadio.phone" :label="row.phone" @click.native.prevent="handleUserChange(row)" disabled v-else><span /></el-radio>
                </template>
              </template>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="nickName" label="联系人姓名" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="phone" label="联系人电话" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="post" label="联系人职务" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="nums" label="发送次数" show-overflow-tooltip>
            <template slot-scope="{ row }">{{ row.nums ? `已发送${row.nums}次` : '未发送' }}</template>
          </el-table-column>
          <el-table-column align="center" prop="post" label="授权有效期">
            <template slot-scope="scope">
              <el-tooltip class="item" effect="dark" :content="`有效期至：${parseTime(scope.row.authTimeEnd, '{y}年{m}月{d}日')}`" placement="top" v-if="!!scope.row.authTimeEnd">
                <el-button type="text" icon="el-icon-edit" size="small" @click="handleAddImpower(scope.row, scope.$index)">修改授权</el-button>
              </el-tooltip>
              <el-button type="text" icon="el-icon-plus" size="small" @click="handleAddImpower(scope.row, scope.$index)" v-else>添加授权</el-button>
            </template>
          </el-table-column>
          <el-table-column align="center" label="站内通知">
            <template slot-scope="{ row }">
              <el-switch class="isSite" :disabled="!row.userId" v-model="row.isSite" active-text="开" inactive-text="关"></el-switch>
            </template>
          </el-table-column>
          <el-table-column align="center" label="注册公司名称" show-overflow-tooltip>
            <template slot-scope="{ row }">{{ row.companyName }}</template>
          </el-table-column>
          <el-table-column align="center" label="注册昵称" show-overflow-tooltip>
            <template slot-scope="{ row }">{{ row.userName }}</template>
          </el-table-column>
        </el-table>
      </div>
      <div slot="footer">
        <button type="button" class="custom-dialog-btn" @click="userOpen = false">取消</button>
        <button type="button" class="custom-dialog-btn primary" :class="{ disabled: !userRadio.phone }" :disabled="!userRadio.phone" @click="handleSendSubmit">确认并发送</button>
      </div>
    </el-dialog>

    <el-dialog v-dialogDragBox title="其他采购详情" :visible.sync="otherOpen" width="1150px" class="custom-dialog" :show-close="false">
      <div style="padding: 0 20px">
        <el-descriptions class="desc" labelClassName="desc-label" title="采购清单信息" :column="3" border>
          <el-descriptions-item label="合同编号" :span="2">{{ otherInfo.serial }}</el-descriptions-item>
          <el-descriptions-item label="采购制单人">{{ otherInfo.docUser }}</el-descriptions-item>
          <el-descriptions-item label="供应商">{{ otherInfo.sellerName }}</el-descriptions-item>
          <el-descriptions-item label="联系人">{{ contactInfo.nickName }}</el-descriptions-item>
          <el-descriptions-item label="联系电话">{{ contactInfo.phone }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ otherInfo.createTime }}</el-descriptions-item>
        </el-descriptions>
        <el-table ref="confirmTable" stripe :data="otherProducts" style="width: 100%" class="custom-table">
          <el-table-column align="center" type="index" label="序号"></el-table-column>
          <el-table-column align="center" prop="product" label="产品名称" show-overflow-tooltip>
            <template slot-scope="{ row }">
              <span class="table-link" @click="handleProductView(row.product)" v-if="row.hasOwnProperty('product')">
                <span v-if="row.source === 'common'">(公域)</span>
                <span style="color: #fe7f22" v-else>(私域)</span>
                {{ row.product.productName }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="unit" label="单位"></el-table-column>
          <el-table-column align="center" prop="quantity" label="采购数量"></el-table-column>
          <el-table-column align="center" prop="amount" label="采购价格"></el-table-column>
        </el-table>
        <div class="confirm-total inline-flex">
          <span>
            共
            <b>{{ otherProducts.length }}</b>
            件产品
          </span>
          <span>
            完成日期：
            <b>{{ otherInfo.createTime }}</b>
          </span>
          <span>
            订单总金额：
            <b>{{ otherInfo.amount }}</b>
          </span>
        </div>
      </div>
      <div slot="footer">
        <button type="button" class="custom-dialog-btn primary" @click="otherOpen = false">关闭</button>
      </div>
    </el-dialog>

    <!-- 产品详情 -->
    <product-dialog ref="productInfo"></product-dialog>

    <!--  提交合同审批  -->
    <el-dialog v-dialogDragBox title="提交合同审批" :visible.sync="approvalOpen" width="1150px" class="custom-dialog">
      <div class="formBox-desc">
        <div class="formBox-item">
          <span>供应商：</span>
          <span class="primary">{{ approvalInfo.sellerName }}</span>
        </div>
        <div class="formBox-item">
          <span>合同金额：</span>
          <span class="orange">{{ approvalInfo.amount }}元</span>
        </div>
        <div class="formBox-item" v-if="!!approvalInfo.salesman">
          <span>业务员：</span>
          <span>{{ approvalInfo.salesman }}</span>
        </div>
      </div>
      <div class="formBox formBoxBg">
        <el-form ref="approvalForm" :model="approvalForm" :rules="approvalRules" label-width="6em">
          <el-row :gutter="10">
            <el-col :span="12">
              <el-form-item label="开户行" prop="bankName">
                <el-autocomplete v-model="approvalForm.bankName" placeholder="请输入开户行" :fetch-suggestions="querySearchBankName" @select="handleSelect" style="width: 100%"></el-autocomplete>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="银行账号" prop="bankNo">
                <el-autocomplete v-model="approvalForm.bankNo" placeholder="请输入开户行" :fetch-suggestions="querySearchBankNo" @select="handleSelect" style="width: 100%"></el-autocomplete>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="已付金额" prop="paidAmount">
                <el-input v-model="approvalForm.paidAmount" placeholder="请输入已付金额"><span slot="suffix">元</span></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="付款金额" prop="payAmount">
                <el-input v-model="approvalForm.payAmount" placeholder="请输入付款金额"><span slot="suffix">元</span></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="收款单位" prop="payee">
                <el-input v-model="approvalForm.payee" placeholder="请输入收款单位	"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="付款方式" prop="payMethod">
                <el-select v-model="approvalForm.payMethod" placeholder="请选择付款方式" clearable style="width: 100%">
                  <el-option v-for="item in payMethodOptions" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="所属部门" prop="depart">
                <el-select v-model="approvalForm.depart" placeholder="请选择所属部门" clearable style="width: 100%">
                  <el-option v-for="item in departOptions" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="备注" prop="remark">
                <el-input v-model="approvalForm.remark" type="textarea" :autosize="{ minRows: 3, maxRows: 5 }" placeholder="请输入备注" clearable></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24" v-if="mediaLimit">
              <el-form-item label="附件" prop="media">
                <el-upload ref="mediaUpload" :action="media.url" :headers="media.headers" :accept="media.accept" :on-remove="handleMediaRemove" :on-success="handleMediaSuccess" :on-progress="handleMediaProgress" :before-upload="handleMediaBeforeUpload" :file-list="media.fileList" :disabled="media.disabled" :limit="mediaLimit">
                  <el-button size="small" type="primary" :loading="media.loading" :disabled="media.disabled">{{ media.loading ? '上传中' : '上传附件' }}</el-button>
                  <div slot="tip" class="upload-tip">
                    请上传
                    <span>{{ media.accept }}</span>
                    格式文件，上传数量不超过
                    <span>{{ mediaLimit }}个</span>
                    ，jpg图片大小不超过
                    <span>1M</span>
                    ，amr、mp3音频大小不超过
                    <span>2M</span>
                    ，mp4视频大小不超过
                    <span>10M</span>
                  </div>
                </el-upload>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <button type="button" class="custom-dialog-btn" @click="approvalOpen = false">取消</button>
        <el-button :disabled="approvalLoading" :loading="approvalLoading" class="custom-dialog-btn primary" :class="{ disabled: approvalLoading }" @click="submitForApproval" style="padding: 0">提交</el-button>
      </div>
    </el-dialog>

    <template v-if="approvalArr.length">
      <div class="collectAll">
        <div class="collectAll-box">
          <div class="collectAll-title">已选择 {{ approvalArr.length }} 项</div>
          <template v-if="!batchOpen">
            <el-tooltip :content="approvalsFormat()" placement="top" effect="dark" v-hasPermi="['purchasing:demand:add']">
              <div class="collectAll-btn">
                <span @click="handleApprovals">提交审批</span>
              </div>
            </el-tooltip>
            <el-tooltip content="金额汇总" placement="top" effect="dark">
              <div class="collectAll-btn">
                <span @click="handleTotal">金额汇总</span>
              </div>
            </el-tooltip>
          </template>
          <el-tooltip content="退出" placement="top" effect="dark">
            <div class="collectAll-close" @click="handleCloseApproval">
              <i class="el-icon-close"></i>
            </div>
          </el-tooltip>
        </div>
      </div>
    </template>

    <!--  审批记录  -->
    <el-dialog v-dialogDragBox title="审批记录" :visible.sync="approvalRecordsOpen" width="1150px" class="custom-dialog">
      <div class="formBox">
        <el-table ref="approvalRecordsTable" stripe :data="approvalRecordsList" row-key="id" style="width: 100%" class="custom-table custom-table-cell10">
          <el-table-column align="center" type="index" label="序号"></el-table-column>
          <el-table-column align="center" prop="approvalNo" label="审批编号" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="bankName" label="开户行" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="bankNo" label="银行账号" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="paidAmount" label="已付金额" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="payAmount" label="付款金额" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="payee" label="收款单位" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="remark" label="备注" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="payMethod" label="支付方式" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="applyTime" label="提交时间" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="status" label="审批状态" show-overflow-tooltip>
            <template slot-scope="{ row }">
              <div v-html="approvalRecordsFormat(row)"></div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div slot="footer">
        <button type="button" class="custom-dialog-btn primary" @click="approvalRecordsOpen = false">关闭</button>
      </div>
    </el-dialog>

    <!--  合同发送记录  -->
    <el-dialog v-dialogDragBox title="合同发送记录" :visible.sync="recordOpen" width="1150px" class="custom-dialog">
      <div class="formBox">
        <el-table ref="approvalRecordsTable" stripe :data="recordList" row-key="id" style="width: 100%" class="custom-table custom-table-cell10">
          <el-table-column align="center" type="index" label="序号"></el-table-column>
          <el-table-column align="center" prop="companyName" label="供应商名称" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="sendUser" label="联系人" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="phone" label="联系电话" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="createTime" label="发送时间" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="createBy" label="操作人" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="status" :formatter="recordStatus" label="发送状态" show-overflow-tooltip></el-table-column>
        </el-table>
      </div>
      <div slot="footer">
        <button type="button" class="custom-dialog-btn primary" @click="recordOpen = false">关闭</button>
      </div>
    </el-dialog>
    <!--  价格变动  -->
    <el-dialog v-dialogDragBox title="价格变更" :visible.sync="changeOpen" width="1150px" class="custom-dialog">
      <div class="formBox">
        <el-table ref="changeTable" stripe :data="changeList" row-key="id" style="width: 100%" class="custom-table custom-table-cell10">
          <el-table-column align="center" prop="productName" label="产品名称" show-overflow-tooltip min-width="130">
            <template slot-scope="{ row }">
              <span class="table-link" @click="handleViewProduct(row.productId, row)">
                <span v-if="row.source === 'common'">(公域)</span>
                <span style="color: #fe7f22" v-else>(私域)</span>
                {{ row.productName }}
              </span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="sjNum" label="采购量" show-overflow-tooltip>
            <template slot-scope="{ row }">{{ row.sjNum + row.replyUnit || row.unit }}</template>
          </el-table-column>
          <el-table-column align="center" prop="originAmount" label="产品报价" show-overflow-tooltip>
            <template slot-scope="{ row }">
              <span class="table-orange">{{ row.originAmount ? '￥' + row.originAmount : '' }}{{ '元' + (row.replyUnit || item.unit ? '/' : '') + (row.replyUnit || item.unit || '') }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="amount" label="合同单价" show-overflow-tooltip>
            <template slot-scope="{ row }">
              <div class="table-flex">
                <span class="table-orange">{{ row.amount ? '￥' + row.amount : '' }}{{ '元' + (row.replyUnit || item.unit ? '/' : '') + (row.replyUnit || item.unit || '') }}</span>
                <el-tooltip class="item" effect="dark" content="该产品合同单价与供应商报价相比进行了上调" v-if="parseFloat(row.amount) - parseFloat(row.originAmount) > 0">
                  <i class="el-icon-caret-top top"></i>
                </el-tooltip>
                <el-tooltip class="item" effect="dark" content="该产品合同单价与供应商报价相比进行了下调" v-if="parseFloat(row.originAmount) - parseFloat(row.amount) > 0">
                  <i class="el-icon-caret-bottom bottom"></i>
                </el-tooltip>
              </div>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="createTime" label="变更时间" show-overflow-tooltip></el-table-column>
        </el-table>
      </div>
      <div slot="footer">
        <button type="button" class="custom-dialog-btn primary" @click="changeOpen = false">关闭</button>
      </div>
    </el-dialog>
    <!--  合同规则设置  -->
    <el-dialog v-dialogDragBox title="合同规则设置" :visible.sync="ruleOpen" width="1150px" class="custom-dialog">
      <div class="formBox">
        <div class="contractBg"><img src="~@/assets/images/contractBg.png" alt="" /></div>
        <div class="contractItem">
          <div class="contractItemTitle">设置条件</div>
          <div class="contractItemInfo">
            <el-input type="number" class="contractItemInfoInput" v-model="rulePrice.configValue" placeholder="请输入金额">
              <span slot="prefix">当前采购金额≥</span>
              <span slot="suffix">元</span>
            </el-input>
            <div class="contractItemInfoTip">
              <img src="~@/assets/images/contractQue.png" />
              <span>注:设置该条件后，将适用于所有采购合同，已完成合同不包含在内</span>
            </div>
          </div>
          <div class="contractItemDesc">将触发面部识别要求，即供应商必须完成面部识别已完成合同签署</div>
        </div>
        <div class="contractItem">
          <div class="contractItemTitle">其他条件</div>
          <div class="contractItemCheck" :class="{ checked: ruleLetter.configValue === 'true' }" @click="ruleLetter.configValue = 'true'">要求委托代理人上传授权书</div>
          <div class="contractItemCheck" :class="{ checked: ruleLetter.configValue === 'false' }" @click="ruleLetter.configValue = 'false'">不需要</div>
        </div>
      </div>
      <div slot="footer">
        <button type="button" class="custom-dialog-btn" @click="ruleOpen = false">取消</button>
        <button type="button" class="custom-dialog-btn primary" @click="handleRulesSubmit">确定</button>
      </div>
    </el-dialog>

    <!-- 授权信息 -->
    <el-dialog v-dialogDragBox title="授权信息" :visible.sync="impowerOpen" width="800px" class="custom-dialog">
      <div style="padding: 0 20px">
        <el-form ref="impowerForm" :model="impowerForm" :rules="impowerRules" label-width="8em">
          <el-form-item label="联系人姓名">
            <span>{{ impowerForm.nickName }}</span>
          </el-form-item>
          <el-form-item label="真实姓名" prop="realName">
            <el-input style="width: 60%" placeholder="请输入真实姓名" v-model="impowerForm.realName"></el-input>
          </el-form-item>
          <el-form-item label="身份证号" prop="idCard">
            <el-input style="width: 60%" placeholder="请输入身份证号" v-model="impowerForm.idCard"></el-input>
          </el-form-item>
          <el-form-item label="授权起止时间" prop="timeArr">
            <el-date-picker style="width: 60%" v-model="impowerForm.timeArr" value-format="yyyy-MM-dd HH:mm:ss" type="daterange" start-placeholder="开始日期" end-placeholder="结束日期" :default-time="['00:00:00', '23:59:59']" :picker-options="pickerOptions"></el-date-picker>
          </el-form-item>
          <el-form-item label="授权委托书" prop="authLetter">
            <image-upload :limit="1" v-model="impowerForm.authLetter" />
          </el-form-item>
          <el-form-item label="身份证复印件" prop="idCardImage">
            <image-upload :limit="1" v-model="impowerForm.idCardImage" />
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer">
        <button class="custom-dialog-btn" @click="impowerOpen = false">取 消</button>
        <button class="custom-dialog-btn primary" @click="handleSubmitImpower">确 定</button>
      </span>
    </el-dialog>

    <!--   认证信息 -->
    <el-dialog v-dialogDragBox title="认证信息" :visible.sync="certifyOpen" width="750px" class="custom-dialog">
      <div class="cretifyBox">
        <div class="cretifyImg">
          <image-preview is-list :src="certifyInfo.faceImage" :width="120" :height="120" />
        </div>
        <div class="cretifyInfo">
          <div class="cretifyItem">
            <span>姓名：</span>
            <b>{{ certifyInfo.certName }}</b>
          </div>
          <div class="cretifyItem">
            <span>电话：</span>
            <b>{{ certifyInfo.mobile }}</b>
          </div>
          <div class="cretifyItem">
            <span>身份证号：</span>
            <b>{{ certifyInfo.certNo }}</b>
          </div>
        </div>
      </div>
      <span slot="footer">
        <button class="custom-dialog-btn primary" @click="certifyOpen = false">关闭</button>
      </span>
    </el-dialog>

    <!--合同产品明细-->
    <contract-product ref="contractProduct" />
    <!--金额汇总-->
    <el-dialog v-dialogDragBox :title="totalTitle" :visible.sync="totalOpen" width="1150px" class="custom-dialog">
      <div class="formBox">
        <el-table ref="totalTable" stripe :data="approvalArr" row-key="id" style="width: 100%" :max-height="totalHeight" class="custom-table custom-table-cell10">
          <el-table-column align="center" type="index" label="序号"></el-table-column>
          <el-table-column align="center" prop="serial" label="合同编号" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="sellerName" label="供应商" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="signingTime" label="签订时间" show-overflow-tooltip v-if="queryParams.orderType != 'abnormal'"></el-table-column>
          <el-table-column align="center" prop="createTime" label="创建时间" show-overflow-tooltip v-if="queryParams.orderType == 'abnormal'"></el-table-column>
          <el-table-column align="center" prop="amount" label="订单总金额" show-overflow-tooltip>
            <template slot-scope="{ row }">
              <span class="table-orange" :class="{ 'table-cursor': row.isChange }" style="font-size: 14px">{{ row.amount ? '￥' + row.amount : '' }}{{ row.isIncludingTax ? '(含税)' : '(不含税)' }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div slot="footer" class="totalNum">
        <div class="totalNumItem">
          合计：
          <span>￥{{ totalNum }}</span>
        </div>
        <button type="button" class="custom-dialog-btn primary" @click="totalOpen = false">关闭</button>
      </div>
    </el-dialog>
    <!-- 金蝶 -->
    <inventory-create ref="inventoryCreate" v-if="inventoryOpen" @callBack="refreshList" />
    <!-- 设置无效 -->
    <purchase ref="purchase" @callback="refreshList"></purchase>
    <!-- 下推至采购订单新增 -->
    <purchase-order-batch ref="purchaseOrderBatch" @callBack="orderCreateCallBack" v-if="showOrderCreate" />
  </div>
</template>

<script>
import { contractList, contractOrderList, contractDelete, contractDetail, contractSend, contractSendRecord, contractUnsign, contractApproval, contractApprovalRecord, contractApprovalDetail, contractPriceChange } from '@/api/purchase'
import supplierDialog from '../demandForMe/supplier'
import { checkPermi } from '@/utils/permission'
import print from 'print-js'
import radarDialog from '@/views/components/radar'
import { getSupplierContact, supplier, updateSupplierContact } from '@/api/system/user'
import { privateSupb } from '@/api/houtai/siyu/gongying'
import ProductDialog from '@/views/public/product/dialog'
import { getProduct } from '@/api/system/product'
import { getPrivateduct } from '@/api/system/privateduct'
import { getMsgDetail, getRecordList } from '@/api/record'
import { isNumber, isIntegerIncludeZero } from '@/utils/validate'
import { getConfigDetail, updateConfig } from '@/api/config'
import contractProduct from '@/views/components/product/contact'
import { removeHtmlTag } from '@/utils'
import { parseTime } from '@/utils/ruoyi'
import InventoryCreate from '@/views/kingdee/purchase/inventory/create'
import { getToken } from '@/utils/auth'
import purchase from './purchase'
import ShowTemplate from './showTemplate'
import { getConfigDetail2 } from '@/api/config'
import { auditPurchaseApply, cancelPurchaseApply, deletePurchaseApply, getPurchaseApplyDetail, pushPurchaseApply, submitPurchaseApply, unAuditPurchaseApply } from '@/api/kingdee/purchase'
import purchaseOrderBatch from '@/views/kingdee/purchase/order/batch'

// 采购合同发送
export default {
  name: 'Contract',
  props: {
    contractList: {
      type: Array,
      default: () => []
    },
    isLook: {
      type: Boolean,
      default: false
    }
  },
  components: { contractProduct, ProductDialog, supplierDialog, radarDialog, InventoryCreate, purchase, ShowTemplate, purchaseOrderBatch },
  data() {
    return {
      forceCertify: false, // 是否需要实名认证
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        serial: undefined,
        keyword: undefined,
        status: undefined,
        createBy: undefined,
        orderType: 'purchase',
        ids: undefined,
        startTime: undefined,
        endTime: undefined,
        approvalStatus: undefined
      },
      classifyOptions: [
        { value: 'purchase', label: '合同列表' },
        { value: 'bidding', label: '招标采购' },
        { value: 'promotion', label: '促销品采购' },
        { value: 'abnormal', label: '其他采购' },
        { value: 'unsign', label: '未签署' }
      ],
      status: [1, 2, 3],
      statusOptions: [
        { label: '无效', value: -1, color: 'color-red' },
        { label: '正常', value: 1, color: 'color-disabled' },
        { label: '已签署', value: 2, color: 'color-success' },
        { label: '已完成', value: 3, color: 'color-blue' }
      ],
      key: 1,
      loading: true,
      list: [],
      total: 0,
      open: false,
      info: {},
      isOrder: false,
      radarOpen: false,
      radarInfo: {},
      contractSource: undefined,
      contractId: undefined,
      contractStatus: undefined,
      seller: undefined,
      userRadio: {},
      userOpen: false,
      userList: [],
      userInfo: { companyName: '', companyAddress: '', companyLegal: '' },
      isChange: false,
      otherOpen: false,
      otherInfo: {},
      otherProducts: [],
      orderNumber: undefined,
      zoom: 1,
      // 未签署列表
      unsignList: [],
      unsignLists: [],
      unsignSerial: undefined,
      // 提交审批
      approvalLoading: false,
      approvalOpen: false,
      approvalInfo: {},
      approvalForm: {},
      approvalRules: {
        bankName: [{ required: true, message: '请输入开户行', trigger: 'blur' }],
        bankNo: [{ required: true, message: '请输入银行账号', trigger: 'blur' }],
        paidAmount: [
          { required: true, message: '请输入已付金额', trigger: 'blur' },
          { validator: isNumber, message: '请输入正确的金额', trigger: 'blur' },
          { validator: isIntegerIncludeZero, message: '金额只可以填写整数', trigger: 'blur' }
        ],
        payAmount: [
          { required: true, message: '请输入付款金额', trigger: 'blur' },
          { validator: isNumber, message: '请输入正确的金额', trigger: 'blur' },
          { validator: isIntegerIncludeZero, message: '金额只可以填写整数', trigger: 'blur' }
        ],
        payee: [{ required: true, message: '请输入收款单位', trigger: 'blur' }],
        payMethod: [{ required: true, message: '请选择付款方式', trigger: 'change' }],
        remark: [{ required: true, message: '请输入备注', trigger: 'blur' }]
      },
      payMethodOptions: [
        { label: '对公', value: 'option-1708752467789' },
        { label: '对私', value: 'option-1708752467790' }
      ],
      approvalArr: [],
      approvalSeller: undefined,
      approvalOptions: [
        { label: '审批中', value: 'AUDITING' },
        { label: '已通过', value: 'PASSED' },
        { label: '已驳回', value: 'REJECTED' },
        { label: '已撤销', value: 'UNDONE' },
        { label: '通过后撤销', value: 'PASS_UNDONE' },
        { label: '已删除', value: 'DELETED' },
        { label: '已支付', value: 'ALREADY_PAY' }
      ],
      departOptions: [
        { label: '世盛金属', value: 29 },
        { label: '世盛网销', value: 48 }
      ],
      approvalMay: 'REJECTED,UNDONE,PASS_UNDONE,DELETED',
      approvalNo: 'AUDITING,PASSED,ALREADY_PAY',
      // 审批记录
      approvalRecordsOpen: false,
      approvalRecordsList: [],
      // 合同发送记录
      recordOpen: false,
      recordList: [],
      bankNames: [],
      bankNos: [],
      companyId: undefined,
      columns: [
        { key: 0, label: `序号`, visible: true },
        { key: 1, label: `合同编号`, visible: true },
        { key: 2, label: `供应商`, visible: true },
        { key: 3, label: `业务员`, visible: true },
        { key: 4, label: `业务员电话`, visible: true },
        { key: 5, label: `采购制单人`, visible: true },
        { key: 6, label: `签订时间`, visible: true },
        { key: 7, label: `签订地点`, visible: true },
        { key: 8, label: `订单总金额`, visible: true },
        { key: 9, label: `状态`, visible: true },
        { key: 10, label: `审批状态`, visible: true },
        { key: 11, label: `付款申请时间`, visible: true }
      ],
      // 价格变动
      changeOpen: false,
      changeList: [],
      contactInfo: {},
      // 合同规则设置
      ruleOpen: false,
      rulePrice: {},
      ruleLetter: {},
      impowerOpen: false,
      impowerIndex: 0,
      impowerForm: {},
      impowerRules: {
        realName: [{ required: true, message: '请输入真实姓名', trigger: 'blur' }],
        idCard: [
          { required: true, message: '请输入身份证号', trigger: 'blur' },
          { pattern: /^[1-9]\d{5}(18|19|([23]\d))\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}(\d|X|x)$/, message: '请输入正确的身份证号', trigger: 'blur' }
        ],
        timeArr: [{ required: true, message: '请选择授权起止时间', trigger: 'change' }],
        authLetter: [{ required: true, message: '请上传授权书', trigger: ['blur', 'change'] }],
        idCardImage: [{ required: true, message: '请上传身份证复印件', trigger: ['blur', 'change'] }]
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now()
        }
      },
      // 认证信息
      certifyInfo: {},
      certifyOpen: false,
      timeFormat: null,
      // 金额汇总
      totalPickerOptions: {
        shortcuts: [
          {
            text: '最近一个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近三个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近六个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 180)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近一年',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 365)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '今年',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setMonth(0)
              start.setDate(1)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '上半年',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setMonth(0)
              start.setDate(1)
              end.setMonth(5)
              end.setDate(30)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '下半年',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setMonth(6)
              start.setDate(1)
              end.setMonth(11)
              end.setDate(31)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '去年',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setFullYear(start.getFullYear() - 1)
              start.setMonth(0)
              start.setDate(1)
              end.setFullYear(end.getFullYear() - 1)
              end.setMonth(11)
              end.setDate(31)
              picker.$emit('pick', [start, end])
            }
          }
        ]
      },
      totalTitle: '',
      totalOpen: false,
      totalNum: 0,
      windowHeight: undefined,
      totalHeight: undefined,
      // 金蝶采购申请状态
      requisitionStatusOptions: [
        { label: '新建', value: 'create' },
        { label: '审核中', value: 'auditing' },
        { label: '已审核', value: 'audited' },
        { label: '重新审核', value: 'reAudit' },
        { label: '已下推', value: 'pushed' },
        { label: '已删除', value: 'delete' }
      ],
      inventoryOpen: false,
      // 合同提交审批附件
      media: {
        url: process.env.VUE_APP_BASE_API + '/wx/cp/media/upload',
        headers: { Authorization: 'Bearer ' + getToken() },
        accept: 'png,jpg,amr,mp3,mp4',
        loading: false,
        disabled: false,
        fileList: []
      },
      signet: '',
      // 金蝶批量操作
      batchOpen: false,
      batchTips: '',
      showOrderCreate: false,
      // 常用按钮配置
      commonActions: []
    }
  },
  created() {
    this.companyId = this.$store.getters.info.companyId
    this.getList()
    this.handleUnsign()
    this.queryColumns()
    this.getRules()

    // 加载常用按钮配置
    const commonActions = localStorage.getItem(this.userId + '.contractCommonActions')
    if (commonActions) this.commonActions = JSON.parse(commonActions)
  },
  watch: {
    windowHeight(val) {
      this.totalHeight = val * 0.85 - 170
    }
  },
  computed: {
    mediaLimit() {
      return 6 - (this.approvalForm.contractId ? this.approvalForm.contractId.length : 0)
    },
    // 获取当前用户ID
    userId() {
      return this.$store.getters.info.userId
    }
  },
  mounted() {
    this.windowHeight = document.documentElement.clientHeight
    window.onresize = () => {
      this.windowHeight = document.documentElement.clientHeight
    }
  },
  methods: {
    removeHtmlTag,
    checkPermi,
    // 金蝶采购申请单状态
    requisitionStatusFormat(row) {
      if (!row.requisitionStatus) return ''
      const obj = this.requisitionStatusOptions.find(item => item.value === row.requisitionStatus)
      return (obj && obj.label) || ''
    },
    // 批量刷新金蝶采购申请单状态
    async refreshKingdeeStatus(list) {
      if (!list || !list.length) return list
      
      // 只处理有requisitionNum的记录
      const needRefreshItems = list.filter(item => !!item.requisitionNum)
      if (!needRefreshItems.length) return list
      
      try {
        await Promise.all(
          needRefreshItems.map(async item => {
            try {
              const newStatus = await getPurchaseApplyDetail({ number: item.requisitionNum, refresh: true })
              if (newStatus?.data?.status) {
                item.requisitionStatus = newStatus.data.status
              }
            } catch (error) {
              console.error(`刷新金蝶状态失败 - ${item.serial}:`, error)
            }
          })
        )
      } catch (error) {
        console.error('批量刷新金蝶状态失败:', error)
      }
      
      return list
    },
    // 更新审批状态
    async updateApprovalStatus(list) {
      if (!list || !list.length) return list
      await Promise.all(
        list.map(async item => {
          if (!!item.approvalNo && item.approvalStatus !== 'PASSED') {
            try {
              const newStatus = await contractApprovalDetail({ approvalNo: item.approvalNo })
              if (newStatus.code === 200 && newStatus.msg) item.approvalStatus = newStatus.msg
            } catch (error) {
              console.error('更新审批状态失败:', error)
            }
          }
        })
      )
      return list
    },
    // 查询合同规则设置
    async getRules() {
      const price = await getConfigDetail({ configKey: 'contract.real.person.auth' })
      const letter = await getConfigDetail({ configKey: 'contract.real.person.auth.letter' })
      if (price.code === 200 && letter.code === 200) {
        this.rulePrice = price.data
        this.ruleLetter = letter.data
      }
    },
    // 列表
    async getList() {
      if (this.isLook) {
        this.list = this.contractList
        this.total = this.contractList.length
        this.key = Math.random()
        this.loading = false
      }
      if (!this.isLook) {
        this.loading = true
        let res
        const { orderId, seller, ids, orderNumber, keyword } = this.$route.query
        if (ids) {
          this.orderNumber = orderNumber
          const len = ids.split(',').length
          if (len > 10) this.queryParams.pageSize = len
          this.queryParams.ids = ids
          this.queryParams.status = this.status.toString()
          res = await contractList(this.queryParams)
          if (res.code === 200) {
            await this.updateApprovalStatus(res.rows)
            await this.refreshKingdeeStatus(res.rows)
            this.list = res.rows
            this.key = Math.random()
            this.total = res.total
            this.loading = false
          } else {
            this.$message.error(res.msg)
          }
        } else {
          if (orderId && seller) {
            this.isOrder = true
            const query = { orderId, seller, pageNum: 1, pageSize: 10 }
            res = await contractOrderList(query)
            if (res.code === 200) {
              await this.updateApprovalStatus(res.data)
              await this.refreshKingdeeStatus(res.data)
              this.list = res.data
              this.total = res.data.length
              this.key = Math.random()
              this.loading = false
            } else {
              this.$message.error(res.msg)
            }
          } else if (keyword) {
            this.queryParams.keyword = keyword
            this.queryParams.status = this.status.toString()
            res = await contractList(this.queryParams)
            if (res.code === 200) {
              await this.updateApprovalStatus(res.rows)
              await this.refreshKingdeeStatus(res.rows)
              this.list = res.rows
              this.key = Math.random()
              this.total = res.total
              this.loading = false
            } else {
              this.$message.error(res.msg)
            }
          } else {
            this.isOrder = false
            const localPageSize = localStorage.getItem('ContractPageSize')
            if (localPageSize) this.queryParams.pageSize = parseInt(localPageSize)
            this.queryParams.status = this.status.toString()
            res = await contractList(this.queryParams)
            if (res.code === 200) {
              await this.updateApprovalStatus(res.rows)
              await this.refreshKingdeeStatus(res.rows)
              this.list = res.rows
              this.key = Math.random()
              this.total = res.total
              this.loading = false
            } else {
              this.$message.error(res.msg)
            }
          }
        }
      }
    },
    // 刷新金蝶采购申请单状态
    async handleRefresh(row) {
      const newStatus = await getPurchaseApplyDetail({ number: row.requisitionNum, refresh: true })
      row.requisitionStatus = newStatus?.data?.status
    },
    // 批量刷新金蝶采购申请单状态
    async refreshBatchStatus(successItems = []) {
      if (!successItems.length) return
      
      try {
        // 并行更新所有成功操作的项目状态
        await Promise.all(
          successItems.map(async item => {
            try {
              const newStatus = await getPurchaseApplyDetail({ number: item.requisitionNum, refresh: true })
              if (newStatus?.data?.status) {
                // 在原数组中找到对应项并更新状态
                const listItem = this.list.find(listRow => listRow.id === item.id)
                if (listItem) {
                  listItem.requisitionStatus = newStatus.data.status
                }
                // 同时更新选中数组中的状态
                const approvalItem = this.approvalArr.find(approvalRow => approvalRow.id === item.id)
                if (approvalItem) {
                  approvalItem.requisitionStatus = newStatus.data.status
                }
              }
            } catch (error) {
              console.error(`刷新状态失败 - ${item.serial}:`, error)
            }
          })
        )
        
        // 强制更新表格显示
        this.$forceUpdate()
      } catch (error) {
        console.error('批量刷新状态失败:', error)
      }
    },
    // 刷新列表
    async refreshList() {
      let res
      const { orderId, seller, ids, orderNumber } = this.$route.query
      if (ids) {
        this.orderNumber = orderNumber
        const len = ids.split(',').length
        if (len > 10) this.queryParams.pageSize = len
        this.queryParams.ids = ids
        this.queryParams.status = this.status.toString()
        res = await contractList(this.queryParams)
        if (res.code === 200) {
          await this.updateApprovalStatus(res.rows)
          await this.refreshKingdeeStatus(res.rows)
          this.$set(this, 'list', res.rows)
        } else {
          this.$message.error(res.msg)
        }
      } else {
        if (orderId && seller) {
          this.isOrder = true
          const query = { orderId, seller, pageNum: 1, pageSize: 10 }
          res = await contractOrderList(query)
          if (res.code === 200) {
            await this.updateApprovalStatus(res.data)
            await this.refreshKingdeeStatus(res.data)
            this.$set(this, 'list', res.data)
          } else {
            this.$message.error(res.msg)
          }
        } else {
          this.isOrder = false
          const localPageSize = localStorage.getItem('ContractPageSize')
          if (localPageSize) this.queryParams.pageSize = parseInt(localPageSize)
          this.queryParams.status = this.status.toString()
          res = await contractList(this.queryParams)
          if (res.code === 200) {
            await this.updateApprovalStatus(res.rows)
            await this.refreshKingdeeStatus(res.rows)
            this.$set(this, 'list', res.rows)
          } else {
            this.$message.error(res.msg)
          }
        }
      }
    },
    // 切换分类
    handleCategory(item) {
      this.batchOpen = false
      this.approvalArr = []
      this.queryParams.keyword = undefined
      this.queryParams.orderType = item.value
      this.unsignSerial = undefined
      if (item.value === 'unsign') this.handleUnsign()
      if (item.value !== 'unsign') this.handleQuery()
    },
    // 状态回显
    statusFormat(row) {
      const res = this.statusOptions.find(item => item.value === row.status)
      return res ? `<span class="${res.color}">${res.label}</span>` : ''
    },
    // 审批状态回显
    approvalStatusFormat(row) {
      const res = this.approvalOptions.find(item => item.value === row.approvalStatus)
      return res ? `<span class="${res.color}">${res.label}</span>` : ''
    },
    // 审批状态回显
    approvalRecordsFormat(row) {
      const res = this.approvalOptions.find(item => item.value === row.status)
      return res ? `<span class="${res.color}">${res.label}</span>` : ''
    },
    // 选择时间
    handleTimeChange(e) {
      this.timeFormat = e
      if (e) {
        this.queryParams.startTime = e[0] + ' 00:00:00'
        this.queryParams.endTime = e[1] + ' 23:59:59'
      } else {
        this.queryParams.startTime = undefined
        this.queryParams.endTime = undefined
      }
      this.handleQuery()
    },
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 重置搜索
    resetQuery() {
      this.timeFormat = null
      this.queryParams.startTime = undefined
      this.queryParams.endTime = undefined
      this.status = [1, 2, 3]
      this.queryParams.approvalStatus = undefined
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 查看供应商详情
    handleViewSupplier(row) {
      const id = row.seller
      this.$refs.supplier.getInfo(id, row.source)
    },
    // 评价
    handleRadar(row) {
      this.$refs.radaDialog.handleOpen(row, this.queryParams.orderType)
    },
    // 查看评价
    handleViewRadar(row) {
      this.$refs.radaDialog.handleOpen(row, this.queryParams.orderType)
    },
    // 查看详情
    handleView(row) {
      this.signet = ''
      this.contractSource = row.source
      this.contractId = row.id
      this.contractStatus = row.status
      this.seller = row.seller
      const contractId = row.id
      contractDetail({ contractId }).then(res => {
        if (res.code === 200) {
          this.info = res.data
          if (this.isJSON(this.info.file)) {
            getConfigDetail2({ configKey: 'signatures', companyId: res.data.companyId, creator: res.data.creator }).then(res => {
              this.signet = res?.data?.configValue || ''
              this.open = true
              this.resizeFun()
            })
          } else {
            this.open = true
            this.resizeFun()
          }
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 无效合同
    // prettier-ignore
    handleDelete(row, type = '') {
      this.$refs.purchase.handleOpen(row, type)
    },
    // 打印
    handlePrint(data) {
      print({ printable: data.file, type: 'image', base64: true })
    },
    // 下载
    handleDownload(data) {
      const imgUrl = data.file
      const fileName = data.serial
      if (window.navigator.msSaveOrOpenBlob) {
        const bstr = atob(imgUrl.split(',')[1])
        let n = bstr.length
        const u8arr = new Uint8Array(n)
        while (n--) {
          u8arr[n] = bstr.charCodeAt(n)
        }
        const blob = new Blob([u8arr])
        window.navigator.msSaveOrOpenBlob(blob, fileName + '.' + 'png')
      } else {
        const a = document.createElement('a')
        a.href = imgUrl
        a.setAttribute('download', fileName)
        a.click()
      }
    },
    // 选择联系人
    handleUserChange(currentRow) {
      if (this.isChange) this.userRadio = currentRow
    },
    // 发送
    handleSend(info) {
      info = info || {}
      let send = []
      contractSendRecord({ contractId: this.contractId }).then(res => {
        if (res.code === 200) send = res.data
        else this.$message.error(res.msg)
      })
      this.isChange = !info.sendPhone
      this.forceCertify = false
      const id = this.seller
      const source = this.contractSource
      const rulePrice = this.rulePrice.configValue || ''
      const ruleLetter = this.ruleLetter.configValue || 'false'
      this.userInfo = {}
      if (source === 'common') {
        supplier({ id }).then(res => {
          if (res.code === 200) {
            this.userInfo.companyName = res.data.supplier.name
            this.userInfo.companyAddress = res.data.company ? res.data.company.address : ''
            this.userInfo.companyLegal = res.data.supplier.legal
            res.data.contacts.map(item => (item.isSite = !!item.userId))
            this.userList = res.data.contacts
            this.userOpen = true
            if (info.sendPhone) this.userRadio = res.data.contacts.find(item => item.phone === info.sendPhone) || {}
            else this.userRadio = res.data.contacts.find(item => item.checked) || {}
            if (ruleLetter === 'true' && Number(rulePrice) <= info.amount && (!this.userRadio.authLetter || !this.timeOver(this.userRadio.authTimeEnd))) {
              this.userRadio = {}
            }
            res.data.contacts.map(item => {
              item.nums = send.filter(i => i.phone === item.phone).length
            })
          } else this.$message.error(res.msg)
        })
      } else {
        privateSupb({ id }).then(res => {
          if (res.code === 200) {
            this.userInfo.companyName = res.data.name
            this.userInfo.companyAddress = res.data.address
            this.userInfo.companyLegal = res.data.legal
            this.userOpen = true
            res.data.contactList.map(item => {
              item.nums = send.filter(i => i.phone === item.phone).length
              item.isSite = !!item.userId
            })
            this.$set(this, 'userList', res.data.contactList)
            if (data.sendPhone) this.userRadio = res.data.contactList.find(item => item.phone == info.sendPhone) || {}
            else this.userRadio = res.data.contactList.find(item => item.checked) || {}
            if (ruleLetter === 'true' && Number(rulePrice) <= info.amount && !this.userRadio.authLetter && !this.timeOver(this.userRadio.authTimeEnd)) {
              this.userRadio = {}
            }
          } else this.$message.error(res.msg)
        })
      }
    },
    // 发送提交
    handleSendSubmit() {
      const forceCertify = this.forceCertify
      const contractId = this.contractId
      const phone = this.userRadio.phone
      const uuid = this.userRadio.uuid
      const reg = /^1[3456789]\d{9}$/
      if (!reg.test(phone)) {
        this.$message.error('请输入正确的手机号')
        return
      }
      let data = { contractId, phone, uuid, userId: undefined, info: undefined, image: undefined, forceCertify }
      if (this.userRadio.isSite) data.userId = this.userRadio.userId
      data.info = `买方：${this.info.buyerName}给您发送了一份合同，需您查看并确认签署。`
      contractSend(data).then(res => {
        if (res.code === 200) {
          this.$message.success('发送成功')
          this.userOpen = false
          this.userRadio = {}
        } else this.$message.error(res.msg)
      })
    },
    // 返回按钮
    handleClose() {
      const obj = { path: '/demand/orderlist' }
      this.$tab.closeOpenPage(obj)
    },
    // 其他采购详情
    async handleOtherView(row) {
      let data = JSON.parse(row.products) || []
      await Promise.all(
        data.map(item => {
          if (item.source === 'common') {
            return getProduct(item.productId).then(res => {
              item.product = res.data
            })
          } else {
            return getPrivateduct(item.productId).then(res => {
              item.product = res.data
            })
          }
        })
      )
      privateSupb({ id: row.seller }).then(res => {
        if (res.code === 200) {
          let index = res.data.contactList.findIndex(item => item.checked)
          if (index === -1) index = 0
          this.contactInfo = res.data.contactList[index]
          this.otherInfo = { ...row }
          this.otherProducts = data
          this.otherOpen = true
        }
      })
    },
    // 产品详情
    handleProductView(data) {
      this.$refs.productInfo.handleView(data)
    },
    // 调整合同图片大小
    resizeFun() {
      const devicePixelRatio = window.devicePixelRatio
      if (devicePixelRatio !== 1) {
        if (!!window.ActiveXObject || 'ActiveXObject' in window) {
        } else this.zoom = 1 / devicePixelRatio
      }
    },
    handleUnsign() {
      contractUnsign().then(res => {
        const { code, msg, data } = res
        if (code === 200) this.unsignList = this.unsignLists = data
        else this.$message.error(msg)
      })
    },
    // 未签署合同搜索
    handleUnsignQuery() {
      if (this.unsignSerial) {
        const arr = [...this.unsignLists]
        this.unsignList = arr.filter(item => item.serial === this.unsignSerial)
      } else this.unsignList = this.unsignLists
    },
    resetUnsignQuery() {
      this.unsignSerial = undefined
      this.handleUnsignQuery()
    },
    // 打开未被签署的合同
    handleOpenUnsign() {
      this.queryParams.orderType = 'unsign'
      this.handleUnsign()
      this.$notify.closeAll()
    },
    // 提交审批
    handleApproval(row) {
      let salesman = ''
      if (row.sendUser && row.sendPhone) salesman = row.sendUser + '(' + row.sendPhone + ')'
      if (row.sendUser && !row.sendPhone) salesman = row.sendUser
      if (!row.sendUser && row.sendPhone) salesman = row.sendPhone
      this.approvalInfo = {
        sellerName: row.sellerName,
        amount: row.amount,
        salesman
      }
      this.approvalForm = {
        bankName: row.bankName,
        bankNo: row.bankNo,
        paidAmount: undefined,
        payAmount: undefined,
        payee: undefined,
        contractId: [row.id],
        payMethod: undefined,
        depart: 29,
        remark: undefined,
        media: []
      }
      this.media.loading = false
      this.media.disabled = false
      this.media.fileList = []
      this.resetForm('approvalForm')
      this.approvalLoading = false
      this.approvalOpen = true
    },
    // 批量审批
    approvalsFormat(val = 0) {
      let str = '合同皆未签署，无法提交审批'
      let statusArr = []
      if (this.queryParams.orderType === 'abnormal') statusArr = this.approvalArr.filter(row => !row.approvalNo || (!!row.approvalNo && row.approvalStatus != 'AUDITING'))
      else statusArr = this.approvalArr.filter(row => row.status > 1 && (!row.approvalNo || (!!row.approvalNo && row.approvalStatus != 'AUDITING')))
      const sellerArr = Array.from(new Set(statusArr.map(item => item.seller)))
      if (val === 0) {
        if (sellerArr.length === 1) {
          if (statusArr.length < 7) str = '提交审批'
          else str = '超出最大审批数量'
        }
        if (sellerArr.length > 1) str = '批量提交审批只可以选择一家供应商'
        return str
      } else {
        if (sellerArr.length === 1) {
          return statusArr.length < 7
        }
        return sellerArr.length === 1
      }
    },
    // 批量提交审批
    handleApprovals() {
      if (!this.approvalsFormat(1)) return
      this.bankNames = []
      this.bankNos = []
      // 数组去重
      const arr = this.approvalArr.map(item => item.sellerName)
      const newArr = Array.from(new Set(arr)).toString()
      const amount = this.approvalArr.reduce((total, item) => {
        return parseFloat((total + item.amount).toFixed(5))
      }, 0)
      const sales = this.approvalArr.map(item => {
        if (item.sendUser && item.sendPhone) return item.sendUser + '(' + item.sendPhone + ')'
        if (item.sendUser && !item.sendPhone) return item.sendUser
        if (!item.sendUser && item.sendPhone) return item.sendPhone
      })
      const salesman = Array.from(new Set(sales)).toString()
      this.approvalInfo = { sellerName: newArr, amount, salesman }
      const bankName = this.approvalArr[0].bankName
      const bankNo = this.approvalArr[0].bankNo
      const bankNames = Array.from(new Set(this.approvalArr.map(item => item.bankName)))
      bankNames.map(item => {
        this.bankNames.push({ value: item })
      })
      const bankNos = Array.from(new Set(this.approvalArr.map(item => item.bankNo)))
      bankNos.map(item => {
        this.bankNos.push({ value: item })
      })
      const contractId = this.approvalArr.map(item => item.id)
      this.approvalForm = {
        bankName: bankName,
        bankNo: bankNo,
        contractId: contractId,
        payMethod: undefined,
        depart: 29,
        remark: undefined,
        media: []
      }
      this.media.loading = false
      this.media.disabled = false
      this.media.fileList = []
      this.resetForm('approvalForm')
      this.approvalLoading = false
      this.approvalOpen = true
    },
    // 金额汇总
    handleTotal() {
      let total = 0
      this.approvalArr.map(item => {
        total += item.amount
      })
      this.totalNum = total
      this.totalOpen = true
      this.totalTitle = parseTime(this.queryParams.startTime, '{y}年{m}月{d}日') + '至' + parseTime(this.queryParams.endTime, '{y}年{m}月{d}日') + '合同金额汇总'
    },
    // 关闭多选
    handleCloseApproval() {
      this.$nextTick(() => {
        if (this.$refs.allTable) this.$refs.allTable.clearSelection()
      })
      this.approvalArr = []
      this.approvalSeller = undefined
    },
    // 提交审批
    submitForApproval() {
      this.$refs.approvalForm.validate(valid => {
        if (valid) {
          this.approvalLoading = true
          this.approvalForm.media = this.media.fileList.map(item => item.response && item.response.msg)
          contractApproval(this.approvalForm).then(res => {
            if (res.code === 200) {
              this.approvalOpen = false
              this.$message.success('提交成功')
              this.refreshList()
              this.approvalLoading = false
            } else {
              this.$message.error(res.msg)
              setTimeout(() => {
                this.approvalLoading = false
              }, 1000)
            }
          })
        }
      })
    },
    selectable(row) {
      if (this.batchOpen) {
        // 仅可以选择有金蝶采购单申请单编码requisitionNum和状态requisitionStatus不为delete、pushed的合同
        return !!(row.requisitionNum && row.requisitionStatus !== 'delete' && row.requisitionStatus !== 'pushed')
      } else {
        if (this.timeFormat) return true
        const selectArr = this.$refs.allTable.selection
        const state = selectArr.some(item => item.id == row.id)
        if (this.approvalSeller) {
          if (this.queryParams.orderType === 'abnormal') return (selectArr.length < 6 || state) && row.seller === this.approvalSeller && (!row.approvalNo || (!!row.approvalNo && row.approvalStatus != 'AUDITING'))
          else return (selectArr.length < 6 || state) && row.status > 1 && row.seller === this.approvalSeller && (!row.approvalNo || (!!row.approvalNo && row.approvalStatus != 'AUDITING'))
        } else {
          if (this.queryParams.orderType === 'abnormal') return (selectArr.length < 6 || state) && (!row.approvalNo || (!!row.approvalNo && row.approvalStatus != 'AUDITING'))
          else return (selectArr.length < 6 || state) && row.status > 1 && (!row.approvalNo || (!!row.approvalNo && row.approvalStatus != 'AUDITING'))
        }
      }
    },
    // 多选申请审批
    handleSelectionChange(val) {
      if (val.length) this.approvalSeller = val[0].seller
      else this.approvalSeller = undefined
      this.approvalArr = val
    },
    // 审批记录
    handleApprovalRecord(row) {
      contractApprovalRecord({ contractId: row.id }).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          if (data.length) {
            this.approvalRecordsList = data
            this.approvalRecordsOpen = true
          } else this.$message.info('暂无审批记录')
        } else this.$message.error(msg)
      })
    },
    // 发送记录
    handleSendRecording(row) {
      getRecordList({ contractId: row.id }).then(async res => {
        if (res.code === 200) {
          const list = res.data
          if (list.length) {
            await Promise.all(
              list.map(async item => {
                const statusData = await getMsgDetail({
                  phone: item.phone,
                  bizId: item.bizId || '',
                  day: this.parseTime(item.createTime, '{y}{m}{d}')
                })
                if (statusData.code === 200) {
                  if (statusData.data.length) {
                    item.status = statusData.data[0].sendStatus
                  } else item.status = 1
                }
              })
            )
          }
          this.recordList = list
          this.recordOpen = true
        } else this.$message.error(res.msg)
      })
    },
    recordStatus(row) {
      if (row.status === 1) return '等待回执'
      if (row.status === 2) return '发送失败'
      if (row.status === 3) return '发送成功'
    },
    querySearchBankName(queryString, cb) {
      let restaurants = this.bankNames
      let results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants
      // 调用 callback 返回建议列表的数据
      cb(results)
    },
    querySearchBankNo(queryString, cb) {
      let restaurants = this.bankNos
      let results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants
      // 调用 callback 返回建议列表的数据
      cb(results)
    },
    createFilter(queryString) {
      return restaurant => {
        return restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0
      }
    },
    handleSelect(item) {},
    // 查询显隐列缓存
    queryColumns() {
      const data = localStorage.getItem('ContractColumns')
      if (data) this.columns = JSON.parse(data)
    },
    // 改变显隐列缓存
    updateColumns(data = []) {
      localStorage.setItem('ContractColumns', JSON.stringify(data))
    },
    // 查询合同金额变动详情
    handleAmount(row) {
      if (row.isChange) {
        contractPriceChange({ contractId: row.id }).then(res => {
          const { code, msg, data } = res
          if (code === 200) {
            this.changeList = data
            this.changeOpen = true
          } else this.$message.error(msg)
        })
      }
    },
    // 产品详情
    handleViewProduct(Id, item) {
      if (item.source === 'common') {
        getProduct(Id).then(res => {
          this.$refs.productInfo.handleView(res.data)
        })
      } else {
        getPrivateduct(Id).then(res => {
          this.$refs.productInfo.handleView(res.data)
        })
      }
    },
    // 查询企业参数包含的合同规则设置
    handleRules() {
      this.ruleOpen = true
    },
    // 合同规则设置
    handleRulesSubmit() {
      const arr = [
        { configId: this.rulePrice.configId, configKey: this.rulePrice.configKey, configValue: this.rulePrice.configValue },
        { configId: this.ruleLetter.configId, configKey: this.ruleLetter.configKey, configValue: this.ruleLetter.configValue }
      ]
      // 循环提交arr
      let num = 0
      arr.forEach(item => {
        updateConfig(item).then(res => {
          if (res.code === 200) {
            num++
            if (num === arr.length) {
              this.$message.success('设置成功')
              this.ruleOpen = false
            }
          } else this.$message.error(res.msg)
        })
      })
    },
    // 检查授权书有效期是否过期
    timeOver(authTimeEnd) {
      const now = new Date().getTime()
      const time = new Date(authTimeEnd).getTime()
      return time >= now
    },
    // 授权信息初始化
    handleInitImpower() {
      this.impowerForm = {
        realName: undefined,
        idCard: undefined,
        authTimeStart: undefined,
        authTimeEnd: undefined,
        timeArr: undefined,
        authLetter: undefined,
        idCardImage: undefined
      }
      this.resetForm('impowerForm')
    },
    // 添加/修改授权
    handleAddImpower(row, index) {
      this.handleInitImpower()
      const { uuid } = row
      getSupplierContact({ uuid }).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          this.impowerForm = data
          if (data.authTimeStart && data.authTimeEnd) {
            this.$set(this.impowerForm, 'timeArr', [data.authTimeStart, data.authTimeEnd])
          }
          this.impowerIndex = index
          this.impowerOpen = true
        } else this.$message.error(msg)
      })
    },
    // 提交授权
    handleSubmitImpower() {
      this.$refs.impowerForm.validate(valid => {
        if (valid) {
          let { uuid, post, phone, nickName, realName, idCard, timeArr, authLetter, idCardImage } = this.impowerForm
          const [authTimeStart, authTimeEnd] = timeArr
          const data = { uuid, post, phone, nickName, realName, idCard, authTimeStart, authTimeEnd, authLetter, idCardImage }
          updateSupplierContact(data).then(res => {
            const { code, msg } = res
            if (code === 200) {
              this.$message.success('授权成功')
              this.impowerOpen = false
              const arr = [...this.userList]
              arr[this.impowerIndex] = { ...arr[this.impowerIndex], ...data }
              this.userList = [...arr]
            } else this.$message.error(msg)
          })
        }
      })
    },
    // 查看认证信息
    handleAutonym(info = {}) {
      this.certifyInfo = info.certify || {}
      this.certifyOpen = true
    },
    // 改变是否需要实名认证
    handleForceCertify() {
      this.isChange = !this.forceCertify
      if (this.forceCertify) {
        if (!this.userRadio.authLetter || !this.timeOver(this.userRadio.authTimeEnd)) this.userRadio = {}
      }
    },
    // 查看产品明细
    async handleProductDetail(row) {
      const { contractType } = row
      if (contractType == 'abnormal') {
        let data = JSON.parse(row.products) || []
        await Promise.all(
          data.map(async item => {
            if (item.source === 'common') {
              const res = await getProduct(item.productId)
              item.product = res.data
            } else {
              const res_1 = await getPrivateduct(item.productId)
              item.product = res_1.data
            }
          })
        )
        this.$refs.contractProduct.handleView(data, 'abnormal')
        return
      }
      contractPriceChange({ contractId: row.id }).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          if (data) this.$refs.contractProduct.handleView(data)
          else this.$message.info('暂无产品明细')
        } else this.$message.error(msg)
      })
    },
    // 推送至金蝶
    handleKingdee(row, type = undefined) {
      this.inventoryOpen = true
      this.$nextTick(() => {
        this.$refs.inventoryCreate.handleOpen(row, type)
      })
    },
    // 金蝶详情
    handleKingdeeDetail(row) {
      this.inventoryOpen = true
      this.$nextTick(() => {
        this.$refs.inventoryCreate.getInfo(row)
      })
    },
    // 上传附件删除
    handleMediaRemove(file, fileList) {
      this.media.disabled = fileList.length === this.mediaLimit
      this.media.fileList = fileList
    },
    // 上传附件成功
    handleMediaSuccess(response, file, fileList) {
      this.media.disabled = fileList.length === this.mediaLimit
      this.media.loading = false
      this.media.fileList = fileList
    },
    // 上传附件进度
    handleMediaProgress(event, file, fileList) {
      this.media.loading = true
    },
    // 上传附件前
    handleMediaBeforeUpload(file) {
      const suffix = file.name.split('.').pop()
      const size = file.size / 1024 / 1024
      if (!this.media.accept.includes(suffix)) {
        this.$message.error(`请上传${this.media.accept}格式文件`)
        return false
      }
      if (suffix === 'jpg' && size > 1) {
        this.$message.error('图片大小不能超过1M')
        return false
      }
      if ((suffix === 'amr' || suffix === 'mp3') && size > 2) {
        this.$message.error('音频大小不能超过2M')
        return false
      }
      if (suffix === 'mp4' && size > 10) {
        this.$message.error('视频大小不能超过10M')
        return false
      }
      return true
    },
    // 判断是不是json
    isJSON(str) {
      try {
        JSON.parse(str)
        return true
      } catch (e) {
        return false
      }
    },
    // 判断是不是base64
    isBase64Image(str) {
      if (typeof str === 'string') {
        return str.startsWith('data:image/') && str.includes(';base64,')
      }
      return false
    },
    // 金蝶批量操作
    handleBatchOpen(e) {
      this.batchOpen = e
      if (e && this.approvalArr.length) {
        this.approvalArr = []
        this.$refs.allTable.clearSelection()
      }
    },
    batchDisabled() {
      return !(this.batchOpen && this.approvalArr.length)
    },
    // 金蝶提交
    handleKingdeeSubmit() {
      if (!this.approvalArr.length) {
        this.$message.warning('请选择需要提交的数据')
        return
      }
      let errorMsg = []
      let successItems = []
      Promise.all(
        this.approvalArr.map(async item => {
          try {
            const res = await submitPurchaseApply({ number: item.requisitionNum })
            if (res.code !== 200) {
              errorMsg.push(`${item.serial}: ${res.msg || '提交失败'}`)
            } else {
              successItems.push(item)
            }
          } catch (error) {
            errorMsg.push(`${item.serial}: ${error.message || '提交失败'}`)
          }
        })
      ).then(async () => {
        if (errorMsg.length) {
          this.$alert(errorMsg.join('\n'), '提示', {
            confirmButtonText: '确定',
            type: 'warning'
          })
        } else {
          this.$message.success('提交成功')
        }
        // 刷新成功操作项的状态
        if (successItems.length) {
          await this.refreshBatchStatus(successItems)
        }
      })
    },
    // 金蝶审核
    handleKingdeeAudit() {
      if (!this.approvalArr.length) {
        this.$message.warning('请选择需要审核的数据')
        return
      }
      let errorMsg = []
      let successItems = []
      Promise.all(
        this.approvalArr.map(async item => {
          try {
            const res = await auditPurchaseApply({ number: item.requisitionNum })
            if (res.code !== 200) {
              errorMsg.push(`${item.serial}: ${res.msg || '审核失败'}`)
            } else {
              successItems.push(item)
            }
          } catch (error) {
            errorMsg.push(`${item.serial}: ${error.message || '审核失败'}`)
          }
        })
      ).then(async () => {
        if (errorMsg.length) {
          this.$alert(errorMsg.join('\n'), '提示', {
            confirmButtonText: '确定',
            type: 'warning'
          })
        } else {
          this.$message.success('审核成功')
        }
        // 刷新成功操作项的状态
        if (successItems.length) {
          await this.refreshBatchStatus(successItems)
        }
      })
    },
    // 金蝶撤销
    handleKingdeeRevoke() {
      if (!this.approvalArr.length) {
        this.$message.warning('请选择需要撤销的数据')
        return
      }
      let errorMsg = []
      let successItems = []
      Promise.all(
        this.approvalArr.map(async item => {
          try {
            const res = await cancelPurchaseApply({ number: item.requisitionNum })
            if (res.code !== 200) {
              errorMsg.push(`${item.serial}: ${res.msg || '撤销失败'}`)
            } else {
              successItems.push(item)
            }
          } catch (error) {
            errorMsg.push(`${item.serial}: ${error.message || '撤销失败'}`)
          }
        })
      ).then(async () => {
        if (errorMsg.length) {
          this.$alert(errorMsg.join('\n'), '提示', {
            confirmButtonText: '确定',
            type: 'warning'
          })
        } else {
          this.$message.success('撤销成功')
        }
        // 刷新成功操作项的状态
        if (successItems.length) {
          await this.refreshBatchStatus(successItems)
        }
      })
    },
    // 金蝶删除
    handleKingdeeDelete() {
      if (!this.approvalArr.length) {
        this.$message.warning('请选择需要删除的数据')
        return
      }
      let errorMsg = []
      Promise.all(
        this.approvalArr.map(async item => {
          try {
            const res = await deletePurchaseApply({ number: item.requisitionNum })
            if (res.code !== 200) {
              errorMsg.push(`${item.serial}: ${res.msg || '删除失败'}`)
            }
          } catch (error) {
            errorMsg.push(`${item.serial}: ${error.message || '删除失败'}`)
          }
        })
      ).then(() => {
        if (errorMsg.length) {
          this.$alert(errorMsg.join('\n'), '提示', {
            confirmButtonText: '确定',
            type: 'warning'
          })
        } else {
          this.$message.success('删除成功')
          this.getList()
          this.approvalArr = []
          this.$refs.allTable.clearSelection()
        }
      })
    },
    // 金蝶反审
    handleKingdeeUnAudit() {
      if (!this.approvalArr.length) {
        this.$message.warning('请选择需要反审的数据')
        return
      }
      let errorMsg = []
      let successItems = []
      Promise.all(
        this.approvalArr.map(async item => {
          try {
            const res = await unAuditPurchaseApply({ number: item.requisitionNum })
            if (res.code !== 200) {
              errorMsg.push(`${item.serial}: ${res.msg || '反审失败'}`)
            } else {
              successItems.push(item)
            }
          } catch (error) {
            errorMsg.push(`${item.serial}: ${error.message || '反审失败'}`)
          }
        })
      ).then(async () => {
        if (errorMsg.length) {
          this.$alert(errorMsg.join('\n'), '提示', {
            confirmButtonText: '确定',
            type: 'warning'
          })
        } else {
          this.$message.success('反审成功')
        }
        // 刷新成功操作项的状态
        if (successItems.length) {
          await this.refreshBatchStatus(successItems)
        }
      })
    },
    // 金蝶批量下推
    handleKingdeePush() {
      this.showOrderCreate = true
      this.$nextTick(() => {
        this.$refs.purchaseOrderBatch.detail(this.approvalArr)
      })
    },
    // 下推至采购订单新增回调
    orderCreateCallBack() {
      this.showOrderCreate = false
      this.approvalArr = []
      this.approvalSeller = undefined
      this.$nextTick(() => {
        if (this.$refs.allTable) this.$refs.allTable.clearSelection()
      })
    },
    // 获取所有操作按钮配置
    getAllActions(row) {
      const actions = []
      // 查看详情按钮 - 始终显示
      actions.push({
        key: 'detail',
        label: '查看详情',
        className: 'table-btn',
        show: true,
        handler: this.queryParams.orderType === 'abnormal' ? this.handleOtherView : this.handleView
      })
      // 根据不同类型和条件添加其他按钮
      if (this.queryParams.orderType === 'purchase' || this.queryParams.orderType === 'bidding' || this.queryParams.orderType === 'promotion') {
        // 金蝶操作按钮
        if (this.companyId === 14 && row.status > -1) {
          if (!row.requisitionStatus || row.requisitionStatus == 'delete') {
            actions.push({
              key: 'kingdeeCreate',
              label: '金蝶新建',
              className: 'table-btn primary',
              show: true,
              handler: this.handleKingdee
            })
          } else {
            actions.push({
              key: 'kingdeeDetail',
              label: '金蝶详情',
              className: 'table-btn primary hasbg',
              show: true,
              handler: this.handleKingdeeDetail
            })
          }
        }
        // 无效合同按钮
        actions.push({
          key: 'invalid',
          label: '无效合同',
          className: 'table-btn danger',
          show: row.status != -1 && this.checkPermi(['purchasing:contract:del']),
          handler: this.handleDelete
        })
        // 收货评价按钮
        actions.push({
          key: 'radar',
          label: '收货评价',
          className: 'table-btn primary',
          show: row.status === 2,
          handler: this.handleRadar
        })
        // 查看评价按钮
        actions.push({
          key: 'viewRadar',
          label: '查看评价',
          className: 'table-btn primary',
          show: row.status === 3,
          handler: this.handleViewRadar
        })
        // 企业ID为14的专用按钮
        if (this.companyId === 14) {
          actions.push({
            key: 'approval',
            label: '提交审批',
            className: 'table-btn success',
            show: row.status > 1 && (!row.approvalNo || (!!row.approvalNo && row.approvalStatus != 'AUDITING')),
            handler: this.handleApproval
          })

          actions.push({
            key: 'approvalRecord',
            label: '审批记录',
            className: 'table-btn orange',
            show: row.status > 1 && !!row.approvalNo && this.approvalNo.indexOf(row.approvalStatus) != -1,
            handler: this.handleApprovalRecord
          })
        }
      }
      if (this.queryParams.orderType === 'abnormal') {
        // 金蝶操作按钮
        if (this.companyId === 14) {
          if (!row.requisitionStatus || row.requisitionStatus == 'delete') {
            actions.push({
              key: 'kingdeeCreateAbnormal',
              label: '金蝶新建',
              className: 'table-btn primary',
              show: true,
              handler: row => this.handleKingdee(row, 'abnormal')
            })
          } else {
            actions.push({
              key: 'kingdeeDetailAbnormal',
              label: '金蝶详情',
              className: 'table-btn primary hasbg',
              show: true,
              handler: this.handleKingdeeDetail
            })
          }
          actions.push({
            key: 'invalidAbnormal',
            label: '无效合同',
            className: 'table-btn danger',
            show: row.status != -1 && this.checkPermi(['purchasing:contract:del']),
            handler: row => this.handleDelete(row, 'abnormal')
          })
          actions.push({
            key: 'approvalAbnormal',
            label: '提交审批',
            className: 'table-btn success',
            show: !row.approvalNo || (!!row.approvalNo && row.approvalStatus != 'AUDITING'),
            handler: this.handleApproval
          })
          actions.push({
            key: 'approvalRecordAbnormal',
            label: '审批记录',
            className: 'table-btn orange',
            show: !!row.approvalNo && this.approvalNo.indexOf(row.approvalStatus) != -1,
            handler: this.handleApprovalRecord
          })
        }
      }
      return actions
    },
    // 获取常用按钮
    getCommonActions(row) {
      const allActions = this.getAllActions(row).filter(action => action.show)
      const commonActions = allActions.filter(action => this.isCommonAction(action.key))
      // 如果没有设置常用按钮，默认显示查看详情
      if (commonActions.length === 0) {
        const detailAction = allActions.find(action => action.key === 'detail')
        return detailAction ? [detailAction] : []
      }
      // 常用按钮不限制数量
      return commonActions
    },
    // 判断是否为常用按钮
    isCommonAction(actionKey) {
      return this.commonActions.includes(actionKey)
    },
    // 判断是否有更多操作按钮
    hasMoreActions(row) {
      const allActions = this.getAllActions(row).filter(action => action.show)
      const commonActions = this.getCommonActions(row)
      // 如果显示的按钮数量大于常用按钮数量，则显示更多操作
      return allActions.length >= commonActions.length
    },
    // 切换常用按钮设置
    toggleCommonAction(actionKey) {
      const index = this.commonActions.indexOf(actionKey)
      if (index > -1) {
        this.commonActions.splice(index, 1)
      } else {
        // 允许设置多个常用按钮
        this.commonActions.push(actionKey)
      }
      localStorage.setItem(this.userId + '.contractCommonActions', JSON.stringify(this.commonActions))
    },
    // 计算操作列宽度
    getActionColumnWidth() {
      // 如果列表为空，返回默认宽度
      if (!this.list || this.list.length === 0) {
        return 220
      }
      // 取第一行数据来计算宽度
      const firstRow = this.list[0]
      const commonActions = this.getCommonActions(firstRow)
      const hasMoreActions = this.hasMoreActions(firstRow)
      // 常用按钮数量 + 更多操作按钮(如果有的话) * 110px
      const buttonCount = commonActions.length + (hasMoreActions ? 1 : 0)
      const width = buttonCount * 110
      // 设置最小宽度为 110px，最大宽度为 550px
      return Math.max(110, Math.min(550, width))
    }
  },
  beforeDestroy() {
    this.$notify.closeAll()
  }
}
</script>

<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
.Box {
  padding: 15px 20px;
}
.custom-dialog ::v-deep {
  .print {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 0 20px;
    &-item {
      padding: 0 50px;
      height: 50px;
      line-height: 50px;
      text-align: center;
      margin-left: 10px;
      border: 1px solid #cbd6e2;
      border-radius: 5px;
      font-size: 16px;
      color: $white;
      cursor: pointer;
      background-color: $blue;
      &:hover {
        opacity: 0.8;
      }
    }
  }
  .totalNum {
    display: flex;
    justify-content: space-between;
    align-items: center;
    &Item {
      display: inline-flex;
      align-items: center;
      font-size: 16px;
      span {
        color: $orange;
        font-size: 18px;
      }
    }
  }
}
.custom-back {
  padding: 7px 20px;
  background-color: $white;
  box-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.06);
  .back-button {
    font-size: 16px;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    padding: 10px 0;
    color: $font;
    &:hover {
      color: $blue;
    }
  }
}
::v-deep {
  .formBox {
    padding: 0 20px;
    .el-form-item__label {
      font-weight: normal;
      color: #333333;
    }
    .el-textarea__inner {
      font-family: inherit;
    }
  }
  .custom-table .disabledSelection .cell .el-checkbox__inner {
    display: none;
    position: relative;
  }
  .radarBox {
    padding: 0 20px;
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 10px;
    &-item {
      width: 33.33%;
      display: inline-flex;
      flex-direction: row;
      font-size: 14px;
      color: #333333;
      margin: 7px 0;
      .el-rate {
        margin-left: 10px;
      }
    }
  }
  .send {
    &-title {
      font-size: 14px;
      margin-bottom: 10px;
      color: #666666;
      display: flex;
      align-items: center;
      padding: 5px 0;
      line-height: 30px;
    }
    &-box {
      padding: 0 30px;
      border: 1px solid #cbd6e2;
      border-radius: 5px;
      margin-bottom: 10px;
      background-color: #fafafb;
      .el-row {
        .el-col {
          border-bottom: 1px solid #cbd6e2;
          font-size: 14px;
          line-height: 55px;
          color: #333333;
          span {
            color: #666666;
          }
          &.border0 {
            border-width: 0;
          }
        }
      }
    }
  }
}
.desc {
  margin: 0 0 20px;
  ::v-deep {
    .desc-label {
      width: calc(6.5em + 20px) !important;
    }
  }
}
.custom-table ::v-deep {
  .table-flex {
    display: flex;
    justify-content: center;
    align-items: center;
    .top {
      font-size: 24px;
      color: red;
    }
    .bottom {
      font-size: 24px;
      color: green;
    }
  }
  .table-cursor {
    cursor: pointer;
  }
  .custom-badge {
    .el-badge__content.is-fixed.is-dot {
      top: 3px;
    }
  }
  .el-form-item {
    margin-top: 10px !important;
    margin-bottom: 10px !important;
    .el-form-item__error {
      top: 95%;
      padding-top: 0;
    }
  }
}
.confirm-total {
  margin-top: 10px;
  background-color: #fbe4ea;
  border: 1px solid $red;
  border-radius: 5px;
  width: 100%;
  height: 48px;
  padding: 0 20px;
  font-size: 14px;
  color: $font;
  span {
    margin-right: 50px;
  }
  b {
    font-size: 18px;
    font-weight: 500;
    color: $red;
  }
}
.orderNumber {
  margin-top: 2px;
  padding: 10px 15px;
  background-color: #ffffff;
  font-size: 14px;
  color: #666666;
  box-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.06);
}
::v-deep .isSite {
  position: relative;
  .el-switch__core {
    height: 24px;
    border-radius: 12px;
    min-width: 50px;
    &:after {
      left: 4px;
      top: 3px;
    }
  }
  &.el-switch {
    &.is-checked {
      .el-switch__core {
        &:after {
          margin-left: -20px;
          left: 100%;
        }
      }
    }
  }
  &.is-checked {
    .el-switch__label--left {
      opacity: 0;
    }
    .el-switch__label--right {
      opacity: 1;
    }
  }
  .el-switch__label {
    position: absolute;
    top: 0;
  }
  .el-switch__label--left {
    right: 0;
    color: #999999;
    z-index: 1;
    margin-right: 8px;
  }
  .el-switch__label--right {
    left: 0;
    color: #ffffff;
    opacity: 0;
    margin-left: 8px;
  }
}
.classify-tip {
  height: 30px;
  padding: 0 10px;
  line-height: 30px;
  margin-top: 8px;
  margin-left: -10px;
  background-color: rgba(245, 14, 14, 0.1);
  color: #f50e0e;
  border-radius: 8px;
  position: relative;
  &:before {
    content: '';
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    left: -10px;
    width: 0;
    height: 0;
    border-width: 10px 10px 10px 0;
    border-style: solid;
    border-color: transparent rgba(245, 14, 14, 0.1) transparent transparent;
  }
}
.formBoxBg {
  padding-top: 20px;
  background-color: #f0f3f9;
  position: relative;
  &::before {
    content: '';
    position: absolute;
    top: -16px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-width: 0 16px 16px 16px;
    border-style: solid;
    border-color: transparent transparent #f0f3f9 transparent;
  }
}
.formBox-desc {
  display: flex;
  align-items: center;
  padding: 0 20px 20px;
  .formBox-item {
    margin-right: 20px;
    line-height: 40px;
    span {
      color: $font;
    }
    span:first-child {
      color: #999999;
    }
    span.primary {
      color: $blue;
    }
    span.orange {
      color: $orange;
    }
  }
}
.contract {
  &Bg {
    width: 100%;
    margin-bottom: 30px;
    img {
      width: 100%;
    }
  }
  &Item {
    display: flex;
    line-height: 46px;
    padding-bottom: 20px;
    &:last-child {
      border-bottom: 1px solid #e2e6f3;
    }
    &Title {
      width: 100px;
      font-size: 14px;
      color: #666666;
    }
    &Info {
      display: flex;
      flex-direction: column;
      width: 450px;
      &Input {
        width: 430px;
        height: 46px;
        ::v-deep {
          .el-input__inner {
            height: 46px;
            line-height: 46px;
            padding-left: 105px;
            padding-right: 50px;
          }
          input::-webkit-outer-spin-button,
          input::-webkit-inner-spin-button {
            -webkit-appearance: none;
          }
          input[type='number'] {
            -moz-appearance: textfield;
          }
        }
      }
      &Tip {
        display: flex;
        align-items: center;
        font-size: 12px;
        color: #9eaec4;
        img {
          width: 20px;
          height: 20px;
          margin-right: 10px;
        }
      }
    }
    &Desc {
      color: #f43f3f;
      font-size: 14px;
    }
    &Check {
      cursor: pointer;
      padding: 0 15px;
      min-width: 140px;
      text-align: center;
      height: 46px;
      line-height: 44px;
      color: #666666;
      border: 1px solid #d7dadc;
      border-radius: 5px;
      margin-right: 15px;
      background: url('~@/assets/images/contractCheck.png') no-repeat 100% bottom;
      background-size: 28px 28px;
      &.checked,
      &:hover {
        background: url('~@/assets/images/contractChecked.png') no-repeat 100% bottom #e5eeff;
        background-size: 28px 28px;
        border-color: #2e73f3;
        color: #2e73f3;
      }
    }
  }
}
.cretifyBox {
  padding: 0 20px;
  display: flex;
  align-items: center;
}
.cretifyImg {
  flex: 0 0 120px;
  margin-right: 20px;
}
.cretifyInfo {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
}
.cretifyItem {
  display: flex;
  align-items: flex-start;
  line-height: 40px;
  font-size: 14px;
}
.upload-tip {
  display: inline-block;
  margin-left: 10px;
  font-size: 12px;
  color: #999999;
  span {
    color: #f43f3f;
  }
}
.custom-push-target {
  .el-col.el-col-12 {
    .el-radio {
      height: 30px;
      display: flex;
      align-items: center;
    }
  }
}
</style>
