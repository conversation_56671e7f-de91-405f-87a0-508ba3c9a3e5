<template>
  <div :style="{ padding: isView ? '10px 0' : '20px 20px 0' }">
    <div class="contractBox" :style="{ zoom: zoom }">
      <div class="contractBox-title">采购合同</div>
      <div class="contractBox-desc">
        <div class="inline-flex flex-column flex-align-start">
          <div>
            <div class="desc-item inline-flex">
              <span>买方：</span>
              <b>{{ info.buyerName }}</b>
            </div>
            <div class="desc-item inline-flex">
              <span>合同编号：</span>
              <b style="padding-left: 15px">{{ info.serial }}</b>
            </div>
          </div>
          <div>
            <div class="desc-item inline-flex">
              <span>卖方：</span>
              <b>{{ info.sellerName }}</b>
            </div>
            <div class="desc-item inline-flex">
              <span>签订地点：</span>
              <b>{{ info.address }}</b>
            </div>
          </div>
          <div>
            <div class="desc-item inline-flex"></div>
            <div class="desc-item inline-flex">
              <span>签订时间：</span>
              <b>{{ info.signingTime }}</b>
            </div>
          </div>
        </div>
        <div class="inline-flex flex-row align-center">
          <img v-if="certify" class="certify" src="~@/assets/images/certify.png" />
          <div id="qrcode" ref="qrcode"></div>
        </div>
      </div>
      <div class="contractBox-htitle"><span>产品采购清单</span></div>
      <el-table ref="contractTable" stripe :data="info.list" style="width: 100%" class="custom-table custom-table-cell10">
        <el-table-column align="center" type="index" label="序号"></el-table-column>
        <el-table-column align="center" prop="productName" label="产品名称" show-overflow-tooltip min-width="150">
          <template slot-scope="{ row }">
            <span class="table-link">
              <span v-if="row.source === 'common'">(公域)</span>
              <span style="color: #fe7f22" v-if="row.source === 'private'">(私域)</span>
              {{ row.productName }}
            </span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="specs" label="规格"></el-table-column>
        <el-table-column align="center" prop="model" label="型号"></el-table-column>
        <el-table-column align="center" prop="amount" label="产品单价">
          <template slot-scope="scope">
            <span class="table-orange">{{ '￥' + parseFloat(scope.row.amount) + '/' + (scope.row.replyUnit || scope.row.unit || '') }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="quantity" label="采购量">
          <template slot-scope="scope">
            <span>{{ scope.row.sjNum + (scope.row.replyUnit || scope.row.unit || '') }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="remark" label="备注"></el-table-column>
      </el-table>
      <div class="contractBox-total">
        <div class="total-box">
          <div class="total-item">
            <span>
              共
              <b>{{ info.list.length || 0 }}</b>
              件产品
            </span>
          </div>
          <div class="total-item">
            <span>
              订单总金额：
              <b>￥ {{ getTotals(info) }}</b>
            </span>
          </div>
          <div class="total-item">
            <span>
              人民币总金额：
              <b>{{ lowerConversionUpper(getTotals(info)) }}</b>
            </span>
          </div>
        </div>
        <div class="total-box" style="justify-content: flex-start">
          <div class="total-item" style="margin-right: 110px">
            <span>
              是否含税：
              <el-switch v-model="info.isIncludingTax" active-text="含税" inactive-text="不含税" disabled></el-switch>
            </span>
          </div>
          <div class="total-tip">
            <span>注：不包含运费、安装费</span>
          </div>
        </div>
      </div>
      <div style="margin-top: 20px">
        <div style="margin-bottom: 10px; text-align: left" v-html="file"></div>
      </div>
      <div class="contractBox-tableBox relative">
        <el-image class="signet" :src="signet" v-if="signet" />
        <ul>
          <li class="inline-flex">
            <span>卖方：</span>
            <b>{{ info.sellerName }}</b>
          </li>
          <li class="inline-flex">
            <span>买方：</span>
            <b>{{ info.buyerName }}</b>
          </li>
          <li class="inline-flex">
            <span>法定代表人：</span>
            <b>{{ info.sellerInfo.nickName }}</b>
          </li>
          <li class="inline-flex">
            <span>法定代表人：</span>
            <b>{{ info.buyerInfo.nickName }}</b>
          </li>
          <li class="inline-flex relative">
            <span>委托代理人：</span>
            <b style="width: 80px">{{ info.sellerInfo.consignor }}</b>
            <span style="margin-left: 10px">法人或委托代理人签字：</span>
            <img class="signature" :src="signature" v-if="signature" />
          </li>
          <li class="inline-flex">
            <span>委托代理人：</span>
            <b>{{ info.buyerInfo.consignor }}</b>
          </li>
          <li class="inline-flex">
            <span>电话和微信号码：</span>
            <b>{{ info.sellerInfo.phone }}</b>
          </li>
          <li class="inline-flex">
            <span>电话和微信号码：</span>
            <b>{{ info.buyerInfo.phone }}</b>
          </li>
          <li class="inline-flex">
            <span>开户行：</span>
            <b>{{ info.sellerInfo.bank }}</b>
          </li>
          <li class="inline-flex">
            <span>开户行：</span>
            <b>{{ info.buyerInfo.bank }}</b>
          </li>
          <li class="inline-flex">
            <span>账号：</span>
            <b>{{ info.sellerInfo.account }}</b>
          </li>
          <li class="inline-flex">
            <span>账号：</span>
            <b>{{ info.buyerInfo.account }}</b>
          </li>
          <li class="inline-flex">
            <span>地址：</span>
            <b>{{ info.sellerInfo.address }}</b>
          </li>
          <li class="inline-flex">
            <span>地址：</span>
            <b>{{ info.buyerInfo.address }}</b>
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script>
import QRCode from 'qrcodejs2'
import { lowerConversionUpper } from '@/utils'

export default {
  props: {
    isView: {
      type: Boolean,
      default: false
    },
    content: {
      type: String || Object,
      default: ''
    },
    // 手签字
    signature: {
      type: String,
      default: ''
    },
    // 电子章
    signet: {
      type: String,
      default: ''
    },
    // 是否认证
    certify: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      info: { list: [] },
      file: '',
      qrcode: undefined,
      zoom: 1
    }
  },
  created() {
    this.getZoom()
  },
  watch: {
    content: {
      deep: true,
      immediate: true,
      handler(val) {
        const info = JSON.parse(val)
        this.info = info
        this.file = info.content
        this.$nextTick(() => {
          if (this.$refs.qrcode) this.$refs.qrcode.innerHTML = ''
          const url = this.$router.resolve({
            path: '/prewview',
            query: {
              type: 'contract',
              requestId: info.serial
            }
          })
          const http = window.location.origin
          new QRCode(this.$refs.qrcode, {
            width: 100, // 二维码宽度
            height: 100, // 二维码高度
            text: http + url.href
          })
        })
      }
    }
  },
  mounted() {
    window.addEventListener('resize', this.getZoom)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.getZoom)
  },
  methods: {
    lowerConversionUpper,
    // 获取合同金额
    getTotals(data) {
      const arr = data.list || []
      let total = 0
      arr.map(item => {
        // const amount = item.checked[0].amount
        // const sjNum = item.checked[0].sjNum
        total += Number(item.amount) * Number(item.sjNum)
      })
      return parseFloat(total.toFixed(3)) || 0
    },
    getZoom() {
      const width = window.innerWidth - 20
      if (width > 1110) return
      this.zoom = width / 1110
    }
  }
}
</script>

<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';
.contractBox {
  min-width: 1110px;
  padding: 20px 20px 10px;
  border: 1px solid #cbd6e2;
  border-radius: 5px;
  background-color: $white;
  margin-bottom: 10px;
  .custom-table {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    border-bottom: 0;
    .el-form-item {
      margin-top: 10px !important;
      margin-bottom: 10px !important;
      .el-form-item__error {
        top: 95%;
        padding-top: 0;
      }
    }
    .danger {
      border-color: #ec4545;
      color: #ec4545;
      &:hover {
        background-color: #ec4545;
      }
    }
  }
  &-title {
    font-size: 20px;
    font-weight: 500;
    color: $info;
    text-align: center;
    margin: 10px 0 30px;
  }
  &-desc {
    display: flex;
    justify-content: space-between;
    padding: 0 25px;
    margin-bottom: 5px;
    .desc-right {
      min-width: 320px;
    }
    .desc-item {
      width: 320px;
      font-size: 14px;
      line-height: 28px;
      .el-input__inner {
        text-align: left;
        font-size: 12px;
      }
      span {
        color: $info;
        text-align: right;
        &.el-input__prefix {
          width: auto;
        }
      }
      b {
        color: $font;
        font-weight: 500;
      }
      & + .desc-item {
        margin-left: 30px;
      }
    }
  }
  &-htitle {
    margin: 10px 0;
    font-size: 16px;
    span {
      color: $info;
    }
    b {
      font-weight: 500;
      color: $font;
    }
  }
  &-total {
    display: flex;
    flex-direction: column;
    border: 1px solid #ec4545;
    background: #fff5f5;
    height: 96px;
    line-height: 48px;
    padding: 0 20px;
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
    .total-box {
      display: inline-flex;
      justify-content: space-between;
      align-items: center;
    }
    .total-item {
      span {
        font-size: 14px;
        color: $info;
      }
      b {
        font-size: 18px;
        font-weight: 500;
        color: $orange;
      }
    }
    .total-tip {
      font-size: 12px;
      color: $orange;
    }
  }
  &-tableBox {
    width: 100%;
    margin: 15px 0;
    ul {
      width: 100%;
      list-style: none;
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      padding: 0;
      margin: 0;
      border: 1px solid #cbd6e2;
      border-radius: 5px;
      border-bottom-width: 0;
      overflow: hidden;
    }
    li {
      width: 50%;
      line-height: 20px;
      padding: 15px 20px;
      border-right: 1px solid #cbd6e2;
      border-bottom: 1px solid #cbd6e2;
      font-size: 12px;
      &:nth-child(2n) {
        border-right-width: 0;
      }
      &:nth-child(4n + 1),
      &:nth-child(4n + 2) {
        background-color: #f8f9fb;
      }
      span {
        color: $disabled;
        margin-right: 20px;
      }
      b {
        color: $font;
        font-weight: 500;
      }
    }
    .contractBox-input {
      .el-input__inner {
        text-align: left;
        font-size: 12px;
      }
    }
  }
  &-input {
    .el-input__inner {
      border-width: 0;
      border-bottom-width: 1px;
      border-radius: 0;
      background-color: transparent;
      text-align: center;
      font-size: 14px;
      color: $font;
    }
    &.input-date {
      .el-input__inner {
        text-align: left;
      }
    }
    &.input-left {
      .el-input__inner {
        text-align: left;
      }
    }
  }
}
.relative {
  position: relative;
}
.signature {
  position: absolute;
  top: 0;
  right: 0;
  width: 210px;
  height: 50px;
}
.signet {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  right: 170px;
  width: 186px;
  height: 186px;
}
.certify {
  width: 90px;
  height: 90px;
  margin-right: 20px;
}
</style>
