<template>
  <div>
    <!--  选择联系人  -->
    <el-dialog v-dialogDragBox title="发送合同" :visible.sync="userOpen" width="1150px" class="custom-dialog">
      <div style="padding: 0 20px">
        <div class="send-title">公司基本信息</div>
        <div class="send-box">
          <el-row>
            <el-col :span="12">
              <span>公司名称：</span>
              {{ userInfo.companyName }}
            </el-col>
            <el-col :span="12">
              <span>公司法人：</span>
              {{ userInfo.companyLegal }}
            </el-col>
            <el-col :span="24" class="border0">
              <span>公司地址：</span>
              {{ removeHtmlTag(userInfo.companyAddress, 300) }}
            </el-col>
          </el-row>
        </div>
        <div class="send-title">
          <span style="color: #333333; font-size: 16px; margin-right: 10px">请选择发送合同接收人</span>
          <span>是否需要实名认证：</span>
          <el-switch v-model="forceCertify" @change="handleForceCertify" active-text="是" inactive-text="否" class="isSite"></el-switch>
        </div>
        <el-table v-loading="loading" ref="userTable" stripe :data="userList" row-key="id" style="width: 100%" class="custom-table custom-table-cell10">
          <el-table-column align="center" label="选择" width="55">
            <template slot-scope="{ row }">
              <el-radio v-model="userRadio.phone" :label="row.phone" v-if="isChange"><span /></el-radio>
              <template v-else>
                <el-tooltip class="item" effect="dark" :content="row.authLetter && !timeOver(row.authTimeEnd) ? '授权书已过期' : '需补充授权书'" placement="top" v-if="(!row.authLetter || !timeOver(row.authTimeEnd)) && ((ruleLetter.configValue === 'true' && Number(rulePrice.configValue) <= info.amount) || Number(rulePrice.configValue) <= info.amount)">
                  <el-radio v-model="userRadio.phone" :label="row.phone" disabled><span /></el-radio>
                </el-tooltip>
                <template v-else>
                  <el-tooltip class="item" effect="dark" :content="row.authLetter && !timeOver(row.authTimeEnd) && forceCertify ? '授权书已过期' : '需补充授权书'" placement="top" v-if="(!row.authLetter || !timeOver(row.authTimeEnd)) && forceCertify">
                    <el-radio v-model="userRadio.phone" :label="row.phone" disabled><span /></el-radio>
                  </el-tooltip>
                  <el-radio v-model="userRadio.phone" :label="row.phone" @click.native.prevent="handleUserChange(row)" disabled v-else><span /></el-radio>
                </template>
              </template>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="nickName" label="联系人姓名" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="phone" label="联系人电话" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="post" label="联系人职务" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="nums" label="发送次数" show-overflow-tooltip>
            <template slot-scope="{ row }">{{ row.nums ? `已发送${row.nums}次` : '未发送' }}</template>
          </el-table-column>
          <el-table-column align="center" prop="post" label="授权有效期">
            <template slot-scope="scope">
              <el-tooltip class="item" effect="dark" :content="`有效期至：${parseTime(scope.row.authTimeEnd, '{y}年{m}月{d}日')}`" placement="top" v-if="!!scope.row.authTimeEnd">
                <el-button type="text" icon="el-icon-edit" size="small" @click="handleAddImpower(scope.row, scope.$index)">修改授权</el-button>
              </el-tooltip>
              <el-button type="text" icon="el-icon-plus" size="small" @click="handleAddImpower(scope.row, scope.$index)" v-else>添加授权</el-button>
            </template>
          </el-table-column>
          <el-table-column align="center" label="站内通知">
            <template slot-scope="{ row }">
              <el-switch class="isSite" :disabled="!row.userId" v-model="row.isSite" active-text="开" inactive-text="关"></el-switch>
            </template>
          </el-table-column>
          <el-table-column align="center" label="注册公司名称" show-overflow-tooltip>
            <template slot-scope="{ row }">{{ row.companyName }}</template>
          </el-table-column>
          <el-table-column align="center" label="注册昵称" show-overflow-tooltip>
            <template slot-scope="{ row }">{{ row.userName }}</template>
          </el-table-column>
        </el-table>
      </div>
      <div slot="footer">
        <button type="button" class="custom-dialog-btn" @click="userOpen = false">取消</button>
        <button type="button" class="custom-dialog-btn primary" :class="{ disabled: !userRadio.phone }" :disabled="!userRadio.phone" @click="handleSendSubmit">确认并发送</button>
      </div>
    </el-dialog>

    <!-- 授权信息 -->
    <el-dialog v-dialogDragBox title="授权信息" :visible.sync="impowerOpen" width="800px" class="custom-dialog">
      <div style="padding: 0 20px">
        <el-form ref="impowerForm" :model="impowerForm" :rules="impowerRules" label-width="8em">
          <el-form-item label="联系人姓名">
            <span>{{ impowerForm.nickName }}</span>
          </el-form-item>
          <el-form-item label="真实姓名" prop="realName">
            <el-input style="width: 60%" placeholder="请输入真实姓名" v-model="impowerForm.realName"></el-input>
          </el-form-item>
          <el-form-item label="身份证号" prop="idCard">
            <el-input style="width: 60%" placeholder="请输入身份证号" v-model="impowerForm.idCard"></el-input>
          </el-form-item>
          <el-form-item label="授权起止时间" prop="timeArr">
            <el-date-picker style="width: 60%" v-model="impowerForm.timeArr" value-format="yyyy-MM-dd HH:mm:ss" type="daterange" start-placeholder="开始日期" end-placeholder="结束日期" :default-time="['00:00:00', '23:59:59']" :picker-options="pickerOptions"></el-date-picker>
          </el-form-item>
          <el-form-item label="授权委托书" prop="authLetter">
            <image-upload :limit="1" v-model="impowerForm.authLetter" />
          </el-form-item>
          <el-form-item label="身份证复印件" prop="idCardImage">
            <image-upload :limit="1" v-model="impowerForm.idCardImage" />
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer">
        <button class="custom-dialog-btn" @click="impowerOpen = false">取 消</button>
        <button class="custom-dialog-btn primary" @click="handleSubmitImpower">确 定</button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { contractDetail, contractSend, contractSendRecord } from '@/api/purchase'
import { getSupplierContact, supplier, updateSupplierContact } from '@/api/system/user'
import { privateSupb } from '@/api/houtai/siyu/gongying'
import { getConfigDetail } from '@/api/config'
import { getToken } from '@/utils/auth'
import { removeHtmlTag } from '@/utils'
// 采购合同发送
export default {
  data() {
    return {
      forceCertify: false, // 是否需要实名认证
      contractId: undefined,
      userOpen: false,
      userRadio: {
        phone: ''
      },
      userInfo: {},
      userList: [],
      loading: false,
      isChange: false,
      info: {},
      rulePrice: {},
      ruleLetter: {},
      impowerOpen: false,
      impowerIndex: 0,
      impowerForm: {},
      impowerRules: {
        realName: [{ required: true, message: '请输入真实姓名', trigger: 'blur' }],
        idCard: [
          { required: true, message: '请输入身份证号', trigger: 'blur' },
          { pattern: /^[1-9]\d{5}(18|19|([23]\d))\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}(\d|X|x)$/, message: '请输入正确的身份证号', trigger: 'blur' }
        ],
        timeArr: [{ required: true, message: '请选择授权起止时间', trigger: 'change' }],
        authLetter: [{ required: true, message: '请上传授权书', trigger: ['blur', 'change'] }],
        idCardImage: [{ required: true, message: '请上传身份证复印件', trigger: ['blur', 'change'] }]
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now()
        }
      }
    }
  },
  methods: {
    removeHtmlTag,
    // 查询合同规则设置
    async getRules() {
      const price = await getConfigDetail({ configKey: 'contract.real.person.auth' })
      const letter = await getConfigDetail({ configKey: 'contract.real.person.auth.letter' })
      if (price.code === 200 && letter.code === 200) {
        this.rulePrice = price.data
        this.ruleLetter = letter.data
      }
    },
    // 选择联系人
    handleUserChange(currentRow) {
      if (this.isChange) this.userRadio = currentRow
    },
    // 发送
    handleSend(contractId = undefined) {
      if (contractId) {
        if (!!getToken()) this.getRules()
        this.forceCertify = false
        this.contractId = contractId
        const rulePrice = this.rulePrice.configValue || ''
        const ruleLetter = this.ruleLetter.configValue || 'false'
        contractDetail({ contractId }).then(res => {
          const { code, msg, data } = res
          if (code === 200) {
            this.info = res.data
            let send = []
            contractSendRecord({ contractId }).then(res => {
              if (res.code === 200) send = res.data
              else this.$message.error(res.msg)
            })
            this.isChange = !data.sendPhone
            const id = data.seller
            const source = data.source
            this.userInfo = {}
            if (source === 'common') {
              supplier({ id }).then(res => {
                if (res.code === 200) {
                  this.userInfo.companyName = res.data.supplier.name
                  this.userInfo.companyAddress = res.data.company ? res.data.company.address : ''
                  this.userInfo.companyLegal = res.data.supplier.legal
                  this.userList = res.data.contacts
                  this.userOpen = true
                  if (data.sendUser && data.sendPhone) this.userRadio = res.data.contacts.find(item => item.phone == data.sendPhone) || {}
                  else this.userRadio = res.data.contacts.find(item => item.checked) || {}
                  if (ruleLetter === 'true' && Number(rulePrice) <= data.amount && (!this.userRadio.authLetter || !this.timeOver(this.userRadio.authTimeEnd))) {
                    this.userRadio = {}
                  }
                  res.data.contacts.map(item => {
                    item.nums = send.filter(i => i.phone === item.phone).length
                  })
                } else this.$message.error(res.msg)
              })
            } else {
              privateSupb({ id }).then(res => {
                if (res.code === 200) {
                  this.userInfo.companyName = res.data.name
                  this.userInfo.companyAddress = res.data.address
                  this.userInfo.companyLegal = res.data.legal
                  this.userOpen = true
                  res.data.contactList.map(item => {
                    item.nums = send.filter(i => i.phone === item.phone).length
                    item.isSite = !!item.userId
                  })
                  this.$set(this, 'userList', res.data.contactList)
                  if (data.sendUser && data.sendPhone) this.userRadio = res.data.contactList.find(item => item.phone == data.sendPhone) || {}
                  else this.userRadio = res.data.contactList.find(item => item.checked) || {}
                  if (ruleLetter === 'true' && Number(rulePrice) <= data.amount && !this.userRadio.authLetter && !this.timeOver(this.userRadio.authTimeEnd)) {
                    this.userRadio = {}
                  }
                } else this.$message.error(res.msg)
              })
            }
          } else this.$message.error(msg)
        })
      } else this.$message.error('请求失败，请联系管理员')
    },
    // 发送提交
    handleSendSubmit() {
      const forceCertify = this.forceCertify
      const contractId = this.contractId
      const phone = this.userRadio.phone
      const uuid = this.userRadio.uuid
      const reg = /^1[3456789]\d{9}$/
      if (!reg.test(phone)) {
        this.$message.error('请输入正确的手机号')
        return
      }
      let data = { contractId, phone, uuid, userId: undefined, info: undefined, image: undefined, forceCertify }
      if (this.userRadio.isSite) data.userId = this.userRadio.userId
      data.info = `买方：${this.info.buyerName}给您发送了一份合同，需您查看并确认签署。`
      contractSend(data).then(res => {
        if (res.code === 200) {
          this.$message.success('已成功生成合同并发送成功')
          this.userOpen = false
          this.userRadio = {}
          this.$emit('callBack')
        } else this.$message.error(res.msg)
      })
    },
    // 检查授权书有效期是否过期
    timeOver(authTimeEnd) {
      const now = new Date().getTime()
      const time = new Date(authTimeEnd).getTime()
      return time >= now
    },
    // 授权信息初始化
    handleInitImpower() {
      this.impowerForm = {
        realName: undefined,
        idCard: undefined,
        authTimeStart: undefined,
        authTimeEnd: undefined,
        timeArr: undefined,
        authLetter: undefined,
        idCardImage: undefined
      }
      this.resetForm('impowerForm')
    },
    // 添加/修改授权
    handleAddImpower(row, index) {
      this.handleInitImpower()
      const { uuid } = row
      getSupplierContact({ uuid }).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          this.impowerForm = data
          if (data.authTimeStart && data.authTimeEnd) {
            this.$set(this.impowerForm, 'timeArr', [data.authTimeStart, data.authTimeEnd])
          }
          this.impowerIndex = index
          this.impowerOpen = true
        } else this.$message.error(msg)
      })
    },
    // 提交授权
    handleSubmitImpower() {
      this.$refs.impowerForm.validate(valid => {
        if (valid) {
          let { uuid, post, phone, nickName, realName, idCard, timeArr, authLetter, idCardImage } = this.impowerForm
          const [authTimeStart, authTimeEnd] = timeArr
          const data = { uuid, post, phone, nickName, realName, idCard, authTimeStart, authTimeEnd, authLetter, idCardImage }
          updateSupplierContact(data).then(res => {
            const { code, msg } = res
            if (code === 200) {
              this.$message.success('授权成功')
              this.impowerOpen = false
              const arr = [...this.userList]
              arr[this.impowerIndex] = { ...arr[this.impowerIndex], ...data }
              this.userList = [...arr]
            } else this.$message.error(msg)
          })
        }
      })
    },
    // 改变是否需要实名认证
    handleForceCertify() {
      if (this.forceCertify) {
        if (!this.userRadio.authLetter || !this.timeOver(this.userRadio.authTimeEnd)) this.userRadio = {}
      }
    }
  }
}
</script>

<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';
::v-deep {
  .formBox {
    padding: 0 20px;
    .el-form-item__label {
      font-weight: normal;
      color: #333333;
    }
    .el-textarea__inner {
      font-family: inherit;
    }
  }
  .radarBox {
    padding: 0 20px;
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 10px;
    &-item {
      width: 33.33%;
      display: inline-flex;
      flex-direction: row;
      font-size: 14px;
      color: #333333;
      margin: 7px 0;
      .el-rate {
        margin-left: 10px;
      }
    }
  }
  .send {
    &-title {
      font-size: 14px;
      margin-bottom: 10px;
      color: #666666;
      display: flex;
      align-items: center;
      padding: 5px 0;
      line-height: 30px;
    }
    &-box {
      padding: 0 30px;
      border: 1px solid #cbd6e2;
      border-radius: 5px;
      margin-bottom: 10px;
      background-color: #fafafb;
      .el-row {
        .el-col {
          border-bottom: 1px solid #cbd6e2;
          font-size: 14px;
          line-height: 55px;
          color: #333333;
          span {
            color: #666666;
          }
          &.border0 {
            border-width: 0;
          }
        }
      }
    }
  }
}
::v-deep .isSite {
  position: relative;
  .el-switch__core {
    height: 24px;
    border-radius: 12px;
    min-width: 50px;
    &:after {
      left: 4px;
      top: 3px;
    }
  }
  &.el-switch {
    &.is-checked {
      .el-switch__core {
        &:after {
          margin-left: -20px;
          left: 100%;
        }
      }
    }
  }
  &.is-checked {
    .el-switch__label--left {
      opacity: 0;
    }
    .el-switch__label--right {
      opacity: 1;
    }
  }
  .el-switch__label {
    position: absolute;
    top: 0;
  }
  .el-switch__label--left {
    right: 0;
    color: #999999;
    z-index: 1;
    margin-right: 8px;
  }
  .el-switch__label--right {
    left: 0;
    color: #ffffff;
    opacity: 0;
    margin-left: 8px;
  }
}
</style>
