<template>
  <div>
    <el-dialog v-dialogDragBox title="合同模板" :visible.sync="listShow" width="1150px" class="custom-dialog">
      <div style="padding: 0 20px">
        <el-row :gutter="10" class="mb20" v-if="!isUse && list.length < 6">
          <el-col :span="1.5">
            <el-button type="primary" plain size="small" icon="el-icon-plus" @click="handleAdd">新增模板</el-button>
          </el-col>
        </el-row>
        <el-table v-loading="loading" :data="list" style="width: 100%" class="custom-table" v-if="list.length">
          <el-table-column align="center" type="index" label="序号"></el-table-column>
          <el-table-column prop="name" label="合同标题" show-overflow-tooltip>
            <template slot-scope="{ row }">
              <el-button type="text" size="small" @click="handleDetail(row)">{{ row.name }}</el-button>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="status" label="是否默认模板">
            <template slot-scope="{ row }">
              <template>
                <el-switch v-model="row.isDefault" active-text="是" inactive-text="否" class="table-switch" @change="handleDefault(row, $event)" v-if="row.id !== -1"></el-switch>
              </template>
            </template>
          </el-table-column>
          <el-table-column prop="address" label="操作" align="center" width="150" v-hasPermi="['contract:template:modify']">
            <template slot-scope="{ row }">
              <template v-if="!isUse">
                <template v-if="row.id !== -1">
                  <el-button type="text" icon="el-icon-delete" @click="handleDelete(row)" size="mini">删除</el-button>
                  <el-button type="text" icon="el-icon-edit" @click="handleUpdate(row)" size="mini">修改</el-button>
                </template>
              </template>
              <el-button type="text" icon="el-icon-paperclip" @click="handleUse(row)" size="mini" v-if="isUse">选择</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="none" v-else>
          <div>暂无合同模板</div>
          <el-button type="text" @click="handleAdd">去新增</el-button>
          <div style="margin-left: 10px">或使用</div>
          <el-button type="text" @click="handleUseDefault">默认模板</el-button>
        </div>
      </div>
      <div slot="footer">
        <button type="button" class="custom-dialog-btn primary" @click="listShow = false">关闭</button>
      </div>
    </el-dialog>

    <el-dialog v-dialogDragBox :title="title" :visible.sync="open" width="1150px" class="custom-dialog">
      <div style="padding: 0 20px">
        <el-form ref="form" :model="form" :rules="rules" label-width="6em">
          <el-form-item label="合同标题" prop="name" v-if="!isRead">
            <el-input v-model="form.name" placeholder="请输入合同标题"></el-input>
          </el-form-item>
          <el-form-item label="合同内容" prop="file">
            <div class="contractBox">
              <div class="contractBox-title">采购合同</div>
              <div class="contractBox-desc">
                <div class="inline-flex flex-column flex-align-start">
                  <div>
                    <div class="desc-item inline-flex">
                      <span>买方：</span>
                      <b>河北世盛金属制品有限公司</b>
                    </div>
                    <div class="desc-item inline-flex">
                      <span>合同编号：</span>
                      <b style="padding-left: 15px">ZYK-8888888888</b>
                    </div>
                  </div>
                  <div>
                    <div class="desc-item inline-flex">
                      <span>卖方：</span>
                      <b>河北世盛金属制品有限公司</b>
                    </div>
                    <div class="desc-item inline-flex">
                      <span>签订地点：</span>
                      <b>河北省邯郸市永年区</b>
                    </div>
                  </div>
                  <div>
                    <div class="desc-item inline-flex"></div>
                    <div class="desc-item inline-flex">
                      <span>签订时间：</span>
                      <b>{{ parseTime(new Date()) }}</b>
                    </div>
                  </div>
                </div>
                <div class="inline-flex flex-column flex-align-start">
                  <div id="qrcode" ref="qrcode"></div>
                </div>
              </div>
              <div class="contractBox-htitle"><span>产品采购清单</span></div>
              <el-table ref="contractTable" stripe :data="productList" style="width: 100%" class="custom-table custom-table-cell10">
                <el-table-column align="center" type="index" label="序号"></el-table-column>
                <el-table-column align="center" prop="productName" label="产品名称" show-overflow-tooltip min-width="150">
                  <template slot-scope="{ row }">
                    <span class="table-link">(公域){{ row.productName }}</span>
                  </template>
                </el-table-column>
                <el-table-column align="center" prop="specs" label="规格"></el-table-column>
                <el-table-column align="center" prop="model" label="型号"></el-table-column>
                <el-table-column align="center" prop="unit" label="单位" width="50"></el-table-column>
                <el-table-column align="center" prop="remark" label="备注"></el-table-column>
                <el-table-column align="center" prop="amount" label="产品单价">
                  <template slot-scope="scope">
                    <span class="table-orange">{{ scope.row.amount ? '￥' + parseFloat(scope.row.amount) : '' }}</span>
                  </template>
                </el-table-column>
                <el-table-column align="center" prop="quantity" label="采购数量">
                  <template slot-scope="scope">
                    <span>{{ scope.row.quantity }}</span>
                  </template>
                </el-table-column>
              </el-table>
              <div class="contractBox-total">
                <div class="total-box">
                  <div class="total-item">
                    <span>
                      共
                      <b>{{ productList.length }}</b>
                      件产品
                    </span>
                  </div>
                  <div class="total-item">
                    <span>
                      订单总金额：
                      <b>￥ 888888.88</b>
                    </span>
                  </div>
                  <div class="total-item">
                    <span>
                      人民币总金额：
                      <b>捌拾捌万捌仟捌佰捌拾捌元整</b>
                    </span>
                  </div>
                </div>
                <div class="total-box" style="justify-content: flex-start">
                  <div class="total-item" style="margin-right: 110px">
                    <span>
                      是否含税：
                      <el-switch v-model="isIncludingTax" active-text="含税" inactive-text="不含税" disabled></el-switch>
                    </span>
                  </div>
                  <div class="total-tip">
                    <span>注：不包含运费、安装费</span>
                  </div>
                </div>
              </div>
              <div style="margin-top: 20px">
                <editor :key="key" v-model="form.file" :readOnly="isRead" :min-height="500" />
              </div>
              <div class="contractBox-tableBox">
                <ul>
                  <li>
                    <span>卖方：</span>
                    <b>河北世盛金属制品有限公司</b>
                  </li>
                  <li>
                    <span>买方：</span>
                    <b>河北世盛金属制品有限公司</b>
                  </li>
                  <li class="inline-flex">
                    <span>法定代表人：</span>
                    <b>XXX</b>
                  </li>
                  <li class="inline-flex">
                    <span>法定代表人：</span>
                    <b>XXX</b>
                  </li>
                  <li class="inline-flex">
                    <span>委托代理人：</span>
                    <b style="width: 80px">XXX</b>
                    <span style="margin-left: 10px">法人或委托代理人签字：</span>
                  </li>
                  <li class="inline-flex">
                    <span>委托代理人：</span>
                    <b>XXX</b>
                  </li>
                  <li class="inline-flex">
                    <span>电话和微信号码：</span>
                    <b>18888888888</b>
                  </li>
                  <li class="inline-flex">
                    <span>电话和微信号码：</span>
                    <b>18888888888</b>
                  </li>
                  <li class="inline-flex">
                    <span>开户行：</span>
                    <b>中国银行</b>
                  </li>
                  <li class="inline-flex">
                    <span>开户行：</span>
                    <b>中国银行</b>
                  </li>
                  <li class="inline-flex">
                    <span>账号：</span>
                    <b>6288888888888888888</b>
                  </li>
                  <li class="inline-flex">
                    <span>账号：</span>
                    <b>6288888888888888888</b>
                  </li>
                  <li class="inline-flex">
                    <span>地址：</span>
                    <b>河北省邯郸市永年区</b>
                  </li>
                  <li class="inline-flex">
                    <span>地址：</span>
                    <b>河北省邯郸市永年区</b>
                  </li>
                </ul>
              </div>
            </div>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer">
        <template v-if="isRead">
          <button type="button" class="custom-dialog-btn primary" @click="open = false">关 闭</button>
        </template>
        <template v-if="!isRead">
          <button type="button" class="custom-dialog-btn" @click="open = false">取 消</button>
          <button type="button" class="custom-dialog-btn primary" @click="handleSubmit">确 定</button>
        </template>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { contractTemplateList, contractTemplateAdd, contractTemplateDelete, contractTemplateUpdate, contractTemplateDefault } from '@/api/purchase'
import { parseTime } from '@/utils/ruoyi'
import QRCode from 'qrcodejs2'

export default {
  data() {
    return {
      listShow: false,
      list: [],
      loading: true,
      title: undefined,
      open: false,
      form: {},
      rules: {
        name: [{ required: true, message: '请输入合同标题', trigger: 'blur' }],
        file: [{ required: true, message: '请输入合同内容', trigger: 'blur' }]
      },
      key: 1,
      isUse: false,
      orderInfo: {},
      productList: [
        {
          productName: '活接螺栓',
          specs: 'M12',
          model: 'M12',
          unit: '个',
          remark: '',
          amount: '88.8888',
          quantity: '10000'
        }
      ],
      isIncludingTax: false,
      isFile: false,
      qrcode: undefined,
      isRead: false
    }
  },
  methods: {
    parseTime,
    // 列表
    getList(isUse = false, orderInfo = {}, isFile = false) {
      this.isUse = isUse
      this.orderInfo = orderInfo
      this.isFile = isFile
      this.loading = true
      contractTemplateList().then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          const obj = {
            id: -1,
            name: '系统默认合同模板',
            file: '<p>一、运输和包装：<b>买方</b>负责运输并支付运费，<b>卖方</b>负责包装及装车。</p><p>二、质量标准：按照标准执行。</p><p>三、收货地点及收货人：<b>买方到卖方厂区自提</b>或<b>买方指定具体收货地点</b>；收货人：_______。</p><p>四、验收方式及期限：买方<b>收到产品后3日内</b>，按照双方约定质量标准进行验收；买方逾期未对产品质量提出书面异议的，视为对产品质量无异议。</p><p>五、结算方式及期限：<b>合同签订当日，买方支付产品全款</b>或<b>买方收到产品后1日内支付全款</b>。</p><p>六、卖方郑重声明：货款等资金往来禁止使用现金、银行承兑及商业承兑，必须付款至卖方指定账号，除此之外的现金往来，均属于个人行为，与卖方无关。</p><p>七、违约责任：按《民法典》相关条款执行。</p><p>八、争议解决方式：本合同项下发生的争议，由买卖双方当事人协商解决，协商不成的，依法<b>向合同签订地</b>人民法院起诉。</p><p>九、本合同自<b>双方签字或盖章</b>时生效，其复印件、扫描件同样具有法律效力。</p>'
          }
          this.list = [...[obj], ...data]
          this.loading = false
          this.listShow = true
        } else this.$message.error(msg)
      })
    },
    // 重置表单
    reset() {
      this.form = {
        name: undefined,
        file: undefined,
        templateId: undefined
      }
      this.resetForm('form')
    },
    // 新增
    handleAdd() {
      this.reset()
      this.title = '新增合同模板'
      this.key = Date.now()
      this.open = true
      this.isRead = false
      this.$nextTick(() => {
        if (this.$refs.qrcode) this.$refs.qrcode.innerHTML = ''
        new QRCode(this.$refs.qrcode, {
          width: 100, // 二维码宽度
          height: 100, // 二维码高度
          text: '演示二维码'
        })
      })
    },
    // 修改
    handleUpdate(item) {
      this.reset()
      this.title = '修改合同模板'
      this.form = { ...item }
      this.key = Date.now()
      this.open = true
      this.isRead = false
      this.$nextTick(() => {
        if (this.$refs.qrcode) this.$refs.qrcode.innerHTML = ''
        new QRCode(this.$refs.qrcode, {
          width: 100, // 二维码宽度
          height: 100, // 二维码高度
          text: '演示二维码' + new Date().getTime()
        })
      })
    },
    // 查看模板
    handleDetail(item) {
      this.reset()
      this.title = '修改合同模板'
      this.form = { ...item }
      this.key = Date.now()
      this.open = true
      this.isRead = true
      this.$nextTick(() => {
        if (this.$refs.qrcode) this.$refs.qrcode.innerHTML = ''
        new QRCode(this.$refs.qrcode, {
          width: 100, // 二维码宽度
          height: 100, // 二维码高度
          text: '演示二维码' + new Date().getTime()
        })
      })
    },
    // 提交
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          const { name, file, id } = this.form
          const params = { name, file }
          if (id) params.templateId = id
          const api = id ? contractTemplateUpdate : contractTemplateAdd
          api(params).then(res => {
            const { code, msg } = res
            if (code === 200) {
              this.$message.success(msg)
              this.open = false
              this.getList()
            } else this.$message.error(msg)
          })
        }
      })
    },
    // 删除
    // prettier-ignore
    handleDelete(item) {
      const data = { templateId: item.id }
      this.$modal.confirm('是否确认删除该模板?').then(function () {
        return contractTemplateDelete(data)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess('操作成功')
      }).catch(() => {
      })
    },
    // 选择模板
    handleUse(item) {
      // const data = { ...this.orderInfo, ...{ file: item.file } }
      // if (this.isFile) this.$parent.handleSubmitTpl(data)
      // else this.$parent.handleContract(data)
      if (this.isFile) this.$parent.handleSubmitTpl(item)
      else this.$parent.handleContract(item)
      this.listShow = false
    },
    // 使用默认模板
    handleUseDefault() {
      const file = '<p>一、运输和包装：<b>买方</b>负责运输并支付运费，<b>卖方</b>负责包装及装车。</p>' + '<p>二、质量标准：按照标准执行。</p>' + '<p>三、收货地点及收货人：<b>买方到卖方厂区自提</b>或<b>买方指定具体收货地点</b>；收货人：_______。</p>' + '<p>四、验收方式及期限：买方<b>收到产品后3日内</b>，按照双方约定质量标准进行验收；买方逾期未对产品质量提出书面异议的，视为对产品质量无异议。</p>' + '<p>五、结算方式及期限：<b>合同签订当日，买方支付产品全款</b>或<b>买方收到产品后1日内支付全款</b>。</p>' + '<p>六、卖方郑重声明：货款等资金往来禁止使用现金、银行承兑及商业承兑，必须付款至卖方指定账号，除此之外的现金往来，均属于个人行为，与卖方无关。</p>' + '<p>七、违约责任：按《民法典》相关条款执行。</p>' + '<p>八、争议解决方式：本合同项下发生的争议，由买卖双方当事人协商解决，协商不成的，依法<b>向合同签订地</b>人民法院起诉。</p>' + '<p>九、本合同自<b>双方签字或盖章</b>时生效，其复印件、扫描件同样具有法律效力。</p>'
      const data = { ...this.orderInfo, ...{ file } }
      if (this.isFile) this.$parent.handleSubmitTpl(data)
      else this.$parent.handleContract(data)
      this.listShow = false
    },
    // 设置默认模板
    handleDefault(row, e) {
      const data = { templateId: row.id, isDefault: e }
      contractTemplateDefault(data).then(res => {
        const { code, msg } = res
        if (code === 200) {
          this.$message.success(msg)
          this.getList()
        } else this.$message.error(msg)
      })
    }
  }
}
</script>
<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';
.none {
  width: 100%;
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  line-height: 1;
  ::v-deep .el-button {
    font-size: 18px;
    padding: 0;
    margin-left: 10px;
  }
}
::v-deep {
  .custom-table .el-table__body-wrapper .table-switch .el-switch__core {
    width: 40px !important;
  }
  .contractBox {
    padding: 20px 20px 10px;
    border: 1px solid #cbd6e2;
    border-radius: 5px;
    background-color: $white;
    margin-bottom: 10px;
    .custom-table {
      border-bottom-left-radius: 0;
      border-bottom-right-radius: 0;
      border-bottom: 0;
      .el-form-item {
        margin-top: 10px !important;
        margin-bottom: 10px !important;
        .el-form-item__error {
          top: 95%;
          padding-top: 0;
        }
      }
      .danger {
        border-color: #ec4545;
        color: #ec4545;
        &:hover {
          background-color: #ec4545;
        }
      }
    }
    &-title {
      font-size: 20px;
      font-weight: 500;
      color: $info;
      text-align: center;
      margin: 10px 0 30px;
    }
    &-desc {
      display: flex;
      justify-content: space-between;
      padding: 0 25px;
      margin-bottom: 5px;
      .desc-right {
        min-width: 320px;
      }
      .desc-item {
        width: 320px;
        font-size: 14px;
        line-height: 28px;
        .el-input__inner {
          text-align: left;
          font-size: 12px;
        }
        span {
          color: $info;
          text-align: right;
          &.el-input__prefix {
            width: auto;
          }
        }
        b {
          color: $font;
          font-weight: 500;
        }
        & + .desc-item {
          margin-left: 30px;
        }
      }
    }
    &-htitle {
      margin: 10px 0;
      font-size: 16px;
      span {
        color: $info;
      }
      b {
        font-weight: 500;
        color: $font;
      }
    }
    &-total {
      display: flex;
      flex-direction: column;
      border: 1px solid #ec4545;
      background: #fff5f5;
      height: 96px;
      line-height: 48px;
      padding: 0 20px;
      border-bottom-left-radius: 5px;
      border-bottom-right-radius: 5px;
      .total-box {
        display: inline-flex;
        justify-content: space-between;
        align-items: center;
      }
      .total-item {
        span {
          font-size: 14px;
          color: $info;
        }
        b {
          font-size: 18px;
          font-weight: 500;
          color: $orange;
        }
      }
      .total-tip {
        font-size: 12px;
        color: $orange;
      }
    }
    &-tableBox {
      width: 100%;
      margin: 15px 0;
      ul {
        width: 100%;
        list-style: none;
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        padding: 0;
        margin: 0;
        border: 1px solid #cbd6e2;
        border-radius: 5px;
        border-bottom-width: 0;
        overflow: hidden;
      }
      li {
        width: 50%;
        line-height: 20px;
        padding: 15px 20px;
        border-right: 1px solid #cbd6e2;
        border-bottom: 1px solid #cbd6e2;
        font-size: 12px;
        &:nth-child(2n) {
          border-right-width: 0;
        }
        &:nth-child(4n + 1),
        &:nth-child(4n + 2) {
          background-color: #f8f9fb;
        }
        span {
          color: $disabled;
          margin-right: 20px;
        }
        b {
          color: $font;
          font-weight: 500;
        }
      }
      .contractBox-input {
        .el-input__inner {
          text-align: left;
          font-size: 12px;
        }
      }
    }
    &-input {
      .el-input__inner {
        border-width: 0;
        border-bottom-width: 1px;
        border-radius: 0;
        background-color: transparent;
        text-align: center;
        font-size: 14px;
        color: $font;
      }
      &.input-date {
        .el-input__inner {
          text-align: left;
        }
      }
      &.input-left {
        .el-input__inner {
          text-align: left;
        }
      }
    }
  }
}
</style>
