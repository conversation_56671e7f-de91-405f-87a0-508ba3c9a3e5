<template>
  <div class="bodya">


    <div class="box">
        <div class="box1">
          <div class="box2">
            <div class="box2-1-2" v-for="(item2,index2) in list">
              <img class="del" src="../../../../public/imgs/取消 <EMAIL>"   @click="del(item2.id)">
              {{ item2.content }}
            </div>
            <div class="box2-1" @click="add()">
              新增标签
            </div>

          </div>
        </div>
    </div>



    <!-- 新增 -->
    <el-dialog
      title="新增标签"
      :visible.sync="addview"
      width="20%"
      style="margin-top: 10%;"
      >

      <el-form ref="form" :model="addData" label-width="80px">
        <el-form-item label="标签名">
          <el-input v-model="addData.content"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="add_c()">新建</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {labelList,labelAdd,labelDel} from "@/api/purchase/category";
export default {
  name: 'Label2',
  data() {
    return {
      list:[],
      addview:false,
      addData:{
        content:"",
        labelType:"custom"
      },
    };
  },
  created() {
    this.ready();
  },
  methods: {
    ready() {
      labelList(this.queryParams).then((res) => {
        this.list=res.data.custom;

      });
    },
    add(){
      this.addview=true;
    },
    add_c()
    {
      labelAdd(this.addData).then((res) => {
        this.ready();
        this.$message.success("添加成功");
        this.addview=false;
      });
    },
    del(row)
    {
      if(!confirm('确定删除吗？'))
      {
        return false;
      }
      labelDel(row).then((res) => {
        this.ready();
        this.$message.success("删除成功");
      });
    },
  }
}
</script>

<style scoped>
.bodya {
  padding: 20px;
}

.lab1 {
  text-align: right;
  height: 30px;
  line-height: 30px;
  padding-right: 10px;
}

.head {
  padding-top: 10px;
  text-align: right;
}

.box{
width: 100%;
  /* height: 500px; */
  margin: auto;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  overflow: hidden;
}
.box1{
  width: 95%;
  /* height: 40px; */
  line-height: 40px;
  font-size: 14px;
  border: 1px solid lightgray;
  margin:20px auto;
  padding-bottom: 10px;
  border-radius: 5px;
}
.box1-1{
  text-indent: 20px;
  cursor: pointer;
}
.box2{
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  margin-top: 10px;
  padding-bottom: 10px;
  padding-top: 10px;
}
.box2-1{
  width: 90px;
height: 30px;
background: #E0EBFF;
border-radius: 5px 5px 5px 5px;
opacity: 1;
text-align: center;
line-height: 30px;
font-size: 12px;
margin-left: 15px;
color: #2E73F3;
font-weight: bold;
position: relative;
cursor: pointer;
}
.box2-1-2{
  width: 90px;
height: 30px;
background: #E5E5E5;
border-radius: 5px 5px 5px 5px;
opacity: 1;
text-align: center;
line-height: 30px;
font-size: 12px;
margin-left: 15px;
color:#333;
font-weight: bold;
position: relative;
cursor: pointer;
}
.box2-1-2>img{
  width: 16px;
  height: 16px;
  position: absolute;
  right: -8px;
  top: -8px;
}
</style>
