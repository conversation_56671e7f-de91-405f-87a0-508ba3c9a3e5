<template>
  <div class="bodya">
    <div class="box">
        <div class="box1" v-for="(item,index) in list">
          <div class="box1-1">
            {{index+1}}.{{ item.content }}
            <img class="del" src="../../../../public/imgs/取消 <EMAIL>"  @click="del(item.id)">

          </div>
          <div class="box2">
            <div class="box2-1" v-for="(item2,index2) in item.child">
              <!-- <el-button type="success" size="mini"  @click="del(item2.id)"> {{index2+1}}.{{ item2.content }}</el-button> -->
              {{index2+1}}.{{ item2.content }}
              <img class="del" src="../../../../public/imgs/取消 <EMAIL>"   @click="del(item2.id)">
            </div>
            <div class="box2-1-2"  @click="add(item.id)">
              <img src="../../../../public/imgs/增加@2xblue.png">
              新增标签内容
            </div>
          </div>
        </div>

          <div class="addbtn" @click="add(0)">
            新增标签
          </div>
    </div>

    <!-- 新增 -->
    <el-dialog
      title="新增标签"
      :visible.sync="addview"
      width="20%"
      style="margin-top: 10%;"
      >

      <el-form ref="form" :model="addData" label-width="80px">
        <el-form-item label="所属上级" style="display: none;">
          <el-select v-model="addData.parentId" placeholder="请选择上级" style="width: 100%;">
            <el-option label="顶级" :value="Number(0)" checked></el-option>
          <el-option
            v-for="item in list"
            :key="item.id"
            :label="item.content"
            :value="Number(item.id)">
          </el-option>
        </el-select>
        </el-form-item>
        <el-form-item label="标签名">
          <el-input v-model="addData.content"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="add_c()">新建</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {labelList,labelAdd,labelDel} from "@/api/purchase/category";
export default {
  name: 'Label',
  data() {
    return {
      list:[],
      addview:false,
      addData:{
        content:"",
        labelType:"system"
      },
    };
  },
  created() {
    this.ready();
  },
  methods: {
    ready() {
      labelList(this.queryParams).then((res) => {
        this.list=res.data.system;

      });
    },
    add(id){
      this.addview=true;
      this.addData.parentId=id;
    },
    add_c()
    {
      labelAdd(this.addData).then((res) => {
        this.ready();
        this.$message.success("添加成功");
        this.addview=false;
      });
    },
    del(row)
    {

      if(!confirm('确定删除吗？'))
      {
        return false;
      }


      labelDel(row).then((res) => {
        this.ready();
        this.$message.success("删除成功");
      });
    },
  }
}
</script>

<style scoped>
.bodya {
  padding: 20px;
}

.lab1 {
  text-align: right;
  height: 30px;
  line-height: 30px;
  padding-right: 10px;
}

.head {
  padding-top: 10px;
  text-align: right;
}

.box{
width: 100%;
  /* height: 500px; */
  margin: auto;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  overflow: hidden;
  padding-bottom: 20px;
}
.box1{
  width: 95%;
  /* height: 40px; */
  line-height: 40px;
  font-size: 14px;
  border: 1px solid #CBD6E2;
  margin:20px auto;
  padding-bottom: 10px;
  border-radius: 5px;
  padding-bottom: 20px;
}
.box1-1{
  text-indent: 20px;
  font-size: 12px;
  color: #999;
  position: relative;
}
.box2{
  display: flex;
  flex-wrap: wrap;
  margin-left: 50px;
  margin-top: 10px;
}
.box2-1{
  width: 164px;
  height: 46px;
  background: #FFFFFF;
  border-radius: 5px 5px 5px 5px;
  opacity: 1;
  border: 1px solid #CBD7E2;
  text-align: center;
  line-height: 46px;
  margin-left: 20px;
  font-weight: bold;
  position: relative;
}

.box2-1-2{
  width: 164px;
  height: 46px;
  background: #FFFFFF;
  border-radius: 5px 5px 5px 5px;
  opacity: 1;
  border: 1px solid #2E73F3;
  text-align: center;
  line-height: 46px;
  margin-left: 20px;
  font-weight: bold;
  position: relative;
  color: #2E73F3;
  cursor: pointer;
}
.box2-1-2>img{
  width: 16px;
  height: 16px;
  transform: translateY(3px);
}

.del{
  width: 20px;
  height: 20px;
  position: absolute;
  right: -10px;
  top: -10px;
}

.addbtn{
  width: 164px;
height: 46px;
background: #2E73F3;
border-radius: 5px 5px 5px 5px;
opacity: 1;
border: 1px solid #2E73F3;
color: white;
text-align: center;
line-height: 46px;
margin-left: 40px;
cursor: pointer;
}
</style>
