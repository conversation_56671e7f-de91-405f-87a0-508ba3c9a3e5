<template>
  <div>
    <el-dialog v-dialogDragBox title="查看报价" :visible.sync="offerOpen" width="1150px" class="custom-dialog offer-dialog" :before-close="handleCancel">
      <template v-if="offerList.length">
        <div v-loading="loading">
          <div class="offer-change">
            <div class="offer-total inline-flex">
              <template v-if="offerList.length > 1">
                共
                <b>{{ offerList.length }}</b>
                个产品
              </template>
            </div>
            <div>
              <span class="option-title">回复期</span>
              <span class="option-item" :class="{ active: item.value == replyDay }" v-for="item in replydayOptions" :key="item.value" @click="handleChangeDay(item)">{{ item.label }}</span>
              <span class="option-title" style="margin-left: 15px">查看方式</span>
              <span class="option-item" :class="{ active: !isAsc }" @click="handleOrderBy(false)">时间降序</span>
              <span class="option-item" :class="{ active: isAsc }" @click="handleOrderBy(true)">时间升序</span>
            </div>
          </div>
          <div class="offer-table" v-for="(item, index) in offerList" :key="item.productId">
            <el-form :ref="`offerForm${index}`" :model="item" :rules="offerRules" label-width="0">
              <div class="offer-table-title">
                {{ item.productName }}
                <span style="margin-left: 10px; color: #999999; font-size: 12px" v-if="item.specs">规格：{{ item.specs }}</span>
                <span style="margin-left: 10px; color: #999999; font-size: 12px" v-if="item.model">型号：{{ item.model }}</span>
                <span style="margin-left: 10px; color: #999999; font-size: 12px" v-if="item.remark">备注：{{ item.remark }}</span>
                <span style="float: right"><el-button type="text" icon="el-icon-s-data" size="mini" @click="handleHistory(item)">历史报价</el-button></span>
              </div>
              <el-table :ref="`offerTable${index}`" stripe :data="item.offer" row-key="id" style="width: 100%" class="custom-table custom-table-cell0" @selection-change="handleSelectOffer(item, $event)">
                <el-table-column align="center" type="selection" width="50"></el-table-column>
                <el-table-column align="center" label="供应商" show-overflow-tooltip min-width="130">
                  <template slot-scope="{ row }">
                    <span class="table-link" @click="handleViewSupplier(row)">
                      <span v-if="row.publishWay === 'common'">(公域)</span>
                      <span style="color: #fe7f22" v-else>(私域)</span>
                      {{ row.supplier.name }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column align="center" prop="replyUnit" label="在线联系" min-width="100">
                  <template slot-scope="scope">
                    <div class="table-phone pointer" @click="handleContact(scope.row, item)" v-if="scope.row.supplier && scope.row.supplier.customer">
                      <i class="ssfont ss-diy-liaotian" />
                      <span>点我聊天</span>
                    </div>
                    <span v-else>-</span>
                  </template>
                </el-table-column>
                <el-table-column align="center" prop="sendUser" label="业务员" show-overflow-tooltip></el-table-column>
                <el-table-column align="center" prop="sendPhone" label="业务员电话" show-overflow-tooltip></el-table-column>
                <el-table-column align="center" prop="amount" label="产品报价" show-overflow-tooltip>
                  <template slot-scope="{ row }">
                    <span class="table-orange">{{ row.amount ? '￥' + row.amount : '' }}{{ '元' + (row.replyUnit || item.unit ? '/' : '') + (row.replyUnit || item.unit || '') }}</span>
                  </template>
                </el-table-column>
                <el-table-column align="center" prop="replyTime" label="报价时间" show-overflow-tooltip></el-table-column>
                <el-table-column align="center" prop="remark" label="报价备注" show-overflow-tooltip></el-table-column>
                <el-table-column align="center" prop="maxNum" label="可供应数量" show-overflow-tooltip>
                  <template slot-scope="{ row }">{{ row.maxNum === 2147483647 || !row.maxNum ? '不限量' : row.maxNum }}</template>
                </el-table-column>
                <el-table-column align="center" label="计划采购数量" show-overflow-tooltip>
                  <template slot-scope="scope">{{ item.needQuantity + item.unit }}</template>
                </el-table-column>
                <el-table-column align="center" prop="quantity" label="实际采购数量" width="100">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`offer.${scope.$index}.quantity`" :rules="offerRules.quantity">
                      <el-input v-model="scope.row.quantity" size="small" placeholder="请输入实际采购数量">
                        <span slot="suffix">{{ item.unit }}</span>
                      </el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column align="center" prop="sjNum" label="采购量" width="100">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`offer.${scope.$index}.sjNum`" :rules="offerRules.sjNum">
                      <el-input v-model="scope.row.sjNum" size="small" placeholder="请输入采购量">
                        <span slot="suffix">{{ scope.row.replyUnit || item.unit || '' }}</span>
                      </el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
              </el-table>
            </el-form>
          </div>
        </div>
      </template>
      <el-empty :description="!offerList.length && !loading ? '暂无数据' : '加载中…'" v-else />
      <div slot="footer" v-if="!loading">
        <button type="button" class="custom-dialog-btn" @click="handleCancel">取消</button>
        <template v-if="offerList.length">
          <button type="button" class="custom-dialog-btn primary" :class="{ disabled: !isNoproblem }" :disabled="!isNoproblem" @click="handleContract">生成合同</button>
        </template>
      </div>
    </el-dialog>

    <!-- 供应商信息 -->
    <supplier-dialog ref="supplier"></supplier-dialog>

    <!--  历史报价  -->
    <el-dialog v-dialogDragBox title="历史报价" :visible.sync="historyOpen" width="1150px" class="custom-dialog">
      <div style="padding: 0 20px">
        <div class="custom-history-tab">
          <span class="tab-title">报价时间</span>
          <span class="tab-item" :class="{ active: item.value === historyDay }" v-for="item in historydayOptions" :key="item.value" @click="handleChangeHistoryDay(item)">{{ item.label }}</span>
        </div>
        <line-chart-demand-history :chart-data="historyData" />
      </div>
      <div slot="footer">
        <button type="button" class="custom-dialog-btn primary" @click="historyOpen = false">关闭</button>
      </div>
    </el-dialog>

    <!-- 合同预览 -->
    <el-dialog v-dialogDragBox title="预览合同" :visible.sync="contractOpen" width="1150px" class="custom-dialog offer-dialog contract-dialog" append-to-body>
      <div style="text-align: center" :style="{ zoom: zoom }" v-if="isAffirm">
        <img style="max-width: 100%" :src="affirmData.file" />
      </div>
      <div ref="contractHtml" class="contractHtml" v-else>
        <el-form ref="contractForm" :model="contractInfo" :rules="contractRules">
          <div class="offer-title"></div>
          <div class="contractBox">
            <div class="contractBox-title">采购合同</div>
            <div class="contractBox-desc">
              <div class="inline-flex flex-column flex-align-start">
                <div>
                  <div class="desc-item inline-flex">
                    <span>买方：</span>
                    <b v-if="isEdit">{{ contractInfo.buyerName }}</b>
                    <b v-else>
                      <el-input v-model="contractInfo.buyerName" placeholder="请输入买方" size="mini" class="contractBox-input" style="width: 250px"></el-input>
                    </b>
                  </div>
                  <div class="desc-item inline-flex">
                    <span>合同编号：</span>
                    <b style="padding-left: 15px">{{ contractInfo.serial }}</b>
                  </div>
                </div>
                <div>
                  <div class="desc-item inline-flex">
                    <span>卖方：</span>
                    <b v-if="isEdit">{{ this.sellerName }}</b>
                    <b v-else>
                      <el-input v-model="this.sellerName" placeholder="请输入卖方" size="mini" class="contractBox-input" style="width: 250px"></el-input>
                    </b>
                  </div>
                  <div class="desc-item inline-flex">
                    <span>签订地点：</span>
                    <b v-if="isEdit">{{ contractInfo.address }}</b>
                    <b v-else>
                      <el-input v-model="contractInfo.address" placeholder="请输入签订地点" size="mini" class="contractBox-input" style="width: 250px"></el-input>
                    </b>
                  </div>
                </div>
                <div>
                  <div class="desc-item inline-flex"></div>
                  <div class="desc-item inline-flex">
                    <span>签订时间：</span>
                    <b v-if="isEdit">{{ contractInfo.signingTime }}</b>
                    <b v-else>
                      <el-date-picker v-model="contractInfo.signingTime" type="date" placeholder="请选择签订时间" size="mini" class="contractBox-input input-date" style="width: 250px"></el-date-picker>
                    </b>
                  </div>
                </div>
              </div>
              <div class="inline-flex flex-column flex-align-start">
                <div id="qrcode" ref="qrcode"></div>
              </div>
            </div>
            <div class="contractBox-htitle"><span>产品采购清单</span></div>
            <el-table ref="contractTable" stripe :data="contractInfo.list" :key="key" style="width: 100%" class="custom-table custom-table-cell10">
              <el-table-column align="center" type="index" label="序号"></el-table-column>
              <el-table-column align="center" prop="productName" label="产品名称" :show-overflow-tooltip="!isEdit" min-width="120">
                <template slot-scope="{ row }">
                  <span class="table-link" v-if="isEdit">{{ row.productName }}</span>
                  <span class="table-link" @click="handleView(row.productId, row)" v-else>
                    <span v-if="row.source === 'common'">(公域)</span>
                    <span style="color: #fe7f22" v-if="row.source === 'private'">(私域)</span>
                    {{ row.productName }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column align="center" prop="specs" label="规格" :show-overflow-tooltip="!isEdit" width="100">
                <template slot-scope="{ row }">
                  <span v-if="row.hasOwnProperty('specs')">{{ row.specs }}</span>
                </template>
              </el-table-column>
              <el-table-column align="center" prop="model" label="型号" show-overflow-tooltip>
                <template slot-scope="{ row }">
                  <span v-if="row.hasOwnProperty('model')">{{ row.model }}</span>
                </template>
              </el-table-column>
              <el-table-column align="center" label="清单扣减量" min-width="100" v-if="!isEdit && !isIndex">
                <template slot-scope="scope">
                  <span class="table-orange" v-if="isEdit">{{ scope.row.quantity + scope.row.unit }}</span>
                  <template v-else>
                    <el-form-item
                      label-width="0"
                      :prop="`list.${scope.$index}.quantity`"
                      :rules="[
                        { required: true, message: '请输入数量', trigger: 'blur' },
                        {
                          type: 'number',
                          min: 0.0000000001,
                          message: '输入有误',
                          trigger: 'blur',
                          transform(value) {
                            return Number(value)
                          }
                        },
                        {
                          type: 'number',
                          max: scope.row.maxNum,
                          message: `未采购${scope.row.maxNum}`,
                          trigger: 'blur',
                          transform(value) {
                            return Number(value)
                          }
                        }
                      ]">
                      <el-input v-model="scope.row.quantity" size="small" placeholder="请输入清单扣减量">
                        <span slot="suffix">{{ scope.row.unit }}</span>
                      </el-input>
                    </el-form-item>
                  </template>
                </template>
              </el-table-column>
              <el-table-column align="center" prop="amount" label="产品单价" min-width="100">
                <template slot-scope="scope">
                  <span class="table-orange" v-if="isEdit">{{ scope.row.amount ? '￥' + parseFloat(scope.row.amount) : '' }}{{ '元' + (scope.row.endUnit ? '/' : '') + (scope.row.endUnit || '') }}</span>
                  <template v-else>
                    <template v-if="canEdit">
                      <el-form-item label-width="0" :prop="`list.${scope.$index}.amount`" :rules="contractRules.amount">
                        <el-input v-model="scope.row.amount" size="small" placeholder="请输入产品单价">
                          <template slot="suffix">
                            <el-select size="mini" class="suffix-select" v-model="scope.row.endUnit" filterable allow-create placeholder="单位">
                              <el-option v-for="(item, index) in unitOptions" :key="index" :label="`元/${item}`" :value="item"></el-option>
                            </el-select>
                          </template>
                        </el-input>
                      </el-form-item>
                    </template>
                    <span class="table-orange" v-if="!canEdit">{{ scope.row.amount ? '￥' + parseFloat(scope.row.amount) : '' }}{{ '元' + (scope.row.endUnit ? '/' : '') + (scope.row.endUnit || '') }}</span>
                  </template>
                </template>
              </el-table-column>
              <el-table-column align="center" prop="quantity" label="采购量" min-width="100">
                <template slot-scope="scope">
                  <span v-if="isEdit">{{ scope.row.sjNum + (scope.row.endUnit || '') }}</span>
                  <template v-else>
                    <template v-if="canEdit">
                      <el-form-item label-width="0" :prop="`list.${scope.$index}.sjNum`" :rules="contractRules.sjNum">
                        <el-input v-model="scope.row.sjNum" size="small" @blur="inspectNum(scope.row)" placeholder="请输入实际采购数量">
                          <span slot="suffix">{{ scope.row.endUnit || '' }}</span>
                        </el-input>
                      </el-form-item>
                    </template>
                    <span v-if="!canEdit">{{ scope.row.sjNum + (scope.row.endUnit || '') }}</span>
                  </template>
                </template>
              </el-table-column>
              <el-table-column align="center" prop="replyRemark" label="报价备注" show-overflow-tooltip v-if="!isEdit"></el-table-column>
              <el-table-column align="center" prop="remark" label="备注" :show-overflow-tooltip="!isEdit">
                <template slot-scope="scope">
                  <span v-if="isEdit">{{ scope.row.remark }}</span>
                  <template v-else>
                    <el-form-item label-width="0" :prop="`list.${scope.$index}.remark`" :rules="contractRules.remark">
                      <el-tooltip :content="scope.row.remark" placement="top" :disabled="!scope.row.remark">
                        <el-input v-model="scope.row.remark" size="small" placeholder="备注"></el-input>
                      </el-tooltip>
                    </el-form-item>
                  </template>
                </template>
              </el-table-column>
              <el-table-column align="center" label="操作" v-if="!isEdit && canEdit">
                <template slot-scope="scope">
                  <!--                  <button type="text" class="table-btn danger" @click="handleDeleteContrat(scope.$index)" v-if="!isEdit">删除</button>-->
                  <el-button type="danger" size="mini" plain @click="handleDeleteContrat(scope.$index)" v-if="!isEdit">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
            <div class="contractBox-total">
              <div class="total-box">
                <div class="total-item">
                  <span>
                    共
                    <b>{{ contractInfo.list ? contractInfo.list.length : 0 }}</b>
                    件产品
                  </span>
                </div>
                <div class="total-item">
                  <span>
                    订单总金额：
                    <b>￥ {{ getTotals(contractInfo) }}</b>
                  </span>
                </div>
                <div class="total-item">
                  <span>
                    人民币总金额：
                    <b>{{ lowerConversionUpper(getTotals(contractInfo)) }}</b>
                  </span>
                </div>
              </div>
              <div class="total-box" style="justify-content: flex-start">
                <div class="total-item" style="margin-right: 110px">
                  <span>
                    是否含税：
                    <el-switch v-model="contractInfo.isIncludingTax" active-text="含税" inactive-text="不含税" @change="handleChangeTaxSwitch"></el-switch>
                  </span>
                </div>
                <div class="total-tip">
                  <span v-if="isEdit">注：{{ contractInfo.conTip }}</span>
                  <span v-else>
                    注：
                    <el-input v-model="contractInfo.conTip" size="mini" class="contractBox-input input-left" style="width: 400px"></el-input>
                  </span>
                </div>
              </div>
            </div>
            <div style="margin-top: 20px">
              <div style="margin-bottom: 10px; text-align: right" v-if="!isEdit">
                <el-button type="primary" size="small" icon="el-icon-connection" @click="handleChangeTpl(contractInfo)">切换合同模板</el-button>
              </div>
              <div v-html="contractTemplate.file" v-if="isEdit"></div>
              <editor v-model="contractTemplate.file" :min-height="500" v-else />
            </div>
            <div class="contractBox-tableBox">
              <ul>
                <li>
                  <span>卖方：</span>
                  <b>{{ this.sellerName }}</b>
                </li>
                <li>
                  <span>买方：</span>
                  <b>{{ contractInfo.buyerName }}</b>
                </li>
                <li class="inline-flex">
                  <span>法定代表人：</span>
                  <b v-if="isEdit">{{ contractInfo.sellerInfo.nickName }}</b>
                  <b v-else>
                    <el-input v-model="contractInfo.sellerInfo.nickName" size="mini" class="contractBox-input" style="width: 120px"></el-input>
                  </b>
                </li>
                <li class="inline-flex">
                  <span>法定代表人：</span>
                  <b v-if="isEdit">{{ contractInfo.buyerInfo.nickName }}</b>
                  <b v-else>
                    <el-input v-model="contractInfo.buyerInfo.nickName" size="mini" class="contractBox-input" style="width: 120px"></el-input>
                  </b>
                </li>
                <li class="inline-flex">
                  <span>
                    <el-tooltip class="item" effect="dark" content="无默认联系人，需手动填写" placement="top" v-if="!isEdit">
                      <i class="el-icon-question"></i>
                    </el-tooltip>
                    委托代理人：
                  </span>
                  <b style="width: 80px" v-if="isEdit">{{ contractInfo.sellerInfo.consignor }}</b>
                  <b v-else>
                    <el-input v-model="contractInfo.sellerInfo.consignor" size="mini" class="contractBox-input" style="width: 80px"></el-input>
                  </b>
                  <span style="margin-left: 10px">法人或委托代理人签字：</span>
                </li>
                <li class="inline-flex">
                  <span>委托代理人：</span>
                  <b v-if="isEdit">{{ contractInfo.buyerInfo.consignor }}</b>
                  <b v-else>
                    <el-input v-model="contractInfo.buyerInfo.consignor" size="mini" class="contractBox-input" style="width: 120px"></el-input>
                  </b>
                </li>
                <li class="inline-flex">
                  <span>电话和微信号码：</span>
                  <b v-if="isEdit">{{ contractInfo.sellerInfo.phone }}</b>
                  <b v-else>
                    <el-input v-model="contractInfo.sellerInfo.phone" size="mini" class="contractBox-input" style="width: 120px"></el-input>
                  </b>
                </li>
                <li class="inline-flex">
                  <span>电话和微信号码：</span>
                  <b v-if="isEdit">{{ contractInfo.buyerInfo.phone }}</b>
                  <b v-else>
                    <el-input v-model="contractInfo.buyerInfo.phone" size="mini" class="contractBox-input" style="width: 120px"></el-input>
                  </b>
                </li>
                <li class="inline-flex">
                  <span>开户行：</span>
                  <b v-if="isEdit">{{ contractInfo.sellerInfo.bank }}</b>
                  <b class="inline-flex" v-else>
                    <el-input v-model="contractInfo.sellerInfo.bank" size="mini" class="contractBox-input"></el-input>
                    <el-button icon="el-icon-search" circle size="mini" style="margin-left: 10px" @click="handleSelectBank(contractInfo, 'sellerInfo')"></el-button>
                  </b>
                </li>
                <li class="inline-flex">
                  <span>开户行：</span>
                  <b v-if="isEdit">{{ contractInfo.buyerInfo.bank }}</b>
                  <b class="inline-flex" v-else>
                    <el-input v-model="contractInfo.buyerInfo.bank" size="mini" class="contractBox-input"></el-input>
                    <el-button icon="el-icon-search" circle size="mini" style="margin-left: 10px" @click="handleSelectBank(contractInfo, 'buyerInfo')"></el-button>
                  </b>
                </li>
                <li class="inline-flex">
                  <span>账号：</span>
                  <b v-if="isEdit">{{ contractInfo.sellerInfo.account }}</b>
                  <b v-else>
                    <el-input v-model="contractInfo.sellerInfo.account" size="mini" class="contractBox-input" style="width: 250px"></el-input>
                  </b>
                </li>
                <li class="inline-flex">
                  <span>账号：</span>
                  <b v-if="isEdit">{{ contractInfo.buyerInfo.account }}</b>
                  <b v-else>
                    <el-input v-model="contractInfo.buyerInfo.account" size="mini" class="contractBox-input" style="width: 250px"></el-input>
                  </b>
                </li>
                <li class="inline-flex">
                  <span>地址：</span>
                  <b v-if="isEdit">{{ contractInfo.sellerInfo.address }}</b>
                  <b v-else>
                    <el-input v-model="contractInfo.sellerInfo.address" size="mini" class="contractBox-input" style="width: 350px"></el-input>
                  </b>
                </li>
                <li class="inline-flex">
                  <span>地址：</span>
                  <b v-if="isEdit">{{ contractInfo.buyerInfo.address }}</b>
                  <b v-else>
                    <el-input v-model="contractInfo.buyerInfo.address" size="mini" class="contractBox-input" style="width: 350px"></el-input>
                  </b>
                </li>
              </ul>
            </div>
          </div>
        </el-form>
      </div>
      <div slot="footer">
        <template v-if="isAffirm">
          <button type="button" class="custom-dialog-btn small" @click="changeEA">修改合同</button>
          <button type="button" class="custom-dialog-btn small primary" @click="handleAffirm">确认生成合同</button>
          <button type="button" class="custom-dialog-btn small primary" @click="handleAffirmSend">确认并发送</button>
        </template>
        <template v-else>
          <button type="button" class="custom-dialog-btn" @click="contractOpen = false">取消</button>
          <button type="button" class="custom-dialog-btn primary" @click="handleSubmit" :disabled="isEdit">生成合同</button>
        </template>
      </div>
    </el-dialog>
    <!--  合同模板  -->
    <contract-tpl ref="contractTpl"></contract-tpl>
    <!--  选择银行卡信息  -->
    <el-dialog v-dialogDragBox title="选择银行卡信息" :visible.sync="checkOpen" width="1150px" class="custom-dialog">
      <div style="padding: 0 20px">
        <el-table ref="bank" :data="checkBankList" style="width: 100%" stripe class="custom-table custom-table-cell10" @row-click="handleBankChange">
          <el-table-column align="center" label="选择" width="55">
            <template slot-scope="{ row }">
              <el-radio v-model="checkedBank.bankNo" :label="row.bankNo"><span /></el-radio>
            </template>
          </el-table-column>
          <el-table-column prop="bankName" label="开户行" align="center"></el-table-column>
          <el-table-column prop="bankNo" label="银行账号" align="center"></el-table-column>
          <el-table-column prop="bankUser" label="账户名" align="center"></el-table-column>
        </el-table>
      </div>
      <div slot="footer">
        <button type="button" class="custom-dialog-btn" @click="checkOpen = false">取消</button>
        <button type="button" class="custom-dialog-btn primary" :class="{ disabled: !checkedBank }" :disabled="!checkedBank" @click="handleSubmitCheck">确定</button>
      </div>
    </el-dialog>
    <!-- 产品详情 -->
    <product-dialog ref="productInfo"></product-dialog>
    <!--  发送合同  -->
    <send-tpl ref="send" @callBack="handleSendBack"></send-tpl>
    <!--  聊天  -->
    <chat ref="chat" :showBadge="false" />
  </div>
</template>

<script>
import { contractTemplateList, createContract, createContractSerial, purchasingDetails, purchasingProductReply } from '@/api/purchase'
import { isNumber, isNumberLength } from '@/utils/validate'
import supplierDialog from './supplier'
import LineChartDemandHistory from '@/views/dashboard/LineChartDemandHistory'
import { getUserProfile, supplier } from '@/api/system/user'
import { privateSupb } from '@/api/houtai/siyu/gongying'
import QRCode from 'qrcodejs2'
import { lowerConversionUpper, removeHtmlTag } from '@/utils'
import contractTpl from '@/views/purchase/contract/template'
import { getProduct } from '@/api/system/product'
import { getPrivateduct } from '@/api/system/privateduct'
import ProductDialog from '@/views/public/product/dialog.vue'
import html2canvas from 'html2canvas'
import sendTpl from '@/views/purchase/contract/send'
import { detailBiddingProduct } from '@/api/bidding'
import Chat from '@/components/Chat/index'

export default {
  components: { Chat, ProductDialog, contractTpl, supplierDialog, LineChartDemandHistory, sendTpl },
  data() {
    var checkNum = (rule, value, callback) => {
      if (value <= 0) {
        callback(new Error('采购量不正确'))
      } else {
        callback()
      }
    }
    return {
      loading: false,
      dataList: [],
      offerOpen: false,
      offerList: [],
      offerRules: {
        quantity: [
          { required: true, message: '请输入实际采购数量', trigger: 'blur' },
          { validator: isNumber, message: '输入有误请重新输入', trigger: 'blur' }
        ],
        sjNum: [
          { required: true, message: '请输入采购量', trigger: 'blur' },
          { validator: isNumber, message: '输入有误请重新输入', trigger: 'blur' }
        ]
      },
      replyDay: 0,
      replydayOptions: [
        { label: '本次报价', value: 0 },
        { label: '7天内', value: 7 },
        { label: '14天内', value: 14 },
        { label: '28天内', value: 28 }
      ],
      isAsc: false,
      isOrder: false,
      orderList: [],
      orderRules: {
        quantity: [
          { required: true, message: '请输入实际采购数量', trigger: 'blur' },
          { validator: isNumber, message: '输入有误请重新输入', trigger: 'blur' }
        ],
        sjNum: [
          { required: true, message: '请输入采购量', trigger: 'blur' },
          { validator: checkNum, trigger: 'blur' },
          { validator: isNumber, message: '输入有误请重新输入', trigger: 'blur' }
        ]
      },
      // 供应商信息
      supplierTitle: undefined,
      supplierOpen: false,
      supplierInfo: {},
      imgOpen: false,
      imgUrl: undefined,
      isNoproblem: true,
      // 历史报价
      historyOpen: false,
      historyItem: {},
      historyData: {},
      historyDay: undefined,
      historydayOptions: [
        { label: '1年内', value: 365 },
        { label: '2年内', value: 730 },
        { label: '全部', value: 9999 }
      ],
      docUser: undefined,
      demandId: undefined,
      key: 1,
      // 供应商信息
      sellerName: undefined,
      sellerinfo: {},
      sellerUser: {},
      // 合同模板
      contractTemplate: {},
      // 预览合同
      contractOpen: false,
      contractInfo: {
        sellerInfo: {
          nickName: undefined,
          consignor: undefined,
          phone: undefined,
          bank: undefined,
          account: undefined,
          address: undefined
        },
        buyerInfo: {
          nickName: undefined,
          consignor: undefined,
          phone: undefined,
          bank: undefined,
          account: undefined,
          address: undefined
        }
      },
      contractRules: {
        amount: [
          { required: true, message: '请输入产品单价', trigger: 'blur' },
          { validator: isNumber, message: '请输入正确的金额', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 10), message: '只可以填写十位小数', trigger: 'blur' }
        ],
        quantity: [
          { required: true, message: '请输入计划采购数量', trigger: 'blur' },
          { validator: checkNum, message: '采购量不正确', trigger: 'blur' },
          { validator: isNumber, message: '请输入正确的数量', trigger: 'blur' }
        ],
        sjNum: [
          { required: true, message: '请输入实际采购数量', trigger: 'blur' },
          { validator: checkNum, message: '采购量不正确', trigger: 'blur' },
          { validator: isNumber, message: '请输入正确的数量', trigger: 'blur' }
        ]
      },
      isEdit: false,
      qrcode: undefined,
      sellerBankList: [],
      buyerBankList: [],
      checkBankList: [],
      checkType: undefined,
      checkedBank: undefined,
      checkOpen: false,
      isAffirm: false,
      affirmData: {},
      zoom: 1,
      isIndex: false,
      canEdit: true,
      contractType: undefined,
      unitOptions: ['吨', '千克', '个', '件', '套', '米', '支', '根', '袋', '托'],
      minNum: 0
    }
  },
  methods: {
    removeHtmlTag,
    lowerConversionUpper,
    // 批量查看报价
    handleOffer(data, docUser = '', demandId = '') {
      this.docUser = docUser
      this.demandId = demandId
      const dataList = JSON.parse(JSON.stringify(data))
      dataList.map(item => {
        item.checked = []
        item.offer &&
          item.offer.map(ite => {
            ite.checked = false
            ite.show = false
            ite.quantity = item.needQuantity
            ite.sjNum = ite.replyUnit == item.unit ? ite.quantity : 0
          })
      })
      this.offerList = dataList.filter(item => item.offer.length)
      this.dataList = dataList
      this.replyDay = 0
      this.isAsc = false
      this.isOrder = false
      // this.getOfferList(data)
      this.offerOpen = true
      this.isNoproblem = true
    },
    // 获取多个报价
    async getOfferList(data) {
      this.loading = true
      let arr = JSON.parse(JSON.stringify(data))
      await Promise.all(
        arr.map(async (item, index) => {
          item.checked = []
          item.offer = []
          if (item.hasOwnProperty('productId') && item.productId) {
            const query = { productId: item.productId, source: item.source, isAsc: this.isAsc, replyDay: this.replyDay }
            const offer = await purchasingProductReply(query)
            if (offer.data.length) {
              offer.data.map(ite => {
                ite.sjNum = ite.replyUnit == item.unit ? ite.quantity : 0
                ite.quantity = item.needQuantity
              })
              item.offer = offer.data
            }
          }
        })
      )
      this.offerList = arr.filter(item => item.offer.length)
      this.loading = false
    },
    // 改变天数
    handleChangeDay(item) {
      this.replyDay = item.value
      if (item.value != 0) this.getOfferList(this.dataList)
      else this.offerList = this.dataList.filter(item => item.offer.length)
    },
    // 改变升序降序
    handleOrderBy(e) {
      this.isAsc = e
      if (this.replyDay != 0) this.getOfferList(this.dataList)
      else {
        const list = this.dataList.filter(item => item.offer.length)
        if (e) {
          const newList = JSON.parse(JSON.stringify(list))
          this.offerList = newList.map(item => {
            return {
              ...item,
              offer: item.offer.sort((a, b) => new Date(a.replyTime) - new Date(b.replyTime))
            }
          })
        } else this.offerList = list
      }
    },
    // 取消
    handleCancel() {
      this.offerOpen = false
      this.$emit('close')
    },
    // 获取供应商数量
    getNumber(data) {
      let arr = []
      data.map(item => {
        arr = arr.concat(item.checked)
      })
      const newArr = arr.map(item => item.supplier.id)
      return Array.from(new Set(newArr)).length
    },
    // 选择供应商
    handleSelectOffer(item, selection) {
      const supplier = selection.map(item => item.supplier.id)
      if (supplier.length !== [...new Set(supplier)].length) {
        this.$message.error('不能选择同一个供应商不同业务员的报价')
        this.isNoproblem = false
        return
      } else if (supplier.length > 1) {
        this.$message.error('不能选择多个供应商的报价')
        this.isNoproblem = false
        return
      } else {
        this.isNoproblem = true
      }
      item.checked = selection
      let offer = []
      this.offerList.map(item => {
        offer = offer.concat(item.checked)
      })
      const offersupplier = [...new Set(offer.map(item => item.supplier.id))]
      if (offersupplier.length > 1) {
        this.$message.error('不能选择多个供应商')
        this.isNoproblem = false
        return
      } else {
        this.isNoproblem = true
      }
      this.$forceUpdate()
    },
    // 生成合同
    handleContract() {
      let isTrue = 0
      this.offerList.map((item, index) => {
        const form = 'offerForm' + index
        this.$refs[form][0].validate(valid => {
          if (valid) {
            isTrue += 1
          } else {
            return false
          }
        })
      })
      let checked = []
      this.offerList.map(item => {
        item.checked.map(ite => {
          checked.push(ite)
        })
      })
      if (checked.length) {
        if (isTrue === this.offerList.length) {
          let data = []
          this.offerList.map(item => {
            if (item.checked.length) data.push(item)
          })
          try {
            data.map(item => {
              let total = 0
              item.checked.map(ite => {
                total += Number(ite.quantity)
              })
              if (total > item.needQuantity) {
                this.$message.error(item.productName + '采购数量超出实际采购数量，请检查')
                throw new Error()
              }
            })
            this.orderList = data
            this.handleGetContract()
          } catch {}
        }
      } else {
        this.$message.error('请至少选择一个产品和供应商')
      }
    },
    // 查看供应商详情
    handleViewSupplier(row) {
      const id = row.supplier.id
      this.$refs.supplier.getInfo(id, row.publishWay)
    },
    // 查看历史报价
    handleHistory(item) {
      this.historyItem = item
      this.historyDay = 365
      this.getHistory()
    },
    // 获取历史报价
    getHistory() {
      const query = {
        productId: this.historyItem.productId,
        source: this.historyItem.source,
        isAsc: this.historyItem.isAsc,
        replyDay: this.historyDay
      }
      purchasingProductReply(query).then(res => {
        if (res.code === 200) {
          this.historyData = res.data
          if (!this.historyOpen) this.historyOpen = true
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 改变天数
    handleChangeHistoryDay(item) {
      this.historyDay = item.value
      this.getHistory()
    },
    // 开始生成合同
    async handleGetContract(sellerUser = {}, product = {} || [], canEdit = 'yes', contractType = '') {
      this.canEdit = canEdit === 'yes'
      this.contractType = contractType
      if (Object.keys(sellerUser).length) {
        await supplier({ id: sellerUser.companyId }).then(res => {
          this.sellerName = res.data.company ? res.data.company.companyName : res.data.supplier.name
          this.sellerinfo = res.data
          this.sellerinfo.banks = res.data.supplier.bankList || []
          this.sellerUser.sendUser = sellerUser.concat || res.data.company.contact
          this.sellerUser.sendPhone = sellerUser.phone || res.data.company.phone
          this.sellerUser.supplier = res.data.supplier
        })
      } else {
        const data = this.orderList
        // 查询供应商信息
        const supplierData = data[0].checked[0]
        this.sellerUser = supplierData
        if (supplierData.publishWay === 'common') {
          await supplier({ id: supplierData.supplier.id }).then(res => {
            this.sellerName = res.data.company ? res.data.company.companyName : res.data.supplier.name
            this.sellerinfo = res.data
          })
        } else {
          await privateSupb({ id: supplierData.supplier.id }).then(res => {
            this.sellerName = res.data.name
            this.sellerinfo = res.data
          })
        }
      }
      // 查询合同模板
      await contractTemplateList().then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          const index = data.findIndex(item => item.isDefault)
          if (index > -1) {
            this.contractTemplate = data[index]
          } else {
            const obj = {
              file: '<p>一、运输和包装：<b>买方</b>负责运输并支付运费，<b>卖方</b>负责包装及装车。</p><p>二、质量标准：按照标准执行。</p><p>三、收货地点及收货人：<b>买方到卖方厂区自提</b>或<b>买方指定具体收货地点</b>；收货人：_______。</p><p>四、验收方式及期限：买方<b>收到产品后3日内</b>，按照双方约定质量标准进行验收；买方逾期未对产品质量提出书面异议的，视为对产品质量无异议。</p><p>五、结算方式及期限：<b>合同签订当日，买方支付产品全款</b>或<b>买方收到产品后1日内支付全款</b>。</p><p>六、卖方郑重声明：货款等资金往来禁止使用现金、银行承兑及商业承兑，必须付款至卖方指定账号，除此之外的现金往来，均属于个人行为，与卖方无关。</p><p>七、违约责任：按《民法典》相关条款执行。</p><p>八、争议解决方式：本合同项下发生的争议，由买卖双方当事人协商解决，协商不成的，依法<b>向合同签订地</b>人民法院起诉。</p><p>九、本合同自<b>双方签字或盖章</b>时生效，其复印件、扫描件同样具有法律效力。</p>'
            }
            this.contractTemplate = obj
          }
        } else {
          this.$message.error(msg)
        }
      })
      await this.handleCreateContract(product)
    },
    // 重置合同表单
    resetContractInfo() {
      this.contractInfo = {
        orderNumber: undefined,
        buyerName: undefined,
        sellerName: undefined,
        serial: undefined,
        address: undefined,
        signingTime: undefined,
        isIncludingTax: false,
        list: [
          {
            productName: undefined,
            specs: undefined,
            model: undefined,
            amount: undefined,
            originAmount: undefined,
            quantity: undefined,
            sjNum: undefined,
            remark: undefined
          }
        ],
        conTip: '不包含运费、安装费',
        transportText1: '买方',
        transportText2: '卖方',
        addressText1: '买方到卖方厂区自提',
        addressText2: '买方指定具体收货地点',
        addressName: undefined,
        checkText: '收到产品后3日内',
        finalText1: '合同签订当日，买方支付产品全款',
        finalText2: '买方收到产品后1日内支付全款',
        disputeText: '合同签订地',
        effectiveText: '双方签字或盖章',
        sellerInfo: {
          nickName: undefined,
          consignor: undefined,
          phone: undefined,
          bank: undefined,
          account: undefined,
          address: undefined
        },
        buyerInfo: {
          nickName: undefined,
          consignor: undefined,
          phone: undefined,
          bank: undefined,
          account: undefined,
          address: undefined
        }
      }
      this.resetForm('contractForm')
    },
    // 修改是否含税
    handleChangeTaxSwitch(e) {
      localStorage.setItem('isIncludingTax', e)
    },
    // 检查内容是json还是array
    checkConType(data) {
      if (Array.isArray(data)) {
        return 'Array'
      } else if (typeof data === 'object' && data !== null) {
        return 'JSON'
      } else {
        return 'Unknown type'
      }
    },
    // 生成合同
    handleCreateContract(product = {} || []) {
      this.isEdit = false
      this.resetContractInfo()
      const info = {
        address: '河北省邯郸市永年区',
        signingTime: this.parseTime(new Date().getTime(), '{y}-{m}-{d}'),
        conTip: '不包含运费、安装费',
        transportText1: '买方',
        transportText2: '卖方',
        addressText1: '买方到卖方厂区自提',
        addressText2: '买方指定具体收货地点',
        checkText: '收到产品后3日内',
        finalText1: '合同签订当日，买方支付产品全款',
        finalText2: '买方收到产品后1日内支付全款',
        disputeText: '合同签订地',
        effectiveText: '双方签字或盖章'
      }
      let list = []
      this.orderList.map(async item => {
        const max = await purchasingDetails({ ids: item.listId, needProduct: false })
        let planNum = 0
        let realNum = 0
        max.data.map(ite => {
          planNum += ite.quantity
          realNum += ite.purchasedQuantity
        })
        list.push({
          maxNum: parseFloat((planNum - realNum).toFixed(5)),
          source: item.source,
          productId: item.productId,
          productName: item.productName,
          productCode: item.productCode,
          specs: item.specs,
          model: item.model,
          unit: item.unit,
          quantity: item.checked[0].quantity,
          needQuantity: item.needQuantity,
          listId: item.listId,
          remark: item.remark,
          amount: item.checked[0].amount,
          originAmount: item.checked[0].amount,
          replyUnit: item.checked[0].replyUnit,
          sjNum: item.checked[0].sjNum,
          replyRemark: item.checked[0].remark,
          endUnit: item.checked[0].replyUnit || item.unit
        })
      })
      this.contractInfo = { ...this.contractInfo, ...info }
      if (this.checkConType(product) === 'Array') {
        this.contractInfo.list = product
        this.isIndex = true
      } else if (this.checkConType(product) === 'JSON' && Object.keys(product).length) {
        product.endUnit = product.replyUnit || product.unit
        this.minNum = product.sjNum
        this.contractInfo.list = [product]
        this.isIndex = true
      } else {
        this.contractInfo.list = list
        this.isIndex = false
      }
      const sellerinfo = this.sellerinfo
      createContractSerial().then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          this.contractInfo.addressName = data.buyerName
          this.contractInfo.serial = data.serial
          if (sellerinfo.hasOwnProperty('legal')) this.contractInfo.sellerInfo.nickName = sellerinfo.legal
          if (!sellerinfo.hasOwnProperty('legal') && sellerinfo.hasOwnProperty('supplier') && sellerinfo.supplier.hasOwnProperty('legal')) this.contractInfo.sellerInfo.nickName = sellerinfo.supplier.legal
          this.contractInfo.sellerInfo.consignor = this.sellerUser.sendUser
          this.contractInfo.sellerInfo.phone = this.sellerUser.sendPhone
          let idx
          if (sellerinfo.hasOwnProperty('bankList') && sellerinfo.bankList.length) idx = sellerinfo.bankList.findIndex(item => item.checked)
          if (!sellerinfo.hasOwnProperty('bankList') && sellerinfo.hasOwnProperty('banks') && sellerinfo.banks.length) idx = sellerinfo.banks.findIndex(item => item.checked)
          if (idx === -1) idx = 0
          if (sellerinfo.hasOwnProperty('bankList') && sellerinfo.bankList.length) {
            this.contractInfo.sellerInfo.bank = sellerinfo.bankList[idx].bankName
            this.contractInfo.sellerInfo.account = sellerinfo.bankList[idx].bankNo
            this.sellerBankList = sellerinfo.bankList
          }
          if (!sellerinfo.hasOwnProperty('bankList') && sellerinfo.hasOwnProperty('banks') && sellerinfo.banks.length) {
            this.contractInfo.sellerInfo.bank = sellerinfo.banks[idx].bankName
            this.contractInfo.sellerInfo.account = sellerinfo.banks[idx].bankNo
            this.sellerBankList = sellerinfo.banks
          }
          if (sellerinfo.hasOwnProperty('address')) this.contractInfo.sellerInfo.address = removeHtmlTag(sellerinfo.address, 300)
          if (sellerinfo.hasOwnProperty('company') && sellerinfo.company && sellerinfo.company.hasOwnProperty('address')) this.contractInfo.sellerInfo.address = removeHtmlTag(sellerinfo.company.address, 300)
          if (!sellerinfo.hasOwnProperty('company') && sellerinfo.hasOwnProperty('supplier') && sellerinfo.supplier.hasOwnProperty('address')) this.contractInfo.sellerInfo.address = removeHtmlTag(sellerinfo.supplier.address, 300)
          const isIncludingTax = localStorage.getItem('isIncludingTax')
          // this.contractInfo.isIncludingTax = isIncludingTax === 'true'
          this.contractInfo.isIncludingTax = product.isIncludingTax || isIncludingTax === 'true'
          const companyId = this.$store.state.user.companyId
          let buyerInfo = {}
          if (companyId !== -1) {
            Promise.all([supplier({ id: companyId }), getUserProfile()]).then(res => {
              const seller = res[0]
              const buyer = res[1]
              if (seller.code === 200) {
                buyerInfo.nickName = seller.data.supplier.legal
                if (seller.data.supplier.bankList.length) {
                  let idx = seller.data.supplier.bankList.findIndex(item => item.checked)
                  if (idx === -1) idx = 0
                  buyerInfo.bank = seller.data.supplier.bankList[idx].bankName
                  buyerInfo.account = seller.data.supplier.bankList[idx].bankNo
                  this.buyerBankList = seller.data.supplier.bankList
                }
                buyerInfo.address = removeHtmlTag(seller.data.company.address, 300)
                this.contractInfo.buyerName = seller.data.supplier.name
              }
              if (buyer.code === 200) {
                buyerInfo.consignor = buyer.data.realName || buyer.data.nickName
                buyerInfo.phone = buyer.data.phonenumber
              } else {
                const userInfor = this.$store.state.user.info
                buyerInfo.consignor = userInfor.realName || userInfor.nickName
                buyerInfo.phone = userInfor.phonenumber
              }
              this.contractInfo.buyerInfo = { ...buyerInfo }
            })
          }
          this.affirmData = {}
          this.isAffirm = false
          this.contractOpen = true
          this.offerOpen = false
          this.$nextTick(() => {
            if (this.$refs.qrcode) this.$refs.qrcode.innerHTML = ''
            const url = this.$router.resolve({
              path: '/prewview',
              query: {
                type: 'contract',
                requestId: data.serial
              }
            })
            const http = window.location.origin
            new QRCode(this.$refs.qrcode, {
              width: 100, // 二维码宽度
              height: 100, // 二维码高度
              text: http + url.href
            })
          })
        } else this.$message.error(msg)
      })
    },
    // 获取合同金额
    getTotals(data) {
      const arr = data.list || []
      let total = 0
      arr.map(item => {
        // const amount = item.checked[0].amount
        // const sjNum = item.checked[0].sjNum
        total += Number(item.amount) * Number(item.sjNum)
      })
      return parseFloat(total.toFixed(3)) || 0
    },
    // 切换合同模板
    handleChangeTpl(data) {
      this.$refs.contractTpl.getList(true, data, true)
    },
    // 确定选择模板
    handleSelectTpl(data) {
      this.contractTemplate = data
    },
    // 选择合同模板
    handleSubmitTpl(data) {
      this.contractTemplate = data
    },
    // 选择银行卡信息
    handleSelectBank(data, val) {
      this.checkType = val
      if (val === 'sellerInfo' && this.sellerBankList.length) {
        this.checkBankList = this.sellerBankList
        if (!this.contractInfo.sellerInfo.account) {
          this.checkedBank = this.sellerBankList.find(item => item.checked) || this.sellerBankList[0]
        } else {
          this.checkedBank = {
            bankName: this.contractInfo.sellerInfo.bank,
            bankNo: this.contractInfo.sellerInfo.account
          }
        }
        this.checkOpen = true
      } else if (val === 'buyerInfo' && this.buyerBankList.length) {
        this.checkBankList = this.buyerBankList
        if (!this.contractInfo.buyerInfo.account) {
          this.checkedBank = this.buyerBankList.find(item => item.checked) || this.buyerBankList[0]
        } else {
          this.checkedBank = {
            bankName: this.contractInfo.buyerInfo.bank,
            bankNo: this.contractInfo.buyerInfo.account
          }
        }
        this.checkOpen = true
      } else {
        this.$message.error('暂无银行卡信息，请先添加银行卡信息')
      }
    },
    // 选择银行卡
    handleBankChange(currentRow) {
      this.checkedBank = currentRow
    },
    // 确定选择
    handleSubmitCheck() {
      const type = this.checkType
      const bank = this.checkedBank
      if (type === 'sellerInfo') {
        this.contractInfo.sellerInfo.bank = bank.bankName
        this.contractInfo.sellerInfo.account = bank.bankNo
        this.checkOpen = false
      }
      if (type === 'buyerInfo') {
        this.contractInfo.buyerInfo.bank = bank.bankName
        this.contractInfo.buyerInfo.account = bank.bankNo
        this.checkOpen = false
      }
    },
    // 确认合同
    handleSubmit() {
      this.$refs['contractForm'].validate(valid => {
        if (valid) {
          const data = { ...this.contractInfo }
          delete data.list
          const products = []
          this.contractInfo.list.map(item => {
            products.push({
              listId: item.listId,
              orderDetailId: item.id,
              amount: item.amount,
              originAmount: item.originAmount,
              productId: item.productId,
              productName: item.productName,
              quantity: item.quantity,
              sjNum: item.sjNum,
              source: item.source,
              unit: item.unit,
              replyUnit: item.replyUnit || item.unit,
              replyRemark: item.replyRemark,
              remark: item.remark
            })
          })
          data.source = data.publishWay
          data.products = products
          if (!!this.contractType) data.type = this.contractType
          this.isEdit = true
          this.key = Math.random()
          this.$nextTick(() => {
            this.resizeFun()
            const element = this.$refs.contractHtml
            html2canvas(element, {
              useCORS: true
            }).then(canvas => {
              const that = this
              let image = new Image()
              image.src = canvas.toDataURL('image/png', 1.0)
              let newcanvas = document.createElement('canvas')
              newcanvas.height = element.scrollHeight
              newcanvas.width = element.scrollWidth
              const ctx = newcanvas.getContext('2d')
              image.onload = function (e) {
                ctx.drawImage(image, 0, 0, image.width, image.height, 0, 0, newcanvas.width, newcanvas.height)
                data.file = newcanvas.toDataURL('image/png', 1.0)
                that.affirmData = data
                that.isAffirm = true
              }
            })
          })
        }
      })
    },
    handleAffirm() {
      const data = {
        ...this.affirmData,
        sellerName: this.sellerName,
        demandId: this.demandId || -1,
        seller: this.sellerUser.supplier.id,
        sendUser: this.sellerUser.sendUser,
        sendPhone: this.sellerUser.sendPhone,
        sellerinfo: this.sellerinfo,
        docUser: this.docUser,
        publishWay: this.sellerUser.publishWay || 'common',
        source: this.sellerUser.publishWay || 'common',
        // 生成合同添加卖方开户行和账号
        bankName: this.affirmData.sellerInfo.bank,
        bankNo: this.affirmData.sellerInfo.account
      }
      createContract(data).then(res => {
        if (res.code === 200) {
          this.$message.success('成功生成合同')
          this.contractOpen = false
          this.open = false
          if (!this.isIndex) this.$parent.getList()
          if (!!this.contractType && this.contractType === 'promotion') this.$emit('refresh')
          if (!!this.contractType && this.contractType === 'bidding') this.$emit('refresh')
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 确认并发送
    handleAffirmSend() {
      const data = {
        ...this.affirmData,
        sellerName: this.sellerName,
        demandId: this.demandId || -1,
        seller: this.sellerUser.supplier.id,
        sendUser: this.sellerUser.sendUser,
        sendPhone: this.sellerUser.sendPhone,
        sellerinfo: this.sellerinfo,
        docUser: this.docUser,
        publishWay: this.sellerUser.publishWay || 'common',
        source: this.sellerUser.publishWay || 'common',
        // 生成合同添加卖方开户行和账号
        bankName: this.affirmData.sellerInfo.bank,
        bankNo: this.affirmData.sellerInfo.account
      }
      createContract(data).then(res => {
        if (res.code === 200) {
          this.contractOpen = false
          this.open = false
          this.$refs.send.handleSend(res.data)
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 确认并发送返回
    handleSendBack() {
      if (!this.isIndex) this.$parent.getList()
      if (!!this.contractType && this.contractType === 'promotion') this.$emit('refresh')
    },
    // 产品详情
    handleView(Id, item) {
      if (item.source === 'common') {
        getProduct(Id).then(res => {
          this.$refs.productInfo.handleView(res.data)
        })
      } else if (item.source === 'bidding') {
        detailBiddingProduct({ productId: Id }).then(res => {
          this.$refs.productInfo.handleView(res.data)
        })
      } else {
        getPrivateduct(Id).then(res => {
          this.$refs.productInfo.handleView(res.data)
        })
      }
    },
    // 删除合同中产品
    handleDeleteContrat(index) {
      const arr = this.contractInfo.list
      if (arr.length === 1) {
        this.$message.error('至少需要保留一个产品')
        return
      }
      arr.splice(index, 1)
    },
    changeEA() {
      this.isEdit = false
      this.isAffirm = false
    },
    // 调整合同图片大小
    resizeFun() {
      const devicePixelRatio = window.devicePixelRatio
      if (devicePixelRatio !== 1) {
        if (!!window.ActiveXObject || 'ActiveXObject' in window) {
        } else this.zoom = 1 / devicePixelRatio
      }
    },
    // 返回单位
    handleUnit(item) {
      if (item.replyUnit) return item.replyUnit
      return item.unit
    },
    inspectNum(item) {
      if (Number(item.sjNum) < this.minNum) {
        item.sjNum = this.minNum
        this.$message.error(`最少采购量不低于${this.minNum}`)
      }
    },
    // 点击聊天
    handleContact(row, item) {
      const { supplier = {} } = row
      const { customer = {} } = supplier
      if (!customer.userId) {
        this.$message.error('该供应商未绑定用户，无法聊天')
        return
      }
      const data = {
        userId: customer.userId,
        nick: customer.nickName,
        avatar: this.imgPath + customer.avatar || this.defaultAvatar
      }
      this.$refs.chat.send(data)
      this.$nextTick(() => {
        const product = JSON.stringify({ doType: 'sendProduct', ...item })
        this.$refs.chat.handleSend(product)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';

.offer-dialog ::v-deep {
  .el-dialog__body {
    padding: 0 20px !important;
  }

  .offer-change {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    line-height: 28px;

    .offer-total {
      font-size: 12px;
      color: #999999;

      b {
        color: $blue;
        font-weight: 500;
        font-size: 16px;
        margin: 0 5px;
      }
    }

    .option-title {
      font-size: 12px;
      color: #999999;
    }

    .option-item {
      display: inline-block;
      color: $info;
      border: 1px solid #cbd6e2;
      border-radius: 5px;
      margin-left: 15px;
      padding: 0 15px;
      cursor: pointer;

      &:hover,
      &.active {
        background-color: $blue;
        color: $white;
        border-color: $blue;
      }
    }
  }

  .offer-table {
    border: 1px solid #d1dffa;
    border-radius: 5px;
    margin-bottom: 15px;
    overflow: hidden;

    &-title {
      font-size: 14px;
      font-weight: 500;
      color: $font;
      padding: 0 20px;
      line-height: 38px;
      background-color: #ecf3ff;
    }

    .custom-table {
      border: 0;
      border-radius: 0;

      .el-form-item {
        margin-top: 10px !important;
        margin-bottom: 10px !important;

        .el-form-item__error {
          top: 95%;
          padding-top: 0;
        }
      }
    }

    &-total {
      width: 100%;
      line-height: 48px;
      font-size: 12px;
      color: #999999;
      padding: 0 20px;
      background-color: #f8f9fb;

      b {
        font-size: 16px;
        font-weight: 500;
        color: $blue;
        margin: 0 15px;
      }
    }
  }

  .offer-title {
    font-size: 14px;
    color: $info;
    padding: 18px 0;

    span {
      margin-right: 120px;
    }
  }

  .offer-account {
    display: flex;
    justify-content: space-between;
    align-items: center;
    line-height: 48px;
    padding: 0 20px;
    background-color: #fff5f5;
    border: 1px solid #ec4545;
    border-radius: 5px;
    margin-bottom: 10px;
    font-size: 14px;
    color: $info;

    span {
      margin-right: 30px;
    }

    b {
      font-size: 18px;
      font-weight: 500;
      color: #f35d09;
      margin: 0 10px;
    }
  }
}

::v-deep .custom-history-tab {
  width: 100%;
  display: inline-flex;
  align-items: center;
  line-height: 30px;

  .tab-title {
    font-size: 14px;
    color: #999999;
  }

  .tab-item {
    display: inline-block;
    color: $info;
    border: 1px solid #cbd6e2;
    border-radius: 5px;
    margin-left: 15px;
    padding: 0 25px;
    cursor: pointer;

    &:hover,
    &.active {
      background-color: $blue;
      color: $white;
      border-color: $blue;
    }
  }
}

::v-deep {
  .el-table__header-wrapper {
    .el-checkbox__inner {
      display: none !important;
    }
  }
}

.offer-dialog ::v-deep {
  .el-dialog__body {
    padding: 0 20px !important;
  }

  .offer-table {
    border: 1px solid #d1dffa;
    border-top: 0;
    border-radius: 5px;
    margin-bottom: 15px;
    overflow: hidden;

    &-title {
      width: 100%;
      height: 38px;
      background-color: #ecf3ff;
      padding-left: 20px;
      padding-right: 15px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-top: 1px solid #cbd6e2;

      .title-text {
        font-size: 14px;
        font-weight: 500;
        color: $font;
      }

      .title-btn {
        line-height: 28px;
        padding: 0 20px;
        border-radius: 5px;
        font-size: 12px;
        font-weight: 500;
        color: $white;
        border: 0;
        outline: none;
        cursor: pointer;

        &.primary {
          background-color: $blue;
        }

        &.danger {
          background-color: #ec4545;
        }

        &:hover {
          opacity: 0.8;
        }
      }

      .title-btn + .title-btn {
        margin-left: 10px;
      }
    }

    .custom-table {
      border: 0;
      border-radius: 0;

      .el-form-item {
        margin-top: 10px !important;
        margin-bottom: 10px !important;

        .el-form-item__error {
          top: 95%;
          padding-top: 0;
        }
      }
    }
  }

  .offer-title {
    font-size: 14px;
    color: $info;
    padding: 18px 0;

    span {
      margin-right: 120px;
    }
  }

  .offer-account {
    display: flex;
    justify-content: space-between;
    align-items: center;
    line-height: 48px;
    padding: 0 20px;
    background-color: #fff5f5;
    border: 1px solid #ec4545;
    border-radius: 5px;
    margin-bottom: 10px;
    font-size: 14px;
    color: $info;

    span {
      margin-right: 30px;
    }

    b {
      font-size: 18px;
      font-weight: 500;
      color: #f35d09;
      margin: 0 10px;
    }
  }
}

.contract-dialog ::v-deep {
  .suffix-select {
    max-width: 50px;

    .el-input--mini {
      .el-input__inner {
        padding: 0;
        border: 0;
        background: transparent;
        color: #c0c4cc;
      }

      .el-input__suffix {
        right: 0;
      }
    }
  }

  .el-dialog__body {
    padding: 0 !important;
  }

  .contractHtml {
    padding: 0 20px;
    overflow: hidden;
  }

  .contractBox {
    padding: 20px 20px 10px;
    border: 1px solid #cbd6e2;
    border-radius: 5px;
    background-color: $white;
    margin-bottom: 10px;

    .custom-table {
      border-bottom-left-radius: 0;
      border-bottom-right-radius: 0;
      border-bottom: 0;

      .el-form-item {
        margin-top: 10px !important;
        margin-bottom: 10px !important;

        .el-form-item__error {
          top: 95%;
          padding-top: 0;
        }
      }

      .danger {
        border-color: #ec4545;
        color: #ec4545;

        &:hover {
          background-color: #ec4545;
        }
      }
    }

    &-title {
      font-size: 20px;
      font-weight: 500;
      color: $info;
      text-align: center;
      margin: 10px 0 30px;
    }

    &-desc {
      display: flex;
      justify-content: space-between;
      padding: 0 25px;
      margin-bottom: 5px;

      .desc-right {
        min-width: 320px;
      }

      .desc-item {
        width: 320px;
        font-size: 14px;
        line-height: 28px;

        .el-input__inner {
          text-align: left;
          font-size: 12px;
        }

        span {
          color: $info;
          text-align: right;

          &.el-input__prefix {
            width: auto;
          }
        }

        b {
          color: $font;
          font-weight: 500;
        }

        & + .desc-item {
          margin-left: 30px;
        }
      }
    }

    &-htitle {
      margin: 10px 0;
      font-size: 16px;

      span {
        color: $info;
      }

      b {
        font-weight: 500;
        color: $font;
      }
    }

    &-total {
      display: flex;
      flex-direction: column;
      border: 1px solid #ec4545;
      background: #fff5f5;
      height: 96px;
      line-height: 48px;
      padding: 0 20px;
      border-bottom-left-radius: 5px;
      border-bottom-right-radius: 5px;

      .total-box {
        display: inline-flex;
        justify-content: space-between;
        align-items: center;
      }

      .total-item {
        span {
          font-size: 14px;
          color: $info;
        }

        b {
          font-size: 18px;
          font-weight: 500;
          color: $orange;
        }
      }

      .total-tip {
        font-size: 12px;
        color: $orange;
      }
    }

    &-tableBox {
      width: 100%;
      margin: 15px 0;

      ul {
        width: 100%;
        list-style: none;
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        padding: 0;
        margin: 0;
        border: 1px solid #cbd6e2;
        border-radius: 5px;
        border-bottom-width: 0;
        overflow: hidden;
      }

      li {
        width: 50%;
        line-height: 20px;
        padding: 15px 20px;
        border-right: 1px solid #cbd6e2;
        border-bottom: 1px solid #cbd6e2;
        font-size: 12px;

        &:nth-child(2n) {
          border-right-width: 0;
        }

        &:nth-child(4n + 1),
        &:nth-child(4n + 2) {
          background-color: #f8f9fb;
        }

        span {
          color: $disabled;
          margin-right: 20px;
        }

        b {
          color: $font;
          font-weight: 500;
        }
      }

      .contractBox-input {
        .el-input__inner {
          text-align: left;
          font-size: 12px;
        }
      }
    }

    &-input {
      .el-input__inner {
        border-width: 0;
        border-bottom-width: 1px;
        border-radius: 0;
        background-color: transparent;
        text-align: center;
        font-size: 14px;
        color: $font;
      }

      &.input-date {
        .el-input__inner {
          text-align: left;
        }
      }

      &.input-left {
        .el-input__inner {
          text-align: left;
        }
      }
    }
  }
}
.table-phone {
  text-wrap: nowrap;
  display: inline-flex;
  flex-wrap: nowrap;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  border-radius: 30px;
  background-color: #d7e5ff;
  color: #2e73f3;

  span {
    display: none;
  }

  &:hover {
    width: auto;
    padding: 0 10px;
    background-color: #2e73f3;
    color: #ffffff;

    span {
      display: inline-block;
    }
  }
}

.table-phone + .table-phone {
  margin-left: 10px;
}
</style>
