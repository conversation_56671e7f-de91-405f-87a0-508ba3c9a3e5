<template>
  <div class="newBox bgcf9 vh-85">
    <!-- 搜索 -->
    <div class="custom-search flex" style="padding-top: 18px">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
        <el-form-item label="订单编号" prop="serial">
          <el-input v-model="queryParams.serial" placeholder="请输入订单编号" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="发布状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="请选择发布状态" clearable>
            <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="制单人" prop="createBy" v-hasPermi="['purchasing:order:all']">
          <el-input v-model="queryParams.createBy" placeholder="请输入制单人" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" style="background-color: #2e73f3; border-color: #2e73f3" size="small" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
          <el-button icon="el-icon-s-order" size="small" @click="handleContractTpl" type="success">合同模板</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="orderBox">
      <el-table v-loading="loading" ref="orderTable" stripe :data="list" row-key="product_id" :key="key" style="width: 100%" class="custom-table" @selection-change="handleSelectionChange">
        <el-table-column align="center" type="selection" width="50"></el-table-column>
        <el-table-column align="center" type="index" label="序号"></el-table-column>
        <el-table-column align="center" prop="serial" label="订单编号" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" prop="supplier" label="供应商" show-overflow-tooltip min-width="130">
          <template slot-scope="{ row }">
            <span class="table-link" @click="handleViewSupplier(item)" v-for="item in row.supplier" :key="item.supplierId">
              <span v-if="item.publishWay === 'common'">(公域)</span>
              <span style="color: #fe7f22" v-else>(私域)</span>
              {{ item.name }}
            </span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="createTime" label="创建时间" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" label="订单金额(不含税)" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <span class="table-orange">{{ row.amount ? '￥' + parseFloat(row.amount) : '' }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="含税总金额" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <span class="table-orange">{{ row.amount ? '￥' + parseFloat(row.amount) : '' }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="状态" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <span :class="row.status ? (row.status === -10 ? 'color-disabled' : 'color-success') : 'color-red'">{{ row.status ? (row.status === -10 ? '已删除' : '已完成') : '未完成' }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="docUser" label="采购制单人" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" label="操作" width="320px" v-if="checkPermi(['purchasing:order:detail', 'purchasing:contract:generate', 'purchasing:order:del'])">
          <template slot-scope="{ row }">
            <button type="button" class="table-btn primary" @click="handleDetail(row, false)" v-hasPermi="['purchasing:order:detail']">查看详情</button>
            <button type="button" :disabled="!!row.status" class="table-btn primary" :class="{ disabled: row.status }" @click="handleDetail(row, true)" v-hasPermi="['purchasing:contract:generate']">生成合同</button>
            <button type="button" class="table-btn danger" @click="handleDelete(row)" v-hasPermi="['purchasing:order:del']" v-if="!row.status">删除</button>
          </template>
        </el-table-column>
      </el-table>

      <div class="custom-pagination">
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" local="OrderListPageSize" @pagination="getList" />
      </div>
    </div>

    <!-- 详情、生成合同 -->
    <el-dialog v-dialogDragBox :title="title" :visible.sync="open" width="1150px" class="custom-dialog offer-dialog">
      <div class="offer-title">
        <span>订单编号：{{ info.serial }}</span>
        <span>创建时间：{{ info.createTime }}</span>
      </div>
      <div class="offer-table">
        <template v-for="(item, index) in infoList">
          <el-form :ref="`form${index}`" :model="item" :rules="rules" label-width="0" :key="item.supplierId">
            <div class="offer-table-title">
              <div>
                <span class="title-text">
                  <span v-if="item.publishWay === 'common'">(公域)</span>
                  <span style="color: #fe7f22" v-else>(私域)</span>
                  {{ item.sellerName }}
                </span>
                <span style="margin-left: 10px; color: rgb(153, 153, 153); font-size: 12px" v-if="item.sendUser">业务员：{{ item.sendUser }}</span>
                <span style="margin-left: 10px; color: rgb(153, 153, 153); font-size: 12px" v-if="item.sendPhone">业务员电话：{{ item.sendPhone }}</span>
              </div>
              <div v-if="isShow">
                <button type="button" class="title-btn primary" @click="handleViewContract(item)">预览合同</button>
                <button type="button" class="title-btn primary" @click="handleSelectTpl(item)">生成合同</button>
              </div>
            </div>
            <el-table :ref="`table${index}`" stripe :data="item.list" row-key="id" style="width: 100%" class="custom-table">
              <el-table-column align="center" type="index" label="序号"></el-table-column>
              <el-table-column align="center" prop="productName" label="产品名称" show-overflow-tooltip min-width="150">
                <template slot-scope="{ row }">
                  <span class="table-link" @click="handleView(row.productId, row)">
                    <span v-if="row.source === 'common'">(公域)</span>
                    <span style="color: #fe7f22" v-else>(私域)</span>
                    {{ row.productName }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column align="center" prop="specs" label="规格">
                <template slot-scope="{ row }">
                  <span v-if="row.hasOwnProperty('product')">{{ row.product.specs }}</span>
                </template>
              </el-table-column>
              <el-table-column align="center" prop="model" label="型号">
                <template slot-scope="{ row }">
                  <span v-if="row.hasOwnProperty('product')">{{ row.product.model }}</span>
                </template>
              </el-table-column>
              <el-table-column align="center" prop="remark" label="备注"></el-table-column>
              <el-table-column align="center" prop="quantity" label="计划采购数量">
                <template slot-scope="{ row }">{{ row.quantity + row.unit }}</template>
              </el-table-column>
              <el-table-column align="center" prop="amount" label="产品单价">
                <template slot-scope="{ row }">
                  <span class="table-orange">{{ row.amount ? '￥' + row.amount : '' }}{{ '元' + (row.replyUnit || row.unit ? '/' : '') + (row.replyUnit || row.unit || '') }}</span>
                </template>
              </el-table-column>
              <el-table-column align="center" prop="replyRemark" label="报价备注"></el-table-column>
              <el-table-column align="center" prop="sjNum" label="采购量">
                <template slot-scope="{ row }">{{ row.sjNum + row.replyUnit }}</template>
              </el-table-column>
              <el-table-column align="center" prop="total" label="小计">
                <template slot-scope="{ row }">
                  <span class="table-orange">{{ row.total ? '￥' + row.total : '' }}</span>
                </template>
              </el-table-column>
            </el-table>
          </el-form>
        </template>
      </div>
      <div class="offer-account">
        <div class="inline-flex">
          <span class="inline-flex">
            共
            <b>{{ getSize(infoList) }}</b>
            件产品
          </span>
          <span class="inline-flex">
            <b>{{ infoList.length }}</b>
            家供应商
          </span>
        </div>
        <span class="inline-flex" style="margin-right: 0">
          订单总金额：
          <b>￥{{ getTotal(infoList) }}</b>
        </span>
      </div>
      <div slot="footer">
        <button type="button" class="custom-dialog-btn primary" @click="open = false">关闭</button>
      </div>
    </el-dialog>

    <!-- 合同预览 -->
    <el-dialog v-dialogDragBox title="预览合同" :visible.sync="contractOpen" width="1150px" class="custom-dialog offer-dialog contract-dialog">
      <div v-if="isAffirm">
        <img style="max-width: 100%" :src="affirmData.file" />
      </div>
      <div ref="contractHtml" class="contractHtml" v-else>
        <el-form ref="contractForm" :model="contractInfo" :rules="contractRules">
          <div class="offer-title">
            <span>订单编号：{{ contractInfo.orderNumber }}</span>
          </div>
          <div class="contractBox">
            <div class="contractBox-title">采购合同</div>
            <div class="contractBox-desc">
              <div class="inline-flex flex-column flex-align-start">
                <div>
                  <div class="desc-item inline-flex">
                    <span>买方：</span>
                    <b v-if="isEdit">{{ contractInfo.buyerName }}</b>
                    <b v-else>
                      <el-input v-model="contractInfo.buyerName" placeholder="请输入买方" size="mini" class="contractBox-input" style="width: 250px"></el-input>
                    </b>
                  </div>
                  <div class="desc-item inline-flex">
                    <span>合同编号：</span>
                    <b style="padding-left: 15px">{{ contractInfo.serial }}</b>
                  </div>
                </div>
                <div>
                  <div class="desc-item inline-flex">
                    <span>卖方：</span>
                    <b v-if="isEdit">{{ contractInfo.sellerName }}</b>
                    <b v-else>
                      <el-input v-model="contractInfo.sellerName" placeholder="请输入卖方" size="mini" class="contractBox-input" style="width: 250px"></el-input>
                    </b>
                  </div>
                  <div class="desc-item inline-flex">
                    <span>签订地点：</span>
                    <b v-if="isEdit">{{ contractInfo.address }}</b>
                    <b v-else>
                      <el-input v-model="contractInfo.address" placeholder="请输入签订地点" size="mini" class="contractBox-input" style="width: 250px"></el-input>
                    </b>
                  </div>
                </div>
                <div>
                  <div class="desc-item inline-flex"></div>
                  <div class="desc-item inline-flex">
                    <span>签订时间：</span>
                    <b v-if="isEdit">{{ contractInfo.signingTime }}</b>
                    <b v-else>
                      <el-date-picker v-model="contractInfo.signingTime" type="date" placeholder="请选择签订时间" size="mini" class="contractBox-input input-date" style="width: 250px"></el-date-picker>
                    </b>
                  </div>
                </div>
              </div>
              <div class="inline-flex flex-column flex-align-start">
                <div id="qrcode" ref="qrcode"></div>
              </div>
            </div>
            <div class="contractBox-htitle"><span>产品采购清单</span></div>
            <el-table ref="contractTable" stripe :data="contractInfo.list" :key="key" style="width: 100%" class="custom-table custom-table-cell10">
              <el-table-column align="center" type="index" label="序号"></el-table-column>
              <el-table-column align="center" prop="productName" label="产品名称" show-overflow-tooltip min-width="120">
                <template slot-scope="{ row }">
                  <span class="table-link" v-if="isEdit">{{ row.productName }}</span>
                  <span class="table-link" @click="handleView(row.productId, row)" v-else>
                    <span v-if="row.source === 'common'">(公域)</span>
                    <span style="color: #fe7f22" v-else>(私域)</span>
                    {{ row.productName }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column align="center" prop="specs" label="规格" show-overflow-tooltip width="100">
                <template slot-scope="{ row }">
                  <span v-if="row.hasOwnProperty('product')">{{ row.product.specs }}</span>
                </template>
              </el-table-column>
              <el-table-column align="center" prop="model" label="型号">
                <template slot-scope="{ row }">
                  <span v-if="row.hasOwnProperty('product')">{{ row.product.model }}</span>
                </template>
              </el-table-column>
              <el-table-column align="center" label="计划采购数量" show-overflow-tooltip width="100" v-if="!isEdit">
                <template slot-scope="{ row }">{{ row.quantity + row.unit }}</template>
              </el-table-column>
              <el-table-column align="center" prop="amount" label="产品单价">
                <template slot-scope="scope">
                  <span class="table-orange" v-if="isEdit">{{ scope.row.amount ? '￥' + parseFloat(scope.row.amount) : '' }}{{ '元' + (scope.row.replyUnit || scope.row.unit ? '/' : '') + (scope.row.replyUnit || scope.row.unit || '') }}</span>
                  <template v-else>
                    <el-form-item label-width="0" :prop="`list.${scope.$index}.amount`" :rules="contractRules.amount">
                      <el-input v-model="scope.row.amount" size="small" placeholder="请输入产品单价">
                        <span slot="suffix">{{ '元/' + (scope.row.replyUnit || scope.row.unit) }}</span>
                      </el-input>
                    </el-form-item>
                  </template>
                </template>
              </el-table-column>
              <el-table-column align="center" prop="quantity" label="采购量" width="100">
                <template slot-scope="scope">
                  <span v-if="isEdit">{{ scope.row.sjNum + (scope.row.replyUnit || scope.row.unit || '') }}</span>
                  <template v-else>
                    <el-form-item label-width="0" :prop="`list.${scope.$index}.quantity`" :rules="contractRules.sjNum">
                      <el-input v-model="scope.row.sjNum" size="small" placeholder="请输入实际采购数量">
                        <span slot="suffix">{{ scope.row.replyUnit || scope.row.unit || '' }}</span>
                      </el-input>
                    </el-form-item>
                  </template>
                </template>
              </el-table-column>
              <el-table-column align="center" prop="replyRemark" label="报价备注" show-overflow-tooltip v-if="!isEdit"></el-table-column>
              <el-table-column align="center" prop="remark" label="备注" show-overflow-tooltip></el-table-column>
              <el-table-column align="center" label="操作" v-if="!isEdit">
                <template slot-scope="scope">
                  <button type="button" class="table-btn danger" @click="handleDeleteContrat(scope.$index)" v-if="!isEdit">删除</button>
                </template>
              </el-table-column>
            </el-table>
            <div class="contractBox-total">
              <div class="total-box">
                <div class="total-item">
                  <span>
                    共
                    <b>{{ contractInfo.list ? contractInfo.list.length : 0 }}</b>
                    件产品
                  </span>
                </div>
                <div class="total-item">
                  <span>
                    订单总金额：
                    <b>￥ {{ getTotals(contractInfo) }}</b>
                  </span>
                </div>
                <div class="total-item">
                  <span>
                    人民币总金额：
                    <b>{{ lowerConversionUpper(getTotals(contractInfo)) }}</b>
                  </span>
                </div>
              </div>
              <div class="total-box" style="justify-content: flex-start">
                <div class="total-item" style="margin-right: 110px">
                  <span>
                    是否含税：
                    <el-switch v-model="contractInfo.isIncludingTax" active-text="含税" inactive-text="不含税" @change="handleChangeTaxSwitch"></el-switch>
                  </span>
                </div>
                <div class="total-tip">
                  <span v-if="isEdit">注：{{ contractInfo.conTip }}</span>
                  <span v-else>
                    注：
                    <el-input v-model="contractInfo.conTip" size="mini" class="contractBox-input input-left" style="width: 400px"></el-input>
                  </span>
                </div>
              </div>
            </div>
            <div style="margin-top: 20px">
              <div style="margin-bottom: 10px; text-align: right" v-if="!isEdit">
                <el-button type="primary" size="small" icon="el-icon-connection" @click="handleChangeTpl(contractInfo)">切换合同模板</el-button>
              </div>
              <div v-html="contractInfo.file" v-if="isEdit"></div>
              <editor v-model="contractInfo.file" :min-height="500" v-else />
            </div>
            <div class="contractBox-tableBox">
              <ul>
                <li>
                  <span>卖方：</span>
                  <b>{{ contractInfo.sellerName }}</b>
                </li>
                <li>
                  <span>买方：</span>
                  <b>{{ contractInfo.buyerName }}</b>
                </li>
                <li class="inline-flex">
                  <span>法定代表人：</span>
                  <b v-if="isEdit">{{ contractInfo.sellerInfo.nickName }}</b>
                  <b v-else>
                    <el-input v-model="contractInfo.sellerInfo.nickName" size="mini" class="contractBox-input" style="width: 120px"></el-input>
                  </b>
                </li>
                <li class="inline-flex">
                  <span>法定代表人：</span>
                  <b v-if="isEdit">{{ contractInfo.buyerInfo.nickName }}</b>
                  <b v-else>
                    <el-input v-model="contractInfo.buyerInfo.nickName" size="mini" class="contractBox-input" style="width: 120px"></el-input>
                  </b>
                </li>
                <li class="inline-flex">
                  <span>
                    <el-tooltip class="item" effect="dark" content="无默认联系人，需手动填写" placement="top" v-if="!isEdit">
                      <i class="el-icon-question"></i>
                    </el-tooltip>
                    委托代理人：
                  </span>
                  <b style="width: 80px" v-if="isEdit">{{ contractInfo.sellerInfo.consignor }}</b>
                  <b v-else>
                    <el-input v-model="contractInfo.sellerInfo.consignor" size="mini" class="contractBox-input" style="width: 80px"></el-input>
                  </b>
                  <span style="margin-left: 10px">法人或委托代理人签字：</span>
                </li>
                <li class="inline-flex">
                  <span>委托代理人：</span>
                  <b v-if="isEdit">{{ contractInfo.buyerInfo.consignor }}</b>
                  <b v-else>
                    <el-input v-model="contractInfo.buyerInfo.consignor" size="mini" class="contractBox-input" style="width: 120px"></el-input>
                  </b>
                </li>
                <li class="inline-flex">
                  <span>电话和微信号码：</span>
                  <b v-if="isEdit">{{ contractInfo.sellerInfo.phone }}</b>
                  <b v-else>
                    <el-input v-model="contractInfo.sellerInfo.phone" size="mini" class="contractBox-input" style="width: 120px"></el-input>
                  </b>
                </li>
                <li class="inline-flex">
                  <span>电话和微信号码：</span>
                  <b v-if="isEdit">{{ contractInfo.buyerInfo.phone }}</b>
                  <b v-else>
                    <el-input v-model="contractInfo.buyerInfo.phone" size="mini" class="contractBox-input" style="width: 120px"></el-input>
                  </b>
                </li>
                <li class="inline-flex">
                  <span>开户行：</span>
                  <b v-if="isEdit">{{ contractInfo.sellerInfo.bank }}</b>
                  <b class="inline-flex" v-else>
                    <el-input v-model="contractInfo.sellerInfo.bank" size="mini" class="contractBox-input"></el-input>
                    <el-button icon="el-icon-search" circle size="mini" style="margin-left: 10px" @click="handleSelectBank(contractInfo, 'sellerInfo')"></el-button>
                  </b>
                </li>
                <li class="inline-flex">
                  <span>开户行：</span>
                  <b v-if="isEdit">{{ contractInfo.buyerInfo.bank }}</b>
                  <b class="inline-flex" v-else>
                    <el-input v-model="contractInfo.buyerInfo.bank" size="mini" class="contractBox-input"></el-input>
                    <el-button icon="el-icon-search" circle size="mini" style="margin-left: 10px" @click="handleSelectBank(contractInfo, 'buyerInfo')"></el-button>
                  </b>
                </li>
                <li class="inline-flex">
                  <span>账号：</span>
                  <b v-if="isEdit">{{ contractInfo.sellerInfo.account }}</b>
                  <b v-else>
                    <el-input v-model="contractInfo.sellerInfo.account" size="mini" class="contractBox-input" style="width: 250px"></el-input>
                  </b>
                </li>
                <li class="inline-flex">
                  <span>账号：</span>
                  <b v-if="isEdit">{{ contractInfo.buyerInfo.account }}</b>
                  <b v-else>
                    <el-input v-model="contractInfo.buyerInfo.account" size="mini" class="contractBox-input" style="width: 250px"></el-input>
                  </b>
                </li>
                <li class="inline-flex">
                  <span>地址：</span>
                  <b v-if="isEdit">{{ contractInfo.sellerInfo.address }}</b>
                  <b v-else>
                    <el-input v-model="contractInfo.sellerInfo.address" size="mini" class="contractBox-input" style="width: 350px"></el-input>
                  </b>
                </li>
                <li class="inline-flex">
                  <span>地址：</span>
                  <b v-if="isEdit">{{ contractInfo.buyerInfo.address }}</b>
                  <b v-else>
                    <el-input v-model="contractInfo.buyerInfo.address" size="mini" class="contractBox-input" style="width: 350px"></el-input>
                  </b>
                </li>
              </ul>
            </div>
          </div>
        </el-form>
      </div>
      <div slot="footer">
        <template v-if="isAffirm">
          <button
            type="button"
            class="custom-dialog-btn"
            @click="
              isEdit = false
              isAffirm = false
            ">
            修改合同
          </button>
          <button type="button" class="custom-dialog-btn primary" @click="handleAffirm">确认生成合同</button>
        </template>
        <template v-else>
          <button type="button" class="custom-dialog-btn" @click="contractOpen = false">取消</button>
          <button type="button" class="custom-dialog-btn primary" @click="handleSubmit" :disabled="isEdit">生成合同</button>
        </template>
      </div>
    </el-dialog>

    <!-- 产品详情 -->
    <product-dialog ref="productInfo"></product-dialog>

    <!--  选择银行卡信息  -->
    <el-dialog v-dialogDragBox title="选择银行卡信息" :visible.sync="checkOpen" width="1150px" class="custom-dialog">
      <div style="padding: 0 20px">
        <el-table ref="bank" :data="checkBankList" style="width: 100%" stripe class="custom-table custom-table-cell10" @row-click="handleBankChange">
          <el-table-column align="center" label="选择" width="55">
            <template slot-scope="{ row }">
              <el-radio v-model="checkedBank.bankNo" :label="row.bankNo"><span /></el-radio>
            </template>
          </el-table-column>
          <el-table-column prop="bankName" label="开户行" align="center"></el-table-column>
          <el-table-column prop="bankNo" label="银行账号" align="center"></el-table-column>
          <el-table-column prop="bankUser" label="账户名" align="center"></el-table-column>
        </el-table>
      </div>
      <div slot="footer">
        <button type="button" class="custom-dialog-btn" @click="checkOpen = false">取消</button>
        <button type="button" class="custom-dialog-btn primary" :class="{ disabled: !checkedBank }" :disabled="!checkedBank" @click="handleSubmitCheck">确定</button>
      </div>
    </el-dialog>

    <!-- 供应商信息 -->
    <supplier-dialog ref="supplier"></supplier-dialog>
    <!--  合同模板  -->
    <contract-tpl ref="contractTpl"></contract-tpl>
  </div>
</template>

<script>
import html2canvas from 'html2canvas'
import ProductDialog from '@/views/public/product/dialog'
import { getProduct } from '@/api/system/product'
import { getPrivateduct } from '@/api/system/privateduct'
import { contractTemplateList, createContract, createContractSerial, purchasingOrderDelete, purchasingOrderDetail, purchasingOrderList, purchasingOrderListSupplier } from '@/api/purchase'
import { privateSupb } from '@/api/houtai/siyu/gongying'
import { supplier } from '@/api/system/user'
import { isNumber, isNumberLength } from '@/utils/validate'
import { lowerConversionUpper, removeHtmlTag } from '@/utils'
import { checkPermi } from '@/utils/permission'
import QRCode from 'qrcodejs2'
import supplierDialog from '@/views/purchase/demandForMe/supplier'
import contractTpl from '@/views/purchase/contract/template'

export default {
  components: { supplierDialog, ProductDialog, contractTpl },
  data() {
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        serial: undefined,
        createBy: undefined,
        status: 0
      },
      statusOptions: [
        { label: '未完成', value: 0 },
        { label: '已完成', value: 1 },
        { label: '已删除', value: -10 }
      ],
      key: 1,
      loading: true,
      list: [],
      total: 0,
      checkedList: [],
      // 详情
      isShow: false, // 是否显示操作按钮
      title: undefined,
      open: false,
      info: {},
      infoList: [],
      rules: {},
      // 预览合同
      contractOpen: false,
      contractInfo: {
        sellerInfo: {
          nickName: undefined,
          consignor: undefined,
          phone: undefined,
          bank: undefined,
          account: undefined,
          address: undefined
        },
        buyerInfo: {
          nickName: undefined,
          consignor: undefined,
          phone: undefined,
          bank: undefined,
          account: undefined,
          address: undefined
        }
      },
      contractRules: {
        amount: [
          { required: true, message: '请输入产品单价', trigger: 'blur' },
          { validator: isNumber, message: '请输入正确的金额', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 10), message: '只可以填写十位小数', trigger: 'blur' }
        ],
        sjNum: [
          { required: true, message: '请输入实际采购数量', trigger: 'blur' },
          { validator: isNumber, message: '请输入正确的数量', trigger: 'blur' }
        ]
      },
      isEdit: false,
      qrcode: undefined,
      sellerBankList: [],
      buyerBankList: [],
      checkBankList: [],
      checkType: undefined,
      checkedBank: undefined,
      checkOpen: false,
      isAffirm: false,
      affirmData: {},
      docUser: undefined
    }
  },
  created() {
    this.getList()
  },
  methods: {
    removeHtmlTag,
    checkPermi,
    lowerConversionUpper,
    // 列表
    getList() {
      this.loading = true
      const localPageSize = localStorage.getItem('OrderListPageSize')
      if (localPageSize) this.queryParams.pageSize = parseInt(localPageSize)
      purchasingOrderList(this.queryParams).then(async res => {
        if (res.code === 200) {
          await Promise.all(
            res.rows.map(async item => {
              item.supplier = []
              const info = await purchasingOrderListSupplier({ orderId: item.id })
              const { code, msg, data } = info
              if (code === 200) item.supplier = data
              else this.$message.error(msg)
            })
          )
          this.list = res.rows
          this.total = res.total
          this.loading = false
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 重置搜索
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 多选
    handleSelectionChange(selection) {
      this.checkedList = selection
    },
    // 查看订单详情
    async handleDetail(row, show) {
      this.docUser = row.docUser
      const orderId = row.id
      const res = await purchasingOrderDetail({ orderId })
      if (res.code === 200) {
        this.info = res.data
        const arr = [...res.data.detailList]
        await Promise.all(
          arr.map(async item => {
            if (item.source === 'common') {
              const info = await getProduct(item.productId)
              if (info.code === 200) item.product = info.data
            }
            if (item.source === 'private') {
              const info = await getPrivateduct(item.productId)
              if (info.code === 200) item.product = info.data
            }
          })
        )
        const infoList = this.checkList(arr, res.data.serial)
        await Promise.all(
          infoList.map(async item => {
            item.sellerName = ''
            item.sellerinfo = undefined
            if (item.publishWay === 'common') {
              const supplierInfo = await supplier({ id: item.seller })
              item.sellerName = supplierInfo.data.company ? supplierInfo.data.company.companyName : supplierInfo.data.supplier.name
              item.sellerinfo = supplierInfo.data
            }
            if (item.publishWay === 'private') {
              const supplier = await privateSupb({ id: item.seller })
              item.sellerName = supplier.data.name
              item.sellerinfo = supplier.data
            }
          })
        )
        this.infoList = infoList
        this.title = show ? '生成合同' : '查看详情'
        this.isShow = show
        this.open = true
      } else {
        this.$message.error(res.msg)
      }
    },
    // 按照供应商分组
    checkList(arr, orderId) {
      let map = {}
      let data = []
      for (let i = 0; i < arr.length; i++) {
        const item = arr[i]
        arr[i]['total'] = parseFloat((Number(arr[i]['amount']) * Number(arr[i]['sjNum'])).toFixed(3))
        if (!map[item.supplierId] && !map[item.publishWay]) {
          data.push({
            seller: item.supplierId,
            publishWay: item.publishWay,
            orderId: item.orderId,
            orderNumber: orderId,
            sendUser: item.sendUser,
            sendPhone: item.sendPhone,
            list: [item]
          })
          map[item.supplierId] = item
        } else {
          for (let j = 0; j < data.length; j++) {
            let jtem = data[j]
            if (jtem.seller === item.supplierId) {
              jtem.list.push(item)
              break
            }
          }
        }
      }
      return data
    },
    // 产品数量
    getSize(data) {
      let arr = []
      data.map(item => {
        arr = arr.concat(item.list)
      })
      const newArr = arr.map(item => item.productId)
      return Array.from(new Set(newArr)).length
    },
    // 获取订单金额
    getTotal(data) {
      let arr = []
      data.map(item => {
        arr = arr.concat(item.list)
      })
      let total = 0
      arr.map(item => {
        total += Number(item.amount) * Number(item.sjNum)
      })
      return parseFloat(total.toFixed(3)) || 0
    },
    // 产品详情
    handleView(Id, item) {
      if (item.source === 'common') {
        getProduct(Id).then(res => {
          this.$refs.productInfo.handleView(res.data)
        })
      } else {
        getPrivateduct(Id).then(res => {
          this.$refs.productInfo.handleView(res.data)
        })
      }
    },
    // 删除订单
    // prettier-ignore
    handleDelete(row) {
      const data = { orderId: row.id, status: -10 }
      this.$modal.confirm('是否确认删除该订单？').then(function () {
        return purchasingOrderDelete(data)
      }).then(() => {
        this.getList()
        this.$message.success('操作成功')
      }).catch(() => {
      })
    },
    resetContractInfo() {
      this.contractInfo = {
        orderNumber: undefined,
        buyerName: undefined,
        sellerName: undefined,
        serial: undefined,
        address: undefined,
        signingTime: undefined,
        isIncludingTax: false,
        list: [],
        conTip: '不包含运费、安装费',
        transportText1: '买方',
        transportText2: '卖方',
        addressText1: '买方到卖方厂区自提',
        addressText2: '买方指定具体收货地点',
        addressName: undefined,
        checkText: '收到产品后3日内',
        finalText1: '合同签订当日，买方支付产品全款',
        finalText2: '买方收到产品后1日内支付全款',
        disputeText: '合同签订地',
        effectiveText: '双方签字或盖章',
        sellerInfo: {
          nickName: undefined,
          consignor: undefined,
          phone: undefined,
          bank: undefined,
          account: undefined,
          address: undefined
        },
        buyerInfo: {
          nickName: undefined,
          consignor: undefined,
          phone: undefined,
          bank: undefined,
          account: undefined,
          address: undefined
        }
      }
      this.resetForm('contractForm')
    },
    // 修改是否含税
    handleChangeTaxSwitch(e) {
      localStorage.setItem('isIncludingTax', e)
    },
    // 生成合同
    handleContract(row) {
      this.isEdit = false
      this.resetContractInfo()
      const info = {
        address: '河北省邯郸市永年区',
        signingTime: this.parseTime(new Date().getTime(), '{y}-{m}-{d}'),
        conTip: '不包含运费、安装费',
        transportText1: '买方',
        transportText2: '卖方',
        addressText1: '买方到卖方厂区自提',
        addressText2: '买方指定具体收货地点',
        checkText: '收到产品后3日内',
        finalText1: '合同签订当日，买方支付产品全款',
        finalText2: '买方收到产品后1日内支付全款',
        disputeText: '合同签订地',
        effectiveText: '双方签字或盖章'
      }
      this.contractInfo = { ...this.contractInfo, ...JSON.parse(JSON.stringify(row)), ...info }
      const data = { seller: this.contractInfo.seller }
      const { sellerinfo } = this.contractInfo
      createContractSerial(data).then(res => {
        if (res.code === 200) {
          // this.contractInfo.buyerName = res.data.buyerName
          this.contractInfo.addressName = res.data.buyerName
          this.contractInfo.serial = res.data.serial
          if (sellerinfo.hasOwnProperty('legal')) this.contractInfo.sellerInfo.nickName = sellerinfo.legal
          if (!sellerinfo.hasOwnProperty('legal') && sellerinfo.hasOwnProperty('supplier') && sellerinfo.supplier.hasOwnProperty('legal')) this.contractInfo.sellerInfo.nickName = sellerinfo.supplier.legal
          this.contractInfo.sellerInfo.consignor = this.contractInfo.sendUser
          this.contractInfo.sellerInfo.phone = this.contractInfo.sendPhone
          let idx
          if (sellerinfo.hasOwnProperty('bankList') && sellerinfo.bankList.length) idx = sellerinfo.bankList.findIndex(item => item.checked)
          if (!sellerinfo.hasOwnProperty('bankList') && sellerinfo.hasOwnProperty('banks') && sellerinfo.banks.length) idx = sellerinfo.banks.findIndex(item => item.checked)
          if (idx === -1) idx = 0
          if (sellerinfo.hasOwnProperty('bankList') && sellerinfo.bankList.length) {
            this.contractInfo.sellerInfo.bank = sellerinfo.bankList[idx].bankName
            this.contractInfo.sellerInfo.account = sellerinfo.bankList[idx].bankNo
            this.sellerBankList = sellerinfo.bankList
          }
          if (!sellerinfo.hasOwnProperty('bankList') && sellerinfo.hasOwnProperty('banks') && sellerinfo.banks.length) {
            this.contractInfo.sellerInfo.bank = sellerinfo.banks[idx].bankName
            this.contractInfo.sellerInfo.account = sellerinfo.banks[idx].bankNo
            this.sellerBankList = sellerinfo.banks
          }
          if (sellerinfo.hasOwnProperty('company') && sellerinfo.company && sellerinfo.company.hasOwnProperty('address')) this.contractInfo.sellerInfo.address = removeHtmlTag(sellerinfo.company.address, 300)
          if (!sellerinfo.hasOwnProperty('company') && sellerinfo.hasOwnProperty('supplier') && sellerinfo.supplier.hasOwnProperty('address')) this.contractInfo.sellerInfo.address = removeHtmlTag(sellerinfo.supplier.address, 300)
          const isIncludingTax = localStorage.getItem('isIncludingTax')
          this.contractInfo.isIncludingTax = isIncludingTax === 'true'
          const companyId = this.$store.state.user.companyId
          let buyerInfo = {}
          if (companyId !== -1) {
            supplier({ id: companyId }).then(res => {
              const { code, msg, data } = res
              if (code === 200) {
                buyerInfo.nickName = data.supplier.legal
                const userInfor = this.$store.state.user.info
                buyerInfo.consignor = userInfor.nickName
                buyerInfo.phone = userInfor.phonenumber
                if (data.supplier.bankList.length) {
                  let idx = data.supplier.bankList.findIndex(item => item.checked)
                  if (idx === -1) idx = 0
                  buyerInfo.bank = data.supplier.bankList[idx].bankName
                  buyerInfo.account = data.supplier.bankList[idx].bankNo
                  this.buyerBankList = data.supplier.bankList
                }
                buyerInfo.address = removeHtmlTag(data.company.address, 300)
                this.contractInfo.buyerName = data.supplier.name
                this.contractInfo.buyerInfo = { ...buyerInfo }
              } else this.$message.error(msg)
            })
          }
          this.affirmData = {}
          this.isAffirm = false
          this.contractOpen = true
          this.$nextTick(() => {
            if (this.$refs.qrcode) this.$refs.qrcode.innerHTML = ''
            const url = this.$router.resolve({
              path: '/prewview',
              query: {
                type: 'contract',
                requestId: res.data.serial
              }
            })
            const http = window.location.origin
            new QRCode(this.$refs.qrcode, {
              width: 100, // 二维码宽度
              height: 100, // 二维码高度
              text: http + url.href
            })
          })
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 选择合同模板
    handleSelectTpl(row) {
      contractTemplateList().then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          const index = data.findIndex(item => item.isDefault)
          if (index > -1) {
            const obj = { ...row, ...{ file: data[index].file } }
            this.handleContract(obj)
          } else {
            const obj = {
              ...row,
              ...{
                file: '<p>一、运输和包装：<b>买方</b>负责运输并支付运费，<b>卖方</b>负责包装及装车。</p><p>二、质量标准：按照标准执行。</p><p>三、收货地点及收货人：<b>买方到卖方厂区自提</b>或<b>买方指定具体收货地点</b>；收货人：_______。</p><p>四、验收方式及期限：买方<b>收到产品后3日内</b>，按照双方约定质量标准进行验收；买方逾期未对产品质量提出书面异议的，视为对产品质量无异议。</p><p>五、结算方式及期限：<b>合同签订当日，买方支付产品全款</b>或<b>买方收到产品后1日内支付全款</b>。</p><p>六、卖方郑重声明：货款等资金往来禁止使用现金、银行承兑及商业承兑，必须付款至卖方指定账号，除此之外的现金往来，均属于个人行为，与卖方无关。</p><p>七、违约责任：按《民法典》相关条款执行。</p><p>八、争议解决方式：本合同项下发生的争议，由买卖双方当事人协商解决，协商不成的，依法<b>向合同签订地</b>人民法院起诉。</p><p>九、本合同自<b>双方签字或盖章</b>时生效，其复印件、扫描件同样具有法律效力。</p>'
              }
            }
            this.handleContract(obj)
          }
        } else {
          this.$message.error(msg)
        }
      })
      // this.$refs.contractTpl.getList(true, row)
    },
    // 切换合同模板
    handleChangeTpl(data) {
      this.$refs.contractTpl.getList(true, data, true)
    },
    // 选择合同模板
    handleSubmitTpl(data) {
      this.contractInfo = { ...data }
    },
    // 选择银行卡信息
    handleSelectBank(data, val) {
      this.checkType = val
      if (val === 'sellerInfo' && this.sellerBankList.length) {
        this.checkBankList = this.sellerBankList
        if (!this.contractInfo.sellerInfo.account) {
          this.checkedBank = this.sellerBankList.find(item => item.checked) || this.sellerBankList[0]
        } else {
          this.checkedBank = {
            bankName: this.contractInfo.sellerInfo.bank,
            bankNo: this.contractInfo.sellerInfo.account
          }
        }
        this.checkOpen = true
      } else if (val === 'buyerInfo' && this.buyerBankList.length) {
        this.checkBankList = this.buyerBankList
        if (!this.contractInfo.buyerInfo.account) {
          this.checkedBank = this.buyerBankList.find(item => item.checked) || this.buyerBankList[0]
        } else {
          this.checkedBank = {
            bankName: this.contractInfo.buyerInfo.bank,
            bankNo: this.contractInfo.buyerInfo.account
          }
        }
        this.checkOpen = true
      } else {
        this.$message.error('暂无银行卡信息，请先添加银行卡信息')
      }
    },
    // 选择银行卡
    handleBankChange(currentRow) {
      this.checkedBank = currentRow
    },
    // 确定选择
    handleSubmitCheck() {
      const type = this.checkType
      const bank = this.checkedBank
      if (type === 'sellerInfo') {
        this.contractInfo.sellerInfo.bank = bank.bankName
        this.contractInfo.sellerInfo.account = bank.bankNo
        this.checkOpen = false
      }
      if (type === 'buyerInfo') {
        this.contractInfo.buyerInfo.bank = bank.bankName
        this.contractInfo.buyerInfo.account = bank.bankNo
        this.checkOpen = false
      }
    },
    // 获取合同金额
    getTotals(data) {
      const arr = data.list || []
      let total = 0
      arr.map(item => {
        total += Number(item.amount) * Number(item.sjNum)
      })
      return parseFloat(total.toFixed(3)) || 0
    },
    // 删除合同中产品
    handleDeleteContrat(index) {
      const arr = this.contractInfo.list
      if (arr.length === 1) {
        this.$message.error('至少需要保留一个产品')
        return
      }
      arr.splice(index, 1)
    },
    // 确认合同
    handleSubmit() {
      this.$refs['contractForm'].validate(valid => {
        if (valid) {
          const data = { ...this.contractInfo }
          delete data.list
          const products = []
          this.contractInfo.list.map(item => {
            products.push({
              listId: item.listId,
              orderDetailId: item.id,
              amount: item.amount,
              originAmount: item.amount,
              productId: item.productId,
              productName: item.productName,
              quantity: item.quantity,
              sjNum: item.sjNum,
              source: item.source,
              unit: item.unit,
              replyUnit: item.replyUnit || item.unit,
              replyRemark: item.replyRemark,
              remark: item.remark
            })
          })
          data.source = data.publishWay
          data.products = products
          this.isEdit = true
          this.key = Math.random()
          this.$nextTick(() => {
            const element = this.$refs.contractHtml
            html2canvas(element, {
              useCORS: true
            }).then(canvas => {
              const that = this
              let image = new Image()
              image.src = canvas.toDataURL('image/png', 1.0)
              let newcanvas = document.createElement('canvas')
              newcanvas.height = element.scrollHeight
              newcanvas.width = element.scrollWidth
              const ctx = newcanvas.getContext('2d')
              image.onload = function (e) {
                ctx.drawImage(image, 0, 0, image.width, image.height, 0, 0, newcanvas.width, newcanvas.height)
                data.file = newcanvas.toDataURL('image/png', 1.0)
                that.affirmData = data
                that.isAffirm = true
              }
            })
          })
        }
      })
    },
    handleAffirm() {
      const data = { ...this.affirmData, docUser: this.docUser }
      createContract(data).then(res => {
        if (res.code === 200) {
          this.$message.success('成功生成合同')
          this.contractOpen = false
          this.open = false
          this.getList()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 预览合同
    handleViewContract(data) {
      this.$router.push({ path: '/demand/contract', query: { orderId: data.orderId, seller: data.seller } })
    },
    // 查看供应商详情
    handleViewSupplier(row) {
      const id = row.supplierId
      this.$refs.supplier.getInfo(id, row.publishWay)
    },
    // 打开合同模板
    handleContractTpl() {
      this.$refs.contractTpl.getList()
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
.orderBox {
  margin: 15px 20px;
  padding: 20px;
  border-radius: 5px;
  background-color: $white;
  box-shadow: 0 0 26px 0 rgba(0, 0, 0, 0.05);
}
.batch-btn {
  padding: 0 20px;
  line-height: 30px;
  font-size: 12px;
  border-radius: 5px;
  border: 0;
  cursor: pointer;
  &:hover {
    opacity: 0.8;
  }
  &:disabled {
    cursor: no-drop;
  }
  &.primary {
    background-color: $blue;
    color: $white;
    &.disabled {
      background-color: #cdcdcd;
    }
  }
}
.offer-dialog ::v-deep {
  .el-dialog__body {
    padding: 0 20px !important;
  }
  .offer-table {
    border: 1px solid #d1dffa;
    border-top: 0;
    border-radius: 5px;
    margin-bottom: 15px;
    overflow: hidden;
    &-title {
      width: 100%;
      height: 38px;
      background-color: #ecf3ff;
      padding-left: 20px;
      padding-right: 15px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-top: 1px solid #cbd6e2;
      .title-text {
        font-size: 14px;
        font-weight: 500;
        color: $font;
      }
      .title-btn {
        line-height: 28px;
        padding: 0 20px;
        border-radius: 5px;
        font-size: 12px;
        font-weight: 500;
        color: $white;
        border: 0;
        outline: none;
        cursor: pointer;
        &.primary {
          background-color: $blue;
        }
        &.danger {
          background-color: #ec4545;
        }
        &:hover {
          opacity: 0.8;
        }
      }
      .title-btn + .title-btn {
        margin-left: 10px;
      }
    }
    .custom-table {
      border: 0;
      border-radius: 0;
      .el-form-item {
        margin-top: 10px !important;
        margin-bottom: 10px !important;
        .el-form-item__error {
          top: 95%;
          padding-top: 0;
        }
      }
    }
  }
  .offer-title {
    font-size: 14px;
    color: $info;
    padding: 18px 0;
    span {
      margin-right: 120px;
    }
  }
  .offer-account {
    display: flex;
    justify-content: space-between;
    align-items: center;
    line-height: 48px;
    padding: 0 20px;
    background-color: #fff5f5;
    border: 1px solid #ec4545;
    border-radius: 5px;
    margin-bottom: 10px;
    font-size: 14px;
    color: $info;
    span {
      margin-right: 30px;
    }
    b {
      font-size: 18px;
      font-weight: 500;
      color: #f35d09;
      margin: 0 10px;
    }
  }
}
.contract-dialog ::v-deep {
  .el-dialog__body {
    padding: 0 !important;
  }
  .contractHtml {
    padding: 0 20px;
    overflow: hidden;
  }
  .contractBox {
    padding: 20px 20px 10px;
    border: 1px solid #cbd6e2;
    border-radius: 5px;
    background-color: $white;
    margin-bottom: 10px;
    .custom-table {
      border-bottom-left-radius: 0;
      border-bottom-right-radius: 0;
      border-bottom: 0;
      .el-form-item {
        margin-top: 10px !important;
        margin-bottom: 10px !important;
        .el-form-item__error {
          top: 95%;
          padding-top: 0;
        }
      }
      .danger {
        border-color: #ec4545;
        color: #ec4545;
        &:hover {
          background-color: #ec4545;
        }
      }
    }
    &-title {
      font-size: 20px;
      font-weight: 500;
      color: $info;
      text-align: center;
      margin: 10px 0 30px;
    }
    &-desc {
      display: flex;
      justify-content: space-between;
      padding: 0 25px;
      margin-bottom: 5px;
      .desc-right {
        min-width: 320px;
      }
      .desc-item {
        width: 320px;
        font-size: 14px;
        line-height: 28px;
        .el-input__inner {
          text-align: left;
          font-size: 12px;
        }
        span {
          color: $info;
          text-align: right;
          &.el-input__prefix {
            width: auto;
          }
        }
        b {
          color: $font;
          font-weight: 500;
        }
        & + .desc-item {
          margin-left: 30px;
        }
      }
    }
    &-htitle {
      margin: 10px 0;
      font-size: 16px;
      span {
        color: $info;
      }
      b {
        font-weight: 500;
        color: $font;
      }
    }
    &-total {
      display: flex;
      flex-direction: column;
      border: 1px solid #ec4545;
      background: #fff5f5;
      height: 96px;
      line-height: 48px;
      padding: 0 20px;
      border-bottom-left-radius: 5px;
      border-bottom-right-radius: 5px;
      .total-box {
        display: inline-flex;
        justify-content: space-between;
        align-items: center;
      }
      .total-item {
        span {
          font-size: 14px;
          color: $info;
        }
        b {
          font-size: 18px;
          font-weight: 500;
          color: $orange;
        }
      }
      .total-tip {
        font-size: 12px;
        color: $orange;
      }
    }
    &-tableBox {
      width: 100%;
      margin: 15px 0;
      ul {
        width: 100%;
        list-style: none;
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        padding: 0;
        margin: 0;
        border: 1px solid #cbd6e2;
        border-radius: 5px;
        border-bottom-width: 0;
        overflow: hidden;
      }
      li {
        width: 50%;
        line-height: 20px;
        padding: 15px 20px;
        border-right: 1px solid #cbd6e2;
        border-bottom: 1px solid #cbd6e2;
        font-size: 12px;
        &:nth-child(2n) {
          border-right-width: 0;
        }
        &:nth-child(4n + 1),
        &:nth-child(4n + 2) {
          background-color: #f8f9fb;
        }
        span {
          color: $disabled;
          margin-right: 20px;
        }
        b {
          color: $font;
          font-weight: 500;
        }
      }
      .contractBox-input {
        .el-input__inner {
          text-align: left;
          font-size: 12px;
        }
      }
    }
    &-input {
      .el-input__inner {
        border-width: 0;
        border-bottom-width: 1px;
        border-radius: 0;
        background-color: transparent;
        text-align: center;
        font-size: 14px;
        color: $font;
      }
      &.input-date {
        .el-input__inner {
          text-align: left;
        }
      }
      &.input-left {
        .el-input__inner {
          text-align: left;
        }
      }
    }
  }
}
</style>
