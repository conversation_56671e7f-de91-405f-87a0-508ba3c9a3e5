<template>
  <div>
    <!-- 供应商信息 -->
    <el-dialog v-dialogDragBox :title="info.name" :visible.sync="open" width="1150px" class="custom-dialog" :append-to-body="isLook">
      <div slot="title">
        <span style="color: #2e73f3" v-if="supplierType == 'common'">(公域)</span>
        <span style="color: #fe7f22" v-if="supplierType == 'private'">(私域)</span>
        <span style="color: #666666">{{ info.name }}</span>
      </div>
      <div class="supplerBox">
        <el-descriptions :column="3" border label-class-name="supplerBox-label" content-class-name="supplerBox-content">
          <el-descriptions-item label="公司名称">
            <span style="color: #2e73f3" v-if="supplierType == 'common'">(公域)</span>
            <span style="color: #fe7f22" v-if="supplierType == 'private'">(私域)</span>
            <span class="supplerBox-label-link" @click="handleCompantView(info)">{{ info.name }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="公司地址" :span="2">{{ removeHtmlTag(info.address, 300) }}</el-descriptions-item>
          <template v-for="(item, index) in info.contactList">
            <el-descriptions-item :label="`第${index + 1}联系人`" :key="'n' + index">{{ item.nickName }}</el-descriptions-item>
            <el-descriptions-item label="职务" :key="'p' + index">{{ item.post }}</el-descriptions-item>
            <el-descriptions-item label="联系方式" :key="'m' + index">{{ item.phone }}</el-descriptions-item>
          </template>
          <el-descriptions-item label="公司logo">
            <el-image :src="imgPath + info.logoUrl" fit="cover" @click="handleImgView(imgPath + info.logoUrl)">
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline"></i>
              </div>
            </el-image>
          </el-descriptions-item>
          <el-descriptions-item label="资质文件">
            <el-image :src="imgPath + info.certificationUrl" fit="cover" @click="handleImgView(imgPath + info.certificationUrl)">
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline"></i>
              </div>
            </el-image>
          </el-descriptions-item>
        </el-descriptions>
        <template v-if="info.hasOwnProperty('productList') && info.productList.length && supplierType == 'private'">
          <div class="supplierProductTitle">私域产品</div>
          <div class="supplierProduct">
            <div class="supplierProductItem" v-for="item in info.productList" :key="item.id" @click="handleProductInfo(item)">{{ item.productName }}</div>
          </div>
        </template>
      </div>
      <div slot="footer">
        <button type="button" class="custom-dialog-btn primary" @click="open = false">关 闭</button>
      </div>
    </el-dialog>

    <!-- 图片预览 -->
    <el-dialog v-dialogDragBox :visible.sync="imgOpen" width="750px">
      <div style="text-align: center"><img style="max-width: 100%" :src="imgUrl" /></div>
    </el-dialog>

    <!-- 产品详情 -->
    <product-dialog ref="productInfo"></product-dialog>
    <!--企业详情-->
    <company-view ref="companyView"></company-view>
  </div>
</template>

<script>
import { privateSupb } from '@/api/houtai/siyu/gongying'
import { supplier } from '@/api/system/user'
import ProductDialog from '@/views/public/product/dialog'
import CompanyView from './supplierView'
import { removeHtmlTag } from '@/utils'

export default {
  components: { CompanyView, ProductDialog },
  props: {
    isLook: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      open: false,
      info: {},
      imgOpen: false,
      imgUrl: undefined,
      supplierType: ''
    }
  },
  methods: {
    removeHtmlTag,
    // 查看供应商详情
    getInfo(id, type) {
      this.supplierType = type
      if (type === 'common') {
        supplier({ id }).then(res => {
          if (res.code === 200) {
            let data = { ...res.data.company, ...res.data.supplier }
            data.contactList = res.data.contacts
            this.info = data
            this.open = true
          } else {
            this.$message.error(res.msg)
          }
        })
      } else {
        privateSupb({ id }).then(res => {
          if (res.code === 200) {
            this.info = res.data
            this.open = true
          } else {
            this.$message.error(res.msg)
          }
        })
      }
    },
    // 图片预览
    handleImgView(url) {
      this.imgUrl = url
      this.imgOpen = true
    },
    // 产品详情
    handleProductInfo(row, type) {
      this.$refs.productInfo.handleView(row, type)
    },
    // 企业详情
    handleCompantView(info) {
      const { name } = info
      this.$refs.companyView.getDetail(name)
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
.custom-dialog ::v-deep {
  .supplerBox {
    padding: 0 20px;
    .el-image {
      width: 52px;
      height: 52px;
      overflow: hidden;
      border: 1px solid #ededed;
      border-radius: 5px;
      display: flex;
      justify-content: center;
      .el-image__inner {
        transition: all 0.3s;
        cursor: pointer;
        &:hover {
          transform: scale(1.2);
        }
      }
      .image-slot {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: 100%;
        color: #909399;
        font-size: 30px;
      }
    }
    &-label {
      width: 100px;
      &-link {
        cursor: pointer;
        color: #2e73f3;
        &:hover {
          text-decoration: underline;
        }
      }
    }
    &-content {
      width: calc(1140px / 3 - 102px);
    }
  }
  .supplierProduct {
    display: flex;
    flex-wrap: wrap;
    &Title {
      width: 100%;
      font-size: 16px;
      margin-top: 10px;
      line-height: 2;
    }
    &Item {
      display: inline-block;
      padding: 5px 10px;
      color: $blue;
      background-color: #f1f6ff;
      border: 1px solid $blue;
      border-radius: 5px;
      margin: 5px;
      position: relative;
      cursor: pointer;
      transition: all 0.3s;
      &:hover {
        background-color: $blue;
        color: #ffffff;
      }
    }
  }
}
</style>
