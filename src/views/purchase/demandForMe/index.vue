<template>
  <div class="newBox bgcf9 vh-85">
    <!-- 搜索 -->
    <div class="custom-search flex" style="padding-top: 18px">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
        <el-form-item label="需求名称" prop="title">
          <el-input v-model="queryParams.title" placeholder="请输入需求名称" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="发布状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="请选择发布状态" clearable>
            <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="有效状态" prop="filterExpire">
          <el-select v-model="queryParams.filterExpire" placeholder="请选择有效状态" clearable>
            <el-option v-for="item in filterExpireOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="制单人" prop="createBy" v-hasPermi="['purchasing:demand:all']">
          <el-input v-model="queryParams.createBy" placeholder="请输入制单人" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" style="background-color: #2e73f3; border-color: #2e73f3" size="small" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
          <el-button type="primary" icon="el-icon-plus" size="small" @click="handleAdd" v-hasPermi="['purchasing:demand:addpurchasing']">新增需求</el-button>
          <el-button icon="el-icon-s-order" size="small" @click="handleContractTpl" type="success">合同模板</el-button>
          <el-button icon="el-icon-s-tools" size="small" @click="handleUocConfig" type="warning" v-hasPermi="['uoc:config']">最少询几家</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 列表 -->
    <div class="Box">
      <template v-if="total > 0">
        <el-table v-loading="loading" ref="allTable" stripe :data="list" row-key="id" :key="key" style="width: 100%" class="custom-table">
          <el-table-column type="expand">
            <template slot-scope="{ row }">
              <div class="table-expand">
                <el-table :ref="`products${row.id}`" stripe :data="row.products" row-key="id" :key="`products${row.id}`" style="width: 100%" class="custom-table">
                  <el-table-column align="center" type="index" label="序号"></el-table-column>
                  <el-table-column align="center" prop="productName" label="产品名称" show-overflow-tooltip min-width="130">
                    <template slot-scope="{ row }">
                      <span class="table-link" @click="handleViewProduct(row.productId, row)">
                        <span v-if="row.source === 'common'">(公域)</span>
                        <span style="color: #fe7f22" v-else>(私域)</span>
                        {{ row.productName }}
                      </span>
                    </template>
                  </el-table-column>
                  <el-table-column align="center" prop="picture1" label="图片" width="75">
                    <template slot-scope="{ row }">
                      <el-image :src="formatProductImg(row)" fit="cover" @click="handleImgView(row)">
                        <div slot="error" class="image-slot">
                          <i class="el-icon-picture-outline"></i>
                        </div>
                      </el-image>
                    </template>
                  </el-table-column>
                  <el-table-column align="center" prop="specs" label="规格" show-overflow-tooltip></el-table-column>
                  <el-table-column align="center" prop="productCode" label="产品编码" show-overflow-tooltip></el-table-column>
                  <el-table-column align="center" prop="materialQuality" label="材质" show-overflow-tooltip></el-table-column>
                  <el-table-column align="center" prop="surface" label="表面处理" show-overflow-tooltip></el-table-column>
                  <el-table-column align="center" prop="unit" label="单位" show-overflow-tooltip></el-table-column>
                  <el-table-column align="center" prop="industry" label="行业分类" show-overflow-tooltip></el-table-column>
                  <el-table-column align="center" prop="weight" label="重量" show-overflow-tooltip></el-table-column>
                  <el-table-column align="center" prop="quantity" label="计划采购数量" show-overflow-tooltip></el-table-column>
                  <el-table-column align="center" prop="needQuantity" label="实际采购数量" show-overflow-tooltip></el-table-column>
                  <el-table-column align="center" prop="remark" label="备注" show-overflow-tooltip></el-table-column>
                </el-table>
              </div>
            </template>
          </el-table-column>
          <el-table-column align="center" type="index" label="序号"></el-table-column>
          <el-table-column align="center" prop="title" label="标题" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="supplyEndTime" label="供货截止时间" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="expireTime" label="剩余有效时间" show-overflow-tooltip>
            <template slot-scope="{ row }">
              <Countdown :expireTime="row.expireTime" pattern="{d}{h}{m}" />
            </template>
          </el-table-column>
          <el-table-column align="center" prop="remark" label="备注" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="status" label="状态" show-overflow-tooltip>
            <template slot-scope="{ row }">
              <span :style="{ color: row.status ? '#999999' : '#f35d09' }">{{ row.status ? '已发布' : '未发布' }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="docUser" label="采购制单人" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="createTime" label="创建时间" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="sendCount" label="发布次数" show-overflow-tooltip>
            <template slot-scope="{ row }">{{ `已发布${row.sendCount}次` }}</template>
          </el-table-column>
          <el-table-column align="center" label="操作" :width="getActionColumnWidth()" v-if="checkPermi(['purchasing:demand:detail', 'purchasing:demand:send', 'purchasing:product:reply', 'purchasing:demand:del'])">
            <template slot-scope="{ row }">
              <!-- 常用按钮 -->
              <template v-for="action in getCommonActions(row)">
                <el-badge :is-dot="true" class="item custom-badge" v-if="action.key === 'offer' && action.badge" :key="action.key">
                  <button type="button" :class="action.className" :disabled="action.disabled" @click="action.handler(row)" v-hasPermi="action.permission" v-if="action.permission && action.permission.length > 0">{{ action.label }}</button>
                  <button v-else type="button" :class="action.className" :disabled="action.disabled" @click="action.handler(row)">{{ action.label }}</button>
                </el-badge>
                <button v-else-if="action.permission && action.permission.length > 0" :key="action.key" type="button" :class="action.className" :disabled="action.disabled" @click="action.handler(row)" v-hasPermi="action.permission">{{ action.label }}</button>
                <button v-else :key="action.key" type="button" :class="action.className" :disabled="action.disabled" @click="action.handler(row)">{{ action.label }}</button>
              </template>
              <!-- 更多操作 -->
              <el-popover trigger="hover" v-if="hasMoreActions(row)">
                <button type="button" class="table-btn primary" slot="reference">
                  更多操作
                  <i class="el-icon-arrow-down el-icon--right"></i>
                </button>
                <div class="popover-button">
                  <template v-for="action in getAllActions(row)">
                    <div :key="action.key" class="popover-button-item" v-if="action.show && checkActionPermission(action)">
                      <template v-if="action.key === 'offer' && action.badge">
                        <el-badge :is-dot="true" class="item custom-badge">
                          <el-button :class="action.className" :disabled="action.disabled" @click="action.handler(row)" v-hasPermi="action.permission" v-if="action.permission && action.permission.length > 0">{{ action.label }}</el-button>
                          <el-button v-else :class="action.className" :disabled="action.disabled" @click="action.handler(row)">{{ action.label }}</el-button>
                        </el-badge>
                      </template>
                      <el-button v-else-if="action.permission && action.permission.length > 0" :class="action.className" :disabled="action.disabled" @click="action.handler(row)" v-hasPermi="action.permission">{{ action.label }}</el-button>
                      <el-button v-else :class="action.className" :disabled="action.disabled" @click="action.handler(row)">{{ action.label }}</el-button>
                      <i class="popover-button-icon" :class="[isCommonAction(action.key) ? 'el-icon-star-on' : 'el-icon-star-off', { active: isCommonAction(action.key) }]" @click="toggleCommonAction(action.key)" :title="isCommonAction(action.key) ? '取消常用' : '设置为常用'"></i>
                    </div>
                  </template>
                </div>
              </el-popover>
            </template>
          </el-table-column>
        </el-table>

        <div class="custom-pagination">
          <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" local="DemandPageSize" @pagination="getList" />
        </div>
      </template>
      <el-empty :description="!loading && !total ? '暂无数据' : '加载中…'" v-else />
    </div>

    <!-- 发布需求 -->
    <el-dialog v-dialogDragBox :visible.sync="publishOpen" width="1150px" class="custom-dialog">
      <template slot="title">
        <div class="flex align-center">
          <div class="dialog_title">发布需求</div>
          <div class="dialog_switch">
            <div class="text">同步至求购专区：</div>
            <el-switch class="switch" v-model="buyType"></el-switch>
          </div>
        </div>
      </template>
      <el-form ref="publishForm" :model="publishForm" :rules="publishRules" label-position="left">
        <div class="publish-new">
          <div class="publish-new-title">
            <div class="title-item" :class="{ active: publishForm.publishWay === 'private' }" @click="handleChangeTab('private')">私域发布</div>
            <div class="title-item" :class="{ active: publishForm.publishWay === 'common' }" @click="handleChangeTab('common')">公域发布</div>
          </div>
          <div class="publish-desc">
            <div class="desc-item">
              <span>标题</span>
              <b>{{ publishForm.title }}</b>
            </div>
            <div class="desc-item">
              <span>有效时间</span>
              <b style="display: inline-block"><Countdown :expireTime="publishForm.expireTime" pattern="{d}{h}{m}" /></b>
            </div>
            <div class="desc-item">
              <span>创建时间</span>
              <b>{{ publishForm.createTime }}</b>
            </div>
            <div class="desc-item">
              <span>供货截止</span>
              <b>{{ publishForm.supplyEndTime }}</b>
            </div>
            <div class="desc-item">
              <span>备注</span>
              <b>{{ publishForm.remark }}</b>
            </div>
          </div>
        </div>
        <div class="publish-box">
          <div class="publish-table-title">
            <span>产品列表</span>
            <button type="button"><i class="el-icon-plus" @click="handleOpenAddProduct">新增产品</i></button>
          </div>
          <el-table ref="publishTable" :max-height="addHeight" stripe :data="publishForm.products" row-key="id" :key="publishKey" style="width: 100%" class="custom-table custom-table-cell0" :row-class-name="rowStyle">
            <el-table-column align="center" type="index" label="序号"></el-table-column>
            <el-table-column align="center" prop="product_name" label="产品名称" show-overflow-tooltip>
              <template slot-scope="{ row }">
                <span class="table-link" @click="handleViewProduct(row.productId, row)" v-if="row.productId">
                  <span v-if="row.source === 'common'">(公域)</span>
                  <span style="color: #fe7f22" v-else>(私域)</span>
                  {{ row.productName }}
                </span>
                <span class="table-link" v-else>
                  <span style="color: #fe7f22">(私域)</span>
                  {{ row.productName }}
                </span>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="specs" label="产品规格" show-overflow-tooltip></el-table-column>
            <el-table-column align="center" prop="model" label="产品型号" show-overflow-tooltip></el-table-column>
            <el-table-column align="center" prop="needQuantity" label="产品数量" width="170">
              <template slot-scope="scope">
                <el-form-item label-width="0" :prop="`products.${scope.$index}.needQuantity`" :rules="publishRules.needQuantity">
                  <el-input v-model="scope.row.needQuantity" :disabled="publishForm.publishWay === 'common' && !scope.row.categoryId" size="small" placeholder="请输入产品数量" />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column align="center" label="操作">
              <template slot-scope="scope">
                <button type="button" class="table-btn danger" @click="handleDeletePublishProduct(scope.$index)">删除</button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="publish-tag" v-show="publishForm.publishWay === 'common'">
          <span class="select">供应商</span>
          <el-tooltip class="item" effect="dark" :content="item.businessScope" :disabled="!item.businessScope" placement="top" v-for="item in commonChecked" :key="item.id">
            <div class="tag-item">
              {{ item.name }}
              <i class="el-icon-close" @click="handleDeleteCommon(item)"></i>
            </div>
          </el-tooltip>
          <button class="tag-btn" type="button">
            <i class="el-icon-plus" @click="handleOpenCommon">{{ commonList.length ? '查看更多' : '选择供应商' }}</i>
          </button>
          <button class="tag-btn danger" type="button" v-if="commonList.length"><i class="el-icon-delete" @click="handleClearCommon">一键清除</i></button>
        </div>
        <div class="publish-tag" v-show="publishForm.publishWay === 'private'">
          <div style="width: 100%; color: #ec2454" v-if="Number(supplierConfig.configValue)">最少需询价{{ Number(supplierConfig.configValue) }}家供应商</div>
          <span class="select">供应商</span>
          <div class="tag-item" v-for="item in supplierChecked" :key="item.id">
            {{ item.name }}
            <i class="el-icon-close" @click="handleDeleteSupplier(item)"></i>
          </div>
          <button class="tag-btn" type="button"><i class="el-icon-plus" @click="handleOpenSupplier">添加供应商</i></button>
          <button class="tag-btn danger" type="button" v-if="supplierChecked.length"><i class="el-icon-delete" @click="handleClearSupplier">一键清除</i></button>
        </div>
      </el-form>
      <div slot="footer">
        <button type="button" class="custom-dialog-btn" @click="publishOpen = false">取消</button>
        <button type="button" class="custom-dialog-btn primary" :class="{ disabled: supplierChecked.length === 0 || supplierChecked.length < Number(supplierConfig.configValue) }" :disabled="supplierChecked.length === 0 || supplierChecked.length < Number(supplierConfig.configValue)" @click="handleSubmitPublish" v-if="publishForm.publishWay === 'private'">立即发布</button>
        <button type="button" class="custom-dialog-btn primary" :class="{ disabled: commonChecked.length === 0 }" :disabled="commonChecked.length === 0" @click="handleSubmitPublish" v-if="publishForm.publishWay === 'common'">立即发布</button>
      </div>
    </el-dialog>

    <!-- 发布添加产品 -->
    <el-dialog v-dialogDragBox title="添加产品" :visible.sync="publishProductOpen" width="1150px" class="custom-dialog">
      <div style="padding: 0 20px">
        <el-table ref="detailTable" stripe :data="publishProducts" row-key="id" style="width: 100%" class="custom-table">
          <el-table-column align="center" type="index" label="序号"></el-table-column>
          <el-table-column align="center" prop="productName" label="产品名称" show-overflow-tooltip>
            <template slot-scope="{ row }">
              <span class="table-link" @click="handleViewProduct(row.productId, row)">
                <span v-if="row.source === 'common'">(公域)</span>
                <span style="color: #fe7f22" v-else>(私域)</span>
                {{ row.productName }}
              </span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="picture1" label="图片" width="75">
            <template slot-scope="{ row }">
              <el-image :src="formatProductImg(row)" fit="cover" @click="handleImgView(row)">
                <div slot="error" class="image-slot">
                  <i class="el-icon-picture-outline"></i>
                </div>
              </el-image>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="specs" label="规格" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="productCode" label="产品编码" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="materialQuality" label="材质" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="surface" label="表面处理" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="unit" label="单位" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="industry" label="行业分类" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" label="操作" width="110">
            <template slot-scope="{ row }">
              <button type="button" class="table-btn primary" :class="{ disabled: row.disabled }" :disabled="row.disabled" @click="handleAddPublishProduct(row)">添加</button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div slot="footer">
        <button type="button" class="custom-dialog-btn primary" @click="publishProductOpen = false">关闭</button>
      </div>
    </el-dialog>

    <!-- 添加供应商 -->
    <el-dialog v-dialogDragBox title="添加供应商" :visible.sync="supplierOpen" width="1150px" class="custom-dialog">
      <div style="padding: 0 20px">
        <div class="custom-search-form flex" style="margin-top: -10px; margin-bottom: 10px">
          <input type="text" v-model="supplierQuery.keyword" placeholder="请输入供应商名称或地址或联系人" class="custom-search-input" @keyup.enter="handleSupplierQuery" style="width: 250px" />
          <button type="button" class="custom-search-button pointer" @click="handleSupplierQuery">
            <i class="el-icon-search"></i>
            搜索
          </button>
        </div>
        <el-table ref="supplierTable" stripe :data="SupplierList" row-key="id" style="width: 100%" class="custom-table custom-expanded" @selection-change="handleSupplierSelection" @select="handleSupplierSelections" default-expand-all :row-class-name="getRowClass">
          <el-table-column align="center" type="selection" width="50" reserve-selection></el-table-column>
          <el-table-column type="expand" width="30">
            <template slot-scope="{ row }">
              <div class="table-expand" v-show="row.contactList.length > 1">
                <el-table :ref="`contactList${row.id}`" :data="row.contactList.filter(item => !item.invalid)" :key="`list${key}`" style="width: 100%" class="custom-table">
                  <el-table-column align="center" label="" width="50">
                    <template slot-scope="scope">
                      <el-checkbox v-model="scope.row.ischecked" @change="handleChangeCheck($event, row, scope.row)">
                        <span />
                      </el-checkbox>
                    </template>
                  </el-table-column>
                  <el-table-column align="center" type="index" label="序号"></el-table-column>
                  <el-table-column align="center" prop="nickName" label="联系人"></el-table-column>
                  <el-table-column align="center" prop="phone" label="电话"></el-table-column>
                  <el-table-column align="center" prop="post" label="职务"></el-table-column>
                  <el-table-column align="center" label="站内通知">
                    <template slot-scope="{ row }">
                      <el-switch class="isSite" :disabled="!row.userId" @change="handleSite(row)" v-model="row.isSite" active-text="开" inactive-text="关"></el-switch>
                    </template>
                  </el-table-column>
                  <el-table-column align="center" label="注册公司名称" show-overflow-tooltip>
                    <template slot-scope="{ row }">{{ row.companyName }}</template>
                  </el-table-column>
                  <el-table-column align="center" label="注册昵称" show-overflow-tooltip>
                    <template slot-scope="{ row }">{{ row.userName }}</template>
                  </el-table-column>
                </el-table>
              </div>
            </template>
          </el-table-column>
          <el-table-column align="center" type="index" label="序号"></el-table-column>
          <el-table-column align="center" prop="name" label="供应商名称" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" label="是否已发布过" show-overflow-tooltip width="120">
            <template slot-scope="{ row }">
              <span :class="row.isPublish ? 'color-red' : 'color-blue'">{{ row.isPublish ? '已发过' : '未发过' }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="联系人" show-overflow-tooltip width="120">
            <template slot-scope="{ row }">
              {{ returnContactList(row, 'nickName') }}
            </template>
          </el-table-column>
          <el-table-column align="center" label="电话" show-overflow-tooltip width="120">
            <template slot-scope="{ row }">
              {{ returnContactList(row, 'phone') }}
            </template>
          </el-table-column>
          <el-table-column align="center" label="职务" show-overflow-tooltip width="120">
            <template slot-scope="{ row }">
              {{ returnContactList(row, 'post') }}
            </template>
          </el-table-column>
          <el-table-column align="center" label="站内通知">
            <template slot-scope="{ row }">
              <template v-if="row.contactList.length === 1">
                <el-switch class="isSite" :disabled="!returnContactList(row, 'userId')" @change="handleSite(row)" v-model="row.isSite" active-text="开" inactive-text="关"></el-switch>
              </template>
            </template>
          </el-table-column>
          <el-table-column align="center" label="注册公司名称" show-overflow-tooltip>
            <template slot-scope="{ row }">
              {{ row.contactList.length === 1 ? returnContactList(row, 'companyName') : '' }}
            </template>
          </el-table-column>
          <el-table-column align="center" label="注册昵称" show-overflow-tooltip>
            <template slot-scope="{ row }">
              {{ row.contactList.length === 1 ? returnContactList(row, 'userName') : '' }}
            </template>
          </el-table-column>
          <el-table-column align="center" prop="address" label="地址" show-overflow-tooltip>
            <template slot-scope="{ row }">{{ removeHtmlTag(row.address, 300) }}</template>
          </el-table-column>
        </el-table>
        <div class="custom-pagination">
          <pagination v-show="supplierTotal > 0" :total="supplierTotal" :page.sync="supplierQuery.pageNum" :limit.sync="supplierQuery.pageSize" @pagination="handleGetSupplier" />
        </div>
      </div>
      <div slot="footer">
        <el-badge>
          <button type="button" class="custom-dialog-btn" @click="supplierOpen = false">取消</button>
        </el-badge>
        <el-badge :value="supplierCheck.length">
          <button type="button" class="custom-dialog-btn primary" @click="handleSubmitSupplier">确定</button>
        </el-badge>
      </div>
    </el-dialog>

    <!--  添加公域供应商  -->
    <el-dialog v-dialogDragBox title="更多供应商" :visible.sync="commonOpen" width="1150px" class="custom-dialog">
      <template v-if="!!commonChecked.length">
        <div class="hasChecked-title">已选择供应商</div>
        <div class="hasChecked-info">
          <el-tooltip class="item" effect="dark" :content="item.businessScope" :disabled="!item.businessScope" placement="top" v-for="item in commonChecked" :key="item.id">
            <div class="hasChecked-item">
              {{ item.name }}
              <i class="el-icon-close" @click="handleDeleteCommon(item)"></i>
            </div>
          </el-tooltip>
        </div>
      </template>
      <div style="padding: 0 20px">
        <div class="hasChecked-search">
          <el-form ref="commonQuery" size="small" :inline="true">
            <el-form-item label="供应商名称" prop="commonName">
              <el-input v-model="commonName" placeholder="请输入供应商名称" clearable @keyup.enter.native="handleNameQuery" />
            </el-form-item>
            <el-form-item label="经营产品" prop="businessScope">
              <el-input v-model="businessScope" placeholder="请输入供应商经营产品,多个用,号分开" clearable @keyup.enter.native="handleNameQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" style="background-color: #2e73f3; border-color: #2e73f3" size="small" @click="handleNameQuery">搜索</el-button>
            </el-form-item>
          </el-form>
        </div>
        <el-table ref="commonTable" stripe :data="commonList.slice((commonPage - 1) * commonSize, commonPage * commonSize)" row-key="id" style="width: 100%" class="custom-table custom-expanded" @selection-change="handleCommonSelection" v-if="commonList.length">
          <el-table-column align="center" type="selection" :selectable="checkStatus" width="50" reserve-selection></el-table-column>
          <el-table-column align="center" type="index" label="序号"></el-table-column>
          <el-table-column align="center" prop="name" label="供应商名称" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" label="是否已发布过" show-overflow-tooltip width="120">
            <template slot-scope="{ row }">
              <span :class="row.isPublish ? 'color-red' : 'color-blue'">{{ row.isPublish ? '已发过' : '未发过' }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="businessScope" label="经营产品" show-overflow-tooltip></el-table-column>
        </el-table>
        <el-empty v-if="!commonList.length" description="暂无可供应的供应商，请修改搜索关键词再重新尝试" />
        <div class="custom-pagination" style="margin-top: 20px">
          <el-pagination align="center" @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="commonPage" :page-sizes="[10, 20, 30, 50, 100]" :page-size="commonSize" layout="total, sizes, prev, pager, next, jumper" :total="commonList.length"></el-pagination>
        </div>
      </div>
      <div slot="footer">
        <el-badge>
          <button type="button" class="custom-dialog-btn" @click="commonOpen = false">取消</button>
        </el-badge>
        <el-badge :value="commonCheck.length">
          <button type="button" class="custom-dialog-btn primary" @click="handleSubmitCommon">确定</button>
        </el-badge>
      </div>
    </el-dialog>

    <!-- 产品详情 -->
    <product-dialog ref="productInfo"></product-dialog>

    <!-- 查看报价 -->
    <offer-dialog ref="offerInfo" v-if="checkedList" :dataList="checkedList"></offer-dialog>

    <!--  合同模板  -->
    <contract-tpl ref="contractTpl"></contract-tpl>

    <!--  需求发送记录  -->
    <el-dialog v-dialogDragBox title="需求发送记录" :visible.sync="recordOpen" width="1150px" class="custom-dialog">
      <div style="padding: 0 20px">
        <el-table ref="approvalRecordsTable" stripe :data="recordList" row-key="id" style="width: 100%" class="custom-table custom-table-cell10">
          <el-table-column align="center" type="index" label="序号"></el-table-column>
          <el-table-column align="center" prop="companyName" label="供应商名称" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="send_user" label="联系人" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="send_phone" label="联系电话" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="time" label="发送时间" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" label="发布方式" show-overflow-tooltip>
            <template slot-scope="{ row }">{{ row.publish_way == 'private' ? '私域发布' : '公域发布' }}</template>
          </el-table-column>
          <el-table-column align="center" prop="status" :formatter="recordStatus" label="发送状态" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="send_times" label="发送次数" show-overflow-tooltip>
            <template slot-scope="{ row }">{{ row.send_times || 0 }}次</template>
          </el-table-column>
          <el-table-column align="center" prop="reply" :formatter="replyStatus" label="回复状态" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" label="操作" width="180">
            <template slot-scope="{ row }">
              <el-button type="text" icon="el-icon-s-order" size="small" @click="handleViewDetails(row)">需求明细</el-button>
              <el-button type="text" icon="el-icon-refresh" size="small" @click="handleResend(row)">重新发送</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div slot="footer">
        <button type="button" class="custom-dialog-btn primary" @click="recordOpen = false">关闭</button>
      </div>
    </el-dialog>

    <!--  发送记录明细  -->
    <el-dialog v-dialogDragBox title="需求明细" :visible.sync="viewDetailsOpen" width="1150px" class="custom-dialog">
      <div style="padding: 0 20px">
        <el-table ref="detailTable" stripe :data="viewDetailsList" row-key="id" style="width: 100%" class="custom-table">
          <el-table-column align="center" type="index" label="序号"></el-table-column>
          <el-table-column align="center" prop="productName" label="产品名称" show-overflow-tooltip>
            <template slot-scope="{ row }">
              <span class="table-link" @click="handleViewProduct(row.productId, row)">
                <span v-if="row.source === 'common'">(公域)</span>
                <span style="color: #fe7f22" v-else>(私域)</span>
                {{ row.productName }}
              </span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="采购数量" show-overflow-tooltip>
            <template slot-scope="{ row }">{{ row.quantity + row.unit }}</template>
          </el-table-column>
          <el-table-column align="center" prop="isReply" label="回复状态" show-overflow-tooltip>
            <template slot-scope="{ row }">{{ row.isReply ? '已回复' : '未回复' }}</template>
          </el-table-column>
        </el-table>
      </div>
      <div slot="footer">
        <button type="button" class="custom-dialog-btn primary" @click="viewDetailsOpen = false">关闭</button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { purchasingDemandDelete, purchasingDemandDetail, purchasingDemandList, purchasingDemandPublish, purchasingDemandReply, purchasingDemandSendRecord, purchasingDemandSupplier, purchasingProductWord, purchasingProductWordSupplier, purchasingPublishSupplier } from '@/api/purchase'
import * as supplier from '@/api/houtai/siyu/gongying'
import { expireTimeFormat, removeHtmlTag } from '@/utils'
import ProductDialog from '@/views/public/product/dialog'
import { getProduct } from '@/api/system/product'
import { addBuy } from '@/api/buy'
import { getPrivateduct } from '@/api/system/privateduct'
import OfferDialog from './offer'
import { checkPermi } from '@/utils/permission'
import contractTpl from '@/views/purchase/contract/template.vue'
import { getConfigDetail, updateConfig } from '@/api/config'
import { getDemandRecordList, getMsgDetail, reSendDemandMsg } from '@/api/record'
import * as company from '@/api/system/company'

export default {
  name: 'List',
  components: { contractTpl, ProductDialog, OfferDialog },
  data() {
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        title: undefined,
        status: undefined,
        filterExpire: true,
        createBy: undefined
      },
      statusOptions: [
        { label: '待发布', value: 0 },
        { label: '已发布', value: 1 }
      ],
      filterExpireOptions: [
        { label: '未过期', value: true },
        { label: '已过期', value: false }
      ],
      total: 0,
      loading: true,
      list: [],
      key: 1,
      // 发布
      publishKey: 1,
      publishOpen: false,
      publishForm: { products: [] },
      publishRules: {
        needQuantity: [{ required: true, message: '请输入产品数量', trigger: 'blur' }]
      },
      publishProducts: [],
      publishProductOpen: false,
      supplierChecked: [],
      supplierCheck: [],
      supplierOpen: false,
      SupplierList: [],
      supplierQuery: {
        pageNum: 1,
        pageSize: 10,
        status: 1,
        keyword: undefined
      },
      supplierTotal: 0,
      checkedList: [],
      // 公域供应商
      commonList: [],
      commonAllList: [],
      commonOpen: false,
      commonChecked: [],
      commonCheck: [],
      commonQuery: {
        demandId: undefined,
        name: undefined,
        scope: undefined
      },
      windowHeight: undefined,
      commonHeight: undefined,
      addHeight: undefined,
      commonPage: 1,
      commonSize: 10,
      commonName: undefined,
      businessScope: undefined,
      supplierConfig: {},
      // 需求发送记录
      recordOpen: false,
      recordList: [],
      viewDetailsOpen: false,
      viewDetailsList: [],
      // 同步求购专区
      buyType: true,
      isFirstVisit: true,
      // 常用按钮配置
      commonActions: []
    }
  },
  activated() {
    if (this.isFirstVisit) {
      this.isFirstVisit = false
      return
    }
    this.getList()
  },
  created() {
    this.getList()
    this.getConfig()
    // 加载常用按钮配置
    const commonActions = localStorage.getItem(this.userId + '.demandCommonActions')
    if (commonActions) this.commonActions = JSON.parse(commonActions)
  },
  watch: {
    windowHeight(val) {
      this.commonHeight = val * 0.94 - 200
      this.addHeight = val * 0.94 - 400
    }
  },
  mounted() {
    this.windowHeight = document.documentElement.clientHeight
    window.onresize = () => {
      this.windowHeight = document.documentElement.clientHeight
    }
  },
  beforeDestroy() {
    window.onresize = null
  },
  computed: {
    // 获取当前用户ID
    userId() {
      return this.$store.getters.info.userId
    }
  },
  methods: {
    removeHtmlTag,
    checkPermi,
    expireTimeFormat,
    getConfig() {
      getConfigDetail({ configKey: 'supplier.min.limit' }).then(res => {
        this.supplierConfig = res.data
      })
    },
    // 列表
    getList() {
      this.loading = true
      const localPageSize = localStorage.getItem('DemandPageSize')
      if (localPageSize) this.queryParams.pageSize = parseInt(localPageSize)
      purchasingDemandList(this.queryParams).then(async res => {
        if (res.code === 200) {
          const arr = res.rows
          await Promise.all(
            arr.map(async row => {
              row.replyNum = 0
              const reply = await purchasingDemandReply({ demandId: row.id })
              let num = 0
              // reply.data.map(item => {
              // 	num += item.reply.length
              // })
              num += reply.data.length
              row.replyNum = num
              row.products = []
              const result = await purchasingDemandDetail({ demandId: row.id })
              const { code, msg, data } = result
              if (code === 200) {
                row.products = data.hasOwnProperty('products') && data.products.filter(item => item.productId)
                row.sendCount = data.sendCount
              } else this.$message.error(msg)
            })
          )
          this.list = res.rows
          this.total = res.total
          this.loading = false
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 重置搜索
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 新增需求
    handleAdd() {
      this.$router.push('/demand/order')
    },
    // 产品详情
    handleViewProduct(Id, item) {
      if (item.source === 'common') {
        getProduct(Id).then(res => {
          this.$refs.productInfo.handleView(res.data)
        })
      } else {
        getPrivateduct(Id).then(res => {
          this.$refs.productInfo.handleView(res.data)
        })
      }
    },
    handleImgView(row) {
      this.$refs.productInfo.handleImgView(row)
    },
    // 查看详情
    handleView(row) {
      const Id = row.id
      this.$router.push('/demand/detail/' + Id)
    },
    // 查看报价
    handleOffer(row) {
      const demandId = row.id
      purchasingDemandDetail({ demandId }).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          // if (res.data.hasOwnProperty('products') && res.data.products) {
          //   const newArr = res.data.products.filter(item => item.productId)
          //   this.$refs.offerInfo.handleOffer(newArr, row.docUser, row.id)
          // }
          if (data.products) {
            purchasingDemandReply({
              demandId
            }).then(response => {
              if (response.code == 200) {
                data.products.map(ite => {
                  ite.offer = []
                  response.data.forEach(el => {
                    if (ite.productId == el.productId) {
                      ite.offer.push(el)
                    }
                  })
                })
                this.$refs.offerInfo.handleOffer(data.products, row.docUser, row.id)
              }
            })
          }
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 删除
    // prettier-ignore
    handleDelete(row) {
      const data = { demandId: row.id }
      this.$modal.confirm('是否确认删除选中的需求？').then(function () {
        return purchasingDemandDelete(data)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess('删除成功')
      }).catch(() => {
      })
    },
    // 发布
    handlePublish(row) {
      this.commonName = undefined
      this.businessScope = undefined
      this.publishForm = {
        demandId: undefined,
        products: [],
        publishWay: 'private',
        sentSupplierIds: undefined,
        suppliers: [],
        expireTime: ''
      }
      this.supplierChecked = []
      this.commonChecked = []
      this.resetForm('publishForm')
      const demandId = row.id
      purchasingDemandDetail({ demandId }).then(res => {
        if (res.code === 200) {
          this.publishForm = res.data
          this.publishProducts = [...res.data.products]
          this.publishForm.publishWay = 'private'
          this.publishKey = 'publish' + Math.random()
          this.handlePublishSupplier(demandId)
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 需求发布产品关联私域供应商
    handlePublishSupplier(demandId) {
      purchasingDemandSupplier({ demandId }).then(res => {
        if (res.code === 200) {
          const supplierList = res.data.filter(item => item.contactList)
          supplierList.map(item => {
            item.contactList.map(ite => {
              ite.checked = ite.checked && !ite.invalid
              ite.ischecked = false
            })
            const checked = item.contactList.filter(ite => ite.checked)
            if (!checked.length) {
              for (let i = 0; i < item.contactList.length; i++) {
                if (!item.contactList[i].invalid) {
                  item.contactList[i].checked = true
                  item.contactList[i].ischecked = true
                  break
                }
              }
            }
          })
          this.supplierChecked = supplierList
          this.publishOpen = true
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 改变公域发布/私域发布
    handleChangeTab(val) {
      this.publishForm.publishWay = val
      this.publishKey = 'publish' + Math.random()
      if (val === 'common') {
        const demandId = this.publishForm.demandId
        purchasingProductWord({ demandId }).then(response => {
          this.commonQuery.demandId = demandId
          this.commonQuery.scope = response.data.toString()
          this.businessScope = response.data.toString()
          this.commonAllList = []
          this.commonList = []
          this.commonChecked = []
          this.handleGetCommon()
        })
      } else {
        this.supplierChecked = []
        this.commonChecked = []
        const demandId = this.publishForm.demandId
        this.handlePublishSupplier(demandId)
      }
    },
    rowStyle({ row, rowIndex }) {
      if (this.publishForm.publishWay === 'common' && !row.categoryId) {
        return 'disabled'
      } else {
        return ''
      }
    },
    // 删除发布中的产品
    handleDeletePublishProduct(index) {
      if (this.publishForm.products.length === 1) {
        this.$message.error('发布需求至少需要有一个产品')
        return
      }
      this.publishForm.products.splice(index, 1)
    },
    // 打开添加产品
    handleOpenAddProduct() {
      this.publishProducts.map(item => {
        item.disabled = false
        this.publishForm.products.map(ite => {
          if (ite.productId === item.productId) item.disabled = true
        })
      })
      this.publishProductOpen = true
    },
    // 添加发布中的产品
    handleAddPublishProduct(row) {
      this.publishForm.products.push(row)
      this.publishProductOpen = false
    },
    // 打开供应商列表
    handleOpenSupplier() {
      this.supplierQuery.keyword = undefined
      this.handleSupplierQuery()
      this.supplierCheck = this.supplierChecked
      this.supplierOpen = true
      if (!this.supplierChecked.length) {
        if (this.$refs.supplierTable) this.$refs.supplierTable.clearSelection()
      }
    },
    // 查询供应商
    async handleGetSupplier() {
      const res = await supplier.getlist(this.supplierQuery)
      if (res.code === 200) {
        if (res.rows) {
          const isPublish = await purchasingPublishSupplier({
            demandId: this.publishForm.demandId,
            publishWay: 'private'
          })
          res.rows.map(item => {
            let index = item.contactList.findIndex(it => it.checked)
            if (index === -1) index = 0
            const conatct = item.contactList[index]
            item.isSite = !!conatct.userId
            item.isPublish = false
            const idx = isPublish.data.findIndex(ite => ite === item.id)
            if (idx !== -1) item.isPublish = true
            item.contactList.map(itt => {
              itt.ischecked = false
              itt.isSite = !!itt.userId
            })
            this.supplierChecked.map(ite => {
              if (ite.id === item.id) {
                ite.contactList.map(itt => {
                  itt.ischecked = itt.checked
                  itt.isSite = !!itt.userId
                })
                item.contactList = ite.contactList
                item.isSite = ite.isSite
              }
            })
          })
        }
        this.SupplierList = res.rows
        this.supplierTotal = res.total
        this.key = Math.random()
        this.echo()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 供应商搜索
    handleSupplierQuery() {
      this.supplierQuery.pageNum = 1
      this.handleGetSupplier()
    },
    // 回显选中
    echo() {
      this.$nextTick(() => {
        this.SupplierList.map(item => {
          this.$refs.supplierTable.toggleRowSelection(item, false)
          this.supplierChecked.map(itt => {
            if (itt.id === item.id) this.$refs.supplierTable.toggleRowSelection(item, true)
          })
        })
      })
    },
    // 选择供应商
    handleSupplierSelections(selection, row) {
      const index = this.supplierChecked.findIndex(item => item.id === row.id)
      if (index !== -1) {
        row.contactList.map(ite => {
          ite.ischecked = false
          ite.checked = false
        })
      } else {
        row.contactList.map(ite => {
          ite.ischecked = !!selection.length
          ite.checked = !!selection.length
        })
      }
    },
    handleSupplierSelection(selection) {
      this.supplierCheck = selection
      this.$nextTick(() => {
        this.supplierChecked = this.supplierCheck
      })
    },
    // 选择联系人
    handleChangeCheck(e, row, child) {
      child.checked = e
      const idx = row.contactList.findIndex(item => item.ischecked)
      if (idx !== -1) this.$refs.supplierTable.toggleRowSelection(row, true)
      else this.$refs.supplierTable.toggleRowSelection(row, false)
    },
    // 返回联系人信息
    returnContactList(row, val) {
      let index = row.contactList.findIndex(item => item.checked)
      if (index === -1) index = 0
      return row.contactList[index][val]
    },
    // 隐藏只有一个联系人的展开
    getRowClass({ row }) {
      if (row.contactList.length > 1) return ''
      else return 'row-expand-cover'
    },
    // 确定选择供应商
    handleSubmitSupplier() {
      this.supplierChecked = this.supplierCheck
      this.supplierOpen = false
    },
    // 删除已选择的供应商
    handleDeleteSupplier(row) {
      const idx = this.supplierChecked.findIndex(item => item.id === row.id)
      if (idx !== -1) this.supplierChecked.splice(idx, 1)
    },
    // 一键清除
    handleClearSupplier() {
      this.supplierChecked = []
    },
    // 一键清除公域供应商
    handleClearCommon() {
      this.commonList = []
      this.commonChecked = []
    },
    // 打开公域供应商列表
    handleOpenCommon() {
      this.commonPage = 1
      this.commonSize = 10
      this.commonOpen = true
      this.commonEcho()
      this.handleGetCommonMore()
    },
    // 查询全部供应商
    async handleGetCommonMore() {
      const demandId = this.publishForm.demandId
      const res = await purchasingProductWordSupplier(this.commonQuery)
      res.data = res.data.filter((item, index, self) => self.findIndex(t => t.id === item.id) === index)
      if (res.code === 200) {
        if (res.data) {
          const isPublish = await purchasingPublishSupplier({ demandId, publishWay: 'common' })
          res.data.map(item => {
            item.isPublish = false
            const idx = isPublish.data.findIndex(ite => ite === item.id)
            if (idx !== -1) item.isPublish = true
          })
        }
        this.commonAllList = res.data
        this.commonList = res.data
        this.key = Math.random()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 查询公域供应商
    async handleGetCommon() {
      const demandId = this.publishForm.demandId
      const res = await purchasingProductWordSupplier(this.commonQuery)
      res.data = res.data.filter((item, index, self) => self.findIndex(t => t.id === item.id) === index)
      if (res.code === 200) {
        if (res.data) {
          const isPublish = await purchasingPublishSupplier({ demandId, publishWay: 'common' })
          res.data.map(item => {
            item.isPublish = false
            const idx = isPublish.data.findIndex(ite => ite === item.id)
            if (idx !== -1) item.isPublish = true
          })
        }
        this.commonAllList = res.data
        this.commonList = res.data
        let arr = [...res.data]
        if (arr.length > 10) this.commonChecked = arr.slice(0, 10)
        else this.commonChecked = arr
        this.key = Math.random()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 供应商搜索
    handleCommonQuery() {
      this.handleGetCommon()
      this.commonEcho()
    },
    // 回显选中
    commonEcho() {
      this.$nextTick(() => {
        this.commonList.map(item => {
          this.$refs.commonTable.toggleRowSelection(item, false)
          this.commonChecked.map(itt => {
            if (itt.id === item.id) this.$refs.commonTable.toggleRowSelection(item, true)
          })
        })
      })
    },
    handleCommonSelection(selection) {
      this.commonCheck = selection
      this.$nextTick(() => {
        this.commonChecked = this.commonCheck
      })
    },
    checkStatus() {
      return this.commonCheck.length < 10
    },
    // 确定选择供应商
    handleSubmitCommon() {
      this.commonChecked = this.commonCheck
      this.commonOpen = false
    },
    // 删除已选择的供应商
    handleDeleteCommon(row) {
      const idx = this.commonChecked.findIndex(item => item.id === row.id)
      if (idx !== -1) this.commonChecked.splice(idx, 1)
      if (this.$refs.commonTable) this.commonEcho()
    },
    // 改变供应商站内通知
    handleSite(item) {
      const idx = this.supplierChecked.findIndex(ite => ite.id === item.id)
      const idxs = this.SupplierList.findIndex(ite => ite.id === item.id)
      if (idxs !== -1) this.$set(this.SupplierList[idxs], 'isSite', item.isSite)
      if (idx !== -1) this.$set(this.supplierChecked[idx], 'isSite', item.isSite)
    },
    // 立即发布
    handleSubmitPublish() {
      this.$refs['publishForm'].validate(valid => {
        if (valid) {
          let data = {}
          data.demandId = this.publishForm.demandId
          data.publishWay = this.publishForm.publishWay
          let products = [...this.publishForm.products]
          if (this.publishForm.publishWay === 'private') {
            data.suppliers = []
            this.supplierChecked.map(item => {
              let arr = {
                supplierId: undefined,
                uuids: [],
                userId: undefined,
                userName: undefined,
                openid: undefined,
                companyName: undefined,
                phone: undefined,
                nickName: undefined
              }
              arr.supplierId = item.id
              if (item.contactList.length === 1) {
                arr.uuids.push(item.contactList[0].uuid)
                arr.phone = item.contactList[0].phone
                arr.openid = item.contactList[0].openid
                arr.nickName = item.contactList[0].nickName
                if (item.isSite) {
                  arr.userId = item.contactList[0].userId
                  arr.userName = item.contactList[0].userName
                  arr.companyName = item.contactList[0].companyName
                } else {
                  arr.userId = undefined
                  arr.userName = undefined
                  arr.companyName = undefined
                }
                data.suppliers.push(arr)
              } else {
                let arrs = { ...arr }
                const newList = item.contactList.filter(ite => ite.ischecked && !ite.invalid)
                if (newList.length) {
                  newList.map(ite => {
                    arrs.uuids.push(ite.uuid)
                    arrs.phone = ite.phone
                    arrs.openid = ite.openid
                    arrs.nickName = ite.nickName
                    if (ite.isSite) {
                      arrs.userId = ite.userId
                      arrs.userName = ite.userName
                      arrs.companyName = ite.companyName
                    } else {
                      arrs.userId = undefined
                      arrs.userName = undefined
                      arrs.companyName = undefined
                    }
                    data.suppliers.push(arrs)
                  })
                } else {
                  for (let i = 0; i < item.contactList.length; i++) {
                    if (!item.contactList[i].invalid) {
                      arrs.uuids.push(item.contactList[i].uuid)
                      arrs.phone = item.contactList[i].phone
                      arrs.openid = item.contactList[i].openid
                      arrs.nickName = item.contactList[i].nickName
                      if (item.contactList[i].isSite) {
                        arrs.userId = item.contactList[i].userId
                        arrs.userName = item.contactList[i].userName
                        arrs.companyName = item.contactList[i].companyName
                      } else {
                        arrs.userId = undefined
                        arrs.userName = undefined
                        arrs.companyName = undefined
                      }
                      data.suppliers.push(arrs)
                      break
                    }
                  }
                }
              }
            })
          }
          if (this.publishForm.publishWay === 'common') {
            data.suppliers = []
            this.commonChecked.map(item => {
              let arr = { supplierId: undefined, uuids: [] }
              arr.supplierId = item.id
              data.suppliers.push(arr)
            })
          }
          let arr = []
          products.map(item => {
            arr.push({
              demandProductId: item.demandProductId,
              productId: item.productId,
              productName: item.productName,
              quantity: item.needQuantity,
              source: item.source,
              unit: item.unit
            })
          })
          data.products = arr
          const numOne = this.supplierChecked.length
          const numTwo = data.suppliers.length
          if (numOne > numTwo) {
            this.$message.error('已选择联系人数量小于已选择供应商数量，请检查是否缺少选择供应商的联系人')
            return
          }
          const imgOss = products.map(item => item.picture1_oss)
          const img = products.map(item => item.picture1)
          if (imgOss.length) data.image = imgOss[0]
          else if (img.length) data.image = this.imgPath + img[0]
          if (this.publishForm.companyId != -1) {
            company.supplier({ id: this.publishForm.companyId }).then(res => {
              if (res.code === 200) {
                data.info = res.data.company.companyName + '向您发布了采购需求,采购产品如下：' + products.map(item => item.productName).join(',') + '，供货截止：' + this.publishForm.supplyEndTime + '，请您及时查看。'
                this.handlepurchasingDemandPublish(data)
              } else {
                data.info = '您收到一条新的采购需求,采购产品如下：' + products.map(item => item.productName).join(',') + '，供货截止：' + this.publishForm.supplyEndTime + '，请您及时查看。'
                this.handlepurchasingDemandPublish(data)
              }
            })
          } else {
            const userName = this.$store.getters.info.realName || this.$store.getters.info.nickName
            data.info = userName + '向您发布了采购需求,采购产品如下：' + products.map(item => item.productName).join(',') + '，供货截止：' + this.publishForm.supplyEndTime + '，请您及时查看。'
            this.handlepurchasingDemandPublish(data)
          }

          if (this.buyType) {
            let buyData = {
              detail: this.publishForm.remark,
              effectiveTime: this.publishForm.expireTime,
              products: this.publishForm.products,
              title: this.publishForm.title,
              type: 'd1'
            }
            addBuy(buyData).then(response => {
              if (response.code === 200) {
                this.$message.success('同步成功')
              } else this.$message.error(response.msg)
            })
          }
        }
      })
    },
    // 发布
    handlepurchasingDemandPublish(data) {
      purchasingDemandPublish(data).then(res => {
        if (res.code === 200) {
          this.$message.success('发布成功')
          this.getList()
          this.publishOpen = false
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    handleSizeChange(val) {
      this.commonPage = 1
      this.commonSize = val
    },
    handleCurrentChange(val) {
      this.commonPage = val
    },
    handleNameQuery() {
      this.commonQuery.name = this.commonName
      this.commonQuery.scope = this.businessScope
      this.handleGetCommonMore()
    },
    // 打开合同模板
    handleContractTpl() {
      this.$refs.contractTpl.getList()
    },
    // prettier-ignore
    handleUocConfig() {
      const that = this
      const config = that.supplierConfig
      that.$prompt('请输入最少询价几家供应商', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValue: config.configValue || 0,
        closeOnClickModal: false,
        inputPattern: /^\d+$/,
        inputErrorMessage: '请输入最少询价几家供应商'
      }).then(({ value }) => {
        const data = {
          configId: config.configId,
          configKey: config.configKey,
          configValue: value
        }
        updateConfig(data).then(function (res) {
          if (res.code === 200) {
            that.$message.success('操作成功')
            that.getConfig()
          } else that.$message.error(res.msg)
        })
      }).catch(() => {
      })
    },
    // 需求发送记录
    handleRecord(row) {
      const demandId = row.id
      getDemandRecordList({ demandId }).then(async res => {
        if (res.code === 200) {
          const list = res.data
          if (list.length) {
            await Promise.all(
              list.map(async item => {
                const statusData = await getMsgDetail({
                  phone: item.send_phone,
                  day: this.parseTime(item.time, '{y}{m}{d}')
                })
                if (statusData.code === 200) {
                  if (statusData.data.length) {
                    item.status = statusData.data[0].sendStatus
                  } else item.status = 1
                }
              })
            )
          }
          this.recordList = list
          this.recordOpen = true
        } else this.$message.error(res.msg)
      })
    },
    recordStatus(row) {
      if (row.status === 1) return '等待回执'
      if (row.status === 2) return '发送失败'
      if (row.status === 3) return '发送成功'
    },
    replyStatus(row) {
      return row.is_reply ? '已回复' : '未回复'
    },
    // 查看明细
    handleViewDetails(row) {
      const requestId = row.request_id
      purchasingDemandSendRecord({ requestId }).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          this.viewDetailsList = data
          this.viewDetailsOpen = true
        } else this.$message.error(msg)
      })
    },
    // 重新发送
    handleResend(row) {
      const requestId = row.request_id
      reSendDemandMsg({ requestId }).then(res => {
        if (res.code === 200) {
          this.$message.success('重新发送成功')
          this.$set(row, 'send_times', row.send_times + 1)
        } else this.$message.error(res.msg)
      })
    },
    // 获取所有操作按钮配置
    getAllActions(row) {
      return [
        {
          key: 'detail',
          label: '查看详情',
          className: 'table-btn primary',
          show: true,
          handler: this.handleView,
          permission: ['purchasing:demand:detail']
        },
        {
          key: 'publish',
          label: '发布需求',
          className: `table-btn primary hasbg${expireTimeFormat(row.expireTime) === '已过期' ? ' disabled' : ''}`,
          show: true,
          handler: this.handlePublish,
          permission: ['purchasing:demand:send'],
          disabled: expireTimeFormat(row.expireTime) === '已过期'
        },
        {
          key: 'offer',
          label: row.replyNum ? '查看报价' : '暂无报价',
          className: `table-btn primary hasbg${!row.replyNum ? ' disabled' : ''}`,
          show: row.status,
          handler: this.handleOffer,
          permission: ['purchasing:demand:send'],
          disabled: !row.replyNum,
          badge: !!row.replyNum
        },
        {
          key: 'offerDisabled',
          label: '查看报价',
          className: 'table-btn disabled',
          show: !row.status,
          handler: () => {},
          permission: ['purchasing:product:reply'],
          disabled: true
        },
        {
          key: 'delete',
          label: '删除需求',
          className: 'table-btn danger hasbg',
          show: true,
          handler: this.handleDelete,
          permission: ['purchasing:demand:del']
        },
        {
          key: 'record',
          label: '发送记录',
          className: 'table-btn orange',
          show: true,
          handler: this.handleRecord,
          permission: []
        }
      ]
    },
    // 获取常用按钮
    getCommonActions(row) {
      const allActions = this.getAllActions(row).filter(action => action.show && this.checkActionPermission(action))
      const commonActions = allActions.filter(action => this.isCommonAction(action.key))
      // 如果没有设置常用按钮，默认只显示查看详情，如果没权限则往下顺延
      if (commonActions.length === 0) {
        // 优先显示查看详情
        const detailAction = allActions.find(action => action.key === 'detail')
        if (detailAction) {
          return [detailAction]
        }
        // 如果没有查看详情权限，返回第一个有权限的按钮
        return allActions.length > 0 ? [allActions[0]] : []
      }
      // 返回所有常用按钮，不限制数量
      return commonActions
    },
    // 判断是否为常用按钮
    isCommonAction(actionKey) {
      return this.commonActions.includes(actionKey)
    },
    // 判断是否有更多操作按钮
    hasMoreActions(row) {
      const allActions = this.getAllActions(row).filter(action => action.show && this.checkActionPermission(action))
      const commonActions = this.getCommonActions(row)
      // 如果显示的按钮数量大于常用按钮数量，则显示更多操作
      return allActions.length >= commonActions.length
    },
    // 切换常用按钮设置
    toggleCommonAction(actionKey) {
      const index = this.commonActions.indexOf(actionKey)
      if (index > -1) {
        this.commonActions.splice(index, 1)
      } else {
        // 允许设置多个常用按钮
        this.commonActions.push(actionKey)
      }
      localStorage.setItem(this.userId + '.demandCommonActions', JSON.stringify(this.commonActions))
    },
    // 检查操作权限
    checkActionPermission(action) {
      if (!action.permission || action.permission.length === 0) return true
      return this.checkPermi(action.permission)
    },
    // 计算操作列宽度
    getActionColumnWidth() {
      // 如果列表为空，返回默认宽度
      if (!this.list || this.list.length === 0) {
        return 220
      }
      // 取第一行数据来计算宽度
      const firstRow = this.list[0]
      const commonActions = this.getCommonActions(firstRow)
      const hasMoreActions = this.hasMoreActions(firstRow)
      // 常用按钮数量 + 更多操作按钮(如果有的话) * 110px
      const buttonCount = commonActions.length + (hasMoreActions ? 1 : 0)
      const width = buttonCount * 110
      // 设置最小宽度为 110px，最大宽度为 550px
      return Math.max(110, Math.min(550, width))
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';

.Box {
  padding: 15px 20px;
}

.custom-dialog ::v-deep {
  .publish-title {
    display: flex;
    align-items: center;
    padding: 0 20px 8px;

    span {
      font-size: 14px;
      color: #999999;
    }

    .title-item {
      width: 160px;
      height: 46px;
      line-height: 46px;
      border: 1px solid #cbd7e2;
      border-radius: 5px;
      margin-left: 15px;
      cursor: pointer;
      text-align: center;
      position: relative;

      &:hover,
      &.active {
        background-image: url('~@/assets/images/subtract.png');
        background-position: right bottom;
        background-repeat: no-repeat;
        background-color: #dbe8ff;
        border-color: $blue;
        color: $blue;

        &:after {
          content: '';
          width: 0;
          border-style: solid;
          border-width: 0 8px 8px 8px;
          border-color: transparent transparent #f0f3f9 transparent;
          position: absolute;
          left: 50%;
          transform: translateX(-50%);
          bottom: -9px;
        }
      }
    }
  }

  .publish-box {
    background-color: #f0f3f9;
    padding: 0px 20px 20px;

    .publish-desc {
      padding: 11px 0;
      border-bottom: 1px solid #e2e6f3;
      margin-bottom: 15px;
      display: flex;
      flex-wrap: wrap;

      .desc-item {
        width: 320px;
        line-height: 32px;

        span {
          font-size: 12px;
          display: inline-block;
          width: 68px;
          color: $info;
        }

        b {
          font-size: 14px;
          font-weight: 500;
          color: #333333;
        }

        &:nth-child(2n) {
          width: calc(100% - 320px);
        }
      }
    }

    .publish-table-title {
      padding: 12px 0;
      font-size: 12px;

      span {
        color: #999999;
      }

      button {
        margin-left: 25px;
        color: $blue;
        border: 0;
        background: transparent;
        cursor: pointer;
        outline: none;
      }
    }

    .custom-table {
      .el-table__row.disabled {
        td.el-table__cell {
          background-color: #cccccc;
        }
      }

      .el-form-item {
        margin-top: 10px !important;
        margin-bottom: 10px !important;

        .el-form-item__error {
          top: 95%;
          padding-top: 0;
        }
      }
    }
  }

  .publish-company {
    display: flex;
    align-items: center;
    padding: 17px 0;
    margin: 0 20px;
    border-bottom: 1px solid #e2e6f3;

    span.select {
      font-size: 14px;
      color: #999999;
      margin-right: 20px;
    }
  }

  .publish-tag {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    padding: 15px 0;
    margin: 0 20px;
    line-height: 30px;
    border-bottom: 1px solid #e2e6f3;

    span.select {
      font-size: 14px;
      color: #999999;
      margin-right: 20px;
    }

    .tag-item {
      padding: 0 10px;
      font-size: 14px;
      font-weight: 500;
      color: #333333;
      position: relative;
      margin: 5px 15px 5px 0;
      border-radius: 5px;
      background-color: #e5e5e5;

      i {
        width: 16px;
        height: 16px;
        line-height: 16px;
        text-align: center;
        border-radius: 50%;
        font-size: 12px;
        background-color: #525356;
        color: $white;
        cursor: pointer;
        position: absolute;
        right: -5px;
        top: -5px;
      }
    }

    .tag-btn {
      font-size: 14px;
      padding: 5px 10px;
      background-color: #dfebff;
      color: $blue;
      border-radius: 5px;
      border: 1px dashed $blue;
      cursor: pointer;
      outline: none;
      &.danger {
        margin-left: 10px;
        background-color: #ffd0d0;
        color: #ec2454;
        border-color: #ec2454;
      }
    }
  }

  .dialog_title {
    font-size: 18px;
    color: #666666;
  }

  .dialog_switch {
    margin-left: 50px;
    display: flex;
    align-items: center;

    .text {
      font-size: 14px;
      line-height: 26px;
      color: #666666;
    }
  }
}

::v-deep {
  .custom-expanded.el-table .row-expand-cover .cell .el-table__expand-icon {
    display: none;
  }

  .custom-expanded.el-table .el-table__cell.el-table__expanded-cell {
    padding: 0;
    border-bottom: 0;
  }

  .custom-expanded .el-badge__content.is-fixed.is-dot {
    right: 10px;
    top: 2px;
  }

  .table-expand {
    padding-left: 80px;
    padding-right: 10px;
    padding-bottom: 10px;
    background-color: #eceef1;
  }

  .el-table__row.expanded {
    background-color: #eceef1;

    td.el-table__cell {
      background-color: #eceef1;
    }
  }

  .el-table__row.row-expand-cover.expanded {
    background-color: transparent;

    td.el-table__cell {
      background-color: transparent;
    }
  }
}

::v-deep {
  .table-expand {
    padding-left: 80px;
    padding-bottom: 10px;
    padding-top: 10px;
    margin-top: -13px;
    background-color: #eceef1;
  }

  .el-table__row.expanded {
    background-color: #eceef1;

    td.el-table__cell {
      background-color: #eceef1;
    }
  }
}

.publish-new {
  border: 1px solid #cbd6e2;
  border-radius: 5px;
  margin: 0 20px 20px;

  .publish-new-title {
    display: flex;
    align-items: center;
    height: 38px;
    background-color: #f0f3f9;
    border-radius: 5px 5px 0 0;
    position: relative;

    .title-item {
      padding: 0 30px;
      font-size: 14px;
      color: #999999;
      cursor: pointer;

      &.active,
      &:hover {
        border: 1px solid #cbd6e2;
        line-height: 46px;
        color: $blue;
        position: relative;
        margin-top: -8px;
        background-color: #ffffff;
        border-radius: 5px 5px 0 0;
      }
    }
  }

  .publish-desc {
    padding: 15px;
    line-height: 32px;
    font-size: 14px;
    display: flex;
    flex-wrap: wrap;

    .desc-item {
      margin-right: 20px;

      span {
        font-size: 12px;
        color: #666666;
        margin-right: 10px;
      }
    }
  }
}

.hasChecked-title {
  text-align: center;
  font-size: 14px;
  color: #666666;
  margin-bottom: 10px;
}

.hasChecked-info {
  display: flex;
  flex-wrap: wrap;
  background-color: #f0f3f9;
  padding: 20px;
  position: relative;
  margin-top: 10px;

  &:before {
    content: '';
    width: 0;
    border-style: solid;
    border-width: 0 8px 8px 8px;
    border-color: transparent transparent #f0f3f9 transparent;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    top: -8px;
  }

  .hasChecked-item {
    border: 1px solid #2e73f3;
    background-color: #f1f6ff;
    font-size: 14px;
    color: #2e73f3;
    border-radius: 5px;
    margin: 5px;
    position: relative;
    padding: 0 40px 0 10px;
    height: 30px;
    line-height: 30px;

    i {
      background-color: #2e73f3;
      color: #ffffff;
      display: inline-block;
      height: 28px;
      line-height: 28px;
      position: absolute;
      right: 0;
      width: 30px;
      text-align: center;
      cursor: pointer;
    }
  }
}

.hasChecked-search {
  margin: 20px 0 10px 0;
}

::v-deep .isSite {
  position: relative;

  .el-switch__core {
    height: 24px;
    border-radius: 12px;
    min-width: 50px;

    &:after {
      left: 4px;
      top: 3px;
    }
  }

  &.el-switch {
    &.is-checked {
      .el-switch__core {
        &:after {
          margin-left: -20px;
          left: 100%;
        }
      }
    }
  }

  &.is-checked {
    .el-switch__label--left {
      opacity: 0;
    }

    .el-switch__label--right {
      opacity: 1;
    }
  }

  .el-switch__label {
    position: absolute;
    top: 0;
  }

  .el-switch__label--left {
    right: 0;
    color: #999999;
    z-index: 1;
    margin-right: 8px;
  }

  .el-switch__label--right {
    left: 0;
    color: #ffffff;
    opacity: 0;
    margin-left: 8px;
  }
}

.custom-badge ::v-deep {
  .el-badge__content.is-fixed.is-dot {
    right: 10px;
    top: 2px;
  }
}
</style>
