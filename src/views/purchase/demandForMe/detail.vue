<template>
  <div class="newBox bgcf9 vh-85">
    <div class="purchasing-detail">
      <div class="purchasing-detail-header">
        <div class="purchasing-detail-title">{{ info.title }}</div>
        <div class="custom-search flex">
          <div class="custom-search-form flex">
            <input type="text" v-model="keyword" placeholder="请输入产品名称" class="custom-search-input" @keyup.enter="handleQuery" />
            <button type="button" class="custom-search-button pointer" @click="handleQuery">
              <i class="el-icon-search"></i>
              搜索
            </button>
          </div>
        </div>
      </div>

      <el-descriptions :column="4" border labelClassName="descriptions-label">
        <el-descriptions-item contentClassName="descriptions-content">
          <template slot="label">标题</template>
          {{ info.title }}
        </el-descriptions-item>
        <el-descriptions-item contentClassName="descriptions-content">
          <template slot="label">供货截止时间</template>
          {{ info.supplyEndTime }}
        </el-descriptions-item>
        <el-descriptions-item contentClassName="descriptions-content" v-if="info.expireTime">
          <template slot="label">剩余有效时间</template>
          <Countdown :expireTime="info.expireTime" pattern="{d}{h}{m}" />
        </el-descriptions-item>
        <el-descriptions-item contentClassName="descriptions-content">
          <template slot="label">创建时间</template>
          {{ info.createTime }}
        </el-descriptions-item>
        <el-descriptions-item contentClassName="descriptions-content">
          <template slot="label">状态</template>
          <span :style="{ color: info.status ? '#999' : '#F35D09' }">{{ info.status ? '已发布' : '未发布' }}</span>
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">备注</template>
          {{ info.remark }}
        </el-descriptions-item>
      </el-descriptions>

      <el-row style="margin-bottom: 20px">
        <el-col :span="1.5">
          <button type="button" class="batch-btn primary" :class="{ disabled: !checkedList.length }" @click="handleOffers" :disabled="!checkedList.length" v-hasPermi="['purchasing:product:reply']">查看报价</button>
        </el-col>
      </el-row>

      <el-table v-loading="loading" ref="detailTable" stripe :data="list" row-key="id" :key="key" style="width: 100%" class="custom-table" @selection-change="handleSelection">
        <el-table-column :selectable="chckeSelect" align="center" type="selection" width="50"></el-table-column>
        <el-table-column align="center" type="index" label="序号"></el-table-column>
        <el-table-column align="center" prop="productName" label="产品名称" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <span class="table-link" @click="handleView(row.productId, row)">
              <span v-if="row.source === 'common'">(公域)</span>
              <span style="color: #fe7f22" v-else>(私域)</span>
              {{ row.productName }}
            </span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="picture1" label="图片" width="75">
          <template slot-scope="{ row }">
            <el-image :src="formatProductImg(row)" fit="cover" @click="handleImgView(row)">
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline"></i>
              </div>
            </el-image>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="specs" label="规格" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" prop="productCode" label="产品编码" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" prop="materialQuality" label="材质" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" prop="surface" label="表面处理" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" prop="unit" label="单位" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" prop="industry" label="行业分类" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" prop="weight" label="重量" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" prop="quantity" label="计划采购数量" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" prop="needQuantity" label="实际采购数量" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" prop="status" label="状态" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <el-badge :value="row.status" style="margin: 10px 0" v-if="row.status"><span>已回复</span></el-badge>
            <span class="color-red" v-else>未回复</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="remark" label="备注" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" label="操作" width="120" v-if="checkPermi(['purchasing:product:reply'])">
          <template slot-scope="{ row }">
            <button type="button" class="table-btn primary" :class="{ disabled: !row.status }" :disabled="!row.status" @click="handleOffer(row)" v-hasPermi="['purchasing:product:reply']">查看报价</button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 产品详情 -->
    <product-dialog ref="productInfo"></product-dialog>

    <!-- 查看报价 -->
    <offer-dialog ref="offerInfo" @close="handleCancel"></offer-dialog>
  </div>
</template>

<script>
import ProductDialog from '@/views/public/product/dialog'
import { purchasingDemandDetail, purchasingProductReply } from '@/api/purchase'
import { expireTimeFormat } from '@/utils'
import { getProduct } from '@/api/system/product'
import { getPrivateduct } from '@/api/system/privateduct'
import OfferDialog from './offer'
import { checkPermi } from '@/utils/permission'

export default {
  components: { ProductDialog, OfferDialog },
  data() {
    return {
      demandId: undefined,
      keyword: undefined,
      key: 1,
      info: {},
      list: [],
      loading: true,
      checkedList: []
    }
  },
  created() {
    const Id = this.$route.params && this.$route.params.Id
    if (Id) this.getInfo(Id)
  },
  methods: {
    checkPermi,
    expireTimeFormat,
    // 返回
    goBack() {
      this.$store.dispatch('tagsView/delView', this.$route)
      this.$router.go(-1)
    },
    // 获取信息
    async getInfo(demandId) {
      this.demandId = demandId
      this.loading = true
      const res = await purchasingDemandDetail({ demandId })
      if (res.code === 200) {
        this.info = res.data
        this.list = res.data.hasOwnProperty('products') && res.data.products.filter(item => item.productId)
        if (this.list) {
          await Promise.all(
            this.list.map(async item => {
              if(item.hasOwnProperty('productId') && item.productId) {
                const params = { productId: item.productId, source: item.source }
                const { data } = await purchasingProductReply(params)
                item.status = data.length
                item.offer = data
              }
            })
          )
        }
        this.loading = false
        this.key = Math.random()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 检查是否可以选中
    chckeSelect(row, index) {
      if (row.status) {
        return true
      } else {
        return false
      }
    },
    // 搜索
    handleQuery() {
      if (this.keyword) {
        const arr = [...this.info.products]
        const querylist = arr.filter(item => item.productName.includes(this.keyword))
        this.list = querylist
        this.key = Math.random()
      } else {
        this.list = this.info.products
      }
    },
    // 多选
    handleSelection(selection) {
      this.checkedList = selection
    },
    // 查看报价
    handleOffer(row) {
      this.$refs.offerInfo.handleOffer([row])
    },
    // 批量查看报价
    handleOffers() {
      this.$refs.offerInfo.handleOffer(this.checkedList)
    },
    // 取消查看报价
    handleCancel() {
      this.checkedList = []
      this.$refs.detailTable.clearSelection()
    },
    // 产品详情
    handleView(Id, item) {
      if (item.source === 'common') {
        getProduct(Id).then(res => {
          this.$refs.productInfo.handleView(res.data)
        })
      } else {
        getPrivateduct(Id).then(res => {
          this.$refs.productInfo.handleView(res.data)
        })
      }
    },
    // 图片预览
    handleImgView(row) {
      this.$refs.productInfo.handleImgView(row)
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
.purchasing-detail {
  margin: 0 20px 20px;
  padding: 0 20px 20px;
  background-color: $white;
  overflow: hidden;
  box-shadow: 0px 0px 26px 0px rgba(0, 0, 0, 0.05);
  border-radius: 5px;
  &-header {
    height: 53px;
    background-color: #f1f1f3;
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    .custom-search {
      padding: 0;
      margin-left: 175px;
      background-color: #f1f1f3;
    }
  }
  &-title {
    display: inline-block;
    height: 100%;
    line-height: 53px;
    padding-right: 30px;
    background-color: $white;
  }
  ::v-deep .el-descriptions {
    margin-bottom: 20px;
    .descriptions-label {
      width: 106px;
      text-align: right;
    }
    .descriptions-content {
      min-width: calc(20% - 106px);
      word-break: break-all;
    }
  }
}
.batch-btn {
  padding: 0 20px;
  line-height: 30px;
  font-size: 12px;
  border-radius: 5px;
  border: 0;
  cursor: pointer;
  &:hover {
    opacity: 0.8;
  }
  &:disabled {
    cursor: no-drop;
  }
  &.primary {
    background-color: $blue;
    color: $white;
    &.disabled {
      background-color: #cdcdcd;
    }
  }
}
</style>
