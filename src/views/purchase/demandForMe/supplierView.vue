<template>
  <div>
    <el-dialog v-dialogDragBox :title="title" :visible.sync="open" width="1150px" class="custom-dialog">
      <div class="detailBox" v-if="!isNone">
        <div class="basicBox">
          <div class="basicTitle">
            <span>基本信息</span>
            <template v-if="this.diff.some(item => item.diff.some(el => ['companyName', 'companyRegStatus', 'companyPhoneSourceList', 'companyEmailList', 'companyWebSite', 'companyLegalPersonName', 'companyApprovedTime', 'companyRegCapital', 'companyActualCapitalAmount', 'companyRegNumber', 'companyTaxNumber', 'companyOrgNumber', 'companyType', 'companyCategory', 'companySocialStaffNum', 'companyRegLocation', 'companyBusinessScope'].includes(el.fieldName)))">
              <b>有变动信息</b>
              <em @click="diffView()">查看详情</em>
            </template>
          </div>
          <div class="basicHead">
            <img :src="info.companyLogo || require('@/assets/images/company_icon1.png')" :alt="info.companyName" class="basicLogo" />
            <div class="basicInfo">
              <div class="basicName">
                <span>{{ info.companyName }}</span>
                <em class="green">{{ info.companyRegStatus }}</em>
              </div>
              <div class="basicInfoFlex">
                <div class="basicInfoItem">
                  <img src="@/assets/images/detail_01.png" alt="电话" />
                  <div class="basicInfoItemTxt">
                    <span>电话：{{ info.companyPhoneSourceList.length ? info.companyPhoneSourceList[0]['number'] : '' }}</span>
                    <div class="button" @click="handleShowAll()" v-if="info.companyPhoneSourceList.length > 1">
                      更多
                      <em>{{ info.companyPhoneSourceList.length }}</em>
                    </div>
                  </div>
                </div>
                <div class="basicInfoItem">
                  <img src="@/assets/images/detail_02.png" alt="邮箱" />
                  <div class="basicInfoItemTxt">
                    <span>邮箱：{{ info.companyEmailList.length ? info.companyEmailList[0] : '' }}</span>
                    {{ info.companyEmailList.length }}
                    <div class="button" @click="handleShowAll('email')" v-if="info.companyEmailList.length > 1">
                      更多
                      <em>{{ info.companyEmailList.length }}</em>
                    </div>
                  </div>
                </div>
                <div class="basicInfoItem" v-if="info.companyWebSite">
                  <img src="@/assets/images/detail_03.png" alt="官网" />
                  <span>官网：{{ info.companyWebSite }}</span>
                </div>
              </div>
            </div>
          </div>
          <el-descriptions class="basicDesc" :column="3" border>
            <el-descriptions-item label="法定代表人">
              <div class="basicDescUser">
                <img src="@/assets/images/company_icon2.png" alt="法定代表人" />
                <div class="basicDescUserFlex">
                  <span>{{ info.companyLegalPersonName }}</span>
                </div>
              </div>
            </el-descriptions-item>
            <el-descriptions-item label="登记状态">
              <span class="green">{{ info.companyRegStatus }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="成立日期">{{ parseTime(info.companyApprovedTime, '{y}-{m}-{d}') }}</el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                统一社会
                <br />
                信用代码
              </template>
              {{ info.companyCreditCode }}
            </el-descriptions-item>
            <el-descriptions-item label="注册资本">{{ info.companyRegCapital }}</el-descriptions-item>
            <el-descriptions-item label="实缴资本">{{ info.companyActualCapitalAmount }}</el-descriptions-item>
            <el-descriptions-item label="工商注册号">{{ info.companyRegNumber }}</el-descriptions-item>
            <el-descriptions-item label="纳税人识别号">{{ info.companyTaxNumber }}</el-descriptions-item>
            <el-descriptions-item label="组织机构代码">{{ info.companyOrgNumber }}</el-descriptions-item>
            <el-descriptions-item label="企业类型">{{ info.companyType ? info.companyType.split(';').pop() : '' }}</el-descriptions-item>
            <el-descriptions-item label="行业">{{ info.companyCategory }}</el-descriptions-item>
            <el-descriptions-item label="参保人数">{{ info.companySocialStaffNum + '人' }}</el-descriptions-item>
            <el-descriptions-item label="人员规模">-</el-descriptions-item>
            <el-descriptions-item label="注册地址" :span="2">{{ removeHtmlTag(info.companyRegLocation, 300) }}</el-descriptions-item>
            <el-descriptions-item label="经营范围" :span="3">{{ info.companyBusinessScope }}</el-descriptions-item>
          </el-descriptions>
        </div>
        <div class="company-contact" v-if="followUps.length > 0">
          <div class="basicTitle">
            <span>其他</span>
          </div>
          <div class="followList">
            <div class="followItem" v-for="item in followUps" :key="item.id">
              <div class="followInfo">
                <div class="followInfo-title">{{ item.salesperson }}</div>
                <div class="followInfo-con">{{ item.followInfo || item.salesperson + '于' + item.createTime + '创建了该审批' }}</div>
                <div class="followInfo-file" v-if="!!archivesFormat(item).length">
                  <image-preview is-list class="followInfo-file-img" v-for="(item, idx) in archivesFormat(item)" :key="idx" :src="item" />
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="viewMore" @click="handleMore" v-if="showMore && info.companyCreditCode">查看更多&gt;</div>
        <company-more ref="companyMore" :show="!showMore" />
      </div>
      <el-empty v-if="isNone" />
      <div slot="footer" class="dialog-footer">
        <el-button class="custom-dialog-btn primary" @click="open = false">关闭</el-button>
      </div>
    </el-dialog>
    <!-- 更改比对 -->
    <el-dialog v-dialogDragBox title="变动历史" :visible.sync="dialogTableVisible">
      <div v-for="item in diffList" :key="item.id">
        <h3>变动时间：{{ item.createTime }}</h3>
        <el-table border :data="item.diff">
          <el-table-column property="fieldName" label="更改字段" width="150">
            <template slot-scope="scope">
              <span v-if="scope.row.fieldName === 'companyAlias'">公司别名</span>
              <span v-if="scope.row.fieldName === 'companyType'">公司类型</span>
              <span v-if="scope.row.fieldName === 'companyRegLocation'">公司地址</span>
              <span v-if="scope.row.fieldName === 'companySocialStaffNum'">参保人数</span>
              <span v-if="scope.row.fieldName === 'companyCategory'">所属行业</span>
              <span v-if="scope.row.fieldName === 'companyRegCapital'">注册资本</span>
              <span v-if="scope.row.fieldName === 'companyApprovedTime'">注册时间</span>
              <span v-if="scope.row.fieldName === 'companyBusinessScope'">经营范围</span>
              <span v-if="scope.row.fieldName === 'companyLegalPersonName'">法人</span>
              <span v-if="scope.row.fieldName === 'companyManagerPersonName'">主要人员</span>
              <span v-if="scope.row.fieldName === 'companyPhoneSourceList'">联系电话</span>
              <span v-if="scope.row.fieldName === 'companyEmailList'">公司邮箱</span>
            </template>
          </el-table-column>
          <el-table-column property="origin" label="更改前"></el-table-column>
          <el-table-column property="newValue" label="更改后"></el-table-column>
        </el-table>
      </div>
    </el-dialog>
    <!--显示全部电话/邮箱-->
    <el-dialog v-dialogDragBox :title="moreTitle" :visible.sync="moreOpen" width="500px" class="custom-dialog">
      <div style="padding: 0 20px">
        <el-table border :data="moreList" :show-header="false" style="width: 100%" @cell-mouse-enter="moreEnter" @cell-mouse-leave="moreLeave">
          <el-table-column prop="info">
            <template slot-scope="{ row }">
              <div class="moreItem">
                <span>{{ row.info }}</span>
                <em v-clipboard:copy="row.info" v-clipboard:success="copySuccess" v-if="row.show">复制</em>
                <em @click="handleSendMail(row)" v-if="moreTitle === '邮箱' && row.show">发送邮件</em>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="moreOpen = false">关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { enterprisesFollowup } from '@/api/payment'
import { parseTime } from '@/utils/ruoyi'
import { removeHtmlTag } from '@/utils'
import CompanyMore from '@/views/payment/companyMore'
import { getRemoteCompanyInfo } from '@/api/tender'
import { getToken } from '@/utils/auth'

export default {
  components: { CompanyMore },
  data() {
    return {
      title: '公司详情',
      open: false,
      info: {},
      mangerList: [],
      // 补充信息
      replenishOpen: false,
      replenishInfo: {},
      replenishForm: {},
      replenishRules: {},
      fileType: ['png', 'PNG', 'jpg', 'JPG', 'jpeg', 'JPEG'],
      followUps: [],
      diff: [],
      dialogTableVisible: false,
      diffList: [],
      // 显示全部电话/邮箱
      moreTitle: '',
      moreOpen: false,
      moreList: [],
      showMore: true,
      isLogin: !!getToken(),
      isDialog: false,
      isNone: false
    }
  },
  created() {
    this.infoInit()
  },
  methods: {
    removeHtmlTag,
    parseTime,
    // 初始化info
    infoInit() {
      this.info = {
        companyLogo: '',
        companyName: '',
        companyRegStatus: '',
        companyPhoneSourceList: [],
        companyEmailList: [],
        companyWebSite: '',
        companyLegalPersonName: '',
        companyApprovedTime: '',
        companyCreditCode: '',
        companyRegCapital: '',
        companyActualCapitalAmount: '',
        companyRegNumber: '',
        companyTaxNumber: '',
        companyOrgNumber: '',
        companyType: '',
        companyCategory: '',
        companySocialStaffNum: '0',
        companyRegLocation: '',
        companyBusinessScope: ''
      }
    },
    // 查询企业信息
    getDetail(companyName) {
      if (!companyName) return
      getRemoteCompanyInfo({ companyName }).then(res => {
        if (res.code === 200) {
          if (res.data) {
            this.getInfo(res.data, 'private', false)
            this.isNone = false
          } else {
            this.isDialog = false
            this.isNone = true
            this.$alert(`未查询到“<span style='color:#ff0000'>${ companyName }</span>”相关公司信息！<br />请检查公司名称是否正确，请输入公司全称！`, '系统提示', { dangerouslyUseHTMLString: true })
          }
        }
      })
    },
    // 查询企业详情
    getInfo(obj = {}, type = undefined, isDialog = true) {
      const query = this.$route.query
      this.isDialog = isDialog && !query.hasOwnProperty('isDialog')
      this.info = type === 'private' ? Object.assign({}, this.info, this.isJSON(obj)) : Object.assign({}, this.info, this.isJSON(obj.info))
      this.followUps = type === 'private' ? [] : obj.followUps
      this.diff = type === 'private' ? [] : obj.diff.map(el => ({ ...el, diff: this.isJSON(el.diff) }))
      this.mangerList = this.managerToArray(this.info)
      this.open = true
      this.showMore = true
    },
    // 高管转数组
    managerToArray(obj = {}) {
      let str = obj.companyManagerPersonName
      const reg = /\t:#\d\t;\t/g
      str = str.replace(reg, ',')
      let arr = str.split(',')
      let newArr = Array.from(new Set(arr.filter(item => item)))
      return newArr.filter(item => item !== obj.companyLegalPersonName)
    },
    // 判断JSON
    isJSON(str) {
      try {
        JSON.parse(str)
      } catch (e) {
        // 转换出错，抛出异常
        return str
      }
      return JSON.parse(str)
    },
    // 格式化跟进附件
    archivesFormat(item) {
      const { archives_oss, archives } = item
      if (archives_oss || archives) {
        const obj = item.archives_oss || item.archives || ''
        const arr = obj.split(',') || []
        if (!item.archives_oss) {
          return arr.map(item => {
            return this.imgPath + item
          })
        } else return arr
      }
      return []
    },
    // 查看更改详情
    diffView(type) {
      this.dialogTableVisible = true
      this.diffList = []
      this.diff.forEach(el => {
        if (el.diff.some(item => ['companyName', 'companyRegStatus', 'companyPhoneSourceList', 'companyEmailList', 'companyWebSite', 'companyLegalPersonName', 'companyApprovedTime', 'companyRegCapital', 'companyActualCapitalAmount', 'companyRegNumber', 'companyTaxNumber', 'companyOrgNumber', 'companyType', 'companyCategory', 'companySocialStaffNum', 'companyRegLocation', 'companyBusinessScope'].includes(item.fieldName))) {
          this.diffList.push(el)
        }
      })
    },
    // 点击查看更多
    handleMore() {
      this.showMore = false
      this.$refs.companyMore.getInfo(this.info)
    },
    // 显示全部电话/邮箱
    handleShowAll(type = 'phone') {
      this.moreList = []
      if (type === 'phone') {
        const list = this.info.companyPhoneSourceList
        list.map(item => {
          this.moreList.push({ info: item.number, show: false })
        })
        this.moreTitle = '电话'
        this.moreOpen = true
      } else {
        const list = this.info.companyEmailList
        list.map(item => {
          this.moreList.push({ info: item, show: false })
        })
        this.moreTitle = '邮箱'
        this.moreOpen = true
      }
    },
    // 电话/邮箱显示复制
    moreEnter(row) {
      row.show = true
    },
    // 电话/邮箱不显示复制
    moreLeave(row) {
      row.show = false
    },
    copySuccess() {
      this.$message.success('复制成功')
    },
    // 发送邮件
    handleSendMail(row) {
      window.location.href = `mailto:${row.info}`
    }
  }
}
</script>

<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';
.containerBox {
  width: 1240px;
  margin: 30px auto;
  display: flex;
  flex-direction: column;
}
.diskInfo {
  display: flex;
  align-items: center;
  line-height: 1.5em;
  padding-bottom: 20px;
  span {
    font-size: 14px;
    color: $disabled;
  }
  b {
    font-size: 16px;
    font-weight: normal;
    padding-right: 30px;
  }
}
.detailBox {
  padding: 0 20px;
  .detailTab {
    ::v-deep {
      .el-tabs__header {
        margin: 0;
        padding: 0 20px;
      }
      .el-tabs__nav-wrap::after {
        height: 0;
      }
    }
    &.bigTab {
      border-style: solid;
      border-color: #ebedf3;
      border-width: 1px 1px 0 1px;
      ::v-deep {
        .el-tabs__item {
          height: 50px;
          line-height: 50px;
        }
        .el-tabs__item.is-active {
          font-size: 16px;
        }
      }
    }
    &.smallTab {
      ::v-deep {
        .el-tabs__item {
          font-size: 12px;
          padding: 0 10px;
        }
      }
    }
  }
  .basic {
    &Box {
      overflow: hidden;
    }
    &Title {
      font-size: 16px;
      color: $blue;
      font-weight: 500;
      padding: 0 20px;
      line-height: 50px;
      border: 1px solid #e2e6f3;
      b {
        font-size: 14px;
        font-weight: normal;
        margin-left: 20px;
        margin-right: 15px;
        color: $orange;
      }
      em {
        display: inline-flex;
        align-items: center;
        font-style: normal;
        line-height: 22px;
        font-size: 12px;
        color: $orange;
        background-color: #ffefe6;
        border-radius: 22px;
        border: 1px solid $orange;
        padding: 0 16px;
        cursor: pointer;
        &:hover {
          background-color: $orange;
          color: $white;
        }
      }
    }
    &Head {
      background-color: #f2f3f9;
      padding: 20px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    &Logo {
      width: 90px;
      height: 90px;
      border-radius: 10px;
    }
    &Info {
      flex: 1;
      width: 100%;
      overflow: hidden;
      padding: 0 20px;
      display: flex;
      flex-direction: column;
    }
    &Name {
      display: flex;
      align-items: center;
      padding-bottom: 25px;
      span {
        font-size: 24px;
        font-weight: 500;
        color: $font;
        margin-right: 20px;
      }
      em {
        display: inline-flex;
        line-height: 30px;
        font-size: 14px;
        color: $white;
        font-style: normal;
        border-radius: 5px;
        padding: 0 17px;
        &.green {
          background-color: #31c776;
        }
      }
    }
    &InfoFlex {
      display: flex;
    }
    &InfoItem {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      img {
        height: 20px;
        margin-right: 5px;
      }
      &Txt {
        display: flex;
        align-items: center;
        font-size: 16px;
        height: 22px;
        line-height: 22px;
        color: $info;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        .button {
          margin-left: 5px;
          background-color: $white;
          border: 1px solid #e1e6eb;
          border-radius: 2px;
          color: #626a73;
          cursor: pointer;
          display: inline-block;
          font-size: 12px;
          line-height: 20px;
          padding: 0 6px;
          white-space: nowrap;
          em {
            font-style: normal;
            color: $blue;
            margin-left: 2px;
          }
          &:hover {
            background-color: #f2f9ff;
            border-color: #80c2ff;
          }
        }
      }
    }
    &Star {
      padding: 5px;
      border-radius: 8px;
      background-color: $white;
      display: flex;
      flex-direction: column;
      &Txt {
        display: flex;
        align-items: center;
        height: 24px;
        padding-left: 15px;
        background-color: #f4f8ff;
        span {
          font-size: 12px;
          color: #9ba7b3;
        }
        em {
          font-style: normal;
          font-size: 16px;
          font-weight: 500;
          &.primary {
            color: $blue;
          }
        }
      }
    }
    &Desc {
      ::v-deep {
        .el-descriptions__body {
          color: $font;
        }
        .is-bordered .el-descriptions-item__cell {
          border-color: #ebedf3;
        }
        .el-descriptions-item__label.is-bordered-label {
          font-size: 12px;
          color: $info;
          width: 100px;
          background-color: #f8faff;
        }
        .el-descriptions-item__content {
          width: 270px;
          .green {
            color: #31c776;
          }
        }
      }
      &User {
        display: flex;
        align-items: center;
        img {
          width: 32px;
          height: 32px;
          margin-right: 15px;
        }
        &Flex {
          display: flex;
          flex-direction: column;
        }
        span {
          font-size: 14px;
        }
        em {
          margin-top: 5px;
          font-style: normal;
          font-size: 12px;
          color: $blue;
        }
      }
    }
  }
  .basicInfoItem + .basicInfoItem {
    margin-left: 20px;
  }
  .viewMore {
    margin: 20px auto 0;
    display: table;
    font-size: 20px;
    line-height: 50px;
    text-align: center;
    cursor: pointer;
    padding: 0 100px;
    background-color: $blue;
    color: $white;
    border-radius: 10px;
    &:hover {
      opacity: 0.8;
    }
  }
}
::v-deep {
  .moreItem {
    display: flex;
    align-items: center;
    span {
      color: $blue;
      font-size: 14px;
      line-height: 24px;
    }
    em {
      display: inline-flex;
      justify-content: flex-start;
      white-space: nowrap;
      align-items: center;
      color: $blue;
      cursor: pointer;
      font-size: 12px;
      font-weight: 400;
      background-color: rgba(0, 132, 255, 0.05);
      border: 1px solid rgba(0, 132, 255, 0.2);
      line-height: 16px;
      margin-left: 8px;
      padding: 3px 6px;
      font-style: normal;
    }
  }
  .image-slot {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    background: #f5f7fa;
    color: #909399;
  }
  .el-form {
    .el-form-item__label {
      font-weight: normal;
      color: $disabled;
      text-align: left;
    }
  }
}
.company-contact {
  margin-top: 20px;
  .followList {
    border: 1px solid #e2e6f3;
    display: flex;
    flex-direction: column;
    .followItem {
      display: flex;
      padding: 5px 20px;
      border-bottom: 1px solid #e2e6f3;
      &:last-child {
        border-bottom: 0;
      }
      .followImg {
        width: 40px;
        height: 40px;
        border: 1px solid #f7f7f7;
        border-radius: 50%;
        flex-shrink: 0;
        margin-right: 10px;
      }
      .followInfo {
        width: 100%;
        display: flex;
        flex-direction: column;
        &-title {
          display: inline-block;
          width: 100%;
          line-height: 40px;
          color: $info;
        }
        &-con {
          font-weight: normal;
          line-height: 2em;
          color: $font;
        }
        &-file {
          display: inline-flex;
          align-items: center;
          flex-wrap: wrap;
          margin-top: 10px;
          &-img {
            width: 100px;
            height: 100px;
            margin-right: 10px;
            margin-bottom: 10px;
          }
        }
      }
    }
  }
}
</style>
