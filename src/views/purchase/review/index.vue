<template>
  <div class="newBox bgcf9 vh-85">
    <!-- 搜索 -->
    <div class="custom-search flex" v-if="showSearch">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" @submit.native.prevent>
        <el-form-item label="名称" prop="category">
          <el-input v-model="queryParams.category" placeholder="请输入名称" clearable @keyup.enter.native="handleQuery" size="small" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="tableBox">
      <!-- 收藏管理 -->
      <collect-tpl ref="collect" :hasCollect="false" :isSearch="true" :search="true" :showSearch.sync="showSearch" @queryTable="getList" :columns="columns" class="collectTpl" type="UserPriProduct" />

      <template v-if="total > 0">
        <el-table v-loading="loading" ref="table" stripe :data="list" row-key="id" :key="key" style="width: 100%" class="custom-table custom-table-cell5">
          <el-table-column align="center" type="index" label="序号" v-if="columns[0].visible"></el-table-column>
          <el-table-column align="center" prop="productName" label="产品名称" show-overflow-tooltip v-if="columns[1].visible">
            <template slot-scope="{ row }">
              <span class="table-link pointer" @click="handleDetail(row)">{{ row.productName }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="productCode" label="产品编码" show-overflow-tooltip v-if="columns[2].visible">
            <template slot-scope="{ row }">{{ row.productCode }}</template>
          </el-table-column>
          <el-table-column align="center" prop="picture1" label="图片" width="75" v-if="columns[3].visible">
            <template slot-scope="{ row }">
              <el-image :src="formatProductImg(row)" fit="cover" @click="handleImg(row)">
                <div slot="error" class="image-slot">
                  <i class="el-icon-picture-outline"></i>
                </div>
              </el-image>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="specs" label="规格" show-overflow-tooltip v-if="columns[4].visible">
            <template slot-scope="{ row }">{{ row.specs }}</template>
          </el-table-column>
          <el-table-column align="center" prop="model" label="型号" show-overflow-tooltip v-if="columns[5].visible">
            <template slot-scope="{ row }">{{ row.model }}</template>
          </el-table-column>
          <el-table-column align="center" prop="unit" label="单位" show-overflow-tooltip v-if="columns[6].visible">
            <template slot-scope="{ row }">{{ row.unit }}</template>
          </el-table-column>
          <el-table-column align="center" prop="weight" label="重量" show-overflow-tooltip v-if="columns[7].visible">
            <template slot-scope="{ row }">{{ row.weight }}</template>
          </el-table-column>
          <el-table-column align="center" prop="industry" label="行业分类" show-overflow-tooltip v-if="columns[8].visible">
            <template slot-scope="{ row }">{{ row.industry }}</template>
          </el-table-column>
          <el-table-column align="center" prop="materialQuality" label="材质" width="80" show-overflow-tooltip v-if="columns[9].visible">
            <template slot-scope="{ row }">{{ row.materialQuality }}</template>
          </el-table-column>
          <el-table-column align="center" prop="surface" label="表面" show-overflow-tooltip v-if="columns[10].visible">
            <template slot-scope="{ row }">{{ row.surface }}</template>
          </el-table-column>
          <el-table-column align="center" prop="createTime" label="创建时间" show-overflow-tooltip v-if="columns[11].visible"></el-table-column>
          <el-table-column align="center" label="操作" min-width="110" v-if="checkPermi(['system:verify:prepare'])">
            <template slot-scope="{ row }">
              <el-button type="text" icon="el-icon-check" @click="handlePass(row)" size="small">通过</el-button>
              <el-button type="text" icon="el-icon-close" @click="handleReject(row)" size="small">驳回</el-button>
            </template>
          </el-table-column>
        </el-table>

        <div class="custom-pagination">
          <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
        </div>
      </template>
      <el-empty :image-size="200" v-else />
    </div>

    <!-- 产品详情 -->
    <product-dialog ref="productInfo"></product-dialog>

    <!-- 审核 -->
    <el-dialog v-dialogDragBox title="审核" :visible.sync="open" width="500px" class="custom-dialog">
      <div class="formBox">
        <el-form ref="form" :model="form" :rules="rules" label-width="80px">
          <el-form-item label="类目名称">
            {{ form.categoryName }}
          </el-form-item>
<!--          <el-form-item label="产品编码" prop="productCode">-->
<!--            <el-input v-model="form.productCode" placeholder="请输入产品编码" />-->
<!--          </el-form-item>-->
          <el-form-item label="审核原因" prop="reason">
            <el-input type="textarea" :autosize="{ minRows: 3, maxRows: 5 }" resize="none" v-model="form.reason" placeholder="请输入审核原因" />
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer">
        <button type="button" class="custom-dialog-btn" @click="open = false">取消</button>
        <button type="button" class="custom-dialog-btn primary" @click="handleSumit">通过审核</button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { auditCategoryList, auditCategoryDetail, auditCategoryAdd } from '@/api/purchase/review'
import { checkPermi } from '@/utils/permission'
import CollectTpl from '@/views/components/collect/index'
import ProductDialog from '@/views/public/product/dialog'

export default {
  components: { ProductDialog, CollectTpl },
  data() {
    return {
      showSearch: true,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        category: undefined,
        status: undefined
      },
      loading: true,
      list: [],
      key: 1,
      total: 0,
      // 显隐列
      columns: [
        { key: 0, label: `序号`, visible: true },
        { key: 1, label: `产品名称`, visible: true },
        { key: 2, label: `产品编码`, visible: true },
        { key: 3, label: `图片`, visible: true },
        { key: 4, label: `规格`, visible: true },
        { key: 5, label: `型号`, visible: true },
        { key: 6, label: `单位`, visible: true },
        { key: 7, label: `重量`, visible: true },
        { key: 8, label: `行业分类`, visible: true },
        { key: 9, label: `材质`, visible: true },
        { key: 10, label: `表面`, visible: true },
        { key: 11, label: `创建时间`, visible: true }
      ],
      // 审核详情
      open: false,
      form: {},
      rules: {
        productCode: [{ required: true, message: '请输入产品编码', trigger: 'blur' }]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    checkPermi,
    // 列表
    getList() {
      this.loading = true
      auditCategoryList(this.queryParams).then(res => {
        if (res.code === 200) {
          this.list = res.rows
          this.total = res.total
          this.key = Math.random()
          this.loading = false
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 重置搜索
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 产品详情
    handleDetail(item) {
      this.$refs.productInfo.handleView(item)
    },
    // 图片预览
    handleImg(row) {
      this.$refs.productInfo.handleImgView(row)
    },
    // 表单重置
    reset() {
      this.form = {
        categoryId: undefined,
        companyName: undefined,
        id: undefined,
        productCode: undefined,
        reason: undefined,
        status: undefined
      }
      this.resetForm('form')
    },
    // 审核
    handlePass(row) {
      this.reset()
      const prepareId = row.id
      auditCategoryDetail({ prepareId }).then(res => {
        if (res.code === 200) {
          this.form = res.data
          this.open = true
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 通过
    handleSumit() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          const data = {
            categoryId: this.form.categoryId,
            id: this.form.id,
            productCode: this.form.productCode,
            reason: this.form.reason,
            status: 1
          }
          auditCategoryAdd(this.form).then(res => {
            if (res.code === 200) {
              this.$message.success('审核成功')
              this.getList()
              this.open = false
            } else {
              this.$message.error(res.msg)
            }
          })
        }
      })
    },

    // 拒绝
    // prettier-ignore
    handleReject(row) {
      const data = { id: row.id, status: 2 }
      this.$confirm('是否驳回审核?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        auditCategoryAdd(data).then(res => {
          if (res.code === 200) {
            this.$message.success('驳回成功')
            this.getList()
          } else {
            this.$message.error(res.msg)
          }
        })
      }).catch(() => {
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
.custom-search {
  ::v-deep {
    .el-form-item--small.el-form-item {
      margin-bottom: 0;
    }
  }
}
.tableBox {
  padding: 10px 20px 20px;
}
.collectTpl {
  margin-bottom: 0;
  ::v-deep {
    .collect {
      margin-bottom: 0;
      background-color: transparent;
      border-color: transparent;
    }
  }
}
.custom-dialog ::v-deep {
  .formBox {
    padding: 10px 20px;
  }
  .custom-dialog-btn {
    width: 150px;
  }
  .el-textarea {
    textarea {
      font-family: inherit;
    }
  }
}
</style>
