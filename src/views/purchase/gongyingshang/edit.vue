<template>
  <div>
    <el-dialog v-dialogDragBox title="新增供应商" :visible.sync="dialogVisible" v-loading="loading" width="60%">
      <div>
        <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm">
          <el-form-item label="公司名称" prop="name">
            <el-input v-model="ruleForm.name"></el-input>
          </el-form-item>
          <el-form-item label="负责人电话" prop="phone">
            <el-input v-model="ruleForm.phone"></el-input>
          </el-form-item>
          <!--          <el-form-item label="经度" prop="lng">-->
          <!--            <el-input v-model="ruleForm.lng"></el-input>-->
          <!--          </el-form-item>-->
          <!--          <el-form-item label="纬度" prop="lat">-->
          <!--            <el-input v-model="ruleForm.lat"></el-input>-->
          <!--          </el-form-item>-->
          <el-form-item label="供应商类型" prop="categoryIds">
            <!--            <el-input v-model="ruleForm.categoryIds"></el-input>-->
            <!--            v-model="ruleForm.categoryIds"-->
            <treeselect :normalizer="normalizerTree" :autoSelectDescendants="true" ref="indicatorTree" node-key="id"
              multiple :flat="true" :value="ruleForm.categoryIds" v-model="ruleForm.categoryIds" :options="tableData"
              :show-count="true" placeholder="请选择类型"     style="z-index: 99999;" />
          </el-form-item>
          <el-form-item label="资质文件" prop="certificationUrl">
            <!--            <el-input v-model="ruleForm.certificationUrl"></el-input>-->

            <el-upload :disabled="ruleForm.certificationUrl.length == 1 ? true : false" class="upload-demo"
                       :action="up_file + '/common/upload'" name="file" :before-upload="handleBeforeUpload" :on-progress="upfile" :on-success="handcgb"
                       :show-file-list="false" :headers="headers" multiple>
              <el-button :disabled="ruleForm.certificationUrl.length == 1 ? true : false" size="small" type="primary">
                点击上传
              </el-button>
              <div style="font-size: 12px;color: #666">
                上传pdf/图片
              </div>
            </el-upload>
            <div class="" v-for="(item, index) in ruleForm.certificationUrl">
              <span @click="show_img(item.filePath)" style="cursor:pointer;">
                {{ item.name }}
              </span>
              <el-button type="text" @click="delb(index)">删除</el-button>
            </div>


          </el-form-item>

          <el-form-item label="公司地址" prop="address">
            <el-input v-model="ruleForm.address"></el-input>
          </el-form-item>
          <el-form-item label="logo图片" prop="logoUrl">
            <el-upload :disabled="ruleForm.logoUrl.length == 1 ? true : false" class="upload-demo"
              :action="up_file + '/common/upload'" name="file" :before-upload="handleBeforeUpload" :on-progress="upfile" :on-success="handcg"
              :show-file-list="false" :headers="headers" multiple>
              <el-button :disabled="ruleForm.logoUrl.length == 1 ? true : false" size="small" type="primary">
                点击上传
              </el-button>
            </el-upload>
            <div class="" v-for="(item, index) in ruleForm.logoUrl">
              <span @click="show_img(item.filePath)" style="cursor:pointer;">
                {{ item.name }}
              </span>
              <el-button type="text" @click="dela(index)">删除</el-button>
            </div>
            <!--            <el-input v-model="ruleForm.photoAddr"></el-input>-->
          </el-form-item>


          <el-form-item label="公司标语" >
            <el-input v-model="ruleForm.slogan"></el-input>
<!--            <image-upload v-model="ruleForm.slogan"/>-->
          </el-form-item>
          <el-form-item label="场地图片" >
            <image-upload v-model="ruleForm.siteImg"/>
          </el-form-item>
          <el-form-item label="荣誉证书" >
            <image-upload v-model="ruleForm.certImg"/>
          </el-form-item>
          <el-form-item label="设备图片" >
            <image-upload v-model="ruleForm.deviceImg"/>
          </el-form-item>
          <el-form-item label="工艺图片/视频" >
            <image-upload :fileSize="20" :fileType="['png', 'jpg', 'jpeg','mp4']" v-model="ruleForm.process"/>
          </el-form-item>

          <el-form-item label="公司简介" prop="fuweb">
            <div style="height: 500px;">
              <div id="editorContainerb"></div>
            </div>
          </el-form-item>



          <el-form-item>
            <el-button type="primary" @click="submitForm('ruleForm')">修改</el-button>
          </el-form-item>
        </el-form>
      </div>


      <!--      <span slot="footer" class="dialog-footer">-->
      <!--    <el-button @click="dialogVisible = false">取 消</el-button>-->
      <!--    <el-button type="primary" @click="dialogVisible = false">确 定</el-button>-->
      <!--  </span>-->
    </el-dialog>


    <el-dialog v-dialogDragBox title="" :visible.sync="centerDialogVisible" width="50%" center>
      <img :src="up_file + imgfile" style="width: 100%;height: 100%" alt="" />
      <span slot="footer" class="dialog-footer">
        <el-button @click="centerDialogVisible = false">取 消</el-button>
      </span>
    </el-dialog>


  </div>
</template>

<script>
import Cookies from "js-cookie";
import { getlistb } from "@/api/purchase/category";
import { editlist, searchText, addText } from "@/api/purchase/gongyingshang";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

import E from "wangeditor";
import { getToken } from "@/utils/auth";

export default {
  name: 'add',
  components: {
    Treeselect
  },
  data() {
    return {
      dialogVisible: false,
      tableData: [],
      ruleForm: {
        name: '',
        phone: '',
        logoUrl: [],
        lat: '',
        lng: '',
        certificationUrl: [],
        categoryIds: [],
        address: '',

        slogan: '',
        siteImg: '',
        certImg: '',
        deviceImg: '',
        process: '',
      },
      rules: {
        name: [
          { required: true, message: '请输入类目名称', trigger: 'blur' },
        ],
        phone: [
          { required: true, message: '请输入联系人电话', trigger: 'blur' }
        ],
        // logoUrl: [
        //   { required: false, message: '请输入联系人电话', trigger: 'change' }
        // ],
        certificationUrl: [
          { required: true, message: '请上传资质文件', trigger: 'change' }
        ],
        categoryIds: [
          { required: true, message: '请选择供应商类型', trigger: 'change' }
        ],
        address: [
          { required: true, message: '请输入地址', trigger: 'blur' }
        ],
      },
      up_file: '',
      headers: {},
      loading: false,
      imgfile: '',
      centerDialogVisible: false,
      onedata: {},

      editor: null,


      editor_num: 1,

      edti_html: '',

    }
  },
  created() {
    this.up_file = process.env.VUE_APP_BASE_API;
    let token = Cookies.get("Admin-Token");
    this.headers = {
      Authorization: "Bearer " + token,
    };
    this.ready();
  },
  methods: {
    // 自定义树配置
    normalizerTree(node) {
      return {
        children: node.children,
        label: node.name,
        id: node.id
      }
    },
    reTreeValue(value) {
      return value?.split(',') || undefined
    },
    ready() {
      getlistb({}).then((res) => {
        let lista = res.data;
        this.tableData = lista;
      });
    },
    upfile() {
      this.loading = true;
    },
    show_img(e) {
      if (e) {
        this.imgfile = e;
        this.centerDialogVisible = true;
      }

    },
    handleBeforeUpload(file) {
      if (!getToken()) {
        this.$message.error(`请先登录`);
        return false
      }
    },
    //上传成功
    handcg(file, fileList) {
      this.ruleForm.logoUrl.push({
        name: file.newFileName,
        filePath: file.fileName,
      });
      this.loading = false;
    },
    //删除上传文件
    dela(e) {
      this.ruleForm.logoUrl.splice(e, 1);
    },

    handcgb(file, fileList) {
      this.ruleForm.certificationUrl.push({
        name: file.newFileName,
        filePath: file.fileName,
      });
      this.loading = false;
    },
    //删除上传文件
    delb(e) {
      this.ruleForm.certificationUrl.splice(e, 1);
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.add();
        } else {
          return false;
        }
      });
    },
    add() {
      let that = this;
      // let parentId =  this.ruleForm.parentIdb;
      let datalist = {
        id: this.onedata.id,
        name: this.ruleForm.name,
        phone: this.ruleForm.phone,
        lat: this.ruleForm.lat,
        lng: this.ruleForm.lng,
        certificationUrl: this.ruleForm.certificationUrl[0]['filePath'],
        categoryIds: this.ruleForm.categoryIds,
        address: this.ruleForm.address,
        logoUrl: this.ruleForm.logoUrl[0]['filePath'],
        slogan: this.ruleForm.slogan,
        siteImg: this.ruleForm.siteImg,
        certImg: this.ruleForm.certImg,
        deviceImg: this.ruleForm.deviceImg,
        process: this.ruleForm.process,
      }


      addText({objectId:this.onedata.id,type:'sygys',textArea:this.editor.txt.html()}).then((res) => {
          });

      editlist(datalist).then((res) => {
        this.$message({
          message: '提交成功',
          type: 'success'
        });
        this.dialogVisible = false;
        this.$emit('shuaxin')
      });
    },
    showmodel(e) {
      let that = this;
      this.ready();
      this.onedata = e;
      let lista = JSON.parse(JSON.stringify(e));

      lista['certificationUrl'] = [];
      lista['certificationUrl'][0] = {
        name: e['certificationUrl'],
        filePath: e['certificationUrl'],
      };
      lista['logoUrl'] = [];
      lista['logoUrl'][0] = {
        name: e['logoUrl'],
        filePath: e['logoUrl'],
      };
      // this.ruleForm.categoryIds.split(','),
      lista['categoryIds'] = lista['categoryId'].split(',');
      this.ruleForm = lista;
      this.dialogVisible = true;



      // //查询供应商
      // searchText({ objectId: this.onedata.id, type: 'sygys' }).then((res) => {
      //   // this.mapinit(res.data.textArea)
      //   // that.editor.txt.html(row) // 重新设置编辑器内容

      //   if(res.data.textArea) {
      //     this.edti_html = res.data.textArea;
      //     setTimeout(function () {
      //       that.mapinit();
      //     }, 100)
      //   } else {
      //     this.edti_html = '';
      //     setTimeout(function () {
      //       that.mapinit();
      //     }, 100)
      //   }
      // });
    },
    mapinit() {
      let that = this;
      if (that.editor_num == 1) {
        const editorElement = document.getElementById('editorContainerb');
        // 销毁旧的编辑器实例
        if (editorElement.editor) {
          editorElement.editor.destroy();
        }
        // 创建富文本编辑器实例
        const editor = new E('#editorContainerb');
        // 自定义配置
        editor.config.height = 400;
        // 配置全屏功能按钮是否展示
        editor.config.showFullScreen = false;
        editor.config.uploadImgShowBase64 = true

        // // 配置 server 接口地址
        // editor.config.uploadImgServer = this.up_file + '/common/upload';
        //
        // // editor.config.uploadImgMaxSize = 2 * 1024 * 1024 // 2M
        // editor.config.uploadImgAccept = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp']
        // editor.config.uploadImgMaxLength = 5;
        //
        // // editor.config.uploadImgParams = {
        // //   token: 'xxxxx',
        // //   x: 100
        // // }
        // editor.config.uploadFileName = 'file'
        // editor.config.uploadImgHeaders = this.headers;
        // editor.config.withCredentials = true;
        //
        // editor.config.uploadImgHooks = {
        //   customInsert: function(insertImgFn, result) {
        //     // result 即服务端返回的接口
        //     // result.fileName
        //     insertImgFn(result.fileName)
        //
        //     // insertImgFn 可把图片插入到编辑器，传入图片 src ，执行函数即可
        //     // insertImgFn(result.data[0])
        //   }
        // }

        // 自定义配置
        editor.config.menus = [
          'head',
          'bold',
          'fontSize',
          'fontName',
          'strikeThrough',
          'foreColor',
          'backColor',
          'link',
          'list',
          'justify',
          'italic',
          'quote',
          'underline',
          'justify',
          'quote',
          'undo',
          'redo',
          'image',
          'video',
          'table',
          'hr',
          'preview',
          // 'fullscreen',
          'print',
          'emoticon',
          'outline'
        ];
        // 初始化富文本编辑器
        editor.create();
        // 将编辑器实例保存到组件的变量中
        this.editor = editor;

        that.editor_num = 2;
        editor.txt.html(this.edti_html)
      } else {
        this.editor.txt.html(this.edti_html)
      }
      // editor.txt.html(row) // 重新设置编辑器内容
    },
    handleChange(value) {
    }
  }
}
</script>

<style scoped></style>
