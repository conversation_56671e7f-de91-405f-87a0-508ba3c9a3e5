<template>
  <div class="bodya">
    <el-row>
      <el-col :span="4">
        <el-input v-model="queryParams.name" size="small" placeholder="请输入名称"></el-input>
      </el-col>
      <el-col :span="4">
        <el-select clearable size="small" style="width: 100%" v-model="queryParams.status" placeholder="请选择类型">
          <el-option
            v-for="item in audit_status"
            :key="item.dictValue"
            :label="item.dictLabel"
            :value="item.dictValue">
          </el-option>
        </el-select>
      </el-col>
      <el-col :span="8">
        <el-button size="small" @click="ready()" style="margin-left: 10px;" type="primary">
          搜索
        </el-button>
        <el-button size="small" @click="cz()" style="margin-left: 10px;" type="">
          重置
        </el-button>
      </el-col>
    </el-row>

    <el-table :data="tableData" border size="small" style="width: 100%;margin-top: 10px;">
      <el-table-column
        align="center"
        label="#"
        type="index">
      </el-table-column>
      <el-table-column prop="name" label="供应商名称" align="center" show-overflow-tooltip></el-table-column>

      <el-table-column prop="phone" label="联系人电话" align="center" show-overflow-tooltip></el-table-column>
      <el-table-column prop="logoUrl" label="logo" align="center" show-overflow-tooltip>
        <template slot-scope="scope">
          <el-image
            style="width:50px; height: 50px"
            :src="up_file + scope.row.logoUrl"
            @click="xzimg(up_file+scope.row.logoUrl)"
            :preview-src-list="srcList">
          </el-image>
          <!--          <img :src="" style="width:100px;height:100px"  alt="">-->
        </template>
      </el-table-column>
      <el-table-column prop="slogan" label="公司标语" align="center" show-overflow-tooltip></el-table-column>


<!--      <el-table-column prop="phone" label="经度,纬度" align="center" show-overflow-tooltip>-->
<!--        <template slot-scope="scope">-->
<!--          {{ scope.row.lng }},-->
<!--          {{ scope.row.lat }}-->
<!--        </template>-->
<!--      </el-table-column>-->

      <el-table-column prop="certificationUrl" label="资质文件" align="center" show-overflow-tooltip>
        <template slot-scope="scope">
          <template v-if="scope.row.certificationUrl.length > 0">
            <el-button size="small" type="primary" @click="xiaz(scope.row.certificationUrl)">
              下载
            </el-button>
          </template>
          <template v-if="scope.row.certificationUrl.length == 0">
            暂无文件
          </template>
        </template>
      </el-table-column>

      <el-table-column prop="categoryId" label="供应商类型" align="center">
        <template slot-scope="scope">
          <treeselect
            :normalizer="normalizerTree"
            :autoSelectDescendants="true"
            ref="indicatorTree"
            node-key="id"
            multiple
            disabled
            :clearable="false"
            :flat="true"
            :value="reTreeValue(scope.row.categoryId)"
            :options="tableDatab"
            :show-count="true"
            placeholder="请选择类型"
          />

        </template>
      </el-table-column>
      <el-table-column prop="address" label="地址" align="center" show-overflow-tooltip>
      </el-table-column>
      <el-table-column prop="reason" label="驳回原因" align="center" show-overflow-tooltip>
      </el-table-column>




      <el-table-column prop="createTime" label="申请时间" align="center" show-overflow-tooltip></el-table-column>
      <el-table-column prop="status" label="状态" align="center" show-overflow-tooltip>
        <template slot-scope="scope">
          <el-tag :type="scope.row.statusa">
            {{ shaixuan(audit_status, scope.row.auditStatus, 'dictValue', 'dictLabel', scope.$index) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column fixed="right" align="center" label="操作" width="120">
        <template slot-scope="scope">
          <template v-if="scope.row.auditStatus == 0">
            <el-button type="text" @click="tongguo(scope.row)" size="small">
              通过
            </el-button>
            <el-button type="text" @click="jujue(scope.row)" size="small">
              驳回
            </el-button>
          </template>
        </template>
      </el-table-column>
    </el-table>


    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="ready"/>


  </div>
</template>

<script>
import {getlist, sh, editlist} from "@/api/purchase/gongyingshangSH";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import {getlistb} from "@/api/purchase/category";


export default {
  name: 'index',
  components: {
    Treeselect
  },
  data() {
    return {
      tableData: [],
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: '',
        status: '',
      },
      audit_status: [],
      listClass: '',

      tableDatab: [],

      up_file: '',
      imgurl: '',
      srcList: [
        'https://fuss10.elemecdn.com/8/27/f01c15bb73e1ef3793e64e6b7bbccjpeg.jpeg',
      ],
    };
  },
  created() {
    this.up_file = process.env.VUE_APP_BASE_API;
    this.getDicts("audit_status").then((response) => {
      this.audit_status = response.data;
    });
    this.ready();
  },
  methods: {
    // 自定义树配置
    normalizerTree(node) {
      return {
        children: node.children,
        label: node.name,
        id: node.id
      }
    },
    xzimg(e) {
      this.srcList[0] = e;
    },
    ready() {
      getlistb({}).then((res) => {
        let lista = res.data;
        this.tableDatab = lista;
      });

      getlist(this.queryParams).then((res) => {
        let lista = res.rows;

        lista.forEach((item, index) => {
          // let aa =  this.shaixuan(this.audit_status,item.status,'dictValue','dictLabel');
          let bb = this.shaixuanb(this.audit_status, item.auditStatus, 'dictValue', 'dictLabel');
          lista[index]['statusa'] = bb;

        })

        this.tableData = lista;
        this.total = res.total;
      });
    },
    reTreeValue(value) {
      return value?.split(',') || undefined
    },
    cz() {
      this.queryParams.name = '';
      this.queryParams.status = '';
      this.ready();
    },
    shaixuan(data, value, name, retname, indexa) {
      for (let i = 0; i < data.length; i++) {
        let item = data[i];
        if (item[name] == value) {
          return item[retname];
        }
      }
    },
    shaixuanb(data, value, name, retname, indexa) {
      for (let i = 0; i < data.length; i++) {
        let item = data[i];
        if (item[name] == value) {
          return item['listClass'];
        }
      }
    },

    tongguo(row) {
      this.$confirm('是否通过[' + row.name + ']审核?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        //  通过
        let datalist = {
          id: row.id,
          status: 1,
        }
        sh(datalist).then((res) => {
          this.$message({
            type: 'success',
            message: '操作成功!'
          });
          this.ready();
        });
      }).catch(() => {
      });
    },
    jujue(row) {
      this.$confirm('是否驳回[' + row.name + ']审核?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$prompt('请输入驳回原因', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
        }).then(({value}) => {
          if (!value) {
            this.$message({
              type: 'info',
              message: '请输入驳回原因'
            });
            return;
          }
          //驳回
          let datalist = {
            id: row.id,
            status: 2,
            reason: value,
          }
          sh(datalist).then((res) => {
            this.$message({
              type: 'success',
              message: '操作成功!'
            });
            this.ready();
          });
        }).catch(() => {
        });
      }).catch(() => {
      });
    },

    xiaz(e) {
      window.open(this.up_file + e);
    }


  }
}
</script>

<style scoped>
.bodya {
  padding: 20px;
}

.lab1 {
  text-align: right;
  height: 30px;
  line-height: 30px;
  padding-right: 10px;
}
</style>
