<template>
  <div>

    <el-dialog
      title="新增供应商"
      :visible.sync="dialogVisible"
      v-loading="loading"
      width="60%">
      <div>
        <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm">
          <el-form-item label="公司名称" prop="name">
            <el-input v-model="ruleForm.name"></el-input>
          </el-form-item>
          <el-form-item label="负责人电话" prop="phone">
            <el-input v-model="ruleForm.phone"></el-input>
          </el-form-item>
<!--          <el-form-item label="地点搜索">-->
<!--            <el-input v-model="query" placeholder="输入地名"></el-input>-->
            <!-- <el-button type="primary" @click="searchPlace">搜索</el-button> -->
<!--          </el-form-item>-->
          <el-form-item label="公司地址" prop="address">
            <el-input v-model="ruleForm.address">
<!--              <el-button @click="dw()" slot="append" icon="el-icon-s-promotion">-->
<!--                定位-->
<!--              </el-button>-->
            </el-input>

          </el-form-item>

<!--          <el-row v-if="dwshow">-->
<!--          <el-col :span="12">-->
<!--            <el-form-item label="经度" prop="lng">-->
<!--              <el-input v-model="ruleForm.lng"></el-input>-->
<!--            </el-form-item>-->
<!--          </el-col>-->
<!--            <el-col :span="12">-->
<!--            <el-form-item label="纬度" prop="lat">-->
<!--              <el-input v-model="ruleForm.lat"></el-input>-->
<!--            </el-form-item>-->
<!--            </el-col>-->
<!--          </el-row>-->

          <el-form-item  v-if="dwshow" label="地图">
             <div style="height: 400px;">
               <baidu-map ref="map" class="bm-view"  :center="center" :zoom="zoom" :scroll-wheel-zoom="true" @ready="handler">
                 <bm-navigation anchor="BMAP_ANCHOR_TOP_RIGHT" />
                  <bm-local-search @onSearchComplete="handleSearchComplete" :keyword="keyword" :location="location" :panel="false" :auto-viewport="true" />
                  <bm-view style="width: 100%; height:400px; flex: 1"></bm-view>

                 <bm-geolocation anchor="BMAP_ANCHOR_BOTTOM_RIGHT" :showAddressBar="true"   :autoLocation="true">
                 </bm-geolocation>
                 <bm-marker :position="{lng: center.lng, lat: center.lat}" :dragging="true" animation="BMAP_ANIMATION_BOUNCE" />
               </baidu-map>
             </div>
          </el-form-item>
<!--          <el-form-item label="经度" prop="lng" >-->
<!--            <el-input v-model="ruleForm.lng"></el-input>-->
<!--          </el-form-item>-->
<!--          <el-form-item label="纬度" prop="lat" >-->
<!--            <el-input v-model="ruleForm.lat"></el-input>-->
<!--          </el-form-item>-->


          <el-form-item label="供应商类型" prop="categoryIds">
            <!--            <el-input v-model="ruleForm.categoryIds"></el-input>-->
            <treeselect
              :normalizer="normalizerTree"
              :autoSelectDescendants="true"
              ref="indicatorTree"
              node-key="id"
              multiple
              :flat="true"
              v-model="ruleForm.categoryIds"
              :options="tableData"
              :show-count="true"
              placeholder="请选择类型"
              style="z-index: 9999"
            />
          </el-form-item>
          <el-form-item label="资质文件" prop="certificationUrl">
            <!--            <el-input v-model="ruleForm.certificationUrl"></el-input>-->

            <el-upload
              :disabled="ruleForm.certificationUrl.length == 1 ? true : false"
              class="upload-demo"
              :action="up_file + '/common/upload'"
              name="file"
              :before-upload="handleBeforeUpload"
              :on-progress="upfile"
              :on-success="handcgb"
              :show-file-list="false"
              :headers="headers"
              multiple>
              <el-button :disabled="ruleForm.certificationUrl.length == 1 ? true : false" size="small" type="primary">
                点击上传
              </el-button>
            </el-upload>
            <div class="" v-for="(item,index) in ruleForm.certificationUrl">
                <span @click="show_img(item.filePath)" style="cursor:pointer;">
                  {{ item.name }}
                </span>
              <el-button type="text" @click="delb(index)">删除</el-button>
            </div>
          </el-form-item>


          <el-form-item label="logo图片" prop="logoUrl">
            <el-upload
              :disabled="ruleForm.logoUrl.length == 1 ? true : false"
              class="upload-demo"
              :action="up_file + '/common/upload'"
              name="file"
              :before-upload="handleBeforeUpload"
              :on-progress="upfile"
              :on-success="handcg"
              :show-file-list="false"
              :headers="headers"
              multiple>
              <el-button :disabled="ruleForm.logoUrl.length == 1 ? true : false" size="small" type="primary">
                点击上传
              </el-button>
              <div style="font-size: 12px;color: #666">
                上传pdf/图片
              </div>
            </el-upload>
            <div class="" v-for="(item,index) in ruleForm.logoUrl">
                <span @click="show_img(item.filePath)" style="cursor:pointer;">
                  {{ item.name }}
                </span>
              <el-button type="text" @click="dela(index)">删除</el-button>
            </div>
            <!--            <el-input v-model="ruleForm.photoAddr"></el-input>-->
          </el-form-item>


          <el-form-item label="公司标语" >
<!--            <image-upload v-model="ruleForm.slogan"/>-->
            <el-input v-model="ruleForm.slogan"></el-input>
          </el-form-item>
          <el-form-item label="场地图片" >
            <image-upload v-model="ruleForm.siteImg"/>
          </el-form-item>
          <el-form-item label="荣誉证书" >
            <image-upload v-model="ruleForm.certImg"/>
          </el-form-item>
          <el-form-item label="设备图片" >
            <image-upload v-model="ruleForm.deviceImg"/>
          </el-form-item>
          <el-form-item label="工艺图片/视频" >
            <image-upload :fileSize="20" :fileType="['png', 'jpg', 'jpeg','mp4']" v-model="ruleForm.process"/>
          </el-form-item>

          <el-form-item label="公司简介" >
            <div style="height: 500px;">
              <div id="editorContainer"></div>
            </div>
          </el-form-item>


          <el-form-item>
            <el-button type="primary" @click="submitForm('ruleForm')">立即创建</el-button>
          </el-form-item>
        </el-form>
      </div>


      <!--      <span slot="footer" class="dialog-footer">-->
      <!--    <el-button @click="dialogVisible = false">取 消</el-button>-->
      <!--    <el-button type="primary" @click="dialogVisible = false">确 定</el-button>-->
      <!--  </span>-->
    </el-dialog>


    <el-dialog v-dialogDragBox title="" :visible.sync="centerDialogVisible" width="50%" center>
      <img :src="up_file+imgfile" style="width: 100%;height: 100%" alt=""/>
      <span slot="footer" class="dialog-footer">
        <el-button @click="centerDialogVisible = false">取 消</el-button>
      </span>
    </el-dialog>


  </div>
</template>

<script>
import Cookies from "js-cookie";
import {getlistb} from "@/api/purchase/category";
import {addlist,addText} from "@/api/purchase/gongyingshang";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

import BmLocalSearch from 'vue-baidu-map/components/search/LocalSearch.vue'
import E from "wangeditor";
import { getToken } from "@/utils/auth";


export default {
  name: 'add',
  components: {
    Treeselect,
    BmLocalSearch
  },
  data() {
    return {

      map: null,
      marker:null,
      search: null,
      query: '',
      dialogVisible: false,
      // center: [116.397428, 39.90923], // 地图中心点坐标
      markerPosition: [116.397428, 39.90923], // 标记点坐标
      tableData: [],
      ruleForm: {
        name: '',
        phone: '',
        logoUrl: [],
        lat: '',
        lng: '',
        certificationUrl: [],
        categoryIds: [],
        address: '',
        slogan: '',
          siteImg: '',
        certImg: '',
          deviceImg: '',
        process: '',

      },
      rules: {
        name: [
          {required: true, message: '请输入类目名称', trigger: 'blur'},
        ],
        phone: [
          {required: true, message: '请输入联系人电话', trigger: 'blur'}
        ],
        // logoUrl: [
        //   { required: false, message: '请输入联系人电话', trigger: 'change' }
        // ],
        certificationUrl: [
          {required: true, message: '请上传资质文件', trigger: 'change'}
        ],
        categoryIds: [
          {required: true, message: '请选择供应商类型', trigger: 'change'}
        ],
        address: [
          {required: true, message: '请输入地址', trigger: 'blur'}
        ],
      },
      up_file: '',
      headers: {},
      loading: false,
      imgfile: '',
      centerDialogVisible: false,
      ak: 'q5wRzbxZbdpCLrxOcgCWXN1QnKYGnHm2',
      center: { lng:  116.404, lat: 39.915},
      zoom: 22,


      keyword: '',
      location: '',
      BMap: null,
      dwshow: false,



      editor: null,


    }
  },
  created() {
    this.up_file = process.env.VUE_APP_BASE_API;
    let token = Cookies.get("Admin-Token");
    this.headers = {
      Authorization: "Bearer " + token,
    };
    this.ready();

  },
  mounted(){

    // this.initAMap()
  },
  methods: {
    handleSearchComplete(results){

    },
    dw() {
    this.keyword =  this.ruleForm.address;
    this.dwshow = true;
      // const geocoder = new BMap.Geocoder();
      // geocoder.getPoint(this.ruleForm.address, point => {
      //   if (point) {
      //     this.center = {
      //       lng: point.lng,
      //       lat: point.lat,
      //     };
      //   }
      // });

    },
    // 自定义树配置
    normalizerTree(node) {
      return {
        children: node.children,
        label: node.name,
        id: node.id
      }
    },
    reTreeValue(value) {
      return value?.split(',') || undefined
    },
    ready() {
      getlistb({}).then((res) => {
        let lista = res.data;
        this.tableData = lista;
      });
    },
    upfile() {
      this.loading = true;
    },
    show_img(e) {
      if (e) {
        this.imgfile = e;
        this.centerDialogVisible = true;
      }

    },
    handleBeforeUpload(file) {
      if (!getToken()) {
        this.$message.error(`请先登录`);
        return false
      }
    },
    //上传成功
    handcg(file, fileList) {
      this.ruleForm.logoUrl.push({
        name: file.newFileName,
        filePath: file.fileName,
      });
      this.loading = false;
    },
    //删除上传文件
    dela(e) {
      this.ruleForm.logoUrl.splice(e, 1);
    },

    handcgb(file, fileList) {
      this.ruleForm.certificationUrl.push({
        name: file.newFileName,
        filePath: file.fileName,
      });
      this.loading = false;
    },
    //删除上传文件
    delb(e) {
      this.ruleForm.certificationUrl.splice(e, 1);
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.add();
        } else {
          return false;
        }
      });
    },
    add() {
      let that = this;
      // 富文本
      // const content = this.editor.txt.html()
      // let parentId =  this.ruleForm.parentIdb;
      let datalist = {
        name: this.ruleForm.name,
        phone: this.ruleForm.phone,
        lat: this.ruleForm.lat,
        lng: this.ruleForm.lng,
        certificationUrl: this.ruleForm.certificationUrl[0]['filePath'],
        categoryIds: this.ruleForm.categoryIds,
        address: this.ruleForm.address,
        logoUrl: this.ruleForm.logoUrl[0]['filePath'],

        slogan: this.ruleForm.slogan,
        siteImg: this.ruleForm.siteImg,
        certImg: this.ruleForm.certImg,
        deviceImg: this.ruleForm.deviceImg,
        process: this.ruleForm.process,
      }
      addlist(datalist).then((res) => {
          // //添加富文本
          addText({objectId:res.data,type:'sygys',textArea:this.editor.txt.html()}).then((res) => {
          });
        this.$message({
          message: '提交成功',
          type: 'success'
        });
        this.dialogVisible = false;
        this.$emit('shuaxin')
      });
    },
    showmodel() {
      let that = this;
      this.ready();
      this.dialogVisible = true;
      setTimeout(function(){
        that.mapinit();
      }, 500)
    },
    mapinit(){
      const editorElement = document.getElementById('editorContainer');

      // 销毁旧的编辑器实例
      if (editorElement.editor) {
        editorElement.editor.destroy();
      }

      // 创建富文本编辑器实例
      const editor = new E('#editorContainer');

      // 自定义配置
      editor.config.height = 400;
      // 配置全屏功能按钮是否展示
      editor.config.showFullScreen = false;
      editor.config.uploadImgShowBase64 = true

      // 自定义配置
      editor.config.menus = [
        'head',
        'bold',
        'fontSize',
        'fontName',
        'strikeThrough',
        'foreColor',
        'backColor',
        'link',
        'list',
        'justify',
        'italic',
        'quote',
        'underline',
        'justify',
        'quote',
        'undo',
        'redo',
        'image',
        'video',
        'table',
        'hr',
        'preview',
        // 'fullscreen',
        'print',
        'emoticon',
        'outline'
      ];

      // 初始化富文本编辑器
      editor.create();
      // 将编辑器实例保存到组件的变量中
      this.editor = editor;
    },
    getContent() {
      // 获取编辑器的内容
      const content = this.editor.txt.html()
    },
    // 初始化
    handler({ BMap, map }) {
      this.map = map
      this.BMap = BMap
      // 初始经纬度
      this.center.lng = 116.404
      this.center.lat = 39.915
      // 缩放级别
      this.zoom = 22

      // 点击百度地图上的搜索出来的红色标记点
      map.addEventListener('click', ({ point }) => {
        this.center = point;
        this.ruleForm.lng =  point.lng;
        this.ruleForm.lat =  point.lat;
        // this.$emit('PositionDegrees', { lng: point.lng, lat: point.lat })
      })

      // // mapdata 有值更新定位
      // if (this.mapdata !== '' && this.mapdata !== undefined) {
      //   this.reset(this.mapdata)
      // }
    },
    // 重置（更新定位）
    reset(data) {
      this.keyword = ''
      this.center.lng = data.lng
      this.center.lat = data.lat
      this.zoom = 15
    },


  }
}
</script>

<style scoped>
/* #container {
  padding: 0px;
  margin: 0px;
  width: 100%;
  height: 300px;
} */

.map_address{
  height:300px;
}
.address-wrapper{
  display:flex;
  flex-direction:column;
}
.search-box{
  width:300px;
  height:40px;
}
.amap-demo{
  flex:1;
  height:300px;
}
   .bm-view {
     height: 400px;
     width: 100%;
     position: relative;
   }
   .control {
     position: absolute;
     top: 350px;
     left: 975px;
     border: 1px solid #000000;
     width: 30px;
     height: 30px;
     border-radius: 50%;
     cursor: pointer;
   }

#editorContainer {
  width: 100%;
  height: 450px;
  /*border: 1px solid #ccc;*/
}

</style>
