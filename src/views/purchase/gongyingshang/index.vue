<template>
  <div class="bodya">
    <el-row>
      <el-col :span="4">
        <el-input v-model="queryParams.name" size="small" placeholder="请输入名称"></el-input>
      </el-col>
      <el-col :span="4">
        <el-input v-model="queryParams.address" size="small" placeholder="请输入地址"></el-input>
      </el-col>
      <el-col :span="4">
        <el-input v-model="queryParams.phone" size="small" placeholder="请输入电话"></el-input>
      </el-col>
      <el-col :span="8">
        <el-button size="small" @click="ready()" style="margin-left: 10px;" type="primary">
          搜索
        </el-button>
        <el-button size="small" @click="cz()" style="margin-left: 10px;" type="">
          重置
        </el-button>
      </el-col>
    </el-row>


    <div class="head">
      <el-button @click="add()" type="primary" size="small" plain>
        新增
      </el-button>
    </div>


    <el-table :data="tableData" border
              row-key="id"
              :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
              size="small" style="width: 100%;margin-top: 10px;">

      <el-table-column
        align="center"
        label="#"
        type="index">
      </el-table-column>

      <el-table-column prop="name" label="供应商名称" align="center" show-overflow-tooltip></el-table-column>

      <el-table-column prop="slogan" label="公司标语" align="center" show-overflow-tooltip></el-table-column>
<!--      <el-table-column prop="site_img" label="场地图片" align="center" show-overflow-tooltip>-->
<!--        <template slot-scope="scope">-->
<!--          <el-image-->
<!--            style="width:50px; height: 50px"-->
<!--            :src="up_file + scope.row.logoUrl"-->
<!--            @click="xzimg(up_file+scope.row.logoUrl)"-->
<!--            :preview-src-list="srcList">-->
<!--          </el-image>-->
<!--          &lt;!&ndash;          <img :src="" style="width:100px;height:100px"  alt="">&ndash;&gt;-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column prop="cert_img" label="荣誉证书" align="center" show-overflow-tooltip></el-table-column>-->
<!--      <el-table-column prop="device_img" label="设备图片" align="center" show-overflow-tooltip></el-table-column>-->
<!--      <el-table-column prop="process" label="工艺图片" align="center" show-overflow-tooltip></el-table-column>-->






      <el-table-column prop="phone" label="联系人电话" align="center" show-overflow-tooltip></el-table-column>
      <el-table-column prop="logoUrl" label="logo" align="center" show-overflow-tooltip>
        <template slot-scope="scope">
          <el-image
            style="width:50px; height: 50px"
            :src="up_file + scope.row.logoUrl"
            @click="xzimg(up_file+scope.row.logoUrl)"
            :preview-src-list="srcList">
          </el-image>
          <!--          <img :src="" style="width:100px;height:100px"  alt="">-->
        </template>
      </el-table-column>




      <!-- <el-table-column prop="phone" label="经度,纬度" align="center" show-overflow-tooltip>
        <template slot-scope="scope">
          {{ scope.row.lng }},
          {{ scope.row.lat }}
        </template>
      </el-table-column> -->

      <el-table-column prop="certificationUrl" label="资质文件" align="center" show-overflow-tooltip>
        <template slot-scope="scope">
          <template v-if="scope.row.certificationUrl.length > 0">
            <el-button size="small" type="primary" @click="xiaz(scope.row.certificationUrl)">
              下载
            </el-button>
          </template>
          <template v-if="scope.row.certificationUrl.length == 0">
            暂无文件
          </template>
        </template>
      </el-table-column>

      <el-table-column prop="categoryId" label="供应商类型" align="center">
        <template slot-scope="scope">
          <el-popover
            trigger="hover"
            placement="top-start"
            width="300px"
          >
            <treeselect
              :normalizer="normalizerTree"
              :autoSelectDescendants="true"
              ref="indicatorTree"
              node-key="id"
              multiple
              disabled
              :clearable="false"
              :flat="true"
              :value="reTreeValue(scope.row.categoryId)"
              :options="tableDatab"
              :show-count="true"
              placeholder="请选择类型"
            />
            <!-- <div slot="reference">{{ scope.row.categoryId }}</div> -->
            <div slot="reference">点击查看</div>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column prop="address" label="地址" align="center" show-overflow-tooltip>
      </el-table-column>
<!--      <el-table-column prop="createTime" label="创建时间" align="center" show-overflow-tooltip></el-table-column>-->

      <el-table-column label="创建人" align="center" prop="createBy" />
      <el-table-column label="创建时间" align="center" prop="createTime" />
      <el-table-column label="更新时间" align="center" prop="updateTime" />


      <el-table-column fixed="right" align="center" label="操作" width="200">
        <template slot-scope="scope">
          <el-button type="text" @click="edita(scope.row)" size="small">
            修改
          </el-button>
          <el-button type="text" @click="dela(scope.row)" size="small">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>


    <pagination
      :total="total"
      :limit.sync="queryParams.pageSize"
      @pagination="ready"/>


    <add ref="Refadd" @shuaxin="shuaxin"></add>
    <edit ref="Refedit" @shuaxin="shuaxin"></edit>

  </div>
</template>

<script>
import {getlist, dellist} from "@/api/purchase/gongyingshang";
import {getlistb} from "@/api/purchase/category";
import add from "./add";
import edit from "./edit";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

export default {
  name: 'index',
  components: {
    add,
    edit,
    Treeselect,
  },
  data() {
    return {
      up_file: '',
      tableData: [],
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: '',
        phone: '',
        address: '',
        status: '',
      },
      audit_status: [],
      listClass: '',
      imgurl: '',
      srcList: [
        'https://fuss10.elemecdn.com/8/27/f01c15bb73e1ef3793e64e6b7bbccjpeg.jpeg',
      ],
      onedata: [],
      tableDatab: [],

    };
  },
  created() {
    this.up_file = process.env.VUE_APP_BASE_API;
    this.ready();
  },
  methods: {
    // 自定义树配置
    normalizerTree(node) {
      return {
        children: node.children,
        label: node.name,
        id: node.id
      }
    },
    shuaxin() {
      this.ready();
    },
    xzimg(e) {
      this.srcList[0] = e;
    },
    reTreeValue(value) {
      return value?.split(',') || undefined
    },
    ready() {
      getlistb({}).then((res) => {
        let lista = res.data;
        this.tableDatab = lista;
      });
      getlist(this.queryParams).then((res) => {
        let lista = res.rows;
        this.tableData = lista;
        this.total = res.total;
      });
    },
    cz() {
      this.queryParams.name = '';
      this.queryParams.phone = '';
      this.queryParams.address = '';
      this.ready();
    },
    xiaz(e) {
      window.open(this.up_file + e);
    },
    shaixuan(data, value, name, retname, indexa) {
      for (let i = 0; i < data.length; i++) {
        let item = data[i];
        if (item[name] == value) {
          return item[retname];
        }
      }
    },
    shaixuanb(data, value, name, retname, indexa) {
      for (let i = 0; i < data.length; i++) {
        let item = data[i];
        if (item[name] == value) {
          return item['listClass'];
        }
      }
    },

    add() {
      this.$refs.Refadd.showmodel();
    },
    edita(row) {
      this.onedata = row;
      this.$refs.Refedit.showmodel(row);
    },
    dela(row) {
      this.$confirm('是否删除此数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        dellist({
          ids: row.id,
        }).then((res) => {
          this.$message({
            message: '删除成功',
            type: 'success'
          });
          this.ready();
        });
      }).catch(() => {
      });
    },


  }
}
</script>

<style scoped>
.bodya {
  padding: 20px;
}

.lab1 {
  text-align: right;
  height: 30px;
  line-height: 30px;
  padding-right: 10px;
}

.head {
  padding-top: 10px;
  text-align: right;
}
</style>
