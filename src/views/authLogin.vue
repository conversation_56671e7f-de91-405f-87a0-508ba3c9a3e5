<template></template>
<script>
import { getState } from '@/api/login'

export default {
  name: 'AuthLogin',
  data() {
    return {}
  },
  watch: {
    $route: {
      handler: function (route) {
        if (route.query.code || route.query.auth_code) {
          this.handleAuthLogin(route.query)
        }
      },
      immediate: true
    }
  },
  methods: {
    // 微信登录
    // prettier-ignore
    handleAuthLogin(query = {}) {
      getState().then(res => {
        const { code, msg } = res
        if (code === 200) {
          if (query.hasOwnProperty('auth_code') && query.auth_code) {
            this.$store.dispatch('AuthLogin', { code: query.auth_code, state: msg, authSource: 'alipay_pc' }).then(() => {
              window.parent.opener.postMessage('success', '*');
              window.close()
            }).catch(() => {})
          }
          if (query.hasOwnProperty('code') && query.code) {
            this.$store.dispatch('AuthLogin', { code: query.code, state: msg, authSource: 'wechat_open_pc' }).then(() => {
              this.$router.push('/')
            }).catch(() => {})
          }
        }
      })
    }
  }
}
</script>
<style scoped lang="scss"></style>
