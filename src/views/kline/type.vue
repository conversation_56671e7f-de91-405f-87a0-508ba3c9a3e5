<template>
  <div>
    <el-dialog v-dialogDragBox title="钢材类别" :visible.sync="open" width="1150px" class="custom-dialog" :before-close="handleClose">
      <div style="padding: 0 20px">
        <el-button type="primary" size="small" icon="el-icon-plus" @click="handleAdd">添加钢材类别</el-button>

        <el-table ref="table" stripe :data="list" row-key="ids" style="width: 100%; margin-top: 20px" class="custom-table">
          <el-table-column align="center" type="index" label="序号"></el-table-column>
          <el-table-column align="center" prop="steelTypeName" label="钢材类型" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="icon" label="图标" show-overflow-tooltip>
            <template slot-scope="{ row }">
              <image-preview :src="imgPath + row.icon" :width="50" :height="50" style="margin: 0 auto" />
            </template>
          </el-table-column>
          <el-table-column align="center" prop="color" label="颜色" show-overflow-tooltip>
            <template slot-scope="{ row }">
              <span style="display: inline-block; width: 50px; height: 50px" :style="{ background: row.color || '#000' }"></span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="steelPlantName" label="所属钢厂" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="description" label="描述" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" label="操作" width="220px">
            <template slot-scope="{ row }">
              <button type="button" class="table-btn primary" @click="handleUpdate(row)">
                <i class="el-icon-edit"></i>
                修改
              </button>
              <button type="button" class="table-btn danger" @click="handleDelete(row)">
                <i class="el-icon-delete"></i>
                删除
              </button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div slot="footer">
        <button type="button" class="custom-dialog-btn primary" @click="handleClose">关闭</button>
      </div>
    </el-dialog>

    <el-dialog v-dialogDragBox :title="title" :visible.sync="formOpen" width="1150px" class="custom-dialog">
      <div style="padding: 0 20px">
        <el-form ref="form" :model="form" :rules="rules" label-width="6em">
          <el-form-item label="类别名称" prop="name">
            <el-input v-model="form.name" placeholder="请输入类别名称" />
          </el-form-item>
          <el-form-item label="专属钢厂" prop="pid">
            <el-select v-model="form.pid" placeholder="请选择专属钢厂" style="width: 100%">
              <el-option v-for="item in factoryOptions" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="颜色" prop="color">
            <el-color-picker v-model="form.color"></el-color-picker>
          </el-form-item>
          <el-form-item label="描述" prop="description">
            <el-input v-model="form.description" placeholder="请输入描述" />
          </el-form-item>
          <el-form-item label="钢厂图标" prop="icon">
            <image-upload v-model="form.icon" />
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer">
        <button type="button" class="custom-dialog-btn" @click="formOpen = false">取消</button>
        <button type="button" class="custom-dialog-btn primary" @click="handleSumit">提交</button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { createSteel, getFactoryList, getSteelList, updateSteel, deleteSteel } from '@/api/kline'

export default {
  name: 'KlineFactory',
  data() {
    return {
      open: false,
      list: [],
      formOpen: false,
      form: {},
      rules: {
        name: [{ required: true, message: '请输入钢厂名称', trigger: 'blur' }],
        location: [{ required: true, message: '请输入钢厂位置', trigger: 'blur' }],
        icon: [{ required: true, message: '请上传钢厂图标', trigger: 'blur' }]
      },
      title: '',
      factoryOptions: [{ id: 0, name: '全部' }]
    }
  },
  created() {
    getFactoryList().then(res => {
      this.factoryOptions = [...this.factoryOptions, ...res.rows]
    })
  },
  methods: {
    getList() {
      this.open = true
      getSteelList().then(res => {
        this.list = res.rows
      })
    },
    handleClose() {
      this.open = false
      this.$parent.getType()
    },
    reset() {
      this.form = {
        color: '#74a3e6',
        description: undefined,
        display: undefined,
        icon: undefined,
        name: undefined,
        pid: 0,
        id: undefined
      }
      this.resetForm('form')
    },
    handleAdd() {
      this.reset()
      this.title = '添加钢材类别'
      this.formOpen = true
    },
    handleUpdate(row) {
      this.reset()
      this.title = '修改钢材类别'
      this.form = { ...row, ...{ name: row.steelTypeName, id: row.steelTypeId } }
      this.formOpen = true
    },
    // 提交
    handleSumit() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateSteel(this.form).then(res => {
              if (res.code === 200) {
                this.$message.success('修改成功')
                this.formOpen = false
                this.getList()
              } else {
                this.$message.error(res.msg)
              }
            })
          } else {
            createSteel(this.form).then(res => {
              if (res.code === 200) {
                this.$message.success('新增成功')
                this.formOpen = false
                this.getList()
              } else {
                this.$message.error(res.msg)
              }
            })
          }
        }
      })
    },
    // prettier-ignore
    handleDelete(row) {
      const typesId = row.steelTypeId
      this.$confirm('是否删除此钢材类别?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteSteel({ typesId }).then(res => {
          if (res.code === 200) {
            this.$message.success('删除成功')
            this.getList()
          } else {
            this.$message.error(res.msg)
          }
        })
      }).catch(() => {
      })
    }
  }
}
</script>

<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';
</style>
