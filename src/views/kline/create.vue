<template>
  <el-dialog v-dialogDragBox :title="title" :visible.sync="open" width="1150px" class="custom-dialog">
    <div style="padding: 0 20px">
      <el-form ref="form" :model="form" :rules="rules" label-width="7em">
        <el-row :gutter="20" style="display: flex; flex-wrap: wrap">
          <el-col :span="12">
            <el-form-item label="钢厂" prop="steelPlantId">
              <el-select v-model="form.steelPlantId" placeholder="请选择钢厂" style="width: 100%" :disabled="form.steelPriceId">
                <el-option v-for="item in factoryOptions" :key="item.id" :label="item.name" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="钢材类别" prop="steelTypeId">
              <el-select v-model="form.steelTypeId" placeholder="请选择钢材类别" style="width: 100%" :disabled="form.steelPriceId">
                <el-option v-for="item in typeOptions" :key="item.steelTypeId" :label="item.steelTypeName" :value="item.steelTypeId"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="未税价格" prop="price">
              <el-input v-model="form.price" placeholder="请输入未税价格">
                <template slot="append">元/吨</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="含税价格" prop="taxPrice">
              <el-input v-model="form.taxPrice" placeholder="请输入含税价格">
                <template slot="append">元/吨</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="规格" prop="spec">
              <el-input v-model="form.spec" placeholder="请输入规格" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="型号" prop="model">
              <el-input v-model="form.model" placeholder="请输入型号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="材质" prop="material">
              <el-input v-model="form.material" placeholder="请输入材质" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="日期" prop="date">
              <el-date-picker v-model="form.date" type="date" placeholder="请选择日期" format="yyyy-MM-dd" value-format="timestamp" style="width: 100%"></el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div slot="footer">
      <button type="button" class="custom-dialog-btn" @click="handleCancel">取消</button>
      <button type="button" class="custom-dialog-btn primary" @click="handleSumit">立即发布</button>
    </div>
  </el-dialog>
</template>
<script>
import { createKline, getFactoryList, getSteelList } from '@/api/kline'
import { isNumber, isNumberLength } from '@/utils/validate'

export default {
  name: 'KlineCreate',
  data() {
    return {
      title: '',
      form: {},
      rules: {
        steelPlantId: [{ required: true, message: '请选择钢厂', trigger: 'change' }],
        steelTypeId: [{ required: true, message: '请选择钢材类别', trigger: 'change' }],
        price: [
          { required: true, message: '请输入未税价格', trigger: 'blur' },
          { validator: isNumber, message: '输入有误请重新输入', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 3), message: '只可以填写三位小数', trigger: 'blur' }
        ],
        taxPrice: [
          { validator: isNumber, message: '输入有误请重新输入', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 3), message: '只可以填写三位小数', trigger: 'blur' }
        ],
        date: [{ required: true, message: '请选择日期', trigger: 'change' }]
      },
      open: false,
      factoryOptions: [],
      typeOptions: []
    }
  },
  created() {
    getFactoryList().then(res => {
      this.factoryOptions = res.rows
    })
    getSteelList().then(res => {
      this.typeOptions = res.rows
    })
  },
  methods: {
    // 重置
    reset() {
      this.form = {
        currency: undefined,
        date: new Date().getTime(),
        material: undefined,
        model: undefined,
        price: undefined,
        region: undefined,
        spec: undefined,
        steelPlantId: undefined,
        steelTypeId: undefined,
        taxPrice: undefined
      }
      this.resetForm('form')
    },
    // 新增
    handleAdd(steelTypeId) {
      this.reset()
      if(steelTypeId) this.form.steelTypeId = steelTypeId
      this.title = '新增价格'
      this.open = true
    },
    // 修改
    handleUpdate(data) {
      this.reset()
      this.title = '修改价格'
      this.form = { ...data }
      this.form.date = (new Date(data.date)).getTime()
      this.open = true
    },
    // 取消
    handleCancel() {
      this.open = false
      this.reset()
    },
    // 提交
    handleSumit() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          createKline(this.form).then(res => {
            if (res.code === 200) {
              const msg = this.form.steelPriceId ? '修改成功' : '新增成功'
              this.$message.success(msg)
              this.open = false
              this.$parent.getList()
            } else {
              this.$message.error(res.msg)
            }
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
</style>
