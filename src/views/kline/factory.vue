<template>
  <div>
    <el-dialog v-dialogDragBox title="钢厂列表" :visible.sync="open" width="1150px" class="custom-dialog" :before-close="handleClose">
      <div style="padding: 0 20px">
        <el-button type="primary" size="small" icon="el-icon-plus" @click="handleAdd">添加钢厂</el-button>

        <el-table ref="table" stripe :data="list" row-key="ids" style="width: 100%; margin-top: 20px" class="custom-table">
          <el-table-column align="center" type="index" label="序号"></el-table-column>
          <el-table-column align="center" prop="name" label="钢厂名称" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="icon" label="图标" show-overflow-tooltip>
            <template slot-scope="{ row }">
              <image-preview :src="imgPath + row.icon" :width="50" :height="50" style="margin: 0 auto" />
            </template>
          </el-table-column>
          <el-table-column align="center" prop="location" label="钢厂位置" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" label="操作" width="220px">
            <template slot-scope="{ row }">
              <button type="button" class="table-btn primary" @click="handleUpdate(row)">
                <i class="el-icon-edit"></i>
                修改
              </button>
              <button type="button" class="table-btn danger" @click="handleDelete(row)">
                <i class="el-icon-delete"></i>
                删除
              </button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div slot="footer">
        <button type="button" class="custom-dialog-btn primary" @click="handleClose">关闭</button>
      </div>
    </el-dialog>

    <el-dialog v-dialogDragBox :title="title" :visible.sync="formOpen" width="700px" class="custom-dialog">
      <div style="padding: 0 20px">
        <el-form ref="form" :model="form" :rules="rules" label-width="6em">
          <el-form-item label="钢厂名称" prop="name">
            <el-input v-model="form.name" placeholder="请输入钢厂名称" />
          </el-form-item>
          <el-form-item label="钢厂位置" prop="location">
            <el-input v-model="form.location" placeholder="请输入钢厂位置" />
          </el-form-item>
          <el-form-item label="钢厂图标" prop="icon">
            <image-upload v-model="form.icon" />
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer">
        <button type="button" class="custom-dialog-btn" @click="formOpen = false">取消</button>
        <button type="button" class="custom-dialog-btn primary" @click="handleSumit">提交</button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { createFactory, deleteFactory, getFactoryList, updateFactory } from '@/api/kline'

export default {
  name: 'KlineFactory',
  data() {
    return {
      open: false,
      list: [],
      formOpen: false,
      form: {},
      rules: {
        name: [{ required: true, message: '请输入钢厂名称', trigger: 'blur' }],
        location: [{ required: true, message: '请输入钢厂位置', trigger: 'blur' }],
        icon: [{ required: true, message: '请上传钢厂图标', trigger: 'blur' }]
      },
      title: ''
    }
  },
  methods: {
    getList() {
      this.open = true
      getFactoryList().then(res => {
        this.list = res.rows
      })
    },
    handleClose() {
      this.open = false
      this.$parent.getList()
    },
    reset() {
      this.form = {
        name: undefined,
        location: undefined,
        icon: undefined,
        id: undefined
      }
      this.resetForm('form')
    },
    handleAdd() {
      this.reset()
      this.title = '添加钢厂'
      this.formOpen = true
    },
    handleUpdate(row) {
      this.reset()
      this.title = '修改钢厂'
      this.form = { ...row }
      this.formOpen = true
    },
    // 提交
    handleSumit() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateFactory(this.form).then(res => {
              if (res.code === 200) {
                this.$message.success('修改成功')
                this.formOpen = false
                this.getList()
              } else {
                this.$message.error(res.msg)
              }
            })
          } else {
            createFactory(this.form).then(res => {
              if (res.code === 200) {
                this.$message.success('新增成功')
                this.formOpen = false
                this.getList()
              } else {
                this.$message.error(res.msg)
              }
            })
          }
        }
      })
    },
    // prettier-ignore
    handleDelete(row) {
      const plantsId = row.id
      this.$confirm('是否删除此钢厂?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteFactory({ plantsId }).then(res => {
          if (res.code === 200) {
            this.$message.success('删除成功')
            this.getList()
          } else {
            this.$message.error(res.msg)
          }
        })
      }).catch(() => {
      })
    }
  }
}
</script>

<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';
</style>
