<template>
  <div class="newBox bgcf9 vh-85">
    <div style="padding: 20px 20px 0">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px">
        <el-form-item label="往期价格">
          <el-date-picker v-model="dateRange" style="width: 240px" value-format="yyyy-MM-dd" type="daterange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" @change="changeDate"></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
          <el-button type="primary" icon="el-icon-plus" size="small" @click="handleAdd">新增价格</el-button>
          <el-button type="success" icon="el-icon-menu" size="small" @click="handleFactory">钢厂管理</el-button>
          <el-button type="warning" icon="el-icon-s-unfold" size="small" @click="handletype">钢材类别</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 分类 -->
    <div class="classify flex">
      <div class="classify-item" :class="{ active: item.steelTypeId === queryParams.typeId }" v-for="item in typeOptions" :key="item.steelTypeId" @click="handleType(item)">
        {{ item.steelTypeName }}
      </div>
    </div>
    <div class="box" v-if="list.length">
      <el-table v-loading="loading" ref="table" stripe :data="list" style="width: 100%" class="custom-table">
        <el-table-column align="center" type="index" label="序号"></el-table-column>
        <el-table-column align="center" prop="date" label="日期" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" prop="steelPlantName" label="钢厂" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" prop="steelTypeName" label="类型名称" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" prop="price" label="单价" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <span class="table-price">{{ `￥${row.price}/吨` }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="taxPrice" label="含税价" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <span class="table-price">{{ `￥${row.price}/吨` }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="spec" label="规格" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" prop="model" label="型号" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" prop="material" label="材质" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" label="操作" width="220px">
          <template slot-scope="{ row }">
            <button type="button" class="table-btn primary" @click="handleUpdate(row)">
              <i class="el-icon-edit"></i>
              修改
            </button>
            <button type="button" class="table-btn danger" @click="handleDelete(row)">
              <i class="el-icon-delete"></i>
              删除
            </button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-empty description="暂无今日价格" v-else />

    <!--  新增/修改价格  -->
    <create-dialog ref="create" />

    <!--    钢厂管理-->
    <factory-dialog ref="factory" />

    <!--    钢材类别-->
    <type-dialog ref="steel" />
  </div>
</template>

<script>
import { deleteFactory, deleteKline, getKline, getSteelList } from '@/api/kline'
import createDialog from './create'
import factoryDialog from './factory'
import typeDialog from './type'

export default {
  name: 'Kline',
  components: { createDialog, factoryDialog, typeDialog },
  data() {
    return {
      queryParams: {
        end: undefined,
        start: undefined,
        typeId: undefined
      },
      dateRange: [],
      loading: true,
      total: 0,
      list: [],
      typeOptions: [],
      factoryShow: false,
      typeShow: false
    }
  },
  created() {
    this.getType()
  },
  methods: {
    // 日期选择
    changeDate() {
      this.handleQuery()
    },
    // 价格列表
    getList() {
      this.loading = true
      getKline(this.queryParams).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          this.loading = false
          this.list = data
        } else this.$message.error(msg)
      })
    },
    // 搜索
    handleQuery() {
      if (this.dateRange) {
        this.queryParams.start = this.dateRange[0]
        this.queryParams.end = this.dateRange[1]
      }
      this.getList()
    },
    // 重置搜索
    resetQuery() {
      this.dateRange = []
      this.handleQuery()
    },
    // 新增价格
    handleAdd() {
      this.$refs.create.handleAdd(this.queryParams.typeId)
    },
    // 修改价格
    handleUpdate(row) {
      this.$refs.create.handleUpdate(row)
    },
    // 分类列表
    getType() {
      getSteelList().then(res => {
        const { code, msg, rows } = res
        if (code === 200 && rows.length) {
          this.typeOptions = rows
          this.queryParams.typeId = rows[0].steelTypeId
          this.getList()
        } else this.$message.error(msg)
      })
    },
    // 改变分类
    handleType(e) {
      this.queryParams.typeId = e.steelTypeId
      this.handleQuery()
    },
    // 钢厂管理
    handleFactory() {
      this.$refs.factory.getList()
    },
    // 钢材类别
    handletype() {
      this.$refs.steel.getList()
    },
    // 删除钢材价格
    // prettier-ignore
    handleDelete(row) {
      const typePriceId = row.steelPriceId
      this.$confirm('是否删除此数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteKline({ typePriceId }).then(res => {
          if (res.code === 200) {
            this.$message.success('删除成功')
            this.getList()
          } else {
            this.$message.error(res.msg)
          }
        })
      }).catch(() => {
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
.box {
  padding: 20px;
}
</style>
