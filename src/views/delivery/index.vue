<template>
  <div>
    <div class="viewBox">
      <div class="viewBox-header">
        <div class="viewBox-container">
          <div class="viewBox-logo">
            <img src="@/assets/logo/<EMAIL>" class="viewBox-logo-img" />
            <div class="viewBox-logo-text">
              <b>自由客紧固件</b>
              一站式采购平台
            </div>
          </div>
        </div>
      </div>
      <div class="viewBox-container">
        <el-form ref="info" :model="info" :rules="rules" label-width="0">
          <div class="viewBox-title">发货单</div>
          <div class="viewBox-item-title">发货单详情</div>
          <div class="viewBox-item-info">
            <div class="info-item">
              <span>发货清单编号</span>
              {{ info.deliveryNum }}
            </div>
            <div class="info-item">
              <span>发货时间</span>
              {{ info.createTime }}
            </div>
            <div class="info-item">
              <span>合同来源</span>
              {{ info.contractNum }}
            </div>
            <div class="info-item">
              <span>发货人</span>
              {{ info.createBy }}
            </div>
          </div>
          <div class="viewBox-item-title">发货单明细</div>
          <el-table ref="detailTable" stripe :data="info.details" style="width: 100%" class="custom-table">
            <el-table-column align="center" type="index" label="序号"></el-table-column>
            <el-table-column align="center" prop="productName" label="产品名称" show-overflow-tooltip min-width="130">
              <template slot-scope="{ row }">
                <span class="table-link" @click="hanleView(row)">{{ row.productName }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="picture1" label="图片" width="70">
              <template slot-scope="{ row }">
                <el-image :src="row.product && formatProductImg(row.product)" fit="cover" @click="handleImgView(row.product)" v-if="row.product && formatProductImg(row.product)">
                  <div slot="error" class="image-slot">
                    <i class="el-icon-picture-outline"></i>
                  </div>
                </el-image>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="specs" label="产品规格" show-overflow-tooltip>
              <template slot-scope="{ row }">{{ row.product && row.product.specs }}</template>
            </el-table-column>
            <el-table-column align="center" prop="model" label="产品型号" show-overflow-tooltip width="70">
              <template slot-scope="{ row }">{{ row.product && row.product.model }}</template>
            </el-table-column>
            <el-table-column align="center" prop="productCode" label="产品编码" show-overflow-tooltip>
              <template slot-scope="{ row }">{{ row.product && row.product.productCode }}</template>
            </el-table-column>
            <el-table-column align="center" prop="materialQuality" label="材质" show-overflow-tooltip>
              <template slot-scope="{ row }">{{ row.product && row.product.materialQuality }}</template>
            </el-table-column>
            <el-table-column align="center" prop="surface" label="表面处理" show-overflow-tooltip>
              <template slot-scope="{ row }">{{ row.product && row.product.surface }}</template>
            </el-table-column>
            <el-table-column align="center" prop="unit" label="单位" show-overflow-tooltip>
              <template slot-scope="{ row }">{{ row.product && row.product.unit }}</template>
            </el-table-column>
            <el-table-column align="center" prop="saleQuantity" label="合同销售数量" show-overflow-tooltip></el-table-column>
            <el-table-column align="center" prop="quantity" label="发货数量" show-overflow-tooltip></el-table-column>
          </el-table>
          <div class="viewBox-button" v-if="info.status === 1">
            <button type="button" class="danger" @click="handleReturn">退货</button>
            <button type="button" class="primary" @click="handleConfirmTake">确认收货</button>
          </div>
        </el-form>
      </div>
    </div>
    <!-- 产品详情 -->
    <product-dialog ref="productInfo" :width="width"></product-dialog>
    <!-- 确认收货提示 -->
    <el-dialog v-dialogDragBox title="确认收货提示" :visible.sync="open" width="750px" class="custom-dialog">
      <div class="certifyBox">
        <img src="~@/assets/images/error.png" alt="" class="certifyBox-icon" />
        <div class="certifyBox-title">该发货单需身份认证，请使用APP确认收货</div>
        <div class="certifyBox-tip">请下载自由客紧固件App，在APP打开，再进行确认收货</div>
        <div class="certifyBox-code">
          <img src="~@/assets/images/qrcode-app.png" alt="" />
          <span>扫码下载APP</span>
        </div>
        <div class="certifyBox-button" @click="open = false">我知道了</div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { deliveryLinkDetail, deliveryReturn, deliveryConfirmTake } from '@/api/delivery'
import ProductDialog from '@/views/public/product/dialog'

export default {
  components: { ProductDialog },
  data() {
    return {
      width: undefined,
      requestId: '',
      info: {},
      sendInfo: {},
      rules: {},
      open: false
    }
  },
  created() {
    const { requestId } = this.$route.query
    if (!requestId) return this.$message.error('参数错误，请稍后再试')
    this.getInfo(requestId)
    // 判断是移动端还是pc端,如果是移动端，则跳转到app页面
    if (this.isMobile()) {
      window.location.href = 'https://m.ziyouke.net/pages/delivery/index?requestId=' + requestId
    }
  },
  mounted() {
    const width = document.documentElement.clientWidth
    if (width > 1199) {
      this.width = '1040px'
    } else {
      this.width = '80%'
    }
    window.onresize = () => {
      return (() => {
        const width = document.documentElement.clientWidth
        if (width > 1199) {
          this.width = '1040px'
        } else {
          this.width = '80%'
        }
      })()
    }
  },
  methods: {
    // 判断是移动端还是pc端
    isMobile() {
      return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
    },
    // 获取发货清单详情
    // prettier-ignore
    getInfo(requestId = undefined) {
      if (!requestId) return this.$message.error('参数错误，请稍后再试')
      const loading = this.$loading({
        lock: true,
        text: '加载中',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      this.requestId = requestId
      deliveryLinkDetail({ requestId }).then(res => {
        const { code, data, msg } = res
        if (code !== 200) return this.$message.error(msg)
        this.info = data && data.data
        this.sendInfo = data && data.send
      }).finally(() => {
        loading.close()
      })
    },
    // 确认收货
    // prettier-ignore
    handleConfirmTake() {
      const { certify } = this.sendInfo
      if (certify) return this.open = true
      this.$confirm('确认收货后，将无法退货，请谨慎操作', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deliveryConfirmTake({ requestId: this.requestId }).then(res => {
          const { code, msg } = res
          if (code !== 200) return this.$message.error(msg)
          this.$message.success('确认收货成功')
        })
      }).catch(() => {})
    },
    // 退货
    // prettier-ignore
    handleReturn() {
      this.$confirm('是否已沟通完成?确认退货?', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deliveryReturn({ requestId: this.requestId }).then(res => {
          const { code, msg } = res
          if (code !== 200) return this.$message.error(msg)
          this.$message.success('退货成功')
          this.getInfo(this.requestId)
        })
      }).catch(() => {})
    },
    // 产品详情
    hanleView(row) {
      const { product } = row
      this.$refs.productInfo.handleView(product)
    },
    // 图片预览
    handleImgView(row) {
      this.$refs.productInfo.handleImgView(row)
    }
  }
}
</script>
<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
.viewBox {
  min-height: 100vh;
  background-color: #f9f9f9;
  &-container {
    max-width: 1200px;
    margin: 0 auto;
    ::v-deep .custom-table {
      box-shadow: none;
      .el-form-item {
        margin-top: 10px !important;
        margin-bottom: 10px !important;
        .el-form-item__error {
          top: 95%;
          padding-top: 0;
        }
        .el-input__inner {
          text-align: center;
        }
        &.primary {
          .el-input__inner:focus {
            color: $blue;
          }
        }
      }
    }
  }
  &-header {
    height: 90px;
    background-color: $blue;
    padding-left: 14px;
  }
  &-logo {
    display: inline-flex;
    height: 90px;
    align-items: center;
    &-img {
      width: 52px;
      height: 50px;
      margin-right: 10px;
    }
    &-text {
      display: inline-flex;
      flex-direction: column;
      color: $white;
      b {
        font-size: 20px;
        font-weight: 500;
        line-height: 23px;
      }
      span {
        font-size: 20px;
        font-weight: 500;
        line-height: 19px;
      }
    }
  }
  &-title {
    font-size: 18px;
    color: $info;
    background-color: #eef0f8;
    line-height: 50px;
    padding-left: 30px;
  }
  &-item {
    &-title {
      font-size: 14px;
      color: #999999;
      line-height: 50px;
      padding-left: 30px;
    }
    &-info {
      padding: 11px 30px;
      background-color: #f0f3f9;
      overflow: hidden;
      .info-item {
        line-height: 32px;
        float: left;
        padding-right: 15px;
        font-size: 14px;
        font-weight: 500;
        color: $font;
        span {
          display: inline-block;
          font-size: 12px;
          color: $info;
          padding-right: 10px;
        }
      }
    }
  }
  &-button {
    display: flex;
    justify-content: flex-end;
    padding: 30px 0;
    .primary,
    .danger {
      width: 270px;
      height: 50px;
      line-height: 50px;
      text-align: center;
      font-size: 16px;
      font-weight: 500;
      color: $white;
      border: 0;
      border-radius: 5px;
      cursor: pointer;
      background-color: $blue;
      &:hover {
        opacity: 0.8;
        background-color: $blue;
      }
    }
    .danger {
      margin-right: 20px;
      background-color: $red;
      &:hover {
        opacity: 0.8;
        background-color: $red;
      }
    }
  }
}
.certifyBox {
  padding: 30px 0;
  background: #ffffff;
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  &-icon {
    width: 36px;
    height: 36px;
  }
  &-title {
    font-size: 14px;
    line-height: 20px;
    margin-top: 17px;
    color: #333333;
  }
  &-tip {
    font-size: 12px;
    line-height: 20px;
    margin-top: 10px;
    color: #2e73f3;
  }
  &-code {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 150px;
    height: 150px;
    border: 1px solid #f6f6f6;
    border-radius: 5px;
    margin-top: 10px;
    img {
      width: 110px;
      height: 110px;
    }
    span {
      font-size: 12px;
      line-height: 20px;
      color: #666666;
    }
  }
  &-button {
    width: 250px;
    line-height: 50px;
    background-color: #2e73f3;
    border-radius: 5px;
    color: #ffffff;
    font-size: 16px;
    margin-top: 20px;
    text-align: center;
    cursor: pointer;
    &:hover {
      opacity: 0.8;
    }
  }
}
</style>
