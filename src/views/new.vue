<template>
  <div>
    <div class="container" onselectstart="return false;">
      <head-tpl :is-login="islogin"></head-tpl>
      <div class="box">
        <h2 class="h2">{{ newlist.title }}</h2>
        <p class="p1" style="text-align: right; margin-right: 20px">发布时间：{{ parseTime(newlist.publishTime, '{y}-{m}-{d}') }}</p>
        <div style="text-align: center" v-if="!!newlist.imgUrl && newlist.imgUrl != '无'">
          <img style="margin: auto" :src="newlist.imgUrl" />
        </div>
        <div class="con" v-html="newlist.content"></div>
        <div class="disclaimers" v-if="!!newlist.disclaimers">免责声明：{{ newlist.disclaimers }}</div>
        <p class="p1" v-if="!newlist.source || newlist.source == '自由客'">本站原创</p>
        <p class="p1" v-else>转载自：{{ newlist.source }}</p>
        <div class="evaluate" v-if="islogin">
          <div class="evaluate-icon">
            <div class="evaluate-icon-item" @click="handleLike('like')">
              <el-badge :value="evaluateLike.count" :max="99" :hidden="!evaluateLike.count">
                <img src="~@/assets/images/news-icon1.png" alt="给文章点赞" v-if="!evaluateLike.withMe" />
                <img src="~@/assets/images/news-icon1d.png" alt="给文章点赞" v-if="evaluateLike.withMe" />
              </el-badge>
              <span>给文章点赞</span>
            </div>
            <div class="evaluate-icon-item" @click="handleLike('belittle')">
              <el-badge :value="evaluateBelittle.count" :max="99" :hidden="!evaluateBelittle.count">
                <img src="~@/assets/images/news-icon3.png" alt="吐槽一下" v-if="!evaluateBelittle.withMe" />
                <img src="~@/assets/images/news-icon3d.png" alt="吐槽一下" v-if="evaluateBelittle.withMe" />
              </el-badge>
              <span>吐槽一下</span>
            </div>
          </div>
          <template v-if="hasComment">
            <div class="evaluate-title">评论 {{ evaluateList.length || '' }}</div>
            <div class="evaluate-con">
              <div class="evaluate-con-answer" v-if="!parentId">
                <img :src="avatar" class="evaluate-con-answer-img" alt="" />
                <div class="evaluate-con-answer-input">
                  <el-input @blur="changeCaretPosition" type="textarea" placeholder="请输入评论内容" v-model="comment" resize="none" maxlength="255" show-word-limit></el-input>
                  <div class="evaluate-con-answer-button">
                    <emoji @handleClickEmoji="handleClickEmoji" />
                    <span class="button" :class="{ disabled: !comment.trim() }" @click="addEvaluate">发表</span>
                  </div>
                </div>
              </div>
              <div class="evaluate-con-item" v-for="item in evaluateList" :key="item.id">
                <img :src="imgPath + item.avatar" class="evaluate-con-item-img" alt="" />
                <div class="evaluate-con-w100">
                  <div class="evaluate-con-item-con">
                    <div class="evaluate-con-item-con-title">{{ hideMiddleStr(item.nickName) }}</div>
                    <div class="evaluate-con-item-con-con" v-html="formatContent(item.name)"></div>
                    <div class="evaluate-con-item-con-time">
                      <span>{{ formatTime(new Date(item.createTime).getTime(), '') }}</span>
                      <div class="inline-flex">
                        <div class="anwser" @click="handleDelete(item)" v-if="userId === item.userId || checkPermi(['system:news'])">
                          <i class="el-icon-delete"></i>
                          <span>删除</span>
                        </div>
                        <div class="anwser" @click="setParentid(item)">
                          <i class="el-icon-chat-line-square"></i>
                          <span>回复 {{ item.hasOwnProperty('children') && !!item.children ? `(${item.children.length})` : '' }}</span>
                        </div>
                      </div>
                    </div>
                    <div class="evaluate-con-item-con-open" @click="handleShowMore(item)" v-if="item.hasOwnProperty('children') && !!item.children">——展开所有评论</div>
                  </div>
                  <div class="evaluate-con-answer" v-if="parentId && parentId === item.id">
                    <img :src="avatar" class="evaluate-con-answer-img" alt="" />
                    <div class="evaluate-con-answer-input">
                      <el-input @blur="changeCaretPosition" type="textarea" placeholder="请输入评论内容" v-model="comment" resize="none" maxlength="255" show-word-limit></el-input>
                      <div class="evaluate-con-answer-button">
                        <emoji @handleClickEmoji="handleClickEmoji" />
                        <span class="button" :class="{ disabled: !comment.trim() }" @click="addEvaluate">发表</span>
                      </div>
                    </div>
                  </div>
                  <el-collapse-transition>
                    <div v-if="showId === item.id">
                      <div class="evaluate-con-item children" v-if="item.hasOwnProperty('children') && !!item.children" v-for="ite in item.children" :key="ite.id">
                        <img :src="imgPath + ite.avatar" class="evaluate-con-item-img" alt="" />
                        <div class="evaluate-con-w100">
                          <div class="evaluate-con-item-con">
                            <div class="evaluate-con-item-con-title">{{ hideMiddleStr(ite.nickName) }}</div>
                            <div class="evaluate-con-item-con-con" v-html="formatContent(ite.name)"></div>
                            <div class="evaluate-con-item-con-time">
                              <span>{{ formatTime(new Date(ite.createTime).getTime(), '') }}</span>
                              <div class="inline-flex">
                                <div class="anwser" @click="handleDelete(ite)" v-if="userId === ite.userId || checkPermi(['system:news'])">
                                  <i class="el-icon-delete"></i>
                                  <span>删除</span>
                                </div>
                                <div class="anwser" @click="setParentid(ite)">
                                  <i class="el-icon-chat-line-square"></i>
                                  <span>回复 {{ ite.hasOwnProperty('children') && !!ite.children ? `(${ite.children.length})` : '' }}</span>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div class="evaluate-con-answer" v-if="parentId && parentId === ite.id">
                            <img :src="avatar" class="evaluate-con-answer-img" alt="" />
                            <div class="evaluate-con-answer-input">
                              <el-input @blur="changeCaretPosition" type="textarea" placeholder="请输入评论内容" v-model="comment" resize="none" maxlength="255" show-word-limit></el-input>
                              <div class="evaluate-con-answer-button">
                                <emoji @handleClickEmoji="handleClickEmoji" />
                                <span class="button" :class="{ disabled: !comment.trim() }" @click="addEvaluate">发表</span>
                              </div>
                            </div>
                          </div>
                          <div class="evaluate-con-item children" v-if="ite.hasOwnProperty('children') && !!ite.children" v-for="itt in ite.children" :key="itt.id">
                            <img :src="imgPath + itt.avatar" class="evaluate-con-item-img" alt="" />
                            <div class="evaluate-con-w100">
                              <div class="evaluate-con-item-con">
                                <div class="evaluate-con-item-con-title">{{ hideMiddleStr(itt.nickName) }}</div>
                                <div class="evaluate-con-item-con-con" v-html="formatContent(itt.name)"></div>
                                <div class="evaluate-con-item-con-time">
                                  <span>{{ formatTime(new Date(itt.createTime).getTime(), '') }}</span>
                                  <div class="inline-flex">
                                    <div class="anwser" @click="handleDelete(itt)" v-if="userId === itt.userId || checkPermi(['system:news'])">
                                      <i class="el-icon-delete"></i>
                                      <span>删除</span>
                                    </div>
                                    <div class="anwser" @click="setParentid(itt)">
                                      <i class="el-icon-chat-line-square"></i>
                                      <span>回复 {{ itt.hasOwnProperty('children') && !!itt.children ? `(${itt.children.length})` : '' }}</span>
                                    </div>
                                  </div>
                                </div>
                              </div>
                              <div class="evaluate-con-answer" v-if="parentId && parentId === itt.id">
                                <img :src="avatar" class="evaluate-con-answer-img" alt="" />
                                <div class="evaluate-con-answer-input">
                                  <el-input @blur="changeCaretPosition" type="textarea" placeholder="请输入评论内容" v-model="comment" resize="none" maxlength="255" show-word-limit></el-input>
                                  <div class="evaluate-con-answer-button">
                                    <emoji @handleClickEmoji="handleClickEmoji" />
                                    <span class="button" :class="{ disabled: !comment.trim() }" @click="addEvaluate">发表</span>
                                  </div>
                                </div>
                              </div>
                              <div class="evaluate-con-item children" v-if="itt.hasOwnProperty('children') && !!itt.children" v-for="it in itt.children" :key="it.id">
                                <img :src="imgPath + it.avatar" class="evaluate-con-item-img" alt="" />
                                <div class="evaluate-con-w100">
                                  <div class="evaluate-con-item-con">
                                    <div class="evaluate-con-item-con-title">{{ hideMiddleStr(it.nickName) }}</div>
                                    <div class="evaluate-con-item-con-con" v-html="formatContent(it.name)"></div>
                                    <div class="evaluate-con-item-con-time">
                                      <span>{{ formatTime(new Date(it.createTime).getTime(), '') }}</span>
                                      <div class="anwser" @click="handleDelete(it)" v-if="userId === it.userId || checkPermi(['system:news'])">
                                        <i class="el-icon-delete"></i>
                                        <span>删除</span>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </el-collapse-transition>
                </div>
              </div>
            </div>
          </template>
        </div>
      </div>
      <foot-tpl />
    </div>
  </div>
</template>
<script>
import { getToken } from '@/utils/auth'
import headTpl from '@/views/public/components/head'
import footTpl from '@/views/public/components/foot'
import { getArticleDetail, getArticleCommentList, addArticleComment, addArticleLike, updateArticleLike, deleteArticleComment } from '@/api/article'
import { mapGetters } from 'vuex'
import { formatTime, hideMiddleStr, formatContent } from '@/utils'
import emoji from '@/components/emoji/index.vue'
import { checkPermi } from '@/utils/permission'

export default {
  components: { headTpl, footTpl, emoji },
  data() {
    return {
      newsId: undefined,
      newlist: [],
      islogin: false,
      evaluateLike: {},
      evaluateBelittle: {},
      evaluateContent: '',
      evaluateList: [],
      comment: '',
      parentId: 0,
      showId: 0,
      userId: undefined,
      caretPosition: 0,
      hasComment: false
    }
  },
  created() {
    this.getConfigKey('news:comment').then(res => {
      this.hasComment = res.msg === 'true'
    })
    this.userId = this.$store.getters && this.$store.getters.info && this.$store.getters.info.userId
    const newId = this.$route.query.id
    if (newId) this.getInfo(newId)
    else {
      this.$confirm('参数有误，点击确定返回上一页', '提示', {
        confirmButtonText: '确定',
        showCancelButton: false,
        type: 'warning'
      }).then(() => {
        this.$router.push({ name: 'news' })
      })
    }
    //判断用户是否存在
    this.islogin = getToken() ? true : false
  },
  computed: {
    ...mapGetters(['avatar'])
  },
  methods: {
    formatTime,
    hideMiddleStr,
    checkPermi,
    formatContent,
    getInfo(newsId) {
      this.newsId = newsId
      getArticleDetail({ newsId }).then(res => {
        if (res.code === 200) {
          this.newlist = res.data
          this.newlist.publishTime = res.data.publishTime || res.data.createTime
          this.newlist.content = this.newlist.content.replace(/\/prod-api\//g, 'https://www.ziyouke.net/prod-api/')
          this.evaluateLike = res.data.likeDo.find(item => item.action === 'like') || { count: 0 }
          this.evaluateBelittle = res.data.likeDo.find(item => item.action === 'belittle') || { count: 0 }
          this.getEvaluate()
        } else this.$message.error(res.msg)
      })
    },
    // 查询新闻评论
    getEvaluate() {
      const { newsId } = this
      getArticleCommentList({ newsId }).then(res => {
        const { code, data, msg } = res
        if (code === 200) this.evaluateList = data || []
        else this.$message.error(msg)
      })
    },
    // 点击回复
    setParentid(item) {
      this.caretPosition = 0
      this.comment = ''
      this.parentId = item.id
    },
    changeCaretPosition(e) {
      this.caretPosition = e.target.selectionStart
    },
    // 点击表情
    handleClickEmoji(emoji) {
      let index = this.caretPosition
      let str = this.comment
      this.comment = str.slice(0, index) + emoji + str.slice(index)
      this.caretPosition += emoji.length
    },
    // 新增新闻评论
    addEvaluate() {
      if (!this.comment) return
      const { newsId, comment, parentId } = this
      addArticleComment({ newsId, comment, parentId }).then(res => {
        const { code, data, msg } = res
        if (code === 200) {
          this.$message.success('发表成功')
          this.comment = ''
          this.parentId = 0
          this.getEvaluate()
        } else this.$message.error(msg)
      })
    },
    // 点赞/吐槽
    handleLike(action = 'like') {
      const { newsId } = this
      addArticleLike({ newsId, action }).then(res => {
        const { code, msg } = res
        if (code === 200) {
          if (action === 'like') {
            if (this.evaluateLike.withMe) this.$message.info('取消点赞')
            else this.$message.success('点赞成功')
          } else {
            if (this.evaluateBelittle.withMe) this.$message.info('取消吐槽')
            else this.$message.success('吐槽成功')
          }
          this.refreshEvaluate()
        } else this.$message.error(msg)
      })
    },
    // 刷新点赞吐糟数量
    refreshEvaluate() {
      const { newsId } = this
      updateArticleLike({ newsId }).then(res => {
        if (res.code === 200) {
          this.evaluateLike = res.data.find(item => item.action === 'like') || { count: 0 }
          this.evaluateBelittle = res.data.find(item => item.action === 'belittle') || { count: 0 }
        } else this.$message.error(res.msg)
      })
    },
    // 展开所有评论
    handleShowMore(item) {
      this.showId = this.showId === item.id ? 0 : item.id
    },
    // 删除评价内容
    handleDelete(item) {
      const commentId = item.id
      deleteArticleComment({ commentId }).then(res => {
        if (res.code === 200) {
          function findAndDelete(list) {
            list.forEach((item, index) => {
              if (item.id === commentId) {
                list.splice(index, 1)
                return
              }
              if (item.children) findAndDelete(item.children)
            })
          }
          findAndDelete(this.evaluateList)
        } else this.$message.error(res.msg)
      })
    }
  }
}
</script>
<style scoped>
body {
  min-width: 1200px;
  background: #f9f9f9;
}
.box {
  width: 1200px;
  margin: 10px auto 10px;
  box-shadow: 0px 0px 26px 0px rgba(0, 0, 0, 0.05);
  border-radius: 6px;
  min-height: 512px;
  background: #f9f9f9;
}
.h2 {
  text-align: center;
  margin-top: 50px;
  font-size: 34px;
}
.p1 {
  font-size: 16px;
  margin: 0;
  color: #666666;
  padding: 0 30px 30px;
}
.con {
  padding: 30px;
}
/deep/ .con img {
  max-width: calc(100% - 2em);
}
.disclaimers {
  padding: 0 30px 30px;
  font-size: 12px;
  line-height: 1.5em;
  color: #999999;
}
/deep/ .con p {
  margin: 0;
  padding: 0;
  line-height: 2;
  text-indent: 2em;
}
</style>
<style scoped lang="scss">
.evaluate {
  border-top: 1px solid #e0e3e9;
  margin: 20px 30px;
  padding-top: 30px;
  &-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    &-item {
      display: inline-flex;
      flex-direction: column;
      align-items: center;
      cursor: pointer;
      margin: 0 15px;
      img {
        width: 50px;
        height: 50px;
      }
      span {
        margin-top: 8px;
        font-size: 12px;
        color: #999999;
      }
      &.disabled {
        cursor: not-allowed;
      }
      ::v-deep {
        .el-badge__content.is-fixed {
          right: 15px;
        }
      }
    }
  }
  &-title {
    font-size: 20px;
    margin: 15px 0;
  }
  &-con {
    width: 100%;
    padding: 10px 0;
    &-answer {
      width: 100%;
      display: flex;
      align-items: flex-start;
      position: relative;
      padding: 15px;
      &-img {
        width: 50px;
        height: 50px;
        margin-right: 15px;
        border-radius: 50%;
        border: 1px solid #c5cad9;
      }
      &-input {
        width: 100%;
        display: flex;
        flex-direction: column;
        background: #ffffff;
        border-radius: 8px;
        border: 1px solid #c5cad9;
        ::v-deep {
          .el-textarea__inner {
            font-family: inherit;
            border: 0;
            padding: 10px;
            height: 120px;
            border-radius: 8px;
          }
        }
      }
      &-button {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 50px;
        border-top: 1px solid #c5cad9;
        padding: 0 20px;
        .button {
          cursor: pointer;
          font-size: 16px;
          color: #ffffff;
          line-height: 32px;
          width: 80px;
          border-radius: 22px;
          background-color: #2e73f3;
          text-align: center;
          &.disabled {
            cursor: not-allowed;
            background-color: #cbd6e2;
          }
        }
      }
    }
    &-w100 {
      width: 100%;
    }
    &-item {
      width: 100%;
      display: flex;
      align-items: flex-start;
      position: relative;
      padding: 15px 0 15px 15px;
      &.children {
        background-color: #f1f1f3;
        border-radius: 8px;
        margin-bottom: 10px;
      }
      &-img {
        width: 50px;
        height: 50px;
        margin-right: 15px;
        border-radius: 50%;
        border: 1px solid #c5cad9;
      }
      &-con {
        width: 100%;
        display: flex;
        flex-direction: column;
        padding-right: 15px;
        margin-bottom: 10px;
        &-title {
          line-height: 50px;
          font-size: 16px;
          color: #333333;
        }
        &-con {
          display: table-cell;
          vertical-align: middle;
          font-size: 14px;
          line-height: 1.75em;
          color: #666666;
          padding-bottom: 10px;
        }
        &-time {
          display: flex;
          align-items: center;
          justify-content: space-between;
          font-size: 12px;
          color: #999999;
          .anwser {
            margin-left: 10px;
            cursor: pointer;
            span {
              margin-left: 5px;
            }
            &:hover {
              color: #2e73f3;
            }
          }
        }
        &-open {
          font-size: 14px;
          line-height: 40px;
          color: #666666;
          cursor: pointer;
          &:hover {
            color: #2e73f3;
          }
        }
      }
    }
  }
  .inline-flex {
    display: inline-flex;
    align-items: center;
  }
}
</style>
