<template>
  <div class="newBox bgcf9 vh-85">
    <!-- 搜索 -->
    <div class="custom-search flex" style="padding-top: 18px">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
        <el-form-item label="交接内容" prop="content">
          <el-input v-model="queryParams.content" placeholder="请输入交接内容" @keyup.enter.native="handleQuery" clearable></el-input>
        </el-form-item>
        <el-form-item label="请假人" prop="createBy">
          <el-input v-model="queryParams.createBy" placeholder="请输入请假人" @keyup.enter.native="handleQuery" clearable></el-input>
        </el-form-item>
        <el-form-item label="部门">
          <el-cascader v-model="searchDept" :options="searchDeptList" :props="{ value: 'id', label: 'label', children: 'children' }" @change="handleQuery" clearable></el-cascader>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="请选择状态" @change="handleQuery" clearable>
            <el-option label="已交接" :value="1" />
            <el-option label="待交接" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      <button type="button" class="custom-search-add pointer" @click="handleAdd">
        <i class="el-icon-plus"></i>
        新建工作交接
      </button>
    </div>
    <!-- tab切换 -->
    <div class="classify flex">
      <div class="classify-item" :class="{ active: queryParams.identity === item.value }" v-for="item in tabOptions" :key="item.value" @click="handleTab(item.value)">{{ item.label }}</div>
    </div>
    <!-- 列表 -->
    <div style="padding: 15px 20px">
      <el-empty v-if="total === 0 && !loading" />
      <el-table v-loading="loading" ref="table" stripe :data="list" class="custom-table" v-if="total">
        <el-table-column align="center" type="index" label="序号"></el-table-column>
        <el-table-column align="center" prop="department" label="部门" show-overflow-tooltip>
          <template slot-scope="{ row }">{{ findInTree(row.deptId) && findInTree(row.deptId).label }}</template>
        </el-table-column>
        <el-table-column align="center" prop="workDetail" label="工作内容详情" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <span class="table-link" @click="handleDetail(row)">{{ row.content }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="createBy" label="创建人" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" prop="status" label="状态" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <span :class="row.status === 1 ? 'color-success' : 'color-orange'">{{ row.status === 1 ? '已交接' : '待交接' }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="handoverUser" label="接收人" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" prop="remark" label="备注" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" prop="createTime" label="交接时间" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" prop="handoverDate" label="接手时间" show-overflow-tooltip>
          <template slot-scope="{ row }">{{ row.handoverDate || '-' }}</template>
        </el-table-column>
        <el-table-column align="center" label="操作" width="220">
          <template slot-scope="{ row }">
            <el-button class="table-btn primary" @click="handleDetail(row)" v-if="queryParams.identity !== 'creator'">查看详情</el-button>
            <el-button class="table-btn" :class="{ disabled: isDisabled(row), 'primary hasbg': !isDisabled(row) }" :disabled="isDisabled(row)" @click="handleClaim(row)" v-if="queryParams.identity == 'handover' && row.status == 0">接手</el-button>
            <el-button class="table-btn danger hasbg" @click="handleClaim(row)" v-if="queryParams.identity == 'handover' && row.status == 1">撤回</el-button>
            <el-button class="table-btn primary" @click="handleUpdate(row)" v-if="queryParams.identity === 'creator'">修改</el-button>
            <el-button class="table-btn danger hasbg" @click="handleDelete(row)" v-if="queryParams.identity === 'creator'">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="custom-pagination" v-if="total > 0 && !loading">
        <pagination :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
      </div>
    </div>
    <!-- 新建工作交接 -->
    <create-dialog ref="create" :deptList="deptList" @refresh="refreshList" v-if="isCreate" />
  </div>
</template>

<script>
import createDialog from './create'
import { deptTreeSelect, listUser } from '@/api/system/user'
import { workHandoverList, workHandoverDelete, workHandover } from '@/api/workHandover'

export default {
  name: 'WorkJoin',
  components: { createDialog },
  data() {
    return {
      tabOptions: [
        // { label: '所有交接', value: '' },
        { label: '我创建的', value: 'creator' },
        { label: '我参与的', value: 'handover' },
        { label: '抄送给我的', value: 'copy' }
      ],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        status: '', // 状态：0-待交接，1-已交接
        identity: 'creator', // 身份：copy-抄送人，creator-创建人,handover-接手人
        deptId: '', // 部门id
        createBy: '', // 请假人
        content: '' // 内容
      },
      list: [],
      total: 0,
      loading: true,
      deptList: [],
      searchDept: [],
      searchDeptList: [],
      isCreate: false
    }
  },
  created() {
    const identity = this.$route.query.identity
    if (identity) this.queryParams.identity = identity
    this.getDeptList()
    this.getList()
  },
  methods: {
    // 获取部门列表
    getDeptList() {
      deptTreeSelect().then(res => {
        this.deptList = res.data
        this.searchDeptList = res.data.length === 1 ? JSON.parse(JSON.stringify(res?.data?.[0]?.children)) : JSON.parse(JSON.stringify(res.data))
      })
    },
    // 从树结构内找到相同id
    findInTree(id) {
      const tree = JSON.parse(JSON.stringify(this.deptList))
      const arr = this.treeToArr(tree)
      return arr.find(item => item.id === id)
    },
    // 树结构转扁平数组
    treeToArr(data, children = 'children', parentId, res = []) {
      data.forEach(v => {
        v.parentId = parentId
        res.push(v)
        if (v[children] && v[children].length) this.treeToArr(v[children], children, v.id, res)
      })
      return res
    },
    // tab切换
    handleTab(value) {
      this.queryParams.identity = value
      this.getList()
    },
    // 获取列表
    // prettier-ignore
    getList() {
      this.loading = true
      workHandoverList(this.queryParams).then(res => {
        const { code, msg, rows, total } = res
        if (code === 200) {
          this.list = rows
          this.total = total
        } else {
          this.$message.error(msg)
        }
      }).finally(() => {
        this.loading = false
      })
    },
    // 刷新列表
    refreshList() {
      this.isCreate = false
      workHandoverList(this.queryParams).then(res => {
        const { code, msg, rows, total } = res
        if (code === 200) {
          this.$set(this, 'list', rows)
          this.$set(this, 'total', total)
        } else this.$message.error(msg)
      })
    },
    // 搜索
    handleQuery() {
      if (this.searchDept.length) this.queryParams.deptId = this.searchDept[this.searchDept.length - 1]
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 重置
    resetQuery() {
      if (this.searchDept.length) {
        this.searchDept = []
        this.queryParams.deptId = ''
      }
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 新建工作交接
    handleAdd() {
      this.isCreate = true
      this.$nextTick(() => {
        this.$refs.create.handleCreate()
      })
    },
    // 查看详情
    handleDetail(row) {
      this.isCreate = true
      this.$nextTick(() => {
        this.$refs.create.handleUpdate(row, true)
      })
    },
    // 是否禁用
    isDisabled(row) {
      if (!row.leaveEndTime) return false
      return new Date().getTime() > new Date(row.leaveEndTime).getTime()
    },
    // 立即领取
    handleClaim(row) {
      if (this.isDisabled(row)) return
      const data = { id: row.id, status: row.status === 0 ? 1 : 0 }
      workHandover(data).then(res => {
        if (res.code === 200) {
          this.$message.success('操作成功')
          this.$set(row, 'status', row.status === 0 ? 1 : 0)
        } else this.$message.error(res.msg)
      })
    },
    // 修改
    handleUpdate(row) {
      this.isCreate = true
      this.$nextTick(() => {
        this.$refs.create.handleUpdate(row)
      })
    },
    // 删除
    // prettier-ignore
    handleDelete(row) {
      this.$confirm(`是否删除此工作交接?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        workHandoverDelete({ handoverId: row.id }).then(res => {
          if (res.code === 200) {
            this.$message.success('删除成功')
            this.getList()
          } else this.$message.error(res.msg)
        })
      }).catch(() => {
      })
    }
  }
}
</script>
<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';
.custom-table {
  .el-button {
    padding: 0;
  }
}
</style>
