<template>
  <div>
    <el-dialog v-dialogDragBox :title="title" :visible.sync="open" width="1150px" class="custom-dialog">
      <div class="formBox">
        <el-form class="custom-form" ref="form" :model="form" :rules="rules" label-width="7em" :disabled="disabled">
          <el-row :gutter="10">
            <el-col :span="12">
              <el-form-item label="请假日期" prop="leaveDate">
                <el-date-picker v-model="form.leaveDate" type="datetimerange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" style="width: 100%" :picker-options="pickerOptions" :default-time="['00:00:00', '23:59:59']" format="yyyy-MM-dd HH:mm:ss"></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="联系电话" prop="phone">
                <el-input v-model="form.phone" maxlength="11" show-word-limit placeholder="请输入联系电话" style="width: 100%"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="交接内容及详细描述" prop="content">
                <el-input type="textarea" v-model="form.content" :autosize="{ minRows: 3, maxRows: 6 }" placeholder="请输入交接内容及详细描述"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="备注内容" prop="remark">
                <el-input v-model="form.remark" placeholder="请输入备注内容"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="接手人" prop="handover">
                <el-cascader v-model="form.handover" :options="approvalsOptions" :props="{ emitPath: false }" filterable :show-all-levels="false" placeholder="请选择接手人" style="width: 100%" @change="handleChange('handover')"></el-cascader>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="抄送人" prop="copyUsers">
                <el-cascader v-model="form.copyUsers" :options="approvalsOptions" :props="copyProps" filterable :show-all-levels="false" placeholder="请选择抄送人" @change="handleChange('copyUsers')" style="width: 100%"></el-cascader>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn" @click="handleClose">取 消</el-button>
        <el-button class="custom-dialog-btn primary" @click="handleSubmit">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { listUser } from '@/api/system/user'
import { workHandoverAdd, workHandoverUpdate } from '@/api/workHandover'

export default {
  props: {
    deptList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      title: '',
      open: false,
      form: {},
      rules: {
        leaveDate: [{ required: true, message: '请选择请假日期', trigger: 'change' }],
        handoverDate: [{ required: true, message: '请选择交接日期', trigger: 'change' }],
        phone: [
          { required: true, message: '请输入联系电话', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码格式', trigger: 'blur' }
        ],
        content: [{ required: true, message: '请输入交接内容及详细描述', trigger: 'change' }],
        handover: [{ required: true, message: '请选择接手人', trigger: 'change' }],
        copyUsers: [{ required: true, message: '请选择抄送人', trigger: 'change' }]
      },
      userList: [
        { value: 1, label: '张三' },
        { value: 2, label: '李四' },
        { value: 3, label: '王五' },
        { value: 4, label: '赵六' },
        { value: 5, label: '孙七' },
        { value: 6, label: '周八' },
        { value: 7, label: '吴九' },
        { value: 8, label: '郑十' }
      ],
      approvalsOptions: [],
      copyCheckList: [], // 抄送人
      copyProps: {
        multiple: true,
        emitPath: false
      },
      disabled: false,
      pickerOptions: {
        disabledDate: time => {
          return time.getTime() < Date.now() - 8.64e7
        }
      },
      treeCache: null
    }
  },
  computed: {
    userInfo() {
      return this.$store.getters.info
    }
  },
  created() {
    this.getApprovalsOptions()
  },
  methods: {
    // 查询部门、查询用户构造树
    async getApprovalsOptions() {
      const dept = JSON.parse(JSON.stringify(this.deptList))
      const user = await listUser()
      this.salespersonOptions = user.rows || []
      const children = dept?.[0]?.children || []
      const deptData = [...children, ...[{ id: -1, label: '其他', value: 'dept-0' }]] || []
      const userData = user.rows || []
      const getChildren = data => {
        data.forEach(item => {
          item.value = 'dept-' + item.id
          if (item.children) {
            getChildren(item.children)
          } else {
            item.children = []
          }
        })
      }
      getChildren(deptData)
      const addChildren = data => {
        data.forEach(item => {
          userData.forEach(user => {
            if (item.id === user.deptId && item.children) {
              item.children.push({
                userId: user.userId,
                label: user.realName || user.nickName,
                value: user.userId,
                disabled: user.status == '1',
                userName: user.userName
              })
            }
            if (item.id === -1 && (!user.deptId || user.deptId === dept?.[0]?.id) && item.children) {
              item.children.push({
                userId: user.userId,
                label: user.realName || user.nickName,
                value: user.userId,
                disabled: user.status == '1',
                userName: user.userName
              })
            }
          })
          if (item.children && item.children.length) {
            addChildren(item.children)
          }
        })
      }
      addChildren(deptData)
      this.approvalsOptions = deptData
    },
    // 抄送人选择
    handleChange(type) {
      this.copyCheckList = []
      if (type == 'copyUsers') {
        this.form.copyUsers.forEach(el => {
          const cc = this.findInTree(this.approvalsOptions, el)
          let obj = {
            copyUserId: el,
            copyUser: cc.label
          }
          this.copyCheckList.push(obj)
        })
      }
      if (type == 'handover') {
        const el = this.form.handover
        const handover = this.findInTree(this.approvalsOptions, el)
        this.form.handoverUser = handover?.label || ''
      }
    },
    // 从树结构内找到相同id
    findInTree(tree, id) {
      if (!this.treeCache) {
        this.treeCache = new Map()
        const flatTree = nodes => {
          nodes.forEach(node => {
            if (node.userName) this.treeCache.set(node.userId, node)
            if (node.children?.length) {
              flatTree(node.children)
            }
          })
        }
        flatTree(tree)
      }
      return this.treeCache.get(id)
    },
    // 表单重置
    reset() {
      this.form = {
        content: undefined, // 内容
        copyUsers: [], // 抄送人
        deptId: undefined, // 部门id
        handover: undefined, // 接手人
        handoverId: undefined, // 工作交接ID
        handoverUser: undefined, // 接手人姓名
        leaveDate: [new Date(), new Date(Date.now() + 24 * 60 * 60 * 1000)], // 请假日期
        leaveStartTime: undefined, // 开始时间
        leaveEndTime: undefined, // 结束时间
        phone: undefined, // 联系电话
        remark: undefined // 备注
      }
      this.resetForm('form')
    },
    // 新建
    handleCreate() {
      this.reset()
      this.disabled = false
      this.title = '新建工作交接'
      this.open = true
    },
    // 编辑
    handleUpdate(row = {}, disabled = false) {
      this.reset()
      this.form = { ...row }
      this.form.handoverId = row.id
      this.form.leaveDate = [row.leaveStartTime, row.leaveEndTime]
      this.form.copyUsers = row?.copyUserId?.split(',')
      this.title = disabled ? '查看工作交接' : '编辑工作交接'
      this.open = true
      this.disabled = disabled
    },
    // 关闭
    handleClose() {
      this.reset()
      this.open = false
    },
    // 提交
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          const { content, handover, handoverId, handoverUser, leaveDate, phone, remark } = this.form
          const { deptId } = this.userInfo
          const data = { content, copyUsers: this.copyCheckList, deptId, handover, handoverId, handoverUser, leaveStartTime: leaveDate[0] && this.parseTime(leaveDate[0], '{y}-{m}-{d} {h}:{i}:{s}'), leaveEndTime: leaveDate[1] && this.parseTime(leaveDate[1], '{y}-{m}-{d} {h}:{i}:{s}'), phone, remark }
          if (handoverId) {
            workHandoverUpdate(data).then(res => {
              const { code, msg } = res
              if (code == 200) {
                this.$message.success(msg)
                this.handleClose()
                this.$emit('refresh')
              } else this.$message.error(msg)
            })
          } else {
            workHandoverAdd(data).then(res => {
              const { code, msg } = res
              if (code == 200) {
                this.$message.success(msg)
                this.handleClose()
                this.$emit('refresh')
              } else this.$message.error(msg)
            })
          }
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';
.formBox {
  padding: 10px 20px 0;
}
::v-deep {
  .el-form.custom-form {
    .el-form-item__label {
      line-height: 20px;
      min-height: 40px;
      display: inline-flex;
      align-items: center;
      font-weight: normal;
      text-align: left;
    }
  }
}
</style>
