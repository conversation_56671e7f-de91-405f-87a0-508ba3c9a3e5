<template>
    <div class="newBox bgcf9 vh-85" v-if="checkPermi(['sys:warranty:list'])">
        <!-- 搜索 -->
        <div class="custom-search flex">
            <div class="flex">
                <div class="custom-search-form flex">
                    <input type="text" v-model="queryParams.name" placeholder="请输入物料名称" class="custom-search-input"
                        @keyup.enter="handleQuery" />
                    <button type="button" class="custom-search-button pointer" @click="handleQuery">
                        <i class="el-icon-search"></i>
                        搜索
                    </button>
                </div>
            </div>
            <div class="classify-toolbar">
                <right-toolbar :search="false" @queryTable="getList" :columns="columns"></right-toolbar>
            </div>
        </div>

        <!-- 表格数据 -->
        <div class="tableBox">
            <el-table v-loading="loading" ref="table" stripe :data="list" row-key="id" :key="key" style="width: 100%"
                class="custom-table custom-table-cell5">
                <el-table-column align="center" type="index" label="序号" v-if="columns[0].visible"></el-table-column>
                <el-table-column align="left" label="物料名称" show-overflow-tooltip v-if="columns[1].visible">
                    <template slot-scope="{ row }">
                        <span>{{ row.materialName }}</span>
                    </template>
                </el-table-column>
                <el-table-column align="center" label="规格" show-overflow-tooltip v-if="columns[2].visible">
                    <template slot-scope="{ row }">
                        <span>{{ row.model }}</span>
                    </template>
                </el-table-column>
                <el-table-column align="center" prop="materialNumber" label="物料编码" show-overflow-tooltip
                    v-if="columns[3].visible">
                    <template slot-scope="{ row }">
                        <span>{{ row.materialNumber }}</span>
                    </template>
                </el-table-column>
                <el-table-column align="center" label="单位" show-overflow-tooltip v-if="columns[4].visible" width="100">
                    <template slot-scope="{ row }">
                        <span>{{ row.unit }}</span>
                    </template>
                </el-table-column>
                <el-table-column align="center" label="总需求量" show-overflow-tooltip v-if="columns[5].visible">
                    <template slot-scope="{ row }">
                        <span>{{ row.useQty || 0 }}</span>
                    </template>
                </el-table-column>
                <el-table-column align="center" label="金蝶库存" show-overflow-tooltip v-if="columns[6].visible">
                    <template slot-scope="{ row }">
                        <span>{{ row.qty || 0 }}</span>
                    </template>
                </el-table-column>
                <el-table-column align="center" label="自由客库存" show-overflow-tooltip v-if="columns[7].visible">
                    <template slot-scope="{ row }">
                        <span>{{ (row.qty - row.useQty) || 0 }}</span>
                    </template>
                </el-table-column>
                <el-table-column align="center" label="操作" width="320px">
                    <template slot-scope="{ row }">
                        <button type="button" class="table-btn primary" @click="handleHistory(row)"
                            v-if="checkPermi(['sys:inventory:history'])">库存历史</button>
                        <button type="button" class="table-btn danger" @click="handleEdit(row)"
                            v-if="checkPermi(['sys:inventory:supplement'])">修改库存</button>
                    </template>
                </el-table-column>
            </el-table>

            <div class="custom-pagination">
                <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
                    :limit.sync="queryParams.pageSize" @pagination="getList" />
            </div>
        </div>

        <!-- 产品详情 -->
        <product-dialog ref="productInfo"></product-dialog>

        <!-- 库存历史 -->
        <el-dialog v-dialogDragBox title="库存历史" :visible.sync="historyOpen" width="1150px" class="custom-dialog">
            <div class="p20">
                <el-table v-loading="historyLoading" ref="table" stripe :data="historyList" row-key="id"
                    style="width: 100%" class="custom-table custom-table-cell5">
                    <el-table-column align="center" type="index" label="序号"></el-table-column>
                    <el-table-column align="left" label="合同编号" show-overflow-tooltip>
                        <template slot-scope="{ row }">
                            <!-- <span class="table-link" @click="handleContractView(row)">{{ row.serial ? (row.serial + ' > ') : '' }}</span> -->
                            <span>{{ row.serial }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" label="物料名称" show-overflow-tooltip>
                        <template slot-scope="{ row }">
                            <span>{{ row.materialName }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" label="物料编码" show-overflow-tooltip>
                        <template slot-scope="{ row }">
                            <span>{{ row.materialNumber }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" label="变动日期" show-overflow-tooltip>
                        <template slot-scope="{ row }">
                            <span>{{ parseTime(row.useDate, '{y}-{m}-{d} {h}:{i}') }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" label="自由客库存" show-overflow-tooltip>
                        <template slot-scope="{ row }">
                            <span>{{ (historyStock - row.useQty) || 0 }}</span>
                            <!-- <el-dropdown placement="bottom" trigger="hover">
                                <span class="el-dropdown-link">
                                    <span v-if="row.productStockBefore > 0" style="color: #15CB69;">{{
                                        row.productStockBefore }}</span>
                                    <span v-else-if="row.productStockBefore < 0" style="color: #F43F3F;">{{
                                        row.productStockBefore }}</span>
                                    <span v-else style="color: #666666;">{{ row.productStockBefore }}</span>
                                    <i class="el-icon-arrow-down el-icon--right"></i>
                                </span>
                                <el-dropdown-menu slot="dropdown">
                                    <el-dropdown-item v-for="item in productStockList" :key="item.id"
                                        @click.native="handleProductStock(item)">
                                        <div style="display: flex; justify-content: space-between; width: 220px;">
                                            <span style="font-weight: 400; font-size: 12px; color: #666666;">{{
                                                item.deptName }}</span>
                                            <span style="font-weight: 500; font-size: 14px; color: #2E73F3;">{{
                                                item.productStock }}</span>
                                            <span style="font-weight: 400; font-size: 12px; color: #666666;">{{
                                                item.unit }}</span>
                                        </div>
                                    </el-dropdown-item>
                                </el-dropdown-menu>
                            </el-dropdown> -->
                        </template>
                    </el-table-column>
                    <el-table-column align="center" label="单位" show-overflow-tooltip>
                        <template slot-scope="{ row }">
                            <div>
                                <span>{{ row.unit }}</span>
                            </div>
                        </template>
                    </el-table-column>
                    <!-- <el-table-column align="center" label="采购数量" show-overflow-tooltip>
                        <template slot-scope="{ row }">
                            <span style="font-weight: 500; font-size: 14px; color: #2E73F3;">{{ row.purchaseQuantity
                                }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" label="产品库存（扣减后）" show-overflow-tooltip>
                        <template slot-scope="{ row }">
                            <span v-if="row.productStockAfter > 0" style="color: #F35D09;">{{ row.productStockAfter
                                }}</span>
                            <span v-else-if="row.productStockAfter < 0" style="color: #F43F3F;">{{
                                row.productStockAfter }}</span>
                            <span v-else style="color: #666666;">{{ row.productStockAfter }}</span>
                        </template>
                    </el-table-column> -->
                </el-table>
            </div>
            <div slot="footer">
                <!-- <el-button @click="historyOpen = false" size="medium" style="width: 200px">取 消</el-button> -->
                <!-- <el-button type="primary" @click="handleHistorySubmit" size="medium" style="width: 200px">提
                    交</el-button> -->
                <el-button type="primary" size="medium" @click="historyOpen = false" style="width: 200px">关
                    闭</el-button>
            </div>
        </el-dialog>

        <!-- 修改库存 -->
        <el-dialog v-dialogDrag title="修改产品库存" :visible.sync="editOpen" width="1150px" class="custom-dialog">
            <div class="p20">
                <div class="info_box">
                    <!-- <image-preview :src="info.picture1_oss || imgPath + info.picture1" :width="50" :height="50" /> -->
                    <div class="info_text_box">
                        <div class="info_text_top">
                            <span class="info_text_title">物料名称：{{ info.materialName }}</span>
                            <span class="info_text_subtitle">当前库存：{{ (info.qty - info.useQty) || 0 }}</span>
                        </div>
                        <div class="info_text_bottom">
                            <span class="info_text_subtitle">规格：{{ info.model }}</span>
                            <span class="info_text_subtitle">物料编码：{{ info.materialNumber }}</span>
                            <!-- <span class="info_text_subtitle">材质：{{ 'Q195' }}</span>
                            <span class="info_text_subtitle">表面处理：{{ '热镀锌' }}</span> -->
                            <span class="info_text_subtitle">单位：{{ info.unit }}</span>
                            <!-- <span class="info_text_subtitle">属性：{{ '抗震配件' }}</span>
                            <span class="info_text_subtitle">重量：{{ '0.85 kg' }}</span> -->
                        </div>
                    </div>
                </div>
                <el-form ref="editForm" :model="editForm" :rules="rules" inline label-width="100px"
                    class="el-form--label-left" style="margin-top: 30px;">
                    <el-form-item label="修改库存" prop="quantity">
                        <el-input v-model="editForm.quantity" placeholder="请输入您要修改的库存数" style="width: 444px;">
                            <!-- <span slot="suffix" style="margin-right: 20px;">根</span> -->
                        </el-input>
                    </el-form-item>
                </el-form>
            </div>
            <div slot="footer">
                <el-button @click="editOpen = false" size="medium" style="width: 200px">取 消</el-button>
                <el-button type="primary" @click="handleEditSubmit" size="medium" style="width: 200px">提 交</el-button>
            </div>
        </el-dialog>

        <!-- 物料详情 -->
        <material-detail ref="materialDetail" @callBack="handleCallBack" v-if="showMaterialDetail" />

    </div>
</template>

<script>
import { parseTime } from '@/utils/ruoyi'
import { checkPermi } from '@/utils/permission'
import ProductDialog from '@/views/public/product/dialog'
import { inventoryHistory, inventoryList, inventoryQty, inventorySupplement } from '@/api/inventory'
import MaterialDetail from '@/views/kingdee/material/detail'

export default {
    name: 'inventory',
    components: {
        ProductDialog,
        MaterialDetail
    },
    data() {
        return {
            key: 1,
            // 搜索条件
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                number: undefined,
                name: undefined
            },
            // 加载
            loading: true,
            // 列表数据
            list: [],
            // 总条数
            total: 0,
            // 显隐列
            columns: [
                { key: 0, label: `序号`, visible: true },
                { key: 1, label: `物料名称`, visible: true },
                { key: 2, label: `规格`, visible: true },
                { key: 3, label: `物料编码`, visible: true },
                { key: 4, label: `单位`, visible: true },
                { key: 5, label: `总需求量`, visible: true },
                { key: 6, label: `金蝶库存`, visible: true },
                { key: 7, label: `自由客库存`, visible: true },
            ],
            userInfo: {},
            // 库存历史
            historyOpen: false,
            historyLoading: false,
            historyList: [],
            productStockList: [
                { id: 1, deptName: '部门A', productStock: 100, unit: '件' },
                { id: 2, deptName: '部门B', productStock: 200, unit: '件' },
            ],
            // 修改库存
            editOpen: false,
            editForm: {
                quantity: 0,
            },
            info: {},
            rules: {
                quantity: [
                    { required: true, message: '请输入库存', trigger: 'blur' },
                    // { type: 'number', message: '库存必须为数字', trigger: 'blur' },
                ],
            },
            // 物料详情
            showMaterialDetail: false,
            historyStock: 0

        }
    },
    computed: {
        userId() {
            return this.$store.getters.info.userId
        }
    },
    created() {
        this.userInfo = this.$store.state.user.info
        this.getList()
    },
    methods: {
        checkPermi,
        parseTime,

        // 查询数据
        // 查询列表
        getList() {
            this.loading = true
            inventoryList(this.queryParams).then(res => {
                const { code, msg, rows, total } = res
                if (code === 200) {
                    rows.forEach(el => {
                        el.qty = 0;
                        el.stocks.forEach(m => {
                            console.log(m.qty)
                            el.qty = el.qty + Number(m.qty)
                        })
                    });
                    this.list = rows
                    this.total = total
                    this.loading = false
                } else this.$message.error(msg)
            })
        },
        // 搜索
        handleQuery() {
            this.queryParams.pageNum = 1
            this.getList()
        },
        // 查看详情
        handleProductView(item) {
            this.$refs.productInfo.handleView(item)
        },
        handleHistory(row) {
            this.historyLoading = true
            let data = {
                number: row.materialNumber,
            }
            inventoryHistory(data).then(res => {
                const { code, msg, data } = res
                if (code === 200) {
                    this.historyStock = data.stock ? data.stock[row.version].reduce((total, item) => total + item.qty, 0) : 0
                    this.historyList = data.useData
                    this.historyOpen = true
                    this.historyLoading = false
                } else {
                    this.$message.error(msg)
                    this.historyLoading = false
                }

            })
        },
        handleEdit(item) {
            this.editOpen = true
            console.log(item)
            this.info = item
        },
        handleSupplement() { },
        handleSupplementSubmit() { },
        // 查看合同
        handleContractView(row) {

        },

        handleCustomerView() { },
        handleHistorySubmit() {
            this.historyOpen = false
            this.getList()
        },
        handleEditSubmit() {
            this.$refs.editForm.validate(valid => {
                if (!valid) return false
                let data = {
                    number: this.info.materialNumber,
                    quantity: this.editForm.quantity,
                }
                inventorySupplement(data).then(res => {
                    const { code, msg } = res
                    if (code === 200) {
                        this.$message.success('补充成功')
                        this.editOpen = false
                        this.getList()
                    } else this.$message.error(msg)
                })
            })

        },

        // 查看物料
        handleMaterialNumber(number) {
            this.showMaterialDetail = true
            this.$nextTick(() => {
                this.$refs.materialDetail.getInfo(number)
            })
        },
        // 关闭物料详情
        handleCallBack(flag = false) {
            this.showMaterialDetail = false
            this.showOutboundDetail = false
            if (flag) this.getList()
        },
    }
}
</script>

<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';

.el-button.organge {
    background-color: #f35d09;
    border-color: #f35d09;
    color: #fff;

    &:hover {
        opacity: 0.8;
    }
}

.tableBox {
    padding: 20px;
}

.custom-search {
    align-items: center;
    justify-content: space-between;
}

.custom-table ::v-deep {
    .el-table__body-wrapper .table-switch {
        .el-switch__label {
            width: calc(3em + 20px) !important;
        }

        .el-switch__core {
            width: calc(3em + 20px) !important;
        }
    }
}

.p20 {
    padding: 0 20px;
}

.info_box {
    height: 92px;
    background: #F0F3F9;
    border-radius: 5px;
    padding: 0 20px;
    display: flex;
    align-items: center;
    justify-content: flex-start;

    .info_text_box {
        margin-left: 33px;
        display: flex;
        flex-direction: column;

        .info_text_top {
            display: flex;
            align-items: center;
            width: 100%;

            .info_text_title {
                font-weight: 500;
                margin-right: 50px;
                font-size: 14px;
                color: #333333;
            }

            .info_text_subtitle {
                font-weight: 400;
                font-size: 14px;
                color: #666666;
            }
        }

        .info_text_bottom {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            width: 100%;
            margin-top: 10px;

            .info_text_subtitle {
                margin-right: 30px;
                font-weight: 400;
                font-size: 14px;
                color: #666666;
            }
        }
    }
}
</style>
