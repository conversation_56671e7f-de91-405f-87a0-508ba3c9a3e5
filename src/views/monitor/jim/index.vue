<template>
  <div class="newBox bgcf9 vh-85">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" @submit.native.prevent>
      <el-form-item label="用户账号" prop="userName">
        <el-input v-model="queryParams.userName" placeholder="请输入用户账号" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <template v-if="list.length">
      <el-table stripe v-loading="loading" :data="list.slice((pageNum - 1) * pageSize, pageNum * pageSize)" style="width: 100%" class="custom-table">
        <el-table-column label="序号" type="index" align="center">
          <template slot-scope="scope">
            <span>{{ (pageNum - 1) * pageSize + scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column label="会话编号" align="center" prop="tokenId" :show-overflow-tooltip="true" />
        <el-table-column label="登录名称" align="center" prop="userName" :show-overflow-tooltip="true" />
        <el-table-column label="昵称" align="center" prop="nickName" :show-overflow-tooltip="true" />
        <el-table-column label="部门名称" align="center" prop="deptName" />
        <el-table-column label="主机" align="center" prop="ipaddr" :show-overflow-tooltip="true" />
        <el-table-column label="登录地点" align="center" prop="loginLocation" :show-overflow-tooltip="true" />
        <el-table-column label="操作系统" align="center" prop="os" />
        <el-table-column label="登录时间" align="center" prop="loginTime" width="180">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.loginTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="备注" align="center" prop="remark" :show-overflow-tooltip="true" />
      </el-table>
      <div class="custom-pagination">
        <pagination v-show="total > 0" :total="total" :page.sync="pageNum" :limit.sync="pageSize" />
      </div>
    </template>
    <el-empty v-else :description="loading ? '加载中...' : '暂无数据'" />
  </div>
</template>

<script>
import { realtimeList, list } from '@/api/monitor/online'

export default {
  name: 'Jimonline',
  data() {
    return {
      loading: true,
      total: 0,
      list: [],
      pageNum: 1,
      pageSize: 10,
      queryParams: {
        userName: undefined
      }
    }
  },
  watch: {
    userChange: {
      deep: true,
      handler(val) {
        this.getList()
      }
    }
  },
  computed: {
    userChange() {
      return this.$store.getters.userChange
    }
  },
  created() {
    this.getList()
  },
  methods: {
    // 列表
    getList() {
      this.loading = true
      realtimeList(this.queryParams).then(res => {
        const { data } = res
        this.list = data
        this.total = data.length || 0
        this.loading = false
      })
    },
    // 搜索
    handleQuery() {
      this.pageNum = 1
      this.getList()
    },
    // 重置搜索
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    }
  }
}
</script>
<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
.newBox {
  padding: 20px 20px 0;
}
.custom-pagination {
  height: 50px;
}
</style>
