<template>
  <div class="newBox" :class="{ 'bgcf9 vh-85': !isProp }">
    <!-- 搜索 -->
    <div class="custom-search" style="padding-top: 18px">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" @submit.native.prevent>
        <el-form-item label="物料名称" prop="name">
          <el-input v-model="queryParams.name" placeholder="请输入物料名称" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="small" @click="handleResetQuery">重置</el-button>
          <el-button type="primary" icon="el-icon-plus" size="small" @click="handleAdd" v-if="!isProp">添加拆零物料</el-button>
        </el-form-item>
      </el-form>
    </div>
    <!-- 列表 -->
    <div style="padding: 15px 20px">
      <el-table v-loading="loading" ref="allTable" stripe :data="list" style="width: 100%" class="custom-table" v-if="list.length > 0" @row-click="handleChoose">
        <el-table-column width="44" label="" align="center" v-if="isProp">
          <template slot-scope="{ row }">
            <el-radio v-removeAriaHidden class="radio" v-model="plusData.materialId" :label="row.materialId" v-if="isMaterialId"><span></span></el-radio>
            <el-radio v-removeAriaHidden class="radio" v-model="plusData.id" :label="row.id" v-if="!isMaterialId"><span></span></el-radio>
          </template>
        </el-table-column>
        <el-table-column align="center" type="index" label="序号"></el-table-column>
        <el-table-column align="center" prop="materialName" label="物料名称" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <span class="table-link" @click.stop="handleView(row && row.product)">{{ row && row.materialName }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="图片">
          <template slot-scope="{ row }">
            <image-preview :src="row && row.product && formatProductImg(row.product)" :width="50" :height="50" />
          </template>
        </el-table-column>
        <el-table-column align="center" prop="specs" label="规格" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" prop="materialQuality" label="材质" show-overflow-tooltip>
          <template slot-scope="{ row }">{{ row && row.product && row.product.materialQuality }}</template>
        </el-table-column>
        <el-table-column align="center" prop="unit" label="单位" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" prop="weight" label="重量" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" prop="surface" label="表面处理" show-overflow-tooltip>
          <template slot-scope="{ row }">{{ row && row.product && row.product.surface }}</template>
        </el-table-column>
        <el-table-column align="center" prop="createBy" label="创建人" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" prop="createTime" label="创建时间" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" prop="unitPrice" label="单价(元)">
          <template slot-scope="{ row }">
            <el-tooltip effect="dark" content="点击查看价格变更记录" :disabled="!row.unitPrice">
              <span class="table-price pointer" @click.stop="handlePriceLog(row)">{{ row.unitPrice || '-' }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="sort" label="排序" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" label="操作" width="330" v-if="!isProp">
          <template slot-scope="{ row }">
            <el-button class="table-btn" icon="el-icon-view" @click="handleDetail(row)">详情</el-button>
            <el-button class="table-btn primary" icon="el-icon-edit" @click="handleUpdate(row)">修改</el-button>
            <el-button class="table-btn danger" icon="el-icon-delete" @click="handleDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-empty v-if="!loading && list.length === 0" description="暂无数据" />
      <!-- 分页 -->
      <div class="custom-pagination">
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
      </div>
    </div>
    <!-- 产品详情 -->
    <product-dialog ref="productInfo" append-body @callback="showProduct = false" v-if="showProduct" />
    <!-- 新增/修改/详情 -->
    <create ref="create" @callback="handleBack" v-if="showCreate" :categoryList="categoryList" />
    <!-- 价格变更记录 -->
    <el-dialog v-dialogDragBox title="价格变更记录" :visible.sync="logOpen" width="750px" class="custom-dialog" append-to-body>
      <div style="padding: 0 20px">
        <el-table stripe :data="logList" class="custom-table" v-if="logList.length > 0">
          <el-table-column align="center" type="index" label="序号"></el-table-column>
          <el-table-column align="center" prop="unitPrice" label="单价(元)" show-overflow-tooltip>
            <template slot-scope="{ row }">
              <span class="table-price">{{ row.unitPrice }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="createBy" label="操作人" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="createTime" label="操作时间" show-overflow-tooltip></el-table-column>
        </el-table>
        <el-empty v-if="logList.length === 0" description="暂无数据" />
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn primary" @click="logOpen = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { getMaterialList, deleteMaterial, getMaterialPriceRecord } from '@/api/splitPart'
import ProductDialog from '@/views/public/product/dialog'
import Create from './create'
import { getlist } from '@/api/purchase/category'

export default {
  name: 'SplitMaterial',
  components: { ProductDialog, Create },
  props: {
    isProp: {
      type: Boolean,
      default: false
    },
    plusData: {
      type: Object,
      default: () => ({})
    },
    isMaterialId: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // 搜索
      queryParams: {
        pageNum: 1, // 当前页码
        pageSize: 10, // 每页条数
        name: undefined // 物料名称
      },
      list: [],
      total: 0,
      loading: false,
      showProduct: false,
      showCreate: false,
      // 价格变更记录
      logOpen: false,
      logList: [],
      categoryList: []
    }
  },
  created() {
    // 获取列表
    this.getList()
    // 获取产品分类
    this.getCategoryList()
  },
  methods: {
    // 获取产品分类
    getCategoryList() {
      getlist().then(res => {
        const { code, data } = res
        if (code === 200) {
          const list = data.find(item => item.id === 216)
          this.categoryList = list?.children || []
        }
      })
    },
    // 获取列表
    // prettier-ignore
    getList() {
      this.loading = true
      getMaterialList(this.queryParams).then(res => {
        const { code, rows, total, msg } = res
        if (code === 200) {
          this.list = rows
          this.total = total
        } else this.$message.error(msg)
      }).finally(() => {
        this.loading = false
      })
    },
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 重置
    handleResetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 查看产品详情
    handleView(row) {
      if (!row || Object.keys(row).length === 0) {
        this.$message.warning('产品信息不存在')
        return
      }
      this.showProduct = true
      this.$nextTick(() => {
        this.$refs.productInfo.handleView(row)
      })
    },
    // 新增
    handleAdd() {
      this.showCreate = true
      this.$nextTick(() => {
        this.$refs.create.handleCreate()
      })
    },
    // 详情
    handleDetail(row) {
      this.showCreate = true
      this.$nextTick(() => {
        this.$refs.create.handleDetail(row)
      })
    },
    // 编辑
    handleUpdate(row) {
      this.showCreate = true
      this.$nextTick(() => {
        this.$refs.create.handleUpdate(row)
      })
    },
    // 回调
    handleBack(flag = false) {
      this.showCreate = false
      if (flag) this.getList()
    },
    // 删除
    // prettier-ignore
    handleDelete(row) {
      const { id } = row
      this.$confirm('确定删除该物料吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteMaterial({ id }).then(res => {
          const { code, msg } = res
          if (code === 200) {
            this.$message.success(msg)
            this.getList()
          } else this.$message.error(msg)
        })
      }).catch(() => {})
    },
    // 价格变更记录
    // prettier-ignore
    handlePriceLog(row) {
      const { id: partMaterialId } = row
      if (!partMaterialId) {
        this.$message.warning('物料信息不存在')
        return
      }
      const loading = this.$loading({
        lock: true,
        text: '查询中…',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      getMaterialPriceRecord({ partMaterialId }).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          this.logList = data
          this.logOpen = !!data.length
          if (!data.length) this.$message.warning('暂无价格变更记录')
        } else this.$message.error(msg)
      }).finally(() => {
        loading.close()
      })
    },
    // 选择物料
    handleChoose(row) {
      if (!this.isProp) return
      this.$emit('update:plusData', row)
    }
  }
}
</script>
<style scoped lang="scss">
@import '@/assets/styles/custom-new.scss';
</style>
