<template>
  <div>
    <el-dialog v-dialogDragBox :title="title" :visible.sync="open" width="1150px" class="custom-dialog" :before-close="handleBeforeClose">
      <div style="padding: 0 20px">
        <template v-if="step == 1">
          <el-form ref="queryForm" :model="queryForm" size="small" inline @submit.native.prevent>
            <el-row :gutter="10">
              <el-col :span="6">
                <el-form-item label="产品名称">
                  <el-input v-model="queryForm.productName" placeholder="请输入产品名称" clearable @keyup.enter.native="handleQuery" />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="产品编码">
                  <el-input v-model="queryForm.productCode" placeholder="请输入产品编码" clearable @keyup.enter.native="handleQuery" />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="产品规格">
                  <el-input v-model="queryForm.specs" placeholder="请输入产品规格" clearable @keyup.enter.native="handleQuery" />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="产品型号">
                  <el-input v-model="queryForm.model" placeholder="请输入产品型号" clearable @keyup.enter.native="handleQuery" />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="产品材质">
                  <el-input v-model="queryForm.materialQuality" placeholder="请输入产品材质" clearable @keyup.enter.native="handleQuery" />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="表面处理">
                  <el-input v-model="queryForm.surface" placeholder="请输入表面处理" clearable @keyup.enter.native="handleQuery" />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="产品分类" v-if="categoryList.length > 0">
                  <el-select v-model="queryForm.categoryId" placeholder="请选择产品分类" clearable @change="handleQuery">
                    <el-option v-for="item in categoryList" :key="item.id" :label="item.name" :value="item.id"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item>
                  <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
                  <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
          <el-table v-loading="loading" ref="allTable" stripe :data="list" style="width: 100%" class="custom-table" @row-click="handleChoose">
            <el-table-column width="44" label="" align="center">
              <template slot-scope="{ row }">
                <el-radio v-removeAriaHidden class="radio" v-model="form.materialId" :label="row.id"><span></span></el-radio>
              </template>
            </el-table-column>
            <el-table-column align="center" type="index" label="序号"></el-table-column>
            <el-table-column align="center" prop="productName" label="产品名称" show-overflow-tooltip>
              <template slot-scope="{ row }">
                <span class="table-link" @click.stop="handleView(row)">{{ row.productName }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" label="图片">
              <template slot-scope="{ row }">
                <image-preview :src="row && formatProductImg(row)" :width="50" :height="50" />
              </template>
            </el-table-column>
            <el-table-column align="center" prop="specs" label="规格" show-overflow-tooltip></el-table-column>
            <el-table-column align="center" prop="productCode" label="产品编码" show-overflow-tooltip></el-table-column>
            <el-table-column align="center" prop="materialQuality" label="材质" show-overflow-tooltip></el-table-column>
            <el-table-column align="center" label="操作" min-width="110">
              <template slot-scope="{ row }">
                <el-button class="table-btn primary" icon="el-icon-choose" @click="handleChoose(row)">选择该产品</el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="custom-pagination">
            <pagination v-show="total > 0" :total="total" :page.sync="queryForm.pageNum" :limit.sync="queryForm.pageSize" @pagination="getList" />
          </div>
        </template>
        <template v-if="step == 2">
          <div class="product" v-if="product && product.productName">
            <image-preview :src="product && formatProductImg(product)" :width="100" :height="100" />
            <div class="flex flex-column flex1">
              <div class="flex align-center mb15">
                <span class="title" @click="handleView(product)">{{ product.productName }}</span>
                <div class="flex align-center" v-if="product.unitPrice">
                  <span class="text">当前单价</span>
                  <span class="price">{{ product.unitPrice }}</span>
                </div>
              </div>
              <div class="flex align-center flex-wrap">
                <div class="flex align-center mr30">
                  <span class="text">规格</span>
                  <span class="info">{{ product.specs }}</span>
                </div>
                <div class="flex align-center mr30">
                  <span class="text">产品编码</span>
                  <span class="info">{{ product.productCode }}</span>
                </div>
                <div class="flex align-center mr30">
                  <span class="text">材质</span>
                  <span class="info">{{ product.materialQuality }}</span>
                </div>
                <div class="flex align-center mr30">
                  <span class="text">表面处理</span>
                  <span class="info">{{ product.surface }}</span>
                </div>
                <div class="flex align-center mr30">
                  <span class="text">单位</span>
                  <span class="info">{{ product.unit }}</span>
                </div>
                <div class="flex align-center mr30">
                  <span class="text">属性</span>
                  <span class="info">{{ product.attribute }}</span>
                </div>
                <div class="flex align-center mr30">
                  <span class="text">重量</span>
                  <span class="info">{{ product.weight }} kg</span>
                </div>
              </div>
            </div>
          </div>
          <el-form :model="form" :rules="rules" ref="form" label-position="left" label-width="60px" :disabled="disabled">
            <el-row :gutter="10">
              <el-col :span="12">
                <el-form-item label="规格" prop="specs">
                  <el-input v-model="form.specs" placeholder="请输入规格" clearable />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="单位" prop="unit">
                  <el-input v-model="form.unit" placeholder="请输入单位" clearable />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="重量" prop="weight">
                  <el-input v-model="form.weight" placeholder="请输入重量" clearable>
                    <template slot="suffix">kg</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="单价" prop="unitPrice">
                  <el-input v-model="form.unitPrice" placeholder="请输入单价" clearable>
                    <template slot="suffix">元</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="排序" prop="sort">
                  <el-input-number :precision="0" v-model="form.sort" :min="0"></el-input-number>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </template>
      </div>
      <div slot="footer">
        <template v-if="step == 1">
          <el-button class="custom-dialog-btn" @click="handleClose()">取 消</el-button>
          <el-button class="custom-dialog-btn primary" :class="{ disabled: !form.materialId }" :disabled="!form.materialId" @click="handleNext">下一步</el-button>
        </template>
        <template v-if="step == 2">
          <template v-if="disabled">
            <el-button class="custom-dialog-btn primary" @click="handleClose()">关 闭</el-button>
          </template>
          <template v-if="!disabled">
            <el-button class="custom-dialog-btn" @click="handleClose()">取 消</el-button>
            <el-button class="custom-dialog-btn primary" @click="handleSubmit">确 定</el-button>
          </template>
        </template>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { getTypeProducts } from '@/api/system/product'
import { isNumber, isNumberLength } from '@/utils/validate'
import { createMaterial, updateMaterial } from '@/api/splitPart'

export default {
  props: {
    categoryList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      step: 1,
      queryForm: {
        pageNum: 1,
        pageSize: 10,
        categoryId: undefined,
        productName: undefined,
        productCode: undefined,
        specs: undefined,
        model: undefined,
        materialQuality: undefined,
        surface: undefined
      },
      title: '添加拆零物料',
      open: false,
      form: {},
      rules: {
        specs: [{ required: true, message: '请输入规格', trigger: 'blur' }],
        unit: [{ required: true, message: '请输入单位', trigger: 'blur' }],
        weight: [
          { required: true, message: '请输入重量', trigger: 'blur' },
          { validator: isNumber, message: '请输入正确的重量', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: 'blur' }
        ],
        unitPrice: [
          { required: true, message: '请输入单价', trigger: 'blur' },
          { validator: isNumber, message: '请输入正确的单价', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: 'blur' }
        ]
      },
      disabled: false,
      // 产品列表
      list: [],
      loading: true,
      total: 0,
      product: {}
    }
  },
  methods: {
    // 表单重置
    reset() {
      this.form = {
        partMaterialId: undefined,
        materialId: undefined,
        specs: undefined,
        unit: undefined,
        unitPrice: undefined,
        weight: undefined,
        sort: undefined
      }
      this.resetForm('form')
    },
    // 添加
    handleCreate() {
      this.reset()
      this.step = 1
      this.title = '添加拆零物料'
      this.disabled = false
      this.getList()
    },
    // 产品列表
    // prettier-ignore
    getList() {
      this.loading = true
      let query = { ...this.queryForm }
      if (!query.categoryId) query.categoryId = 216
      getTypeProducts(query).then(res => {
        const { code, rows, total } = res
        if (code === 200) {
          this.list = rows
          this.total = total
          if (!this.open) this.open = true
        } else this.$message.error(msg)
      }).finally(() => {
        this.loading = false
      })
    },
    // 查询
    handleQuery() {
      this.queryForm.pageNum = 1
      this.getList()
    },
    // 重置
    resetQuery() {
      this.queryForm = {
        pageNum: 1,
        pageSize: 10,
        categoryId: undefined,
        productName: undefined,
        productCode: undefined,
        specs: undefined,
        model: undefined,
        materialQuality: undefined,
        surface: undefined
      }
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 选择产品
    handleChoose(row) {
      this.form = { materialId: row.id, specs: row.specs, unit: row.unit, weight: row.weight }
      this.product = row
    },
    // 下一步
    handleNext() {
      this.step = 2
      this.form.sort = 99
    },
    // 修改
    handleUpdate(row) {
      this.reset()
      this.step = 2
      this.form = { partMaterialId: row.id, specs: row.specs, unit: row.unit, weight: row.weight, unitPrice: row.unitPrice, sort: row.sort }
      this.product = { ...(row && row.product), unitPrice: row.unitPrice }
      this.title = '修改拆零物料'
      this.disabled = false
      this.open = true
    },
    // 详情
    handleDetail(row) {
      this.reset()
      this.step = 2
      this.form = { partMaterialId: row.id, specs: row.specs, unit: row.unit, weight: row.weight, unitPrice: row.unitPrice, sort: row.sort }
      this.product = { ...(row && row.product), unitPrice: row.unitPrice }
      this.title = '拆零物料详情'
      this.disabled = true
      this.open = true
    },
    // 关闭
    handleClose(flag = false) {
      this.open = false
      this.$emit('callback', flag)
    },
    // 关闭前
    handleBeforeClose() {
      this.handleClose()
    },
    // 查看产品详情
    handleView(data) {
      this.$parent.handleView(data)
    },
    // 提交
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          const { partMaterialId } = this.form
          if (partMaterialId) {
            updateMaterial(this.form).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success(msg)
                this.handleClose(true)
              } else this.$message.error(msg)
            })
          } else {
            createMaterial(this.form).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success(msg)
                this.handleClose(true)
              } else this.$message.error(msg)
            })
          }
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
.gap-50 {
  gap: 50px;
}
.mb15 {
  margin-bottom: 15px;
}
.mr30 {
  margin-right: 30px;
}
.product {
  padding: 20px;
  background-color: #f0f3f9;
  border-radius: 5px;
  display: flex;
  gap: 30px;
  margin-bottom: 20px;
  line-height: 25px;
  .title {
    color: $blue;
    cursor: pointer;
    transition: all 0.3s;
    margin-right: 30px;
    &:hover {
      text-decoration: underline;
    }
  }
  .text {
    color: $disabled;
    margin-right: 5px;
  }
  .price {
    color: $red;
    &:before {
      content: '¥';
    }
  }
  .info {
    color: $font;
  }
}
.el-row {
  .el-col {
    ::v-deep .el-form-item {
      display: flex;
      align-items: center;
      width: 100%;
      overflow: hidden;
      .el-form-item__label {
        flex-shrink: 0;
      }
      .el-form-item__content {
        flex: 1;
        overflow: hidden;
        width: 100%;
      }
    }
  }
}
</style>
