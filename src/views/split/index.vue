<template>
  <div>
    <head-tpl :is-login="isLogin" />
    <div class="container">
      <el-form ref="form" :model="form" :rules="rules" label-position="left" label-width="80px" hide-required-asterisk>
        <div class="splitBox">
          <div class="splitBox-title">基础条件</div>
          <div class="splitBox-conditions">
            <el-row :gutter="10">
              <el-col :span="11">
                <el-form-item label="标高" prop="height">
                  <el-input v-model="form.height" placeholder="请输入标高">
                    <template slot="suffix">米（m）</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="11">
                <el-form-item label="镀锌方式" prop="isRdx">
                  <el-radio-group v-model="form.isRdx">
                    <el-radio v-removeAriaHidden :label="true">热镀锌</el-radio>
                    <el-radio v-removeAriaHidden :label="false">电镀锌</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="11">
                <el-form-item label="结构类型" prop="structure">
                  <el-radio-group v-model="form.structure">
                    <el-radio v-removeAriaHidden v-for="item in structureOptions" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="11">
                <el-form-item label="拆零类型" prop="useTo">
                  <el-radio-group v-model="form.useTo">
                    <el-radio v-removeAriaHidden :label="1">拆零</el-radio>
                    <el-radio v-removeAriaHidden :label="2">一键报价</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="splitBox-title">拆分报价</div>
          <div class="splitBox-info">
            <el-form-item label="" prop="content" label-width="0">
              <el-input type="textarea" class="splitBox-textarea" v-model="form.content" :placeholder="`请将内容粘贴至此处\n注意：如需拆分多个，请换行，每行为一个。例如\nKZS-DN65-T 1\nKZS-DN65-TL 2`" resize="none" :autosize="{ minRows: 10 }" @change="handleContentChange"></el-input>
            </el-form-item>
            <div class="splitBox-file">
              <el-upload ref="upload" accept=".txt, .text" action="#" :auto-upload="false" :on-change="handleFileChange" :show-file-list="false" drag class="splitBox-upload">
                <div class="splitBox-upload-tip" v-if="!fileTitle">
                  <el-button size="small" type="primary" icon="el-icon-plus"></el-button>
                  <span>您也可以点此上传文档，或拖动文档至此处</span>
                </div>
                <div class="splitBox-upload-tip" v-else>
                  <i class="el-icon-success" style="font-size: 40px; color: green"></i>
                  <span>{{ fileTitle }}</span>
                </div>
              </el-upload>
            </div>
          </div>
          <div class="splitBox-btn">
            <el-button class="splitBox-btn-btn" @click="handleSubmit">确认拆零</el-button>
          </div>
        </div>
        <div class="splitBox" v-if="showList">
          <template v-if="form.useTo === 2">
            <div class="splitBox-title">一键报价</div>
            <el-table :data="list" stripe class="custom-table" :key="Math.random()">
              <el-table-column prop="content" label="内容" align="center" show-overflow-tooltip></el-table-column>
              <el-table-column prop="count" label="数量" align="center" width="140">
                <template slot-scope="{ row }">
                  <el-input-number size="mini" v-model="row.count" :min="0.001" @change="numberChange(row)" @blur="numberBlur(row)"></el-input-number>
                </template>
              </el-table-column>
              <el-table-column prop="unitPrice" label="金额" align="center">
                <template slot-scope="{ row }">
                  <span class="table-price">{{ row.unitPrice ? '￥' + row.unitPrice : '-' }}</span>
                </template>
              </el-table-column>
              <el-table-column align="center" width="120">
                <template slot="header">
                  <div class="flex align-center">
                    <span style="flex-shrink: 0; margin-right: 5px">税点</span>
                    <el-input-number :controls="false" v-model="form.taxPercent" size="mini" placeholder="请输入" @change="taxPercentChange"></el-input-number>
                  </div>
                </template>
                <template slot-scope="{ row }">
                  <span>{{ form.taxPercent || '-' }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="taxAmount" label="含税" align="center">
                <template slot-scope="{ row }">
                  <span class="color-orange">{{ row.taxAmount ? '￥' + row.taxAmount : '-' }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="total" label="报价" align="center">
                <template slot-scope="{ row }">
                  <span class="table-price">{{ row.total ? '￥' + row.total : '-' }}</span>
                </template>
              </el-table-column>
              <el-table-column label="操作" align="center" width="120">
                <template slot-scope="scope">
                  <el-button type="text" size="mini" @click="handleDelete(scope.$index)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
            <div class="splitBox-total">
              <span class="text">商品总价{{ form.taxPercent ? '(含税)' : '' }}</span>
              <span class="price">{{ total }}</span>
            </div>
          </template>
          <template v-else>
            <div class="splitBox-title">拆零列表</div>
            <el-table :data="list" stripe class="custom-table" :key="Math.random()">
              <el-table-column type="index" label="序号" width="50"></el-table-column>
              <el-table-column prop="materialName" label="名称" align="center" show-overflow-tooltip></el-table-column>
              <el-table-column prop="specs" label="规格" align="center" show-overflow-tooltip></el-table-column>
              <el-table-column prop="unit" label="单位" align="center"></el-table-column>
              <el-table-column prop="weight" label="重量" align="center"></el-table-column>
              <el-table-column prop="number" label="数量" align="center" width="140">
                <template slot-scope="{ row }">
                  <el-input-number size="mini" v-model="row.number" :min="0.001" @change="numberChange(row)" @blur="numberBlur(row)"></el-input-number>
                </template>
              </el-table-column>
              <el-table-column prop="unitPrice" label="单价" align="center">
                <template slot-scope="{ row }">
                  <span class="table-price">{{ row.unitPrice ? '￥' + row.unitPrice : '-' }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="amount" label="金额" align="center" show-overflow-tooltip>
                <template slot-scope="{ row }">
                  <span class="table-price">{{ row.amount ? '￥' + row.amount : '-' }}</span>
                </template>
              </el-table-column>
              <el-table-column align="center" width="120">
                <template slot="header">
                  <div class="flex align-center">
                    <span style="flex-shrink: 0; margin-right: 5px">税点</span>
                    <el-input-number :controls="false" v-model="form.taxPercent" size="mini" placeholder="请输入" @change="taxPercentChange"></el-input-number>
                  </div>
                </template>
                <template slot-scope="{ row }">
                  <span>{{ form.taxPercent || '-' }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="taxAmount" label="含税" align="center">
                <template slot-scope="{ row }">
                  <span class="color-orange">{{ row.taxAmount ? '￥' + row.taxAmount : '-' }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="quotedPrice" label="报价" align="center">
                <template slot-scope="{ row }">
                  <span class="color-blue">{{ row.quotedPrice ? '￥' + row.quotedPrice : '-' }}</span>
                </template>
              </el-table-column>
              <el-table-column label="操作" align="center" width="120">
                <template slot-scope="scope">
                  <el-dropdown @command="handleCommand(scope.row, scope.$index, $event)">
                    <div>
                      <span>操作</span>
                      <i class="el-icon-caret-bottom"></i>
                    </div>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item command="change">更换</el-dropdown-item>
                      <el-dropdown-item command="delete">删除</el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>
                </template>
              </el-table-column>
            </el-table>
            <div class="splitBox-total">
              <span class="text">商品总价{{ form.taxPercent ? '(含税)' : '' }}</span>
              <span class="price">{{ total }}</span>
            </div>
          </template>
          <div class="splitBox-button">
            <el-button class="splitBox-button-item" @click="handleSave">保存{{ form.useTo === 2 ? '一键报价' : '拆零结果' }}</el-button>
            <el-button class="splitBox-button-item hasbg" @click="handleExport">导出列表</el-button>
          </div>
        </div>
      </el-form>
      <!-- 更换物料 -->
      <el-dialog v-dialogDragBox title="更换物料" :visible.sync="changeOpen" width="1150px" class="custom-dialog">
        <div>
          <material-list is-prop is-material-id :plusData.sync="changeData" ref="materialList" />
        </div>
        <div slot="footer">
          <el-button class="custom-dialog-btn" @click="changeOpen = false">取 消</el-button>
          <el-button class="custom-dialog-btn primary" :class="{ disabled: !(changeData && changeData.materialId) }" :disabled="!(changeData && changeData.materialId)" @click="handleChangeSubmit">确 定</el-button>
        </div>
      </el-dialog>
      <!-- 保存拆零结果 -->
      <el-dialog v-dialogDragBox :title="`保存${form.useTo === 2 ? '一键报价' : '拆零结果'}`" :visible.sync="saveOpen" width="1150px" class="custom-dialog">
        <div style="padding: 0 20px">
          <div class="saveTip">
            <i class="el-icon-warning"></i>
            <span>请为此次{{ form.useTo === 2 ? '一键报价' : '拆零结果' }}备注命名，命名后保存可随时至管理后台拆零管理＞拆零记录处查看本次{{ form.useTo === 2 ? '一键报价' : '拆零结果' }}</span>
          </div>
          <el-form ref="saveForm" :model="saveForm" :rules="saveRules" label-position="left" label-width="105px" hide-required-asterisk>
            <el-row :gutter="10">
              <el-col :span="11">
                <el-form-item :label="`${form.useTo === 2 ? '一键报价' : '拆零结果'}备注`" prop="nameMark">
                  <el-input v-model="saveForm.nameMark" :placeholder="`请输入${form.useTo === 2 ? '一键报价' : '拆零结果'}备注`" clearable></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="11">
                <el-form-item label="保存至" prop="storeId">
                  <el-select ref="collect" v-model="saveForm.storeId" placeholder="请选择" style="width: 100%" @visible-change="v => visibleChange(v, 'collect')" filterable clearable>
                    <el-option v-for="item in collectList" :key="item.storeId" :label="item.dirName" :value="item.storeId"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
          <div class="saveLine"></div>
        </div>
        <div slot="footer">
          <el-button class="custom-dialog-btn" @click="saveOpen = false">取 消</el-button>
          <el-button class="custom-dialog-btn primary" @click="handleSaveSubmit">确 定</el-button>
        </div>
      </el-dialog>
    </div>
    <foot-tpl />
  </div>
</template>
<script>
import { getToken } from '@/utils/auth'
import headTpl from '@/views/public/components/head'
import footTpl from '@/views/public/components/foot'
import { isNumber, isNumberLength } from '@/utils/validate'
import { splitPart, saveMaterialSplitRecord } from '@/api/splitPart'
import MaterialList from '@/views/split/material'
import { getlist, addlist } from '@/api/houtai/shoucang'
import { createSplitTemplate } from '@/vendor/ExportTemplate'

export default {
  components: { headTpl, footTpl, MaterialList },
  data() {
    return {
      isLogin: false,
      form: {},
      rules: {
        height: [
          { required: true, message: '请输入标高', trigger: 'blur' },
          { validator: isNumber, message: '请输入正确的标高', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: 'blur' }
        ]
      },
      structureOptions: [
        { label: '钢结构', value: 'steels' },
        { label: '混凝土', value: 'concrete' }
      ],
      fileTitle: '',
      showList: false,
      list: [],
      // 更换拆零物料弹窗
      changeOpen: false,
      changeIndex: undefined,
      changeData: {},
      // 保存拆零结果
      saveOpen: false,
      saveForm: {},
      saveRules: {
        nameMark: [{ required: true, message: `请输入备注`, trigger: 'blur' }],
        storeId: [{ required: true, message: '请选择保存至', trigger: 'blur' }]
      },
      collectList: []
    }
  },
  computed: {
    total() {
      let num = 0
      this.list.forEach(row => {
        if (this.form.useTo === 2) num += row.total || 0
        else num += row.quotedPrice || 0
      })
      return parseFloat(num.toFixed(5))
    }
  },
  created() {
    this.isLogin = !!getToken()
    this.reset()
  },
  methods: {
    reset() {
      this.form = {
        height: undefined,
        isRdx: true,
        useContent: true, // 文本true 文件false
        structure: this.structureOptions[0].value || '',
        content: undefined,
        file: undefined,
        taxPercent: undefined,
        useTo: 1
      }
      this.resetForm('form')
      this.list = []
      this.showList = false
    },
    // 文件上传
    handleFileChange(file) {
      this.form.file = file.raw
      this.fileTitle = file.name
      this.form.useContent = false
      this.form.content = undefined
      this.list = []
      this.showList = false
    },
    // 内容变化
    handleContentChange() {
      this.form.useContent = true
      this.form.file = undefined
      this.fileTitle = ''
      this.list = []
      this.showList = false
    },
    // 确认拆零
    // prettier-ignore
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          const { content = '', file } = this.form
          if (!content.trim() && !file) {
            this.$message.error('请输入拆零内容或上传文档')
            return
          }
          let data = {}
          if (content) {
            const contentList = content.trim().split('\n')
            data = { ...this.form, content: contentList.join(',') }
          } else data = { ...this.form }
          const loading = this.$loading({
            lock: true,
            text: '拆零中...',
            background: 'rgba(0, 0, 0, 0.7)'
          })
          splitPart(data).then(res => {
            const { code, data, msg } = res
            if (code === 200) {
              if (!data.length) {
                this.$message.error('拆零结果为空')
                this.showList = false
                return
              }
              if (this.form.useTo !== 2) {
                data.map(item => {
                  item.quotedPrice = parseFloat((item.amount + (item.taxAmount || 0)).toFixed(5))
                })
              }
              this.list = data
              this.showList = true
            } else this.$message.error(msg)
          }).finally(() => {
            loading.close()
          })
        }
      })
    },
    // 数量变化
    numberChange(row) {
      if (this.form.useTo === 2) {
        const percent = this.form.taxPercent * 0.01 || 0
        row.taxAmount = parseFloat((row.unitPrice * row.count * percent).toFixed(5))
        row.total = parseFloat((row.unitPrice * row.count + row.taxAmount).toFixed(5))
      } else {
        row.amount = parseFloat((row.unitPrice * row.number).toFixed(5))
        row.taxAmount = parseFloat((row.amount * (this.form.taxPercent || 0) * 0.01).toFixed(5))
        row.quotedPrice = parseFloat((row.amount + (row.taxAmount || 0)).toFixed(5))
      }
    },
    // 数量失去焦点
    numberBlur(row) {
      if (!row.number) row.number = 1
    },
    // 税率变化
    taxPercentChange() {
      this.list.forEach(row => {
        this.numberChange(row)
      })
    },
    // 操作
    handleCommand(row, index, command) {
      if (command === 'change') {
        this.changeData = row
        this.changeIndex = index
        this.changeOpen = true
      } else if (command === 'delete') {
        this.list.splice(index, 1)
      }
    },
    // 删除
    handleDelete(index) {
      this.list.splice(index, 1)
    },
    // 更换物料
    handleChangeSubmit() {
      const { materialId, materialName, specs, unit, weight, number = 1, unitPrice } = this.changeData
      const amount = parseFloat((unitPrice * number).toFixed(5))
      const taxAmount = parseFloat((amount * (this.form.taxPercent || 0) * 0.01).toFixed(5))
      const quotedPrice = parseFloat((amount + taxAmount).toFixed(5))
      const data = { materialId, materialName, specs, unit, weight, number, unitPrice, amount, taxAmount, quotedPrice }
      this.$set(this.list, this.changeIndex, data)
      this.changeData = {}
      this.changeOpen = false
    },
    // 保存拆零结果
    async handleSave() {
      const err = await this.getCollectList()
      if (err) return
      const { height, isRdx, structure, taxPercent = 0, useTo } = this.form
      this.saveForm = { entityList: this.list, height, isIncludeTax: taxPercent ? 1 : 0, isRdx: isRdx ? 1 : 0, nameMark: undefined, storeId: undefined, structure, taxPercent, useTo }
      this.resetForm('saveForm')
      this.saveOpen = true
    },
    // 查询收藏夹
    getCollectList() {
      return new Promise((resolve, reject) => {
        getlist({ type: 'splitPartInfo' }).then(res => {
          const { code, msg, data } = res
          if (code === 200) {
            this.collectList = data
            resolve()
          } else {
            this.$message.error(msg)
            reject(msg)
          }
        })
      })
    },
    // 文件夹选择
    visibleChange(visible, refName) {
      if (visible) {
        const ref = this.$refs[refName]
        let product = ref.$refs.popper
        if (product.$el) product = product.$el
        if (!Array.from(product.children).some(v => v.className === 'el-template-menu__list')) {
          const el = document.createElement('ul')
          el.className = 'el-template-menu__list'
          el.style = 'border-top: solid 1px #e4e7ed;padding:0;margin:0;list-style: none;'
          el.innerHTML = `<li class="flex align-center pointer" style="line-height: 40px;color: #2e73f3;padding:0 20px;font-size: 13px;"><i class="font-blue el-icon-plus"></i> 新建文件夹</li>`
          product.appendChild(el)
          // 新增按钮点击事件
          el.onclick = () => {
            this.addItem(null)
            if (ref.toggleDropDownVisible) ref.toggleDropDownVisible(false)
            else ref.visible = false
          }
        }
      }
    },
    // 新增文件夹
    addItem() {
      this.$prompt('请输入新的文件夹名称', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(async ({ value }) => {
        if (!value || value == '') this.$message.error('文件夹名称不能为空!')
        else {
          addlist({ dirName: value, type: 'splitPartInfo' }).then(res => {
            this.$modal.msgSuccess('新增成功')
            this.getCollectList()
          })
        }
      })
    },
    // 保存拆零结果提交
    handleSaveSubmit() {
      this.$refs.saveForm.validate(valid => {
        if (valid) {
          let data = { ...this.saveForm }
          if (data.useTo === 2) {
            data.entityList2 = data.entityList.map(item => {
              return {
                content: item.content,
                count: item.count,
                total: item.total,
                unitPrice: item.unitPrice
              }
            })
            delete data.entityList
          }
          saveMaterialSplitRecord(data).then(res => {
            const { code, msg } = res
            if (code === 200) {
              this.reset()
              this.$modal.msgSuccess('保存成功')
              this.saveOpen = false
            } else this.$message.error(msg)
          })
        }
      })
    },
    // 导出列表
    async handleExport() {
      if (!this.list || !this.list.length) {
        this.$message.warning('暂无数据可导出')
        return
      }
      const loading = this.$loading({
        lock: true,
        text: '导出中...',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      try {
        const hasTax = !!this.form.taxPercent
        const filename = this.form.useTo === 2 ? '一键报价' : '拆零结果'
        const options = { useTo: this.form.useTo, hasTax: hasTax }
        await createSplitTemplate({
          data: this.list,
          filename,
          options
        })
        this.$message.success('导出成功')
      } catch (error) {
        console.error('导出失败:', error)
        this.$message.error('导出失败: ' + (error.message || '未知错误'))
      } finally {
        loading.close()
      }
    }
  }
}
</script>
<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
.container {
  padding: 30px 0;
  background-color: #f9f9f9;
}
.splitBox {
  width: 1200px;
  background-color: #fff;
  padding: 20px;
  border-radius: 10px;
  min-height: 500px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  box-shadow: 0 1px 9px 0 rgba(0, 0, 0, 0.07);
  border-radius: 2px;
  padding: 0 20px;
  &-title {
    font-size: 16px;
    color: #333;
    padding: 15px 0;
  }
  &-conditions {
    padding: 20px 20px 0;
    background-color: #f8f9fb;
    border: 1px solid #cbd7e2;
    border-radius: 5px;
  }
  &-info {
    border: 1px solid #2e73f3;
    border-radius: 5px;
    overflow: hidden;
  }
  &-textarea {
    border: 0;
    border-radius: 0;
    ::v-deep {
      .el-textarea__inner {
        border: 0;
        border-radius: 0;
      }
    }
  }
  &-file {
    background-color: #eef0f8;
    padding-bottom: 20px;
    border-radius: 0 0 5px 5px;
  }
  &-upload {
    &-tip {
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: flex-end;
      span {
        font-size: 14px;
        color: #333;
        line-height: 2em;
        margin-top: 10px;
      }
    }
    ::v-deep {
      .el-upload {
        width: 100%;
      }
      .el-upload-dragger {
        width: 100%;
        height: 100px;
        border: 0;
        background-color: #eef0f8;
      }
    }
  }
  &-download {
    text-align: center;
    border-radius: 0 0 5px 5px;
    span {
      font-size: 14px;
      color: #2e73f3;
      display: inline-block;
      width: 100%;
      height: 30px;
      line-height: 30px;
      text-decoration: underline;
      cursor: pointer;
      transition: all 0.3s;
      &:hover {
        color: #f43f3f;
      }
    }
  }
  &-btn {
    margin: 20px 0;
    display: flex;
    justify-content: flex-end;
    padding-top: 15px;
    border-top: 1px solid #e2e6f3;
    &-btn {
      width: 270px;
      height: 50px;
      background-color: #cbd6e2;
      color: #fff;
      &.disabled,
      &:hover {
        background-color: #2e73f3;
      }
    }
  }
  &-total {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    border: 1px solid #d91f56;
    background-color: #fcf7f8;
    border-radius: 5px;
    height: 56px;
    padding: 0 20px;
    margin-top: 20px;
    .text {
      font-size: 12px;
      color: #666;
      margin-right: 20px;
    }
    .price {
      font-size: 18px;
      color: #ec2454;
      &::before {
        content: '￥';
        font-size: 14px;
        margin-right: 5px;
      }
    }
  }
  &-button {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin: 20px 0;
    &-item {
      width: 270px;
      height: 50px;
      background-color: #f2f6ff;
      color: #2e73f3;
      border: 1px solid #2e73f3;
      border-radius: 5px;
      transition: all 0.3s;
      &.hasbg {
        background-color: #2e73f3;
        color: #fff;
        &:hover {
          opacity: 0.8;
        }
      }
      &:hover {
        background-color: #2e73f3;
        color: #fff;
      }
    }
  }
  & + .splitBox {
    margin-top: 20px;
  }
}
.saveTip {
  padding: 10px 15px;
  line-height: 22px;
  border-radius: 60px;
  background-color: #fff3f3;
  color: #ed4040;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 5px;
  margin-bottom: 20px;
  i {
    font-size: 20px;
    color: #fe9191;
  }
}
.saveLine {
  height: 1px;
  background-color: #e2e6f3;
}
</style>
