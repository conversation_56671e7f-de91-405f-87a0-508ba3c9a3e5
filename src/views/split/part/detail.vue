<template>
  <div>
    <el-dialog v-dialogDragBox title="拆零结果详情" :visible.sync="open" width="1150px" class="custom-dialog">
      <div style="padding: 0 20px">
        <div class="splitDetail-title">拆零信息</div>
        <div class="splitDetail-info">
          <div class="splitDetail-info-item">
            <span class="text">拆零备注</span>
            <span class="content">{{ detail.nameMark }}</span>
          </div>
          <div class="splitDetail-info-item">
            <span class="text">拆零时间</span>
            <span class="content">{{ detail.createTime || '--' }}</span>
          </div>
          <div class="splitDetail-info-item">
            <span class="text">标高</span>
            <span class="content">{{ detail.height ? `${detail.height}米` : '--' }}</span>
          </div>
          <div class="splitDetail-info-item">
            <span class="text">结构类型</span>
            <span class="content">{{ detail.structure === 'steels' ? '钢结构' : '混凝土' }}</span>
          </div>
          <div class="splitDetail-info-item">
            <span class="text">是否热镀锌</span>
            <span class="content">{{ detail.isRdx ? '热镀锌' : '电镀锌' }}</span>
          </div>
          <div class="splitDetail-info-item">
            <span class="text">是否含税</span>
            <span class="content">{{ detail.isIncludeTax ? '是' : '否' }}</span>
          </div>
          <div class="splitDetail-info-item" v-if="detail.isIncludeTax">
            <span class="text">税点</span>
            <span class="content">{{ detail.taxPercent ? `${detail.taxPercent}` : '--' }}</span>
          </div>
          <div class="splitDetail-info-item">
            <span class="text">总数量</span>
            <span class="content">{{ (detail && detail.details && detail.details.length) || 0 }}</span>
          </div>
          <div class="splitDetail-info-item">
            <span class="text">金额</span>
            <span class="content price">{{ detail.totalAmount ? `¥${detail.totalAmount}` : '--' }}</span>
          </div>
          <div class="splitDetail-info-item" v-if="detail.isIncludeTax">
            <span class="text">含税金额</span>
            <span class="content price">{{ detail.totalTax ? `¥${detail.totalTax}` : '--' }}</span>
          </div>
          <div class="splitDetail-info-item">
            <span class="text">总金额</span>
            <span class="content price">{{ detail.totalAmountWithTax ? `¥${detail.totalAmountWithTax}` : '--' }}</span>
          </div>
        </div>
        <template v-if="detail.useTo === 2">
          <div class="splitDetail-title">一键报价明细</div>
          <el-table :data="detail.details2" style="width: 100%" class="custom-table" stripe :key="Math.random()">
            <el-table-column label="序号" type="index" align="center"></el-table-column>
            <el-table-column prop="content" label="内容" align="center" show-overflow-tooltip />
            <el-table-column prop="count" label="数量" align="center" show-overflow-tooltip />
            <el-table-column prop="unitPrice" label="金额" align="center" show-overflow-tooltip />
            <template v-if="detail.isIncludeTax">
              <el-table-column label="税点" align="center">
                <template slot-scope="scope">{{ detail.taxPercent ? `${detail.taxPercent}` : '-' }}</template>
              </el-table-column>
              <el-table-column prop="taxIncluded" label="含税" align="center">
                <template slot-scope="{ row }">
                  <span class="color-orange">{{ row.taxAmount ? '￥' + row.taxAmount : '-' }}</span>
                </template>
              </el-table-column>
            </template>
            <el-table-column prop="total" label="报价" align="center" show-overflow-tooltip>
              <template slot-scope="{ row }">
                <span class="color-blue">{{ row.total ? '￥' + row.total : '-' }}</span>
              </template>
            </el-table-column>
          </el-table>
          <div class="splitBox-total">
            <span class="text">商品总价{{ detail.isIncludeTax ? '(含税)' : '' }}</span>
            <span class="price">{{ detail.totalAmountWithTax }}</span>
          </div>
        </template>
        <template v-else>
          <div class="splitDetail-title">拆零物料列表</div>
          <el-table :data="detail.details" style="width: 100%" class="custom-table" stripe :key="Math.random()">
            <el-table-column label="序号" type="index" align="center"></el-table-column>
            <el-table-column prop="materialName" label="物料名称" align="center" show-overflow-tooltip />
            <el-table-column prop="specs" label="规格" align="center" show-overflow-tooltip />
            <el-table-column prop="unit" label="单位" align="center" show-overflow-tooltip />
            <el-table-column prop="weight" label="重量" align="center" show-overflow-tooltip />
            <el-table-column prop="number" label="数量" align="center" show-overflow-tooltip />
            <el-table-column prop="unitPrice" label="单价" align="center">
              <template slot-scope="{ row }">
                <span class="table-price">{{ row.unitPrice ? '￥' + row.unitPrice : '-' }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="amount" label="金额" align="center">
              <template slot-scope="{ row }">
                <span class="table-price">{{ row.amount ? '￥' + row.amount : '-' }}</span>
              </template>
            </el-table-column>
            <template v-if="detail.isIncludeTax">
              <el-table-column label="税点" align="center">
                <template slot-scope="scope">{{ detail.taxPercent ? `${detail.taxPercent}` : '-' }}</template>
              </el-table-column>
              <el-table-column prop="taxIncluded" label="含税" align="center">
                <template slot-scope="{ row }">
                  <span class="color-orange">{{ row.taxAmount ? '￥' + row.taxAmount : '-' }}</span>
                </template>
              </el-table-column>
            </template>
            <el-table-column prop="quote" label="报价" align="center">
              <template slot-scope="{ row }">
                <span class="color-blue">{{ row.quotedPrice ? '￥' + row.quotedPrice : '-' }}</span>
              </template>
            </el-table-column>
          </el-table>
          <div class="splitBox-total">
            <span class="text">商品总价{{ detail.isIncludeTax ? '(含税)' : '' }}</span>
            <span class="price">{{ detail.totalAmountWithTax }}</span>
          </div>
        </template>
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn" @click="open = false">取消</el-button>
        <el-button class="custom-dialog-btn primary" @click="handleExport">导出该拆零列表</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { detail } from '@/api/houtai/gongyu/caigou'
import { getSplitPartDetail } from '@/api/splitPart'
import { createSplitTemplate } from '@/vendor/ExportTemplate'
export default {
  name: 'SplitPartDetail',
  data() {
    return {
      open: false,
      detail: {}
    }
  },
  methods: {
    getDetail(row) {
      const { id: infoId } = row
      if (!infoId) {
        this.$message.error('参数错误')
        return
      }
      getSplitPartDetail({ infoId }).then(res => {
        const { code, data = [], msg } = res
        if (code === 200) {
          if (data.useTo === 2) {
            if (data.totalAmount && data.isIncludeTax) {
              const nowAmount = data.totalAmount || 0
              data.totalAmount = parseFloat((nowAmount / (1 + data.taxPercent / 100)).toFixed(5)) || 0
              data.totalTax = parseFloat((nowAmount - data.totalAmount).toFixed(5)) || 0
              data.totalAmountWithTax = nowAmount
            } else {
              data.totalAmountWithTax = data.totalAmount || 0
              data.totalTax = 0
            }
          } else {
            if (data.totalAmount && data.isIncludeTax) {
              data.totalAmountWithTax = parseFloat((data.totalAmount + (data.totalAmount * data.taxPercent) / 100).toFixed(5)) || 0
              data.totalTax = parseFloat(((data.totalAmount * data.taxPercent) / 100).toFixed(5)) || 0
            } else {
              data.totalAmountWithTax = data.totalAmount || 0
              data.totalTax = 0
            }
          }
          if (data && data.details && data.details.length > 0) {
            data.details.forEach(item => {
              if (item.amount && data.taxPercent) {
                item.taxAmount = parseFloat(((item.amount * data.taxPercent) / 100).toFixed(5))
                item.quotedPrice = parseFloat((parseFloat(item.amount) + parseFloat(item.taxAmount)).toFixed(5))
              } else {
                item.quotedPrice = item.amount
              }
            })
          }
          if (data && data.details2 && data.details2.length > 0) {
            data.details2.forEach(item => {
              if (item.unitPrice && data.taxPercent) {
                item.taxAmount = parseFloat(((item.unitPrice * item.count * data.taxPercent) / 100).toFixed(5))
                item.total = parseFloat((item.unitPrice * item.count + item.taxAmount).toFixed(5))
              } else {
                item.total = item.unitPrice * item.count
              }
            })
          }
          this.detail = data
          this.open = true
        } else this.$message.error(msg)
      })
    },
    // 导出列表
    async handleExport() {
      const { details: list = [], details2: list2 = [], nameMark: filename = '拆零结果' } = this.detail
      const targetList = this.detail.useTo === 2 ? list2 : list
      
      if (!targetList || !targetList.length) {
        this.$message.warning('暂无数据可导出')
        return
      }
      
      const loading = this.$loading({
        lock: true,
        text: '导出中...',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      
      try {
        const hasTax = !!this.detail.isIncludeTax
        const options = { useTo: this.detail.useTo, hasTax: hasTax }
        await createSplitTemplate({
          data: targetList,
          filename,
          options
        })
        this.$message.success('导出成功')
      } catch (error) {
        console.error('导出失败:', error)
        this.$message.error('导出失败: ' + (error.message || '未知错误'))
      } finally {
        loading.close()
      }
    },
    // 格式化数据
    // prettier-ignore
    formatJson(filterVal, jsonData) {
      return jsonData.map(v => filterVal.map(j => {
        if (j === 'timestamp') {
          return parseTime(v[j])
        } else {
          return v[j]
        }
      }))
    }
  }
}
</script>
<style scoped lang="scss">
@import '@/assets/styles/custom-new.scss';
.splitDetail {
  &-title {
    font-size: 14px;
    color: #999999;
    line-height: 20px;
    margin-bottom: 12px;
  }
  &-info {
    display: flex;
    flex-wrap: wrap;
    padding: 10px 0;
    border-top: 1px solid #e2e6f3;
    border-bottom: 1px solid #e2e6f3;
    margin-bottom: 12px;
    &-item {
      display: flex;
      align-items: center;
      line-height: 20px;
      padding: 6px 0;
      margin-right: 30px;
      .text {
        font-size: 12px;
        color: #888;
        margin-right: 10px;
      }
      .content {
        font-size: 14px;
        font-weight: 500;
        color: #333;
        &.price {
          color: #f43f3f;
        }
      }
    }
  }
}
.splitBox-total {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  border: 1px solid #d91f56;
  background-color: #fcf7f8;
  border-radius: 5px;
  height: 56px;
  padding: 0 20px;
  margin-top: 20px;
  .text {
    font-size: 12px;
    color: #666;
    margin-right: 20px;
  }
  .price {
    font-size: 18px;
    color: #ec2454;
    &::before {
      content: '￥';
      font-size: 14px;
      margin-right: 5px;
    }
  }
}
</style>
