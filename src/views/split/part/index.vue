<template>
  <div class="newBox bgcf9 vh-85">
    <!-- 搜索 -->
    <div class="custom-search" style="padding-top: 18px">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" @submit.native.prevent>
        <el-form-item label="是否热镀锌">
          <el-select v-model="queryParams.isRdx" placeholder="请选择" @change="handleQuery">
            <el-option v-for="item in isRdxOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="是否含税">
          <el-select v-model="queryParams.isIncludeTax" placeholder="请选择" @change="handleQuery">
            <el-option v-for="item in isIncludeTaxOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="结构类型">
          <el-select v-model="queryParams.structure" placeholder="请选择" @change="handleQuery">
            <el-option v-for="item in structureOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="拆零类型">
          <el-select v-model="queryParams.useTo" placeholder="请选择" @change="handleQuery">
            <el-option v-for="item in useToOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="small" @click="handleResetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 列表 -->
    <div style="padding: 15px 20px">
      <el-table v-loading="loading" ref="allTable" stripe :data="list" style="width: 100%" class="custom-table" v-if="list.length > 0">
        <el-table-column align="center" type="index" label="序号"></el-table-column>
        <el-table-column prop="nameMark" label="拆零结果备注" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column prop="height" label="标高" align="center">
          <template slot-scope="{ row }">{{ row.height }}米</template>
        </el-table-column>
        <el-table-column prop="structure" label="结构类型" align="center">
          <template slot-scope="{ row }">
            {{ row.structure === 'steels' ? '钢结构' : '混凝土' }}
          </template>
        </el-table-column>
        <el-table-column prop="isRdx" label="是否热镀锌" align="center">
          <template slot-scope="{ row }">
            {{ row.isRdx ? '热镀锌' : '电镀锌' }}
          </template>
        </el-table-column>
        <el-table-column prop="isIncludeTax" label="是否含税" align="center">
          <template slot-scope="{ row }">
            {{ row.isIncludeTax ? '是' : '否' }}
          </template>
        </el-table-column>
        <el-table-column prop="taxPercent" label="税点" align="center">
          <template slot-scope="{ row }">{{ row.taxPercent || '-' }}</template>
        </el-table-column>
        <el-table-column prop="totalAmount" label="金额" align="center">
          <template slot-scope="{ row }">
            <span class="table-price">￥{{ row.totalAmount }}</span>
          </template>
        </el-table-column>
        <el-table-column label="含税金额" align="center">
          <template slot-scope="{ row }">
            <span class="table-price" v-if="row.isIncludeTax && row.taxPercent">￥{{ parseFloat(((row.totalAmount * row.taxPercent) / 100).toFixed(5)) }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="总金额" align="center">
          <template slot-scope="{ row }">
            <span class="table-price">￥{{ row.isIncludeTax && row.taxPercent ? parseFloat((row.totalAmount + (row.totalAmount * row.taxPercent) / 100).toFixed(5)) : row.totalAmount }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="createBy" label="创建人" align="center"></el-table-column>
        <el-table-column prop="createTime" label="创建时间" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column label="操作" align="center">
          <template slot-scope="{ row }">
            <el-button class="table-btn primary" icon="el-icon-view" @click="handleView(row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-empty v-if="!loading && list.length === 0" description="暂无数据" />
      <!-- 详情 -->
      <split-part-detail ref="splitPartDetail" v-if="showDetail" />
    </div>
  </div>
</template>
<script>
import { getSplitPartList } from '@/api/splitPart'
import SplitPartDetail from './detail'

export default {
  name: 'SplitPart',
  components: { SplitPartDetail },
  data() {
    return {
      // 搜索
      queryParams: {
        pageNum: 1, // 当前页码
        pageSize: 10, // 每页条数
        isRdx: true, // 是否热镀锌
        isIncludeTax: 1, // 是否含税
        structure: 'steels', // 结构类型
        useTo: 1 // 拆零类型
      },
      list: [],
      total: 0,
      loading: false,
      isRdxOptions: [
        { label: '是', value: true },
        { label: '否', value: false }
      ],
      isIncludeTaxOptions: [
        { label: '是', value: 1 },
        { label: '否', value: 0 }
      ],
      structureOptions: [
        { label: '钢结构', value: 'steels' },
        { label: '混凝土', value: 'concrete' }
      ],
      useToOptions: [
        { label: '拆零结果', value: 1 },
        { label: '一键报价', value: 2 }
      ],
      showDetail: false
    }
  },
  created() {
    // 获取列表
    this.getList()
  },
  methods: {
    // 获取列表
    // prettier-ignore
    getList() {
      this.loading = true
      getSplitPartList(this.queryParams).then(res => {
        const { code, data, msg } = res
        if (code === 200) {
          this.list = data
        } else this.$message.error(msg)
      }).finally(() => {
        this.loading = false
      })
    },
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 重置
    handleResetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 查看详情
    handleView(row) {
      this.showDetail = true
      this.$nextTick(() => {
        this.$refs.splitPartDetail.getDetail(row)
      })
    }
  }
}
</script>
<style scoped lang="scss">
@import '@/assets/styles/custom-new.scss';
</style>
