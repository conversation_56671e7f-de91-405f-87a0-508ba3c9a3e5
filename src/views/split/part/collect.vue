<template>
  <div class="newBox bgcf9 vh-85">
    <!-- 列表 -->
    <div style="padding: 15px 20px">
      <collect-tpl ref="collect" type="splitPartInfo" :storeId.sync="storeId" :collectList.sync="collectList" @getList="getList" @colletSubmit="colletSubmit" :isSearch="false" :search="false" @queryTable="getList" />

      <el-table v-loading="loading" ref="allTable" stripe :data="list" style="width: 100%" class="custom-table" v-if="list.length > 0">
        <el-table-column align="center" type="index" label="序号"></el-table-column>
        <el-table-column prop="nameMark" label="拆零结果备注" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column prop="height" label="标高" align="center">
          <template slot-scope="{ row }">{{ row.height }}米</template>
        </el-table-column>
        <el-table-column prop="structure" label="结构类型" align="center">
          <template slot-scope="{ row }">
            {{ row.structure === 'steels' ? '钢结构' : '混凝土' }}
          </template>
        </el-table-column>
        <el-table-column prop="isRdx" label="是否热镀锌" align="center">
          <template slot-scope="{ row }">
            {{ row.isRdx ? '热镀锌' : '电镀锌' }}
          </template>
        </el-table-column>
        <el-table-column prop="isIncludeTax" label="是否含税" align="center">
          <template slot-scope="{ row }">
            {{ row.isIncludeTax ? '是' : '否' }}
          </template>
        </el-table-column>
        <el-table-column prop="taxPercent" label="税点" align="center">
          <template slot-scope="{ row }">{{ row.taxPercent || '-' }}</template>
        </el-table-column>
        <el-table-column prop="totalAmount" label="金额" align="center">
          <template slot-scope="{ row }">
            <span class="table-price">￥{{ row.totalAmount }}</span>
          </template>
        </el-table-column>
        <el-table-column label="含税金额" align="center">
          <template slot-scope="{ row }">
            <span class="table-price" v-if="row.isIncludeTax && row.taxPercent">￥{{ parseFloat(((row.totalAmount * row.taxPercent) / 100).toFixed(5)) }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="总金额" align="center">
          <template slot-scope="{ row }">
            <span class="table-price">￥{{ row.isIncludeTax && row.taxPercent ? parseFloat((row.totalAmount + (row.totalAmount * row.taxPercent) / 100).toFixed(5)) : row.totalAmount }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="createBy" label="创建人" align="center"></el-table-column>
        <el-table-column prop="createTime" label="创建时间" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column label="操作" align="center" :min-width="130">
          <template slot-scope="{ row }">
            <el-button type="text" size="mini" icon="el-icon-view" @click="handleView(row)">详情</el-button>
            <el-button type="text" size="mini" icon="el-icon-delete" @click="handleDelete(row)">删除</el-button>
            <el-button type="text" size="mini" icon="el-icon-paperclip" @click="handleCollet(row)">收藏到</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-empty v-if="!loading && list.length === 0" description="暂无数据" />
      <!-- 分页 -->
      <div class="custom-pagination">
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
      </div>
      <!-- 详情 -->
      <split-part-detail ref="splitPartDetail" v-if="showDetail" />
    </div>
  </div>
</template>
<script>
import SplitPartDetail from './detail'
import { getlistb, shoucTo, delshouc } from '@/api/houtai/shoucang'
import CollectTpl from '@/views/components/collect'

export default {
  name: 'SplitCollect',
  components: { SplitPartDetail, CollectTpl },
  data() {
    return {
      // 搜索
      queryParams: {
        pageNum: 1, // 当前页码
        pageSize: 10, // 每页条数
        storeId: undefined
      },
      list: [],
      total: 0,
      loading: false,
      isRdxOptions: [
        { label: '是', value: true },
        { label: '否', value: false }
      ],
      isIncludeTaxOptions: [
        { label: '是', value: 1 },
        { label: '否', value: 0 }
      ],
      structureOptions: [
        { label: '钢结构', value: 'steels' },
        { label: '混凝土', value: 'concrete' }
      ],
      showDetail: false,
      storeId: undefined,
      collectList: []
    }
  },
  created() {
    // 获取列表
    this.getList()
  },
  methods: {
    // 获取列表
    // prettier-ignore
    getList() {
      this.loading = true
      if (this.storeId) {
        this.queryParams.storeId = this.storeId
        getlistb(this.queryParams).then(res => {
          const { code, rows=[],total, msg } = res
          if (code === 200) {
            this.list = rows
            this.total = total
          } else this.$message.error(msg)
        }).finally(() => {
          this.loading = false
        })
      } else this.loading = false
    },
    // 查看详情
    handleView(row) {
      this.showDetail = true
      this.$nextTick(() => {
        this.$refs.splitPartDetail.getDetail(row)
      })
    },
    // 提交收藏
    colletSubmit(storeId, data) {
      if (!Array.isArray(data)) data = [data]
      shoucTo({ storeId, valueIdList: data }).then(res => {
        this.getList()
        this.$modal.msgSuccess('操作成功')
      })
    },
    // 单个收藏
    handleCollet(item) {
      this.$refs.collect.handleChange(item.id)
    },
    // 删除
    // prettier-ignore
    handleDelete(item) {
      const data = { storeId: this.storeId, valueId: item.id }
      this.$modal.confirm('是否确认删除选中的收藏？').then(function () {
        return delshouc(data)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess('删除成功')
      }).catch(() => {
      })
    }
  }
}
</script>
<style scoped lang="scss">
@import '@/assets/styles/custom-new.scss';
</style>
