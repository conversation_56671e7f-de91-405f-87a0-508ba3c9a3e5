<template>
  <div>
    <el-dialog v-dialogDragBox :title="title" :visible.sync="open" width="1150px" class="custom-dialog" :before-close="handleBeforeClose">
      <div style="padding: 0 20px">
        <el-form :model="form" :rules="rules" ref="form" label-position="left" label-width="110px">
          <el-row :gutter="10">
            <template v-if="!disabled && !isUpdate">
              <el-col :span="12">
                <el-form-item label="所属支架" prop="belong">
                  <el-select v-model="form.belong" placeholder="请选择支架类型" style="width: 100%">
                    <el-option v-for="item in belongOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="支架方向" prop="direction">
                  <el-select v-model="form.direction" placeholder="请选择支架方向" style="width: 100%">
                    <el-option v-for="item in directionOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12" v-if="form.belong === 'KZS'">
                <el-form-item label="管道数量" prop="pipes">
                  <el-select v-model="form.pipes" placeholder="请选择管道数量" style="width: 100%">
                    <el-option v-for="item in pipesOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="结构类型" prop="structure">
                  <el-select v-model="form.structure" placeholder="请选择结构类型" style="width: 100%">
                    <el-option v-for="item in structureOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </template>
            <el-col :span="24">
              <el-table :data="form.bomMaterialList" stripe class="custom-table" :class="{ 'custom-table-cell0': !disabled }" style="width: 100%">
                <el-table-column align="center" type="index" label="序号"></el-table-column>
                <el-table-column prop="materialName" label="物料名称" align="center" show-overflow-tooltip></el-table-column>
                <el-table-column prop="specs" label="规格" align="center"></el-table-column>
                <el-table-column prop="weight" label="重量" align="center"></el-table-column>
                <el-table-column prop="unit" label="单位" align="center"></el-table-column>
                <el-table-column prop="number" label="所需数量" align="center">
                  <template slot-scope="scope">
                    <template v-if="disabled">{{ scope.row.number }}</template>
                    <template v-if="!disabled">
                      <template v-if="scope.row.disabled">
                        <span style="display: inline-block; padding: 13px 0">{{ scope.row.number }}</span>
                      </template>
                      <template v-else>
                        <el-form-item label-width="0" :prop="`bomMaterialList.${scope.$index}.number`" :rules="rules.number">
                          <el-input-number v-model="scope.row.number" placeholder="请输入所需数量" :min="0.00001" :controls="false" size="small"></el-input-number>
                        </el-form-item>
                      </template>
                    </template>
                  </template>
                </el-table-column>
                <el-table-column prop="unitPrice" label="单价(元)" align="center">
                  <template slot-scope="{ row }">
                    <span class="table-price">{{ row.unitPrice || '-' }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="操作" align="center" v-if="!disabled">
                  <template slot-scope="scope">
                    <template v-if="scope.row.disabled">
                      <el-button class="table-btn disabled" disabled icon="el-icon-delete" v-if="canDelete()">删除</el-button>
                      <el-button class="table-btn danger" icon="el-icon-delete" @click="handleDelete(scope.row, scope.$index)" v-else>删除</el-button>
                    </template>
                    <template v-else>
                      <el-button class="table-btn danger" icon="el-icon-delete" @click="form.bomMaterialList.splice(scope.$index, 1)">删除</el-button>
                    </template>
                  </template>
                </el-table-column>
              </el-table>
              <div style="margin-top: 20px" v-if="!disabled">
                <el-button type="primary" icon="el-icon-plus" @click="handlePlus">添加拆零物料</el-button>
              </div>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <template v-if="disabled">
          <el-button class="custom-dialog-btn primary" @click="handleClose()">关 闭</el-button>
        </template>
        <template v-if="!disabled">
          <el-button class="custom-dialog-btn" @click="handleClose()">取 消</el-button>
          <el-button class="custom-dialog-btn primary" :class="{ disabled: !(form.bomMaterialList && form.bomMaterialList.length) }" :disabled="!(form.bomMaterialList && form.bomMaterialList.length)" @click="handleSubmit">确 定</el-button>
        </template>
      </div>
    </el-dialog>
    <!-- 添加拆零物料 -->
    <el-dialog v-dialogDragBox title="添加拆零物料" :visible.sync="plusOpen" width="1150px" class="custom-dialog">
      <div>
        <material-list is-prop :plusData.sync="plusData" ref="materialList" />
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn" @click="plusOpen = false">取 消</el-button>
        <el-button class="custom-dialog-btn primary" :class="{ disabled: !(plusData && plusData.id) }" :disabled="!(plusData && plusData.id)" @click="handlePlusSubmit">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import MaterialList from '@/views/split/material'
import { isNumberLength } from '@/utils/validate'
import { createBom, createBomChild, deleteBomChild } from '@/api/splitPart'

export default {
  components: { MaterialList },
  props: {
    // 所属支架
    belongOptions: {
      type: Array,
      default: () => []
    },
    // 支架方向
    directionOptions: {
      type: Array,
      default: () => []
    },
    // 管道数量类型（仅针对抗震水管）
    pipesOptions: {
      type: Array,
      default: () => []
    },
    // 结构类型
    structureOptions: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      disabled: false,
      isUpdate: false,
      hasChange: false,
      open: false,
      title: '新增拆零BOM',
      form: {},
      rules: {
        belong: [{ required: true, message: '请选择支架类型', trigger: 'change' }],
        direction: [{ required: true, message: '请选择支架方向', trigger: 'change' }],
        pipes: [{ required: true, message: '请选择管道数量', trigger: 'change' }],
        structure: [{ required: true, message: '请选择结构类型', trigger: 'change' }],
        number: [
          { required: true, message: '请输入所需数量', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: 'blur' }
        ]
      },
      // 添加拆零物料弹窗
      plusOpen: false,
      plusData: {}
    }
  },
  methods: {
    reset() {
      this.form = {
        partBomId: undefined,
        belong: this.belongOptions[0].value || undefined, // 所属支架
        direction: this.directionOptions[0].value || undefined, // 支架方向
        pipes: this.pipesOptions[0].value || undefined, // 管道数量类型（仅针对抗震水管）
        structure: this.structureOptions[0].value || undefined, // 结构类型
        bomMaterialList: [] // 拆零物料
      }
      this.resetForm('form')
    },
    // 新增
    handleCreate() {
      this.reset()
      this.title = '新增拆零BOM'
      this.disabled = false
      this.isUpdate = false
      this.hasChange = false
      this.open = true
    },
    // 修改
    handleUpdate(row) {
      this.reset()
      const { id, details } = row
      this.form.partBomId = id
      if (details && details.length) {
        const list = details.map(item => {
          return { ...item.partMaterial, partBomDetailId: item.id, number: item.number, disabled: true }
        })
        this.form.bomMaterialList = [...list]
      }
      this.title = '修改拆零BOM'
      this.disabled = false
      this.isUpdate = true
      this.hasChange = false
      this.open = true
    },
    // 详情
    handleDetail(row) {
      this.reset()
      const { details } = row
      if (!(details && details.length)) return
      const list = details.map(item => {
        return { ...item.partMaterial, number: item.number }
      })
      this.form.bomMaterialList = [...list]
      this.title = '拆零BOM详情'
      this.disabled = true
      this.isUpdate = true
      this.hasChange = false
      this.open = true
    },
    // 提交
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          const { partBomId, belong, direction, pipes, structure, bomMaterialList } = this.form
          if (partBomId) {
            const list = bomMaterialList.filter(item => !item.disabled)
            const newList = list.map(item => {
              return { partMaterialId: item.id, number: Number(item.number) }
            })
            if (!newList.length) {
              this.handleClose()
              return
            }
            let obj = { partBomId, bomMaterialList: newList }
            createBomChild(obj).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('修改成功')
                this.handleClose(true)
              } else this.$message.error(msg || '修改失败')
            })
          } else {
            const list = bomMaterialList.map(item => {
              return { partMaterialId: item.id, number: Number(item.number) }
            })
            let obj = { belong, direction, structure, bomMaterialList: list }
            if (belong === 'KZS') obj.pipes = pipes
            else obj.pipes = 's'
            createBom(obj).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('新增成功')
                this.handleClose(true)
              } else this.$message.error(msg || '新增失败')
            })
          }
        }
      })
    },
    // 关闭
    handleClose(flag = false) {
      this.open = false
      if (this.hasChange) this.$emit('callback', true)
      else this.$emit('callback', flag)
    },
    // 关闭前
    handleBeforeClose() {
      this.handleClose()
    },
    // 添加拆零物料
    handlePlus() {
      this.plusData = {}
      this.plusOpen = true
    },
    // 确定添加拆零物料
    handlePlusSubmit() {
      const data = { ...this.plusData, number: undefined }
      const index = this.form.bomMaterialList.findIndex(item => item.id === data.id)
      if (index !== -1) {
        this.$message.warning('物料已存在')
        return
      }
      this.form.bomMaterialList.push(data)
      this.plusOpen = false
    },
    // 判断是否可删除
    canDelete() {
      const list = this.form.bomMaterialList.filter(item => item.disabled)
      return list.length === 1
    },
    // 删除拆零物料
    // prettier-ignore
    handleDelete(row, index) {
      const { partBomDetailId } = row
      if (!partBomDetailId) {
        this.$message.warning('参数错误')
        return
      }
      this.$confirm('是否删除该拆零物料？', '提示', {
        type: 'warning'
      }).then(() => {
        deleteBomChild({ partBomDetailId }).then(res => {
          const { code, msg } = res
          if (code === 200) {
            this.$message.success('删除成功')
            this.form.bomMaterialList.splice(index, 1)
            this.hasChange = true
          } else this.$message.error(msg || '删除失败')
        })
      }).catch(() => {})
    }
  }
}
</script>
<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
::v-deep {
  .custom-table {
    .el-table__header .el-table__cell {
      padding: 0;
    }
    .el-form-item {
      margin-top: 10px !important;
      margin-bottom: 10px !important;
      .el-form-item__error {
        top: 95%;
        padding-top: 0;
      }
    }
  }
}
</style>
