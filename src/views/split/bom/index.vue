<template>
  <div class="newBox bgcf9 vh-85">
    <div class="custom-search">
      <el-button type="primary" icon="el-icon-plus" size="small" @click="handleCreate">添加拆零BOM</el-button>
    </div>
    <div style="padding: 15px 20px">
      <el-table v-loading="loading" ref="allTable" stripe :data="list" style="width: 100%" class="custom-table" v-if="list.length > 0">
        <el-table-column align="center" type="index" label="序号"></el-table-column>
        <el-table-column prop="belong" label="所属支架" align="center" show-overflow-tooltip :formatter="belongFormat" />
        <el-table-column prop="direction" label="支架方向" align="center" show-overflow-tooltip :formatter="directionFormat" />
        <el-table-column prop="pipes" label="管道数量" align="center" show-overflow-tooltip :formatter="pipesFormat" />
        <el-table-column prop="structure" label="结构类型" align="center" show-overflow-tooltip :formatter="structureFormat" />
        <el-table-column prop="status" label="状态" align="center" width="100">
          <template slot-scope="scope">
            <el-switch v-model="scope.row.status" active-text="启用" inactive-text="禁用" :active-value="1" :inactive-value="0" @change="handleChangeStatus(scope.row, $event)" class="table-switch"></el-switch>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center">
          <template slot-scope="{ row }">
            <el-button class="table-btn" size="small" icon="el-icon-view" @click="handleDetail(row)">详情</el-button>
            <el-button class="table-btn primary" size="small" icon="el-icon-edit" @click="handleUpdate(row)">修改</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-empty v-if="!loading && list.length === 0" description="暂无数据" />
      <!-- 分页 -->
      <!-- <div class="custom-pagination">
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
      </div> -->
    </div>
    <!-- 新增/修改 -->
    <create ref="create" :belongOptions="belongOptions" :directionOptions="directionOptions" :pipesOptions="pipesOptions" :structureOptions="structureOptions" @callback="handleBack" v-if="showCreate" />
  </div>
</template>
<script>
import Create from './create'
import { getBomList, toggleBomStatus } from '@/api/splitPart'

export default {
  name: 'SplitBom',
  components: { Create },
  data() {
    return {
      // 搜索
      queryParams: {
        pageNum: 1, // 当前页码
        pageSize: 10 // 每页条数
      },
      list: [],
      total: 0,
      loading: false,
      showCreate: false,
      // 所属支架
      belongOptions: [
        { label: '抗震水管', value: 'KZS' },
        { label: '抗震桥架', value: 'KZQ' },
        { label: '抗震风管', value: 'KZF' }
      ],
      // 支架方向
      directionOptions: [
        { label: '侧向', value: 'T' },
        { label: '侧纵向', value: 'TL' }
      ],
      // 管道数量类型（仅针对抗震水管）
      pipesOptions: [
        { label: '单管', value: 's' },
        { label: '多管', value: 'm' }
      ],
      // 结构类型
      structureOptions: [
        { label: '钢结构', value: 'steels' },
        { label: '混凝土', value: 'concrete' }
      ]
    }
  },
  created() {
    // 获取列表
    this.getList()
  },
  methods: {
    // 回显所属支架
    belongFormat(row) {
      const belong = this.belongOptions.find(item => item.value === row.belong)
      return belong?.label || '-'
    },
    // 回显支架方向
    directionFormat(row) {
      const direction = this.directionOptions.find(item => item.value === row.direction)
      return direction?.label || '-'
    },
    // 回显管道数量类型（仅针对抗震水管）
    pipesFormat(row) {
      const pipes = this.pipesOptions.find(item => item.value === row.pipes)
      if (row.belong !== 'KZS') return '-'
      return pipes?.label || '-'
    },
    // 回显结构类型
    structureFormat(row) {
      const structure = this.structureOptions.find(item => item.value === row.structure)
      return structure?.label || '-'
    },
    // 获取列表
    // prettier-ignore
    getList() {
      this.loading = true
      getBomList(this.queryParams).then(res => {
        const { code, data, msg } = res
        if (code === 200) {
          this.list = data
        } else this.$message.error(msg)
      }).finally(() => {
        this.loading = false
      })
    },
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 重置
    handleResetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 回调
    handleBack(flag = false) {
      this.showCreate = false
      if (flag) this.getList()
    },
    // 新增
    handleCreate() {
      this.showCreate = true
      this.$nextTick(() => {
        this.$refs.create.handleCreate()
      })
    },
    // 修改
    handleUpdate(row) {
      this.showCreate = true
      this.$nextTick(() => {
        this.$refs.create.handleUpdate(row)
      })
    },
    // 详情
    handleDetail(row) {
      this.showCreate = true
      this.$nextTick(() => {
        this.$refs.create.handleDetail(row)
      })
    },
    // 启用/禁用
    // prettier-ignore
    handleChangeStatus(row, status) {
      const data = { id: row.id, status }
      const title = status === 1 ? '启用' : '禁用'
      this.$confirm(`是否${title}该拆零BOM？`, '提示', {
        type: 'warning',
        showCancelButton: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        toggleBomStatus(data).then(res => {
          const { code, msg } = res
          if (code === 200) {
            this.$message.success('操作成功')
            this.getList()
          } else this.$message.error(msg)
        })
      }).catch(() => {
        this.$set(row, 'status', row.status === 1 ? 0 : 1) // 恢复原状态
      })
    }
  }
}
</script>
<style scoped lang="scss">
@import '@/assets/styles/custom-new.scss';
</style>
