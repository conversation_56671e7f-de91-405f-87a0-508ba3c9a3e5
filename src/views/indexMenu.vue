<template>
  <div>
    <div class="index-menu">
      <div class="menu-section">
        <div class="section-header">
          <span class="section-title">常用功能</span>
          <div class="section-actions">
            <div class="section-actions-item" :class="{ 'is-active': isEditing }" @click="isEditing = !isEditing">
              <i :class="isEditing ? 'el-icon-circle-check' : 'el-icon-edit-outline'"></i>
              <span>{{ isEditing ? '完成编辑' : '编辑' }}</span>
            </div>
            <div class="section-actions-item is-active" @click="openAddDialog">
              <i class="el-icon-plus"></i>
              <span>添加常用功能</span>
            </div>
          </div>
        </div>
        <div class="menu-cards" v-if="menuList.length > 0">
          <div class="menu-card" v-for="item in menuList" :key="item.id" :class="{ 'edit-mode': isEditing, disabled: !isMenuAvailable(item) }" @click="handleModuleClick(item)">
            <div class="delete-icon" v-if="isEditing" @click.stop="deleteModule(item.id)">×</div>
            <svg-icon class="card-icon" :icon-class="item.icon" />
            <div class="card-name">{{ item.menuName }}</div>
          </div>
        </div>
        <el-empty v-else description="暂无常用功能" />
      </div>
    </div>
    <!-- 添加常用功能 -->
    <el-dialog v-dialogDragBox title="添加常用功能" :visible.sync="open" width="1150px" class="custom-dialog" append-to-body :before-close="handleClose">
      <div style="padding: 0 20px" class="add-menu-title">已添加功能</div>
      <div class="add-menu-checked" v-if="checkedList.length > 0">
        <div class="add-menu-checked-item" v-for="item in checkedList" :key="item.id">
          <svg-icon class="item-icon" :icon-class="item.icon" />
          <div class="item-name">{{ item.menuName }}</div>
          <div class="item-delete" @click.stop="deleteChecked(item.id)">×</div>
        </div>
      </div>
      <div class="add-menu-checked" v-else>暂无已添加功能</div>
      <div style="padding: 0 20px">
        <div class="add-menu-title">请选择您要添加到管理后台首页的功能</div>
        <el-tabs v-model="activeTab" type="border-card">
          <el-tab-pane :label="item.title" :name="item.name" v-for="(item, index) in routerList" :key="item.name + index">
            <el-checkbox class="add-menu-checkbox" v-for="child in item.children" :key="`${child.id}-${checkboxKey}`" :label="child.path" :checked="checkedList.some(menu => menu.menuId === child.id)" @change="handleCheckboxChange(child, $event)">{{ child.title }}</el-checkbox>
          </el-tab-pane>
        </el-tabs>
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn primary" @click="handleClose">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { menuList, menuCreate, menuDelete, menuSort } from '@/api/menu'

export default {
  name: 'indexMenu',
  data() {
    return {
      isEditing: false, // 是否编辑
      menuList: [],
      open: false,
      activeTab: undefined,
      checkedList: [],
      checkboxKey: 0,
      hasChanges: false
    }
  },
  created() {
    this.getMenuList()
  },
  computed: {
    routerList() {
      const list = this.$store.state && this.$store.state.permission && this.$store.state.permission.topbarRouters
      return this.formatMenuData(list)
    }
  },
  methods: {
    // 检查菜单项是否在routerList中可用
    isMenuAvailable(menuItem) {
      if (!this.routerList || this.routerList.length === 0) {
        return false
      }
      const checkMenuExists = (menuList, targetMenuId) => {
        for (const menu of menuList) {
          if (menu.id === targetMenuId) {
            return true
          }
          if (menu.children && menu.children.length > 0) {
            if (checkMenuExists(menu.children, targetMenuId)) {
              return true
            }
          }
        }
        return false
      }
      return checkMenuExists(this.routerList, menuItem.menuId || menuItem.id)
    },
    // 格式化菜单数据
    // prettier-ignore
    formatMenuData(menuItems) {
      return menuItems.filter(item => !item.hidden).map(item => {
        let formattedItem = {
          id: item.menuId || '',
          name: item.name || '',
          path: item.path || '',
          icon: item.meta?.icon || '',
          title: item.meta?.title || '',
          children: []
        }        
        if (item.children && item.children.length > 0) {
          formattedItem.children = item.children.filter(child => !child.hidden).map(child => ({
            id: child.menuId || item.menuId || '',
            name: child.name || '',
            path: child.path || '',
            icon: child.meta?.icon || '',
            title: child.meta?.title || ''
          }))          
          // 如果父级的name、icon、title为空且有children，使用第一个child的值
          const firstChild = formattedItem.children[0]
          if (firstChild) {
            if (!formattedItem.name) {
              formattedItem.name = firstChild.name
            }
            if (!formattedItem.icon) {
              formattedItem.icon = firstChild.icon
            }
            if (!formattedItem.title) {
              formattedItem.title = firstChild.title
            }
          }
        }        
        return formattedItem
      })
    },
    // 获取功能库列表
    getMenuList() {
      menuList().then(res => {
        const { code, msg, data } = res
        if (code === 200) this.menuList = data
        else this.$message.error(msg)
      })
    },
    // 删除模块
    // prettier-ignore
    deleteModule(moduleId) {
      if (this.isEditing) {
        this.$confirm('确定删除该常用功能吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          menuDelete({ id: moduleId }).then(res => {
            const { code, msg } = res
            if (code === 200) {
              const index = this.menuList.findIndex(item => item.id === moduleId)
              if (index !== -1) this.menuList.splice(index, 1)
              this.$message.success('操作成功')
            } else this.$message.error(msg)
          })
        }).catch(() => {})
      }
    },
    // 点击处理
    handleModuleClick(item) {
      if (!this.isEditing && this.isMenuAvailable(item)) {
        this.$router.push((item.parentPath !== '/' ? '/' + item.parentPath : '') + '/' + item.path)
      }
    },
    // 处理复选框状态变化
    handleCheckboxChange(item, checked) {
      if (checked) {
        const params = {
          menuId: item.id,
          sort: 0
        }
        menuCreate(params).then(res => {
          const { code, msg } = res
          if (code === 200) {
            // 添加到已选列表
            this.checkedList.push({
              id: item.id,
              menuId: item.id,
              menuName: item.title,
              icon: item.icon,
              path: item.path,
              parentPath: item.parentPath || ''
            })
            this.hasChanges = true
          } else {
            this.$message.error(msg)
          }
        })
      } else {
        const id = this.checkedList.find(menu => menu.menuId === item.id)?.id
        if (id) this.deleteChecked(id)
      }
    },

    // 删除已添加功能
    deleteChecked(id) {
      menuDelete({ id: id }).then(res => {
        const { code, msg } = res
        if (code === 200) {
          this.checkedList = this.checkedList.filter(item => item.id !== id)
          this.checkboxKey++
          this.hasChanges = true
          this.$message.success('删除成功')
        } else {
          this.$message.error(msg)
        }
      })
    },
    // 打开添加功能对话框
    openAddDialog() {
      this.open = true
      this.activeTab = this.routerList[0].name
      this.hasChanges = false
      this.checkedList = JSON.parse(JSON.stringify(this.menuList)).map(item => ({ ...item, menuId: item.menuId || item.id }))
    },
    // 关闭
    handleClose() {
      this.open = false
      if (this.hasChanges) this.getMenuList()
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/assets/styles/custom-new.scss';
@import '~@/assets/styles/custom-chart.scss';
.index-menu {
  width: 100%;
  box-shadow: 0 0 26px 0 rgba(0, 0, 0, 0.05);
  margin-top: 20px;
  padding: 0 20px 20px;
  border-radius: 5px;
}
.menu-section {
  border-radius: 5px;
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    border-bottom: 1px solid #e1e2e2;
    padding-top: 20px;
    padding-bottom: 8px;
    .section-title {
      font-size: 14px;
      color: #666;
    }
    .section-actions {
      display: flex;
      align-items: center;
      gap: 30px;
      &-item {
        display: flex;
        align-items: center;
        gap: 5px;
        cursor: pointer;
        i {
          font-size: 16px;
        }
        span {
          font-size: 14px;
          color: #666;
        }
        &.is-active {
          i {
            color: #2e73f3;
          }
          span {
            color: #2e73f3;
          }
        }
        &:hover {
          i {
            color: #2e73f3;
          }
          span {
            color: #2e73f3;
          }
        }
      }
    }
  }

  .menu-cards {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
    .menu-card {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 10px;
      width: 168px;
      height: 68px;
      background: #f8f9fb;
      border: 1px solid #cbd6e2;
      border-radius: 5px;
      cursor: pointer;
      transition: all 0.3s ease;
      // 图标
      .card-icon {
        font-size: 20px;
        color: #2e73f3;
        transition: color 0.3s ease;
      }
      // 名称
      .card-name {
        font-size: 16px;
        color: #333333;
        text-align: center;
        transition: color 0.3s ease;
      }
      // 默认状态
      &:hover {
        background-color: #2e73f3;
        border-color: #2e73f3;
        box-shadow: 0px 1px 19px 0px rgba(46, 115, 243, 0.23);
        color: #fff;
        .card-icon,
        .card-name {
          color: #fff;
        }
      }
      // 编辑模式
      &.edit-mode {
        .delete-icon {
          display: flex;
        }
      }
      // 禁用状态
      &.disabled {
        opacity: 0.6;
        cursor: not-allowed;
        background-color: #f0f0f0;
        border-color: #e0e0e0;
        color: #999;
        .card-icon,
        .card-name {
          color: #999;
        }
        &:hover {
          background-color: #f0f0f0;
          border-color: #e0e0e0;
          color: #999;
          box-shadow: none;
          .card-icon,
          .card-name {
            color: #999;
          }
        }
      }
      // 删除按钮
      .delete-icon {
        position: absolute;
        top: -8px;
        right: -8px;
        width: 16px;
        height: 16px;
        background-color: #666666;
        color: #ffffff;
        border-radius: 50%;
        display: none;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        cursor: pointer;
        z-index: 10;
        &:hover {
          background-color: #333333;
        }
      }
    }
  }
}
.add-menu {
  &-title {
    font-size: 14px;
    color: #666;
    line-height: 40px;
  }
  &-checked {
    padding: 20px;
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    background-color: #f8f9fb;
    &-item {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 10px;
      width: 148px;
      height: 42px;
      background: #fff;
      border: 1px solid #cbd6e2;
      border-radius: 5px;
      .item-icon {
        font-size: 20px;
        color: #2e73f3;
        transition: color 0.3s ease;
      }
      // 名称
      .item-name {
        font-size: 16px;
        color: #333333;
        text-align: center;
        transition: color 0.3s ease;
      }
      .item-delete {
        position: absolute;
        top: -8px;
        right: -8px;
        width: 16px;
        height: 16px;
        background-color: #666666;
        color: #ffffff;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        cursor: pointer;
        z-index: 10;
        &:hover {
          background-color: #333333;
        }
      }
    }
  }
}
::v-deep {
  // 自定义复选框样式
  .el-checkbox.add-menu-checkbox {
    margin-bottom: 10px;
    .el-checkbox__label {
      font-size: 12px;
      color: #666;
      line-height: 20px;
      padding: 5px 20px;
      border: 1px solid #cbd7e2;
      border-radius: 5px;
      margin-left: 10px;
    }
    &.is-checked {
      .el-checkbox__label {
        border-color: #2e73f3;
        color: #2e73f3;
      }
    }
  }
}
</style>
