<template>
  <div>
    <el-dialog v-dialogDragBox :title="title" :visible.sync="open" width="90%" class="custom-dialog" :before-close="beforeClose" append-to-body>
      <div style="padding: 0 20px">
        <el-form :model="form" :rules="rules" ref="form" label-width="6em" class="custom-form">
          <el-row :gutter="20" style="display: flex; flex-wrap: wrap">
            <!-- 单据类型 -->
            <el-col :span="6">
              <el-form-item label="单据类型" prop="fbilltypeid">
                <el-select v-model="form.fbilltypeid" filterable placeholder="请选择单据类型" class="full-width" @change="handleChangeBillType">
                  <el-option v-for="item in billTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 业务类型 -->
            <el-col :span="6">
              <el-form-item label="业务类型" prop="fbusinesstype">
                <el-select v-model="form.fbusinesstype" filterable placeholder="请选择业务类型" class="full-width" disabled>
                  <el-option v-for="item in businessTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 入库日期 -->
            <el-col :span="6">
              <el-form-item label="入库日期" prop="fdate">
                <el-date-picker v-model="form.fdate" type="date" placeholder="请选择入库日期" class="full-width"></el-date-picker>
              </el-form-item>
            </el-col>
            <!-- 需求组织 -->
            <el-col :span="6">
              <el-form-item label="需求组织" prop="fdemandorgid">
                <el-select v-model="form.fdemandorgid" filterable placeholder="请选择需求组织" class="full-width">
                  <el-option v-for="item in ApplicationOrgId" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 供应商 -->
            <el-col :span="6">
              <el-form-item label="供应商" prop="fsuppliername">
                <supplier-search-select :keyword.sync="form.fsuppliername" :useOrg="form.fstockorgid" :showLabel="false" isBack @callBack="handleSupplierSelect($event)" class="full-width" />
              </el-form-item>
            </el-col>
            <!-- 采购组织 -->
            <el-col :span="6">
              <el-form-item label="采购组织" prop="fpurchaseorgid">
                <el-select v-model="form.fpurchaseorgid" filterable placeholder="请选择采购组织" class="full-width" @change="handleChangeSaleOrg">
                  <el-option v-for="item in ApplicationOrgId" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 采购部门 -->
            <el-col :span="6">
              <el-form-item label="采购部门" prop="fpurchasedeptid">
                <el-select v-model="form.fpurchasedeptid" filterable placeholder="请选择采购部门" class="full-width" @change="handleChangeSaleDept">
                  <el-option v-for="item in purchaseDeptList" :key="item.FNumber" :label="item.FName" :value="item.FNumber"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 采购员 -->
            <el-col :span="6">
              <el-form-item label="采购员" prop="fpurchaserid">
                <el-select v-model="form.fpurchaserid" filterable placeholder="请选择采购员" class="full-width" :disabled="!form.fpurchasedeptid || !form.fpurchaseorgid">
                  <el-option v-for="item in calculateApplicantList" :key="item.Number" :label="item.Name" :value="item.Number"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 收料组织 -->
            <el-col :span="6">
              <el-form-item label="收料组织" prop="fstockorgid">
                <el-select v-model="form.fstockorgid" filterable placeholder="请选择收料组织" class="full-width" @change="handleChangeStockOrg">
                  <el-option v-for="item in ApplicationOrgId" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 收料部门 -->
            <el-col :span="6">
              <el-form-item label="收料部门" prop="fstockdeptid">
                <el-select v-model="form.fstockdeptid" filterable placeholder="请选择收料部门" class="full-width" @change="handleChangeStockDept">
                  <el-option v-for="item in stockDeptList" :key="item.FNumber" :label="item.FName" :value="item.FNumber"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 仓管员 -->
            <el-col :span="6">
              <el-form-item label="仓管员" prop="fstockerid">
                <el-select v-model="form.fstockerid" filterable placeholder="请选择仓管员" class="full-width" :disabled="!form.fstockdeptid || !form.fstockorgid">
                  <el-option v-for="item in calculateStockerList" :key="item.Number" :label="item.Name" :value="item.Number"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 结算组织 -->
            <el-col :span="6">
              <el-form-item label="结算组织" prop="fsettleorgid">
                <el-select v-model="form.fsettleorgid" filterable placeholder="请选择结算组织" class="full-width">
                  <el-option v-for="item in ApplicationOrgId" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 定价时点 -->
            <el-col :span="6">
              <el-form-item label="定价时点" prop="fpricetimepoint">
                <el-select v-model="form.fpricetimepoint" filterable placeholder="请选择定价时点" class="full-width">
                  <el-option v-for="item in PriceTimePointOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 结算币别 -->
            <el-col :span="6">
              <el-form-item label="结算币别" prop="fsettlecurrid">
                <el-select v-model="form.fsettlecurrid" filterable placeholder="请选择结算币别" class="full-width">
                  <el-option v-for="item in CurrencyId" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 货主类型 -->
            <el-col :span="6">
              <el-form-item label="货主类型" prop="fownertypeidhead">
                <el-select v-model="form.fownertypeidhead" filterable placeholder="请选择货主类型" class="full-width" disabled>
                  <el-option v-for="item in OwnerTypeIdHeadOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 货主 -->
            <el-col :span="6">
              <el-form-item label="货主" prop="fowneridhead">
                <el-select v-model="form.fowneridhead" filterable placeholder="请选择货主" class="full-width">
                  <el-option v-for="item in ApplicationOrgId" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 备注 -->
            <el-col :span="12">
              <el-form-item label="备注" prop="fscmjremarks">
                <el-input v-model="form.fscmjremarks" placeholder="请输入备注"></el-input>
              </el-form-item>
            </el-col>
            <!-- 明细信息 -->
            <el-col :span="24">
              <el-table :data="form.entities" class="full-width custom-table custom-table-cell0" stripe show-summary :summary-method="getSummary">
                <!-- 序号 -->
                <el-table-column type="index" label="序号" width="50" align="center"></el-table-column>
                <!-- 物料编码 -->
                <el-table-column label="物料编码" align="center" show-overflow-tooltip>
                  <template slot-scope="{ row }">
                    <span class="table-link" @click="handleMaterialNumber(row.fmaterialid)">{{ row.fmaterialid }}</span>
                  </template>
                </el-table-column>
                <!-- 物料名称 -->
                <el-table-column label="物料名称" align="center" show-overflow-tooltip>
                  <template slot-scope="{ row }">{{ row.fmaterialname }}</template>
                </el-table-column>
                <!-- 库存单位 -->
                <el-table-column prop="funitid" label="库存单位" width="100" align="center">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.funitid`" :rules="rules.funitid">
                      <el-select v-model="scope.row.funitid" filterable placeholder="请选择库存单位" class="full-width" size="small">
                        <el-option v-for="item in UnitList" :key="item.FNumber" :label="item.FName" :value="item.FNumber"></el-option>
                      </el-select>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 规格型号 -->
                <el-table-column label="规格型号" align="center" show-overflow-tooltip>
                  <template slot-scope="{ row }">{{ row.fmaterialmodel }}</template>
                </el-table-column>
                <!-- 应收数量 -->
                <el-table-column prop="fmustqty" label="应收数量" width="100" align="center"></el-table-column>
                <!-- 实收数量 -->
                <el-table-column prop="frealqty" label="实收数量" width="100" align="center">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.frealqty`" :rules="rules.frealqty">
                      <el-input v-model="scope.row.frealqty" placeholder="请输入实收数量" class="full-width" size="small" @blur="handleChangeRealQty(scope.row)"></el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 计价单位 -->
                <el-table-column prop="fpriceunitid" label="计价单位" width="100" align="center">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.fpriceunitid`" :rules="rules.fpriceunitid">
                      <el-select v-model="scope.row.fpriceunitid" filterable placeholder="请选择计价单位" class="full-width" size="small">
                        <el-option v-for="item in UnitList" :key="item.FNumber" :label="item.FName" :value="item.FNumber"></el-option>
                      </el-select>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 计价数量 -->
                <el-table-column prop="fpriceunitqty" label="计价数量" width="100" align="center"></el-table-column>
                <!-- 批号 -->
                <el-table-column prop="flot" label="批号" width="120" align="center">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.flot`" :rules="rules.flot">
                      <el-input v-model="scope.row.flot" placeholder="请输入批号" class="full-width" size="small"></el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 仓库 -->
                <el-table-column prop="fstockid" label="仓库" width="120" align="center">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.fstockid`" :rules="rules.fstockid">
                      <stock-search-select size="small" :useOrg="form.fstockorgid" :showLabel="false" :keyword.sync="scope.row.fstockname" class="full-width" @callBack="handleStockSelect($event, scope.row)" />
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 库存状态 -->
                <el-table-column prop="fstockstatusid" label="库存状态" width="100" align="center">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.fstockstatusid`" :rules="rules.fstockstatusid">
                      <el-select v-model="scope.row.fstockstatusid" filterable placeholder="请选择库存状态" class="full-width" size="small">
                        <el-option v-for="item in stockStatusOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                      </el-select>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 是否赠品 -->
                <el-table-column prop="fgiveaway" label="是否赠品" width="80" align="center">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.fgiveaway`">
                      <el-checkbox v-model="scope.row.fgiveaway" size="small"></el-checkbox>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 备注 -->
                <el-table-column prop="fnote" label="备注" width="120" align="center">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.fnote`" :rules="rules.fnote">
                      <el-input v-model="scope.row.fnote" placeholder="请输入备注" class="full-width" size="small"></el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 货主类型 -->
                <el-table-column prop="fownertypeid" label="货主类型" width="120" align="center">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.fownertypeid`" :rules="rules.fownertypeid">
                      <el-select v-model="scope.row.fownertypeid" filterable placeholder="请选择货主类型" class="full-width" size="small">
                        <el-option v-for="item in OwnerTypeIdHeadOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                      </el-select>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 货主 -->
                <el-table-column prop="fownerid" label="货主" width="120" align="center">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.fownerid`" :rules="rules.fownerid">
                      <el-select v-model="scope.row.fownerid" filterable placeholder="请选择货主" class="full-width" size="small">
                        <el-option v-for="item in ApplicationOrgId" :key="item.value" :label="item.label" :value="item.value"></el-option>
                      </el-select>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 辅助数量(kg) -->
                <el-table-column prop="fscmjfzslkg" label="辅助数量(kg)" width="120" align="center">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.fscmjfzslkg`" :rules="rules.fscmjfzslkg">
                      <el-input v-model="scope.row.fscmjfzslkg" placeholder="请输入辅助数量" class="full-width" size="small"></el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn" @click="handleClose()">取消</el-button>
        <el-button class="custom-dialog-btn primary" :loading="loading" @click="handleSubmit">确定</el-button>
      </div>
    </el-dialog>
    <!-- 采购入库单详情 -->
    <in-storage-detail ref="inStorageDetail" />
  </div>
</template>
<script>
import { kingdee } from '@/minix'
import { isNumber, isNumberLength } from '@/utils/validate'
import StockSearchSelect from '@/components/SearchSelect/stock'
import SupplierSearchSelect from '@/components/SearchSelect/supplier'
import { getSalesmanList } from '@/api/kingdee'
import { getInStorageDetail2, createInStorage, deleteInStorageV2 } from '@/api/kingdee/purchase/inStorage'
import InStorageDetail from '@/views/kingdee/purchase/inStorage/detail'

export default {
  name: 'InStorageCreate',
  mixins: [kingdee],
  components: { StockSearchSelect, SupplierSearchSelect, InStorageDetail },
  data() {
    return {
      fid: undefined,
      form: {},
      rules: {
        fstockorgid: [{ required: true, message: '请选择收料组织', trigger: 'change' }],
        fdate: [{ required: true, message: '请选择入库日期', trigger: 'change' }],
        fbilltypeid: [{ required: true, message: '请选择单据类型', trigger: 'change' }],
        fownertypeidhead: [{ required: true, message: '请选择货主类型', trigger: 'change' }],
        fowneridhead: [{ required: true, message: '请选择货主', trigger: 'change' }],
        fpurchaseorgid: [{ required: true, message: '请选择采购组织', trigger: 'change' }],
        fsuppliername: [{ required: true, message: '请选择供应商', trigger: 'change' }],
        fbusinesstype: [{ required: true, message: '请选择业务类型', trigger: 'change' }],
        fsettleorgid: [{ required: true, message: '请选择结算组织', trigger: 'change' }],
        fsettlecurrid: [{ required: true, message: '请选择结算币别', trigger: 'change' }],
        fpricetimepoint: [{ required: true, message: '请选择定价时点', trigger: 'change' }],
        fmaterialid: [{ required: true, message: '请选择物料编码', trigger: 'change' }],
        funitid: [{ required: true, message: '请选择库存单位', trigger: 'change' }],
        fpriceunitid: [{ required: true, message: '请选择计价单位', trigger: 'change' }],
        fstockid: [{ required: true, message: '请选择仓库', trigger: 'change' }],
        fstockstatusid: [{ required: true, message: '请选择库存状态', trigger: 'change' }],
        fownertypeid: [{ required: true, message: '请选择货主类型', trigger: 'change' }],
        fownerid: [{ required: true, message: '请选择货主', trigger: 'change' }],
        frealqty: [
          { validator: isNumber, message: '请输入正确的实收数量', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: 'blur' }
        ],
        fscmjfzslkg: [
          { validator: isNumber, message: '请输入正确的辅助数量', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: 'blur' }
        ]
      },
      title: '新增采购入库单',
      open: false,
      loading: false,
      // 配置选项数据
      staticOptions: {
        // 单据类型
        billType: [
          { value: 'RKD01_SYS', label: '标准采购入库单' },
          { value: 'RKD02_SYS', label: '零散采购入库单' },
          { value: 'RKD03_SYS', label: '委外入库单' },
          { value: 'RKD04_SYS', label: '分销购销入库单' },
          { value: 'RKD05_SYS', label: '资产入库单' },
          { value: 'RKD06_SYS', label: '费用入库单' },
          { value: 'RKD07_SYS', label: 'VMI入库单' },
          { value: 'RKD08_SYS', label: '现购入库单' },
          { value: 'RKD09_SYS', label: '直运入库单' }
        ],
        // 业务类型
        businessType: [
          { value: 'CG', label: '标准采购', target: 'RKD01_SYS' },
          { value: 'WW', label: '标准委外', target: 'RKD03_SYS' },
          { value: 'DRPSALE', label: '分销购销', target: 'RKD04_SYS' },
          { value: 'ZCCG', label: '资产采购', target: 'RKD05_SYS' },
          { value: 'FYCG', label: '费用采购', target: 'RKD06_SYS' },
          { value: 'VMICG', label: 'VMI采购', target: 'RKD07_SYS' },
          { value: 'ZYCG', label: '直运采购', target: 'RKD09_SYS' }
        ],
        // 库存状态
        stockStatus: [
          { value: 'KCZT01_SYS', label: '可用' },
          { value: 'KCZT02_SYS', label: '待检' },
          { value: 'KCZT03_SYS', label: '冻结' },
          { value: 'KCZT04_SYS', label: '退回冻结' },
          { value: 'KCZT07_SYS', label: '废品' },
          { value: 'KCZT08_SYS', label: '不良' },
          { value: 'KCZT09_SYS', label: '已包装' },
          { value: 'KCZT099_SYS', label: '外借' }
        ],
        // 定价时点
        priceTimePoint: [
          { label: '系统日期', value: '1' },
          { label: '单据日期', value: '2' }
        ],
        // 货主类型
        ownerTypeHead: [
          { label: '业务组织', value: 'BD_OwnerOrg' },
          { label: '供应商', value: 'BD_Supplier', target: 'RKD07_SYS' }
        ]
      },
      // 动态数据
      applicantList: [], // 采购员
      stockerList: [], // 仓管员
      isPush: false,
      hasSuccessfully: false // 是否已成功提交
    }
  },
  computed: {
    // 获取配置选项的getter（避免重复引用）
    billTypeOptions() {
      return this.staticOptions.billType
    },
    businessTypeOptions() {
      return this.staticOptions.businessType
    },
    stockStatusOptions() {
      return this.staticOptions.stockStatus
    },
    PriceTimePointOptions() {
      return this.staticOptions.priceTimePoint
    },
    OwnerTypeIdHeadOptions() {
      return this.staticOptions.ownerTypeHead
    },
    // 过滤后的部门列表 - 采购部门
    purchaseDeptList() {
      return this.ApplicationDeptId.filter(item => item.FUseOrg === this.form.fpurchaseorgid)
    },
    // 过滤后的部门列表 - 收料部门
    stockDeptList() {
      return this.ApplicationDeptId.filter(item => item.FUseOrg === this.form.fstockorgid)
    },
    // 采购员列表
    calculateApplicantList() {
      return this.getProcessedEmployeeList(this.applicantList, this.form.fpurchasedeptid)
    },
    // 仓管员列表
    calculateStockerList() {
      return this.getProcessedEmployeeList(this.stockerList, this.form.fstockdeptid)
    }
  },
  methods: {
    // 通用的员工列表处理方法
    getProcessedEmployeeList(employeeList, deptId) {
      if (!deptId) return []
      const dept = this.ApplicationDeptId.find(item => item.FNumber === deptId) || {}
      const deptName = dept.FName || ''
      const filteredList = employeeList.filter(item => item.FDept === deptName)
      // 处理员工编号，只取前三位数字
      return filteredList.map(item => ({
        ...item,
        Number: item.Number ? item.Number.split('_')[0] : item.Number
      }))
    },
    // 获取员工列表的通用方法
    async fetchEmployeeList(orgId, operatorType) {
      if (!orgId) return []
      try {
        const params = { bizOrg: orgId, OperatorType: operatorType }
        const res = await getSalesmanList(params)
        const { code, msg, data } = res

        if (code === 200) {
          return data?.data || []
        } else {
          this.$message.error(msg)
          return []
        }
      } catch (error) {
        console.error(`获取${operatorType}列表失败:`, error)
        return []
      }
    },
    // 采购员远程方法
    async ApplicantRemoteMethod() {
      this.applicantList = await this.fetchEmployeeList(this.form.fpurchaseorgid, 'CGY')
    },
    // 仓管员远程方法
    async StockerRemoteMethod() {
      this.stockerList = await this.fetchEmployeeList(this.form.fstockorgid, 'WHY')
    },
    // 关闭弹窗前的处理
    beforeClose() {
      this.handleClose()
    },
    // 关闭弹窗
    async handleClose(flag = false) {
      if (this.fid && !this.hasSuccessfully) {
        try {
          await deleteInStorageV2({ id: this.fid })
          this.fid = undefined
          this.$nextTick(() => {
            this.open = false
            this.$emit('callBack', flag)
          })
        } catch (error) {
          this.$message.error(error.message)
        }
      } else {
        this.open = false
        this.$emit('callBack', flag)
      }
    },
    // 表单重置
    reset() {
      this.form = {
        entities: [], // 明细信息
        fbilltypeid: undefined, // 单据类型
        fbusinesstype: undefined, // 业务类型
        fdate: undefined, // 入库日期
        fdemandorgid: undefined, // 需求组织
        fid: undefined, // 主键
        fowneridhead: undefined, // 货主
        fownertypeidhead: undefined, // 货主类型
        fpayconditionid: undefined, // 付款条件
        fpricetimepoint: undefined, // 定价时点
        fpurchasedeptid: undefined, // 采购部门
        fpurchaseorgid: undefined, // 采购组织
        fpurchaserid: undefined, // 采购员
        fscmjremarks: undefined, // 备注
        fsettlecurrid: undefined, // 结算币种
        fsettleorgid: undefined, // 结算组织
        fsettletypeid: undefined, // 结算方式
        fstockdeptid: undefined, // 收料部门
        fstockerid: undefined, // 仓管员
        fstockorgid: undefined, // 收料组织
        fsupplierid: undefined, // 供应商
        fsuppliername: undefined // 供应商名称
      }
      this.resetForm('form')
      this.isPush = false
      this.hasSuccessfully = false
    },
    // 数据初始化
    async initPush(fid, type = undefined) {
      if (!fid) {
        this.$message.warning('参数错误，请刷新页面重试')
        return
      }
      const loading = this.$loading({
        lock: true,
        text: '加载中...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      try {
        this.reset()
        this.isPush = type == 'push'
        const res = await getInStorageDetail2({ fid })
        const { code, msg, data } = res
        if (code === 200) {
          this.processInitialData(data, fid)
          this.open = true
          // 异步加载员工列表
          this.$nextTick(() => {
            this.ApplicantRemoteMethod()
            this.StockerRemoteMethod()
          })
        } else {
          this.$message.error(msg)
        }
      } catch (error) {
        console.error('初始化数据失败:', error)
        this.$message.error('加载数据失败，请重试')
      } finally {
        loading.close()
      }
    },
    // 处理初始化数据
    processInitialData(data, fid) {
      const info = data?.result?.result || {}
      const detailEntities = info?.InStockEntry || []
      const oinfo = info?.InStockFin?.[0] || {}
      // 处理明细数据
      const entities = detailEntities.map(item => ({
        fentryid: item?.Id || undefined, // 使用时需检查
        fmaterialid: item?.MaterialId?.Number || undefined,
        fmaterialname: (item?.MaterialId?.Name && this.getString(item?.MaterialId?.Name)) || undefined,
        fmaterialmodel: (item?.MaterialId?.Specification && this.getString(item?.MaterialId?.Specification)) || undefined,
        funitid: item?.UnitID?.Number || undefined,
        frealqty: item?.RealQty || undefined,
        fpriceunitid: item?.PriceUnitID?.Number || undefined,
        fpriceunitqty: item?.PriceUnitQty || undefined,
        flot: item?.Lot?.Number || undefined,
        fstockid: item?.StockId?.Number || undefined,
        fstockname: (item?.StockId?.Name && this.getString(item?.StockId?.Name)) || undefined,
        fstockstatusid: item?.StockStatusId?.Number || undefined,
        fgiveaway: item?.GiveAway || undefined,
        fnote: item?.Note || undefined,
        fownertypeid: item?.OWNERTYPEID || undefined,
        fownerid: item?.OWNERID?.Number || undefined,
        fscmjfzslkg: item?.F_SCMJ_FZSLkg || undefined
      }))
      // 设置表单数据
      this.form = {
        entities,
        fbilltypeid: info?.FBillTypeID?.Number || undefined,
        fbusinesstype: info?.BusinessType || undefined,
        fdate: info?.Date || undefined,
        fdemandorgid: info?.DemandOrgId?.Number || undefined,
        fid,
        fowneridhead: info?.OwnerIdHead?.Number || undefined,
        fownertypeidhead: info?.OwnerTypeIdHead || undefined,
        fpayconditionid: oinfo?.PayConditionId?.Number || undefined,
        fpricetimepoint: oinfo?.PriceTimePoint || undefined,
        fpurchasedeptid: info?.PurchaseDeptId?.Number || undefined,
        fpurchaseorgid: info?.PurchaseOrgId?.Number || undefined,
        fpurchaserid: info?.PurchaserId?.Number || undefined,
        fscmjremarks: info?.F_SCMJ_Remarks || undefined,
        fsettlecurrid: oinfo?.SettleCurrId?.Number || undefined,
        fsettleorgid: oinfo?.SettleOrgId?.Number || undefined,
        fsettletypeid: oinfo?.FSettleTypeId?.Number || undefined,
        fstockdeptid: info?.StockDeptId?.Number || undefined,
        fstockerid: info?.StockerId?.Number || undefined,
        fstockorgid: info?.StockOrgId?.Number || undefined,
        fsupplierid: info?.SupplierId?.Number || undefined,
        fsuppliername: (info?.SupplierId?.Name && this.getString(info?.SupplierId?.Name)) || undefined
      }
      this.fid = fid
      this.title = '新增采购入库单'
    },
    // 改变单据类型
    handleChangeBillType(val) {
      const businessTypeObj = this.businessTypeOptions.find(item => item.target === val) || this.businessTypeOptions[0]
      this.form.fbusinesstype = businessTypeObj.value
      const ownerTypeObj = this.OwnerTypeIdHeadOptions.find(item => item.target === val) || this.OwnerTypeIdHeadOptions[0]
      this.form.fownertypeidhead = ownerTypeObj.value
    },
    // 改变收料组织
    handleChangeStockOrg(val) {
      // 清空关联字段
      this.form.fstockdeptid = undefined
      this.form.fstockerid = undefined
      // 同步相关组织字段
      this.$set(this.form, 'fdemandorgid', val)
      this.$set(this.form, 'fpurchaseorgid', val)
      this.$set(this.form, 'fsettleorgid', val)
      this.$set(this.form, 'fowneridhead', val)
      this.StockerRemoteMethod()
    },
    // 改变收料部门
    handleChangeStockDept() {
      this.form.fstockerid = undefined
    },
    // 改变采购组织
    handleChangeSaleOrg() {
      this.form.fpurchasedeptid = undefined
      this.form.fpurchaserid = undefined
      this.ApplicantRemoteMethod()
    },
    // 改变采购部门
    handleChangeSaleDept() {
      this.form.fpurchaserid = undefined
    },
    // 改变实收数量
    handleChangeRealQty(row) {
      this.$set(row, 'fpriceunitqty', row.frealqty)
    },
    // 选择供应商
    handleSupplierSelect(data) {
      this.$set(this.form, 'fsupplierid', data.FNumber)
      this.$set(this.form, 'fsuppliername', data.FName)
    },
    // 选择仓库
    handleStockSelect(stock, row) {
      this.$set(row, 'fstockid', stock.Number)
      this.$set(row, 'fstockname', stock.Name)
    },
    // 获取表格合计
    getSummary(param) {
      const { columns, data } = param
      const summaryFields = ['fmustqty', 'frealqty', 'fpriceunitqty', 'fscmjfzslkg']
      return columns.map((column, index) => {
        if (index === 0) return '合计'
        if (summaryFields.includes(column.property)) {
          return `${this.calculateTotal(data, column.property)}`
        }
        return ''
      })
    },
    // 计算合计
    calculateTotal(data, key) {
      const total = data.reduce((sum, item) => {
        const value = parseFloat(item[key]) || 0
        return sum + value
      }, 0)
      return parseFloat(total.toFixed(5))
    },
    // 提交表单
    async handleSubmit() {
      try {
        const valid = await this.$refs.form.validate()
        if (!valid) return
        this.loading = true
        const res = await createInStorage(this.form)
        const { code, msg } = res
        if (code === 200) {
          this.$message.success('操作成功')
          this.hasSuccessfully = true
          if (this.isPush) {
            const { number } = res.data
            if (number) {
              this.open = false
              this.$nextTick(() => {
                this.$refs.inStorageDetail.getInfo({ BillNo: number })
              })
            } else {
              this.open = false
              this.$emit('callBack', true)
            }
          } else {
            this.open = false
            this.$emit('callBack', true)
          }
        } else {
          this.$message.error(msg)
        }
      } catch (error) {
        console.error('提交失败:', error)
      } finally {
        this.loading = false
      }
    },
    // 物料详情
    handleMaterialNumber(number) {
      if (number && this.$parent.handleMaterialNumber) {
        this.$parent.handleMaterialNumber(number)
      }
    }
  }
}
</script>
<style scoped lang="scss">
@import '@/assets/styles/custom-new.scss';
.full-width {
  width: 100%;
}
::v-deep {
  .custom-table {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    border-bottom: 0;
    margin-bottom: 20px;
    .el-form-item {
      margin-top: 10px !important;
      margin-bottom: 10px !important;
      .el-form-item__error {
        top: 95%;
        padding-top: 0;
      }
    }
    .el-input--prefix .el-input__inner {
      padding-left: 20px;
      padding-right: 20px;
    }
    .el-input--prefix.el-date-editor--date .el-input__inner {
      padding-left: 30px;
    }
    .el-date-editor--datetime .el-input__inner {
      padding-left: 30px;
      padding-right: 30px;
    }
  }
}
</style>
