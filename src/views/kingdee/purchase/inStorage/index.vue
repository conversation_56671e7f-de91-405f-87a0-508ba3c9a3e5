<template>
  <div class="newBox bgcf9 vh-85">
    <!-- 搜索 -->
    <div class="custom-search flex" style="padding-top: 18px" :style="{ justifyContent: showSearch ? 'space-between' : 'flex-end' }">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" @submit.native.prevent>
        <el-form-item label="单据编号" prop="billNo">
          <el-input v-model="queryParams.billNo" placeholder="请输入单据编号" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="物料名称" prop="materialName">
          <el-input v-model="queryParams.materialName" placeholder="请输入物料名称" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="供应商" prop="supplierName">
          <el-input v-model="queryParams.supplierName" placeholder="请输入供应商" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="单据状态" prop="documentStatus">
          <el-select v-model="queryParams.documentStatus" placeholder="请选择单据状态" clearable>
            <el-option v-for="item in DocumentStatusOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="使用组织" prop="useOrg">
          <el-select v-model="queryParams.useOrg" placeholder="请选择使用组织" clearable filterable>
            <el-option v-for="item in ApplicationOrgId" :key="item.value" :label="item.label" :value="item.label" />
          </el-select>
        </el-form-item>
        <!-- 结算组织 -->
        <el-form-item label="结算组织" prop="settleOrg">
          <el-select v-model="queryParams.settleOrg" placeholder="请选择结算组织" clearable filterable>
            <el-option v-for="item in ApplicationOrgId" :key="item.value" :label="item.label" :value="item.label" />
          </el-select>
        </el-form-item>
        <!-- 收料组织 -->
        <el-form-item label="收料组织" prop="stockOrg">
          <el-select v-model="queryParams.stockOrg" placeholder="请选择收料组织" clearable filterable>
            <el-option v-for="item in ApplicationOrgId" :key="item.value" :label="item.label" :value="item.label" />
          </el-select>
        </el-form-item>
        <!-- 采购日期 -->
        <el-form-item label="采购日期" prop="date">
          <el-date-picker v-model="queryParams.date" placeholder="请选择采购日期" type="date" value-format="yyyy-MM-dd" size="small"></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="small" @click="handleResetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns" isSetitem @updateColumns="updateColumns"></right-toolbar>
    </div>
    <!-- 列表 -->
    <div style="padding: 15px 20px">
      <el-table v-loading="loading" ref="allTable" border :data="list" row-key="id" style="width: 100%" class="custom-table" :span-method="objectSpanMethod">
        <el-table-column align="center" type="index" label="序号" v-if="columns[0].visible"></el-table-column>
        <!-- 单据编号 -->
        <el-table-column align="center" prop="BillNo" label="单据编号" show-overflow-tooltip v-if="columns[1].visible">
          <template slot-scope="{ row }">
            <span class="table-link" @click="handleDetail(row)">{{ row.BillNo }}</span>
          </template>
        </el-table-column>
        <!-- 入库日期 -->
        <el-table-column align="center" prop="Date" label="入库日期" show-overflow-tooltip v-if="columns[2].visible">
          <template slot-scope="{ row }">{{ parseTime(row.Date, '{y}-{m}-{d}') }}</template>
        </el-table-column>
        <!-- 供应商 -->
        <el-table-column align="center" prop="Supplier" label="供应商" show-overflow-tooltip v-if="columns[3].visible"></el-table-column>
        <!-- 单据状态 -->
        <el-table-column align="center" prop="DocumentStatus" label="单据状态" show-overflow-tooltip v-if="columns[4].visible">
          <template slot-scope="{ row }">{{ getDocumentStatusLabel(row.DocumentStatus) }}</template>
        </el-table-column>
        <!-- 使用组织 -->
        <el-table-column align="center" prop="PurchaseOrg" label="使用组织" show-overflow-tooltip v-if="columns[5].visible"></el-table-column>
        <!-- 物料编码-->
        <el-table-column align="center" prop="MaterialNumber" label="物料编码" show-overflow-tooltip v-if="columns[6].visible">
          <template slot-scope="{ row }">
            <span class="table-link" @click="handleMaterialNumber(row.MaterialNumber)">{{ row.MaterialNumber }}</span>
          </template>
        </el-table-column>
        <!-- 物料名称 -->
        <el-table-column align="center" prop="MaterialName" label="物料名称" show-overflow-tooltip v-if="columns[7].visible"></el-table-column>
        <!-- 库存单位-->
        <el-table-column align="center" prop="UnitName" label="库存单位" show-overflow-tooltip v-if="columns[8].visible"></el-table-column>
        <!-- 实收数量-->
        <el-table-column align="center" prop="RealQty" label="实收数量" show-overflow-tooltip v-if="columns[9].visible"></el-table-column>
        <!-- 批号-->
        <el-table-column align="center" prop="LotName" label="批号" show-overflow-tooltip v-if="columns[10].visible"></el-table-column>
        <!-- 仓库-->
        <el-table-column align="center" prop="StockName" label="仓库" show-overflow-tooltip v-if="columns[11].visible"></el-table-column>
        <!-- 仓位(未明确) -->
        <el-table-column align="center" prop="StockLocName" label="仓位" show-overflow-tooltip v-if="columns[12].visible"></el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="custom-pagination">
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.limit" @pagination="getList" :local="userId + '.inStoragePageSize'" />
      </div>
    </div>
    <!-- 详情 -->
    <in-storage-detail ref="inStorageDetail" @callBack="handleDetailCallBack" @update="handleUpdate" v-if="showDetail" />
    <!-- 物料详情 -->
    <material-detail ref="materialDetail" v-if="showMaterialDetail" />
  </div>
</template>
<script>
import { getInStorageList } from '@/api/kingdee/purchase/inStorage'
import InStorageDetail from '@/views/kingdee/purchase/inStorage/detail'
import MaterialDetail from '@/views/kingdee/material/detail'
import { kingdee } from '@/minix'

export default {
  name: 'InStorage',
  mixins: [kingdee],
  components: { InStorageDetail, MaterialDetail },
  data() {
    return {
      // 搜索
      queryParams: {
        pageNum: 1, // 当前页码
        startRow: 0, // 开始行索引
        limit: 10, // 最大行数
        useOrg: undefined, // 使用组织
        billNo: undefined, // 单据编号
        materialNumber: undefined, // 物料编码
        documentStatus: undefined, // 单据状态
        supplierName: undefined, // 供应商
        materialName: undefined, // 物料名称
        settleOrg: undefined, // 结算组织
        stockOrg: undefined, // 收料组织
        date: undefined // 采购日期
      },
      // 列表
      list: [],
      total: 0,
      loading: false,
      showDetail: false, // 详情
      showMaterialDetail: false, // 物料详情
      // 列表显隐
      showSearch: true,
      // 列信息
      columns: [
        { key: 0, label: `序号`, visible: true },
        { key: 1, label: `单据编号`, visible: true },
        { key: 2, label: `入库日期`, visible: true },
        { key: 3, label: `供应商`, visible: true },
        { key: 4, label: `单据状态`, visible: true },
        { key: 5, label: `使用组织`, visible: true },
        { key: 6, label: `物料编码`, visible: true },
        { key: 7, label: `物料名称`, visible: true },
        { key: 8, label: `库存单位`, visible: true },
        { key: 9, label: `实收数量`, visible: true },
        { key: 10, label: `批号`, visible: true },
        { key: 11, label: `仓库`, visible: true },
        { key: 12, label: `仓位`, visible: true }
      ]
    }
  },
  computed: {
    // 获取当前用户ID
    userId() {
      return this.$store.getters.info.userId
    }
  },
  created() {
    const columns = localStorage.getItem(this.userId + '.inStorageColumns')
    if (columns) this.columns = JSON.parse(columns)
    // 读取缓存的分页大小
    const cachedPageSize = localStorage.getItem(this.userId + '.inStoragePageSize')
    if (cachedPageSize) {
      this.queryParams.limit = parseInt(cachedPageSize)
    }
    // 获取列表
    this.getList()
  },
  methods: {
    // 改变显隐列缓存
    updateColumns(data = []) {
      localStorage.setItem(this.userId + '.inStorageColumns', JSON.stringify(data))
    },
    // 列表
    getList() {
      this.loading = true
      this.queryParams.startRow = Math.max(0, (this.queryParams.pageNum - 1) * this.queryParams.limit)
      const query = { ...this.queryParams }
      delete query.pageNum
      getInStorageList(query).then(res => {
        const { code, data, msg } = res
        if (code === 200) {
          const { data: list, total } = data
          this.list = list
          this.total = total
        } else this.$message.error(msg)
        this.loading = false
      })
    },
    // 相同字段合并
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      const visibleColumns = this.columns.filter(col => col.visible)
      const currentColumnKey = visibleColumns[columnIndex]?.key
      if (currentColumnKey >= 1 && currentColumnKey <= 5) {
        const _row = this.calculateSpan(this.list, 'BillNo')[rowIndex]
        const _col = _row > 0 ? 1 : 0
        return {
          rowspan: _row,
          colspan: _col
        }
      }
    },
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 重置搜索
    handleResetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 查看物料
    handleMaterialNumber(number) {
      this.showMaterialDetail = true
      this.$nextTick(() => {
        this.$refs.materialDetail.getInfo(number)
      })
    },
    // 查看详情
    handleDetail(row) {
      this.showDetail = true
      this.$nextTick(() => {
        this.$refs.inStorageDetail.getInfo(row)
      })
    },
    // 详情回调
    handleDetailCallBack(flag) {
      this.showDetail = false
      if (flag) this.getList()
    },
    // 更新
    handleUpdate(data) {
      const { billNo, newStatus } = data
      const index = this.list.findIndex(item => item.BillNo === billNo)
      if (index !== -1) this.list[index].DocumentStatus = newStatus
    }
  }
}
</script>
<style scoped lang="scss">
@import '@/assets/styles/custom-new.scss';
</style>
