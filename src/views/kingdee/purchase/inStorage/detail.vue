<template>
  <div>
    <el-dialog v-dialogDragBox title="采购入库单详情" :visible.sync="open" width="90%" class="custom-dialog" :before-close="beforeClose">
      <div style="padding: 0 20px">
        <div class="kindeeButton" :key="info.DocumentStatus">
          <el-button type="primary" size="medium" v-if="info.DocumentStatus == 'A' || info.DocumentStatus == 'D'" @click="handleKingdeeDo('submit')">提交</el-button>
          <el-button type="primary" size="medium" disabled v-else>提交</el-button>
          <el-button type="warning" size="medium" v-if="info.DocumentStatus == 'B'" @click="handleKingdeeDo('audit')">审核</el-button>
          <el-button type="warning" size="medium" disabled v-else>审核</el-button>
          <el-button type="info" size="medium" v-if="info.DocumentStatus == 'B'" @click="handleKingdeeDo('revoke')">撤销</el-button>
          <el-button type="info" size="medium" disabled v-else>撤销</el-button>
          <el-button type="danger" size="medium" v-if="info.DocumentStatus == 'A' || info.DocumentStatus == 'D'" @click="handleKingdeeDo('delete')">删除</el-button>
          <el-button type="danger" size="medium" disabled v-else>删除</el-button>
          <el-button type="warning" size="medium" v-if="info.DocumentStatus == 'C'" @click="handleKingdeeDo('unAudit')">反审</el-button>
          <el-button type="warning" size="medium" disabled v-else>反审</el-button>
          <el-button type="success" size="medium" v-if="info.DocumentStatus == 'C'" @click="handleKingdeeDo('push')">下推</el-button>
          <el-button type="success" size="medium" disabled v-else>下推</el-button>
        </div>
        <el-tabs v-model="activeName" type="card" @tab-click="handleClick" class="custom-tabs">
          <el-tab-pane label="基本信息" name="first"></el-tab-pane>
          <el-tab-pane label="供应商信息" name="second"></el-tab-pane>
          <el-tab-pane label="财务信息" name="third"></el-tab-pane>
          <el-tab-pane label="其他" name="fourth"></el-tab-pane>
        </el-tabs>
        <el-descriptions border :column="4" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 4 - 7em - 22px)' }" v-show="activeName === 'first'">
          <!-- 单据类型 -->
          <el-descriptions-item label="单据类型">{{ info.FBillTypeID && getString(info.FBillTypeID.Name) }}</el-descriptions-item>
          <!-- 需求组织 -->
          <el-descriptions-item label="需求组织">{{ info.DemandOrgId && getString(info.DemandOrgId.Name) }}</el-descriptions-item>
          <!-- 收料组织 -->
          <el-descriptions-item label="收料组织">{{ info.StockOrgId && getString(info.StockOrgId.Name) }}</el-descriptions-item>
          <!-- 订单销售组织 -->
          <el-descriptions-item label="订单销售组织">{{ info.F_SCMJ_DDXSZZ && getString(info.F_SCMJ_DDXSZZ.Name) }}</el-descriptions-item>
          <!-- 业务类型 -->
          <el-descriptions-item label="业务类型">{{ info.BusinessType && getOptionLabel(BusinessTypeOptions, info.BusinessType) }}</el-descriptions-item>
          <!-- 供应商 -->
          <el-descriptions-item label="供应商">{{ info.SupplierId && getString(info.SupplierId.Name) }}</el-descriptions-item>
          <!-- 收料部门 -->
          <el-descriptions-item label="收料部门">{{ info.StockDeptId && getString(info.StockDeptId.Name) }}</el-descriptions-item>
          <!-- 订单销售部门 -->
          <el-descriptions-item label="订单销售部门">{{ info.F_SCMJ_DDXSBM && getString(info.F_SCMJ_DDXSBM.Name) }}</el-descriptions-item>
          <!-- 单据编号 -->
          <el-descriptions-item label="单据编号">{{ info.BillNo }}</el-descriptions-item>
          <!-- 采购组织 -->
          <el-descriptions-item label="采购组织">{{ info.PurchaseOrgId && getString(info.PurchaseOrgId.Name) }}</el-descriptions-item>
          <!-- 仓管员 -->
          <el-descriptions-item label="仓管员">{{ info.StockerId && getString(info.StockerId.Name) }}</el-descriptions-item>
          <!-- 订单销售员 -->
          <el-descriptions-item label="订单销售员">{{ info.F_SCMJ_DDXSY && getString(info.F_SCMJ_DDXSY.Name) }}</el-descriptions-item>
          <!-- 入库日期 -->
          <el-descriptions-item label="入库日期">{{ info.Date && parseTime(info.Date, '{y}-{m}-{d}') }}</el-descriptions-item>
          <!-- 采购部门 -->
          <el-descriptions-item label="采购部门">{{ info.PurchaseDeptId && getString(info.PurchaseDeptId.Name) }}</el-descriptions-item>
          <!-- 备注 -->
          <el-descriptions-item label="备注">{{ info.F_SCMJ_Remarks }}</el-descriptions-item>
          <!-- 销售订单号 -->
          <el-descriptions-item label="销售订单号">{{ info.F_SCMJ_XSDDH }}</el-descriptions-item>
          <!-- 单据状态 -->
          <el-descriptions-item label="单据状态">{{ info.DocumentStatus && getDocumentStatusLabel(info.DocumentStatus) }}</el-descriptions-item>
          <!-- 采购员 -->
          <el-descriptions-item label="采购员">{{ info.PurchaserId && getString(info.PurchaserId.Name) }}</el-descriptions-item>
        </el-descriptions>
        <el-descriptions border :column="3" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 3 - 7em - 22px)' }" v-show="activeName === 'second'">
          <!-- 供货方 -->
          <el-descriptions-item label="供货方">{{ info.SupplyId && getString(info.SupplyId.Name) }}</el-descriptions-item>
          <!-- 供货方联系人 -->
          <el-descriptions-item label="供货方联系人">{{ info.ProviderContactID && getString(info.ProviderContactID.Name) }}</el-descriptions-item>
          <!-- 供货方地址 -->
          <el-descriptions-item label="供货方地址">{{ info.SupplyAddress }}</el-descriptions-item>
          <!-- 结算方 -->
          <el-descriptions-item label="结算方">{{ info.SettleId && getString(info.SettleId.Name) }}</el-descriptions-item>
          <!-- 收款方 -->
          <el-descriptions-item label="收款方">{{ info.ChargeId && getString(info.ChargeId.Name) }}</el-descriptions-item>
          <!-- 邮箱 -->
          <el-descriptions-item label="邮箱">{{ info.FSupplyEMail }}</el-descriptions-item>
        </el-descriptions>
        <el-descriptions border :column="3" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 3 - 7em - 22px)' }" v-show="activeName === 'third'">
          <!-- 结算组织 -->
          <el-descriptions-item label="结算组织">{{ info.InStockFin && info.InStockFin[0] && info.InStockFin[0].SettleOrgId && getString(info.InStockFin[0].SettleOrgId.Name) }}</el-descriptions-item>
          <!-- 定价时点(1、系统日期 2、单据日期) -->
          <el-descriptions-item label="定价时点">{{ info.InStockFin && info.InStockFin[0] && info.InStockFin[0].PriceTimePoint == '1' ? '系统日期' : '单据日期' }}</el-descriptions-item>
          <!-- 本位币 -->
          <el-descriptions-item label="本位币">{{ info.InStockFin && info.InStockFin[0] && info.InStockFin[0].LocalCurrId && getString(info.InStockFin[0].LocalCurrId.Name) }}</el-descriptions-item>
          <!-- 结算方式 -->
          <el-descriptions-item label="结算方式">{{ info.InStockFin && info.InStockFin[0] && info.InStockFin[0].FSettleTypeId && getString(info.InStockFin[0].FSettleTypeId.Name) }}</el-descriptions-item>
          <!-- 价目表 -->
          <el-descriptions-item label="价目表">{{ info.InStockFin && info.InStockFin[0] && info.InStockFin[0].PriceListId && getString(info.InStockFin[0].PriceListId.Name) }}</el-descriptions-item>
          <!-- 汇率类型 -->
          <el-descriptions-item label="汇率类型">{{ info.InStockFin && info.InStockFin[0] && info.InStockFin[0].ExchangeTypeId && getString(info.InStockFin[0].ExchangeTypeId.Name) }}</el-descriptions-item>
          <!-- 结算币别 -->
          <el-descriptions-item label="结算币别">{{ info.InStockFin && info.InStockFin[0] && info.InStockFin[0].SettleCurrId && getString(info.InStockFin[0].SettleCurrId.Name) }}</el-descriptions-item>
          <!-- 折扣表 -->
          <el-descriptions-item label="折扣表">{{ info.InStockFin && info.InStockFin[0] && info.InStockFin[0].DiscountListId && getString(info.InStockFin[0].DiscountListId.Name) }}</el-descriptions-item>
          <!-- 汇率 -->
          <el-descriptions-item label="汇率">{{ info.InStockFin && info.InStockFin[0] && info.InStockFin[0].ExchangeRate }}</el-descriptions-item>
          <!-- 付款条件 -->
          <el-descriptions-item label="付款条件">{{ info.InStockFin && info.InStockFin[0] && info.InStockFin[0].PayConditionId && getString(info.InStockFin[0].PayConditionId.Name) }}</el-descriptions-item>
          <!-- 金额 -->
          <el-descriptions-item label="金额">{{ (info.InStockFin && info.InStockFin[0] && info.InStockFin[0].BillAmount) || '' }}</el-descriptions-item>
          <!-- 金额(本位币) -->
          <el-descriptions-item label="金额(本位币)">{{ (info.InStockFin && info.InStockFin[0] && info.InStockFin[0].BillAmount_LC) || '' }}</el-descriptions-item>
          <!-- 货主类型(业务组织、供应商、客户) -->
          <el-descriptions-item label="货主类型">{{ info.OwnerTypeIdHead && getOptionLabel(OwnerTypeIdHeadOptions, info.OwnerTypeIdHead) }}</el-descriptions-item>
          <!-- 税额 -->
          <el-descriptions-item label="税额">{{ (info.InStockFin && info.InStockFin[0] && info.InStockFin[0].BillTaxAmount) || '' }}</el-descriptions-item>
          <!-- 税额(本位币) -->
          <el-descriptions-item label="税额(本位币)">{{ (info.InStockFin && info.InStockFin[0] && info.InStockFin[0].BillTaxAmount_LC) || '' }}</el-descriptions-item>
          <!-- 货主 -->
          <el-descriptions-item label="货主">{{ info.OwnerIdHead && getString(info.OwnerIdHead.Name) }}</el-descriptions-item>
          <!-- 价税合计 -->
          <el-descriptions-item label="价税合计">{{ (info.InStockFin && info.InStockFin[0] && info.InStockFin[0].BillAllAmount) || '' }}</el-descriptions-item>
          <!-- 价税合计(本位币) -->
          <el-descriptions-item label="价税合计(本位币)">{{ (info.InStockFin && info.InStockFin[0] && info.InStockFin[0].BillAllAmount_LC) || '' }}</el-descriptions-item>
          <!-- 含税 -->
          <el-descriptions-item label="含税">
            <el-checkbox v-model="isIncludedTax" disabled></el-checkbox>
          </el-descriptions-item>
          <!-- 价外税 -->
          <el-descriptions-item label="价外税">
            <el-checkbox v-model="isPriceExcludeTax" disabled></el-checkbox>
          </el-descriptions-item>
        </el-descriptions>
        <el-descriptions border :column="3" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 3 - 7em - 22px)' }" v-show="activeName === 'fourth'">
          <!-- 创建人 -->
          <el-descriptions-item label="创建人">{{ info.CreatorId && info.CreatorId.Name }}</el-descriptions-item>
          <!-- 审核人 -->
          <el-descriptions-item label="审核人">{{ info.ApproverId && info.ApproverId.Name }}</el-descriptions-item>
          <!-- 作废人 -->
          <el-descriptions-item label="作废人">{{ info.CancellerId && info.CancellerId.Name }}</el-descriptions-item>
          <!-- 创建日期 -->
          <el-descriptions-item label="创建日期">{{ info.CreateDate && parseTime(info.CreateDate, '{y}-{m}-{d}') }}</el-descriptions-item>
          <!-- 审核日期 -->
          <el-descriptions-item label="审核日期">{{ info.ApproveDate && parseTime(info.ApproveDate, '{y}-{m}-{d}') }}</el-descriptions-item>
          <!-- 作废日期 -->
          <el-descriptions-item label="作废日期">{{ info.CancelDate && parseTime(info.CancelDate, '{y}-{m}-{d}') }}</el-descriptions-item>
          <!-- 最后修改人 -->
          <el-descriptions-item label="最后修改人">{{ info.FModifierId && info.FModifierId.Name }}</el-descriptions-item>
          <!-- 确认人 -->
          <el-descriptions-item label="确认人">{{ info.ConfirmerId && info.ConfirmerId.Name }}</el-descriptions-item>
          <!-- 作废状态(未作废、已作废) -->
          <el-descriptions-item label="作废状态">{{ info.CancelStatus == 'A' ? '未作废' : '已作废' }}</el-descriptions-item>
          <!-- 最后修改日期 -->
          <el-descriptions-item label="最后修改日期">{{ info.FModifyDate && parseTime(info.FModifyDate, '{y}-{m}-{d}') }}</el-descriptions-item>
          <!-- 确认日期 -->
          <el-descriptions-item label="确认日期">{{ info.ConfirmDate && parseTime(info.ConfirmDate, '{y}-{m}-{d}') }}</el-descriptions-item>
          <!-- 确认状态(未确认、已确认) -->
          <el-descriptions-item label="确认状态">{{ info.ConfirmStatus == 'A' ? '未确认' : '已确认' }}</el-descriptions-item>
        </el-descriptions>
        <!-- 明细信息 -->
        <el-tabs v-model="detailActiveName" type="card" @tab-click="handleDetailClick" class="custom-tabs">
          <el-tab-pane label="明细信息" name="Dfirst"></el-tab-pane>
          <el-tab-pane label="物料数据" name="Dsecond"></el-tab-pane>
          <el-tab-pane label="明细财务信息" name="Dthird"></el-tab-pane>
          <el-tab-pane label="其他信息" name="Dfourth"></el-tab-pane>
        </el-tabs>
        <el-table ref="detailTable" :data="info.InStockEntry" class="custom-table" highlight-current-row @current-change="handleCurrentChange" :setCurrentRow="setCurrentRow" v-show="detailActiveName === 'Dfirst'">
          <!-- 序号 -->
          <el-table-column type="index" label="序号" align="center"></el-table-column>
          <!-- 入库类型 -->
          <el-table-column label="入库类型" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.WWInType }}</template>
          </el-table-column>
          <!-- 物料编码 -->
          <el-table-column label="物料编码" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.MaterialId && scope.row.MaterialId.Number }}</template>
          </el-table-column>
          <!-- 物料名称 -->
          <el-table-column label="物料名称" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.MaterialId && getString(scope.row.MaterialId.Name) }}</template>
          </el-table-column>
          <!-- 库存单位 -->
          <el-table-column label="库存单位" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.UnitID && getString(scope.row.UnitID.Name) }}</template>
          </el-table-column>
          <!-- 规格型号 -->
          <el-table-column label="规格型号" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.MaterialId && getString(scope.row.MaterialId.Specification) }}</template>
          </el-table-column>
          <!-- 物料说明 -->
          <el-table-column label="物料说明" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.MaterialDesc && getString(scope.row.MaterialDesc) }}</template>
          </el-table-column>
          <!-- 应收数量 -->
          <el-table-column label="应收数量" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.MustQty }}</template>
          </el-table-column>
          <!-- 实收数量 -->
          <el-table-column label="实收数量" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.RealQty }}</template>
          </el-table-column>
          <!-- 计价单位 -->
          <el-table-column label="计价单位" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.PriceUnitID && getString(scope.row.PriceUnitID.Name) }}</template>
          </el-table-column>
          <!-- 计价数量 -->
          <el-table-column label="计价数量" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.PriceUnitQty }}</template>
          </el-table-column>
          <!-- 批号 -->
          <el-table-column label="批号" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.Lot && scope.row.Lot.Number }}</template>
          </el-table-column>
          <!-- 仓库 -->
          <el-table-column label="仓库" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.StockId && getString(scope.row.StockId.Name) }}</template>
          </el-table-column>
          <!-- 仓位 -->
          <el-table-column label="仓位" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.StockLocId && getString(scope.row.StockLocId.Name) }}</template>
          </el-table-column>
          <!-- 库存状态 -->
          <el-table-column label="库存状态" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.StockStatusId && getString(scope.row.StockStatusId.Name) }}</template>
          </el-table-column>
          <!-- 是否赠品 -->
          <el-table-column label="是否赠品" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
              <el-checkbox v-model="scope.row.GiveAway" disabled></el-checkbox>
            </template>
          </el-table-column>
          <!-- 备注 -->
          <el-table-column label="备注" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.Note }}</template>
          </el-table-column>
          <!-- 辅助数量(kg) -->
          <el-table-column label="辅助数量(kg)" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.F_SCMJ_FZSLkg }}</template>
          </el-table-column>
        </el-table>
        <el-descriptions border :column="3" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 3 - 7em - 22px)' }" v-show="detailActiveName === 'Dsecond'">
          <el-descriptions-item label="物料编码">{{ setCurrentRow && setCurrentRow.MaterialId && setCurrentRow.MaterialId.Number }}</el-descriptions-item>
          <el-descriptions-item label="生产日期">{{ setCurrentRow && setCurrentRow.ProduceDate && parseTime(setCurrentRow.ProduceDate, '{y}-{m}-{d}') }}</el-descriptions-item>
          <el-descriptions-item label="辅单位">{{ setCurrentRow && setCurrentRow.ExtAuxUnitId && getString(setCurrentRow.ExtAuxUnitId.Name) }}</el-descriptions-item>
          <el-descriptions-item label="物料名称">{{ setCurrentRow && setCurrentRow.MaterialId && getString(setCurrentRow.MaterialId.Name) }}</el-descriptions-item>
          <el-descriptions-item label="保质期">{{ setCurrentRow && setCurrentRow.MaterialId && setCurrentRow.MaterialId.MaterialStock[0] && setCurrentRow.MaterialId.MaterialStock[0].ExpPeriod }}</el-descriptions-item>
          <el-descriptions-item label="实收数量(辅单位)">{{ (setCurrentRow && setCurrentRow.ExtAuxUnitQty) || '' }}</el-descriptions-item>
          <el-descriptions-item label="BOM版本">{{ setCurrentRow && setCurrentRow.BOMId && getString(setCurrentRow.BOMId.Name) }}</el-descriptions-item>
          <el-descriptions-item label="保质期单位">{{ setCurrentRow && setCurrentRow.MaterialId && setCurrentRow.MaterialId.MaterialStock[0] && setCurrentRow.MaterialId.MaterialStock[0].ExpUnit }}</el-descriptions-item>
          <el-descriptions-item label="备注">{{ setCurrentRow && setCurrentRow.Note }}</el-descriptions-item>
          <el-descriptions-item label="批号">{{ setCurrentRow && setCurrentRow.Lot && setCurrentRow.Lot.Number }}</el-descriptions-item>
          <el-descriptions-item label="有效期至" :span="2">{{ setCurrentRow && setCurrentRow.EXPIRYDATE && parseTime(setCurrentRow.EXPIRYDATE, '{y}-{m}-{d}') }}</el-descriptions-item>
          <el-descriptions-item label="供应商批号">{{ setCurrentRow && setCurrentRow.SupplierLot }}</el-descriptions-item>
          <el-descriptions-item label="在架寿命期">{{ setCurrentRow && setCurrentRow.MaterialId && setCurrentRow.MaterialId.MaterialStock[0] && setCurrentRow.MaterialId.MaterialStock[0].OnlineLife }}</el-descriptions-item>
        </el-descriptions>
        <el-descriptions border :column="3" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 3 - 7em - 22px)' }" v-show="detailActiveName === 'Dthird'">
          <el-descriptions-item label="物料编码">{{ setCurrentRow && setCurrentRow.MaterialId && setCurrentRow.MaterialId.Number }}</el-descriptions-item>
          <el-descriptions-item label="金额">{{ (setCurrentRow && setCurrentRow.Amount) || '' }}</el-descriptions-item>
          <el-descriptions-item label="价格系数">{{ (setCurrentRow && setCurrentRow.PriceCoefficient) || '' }}</el-descriptions-item>
          <el-descriptions-item label="物料名称">{{ setCurrentRow && setCurrentRow.MaterialId && getString(setCurrentRow.MaterialId.Name) }}</el-descriptions-item>
          <el-descriptions-item label="价税合计">{{ (setCurrentRow && setCurrentRow.AllAmount) || '' }}</el-descriptions-item>
          <el-descriptions-item label="计价单位">{{ setCurrentRow && setCurrentRow.PriceUnitID && getString(setCurrentRow.PriceUnitID.Name) }}</el-descriptions-item>
          <el-descriptions-item label="税率%">{{ (setCurrentRow && setCurrentRow.TaxRate) || '' }}</el-descriptions-item>
          <el-descriptions-item label="税额(本位币)">{{ (setCurrentRow && setCurrentRow.TaxAmount_LC) || '' }}</el-descriptions-item>
          <el-descriptions-item label="计价数量">{{ (setCurrentRow && setCurrentRow.PriceUnitQty) || '' }}</el-descriptions-item>
          <el-descriptions-item label="单价">{{ setCurrentRow && setCurrentRow.Price }}</el-descriptions-item>
          <el-descriptions-item label="金额(本位币)">{{ (setCurrentRow && setCurrentRow.Amount_LC) || '' }}</el-descriptions-item>
          <el-descriptions-item label="系统定价">{{ (setCurrentRow && setCurrentRow.SysPrice) || '' }}</el-descriptions-item>
          <el-descriptions-item label="含税单价">{{ (setCurrentRow && setCurrentRow.TaxPrice) || '' }}</el-descriptions-item>
          <el-descriptions-item label="价税合计(本位币)">{{ (setCurrentRow && setCurrentRow.AllAmount_LC) || '' }}</el-descriptions-item>
          <el-descriptions-item label="价格上限">{{ (setCurrentRow && setCurrentRow.UpPrice) || '' }}</el-descriptions-item>
          <el-descriptions-item label="折扣率%" :span="2">{{ (setCurrentRow && setCurrentRow.DiscountRate) || '' }}</el-descriptions-item>
          <el-descriptions-item label="价格下限">{{ (setCurrentRow && setCurrentRow.DownPrice) || '' }}</el-descriptions-item>
          <el-descriptions-item label="净价" :span="2">{{ (setCurrentRow && setCurrentRow.FTaxNetPrice) || '' }}</el-descriptions-item>
          <el-descriptions-item label="分录价目录">{{ setCurrentRow && setCurrentRow.PriceListEntry }}</el-descriptions-item>
          <el-descriptions-item label="单价折扣" :span="3">{{ (setCurrentRow && setCurrentRow.PriceDiscount) || '' }}</el-descriptions-item>
          <el-descriptions-item label="折扣额" :span="3">{{ (setCurrentRow && setCurrentRow.Discount) || '' }}</el-descriptions-item>
          <el-descriptions-item label="税额" :span="3">{{ (setCurrentRow && setCurrentRow.TaxAmount) || '' }}</el-descriptions-item>
        </el-descriptions>
        <el-descriptions border :column="3" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 3 - 7em - 22px)' }" v-show="detailActiveName === 'Dfourth'">
          <el-descriptions-item label="物料编码">{{ setCurrentRow && setCurrentRow.MaterialId && setCurrentRow.MaterialId.Number }}</el-descriptions-item>
          <el-descriptions-item label="源单类型">{{ setCurrentRow && setCurrentRow.SRCBILLTYPEID && getOptionLabel(SRCBILLTYPEIDOptions, setCurrentRow.SRCBILLTYPEID) }}</el-descriptions-item>
          <el-descriptions-item label="退料关联数量">{{ (setCurrentRow && setCurrentRow.ReturnJoinQty) || '' }}</el-descriptions-item>
          <el-descriptions-item label="物料名称">{{ setCurrentRow && setCurrentRow.MaterialId && getString(setCurrentRow.MaterialId.Name) }}</el-descriptions-item>
          <el-descriptions-item label="源单编号">{{ setCurrentRow && setCurrentRow.SRCBillNo }}</el-descriptions-item>
          <el-descriptions-item label="业务流程">{{ setCurrentRow && setCurrentRow.BFLowId && getString(setCurrentRow.BFLowId.Name) }}</el-descriptions-item>
          <el-descriptions-item label="需求跟踪号">{{ setCurrentRow && setCurrentRow.DemandNo }}</el-descriptions-item>
          <el-descriptions-item label="订单单号">{{ setCurrentRow && setCurrentRow.POOrderNo }}</el-descriptions-item>
          <el-descriptions-item label="样本破坏数量(计价单位)">{{ (setCurrentRow && setCurrentRow.FSampleDamageQty) || '' }}</el-descriptions-item>
          <el-descriptions-item label="计划跟踪号">{{ setCurrentRow && setCurrentRow.MtoNo }}</el-descriptions-item>
          <el-descriptions-item label="应付关闭状态">{{ setCurrentRow && setCurrentRow.PayableCloseStatus === 'A' ? '未关闭' : '已关闭' }}</el-descriptions-item>
          <el-descriptions-item label="应付关闭日期">{{ setCurrentRow && setCurrentRow.PayableCloseDate && parseTime(setCurrentRow.PayableCloseDate, '{y}-{m}-{d}') }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
    <!-- 下推弹出框 -->
    <el-dialog v-dialogDragBox title="选择单据" :visible.sync="pushOpen" width="580px" class="custom-dialog">
      <div style="padding: 0 20px">
        <el-form ref="pushForm" :model="pushForm" :rules="pushFormRules" label-width="80px">
          <el-form-item label="" label-width="0" prop="target">
            <el-radio-group v-model="pushForm.target" v-removeAriaHidden @change="handleTargetChange" style="width: 100%">
              <el-row :gutter="10" class="custom-push-target">
                <el-col :span="12" v-for="item in pushTarget" :key="item.value">
                  <el-radio :label="item.value">{{ item.label }}</el-radio>
                </el-col>
              </el-row>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="转换规则" prop="ruleId" v-if="isNeedRule()">
            <el-select v-model="pushForm.ruleId" placeholder="请选择转换规则" style="width: 100%" @change="calculateTargetBillType">
              <el-option v-for="item in calculateRule" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="单据类型" prop="targetBillTypeId" v-if="isNeedBillType()">
            <el-select v-model="pushForm.targetBillTypeId" placeholder="请选择单据类型" style="width: 100%">
              <el-option v-for="item in calculateBillType" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn small" @click="pushOpen = false">取消</el-button>
        <el-button class="custom-dialog-btn small primary" :class="{ disabled: !pushForm.target }" :disabled="!pushForm.target" @click="handlePushSubmit">确定</el-button>
      </div>
    </el-dialog>
    <!-- 应付单创建 -->
    <PayablesCreate ref="payablesCreate" @callBack="showPayablesCreate = false" v-if="showPayablesCreate"></PayablesCreate>
  </div>
</template>
<script>
import { getInStorageDetail, auditInStorage, revokeInStorage, deleteInStorage, pushDownInStorage, submitInStorage, unauditInStorage } from '@/api/kingdee/purchase/inStorage'
import { kingdee } from '@/minix'
import PayablesCreate from '@/views/kingdee/payables/create'

export default {
  mixins: [kingdee],
  components: { PayablesCreate },
  data() {
    return {
      open: false,
      info: {},
      setCurrentRow: {},
      activeName: 'first',
      detailActiveName: 'Dfirst',
      // 业务类型
      BusinessTypeOptions: [
        { label: '标准采购', value: 'CG' },
        { label: '标准委外', value: 'WW' },
        { label: '分销购销', value: 'DRPSALE' },
        { label: '资产采购', value: 'ZCCG' },
        { label: '费用采购', value: 'FYCG' },
        { label: 'VMI采购', value: 'VMICG' },
        { label: '直运采购', value: 'ZYCG' }
      ],
      // 货主类型
      OwnerTypeIdHeadOptions: [
        { label: '业务组织', value: 'BD_OwnerOrg' },
        { label: '供应商', value: 'BD_Supplier' },
        { label: '客户', value: 'BD_Customer' }
      ],
      // 源单类型
      SRCBILLTYPEIDOptions: [
        { label: '采购订单', value: 'PUR_PurchaseOrder' },
        { label: '采购入库单', value: 'STK_InStock' },
        { label: '渠道入库单', value: 'ESS_OrderSign' },
        { label: '收料通知单', value: 'PUR_ReceiveBill' },
        { label: '销售出库单', value: 'SAL_OUTSTOCK' }
      ],
      // 下推
      pushOpen: false,
      pushForm: {},
      pushFormRules: {
        target: [{ required: true, message: '请选择下推单据', trigger: ['blur', 'change'] }],
        ruleId: [{ required: true, message: '请选择转换规则', trigger: ['blur', 'change'] }],
        targetBillTypeId: [{ required: true, message: '请选择单据类型', trigger: ['blur', 'change'] }],
        targetOrgId: [{ required: true, message: '请选择目标组织', trigger: ['blur', 'change'] }]
      },
      // 下推目标
      pushTarget: [
        { value: 'PUR_MRB', label: '采购退料单' },
        { value: 'InStockPushRequest', label: '其他出库单' },
        { value: 'AP_Payable', label: '应付单' }
      ],
      // 转换规则
      ruleList: [
        // 下推应付单
        { value: 'AP_InStockToPayableMap', label: '采购入库单-应付单', target: 'AP_Payable' }
      ],
      // 单据类型
      targetBillTypeList: [
        // 下推应付单
        { value: 'a83c007f22414b399b0ee9b9aafc75f9', label: '标准应付单', target: 'AP_InStockToPayableMap' }
      ],
      // 应付单创建
      showPayablesCreate: false
    }
  },
  computed: {
    // 是否含税
    isIncludedTax() {
      return this.info?.InStockFin?.[0]?.IsIncludedTax || false
    },
    // 价外税
    isPriceExcludeTax() {
      return this.info?.InStockFin?.[0]?.ISPRICEEXCLUDETAX || false
    },
    // 计算规则
    calculateRule() {
      return this.ruleList.filter(item => item.target === this.pushForm.target)
    },
    // 计算单据类型
    calculateBillType() {
      const { ruleId } = this.pushForm
      const arr = this.targetBillTypeList.filter(item => item.target === ruleId) || []
      return arr
    }
  },
  methods: {
    // 切换tab
    handleClick(tab, event) {
      this.activeName = tab.name
    },
    handleDetailClick(tab, event) {
      this.detailActiveName = tab.name
    },
    // 获取详情
    getInfo(row = {}) {
      if (!row.BillNo) {
        this.$message.error('参数错误，请重新选择')
        return
      }
      getInStorageDetail({ billNo: row.BillNo }).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          const { result } = data
          this.activeName = 'first'
          this.detailActiveName = 'Dfirst'
          this.open = !!result?.result
          this.info = result?.result || {}
          this.$nextTick(() => {
            this.setCurrentRow = result?.result?.InStockEntry?.[0] || {}
            if (this.$refs.detailTable) {
              this.$refs.detailTable.setCurrentRow(this.setCurrentRow)
              this.$refs.detailTable.bodyWrapper.scrollLeft = 0
            }
          })
        } else this.$message.error(msg)
      })
    },
    // 当前行
    handleCurrentChange(val) {
      this.setCurrentRow = val
    },
    beforeClose() {
      this.handleClose()
      this.$emit('update', { billNo: this.info.BillNo, newStatus: this.info.DocumentStatus })
    },
    // 关闭
    handleClose(flag = false) {
      this.open = false
      this.$emit('callBack', flag)
    },
    // 金蝶操作
    // prettier-ignore
    handleKingdeeDo(type) {
      const number = this.info.BillNo
      if (!number) return
      switch (type) {
        case 'submit':
          // 提交
          this.$modal.confirm('确认要提交该采购入库单吗？').then(() => {
            submitInStorage({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('提交成功')
                this.$set(this.info, 'DocumentStatus', 'B')
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'audit':
          // 审核
          this.$modal.confirm('确认要审核该采购入库单吗？').then(() => {
            auditInStorage({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('审核成功')
                this.$set(this.info, 'DocumentStatus', 'C')
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'revoke':
          // 撤销
          this.$modal.confirm('确认要撤销该采购入库单吗？').then(() => {
            revokeInStorage({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('撤销成功')
                this.$set(this.info, 'DocumentStatus', 'D')
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'unAudit':
          // 反审核
          this.$modal.confirm('确认要反审核该采购入库单吗？').then(() => {
            unauditInStorage({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('反审核成功')
                this.$set(this.info, 'DocumentStatus', 'D')
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'delete':
          // 删除
          this.$modal.confirm('确认要删除该采购入库单吗？').then(() => {
            deleteInStorage({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('删除成功')
                this.handleClose(true)
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'push':
          // 下推
          this.initPushForm()
          break
      }
    },
    // 初始化下推表单
    initPushForm() {
      this.pushForm = {
        number: this.info.BillNo,
        target: undefined,
        ruleId: undefined,
        targetBillTypeId: undefined
      }
      this.$nextTick(() => {
        this.resetForm('pushForm')
      })
      this.pushOpen = true
    },
    // 切换下推单据
    handleTargetChange(val) {
      if (val === 'AP_Payable') {
        this.setupPayablesPush()
        return
      }
      this.clearPushFormFields()
    },
    // 设置应付单下推
    setupPayablesPush() {
      this.pushForm.ruleId = this.calculateRule?.[0]?.value || undefined
      this.pushForm.targetBillTypeId = undefined
      // 计算目标单据类型
      this.$nextTick(() => {
        this.calculateTargetBillType()
      })
    },
    // 清空下推表单字段
    clearPushFormFields() {
      this.pushForm.ruleId = undefined
      this.pushForm.targetBillTypeId = undefined
    },
    // 计算目标单据类型
    calculateTargetBillType() {
      const { ruleId } = this.pushForm
      const arr = this.targetBillTypeList.filter(item => item.target === ruleId) || []
      this.pushForm.targetBillTypeId = arr?.[0]?.value || undefined
    },
    // 判断是否需要转换规则
    isNeedRule() {
      const { target, ruleId } = this.pushForm
      if (target === 'AP_Payable') {
        return true
      }
      return false
    },
    // 判断是否需要单据类型
    isNeedBillType() {
      const { target, ruleId } = this.pushForm
      if (target === 'AP_Payable') {
        return true
      }
      return false
    },
    // 下推提交
    handlePushSubmit() {
      const { number } = this.pushForm
      if (!number) return
      this.$refs.pushForm.validate(valid => {
        if (valid) {
          this.executePushDown()
        }
      })
    },
    // 执行下推操作
    // prettier-ignore
    executePushDown() {
      pushDownInStorage(this.pushForm).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          this.handlePushSuccess(data)
        } else if (code === 400) {
          this.handlePushError(msg)
        } else {
          this.$message.error(msg)
        }
      }).catch(error => {
        console.error('下推操作失败:', error)
        this.$message.error('下推操作失败，请重试')
      })
    },
    // 处理下推成功
    handlePushSuccess(data) {
      if (this.pushForm.target === 'AP_Payable') {
        this.handlePayablesPushSuccess(data)
      } else {
        this.$message.success('下推成功')
        this.pushOpen = false
        this.handleClose(true)
      }
    },
    // 处理应付单下推成功
    handlePayablesPushSuccess(data) {
      this.pushOpen = false
      this.showPayablesCreate = true
      const { responseStatus } = data
      const fid = responseStatus?.successEntitys?.[0]?.id
      if (!fid) {
        this.$message.error('参数错误，请重试！')
        return
      }
      this.$nextTick(() => {
        this.$refs.payablesCreate.initPush(fid, 'push')
      })
    },
    // 处理下推错误
    handlePushError(msg) {
      this.$alert(msg || '操作失败，请重试或使用金蝶进行操作！', '提示', {
        type: 'info',
        confirmButtonText: '确定',
        callback: () => {
          this.pushOpen = false
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
@import '@/assets/styles/custom-new.scss';
::v-deep {
  .el-descriptions__header {
    margin-bottom: 0;
  }
  .el-table.custom-table .el-table__body-wrapper .el-table__row:hover td.el-table__cell {
    background-color: #eaf1fe !important;
  }
}
.customTitle {
  font-size: 16px;
  font-weight: normal;
  line-height: 60px;
  color: $font;
}
.kindeeButton {
  display: flex;
  align-items: center;
}
.custom-push-target {
  .el-col.el-col-12 {
    .el-radio {
      height: 30px;
      display: flex;
      align-items: center;
    }
  }
}
.custom-tabs {
  margin-top: 15px;
  ::v-deep {
    .el-tabs__header {
      .el-tabs__nav {
        border: 0;
      }
      .el-tabs__item {
        background-color: #eef2f8;
        border-radius: 5px 5px 0 0;
        border-left-width: 0;
        border-right-width: 0;
        &.is-active {
          background-color: $blue;
          color: $white;
        }
      }
      .el-tabs__item + .el-tabs__item {
        margin-left: 2px;
      }
    }
  }
}
</style>
