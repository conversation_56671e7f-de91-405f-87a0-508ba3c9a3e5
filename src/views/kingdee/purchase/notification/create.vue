<template>
  <div>
    <el-dialog v-dialogDragBox :title="title" :visible.sync="open" width="90%" class="custom-dialog" :before-close="beforeClose" append-to-body>
      <div style="padding: 0 20px">
        <el-form :model="form" :rules="rules" ref="form" label-width="6em" class="custom-form">
          <el-row :gutter="20" style="display: flex; flex-wrap: wrap">
            <!-- 单据类型 -->
            <el-col :span="6">
              <el-form-item label="单据类型" prop="fbilltypeid">
                <el-select v-model="form.fbilltypeid" filterable placeholder="请选择单据类型" style="width: 100%" @change="handleChangeBillType">
                  <el-option v-for="item in billTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 业务类型 -->
            <el-col :span="6">
              <el-form-item label="业务类型" prop="fbusinesstype">
                <el-select v-model="form.fbusinesstype" placeholder="请选择业务类型" disabled style="width: 100%">
                  <el-option v-for="item in bizTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 收料组织 -->
            <el-col :span="6">
              <el-form-item label="收料组织" prop="fstockorgid">
                <el-select v-model="form.fstockorgid" filterable placeholder="请选择收料组织" style="width: 100%" @change="handleChangeStockOrg">
                  <el-option v-for="item in ApplicationOrgId" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 需求组织 -->
            <el-col :span="6">
              <el-form-item label="需求组织" prop="fdemandorgid">
                <el-select v-model="form.fdemandorgid" filterable placeholder="请选择需求组织" style="width: 100%">
                  <el-option v-for="item in ApplicationOrgId" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 供应商 -->
            <el-col :span="6">
              <el-form-item label="供应商" prop="fsuppliername">
                <supplier-search-select :keyword.sync="form.fsuppliername" :useOrg="form.fstockorgid" :showLabel="false" isBack @callBack="handleSupplierSelect($event)" style="width: 100%" />
              </el-form-item>
            </el-col>
            <!-- 收料部门 -->
            <el-col :span="6">
              <el-form-item label="收料部门" prop="freceivedeptid">
                <el-select v-model="form.freceivedeptid" filterable placeholder="请选择收料部门" style="width: 100%" @change="handleChangeStockDept">
                  <el-option v-for="item in ApplicationDeptId.filter(ite => ite.FUseOrg == form.fstockorgid)" :key="item.FNumber" :label="item.FName" :value="item.FNumber"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 收料员 -->
            <el-col :span="6">
              <el-form-item label="收料员" prop="freceiverid">
                <el-select v-model="form.freceiverid" filterable placeholder="请选择收料员" style="width: 100%" :disabled="!form.fstockorgid || !form.freceivedeptid">
                  <el-option v-for="item in calculateStockerList" :key="item.Number" :label="item.Name" :value="item.Number">
                    <span style="float: left">{{ item.Name }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.FDept }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 收料日期 -->
            <el-col :span="6">
              <el-form-item label="收料日期" prop="fdate">
                <el-date-picker v-model="form.fdate" type="date" placeholder="请选择收料日期" style="width: 100%" />
              </el-form-item>
            </el-col>
            <el-col :span="24"></el-col>
            <!-- 采购组织 -->
            <el-col :span="6">
              <el-form-item label="采购组织" prop="fpurorgid">
                <el-select v-model="form.fpurorgid" filterable placeholder="请选择采购组织" style="width: 100%" @change="handleChangeSaleOrg">
                  <el-option v-for="item in ApplicationOrgId" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 采购部门 -->
            <el-col :span="6">
              <el-form-item label="采购部门" prop="fpurdeptid">
                <el-select v-model="form.fpurdeptid" filterable placeholder="请选择采购部门" style="width: 100%" @change="handleChangeDept">
                  <el-option v-for="item in ApplicationDeptId.filter(ite => ite.FUseOrg == form.fpurorgid)" :key="item.FNumber" :label="item.FName" :value="item.FNumber"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 采购员 -->
            <el-col :span="6">
              <el-form-item label="采购员" prop="fpurchaserid">
                <el-select v-model="form.fpurchaserid" filterable placeholder="请选择采购员" style="width: 100%" :disabled="!form.fpurorgid || !form.fpurdeptid">
                  <el-option v-for="item in calculateApplicantList" :key="item.Number" :label="item.Name" :value="item.Number">
                    <span style="float: left">{{ item.Name }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.FDept }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 结算组织 -->
            <el-col :span="6">
              <el-form-item label="结算组织" prop="fsettleorgid">
                <el-select v-model="form.fsettleorgid" filterable placeholder="请选择结算组织" style="width: 100%">
                  <el-option v-for="item in ApplicationOrgId" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 结算币别 -->
            <el-col :span="6">
              <el-form-item label="结算币别" prop="fsettlecurrid">
                <el-select v-model="form.fsettlecurrid" filterable placeholder="请选择结算币别" style="width: 100%">
                  <el-option v-for="item in CurrencyId" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 备注 -->
            <el-col :span="6">
              <el-form-item label="备注" prop="fnote">
                <el-input v-model="form.fnote" placeholder="请输入备注" style="width: 100%" />
              </el-form-item>
            </el-col>
            <!-- 明细信息 -->
            <el-col :span="24">
              <el-table :data="form.entities" style="width: 100%" stripe class="custom-table custom-table-cell0" show-summary :summary-method="getSummary">
                <!-- 序号 -->
                <el-table-column type="index" label="序号" width="50" align="center"></el-table-column>
                <!-- 物料编码 -->
                <el-table-column label="物料编码" align="center" show-overflow-tooltip>
                  <template slot-scope="scope">
                    <template v-if="isCreate">
                      <el-tooltip effect="dark" :content="scope.row.fmaterialid" :disabled="!scope.row.fmaterialid">
                        <el-form-item label-width="0" :prop="`entities.${scope.$index}.fmaterialid`" :rules="rules.fmaterialid">
                          <material-search-select ref="materialSearchSelect" :keyword.sync="scope.row.fmaterialid" :useOrg="form.fstockorgid" size="small" :showLabel="false" @callBack="handleMaterialSearchSelect($event, scope.row)" :options="[{ Number: scope.row.fmaterialid, Name: scope.row.fmaterialname, Specification: scope.row.fmaterialmodel }]" />
                        </el-form-item>
                      </el-tooltip>
                    </template>
                    <template v-else>
                      <span class="table-link" @click="handleMaterialNumber(scope.row.fmaterialid)">{{ scope.row.fmaterialid }}</span>
                    </template>
                  </template>
                </el-table-column>
                <!-- 物料名称 -->
                <el-table-column label="物料名称" align="center" show-overflow-tooltip>
                  <template slot-scope="{ row }">{{ row.fmaterialname }}</template>
                </el-table-column>
                <!-- 规格型号 -->
                <el-table-column label="规格型号" align="center" show-overflow-tooltip>
                  <template slot-scope="{ row }">{{ row.fmaterialmodel }}</template>
                </el-table-column>
                <!-- 收料单位 -->
                <el-table-column prop="funitid" label="收料单位" align="center" min-width="80">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.funitid`" :rules="rules.funitid">
                      <el-select v-model="scope.row.funitid" filterable placeholder="请选择收料单位" style="width: 100%" size="small">
                        <el-option v-for="item in UnitList" :key="item.FNumber" :label="item.FName" :value="item.FNumber"></el-option>
                      </el-select>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 交货数量 -->
                <el-table-column prop="factreceiveqty" label="交货数量" align="center" min-width="100">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.factreceiveqty`" :rules="rules.factreceiveqty">
                      <el-input v-model="scope.row.factreceiveqty" placeholder="请输入交货数量" style="width: 100%" size="small"></el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 预计到货日期 -->
                <el-table-column prop="fpredeliverydate" label="预计到货日期" align="center" min-width="120">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.fpredeliverydate`" :rules="rules.fpredeliverydate">
                      <el-date-picker v-model="scope.row.fpredeliverydate" type="date" placeholder="请选择预计到货日期" style="width: 100%" size="small"></el-date-picker>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 仓库 -->
                <el-table-column prop="fstockname" label="仓库" align="center" min-width="100">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.fstockname`" :rules="rules.fstockname">
                      <stock-search-select size="small" :useOrg="form.fstockorgid" :showLabel="false" :keyword.sync="scope.row.fstockname" style="width: 100%" @callBack="handleStockSelect($event, scope.row)" />
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 库存状态 -->
                <el-table-column prop="fstockstatusid" label="库存状态" align="center" min-width="100">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.fstockstatusid`" :rules="rules.fstockstatusid">
                      <el-select v-model="scope.row.fstockstatusid" filterable placeholder="请选择库存状态" style="width: 100%" size="small">
                        <el-option v-for="item in stockStatusOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                      </el-select>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 批号 -->
                <el-table-column prop="flot" label="批号" align="center" min-width="100">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.flot`" :rules="rules.flot">
                      <el-input v-model="scope.row.flot" placeholder="请输入批号" style="width: 100%" size="small"></el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 是否赠品 -->
                <el-table-column prop="fgiveaway" label="是否赠品" align="center" width="80">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.fgiveaway`" :rules="rules.fgiveaway">
                      <el-checkbox v-model="scope.row.fgiveaway" size="small"></el-checkbox>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 备注 -->
                <el-table-column prop="fdescription" label="备注" align="center" min-width="120">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.fdescription`" :rules="rules.fdescription">
                      <el-input v-model="scope.row.fdescription" placeholder="请输入备注" style="width: 100%" size="small"></el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 操作 -->
                <el-table-column label="操作" width="120" align="center" v-if="isCreate">
                  <template slot-scope="scope">
                    <el-button type="text" size="small" icon="el-icon-plus" @click="handleAdd">添加</el-button>
                    <el-button type="text" size="small" :disabled="form.entities.length == 1" icon="el-icon-delete" @click="handleDelete(scope.$index)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn" @click="handleClose()">取消</el-button>
        <el-button class="custom-dialog-btn primary" :loading="loading" @click="handleSubmit">确定</el-button>
      </div>
    </el-dialog>
    <!-- 收料通知单详情 -->
    <notification-detail ref="notificationDetail" />
  </div>
</template>
<script>
import { kingdee } from '@/minix'
import { isNumber, isNumberLength } from '@/utils/validate'
import { getNotificationDetail2, createNotification, getNotificationDetail, deleteNotificationV2 } from '@/api/kingdee/purchase/notification'
import StockSearchSelect from '@/components/SearchSelect/stock'
import SupplierSearchSelect from '@/components/SearchSelect/supplier'
import { getSalesmanList } from '@/api/kingdee'
import MaterialSearchSelect from '@/components/SearchSelect/material'
import NotificationDetail from '@/views/kingdee/purchase/notification/detail'

export default {
  name: 'PurchaseNotificationCreate',
  mixins: [kingdee],
  components: { StockSearchSelect, SupplierSearchSelect, MaterialSearchSelect, NotificationDetail },
  data() {
    return {
      fid: undefined,
      form: {},
      rules: {
        fbilltypeid: [{ required: true, message: '请选择单据类型', trigger: 'change' }],
        fstockorgid: [{ required: true, message: '请选择收料组织', trigger: 'change' }],
        fpurorgid: [{ required: true, message: '请选择采购组织', trigger: 'change' }],
        fdate: [{ required: true, message: '请选择收料日期', trigger: 'change' }],
        funitid: [{ required: true, message: '请选择收料单位', trigger: 'change' }],
        factreceiveqty: [
          { required: true, message: '请输入交货数量', trigger: 'change' },
          { validator: isNumber, message: '请输入正确的数量', trigger: ['change', 'blur'] },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: ['change', 'blur'] }
        ],
        fsettleorgid: [{ required: true, message: '请选择结算组织', trigger: 'change' }],
        fsettlecurrid: [{ required: true, message: '请选择结算币别', trigger: 'change' }]
      },
      title: '新增收料通知单',
      open: false,
      loading: false,
      // 单据类型
      billTypeOptions: [
        { value: 'SLD01_SYS', label: '标准收料单' },
        { value: 'SLD02_SYS', label: '零散采购收料单' },
        { value: 'SLD03_SYS', label: '委外收料单' },
        { value: 'SLD04_SYS', label: '资产接收单' },
        { value: 'SLD05_SYS', label: '费用物料接收单' },
        { value: 'SLD06_SYS', label: 'VMI收料单' },
        { value: 'SLD07_SYS', label: '现购收料单' },
        { value: 'SLD08_SYS', label: '分销购销收料单' }
      ],
      // 库存状态
      stockStatusOptions: [
        { value: 'KCZT01_SYS', label: '可用' },
        { value: 'KCZT02_SYS', label: '待检' },
        { value: 'KCZT03_SYS', label: '冻结' },
        { value: 'KCZT04_SYS', label: '退回冻结' },
        { value: 'KCZT07_SYS', label: '废品' },
        { value: 'KCZT08_SYS', label: '不良' },
        { value: 'KCZT09_SYS', label: '已包装' },
        { value: 'KCZT099_SYS', label: '外借' }
      ],
      // 业务类型
      bizTypeOptions: [
        { value: 'CG', label: '标准采购', target: 'SLD01_SYS' },
        { value: 'WW', label: '标准委外', target: 'SLD03_SYS' },
        { value: 'ZCCG', label: '资产采购', target: 'SLD04_SYS' },
        { value: 'FYCG', label: '费用采购', target: 'SLD05_SYS' },
        { value: 'VMICG', label: 'VMI采购', target: 'SLD06_SYS' },
        { value: 'DRPCG', label: '分销购销', target: 'SLD08_SYS' }
      ],
      // 采购员
      applicantList: [],
      // 收料员
      stockerList: [],
      isCreate: false,
      isPush: false,
      hasSuccessfully: false // 是否已成功提交
    }
  },
  computed: {
    // 采购员列表
    calculateApplicantList() {
      const dept = this.ApplicationDeptId.find(ite => ite.FNumber == this.form.fpurdeptid) || {}
      const deptName = dept.FName || ''
      const list = this.applicantList.filter(ite => ite.FDept == deptName)
      const processedList = list.map(item => ({
        ...item,
        Number: item.Number ? item.Number.split('_')[0] : item.Number
      }))
      return processedList
    },
    // 收料员列表
    calculateStockerList() {
      const dept = this.ApplicationDeptId.find(ite => ite.FNumber == this.form.freceivedeptid) || {}
      const deptName = dept.FName || ''
      const list = this.stockerList.filter(ite => ite.FDept == deptName)
      // 处理收料员编号，只取前三位数字
      const processedList = list.map(item => ({
        ...item,
        Number: item.Number ? item.Number.split('_')[0] : item.Number
      }))
      return processedList
    }
  },
  methods: {
    // 创建默认实体对象
    createDefaultEntity(options = {}) {
      return {
        fmaterialid: options?.fmaterialid || undefined, // 物料编码
        fmaterialname: (options?.fmaterialname && this.getString(options?.fmaterialname)) || undefined, // 物料名称
        fmaterialmodel: (options?.fmaterialmodel && this.getString(options?.fmaterialmodel)) || undefined, // 规格型号
        funitid: options?.funitid || undefined, // 收料单位
        factreceiveqty: options?.factreceiveqty || undefined, // 交货数量
        fpredeliverydate: options?.fpredeliverydate || undefined, // 预计到货日期
        fstockid: options?.fstockid || undefined, // 仓库
        fstockname: (options?.fstockname && this.getString(options?.fstockname)) || undefined, // 仓库名称
        fstockstatusid: options?.fstockstatusid || this.stockStatusOptions[0].value, // 库存状态
        flot: options?.flot || undefined, // 批号
        fgiveaway: options?.fgiveaway || undefined, // 是否赠品
        fdescription: options?.fdescription || undefined, // 备注
        fprice: options?.fprice || undefined, // 单价
        ftaxprice: options?.ftaxprice || undefined, // 含税单价
        fentrytaxrate: options?.fentrytaxrate || undefined // 税率%
      }
    },
    // 改变单据类型
    handleChangeBillType(val) {
      const obj = this.bizTypeOptions.find(ite => ite.target == val) || this.bizTypeOptions[0]
      this.form.fbusinesstype = obj.value
    },
    // 采购员远程方法
    ApplicantRemoteMethod() {
      const params = { bizOrg: this.form.fpurorgid, OperatorType: 'CGY' }
      getSalesmanList(params).then(res => {
        const { code, msg, data } = res
        const { data: applicantList } = data
        if (code == 200) {
          this.applicantList = applicantList
        } else this.$message.error(msg)
      })
    },
    // 收料员远程方法
    StockerRemoteMethod() {
      const params = { bizOrg: this.form.fstockorgid, OperatorType: 'WHY' }
      getSalesmanList(params).then(res => {
        const { code, msg, data } = res
        const { data: stockerList } = data
        if (code == 200) {
          this.stockerList = stockerList
        } else this.$message.error(msg)
      })
    },
    // 改变收料组织
    handleChangeStockOrg(val) {
      this.form.fdemandorgid = val
      this.form.fpurorgid = val
      this.form.fsettleorgid = val
      this.form.freceivedeptid = undefined // 清空收料部门
      this.form.freceiverid = undefined // 清空仓管员
      this.StockerRemoteMethod()
    },
    // 改变收料部门
    handleChangeStockDept() {
      this.form.freceiverid = undefined // 清空仓管员
    },
    // 改变采购组织
    handleChangeSaleOrg() {
      this.form.fpurdeptid = undefined // 清空采购部门
      this.form.fpurchaserid = undefined // 清空采购员
      this.ApplicantRemoteMethod()
    },
    // 改变采购部门
    handleChangeDept() {
      this.form.fpurchaserid = undefined // 清空采购员
    },
    // 供应商选择
    handleSupplierSelect(data) {
      this.$set(this.form, 'fsupplierid', data.FNumber)
      this.$set(this.form, 'fsuppliername', data.FName)
    },
    // 选择仓库
    handleStockSelect(stock, row) {
      this.$set(row, 'fstockid', stock.Number)
      this.$set(row, 'fstockname', stock.Name)
    },
    // 关闭
    beforeClose() {
      this.handleClose()
    },
    // 关闭
    async handleClose(flag = false) {
      if (this.fid && !this.isCreate && !this.hasSuccessfully) {
        try {
          await deleteNotificationV2({ id: this.fid })
          this.fid = undefined
          this.$nextTick(() => {
            this.open = false
            this.$emit('callBack', flag)
          })
        } catch (error) {
          this.$message.error(error.message)
        }
      } else {
        this.open = false
        this.$emit('callBack', flag)
      }
    },
    // 表单初始化
    reset() {
      this.form = {
        entities: [],
        fbilltypeid: undefined,
        fbusinesstype: undefined,
        fdate: undefined,
        fdemandorgid: undefined,
        fid: undefined,
        fnote: undefined,
        fpayconditionid: undefined,
        fpurchaserid: undefined,
        fpurdeptid: undefined,
        fpurorgid: undefined,
        freceivedeptid: undefined,
        freceiverid: undefined,
        fsettlecurrid: undefined,
        fsettlemodeid: undefined,
        fsettleorgid: undefined,
        fstockorgid: undefined,
        fsupplierid: undefined,
        traceDetails: []
      }
      this.resetForm('form')
      this.isCreate = false
      this.isPush = false
      this.hasSuccessfully = false
    },
    // 新增采购通知单
    handleCreate() {
      this.reset()
      this.form.fsettlecurrid = this.CurrencyId[0].value
      this.form.fdate = new Date()
      this.form.entities = [this.createDefaultEntity()]
      this.isCreate = true
      this.title = '新增收料通知单'
      this.open = true
    },
    // 编辑采购通知单
    // prettier-ignore
    handleUpdate(row) {
      const { BillNo } = row
      if (!BillNo) return this.$message.warning('参数错误，请刷新页面重试')
      const loading = this.$loading({
        lock: true,
        text: '加载中...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      this.reset()
      getNotificationDetail({ billNo: BillNo }).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          const info = data?.result?.result || {}
          const detailEntities = info?.PUR_ReceiveEntry || []
          const entities = detailEntities.map(item => {
            return {
              fentryid: item?.Id || undefined, // 使用时需检查
              fmaterialid: item?.MaterialID?.Number || undefined, // 物料编码
              fmaterialname: (item?.MaterialID?.Name && this.getString(item?.MaterialID?.Name)) || undefined, // 物料名称
              fmaterialmodel: (item?.MaterialID?.Specification && this.getString(item?.MaterialID?.Specification)) || undefined, // 规格型号
              funitid: item?.UnitId?.Number || undefined, // 收料单位
              factreceiveqty: item?.ActReceiveQty || undefined, // 交货数量
              fpredeliverydate: item?.PreDeliveryDate || undefined, // 预计到货日期
              fstockid: item?.StockID?.Number || undefined, // 仓库
              fstockname: (item?.StockID?.Name && this.getString(item?.StockID?.Name)) || undefined, // 仓库名称
              fstockstatusid: item?.StockStatusId?.Number || undefined, // 库存状态
              flot: item?.Lot?.Number || undefined, // 批号
              fgiveaway: item?.GiveAway || undefined, // 是否赠品
              fdescription: item?.Description || undefined, // 备注
              fprice: undefined, // 单价
              ftaxprice: undefined, // 含税单价
              fentrytaxrate: undefined // 税率%
            }
          })
          // 结算信息
          const settleInfo = info?.Receivefinance || []
          const fsettleorgid = settleInfo.length > 0 ? settleInfo[0]?.SettleOrgId?.Number : undefined // 结算组织
          const fsettlecurrid = settleInfo.length > 0 ? settleInfo[0]?.SettleCurrId?.Number : undefined // 结算币别
          this.form = {
            entities, // 明细信息
            fbilltypeid: info?.BillTypeId?.Number || undefined, // 单据类型
            fbusinesstype: info?.BusinessType || undefined, // 业务类型
            fdate: info?.Date || undefined, // 日期
            fdemandorgid: info?.DemandOrgId?.Number || undefined, // 需求组织
            fid: info?.Id || undefined, // id
            fnote: info?.Note || undefined, // 备注
            fpayconditionid: undefined, // 付款条件
            fpurchaserid: info?.PurchaserId?.Number || undefined, // 采购员
            fpurdeptid: info?.PurDeptId?.Number || undefined, // 采购部门
            fpurorgid: info?.PurOrgId?.Number || undefined, // 采购组织
            freceivedeptid: info?.ReceiveDeptId?.Number || undefined, // 收料部门
            freceiverid: info?.ReceiverId?.Number || undefined, // 收料员
            fsettlecurrid, // 结算币别
            fsettlemodeid: undefined, // 结算方式
            fsettleorgid, // 结算组织
            fstockorgid: info?.StockOrgId?.Number || undefined, // 收料组织
            fsupplierid: info?.SupplierId?.Number || undefined, // 供应商
            fsuppliername: (info?.SupplierId?.Name && this.getString(info?.SupplierId?.Name)) || undefined, // 供应商名称
            traceDetails: [] // 物流信息
          }
          this.fid = info?.Id || undefined
          this.title = '编辑收料通知单'
          this.$nextTick(() => {
            this.ApplicantRemoteMethod()
            this.StockerRemoteMethod()
          })
          this.isCreate = true
          this.open = true
        } else this.$message.error(msg)
      }).finally(() => {
        loading.close()
      })
    },
    // 添加明细
    handleAdd() {
      this.form.entities.push(this.createDefaultEntity())
    },
    // 删除明细
    handleDelete(index) {
      this.form.entities.splice(index, 1)
    },
    // 选择物料回调
    handleMaterialSearchSelect(event, row) {
      const obj = this.UnitList.find(item => item.FName == event.Unit)
      this.$set(row, 'fmaterialid', event.Number)
      this.$set(row, 'fmaterialname', event.Name)
      this.$set(row, 'fmaterialmodel', event.Specification)
      this.$set(row, 'funitid', obj?.FNumber || '')
      const hasEmpty = this.form.entities.filter(item => !item.fmaterialid)
      if (hasEmpty.length == 0) this.form.entities.push(this.createDefaultEntity())
    },
    // 初始化
    // prettier-ignore
    initPush(fid, type = undefined) {
      if (!fid) return this.$message.warning('参数错误，请刷新页面重试')
      const loading = this.$loading({
        lock: true,
        text: '加载中...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      this.reset()
      this.isPush = type == 'push'
      getNotificationDetail2({ fid }).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          const info = data?.result?.result || {}
          const detailEntities = info?.PUR_ReceiveEntry || []
          const entities = detailEntities.map(item => {
            return {
              fentryid: item?.Id || undefined, // 使用时需检查
              fmaterialid: item?.MaterialID?.Number || undefined, // 物料编码
              fmaterialname: (item?.MaterialID?.Name && this.getString(item?.MaterialID?.Name)) || undefined, // 物料名称
              fmaterialmodel: (item?.MaterialID?.Specification && this.getString(item?.MaterialID?.Specification)) || undefined, // 规格型号
              funitid: item?.UnitId?.Number || undefined, // 收料单位
              factreceiveqty: item?.ActReceiveQty || undefined, // 交货数量
              fpredeliverydate: item?.PreDeliveryDate || undefined, // 预计到货日期
              fstockid: item?.StockID?.Number || undefined, // 仓库
              fstockname: (item?.StockID?.Name && this.getString(item?.StockID?.Name)) || undefined, // 仓库名称
              fstockstatusid: item?.StockStatusId?.Number || undefined, // 库存状态
              flot: item?.Lot?.Number || undefined, // 批号
              fgiveaway: item?.GiveAway || undefined, // 是否赠品
              fdescription: item?.Description || undefined, // 备注
              fprice: undefined, // 单价
              ftaxprice: undefined, // 含税单价
              fentrytaxrate: undefined // 税率%
            }
          })
          // 结算信息
          const settleInfo = info?.Receivefinance || []
          const fsettleorgid = settleInfo.length > 0 ? settleInfo[0]?.SettleOrgId?.Number : undefined // 结算组织
          const fsettlecurrid = settleInfo.length > 0 ? settleInfo[0]?.SettleCurrId?.Number : undefined // 结算币别
          this.form = {
            entities, // 明细信息
            fbilltypeid: info?.BillTypeId?.Number || undefined, // 单据类型
            fbusinesstype: info?.BusinessType || undefined, // 业务类型
            fdate: info?.Date || undefined, // 日期
            fdemandorgid: info?.DemandOrgId?.Number || undefined, // 需求组织
            fid, // id
            fnote: info?.Note || undefined, // 备注
            fpayconditionid: undefined, // 付款条件
            fpurchaserid: info?.PurchaserId?.Number || undefined, // 采购员
            fpurdeptid: info?.PurDeptId?.Number || undefined, // 采购部门
            fpurorgid: info?.PurOrgId?.Number || undefined, // 采购组织
            freceivedeptid: info?.ReceiveDeptId?.Number || undefined, // 收料部门
            freceiverid: info?.ReceiverId?.Number || undefined, // 收料员
            fsettlecurrid, // 结算币别
            fsettlemodeid: undefined, // 结算方式
            fsettleorgid, // 结算组织
            fstockorgid: info?.StockOrgId?.Number || undefined, // 收料组织
            fsupplierid: info?.SupplierId?.Number || undefined, // 供应商
            fsuppliername: (info?.SupplierId?.Name && this.getString(info?.SupplierId?.Name)) || undefined, // 供应商名称
            traceDetails: [] // 物流信息
          }
          this.fid = fid
          this.title = '新增收料通知单'
          this.$nextTick(() => {
            this.ApplicantRemoteMethod()
            this.StockerRemoteMethod()
          })
          this.open = true
        } else this.$message.error(msg)
      }).finally(() => {
        loading.close()
      })
    },
    // 获取合计
    getSummary(param) {
      const { columns, data } = param
      const result = columns.map((column, index) => {
        if (index === 0) {
          return '合计'
        }
        if (column.property === 'factreceiveqty') {
          return `${this.calculateTotal(data, column.property)}`
        }
        return ''
      })
      return result
    },
    // 计算合计
    calculateTotal(data, key) {
      return data.reduce((total, item) => {
        if (key === 'factreceiveqty') {
          const value = parseFloat(item.factreceiveqty) || 0
          return parseFloat((total + value).toFixed(5))
        }
        return total
      }, 0)
    },
    // 提交
    // prettier-ignore
    handleSubmit() {
      if (this.form.entities.length > 1) this.form.entities = this.form.entities.filter(item => item.fmaterialid)
      this.$nextTick(() => {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.loading = true
            createNotification(this.form).then(res => {
              const { code, msg } = res
              if (code == 200) {
                this.$message.success('操作成功')
                this.hasSuccessfully = true
                if (this.isPush) {
                  const { number } = res.data
                  if (number) {
                    this.open = false
                    this.$nextTick(() => {
                      this.$refs.notificationDetail.getInfo({ BillNo: number })
                    })
                  } else {
                    this.open = false
                    this.$emit('callBack', true)
                  }
                } else {
                  this.open = false
                  this.$emit('callBack', true)
                }
              } else this.$message.error(msg)
            }).finally(() => {
              this.loading = false
            })
          }
        })
      })
    },
    // 物料详情
    handleMaterialNumber(number) {
      if (number) this.$parent.handleMaterialNumber(number)
    }
  }
}
</script>
<style scoped lang="scss">
@import '@/assets/styles/custom-new.scss';
::v-deep {
  .custom-table {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    border-bottom: 0;
    margin-bottom: 20px;
    .el-form-item {
      margin-top: 10px !important;
      margin-bottom: 10px !important;
      .el-form-item__error {
        top: 95%;
        padding-top: 0;
      }
    }
    .el-input--prefix .el-input__inner {
      padding-left: 20px;
      padding-right: 20px;
    }
    .el-input--prefix.el-date-editor--date .el-input__inner {
      padding-left: 30px;
    }
    .el-date-editor--datetime .el-input__inner {
      padding-left: 30px;
      padding-right: 30px;
    }
  }
}
</style>
