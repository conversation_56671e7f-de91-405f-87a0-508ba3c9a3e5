<template>
  <div>
    <el-dialog v-dialogDragBox title="收料通知单详情" :visible.sync="open" width="90%" class="custom-dialog" :before-close="beforeClose">
      <div style="padding: 0 20px">
        <div class="kindeeButton" :key="info.DocumentStatus">
          <el-button type="primary" size="medium" v-if="info.DocumentStatus == 'A' || info.DocumentStatus == 'D'" @click="handleKingdeeDo('submit')">提交</el-button>
          <el-button type="primary" size="medium" disabled v-else>提交</el-button>
          <el-button type="warning" size="medium" v-if="info.DocumentStatus == 'B'" @click="handleKingdeeDo('audit')">审核</el-button>
          <el-button type="warning" size="medium" disabled v-else>审核</el-button>
          <el-button type="info" size="medium" v-if="info.DocumentStatus == 'B'" @click="handleKingdeeDo('revoke')">撤销</el-button>
          <el-button type="info" size="medium" disabled v-else>撤销</el-button>
          <el-button type="danger" size="medium" v-if="info.DocumentStatus == 'A' || info.DocumentStatus == 'D'" @click="handleKingdeeDo('delete')">删除</el-button>
          <el-button type="danger" size="medium" disabled v-else>删除</el-button>
          <el-button type="warning" size="medium" v-if="info.DocumentStatus == 'C'" @click="handleKingdeeDo('unAudit')">反审</el-button>
          <el-button type="warning" size="medium" disabled v-else>反审</el-button>
          <el-button type="success" size="medium" v-if="info.DocumentStatus == 'C'" @click="handleKingdeeDo('push')">下推</el-button>
          <el-button type="success" size="medium" disabled v-else>下推</el-button>
        </div>
        <el-descriptions border :column="4" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 4 - 7em - 22px)' }">
          <template slot="title">
            <div class="customTitle">基本信息</div>
          </template>
          <!-- 订单销售组织 -->
          <el-descriptions-item label="订单销售组织">{{ info.F_SCMJ_DDXSZZ && getString(info.F_SCMJ_DDXSZZ.Name) }}</el-descriptions-item>
          <!-- 单据类型 -->
          <el-descriptions-item label="单据类型">{{ info.BillTypeId && getString(info.BillTypeId.Name) }}</el-descriptions-item>
          <!-- 需求组织 -->
          <el-descriptions-item label="需求组织">{{ info.DemandOrgId && getString(info.DemandOrgId.Name) }}</el-descriptions-item>
          <!-- 收料组织 -->
          <el-descriptions-item label="收料组织">{{ info.StockOrgId && getString(info.StockOrgId.Name) }}</el-descriptions-item>
          <!-- 收料部门 -->
          <el-descriptions-item label="收料部门">{{ info.ReceiveDeptId && getString(info.ReceiveDeptId.Name) }}</el-descriptions-item>
          <!-- 订单销售部门 -->
          <el-descriptions-item label="订单销售部门">{{ info.F_SCMJ_DDXSBM && getString(info.F_SCMJ_DDXSBM.Name) }}</el-descriptions-item>
          <!-- 供应商 -->
          <el-descriptions-item label="供应商">{{ info.SupplierId && getString(info.SupplierId.Name) }}</el-descriptions-item>
          <!-- 业务类型 -->
          <el-descriptions-item label="业务类型">{{ info.BusinessType && getOptionLabel(BusinessTypeOptions, info.BusinessType) }}</el-descriptions-item>
          <!-- 收料员 -->
          <el-descriptions-item label="收料员">{{ info.ReceiverId && getString(info.ReceiverId.Name) }}</el-descriptions-item>
          <!-- 采购组织 -->
          <el-descriptions-item label="采购组织">{{ info.PurOrgId && getString(info.PurOrgId.Name) }}</el-descriptions-item>
          <!-- 订单销售员 -->
          <el-descriptions-item label="订单销售员">{{ info.F_SCMJ_DDXSY && getString(info.F_SCMJ_DDXSY.Name) }}</el-descriptions-item>
          <!-- 单据编号 -->
          <el-descriptions-item label="单据编号">{{ info.BillNo }}</el-descriptions-item>
          <!-- 备注 -->
          <el-descriptions-item label="备注">{{ info.Note }}</el-descriptions-item>
          <!-- 采购部门 -->
          <el-descriptions-item label="采购部门">{{ info.PurDeptId && getString(info.PurDeptId.Name) }}</el-descriptions-item>
          <!-- 销售订单号 -->
          <el-descriptions-item label="销售订单号">{{ info.F_SCMJ_XSDDH }}</el-descriptions-item>
          <!-- 收料日期 -->
          <el-descriptions-item label="收料日期">{{ info.Date && parseTime(info.Date, '{y}-{m}-{d}') }}</el-descriptions-item>
          <!-- 采购员 -->
          <el-descriptions-item label="采购员">{{ info.PurchaserId && getString(info.PurchaserId.Name) }}</el-descriptions-item>
          <!-- 单据状态 -->
          <el-descriptions-item label="单据状态">{{ info.DocumentStatus && getDocumentStatusLabel(info.DocumentStatus) }}</el-descriptions-item>
        </el-descriptions>
        <el-descriptions border :column="3" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 3 - 7em - 22px)' }">
          <template slot="title">
            <div class="customTitle">供应商信息</div>
          </template>
          <!-- 供货方 -->
          <el-descriptions-item label="供货方">{{ info.SupplyId && getString(info.SupplyId.Name) }}</el-descriptions-item>
          <!-- 供货方联系人 -->
          <el-descriptions-item label="供货方联系人">{{ info.ProviderContactId && getString(info.ProviderContactId.Name) }}</el-descriptions-item>
          <!-- 供货方地址 -->
          <el-descriptions-item label="供货方地址">{{ info.SupplyAddress }}</el-descriptions-item>
          <!-- 结算方 -->
          <el-descriptions-item label="结算方">{{ info.SettleId && getString(info.SettleId.Name) }}</el-descriptions-item>
          <!-- 收款方 -->
          <el-descriptions-item label="收款方">{{ info.ChargeId && getString(info.ChargeId.Name) }}</el-descriptions-item>
          <!-- 邮箱 -->
          <el-descriptions-item label="邮箱">{{ info.FSupplyEMail }}</el-descriptions-item>
        </el-descriptions>
        <div class="customTitle">明细信息</div>
        <el-table ref="detailTable" :data="info.PUR_ReceiveEntry" class="custom-table" highlight-current-row>
          <!-- 序号 -->
          <el-table-column type="index" label="序号" align="center"></el-table-column>
          <!-- 物料编码 -->
          <el-table-column label="物料编码" align="center">
            <template slot-scope="scope">{{ scope.row.MaterialID && scope.row.MaterialID.Number }}</template>
          </el-table-column>
          <!-- 物料名称 -->
          <el-table-column label="物料名称" align="center">
            <template slot-scope="scope">{{ scope.row.MaterialID && getString(scope.row.MaterialID.Name) }}</template>
          </el-table-column>
          <!-- 规格型号 -->
          <el-table-column prop="Specification" label="规格型号" align="center">
            <template slot-scope="scope">{{ scope.row.MaterialID && scope.row.MaterialID.Specification && getString(scope.row.MaterialID.Specification) }}</template>
          </el-table-column>
          <!-- 收料单位 -->
          <el-table-column label="收料单位" align="center">
            <template slot-scope="scope">{{ scope.row.UnitId && getString(scope.row.UnitId.Name) }}</template>
          </el-table-column>
          <!-- 交货数量 -->
          <el-table-column label="交货数量" align="center">
            <template slot-scope="scope">{{ scope.row.ActReceiveQty }}</template>
          </el-table-column>
          <!-- 预计到货日期 -->
          <el-table-column label="预计到货日期" align="center">
            <template slot-scope="scope">{{ scope.row.PreDeliveryDate && parseTime(scope.row.PreDeliveryDate, '{y}-{m}-{d}') }}</template>
          </el-table-column>
          <!-- 仓库 -->
          <el-table-column label="仓库" align="center">
            <template slot-scope="scope">{{ scope.row.StockID && getString(scope.row.StockID.Name) }}</template>
          </el-table-column>
          <!-- 仓位 -->
          <el-table-column label="仓位" align="center">
            <template slot-scope="scope">{{ scope.row.StockLocId && getString(scope.row.StockLocId.Name) }}</template>
          </el-table-column>
          <!-- 库存状态 -->
          <el-table-column label="库存状态" align="center">
            <template slot-scope="scope">{{ scope.row.StockStatusId && getString(scope.row.StockStatusId.Name) }}</template>
          </el-table-column>
          <!-- 批号 -->
          <el-table-column label="批号" align="center">
            <template slot-scope="scope">{{ scope.row.Lot && scope.row.Lot.Number }}</template>
          </el-table-column>
          <!-- 是否赠品 -->
          <el-table-column label="是否赠品" align="center">
            <template slot-scope="scope">
              <el-checkbox :value="scope.row.GiveAway" disabled></el-checkbox>
            </template>
          </el-table-column>
          <!-- 备注 -->
          <el-table-column label="备注" align="center">
            <template slot-scope="scope">{{ scope.row.Description }}</template>
          </el-table-column>
          <!-- 行状态 -->
          <el-table-column label="行状态" align="center">
            <template slot-scope="scope">{{ scope.row.ENTRYSTATUS === 'A' ? '正常' : '已关闭' }}</template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
    <!-- 下推弹出框 -->
    <el-dialog v-dialogDragBox title="选择单据" :visible.sync="pushOpen" width="580px" class="custom-dialog">
      <div style="padding: 0 20px">
        <el-form ref="pushForm" :model="pushForm" :rules="pushFormRules" label-width="80px">
          <el-form-item label="" label-width="0" prop="target">
            <el-radio-group v-model="pushForm.target" v-removeAriaHidden @change="handleTargetChange" style="width: 100%">
              <el-row :gutter="10" class="custom-push-target">
                <el-col :span="12" v-for="item in pushTarget" :key="item.value">
                  <el-radio :label="item.value">{{ item.label }}</el-radio>
                </el-col>
              </el-row>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="转换规则" prop="ruleId" v-if="isNeedRule()">
            <el-select v-model="pushForm.ruleId" placeholder="请选择转换规则" style="width: 100%" @change="calculateTargetBillType">
              <el-option v-for="item in calculateRule" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="单据类型" prop="targetBillTypeId" v-if="isNeedBillType()">
            <el-select v-model="pushForm.targetBillTypeId" placeholder="请选择单据类型" style="width: 100%">
              <el-option v-for="item in calculateBillType" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn small" @click="pushOpen = false">取消</el-button>
        <el-button class="custom-dialog-btn small primary" :class="{ disabled: !pushForm.target }" :disabled="!pushForm.target" @click="handlePushSubmit">确定</el-button>
      </div>
    </el-dialog>
    <!-- 新增采购入库单 -->
    <add-in-storage ref="addInStorage" @callBack="showAddInStorage = false" v-if="showAddInStorage" />
  </div>
</template>
<script>
import { getNotificationDetail, auditNotification, revokeNotification, deleteNotification, pushDownNotification, submitNotification, unauditNotification } from '@/api/kingdee/purchase/notification'
import { kingdee } from '@/minix'
import AddInStorage from '@/views/kingdee/purchase/inStorage/create'

export default {
  mixins: [kingdee],
  components: { AddInStorage },
  data() {
    return {
      open: false,
      info: {},
      setCurrentRow: {},
      // 业务类型
      BusinessTypeOptions: [
        { label: '标准采购', value: 'CG' },
        { label: '标准委外', value: 'WW' },
        { label: '资产采购', value: 'ZCCG' },
        { label: '费用采购', value: 'FYCG' },
        { label: 'VMI采购', value: 'VMICG' },
        { label: '分销采购', value: 'DRPCG' }
      ],
      // 下推
      pushOpen: false,
      pushTarget: [
        { value: 'STK_InStock', label: '采购入库单' },
        { value: 'PUR_MRB', label: '采购退料单' }
      ],
      pushForm: {},
      pushFormRules: {
        target: [{ required: true, message: '请选择下推单据', trigger: ['blur', 'change'] }],
        ruleId: [{ required: true, message: '请选择转换规则', trigger: ['blur', 'change'] }],
        targetBillTypeId: [{ required: true, message: '请选择单据类型', trigger: ['blur', 'change'] }],
        targetOrgId: [{ required: true, message: '请选择目标组织', trigger: ['blur', 'change'] }]
      },
      // 转换规则
      ruleList: [
        // 下推采购入库单
        { value: 'PUR_ReceiveBill-STK_InStock', label: '采购收料单至采购入库单', target: 'STK_InStock' }
      ],
      // 单据类型
      targetBillTypeList: [
        // 下推采购入库单
        { value: '0a2c1694596d440882adb080a7a8ca1b', label: '委外入库单', target: 'PUR_ReceiveBill-STK_InStock', billType: 'SLD03_SYS' },
        { value: '5b91410d323043f3b4f3a7079aad3c68', label: '零散采购入库单', target: 'PUR_ReceiveBill-STK_InStock', billType: 'SLD02_SYS' },
        { value: 'd8d3858138f0810811e2fb1db393697f', label: '资产入库单', target: 'PUR_ReceiveBill-STK_InStock', billType: 'SLD04_SYS' },
        { value: 'a1ff32276cd9469dad3bf2494366fa4f', label: '标准采购入库', target: 'PUR_ReceiveBill-STK_InStock', billType: 'SLD01_SYS' },
        { value: 'd8d3858138f0810811e2fb49f168b6d4', label: '费用入库单', target: 'PUR_ReceiveBill-STK_InStock', billType: 'SLD05_SYS' },
        { value: '503b2f425825455ea0a9c545a8045b01', label: '分销购销入库单', target: 'PUR_ReceiveBill-STK_InStock', billType: 'SLD08_SYS' },
        { value: '0023240234df807511e3089bc912a28a', label: 'VMI入库单', target: 'PUR_ReceiveBill-STK_InStock', billType: 'SLD06_SYS' },
        { value: 'a1ff32276cd9469dad3bf2494366fa4f', label: '标准采购入库', target: 'PUR_ReceiveBill-STK_InStock', billType: 'SLD06_SYS' },
        { value: '5abdab3ba595b6', label: '现购入库单', target: 'PUR_ReceiveBill-STK_InStock', billType: 'SLD07_SYS' }
      ],
      // 新增采购入库单
      showAddInStorage: false
    }
  },
  computed: {
    // 计算规则
    calculateRule() {
      return this.ruleList.filter(item => item.target === this.pushForm.target)
    },
    // 计算单据类型
    calculateBillType() {
      const { ruleId } = this.pushForm
      const billType = this.info?.BillTypeId?.Number || ''
      const arr = this.targetBillTypeList.filter(item => item.target === ruleId) || []
      const newArr = arr.filter(item => item.billType === billType) || []
      return newArr
    }
  },
  methods: {
    // 获取详情
    getInfo(row = {}) {
      this.open = true
      if (!row.BillNo) {
        this.$message.error('参数错误，请重新选择')
        return
      }
      getNotificationDetail({ billNo: row.BillNo }).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          const { result } = data
          this.open = !!result?.result
          this.info = result?.result || {}
          this.$nextTick(() => {
            this.setCurrentRow = result?.result?.PUR_ReceiveEntry?.[0] || {}
            if (this.$refs.detailTable) {
              this.$refs.detailTable.setCurrentRow(this.setCurrentRow)
              this.$refs.detailTable.bodyWrapper.scrollLeft = 0
            }
          })
        } else this.$message.error(msg)
      })
    },
    beforeClose() {
      this.handleClose()
      this.$emit('update', { billNo: this.info.BillNo, newStatus: this.info.DocumentStatus })
    },
    // 关闭
    handleClose(flag = false) {
      this.open = false
      this.$emit('callBack', flag)
    },
    // 金蝶操作
    // prettier-ignore
    handleKingdeeDo(type) {
      const number = this.info.BillNo
      if (!number) return
      switch (type) {
        case 'submit':
          // 提交
          this.$modal.confirm('确认要提交该收料通知单吗？').then(() => {
            submitNotification({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('提交成功')
                this.$set(this.info, 'DocumentStatus', 'B')
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'audit':
          // 审核
          this.$modal.confirm('确认要审核该收料通知单吗？').then(() => {
            auditNotification({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('审核成功')
                this.$set(this.info, 'DocumentStatus', 'C')
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'revoke':
          // 撤销
          this.$modal.confirm('确认要撤销该收料通知单吗？').then(() => {
            revokeNotification({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('撤销成功')
                this.$set(this.info, 'DocumentStatus', 'D')
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'unAudit':
          // 反审核
          this.$modal.confirm('确认要反审核该收料通知单吗？').then(() => {
            unauditNotification({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('反审核成功')
                this.$set(this.info, 'DocumentStatus', 'D')
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'delete':
          // 删除
          this.$modal.confirm('确认要删除该收料通知单吗？').then(() => {
            deleteNotification({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('删除成功')
                this.handleClose(true)
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'push':
          // 下推
          this.initPushForm()
          break
      }
    },
    // 初始化下推表单
    initPushForm() {
      this.pushForm = {
        number: this.info.BillNo,
        target: undefined,
        ruleId: undefined,
        targetBillTypeId: undefined
      }
      this.$nextTick(() => {
        this.resetForm('pushForm')
      })
      this.pushOpen = true
    },
    // 切换下推单据
    handleTargetChange(val) {
      if (val === 'STK_InStock') {
        this.setupPurchaseReturnPush()
        return
      }
      this.clearPushFormFields()
    },
    // 设置采购入库单下推
    setupPurchaseReturnPush() {
      this.pushForm.ruleId = this.calculateRule?.[0]?.value || undefined
      this.pushForm.targetBillTypeId = undefined
      // 计算目标单据类型
      this.$nextTick(() => {
        this.calculateTargetBillType()
      })
    },
    // 清空下推表单字段
    clearPushFormFields() {
      this.pushForm.ruleId = undefined
      this.pushForm.targetBillTypeId = undefined
    },
    // 计算目标单据类型
    calculateTargetBillType() {
      const { ruleId } = this.pushForm
      const billType = this.info?.BillTypeId?.Number || ''
      const arr = this.targetBillTypeList.filter(item => item.target === ruleId) || []
      const newArr = arr.filter(item => item.billType === billType) || []
      this.pushForm.targetBillTypeId = newArr?.[0]?.value || undefined
    },
    // 判断是否需要转换规则
    isNeedRule() {
      const { target, ruleId } = this.pushForm
      if (target === 'STK_InStock') {
        return true
      }
      return false
    },
    // 判断是否需要单据类型
    isNeedBillType() {
      const { target, ruleId } = this.pushForm
      if (target === 'STK_InStock') {
        return true
      }
      return false
    },
    // 下推提交
    handlePushSubmit() {
      const { number } = this.pushForm
      if (!number) return
      this.$refs.pushForm.validate(valid => {
        if (valid) {
          this.executePushDown()
        }
      })
    },
    // 执行下推操作
    // prettier-ignore
    executePushDown() {
      pushDownNotification(this.pushForm).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          this.handlePushSuccess(data)
        } else if (code === 400) {
          this.handlePushError(msg)
        } else {
          this.$message.error(msg)
        }
      }).catch(error => {
        console.error('下推操作失败:', error)
        this.$message.error('下推操作失败，请重试')
      })
    },
    // 处理下推成功
    handlePushSuccess(data) {
      if (this.pushForm.target === 'STK_InStock') {
        this.handlePurchaseReturnPushSuccess(data)
      } else {
        this.$message.success('下推成功')
        this.pushOpen = false
        this.handleClose(true)
      }
    },
    // 处理采购入库单下推成功
    handlePurchaseReturnPushSuccess(data) {
      this.pushOpen = false
      this.showAddInStorage = true
      const { responseStatus } = data
      const fid = responseStatus?.successEntitys?.[0]?.id
      if (!fid) {
        this.$message.error('参数错误，请重试！')
        return
      }
      this.$nextTick(() => {
        this.$refs.addInStorage.initPush(fid, 'push')
      })
    },
    // 处理下推错误
    handlePushError(msg) {
      this.$alert(msg || '操作失败，请重试或使用金蝶进行操作！', '提示', {
        type: 'info',
        confirmButtonText: '确定',
        callback: () => {
          this.pushOpen = false
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
@import '@/assets/styles/custom-new.scss';
::v-deep {
  .el-descriptions__header {
    margin-bottom: 0;
  }
  .el-table.custom-table .el-table__body-wrapper .el-table__row:hover td.el-table__cell {
    background-color: #eaf1fe !important;
  }
}
.customTitle {
  font-size: 16px;
  font-weight: normal;
  line-height: 60px;
  color: $font;
}
.kindeeButton {
  display: flex;
  align-items: center;
}
.custom-push-target {
  .el-col.el-col-12 {
    .el-radio {
      height: 30px;
      display: flex;
      align-items: center;
    }
  }
}
</style>
