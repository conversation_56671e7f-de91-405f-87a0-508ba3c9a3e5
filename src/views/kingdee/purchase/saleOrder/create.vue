<template>
  <div>
    <el-dialog v-dialogDragBox :title="title" :visible.sync="open" width="90%" class="custom-dialog" @close="handleCancel()">
      <div style="padding: 0 20px">
        <el-form ref="form" :model="form" :rules="rules" label-width="6em" class="custom-form">
          <el-row :gutter="10">
            <!-- 日期 -->
            <el-col :span="8">
              <el-form-item label="日期" prop="fdate">
                <el-date-picker v-model="form.fdate" type="date" placeholder="请选择日期" style="width: 100%" value-format="yyyy-MM-dd"></el-date-picker>
              </el-form-item>
            </el-col>
            <!-- 单据类型 -->
            <el-col :span="8">
              <el-form-item label="单据类型" prop="fbillTypeID">
                <el-select v-model="form.fbillTypeID" placeholder="请选择单据类型" clearable style="width: 100%" @change="handleSelectBillType">
                  <el-option v-for="item in BillTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 销售组织 -->
            <el-col :span="8">
              <el-form-item label="销售组织" prop="fsaleOrgId">
                <el-select v-model="form.fsaleOrgId" placeholder="请选择销售组织" clearable style="width: 100%" @change="handleSelectSaleOrg">
                  <el-option v-for="item in ApplicationOrgId" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 结算组织 -->
            <el-col :span="8">
              <el-form-item label="结算组织" prop="fsettleOrgIds">
                <el-select v-model="form.fsettleOrgIds" placeholder="结算组织" style="width: 100%">
                  <el-option v-for="item in ApplicationOrgId" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 客户 -->
            <el-col :span="8">
              <el-form-item label="客户" prop="fcustNumber">
                <customer-search-select :useOrg="form.fsaleOrgId" :showLabel="false" :keyword.sync="form.fcustNumber" style="width: 100%" isBack @callBack="handleCustomerSearchSelect($event)" :options="[{ Number: form.fcustNumber, Name: form.fcustomerName }]" />
              </el-form-item>
            </el-col>
            <!-- 要货日期 -->
            <el-col :span="8">
              <el-form-item label="要货日期" prop="fdeliveryDate">
                <el-date-picker v-model="form.fdeliveryDate" type="datetime" placeholder="选择日期" style="width: 100%" value-format="yyyy-MM-dd HH:mm:ss" :clearable="false"></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="24"></el-col>
            <!-- 销售员 -->
            <el-col :span="8">
              <el-form-item label="销售员" prop="fsaleNumber">
                <el-select v-model="form.fsaleNumber" filterable remote placeholder="请输入申请人关键词进行搜索" :disabled="!form.fsaleOrgId || ApplicantList.length == 0" style="width: 100%">
                  <el-option v-for="item in ApplicantList" :key="item.Number" :label="item.Name" :value="item.Number">
                    <span style="float: left">{{ item.Name }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.FDept }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 跟单员 -->
            <el-col :span="8">
              <el-form-item label="跟单员" prop="fscmjGdy">
                <staff-search-select :useOrg="form.fsaleOrgId" placeholder="请选择跟单员" :showLabel="false" :keyword.sync="form.fscmjGdy" style="width: 100%" isBack @callBack="handleStaffSearchSelect($event)" :options="[{ FNumber: form.fscmjGdy, FName: form.fscmjGdyName }]" />
              </el-form-item>
            </el-col>
            <!-- 是否含税 -->
            <el-col :span="8">
              <el-form-item label="是否含税" prop="fisincludedtax">
                <el-checkbox v-model="form.fisincludedtax">是否含税</el-checkbox>
              </el-form-item>
            </el-col>
            <!-- 备注 -->
            <el-col :span="24">
              <el-form-item label="备注" prop="fnote">
                <el-input v-model="form.fnote" type="textarea" placeholder="请输入备注" resize="none" :autosize="{ minRows: 2, maxRows: 4 }"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-button type="primary" size="small" @click="setPriceToZero">设置单价为0</el-button>
              <only-column isSetitem @updateColumns="handleUpdateColumns" :columns="columns" style="margin-bottom: 10px"></only-column>
            </el-col>
            <!-- 订单明细 -->
            <el-col :span="24">
              <el-form-item label="" label-width="0" prop="fsaleOrderEntry">
                <el-table ref="entitieTable" :data="form.fsaleOrderEntry" stripe class="custom-table custom-table-cell0" show-summary :summary-method="getSummary">
                  <!-- 序号 -->
                  <el-table-column align="center" type="index" label="序号"></el-table-column>
                  <!-- 物料编码 -->
                  <el-table-column align="center" show-overflow-tooltip prop="fmaterialId" label="物料编码" min-width="120" v-if="columns[0].visible">
                    <template slot-scope="scope">
                      <el-tooltip effect="dark" :content="scope.row.fmaterialId" :disabled="!scope.row.fmaterialId">
                        <el-form-item label-width="0" :prop="`fsaleOrderEntry.${scope.$index}.fmaterialId`" :rules="rules.fmaterialId">
                          <material-search-select :keyword.sync="scope.row.fmaterialId" :useOrg="form.fsaleOrgId" size="small" :showLabel="false" @callBack="handleMaterialSearchSelect($event, scope.row)" :options="[{ Number: scope.row.fmaterialId, Name: scope.row.fmaterialName, Specification: scope.row.Specification }]" />
                        </el-form-item>
                      </el-tooltip>
                    </template>
                  </el-table-column>
                  <!-- 物料名称 -->
                  <el-table-column align="center" show-overflow-tooltip prop="fmaterialDesc" label="物料名称" min-width="120" v-if="columns[1].visible">
                    <template slot-scope="scope">
                      <span class="table-link" @click="handleViewProduct(scope.row)">{{ scope.row.fmaterialDesc }}</span>
                    </template>
                  </el-table-column>
                  <!-- 规格型号 -->
                  <el-table-column align="center" show-overflow-tooltip prop="specs" label="规格型号" min-width="120" v-if="columns[2].visible">
                    <template slot-scope="scope">
                      <el-tooltip effect="dark" :content="`原始规格:${scope.row.specs}`" :disabled="!scope.row.Specification" placement="top">
                        <span :style="{ color: scope.row.Specification ? 'red' : '' }">{{ scope.row.Specification || scope.row.specs }}</span>
                      </el-tooltip>
                    </template>
                  </el-table-column>
                  <!-- 销售单位 -->
                  <el-table-column prop="funitID" label="销售单位" width="95" class-name="el-table-tip" v-if="columns[3].visible">
                    <template slot-scope="scope">
                      <el-form-item :class="{ isChange: scope.row.isChange }" label-width="0" :prop="`fsaleOrderEntry.${scope.$index}.funitID`" :rules="rules.funitID">
                        <el-select size="small" v-model="scope.row.funitID" filterable default-first-option placeholder="请选择">
                          <el-option v-for="(item, index) in UnitList" :key="index" :label="item.FName" :value="item.FNumber"></el-option>
                        </el-select>
                      </el-form-item>
                      <div class="table-cell-tip" v-if="scope.row.isChange">
                        <i class="el-icon-warning"></i>
                        <span>请注意，单位已变更。请检查申请数量、单价等是否需要修改！</span>
                      </div>
                    </template>
                  </el-table-column>
                  <!-- 计价单位 -->
                  <el-table-column align="center" show-overflow-tooltip prop="fpriceUnitId" label="计价单位" v-if="columns[4].visible">
                    <template slot-scope="scope">{{ unitFormat(scope.row.funitID) }}</template>
                  </el-table-column>
                  <!-- 库存单位 -->
                  <el-table-column align="center" show-overflow-tooltip prop="fstockUnitID" label="库存单位" v-if="columns[5].visible">
                    <template slot-scope="scope">{{ unitFormat(scope.row.funitID) }}</template>
                  </el-table-column>
                  <!-- 销售数量 -->
                  <el-table-column align="center" show-overflow-tooltip prop="fqty" label="销售数量" min-width="120" v-if="columns[6].visible">
                    <template slot-scope="scope">
                      <el-form-item label-width="0" :prop="`fsaleOrderEntry.${scope.$index}.fqty`" :rules="rules.fqty">
                        <el-input v-model="scope.row.fqty" size="small" placeholder="销售数量" @change="handlefqtyChange(scope.row)"></el-input>
                      </el-form-item>
                    </template>
                  </el-table-column>
                  <!-- 计价数量 -->
                  <el-table-column align="center" show-overflow-tooltip prop="fpriceUnitQty" label="计价数量" min-width="120" v-if="columns[7].visible">
                    <template slot-scope="scope">
                      <el-form-item label-width="0" :prop="`fsaleOrderEntry.${scope.$index}.fpriceUnitQty`" :rules="rules.fpriceUnitQty">
                        <el-input v-model="scope.row.fpriceUnitQty" size="small" placeholder="计价数量"></el-input>
                      </el-form-item>
                    </template>
                  </el-table-column>
                  <!-- 单价 -->
                  <el-table-column align="center" prop="fprice" label="单价" min-width="150" v-if="columns[8].visible">
                    <template slot-scope="scope">
                      <el-form-item :class="{ isChange: scope.row.isChange }" label-width="0" :prop="`fsaleOrderEntry.${scope.$index}.fprice`" :rules="rules.fprice">
                        <el-input v-model="scope.row.fprice" size="small" placeholder="单价" @change="handlefpriceChange(scope.row)">
                          <span slot="prefix" class="inline-flex">￥</span>
                        </el-input>
                      </el-form-item>
                    </template>
                  </el-table-column>
                  <!-- 含税单价 -->
                  <el-table-column align="center" show-overflow-tooltip prop="ftaxprice" label="含税单价" min-width="125" v-if="columns[9].visible">
                    <template slot-scope="scope">
                      <el-form-item label-width="0" :prop="`fsaleOrderEntry.${scope.$index}.ftaxprice`" :rules="rules.ftaxprice">
                        <el-input v-model="scope.row.ftaxprice" size="small" placeholder="含税单价" @change="handleftaxpriceChange(scope.row)">
                          <span slot="prefix" class="inline-flex">￥</span>
                        </el-input>
                      </el-form-item>
                    </template>
                  </el-table-column>
                  <!-- 结算单价 -->
                  <el-table-column align="center" prop="jsprice" label="结算单价" min-width="150" v-if="columns[10].visible">
                    <template slot-scope="scope">
                      <el-form-item label-width="0" :prop="`fsaleOrderEntry.${scope.$index}.jsprice`" :rules="rules.jsprice">
                        <el-input v-model="scope.row.jsprice" size="small" placeholder="结算单价">
                          <span slot="prefix" class="inline-flex">￥</span>
                        </el-input>
                      </el-form-item>
                    </template>
                  </el-table-column>
                  <!-- 税率 -->
                  <el-table-column align="center" show-overflow-tooltip prop="fentrytaxrate" label="税率" width="120" v-if="columns[11].visible">
                    <template slot-scope="scope">
                      <el-form-item label-width="0" :prop="`fsaleOrderEntry.${scope.$index}.fentrytaxrate`" :rules="rules.fentrytaxrate">
                        <el-input v-model="scope.row.fentrytaxrate" size="small" placeholder="税率" @change="handlefentrytaxrateChange(scope.row)">
                          <span slot="suffix" class="inline-flex">%</span>
                        </el-input>
                      </el-form-item>
                    </template>
                  </el-table-column>
                  <!-- 操作 -->
                  <el-table-column fixed="right" align="center" label="操作" width="120" v-if="form.fsaleOrderEntry.length > 1 && !form.fid && columns[12].visible">
                    <template slot-scope="{ row }">
                      <el-button class="table-btn danger" size="small" @click="handleDelete(row)">删除</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn" @click="handleCancel()">取消</el-button>
        <el-button class="custom-dialog-btn primary" @click="handleSubmit">保存</el-button>
      </div>
    </el-dialog>
    <!-- 产品详情 -->
    <product-dialog ref="productInfo"></product-dialog>
  </div>
</template>

<script>
import { isNumber, isNumberLength } from '@/utils/validate'
import { contractPriceChange } from '@/api/purchase'
import { getProduct } from '@/api/system/product'
import { getPrivateduct } from '@/api/system/privateduct'
import { getProductKingdeeCode, getSalesmanList, saveProductKingdeeCode, getMaterialDetail } from '@/api/kingdee'
import { saveSaleOrder } from '@/api/kingdee/purchase/saleOrder'
import { getCustomerList } from '@/api/kingdee/customer'
import ProductDialog from '@/views/public/product/dialog'
import MaterialDetail from '@/views/kingdee/material/detail'
import materialSearchSelect from '@/components/SearchSelect/material'
import customerSearchSelect from '@/components/SearchSelect/customer'
import { kingdee } from '@/minix'
import OnlyColumn from '@/components/RightToolbar/onlyColumn'
import staffSearchSelect from '@/components/SearchSelect/staff'

export default {
  mixins: [kingdee],
  components: { ProductDialog, MaterialDetail, materialSearchSelect, customerSearchSelect, OnlyColumn, staffSearchSelect },
  data() {
    return {
      title: '新增销售订单',
      open: false,
      form: { fsaleOrderEntry: [] },
      rules: {
        fbillTypeID: [{ required: true, message: '请选择单据类型', trigger: 'change' }],
        fdate: [{ required: true, message: '请选择日期', trigger: 'change' }],
        fsaleOrgId: [{ required: true, message: '请选择销售组织', trigger: 'change' }],
        fcustNumber: [{ required: true, message: '请选择客户', trigger: 'change' }],
        fsaleNumber: [{ required: true, message: '请选择销售员', trigger: 'change' }],
        fscmjGdy: [{ required: true, message: '请选择跟单员', trigger: 'change' }],
        fsaleOrderEntry: [{ required: true, message: '订单明细不能为空', trigger: 'change' }],
        fmaterialId: [{ required: true, message: '请选择物料编码', trigger: 'change' }],
        funitID: [{ required: true, message: '请选择销售单位', trigger: 'change' }],
        fpriceUnitId: [{ required: true, message: '请选择计价单位', trigger: 'change' }],
        fdeliveryDate: [{ required: true, message: '请选择要货日期', trigger: 'change' }],
        fsettleOrgIds: [{ required: true, message: '请选择结算组织', trigger: 'change' }],
        fstockUnitID: [{ required: true, message: '请选择库存单位', trigger: 'change' }],
        fqty: [
          { required: true, message: '请输入销售数量', trigger: 'blur' },
          { validator: isNumber, message: '请输入正确的数量', trigger: ['change', 'blur'] },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: ['change', 'blur'] }
        ],
        fpriceUnitQty: [
          { required: true, message: '请输入计价数量', trigger: 'blur' },
          { validator: isNumber, message: '请输入正确的计价数量', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (value > this.form.fsaleOrderEntry.find(entry => entry.fpriceUnitQty === value).fqty) {
                callback(new Error('计价数量不可超过销售数量'))
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ],
        fprice: [
          { required: true, message: '请输入单价', trigger: 'blur' },
          { validator: isNumber, message: '请输入正确的单价', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: 'blur' }
        ],
        ftaxprice: [
          { validator: isNumber, message: '请输入正确的含税单价', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: 'blur' }
        ],
        fentrytaxrate: [
          { validator: isNumber, message: '请输入正确的税率', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: 'blur' }
        ],
        jsprice: [
          { required: true, message: '请输入结算单价', trigger: 'blur' },
          { validator: isNumber, message: '请输入正确的结算单价', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: 'blur' }
        ]
      },
      // 单据类型
      BillTypeOptions: [
        { label: '标准销售订单', value: 'XSDD01_SYS' },
        // { label: '寄售销售订单', value: 'XSDD02_SYS' },
        // { label: '受托加工销售', value: 'XSDD03_SYS' },
        { label: '直运销售订单', value: 'XSDD04_SYS' },
        { label: '退货订单', value: 'XSDD05_SYS' },
        // { label: '分销调拨订单', value: 'XSDD06_SYS' },
        // { label: '分销购销订单', value: 'XSDD07_SYS' },
        // { label: 'VMI销售订单', value: 'XSDD08_SYS' },
        // { label: '现销订单', value: 'XSDD09_SYS' },
        { label: '工程项目订单', value: 'XSDD10_SYS' }
      ],
      useOrg: undefined, // 使用组织
      // 销售员列表
      ApplicantList: [],
      ApplicantLoading: false,
      // 显隐列
      columns: [
        { key: 0, label: `物料编码`, visible: true },
        { key: 1, label: `物料名称`, visible: true },
        { key: 2, label: `规格型号`, visible: true },
        { key: 3, label: `销售单位`, visible: true },
        { key: 4, label: `计价单位`, visible: true },
        { key: 5, label: `库存单位`, visible: true },
        { key: 6, label: `销售数量`, visible: true },
        { key: 7, label: `计价数量`, visible: true },
        { key: 8, label: `单价`, visible: true },
        { key: 9, label: `含税单价`, visible: true },
        { key: 10, label: `结算单价`, visible: true },
        { key: 11, label: `税率`, visible: true },
        { key: 12, label: `操作`, visible: true }
      ]
    }
  },
  watch: {
    'form.fsaleOrgId': function (newVal) {
      this.ApplicantRemoteMethod()
    }
  },
  computed: {
    userId() {
      return this.$store.getters.info.userId
    }
  },
  methods: {
    // 更新显隐列
    handleUpdateColumns(columns) {
      this.columns = columns
      localStorage.setItem(`${this.userId}.sellSaleOrderColumns`, JSON.stringify(columns))
    },
    // 销售员远程方法
    ApplicantRemoteMethod(query) {
      if (!this.form.fsaleOrgId) {
        this.ApplicantList = []
        return
      }
      this.ApplicantLoading = true
      const params = { bizOrg: this.form.fsaleOrgId, name: query }
      getSalesmanList(params).then(res => {
        const { code, msg, data } = res
        const { data: applicantList } = data
        if (code == 200) {
          this.ApplicantList = applicantList
        } else this.$message.error(msg)
        this.ApplicantLoading = false
      })
    },
    // 表单重置
    reset() {
      this.form = {
        contractId: undefined, // 合同ID
        fcustNumber: undefined,
        fcustomerName: undefined,
        fdate: this.parseTime(new Date(), '{y}-{m}-{d}'),
        fsaleNumber: undefined,
        fsaleOrderEntry: [],
        fsaleOrgId: undefined,
        fscmjGdy: undefined,
        fscmjGdyName: undefined,
        fisincludedtax: false,
        fnote: undefined,
        fbillTypeID: undefined,
        fdeliveryDate: this.parseTime(new Date(), '{y}-{m}-{d} {h}:{i}:{s}'),
        fsettleOrgIds: undefined
      }
      this.resetForm('form')
    },
    // 创建
    handleCreate(data = {}) {
      const { sellerName, id } = data
      if (!id) {
        this.$message.error('参数错误，请稍后再试！')
        return
      }
      const loading = this.$loading({
        lock: true,
        text: '加载中',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      this.reset()
      this.form.contractId = id
      const columns = localStorage.getItem(`${this.userId}.sellSaleOrderColumns`)
      if (columns) this.columns = JSON.parse(columns)
      contractPriceChange({ contractId: id })
        .then(async res => {
          const { code, data, msg } = res
          if (code == 200) {
            for (const item of data) {
              const info = item.source == 'common' ? await getProduct(item.productId) : await getPrivateduct(item.productId)
              item.info = info.data
              const funitID = this.UnitList.find(unit => unit.FName == item.unit)?.FNumber || ''
              this.form.fsaleOrderEntry.push({
                fmaterialId: item.info?.materialCode || '', // 物料编码
                fmaterialName: item.info?.materialCode || '', // 物料名称
                fmaterialDesc: item.productName, // 物料名称
                specs: info?.data?.specs, // 规格
                funitID: funitID, // 销售单位
                fpriceUnitId: funitID, // 计价单位
                fdeliveryDate: undefined, // 要货日期
                fsettleOrgIds: undefined, // 结算组织
                fstockUnitID: funitID, // 库存单位
                fqty: item.sjNum, // 销售数量
                fpriceUnitQty: item.sjNum, // 计价数量
                fprice: item.amount, // 单价
                ftaxprice: item.amount, // 含税单价
                jsprice: undefined, // 结算单价
                fentrytaxrate: 0, // 税率
                info: info.data, // 产品信息
                isChange: false,
                source: item.source,
                productId: item.productId
              })
            }
            const customer = await getCustomerList({ useOrg: this.form.fsaleOrgId, name2: sellerName })
            const { data: customerList } = customer.data
            const filterList = customerList.filter(item => item.UseOrg != '世盛总部')
            const newList = filterList.reduce((unique, item) => {
              const exists = unique.find(u => u.Number === item.Number)
              if (!exists) unique.push(item)
              return unique
            }, [])
            if (customer.code == 200 && newList.length) {
              this.form.fcustNumber = newList[0].Number
              this.form.fcustomerName = newList[0].Name
              this.options = newList
              const fsaleOrgId = this.ApplicationOrgId.find(org => org.label == newList[0].UseOrg)
              this.$set(this.form, 'fsaleOrgId', fsaleOrgId?.value || undefined)
            }
            this.title = '新增销售订单'
            this.open = true
            if (this.form.fsaleOrderEntry.length) {
              this.loadAdditionalData(this.form.fsaleOrderEntry)
            }
          } else this.$message.error(msg)
        })
        .finally(() => {
          loading.close()
        })
    },
    // 加载附加数据
    async loadAdditionalData(rows) {
      try {
        // 使用 for...of 循环保证顺序
        for (const item of rows) {
          try {
            // 查询产品对应金蝶产品编码
            const productKingdeeCode = await getProductKingdeeCode({ productId: item.productId, source: item.source })
            const materialNumber = productKingdeeCode?.data?.materialNumber || productKingdeeCode?.data || ''
            const materialName = productKingdeeCode?.data?.materialName || ''
            const funitID = this.UnitList.find(unit => unit.FName == productKingdeeCode?.data?.unit)?.FNumber || ''
            if (materialNumber && funitID) {
              let fmaterialName = materialName
              if (!fmaterialName) {
                // 查询物料详情
                const materialDetail = await getMaterialDetail({ number: materialNumber })
                const resData = materialDetail?.data?.result?.result || {}
                fmaterialName = resData?.Name?.[0]?.Value || ''
              }
              // 更新物料编码和单位
              const index = this.form.fsaleOrderEntry.findIndex(row => row.productId === item.productId && row.source === item.source)
              if (index > -1) {
                const entry = { ...this.form.fsaleOrderEntry[index] }
                // 如果单位发生变化,标记isChange为true
                if (funitID !== entry.funitID) {
                  entry.isChange = true
                }
                // 更新物料编码和单位
                entry.fmaterialId = materialNumber
                entry.fmaterialName = fmaterialName
                entry.funitID = funitID
                entry.fpriceUnitId = funitID
                entry.fstockUnitID = funitID
                // 使用 Vue.set 更新数据
                this.$set(this.form.fsaleOrderEntry, index, entry)
              }
            } else {
              const { materialCode } = item?.info || {}
              if (materialCode) {
                const materialDetail = await getMaterialDetail({ number: materialCode })
                const resData = materialDetail?.data?.result?.result || {}
                const kMaterialNumber = resData?.Number || ''
                const kMaterialName = resData?.Name?.[0]?.Value || ''
                const kFunitID = resData?.MaterialBase?.[0]?.BaseUnitId?.Number || ''
                const index = this.form.fsaleOrderEntry.findIndex(row => row.productId === item.productId && row.source === item.source)
                if (index > -1) {
                  const entry = { ...this.form.fsaleOrderEntry[index] }
                  if (kFunitID !== entry.funitID) {
                    entry.isChange = true
                  }
                  entry.fmaterialId = kMaterialNumber
                  entry.fmaterialName = kMaterialName
                  entry.funitID = kFunitID
                  entry.fpriceUnitId = kFunitID
                  entry.fstockUnitID = kFunitID
                  // 使用 Vue.set 更新数据
                  this.$set(this.form.fsaleOrderEntry, index, entry)
                  // 保存产品金蝶编码
                  if (kMaterialNumber && kFunitID) {
                    const data = {
                      materialNumber: kMaterialNumber,
                      materialName: kMaterialName,
                      productId: item.productId,
                      source: item.source,
                      unit: this.UnitList.find(unit => unit.FNumber == kFunitID)?.FName || ''
                    }
                    await saveProductKingdeeCode(data)
                  }
                }
              }
            }
          } catch (error) {
            console.error('加载数据出错:', error)
            // 继续处理下一条数据
            continue
          }
        }
      } catch (error) {
        console.error('loadAdditionalData 执行出错:', error)
      }
    },
    // 修改
    handleUpdate(info = {}) {
      // 判断info是否为空对象
      if (!info || Object.keys(info).length === 0) {
        this.$message.error('参数错误，请稍后再试！')
        return
      }
      this.reset()
      const columns = localStorage.getItem(`${this.userId}.sellSaleOrderColumns`)
      if (columns) this.columns = JSON.parse(columns)
      const fsettleOrgIds = info.SaleOrderEntry.map(item => item.SettleOrgId && item.SettleOrgId?.Number)
      // 对结算组织进行去重处理
      const uniqueSettleOrgIds = [...new Set(fsettleOrgIds.filter(item => item))]
      const fdeliveryDate = info.SaleOrderEntry.map(item => item.DeliveryDate && this.parseTime(item.DeliveryDate, '{y}-{m}-{d} {h}:{i}:{s}'))
      const uniqueDeliveryDate = [...new Set(fdeliveryDate.filter(item => item))]
      const fsaleOrderEntry = info.SaleOrderEntry.map(item => {
        return {
          fentryID: item.Id,
          fmaterialId: item.MaterialId && item.MaterialId?.Number, // 物料编码
          fmaterialName: item.MaterialId && this.getString(item.MaterialId?.Name), // 物料名称
          fmaterialDesc: item.MaterialId && this.getString(item.MaterialId?.Name), // 物料描述
          specs: item.MaterialId && this.getString(item.MaterialId?.Specification), // 规格型号
          funitID: item.UnitId && item.UnitId?.Number, // 销售单位
          fpriceUnitId: item.PriceUnitId && item.PriceUnitId?.Number, // 计价单位
          fstockUnitID: item.StockUnitID && item.StockUnitID?.Number, // 库存单位
          fqty: item.Qty, // 销售数量
          fpriceUnitQty: item.PriceUnitQty, // 计价数量
          fprice: item.Price, // 单价
          ftaxprice: item.TaxPrice, // 含税单价
          jsprice: item.F_SCMJ_JSPrice, // 结算单价
          fentrytaxrate: item.TaxRate // 税率
        }
      })
      this.form = {
        fid: info.Id,
        fdate: info.Date && this.parseTime(info.Date, '{y}-{m}-{d}'), // 日期
        fbillTypeID: info.BillTypeId && info.BillTypeId?.Number, // 单据类型
        fsaleOrgId: info.SaleOrgId && info.SaleOrgId?.Number, // 销售组织
        fsettleOrgIds: uniqueSettleOrgIds.toString(), // 结算组织
        fcustNumber: info.CustId && info.CustId?.Number, // 客户编码
        fcustomerName: info.CustId && this.getString(info.CustId?.Name), // 客户名称
        fdeliveryDate: uniqueDeliveryDate.toString(), // 要货日期
        fsaleNumber: info.SalerId && info.SalerId?.Number, // 销售员
        fscmjGdy: info.F_SCMJ_GDY && info.F_SCMJ_GDY?.FStaffNumber, // 跟单员
        fscmjGdyName: info.F_SCMJ_GDY && this.getString(info.F_SCMJ_GDY?.Name), // 跟单员名称
        fisincludedtax: info?.SaleOrderFinance?.[0]?.IsIncludedTax || false, // 是否含税
        fsaleOrderEntry, // 销售订单明细
        fnote: info?.Note // 备注
      }
      this.title = '修改销售订单'
      this.open = true
    },
    // 提交
    handleSubmit() {
      this.$refs.form.validate(async valid => {
        if (valid) {
          const { contractId, fdate, fsaleOrgId, fcustNumber, fsaleNumber, fscmjGdy, fsaleOrderEntry, fisincludedtax, fnote, fbillTypeID, fdeliveryDate, fsettleOrgIds, fid } = this.form
          const newfsaleOrderEntry = fsaleOrderEntry.map(item => {
            return {
              fentryID: item.fentryID,
              fmaterialId: item.fmaterialId,
              funitID: item.funitID,
              fpriceUnitId: item.funitID,
              fdeliveryDate: fdeliveryDate,
              fsettleOrgIds: fsettleOrgIds,
              fstockUnitID: item.funitID,
              fqty: item.fqty,
              fpriceUnitQty: item.fpriceUnitQty,
              fprice: item.fprice,
              ftaxprice: item.ftaxprice,
              jsprice: item.jsprice,
              fentrytaxrate: item.fentrytaxrate
            }
          })
          let data = { contractId, fbillTypeID, fdate, fsaleOrgId, fcustNumber, fsaleNumber, fscmjGdy, fsaleOrderEntry: newfsaleOrderEntry, fisincludedtax, fnote }
          const res = fid ? await saveSaleOrder({ ...data, fid }) : await saveSaleOrder(data)
          if (res.code == 200) {
            this.$message.success(fid ? '修改成功' : '保存成功')
            this.handleCancel(true)
          } else this.$message.error(res.msg)
        }
      })
    },
    // 取消
    handleCancel(refresh = false) {
      this.open = false
      this.$emit('callBack', refresh)
    },
    // 客户回调
    handleCustomerSearchSelect(event) {
      this.form.fcustNumber = event.Number
      this.form.fcustomerName = event.Name
    },
    // 跟单员回调
    handleStaffSearchSelect(event) {
      this.form.fscmjGdy = event.FNumber
      this.form.fscmjGdyName = event.FName
    },
    // 选择物料回调
    handleMaterialSearchSelect(event, row) {
      const obj = this.UnitList.find(item => item.FName == event.Unit)
      this.$set(row, 'fmaterialId', event.Number)
      this.$set(row, 'fmaterialName', event.Name)
      this.$set(row, 'Specification', event.Specification || '')
      this.$set(row, 'isChange', row.funitID != obj?.FNumber)
      this.$set(row, 'funitID', obj?.FNumber || '')
      this.$set(row, 'fpriceUnitId', obj?.FNumber || '')
      this.$set(row, 'fstockUnitID', obj?.FNumber || '')
      const product = row.info
      const source = row.source
      const data = { materialNumber: event.Number, materialName: event.Name, productId: (product && product.id) || undefined, source, unit: event.Unit }
      if (product && product.id) {
        saveProductKingdeeCode(data).then(res => {
          const { code, msg } = res
          if (code !== 200) this.$message.error(msg)
        })
      }
    },
    // 查看产品
    handleViewProduct(row) {
      this.$refs.productInfo.handleView(row.info)
    },
    // 单价变化改变含税单价
    handlefpriceChange(row) {
      // 如果税率不为空则计算含税单价
      if (!row.fentrytaxrate) row.ftaxprice = row.fprice
      else row.ftaxprice = parseFloat((row.fprice * (1 + row.fentrytaxrate / 100)).toFixed(5))
    },
    // 含税单价变化，改变单价
    handleftaxpriceChange(row) {
      // 如果税率不为空则计算单价
      if (!row.fentrytaxrate) row.fprice = row.ftaxprice
      else row.fprice = parseFloat((row.ftaxprice / (1 + row.fentrytaxrate / 100)).toFixed(5))
    },
    // 税率变化，含税单价不变，改变单价
    handlefentrytaxrateChange(row) {
      // 如果含税单价不为空则计算单价
      if (row.ftaxprice) row.fprice = parseFloat((row.ftaxprice / (1 + row.fentrytaxrate / 100)).toFixed(5))
    },
    // 格式化计价单位
    unitFormat(unitId) {
      const unitObj = this.UnitList.find(unit => unit.FNumber == unitId)
      return unitObj?.FName || ''
    },
    // 要货日期变化
    handleFarrivalDateChange(e) {
      if (!e) return
      this.form.fsaleOrderEntry.forEach(entity => {
        this.$set(entity, 'fdeliveryDate', e)
      })
    },
    // 结算组织变化
    handleSettleOrgChange(e) {
      if (!e) return
      this.form.fsaleOrderEntry.forEach(entity => {
        this.$set(entity, 'fsettleOrgIds', e)
      })
    },
    // 删除
    handleDelete(row) {
      this.form.fsaleOrderEntry.splice(this.form.fsaleOrderEntry.indexOf(row), 1)
    },
    // 销售数量变化
    handlefqtyChange(row) {
      row.fpriceUnitQty = row.fqty
    },
    // 选择销售组织
    handleSelectSaleOrg(val) {
      if (!!val) {
        this.form.fcustNumber = undefined
        this.form.fcustomerName = undefined
        this.form.fsaleNumber = undefined
        this.form.fscmjGdy = undefined
        this.form.fscmjGdyName = undefined
      }
    },
    // 选择单据类型
    handleSelectBillType(val) {
      if (val === 'XSDD10_SYS') {
        if (this.form.fsaleOrderEntry && this.form.fsaleOrderEntry.length > 0) {
          this.form.fsaleOrderEntry.forEach(item => {
            item.fprice = 0
            item.ftaxprice = 0
          })
        }
      }
    },
    // 设置单价为0
    setPriceToZero() {
      if (this.form.fsaleOrderEntry && this.form.fsaleOrderEntry.length > 0) {
        this.form.fsaleOrderEntry.forEach(item => {
          item.fprice = 0
          item.ftaxprice = 0
        })
      }
    },
    // 合计金额、结算金额
    getSummary(param) {
      const { columns, data } = param
      const result = columns.map((column, index) => {
        if (index === 0) {
          return '合计'
        }
        if (column.property === 'fprice' || column.property === 'jsprice') {
          return `${this.calculateTotal(data, column.property)}`
        }
        return ''
      })
      return result
    },
    // 计算合计金额
    calculateTotal(data, key) {
      const total = data.reduce((sum, item) => {
        const qty = parseFloat(item.fqty) || 0
        const price = key === 'fprice' ? parseFloat(item.fprice) || 0 : parseFloat(item.jsprice) || 0
        return sum + qty * price
      }, 0)

      const prefix = key === 'fprice' ? '销售金额：' : '结算金额：'
      return prefix + parseFloat(total.toFixed(5))
    }
  }
}
</script>
<style scoped lang="scss">
@import '@/assets/styles/custom-new.scss';
::v-deep {
  .el-form.custom-form {
    .el-form-item__label {
      line-height: 20px;
      min-height: 40px;
      display: inline-flex;
      align-items: center;
      font-weight: normal;
      color: $disabled;
      text-align: left;
    }
  }
  .custom-table {
    .notFind {
      background: #ffaaaa;
    }
    .el-form-item {
      margin-top: 10px !important;
      margin-bottom: 10px !important;
      .el-form-item__error {
        top: 95%;
        padding-top: 0;
      }
    }
    .el-table__fixed-right {
      .el-table__cell {
        padding: 0;
        &.is-leaf {
          border-bottom-width: 0;
        }
        .table-btn {
          width: 90px;
          height: 30px;
          border-radius: 5px;
          border: 1px solid #cbd7e2;
          background-color: transparent;
          cursor: pointer;
          margin: 2px 5px;
          font-size: 12px;
          color: $info;
          &.danger {
            border-color: $red;
            color: $red;
            &:hover {
              background-color: $red;
              color: $white;
            }
            &:disabled {
              cursor: no-drop;
            }
            &.hasbg {
              background-color: #ffe5e5;
              &:hover {
                background-color: $red;
                color: $white;
              }
            }
          }
        }
      }
    }
    .custom-date-picker {
      .el-date-editor {
        .el-input__inner {
          padding-right: 15px;
        }
      }
    }
    .custom-select {
      .el-input {
        .el-input__inner {
          padding-right: 15px;
        }
      }
    }
    td.el-table__cell.el-table-tip {
      .cell {
        overflow: unset;
        position: relative;
        .el-form-item.isChange {
          .el-input__inner {
            border-color: #f43f3f;
          }
        }
        .table-cell-tip {
          display: flex;
          align-items: center;
          width: 500px;
          color: #f43f3f;
          position: absolute;
          top: -13px;
          left: 10px;
          z-index: 2;
        }
      }
    }
  }
}
.custom-dialog.custom-material-dialog ::v-deep {
  .el-dialog__body {
    padding: 0;
  }
}
.el-col-50 {
  width: 20%;
}
</style>
