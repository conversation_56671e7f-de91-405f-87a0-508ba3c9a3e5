<template>
  <div>
    <el-dialog v-dialogDragBox title="销售订单详情" :visible.sync="detailOpen" width="90%" class="custom-dialog" :before-close="beforeClose" append-to-body>
      <div style="padding: 0 20px">
        <div class="kindeeButton" :key="info.DocumentStatus">
          <el-button type="primary" size="medium" v-if="info.DocumentStatus == 'A' || info.DocumentStatus == 'D'" @click="handleKingdeeDo('submit')">提交</el-button>
          <el-button type="primary" size="medium" disabled v-else>提交</el-button>
          <el-button type="warning" size="medium" v-if="info.DocumentStatus == 'B'" @click="handleKingdeeDo('audit')">审核</el-button>
          <el-button type="warning" size="medium" disabled v-else>审核</el-button>
          <el-button type="info" size="medium" v-if="info.DocumentStatus == 'B'" @click="handleKingdeeDo('revoke')">撤销</el-button>
          <el-button type="info" size="medium" disabled v-else>撤销</el-button>
          <el-button type="danger" size="medium" v-if="info.DocumentStatus == 'A' || info.DocumentStatus == 'D'" @click="handleKingdeeDo('delete')">删除</el-button>
          <el-button type="danger" size="medium" disabled v-else>删除</el-button>
          <el-button type="warning" size="medium" v-if="info.DocumentStatus == 'C'" @click="handleKingdeeDo('unAudit')">反审</el-button>
          <el-button type="warning" size="medium" disabled v-else>反审</el-button>
          <el-button type="success" size="medium" v-if="info.DocumentStatus == 'C'" @click="handleKingdeeDo('push')">下推</el-button>
          <el-button type="success" size="medium" disabled v-else>下推</el-button>
          <el-button size="medium" icon="el-icon-edit" v-if="info.DocumentStatus == 'A' || info.DocumentStatus == 'D'" @click="handleEdit()">修改</el-button>
        </div>
        <el-tabs v-model="activeName" type="card" @tab-click="handleClick" class="custom-tabs">
          <el-tab-pane label="基本信息" name="first"></el-tab-pane>
          <el-tab-pane label="客户信息" name="second"></el-tab-pane>
          <el-tab-pane label="财务信息" name="third"></el-tab-pane>
          <el-tab-pane label="订单条款" name="fourth"></el-tab-pane>
          <el-tab-pane label="物流跟踪" name="fifth"></el-tab-pane>
          <el-tab-pane label="其他" name="sixth"></el-tab-pane>
        </el-tabs>
        <el-descriptions border :column="4" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 4 - 7em - 22px)' }" v-show="activeName === 'first'">
          <!-- 单据类型 -->
          <el-descriptions-item label="单据类型">{{ info.BillTypeId && getString(info.BillTypeId.Name) }}</el-descriptions-item>
          <!-- 销售组织 -->
          <el-descriptions-item label="销售组织">{{ info.SaleOrgId && getString(info.SaleOrgId.Name) }}</el-descriptions-item>
          <!-- 销售部门 -->
          <el-descriptions-item label="销售部门">{{ info.SaleDeptId && getString(info.SaleDeptId.Name) }}</el-descriptions-item>
          <!-- 交货方式 -->
          <el-descriptions-item label="交货方式">{{ info.HeadDeliveryWay && getString(info.HeadDeliveryWay.Name) }}</el-descriptions-item>
          <!-- 单据编号 -->
          <el-descriptions-item label="单据编号">{{ info.BillNo }}</el-descriptions-item>
          <!-- 客户 -->
          <el-descriptions-item label="客户">{{ info.CustId && getString(info.CustId.Name) }}</el-descriptions-item>
          <!-- 销售员 -->
          <el-descriptions-item label="销售员">{{ info.SalerId && getString(info.SalerId.Name) }}</el-descriptions-item>
          <!-- 交货地点 -->
          <el-descriptions-item label="交货地点">{{ info.HeadLocId && getString(info.HeadLocId.Name) }}</el-descriptions-item>
          <!-- 日期 -->
          <el-descriptions-item label="日期">{{ info.Date && parseTime(info.Date, '{y}-{m}-{d}') }}</el-descriptions-item>
          <!-- 价目表 -->
          <el-descriptions-item label="价目表">{{ info.SaleOrderFinance && info.SaleOrderFinance[0] && info.SaleOrderFinance[0].PriceListId && getString(info.SaleOrderFinance[0].PriceListId.Name) }}</el-descriptions-item>
          <!-- 单据状态 -->
          <el-descriptions-item label="单据状态">{{ info.DocumentStatus && getDocumentStatusLabel(info.DocumentStatus) }}</el-descriptions-item>
          <!-- 备注 -->
          <el-descriptions-item label="备注">{{ info.Note }}</el-descriptions-item>
          <!-- 业务类型 -->
          <el-descriptions-item label="业务类型">{{ info.BusinessType && getOptionLabel(BusinessTypeOptions, info.BusinessType) }}</el-descriptions-item>
          <!-- 收款条件 -->
          <el-descriptions-item label="收款条件">{{ info.SaleOrderFinance && info.SaleOrderFinance[0] && info.SaleOrderFinance[0].RecConditionId && getString(info.SaleOrderFinance[0].RecConditionId.Name) }}</el-descriptions-item>
          <!-- 变更原因 -->
          <el-descriptions-item label="变更原因">{{ info.ChangeReason }}</el-descriptions-item>
          <!-- 结算币别 -->
          <el-descriptions-item label="结算币别">{{ info.SaleOrderFinance && info.SaleOrderFinance[0] && info.SaleOrderFinance[0].SettleCurrId && getString(info.SaleOrderFinance[0].SettleCurrId.Name) }}</el-descriptions-item>
          <!-- 是否含税 -->
          <el-descriptions-item label="是否含税">
            <el-checkbox v-model="isIncludedTax" disabled></el-checkbox>
          </el-descriptions-item>
          <!-- 跟单员 -->
          <el-descriptions-item label="跟单员">{{ info.F_SCMJ_GDY && getString(info.F_SCMJ_GDY.Name) }}</el-descriptions-item>
        </el-descriptions>
        <el-descriptions border :column="2" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 2 - 7em - 22px)' }" v-show="activeName === 'second'">
          <!-- 收货方 -->
          <el-descriptions-item label="收货方">{{ info.ReceiveId && getString(info.ReceiveId.Name) }}</el-descriptions-item>
          <!-- 收货方联系人 -->
          <el-descriptions-item label="收货方联系人">{{ info.ReceiveContact }}</el-descriptions-item>
          <!-- 收货方地址 -->
          <el-descriptions-item label="收货方地址" :span="2">{{ info.ReceiveAddress }}</el-descriptions-item>
          <!-- 收货人姓名 -->
          <el-descriptions-item label="收货人姓名">{{ info.FLinkMan }}</el-descriptions-item>
          <!-- 联系电话 -->
          <el-descriptions-item label="联系电话">{{ info.ContactPhone }}</el-descriptions-item>
          <!-- 结算方 -->
          <el-descriptions-item label="结算方">{{ info.SettleId && getString(info.SettleId.Name) }}</el-descriptions-item>
          <!-- 付款方 -->
          <el-descriptions-item label="付款方">{{ info.ChargeId && getString(info.ChargeId.Name) }}</el-descriptions-item>
        </el-descriptions>
        <el-descriptions border :column="4" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 4 - 7em - 22px)' }" v-show="activeName === 'third'">
          <!-- 结算币别 -->
          <el-descriptions-item label="结算币别">{{ info.SaleOrderFinance && info.SaleOrderFinance[0] && info.SaleOrderFinance[0].SettleCurrId && getString(info.SaleOrderFinance[0].SettleCurrId.Name) }}</el-descriptions-item>
          <!-- 折扣表 -->
          <el-descriptions-item label="折扣表">{{ info.SaleOrderFinance && info.SaleOrderFinance[0] && info.SaleOrderFinance[0].DiscountListId && getString(info.SaleOrderFinance[0].DiscountListId.Name) }}</el-descriptions-item>
          <!-- 本位币 -->
          <el-descriptions-item label="本位币">{{ info.SaleOrderFinance && info.SaleOrderFinance[0] && info.SaleOrderFinance[0].LocalCurrId && getString(info.SaleOrderFinance[0].LocalCurrId.Name) }}</el-descriptions-item>
          <!-- 未收款金额 -->
          <el-descriptions-item label="未收款金额">{{ (info.SalOrderRec && info.SalOrderRec[0] && info.SalOrderRec[0].FPLANNOTRECAMOUNT) || '' }}</el-descriptions-item>
          <!-- 收款条件 -->
          <el-descriptions-item label="收款条件">{{ info.SaleOrderFinance && info.SaleOrderFinance[0] && info.SaleOrderFinance[0].RecConditionId && getString(info.SaleOrderFinance[0].RecConditionId.Name) }}</el-descriptions-item>
          <!-- 税额 -->
          <el-descriptions-item label="税额">{{ (info.SaleOrderFinance && info.SaleOrderFinance[0] && info.SaleOrderFinance[0].BillTaxAmount) || '' }}</el-descriptions-item>
          <!-- 汇率类型 -->
          <el-descriptions-item label="汇率类型">{{ info.SaleOrderFinance && info.SaleOrderFinance[0] && info.SaleOrderFinance[0].ExchangeTypeId && getString(info.SaleOrderFinance[0].ExchangeTypeId.Name) }}</el-descriptions-item>
          <!-- 累计收款金额 -->
          <el-descriptions-item label="累计收款金额">{{ (info.SalOrderRec && info.SalOrderRec[0] && info.SalOrderRec[0].FPLANALLRECAMOUNT) || '' }}</el-descriptions-item>
          <!-- 结算方式 -->
          <el-descriptions-item label="结算方式">{{ info.SaleOrderFinance && info.SaleOrderFinance[0] && info.SaleOrderFinance[0].SettleModeId && getString(info.SaleOrderFinance[0].SettleModeId.Name) }}</el-descriptions-item>
          <!-- 金额 -->
          <el-descriptions-item label="金额">{{ (info.SaleOrderFinance && info.SaleOrderFinance[0] && info.SaleOrderFinance[0].BillAmount) || '' }}</el-descriptions-item>
          <!-- 汇率 -->
          <el-descriptions-item label="汇率">{{ (info.SaleOrderFinance && info.SaleOrderFinance[0] && info.SaleOrderFinance[0].ExchangeRate) || '' }}</el-descriptions-item>
          <!-- 累计退款金额 -->
          <el-descriptions-item label="累计退款金额">{{ (info.SalOrderRec && info.SalOrderRec[0] && info.SalOrderRec[0].FPLANREFUNDAMOUNT) || '' }}</el-descriptions-item>
          <!-- 价目表 -->
          <el-descriptions-item label="价目表">{{ info.SaleOrderFinance && info.SaleOrderFinance[0] && info.SaleOrderFinance[0].PriceListId && getString(info.SaleOrderFinance[0].PriceListId.Name) }}</el-descriptions-item>
          <!-- 价税合计 -->
          <el-descriptions-item label="价税合计">{{ info.SaleOrderFinance && info.SaleOrderFinance[0] && info.SaleOrderFinance[0].BillAllAmount }}</el-descriptions-item>
          <!-- 是否含税 -->
          <el-descriptions-item label="是否含税">
            <el-checkbox v-model="isIncludedTax" disabled></el-checkbox>
          </el-descriptions-item>
          <!-- 价外税 -->
          <el-descriptions-item label="价外税">
            <el-checkbox v-model="isPriceExcludeTax" disabled></el-checkbox>
          </el-descriptions-item>
          <!-- 整单折扣额 -->
          <el-descriptions-item label="整单折扣额">{{ info.SaleOrderFinance && info.SaleOrderFinance[0] && info.SaleOrderFinance[0].FAllDisCount }}</el-descriptions-item>
          <!-- 保证金比例（%） -->
          <el-descriptions-item label="保证金比例（%）">{{ (info.SaleOrderFinance && info.SaleOrderFinance[0] && info.SaleOrderFinance[0].FMarginLevel) || '' }}</el-descriptions-item>
          <!-- 关联保证金 -->
          <el-descriptions-item label="关联保证金">{{ (info.SaleOrderFinance && info.SaleOrderFinance[0] && info.SaleOrderFinance[0].FAssociateMargin) || '' }}</el-descriptions-item>
          <!-- 保证金 -->
          <el-descriptions-item label="保证金">{{ (info.SaleOrderFinance && info.SaleOrderFinance[0] && info.SaleOrderFinance[0].FMargin) || '' }}</el-descriptions-item>
          <!-- 关联退款保证金 -->
          <el-descriptions-item label="关联退款保证金">{{ (info.SaleOrderFinance && info.SaleOrderFinance[0] && info.SaleOrderFinance[0].FAssRefundMargin) || '' }}</el-descriptions-item>
        </el-descriptions>
        <el-table ref="orderTable" :data="info.SaleOrderFinance && info.SaleOrderFinance[0] && info.SaleOrderFinance[0].SaleOrderClause" class="custom-table" highlight-current-row v-show="activeName === 'fourth'">
          <!-- 序号 -->
          <el-table-column type="index" label="序号" align="center"></el-table-column>
          <!-- 条款编码 -->
          <el-table-column label="条款编码" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.ClauseId }}</template>
          </el-table-column>
          <!-- 条款名称 -->
          <el-table-column label="条款名称" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.ClauseName }}</template>
          </el-table-column>
          <!-- 条款类型 -->
          <el-table-column label="条款类型" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.ClauseType }}</template>
          </el-table-column>
          <!-- 条款内容 -->
          <el-table-column label="条款内容" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.ClauseContent }}</template>
          </el-table-column>
          <!-- 描述 -->
          <el-table-column label="描述" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.ClauseDesc }}</template>
          </el-table-column>
        </el-table>
        <el-table ref="logisticsTable" :data="info.SalOrderTrace" class="custom-table" highlight-current-row v-show="activeName === 'fifth'">
          <!-- 物流公司 -->
          <el-table-column label="物流公司" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.LogComId }}</template>
          </el-table-column>
          <!-- 物流单号 -->
          <el-table-column label="物流单号" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.CarryBillNo }}</template>
          </el-table-column>
          <!-- 寄件人手机号码 -->
          <el-table-column label="寄件人手机号码" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.PhoneNumber }}</template>
          </el-table-column>
          <!-- 起始地点 -->
          <el-table-column label="起始地点" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.From }}</template>
          </el-table-column>
          <!-- 终止地点 -->
          <el-table-column label="终止地点" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.To }}</template>
          </el-table-column>
          <!-- 发货时间 -->
          <el-table-column label="发货时间" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.DelTime }}</template>
          </el-table-column>
          <!-- 物流状态 -->
          <el-table-column label="物流状态" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.TraceStatus }}</template>
          </el-table-column>
          <!-- 签收时间 -->
          <el-table-column label="签收时间" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.ReceiptTime }}</template>
          </el-table-column>
          <!-- 类型 -->
          <el-table-column label="类型" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.CarryBillNoType }}</template>
          </el-table-column>
        </el-table>
        <el-descriptions border :column="3" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 3 - 7em - 22px)' }" v-show="activeName === 'sixth'">
          <!-- 创建人 -->
          <el-descriptions-item label="创建人">{{ info.CreatorId && info.CreatorId.Name }}</el-descriptions-item>
          <!-- 关闭状态 -->
          <el-descriptions-item label="关闭状态">{{ info.CloseStatus && getOptionLabel(CloseStatusOptions, info.CloseStatus) }}</el-descriptions-item>
          <!-- 版本号 -->
          <el-descriptions-item label="版本号">{{ info.VersionNo }}</el-descriptions-item>
          <!-- 创建日期 -->
          <el-descriptions-item label="创建日期">{{ info.CreateDate && parseTime(info.CreateDate, '{y}-{m}-{d}') }}</el-descriptions-item>
          <!-- 关闭人 -->
          <el-descriptions-item label="关闭人">{{ info.CloserId && info.CloserId.Name }}</el-descriptions-item>
          <!-- 变更人 -->
          <el-descriptions-item label="变更人">{{ info.ChangerId && info.ChangerId.Name }}</el-descriptions-item>
          <!-- 最后修改人 -->
          <el-descriptions-item label="最后修改人">{{ info.ModifierId && info.ModifierId.Name }}</el-descriptions-item>
          <!-- 关闭日期 -->
          <el-descriptions-item label="关闭日期">{{ info.CloseDate && parseTime(info.CloseDate, '{y}-{m}-{d}') }}</el-descriptions-item>
          <!-- 变更日期 -->
          <el-descriptions-item label="变更日期">{{ info.ChangeDate && parseTime(info.ChangeDate, '{y}-{m}-{d}') }}</el-descriptions-item>
          <!-- 最后修改日期 -->
          <el-descriptions-item label="最后修改日期">{{ info.MOdifyDate && parseTime(info.MOdifyDate, '{y}-{m}-{d}') }}</el-descriptions-item>
          <!-- 作废状态 -->
          <el-descriptions-item label="作废状态">{{ info.CancelStatus && getOptionLabel(CancelStatusOptions, info.CancelStatus) }}</el-descriptions-item>
          <!-- 签收状态 -->
          <el-descriptions-item label="签收状态">{{ info.SignStatus && getOptionLabel(SignStatusOptions, info.SignStatus) }}</el-descriptions-item>
          <!-- 审核人 -->
          <el-descriptions-item label="审核人">{{ info.ApproverId && info.ApproverId.Name }}</el-descriptions-item>
          <!-- 作废人 -->
          <el-descriptions-item label="作废人">{{ info.CancellerId && info.CancellerId.Name }}</el-descriptions-item>
          <!-- 是否手工关闭 -->
          <el-descriptions-item label="是否手工关闭">
            <el-checkbox v-model="info.ManualClose" disabled></el-checkbox>
          </el-descriptions-item>
          <!-- 审核日期 -->
          <el-descriptions-item label="审核日期">{{ info.ApproveDate && parseTime(info.ApproveDate, '{y}-{m}-{d}') }}</el-descriptions-item>
          <!-- 作废日期 -->
          <el-descriptions-item label="作废日期">{{ info.CancelDate && parseTime(info.CancelDate, '{y}-{m}-{d}') }}</el-descriptions-item>
          <!-- 关闭原因 -->
          <el-descriptions-item label="关闭原因">{{ info.CloseReason }}</el-descriptions-item>
        </el-descriptions>
        <!-- 明细信息 -->
        <el-tabs v-model="detailActiveName" type="card" @tab-click="handleDetailClick" class="custom-tabs">
          <el-tab-pane label="明细信息" name="Dfirst"></el-tab-pane>
          <el-tab-pane label="物料数据" name="Dsecond"></el-tab-pane>
          <el-tab-pane label="交货明细" name="Dthird"></el-tab-pane>
          <el-tab-pane label="明细财务信息" name="Dfourth"></el-tab-pane>
          <el-tab-pane label="收款计划" name="Dfifth"></el-tab-pane>
          <el-tab-pane label="其他信息" name="Dsixth"></el-tab-pane>
          <el-tab-pane label="收款执行明细" name="Dseventh"></el-tab-pane>
        </el-tabs>
        <el-table ref="detailTable" :data="info.SaleOrderEntry" class="custom-table" highlight-current-row @current-change="handleCurrentChange" :setCurrentRow="setCurrentRow" v-show="detailActiveName === 'Dfirst'" show-summary :summary-method="getSummary">
          <!-- 序号 -->
          <el-table-column type="index" label="序号" align="center"></el-table-column>
          <!-- 物料编码 -->
          <el-table-column label="物料编码" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.MaterialId && scope.row.MaterialId.Number }}</template>
          </el-table-column>
          <!-- 物料名称 -->
          <el-table-column label="物料名称" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.MaterialId && getString(scope.row.MaterialId.Name) }}</template>
          </el-table-column>
          <!-- 规格型号 -->
          <el-table-column label="规格型号" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.MaterialId && getString(scope.row.MaterialId.Specification) }}</template>
          </el-table-column>
          <!-- 销售单位 -->
          <el-table-column label="销售单位" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.UnitId && getString(scope.row.UnitId.Name) }}</template>
          </el-table-column>
          <!-- 销售数量 -->
          <el-table-column label="销售数量" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.Qty }}</template>
          </el-table-column>
          <!-- 计价单位 -->
          <el-table-column label="计价单位" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.PriceUnitId && getString(scope.row.PriceUnitId.Name) }}</template>
          </el-table-column>
          <!-- 计价数量 -->
          <el-table-column label="计价数量" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.PriceUnitQty }}</template>
          </el-table-column>
          <!-- 单价 -->
          <el-table-column label="单价" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.Price }}</template>
          </el-table-column>
          <!-- 含税单价 -->
          <el-table-column label="含税单价" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.TaxPrice }}</template>
          </el-table-column>
          <!-- 是否赠品 -->
          <el-table-column label="是否赠品" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
              <el-checkbox v-model="scope.row.IsFree" disabled></el-checkbox>
            </template>
          </el-table-column>
          <!-- 税率% -->
          <el-table-column label="税率%" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.TaxRate || '' }}</template>
          </el-table-column>
          <!-- 税额 -->
          <el-table-column label="税额" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.TaxAmount || '' }}</template>
          </el-table-column>
          <!-- 金额 -->
          <el-table-column prop="Amount" label="金额" align="center" show-overflow-tooltip min-width="120">
            <template slot-scope="scope">{{ scope.row.Amount || '' }}</template>
          </el-table-column>
          <!-- 价税合计 -->
          <el-table-column label="价税合计" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.AllAmount }}</template>
          </el-table-column>
          <!-- 要货日期 -->
          <el-table-column label="要货日期" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.DeliveryDate && parseTime(scope.row.DeliveryDate, '{y}-{m}-{d}') }}</template>
          </el-table-column>
          <!-- 库存组织 -->
          <el-table-column label="库存组织" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.StockOrgId && getString(scope.row.StockOrgId.Name) }}</template>
          </el-table-column>
          <!-- 结算组织 -->
          <el-table-column label="结算组织" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.SettleOrgId && getString(scope.row.SettleOrgId.Name) }}</template>
          </el-table-column>
          <!-- 货主类型 -->
          <el-table-column label="货主类型" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.OwnerTypeId && getOptionLabel(OwnerTypeOptions, scope.row.OwnerTypeId) }}</template>
          </el-table-column>
          <!-- 货主 -->
          <el-table-column label="货主" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.OwnerId && getString(scope.row.OwnerId.Name) }}</template>
          </el-table-column>
          <!-- 备注 -->
          <el-table-column label="备注" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.Note }}</template>
          </el-table-column>
          <!-- 结算单价 -->
          <el-table-column prop="F_SCMJ_JSPrice" label="结算单价" align="center" min-width="120">
            <template slot-scope="scope">{{ scope.row.F_SCMJ_JSPrice }}</template>
          </el-table-column>
        </el-table>
        <el-descriptions border :column="3" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 3 - 7em - 22px)' }" v-show="detailActiveName === 'Dsecond'">
          <!-- 物料编码 -->
          <el-descriptions-item label="物料编码">{{ setCurrentRow && setCurrentRow.MaterialId && setCurrentRow.MaterialId.Number }}</el-descriptions-item>
          <!-- 物料名称 -->
          <el-descriptions-item label="物料名称">{{ setCurrentRow && setCurrentRow.MaterialId && getString(setCurrentRow.MaterialId.Name) }}</el-descriptions-item>
          <!-- 供应组织 -->
          <el-descriptions-item label="供应组织">{{ setCurrentRow && setCurrentRow.SupplyOrgId && getString(setCurrentRow.SupplyOrgId.Name) }}</el-descriptions-item>
          <!-- 仓库 -->
          <el-descriptions-item label="仓库">{{ setCurrentRow && setCurrentRow.SOStockId && getString(setCurrentRow.SOStockId.Name) }}</el-descriptions-item>
          <!-- 仓位 -->
          <el-descriptions-item label="仓位">{{ setCurrentRow && setCurrentRow.SOStockLocalId && getString(setCurrentRow.SOStockLocalId.Name) }}</el-descriptions-item>
          <!-- 销售单位 -->
          <el-descriptions-item label="销售单位">{{ setCurrentRow && setCurrentRow.UnitId && getString(setCurrentRow.UnitId.Name) }}</el-descriptions-item>
          <!-- 销售数量 -->
          <el-descriptions-item label="销售数量">{{ setCurrentRow && setCurrentRow.Qty }}</el-descriptions-item>
          <!-- 库存单位 -->
          <el-descriptions-item label="库存单位">{{ setCurrentRow && setCurrentRow.UnitId && getString(setCurrentRow.UnitId.Name) }}</el-descriptions-item>
          <!-- 库存数量 -->
          <el-descriptions-item label="库存数量">{{ setCurrentRow && setCurrentRow.StockQty }}</el-descriptions-item>
          <!-- BOM版本 -->
          <el-descriptions-item label="BOM版本">{{ setCurrentRow && setCurrentRow.BomId && getString(setCurrentRow.BomId.Name) }}</el-descriptions-item>
          <!-- 计划跟踪号 -->
          <el-descriptions-item label="计划跟踪号">{{ setCurrentRow && setCurrentRow.MtoNo }}</el-descriptions-item>
          <!-- 预留类型 -->
          <el-descriptions-item label="预留类型">{{ setCurrentRow && setCurrentRow.ReserveType && getOptionLabel(ReserveTypeOptions, setCurrentRow.ReserveType) }}</el-descriptions-item>
          <!-- 需求优先级 -->
          <el-descriptions-item label="需求优先级">{{ (setCurrentRow && setCurrentRow.Priority) || '' }}</el-descriptions-item>
          <!-- 货主类型 -->
          <el-descriptions-item label="货主类型">{{ setCurrentRow && setCurrentRow.OwnerTypeId && getOptionLabel(OwnerTypeOptions, setCurrentRow.OwnerTypeId) }}</el-descriptions-item>
          <!-- 货主 -->
          <el-descriptions-item label="货主">{{ setCurrentRow && setCurrentRow.OwnerId && getString(setCurrentRow.OwnerId.Name) }}</el-descriptions-item>
          <!-- 已预留 -->
          <el-descriptions-item label="已预留">
            <el-checkbox v-model="isReserved" disabled></el-checkbox>
          </el-descriptions-item>
          <!-- 已计划运算 -->
          <el-descriptions-item label="已计划运算">
            <el-checkbox v-model="isPlanCalculate" disabled></el-checkbox>
          </el-descriptions-item>
          <!-- 批号 -->
          <el-descriptions-item label="批号">{{ setCurrentRow && setCurrentRow.Lot && setCurrentRow.Lot.Number }}</el-descriptions-item>
          <!-- 保质期单位 -->
          <el-descriptions-item label="保质期单位">{{ setCurrentRow && setCurrentRow.ExpUnit }}</el-descriptions-item>
          <!-- 保质期 -->
          <el-descriptions-item label="保质期">{{ (setCurrentRow && setCurrentRow.ExpPeriod) || '' }}</el-descriptions-item>
          <!-- 生产日期 -->
          <el-descriptions-item label="生产日期">{{ setCurrentRow && setCurrentRow.ProduceDate && parseTime(setCurrentRow.ProduceDate, '{y}-{m}-{d}') }}</el-descriptions-item>
          <!-- 有效期至 -->
          <el-descriptions-item label="有效期至">{{ setCurrentRow && setCurrentRow.ExpiryDate && parseTime(setCurrentRow.ExpiryDate, '{y}-{m}-{d}') }}</el-descriptions-item>
        </el-descriptions>
        <div v-show="detailActiveName === 'Dthird'">
          <el-descriptions border :column="3" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 3 - 7em - 22px)' }">
            <!-- 物料编码 -->
            <el-descriptions-item label="物料编码">{{ setCurrentRow && setCurrentRow.MaterialId && setCurrentRow.MaterialId.Number }}</el-descriptions-item>
            <!-- 物料名称 -->
            <el-descriptions-item label="物料名称">{{ setCurrentRow && setCurrentRow.MaterialId && getString(setCurrentRow.MaterialId.Name) }}</el-descriptions-item>
            <!-- 库存组织 -->
            <el-descriptions-item label="库存组织">{{ setCurrentRow && setCurrentRow.StockOrgId && getString(setCurrentRow.StockOrgId.Name) }}</el-descriptions-item>
            <!-- 控制发货数量 -->
            <el-descriptions-item label="控制发货数量">
              <el-checkbox v-model="isDeliveryControl" disabled></el-checkbox>
            </el-descriptions-item>
            <!-- 发货上限 -->
            <el-descriptions-item label="发货上限">{{ setCurrentRow && setCurrentRow.DeliveryMaxQty }}</el-descriptions-item>
            <!-- 发货下限 -->
            <el-descriptions-item label="发货下限">{{ setCurrentRow && setCurrentRow.DeliveryMinQty }}</el-descriptions-item>
            <!-- 超发控制单位类型 -->
            <el-descriptions-item label="超发控制单位类型">{{ setCurrentRow && setCurrentRow.OUTLMTUNIT && getOptionLabel(UnitTypeOptions, setCurrentRow.OUTLMTUNIT) }}</el-descriptions-item>
            <!-- 超发控制单位 -->
            <el-descriptions-item label="超发控制单位">{{ setCurrentRow && setCurrentRow.OutLmtUnitID && getString(setCurrentRow.OutLmtUnitID.Name) }}</el-descriptions-item>
          </el-descriptions>
          <el-table ref="deliveryTable" :data="setCurrentRow && setCurrentRow.OrderEntryPlan" class="custom-table" highlight-current-row style="margin-top: 15px">
            <!-- 序号 -->
            <el-table-column type="index" label="序号" align="center"></el-table-column>
            <!-- 交货地点 -->
            <el-table-column label="交货地点" align="center" show-overflow-tooltip>
              <template slot-scope="scope">{{ scope.row.DetailLocId && getString(scope.row.DetailLocId.Name) }}</template>
            </el-table-column>
            <!-- 交货地址 -->
            <el-table-column label="交货地址" align="center" show-overflow-tooltip>
              <template slot-scope="scope">{{ scope.row.DetailLocAddress }}</template>
            </el-table-column>
            <!-- 要货日期 -->
            <el-table-column label="要货日期" align="center" show-overflow-tooltip>
              <template slot-scope="scope">{{ scope.row.PlanDate && parseTime(scope.row.PlanDate, '{y}-{m}-{d} {h}:{i}:{s}') }}</template>
            </el-table-column>
            <!-- 计划发货日期 -->
            <el-table-column label="计划发货日期" align="center" show-overflow-tooltip>
              <template slot-scope="scope">{{ scope.row.PlanDeliveryDate && parseTime(scope.row.PlanDeliveryDate, '{y}-{m}-{d} {h}:{i}:{s}') }}</template>
            </el-table-column>
            <!-- 运输提前期 -->
            <el-table-column label="运输提前期" align="center" show-overflow-tooltip>
              <template slot-scope="scope">{{ scope.row.TransportLeadTime || '' }}</template>
            </el-table-column>
            <!-- 销售单位 -->
            <el-table-column label="销售单位" align="center" show-overflow-tooltip>
              <template slot-scope="scope">{{ scope.row.PlanUnitId && getString(scope.row.PlanUnitId.Name) }}</template>
            </el-table-column>
            <!-- 数量 -->
            <el-table-column label="数量" align="center" show-overflow-tooltip>
              <template slot-scope="scope">{{ scope.row.PlanQty }}</template>
            </el-table-column>
          </el-table>
        </div>
        <el-descriptions border :column="3" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 3 - 7em - 22px)' }" v-show="detailActiveName === 'Dfourth'">
          <!-- 物料编码 -->
          <el-descriptions-item label="物料编码">{{ setCurrentRow && setCurrentRow.MaterialId && setCurrentRow.MaterialId.Number }}</el-descriptions-item>
          <!-- 物料名称 -->
          <el-descriptions-item label="物料名称">{{ setCurrentRow && setCurrentRow.MaterialId && getString(setCurrentRow.MaterialId.Name) }}</el-descriptions-item>
          <!-- 结算组织 -->
          <el-descriptions-item label="结算组织">{{ setCurrentRow && setCurrentRow.SettleOrgId && getString(setCurrentRow.SettleOrgId.Name) }}</el-descriptions-item>
          <!-- 是否赠品 -->
          <el-descriptions-item label="是否赠品">
            <el-checkbox v-model="isFree" disabled></el-checkbox>
          </el-descriptions-item>
          <!-- 计价单位 -->
          <el-descriptions-item label="计价单位">{{ setCurrentRow && setCurrentRow.PriceUnitId && getString(setCurrentRow.PriceUnitId.Name) }}</el-descriptions-item>
          <!-- 计价数量 -->
          <el-descriptions-item label="计价数量">{{ setCurrentRow && setCurrentRow.PriceUnitQty }}</el-descriptions-item>
          <!-- 单价 -->
          <el-descriptions-item label="单价">{{ setCurrentRow && setCurrentRow.Price }}</el-descriptions-item>
          <!-- 含税单价 -->
          <el-descriptions-item label="含税单价">{{ setCurrentRow && setCurrentRow.TaxPrice }}</el-descriptions-item>
          <!-- 净价 -->
          <el-descriptions-item label="净价">{{ setCurrentRow && setCurrentRow.TaxNetPrice }}</el-descriptions-item>
          <!-- 折扣率% -->
          <el-descriptions-item label="折扣率%">{{ (setCurrentRow && setCurrentRow.DiscountRate) || '' }}</el-descriptions-item>
          <!-- 单价折扣 -->
          <el-descriptions-item label="单价折扣">{{ (setCurrentRow && setCurrentRow.PriceDiscount) || '' }}</el-descriptions-item>
          <!-- 税率% -->
          <el-descriptions-item label="税率%">{{ (setCurrentRow && setCurrentRow.TaxRate) || '' }}</el-descriptions-item>
          <!-- 税额 -->
          <el-descriptions-item label="税额">{{ (setCurrentRow && setCurrentRow.TaxAmount) || '' }}</el-descriptions-item>
          <!-- 金额 -->
          <el-descriptions-item label="金额">{{ setCurrentRow && setCurrentRow.Amount }}</el-descriptions-item>
          <!-- 价税合计 -->
          <el-descriptions-item label="价税合计">{{ setCurrentRow && setCurrentRow.AllAmount }}</el-descriptions-item>
          <!-- 系统定价 -->
          <el-descriptions-item label="系统定价">{{ (setCurrentRow && setCurrentRow.SysPrice) || '' }}</el-descriptions-item>
          <!-- 价格系数 -->
          <el-descriptions-item label="价格系数">{{ (setCurrentRow && setCurrentRow.PriceCoefficient) || '' }}</el-descriptions-item>
          <!-- 定价单位 -->
          <el-descriptions-item label="定价单位">{{ setCurrentRow && setCurrentRow.SetPriceUnitId && getString(setCurrentRow.SetPriceUnitId.Name) }}</el-descriptions-item>
          <!-- 最低限价 -->
          <el-descriptions-item label="最低限价">{{ (setCurrentRow && setCurrentRow.LimitDownPrice) || '' }}</el-descriptions-item>
          <!-- 行价目表 -->
          <el-descriptions-item label="行价目表">{{ setCurrentRow && setCurrentRow.PriceListEntry }}</el-descriptions-item>
          <!-- 行折扣表 -->
          <el-descriptions-item label="行折扣表">{{ setCurrentRow && setCurrentRow.EntryDiscountList }}</el-descriptions-item>
          <!-- 累计数量取价标识 -->
          <el-descriptions-item label="累计数量取价标识">
            <el-checkbox v-model="isSumQtyTag" disabled></el-checkbox>
          </el-descriptions-item>
          <!-- 促销内容 -->
          <el-descriptions-item label="促销内容">{{ setCurrentRow && setCurrentRow.SPMANDRPMCONTENT }}</el-descriptions-item>
        </el-descriptions>
        <el-table ref="logisticsTable" :data="info.SaleOrderPlan" class="custom-table" highlight-current-row v-show="detailActiveName === 'Dfifth'">
          <!-- 序号 -->
          <el-table-column type="index" label="序号" align="center"></el-table-column>
          <!-- 是否预收 -->
          <el-table-column label="是否预收" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
              <el-checkbox v-model="scope.row.NeedRecAdvance" disabled></el-checkbox>
            </template>
          </el-table-column>
          <!-- 应收比例% -->
          <el-table-column label="应收比例%" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.RecAdvanceRate }}</template>
          </el-table-column>
          <!-- 应收金额 -->
          <el-table-column label="应收金额" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.RecAdvanceAmount }}</template>
          </el-table-column>
          <!-- 到期日 -->
          <el-table-column label="到期日" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.MustDate && parseTime(scope.row.MustDate, '{y}-{m}-{d} {h}:{i}:{s}') }}</template>
          </el-table-column>
          <!-- 关联单号 -->
          <el-table-column label="关联单号" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.RelBillNo }}</template>
          </el-table-column>
          <!-- 实收金额 -->
          <el-table-column label="实收金额" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.RecAmount }}</template>
          </el-table-column>
          <!-- 控制环节 -->
          <el-table-column label="控制环节" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.ControlSend }}</template>
          </el-table-column>
          <!-- 按实际预收控制发货 -->
          <el-table-column label="按实际预收控制发货" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
              <el-checkbox v-model="scope.row.IsOutStockByRecamount" disabled></el-checkbox>
            </template>
          </el-table-column>
          <!-- 预收超收金额 -->
          <el-table-column label="预收超收金额" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.OverRecAmount || '' }}</template>
          </el-table-column>
        </el-table>
        <el-descriptions border :column="3" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 3 - 7em - 22px)' }" v-show="detailActiveName === 'Dsixth'">
          <!-- 物料编码 -->
          <el-descriptions-item label="物料编码">{{ setCurrentRow && setCurrentRow.MaterialId && setCurrentRow.MaterialId.Number }}</el-descriptions-item>
          <!-- 源单类型 -->
          <el-descriptions-item label="源单类型">{{ setCurrentRow && setCurrentRow.SrcType }}</el-descriptions-item>
          <!-- 累计采购申请数量 -->
          <el-descriptions-item label="累计采购申请数量">{{ (setCurrentRow && setCurrentRow.PurReqQty) || '' }}</el-descriptions-item>
          <!-- 物料名称 -->
          <el-descriptions-item label="物料名称">{{ setCurrentRow && setCurrentRow.MaterialId && getString(setCurrentRow.MaterialId.Name) }}</el-descriptions-item>
          <!-- 源单单号 -->
          <el-descriptions-item label="源单单号">{{ setCurrentRow && setCurrentRow.SrcBillNo }}</el-descriptions-item>
          <!-- 累计采购订单数量 -->
          <el-descriptions-item label="累计采购订单数量">{{ (setCurrentRow && setCurrentRow.PurOrderQty) || '' }}</el-descriptions-item>
          <!-- 业务冻结 -->
          <el-descriptions-item label="业务冻结">{{ setCurrentRow && setCurrentRow.MrpFreezeStatus && getOptionLabel(MrpFreezeStatusOptions, setCurrentRow.MrpFreezeStatus) }}</el-descriptions-item>
          <!-- 累计发货通知数量 -->
          <el-descriptions-item label="累计发货通知数量">{{ (setCurrentRow && setCurrentRow.DeliQty) || '' }}</el-descriptions-item>
          <!-- 累计应收数量 -->
          <el-descriptions-item label="累计应收数量">{{ (setCurrentRow && setCurrentRow.ARQTY) || '' }}</el-descriptions-item>
          <!-- 冻结人 -->
          <el-descriptions-item label="冻结人">{{ setCurrentRow && setCurrentRow.FreezerId }}</el-descriptions-item>
          <!-- 累计出库数量 -->
          <el-descriptions-item label="累计出库数量">{{ (setCurrentRow && setCurrentRow.StockOutQty) || '' }}</el-descriptions-item>
          <!-- 累计调拨数量 -->
          <el-descriptions-item label="累计调拨数量">{{ (setCurrentRow && setCurrentRow.TransJoinQty) || '' }}</el-descriptions-item>
          <!-- 冻结日期 -->
          <el-descriptions-item label="冻结日期">{{ setCurrentRow && setCurrentRow.FreezeDate && parseTime(setCurrentRow.FreezeDate, '{y}-{m}-{d}') }}</el-descriptions-item>
          <!-- 累计退货通知数量 -->
          <el-descriptions-item label="累计退货通知数量">{{ (setCurrentRow && setCurrentRow.RetNoticeQty) || '' }}</el-descriptions-item>
          <!-- 累计调拨退货数量 -->
          <el-descriptions-item label="累计调拨退货数量">{{ (setCurrentRow && setCurrentRow.TRANSRETURNQTY) || '' }}</el-descriptions-item>
          <!-- 业务终止 -->
          <el-descriptions-item label="业务终止">{{ setCurrentRow && setCurrentRow.MrpTerminateStatus && getOptionLabel(MrpTerminateStatusOptions, setCurrentRow.MrpTerminateStatus) }}</el-descriptions-item>
          <!-- 累计退货数量 -->
          <el-descriptions-item label="累计退货数量">{{ (setCurrentRow && setCurrentRow.ReturnQty) || '' }}</el-descriptions-item>
          <!-- 寄售结算数量 -->
          <el-descriptions-item label="寄售结算数量">{{ (setCurrentRow && setCurrentRow.CONSIGNSETTQTY) || '' }}</el-descriptions-item>
          <!-- 终止人 -->
          <el-descriptions-item label="终止人">{{ setCurrentRow && setCurrentRow.TerminaterId }}</el-descriptions-item>
          <!-- 剩余未出数量 -->
          <el-descriptions-item label="剩余未出数量">{{ (setCurrentRow && setCurrentRow.RemainOutQty) || '' }}</el-descriptions-item>
          <!-- 变更标志 -->
          <el-descriptions-item label="变更标志">{{ setCurrentRow && setCurrentRow.ChangeFlag }}</el-descriptions-item>
          <!-- 终止日期 -->
          <el-descriptions-item label="终止日期">{{ setCurrentRow && setCurrentRow.TerminateDate && parseTime(setCurrentRow.TerminateDate, '{y}-{m}-{d}') }}</el-descriptions-item>
          <!-- 关联受托材料入库套数 -->
          <el-descriptions-item label="关联受托材料入库套数">{{ (setCurrentRow && setCurrentRow.OEMInStockJoinQty) || '' }}</el-descriptions-item>
          <!-- 业务流程 -->
          <el-descriptions-item label="业务流程">{{ setCurrentRow && setCurrentRow.FBFLowId }}</el-descriptions-item>
          <!-- 业务关闭 -->
          <el-descriptions-item label="业务关闭">{{ setCurrentRow && setCurrentRow.MrpCloseStatus && getOptionLabel(MrpCloseStatusOptions, setCurrentRow.MrpCloseStatus) }}</el-descriptions-item>
          <!-- 是否手工行关闭 -->
          <el-descriptions-item label="是否手工行关闭">
            <el-checkbox v-model="isManualRowClose" disabled></el-checkbox>
          </el-descriptions-item>
        </el-descriptions>
        <el-table ref="executeTable" :data="info.RecMentEntry" class="custom-table" highlight-current-row v-show="detailActiveName === 'Dseventh'">
          <!-- 关联单据 -->
          <el-table-column label="关联单据" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.FORMID }}</template>
          </el-table-column>
          <!-- 关联单据编号 -->
          <el-table-column label="关联单据编号" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.FRELATBILLNO }}</template>
          </el-table-column>
          <!-- 业务日期 -->
          <el-table-column label="业务日期" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.FRELATDATE && parseTime(scope.row.FRELATDATE, '{y}-{m}-{d}') }}</template>
          </el-table-column>
          <!-- 收款金额 -->
          <el-table-column label="收款金额" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.FENTRYRECAMOUNT || '' }}</template>
          </el-table-column>
          <!-- 累计退款金额 -->
          <el-table-column label="累计退款金额" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.FENTRYREFUNDAMT || '' }}</template>
          </el-table-column>
          <!-- 核销记录的单据 -->
          <el-table-column label="核销记录的单据" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.FTYPE }}</template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
    <!-- 下推弹出框 -->
    <el-dialog v-if="pushOpen" v-dialogDragBox title="选择单据" :visible.sync="pushOpen" :width="hasSuccess || isBig ? '90%' : '580px'" class="custom-dialog" :key="dialogKey" :before-close="handlePushCancel">
      <div style="padding: 0 20px">
        <el-form ref="pushForm" :model="pushForm" :rules="pushFormRules" label-width="80px" :disabled="hasSuccess">
          <el-row>
            <el-col :span="hasSuccess || isBig ? 6 : 24">
              <el-form-item label="" label-width="0" prop="target">
                <el-radio-group v-model="pushForm.target" v-removeAriaHidden @change="handleTargetChange" style="width: 100%">
                  <el-row :gutter="10" class="custom-push-target">
                    <el-col :span="12" v-for="item in pushTarget" :key="item.value">
                      <el-radio :label="item.value">{{ item.label }}</el-radio>
                    </el-col>
                  </el-row>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="hasSuccess || isBig ? 6 : 24">
              <el-form-item label="转换规则" prop="ruleId" v-if="isNeedRule()">
                <el-select v-model="pushForm.ruleId" placeholder="请选择转换规则" style="width: 100%" @change="calculateTargetBillType">
                  <el-option v-for="item in calculateRule" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="hasSuccess || isBig ? 6 : 24">
              <el-form-item label="单据类型" prop="targetBillTypeId" v-if="isNeedBillType()">
                <el-select v-model="pushForm.targetBillTypeId" placeholder="请选择单据类型" style="width: 100%">
                  <el-option v-for="item in calculateBillType" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="hasSuccess || isBig ? 6 : 24">
              <el-form-item label="目标组织" prop="targetOrgId" v-if="isNeedOrg()">
                <el-select v-model="pushForm.targetOrgId" placeholder="请选择目标组织" style="width: 100%">
                  <el-option v-for="item in ApplicationOrgNumber" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <!-- 引入发货通知单 -->
        <shipping-note-create ref="shippingNoteShow" v-if="hasSuccess" :is-dialog="false" @pushClose="isSuccessPush = true" @delete="pushOpen = false" />
      </div>
      <div slot="footer">
        <template v-if="isSuccessPush">
          <el-button class="custom-dialog-btn primary" :class="{ small: !hasSuccess }" @click="handlePushCancel">关闭</el-button>
        </template>
        <template v-else>
          <el-button class="custom-dialog-btn" :class="{ small: !hasSuccess }" @click="handlePushCancel">取消</el-button>
          <el-button class="custom-dialog-btn primary" :class="{ small: !hasSuccess }" @click="handlePushSubmit">确定</el-button>
        </template>
      </div>
    </el-dialog>
    <!-- 新增采购订单 -->
    <purchase-order-create ref="purchaseOrderCreate" @callBack="callBack" v-if="showAdd" />
    <!-- 修改销售订单 -->
    <sale-order-create ref="saleOrderCreate" @callBack="handleUpdate" v-if="showEdit" />
    <!-- 新增采购申请单 -->
    <inventory-create ref="inventoryCreate" @callBack="callBack" v-if="showInventoryAdd" />
    <!-- 新增发货通知单 -->
    <shipping-note-create ref="shippingNoteCreate" @callBack="callBack" v-if="showShippingNoteAdd" />
    <!-- 新增销售退货单 -->
    <sales-return-create ref="salesReturnCreate" @callBack="callBack" v-if="showSalesReturnAdd" />
  </div>
</template>
<script>
import { getSaleOrderDetail, auditSaleOrder, revokeSaleOrder, deleteSaleOrder, pushDownSaleOrder, submitSaleOrder, unauditSaleOrder } from '@/api/kingdee/purchase/saleOrder'
import { kingdee } from '@/minix'
import PurchaseOrderCreate from '@/views/kingdee/purchase/order/create'
import SaleOrderCreate from './create'
import InventoryCreate from '@/views/kingdee/purchase/inventory/create'
import ShippingNoteCreate from '@/views/kingdee/sell/shippingNote/create'
import SalesReturnCreate from '@/views/kingdee/sell/salesReturn/create'

export default {
  components: { PurchaseOrderCreate, SaleOrderCreate, InventoryCreate, ShippingNoteCreate, SalesReturnCreate },
  mixins: [kingdee],
  data() {
    return {
      billNo: undefined, // 单据编号
      detailOpen: false,
      info: {},
      setCurrentRow: {},
      // 下推
      pushOpen: false,
      pushTarget: [
        { value: 'SAL_DELIVERYNOTICE', label: '发货通知单' },
        { value: 'PUR_PurchaseOrder', label: '采购订单' }
        // { value: 'PUR_Requisition', label: '采购申请单' },
        // { value: 'PRD_MO', label: '生产订单' },
        // { value: 'STK_TRANSFERAPPLY', label: '调拨申请单' },
        // { value: 'SAL_RETURNNOTICE', label: '退货通知单' },
        // { value: 'SUB_SUBREQORDER', label: '委外订单' },
        // { value: 'SAL_OUTSTOCK', label: '销售出库单' },
        // { value: 'SAL_SaleOrder', label: '销售订单' },
        // { value: 'SAL_RETURNSTOCK', label: '销售退货单' },
        // { value: 'STK_TransferDirect', label: '直接调拨单' },
        // { value: 'STK_AssembledApp', label: '组装拆卸单' },
        // { value: 'STK_OEMReceive', label: '受托加工材料收料单' },
        // { value: 'STK_OEMInStock', label: '受托加工材料入库单' }
      ],
      pushForm: {
        number: undefined,
        target: undefined,
        ruleId: undefined, // 转换规则
        targetBillTypeId: undefined, // 目标单据类型内码
        targetOrgId: undefined // 目标组织内码
      },
      pushFormRules: {
        target: [{ required: true, message: '请选择下推单据', trigger: ['blur', 'change'] }],
        ruleId: [{ required: true, message: '请选择转换规则', trigger: ['blur', 'change'] }],
        targetBillTypeId: [{ required: true, message: '请选择单据类型', trigger: ['blur', 'change'] }],
        targetOrgId: [{ required: true, message: '请选择目标组织', trigger: ['blur', 'change'] }]
      },
      activeName: 'first',
      detailActiveName: 'Dfirst',
      // 业务类型
      BusinessTypeOptions: [
        { value: 'NORMAL', label: '普通销售' },
        { value: 'CONSIGNMENT', label: '寄售' },
        { value: 'STRAIGHT', label: '直运销售' },
        { value: 'DRPTRANS', label: '分销调拨' },
        { value: 'DRPSALE', label: '分销购销' },
        { value: 'VMI', label: 'VMI业务' },
        { value: 'RETURNSO', label: '退货订单' }
      ],
      // 关闭状态
      CloseStatusOptions: [
        { value: 'A', label: '正常' },
        { value: 'B', label: '已关闭' }
      ],
      // 作废状态
      CancelStatusOptions: [
        { value: 'A', label: '未作废' },
        { value: 'B', label: '已作废' }
      ],
      // 签收状态
      SignStatusOptions: [
        { value: 'A', label: '未出库' },
        { value: 'B', label: '待签收' },
        { value: 'C', label: '已签收' }
      ],
      // 货主类型
      OwnerTypeOptions: [
        { label: '业务组织', value: 'BD_OwnerOrg' },
        { label: '供应商', value: 'BD_Supplier' },
        { label: '客户', value: 'BD_Customer' }
      ],
      // 预留类型
      ReserveTypeOptions: [
        { label: '强预留', value: '3' },
        { label: '弱预留', value: '1' }
      ],
      // 超发控制单位类型
      UnitTypeOptions: [
        { label: '销售单位', value: 'SAL' },
        { label: '库存单位', value: 'STK' }
      ],
      // 业务冻结
      MrpFreezeStatusOptions: [
        { label: '正常', value: 'A' },
        { label: '业务冻结', value: 'B' }
      ],
      // 业务终止
      MrpTerminateStatusOptions: [
        { label: '正常', value: 'A' },
        { label: '业务终止', value: 'B' }
      ],
      // 业务关闭
      MrpCloseStatusOptions: [
        { label: '未关闭', value: 'A' },
        { label: '业务关闭', value: 'B' }
      ],
      showAdd: false,
      showEdit: false,
      showInventoryAdd: false, // 下推至采购申请单
      showShippingNoteAdd: false, // 下推至发货通知单
      showSalesReturnAdd: false, // 下推至销售退货单
      // 转换规则
      ruleList: [
        // 下推采购订单
        { value: 'SaleOrder-POOrder', label: '销售订单至采购订单', target: 'PUR_PurchaseOrder' },
        { value: 'DRPSaleOrder-DRPPOOrder', label: '分销购销销售订单至分销购销采购订单', target: 'PUR_PurchaseOrder' },
        // 下推采购申请单
        { value: 'SaleOrder-PurRequestion', label: '销售订单至采购申请单', target: 'PUR_Requisition' },
        // 下推至发货通知单
        { value: 'SaleOrder-DeliveryNotice', label: '销售订单至发货通知单', target: 'SAL_DELIVERYNOTICE' },
        { value: '7b0659a1-6cb3-493b-bbde-37ddefdb8e21', label: '工程项目订单至工程发货通知单', target: 'SAL_DELIVERYNOTICE', billType: 'XSDD10_SYS' }
        // 下推至销售退货单
        // { value: 'SaleOrder-SalReturnStock', label: '销售订单至销售退货单', target: 'SAL_RETURNSTOCK' }
      ],
      // 单据类型
      targetBillTypeList: [
        // 下推采购订单
        { value: '83d822ca3e374b4ab01e5dd46a0062bd', label: '标准采购订单', target: 'SaleOrder-POOrder', billType: 'XSDD01_SYS' },
        // { value: '6d01d059713d42a28bb976c90a121142', label: '标准委外订单', target: 'SaleOrder-POOrder',billType:'' },
        { value: 'b8df755fd92b4c2baedef2439c29f793', label: '直运采购订单', target: 'SaleOrder-POOrder', billType: 'XSDD04_SYS' },
        // 下推采购申请单
        { value: '93591469feb54ca2b08eb635f8b79de3', label: '标准采购申请单', target: 'SaleOrder-PurRequestion', billType: 'XSDD01_SYS' },
        { value: 'ba0754db8aa04f8cb3d42d186858ed03', label: '直运采购申请单', target: 'SaleOrder-PurRequestion', billType: 'XSDD04_SYS' },
        // 下推至发货通知单
        { value: '193822715afc48aa9fa6d6beca7700ab', label: '标准发货通知单', target: 'SaleOrder-DeliveryNotice', billType: 'XSDD01_SYS' },
        { value: '193822715afc48aa9fa6d6beca7700ab', label: '标准发货通知单', target: 'SaleOrder-DeliveryNotice', billType: 'XSDD03_SYS' },
        { value: '193822715afc48aa9fa6d6beca7700ab', label: '标准发货通知单', target: 'SaleOrder-DeliveryNotice', billType: 'XSDD05_SYS' },
        { value: '64d73ed23848e5', label: '工程发货通知单', target: '7b0659a1-6cb3-493b-bbde-37ddefdb8e21', billType: 'XSDD10_SYS' }
        // 下推至销售退货单
        // { value: '73383412199a402bb58439509e089077', label: '标准销售退货单', target: 'SaleOrder-SalReturnStock', billType: 'XSDD01_SYS' },
        // { value: '73383412199a402bb58439509e089077', label: '标准销售退货单', target: 'SaleOrder-SalReturnStock', billType: 'XSDD03_SYS' },
        // { value: '73383412199a402bb58439509e089077', label: '标准销售退货单', target: 'SaleOrder-SalReturnStock', billType: 'XSDD05_SYS' }
      ],
      contractInfo: undefined, // 销售合同信息
      hasSuccess: false, // 是否成功
      dialogKey: 0, // 弹出框key
      isSuccessPush: false, // 是否成功
      isBig: false // 是否大单
    }
  },
  computed: {
    // 是否含税
    isIncludedTax() {
      return this.info?.SaleOrderFinance?.[0]?.IsIncludedTax || false
    },
    // 价外税
    isPriceExcludeTax() {
      return this.info?.SaleOrderFinance?.[0]?.IsPriceExcludeTax || false
    },
    // 已预留
    isReserved() {
      return this.setCurrentRow?.FISMRP || false
    },
    // 是否计划运算
    isPlanCalculate() {
      return this.setCurrentRow?.ISMRPCAL || false
    },
    // 是否控制发货数量
    isDeliveryControl() {
      return this.setCurrentRow?.DeliveryControl || false
    },
    // 是否赠品
    isFree() {
      return this.setCurrentRow?.IsFree || false
    },
    // 是否累计数量取价标识
    isSumQtyTag() {
      return this.setCurrentRow?.IsSumQtyTag || false
    },
    // 是否手工行关闭
    isManualRowClose() {
      return this.setCurrentRow?.MANUALROWCLOSE || false
    },
    // 计算规则
    calculateRule() {
      const billType = this.info?.BillTypeId?.Number || ''
      if (billType === 'XSDD10_SYS') return this.ruleList.filter(item => item.target === this.pushForm.target && item.billType === billType)
      return this.ruleList.filter(item => item.target === this.pushForm.target)
    },
    // 计算单据类型
    calculateBillType() {
      const { ruleId } = this.pushForm
      const billType = this.info?.BillTypeId?.Number || ''
      const arr = this.targetBillTypeList.filter(item => item.target === ruleId) || []
      if (ruleId === 'SaleOrder-PurRequestion') return arr
      else return arr.filter(item => item.billType === billType) || []
    }
  },
  methods: {
    // 切换tab
    handleClick(tab, event) {
      this.activeName = tab.name
    },
    handleDetailClick(tab, event) {
      this.detailActiveName = tab.name
    }, // 获取详情
    getInfo(row = {}) {
      if (!row.BillNo) {
        this.$message.error('参数错误，请重新选择')
        return
      }
      getSaleOrderDetail({ billNo: row.BillNo }).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          const { result } = data
          this.activeName = 'first'
          this.detailActiveName = 'Dfirst'
          this.detailOpen = !!result?.result
          this.info = result?.result || {}
          this.billNo = row.BillNo
          this.$nextTick(() => {
            this.setCurrentRow = result?.result?.SaleOrderEntry?.[0] || {}
            if (this.$refs.detailTable) {
              this.$refs.detailTable.setCurrentRow(this.setCurrentRow)
              this.$refs.detailTable.bodyWrapper.scrollLeft = 0
            }
          })
        } else this.$message.error(msg)
      })
    },
    // 获取合计
    getSummary(param) {
      const { columns, data } = param
      const result = columns.map((column, index) => {
        if (index === 0) {
          return '合计'
        }
        if (column.property === 'Amount' || column.property === 'F_SCMJ_JSPrice') {
          return `${this.calculateTotal(data, column.property)}`
        }
        return ''
      })
      return result
    },
    // 计算合计
    calculateTotal(data, key) {
      return data.reduce((total, item) => {
        if (key === 'Amount') {
          return parseFloat((total + (item.Amount || 0)).toFixed(5))
        } else if (key === 'F_SCMJ_JSPrice') {
          return parseFloat((total + (item.Qty * item.F_SCMJ_JSPrice || 0)).toFixed(5))
        }
        return total
      }, 0)
    },
    // 当前行
    handleCurrentChange(val) {
      this.setCurrentRow = val
    },
    beforeClose() {
      this.handleClose()
      this.$emit('update', { billNo: this.info.BillNo, newStatus: this.info.DocumentStatus })
    },
    // 关闭
    handleClose(refresh = false) {
      this.detailOpen = false
      this.$emit('callBack', refresh)
      // 重置下推相关状态
      this.hasSuccess = false
      this.dialogKey = 0
    },
    // 新建采购订单回调
    callBack(flag = false) {
      this.showAdd = false
      this.showInventoryAdd = false
      this.showShippingNoteAdd = false
      this.showSalesReturnAdd = false
      if (flag) this.detailOpen = false
    },
    // 金蝶操作
    // prettier-ignore
    handleKingdeeDo(type) {
      const number = this.info.BillNo
      if (!number) {
        this.$message.error('单据编号为空，无法执行操作')
        return
      }
      // 操作配置映射
      const actionConfig = {
        submit: {
          confirm: '确认要提交该销售订单吗？',
          action: submitSaleOrder,
          success: '提交成功',
          status: 'B'
        },
        audit: {
          confirm: '确认要审核该销售订单吗？',
          action: auditSaleOrder,
          success: '审核成功',
          status: 'C'
        },
        revoke: {
          confirm: '确认要撤销该销售订单吗？',
          action: revokeSaleOrder,
          success: '撤销成功',
          status: 'D'
        },
        unAudit: {
          confirm: '确认要反审核该销售订单吗？',
          action: unauditSaleOrder,
          success: '反审核成功',
          status: 'D'
        },
        delete: {
          confirm: '确认要删除该销售订单吗？',
          action: deleteSaleOrder,
          success: '删除成功',
          status: null,
          afterSuccess: () => this.handleClose(true)
        }
      }
      if (type === 'push') {
        this.initPushForm()
        this.$nextTick(() => {
          if (this.info.BillTypeId && this.info.BillTypeId.Number != 'XSDD04_SYS') {
            this.pushForm.target = 'SAL_DELIVERYNOTICE'
            this.handleTargetChange('SAL_DELIVERYNOTICE')
          } else {
            this.pushForm.target = 'PUR_PurchaseOrder'
            this.handleTargetChange('PUR_PurchaseOrder')
          }
        })
        return
      }
      const config = actionConfig[type]
      if (!config) {
        console.error('未知的操作类型:', type)
        return
      }
      this.executeKingdeeAction(config, number)
    },
    // 执行金蝶操作
    // prettier-ignore
    executeKingdeeAction(config, number) {
      this.$modal.confirm(config.confirm).then(() => {
        config.action({ number }).then(res => {
          const { code, msg } = res
          if (code === 200) {
            this.$message.success(config.success)
            if (config.status) {
              this.$set(this.info, 'DocumentStatus', config.status)
            }
            if (config.afterSuccess) {
              config.afterSuccess()
            }
          } else {
            this.$message.error(msg)
          }
        }).catch(error => {
          console.error(`${config.success}操作失败:`, error)
          this.$message.error(`${config.success}失败，请重试`)
        })
      }).catch(() => { })
    },
    // 去发货执行的下推操作
    handleSendPush(data = {}, contractInfo = {}, isBig = false) {
      this.initPushForm()
      this.isBig = isBig
      this.$nextTick(() => {
        this.info = data
        this.pushForm.number = data.BillNo
        if (data.BillTypeId && data.BillTypeId.Number != 'XSDD04_SYS') {
          this.pushForm.target = 'SAL_DELIVERYNOTICE'
          this.handleTargetChange('SAL_DELIVERYNOTICE')
        } else {
          this.pushForm.target = 'PUR_PurchaseOrder'
          this.handleTargetChange('PUR_PurchaseOrder')
        }
        this.contractInfo = contractInfo
      })
    },
    // 初始化下推表单
    initPushForm() {
      this.pushOpen = true
      this.hasSuccess = false
      this.dialogKey = Date.now() // 使用时间戳确保每次都是新的key
      this.isSuccessPush = false
      this.isBig = false
      this.pushForm = {
        number: this.info.BillNo,
        target: undefined,
        ruleId: undefined,
        targetBillTypeId: undefined,
        targetOrgId: undefined
      }
      this.$nextTick(() => {
        this.resetForm('pushForm')
      })
    },
    // 切换下推单据
    handleTargetChange(val) {
      if (val === 'PUR_PurchaseOrder' || val === 'PUR_Requisition' || val === 'SAL_DELIVERYNOTICE' || val === 'SAL_RETURNSTOCK') {
        this.setupPurchaseOrderPush()
        return
      }
      this.clearPushFormFields()
    },
    // 设置采购订单下推
    setupPurchaseOrderPush() {
      this.pushForm.ruleId = this.calculateRule?.[0]?.value || undefined
      this.pushForm.targetBillTypeId = undefined
      this.pushForm.targetOrgId = this.info?.SaleOrgId_Id + '' || this.ApplicationOrgNumber?.[0]?.value || undefined
      // 计算目标单据类型
      this.$nextTick(() => {
        this.calculateTargetBillType()
      })
    },
    // 清空下推表单字段
    clearPushFormFields() {
      this.pushForm.ruleId = undefined
      this.pushForm.targetBillTypeId = undefined
      this.pushForm.targetOrgId = undefined
    },
    // 计算目标单据类型
    calculateTargetBillType() {
      const { ruleId } = this.pushForm
      const billType = this.info?.BillTypeId?.Number || ''
      const arr = this.targetBillTypeList.filter(item => item.target === ruleId) || []
      let newArr = []
      if (ruleId === 'SaleOrder-PurRequestion') newArr = arr
      else newArr = arr.filter(item => item.billType === billType) || []
      this.pushForm.targetBillTypeId = newArr?.[0]?.value || undefined
    },
    // 判断是否需要转换规则
    isNeedRule() {
      const { target, ruleId } = this.pushForm
      if (target === 'PUR_PurchaseOrder' || target === 'PUR_Requisition' || target === 'SAL_DELIVERYNOTICE') {
        return true
      }
      return false
    },
    // 判断是否需要单据类型
    isNeedBillType() {
      const { target, ruleId } = this.pushForm
      if (target === 'PUR_PurchaseOrder' || target === 'PUR_Requisition' || target === 'SAL_DELIVERYNOTICE') {
        return true
      }
      return false
    },
    // 判断是否需要组织
    isNeedOrg() {
      const { target, ruleId } = this.pushForm
      if (target === 'PUR_PurchaseOrder' || target === 'PUR_Requisition' || target === 'SAL_DELIVERYNOTICE') {
        return true
      }
      return false
    },
    // 下推取消
    handlePushCancel() {
      if (this.hasSuccess) {
        this.$refs.shippingNoteShow.handleClose()
        this.$nextTick(() => {
          this.pushOpen = false
        })
      } else this.pushOpen = false
    },
    // 下推提交
    handlePushSubmit() {
      if (this.hasSuccess) {
        this.$refs.shippingNoteShow.handleSubmit()
      } else {
        const { number } = this.pushForm
        if (!number) return
        this.$refs.pushForm.validate(valid => {
          if (valid) {
            this.executePushDown()
          }
        })
      }
    },
    // 执行下推操作
    // prettier-ignore
    executePushDown() {
      pushDownSaleOrder(this.pushForm).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          this.handlePushSuccess(data)
        } else if (code === 400) {
          this.handlePushError(msg)
        } else {
          this.$message.error(msg)
        }
      }).catch(error => {
        console.error('下推操作失败:', error)
        this.$message.error('下推操作失败，请重试')
      })
    },
    // 处理下推成功
    handlePushSuccess(data) {
      if (this.pushForm.target === 'PUR_PurchaseOrder') {
        this.handlePurchaseOrderPushSuccess(data)
      } else if (this.pushForm.target === 'PUR_Requisition') {
        this.handleInventoryPushSuccess(data)
      } else if (this.pushForm.target === 'SAL_DELIVERYNOTICE') {
        this.handleDeliveryNoticePushSuccess(data)
      } else if (this.pushForm.target === 'SAL_RETURNSTOCK') {
        this.handleSalReturnStockPushSuccess(data)
      } else {
        this.$message.success('下推成功')
        this.pushOpen = false
        this.handleClose(true)
      }
    },
    // 处理采购订单下推成功
    handlePurchaseOrderPushSuccess(data) {
      this.pushOpen = false
      this.showAdd = true
      const { responseStatus } = data
      const fid = responseStatus?.successEntitys?.[0]?.id
      if (!fid) {
        this.$message.error('参数错误，请重试！')
        return
      }
      this.$nextTick(() => {
        this.$refs.purchaseOrderCreate.init(undefined, 'order', { fid, type: 'push' })
      })
    },
    // 处理采购申请单下推成功
    handleInventoryPushSuccess(data) {
      this.pushOpen = false
      this.showInventoryAdd = true
      const { responseStatus } = data
      const fid = responseStatus?.successEntitys?.[0]?.id
      if (!fid) {
        this.$message.error('参数错误，请重试！')
        return
      }
      this.$nextTick(() => {
        this.$refs.inventoryCreate.initPush(fid, 'push')
      })
    },
    // 处理发货通知单下推成功
    handleDeliveryNoticePushSuccess(data) {
      const { responseStatus } = data
      const fid = responseStatus?.successEntitys?.[0]?.id
      if (!fid) {
        this.$message.error('参数错误，请重试！')
        return
      }
      if (this.contractInfo && Object.keys(this.contractInfo).length > 0) {
        this.hasSuccess = true
        this.dialogKey = Date.now() // 使用时间戳确保每次都是新的key
        this.$nextTick(() => {
          this.$refs.shippingNoteShow.initPush(fid, 'push', this.contractInfo)
        })
      } else {
        this.pushOpen = false
        this.showShippingNoteAdd = true
        this.$nextTick(() => {
          this.$refs.shippingNoteCreate.initPush(fid, 'push')
        })
      }
    },
    // 处理销售退货单下推成功
    handleSalReturnStockPushSuccess(data) {
      this.pushOpen = false
      this.showSalReturnStockAdd = true
      const { responseStatus } = data
      const fid = responseStatus?.successEntitys?.[0]?.id
      if (!fid) {
        this.$message.error('参数错误，请重试！')
        return
      }
      this.$nextTick(() => {
        this.$refs.salesReturnCreate.initPush(fid, 'push')
      })
    },
    // 处理下推错误
    handlePushError(msg) {
      this.$alert(msg || '操作失败，请重试或使用金蝶进行操作！', '提示', {
        type: 'info',
        confirmButtonText: '确定',
        callback: () => {
          this.pushOpen = false
        }
      })
    },
    // 修改
    handleEdit() {
      this.showEdit = true
      this.$nextTick(() => {
        this.$refs.saleOrderCreate.handleUpdate(this.info)
      })
    },
    // 修改回调
    handleUpdate(flag) {
      this.showEdit = false
      if (flag) {
        getSaleOrderDetail({ billNo: this.billNo }).then(res => {
          const { code, msg, data } = res
          if (code === 200) {
            const { result } = data
            this.$set(this, 'activeName', 'first')
            this.$set(this, 'detailActiveName', 'Dfirst')
            this.$set(this, 'info', result?.result || {})
            this.$nextTick(() => {
              this.setCurrentRow = result?.result?.SaleOrderEntry?.[0] || {}
              if (this.$refs.detailTable) {
                this.$refs.detailTable.setCurrentRow(this.setCurrentRow)
                this.$refs.detailTable.bodyWrapper.scrollLeft = 0
              }
            })
          } else this.$message.error(msg)
        })
      }
    }
  }
}
</script>
<style scoped lang="scss">
@import '@/assets/styles/custom-new.scss';
::v-deep {
  .el-descriptions__header {
    margin-bottom: 0;
  }
  .el-table.custom-table .el-table__body-wrapper .el-table__row:hover td.el-table__cell {
    background-color: #eaf1fe !important;
  }
}
.customTitle {
  font-size: 16px;
  font-weight: normal;
  line-height: 60px;
  color: $font;
}
.kindeeButton {
  display: flex;
  align-items: center;
}
.custom-push-target {
  .el-col.el-col-12 {
    .el-radio {
      height: 30px;
      display: flex;
      align-items: center;
    }
  }
}
.custom-tabs {
  margin-top: 15px;
  ::v-deep {
    .el-tabs__header {
      .el-tabs__nav {
        border: 0;
      }
      .el-tabs__item {
        background-color: #eef2f8;
        border-radius: 5px 5px 0 0;
        border-left-width: 0;
        border-right-width: 0;
        &.is-active {
          background-color: $blue;
          color: $white;
        }
      }
      .el-tabs__item + .el-tabs__item {
        margin-left: 2px;
      }
    }
  }
}
</style>
