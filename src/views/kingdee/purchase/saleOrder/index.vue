<template>
  <div class="newBox bgcf9 vh-85">
    <!-- 搜索 -->
    <div class="custom-search flex" style="padding-top: 18px" :style="{ justifyContent: showSearch ? 'space-between' : 'flex-end' }">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" @submit.native.prevent>
        <el-form-item label="单据编号" prop="billNo">
          <el-input v-model="queryParams.billNo" placeholder="请输入单据编号" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="物料编码" prop="materialNumber">
          <el-input v-model="queryParams.materialNumber" placeholder="请输入物料编码" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="客户名称名称" prop="customerName">
          <el-input v-model="queryParams.customerName" placeholder="请输入客户名称名称" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="单据状态" prop="documentStatus">
          <el-select v-model="queryParams.documentStatus" placeholder="请选择单据状态" clearable>
            <el-option v-for="item in DocumentStatusOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="销售部门" prop="saleDept">
          <el-select v-model="queryParams.saleDept" placeholder="请选择销售部门" filterable clearable>
            <el-option v-for="item in ApplicationDeptId" :key="item.FNumber" :label="item.FName" :value="item.FNumber" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="small" @click="handleResetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns" isSetitem @updateColumns="updateColumns"></right-toolbar>
    </div>
    <!-- 列表 -->
    <div style="padding: 15px 20px">
      <el-table v-loading="loading" ref="allTable" border :data="list" row-key="id" style="width: 100%" class="custom-table" :span-method="objectSpanMethod">
        <el-table-column align="center" type="index" label="序号" v-if="columns[0].visible"></el-table-column>
        <!-- 日期 -->
        <el-table-column align="center" prop="Date" label="日期" show-overflow-tooltip v-if="columns[1].visible">
          <template slot-scope="{ row }">{{ parseTime(row.Date, '{y}-{m}-{d}') }}</template>
        </el-table-column>
        <!-- 单据类型 -->
        <el-table-column align="center" prop="BillType" label="单据类型" show-overflow-tooltip v-if="columns[2].visible"></el-table-column>
        <!-- 单据编号 -->
        <el-table-column align="center" prop="BillNo" label="单据编号" show-overflow-tooltip v-if="columns[3].visible">
          <template slot-scope="{ row }">
            <span class="table-link" @click="handleDetail(row)">{{ row.BillNo }}</span>
          </template>
        </el-table-column>
        <!-- 单据状态 -->
        <el-table-column align="center" prop="DocumentStatus" label="单据状态" show-overflow-tooltip v-if="columns[4].visible">
          <template slot-scope="{ row }">{{ getDocumentStatusLabel(row.DocumentStatus) }}</template>
        </el-table-column>
        <!-- 客户-->
        <el-table-column align="center" prop="CustName" label="客户" show-overflow-tooltip v-if="columns[5].visible"></el-table-column>
        <!-- 销售部门 -->
        <el-table-column align="center" prop="SaleDeptName" label="销售部门" show-overflow-tooltip v-if="columns[6].visible"></el-table-column>
        <!-- 关闭状态-->
        <el-table-column align="center" prop="CloseStatus" label="关闭状态" show-overflow-tooltip v-if="columns[7].visible">
          <template slot-scope="{ row }">{{ row.CloseStatus === 'A' ? '未关闭' : '已关闭' }}</template>
        </el-table-column>
        <!-- 物料编码-->
        <el-table-column align="center" prop="MaterialNumber" label="物料编码" show-overflow-tooltip v-if="columns[8].visible">
          <template slot-scope="{ row }">
            <span class="table-link" @click="handleMaterialNumber(row.MaterialNumber)">{{ row.MaterialNumber }}</span>
          </template>
        </el-table-column>
        <!-- 物料名称-->
        <el-table-column align="center" prop="MaterialName" label="物料名称" show-overflow-tooltip v-if="columns[9].visible"></el-table-column>
        <!-- 销售单位-->
        <el-table-column align="center" prop="UnitName" label="销售单位" show-overflow-tooltip v-if="columns[10].visible"></el-table-column>
        <!-- 销售数量 -->
        <el-table-column align="center" prop="Qty" label="销售数量" show-overflow-tooltip v-if="columns[11].visible"></el-table-column>
        <!-- 计价单位 -->
        <el-table-column align="center" prop="PriceUnitName" label="计价单位" show-overflow-tooltip v-if="columns[12].visible"></el-table-column>
        <!-- 计价数量 -->
        <el-table-column align="center" prop="Qty" label="计价数量" show-overflow-tooltip v-if="columns[13].visible"></el-table-column>
        <!-- 要货日期 -->
        <el-table-column align="center" prop="DeliveryDate" label="要货日期" show-overflow-tooltip v-if="columns[14].visible">
          <template slot-scope="{ row }">{{ parseTime(row.DeliveryDate) }}</template>
        </el-table-column>
        <!-- 业务关闭 -->
        <el-table-column align="center" prop="MrpCloseStatus" label="业务关闭" show-overflow-tooltip v-if="columns[15].visible">
          <template slot-scope="{ row }">{{ row.MrpCloseStatus === 'A' ? '正常' : '业务关闭' }}</template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="custom-pagination">
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.limit" @pagination="getList" />
      </div>
    </div>
    <!-- 详情 -->
    <sale-order-detail ref="saleOrderDetail" @callBack="handleDetailCallBack" @update="handleUpdate" v-if="showDetail" />
    <!-- 物料详情 -->
    <material-detail ref="materialDetail" v-if="showMaterialDetail" />
  </div>
</template>
<script>
import { getSaleOrderList } from '@/api/kingdee/purchase/saleOrder'
import SaleOrderDetail from '@/views/kingdee/purchase/saleOrder/detail'
import MaterialDetail from '@/views/kingdee/material/detail'
import { kingdee } from '@/minix'

export default {
  name: 'SaleOrder',
  mixins: [kingdee],
  components: { SaleOrderDetail, MaterialDetail },
  data() {
    return {
      // 搜索
      queryParams: {
        pageNum: 1, // 当前页码
        startRow: 0, // 开始行索引
        limit: 10, // 最大行数
        saleDept: undefined, // 销售部门
        billNo: undefined, // 单据编号
        materialNumber: undefined, // 物料编码
        documentStatus: undefined, // 单据状态
        customerName: undefined // 客户名称
      },
      // 列表
      list: [],
      total: 0,
      loading: false,
      showDetail: false, // 详情
      showMaterialDetail: false, // 物料详情
      // 列表显隐
      showSearch: true,
      // 列信息
      columns: [
        { key: 0, label: `序号`, visible: true },
        { key: 1, label: `日期`, visible: true },
        { key: 2, label: `单据类型`, visible: true },
        { key: 3, label: `单据编号`, visible: true },
        { key: 4, label: `单据状态`, visible: true },
        { key: 5, label: `客户`, visible: true },
        { key: 6, label: `销售部门`, visible: true },
        { key: 7, label: `关闭状态`, visible: true },
        { key: 8, label: `物料编码`, visible: true },
        { key: 9, label: `物料名称`, visible: true },
        { key: 10, label: `销售单位`, visible: true },
        { key: 11, label: `销售数量`, visible: true },
        { key: 12, label: `计价单位`, visible: true },
        { key: 13, label: `计价数量`, visible: true },
        { key: 14, label: `要货日期`, visible: true },
        { key: 15, label: `业务关闭`, visible: true }
      ]
    }
  },
  computed: {
    // 获取当前用户ID
    userId() {
      return this.$store.getters.info.userId
    }
  },
  created() {
    const columns = localStorage.getItem(this.userId + '.saleOrderColumns')
    if (columns) this.columns = JSON.parse(columns)
    // 获取列表
    this.getList()
  },
  methods: {
    // 改变显隐列缓存
    updateColumns(data = []) {
      localStorage.setItem(this.userId + '.saleOrderColumns', JSON.stringify(data))
    },
    // 列表
    getList() {
      this.loading = true
      this.queryParams.startRow = Math.max(0, (this.queryParams.pageNum - 1) * this.queryParams.limit)
      const query = { ...this.queryParams }
      delete query.pageNum
      getSaleOrderList(query).then(res => {
        const { code, data, msg } = res
        if (code === 200) {
          const { data: list, total } = data
          this.list = list
          this.total = total
        } else this.$message.error(msg)
        this.loading = false
      })
    },
    // 相同字段合并
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      const visibleColumns = this.columns.filter(col => col.visible)
      const currentColumnKey = visibleColumns[columnIndex]?.key
      if (currentColumnKey >= 1 && currentColumnKey <= 6) {
        const _row = this.calculateSpan(this.list, 'BillNo')[rowIndex]
        const _col = _row > 0 ? 1 : 0
        return {
          rowspan: _row,
          colspan: _col
        }
      }
    },
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 重置搜索
    handleResetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 查看物料
    handleMaterialNumber(number) {
      this.showMaterialDetail = true
      this.$nextTick(() => {
        this.$refs.materialDetail.getInfo(number)
      })
    },
    // 查看详情
    handleDetail(row) {
      this.showDetail = true
      this.$nextTick(() => {
        this.$refs.saleOrderDetail.getInfo(row)
      })
    },
    // 详情回调
    handleDetailCallBack(flag) {
      this.showDetail = false
      if (flag) this.getList()
    },
    // 更新
    handleUpdate(data) {
      const { billNo, newStatus } = data
      const index = this.list.findIndex(item => item.BillNo === billNo)
      if (index !== -1) this.list[index].DocumentStatus = newStatus
    }
  }
}
</script>
<style scoped lang="scss">
@import '@/assets/styles/custom-new.scss';
</style>
