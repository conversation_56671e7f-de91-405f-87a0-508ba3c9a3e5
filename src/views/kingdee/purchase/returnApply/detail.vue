<template>
  <div>
    <el-dialog v-dialogDragBox title="退料申请单详情" :visible.sync="open" width="90%" class="custom-dialog" :before-close="beforeClose">
      <div style="padding: 0 20px">
        <div class="kindeeButton" :key="info.DocumentStatus">
          <el-button type="primary" size="medium" v-if="info.DocumentStatus == 'A' || info.DocumentStatus == 'D'" @click="handleKingdeeDo('submit')">提交</el-button>
          <el-button type="primary" size="medium" disabled v-else>提交</el-button>
          <el-button type="warning" size="medium" v-if="info.DocumentStatus == 'B'" @click="handleKingdeeDo('audit')">审核</el-button>
          <el-button type="warning" size="medium" disabled v-else>审核</el-button>
          <el-button type="info" size="medium" v-if="info.DocumentStatus == 'B'" @click="handleKingdeeDo('revoke')">撤销</el-button>
          <el-button type="info" size="medium" disabled v-else>撤销</el-button>
          <el-button type="danger" size="medium" v-if="info.DocumentStatus == 'A' || info.DocumentStatus == 'D'" @click="handleKingdeeDo('delete')">删除</el-button>
          <el-button type="danger" size="medium" disabled v-else>删除</el-button>
          <el-button type="warning" size="medium" v-if="info.DocumentStatus == 'C'" @click="handleKingdeeDo('unAudit')">反审</el-button>
          <el-button type="warning" size="medium" disabled v-else>反审</el-button>
          <el-button type="success" size="medium" v-if="info.DocumentStatus == 'C'" @click="handleKingdeeDo('push')">下推</el-button>
          <el-button type="success" size="medium" disabled v-else>下推</el-button>
        </div>
        <el-descriptions border :column="4" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 4 - 7em - 22px)' }">
          <template slot="title">
            <div class="customTitle">基本信息</div>
          </template>
          <!-- 单据类型 -->
          <el-descriptions-item label="单据类型">{{ info.FBillTypeID && getString(info.FBillTypeID.Name) }}</el-descriptions-item>
          <!-- 需求组织 -->
          <el-descriptions-item label="需求组织">{{ info.RequireOrgId && getString(info.RequireOrgId.Name) }}</el-descriptions-item>
          <!-- 采购组织 -->
          <el-descriptions-item label="采购组织">{{ info.PURCHASEORGID && getString(info.PURCHASEORGID.Name) }}</el-descriptions-item>
          <!-- 订单销售组织 -->
          <el-descriptions-item label="订单销售组织">{{ info.F_SCMJ_DDXSZZ && getString(info.F_SCMJ_DDXSZZ.Name) }}</el-descriptions-item>
          <!-- 订单销售部门 -->
          <el-descriptions-item label="订单销售部门">{{ info.F_SCMJ_DDXSBM && getString(info.F_SCMJ_DDXSBM.Name) }}</el-descriptions-item>
          <!-- 业务类型 -->
          <el-descriptions-item label="业务类型">{{ info.BusinessType && getOptionLabel(BusinessTypeOptions, info.BusinessType) }}</el-descriptions-item>
          <!-- 退料类型 -->
          <el-descriptions-item label="退料类型">{{ info.RMTYPE && getOptionLabel(ReturnTypeOptions, info.RMTYPE) }}</el-descriptions-item>
          <!-- 结算组织 -->
          <el-descriptions-item label="结算组织">{{ info.PUR_MRAPPFIN && info.PUR_MRAPPFIN[0].SettleOrgId && getString(info.PUR_MRAPPFIN[0].SettleOrgId.Name) }}</el-descriptions-item>
          <!-- 订单销售员 -->
          <el-descriptions-item label="订单销售员">{{ info.F_SCMJ_DDXSY && getString(info.F_SCMJ_DDXSY.Name) }}</el-descriptions-item>
          <!-- 单据编号 -->
          <el-descriptions-item label="单据编号">{{ info.BillNo }}</el-descriptions-item>
          <!-- 退料方式 -->
          <el-descriptions-item label="退料方式">{{ info.RMMODE && getOptionLabel(ReturnModeOptions, info.RMMODE) }}</el-descriptions-item>
          <!-- 供应商 -->
          <el-descriptions-item label="供应商">{{ info.SUPPLIERID && getString(info.SUPPLIERID.Name) }}</el-descriptions-item>
          <!-- 销售订单号 -->
          <el-descriptions-item label="销售订单号">{{ info.F_SCMJ_XSDDH }}</el-descriptions-item>
          <!-- 申请日期 -->
          <el-descriptions-item label="申请日期">{{ parseTime(info.FDate, '{y}-{m}-{d}') }}</el-descriptions-item>
          <!-- 补料方式 -->
          <el-descriptions-item label="补料方式">{{ info.REPLENISHMODE && getOptionLabel(ReplenishModeOptions, info.REPLENISHMODE) }}</el-descriptions-item>
          <!-- 币别 -->
          <el-descriptions-item label="币别">{{ info.PUR_MRAPPFIN && info.PUR_MRAPPFIN[0].LOCALCURRID && getString(info.PUR_MRAPPFIN[0].LOCALCURRID.Name) }}</el-descriptions-item>
          <!-- 备注 -->
          <el-descriptions-item label="备注">{{ info.FRemarks }}</el-descriptions-item>
          <!-- 申请组织 -->
          <el-descriptions-item label="申请组织">{{ info.APPORGID && getString(info.APPORGID.Name) }}</el-descriptions-item>
          <!-- 退料地点 -->
          <el-descriptions-item label="退料地点">{{ info.RMLOC }}</el-descriptions-item>
          <!-- 汇率类型 -->
          <el-descriptions-item label="汇率类型">{{ info.PUR_MRAPPFIN && info.PUR_MRAPPFIN[0].ExchangeTypeId && getString(info.PUR_MRAPPFIN[0].ExchangeTypeId.Name) }}</el-descriptions-item>
          <!-- 申请部门 -->
          <el-descriptions-item label="申请部门">{{ info.APPDEPTID && getString(info.APPDEPTID.Name) }}</el-descriptions-item>
          <!-- 退料原因 -->
          <el-descriptions-item label="退料原因">{{ info.RMREASON }}</el-descriptions-item>
          <!-- 单据状态 -->
          <el-descriptions-item label="单据状态">{{ info.DocumentStatus && getDocumentStatusLabel(info.DocumentStatus) }}</el-descriptions-item>
          <!-- 申请人 -->
          <el-descriptions-item label="申请人">{{ info.FApplicantId && getString(info.FApplicantId.Name) }}</el-descriptions-item>
          <!-- 价目表 -->
          <el-descriptions-item label="价目表">{{ info.PUR_MRAPPFIN && info.PUR_MRAPPFIN[0].PriceListId && getString(info.PUR_MRAPPFIN[0].PriceListId.Name) }}</el-descriptions-item>
        </el-descriptions>
        <div class="customTitle">明细信息</div>
        <el-table ref="detailTable" :data="info.PUR_MRAPPENTRY" class="custom-table" highlight-current-row @current-change="handleCurrentChange" :setCurrentRow="setCurrentRow">
          <!-- 序号 -->
          <el-table-column type="index" label="序号" align="center"></el-table-column>
          <!-- 物料编码 -->
          <el-table-column label="物料编码" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.MATERIALID && scope.row.MATERIALID.Number }}</template>
          </el-table-column>
          <!-- 物料名称 -->
          <el-table-column label="物料名称" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.MATERIALID && getString(scope.row.MATERIALID.Name) }}</template>
          </el-table-column>
          <!-- 规格型号 -->
          <el-table-column label="规格型号" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.MATERIALID && getString(scope.row.MATERIALID.Specification) }}</template>
          </el-table-column>
          <!-- 库存单位 -->
          <el-table-column label="库存单位" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.UNITID && getString(scope.row.UNITID.Name) }}</template>
          </el-table-column>
          <!-- 申请退料数量 -->
          <el-table-column prop="MRAPPQTY" label="申请退料数量" align="center" show-overflow-tooltip></el-table-column>
          <!-- 计价单位 -->
          <el-table-column label="计价单位" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.PRICEUNITID_F && getString(scope.row.PRICEUNITID_F.Name) }}</template>
          </el-table-column>
          <!-- 计价数量 -->
          <el-table-column prop="PRICEQTY_F" label="计价数量" align="center" show-overflow-tooltip></el-table-column>
          <!-- 补料数量 -->
          <el-table-column prop="REPLENISHQTY" label="补料数量" align="center" show-overflow-tooltip></el-table-column>
          <!-- 扣款数量 -->
          <el-table-column prop="KEAPAMTQTY" label="扣款数量" align="center" show-overflow-tooltip></el-table-column>
          <!-- 退料原因 -->
          <el-table-column prop="RMREASON_M" label="退料原因" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.RMREASON_M && getSting(scope.row.RMREASON_M.Name) }}</template>
          </el-table-column>
          <!-- 是否赠品 -->
          <el-table-column prop="GiveAway" label="是否赠品" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
              <el-checkbox v-model="scope.row.GiveAway" disabled></el-checkbox>
            </template>
          </el-table-column>
          <!-- 备注 -->
          <el-table-column prop="NOTE_M" label="备注" align="center" show-overflow-tooltip></el-table-column>
          <!-- 单价 -->
          <el-table-column prop="PRICE_F" label="单价" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.PRICE_F || '' }}</template>
          </el-table-column>
          <!-- 含税单价 -->
          <el-table-column prop="APPROVEPRICE_F" label="含税单价" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.APPROVEPRICE_F || '' }}</template>
          </el-table-column>
          <!-- 退料金额 -->
          <el-table-column prop="AMOUNT_F" label="退料金额" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.AMOUNT_F || '' }}</template>
          </el-table-column>
          <!-- 退料含税金额 -->
          <el-table-column prop="ALLAMOUNT_F" label="退料含税金额" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.ALLAMOUNT_F || '' }}</template>
          </el-table-column>
          <!-- 仓库 -->
          <el-table-column label="仓库" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.StockId && getString(scope.row.StockId.Name) }}</template>
          </el-table-column>
          <!-- 仓位 -->
          <el-table-column label="仓位" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.FSTOCKLOCID && getString(scope.row.FSTOCKLOCID.Name) }}</template>
          </el-table-column>
        </el-table>
        <el-descriptions border :column="3" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 3 - 7em - 22px)' }">
          <template slot="title">
            <div class="customTitle">其他</div>
          </template>
          <!-- 物料编码 -->
          <el-descriptions-item label="物料编码">{{ setCurrentRow && setCurrentRow.MATERIALID && setCurrentRow.MATERIALID.Number }}</el-descriptions-item>
          <!-- 物料名称 -->
          <el-descriptions-item label="物料名称">{{ setCurrentRow && setCurrentRow.MATERIALID && getString(setCurrentRow.MATERIALID.Name) }}</el-descriptions-item>
          <!-- BOM版本 -->
          <el-descriptions-item label="BOM版本">{{ setCurrentRow && setCurrentRow.BOMID && getString(setCurrentRow.BOMID.Name) }}</el-descriptions-item>
          <!-- 辅助属性 -->
          <el-descriptions-item label="辅助属性">{{ setCurrentRow && setCurrentRow.AUXPROPID && getString(setCurrentRow.AUXPROPID.Name) }}</el-descriptions-item>
          <!-- 批号 -->
          <el-descriptions-item label="批号">{{ setCurrentRow && setCurrentRow.FLot_Text }}</el-descriptions-item>
          <!-- 退料关联数量 -->
          <el-descriptions-item label="退料关联数量">{{ setCurrentRow && setCurrentRow.MRJOINQTY }}</el-descriptions-item>
          <!-- 备注 -->
          <el-descriptions-item label="备注">{{ setCurrentRow && setCurrentRow.NOTE_M }}</el-descriptions-item>
          <!-- 源单类型 -->
          <el-descriptions-item label="源单类型">{{ setCurrentRow && setCurrentRow.SRCBILLTYPEID && getOptionLabel(SRCBILLTYPEIDOptions, setCurrentRow.SRCBILLTYPEID) }}</el-descriptions-item>
          <!-- 源单单号 -->
          <el-descriptions-item label="源单单号">{{ setCurrentRow && setCurrentRow.SRCBILLNO }}</el-descriptions-item>
          <!-- 订单单号 -->
          <el-descriptions-item label="订单单号">{{ setCurrentRow && setCurrentRow.ORDERNO }}</el-descriptions-item>
          <!-- 业务流程 -->
          <el-descriptions-item label="业务流程">{{ setCurrentRow && setCurrentRow.FBFLowId && getString(setCurrentRow.FBFLowId.Name) }}</el-descriptions-item>
          <!-- 需求跟踪号 -->
          <el-descriptions-item label="需求跟踪号">{{ setCurrentRow && setCurrentRow.REQTRACENO }}</el-descriptions-item>
          <!-- 计划跟踪号 -->
          <el-descriptions-item label="计划跟踪号">{{ setCurrentRow && setCurrentRow.MtoNo }}</el-descriptions-item>
          <!-- 单价 -->
          <el-descriptions-item label="单价">{{ (setCurrentRow && setCurrentRow.PRICE_F) || '' }}</el-descriptions-item>
          <!-- 税率% -->
          <el-descriptions-item label="税率%">{{ (setCurrentRow && setCurrentRow.TAXRATE) || '' }}</el-descriptions-item>
          <!-- 含税单价 -->
          <el-descriptions-item label="含税单价">{{ (setCurrentRow && setCurrentRow.APPROVEPRICE_F) || '' }}</el-descriptions-item>
          <!-- 折扣率 -->
          <el-descriptions-item label="折扣率">{{ (setCurrentRow && setCurrentRow.DISCOUNTRATE_F) || '' }}</el-descriptions-item>
          <!-- 单价折扣 -->
          <el-descriptions-item label="单价折扣">{{ (setCurrentRow && setCurrentRow.PriceDiscount) || '' }}</el-descriptions-item>
          <!-- 折扣额 -->
          <el-descriptions-item label="折扣额">{{ (setCurrentRow && setCurrentRow.DISCOUNT_F) || '' }}</el-descriptions-item>
          <!-- 退料含税金额 -->
          <el-descriptions-item label="退料含税金额">{{ (setCurrentRow && setCurrentRow.ALLAMOUNT_F) || '' }}</el-descriptions-item>
          <!-- 分录价目表 -->
          <el-descriptions-item label="分录价目表">{{ setCurrentRow && setCurrentRow.ENTRYPRICELISTID && getString(setCurrentRow.ENTRYPRICELISTID.Name) }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
    <!-- 下推弹出框 -->
    <el-dialog v-dialogDragBox title="选择单据" :visible.sync="pushOpen" width="580px" class="custom-dialog">
      <div style="padding: 0 20px">
        <el-form ref="pushForm" :model="pushForm" :rules="pushFormRules" label-width="80px">
          <el-form-item label="" label-width="0" prop="target">
            <el-radio-group v-model="pushForm.target" v-removeAriaHidden @change="handleTargetChange">
              <el-row :gutter="10" class="custom-push-target">
                <el-col :span="12" v-for="item in pushTarget" :key="item.value">
                  <el-radio :label="item.value">{{ item.label }}</el-radio>
                </el-col>
              </el-row>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="转换规则" prop="ruleId">
            <el-select v-model="pushForm.ruleId" placeholder="请选择转换规则" style="width: 100%">
              <el-option v-for="item in calculateRule" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="单据类型" prop="targetBillTypeId">
            <el-select v-model="pushForm.targetBillTypeId" placeholder="请选择单据类型" style="width: 100%">
              <el-option v-for="item in calculateBillType" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn small" @click="pushOpen = false">取消</el-button>
        <el-button class="custom-dialog-btn small primary" @click="handlePushSubmit">确定</el-button>
      </div>
    </el-dialog>
    <!-- 新增采购退料单 -->
    <purchase-return-create ref="purchaseReturnCreate" @callBack="showReturnCreate = false" v-if="showReturnCreate" />
  </div>
</template>
<script>
import { getReturnApplyDetail, auditReturnApply, revokeReturnApply, deleteReturnApply, pushDownReturnApply, submitReturnApply, unauditReturnApply } from '@/api/kingdee/purchase/returnApply'
import { kingdee } from '@/minix'
import purchaseReturnCreate from '@/views/kingdee/purchase/return/create'

export default {
  mixins: [kingdee],
  components: { purchaseReturnCreate },
  data() {
    return {
      open: false,
      info: {},
      setCurrentRow: {}, // 选中的行
      // 业务类型
      BusinessTypeOptions: [
        { label: '标准采购', value: 'CG' },
        { label: '标准委外', value: 'WW' },
        { label: '资产采购', value: 'ZCCG' },
        { label: 'VMI采购', value: 'VMI' }
      ],
      // 退料类型
      ReturnTypeOptions: [
        { label: '检验退料', value: 'A' },
        { label: '库存退料', value: 'B' }
      ],
      // 退料方式
      ReturnModeOptions: [
        { label: '退料补料', value: 'A' },
        { label: '退料并扣款', value: 'B' }
      ],
      // 补料方式
      ReplenishModeOptions: [
        { label: '按源单补料', value: 'A' },
        { label: '创建补料订单', value: 'B' }
      ],
      // 源单类型
      SRCBILLTYPEIDOptions: [{ label: '采购订单', value: 'PUR_PurchaseOrder' }],
      // 下推
      pushOpen: false,
      pushForm: {},
      pushFormRules: {
        target: [{ required: true, message: '请选择下推单据', trigger: 'change' }],
        ruleId: [{ required: true, message: '请选择转换规则', trigger: 'change' }],
        targetBillTypeId: [{ required: true, message: '请选择单据类型', trigger: 'change' }]
      },
      // 下推目标
      pushTarget: [{ value: 'PUR_MRB', label: '采购退料单' }],
      // 转换规则
      pushRuleOptions: [
        // 下推至采购退料单
        { value: 'PUR_MRAPP-PUR_MRB', label: '退料申请单至采购退料单', target: 'PUR_MRB' }
      ],
      // 单据类型
      pushBillTypeAllOptions: [
        // 下推至采购退料单
        { value: '583ed26e77664a9d8346e78aa917ce08', label: '标准退料单', target: 'PUR_MRAPP-PUR_MRB', billType: 'TLSQDD01_SYS' },
        { value: 'd52451a5048a40818d7af332fb181966', label: '零散采购退料单', target: 'PUR_MRAPP-PUR_MRB', billType: 'TLSQDD01_SYS' },
        { value: 'ef17d378e9bc46beb2b216944688d45b', label: '资产退回单', target: 'PUR_MRAPP-PUR_MRB', billType: 'TLSQDD02_SYS' },
        { value: '493b8d8560834013adf6f446a149ceda', label: '委外退料单', target: 'PUR_MRAPP-PUR_MRB', billType: 'TLSQDD03_SYS' },
        { value: '5abdae5da59757', label: '现购退料单', target: 'PUR_MRAPP-PUR_MRB', billType: 'TLSQDD04_SYS' },
        { value: '0023240234df807511e3089d56422b7a', label: 'VMI退料单', target: 'PUR_MRAPP-PUR_MRB', billType: 'TLSQDD05_SYS' }
      ],
      // 新增采购退料单
      showReturnCreate: false
    }
  },
  computed: {
    // 计算规则
    calculateRule() {
      return this.pushRuleOptions.filter(item => item.target === this.pushForm.target)
    },
    // 计算单据类型
    calculateBillType() {
      const { ruleId } = this.pushForm
      const billType = this?.info?.FBillTypeID?.Number || ''
      const arr = this.pushBillTypeAllOptions.filter(item => item.target === ruleId) || []
      return arr.filter(item => item.billType === billType) || []
    }
  },
  methods: {
    // 获取详情
    getInfo(row = {}) {
      this.open = true
      if (!row.BillNo) {
        this.$message.error('参数错误，请重新选择')
        return
      }
      getReturnApplyDetail({ billNo: row.BillNo }).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          const { result } = data
          this.open = !!result?.result
          this.info = result?.result || {}
          this.$nextTick(() => {
            this.setCurrentRow = result?.result?.PUR_MRAPPENTRY?.[0] || {}
            if (this.$refs.detailTable) {
              this.$refs.detailTable.setCurrentRow(this.setCurrentRow)
              this.$refs.detailTable.bodyWrapper.scrollLeft = 0
            }
          })
        } else this.$message.error(msg)
      })
    },
    // 当前行
    handleCurrentChange(val) {
      this.setCurrentRow = val
    },
    beforeClose() {
      this.handleClose()
      this.$emit('update', { billNo: this.info.BillNo, newStatus: this.info.DocumentStatus })
    },
    // 关闭
    handleClose(flag = false) {
      this.open = false
      this.$emit('callBack', flag)
    },
    // 金蝶退料申请单操作
    // prettier-ignore
    handleKingdeeDo(type) {
      const number = this.info.BillNo
      if (!number) return
      switch (type) {
        case 'submit':
          // 提交
          this.$modal.confirm('确认要提交该退料申请单吗？').then(() => {
            submitReturnApply({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('提交成功')
                this.$set(this.info, 'DocumentStatus', 'B')
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'audit':
          // 审核
          this.$modal.confirm('确认要审核该退料申请单吗？').then(() => {
            auditReturnApply({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('审核成功')
                this.$set(this.info, 'DocumentStatus', 'C')
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'revoke':
          // 撤销
          this.$modal.confirm('确认要撤销该退料申请单吗？').then(() => {
            revokeReturnApply({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('撤销成功')
                this.$set(this.info, 'DocumentStatus', 'D')
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'unAudit':
          // 反审核
          this.$modal.confirm('确认要反审核该退料申请单吗？').then(() => {
            unauditReturnApply({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('反审核成功')
                this.$set(this.info, 'DocumentStatus', 'D')
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'delete':
          // 删除
          this.$modal.confirm('确认要删除该退料申请单吗？').then(() => {
            deleteReturnApply({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('删除成功')
                this.handleClose(true)
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'push':
          // 下推
          this.initPushForm()
          this.pushForm.target = 'PUR_MRB'
          this.handleTargetChange('PUR_MRB')
          break
      }
    },
    // 初始化下推表单
    initPushForm() {
      this.pushForm = {
        number: this.info.BillNo,
        target: undefined,
        ruleId: undefined,
        targetBillTypeId: undefined
      }
      this.$nextTick(() => {
        this.resetForm('pushForm')
      })
      this.pushOpen = true
    },
    // 切换下推单据
    handleTargetChange(val) {
      if (val === 'PUR_MRB') {
        this.setupPurchaseOrderPush()
        return
      }
      this.clearPushFormFields()
    },
    // 设置采购订单下推
    setupPurchaseOrderPush() {
      this.pushForm.ruleId = this.calculateRule?.[0]?.value || undefined
      this.pushForm.targetBillTypeId = undefined
      // 计算目标单据类型
      this.$nextTick(() => {
        this.calculateTargetBillType()
      })
    },
    // 清空下推表单字段
    clearPushFormFields() {
      this.pushForm.ruleId = undefined
      this.pushForm.targetBillTypeId = undefined
    },
    // 计算目标单据类型
    calculateTargetBillType() {
      const { ruleId } = this.pushForm
      const billType = this?.info?.FBillTypeID?.Number || ''
      const arr = this.pushBillTypeAllOptions.filter(item => item.target === ruleId) || []
      const newArr = arr.filter(item => item.billType === billType) || []
      this.pushForm.targetBillTypeId = newArr?.[0]?.value || undefined
    },
    // 下推提交
    handlePushSubmit() {
      const { number } = this.pushForm
      if (!number) return
      this.$refs.pushForm.validate(valid => {
        if (valid) {
          this.executePushDown()
        }
      })
    },
    // 执行下推操作
    // prettier-ignore
    executePushDown() {
      pushDownReturnApply(this.pushForm).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          this.handlePushSuccess(data)
        } else if (code === 400) {
          this.handlePushError(msg)
        } else {
          this.$message.error(msg)
        }
      }).catch(error => {
        console.error('下推操作失败:', error)
        this.$message.error('下推操作失败，请重试')
      })
    },
    // 处理下推成功
    handlePushSuccess(data) {
      if (this.pushForm.target === 'PUR_MRB') {
        this.pushOpen = false
        this.showReturnCreate = true
        const { responseStatus } = data
        const fid = responseStatus?.successEntitys?.[0]?.id
        if (!fid) {
          this.$message.error('参数错误，请重试！')
          return
        }
        this.$nextTick(() => {
          this.$refs.purchaseReturnCreate.initPush(fid, 'push')
        })
      } else {
        this.$message.success('下推成功')
        this.pushOpen = false
        this.handleClose(true)
      }
    },
    // 处理下推错误
    handlePushError(msg) {
      this.$alert(msg || '操作失败，请重试或使用金蝶进行操作！', '提示', {
        type: 'info',
        confirmButtonText: '确定',
        callback: () => {
          this.pushOpen = false
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
@import '@/assets/styles/custom-new.scss';
::v-deep {
  .el-descriptions__header {
    margin-bottom: 0;
  }
  .el-table.custom-table .el-table__body-wrapper .el-table__row:hover td.el-table__cell {
    background-color: #eaf1fe !important;
  }
}
.customTitle {
  font-size: 16px;
  font-weight: normal;
  line-height: 60px;
  color: $font;
}
.kindeeButton {
  display: flex;
  align-items: center;
}
</style>
