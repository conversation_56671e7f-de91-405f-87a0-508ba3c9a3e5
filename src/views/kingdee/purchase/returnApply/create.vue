<template>
  <div>
    <el-dialog v-dialogDragBox :title="title" :visible.sync="open" width="90%" :before-close="handleClose" class="custom-dialog">
      <div style="padding: 0 20px">
        <el-form ref="form" :model="form" :rules="rules" label-width="90px">
          <el-row :gutter="10">
            <el-col :span="8">
              <el-form-item label="单据类型" prop="fbillTypeID">
                <el-select v-model="form.fbillTypeID" placeholder="请选择单据类型" @change="handleBillTypeChange" style="width: 100%">
                  <el-option v-for="item in billTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="申请组织" prop="fappOrgId">
                <el-select v-model="form.fappOrgId" placeholder="请选择申请组织" filterable style="width: 100%" @change="handleAppOrgChange">
                  <el-option v-for="item in ApplicationOrgId" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="需求组织" prop="frequireOrgId">
                <el-select v-model="form.frequireOrgId" placeholder="请选择需求组织" filterable style="width: 100%">
                  <el-option v-for="item in ApplicationOrgId" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="采购组织" prop="fpurchaseOrgId">
                <el-select v-model="form.fpurchaseOrgId" placeholder="请选择采购组织" filterable style="width: 100%">
                  <el-option v-for="item in ApplicationOrgId" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="业务类型" prop="fbusinessType">
                <el-select v-model="form.fbusinessType" placeholder="请选择业务类型" disabled style="width: 100%">
                  <el-option v-for="item in billTypeOptions" :key="item.value" :label="item.businessLabel" :value="item.businessType"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="退料类型" prop="frmType">
                <el-select v-model="form.frmType" placeholder="请选择退料类型" disabled style="width: 100%">
                  <el-option label="库存退料" value="B"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="结算组织" prop="fsettleTypeId">
                <el-select v-model="form.fsettleTypeId" placeholder="请选择结算组织" filterable style="width: 100%">
                  <el-option v-for="item in ApplicationOrgId" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="退料方式" prop="frmMode">
                <el-select v-model="form.frmMode" placeholder="请选择退料方式" style="width: 100%">
                  <el-option label="退料补料" value="A"></el-option>
                  <el-option label="退料并扣款" value="B"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="币别" prop="flocalcurrid">
                <el-select v-model="form.flocalcurrid" placeholder="请选择币别" style="width: 100%">
                  <el-option v-for="(item, index) in CurrencyId" :key="index" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="供应商" prop="fsupplierName">
                <supplier-search-select :keyword.sync="form.fsupplierId" :useOrg="useOrg" :showLabel="false" style="width: 100%" isBack @callBack="handleSupplierSearchSelect" :options="[{ FNumber: form.fsupplierId, FName: form.fsupplierName }]" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="申请日期" prop="fdate">
                <el-date-picker v-model="form.fdate" type="date" placeholder="选择日期" value-format="yyyy-MM-dd" style="width: 100%"></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-table :data="form.entities" stripe style="width: 100%" class="custom-table custom-table-cell0">
                <!-- 序号 -->
                <el-table-column align="center" type="index" label="序号" width="50"></el-table-column>
                <!-- 物料编码 -->
                <el-table-column align="center" prop="fmaterialId" label="物料编码" min-width="120">
                  <template slot-scope="scope">
                    <el-tooltip effect="dark" :content="scope.row.fmaterialId" :disabled="!scope.row.fmaterialId">
                      <el-form-item label-width="0" :prop="`entities.${scope.$index}.fmaterialId`" :rules="rules.fmaterialId">
                        <material-search-select ref="materialSearchSelect" :keyword.sync="scope.row.fmaterialId" :useOrg="form.fappOrgId" size="small" :showLabel="false" @callBack="handleMaterialSearchSelect($event, scope.row)" :options="[{ Number: scope.row.fmaterialId, Name: scope.row.Name, Specification: scope.row.Specification }]" />
                      </el-form-item>
                    </el-tooltip>
                  </template>
                </el-table-column>
                <!-- 物料名称 -->
                <el-table-column align="center" prop="Name" label="物料名称" show-overflow-tooltip></el-table-column>
                <!-- 规格型号 -->
                <el-table-column align="center" prop="Specification" label="规格型号" show-overflow-tooltip></el-table-column>
                <!-- 库存单位 -->
                <el-table-column align="center" prop="funitId" label="库存单位">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.funitId`" :rules="rules.funitId">
                      <el-select v-model="scope.row.funitId" style="width: 100%" size="small">
                        <el-option v-for="(item, index) in UnitList" :key="index" :label="item.FName" :value="item.FNumber"></el-option>
                      </el-select>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 申请退料数量 -->
                <el-table-column align="center" prop="fmrAppQty" label="申请退料数量">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.fmrAppQty`" :rules="rules.fmrAppQty">
                      <el-input :controls="false" v-model="scope.row.fmrAppQty" placeholder="请输入" style="width: 100%" size="small" @change="calculateAmount(scope.row)"></el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 计价单位 -->
                <el-table-column align="center" prop="fpriceUnitIdF" label="计价单位">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.fpriceUnitIdF`" :rules="rules.fpriceUnitIdF">
                      <el-select v-model="scope.row.fpriceUnitIdF" style="width: 100%" size="small">
                        <el-option v-for="(item, index) in UnitList" :key="index" :label="item.FName" :value="item.FNumber"></el-option>
                      </el-select>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 仓库 -->
                <el-table-column align="center" prop="fstockName" label="仓库">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.fstockName`" :rules="rules.fstockName">
                      <stock-search-select :keyword.sync="scope.row.fstockId" :useOrg="useOrg" :showLabel="false" style="width: 100%" size="small" isBack @callBack="handleStockSelect($event, scope.row)" />
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 单价 -->
                <el-table-column align="center" prop="fprice" label="单价">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.fprice`" :rules="rules.fprice">
                      <el-input :controls="false" v-model="scope.row.fprice" placeholder="请输入" style="width: 100%" size="small" @change="calculateAmount(scope.row)" :disabled="!scope.row.fmrAppQty">
                        <template slot="prefix">￥</template>
                      </el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 税率 -->
                <el-table-column align="center" prop="fentrytaxrate" label="税率">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.fentrytaxrate`" :rules="rules.fentrytaxrate">
                      <el-input :controls="false" v-model="scope.row.fentrytaxrate" placeholder="请输入" style="width: 100%" size="small" @change="calculateAmount(scope.row)" :disabled="!scope.row.fmrAppQty">
                        <template slot="suffix">%</template>
                      </el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 金额 -->
                <el-table-column align="center" prop="famount" label="金额">
                  <template slot-scope="scope">
                    <span class="table-price">{{ scope.row.famount ? '￥' + scope.row.famount : '-' }}</span>
                  </template>
                </el-table-column>
                <!-- 含税金额 -->
                <el-table-column align="center" prop="fallamount" label="含税金额">
                  <template slot-scope="scope">
                    <span class="table-price">{{ scope.row.fallamount ? '￥' + scope.row.fallamount : '-' }}</span>
                  </template>
                </el-table-column>
                <!-- 备注 -->
                <el-table-column align="center" prop="fnote" label="备注">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.fnote`" :rules="rules.fnote">
                      <el-input v-model="scope.row.fnote" placeholder="请输入" style="width: 100%" size="small"></el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 采购单位 -->
                <el-table-column align="center" prop="fpurUnitName" label="采购单位"></el-table-column>
                <!-- 操作 -->
                <el-table-column align="center" label="操作" width="120">
                  <template slot-scope="scope">
                    <el-button type="text" size="small" class="el-icon-plus" @click="handleAddMaterial">添加</el-button>
                    <el-button v-if="form.entities.length > 1" type="text" size="small" class="el-icon-delete" @click="form.entities.splice(scope.$index, 1)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn" @click="handleCancel()">取 消</el-button>
        <el-button class="custom-dialog-btn primary" @click="handleSubmit">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { kingdee } from '@/minix'
import materialSearchSelect from '@/components/SearchSelect/material'
import { isNumber, isNumberLength } from '@/utils/validate'
import { saveReturnApply, getReturnApplyDetail, deleteReturnApplyV2 } from '@/api/kingdee/purchase/returnApply'
import SupplierSearchSelect from '@/components/SearchSelect/supplier'
import StockSearchSelect from '@/components/SearchSelect/stock'

export default {
  components: { materialSearchSelect, StockSearchSelect, SupplierSearchSelect },
  mixins: [kingdee],
  data() {
    return {
      open: false,
      title: '新增退料申请单',
      form: {},
      rules: {
        fbillTypeID: [{ required: true, message: '请选择单据类型', trigger: 'change' }],
        frequireOrgId: [{ required: true, message: '请选择需求组织', trigger: 'change' }],
        fpurchaseOrgId: [{ required: true, message: '请选择采购组织', trigger: 'change' }],
        fbusinessType: [{ required: true, message: '请选择业务类型', trigger: 'change' }],
        frmType: [{ required: true, message: '请选择退料类型', trigger: 'change' }],
        frmMode: [{ required: true, message: '请选择退料方式', trigger: 'change' }],
        fsettleTypeId: [{ required: true, message: '请选择结算组织', trigger: 'change' }],
        fsupplierName: [{ required: true, message: '请选择供应商', trigger: 'change' }],
        fdate: [{ required: true, message: '请选择申请日期', trigger: 'change' }],
        flocalcurrid: [{ required: true, message: '请选择币别', trigger: 'change' }],
        fappOrgId: [{ required: true, message: '请选择申请组织', trigger: 'change' }],
        fmaterialId: [{ required: true, message: '请选择物料编码', trigger: 'change' }],
        fmrAppQty: [
          { required: true, message: '请输入申请退料数量', trigger: 'blur' },
          { validator: isNumber, message: '请输入正确的退料数量', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: 'blur' }
        ],
        fpriceUnitIdF: [{ required: true, message: '请选择计价单位', trigger: 'change' }],
        funitId: [{ required: true, message: '请选择库存单位', trigger: 'change' }],
        fstockName: [{ required: true, message: '请选择仓库', trigger: 'change' }],
        fprice: [
          { required: true, message: '请输入退料单价', trigger: 'blur' },
          { validator: isNumber, message: '请输入正确的退料单价', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: 'blur' }
        ],
        fentrytaxrate: [
          { validator: isNumber, message: '请输入正确的税率', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: 'blur' }
        ],
        famount: [
          { required: true, message: '请输入退料不含税金额', trigger: 'blur' },
          { validator: isNumber, message: '请输入正确的退料不含税金额', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: 'blur' }
        ],
        fallamount: [
          { required: true, message: '请输入退料含税金额', trigger: 'blur' },
          { validator: isNumber, message: '请输入正确的退料含税金额', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: 'blur' }
        ]
      },
      // 单据类型选项
      billTypeOptions: [
        { label: '标准退料申请', value: 'TLSQDD01_SYS', businessLabel: '标准采购', businessType: 'FBusinessType-CG' },
        { label: '资产退回申请单', value: 'TLSQDD02_SYS', businessLabel: '资产采购', businessType: 'FBusinessType-ZCCG' },
        { label: '委外退料申请单', value: 'TLSQDD03_SYS', businessLabel: '委外采购', businessType: 'FBusinessType-WW' },
        { label: '现购退料申请', value: 'TLSQDD04_SYS', businessLabel: '标准采购', businessType: 'FBusinessType-CG' },
        { label: 'VMI退料申请', value: 'TLSQDD05_SYS', businessLabel: 'VMI采购', businessType: 'FBusinessType-VMI' }
      ],
      useOrg: undefined,
      hasSuccessfully: false // 是否已成功提交
    }
  },
  methods: {
    // 创建空的实体对象
    createEmptyEntity() {
      return {
        fallamount: undefined, // 退料含税金额
        famount: undefined, // 退料不含税金额
        fentryId: undefined, // 明细ID
        fentrytaxrate: undefined, // 明细税率
        fmaterialId: undefined, // 物料ID
        fmrAppQty: undefined, // 申请退料数量
        fnote: undefined, // 备注
        fprice: undefined, // 退料单价
        fpriceUnitIdF: undefined, // 计价单位
        fpurUnitId: undefined, // 采购单位
        funitId: undefined, // 库存单位
        fstockId: undefined // 仓库
      }
    },
    // 计算金额（统一的计算逻辑）
    calculateAmount(row) {
      this.$nextTick(() => {
        const { fprice = 0, fmrAppQty = 0, fentrytaxrate = 0 } = row
        const amount = fprice * fmrAppQty
        const taxAmount = amount * (1 + fentrytaxrate / 100)

        this.$set(row, 'famount', parseFloat(amount.toFixed(5)))
        this.$set(row, 'fallamount', parseFloat(taxAmount.toFixed(5)))
      })
    },
    // 格式化实体数据（用于提交）
    formatEntityForSubmit(item, isEdit = false) {
      const baseEntity = {
        fallamount: item.fallamount,
        famount: item.famount,
        fentrytaxrate: item.fentrytaxrate,
        fmaterialId: item.fmaterialId,
        fmrAppQty: item.fmrAppQty,
        fnote: item.fnote,
        fprice: item.fprice,
        fpriceUnitIdF: item.fpriceUnitIdF,
        fpurUnitId: item.fpurUnitId,
        funitId: item.funitId,
        fstockId: item.fstockId
      }
      // 编辑时需要包含ID
      if (isEdit) {
        baseEntity.fentryId = item.fentryId
      }
      return baseEntity
    },

    reset() {
      this.form = {
        // 明细
        entities: [this.createEmptyEntity()],
        fappOrgId: this.ApplicationOrgId[0]?.value || undefined, // 申请组织
        fbillTypeID: this.billTypeOptions[0]?.value || undefined, // 单据类型
        fbusinessType: this.billTypeOptions[0]?.businessType || undefined, // 业务类型
        fdate: undefined, // 单据日期
        fid: undefined, // 单据ID
        flocalcurrid: this.CurrencyId[0]?.value || undefined, // 币别
        fpurchaseOrgId: this.ApplicationOrgId[0]?.value || undefined, // 采购组织
        frequireOrgId: this.ApplicationOrgId[0]?.value || undefined, // 需求组织
        frmMode: 'A', // 退料方式 A-退料补料 B-退料并扣款
        frmType: 'B', // 退料类型 B-库存退料
        fsettleTypeId: this.ApplicationOrgId[0]?.value || undefined, // 结算组织
        fsubentryid: undefined,
        fsupplierId: undefined, // 供应商
        fsupplierName: undefined // 供应商名称
      }
      this.resetForm('form')
      this.hasSuccessfully = false
    },
    // 初始化
    init(billNo = undefined) {
      if (billNo) {
        this.reset()
        getReturnApplyDetail({ billNo }).then(res => {
          const { code, msg, data } = res
          if (code === 200) {
            const { result } = data
            const info = result?.result || {}
            // 单据ID
            const fid = info?.Id || undefined
            // 申请组织
            const fappOrgId = info?.APPORGID?.Number || undefined
            // 单据类型
            const fbillTypeID = info?.FBillTypeID?.Number || undefined
            // 业务类型
            const fbusinessType = 'FBusinessType-' + info?.BusinessType || undefined
            // 申请日期
            const fdate = (info?.FDate && this.parseTime(info?.FDate, '{y}-{m}-{d}')) || undefined
            // 币别
            const flocalcurrid = info?.PUR_MRAPPFIN?.[0]?.LOCALCURRID?.Number || undefined
            // 采购组织
            const fpurchaseOrgId = info?.PURCHASEORGID?.Number || undefined
            // 需求组织
            const frequireOrgId = info?.RequireOrgId?.Number || undefined
            // 退料方式
            const frmMode = info?.RMMODE || undefined
            // 退料类型
            const frmType = info?.RMTYPE || undefined
            // 结算组织
            const fsettleTypeId = info?.PUR_MRAPPFIN?.[0]?.SettleTypeId?.Number || undefined
            // 供应商
            const fsupplierId = info?.SUPPLIERID?.Number || undefined
            const fsupplierName = info?.SUPPLIERID?.Name?.[0]?.Value || undefined
            // 明细
            const PUR_MRAPPENTRY = info?.PUR_MRAPPENTRY || []
            const entities = PUR_MRAPPENTRY.map(item => ({
              fallamount: item?.ALLAMOUNT_F || undefined,
              famount: item?.AMOUNT_F || undefined,
              fentryId: item?.Id || undefined,
              fentrytaxrate: item?.TAXRATE || undefined,
              fmaterialId: item?.MATERIALID?.Number || undefined,
              fmaterialName: (item?.MATERIALID?.Name && this.getString(item?.MATERIALID?.Name)) || undefined,
              Name: item?.MATERIALID?.Name?.[0]?.Value || undefined,
              Specification: item?.MATERIALID?.Specification?.[0]?.Value || undefined,
              fmrAppQty: item?.MRAPPQTY || undefined,
              fnote: item?.NOTE_M || undefined,
              fprice: item?.PRICE_F || undefined,
              fpriceUnitIdF: item?.PRICEUNITID_F?.Number || undefined,
              fpurUnitId: item?.PurUnitID?.Number || undefined,
              fpurUnitName: item?.PurUnitID?.Name?.[0]?.Value || undefined,
              funitId: item?.UNITID?.Number || undefined,
              fstockId: item?.StockId?.Number || undefined,
              fstockName: item?.StockId?.Name?.[0]?.Value || undefined
            }))
            const fsubentryid = info?.PUR_MRAPPFIN?.[0]?.Id || undefined
            this.form = { fappOrgId, fbillTypeID, fbusinessType, fdate, flocalcurrid, fpurchaseOrgId, frequireOrgId, frmMode, frmType, fsettleTypeId, fsupplierId, fsupplierName, entities, fid, fsubentryid }
            this.title = '编辑退料申请单'
            this.open = true
            this.useOrg = fappOrgId
          } else this.$message.error(msg)
        })
      } else {
        this.reset()
        this.title = '新增退料申请单'
        this.open = true
        this.useOrg = this.ApplicationOrgId[0]?.value
        this.form.fdate = new Date()
      }
    },
    // 申请组织改变
    // prettier-ignore
    handleAppOrgChange(value) {
      this.$set(this, 'useOrg', value)
      this.$set(this.form, 'fappOrgId', value)
      this.$set(this.form, 'frequireOrgId', value)
      this.$set(this.form, 'fpurchaseOrgId', value)
      this.$set(this.form, 'fsettleTypeId', value)
      this.$set(this.form, 'fsupplierName', undefined)
      this.$set(this.form, 'fsupplierId', undefined)
      this.$set(this.form, 'entities', this.form.entities.map(item => ({ ...item, fstockId: undefined, fstockName: undefined })))
    },
    // 单据类型改变
    handleBillTypeChange(value) {
      this.form.fbusinessType = this.billTypeOptions.find(item => item.value === value)?.businessType
    },
    // 选择供应商回调
    handleSupplierSearchSelect(event) {
      this.$set(this.form, 'fsupplierId', event.FNumber)
      this.$set(this.form, 'fsupplierName', event.FName)
    },
    // 选择仓库回调
    handleStockSelect(event, row) {
      this.$set(row, 'fstockId', event.Number)
      this.$set(row, 'fstockName', event.Name)
    },
    // 选择物料回调
    handleMaterialSearchSelect(event, row) {
      const obj = this.UnitList.find(item => item.FName == event.Unit)
      this.$set(row, 'fmaterialId', event.Number)
      this.$set(row, 'fmaterialName', event.Number)
      this.$set(row, 'Name', event.Name)
      this.$set(row, 'Specification', event.Specification)
      this.$set(row, 'funitId', obj?.FNumber || '')
      this.$set(row, 'fpurUnitName', event.Unit)
      this.$set(row, 'fpurUnitId', obj?.FNumber || '')
      this.$set(row, 'fpriceUnitIdF', obj?.FNumber || '')
      const hasEmpty = this.form.entities.filter(item => !item.fmaterialId)
      if (hasEmpty.length == 0) this.form.entities.push(this.createEmptyEntity())
    },
    // 添加物料
    handleAddMaterial() {
      this.form.entities.push(this.createEmptyEntity())
    },
    // 关闭
    handleClose() {
      this.handleCancel()
    },
    // 取消
    async handleCancel(flag = false) {
      if (this.fid && !this.hasSuccessfully) {
        try {
          await deleteReturnApplyV2({ id: this.fid })
          this.fid = undefined
          this.$nextTick(() => {
            this.open = false
            this.$emit('callBack', flag)
          })
        } catch (error) {
          this.$message.error(error.message)
        }
      } else {
        this.open = false
        this.$emit('callBack', flag)
      }
    },
    // 提交
    handleSubmit() {
      if (this.form.entities.length > 1) {
        this.form.entities = this.form.entities.filter(item => item.fmaterialId)
      }
      this.$nextTick(() => {
        this.$refs.form.validate(valid => {
          if (valid) {
            const isEdit = !!this.form.fid
            let data = { ...this.form }
            delete data.fsupplierName
            // 格式化实体数据
            data.entities = data.entities.map(item => this.formatEntityForSubmit(item, isEdit))
            saveReturnApply(data).then(res => {
              const { code, msg } = res
              if (code == 200) {
                this.$message.success(isEdit ? '修改成功' : '提交成功')
                this.hasSuccessfully = true
                this.open = false
                this.$emit('callBack', true)
              } else {
                this.$message.error(msg)
              }
            })
          }
        })
      })
    }
  }
}
</script>
<style scoped lang="scss">
@import '@/assets/styles/custom-new.scss';
::v-deep {
  .custom-table {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    border-bottom: 0;
    margin-bottom: 20px;
    .el-form-item {
      margin-top: 10px !important;
      margin-bottom: 10px !important;
      .el-form-item__error {
        top: 95%;
        padding-top: 0;
      }
    }
  }
}
</style>
