<template>
  <div>
    <el-dialog v-dialogDragBox :title="title" :visible.sync="open" width="90%" :before-close="handleClose" class="custom-dialog">
      <div style="padding: 0 20px">
        <el-form ref="form" :model="form" :rules="rules" label-width="90px">
          <el-row :gutter="10">
            <el-col :span="8">
              <el-form-item label="单据类型" prop="fbillTypeID">
                <el-select v-model="form.fbillTypeID" placeholder="请选择单据类型" @change="handleBillTypeChange" style="width: 100%">
                  <el-option v-for="item in billTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="业务类型" prop="fbusinessType">
                <el-select v-model="form.fbusinessType" placeholder="请选择业务类型" disabled style="width: 100%">
                  <el-option v-for="item in billTypeOptions" :key="item.value" :label="item.businessLabel" :value="item.businessType"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="定价时点" prop="fpriceTimePoint">
                <el-select v-model="form.fpriceTimePoint" placeholder="请选择定价时点" style="width: 100%">
                  <el-option label="系统日期" value="1"></el-option>
                  <el-option label="单据日期" value="2"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="结算币别" prop="fsettleCurrId">
                <el-select v-model="form.fsettleCurrId" placeholder="请选择结算币别" style="width: 100%">
                  <el-option v-for="(item, index) in CurrencyId" :key="index" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="采购组织" prop="fpurchaseOrgId">
                <el-select v-model="form.fpurchaseOrgId" placeholder="请选择采购组织" filterable style="width: 100%" @change="handlePurchaseOrgChange">
                  <el-option v-for="item in ApplicationOrgId" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="申请日期" prop="fdate">
                <el-date-picker v-model="form.fdate" type="date" placeholder="选择日期" value-format="yyyy-MM-dd" style="width: 100%"></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="供应商" prop="fsupplierId">
                <supplier-search-select :useOrg="useOrg" :keyword.sync="form.fsupplierId" style="width: 100%" :showLabel="false" isBack @callBack="handleSupplierSelect($event)" :options="[{ FNumber: form.fsupplierId, FName: form.fsupplierName }]" />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <div style="display: flex; justify-content: flex-end; margin-bottom: 10px"><only-column isSetitem @updateColumns="updateKingdeeColumns" :columns="kingdeeColumns"></only-column></div>
              <el-table :data="form.entities" stripe style="width: 100%" class="custom-table custom-table-cell0" show-summary :summary-method="getSummary">
                <!-- 序号 -->
                <el-table-column align="center" type="index" label="序号" width="50" v-if="kingdeeColumns[0].visible"></el-table-column>
                <!-- 物料编码 -->
                <el-table-column align="center" prop="fmaterialId" label="物料编码" min-width="120" v-if="kingdeeColumns[1].visible">
                  <template slot-scope="scope">
                    <el-tooltip effect="dark" :content="scope.row.fmaterialId" :disabled="!scope.row.fmaterialId">
                      <el-form-item label-width="0" :prop="`entities.${scope.$index}.fmaterialId`" :rules="rules.fmaterialId">
                        <material-search-select ref="materialSearchSelect" :keyword.sync="scope.row.fmaterialId" :useOrg="form.fpurchaseOrgId" size="small" :showLabel="false" @callBack="handleMaterialSearchSelect($event, scope.row)" :options="[{ Number: scope.row.fmaterialId, Name: scope.row.Name, Specification: scope.row.Specification }]" />
                      </el-form-item>
                    </el-tooltip>
                  </template>
                </el-table-column>
                <!-- 物料名称 -->
                <el-table-column align="center" prop="Name" label="物料名称" show-overflow-tooltip v-if="kingdeeColumns[2].visible"></el-table-column>
                <!-- 规格型号 -->
                <el-table-column align="center" prop="Specification" label="规格型号" show-overflow-tooltip v-if="kingdeeColumns[3].visible"></el-table-column>
                <!-- 结算组织 -->
                <el-table-column align="center" prop="fentrySettleOrgId" label="结算组织" v-if="kingdeeColumns[4].visible" min-width="120">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.fentrySettleOrgId`" :rules="rules.fentrySettleOrgId">
                      <el-select v-model="scope.row.fentrySettleOrgId" style="width: 100%" size="small">
                        <el-option v-for="item in ApplicationOrgId" :key="item.value" :label="item.label" :value="item.value"></el-option>
                      </el-select>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 采购单位 -->
                <el-table-column align="center" prop="funitId" label="采购单位" v-if="kingdeeColumns[5].visible">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.funitId`" :rules="rules.funitId">
                      <el-select v-model="scope.row.funitId" style="width: 100%" size="small">
                        <el-option v-for="(item, index) in UnitList" :key="index" :label="item.FName" :value="item.FNumber"></el-option>
                      </el-select>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 采购数量 -->
                <el-table-column align="center" prop="fqty" label="采购数量" v-if="kingdeeColumns[6].visible" min-width="100">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.fqty`" :rules="rules.fqty">
                      <el-input :controls="false" v-model="scope.row.fqty" placeholder="请输入" style="width: 100%" size="small" @change="handleQtyChange(scope.row)"></el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 计价单位 -->
                <el-table-column align="center" prop="fpriceUnitId" label="计价单位" v-if="kingdeeColumns[7].visible">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.fpriceUnitId`" :rules="rules.fpriceUnitId">
                      <el-select v-model="scope.row.fpriceUnitId" style="width: 100%" size="small">
                        <el-option v-for="(item, index) in UnitList" :key="index" :label="item.FName" :value="item.FNumber"></el-option>
                      </el-select>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 计价数量 -->
                <el-table-column align="center" prop="fpriceUnitQty" label="计价数量" v-if="kingdeeColumns[8].visible" min-width="100">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.fpriceUnitQty`" :rules="rules.fpriceUnitQty">
                      <el-input :controls="false" v-model="scope.row.fpriceUnitQty" placeholder="请输入" style="width: 100%" size="small" readonly></el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 库存单位 -->
                <el-table-column align="center" prop="fstockUnitID" label="库存单位" v-if="kingdeeColumns[9].visible">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.fstockUnitID`" :rules="rules.fstockUnitID">
                      <el-select v-model="scope.row.fstockUnitID" style="width: 100%" size="small">
                        <el-option v-for="(item, index) in UnitList" :key="index" :label="item.FName" :value="item.FNumber"></el-option>
                      </el-select>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 单价 -->
                <el-table-column align="center" prop="fprice" label="单价" v-if="kingdeeColumns[10].visible" min-width="120">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.fprice`" :rules="rules.fprice">
                      <el-input :controls="false" v-model="scope.row.fprice" placeholder="请输入" style="width: 100%" size="small" @change="handlePriceChange(scope.row)">
                        <template slot="prefix">￥</template>
                      </el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 税率 -->
                <el-table-column align="center" prop="fentryTaxRate" label="税率" v-if="kingdeeColumns[11].visible" min-width="100">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.fentryTaxRate`" :rules="rules.fentryTaxRate">
                      <el-input :controls="false" v-model="scope.row.fentryTaxRate" placeholder="请输入" style="width: 100%" size="small" @change="handleTaxRateChange(scope.row)">
                        <template slot="suffix">%</template>
                      </el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 含税单价 -->
                <el-table-column align="center" prop="ftaxPrice" label="含税单价" v-if="kingdeeColumns[12].visible" min-width="120">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.ftaxPrice`" :rules="rules.ftaxPrice">
                      <el-input :controls="false" v-model="scope.row.ftaxPrice" placeholder="请输入" style="width: 100%" size="small" @change="handleTaxPriceChange(scope.row)">
                        <template slot="prefix">￥</template>
                      </el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 金额 -->
                <el-table-column align="center" prop="famount" label="金额" v-if="kingdeeColumns[13].visible" min-width="120">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.famount`" :rules="rules.famount">
                      <el-input :controls="false" v-model="scope.row.famount" placeholder="请输入" style="width: 100%" size="small" @change="handleAmountChange(scope.row)">
                        <template slot="prefix">￥</template>
                      </el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 含税金额 -->
                <el-table-column align="center" prop="ftaxAmount" label="含税金额" v-if="kingdeeColumns[14].visible" min-width="120">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.ftaxAmount`" :rules="rules.ftaxAmount">
                      <el-input :controls="false" v-model="scope.row.ftaxAmount" placeholder="请输入" style="width: 100%" size="small" @change="handleTaxAmountChange(scope.row)">
                        <template slot="prefix">￥</template>
                      </el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 交货日期 -->
                <el-table-column align="center" prop="fdeliveryDate" label="交货日期" v-if="kingdeeColumns[15].visible" min-width="120">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.fdeliveryDate`" :rules="rules.fdeliveryDate">
                      <el-date-picker v-model="scope.row.fdeliveryDate" type="datetime" placeholder="选择日期" value-format="yyyy-MM-dd HH:mm:ss" style="width: 100%" size="small"></el-date-picker>
                    </el-form-item>
                  </template>
                </el-table-column>
                <template v-if="form.fbillTypeID === 'CGDD03_SYS'">
                  <!-- 仓库 -->
                  <el-table-column align="center" prop="fstockId" label="仓库" v-if="kingdeeColumns[16].visible" min-width="120">
                    <template slot-scope="scope">
                      <el-form-item label-width="0" :prop="`entities.${scope.$index}.fstockName`" :rules="rules.fstockName">
                        <stock-search-select :keyword.sync="scope.row.fstockId" :useOrg="useOrg" :showLabel="false" style="width: 100%" size="small" isBack @callBack="handleStockSelect($event, scope.row)" />
                      </el-form-item>
                    </template>
                  </el-table-column>
                  <!-- 批号 -->
                  <el-table-column align="center" prop="flot" label="批号" v-if="kingdeeColumns[17].visible" min-width="120">
                    <template slot-scope="scope">
                      <el-form-item label-width="0" :prop="`entities.${scope.$index}.flot`" :rules="rules.flot">
                        <el-input v-model="scope.row.flot" placeholder="请输入批号" style="width: 100%" size="small"></el-input>
                      </el-form-item>
                    </template>
                  </el-table-column>
                  <!-- 辅助数量 -->
                  <el-table-column align="center" prop="fauxQty" label="辅助数量(kg)" v-if="kingdeeColumns[18].visible" min-width="120">
                    <template slot-scope="scope">
                      <el-form-item label-width="0" :prop="`entities.${scope.$index}.fauxQty`" :rules="rules.fauxQty">
                        <el-input :controls="false" v-model="scope.row.fauxQty" placeholder="请输入" style="width: 100%" size="small">
                          <template slot="suffix">kg</template>
                        </el-input>
                      </el-form-item>
                    </template>
                  </el-table-column>
                </template>
                <!-- 操作 -->
                <el-table-column align="center" label="操作" width="120">
                  <template slot-scope="scope">
                    <el-button type="text" size="small" class="el-icon-plus" @click="handleAddMaterial">添加</el-button>
                    <el-button v-if="form.entities.length > 1" type="text" size="small" class="el-icon-delete" @click="form.entities.splice(scope.$index, 1)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn" @click="handleCancel()">取 消</el-button>
        <el-button class="custom-dialog-btn primary" @click="handleSubmit">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 采购订单详情 -->
    <order-detail ref="orderDetail" />
  </div>
</template>
<script>
import { kingdee } from '@/minix'
import { addPurchaseOrder, getPurchaseOrderDetail, getPurchaseOrderDetailByFid, deletePurchaseOrderV2 } from '@/api/kingdee/purchase/order'
import supplierSearchSelect from '@/components/SearchSelect/supplier'
import materialSearchSelect from '@/components/SearchSelect/material'
import { isNumber, isNumberLength } from '@/utils/validate'
import OnlyColumn from '@/components/RightToolbar/onlyColumn'
import stockSearchSelect from '@/components/SearchSelect/stock'
import OrderDetail from './detail'

export default {
  components: { supplierSearchSelect, materialSearchSelect, OnlyColumn, stockSearchSelect, OrderDetail },
  mixins: [kingdee],
  name: 'PurchaseOrderCreate',
  data() {
    return {
      open: false,
      title: '新增采购订单',
      form: {},
      rules: {
        fbillTypeID: [{ required: true, message: '请选择单据类型', trigger: 'change' }],
        fbusinessType: [{ required: true, message: '请选择业务类型', trigger: 'change' }],
        fdate: [{ required: true, message: '请选择采购日期', trigger: 'change' }],
        fpriceTimePoint: [{ required: true, message: '请选择定价时点', trigger: 'change' }],
        fsettleCurrId: [{ required: true, message: '请选择结算币别', trigger: 'change' }],
        fpurchaseOrgId: [{ required: true, message: '请选择采购组织', trigger: 'change' }],
        fsupplierId: [{ required: true, message: '请选择供应商', trigger: 'change' }],
        fmaterialId: [{ required: true, message: '请选择物料编码', trigger: 'change' }],
        funitId: [{ required: true, message: '请选择采购单位', trigger: 'change' }],
        fstockUnitID: [{ required: true, message: '请选择库存单位', trigger: 'change' }],
        fpriceUnitId: [{ required: true, message: '请选择计价单位', trigger: 'change' }],
        fpriceUnitQty: [
          { required: true, message: '请输入计价数量', trigger: 'blur' },
          { validator: isNumber, message: '请输入正确的计价数量', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: 'blur' }
        ],
        fprice: [
          { required: true, message: '请输入单价', trigger: 'blur' },
          { validator: isNumber, message: '请输入正确的单价', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: 'blur' }
        ],
        fentryTaxRate: [
          { validator: isNumber, message: '请输入正确的税率', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: 'blur' }
        ],
        fqty: [
          { required: true, message: '请输入采购数量', trigger: 'blur' },
          { validator: isNumber, message: '请输入正确的采购数量', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: 'blur' }
        ],
        ftaxPrice: [
          { validator: isNumber, message: '请输入正确的含税单价', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: 'blur' }
        ],
        famount: [
          { validator: isNumber, message: '请输入正确的金额', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: 'blur' }
        ],
        ftaxAmount: [
          { validator: isNumber, message: '请输入正确的含税金额', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: 'blur' }
        ],
        fdeliveryDate: [{ required: true, message: '请选择交货日期', trigger: 'change' }]
      },
      // 单据类型选项
      billTypeOptions: [
        { label: '标准采购订单', value: 'CGDD01_SYS', businessLabel: '标准采购', businessType: 'CG' },
        { label: '标准委外订单', value: 'CGDD02_SYS', businessLabel: '标准委外', businessType: 'WW' },
        { label: '直运采购订单', value: 'CGDD03_SYS', businessLabel: '直运采购', businessType: 'ZYCG' },
        { label: '资产采购订单', value: 'CGDD04_SYS', businessLabel: '资产采购', businessType: 'ZCCG' },
        { label: '费用采购订单', value: 'CGDD05_SYS', businessLabel: '费用采购', businessType: 'FYCG' },
        { label: '补料采购订单', value: 'CGDD06_SYS', businessLabel: '标准采购', businessType: 'CG' }
      ],
      useOrg: undefined,
      kingdeeColumns: [
        { key: 0, label: `序号`, visible: true },
        { key: 1, label: `物料编码`, visible: true },
        { key: 2, label: `物料名称`, visible: true },
        { key: 3, label: `规格`, visible: true },
        { key: 4, label: `结算组织`, visible: true },
        { key: 5, label: `采购单位`, visible: true },
        { key: 6, label: `采购数量`, visible: true },
        { key: 7, label: `计价单位`, visible: true },
        { key: 8, label: `计价数量`, visible: true },
        { key: 9, label: `库存单位`, visible: true },
        { key: 10, label: `单价`, visible: true },
        { key: 11, label: `税率`, visible: true },
        { key: 12, label: `含税单价`, visible: true },
        { key: 13, label: `金额`, visible: true },
        { key: 14, label: `含税金额`, visible: true },
        { key: 15, label: `交货日期`, visible: true },
        { key: 16, label: `仓库`, visible: true },
        { key: 17, label: `批号`, visible: true },
        { key: 18, label: `辅助数量`, visible: true }
      ],
      type: undefined, // 类型 order-销售订单下推 push-采购申请单下推
      isPush: false, // 是否下推
      fid: undefined, // 主键
      hasSuccessfully: false // 是否已成功提交
    }
  },
  computed: {
    userId() {
      return this.$store.getters.info.userId
    }
  },
  methods: {
    // 创建默认实体对象
    createDefaultEntity(options = {}) {
      return {
        fdeliveryDate: options.fdeliveryDate || undefined, // 交货日期
        fentryID: options.fentryID || undefined, // 主键
        fentrySettleOrgId: options.fentrySettleOrgId || this.form.fpurchaseOrgId, // 结算组织
        fentryTaxRate: options.fentryTaxRate || 0, // 税率
        fmaterialId: options.fmaterialId || undefined, // 物料编码
        fprice: options.fprice || undefined, // 单价
        fpriceUnitId: options.fpriceUnitId || undefined, // 计价单位
        fpriceUnitQty: options.fpriceUnitQty || undefined, // 计价数量
        fqty: options.fqty || undefined, // 采购数量
        fstockUnitID: options.fstockUnitID || undefined, // 库存单位
        funitId: options.funitId || undefined, // 采购单位
        fmaterialName: options.fmaterialName || undefined, // 物料名称
        Name: options.Name || undefined, // 物料名称
        Specification: options.Specification || undefined, // 规格型号
        ftaxPrice: options.ftaxPrice || undefined, // 含税单价
        famount: options.famount || undefined, // 金额
        ftaxAmount: options.ftaxAmount || undefined, // 含税金额
        fstockName: options.fstockName || undefined, // 仓库
        fstockId: options.fstockId || undefined, // 仓库ID
        flot: options.flot || undefined, // 批号
        fauxQty: options.fauxQty || undefined // 辅助数量
      }
    },
    reset() {
      this.form = {
        entities: [this.createDefaultEntity()],
        fbillTypeID: undefined, // 单据类型 CGDD01_SYS-标准采购订单 CGDD02_SYS-标准委外订单 CGDD03_SYS-直运采购订单 CGDD04_SYS-资产采购订单 CGDD05_SYS-费用采购订单 CGDD06-SYS-补料采购订单
        fbusinessType: undefined, // 业务类型 关联单据类型 CGDD01_SYS-> CG(标准采购) CGDD02_SYS-> WW(标准委外) CGDD03_SYS-> ZYCG(直运采购) CGDD04_SYS->ZCCG(资产采购) CGDD05_SYS-FYCG(费用采购) CGDD06_SYS-> CG(标准采购)
        fdate: undefined, // 采购日期
        fid: undefined, // 主键
        forderFinanceId: undefined, // 财务明细object ID
        fpriceTimePoint: undefined, // 定价时点 1-系统日期 2-单据日期
        fpurchaseOrgId: undefined, // 采购组织
        fsettleCurrId: undefined, // 结算币别
        fsupplierId: undefined, // 供应商
        fsupplierName: undefined // 供应商名称
      }
      this.resetForm('form')
      this.isPush = false
      this.fid = undefined
      this.hasSuccessfully = false
    },
    // 统一的价格计算方法
    calculatePrices(row) {
      const { fqty = 0, fprice = 0, ftaxPrice = 0, fentryTaxRate = 0, famount = 0, ftaxAmount = 0 } = row
      return {
        // 含税单价 = 单价 * (1 + 税率%)
        ftaxPrice: parseFloat((fprice * (1 + fentryTaxRate / 100)).toFixed(5)),
        // 单价 = 含税单价 / (1 + 税率%)
        fprice: parseFloat((ftaxPrice / (1 + fentryTaxRate / 100)).toFixed(5)),
        // 金额 = 单价 * 数量
        famount: parseFloat((fprice * fqty).toFixed(5)),
        // 含税金额 = 含税单价 * 数量
        ftaxAmount: parseFloat((ftaxPrice * fqty).toFixed(5)),
        // 从金额反推单价 = 金额 / 数量
        fpriceFromAmount: parseFloat((famount / fqty).toFixed(5)),
        // 从含税金额反推含税单价 = 含税金额 / 数量
        ftaxPriceFromAmount: parseFloat((ftaxAmount / fqty).toFixed(5))
      }
    },
    // 初始化列设置
    initColumnSettings() {
      const kingdeeColumns = localStorage.getItem(`kingdee_purchase_order_${this.userId}`)
      if (kingdeeColumns) this.kingdeeColumns = JSON.parse(kingdeeColumns)
    },
    // 初始化
    // prettier-ignore
    init(billNo = undefined, type = undefined, obj = undefined) {
      this.type = type
      if (type == 'push') {
        this.initPush(obj)
      } else if (type == 'order') {
        this.initOrder(obj)
      } else {
        if (billNo) {
          const loading = this.$loading({
            lock: true,
            text: '加载中...',
            background: 'rgba(0, 0, 0, 0.7)'
          })
          this.reset()
          getPurchaseOrderDetail({ billNo }).then(res => {
            const { code, msg, data } = res
            if (code === 200) {
              const { result } = data
              const info = result?.result || {}
              // 主键
              const fid = info?.Id || undefined
              // 单据类型
              const fbillTypeID = info?.BillTypeId?.Number || undefined
              // 业务类型
              const fbusinessType = info?.BusinessType || undefined
              // 采购日期
              const fdate = (info?.Date && this.parseTime(info?.Date, '{y}-{m}-{d}')) || undefined
              // 结算币别
              const fsettleCurrId = info?.POOrderFinance?.[0]?.SettleCurrId?.Number || undefined
              // 采购组织
              const fpurchaseOrgId = info?.PurchaseOrgId?.Number || undefined
              // 供应商
              const fsupplierId = info?.SupplierId?.Number || undefined
              // 供应商名称
              const fsupplierName = (info?.SupplierId?.Name && this.getString(info?.SupplierId?.Name)) || undefined
              // 定价时点
              const fpriceTimePoint = info?.POOrderFinance?.[0]?.PriceTimePoint || undefined
              // 财务明细object ID
              const forderFinanceId = info?.POOrderFinance?.[0]?.Id || undefined
              // 明细
              const POOrderEntry = info?.POOrderEntry || []
              const entities = POOrderEntry.map(item => this.mapApiDataToEntity(item))
              this.form = { fid, fbillTypeID, fbusinessType, fdate, fsettleCurrId, fpurchaseOrgId, fsupplierId, fsupplierName, fpriceTimePoint, forderFinanceId, entities }
              this.title = '编辑采购订单'
              this.initColumnSettings()
              this.open = true
              this.useOrg = fpurchaseOrgId
            } else this.$message.error(msg)
          }).finally(() => {
            loading.close()
          })
        } else {
          this.reset()
          this.title = '新增采购订单'
          this.initColumnSettings()
          this.open = true
          this.useOrg = this.ApplicationOrgId[0].value
          this.form.fpriceTimePoint = '1'
          this.form.fsettleCurrId = this.CurrencyId[0].value
          this.form.fdate = new Date()
          this.form.fbillTypeID = this.billTypeOptions[0].value
          this.form.fbusinessType = this.billTypeOptions[0].businessType
        }
      }
    },
    // 采购申请单下推进入
    // prettier-ignore
    initPush(obj = undefined) {
      if (!obj) return this.$message.error('参数错误，请重试！')
      this.reset()
      this.title = '新增采购订单'
      const loading = this.$loading({
        lock: true,
        text: '加载中...',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      const supplierId = obj?.entities?.[0]?.fsuggestSupplierId || undefined
      const entitiesArr = obj?.entities || []
      const entities = entitiesArr.map(item => this.createDefaultEntity({
        fdeliveryDate: item.farrivalDate,
        fentrySettleOrgId: item.frequireOrgId,
        fentryTaxRate: item.ftaxrate,
        fmaterialId: item.fmaterialId,
        fmaterialName: item.fmaterialId,
        Name: item.fmaterialDesc,
        Specification: item.specs,
        fprice: item.fevaluatePrice,
        ftaxPrice: item.ftaxprice,
        famount: parseFloat((item.freqQty * item.ftaxprice).toFixed(5)),
        ftaxAmount: parseFloat((item.freqQty * item.ftaxprice).toFixed(5)),
        fpriceUnitId: item.fpriceUnitId,
        fpriceUnitQty: item.fpriceUnitQty,
        fqty: item.freqQty,
        fstockUnitID: item.freqstockunitid,
        funitId: item.funitId
      }))
      this.form = {
        entities,
        fbillTypeID: undefined, // 单据类型 CGDD01_SYS-标准采购订单 CGDD02_SYS-标准委外订单 CGDD03_SYS-直运采购订单 CGDD04_SYS-资产采购订单 CGDD05_SYS-费用采购订单 CGDD06-SYS-补料采购订单
        fbusinessType: undefined, // 业务类型 关联单据类型 CGDD01_SYS-> CG(标准采购) CGDD02_SYS-> WW(标准委外) CGDD03_SYS-> ZYCG(直运采购) CGDD04_SYS->ZCCG(资产采购) CGDD05_SYS-FYCG(费用采购) CGDD06_SYS-> CG(标准采购)
        fdate: undefined, // 采购日期
        fid: undefined, // 主键
        forderFinanceId: undefined, // 财务明细object ID
        fpriceTimePoint: undefined, // 定价时点 1-系统日期 2-单据日期
        fpurchaseOrgId: obj?.fapplicationOrgId || undefined, // 采购组织
        fsettleCurrId: obj?.fcurrencyId || undefined, // 币别
        fsupplierId: supplierId // 供应商
      }
      this.initColumnSettings()
      this.open = true
      this.useOrg = obj?.fapplicationOrgId || undefined
      loading.close()
    },
    // 销售订单下推进入
    // prettier-ignore
    initOrder(obj = undefined) {
      if (!obj || Object.keys(obj).length === 0) {
        return this.$message.error('参数错误，请重试！')
      }
      const fid = obj?.fid || undefined
      if (!fid) return this.$message.error('参数错误，请重试！')
      this.reset()
      this.fid = fid
      this.isPush = obj?.type == 'push'
      const loading = this.$loading({
        lock: true,
        text: '加载中...',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      getPurchaseOrderDetailByFid({ fid }).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          const { result } = data
          const info = result?.result || {}
          // 主键
          const fid = info?.Id || undefined
          // 单据类型
          const fbillTypeID = info?.BillTypeId?.Number || undefined
          // 业务类型
          const fbusinessType = info?.BusinessType || undefined
          // 采购日期
          const fdate = (info?.Date && this.parseTime(info?.Date, '{y}-{m}-{d}')) || undefined
          // 结算币别
          const fsettleCurrId = info?.POOrderFinance?.[0]?.SettleCurrId?.Number || undefined
          // 采购组织
          const fpurchaseOrgId = info?.PurchaseOrgId?.Number || undefined
          // 供应商
          const fsupplierId = info?.SupplierId?.Number || undefined
          // 供应商名称
          const fsupplierName = (info?.SupplierId?.Name && this.getString(info?.SupplierId?.Name)) || undefined
          // 定价时点
          const fpriceTimePoint = info?.POOrderFinance?.[0]?.PriceTimePoint || undefined
          // 财务明细object ID
          const forderFinanceId = info?.POOrderFinance?.[0]?.Id || undefined
          // 明细
          const POOrderEntry = info?.POOrderEntry || []
          const entities = POOrderEntry.map(item => this.mapApiDataToEntity(item))
          const newRules = {
            fauxQty: [
              { required: true, message: '请输入辅助数量', trigger: 'blur' },
              { validator: isNumber, message: '请输入正确的辅助数量', trigger: 'blur' },
              { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: 'blur' }
            ],
            flot: [{ required: true, message: '请输入批号', trigger: 'blur' }],
            fstockName: [{ required: true, message: '请选择仓库', trigger: 'change' }]
          }
          this.rules = { ...this.rules, ...newRules }
          this.form = { fid, fbillTypeID, fbusinessType, fdate, fsettleCurrId, fpurchaseOrgId, fsupplierId, fsupplierName, fpriceTimePoint, forderFinanceId, entities }
          this.title = '新增采购订单'
          this.initColumnSettings()
          this.open = true
          this.useOrg = fpurchaseOrgId
        } else this.$message.error(msg)
      }).finally(() => {
        loading.close()
      })
    },
    // 申请组织改变
    handlePurchaseOrgChange(value) {
      this.$set(this, 'useOrg', value)
      this.$set(this.form, 'fpurchaseOrgId', value)
      this.$set(this.form, 'fsupplierId', undefined)
      this.$set(this.form, 'fsupplierName', undefined)
      this.$set(
        this.form,
        'entities',
        this.form.entities.map(item => ({ ...item, fentrySettleOrgId: value }))
      )
    },
    // 供应商选择
    handleSupplierSelect(data) {
      this.$set(this.form, 'fsupplierId', data.FNumber)
      this.$set(this.form, 'fsupplierName', data.FName)
    },
    // 单据类型改变
    handleBillTypeChange(value) {
      this.form.fbusinessType = this.billTypeOptions.find(item => item.value === value).businessType
    },
    // 选择仓库回调
    handleStockSelect(event, row) {
      this.$set(row, 'fstockId', event.Number)
      this.$set(row, 'fstockName', event.Name)
    },
    // 选择物料回调
    handleMaterialSearchSelect(event, row) {
      const obj = this.UnitList.find(item => item.FName == event.Unit)
      this.$set(row, 'fmaterialId', event.Number)
      this.$set(row, 'fmaterialName', event.Number)
      this.$set(row, 'Name', event.Name || '')
      this.$set(row, 'Specification', event.Specification || '')
      this.$set(row, 'fpriceUnitId', obj?.FNumber || '')
      this.$set(row, 'fstockUnitID', obj?.FNumber || '')
      this.$set(row, 'funitId', obj?.FNumber || '')
      this.$set(row, 'fdeliveryDate', this.parseTime(new Date(), '{y}-{m}-{d} {h}:{i}:{s}'))
      const hasEmpty = this.form.entities.filter(item => !item.fmaterialId)
      if (hasEmpty.length == 0) this.form.entities.push(this.createDefaultEntity())
    },
    // 添加物料
    handleAddMaterial() {
      this.form.entities.push(
        this.createDefaultEntity({
          fdeliveryDate: this.parseTime(new Date(), '{y}-{m}-{d} {h}:{i}:{s}')
        })
      )
    },
    // 关闭
    handleClose() {
      this.handleCancel()
    },
    // 取消
    async handleCancel(flag = false) {
      if (this.fid && !this.hasSuccessfully) {
        try {
          await deletePurchaseOrderV2({ id: this.fid })
          this.fid = undefined
          this.$nextTick(() => {
            this.open = false
            this.$emit('callBack', flag)
          })
        } catch (error) {
          this.$message.error(error.message)
        }
      } else {
        this.open = false
        this.$emit('callBack', flag)
      }
    },
    // 采购数量变化
    handleQtyChange(row) {
      this.$nextTick(() => {
        const calculations = this.calculatePrices(row)
        this.$set(row, 'famount', calculations.famount)
        this.$set(row, 'ftaxAmount', calculations.ftaxAmount)
        this.$set(row, 'fpriceUnitQty', row.fqty)
      })
    },
    // 单价变化
    handlePriceChange(row) {
      this.$nextTick(() => {
        const calculations = this.calculatePrices(row)
        this.$set(row, 'ftaxPrice', calculations.ftaxPrice)
        this.$set(row, 'famount', calculations.famount)
        this.$set(row, 'ftaxAmount', calculations.ftaxAmount)
      })
    },
    // 含税单价变化
    handleTaxPriceChange(row) {
      this.$nextTick(() => {
        const calculations = this.calculatePrices(row)
        this.$set(row, 'fprice', calculations.fprice)
        this.$set(row, 'famount', calculations.famount)
        this.$set(row, 'ftaxAmount', calculations.ftaxAmount)
      })
    },
    // 税率变化
    handleTaxRateChange(row) {
      this.$nextTick(() => {
        const calculations = this.calculatePrices(row)
        this.$set(row, 'ftaxPrice', calculations.ftaxPrice)
        this.$set(row, 'famount', calculations.famount)
        this.$set(row, 'ftaxAmount', calculations.ftaxAmount)
      })
    },
    // 金额变化
    handleAmountChange(row) {
      this.$nextTick(() => {
        const calculations = this.calculatePrices(row)
        this.$set(row, 'fprice', calculations.fpriceFromAmount)
        this.$set(row, 'ftaxPrice', calculations.ftaxPrice)
        this.$set(row, 'ftaxAmount', calculations.ftaxAmount)
      })
    },
    // 含税金额变化
    handleTaxAmountChange(row) {
      this.$nextTick(() => {
        const calculations = this.calculatePrices(row)
        this.$set(row, 'fprice', calculations.fprice)
        this.$set(row, 'ftaxPrice', calculations.ftaxPriceFromAmount)
        this.$set(row, 'famount', calculations.famount)
      })
    },
    // 从API数据映射到实体对象
    mapApiDataToEntity(item) {
      return this.createDefaultEntity({
        fdeliveryDate: item.DeliveryDate,
        fentryID: item.Id,
        fentrySettleOrgId: item.SettleOrgId?.Number,
        fentryTaxRate: item.TaxRate || 0,
        fmaterialId: item.MaterialId?.Number,
        fmaterialName: (item.MaterialId?.Name && this.getString(item.MaterialId?.Name)) || undefined,
        Name: item.MaterialId?.Name?.[0]?.Value,
        Specification: item.MaterialId?.Specification?.[0]?.Value,
        fprice: item.Price,
        ftaxPrice: item.TaxPrice,
        fpriceUnitId: item.PriceUnitId?.Number,
        fpriceUnitQty: item.PriceUnitQty,
        fqty: item.Qty,
        fstockUnitID: item.StockUnitID?.Number,
        funitId: item.UnitId?.Number,
        famount: item.Amount_LC || 0,
        ftaxAmount: item.TaxAmount || 0
      })
    },
    // 统一的实体映射方法
    mapEntityForSubmit(item) {
      return {
        fdeliveryDate: item.fdeliveryDate,
        fentryID: item.fentryID,
        fentrySettleOrgId: item.fentrySettleOrgId,
        fentryTaxRate: item.fentryTaxRate || 0,
        fmaterialId: item.fmaterialId,
        fprice: item.fprice,
        fpriceUnitId: item.fpriceUnitId,
        fpriceUnitQty: item.fpriceUnitQty,
        fqty: item.fqty,
        fstockUnitID: item.fstockUnitID,
        funitId: item.funitId,
        fzsl: item.fauxQty,
        flotnumber: item.flot,
        scmj: item.fstockId
      }
    },
    // 获取合计（实退数量、补料数量、扣款数量、金额）
    getSummary(param) {
      const { columns, data } = param
      const result = columns.map((column, index) => {
        if (index === 0) {
          return '合计'
        }
        if (column.property === 'fqty' || column.property === 'fauxQty' || column.property === 'famount' || column.property === 'ftaxAmount') {
          return `${this.calculateTotal(data, column.property)}`
        }
        return ''
      })
      return result
    },
    // 计算合计
    calculateTotal(data, key) {
      return data.reduce((total, item) => {
        if (key === 'fqty' || key === 'fauxQty' || key === 'famount' || key === 'ftaxAmount') {
          const value = parseFloat(item[key]) || 0
          return parseFloat((total + value).toFixed(5))
        }
        return total
      }, 0)
    },
    // 提交
    handleSubmit() {
      if (this.form.entities.length > 1) this.form.entities = this.form.entities.filter(item => item.fmaterialId)
      this.$nextTick(() => {
        this.$refs.form.validate(valid => {
          if (valid) {
            let data = { ...this.form }
            data.entities = data.entities.map(item => this.mapEntityForSubmit(item))
            addPurchaseOrder(data).then(res => {
              const { code, msg } = res
              if (code == 200) {
                const success = this.form.fid ? (this.type == 'order' ? '提交成功' : '修改成功') : '提交成功'
                this.$message.success(success)
                this.hasSuccessfully = true
                if (this.isPush) {
                  const { number } = res.data
                  if (number) {
                    this.open = false
                    this.$nextTick(() => {
                      this.$refs.orderDetail.getInfo({ BillNo: number })
                    })
                  } else this.handleCancel(true)
                } else this.handleCancel(true)
              } else this.$message.error(msg)
            })
          }
        })
      })
    },
    // 更新显隐列
    updateKingdeeColumns(columns) {
      this.kingdeeColumns = columns
      localStorage.setItem(`kingdee_purchase_order_${this.userId}`, JSON.stringify(columns))
    }
  }
}
</script>
<style scoped lang="scss">
@import '@/assets/styles/custom-new.scss';
::v-deep {
  .custom-table {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    border-bottom: 0;
    margin-bottom: 20px;
    .el-form-item {
      margin-top: 10px !important;
      margin-bottom: 10px !important;
      .el-form-item__error {
        top: 95%;
        padding-top: 0;
      }
    }
    .el-input--prefix .el-input__inner {
      padding-left: 20px;
      padding-right: 20px;
    }
    .el-date-editor--datetime .el-input__inner {
      padding-left: 30px;
      padding-right: 30px;
    }
  }
}
</style>
