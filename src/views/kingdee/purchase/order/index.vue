<template>
  <div class="newBox bgcf9 vh-85">
    <!-- 搜索 -->
    <div class="custom-search flex" style="padding-top: 18px" :style="{ justifyContent: showSearch ? 'space-between' : 'flex-end' }">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" @submit.native.prevent>
        <!-- 单据编号 -->
        <el-form-item label="单据编号" prop="billNo">
          <el-input v-model="queryParams.billNo" placeholder="请输入单据编号" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <!-- 物料名称 -->
        <el-form-item label="物料名称" prop="materialName">
          <el-input v-model="queryParams.materialName" placeholder="请输入物料名称" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <!-- 供应商 -->
        <el-form-item label="供应商" prop="supplierName">
          <el-input v-model="queryParams.supplierName" placeholder="请输入供应商" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <!-- 单据状态 -->
        <el-form-item label="单据状态" prop="documentStatus">
          <el-select v-model="queryParams.documentStatus" placeholder="请选择单据状态" clearable>
            <el-option v-for="item in DocumentStatusOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <!-- 采购组织 -->
        <el-form-item label="采购组织" prop="purchaseOrg">
          <el-select v-model="queryParams.purchaseOrg" placeholder="请选择采购组织" clearable filterable>
            <el-option v-for="item in ApplicationOrgId" :key="item.value" :label="item.label" :value="item.label" />
          </el-select>
        </el-form-item>
        <!-- 结算组织 -->
        <el-form-item label="结算组织" prop="settleOrg">
          <el-select v-model="queryParams.settleOrg" placeholder="请选择结算组织" clearable filterable>
            <el-option v-for="item in ApplicationOrgId" :key="item.value" :label="item.label" :value="item.label" />
          </el-select>
        </el-form-item>
        <!-- 收料组织 -->
        <el-form-item label="收料组织" prop="receiveOrg">
          <el-select v-model="queryParams.receiveOrg" placeholder="请选择收料组织" clearable filterable>
            <el-option v-for="item in ApplicationOrgId" :key="item.value" :label="item.label" :value="item.label" />
          </el-select>
        </el-form-item>
        <!-- 关闭状态 -->
        <el-form-item label="关闭状态" prop="closeStatus">
          <el-select v-model="queryParams.closeStatus" placeholder="请选择关闭状态" clearable>
            <el-option label="未关闭" value="A" />
            <el-option label="已关闭" value="B" />
          </el-select>
        </el-form-item>
        <!-- 采购日期 -->
        <el-form-item label="采购日期" prop="date">
          <el-date-picker v-model="queryParams.date" placeholder="请选择采购日期" type="date" value-format="yyyy-MM-dd" size="small"></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="small" @click="handleResetQuery">重置</el-button>
          <el-button type="primary" icon="el-icon-plus" size="small" @click="handleCreate">新增采购订单</el-button>
        </el-form-item>
      </el-form>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns" isSetitem @updateColumns="updateColumns"></right-toolbar>
    </div>
    <!-- 列表 -->
    <div style="padding: 15px 20px">
      <el-table v-loading="loading" ref="allTable" border :data="list" row-key="id" style="width: 100%" class="custom-table" :span-method="objectSpanMethod" @selection-change="handleSelectionChange">
        <!-- 复选框 -->
        <el-table-column type="selection" width="55" align="center" :reserve-selection="true" />
        <!-- 序号 -->
        <el-table-column align="center" type="index" label="序号" v-if="columns[0].visible"></el-table-column>
        <!-- 单据编号 -->
        <el-table-column align="center" prop="BillNo" label="单据编号" show-overflow-tooltip v-if="columns[1].visible">
          <template slot-scope="{ row }">
            <span class="table-link" @click="handleDetail(row)">{{ row.BillNo }}</span>
          </template>
        </el-table-column>
        <!-- 采购日期 -->
        <el-table-column align="center" prop="Date" label="采购日期" show-overflow-tooltip v-if="columns[2].visible">
          <template slot-scope="{ row }">{{ parseTime(row.Date, '{y}-{m}-{d}') }}</template>
        </el-table-column>
        <!-- 供应商 -->
        <el-table-column align="center" prop="Supplier" label="供应商" show-overflow-tooltip v-if="columns[3].visible"></el-table-column>
        <!-- 单据状态 -->
        <el-table-column align="center" prop="DocumentStatus" label="单据状态" show-overflow-tooltip v-if="columns[4].visible">
          <template slot-scope="{ row }">{{ getDocumentStatusLabel(row.DocumentStatus) }}</template>
        </el-table-column>
        <!-- 采购组织 -->
        <el-table-column align="center" prop="PurchaseOrg" label="采购组织" show-overflow-tooltip v-if="columns[5].visible"></el-table-column>
        <!-- 关闭状态 -->
        <el-table-column align="center" prop="CloseStatus" label="关闭状态" show-overflow-tooltip v-if="columns[6].visible">
          <template slot-scope="{ row }">{{ row.CloseStatus === 'A' ? '未关闭' : '已关闭' }}</template>
        </el-table-column>
        <!-- 物料编码-->
        <el-table-column align="center" prop="MaterialNumber" label="物料编码" show-overflow-tooltip v-if="columns[7].visible">
          <template slot-scope="{ row }">
            <span class="table-link" @click="handleMaterialNumber(row.MaterialNumber)">{{ row.MaterialNumber }}</span>
          </template>
        </el-table-column>
        <!-- 物料名称 -->
        <el-table-column align="center" prop="MaterialName" label="物料名称" show-overflow-tooltip v-if="columns[8].visible"></el-table-column>
        <!-- 规格型号 -->
        <el-table-column align="center" prop="Model" label="规格型号" show-overflow-tooltip v-if="columns[9].visible"></el-table-column>
        <!-- 采购单位-->
        <el-table-column align="center" prop="UnitName" label="采购单位" show-overflow-tooltip v-if="columns[10].visible"></el-table-column>
        <!-- 采购数量-->
        <el-table-column align="center" prop="Qty" label="采购数量" show-overflow-tooltip v-if="columns[11].visible"></el-table-column>
        <!-- 单价 -->
        <el-table-column align="center" prop="Price" label="单价" show-overflow-tooltip v-if="columns[12].visible"></el-table-column>
        <!-- 金额 -->
        <el-table-column align="center" prop="EntryAmount" label="金额" show-overflow-tooltip v-if="columns[13].visible"></el-table-column>
        <!-- 交货日期-->
        <el-table-column align="center" prop="DeliveryDate" label="交货日期" show-overflow-tooltip v-if="columns[14].visible">
          <template slot-scope="{ row }">{{ parseTime(row.DeliveryDate, '{y}-{m}-{d} {h}:{i}:{s}') }}</template>
        </el-table-column>
        <!-- 是否赠品-->
        <el-table-column align="center" prop="GiveAway" label="是否赠品" show-overflow-tooltip v-if="columns[15].visible">
          <template slot-scope="{ row }">{{ row.GiveAway ? '是' : '否' }}</template>
        </el-table-column>
        <!-- 备注 -->
        <el-table-column align="center" prop="Remarks" label="备注" show-overflow-tooltip v-if="columns[16].visible"></el-table-column>
        <!-- 操作 -->
        <el-table-column align="center" label="操作" show-overflow-tooltip v-if="columns[17].visible">
          <template slot-scope="{ row }">
            <el-button type="text" size="small" icon="el-icon-edit" :disabled="!(row.DocumentStatus === 'A' || row.DocumentStatus === 'D')" @click="handleEdit(row)">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="custom-pagination">
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.limit" @pagination="getList" :local="userId + '.purchaseOrderPageSize'" />
      </div>
    </div>
    <!--详情-->
    <order-detail ref="orderDetail" @callBack="callBack" @update="handleCallUpdate" v-if="showDetail"></order-detail>
    <!-- 物料详情 -->
    <material-detail ref="materialDetail" v-if="showMaterialDetail" />
    <!-- 新增采购订单 -->
    <purchase-order-create ref="purchaseOrderCreate" @callBack="callBack" v-if="showAdd" />
    <!-- 已选择操作栏 -->
    <template v-if="selectChecked.length">
      <div class="collectAll">
        <div class="collectAll-box">
          <div class="collectAll-title">已选择 {{ selectChecked.length }} 项</div>
          <el-tooltip content="提交" placement="top" effect="dark">
            <div class="collectAll-btn">
              <span @click="handleBatchAction('submit')">提交</span>
            </div>
          </el-tooltip>
          <el-tooltip content="审核" placement="top" effect="dark">
            <div class="collectAll-btn">
              <span @click="handleBatchAction('audit')">审核</span>
            </div>
          </el-tooltip>
          <el-tooltip content="撤销" placement="top" effect="dark">
            <div class="collectAll-btn">
              <span @click="handleBatchAction('revoke')">撤销</span>
            </div>
          </el-tooltip>
          <el-tooltip content="删除" placement="top" effect="dark">
            <div class="collectAll-btn">
              <span @click="handleBatchAction('delete')">删除</span>
            </div>
          </el-tooltip>
          <el-tooltip content="反审" placement="top" effect="dark">
            <div class="collectAll-btn">
              <span @click="handleBatchAction('unAudit')">反审</span>
            </div>
          </el-tooltip>
          <el-tooltip content="退出" placement="top" effect="dark">
            <div class="collectAll-close" @click="handleCloseSelection">
              <i class="el-icon-close"></i>
            </div>
          </el-tooltip>
        </div>
      </div>
    </template>
  </div>
</template>
<script>
import { auditPurchaseOrder, cancelPurchaseOrder, deletePurchaseOrder, pushPurchaseOrder, submitPurchaseOrder, unAuditPurchaseOrder, getPurchaseOrderList } from '@/api/kingdee/purchase/order'
import OrderDetail from '@/views/kingdee/purchase/order/detail'
import MaterialDetail from '@/views/kingdee/material/detail'
import { kingdee } from '@/minix'
import PurchaseOrderCreate from '@/views/kingdee/purchase/order/create'

export default {
  name: 'Korder',
  mixins: [kingdee],
  components: { OrderDetail, MaterialDetail, PurchaseOrderCreate },
  data() {
    return {
      // 搜索
      queryParams: {
        pageNum: 1,
        startRow: 0, // 开始行索引
        limit: 10, // 最大行数
        billNo: undefined, // 单据编号
        materialNumber: undefined, // 物料编码
        supplierName: undefined, // 供应商
        documentStatus: undefined, // 单据状态
        purchaseOrg: undefined, // 采购组织
        settleOrg: undefined, // 结算组织
        receiveOrg: undefined, // 收料组织
        materialName: undefined, // 物料名称
        closeStatus: undefined, // 关闭状态
        date: undefined // 采购日期
      },
      // 列表
      list: [],
      total: 0,
      loading: false,
      showDetail: false,
      showMaterialDetail: false,
      showAdd: false,
      // 列表显隐
      showSearch: true,
      // 列信息
      columns: [
        { key: 0, label: `序号`, visible: true },
        { key: 1, label: `单据编号`, visible: true },
        { key: 2, label: `采购日期`, visible: true },
        { key: 3, label: `供应商`, visible: true },
        { key: 4, label: `单据状态`, visible: true },
        { key: 5, label: `采购组织`, visible: true },
        { key: 6, label: `关闭状态`, visible: true },
        { key: 7, label: `物料编码`, visible: true },
        { key: 8, label: `物料名称`, visible: true },
        { key: 9, label: `规格型号`, visible: true },
        { key: 10, label: `采购单位`, visible: true },
        { key: 11, label: `采购数量`, visible: true },
        { key: 12, label: `单价`, visible: true },
        { key: 13, label: `金额`, visible: true },
        { key: 14, label: `交货日期`, visible: true },
        { key: 15, label: `是否赠品`, visible: true },
        { key: 16, label: `备注`, visible: true },
        { key: 17, label: `操作`, visible: true }
      ],
      isFirstVisit: true,
      selectChecked: []
    }
  },
  activated() {
    if (this.isFirstVisit) {
      this.isFirstVisit = false
      return
    }
    this.getList()
  },
  created() {
    const columns = localStorage.getItem(this.userId + '.purchaseOrderColumns')
    if (columns && JSON.parse(columns).length === this.columns.length) this.columns = JSON.parse(columns)
    // 读取缓存的分页大小
    const cachedPageSize = localStorage.getItem(this.userId + '.purchaseOrderPageSize')
    if (cachedPageSize) {
      this.queryParams.limit = parseInt(cachedPageSize)
    }
    // 获取列表
    this.getList()
  },
  computed: {
    // 获取当前用户ID
    userId() {
      return this.$store.getters.info.userId
    }
  },
  methods: {
    // 列表
    getList() {
      this.loading = true
      this.queryParams.startRow = Math.max(0, (this.queryParams.pageNum - 1) * this.queryParams.limit)
      const query = { ...this.queryParams }
      delete query.pageNum
      getPurchaseOrderList(query).then(res => {
        const { code, data, msg } = res
        if (code === 200) {
          this.list = data.data || []
          this.total = data.total || 0
        } else this.$message.error(msg)
        this.loading = false
      })
    },
    // 相同字段合并
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      const visibleColumns = this.columns.filter(col => col.visible)
      const currentColumnKey = visibleColumns[columnIndex]?.key
      // 第一列复选框需要合并（不在columns配置中）
      if (columnIndex === 0) {
        const _row = this.calculateSpan(this.list, 'BillNo')[rowIndex]
        const _col = _row > 0 ? 1 : 0
        return {
          rowspan: _row,
          colspan: _col
        }
      }
      // 需要合并的列：单据编号(2)到关闭状态(7)
      if (currentColumnKey >= 2 && currentColumnKey <= 7) {
        const _row = this.calculateSpan(this.list, 'BillNo')[rowIndex]
        const _col = _row > 0 ? 1 : 0
        return {
          rowspan: _row,
          colspan: _col
        }
      }
    },
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 重置搜索
    handleResetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 查看物料
    handleMaterialNumber(number) {
      this.showMaterialDetail = true
      this.$nextTick(() => {
        this.$refs.materialDetail.getInfo(number)
      })
    },
    // 查看详情
    handleDetail(row) {
      this.showDetail = true
      this.$nextTick(() => {
        this.$refs.orderDetail.getInfo(row)
      })
    },
    // 新增
    handleCreate() {
      this.showAdd = true
      this.$nextTick(() => {
        this.$refs.purchaseOrderCreate.init()
      })
    },
    // 编辑退料申请单
    handleEdit(row) {
      const { BillNo } = row
      this.showAdd = true
      this.$nextTick(() => {
        this.$refs.purchaseOrderCreate.init(BillNo)
      })
    },
    // 回调
    callBack(flag = false) {
      this.showDetail = false
      this.showAdd = false
      if (flag) this.getList()
    },
    // 更新
    handleCallUpdate(data) {
      const { billNo, newStatus } = data
      const index = this.list.findIndex(item => item.BillNo === billNo)
      if (index !== -1) this.list[index].DocumentStatus = newStatus
    },
    // 改变显隐列缓存
    updateColumns(data = []) {
      localStorage.setItem(this.userId + '.purchaseOrderColumns', JSON.stringify(data))
    },
    // 选择
    handleSelectionChange(selection) {
      this.selectChecked = selection
    },
    // 关闭已选择操作栏
    handleCloseSelection() {
      this.selectChecked = []
      this.$nextTick(() => {
        if (this.$refs.allTable) {
          this.$refs.allTable.clearSelection()
        }
      })
    },
    // 批量操作
    async handleBatchAction(action) {
      if (!this.selectChecked.length) {
        this.$message.warning('请先选择要操作的数据')
        return
      }
      // 操作文本映射
      const actionTextMap = {
        submit: '提交',
        audit: '审核',
        revoke: '撤销',
        delete: '删除',
        unAudit: '反审'
      }
      const actionText = actionTextMap[action]
      if (!actionText) {
        this.$message.error('未知操作类型')
        return
      }
      // 确认操作
      try {
        await this.$modal.confirm(`确认要批量${actionText}选中的 ${this.selectChecked.length} 条采购订单吗？`)
      } catch {
        return
      }
      // 验证操作权限（根据单据状态）
      const validItems = this.validateBatchAction(action)
      if (validItems.length === 0) {
        this.$message.warning(`选中的数据都不符合${actionText}条件`)
        return
      }
      if (validItems.length < this.selectChecked.length) {
        const invalidCount = this.selectChecked.length - validItems.length
        try {
          await this.$modal.confirm(`有 ${invalidCount} 条数据不符合${actionText}条件，是否继续${actionText}其余 ${validItems.length} 条数据？`)
        } catch {
          return
        }
      }
      // 执行批量操作
      this.executeBatchOperation(action, validItems, actionText)
    },

    // 验证批量操作权限
    validateBatchAction(action) {
      return this.selectChecked.filter(item => {
        const status = item.DocumentStatus
        switch (action) {
          case 'submit':
            return status === 'A' || status === 'D'
          case 'audit':
            return status === 'B'
          case 'revoke':
            return status === 'B'
          case 'delete':
            return status === 'A' || status === 'D'
          case 'unAudit':
            return status === 'C'
          default:
            return false
        }
      })
    },

    // 执行批量操作
    async executeBatchOperation(action, validItems, actionText) {
      const successResults = []
      const errorResults = []
      const loading = this.$loading({
        lock: true,
        text: `正在${actionText}中...`,
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      try {
        // 并行执行所有操作
        const promises = validItems.map(item => this.executeSingleAction(action, item))
        const results = await Promise.allSettled(promises)
        // 处理结果
        results.forEach((result, index) => {
          const item = validItems[index]
          if (result.status === 'fulfilled' && result.value.success) {
            successResults.push({
              item,
              newStatus: result.value.newStatus
            })
          } else {
            errorResults.push({
              item,
              error: result.status === 'rejected' ? result.reason : result.value.error
            })
          }
        })
        // 更新列表状态
        this.updateListStatus(successResults)
        // 显示结果
        this.showBatchResult(action, actionText, successResults, errorResults)
      } finally {
        loading.close()
      }
    },
    // 执行单个操作
    async executeSingleAction(action, item) {
      try {
        const { BillNo } = item
        let apiCall, newStatus

        switch (action) {
          case 'submit':
            apiCall = submitPurchaseOrder({ number: BillNo })
            newStatus = 'B'
            break
          case 'audit':
            apiCall = auditPurchaseOrder({ number: BillNo })
            newStatus = 'C'
            break
          case 'revoke':
            apiCall = cancelPurchaseOrder({ number: BillNo })
            newStatus = 'D'
            break
          case 'delete':
            apiCall = deletePurchaseOrder({ number: BillNo })
            newStatus = null // 删除后从列表移除
            break
          case 'unAudit':
            apiCall = unAuditPurchaseOrder({ number: BillNo })
            newStatus = 'D'
            break
          default:
            throw new Error('未知操作类型')
        }
        const res = await apiCall
        const { code, msg } = res

        if (code === 200) {
          return { success: true, newStatus }
        } else {
          return { success: false, error: msg || '操作失败' }
        }
      } catch (error) {
        return { success: false, error: error.message || '网络错误' }
      }
    },

    // 更新列表状态
    updateListStatus(successResults) {
      const deletedBillNos = []
      successResults.forEach(({ item, newStatus }) => {
        const index = this.list.findIndex(listItem => listItem.BillNo === item.BillNo)
        if (index !== -1) {
          if (newStatus === null) {
            // 删除操作：从列表中移除
            this.list.splice(index, 1)
            this.total = Math.max(0, this.total - 1)
            deletedBillNos.push(item.BillNo)
          } else {
            // 其他操作：更新状态
            this.$set(this.list[index], 'DocumentStatus', newStatus)
          }
        }
      })
      // 更新选中项状态（排除已删除的项）
      if (deletedBillNos.length === 0) {
        // 没有删除操作时，更新选中项的状态
        this.updateSelectedItemsStatus(successResults)
      } else {
        // 有删除操作时，从选中项中移除已删除的项
        this.selectChecked = this.selectChecked.filter(item => !deletedBillNos.includes(item.BillNo))
        // 更新剩余选中项的状态
        const remainingSuccessResults = successResults.filter(result => result.newStatus !== null)
        this.updateSelectedItemsStatus(remainingSuccessResults)
      }
    },
    // 更新选中项状态
    updateSelectedItemsStatus(successResults) {
      successResults.forEach(({ item, newStatus }) => {
        const selectedIndex = this.selectChecked.findIndex(selectedItem => selectedItem.BillNo === item.BillNo)
        if (selectedIndex !== -1) {
          this.$set(this.selectChecked[selectedIndex], 'DocumentStatus', newStatus)
        }
      })
    },
    // 显示批量操作结果
    showBatchResult(action, actionText, successResults, errorResults) {
      const successCount = successResults.length
      const errorCount = errorResults.length
      const totalCount = successCount + errorCount

      if (errorCount === 0) {
        // 全部成功
        this.$message.success(`批量${actionText}完成！成功处理 ${successCount} 条数据`)
        // 对于删除操作，需要清除选中状态（因为数据已被移除）
        if (action === 'delete') {
          this.handleCloseSelection()
        }
      } else if (successCount === 0) {
        // 全部失败
        const errorMessage = this.formatErrorMessage(errorResults, actionText)
        this.$alert(errorMessage, `批量${actionText}失败`, {
          type: 'error',
          confirmButtonText: '确定'
        })
      } else {
        // 部分成功
        const errorMessage = this.formatErrorMessage(errorResults, actionText)
        const message = `批量${actionText}完成！\n成功：${successCount} 条\n失败：${errorCount} 条\n\n失败详情：\n${errorMessage}`

        this.$alert(message, `批量${actionText}结果`, {
          type: 'warning',
          confirmButtonText: '确定'
        })
      }
    },
    // 格式化错误信息
    // prettier-ignore
    formatErrorMessage(errorResults, actionText) {
      return errorResults.map((result, index) => {
        const { item, error } = result
        return `${index + 1}. 单据编号：${item.BillNo} - ${error}`
      }).join('\n')
    }
  }
}
</script>
<style scoped lang="scss">
@import '@/assets/styles/custom-new.scss';
::v-deep {
  .collectAll {
    bottom: 10px;
    left: calc(50% - 200px);
    position: fixed;
    z-index: 50;
    transform: translateX(calc(-50% + 200px));
    -webkit-user-select: none;
    -ms-user-select: none;
    user-select: none;
    transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
    &-box {
      background: rgba(52, 54, 57, 1);
      border-radius: 12px;
      box-shadow: 0 -2px 8px rgba(38, 38, 38, 0.05), 0 10px 16px rgba(38, 38, 38, 0.08);
      height: 52px;
      padding: 7px 25px;
      position: relative;
      display: flex;
      align-items: center;
      color: #ffffff;
      font-size: 14px;
    }
    &-btn {
      margin-left: 15px;
      span {
        display: inline-block;
        padding: 0 10px;
        line-height: 36px;
        cursor: pointer;
        &:hover {
          background-color: rgba(255, 255, 255, 0.08) !important;
        }
      }
    }
    &-close {
      font-size: 20px;
      cursor: pointer;
      i {
        line-height: 36px;
        padding: 0 10px;
        &:hover {
          background-color: rgba(255, 255, 255, 0.08) !important;
        }
      }
    }
  }
}
</style>
