<template>
  <div>
    <el-dialog v-dialogDragBox title="采购订单详情" :visible.sync="open" width="90%" class="custom-dialog" @close="handleBeforeClose">
      <div style="padding: 0 20px">
        <div class="kindeeButton" :key="info.DocumentStatus">
          <el-button type="primary" size="medium" v-if="info.DocumentStatus == 'A' || info.DocumentStatus == 'D'" @click="handleKingdeeDo('submit')">提交</el-button>
          <el-button type="primary" size="medium" disabled v-else>提交</el-button>
          <el-button type="warning" size="medium" v-if="info.DocumentStatus == 'B'" @click="handleKingdeeDo('audit')">审核</el-button>
          <el-button type="warning" size="medium" disabled v-else>审核</el-button>
          <el-button type="info" size="medium" v-if="info.DocumentStatus == 'B'" @click="handleKingdeeDo('revoke')">撤销</el-button>
          <el-button type="info" size="medium" disabled v-else>撤销</el-button>
          <el-button type="danger" size="medium" v-if="info.DocumentStatus == 'A' || info.DocumentStatus == 'D'" @click="handleKingdeeDo('delete')">删除</el-button>
          <el-button type="danger" size="medium" disabled v-else>删除</el-button>
          <el-button type="warning" size="medium" v-if="info.DocumentStatus == 'C'" @click="handleKingdeeDo('unAudit')">反审</el-button>
          <el-button type="warning" size="medium" disabled v-else>反审</el-button>
          <el-button type="success" size="medium" v-if="info.DocumentStatus == 'C'" @click="handleKingdeeDo('push')">下推</el-button>
          <el-button type="success" size="medium" disabled v-else>下推</el-button>
        </div>
        <el-descriptions border :column="4" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 4 - 7em - 22px)' }">
          <template slot="title">
            <div class="customTitle">基本信息</div>
          </template>
          <!-- 单据类型 -->
          <el-descriptions-item label="单据类型">{{ info.BillTypeId && getString(info.BillTypeId.Name) }}</el-descriptions-item>
          <!-- 业务类型 -->
          <el-descriptions-item label="业务类型">{{ getBusinessTypeLabel(info.BusinessType) }}</el-descriptions-item>
          <!-- 单据编号 -->
          <el-descriptions-item label="单据编号">{{ info.BillNo }}</el-descriptions-item>
          <!-- 采购日期 -->
          <el-descriptions-item label="采购日期">{{ parseTime(info.Date, '{y}-{m}-{d}') }}</el-descriptions-item>
          <!-- 单据状态 -->
          <el-descriptions-item label="单据状态">{{ getDocumentStatusLabel(info.DocumentStatus) }}</el-descriptions-item>
          <!-- 供应商 -->
          <el-descriptions-item label="供应商">
            {{ info.SupplierId && getString(info.SupplierId.Name) }}
            <el-button type="text" size="small" icon="el-icon-view" @click="showProvider = !showProvider">查看详情</el-button>
          </el-descriptions-item>
          <!-- 采购组织 -->
          <el-descriptions-item label="采购组织">{{ info.PurchaseOrgId && getString(info.PurchaseOrgId.Name) }}</el-descriptions-item>
          <!-- 采购部门 -->
          <el-descriptions-item label="采购部门">{{ info.PurchaseDeptId && getString(info.PurchaseDeptId.Name) }}</el-descriptions-item>
          <!-- 采购员 -->
          <el-descriptions-item label="采购员">{{ info.PurchaserId && getString(info.PurchaserId.Name) }}</el-descriptions-item>
          <!-- 付款条件 -->
          <el-descriptions-item label="付款条件">{{ info.PayConditionId && getString(info.PayConditionId.Name) }}</el-descriptions-item>
          <!-- 验收方式 -->
          <el-descriptions-item label="验收方式">{{ getACCTYPELabel(info.FACCTYPE) }}</el-descriptions-item>
          <!-- 变更原因 -->
          <el-descriptions-item label="变更原因">{{ info.ChangeReason }}</el-descriptions-item>
          <!-- 订单销售组织 -->
          <el-descriptions-item label="订单销售组织">{{ info.F_SCMJ_DDXSZZ && getString(info.F_SCMJ_DDXSZZ.Name) }}</el-descriptions-item>
          <!-- 订单销售部门 -->
          <el-descriptions-item label="订单销售部门">{{ info.F_SCMJ_DDXSBM && getString(info.F_SCMJ_DDXSBM.Name) }}</el-descriptions-item>
          <!-- 订单销售员 -->
          <el-descriptions-item label="订单销售员">{{ info.F_SCMJ_DDXSY && getString(info.F_SCMJ_DDXSY.Name) }}</el-descriptions-item>
          <!-- 销售订单号 -->
          <el-descriptions-item label="销售订单号">{{ info.F_SCMJ_XSDDH }}</el-descriptions-item>
          <!-- 备注 -->
          <el-descriptions-item label="备注" :span="4">{{ info.F_SCMJ_Remarks }}</el-descriptions-item>
        </el-descriptions>
        <el-descriptions border :column="2" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 2 - 7em - 22px)' }" v-if="showProvider">
          <template slot="title">
            <div class="customTitle">供应商信息</div>
          </template>
          <!-- 供货方 -->
          <el-descriptions-item label="供货方">{{ info.ProviderId && getString(info.ProviderId.Name) }}</el-descriptions-item>
          <!-- 供货方联系人 -->
          <el-descriptions-item label="供货方联系人">{{ (info.ProviderContactId && info.ProviderContactId.Contact) || info.ProviderContact }}</el-descriptions-item>
          <!-- 职务(无返回) -->
          <el-descriptions-item label="职务">{{ info.ProviderJob }}</el-descriptions-item>
          <!-- 手机(无返回) -->
          <el-descriptions-item label="手机">{{ info.ProviderPhone }}</el-descriptions-item>
          <!-- 供应方地址 -->
          <el-descriptions-item label="供应方地址" :span="2">{{ info.ProviderAddress }}</el-descriptions-item>
          <!-- 结算方 -->
          <el-descriptions-item label="结算方">{{ info.SettleId && info.SettleId.SupplierFinance && getString(info.SettleId.SupplierFinance[0].SettleId.Name) }}</el-descriptions-item>
          <!-- 收款方 -->
          <el-descriptions-item label="收款方">{{ info.ChargeId && info.ChargeId.SupplierFinance && getString(info.ChargeId.SupplierFinance[0].ChargeId.Name) }}</el-descriptions-item>
          <!-- 邮箱 -->
          <el-descriptions-item label="邮箱">{{ info.ProviderEMail }}</el-descriptions-item>
        </el-descriptions>
        <el-table :data="info.PaymentEntry" class="custom-table" style="margin-top: 10px">
          <!-- 关联单据 -->
          <el-table-column prop="ORMID" label="关联单据" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.ORMID && getString(scope.row.ORMID.Name) }}</template>
          </el-table-column>
          <!-- 关联单据编号 -->
          <el-table-column prop="RELATBILLNO" label="关联单据编号" align="center" show-overflow-tooltip></el-table-column>
          <!-- 申请付款金额 -->
          <el-table-column prop="PAYAPPLYAMOUNT" label="申请付款金额" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.PAYAPPLYAMOUNT || '' }}</template>
          </el-table-column>
          <!-- 付款金额 -->
          <el-table-column prop="AMAmount" label="付款金额" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.AMAmount || '' }}</template>
          </el-table-column>
          <!-- 退款金额 -->
          <el-table-column prop="REFUNDAMOUNT" label="退款金额" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.REFUNDAMOUNT || '' }}</template>
          </el-table-column>
          <!-- 业务日期 -->
          <el-table-column prop="FPAYDATE" label="业务日期" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ parseTime(scope.row.FPAYDATE) }}</template>
          </el-table-column>
        </el-table>
        <div class="customTitle">明细信息</div>
        <el-table ref="detailTable" :data="info.POOrderEntry" class="custom-table" highlight-current-row @current-change="handleCurrentChange" :setCurrentRow="setCurrentRow">
          <!-- 序号 -->
          <el-table-column type="index" label="序号" align="center"></el-table-column>
          <!-- 物料编码 -->
          <el-table-column label="物料编码" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.MaterialId && scope.row.MaterialId.Number }}</template>
          </el-table-column>
          <!-- 物料名称 -->
          <el-table-column label="物料名称" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.MaterialId && getString(scope.row.MaterialId.Name) }}</template>
          </el-table-column>
          <!-- 规格型号 -->
          <el-table-column label="规格型号" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.MaterialId && getString(scope.row.MaterialId.Specification) }}</template>
          </el-table-column>
          <!-- 采购单位 -->
          <el-table-column label="采购单位" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.UnitId && getString(scope.row.UnitId.Name) }}</template>
          </el-table-column>
          <!-- 采购数量 -->
          <el-table-column prop="Qty" label="采购数量" align="center" show-overflow-tooltip></el-table-column>
          <!-- 计价单位 -->
          <el-table-column label="计价单位" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.PriceUnitId && getString(scope.row.PriceUnitId.Name) }}</template>
          </el-table-column>
          <!-- 计价数量 -->
          <el-table-column prop="PriceUnitQty" label="计价数量" align="center" show-overflow-tooltip></el-table-column>
          <!-- 交货日期 -->
          <el-table-column prop="DeliveryDate" label="交货日期" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ parseTime(scope.row.DeliveryDate) }}</template>
          </el-table-column>
          <!-- 单价 -->
          <el-table-column prop="Price" label="单价" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.Price || '' }}</template>
          </el-table-column>
          <!-- 含税单价 -->
          <el-table-column prop="TaxPrice" label="含税单价" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.TaxPrice || '' }}</template>
          </el-table-column>
          <!-- 税率 -->
          <el-table-column prop="TaxRate" label="税率" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.TaxRate || '' }}</template>
          </el-table-column>
          <!-- 税额 -->
          <el-table-column prop="TaxAmount" label="税额" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.TaxAmount || '' }}</template>
          </el-table-column>
          <!-- 价税合计 -->
          <el-table-column prop="AllAmount_LC" label="价税合计" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.AllAmount_LC || '' }}</template>
          </el-table-column>
          <!-- 金额 -->
          <el-table-column prop="Amount_LC" label="金额" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.Amount_LC || '' }}</template>
          </el-table-column>
          <!-- 需求组织 -->
          <el-table-column label="需求组织" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.RequireOrgId && getString(scope.row.RequireOrgId.Name) }}</template>
          </el-table-column>
          <!-- 收料组织 -->
          <el-table-column label="收料组织" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.ReceiveOrgId && getString(scope.row.ReceiveOrgId.Name) }}</template>
          </el-table-column>
          <!-- 结算组织 -->
          <el-table-column label="结算组织" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.SettleOrgId && getString(scope.row.SettleOrgId.Name) }}</template>
          </el-table-column>
          <!-- 是否赠品 -->
          <el-table-column prop="GiveAway" label="是否赠品" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
              <el-checkbox v-model="scope.row.GiveAway" disabled></el-checkbox>
            </template>
          </el-table-column>
          <!-- 备注 -->
          <el-table-column prop="Note" label="备注" align="center" show-overflow-tooltip></el-table-column>
        </el-table>
        <el-descriptions border :column="3" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 3 - 7em - 22px)' }">
          <template slot="title">
            <div class="customTitle">交货安排</div>
          </template>
          <!-- 物料编码 -->
          <el-descriptions-item label="物料编码">{{ setCurrentRow && setCurrentRow.MaterialId && setCurrentRow.MaterialId.Number }}</el-descriptions-item>
          <!-- 物料名称 -->
          <el-descriptions-item label="物料名称">{{ setCurrentRow && setCurrentRow.MaterialId && getString(setCurrentRow.MaterialId.Name) }}</el-descriptions-item>
          <!-- 辅助属性 -->
          <el-descriptions-item label="辅助属性">{{ setCurrentRow && setCurrentRow.AuxPropId && getString(setCurrentRow.AuxPropId.Name) }}</el-descriptions-item>
          <!-- BOM版本 -->
          <el-descriptions-item label="BOM版本">{{ setCurrentRow && setCurrentRow.BomId && getString(setCurrentRow.BomId.Name) }}</el-descriptions-item>
          <!-- 批号 -->
          <el-descriptions-item label="批号">{{ setCurrentRow && setCurrentRow.FLot && setCurrentRow.FLot.Number }}</el-descriptions-item>
          <!-- 供应商批号 -->
          <el-descriptions-item label="供应商批号">{{ setCurrentRow && setCurrentRow.SupplierLot }}</el-descriptions-item>
          <!-- 收料组织 -->
          <el-descriptions-item label="收料组织">{{ setCurrentRow && setCurrentRow.ReceiveOrgId && getString(setCurrentRow.ReceiveOrgId.Name) }}</el-descriptions-item>
          <!-- 收料部门 -->
          <el-descriptions-item label="收料部门">{{ setCurrentRow && setCurrentRow.ReceiveDeptId && getString(setCurrentRow.ReceiveDeptId.Name) }}</el-descriptions-item>
          <!-- 需求组织 -->
          <el-descriptions-item label="需求组织">{{ setCurrentRow && setCurrentRow.RequireOrgId && getString(setCurrentRow.RequireOrgId.Name) }}</el-descriptions-item>
          <!-- 需求部门 -->
          <el-descriptions-item label="需求部门">{{ setCurrentRow && setCurrentRow.RequireDeptId && getString(setCurrentRow.RequireDeptId.Name) }}</el-descriptions-item>
          <!-- 交货上限 -->
          <el-descriptions-item label="交货上限">{{ (setCurrentRow && setCurrentRow.DeliveryMaxQty) || '' }}</el-descriptions-item>
          <!-- 交货下限 -->
          <el-descriptions-item label="交货下限">{{ (setCurrentRow && setCurrentRow.DeliveryMinQty) || '' }}</el-descriptions-item>
          <!-- 交货提前天数 -->
          <el-descriptions-item label="交货提前天数">{{ (setCurrentRow && setCurrentRow.DeliveryBeforeDays) || '' }}</el-descriptions-item>
          <!-- 交货延迟天数 -->
          <el-descriptions-item label="交货延迟天数">{{ (setCurrentRow && setCurrentRow.DeliveryDelayDays) || '' }}</el-descriptions-item>
          <!-- 最早交货日期 -->
          <el-descriptions-item label="最早交货日期">{{ setCurrentRow && parseTime(setCurrentRow.DeliveryEarlyDate) }}</el-descriptions-item>
          <!-- 最晚交货日期 -->
          <el-descriptions-item label="最晚交货日期">{{ setCurrentRow && parseTime(setCurrentRow.DeliveryLastDate) }}</el-descriptions-item>
        </el-descriptions>
        <el-table :data="setCurrentRow.POOrdersetCurrentRow" class="custom-table" style="margin-top: 10px" v-if="setCurrentRow && setCurrentRow.POOrdersetCurrentRow">
          <!-- 序号 -->
          <el-table-column type="index" label="序号" align="center"></el-table-column>
          <!-- 交货日期 -->
          <el-table-column prop="PlanDeliveryDate" label="交货日期" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ parseTime(scope.row.PlanDeliveryDate) }}</template>
          </el-table-column>
          <!-- 计划单位 -->
          <el-table-column label="计划单位" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.PlanUnitID && getString(scope.row.PlanUnitID.Name) }}</template>
          </el-table-column>
          <!-- 数量 -->
          <el-table-column prop="BasePlanQty" label="数量" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.BasePlanQty || '' }}</template>
          </el-table-column>
          <!-- 已交货数量 -->
          <el-table-column prop="BaseDeliCommitQty" label="已交货数量" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.BaseDeliCommitQty || '' }}</template>
          </el-table-column>
          <!-- 剩余数量 -->
          <el-table-column prop="BaseDeliRemainQty" label="剩余数量" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.BaseDeliRemainQty || '' }}</template>
          </el-table-column>
          <!-- 交货地点 -->
          <el-table-column label="交货地点" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.ELocationId && getString(scope.row.ELocationId.Name) }}</template>
          </el-table-column>
          <!-- 交货地址 -->
          <el-table-column prop="ELocationAddress" label="交货地址" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.ELocationAddress || '' }}</template>
          </el-table-column>
          <!-- 确认交货数量 -->
          <el-table-column prop="ConfirmDeliQty" label="确认交货数量" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.ConfirmDeliQty || '' }}</template>
          </el-table-column>
          <!-- 确认交货日期 -->
          <el-table-column prop="ConfirmDeliDate" label="确认交货日期" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ parseTime(scope.row.ConfirmDeliDate) }}</template>
          </el-table-column>
          <!-- 确认意见 -->
          <el-table-column prop="ConfirmInfo" label="确认意见" align="center" show-overflow-tooltip></el-table-column>
        </el-table>
        <!-- </div> -->
      </div>
    </el-dialog>
    <!-- 下推弹出框 -->
    <el-dialog v-dialogDragBox title="选择单据" :visible.sync="pushOpen" width="580px" class="custom-dialog">
      <div style="padding: 0 20px">
        <el-form ref="pushForm" :model="pushForm" :rules="pushFormRules" label-width="80px">
          <el-form-item label="" label-width="0" prop="target">
            <el-radio-group v-model="pushForm.target" v-removeAriaHidden @change="handleTargetChange" style="width: 100%">
              <el-row :gutter="10" class="custom-push-target">
                <el-col :span="12" v-for="item in pushTarget" :key="item.value">
                  <el-radio :label="item.value">{{ item.label }}</el-radio>
                </el-col>
              </el-row>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="转换规则" prop="ruleId" v-if="isNeedRule()">
            <el-select v-model="pushForm.ruleId" placeholder="请选择转换规则" style="width: 100%" @change="calculateTargetBillType">
              <el-option v-for="item in calculateRule" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="单据类型" prop="targetBillTypeId" v-if="isNeedBillType()">
            <el-select v-model="pushForm.targetBillTypeId" placeholder="请选择单据类型" style="width: 100%">
              <el-option v-for="item in calculateBillType" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn small" @click="pushOpen = false">取消</el-button>
        <el-button class="custom-dialog-btn small primary" :class="{ disabled: !pushForm.target }" :disabled="!pushForm.target" @click="handlePushSubmit">确定</el-button>
      </div>
    </el-dialog>
    <!-- 新增收料通知单 -->
    <add-notification ref="addNotification" @callBack="showAddNotification = false" v-if="showAddNotification" />
    <!-- 新增应付单 -->
    <create-payables ref="createPayables" @callBack="showCreatePayables = false" v-if="showCreatePayables" />
  </div>
</template>
<script>
import { auditPurchaseOrder, cancelPurchaseOrder, deletePurchaseOrder, getPurchaseOrderDetail, pushPurchaseOrder, submitPurchaseOrder, unAuditPurchaseOrder } from '@/api/kingdee/purchase/order'
import { kingdee } from '@/minix'
import AddNotification from '@/views/kingdee/purchase/notification/create'
import CreatePayables from '@/views/kingdee/payables/create'

export default {
  mixins: [kingdee],
  components: { AddNotification, CreatePayables },
  data() {
    return {
      open: false,
      info: {},
      setCurrentRow: {},
      showProvider: false,
      // 业务类型
      BusinessTypeOptions: [
        { value: 'CG', label: '标准采购' },
        { value: 'WW', label: '标准委外' },
        { value: 'ZCCG', label: '资产采购' },
        { value: 'ZYCG', label: '直运采购' },
        { value: 'FYCG', label: '费用采购' },
        { value: 'VMICG', label: 'VMI采购' },
        { value: 'cscg', label: '测试采购' },
        { value: 'DRPCG', label: '分销购销' }
      ],
      // 验收方式
      ACCTYPEOptions: [
        { value: 'A', label: '金额验收' },
        { value: 'Q', label: '数量验收' },
        { value: 'R', label: '比例验收' }
      ],
      // 下推
      pushOpen: false,
      pushTarget: [
        { value: 'PUR_POChange', label: '采购订单变更单' },
        { value: 'IV_PURCHASEOC', label: '采购普通发票' },
        { value: 'STK_InStock', label: '采购入库单' },
        { value: 'PUR_MRB', label: '采购退料单' },
        { value: 'IV_PURCHASEIC', label: '采购增值税专用发票' },
        { value: 'PUR_InitMRS', label: '期初采购入库单' },
        { value: 'PUR_ReceiveBill', label: '收料通知单' },
        { value: 'PUR_MRAPP', label: '退料申请单' },
        { value: 'SAL_SaleOrder', label: '销售订单' },
        { value: 'AP_Payable', label: '应付单' }
      ],
      pushForm: {},
      pushFormRules: {
        target: [{ required: true, message: '请选择下推单据', trigger: ['blur', 'change'] }],
        ruleId: [{ required: true, message: '请选择转换规则', trigger: ['blur', 'change'] }],
        targetBillTypeId: [{ required: true, message: '请选择单据类型', trigger: ['blur', 'change'] }],
        targetOrgId: [{ required: true, message: '请选择目标组织', trigger: ['blur', 'change'] }]
      },
      // 转换规则
      ruleList: [
        // 下推至收料通知单
        { value: 'PUR_PurchaseOrder-PUR_ReceiveBill', label: '采购订单至收料通知单', target: 'PUR_ReceiveBill' },
        // 下推至应付单
        { value: 'AP_PurOrderToPayable', label: '采购订单-应付单', target: 'AP_Payable' }
      ],
      // 单据类型
      targetBillTypeList: [
        // 下推至收料通知单
        { value: '7cd93c259999489c97798063f2f7bd70', label: '标准收料单', target: 'PUR_PurchaseOrder-PUR_ReceiveBill', billType: 'CGDD01_SYS' },
        { value: '57b00d63a20d413db53ba799040f04a2', label: '委外收料单', target: 'PUR_PurchaseOrder-PUR_ReceiveBill', billType: 'CGDD02_SYS' },
        { value: '777f5fd25084498a9e77e1ef2a72e645', label: '资产接收单', target: 'PUR_PurchaseOrder-PUR_ReceiveBill', billType: 'CGDD04_SYS' },
        { value: 'c97bce78f66642c8a655e304bdc8610d', label: '费用物料接收单', target: 'PUR_PurchaseOrder-PUR_ReceiveBill', billType: 'CGDD05_SYS' },
        { value: '7cd93c259999489c97798063f2f7bd70', label: '标准收料单', target: 'PUR_PurchaseOrder-PUR_ReceiveBill', billType: 'CGDD06_SYS' },
        { value: 'e634313fdebb43259fcb9db7b528260b', label: '零散采购收料单', target: 'PUR_PurchaseOrder-PUR_ReceiveBill', billType: 'CGDD01_SYS' },
        // 下推至应付单
        { value: 'a83c007f22414b399b0ee9b9aafc75f9', label: '标准应付单', target: 'AP_PurOrderToPayable' }
      ],
      // 新增收料通知单
      showAddNotification: false,
      // 新增应付单
      showCreatePayables: false
    }
  },
  computed: {
    // 计算规则
    calculateRule() {
      return this.ruleList.filter(item => item.target === this.pushForm.target)
    },
    // 计算单据类型
    calculateBillType() {
      const { ruleId } = this.pushForm
      const billType = this.info?.BillTypeId?.Number || ''
      const arr = this.targetBillTypeList.filter(item => item.target === ruleId) || []
      if (this.pushForm.target === 'AP_Payable') {
        return arr
      }
      const newArr = arr.filter(item => item.billType === billType) || []
      return newArr
    }
  },
  methods: {
    // 获取详情
    getInfo(row = {}) {
      if (!row.BillNo) {
        this.$message.error('参数错误，请重新选择')
        return
      }
      getPurchaseOrderDetail({ billNo: row.BillNo }).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          const { result } = data
          this.open = !!result?.result
          this.info = result?.result || {}
          this.showProvider = false
          this.$nextTick(() => {
            this.setCurrentRow = result?.result?.POOrderEntry?.[0] || {}
            if (this.$refs.detailTable) {
              this.$refs.detailTable.setCurrentRow(this.setCurrentRow)
              this.$refs.detailTable.bodyWrapper.scrollLeft = 0
            }
          })
        } else this.$message.error(msg)
      })
    },
    // 当前行
    handleCurrentChange(val) {
      this.setCurrentRow = val
    },
    // 获取业务类型
    getBusinessTypeLabel(value) {
      const obj = this.BusinessTypeOptions.find(item => item.value === value)
      return obj?.label || ''
    },
    // 获取验收方式
    getACCTYPELabel(value) {
      const obj = this.ACCTYPEOptions.find(item => item.value === value)
      return obj?.label || ''
    },
    // 关闭
    handleBeforeClose() {
      this.handleClose()
      this.$emit('update', { billNo: this.info.BillNo, newStatus: this.info.DocumentStatus })
    },
    // 取消
    handleClose(flag = false) {
      this.open = false
      this.$emit('callBack', flag)
    },
    // 金蝶采购订单操作
    // prettier-ignore
    handleKingdeeDo(type) {
      const number = this.info.BillNo
      if (!number) return
      switch (type) {
        case 'submit':
          // 提交
          this.$modal.confirm('确认要提交该采购订单吗？').then(() => {
            submitPurchaseOrder({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('提交成功')
                this.$set(this.info, 'DocumentStatus', 'B')
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'audit':
          // 审核
          this.$modal.confirm('确认要审核该采购订单吗？').then(() => {
            auditPurchaseOrder({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('审核成功')
                this.$set(this.info, 'DocumentStatus', 'C')
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'revoke':
          // 撤销
          this.$modal.confirm('确认要撤销该采购订单吗？').then(() => {
            cancelPurchaseOrder({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('撤销成功')
                this.$set(this.info, 'DocumentStatus', 'D')
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'unAudit':
          // 反审核
          this.$modal.confirm('确认要反审核该采购订单吗？').then(() => {
            unAuditPurchaseOrder({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('反审核成功')
                this.$set(this.info, 'DocumentStatus', 'D')
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'delete':
          // 删除
          this.$modal.confirm('确认要删除该采购订单吗？').then(() => {
            deletePurchaseOrder({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('删除成功')
                this.handleClose(true)
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'push':
          // 下推
          this.initPushForm()
          break
      }
    },
    // 初始化下推表单
    initPushForm() {
      this.pushForm = {
        number: this.info.BillNo,
        target: undefined,
        ruleId: undefined,
        targetBillTypeId: undefined
      }
      this.$nextTick(() => {
        this.resetForm('pushForm')
      })
      this.pushOpen = true
    },
    // 切换下推单据
    handleTargetChange(val) {
      if (val === 'PUR_ReceiveBill' || val === 'AP_Payable') {
        this.setupReceiveBillPush()
        return
      }
      this.clearPushFormFields()
    },
    // 设置收料通知单下推
    setupReceiveBillPush() {
      this.pushForm.ruleId = this.calculateRule?.[0]?.value || undefined
      this.pushForm.targetBillTypeId = undefined
      // 计算目标单据类型
      this.$nextTick(() => {
        this.calculateTargetBillType()
      })
    },
    // 清空下推表单字段
    clearPushFormFields() {
      this.pushForm.ruleId = undefined
      this.pushForm.targetBillTypeId = undefined
    },
    // 计算目标单据类型
    calculateTargetBillType() {
      const { ruleId } = this.pushForm
      const billType = this.info?.BillTypeId?.Number || ''
      const arr = this.targetBillTypeList.filter(item => item.target === ruleId) || []
      if (this.pushForm.target === 'AP_Payable') {
        this.pushForm.targetBillTypeId = arr?.[0]?.value || undefined
        return
      }
      const newArr = arr.filter(item => item.billType === billType) || []
      this.pushForm.targetBillTypeId = newArr?.[0]?.value || undefined
    },
    // 判断是否需要转换规则
    isNeedRule() {
      const { target, ruleId } = this.pushForm
      if (target === 'PUR_ReceiveBill' || target === 'AP_Payable') {
        return true
      }
      return false
    },
    // 判断是否需要单据类型
    isNeedBillType() {
      const { target, ruleId } = this.pushForm
      if (target === 'PUR_ReceiveBill' || target === 'AP_Payable') {
        return true
      }
      return false
    },
    // 下推提交
    handlePushSubmit() {
      const { number } = this.pushForm
      if (!number) return
      this.$refs.pushForm.validate(valid => {
        if (valid) {
          this.executePushDown()
        }
      })
    },
    // 执行下推操作
    // prettier-ignore
    executePushDown() {
      pushPurchaseOrder(this.pushForm).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          this.handlePushSuccess(data)
        } else if (code === 400) {
          this.handlePushError(msg)
        } else {
          this.$message.error(msg)
        }
      }).catch(error => {
        console.error('下推操作失败:', error)
        this.$message.error('下推操作失败，请重试')
      })
    },
    // 处理下推成功
    handlePushSuccess(data) {
      if (this.pushForm.target === 'PUR_ReceiveBill') {
        this.handleReceiveBillPushSuccess(data)
      } else if (this.pushForm.target === 'AP_Payable') {
        this.handlePayablePushSuccess(data)
      } else {
        this.$message.success('下推成功')
        this.pushOpen = false
        this.handleClose(true)
      }
    },
    // 处理收料通知单下推成功
    handleReceiveBillPushSuccess(data) {
      this.pushOpen = false
      this.showAddNotification = true
      const { responseStatus } = data
      const fid = responseStatus?.successEntitys?.[0]?.id
      if (!fid) {
        this.$message.error('参数错误，请重试！')
        return
      }
      this.$nextTick(() => {
        this.$refs.addNotification.initPush(fid, 'push')
      })
    },
    // 处理应付单下推成功
    handlePayablePushSuccess(data) {
      this.pushOpen = false
      this.showCreatePayables = true
      const { responseStatus } = data
      const fid = responseStatus?.successEntitys?.[0]?.id
      if (!fid) {
        this.$message.error('参数错误，请重试！')
        return
      }
      this.$nextTick(() => {
        this.$refs.createPayables.initPush(fid, 'push')
      })
    },
    // 处理下推错误
    handlePushError(msg) {
      this.$alert(msg || '操作失败，请重试或使用金蝶进行操作！', '提示', {
        type: 'info',
        confirmButtonText: '确定',
        callback: () => {
          this.pushOpen = false
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
@import '@/assets/styles/custom-new.scss';
::v-deep {
  .el-descriptions__header {
    margin-bottom: 0;
  }
  .el-table.custom-table .el-table__body-wrapper .el-table__row:hover td.el-table__cell {
    background-color: #eaf1fe !important;
  }
}
.customTitle {
  font-size: 16px;
  font-weight: normal;
  line-height: 60px;
  color: $font;
}
.kindeeButton {
  display: flex;
  align-items: center;
}
.custom-push-target {
  .el-col.el-col-12 {
    .el-radio {
      height: 30px;
      display: flex;
      align-items: center;
    }
  }
}
</style>
