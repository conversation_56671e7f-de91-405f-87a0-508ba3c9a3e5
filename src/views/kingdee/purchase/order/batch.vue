<template>
  <div>
    <el-dialog v-dialogDragBox :title="title" :visible.sync="open" width="90%" :before-close="handleClose" class="custom-dialog">
      <div style="padding: 0 20px">
        <el-table ref="batchTable" :data="batchList" class="custom-table" highlight-current-row @current-change="handleCurrentChange" :setCurrentRow="setCurrentRow">
          <el-table-column type="index" label="序号" align="center"></el-table-column>
          <!-- 采购日期 -->
          <el-table-column prop="fdate" label="采购日期" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ parseTime(scope.row.fdate, '{y}-{m}-{d}') }}</template>
          </el-table-column>
          <!-- 供应商 -->
          <el-table-column prop="supplierName" label="供应商" align="center" show-overflow-tooltip></el-table-column>
          <!-- 采购组织 -->
          <el-table-column prop="fpurchaseOrgId" label="采购组织" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ formatOrg(scope.row.fpurchaseOrgId) }}</template>
          </el-table-column>
        </el-table>
        <el-row :gutter="10" v-for="(itemList, index) in batchList" :key="itemList.contractId" v-show="itemList.contractId == setCurrentRow.contractId">
          <el-form ref="form" :model="itemList" :rules="rules" label-width="90px">
            <el-col :span="8">
              <el-form-item label="单据类型" prop="fbillTypeID">
                <el-select v-model="itemList.fbillTypeID" placeholder="请选择单据类型" @change="handleBillTypeChange($event, itemList)" style="width: 100%">
                  <el-option v-for="item in billTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="业务类型" prop="fbusinessType">
                <el-select v-model="itemList.fbusinessType" placeholder="请选择业务类型" disabled style="width: 100%">
                  <el-option v-for="item in billTypeOptions" :key="item.value" :label="item.businessLabel" :value="item.businessType"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="定价时点" prop="fpriceTimePoint">
                <el-select v-model="itemList.fpriceTimePoint" placeholder="请选择定价时点" style="width: 100%">
                  <el-option label="系统日期" value="1"></el-option>
                  <el-option label="单据日期" value="2"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="结算币别" prop="fsettleCurrId">
                <el-select v-model="itemList.fsettleCurrId" placeholder="请选择结算币别" style="width: 100%">
                  <el-option v-for="(item, index) in CurrencyId" :key="index" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="采购组织" prop="fpurchaseOrgId">
                <el-select v-model="itemList.fpurchaseOrgId" placeholder="请选择采购组织" filterable style="width: 100%" @change="handlePurchaseOrgChange($event, itemList)">
                  <el-option v-for="item in ApplicationOrgId" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="申请日期" prop="fdate">
                <el-date-picker v-model="itemList.fdate" type="date" placeholder="选择日期" value-format="yyyy-MM-dd" style="width: 100%"></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="供应商" prop="fsupplierId">
                <supplier-search-select :useOrg="useOrg" :keyword.sync="itemList.fsupplierId" style="width: 100%" isBack :showLabel="false" @callBack="handleSupplierSearchSelect($event, itemList)" />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <div style="display: flex; justify-content: flex-end; margin-bottom: 10px"><only-column isSetitem @updateColumns="updateKingdeeColumns" :columns="kingdeeColumns"></only-column></div>
              <el-table :data="itemList.entities" stripe style="width: 100%" class="custom-table custom-table-cell0">
                <!-- 序号 -->
                <el-table-column align="center" type="index" label="序号" width="50" v-if="kingdeeColumns[0].visible"></el-table-column>
                <!-- 物料编码 -->
                <el-table-column align="center" prop="fmaterialId" label="物料编码" min-width="120" v-if="kingdeeColumns[1].visible">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.fmaterialId`" :rules="rules.fmaterialId">
                      <material-search-select ref="materialSearchSelect" :keyword.sync="scope.row.fmaterialName" :useOrg="itemList.fpurchaseOrgId" size="small" :showLabel="false" @callBack="handleMaterialSearchSelect($event, scope.row, itemList)" />
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 物料名称 -->
                <el-table-column align="center" prop="Name" label="物料名称" show-overflow-tooltip v-if="kingdeeColumns[2].visible"></el-table-column>
                <!-- 规格型号 -->
                <el-table-column align="center" prop="Specification" label="规格型号" show-overflow-tooltip v-if="kingdeeColumns[3].visible"></el-table-column>
                <!-- 结算组织 -->
                <el-table-column align="center" prop="fentrySettleOrgId" label="结算组织" v-if="kingdeeColumns[4].visible" min-width="120">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.fentrySettleOrgId`" :rules="rules.fentrySettleOrgId">
                      <el-select v-model="scope.row.fentrySettleOrgId" style="width: 100%" size="small">
                        <el-option v-for="item in ApplicationOrgId" :key="item.value" :label="item.label" :value="item.value"></el-option>
                      </el-select>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 采购单位 -->
                <el-table-column align="center" prop="funitId" label="采购单位" v-if="kingdeeColumns[5].visible">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.funitId`" :rules="rules.funitId">
                      <el-select v-model="scope.row.funitId" style="width: 100%" size="small">
                        <el-option v-for="(item, index) in UnitList" :key="index" :label="item.FName" :value="item.FNumber"></el-option>
                      </el-select>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 采购数量 -->
                <el-table-column align="center" prop="fqty" label="采购数量" v-if="kingdeeColumns[6].visible" min-width="100">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.fqty`" :rules="rules.fqty">
                      <el-input :controls="false" v-model="scope.row.fqty" placeholder="请输入" style="width: 100%" size="small" @change="handleQtyChange(scope.row)"></el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 计价单位 -->
                <el-table-column align="center" prop="fpriceUnitId" label="计价单位" v-if="kingdeeColumns[7].visible">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.fpriceUnitId`" :rules="rules.fpriceUnitId">
                      <el-select v-model="scope.row.fpriceUnitId" style="width: 100%" size="small">
                        <el-option v-for="(item, index) in UnitList" :key="index" :label="item.FName" :value="item.FNumber"></el-option>
                      </el-select>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 计价数量 -->
                <el-table-column align="center" prop="fpriceUnitQty" label="计价数量" v-if="kingdeeColumns[8].visible" min-width="100">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.fpriceUnitQty`" :rules="rules.fpriceUnitQty">
                      <el-input :controls="false" v-model="scope.row.fpriceUnitQty" placeholder="请输入" style="width: 100%" size="small"></el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 库存单位 -->
                <el-table-column align="center" prop="fstockUnitID" label="库存单位" v-if="kingdeeColumns[9].visible">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.fstockUnitID`" :rules="rules.fstockUnitID">
                      <el-select v-model="scope.row.fstockUnitID" style="width: 100%" size="small">
                        <el-option v-for="(item, index) in UnitList" :key="index" :label="item.FName" :value="item.FNumber"></el-option>
                      </el-select>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 单价 -->
                <el-table-column align="center" prop="fprice" label="单价" v-if="kingdeeColumns[10].visible" min-width="120">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.fprice`" :rules="rules.fprice">
                      <el-input :controls="false" v-model="scope.row.fprice" placeholder="请输入" style="width: 100%" size="small" @change="handlePriceChange(scope.row)">
                        <template slot="prefix">￥</template>
                      </el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 税率 -->
                <el-table-column align="center" prop="fentryTaxRate" label="税率" v-if="kingdeeColumns[11].visible" min-width="100">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.fentryTaxRate`" :rules="rules.fentryTaxRate">
                      <el-input :controls="false" v-model="scope.row.fentryTaxRate" placeholder="请输入" style="width: 100%" size="small" @change="handleTaxRateChange(scope.row)">
                        <template slot="prefix">￥</template>
                        <template slot="suffix">%</template>
                      </el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 含税单价 -->
                <el-table-column align="center" prop="ftaxPrice" label="含税单价" v-if="kingdeeColumns[12].visible" min-width="120">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.ftaxPrice`" :rules="rules.ftaxPrice">
                      <el-input :controls="false" v-model="scope.row.ftaxPrice" placeholder="请输入" style="width: 100%" size="small" @change="handleTaxPriceChange(scope.row)">
                        <template slot="prefix">￥</template>
                      </el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 金额 -->
                <el-table-column align="center" prop="famount" label="金额" v-if="kingdeeColumns[13].visible" min-width="120">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.famount`" :rules="rules.famount">
                      <el-input :controls="false" v-model="scope.row.famount" placeholder="请输入" style="width: 100%" size="small" @change="handleAmountChange(scope.row)">
                        <template slot="prefix">￥</template>
                      </el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 含税金额 -->
                <el-table-column align="center" prop="ftaxAmount" label="含税金额" v-if="kingdeeColumns[14].visible" min-width="120">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.ftaxAmount`" :rules="rules.ftaxAmount">
                      <el-input :controls="false" v-model="scope.row.ftaxAmount" placeholder="请输入" style="width: 100%" size="small" @change="handleTaxAmountChange(scope.row)">
                        <template slot="prefix">￥</template>
                      </el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 交货日期 -->
                <el-table-column align="center" prop="fdeliveryDate" label="交货日期" v-if="kingdeeColumns[15].visible" min-width="120">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.fdeliveryDate`" :rules="rules.fdeliveryDate">
                      <el-date-picker v-model="scope.row.fdeliveryDate" type="datetime" placeholder="选择日期" value-format="yyyy-MM-dd HH:mm:ss" style="width: 100%" size="small"></el-date-picker>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 操作 -->
                <el-table-column align="center" label="操作" width="120">
                  <template slot-scope="scope">
                    <el-button type="text" size="small" class="el-icon-plus" @click="handleAddMaterial(itemList)">添加</el-button>
                    <el-button v-if="itemList.entities.length > 1" type="text" size="small" class="el-icon-delete" @click="itemList.entities.splice(scope.$index, 1)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-form>
        </el-row>
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn" @click="handleCancel()">取 消</el-button>
        <el-button class="custom-dialog-btn primary" @click="handleSubmit">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { kingdee } from '@/minix'
import { addPurchaseOrder, getPurchaseOrderDetail } from '@/api/kingdee/purchase/order'
import supplierSearchSelect from '@/components/SearchSelect/supplier'
import materialSearchSelect from '@/components/SearchSelect/material'
import { isNumber, isNumberLength } from '@/utils/validate'
import OnlyColumn from '@/components/RightToolbar/onlyColumn'
import { getPurchaseApplyDetail, getPurchaseApplyDetailV2 } from '@/api/kingdee/purchase'

export default {
  components: { supplierSearchSelect, materialSearchSelect, OnlyColumn },
  mixins: [kingdee],
  name: 'PurchaseOrderCreate',
  data() {
    return {
      open: false,
      title: '新增采购订单',
      setCurrentRow: undefined,
      batchList: [],
      form: {},
      rules: {
        fbillTypeID: [{ required: true, message: '请选择单据类型', trigger: 'change' }],
        fbusinessType: [{ required: true, message: '请选择业务类型', trigger: 'change' }],
        fdate: [{ required: true, message: '请选择采购日期', trigger: 'change' }],
        fpriceTimePoint: [{ required: true, message: '请选择定价时点', trigger: 'change' }],
        fsettleCurrId: [{ required: true, message: '请选择结算币别', trigger: 'change' }],
        fpurchaseOrgId: [{ required: true, message: '请选择采购组织', trigger: 'change' }],
        fsupplierId: [{ required: true, message: '请选择供应商', trigger: 'change' }],
        fmaterialId: [{ required: true, message: '请选择物料编码', trigger: 'change' }],
        funitId: [{ required: true, message: '请选择采购单位', trigger: 'change' }],
        fstockUnitID: [{ required: true, message: '请选择库存单位', trigger: 'change' }],
        fpriceUnitId: [{ required: true, message: '请选择计价单位', trigger: 'change' }],
        fpriceUnitQty: [
          { required: true, message: '请输入计价数量', trigger: 'blur' },
          { validator: isNumber, message: '请输入正确的计价数量', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: 'blur' }
        ],
        fprice: [
          { required: true, message: '请输入单价', trigger: 'blur' },
          { validator: isNumber, message: '请输入正确的单价', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: 'blur' }
        ],
        fentryTaxRate: [
          { validator: isNumber, message: '请输入正确的税率', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: 'blur' }
        ],
        fqty: [
          { required: true, message: '请输入采购数量', trigger: 'blur' },
          { validator: isNumber, message: '请输入正确的采购数量', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: 'blur' }
        ],
        ftaxPrice: [
          { validator: isNumber, message: '请输入正确的含税单价', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: 'blur' }
        ],
        famount: [
          { validator: isNumber, message: '请输入正确的金额', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: 'blur' }
        ],
        ftaxAmount: [
          { validator: isNumber, message: '请输入正确的含税金额', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: 'blur' }
        ],
        fdeliveryDate: [{ required: true, message: '请选择交货日期', trigger: 'change' }]
      },
      // 单据类型选项
      billTypeOptions: [
        { label: '标准采购订单', value: 'CGDD01_SYS', businessLabel: '标准采购', businessType: 'CG' },
        { label: '标准委外订单', value: 'CGDD02_SYS', businessLabel: '标准委外', businessType: 'WW' },
        { label: '直运采购订单', value: 'CGDD03_SYS', businessLabel: '直运采购', businessType: 'ZYCG' },
        { label: '资产采购订单', value: 'CGDD04_SYS', businessLabel: '资产采购', businessType: 'ZCCG' },
        { label: '费用采购订单', value: 'CGDD05_SYS', businessLabel: '费用采购', businessType: 'FYCG' },
        { label: '补料采购订单', value: 'CGDD06_SYS', businessLabel: '标准采购', businessType: 'CG' }
      ],
      useOrg: undefined,
      kingdeeColumns: [
        { key: 0, label: `序号`, visible: true },
        { key: 1, label: `物料编码`, visible: true },
        { key: 2, label: `物料名称`, visible: true },
        { key: 3, label: `规格`, visible: true },
        { key: 4, label: `结算组织`, visible: true },
        { key: 5, label: `采购单位`, visible: true },
        { key: 6, label: `采购数量`, visible: true },
        { key: 7, label: `计价单位`, visible: true },
        { key: 8, label: `计价数量`, visible: true },
        { key: 9, label: `库存单位`, visible: true },
        { key: 10, label: `单价`, visible: true },
        { key: 11, label: `税率`, visible: true },
        { key: 12, label: `含税单价`, visible: true },
        { key: 13, label: `金额`, visible: true },
        { key: 14, label: `含税金额`, visible: true },
        { key: 15, label: `交货日期`, visible: true }
      ],
      supplierOptions: []
    }
  },
  computed: {
    userId() {
      return this.$store.getters.info.userId
    }
  },
  methods: {
    // 批量下推
    detail(approvalArr = undefined) {
      if (!approvalArr) return this.$message.error('参数错误，请重试！')
      const loading = this.$loading({
        lock: true,
        text: '加载中',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      const newArr = approvalArr.map(item => this.getInfo(item))
      Promise.all(newArr).then(res => {
        loading.close()
        this.title = '批量新建采购订单'
        this.batchList = res.map(item => ({ ...item, ...this.formatData(item) }))
        this.setCurrentRow = this.batchList[0]
        this.open = true
        this.$nextTick(() => {
          if (this.$refs.batchTable) {
            this.$refs.batchTable.setCurrentRow(this.batchList[0])
            this.$refs.batchTable.bodyWrapper.scrollLeft = 0
          }
        })
      })
    },
    // 当前行
    handleCurrentChange(val) {
      this.setCurrentRow = { ...val, ...this.formatData(val) }
    },
    // 格式化数据
    formatData(obj) {
      const supplierId = obj?.entities?.[0]?.fsuggestSupplierId || undefined
      const entitiesArr = obj?.entities || []
      const entities = entitiesArr.map(item => ({
        fdeliveryDate: item.farrivalDate, // 交货日期
        fentryID: undefined, // 主键
        fentrySettleOrgId: item.frequireOrgId || undefined, // 结算组织
        fentryTaxRate: item.ftaxrate || undefined, // 税率
        fmaterialId: item.fmaterialId || undefined, // 物料编码
        fmaterialName: item.fmaterialId || undefined, // 物料编码
        Name: item.fmaterialDesc || undefined, // 物料名称
        Specification: item.specs || undefined, // 规格型号
        fprice: item.fevaluatePrice || undefined, // 单价
        ftaxPrice: item.ftaxprice || undefined, // 含税单价
        famount: parseFloat((item.freqQty * item.ftaxprice).toFixed(5)) || undefined, // 金额
        ftaxAmount: parseFloat((item.freqQty * item.ftaxprice).toFixed(5)) || undefined, // 含税金额
        fpriceUnitId: item.fpriceUnitId || undefined, // 计价单位
        fpriceUnitQty: item.fpriceUnitQty || undefined, // 计价数量
        fqty: item.freqQty || undefined, // 采购数量
        fstockUnitID: item.freqstockunitid || undefined, // 库存单位
        funitId: item.funitId || undefined // 采购单位
      }))
      return {
        entities,
        fbillTypeID: undefined, // 单据类型 CGDD01_SYS-标准采购订单 CGDD02_SYS-标准委外订单 CGDD03_SYS-直运采购订单 CGDD04_SYS-资产采购订单 CGDD05_SYS-费用采购订单 CGDD06-SYS-补料采购订单
        fbusinessType: undefined, // 业务类型 关联单据类型 CGDD01_SYS-> CG(标准采购) CGDD02_SYS-> WW(标准委外) CGDD03_SYS-> ZYCG(直运采购) CGDD04_SYS->ZCCG(资产采购) CGDD05_SYS-FYCG(费用采购) CGDD06_SYS-> CG(标准采购)
        fdate: undefined, // 采购日期
        fid: undefined, // 主键
        forderFinanceId: undefined, // 财务明细object ID
        fpriceTimePoint: undefined, // 定价时点 1-系统日期 2-单据日期
        fpurchaseOrgId: obj?.fapplicationOrgId || undefined, // 采购组织
        fsettleCurrId: obj?.fcurrencyId || undefined, // 币别
        fsupplierId: supplierId // 供应商
      }
    },
    // 查询采购申请单详情
    async getInfo(row = {}) {
      const number = row?.requisitionNum
      if (!number) return this.$message.error('参数错误')
      let params = { number }
      params.refresh = true
      const kDetail = await getPurchaseApplyDetailV2({ billNo: number })
      const list = kDetail?.data?.result?.result?.ReqEntry || []
      const kentityList = list.map(item => {
        return {
          ...item.MaterialId,
          freqQty: item.ReqQty
        }
      })
      const detail = await getPurchaseApplyDetail(params)
      const { code, msg, data } = detail
      if (code === 200) {
        const entityList = data?.entityList || []
        const entities = entityList.map(item => {
          const kitem = kentityList.find(kitem => kitem.Number == item.fmaterialId)
          return {
            ...item,
            specs: kitem?.Specification?.[0]?.Value || ''
          }
        })
        const form = {
          contractId: data?.contractId || undefined, // 合同ID
          entities, // 明细
          fapplicantId: data?.applicationId || undefined, // 申请人ID
          fapplicationDeptId: data?.applicationDeptId || undefined, // 申请部门ID
          fapplicationOrgId: data?.applicationOrgId || undefined, // 申请组织ID
          fbillTypeID: data?.billType || undefined, // 单据类型ID
          fcurrencyId: data?.currencyId || undefined, // 币种ID
          fnote: data?.note || undefined, // 备注
          fsupplierId: entities?.[0]?.fsuggestSupplierId || undefined, // 建议供应商ID
          supplierName: entities?.[0]?.supplierName || undefined, // 建议供应商ID
          fpurchaseOrgId: entities?.[0]?.frequireOrgId || undefined // 采购组织ID
        }
        return form
      } else this.$message.error(msg)
    },
    // 申请组织改变
    handlePurchaseOrgChange(value, itemList) {
      this.$set(itemList, 'useOrg', value)
      this.$set(itemList, 'fpurchaseOrgId', value)
      this.$set(itemList, 'fsupplierId', undefined)
      this.$set(
        itemList,
        'entities',
        itemList.entities.map(item => ({ ...item, fentrySettleOrgId: value }))
      )
    },
    // 格式化采购组织
    formatOrg(value) {
      const obj = this.ApplicationOrgId.find(item => item.value == value)
      return obj?.label || ''
    },
    // 单据类型改变
    handleBillTypeChange(value, itemList) {
      this.$set(itemList, 'fbusinessType', this.billTypeOptions.find(item => item.value === value).businessType)
    },
    // 选择物料回调
    handleMaterialSearchSelect(event, row, itemList) {
      const obj = this.UnitList.find(item => item.FName == event.Unit)
      this.$set(row, 'fmaterialId', event.Number)
      this.$set(row, 'fmaterialName', event.Number)
      this.$set(row, 'Name', event.Name || '')
      this.$set(row, 'Specification', event.Specification || '')
      this.$set(row, 'fpriceUnitId', obj?.FNumber || '')
      this.$set(row, 'fstockUnitID', obj?.FNumber || '')
      this.$set(row, 'funitId', obj?.FNumber || '')
      this.$set(row, 'fdeliveryDate', this.parseTime(new Date(), '{y}-{m}-{d} {h}:{i}:{s}'))
      const entitiesObj = {
        fdeliveryDate: undefined, // 交货日期
        fentryID: undefined, // 主键
        fentrySettleOrgId: this.form.fpurchaseOrgId, // 结算组织
        fentryTaxRate: undefined, // 税率
        fmaterialId: undefined, // 物料编码
        fprice: undefined, // 单价
        fpriceUnitId: undefined, // 计价单位
        fpriceUnitQty: undefined, // 计价数量
        fqty: undefined, // 采购数量
        fstockUnitID: undefined, // 库存单位
        funitId: undefined // 采购单位
      }
      const hasEmpty = itemList.entities.filter(item => !item.fmaterialId)
      if (hasEmpty.length == 0) itemList.entities.push(entitiesObj)
    },
    // 供应商选择回调
    handleSupplierSearchSelect(event, itemList) {
      this.$set(itemList, 'fsupplierId', event.FNumber)
      this.$set(itemList, 'supplierName', event.FName)
    },
    // 添加物料
    handleAddMaterial(itemList) {
      const entitiesObj = {
        fdeliveryDate: this.parseTime(new Date(), '{y}-{m}-{d} {h}:{i}:{s}'), // 交货日期
        fentryID: undefined, // 主键
        fentrySettleOrgId: this.form.fpurchaseOrgId, // 结算组织
        fentryTaxRate: undefined, // 税率
        fmaterialId: undefined, // 物料编码
        fprice: undefined, // 单价
        fpriceUnitId: undefined, // 计价单位
        fpriceUnitQty: undefined, // 计价数量
        fqty: undefined, // 采购数量
        fstockUnitID: undefined, // 库存单位
        funitId: undefined // 采购单位
      }
      itemList.entities.push(entitiesObj)
    },
    // 关闭
    handleClose() {
      this.open = false
    },
    // 取消
    handleCancel(flag = false) {
      this.open = false
      this.$emit('callBack', flag)
    },
    // 采购数量变化
    handleQtyChange(row) {
      this.$nextTick(() => {
        const { fqty = 0, fprice = 0, fentryTaxRate = 0 } = row
        // 计算金额
        const famount = parseFloat((fprice * fqty).toFixed(5))
        // 计算含税金额
        const ftaxAmount = parseFloat((fprice * (1 + fentryTaxRate / 100) * fqty).toFixed(5))
        // 赋值
        this.$set(row, 'famount', famount)
        this.$set(row, 'ftaxAmount', ftaxAmount)
      })
    },
    // 单价变化
    handlePriceChange(row) {
      this.$nextTick(() => {
        const { fqty = 0, fprice = 0, fentryTaxRate = 0 } = row
        // 计算含税单价
        const ftaxPrice = parseFloat((fprice * (1 + fentryTaxRate / 100)).toFixed(5))
        // 计算金额
        const famount = parseFloat((fprice * fqty).toFixed(5))
        // 计算含税金额
        const ftaxAmount = parseFloat((ftaxPrice * fqty).toFixed(5))
        // 赋值
        this.$set(row, 'ftaxPrice', ftaxPrice)
        this.$set(row, 'famount', famount)
        this.$set(row, 'ftaxAmount', ftaxAmount)
      })
    },
    // 含税单价变化
    handleTaxPriceChange(row) {
      this.$nextTick(() => {
        const { fqty = 0, ftaxPrice = 0, fentryTaxRate = 0 } = row
        // 计算单价
        const fprice = parseFloat((ftaxPrice / (1 + fentryTaxRate / 100)).toFixed(5))
        // 计算金额
        const famount = parseFloat((fprice * fqty).toFixed(5))
        // 计算含税金额
        const ftaxAmount = parseFloat((ftaxPrice * fqty).toFixed(5))
        // 赋值
        this.$set(row, 'fprice', fprice)
        this.$set(row, 'famount', famount)
        this.$set(row, 'ftaxAmount', ftaxAmount)
      })
    },
    // 税率变化
    handleTaxRateChange(row) {
      this.$nextTick(() => {
        const { fqty = 0, fprice = 0, fentryTaxRate = 0 } = row
        // 计算含税单价
        const ftaxPrice = parseFloat((fprice * (1 + fentryTaxRate / 100)).toFixed(5))
        // 计算金额
        const famount = parseFloat((fprice * fqty).toFixed(5))
        // 计算含税金额
        const ftaxAmount = parseFloat((ftaxPrice * fqty).toFixed(5))
        // 赋值
        this.$set(row, 'ftaxPrice', ftaxPrice)
        this.$set(row, 'famount', famount)
        this.$set(row, 'ftaxAmount', ftaxAmount)
      })
    },
    // 金额变化
    handleAmountChange(row) {
      this.$nextTick(() => {
        const { fqty = 0, famount = 0, fentryTaxRate = 0 } = row
        // 计算单价
        const fprice = parseFloat((famount / fqty).toFixed(5))
        // 计算含税单价
        const ftaxPrice = parseFloat((fprice * (1 + fentryTaxRate / 100)).toFixed(5))
        // 计算含税金额
        const ftaxAmount = parseFloat((ftaxPrice * fqty).toFixed(5))
        // 赋值
        this.$set(row, 'fprice', fprice)
        this.$set(row, 'ftaxPrice', ftaxPrice)
        this.$set(row, 'ftaxAmount', ftaxAmount)
      })
    },
    // 含税金额变化
    handleTaxAmountChange(row) {
      this.$nextTick(() => {
        const { fqty = 0, ftaxAmount = 0, fentryTaxRate = 0 } = row
        // 计算单价
        const fprice = parseFloat((ftaxAmount / fqty / (1 + fentryTaxRate / 100)).toFixed(5))
        // 计算含税单价
        const ftaxPrice = parseFloat((ftaxAmount / fqty).toFixed(5))
        // 计算金额
        const famount = parseFloat((fprice * fqty).toFixed(5))
        // 赋值
        this.$set(row, 'fprice', fprice)
        this.$set(row, 'ftaxPrice', ftaxPrice)
        this.$set(row, 'famount', famount)
      })
    },
    // 提交
    async handleSubmit() {
      const formRefList = this.$refs.form
      let result = true
      for (let index = 0; index < this.batchList.length; index++) {
        try {
          await formRefList[index].validate()
        } catch (err) {
          this.$message.error(`第${index + 1}个表单数据校验失败，请检查！`)
          result = false
          break
        }
      }
      if (result) {
        // 批量提交
        const loading = this.$loading({
          lock: true,
          text: '提交中...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        })
        try {
          const promises = this.batchList.map((item, index) => {
            const data = { ...item }
            data.entities = data.entities.map(entity => ({
              fdeliveryDate: entity.fdeliveryDate,
              fentrySettleOrgId: entity.fentrySettleOrgId, 
              fentryTaxRate: entity.fentryTaxRate || 0,
              fmaterialId: entity.fmaterialId,
              fprice: entity.fprice,
              fpriceUnitId: entity.fpriceUnitId,
              fpriceUnitQty: entity.fpriceUnitQty,
              fqty: entity.fqty,
              fstockUnitID: entity.fstockUnitID,
              funitId: entity.funitId
            }))
            return addPurchaseOrder(data).then(res => ({...res, index: index + 1}))
          })
          
          const results = await Promise.all(promises)
          const errors = results.filter(res => res.code !== 200)
          
          if (errors.length === 0) {
            this.$modal.msgSuccess('提交成功')            
            this.handleCancel(true)
          } else {
            const errorMessages = errors.map(err => `第${err.index}个表单: ${err.msg}`).join('<br/>')
            this.$alert(errorMessages, '提交失败', {
              confirmButtonText: '确定',
              type: 'error',
              dangerouslyUseHTMLString: true
            })
          }
        } catch (error) {
          console.error('提交失败:', error)
          this.$alert(error.message || '未知错误', '提交失败', {
            confirmButtonText: '确定',
            type: 'error'
          })
        } finally {
          loading.close()
        }
      }
    },
    // 更新显隐列
    updateKingdeeColumns(columns) {
      this.kingdeeColumns = columns
      localStorage.setItem(`kingdee_purchase_order_${this.userId}`, JSON.stringify(columns))
    }
  }
}
</script>
<style scoped lang="scss">
@import '@/assets/styles/custom-new.scss';
::v-deep {
  .custom-table {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    border-bottom: 0;
    margin-bottom: 20px;
    .el-form-item {
      margin-top: 10px !important;
      margin-bottom: 10px !important;
      .el-form-item__error {
        top: 95%;
        padding-top: 0;
      }
    }
    .el-input--prefix .el-input__inner {
      padding-left: 20px;
      padding-right: 20px;
    }
    .el-date-editor--datetime .el-input__inner {
      padding-left: 30px;
      padding-right: 30px;
    }
  }
}
</style>
