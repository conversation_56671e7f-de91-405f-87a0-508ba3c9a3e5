<template>
  <div>
    <el-dialog v-dialogDragBox title="采购订单变更单详情" :visible.sync="open" width="90%" class="custom-dialog" :before-close="beforeClose">
      <div style="padding: 0 20px">
        <div class="kindeeButton" :key="info.DocumentStatus">
          <el-button type="primary" size="medium" v-if="info.DocumentStatus == 'A' || info.DocumentStatus == 'D'" @click="handleKingdeeDo('submit')">提交</el-button>
          <el-button type="primary" size="medium" disabled v-else>提交</el-button>
          <el-button type="warning" size="medium" v-if="info.DocumentStatus == 'B'" @click="handleKingdeeDo('audit')">审核</el-button>
          <el-button type="warning" size="medium" disabled v-else>审核</el-button>
          <el-button type="info" size="medium" v-if="info.DocumentStatus == 'B'" @click="handleKingdeeDo('revoke')">撤销</el-button>
          <el-button type="info" size="medium" disabled v-else>撤销</el-button>
          <el-button type="danger" size="medium" v-if="info.DocumentStatus == 'A' || info.DocumentStatus == 'D'" @click="handleKingdeeDo('delete')">删除</el-button>
          <el-button type="danger" size="medium" disabled v-else>删除</el-button>
          <el-button type="warning" size="medium" v-if="info.DocumentStatus == 'C'" @click="handleKingdeeDo('unAudit')">反审</el-button>
          <el-button type="warning" size="medium" disabled v-else>反审</el-button>
        </div>
        <el-descriptions border :column="4" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 4 - 7em - 22px)' }">
          <template slot="title">
            <div class="customTitle">基本信息</div>
          </template>
          <!-- 单据类型 -->
          <el-descriptions-item label="单据类型">{{ info.FBillTypeID && getString(info.FBillTypeID.Name) }}</el-descriptions-item>
          <!-- 业务类型 -->
          <el-descriptions-item label="业务类型">{{ info.BusinessType && getOptionLabel(BusinessTypeOptions, info.BusinessType) }}</el-descriptions-item>
          <!-- 单据编号 -->
          <el-descriptions-item label="单据编号">{{ info.FBillNo }}</el-descriptions-item>
          <!-- 日期 -->
          <el-descriptions-item label="日期">{{ info.Date && parseTime(info.Date, '{y}-{m}-{d}') }}</el-descriptions-item>
          <!-- 供应商 -->
          <el-descriptions-item label="供应商">{{ info.SupplierId && getString(info.SupplierId.Name) }}</el-descriptions-item>
          <!-- 结算币别 -->
          <el-descriptions-item label="结算币别">{{ info.SettleCurrId && getString(info.SettleCurrId.Name) }}</el-descriptions-item>
          <!-- 采购组织 -->
          <el-descriptions-item label="采购组织">{{ info.PurchaseOrgId && getString(info.PurchaseOrgId.Name) }}</el-descriptions-item>
          <!-- 采购部门 -->
          <el-descriptions-item label="采购部门">{{ info.PurchaseDeptId && getString(info.PurchaseDeptId.Name) }}</el-descriptions-item>
          <!-- 采购组 -->
          <el-descriptions-item label="采购组">{{ info.PurchaserGroupId && getString(info.PurchaserGroupId.Name) }}</el-descriptions-item>
          <!-- 采购员 -->
          <el-descriptions-item label="采购员">{{ info.PurchaserId && getString(info.PurchaserId.Name) }}</el-descriptions-item>
          <!-- 变更原因 -->
          <el-descriptions-item label="变更原因">{{ info.ChangeReason }}</el-descriptions-item>
          <!-- 单据状态 -->
          <el-descriptions-item label="单据状态">{{ info.DocumentStatus && getDocumentStatusLabel(info.DocumentStatus) }}</el-descriptions-item>
        </el-descriptions>
        <div class="customTitle">明细信息</div>
        <el-table ref="detailTable" :data="info.POChangeEntry" class="custom-table" highlight-current-row>
          <!-- 序号 -->
          <el-table-column type="index" label="序号" align="center"></el-table-column>
          <!-- 物料编码 -->
          <el-table-column label="物料编码" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.MaterialId && scope.row.MaterialId.Number }}</template>
          </el-table-column>
          <!-- 物料名称 -->
          <el-table-column label="物料名称" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.MaterialId && getString(scope.row.MaterialId.Name) }}</template>
          </el-table-column>
          <!-- 规格型号 -->
          <el-table-column prop="Specification" label="规格型号" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.MaterialId && scope.row.MaterialId.Specification && getString(scope.row.MaterialId.Specification) }}</template>
          </el-table-column>
          <!-- 变更类型 -->
          <el-table-column label="变更类型" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.ChangeType }}</template>
          </el-table-column>
          <!-- 采购单位 -->
          <el-table-column label="采购单位" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.UnitId && getString(scope.row.UnitId.Name) }}</template>
          </el-table-column>
          <!-- 原采购数量 -->
          <el-table-column label="原采购数量" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.OldQty || '' }}</template>
          </el-table-column>
          <!-- 新采购数量 -->
          <el-table-column label="新采购数量" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.NewQty || '' }}</template>
          </el-table-column>
          <!-- 原交货日期 -->
          <el-table-column label="原交货日期" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.OldDeliveryDate && parseTime(scope.row.OldDeliveryDate, '{y}-{m}-{d}') }}</template>
          </el-table-column>
          <!-- 新交货日期 -->
          <el-table-column label="新交货日期" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.NewDeliveryDate && parseTime(scope.row.NewDeliveryDate, '{y}-{m}-{d}') }}</template>
          </el-table-column>
          <!-- 原单价 -->
          <el-table-column label="原单价" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.OldPrice || '' }}</template>
          </el-table-column>
          <!-- 新单价 -->
          <el-table-column label="新单价" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.NewPrice || '' }}</template>
          </el-table-column>
          <!-- 是否赠品 -->
          <el-table-column label="是否赠品" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
              <el-checkbox v-model="scope.row.GiveAway" disabled></el-checkbox>
            </template>
          </el-table-column>
          <!-- 原订单编号 -->
          <el-table-column label="原订单编号" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.OrderFBillNo }}</template>
          </el-table-column>
          <!-- 备注 -->
          <el-table-column label="备注" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.Note }}</template>
          </el-table-column>
          <!-- 计价单位 -->
          <el-table-column label="计价单位" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.PRICEUNITID && getString(scope.row.PRICEUNITID.Name) }}</template>
          </el-table-column>
          <!-- 原计价数量 -->
          <el-table-column label="原计价数量" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.OPRICEQTY || '' }}</template>
          </el-table-column>
          <!-- 新计价数量 -->
          <el-table-column label="新计价数量" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.NPRICEQTY || '' }}</template>
          </el-table-column>
          <!-- 库存单位 -->
          <el-table-column label="库存单位" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.STOCKUNITID && getString(scope.row.STOCKUNITID.Name) }}</template>
          </el-table-column>
          <!-- 原库存单位数量 -->
          <el-table-column label="原库存单位数量" align="center" show-overflow-tooltip min-width="110">
            <template slot-scope="scope">{{ scope.row.OSTOCKQTY || '' }}</template>
          </el-table-column>
          <!-- 新库存单位数量 -->
          <el-table-column label="新库存单位数量" align="center" show-overflow-tooltip min-width="110">
            <template slot-scope="scope">{{ scope.row.NSTOCKQTY || '' }}</template>
          </el-table-column>
          <!-- 新库存基本数量 -->
          <el-table-column label="新库存基本数量" align="center" show-overflow-tooltip min-width="110">
            <template slot-scope="scope">{{ scope.row.NSTOCKBASEQTY || '' }}</template>
          </el-table-column>
          <!-- 原需求组织 -->
          <el-table-column label="原需求组织" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.ORequireOrgId && getString(scope.row.ORequireOrgId.Name) }}</template>
          </el-table-column>
          <!-- 新需求组织 -->
          <el-table-column label="新需求组织" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.NRequireOrgId && getString(scope.row.NRequireOrgId.Name) }}</template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { getOrderChangeDetail, auditOrderChange, revokeOrderChange, deleteOrderChange, submitOrderChange, unauditOrderChange } from '@/api/kingdee/purchase/orderChange'
import { kingdee } from '@/minix'

export default {
  mixins: [kingdee],
  data() {
    return {
      open: false,
      info: {},
      setCurrentRow: {},
      // 业务类型
      BusinessTypeOptions: [
        { label: '标准采购', value: 'CG' },
        { label: '标准委外', value: 'WW' },
        { label: '资产采购', value: 'ZCCG' },
        { label: '费用采购', value: 'FYCG' },
        { label: 'VMI采购', value: 'VMICG' },
        { label: '测试采购', value: 'cscg' },
        { label: '分销购销', value: 'DRPCG' }
      ]
    }
  },
  methods: {
    // 获取详情
    getInfo(row = {}) {
      this.open = true
      if (!row.BillNo) {
        this.$message.error('参数错误，请重新选择')
        return
      }
      getOrderChangeDetail({ billNo: row.BillNo }).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          const { result } = data
          this.open = !!result?.result
          this.info = result?.result || {}
          this.$nextTick(() => {
            this.setCurrentRow = result?.result?.POChangeEntry?.[0] || {}
            if (this.$refs.detailTable) {
              this.$refs.detailTable.setCurrentRow(this.setCurrentRow)
              this.$refs.detailTable.bodyWrapper.scrollLeft = 0
            }
          })
        } else this.$message.error(msg)
      })
    },
    beforeClose() {
      this.handleClose()
      this.$emit('update', { billNo: this.info.BillNo, newStatus: this.info.DocumentStatus })
    },
    // 关闭
    handleClose(flag = false) {
      this.open = false
      this.$emit('callBack', flag)
    },
    // 金蝶操作
    // prettier-ignore
    handleKingdeeDo(type) {
      const number = this.info.BillNo
      if (!number) return
      switch (type) {
        case 'submit':
          // 提交
          this.$modal.confirm('确认要提交该采购订单变更单吗？').then(() => {
            submitOrderChange({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('提交成功')
                this.$set(this.info, 'DocumentStatus', 'B')
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'audit':
          // 审核
          this.$modal.confirm('确认要审核该采购订单变更单吗？').then(() => {
            auditOrderChange({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('审核成功')
                this.$set(this.info, 'DocumentStatus', 'C')
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'revoke':
          // 撤销
          this.$modal.confirm('确认要撤销该采购订单变更单吗？').then(() => {
            revokeOrderChange({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('撤销成功')
                this.$set(this.info, 'DocumentStatus', 'D')
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'unAudit':
          // 反审核
          this.$modal.confirm('确认要反审核该采购订单变更单吗？').then(() => {
            unauditOrderChange({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('反审核成功')
                this.$set(this.info, 'DocumentStatus', 'D')
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'delete':
          // 删除
          this.$modal.confirm('确认要删除该采购订单变更单吗？').then(() => {
            deleteOrderChange({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('删除成功')
                this.handleClose(true)
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
      }
    }
  }
}
</script>
<style scoped lang="scss">
@import '@/assets/styles/custom-new.scss';
::v-deep {
  .el-descriptions__header {
    margin-bottom: 0;
  }
  .el-table.custom-table .el-table__body-wrapper .el-table__row:hover td.el-table__cell {
    background-color: #eaf1fe !important;
  }
}
.customTitle {
  font-size: 16px;
  font-weight: normal;
  line-height: 60px;
  color: $font;
}
.kindeeButton {
  display: flex;
  align-items: center;
}
</style>
