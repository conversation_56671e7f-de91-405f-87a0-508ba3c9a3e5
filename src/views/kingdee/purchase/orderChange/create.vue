<template>
  <div>
    <el-dialog v-dialogDragBox :title="title" :visible.sync="open" width="90%" class="custom-dialog" :before-close="beforeClose" append-to-body>
      <div style="padding: 0 20px">
        <el-form :model="form" :rules="rules" ref="form" label-width="6em" class="custom-form">
          <el-row :gutter="20" style="display: flex; flex-wrap: wrap">
            <!-- 单据类型 -->
            <el-col :span="6">
              <el-form-item label="单据类型" prop="fbilltypeid">
                <el-select v-model="form.fbilltypeid" filterable placeholder="请选择单据类型" style="width: 100%" @change="handleChangeBillType">
                  <el-option v-for="item in billTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 业务类型 -->
            <el-col :span="6">
              <el-form-item label="业务类型" prop="fbusinesstype">
                <el-select v-model="form.fbusinesstype" filterable placeholder="请选择业务类型" style="width: 100%" disabled>
                  <el-option v-for="item in businessTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 日期 -->
            <el-col :span="6">
              <el-form-item label="日期" prop="fdate">
                <el-date-picker v-model="form.fdate" type="date" placeholder="请选择日期" style="width: 100%" value-format="yyyy-MM-dd"></el-date-picker>
              </el-form-item>
            </el-col>
            <!-- 采购组织 -->
            <el-col :span="6">
              <el-form-item label="采购组织" prop="fpurchaseorgid">
                <el-select v-model="form.fpurchaseorgid" filterable placeholder="请选择采购组织" style="width: 100%" @change="handleChangePurchaseOrg">
                  <el-option v-for="item in ApplicationOrgId" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 供应商 -->
            <el-col :span="6">
              <el-form-item label="供应商" prop="fsupplierid">
                <supplier-search-select :keyword.sync="form.fsupplierid" :useOrg="form.fpurchaseorgid" :showLabel="false" isBack @callBack="handleSupplierSelect($event)" style="width: 100%" :options="[{ FNumber: form.fsupplierid, FName: form.fsuppliername }]" :disabled="!form.fpurchaseorgid" />
              </el-form-item>
            </el-col>
            <!-- 结算币别 -->
            <el-col :span="6">
              <el-form-item label="结算币别" prop="fsettlecurrid">
                <el-select v-model="form.fsettlecurrid" filterable placeholder="请选择结算币别" style="width: 100%">
                  <el-option v-for="item in CurrencyId" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 采购部门 -->
            <!-- <el-col :span="6">
              <el-form-item label="采购部门" prop="fpurchasedeptid">
                <el-select v-model="form.fpurchasedeptid" filterable placeholder="请选择采购部门" style="width: 100%">
                  <el-option v-for="item in purchaseDeptOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col> -->
            <!-- 采购组 -->
            <!-- <el-col :span="6">
              <el-form-item label="采购组" prop="fpurchasergroupid">
                <el-select v-model="form.fpurchasergroupid" filterable placeholder="请选择采购组" style="width: 100%">
                  <el-option v-for="item in purchaseGroupOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col> -->
            <!-- 采购员 -->
            <el-col :span="6">
              <el-form-item label="采购员" prop="fpurchaserid">
                <el-select v-model="form.fpurchaserid" filterable placeholder="请选择采购员" style="width: 100%" :disabled="!form.fpurchaseorgid">
                  <el-option v-for="item in purchaserOptions" :key="item.Number" :label="item.Name" :value="item.Number">
                    <span style="float: left">{{ item.Name }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.FDept }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 变更原因 -->
            <el-col :span="6">
              <el-form-item label="变更原因" prop="fchangereason">
                <el-input v-model="form.fchangereason" placeholder="请输入变更原因"></el-input>
              </el-form-item>
            </el-col>
            <!-- 明细信息 -->
            <el-col :span="24">
              <el-table :data="form.entities" style="width: 100%" stripe class="custom-table custom-table-cell0" show-summary :summary-method="getSummary">
                <!-- 序号 -->
                <el-table-column type="index" label="序号" width="50" align="center"></el-table-column>
                <!-- 物料编码 -->
                <el-table-column label="物料编码" prop="fmaterialid" min-width="140" align="center">
                  <template slot-scope="scope">
                    <el-tooltip effect="dark" :content="scope.row.fmaterialid" :disabled="!scope.row.fmaterialid">
                      <el-form-item label-width="0" :prop="`entities.${scope.$index}.fmaterialid`" :rules="rules.fmaterialid">
                        <material-search-select ref="materialSearchSelect" :keyword.sync="scope.row.fmaterialid" :useOrg="form.fpurchaseorgid" size="small" :showLabel="false" @callBack="handleMaterialSearchSelect($event, scope.row)" :options="[{ Number: scope.row.fmaterialid, Name: scope.row.fmaterialname, Specification: scope.row.fmaterialmodel }]" />
                      </el-form-item>
                    </el-tooltip>
                  </template>
                </el-table-column>
                <!-- 物料名称 -->
                <el-table-column label="物料名称" prop="fmaterialname" align="center" show-overflow-tooltip min-width="100"></el-table-column>
                <!-- 规格型号 -->
                <el-table-column label="规格型号" prop="fmaterialmodel" align="center" show-overflow-tooltip min-width="100"></el-table-column>
                <!-- 变更类型 -->
                <el-table-column label="变更类型" prop="fchangetype" align="center" min-width="100">
                  <template slot-scope="scope">
                    <el-select v-model="scope.row.fchangetype" placeholder="请选择变更类型" style="width: 100%" size="small">
                      <el-option v-for="item in changeTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                    </el-select>
                  </template>
                </el-table-column>
                <!-- 采购单位 -->
                <el-table-column label="采购单位" prop="funitid" min-width="100" align="center">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.funitid`" :rules="rules.funitid">
                      <el-select v-model="scope.row.funitid" filterable placeholder="请选择采购单位" style="width: 100%" size="small" @change="handleUnitChange($event, scope.row)">
                        <el-option v-for="item in UnitList" :key="item.FNumber" :label="item.FName" :value="item.FNumber"></el-option>
                      </el-select>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 原采购数量 -->
                <el-table-column label="原采购数量" prop="onqty" align="center" show-overflow-tooltip></el-table-column>
                <!-- 新采购数量 -->
                <el-table-column label="新采购数量" prop="fnqty" align="center" min-width="120">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.fnqty`" :rules="rules.fnqty">
                      <el-input v-model="scope.row.fnqty" placeholder="请输入新采购数量" @blur="handleNewQtyChange(scope.row)" size="small"></el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 原交货日期 -->
                <el-table-column label="原交货日期" prop="ondeliverydate" align="center" min-width="100" show-overflow-tooltip>
                  <template slot-scope="scope">{{ parseTime(scope.row.ondeliverydate, '{y}-{m}-{d} {h}:{i}:{s}') }}</template>
                </el-table-column>
                <!-- 新交货日期 -->
                <el-table-column label="新交货日期" prop="fndeliverydate" align="center" min-width="140">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.fndeliverydate`" :rules="rules.fndeliverydate">
                      <el-date-picker v-model="scope.row.fndeliverydate" type="date" placeholder="请选择新交货日期" style="width: 100%" size="small"></el-date-picker>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 原单价 -->
                <el-table-column label="原单价" prop="onprice" align="center" show-overflow-tooltip></el-table-column>
                <!-- 新单价 -->
                <el-table-column label="新单价" prop="fnprice" align="center" min-width="120">
                  <template slot-scope="scope">
                    <template v-if="scope.row.fgiveaway">
                      <el-input placeholder="" style="width: 100%" size="small" disabled></el-input>
                    </template>
                    <template v-else>
                      <el-form-item label-width="0" :prop="`entities.${scope.$index}.fnprice`" :rules="rules.fnprice">
                        <el-input v-model="scope.row.fnprice" placeholder="请输入新单价" size="small"></el-input>
                      </el-form-item>
                    </template>
                  </template>
                </el-table-column>
                <!-- 是否赠品 -->
                <el-table-column label="是否赠品" prop="fgiveaway" align="center">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.fgiveaway`" :rules="rules.fgiveaway">
                      <el-checkbox v-model="scope.row.fgiveaway"></el-checkbox>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 原订单编号 -->
                <el-table-column label="原订单编号" prop="fofbillno" min-width="150" align="center">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.fofbillno`" :rules="rules.fofbillno">
                      <el-input v-model="scope.row.fofbillno" placeholder="原订单编号" size="small"></el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 备注 -->
                <el-table-column label="备注" prop="fnote" min-width="100" align="center">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.fnote`" :rules="rules.fnote">
                      <el-input v-model="scope.row.fnote" placeholder="请输入备注" size="small"></el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 计价单位 -->
                <el-table-column label="计价单位" prop="fpriceunitid" align="center" show-overflow-tooltip>
                  <template slot-scope="scope">{{ scope.row.fpriceunitid && formatUnitName(scope.row.fpriceunitid) }}</template>
                </el-table-column>
                <!-- 原计价数量 -->
                <el-table-column label="原计价数量" prop="onpriceqty" align="center" show-overflow-tooltip></el-table-column>
                <!-- 新计价数量 -->
                <el-table-column label="新计价数量" prop="fnpriceqty" align="center" show-overflow-tooltip></el-table-column>
                <!-- 库存单位 -->
                <el-table-column label="库存单位" prop="fstockunitid" align="center" show-overflow-tooltip>
                  <template slot-scope="scope">{{ scope.row.fstockunitid && formatUnitName(scope.row.fstockunitid) }}</template>
                </el-table-column>
                <!-- 原库存单位数量 -->
                <el-table-column label="原库存单位数量" prop="onstockqty" align="center" show-overflow-tooltip min-width="120"></el-table-column>
                <!-- 新库存单位数量 -->
                <el-table-column label="新库存单位数量" prop="fnstockqty" align="center" show-overflow-tooltip min-width="120"></el-table-column>
                <!-- 新库存基本数量 -->
                <el-table-column label="新库存基本数量" prop="fnewbaseqty" align="center" show-overflow-tooltip min-width="120"></el-table-column>
                <!-- 原需求组织 -->
                <el-table-column label="原需求组织" prop="onrequireorgid" align="center" show-overflow-tooltip>
                  <template slot-scope="scope">{{ scope.row.onrequireorgid && formatOrgName(scope.row.onrequireorgid) }}</template>
                </el-table-column>
                <!-- 新需求组织 -->
                <el-table-column label="新需求组织" prop="fnrequireorgid" align="center" min-width="140">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.fnrequireorgid`" :rules="rules.fnrequireorgid">
                      <el-select v-model="scope.row.fnrequireorgid" filterable placeholder="请选择需求组织" style="width: 100%" size="small">
                        <el-option v-for="item in ApplicationOrgId" :key="item.value" :label="item.label" :value="item.value"></el-option>
                      </el-select>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 操作 -->
                <el-table-column label="操作" width="120" align="center" v-if="isCreate">
                  <template slot-scope="scope">
                    <el-button type="text" size="small" icon="el-icon-plus" @click="handleAdd">添加</el-button>
                    <el-button type="text" size="small" :disabled="form.entities.length == 1" icon="el-icon-delete" @click="handleDelete(scope.$index)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn" @click="handleClose()">取消</el-button>
        <el-button class="custom-dialog-btn primary" :loading="loading" @click="handleSubmit">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { kingdee } from '@/minix'
import { isNumber, isNumberLength } from '@/utils/validate'
import { getOrderChangeDetail, auditOrderChange, deleteOrderChangeV2 } from '@/api/kingdee/purchase/orderChange'
import SupplierSearchSelect from '@/components/SearchSelect/supplier'
import MaterialSearchSelect from '@/components/SearchSelect/material'
import { getSalesmanList } from '@/api/kingdee'

export default {
  name: 'Create',
  mixins: [kingdee],
  components: { SupplierSearchSelect, MaterialSearchSelect },
  data() {
    return {
      fid: undefined,
      form: {},
      rules: {
        fpurchaseorgid: [{ required: true, message: '请选择采购组织', trigger: 'change' }],
        fbilltypeid: [{ required: true, message: '请选择单据类型', trigger: 'change' }],
        fbusinesstype: [{ required: true, message: '请选择业务类型', trigger: 'change' }],
        fchangereason: [{ required: true, message: '请输入变更原因', trigger: 'blur' }],
        fdate: [{ required: true, message: '请选择变更日期', trigger: 'change' }],
        fmaterialid: [{ required: true, message: '请选择物料编码', trigger: 'change' }],
        fchangetype: [{ required: true, message: '请选择变更类型', trigger: 'change' }],
        funitid: [{ required: true, message: '请选择采购单位', trigger: 'change' }],
        fofbillno: [{ required: true, message: '请输入原订单编号', trigger: 'blur' }],
        fnrequireorgid: [{ required: true, message: '请选择需求组织', trigger: 'change' }]
      },
      title: '新增采购退料单',
      open: false,
      loading: false,
      isCreate: false,
      hasSuccessfully: false, // 是否已成功提交
      // 单据类型
      billTypeOptions: [{ label: '标准采购订单变更单', value: 'CGDDBGD01_SYS' }],
      // 业务类型
      businessTypeOptions: [
        { label: '标准采购', value: 'CG', target: 'CGDDBGD01_SYS' },
        { label: '标准委外', value: 'WW' },
        { label: '资产采购', value: 'ZCCG' },
        { label: '直运采购', value: 'ZYCG' },
        { label: '费用采购', value: 'FYCG' },
        { label: 'VMI采购', value: 'VMICG' },
        { label: '测试采购', value: 'cscg' },
        { label: '分销购销', value: 'DRPCG' }
      ],
      changeTypeOptions: [
        { label: '新增', value: 'A' },
        { label: '修改', value: 'B' },
        { label: '取消', value: 'C' }
      ],
      // 采购员
      purchaserOptions: []
    }
  },
  methods: {
    // 采购员远程方法
    ApplicantRemoteMethod() {
      const params = { OperatorType: 'CGY' }
      getSalesmanList(params).then(res => {
        const { code, msg, data } = res
        const { data: purchaserList } = data
        if (code == 200) {
          this.purchaserOptions = purchaserList
        } else this.$message.error(msg)
      })
    },
    // 创建默认实体对象
    createDefaultEntity(options = {}) {
      return {
        fmaterialid: options?.fmaterialid || undefined, // 物料编码
        fmaterialname: options?.fmaterialname || undefined, // 物料名称
        fmaterialmodel: options?.fmaterialmodel || undefined, // 规格型号
        fchangetype: options?.fchangetype || this.changeTypeOptions[0].value, // 变更类型
        funitid: options?.funitid || undefined, // 采购单位
        fofbillno: options?.fofbillno || undefined, // 原订单编号
        fnqty: options?.fnqty || undefined, // 新采购数量
        fndeliverydate: options?.fndeliverydate || this.parseTime(new Date(), '{y}-{m}-{d} 00:00:00'), // 新交货日期
        fnprice: options?.fnprice || undefined, // 新单价
        fgiveaway: options?.fgiveaway || undefined, // 是否赠品
        fnote: options?.fnote || undefined, // 备注
        fnpriceqty: options?.fnpriceqty || undefined, // 新计价数量
        fnstockqty: options?.fnstockqty || undefined, // 新库存单位数量
        fnrequireorgid: options?.fnrequireorgid || this.form.fpurchaseorgid // 新需求组织
      }
    },
    // 关闭
    beforeClose() {
      this.handleClose()
    },
    // 关闭
    async handleClose(flag = false) {
      if (this.fid && !this.isCreate && !this.hasSuccessfully) {
        try {
          await deleteOrderChangeV2({ id: this.fid })
          this.fid = undefined
          this.$nextTick(() => {
            this.open = false
            this.$emit('callBack', flag)
          })
        } catch (error) {
          this.$message.error(error.message)
        }
      } else {
        this.open = false
        this.$emit('callBack', flag)
      }
    },
    // 表单初始化
    reset() {
      this.form = {
        entities: [],
        fbilltypeid: undefined, // 单据类型
        fbusinesstype: undefined, // 业务类型
        fchangereason: undefined, // 变更原因
        fdate: undefined, // 变更日期
        fid: undefined, // 单据ID
        fpurchasedeptid: undefined, // 采购部门
        fpurchaseorgid: undefined, // 采购组织
        fpurchasergroupid: undefined, // 采购组
        fpurchaserid: undefined, // 采购员
        fsettlecurrid: undefined, // 结算币别
        fsupplierid: undefined, // 供应商
        fsuppliername: undefined // 供应商名称
      }
      this.resetForm('form')
      this.isCreate = false
      this.hasSuccessfully = false
    },
    // 新增采购订单变更单
    handleCreate() {
      this.reset()
      this.changeTypeOptions = this.changeTypeOptions.filter(item => item.value == 'A')
      this.form.fdate = new Date()
      this.form.entities = [this.createDefaultEntity()]
      this.form.fbilltypeid = this.billTypeOptions[0].value
      this.form.fbusinesstype = this.businessTypeOptions[0].value
      this.form.fsettlecurrid = this.CurrencyId[0].value
      this.isCreate = true
      this.title = '新增采购订单变更单'
      this.open = true
    },
    // 编辑采购订单变更单
    // prettier-ignore
    handleUpdate(row) {
      const { BillNo } = row
      if (!BillNo) return this.$message.warning('参数错误，请刷新页面重试')
      const loading = this.$loading({
        lock: true,
        text: '加载中...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      this.reset()
      getOrderChangeDetail({ billNo: BillNo }).then(res => {
        const { code, msg, data } = res
        if (code == 200) {
          const info = data?.result?.result || {}
          const detailEntities = info?.POChangeEntry || []
          const entities = detailEntities.map(item => ({
            fentryid: item?.Id || undefined, // 使用时需检查
            fmaterialid: item?.MaterialId?.Number || undefined, // 物料编码
            fmaterialname: (item?.MaterialId?.Name && this.getString(item?.MaterialId.Name)) || undefined, // 物料名称
            fmaterialmodel: (item?.MaterialId?.Specification && this.getString(item?.MaterialId.Specification)) || undefined, // 规格型号
            fchangetype: item?.ChangeType || undefined, // 变更类型
            funitid: item?.UnitId?.Number || undefined, // 采购单位
            fofbillno: item?.OrderFBillNo || undefined, // 原订单编号
            onqty: item?.OldQty || undefined, // 原采购数量
            fnqty: item?.NewQty || undefined, // 新采购数量
            ondeliverydate: item?.OldDeliveryDate || undefined, // 原交货日期
            fndeliverydate: item?.NewDeliveryDate || undefined, // 新交货日期
            onprice: item?.OldPrice || undefined, // 原单价
            fnprice: item?.NewPrice || undefined, // 新单价
            fgiveaway: item?.GiveAway || undefined, // 是否赠品
            fnote: item?.Note || undefined, // 备注
            fpriceunitid: item?.PRICEUNITID?.Number || undefined, // 计价单位
            onpriceqty: item?.OPRICEQTY || undefined, // 原计价数量
            fnpriceqty: item?.NPRICEQTY || undefined, // 新计价数量
            fstockunitid: item?.STOCKUNITID?.Number || undefined, // 库存单位
            onstockqty: item?.OSTOCKQTY || undefined, // 原库存单位数量
            fnstockqty: item?.NSTOCKQTY || undefined, // 新库存单位数量
            fnewbaseqty: item?.NSTOCKBASEQTY || undefined, // 新库存基本数量
            onrequireorgid: item?.ORequireOrgId?.Number || undefined, // 原需求组织
            fnrequireorgid: item?.NRequireOrgId?.Number || undefined // 新需求组织
          }))
          this.form = {
            entities,
            fbilltypeid: info?.FBillTypeID?.Number || undefined, // 单据类型
            fbusinesstype: info?.BusinessType || undefined, // 业务类型
            fchangereason: info?.ChangeReason || undefined, // 变更原因
            fdate: info?.Date || undefined, // 变更日期
            fid: info?.Id || undefined, // 单据ID
            fpurchasedeptid: info?.PurchaseDeptId?.Number || undefined, // 采购部门
            fpurchaseorgid: info?.PurchaseOrgId?.Number || undefined, // 采购组织
            fpurchasergroupid: info?.PurchaserGroupId?.Number || undefined, // 采购组
            fpurchaserid: info?.PurchaserId?.Number || undefined, // 采购员
            fsettlecurrid: info?.SettleCurrId?.Number || undefined, // 结算币别
            fsupplierid: info?.SupplierId?.Number || undefined, // 供应商
            fsuppliername: (info?.SupplierId?.Name && this.getString(info?.SupplierId.Name)) || undefined // 供应商名称
          }
          this.fid = info?.Id || undefined
          this.title = '编辑采购订单变更单'
          this.isCreate = true
          this.$nextTick(() => {
            this.ApplicantRemoteMethod()
          })
          this.open = true
        } else this.$message.error(msg)
      }).finally(() => {
        loading.close()
      })
    },
    // 回显单位
    formatUnitName(val) {
      const obj = this.UnitList.find(item => item.FNumber == val) || {}
      return obj.FName || ''
    },
    // 回显需求组织
    formatOrgName(val) {
      const obj = this.ApplicationOrgId.find(item => item.value == val) || {}
      return obj.label || ''
    },
    // 改变单据类型
    handleChangeBillType(val) {
      const obj = this.businessTypeOptions.find(item => item.target == val) || {}
      this.form.fbusinesstype = obj.value || undefined
    },
    // 改变采购组织
    handleChangePurchaseOrg(val) {
      this.form.fpurchaserid = undefined
      this.form.fsupplierid = undefined
      this.form.fsuppliername = undefined
      this.form.entities.forEach(item => {
        item.fnrequireorgid = val
      })
      this.ApplicantRemoteMethod()
    },
    // 供应商选择
    handleSupplierSelect(data) {
      this.$set(this.form, 'fsupplierid', data.FNumber)
      this.$set(this.form, 'fsuppliername', data.FName)
    },
    // 添加明细
    handleAdd() {
      this.form.entities.push(this.createDefaultEntity())
    },
    // 删除明细
    handleDelete(index) {
      this.form.entities.splice(index, 1)
    },
    // 选择物料回调
    handleMaterialSearchSelect(event, row) {
      const obj = this.UnitList.find(item => item.FName == event.Unit)
      this.$set(row, 'fmaterialid', event.Number)
      this.$set(row, 'fmaterialname', event.Name)
      this.$set(row, 'fmaterialmodel', event.Specification)
      this.$set(row, 'funitid', obj?.FNumber || '')
      this.$set(row, 'fpriceunitid', obj?.FNumber || '')
      this.$set(row, 'fstockunitid', obj?.FNumber || '')
      const hasEmpty = this.form.entities.filter(item => !item.fmaterialid)
      if (hasEmpty.length == 0) this.form.entities.push(this.createDefaultEntity())
    },
    // 改变采购单位
    handleUnitChange(event, row) {
      this.$set(row, 'fpriceunitid', event)
      this.$set(row, 'fstockunitid', event)
    },
    // 改变新采购数量
    handleNewQtyChange(row) {
      const { fnqty = 0 } = row
      this.$set(row, 'fnpriceqty', fnqty)
      this.$set(row, 'fnstockqty', fnqty)
      this.$set(row, 'fnewbaseqty', fnqty)
    },
    // 获取合计（实退数量、补料数量、扣款数量、金额）
    getSummary(param) {
      const { columns, data } = param
      const result = columns.map((column, index) => {
        if (index === 0) {
          return '合计'
        }
        if (column.property === 'onqty' || column.property === 'fnqty' || column.property === 'onpriceqty' || column.property === 'fnpriceqty' || column.property === 'onstockqty' || column.property === 'fnstockqty') {
          return `${this.calculateTotal(data, column.property)}`
        }
        return ''
      })
      return result
    },
    // 计算合计
    calculateTotal(data, key) {
      return data.reduce((total, item) => {
        if (key === 'onqty' || key === 'fnqty' || key === 'onpriceqty' || key === 'fnpriceqty' || key === 'onstockqty' || key === 'fnstockqty') {
          const value = parseFloat(item[key]) || 0
          return parseFloat((total + value).toFixed(5))
        }
        return total
      }, 0)
    },
    // 提交
    // prettier-ignore
    handleSubmit() {
      if (this.form.entities.length > 1) this.form.entities = this.form.entities.filter(item => item.fmaterialid)
      this.$nextTick(() => {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.loading = true
            let data = JSON.parse(JSON.stringify(this.form))
            // 处理是否赠品勾选时，单价和金额设置为0
            data.entities.forEach(item => {
              if (item.fgiveaway) {
                item.fnprice = 0
              }
            })
            auditOrderChange(data).then(res=> {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('操作成功')
                this.hasSuccessfully = true
                this.open = false
                this.$emit('callBack', true)
              } else this.$message.error(msg)
            }).finally(() => {
              this.loading = false
            })
          }
        })
      })
    },
    // 物料详情
    handleMaterialNumber(number) {
      if (number) this.$parent.handleMaterialNumber(number)
    }
  }
}
</script>
<style scoped lang="scss">
@import '@/assets/styles/custom-new.scss';
::v-deep {
  .custom-table {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    border-bottom: 0;
    margin-bottom: 20px;
    .el-form-item {
      margin-top: 10px !important;
      margin-bottom: 10px !important;
      .el-form-item__error {
        top: 95%;
        padding-top: 0;
      }
    }
    .el-input--prefix .el-input__inner {
      padding-left: 20px;
      padding-right: 20px;
    }
    .el-input--prefix.el-date-editor--date .el-input__inner {
      padding-left: 30px;
    }
    .el-date-editor--datetime .el-input__inner {
      padding-left: 30px;
      padding-right: 30px;
    }
  }
}
</style>
