<template>
  <div class="newBox bgcf9 vh-85">
    <!-- 搜索 -->
    <div class="custom-search flex" style="padding-top: 18px" :style="{ justifyContent: showSearch ? 'space-between' : 'flex-end' }">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" @submit.native.prevent>
        <el-form-item label="编号" prop="billNo">
          <el-input v-model="queryParams.billNo" placeholder="请输入编号" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="物料编码" prop="materialNumber">
          <el-input v-model="queryParams.materialNumber" placeholder="请输入物料编码" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="供应商名称" prop="supplierName">
          <el-input v-model="queryParams.supplierName" placeholder="请输入供应商名称" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="单据状态" prop="documentStatus">
          <el-select v-model="queryParams.documentStatus" placeholder="请选择单据状态" clearable>
            <el-option v-for="item in DocumentStatusOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="采购组织" prop="purchaseOrg">
          <el-select v-model="queryParams.purchaseOrg" placeholder="请选择采购组织" clearable>
            <el-option v-for="item in ApplicationOrgId" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="small" @click="handleResetQuery">重置</el-button>
          <el-button type="primary" icon="el-icon-plus" size="small" @click="handleCreate">新增采购变更</el-button>
        </el-form-item>
      </el-form>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns" isSetitem @updateColumns="updateColumns"></right-toolbar>
    </div>
    <!-- 列表 -->
    <div style="padding: 15px 20px">
      <el-table v-loading="loading" ref="allTable" border :data="list" row-key="id" style="width: 100%" class="custom-table" :span-method="objectSpanMethod">
        <el-table-column align="center" type="index" label="序号" v-if="columns[0].visible"></el-table-column>
        <!-- 编号 -->
        <el-table-column align="center" prop="BillNo" label="编号" show-overflow-tooltip v-if="columns[1].visible">
          <template slot-scope="{ row }">
            <span class="table-link" @click="handleDetail(row)">{{ row.BillNo }}</span>
          </template>
        </el-table-column>
        <!-- 供应商-->
        <el-table-column align="center" prop="Supplier" label="供应商" show-overflow-tooltip v-if="columns[2].visible"></el-table-column>
        <!-- 单据状态 -->
        <el-table-column align="center" prop="DocumentStatus" label="单据状态" show-overflow-tooltip v-if="columns[3].visible">
          <template slot-scope="{ row }">{{ getDocumentStatusLabel(row.DocumentStatus) }}</template>
        </el-table-column>
        <!-- 采购组织 -->
        <el-table-column align="center" prop="PurchaseOrg" label="采购组织" show-overflow-tooltip v-if="columns[4].visible"></el-table-column>
        <!-- 物料编码-->
        <el-table-column align="center" prop="MaterialNumber" label="物料编码" show-overflow-tooltip v-if="columns[5].visible">
          <template slot-scope="{ row }">
            <span class="table-link" @click="handleMaterialNumber(row.MaterialNumber)">{{ row.MaterialNumber }}</span>
          </template>
        </el-table-column>
        <!-- 物料名称-->
        <el-table-column align="center" prop="MaterialName" label="物料名称" show-overflow-tooltip v-if="columns[6].visible"></el-table-column>
        <!-- 规格型号 -->
        <el-table-column align="center" prop="MaterialModel" label="规格型号" show-overflow-tooltip v-if="columns[7].visible"></el-table-column>
        <!-- 辅助属性(未明确) -->
        <el-table-column align="center" prop="AuxiliaryAttribute" label="辅助属性" show-overflow-tooltip v-if="columns[8].visible"></el-table-column>
        <!-- 采购单位 -->
        <el-table-column align="center" prop="UnitName" label="采购单位" show-overflow-tooltip v-if="columns[9].visible"></el-table-column>
        <!-- 新采购数量 -->
        <el-table-column align="center" prop="Nqty" label="新采购数量" show-overflow-tooltip v-if="columns[10].visible"></el-table-column>
        <!-- 新交货日期(未明确) -->
        <el-table-column align="center" prop="NDeliveryDate" label="新交货日期" show-overflow-tooltip v-if="columns[11].visible">
          <template slot-scope="{ row }">{{ parseTime(row.NDeliveryDate, '{y}-{m}-{d}') }}</template>
        </el-table-column>
        <!-- 新单价 -->
        <el-table-column align="center" prop="NPrice" label="新单价" show-overflow-tooltip v-if="columns[12].visible"></el-table-column>
        <!-- 原订单编号 -->
        <el-table-column align="center" prop="OBillNo" label="原订单编号" show-overflow-tooltip v-if="columns[13].visible"></el-table-column>
        <!-- 操作 -->
        <el-table-column align="center" label="操作">
          <template slot-scope="{ row }">
            <el-button type="text" icon="el-icon-edit" @click="handleUpdate(row)" :disabled="row.DocumentStatus !== 'A' && row.DocumentStatus !== 'D'">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="custom-pagination">
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.limit" @pagination="getList" />
      </div>
    </div>
    <!-- 详情 -->
    <order-change-detail ref="orderChangeDetail" @callBack="handleDetailCallBack" @update="handleUpdate" v-if="showDetail" />
    <!-- 物料详情 -->
    <material-detail ref="materialDetail" v-if="showMaterialDetail" />
    <!-- 新增 -->
    <create ref="create" @callBack="handleDetailCallBack" v-if="showCreate" />
  </div>
</template>
<script>
import { getOrderChangeList } from '@/api/kingdee/purchase/orderChange'
import OrderChangeDetail from '@/views/kingdee/purchase/orderChange/detail'
import MaterialDetail from '@/views/kingdee/material/detail'
import Create from './create'
import { kingdee } from '@/minix'

export default {
  name: 'OrderChange',
  mixins: [kingdee],
  components: { OrderChangeDetail, MaterialDetail, Create },
  data() {
    return {
      // 搜索
      queryParams: {
        pageNum: 1, // 当前页码
        startRow: 0, // 开始行索引
        limit: 10, // 最大行数
        purchaseOrg: undefined, // 采购组织
        billNo: undefined, // 编号
        materialNumber: undefined, // 物料编码
        documentStatus: undefined, // 单据状态
        supplierName: undefined // 供应商
      },
      // 列表
      list: [],
      total: 0,
      loading: false,
      showDetail: false, // 详情
      showMaterialDetail: false, // 物料详情
      showCreate: false, // 新增
      // 列表显隐
      showSearch: true,
      // 列信息
      columns: [
        { key: 0, label: `序号`, visible: true },
        { key: 1, label: `编号`, visible: true },
        { key: 2, label: `供应商`, visible: true },
        { key: 3, label: `单据状态`, visible: true },
        { key: 4, label: `采购组织`, visible: true },
        { key: 5, label: `物料编码`, visible: true },
        { key: 6, label: `物料名称`, visible: true },
        { key: 7, label: `规格型号`, visible: true },
        { key: 8, label: `辅助属性`, visible: true },
        { key: 9, label: `采购单位`, visible: true },
        { key: 10, label: `新采购数量`, visible: true },
        { key: 11, label: `新交货日期`, visible: true },
        { key: 12, label: `新单价`, visible: true },
        { key: 13, label: `原订单编号`, visible: true }
      ]
    }
  },
  computed: {
    // 获取当前用户ID
    userId() {
      return this.$store.getters.info.userId
    }
  },
  created() {
    const columns = localStorage.getItem(this.userId + '.orderChangeColumns')
    if (columns) this.columns = JSON.parse(columns)
    // 获取列表
    this.getList()
  },
  methods: {
    // 改变显隐列缓存
    updateColumns(data = []) {
      localStorage.setItem(this.userId + '.orderChangeColumns', JSON.stringify(data))
    },
    // 列表
    getList() {
      this.loading = true
      this.queryParams.startRow = Math.max(0, (this.queryParams.pageNum - 1) * this.queryParams.limit)
      const query = { ...this.queryParams }
      delete query.pageNum
      getOrderChangeList(query).then(res => {
        const { code, data, msg } = res
        if (code === 200) {
          const { data: list, total } = data
          this.list = list
          this.total = total
        } else this.$message.error(msg)
        this.loading = false
      })
    },
    // 相同字段合并
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      const visibleColumns = this.columns.filter(col => col.visible)
      const currentColumnKey = visibleColumns[columnIndex]?.key
      if ((currentColumnKey >= 1 && currentColumnKey <= 4) || currentColumnKey === 13) {
        const _row = this.calculateSpan(this.list, 'BillNo')[rowIndex]
        const _col = _row > 0 ? 1 : 0
        return {
          rowspan: _row,
          colspan: _col
        }
      }
    },
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 重置搜索
    handleResetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 查看物料
    handleMaterialNumber(number) {
      this.showMaterialDetail = true
      this.$nextTick(() => {
        this.$refs.materialDetail.getInfo(number)
      })
    },
    // 查看详情
    handleDetail(row) {
      this.showDetail = true
      this.$nextTick(() => {
        this.$refs.orderChangeDetail.getInfo(row)
      })
    },
    // 详情回调
    handleDetailCallBack(flag) {
      this.showDetail = false
      this.showCreate = false
      if (flag) this.getList()
    },
    // 新增
    handleCreate() {
      this.showCreate = true
      this.$nextTick(() => {
        this.$refs.create.handleCreate()
      })
    },
    // 编辑
    handleUpdate(row) {
      this.showCreate = true
      this.$nextTick(() => {
        this.$refs.create.handleUpdate(row)
      })
    },
    // 更新
    handleUpdate(data) {
      const { billNo, newStatus } = data
      const index = this.list.findIndex(item => item.BillNo === billNo)
      if (index !== -1) this.list[index].DocumentStatus = newStatus
    }
  }
}
</script>
<style scoped lang="scss">
@import '@/assets/styles/custom-new.scss';
</style>
