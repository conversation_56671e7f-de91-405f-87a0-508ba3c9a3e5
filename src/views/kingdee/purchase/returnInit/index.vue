<template>
  <div class="newBox bgcf9 vh-85">
    <!-- 搜索 -->
    <div class="custom-search" style="padding-top: 18px">
      <div class="flex" :style="{ justifyContent: showSearch ? 'space-between' : 'flex-end' }">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" @submit.native.prevent>
          <el-form-item label="单据编号" prop="billNo">
            <el-input v-model="queryParams.billNo" placeholder="请输入单据编号" clearable @keyup.enter.native="handleQuery" />
          </el-form-item>
          <el-form-item label="物料编码" prop="materialNumber">
            <el-input v-model="queryParams.materialNumber" placeholder="请输入物料编码" clearable @keyup.enter.native="handleQuery" />
          </el-form-item>
          <el-form-item label="供应商名称" prop="supplierName">
            <el-input v-model="queryParams.supplierName" placeholder="请输入供应商名称" clearable @keyup.enter.native="handleQuery" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="small" @click="handleResetQuery">重置</el-button>
          </el-form-item>
        </el-form>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
      </div>
    </div>
    <!-- 列表 -->
    <div style="padding: 15px 20px">
      <el-table v-loading="loading" ref="allTable" border :data="list" row-key="id" style="width: 100%" class="custom-table" :span-method="objectSpanMethod">
        <el-table-column align="center" type="index" label="序号" v-if="columns[0].visible"></el-table-column>
        <!-- 单据编号 -->
        <el-table-column align="center" prop="BillNo" label="单据编号" show-overflow-tooltip v-if="columns[1].visible">
          <template slot-scope="{ row }">
            <span class="table-link" @click="handleDetail(row)">{{ row.BillNo }}</span>
          </template>
        </el-table-column>
        <!-- 单据状态 -->
        <el-table-column align="center" prop="DocumentStatus" label="单据状态" show-overflow-tooltip v-if="columns[2].visible">
          <template slot-scope="{ row }">{{ getDocumentStatusLabel(row.DocumentStatus) }}</template>
        </el-table-column>
        <!-- 退料日期 -->
        <el-table-column align="center" prop="Date" label="退料日期" show-overflow-tooltip v-if="columns[3].visible">
          <template slot-scope="{ row }">{{ parseTime(row.Date, '{y}-{m}-{d}') }}</template>
        </el-table-column>
        <!-- 供应商-->
        <el-table-column align="center" prop="Supplier" label="供应商" show-overflow-tooltip v-if="columns[4].visible"></el-table-column>
        <!-- 退料组织 -->
        <el-table-column align="center" prop="StockOrg" label="退料组织" show-overflow-tooltip v-if="columns[5].visible"></el-table-column>
        <!-- 物料编码-->
        <el-table-column align="center" prop="MaterialNumber" label="物料编码" show-overflow-tooltip v-if="columns[6].visible">
          <template slot-scope="{ row }">
            <span class="table-link" @click="handleMaterialNumber(row.MaterialNumber)">{{ row.MaterialNumber }}</span>
          </template>
        </el-table-column>
        <!-- 物料名称-->
        <el-table-column align="center" prop="MaterialName" label="物料名称" show-overflow-tooltip v-if="columns[7].visible"></el-table-column>
        <!-- 库存单位-->
        <el-table-column align="center" prop="UnitName" label="库存单位" show-overflow-tooltip v-if="columns[8].visible"></el-table-column>
        <!-- 实退数量 -->
        <el-table-column align="center" prop="RMREALQTY" label="实退数量" show-overflow-tooltip v-if="columns[9].visible"></el-table-column>
        <!-- 仓库-->
        <el-table-column align="center" prop="StockName" label="仓库" show-overflow-tooltip v-if="columns[10].visible"></el-table-column>
        <!-- 仓位(未明确) -->
        <el-table-column align="center" prop="StockLocName" label="仓位" show-overflow-tooltip v-if="columns[11].visible"></el-table-column>
        <!-- 开票结束状态(未明确) -->
        <el-table-column align="center" prop="InvoicedStatus" label="开票结束状态" show-overflow-tooltip v-if="columns[12].visible">
          <template slot-scope="{ row }">{{ row.InvoicedStatus === 'A' ? '正常' : '已结束' }}</template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="custom-pagination">
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.limit" @pagination="getList" />
      </div>
    </div>
    <!-- 详情 -->
    <return-init-detail ref="returnInitDetail" @callBack="getList" v-if="showDetail" />
    <!-- 物料详情 -->
    <material-detail ref="materialDetail" v-if="showMaterialDetail" />
  </div>
</template>
<script>
import { getInitPurchaseReturnList } from '@/api/kingdee/purchase/returnInit'
import ReturnInitDetail from '@/views/kingdee/purchase/returnInit/detail'
import MaterialDetail from '@/views/kingdee/material/detail'
import { kingdee } from '@/minix'

export default {
  mixins: [kingdee],
  components: { ReturnInitDetail, MaterialDetail },
  data() {
    return {
      // 搜索
      queryParams: {
        pageNum: 1,
        startRow: 0, // 开始行索引
        limit: 10, // 最大行数
        stockOrg: undefined, // 退料组织
        billNo: undefined, // 单据编号
        materialNumber: undefined, // 物料编码
        documentStatus: undefined, // 单据状态
        supplierName: undefined // 供应商
      },
      // 列表
      list: [],
      total: 0,
      loading: false,
      showDetail: false, // 详情
      showMaterialDetail: false, // 物料详情
      // 列表显隐
      showSearch: true,
      // 列信息
      columns: [
        { key: 0, label: `序号`, visible: true },
        { key: 1, label: `单据编号`, visible: true },
        { key: 2, label: `单据状态`, visible: true },
        { key: 3, label: `退料日期`, visible: true },
        { key: 4, label: `供应商`, visible: true },
        { key: 5, label: `退料组织`, visible: true },
        { key: 6, label: `物料编码`, visible: true },
        { key: 7, label: `物料名称`, visible: true },
        { key: 8, label: `库存单位`, visible: true },
        { key: 9, label: `实退数量`, visible: true },
        { key: 10, label: `仓库`, visible: true },
        { key: 11, label: `仓位`, visible: true },
        { key: 12, label: `开票结束状态`, visible: true }
      ]
    }
  },
  created() {
    // 获取列表
    this.getList()
  },
  methods: {
    // 列表
    getList() {
      this.loading = true
      this.queryParams.startRow = Math.max(0, (this.queryParams.pageNum - 1) * this.queryParams.limit)
      const query = { ...this.queryParams }
      delete query.pageNum
      getInitPurchaseReturnList(query).then(res => {
        const { code, data, msg } = res
        if (code === 200) {
          const { data: list, total } = data
          this.list = list
          this.total = total
        } else this.$message.error(msg)
        this.loading = false
      })
    },
    // 相同字段合并
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      const visibleColumns = this.columns.filter(col => col.visible)
      const currentColumnKey = visibleColumns[columnIndex]?.key
      if (currentColumnKey >= 1 && currentColumnKey <= 5) {
        const _row = this.calculateSpan(this.list, 'BillNo')[rowIndex]
        const _col = _row > 0 ? 1 : 0
        return {
          rowspan: _row,
          colspan: _col
        }
      }
    },
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 重置搜索
    handleResetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 查看物料
    handleMaterialNumber(number) {
      this.showMaterialDetail = true
      this.$nextTick(() => {
        this.$refs.materialDetail.getInfo(number)
      })
    },
    // 查看详情
    handleDetail(row) {
      this.showDetail = true
      this.$nextTick(() => {
        this.$refs.returnInitDetail.getInfo(row)
      })
    }
  }
}
</script>
<style scoped lang="scss">
@import '@/assets/styles/custom-new.scss';
</style>
