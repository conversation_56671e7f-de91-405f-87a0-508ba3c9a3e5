<template>
  <div>
    <el-dialog v-dialogDragBox title="采购申请单详情" :visible.sync="open" width="90%" class="custom-dialog" :before-close="handleBeforeClose">
      <div style="padding: 0 20px">
        <div class="kindeeButton" :key="info.DocumentStatus">
          <el-button type="primary" size="medium" v-if="info.DocumentStatus == 'A' || info.DocumentStatus == 'D'" @click="handleKingdeeDo('submit')">提交</el-button>
          <el-button type="primary" size="medium" disabled v-else>提交</el-button>
          <el-button type="warning" size="medium" v-if="info.DocumentStatus == 'B'" @click="handleKingdeeDo('audit')">审核</el-button>
          <el-button type="warning" size="medium" disabled v-else>审核</el-button>
          <el-button type="info" size="medium" v-if="info.DocumentStatus == 'B'" @click="handleKingdeeDo('revoke')">撤销</el-button>
          <el-button type="info" size="medium" disabled v-else>撤销</el-button>
          <el-button type="danger" size="medium" v-if="info.DocumentStatus == 'A' || info.DocumentStatus == 'D'" @click="handleKingdeeDo('delete')">删除</el-button>
          <el-button type="danger" size="medium" disabled v-else>删除</el-button>
          <el-button type="warning" size="medium" v-if="info.DocumentStatus == 'C'" @click="handleKingdeeDo('unAudit')">反审</el-button>
          <el-button type="warning" size="medium" disabled v-else>反审</el-button>
          <el-button type="success" size="medium" v-if="info.DocumentStatus == 'C'" @click="handleKingdeeDo('push')">下推</el-button>
          <el-button type="success" size="medium" disabled v-else>下推</el-button>
        </div>
        <el-descriptions border :column="4" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 4 - 7em - 22px)' }">
          <template slot="title">
            <div class="customTitle">基本信息</div>
          </template>
          <!-- 申请组织 -->
          <el-descriptions-item label="申请组织">{{ info.ApplicationOrgId && getString(info.ApplicationOrgId.Name) }}</el-descriptions-item>
          <!-- 单据类型 -->
          <el-descriptions-item label="单据类型">{{ info.BillTypeID && getString(info.BillTypeID.Name) }}</el-descriptions-item>
          <!-- 申请部门 -->
          <el-descriptions-item label="申请部门">{{ info.ApplicationDeptId && getString(info.ApplicationDeptId.Name) }}</el-descriptions-item>
          <!-- 单据编号 -->
          <el-descriptions-item label="单据编号">{{ info.BillNo }}</el-descriptions-item>
          <!-- 申请人 -->
          <el-descriptions-item label="申请人">{{ info.ApplicantId && getString(info.ApplicantId.Name) }}</el-descriptions-item>
          <!-- 申请日期 -->
          <el-descriptions-item label="申请日期">{{ parseTime(info.ApplicationDate, '{y}-{m}-{d}') }}</el-descriptions-item>
          <!-- 币别 -->
          <el-descriptions-item label="币别">{{ info.CurrencyId && getString(info.CurrencyId.Name) }}</el-descriptions-item>
          <!-- 申请类型 -->
          <el-descriptions-item label="申请类型">{{ info.RequestType && formatRequestType(info.RequestType) }}</el-descriptions-item>
          <!-- 验收方式 -->
          <el-descriptions-item label="验收方式">{{ info.FACCTYPE && formatACCTYPE(info.FACCTYPE) }}</el-descriptions-item>
          <!-- 单据状态 -->
          <el-descriptions-item label="单据状态">{{ getDocumentStatusLabel(info.DocumentStatus) }}</el-descriptions-item>
          <!-- 备注 -->
          <el-descriptions-item label="备注" :span="4">{{ info.Note }}</el-descriptions-item>
        </el-descriptions>
        <div class="customTitle">明细信息</div>
        <el-table ref="detailTable" :data="info.ReqEntry" class="custom-table" highlight-current-row @current-change="handleCurrentChange" :setCurrentRow="setCurrentRow">
          <!-- 序号 -->
          <el-table-column type="index" label="序号" align="center"></el-table-column>
          <!-- 需求组织 -->
          <el-table-column label="需求组织" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.RequireOrgId && getString(scope.row.RequireOrgId.Name) }}</template>
          </el-table-column>
          <!-- 物料编号 -->
          <el-table-column label="物料编号" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.MaterialId && scope.row.MaterialId.Number }}</template>
          </el-table-column>
          <!-- 物料名称 -->
          <el-table-column label="物料名称" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.MaterialId && getString(scope.row.MaterialId.Name) }}</template>
          </el-table-column>
          <!-- 规格型号 -->
          <el-table-column label="规格型号" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.MaterialId && getString(scope.row.MaterialId.Specification) }}</template>
          </el-table-column>
          <!-- 申请单位 -->
          <el-table-column label="申请单位" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.UnitID && getString(scope.row.UnitID.Name) }}</template>
          </el-table-column>
          <!-- 申请数量 -->
          <el-table-column prop="ReqQty" label="申请数量" align="center" show-overflow-tooltip></el-table-column>
          <!-- 明细物品 -->
          <el-table-column prop="DetailItem" label="明细物品" align="center" show-overflow-tooltip>
            <template slot-scope="scope">待确认</template>
          </el-table-column>
          <!-- 颜色 -->
          <el-table-column prop="Color" label="颜色" align="center" show-overflow-tooltip>
            <template slot-scope="scope">待确认</template>
          </el-table-column>
          <!-- 批准数量 -->
          <el-table-column prop="ApproveQty" label="批准数量" align="center" show-overflow-tooltip></el-table-column>
          <!-- 到货日期 -->
          <el-table-column prop="DeliveryDate" label="到货日期" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ parseTime(scope.row.ArrivalDate) }}</template>
          </el-table-column>
          <!-- 采购组织 -->
          <el-table-column label="采购组织" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.PurchaseOrgId && getString(scope.row.PurchaseOrgId.Name) }}</template>
          </el-table-column>
          <!-- 建议供应商 -->
          <el-table-column label="建议供应商" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.SuggestSupplierId && getString(scope.row.SuggestSupplierId.Name) }}</template>
          </el-table-column>
          <!-- 收料组织 -->
          <el-table-column label="收料组织" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.ReceiveOrgId && getString(scope.row.ReceiveOrgId.Name) }}</template>
          </el-table-column>
          <!-- 计价单位 -->
          <el-table-column label="计价单位" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.PriceUnitId && getString(scope.row.PriceUnitId.Name) }}</template>
          </el-table-column>
          <!-- 计价数量 -->
          <el-table-column prop="PriceUnitQty" label="计价数量" align="center" show-overflow-tooltip></el-table-column>
          <!-- 备注 -->
          <el-table-column prop="EntryNote" label="备注" align="center" show-overflow-tooltip></el-table-column>
          <!-- 当前库存 -->
          <el-table-column prop="F_SCMJ_Qty" label="当前库存" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.F_SCMJ_Qty || '' }}</template>
          </el-table-column>
          <!-- 单价 -->
          <el-table-column prop="EvaluatePrice" label="单价" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.EvaluatePrice || '' }}</template>
          </el-table-column>
          <!-- 可发库存 -->
          <el-table-column prop="F_SCMJ_Qty1" label="可发库存" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.F_SCMJ_Qty1 || '' }}</template>
          </el-table-column>
          <!-- 含税单价 -->
          <el-table-column prop="TAXPRICE" label="含税单价" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.TAXPRICE || '' }}</template>
          </el-table-column>
          <!-- 税率% -->
          <el-table-column prop="TAXRATE" label="税率%" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.TAXRATE || '' }}</template>
          </el-table-column>
          <!-- 金额 -->
          <el-table-column prop="Amount" label="金额" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.Amount || '' }}</template>
          </el-table-column>
          <!-- 含税金额 -->
          <el-table-column prop="ReqAmount" label="含税金额" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.ReqAmount || '' }}</template>
          </el-table-column>
          <!-- 库存单位 -->
          <el-table-column label="库存单位" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.REQSTOCKUNITID && getString(scope.row.REQSTOCKUNITID.Name) }}</template>
          </el-table-column>
          <!-- 库存单位数量 -->
          <el-table-column prop="REQSTOCKQTY" label="库存单位数量" align="center" show-overflow-tooltip min-width="100"></el-table-column>
        </el-table>
      </div>
    </el-dialog>
    <!-- 新增采购订单 -->
    <purchase-order-create ref="purchaseOrderCreate" @callBack="orderCreateCallBack" v-if="showOrderCreate" />
    <!-- 下推弹出框 -->
    <el-dialog v-dialogDragBox title="选择单据" :visible.sync="pushOpen" width="580px" class="custom-dialog">
      <div style="padding: 0 20px">
        <el-form ref="pushForm" :model="pushForm" :rules="pushFormRules" label-width="80px">
          <el-form-item label="" label-width="0" prop="target">
            <el-radio-group v-model="pushForm.target" v-removeAriaHidden @change="handleTargetChange">
              <el-row :gutter="10" class="custom-push-target">
                <el-col :span="12" v-for="item in pushTarget" :key="item.value">
                  <el-radio :label="item.value">{{ item.label }}</el-radio>
                </el-col>
              </el-row>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="转换规则" prop="ruleId">
            <el-select v-model="pushForm.ruleId" placeholder="请选择转换规则" style="width: 100%">
              <el-option v-for="item in calculateRule" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="单据类型" prop="targetBillTypeId">
            <el-select v-model="pushForm.targetBillTypeId" placeholder="请选择单据类型" style="width: 100%">
              <el-option v-for="item in calculateBillType" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn small" @click="pushOpen = false">取消</el-button>
        <el-button class="custom-dialog-btn small primary" @click="handlePushSubmit">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { auditPurchaseApplyV2, cancelPurchaseApplyV2, deletePurchaseApplyV2, getPurchaseApplyDetailV2, pushPurchaseApplyV2, submitPurchaseApplyV2, unAuditPurchaseApplyV2 } from '@/api/kingdee/purchase'
import { kingdee } from '@/minix'
import PurchaseOrderCreate from '@/views/kingdee/purchase/order/create'

export default {
  name: 'InventoryDetail',
  components: { PurchaseOrderCreate },
  mixins: [kingdee],
  data() {
    return {
      open: false,
      info: {},
      setCurrentRow: {},
      // 申请类型Options
      RequestTypeOptions: [
        { value: 'Material', label: '物料' },
        { value: 'Property', label: '资产' },
        { value: 'Expense', label: '费用' },
        { value: 'ZYSQ', label: '直运' }
      ],
      // 验收方式Options
      ACCTYPEOptions: [
        { value: 'Q', label: '数量验收' },
        { value: 'A', label: '金额验收' },
        { value: 'R', label: '比例验收' }
      ],
      // 新增采购订单
      showOrderCreate: false,
      // 下推
      pushOpen: false,
      pushForm: {},
      pushFormRules: {
        target: [{ required: true, message: '请选择下推目标', trigger: 'change' }],
        ruleId: [{ required: true, message: '请选择转换规则', trigger: 'change' }],
        targetBillTypeId: [{ required: true, message: '请选择单据类型', trigger: 'change' }]
      },
      // 下推目标
      pushTarget: [{ value: 'PUR_PurchaseOrder', label: '采购订单' }],
      // 下推转换规则
      pushRuleOptions: [{ label: '采购申请单至采购订单', value: 'PUR_Requisition-PUR_PurchaseOrder', target: 'PUR_PurchaseOrder' }],
      // 下推单据类型
      pushBillTypeAllOptions: [
        { label: '资产采购订单', value: 'b0677860cd16433895be5f520086b69f', target: 'PUR_Requisition-PUR_PurchaseOrder', billType: 'Property' },
        { label: '标准采购订单', value: '83d822ca3e374b4ab01e5dd46a0062bd', target: 'PUR_Requisition-PUR_PurchaseOrder', billType: 'Material' },
        { label: '直运采购订单', value: 'b8df755fd92b4c2baedef2439c29f793', target: 'PUR_Requisition-PUR_PurchaseOrder', billType: 'ZYSQ' },
        { label: '费用采购订单', value: 'b1985f24f35841fdb418329af6ed7bd0', target: 'PUR_Requisition-PUR_PurchaseOrder', billType: 'Expense' }
      ]
    }
  },
  computed: {
    // 计算规则
    calculateRule() {
      return this.pushRuleOptions.filter(item => item.target === this.pushForm.target)
    },
    // 计算单据类型
    calculateBillType() {
      const { ruleId } = this.pushForm
      const billType = this.info?.RequestType || ''
      const arr = this.pushBillTypeAllOptions.filter(item => item.target === ruleId) || []
      return arr.filter(item => item.billType === billType) || []
    }
  },
  methods: {
    // 格式化申请类型
    formatRequestType(type) {
      return this.RequestTypeOptions.find(item => item.value === type)?.label || ''
    },
    // 格式化验收方式
    formatACCTYPE(type) {
      return this.ACCTYPEOptions.find(item => item.value === type)?.label || ''
    },
    // 获取详情
    getInfo(row = {}) {
      const { BillNo } = row
      if (!BillNo) return this.$message.error('参数错误,请稍后重试')
      getPurchaseApplyDetailV2({ billNo: BillNo }).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          const { result } = data
          this.open = !!result?.result
          this.info = result?.result || {}
          this.$nextTick(() => {
            this.setCurrentRow = result?.result?.ReqEntry?.[0] || {}
            if (this.$refs.detailTable) {
              this.$refs.detailTable.setCurrentRow(this.setCurrentRow)
              this.$refs.detailTable.bodyWrapper.scrollLeft = 0
            }
          })
        } else this.$message.error(msg)
      })
    },
    // 当前行
    handleCurrentChange(val) {
      this.setCurrentRow = val
    },
    // 关闭
    handleBeforeClose() {
      this.handleClose()
      this.$emit('update', { billNo: this.info.BillNo, newStatus: this.info.DocumentStatus })
    },
    // 取消
    handleClose(flag = false) {
      this.open = false
      this.$emit('callBack', flag)
    },
    // 金蝶提交、审核、撤销、反审核、删除
    // prettier-ignore
    handleKingdeeDo(type = '') {
      const number = this.info.BillNo
      if (!number) return this.$message.error('参数错误,请稍后重试')
      switch (type) {
        case 'submit':
          // 提交
          this.$modal.confirm('确认要提交该采购申请单吗？').then(() => {
            submitPurchaseApplyV2({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('提交成功')
                this.$set(this.info, 'DocumentStatus', 'B')
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'audit':
          // 审核
          this.$modal.confirm('确认要审核该采购申请单吗？').then(() => {
            auditPurchaseApplyV2({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('审核成功')
                this.$set(this.info, 'DocumentStatus', 'C')
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'revoke':
          // 撤销
          this.$modal.confirm('确认要撤销该采购申请单吗？').then(() => {
            cancelPurchaseApplyV2({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('撤销成功')
                this.$set(this.info, 'DocumentStatus', 'D')
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'unAudit':
          // 反审核
          this.$modal.confirm('确认要反审核该采购申请单吗？').then(() => {
            unAuditPurchaseApplyV2({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('反审核成功')
                this.$set(this.info, 'DocumentStatus', 'D')
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'delete':
          // 删除
          this.$modal.confirm('确认要删除该采购申请单吗？').then(() => {
            deletePurchaseApplyV2({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('删除成功')
                this.handleClose(true)
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'push':
          // 下推
          this.initPushForm()
          this.pushForm.target = 'PUR_PurchaseOrder'
          this.handleTargetChange('PUR_PurchaseOrder')
          break
      }
    },
    // 初始化下推表单
    initPushForm() {
      this.pushForm = {
        number: this.info.BillNo,
        target: undefined,
        ruleId: undefined,
        targetBillTypeId: undefined
      }
      this.$nextTick(() => {
        this.resetForm('pushForm')
      })
      this.pushOpen = true
    },
    // 切换下推单据
    handleTargetChange(val) {
      if (val === 'PUR_PurchaseOrder') {
        this.setupPurchaseOrderPush()
        return
      }
      this.clearPushFormFields()
    },
    // 设置采购订单下推
    setupPurchaseOrderPush() {
      this.pushForm.ruleId = this.calculateRule?.[0]?.value || undefined
      this.pushForm.targetBillTypeId = undefined
      // 计算目标单据类型
      this.$nextTick(() => {
        this.calculateTargetBillType()
      })
    },
    // 清空下推表单字段
    clearPushFormFields() {
      this.pushForm.ruleId = undefined
      this.pushForm.targetBillTypeId = undefined
    },
    // 计算目标单据类型
    calculateTargetBillType() {
      const { ruleId } = this.pushForm
      const billType = this.info?.RequestType || ''
      const arr = this.pushBillTypeAllOptions.filter(item => item.target === ruleId) || []
      const newArr = arr.filter(item => item.billType === billType) || []
      this.pushForm.targetBillTypeId = newArr?.[0]?.value || undefined
    },
    // 下推提交
    handlePushSubmit() {
      const { number } = this.pushForm
      if (!number) return
      this.$refs.pushForm.validate(valid => {
        if (valid) {
          this.executePushDown()
        }
      })
    },
    // 执行下推操作
    // prettier-ignore
    executePushDown() {
      pushPurchaseApplyV2(this.pushForm).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          this.handlePushSuccess(data)
        } else if (code === 400) {
          this.handlePushError(msg)
        } else {
          this.$message.error(msg)
        }
      }).catch(error => {
        console.error('下推操作失败:', error)
        this.$message.error('下推操作失败，请重试')
      })
    },
    // 处理下推成功
    handlePushSuccess(data) {
      if (this.pushForm.target === 'PUR_PurchaseOrder') {
        this.pushOpen = false
        this.showOrderCreate = true
        const { responseStatus } = data
        const fid = responseStatus?.successEntitys?.[0]?.id
        if (!fid) {
          this.$message.error('参数错误，请重试！')
          return
        }
        this.$nextTick(() => {
          this.$refs.purchaseOrderCreate.init(undefined, 'order', { fid, type: 'push' })
        })
      } else {
        this.$message.success('下推成功')
        this.pushOpen = false
        this.handleClose(true)
      }
    },
    // 处理下推错误
    handlePushError(msg) {
      this.$alert(msg || '操作失败，请重试或使用金蝶进行操作！', '提示', {
        type: 'info',
        confirmButtonText: '确定',
        callback: () => {
          this.pushOpen = false
        }
      })
    },
    // 新增采购订单回调
    orderCreateCallBack() {
      this.showOrderCreate = false
    }
  }
}
</script>
<style scoped lang="scss">
@import '@/assets/styles/custom-new.scss';
::v-deep {
  .el-descriptions__header {
    margin-bottom: 0;
  }
  .el-table.custom-table .el-table__body-wrapper .el-table__row:hover td.el-table__cell {
    background-color: #eaf1fe !important;
  }
}
.customTitle {
  font-size: 16px;
  font-weight: normal;
  line-height: 60px;
  color: $font;
}
.kindeeButton {
  display: flex;
  align-items: center;
}
.custom-push-target {
  .el-col.el-col-12 {
    .el-radio {
      height: 30px;
      display: flex;
      align-items: center;
    }
  }
}
</style>
