<template>
  <div class="newBox bgcf9 vh-85">
    <!-- 搜索 -->
    <div class="custom-search flex" style="padding-top: 18px" :style="{ justifyContent: showSearch ? 'space-between' : 'flex-end' }">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" @submit.native.prevent>
        <el-form-item label="单据编号" prop="billNo">
          <el-input v-model="queryParams.billNo" placeholder="请输入单据编号" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="物料编码" prop="materialNumber">
          <el-input v-model="queryParams.materialNumber" placeholder="请输入物料编码" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="创建人" prop="creator">
          <el-input v-model="queryParams.creator" placeholder="请输入创建人" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="单据状态" prop="documentStatus">
          <el-select v-model="queryParams.documentStatus" placeholder="请选择单据状态" clearable>
            <el-option v-for="item in DocumentStatusOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="申请组织" prop="appOrg">
          <el-select v-model="queryParams.appOrg" placeholder="请选择申请组织" clearable>
            <el-option v-for="item in ApplicationOrgId" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="small" @click="handleResetQuery">重置</el-button>
          <!-- 新增 -->
          <el-button type="primary" icon="el-icon-plus" size="small" @click="handleCreate">新增采购申请单</el-button>
        </el-form-item>
      </el-form>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns" isSetitem @updateColumns="updateColumns"></right-toolbar>
    </div>
    <!-- 列表 -->
    <div style="padding: 15px 20px">
      <el-table v-loading="loading" ref="allTable" border :data="list" row-key="id" style="width: 100%" class="custom-table" :span-method="objectSpanMethod">
        <el-table-column align="center" type="index" label="序号" v-if="columns[0].visible"></el-table-column>
        <!-- 单据类型 -->
        <el-table-column align="center" prop="BillType" label="单据类型" show-overflow-tooltip v-if="columns[1].visible"></el-table-column>
        <!-- 单据编号 -->
        <el-table-column align="center" prop="BillNo" label="单据编号" show-overflow-tooltip v-if="columns[2].visible">
          <template slot-scope="{ row }">
            <span class="table-link" @click="handleDetail(row)">{{ row.BillNo }}</span>
          </template>
        </el-table-column>
        <!-- 数据状态 -->
        <el-table-column align="center" prop="DocumentStatus" label="数据状态" show-overflow-tooltip v-if="columns[3].visible">
          <template slot-scope="{ row }">{{ getDocumentStatusLabel(row.DocumentStatus) }}</template>
        </el-table-column>
        <!-- 关闭状态 -->
        <el-table-column align="center" prop="CloseStatus" label="关闭状态" show-overflow-tooltip v-if="columns[4].visible">
          <template slot-scope="{ row }">{{ row.CloseStatus == 'A' ? '未关闭' : '已关闭' }}</template>
        </el-table-column>
        <!-- 创建人 -->
        <el-table-column align="center" prop="Creator" label="创建人" show-overflow-tooltip v-if="columns[5].visible"></el-table-column>
        <!-- 申请组织 -->
        <el-table-column align="center" prop="ApplicationOrg" label="申请组织" show-overflow-tooltip v-if="columns[6].visible"></el-table-column>
        <!-- 申请日期 -->
        <el-table-column align="center" prop="ApplicationDate" label="申请日期" show-overflow-tooltip v-if="columns[7].visible">
          <template slot-scope="{ row }">{{ parseTime(row.ApplicationDate, '{y}-{m}-{d}') }}</template>
        </el-table-column>
        <!-- 物料名称 -->
        <el-table-column align="center" prop="MaterialName" label="物料名称" show-overflow-tooltip v-if="columns[8].visible">
          <template slot-scope="{ row }">
            <span class="table-link" @click="handleMaterialNumber(row.MaterialNumber)">{{ row.MaterialName }}</span>
          </template>
        </el-table-column>
        <!-- 规格型号 -->
        <el-table-column align="center" prop="MaterialModel" label="规格型号" show-overflow-tooltip v-if="columns[9].visible"></el-table-column>
        <!-- 申请数量 -->
        <el-table-column align="center" prop="FReqQty" label="申请数量" show-overflow-tooltip v-if="columns[10].visible"></el-table-column>
        <!-- 订单销售员 -->
        <el-table-column align="center" prop="DDXSY" label="订单销售员" show-overflow-tooltip v-if="columns[11].visible">
          <template slot-scope="{ row }">{{ row.DDXSY == 0 ? '' : row.DDXSY }}</template>
        </el-table-column>
        <!-- 订单数量 -->
        <el-table-column align="center" prop="OrderQty" label="订单数量" show-overflow-tooltip v-if="columns[12].visible">
          <template slot-scope="{ row }">{{ row.OrderQty == 0 ? '' : row.OrderQty }}</template>
        </el-table-column>
        <!-- 操作 -->
        <el-table-column align="center" label="操作">
          <template slot-scope="{ row }">
            <el-button type="text" size="small" :disabled="row.DocumentStatus != 'A' && row.DocumentStatus != 'D'" icon="el-icon-edit" @click="handleUpdate(row)">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="custom-pagination">
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.limit" @pagination="getList" :local="userId + '.inventoryPageSize'" />
      </div>
    </div>
    <!-- 详情 -->
    <inventory-detail ref="inventoryDetail" @callBack="handleCallBack" @update="handleCallUpdate" v-if="showDetail" />
    <!-- 物料详情 -->
    <material-detail ref="materialDetail" v-if="showMaterialDetail" />
    <!-- 新增采购申请 -->
    <inventory-create ref="inventoryCreate" @callBack="handleCallBack" v-if="showInventoryCreate" />
  </div>
</template>
<script>
import { getPurchaseApplyList } from '@/api/kingdee/purchase'
import InventoryDetail from './detail'
import MaterialDetail from '@/views/kingdee/material/detail'
import InventoryCreate from './create'
import { kingdee } from '@/minix'

export default {
  name: 'Kinventory',
  mixins: [kingdee],
  components: { InventoryDetail, MaterialDetail, InventoryCreate },
  data() {
    return {
      // 搜索
      queryParams: {
        pageNum: 1, // 当前页码
        startRow: 0, // 开始行索引
        limit: 10, // 最大行数
        appOrg: undefined, // 申请组织
        billNo: undefined, // 单据编号
        materialNumber: undefined, // 物料编码
        documentStatus: undefined, // 单据状态
        creator: undefined // 创建人
      },
      // 列表
      list: [],
      total: 0,
      loading: false,
      showDetail: false, // 详情
      showMaterialDetail: false, // 物料详情
      // 列表显隐
      showSearch: true,
      // 列信息
      columns: [
        { key: 0, label: `序号`, visible: true },
        { key: 1, label: `单据类型`, visible: true },
        { key: 2, label: `单据编号`, visible: true },
        { key: 3, label: `数据状态`, visible: true },
        { key: 4, label: `关闭状态`, visible: true },
        { key: 5, label: `创建人`, visible: true },
        { key: 6, label: `申请组织`, visible: true },
        { key: 7, label: `申请日期`, visible: true },
        { key: 8, label: `物料名称`, visible: true },
        { key: 9, label: `规格型号`, visible: true },
        { key: 10, label: `申请数量`, visible: true },
        { key: 11, label: `订单销售员`, visible: true },
        { key: 12, label: `订单数量`, visible: true }
      ],
      showInventoryCreate: false // 新增采购申请
    }
  },
  computed: {
    // 获取当前用户ID
    userId() {
      return this.$store.getters.info.userId
    }
  },
  created() {
    const columns = localStorage.getItem(this.userId + '.inventoryColumns')
    if (columns) this.columns = JSON.parse(columns)
    // 读取缓存的分页大小
    const cachedPageSize = localStorage.getItem(this.userId + '.inventoryPageSize')
    if (cachedPageSize) {
      this.queryParams.limit = parseInt(cachedPageSize)
    }
    // 获取列表
    this.getList()
  },
  methods: {
    // 改变显隐列缓存
    updateColumns(data = []) {
      localStorage.setItem(this.userId + '.inventoryColumns', JSON.stringify(data))
    },
    // 列表
    getList() {
      this.loading = true
      this.queryParams.startRow = Math.max(0, (this.queryParams.pageNum - 1) * this.queryParams.limit)
      const query = { ...this.queryParams }
      delete query.pageNum
      getPurchaseApplyList(query).then(res => {
        const { code, data, msg } = res
        if (code === 200) {
          const { data: list, total } = data
          this.list = list
          this.total = total
        } else this.$message.error(msg)
        this.loading = false
      })
    },
    // 相同字段合并
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {      
      const visibleColumns = this.columns.filter(col => col.visible)
      const currentColumnKey = visibleColumns[columnIndex]?.key
      if (currentColumnKey >= 1 && currentColumnKey <= 6) {
        const _row = this.calculateSpan(this.list, 'BillNo')[rowIndex]
        const _col = _row > 0 ? 1 : 0
        return {
          rowspan: _row,
          colspan: _col
        }
      }
    },
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 重置搜索
    handleResetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 查看物料
    handleMaterialNumber(number) {
      this.showMaterialDetail = true
      this.$nextTick(() => {
        this.$refs.materialDetail.getInfo(number)
      })
    },
    // 查看详情
    handleDetail(row) {
      this.showDetail = true
      this.$nextTick(() => {
        this.$refs.inventoryDetail.getInfo(row)
      })
    },
    // 回调
    handleCallBack(flag = false) {
      this.showDetail = false
      this.showInventoryCreate = false
      if (flag) this.getList()
    },
    // 更新
    handleCallUpdate(data) {
      const { billNo, newStatus } = data
      const index = this.list.findIndex(item => item.BillNo === billNo)
      if (index !== -1) this.list[index].DocumentStatus = newStatus
    },
    // 新增采购申请
    handleCreate() {
      this.showInventoryCreate = true
      this.$nextTick(() => {
        this.$refs.inventoryCreate.handleCreate()
      })
    },
    // 编辑
    handleUpdate(row) {
      this.showInventoryCreate = true
      this.$nextTick(() => {
        this.$refs.inventoryCreate.handleUpdate(row)
      })
    }
  }
}
</script>
<style scoped lang="scss">
@import '@/assets/styles/custom-new.scss';
</style>
