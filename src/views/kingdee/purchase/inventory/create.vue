<template>
  <div>
    <el-dialog v-dialogDragBox :title="title" :visible.sync="open" width="90%" class="custom-dialog" :before-close="beforeClose">
      <div style="padding: 0 20px">
        <div class="kindeeButton" v-if="isDisabled" :key="kingdeeStatus">
          <el-button type="primary" size="medium" v-if="kingdeeStatus == 'create' || kingdeeStatus == 'reAudit'" @click="handleKingdeeDo('submit')">提交</el-button>
          <el-button type="primary" size="medium" disabled v-else>提交</el-button>
          <el-button type="warning" size="medium" v-if="kingdeeStatus == 'auditing' || kingdeeStatus == 'pushed'" @click="handleKingdeeDo('audit')">审核</el-button>
          <el-button type="warning" size="medium" disabled v-else>审核</el-button>
          <el-button type="info" size="medium" v-if="kingdeeStatus == 'auditing' || kingdeeStatus == 'pushed'" @click="handleKingdeeDo('revoke')">撤销</el-button>
          <el-button type="info" size="medium" disabled v-else>撤销</el-button>
          <el-button type="danger" size="medium" v-if="kingdeeStatus == 'create' || kingdeeStatus == 'reAudit' || kingdeeStatus == 'pushed'" @click="handleKingdeeDo('delete')">删除</el-button>
          <el-button type="danger" size="medium" disabled v-else>删除</el-button>
          <el-button type="warning" size="medium" v-if="kingdeeStatus == 'audited' || kingdeeStatus == 'pushed'" @click="handleKingdeeDo('unAudit')">反审</el-button>
          <el-button type="warning" size="medium" disabled v-else>反审</el-button>
          <el-button type="success" size="medium" v-if="kingdeeStatus == 'audited' || kingdeeStatus == 'pushed'" @click="handleKingdeeDo('push')">下推</el-button>
          <el-button type="success" size="medium" disabled v-else>下推</el-button>
          <div class="same" :class="{ danger: !isSame }">
            <i :class="isSame ? 'el-icon-success' : 'el-icon-warning'" style="font-size: 20px"></i>
            <span>{{ isSame ? '数据一致，可继续操作。' : '请注意，数据有异常，请检查。' }}</span>
          </div>
        </div>
        <el-form class="custom-form" :model="form" :rules="rules" ref="form" label-width="6.5em" :disabled="isDisabled">
          <el-row :gutter="10">
            <el-col :span="50">
              <el-form-item label="申请组织" prop="fapplicationOrgId">
                <el-select v-model="form.fapplicationOrgId" filterable placeholder="请选择申请组织" style="width: 100%" @change="form.fapplicationDeptId = undefined">
                  <el-option v-for="(item, index) in ApplicationOrgId" :key="index" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="50">
              <el-form-item label="申请部门" prop="fapplicationDeptId">
                <el-select v-model="form.fapplicationDeptId" filterable :placeholder="form.fapplicationOrgId ? '请选择申请部门' : '请选择申请组织'" style="width: 100%" :disabled="!form.fapplicationOrgId" @change="form.fapplicantId = undefined">
                  <el-option v-for="(item, index) in ApplicationDeptId.filter(ite => ite.FUseOrg == form.fapplicationOrgId)" :key="index" :label="item.FName" :value="item.FNumber"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="50">
              <el-form-item label="单据类型" prop="fbillTypeID">
                <el-select v-model="form.fbillTypeID" filterable placeholder="请选择单据类型" style="width: 100%">
                  <el-option v-for="(item, index) in BillTypeID" :key="index" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="50">
              <el-form-item label="币别" prop="fcurrencyId">
                <el-select v-model="form.fcurrencyId" placeholder="请选择币别" style="width: 100%">
                  <el-option v-for="(item, index) in CurrencyId" :key="index" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="50">
              <el-form-item label="申请人" prop="fapplicantId">
                <el-select v-model="form.fapplicantId" filterable remote placeholder="请输入申请人关键词进行搜索" :disabled="!(form.fapplicationOrgId && form.fapplicationDeptId) || ApplicantList.length == 0" style="width: 100%">
                  <el-option v-for="item in ApplicantList" :key="item.FStaffNumber" :label="item.FName" :value="item.FStaffNumber"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="备注" prop="fnote">
                <el-input v-model="form.fnote" placeholder="请输入备注" style="width: 100%" />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <div style="display: flex; justify-content: flex-end; margin-bottom: 10px">
                <only-column isSetitem @updateColumns="updateKingdeeColumns" :columns="kingdeeColumns"></only-column>
              </div>
              <el-table ref="entitieTable" :data="form.entities" stripe class="custom-table" :class="{ 'custom-table-cell0': !isDisabled }" show-summary :summary-method="customSummary" :key="key" :row-class-name="tableRowClassName">
                <el-table-column align="center" type="index" label="序号" v-if="kingdeeColumns[0].visible"></el-table-column>
                <el-table-column align="center" show-overflow-tooltip prop="fmaterialId" label="物料编码" min-width="120" v-if="kingdeeColumns[1].visible">
                  <template slot-scope="scope">
                    <span class="table-link" @click="handleViewMaterial(scope.row)" v-if="isDisabled || isPush">{{ scope.row.fmaterialId }}</span>
                    <el-tooltip effect="dark" :content="scope.row.fmaterialId" :disabled="!scope.row.fmaterialId" v-else>
                      <el-form-item label-width="0" :prop="`entities.${scope.$index}.fmaterialId`" :rules="rules.fmaterialId">
                        <material-search-select ref="materialSearchSelect" :keyword.sync="scope.row.fmaterialId" :useOrg="form.fapplicationOrgId" size="small" :showLabel="false" @callBack="handleMaterialSearchSelect($event, scope.row)" :options="[{ Number: scope.row.fmaterialId, Name: scope.row.fmaterialDesc, Specification: scope.row.specs }]" />
                      </el-form-item>
                    </el-tooltip>
                  </template>
                </el-table-column>
                <el-table-column align="center" show-overflow-tooltip prop="fmaterialDesc" label="物料名称" min-width="120" v-if="kingdeeColumns[2].visible">
                  <template slot-scope="scope">
                    <span v-if="isDisabled || isPush">{{ scope.row.fmaterialDesc }}</span>
                    <span class="table-link" @click="handleViewProduct(scope.row)" v-else>{{ scope.row.fmaterialDesc }}</span>
                  </template>
                </el-table-column>
                <el-table-column align="center" show-overflow-tooltip prop="specs" label="规格" min-width="120" v-if="kingdeeColumns[3].visible">
                  <template slot-scope="scope">
                    <span v-if="isDisabled || isPush">{{ scope.row.specs }}</span>
                    <el-tooltip effect="dark" :content="`原始规格:${scope.row.specs}`" :disabled="!scope.row.Specification" placement="top" v-else>
                      <span :style="{ color: scope.row.Specification ? 'red' : '' }">{{ scope.row.Specification || scope.row.specs }}</span>
                    </el-tooltip>
                  </template>
                </el-table-column>
                <el-table-column align="center" prop="unit" label="申请单位" width="95" class-name="el-table-tip" v-if="kingdeeColumns[4].visible">
                  <template slot-scope="scope">
                    <span v-if="isDisabled">{{ unitFormat(scope.row.funitId) }}</span>
                    <el-form-item :class="{ isChange: scope.row.isChange }" label-width="0" :prop="`entities.${scope.$index}.funitId`" :rules="scope.$index === 0 || scope.row.fmaterialId ? rules.funitId : []" v-else>
                      <el-select size="small" v-model="scope.row.funitId" filterable default-first-option placeholder="请选择">
                        <el-option v-for="(item, index) in UnitList" :key="index" :label="item.FName" :value="item.FNumber"></el-option>
                      </el-select>
                    </el-form-item>
                    <div class="table-cell-tip" v-if="scope.row.isChange">
                      <i class="el-icon-warning"></i>
                      <span>请注意，单位已变更。请检查申请数量、单价等是否需要修改！</span>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column align="center" prop="freqQty" label="申请数量" min-width="120" class-name="el-table-tip" v-if="kingdeeColumns[5].visible">
                  <template slot-scope="scope">
                    <span v-if="isDisabled">{{ scope.row.freqQty && `${scope.row.freqQty}${unitFormat(scope.row.funitId)}` }}</span>
                    <el-form-item :class="{ isChange: scope.row.isChange }" label-width="0" :prop="`entities.${scope.$index}.freqQty`" :rules="scope.$index === 0 || scope.row.fmaterialId ? rules.freqQty : []" v-else>
                      <el-input v-model="scope.row.freqQty" size="small" placeholder="申请数量" @change="handleQtyChange(scope.row)">
                        <span slot="suffix" class="inline-flex">{{ unitFormat(scope.row.funitId) }}</span>
                      </el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column align="center" show-overflow-tooltip prop="fevaluatePrice" label="单价" min-width="125" class-name="el-table-tip" v-if="kingdeeColumns[6].visible">
                  <template slot-scope="scope">
                    <span v-if="isDisabled">{{ scope.row.fevaluatePrice && `￥${scope.row.fevaluatePrice}` }}</span>
                    <el-form-item :class="{ isChange: scope.row.isChange }" label-width="0" :prop="`entities.${scope.$index}.fevaluatePrice`" :rules="scope.$index === 0 || scope.row.fmaterialId ? rules.fevaluatePrice : []" v-else>
                      <el-input v-model="scope.row.fevaluatePrice" size="small" placeholder="单价" @change="handlefevaluatePriceChange(scope.row)">
                        <span slot="prefix" class="inline-flex">￥</span>
                      </el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column align="center" show-overflow-tooltip prop="ftaxprice" label="含税单价" min-width="125" v-if="kingdeeColumns[7].visible">
                  <template slot-scope="scope">
                    <span v-if="isDisabled">{{ scope.row.ftaxprice && `￥${scope.row.ftaxprice}` }}</span>
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.ftaxprice`" :rules="scope.$index === 0 || scope.row.fmaterialId ? rules.ftaxprice : []" v-else>
                      <el-input v-model="scope.row.ftaxprice" size="small" placeholder="含税单价" @change="handleftaxpriceChange(scope.row)">
                        <span slot="prefix" class="inline-flex">￥</span>
                      </el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column align="center" prop="ftaxrate" label="税率%" min-width="120" v-if="kingdeeColumns[8].visible">
                  <template slot-scope="scope">
                    <span v-if="isDisabled">{{ scope.row.ftaxrate && `${scope.row.ftaxrate}%` }}</span>
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.ftaxrate`" :rules="scope.$index === 0 || scope.row.fmaterialId ? rules.ftaxrate : []" v-else>
                      <el-input v-model="scope.row.ftaxrate" size="small" placeholder="税率" @change="handleftaxrateChange(scope.row)">
                        <span slot="suffix" class="inline-flex">%</span>
                      </el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column align="center" prop="otherAmount" label="其他费用" min-width="120" v-if="kingdeeColumns[9].visible">
                  <template slot-scope="scope">
                    <span v-if="isDisabled">{{ scope.row.otherAmount && `￥${scope.row.otherAmount}` }}</span>
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.otherAmount`" :rules="scope.$index === 0 || scope.row.fmaterialId ? rules.otherAmount : []" v-else>
                      <el-input v-model="scope.row.otherAmount" size="small" placeholder="其他费用" @change="handleotherAmountChange(scope.row, scope.$index)">
                        <span slot="prefix" class="inline-flex">￥</span>
                      </el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column align="center" prop="amount" label="金额" min-width="120" v-if="kingdeeColumns[10].visible">
                  <template slot-scope="scope">
                    <span v-if="isDisabled">{{ scope.row.famount || returnAmount(scope.row) }}</span>
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.famount`" :rules="scope.$index === 0 || scope.row.fmaterialId ? rules.famount : []" v-else>
                      <el-input v-model="scope.row.famount" size="small" placeholder="金额" @change="handleAmountChange(scope.row)">
                        <span slot="prefix" class="inline-flex">￥</span>
                      </el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column align="center" show-overflow-tooltip prop="taxAmount" label="含税金额" min-width="120" v-if="kingdeeColumns[11].visible">
                  <template slot-scope="scope">
                    <span v-if="isDisabled">{{ scope.row.freqamount || returnTaxAmount(scope.row) }}</span>
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.freqamount`" :rules="scope.$index === 0 || scope.row.fmaterialId ? rules.freqamount : []" v-else>
                      <el-input v-model="scope.row.freqamount" size="small" placeholder="含税金额" @change="handleTaxAmountChange(scope.row)">
                        <span slot="prefix" class="inline-flex">￥</span>
                      </el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column align="center" show-overflow-tooltip prop="fsuggestSupplierId" label="建议供应商" min-width="135" v-if="kingdeeColumns[12].visible">
                  <template slot-scope="scope">
                    <span v-if="isDisabled">{{ scope.row.supplierName || supplierFormat(scope.row.fsuggestSupplierId) }}</span>
                    <el-tooltip effect="dark" :content="scope.row.supplierName" :disabled="!scope.row.supplierName" v-else>
                      <el-form-item label-width="0" :prop="`entities.${scope.$index}.fsuggestSupplierId`" :rules="scope.$index === 0 || scope.row.fmaterialId ? rules.fsuggestSupplierId : []">
                        <supplier-search-select :keyword.sync="scope.row.supplierName" :useOrg="form.fapplicationOrgId" size="small" :showLabel="false" :isBack="true" @callBack="handleSupplierSearchSelect($event, scope.row)" />
                      </el-form-item>
                    </el-tooltip>
                  </template>
                </el-table-column>
                <!-- 到货日期 -->
                <el-table-column align="center" prop="farrivalDate" label="到货日期" width="135" v-if="kingdeeColumns[13].visible">
                  <template slot-scope="scope">
                    <span v-if="isDisabled">{{ parseTime(scope.row.farrivalDate) }}</span>
                    <el-tooltip effect="dark" :content="scope.row.farrivalDate" :disabled="!scope.row.farrivalDate" v-else>
                      <el-form-item label-width="0" :prop="`entities.${scope.$index}.farrivalDate`" :rules="rules.farrivalDate" class="custom-date-picker">
                        <el-date-picker v-model="scope.row.farrivalDate" type="datetime" placeholder="选择日期" size="small" value-format="yyyy-MM-dd HH:mm:ss" style="width: 100%" :clearable="false" @change="handleArrivalDateChange"></el-date-picker>
                      </el-form-item>
                    </el-tooltip>
                  </template>
                </el-table-column>
                <el-table-column align="center" show-overflow-tooltip prop="fentryNote" label="备注" min-width="120" v-if="kingdeeColumns[14].visible">
                  <template slot-scope="scope">
                    <span v-if="isDisabled">{{ scope.row.fentryNote }}</span>
                    <el-input v-model="scope.row.fentryNote" size="small" placeholder="备注" v-else></el-input>
                  </template>
                </el-table-column>
                <el-table-column align="center" show-overflow-tooltip prop="fPurchaserId" label="采购员" min-width="80" v-if="kingdeeColumns[15].visible">
                  <template slot-scope="scope">{{ formatFapplicantId(form.fapplicantId) }}</template>
                </el-table-column>
                <el-table-column align="center" show-overflow-tooltip prop="fapproveQty" label="批准数量" v-if="kingdeeColumns[16].visible">
                  <template slot-scope="scope">
                    {{ scope.row.freqQty && `${scope.row.freqQty}${unitFormat(scope.row.funitId)}` }}
                  </template>
                </el-table-column>
                <el-table-column align="center" show-overflow-tooltip label="采购组织" width="80" v-if="kingdeeColumns[17].visible">
                  <template slot-scope="{ row }">{{ formatOrg() }}</template>
                </el-table-column>
                <el-table-column align="center" show-overflow-tooltip label="需求组织" width="80" v-if="kingdeeColumns[18].visible">
                  <template slot-scope="{ row }">{{ formatOrg() }}</template>
                </el-table-column>
                <el-table-column align="center" prop="fpriceUnitId" label="计价单位" width="80" v-if="kingdeeColumns[19].visible">
                  <template slot-scope="scope">{{ unitFormat(scope.row.funitId) }}</template>
                </el-table-column>
                <el-table-column align="center" show-overflow-tooltip prop="fpriceUnitQty" label="计价数量" v-if="kingdeeColumns[20].visible">
                  <template slot-scope="scope">{{ scope.row.freqQty && `${scope.row.freqQty}${unitFormat(scope.row.funitId)}` }}</template>
                </el-table-column>
                <el-table-column fixed="right" align="center" label="操作" :width="isCreate ? 220 : 120" v-if="(form.entities.length > 1 && !isDisabled) || isCreate">
                  <template slot-scope="{ row }">
                    <el-button class="table-btn primary" size="small" @click="handleAdd()" v-if="isCreate">添加</el-button>
                    <el-button class="table-btn danger" :class="{ disabled: form.entities.length <= 1 }" size="small" @click="handleDelete(row)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn primary" @click="handleClose()" v-if="isDisabled">关 闭</el-button>
        <template v-else>
          <el-button class="custom-dialog-btn" @click="handleClose()">取 消</el-button>
          <el-button class="custom-dialog-btn primary" @click="handleSubmit">保 存</el-button>
        </template>
      </div>
    </el-dialog>
    <!-- 产品详情 -->
    <product-dialog ref="productInfo"></product-dialog>
    <!-- 物料详情 -->
    <material-detail ref="materialDetail" v-if="showMaterialDetail"></material-detail>
    <!-- 新增采购订单 -->
    <purchase-order-create ref="purchaseOrderCreate" @callBack="orderCreateCallBack" v-if="showOrderCreate" />
    <!-- 下推弹出框 -->
    <el-dialog v-dialogDragBox title="选择单据" :visible.sync="pushOpen" width="580px" class="custom-dialog">
      <div style="padding: 0 20px">
        <el-form ref="pushForm" :model="pushForm" :rules="pushFormRules" label-width="80px">
          <el-form-item label="" label-width="0" prop="target">
            <el-radio-group v-model="pushForm.target" v-removeAriaHidden @change="handleTargetChange">
              <el-row :gutter="10" class="custom-push-target">
                <el-col :span="12" v-for="item in pushTarget" :key="item.value">
                  <el-radio :label="item.value">{{ item.label }}</el-radio>
                </el-col>
              </el-row>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="转换规则" prop="ruleId">
            <el-select v-model="pushForm.ruleId" placeholder="请选择转换规则" style="width: 100%">
              <el-option v-for="item in calculateRule" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="单据类型" prop="targetBillTypeId">
            <el-select v-model="pushForm.targetBillTypeId" placeholder="请选择单据类型" style="width: 100%">
              <el-option v-for="item in calculateBillType" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn small" @click="pushOpen = false">取消</el-button>
        <el-button class="custom-dialog-btn small primary" @click="handlePushSubmit">确定</el-button>
      </div>
    </el-dialog>
    <!-- 采购申请单详情 -->
    <purchase-apply-detail ref="purchaseApplyDetail" />
  </div>
</template>
<script>
import { isNumber, isNumberLength } from '@/utils/validate'
import { getProductKingdeeCode, getStaffPostList, getSupplierList, saveProductKingdeeCode, getMaterialDetail } from '@/api/kingdee'
import { addPurchaseApply, auditPurchaseApply, cancelPurchaseApply, deletePurchaseApply, getPurchaseApplyDetail, getPurchaseApplyDetailV2, pushPurchaseApply, submitPurchaseApply, unAuditPurchaseApply, getPurchaseApplyDetailV3, addPurchaseApplyV2, deletePurchaseApplyV3 } from '@/api/kingdee/purchase'
import { contractPriceChange } from '@/api/purchase'
import { getProduct } from '@/api/system/product'
import { getPrivateduct } from '@/api/system/privateduct'
import ProductDialog from '@/views/public/product/dialog'
import MaterialDetail from '@/views/kingdee/material/detail'
import { kingdee } from '@/minix'
import OnlyColumn from '@/components/RightToolbar/onlyColumn'
import PurchaseOrderCreate from '@/views/kingdee/purchase/order/create'
import materialSearchSelect from '@/components/SearchSelect/material'
import supplierSearchSelect from '@/components/SearchSelect/supplier'
import PurchaseApplyDetail from '@/views/kingdee/purchase/inventory/detail'

export default {
  mixins: [kingdee],
  components: { ProductDialog, MaterialDetail, OnlyColumn, PurchaseOrderCreate, materialSearchSelect, supplierSearchSelect, PurchaseApplyDetail },
  data() {
    return {
      open: false,
      title: '采购合同推送至金蝶',
      form: { entities: [] },
      rules: {
        fapplicationOrgId: [{ required: true, message: '请选择申请组织', trigger: ['change', 'blur'] }],
        fbillTypeID: [{ required: true, message: '请选择单据类型', trigger: ['change', 'blur'] }],
        fapplicationDeptId: [{ required: true, message: '请选择申请部门', trigger: ['change', 'blur'] }],
        fcurrencyId: [{ required: true, message: '请选择币别', trigger: ['change', 'blur'] }],
        fapplicantId: [{ required: true, message: '请选择申请人', trigger: ['change', 'blur'] }],
        fmaterialId: [{ required: true, message: '请输入物料编码', trigger: ['change', 'blur'] }],
        funitId: [{ required: true, message: '请选择单位', trigger: ['change', 'blur'] }],
        freqQty: [
          { required: true, message: '请输入申请数量', trigger: ['change', 'blur'] },
          { validator: isNumber, message: '请输入正确的数量', trigger: ['change', 'blur'] },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: ['change', 'blur'] }
        ],
        // fsuggestSupplierId: [{ required: true, message: '请选择建议供应商', trigger: ['change', 'blur'] }],
        fevaluatePrice: [
          { required: true, message: '请输入单价', trigger: ['blur', 'change'] },
          { validator: isNumber, message: '请输入正确的单价', trigger: ['blur', 'change'] },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: ['blur', 'change'] }
        ],
        ftaxprice: [
          { required: true, message: '请输入含税单价', trigger: ['blur', 'change'] },
          { validator: isNumber, message: '请输入正确的含税单价', trigger: ['blur', 'change'] },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: ['blur', 'change'] }
        ],
        ftaxrate: [
          { required: true, message: '请输入税率', trigger: 'blur' },
          { validator: isNumber, message: '请输入正确的税率', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: 'blur' }
        ],
        otherAmount: [
          { validator: isNumber, message: '请输入正确的费用', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: 'blur' }
        ],
        famount: [
          { validator: isNumber, message: '请输入正确的金额', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: 'blur' }
        ],
        freqamount: [
          { validator: isNumber, message: '请输入正确的含税金额', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: 'blur' }
        ]
      },
      ApplicantList: [], // 申请人
      ApplicantLoading: false,
      // 是否只读
      isDisabled: false,
      // 金蝶采购申请单编码
      kingdeeNumber: undefined,
      kingdeeStatus: undefined,
      showMaterialDetail: false,
      key: 1,
      // 是否一致
      isSame: true,
      // 下推
      showOrderCreate: false,
      kingdeeColumns: [
        { key: 0, label: `序号`, visible: true },
        { key: 1, label: `物料编码`, visible: true },
        { key: 2, label: `物料名称`, visible: true },
        { key: 3, label: `规格`, visible: true },
        { key: 4, label: `申请单位`, visible: true },
        { key: 5, label: `申请数量`, visible: true },
        { key: 6, label: `单价`, visible: true },
        { key: 7, label: `含税单价`, visible: true },
        { key: 8, label: `税率`, visible: true },
        { key: 9, label: `其他费用`, visible: true },
        { key: 10, label: `金额`, visible: true },
        { key: 11, label: `含税金额`, visible: true },
        { key: 12, label: `建议供应商`, visible: true },
        { key: 13, label: `到货日期`, visible: true },
        { key: 14, label: `备注`, visible: true },
        { key: 15, label: `采购员`, visible: true },
        { key: 16, label: `批准数量`, visible: true },
        { key: 17, label: `采购组织`, visible: true },
        { key: 18, label: `需求组织`, visible: true },
        { key: 19, label: `计价单位`, visible: true },
        { key: 20, label: `计价数量`, visible: true }
      ],
      isPush: false,
      fid: undefined,
      // 下推
      kingdeeRequestType: undefined,
      pushOpen: false,
      pushForm: {},
      pushFormRules: {
        target: [{ required: true, message: '请选择下推目标', trigger: 'change' }],
        ruleId: [{ required: true, message: '请选择转换规则', trigger: 'change' }],
        targetBillTypeId: [{ required: true, message: '请选择单据类型', trigger: 'change' }]
      },
      // 下推目标
      pushTarget: [{ value: 'PUR_PurchaseOrder', label: '采购订单' }],
      // 下推转换规则
      pushRuleOptions: [{ label: '采购申请单至采购订单', value: 'PUR_Requisition-PUR_PurchaseOrder', target: 'PUR_PurchaseOrder' }],
      // 下推单据类型
      pushBillTypeAllOptions: [
        { label: '资产采购订单', value: 'b0677860cd16433895be5f520086b69f', target: 'PUR_Requisition-PUR_PurchaseOrder', billType: 'Property' },
        { label: '标准采购订单', value: '83d822ca3e374b4ab01e5dd46a0062bd', target: 'PUR_Requisition-PUR_PurchaseOrder', billType: 'Material' },
        { label: '直运采购订单', value: 'b8df755fd92b4c2baedef2439c29f793', target: 'PUR_Requisition-PUR_PurchaseOrder', billType: 'ZYSQ' },
        { label: '费用采购订单', value: 'b1985f24f35841fdb418329af6ed7bd0', target: 'PUR_Requisition-PUR_PurchaseOrder', billType: 'Expense' }
      ],
      isCreate: false,
      // 是否已成功提交
      hasSuccessfully: false
    }
  },
  watch: {
    'form.fapplicationDeptId': function (newVal) {
      this.ApplicantRemoteMethod()
    }
  },
  computed: {
    userId() {
      return this.$store.getters.info.userId
    },
    // 计算规则
    calculateRule() {
      return this.pushRuleOptions.filter(item => item.target === this.pushForm.target)
    },
    // 计算单据类型
    calculateBillType() {
      const { ruleId } = this.pushForm
      const billType = this.kingdeeRequestType || ''
      const arr = this.pushBillTypeAllOptions.filter(item => item.target === ruleId) || []
      return arr.filter(item => item.billType === billType) || []
    }
  },
  methods: {
    // 申请人远程方法
    ApplicantRemoteMethod(query) {
      if (!this.form.fapplicationOrgId || !this.form.fapplicationDeptId) {
        this.ApplicantList = []
        return
      }
      this.ApplicantLoading = true
      const params = { useOrg: this.form.fapplicationOrgId, dept: this.form.fapplicationDeptId, name: query }
      getStaffPostList(params).then(res => {
        const { code, msg, data } = res
        if (code == 200) {
          const { data: dataList } = data
          dataList.forEach(item => {
            const postArr = item.FStaffNumber.split('_')
            const postValue = postArr[1]
            // 职位
            const post = this.OrgHrPost.find(post => post.FNumber == postValue)
            item.post = post?.FName || ''
            // 部门
            const dept = this.ApplicationDeptId.find(dept => dept.FNumber == item.FDept)
            item.dept = dept?.FName || ''
            // 组织
            if (dept) {
              const org = this.ApplicationOrgId.find(org => org.value == dept.FUseOrg)
              item.org = org?.label || ''
            }
          })
          this.ApplicantList = dataList
        } else this.$message.error(msg)
        this.ApplicantLoading = false
      })
    },
    // 格式化申请人
    formatFapplicantId(fapplicantId) {
      if (!fapplicantId) return ''
      const applicant = this.ApplicantList.find(applicant => applicant.FStaffNumber == fapplicantId)
      return applicant?.FName || ''
    },
    // 格式化供应商
    supplierFormat(fsuggestSupplierId) {
      if (!fsuggestSupplierId) return ''
      // 在实际的显示中会优先使用 supplierName 字段
      return fsuggestSupplierId
    },
    // 供应商选择回调
    handleSupplierSearchSelect(supplier, row) {
      if (supplier) {
        this.$set(row, 'fsuggestSupplierId', supplier.FNumber)
        this.$set(row, 'supplierName', supplier.FName)
      } else {
        this.$set(row, 'fsuggestSupplierId', '')
        this.$set(row, 'supplierName', '')
      }
    },
    // 格式化采购组织
    formatOrg() {
      if (!this.form.fapplicationOrgId) return ''
      const org = this.ApplicationOrgId.find(org => org.value == this.form.fapplicationOrgId)
      return org?.label || ''
    },
    // 格式化计价单位
    unitFormat(unitId) {
      if (!this.UnitList.length) return ''
      const unitObj = this.UnitList.find(unit => unit.FNumber == unitId)
      return unitObj?.FName || ''
    },
    // 表单重置
    reset() {
      this.form = {
        contractId: undefined, // 合同ID
        entities: [], // 明细
        fapplicantId: undefined, // 申请人ID
        fapplicationDeptId: undefined, // 申请部门ID
        fapplicationOrgId: undefined, // 申请组织ID
        fbillTypeID: undefined, // 单据类型ID
        fcurrencyId: undefined, // 币种ID
        fnote: undefined, // 备注
        fsuggestSupplierId: undefined, // 建议供应商ID
        supplierName: undefined // 供应商名称
      }
      this.resetForm('form')
      this.isPush = false
      this.fid = undefined
      this.isCreate = false
      this.hasSuccessfully = false
    },
    // 新增采购申请单
    handleCreate() {
      this.reset()
      this.isCreate = true
      this.form.entities = [
        {
          fmaterialId: undefined,
          fmaterialDesc: undefined,
          fmaterialName: undefined,
          specs: undefined,
          funitId: undefined,
          freqQty: undefined,
          fevaluatePrice: undefined,
          ftaxprice: undefined,
          ftaxrate: 0,
          famount: undefined,
          freqamount: undefined,
          info: undefined,
          fentryNote: undefined,
          isChange: false,
          source: undefined,
          isFind: true,
          productId: undefined,
          fsuggestSupplierId: undefined,
          supplierName: undefined
        }
      ]
      this.isDisabled = false
      this.title = '新增采购申请单'
      this.key = Math.random()
      this.open = true
    },
    // 添加一行
    handleAdd() {
      this.form.entities.push({
        fmaterialId: undefined,
        fmaterialDesc: undefined,
        fmaterialName: undefined,
        specs: undefined,
        funitId: undefined,
        freqQty: undefined,
        fevaluatePrice: undefined,
        ftaxprice: undefined,
        ftaxrate: 0,
        famount: undefined,
        freqamount: undefined,
        info: undefined,
        fentryNote: undefined,
        isChange: false,
        source: undefined,
        isFind: true,
        productId: undefined,
        fsuggestSupplierId: undefined,
        supplierName: undefined
      })
    },
    // 编辑
    async handleUpdate(row) {
      const loading = this.$loading({
        lock: true,
        text: '加载中...',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      try {
        this.reset()
        this.isDisabled = false
        this.title = '编辑采购申请单'
        const detail = await getPurchaseApplyDetailV2({ billNo: row.BillNo })
        const { code, msg, data } = detail
        if (code === 200) {
          const { result } = data
          const info = result?.result || {}
          const tsApplicationOrgId = info?.ApplicationOrgId?.Number || ''
          const tsApplicationDeptId = info?.ApplicationDeptId?.Number || ''
          const tsBillTypeID = info?.BillTypeID?.Number || ''
          const tsCurrencyId = info?.CurrencyId?.Number || ''
          const tsNote = info?.Note || ''
          const fapplicationOrgId = this.ApplicationOrgId.find(item => item.label == '世盛销售部')
          this.form.fapplicationOrgId = tsApplicationOrgId || localStorage.getItem('kingdee_fapplicationOrgId') || fapplicationOrgId?.value // 申请组织
          const fapplicationDeptId = this.ApplicationDeptId.filter(ite => ite.FUseOrg == this.form.fapplicationOrgId).find(item => item.FName == '采购部')
          this.form.fapplicationDeptId = tsApplicationDeptId || localStorage.getItem('kingdee_fapplicationDeptId') || fapplicationDeptId?.FNumber // 申请部门
          const fbillTypeID = this.BillTypeID.find(item => item.label == '标准采购申请')
          this.form.fbillTypeID = tsBillTypeID || localStorage.getItem('kingdee_fbillTypeID') || fbillTypeID?.value // 单据类型
          const fcurrencyId = this.CurrencyId.find(item => item.label == '人民币')
          this.form.fcurrencyId = tsCurrencyId || localStorage.getItem('kingdee_fcurrencyId') || fcurrencyId?.value // 币种
          this.form.fapplicantId = tsApplicationDeptId == localStorage.getItem('kingdee_fapplicationDeptId') ? localStorage.getItem('kingdee_fapplicantId') : undefined // 申请人
          this.form.fnote = tsNote || ``
          this.key = Math.random()
          const list = info?.ReqEntry || []
          const entities = list.map(item => {
            const qty = item.ReqQty || 0
            const price = item.EvaluatePrice || 0
            const taxPrice = item.TAXPRICE || price
            return {
              fmaterialId: item.MaterialId?.Number || undefined, // 物料编码
              fmaterialDesc: (item.MaterialId && this.getString(item.MaterialId.Name)) || undefined, // 物料名称
              specs: (item.MaterialId && this.getString(item.MaterialId.Specification)) || undefined, // 规格型号
              funitId: item.UnitID?.Number || undefined, // 单位
              freqQty: qty, // 采购数量
              fevaluatePrice: price, // 单价
              ftaxprice: taxPrice, // 含税单价
              ftaxrate: item.TAXRATE || 0, // 税率
              otherAmount: undefined, // 其他费用
              famount: parseFloat((qty * price).toFixed(5)), // 金额
              freqamount: parseFloat((qty * taxPrice).toFixed(5)), // 含税金额
              fsuggestSupplierId: item.SuggestSupplierId?.Number || undefined, // 建议供应商
              supplierName: (item.SuggestSupplierId && this.getString(item.SuggestSupplierId.Name)) || undefined, // 供应商名称
              farrivalDate: item.ArrivalDate || undefined, // 到货日期
              fentryNote: item.EntryNote || undefined, // 备注
              isChange: false,
              isFind: true
            }
          })
          this.fid = info?.Id || undefined
          this.form.entities = entities
          this.$nextTick(() => {
            this.isPush = false
            this.open = true
          })
        } else {
          this.$message.error(msg || '获取采购申请单详情失败，请稍后重试')
        }
      } catch (error) {
        console.error('编辑采购申请单时发生错误:', error)
        this.$message.error('编辑采购申请单时发生错误，请检查网络连接后重试')
      } finally {
        // 确保无论成功还是失败都关闭loading
        loading.close()
      }
    },
    // 销售订单下推至采购申请单
    async initPush(fid, type = undefined) {
      const loading = this.$loading({
        lock: true,
        text: '加载中...',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      try {
        this.reset()
        this.isPush = type == 'push'
        this.fid = fid
        this.isDisabled = false
        this.title = '新增采购申请单'
        const detail = await getPurchaseApplyDetailV3({ fid })
        const { code, msg, data } = detail
        if (code === 200) {
          const { result } = data
          const info = result?.result || {}
          const tsApplicationOrgId = info?.ApplicationOrgId?.Number || ''
          const tsApplicationDeptId = info?.ApplicationDeptId?.Number || ''
          const tsBillTypeID = info?.BillTypeID?.Number || ''
          const tsCurrencyId = info?.CurrencyId?.Number || ''
          const tsNote = info?.Note || ''
          const fapplicationOrgId = this.ApplicationOrgId.find(item => item.label == '世盛销售部')
          this.form.fapplicationOrgId = tsApplicationOrgId || localStorage.getItem('kingdee_fapplicationOrgId') || fapplicationOrgId?.value // 申请组织
          const fapplicationDeptId = this.ApplicationDeptId.filter(ite => ite.FUseOrg == this.form.fapplicationOrgId).find(item => item.FName == '采购部')
          this.form.fapplicationDeptId = tsApplicationDeptId || localStorage.getItem('kingdee_fapplicationDeptId') || fapplicationDeptId?.FNumber // 申请部门
          const fbillTypeID = this.BillTypeID.find(item => item.label == '标准采购申请')
          this.form.fbillTypeID = tsBillTypeID || localStorage.getItem('kingdee_fbillTypeID') || fbillTypeID?.value // 单据类型
          const fcurrencyId = this.CurrencyId.find(item => item.label == '人民币')
          this.form.fcurrencyId = tsCurrencyId || localStorage.getItem('kingdee_fcurrencyId') || fcurrencyId?.value // 币种
          this.form.fapplicantId = tsApplicationDeptId == localStorage.getItem('kingdee_fapplicationDeptId') ? localStorage.getItem('kingdee_fapplicantId') : undefined // 申请人
          this.form.fnote = tsNote || ``
          this.key = Math.random()
          const list = info?.ReqEntry || []
          const entities = list.map(item => {
            const qty = item.ReqQty || 0
            const price = item.EvaluatePrice || 0
            const taxPrice = item.TAXPRICE || price
            return {
              fmaterialId: item.MaterialId?.Number || undefined, // 物料编码
              fmaterialDesc: (item.MaterialId && this.getString(item.MaterialId.Name)) || undefined, // 物料名称
              specs: (item.MaterialId && this.getString(item.MaterialId.Specification)) || undefined, // 规格型号
              funitId: item.UnitID?.Number || undefined, // 单位
              freqQty: qty, // 采购数量
              fevaluatePrice: price, // 单价
              ftaxprice: taxPrice, // 含税单价
              ftaxrate: item.TAXRATE || 0, // 税率
              otherAmount: undefined, // 其他费用
              famount: parseFloat((qty * price).toFixed(5)), // 金额
              freqamount: parseFloat((qty * taxPrice).toFixed(5)), // 含税金额
              fsuggestSupplierId: item.SuggestSupplierId?.Number || undefined, // 建议供应商
              supplierName: (item.SuggestSupplierId && this.getString(item.SuggestSupplierId.Name)) || undefined, // 供应商名称
              farrivalDate: item.ArrivalDate || undefined, // 到货日期
              fentryNote: item.EntryNote || undefined, // 备注
              isChange: false,
              isFind: true
            }
          })
          this.form.entities = entities
          this.$nextTick(() => {
            this.isPush = true
            this.open = true
          })
        } else {
          this.$message.error(msg || '获取采购申请单详情失败，请稍后重试')
        }
      } catch (error) {
        console.error('下推采购申请单时发生错误:', error)
        this.$message.error('下推采购申请单时发生错误，请稍后重试')
      } finally {
        // 确保无论成功还是失败都关闭loading
        loading.close()
      }
    },
    // 打开
    async handleOpen(row = {}, type = undefined) {
      const loading = this.$loading({
        lock: true,
        text: '加载中...',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      try {
        const sellerName = row.sellerName
        const contractId = row.id
        if (!contractId) {
          this.$message.error('参数错误')
          return
        }
        this.reset()
        this.form.contractId = contractId
        this.isDisabled = false
        this.title = '采购合同推送至金蝶'
        const fapplicationOrgId = this.ApplicationOrgId.find(item => item.label == '世盛销售部')
        this.form.fapplicationOrgId = localStorage.getItem('kingdee_fapplicationOrgId') || fapplicationOrgId?.value
        const fapplicationDeptId = this.ApplicationDeptId.filter(ite => ite.FUseOrg == this.form.fapplicationOrgId).find(item => item.FName == '采购部')
        this.form.fapplicationDeptId = localStorage.getItem('kingdee_fapplicationDeptId') || fapplicationDeptId?.FNumber
        const fbillTypeID = this.BillTypeID.find(item => item.label == '标准采购申请')
        this.form.fbillTypeID = localStorage.getItem('kingdee_fbillTypeID') || fbillTypeID?.value
        const fcurrencyId = this.CurrencyId.find(item => item.label == '人民币')
        this.form.fcurrencyId = localStorage.getItem('kingdee_fcurrencyId') || fcurrencyId?.value
        this.form.fapplicantId = localStorage.getItem('kingdee_fapplicantId') || undefined
        this.form.fnote = `来自自由客,采购合同编号为：${row.serial}`
        this.key = Math.random()
        if (!type) {
          const res = await contractPriceChange({ contractId })
          const { code, data, msg } = res
          if (code === 200) {
            const kingdeeColumns = localStorage.getItem(`kingdee_purchase_inventory_${this.userId}`)
            if (kingdeeColumns) this.kingdeeColumns = JSON.parse(kingdeeColumns)
            // 使用 Promise.all 等待所有异步操作完成
            const entities = await Promise.all(
              data.map(async item => {
                const info = item.source == 'common' ? await getProduct(item.productId) : await getPrivateduct(item.productId)
                item.info = info.data
                const funitID = this.UnitList.find(unit => unit.FName == item.unit)?.FNumber || ''
                return {
                  fmaterialId: item.info?.materialCode || '',
                  fmaterialDesc: item.productName,
                  fmaterialName: item.info?.materialCode || '',
                  specs: info?.data?.specs,
                  funitId: funitID,
                  freqQty: item.sjNum,
                  fevaluatePrice: item.amount,
                  ftaxprice: item.amount,
                  ftaxrate: 0,
                  famount: parseFloat((item.sjNum * item.amount).toFixed(5)),
                  freqamount: parseFloat((item.sjNum * item.amount).toFixed(5)),
                  info: info?.data,
                  fentryNote: '',
                  isChange: false,
                  source: item.source,
                  isFind: true,
                  productId: item.productId,
                  fsuggestSupplierId: undefined,
                  supplierName: undefined
                }
              })
            )
            this.form.entities = entities
            this.open = true
            if (this.form.entities.length) {
              await this.loadAdditionalData(this.form.entities, sellerName)
            }
          } else {
            this.$message.error(msg || '获取合同数据失败，请稍后重试')
          }
        } else {
          const kingdeeColumns = localStorage.getItem(`kingdee_purchase_inventory_${this.userId}`)
          if (kingdeeColumns) this.kingdeeColumns = JSON.parse(kingdeeColumns)
          const data = row.products ? JSON.parse(row.products) : []
          // 使用 Promise.all 等待所有异步操作完成
          const entities = await Promise.all(
            data.map(async item => {
              const info = item.source == 'common' ? await getProduct(item.productId) : await getPrivateduct(item.productId)
              item.info = info.data
              const funitID = this.UnitList.find(unit => unit.FName == item.unit)?.FNumber || ''
              return {
                fmaterialId: item.info?.materialCode || '',
                fmaterialDesc: item.info?.productName,
                fmaterialName: item.info?.materialCode || '',
                specs: info?.data?.specs,
                funitId: funitID,
                freqQty: item.quantity,
                fevaluatePrice: item.amount,
                ftaxprice: item.amount,
                ftaxrate: 0,
                famount: parseFloat((item.quantity * item.amount).toFixed(5)),
                freqamount: parseFloat((item.quantity * item.amount).toFixed(5)),
                info: info?.data,
                fentryNote: '',
                isChange: false,
                source: item.source,
                isFind: true,
                productId: item.productId,
                fsuggestSupplierId: undefined,
                supplierName: undefined
              }
            })
          )
          this.form.entities = entities
          this.open = true
          if (this.form.entities.length) {
            await this.loadAdditionalData(this.form.entities, sellerName)
          }
        }
      } catch (error) {
        console.error('打开采购申请单时发生错误:', error)
        this.$message.error('打开采购申请单时发生错误，请稍后重试')
      } finally {
        // 确保无论成功还是失败都关闭loading
        loading.close()
      }
    },
    // 加载附加数据
    async loadAdditionalData(rows, sellerName = undefined) {
      if (!sellerName) {
        this.$message.error('参数错误，请重试')
        return
      }
      const supplierData = await getSupplierList({ name: sellerName })
      const supplier = supplierData?.data?.data
      // 根据FNumber去重
      const resSupplier = supplier ? supplier.filter((item, index, arr) => arr.findIndex(s => s.FNumber === item.FNumber) === index) : []
      try {
        // 使用 for...of 循环保证顺序
        for (const item of rows) {
          try {
            // 查询产品对应金蝶产品编码
            const productKingdeeCode = await getProductKingdeeCode({ productId: item.productId, source: item.source })
            const materialNumber = productKingdeeCode?.data?.materialNumber || productKingdeeCode?.data || ''
            const materialName = productKingdeeCode?.data?.materialName || ''
            const funitID = this.UnitList.find(unit => unit.FName == productKingdeeCode?.data?.unit)?.FNumber || ''
            if (materialNumber && funitID) {
              let fmaterialName = materialName
              if (!fmaterialName) {
                // 查询物料详情
                const materialDetail = await getMaterialDetail({ number: materialNumber })
                const resData = materialDetail?.data?.result?.result || {}
                fmaterialName = resData?.Name?.[0]?.Value || ''
              }
              // 更新物料编码和单位
              const index = this.form.entities.findIndex(row => row.productId === item.productId && row.source === item.source)
              if (index > -1) {
                const entry = { ...this.form.entities[index] }
                // 如果单位发生变化,标记isChange为true
                if (funitID !== entry.funitId) {
                  entry.isChange = true
                }
                // 更新物料编码和单位
                entry.fmaterialId = materialNumber
                entry.fmaterialName = fmaterialName
                entry.funitId = funitID
                entry.fpriceUnitId = funitID
                entry.fstockUnitId = funitID
                entry.supplierName = resSupplier?.[0]?.FName || ''
                entry.fsuggestSupplierId = resSupplier?.[0]?.FNumber || ''
                // 使用 Vue.set 更新数据
                this.$set(this.form.entities, index, entry)
              }
            } else {
              const { materialCode } = item?.info || {}
              if (materialCode) {
                const materialDetail = await getMaterialDetail({ number: materialCode })
                const resData = materialDetail?.data?.result?.result || {}
                const kMaterialNumber = resData?.Number || ''
                const kMaterialName = resData?.Name?.[0]?.Value || ''
                const kFunitID = resData?.MaterialBase?.[0]?.BaseUnitId?.Number || ''
                const index = this.form.entities.findIndex(row => row.productId === item.productId && row.source === item.source)
                if (index > -1) {
                  const entry = { ...this.form.entities[index] }
                  if (kFunitID !== entry.funitId) {
                    entry.isChange = true
                  }
                  entry.fmaterialId = kMaterialNumber
                  entry.fmaterialName = kMaterialName
                  entry.funitId = kFunitID
                  entry.fpriceUnitId = kFunitID
                  entry.fstockUnitId = kFunitID
                  entry.supplierName = resSupplier?.[0]?.FName || ''
                  entry.fsuggestSupplierId = resSupplier?.[0]?.FNumber || ''
                  // 使用 Vue.set 更新数据
                  this.$set(this.form.entities, index, entry)
                  // 保存产品金蝶编码
                  if (kMaterialNumber && kFunitID) {
                    const data = {
                      materialNumber: kMaterialNumber,
                      materialName: kMaterialName,
                      productId: item.productId,
                      source: item.source,
                      unit: this.UnitList.find(unit => unit.FNumber == kFunitID)?.FName || ''
                    }
                    await saveProductKingdeeCode(data)
                  }
                }
              }
            }
          } catch (error) {
            console.error('加载数据出错:', error)
            // 继续处理下一条数据
            continue
          }
        }
      } catch (error) {
        console.error('加载附加数据失败', error)
      }
    },
    // 查看详情
    async getInfo(row = {}) {
      const loading = this.$loading({
        lock: true,
        text: '加载中...',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      try {
        const number = row?.requisitionNum
        if (!number) {
          this.$message.error('参数错误')
          return
        }
        this.reset()
        this.kingdeeNumber = number
        this.isDisabled = true
        this.title = '采购申请单详情'
        let params = { number }
        params.refresh = true
        // 获取金蝶详情
        const kDetail = await getPurchaseApplyDetailV2({ billNo: number })
        this.kingdeeRequestType = kDetail?.data?.result?.result?.RequestType || ''
        const list = kDetail?.data?.result?.result?.ReqEntry || []
        const kentityList = list.map(item => {
          return {
            ...item.MaterialId,
            freqQty: item.ReqQty
          }
        })
        // 获取本地详情
        const detail = await getPurchaseApplyDetail(params)
        const { code, msg, data } = detail
        if (code === 200) {
          const entityList = data?.entityList || []
          if (entityList.length !== kentityList.length) {
            this.isSame = false
            entityList.forEach(item => {
              item.isFind = kentityList.findIndex(kitem => kitem.Number == item.fmaterialId && kitem.freqQty == item.freqQty) !== -1
            })
          } else {
            this.isSame = true
            entityList.forEach(item => {
              item.isFind = true
            })
          }
          const entities = entityList.map(item => {
            const kitem = kentityList.find(kitem => kitem.Number == item.fmaterialId)
            return {
              ...item,
              specs: kitem?.Specification?.[0]?.Value || ''
            }
          })
          this.form = {
            contractId: data?.contractId || undefined, // 合同ID
            entities, // 明细
            fapplicantId: data?.applicationId || undefined, // 申请人ID
            fapplicationDeptId: data?.applicationDeptId || undefined, // 申请部门ID
            fapplicationOrgId: data?.applicationOrgId || undefined, // 申请组织ID
            fbillTypeID: data?.billType || undefined, // 单据类型ID
            fcurrencyId: data?.currencyId || undefined, // 币种ID
            fnote: data?.note || undefined, // 备注
            fsuggestSupplierId: data?.fsuggestSupplierId || undefined // 建议供应商ID
          }
          this.kingdeeStatus = data?.status
          const kingdeeColumns = localStorage.getItem(`kingdee_purchase_inventory_${this.userId}`)
          if (kingdeeColumns) this.kingdeeColumns = JSON.parse(kingdeeColumns)
          this.open = true
          this.$nextTick(() => {
            if (this.$refs.entitieTable) this.$refs.entitieTable.bodyWrapper.scrollLeft = 0
          })
        } else {
          this.$message.error(msg || '获取采购申请单详情失败，请稍后重试')
        }
      } catch (error) {
        console.error('获取采购申请单详情时发生错误:', error)
        this.$message.error('获取采购申请单详情时发生错误，请稍后重试')
      } finally {
        // 确保无论成功还是失败都关闭loading
        loading.close()
      }
    },
    tableRowClassName({ row, rowIndex }) {
      if (!row.isFind) return 'notFind'
      return ''
    },
    // 关闭
    beforeClose() {
      this.handleClose()
    },
    // 关闭
    async handleClose(flag = false) {
      if (this.fid && !this.isCreate && !this.hasSuccessfully) {
        try {
          await deletePurchaseApplyV3({ id: this.fid })
          this.fid = undefined
          this.$nextTick(() => {
            this.open = false
            this.$emit('callBack', flag)
          })
        } catch (error) {
          this.$message.error(error.message)
        }
      } else {
        this.open = false
        this.$emit('callBack', flag)
      }
    },
    // 确定
    handleSubmit() {
      // 过滤掉没有申请数量的行
      if (this.form.entities.length > 1) this.form.entities = this.form.entities.filter(item => item.freqQty)
      this.$nextTick(() => {
        this.$refs.form.validate(valid => {
          if (valid) {
            let { contractId, entities, fapplicantId, fapplicationDeptId, fapplicationOrgId, fbillTypeID, fcurrencyId, fnote, fsuggestSupplierId, supplierName } = this.form
            const applicant = this.ApplicantList.find(applicant => applicant.FStaffNumber == fapplicantId)
            entities = entities.map(item => {
              return {
                fmaterialId: item.fmaterialId, // 物料编码
                fmaterialDesc: item.fmaterialDesc, // 物料名称
                funitId: item.funitId, // 申请单位ID
                freqQty: item.freqQty, // 申请数量
                fapproveQty: item.freqQty, // 批准数量
                farrivalDate: item.farrivalDate, // 到货日期
                fpurchaseOrgId: fapplicationOrgId, // 采购组织ID
                fpurchaserId: applicant?.FNumber, // 采购员
                frequireOrgId: fapplicationOrgId, // 需求组织ID
                fpriceUnitId: item.funitId, // 计价单位ID
                fpriceUnitQty: item.freqQty, // 计价数量
                fsuggestSupplierId: item.fsuggestSupplierId, // 建议供应商
                supplierName: item.supplierName, // 供应商名称
                fevaluatePrice: item.fevaluatePrice, // 单价
                ftaxprice: item.ftaxprice, // 含税单价
                ftaxrate: item.ftaxrate, // 税率
                fentryNote: item.fentryNote, // 备注
                freqstockunitid: item.funitId, // 库存单位ID
                famount: item.famount || (item.freqQty && item.fevaluatePrice && parseFloat((item.freqQty * item.fevaluatePrice).toFixed(5))), // 金额
                freqamount: item.freqamount || (item.freqQty && item.ftaxprice && parseFloat((item.freqQty * item.ftaxprice).toFixed(5))), // 含税金额
                otherAmount: item.otherAmount // 其他费用
              }
            })
            const data = { contractId, entities, fapplicantId, fapplicationDeptId, fapplicationOrgId, fbillTypeID, fcurrencyId, fnote }
            // 缓存申请组织、申请部门、单据类型、币别
            localStorage.setItem('kingdee_fapplicationOrgId', fapplicationOrgId)
            localStorage.setItem('kingdee_fapplicationDeptId', fapplicationDeptId)
            localStorage.setItem('kingdee_fbillTypeID', fbillTypeID)
            localStorage.setItem('kingdee_fcurrencyId', fcurrencyId)
            localStorage.setItem('kingdee_fapplicantId', fapplicantId)
            if (data.contractId) {
              addPurchaseApply(data).then(res => {
                const { code, msg } = res
                if (code === 200) {
                  this.$message.success('操作成功')
                  this.hasSuccessfully = true
                  this.open = false
                  this.$emit('callBack', true)
                } else if (code === 400) {
                  // prettier-ignore
                  if (msg.includes('单据体')) {
                    const row = msg.match(/\d+/)
                    const rowNumber = row[0]
                    const lineIndex = msg.indexOf(`第${rowNumber}行`)
                    const fieldsAfterLine = msg.slice(lineIndex).match(/“([^”]+)”是([^，]+)/g).map(match => {
                      const parts = match.match(/“([^”]+)”是([^，]+)/)
                      return parts[1]
                    })
                    this.$message.error(`请检查第${rowNumber}行${fieldsAfterLine.toString()}是否正确`)
                  } else this.$message.error('请认真检查物料编码、申请单位等信息')
                } else this.$message.error(msg)
              })
            } else {
              data.fid = this.fid
              addPurchaseApplyV2(data).then(res => {
                const { code, msg } = res
                if (code === 200) {
                  this.$message.success('操作成功')
                  this.hasSuccessfully = true
                  if (this.isPush) {
                    const { number } = res.data
                    if (number) {
                      this.open = false
                      this.$nextTick(() => {
                        this.$refs.purchaseApplyDetail.getInfo({ BillNo: number })
                      })
                    } else {
                      this.open = false
                      this.$emit('callBack', true)
                    }
                  } else {
                    this.open = false
                    this.$emit('callBack', true)
                  }
                } else if (code === 400) {
                  // prettier-ignore
                  if (msg.includes('单据体')) {
                    const row = msg.match(/\d+/)
                    const rowNumber = row[0]
                    const lineIndex = msg.indexOf(`第${rowNumber}行`)
                    const fieldsAfterLine = msg.slice(lineIndex).match(/“([^”]+)”是([^，]+)/g).map(match => {
                      const parts = match.match(/“([^”]+)”是([^，]+)/)
                      return parts[1]
                    })
                    this.$message.error(`请检查第${rowNumber}行${fieldsAfterLine.toString()}是否正确`)
                  } else this.$message.error('请认真检查物料编码、申请单位等信息')
                } else this.$message.error(msg)
              })
            }
          }
        })
      })
    },
    // 删除
    handleDelete(row) {
      this.form.entities.splice(this.form.entities.indexOf(row), 1)
    },
    // 单价变化改变含税单价，并计算金额和含税金额
    handlefevaluatePriceChange(row) {
      // 如果税率不为空则计算含税单价
      const price = parseFloat(row.fevaluatePrice) || 0
      const taxRate = parseFloat(row.ftaxrate) || 0
      if (!taxRate) {
        row.ftaxprice = price
      } else {
        row.ftaxprice = parseFloat((price * (1 + taxRate / 100)).toFixed(5))
      }
      // 计算金额和含税金额
      this.calculateAmounts(row)
    },
    // 含税单价变化，改变单价，并计算金额和含税金额
    handleftaxpriceChange(row) {
      // 如果税率不为空则计算单价
      const taxPrice = parseFloat(row.ftaxprice) || 0
      const taxRate = parseFloat(row.ftaxrate) || 0
      if (!taxRate) {
        row.fevaluatePrice = taxPrice
      } else {
        row.fevaluatePrice = parseFloat((taxPrice / (1 + taxRate / 100)).toFixed(5))
      }
      // 计算金额和含税金额
      this.calculateAmounts(row)
    },
    // 税率变化，根据单价计算含税单价，并计算金额和含税金额
    handleftaxrateChange(row) {
      // 如果单价不为空则计算含税单价
      const price = parseFloat(row.fevaluatePrice) || 0
      const taxRate = parseFloat(row.ftaxrate) || 0
      if (price) {
        row.ftaxprice = parseFloat((price * (1 + taxRate / 100)).toFixed(5))
      }
      // 计算金额和含税金额
      this.calculateAmounts(row)
    },
    // 其他费用变化，改变单价
    handleotherAmountChange(row, index) {
      if (!row.freqQty) return
      let num = 0
      this.$refs.form.validateField(`entities.${index}.otherAmount`, error => {
        if (!error) {
          num++
          if (num == 1) {
            const otherAmount = parseFloat(row.otherAmount) || 0
            const qty = parseFloat(row.freqQty) || 0
            const currentPrice = parseFloat(row.fevaluatePrice) || 0
            if (qty > 0) {
              const newPrice = parseFloat((otherAmount / qty).toFixed(5))
              row.fevaluatePrice = parseFloat((currentPrice + newPrice).toFixed(5))
            }
            this.handlefevaluatePriceChange(row)
          }
        }
      })
    },
    // 申请数量变化，计算金额和含税金额
    handleQtyChange(row) {
      this.calculateAmounts(row)
    },
    // 金额变化，根据数量计算单价和含税单价
    handleAmountChange(row) {
      const amount = parseFloat(row.famount) || 0
      const qty = parseFloat(row.freqQty) || 0
      if (!qty || !amount) return
      // 根据金额和数量计算单价
      row.fevaluatePrice = parseFloat((amount / qty).toFixed(5))
      const taxRate = parseFloat(row.ftaxrate) || 0
      const price = parseFloat(row.fevaluatePrice) || 0
      // 根据单价和税率计算含税单价
      if (taxRate) {
        row.ftaxprice = parseFloat((price * (1 + taxRate / 100)).toFixed(5))
      } else {
        row.ftaxprice = price
      }
      // 计算含税金额
      const taxPrice = parseFloat(row.ftaxprice) || 0
      row.freqamount = parseFloat((qty * taxPrice).toFixed(5))
    },
    // 含税金额变化，计算含税单价
    handleTaxAmountChange(row) {
      const taxAmount = parseFloat(row.freqamount) || 0
      const qty = parseFloat(row.freqQty) || 0
      if (!qty || !taxAmount) return
      // 根据含税金额和数量计算含税单价
      row.ftaxprice = parseFloat((taxAmount / qty).toFixed(5))
      const taxRate = parseFloat(row.ftaxrate) || 0
      const taxPrice = parseFloat(row.ftaxprice) || 0
      // 根据含税单价和税率计算单价
      if (taxRate) {
        row.fevaluatePrice = parseFloat((taxPrice / (1 + taxRate / 100)).toFixed(5))
      } else {
        row.fevaluatePrice = taxPrice
      }
      // 计算金额
      const price = parseFloat(row.fevaluatePrice) || 0
      row.famount = parseFloat((qty * price).toFixed(5))
    },
    // 计算金额和含税金额
    calculateAmounts(row) {
      const qty = parseFloat(row.freqQty) || 0
      if (!qty) return
      // 计算金额
      const price = parseFloat(row.fevaluatePrice) || 0
      if (price) {
        row.famount = parseFloat((qty * price).toFixed(5))
      }
      // 计算含税金额
      const taxPrice = parseFloat(row.ftaxprice) || 0
      if (taxPrice) {
        row.freqamount = parseFloat((qty * taxPrice).toFixed(5))
      }
    },
    // 返回金额
    returnAmount(row) {
      const qty = parseFloat(row.freqQty) || 0
      const price = parseFloat(row.fevaluatePrice) || 0
      return qty && price ? parseFloat((qty * price).toFixed(5)) : 0
    },
    // 返回含税金额
    returnTaxAmount(row) {
      const qty = parseFloat(row.freqQty) || 0
      const taxPrice = parseFloat(row.ftaxprice) || 0
      return qty && taxPrice ? parseFloat((qty * taxPrice).toFixed(5)) : 0
    },
    // 查看产品
    handleViewProduct(row) {
      this.$refs.productInfo.handleView(row.info)
    },
    // 查看物料
    handleViewMaterial(row) {
      this.showMaterialDetail = true
      this.$nextTick(() => {
        this.$refs.materialDetail.getInfo(row.fmaterialId)
      })
    },
    // 刷新状态替换kingdeeStatus
    // prettier-ignore
    refresh(refresh = true) {
      let params = { number: this.kingdeeNumber }
      if (refresh) params.refresh = true
      getPurchaseApplyDetail(params).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          this.$set(this, 'kingdeeStatus', data?.status)
        } else {
          this.$message.error(msg || '刷新状态失败，请稍后重试')
        }
      }).catch(error => {
        console.error('刷新状态时发生错误:', error)
        this.$message.error('刷新状态时发生错误，请稍后重试')
      })
    },
    // 金蝶提交、审核、撤销、反审核、删除
    // prettier-ignore
    handleKingdeeDo(type = '') {
      const number = this.kingdeeNumber
      if (!number) return
      switch (type) {
        case 'submit':
          // 提交
          this.$modal.confirm('确认要提交该采购申请单吗？').then(() => {
            submitPurchaseApply({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('提交成功')
                this.refresh()
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'audit':
          // 审核
          this.$modal.confirm('确认要审核该采购申请单吗？').then(() => {
            auditPurchaseApply({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('审核成功')
                this.refresh()
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'revoke':
          // 撤销
          this.$modal.confirm('确认要撤销该采购申请单吗？').then(() => {
            cancelPurchaseApply({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('撤销成功')
                this.refresh()
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'unAudit':
          // 反审核
          this.$modal.confirm('确认要反审核该采购申请单吗？').then(() => {
            unAuditPurchaseApply({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('反审核成功')
                this.refresh()
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'delete':
          // 删除
          this.$modal.confirm('确认要删除该采购申请单吗？').then(() => {
            deletePurchaseApply({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('删除成功')
                this.handleClose(true)
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'push':
          // 下推
          this.initPushForm()
          this.pushForm.target = 'PUR_PurchaseOrder'
          this.handleTargetChange('PUR_PurchaseOrder')
          break
      }
    },
    // 初始化下推表单
    initPushForm() {
      this.pushForm = {
        number: this.kingdeeNumber,
        target: undefined,
        ruleId: undefined,
        targetBillTypeId: undefined
      }
      this.$nextTick(() => {
        this.resetForm('pushForm')
      })
      this.pushOpen = true
    },
    // 切换下推单据
    handleTargetChange(val) {
      if (val === 'PUR_PurchaseOrder') {
        this.setupPurchaseOrderPush()
        return
      }
      this.clearPushFormFields()
    },
    // 设置采购订单下推
    setupPurchaseOrderPush() {
      this.pushForm.ruleId = this.calculateRule?.[0]?.value || undefined
      this.pushForm.targetBillTypeId = undefined
      // 计算目标单据类型
      this.$nextTick(() => {
        this.calculateTargetBillType()
      })
    },
    // 清空下推表单字段
    clearPushFormFields() {
      this.pushForm.ruleId = undefined
      this.pushForm.targetBillTypeId = undefined
    },
    // 计算目标单据类型
    calculateTargetBillType() {
      const { ruleId } = this.pushForm
      const billType = this.kingdeeRequestType || ''
      const arr = this.pushBillTypeAllOptions.filter(item => item.target === ruleId) || []
      const newArr = arr.filter(item => item.billType === billType) || []
      this.pushForm.targetBillTypeId = newArr?.[0]?.value || undefined
    },
    // 下推提交
    handlePushSubmit() {
      const { number } = this.pushForm
      if (!number) return
      this.$refs.pushForm.validate(valid => {
        if (valid) {
          this.executePushDown()
        }
      })
    },
    // 执行下推操作
    // prettier-ignore
    executePushDown() {
      pushPurchaseApply(this.pushForm).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          this.handlePushSuccess(data)
        } else if (code === 400) {
          this.handlePushError(msg)
        } else {
          this.$message.error(msg)
        }
      }).catch(error => {
        console.error('下推操作失败:', error)
        this.$message.error('下推操作失败，请重试')
      })
    },
    // 处理下推成功
    handlePushSuccess(data) {
      if (this.pushForm.target === 'PUR_PurchaseOrder') {
        this.pushOpen = false
        this.showOrderCreate = true
        const { responseStatus } = data
        const fid = responseStatus?.successEntitys?.[0]?.id
        if (!fid) {
          this.$message.error('参数错误，请重试！')
          return
        }
        this.$nextTick(() => {
          this.$refs.purchaseOrderCreate.init(undefined, 'order', { fid })
        })
      } else {
        this.$message.success('下推成功')
        this.pushOpen = false
        this.handleClose(true)
      }
    },
    // 处理下推错误
    handlePushError(msg) {
      this.$alert(msg || '操作失败，请重试或使用金蝶进行操作！', '提示', {
        type: 'info',
        confirmButtonText: '确定',
        callback: () => {
          this.pushOpen = false
        }
      })
    },
    orderCreateCallBack() {
      this.showOrderCreate = false
    },
    // 选择物料回调
    handleMaterialSearchSelect(event, row) {
      const obj = this.UnitList.find(item => item.FName == event.Unit)
      this.$set(row, 'fmaterialId', event.Number)
      this.$set(row, 'fmaterialName', event.Name)
      this.$set(row, 'fmaterialDesc', event.Name)
      this.$set(row, 'Specification', event.Specification || '')
      this.$set(row, 'funitId', obj?.FNumber || '')
      this.$set(row, 'isChange', row.funitId != obj?.FNumber)
      const product = row.info
      const source = row.source
      const data = { materialNumber: event.Number, materialName: event.Name, productId: (product && product.id) || undefined, source, unit: event.Unit }
      if (product && product.id) {
        saveProductKingdeeCode(data).then(res => {
          const { code, msg } = res
          if (code !== 200) this.$message.error(msg)
        })
      }
      if (this.isCreate) this.handleAdd()
    },
    customSummary(param) {
      const { columns, data } = param
      const result = columns.map((column, index) => {
        if (index === 0) {
          return '合计'
        }
        if (column.property === 'amount' || column.property === 'taxAmount') {
          return `${this.calculateTotal(data, column.property)}`
        }
        return ''
      })
      return result
    },
    // 计算合计
    calculateTotal(data, key) {
      return data.reduce((total, item) => {
        if (key === 'amount') {
          const amount = parseFloat(item.famount) || parseFloat(item.freqQty) * parseFloat(item.fevaluatePrice) || 0
          return parseFloat((total + amount).toFixed(5))
        } else if (key === 'taxAmount') {
          const taxAmount = parseFloat(item.freqamount) || parseFloat(item.freqQty) * parseFloat(item.ftaxprice) || 0
          return parseFloat((total + taxAmount).toFixed(5))
        }
        return total
      }, 0)
    },
    // 到货日期变化
    handleArrivalDateChange(e) {
      if (!e) return
      this.form.entities.forEach(entity => {
        if (!entity.farrivalDate) this.$set(entity, 'farrivalDate', e)
      })
    },
    // 更新显隐列
    updateKingdeeColumns(columns) {
      this.kingdeeColumns = columns
      localStorage.setItem(`kingdee_purchase_inventory_${this.userId}`, JSON.stringify(columns))
    }
  }
}
</script>
<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';
::v-deep {
  .el-table.custom-table .el-table__body-wrapper .el-table__row--striped.notFind td.el-table__cell,
  .el-table.custom-table .el-table__fixed-body-wrapper .el-table__row--striped.notFind td.el-table__cell {
    background: #ffaaaa !important;
  }
  .el-form.custom-form {
    .el-form-item__label {
      line-height: 20px;
      min-height: 40px;
      display: inline-flex;
      align-items: center;
      font-weight: normal;
      color: $disabled;
      text-align: left;
    }
  }
  .custom-table {
    .notFind {
      background: #ffaaaa;
    }
    .el-form-item {
      margin-top: 10px !important;
      margin-bottom: 10px !important;
      .el-form-item__error {
        top: 95%;
        padding-top: 0;
      }
    }
    .el-table__fixed-right {
      .el-table__cell {
        padding: 0;
        &.is-leaf {
          border-bottom-width: 0;
        }
        .table-btn {
          width: 90px;
          height: 30px;
          border-radius: 5px;
          border: 1px solid #cbd7e2;
          background-color: transparent;
          cursor: pointer;
          margin: 2px 5px;
          font-size: 12px;
          color: $info;
          &.danger {
            border-color: $red;
            color: $red;
            &:hover {
              background-color: $red;
              color: $white;
            }
            &:disabled {
              cursor: no-drop;
            }
            &.hasbg {
              background-color: #ffe5e5;
              &:hover {
                background-color: $red;
                color: $white;
              }
            }
          }
        }
      }
    }
    .custom-date-picker {
      .el-date-editor {
        .el-input__inner {
          padding-right: 15px;
        }
      }
    }
    .custom-select {
      .el-input {
        .el-input__inner {
          padding-right: 15px;
        }
      }
    }
    td.el-table__cell.el-table-tip {
      .cell {
        overflow: unset;
        position: relative;
        .el-form-item.isChange {
          .el-input__inner {
            border-color: #f43f3f;
          }
        }
        .table-cell-tip {
          display: flex;
          align-items: center;
          width: 500px;
          color: #f43f3f;
          position: absolute;
          top: -13px;
          left: 10px;
          z-index: 2;
        }
      }
    }
    .el-table__footer-wrapper {
      .el-table__cell.is-center {
        .cell {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
  }
}
.kindeeButton {
  display: flex;
  align-items: center;
  padding-bottom: 20px;
  .same {
    display: flex;
    align-items: center;
    font-size: 16px;
    padding-left: 20px;
    color: #2e73f3;
    &.danger {
      color: #f43f3f;
    }
  }
}
.custom-dialog.custom-material-dialog ::v-deep {
  .el-dialog__body {
    padding: 0;
  }
}
.el-col-50 {
  width: 20%;
}
.custom-push-target {
  .el-col.el-col-12 {
    .el-radio {
      height: 30px;
      display: flex;
      align-items: center;
    }
  }
}
</style>
