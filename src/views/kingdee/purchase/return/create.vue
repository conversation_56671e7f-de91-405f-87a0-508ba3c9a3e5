<template>
  <div>
    <el-dialog v-dialogDragBox :title="title" :visible.sync="open" width="90%" class="custom-dialog" :before-close="beforeClose" append-to-body>
      <div style="padding: 0 20px">
        <el-form :model="form" :rules="rules" ref="form" label-width="6em" class="custom-form">
          <el-row :gutter="20" style="display: flex; flex-wrap: wrap">
            <!-- 单据类型 -->
            <el-col :span="6">
              <el-form-item label="单据类型" prop="fbilltypeid">
                <el-select v-model="form.fbilltypeid" filterable placeholder="请选择单据类型" style="width: 100%" @change="handleChangeBillType">
                  <el-option v-for="item in billTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 业务类型 -->
            <el-col :span="6">
              <el-form-item label="业务类型" prop="fbusinesstype">
                <el-select v-model="form.fbusinesstype" placeholder="请选择业务类型" style="width: 100%" disabled>
                  <el-option v-for="item in businessTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 退料组织 -->
            <el-col :span="6">
              <el-form-item label="退料组织" prop="fstockorgid">
                <el-select v-model="form.fstockorgid" filterable placeholder="请选择退料组织" style="width: 100%" @change="handleChangeStockOrg">
                  <el-option v-for="item in ApplicationOrgId" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 需求组织 -->
            <el-col :span="6">
              <el-form-item label="需求组织" prop="frequireorgid">
                <el-select v-model="form.frequireorgid" filterable placeholder="请选择需求组织" style="width: 100%">
                  <el-option v-for="item in ApplicationOrgId" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 供应商 -->
            <el-col :span="6">
              <el-form-item label="供应商" prop="fsuppliername">
                <supplier-search-select :keyword.sync="form.fsuppliername" :useOrg="form.fstockorgid" :showLabel="false" isBack @callBack="handleSupplierSelect($event)" style="width: 100%" />
              </el-form-item>
            </el-col>
            <!-- 退料部门 -->
            <el-col :span="6">
              <el-form-item label="退料部门" prop="fmrdeptid">
                <el-select v-model="form.fmrdeptid" filterable placeholder="请选择退料部门" style="width: 100%" @change="handleChangeStockDept">
                  <el-option v-for="item in ApplicationDeptId.filter(ite => ite.FUseOrg == form.fstockorgid)" :key="item.FNumber" :label="item.FName" :value="item.FNumber"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 仓管员 -->
            <el-col :span="6">
              <el-form-item label="仓管员" prop="fstockerid">
                <el-select v-model="form.fstockerid" placeholder="请选择仓管员" style="width: 100%" :disabled="!form.fstockorgid || !form.fmrdeptid">
                  <el-option v-for="item in calculateStockerList" :key="item.Number" :label="item.Name" :value="item.Number">
                    <span style="float: left">{{ item.Name }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.FDept }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 采购组织 -->
            <el-col :span="6">
              <el-form-item label="采购组织" prop="fpurchaseorgid">
                <el-select v-model="form.fpurchaseorgid" filterable placeholder="请选择采购组织" style="width: 100%" @change="handleChangeSaleOrg">
                  <el-option v-for="item in ApplicationOrgId" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 采购部门 -->
            <el-col :span="6">
              <el-form-item label="采购部门" prop="fpurchasedeptid">
                <el-select v-model="form.fpurchasedeptid" filterable placeholder="请选择采购部门" style="width: 100%" @change="handleChangeSaleDept">
                  <el-option v-for="item in ApplicationDeptId.filter(ite => ite.FUseOrg == form.fpurchaseorgid)" :key="item.FNumber" :label="item.FName" :value="item.FNumber"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 采购员 -->
            <el-col :span="6">
              <el-form-item label="采购员" prop="fpurchaserid">
                <el-select v-model="form.fpurchaserid" filterable placeholder="请选择采购员" style="width: 100%" :disabled="!form.fpurchaseorgid || !form.fpurchasedeptid">
                  <el-option v-for="item in calculateApplicantList" :key="item.Number" :label="item.Name" :value="item.Number">
                    <span style="float: left">{{ item.Name }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.FDept }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 退料方式 -->
            <el-col :span="6">
              <el-form-item label="退料方式" prop="fmrmode">
                <el-select v-model="form.fmrmode" filterable placeholder="请选择退料方式" style="width: 100%" @change="handleChangeReturnMode">
                  <el-option v-for="item in ReturnModeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 退料类型 -->
            <el-col :span="6">
              <el-form-item label="退料类型" prop="fmrtype">
                <el-select v-model="form.fmrtype" placeholder="请选择退料类型" style="width: 100%" disabled>
                  <el-option v-for="item in ReturnTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 补料方式 -->
            <!-- <el-col :span="6">
              <el-form-item label="补料方式" prop="replenishMode">
                <el-select v-model="form.replenishMode" placeholder="请选择补料方式" style="width: 100%" disabled>
                  <el-option v-for="item in ReplenishTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col> -->
            <!-- 退料日期 -->
            <el-col :span="6">
              <el-form-item label="退料日期" prop="fdate">
                <el-date-picker v-model="form.fdate" type="date" placeholder="请选择退料日期" style="width: 100%"></el-date-picker>
              </el-form-item>
            </el-col>
            <!-- 结算组织 -->
            <el-col :span="6">
              <el-form-item label="结算组织" prop="fsettleorgid">
                <el-select v-model="form.fsettleorgid" filterable placeholder="请选择结算组织" style="width: 100%">
                  <el-option v-for="item in ApplicationOrgId" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 定价时点 -->
            <el-col :span="6">
              <el-form-item label="定价时点" prop="fpricetimepoint">
                <el-select v-model="form.fpricetimepoint" filterable placeholder="请选择定价时点" style="width: 100%">
                  <el-option v-for="item in PriceTimePointOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 结算币别 -->
            <el-col :span="6">
              <el-form-item label="结算币别" prop="fsettlecurrid">
                <el-select v-model="form.fsettlecurrid" filterable placeholder="请选择结算币别" style="width: 100%">
                  <el-option v-for="item in CurrencyId" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 货主类型 -->
            <el-col :span="6">
              <el-form-item label="货主类型" prop="fownertypeidhead">
                <el-select v-model="form.fownertypeidhead" filterable placeholder="请选择货主类型" style="width: 100%" disabled>
                  <el-option v-for="item in OwnerTypeIdHeadOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 货主 -->
            <el-col :span="6">
              <el-form-item label="货主" prop="fowneridhead">
                <el-select v-model="form.fowneridhead" filterable placeholder="请选择货主" style="width: 100%" disabled>
                  <el-option v-for="item in ApplicationOrgId" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 备注 -->
            <el-col :span="6">
              <el-form-item label="备注" prop="fdescription">
                <el-input v-model="form.fdescription" placeholder="请输入备注"></el-input>
              </el-form-item>
            </el-col>
            <!-- 明细信息 -->
            <el-col :span="24">
              <el-table :data="form.entities" style="width: 100%" stripe class="custom-table custom-table-cell0" show-summary :summary-method="getSummary">
                <!-- 序号 -->
                <el-table-column type="index" label="序号" width="50" align="center"></el-table-column>
                <!-- 物料编码 -->
                <el-table-column label="物料编码" align="center" min-width="120" show-overflow-tooltip>
                  <template slot-scope="scope">
                    <template v-if="isCreate">
                      <el-tooltip effect="dark" :content="scope.row.fmaterialid" :disabled="!scope.row.fmaterialid">
                        <el-form-item label-width="0" :prop="`entities.${scope.$index}.fmaterialid`" :rules="rules.fmaterialid">
                          <material-search-select ref="materialSearchSelect" :keyword.sync="scope.row.fmaterialid" :useOrg="form.fstockorgid" size="small" :showLabel="false" @callBack="handleMaterialSearchSelect($event, scope.row)" :options="[{ Number: scope.row.fmaterialid, Name: scope.row.fmaterialname, Specification: scope.row.fmaterialmodel }]" />
                        </el-form-item>
                      </el-tooltip>
                    </template>
                    <template v-else>
                      <span class="table-link" @click="handleMaterialNumber(scope.row.fmaterialid)">{{ scope.row.fmaterialid }}</span>
                    </template>
                  </template>
                </el-table-column>
                <!-- 物料名称 -->
                <el-table-column label="物料名称" align="center" show-overflow-tooltip>
                  <template slot-scope="{ row }">{{ row.fmaterialname }}</template>
                </el-table-column>
                <!-- 规格型号 -->
                <el-table-column label="规格型号" align="center" show-overflow-tooltip>
                  <template slot-scope="{ row }">{{ row.fmaterialmodel }}</template>
                </el-table-column>
                <!-- 库存单位 -->
                <el-table-column prop="funitid" label="库存单位" width="100" align="center">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.funitid`" :rules="rules.funitid">
                      <el-select v-model="scope.row.funitid" filterable placeholder="请选择收料单位" style="width: 100%" size="small">
                        <el-option v-for="item in UnitList" :key="item.FNumber" :label="item.FName" :value="item.FNumber"></el-option>
                      </el-select>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 实退数量 -->
                <el-table-column prop="frmrealqty" label="实退数量" width="100" align="center">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.frmrealqty`" :rules="rules.frmrealqty">
                      <el-input v-model="scope.row.frmrealqty" placeholder="请输入实退数量" style="width: 100%" size="small" @blur="handleChangeRealQty(scope.row)"></el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 补料数量 -->
                <el-table-column prop="freplenishqty" label="补料数量" width="100" align="center">
                  <template slot-scope="scope">
                    <template v-if="form.fmrmode == 'B'">
                      <el-input placeholder="" style="width: 100%" size="small" disabled></el-input>
                    </template>
                    <template v-else>
                      <el-form-item label-width="0" :prop="`entities.${scope.$index}.freplenishqty`" :rules="rules.freplenishqty">
                        <el-input v-model="scope.row.freplenishqty" placeholder="请输入补料数量" style="width: 100%" size="small" @blur="handleChangeRealQty(scope.row)"></el-input>
                      </el-form-item>
                    </template>
                  </template>
                </el-table-column>
                <!-- 扣款数量 -->
                <el-table-column prop="fkeapamtqty" label="扣款数量" width="100" align="center">
                  <template slot-scope="scope">
                    <template v-if="form.fmrmode == 'A'">
                      <el-input v-model="scope.row.fkeapamtqty" placeholder="" style="width: 100%" size="small" disabled></el-input>
                    </template>
                    <template v-else>
                      <el-form-item label-width="0" :prop="`entities.${scope.$index}.fkeapamtqty`" :rules="rules.fkeapamtqty">
                        <el-input v-model="scope.row.fkeapamtqty" placeholder="请输入扣款数量" style="width: 100%" size="small" @blur="handleChangeRealQty(scope.row)"></el-input>
                      </el-form-item>
                    </template>
                  </template>
                </el-table-column>
                <!-- 计价单位 -->
                <el-table-column prop="fpriceunitid" label="计价单位" width="100" align="center">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.fpriceunitid`" :rules="rules.fpriceunitid">
                      <el-select v-model="scope.row.fpriceunitid" filterable placeholder="请选择计价单位" style="width: 100%" size="small">
                        <el-option v-for="item in UnitList" :key="item.FNumber" :label="item.FName" :value="item.FNumber"></el-option>
                      </el-select>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 计价数量 -->
                <el-table-column prop="priceQty" label="计价数量" width="100" align="center"></el-table-column>
                <!-- 仓库 -->
                <el-table-column prop="fstockname" label="仓库" width="120" align="center">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.fstockname`" :rules="rules.fstockname">
                      <stock-search-select size="small" :useOrg="form.fstockorgid" :showLabel="false" :keyword.sync="scope.row.fstockname" style="width: 100%" @callBack="handleStockSelect($event, scope.row)" />
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 库存状态 -->
                <el-table-column prop="fstockstatusid" label="库存状态" width="100" align="center">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.fstockstatusid`" :rules="rules.fstockstatusid">
                      <el-select v-model="scope.row.fstockstatusid" filterable placeholder="请选择库存状态" style="width: 100%" size="small">
                        <el-option v-for="item in stockStatusOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                      </el-select>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 备注 -->
                <el-table-column prop="fnote" label="备注" width="120" align="center">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.fnote`" :rules="rules.fnote">
                      <el-input v-model="scope.row.fnote" placeholder="请输入备注" style="width: 100%" size="small"></el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 单价 -->
                <el-table-column prop="fprice" label="单价" width="100" align="center">
                  <template slot-scope="scope">
                    <template v-if="scope.row.fgiveaway">
                      <el-input placeholder="" style="width: 100%" size="small" disabled></el-input>
                    </template>
                    <template v-else>
                      <el-form-item label-width="0" :prop="`entities.${scope.$index}.fprice`" :rules="rules.fprice">
                        <el-input v-model="scope.row.fprice" placeholder="请输入单价" style="width: 100%" size="small" @change="handleChangePrice(scope.row)"></el-input>
                      </el-form-item>
                    </template>
                  </template>
                </el-table-column>
                <!-- 金额 -->
                <el-table-column prop="amount" label="金额" width="100" align="center">
                  <template slot-scope="scope">
                    <template v-if="scope.row.fgiveaway">
                      <el-input placeholder="" style="width: 100%" size="small" disabled></el-input>
                    </template>
                    <template v-else>
                      <el-form-item label-width="0" :prop="`entities.${scope.$index}.amount`" :rules="rules.amount">
                        <el-input v-model="scope.row.amount" placeholder="请输入金额" style="width: 100%" size="small" @change="handleChangeAmount(scope.row)"></el-input>
                      </el-form-item>
                    </template>
                  </template>
                </el-table-column>
                <!-- 是否赠品 -->
                <el-table-column prop="fgiveaway" label="是否赠品" width="80" align="center">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.fgiveaway`" :rules="rules.fgiveaway">
                      <el-checkbox v-model="scope.row.fgiveaway"></el-checkbox>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 批号 -->
                <el-table-column prop="flot" label="批号" width="120" align="center">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.flot`" :rules="rules.flot">
                      <el-input v-model="scope.row.flot" placeholder="请输入批号" style="width: 100%" size="small"></el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 操作 -->
                <el-table-column label="操作" width="120" align="center" v-if="isCreate">
                  <template slot-scope="scope">
                    <el-button type="text" size="small" icon="el-icon-plus" @click="handleAdd">添加</el-button>
                    <el-button type="text" size="small" :disabled="form.entities.length == 1" icon="el-icon-delete" @click="handleDelete(scope.$index)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn" @click="handleClose()">取消</el-button>
        <el-button class="custom-dialog-btn primary" :loading="loading" @click="handleSubmit">确定</el-button>
      </div>
    </el-dialog>
    <!-- 采购退料单详情 -->
    <purchase-return-detail ref="purchaseReturnDetail" />
  </div>
</template>
<script>
import { kingdee } from '@/minix'
import { isNumber, isNumberLength } from '@/utils/validate'
import StockSearchSelect from '@/components/SearchSelect/stock'
import SupplierSearchSelect from '@/components/SearchSelect/supplier'
import MaterialSearchSelect from '@/components/SearchSelect/material'
import { getSalesmanList } from '@/api/kingdee'
import { getPurchaseReturnDetail2, addPurchaseReturn, getPurchaseReturnDetail, deletePurchaseReturnV2 } from '@/api/kingdee/purchase/return'
import PurchaseReturnDetail from '@/views/kingdee/purchase/return/detail'

export default {
  name: 'CreateReturn',
  mixins: [kingdee],
  components: { StockSearchSelect, SupplierSearchSelect, MaterialSearchSelect, PurchaseReturnDetail },
  data() {
    return {
      fid: undefined,
      form: {},
      rules: {
        fstockorgid: [{ required: true, message: '请选择退料组织', trigger: 'change' }],
        fbilltypeid: [{ required: true, message: '请选择单据类型', trigger: 'change' }],
        fbusinesstype: [{ required: true, message: '请选择业务类型', trigger: 'change' }],
        frequireorgid: [{ required: true, message: '请选择需求组织', trigger: 'change' }],
        fsuppliername: [{ required: true, message: '请选择供应商', trigger: 'change' }],
        fpurchaseorgid: [{ required: true, message: '请选择采购组织', trigger: 'change' }],
        fmrmode: [{ required: true, message: '请选择退料方式', trigger: 'change' }],
        fmrtype: [{ required: true, message: '请选择退料类型', trigger: 'change' }],
        fdate: [{ required: true, message: '请选择退料日期', trigger: 'change' }],
        fmaterialid: [{ required: true, message: '请选择物料编码', trigger: ['change', 'blur'] }],
        funitid: [{ required: true, message: '请选择库存单位', trigger: 'change' }],
        fpriceunitid: [{ required: true, message: '请选择计价单位', trigger: 'change' }],
        frmrealqty: [
          { validator: isNumber, message: '请输入正确的实退数量', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: 'blur' }
        ],
        freplenishqty: [
          { validator: isNumber, message: '请输入正确的补料数量', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: 'blur' }
        ],
        fkeapamtqty: [
          { validator: isNumber, message: '请输入正确的扣款数量', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: 'blur' }
        ],
        fprice: [
          { validator: isNumber, message: '请输入正确的单价', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: 'blur' }
        ],
        amount: [
          { validator: isNumber, message: '请输入正确的金额', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: 'blur' }
        ],
        fsettlecurrid: [{ required: true, message: '请选择结算币别', trigger: 'change' }],
        fpricetimepoint: [{ required: true, message: '请选择定价时点', trigger: 'change' }],
        fownertypeidhead: [{ required: true, message: '请选择货主类型', trigger: 'change' }]
      },
      title: '新增采购退料单',
      open: false,
      loading: false,
      // 单据类型
      billTypeOptions: [
        { value: 'TLD01_SYS', label: '标准退料单' },
        { value: 'TLD02_SYS', label: '零散采购退料单' },
        { value: 'TLD03_SYS', label: '资产退回单' },
        { value: 'TLD04_SYS', label: '委外退料单' },
        { value: 'TLD05_SYS', label: '分销购销退料单' },
        { value: 'TLD06_SYS', label: 'VMI退料单' },
        { value: 'TLD07_SYS', label: '现购退料单' },
        { value: 'TLD08_SYS', label: '直运退料单' }
      ],
      // 业务类型
      businessTypeOptions: [
        { label: '标准采购', value: 'CG', target: 'TLD01_SYS' },
        { label: '标准委外', value: 'WW', target: 'TLD04_SYS' },
        { label: '资产采购', value: 'ZCCG', target: 'TLD03_SYS' },
        { label: '分销采购', value: 'DRPSALE', target: 'TLD05_SYS' },
        { label: 'VMI采购', value: 'VMICG', target: 'TLD06_SYS' },
        { label: '直运采购', value: 'ZYCG', target: 'TLD08_SYS' }
      ],
      // 库存状态
      stockStatusOptions: [
        { value: 'KCZT01_SYS', label: '可用' },
        { value: 'KCZT02_SYS', label: '待检' },
        { value: 'KCZT03_SYS', label: '冻结' },
        { value: 'KCZT04_SYS', label: '退回冻结' },
        { value: 'KCZT07_SYS', label: '废品' },
        { value: 'KCZT08_SYS', label: '不良' },
        { value: 'KCZT09_SYS', label: '已包装' },
        { value: 'KCZT099_SYS', label: '外借' }
      ],
      // 补料方式
      ReplenishTypeOptions: [
        { label: '按源单补料', value: 'A' },
        { label: '创建补料订单', value: 'B' },
        { label: '零散补料', value: 'C' }
      ],
      // 退料方式
      ReturnModeOptions: [
        { label: '退料补料', value: 'A' },
        { label: '退料并扣款', value: 'B' }
      ],
      // 退料类型
      ReturnTypeOptions: [
        { label: '检验退料', value: 'A' },
        { label: '库存退料', value: 'B' }
      ],
      // 定价时点
      PriceTimePointOptions: [
        { label: '系统日期', value: '1' },
        { label: '单据日期', value: '2' }
      ],
      // 货主类型
      OwnerTypeIdHeadOptions: [
        { label: '业务组织', value: 'BD_OwnerOrg' },
        { label: '供应商', value: 'BD_Supplier' },
        { label: '客户', value: 'BD_Customer' }
      ],
      // 采购员
      applicantList: [],
      // 仓管员
      stockerList: [],
      isCreate: false,
      isPush: false,
      hasSuccessfully: false // 是否已成功提交
    }
  },
  computed: {
    // 采购员列表
    calculateApplicantList() {
      const dept = this.ApplicationDeptId.find(ite => ite.FNumber == this.form.fpurchasedeptid) || {}
      const deptName = dept.FName || ''
      const list = this.applicantList.filter(ite => ite.FDept == deptName)
      const processedList = list.map(item => ({
        ...item,
        Number: item.Number ? item.Number.split('_')[0] : item.Number
      }))
      return processedList
    },
    // 仓管员列表
    calculateStockerList() {
      const dept = this.ApplicationDeptId.find(ite => ite.FNumber == this.form.fmrdeptid) || {}
      const deptName = dept.FName || ''
      const list = this.stockerList.filter(ite => ite.FDept == deptName)
      // 处理仓管员编号，只取前三位数字
      const processedList = list.map(item => ({
        ...item,
        Number: item.Number ? item.Number.split('_')[0] : item.Number
      }))
      return processedList
    }
  },
  methods: {
    // 创建默认实体对象
    createDefaultEntity(options = {}) {
      return {
        fmaterialid: options?.fmaterialid || undefined, // 物料编码
        fmaterialname: options?.fmaterialname || undefined, // 物料名称
        fmaterialmodel: options?.fmaterialmodel || undefined, // 规格型号
        funitid: options?.funitid || undefined, // 库存单位
        frmrealqty: options?.frmrealqty || undefined, // 实退数量
        freplenishqty: options?.freplenishqty || undefined, // 补料数量
        fkeapamtqty: options?.fkeapamtqty || undefined, // 扣款数量
        fpriceunitid: options?.fpriceunitid || undefined, // 计价单位
        priceQty: options?.priceQty || undefined, // 计价数量
        fstockid: options?.fstockid || undefined, // 仓库
        fstockname: options?.fstockname || undefined, // 仓库名称
        fstockstatusid: options?.fstockstatusid || this.stockStatusOptions[0].value, // 库存状态
        fnote: options?.fnote || undefined, // 备注
        fprice: options?.fprice || undefined, // 单价
        amount: options?.amount || undefined, // 金额
        fgiveaway: options?.fgiveaway || undefined, // 是否赠品
        flot: options?.flot || undefined // 批号
      }
    },
    // 关闭
    beforeClose() {
      this.handleClose()
    },
    // 关闭
    async handleClose(flag = false) {
      if (this.fid && !this.isCreate && !this.hasSuccessfully) {
        try {
          await deletePurchaseReturnV2({ id: this.fid })
          this.fid = undefined
          this.$nextTick(() => {
            this.open = false
            this.$emit('callBack', flag)
          })
        } catch (error) {
          this.$message.error(error.message)
        }
      } else {
        this.open = false
        this.$emit('callBack', flag)
      }
    },
    // 表单初始化
    reset() {
      this.form = {
        entities: [], // 明细明细
        fbilltypeid: undefined, // 单据类型
        fbusinesstype: undefined, // 业务类型
        fdate: undefined, // 退料日期
        fdescription: undefined, // 备注
        fid: undefined, // 单据ID
        fmrdeptid: undefined, // 退料部门
        fmrmode: undefined, // 退料方式
        fmrreason: undefined, // 退料原因
        fmrtype: undefined, // 退料类型
        fowneridhead: undefined, // 货主
        fownertypeidhead: undefined, // 货主类型
        fpayconditionid: undefined, // 付款条件
        fpricetimepoint: undefined, // 定价时点
        fpurchasedeptid: undefined, // 采购部门
        fpurchaseorgid: undefined, // 采购组织
        fpurchaserid: undefined, // 采购员
        frequireorgid: undefined, // 需求组织
        fscmjddxsbm: undefined, // 订单销售部门
        fscmjddxsy: undefined, // 订单销售员
        fscmjddxszz: undefined, // 订单销售组织
        fscmjxsddh: undefined, // 销售订单号
        fsettlecurrid: undefined, // 结算币种
        fsettleorgid: undefined, // 结算组织
        fsettletypeid: undefined, // 结算类型
        fstockergroupid: undefined, // 库存组
        fstockerid: undefined, // 仓管员
        fstockorgid: undefined, // 退料组织
        fsupplierid: undefined // 供应商
      }
      this.resetForm('form')
      this.isCreate = false
      this.isPush = false
      this.hasSuccessfully = false
    },
    // 新增采购退料单
    handleCreate() {
      this.reset()
      this.form.fmrmode = this.ReturnModeOptions[0].value
      this.form.fpricetimepoint = this.PriceTimePointOptions[0].value
      this.form.fsettlecurrid = this.CurrencyId[0].value
      this.form.fdate = new Date()
      this.form.fmrtype = 'B'
      this.form.entities = [this.createDefaultEntity()]
      this.isCreate = true
      this.title = '新增采购退料单'
      this.open = true
    },
    // 编辑采购退料单
    // prettier-ignore
    handleUpdate(row) {
      const { BillNo } = row
      if (!BillNo) return this.$message.warning('参数错误，请刷新页面重试')
      const loading = this.$loading({
        lock: true,
        text: '加载中...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      this.reset()
      getPurchaseReturnDetail({ billNo: BillNo }).then(res => {
        const { code, msg, data } = res
        if (code == 200) {
          const info = data?.result?.result || {}
          const detailEntities = info?.PUR_MRBENTRY || []
          const entities = detailEntities.map(item => ({
            fentryid: item?.Id || undefined, // 使用时需检查
            fmaterialid: item?.MATERIALID?.Number || undefined, // 物料编码
            fmaterialname: (item?.MATERIALID?.Name && this.getString(item?.MATERIALID?.Name)) || undefined, // 物料名称
            fmaterialmodel: (item?.MATERIALID?.Specification && this.getString(item?.MATERIALID?.Specification)) || undefined, // 规格型号
            funitid: item?.FUnitID?.Number || undefined, // 库存单位
            frmrealqty: item?.RMREALQTY || 0, // 实退数量
            freplenishqty: item?.REPLENISHQTY || 0, // 补料数量
            fkeapamtqty: item?.KEAPAMTQTY || 0, // 扣款数量
            fpriceunitid: item?.PRICEUNITID?.Number || undefined, // 计价单位
            priceQty: item?.PRICEUNITQTY || 0, // 计价数量
            fstockid: item?.STOCKID?.Number || undefined, // 仓库
            fstockname: (item?.STOCKID?.Name && this.getString(item?.STOCKID?.Name)) || undefined, // 仓库名称
            fstockstatusid: item?.StockStatusId?.Number || undefined, // 库存状态
            fnote: item?.NOTE || undefined, // 备注
            fprice: item?.Price || 0, // 单价
            amount: item?.Amount || 0, // 金额
            fgiveaway: item?.GiveAway || undefined, // 是否赠品
            flot: (item?.Lot && item?.Lot.Number) || undefined // 批号
          }))
          const mrbfin = info?.PUR_MRBFIN?.[0] || {}
          this.form = {
            entities, // 明细明细
            fbilltypeid: info?.BillTypeID?.Number || undefined, // 单据类型
            fbusinesstype: info?.BusinessType || undefined, // 业务类型
            fdate: info?.Date || undefined, // 退料日期
            fdescription: info?.DESCRIPTION || undefined, // 备注
            fid: info?.Id || undefined, // 单据ID
            fmrdeptid: info?.MRDeptId?.Number || undefined, // 退料部门
            fmrmode: info?.MRMODE || undefined, // 退料方式
            fmrreason: info?.MRREASON || undefined, // 退料原因
            fmrtype: info?.MRTYPE || undefined, // 退料类型
            fowneridhead: info?.OwnerIdHead?.Number || undefined, // 货主
            fownertypeidhead: info?.OwnerTypeIdHead || undefined, // 货主类型
            fpayconditionid: mrbfin?.PAYCONDITIONID?.Number || undefined, // 付款条件
            fpricetimepoint: mrbfin?.PRICETIMEPOINT || undefined, // 定价时点
            fpurchasedeptid: info?.PURCHASEDEPTID?.Number || undefined, // 采购部门
            fpurchaseorgid: info?.PURCHASEORGID?.Number || undefined, // 采购组织
            fpurchaserid: info?.PURCHASERID?.Number || undefined, // 采购员
            frequireorgid: info?.RequireOrgId?.Number || undefined, // 需求组织
            fscmjddxsbm: info?.F_SCMJ_DDXSBM?.Number || undefined, // 订单销售部门
            fscmjddxsy: info?.F_SCMJ_DDXSY?.Number || undefined, // 订单销售员
            fscmjddxszz: info?.F_SCMJ_DDXSZZ?.Number || undefined, // 订单销售组织
            fscmjxsddh: info?.F_SCMJ_XSDDH || undefined, // 销售订单号
            fsettlecurrid: mrbfin?.SettleCurrId?.Number || undefined, // 结算币种
            fsettleorgid: mrbfin?.SettleOrgId?.Number || undefined, // 结算组织
            fsettletypeid: mrbfin?.SETTLETYPEID?.Number || undefined, // 结算类型
            fstockergroupid: info?.STOCKERGROUPID?.Number || undefined, // 库存组
            fstockerid: info?.STOCKERID?.Number || undefined, // 仓管员
            fstockorgid: info?.StockOrgId?.Number || undefined, // 退料组织
            fsupplierid: info?.SUPPLIERID?.Number || undefined, // 供应商
            fsuppliername: (info?.SUPPLIERID?.Name && this.getString(info?.SUPPLIERID?.Name)) || undefined // 供应商名称
          }
          this.fid = info?.Id || undefined
          this.title = '编辑采购退料单'
          this.isCreate = true
          this.$nextTick(() => {
            this.ApplicantRemoteMethod()
            this.StockerRemoteMethod()
          })
          this.open = true
        } else this.$message.error(msg)
      }).finally(() => {
        loading.close()
      })
    },
    // 添加明细
    handleAdd() {
      this.form.entities.push(this.createDefaultEntity())
    },
    // 删除明细
    handleDelete(index) {
      this.form.entities.splice(index, 1)
    },
    // 选择物料回调
    handleMaterialSearchSelect(event, row) {
      const obj = this.UnitList.find(item => item.FName == event.Unit)
      this.$set(row, 'fmaterialid', event.Number)
      this.$set(row, 'fmaterialname', event.Name)
      this.$set(row, 'fmaterialmodel', event.Specification)
      this.$set(row, 'funitid', obj?.FNumber || '')
      this.$set(row, 'fpriceunitid', obj?.FNumber || '')
      const hasEmpty = this.form.entities.filter(item => !item.fmaterialid)
      if (hasEmpty.length == 0) this.form.entities.push(this.createDefaultEntity())
    },
    // 初始化
    // prettier-ignore
    initPush(fid, type = undefined) {
      if (!fid) return this.$message.warning('参数错误，请刷新页面重试')
      const loading = this.$loading({
        lock: true,
        text: '加载中...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      this.reset()
      this.isPush = type == 'push'
      getPurchaseReturnDetail2({ fid }).then(res => {
        const { code, msg, data } = res
        if (code == 200) {
          const info = data?.result?.result || {}
          const detailEntities = info?.PUR_MRBENTRY || []
          const entities = detailEntities.map(item => ({
            fentryid: item?.Id || undefined, // 使用时需检查
            fmaterialid: item?.MATERIALID?.Number || undefined, // 物料编码
            fmaterialname: (item?.MATERIALID?.Name && this.getString(item?.MATERIALID?.Name)) || undefined, // 物料名称
            fmaterialmodel: (item?.MATERIALID?.Specification && this.getString(item?.MATERIALID?.Specification)) || undefined, // 规格型号
            funitid: item?.FUnitID?.Number || undefined, // 库存单位
            frmrealqty: item?.RMREALQTY || 0, // 实退数量
            freplenishqty: item?.REPLENISHQTY || 0, // 补料数量
            fkeapamtqty: item?.KEAPAMTQTY || 0, // 扣款数量
            fpriceunitid: item?.PRICEUNITID?.Number || undefined, // 计价单位
            priceQty: item?.PRICEUNITQTY || 0, // 计价数量
            fstockid: item?.STOCKID?.Number || undefined, // 仓库
            fstockname: (item?.STOCKID?.Name && this.getString(item?.STOCKID?.Name)) || undefined, // 仓库名称
            fstockstatusid: item?.StockStatusId?.Number || undefined, // 库存状态
            fnote: item?.NOTE || undefined, // 备注
            fprice: item?.Price || 0, // 单价
            amount: item?.Amount || 0, // 金额
            fgiveaway: item?.GiveAway || undefined, // 是否赠品
            flot: (item?.Lot && item?.Lot.Number) || undefined // 批号
          }))
          const mrbfin = info?.PUR_MRBFIN?.[0] || {}
          this.form = {
            entities, // 明细明细
            fbilltypeid: info?.BillTypeID?.Number || undefined, // 单据类型
            fbusinesstype: info?.BusinessType || undefined, // 业务类型
            fdate: info?.Date || undefined, // 退料日期
            fdescription: info?.DESCRIPTION || undefined, // 备注
            fid, // 单据ID
            fmrdeptid: info?.MRDeptId?.Number || undefined, // 退料部门
            fmrmode: info?.MRMODE || undefined, // 退料方式
            fmrreason: info?.MRREASON || undefined, // 退料原因
            fmrtype: info?.MRTYPE || undefined, // 退料类型
            fowneridhead: info?.OwnerIdHead?.Number || undefined, // 货主
            fownertypeidhead: info?.OwnerTypeIdHead || undefined, // 货主类型
            fpayconditionid: mrbfin?.PAYCONDITIONID?.Number || undefined, // 付款条件
            fpricetimepoint: mrbfin?.PRICETIMEPOINT || undefined, // 定价时点
            fpurchasedeptid: info?.PURCHASEDEPTID?.Number || undefined, // 采购部门
            fpurchaseorgid: info?.PURCHASEORGID?.Number || undefined, // 采购组织
            fpurchaserid: info?.PURCHASERID?.Number || undefined, // 采购员
            frequireorgid: info?.RequireOrgId?.Number || undefined, // 需求组织
            fscmjddxsbm: info?.F_SCMJ_DDXSBM?.Number || undefined, // 订单销售部门
            fscmjddxsy: info?.F_SCMJ_DDXSY?.Number || undefined, // 订单销售员
            fscmjddxszz: info?.F_SCMJ_DDXSZZ?.Number || undefined, // 订单销售组织
            fscmjxsddh: info?.F_SCMJ_XSDDH || undefined, // 销售订单号
            fsettlecurrid: mrbfin?.SettleCurrId?.Number || undefined, // 结算币种
            fsettleorgid: mrbfin?.SettleOrgId?.Number || undefined, // 结算组织
            fsettletypeid: mrbfin?.SETTLETYPEID?.Number || undefined, // 结算类型
            fstockergroupid: info?.STOCKERGROUPID?.Number || undefined, // 库存组
            fstockerid: info?.STOCKERID?.Number || undefined, // 仓管员
            fstockorgid: info?.StockOrgId?.Number || undefined, // 退料组织
            fsupplierid: info?.SUPPLIERID?.Number || undefined, // 供应商
            fsuppliername: (info?.SUPPLIERID?.Name && this.getString(info?.SUPPLIERID?.Name)) || undefined // 供应商名称
          }
          this.fid = fid
          this.title = '新增采购退料单'
          this.$nextTick(() => {
            this.ApplicantRemoteMethod()
            this.StockerRemoteMethod()
          })
          this.open = true
        } else this.$message.error(msg)
      }).finally(() => {
        loading.close()
      })
    },
    // 改变退料方式
    handleChangeReturnMode(val) {
      if (val == 'B') {
        this.form.entities.forEach(item => {
          item.freplenishqty = 0
        })
      }
    },
    // 改变单据类型
    handleChangeBillType(val) {
      const obj = this.businessTypeOptions.find(ite => ite.target == val) || this.businessTypeOptions[0]
      this.form.fbusinesstype = obj.value
    },
    // 采购员远程方法
    ApplicantRemoteMethod() {
      const params = { bizOrg: this.form.fpurchaseorgid, OperatorType: 'CGY' }
      getSalesmanList(params).then(res => {
        const { code, msg, data } = res
        const { data: applicantList } = data
        if (code == 200) {
          this.applicantList = applicantList
        } else this.$message.error(msg)
      })
    },
    // 仓管员远程方法
    StockerRemoteMethod() {
      const params = { bizOrg: this.form.fstockorgid, OperatorType: 'WHY' }
      getSalesmanList(params).then(res => {
        const { code, msg, data } = res
        const { data: stockerList } = data
        if (code == 200) {
          this.stockerList = stockerList
        } else this.$message.error(msg)
      })
    },
    // 改变退料组织
    handleChangeStockOrg(val) {
      this.form.fmrdeptid = undefined // 清空退料部门
      this.form.fstockerid = undefined // 清空仓管员
      // 修改需求组织\采购组织\结算组织\货主
      this.$set(this.form, 'fowneridhead', val)
      this.$set(this.form, 'frequireorgid', val)
      this.$set(this.form, 'fpurchaseorgid', val)
      this.$set(this.form, 'fsettleorgid', val)
      this.$set(this.form, 'fownertypeidhead', 'BD_OwnerOrg')
      this.StockerRemoteMethod()
    },
    // 改变退料部门
    handleChangeStockDept() {
      this.form.fstockerid = undefined // 清空仓管员
    },
    // 改变采购组织
    handleChangeSaleOrg() {
      this.form.fpurchasedeptid = undefined // 清空采购部门
      this.form.fpurchaserid = undefined // 清空采购员
      this.ApplicantRemoteMethod()
    },
    // 改变采购部门
    handleChangeSaleDept() {
      this.form.fpurchaserid = undefined // 清空采购员
    },
    // 供应商选择
    handleSupplierSelect(data) {
      this.$set(this.form, 'fsupplierid', data.FNumber)
      this.$set(this.form, 'fsuppliername', data.FName)
    },
    // 改变实退数量
    handleChangeRealQty(row) {
      const realQty = parseFloat(row.frmrealqty) || 0
      if (this.form.fmrmode !== 'B') this.$set(row, 'freplenishqty', realQty)
      this.$set(row, 'fkeapamtqty', realQty)
      this.$set(row, 'priceQty', realQty)
    },
    // 改变补料数量
    handleChangeReplenishQty(row) {
      const realQty = parseFloat(row.frmrealqty) || 0
      const replenishQty = parseFloat(row.freplenishqty) || 0
      if (replenishQty > realQty) this.$set(row, 'freplenishqty', realQty)
    },
    // 改变扣款数量
    handleChangeKeapamtQty(row) {
      const realQty = parseFloat(row.frmrealqty) || 0
      const keapamtQty = parseFloat(row.fkeapamtqty) || 0
      if (keapamtQty > realQty) this.$set(row, 'fkeapamtqty', realQty)
    },
    // 选择仓库
    handleStockSelect(stock, row) {
      this.$set(row, 'fstockid', stock.Number)
      this.$set(row, 'fstockname', stock.Name)
    },
    // 改变单价
    handleChangePrice(row) {
      const price = parseFloat(row.fprice) || 0
      const realQty = parseFloat(row.frmrealqty) || 0
      const total = parseFloat((price * realQty).toFixed(5)) || 0
      this.$set(row, 'amount', total)
    },
    // 改变金额
    handleChangeAmount(row) {
      const realQty = parseFloat(row.frmrealqty) || 0
      const amount = parseFloat(row.amount) || 0
      const price = parseFloat((amount / realQty).toFixed(5)) || 0
      this.$set(row, 'fprice', price)
    },
    // 获取合计（实退数量、补料数量、扣款数量、金额）
    getSummary(param) {
      const { columns, data } = param
      const result = columns.map((column, index) => {
        if (index === 0) {
          return '合计'
        }
        if (column.property === 'frmrealqty' || column.property === 'freplenishqty' || column.property === 'fkeapamtqty' || column.property === 'amount') {
          return `${this.calculateTotal(data, column.property)}`
        }
        return ''
      })
      return result
    },
    // 计算合计
    calculateTotal(data, key) {
      return data.reduce((total, item) => {
        if (key === 'frmrealqty' || key === 'freplenishqty' || key === 'fkeapamtqty' || key === 'amount') {
          const value = parseFloat(item[key]) || 0
          return parseFloat((total + value).toFixed(5))
        }
        return total
      }, 0)
    },
    // 提交
    // prettier-ignore
    handleSubmit() {
      if (this.form.entities.length > 1) this.form.entities = this.form.entities.filter(item => item.fmaterialid)
      this.$nextTick(() => {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.loading = true
            let data = JSON.parse(JSON.stringify(this.form))
            // 处理退料方式为B时，补料数量设置为0
            if (data.fmrmode === 'B') {
              data.entities.forEach(item => {
                item.freplenishqty = 0
              })
            }
            // 处理是否赠品勾选时，单价和金额设置为0
            data.entities.forEach(item => {
              if (item.fgiveaway) {
                item.fprice = 0
                item.amount = 0
              }
            })
            addPurchaseReturn(data).then(res=> {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('操作成功')
                this.hasSuccessfully = true
                if (this.isPush) {
                  const { number } = res.data
                  if (number) {
                    this.open = false
                    this.$nextTick(() => {
                      this.$refs.purchaseReturnDetail.getInfo({ BillNo: number })
                    })
                  } else {
                    this.open = false
                    this.$emit('callBack', true)
                  }
                } else {
                  this.open = false
                  this.$emit('callBack', true)
                }
              } else this.$message.error(msg)
            }).finally(() => {
              this.loading = false
            })
          }
        })
      })
    },
    // 物料详情
    handleMaterialNumber(number) {
      if (number) this.$parent.handleMaterialNumber(number)
    }
  }
}
</script>
<style scoped lang="scss">
@import '@/assets/styles/custom-new.scss';
::v-deep {
  .custom-table {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    border-bottom: 0;
    margin-bottom: 20px;
    .el-form-item {
      margin-top: 10px !important;
      margin-bottom: 10px !important;
      .el-form-item__error {
        top: 95%;
        padding-top: 0;
      }
    }
    .el-input--prefix .el-input__inner {
      padding-left: 20px;
      padding-right: 20px;
    }
    .el-input--prefix.el-date-editor--date .el-input__inner {
      padding-left: 30px;
    }
    .el-date-editor--datetime .el-input__inner {
      padding-left: 30px;
      padding-right: 30px;
    }
  }
}
</style>
