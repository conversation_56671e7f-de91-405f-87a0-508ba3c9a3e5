<template>
  <div>
    <el-dialog v-dialogDragBox title="采购退料单详情" :visible.sync="open" width="90%" class="custom-dialog" :before-close="beforeClose">
      <div style="padding: 0 20px">
        <div class="kindeeButton" :key="info.DocumentStatus">
          <el-button type="primary" size="medium" v-if="info.DocumentStatus == 'A' || info.DocumentStatus == 'D'" @click="handleKingdeeDo('submit')">提交</el-button>
          <el-button type="primary" size="medium" disabled v-else>提交</el-button>
          <el-button type="warning" size="medium" v-if="info.DocumentStatus == 'B'" @click="handleKingdeeDo('audit')">审核</el-button>
          <el-button type="warning" size="medium" disabled v-else>审核</el-button>
          <el-button type="info" size="medium" v-if="info.DocumentStatus == 'B'" @click="handleKingdeeDo('revoke')">撤销</el-button>
          <el-button type="info" size="medium" disabled v-else>撤销</el-button>
          <el-button type="danger" size="medium" v-if="info.DocumentStatus == 'A' || info.DocumentStatus == 'D'" @click="handleKingdeeDo('delete')">删除</el-button>
          <el-button type="danger" size="medium" disabled v-else>删除</el-button>
          <el-button type="warning" size="medium" v-if="info.DocumentStatus == 'C'" @click="handleKingdeeDo('unAudit')">反审</el-button>
          <el-button type="warning" size="medium" disabled v-else>反审</el-button>
          <el-button type="success" size="medium" v-if="info.DocumentStatus == 'C'" @click="handleKingdeeDo('push')">下推</el-button>
          <el-button type="success" size="medium" disabled v-else>下推</el-button>
        </div>
        <el-descriptions border :column="4" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 4 - 7em - 22px)' }">
          <template slot="title">
            <div class="customTitle">基本信息</div>
          </template>
          <!-- 订单销售组织 -->
          <el-descriptions-item label="订单销售组织">{{ info.F_SCMJ_DDXSZZ && getString(info.F_SCMJ_DDXSZZ.Name) }}</el-descriptions-item>
          <!-- 退料组织 -->
          <el-descriptions-item label="退料组织">{{ info.StockOrgId && getString(info.StockOrgId.Name) }}</el-descriptions-item>
          <!-- 单据类型 -->
          <el-descriptions-item label="单据类型">{{ info.BillTypeID && getString(info.BillTypeID.Name) }}</el-descriptions-item>
          <!-- 需求组织 -->
          <el-descriptions-item label="需求组织">{{ info.RequireOrgId && getString(info.RequireOrgId.Name) }}</el-descriptions-item>
          <!-- 订单销售部门 -->
          <el-descriptions-item label="订单销售部门">{{ info.F_SCMJ_DDXSBM && getString(info.F_SCMJ_DDXSBM.Name) }}</el-descriptions-item>
          <!-- 退料部门 -->
          <el-descriptions-item label="退料部门">{{ info.MRDeptId && getString(info.MRDeptId.Name) }}</el-descriptions-item>
          <!-- 供应商 -->
          <el-descriptions-item label="供应商">{{ info.SUPPLIERID && getString(info.SUPPLIERID.Name) }}</el-descriptions-item>
          <!-- 业务类型 -->
          <el-descriptions-item label="业务类型">{{ info.BusinessType && getOptionLabel(BusinessTypeOptions, info.BusinessType) }}</el-descriptions-item>
          <!-- 订单销售员 -->
          <el-descriptions-item label="订单销售员">{{ info.F_SCMJ_DDXSY && getString(info.F_SCMJ_DDXSY.Name) }}</el-descriptions-item>
          <!-- 库存组 -->
          <el-descriptions-item label="库存组">{{ info.STOCKERGROUPID && getString(info.STOCKERGROUPID.Name) }}</el-descriptions-item>
          <!-- 采购组织 -->
          <el-descriptions-item label="采购组织">{{ info.PURCHASEORGID && getString(info.PURCHASEORGID.Name) }}</el-descriptions-item>
          <!-- 单据编号 -->
          <el-descriptions-item label="单据编号">{{ info.BillNo }}</el-descriptions-item>
          <!-- 销售订单号 -->
          <el-descriptions-item label="销售订单号">{{ info.F_SCMJ_XSDDH }}</el-descriptions-item>
          <!-- 仓管员 -->
          <el-descriptions-item label="仓管员">{{ info.STOCKERID && getString(info.STOCKERID.Name) }}</el-descriptions-item>
          <!-- 采购部门 -->
          <el-descriptions-item label="采购部门">{{ info.PURCHASEDEPTID && getString(info.PURCHASEDEPTID.Name) }}</el-descriptions-item>
          <!-- 退料日期 -->
          <el-descriptions-item label="退料日期">{{ parseTime(info.Date, '{y}-{m}-{d}') }}</el-descriptions-item>
          <!-- 备注 -->
          <el-descriptions-item label="备注">{{ info.DESCRIPTION }}</el-descriptions-item>
          <!-- 补料方式 -->
          <el-descriptions-item label="补料方式">{{ info.REPLENISHMODE && getOptionLabel(ReplenishTypeOptions, info.REPLENISHMODE) }}</el-descriptions-item>
          <!-- 采购员 -->
          <el-descriptions-item label="采购员">{{ info.PURCHASERID && getString(info.PURCHASERID.Name) }}</el-descriptions-item>
          <!-- 单据状态 -->
          <el-descriptions-item label="单据状态">{{ getDocumentStatusLabel(info.DocumentStatus) }}</el-descriptions-item>
          <!-- 退料原因 -->
          <el-descriptions-item label="退料原因">{{ info.MRREASON }}</el-descriptions-item>
          <!-- 退料方式 -->
          <el-descriptions-item label="退料方式">{{ info.MRMODE && getOptionLabel(ReturnModeOptions, info.MRMODE) }}</el-descriptions-item>
          <!-- 退料类型 -->
          <el-descriptions-item label="退料类型">{{ info.MRTYPE && getOptionLabel(ReturnTypeOptions, info.MRTYPE) }}</el-descriptions-item>
        </el-descriptions>
        <!-- 供应商信息 -->
        <el-descriptions border :column="3" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 3 - 7em - 22px)' }">
          <template slot="title">
            <div class="customTitle">供应商信息</div>
          </template>
          <!-- 接收方 -->
          <el-descriptions-item label="接收方">{{ info.ACCEPTORID && getString(info.ACCEPTORID.Name) }}</el-descriptions-item>
          <!-- 接收方联系人 -->
          <el-descriptions-item label="接收方联系人">{{ info.AcceptorContactID && getString(info.AcceptorContactID.Name) }}</el-descriptions-item>
          <!-- 接收方地址 -->
          <el-descriptions-item label="接收方地址">{{ info.AcceptAddress }}</el-descriptions-item>
          <!-- 结算方 -->
          <el-descriptions-item label="结算方">{{ info.SETTLEID && getString(info.SETTLEID.Name) }}</el-descriptions-item>
          <!-- 收款方 -->
          <el-descriptions-item label="收款方">{{ info.CHARGEID && getString(info.CHARGEID.Name) }}</el-descriptions-item>
          <!-- 邮箱 -->
          <el-descriptions-item label="邮箱">{{ info.FAcceptEMail }}</el-descriptions-item>
        </el-descriptions>
        <div class="customTitle">明细信息</div>
        <el-table ref="detailTable" :data="info.PUR_MRBENTRY" class="custom-table" highlight-current-row @current-change="handleCurrentChange" :setCurrentRow="setCurrentRow">
          <!-- 序号 -->
          <el-table-column type="index" label="序号" align="center"></el-table-column>
          <!-- 物料编码 -->
          <el-table-column label="物料编码" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.MATERIALID && scope.row.MATERIALID.Number }}</template>
          </el-table-column>
          <!-- 物料名称 -->
          <el-table-column label="物料名称" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.MATERIALID && getString(scope.row.MATERIALID.Name) }}</template>
          </el-table-column>
          <!-- 规格型号 -->
          <el-table-column label="规格型号" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.MATERIALID && getString(scope.row.MATERIALID.Specification) }}</template>
          </el-table-column>
          <!-- 库存单位 -->
          <el-table-column label="库存单位" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.FUnitID && getString(scope.row.FUnitID.Name) }}</template>
          </el-table-column>
          <!-- 实退数量 -->
          <el-table-column label="实退数量" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.RMREALQTY }}</template>
          </el-table-column>
          <!-- 补料数量 -->
          <el-table-column label="补料数量" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.REPLENISHQTY }}</template>
          </el-table-column>
          <!-- 扣款数量 -->
          <el-table-column label="扣款数量" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.KEAPAMTQTY }}</template>
          </el-table-column>
          <!-- 计价单位 -->
          <el-table-column label="计价单位" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.PRICEUNITID && getString(scope.row.PRICEUNITID.Name) }}</template>
          </el-table-column>
          <!-- 计价数量 -->
          <el-table-column label="计价数量" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.PRICEUNITQTY }}</template>
          </el-table-column>
          <!-- 仓库 -->
          <el-table-column label="仓库" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.STOCKID && getString(scope.row.STOCKID.Name) }}</template>
          </el-table-column>
          <!-- 仓位 -->
          <el-table-column label="仓位" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.FSTOCKLOCID && getString(scope.row.FSTOCKLOCID.Name) }}</template>
          </el-table-column>
          <!-- 库存状态 -->
          <el-table-column label="库存状态" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.StockStatusId && getString(scope.row.StockStatusId.Name) }}</template>
          </el-table-column>
          <!-- 备注 -->
          <el-table-column label="备注" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.NOTE }}</template>
          </el-table-column>
          <!-- 单价 -->
          <el-table-column label="单价" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.Price }}</template>
          </el-table-column>
          <!-- 金额 -->
          <el-table-column label="金额" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.Amount }}</template>
          </el-table-column>
          <!-- 是否赠品 -->
          <el-table-column label="是否赠品" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
              <el-checkbox v-model="scope.row.GiveAway" disabled></el-checkbox>
            </template>
          </el-table-column>
          <!-- 批号 -->
          <el-table-column label="批号" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.Lot && scope.row.Lot.Number }}</template>
          </el-table-column>
        </el-table>
        <el-descriptions border :column="4" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 4 - 7em - 22px)' }">
          <template slot="title">
            <div class="customTitle">明细财务信息</div>
          </template>
          <!-- 物料编码 -->
          <el-descriptions-item label="物料编码">{{ setCurrentRow && setCurrentRow.MATERIALID && setCurrentRow.MATERIALID.Number }}</el-descriptions-item>
          <!-- 物料名称 -->
          <el-descriptions-item label="物料名称">{{ setCurrentRow && setCurrentRow.MATERIALID && getString(setCurrentRow.MATERIALID.Name) }}</el-descriptions-item>
          <!-- 税率 -->
          <el-descriptions-item label="税率">{{ (setCurrentRow && setCurrentRow.TAXRATE) || '' }}</el-descriptions-item>
          <!-- 单价 -->
          <el-descriptions-item label="单价">{{ (setCurrentRow && setCurrentRow.Price) || '' }}</el-descriptions-item>
          <!-- 含税单价 -->
          <el-descriptions-item label="含税单价">{{ (setCurrentRow && setCurrentRow.TAXPRICE) || '' }}</el-descriptions-item>
          <!-- 折扣率(%) -->
          <el-descriptions-item label="折扣率(%)">{{ (setCurrentRow && setCurrentRow.DISCOUNTRATE) || '' }}</el-descriptions-item>
          <!-- 净价 -->
          <el-descriptions-item label="净价">{{ (setCurrentRow && setCurrentRow.TAXNETPRICE) || '' }}</el-descriptions-item>
          <!-- 折扣额 -->
          <el-descriptions-item label="折扣额">{{ (setCurrentRow && setCurrentRow.Discount) || '' }}</el-descriptions-item>
          <!-- 税额 -->
          <el-descriptions-item label="税额">{{ (setCurrentRow && setCurrentRow.TAXAMOUNT) || '' }}</el-descriptions-item>
          <!-- 单价折扣 -->
          <el-descriptions-item label="单价折扣">{{ (setCurrentRow && setCurrentRow.PriceDiscount) || '' }}</el-descriptions-item>
          <!-- 分录价目表 -->
          <el-descriptions-item label="分录价目表">{{ setCurrentRow && setCurrentRow.PRICELISTENTRY && getString(setCurrentRow.PRICELISTENTRY.Name) }}</el-descriptions-item>
          <!-- 金额 -->
          <el-descriptions-item label="金额">{{ (setCurrentRow && setCurrentRow.Amount) || '' }}</el-descriptions-item>
          <!-- 价税合计 -->
          <el-descriptions-item label="价税合计">{{ (setCurrentRow && setCurrentRow.ALLAMOUNT) || '' }}</el-descriptions-item>
          <!-- 税额（本位币） -->
          <el-descriptions-item label="税额（本位币）">{{ (setCurrentRow && setCurrentRow.TAXAMOUNT_LC) || '' }}</el-descriptions-item>
          <!-- 金额（本位币） -->
          <el-descriptions-item label="金额（本位币）">{{ (setCurrentRow && setCurrentRow.Amount_LC) || '' }}</el-descriptions-item>
          <!-- 价税合计（本位币） -->
          <el-descriptions-item label="价税合计（本位币）">{{ (setCurrentRow && setCurrentRow.ALLAMOUNT_LC) || '' }}</el-descriptions-item>
          <!-- 价格系数 -->
          <el-descriptions-item label="价格系数">{{ (setCurrentRow && setCurrentRow.PRICECOEFFICIENT) || '' }}</el-descriptions-item>
          <!-- 计价单位 -->
          <el-descriptions-item label="计价单位">{{ setCurrentRow && setCurrentRow.PRICEUNITID && getString(setCurrentRow.PRICEUNITID.Name) }}</el-descriptions-item>
          <!-- 计价数量 -->
          <el-descriptions-item label="计价数量">{{ (setCurrentRow && setCurrentRow.PRICEUNITQTY) || '' }}</el-descriptions-item>
          <!-- 系统定价 -->
          <el-descriptions-item label="系统定价">{{ (setCurrentRow && setCurrentRow.SYSPRICE) || '' }}</el-descriptions-item>
          <!-- 价格上限 -->
          <el-descriptions-item label="价格上限">{{ (setCurrentRow && setCurrentRow.UPPRICE) || '' }}</el-descriptions-item>
          <!-- 价格下限 -->
          <el-descriptions-item label="价格下限">{{ (setCurrentRow && setCurrentRow.DOWNPRICE) || '' }}</el-descriptions-item>
          <!-- 费用项目 -->
          <el-descriptions-item label="费用项目">{{ setCurrentRow && setCurrentRow.ChargeProjectID && getString(setCurrentRow.ChargeProjectID.Name) }}</el-descriptions-item>
          <!-- 已开票数量 -->
          <el-descriptions-item label="已开票数量">{{ (setCurrentRow && setCurrentRow.COSTAMOUNT) || '' }}</el-descriptions-item>
          <!-- 开票结束状态 -->
          <el-descriptions-item label="开票结束状态">{{ setCurrentRow && setCurrentRow.InvoicedStatus === 'A' ? '正常' : '已结束' }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
    <!-- 下推弹出框 -->
    <el-dialog v-dialogDragBox title="选择单据" :visible.sync="pushOpen" width="580px" class="custom-dialog">
      <div style="padding: 0 20px">
        <el-row :gutter="10" class="custom-push-target">
          <el-radio-group v-model="pushForm.target" v-removeAriaHidden>
            <el-col :span="12" v-for="item in pushTarget" :key="item.value">
              <el-radio :label="item.value">{{ item.label }}</el-radio>
            </el-col>
          </el-radio-group>
        </el-row>
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn small" @click="pushOpen = false">取消</el-button>
        <el-button class="custom-dialog-btn small primary" :class="{ disabled: !pushForm.target }" :disabled="!pushForm.target" @click="handlePushSubmit">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { getPurchaseReturnDetail, auditPurchaseReturn, revokePurchaseReturn, deletePurchaseReturn, pushDownPurchaseReturn, submitPurchaseReturn, unauditPurchaseReturn } from '@/api/kingdee/purchase/return'
import { kingdee } from '@/minix'

export default {
  mixins: [kingdee],
  data() {
    return {
      open: false,
      info: {},
      setCurrentRow: {}, // 选中的行
      // 业务类型
      BusinessTypeOptions: [
        { label: '标准采购', value: 'CG' },
        { label: '标准委外', value: 'WW' },
        { label: '资产采购', value: 'ZCCG' },
        { label: '费用采购', value: 'FYCG' },
        { label: '分销采购', value: 'DRPSALE' },
        { label: 'VMI采购', value: 'VMICG' },
        { label: '直运采购', value: 'ZYCG' }
      ],
      // 补料方式
      ReplenishTypeOptions: [
        { label: '按源单补料', value: 'A' },
        { label: '创建补料订单', value: 'B' },
        { label: '零散补料', value: 'C' }
      ],
      // 退料方式
      ReturnModeOptions: [
        { label: '退料补料', value: 'A' },
        { label: '退料并扣款', value: 'B' }
      ],
      // 退料类型
      ReturnTypeOptions: [
        { label: '检验退料', value: 'A' },
        { label: '库存退料', value: 'B' }
      ],
      // 下推
      pushOpen: false,
      pushTarget: [
        { value: 'PUR_Purchase0rder', label: '采购订单' },
        { value: 'SAL_RETURNSTOCK', label: '销售退货单' }
      ],
      pushForm: {
        number: undefined,
        target: undefined
      }
    }
  },
  created() {},
  methods: {
    // 获取详情
    getInfo(row = {}) {
      this.open = true
      if (!row.BillNo) {
        this.$message.error('参数错误，请重新选择')
        return
      }
      getPurchaseReturnDetail({ billNo: row.BillNo }).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          const { result } = data
          this.open = !!result?.result
          this.info = result?.result || {}
          this.$nextTick(() => {
            this.setCurrentRow = result?.result?.PUR_MRBENTRY?.[0] || {}
            if (this.$refs.detailTable) {
              this.$refs.detailTable.setCurrentRow(this.setCurrentRow)
              this.$refs.detailTable.bodyWrapper.scrollLeft = 0
            }
          })
        } else this.$message.error(msg)
      })
    },
    // 当前行
    handleCurrentChange(val) {
      this.setCurrentRow = val
    },
    // 关闭
    beforeClose() {
      this.handleClose()
      this.$emit('update', { billNo: this.info.BillNo, newStatus: this.info.DocumentStatus })
    },
    // 关闭
    handleClose(flag = false) {
      this.open = false
      this.$emit('callBack', flag)
    },
    // 金蝶采购退料单操作
    // prettier-ignore
    handleKingdeeDo(type) {
      const number = this.info.BillNo
      if (!number) return
      switch (type) {
        case 'submit':
          // 提交
          this.$modal.confirm('确认要提交该采购退料单吗？').then(() => {
            submitPurchaseReturn({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('提交成功')
                this.$set(this.info, 'DocumentStatus', 'B')
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'audit':
          // 审核
          this.$modal.confirm('确认要审核该采购退料单吗？').then(() => {
            auditPurchaseReturn({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('审核成功')
                this.$set(this.info, 'DocumentStatus', 'C')
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'revoke':
          // 撤销
          this.$modal.confirm('确认要撤销该采购退料单吗？').then(() => {
            revokePurchaseReturn({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('撤销成功')
                this.$set(this.info, 'DocumentStatus', 'D')
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'unAudit':
          // 反审核
          this.$modal.confirm('确认要反审核该采购退料单吗？').then(() => {
            unauditPurchaseReturn({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('反审核成功')
                this.$set(this.info, 'DocumentStatus', 'D')
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'delete':
          // 删除
          this.$modal.confirm('确认要删除该采购退料单吗？').then(() => {
            deletePurchaseReturn({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('删除成功')
                this.handleClose(true)
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'push':
          // 下推
          this.pushForm = {
            number: this.info.BillNo,
            target: undefined
          }
          this.pushOpen = true
          break
      }
    },
    // 下推提交
    handlePushSubmit() {
      const { number, target } = this.pushForm
      if (!number || !target) return
      pushDownPurchaseReturn({ number, target }).then(res => {
        const { code, msg } = res
        if (code === 200) {
          this.$message.success('下推成功')
          this.pushOpen = false
          this.handleClose(true)
        } else if (code === 400) {
          this.$alert(msg || '操作失败，请重试或使用金蝶进行操作！', '提示', {
            type: 'info',
            confirmButtonText: '确定',
            callback: action => {
              this.pushOpen = false
            }
          })
        } else this.$message.error(msg)
      })
    }
  }
}
</script>
<style scoped lang="scss">
@import '@/assets/styles/custom-new.scss';
::v-deep {
  .el-descriptions__header {
    margin-bottom: 0;
  }
  .el-table.custom-table .el-table__body-wrapper .el-table__row:hover td.el-table__cell {
    background-color: #eaf1fe !important;
  }
}
.customTitle {
  font-size: 16px;
  font-weight: normal;
  line-height: 60px;
  color: $font;
}
.kindeeButton {
  display: flex;
  align-items: center;
}
.custom-push-target {
  .el-col.el-col-12 {
    .el-radio {
      height: 30px;
      display: flex;
      align-items: center;
    }
  }
}
</style>
