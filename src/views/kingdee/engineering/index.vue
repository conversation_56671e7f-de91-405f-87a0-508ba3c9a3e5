<template>
  <div class="newBox" :class="{ 'vh-85': !isPopup, bgcf9: !isPopup }">
    <!-- 搜索 -->
    <div class="custom-search flex" style="padding-top: 18px" :style="{ justifyContent: 'space-between' }">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" @submit.native.prevent>
        <el-form-item label="物料名称" prop="name">
          <el-input v-model="queryParams.name" placeholder="请输入物料名称" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="物料编码" prop="number">
          <el-input v-model="queryParams.number" placeholder="请输入物料编码" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="small" @click="handleResetQuery">重置</el-button>
          <el-button type="primary" icon="el-icon-plus" size="small" @click="handleCreate" v-if="!isPopup">新增工程项目</el-button>
        </el-form-item>
      </el-form>
    </div>
    <!-- 列表 -->
    <div style="padding: 15px 20px">
      <el-table v-loading="loading" ref="allTable" border :data="list" style="width: 100%" class="custom-table">
        <!-- 编码 -->
        <el-table-column label="编码" prop="Number" align="center" show-overflow-tooltip>
          <template slot-scope="scope">
            <span class="table-link" @click="handleUpdate(scope.row)">{{ scope.row.Number }}</span>
          </template>
        </el-table-column>
        <!-- 名称 -->
        <el-table-column label="名称" prop="DataValue" align="center" show-overflow-tooltip></el-table-column>
        <!-- 数据状态 -->
        <el-table-column label="数据状态" prop="DocumentStatus " align="center" show-overflow-tooltip>
          <template slot-scope="{ row }">{{ getDocumentStatusLabel(row.DocumentStatus) }}</template>
        </el-table-column>
        <!-- 备注 -->
        <el-table-column label="备注" prop="Description" align="center" show-overflow-tooltip></el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="custom-pagination">
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.limit" @pagination="getList" />
      </div>
    </div>
    <!-- 新增/编辑 -->
    <EngineeringCreate ref="create" @callback="handleCallBack" @update="handleCallUpdate" v-if="showCreate" />
  </div>
</template>
<script>
import { kingdee } from '@/minix'
import { getEngineeringList } from '@/api/kingdee/engineering'
import EngineeringCreate from './create'

export default {
  name: 'Engineering',
  mixins: [kingdee],
  components: {
    EngineeringCreate
  },
  props: {
    isPopup: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // 搜索
      queryParams: {
        pageNum: 1, // 当前页码
        startRow: 0,
        limit: 10,
        fid: '68464b0a545fe7',
        number: undefined,
        name: undefined
      },
      // 列表
      list: [],
      total: 0,
      loading: false,
      showCreate: false
    }
  },
  created() {
    // 获取列表
    if (!this.isPopup) this.getList()
  },
  methods: {
    // 列表
    // prettier-ignore
    getList() {
      this.loading = true
      this.queryParams.startRow = Math.max(0, (this.queryParams.pageNum - 1) * this.queryParams.limit)
      const query = { ...this.queryParams }
      delete query.pageNum
      getEngineeringList(query).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          const { data: list, total } = data
          this.list = list
          this.total = total
        } else this.$message.error(msg)
      }).finally(() => {
        this.loading = false
      })
    },
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 重置搜索
    handleResetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 新增
    handleCreate() {
      this.showCreate = true
      this.$nextTick(() => {
        this.$refs.create.handleCreate('GCXM')
      })
    },
    // 修改
    handleUpdate(row) {
      this.showCreate = true
      this.$nextTick(() => {
        this.$refs.create.handleUpdate(row)
      })
    },
    // 新增/编辑回调
    handleCallBack(flag = false) {
      this.showCreate = false
      if (flag) this.getList()
    },
    // 更新
    handleCallUpdate(data) {
      const { billNo, newStatus } = data
      const index = this.list.findIndex(item => item.BillNo === billNo)
      if (index !== -1) this.list[index].DocumentStatus = newStatus
    }
  }
}
</script>
<style scoped lang="scss">
@import '@/assets/styles/custom-new.scss';
</style>
