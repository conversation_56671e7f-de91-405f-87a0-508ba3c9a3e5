<template>
  <div>
    <el-dialog :title="title" :visible.sync="open" width="800px" :before-close="beforeClose">
      <div style="padding: 0 20px">
        <div class="kindeeButton" :key="DocumentStatus">
          <template v-for="(btn, index) in buttons">
            <el-button :key="index" :type="btn.type" size="medium" v-if="checkStatus(btn.status)" @click="handleKingdeeDo(btn.action)">{{ btn.text }}</el-button>
            <el-button :key="'disabled-' + index" :type="btn.type" size="medium" disabled v-else>{{ btn.text }}</el-button>
          </template>
          <el-button type="danger" @click="handleEnable" size="medium" v-if="ForbidStatus === 'A'">禁用</el-button>
          <el-button type="success" @click="handleForbid" size="medium" v-if="ForbidStatus !== 'A'">反禁用</el-button>
          <el-button icon="el-icon-edit" @click="handleDoEdit" size="medium" v-if="(DocumentStatus === 'A' || DocumentStatus === 'D') && ForbidStatus === 'A'">修改</el-button>
        </div>
        <el-form :model="form" ref="form" :rules="rules" label-width="4em" :disabled="!isEdit">
          <!-- 编码 -->
          <el-form-item label="编码" prop="fnumber">
            <el-input v-model="form.fnumber" placeholder="请输入编码" />
          </el-form-item>
          <!-- 名称 -->
          <el-form-item label="名称" prop="fdatavalue">
            <el-input v-model="form.fdatavalue" placeholder="请输入名称" />
          </el-form-item>
          <!-- 顺序 -->
          <el-form-item label="顺序" prop="fseq">
            <el-input-number v-model="form.fseq" placeholder="请输入顺序" size="small" :precision="0" :min="0" :max="999999" />
          </el-form-item>
          <!-- 备注 -->
          <el-form-item label="备注" prop="fdescription">
            <el-input v-model="form.fdescription" type="textarea" :autosize="{ minRows: 3, maxRows: 5 }" resize="none" placeholder="请输入备注" />
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer">
        <template v-if="isEdit">
          <el-button class="custom-dialog-btn" @click="handleCancel()">取 消</el-button>
          <el-button class="custom-dialog-btn primary" :loading="loading" @click="handleSubmit">确 定</el-button>
        </template>
        <template v-else>
          <el-button class="custom-dialog-btn primary" @click="handleCancel()">关 闭</el-button>
        </template>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { saveEngineering, getEngineeringDetail2, auditEngineering, cancelEngineering, deleteEngineering, enableEngineering, forbidEngineering, submitEngineering, unAuditEngineering } from '@/api/kingdee/engineering'

export default {
  name: 'EngineeringCreate',
  data() {
    return {
      title: '工程项目新增',
      open: false,
      loading: false,
      form: {},
      rules: {
        fnumber: [{ required: true, message: '请输入编码', trigger: 'blur' }],
        fdatavalue: [{ required: true, message: '请输入名称', trigger: 'blur' }]
      },
      ForbidStatus: undefined, // 禁用状态
      DocumentStatus: undefined, // 单据状态
      buttons: [
        { text: '提交', type: 'primary', action: 'submit', status: ['A', 'D'] },
        { text: '审核', type: 'warning', action: 'audit', status: ['B'] },
        { text: '撤销', type: 'info', action: 'revoke', status: ['B'] },
        { text: '删除', type: 'danger', action: 'delete', status: ['A', 'D'] },
        { text: '反审', type: 'warning', action: 'unAudit', status: ['C'] }
      ],
      isEdit: false
    }
  },
  methods: {
    checkStatus(allowedStatus) {
      return allowedStatus.includes(this.DocumentStatus)
    },
    reset() {
      this.form = {
        fcreateorgid: undefined, // 创建组织
        fdatavalue: undefined, // 名称
        fdescription: undefined, // 备注
        fentryid: undefined, // 实体主键
        fid: undefined, // 类别
        fnumber: undefined, // 编码
        fparentid: undefined, // 上级资料
        fseq: 0, // 显示顺序
        fuseorgid: undefined // 使用组织
      }
      this.resetForm('form')
    },
    handleCreate(fid = undefined) {
      this.reset()
      this.form.fid = fid
      this.title = '工程项目新增'
      this.open = true
      this.isEdit = false
    },
    handleUpdate(row) {
      const { EntryID } = row
      if (!EntryID) return this.$message.warning('参数错误，请刷新页面重试')
      this.reset()
      getEngineeringDetail2({ fid: EntryID }).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          const { result } = data
          const info = result?.result || {}
          this.form = {
            fdatavalue: info?.DataValue?.[0]?.Value || undefined, // 名称
            fdescription: info?.Description?.[0]?.Value || undefined, // 备注
            fentryid: info?.Id, // 实体主键
            fid: info?.FId?.Number || undefined, // 类别
            fnumber: info?.Number, // 编码
            fseq: info?.Seq || 0 // 显示顺序
          }
          this.ForbidStatus = info?.ForbidStatus || undefined
          this.DocumentStatus = info?.DocumentStatus || undefined
          this.title = '工程项目修改'
          this.open = true
          this.isEdit = false
        } else this.$message.error(msg)
      })
    },
    handleDoEdit() {
      this.isEdit = true
    },
    beforeClose() {
      this.handleCancel()
      this.$emit('update', { billNo: this.form.fentryid, newStatus: this.DocumentStatus })
    },
    handleCancel(flag = false) {
      this.open = false
      this.$emit('callback', flag)
    },
    // prettier-ignore
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true
          saveEngineering(this.form).then(res => {
            const { code, msg } = res
            if (code === 200) {
              this.$message.success(this.form.fentryid ? '修改成功' : '新增成功')
              this.handleCancel(true)
            } else this.$message.error(msg)
          }).finally(() => {
            this.loading = false
          })
        }
      })
    },
    // 金蝶操作
    // prettier-ignore
    handleKingdeeDo(type) {
      const id = this.form.fentryid
      if (!id) return
      switch (type) {
        case 'submit':
          // 提交
          this.$modal.confirm('确认要提交该工程项目吗？').then(() => {
            submitEngineering({ id }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('提交成功')
                this.$set(this, 'DocumentStatus', 'B')
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'audit':
          // 审核
          this.$modal.confirm('确认要审核该工程项目吗？').then(() => {
            auditEngineering({ id }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('审核成功')
                this.$set(this, 'DocumentStatus', 'C')
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'revoke':
          // 撤销
          this.$modal.confirm('确认要撤销该工程项目吗？').then(() => {
            cancelEngineering({ id }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('撤销成功')
                this.$set(this, 'DocumentStatus', 'D')
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'unAudit':
          // 反审核
          this.$modal.confirm('确认要反审核该工程项目吗？').then(() => {
            unAuditEngineering({ id }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('反审核成功')
                this.$set(this, 'DocumentStatus', 'D')
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'delete':
          // 删除
          this.$modal.confirm('确认要删除该工程项目吗？').then(() => {
            deleteEngineering({ id }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('删除成功')
                this.handleCancel(true)
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
      }
    },
    handleEnable() {
      this.$modal.confirm('确认要禁用该工程项目吗？').then(() => {
        forbidEngineering({ id: this.form.fentryid }).then(res => {
          const { code, msg } = res
          if (code === 200) {
            this.$message.success('禁用成功')
            this.$set(this, 'ForbidStatus', 'D')
          } else this.$message.error(msg)
        })
      }).catch(() => {})
    },
    handleForbid() {
      this.$modal.confirm('确认要反禁用该工程项目吗？').then(() => {
        enableEngineering({ id: this.form.fentryid }).then(res => {
          const { code, msg } = res
          if (code === 200) {
            this.$message.success('反禁用成功')
            this.$set(this, 'ForbidStatus', 'A')
          } else this.$message.error(msg)
        })
      }).catch(() => {})
    }
  }
}
</script>
<style scoped lang="scss">
@import '@/assets/styles/custom-new.scss';
.kindeeButton {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}
</style>
