<template>
  <div>
    <el-dialog v-dialogDragBox title="退货通知单详情" :visible.sync="open" width="90%" class="custom-dialog" :before-close="beforeClose">
      <div style="padding: 0 20px">
        <div class="kindeeButton" :key="info.DocumentStatus">
          <template v-for="(btn, index) in buttons">
            <el-button :key="index" :type="btn.type" size="medium" v-if="checkStatus(btn.status)" @click="handleKingdeeDo(btn.action)">{{ btn.text }}</el-button>
            <el-button :key="'disabled-' + index" :type="btn.type" size="medium" disabled v-else>{{ btn.text }}</el-button>
          </template>
        </div>
        <el-descriptions border :column="4" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 4 - 7em - 22px)' }">
          <template slot="title">
            <div class="customTitle">基本信息</div>
          </template>
          <!-- 单据类型 -->
          <el-descriptions-item label="单据类型">{{ info.BillTypeID && getString(info.BillTypeID.Name, 1) }}</el-descriptions-item>
          <!-- 单据编号 -->
          <el-descriptions-item label="单据编号">{{ info.BillNo }}</el-descriptions-item>
          <!-- 日期 -->
          <el-descriptions-item label="日期">{{ info.Date && parseTime(info.Date, '{y}-{m}-{d}') }}</el-descriptions-item>
          <!-- 结算币别 -->
          <el-descriptions-item label="结算币别">{{ info.SAL_RETURNNOTICEFIN && info.SAL_RETURNNOTICEFIN[0] && info.SAL_RETURNNOTICEFIN[0].SettleCurrId && getString(info.SAL_RETURNNOTICEFIN[0].SettleCurrId.Name) }}</el-descriptions-item>
          <!-- 单据状态 -->
          <el-descriptions-item label="单据状态">{{ getDocumentStatusLabel(info.DocumentStatus) }}</el-descriptions-item>
          <!-- 退货客户 -->
          <el-descriptions-item label="退货客户">{{ info.RetcustId && getString(info.RetcustId.Name) }}</el-descriptions-item>
          <!-- 退货原因 -->
          <el-descriptions-item label="退货原因">{{ info.ReturnReason }}</el-descriptions-item>
          <!-- 交货地点 -->
          <el-descriptions-item label="交货地点">{{ info.HeadLocId && getString(info.HeadLocId.Name) }}</el-descriptions-item>
          <!-- 订单类型 -->
          <el-descriptions-item label="订单类型">{{ info.F_SCMJ_DDLX && getOptionLabel(F_SCMJ_DDLXOptions, info.F_SCMJ_DDLX) }}</el-descriptions-item>
          <!-- 库存组织 -->
          <el-descriptions-item label="库存组织">{{ info.RetorgId && getString(info.RetorgId.Name) }}</el-descriptions-item>
          <!-- 库存部门 -->
          <el-descriptions-item label="库存部门">{{ info.RetDeptId && getString(info.RetDeptId.Name) }}</el-descriptions-item>
          <!-- 仓管员 -->
          <el-descriptions-item label="仓管员">{{ info.StockerId && getString(info.StockerId.Name) }}</el-descriptions-item>
          <!-- 备注 -->
          <el-descriptions-item label="备注">{{ info.Description }}</el-descriptions-item>
          <!-- 销售组织 -->
          <el-descriptions-item label="销售组织">{{ info.SaleOrgId && getString(info.SaleOrgId.Name) }}</el-descriptions-item>
          <!-- 销售部门 -->
          <el-descriptions-item label="销售部门">{{ info.Sledeptid && getString(info.Sledeptid.Name) }}</el-descriptions-item>
          <!-- 销售员 -->
          <el-descriptions-item label="销售员">{{ info.SalesManId && getString(info.SalesManId.Name) }}</el-descriptions-item>
          <!-- 销售订单号 -->
          <el-descriptions-item label="销售订单号">{{ info.F_SCMJ_XSDDH }}</el-descriptions-item>
        </el-descriptions>
        <div class="customTitle">明细信息</div>
        <el-table ref="detailTable" :data="info.SAL_RETURNNOTICEENTRY" class="custom-table" highlight-current-row @current-change="handleCurrentChange" :setCurrentRow="setCurrentRow">
          <!-- 序号 -->
          <el-table-column type="index" label="序号" align="center"></el-table-column>
          <!-- 物料编码 -->
          <el-table-column label="物料编码" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.MaterialId && scope.row.MaterialId.Number }}</template>
          </el-table-column>
          <!-- 物料名称 -->
          <el-table-column label="物料名称" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.MaterialId && getString(scope.row.MaterialId.Name) }}</template>
          </el-table-column>
          <!-- 规格型号 -->
          <el-table-column prop="Specification" label="规格型号" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.MaterialId && scope.row.MaterialId.Specification && getString(scope.row.MaterialId.Specification) }}</template>
          </el-table-column>
          <!-- 销售单位 -->
          <el-table-column label="销售单位" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.UnitID && getString(scope.row.UnitID.Name) }}</template>
          </el-table-column>
          <!-- 销售数量 -->
          <el-table-column prop="Qty" label="销售数量" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.Qty || '' }}</template>
          </el-table-column>
          <!-- 是否赠品 -->
          <el-table-column prop="IsPresent" label="是否赠品" align="center">
            <template slot-scope="scope">
              <el-checkbox v-model="scope.row.IsFree" disabled></el-checkbox>
            </template>
          </el-table-column>
          <!-- 退货日期 -->
          <el-table-column prop="ReturnDate" label="退货日期" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.Deliverydate && parseTime(scope.row.Deliverydate, '{y}-{m}-{d}') }}</template>
          </el-table-column>
          <!-- 仓库 -->
          <el-table-column label="仓库" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.StockId && getString(scope.row.StockId.Name) }}</template>
          </el-table-column>
          <!-- 备注 -->
          <el-table-column prop="Description" label="备注" align="center" show-overflow-tooltip></el-table-column>
          <!-- 退货类型 -->
          <el-table-column prop="RmType" label="退货类型" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.RmType && getString(scope.row.RmType.FDataValue, 1) }}</template>
          </el-table-column>
          <!-- 结算单价 -->
          <el-table-column prop="F_SCMJ_JSPrice" label="结算单价" align="center" show-overflow-tooltip></el-table-column>
          <!-- 结算金额 -->
          <el-table-column prop="F_SCMJ_JSAmount" label="结算金额" align="center" show-overflow-tooltip></el-table-column>
        </el-table>
        <el-descriptions border :column="3" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 3 - 7em - 22px)' }">
          <template slot="title">
            <div class="customTitle">物料数据</div>
          </template>
          <!-- 物料编码 -->
          <el-descriptions-item label="物料编码">{{ setCurrentRow && setCurrentRow.MaterialId && setCurrentRow.MaterialId.Number }}</el-descriptions-item>
          <!-- 物料名称 -->
          <el-descriptions-item label="物料名称">{{ setCurrentRow && setCurrentRow.MaterialId && getString(setCurrentRow.MaterialId.Name) }}</el-descriptions-item>
          <!-- 销售单位 -->
          <el-descriptions-item label="销售单位">{{ setCurrentRow && setCurrentRow.UnitID && getString(setCurrentRow.UnitID.Name) }}</el-descriptions-item>
          <!-- 销售数量 -->
          <el-descriptions-item label="销售数量">{{ setCurrentRow && setCurrentRow.Qty }}</el-descriptions-item>
          <!-- 库存单位 -->
          <el-descriptions-item label="库存单位">{{ setCurrentRow && setCurrentRow.StockUnitID && getString(setCurrentRow.StockUnitID.Name) }}</el-descriptions-item>
          <!-- 库存数量 -->
          <el-descriptions-item label="库存数量">{{ setCurrentRow && setCurrentRow.StockQty }}</el-descriptions-item>
          <!-- BOM版本 -->
          <el-descriptions-item label="BOM版本">{{ setCurrentRow && setCurrentRow.BOMId && getString(setCurrentRow.BOMId.Name) }}</el-descriptions-item>
          <!-- 辅助属性 -->
          <el-descriptions-item label="辅助属性">{{ setCurrentRow && setCurrentRow.AuxpropId && getString(setCurrentRow.AuxpropId.Name) }}</el-descriptions-item>
          <!-- 物料类别 -->
          <el-descriptions-item label="物料类别">{{ setCurrentRow && setCurrentRow.MaterialId && setCurrentRow.MaterialId.MaterialBase && setCurrentRow.MaterialId.MaterialBase[0] && setCurrentRow.MaterialId.MaterialBase[0].CategoryID && getString(setCurrentRow.MaterialId.MaterialBase[0].CategoryID.Name) }}</el-descriptions-item>
          <!-- 货主类型 -->
          <el-descriptions-item label="货主类型">{{ setCurrentRow && setCurrentRow.OwnerTypeID && getOptionLabel(OwnerTypeIDOptions, setCurrentRow.OwnerTypeID) }}</el-descriptions-item>
          <!-- 货主 -->
          <el-descriptions-item label="货主">{{ setCurrentRow && setCurrentRow.OwnerID && getString(setCurrentRow.OwnerID.Name) }}</el-descriptions-item>
          <!-- 退货检验 -->
          <el-descriptions-item label="退货检验">
            <el-checkbox v-model="IsReturnCheck" disabled></el-checkbox>
          </el-descriptions-item>
          <!-- 批号 -->
          <el-descriptions-item label="批号">{{ setCurrentRow && setCurrentRow.Lot && setCurrentRow.Lot.Number }}</el-descriptions-item>
          <!-- 生产日期 -->
          <el-descriptions-item label="生产日期">{{ setCurrentRow && setCurrentRow.ProduceDate && parseTime(setCurrentRow.ProduceDate, '{y}-{m}-{d}') }}</el-descriptions-item>
          <!-- 保质期单位 -->
          <el-descriptions-item label="保质期单位">{{ setCurrentRow && setCurrentRow.MaterialId && setCurrentRow.MaterialId.MaterialStock && setCurrentRow.MaterialId.MaterialStock[0] && setCurrentRow.MaterialId.MaterialStock[0].ExpUnit }}</el-descriptions-item>
          <!-- 保质期 -->
          <el-descriptions-item label="保质期">{{ setCurrentRow && setCurrentRow.MaterialId && setCurrentRow.MaterialId.MaterialStock && setCurrentRow.MaterialId.MaterialStock[0] && setCurrentRow.MaterialId.MaterialStock[0].ExpPeriod }}</el-descriptions-item>
          <!-- 有效期至 -->
          <el-descriptions-item label="有效期至">{{ setCurrentRow && setCurrentRow.ExpiryDate && parseTime(setCurrentRow.ExpiryDate, '{y}-{m}-{d}') }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
    <!-- 下推弹出框 -->
    <el-dialog v-dialogDragBox title="选择单据" :visible.sync="pushOpen" width="580px" class="custom-dialog">
      <div style="padding: 0 20px">
        <el-row :gutter="10" class="custom-push-target">
          <el-radio-group v-model="pushForm.target" v-removeAriaHidden>
            <el-col :span="12" v-for="item in pushTarget" :key="item.value">
              <el-radio :label="item.value">{{ item.label }}</el-radio>
            </el-col>
          </el-radio-group>
        </el-row>
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn small" @click="pushOpen = false">取消</el-button>
        <el-button class="custom-dialog-btn small primary" :class="{ disabled: !pushForm.target }" :disabled="!pushForm.target" @click="handlePushSubmit">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { getReturnNoticeDetail, auditReturnNotice, revokeReturnNotice, deleteReturnNotice, pushReturnNotice, submitReturnNotice, unauditReturnNotice } from '@/api/kingdee/sell/returnNotice'
import { kingdee } from '@/minix'

export default {
  mixins: [kingdee],
  data() {
    return {
      open: false,
      info: {},
      setCurrentRow: {},
      // 订单类型
      F_SCMJ_DDLXOptions: [
        { label: '标准销售订单', value: '0' },
        { label: '工程项目订单', value: '1' }
      ],
      // 货主类型
      OwnerTypeIDOptions: [
        { label: '业务组织', value: 'BD_OwnerOrg' },
        { label: '供应商', value: 'BD_Supplier' },
        { label: '客户', value: 'BD_Customer' }
      ],
      // 下推
      pushOpen: false,
      pushTarget: [
        { value: 'STK_MisDelivery', label: '其他出库单' },
        { value: 'SAL_RETURNSTOCK', label: '销售退货单' }
      ],
      pushForm: {
        number: undefined,
        target: undefined
      },
      buttons: [
        { text: '提交', type: 'primary', action: 'submit', status: ['A', 'D'] },
        { text: '审核', type: 'warning', action: 'audit', status: ['B'] },
        { text: '撤销', type: 'info', action: 'revoke', status: ['B'] },
        { text: '删除', type: 'danger', action: 'delete', status: ['A', 'D'] },
        { text: '反审', type: 'warning', action: 'unAudit', status: ['C'] },
        { text: '下推', type: 'success', action: 'push', status: ['C'] }
      ]
    }
  },
  computed: {
    // 是否发货检验
    IsReturnCheck() {
      return this.setCurrentRow?.IsReturnCheck || false
    }
  },
  methods: {
    // 检查状态
    checkStatus(allowedStatus) {
      return allowedStatus.includes(this.info.DocumentStatus)
    },
    // 获取详情
    getInfo(row = {}) {
      this.open = true
      if (!row.BillNo) {
        this.$message.error('参数错误，请重新选择')
        return
      }
      getReturnNoticeDetail({ billNo: row.BillNo }).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          const { result } = data
          this.open = !!result?.result
          this.info = result?.result || {}
          this.$nextTick(() => {
            this.setCurrentRow = result?.result?.SAL_RETURNNOTICEENTRY?.[0] || {}
            if (this.$refs.detailTable) {
              this.$refs.detailTable.setCurrentRow(this.setCurrentRow)
              this.$refs.detailTable.bodyWrapper.scrollLeft = 0
            }
          })
        } else this.$message.error(msg)
      })
    },
    // 当前行
    handleCurrentChange(val) {
      this.setCurrentRow = val
    },
    // 关闭前
    beforeClose() {
      this.handleClose()
      this.$emit('update', { billNo: this.info.BillNo, newStatus: this.info.DocumentStatus })
    },
    // 关闭
    handleClose(flag = false) {
      this.open = false
      this.$emit('callBack', flag)
    },
    // 金蝶操作
    // prettier-ignore
    handleKingdeeDo(type) {
      const number = this.info.BillNo
      if (!number) return
      switch (type) {
        case 'submit':
          // 提交
          this.$modal.confirm('确认要提交该退货通知单吗？').then(() => {
            submitReturnNotice({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('提交成功')
                this.$set(this.info, 'DocumentStatus', 'B')
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'audit':
          // 审核
          this.$modal.confirm('确认要审核该退货通知单吗？').then(() => {
            auditReturnNotice({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('审核成功')
                this.$set(this.info, 'DocumentStatus', 'C')
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'revoke':
          // 撤销
          this.$modal.confirm('确认要撤销该退货通知单吗？').then(() => {
            revokeReturnNotice({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('撤销成功')
                this.$set(this.info, 'DocumentStatus', 'D')
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'unAudit':
          // 反审核
          this.$modal.confirm('确认要反审核该退货通知单吗？').then(() => {
            unauditReturnNotice({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('反审核成功')
                this.$set(this.info, 'DocumentStatus', 'D')
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'delete':
          // 删除
          this.$modal.confirm('确认要删除该退货通知单吗？').then(() => {
            deleteReturnNotice({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('删除成功')
                this.handleClose(true)
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'push':
          // 下推
          this.pushForm = {
            number: this.info.BillNo,
            target: undefined
          }
          this.pushOpen = true
          break
      }
    },
    // 下推提交
    handlePushSubmit() {
      const { number, target } = this.pushForm
      if (!number || !target) return
      pushReturnNotice({ number, target }).then(res => {
        const { code, msg } = res
        if (code === 200) {
          this.$message.success('下推成功')
          this.pushOpen = false
          this.handleClose(true)
        } else if (code === 400) {
          this.$alert(msg || '操作失败，请重试或使用金蝶进行操作！', '提示', {
            type: 'info',
            confirmButtonText: '确定',
            callback: action => {
              this.pushOpen = false
            }
          })
        } else this.$message.error(msg)
      })
    }
  }
}
</script>
<style scoped lang="scss">
@import '@/assets/styles/custom-new.scss';
::v-deep {
  .el-descriptions__header {
    margin-bottom: 0;
  }
  .el-table.custom-table .el-table__body-wrapper .el-table__row:hover td.el-table__cell {
    background-color: #eaf1fe !important;
  }
}
.customTitle {
  font-size: 16px;
  font-weight: normal;
  line-height: 60px;
  color: $font;
}
.kindeeButton {
  display: flex;
  align-items: center;
}
.custom-push-target {
  .el-col.el-col-12 {
    .el-radio {
      height: 30px;
      display: flex;
      align-items: center;
    }
  }
}
</style>
