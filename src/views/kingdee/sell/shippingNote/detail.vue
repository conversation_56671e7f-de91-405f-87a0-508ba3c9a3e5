<template>
  <div>
    <el-dialog v-dialogDragBox title="发货通知单详情" :visible.sync="open" width="90%" class="custom-dialog" :before-close="beforeClose" append-to-body>
      <div style="padding: 0 20px">
        <div class="kindeeButton" :key="info.DocumentStatus">
          <template v-for="(btn, index) in buttons">
            <el-button :key="index" :type="btn.type" size="medium" v-if="checkStatus(btn.status)" @click="handleKingdeeDo(btn.action)">{{ btn.text }}</el-button>
            <el-button :key="'disabled-' + index" :type="btn.type" size="medium" disabled v-else>{{ btn.text }}</el-button>
          </template>
        </div>
        <el-descriptions border :column="4" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 4 - 7em - 22px)' }">
          <template slot="title">
            <div class="customTitle">基本信息</div>
          </template>
          <!-- 单据类型 -->
          <el-descriptions-item label="单据类型">{{ info.BillTypeID && getString(info.BillTypeID.Name) }}</el-descriptions-item>
          <!-- 单据编号 -->
          <el-descriptions-item label="单据编号">{{ info.BillNo }}</el-descriptions-item>
          <!-- 日期 -->
          <el-descriptions-item label="日期">{{ info.Date && parseTime(info.Date, '{y}-{m}-{d}') }}</el-descriptions-item>
          <!-- 结算币别 -->
          <el-descriptions-item label="结算币别">{{ info.SAL_DELIVERYNOTICEFIN && info.SAL_DELIVERYNOTICEFIN[0] && info.SAL_DELIVERYNOTICEFIN[0].SettleCurrID && getString(info.SAL_DELIVERYNOTICEFIN[0].SettleCurrID.Name) }}</el-descriptions-item>
          <!-- 单据状态 -->
          <el-descriptions-item label="单据状态">{{ getDocumentStatusLabel(info.DocumentStatus) }}</el-descriptions-item>
          <!-- 客户 -->
          <el-descriptions-item label="客户">{{ info.CustomerID && getString(info.CustomerID.Name) }}</el-descriptions-item>
          <!-- 交货方式 -->
          <el-descriptions-item label="交货方式">{{ info.HeadDeliveryWay && getString(info.HeadDeliveryWay.Name) }}</el-descriptions-item>
          <!-- 交货地点 -->
          <el-descriptions-item label="交货地点">{{ info.HeadLocId && getString(info.HeadLocId.Name) }}</el-descriptions-item>
          <!-- 承运商 -->
          <el-descriptions-item label="承运商">{{ info.CarrierID && getString(info.CarrierID.Name) }}</el-descriptions-item>
          <!-- 运输单号 -->
          <el-descriptions-item label="运输单号">{{ info.CarriageNO }}</el-descriptions-item>
          <!-- 发货组织 -->
          <el-descriptions-item label="发货组织">{{ info.DeliveryOrgID && getString(info.DeliveryOrgID.Name) }}</el-descriptions-item>
          <!-- 发货部门 -->
          <el-descriptions-item label="发货部门">{{ info.DeliveryDeptID && getString(info.DeliveryDeptID.Name) }}</el-descriptions-item>
          <!-- 仓管员 -->
          <el-descriptions-item label="仓管员">{{ info.StockerID && getString(info.StockerID.Name) }}</el-descriptions-item>
          <!-- 备注 -->
          <el-descriptions-item label="备注">{{ info.Note }}</el-descriptions-item>
          <!-- 跟单员 -->
          <el-descriptions-item label="跟单员">{{ info.F_SCMJ_GDY && getString(info.F_SCMJ_GDY.Name) }}</el-descriptions-item>
          <!-- 销售组织 -->
          <el-descriptions-item label="销售组织">{{ info.SaleOrgId && getString(info.SaleOrgId.Name) }}</el-descriptions-item>
          <!-- 销售部门 -->
          <el-descriptions-item label="销售部门">{{ info.SaleDeptID && getString(info.SaleDeptID.Name) }}</el-descriptions-item>
          <!-- 销售员 -->
          <el-descriptions-item label="销售员">{{ info.SalesManID && getString(info.SalesManID.Name) }}</el-descriptions-item>
          <!-- 销售订单号 -->
          <el-descriptions-item label="销售订单号">{{ info.F_SCMJ_XSDDH }}</el-descriptions-item>
        </el-descriptions>
        <div class="customTitle">明细信息</div>
        <el-table ref="detailTable" :data="info.SAL_DELIVERYNOTICEENTRY" class="custom-table" highlight-current-row @current-change="handleCurrentChange" :setCurrentRow="setCurrentRow">
          <!-- 序号 -->
          <el-table-column type="index" label="序号" align="center"></el-table-column>
          <!-- 物料编码 -->
          <el-table-column label="物料编码" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.MaterialID && scope.row.MaterialID.Number }}</template>
          </el-table-column>
          <!-- 物料名称 -->
          <el-table-column label="物料名称" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.MaterialID && getString(scope.row.MaterialID.Name) }}</template>
          </el-table-column>
          <!-- 规格型号 -->
          <el-table-column prop="Specification" label="规格型号" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.MaterialID && scope.row.MaterialID.Specification && getString(scope.row.MaterialID.Specification) }}</template>
          </el-table-column>
          <!-- 销售单位 -->
          <el-table-column label="销售单位" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.UnitID && getString(scope.row.UnitID.Name) }}</template>
          </el-table-column>
          <!-- 销售数量 -->
          <el-table-column prop="Qty" label="销售数量" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.Qty || '' }}</template>
          </el-table-column>
          <!-- 是否赠品 -->
          <el-table-column prop="IsPresent" label="是否赠品" align="center">
            <template slot-scope="scope">
              <el-checkbox v-model="scope.row.IsFree" disabled></el-checkbox>
            </template>
          </el-table-column>
          <!-- 要货日期 -->
          <el-table-column prop="DeliveryDate" label="要货日期" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ parseTime(scope.row.DeliveryDate) }}</template>
          </el-table-column>
          <!-- 出货仓库 -->
          <el-table-column label="出货仓库" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.StockID && getString(scope.row.StockID.Name) }}</template>
          </el-table-column>
          <!-- 出货仓位 -->
          <el-table-column label="出货仓位" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.StockLocID && getString(scope.row.StockLocID.Name) }}</template>
          </el-table-column>
          <!-- 备注 -->
          <el-table-column prop="NoteEntry" label="备注" align="center" show-overflow-tooltip></el-table-column>
          <!-- 结算单价 -->
          <el-table-column prop="Price" label="结算单价" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.F_SCMJ_JSPrice || '' }}</template>
          </el-table-column>
          <!-- 结算金额 -->
          <el-table-column prop="Amount" label="结算金额" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.F_SCMJ_JSAmount || '' }}</template>
          </el-table-column>
        </el-table>
        <el-descriptions border :column="3" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 3 - 7em - 22px)' }">
          <template slot="title">
            <div class="customTitle">物料数据</div>
          </template>
          <!-- 物料编码 -->
          <el-descriptions-item label="物料编码">{{ setCurrentRow && setCurrentRow.MaterialID && setCurrentRow.MaterialID.Number }}</el-descriptions-item>
          <!-- 物料名称 -->
          <el-descriptions-item label="物料名称">{{ setCurrentRow && setCurrentRow.MaterialID && getString(setCurrentRow.MaterialID.Name) }}</el-descriptions-item>
          <!-- BOM版本 -->
          <el-descriptions-item label="BOM版本">{{ setCurrentRow && setCurrentRow.BomID && getString(setCurrentRow.BomID.Name) }}</el-descriptions-item>
          <!-- 辅助属性 -->
          <el-descriptions-item label="辅助属性">{{ setCurrentRow && setCurrentRow.AuxpropID && getString(setCurrentRow.AuxpropID.Name) }}</el-descriptions-item>
          <!-- 物料类别 -->
          <el-descriptions-item label="物料类别">{{ setCurrentRow && setCurrentRow.MaterialID && setCurrentRow.MaterialID.MaterialBase && setCurrentRow.MaterialID.MaterialBase[0] && setCurrentRow.MaterialID.MaterialBase[0].CategoryID && getString(setCurrentRow.MaterialID.MaterialBase[0].CategoryID.Name) }}</el-descriptions-item>
          <!-- 货主类型 -->
          <el-descriptions-item label="货主类型">{{ setCurrentRow && setCurrentRow.OwnerTypeID && getOptionLabel(OwnerTypeIDOptions, setCurrentRow.OwnerTypeID) }}</el-descriptions-item>
          <!-- 货主 -->
          <el-descriptions-item label="货主">{{ setCurrentRow && setCurrentRow.OwnerID && getString(setCurrentRow.OwnerID.Name) }}</el-descriptions-item>
          <!-- 批号 -->
          <el-descriptions-item label="批号">{{ setCurrentRow && setCurrentRow.Lot && setCurrentRow.Lot.Number }}</el-descriptions-item>
          <!-- 生产日期 -->
          <el-descriptions-item label="生产日期">{{ setCurrentRow && setCurrentRow.PRODUCEDATE && parseTime(setCurrentRow.PRODUCEDATE, '{y}-{m}-{d}') }}</el-descriptions-item>
          <!-- 保质期单位 -->
          <el-descriptions-item label="保质期单位">{{ setCurrentRow && setCurrentRow.FEXPUNIT && getString(setCurrentRow.FEXPUNIT.Name) }}</el-descriptions-item>
          <!-- 保质期 -->
          <el-descriptions-item label="保质期">{{ (setCurrentRow && setCurrentRow.FEXPPERIOD) || '' }}</el-descriptions-item>
          <!-- 有效期至 -->
          <el-descriptions-item label="有效期至">{{ setCurrentRow && setCurrentRow.FEXPIRYDATE && parseTime(setCurrentRow.FEXPIRYDATE, '{y}-{m}-{d}') }}</el-descriptions-item>
          <!-- 销售单位 -->
          <el-descriptions-item label="销售单位">{{ setCurrentRow && setCurrentRow.UnitID && getString(setCurrentRow.UnitID.Name) }}</el-descriptions-item>
          <!-- 销售数量 -->
          <el-descriptions-item label="销售数量">{{ (setCurrentRow && setCurrentRow.Qty) || '' }}</el-descriptions-item>
          <!-- 库存单位 -->
          <el-descriptions-item label="库存单位">{{ setCurrentRow && setCurrentRow.StockUnitID && getString(setCurrentRow.StockUnitID.Name) }}</el-descriptions-item>
          <!-- 库存数量 -->
          <el-descriptions-item label="库存数量">{{ (setCurrentRow && setCurrentRow.StockQty) || '' }}</el-descriptions-item>
          <!-- 请检关联数量 -->
          <el-descriptions-item label="请检关联数量">{{ (setCurrentRow && setCurrentRow.JoinCheckQty) || '' }}</el-descriptions-item>
          <!-- 合格数量 -->
          <el-descriptions-item label="合格数量">{{ (setCurrentRow && setCurrentRow.QualifiedQty) || '' }}</el-descriptions-item>
          <!-- 不合格数量 -->
          <el-descriptions-item label="不合格数量">{{ setCurrentRow && setCurrentRow.UnqualifiedQty }}</el-descriptions-item>
          <!-- 不合格可销售数量 -->
          <el-descriptions-item label="不合格可销售数量">{{ (setCurrentRow && setCurrentRow.UQCanSaleQty) || '' }}</el-descriptions-item>
          <!-- 报废数量 -->
          <el-descriptions-item label="报废数量">{{ (setCurrentRow && setCurrentRow.JunkedQty) || '' }}</el-descriptions-item>
          <!-- 发货检验 -->
          <el-descriptions-item label="发货检验">
            <el-checkbox v-model="isCheckDelivery" disabled></el-checkbox>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
    <!-- 下推弹出框 -->
    <el-dialog v-dialogDragBox title="选择单据" :visible.sync="pushOpen" width="580px" class="custom-dialog">
      <div style="padding: 0 20px">
        <el-form ref="pushForm" :model="pushForm" :rules="pushFormRules" label-width="80px">
          <el-form-item label="" label-width="0" prop="target">
            <el-radio-group v-model="pushForm.target" v-removeAriaHidden @change="handleTargetChange" style="width: 100%">
              <el-row :gutter="10" class="custom-push-target">
                <el-col :span="12" v-for="item in pushTarget" :key="item.value">
                  <el-radio :label="item.value">{{ item.label }}</el-radio>
                </el-col>
              </el-row>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="转换规则" prop="ruleId" v-if="isNeedRule()">
            <el-select v-model="pushForm.ruleId" placeholder="请选择转换规则" style="width: 100%" @change="calculateTargetBillType">
              <el-option v-for="item in calculateRule" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="单据类型" prop="targetBillTypeId" v-if="isNeedBillType()">
            <el-select v-model="pushForm.targetBillTypeId" placeholder="请选择单据类型" style="width: 100%">
              <el-option v-for="item in calculateBillType" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="目标组织" prop="targetOrgId" v-if="isNeedOrg()">
            <el-select v-model="pushForm.targetOrgId" placeholder="请选择目标组织" style="width: 100%">
              <el-option v-for="item in ApplicationOrgNumber" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn small" @click="pushOpen = false">取消</el-button>
        <el-button class="custom-dialog-btn small primary" :class="{ disabled: !pushForm.target }" :disabled="!pushForm.target" @click="handlePushSubmit">确定</el-button>
      </div>
    </el-dialog>
    <!-- 新增销售出库单 -->
    <sales-out-create ref="salesOutCreate" @callBack="showSalesOutCreate = false" v-if="showSalesOutCreate" />
    <!-- 新增其他出库单 -->
    <mis-delivery-create ref="misDeliveryCreate" @callBack="showMisDeliveryCreate = false" v-if="showMisDeliveryCreate" />
  </div>
</template>
<script>
import { getShippingNoteDetail, auditShippingNote, cancelShippingNote, deleteShippingNote, pushShippingNote, submitShippingNote, unAuditShippingNote } from '@/api/kingdee/sell/shippingNote'
import { kingdee } from '@/minix'
import SalesOutCreate from '@/views/kingdee/sell/salesOut/create'
import MisDeliveryCreate from '@/views/kingdee/inventory/outbound/create'

export default {
  mixins: [kingdee],
  components: { SalesOutCreate, MisDeliveryCreate },
  data() {
    return {
      // 新增
      open: false,
      info: {},
      setCurrentRow: {},
      // 货主类型
      OwnerTypeIDOptions: [
        { label: '业务组织', value: 'BD_OwnerOrg' },
        { label: '供应商', value: 'BD_Supplier' },
        { label: '客户', value: 'BD_Customer' }
      ],
      // 下推
      pushOpen: false,
      pushTarget: [
        { value: 'SAL_OUTSTOCK', label: '销售出库单' },
        { value: 'STK_TRANSFEROUT', label: '分步式调出单' },
        { value: 'STK_MisDelivery', label: '其他出库单' },
        { value: 'STK_TransferDirect', label: '直接调拨单' }
      ],
      pushForm: {},
      pushFormRules: {
        target: [{ required: true, message: '请选择下推单据', trigger: ['blur', 'change'] }],
        ruleId: [{ required: true, message: '请选择转换规则', trigger: ['blur', 'change'] }],
        targetBillTypeId: [{ required: true, message: '请选择单据类型', trigger: ['blur', 'change'] }],
        targetOrgId: [{ required: true, message: '请选择目标组织', trigger: ['blur', 'change'] }]
      },
      buttons: [
        { text: '提交', type: 'primary', action: 'submit', status: ['A', 'D'] },
        { text: '审核', type: 'warning', action: 'audit', status: ['B'] },
        { text: '撤销', type: 'info', action: 'revoke', status: ['B'] },
        { text: '删除', type: 'danger', action: 'delete', status: ['A', 'D'] },
        { text: '反审', type: 'warning', action: 'unAudit', status: ['C'] },
        { text: '下推', type: 'success', action: 'push', status: ['C'] }
      ],
      // 转换规则
      ruleList: [
        // 下推至销售出库单
        { value: 'DeliveryNotice-OutStock', label: '发货通知单至销售出库单', target: 'SAL_OUTSTOCK' },
        { value: 'be0c2e4d-5513-4709-82ac-cc1df3054568', label: '发货通知单-异价调出单', target: 'STK_MisDelivery' },
        { value: 'dea31b35-3311-47b9-badb-3ffcc04da7fb', label: '工程发货通知单-工程项目出库单', target: 'STK_MisDelivery' }
      ],
      // 单据类型
      targetBillTypeList: [
        // 下推至销售出库单
        { value: 'ad0779a4685a43a08f08d2e42d7bf3e9', label: '标准销售出库单', target: 'DeliveryNotice-OutStock', billType: 'FHTZD01_SYS' },
        { value: 'ea4889bc7c004230babdbe8f6ef889b7', label: '寄售出库单', target: 'DeliveryNotice-OutStock', billType: 'FHTZD02_SYS' },
        { value: 'f9744c3493db410cbe2ef85a5a1290c3', label: '分销购销销售出库单', target: 'DeliveryNotice-OutStock', billType: 'FHTZD04_SYS' },
        { value: '78acc0ac34018bd711e30581c729f1fb', label: 'VMI销售出库单', target: 'DeliveryNotice-OutStock', billType: 'FHTZD05_SYS' },
        // 下推至异价调出单
        { value: '64c67a6a876911', label: '异价调出单', target: 'be0c2e4d-5513-4709-82ac-cc1df3054568', billType: 'FHTZD01_SYS' },
        // 下推至工程项目出库单
        { value: '64d73f52384d47', label: '工程项目出库单', target: 'dea31b35-3311-47b9-badb-3ffcc04da7fb', billType: 'FHTZD06_SYS' }
      ],
      // 新增销售出库单
      showSalesOutCreate: false,
      // 新增其他出库单
      showMisDeliveryCreate: false
    }
  },
  computed: {
    // 是否发货检验
    isCheckDelivery() {
      return this.setCurrentRow?.CheckDelivery || false
    },
    // 计算规则
    calculateRule() {
      return this.ruleList.filter(item => item.target === this.pushForm.target)
    },
    // 计算单据类型
    calculateBillType() {
      const { ruleId } = this.pushForm
      const billType = this.info?.BillTypeID?.Number || ''
      const arr = this.targetBillTypeList.filter(item => item.target === ruleId) || []
      const newArr = arr.filter(item => item.billType === billType) || []
      return newArr
    }
  },
  methods: {
    // 检查状态
    checkStatus(allowedStatus) {
      return allowedStatus.includes(this.info.DocumentStatus)
    },
    // 获取详情
    getInfo(row = {}) {
      this.open = true
      if (!row.BillNo) {
        this.$message.error('参数错误，请重新选择')
        return
      }
      getShippingNoteDetail({ billNo: row.BillNo }).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          const { result } = data
          this.open = !!result?.result
          this.info = result?.result || {}
          this.$nextTick(() => {
            this.setCurrentRow = result?.result?.SAL_DELIVERYNOTICEENTRY?.[0] || {}
            if (this.$refs.detailTable) {
              this.$refs.detailTable.setCurrentRow(this.setCurrentRow)
              this.$refs.detailTable.bodyWrapper.scrollLeft = 0
            }
          })
        } else this.$message.error(msg)
      })
    },
    // 当前行
    handleCurrentChange(val) {
      this.setCurrentRow = val
    },
    // 关闭前
    beforeClose() {
      this.handleClose()
      this.$emit('update', { billNo: this.info.BillNo, newStatus: this.info.DocumentStatus })
    },
    // 关闭
    handleClose(flag = false) {
      this.open = false
      this.$emit('callBack', flag)
    },
    // 金蝶操作
    // prettier-ignore
    handleKingdeeDo(type) {
      const number = this.info.BillNo
      if (!number) return
      switch (type) {
        case 'submit':
          // 提交
          this.$modal.confirm('确认要提交该发货通知单吗？').then(() => {
            submitShippingNote({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('提交成功')
                this.$set(this.info, 'DocumentStatus', 'B')
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'audit':
          // 审核
          this.$modal.confirm('确认要审核该发货通知单吗？').then(() => {
            auditShippingNote({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('审核成功')
                this.$set(this.info, 'DocumentStatus', 'C')
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'revoke':
          // 撤销
          this.$modal.confirm('确认要撤销该发货通知单吗？').then(() => {
            cancelShippingNote({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('撤销成功')
                this.$set(this.info, 'DocumentStatus', 'D')
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'unAudit':
          // 反审核
          this.$modal.confirm('确认要反审核该发货通知单吗？').then(() => {
            unAuditShippingNote({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('反审核成功')
                this.$set(this.info, 'DocumentStatus', 'D')
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'delete':
          // 删除
          this.$modal.confirm('确认要删除该发货通知单吗？').then(() => {
            deleteShippingNote({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('删除成功')
                this.handleClose(true)
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'push':
          // 下推
          this.initPushForm()
          break
      }
    },
    // 初始化下推表单
    initPushForm() {
      this.pushForm = {
        number: this.info.BillNo,
        target: undefined,
        ruleId: undefined,
        targetBillTypeId: undefined,
        targetOrgId: undefined
      }
      this.$nextTick(() => {
        this.resetForm('pushForm')
      })
      this.pushOpen = true
    },
    // 切换下推单据
    handleTargetChange(val) {
      if (val === 'SAL_OUTSTOCK' || val === 'STK_MisDelivery') {
        this.setupSalOutStockPush()
        return
      }
      this.clearPushFormFields()
    },
    // 设置发货通知单下推
    setupSalOutStockPush() {
      this.pushForm.ruleId = this.calculateRule?.[0]?.value || undefined
      this.pushForm.targetBillTypeId = undefined
      // 计算目标单据类型
      this.$nextTick(() => {
        this.calculateTargetBillType()
      })
    },
    // 清空下推表单字段
    clearPushFormFields() {
      this.pushForm.ruleId = undefined
      this.pushForm.targetBillTypeId = undefined
    },
    // 计算目标单据类型
    calculateTargetBillType() {
      const { ruleId } = this.pushForm
      const billType = this.info?.BillTypeID?.Number || ''
      const arr = this.targetBillTypeList.filter(item => item.target === ruleId) || []
      const newArr = arr.filter(item => item.billType === billType) || []
      this.pushForm.targetBillTypeId = newArr?.[0]?.value || undefined
    },
    // 判断是否需要转换规则
    isNeedRule() {
      const { target, ruleId } = this.pushForm
      if (target === 'SAL_OUTSTOCK' || target === 'STK_MisDelivery') {
        return true
      }
      return false
    },
    // 判断是否需要单据类型
    isNeedBillType() {
      const { target, ruleId } = this.pushForm
      if (target === 'SAL_OUTSTOCK' || target === 'STK_MisDelivery') {
        return true
      }
      return false
    },
    // 判断是否需要组织
    isNeedOrg() {
      const { target, ruleId } = this.pushForm
      return false
    },
    // 下推提交
    handlePushSubmit() {
      const { number } = this.pushForm
      if (!number) return
      this.$refs.pushForm.validate(valid => {
        if (valid) {
          this.executePushDown()
        }
      })
    },
    // 执行下推操作
    // prettier-ignore
    executePushDown() {
      pushShippingNote(this.pushForm).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          this.handlePushSuccess(data)
        } else if (code === 400) {
          this.handlePushError(msg)
        } else {
          this.$message.error(msg)
        }
      }).catch(error => {
        console.error('下推操作失败:', error)
        this.$message.error('下推操作失败，请重试')
      })
    },
    // 处理下推成功
    handlePushSuccess(data) {
      if (this.pushForm.target === 'SAL_OUTSTOCK') {
        this.handleSalOutStockPushSuccess(data)
      } else if (this.pushForm.target === 'STK_MisDelivery') {
        this.handleMisDeliveryPushSuccess(data)
      } else {
        this.$message.success('下推成功')
        this.pushOpen = false
        this.handleClose(true)
      }
    },
    // 处理销售出库单下推成功
    handleSalOutStockPushSuccess(data) {
      this.pushOpen = false
      this.showSalesOutCreate = true
      const { responseStatus } = data
      const fid = responseStatus?.successEntitys?.[0]?.id
      if (!fid) {
        this.$message.error('参数错误，请重试！')
        return
      }
      this.$nextTick(() => {
        this.$refs.salesOutCreate.initPush(fid, 'push')
      })
    },
    // 处理其他出库单下推成功
    handleMisDeliveryPushSuccess(data) {
      this.pushOpen = false
      this.showMisDeliveryCreate = true
      const { responseStatus } = data
      const fid = responseStatus?.successEntitys?.[0]?.id
      if (!fid) {
        this.$message.error('参数错误，请重试！')
        return
      }
      this.$nextTick(() => {
        this.$refs.misDeliveryCreate.initPush(fid, 'push')
      })
    },
    // 处理下推错误
    handlePushError(msg) {
      this.$alert(msg || '操作失败，请重试或使用金蝶进行操作！', '提示', {
        type: 'info',
        confirmButtonText: '确定',
        callback: () => {
          this.pushOpen = false
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
@import '@/assets/styles/custom-new.scss';
::v-deep {
  .el-descriptions__header {
    margin-bottom: 0;
  }
  .el-table.custom-table .el-table__body-wrapper .el-table__row:hover td.el-table__cell {
    background-color: #eaf1fe !important;
  }
}
.customTitle {
  font-size: 16px;
  font-weight: normal;
  line-height: 60px;
  color: $font;
}
.kindeeButton {
  display: flex;
  align-items: center;
}
.custom-push-target {
  .el-col.el-col-12 {
    .el-radio {
      height: 30px;
      display: flex;
      align-items: center;
    }
  }
}
</style>
