<template>
  <div>
    <template v-if="isDialog">
      <el-dialog v-dialogDragBox :title="title" :visible.sync="open" width="90%" class="custom-dialog" :before-close="beforeClose" append-to-body>
        <div style="padding: 0 20px">
          <el-form :model="form" :rules="rules" ref="form" label-width="6em" class="custom-form">
            <el-row :gutter="10">
              <!-- 单据类型 -->
              <el-col :span="6">
                <el-form-item label="单据类型" prop="fbilltypeid">
                  <el-select v-model="form.fbilltypeid" placeholder="请选择单据类型" style="width: 100%">
                    <el-option v-for="item in billTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <!-- 发货组织 -->
              <el-col :span="6">
                <el-form-item label="发货组织" prop="fdeliveryorgid">
                  <el-select v-model="form.fdeliveryorgid" placeholder="请选择发货组织" style="width: 100%">
                    <el-option v-for="item in ApplicationOrgId" :key="item.value" :label="item.label" :value="item.value"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <!-- 销售组织 -->
              <el-col :span="6">
                <el-form-item label="销售组织" prop="fsaleorgid">
                  <el-select v-model="form.fsaleorgid" placeholder="请选择销售组织" style="width: 100%">
                    <el-option v-for="item in ApplicationOrgId" :key="item.value" :label="item.label" :value="item.value"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <!-- 客户 -->
              <el-col :span="6">
                <el-form-item label="客户" prop="fcustomername">
                  <customer-search-select :useOrg="form.fdeliveryorgid" :showLabel="false" :keyword.sync="form.fcustomername" style="width: 100%" isBack @callBack="handleFormCustomer($event)" />
                </el-form-item>
              </el-col>
              <!-- 日期 -->
              <el-col :span="6">
                <el-form-item label="日期" prop="fdate">
                  <el-date-picker v-model="form.fdate" type="date" placeholder="请选择日期" style="width: 100%"></el-date-picker>
                </el-form-item>
              </el-col>
              <!-- 结算币别 -->
              <el-col :span="6">
                <el-form-item label="结算币别" prop="fsettlecurrid">
                  <el-select v-model="form.fsettlecurrid" placeholder="请选择结算币别" style="width: 100%">
                    <el-option v-for="item in CurrencyId" :key="item.value" :label="item.label" :value="item.value"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <!-- 跟单员 -->
              <el-col :span="6">
                <el-form-item label="跟单员" prop="fscmjgdyname">
                  <staff-search-select placeholder="请选择跟单员" :showLabel="false" :keyword.sync="form.fscmjgdyname" style="width: 100%" isBack @callBack="handleStaffSearchSelect($event)" />
                </el-form-item>
              </el-col>
              <!-- 备注 -->
              <el-col :span="6">
                <el-form-item label="备注" prop="fnote">
                  <el-input v-model="form.fnote" placeholder="请输入备注" style="width: 100%"></el-input>
                </el-form-item>
              </el-col>
              <!-- 明细信息 -->
              <el-col :span="24">
                <el-table :data="form.entities" style="width: 100%" stripe class="custom-table custom-table-cell0" show-summary :summary-method="getSummary">
                  <!-- 序号 -->
                  <el-table-column type="index" label="序号" align="center" width="50"></el-table-column>
                  <!-- 物料编码 -->
                  <el-table-column label="物料编码" align="center" show-overflow-tooltip>
                    <template slot-scope="{ row }">
                      <span class="table-link" @click="handleMaterialNumber(row.fmaterialid)">{{ row.fmaterialid }}</span>
                    </template>
                  </el-table-column>
                  <!-- 物料名称 -->
                  <el-table-column label="物料名称" align="center" show-overflow-tooltip>
                    <template slot-scope="{ row }">{{ row.fmaterialname }}</template>
                  </el-table-column>
                  <!-- 规格型号 -->
                  <el-table-column label="规格型号" align="center" show-overflow-tooltip>
                    <template slot-scope="{ row }">{{ row.fmaterialmodel }}</template>
                  </el-table-column>
                  <!-- 销售单位 -->
                  <el-table-column label="销售单位" align="center">
                    <template slot-scope="scope">
                      <el-form-item label-width="0" :prop="`entities.${scope.$index}.funitid`" :rules="rules.funitid">
                        <el-select v-model="scope.row.funitid" placeholder="请选择销售单位" style="width: 100%" size="small">
                          <el-option v-for="(item, index) in UnitList" :key="index" :label="item.FName" :value="item.FNumber"></el-option>
                        </el-select>
                      </el-form-item>
                    </template>
                  </el-table-column>
                  <!-- 销售数量 -->
                  <el-table-column label="销售数量" align="center" prop="fqty">
                    <template slot-scope="scope">
                      <el-tooltip :content="scope.row.fqty + ''" placement="top">
                        <el-form-item label-width="0" :prop="`entities.${scope.$index}.fqty`" :rules="rules.fqty">
                          <el-input v-model="scope.row.fqty" placeholder="销售数量" style="width: 100%" size="small" @change="handleQtyChange(scope.row)">
                            <span slot="suffix" class="inline-flex">{{ scope.row.funit }}</span>
                          </el-input>
                        </el-form-item>
                      </el-tooltip>
                    </template>
                  </el-table-column>
                  <!-- 是否赠品 -->
                  <el-table-column prop="fgift" label="是否赠品" align="center">
                    <template slot-scope="scope">
                      <el-form-item label-width="0" :prop="`entities.${scope.$index}.fisfree`" :rules="rules.fisfree">
                        <el-checkbox v-model="scope.row.fisfree" size="small"></el-checkbox>
                      </el-form-item>
                    </template>
                  </el-table-column>
                  <!-- 要货日期 -->
                  <el-table-column prop="fdeliverydate" label="要货日期" align="center" width="140">
                    <template slot-scope="scope">
                      <el-form-item label-width="0" :prop="`entities.${scope.$index}.fdeliverydate`" :rules="rules.fdeliverydate">
                        <el-date-picker v-model="scope.row.fdeliverydate" type="date" placeholder="请选择要货日期" style="width: 100%" size="small"></el-date-picker>
                      </el-form-item>
                    </template>
                  </el-table-column>
                  <!-- 出货仓库 -->
                  <el-table-column prop="fstockid" label="出货仓库" align="center">
                    <template slot-scope="scope">
                      <el-form-item label-width="0" :prop="`entities.${scope.$index}.fstockid`" :rules="rules.fstockid">
                        <stock-search-select :keyword.sync="scope.row.fstockid" :useOrg="form.fdeliveryorgid" :showLabel="false" style="width: 100%" size="small" isBack @callBack="handleStockSelect($event, scope.row)" />
                      </el-form-item>
                    </template>
                  </el-table-column>
                  <!-- 出货仓位 -->
                  <el-table-column prop="fstocklocid" label="出货仓位" align="center">
                    <template slot-scope="scope">{{ scope.row.fstockLocid && getString(scope.row.fstockLocid.Name) }}</template>
                  </el-table-column>
                  <!-- 备注 -->
                  <el-table-column label="备注" align="center">
                    <template slot-scope="scope">
                      <el-input v-model="scope.row.fnoteentry" placeholder="请输入备注" style="width: 100%" size="small"></el-input>
                    </template>
                  </el-table-column>
                  <!-- 结算单价 -->
                  <el-table-column prop="fprice" label="结算单价" align="center">
                    <template slot-scope="scope">
                      <el-form-item label-width="0" :prop="`entities.${scope.$index}.fscmjjsprice`" :rules="rules.fscmjjsprice">
                        <el-input v-model="scope.row.fscmjjsprice" placeholder="结算单价" style="width: 100%" size="small" @change="handleScmjJspriceChange(scope.row)">
                          <span slot="prefix" class="inline-flex">￥</span>
                        </el-input>
                      </el-form-item>
                    </template>
                  </el-table-column>
                  <!-- 结算金额 -->
                  <el-table-column prop="fscmjjsamount" label="结算金额" align="center"></el-table-column>
                  <!-- 操作 -->
                  <el-table-column label="操作" align="center">
                    <template slot-scope="scope">
                      <el-button type="text" size="small" icon="el-icon-delete" :disabled="form.entities.length === 1" @click="handleDeleteEntry(scope.$index)">删除</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </el-col>
            </el-row>
          </el-form>
        </div>
        <div slot="footer">
          <el-button class="custom-dialog-btn" @click="handleClose()">取消</el-button>
          <el-button class="custom-dialog-btn primary" :loading="loading" @click="handleSubmit">确定</el-button>
        </div>
      </el-dialog>
    </template>
    <template v-else>
      <div style="padding: 0 20px">
        <el-form :model="form" :rules="rules" ref="form" label-width="6em" class="custom-form">
          <el-row :gutter="10">
            <!-- 单据类型 -->
            <el-col :span="6">
              <el-form-item label="单据类型" prop="fbilltypeid">
                <el-select v-model="form.fbilltypeid" placeholder="请选择单据类型" style="width: 100%">
                  <el-option v-for="item in billTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 发货组织 -->
            <el-col :span="6">
              <el-form-item label="发货组织" prop="fdeliveryorgid">
                <el-select v-model="form.fdeliveryorgid" placeholder="请选择发货组织" style="width: 100%">
                  <el-option v-for="item in ApplicationOrgId" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 销售组织 -->
            <el-col :span="6">
              <el-form-item label="销售组织" prop="fsaleorgid">
                <el-select v-model="form.fsaleorgid" placeholder="请选择销售组织" style="width: 100%">
                  <el-option v-for="item in ApplicationOrgId" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 客户 -->
            <el-col :span="6">
              <el-form-item label="客户" prop="fcustomername">
                <customer-search-select :useOrg="form.fdeliveryorgid" :showLabel="false" :keyword.sync="form.fcustomername" style="width: 100%" isBack @callBack="handleFormCustomer($event)" />
              </el-form-item>
            </el-col>
            <!-- 日期 -->
            <el-col :span="6">
              <el-form-item label="日期" prop="fdate">
                <el-date-picker v-model="form.fdate" type="date" placeholder="请选择日期" style="width: 100%"></el-date-picker>
              </el-form-item>
            </el-col>
            <!-- 结算币别 -->
            <el-col :span="6">
              <el-form-item label="结算币别" prop="fsettlecurrid">
                <el-select v-model="form.fsettlecurrid" placeholder="请选择结算币别" style="width: 100%">
                  <el-option v-for="item in CurrencyId" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 跟单员 -->
            <el-col :span="6">
              <el-form-item label="跟单员" prop="fscmjgdyname">
                <staff-search-select placeholder="请选择跟单员" :showLabel="false" :keyword.sync="form.fscmjgdyname" style="width: 100%" isBack @callBack="handleStaffSearchSelect($event)" />
              </el-form-item>
            </el-col>
            <!-- 备注 -->
            <el-col :span="6">
              <el-form-item label="备注" prop="fnote">
                <el-input v-model="form.fnote" placeholder="请输入备注" style="width: 100%"></el-input>
              </el-form-item>
            </el-col>
            <!-- 明细信息 -->
            <el-col :span="24">
              <el-table :data="form.entities" style="width: 100%" stripe class="custom-table custom-table-cell0" show-summary :summary-method="getSummary">
                <!-- 序号 -->
                <el-table-column type="index" label="序号" align="center" width="50"></el-table-column>
                <!-- 物料编码 -->
                <el-table-column label="物料编码" align="center" show-overflow-tooltip>
                  <template slot-scope="{ row }">
                    <span class="table-link" @click="handleMaterialNumber(row.fmaterialid)">{{ row.fmaterialid }}</span>
                  </template>
                </el-table-column>
                <!-- 物料名称 -->
                <el-table-column label="物料名称" align="center" show-overflow-tooltip>
                  <template slot-scope="{ row }">{{ row.fmaterialname }}</template>
                </el-table-column>
                <!-- 规格型号 -->
                <el-table-column label="规格型号" align="center" show-overflow-tooltip>
                  <template slot-scope="{ row }">{{ row.fmaterialmodel }}</template>
                </el-table-column>
                <!-- 销售单位 -->
                <el-table-column label="销售单位" align="center">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.funitid`" :rules="rules.funitid">
                      <el-select v-model="scope.row.funitid" placeholder="请选择销售单位" style="width: 100%" size="small">
                        <el-option v-for="(item, index) in UnitList" :key="index" :label="item.FName" :value="item.FNumber"></el-option>
                      </el-select>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 销售数量 -->
                <el-table-column label="销售数量" align="center" prop="fqty">
                  <template slot-scope="scope">
                    <el-tooltip :content="scope.row.fqty + ''" placement="top">
                      <el-form-item label-width="0" :prop="`entities.${scope.$index}.fqty`" :rules="rules.fqty">
                        <el-input v-model="scope.row.fqty" placeholder="销售数量" style="width: 100%" size="small" @change="handleQtyChange(scope.row)">
                          <span slot="suffix" class="inline-flex">{{ scope.row.funit }}</span>
                        </el-input>
                      </el-form-item>
                    </el-tooltip>
                  </template>
                </el-table-column>
                <!-- 是否赠品 -->
                <el-table-column prop="fgift" label="是否赠品" align="center">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.fisfree`" :rules="rules.fisfree">
                      <el-checkbox v-model="scope.row.fisfree" size="small"></el-checkbox>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 要货日期 -->
                <el-table-column prop="fdeliverydate" label="要货日期" align="center" width="140">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.fdeliverydate`" :rules="rules.fdeliverydate">
                      <el-date-picker v-model="scope.row.fdeliverydate" type="date" placeholder="请选择要货日期" style="width: 100%" size="small"></el-date-picker>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 出货仓库 -->
                <el-table-column prop="fstockid" label="出货仓库" align="center">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.fstockid`" :rules="rules.fstockid">
                      <stock-search-select :keyword.sync="scope.row.fstockid" :useOrg="form.fdeliveryorgid" :showLabel="false" style="width: 100%" size="small" isBack @callBack="handleStockSelect($event, scope.row)" />
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 出货仓位 -->
                <el-table-column prop="fstocklocid" label="出货仓位" align="center">
                  <template slot-scope="scope">{{ scope.row.fstockLocid && getString(scope.row.fstockLocid.Name) }}</template>
                </el-table-column>
                <!-- 备注 -->
                <el-table-column label="备注" align="center">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.fnoteentry" placeholder="请输入备注" style="width: 100%" size="small"></el-input>
                  </template>
                </el-table-column>
                <!-- 结算单价 -->
                <el-table-column prop="fprice" label="结算单价" align="center">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.fscmjjsprice`" :rules="rules.fscmjjsprice">
                      <el-input v-model="scope.row.fscmjjsprice" placeholder="结算单价" style="width: 100%" size="small" @change="handleScmjJspriceChange(scope.row)">
                        <span slot="prefix" class="inline-flex">￥</span>
                      </el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 结算金额 -->
                <el-table-column prop="fscmjjsamount" label="结算金额" align="center"></el-table-column>
                <!-- 操作 -->
                <el-table-column label="操作" align="center">
                  <template slot-scope="scope">
                    <el-button type="text" size="small" icon="el-icon-delete" :disabled="form.entities.length === 1" @click="handleDeleteEntry(scope.$index)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </template>
    <!-- 发货通知单详情 -->
    <shipping-note-detail ref="shippingNoteDetail" />
    <!-- 去发货 -->
    <send-product ref="sendProduct" @callBack="sendProduct = false" @delete="handleDelete" v-if="sendProduct" />
  </div>
</template>
<script>
import { kingdee } from '@/minix'
import CustomerSearchSelect from '@/components/SearchSelect/customer'
import StockSearchSelect from '@/components/SearchSelect/stock'
import StaffSearchSelect from '@/components/SearchSelect/staff'
import { getShippingNoteDetail2, createShippingNote, deleteShippingNoteV2 } from '@/api/kingdee/sell/shippingNote'
import { isNumber, isNumberLength } from '@/utils/validate'
import ShippingNoteDetail from '@/views/kingdee/sell/shippingNote/detail'
import SendProduct from '@/views/sendProduct/send'

export default {
  props: {
    isDialog: {
      type: Boolean,
      default: true
    }
  },
  name: 'ShippingNoteCreate',
  mixins: [kingdee],
  components: { CustomerSearchSelect, StockSearchSelect, StaffSearchSelect, ShippingNoteDetail, SendProduct },
  data() {
    return {
      fid: undefined,
      form: {},
      rules: {
        fbilltypeid: [{ required: true, message: '请选择单据类型', trigger: 'change' }],
        fdeliveryorgid: [{ required: true, message: '请选择发货组织', trigger: 'change' }],
        fsaleorgid: [{ required: true, message: '请选择销售组织', trigger: 'change' }],
        fcustomername: [{ required: true, message: '请选择客户', trigger: 'change' }],
        fdate: [{ required: true, message: '请选择日期', trigger: 'change' }],
        fsettlecurrid: [{ required: true, message: '请选择结算币别', trigger: 'change' }],
        fscmjgdyname: [{ required: true, message: '请选择跟单员', trigger: 'change' }],
        funitid: [{ required: true, message: '请选择销售单位', trigger: 'change' }],
        fqty: [
          { required: true, message: '请输入销售数量', trigger: 'blur' },
          { validator: isNumber, message: '请输入正确的销售数量', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: 'blur' }
        ],
        fscmjjsprice: [
          { required: true, message: '请输入结算单价', trigger: 'blur' },
          { validator: isNumber, message: '请输入正确的结算单价', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: 'blur' }
        ]
      },
      title: '',
      open: false,
      loading: false,
      // 单据类型
      billTypeOptions: [
        { label: '标准发货通知单', value: 'FHTZD01_SYS' },
        { label: '寄售发货通知单', value: 'FHTZD02_SYS' },
        { label: '分销调拨发货通知单', value: 'FHTZD03_SYS' },
        { label: '分销购销发货通知单', value: 'FHTZD04_SYS' },
        { label: 'VMI发货通知单', value: 'FHTZD05_SYS' },
        { label: '工程发货通知单', value: 'FHTZD06_SYS' }
      ],
      isPush: false,
      contractInfo: undefined, // 销售合同信息
      sendProduct: false, // 去发货
      hasSuccessfully: false // 是否已成功提交
    }
  },
  methods: {
    // 删除明细
    handleDeleteEntry(index) {
      this.form.entities.splice(index, 1)
    },
    // 选择仓库
    handleStockSelect(stock, row) {
      this.$set(row, 'fstockid', stock.Number)
      this.$set(row, 'fstockname', stock.Name)
    },
    // 选择客户
    handleFormCustomer(data) {
      this.$set(this.form, 'fcustomername', data.name)
      this.$set(this.form, 'fcustomerid', data.id)
    },
    // 选择跟单员
    handleStaffSearchSelect(data) {
      this.$set(this.form, 'fscmjgdyname', data.name)
      this.$set(this.form, 'fscmjgdy', data.id)
    },
    // 关闭弹窗
    beforeClose() {
      this.handleClose()
    },
    // 关闭弹窗
    async handleClose(flag = false) {
      if (this.fid && !this.hasSuccessfully) {
        try {
          await deleteShippingNoteV2({ id: this.fid })
          this.fid = undefined
          this.$nextTick(() => {
            this.open = false
            this.$emit('callBack', flag)
          })
        } catch (error) {
          this.$message.error(error.message)
        }
      } else {
        this.open = false
        this.$emit('callBack', flag)
      }
    },
    // 删除
    async handleDelete() {
      if (this.fid) {
        try {
          await deleteShippingNoteV2({ id: this.fid })
          this.fid = undefined
          this.$nextTick(() => {
            this.open = false
            this.$emit('delete')
          })
        } catch (error) {
          this.$message.error(error.message)
        }
      }
    },
    // 表单初始化
    reset() {
      this.form = {
        entities: [], // 明细信息
        fbilltypeid: undefined, // 单据类型
        fcarriageno: undefined, // 运输单号
        fcarrierid: undefined, // 承运商
        fcustomerid: undefined, // 客户
        fcustomername: undefined, // 客户名称
        fdate: undefined, // 日期
        fdeliverydeptid: undefined, // 发货部门
        fdeliveryorgid: undefined, // 发货组织
        fheaddeliveryway: undefined, // 交货方式
        fheadlocid: undefined, // 交货地点
        fid: undefined, // 单据ID
        fnote: undefined, // 备注
        fsaledeptid: undefined, // 销售部门
        fsaleorgid: undefined, // 销售组织
        fsalesmanid: undefined, // 销售员
        fscmjgdy: undefined, // 跟单员
        fscmjgdyname: undefined, // 跟单员名称
        fscmjxsddh: undefined, // 销售订单号
        fsettlecurrid: undefined, // 结算币别
        fstockerid: undefined, // 仓管员
        ftraceDetails: [] // 物流详细信息
      }
      this.resetForm('form')
      this.isPush = false
      this.hasSuccessfully = false
    },
    // 初始化
    // prettier-ignore
    initPush(fid, type = undefined, contractInfo = {}) {
      if (!fid) return this.$message.warning('参数错误，请刷新页面重试')
      const loading = this.$loading({
        lock: true,
        text: '加载中...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      this.reset()
      this.isPush = type == 'push'
      this.contractInfo = contractInfo
      getShippingNoteDetail2({ fid }).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          const info = data?.result?.result || {}
          this.fid = fid
          const detailEntities = info?.SAL_DELIVERYNOTICEENTRY || []
          const entities = detailEntities.map(item => {
            return {
              fentryid: item?.Id || undefined,
              fmaterialid: item?.MaterialID?.Number || undefined, // 物料编码
              fmaterialname: (item?.MaterialID?.Name && this.getString(item?.MaterialID?.Name)) || undefined, // 物料名称
              fmaterialmodel: (item?.MaterialID?.Specification && this.getString(item?.MaterialID?.Specification)) || undefined, // 规格型号
              funitid: item?.UnitID?.Number || undefined, // 销售单位
              funit: (item?.UnitID?.Name && this.getString(item?.UnitID?.Name)) || undefined, // 销售单位名称
              fqty: item?.Qty || undefined, // 销售数量
              fisfree: item?.IsFree || false, // 是否赠品
              fdeliverydate: item?.DeliveryDate || undefined, // 要货日期
              fstockid: item?.StockID?.Number || undefined, // 出货仓库
              fstockname: (item?.StockID?.Name && this.getString(item?.StockID?.Name)) || undefined, // 出货仓库名称
              fstockLocid: item?.StockLocID?.Number || undefined, // 出货仓位
              fstockLocname: (item?.StockLocID?.Name && this.getString(item?.StockLocID?.Name)) || undefined, // 出货仓位名称
              fnoteentry: item?.NoteEntry || undefined, // 备注
              fscmjjsprice: item?.F_SCMJ_JSPrice || undefined, // 结算单价
              fscmjjsamount: item?.F_SCMJ_JSAmount ? item?.F_SCMJ_JSAmount : item?.F_SCMJ_JSPrice ? parseFloat((item?.F_SCMJ_JSPrice * item?.Qty).toFixed(5)) : undefined, // 结算金额
              foutlmtunit: item?.OutLmtUnit || undefined // 超发控制单位类型
              // // 新增的价格相关字段
              // fallamountexceptdiscount: item?.AllAmountExceptDisCount || undefined, // 价税合计（折前）
              // famount_lc: item?.Amount_LC || undefined, // 金额（本位币）
              // famount: item?.Amount || undefined, // 金额
              // ftaxnetprice: item?.TaxNetPrice || undefined, // 净价
              // fallamount: item?.AllAmount || undefined, // 价税合计
              // fallamount_lc: item?.AllAmount_LC || undefined, // 价税合计（本位币）
              // fprice: item?.Price || undefined, // 单价
              // ftaxprice: item?.TaxPrice || undefined // 含税单价
            }
          })
          const fsettlecurridArr = info?.SAL_DELIVERYNOTICEFIN || []
          const fsettlecurrid = fsettlecurridArr.length > 0 ? fsettlecurridArr[0]?.SettleCurrID?.Number : undefined
          this.form = {
            entities, // 明细信息
            fbilltypeid: info?.BillTypeID?.Number || undefined, // 单据类型
            fcarriageno: info?.CarriageNO || undefined, // 运输单号
            fcarrierid: info?.CarrierID?.Number || undefined, // 承运商
            fcustomerid: info?.CustomerID?.Number || undefined, // 客户
            fcustomername: (info?.CustomerID?.Name && this.getString(info?.CustomerID?.Name)) || undefined, // 客户名称
            fdate: info?.Date || undefined, // 日期
            fdeliverydeptid: info?.DeliveryDeptID?.Number || undefined, // 发货部门
            fdeliveryorgid: info?.DeliveryOrgID?.Number, // 发货组织
            fheaddeliveryway: info?.HeadDeliveryWay?.Number || undefined, // 交货方式
            fheadlocid: info?.HeadLocId?.Number || undefined, // 交货地点
            fid, // 单据ID
            fnote: info?.Note || undefined, // 备注
            fsaledeptid: info?.SaleDeptID?.Number || undefined, // 销售部门
            fsaleorgid: info?.SaleOrgId?.Number || undefined, // 销售组织
            fsalesmanid: info?.SalesManID?.Number || undefined, // 销售员
            fscmjgdy: info?.F_SCMJ_GDY?.Number || undefined, // 跟单员
            fscmjgdyname: (info?.F_SCMJ_GDY?.Name && this.getString(info?.F_SCMJ_GDY?.Name)) || undefined, // 跟单员名称
            fscmjxsddh: info?.F_SCMJ_XSDDH || undefined, // 销售订单号
            fsettlecurrid, // 结算币别
            fstockerid: info?.StockerID?.Number || undefined, // 仓管员
            ftraceDetails: info?.DeliNoticeTrace || [] // 物流详细信息
          }
          this.title = '新增发货通知单'
          this.open = true
        } else this.$message.error(msg)
      }).finally(() => {
        loading.close()
      })
    },
    // 改变销售数量计算结算金额
    handleQtyChange(row) {
      const qty = Number(row?.fqty) || 0
      const price = Number(row?.fscmjjsprice) || 0
      if (qty > 0 && price > 0) {
        this.$set(row, 'fscmjjsamount', parseFloat((price * qty).toFixed(5)))
      } else {
        this.$set(row, 'fscmjjsamount', undefined)
      }
    },
    // 改变结算单价计算结算金额
    handleScmjJspriceChange(row) {
      const qty = Number(row?.fqty) || 0
      const price = Number(row?.fscmjjsprice) || 0
      if (qty > 0 && price > 0) {
        this.$set(row, 'fscmjjsamount', parseFloat((price * qty).toFixed(5)))
      } else {
        this.$set(row, 'fscmjjsamount', undefined)
      }
    },
    // 获取合计
    getSummary(param) {
      const { columns, data } = param
      const result = columns.map((column, index) => {
        if (index === 0) {
          return '合计'
        }
        if (column.property === 'fqty' || column.property === 'fscmjjsamount') {
          return `${this.calculateTotal(data, column.property)}`
        }
        return ''
      })
      return result
    },
    // 计算合计
    calculateTotal(data, key) {
      return data.reduce((total, item) => {
        // 确保total是数字类型
        const currentTotal = Number(total) || 0
        if (key === 'fqty') {
          const qty = Number(item.fqty) || 0
          return parseFloat((currentTotal + qty).toFixed(5))
        } else if (key === 'fscmjjsamount') {
          const qty = Number(item.fqty) || 0
          const price = Number(item.fscmjjsprice) || 0
          return parseFloat((currentTotal + qty * price).toFixed(5))
        }
        return currentTotal
      }, 0)
    },
    // 提交
    // prettier-ignore
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true
          const { entities } = this.form          
          const submitForm = JSON.parse(JSON.stringify(this.form))
          submitForm.entities = submitForm.entities.map(item => {
            const { fmaterialid, fmaterialname, ...rest } = item
            return rest
          })
          createShippingNote(submitForm).then(res => {
            const { code, msg } = res
            if (code === 200) {
              this.$message.success('操作成功')
              this.hasSuccessfully = true
              this.$emit('pushClose')
              if (this.isPush) {
                const { number } = res.data
                if (number) {
                  this.open = false
                  this.sendProduct = true
                  this.$nextTick(() => {
                    if (this.contractInfo && Object.keys(this.contractInfo).length > 0) {
                      this.$refs.sendProduct.handleOpen({ ...this.contractInfo, BillNo: number }, entities)
                    } else {
                      this.$refs.shippingNoteDetail.getInfo({ BillNo: number })
                    }
                  })
                } else {
                  this.open = false
                  this.$emit('callBack', true)
                }
              } else {
                this.open = false
                this.$emit('callBack', true)
              }
            } else this.$message.error(msg)
          }).finally(() => {
            this.loading = false
          })
        }
      })
    },
    // 物料详情
    handleMaterialNumber(number) {
      if (number) this.$parent.handleMaterialNumber(number)
    }
  }
}
</script>
<style scoped lang="scss">
@import '@/assets/styles/custom-new.scss';
::v-deep {
  .custom-table {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    border-bottom: 0;
    margin-bottom: 20px;
    .el-form-item {
      margin-top: 10px !important;
      margin-bottom: 10px !important;
      .el-form-item__error {
        top: 95%;
        padding-top: 0;
      }
    }
    .el-input--prefix .el-input__inner {
      padding-left: 20px;
      padding-right: 20px;
    }
    .el-input--prefix.el-date-editor--date .el-input__inner {
      padding-left: 30px;
    }
    .el-date-editor--datetime .el-input__inner {
      padding-left: 30px;
      padding-right: 30px;
    }
  }
}
</style>
