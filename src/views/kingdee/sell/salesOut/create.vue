<template>
  <div>
    <el-dialog v-dialogDragBox :title="title" :visible.sync="open" width="90%" class="custom-dialog" :before-close="beforeClose" append-to-body>
      <div style="padding: 0 20px">
        <el-form :model="form" :rules="rules" ref="form" label-width="6em" class="custom-form">
          <el-row :gutter="20">
            <!-- 单据类型 -->
            <el-col :span="6">
              <el-form-item label="单据类型" prop="fbilltypeid">
                <el-select v-model="form.fbilltypeid" placeholder="请选择单据类型" style="width: 100%">
                  <el-option v-for="item in billTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 日期 -->
            <el-col :span="6">
              <el-form-item label="日期" prop="fdate">
                <el-date-picker v-model="form.fdate" type="date" placeholder="请选择日期" style="width: 100%"></el-date-picker>
              </el-form-item>
            </el-col>
            <!-- 结算币别 -->
            <el-col :span="6">
              <el-form-item label="结算币别" prop="fsettlecurrid">
                <el-select v-model="form.fsettlecurrid" placeholder="请选择结算币别" style="width: 100%">
                  <el-option v-for="item in CurrencyId" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 跟单员 -->
            <el-col :span="6">
              <el-form-item label="跟单员" prop="fscmjgdyname">
                <staff-search-select :useOrg="form.fsaleorgid" placeholder="请选择跟单员" :showLabel="false" :keyword.sync="form.fscmjgdyname" style="width: 100%" isBack @callBack="handleStaffSearchSelect($event)" />
              </el-form-item>
            </el-col>
            <el-col :span="24"></el-col>
            <!-- 发货组织 -->
            <el-col :span="6">
              <el-form-item label="发货组织" prop="fstockorgid">
                <el-select v-model="form.fstockorgid" filterable placeholder="请选择发货组织" style="width: 100%" @change="handleChangeStockOrg">
                  <el-option v-for="item in ApplicationOrgId" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 发货部门 -->
            <el-col :span="6">
              <el-form-item label="发货部门" prop="fdeliverydeptid">
                <el-select v-model="form.fdeliverydeptid" filterable placeholder="请选择发货部门" style="width: 100%" @change="handleChangeStockDept">
                  <el-option v-for="item in ApplicationDeptId.filter(ite => ite.FUseOrg == form.fstockorgid)" :key="item.FNumber" :label="item.FName" :value="item.FNumber"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 仓管员 -->
            <el-col :span="6">
              <el-form-item label="仓管员" prop="fstockerid">
                <el-select v-model="form.fstockerid" filterable placeholder="请选择仓管员" style="width: 100%" :disabled="!form.fstockorgid || !form.fdeliverydeptid">
                  <el-option v-for="item in calculateStockerList" :key="item.Number" :label="item.Name" :value="item.Number">
                    <span style="float: left">{{ item.Name }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.FDept }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24"></el-col>
            <!-- 销售组织 -->
            <el-col :span="6">
              <el-form-item label="销售组织" prop="fsaleorgid">
                <el-select v-model="form.fsaleorgid" filterable placeholder="请选择销售组织" style="width: 100%" @change="handleChangeSaleOrg">
                  <el-option v-for="item in ApplicationOrgId" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 销售部门 -->
            <el-col :span="6">
              <el-form-item label="销售部门" prop="fsaledeptid">
                <el-select v-model="form.fsaledeptid" filterable placeholder="请选择销售部门" style="width: 100%" @change="handleChangeDept">
                  <el-option v-for="item in ApplicationDeptId.filter(ite => ite.FUseOrg == form.fsaleorgid)" :key="item.FNumber" :label="item.FName" :value="item.FNumber"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 销售员 -->
            <el-col :span="6">
              <el-form-item label="销售员" prop="fsalesmanid">
                <el-select v-model="form.fsalesmanid" filterable placeholder="请选择销售员" :disabled="!form.fsaleorgid || !form.fsaledeptid" style="width: 100%">
                  <el-option v-for="item in calculateApplicantList" :key="item.Number" :label="item.Name" :value="item.Number">
                    <span style="float: left">{{ item.Name }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.FDept }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 销售订单号 -->
            <el-col :span="6">
              <el-form-item label="销售订单号" prop="fscmjxsddh">
                <el-input v-model="form.fscmjxsddh" readonly placeholder="请输入销售订单号" style="width: 100%"></el-input>
              </el-form-item>
            </el-col>
            <!-- 客户 -->
            <el-col :span="6">
              <el-form-item label="客户" prop="fcustomername">
                <customer-search-select :useOrg="form.fsaleorgid" :showLabel="false" :keyword.sync="form.fcustomername" style="width: 100%" isBack @callBack="handleFormCustomer($event)" />
              </el-form-item>
            </el-col>
            <!-- 备注 -->
            <el-col :span="12">
              <el-form-item label="备注" prop="fnote">
                <el-input v-model="form.fnote" placeholder="请输入备注" style="width: 100%"></el-input>
              </el-form-item>
            </el-col>
            <!-- 明细信息 -->
            <el-col :span="24">
              <el-table :data="form.entities" style="width: 100%" stripe class="custom-table custom-table-cell0" show-summary :summary-method="getSummary">
                <!-- 序号 -->
                <el-table-column type="index" label="序号" width="50" align="center"></el-table-column>
                <!-- 物料编码 -->
                <el-table-column label="物料编码" align="center" show-overflow-tooltip>
                  <template slot-scope="{ row }">
                    <span class="table-link" @click="handleMaterialNumber(row.fmaterialid)">{{ row.fmaterialid }}</span>
                  </template>
                </el-table-column>
                <!-- 物料名称 -->
                <el-table-column label="物料名称" align="center" show-overflow-tooltip>
                  <template slot-scope="{ row }">{{ row.fmaterialname }}</template>
                </el-table-column>
                <!-- 规格型号 -->
                <el-table-column label="规格型号" align="center" show-overflow-tooltip>
                  <template slot-scope="{ row }">{{ row.fmaterialmodel }}</template>
                </el-table-column>
                <!-- 库存单位 -->
                <el-table-column prop="funitid" label="库存单位" align="center" min-width="80">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.funitid`" :rules="rules.funitid">
                      <el-select v-model="scope.row.funitid" placeholder="请选择库存单位" style="width: 100%" size="small">
                        <el-option v-for="item in UnitList" :key="item.FNumber" :label="item.FName" :value="item.FNumber"></el-option>
                      </el-select>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 应发数量 -->
                <el-table-column prop="fmustqty" label="应发数量" align="center"></el-table-column>
                <!-- 实发数量 -->
                <el-table-column prop="frealqty" label="实发数量" align="center" min-width="100">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.frealqty`" :rules="rules.frealqty">
                      <el-input v-model="scope.row.frealqty" placeholder="请输入实发数量" style="width: 100%" @change="handleQtyChange(scope.row)" size="small"></el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 单价 -->
                <el-table-column prop="fprice" label="单价" align="center" min-width="100">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.fprice`" :rules="rules.fprice">
                      <el-input v-model="scope.row.fprice" placeholder="请输入单价" style="width: 100%" size="small" @change="handlePriceChange(scope.row)"></el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 含税单价 -->
                <el-table-column prop="ftaxprice" label="含税单价" align="center">
                  <template slot-scope="scope">
                    <span>{{ scope.row.ftaxprice || scope.row.fprice }}</span>
                  </template>
                </el-table-column>
                <!-- 是否赠品 -->
                <el-table-column prop="fisfree" label="是否赠品" align="center" width="70">
                  <template slot-scope="scope">
                    <el-checkbox v-model="scope.row.fisfree"></el-checkbox>
                  </template>
                </el-table-column>
                <!-- 批号 -->
                <el-table-column prop="flot" label="批号" align="center" min-width="100">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.flot`" :rules="rules.flot">
                      <el-input v-model="scope.row.flot" placeholder="请输入批号" style="width: 100%" size="small"></el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 金额 -->
                <el-table-column prop="famount" label="金额" align="center" min-width="100">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.famount`" :rules="rules.famount">
                      <el-input v-model="scope.row.famount" placeholder="请输入金额" style="width: 100%" size="small" @change="handleAmountChange(scope.row)"></el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 仓库 -->
                <el-table-column prop="fstockname" label="仓库" align="center" min-width="130">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.fstockname`" :rules="rules.fstockname">
                      <stock-search-select size="small" :useOrg="form.fstockorgid" :showLabel="false" :keyword.sync="scope.row.fstockname" style="width: 100%" @callBack="handleStockSelect($event, scope.row)" />
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 仓位 -->
                <!-- <el-table-column prop="fstocklocid" label="仓位" align="center" show-overflow-tooltip>
                  <template slot-scope="scope">{{ scope.row.fstocklocid && getString(scope.row.fstocklocid.Name) }}</template>
                </el-table-column> -->
                <!-- 库存状态 -->
                <el-table-column prop="fstockstatusid" label="库存状态" align="center" min-width="100">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.fstockstatusid`" :rules="rules.fstockstatusid">
                      <el-select v-model="scope.row.fstockstatusid" placeholder="请选择库存状态" style="width: 100%" size="small">
                        <el-option v-for="item in stockStatusOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                      </el-select>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 备注 -->
                <el-table-column prop="fentrynote" label="备注" align="center" min-width="100">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.fentrynote`" :rules="rules.fentrynote">
                      <el-input v-model="scope.row.fnote" placeholder="请输入备注" style="width: 100%" size="small"></el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 销售基本数量 -->
                <el-table-column prop="fsalbaseqty" label="销售基本数量" align="center" min-width="100"></el-table-column>
                <!-- 结算单价 -->
                <el-table-column prop="fscmjjsprice" label="结算单价" align="center" min-width="100">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.fscmjjsprice`" :rules="rules.fscmjjsprice">
                      <el-input v-model="scope.row.fscmjjsprice" placeholder="请输入结算单价" style="width: 100%" @change="handleScmjJspriceChange(scope.row)" size="small"></el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 结算金额 -->
                <el-table-column prop="fscmjjsamount" label="结算金额" align="center">
                  <template slot-scope="scope">
                    <span>{{ scope.row.fscmjjsamount }}</span>
                  </template>
                </el-table-column>
                <!-- 操作 -->
                <el-table-column label="操作" width="80" align="center">
                  <template slot-scope="scope">
                    <el-button type="text" :disabled="form.entities.length === 1" icon="el-icon-delete" @click="handleDelete(scope.$index)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn" @click="handleClose()">取消</el-button>
        <el-button class="custom-dialog-btn primary" :loading="loading" @click="handleSubmit">确定</el-button>
      </div>
    </el-dialog>
    <!-- 销售出库单详情 -->
    <sales-out-detail ref="salesOutDetail" />
  </div>
</template>
<script>
import { kingdee } from '@/minix'
import { isNumber, isNumberLength } from '@/utils/validate'
import CustomerSearchSelect from '@/components/SearchSelect/customer'
import StockSearchSelect from '@/components/SearchSelect/stock'
import StaffSearchSelect from '@/components/SearchSelect/staff'
import { getSalesOutDetail2, createSalesOut, deleteSalesOutV2 } from '@/api/kingdee/sell/salesOut'
import { getSalesmanList } from '@/api/kingdee'
import SalesOutDetail from '@/views/kingdee/sell/salesOut/detail'

export default {
  name: 'SalesOutCreate',
  mixins: [kingdee],
  components: { CustomerSearchSelect, StockSearchSelect, StaffSearchSelect, SalesOutDetail },
  data() {
    return {
      fid: undefined,
      form: {},
      rules: {
        fbilltypeid: [{ required: true, message: '请选择单据类型', trigger: 'change' }],
        fdate: [{ required: true, message: '请选择日期', trigger: 'change' }],
        fsettlecurrid: [{ required: true, message: '请选择结算币别', trigger: 'change' }],
        fstockorgid: [{ required: true, message: '请选择发货组织', trigger: 'change' }],
        fsaleorgid: [{ required: true, message: '请选择销售组织', trigger: 'change' }],
        fsaledeptid: [{ required: true, message: '请选择销售部门', trigger: 'change' }],
        fsalesmanid: [{ required: true, message: '请选择销售员', trigger: 'change' }],
        fcustomername: [{ required: true, message: '请选择客户', trigger: 'change' }],
        funitid: [{ required: true, message: '请选择库存单位', trigger: 'change' }],
        fprice: [
          { required: true, message: '请输入单价', trigger: 'change' },
          { validator: isNumber, message: '请输入正确的单价', trigger: ['change', 'blur'] },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: ['change', 'blur'] }
        ],
        frealqty: [
          { required: true, message: '请输入实发数量', trigger: 'change' },
          { validator: isNumber, message: '请输入正确的数量', trigger: ['change', 'blur'] },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: ['change', 'blur'] }
        ],
        famount: [
          { required: true, message: '请输入金额', trigger: 'change' },
          { validator: isNumber, message: '请输入正确的金额', trigger: ['change', 'blur'] },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: ['change', 'blur'] }
        ],
        fscmjjsprice: [
          { required: true, message: '请输入结算单价', trigger: ['change', 'blur'] },
          { validator: isNumber, message: '请输入正确的数量', trigger: ['change', 'blur'] },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: ['change', 'blur'] }
        ],
        flot: [{ required: true, message: '请输入批号', trigger: 'change' }],
        fstockname: [{ required: true, message: '请选择仓库', trigger: 'change' }],
        fstockstatusid: [{ required: true, message: '请选择库存状态', trigger: 'change' }],
        fscmjjsprice: [
          { required: true, message: '请输入结算单价', trigger: ['change', 'blur'] },
          { validator: isNumber, message: '请输入正确的数量', trigger: ['change', 'blur'] },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: ['change', 'blur'] }
        ]
      },
      title: '新增销售出库单',
      open: false,
      loading: false,
      billTypeOptions: [
        { value: 'XSCKD01_SYS', label: '标准销售出库单' },
        { value: 'XSCKD02_SYS', label: '寄售出库单' },
        { value: 'XSCKD03_SYS', label: '零售出库单' },
        { value: 'XSCKD04_SYS', label: '分销购销出库单' },
        { value: 'XSCKD05_SYS', label: 'VMI出库单' },
        { value: 'XSCKD06_SYS', label: '现销出库单' },
        { value: 'XSCKD07_SYS', label: 'B2C销售出库单' },
        { value: 'XSCKD08_SYS', label: '直运销售出库单' }
      ],
      // 库存状态
      stockStatusOptions: [
        { value: 'KCZT01_SYS', label: '可用' },
        { value: 'KCZT02_SYS', label: '待检' },
        { value: 'KCZT03_SYS', label: '冻结' },
        { value: 'KCZT04_SYS', label: '退回冻结' },
        { value: 'KCZT07_SYS', label: '废品' },
        { value: 'KCZT08_SYS', label: '不良' },
        { value: 'KCZT09_SYS', label: '已包装' },
        { value: 'KCZT099_SYS', label: '外借' }
      ],
      // 销售员列表
      applicantList: [],
      // 仓管员列表
      stockerList: [],
      isPush: false,
      hasSuccessfully: false // 是否已成功提交
    }
  },
  computed: {
    // 销售员列表
    calculateApplicantList() {
      const dept = this.ApplicationDeptId.find(ite => ite.FNumber == this.form.fsaledeptid) || {}
      const deptName = dept.FName || ''
      return this.applicantList.filter(ite => ite.FDept == deptName)
    },
    // 仓管员列表
    calculateStockerList() {
      const dept = this.ApplicationDeptId.find(ite => ite.FNumber == this.form.fdeliverydeptid) || {}
      const deptName = dept.FName || ''
      const list = this.stockerList.filter(ite => ite.FDept == deptName)
      // 处理仓管员编号，只取前三位数字
      const processedList = list.map(item => ({
        ...item,
        Number: item.Number ? item.Number.split('_')[0] : item.Number
      }))
      return processedList
    }
  },
  methods: {
    // 销售员远程方法
    ApplicantRemoteMethod() {
      const params = { bizOrg: this.form.fsaleorgid, OperatorType: 'XSY' }
      getSalesmanList(params).then(res => {
        const { code, msg, data } = res
        const { data: applicantList } = data
        if (code == 200) {
          this.applicantList = applicantList
        } else this.$message.error(msg)
      })
    },
    // 仓管员远程方法
    StockerRemoteMethod() {
      const params = { bizOrg: this.form.fstockorgid, OperatorType: 'WHY' }
      getSalesmanList(params).then(res => {
        const { code, msg, data } = res
        const { data: stockerList } = data
        if (code == 200) {
          this.stockerList = stockerList
        } else this.$message.error(msg)
      })
    },
    // 改变发货组织
    handleChangeStockOrg() {
      this.form.fdeliverydeptid = undefined // 清空发货部门
      this.form.fstockerid = undefined // 清空仓管员
      this.StockerRemoteMethod()
    },
    // 改变发货部门
    handleChangeStockDept() {
      this.form.fstockerid = undefined // 清空仓管员
    },
    // 改变销售组织
    handleChangeSaleOrg() {
      this.form.fsaledeptid = undefined // 清空销售部门
      this.form.fsalesmanid = undefined // 清空销售员
      this.ApplicantRemoteMethod()
    },
    // 改变销售部门
    handleChangeDept() {
      this.form.fsalesmanid = undefined // 清空销售员
    },
    // 选择仓库
    handleStockSelect(stock, row) {
      this.$set(row, 'fstockid', stock.Number)
      this.$set(row, 'fstockname', stock.Name)
    },
    // 客户
    handleFormCustomer(data) {
      this.$set(this.form, 'fcustomerid', data.id)
      this.$set(this.form, 'fcustomername', data.name)
    },
    // 跟单员
    handleStaffSearchSelect(data) {
      this.$set(this.form, 'fscmjgdy', data.FNumber)
      this.$set(this.form, 'fscmjgdyname', data.FName)
    },
    // 关闭
    beforeClose() {
      this.handleClose()
    },
    // 关闭
    async handleClose(flag = false) {
      if (this.fid && !this.isCreate && !this.hasSuccessfully) {
        try {
          await deleteSalesOutV2({ id: this.fid })
          this.fid = undefined
          this.$nextTick(() => {
            this.open = false
            this.$emit('callBack', flag)
          })
        } catch (error) {
          this.$message.error(error.message)
        }
      } else {
        this.open = false
        this.$emit('callBack', flag)
      }
    },
    // 表单初始化
    reset() {
      this.form = {
        entities: [],
        fbilltypeid: undefined,
        fcarriageno: undefined,
        fcarrierid: undefined,
        fcustomerid: undefined,
        fdate: undefined,
        fdeliverydeptid: undefined,
        fheadlocationid: undefined,
        fid: undefined,
        fnote: undefined,
        fsaledeptid: undefined,
        fsaleorgid: undefined,
        fsalesmanid: undefined,
        fscmjgdy: undefined,
        fscmjgdyname: undefined,
        fscmjsj: undefined,
        fscmjxsddh: undefined,
        fsettlecurrid: undefined,
        fstockerid: undefined,
        fstockorgid: undefined,
        traceDetails: []
      }
      this.resetForm('form')
      this.isPush = false
      this.hasSuccessfully = false
    },
    // 初始化
    // prettier-ignore
    initPush(fid, type = undefined) {
      if (!fid) return this.$message.warning('参数错误，请刷新页面重试')
      const loading = this.$loading({
        lock: true,
        text: '加载中...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      this.reset()
      this.isPush = type == 'push'
      getSalesOutDetail2({ fid }).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          const info = data?.result?.result || {}
          const detailEntities = info?.SAL_OUTSTOCKENTRY || []
          const entities = detailEntities.map(item => {
            return {
              fentryid: item?.Id || undefined, // 使用时需检查
              fmaterialid: item?.MaterialID?.Number || undefined, // 物料编码
              fmaterialname: (item?.MaterialID?.Name && this.getString(item?.MaterialID?.Name)) || undefined, // 物料名称
              fmaterialmodel: (item?.MaterialID?.Specification && this.getString(item?.MaterialID?.Specification)) || undefined, // 规格型号
              funitid: item?.UnitID?.Number || undefined, // 库存单位
              fmustqty: item?.MustQty || undefined, // 应发数量
              frealqty: item?.RealQty || undefined, // 实发数量
              fprice: item?.Price || undefined, // 单价
              ftaxprice: item?.TaxPrice || undefined, // 含税单价
              fisfree: item?.IsFree || undefined, // 是否赠品
              flot: item?.Lot?.Number || undefined, // 批号
              famount: item?.Amount || undefined, // 金额
              fstockname: item?.StockID?.Name || undefined, // 仓库名称
              fstockid: item?.StockID?.Number || undefined, // 仓库
              fstockstatusid: item?.StockStatusID?.Number || undefined, // 库存状态
              fentrynote: item?.Note || undefined, // 备注
              fsalbaseqty: item?.SALBASEQTY || undefined, // 销售基本数量
              fscmjjsprice: item?.F_SCMJ_JSPrice || undefined, // 结算单价
              fscmjjsamount: item?.F_SCMJ_JSAmount ? parseFloat(item.F_SCMJ_JSAmount) : item?.F_SCMJ_JSPrice && item?.RealQty ? parseFloat((parseFloat(item.F_SCMJ_JSPrice) * parseFloat(item.RealQty)).toFixed(5)) : undefined // 结算金额
            }
          })
          const fcurrencyidArr = info?.SAL_OUTSTOCKFIN || []
          const fsettlecurrid = fcurrencyidArr.length > 0 ? fcurrencyidArr[0]?.SettleCurrID?.Number : undefined
          this.form = {
            entities, // 明细信息
            fbilltypeid: info?.BillTypeID?.Number || undefined, // 单据类型
            fdate: info?.Date || undefined, // 单据日期
            fsettlecurrid, // 结算币别
            fscmjgdy: info?.F_SCMJ_GDY?.Number || undefined, // 跟单员
            fscmjgdyname: (info?.F_SCMJ_GDY?.Name && this.getString(info?.F_SCMJ_GDY?.Name)) || undefined, // 跟单员名称
            fstockorgid: info?.StockOrgId?.Number || undefined, // 发货组织
            fstockdeptid: info?.DeliveryDeptID?.Number || undefined, // 发货部门
            fstockerid: info?.StockerID?.Number || undefined, // 仓管员
            fsaleorgid: info?.SaleOrgId?.Number || undefined, // 销售组织
            fsaledeptid: info?.SaleDeptID?.Number || undefined, // 销售部门
            fsalesmanid: info?.SalesManID?.Number || undefined, // 销售员
            fscmjxsddh: info?.F_SCMJ_XSDDH || undefined, // 销售订单号
            fcustomername: (info?.CustomerID?.Name && this.getString(info?.CustomerID?.Name)) || undefined, // 客户名称
            fcustomerid: info?.CustomerID?.Number || undefined, // 客户
            fnote: info?.Note || undefined, // 备注
            fid // 单据ID
          }
          this.fid = fid
          this.title = '新增销售出库单'
          this.$nextTick(() => {
            this.ApplicantRemoteMethod()
            this.StockerRemoteMethod()
          })
          this.open = true
        } else this.$message.error(msg)
      }).finally(() => {
        loading.close()
      })
    },
    // 改变实退数量计算结算金额
    handleQtyChange(row) {
      const qty = parseFloat(row?.frealqty) || 0
      const price = parseFloat(row?.fscmjjsprice) || 0
      this.$set(row, 'fscmjjsamount', qty && price ? parseFloat((price * qty).toFixed(5)) : undefined)
    },
    // 改变结算单价计算结算金额
    handleScmjJspriceChange(row) {
      const qty = parseFloat(row?.frealqty) || 0
      const price = parseFloat(row?.fscmjjsprice) || 0
      this.$set(row, 'fscmjjsamount', qty && price ? parseFloat((price * qty).toFixed(5)) : undefined)
    },
    // 改变单价计算金额
    handlePriceChange(row) {
      const price = parseFloat(row?.fprice) || 0
      const qty = parseFloat(row?.frealqty) || 0
      this.$set(row, 'ftaxprice', price)
      this.$set(row, 'famount', price && qty ? parseFloat((price * qty).toFixed(5)) : undefined)
    },
    // 改变金额计算单价
    handleAmountChange(row) {
      const amount = parseFloat(row?.famount) || 0
      const qty = parseFloat(row?.frealqty) || 0
      this.$set(row, 'fprice', amount && qty ? parseFloat((amount / qty).toFixed(5)) : undefined)
    },
    // 获取合计
    getSummary(param) {
      const { columns, data } = param
      const result = columns.map((column, index) => {
        if (index === 0) {
          return '合计'
        }
        if (column.property === 'fmustqty' || column.property === 'frealqty' || column.property === 'fscmjjsamount' || column.property === 'famount' || column.property === 'fsalbaseqty') {
          return `${this.calculateTotal(data, column.property)}`
        }
        return ''
      })
      return result
    },
    // 计算合计
    calculateTotal(data, key) {
      return data.reduce((total, item) => {
        if (key === 'fmustqty') {
          const value = parseFloat(item.fmustqty) || 0
          return parseFloat((total + value).toFixed(5))
        } else if (key === 'frealqty') {
          const value = parseFloat(item.frealqty) || 0
          return parseFloat((total + value).toFixed(5))
        } else if (key === 'fscmjjsamount') {
          const value = parseFloat(item.fscmjjsamount) || 0
          return parseFloat((total + value).toFixed(5))
        } else if (key === 'famount') {
          const value = parseFloat(item.famount) || 0
          return parseFloat((total + value).toFixed(5))
        } else if (key === 'fsalbaseqty') {
          const value = parseFloat(item.fsalbaseqty) || 0
          return parseFloat((total + value).toFixed(5))
        }
        return total
      }, 0)
    },
    // 删除
    handleDelete(index) {
      this.form.entities.splice(index, 1)
    },
    // 提交
    // prettier-ignore
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true
          const formData = JSON.parse(JSON.stringify(this.form))
          formData.entities = formData.entities.map(entity => ({
            ...entity,
            ftaxprice: entity.ftaxprice || entity.fprice
          }))          
          createSalesOut(formData).then(res => {
            const { code, msg } = res
            if (code === 200) {
              this.$message.success('操作成功')
              this.hasSuccessfully = true
              if (this.isPush) {
                const { number } = res.data
                if (number) {
                  this.open = false
                  this.$nextTick(() => {
                    this.$refs.salesOutDetail.getInfo({ BillNo: number })
                  })
                } else {
                  this.open = false
                  this.$emit('callBack', true)
                }
              } else {
                this.open = false
                this.$emit('callBack', true)
              }
            } else this.$message.error(msg)
          }).finally(() => {
            this.loading = false
          })
        }
      })
    },
    // 物料详情
    handleMaterialNumber(number) {
      if (number) this.$parent.handleMaterialNumber(number)
    }
  }
}
</script>
<style scoped lang="scss">
@import '@/assets/styles/custom-new.scss';
::v-deep {
  .custom-table {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    border-bottom: 0;
    margin-bottom: 20px;
    .el-form-item {
      margin-top: 10px !important;
      margin-bottom: 10px !important;
      .el-form-item__error {
        top: 95%;
        padding-top: 0;
      }
    }
    .el-input--prefix .el-input__inner {
      padding-left: 20px;
      padding-right: 20px;
    }
    .el-input--prefix.el-date-editor--date .el-input__inner {
      padding-left: 30px;
    }
    .el-date-editor--datetime .el-input__inner {
      padding-left: 30px;
      padding-right: 30px;
    }
  }
}
</style>
