<template>
  <div>
    <el-dialog v-dialogDragBox title="销售出库单详情" :visible.sync="open" width="90%" class="custom-dialog" :before-close="beforeClose">
      <div style="padding: 0 20px">
        <div class="kindeeButton" :key="info.DocumentStatus">
          <template v-for="(btn, index) in buttons">
            <el-button :key="index" :type="btn.type" size="medium" v-if="checkStatus(btn.status)" @click="handleKingdeeDo(btn.action)">{{ btn.text }}</el-button>
            <el-button :key="'disabled-' + index" :type="btn.type" size="medium" disabled v-else>{{ btn.text }}</el-button>
          </template>
        </div>
        <el-tabs v-model="activeName" type="card" @tab-click="handleClick" class="custom-tabs">
          <el-tab-pane label="基本信息" name="first"></el-tab-pane>
          <el-tab-pane label="客户信息" name="second"></el-tab-pane>
          <el-tab-pane label="财务信息" name="third"></el-tab-pane>
          <el-tab-pane label="物流跟踪" name="fourth"></el-tab-pane>
          <el-tab-pane label="其他" name="fifth"></el-tab-pane>
        </el-tabs>
        <el-descriptions border :column="4" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 4 - 7em - 22px)' }" v-show="activeName === 'first'">
          <!-- 单据类型 -->
          <el-descriptions-item label="单据类型">{{ info.BillTypeID && getString(info.BillTypeID.Name) }}</el-descriptions-item>
          <!-- 单据编号 -->
          <el-descriptions-item label="单据编号">{{ info.BillNo }}</el-descriptions-item>
          <!-- 日期 -->
          <el-descriptions-item label="日期">{{ info.Date && parseTime(info.Date, '{y}-{m}-{d}') }}</el-descriptions-item>
          <!-- 结算币别 -->
          <el-descriptions-item label="结算币别">{{ info.SAL_OUTSTOCKFIN && info.SAL_OUTSTOCKFIN[0] && info.SAL_OUTSTOCKFIN[0].SettleCurrID && getString(info.SAL_OUTSTOCKFIN[0].SettleCurrID.Name) }}</el-descriptions-item>
          <!-- 单据状态 -->
          <el-descriptions-item label="单据状态">{{ info.DocumentStatus && getDocumentStatusLabel(info.DocumentStatus) }}</el-descriptions-item>
          <!-- 客户 -->
          <el-descriptions-item label="客户">{{ info.CustomerID && getString(info.CustomerID.Name) }}</el-descriptions-item>
          <!-- 交货地点 -->
          <el-descriptions-item label="交货地点">{{ info.HeadLocationId && getString(info.HeadLocationId.Name) }}</el-descriptions-item>
          <!-- 承运商 -->
          <el-descriptions-item label="承运商">{{ info.CarrierID && getString(info.CarrierID.Name) }}</el-descriptions-item>
          <!-- 运输单号 -->
          <el-descriptions-item label="运输单号">{{ info.CarriageNO }}</el-descriptions-item>
          <!-- 跟单员 -->
          <el-descriptions-item label="跟单员">{{ info.F_SCMJ_GDY && getString(info.F_SCMJ_GDY.Name) }}</el-descriptions-item>
          <!-- 发货组织 -->
          <el-descriptions-item label="发货组织">{{ info.StockOrgId && getString(info.StockOrgId.Name) }}</el-descriptions-item>
          <!-- 发货部门 -->
          <el-descriptions-item label="发货部门">{{ info.DeliveryDeptID && getString(info.DeliveryDeptID.Name) }}</el-descriptions-item>
          <!-- 仓管员 -->
          <el-descriptions-item label="仓管员">{{ info.StockerID && getString(info.StockerID.Name) }}</el-descriptions-item>
          <!-- 备注 -->
          <el-descriptions-item label="备注">{{ info.Note }}</el-descriptions-item>
          <!-- 销售组织 -->
          <el-descriptions-item label="销售组织">{{ info.SaleOrgId && getString(info.SaleOrgId.Name) }}</el-descriptions-item>
          <!-- 销售部门 -->
          <el-descriptions-item label="销售部门">{{ info.SaleDeptID && getString(info.SaleDeptID.Name) }}</el-descriptions-item>
          <!-- 销售员 -->
          <el-descriptions-item label="销售员">{{ info.SalesManID && getString(info.SalesManID.Name) }}</el-descriptions-item>
          <!-- 销售订单号 -->
          <el-descriptions-item label="销售订单号">{{ info.F_SCMJ_XSDDH }}</el-descriptions-item>
        </el-descriptions>
        <el-descriptions border :column="2" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 2 - 7em - 22px)' }" v-show="activeName === 'second'">
          <!-- 收货方 -->
          <el-descriptions-item label="收货方">{{ info.ReceiverID && getString(info.ReceiverID.Name) }}</el-descriptions-item>
          <!-- 收货方联系人 -->
          <el-descriptions-item label="收货方联系人">{{ info.ReceiverContactID && getString(info.ReceiverContactID.Name) }}</el-descriptions-item>
          <!-- 收货方地址 -->
          <el-descriptions-item label="收货方地址" :span="2">{{ info.ReceiveAddress }}</el-descriptions-item>
          <!-- 收货人姓名 -->
          <el-descriptions-item label="收货人姓名">{{ info.FLinkMan }}</el-descriptions-item>
          <!-- 联系电话 -->
          <el-descriptions-item label="联系电话">{{ info.FLinkPhone }}</el-descriptions-item>
          <!-- 结算方 -->
          <el-descriptions-item label="结算方">{{ info.SettleID && getString(info.SettleID.Name) }}</el-descriptions-item>
          <!-- 付款方 -->
          <el-descriptions-item label="付款方">{{ info.PayerID && getString(info.PayerID.Name) }}</el-descriptions-item>
        </el-descriptions>
        <el-descriptions border :column="4" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 4 - 7em - 22px)' }" v-show="activeName === 'third'">
          <!-- 结算组织 -->
          <el-descriptions-item label="结算组织">{{ info.SAL_OUTSTOCKFIN && info.SAL_OUTSTOCKFIN[0] && info.SAL_OUTSTOCKFIN[0].SettleOrgID && getString(info.SAL_OUTSTOCKFIN[0].SettleOrgID.Name) }}</el-descriptions-item>
          <!-- 结算币别 -->
          <el-descriptions-item label="结算币别">{{ info.SAL_OUTSTOCKFIN && info.SAL_OUTSTOCKFIN[0] && info.SAL_OUTSTOCKFIN[0].SettleCurrID && getString(info.SAL_OUTSTOCKFIN[0].SettleCurrID.Name) }}</el-descriptions-item>
          <!-- 结算方式 -->
          <el-descriptions-item label="结算方式">{{ info.SAL_OUTSTOCKFIN && info.SAL_OUTSTOCKFIN[0] && info.SAL_OUTSTOCKFIN[0].SettleTypeID && getString(info.SAL_OUTSTOCKFIN[0].SettleTypeID.Name) }}</el-descriptions-item>
          <!-- 收款条件 -->
          <el-descriptions-item label="收款条件">{{ info.SAL_OUTSTOCKFIN && info.SAL_OUTSTOCKFIN[0] && info.SAL_OUTSTOCKFIN[0].ReceiptConditionID && getString(info.SAL_OUTSTOCKFIN[0].ReceiptConditionID.Name) }}</el-descriptions-item>
          <!-- 本位币 -->
          <el-descriptions-item label="本位币">{{ info.SAL_OUTSTOCKFIN && info.SAL_OUTSTOCKFIN[0] && info.SAL_OUTSTOCKFIN[0].SettleCurrID && getString(info.SAL_OUTSTOCKFIN[0].SettleCurrID.Name) }}</el-descriptions-item>
          <!-- 汇率类型 -->
          <el-descriptions-item label="汇率类型">{{ info.SAL_OUTSTOCKFIN && info.SAL_OUTSTOCKFIN[0] && info.SAL_OUTSTOCKFIN[0].ExchangeTypeID && getString(info.SAL_OUTSTOCKFIN[0].ExchangeTypeID.Name) }}</el-descriptions-item>
          <!-- 汇率 -->
          <el-descriptions-item label="汇率">{{ info.SAL_OUTSTOCKFIN && info.SAL_OUTSTOCKFIN[0] && info.SAL_OUTSTOCKFIN[0].ExchangeRate }}</el-descriptions-item>
          <!-- 整单折扣额 -->
          <el-descriptions-item label="整单折扣额">{{ (info.SAL_OUTSTOCKFIN && info.SAL_OUTSTOCKFIN[0] && info.SAL_OUTSTOCKFIN[0].AllDisCount) || '' }}</el-descriptions-item>
        </el-descriptions>
        <el-table ref="logisticsTable" :data="info.OutStockTrace" class="custom-table" highlight-current-row v-show="activeName === 'fourth'">
          <!-- 物流公司 -->
          <el-table-column label="物流公司" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.LogComId && getString(scope.row.LogComId.Name) }}</template>
          </el-table-column>
          <!-- 物流单号 -->
          <el-table-column label="物流单号" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.CarryBillNo }}</template>
          </el-table-column>
          <!-- 寄件人手机号码 -->
          <el-table-column label="寄件人手机号码" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.PhoneNumber }}</template>
          </el-table-column>
          <!-- 起始地点 -->
          <el-table-column label="起始地点" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.From }}</template>
          </el-table-column>
          <!-- 终止地点 -->
          <el-table-column label="终止地点" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.To }}</template>
          </el-table-column>
          <!-- 发货时间 -->
          <el-table-column label="发货时间" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.DelTime && parseTime(scope.row.DelTime, '{y}-{m}-{d}') }}</template>
          </el-table-column>
          <!-- 物流状态 -->
          <el-table-column label="物流状态" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.TraceStatus }}</template>
          </el-table-column>
          <!-- 签收时间 -->
          <el-table-column label="签收时间" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.ReceiptTime && parseTime(scope.row.ReceiptTime, '{y}-{m}-{d}') }}</template>
          </el-table-column>
          <!-- 类型 -->
          <el-table-column label="类型" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.CarryBillNoType }}</template>
          </el-table-column>
        </el-table>
        <el-descriptions border :column="3" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 3 - 7em - 22px)' }" v-show="activeName === 'fifth'">
          <!-- 创建人 -->
          <el-descriptions-item label="创建人">{{ info.FCreatorId && info.FCreatorId.Name }}</el-descriptions-item>
          <!-- 创建日期 -->
          <el-descriptions-item label="创建日期">{{ info.FCreateDate && parseTime(info.FCreateDate, '{y}-{m}-{d}') }}</el-descriptions-item>
          <!-- 最后修改人 -->
          <el-descriptions-item label="最后修改人">{{ info.FModifierId && info.FModifierId.Name }}</el-descriptions-item>
          <!-- 最后修改日期 -->
          <el-descriptions-item label="最后修改日期">{{ info.FModifyDate && parseTime(info.FModifyDate, '{y}-{m}-{d}') }}</el-descriptions-item>
          <!-- 审核人 -->
          <el-descriptions-item label="审核人">{{ info.ApproverID && info.ApproverID.Name }}</el-descriptions-item>
          <!-- 审核日期 -->
          <el-descriptions-item label="审核日期">{{ info.ApproveDate && parseTime(info.ApproveDate, '{y}-{m}-{d}') }}</el-descriptions-item>
          <!-- 作废状态 -->
          <el-descriptions-item label="作废状态">{{ info.CancelStatus && getOptionLabel(CancelStatusOptions, info.CancelStatus) }}</el-descriptions-item>
          <!-- 作废人 -->
          <el-descriptions-item label="作废人">{{ info.CancellerID && info.CancellerID.Name }}</el-descriptions-item>
          <!-- 作废日期 -->
          <el-descriptions-item label="作废日期">{{ info.CancelDate && parseTime(info.CancelDate, '{y}-{m}-{d}') }}</el-descriptions-item>
          <!-- 关联应收状态 -->
          <el-descriptions-item label="关联应收状态">{{ info.ARStatus && getOptionLabel(ARStatusOptions, info.ARStatus) }}</el-descriptions-item>
        </el-descriptions>
        <!-- 明细信息 -->
        <el-tabs v-model="detailActiveName" type="card" @tab-click="handleDetailClick" class="custom-tabs">
          <el-tab-pane label="明细信息" name="Dfirst"></el-tab-pane>
          <el-tab-pane label="物料数据" name="Dsecond"></el-tab-pane>
          <el-tab-pane label="其他信息" name="Dthird"></el-tab-pane>
        </el-tabs>
        <el-table ref="detailTable" :data="info.SAL_OUTSTOCKENTRY" class="custom-table" highlight-current-row @current-change="handleCurrentChange" :setCurrentRow="setCurrentRow" show-summary :summary-method="getSummary" v-show="detailActiveName === 'Dfirst'">
          <!-- 序号 -->
          <el-table-column type="index" label="序号" align="center"></el-table-column>
          <!-- 物料编码 -->
          <el-table-column label="物料编码" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.MaterialID && scope.row.MaterialID.Number }}</template>
          </el-table-column>
          <!-- 物料名称 -->
          <el-table-column label="物料名称" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.MaterialID && getString(scope.row.MaterialID.Name) }}</template>
          </el-table-column>
          <!-- 规格型号 -->
          <el-table-column label="规格型号" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.MaterialID && getString(scope.row.MaterialID.Specification) }}</template>
          </el-table-column>
          <!-- 库存单位 -->
          <el-table-column label="库存单位" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.UnitID && getString(scope.row.UnitID.Name) }}</template>
          </el-table-column>
          <!-- 应发数量 -->
          <el-table-column label="应发数量" prop="MustQty" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.MustQty }}</template>
          </el-table-column>
          <!-- 实发数量 -->
          <el-table-column label="实发数量" prop="RealQty" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.RealQty }}</template>
          </el-table-column>
          <!-- 单价 -->
          <el-table-column label="单价" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.Price }}</template>
          </el-table-column>
          <!-- 含税单价 -->
          <el-table-column label="含税单价" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.TaxPrice }}</template>
          </el-table-column>
          <!-- 是否赠品 -->
          <el-table-column label="是否赠品" align="center" width="80">
            <template slot-scope="scope">
              <el-checkbox v-model="scope.row.IsFree" disabled></el-checkbox>
            </template>
          </el-table-column>
          <!-- 批号 -->
          <el-table-column label="批号" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.Lot && scope.row.Lot.Number }}</template>
          </el-table-column>
          <!-- 基本单位 -->
          <el-table-column label="基本单位" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.BaseUnitID && getString(scope.row.BaseUnitID.Name) }}</template>
          </el-table-column>
          <!-- 金额 -->
          <el-table-column label="金额" prop="Amount" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.Amount }}</template>
          </el-table-column>
          <!-- 辅单位 -->
          <el-table-column label="辅单位" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.ExtAuxUnitId && getString(scope.row.ExtAuxUnitId.Name) }}</template>
          </el-table-column>
          <!-- 实发数量(辅单位) -->
          <el-table-column label="实发数量(辅单位)" prop="ExtAuxUnitQty" align="center" min-width="120" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.ExtAuxUnitQty }}</template>
          </el-table-column>
          <!-- 价税合计 -->
          <el-table-column label="价税合计" prop="AllAmount" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.AllAmount }}</template>
          </el-table-column>
          <!-- 仓库 -->
          <el-table-column label="仓库" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.StockID && getString(scope.row.StockID.Name) }}</template>
          </el-table-column>
          <!-- 库存状态 -->
          <el-table-column label="库存状态" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.StockStatusID && getString(scope.row.StockStatusID.Name) }}</template>
          </el-table-column>
          <!-- 备注 -->
          <el-table-column label="备注" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.Note }}</template>
          </el-table-column>
          <!-- 保管者类型 -->
          <!-- <el-table-column label="保管者类型" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.KeeperTypeID && getOptionLabel(KeeperTypeIDOptions, scope.row.KeeperTypeID) }}</template>
          </el-table-column> -->
          <!-- 保管者 -->
          <!-- <el-table-column label="保管者" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.KeeperID && getString(scope.row.KeeperID.Name) }}</template>
          </el-table-column> -->
          <!-- 销售基本数量 -->
          <el-table-column label="销售基本数量" prop="SALBASEQTY" align="center" min-width="100" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.SALBASEQTY }}</template>
          </el-table-column>
          <!-- 结算单价 -->
          <el-table-column label="结算单价" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.F_SCMJ_JSPrice }}</template>
          </el-table-column>
          <!-- 结算金额 -->
          <el-table-column label="结算金额" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.F_SCMJ_JSAmount }}</template>
          </el-table-column>
        </el-table>
        <el-descriptions border :column="3" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 3 - 7em - 22px)' }" v-show="detailActiveName === 'Dsecond'">
          <!-- 物料编码 -->
          <el-descriptions-item label="物料编码">{{ setCurrentRow && setCurrentRow.MaterialID && setCurrentRow.MaterialID.Number }}</el-descriptions-item>
          <!-- 物料名称 -->
          <el-descriptions-item label="物料名称">{{ setCurrentRow && setCurrentRow.MaterialID && getString(setCurrentRow.MaterialID.Name) }}</el-descriptions-item>
          <!-- 库存单位 -->
          <el-descriptions-item label="库存单位">{{ setCurrentRow && setCurrentRow.UnitID && getString(setCurrentRow.UnitID.Name) }}</el-descriptions-item>
          <!-- 实发数量 -->
          <el-descriptions-item label="实发数量">{{ setCurrentRow && setCurrentRow.RealQty }}</el-descriptions-item>
          <!-- 销售单位 -->
          <el-descriptions-item label="销售单位">{{ setCurrentRow && setCurrentRow.SalUnitId && getString(setCurrentRow.SalUnitId.Name) }}</el-descriptions-item>
          <!-- 销售数量 -->
          <el-descriptions-item label="销售数量">{{ setCurrentRow && setCurrentRow.SALUNITQTY }}</el-descriptions-item>
          <!-- 计价单位 -->
          <el-descriptions-item label="计价单位">{{ setCurrentRow && setCurrentRow.PriceUnitId && getString(setCurrentRow.PriceUnitId.Name) }}</el-descriptions-item>
          <!-- 计价数量 -->
          <el-descriptions-item label="计价数量">{{ setCurrentRow && setCurrentRow.PriceUnitQty }}</el-descriptions-item>
          <!-- 辅单位 -->
          <el-descriptions-item label="辅单位">{{ setCurrentRow && setCurrentRow.ExtAuxUnitId && getString(setCurrentRow.ExtAuxUnitId.Name) }}</el-descriptions-item>
          <!-- 数量(辅单位) -->
          <el-descriptions-item label="数量(辅单位)">{{ (setCurrentRow && setCurrentRow.ExtAuxUnitQty) || '' }}</el-descriptions-item>
          <!-- BOM版本 -->
          <el-descriptions-item label="BOM版本">{{ setCurrentRow && setCurrentRow.BomID && getString(setCurrentRow.BomID.Name) }}</el-descriptions-item>
          <!-- 辅助属性 -->
          <el-descriptions-item label="辅助属性">{{ setCurrentRow && setCurrentRow.AuxPropId && getString(setCurrentRow.AuxPropId.Name) }}</el-descriptions-item>
          <!-- 物料类别 -->
          <el-descriptions-item label="物料类别">{{ setCurrentRow && setCurrentRow.MaterialID && setCurrentRow.MaterialID.MaterialBase && setCurrentRow.MaterialID.MaterialBase[0] && setCurrentRow.MaterialID.MaterialBase[0].CategoryID && getString(setCurrentRow.MaterialID.MaterialBase[0].CategoryID.Name) }}</el-descriptions-item>
          <!-- 货主类型 -->
          <el-descriptions-item label="货主类型">{{ setCurrentRow && setCurrentRow.OwnerTypeID && getOptionLabel(OwnerTypeIDOptions, setCurrentRow.OwnerTypeID) }}</el-descriptions-item>
          <!-- 货主 -->
          <el-descriptions-item label="货主">{{ setCurrentRow && setCurrentRow.OwnerID && getString(setCurrentRow.OwnerID.Name) }}</el-descriptions-item>
          <!-- 保管者类型 -->
          <el-descriptions-item label="保管者类型">{{ setCurrentRow && setCurrentRow.KeeperTypeID && getOptionLabel(KeeperTypeIDOptions, setCurrentRow.KeeperTypeID) }}</el-descriptions-item>
          <!-- 保管者 -->
          <el-descriptions-item label="保管者">{{ setCurrentRow && setCurrentRow.KeeperID && getString(setCurrentRow.KeeperID.Name) }}</el-descriptions-item>
          <!-- VMI业务 -->
          <el-descriptions-item label="VMI业务">
            <el-checkbox v-model="isVmiBusiness" disabled></el-checkbox>
          </el-descriptions-item>
          <!-- 批号 -->
          <el-descriptions-item label="批号">{{ setCurrentRow && setCurrentRow.Lot && setCurrentRow.Lot.Number }}</el-descriptions-item>
          <!-- 生产日期 -->
          <el-descriptions-item label="生产日期">{{ setCurrentRow && setCurrentRow.ProduceDate && parseTime(setCurrentRow.ProduceDate, '{y}-{m}-{d}') }}</el-descriptions-item>
          <!-- 保质期 -->
          <el-descriptions-item label="保质期">{{ setCurrentRow && setCurrentRow.MaterialID && setCurrentRow.MaterialID.MaterialStock && setCurrentRow.MaterialID.MaterialStock[0] && setCurrentRow.MaterialID.MaterialStock[0].ExpPeriod }}</el-descriptions-item>
          <!-- 保质期单位 -->
          <el-descriptions-item label="保质期单位">{{ setCurrentRow && setCurrentRow.MaterialID && setCurrentRow.MaterialID.MaterialStock && setCurrentRow.MaterialID.MaterialStock[0] && setCurrentRow.MaterialID.MaterialStock[0].ExpUnit }}</el-descriptions-item>
          <!-- 有效期至 -->
          <el-descriptions-item label="有效期至">{{ setCurrentRow && setCurrentRow.MaterialStock && setCurrentRow.MaterialStock[0] && setCurrentRow.MaterialStock[0].ExpiryDate && parseTime(setCurrentRow.MaterialStock[0].ExpiryDate, '{y}-{m}-{d}') }}</el-descriptions-item>
        </el-descriptions>
        <el-descriptions border :column="3" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 3 - 7em - 22px)' }" v-show="detailActiveName === 'Dthird'">
          <!-- 物料编码 -->
          <el-descriptions-item label="物料编码">{{ setCurrentRow && setCurrentRow.MaterialID && setCurrentRow.MaterialID.Number }}</el-descriptions-item>
          <!-- 物料名称 -->
          <el-descriptions-item label="物料名称">{{ setCurrentRow && setCurrentRow.MaterialID && getString(setCurrentRow.MaterialID.Name) }}</el-descriptions-item>
          <!-- 源单类型 -->
          <el-descriptions-item label="源单类型">{{ setCurrentRow && setCurrentRow.SrcType }}</el-descriptions-item>
          <!-- 源单单号 -->
          <el-descriptions-item label="源单单号">{{ setCurrentRow && setCurrentRow.SrcBillNo }}</el-descriptions-item>
          <!-- 订单单号 -->
          <el-descriptions-item label="订单单号">{{ setCurrentRow && setCurrentRow.SoorDerno }}</el-descriptions-item>
          <!-- 业务流程 -->
          <el-descriptions-item label="业务流程">{{ setCurrentRow && setCurrentRow.FBFLowId && getString(setCurrentRow.FBFLowId.Name) }}</el-descriptions-item>
          <!-- 累计退货通知数量 -->
          <el-descriptions-item label="累计退货通知数量">{{ (setCurrentRow && setCurrentRow.SumRetNoticeQty) || 0 }}</el-descriptions-item>
          <!-- 累计退货数量 -->
          <el-descriptions-item label="累计退货数量">{{ (setCurrentRow && setCurrentRow.SumRetStockQty) || 0 }}</el-descriptions-item>
          <!-- 关联应收状态 -->
          <el-descriptions-item label="关联应收状态">{{ setCurrentRow && setCurrentRow.RowARStatus && getOptionLabel(ARStatusOptions, setCurrentRow.RowARStatus) }}</el-descriptions-item>
          <!-- 累计应收数量 -->
          <el-descriptions-item label="累计应收数量">{{ setCurrentRow && setCurrentRow.BASEARQTY }}</el-descriptions-item>
          <!-- 关联应收数量 -->
          <el-descriptions-item label="关联应收数量">{{ setCurrentRow && setCurrentRow.ARJoinQty }}</el-descriptions-item>
          <!-- 到货确认 -->
          <el-descriptions-item label="到货确认">{{ setCurrentRow && setCurrentRow.ArrivalStatus }}</el-descriptions-item>
          <!-- 到货日期 -->
          <el-descriptions-item label="到货日期">{{ setCurrentRow && setCurrentRow.ArrivalDate && parseTime(setCurrentRow.ArrivalDate, '{y}-{m}-{d}') }}</el-descriptions-item>
          <!-- 到货确认人 -->
          <el-descriptions-item label="到货确认人">{{ setCurrentRow && setCurrentRow.ArrivalConfirmor && getString(setCurrentRow.ArrivalConfirmor.Name) }}</el-descriptions-item>
          <!-- 检验确认 -->
          <el-descriptions-item label="检验确认">{{ setCurrentRow && setCurrentRow.ValidateStatus }}</el-descriptions-item>
          <!-- 检验日期 -->
          <el-descriptions-item label="检验日期">{{ setCurrentRow && setCurrentRow.ValidateDate && parseTime(setCurrentRow.ValidateDate, '{y}-{m}-{d}') }}</el-descriptions-item>
          <!-- 检验确认人 -->
          <el-descriptions-item label="检验确认人">{{ setCurrentRow && setCurrentRow.ValidateConfirmor && getString(setCurrentRow.ValidateConfirmor.Name) }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
    <!-- 下推弹出框 -->
    <el-dialog v-dialogDragBox title="选择单据" :visible.sync="pushOpen" width="580px" class="custom-dialog">
      <div style="padding: 0 20px">
        <el-form ref="pushForm" :model="pushForm" :rules="pushFormRules" label-width="80px">
          <el-form-item label="" label-width="0" prop="target">
            <el-radio-group v-model="pushForm.target" v-removeAriaHidden @change="handleTargetChange" style="width: 100%">
              <el-row :gutter="10" class="custom-push-target">
                <el-col :span="12" v-for="item in pushTarget" :key="item.value">
                  <el-radio :label="item.value">{{ item.label }}</el-radio>
                </el-col>
              </el-row>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="转换规则" prop="ruleId" v-if="isNeedRule()">
            <el-select v-model="pushForm.ruleId" placeholder="请选择转换规则" style="width: 100%" @change="calculateTargetBillType">
              <el-option v-for="item in calculateRule" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="单据类型" prop="targetBillTypeId" v-if="isNeedBillType()">
            <el-select v-model="pushForm.targetBillTypeId" placeholder="请选择单据类型" style="width: 100%">
              <el-option v-for="item in calculateBillType" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn small" @click="pushOpen = false">取消</el-button>
        <el-button class="custom-dialog-btn small primary" :class="{ disabled: !pushForm.target }" :disabled="!pushForm.target" @click="handlePushSubmit">确定</el-button>
      </div>
    </el-dialog>
    <!-- 新增销售退货单 -->
    <sales-return-create ref="salesReturnCreate" @callBack="showSalesReturnAdd = false" v-if="showSalesReturnAdd" />
  </div>
</template>
<script>
import { getSalesOutDetail, auditSalesOut, revokeSalesOut, deleteSalesOut, pushSalesOut, submitSalesOut, unAuditSalesOut } from '@/api/kingdee/sell/salesOut'
import { kingdee } from '@/minix'
import SalesReturnCreate from '@/views/kingdee/sell/salesReturn/create'

export default {
  mixins: [kingdee],
  components: { SalesReturnCreate },
  data() {
    return {
      open: false,
      info: {},
      setCurrentRow: {},
      // 下推
      pushOpen: false,
      pushTarget: [
        { value: 'STK_InStock', label: '采购入库单' },
        { value: 'SAL_RETURNNOTICE', label: '退货通知单' },
        { value: 'SAL_RETURNSTOCK', label: '销售退货单' }
      ],
      pushForm: {
        number: undefined,
        target: undefined
      },
      pushFormRules: {
        target: [{ required: true, message: '请选择下推单据', trigger: 'change' }],
        ruleId: [{ required: true, message: '请选择转换规则', trigger: 'change' }],
        targetBillTypeId: [{ required: true, message: '请选择单据类型', trigger: 'change' }]
      },
      activeName: 'first',
      detailActiveName: 'Dfirst',
      // 作废状态
      CancelStatusOptions: [
        { value: 'A', label: '未作废' },
        { value: 'B', label: '已作废' }
      ],
      // 关联应收状态
      ARStatusOptions: [
        { value: 'B', label: '部分应收' },
        { value: 'C', label: '已完全应收' }
      ],
      // 货主类型
      OwnerTypeIDOptions: [
        { label: '业务组织', value: 'BD_OwnerOrg' },
        { label: '供应商', value: 'BD_Supplier' },
        { label: '客户', value: 'BD_Customer' }
      ],
      // 保管者类型
      KeeperTypeIDOptions: [
        { label: '业务组织', value: 'BD_KeeperOrg' },
        { label: '供应商', value: 'BD_Supplier' },
        { label: '客户', value: 'BD_Customer' }
      ],
      buttons: [
        { text: '提交', type: 'primary', action: 'submit', status: ['A', 'D'] },
        { text: '审核', type: 'warning', action: 'audit', status: ['B'] },
        { text: '撤销', type: 'info', action: 'revoke', status: ['B'] },
        { text: '删除', type: 'danger', action: 'delete', status: ['A', 'D'] },
        { text: '反审', type: 'warning', action: 'unAudit', status: ['C'] },
        { text: '下推', type: 'success', action: 'push', status: ['C'] }
      ],
      // 转换规则
      ruleList: [
        // 下推至销售退货单
        { value: 'OutStock-SalReturnStock', label: '销售订单至销售退货单', target: 'SAL_RETURNSTOCK' }
      ],
      // 单据类型
      targetBillTypeList: [
        // 下推至销售退货单
        { value: '73383412199a402bb58439509e089077', label: '标准销售退货单', target: 'OutStock-SalReturnStock', billType: 'XSCKD01_SYS' },
        { value: 'CEB80356E70D492F9DD7C5E9CD37107E', label: '零售退货单', target: 'OutStock-SalReturnStock', billType: 'XSCKD03_SYS' },
        { value: '3a9ac994945e4a5eac2038f0c960221a', label: '分销购销销售退货单', target: 'OutStock-SalReturnStock', billType: 'XSCKD04_SYS' },
        { value: '0050569463e5b88611e39459894134ae', label: 'VMI销售退货单', target: 'OutStock-SalReturnStock', billType: 'XSCKD05_SYS' },
        { value: '54a39502b6c77d', label: '现销退货单', target: 'OutStock-SalReturnStock', billType: 'XSCKD06_SYS' },
        { value: '6284917c690cfc', label: '直运销售退货单', target: 'OutStock-SalReturnStock', billType: 'XSCKD08_SYS' }
      ],
      // 新增销售退货单
      showSalesReturnAdd: false
    }
  },
  computed: {
    // VIM业务
    isVmiBusiness() {
      return this.setCurrentRow?.VmiBusinessStatus || false
    },
    // 计算规则
    calculateRule() {
      return this.ruleList.filter(item => item.target === this.pushForm.target)
    },
    // 计算单据类型
    calculateBillType() {
      const { ruleId } = this.pushForm
      const billType = this.info?.BillTypeID?.Number || ''
      const arr = this.targetBillTypeList.filter(item => item.target === ruleId) || []
      const newArr = arr.filter(item => item.billType === billType) || []
      return newArr
    }
  },
  methods: {
    // 检查状态
    checkStatus(allowedStatus) {
      return allowedStatus.includes(this.info.DocumentStatus)
    },
    // 切换tab
    handleClick(tab, event) {
      this.activeName = tab.name
    },
    handleDetailClick(tab, event) {
      this.detailActiveName = tab.name
    }, // 获取详情
    getInfo(row = {}) {
      if (!row.BillNo) {
        this.$message.error('参数错误，请重新选择')
        return
      }
      getSalesOutDetail({ billNo: row.BillNo }).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          const { result } = data
          this.activeName = 'first'
          this.detailActiveName = 'Dfirst'
          this.open = !!result?.result
          this.info = result?.result || {}
          this.$nextTick(() => {
            this.setCurrentRow = result?.result?.SAL_OUTSTOCKENTRY?.[0] || {}
            if (this.$refs.detailTable) {
              this.$refs.detailTable.setCurrentRow(this.setCurrentRow)
              this.$refs.detailTable.bodyWrapper.scrollLeft = 0
            }
          })
        } else this.$message.error(msg)
      })
    },
    // 当前行
    handleCurrentChange(val) {
      this.setCurrentRow = val
    },
    beforeClose() {
      this.handleClose()
      this.$emit('update', { billNo: this.info.BillNo, newStatus: this.info.DocumentStatus })
    },
    // 关闭
    handleClose(flag = false) {
      this.open = false
      this.$emit('callBack', flag)
    },
    // 金蝶操作
    // prettier-ignore
    handleKingdeeDo(type) {
      const number = this.info.BillNo
      if (!number) return
      switch (type) {
        case 'submit':
          // 提交
          this.$modal.confirm('确认要提交该销售出库单吗？').then(() => {
            submitSalesOut({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('提交成功')
                this.$set(this.info, 'DocumentStatus', 'B')
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'audit':
          // 审核
          this.$modal.confirm('确认要审核该销售出库单吗？').then(() => {
            auditSalesOut({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('审核成功')
                this.$set(this.info, 'DocumentStatus', 'C')
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'revoke':
          // 撤销
          this.$modal.confirm('确认要撤销该销售出库单吗？').then(() => {
            revokeSalesOut({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('撤销成功')
                this.$set(this.info, 'DocumentStatus', 'D')
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'unAudit':
          // 反审核
          this.$modal.confirm('确认要反审核该销售出库单吗？').then(() => {
            unAuditSalesOut({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('反审核成功')
                this.$set(this.info, 'DocumentStatus', 'D')
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'delete':
          // 删除
          this.$modal.confirm('确认要删除该销售出库单吗？').then(() => {
            deleteSalesOut({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('删除成功')
                this.handleClose(true)
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'push':
          // 下推          
          this.initPushForm()
          break
      }
    },
    // 初始化下推表单
    initPushForm() {
      this.pushForm = {
        number: this.info.BillNo,
        target: undefined,
        ruleId: undefined,
        targetBillTypeId: undefined
      }
      this.$nextTick(() => {
        this.resetForm('pushForm')
      })
      this.pushOpen = true
    },
    // 切换下推单据
    handleTargetChange(val) {
      if (val === 'SAL_RETURNSTOCK') {
        this.setupSalReturnStockPush()
        return
      }
      this.clearPushFormFields()
    },
    // 设置销售退货单下推
    setupSalReturnStockPush() {
      this.pushForm.ruleId = this.calculateRule?.[0]?.value || undefined
      this.pushForm.targetBillTypeId = undefined
      // 计算目标单据类型
      this.$nextTick(() => {
        this.calculateTargetBillType()
      })
    },
    // 清空下推表单字段
    clearPushFormFields() {
      this.pushForm.ruleId = undefined
      this.pushForm.targetBillTypeId = undefined
    },
    // 计算目标单据类型
    calculateTargetBillType() {
      const { ruleId } = this.pushForm
      const billType = this.info?.BillTypeID?.Number || ''
      const arr = this.targetBillTypeList.filter(item => item.target === ruleId) || []
      const newArr = arr.filter(item => item.billType === billType) || []
      this.pushForm.targetBillTypeId = newArr?.[0]?.value || undefined
    },
    // 判断是否需要转换规则
    isNeedRule() {
      const { target, ruleId } = this.pushForm
      if (target === 'SAL_RETURNSTOCK') {
        return true
      }
      return false
    },
    // 判断是否需要单据类型
    isNeedBillType() {
      const { target, ruleId } = this.pushForm
      if (target === 'SAL_RETURNSTOCK') {
        return true
      }
      return false
    },
    // 下推提交
    handlePushSubmit() {
      const { number } = this.pushForm
      if (!number) return
      this.$refs.pushForm.validate(valid => {
        if (valid) {
          this.executePushDown()
        }
      })
    },
    // 执行下推操作
    // prettier-ignore
    executePushDown() {
      pushSalesOut(this.pushForm).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          this.handlePushSuccess(data)
        } else if (code === 400) {
          this.handlePushError(msg)
        } else {
          this.$message.error(msg)
        }
      }).catch(error => {
        console.error('下推操作失败:', error)
        this.$message.error('下推操作失败，请重试')
      })
    },
    // 处理下推成功
    handlePushSuccess(data) {
      if (this.pushForm.target === 'SAL_RETURNSTOCK') {
        this.handleSalReturnStockPushSuccess(data)
      } else {
        this.$message.success('下推成功')
        this.pushOpen = false
        this.handleClose(true)
      }
    },
    // 处理销售退货单下推成功
    handleSalReturnStockPushSuccess(data) {
      this.pushOpen = false
      this.showSalReturnStockAdd = true
      const { responseStatus } = data
      const fid = responseStatus?.successEntitys?.[0]?.id
      if (!fid) {
        this.$message.error('参数错误，请重试！')
        return
      }
      this.$nextTick(() => {
        this.$refs.salesReturnCreate.initPush(fid, 'push')
      })
    },
    // 处理下推错误
    handlePushError(msg) {
      this.$alert(msg || '操作失败，请重试或使用金蝶进行操作！', '提示', {
        type: 'info',
        confirmButtonText: '确定',
        callback: () => {
          this.pushOpen = false
        }
      })
    },
    // 获取合计
    getSummary(param) {
      const { columns, data } = param
      const result = columns.map((column, index) => {
        if (index === 0) {
          return '合计'
        }
        if (column.property === 'MustQty' || column.property === 'RealQty' || column.property === 'AllAmount' || column.property === 'Amount' || column.property === 'SALBASEQTY' || column.property === 'ExtAuxUnitQty') {
          return `${this.calculateTotal(data, column.property)}`
        }
        return ''
      })
      return result
    },
    // 计算合计
    calculateTotal(data, key) {
      return data.reduce((total, item) => {
        if (key === 'MustQty') {
          const value = parseFloat(item.MustQty) || 0
          return parseFloat((total + value).toFixed(5))
        } else if (key === 'RealQty') {
          const value = parseFloat(item.RealQty) || 0
          return parseFloat((total + value).toFixed(5))
        } else if (key === 'AllAmount') {
          const value = parseFloat(item.AllAmount) || 0
          return parseFloat((total + value).toFixed(5))
        } else if (key === 'Amount') {
          const value = parseFloat(item.Amount) || 0
          return parseFloat((total + value).toFixed(5))
        } else if (key === 'SALBASEQTY') {
          const value = parseFloat(item.SALBASEQTY) || 0
          return parseFloat((total + value).toFixed(5))
        } else if (key === 'ExtAuxUnitQty') {
          const value = parseFloat(item.ExtAuxUnitQty) || 0
          return parseFloat((total + value).toFixed(5))
        }
        return total
      }, 0)
    }
  }
}
</script>
<style scoped lang="scss">
@import '@/assets/styles/custom-new.scss';
::v-deep {
  .el-descriptions__header {
    margin-bottom: 0;
  }
  .el-table.custom-table .el-table__body-wrapper .el-table__row:hover td.el-table__cell {
    background-color: #eaf1fe !important;
  }
}
.customTitle {
  font-size: 16px;
  font-weight: normal;
  line-height: 60px;
  color: $font;
}
.kindeeButton {
  display: flex;
  align-items: center;
}
.custom-push-target {
  .el-col.el-col-12 {
    .el-radio {
      height: 30px;
      display: flex;
      align-items: center;
    }
  }
}
.custom-tabs {
  margin-top: 15px;
  ::v-deep {
    .el-tabs__header {
      .el-tabs__nav {
        border: 0;
      }
      .el-tabs__item {
        background-color: #eef2f8;
        border-radius: 5px 5px 0 0;
        border-left-width: 0;
        border-right-width: 0;
        &.is-active {
          background-color: $blue;
          color: $white;
        }
      }
      .el-tabs__item + .el-tabs__item {
        margin-left: 2px;
      }
    }
  }
}
</style>
