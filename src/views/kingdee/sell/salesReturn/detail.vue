<template>
  <div>
    <el-dialog v-dialogDragBox title="销售退货单详情" :visible.sync="open" width="90%" class="custom-dialog" :before-close="beforeClose">
      <div style="padding: 0 20px">
        <div class="kindeeButton" :key="info.DocumentStatus">
          <template v-for="(btn, index) in buttons">
            <el-button :key="index" :type="btn.type" size="medium" v-if="checkStatus(btn.status)" @click="handleKingdeeDo(btn.action)">{{ btn.text }}</el-button>
            <el-button :key="'disabled-' + index" :type="btn.type" size="medium" disabled v-else>{{ btn.text }}</el-button>
          </template>
        </div>
        <el-tabs v-model="activeName" type="card" @tab-click="handleClick" class="custom-tabs">
          <el-tab-pane label="基本信息" name="first"></el-tab-pane>
          <el-tab-pane label="客户信息" name="second"></el-tab-pane>
          <el-tab-pane label="财务信息" name="third"></el-tab-pane>
          <el-tab-pane label="物流跟踪" name="fourth"></el-tab-pane>
          <el-tab-pane label="其他" name="fifth"></el-tab-pane>
        </el-tabs>
        <el-descriptions border :column="4" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 4 - 7em - 22px)' }" v-show="activeName === 'first'">
          <!-- 单据类型 -->
          <el-descriptions-item label="单据类型">{{ info.BillTypeID && getString(info.BillTypeID.Name, 1) }}</el-descriptions-item>
          <!-- 单据编号 -->
          <el-descriptions-item label="单据编号">{{ info.BillNo }}</el-descriptions-item>
          <!-- 日期 -->
          <el-descriptions-item label="日期">{{ info.Date && parseTime(info.Date, '{y}-{m}-{d}') }}</el-descriptions-item>
          <!-- 结算币别 -->
          <el-descriptions-item label="结算币别">{{ info.SAL_RETURNSTOCKFIN && info.SAL_RETURNSTOCKFIN[0] && info.SAL_RETURNSTOCKFIN[0].SettleCurrId && getString(info.SAL_RETURNSTOCKFIN[0].SettleCurrId.Name) }}</el-descriptions-item>
          <!-- 单据状态 -->
          <el-descriptions-item label="单据状态">{{ info.DocumentStatus && getDocumentStatusLabel(info.DocumentStatus) }}</el-descriptions-item>
          <!-- 退货客户 -->
          <el-descriptions-item label="退货客户">{{ info.RetcustId && getString(info.RetcustId.Name) }}</el-descriptions-item>
          <!-- 退货原因 -->
          <el-descriptions-item label="退货原因">{{ info.ReturnReason }}</el-descriptions-item>
          <!-- 交货地点 -->
          <el-descriptions-item label="交货地点">{{ info.HeadLocId && getString(info.HeadLocId.Name) }}</el-descriptions-item>
          <!-- 库存组织 -->
          <el-descriptions-item label="库存组织">{{ info.StockOrgId && getString(info.StockOrgId.Name) }}</el-descriptions-item>
          <!-- 库存部门 -->
          <el-descriptions-item label="库存部门">{{ info.StockDeptId && getString(info.StockDeptId.Name) }}</el-descriptions-item>
          <!-- 仓管员 -->
          <el-descriptions-item label="仓管员">{{ info.StockerId && getString(info.StockerId.Name) }}</el-descriptions-item>
          <!-- 备注 -->
          <el-descriptions-item label="备注">{{ info.FNote }}</el-descriptions-item>
          <!-- 销售组织 -->
          <el-descriptions-item label="销售组织">{{ info.SaleOrgId && getString(info.SaleOrgId.Name) }}</el-descriptions-item>
          <!-- 销售部门 -->
          <el-descriptions-item label="销售部门">{{ info.Sledeptid && getString(info.Sledeptid.Name) }}</el-descriptions-item>
          <!-- 销售员 -->
          <el-descriptions-item label="销售员">{{ info.SalesManId && getString(info.SalesManId.Name) }}</el-descriptions-item>
          <!-- 销售订单号 -->
          <el-descriptions-item label="销售订单号">{{ info.F_SCMJ_XSDDH }}</el-descriptions-item>
        </el-descriptions>
        <el-descriptions border :column="2" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 2 - 7em - 22px)' }" v-show="activeName === 'second'">
          <!-- 收货方 -->
          <el-descriptions-item label="收货方">{{ info.ReceiveCustId && getString(info.ReceiveCustId.Name) }}</el-descriptions-item>
          <!-- 收货方联系人 -->
          <el-descriptions-item label="收货方联系人">{{ info.ReceiveCusContact && getString(info.ReceiveCusContact.Name) }}</el-descriptions-item>
          <!-- 收货方地址 -->
          <el-descriptions-item label="收货方地址" :span="2">{{ info.ReceiveAddress }}</el-descriptions-item>
          <!-- 收货人姓名 -->
          <el-descriptions-item label="收货人姓名">{{ info.FLinkMan }}</el-descriptions-item>
          <!-- 联系电话 -->
          <el-descriptions-item label="联系电话">{{ info.FLinkPhone }}</el-descriptions-item>
          <!-- 结算方 -->
          <el-descriptions-item label="结算方">{{ info.SettleCustId && getString(info.SettleCustId.Name) }}</el-descriptions-item>
          <!-- 付款方 -->
          <el-descriptions-item label="付款方">{{ info.PayCustId && getString(info.PayCustId.Name) }}</el-descriptions-item>
        </el-descriptions>
        <el-descriptions border :column="4" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 4 - 7em - 22px)' }" v-show="activeName === 'third'">
          <!-- 结算组织 -->
          <el-descriptions-item label="结算组织">{{ info.SAL_RETURNSTOCKFIN && info.SAL_RETURNSTOCKFIN[0] && info.SAL_RETURNSTOCKFIN[0].SettleOrgId && getString(info.SAL_RETURNSTOCKFIN[0].SettleOrgId.Name) }}</el-descriptions-item>
          <!-- 结算币别 -->
          <el-descriptions-item label="结算币别">{{ info.SAL_RETURNSTOCKFIN && info.SAL_RETURNSTOCKFIN[0] && info.SAL_RETURNSTOCKFIN[0].SettleCurrId && getString(info.SAL_RETURNSTOCKFIN[0].SettleCurrId.Name) }}</el-descriptions-item>
          <!-- 结算方式 -->
          <el-descriptions-item label="结算方式">{{ info.SAL_RETURNSTOCKFIN && info.SAL_RETURNSTOCKFIN[0] && info.SAL_RETURNSTOCKFIN[0].SettleTypeId && getString(info.SAL_RETURNSTOCKFIN[0].SettleTypeId.Name) }}</el-descriptions-item>
          <!-- 收款条件 -->
          <el-descriptions-item label="收款条件">{{ info.SAL_RETURNSTOCKFIN && info.SAL_RETURNSTOCKFIN[0] && info.SAL_RETURNSTOCKFIN[0].RECEIPTCONDITIONID && getString(info.SAL_RETURNSTOCKFIN[0].RECEIPTCONDITIONID.Name) }}</el-descriptions-item>
          <!-- 汇率类型 -->
          <el-descriptions-item label="汇率类型">{{ info.SAL_RETURNSTOCKFIN && info.SAL_RETURNSTOCKFIN[0] && info.SAL_RETURNSTOCKFIN[0].ExchangeTypeId && getString(info.SAL_RETURNSTOCKFIN[0].ExchangeTypeId.Name) }}</el-descriptions-item>
          <!-- 汇率 -->
          <el-descriptions-item label="汇率">{{ info.SAL_RETURNSTOCKFIN && info.SAL_RETURNSTOCKFIN[0] && info.SAL_RETURNSTOCKFIN[0].ExchangeRate }}</el-descriptions-item>
          <!-- 本位币 -->
          <el-descriptions-item label="本位币">{{ info.SAL_RETURNSTOCKFIN && info.SAL_RETURNSTOCKFIN[0] && info.SAL_RETURNSTOCKFIN[0].LocalCurrId && getString(info.SAL_RETURNSTOCKFIN[0].LocalCurrId.Name) }}</el-descriptions-item>
        </el-descriptions>
        <el-table ref="logisticsTable" :data="info.ReturnStockTrace" class="custom-table" highlight-current-row v-show="activeName === 'fourth'">
          <!-- 物流公司 -->
          <el-table-column label="物流公司" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.LogComId && getString(scope.row.LogComId.Name) }}</template>
          </el-table-column>
          <!-- 物流单号 -->
          <el-table-column label="物流单号" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.CarryBillNo }}</template>
          </el-table-column>
          <!-- 寄件人手机号码 -->
          <el-table-column label="寄件人手机号码" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.PhoneNumber }}</template>
          </el-table-column>
          <!-- 起始地点 -->
          <el-table-column label="起始地点" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.From }}</template>
          </el-table-column>
          <!-- 终止地点 -->
          <el-table-column label="终止地点" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.To }}</template>
          </el-table-column>
          <!-- 发货时间 -->
          <el-table-column label="发货时间" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.DelTime && parseTime(scope.row.DelTime, '{y}-{m}-{d}') }}</template>
          </el-table-column>
          <!-- 物流状态 -->
          <el-table-column label="物流状态" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.TraceStatus }}</template>
          </el-table-column>
          <!-- 签收时间 -->
          <el-table-column label="签收时间" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.ReceiptTime && parseTime(scope.row.ReceiptTime, '{y}-{m}-{d}') }}</template>
          </el-table-column>
          <!-- 类型 -->
          <el-table-column label="类型" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.CarryBillNoType }}</template>
          </el-table-column>
        </el-table>
        <el-descriptions border :column="3" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 3 - 7em - 22px)' }" v-show="activeName === 'fifth'">
          <!-- 创建人 -->
          <el-descriptions-item label="创建人">{{ info.CreatorId && info.CreatorId.Name }}</el-descriptions-item>
          <!-- 创建日期 -->
          <el-descriptions-item label="创建日期">{{ info.CreateDate && parseTime(info.CreateDate, '{y}-{m}-{d}') }}</el-descriptions-item>
          <!-- 最后修改人 -->
          <el-descriptions-item label="最后修改人">{{ info.ModifierId && info.ModifierId.Name }}</el-descriptions-item>
          <!-- 最后修改日期 -->
          <el-descriptions-item label="最后修改日期">{{ info.ModifyDate && parseTime(info.ModifyDate, '{y}-{m}-{d}') }}</el-descriptions-item>
          <!-- 审核人 -->
          <el-descriptions-item label="审核人">{{ info.ApproverId && info.ApproverId.Name }}</el-descriptions-item>
          <!-- 审核日期 -->
          <el-descriptions-item label="审核日期">{{ info.ApproveDate && parseTime(info.ApproveDate, '{y}-{m}-{d}') }}</el-descriptions-item>
          <!-- 作废状态 -->
          <el-descriptions-item label="作废状态">{{ info.CancelStatus && getOptionLabel(CancelStatusOptions, info.CancelStatus) }}</el-descriptions-item>
          <!-- 作废人 -->
          <el-descriptions-item label="作废人">{{ info.CancellerId && info.CancellerId.Name }}</el-descriptions-item>
          <!-- 作废日期 -->
          <el-descriptions-item label="作废日期">{{ info.CancelDate && parseTime(info.CancelDate, '{y}-{m}-{d}') }}</el-descriptions-item>
          <!-- 关联应收状态 -->
          <el-descriptions-item label="关联应收状态">{{ info.ARStatus && getOptionLabel(ARStatusOptions, info.ARStatus) }}</el-descriptions-item>
        </el-descriptions>
        <!-- 明细信息 -->
        <el-tabs v-model="detailActiveName" type="card" @tab-click="handleDetailClick" class="custom-tabs">
          <el-tab-pane label="明细信息" name="Dfirst"></el-tab-pane>
          <el-tab-pane label="物料数据" name="Dsecond"></el-tab-pane>
          <el-tab-pane label="其他信息" name="Dthird"></el-tab-pane>
        </el-tabs>
        <el-table ref="detailTable" :data="info.SAL_RETURNSTOCKENTRY" class="custom-table" highlight-current-row @current-change="handleCurrentChange" :setCurrentRow="setCurrentRow" v-show="detailActiveName === 'Dfirst'">
          <!-- 序号 -->
          <el-table-column type="index" label="序号" align="center"></el-table-column>
          <!-- 物料编码 -->
          <el-table-column label="物料编码" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.MaterialId && scope.row.MaterialId.Number }}</template>
          </el-table-column>
          <!-- 物料名称 -->
          <el-table-column label="物料名称" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.MaterialId && getString(scope.row.MaterialId.Name) }}</template>
          </el-table-column>
          <!-- 规格型号 -->
          <el-table-column label="规格型号" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.MaterialId && getString(scope.row.MaterialId.Specification) }}</template>
          </el-table-column>
          <!-- 库存单位 -->
          <el-table-column label="库存单位" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.UnitId && getString(scope.row.UnitId.Name) }}</template>
          </el-table-column>
          <!-- 应退数量 -->
          <el-table-column label="应退数量" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.Mustqty }}</template>
          </el-table-column>
          <!-- 实退数量 -->
          <el-table-column label="实退数量" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.RealQty }}</template>
          </el-table-column>
          <!-- 是否赠品 -->
          <el-table-column label="是否赠品" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
              <el-checkbox v-model="scope.row.IsFree" disabled></el-checkbox>
            </template>
          </el-table-column>
          <!-- 退货类型 -->
          <el-table-column label="退货类型" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.ReturnType && getString(scope.row.ReturnType.FDataValue, 1) }}</template>
          </el-table-column>
          <!-- 仓库 -->
          <el-table-column label="仓库" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.StockId && getString(scope.row.StockId.Name) }}</template>
          </el-table-column>
          <!-- 仓位 -->
          <el-table-column label="仓位" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.StocklocId && getString(scope.row.StocklocId.Name) }}</template>
          </el-table-column>
          <!-- 库存状态 -->
          <el-table-column label="库存状态" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.StockstatusId && getString(scope.row.StockstatusId.Name) }}</template>
          </el-table-column>
          <!-- 退货日期 -->
          <el-table-column label="退货日期" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.DeliveryDate && parseTime(scope.row.DeliveryDate, '{y}-{m}-{d}') }}</template>
          </el-table-column>
          <!-- 备注 -->
          <el-table-column label="备注" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.Note }}</template>
          </el-table-column>
          <!-- 结算单价 -->
          <el-table-column label="结算单价" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.F_SCMJ_JSPrice }}</template>
          </el-table-column>
          <!-- 结算金额 -->
          <el-table-column label="结算金额" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.F_SCMJ_JSAmount }}</template>
          </el-table-column>
        </el-table>
        <el-descriptions border :column="3" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 3 - 7em - 22px)' }" v-show="detailActiveName === 'Dsecond'">
          <!-- 物料编码 -->
          <el-descriptions-item label="物料编码">{{ setCurrentRow && setCurrentRow.MaterialId && setCurrentRow.MaterialId.Number }}</el-descriptions-item>
          <!-- 物料名称 -->
          <el-descriptions-item label="物料名称">{{ setCurrentRow && setCurrentRow.MaterialId && getString(setCurrentRow.MaterialId.Name) }}</el-descriptions-item>
          <!-- 库存单位 -->
          <el-descriptions-item label="库存单位">{{ setCurrentRow && setCurrentRow.UnitId && getString(setCurrentRow.UnitId.Name) }}</el-descriptions-item>
          <!-- 实退数量 -->
          <el-descriptions-item label="实退数量">{{ (setCurrentRow && setCurrentRow.RealQty) || '' }}</el-descriptions-item>
          <!-- 销售单位 -->
          <el-descriptions-item label="销售数量">{{ setCurrentRow && setCurrentRow.SalUnitID && getString(setCurrentRow.SalUnitID.Name) }}</el-descriptions-item>
          <!-- 销售数量 -->
          <el-descriptions-item label="销售数量">{{ (setCurrentRow && setCurrentRow.SalUnitQty) || '' }}</el-descriptions-item>
          <!-- 计价单位 -->
          <el-descriptions-item label="计价单位">{{ setCurrentRow && setCurrentRow.PriceUnitId && getString(setCurrentRow.PriceUnitId.Name) }}</el-descriptions-item>
          <!-- 计价数量 -->
          <el-descriptions-item label="计价数量">{{ (setCurrentRow && setCurrentRow.PriceUnitQty) || '' }}</el-descriptions-item>
          <!-- 辅单位 -->
          <el-descriptions-item label="辅单位">{{ setCurrentRow && setCurrentRow.ExtAuxUnitId && getString(setCurrentRow.ExtAuxUnitId.Name) }}</el-descriptions-item>
          <!-- 数量(辅单位) -->
          <el-descriptions-item label="数量(辅单位)">{{ (setCurrentRow && setCurrentRow.ExtAuxUnitQty) || '' }}</el-descriptions-item>
          <!-- BOM版本 -->
          <el-descriptions-item label="BOM版本">{{ setCurrentRow && setCurrentRow.BOMId && getString(setCurrentRow.BOMId.Name) }}</el-descriptions-item>
          <!-- 物料类别 -->
          <el-descriptions-item label="物料类别">{{ setCurrentRow && setCurrentRow.MaterialId && setCurrentRow.MaterialId.MaterialBase && setCurrentRow.MaterialId.MaterialBase[0] && setCurrentRow.MaterialId.MaterialBase[0].CategoryID && getString(setCurrentRow.MaterialId.MaterialBase[0].CategoryID.Name) }}</el-descriptions-item>
          <!-- 货主类型 -->
          <el-descriptions-item label="货主类型">{{ setCurrentRow && setCurrentRow.OwnerTypeId && getOptionLabel(OwnerTypeIDOptions, setCurrentRow.OwnerTypeId) }}</el-descriptions-item>
          <!-- 货主 -->
          <el-descriptions-item label="货主">{{ setCurrentRow && setCurrentRow.OwnerId && getString(setCurrentRow.OwnerId.Name) }}</el-descriptions-item>
          <!-- 保管者类型 -->
          <el-descriptions-item label="保管者类型">{{ setCurrentRow && setCurrentRow.KeeperTypeId && getOptionLabel(KeeperTypeIDOptions, setCurrentRow.KeeperTypeId) }}</el-descriptions-item>
          <!-- 保管者 -->
          <el-descriptions-item label="保管者">{{ setCurrentRow && setCurrentRow.KeeperId && getString(setCurrentRow.KeeperId.Name) }}</el-descriptions-item>
          <!-- VMI业务 -->
          <el-descriptions-item label="VMI业务">
            <el-checkbox v-model="VmiBusinessStatus" disabled></el-checkbox>
          </el-descriptions-item>
          <!-- 批号 -->
          <el-descriptions-item label="批号">{{ setCurrentRow && setCurrentRow.Lot && setCurrentRow.Lot.Number }}</el-descriptions-item>
          <!-- 生产日期 -->
          <el-descriptions-item label="生产日期">{{ setCurrentRow && setCurrentRow.ProduceDate && parseTime(setCurrentRow.ProduceDate, '{y}-{m}-{d}') }}</el-descriptions-item>
          <!-- 保质期单位 -->
          <el-descriptions-item label="保质期单位">{{ setCurrentRow && setCurrentRow.MaterialId && setCurrentRow.MaterialId.MaterialStock && setCurrentRow.MaterialId.MaterialStock[0] && setCurrentRow.MaterialId.MaterialStock[0].ExpUnit }}</el-descriptions-item>
          <!-- 保质期 -->
          <el-descriptions-item label="保质期">{{ setCurrentRow && setCurrentRow.MaterialId && setCurrentRow.MaterialId.MaterialStock && setCurrentRow.MaterialId.MaterialStock[0] && setCurrentRow.MaterialId.MaterialStock[0].ExpPeriod }}</el-descriptions-item>
          <!-- 有效期至 -->
          <el-descriptions-item label="有效期至">{{ setCurrentRow && setCurrentRow.ExpiryDate && parseTime(setCurrentRow.ExpiryDate, '{y}-{m}-{d}') }}</el-descriptions-item>
          <!-- 入库日期 -->
          <el-descriptions-item label="入库日期">{{ setCurrentRow && setCurrentRow.INSTOCKDATE && parseTime(setCurrentRow.INSTOCKDATE, '{y}-{m}-{d}') }}</el-descriptions-item>
        </el-descriptions>
        <el-descriptions border :column="3" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 3 - 7em - 22px)' }" v-show="detailActiveName === 'Dthird'">
          <!-- 物料编码 -->
          <el-descriptions-item label="物料编码">{{ setCurrentRow && setCurrentRow.MaterialId && setCurrentRow.MaterialId.Number }}</el-descriptions-item>
          <!-- 物料名称 -->
          <el-descriptions-item label="物料名称">{{ setCurrentRow && setCurrentRow.MaterialId && getString(setCurrentRow.MaterialId.Name) }}</el-descriptions-item>
          <!-- 源单类型 -->
          <el-descriptions-item label="源单类型">{{ setCurrentRow && setCurrentRow.SrcBillTypeID && getOptionLabel(SrcBillTypeIDOptions, setCurrentRow.SrcBillTypeID) }}</el-descriptions-item>
          <!-- 源单单号 -->
          <el-descriptions-item label="源单单号">{{ setCurrentRow && setCurrentRow.SrcBillNo }}</el-descriptions-item>
          <!-- 订单单号 -->
          <el-descriptions-item label="订单单号">{{ setCurrentRow && setCurrentRow.OrderNo }}</el-descriptions-item>
          <!-- 关联应收状态 -->
          <el-descriptions-item label="关联应收状态">{{ setCurrentRow && setCurrentRow.RowARStatus && getOptionLabel(ARStatusOptions, setCurrentRow.RowARStatus) }}</el-descriptions-item>
          <!-- 累计应收数量 -->
          <el-descriptions-item label="累计应收数量">{{ (setCurrentRow && setCurrentRow.SumInvoicedQty) || '' }}</el-descriptions-item>
          <!-- 消耗汇总 -->
          <el-descriptions-item label="消耗汇总">{{ setCurrentRow && setCurrentRow.IsConsumeSum && getOptionLabel(ConsumeSumOptions, setCurrentRow.IsConsumeSum) }}</el-descriptions-item>
          <!-- 业务流程 -->
          <el-descriptions-item label="业务流程">{{ setCurrentRow && setCurrentRow.FBFLowId && getString(setCurrentRow.FBFLowId.Name) }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
    <!-- 下推弹出框 -->
    <el-dialog v-dialogDragBox title="选择单据" :visible.sync="pushOpen" width="580px" class="custom-dialog">
      <div style="padding: 0 20px">
        <el-row :gutter="10" class="custom-push-target">
          <el-radio-group v-model="pushForm.target" v-removeAriaHidden>
            <el-col :span="12" v-for="item in pushTarget" :key="item.value">
              <el-radio :label="item.value">{{ item.label }}</el-radio>
            </el-col>
          </el-radio-group>
        </el-row>
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn small" @click="pushOpen = false">取消</el-button>
        <el-button class="custom-dialog-btn small primary" :class="{ disabled: !pushForm.target }" :disabled="!pushForm.target" @click="handlePushSubmit">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { getSalesReturnDetail, auditSalesReturn, revokeSalesReturn, deleteSalesReturn, pushSalesReturn, submitSalesReturn, unauditSalesReturn } from '@/api/kingdee/sell/salesReturn'
import { kingdee } from '@/minix'

export default {
  mixins: [kingdee],
  data() {
    return {
      open: false,
      info: {},
      setCurrentRow: {},
      // 下推
      pushOpen: false,
      pushTarget: [
        { value: 'STK_InStock', label: '采购入库单' },
        { value: 'SAL_RETURNNOTICE', label: '退货通知单' },
        { value: 'SAL_RETURNSTOCK', label: '销售退货单' }
      ],
      pushForm: {
        number: undefined,
        target: undefined
      },
      activeName: 'first',
      detailActiveName: 'Dfirst',
      // 作废状态
      CancelStatusOptions: [
        { value: 'A', label: '否' },
        { value: 'B', label: '是' }
      ],
      // 关联应收状态
      ARStatusOptions: [
        { value: 'B', label: '部分应收' },
        { value: 'C', label: '已完全应收' }
      ],
      // 货主类型
      OwnerTypeIDOptions: [
        { label: '业务组织', value: 'BD_OwnerOrg' },
        { label: '供应商', value: 'BD_Supplier' },
        { label: '客户', value: 'BD_Customer' }
      ],
      // 保管者类型
      KeeperTypeIDOptions: [
        { label: '业务组织', value: 'BD_KeeperOrg' },
        { label: '供应商', value: 'BD_Supplier' },
        { label: '客户', value: 'BD_Customer' }
      ],
      // 源单类型
      SrcBillTypeIDOptions: [
        { value: 'CP_BBCReturnReq', label: 'BBC退货申请单' },
        { value: 'CRM_RepairOrder', label: '服务工单' },
        { value: 'ECC_AfterSaleOrder', label: '售后申请单' },
        { value: 'ESS_ChannelOutStock', label: '渠道出库单' },
        { value: 'ESS_ChannelReq', label: '渠道调拨申请单' },
        { value: 'ESS_RetailReturnReq', label: '销售开单退货' },
        { value: 'SAL_ConsignmentSettle', label: '寄售结算单' },
        { value: 'SAL_INITOUTSTOCK', label: '期初销售出库单' },
        { value: 'SAL_OUTSTOCK', label: '销售出库单' },
        { value: 'SAL_RETURNNOTICE', label: '退货通知单' },
        { value: 'SAL_RETURNSTOCK', label: '销售退货单' },
        { value: 'SAL_SaleOrder', label: '销售订单' }
      ],
      // 消耗汇总
      ConsumeSumOptions: [
        { value: '0', label: '否' },
        { value: '1', label: '是' }
      ],
      buttons: [
        { text: '提交', type: 'primary', action: 'submit', status: ['A', 'D'] },
        { text: '审核', type: 'warning', action: 'audit', status: ['B'] },
        { text: '撤销', type: 'info', action: 'revoke', status: ['B'] },
        { text: '删除', type: 'danger', action: 'delete', status: ['A', 'D'] },
        { text: '反审', type: 'warning', action: 'unAudit', status: ['C'] }
      ]
    }
  },
  computed: {
    // VIM业务
    VmiBusinessStatus() {
      return this.setCurrentRow?.VmiBusinessStatus || false
    }
  },
  methods: {
    // 检查状态
    checkStatus(allowedStatus) {
      return allowedStatus.includes(this.info.DocumentStatus)
    },
    // 切换tab
    handleClick(tab, event) {
      this.activeName = tab.name
    },
    handleDetailClick(tab, event) {
      this.detailActiveName = tab.name
    }, // 获取详情
    getInfo(row = {}) {
      if (!row.BillNo) {
        this.$message.error('参数错误，请重新选择')
        return
      }
      getSalesReturnDetail({ billNo: row.BillNo }).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          const { result } = data
          this.activeName = 'first'
          this.detailActiveName = 'Dfirst'
          this.open = !!result?.result
          this.info = result?.result || {}
          this.$nextTick(() => {
            this.setCurrentRow = result?.result?.SAL_RETURNSTOCKENTRY?.[0] || {}
            if (this.$refs.detailTable) {
              this.$refs.detailTable.setCurrentRow(this.setCurrentRow)
              this.$refs.detailTable.bodyWrapper.scrollLeft = 0
            }
          })
        } else this.$message.error(msg)
      })
    },
    // 当前行
    handleCurrentChange(val) {
      this.setCurrentRow = val
    },
    beforeClose() {
      this.handleClose()
      this.$emit('update', { billNo: this.info.BillNo, newStatus: this.info.DocumentStatus })
    },
    // 关闭
    handleClose(flag = false) {
      this.open = false
      if (flag) this.$emit('callBack')
    },
    // 金蝶操作
    // prettier-ignore
    handleKingdeeDo(type) {
      const number = this.info.BillNo
      if (!number) return
      switch (type) {
        case 'submit':
          // 提交
          this.$modal.confirm('确认要提交该销售退货单吗？').then(() => {
            submitSalesReturn({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('提交成功')
                this.$set(this.info, 'DocumentStatus', 'B')
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'audit':
          // 审核
          this.$modal.confirm('确认要审核该销售退货单吗？').then(() => {
            auditSalesReturn({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('审核成功')
                this.$set(this.info, 'DocumentStatus', 'C')
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'revoke':
          // 撤销
          this.$modal.confirm('确认要撤销该销售退货单吗？').then(() => {
            revokeSalesReturn({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('撤销成功')
                this.$set(this.info, 'DocumentStatus', 'D')
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'unAudit':
          // 反审核
          this.$modal.confirm('确认要反审核该销售退货单吗？').then(() => {
            unauditSalesReturn({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('反审核成功')
                this.$set(this.info, 'DocumentStatus', 'D')
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'delete':
          // 删除
          this.$modal.confirm('确认要删除该销售退货单吗？').then(() => {
            deleteSalesReturn({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('删除成功')
                this.handleClose(true)
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'push':
          // 下推
          this.pushForm = {
            number: this.info.BillNo,
            target: undefined
          }
          this.pushOpen = true
          break
      }
    },
    // 下推提交
    handlePushSubmit() {
      const { number, target } = this.pushForm
      if (!number || !target) return
      pushSalesReturn({ number, target }).then(res => {
        const { code, msg } = res
        if (code === 200) {
          this.$message.success('下推成功')
          this.pushOpen = false
          this.handleClose(true)
        } else if (code === 400) {
          this.$alert(msg || '操作失败，请重试或使用金蝶进行操作！', '提示', {
            type: 'info',
            confirmButtonText: '确定',
            callback: action => {
              this.pushOpen = false
            }
          })
        } else this.$message.error(msg)
      })
    }
  }
}
</script>
<style scoped lang="scss">
@import '@/assets/styles/custom-new.scss';
::v-deep {
  .el-descriptions__header {
    margin-bottom: 0;
  }
  .el-table.custom-table .el-table__body-wrapper .el-table__row:hover td.el-table__cell {
    background-color: #eaf1fe !important;
  }
}
.customTitle {
  font-size: 16px;
  font-weight: normal;
  line-height: 60px;
  color: $font;
}
.kindeeButton {
  display: flex;
  align-items: center;
}
.custom-push-target {
  .el-col.el-col-12 {
    .el-radio {
      height: 30px;
      display: flex;
      align-items: center;
    }
  }
}
.custom-tabs {
  margin-top: 15px;
  ::v-deep {
    .el-tabs__header {
      .el-tabs__nav {
        border: 0;
      }
      .el-tabs__item {
        background-color: #eef2f8;
        border-radius: 5px 5px 0 0;
        border-left-width: 0;
        border-right-width: 0;
        &.is-active {
          background-color: $blue;
          color: $white;
        }
      }
      .el-tabs__item + .el-tabs__item {
        margin-left: 2px;
      }
    }
  }
}
</style>
