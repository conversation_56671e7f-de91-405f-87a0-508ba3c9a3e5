<template>
  <div>
    <el-dialog v-dialogDragBox :title="title" :visible.sync="open" width="90%" class="custom-dialog" :before-close="beforeClose" append-to-body>
      <div style="padding: 0 20px">
        <el-form :model="form" :rules="rules" ref="form" label-width="6em" class="custom-form">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="单据类型" prop="fbilltypeid">
                <el-select v-model="form.fbilltypeid" placeholder="请选择单据类型" style="width: 100%">
                  <el-option v-for="item in billTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 日期 -->
            <el-col :span="6">
              <el-form-item label="日期" prop="fdate">
                <el-date-picker v-model="form.fdate" type="date" placeholder="请选择日期" style="width: 100%"></el-date-picker>
              </el-form-item>
            </el-col>
            <!-- 结算币别 -->
            <el-col :span="6">
              <el-form-item label="结算币别" prop="fcurrencyid">
                <el-select v-model="form.fcurrencyid" placeholder="请选择结算币别" style="width: 100%">
                  <el-option v-for="item in CurrencyId" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 退货客户 -->
            <el-col :span="6">
              <el-form-item label="退货客户" prop="fretcustname">
                <customer-search-select :useOrg="form.fsaleorgid" :showLabel="false" :keyword.sync="form.fretcustname" style="width: 100%" isBack @callBack="handleFormCustomer($event)" />
              </el-form-item>
            </el-col>
            <!-- 库存组织 -->
            <el-col :span="6">
              <el-form-item label="库存组织" prop="fstockorgid">
                <el-select v-model="form.fstockorgid" filterable placeholder="请选择库存组织" style="width: 100%" @change="handleChangeStockOrg">
                  <el-option v-for="item in ApplicationOrgId" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 库存部门 -->
            <el-col :span="6">
              <el-form-item label="库存部门" prop="fstockdeptid">
                <el-select v-model="form.fstockdeptid" filterable placeholder="请选择库存部门" style="width: 100%" @change="handleChangeStockDept">
                  <el-option v-for="item in ApplicationDeptId.filter(ite => ite.FUseOrg == form.fstockorgid)" :key="item.FNumber" :label="item.FName" :value="item.FNumber"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 仓管员 -->
            <el-col :span="6">
              <el-form-item label="仓管员" prop="fstockerid">
                <el-select v-model="form.fstockerid" filterable placeholder="请选择仓管员" style="width: 100%" :disabled="!form.fstockorgid || !form.fstockdeptid">
                  <el-option v-for="item in calculateStockerList" :key="item.Number" :label="item.Name" :value="item.Number">
                    <span style="float: left">{{ item.Name }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.FDept }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24"></el-col>
            <!-- 销售组织 -->
            <el-col :span="6">
              <el-form-item label="销售组织" prop="fsaleorgid">
                <el-select v-model="form.fsaleorgid" filterable placeholder="请选择销售组织" style="width: 100%" @change="handleChangeSaleOrg">
                  <el-option v-for="item in ApplicationOrgId" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 销售部门 -->
            <el-col :span="6">
              <el-form-item label="销售部门" prop="fsaledeptid">
                <el-select v-model="form.fsaledeptid" filterable placeholder="请选择销售部门" style="width: 100%" @change="handleChangeDept">
                  <el-option v-for="item in ApplicationDeptId.filter(ite => ite.FUseOrg == form.fsaleorgid)" :key="item.FNumber" :label="item.FName" :value="item.FNumber"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 销售员 -->
            <el-col :span="6">
              <el-form-item label="销售员" prop="fsalesmanid">
                <el-select v-model="form.fsalesmanid" filterable placeholder="请选择销售员" :disabled="!form.fsaleorgid || !form.fsaledeptid" style="width: 100%">
                  <el-option v-for="item in calculateApplicantList" :key="item.Number" :label="item.Name" :value="item.Number">
                    <span style="float: left">{{ item.Name }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.FDept }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24"></el-col>
            <!-- 备注 -->
            <el-col :span="12">
              <el-form-item label="备注" prop="fheadnote">
                <el-input v-model="form.fheadnote" placeholder="请输入备注" style="width: 100%"></el-input>
              </el-form-item>
            </el-col>
            <!-- 明细信息 -->
            <el-col :span="24">
              <el-table :data="form.entities" style="width: 100%" stripe class="custom-table custom-table-cell0" show-summary :summary-method="getSummary">
                <!-- 序号 -->
                <el-table-column type="index" label="序号" width="50" align="center"></el-table-column>
                <!-- 物料编码 -->
                <el-table-column label="物料编码" align="center" show-overflow-tooltip>
                  <template slot-scope="{ row }">
                    <span class="table-link" @click="handleMaterialNumber(row.fmaterialid)">{{ row.fmaterialid }}</span>
                  </template>
                </el-table-column>
                <!-- 物料名称 -->
                <el-table-column label="物料名称" align="center" show-overflow-tooltip>
                  <template slot-scope="{ row }">{{ row.fmaterialname }}</template>
                </el-table-column>
                <!-- 规格型号 -->
                <el-table-column label="规格型号" align="center" show-overflow-tooltip>
                  <template slot-scope="{ row }">{{ row.fmaterialmodel }}</template>
                </el-table-column>
                <!-- 库存单位 -->
                <el-table-column prop="funitid" label="库存单位" align="center">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.funitid`" :rules="rules.funitid">
                      <el-select v-model="scope.row.funitid" placeholder="请选择库存单位" style="width: 100%" size="small">
                        <el-option v-for="item in UnitList" :key="item.FNumber" :label="item.FName" :value="item.FNumber"></el-option>
                      </el-select>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 应退数量 -->
                <el-table-column prop="fshouldqty" label="应退数量" align="center"></el-table-column>
                <!-- 实退数量 -->
                <el-table-column prop="frealqty" label="实退数量" align="center">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.frealqty`" :rules="rules.frealqty">
                      <el-input v-model="scope.row.frealqty" :precision="2" :min="0" placeholder="实退数量" style="width: 100%" size="small" @change="handleQtyChange(scope.row)"></el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 是否赠品 -->
                <el-table-column prop="fisfree" label="是否赠品" align="center">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.fisfree`" :rules="rules.fisfree">
                      <el-checkbox v-model="scope.row.fisfree" size="small"></el-checkbox>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 退货类型 -->
                <el-table-column prop="freturn" label="退货类型" align="center" min-width="100">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.freturn`" :rules="rules.freturn">
                      <el-select v-model="scope.row.freturn" placeholder="请选择退货类型" style="width: 100%" size="small">
                        <el-option v-for="item in returnTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                      </el-select>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 仓库 -->
                <el-table-column prop="fstockname" label="仓库" align="center" min-width="130">
                  <template slot-scope="scope">
                    <el-tooltip effect="dark" :content="scope.row.fstockname" placement="top" :disabled="!scope.row.fstockname">
                      <el-form-item label-width="0" :prop="`entities.${scope.$index}.fstockname`" :rules="rules.fstockname">
                        <stock-search-select :keyword.sync="scope.row.fstockname" :useOrg="form.fstockorgid" :showLabel="false" style="width: 100%" size="small" isBack @callBack="handleStockSelect($event, scope.row)" />
                      </el-form-item>
                    </el-tooltip>
                  </template>
                </el-table-column>
                <!-- 仓位 -->
                <!-- <el-table-column prop="fstocklocid" label="仓位" align="center" show-overflow-tooltip>
                  <template slot-scope="scope">{{ scope.row.fstocklocid && getString(scope.row.fstocklocid.Name) }}</template>
                </el-table-column> -->
                <!-- 库存状态 -->
                <el-table-column prop="fstockstatusid" label="库存状态" align="center" min-width="100">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.fstockstatusid`" :rules="rules.fstockstatusid">
                      <el-select v-model="scope.row.fstockstatusid" placeholder="请选择库存状态" style="width: 100%" size="small">
                        <el-option v-for="item in stockStatusOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                      </el-select>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 退货日期 -->
                <el-table-column prop="fdeliverydate" label="退货日期" align="center" min-width="135">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.fdeliverydate`" :rules="rules.fdeliverydate">
                      <el-date-picker v-model="scope.row.fdeliverydate" type="date" placeholder="请选择退货日期" style="width: 100%" size="small"></el-date-picker>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 备注 -->
                <el-table-column prop="fnote" label="备注" align="center">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.fnote`" :rules="rules.fnote">
                      <el-input v-model="scope.row.fnote" placeholder="请输入备注" style="width: 100%" size="small"></el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 结算单价 -->
                <el-table-column prop="fscmjjsprice" label="结算单价" align="center">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.fscmjjsprice`" :rules="rules.fscmjjsprice">
                      <el-input v-model="scope.row.fscmjjsprice" :precision="2" :min="0" placeholder="结算单价" style="width: 100%" size="small" @change="handleScmjJspriceChange(scope.row)"></el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 结算金额 -->
                <el-table-column prop="fscmjjsamount" label="结算金额" align="center"></el-table-column>
                <!-- 操作 -->
                <el-table-column label="操作" align="center" width="100">
                  <template slot-scope="scope">
                    <el-button type="text" size="small" @click="handleDelete(scope.$index)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn" @click="handleClose()">取消</el-button>
        <el-button class="custom-dialog-btn primary" :loading="loading" @click="handleSubmit">确定</el-button>
      </div>
    </el-dialog>
    <!-- 销售退货单详情 -->
    <sales-return-detail ref="salesReturnDetail" />
  </div>
</template>
<script>
import { kingdee } from '@/minix'
import CustomerSearchSelect from '@/components/SearchSelect/customer'
import StockSearchSelect from '@/components/SearchSelect/stock'
import { getSalesReturnDetail2, addSalesReturn, deleteSalesReturnV2 } from '@/api/kingdee/sell/salesReturn'
import { getSalesmanList } from '@/api/kingdee'
import { isNumber, isNumberLength } from '@/utils/validate'
import SalesReturnDetail from '@/views/kingdee/sell/salesReturn/detail'

export default {
  name: 'SalesReturnCreate',
  mixins: [kingdee],
  components: { CustomerSearchSelect, StockSearchSelect, SalesReturnDetail },
  data() {
    return {
      fid: undefined,
      form: {},
      rules: {
        fbilltypeid: [{ required: true, message: '请选择单据类型', trigger: 'change' }],
        fstockorgid: [{ required: true, message: '请选择库存组织', trigger: 'change' }],
        fsaleorgid: [{ required: true, message: '请选择销售组织', trigger: 'change' }],
        fdate: [{ required: true, message: '请选择日期', trigger: 'change' }],
        fcurrencyid: [{ required: true, message: '请选择结算币别', trigger: 'change' }],
        fretcustname: [{ required: true, message: '请选择退货客户', trigger: 'change' }],
        funitid: [{ required: true, message: '请选择库存单位', trigger: 'change' }],
        frealqty: [
          { required: true, message: '请输入实退数量', trigger: ['change', 'blur'] },
          { validator: isNumber, message: '请输入正确的数量', trigger: ['change', 'blur'] },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: ['change', 'blur'] }
        ],
        freturn: [{ required: true, message: '请选择退货类型', trigger: 'change' }],
        fstockname: [{ required: true, message: '请选择仓库', trigger: 'change' }],
        fstockstatusid: [{ required: true, message: '请选择库存状态', trigger: 'change' }],
        fdeliverydate: [{ required: true, message: '请选择退货日期', trigger: 'change' }],
        fscmjjsprice: [{ required: true, message: '请输入结算单价', trigger: ['change', 'blur'] }]
      },
      title: '',
      open: true,
      loading: false,
      // 单据类型
      billTypeOptions: [
        { value: 'XSTHD01_SYS', label: '标准销售退货单' },
        { value: 'XSTHD02_SYS', label: '寄售退货单' },
        { value: 'XSTHD03_SYS', label: '零售退货单' },
        { value: 'XSTHD04_SYS', label: '分销购销销售退货单' },
        { value: 'XSTHD05_SYS', label: 'VMI销售退货单' },
        { value: 'XSTHD06_SYS', label: '现销退货单' },
        { value: 'XSTHD07_SYS', label: 'B2C销售退货单' },
        { value: 'XSTHD08_SYS', label: 'BBC销售退货单' },
        { value: 'XSTHD09_SYS', label: '直运销售退货单' }
      ],
      // 退货类型
      returnTypeOptions: [
        { value: 'THLX01_SYS', label: '退货' },
        { value: 'THLX02_SYS', label: '退货补货' },
        { value: 'THLX03_SYS', label: '仅退款不退货' }
      ],
      // 库存状态
      stockStatusOptions: [
        { value: 'KCZT01_SYS', label: '可用' },
        { value: 'KCZT02_SYS', label: '待检' },
        { value: 'KCZT03_SYS', label: '冻结' },
        { value: 'KCZT04_SYS', label: '退回冻结' },
        { value: 'KCZT07_SYS', label: '废品' },
        { value: 'KCZT08_SYS', label: '不良' },
        { value: 'KCZT09_SYS', label: '已包装' },
        { value: 'KCZT099_SYS', label: '外借' }
      ],
      // 销售员列表
      applicantList: [],
      // 仓管员列表
      stockerList: [],
      isPush: false,
      hasSuccessfully: false // 是否已成功提交
    }
  },
  computed: {
    calculateApplicantList() {
      const dept = this.ApplicationDeptId.find(ite => ite.FNumber == this.form.fsaledeptid) || {}
      const deptName = dept.FName || ''
      return this.applicantList.filter(ite => ite.FDept == deptName)
    },
    calculateStockerList() {
      const dept = this.ApplicationDeptId.find(ite => ite.FNumber == this.form.fstockdeptid) || {}
      const deptName = dept.FName || ''
      const list = this.stockerList.filter(ite => ite.FDept == deptName)
      // 处理仓管员编号，只取前三位数字
      const processedList = list.map(item => ({
        ...item,
        Number: item.Number ? item.Number.split('_')[0] : item.Number
      }))
      return processedList
    }
  },
  methods: {
    // 销售员远程方法
    ApplicantRemoteMethod() {
      const params = { bizOrg: this.form.fsaleorgid, OperatorType: 'XSY' }
      getSalesmanList(params).then(res => {
        const { code, msg, data } = res
        const { data: applicantList } = data
        if (code == 200) {
          this.applicantList = applicantList
        } else this.$message.error(msg)
      })
    },
    // 仓管员远程方法
    StockerRemoteMethod() {
      const params = { bizOrg: this.form.fstockorgid, OperatorType: 'WHY' }
      getSalesmanList(params).then(res => {
        const { code, msg, data } = res
        const { data: stockerList } = data
        if (code == 200) {
          this.stockerList = stockerList
        } else this.$message.error(msg)
      })
    },
    // 改变库存组织
    handleChangeStockOrg() {
      this.form.fstockdeptid = undefined // 清空库存部门
      this.form.fstockerid = undefined // 清空仓管员
      this.StockerRemoteMethod()
    },
    // 改变销售组织
    handleChangeSaleOrg() {
      this.form.fsaledeptid = undefined // 清空销售部门
      this.form.fsalesmanid = undefined // 清空销售员
      this.ApplicantRemoteMethod()
    },
    // 改变销售部门
    handleChangeDept() {
      this.form.fsalesmanid = undefined // 清空销售员
    },
    // 改变库存部门
    handleChangeStockDept() {
      this.form.fstockerid = undefined // 清空仓管员
    },
    // 选择仓库
    handleStockSelect(stock, row) {
      this.$set(row, 'fstockid', stock.Number)
      this.$set(row, 'fstockname', stock.Name)
    },
    // 退货客户
    handleFormCustomer(data) {
      this.$set(this.form, 'fcustid', data.id)
      this.$set(this.form, 'fcustname', data.name)
    },
    // 关闭
    beforeClose() {
      this.handleClose()
    },
    // 关闭
    async handleClose(flag = false) {
      if (this.fid && !this.isCreate && !this.hasSuccessfully) {
        try {
          await deleteSalesReturnV2({ id: this.fid })
          this.fid = undefined
          this.$nextTick(() => {
            this.open = false
            this.$emit('callBack', flag)
          })
        } catch (error) {
          this.$message.error(error.message)
        }
      } else {
        this.open = false
        this.$emit('callBack', flag)
      }
    },
    // 表单初始化
    reset() {
      this.form = {
        entities: [],
        fbilltypeid: undefined,
        fdate: undefined,
        fheadlocid: undefined,
        fheadnote: undefined,
        fid: undefined,
        fretcustid: undefined,
        freturnreason: undefined,
        fsaledeptid: undefined,
        fsaleorgid: undefined,
        fsalesmanid: undefined,
        fscmjsj: undefined,
        fscmjxsddh: undefined,
        fsettlecurrid: undefined,
        fstockdeptid: undefined,
        fstockerid: undefined,
        fstockorgid: undefined,
        traceDetails: []
      }
      this.resetForm('form')
      this.isPush = false
      this.hasSuccessfully = false
    },
    // 初始化
    // prettier-ignore
    initPush(fid, type = undefined) {
      if (!fid) return this.$message.warning('参数错误，请刷新页面重试')
      const loading = this.$loading({
        lock: true,
        text: '加载中...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      this.reset()
      this.isPush = type == 'push'
      getSalesReturnDetail2({ fid }).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          const info = data?.result?.result || {}
          const detailEntities = info?.SAL_RETURNSTOCKENTRY || []
          const entities = detailEntities.map(item => {
            return {
              fentryid: item?.Id || undefined, // 使用时需检查
              fmaterialid: item?.MaterialId?.Number || undefined, // 物料编码
              fmaterialname: (item?.MaterialId?.Name && this.getString(item?.MaterialId?.Name)) || undefined, // 物料名称
              fmaterialmodel: (item?.MaterialId?.Specification && this.getString(item?.MaterialId?.Specification)) || undefined, // 规格型号
              funitid: item?.UnitId?.Number || undefined, // 库存单位
              fshouldqty: item?.Mustqty ? parseFloat(item.Mustqty) : undefined, // 应退数量
              frealqty: item?.RealQty ? parseFloat(item.RealQty) : undefined, // 实退数量
              fisfree: item?.IsFree || false, // 是否赠品
              freturn: item?.ReturnType?.FNumber || undefined, // 退货类型
              fstockid: item?.StockId?.Number || undefined, // 仓库
              fstockname: (item?.StockId?.Name && this.getString(item?.StockId.Name)) || undefined, // 仓库名称
              fstocklocid: item?.StockLocId?.Number || undefined, // 仓位
              fstockstatusid: item?.StockstatusId?.Number || undefined, // 库存状态
              fdeliverydate: item?.DeliveryDate || undefined, // 退货日期
              fnote: item?.Note || undefined, // 备注
              fscmjjsprice: item?.F_SCMJ_JSPrice ? parseFloat(item.F_SCMJ_JSPrice) : undefined, // 结算单价
              fscmjjsamount: item?.F_SCMJ_JSAmount ? parseFloat(item.F_SCMJ_JSAmount) : item?.F_SCMJ_JSPrice && item?.Qty ? parseFloat((parseFloat(item.F_SCMJ_JSPrice) * parseFloat(item.Qty)).toFixed(5)) : undefined // 结算金额
            }
          })
          const fcurrencyidArr = info?.SAL_RETURNSTOCKFIN || []
          const fcurrencyid = fcurrencyidArr.length > 0 ? fcurrencyidArr[0]?.SettleCurrId?.Number : undefined
          this.form = {
            entities, // 明细信息
            fbilltypeid: info?.BillTypeID?.Number || undefined, // 单据类型
            fdate: info?.Date || undefined, // 日期
            fcurrencyid, // 结算币别
            fretcustid: info?.RetcustId?.Number || undefined, // 退货客户
            fretcustname: info?.RetcustId && this.getString(info?.RetcustId.Name), // 退货客户名称
            fstockorgid: info?.StockOrgId?.Number || undefined, // 库存组织
            fstockdeptid: info?.StockDeptId?.Number || undefined, // 库存部门
            fstockerid: info?.StockerId?.Number || undefined, // 仓管员
            // fstockername: info?.StockerId && this.getString(info?.StockerId.Name), // 仓管员名称
            fsaleorgid: info?.SaleOrgId?.Number || undefined, // 销售组织
            fsaledeptid: info?.Sledeptid?.Number || undefined, // 销售部门
            fsalesmanid: info?.SalesManId?.Number || undefined, // 销售员
            // fsalesmanname: info?.SalesManId && this.getString(info?.SalesManId.Name), // 销售员名称
            fheadnote: info?.FNote || undefined, // 备注
            fid // 单据ID
          }
          this.fid = fid
          this.title = '新增销售退货单'
          this.$nextTick(() => {
            this.ApplicantRemoteMethod()
            this.StockerRemoteMethod()
          })
          this.open = true
        } else this.$message.error(msg)
      }).finally(() => {
        loading.close()
      })
    },
    // 改变实退数量计算结算金额
    handleQtyChange(row) {
      const qty = parseFloat(row?.frealqty) || 0
      const price = parseFloat(row?.fscmjjsprice) || 0
      this.$set(row, 'fscmjjsamount', qty && price ? parseFloat((price * qty).toFixed(5)) : undefined)
    },
    // 改变结算单价计算结算金额
    handleScmjJspriceChange(row) {
      const qty = parseFloat(row?.frealqty) || 0
      const price = parseFloat(row?.fscmjjsprice) || 0
      this.$set(row, 'fscmjjsamount', qty && price ? parseFloat((price * qty).toFixed(5)) : undefined)
    },
    // 获取合计
    getSummary(param) {
      const { columns, data } = param
      const result = columns.map((column, index) => {
        if (index === 0) {
          return '合计'
        }
        if (column.property === 'fshouldqty' || column.property === 'frealqty' || column.property === 'fscmjjsamount') {
          return `${this.calculateTotal(data, column.property)}`
        }
        return ''
      })
      return result
    },
    // 计算合计
    calculateTotal(data, key) {
      return data.reduce((total, item) => {
        if (key === 'fshouldqty') {
          const value = parseFloat(item.fshouldqty) || 0
          return parseFloat((total + value).toFixed(5))
        } else if (key === 'frealqty') {
          const value = parseFloat(item.frealqty) || 0
          return parseFloat((total + value).toFixed(5))
        } else if (key === 'fscmjjsamount') {
          const value = parseFloat(item.fscmjjsamount) || 0
          return parseFloat((total + value).toFixed(5))
        }
        return total
      }, 0)
    },
    // 删除
    handleDelete(index) {
      this.form.entities.splice(index, 1)
    },
    // 提交
    // prettier-ignore
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true
          addSalesReturn(this.form).then(res => {
            const { code, msg } = res
            if (code === 200) {
              this.$message.success('操作成功')
              this.hasSuccessfully = true
              if (this.isPush) {
                const { number } = res.data
                if (number) {
                  this.open = false
                  this.$nextTick(() => {
                    this.$refs.salesReturnDetail.getInfo({ BillNo: number })
                  })
                } else {
                  this.open = false
                  this.$emit('callBack', true)
                }
              } else {
                this.open = false
                this.$emit('callBack', true)
              }
            } else this.$message.error(msg)
          }).finally(() => {
            this.loading = false
          })
        }
      })
    },
    // 物料详情
    handleMaterialNumber(number) {
      if (number) this.$parent.handleMaterialNumber(number)
    }
  }
}
</script>
<style scoped lang="scss">
@import '@/assets/styles/custom-new.scss';
::v-deep {
  .custom-table {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    border-bottom: 0;
    margin-bottom: 20px;
    .el-form-item {
      margin-top: 10px !important;
      margin-bottom: 10px !important;
      .el-form-item__error {
        top: 95%;
        padding-top: 0;
      }
    }
    .el-input--prefix .el-input__inner {
      padding-left: 20px;
      padding-right: 20px;
    }
    .el-input--prefix.el-date-editor--date .el-input__inner {
      padding-left: 30px;
    }
    .el-date-editor--datetime .el-input__inner {
      padding-left: 30px;
      padding-right: 30px;
    }
  }
}
</style>
