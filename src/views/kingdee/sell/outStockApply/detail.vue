<template>
  <div>
    <el-dialog v-dialogDragBox title="出库申请单详情" :visible.sync="open" width="90%" class="custom-dialog" :before-close="beforeClose">
      <div style="padding: 0 20px">
        <div class="kindeeButton" :key="info.DocumentStatus">
          <template v-for="(btn, index) in buttons">
            <el-button :key="index" :type="btn.type" size="medium" v-if="checkStatus(btn.status)" @click="handleKingdeeDo(btn.action)">{{ btn.text }}</el-button>
            <el-button :key="'disabled-' + index" :type="btn.type" size="medium" disabled v-else>{{ btn.text }}</el-button>
          </template>
        </div>
        <el-tabs v-model="activeName" type="card" @tab-click="handleClick" class="custom-tabs">
          <el-tab-pane label="基本信息" name="first"></el-tab-pane>
          <el-tab-pane label="其他" name="second"></el-tab-pane>
        </el-tabs>
        <el-descriptions border :column="4" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 4 - 7em - 22px)' }" v-show="activeName === 'first'">
          <!-- 单据编号 -->
          <el-descriptions-item label="单据编号">{{ info.BillNo }}</el-descriptions-item>
          <!-- 单据类型 -->
          <el-descriptions-item label="单据类型">{{ info.BillTypeID && getString(info.BillTypeID.Name) }}</el-descriptions-item>
          <!-- 业务类型 -->
          <el-descriptions-item label="业务类型">{{ info.BizType && getOptionLabel(BizTypeOptions, info.BizType) }}</el-descriptions-item>
          <!-- 申请日期 -->
          <el-descriptions-item label="申请日期">{{ info.Date && parseTime(info.Date, '{y}-{m}-{d}') }}</el-descriptions-item>
          <!-- 单据状态 -->
          <el-descriptions-item label="单据状态">{{ info.DocumentStatus && getDocumentStatusLabel(info.DocumentStatus) }}</el-descriptions-item>
          <!-- 申请组织 -->
          <el-descriptions-item label="申请组织">{{ info.StockOrgId && getString(info.StockOrgId.Name) }}</el-descriptions-item>
          <!-- 领用部门 -->
          <el-descriptions-item label="领用部门">{{ info.DeptId && getString(info.DeptId.Name) }}</el-descriptions-item>
          <!-- 客户 -->
          <el-descriptions-item label="客户">{{ info.CustId && getString(info.CustId.Name) }}</el-descriptions-item>
          <!-- 备注 -->
          <el-descriptions-item label="备注">{{ info.Note }}</el-descriptions-item>
          <!-- 申领人 -->
          <el-descriptions-item label="申领人">{{ info.F_SCMJ_Base && getString(info.F_SCMJ_Base.Name) }}</el-descriptions-item>
          <!-- 申请类型 -->
          <el-descriptions-item label="申请类型">{{ info.ApplyType && getString(info.ApplyType.FDataValue) }}</el-descriptions-item>
          <!-- 货主类型 -->
          <el-descriptions-item label="货主类型">{{ info.FOwnerTypeIdHead && getOptionLabel(FOwnerTypeIdHeadOptions, info.FOwnerTypeIdHead) }}</el-descriptions-item>
        </el-descriptions>
        <el-descriptions border :column="3" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 3 - 7em - 22px)' }" v-show="activeName === 'second'">
          <!-- 创建人 -->
          <el-descriptions-item label="创建人">{{ info.CreatorId && info.CreatorId.Name }}</el-descriptions-item>
          <!-- 创建日期 -->
          <el-descriptions-item label="创建日期">{{ info.CreateDate && parseTime(info.CreateDate, '{y}-{m}-{d}') }}</el-descriptions-item>
          <!-- 最后修改人 -->
          <el-descriptions-item label="最后修改人">{{ info.ModifierId && info.ModifierId.Name }}</el-descriptions-item>
          <!-- 最后修改日期 -->
          <el-descriptions-item label="最后修改日期">{{ info.ModifyDate && parseTime(info.ModifyDate, '{y}-{m}-{d}') }}</el-descriptions-item>
          <!-- 审核人 -->
          <el-descriptions-item label="审核人">{{ info.ApproverId && info.ApproverId.Name }}</el-descriptions-item>
          <!-- 审核日期 -->
          <el-descriptions-item label="审核日期">{{ info.ApproveDate && parseTime(info.ApproveDate, '{y}-{m}-{d}') }}</el-descriptions-item>
          <!-- 关闭状态 -->
          <el-descriptions-item label="关闭状态">{{ info.FCloseStatus && info.FCloseStatus === 'A' ? '未关闭' : '已关闭' }}</el-descriptions-item>
          <!-- 关闭人 -->
          <el-descriptions-item label="关闭人">{{ info.FCloserId && info.FCloserId.Name }}</el-descriptions-item>
          <!-- 关闭日期 -->
          <el-descriptions-item label="关闭日期">{{ info.FCloseDate && parseTime(info.FCloseDate, '{y}-{m}-{d}') }}</el-descriptions-item>
          <!-- 作废状态 -->
          <el-descriptions-item label="作废状态">{{ info.CancelStatus && info.CancelStatus === 'A' ? '未作废' : '已作废' }}</el-descriptions-item>
          <!-- 作废人 -->
          <el-descriptions-item label="作废人">{{ info.CancellerId && info.CancellerId.Name }}</el-descriptions-item>
          <!-- 作废日期 -->
          <el-descriptions-item label="作废日期">{{ info.CancelDate && parseTime(info.CancelDate, '{y}-{m}-{d}') }}</el-descriptions-item>
        </el-descriptions>
        <!-- 明细信息 -->
        <el-tabs v-model="detailActiveName" type="card" @tab-click="handleDetailClick" class="custom-tabs">
          <el-tab-pane label="明细信息" name="Dfirst"></el-tab-pane>
          <el-tab-pane label="物料数据" name="Dsecond"></el-tab-pane>
        </el-tabs>
        <el-table ref="detailTable" :data="info.BillEntry" class="custom-table" highlight-current-row @current-change="handleCurrentChange" :setCurrentRow="setCurrentRow" v-show="detailActiveName === 'Dfirst'">
          <!-- 序号 -->
          <el-table-column type="index" label="序号" align="center"></el-table-column>
          <!-- 物料编码 -->
          <el-table-column label="物料编码" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.MaterialId && scope.row.MaterialId.Number }}</template>
          </el-table-column>
          <!-- 物料名称 -->
          <el-table-column label="物料名称" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.MaterialId && getString(scope.row.MaterialId.Name) }}</template>
          </el-table-column>
          <!-- 规格型号 -->
          <el-table-column label="规格型号" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.MaterialId && getString(scope.row.MaterialId.Specification) }}</template>
          </el-table-column>
          <!-- 单位 -->
          <el-table-column label="单位" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.UnitID && getString(scope.row.UnitID.Name) }}</template>
          </el-table-column>
          <!-- 申请数量 -->
          <el-table-column label="申请数量" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.Qty }}</template>
          </el-table-column>
          <!-- 库存组织 -->
          <el-table-column label="库存组织" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.StockOrgIdEntry && getString(scope.row.StockOrgIdEntry.Name) }}</template>
          </el-table-column>
          <!-- 仓库 -->
          <el-table-column label="仓库" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.StockId && getString(scope.row.StockId.Name) }}</template>
          </el-table-column>
          <!-- 仓位 -->
          <el-table-column label="仓位" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.StockLocId && getString(scope.row.StockLocId.Name) }}</template>
          </el-table-column>
          <!-- 货主 -->
          <el-table-column label="货主" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.OwnerId && getString(scope.row.OwnerId.Name) }}</template>
          </el-table-column>
          <!-- 备注 -->
          <el-table-column label="备注" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.EntryNote }}</template>
          </el-table-column>
          <!-- 结算单价 -->
          <el-table-column label="结算单价" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.F_SCMJ_JSPrice }}</template>
          </el-table-column>
          <!-- 结算金额 -->
          <el-table-column label="结算金额" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.F_SCMJ_JSAmount }}</template>
          </el-table-column>
        </el-table>
        <el-descriptions border :column="3" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 3 - 7em - 22px)' }" v-show="detailActiveName === 'Dsecond'">
          <!-- 物料编码 -->
          <el-descriptions-item label="物料编码">{{ setCurrentRow && setCurrentRow.MaterialId && setCurrentRow.MaterialId.Number }}</el-descriptions-item>
          <!-- 物料名称 -->
          <el-descriptions-item label="物料名称">{{ setCurrentRow && setCurrentRow.MaterialId && getString(setCurrentRow.MaterialId.Name) }}</el-descriptions-item>
          <!-- 规格型号 -->
          <el-descriptions-item label="规格型号">{{ setCurrentRow && setCurrentRow.MaterialId && getString(setCurrentRow.MaterialId.Specification) }}</el-descriptions-item>
          <!-- 辅助属性 -->
          <el-descriptions-item label="辅助属性">{{ setCurrentRow && setCurrentRow.AuxPropId && getString(setCurrentRow.AuxPropId.Name) }}</el-descriptions-item>
          <!-- 单位 -->
          <el-descriptions-item label="单位">{{ setCurrentRow && setCurrentRow.UnitID && getString(setCurrentRow.UnitID.Name) }}</el-descriptions-item>
          <!-- 申请数量 -->
          <el-descriptions-item label="申请数量">{{ setCurrentRow && setCurrentRow.Qty }}</el-descriptions-item>
          <!-- 辅单位 -->
          <el-descriptions-item label="辅单位">{{ setCurrentRow && setCurrentRow.SecUnitId && getString(setCurrentRow.SecUnitId.Name) }}</el-descriptions-item>
          <!-- 数量(辅单位) -->
          <el-descriptions-item label="申请数量(辅单位)">{{ (setCurrentRow && setCurrentRow.SecQty) || '' }}</el-descriptions-item>
          <!-- 批号 -->
          <el-descriptions-item label="批号">{{ setCurrentRow && setCurrentRow.Lot && setCurrentRow.Lot.Number }}</el-descriptions-item>
          <!-- 生产日期 -->
          <el-descriptions-item label="生产日期">{{ setCurrentRow && setCurrentRow.ProduceDate && parseTime(setCurrentRow.ProduceDate, '{y}-{m}-{d}') }}</el-descriptions-item>
          <!-- 保质期单位(暂定) -->
          <el-descriptions-item label="保质期单位">{{ setCurrentRow && setCurrentRow.KFPeriodUnit && getString(setCurrentRow.KFPeriodUnit.Name) }}</el-descriptions-item>
          <!-- 保质期（暂定） -->
          <el-descriptions-item label="保质期">{{ setCurrentRow && setCurrentRow.KFPeriod }}</el-descriptions-item>
          <!-- 有效期至 -->
          <el-descriptions-item label="有效期至">{{ setCurrentRow && setCurrentRow.ExpiryDate && parseTime(setCurrentRow.ExpiryDate, '{y}-{m}-{d}') }}</el-descriptions-item>
          <!-- 库存组织 -->
          <el-descriptions-item label="库存组织">{{ setCurrentRow && setCurrentRow.StockOrgIdEntry && getString(setCurrentRow.StockOrgIdEntry.Name) }}</el-descriptions-item>
          <!-- 仓库 -->
          <el-descriptions-item label="仓库">{{ setCurrentRow && setCurrentRow.StockId && getString(setCurrentRow.StockId.Name) }}</el-descriptions-item>
          <!-- 仓位 -->
          <el-descriptions-item label="仓位">{{ setCurrentRow && setCurrentRow.StockLocId && getString(setCurrentRow.StockLocId.Name) }}</el-descriptions-item>
          <!-- BOM版本 -->
          <el-descriptions-item label="BOM版本">{{ setCurrentRow && setCurrentRow.BomId && getString(setCurrentRow.BomId.Name) }}</el-descriptions-item>
          <!-- 计划跟踪号 -->
          <el-descriptions-item label="计划跟踪号">{{ setCurrentRow && setCurrentRow.MtoNo }}</el-descriptions-item>
          <!-- 货主 -->
          <el-descriptions-item label="货主">{{ setCurrentRow && setCurrentRow.OwnerId && getString(setCurrentRow.OwnerId.Name) }}</el-descriptions-item>
          <!-- 库存状态（暂定） -->
          <el-descriptions-item label="库存状态">{{ setCurrentRow && setCurrentRow.StockStatusId && getString(setCurrentRow.StockStatusId.Name) }}</el-descriptions-item>
          <!-- 备注 -->
          <el-descriptions-item label="备注">{{ setCurrentRow && setCurrentRow.EntryNote }}</el-descriptions-item>
          <!-- 业务终止 -->
          <el-descriptions-item label="业务终止">{{ setCurrentRow && setCurrentRow.BusinessEnd === 'A' ? '正常' : '业务终止' }}</el-descriptions-item>
          <!-- 终止人 -->
          <el-descriptions-item label="终止人">{{ setCurrentRow && setCurrentRow.BusinessEnderId && getString(setCurrentRow.BusinessEnderId.Name) }}</el-descriptions-item>
          <!-- 终止日期 -->
          <el-descriptions-item label="终止日期">{{ setCurrentRow && setCurrentRow.EndDate && parseTime(setCurrentRow.EndDate, '{y}-{m}-{d}') }}</el-descriptions-item>
          <!-- 业务关闭 -->
          <el-descriptions-item label="业务关闭">{{ setCurrentRow && setCurrentRow.BusinessClosed === 'A' ? '正常' : '业务关闭' }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
    <!-- 下推弹出框 -->
    <el-dialog v-dialogDragBox title="选择单据" :visible.sync="pushOpen" width="580px" class="custom-dialog">
      <div style="padding: 0 20px">
        <el-form ref="pushForm" :model="pushForm" :rules="pushFormRules" label-width="80px">
          <el-form-item label="" label-width="0" prop="target">
            <el-radio-group v-model="pushForm.target" v-removeAriaHidden @change="handleTargetChange" style="width: 100%">
              <el-row :gutter="10" class="custom-push-target">
                <el-col :span="12" v-for="item in pushTarget" :key="item.value">
                  <el-radio :label="item.value">{{ item.label }}</el-radio>
                </el-col>
              </el-row>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="转换规则" prop="ruleId" v-if="isNeedRule()">
            <el-select v-model="pushForm.ruleId" placeholder="请选择转换规则" style="width: 100%" @change="calculateTargetBillType">
              <el-option v-for="item in calculateRule" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="单据类型" prop="targetBillTypeId" v-if="isNeedBillType()">
            <el-select v-model="pushForm.targetBillTypeId" placeholder="请选择单据类型" style="width: 100%">
              <el-option v-for="item in calculateBillType" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="目标组织" prop="targetOrgId" v-if="isNeedOrg()">
            <el-select v-model="pushForm.targetOrgId" placeholder="请选择目标组织" style="width: 100%">
              <el-option v-for="item in ApplicationOrgNumber" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn small" @click="pushOpen = false">取消</el-button>
        <el-button class="custom-dialog-btn small primary" :class="{ disabled: !pushForm.target }" :disabled="!pushForm.target" @click="handlePushSubmit">确定</el-button>
      </div>
    </el-dialog>
    <!-- 新增其他出库单 -->
    <mis-delivery-create ref="misDeliveryCreate" @callBack="showMisDeliveryCreate = false" v-if="showMisDeliveryCreate" />
  </div>
</template>
<script>
import { kingdee } from '@/minix'
import { getOutStockApplyDetail, auditOutStockApply, revokeOutStockApply, deleteOutStockApply, pushOutStockApply, submitOutStockApply, unAuditOutStockApply } from '@/api/kingdee/sell/outStockApply'
import MisDeliveryCreate from '@/views/kingdee/inventory/outbound/create'

export default {
  mixins: [kingdee],
  name: 'OutStockApplyDetail',
  components: { MisDeliveryCreate },
  data() {
    return {
      open: false,
      info: {},
      setCurrentRow: {},
      activeName: 'first',
      detailActiveName: 'Dfirst',
      buttons: [
        { text: '提交', type: 'primary', action: 'submit', status: ['A', 'D'] },
        { text: '审核', type: 'warning', action: 'audit', status: ['B'] },
        { text: '撤销', type: 'info', action: 'revoke', status: ['B'] },
        { text: '删除', type: 'danger', action: 'delete', status: ['A', 'D'] },
        { text: '反审', type: 'warning', action: 'unAudit', status: ['C'] },
        { text: '下推', type: 'success', action: 'push', status: ['C'] }
      ],
      // 业务类型
      BizTypeOptions: [
        { label: '物料领用', value: '0' },
        { label: '资产领用', value: '1' },
        { label: 'VMI业务', value: '2' },
        { label: '费用物料领用', value: '3' }
      ],
      // 货主类型
      FOwnerTypeIdHeadOptions: [
        { label: '业务组织', value: 'BD_OwnerOrg' },
        { label: '供应商', value: 'BD_Supplier' },
        { label: '客户', value: 'BD_Customer' }
      ],
      // 下推
      pushOpen: false,
      pushTarget: [{ value: 'STK_MisDelivery', label: '其他出库单' }],
      pushForm: {},
      pushFormRules: {
        target: [{ required: true, message: '请选择下推单据', trigger: ['blur', 'change'] }],
        ruleId: [{ required: true, message: '请选择转换规则', trigger: ['blur', 'change'] }],
        targetBillTypeId: [{ required: true, message: '请选择单据类型', trigger: ['blur', 'change'] }],
        targetOrgId: [{ required: true, message: '请选择目标组织', trigger: ['blur', 'change'] }]
      },
      // 转换规则
      ruleList: [
        // 下推至其他出库单
        { value: '265acd8d-705d-4b61-8917-441601e07ad5', label: '出库申请单—异价调出（普通）', target: 'STK_MisDelivery' },
        { value: 'STK_OutStockApplyToSTK_MisDelivery', label: '出库申请单—其他出库单（普通）', target: 'STK_MisDelivery' },
        { value: 'STK_OutStockApplyToSTK_MisDeliveryR', label: '出库申请单—其他出库单（退货）', target: 'STK_MisDelivery' }
      ],
      // 单据类型
      targetBillTypeList: [
        // 下推至异价调出单
        { value: '64c67a6a876911', label: '异价调出单', target: '265acd8d-705d-4b61-8917-441601e07ad5', billType: 'CKSQ01_SYS' },
        // 下推至其他出库单（普通）
        { value: '54533291F9A44D38809F70000499BEE9', label: '标准其他出库单', target: 'STK_OutStockApplyToSTK_MisDelivery', billType: 'CKSQ01_SYS' },
        { value: '005056941128814b11e31b9d2979ed35', label: 'VMI出库（金蝶未命名）', target: 'STK_OutStockApplyToSTK_MisDelivery', billType: 'CKSQ02_SYS' },
        { value: '136bf6db80a14da8b3c332383f796739', label: '资产出库', target: 'STK_OutStockApplyToSTK_MisDelivery', billType: 'CKSQ03_SYS' },
        { value: '0050569448a4966511e336d026c93cba', label: '费用物料出库', target: 'STK_OutStockApplyToSTK_MisDelivery', billType: 'CKSQ04_SYS' },
        // 下推至其他出库单（退货）
        { value: '54533291F9A44D38809F70000499BEE9', label: '标准其他出库单', target: 'STK_OutStockApplyToSTK_MisDeliveryR', billType: 'CKSQ01_SYS' },
        { value: '005056941128814b11e31b9d2979ed35', label: 'VMI出库（金蝶未命名）', target: 'STK_OutStockApplyToSTK_MisDeliveryR', billType: 'CKSQ02_SYS' },
        { value: '136bf6db80a14da8b3c332383f796739', label: '资产出库', target: 'STK_OutStockApplyToSTK_MisDeliveryR', billType: 'CKSQ03_SYS' },
        { value: '0050569448a4966511e336d026c93cba', label: '费用物料出库', target: 'STK_OutStockApplyToSTK_MisDeliveryR', billType: 'CKSQ04_SYS' }
      ],
      // 新增其他出库单
      showMisDeliveryCreate: false
    }
  },
  computed: {
    // 计算规则
    calculateRule() {
      return this.ruleList.filter(item => item.target === this.pushForm.target)
    },
    // 计算单据类型
    calculateBillType() {
      const { ruleId } = this.pushForm
      const billType = this.info?.BillTypeID?.Number || ''
      const arr = this.targetBillTypeList.filter(item => item.target === ruleId) || []
      const newArr = arr.filter(item => item.billType === billType) || []
      return newArr
    }
  },
  methods: {
    // 检查状态
    checkStatus(allowedStatus) {
      return allowedStatus.includes(this.info.DocumentStatus)
    },
    // 切换tab
    handleClick(tab, event) {
      this.activeName = tab.name
    },
    handleDetailClick(tab, event) {
      this.detailActiveName = tab.name
    },
    // 获取详情
    getInfo(row = {}) {
      if (!row.BillNo) {
        this.$message.error('参数错误，请重新选择')
        return
      }
      getOutStockApplyDetail({ billNo: row.BillNo }).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          const { result } = data
          this.activeName = 'first'
          this.detailActiveName = 'Dfirst'
          this.open = !!result?.result
          this.info = result?.result || {}
          this.$nextTick(() => {
            this.setCurrentRow = result?.result?.BillEntry?.[0] || {}
            if (this.$refs.detailTable) {
              this.$refs.detailTable.setCurrentRow(this.setCurrentRow)
              this.$refs.detailTable.bodyWrapper.scrollLeft = 0
            }
          })
        } else this.$message.error(msg)
      })
    },
    // 当前行
    handleCurrentChange(val) {
      this.setCurrentRow = val
    },
    // 关闭弹框
    beforeClose() {
      this.handleClose(false)
      this.$emit('update', { billNo: this.info.BillNo, newStatus: this.info.DocumentStatus })
    },
    // 关闭
    handleClose(flag = false) {
      this.open = false
      this.$emit('callBack', flag)
    },
    // 金蝶操作
    // prettier-ignore
    handleKingdeeDo(type) {
      const number = this.info.BillNo
      if (!number) return
      switch (type) {
        case 'submit':
          // 提交
          this.$modal.confirm('确认要提交该出库申请单吗？').then(() => {
            submitOutStockApply({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('提交成功')
                this.$set(this.info, 'DocumentStatus', 'B')
              } else this.$message.error(msg)
            })
          })
          break
        case 'audit':
          // 审核
          this.$modal.confirm('确认要审核该出库申请单吗？').then(() => {
            auditOutStockApply({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('审核成功')
                this.$set(this.info, 'DocumentStatus', 'C')
              } else this.$message.error(msg)
            })
          })
          break
        case 'revoke':
          // 撤销
          this.$modal.confirm('确认要撤销该出库申请单吗？').then(() => {
            revokeOutStockApply({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('撤销成功')
                this.$set(this.info, 'DocumentStatus', 'D')
              } else this.$message.error(msg)
            })
          })
          break
        case 'delete':
          // 删除
          this.$modal.confirm('确认要删除该出库申请单吗？').then(() => {
            deleteOutStockApply({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('删除成功')
                this.handleClose(true)
              } else this.$message.error(msg)
            })
          })
          break
        case 'unAudit':
          // 反审
          this.$modal.confirm('确认要反审该出库申请单吗？').then(() => {
            unAuditOutStockApply({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('反审成功')
                this.$set(this.info, 'DocumentStatus', 'D')
              } else this.$message.error(msg)
            })
          })
          break
        case 'push':
          // 下推
          this.initPushForm()
          this.pushForm.target = 'STK_MisDelivery'
          this.handleTargetChange('STK_MisDelivery')
          break
      }
    },
    // 初始化下推表单
    initPushForm() {
      this.pushForm = {
        number: this.info.BillNo,
        target: undefined,
        ruleId: undefined,
        targetBillTypeId: undefined,
        targetOrgId: undefined
      }
      this.$nextTick(() => {
        this.resetForm('pushForm')
      })
      this.pushOpen = true
    },
    // 切换下推单据
    handleTargetChange(val) {
      if (val === 'STK_MisDelivery') {
        this.setupSalOutStockPush()
        return
      }
      this.clearPushFormFields()
    },
    // 设置发货通知单下推
    setupSalOutStockPush() {
      this.pushForm.ruleId = this.calculateRule?.[0]?.value || undefined
      this.pushForm.targetBillTypeId = undefined
      // 计算目标单据类型
      this.$nextTick(() => {
        this.calculateTargetBillType()
      })
    },
    // 清空下推表单字段
    clearPushFormFields() {
      this.pushForm.ruleId = undefined
      this.pushForm.targetBillTypeId = undefined
    },
    // 计算目标单据类型
    calculateTargetBillType() {
      const { ruleId } = this.pushForm
      const billType = this.info?.BillTypeID?.Number || ''
      const arr = this.targetBillTypeList.filter(item => item.target === ruleId) || []
      const newArr = arr.filter(item => item.billType === billType) || []
      this.pushForm.targetBillTypeId = newArr?.[0]?.value || undefined
    },
    // 判断是否需要转换规则
    isNeedRule() {
      const { target, ruleId } = this.pushForm
      if (target === 'STK_MisDelivery') {
        return true
      }
      return false
    },
    // 判断是否需要单据类型
    isNeedBillType() {
      const { target, ruleId } = this.pushForm
      if (target === 'STK_MisDelivery') {
        return true
      }
      return false
    },
    // 判断是否需要组织
    isNeedOrg() {
      const { target, ruleId } = this.pushForm
      return false
    },
    // 下推提交
    handlePushSubmit() {
      const { number } = this.pushForm
      if (!number) return
      this.$refs.pushForm.validate(valid => {
        if (valid) {
          this.executePushDown()
        }
      })
    },
    // 执行下推操作
    // prettier-ignore
    executePushDown() {
      pushOutStockApply(this.pushForm).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          this.handlePushSuccess(data)
        } else if (code === 400) {
          this.handlePushError(msg)
        } else {
          this.$message.error(msg)
        }
      }).catch(error => {
        console.error('下推操作失败:', error)
        this.$message.error('下推操作失败，请重试')
      })
    },
    // 处理下推成功
    handlePushSuccess(data) {
      if (this.pushForm.target === 'STK_MisDelivery') {
        this.handleMisDeliveryPushSuccess(data)
      } else {
        this.$message.success('下推成功')
        this.pushOpen = false
        this.handleClose(true)
      }
    },
    // 处理其他出库单下推成功
    handleMisDeliveryPushSuccess(data) {
      this.pushOpen = false
      this.showMisDeliveryCreate = true
      const { responseStatus } = data
      const fid = responseStatus?.successEntitys?.[0]?.id
      if (!fid) {
        this.$message.error('参数错误，请重试！')
        return
      }
      this.$nextTick(() => {
        this.$refs.misDeliveryCreate.initPush(fid, 'push')
      })
    },
    // 处理下推错误
    handlePushError(msg) {
      this.$alert(msg || '操作失败，请重试或使用金蝶进行操作！', '提示', {
        type: 'info',
        confirmButtonText: '确定',
        callback: () => {
          this.pushOpen = false
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
@import '@/assets/styles/custom-new.scss';
::v-deep {
  .el-descriptions__header {
    margin-bottom: 0;
  }
  .el-table.custom-table .el-table__body-wrapper .el-table__row:hover td.el-table__cell {
    background-color: #eaf1fe !important;
  }
}
.customTitle {
  font-size: 16px;
  font-weight: normal;
  line-height: 60px;
  color: $font;
}
.kindeeButton {
  display: flex;
  align-items: center;
}
.custom-push-target {
  .el-col.el-col-12 {
    .el-radio {
      height: 30px;
      display: flex;
      align-items: center;
    }
  }
}
.custom-tabs {
  margin-top: 15px;
  ::v-deep {
    .el-tabs__header {
      .el-tabs__nav {
        border: 0;
      }
      .el-tabs__item {
        background-color: #eef2f8;
        border-radius: 5px 5px 0 0;
        border-left-width: 0;
        border-right-width: 0;
        &.is-active {
          background-color: $blue;
          color: $white;
        }
      }
      .el-tabs__item + .el-tabs__item {
        margin-left: 2px;
      }
    }
  }
}
</style>
