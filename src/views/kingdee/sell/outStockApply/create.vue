<template>
  <div>
    <el-dialog v-dialogDragBox :title="title" :visible.sync="open" width="90%" :before-close="beforeClose" class="custom-dialog">
      <div style="padding: 0 20px">
        <el-form ref="form" :model="form" :rules="rules" label-width="90px">
          <el-row :gutter="10">
            <!-- 申请组织 -->
            <el-col :span="8">
              <el-form-item label="申请组织" prop="fstockorgid">
                <el-select v-model="form.fstockorgid" placeholder="请选择申请组织" filterable style="width: 100%" @change="handleStockOrgChange">
                  <el-option v-for="item in ApplicationOrgId" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 申请类型 -->
            <el-col :span="8">
              <el-form-item label="申请类型" prop="fapplytype">
                <el-select v-model="form.fapplytype" placeholder="请选择申请类型" style="width: 100%">
                  <el-option v-for="item in applyTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 申请日期 -->
            <el-col :span="8">
              <el-form-item label="申请日期" prop="fdate">
                <el-date-picker v-model="form.fdate" type="date" placeholder="选择日期" value-format="yyyy-MM-dd" style="width: 100%"></el-date-picker>
              </el-form-item>
            </el-col>
            <!-- 单据类型 -->
            <el-col :span="8">
              <el-form-item label="单据类型" prop="fbilltypeid">
                <el-select v-model="form.fbilltypeid" placeholder="请选择单据类型" style="width: 100%" @change="handleBillTypeChange">
                  <el-option v-for="item in billTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 货主类型 -->
            <el-col :span="8">
              <el-form-item label="货主类型" prop="fownertypeidhead">
                <el-select v-model="form.fownertypeidhead" placeholder="请选择货主类型" style="width: 100%" :disabled="form.fbiztype != '0'" @change="handleOwnerTypeChange">
                  <el-option v-for="item in ownerTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 业务类型 -->
            <el-col :span="8">
              <el-form-item label="业务类型" prop="fbiztype">
                <el-select v-model="form.fbiztype" placeholder="请选择业务类型" style="width: 100%" disabled>
                  <el-option v-for="item in BizTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 客户 -->
            <el-col :span="8">
              <el-form-item label="客户" prop="fcustid">
                <customer-search-select :useOrg="form.fstockorgid" :showLabel="false" :keyword.sync="form.fcustname" style="width: 100%" isBack @callBack="handleFormCustomer($event)" />
              </el-form-item>
            </el-col>
            <!-- 备注 -->
            <el-col :span="8">
              <el-form-item label="备注" prop="fnote">
                <el-input v-model="form.fnote" placeholder="请输入备注"></el-input>
              </el-form-item>
            </el-col>
            <!-- 物料明细 -->
            <el-col :span="24">
              <el-table :data="form.entities" stripe style="width: 100%" class="custom-table custom-table-cell0">
                <!-- 序号 -->
                <el-table-column align="center" type="index" label="序号" width="50"></el-table-column>
                <!-- 物料编码 -->
                <el-table-column align="center" label="物料编码" prop="fmaterialid">
                  <template slot-scope="scope">
                    <el-tooltip effect="dark" :content="scope.row.fmaterialid" :disabled="!scope.row.fmaterialid">
                      <el-form-item label-width="0" :prop="'entities.' + scope.$index + '.fmaterialid'" :rules="rules.fmaterialid">
                        <material-search-select :keyword.sync="scope.row.fmaterialName" :useOrg="form.fstockorgid" size="small" :showLabel="false" @callBack="handleMaterialSearchSelect($event, scope.row)" />
                      </el-form-item>
                    </el-tooltip>
                  </template>
                </el-table-column>
                <!-- 物料名称 -->
                <el-table-column align="center" label="物料名称" prop="fmaterialName" show-overflow-tooltip></el-table-column>
                <!-- 规格型号 -->
                <el-table-column align="center" label="规格型号" prop="specification" show-overflow-tooltip></el-table-column>
                <!-- 数量 -->
                <el-table-column align="center" label="数量" prop="fqty">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="'entities.' + scope.$index + '.fqty'" :rules="rules.fqty">
                      <el-input v-model="scope.row.fqty" size="small" placeholder="请输入数量"></el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 单位 -->
                <el-table-column align="center" label="单位" prop="funitid">
                  <template slot-scope="scope">{{ scope.row.funitname }}</template>
                </el-table-column>
                <!-- 仓库 -->
                <el-table-column align="center" label="仓库" prop="fstockid">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="'entities.' + scope.$index + '.fstockname'">
                      <stock-search-select :keyword.sync="scope.row.fstockid" :useOrg="form.fstockorgid" :showLabel="false" style="width: 100%" size="small" isBack @callBack="handleStockSelect($event, scope.row)" />
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 仓位 -->
                <!-- <el-table-column align="center" label="仓位" prop="fstocklocid"></el-table-column> -->
                <!-- 货主 -->
                <el-table-column align="center" label="货主" prop="fownerid">
                  <template slot-scope="scope">
                    <!-- 货主类型为业务组织时，选择货主 -->
                    <el-form-item label-width="0" :prop="'entities.' + scope.$index + '.fownerid'" v-if="form.fownertypeidhead == 'BD_OwnerOrg'">
                      <el-select v-model="scope.row.fownerid" placeholder="请选择货主" style="width: 100%" size="small">
                        <el-option v-for="item in ApplicationOrgId" :key="item.value" :label="item.label" :value="item.value"></el-option>
                      </el-select>
                    </el-form-item>
                    <!-- 货主类型为供应商时，选择供应商 -->
                    <el-form-item label-width="0" :prop="'entities.' + scope.$index + '.fownerid'" v-else-if="form.fownertypeidhead == 'BD_Supplier'">
                      <supplier-search-select :keyword.sync="scope.row.fowneridname" :useOrg="form.fstockorgid" size="small" :showLabel="false" isBack @callBack="handleSupplierSearchSelect($event, scope.row)" />
                    </el-form-item>
                    <!-- 货主类型为客户时，选择客户 -->
                    <el-form-item label-width="0" :prop="'entities.' + scope.$index + '.fownerid'" v-else-if="form.fownertypeidhead == 'BD_Customer'">
                      <customer-search-select :keyword.sync="scope.row.fowneridname" :useOrg="form.fstockorgid" size="small" :showLabel="false" isBack @callBack="handleCustomerSearchSelect($event, scope.row)" />
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 结算单价 -->
                <el-table-column align="center" label="结算单价" prop="jsprice">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="'entities.' + scope.$index + '.jsprice'" :rules="rules.jsprice">
                      <el-input :controls="false" v-model="scope.row.jsprice" placeholder="请输入" style="width: 100%" size="small" @change="handleJspriceChange(scope.row)">
                        <template slot="prefix">￥</template>
                      </el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 结算金额 -->
                <el-table-column align="center" label="结算金额" prop="jsamount">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="'entities.' + scope.$index + '.jsamount'" :rules="rules.jsamount">
                      <el-input :controls="false" v-model="scope.row.jsamount" placeholder="请输入" style="width: 100%" size="small" @change="handleJsamountChange(scope.row)">
                        <template slot="prefix">￥</template>
                      </el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 操作 -->
                <el-table-column align="center" label="操作" width="120">
                  <template slot-scope="scope">
                    <el-button type="text" size="small" class="el-icon-plus" @click="handleAddMaterial">添加</el-button>
                    <el-button v-if="form.entities.length > 1" type="text" size="small" class="el-icon-delete" @click="handleDelete(scope.$index)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn" @click="handleClose()">取 消</el-button>
        <el-button class="custom-dialog-btn primary" @click="handleSubmit">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { kingdee } from '@/minix'
import materialSearchSelect from '@/components/SearchSelect/material'
import customerSearchSelect from '@/components/SearchSelect/customer'
import supplierSearchSelect from '@/components/SearchSelect/supplier'
import { isNumber, isNumberLength } from '@/utils/validate'
import { saveOutStockApply } from '@/api/kingdee/sell/outStockApply'
import stockSearchSelect from '@/components/SearchSelect/stock'

export default {
  mixins: [kingdee],
  name: 'OutStockApplyAdd',
  components: { materialSearchSelect, customerSearchSelect, supplierSearchSelect, stockSearchSelect },
  data() {
    return {
      open: false,
      title: '新增出库申请单',
      form: {},
      rules: {
        fstockorgid: [{ required: true, message: '请选择申请组织', trigger: 'change' }],
        fapplytype: [{ required: true, message: '请选择申请类型', trigger: 'change' }],
        fdate: [{ required: true, message: '请选择申请日期', trigger: 'change' }],
        fbilltypeid: [{ required: true, message: '请选择单据类型', trigger: 'change' }],
        fownertypeidhead: [{ required: true, message: '请选择货主类型', trigger: 'change' }],
        fbiztype: [{ required: true, message: '请选择业务类型', trigger: 'change' }],
        fcustid: [{ required: true, message: '请选择客户', trigger: 'change' }],
        fmaterialid: [{ required: true, message: '请选择物料编码', trigger: 'change' }],
        fqty: [
          { required: true, message: '请输入数量', trigger: 'blur' },
          { validator: isNumber, message: '请输入正确的数量', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: 'blur' }
        ],
        jsprice: [
          { required: true, message: '请输入结算单价', trigger: 'blur' },
          { validator: isNumber, message: '请输入正确的结算单价', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: 'blur' }
        ],
        jsamount: [
          { required: true, message: '请输入结算金额', trigger: 'blur' },
          { validator: isNumber, message: '请输入正确的结算金额', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: 'blur' }
        ]
      },
      lastStockOrgId: undefined, // 保存上一次的申请组织值
      lastBillTypeId: undefined, // 保存上一次的单据类型值
      // 申请类型
      applyTypeOptions: [
        { label: '货损出库', value: 'CKSQLX01_SYS' },
        { label: '耗材出库', value: 'CKSQLX02_SYS' },
        { label: '报废出库', value: 'CKSQLX03_SYS' },
        { label: '福利领用', value: 'CKSQLX04_SYS' },
        { label: '办公用品领用', value: 'CKSQLX05_SYS' },
        { label: '劳动用品领用', value: 'CKSQLX06_SYS' },
        { label: '样品出库', value: 'CKSQLX07_SYS' }
      ],
      // 单据类型
      billTypeOptions: [
        { label: '标准出库申请', value: 'CKSQ01_SYS' },
        { label: 'VMI出库申请', value: 'CKSQ02_SYS' },
        { label: '资产出库申请', value: 'CKSQ03_SYS' },
        { label: '费用物料出库申请', value: 'CKSQ04_SYS' }
      ],
      // 货主类型
      ownerTypeOptions: [
        { label: '业务组织', value: 'BD_OwnerOrg' },
        { label: '供应商', value: 'BD_Supplier' },
        { label: '客户', value: 'BD_Customer' }
      ],
      // 业务类型
      BizTypeOptions: [
        { label: '物料领用', value: '0' },
        { label: '资产领用', value: '1' },
        { label: 'VMI业务', value: '2' },
        { label: '费用物料领用', value: '3' }
      ]
    }
  },
  methods: {
    // 表单重置
    reset() {
      this.form = {
        entities: [
          {
            fentryid: undefined,
            fmaterialid: undefined, // 物料编码
            fqty: undefined, // 数量
            funitid: undefined, // 单位
            fstockid: undefined, // 仓库
            fstockname: undefined, // 仓库名称
            fstocklocid: undefined, // 仓位
            fownerid: this.ApplicationOrgId[0].value || undefined, // 货主
            fowneridname: this.ApplicationOrgId[0].label || undefined, // 货主名称
            jsprice: undefined, // 结算单价
            jsamount: undefined // 结算金额
          }
        ], // 物料明细
        fapplytype: undefined, // 申请类型
        fbilltypeid: this.billTypeOptions[0].value || undefined, // 单据类型
        fbiztype: this.BizTypeOptions[0].value || undefined, // 业务类型
        fcustid: undefined, // 客户
        fcustname: undefined, // 客户名称
        fdate: this.parseTime(new Date(), '{y}-{m}-{d}'), // 申请日期
        fid: undefined,
        fnote: undefined, // 备注
        fownertypeidhead: this.ownerTypeOptions[0].value || undefined, // 货主类型
        fstockorgid: this.ApplicationOrgId[0].value || undefined // 申请组织
      }
      this.resetForm('form')
    },
    // 初始化
    init() {
      this.reset()
      this.lastStockOrgId = undefined // 初始化保存的申请组织值
      this.title = '新增出库申请单'
      this.open = true
    },
    // 申请组织变更
    // prettier-ignore
    handleStockOrgChange(val) {
      if (this.form.entities && this.form.entities.length > 0 && this.form.entities.some(item => item.fmaterialid) && this.lastStockOrgId) {
        this.$confirm('主业务组织已改变，系统将创建新单，是否继续？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.clearMaterialDetails()
          this.lastStockOrgId = val
        }).catch(() => {
          this.$set(this.form, 'fstockorgid', this.lastStockOrgId)
        })
      } else {
        this.lastStockOrgId = val
        this.form.entities.forEach(item => {
          item.fownerid = val
          item.fowneridname = this.ApplicationOrgId.find(item => item.value == val)?.label || undefined
        })
      }
    },
    // 清空物料明细
    clearMaterialDetails() {
      this.form.entities = [
        {
          fentryid: undefined,
          fmaterialid: undefined, // 物料编码
          fqty: undefined, // 数量
          funitid: undefined, // 单位
          fstockid: undefined, // 仓库
          fstockname: undefined, // 仓库名称
          fstocklocid: undefined, // 仓位
          fownerid: undefined, // 货主
          fowneridname: undefined, // 货主名称
          jsprice: undefined, // 结算单价
          jsamount: undefined // 结算金额
        }
      ]
    },
    // 单据类型变更
    // prettier-ignore
    handleBillTypeChange(val) {
      if (this.form.entities && this.form.entities.length > 0 && this.form.entities.some(item => item.fmaterialid) && this.lastBillTypeId) {
        this.$confirm('单据类型已改变，系统将创建新单，是否继续？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.clearMaterialDetails()
          this.lastBillTypeId = val
          this.handleChangeBillType(val)
        }).catch(() => {
          this.$set(this.form, 'fbilltypeid', this.lastBillTypeId)
        })
      } else {
        this.lastBillTypeId = val
        this.handleChangeBillType(val)
      }
    },
    // 单据类型变更修改货主类型、业务类型
    handleChangeBillType(val) {
      if (val === 'CKSQ01_SYS') {
        this.$set(this.form, 'fbiztype', '0')
        this.$set(this.form, 'fownertypeidhead', 'BD_OwnerOrg')
      } else if (val === 'CKSQ02_SYS') {
        this.$set(this.form, 'fbiztype', '2')
        this.$set(this.form, 'fownertypeidhead', 'BD_Supplier')
      } else if (val === 'CKSQ03_SYS') {
        this.$set(this.form, 'fbiztype', '1')
        this.$set(this.form, 'fownertypeidhead', 'BD_OwnerOrg')
      } else if (val === 'CKSQ04_SYS') {
        this.$set(this.form, 'fbiztype', '3')
        this.$set(this.form, 'fownertypeidhead', 'BD_OwnerOrg')
      }
    },
    // 货主类型变更
    handleOwnerTypeChange(val) {
      this.form.entities.forEach(item => {
        item.fownerid = val == 'BD_OwnerOrg' ? this.form.fstockorgid : undefined
        item.fowneridname = val == 'BD_OwnerOrg' ? this.ApplicationOrgId.find(item => item.value == this.form.fstockorgid)?.label : undefined
      })
    },
    // 客户回调
    handleFormCustomer(event) {
      this.form.fcustid = event.Number
      this.form.fcustname = event.Name
    },
    // 选择物料回调
    handleMaterialSearchSelect(event, row) {
      const obj = this.UnitList.find(item => item.FName == event.Unit)
      this.$set(row, 'fmaterialid', event.Number)
      this.$set(row, 'fmaterialName', event.Name)
      this.$set(row, 'specification', event.Specification)
      this.$set(row, 'funitid', obj?.FNumber || '')
      this.$set(row, 'funitname', obj?.FName || '')
      const entitiesObj = {
        fentryid: undefined,
        fmaterialid: undefined, // 物料编码
        fqty: undefined, // 数量
        funitid: undefined, // 单位
        fstockid: undefined, // 仓库
        fstockname: undefined, // 仓库名称
        fstocklocid: undefined, // 仓位
        fownerid: this.form.fownertypeidhead == 'BD_OwnerOrg' ? this.ApplicationOrgId[0].value : undefined, // 货主
        fowneridname: this.form.fownertypeidhead == 'BD_OwnerOrg' ? this.ApplicationOrgId[0].label : undefined, // 货主名称
        jsprice: undefined, // 结算单价
        jsamount: undefined // 结算金额
      }
      const hasEmpty = this.form.entities.filter(item => !item.fmaterialid)
      if (hasEmpty.length == 0) this.form.entities.push(entitiesObj)
    },
    // 选择仓库
    handleStockSelect(event, row) {
      this.$set(row, 'fstockid', event.Number)
      this.$set(row, 'fstockname', event.Name)
    },
    // 货主选择供应商
    handleSupplierSearchSelect(event, row) {
      row.fownerid = event.FNumber
      row.fowneridname = event.FName
    },
    // 货主选择客户
    handleCustomerSearchSelect(event, row) {
      row.fownerid = event.Number
      row.fowneridname = event.Name
    },
    // 结算单价变更
    handleJspriceChange(row) {
      row.jsamount = parseFloat((row.fqty * row.jsprice).toFixed(5))
    },
    // 结算金额变更
    handleJsamountChange(row) {
      row.jsprice = parseFloat((row.jsamount / row.fqty).toFixed(5))
    },
    // 删除物料明细
    handleDelete(index) {
      this.form.entities.splice(index, 1)
    },
    // 添加物料明细
    handleAddMaterial() {
      const entitiesObj = {
        fentryid: undefined,
        fmaterialid: undefined, // 物料编码
        fqty: undefined, // 数量
        funitid: undefined, // 单位
        fstockid: undefined, // 仓库
        fstockname: undefined, // 仓库名称
        fstocklocid: undefined, // 仓位
        fownerid: this.form.fownertypeidhead == 'BD_OwnerOrg' ? this.ApplicationOrgId[0].value : undefined, // 货主
        fowneridname: this.form.fownertypeidhead == 'BD_OwnerOrg' ? this.ApplicationOrgId[0].label : undefined, // 货主名称
        jsprice: undefined, // 结算单价
        jsamount: undefined // 结算金额
      }
      this.form.entities.push(entitiesObj)
    },
    // 关闭
    beforeClose() {
      this.handleClose()
    },
    // 关闭
    handleClose(flag = false) {
      this.open = false
      this.$emit('callBack', flag)
    },
    // 提交
    handleSubmit() {
      if (this.form.entities.length > 1) this.form.entities = this.form.entities.filter(item => item.fmaterialid)
      // 重置表单验证
      this.$nextTick(() => {
        this.$refs.form.validate(valid => {
          if (valid) {
            let data = { ...this.form }
            if (this.form.fid) {
              data.entities = data.entities.map(item => {
                return {
                  fentryid: item.fentryid,
                  fmaterialid: item.fmaterialid,
                  fqty: item.fqty,
                  funitid: item.funitid,
                  fstockid: item.fstockid,
                  fstocklocid: item.fstocklocid,
                  fownerid: item.fownerid,
                  jsprice: item.jsprice,
                  jsamount: item.jsamount
                }
              })
            } else {
              data.entities = data.entities.map(item => {
                return {
                  fmaterialid: item.fmaterialid,
                  fqty: item.fqty,
                  funitid: item.funitid,
                  fstockid: item.fstockid,
                  fstocklocid: item.fstocklocid,
                  fownerid: item.fownerid,
                  jsprice: item.jsprice,
                  jsamount: item.jsamount
                }
              })
            }
            const success = this.form.fid ? '修改成功' : '提交成功'
            delete data.fcustname
            saveOutStockApply(data).then(res => {
              const { code, msg } = res
              if (code == 200) {
                this.$message.success(success)
                this.handleClose(true)
              } else this.$message.error(msg)
            })
          }
        })
      })
    }
  }
}
</script>
<style scoped lang="scss">
@import '@/assets/styles/custom-new.scss';
::v-deep {
  .custom-table {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    border-bottom: 0;
    margin-bottom: 20px;
    .el-form-item {
      margin-top: 10px !important;
      margin-bottom: 10px !important;
      .el-form-item__error {
        top: 95%;
        padding-top: 0;
      }
    }
    .el-input--prefix .el-input__inner {
      padding-left: 20px;
      padding-right: 20px;
    }
    .el-date-editor--datetime .el-input__inner {
      padding-left: 30px;
      padding-right: 30px;
    }
  }
}
</style>
