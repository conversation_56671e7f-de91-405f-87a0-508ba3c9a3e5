<template>
  <div class="newBox bgcf9 vh-85">
    <!-- 搜索 -->
    <div class="custom-search flex" style="padding-top: 18px" :style="{ justifyContent: showSearch ? 'space-between' : 'flex-end' }">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" @submit.native.prevent>
        <!-- 申请组织 -->
        <el-form-item label="申请组织" prop="stockOrg">
          <el-select v-model="queryParams.stockOrg" placeholder="请选择申请组织" clearable>
            <el-option v-for="item in ApplicationOrgId" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <!-- 单据编号 -->
        <el-form-item label="单据编号" prop="billNo">
          <el-input v-model="queryParams.billNo" placeholder="请输入单据编号" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <!-- 客户名称 -->
        <el-form-item label="客户名称" prop="custName">
          <el-input v-model="queryParams.custName" placeholder="请输入客户名称" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <!-- 单据状态 -->
        <el-form-item label="单据状态" prop="documentStatus">
          <el-select v-model="queryParams.documentStatus" placeholder="请选择单据状态" clearable>
            <el-option v-for="item in DocumentStatusOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <!-- 物料名称 -->
        <el-form-item label="物料名称" prop="materialName">
          <el-input v-model="queryParams.materialName" placeholder="请输入物料名称" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <!-- 业务日期 -->
        <el-form-item label="业务日期" prop="date">
          <el-date-picker v-model="queryParams.date" placeholder="请选择业务日期" type="date" value-format="yyyy-MM-dd" size="small"></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="small" @click="handleResetQuery">重置</el-button>
          <!-- 新增 -->
          <el-button type="primary" icon="el-icon-plus" size="small" @click="handleAdd">新增出库申请单</el-button>
        </el-form-item>
      </el-form>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns" isSetitem @updateColumns="updateColumns"></right-toolbar>
    </div>
    <!-- 列表 -->
    <div style="padding: 15px 20px">
      <el-table v-loading="loading" ref="allTable" border :data="list" style="width: 100%" class="custom-table" :span-method="objectSpanMethod">
        <el-table-column align="center" type="index" label="序号"></el-table-column>
        <!-- 单据编号 -->
        <el-table-column align="center" prop="BillNo" label="单据编号" show-overflow-tooltip v-if="columns[0].visible">
          <template slot-scope="{ row }">
            <span class="table-link" @click="handleDetail(row)">{{ row.BillNo }}</span>
          </template>
        </el-table-column>
        <!-- 单据类型 -->
        <el-table-column align="center" prop="BillType" label="单据类型" show-overflow-tooltip v-if="columns[1].visible"></el-table-column>
        <!-- 申请类型 -->
        <el-table-column align="center" prop="ApplyType" label="申请类型" show-overflow-tooltip v-if="columns[2].visible">
          <template slot-scope="{ row }">{{ getApplyType(row.ApplyType) }}</template>
        </el-table-column>
        <!-- 申请组织 -->
        <el-table-column align="center" prop="StockOrg" label="申请组织" show-overflow-tooltip v-if="columns[3].visible"></el-table-column>
        <!-- 申请日期 -->
        <el-table-column align="center" prop="FDate" label="申请日期" show-overflow-tooltip v-if="columns[4].visible">
          <template slot-scope="{ row }">{{ parseTime(row.FDate, '{y}-{m}-{d}') }}</template>
        </el-table-column>
        <!-- 单据状态 -->
        <el-table-column align="center" prop="DocumentStatus" label="单据状态" show-overflow-tooltip v-if="columns[5].visible">
          <template slot-scope="{ row }">{{ getDocumentStatusLabel(row.DocumentStatus) }}</template>
        </el-table-column>
        <!-- 关闭状态 -->
        <el-table-column align="center" prop="CloseStatus" label="关闭状态" show-overflow-tooltip v-if="columns[6].visible">
          <template slot-scope="{ row }">{{ row.CloseStatus === 'A' ? '未关闭' : '已关闭' }}</template>
        </el-table-column>
        <!-- 物料编码 -->
        <el-table-column align="center" prop="MaterialNumber" label="物料编码" show-overflow-tooltip v-if="columns[7].visible">
          <template slot-scope="{ row }">
            <span class="table-link" @click="handleMaterialNumber(row.MaterialNumber)">{{ row.MaterialNumber }}</span>
          </template>
        </el-table-column>
        <!-- 物料名称 -->
        <el-table-column align="center" prop="MaterialName" label="物料名称" show-overflow-tooltip v-if="columns[8].visible"></el-table-column>
        <!-- 规格型号 -->
        <el-table-column align="center" prop="Model" label="规格型号" show-overflow-tooltip v-if="columns[9].visible"></el-table-column>
        <!-- 单位 -->
        <el-table-column align="center" prop="Unit" label="单位" show-overflow-tooltip v-if="columns[10].visible"></el-table-column>
        <!-- 申请数量 -->
        <el-table-column align="center" prop="Qty" label="申请数量" show-overflow-tooltip v-if="columns[11].visible"></el-table-column>
        <!-- 库存组织 -->
        <el-table-column align="center" prop="StockOrgIdEntry" label="库存组织" show-overflow-tooltip v-if="columns[12].visible"></el-table-column>
        <!-- 仓库 -->
        <el-table-column align="center" prop="Stock" label="仓库" show-overflow-tooltip v-if="columns[13].visible"></el-table-column>
        <!-- 仓位 -->
        <el-table-column align="center" prop="StockLocId" label="仓位" show-overflow-tooltip v-if="columns[14].visible"></el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="custom-pagination">
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.limit" @pagination="getList" />
      </div>
    </div>
    <!-- 详情 -->
    <out-stock-apply-detail ref="outStockApplyDetail" @callBack="handleCallBack" @update="handleUpdate" v-if="showDetail" />
    <!-- 物料详情 -->
    <material-detail ref="materialDetail" v-if="showMaterialDetail" />
    <!-- 新增 -->
    <out-stock-apply-add ref="outStockApplyAdd" @callBack="handleCallBack" v-if="showAdd" />
  </div>
</template>
<script>
import { kingdee } from '@/minix'
import OutStockApplyDetail from './detail'
import OutStockApplyAdd from './create'
import MaterialDetail from '@/views/kingdee/material/detail'
import { getOutStockApplyList } from '@/api/kingdee/sell/outStockApply'

export default {
  mixins: [kingdee],
  name: 'OutStockApply',
  components: { OutStockApplyDetail, MaterialDetail, OutStockApplyAdd },
  data() {
    return {
      // 搜索
      queryParams: {
        pageNum: 1, // 当前页码
        startRow: 0, // 开始行索引
        limit: 10, // 每页条数
        stockOrg: undefined, // 申请组织
        billNo: undefined, // 单据编号
        custName: undefined, // 客户名称-模糊搜索
        custName2: undefined, // 客户名称-精确匹配
        documentStatus: undefined, // 单据状态
        materialName: undefined, // 物料名称
        date: undefined // 业务日期
      },
      // 列表
      list: [],
      total: 0,
      loading: false,
      showDetail: false,
      showMaterialDetail: false,
      // 列表显隐
      showSearch: true,
      // 列信息
      columns: [
        { key: 0, label: `单据编号`, visible: true },
        { key: 1, label: `单据类型`, visible: true },
        { key: 2, label: `申请类型`, visible: true },
        { key: 3, label: `申请组织`, visible: true },
        { key: 4, label: `申请日期`, visible: true },
        { key: 5, label: `单据状态`, visible: true },
        { key: 6, label: `关闭状态`, visible: true },
        { key: 7, label: `物料编码`, visible: true },
        { key: 8, label: `物料名称`, visible: true },
        { key: 9, label: `规格型号`, visible: true },
        { key: 10, label: `单位`, visible: true },
        { key: 11, label: `申请数量`, visible: true },
        { key: 12, label: `库存组织`, visible: true },
        { key: 13, label: `仓库`, visible: true },
        { key: 14, label: `仓位`, visible: true }
      ],
      // 申请类型
      applyTypeOptions: [
        { label: '货损出库', value: 'CKSQLX01_SYS' },
        { label: '耗材出库', value: 'CKSQLX02_SYS' },
        { label: '报废出库', value: 'CKSQLX03_SYS' },
        { label: '福利领用', value: 'CKSQLX04_SYS' },
        { label: '办公用品领用', value: 'CKSQLX05_SYS' },
        { label: '劳动用品领用', value: 'CKSQLX06_SYS' },
        { label: '样品出库', value: 'CKSQLX07_SYS' }
      ],
      showAdd: false
    }
  },
  computed: {
    // 获取当前用户ID
    userId() {
      return this.$store.getters.info.userId
    }
  },
  created() {
    const columns = localStorage.getItem(this.userId + '.outStockApplyColumns')
    if (columns) this.columns = JSON.parse(columns)
    // 获取列表
    this.getList()
  },
  methods: {
    // 获取单据类型
    getApplyType(value) {
      const obj = this.applyTypeOptions.find(item => item.value === value)
      return obj ? obj.label : ''
    },
    // 改变显隐列缓存
    updateColumns(data = []) {
      localStorage.setItem(this.userId + '.outStockApplyColumns', JSON.stringify(data))
    },
    // 列表
    // prettier-ignore
    getList() {
      this.loading = true
      this.queryParams.startRow = Math.max(0, (this.queryParams.pageNum - 1) * this.queryParams.limit)
      const query = { ...this.queryParams }
      delete query.pageNum
      getOutStockApplyList(query).then(res => {
        const { code, data, msg } = res
        if (code === 200) {
          const { data: list, total } = data
          this.list = list
          this.total = total
        } else this.$message.error(msg)
      }).finally(() => {
        this.loading = false
      })
    },
    // 相同字段合并
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      const visibleColumns = this.columns.filter(col => col.visible)
      const currentColumnKey = visibleColumns[columnIndex]?.key
      if (currentColumnKey >= 1 && currentColumnKey <= 6) {
        const _row = this.calculateSpan(this.list, 'BillNo')[rowIndex]
        const _col = _row > 0 ? 1 : 0
        return {
          rowspan: _row,
          colspan: _col
        }
      }
    },
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 重置搜索
    handleResetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 物料详情
    handleMaterialNumber(row) {
      this.showMaterialDetail = true
      this.$nextTick(() => {
        this.$refs.materialDetail.getInfo(row)
      })
    },
    // 详情
    handleDetail(row) {
      this.showDetail = true
      this.$nextTick(() => {
        this.$refs.outStockApplyDetail.getInfo(row)
      })
    },
    // 回调
    handleCallBack(flag = false) {
      this.showDetail = false
      if (flag) this.getList()
    },
    // 新增
    handleAdd() {
      this.showAdd = true
      this.$nextTick(() => {
        this.$refs.outStockApplyAdd.init()
      })
    },
    // 更新
    handleUpdate(data) {
      const { billNo, newStatus } = data
      const index = this.list.findIndex(item => item.BillNo === billNo)
      if (index !== -1) this.list[index].DocumentStatus = newStatus
    }
  }
}
</script>
<style scoped lang="scss">
@import '@/assets/styles/custom-new.scss';
</style>
