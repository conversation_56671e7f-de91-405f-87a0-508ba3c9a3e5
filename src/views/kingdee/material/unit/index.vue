<template>
  <div class="newBox" :class="{ 'vh-85': !isPopup, bgcf9: !isPopup }">
    <!-- 搜索 -->
    <div class="custom-search flex" style="padding-top: 18px" :style="{ justifyContent: showSearch ? 'space-between' : 'flex-end' }">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" @submit.native.prevent v-show="showSearch">
        <el-form-item label="物料编码" prop="number">
          <el-input v-model="queryParams.number" placeholder="请输入物料编码" clearable @keyup.enter.native="handleQuery" :readonly="isPopup" />
        </el-form-item>
        <el-form-item label="物料名称" prop="name" v-if="!isPopup">
          <el-input v-model="queryParams.name" placeholder="请输入物料名称" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="禁用状态" prop="forbidStatus">
          <el-select v-model="queryParams.forbidStatus" placeholder="请选择禁用状态">
            <el-option label="否" value="A" />
            <el-option label="是" value="B" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="small" @click="handleResetQuery">重置</el-button>
          <!-- 新增 -->
          <el-button type="primary" icon="el-icon-plus" size="small" @click="handleCreate" v-hasPermi="['kingdee:materialUnit:create']">新增单位换算</el-button>
        </el-form-item>
      </el-form>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns" isSetitem @updateColumns="updateColumns"></right-toolbar>
    </div>
    <!-- 列表 -->
    <div style="padding: 15px 20px">
      <el-table v-loading="loading" ref="allTable" border :data="list" row-key="Id" style="width: 100%" class="custom-table">
        <!-- 序号 -->
        <el-table-column label="序号" type="index" width="80" align="center" />
        <!-- 物料编码 -->
        <el-table-column label="物料编码" prop="MaterialNumber" align="center" show-overflow-tooltip v-if="columns[0].visible">
          <template slot-scope="{ row }">
            <span class="table-link" @click="openUnitCreate(row)">{{ row.MaterialNumber }}</span>
          </template>
        </el-table-column>
        <!-- 物料名称 -->
        <el-table-column label="物料名称" prop="MaterialName" align="center" show-overflow-tooltip v-if="columns[1].visible" />
        <!-- 单位 -->
        <el-table-column label="单位" prop="CurrentUnit" align="center" show-overflow-tooltip v-if="columns[2].visible" />
        <!-- 基本单位 -->
        <el-table-column label="基本单位" prop="DestUnit" align="center" show-overflow-tooltip v-if="columns[3].visible" />
        <!-- 分子 -->
        <el-table-column label="分子" prop="ConvertNumerator" align="center" show-overflow-tooltip v-if="columns[4].visible" />
        <!-- 换算类型 -->
        <el-table-column label="换算类型" prop="ConvertType" align="center" show-overflow-tooltip v-if="columns[5].visible">
          <template slot-scope="{ row }">{{ row.ConvertType == 1 ? '浮动' : '固定' }}</template>
        </el-table-column>
        <!-- 数据状态 -->
        <el-table-column label="数据状态" prop="DocumentStatus" align="center" show-overflow-tooltip v-if="columns[6].visible">
          <template slot-scope="{ row }">{{ getDocumentStatusLabel(row.DocumentStatus) }}</template>
        </el-table-column>
        <!-- 创建人 -->
        <el-table-column label="创建人" prop="Creator" align="center" show-overflow-tooltip v-if="columns[7].visible" />
        <!-- 创建日期 -->
        <el-table-column label="创建日期" prop="CreateDate" align="center" show-overflow-tooltip v-if="columns[8].visible">
          <template slot-scope="{ row }">{{ parseTime(row.CreateDate, '{y}-{m}-{d}') }}</template>
        </el-table-column>
        <!-- 修改日期 -->
        <el-table-column label="修改日期" prop="ModifyDate" align="center" show-overflow-tooltip v-if="columns[9].visible">
          <template slot-scope="{ row }">{{ parseTime(row.ModifyDate, '{y}-{m}-{d}') }}</template>
        </el-table-column>
        <!-- 修改人 -->
        <el-table-column label="修改人" prop="Modifier" align="center" show-overflow-tooltip v-if="columns[10].visible" />
      </el-table>
      <!-- 分页 -->
      <div class="custom-pagination">
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.limit" @pagination="getList" />
      </div>
    </div>
    <!-- 单位换算详情 -->
    <unit-create ref="unitCreate" @callBack="detailCallBack" @update="handleUpdate" v-if="showUnitCreate" />
  </div>
</template>
<script>
import { kingdee } from '@/minix'
import UnitCreate from '@/views/kingdee/material/unit/create'
import { getMaterialUnitConvertList } from '@/api/kingdee/materialUnit'

export default {
  name: 'MaterialUnit',
  mixins: [kingdee],
  props: {
    isPopup: {
      type: Boolean,
      default: false
    }
  },
  components: { UnitCreate },
  data() {
    return {
      // 搜索
      queryParams: {
        pageNum: 1, // 当前页码
        startRow: 0,
        limit: 10,
        number: undefined, // 物料编码(包含)
        materialName: undefined, // 物料名称(包含)
        forbidStatus: 'A' // 禁用状态
      },
      // 列表
      list: [],
      total: 0,
      loading: false,
      // 单位换算详情
      showUnitCreate: false,
      // 是否显示搜索
      showSearch: true,
      columns: [
        { key: 0, label: `物料编码`, visible: true },
        { key: 1, label: `物料名称`, visible: true },
        { key: 2, label: `单位`, visible: true },
        { key: 3, label: `基本单位`, visible: true },
        { key: 4, label: `分子`, visible: true },
        { key: 5, label: `换算类型`, visible: true },
        { key: 6, label: `数据状态`, visible: true },
        { key: 7, label: `创建人`, visible: true },
        { key: 8, label: `创建日期`, visible: true },
        { key: 9, label: `修改日期`, visible: true },
        { key: 10, label: `修改人`, visible: true }
      ],
      // 接收参数
      receiveData: {}
    }
  },
  computed: {
    // 获取当前用户ID
    userId() {
      return this.$store.getters.info.userId
    }
  },
  created() {
    const columns = localStorage.getItem(this.userId + '.materialUnitColumns')
    if (columns) this.columns = JSON.parse(columns)
    // 获取列表
    if (!this.isPopup) this.getList()
  },
  methods: {
    // 改变显隐列缓存
    updateColumns(data = []) {
      localStorage.setItem(this.userId + '.materialUnitColumns', JSON.stringify(data))
    },
    // 获取列表
    // prettier-ignore
    getList() {
      this.loading = true
      this.queryParams.startRow = Math.max(0, (this.queryParams.pageNum - 1) * this.queryParams.limit)
      const query = { ...this.queryParams }
      delete query.pageNum
      getMaterialUnitConvertList(query).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          const { data: list, total } = data
          this.list = list
          this.total = total
        } else this.$message.error(msg)
      }).finally(() => {
        this.loading = false
      })
    },
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 重置搜索
    handleResetQuery() {
      this.resetForm('queryForm')
      this.queryParams.number = this.receiveData?.materialId || undefined
      this.handleQuery()
    },
    // 接收参数
    handleReceive(data = {}) {
      this.receiveData = data
      this.handleResetQuery()
    },
    // 详情回调
    detailCallBack(flag) {
      this.showUnitCreate = false
      if (flag) this.getList()
    },
    // 打开单位换算详情
    openUnitCreate(row) {
      this.showUnitCreate = true
      this.$nextTick(() => {
        this.$refs.unitCreate.handleOpen(row)
      })
    },
    // 新增单位换算
    handleCreate() {
      this.showUnitCreate = true
      if (this.isPopup && this.receiveData.materialId) {
        const data = {
          fmaterialid: this.receiveData.materialId,
          fmaterialname: this.receiveData.materialName,
          fdestunitid: this.receiveData.materialUnit,
          isHas: true
        }
        this.$nextTick(() => {
          this.$refs.unitCreate.handleOpen(data)
        })
        return
      }
      this.$nextTick(() => {
        this.$refs.unitCreate.handleOpen()
      })
    },
    // 更新
    handleUpdate(data) {
      const { billNo, newStatus } = data
      const index = this.list.findIndex(item => item.MaterialNumber === billNo)
      if (index !== -1) this.list[index].DocumentStatus = newStatus
    }
  }
}
</script>
<style scoped lang="scss">
@import '@/assets/styles/custom-new.scss';
</style>
