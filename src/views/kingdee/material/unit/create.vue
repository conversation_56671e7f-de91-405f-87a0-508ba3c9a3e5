<template>
  <div>
    <el-dialog v-dialogDragBox :title="title" :visible.sync="open" width="1150px" :before-close="beforeClose" class="custom-dialog" append-to-body>
      <div style="padding: 0 20px">
        <div class="kindeeButton" :key="info.DocumentStatus" v-if="info.DocumentStatus">
          <template v-if="disabled">
            <template v-if="info.ForbidStatus == 'A'">
              <el-button type="primary" size="medium" :disabled="info.DocumentStatus != 'A' && info.DocumentStatus != 'D'" @click="handleKingdeeDo('submit')">提交</el-button>
              <el-button type="warning" size="medium" :disabled="info.DocumentStatus != 'B'" @click="handleKingdeeDo('audit')">审核</el-button>
              <el-button type="info" size="medium" :disabled="info.DocumentStatus != 'B'" @click="handleKingdeeDo('revoke')">撤销</el-button>
              <el-button type="danger" size="medium" :disabled="info.DocumentStatus != 'A' && info.DocumentStatus != 'D'" @click="handleKingdeeDo('delete')">删除</el-button>
              <el-button type="warning" size="medium" :disabled="info.DocumentStatus != 'C'" @click="handleKingdeeDo('unAudit')">反审</el-button>
            </template>
            <el-button type="primary" size="medium" :disabled="info.ForbidStatus == 'B'" @click="handleKingdeeDo('forbid')">禁用</el-button>
            <el-button type="success" size="medium" :disabled="info.ForbidStatus == 'A'" @click="handleKingdeeDo('enable')">反禁用</el-button>
            <el-button type="default" size="medium" icon="el-icon-edit" @click="handleUpdate" v-if="info.ForbidStatus == 'A' && (info.DocumentStatus == 'A' || info.DocumentStatus == 'D')">修改</el-button>
          </template>
        </div>
        <el-form ref="form" :model="form" :rules="rules" label-width="7em" :disabled="disabled">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="物料编码" prop="fmaterialid">
                <material-search-select :keyword.sync="form.fmaterialid" :showLabel="false" @callBack="handleMaterialSearchSelect($event)" style="width: 100%" :disabled="!!form.fid || isHas" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="物料名称" prop="fmaterialname">
                <el-input v-model="form.fmaterialname" readonly placeholder="请输入物料名称" :disabled="!!form.fid || isHas" />
              </el-form-item>
            </el-col>
            <el-col :span="20">
              <el-form-item label="换算关系">
                <div style="display: flex; align-items: center">
                  <!-- 换算关系 -->
                  <el-form-item prop="fconvertdenominator" label-width="0">
                    <el-input v-model="form.fconvertdenominator" :placeholder="form.fmaterialid ? '请输入换算关系' : '请选择物料'" :disabled="!form.fmaterialid" />
                  </el-form-item>
                  <!-- 单位 -->
                  <el-form-item prop="fcurrentunitid" label-width="0">
                    <el-select v-model="form.fcurrentunitid" :placeholder="form.fmaterialid ? '请选择单位' : '请选择物料'" style="width: 150px; flex-shrink: 0" :disabled="!form.fmaterialid || !!form.fid">
                      <el-option v-for="(item, index) in UnitList" :key="index" :label="item.FName" :value="item.FNumber"></el-option>
                    </el-select>
                  </el-form-item>
                  <span style="padding: 0 10px">=</span>
                  <!-- 分子 -->
                  <el-form-item prop="fconvertnumerator" label-width="0">
                    <el-input v-model="form.fconvertnumerator" :placeholder="form.fdestunitid ? '请输入分子' : '请选择物料'" :disabled="!form.fmaterialid" />
                  </el-form-item>
                  <!-- 基本单位 -->
                  <el-select v-model="form.fdestunitid" :placeholder="form.fdestunitid ? '请选择' : '请选择物料'" style="width: 150px; flex-shrink: 0" disabled>
                    <el-option v-for="(item, index) in UnitList" :key="index" :label="item.FName" :value="item.FNumber"></el-option>
                  </el-select>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="换算类型" prop="fconverttype">
                <el-select v-model="form.fconverttype" placeholder="请选择" style="width: 100%">
                  <el-option label="固定" value="0" />
                  <el-option label="浮动" value="1" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="备注" prop="fdescription">
                <el-input v-model="form.fdescription" placeholder="请输入" type="textarea" resize="none" :autosize="{ minRows: 2, maxRows: 4 }" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <template v-if="disabled">
          <el-button class="custom-dialog-btn primary" @click="handleClose()">关 闭</el-button>
        </template>
        <template v-else>
          <el-button class="custom-dialog-btn" @click="handleClose()">取 消</el-button>
          <el-button class="custom-dialog-btn primary" @click="handleSubmit">确 定</el-button>
        </template>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { kingdee } from '@/minix'
import { getMaterialUnitConvertDetail, saveMaterialUnitConvert, submitMaterialUnitConvert, auditMaterialUnitConvert, cancelMaterialUnitConvert, deleteMaterialUnitConvert, unAuditMaterialUnitConvert, forbidMaterialUnitConvert, enableMaterialUnitConvert } from '@/api/kingdee/materialUnit'
import { isNumber, isNumberLength } from '@/utils/validate'

export default {
  name: 'UnitCreate',
  mixins: [kingdee],
  components: { materialSearchSelect: () => import('@/components/SearchSelect/material') },
  data() {
    return {
      title: '物料单位换算-新增',
      open: false,
      form: {},
      oldForbidStatus: '',
      info: {},
      rules: {
        fmaterialid: [{ required: true, message: '请选择物料', trigger: ['blur', 'change'] }],
        fconvertdenominator: [
          { required: true, message: '请输入换算关系', trigger: 'blur' },
          { validator: isNumber, message: '请输入正确的换算关系', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: 'blur' }
        ],
        fcurrentunitid: [{ required: true, message: '请选择单位', trigger: 'change' }],
        fconvertnumerator: [
          { required: true, message: '请输入分子', trigger: 'blur' },
          { validator: isNumber, message: '请输入正确的分子', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: 'blur' }
        ],
        fconverttype: [{ required: true, message: '请选择换算类型', trigger: 'change' }]
      },
      disabled: false,
      isHas: false
    }
  },
  methods: {
    reset() {
      this.form = {
        fconvertdenominator: undefined, // 换算关系
        fconvertnumerator: undefined, // 分子
        fconverttype: undefined, // 换算类型
        fcurrentunitid: undefined, // 单位
        fdescription: undefined, // 备注
        fdestunitid: undefined, // 基本单位
        fid: undefined, // id
        fmaterialid: undefined, // 物料编码
        fmaterialname: undefined // 物料名称
      }
      this.isHas = false
      this.resetForm('form')
    },
    // 打开单位换算
    handleOpen(item = {}) {
      const { BillNo, isHas = false } = item
      this.reset()
      if (isHas) {
        this.form = {
          fmaterialid: item.fmaterialid,
          fmaterialname: item.fmaterialname,
          fdestunitid: item.fdestunitid
        }
        this.isHas = true
        this.title = '物料单位换算-新增'
        this.disabled = false
        this.open = true
      } else if (BillNo) {
        getMaterialUnitConvertDetail({ billNo: BillNo }).then(res => {
          const { code, msg, data } = res
          if (code === 200) {
            const info = data?.result?.result || {}
            const Description = (info.Description && info.Description.map(item => item.Value)) || ''
            this.form = {
              fconvertdenominator: info.ConvertDenominator || undefined, // 换算关系
              fconvertnumerator: info.ConvertNumerator || undefined, // 分子
              fconverttype: info.ConvertType || undefined, // 换算类型
              fcurrentunitid: info.CurrentUnitId?.Number || undefined, // 单位
              fdescription: Description.toString() || undefined, // 备注
              fdestunitid: info.DestUnitId?.Number || undefined, // 基本单位
              fid: info.Id || undefined, // id
              fmaterialid: info.MaterialId?.Number || undefined, // 物料编码
              fmaterialname: item.MaterialName || undefined // 物料名称
            }
            this.title = '物料单位换算-修改'
            this.disabled = true
            this.open = true
            this.info = info
            this.oldForbidStatus = info.ForbidStatus
          } else this.$message.error(msg)
        })
      } else {
        this.title = '物料单位换算-新增'
        this.disabled = false
        this.open = true
      }
    },
    // 弹出框关闭
    beforeClose() {
      this.handleClose()
      this.$emit('update', { billNo: this.info.BillNo, newStatus: this.info.DocumentStatus })
    },
    // 取消操作
    handleClose(flag = false) {
      this.open = false
      if (this.oldForbidStatus && this.info.ForbidStatus != this.oldForbidStatus) {
        return this.$emit('callBack', true)
      }
      this.$emit('callBack', flag)
    },
    // 提交操作
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          saveMaterialUnitConvert(this.form).then(res => {
            const { code, msg } = res
            if (code === 200) {
              const message = this.form.fid ? '修改成功' : '新增成功'
              this.$message.success(message)
              this.handleClose(true)
            } else this.$message.error(msg)
          })
        }
      })
    },
    // 物料选择 回调
    handleMaterialSearchSelect(event) {
      const { Number, Name, Unit } = event
      this.form.fmaterialid = Number
      this.form.fmaterialname = Name
      this.form.fdestunitid = this.UnitList.find(item => item.FName == Unit)?.FNumber || ''
    },
    // 修改
    handleUpdate() {
      this.disabled = false
    },
    // 金蝶操作
    // prettier-ignore
    handleKingdeeDo(type) {
      const number = this.info.BillNo
      if (!number) return 
      switch (type) {
        case 'submit':
          // 提交
          this.$modal.confirm('确认要提交该物料单位换算吗？').then(() => {
            submitMaterialUnitConvert({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('提交成功')
                this.$set(this.info, 'DocumentStatus', 'B')
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'audit':
          // 审核
          this.$modal.confirm('确认要审核该物料单位换算吗？').then(() => {
            auditMaterialUnitConvert({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('审核成功')
                this.$set(this.info, 'DocumentStatus', 'C')
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'revoke':
          // 撤销
          this.$modal.confirm('确认要撤销该物料单位换算吗？').then(() => {
            cancelMaterialUnitConvert({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('撤销成功')
                this.$set(this.info, 'DocumentStatus', 'D')
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'unAudit':
          // 反审核
          this.$modal.confirm('确认要反审核该物料单位换算吗？').then(() => {
            unAuditMaterialUnitConvert({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('反审核成功')
                this.$set(this.info, 'DocumentStatus', 'D')
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'delete':
          // 删除
          this.$modal.confirm('确认要删除该物料单位换算吗？').then(() => {
            deleteMaterialUnitConvert({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('删除成功')
                this.handleClose(true)
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'forbid':
          // 禁用
          this.$modal.confirm('确认要禁用该物料单位换算吗？').then(() => {
            forbidMaterialUnitConvert({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) { 
                this.$message.success('禁用成功')
                this.$set(this.info, 'ForbidStatus', 'B')
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'enable':
          // 反禁用
          this.$modal.confirm('确认要反禁用该物料单位换算吗？').then(() => {
            enableMaterialUnitConvert({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('反禁用成功')
                this.$set(this.info, 'ForbidStatus', 'A')
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
      }
    }
  }
}
</script>
<style scoped lang="scss">
@import '@/assets/styles/custom-new.scss';
.kindeeButton {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}
</style>
