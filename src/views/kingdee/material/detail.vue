<template>
  <div>
    <el-dialog v-dialogDragBox title="物料详情" :visible.sync="open" width="90%" class="custom-dialog" append-to-body :before-close="beforeClose">
      <div class="material">
        <div class="kindeeButton" :key="info.DocumentStatus">
          <el-button type="primary" size="medium" v-if="info.DocumentStatus == 'A' || info.DocumentStatus == 'D'" @click="handleKingdeeDo('submit')" v-hasPermi="['kingdee:material:submit']">提交</el-button>
          <el-button type="primary" size="medium" disabled v-else v-hasPermi="['kingdee:material:submit']">提交</el-button>
          <el-button type="warning" size="medium" v-if="info.DocumentStatus == 'B'" @click="handleKingdeeDo('audit')" v-hasPermi="['kingdee:material:audit']">审核</el-button>
          <el-button type="warning" size="medium" disabled v-else v-hasPermi="['kingdee:material:audit']">审核</el-button>
          <el-button type="info" size="medium" v-if="info.DocumentStatus == 'B'" @click="handleKingdeeDo('revoke')" v-hasPermi="['kingdee:material:revoke']">撤销</el-button>
          <el-button type="info" size="medium" disabled v-else v-hasPermi="['kingdee:material:revoke']">撤销</el-button>
          <el-button type="danger" size="medium" v-if="info.DocumentStatus == 'A' || info.DocumentStatus == 'D'" @click="handleKingdeeDo('delete')" v-hasPermi="['kingdee:material:delete']">删除</el-button>
          <el-button type="danger" size="medium" disabled v-else v-hasPermi="['kingdee:material:delete']">删除</el-button>
          <el-button type="warning" size="medium" v-if="info.DocumentStatus == 'C'" @click="handleKingdeeDo('unAudit')" v-hasPermi="['kingdee:material:unAudit']">反审</el-button>
          <el-button type="warning" size="medium" disabled v-else v-hasPermi="['kingdee:material:unAudit']">反审</el-button>
          <el-button type="success" size="medium" @click="handleUnitConvert" v-if="info.Number" v-hasPermi="['kingdee:materialUnit:list']">单位换算</el-button>
        </div>
        <div class="material-basics">
          <div class="material-basics-item">
            <b>创建组织</b>
            <span>{{ info.CreateOrgId && getArrToStr(info.CreateOrgId.Name) }}</span>
          </div>
          <div class="material-basics-item">
            <b>使用组织</b>
            <span>{{ info.UseOrgId && getArrToStr(info.UseOrgId.Name) }}</span>
          </div>
          <div class="material-basics-item">
            <b>编码</b>
            <span>{{ info.Number }}</span>
          </div>
          <div class="material-basics-item">
            <b>名称</b>
            <span>{{ info.Name && getArrToStr(info.Name) }}</span>
          </div>
        </div>
        <div class="material-tabs">
          <el-tabs type="border-card" v-model="tabsActive" @tab-click="handleTabsClick">
            <el-tab-pane v-for="item in tabsOptions" :key="item.name" :label="item.label" :name="item.name">
              <!-- 基础 -->
              <template v-if="tabsActive == 'base'">
                <!-- 基本信息 -->
                <el-descriptions border :column="4" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 4 - 7em - 22px)' }">
                  <template slot="title">
                    <div class="customTitle">基本信息</div>
                  </template>
                  <el-descriptions-item label="规格型号">{{ info.Specification && getArrToStr(info.Specification) }}</el-descriptions-item>
                  <el-descriptions-item label="助记码">{{ info.FMnemonicCode }}</el-descriptions-item>
                  <el-descriptions-item label="旧物料编码">{{ info.OldNumber }}</el-descriptions-item>
                  <el-descriptions-item label="条码">{{ MaterialBase.BARCODE }}</el-descriptions-item>
                  <el-descriptions-item label="描述">{{ info.Description && getArrToStr(info.Description) }}</el-descriptions-item>
                  <el-descriptions-item label="物料分组">{{ info.MaterialGroup && info.MaterialGroup.Name && getArrToStr(info.MaterialGroup.Name) }}</el-descriptions-item>
                  <el-descriptions-item label="物料属性">{{ MaterialBase.ErpClsID && getOptionLabel(ErpClsIDOptions, MaterialBase.ErpClsID) }}</el-descriptions-item>
                  <el-descriptions-item label="配置生产">{{ MaterialBase.CONFIGTYPE && getOptionLabel(ConfigTypeOptions, MaterialBase.CONFIGTYPE) }}</el-descriptions-item>
                  <el-descriptions-item label="特征件子项">{{ MaterialBase.FeatureItem && getOptionLabel(FeatureItemOptions, MaterialBase.FeatureItem) }}</el-descriptions-item>
                  <el-descriptions-item label="套件">{{ MaterialBase.Suite && getOptionLabel(SuiteOptions, MaterialBase.Suite) }}</el-descriptions-item>
                  <el-descriptions-item label="基本单位">{{ MaterialBase.BaseUnitId && MaterialBase.BaseUnitId.Name && getArrToStr(MaterialBase.BaseUnitId.Name) }}</el-descriptions-item>
                  <el-descriptions-item label="禁用原因">{{ info.FORBIDREASON && getArrToStr(info.FORBIDREASON) }}</el-descriptions-item>
                </el-descriptions>
                <!-- 控制 -->
                <el-descriptions border :column="6" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 6 - 7em - 22px)' }">
                  <template slot="title">
                    <div class="customTitle">控制</div>
                  </template>
                  <el-descriptions-item label="允许采购">
                    <el-checkbox :value="MaterialBase.IsPurchase" disabled></el-checkbox>
                  </el-descriptions-item>
                  <el-descriptions-item label="允许销售">
                    <el-checkbox :value="MaterialBase.IsSale" disabled></el-checkbox>
                  </el-descriptions-item>
                  <el-descriptions-item label="允许库存">
                    <el-checkbox :value="MaterialBase.IsInventory" disabled></el-checkbox>
                  </el-descriptions-item>
                  <el-descriptions-item label="允许生产">
                    <el-checkbox :value="MaterialBase.IsProduce" disabled></el-checkbox>
                  </el-descriptions-item>
                  <el-descriptions-item label="允许委外">
                    <el-checkbox :value="MaterialBase.IsSubContract" disabled></el-checkbox>
                  </el-descriptions-item>
                  <el-descriptions-item label="允许转资产">
                    <el-checkbox :value="MaterialBase.IsAsset" disabled></el-checkbox>
                  </el-descriptions-item>
                </el-descriptions>
                <!-- 财务信息 -->
                <el-descriptions border :column="4" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 4 - 7em - 22px)' }">
                  <template slot="title">
                    <div class="customTitle">财务信息</div>
                  </template>
                  <el-descriptions-item label="默认税率">{{ MaterialBase.TaxRateId && MaterialBase.TaxRateId.Name && getArrToStr(MaterialBase.TaxRateId.Name) }}</el-descriptions-item>
                  <el-descriptions-item label="存货类别">{{ MaterialBase.CategoryID && MaterialBase.CategoryID.Name && getArrToStr(MaterialBase.CategoryID.Name) }}</el-descriptions-item>
                  <el-descriptions-item label="税分类">{{ MaterialBase.TaxType && MaterialBase.TaxType.FDataValue && getArrToStr(MaterialBase.TaxType.FDataValue) }}</el-descriptions-item>
                  <el-descriptions-item label="结算成本价加减价比例(%)">{{ MaterialBase.CostPriceRate }}</el-descriptions-item>
                </el-descriptions>
                <!-- 重量信息 -->
                <el-descriptions border :column="3" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 3 - 7em - 22px)' }">
                  <template slot="title">
                    <div class="customTitle">重量信息</div>
                  </template>
                  <el-descriptions-item label="毛重">{{ MaterialBase.GROSSWEIGHT || '' }}</el-descriptions-item>
                  <el-descriptions-item label="净重">{{ MaterialBase.NETWEIGHT || '' }}</el-descriptions-item>
                  <el-descriptions-item label="重量单位">{{ MaterialBase.WEIGHTUNITID && MaterialBase.WEIGHTUNITID.Name && getArrToStr(MaterialBase.WEIGHTUNITID.Name) }}</el-descriptions-item>
                </el-descriptions>
                <!-- 尺寸信息 -->
                <el-descriptions border :column="5" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 5 - 7em - 22px)' }">
                  <template slot="title">
                    <div class="customTitle">尺寸信息</div>
                  </template>
                  <el-descriptions-item label="长">{{ MaterialBase.LENGTH || '' }}</el-descriptions-item>
                  <el-descriptions-item label="宽">{{ MaterialBase.WIDTH || '' }}</el-descriptions-item>
                  <el-descriptions-item label="高">{{ MaterialBase.HEIGHT || '' }}</el-descriptions-item>
                  <el-descriptions-item label="体积">{{ MaterialBase.VOLUME || '' }}</el-descriptions-item>
                  <el-descriptions-item label="尺寸单位">{{ MaterialBase.VOLUMEUNITID && MaterialBase.VOLUMEUNITID.Name && getArrToStr(MaterialBase.VOLUMEUNITID.Name) }}</el-descriptions-item>
                </el-descriptions>
                <!-- 状态 -->
                <el-descriptions border :column="4" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 4 - 7em - 22px)' }">
                  <template slot="title">
                    <div class="customTitle">状态</div>
                  </template>
                  <el-descriptions-item label="数据状态">{{ info.DocumentStatus && getOptionLabel(DocumentStatusOptions, info.DocumentStatus) }}</el-descriptions-item>
                  <el-descriptions-item label="禁用状态">{{ info.ForbidStatus && getOptionLabel(ForbidStatusOptions, info.ForbidStatus) }}</el-descriptions-item>
                  <el-descriptions-item label="已使用">{{ info.RefStatus && getOptionLabel(RefStatusOptions, info.RefStatus) }}</el-descriptions-item>
                  <el-descriptions-item label="是否变更中">
                    <el-checkbox :value="MaterialBase.IsChange" disabled></el-checkbox>
                  </el-descriptions-item>
                </el-descriptions>
              </template>
              <!-- 库存 -->
              <template v-if="tabsActive == 'stock'">
                <!-- 存储 -->
                <el-descriptions border :column="4" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 4 - 7em - 22px)' }">
                  <template slot="title">
                    <div class="customTitle">存储</div>
                  </template>
                  <el-descriptions-item label="库存单位">{{ MaterialStock.StoreUnitID && MaterialStock.StoreUnitID.Name && getArrToStr(MaterialStock.StoreUnitID.Name) }}</el-descriptions-item>
                  <el-descriptions-item label="辅助单位">{{ MaterialStock.AuxUnitID && MaterialStock.AuxUnitID.Name && getArrToStr(MaterialStock.AuxUnitID.Name) }}</el-descriptions-item>
                  <el-descriptions-item label="换算方向">{{ MaterialStock.UnitConvertDir && getOptionLabel(UnitConvertDirOptions, MaterialStock.UnitConvertDir) }}</el-descriptions-item>
                  <el-descriptions-item label="仓库">{{ MaterialStock.WarehouseID && MaterialStock.WarehouseID.Name && getArrToStr(MaterialStock.WarehouseID.Name) }}</el-descriptions-item>
                  <el-descriptions-item label="仓位">{{ MaterialStock.StockPlaceId && MaterialStock.StockPlaceId.Name && getArrToStr(MaterialStock.StockPlaceId.Name) }}</el-descriptions-item>
                  <el-descriptions-item label="单箱标准数量">{{ MaterialStock.BoxStandardQty || '' }}</el-descriptions-item>
                </el-descriptions>
                <!-- 库存控制 -->
                <el-descriptions border :column="4" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 4 - 7em - 22px)' }">
                  <template slot="title">
                    <div class="customTitle">库存控制</div>
                  </template>
                  <el-descriptions-item label="可锁库">
                    <el-checkbox :value="MaterialStock.IsLockStock" disabled></el-checkbox>
                  </el-descriptions-item>
                </el-descriptions>
                <!-- 循环盘点 -->
                <el-descriptions border :column="4" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 4 - 7em - 22px)' }">
                  <template slot="title">
                    <div class="customTitle">循环盘点</div>
                  </template>
                  <el-descriptions-item label="启用周期盘点">
                    <el-checkbox :value="MaterialStock.IsCycleCounting" disabled></el-checkbox>
                  </el-descriptions-item>
                  <el-descriptions-item label="盘点周期">每{{ MaterialStock.CountCycle && getOptionLabel(CountCycleOptions, MaterialStock.CountCycle) }}第{{ MaterialStock.CountDay }}天</el-descriptions-item>
                  <el-descriptions-item label="必盘">
                    <el-checkbox :value="MaterialStock.IsMustCounting" disabled></el-checkbox>
                  </el-descriptions-item>
                </el-descriptions>
                <!-- 管理 -->
                <el-descriptions border :column="4" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 4 - 7em - 22px)' }">
                  <template slot="title">
                    <div class="customTitle">管理</div>
                  </template>
                  <el-descriptions-item label="启用批号管理">
                    <el-checkbox :value="MaterialStock.IsBatchManage" disabled></el-checkbox>
                  </el-descriptions-item>
                  <el-descriptions-item label="批号编码规则">{{ MaterialStock.BatchRuleID && MaterialStock.BatchRuleID.Name && getArrToStr(MaterialStock.BatchRuleID.Name) }}</el-descriptions-item>
                  <el-descriptions-item label="启用保质期管理">
                    <el-checkbox :value="MaterialStock.IsKFPeriod" disabled></el-checkbox>
                  </el-descriptions-item>
                  <el-descriptions-item label="批号附属信息">
                    <el-checkbox :value="MaterialStock.IsExpParToFlot" disabled></el-checkbox>
                  </el-descriptions-item>
                  <el-descriptions-item label="保质期单位">{{ MaterialStock.ExpUnit && getOptionLabel(ExpUnitOptions, MaterialStock.ExpUnit) }}</el-descriptions-item>
                  <el-descriptions-item label="保质期">{{ MaterialStock.ExpPeriod }}</el-descriptions-item>
                  <el-descriptions-item label="在架寿命期">{{ MaterialStock.OnlineLife }}</el-descriptions-item>
                </el-descriptions>
                <!-- 库存成本 -->
                <el-descriptions border :column="3" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 3 - 7em - 22px)' }">
                  <template slot="title">
                    <div class="customTitle">库存成本</div>
                  </template>
                  <el-descriptions-item label="参考成本">￥{{ MaterialStock.RefCost }}</el-descriptions-item>
                  <el-descriptions-item label="成本单位">{{ MaterialBase.BaseUnitId && MaterialBase.BaseUnitId.Name && getArrToStr(MaterialBase.BaseUnitId.Name) }}</el-descriptions-item>
                  <el-descriptions-item label="币别">{{ MaterialStock.CurrencyId && MaterialStock.CurrencyId.Name && getArrToStr(MaterialStock.CurrencyId.Name) }}</el-descriptions-item>
                </el-descriptions>
                <!-- 库存计划 -->
                <el-descriptions border :column="5" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 5 - 7em - 22px)' }">
                  <template slot="title">
                    <div class="customTitle">库存计划</div>
                  </template>
                  <el-descriptions-item label="启用" :span="5">
                    <el-checkbox :value="MaterialStock.IsEnableMinStock" disabled>最小库存预警</el-checkbox>
                    <el-checkbox :value="MaterialStock.IsEnableMaxStock" disabled>最大库存预警</el-checkbox>
                    <el-checkbox :value="MaterialStock.IsEnableSafeStock" disabled>安全库存预警</el-checkbox>
                    <el-checkbox :value="MaterialStock.IsEnableReOrder" disabled>再订货点预警</el-checkbox>
                  </el-descriptions-item>
                  <el-descriptions-item label="最小库存">{{ MaterialStock.MinStock }}</el-descriptions-item>
                  <el-descriptions-item label="安全库存">{{ MaterialStock.SafeStock }}</el-descriptions-item>
                  <el-descriptions-item label="再订货点">{{ MaterialStock.ReOrderGood }}</el-descriptions-item>
                  <el-descriptions-item label="经济订货批量">{{ MaterialStock.EconReOrderQty }}</el-descriptions-item>
                  <el-descriptions-item label="最大库存">{{ MaterialStock.MaxStock }}</el-descriptions-item>
                </el-descriptions>
              </template>
              <!-- 销售 -->
              <template v-if="tabsActive == 'sales'">
                <!-- 销售 -->
                <el-descriptions border :column="4" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 4 - 7em - 22px)' }">
                  <template slot="title">
                    <div class="customTitle">销售</div>
                  </template>
                  <el-descriptions-item label="销售单位">{{ MaterialSale.SaleUnitId && MaterialSale.SaleUnitId.Name && getArrToStr(MaterialSale.SaleUnitId.Name) }}</el-descriptions-item>
                  <el-descriptions-item label="销售计价单位">{{ MaterialSale.SalePriceUnitId && MaterialSale.SalePriceUnitId.Name && getArrToStr(MaterialSale.SalePriceUnitId.Name) }}</el-descriptions-item>
                  <el-descriptions-item label="起订量">{{ MaterialSale.OrderQty }}</el-descriptions-item>
                  <el-descriptions-item label="超发控制单位">{{ MaterialSale.OutLmtUnit && getOptionLabel(OutLmtUnitOptions, MaterialSale.OutLmtUnit) }}</el-descriptions-item>
                  <el-descriptions-item label="超发上限(%)">{{ MaterialSale.OutStockLmtH }}</el-descriptions-item>
                  <el-descriptions-item label="超发下限(%)">{{ MaterialSale.OutStockLmtL }}</el-descriptions-item>
                  <el-descriptions-item label="代理销售减价比例(%)">{{ MaterialSale.AgentSalReduceRate }}</el-descriptions-item>
                  <el-descriptions-item label="税收分类编码">{{ MaterialSale.TaxCategoryCodeId && MaterialSale.TaxCategoryCodeId.Name && getArrToStr(MaterialSale.TaxCategoryCodeId.Name) }}</el-descriptions-item>
                  <el-descriptions-item label="享受税收优惠政策">
                    <el-checkbox :value="MaterialSale.IsTaxEnjoy" disabled></el-checkbox>
                  </el-descriptions-item>
                  <el-descriptions-item label="税收优惠政策类型">{{ MaterialSale.TaxDiscountsType && getOptionLabel(TaxDiscountsTypeOptions, MaterialSale.TaxDiscountsType) }}</el-descriptions-item>
                  <el-descriptions-item label="销售分组">{{ MaterialSale.SalGroup && MaterialSale.SalGroup.Name && getArrToStr(MaterialSale.SalGroup.Name) }}</el-descriptions-item>
                </el-descriptions>
                <!-- 销售控制 -->
                <el-descriptions border :column="4" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 4 - 7em - 22px)' }">
                  <template slot="title">
                    <div class="customTitle">销售控制</div>
                  </template>
                  <el-descriptions-item label="ATP检查">
                    <el-checkbox :value="MaterialSale.IsATPCheck" disabled></el-checkbox>
                  </el-descriptions-item>
                  <el-descriptions-item label="允许退货">
                    <el-checkbox :value="MaterialSale.IsReturn" disabled></el-checkbox>
                  </el-descriptions-item>
                  <el-descriptions-item label="部件可退">
                    <el-checkbox :value="MaterialSale.IsReturnPart" disabled></el-checkbox>
                  </el-descriptions-item>
                  <el-descriptions-item label="不参与可发量统计">
                    <el-checkbox :value="MaterialSale.UnValidateExpQty" disabled></el-checkbox>
                  </el-descriptions-item>
                  <el-descriptions-item label="销售出库替代物料">{{ (info.FMULREPLACEMATERIALID && getArrToStr(info.FMULREPLACEMATERIALID)) || '' }}</el-descriptions-item>
                </el-descriptions>
                <!-- 订货平台 -->
                <el-descriptions border :column="4" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 4 - 7em - 22px)' }">
                  <template slot="title">
                    <div class="customTitle">订货平台</div>
                  </template>
                  <el-descriptions-item label="允许发布到订货平台">
                    <el-checkbox :value="MaterialSale.AllowPublish" disabled></el-checkbox>
                  </el-descriptions-item>
                </el-descriptions>
                <!-- CRM -->
                <el-descriptions border :column="5" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 5 - 7em - 22px)' }">
                  <template slot="title">
                    <div class="customTitle">CRM</div>
                  </template>
                  <el-descriptions-item label="启用售后服务">
                    <el-checkbox :value="MaterialSale.ISAFTERSALE" disabled></el-checkbox>
                  </el-descriptions-item>
                  <el-descriptions-item label="生成产品档案">
                    <el-checkbox :value="MaterialSale.ISPRODUCTFILES" disabled></el-checkbox>
                  </el-descriptions-item>
                  <el-descriptions-item label="是否保修">
                    <el-checkbox :value="MaterialSale.ISWARRANTED" disabled></el-checkbox>
                  </el-descriptions-item>
                  <el-descriptions-item label="保修期">{{ MaterialSale.FWARRANTY || '' }}</el-descriptions-item>
                  <el-descriptions-item label="保修期单位">{{ MaterialSale.WARRANTYUNITID && getOptionLabel(ExpUnitOptions, MaterialSale.WARRANTYUNITID) }}</el-descriptions-item>
                </el-descriptions>
              </template>
              <!-- 采购 -->
              <template v-if="tabsActive == 'purchase'">
                <!-- 采购 -->
                <el-descriptions border :column="4" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 4 - 7em - 22px)' }">
                  <template slot="title">
                    <div class="customTitle">采购</div>
                  </template>
                  <el-descriptions-item label="采购单位">{{ MaterialPurchase.PurchaseUnitID && MaterialPurchase.PurchaseUnitID.Name && getArrToStr(MaterialPurchase.PurchaseUnitID.Name) }}</el-descriptions-item>
                  <el-descriptions-item label="采购计价单位">{{ MaterialPurchase.PurchasePriceUnitId && MaterialPurchase.PurchasePriceUnitId.Name && getArrToStr(MaterialPurchase.PurchasePriceUnitId.Name) }}</el-descriptions-item>
                  <el-descriptions-item label="采购组织">{{ MaterialPurchase.PurchaseOrgId && MaterialPurchase.PurchaseOrgId.Name && getArrToStr(MaterialPurchase.PurchaseOrgId.Name) }}</el-descriptions-item>
                  <el-descriptions-item label="采购组">{{ MaterialPurchase.PurchaseGroupId && MaterialPurchase.PurchaseGroupId.Name && getArrToStr(MaterialPurchase.PurchaseGroupId.Name) }}</el-descriptions-item>
                  <el-descriptions-item label="采购员">{{ MaterialPurchase.PurchaserId && MaterialPurchase.PurchaserId.Name && getArrToStr(MaterialPurchase.PurchaserId.Name) }}</el-descriptions-item>
                  <el-descriptions-item label="默认供应商">{{ MaterialPurchase.DefaultVendor && MaterialPurchase.DefaultVendor.Name && getArrToStr(MaterialPurchase.DefaultVendor.Name) }}</el-descriptions-item>
                  <el-descriptions-item label="费用项目">{{ MaterialPurchase.ChargeID && MaterialPurchase.ChargeID.Name && getArrToStr(MaterialPurchase.ChargeID.Name) }}</el-descriptions-item>
                  <el-descriptions-item label="采购类型">{{ MaterialPurchase.POBillTypeId && MaterialPurchase.POBillTypeId.Name && getArrToStr(MaterialPurchase.POBillTypeId.Name) }}</el-descriptions-item>
                  <el-descriptions-item label="配额管理">
                    <el-checkbox :value="MaterialPurchase.IsQuota" disabled></el-checkbox>
                  </el-descriptions-item>
                  <el-descriptions-item label="配额方式">{{ MaterialPurchase.QuotaType && getOptionLabel(QuotaTypeOptions, MaterialPurchase.QuotaType) }}</el-descriptions-item>
                  <el-descriptions-item label="最小拆分数量">{{ MaterialPurchase.MinSplitQty }}</el-descriptions-item>
                  <el-descriptions-item label="是否VMI业务">
                    <el-checkbox :value="MaterialPurchase.IsVmiBusiness" disabled></el-checkbox>
                  </el-descriptions-item>
                </el-descriptions>
                <!-- 采购控制 -->
                <el-descriptions border :column="4" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 4 - 7em - 22px)' }">
                  <template slot="title">
                    <div class="customTitle">采购控制</div>
                  </template>
                  <el-descriptions-item label="需要请购">
                    <el-checkbox :value="MaterialPurchase.IsPR" disabled></el-checkbox>
                  </el-descriptions-item>
                  <el-descriptions-item label="货源控制">
                    <el-checkbox :value="MaterialPurchase.IsSourceControl" disabled></el-checkbox>
                  </el-descriptions-item>
                  <el-descriptions-item label="允许退料">
                    <el-checkbox :value="MaterialPurchase.IsReturnMaterial" disabled></el-checkbox>
                  </el-descriptions-item>
                  <el-descriptions-item label="收货上限比例(%)">{{ MaterialPurchase.ReceiveMaxScale }}</el-descriptions-item>
                  <el-descriptions-item label="收货下限比例(%)">{{ MaterialPurchase.ReceiveMinScale }}</el-descriptions-item>
                  <el-descriptions-item label="收货提前天数">{{ MaterialPurchase.ReceiveAdvanceDays }}</el-descriptions-item>
                  <el-descriptions-item label="收货延迟天数">{{ MaterialPurchase.ReceiveDelayDays }}</el-descriptions-item>
                </el-descriptions>
                <!-- 条码管理 -->
                <el-descriptions border :column="4" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 4 - 7em - 22px)' }">
                  <template slot="title">
                    <div class="customTitle">条码管理</div>
                  </template>
                  <el-descriptions-item label="默认条码规则">{{ MaterialPurchase.DefBarCodeRuleId && MaterialPurchase.DefBarCodeRuleId.Name && getArrToStr(MaterialPurchase.DefBarCodeRuleId.Name) }}</el-descriptions-item>
                  <el-descriptions-item label="最小包装数">{{ MaterialPurchase.MinPackCount }}</el-descriptions-item>
                  <el-descriptions-item label="重复打印数">{{ MaterialPurchase.PrintCount }}</el-descriptions-item>
                </el-descriptions>
                <!-- 委外 -->
                <el-descriptions border :column="4" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 4 - 7em - 22px)' }">
                  <template slot="title">
                    <div class="customTitle">委外</div>
                  </template>
                  <el-descriptions-item label="委外单位">{{ MaterialSubcon.SubconUnitId && MaterialSubcon.SubconUnitId.Name && getArrToStr(MaterialSubcon.SubconUnitId.Name) }}</el-descriptions-item>
                  <el-descriptions-item label="委外计价单位">{{ MaterialSubcon.SubconPriceUnitId && MaterialSubcon.SubconPriceUnitId.Name && getArrToStr(MaterialSubcon.SubconPriceUnitId.Name) }}</el-descriptions-item>
                  <el-descriptions-item label="委外类型">{{ MaterialSubcon.SUBBILLTYPE && MaterialSubcon.SUBBILLTYPE.Name && getArrToStr(MaterialSubcon.SUBBILLTYPE.Name) }}</el-descriptions-item>
                </el-descriptions>
                <!-- 日计划 -->
                <el-descriptions border :column="4" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 4 - 7em - 22px)' }">
                  <template slot="title">
                    <div class="customTitle">日计划</div>
                  </template>
                  <el-descriptions-item label="日产量">{{ MaterialPurchase.DailyOutQtySub }}{{ MaterialBase.BaseUnitId && MaterialBase.BaseUnitId.Name && getArrToStr(MaterialBase.BaseUnitId.Name) }}</el-descriptions-item>
                  <el-descriptions-item label="默认产线">{{ MaterialPurchase.DefaultLineIdSub && MaterialPlan.DefaultLineIdSub.Name && getArrToStr(MaterialPlan.DefaultLineIdSub.Name) }}</el-descriptions-item>
                  <el-descriptions-item label="启用日排产">
                    <el-checkbox :value="MaterialPurchase.IsEnableScheduleSub" disabled></el-checkbox>
                  </el-descriptions-item>
                </el-descriptions>
                <!-- 其他 -->
                <el-descriptions border :column="4" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 4 - 7em - 22px)' }">
                  <template slot="title">
                    <div class="customTitle">其他</div>
                  </template>
                  <el-descriptions-item label="代理采购加成比例(%)">{{ MaterialPurchase.AgentPurPlusRate }}</el-descriptions-item>
                </el-descriptions>
              </template>
              <!-- 质量 -->
              <template v-if="tabsActive == 'quality'">
                <!-- 检验设置 -->
                <el-descriptions border :column="4" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 4 - 7em - 22px)' }">
                  <template slot="title">
                    <div class="customTitle">检验设置</div>
                  </template>
                  <el-descriptions-item label="来料检验">
                    <el-checkbox :value="MaterialQM.CheckIncoming" disabled></el-checkbox>
                  </el-descriptions-item>
                  <el-descriptions-item label="产品检验">
                    <el-checkbox :value="MaterialQM.CheckProduct" disabled></el-checkbox>
                  </el-descriptions-item>
                  <el-descriptions-item label="产品首检">
                    <el-checkbox :value="MaterialQM.IsFirstInspect" disabled></el-checkbox>
                  </el-descriptions-item>
                  <el-descriptions-item label="首检控制方式">{{ MaterialQM.FirstQCControlType && getOptionLabel(FirstQCControlTypeOptions, MaterialQM.FirstQCControlType) }}</el-descriptions-item>
                  <el-descriptions-item label="库存检验">
                    <el-checkbox :value="MaterialQM.CheckStock" disabled></el-checkbox>
                  </el-descriptions-item>
                  <el-descriptions-item label="退货检验">
                    <el-checkbox :value="MaterialQM.CheckReturn" disabled></el-checkbox>
                  </el-descriptions-item>
                  <el-descriptions-item label="发货检验">
                    <el-checkbox :value="MaterialQM.CheckDelivery" disabled></el-checkbox>
                  </el-descriptions-item>
                  <el-descriptions-item label="其他检验">
                    <el-checkbox :value="MaterialQM.CheckOther" disabled></el-checkbox>
                  </el-descriptions-item>
                  <el-descriptions-item label="受托材料检验">
                    <el-checkbox :value="MaterialQM.CheckEntrusted" disabled></el-checkbox>
                  </el-descriptions-item>
                  <el-descriptions-item label="生产退料检验">
                    <el-checkbox :value="MaterialQM.CheckReturnMtrl" disabled></el-checkbox>
                  </el-descriptions-item>
                  <el-descriptions-item label="委外退料检验">
                    <el-checkbox :value="MaterialQM.CheckSubRtnMtrl" disabled></el-checkbox>
                  </el-descriptions-item>
                </el-descriptions>
                <!-- 复检设置 -->
                <el-descriptions border :column="4" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 4 - 7em - 22px)' }">
                  <template slot="title">
                    <div class="customTitle">复检设置</div>
                  </template>
                  <el-descriptions-item label="启用库存周期复检">
                    <el-checkbox :value="MaterialQM.EnableCyclistQCSTK" disabled></el-checkbox>
                  </el-descriptions-item>
                  <el-descriptions-item label="复检周期">{{ MaterialQM.StockCycle ? MaterialQM.StockCycle + '天' : '' }}</el-descriptions-item>
                  <el-descriptions-item label="启用库存周期复检提醒">
                    <el-checkbox :value="MaterialQM.EnableCyclistQCSTKEW" disabled></el-checkbox>
                  </el-descriptions-item>
                  <el-descriptions-item label="提醒提前期">{{ MaterialQM.EWLeadDay ? MaterialQM.EWLeadDay + '天' : '' }}</el-descriptions-item>
                </el-descriptions>
                <!-- 方案设置 -->
                <el-descriptions border :column="4" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 4 - 7em - 22px)' }">
                  <template slot="title">
                    <div class="customTitle">方案设置</div>
                  </template>
                  <el-descriptions-item label="抽样方案">{{ MaterialQM.IncSampSchemeId && MaterialQM.IncSampSchemeId.Name && getArrToStr(MaterialQM.IncSampSchemeId.Name) }}</el-descriptions-item>
                  <el-descriptions-item label="质检方案">{{ MaterialQM.IncQcSchemeId && MaterialQM.IncQcSchemeId.Name && getArrToStr(MaterialQM.IncQcSchemeId.Name) }}</el-descriptions-item>
                  <el-descriptions-item label="质检组">{{ MaterialQM.InspectGroupId && MaterialQM.InspectGroupId.Name && getArrToStr(MaterialQM.InspectGroupId.Name) }}</el-descriptions-item>
                  <el-descriptions-item label="质检员">{{ MaterialQM.InspectorId && MaterialQM.InspectorId.Name && getArrToStr(MaterialQM.InspectorId.Name) }}</el-descriptions-item>
                </el-descriptions>
              </template>
              <!-- 生产 -->
              <template v-if="tabsActive == 'production'">
                <!-- 生产 -->
                <el-descriptions border :column="4" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 4 - 7em - 22px)' }">
                  <template slot="title">
                    <div class="customTitle">生产</div>
                  </template>
                  <el-descriptions-item label="生产车间">{{ MaterialProduce.WorkShopId && MaterialPlan.WorkShopId.Name && getArrToStr(MaterialPlan.WorkShopId.Name) }}</el-descriptions-item>
                  <el-descriptions-item label="生产单位">{{ MaterialProduce.ProduceUnitId && MaterialProduce.ProduceUnitId.Name && getArrToStr(MaterialProduce.ProduceUnitId.Name) }}</el-descriptions-item>
                  <el-descriptions-item label="入库超收比例(%)">{{ MaterialProduce.FinishReceiptOverRate }}</el-descriptions-item>
                  <el-descriptions-item label="入库欠收比例(%)">{{ MaterialProduce.FinishReceiptShortRate }}</el-descriptions-item>
                  <el-descriptions-item label="生产类型">{{ MaterialProduce.ProduceBillType && MaterialProduce.ProduceBillType.Name && getArrToStr(MaterialProduce.ProduceBillType.Name) }}</el-descriptions-item>
                  <el-descriptions-item label="组织间受托类型">{{ MaterialProduce.OrgTrustBillType && MaterialProduce.OrgTrustBillType.Name && getArrToStr(MaterialProduce.OrgTrustBillType.Name) }}</el-descriptions-item>
                  <el-descriptions-item label="生产线生产">
                    <el-checkbox :value="MaterialProduce.IsProductLine" disabled></el-checkbox>
                  </el-descriptions-item>
                  <el-descriptions-item label="序列号携带到父项">
                    <el-checkbox :value="MaterialProduce.IsSNCarryToParent" disabled></el-checkbox>
                  </el-descriptions-item>
                  <el-descriptions-item label="倒冲数量">{{ MaterialProduce.BackFlushType && getOptionLabel(BackFlushTypeOptions, MaterialProduce.BackFlushType) }}</el-descriptions-item>
                </el-descriptions>
                <!-- 物料清单 -->
                <el-descriptions border :column="4" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 4 - 7em - 22px)' }">
                  <template slot="title">
                    <div class="customTitle">物料清单</div>
                  </template>
                  <el-descriptions-item label="子项单位">{{ MaterialProduce.BOMUnitId && MaterialProduce.BOMUnitId.Name && getArrToStr(MaterialProduce.BOMUnitId.Name) }}</el-descriptions-item>
                  <el-descriptions-item label="消耗波动(%)">{{ MaterialProduce.ConsumVolatility }}</el-descriptions-item>
                  <el-descriptions-item label="变动损耗率(%)">{{ MaterialProduce.LossPercent }}</el-descriptions-item>
                  <el-descriptions-item label="固定损耗">{{ MaterialProduce.FIXLOSS }}</el-descriptions-item>
                  <el-descriptions-item label="可为主产品">
                    <el-checkbox :value="MaterialProduce.IsMainPrd" disabled></el-checkbox>
                  </el-descriptions-item>
                  <el-descriptions-item label="可为联副产品">
                    <el-checkbox :value="MaterialProduce.IsCoby" disabled></el-checkbox>
                  </el-descriptions-item>
                  <el-descriptions-item label="启用ECN">
                    <el-checkbox :value="MaterialProduce.IsECN" disabled></el-checkbox>
                  </el-descriptions-item>
                </el-descriptions>
                <!-- 发料控制 -->
                <el-descriptions border :column="4" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 4 - 7em - 22px)' }">
                  <template slot="title">
                    <div class="customTitle">发料控制</div>
                  </template>
                  <el-descriptions-item label="发料方式">{{ MaterialProduce.IssueType && getOptionLabel(IssueTypeOptions, MaterialProduce.IssueType) }}</el-descriptions-item>
                  <el-descriptions-item label="倒冲时机">{{ MaterialProduce.BKFLTime && getOptionLabel(BackFlushTimeOptions, MaterialProduce.BKFLTime) }}</el-descriptions-item>
                  <el-descriptions-item label="发料仓库">{{ MaterialProduce.PickStockId && MaterialProduce.PickStockId.Name && getArrToStr(MaterialProduce.PickStockId.Name) }}</el-descriptions-item>
                  <el-descriptions-item label="发料仓位">{{ MaterialProduce.PickBinId && MaterialProduce.PickBinId.Name && getArrToStr(MaterialProduce.PickBinId.Name) }}</el-descriptions-item>
                  <el-descriptions-item label="超发控制方式">{{ MaterialProduce.OverControlMode && getOptionLabel(OverControlModeOptions, MaterialProduce.OverControlMode) }}</el-descriptions-item>
                  <el-descriptions-item label="最小发料批量">{{ MaterialProduce.MinIssueQty }}{{ MaterialProduce.MinIssueUnitId && MaterialProduce.MinIssueUnitId.Name && getArrToStr(MaterialProduce.MinIssueUnitId.Name) }}</el-descriptions-item>
                  <el-descriptions-item label="领料考虑最小发料批量">
                    <el-checkbox :value="MaterialProduce.ISMinIssueQty" disabled></el-checkbox>
                  </el-descriptions-item>
                  <el-descriptions-item label="是否关键件">
                    <el-checkbox :value="MaterialProduce.IsKitting" disabled></el-checkbox>
                  </el-descriptions-item>
                  <el-descriptions-item label="是否齐套件">
                    <el-checkbox :value="MaterialProduce.IsCompleteSet" disabled></el-checkbox>
                  </el-descriptions-item>
                </el-descriptions>
                <!-- 日计划 -->
                <el-descriptions border :column="4" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 4 - 7em - 22px)' }">
                  <template slot="title">
                    <div class="customTitle">日计划</div>
                  </template>
                  <el-descriptions-item label="日产量">{{ MaterialPlan.DailyOutQty }}{{ MaterialBase.BaseUnitId && MaterialBase.BaseUnitId.Name && getArrToStr(MaterialBase.BaseUnitId.Name) }}</el-descriptions-item>
                  <el-descriptions-item label="默认产线">{{ MaterialProduce.DefaultLineId && MaterialProduce.DefaultLineId.Name && getArrToStr(MaterialProduce.DefaultLineId.Name) }}</el-descriptions-item>
                  <el-descriptions-item label="启用日排产">
                    <el-checkbox :value="MaterialProduce.IsEnableSchedule" disabled></el-checkbox>
                  </el-descriptions-item>
                </el-descriptions>
                <!-- 工艺路线 -->
                <el-descriptions border :column="4" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 4 - 7em - 22px)' }">
                  <template slot="title">
                    <div class="customTitle">工艺路线</div>
                  </template>
                  <el-descriptions-item label="默认工艺路线">{{ MaterialProduce.DefaultRouting && MaterialProduce.DefaultRouting.Name && getArrToStr(MaterialProduce.DefaultRouting.Name) }}</el-descriptions-item>
                  <el-descriptions-item label="标准工时">{{ MaterialProduce.PerUnitStandHour }}{{ MaterialProduce.StandHourUnitId && getOptionLabel(StandHourUnitIdOptions, MaterialProduce.StandHourUnitId) }}</el-descriptions-item>
                  <el-descriptions-item label="标准人员准备工时">{{ MaterialProduce.StdLaborPrePareTime || '' }}</el-descriptions-item>
                  <el-descriptions-item label="标准人员实作工时">{{ MaterialProduce.StdLaborProcessTime || '' }}</el-descriptions-item>
                  <el-descriptions-item label="标准机器准备工时">{{ MaterialProduce.StdMachinePrepareTime || '' }}</el-descriptions-item>
                  <el-descriptions-item label="标准机器实作工时">{{ MaterialProduce.StdMachineProcessTime || '' }}</el-descriptions-item>
                </el-descriptions>
                <!-- 模型 -->
                <el-descriptions border :column="4" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 4 - 7em - 22px)' }">
                  <template slot="title">
                    <div class="customTitle">模型</div>
                  </template>
                  <el-descriptions-item label="产品模型">{{ MaterialProduce.MDLID && MaterialProduce.MDLID.Name && getArrToStr(MaterialProduce.MDLID.Name) }}</el-descriptions-item>
                  <el-descriptions-item label="模型物料">{{ MaterialProduce.MdlMaterialId && MaterialProduce.MdlMaterialId.Name && getArrToStr(MaterialProduce.MdlMaterialId.Name) }}</el-descriptions-item>
                </el-descriptions>
              </template>
              <!-- 计划属性 -->
              <template v-if="tabsActive == 'planAttributes'">
                <!-- 计划 -->
                <el-descriptions border :column="4" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 4 - 7em - 22px)' }">
                  <template slot="title">
                    <div class="customTitle">计划</div>
                  </template>
                  <el-descriptions-item label="计划策略">{{ MaterialPlan.PlanningStrategy && getOptionLabel(PlanningStrategyOptions, MaterialPlan.PlanningStrategy) }}</el-descriptions-item>
                  <el-descriptions-item label="制造策略">{{ MaterialPlan.MfgPolicyId && MaterialPlan.MfgPolicyId.Name && getArrToStr(MaterialPlan.MfgPolicyId.Name) }}</el-descriptions-item>
                  <el-descriptions-item label="订货策略">{{ MaterialPlan.OrderPolicy && getOptionLabel(OrderPolicyOptions, MaterialPlan.OrderPolicy) }}</el-descriptions-item>
                  <el-descriptions-item label="计划区">{{ MaterialPlan.PlanWorkshop && MaterialPlan.PlanWorkshop.Name && getArrToStr(MaterialPlan.PlanWorkshop.Name) }}</el-descriptions-item>
                </el-descriptions>
                <!-- 提前期 -->
                <el-descriptions border :column="4" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 4 - 7em - 22px)' }">
                  <template slot="title">
                    <div class="customTitle">提前期</div>
                  </template>
                  <el-descriptions-item label="固定提前期">{{ MaterialPlan.FixLeadTime }}</el-descriptions-item>
                  <el-descriptions-item label="单位">{{ MaterialPlan.FixLeadTimeType && getOptionLabel(TimeTypeOptions, MaterialPlan.FixLeadTimeType) }}</el-descriptions-item>
                  <el-descriptions-item label="变动提前期">{{ MaterialPlan.VarLeadTime }}</el-descriptions-item>
                  <el-descriptions-item label="单位">{{ MaterialPlan.VarLeadTimeType && getOptionLabel(TimeTypeOptions, MaterialPlan.VarLeadTimeType) }}</el-descriptions-item>
                  <el-descriptions-item label="检验提前期">{{ MaterialPlan.CheckLeadTime }}</el-descriptions-item>
                  <el-descriptions-item label="单位">{{ MaterialPlan.CheckLeadTimeType && getOptionLabel(CheckLeadTimeTypeOptions, MaterialPlan.CheckLeadTimeType) }}</el-descriptions-item>
                  <el-descriptions-item label="累计提前期">{{ MaterialPlan.AccuLeadTime }}</el-descriptions-item>
                </el-descriptions>
                <!-- 订货间隔期 -->
                <el-descriptions border :column="4" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 4 - 7em - 22px)' }">
                  <template slot="title">
                    <div class="customTitle">订货间隔期</div>
                  </template>
                  <el-descriptions-item label="订货间隔期单位">{{ MaterialPlan.OrderIntervalTimeType && getOptionLabel(CheckLeadTimeTypeOptions, MaterialPlan.OrderIntervalTimeType) }}</el-descriptions-item>
                  <el-descriptions-item label="订货间隔期">{{ MaterialPlan.OrderIntervalTime }}</el-descriptions-item>
                </el-descriptions>
                <!-- 批量控制 -->
                <el-descriptions border :column="4" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 4 - 7em - 22px)' }">
                  <template slot="title">
                    <div class="customTitle">批量控制</div>
                  </template>
                  <el-descriptions-item label="最大订货量">{{ MaterialPlan.MaxPOQty }}{{ MaterialBase.BaseUnitId && MaterialBase.BaseUnitId.Name && getArrToStr(MaterialBase.BaseUnitId.Name) }}</el-descriptions-item>
                  <el-descriptions-item label="最小订货量">{{ MaterialPlan.MinPOQty }}{{ MaterialBase.BaseUnitId && MaterialBase.BaseUnitId.Name && getArrToStr(MaterialBase.BaseUnitId.Name) }}</el-descriptions-item>
                  <el-descriptions-item label="最小包装量">{{ MaterialPlan.IncreaseQty }}{{ MaterialBase.BaseUnitId && MaterialBase.BaseUnitId.Name && getArrToStr(MaterialBase.BaseUnitId.Name) }}</el-descriptions-item>
                  <el-descriptions-item label="固定/经济批量">{{ MaterialPlan.EOQ }}{{ MaterialBase.BaseUnitId && MaterialBase.BaseUnitId.Name && getArrToStr(MaterialBase.BaseUnitId.Name) }}</el-descriptions-item>
                  <el-descriptions-item label="变动提前期批量">{{ MaterialPlan.VarLeadTimeLotSize }}{{ MaterialBase.BaseUnitId && MaterialBase.BaseUnitId.Name && getArrToStr(MaterialBase.BaseUnitId.Name) }}</el-descriptions-item>
                </el-descriptions>
                <!-- 拆分控制 -->
                <el-descriptions border :column="4" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 4 - 7em - 22px)' }">
                  <template slot="title">
                    <div class="customTitle">拆分控制</div>
                  </template>
                  <el-descriptions-item label="拆分批量">{{ MaterialPlan.PlanBatchSplitQty }}{{ MaterialBase.BaseUnitId && MaterialBase.BaseUnitId.Name && getArrToStr(MaterialBase.BaseUnitId.Name) }}</el-descriptions-item>
                  <el-descriptions-item label="批量拆分间隔天数">{{ MaterialPlan.PlanIntervalsDays }}</el-descriptions-item>
                </el-descriptions>
                <!-- 时界控制 -->
                <el-descriptions border :column="4" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 4 - 7em - 22px)' }">
                  <template slot="title">
                    <div class="customTitle">时界控制</div>
                  </template>
                  <el-descriptions-item label="需求时界">{{ MaterialPlan.RequestTimeZone }}</el-descriptions-item>
                  <el-descriptions-item label="计划时界">{{ MaterialPlan.PlanTimeZone }}</el-descriptions-item>
                </el-descriptions>
                <!-- 其他 -->
                <el-descriptions border :column="4" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 4 - 7em - 22px)' }">
                  <template slot="title">
                    <div class="customTitle">其他</div>
                  </template>
                  <el-descriptions-item label="计划组">{{ MaterialPlan.PlanGroupId && MaterialPlan.PlanGroupId.Name && getArrToStr(MaterialPlan.PlanGroupId.Name) }}</el-descriptions-item>
                  <el-descriptions-item label="计划员">{{ MaterialPlan.PlanerID && MaterialPlan.PlanerID.Name && getArrToStr(MaterialPlan.PlanerID.Name) }}</el-descriptions-item>
                  <el-descriptions-item label="计划标识">{{ MaterialPlan.PlanIdent && MaterialPlan.PlanIdent.Name && getArrToStr(MaterialPlan.PlanIdent.Name) }}</el-descriptions-item>
                  <el-descriptions-item label="按批号匹配供需">
                    <el-checkbox :value="info.DSMatchByLot" disabled></el-checkbox>
                  </el-descriptions-item>
                  <el-descriptions-item label="安全库存">{{ MaterialPlan.PLANSAFESTOCKQTY }}{{ MaterialBase.BaseUnitId && MaterialBase.BaseUnitId.Name && getArrToStr(MaterialBase.BaseUnitId.Name) }}</el-descriptions-item>
                  <el-descriptions-item label="ATO预测冲销方案">{{ MaterialPlan.ATOSchemeId && MaterialPlan.ATOSchemeId.Name && getArrToStr(MaterialPlan.ATOSchemeId.Name) }}</el-descriptions-item>
                  <el-descriptions-item label="产品系列">{{ MaterialPlan.ProductLine && MaterialPlan.ProductLine.Name && getArrToStr(MaterialPlan.ProductLine.Name) }}</el-descriptions-item>
                  <el-descriptions-item label="冲销数量">{{ MaterialPlan.WriteOffQty }}{{ MaterialBase.BaseUnitId && MaterialBase.BaseUnitId.Name && getArrToStr(MaterialBase.BaseUnitId.Name) }}</el-descriptions-item>
                </el-descriptions>
                <!-- 预留 -->
                <el-descriptions border :column="4" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 4 - 7em - 22px)' }">
                  <template slot="title">
                    <div class="customTitle">预留</div>
                  </template>
                  <el-descriptions-item label="预留类型">{{ MaterialPlan.ReserveType && getOptionLabel(ReserveTypeOptions, MaterialPlan.ReserveType) }}</el-descriptions-item>
                  <el-descriptions-item label="可手工预留">
                    <el-checkbox :value="info.IsHandleReserve" disabled></el-checkbox>
                  </el-descriptions-item>
                </el-descriptions>
              </template>
              <!-- 计划控制 -->
              <template v-if="tabsActive == 'planControl'">
                <!-- 计划控制 -->
                <el-descriptions border :column="4" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 4 - 7em - 22px)' }">
                  <template slot="title">
                    <div class="customTitle">计划控制</div>
                  </template>
                  <el-descriptions-item label="允许提前天数">{{ MaterialPlan.CanLeadDays }}</el-descriptions-item>
                  <el-descriptions-item label="提前宽限期">{{ MaterialPlan.LeadExtendDay }}</el-descriptions-item>
                  <el-descriptions-item label="预计入库允许部分提前">
                    <el-checkbox :value="MaterialPlan.AllowPartAhead" disabled></el-checkbox>
                  </el-descriptions-item>
                  <el-descriptions-item label="允许延后天数">{{ MaterialPlan.CanDelayDays }}</el-descriptions-item>
                  <el-descriptions-item label="延后宽限期">{{ MaterialPlan.DelayExtendDay }}</el-descriptions-item>
                  <el-descriptions-item label="预计入库允许部分延后">
                    <el-checkbox :value="MaterialPlan.AllowPartDelay" disabled></el-checkbox>
                  </el-descriptions-item>
                </el-descriptions>
                <!-- 计划偏置 -->
                <el-descriptions border :column="4" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 4 - 7em - 22px)' }">
                  <template slot="title">
                    <div class="customTitle">计划偏置</div>
                  </template>
                  <el-descriptions-item label="时间单位">{{ MaterialPlan.PlanOffsetTimeType && getOptionLabel(TimeTypeOptions, MaterialPlan.PlanOffsetTimeType) }}</el-descriptions-item>
                  <el-descriptions-item label="偏置时间">{{ MaterialPlan.PlanOffsetTime }}</el-descriptions-item>
                  <el-descriptions-item label="供应来源">{{ MaterialPlan.SupplySourceId && MaterialPlan.SupplySourceId.Name && getArrToStr(MaterialPlan.SupplySourceId.Name) }}</el-descriptions-item>
                </el-descriptions>
                <!-- 优先级计算参数 -->
                <el-descriptions border :column="4" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 4 - 7em - 22px)' }">
                  <template slot="title">
                    <div class="customTitle">优先级计算参数</div>
                  </template>
                  <el-descriptions-item label="时间紧迫系数">{{ MaterialPlan.TimeFactorId && MaterialPlan.TimeFactorId.Name && getArrToStr(MaterialPlan.TimeFactorId.Name) }}</el-descriptions-item>
                  <el-descriptions-item label="数量负荷系数">{{ MaterialPlan.QtyFactorId && MaterialPlan.QtyFactorId.Name && getArrToStr(MaterialPlan.QtyFactorId.Name) }}</el-descriptions-item>
                </el-descriptions>
                <!-- 订单进度跟踪 -->
                <el-descriptions border :column="4" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 4 - 7em - 22px)' }">
                  <template slot="title">
                    <div class="customTitle">订单进度跟踪</div>
                  </template>
                  <el-descriptions-item label="订单进度分组">{{ MaterialPlan.ProScheTrackId && MaterialPlan.ProScheTrackId.Name && getArrToStr(MaterialPlan.ProScheTrackId.Name) }}</el-descriptions-item>
                </el-descriptions>
                <!-- 合并控制 -->
                <el-descriptions border :column="4" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 4 - 7em - 22px)' }">
                  <template slot="title">
                    <div class="customTitle">合并控制</div>
                  </template>
                  <el-descriptions-item label="MRP计算是否合并需求">
                    <el-checkbox :value="MaterialPlan.IsMrpComReq" disabled></el-checkbox>
                  </el-descriptions-item>
                  <el-descriptions-item label="MRP计算是否按单合并">
                    <el-checkbox :value="MaterialPlan.IsMrpComBill" disabled></el-checkbox>
                  </el-descriptions-item>
                </el-descriptions>
              </template>
              <!-- 物料维度 -->
              <template v-if="tabsActive == 'materialDimensions'">
                <!-- 库存属性控制 -->
                <div class="customTitle">库存属性控制</div>
                <el-table :data="MaterialInvPty" border class="custom-table">
                  <el-table-column prop="inventoryAttribute" label="库存属性" align="center" show-overflow-tooltip>
                    <template slot-scope="scope">{{ scope.row.InvPtyId && scope.row.InvPtyId.Name && getArrToStr(scope.row.InvPtyId.Name) }}</template>
                  </el-table-column>
                  <el-table-column prop="IsEnable" label="启用" align="center" show-overflow-tooltip>
                    <template slot-scope="scope">
                      <el-checkbox v-model="scope.row.IsEnable" disabled></el-checkbox>
                    </template>
                  </el-table-column>
                  <el-table-column prop="IsAffectPrice" label="影响价格" align="center" show-overflow-tooltip>
                    <template slot-scope="scope">
                      <el-checkbox v-model="scope.row.IsAffectPrice" disabled></el-checkbox>
                    </template>
                  </el-table-column>
                  <el-table-column prop="IsAffectPlan" label="影响计划" align="center" show-overflow-tooltip>
                    <template slot-scope="scope">
                      <el-checkbox v-model="scope.row.IsAffectPlan" disabled></el-checkbox>
                    </template>
                  </el-table-column>
                  <el-table-column prop="IsAffectCost" label="影响出库成本" align="center" show-overflow-tooltip>
                    <template slot-scope="scope">
                      <el-checkbox v-model="scope.row.IsAffectCost" disabled></el-checkbox>
                    </template>
                  </el-table-column>
                </el-table>
              </template>
              <!-- 图片 -->
              <template v-if="tabsActive == 'images'"></template>
              <!-- 其他 -->
              <template v-if="tabsActive == 'others'">
                <el-descriptions border :column="4" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 4 - 7em - 22px)' }">
                  <el-descriptions-item label="创建人">{{ info.CreatorId && info.CreatorId.Name }}</el-descriptions-item>
                  <el-descriptions-item label="创建日期">{{ parseTime(info.CreateDate, '{y}-{m}-{d}') }}</el-descriptions-item>
                  <el-descriptions-item label="修改人">{{ info.ModifierId && info.ModifierId.Name }}</el-descriptions-item>
                  <el-descriptions-item label="修改日期">{{ parseTime(info.ModifyDate, '{y}-{m}-{d}') }}</el-descriptions-item>
                  <el-descriptions-item label="审核人">{{ info.ApproverId && info.ApproverId.Name }}</el-descriptions-item>
                  <el-descriptions-item label="审核日期">{{ parseTime(info.ApproveDate, '{y}-{m}-{d}') }}</el-descriptions-item>
                  <el-descriptions-item label="禁用人">{{ info.ForbidderId && info.ForbidderId.Name }}</el-descriptions-item>
                  <el-descriptions-item label="禁用日期">{{ parseTime(info.ForbidDate, '{y}-{m}-{d}') }}</el-descriptions-item>
                  <el-descriptions-item label="物料来源">{{ info.MaterialSRC && getOptionLabel(MaterialSRCTypeOptions, info.MaterialSRC) }}</el-descriptions-item>
                </el-descriptions>
              </template>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </el-dialog>
    <!-- 单位换算 -->
    <el-dialog v-dialogDragBox title="单位换算列表" :visible.sync="showMaterUnit" width="90%" class="custom-dialog">
      <materialUnitList ref="materialUnitList" is-popup />
    </el-dialog>
  </div>
</template>
<script>
import { getMaterialDetail } from '@/api/kingdee'
import { auditMaterial, cancelMaterial, deleteMaterial, submitMaterial, unauditMaterial } from '@/api/kingdee/material'

export default {
  components: { materialUnitList: () => import('@/views/kingdee/material/unit/index') },
  data() {
    return {
      open: false,
      info: {},
      MaterialBase: {}, // 基本信息
      MaterialStock: {}, // 库存
      MaterialSale: {}, // 销售
      MaterialPurchase: {}, // 采购
      MaterialQM: {}, // 质量
      MaterialProduce: {}, // 生产
      MaterialPlan: {}, // 计划
      MaterialInvPty: [], // 库存属性控制
      MaterialSubcon: [], // 委外
      // tab标签
      tabsActive: 'base',
      tabsOptions: [
        { label: '基础', name: 'base' },
        { label: '库存', name: 'stock' },
        { label: '销售', name: 'sales' },
        { label: '采购', name: 'purchase' },
        { label: '质量', name: 'quality' },
        { label: '生产', name: 'production' },
        { label: '计划属性', name: 'planAttributes' },
        { label: '计划控制', name: 'planControl' },
        { label: '物料维度', name: 'materialDimensions' },
        // { label: '图片', name: 'images' },
        { label: '其他', name: 'others' }
      ],
      startUsing: ['min', 'max'],
      // 物料属性
      ErpClsIDOptions: [
        { value: '1', label: '外购' },
        { value: '2', label: '自制' },
        { value: '3', label: '委外' },
        { value: '9', label: '配置' },
        { value: '10', label: '资产' },
        { value: '4', label: '特征' },
        { value: '11', label: '费用' },
        { value: '5', label: '虚拟' },
        { value: '6', label: '服务' },
        { value: '7', label: '一次性' },
        { value: '12', label: '模型' },
        { value: '13', label: '产品系列' }
      ],
      // 配置生产
      ConfigTypeOptions: [
        { value: 'A', label: '自制生产' },
        { value: 'B', label: '委外加工' }
      ],
      // 特征件子项
      FeatureItemOptions: [
        { value: '1', label: '单选' },
        { value: '2', label: '多选' }
      ],
      // 套件
      SuiteOptions: [
        { value: '0', label: '否' },
        { value: '1', label: '是' }
      ],
      // 数据状态
      DocumentStatusOptions: [
        { value: 'A', label: '创建' },
        { value: 'B', label: '审核中' },
        { value: 'C', label: '已审核' },
        { value: 'D', label: '重新审核' }
      ],
      // 禁用状态
      ForbidStatusOptions: [
        { value: 'A', label: '否' },
        { value: 'B', label: '是' }
      ],
      // 已使用
      RefStatusOptions: [
        { value: '0', label: '否' },
        { value: '1', label: '是' }
      ],
      // 换算方向
      UnitConvertDirOptions: [
        { value: '1', label: '库存单位-->辅助单位' },
        { value: '2', label: '辅助单位-->库存单位' }
      ],
      // 盘点周期
      CountCycleOptions: [
        { value: '1', label: '周' },
        { value: '2', label: '月' }
      ],
      // 保质期单位
      ExpUnitOptions: [
        { value: 'D', label: '日' },
        { value: 'M', label: '月' },
        { value: 'Y', label: '年' }
      ],
      // 超发控制单位
      OutLmtUnitOptions: [
        { value: 'SAL', label: '销售单位' },
        { value: 'STK', label: '库存单位' }
      ],
      // 税收优惠政策类型
      TaxDiscountsTypeOptions: [],
      // 保修期单位(同保质期单位)
      // 配额方式
      QuotaTypeOptions: [
        { value: '1', label: '顺序优先' },
        { value: '2', label: '固定供应商' },
        { value: '3', label: '固定比例' },
        { value: '4', label: '价格优先' }
      ],
      // 首检控制方式
      FirstQCControlTypeOptions: [
        { value: '0', label: '无' },
        { value: '1', label: '严格控制' },
        { value: '2', label: '非严格控制' }
      ],
      // 倒冲数量
      BackFlushTypeOptions: [
        { value: '1', label: '主业务单位数量' },
        { value: '2', label: '辅库存单位数量' }
      ],
      // 发料方式
      IssueTypeOptions: [
        { value: '1', label: '直接领料' },
        { value: '2', label: '直接倒冲' },
        { value: '3', label: '调拨领料' },
        { value: '4', label: '调拨倒冲' },
        { value: '7', label: '不发料' }
      ],
      // 倒冲时机
      BackFlushTimeOptions: [
        { value: '2', label: '汇报倒冲' },
        { value: '3', label: '入库倒冲' }
      ],
      // 超发控制方式
      OverControlModeOptions: [
        { value: '1', label: '最小发料批量' },
        { value: '2', label: '允许超发' },
        { value: '3', label: '不允许超发' },
        { value: '4', label: '按损耗率超发' }
      ],
      // 标准工时单位
      StandHourUnitIdOptions: [
        { value: '1', label: '时' },
        { value: '60', label: '分' },
        { value: '3600', label: '秒' }
      ],
      // 计划策略
      PlanningStrategyOptions: [
        { value: '0', label: 'MPS' },
        { value: '1', label: 'MRP' },
        { value: '2', label: '无' }
      ],
      // 订货策略
      OrderPolicyOptions: [
        { value: '0', label: 'LFL(批对批)' },
        { value: '1', label: 'POQ(期间订货)' },
        { value: '2', label: 'FOQ(固定批量)' },
        { value: '3', label: 'ROP(再订货点)' }
      ],
      // 提前期单位
      TimeTypeOptions: [
        { value: '1', label: '天' },
        { value: '24', label: '时' },
        { value: '1440', label: '分' },
        { value: '86400', label: '秒' }
      ],
      // 变动提前期(同提前期单位)
      // 检验提前期
      CheckLeadTimeTypeOptions: [
        { value: '1', label: '天' },
        { value: '2', label: '周' },
        { value: '3', label: '月' }
      ],
      // 订货间隔期单位(检验提前期)
      // 预留类型
      ReserveTypeOptions: [
        { value: '1', label: '弱预留' },
        { value: '3', label: '强预留' }
      ],
      // 时间单位(同提前期单位)
      // 物料来源
      MaterialSRCTypeOptions: [
        { value: 'A', label: 'PLM' },
        { value: 'B', label: 'ERP' }
      ],
      // 显示单位换算
      showMaterUnit: false
    }
  },
  methods: {
    // 切换tab标签
    handleTabsClick(tab) {
      this.tabsActive = tab.name
    },
    // 查看详情
    getInfo(number) {
      if (!number) {
        this.$message.error('参数错误，请重新选择')
        return
      }
      this.tabsActive = 'base'
      getMaterialDetail({ number }).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          const { result } = data
          this.open = !!result?.result
          this.info = result?.result || {}
          this.MaterialBase = result?.result?.MaterialBase[0] || {}
          this.MaterialStock = result?.result?.MaterialStock[0] || {}
          this.MaterialSale = result?.result?.MaterialSale[0] || {}
          this.MaterialPurchase = result?.result?.MaterialPurchase[0] || {}
          this.MaterialQM = result?.result?.MaterialQM[0] || {}
          this.MaterialProduce = result?.result?.MaterialProduce[0] || {}
          this.MaterialPlan = result?.result?.MaterialPlan[0] || {}
          this.MaterialInvPty = result?.result?.MaterialInvPty || []
          this.MaterialSubcon = result?.result?.MaterialSubcon[0] || {}
        } else this.$message.error(msg)
      })
    },
    // 数组转字符串
    getArrToStr(arr) {
      const NewArr = arr.filter(item => item.Value != ' ') || []
      return NewArr.map(item => item.Value).join('，')
    },
    // option回显label
    getOptionLabel(options, value) {
      return options.find(item => item.value === value)?.label || ''
    },
    // 金蝶操作
    // prettier-ignore
    handleKingdeeDo(type) {
      const number = this.info.Number
      if (!number) return
      switch (type) {
        case 'submit':
          // 提交
          this.$modal.confirm('确认要提交该物料吗？').then(() => {
            submitMaterial({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('提交成功')
                this.$set(this.info, 'DocumentStatus', 'B')
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'audit':
          // 审核
          this.$modal.confirm('确认要审核该物料吗？').then(() => {
            auditMaterial({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('审核成功')
                this.$set(this.info, 'DocumentStatus', 'C')
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'revoke':
          // 撤销
          this.$modal.confirm('确认要撤销该物料吗？').then(() => {
            cancelMaterial({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('撤销成功')
                this.$set(this.info, 'DocumentStatus', 'D')
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'unAudit':
          // 反审核
          this.$modal.confirm('确认要反审核该物料吗？').then(() => {
            unauditMaterial({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('反审核成功')
                this.$set(this.info, 'DocumentStatus', 'D')
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'delete':
          // 删除
          this.$modal.confirm('确认要删除该物料吗？').then(() => {
            deleteMaterial({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('删除成功')
                this.handleClose(true)
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
      }
    },
    beforeClose() {
      this.handleClose()
      this.$emit('update', { billNo: this.info.Number, newStatus: this.info.DocumentStatus })
    },
    // 关闭
    handleClose(flag = false) {
      this.open = false
      this.$emit('callBack', flag)
    },
    handleUnitConvert() {
      this.showMaterUnit = true
      const data = {
        materialId: this.info?.Number || '',
        materialName: (this.info?.Name && this.info?.Name?.[0]?.Value) || '',
        materialUnit: (this.MaterialBase?.BaseUnitId && this.MaterialBase?.BaseUnitId?.Number) || ''
      }
      this.$nextTick(() => {
        this.$refs.materialUnitList.handleReceive(data)
      })
    }
  }
}
</script>
<style scoped lang="scss">
@import '@/assets/styles/custom-new.scss';
.material {
  padding: 0 20px;
  .material-basics {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    .material-basics-item {
      display: flex;
      align-items: center;
      line-height: 40px;
      font-size: 14px;
      padding-right: 20px;
      b {
        font-weight: normal;
        color: $font;
        &:after {
          content: '：';
        }
      }
      span {
        color: $info;
      }
    }
  }
}
::v-deep {
  .el-descriptions__header {
    margin-bottom: 0;
  }
  // .el-dialog__body {
  //   padding-top: 0 !important;
  // }
}
.customTitle {
  font-size: 16px;
  font-weight: normal;
  line-height: 40px;
  color: $font;
}
</style>
