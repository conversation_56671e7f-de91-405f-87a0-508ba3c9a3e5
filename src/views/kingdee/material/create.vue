<template>
  <div>
    <el-dialog v-dialogDragBox title="新增物料" :visible.sync="open" width="1150px" class="custom-dialog" append-to-body :before-close="beforeClose">
      <div style="padding: 0 20px">
        <el-form ref="form" :model="form" :rules="rules" label-width="7em">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="创建组织" prop="fcreateOrgId">
                <el-select v-model="form.fcreateOrgId" placeholder="请选择创建组织" filterable default-first-option style="width: 100%" disabled @change="handleChangeCreateOrg">
                  <el-option v-for="item in ApplicationOrgId" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="使用组织" prop="fuseOrgId">
                <el-select v-model="form.fuseOrgId" placeholder="请选择使用组织" filterable default-first-option style="width: 100%" disabled>
                  <el-option v-for="item in ApplicationOrgId" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="物料名称" prop="name">
                <el-input v-model="form.name" placeholder="请输入物料名称" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="物料编码" prop="number">
                <el-input v-model="form.number" placeholder="请输入物料编码" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="规格型号" prop="fspecification">
                <el-input v-model="form.fspecification" placeholder="请输入规格型号" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="物料分组" prop="fmaterialGroup">
                <el-cascader v-model="form.fmaterialGroup" :options="materialGroupOptions" :props="{ emitPath: false, value: 'number', label: 'name' }" clearable filterable placeholder="请选择物料分组" style="width: 100%" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="物料属性" prop="ferpClsID">
                <el-select v-model="form.ferpClsID" placeholder="请选择物料属性" filterable default-first-option style="width: 100%">
                  <el-option label="外购" value="1" />
                  <el-option label="自制" value="2" />
                  <el-option label="委外" value="3" />
                  <el-option label="资产" value="10" />
                  <el-option label="服务" value="6" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="特征件子项" prop="ffeatureItem">
                <el-select v-model="form.ffeatureItem" placeholder="请选择特征件子项" filterable default-first-option style="width: 100%">
                  <el-option label="单选" value="1" />
                  <el-option label="多选" value="2" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="套件" prop="fsuite">
                <el-select v-model="form.fsuite" placeholder="请选择是否套件" filterable default-first-option style="width: 100%">
                  <el-option label="是" value="1" />
                  <el-option label="否" value="0" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="基本单位" prop="fbaseUnitId">
                <el-select v-model="form.fbaseUnitId" filterable default-first-option placeholder="请选择基本单位" style="width: 100%">
                  <el-option v-for="(item, index) in UnitList" :key="index" :label="item.FName" :value="item.FNumber"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="存货类别" prop="fcategoryID">
                <el-select v-model="form.fcategoryID" placeholder="请选择存货类别" filterable default-first-option style="width: 100%">
                  <el-option label="原材料" value="CHLB01_SYS" />
                  <el-option label="辅料" value="CHLB02_SYS" />
                  <el-option label="自制半成品" value="CHLB03_SYS" />
                  <el-option label="委外半成品" value="CHLB04_SYS" />
                  <el-option label="产成品" value="CHLB05_SYS" />
                  <el-option label="服务" value="CHLB06_SYS" />
                  <el-option label="资产" value="CHLB07_SYS" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="辅助单位" prop="fauxunitid">
                <el-select v-model="form.fauxunitid" filterable default-first-option placeholder="请选择辅助单位" style="width: 100%">
                  <el-option v-for="(item, index) in UnitList" :key="index" :label="item.FName" :value="item.FNumber"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-table :data="form.invPtyEntityList" style="width: 100%">
                <el-table-column prop="fInvPtyId" label="库存属性" align="center" :formatter="formatInvPtyId"></el-table-column>
                <el-table-column prop="fIsEnable" label="启用" align="center">
                  <template slot-scope="{ row }">
                    <el-checkbox v-model="row.fIsEnable" :disabled="row.fInvPtyId === '03' || row.fInvPtyId === '06'" />
                  </template>
                </el-table-column>
                <el-table-column prop="fIsAffectPrice" label="影响价格" align="center">
                  <template slot-scope="{ row }">
                    <el-checkbox v-model="row.fIsAffectPrice" :disabled="row.fInvPtyId === '03' || row.fInvPtyId === '06'" />
                  </template>
                </el-table-column>
                <el-table-column prop="fIsAffectPlan" label="影响计划" align="center">
                  <template slot-scope="{ row }">
                    <el-checkbox v-model="row.fIsAffectPlan" :disabled="row.fInvPtyId === '03' || row.fInvPtyId === '06'" />
                  </template>
                </el-table-column>
                <el-table-column prop="fIsAffectCost" label="影响出库成本" align="center">
                  <template slot-scope="{ row }">
                    <el-checkbox v-model="row.fIsAffectCost" :disabled="row.fInvPtyId === '03' || row.fInvPtyId === '06'" />
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn" @click="handleCancel()">取 消</el-button>
        <el-button class="custom-dialog-btn primary" @click="handleSubmit">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { kingdee } from '@/minix'
import { queryMaterialGroupList, saveMaterial } from '@/api/kingdee/material'
import { getMaterialDetail } from '@/api/kingdee'

export default {
  mixins: [kingdee],
  data() {
    return {
      open: false,
      form: {},
      newForm: {},
      rules: {
        fcreateOrgId: [{ required: true, message: '请选择创建组织', trigger: 'change' }],
        fuseOrgId: [{ required: true, message: '请选择使用组织', trigger: 'change' }],
        name: [{ required: true, message: '请输入物料名称', trigger: 'blur' }],
        number: [{ required: true, message: '请输入物料编码', trigger: 'blur' }],
        fspecification: [{ required: true, message: '请输入规格型号', trigger: 'blur' }],
        fmaterialGroup: [{ required: true, message: '请选择物料分组', trigger: 'change' }],
        ferpClsID: [{ required: true, message: '请选择物料属性', trigger: 'change' }],
        ffeatureItem: [{ required: true, message: '请选择特征件子项', trigger: 'change' }],
        fsuite: [{ required: true, message: '请选择是否套件', trigger: 'change' }],
        fbaseUnitId: [{ required: true, message: '请选择基本单位', trigger: 'change' }],
        fcategoryID: [{ required: true, message: '请选择存货类别', trigger: 'change' }]
      },
      materialGroupOptions: [],
      number: '',
      fMaterialId: '',
      // 物料属性
      ErpClsIDOptions: [
        { value: '1', label: '外购' },
        { value: '2', label: '自制' },
        { value: '3', label: '委外' },
        { value: '9', label: '配置' },
        { value: '10', label: '资产' },
        { value: '4', label: '特征' },
        { value: '11', label: '费用' },
        { value: '5', label: '虚拟' },
        { value: '6', label: '服务' },
        { value: '7', label: '一次性' },
        { value: '12', label: '模型' },
        { value: '13', label: '产品系列' }
      ]
    }
  },
  created() {
    this.getMaterialGroupList()
  },
  methods: {
    // 改变创建组织
    handleChangeCreateOrg(val) {
      this.form.fuseOrgId = val
    },
    // 获取物料分组
    getMaterialGroupList() {
      queryMaterialGroupList().then(res => {
        this.materialGroupOptions = res.data
      })
    },
    // 表单重置
    reset() {
      this.form = {
        fbaseUnitId: 'Pcs', // 基本单位
        fauxunitid: '', // 辅助单位
        fcategoryID: 'CHLB01_SYS', // 存货类别 CHLB01_SYS-原材料 CHLB02_SYS-辅料 CHLB03_SYS-自制半成品 CHLB04_SYS-委外半成品 CHLB05_SYS-产成品 CHLB06_SYS-服务 CHLB07_SYS-资产
        fcreateOrgId: this.ApplicationOrgId[0].value, // 创建组织
        ferpClsID: '2', //物料属性  1-外购 2-自制 3-委外  资产-10 服务-6
        ffeatureItem: '1', //特征件子项 default 1-单选 2-多选
        fmaterialGroup: '', //物料分组
        fspecification: '', //规格型号
        fsuite: '0', //套件 default 0-否 1-是
        fuseOrgId: this.ApplicationOrgId[0].value, //使用组织
        name: '', // 物料名称
        number: '', // 物料编码
        invPtyEntityList: [
          { fInvPtyId: '01', fIsEnable: true, fIsAffectPrice: false, fIsAffectPlan: false, fIsAffectCost: true },
          { fInvPtyId: '02', fIsEnable: true, fIsAffectPrice: false, fIsAffectPlan: false, fIsAffectCost: true },
          { fInvPtyId: '03', fIsEnable: false, fIsAffectPrice: false, fIsAffectPlan: false, fIsAffectCost: false },
          { fInvPtyId: '04', fIsEnable: true, fIsAffectPrice: false, fIsAffectPlan: false, fIsAffectCost: true },
          { fInvPtyId: '06', fIsEnable: false, fIsAffectPrice: false, fIsAffectPlan: false, fIsAffectCost: false }
        ] // 库存属性控制
      }
      this.resetForm('form')
    },
    formatInvPtyId(row) {
      const arr = [
        { label: '仓库', value: '01' },
        { label: '仓位', value: '02' },
        { label: 'bom版本号', value: '03' },
        { label: '批号', value: '04' },
        { label: '计划跟踪号', value: '06' }
      ]
      return arr.find(item => item.value === row.fInvPtyId)?.label
    },
    // 数组转字符串
    getArrToStr(arr) {
      if (!arr) return ''
      const NewArr = arr.filter(item => item.Value != ' ') || []
      return NewArr.map(item => item.Value).join('，')
    },
    // 初始化
    init(number = undefined) {
      this.reset()
      if (number) {
        getMaterialDetail({ number }).then(res => {
          const { code, msg, data } = res
          if (code === 200) {
            this.number = number
            const { result } = data
            const { result: info } = result
            this.fMaterialId = info?.Id
            const MaterialBase = info?.MaterialBase[0] || {}
            this.form.fcreateOrgId = info?.CreateOrgId?.Number
            this.form.fuseOrgId = info?.UseOrgId?.Number
            this.form.name = this.getArrToStr(info?.Name)
            this.form.number = info?.Number
            this.form.fspecification = this.getArrToStr(info?.Specification) // 规格型号
            this.form.fmaterialGroup = info?.MaterialGroup?.Number // 物料分组
            this.form.ferpClsID = MaterialBase?.ErpClsID // 物料属性
            this.form.ffeatureItem = MaterialBase?.FeatureItem // 特征件子项
            this.form.fsuite = MaterialBase?.Suite // 套件
            this.form.fbaseUnitId = MaterialBase?.BaseUnitId?.Number // 基本单位
            this.form.fcategoryID = MaterialBase?.CategoryID?.Number // 存货类别
            const MaterialInvPty = info?.MaterialInvPty || []
            const invPtyEntityList = MaterialInvPty.map(item => ({
              fEntryID: item?.Id,
              fInvPtyId: item?.InvPtyId?.Number,
              fIsEnable: item?.IsEnable,
              fIsAffectPrice: item?.IsAffectPrice,
              fIsAffectPlan: item?.IsAffectPlan,
              fIsAffectCost: item?.IsAffectCost
            }))
            this.form.invPtyEntityList = invPtyEntityList
            this.newForm = JSON.parse(JSON.stringify(this.form))
            this.open = !!result?.result
          } else this.$message.error(msg)
        })
      } else {
        this.form.fcreateOrgId = this.ApplicationOrgId[0].value
        this.form.fuseOrgId = this.ApplicationOrgId[0].value
        this.open = true
      }
    },
    // 添加自由客产品时同时添加金蝶物料
    handleCreate(material = {}) {
      this.reset()
      const { materialCode, productName, specs, model, unit } = material
      const fbaseUnitId = this.UnitList.find(item => item.FName == unit)?.FNumber || 'Pcs'
      this.form.fbaseUnitId = fbaseUnitId
      this.form.fspecification = specs === model ? specs : specs + ' ' + model
      this.form.name = productName
      this.form.number = materialCode
      this.form.fcreateOrgId = this.ApplicationOrgId[0].value
      this.form.fuseOrgId = this.ApplicationOrgId[0].value
      this.open = true
    },
    // 改变销售单位同时改变销售计价单位
    changeSaleUnit(value) {
      this.form.fsalePriceUnitId = value
    },
    // 改变销售计价单位同时改变销售单位
    changeSalePriceUnit(value) {
      this.form.fsaleUnitId = value
    },
    // 改变采购单位同时改变采购计价单位
    changePurchaseUnit(value) {
      this.form.fpurchasePriceUnitId = value
    },
    // 改变采购计价单位同时改变采购单位
    changePurchasePriceUnit(value) {
      this.form.fpurchaseUnitId = value
    },
    beforeClose() {
      this.handleCancel()
    },
    // 取消
    handleCancel(flag = false) {
      this.open = false
      this.$emit('callBack', flag)
    },
    // 提交
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          if (this.number) {
            const needUpDateFields = []
            const fieldMapping = {
              name: 'FName',
              number: 'FNumber',
              fspecification: 'FSpecification',
              fmaterialGroup: 'FMaterialGroup',
              ferpClsID: 'SubHeadEntity.FErpClsID',
              ffeatureItem: 'SubHeadEntity.FFeatureItem',
              fsuite: 'SubHeadEntity.FSuite',
              fbaseUnitId: 'SubHeadEntity.FBaseUnitId',
              fcategoryID: 'SubHeadEntity.FCategoryID',
              fIsEnable: 'FEntityInvPty.FIsEnable',
              fIsAffectPrice: 'FEntityInvPty.FIsAffectPrice',
              fIsAffectPlan: 'FEntityInvPty.FIsAffectPlan',
              fIsAffectCost: 'FEntityInvPty.FIsAffectCost'
            }
            // 检查主表单字段变更
            for (const key in this.form) {
              if (JSON.stringify(this.form[key]) !== JSON.stringify(this.newForm[key])) {
                needUpDateFields.push(key)
              }
            }
            // 检查invPtyEntityList的变更
            if (this.form.invPtyEntityList?.length === this.newForm.invPtyEntityList?.length) {
              const propertiesToCheck = ['fIsEnable', 'fIsAffectPrice', 'fIsAffectPlan', 'fIsAffectCost']
              this.form.invPtyEntityList.forEach((item, index) => {
                const newItem = this.newForm.invPtyEntityList[index]
                propertiesToCheck.forEach(prop => {
                  if (item[prop] !== newItem[prop]) {
                    needUpDateFields.push(prop)
                  }
                })
              })
            }
            // 直接使用映射转换字段名称
            let lastNeedUpDateFields = [...new Set(needUpDateFields)].map(field => fieldMapping[field]).filter(Boolean)
            // 处理包含点号的字段,只保留最后一段并添加前缀
            const processedFields = lastNeedUpDateFields.reduce((acc, field) => {
              if (field.includes('.')) {
                const parts = field.split('.')
                acc.push(parts[0]) // 添加前缀
                acc.push(parts[parts.length - 1]) // 添加最后一段
              } else {
                acc.push(field)
              }
              return acc
            }, [])
            // 去重
            lastNeedUpDateFields = [...new Set(processedFields)]

            const data = { ...this.form, fMaterialId: this.fMaterialId, needUpDateFields: lastNeedUpDateFields }
            saveMaterial(data).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('修改成功')
                this.handleCancel(true)
              } else this.$message.error(msg)
            })
          } else {
            saveMaterial(this.form).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('新增成功')
                this.handleCancel(true)
                this.$emit('callCreateSuccess', this.form.number)
              } else this.$message.error(msg)
            })
          }
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
@import '@/assets/styles/custom-new.scss';
</style>
