<template>
  <div class="newBox bgcf9 vh-85">
    <!-- 搜索 -->
    <div class="custom-search flex" style="padding-top: 18px" :style="{ justifyContent: showSearch ? 'space-between' : 'flex-end' }">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" @submit.native.prevent>
        <el-form-item label="库存组织" prop="stockOrg">
          <el-select v-model="queryParams.stockOrg" placeholder="请选择库存组织" @change="handleQuery">
            <el-option v-for="item in ApplicationOrgId" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="物料名称" prop="name">
          <el-input v-model="queryParams.name" placeholder="请输入物料名称" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="物料编码" prop="number">
          <el-input v-model="queryParams.number" placeholder="请输入物料编码" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="仓库名称" prop="stockName">
          <el-input v-model="queryParams.stockName" placeholder="请输入仓库名称" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="small" @click="handleResetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns" isSetitem @updateColumns="updateColumns"></right-toolbar>
    </div>
    <!-- 列表 -->
    <div style="padding: 15px 20px">
      <el-table ref="allTable" stripe :data="list" style="width: 100%" class="custom-table" :default-sort="{ prop: 'Qty', order: 'descending' }" v-if="list.length > 0" show-summary :summary-method="customSummary">
        <el-table-column align="center" type="index" label="序号" v-if="columns[0].visible"></el-table-column>
        <!-- 物料编码 -->
        <el-table-column align="center" prop="MaterialNumber" label="物料编码" show-overflow-tooltip v-if="columns[1].visible">
          <template slot-scope="{ row }">
            <span class="table-link" @click="openMaterialDetail(row.MaterialNumber)">{{ row.MaterialNumber }}</span>
          </template>
        </el-table-column>
        <!-- 物料名称 -->
        <el-table-column align="center" prop="MaterialName" label="物料名称" show-overflow-tooltip v-if="columns[2].visible">
          <template slot-scope="{ row }">
            <span class="table-link" @click="openMaterialDetail(row.MaterialNumber)">{{ row.MaterialName }}</span>
          </template>
        </el-table-column>
        <!-- 规格型号 -->
        <el-table-column align="center" prop="MaterialModel" label="规格型号" show-overflow-tooltip v-if="columns[3].visible"></el-table-column>
        <!-- 仓库名称 -->
        <el-table-column align="center" prop="StockName" label="仓库名称" show-overflow-tooltip v-if="columns[4].visible"></el-table-column>
        <!-- 批号 -->
        <el-table-column align="center" prop="FLot" label="批号" show-overflow-tooltip v-if="columns[5].visible"></el-table-column>
        <!-- 库存单位 -->
        <el-table-column align="center" prop="UnitName" label="库存单位" show-overflow-tooltip v-if="columns[6].visible"></el-table-column>
        <!-- 库存量 -->
        <el-table-column align="center" prop="Qty" label="库存量" sortable show-overflow-tooltip v-if="columns[7].visible"></el-table-column>
        <!-- 库存组织 -->
        <el-table-column align="center" prop="StockOrgName" label="库存组织" show-overflow-tooltip v-if="columns[8].visible"></el-table-column>
      </el-table>
      <el-empty v-else description="暂无数据" />
      <!-- 分页 -->
      <div class="custom-pagination">
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.limit" @pagination="getList" />
      </div>
    </div>
    <!-- 物料详情 -->
    <material-detail ref="materialDetail" v-if="showMaterialDetail" />
  </div>
</template>

<script>
import { getMaterialStockList } from '@/api/kingdee'
import { kingdee } from '@/minix'
import MaterialDetail from '@/views/kingdee/material/detail'

export default {
  name: 'Inventory',
  mixins: [kingdee],
  components: { MaterialDetail },
  data() {
    return {
      // 搜索
      queryParams: {
        pageNum: 1, // 当前页码
        startRow: 0, // 开始行索引
        limit: 10, // 每页条数
        stockOrg: '102', // 库存组织
        name: undefined, // 物料名称
        number: undefined, // 物料编码
        stockNum: undefined, // 仓库编码
        stockName: undefined // 仓库名称
      },
      total: 0,
      // 列表
      list: [],
      // 物料详情
      showMaterialDetail: false,
      // 列表显隐
      showSearch: true,
      columns: [
        { key: 0, label: `序号`, visible: true },
        { key: 1, label: `物料编码`, visible: true },
        { key: 2, label: `物料名称`, visible: true },
        { key: 3, label: `规格型号`, visible: true },
        { key: 4, label: `仓库名称`, visible: true },
        { key: 5, label: `批号`, visible: true },
        { key: 6, label: `库存单位`, visible: true },
        { key: 7, label: `库存量`, visible: true },
        { key: 8, label: `库存组织`, visible: true }
      ]
    }
  },
  created() {
    const columns = localStorage.getItem(this.userId + '.timeInventoryColumns')
    if (columns) this.columns = JSON.parse(columns)
    const limit = localStorage.getItem(this.userId + '.inventoryQueryParams.limit')
    if (limit) this.queryParams.limit = Number(limit) || 10
    const stockOrg = localStorage.getItem(this.userId + '.inventoryQueryParams.stockOrg')
    if (stockOrg) this.queryParams.stockOrg = stockOrg
    // 获取列表
    this.getList()
  },
  computed: {
    // 获取当前用户ID
    userId() {
      return this.$store.getters.info.userId
    }
  },
  watch: {
    'queryParams.limit': {
      handler(newVal) {
        localStorage.setItem(this.userId + '.inventoryQueryParams.limit', newVal)
      },
      deep: true
    }
  },
  methods: {
    // 改变显隐列缓存
    updateColumns(data = []) {
      localStorage.setItem(this.userId + '.timeInventoryColumns', JSON.stringify(data))
    },
    // 列表
    // prettier-ignore
    getList() {
      const loading = this.$loading({
        lock: true,
        text: '加载中',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      this.queryParams.startRow = Math.max(0, (this.queryParams.pageNum - 1) * this.queryParams.limit)
      const query = { ...this.queryParams }
      delete query.pageNum
      getMaterialStockList(query).then(res => {
        const { code, data } = res
        if (code === 200) {
          const { data: list, total } = data
          this.list = list
          this.total = total
        } else this.$message.error(res.msg)
      }).finally(() => {
        this.$nextTick(() => {
          if (this.$refs.allTable) {
            const table = this.$refs.allTable.$el.querySelector('.el-table__body-wrapper');
            if (table) table.scrollTop = 0
          }
        })
        loading.close()
      })
    },
    customSummary(param) {
      const { columns, data } = param
      const result = columns.map((column, index) => {
        if (index === 0) {
          return '合计'
        }
        if (column.property === 'Qty') {
          return `${this.calculateTotal(data, column.property)}`
        }
        return ''
      })
      return result
    },
    // 计算合计
    calculateTotal(data, key) {
      return data.reduce((total, item) => {
        if (key === 'Qty') {
          return parseFloat((total + (item.Qty || 0)).toFixed(5))
        }
        return total
      }, 0)
    },
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      if (this.queryParams.stockOrg !== '103') {
        localStorage.setItem(this.userId + '.inventoryQueryParams.stockOrg', this.queryParams.stockOrg)
      }
      this.getList()
    },
    // 重置搜索
    handleResetQuery() {
      this.resetForm('queryForm')
      const limit = localStorage.getItem(this.userId + '.inventoryQueryParams.limit')
      if (limit) this.queryParams.limit = Number(limit) || 10
      const stockOrg = localStorage.getItem(this.userId + '.inventoryQueryParams.stockOrg')
      if (stockOrg) this.queryParams.stockOrg = stockOrg
      this.handleQuery()
    },
    // 打开物料详情
    openMaterialDetail(number) {
      this.showMaterialDetail = true
      this.$nextTick(() => {
        this.$refs.materialDetail.getInfo(number)
      })
    }
  }
}
</script>
<style scoped lang="scss">
@import '@/assets/styles/custom-new.scss';
</style>
