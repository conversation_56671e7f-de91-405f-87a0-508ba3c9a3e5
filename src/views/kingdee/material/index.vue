<template>
  <div class="newBox" :class="{ 'vh-85': !isPopup, bgcf9: !isPopup }">
    <!-- 搜索 -->
    <div class="custom-search flex" style="padding-top: 18px" :style="{ justifyContent: showSearch ? 'space-between' : 'flex-end' }">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" @submit.native.prevent>
        <el-form-item label="使用组织" prop="useOrg" v-if="!isPopup || showUseOrg">
          <el-select v-model="queryParams.useOrg" placeholder="请选择使用组织" clearable>
            <el-option v-for="item in ApplicationOrgId" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="物料名称" prop="name">
          <el-input v-model="queryParams.name" placeholder="请输入物料名称" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="物料编码" prop="number">
          <el-input v-model="queryParams.number" placeholder="请输入物料编码" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="规格型号" prop="spec">
          <el-input v-model="queryParams.spec" placeholder="请输入规格型号" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="small" @click="handleResetQuery">重置</el-button>
          <el-button type="primary" icon="el-icon-plus" size="small" @click="handleAddMaterial" v-if="!isPopup" v-hasPermi="['kingdee:material:plus']">新增物料</el-button>
        </el-form-item>
      </el-form>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns" isSetitem @updateColumns="updateColumns"></right-toolbar>
    </div>
    <!-- 列表 -->
    <div style="padding: 15px 20px">
      <el-table v-loading="loading" ref="allTable" border :data="list" row-key="Number" style="width: 100%" class="custom-table" @row-click="handleRowClick" :highlight-current-row="isPopup" @selection-change="handleSelectionChange">
        <!-- 复选框 -->
        <el-table-column type="selection" width="55" align="center" v-if="!isPopup" />
        <!-- 单选按钮 -->
        <el-table-column align="center" label="选择" width="60" v-if="isPopup">
          <template slot-scope="scope">
            <el-radio v-model="selected.Number" :label="scope.row.Number" v-removeAriaHidden><span /></el-radio>
          </template>
        </el-table-column>
        <!-- 物料编码 -->
        <el-table-column align="center" prop="Number" label="物料编码" show-overflow-tooltip v-if="columns[0].visible">
          <template slot-scope="{ row }">
            <span class="table-link" @click="openMaterialDetail(row.Number)">{{ row.Number }}</span>
          </template>
        </el-table-column>
        <!-- 物料名称 -->
        <el-table-column align="center" prop="Name" label="物料名称" show-overflow-tooltip v-if="columns[1].visible"></el-table-column>
        <!-- 物料属性 -->
        <el-table-column align="center" prop="ErpClsID" label="物料属性" show-overflow-tooltip v-if="columns[2].visible">
          <template slot-scope="{ row }">{{ getMaterialPropertyLabel(row.ErpClsID) }}</template>
        </el-table-column>
        <!-- 单位 -->
        <el-table-column align="center" prop="Unit" label="单位" show-overflow-tooltip v-if="columns[3].visible"></el-table-column>
        <!-- 规格型号 -->
        <el-table-column align="center" prop="Specification" label="规格型号" show-overflow-tooltip v-if="columns[4].visible"></el-table-column>
        <!-- 禁用状态 -->
        <el-table-column align="center" prop="forbidStatus" label="禁用状态" show-overflow-tooltip v-if="columns[5].visible">
          <template slot-scope="{ row }">
            {{ row.forbidStatus === 'A' ? '否' : '是' }}
          </template>
        </el-table-column>
        <!-- 单据状态 -->
        <el-table-column align="center" prop="DocumentStatus" label="单据状态" show-overflow-tooltip v-if="columns[6].visible">
          <template slot-scope="{ row }">{{ getDocumentStatusLabel(row.documentStatus) }}</template>
        </el-table-column>
        <!-- 使用组织 -->
        <el-table-column align="center" prop="UseOrgName" label="使用组织" show-overflow-tooltip v-if="columns[7].visible"></el-table-column>
        <!-- 操作 -->
        <el-table-column align="center" label="操作" width="100" v-if="!isPopup">
          <template slot-scope="{ row }">
            <el-button type="text" size="small" @click="handleEditMaterial(row.Number)" :disabled="!(row.documentStatus === 'A' || row.documentStatus === 'D')">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="custom-pagination">
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.limit" @pagination="getList" />
      </div>
    </div>
    <!-- 物料详情 -->
    <material-detail ref="materialDetail" @callBack="detailCallBack" @update="handleUpdate" v-if="showMaterialDetail" />
    <!-- 新增物料 -->
    <material-create ref="materialCreate" v-if="showMaterialCreate" @callBack="createCallBack" />
    <!-- 批量操作 -->
    <template v-if="checked.length">
      <div class="collectAll">
        <div class="collectAll-box">
          <div class="collectAll-title">已选择 {{ checked.length }} 项</div>
          <el-tooltip :content="contentFormat(item.value)" placement="top" effect="dark" v-for="item in doType" :key="item.value">
            <div class="collectAll-btn" :class="{ disabled: classFormat(item.value) }" @click="handleBatch(item.value)" v-hasPermi="[item.hasPermission]">
              <span>{{ item.label }}</span>
            </div>
          </el-tooltip>
          <el-tooltip content="退出" placement="top" effect="dark">
            <div class="collectAll-close" @click="handleBatchClose">
              <i class="el-icon-close"></i>
            </div>
          </el-tooltip>
        </div>
      </div>
    </template>
  </div>
</template>
<script>
import { getMaterialList } from '@/api/kingdee'
import MaterialDetail from '@/views/kingdee/material/detail'
import MaterialCreate from '@/views/kingdee/material/create'
import { kingdee } from '@/minix'
import { auditMaterial, cancelMaterial, deleteMaterial, submitMaterial, unauditMaterial } from '@/api/kingdee/material'

export default {
  name: 'Material',
  props: {
    isPopup: {
      type: Boolean,
      default: false
    },
    showUseOrg: {
      type: Boolean,
      default: false
    },
    queryData: {
      type: Object,
      default: () => ({})
    }
  },
  components: { MaterialDetail, MaterialCreate },
  mixins: [kingdee],
  data() {
    return {
      // 搜索
      queryParams: {
        pageNum: 1, // 当前页码
        startRow: 0,
        limit: 10,
        useOrg: undefined,
        number: undefined,
        name: undefined,
        spec: undefined
      },
      // 列表
      list: [],
      total: 0,
      loading: false,
      // 选中的
      selected: {},
      showMaterialDetail: false,
      showMaterialCreate: false,
      // 物料属性
      ErpClsIDOptions: [
        { value: '1', label: '外购' },
        { value: '2', label: '自制' },
        { value: '3', label: '委外' },
        { value: '9', label: '配置' },
        { value: '10', label: '资产' },
        { value: '4', label: '特征' },
        { value: '11', label: '费用' },
        { value: '5', label: '虚拟' },
        { value: '6', label: '服务' },
        { value: '7', label: '一次性' },
        { value: '12', label: '模型' },
        { value: '13', label: '产品系列' }
      ],
      // 批量操作
      checked: [],
      doType: [
        { label: '批量提交', value: 'submit', hasPermission: 'kingdee:material:submit' },
        { label: '批量审核', value: 'audit', hasPermission: 'kingdee:material:audit' },
        { label: '批量撤销', value: 'revoke', hasPermission: 'kingdee:material:revoke' },
        { label: '批量删除', value: 'delete', hasPermission: 'kingdee:material:delete' },
        { label: '批量反审', value: 'unaudit', hasPermission: 'kingdee:material:unAudit' }
      ],
      // 列表显隐
      showSearch: true,
      columns: [
        { key: 0, label: `物料编码`, visible: true },
        { key: 1, label: `物料名称`, visible: true },
        { key: 2, label: `物料属性`, visible: true },
        { key: 3, label: `单位`, visible: true },
        { key: 4, label: `规格型号`, visible: true },
        { key: 5, label: `禁用状态`, visible: true },
        { key: 6, label: `单据状态`, visible: true },
        { key: 7, label: `使用组织`, visible: true }
      ]
    }
  },
  computed: {
    // 获取当前用户ID
    userId() {
      return this.$store.getters.info.userId
    }
  },
  created() {
    const columns = localStorage.getItem(this.userId + '.materialColumns')
    if (columns) this.columns = JSON.parse(columns)
    // 获取列表
    if (!this.isPopup) this.getList()
  },
  methods: {
    // 改变显隐列缓存
    updateColumns(data = []) {
      localStorage.setItem(this.userId + '.materialColumns', JSON.stringify(data))
    },
    // 列表
    getList() {
      this.loading = true
      this.queryParams.startRow = Math.max(0, (this.queryParams.pageNum - 1) * this.queryParams.limit)
      const query = { ...this.queryParams }
      delete query.pageNum
      getMaterialList(query)
        .then(res => {
          const { code, msg, data } = res
          if (code === 200) {
            const { data: list, total } = data
            this.list = list
            this.total = total
          } else this.$message.error(msg)
        })
        .finally(() => {
          this.loading = false
        })
    },
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 重置搜索
    handleResetQuery(data = {}) {
      this.resetForm('queryForm')
      if (this.isPopup) {
        this.selected = this.queryData?.selected || {}
        this.queryParams.useOrg = this.queryData?.useOrg || undefined
      }
      this.queryParams.number = data?.materialCode || undefined
      this.queryParams.name = data?.productName || undefined
      this.queryParams.spec = data?.specs || undefined
      this.handleQuery()
    },
    // 行点击
    handleRowClick(row) {
      this.selected = row
      if (this.isPopup) this.$emit('selected', row)
    },
    // 打开物料详情
    openMaterialDetail(number) {
      this.showMaterialDetail = true
      this.$nextTick(() => {
        this.$refs.materialDetail.getInfo(number)
      })
    },
    // 获取物料属性
    getMaterialPropertyLabel(value) {
      return this.ErpClsIDOptions.find(item => item.value === value)?.label || '--'
    },
    // 新增物料
    handleAddMaterial() {
      this.showMaterialCreate = true
      this.$nextTick(() => {
        this.$refs.materialCreate.init()
      })
    },
    // 物料详情回调
    detailCallBack(flag) {
      this.showMaterialDetail = false
      if (flag) this.getList()
    },
    // 新增物料回调
    createCallBack(flag) {
      this.showMaterialCreate = false
      if (flag) this.getList()
    },
    // 编辑物料
    handleEditMaterial(number) {
      this.showMaterialCreate = true
      this.$nextTick(() => {
        this.$refs.materialCreate.init(number)
      })
    },
    // 多选
    handleSelectionChange(val) {
      this.checked = val
    },
    // 格式化批量提示
    contentFormat(type) {
      const hasLen = this.checked.length
      if (!hasLen) return '请选择要操作的数据'
      const DocumentStatus = this.checked.map(item => item.documentStatus)
      const onlyAorD = DocumentStatus.every(status => status === 'A' || status === 'D')
      const onlyB = DocumentStatus.every(status => status === 'B')
      const onlyC = DocumentStatus.every(status => status === 'C')
      switch (type) {
        case 'submit':
          return onlyAorD ? '批量提交' : '选择的物料中单据状态只能为创建或重新审核方可批量提交'
        case 'audit':
          return onlyB ? '批量审核' : '选择的物料中单据状态只能为已提交的方可以批量审核'
        case 'revoke':
          return onlyB ? '批量撤销' : '选择的物料中单据状态只能为已提交的方可以批量撤销'
        case 'delete':
          return onlyAorD ? '批量删除' : '选择的物料中单据状态只能为创建或重新审核方可批量删除'
        case 'unaudit':
          return onlyC ? '批量反审' : '选择的物料中单据状态只能为已审核方可批量反审'
        default:
          return ''
      }
    },
    // 格式化样式
    classFormat(type) {
      if (!this.checked.length) return true
      const DocumentStatus = this.checked.map(item => item.documentStatus)
      const onlyAorD = DocumentStatus.every(status => status === 'A' || status === 'D')
      const onlyB = DocumentStatus.every(status => status === 'B')
      const onlyC = DocumentStatus.every(status => status === 'C')
      switch (type) {
        case 'submit':
          return !onlyAorD
        case 'audit':
          return !onlyB
        case 'revoke':
          return !onlyB
        case 'delete':
          return !onlyAorD
        case 'unaudit':
          return !onlyC
        default:
          return true
      }
    },
    // 批量操作
    handleBatch(type) {
      if (!this.checked.length) return
      const DocumentStatus = this.checked.map(item => item.documentStatus)
      const onlyAorD = DocumentStatus.every(status => status === 'A' || status === 'D')
      if (type === 'submit' && !onlyAorD) return
      if (type === 'audit' && !DocumentStatus.every(status => status === 'B')) return
      if (type === 'revoke' && !DocumentStatus.every(status => status === 'B')) return
      if (type === 'delete' && !onlyAorD) return
      if (type === 'unaudit' && !DocumentStatus.every(status => status === 'C')) return
      const title = this.doType.find(item => item.value === type).label
      this.$confirm(`是否确认${title}？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          const errors = []
          const loading = this.$loading({
            lock: true,
            text: '处理中...',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })
          try {
            if (type === 'submit') {
              for (const item of this.checked) {
                try {
                  await submitMaterial({ number: item.Number })
                } catch (error) {
                  errors.push(`${item.Number}：${error.message || '提交失败'}`)
                }
              }
            } else if (type === 'audit') {
              for (const item of this.checked) {
                try {
                  await auditMaterial({ number: item.Number })
                } catch (error) {
                  errors.push(`${item.Number}：${error.message || '审核失败'}`)
                }
              }
            } else if (type === 'revoke') {
              for (const item of this.checked) {
                try {
                  await cancelMaterial({ number: item.Number })
                } catch (error) {
                  errors.push(`${item.Number}：${error.message || '撤销失败'}`)
                }
              }
            } else if (type === 'delete') {
              for (const item of this.checked) {
                try {
                  await deleteMaterial({ number: item.Number })
                } catch (error) {
                  errors.push(`${item.Number}：${error.message || '删除失败'}`)
                }
              }
            } else if (type === 'unaudit') {
              for (const item of this.checked) {
                try {
                  await unauditMaterial({ number: item.Number })
                } catch (error) {
                  errors.push(`${item.Number}：${error.message || '反审失败'}`)
                }
              }
            }
            loading.close()
            if (errors.length > 0) {
              this.$alert(errors.join('<br>'), '操作结果', {
                dangerouslyUseHTMLString: true,
                confirmButtonText: '确定'
              })
            } else {
              this.$message.success('操作成功')
            }
            this.handleBatchClose()
            this.getList()
          } catch (error) {
            loading.close()
            this.$message.error('批量操作失败')
          }
        })
        .catch(() => {})
    },
    // 关闭多选
    handleBatchClose() {
      this.$nextTick(() => {
        if (this.$refs.allTable) this.$refs.allTable.clearSelection()
      })
      this.checked = []
    },
    // 更新
    handleUpdate(data) {
      const { billNo, newStatus } = data
      const index = this.list.findIndex(item => item.Number === billNo)
      if (index !== -1) this.list[index].documentStatus = newStatus
    }
  }
}
</script>
<style scoped lang="scss">
@import '@/assets/styles/custom-new.scss';
.collectAll-btn.disabled {
  span {
    cursor: not-allowed;
    color: #ccc;
  }
}
</style>
