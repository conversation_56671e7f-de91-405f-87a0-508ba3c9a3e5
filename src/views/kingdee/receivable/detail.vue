<template>
  <div>
    <el-dialog v-dialogDragBox title="应收单详情" :visible.sync="open" width="90%" class="custom-dialog custom-dialog-body">
      <template slot="title">
        <div class="custom-dialog-title">
          应收单详情
          <el-button type="primary" size="small" icon="el-icon-plus" style="margin-left: 10px" @click="handleAdd">新增一款一档</el-button>
        </div>
      </template>
      <div style="padding: 0 20px">
        <!-- 基本 -->
        <el-descriptions border :column="4" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 4 - 7em - 22px)' }">
          <template slot="title">
            <div class="customTitle">基本</div>
          </template>
          <el-descriptions-item label="单据类型">{{ info.BillTypeID && info.BillTypeID.Name && getString(info.BillTypeID.Name) }}</el-descriptions-item>
          <el-descriptions-item label="单据编号">{{ info.BillNo }}</el-descriptions-item>
          <el-descriptions-item label="业务日期">{{ parseTime(info.DATE, '{y}-{m}-{d}') }}</el-descriptions-item>
          <el-descriptions-item label="到期日">{{ parseTime(info.FENDDATE_H, '{y}-{m}-{d}') }}</el-descriptions-item>
          <el-descriptions-item label="单据状态">{{ getDocumentStatusLabel(info.DocumentStatus) }}</el-descriptions-item>
          <el-descriptions-item label="立账类型">{{ getFSetAccountTypeLabel(info.FSetAccountType) }}</el-descriptions-item>
          <el-descriptions-item label="按费用项目生成计划">
            <el-checkbox :value="info.FISGENERATEPLANBYCOSTITEM" disabled></el-checkbox>
          </el-descriptions-item>
          <el-descriptions-item label="客户">{{ info.CUSTOMERID && info.CUSTOMERID.Name && getString(info.CUSTOMERID.Name) }}</el-descriptions-item>
          <el-descriptions-item label="币别">{{ info.CURRENCYID && info.CURRENCYID.Name && getString(info.CURRENCYID.Name) }}</el-descriptions-item>
          <el-descriptions-item label="价税合计">{{ info.FALLAMOUNTFOR }}</el-descriptions-item>
          <el-descriptions-item label="收款条件">{{ info.PayConditon && info.PayConditon.Name && getString(info.PayConditon.Name) }}</el-descriptions-item>
          <el-descriptions-item label="整单折扣金额">{{ info.FOrderDiscountAmountFor || '' }}</el-descriptions-item>
          <el-descriptions-item label="是否含税">
            <el-checkbox :value="info.F_SCMJ_CheckBox" disabled></el-checkbox>
          </el-descriptions-item>
          <el-descriptions-item label="收款组织">{{ info.FPAYORGID && info.FPAYORGID.Name && getString(info.FPAYORGID.Name) }}</el-descriptions-item>
          <el-descriptions-item label="结算组织">{{ info.SETTLEORGID && info.SETTLEORGID.Name && getString(info.SETTLEORGID.Name) }}</el-descriptions-item>
          <el-descriptions-item label="是否下推应付">{{ getF_SCMJ_IFYFLabel(info.F_SCMJ_IFYF) }}</el-descriptions-item>
          <el-descriptions-item label="应付供应商">{{ info.F_SCMJ_YFGYS && info.F_SCMJ_YFGYS.Name && getString(info.F_SCMJ_YFGYS.Name) }}</el-descriptions-item>
          <el-descriptions-item label="备注">{{ info.REMARK }}</el-descriptions-item>
          <el-descriptions-item label="销售组织">{{ info.SALEORGID && info.SALEORGID.Name && getString(info.SALEORGID.Name) }}</el-descriptions-item>
          <el-descriptions-item label="销售部门">{{ info.SALEDEPTID && info.SALEDEPTID.Name && getString(info.SALEDEPTID.Name) }}</el-descriptions-item>
          <el-descriptions-item label="销售员">{{ info.SALEERID && info.SALEERID.Name && getString(info.SALEERID.Name) }}</el-descriptions-item>
          <el-descriptions-item label="销售订单号">{{ info.F_SCMJ_XSDDH }}</el-descriptions-item>
          <el-descriptions-item label="跟单员">{{ info.F_SCMJ_GDY && info.F_SCMJ_GDY.Name && getString(info.F_SCMJ_GDY.Name) }}</el-descriptions-item>
          <el-descriptions-item label="参与暂估应收核销">
            <el-checkbox :value="info.FISHookMatch" disabled></el-checkbox>
          </el-descriptions-item>
        </el-descriptions>
        <!-- 明细 -->
        <div class="customTitle">明细</div>
        <el-table ref="detailTable" :data="PAYABLEENTRY" class="custom-table" show-summary>
          <!-- 序号 -->
          <el-table-column type="index" label="序号" align="center"></el-table-column>
          <!-- 物料编码 -->
          <el-table-column label="物料编码" align="center" show-overflow-tooltip min-width="130">
            <template slot-scope="scope">{{ scope.row.MATERIALID && scope.row.MATERIALID.Number }}</template>
          </el-table-column>
          <!-- 物料名称 -->
          <el-table-column label="物料名称" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.MATERIALID && scope.row.MATERIALID.Name && getString(scope.row.MATERIALID.Name) }}</template>
          </el-table-column>
          <!-- 计价单位 -->
          <el-table-column label="计价单位" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.PRICEUNITID && scope.row.PRICEUNITID.Name && getString(scope.row.PRICEUNITID.Name) }}</template>
          </el-table-column>
          <!-- 计价数量 -->
          <el-table-column prop="PriceQty" label="计价数量" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.PriceQty }}</template>
          </el-table-column>
          <!-- 含税单价 -->
          <el-table-column label="含税单价" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.TaxPrice || '' }}</template>
          </el-table-column>
          <!-- 单价 -->
          <el-table-column label="单价" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.FPrice || '' }}</template>
          </el-table-column>
          <!-- 税率(%) -->
          <el-table-column label="税率(%)" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.EntryTaxRate || '' }}</template>
          </el-table-column>
          <!-- 折扣率(%) -->
          <el-table-column label="折扣率(%)" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.EntryDiscountRate || '' }}</template>
          </el-table-column>
          <!-- 不含税金额 -->
          <el-table-column prop="FNoTaxAmountFor_D" label="不含税金额" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.FNoTaxAmountFor_D || '' }}</template>
          </el-table-column>
          <!-- 折扣额 -->
          <el-table-column prop="FDISCOUNTAMOUNTFOR" label="折扣额" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.FDISCOUNTAMOUNTFOR || '' }}</template>
          </el-table-column>
          <!-- 备注 -->
          <el-table-column label="备注" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.Comment }}</template>
          </el-table-column>
          <!-- 税额 -->
          <el-table-column prop="FTAXAMOUNTFOR_D" label="税额" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.FTAXAMOUNTFOR_D || '' }}</template>
          </el-table-column>
          <!-- 价税合计 -->
          <el-table-column prop="FALLAMOUNTFOR_D" label="价税合计" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.FALLAMOUNTFOR_D || '' }}</template>
          </el-table-column>
          <!-- 计价基本数量 -->
          <el-table-column prop="BASICUNITQTY" label="计价基本数量" align="center" min-width="100" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.BASICUNITQTY }}</template>
          </el-table-column>
          <!-- 批号 -->
          <el-table-column label="批号" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.FLot && scope.row.FLot.Number }}</template>
          </el-table-column>
          <!-- 库存数量 -->
          <el-table-column label="库存数量" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.StockQty }}</template>
          </el-table-column>
          <!-- 库存基本数量 -->
          <el-table-column label="库存基本数量" align="center" min-width="100" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.StockBaseQty }}</template>
          </el-table-column>
          <!-- 销售单位 -->
          <el-table-column label="销售单位" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.SalUnitId && scope.row.SalUnitId.Name && getString(scope.row.SalUnitId.Name) }}</template>
          </el-table-column>
          <!-- 销售数量 -->
          <el-table-column label="销售数量" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.SalQty }}</template>
          </el-table-column>
          <!-- 销售基本数量 -->
          <el-table-column label="销售基本数量" align="center" min-width="100" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.SalBaseQty }}</template>
          </el-table-column>
          <!-- 成本金额 -->
          <el-table-column prop="COSTAMTSUM" label="成本金额" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.COSTAMTSUM || '' }}</template>
          </el-table-column>
          <!-- 销售订单行号 -->
          <el-table-column label="销售订单行号" align="center" min-width="100" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.ORDERENTRYSEQ }}</template>
          </el-table-column>
          <!-- 销售基本数量(财务) -->
          <el-table-column label="销售基本数量(财务)" align="center" min-width="130" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.FSALFINBASEQTY || '' }}</template>
          </el-table-column>
          <!-- 已结算金额 -->
          <el-table-column prop="FRECEIVEAMOUNT" label="已结算金额" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.FRECEIVEAMOUNT || '' }}</template>
          </el-table-column>
          <!-- 出库仓库 -->
          <el-table-column label="出库仓库" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.F_SCMJ_CKCK && scope.row.F_SCMJ_CKCK.Name && getString(scope.row.F_SCMJ_CKCK.Name) }}</template>
          </el-table-column>
          <!-- 仓库部门 -->
          <el-table-column label="仓库部门" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.F_SCMJ_CKCK && scope.row.F_SCMJ_CKCK.DeptId && scope.row.F_SCMJ_CKCK.DeptId.Name && getString(scope.row.F_SCMJ_CKCK.DeptId.Name) }}</template>
          </el-table-column>
          <!-- 是否赠品 -->
          <el-table-column label="是否赠品" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
              <el-checkbox v-model="scope.row.IsFree" disabled></el-checkbox>
            </template>
          </el-table-column>
        </el-table>
        <!-- 收款计划 -->
        <div class="customTitle">收款计划</div>
        <el-table :data="PAYABLEPLAN" class="custom-table">
          <!-- 序号 -->
          <el-table-column type="index" label="序号" align="center"></el-table-column>
          <!-- 到期日 -->
          <el-table-column label="到期日" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ parseTime(scope.row.ENDDATE, '{y}-{m}-{d}') }}</template>
          </el-table-column>
          <!-- 应收比例(%) -->
          <el-table-column label="应收比例(%)" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.FPAYRATE }}</template>
          </el-table-column>
          <!-- 应收金额 -->
          <el-table-column label="应收金额" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.PAYAMOUNTFOR }}</template>
          </el-table-column>
          <!-- 应收金额本币 -->
          <el-table-column label="应收金额本位币" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.PAYAMOUNT }}</template>
          </el-table-column>
          <!-- 销售订单号 -->
          <el-table-column label="销售订单号" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.ORDERBILLNO }}</template>
          </el-table-column>
          <!-- 订单行号 -->
          <el-table-column label="订单行号" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.FMATERIALSEQ || '' }}</template>
          </el-table-column>
          <!-- 物料编码 -->
          <el-table-column label="物料编码" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.FMATERIALID_S && scope.row.FMATERIALID_S.Number }}</template>
          </el-table-column>
          <!-- 物料名称 -->
          <el-table-column label="物料名称" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.FMATERIALID_S && scope.row.FMATERIALID_S.Name && getString(scope.row.FMATERIALID_S.Name) }}</template>
          </el-table-column>
          <!-- 备注 -->
          <el-table-column label="备注" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.FREMARK }}</template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
    <payment-create ref="paymentCreate" />
  </div>
</template>
<script>
import { getReceivableDetail } from '@/api/kingdee/receivable'
import PaymentCreate from '@/views/payment/create'
import { kingdee } from '@/minix'

export default {
  mixins: [kingdee],
  components: { PaymentCreate },
  data() {
    return {
      open: false,
      info: {},
      PAYABLEENTRY: [],
      PAYABLEPLAN: [],
      // 立账类型
      FSetAccountTypeOptions: [
        { label: '业务应收', value: '1' },
        { label: '暂估应收', value: '2' },
        { label: '财务应收', value: '3' }
      ],
      // 是否下推应付
      F_SCMJ_IFYFOptions: [
        { label: '是', value: '1' },
        { label: '否', value: '0' }
      ]
    }
  },
  methods: {
    // 查看详情
    getInfo(row = {}) {
      if (!row.BillNo) {
        this.$message.error('参数错误，请重新选择')
        return
      }
      getReceivableDetail({ billNo: row.BillNo }).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          const { result } = data
          this.open = !!result?.result
          this.info = result?.result || {}
          this.PAYABLEPLAN = result?.result?.AP_PAYABLEPLAN || []
          this.$nextTick(() => {
            this.PAYABLEENTRY = result?.result?.AP_PAYABLEENTRY || []
            if (this.$refs.detailTable) this.$refs.detailTable.bodyWrapper.scrollLeft = 0
          })
        } else this.$message.error(msg)
      })
    },
    // 获取立账类型
    getFSetAccountTypeLabel(value) {
      const obj = this.FSetAccountTypeOptions.find(item => item.value === value)
      return obj?.label || ''
    },
    // 获取是否下推应付
    getF_SCMJ_IFYFLabel(value) {
      const obj = this.F_SCMJ_IFYFOptions.find(item => item.value === value)
      return obj?.label || ''
    },
    // 新增一款一档
    handleAdd() {
      this.$refs.paymentCreate.create(this.info)
    }
  }
}
</script>
<style scoped lang="scss">
@import '@/assets/styles/custom-new.scss';
::v-deep {
  .custom-dialog-body {
    .el-descriptions__header {
      margin-bottom: 0;
    }
    .el-dialog__body {
      padding-top: 0 !important;
    }
  }
}
.customTitle {
  font-size: 16px;
  font-weight: normal;
  line-height: 60px;
  color: $font;
}
</style>
