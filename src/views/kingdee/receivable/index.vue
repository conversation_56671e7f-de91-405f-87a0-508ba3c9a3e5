<template>
  <div class="newBox bgcf9 vh-85">
    <!-- 搜索 -->
    <div class="custom-search flex" style="padding-top: 18px" :style="{ justifyContent: showSearch ? 'space-between' : 'flex-end' }">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" @submit.native.prevent>
        <el-form-item label="单据编号" prop="billNo">
          <el-input v-model="queryParams.billNo" placeholder="请输入单据编号" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="客户名称" prop="customerName">
          <el-input v-model="queryParams.customerName" placeholder="请输入客户名称" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="使用组织" prop="useOrg">
          <el-select v-model="queryParams.useOrg" placeholder="请选择使用组织" clearable>
            <el-option v-for="item in ApplicationOrgId" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="small" @click="handleResetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns" isSetitem @updateColumns="updateColumns"></right-toolbar>
    </div>
    <!-- 列表 -->
    <div style="padding: 15px 20px">
      <el-table v-loading="loading" border ref="allTable" :data="list" class="custom-table" show-summary>
        <!-- 单据编号 -->
        <el-table-column align="center" show-overflow-tooltip label="单据编号" prop="BillNo" v-if="columns[0].visible">
          <template slot-scope="{ row }">
            <span class="table-link" @click="handleDetail(row)">{{ row.BillNo }}</span>
          </template>
        </el-table-column>
        <!-- 单据类型 -->
        <el-table-column align="center" show-overflow-tooltip label="单据类型" prop="BillType" v-if="columns[1].visible" />
        <!-- 业务日期 -->
        <el-table-column align="center" show-overflow-tooltip label="业务日期" prop="Date" v-if="columns[2].visible">
          <template slot-scope="{ row }">{{ parseTime(row.Date, '{y}-{m}-{d}') }}</template>
        </el-table-column>
        <!-- 客户 -->
        <el-table-column align="center" show-overflow-tooltip label="客户" prop="Customer" v-if="columns[3].visible" />
        <!-- 币别 -->
        <el-table-column align="center" show-overflow-tooltip label="币别" prop="Currency" v-if="columns[4].visible" />
        <!-- 价税合计 -->
        <el-table-column align="center" show-overflow-tooltip label="价税合计" prop="AllAmount" v-if="columns[5].visible">
          <template slot-scope="{ row }">{{ row.AllAmount }}</template>
        </el-table-column>
        <!-- 结算组织 -->
        <el-table-column align="center" show-overflow-tooltip label="结算组织" prop="SettleOrg" v-if="columns[6].visible" />
        <!-- 销售组织 -->
        <el-table-column align="center" show-overflow-tooltip label="销售组织" prop="SaleOrg" v-if="columns[7].visible" />
        <!-- 单据状态 -->
        <el-table-column align="center" show-overflow-tooltip label="单据状态" prop="DocumentStatus" v-if="columns[8].visible">
          <template slot-scope="{ row }">{{ getDocumentStatusLabel(row.DocumentStatus) }}</template>
        </el-table-column>
        <!-- 到期日 -->
        <el-table-column align="center" show-overflow-tooltip label="到期日" prop="EndDate" v-if="columns[9].visible">
          <template slot-scope="{ row }">{{ parseTime(row.EndDate, '{y}-{m}-{d}') }}</template>
        </el-table-column>
        <!-- 收款组织 -->
        <el-table-column align="center" show-overflow-tooltip label="收款组织" prop="PayOrg" v-if="columns[10].visible" />
      </el-table>
      <!-- 分页 -->
      <div class="custom-pagination">
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.limit" @pagination="getList" />
      </div>
    </div>
    <!--详情-->
    <receivable-detail ref="receivableDetail" @callBack="getList" v-if="showDetail"></receivable-detail>
  </div>
</template>
<script>
import { getReceivableList } from '@/api/kingdee/receivable'
import ReceivableDetail from '@/views/kingdee/receivable/detail'
import { kingdee } from '@/minix'

export default {
  name: 'Kreceivable',
  mixins: [kingdee],
  components: { ReceivableDetail },
  data() {
    return {
      loading: false,
      list: [],
      total: 0,
      queryParams: {
        pageNum: 1,
        startRow: 0, // 开始行索引
        limit: 10, // 最大行数
        useOrg: undefined, // 使用组织
        billNo: undefined, // 单据编号
        customerName: undefined // 客户
      },
      showDetail: true,
      // 列表显隐
      showSearch: true,
      // 列信息
      columns: [
        { key: 0, label: `单据编号`, visible: true },
        { key: 1, label: `单据类型`, visible: true },
        { key: 2, label: `业务日期`, visible: true },
        { key: 3, label: `客户`, visible: true },
        { key: 4, label: `币别`, visible: true },
        { key: 5, label: `价税合计`, visible: true },
        { key: 6, label: `结算组织`, visible: true },
        { key: 7, label: `销售组织`, visible: true },
        { key: 8, label: `单据状态`, visible: true },
        { key: 9, label: `到期日`, visible: true },
        { key: 10, label: `收款组织`, visible: true }
      ]
    }
  },
  computed: {
    // 获取当前用户ID
    userId() {
      return this.$store.getters.info.userId
    }
  },
  created() {
    const columns = localStorage.getItem(this.userId + '.receivableColumns')
    if (columns) this.columns = JSON.parse(columns)
    this.getList()
  },
  methods: {
    // 改变显隐列缓存
    updateColumns(data = []) {
      localStorage.setItem(this.userId + '.receivableColumns', JSON.stringify(data))
    },
    // 列表
    getList() {
      this.loading = true
      this.queryParams.startRow = Math.max(0, (this.queryParams.pageNum - 1) * this.queryParams.limit)
      const query = { ...this.queryParams }
      delete query.pageNum
      getReceivableList(query).then(res => {
        const { code, data, msg } = res
        if (code === 200) {
          const { data: list, total } = data
          this.list = list
          this.total = total
        } else this.$message.error(msg)
        this.loading = false
      })
    },
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 重置搜索
    handleResetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 详情
    handleDetail(row) {
      this.showDetail = true
      this.$nextTick(() => {
        this.$refs.receivableDetail.getInfo(row)
      })
    }
  }
}
</script>
<style scoped lang="scss">
@import '@/assets/styles/custom-new.scss';
</style>
