<template>
  <div class="newBox bgcf9 vh-85">
    <!-- 搜索 -->
    <div class="custom-search flex" style="padding-top: 18px" :style="{ justifyContent: showSearch ? 'space-between' : 'flex-end' }">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" @submit.native.prevent>
        <el-form-item label="结算组织" prop="settleOrg">
          <el-select v-model="queryParams.settleOrg" placeholder="请选择结算组织" multiple collapse-tags>
            <el-option v-for="org in ApplicationOrgNumber" :key="org.value" :label="org.label" :value="org.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="日期" prop="date">
          <el-date-picker v-model="queryParams.date" type="date" placeholder="选择日期" value-format="yyyy-MM-dd" clearable></el-date-picker>
        </el-form-item>
        <el-form-item label="往来单位类型" prop="contactUnitType">
          <el-select v-model="queryParams.contactUnitType" placeholder="请选择往来单位类型" @change="handleContactUnitTypeChange">
            <el-option v-for="type in ContactUnitTypeOptions" :key="type.value" :label="type.label" :value="type.value"></el-option>
          </el-select>
        </el-form-item>
        <customer-search-select :keyword.sync="queryParams.customerNumber" @callBack="handleQuery" v-if="queryParams.contactUnitType === 'BD_Customer'" />
        <supplier-search-select :keyword.sync="queryParams.customerNumber" @callBack="handleQuery" v-if="queryParams.contactUnitType === 'BD_Supplier'" />
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="small" @click="handleResetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns" isSetitem @updateColumns="updateColumns"></right-toolbar>
    </div>
    <!-- 列表 -->
    <div style="padding: 15px 20px">
      <el-table v-loading="loading" ref="allTable" border :data="list" style="width: 100%" class="custom-table" :row-class-name="tableRowClassName" :default-sort="{ prop: 'leftAmount', order: 'descending' }">
        <!-- 往来单位编码 -->
        <el-table-column align="center" prop="ContactUnitNumber" label="往来单位编码" show-overflow-tooltip v-if="columns[0].visible"></el-table-column>
        <!-- 往来单位 -->
        <el-table-column align="center" prop="ContactUnit" label="往来单位" show-overflow-tooltip v-if="columns[1].visible"></el-table-column>
        <!-- 币别 -->
        <el-table-column align="center" prop="CurrencyName" label="币别" show-overflow-tooltip v-if="columns[2].visible"></el-table-column>
        <!-- 收付组织 -->
        <el-table-column align="center" prop="PayOrgName" label="收付组织" show-overflow-tooltip v-if="columns[3].visible"></el-table-column>
        <!-- 原币 -->
        <el-table-column align="center" label="原币" v-if="columns[4].visible">
          <!-- 尚未收款金额 -->
          <el-table-column align="center" prop="BalanceAmtFor" label="尚未收款金额" show-overflow-tooltip sortable v-if="columns[5].visible"></el-table-column>
          <!-- 0-30天 -->
          <el-table-column align="center" prop="Balance1AmtFor" label="0-30天" show-overflow-tooltip v-if="columns[6].visible"></el-table-column>
          <!-- 31-60天 -->
          <el-table-column align="center" prop="Balance2AmtFor" label="31-60天" show-overflow-tooltip v-if="columns[7].visible"></el-table-column>
          <!-- 61-90天 -->
          <el-table-column align="center" prop="Balance3AmtFor" label="61-90天" show-overflow-tooltip v-if="columns[8].visible"></el-table-column>
          <!-- 90天以上 -->
          <el-table-column align="center" prop="Balance4AmtFor" label="90天以上" show-overflow-tooltip v-if="columns[9].visible"></el-table-column>
        </el-table-column>
        <!-- 立账类型 -->
        <el-table-column align="center" prop="SetAccountType" label="立账类型" show-overflow-tooltip v-if="columns[10].visible"></el-table-column>
        <!-- 备注 -->
        <el-table-column align="center" prop="remark" label="备注" show-overflow-tooltip v-if="columns[11].visible"></el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="custom-pagination">
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.limit" :page-sizes="[30, 60, 90, 150, 300]" @pagination="getList" />
      </div>
    </div>
  </div>
</template>
<script>
import { kingdee } from '@/minix'
import { getReceivableAging } from '@/api/kingdee/receivable'
import { parseTime } from '@/utils/ruoyi'
import customerSearchSelect from '@/components/SearchSelect/customer'
import supplierSearchSelect from '@/components/SearchSelect/supplier'

export default {
  name: 'ReceivableAging',
  components: { customerSearchSelect, supplierSearchSelect },
  mixins: [kingdee],
  data() {
    return {
      queryParams: {
        pageNum: 1,
        startRow: 0,
        limit: 30,
        settleOrg: '',
        date: parseTime(new Date(), '{y}-{m}-{d}'),
        contactUnitType: 'BD_Customer',
        customerNumber: '',
        customerName: ''
      },
      list: [],
      total: 0,
      loading: false,
      showSearch: true,
      columns: [
        { key: 0, label: `往来单位编码`, visible: true },
        { key: 1, label: `往来单位`, visible: true },
        { key: 2, label: `币别`, visible: true },
        { key: 3, label: `收付组织`, visible: true },
        { key: 4, label: `原币`, visible: true },
        { key: 5, label: `尚未收款金额`, visible: true },
        { key: 6, label: `0-30天`, visible: true },
        { key: 7, label: `31-60天`, visible: true },
        { key: 8, label: `61-90天`, visible: true },
        { key: 9, label: `90天以上`, visible: true },
        { key: 10, label: `立账类型`, visible: true },
        { key: 11, label: `备注`, visible: true }
      ]
    }
  },
  computed: {
    // 获取当前用户ID
    userId() {
      return this.$store.getters.info.userId
    }
  },
  created() {
    const columns = localStorage.getItem(this.userId + '.receivableAgingColumns')
    if (columns) this.columns = JSON.parse(columns)
    if (!this.queryParams.settleOrg) this.queryParams.settleOrg = [this.ApplicationOrgNumber[0].value]
    this.getList()
  },
  methods: {
    // 改变显隐列缓存
    updateColumns(data = []) {
      localStorage.setItem(this.userId + '.receivableAgingColumns', JSON.stringify(data))
    },
    // 获取列表
    getList() {
      this.loading = true
      this.queryParams.startRow = Math.max(0, (this.queryParams.pageNum - 1) * this.queryParams.limit)
      const query = { ...this.queryParams, settleOrg: this.queryParams.settleOrg.join(',') }
      delete query.pageNum
      getReceivableAging(query).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          const { data: list, total } = data
          this.list = list
          this.total = total
        } else this.$message.error(msg)
        this.loading = false
      })
    },
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 重置
    handleResetQuery() {
      this.resetForm('queryForm')
      this.queryParams.customerNumber = undefined
      this.handleQuery()
    },
    // 往来单位类型改变
    handleContactUnitTypeChange(value) {
      this.resetForm('queryForm')
      this.queryParams.customerNumber = undefined
      this.queryParams.contactUnitType = value
      this.handleQuery()
    },
    // 表格行样式
    tableRowClassName({ row }) {
      if (row.ContactUnit === '') return 'highlight-row'
    }
  }
}
</script>
<style scoped lang="scss">
@import '@/assets/styles/custom-new.scss';
.el-table.custom-table {
  ::v-deep .el-table__row.highlight-row {
    td.el-table__cell {
      background-color: #f5f5f5 !important;
    }
    &:hover {
      td.el-table__cell {
        background-color: #f5f5f5 !important;
      }
    }
  }
}
.custom-dialog.custom-customer-dialog ::v-deep {
  .el-dialog__body {
    padding: 0;
  }
}
</style>
