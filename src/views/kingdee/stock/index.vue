<template>
  <div class="newBox" :class="{ 'vh-85': !isPopup, bgcf9: !isPopup }">
    <!-- 搜索 -->
    <div class="custom-search flex" :style="{ paddingTop: isPopup ? '0' : '18px', paddingBottom: isPopup ? '0' : '7px' }">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" @submit.native.prevent>
        <el-form-item label="使用组织" prop="useOrg" v-if="(!isPopup && showUseOrg) || showUseOrg || isUseOrg">
          <el-select v-model="queryParams.useOrg" placeholder="请选择使用组织" :style="{ width: (!isPopup && showUseOrg) || showUseOrg || isUseOrg ? '150px' : 'auto' }">
            <el-option v-for="org in ApplicationOrgId" :key="org.value" :label="org.label" :value="org.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="仓库编码" prop="number">
          <el-input v-model="queryParams.number" placeholder="请输入仓库编码" clearable @keyup.enter.native="handleQuery" :style="{ width: (!isPopup && showUseOrg) || showUseOrg || isUseOrg ? '170px' : 'auto' }" />
        </el-form-item>
        <el-form-item label="仓库名称" prop="name">
          <el-input v-model="queryParams.name" placeholder="请输入仓库名称" clearable @keyup.enter.native="handleQuery" :style="{ width: (!isPopup && showUseOrg) || showUseOrg || isUseOrg ? '170px' : 'auto' }" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="small" @click="handleResetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <!-- 列表 -->
    <div style="padding: 15px 20px">
      <el-table v-loading="loading" ref="allTable" border :data="list" row-key="Number" style="width: 100%" class="custom-table" @row-click="handleRowClick" :highlight-current-row="isPopup">
        <!-- 单选按钮 -->
        <el-table-column align="center" label="选择" width="60" v-if="isPopup">
          <template slot-scope="scope">
            <el-radio v-model="selectedStock.Number" :label="scope.row.Number" v-removeAriaHidden><span /></el-radio>
          </template>
        </el-table-column>
        <!-- 仓库编码 -->
        <el-table-column align="center" prop="Number" label="仓库编码" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <span class="table-link">{{ row.Number }}</span>
          </template>
        </el-table-column>
        <!-- 仓库名称 -->
        <el-table-column align="center" prop="Name" label="仓库名称" show-overflow-tooltip></el-table-column>
        <!-- 仓库属性 -->
        <el-table-column align="center" prop="StockProperty" label="仓库属性" :formatter="stockPropertyFormatter" show-overflow-tooltip></el-table-column>
        <!-- 使用组织 -->
        <el-table-column align="center" prop="UseOrg" label="使用组织" show-overflow-tooltip></el-table-column>
        <!-- 分组 -->
        <el-table-column align="center" prop="Group" label="分组" show-overflow-tooltip></el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="custom-pagination">
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.limit" @pagination="getList" />
      </div>
    </div>
  </div>
</template>
<script>
import { getStockList } from '@/api/kingdee'
import { kingdee } from '@/minix'

export default {
  props: {
    isPopup: {
      type: Boolean,
      default: false
    },
    useOrg: {
      type: String,
      default: undefined
    },
    showUseOrg: {
      type: Boolean,
      default: true
    }
  },
  mixins: [kingdee],
  data() {
    return {
      // 搜索
      queryParams: {
        pageNum: 1, // 当前页码
        startRow: 0, // 开始行索引
        limit: 10, // 最大行数
        useOrg: undefined, // 使用组织
        number: undefined, // 仓库编码
        name: undefined // 参考名称
      },
      // 列表
      list: [],
      total: 0,
      loading: false,
      // 选中的
      selectedStock: {},
      // 是否显示使用组织
      isUseOrg: false,
      // 仓库属性
      stockPropertyOptions: [
        { value: '1', label: '普通仓库' },
        { value: '2', label: '车间仓库' },
        { value: '3', label: '供应商仓库' },
        { value: '4', label: '客户仓库' },
        { value: '5', label: '第三方仓储' }
      ]
    }
  },
  created() {
    this.queryParams.useOrg = this.useOrg || this.ApplicationOrgId[0].value || undefined
    // 获取列表
    if (!this.isPopup) this.getList()
  },
  methods: {
    // 仓库属性格式化
    stockPropertyFormatter(row) {
      return this.stockPropertyOptions.find(item => item.value == row.StockProperty)?.label || '--'
    },
    // 列表
    getList() {
      this.loading = true
      this.queryParams.startRow = Math.max(0, (this.queryParams.pageNum - 1) * this.queryParams.limit)
      const query = { ...this.queryParams }
      delete query.pageNum
      getStockList(query).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          const { data: list, total } = data
          this.list = list
          this.total = total
        } else this.$message.error(msg)
        this.loading = false
      })
    },
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      if (this.isPopup && !this.showUseOrg) this.queryParams.useOrg = this.useOrg || this.ApplicationOrgId[0].value || undefined
      this.getList()
    },
    // 重置搜索
    handleResetQuery(isUseOrg = false, selectedStock = {}) {
      this.resetForm('queryForm')
      if (this.isPopup) this.selectedStock = selectedStock
      if (!this.isPopup && this.showUseOrg) this.isUseOrg = isUseOrg
      this.handleQuery()
    },
    // 行点击
    handleRowClick(row) {
      this.selectedStock = row
      if (this.isPopup) this.$emit('selectStock', row)
    }
  }
}
</script>
<style scoped lang="scss">
@import '@/assets/styles/custom-new.scss';
</style>
