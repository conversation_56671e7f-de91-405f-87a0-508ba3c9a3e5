<template>
  <div>
    <el-dialog v-dialogDragBox title="应付单详情" :visible.sync="open" width="90%" class="custom-dialog" :before-close="beforeClose">
      <div style="padding: 0 20px">
        <div class="kindeeButton" :key="info.DOCUMENTSTATUS">
          <template v-for="(btn, index) in buttons">
            <el-button :key="index" :type="btn.type" size="medium" v-if="checkStatus(btn.status)" @click="handleKingdeeDo(btn.action)">{{ btn.text }}</el-button>
            <el-button :key="'disabled-' + index" :type="btn.type" size="medium" disabled v-else>{{ btn.text }}</el-button>
          </template>
        </div>
        <el-descriptions border :column="4" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 4 - 7em - 22px)' }">
          <template slot="title">
            <div class="customTitle">基本信息</div>
          </template>
          <!-- 单据类型 -->
          <el-descriptions-item label="单据类型">{{ info.BillTypeID && getString(info.BillTypeID.Name, 1) }}</el-descriptions-item>
          <!-- 单据编号 -->
          <el-descriptions-item label="单据编号">{{ info.BillNo }}</el-descriptions-item>
          <!-- 业务日期 -->
          <el-descriptions-item label="业务日期">{{ parseTime(info.DATE, '{y}-{m}-{d}') }}</el-descriptions-item>
          <!-- 到期日 -->
          <el-descriptions-item label="到期日">{{ parseTime(info.FENDDATE_H, '{y}-{m}-{d}') }}</el-descriptions-item>
          <!-- 单据状态 -->
          <el-descriptions-item label="单据状态">{{ getDocumentStatusLabel(info.DOCUMENTSTATUS) }}</el-descriptions-item>
          <!-- 立账类型 -->
          <el-descriptions-item label="立账类型">{{ info.FSetAccountType && getOptionLabel(FSetAccountTypeOptions, info.FSetAccountType) }}</el-descriptions-item>
          <!-- 按费用项目生成计划 -->
          <el-descriptions-item label="按费用项目生成计划">
            <el-checkbox v-model="info.FIsGeneratePlanByCostItem" disabled></el-checkbox>
          </el-descriptions-item>
          <!-- 供应商 -->
          <el-descriptions-item label="供应商">{{ info.SUPPLIERID && getString(info.SUPPLIERID.Name) }}</el-descriptions-item>
          <!-- 币别 -->
          <el-descriptions-item label="币别">{{ info.CURRENCYID && getString(info.CURRENCYID.Name) }}</el-descriptions-item>
          <!-- 价税合计 -->
          <el-descriptions-item label="价税合计">{{ info.FALLAMOUNTFOR }}</el-descriptions-item>
          <!-- 付款条件 -->
          <el-descriptions-item label="付款条件">{{ info.PayConditon && getString(info.PayConditon.Name) }}</el-descriptions-item>
          <!-- 整单折扣金额 -->
          <el-descriptions-item label="整单折扣金额">{{ info.FOrderDiscountAmountFor }}</el-descriptions-item>
          <!-- 按含税单价录入 -->
          <el-descriptions-item label="按含税单价录入">
            <el-checkbox v-model="info.ISTAX" disabled></el-checkbox>
          </el-descriptions-item>
          <!-- 付款组织 -->
          <el-descriptions-item label="付款组织">{{ info.FPAYORGID && getString(info.FPAYORGID.Name) }}</el-descriptions-item>
          <!-- 结算组织 -->
          <el-descriptions-item label="结算组织">{{ info.SETTLEORGID && getString(info.SETTLEORGID.Name) }}</el-descriptions-item>
          <!-- 采购组织 -->
          <el-descriptions-item label="采购组织">{{ info.PURCHASEORGID && getString(info.PURCHASEORGID.Name) }}</el-descriptions-item>
          <!-- 采购部门 -->
          <el-descriptions-item label="采购部门">{{ info.PURCHASEDEPTID && getString(info.PURCHASEDEPTID.Name) }}</el-descriptions-item>
          <!-- 采购员 -->
          <el-descriptions-item label="采购员">{{ info.PURCHASERID && getString(info.PURCHASERID.Name) }}</el-descriptions-item>
          <!-- 待开票客户 -->
          <el-descriptions-item label="待开票客户">{{ info.F_SCMJ_Base1 && getString(info.F_SCMJ_Base1.Name) }}</el-descriptions-item>
          <!-- 销售员 -->
          <el-descriptions-item label="销售员">{{ info.F_SCMJ_Base && getString(info.F_SCMJ_Base.Name) }}</el-descriptions-item>
          <!-- 订单销售组织 -->
          <el-descriptions-item label="订单销售组织">{{ info.F_SCMJ_DDXSZZ && getString(info.F_SCMJ_DDXSZZ.Name) }}</el-descriptions-item>
          <!-- 订单销售部门 -->
          <el-descriptions-item label="订单销售部门">{{ info.F_SCMJ_DDXSBM && getString(info.F_SCMJ_DDXSBM.Name) }}</el-descriptions-item>
          <!-- 订单销售员 -->
          <el-descriptions-item label="订单销售员">{{ info.F_SCMJ_DDXSY && getString(info.F_SCMJ_DDXSY.Name) }}</el-descriptions-item>
          <!-- 销售订单号 -->
          <el-descriptions-item label="销售订单号">{{ info.F_SCMJ_XSDDH }}</el-descriptions-item>
          <!-- 备注 -->
          <el-descriptions-item label="备注">{{ info.REMARK }}</el-descriptions-item>
          <!-- 参与暂估应付核销 -->
          <el-descriptions-item label="参与暂估应付核销">
            <el-checkbox v-model="info.FISHookMatch" disabled></el-checkbox>
          </el-descriptions-item>
        </el-descriptions>
        <div class="customTitle">明细信息</div>
        <el-table ref="detailTable" :data="info.AP_PAYABLEENTRY" class="custom-table" highlight-current-row @current-change="handleCurrentChange" :setCurrentRow="setCurrentRow">
          <!-- 序号 -->
          <el-table-column type="index" label="序号" align="center"></el-table-column>
          <!-- 费用项目编码 -->
          <el-table-column align="center" prop="FCOSTID" label="费用项目编码" show-overflow-tooltip min-width="100px">
            <template slot-scope="{ row }">{{ row.FCOSTID && row.FCOSTID.Number }}</template>
          </el-table-column>
          <!-- 物料名称 -->
          <el-table-column align="center" prop="MATERIALID" label="物料名称" show-overflow-tooltip>
            <template slot-scope="{ row }">{{ row.MATERIALID && getString(row.MATERIALID.Name) }}</template>
          </el-table-column>
          <!-- 计价单位 -->
          <el-table-column align="center" prop="PRICEUNITID" label="计价单位" show-overflow-tooltip>
            <template slot-scope="{ row }">{{ row.PRICEUNITID && getString(row.PRICEUNITID.Name) }}</template>
          </el-table-column>
          <!-- 单价 -->
          <el-table-column align="center" prop="FPrice" label="单价" show-overflow-tooltip></el-table-column>
          <!-- 计价数量 -->
          <el-table-column align="center" prop="PriceQty" label="计价数量" show-overflow-tooltip></el-table-column>
          <!-- 费用项目名称 -->
          <el-table-column align="center" prop="FCOSTDEPARTMENTID" label="费用项目名称" show-overflow-tooltip min-width="100px">
            <template slot-scope="{ row }">{{ row.FCOSTDEPARTMENTID && getString(row.FCOSTDEPARTMENTID.Name) }}</template>
          </el-table-column>
          <!-- 含税单价 -->
          <el-table-column align="center" prop="TaxPrice" label="含税单价" show-overflow-tooltip></el-table-column>
          <!-- 税率 -->
          <el-table-column align="center" prop="EntryTaxRate" label="税率(%)" show-overflow-tooltip></el-table-column>
          <!-- 备注 -->
          <el-table-column align="center" prop="Comment" label="备注" show-overflow-tooltip></el-table-column>
          <!-- 折扣率 -->
          <el-table-column align="center" prop="EntryDiscountRate" label="折扣率(%)" show-overflow-tooltip></el-table-column>
          <!-- 折扣额 -->
          <el-table-column align="center" prop="FDISCOUNTAMOUNTFOR" label="折扣额" show-overflow-tooltip></el-table-column>
          <!-- 不含税金额 -->
          <el-table-column align="center" prop="FNoTaxAmountFor_D" label="不含税金额" show-overflow-tooltip></el-table-column>
          <!-- 税额 -->
          <el-table-column align="center" prop="FTAXAMOUNTFOR_D" label="税额" show-overflow-tooltip></el-table-column>
          <!-- 价税合计 -->
          <el-table-column align="center" prop="FALLAMOUNTFOR_D" label="价税合计" show-overflow-tooltip></el-table-column>
          <!-- 批号 -->
          <el-table-column align="center" prop="FLOT" label="批号" show-overflow-tooltip>
            <template slot-scope="{ row }">{{ row.FLOT && row.FLOT.Number }}</template>
          </el-table-column>
          <!-- 仓库 -->
          <el-table-column align="center" prop="F_SCMJ_Base2" label="仓库" show-overflow-tooltip>
            <template slot-scope="{ row }">{{ row.F_SCMJ_Base2 && getString(row.F_SCMJ_Base2.Name) }}</template>
          </el-table-column>
          <!-- 辅助数量 -->
          <el-table-column align="center" prop="F_SCMJ_FZSLkg" label="辅助数量(kg)" show-overflow-tooltip min-width="100px"></el-table-column>
          <!-- 计入采购成本 -->
          <el-table-column align="center" prop="INCLUDECOST" label="计入采购成本" show-overflow-tooltip min-width="100px">
            <template slot-scope="{ row }">
              <el-checkbox v-model="row.INCLUDECOST" disabled></el-checkbox>
            </template>
          </el-table-column>
          <!-- 入库单号 -->
          <el-table-column align="center" prop="INSTOCKID" label="入库单号" show-overflow-tooltip></el-table-column>
          <!-- 计入合同履约成本 -->
          <el-table-column align="center" prop="INCLUDECONTRACTCOMPCOST" label="计入合同履约成本" show-overflow-tooltip min-width="120px">
            <template slot-scope="{ row }">
              <el-checkbox v-model="row.INCLUDECONTRACTCOMPCOST" disabled></el-checkbox>
            </template>
          </el-table-column>
          <!-- 出库单号 -->
          <el-table-column align="center" prop="OUTSTOCKID" label="出库单号" show-overflow-tooltip></el-table-column>
          <!-- 费用承担部门 -->
          <el-table-column align="center" prop="FCOSTDEPARTMENTID" label="费用承担部门" show-overflow-tooltip min-width="100px">
            <template slot-scope="{ row }">{{ row.FCOSTDEPARTMENTID && getString(row.FCOSTDEPARTMENTID.Name) }}</template>
          </el-table-column>
          <!-- 费用承担业务员 -->
          <el-table-column align="center" prop="F_SCMJ_Base_qtr" label="费用承担业务员" show-overflow-tooltip min-width="110px">
            <template slot-scope="{ row }">{{ row.F_SCMJ_Base_qtr && getString(row.F_SCMJ_Base_qtr.Name) }}</template>
          </el-table-column>
          <!-- 项目客户 -->
          <el-table-column align="center" prop="F_SCMJ_Base_83g" label="项目客户" show-overflow-tooltip>
            <template slot-scope="{ row }">{{ row.F_SCMJ_Base_83g && getString(row.F_SCMJ_Base_83g.Name) }}</template>
          </el-table-column>
        </el-table>
        <div class="customTitle">付款计划</div>
        <el-table ref="plainTable" :data="info.AP_PAYABLEPLAN" class="custom-table" highlight-current-row>
          <!-- 序号 -->
          <el-table-column type="index" label="序号" align="center"></el-table-column>
          <!-- 到期日 -->
          <el-table-column align="center" prop="ENDDATE" label="到期日" show-overflow-tooltip>
            <template slot-scope="{ row }">{{ parseTime(row.ENDDATE, '{y}-{m}-{d}') }}</template>
          </el-table-column>
          <!-- 应付金额 -->
          <el-table-column align="center" prop="PAYAMOUNTFOR" label="应付金额" show-overflow-tooltip></el-table-column>
          <!-- 应付比例(%) -->
          <el-table-column align="center" prop="FPAYRATE" label="应付比例(%)" show-overflow-tooltip></el-table-column>
          <!-- 应付金额本位币 -->
          <el-table-column align="center" prop="PAYAMOUNT" label="应付金额本位币" show-overflow-tooltip></el-table-column>
          <!-- 物料编码 -->
          <el-table-column align="center" prop="FMATERIALID_P" label="物料编码" show-overflow-tooltip>
            <template slot-scope="{ row }">{{ row.FMATERIALID_P && row.FMATERIALID_P.Number }}</template>
          </el-table-column>
          <!-- 物料名称 -->
          <el-table-column align="center" prop="FMATERIALID_P" label="物料名称" show-overflow-tooltip>
            <template slot-scope="{ row }">{{ row.FMATERIALID_P && getString(row.FMATERIALID_P.Name) }}</template>
          </el-table-column>
          <!-- 采购订单号 -->
          <el-table-column align="center" prop="FPURCHASEORDERNO" label="采购订单号" show-overflow-tooltip></el-table-column>
          <!-- 订单行号 -->
          <el-table-column align="center" prop="FMATERIALSEQ" label="订单行号" show-overflow-tooltip>
            <template slot-scope="{ row }">{{ row.FMATERIALSEQ || ' ' }}</template>
          </el-table-column>
          <!-- 付(退)款关联金额本位币 -->
          <el-table-column align="center" prop="FWRITTENOFFAMOUNT" label="付(退)款关联金额本位币" show-overflow-tooltip>
            <template slot-scope="{ row }">{{ row.FWRITTENOFFAMOUNT || ' ' }}</template>
          </el-table-column>
          <!-- 备注 -->
          <el-table-column align="center" prop="FREMARK" label="备注" show-overflow-tooltip></el-table-column>
        </el-table>
      </div>
    </el-dialog>
    <!-- 下推弹出框 -->
    <el-dialog v-dialogDragBox title="选择单据" :visible.sync="pushOpen" width="580px" class="custom-dialog">
      <div style="padding: 0 20px">
        <el-row :gutter="10" class="custom-push-target">
          <el-radio-group v-model="pushForm.target" v-removeAriaHidden>
            <el-col :span="12" v-for="item in pushTarget" :key="item.value">
              <el-radio :label="item.value">{{ item.label }}</el-radio>
            </el-col>
          </el-radio-group>
        </el-row>
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn small" @click="pushOpen = false">取消</el-button>
        <el-button class="custom-dialog-btn small primary" :class="{ disabled: !pushForm.target }" :disabled="!pushForm.target" @click="handlePushSubmit">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { getPayablesDetail, auditPayables, revokePayables, deletePayables, pushPayables, submitPayables, unAuditPayables } from '@/api/kingdee/payables'
import { kingdee } from '@/minix'

export default {
  mixins: [kingdee],
  data() {
    return {
      open: false,
      info: {},
      setCurrentRow: {},
      // 立账类型
      FSetAccountTypeOptions: [
        { label: '业务应付', value: '1' },
        { label: '暂估应付', value: '2' },
        { label: '财务应付', value: '3' }
      ],
      // 下推
      pushOpen: false,
      pushTarget: [],
      pushForm: {
        number: undefined,
        target: undefined
      },
      buttons: [
        { text: '提交', type: 'primary', action: 'submit', status: ['A', 'D'] },
        { text: '审核', type: 'warning', action: 'audit', status: ['B'] },
        { text: '撤销', type: 'info', action: 'revoke', status: ['B'] },
        { text: '删除', type: 'danger', action: 'delete', status: ['A', 'D'] },
        { text: '反审', type: 'warning', action: 'unAudit', status: ['C'] }
      ]
    }
  },
  computed: {
    // 是否发货检验
    IsReturnCheck() {
      return this.setCurrentRow?.IsReturnCheck || false
    }
  },
  methods: {
    // 检查状态
    checkStatus(allowedStatus) {
      return allowedStatus.includes(this.info.DOCUMENTSTATUS)
    },
    // 获取详情
    getInfo(row = {}) {
      this.info = {}
      if (!row.BillNo) {
        this.$message.error('参数错误，请重新选择')
        return
      }
      this.open = true
      getPayablesDetail({ billNo: row.BillNo }).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          const { result } = data
          this.open = !!result?.result
          this.info = result?.result || {}
          this.$nextTick(() => {
            this.setCurrentRow = result?.result?.AP_PAYABLEENTRY?.[0] || {}
            if (this.$refs.detailTable) {
              this.$refs.detailTable.setCurrentRow(this.setCurrentRow)
              this.$refs.detailTable.bodyWrapper.scrollLeft = 0
            }
          })
        } else this.$message.error(msg)
      })
    },
    // 当前行
    handleCurrentChange(val) {
      this.setCurrentRow = val
    },
    // 关闭前
    beforeClose() {
      this.handleClose()
      this.$emit('update', { billNo: this.info.BillNo, newStatus: this.info.DocumentStatus })
    },
    // 关闭
    handleClose(flag = false) {
      this.open = false
      this.$emit('callBack', flag)
    },
    // 金蝶操作
    // prettier-ignore
    handleKingdeeDo(type) {
      const number = this.info.BillNo
      if (!number) return
      switch (type) {
        case 'submit':
          // 提交
          this.$modal.confirm('确认要提交该应付单吗？').then(() => {
            submitPayables({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('提交成功')
                this.$set(this.info, 'DOCUMENTSTATUS', 'B')
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'audit':
          // 审核
          this.$modal.confirm('确认要审核该应付单吗？').then(() => {
            auditPayables({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('审核成功')
                this.$set(this.info, 'DOCUMENTSTATUS', 'C')
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'revoke':
          // 撤销
          this.$modal.confirm('确认要撤销该应付单吗？').then(() => {
            revokePayables({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('撤销成功')
                this.$set(this.info, 'DOCUMENTSTATUS', 'D')
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'unAudit':
          // 反审核
          this.$modal.confirm('确认要反审核该应付单吗？').then(() => {
            unAuditPayables({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('反审核成功')
                this.$set(this.info, 'DOCUMENTSTATUS', 'D')
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'delete':
          // 删除
          this.$modal.confirm('确认要删除该应付单吗？').then(() => {
            deletePayables({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('删除成功')
                this.handleClose(true)
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'push':
          // 下推
          this.pushForm = {
            number: this.info.BillNo,
            target: undefined
          }
          this.pushOpen = true
          break
      }
    },
    // 下推提交
    handlePushSubmit() {
      const { number, target } = this.pushForm
      if (!number || !target) return
      pushPayables({ number, target }).then(res => {
        const { code, msg } = res
        if (code === 200) {
          this.$message.success('下推成功')
          this.pushOpen = false
          this.handleClose(true)
        } else if (code === 400) {
          this.$alert(msg || '操作失败，请重试或使用金蝶进行操作！', '提示', {
            type: 'info',
            confirmButtonText: '确定',
            callback: action => {
              this.pushOpen = false
            }
          })
        } else this.$message.error(msg)
      })
    }
  }
}
</script>
<style scoped lang="scss">
@import '@/assets/styles/custom-new.scss';
::v-deep {
  .el-descriptions__header {
    margin-bottom: 0;
  }
  .el-table.custom-table .el-table__body-wrapper .el-table__row:hover td.el-table__cell {
    background-color: #eaf1fe !important;
  }
}
.customTitle {
  font-size: 16px;
  font-weight: normal;
  line-height: 60px;
  color: $font;
}
.kindeeButton {
  display: flex;
  align-items: center;
}
.custom-push-target {
  .el-col.el-col-12 {
    .el-radio {
      height: 30px;
      display: flex;
      align-items: center;
    }
  }
}
</style>
