<template>
  <div>
    <el-dialog v-dialogDragBox :title="title" :visible.sync="open" width="90%" class="custom-dialog" :before-close="beforeClose" append-to-body>
      <div style="padding: 0 20px">
        <el-form :model="form" :rules="rules" ref="form" label-width="6em" class="custom-form">
          <el-row :gutter="20" style="display: flex; flex-wrap: wrap">
            <!-- 单据类型 -->
            <el-col :span="6">
              <el-form-item label="单据类型" prop="fbilltypeid">
                <el-select v-model="form.fbilltypeid" filterable placeholder="请选择单据类型" class="full-width">
                  <el-option v-for="item in billTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 业务日期 -->
            <el-col :span="6">
              <el-form-item label="业务日期" prop="fdate">
                <el-date-picker v-model="form.fdate" type="date" placeholder="请选择业务日期" class="full-width"></el-date-picker>
              </el-form-item>
            </el-col>
            <!-- 到期日 -->
            <el-col :span="6">
              <el-form-item label="到期日" prop="fenddateh">
                <el-date-picker v-model="form.fenddateh" type="date" placeholder="请选择到期日" class="full-width"></el-date-picker>
              </el-form-item>
            </el-col>
            <!-- 供应商 -->
            <el-col :span="6">
              <el-form-item label="供应商" prop="fsuppliername">
                <supplier-search-select :keyword.sync="form.fsuppliername" :useOrg="form.fstockorgid" :showLabel="false" isBack @callBack="handleSupplierSelect($event)" class="full-width" />
              </el-form-item>
            </el-col>
            <!-- 币别 -->
            <el-col :span="6">
              <el-form-item label="币别" prop="fcurrencyid">
                <el-select v-model="form.fcurrencyid" filterable placeholder="请选择币别" class="full-width">
                  <el-option v-for="item in CurrencyId" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 付款条件 -->
            <el-col :span="6">
              <el-form-item label="付款条件" prop="fpayconditon">
                <el-select v-model="form.fpayconditon" filterable placeholder="请选择付款条件" class="full-width">
                  <el-option v-for="item in payconditonOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 付款组织 -->
            <el-col :span="6">
              <el-form-item label="付款组织" prop="fpayorgid">
                <el-select v-model="form.fpayorgid" filterable placeholder="请选择付款组织" class="full-width">
                  <el-option v-for="item in ApplicationOrgId" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 结算组织 -->
            <el-col :span="6">
              <el-form-item label="结算组织" prop="fsettleorgid">
                <el-select v-model="form.fsettleorgid" filterable placeholder="请选择结算组织" class="full-width" @change="handleChangeSettleOrg">
                  <el-option v-for="item in ApplicationOrgId" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 采购组织 -->
            <el-col :span="6">
              <el-form-item label="采购组织" prop="fpurchaseorgid">
                <el-select v-model="form.fpurchaseorgid" filterable placeholder="请选择采购组织" class="full-width" @change="handleChangeSaleOrg">
                  <el-option v-for="item in ApplicationOrgId" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 采购部门 -->
            <el-col :span="6">
              <el-form-item label="采购部门" prop="fpurchasedeptid">
                <el-select v-model="form.fpurchasedeptid" filterable placeholder="请选择采购部门" class="full-width" @change="handleChangeSaleDept">
                  <el-option v-for="item in purchaseDeptList" :key="item.FNumber" :label="item.FName" :value="item.FNumber"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 采购员 -->
            <el-col :span="6">
              <el-form-item label="采购员" prop="fpurchaserid">
                <el-select v-model="form.fpurchaserid" filterable placeholder="请选择采购员" class="full-width">
                  <el-option v-for="item in calculateApplicantList" :key="item.Number" :label="item.Name" :value="item.Number"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 备注 -->
            <el-col :span="6">
              <el-form-item label="备注" prop="fapremark">
                <el-input v-model="form.fapremark" placeholder="请输入备注" class="full-width"></el-input>
              </el-form-item>
            </el-col>
            <!-- 按含税单价录入 -->
            <el-col :span="6">
              <el-form-item label="按含税单价录入" prop="fistax">
                <el-checkbox v-model="form.fistax" @change="handleChangeIsTaxPriceInput">按含税单价录入</el-checkbox>
              </el-form-item>
            </el-col>
            <!-- 结算方式 -->
            <el-col :span="6">
              <el-form-item label="结算方式" prop="fsettletypeid">
                <el-select v-model="form.fsettletypeid" filterable placeholder="请选择结算方式" class="full-width">
                  <el-option v-for="item in settletypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 到期日计算日期 -->
            <el-col :span="6">
              <el-form-item label="到期日计算日期" prop="faccnttimejudgetime">
                <el-date-picker v-model="form.faccnttimejudgetime" type="date" placeholder="请选择到期日计算日期" class="full-width"></el-date-picker>
              </el-form-item>
            </el-col>
            <!-- 明细信息表格 -->
            <el-col :span="24">
              <el-table :data="form.entities" class="full-width custom-table custom-table-cell0" stripe show-summary :summary-method="getSummary">
                <!-- 序号 -->
                <el-table-column label="序号" type="index" width="50"></el-table-column>
                <!-- 物料编码 -->
                <el-table-column label="物料编码" prop="fmaterialid" align="center" show-overflow-tooltip>
                  <template slot-scope="{ row }">
                    <span class="table-link" @click="handleMaterialNumber(row.fmaterialid)">{{ row.fmaterialid }}</span>
                  </template>
                </el-table-column>
                <!-- 物料名称 -->
                <el-table-column label="物料名称" prop="fmaterialname" align="center" show-overflow-tooltip></el-table-column>
                <!-- 计价单位 -->
                <el-table-column label="计价单位" prop="fpriceunitid" align="center" min-width="100">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.fpriceunitid`" :rules="rules.fpriceunitid">
                      <el-select v-model="scope.row.fpriceunitid" filterable placeholder="请选择计价单位" class="full-width" size="small">
                        <el-option v-for="item in UnitList" :key="item.FNumber" :label="item.FName" :value="item.FNumber"></el-option>
                      </el-select>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 单价 -->
                <el-table-column label="单价" prop="fprice" align="center">
                  <template slot-scope="scope">
                    <template v-if="form.fistax">{{ scope.row.fprice }}</template>
                    <template v-else>
                      <template v-if="!scope.row.fisfree">
                        <el-form-item label-width="0" :prop="`entities.${scope.$index}.fprice`" :rules="rules.fprice">
                          <el-input v-model.number="scope.row.fprice" placeholder="请输入单价" size="small" @change="handleChangePrice(scope.row)"></el-input>
                        </el-form-item>
                      </template>
                    </template>
                  </template>
                </el-table-column>
                <!-- 计价数量 -->
                <el-table-column label="计价数量" prop="fpriceqty" align="center" min-width="100">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.fpriceqty`" :rules="rules.fpriceqty">
                      <el-input v-model.number="scope.row.fpriceqty" placeholder="请输入数量" size="small" @change="handleChangePriceQty(scope.row)"></el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 含税单价 -->
                <el-table-column label="含税单价" prop="ftaxprice" align="center" min-width="100">
                  <template slot-scope="scope">
                    <template v-if="!form.fistax">{{ scope.row.ftaxprice }}</template>
                    <template v-else>
                      <template v-if="!scope.row.fisfree">
                        <el-form-item label-width="0" :prop="`entities.${scope.$index}.ftaxprice`" :rules="rules.ftaxprice">
                          <el-input v-model.number="scope.row.ftaxprice" placeholder="请输入含税单价" size="small" @change="handleChangeTaxPrice(scope.row)"></el-input>
                        </el-form-item>
                      </template>
                    </template>
                  </template>
                </el-table-column>
                <!-- 税率 -->
                <el-table-column label="税率" prop="fentrytaxrate" align="center" min-width="100">
                  <template slot-scope="scope">
                    <template v-if="!scope.row.fisfree">
                      <el-form-item label-width="0" :prop="`entities.${scope.$index}.fentrytaxrate`" :rules="rules.fentrytaxrate">
                        <el-input v-model.number="scope.row.fentrytaxrate" placeholder="税率" size="small" @change="handleChangeEntryTaxRate(scope.row)">
                          <template slot="suffix">%</template>
                        </el-input>
                      </el-form-item>
                    </template>
                  </template>
                </el-table-column>
                <!-- 折扣率 -->
                <el-table-column label="折扣率" prop="fentrydiscountrate" align="center" min-width="100">
                  <template slot-scope="scope">
                    <template v-if="!scope.row.fisfree">
                      <el-form-item label-width="0" :prop="`entities.${scope.$index}.fentrydiscountrate`" :rules="rules.fentrydiscountrate">
                        <el-input v-model.number="scope.row.fentrydiscountrate" placeholder="折扣率" size="small" @change="handleChangeEntryDiscountRate(scope.row)">
                          <template slot="suffix">%</template>
                        </el-input>
                      </el-form-item>
                    </template>
                  </template>
                </el-table-column>
                <!-- 折扣额 -->
                <el-table-column label="折扣额" prop="fdiscountamount" align="center" min-width="100"></el-table-column>
                <!-- 不含税金额 -->
                <el-table-column label="不含税金额" prop="fnotaxamountford" align="center" min-width="100">
                  <template slot-scope="scope">
                    <template v-if="!scope.row.fisfree">
                      <el-form-item label-width="0" :prop="`entities.${scope.$index}.fnotaxamountford`" :rules="rules.fnotaxamountford">
                        <el-input v-model.number="scope.row.fnotaxamountford" placeholder="不含税金额" size="small" @change="handleChangeNotaxAmountFor(scope.row)"></el-input>
                      </el-form-item>
                    </template>
                  </template>
                </el-table-column>
                <!-- 税额 -->
                <el-table-column label="税额" prop="ftaxamountford" align="center" min-width="100">
                  <template slot-scope="scope">
                    <template v-if="!scope.row.fisfree">
                      <el-form-item label-width="0" :prop="`entities.${scope.$index}.ftaxamountford`" :rules="rules.ftaxamountford">
                        <el-input v-model.number="scope.row.ftaxamountford" placeholder="税额" size="small" @change="handleChangeTaxAmountFor(scope.row)"></el-input>
                      </el-form-item>
                    </template>
                  </template>
                </el-table-column>
                <!-- 价税合计 -->
                <el-table-column label="价税合计" prop="ftaxamount" align="center" min-width="100"></el-table-column>
                <!-- 批号 -->
                <el-table-column label="批号" prop="flot" align="center" min-width="100">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.flot`" :rules="rules.flot">
                      <el-input v-model="scope.row.flot" placeholder="请输入批号" size="small"></el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 是否赠品 -->
                <el-table-column label="是否赠品" prop="fisfree" align="center" min-width="80">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.fisfree`" :rules="rules.fisfree">
                      <el-checkbox v-model="scope.row.fisfree" @change="handleChangeIsFree(scope.row)"></el-checkbox>
                    </el-form-item>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn" @click="handleClose()">取消</el-button>
        <el-button class="custom-dialog-btn primary" :loading="loading" @click="handleSubmit">确定</el-button>
      </div>
    </el-dialog>
    <!-- 应付单详情 -->
    <payables-detail ref="payablesDetail" />
  </div>
</template>
<script>
import { kingdee } from '@/minix'
import { isNumber, isNumberLength } from '@/utils/validate'
import StockSearchSelect from '@/components/SearchSelect/stock'
import SupplierSearchSelect from '@/components/SearchSelect/supplier'
import { getSalesmanList } from '@/api/kingdee'
import { getPayablesDetail2, savePayables, deletePayablesV2 } from '@/api/kingdee/payables'
import PayablesDetail from '@/views/kingdee/payables/detail'

export default {
  name: 'PayablesCreate',
  mixins: [kingdee],
  components: { StockSearchSelect, SupplierSearchSelect, PayablesDetail },
  data() {
    return {
      fid: undefined,
      form: {},
      rules: {
        // 业务日期
        fdate: [{ required: true, message: '请选择业务日期', trigger: 'change' }],
        // 结算组织
        fsettleorgid: [{ required: true, message: '请选择结算组织', trigger: 'change' }],
        // 币别
        fcurrencyid: [{ required: true, message: '请选择币别', trigger: 'change' }],
        // 单据类型
        fbilltypeid: [{ required: true, message: '请选择单据类型', trigger: 'change' }],
        // 到期日
        fenddateh: [{ required: true, message: '请选择到期日', trigger: 'change' }],
        // 供应商
        fsuppliername: [{ required: true, message: '请选择供应商', trigger: 'change' }],
        // 付款组织
        fpayorgid: [{ required: true, message: '请选择付款组织', trigger: 'change' }],
        fpriceqty: [
          { validator: isNumber, message: '请输入正确的实收数量', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: 'blur' }
        ],
        ftaxprice: [
          { validator: isNumber, message: '请输入正确的含税单价', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: 'blur' }
        ],
        fentrytaxrate: [
          { validator: isNumber, message: '请输入正确的税率', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: 'blur' }
        ],
        fentrydiscountrate: [
          { validator: isNumber, message: '请输入正确的折扣率', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: 'blur' }
        ],
        fnotaxamountford: [
          { validator: isNumber, message: '请输入正确的不含税金额', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: 'blur' }
        ],
        ftaxamountford: [
          { validator: isNumber, message: '请输入正确的税额', trigger: 'blur' },
          { validator: isNumberLength.bind(this, 5), message: '只可以填写五位小数', trigger: 'blur' }
        ]
      },
      title: '新增应付单',
      open: false,
      loading: false,
      // 单据类型
      billTypeOptions: [
        { value: 'YFD01_SYS', label: '标准应付单' },
        { value: 'YFD02_SYS', label: '费用应付单' },
        { value: 'YFD03_SYS', label: '资产调拨应付' },
        { value: 'YFD04_SYS', label: '转销应付单' }
      ],
      // 付款条件
      payconditonOptions: [
        { value: '001', label: '100%付款' },
        { value: 'FKTJ01_SYS', label: '货到付款' },
        { value: 'FKTJ02_SYS', label: '30天后付款' },
        { value: 'FKTJ03_SYS', label: '月结30天' },
        { value: 'FKTJ04_SYS', label: '多到期日(按金额)' }
      ],
      // 结算方式
      settletypeOptions: [
        { value: 'JSFS01_SYS', label: '现金' },
        { value: 'JSFS04_SYS', label: '对公' },
        { value: 'JSFS21_SYS', label: '集中结算' },
        { value: 'JSFS23_SYS', label: '对私' },
        { value: 'JSFS24_SYS', label: '票据' },
        { value: 'JSFS25_SYS', label: '转收分厂进项税' }
      ],
      applicantList: [], // 采购员
      isPush: false,
      hasSuccessfully: false // 是否已成功提交
    }
  },
  computed: {
    // 过滤后的部门列表 - 采购部门
    purchaseDeptList() {
      return this.ApplicationDeptId.filter(item => item.FUseOrg === this.form.fpurchaseorgid)
    },
    // 采购员列表
    calculateApplicantList() {
      return this.getProcessedEmployeeList(this.applicantList, this.form.fpurchasedeptid)
    }
  },
  methods: {
    // 通用的员工列表处理方法
    getProcessedEmployeeList(employeeList, deptId) {
      if (!deptId) return []
      const dept = this.ApplicationDeptId.find(item => item.FNumber === deptId) || {}
      const deptName = dept.FName || ''
      const filteredList = employeeList.filter(item => item.FDept === deptName)
      // 处理员工编号，只取前三位数字
      return filteredList.map(item => ({
        ...item,
        Number: item.Number ? item.Number.split('_')[0] : item.Number
      }))
    },
    // 获取员工列表的通用方法
    async fetchEmployeeList(orgId, operatorType) {
      if (!orgId) return []
      try {
        const params = { bizOrg: orgId, OperatorType: operatorType }
        const res = await getSalesmanList(params)
        const { code, msg, data } = res

        if (code === 200) {
          return data?.data || []
        } else {
          this.$message.error(msg)
          return []
        }
      } catch (error) {
        console.error(`获取${operatorType}列表失败:`, error)
        return []
      }
    },
    // 采购员远程方法
    async ApplicantRemoteMethod() {
      this.applicantList = await this.fetchEmployeeList(this.form.fpurchaseorgid, 'CGY')
    },
    // 关闭弹窗前的处理
    beforeClose() {
      this.handleClose()
    },
    // 关闭弹窗
    async handleClose(flag = false) {
      if (this.fid && !this.hasSuccessfully) {
        try {
          await deletePayablesV2({ id: this.fid })
          this.fid = undefined
          this.$nextTick(() => {
            this.open = false
            this.$emit('callBack', flag)
          })
        } catch (error) {
          this.$message.error(error.message)
        }
      } else {
        this.open = false
        this.$emit('callBack', flag)
      }
    },
    // 表单重置
    reset() {
      this.form = {
        entities: [], // 明细信息
        faccnttimejudgetime: undefined, // 到期日计算日期
        fapremark: undefined, // 备注
        fbilltypeid: undefined, // 单据类型
        fcurrencyid: undefined, // 币别
        fdate: undefined, // 业务日期
        fenddateh: undefined, // 到期日
        fid: undefined, // 主键
        fistax: false, // 按含税单价录入
        fpayconditon: undefined, // 付款条件
        fpayorgid: undefined, // 付款组织
        fpurchasedeptid: undefined, // 采购部门
        fpurchaserid: undefined, // 采购员
        fsettleorgid: undefined, // 结算组织
        fsettletypeid: undefined, // 结算方式
        fsupplierid: undefined, // 供应商
        fsuppliername: undefined // 供应商名称
      }
      this.resetForm('form')
      this.isPush = false
      this.hasSuccessfully = false
    },
    // 数据初始化
    async initPush(fid, type = undefined) {
      if (!fid) {
        this.$message.warning('参数错误，请刷新页面重试')
        return
      }
      const loading = this.$loading({
        lock: true,
        text: '加载中...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      try {
        this.reset()
        this.isPush = type == 'push'
        const res = await getPayablesDetail2({ fid })
        const { code, msg, data } = res
        if (code === 200) {
          this.processInitialData(data, fid)
          this.open = true
          // 异步加载员工列表
          this.$nextTick(() => {
            this.ApplicantRemoteMethod()
          })
        } else {
          this.$message.error(msg)
        }
      } catch (error) {
        console.error('初始化数据失败:', error)
        this.$message.error('加载数据失败，请重试')
      } finally {
        loading.close()
      }
    },
    // 处理初始化数据
    processInitialData(data, fid) {
      const info = data?.result?.result || {}
      const detailEntities = info?.AP_PAYABLEENTRY || []
      const oinfo = info?.AP_PAYABLEFIN?.[0] || {}
      // 处理明细数据
      const entities = detailEntities.map(item => ({
        fentryid: item?.Id || undefined, // 使用时需检查
        fmaterialid: item?.MATERIALID?.Number || undefined,
        fmaterialname: (item?.MATERIALID?.Name && this.getString(item?.MATERIALID?.Name)) || undefined,
        fpriceunitid: item?.PRICEUNITID?.Number || undefined, // 计价单位
        fprice: item?.FPrice || undefined, // 单价
        fpriceqty: item?.PriceQty || undefined, // 计价数量
        ftaxprice: item?.TaxPrice || undefined, // 含税单价
        fentrytaxrate: item?.EntryTaxRate || undefined, // 税率
        fentrydiscountrate: item?.EntryDiscountRate || undefined, // 折扣率
        fdiscountamount: item?.FDISCOUNTAMOUNTFOR || 0, // 折扣额
        fnotaxamountford: item?.FNoTaxAmountFor_D || undefined, // 不含税金额
        ftaxamountford: item?.FTAXAMOUNTFOR_D || undefined, // 税额
        ftaxamount: item?.FALLAMOUNTFOR_D || undefined, // 价税合计
        flot: item?.FLOT?.Number || undefined, // 批号
        fisfree: item?.IsFree || undefined // 是否赠品
      }))
      // 设置表单数据
      this.form = {
        entities, // 明细信息
        faccnttimejudgetime: oinfo?.ACCNTTIMEJUDGETIME || undefined, // 到期日计算日期
        fapremark: info?.REMARK || undefined, // 备注
        fbilltypeid: info?.BillTypeID?.Number || undefined, // 单据类型
        fcurrencyid: info?.CURRENCYID?.Number || undefined, // 币别
        fdate: info?.DATE || undefined, // 业务日期
        fenddateh: info?.FENDDATE_H || undefined, // 到期日
        fid, // 主键
        fistax: info?.ISTAX || false, // 按含税单价录入
        fpayconditon: info?.PayConditon?.Number || undefined, // 付款条件
        fpayorgid: info?.FPAYORGID?.Number || undefined, // 付款组织
        fpurchaseorgid: info?.PURCHASEORGID?.Number || undefined, // 采购组织
        fpurchasedeptid: info?.PURCHASEDEPTID?.Number || undefined, // 采购部门
        fpurchaserid: info?.PURCHASERID?.Number || undefined, // 采购员
        fsettleorgid: info?.SETTLEORGID?.Number || undefined, // 结算组织
        fsettletypeid: oinfo?.FSettleTypeID?.Number || undefined, // 结算方式
        fsupplierid: info?.SUPPLIERID?.Number || undefined, // 供应商
        fsuppliername: (info?.SUPPLIERID?.Name && this.getString(info?.SUPPLIERID?.Name)) || undefined // 供应商名称
      }
      this.fid = fid
      this.title = '新增应付单'
    },
    // 改变按含税单价录入
    handleChangeIsTaxPriceInput() {
      this.form.entities.forEach(item => {
        this.handleChangePrice(item)
        this.handleChangePriceQty(item)
        this.handleChangeTaxPrice(item)
        this.handleChangeEntryTaxRate(item)
        this.handleChangeEntryDiscountRate(item)
        this.handleChangeNotaxAmountFor(item)
        this.handleChangeIsFree(item)
      })
    },
    // 改变结算组织
    handleChangeSettleOrg(val) {
      this.$set(this.form, 'fpurchaseorgid', val)
      this.handleChangeSaleOrg()
    },
    // 改变采购组织
    handleChangeSaleOrg() {
      this.form.fpurchasedeptid = undefined
      this.form.fpurchaserid = undefined
      this.ApplicantRemoteMethod()
    },
    // 改变采购部门
    handleChangeSaleDept() {
      this.form.fpurchaserid = undefined
    },
    // 选择供应商
    handleSupplierSelect(data) {
      this.$set(this.form, 'fsupplierid', data.FNumber)
      this.$set(this.form, 'fsuppliername', data.FName)
    },
    toFixedLength(num, length = 6) {
      const n = parseFloat(num)
      return isNaN(n) ? 0 : parseFloat(n.toFixed(length))
    },
    // 改变单价
    handleChangePrice(row) {
      const { fprice = 0, fpriceqty = 0, fentrytaxrate = 0, fentrydiscountrate = 0 } = row
      if (!fpriceqty) return
      const fentrytaxrate1 = fentrytaxrate / 100
      const fentrydiscountrate1 = fentrydiscountrate / 100
      // 计算含税单价（单价*（1+税率））
      const ftaxprice = this.toFixedLength(fprice * (1 + fentrytaxrate1))
      this.$set(row, 'ftaxprice', ftaxprice || undefined)
      // 计算折扣额（单价*计价数量*折扣率）
      const fdiscountamount = this.toFixedLength((fprice * fpriceqty * fentrydiscountrate) / 100)
      this.$set(row, 'fdiscountamount', fdiscountamount || undefined)
      // 计算不含税金额（含税单价/（1+税率）*计价数量*（1-折扣率））
      const fnotaxamountford = this.toFixedLength((ftaxprice / (1 + fentrytaxrate1)) * fpriceqty * (1 - fentrydiscountrate1), 2)
      this.$set(row, 'fnotaxamountford', fnotaxamountford || undefined)
      // 计算税额（不含税金额*税率）
      const ftaxamountford = this.toFixedLength(fnotaxamountford * fentrytaxrate1, 2)
      this.$set(row, 'ftaxamountford', ftaxamountford || undefined)
      // 计算价税合计（不含税金额+税额）
      const ftaxamount = this.toFixedLength(fnotaxamountford + ftaxamountford, 2)
      this.$set(row, 'ftaxamount', ftaxamount || undefined)
    },
    // 改变计价数量
    handleChangePriceQty(row) {
      const { fpriceqty = 0, ftaxprice = 0, fentrytaxrate = 0, fentrydiscountrate = 0 } = row
      if (!fpriceqty) return
      const fentrytaxrate1 = fentrytaxrate / 100
      const fentrydiscountrate1 = fentrydiscountrate / 100
      // 计算折扣额（含税单价*计价数量*折扣率）
      const fdiscountamount = this.toFixedLength((ftaxprice * fpriceqty * fentrydiscountrate) / 100)
      this.$set(row, 'fdiscountamount', fdiscountamount || undefined)
      // 计算不含税金额（含税单价/（1+税率）*计价数量*（1-折扣率））
      const fnotaxamountford = this.toFixedLength((ftaxprice / (1 + fentrytaxrate1)) * fpriceqty * (1 - fentrydiscountrate1), 2)
      this.$set(row, 'fnotaxamountford', fnotaxamountford || undefined)
      // 计算税额（不含税金额*税率）
      const ftaxamountford = this.toFixedLength(fnotaxamountford * fentrytaxrate1, 2)
      this.$set(row, 'ftaxamountford', ftaxamountford || undefined)
      // 计算价税合计（不含税金额+税额）
      const ftaxamount = this.toFixedLength(fnotaxamountford + ftaxamountford, 2)
      this.$set(row, 'ftaxamount', ftaxamount || undefined)
    },
    // 改变含税单价
    handleChangeTaxPrice(row) {
      const { fpriceqty = 0, ftaxprice = 0, fentrytaxrate = 0, fentrydiscountrate = 0 } = row
      if (!fpriceqty) return
      const fentrytaxrate1 = fentrytaxrate / 100
      const fentrydiscountrate1 = fentrydiscountrate / 100
      // 计算单价
      const fprice = this.toFixedLength(ftaxprice / (1 + fentrytaxrate1))
      this.$set(row, 'fprice', fprice || undefined)
      // 计算折扣额（含税单价*计价数量*折扣率）
      const fdiscountamount = this.toFixedLength((ftaxprice * fpriceqty * fentrydiscountrate) / 100)
      this.$set(row, 'fdiscountamount', fdiscountamount || undefined)
      // 计算不含税金额（含税单价/（1+税率）*计价数量*（1-折扣率））
      const fnotaxamountford = this.toFixedLength((ftaxprice / (1 + fentrytaxrate1)) * fpriceqty * (1 - fentrydiscountrate1), 2)
      this.$set(row, 'fnotaxamountford', fnotaxamountford || undefined)
      // 计算税额（不含税金额*税率）
      const ftaxamountford = this.toFixedLength(fnotaxamountford * fentrytaxrate1, 2)
      this.$set(row, 'ftaxamountford', ftaxamountford || undefined)
      // 计算价税合计（不含税金额+税额）
      const ftaxamount = this.toFixedLength(fnotaxamountford + ftaxamountford, 2)
      this.$set(row, 'ftaxamount', ftaxamount || undefined)
    },
    // 改变税率
    handleChangeEntryTaxRate(row) {
      const { fprice = 0, fpriceqty = 0, ftaxprice = 0, fentrytaxrate = 0, fentrydiscountrate = 0 } = row
      if (!fprice || !fpriceqty || !ftaxprice) return
      const fentrytaxrate1 = fentrytaxrate / 100
      const fentrydiscountrate1 = fentrydiscountrate / 100
      // 判断是按含税单价录入还是按不含税单价录入
      if (this.form.fistax) {
        // 按含税单价录入，改变税率时需反算不含税单价
        // 计算单价 = 含税单价 / (1 + 税率)
        const fprice = this.toFixedLength(ftaxprice / (1 + fentrytaxrate1))
        this.$set(row, 'fprice', fprice || undefined)
      } else {
        // 按不含税单价录入，改变税率时需正算含税单价
        // 计算含税单价 = 单价 * (1 + 税率)
        const ftaxpriceNew = this.toFixedLength(fprice * (1 + fentrytaxrate1))
        this.$set(row, 'ftaxprice', ftaxpriceNew || undefined)
      }
      // 计算折扣额（含税单价*计价数量*折扣率）
      const fdiscountamount = this.toFixedLength((ftaxprice * fpriceqty * fentrydiscountrate) / 100)
      this.$set(row, 'fdiscountamount', fdiscountamount || undefined)
      // 计算不含税金额（含税单价/（1+税率）*计价数量*（1-折扣率））
      const fnotaxamountford = this.toFixedLength((ftaxprice / (1 + fentrytaxrate1)) * fpriceqty * (1 - fentrydiscountrate1), 2)
      this.$set(row, 'fnotaxamountford', fnotaxamountford || undefined)
      // 计算税额（不含税金额*税率）
      const ftaxamountford = this.toFixedLength(fnotaxamountford * fentrytaxrate1, 2)
      this.$set(row, 'ftaxamountford', ftaxamountford || undefined)
      // 计算价税合计（不含税金额+税额）
      const ftaxamount = this.toFixedLength(fnotaxamountford + ftaxamountford, 2)
      this.$set(row, 'ftaxamount', ftaxamount || undefined)
    },
    // 改变折扣率
    handleChangeEntryDiscountRate(row) {
      const { fprice = 0, fpriceqty = 0, ftaxprice = 0, fentrytaxrate = 0, fentrydiscountrate = 0 } = row
      if (!fprice || !fpriceqty || !ftaxprice) return
      const fentrytaxrate1 = fentrytaxrate / 100
      const fentrydiscountrate1 = fentrydiscountrate / 100
      // 判断是按含税单价录入还是按不含税单价录入
      // 按含税单价录入时，折扣额 = 含税单价 * 数量 * 折扣率
      // 否则，折扣额 = 单价 * 数量 * 折扣率
      let fdiscountamount = 0
      if (this.form.fistax) {
        // 按含税单价录入
        fdiscountamount = this.toFixedLength((ftaxprice * fpriceqty * fentrydiscountrate) / 100)
      } else {
        // 按不含税单价录入
        fdiscountamount = this.toFixedLength((fprice * fpriceqty * fentrydiscountrate) / 100)
      }
      this.$set(row, 'fdiscountamount', fdiscountamount || undefined)
      // 计算不含税金额（含税单价/（1+税率）*计价数量*（1-折扣率））
      const fnotaxamountford = this.toFixedLength((ftaxprice / (1 + fentrytaxrate1)) * fpriceqty * (1 - fentrydiscountrate1), 2)
      this.$set(row, 'fnotaxamountford', fnotaxamountford || undefined)
      // 计算税额（不含税金额*税率）
      const ftaxamountford = this.toFixedLength(fnotaxamountford * fentrytaxrate1, 2)
      this.$set(row, 'ftaxamountford', ftaxamountford || undefined)
      // 计算价税合计（不含税金额+税额）
      const ftaxamount = this.toFixedLength(fnotaxamountford + ftaxamountford, 2)
      this.$set(row, 'ftaxamount', ftaxamount || undefined)
    },
    // 改变不含税金额
    handleChangeNotaxAmountFor(row) {
      const { fentrytaxrate = 0, fnotaxamountford = 0 } = row
      const fentrytaxrate1 = fentrytaxrate / 100
      this.$set(row, 'ftaxamountford', this.toFixedLength(fnotaxamountford * fentrytaxrate1) || undefined)
      this.$set(row, 'ftaxamount', this.toFixedLength(fnotaxamountford + fnotaxamountford * fentrytaxrate1) || undefined)
    },
    // 改变税额
    handleChangeTaxAmountFor(row) {
      const { fnotaxamountford, ftaxamountford } = row
      this.$set(row, 'ftaxamount', this.toFixedLength(fnotaxamountford + ftaxamountford))
    },
    // 改变是否赠品
    handleChangeIsFree(row) {
      const { fisfree = false } = row
      if (fisfree) {
        this.$set(row, 'ftaxprice', undefined)
        this.$set(row, 'fprice', undefined)
        this.$set(row, 'fentrytaxrate', undefined)
        this.$set(row, 'fentrydiscountrate', undefined)
        this.$set(row, 'fdiscountamount', undefined)
        this.$set(row, 'fnotaxamountford', undefined)
        this.$set(row, 'ftaxamountford', undefined)
        this.$set(row, 'ftaxamount', undefined)
      }
    },
    // 获取表格合计
    getSummary(param) {
      const { columns, data } = param
      const summaryFields = ['fpriceqty', 'fdiscountamount', 'fnotaxamountford', 'ftaxamountford', 'ftaxamount']
      return columns.map((column, index) => {
        if (index === 0) return '合计'
        if (summaryFields.includes(column.property)) {
          return `${this.calculateTotal(data, column.property)}`
        }
        return ''
      })
    },
    // 计算合计
    calculateTotal(data, key) {
      const total = data.reduce((sum, item) => {
        const value = parseFloat(item[key]) || 0
        return sum + value
      }, 0)
      return parseFloat(total.toFixed(5))
    },
    // 提交表单
    async handleSubmit() {
      try {
        const valid = await this.$refs.form.validate()
        if (!valid) return
        this.loading = true
        const res = await savePayables(this.form)
        const { code, msg } = res
        if (code === 200) {
          this.$message.success('操作成功')
          this.hasSuccessfully = true
          if (this.isPush) {
            const { number } = res.data
            if (number) {
              this.open = false
              this.$nextTick(() => {
                this.$refs.payablesDetail.getInfo({ BillNo: number })
              })
            } else {
              this.open = false
              this.$emit('callBack', true)
            }
          } else {
            this.open = false
            this.$emit('callBack', true)
          }
        } else {
          this.$message.error(msg)
        }
      } catch (error) {
        console.error('提交失败:', error)
      } finally {
        this.loading = false
      }
    },
    // 物料详情
    handleMaterialNumber(number) {
      if (number && this.$parent.handleMaterialNumber) {
        this.$parent.handleMaterialNumber(number)
      }
    }
  }
}
</script>
<style scoped lang="scss">
@import '@/assets/styles/custom-new.scss';
.full-width {
  width: 100%;
}
::v-deep {
  .custom-table {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    border-bottom: 0;
    margin-bottom: 20px;
    .el-form-item {
      margin-top: 10px !important;
      margin-bottom: 10px !important;
      .el-form-item__error {
        top: 95%;
        padding-top: 0;
      }
    }
    .el-input--prefix .el-input__inner {
      padding-left: 20px;
      padding-right: 20px;
    }
    .el-input--prefix.el-date-editor--date .el-input__inner {
      padding-left: 30px;
    }
    .el-date-editor--datetime .el-input__inner {
      padding-left: 30px;
      padding-right: 30px;
    }
  }
}
</style>
