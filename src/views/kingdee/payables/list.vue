<template>
  <div class="newBox bgcf9 vh-85">
    <!-- 搜索 -->
    <div class="custom-search flex" style="padding-top: 18px; overflow: hidden" :style="{ justifyContent: showSearch ? 'space-between' : 'flex-end' }">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" @submit.native.prevent>
        <el-form-item label="结算组织" prop="settleOrg">
          <el-select v-model="queryParams.settleOrg" placeholder="请选择结算组织" multiple collapse-tags>
            <el-option v-for="org in ApplicationOrgNumber" :key="org.value" :label="org.label" :value="org.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="时间范围" prop="dateRange">
          <el-date-picker v-model="dateRange" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd" clearable :picker-options="dateOptions" style="width: 220px"></el-date-picker>
        </el-form-item>
        <el-form-item label="往来单位类型" prop="contactUnitType">
          <el-select v-model="queryParams.contactUnitType" placeholder="请选择往来单位类型" @change="handleContactUnitTypeChange">
            <el-option v-for="type in ContactUnitTypeOptions" :key="type.value" :label="type.label" :value="type.value"></el-option>
          </el-select>
        </el-form-item>
        <customer-search-select :keyword.sync="queryParams.customerNumber" isBack @callBack="handleCustomerSearch($event)" :options="[{ Number: queryParams.customerNumber, Name: queryParams.customerName }]" v-if="queryParams.contactUnitType === 'BD_Customer'" />
        <supplier-search-select :keyword.sync="queryParams.customerNumber" isBack @callBack="handleSupplierSearch($event)" :options="[{ FNumber: queryParams.customerNumber, FName: queryParams.customerName }]" v-if="queryParams.contactUnitType === 'BD_Supplier'" />
        <el-form-item label="包括未审核单据" prop="noAudit">
          <el-switch v-model="queryParams.noAudit" active-text="是" inactive-text="否" @change="handleQuery" />
        </el-form-item>
        <el-form-item label="余额为零不显示" prop="noShowForNoLeft">
          <el-switch v-model="queryParams.noShowForNoLeft" active-text="是" inactive-text="否" @change="handleQuery" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="small" @click="handleResetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns" isSetitem @updateColumns="updateColumns"></right-toolbar>
    </div>
    <!-- 列表 -->
    <div style="padding: 15px 20px">
      <el-table v-loading="loading" ref="allTable" :data="list" style="width: 100%" class="custom-table" :row-class-name="tableRowClassName">
        <el-table-column align="center" type="index" label="序号"></el-table-column>
        <!-- 往来单位编码 -->
        <el-table-column align="center" prop="ContactUnitNumber" label="往来单位编码" show-overflow-tooltip min-width="110" v-if="columns[0].visible"></el-table-column>
        <!-- 往来单位名称 -->
        <el-table-column align="center" prop="ContactUnitName" label="往来单位名称" show-overflow-tooltip min-width="110" v-if="columns[1].visible"></el-table-column>
        <!-- 业务描述 -->
        <el-table-column align="center" prop="BusinessDesp" label="业务描述" show-overflow-tooltip v-if="columns[2].visible"></el-table-column>
        <!-- 单据类型 -->
        <el-table-column align="center" prop="BillTypeName" label="单据类型" show-overflow-tooltip v-if="columns[3].visible"></el-table-column>
        <!-- 单据编码 -->
        <el-table-column align="center" prop="BillNo" label="单据编码" show-overflow-tooltip v-if="columns[4].visible">
          <template slot-scope="{ row }">
            <span class="table-link" @click="handleShowPayablesDetail(row)" v-if="row.BillNo && String(row.BillNo).includes('AP')">{{ row.BillNo }}</span>
            <span v-else>{{ row.BillNo }}</span>
          </template>
        </el-table-column>
        <!-- 业务日期 -->
        <el-table-column align="center" prop="Date" label="业务日期" show-overflow-tooltip v-if="columns[5].visible"></el-table-column>
        <!-- 订货方 -->
        <el-table-column align="center" prop="Order" label="订货方" show-overflow-tooltip v-if="columns[6].visible"></el-table-column>
        <!-- 收货方 -->
        <el-table-column align="center" prop="Transfer" label="收货方" show-overflow-tooltip v-if="columns[7].visible"></el-table-column>
        <!-- 付款方 -->
        <el-table-column align="center" prop="Charge" label="付款方" show-overflow-tooltip v-if="columns[8].visible"></el-table-column>
        <!-- 原币 -->
        <el-table-column align="center" label="原币">
          <!-- 币别 -->
          <el-table-column align="center" prop="CurrencyForName" label="币别" show-overflow-tooltip v-if="columns[9].visible"></el-table-column>
          <!-- 本期应收 -->
          <el-table-column align="center" prop="AmountFor" label="本期应收" show-overflow-tooltip v-if="columns[10].visible"></el-table-column>
          <!-- 本期收款 -->
          <el-table-column align="center" prop="RealAmountFor" label="本期收款" show-overflow-tooltip v-if="columns[11].visible"></el-table-column>
          <!-- 本期冲销额 -->
          <el-table-column align="center" prop="OffAmountFor" label="本期冲销额" show-overflow-tooltip min-width="90" v-if="columns[12].visible"></el-table-column>
          <!-- 期末余额 -->
          <el-table-column align="center" prop="LeftAmountFor" label="期末余额" show-overflow-tooltip v-if="columns[13].visible"></el-table-column>
        </el-table-column>
        <!-- 本位币 -->
        <el-table-column align="center" label="本位币">
          <!-- 币别 -->
          <el-table-column align="center" prop="CurrencyName" label="币别" show-overflow-tooltip v-if="columns[14].visible"></el-table-column>
          <!-- 本期应收 -->
          <el-table-column align="center" prop="Amount" label="本期应收" show-overflow-tooltip v-if="columns[15].visible"></el-table-column>
          <!-- 本期收款 -->
          <el-table-column align="center" prop="RealAmount" label="本期收款" show-overflow-tooltip v-if="columns[16].visible"></el-table-column>
          <!-- 本期冲销额 -->
          <el-table-column align="center" prop="OffAmount" label="本期冲销额" show-overflow-tooltip min-width="90" v-if="columns[17].visible"></el-table-column>
          <!-- 期末余额 -->
          <el-table-column align="center" prop="LeftAmount" label="期末余额" show-overflow-tooltip v-if="columns[18].visible"></el-table-column>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="custom-pagination">
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.limit" @pagination="getList" :local="userId + '.payablesDetailPageSize'" />
      </div>
    </div>
    <!-- 应付详情 -->
    <payables-detail ref="payablesDetail" @callBack="showPayablesDetail = false" v-if="showPayablesDetail" />
  </div>
</template>
<script>
import { kingdee } from '@/minix'
import { getPayablesDetailList } from '@/api/kingdee/payables'
import customerSearchSelect from '@/components/SearchSelect/customer'
import supplierSearchSelect from '@/components/SearchSelect/supplier'
import payablesDetail from './detail'

export default {
  name: 'PayablesDetail',
  mixins: [kingdee],
  components: { customerSearchSelect, supplierSearchSelect, payablesDetail },
  data() {
    return {
      showPayablesDetail: false,
      // 搜索
      queryParams: {
        pageNum: 1,
        startRow: 0, // 开始行索引
        limit: 10, // 最大行数
        settleOrg: undefined, // 结算组织
        startDate: undefined, // 开始日期
        endDate: undefined, // 结束日期
        contactUnitType: 'BD_Supplier', // 往来单位类型
        customerNumber: undefined, // 客户编码
        customerName: undefined, // 客户名称
        noAudit: false, // 包括未审核单据
        noShowForNoLeft: false, // 余额为零不显示
        schemeId: '67870763498978' // 过滤方案内码
      },
      dateRange: [new Date('2023-08-01'), new Date()],
      dateOptions: {
        shortcuts: [
          {
            text: '最近一周',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setDate(start.getDate() - 7)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近一个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setMonth(start.getMonth() - 1)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近三个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setMonth(start.getMonth() - 3)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近六个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setMonth(start.getMonth() - 6)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近一年',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setFullYear(start.getFullYear() - 1)
              picker.$emit('pick', [start, end])
            }
          }
        ]
      },
      // 列表
      list: [],
      total: 0,
      loading: false,
      // 显隐列控制
      showSearch: true,
      // 列信息
      columns: [
        { key: 0, label: `往来单位编码`, visible: true },
        { key: 1, label: `往来单位名称`, visible: true },
        { key: 2, label: `业务描述`, visible: true },
        { key: 3, label: `单据类型`, visible: true },
        { key: 4, label: `单据编码`, visible: true },
        { key: 5, label: `业务日期`, visible: true },
        { key: 6, label: `订货方`, visible: true },
        { key: 7, label: `收货方`, visible: true },
        { key: 8, label: `付款方`, visible: true },
        { key: 9, label: `币别(原币)`, visible: true },
        { key: 10, label: `本期应收(原币)`, visible: true },
        { key: 11, label: `本期收款(原币)`, visible: true },
        { key: 12, label: `本期冲销额(原币)`, visible: true },
        { key: 13, label: `期末余额(原币)`, visible: true },
        { key: 14, label: `币别(本位币)`, visible: true },
        { key: 15, label: `本期应收(本位币)`, visible: true },
        { key: 16, label: `本期收款(本位币)`, visible: true },
        { key: 17, label: `本期冲销额(本位币)`, visible: true },
        { key: 18, label: `期末余额(本位币)`, visible: true }
      ]
    }
  },
  computed: {
    // 获取当前用户ID
    userId() {
      return this.$store.getters.info.userId
    }
  },
  created() {
    const columns = localStorage.getItem(this.userId + '.payablesDetailColumns')
    if (columns) this.columns = JSON.parse(columns)
    // 读取缓存的分页大小
    const cachedPageSize = localStorage.getItem(this.userId + '.payablesDetailPageSize')
    if (cachedPageSize) {
      this.queryParams.limit = parseInt(cachedPageSize)
    }
    // 读取缓存的搜索条件
    const cachedSearchParams = localStorage.getItem(this.userId + '.payablesDetailSearchParams')
    if (cachedSearchParams) {
      const searchParams = JSON.parse(cachedSearchParams)
      // 恢复搜索条件
      if (searchParams.settleOrg) this.queryParams.settleOrg = searchParams.settleOrg
      if (searchParams.dateRange) this.dateRange = searchParams.dateRange.map(date => new Date(date))
      if (searchParams.contactUnitType) this.queryParams.contactUnitType = searchParams.contactUnitType
      if (searchParams.customerNumber) this.queryParams.customerNumber = searchParams.customerNumber
      if (searchParams.customerName) this.queryParams.customerName = searchParams.customerName
      if (searchParams.noAudit !== undefined) this.queryParams.noAudit = searchParams.noAudit
      if (searchParams.noShowForNoLeft !== undefined) this.queryParams.noShowForNoLeft = searchParams.noShowForNoLeft
    }
    // 处理路由参数（路由参数优先级高于缓存）
    const { settleOrg = undefined, contactUnitType = undefined, customerNumber = undefined, customerName = undefined } = this.$route.query
    if (settleOrg || contactUnitType || customerNumber || customerName) {
      const newSettleOrg = settleOrg || this.ApplicationOrgNumber[0].value
      this.queryParams.settleOrg = [newSettleOrg]
      this.queryParams.contactUnitType = contactUnitType || 'BD_Supplier'
      this.queryParams.customerNumber = customerNumber?.replace(/\D/g, '') || undefined
      this.queryParams.customerName = customerName || undefined
    } else if (!cachedSearchParams) {
      // 如果没有缓存的搜索条件，使用默认值
      const newSettleOrg = this.ApplicationOrgNumber[0].value
      this.queryParams.settleOrg = [newSettleOrg]
      this.queryParams.contactUnitType = 'BD_Supplier'
    }
    // 获取列表
    this.getList()
  },
  methods: {
    // 显示应付详情
    handleShowPayablesDetail(row) {
      this.showPayablesDetail = true
      this.$nextTick(() => {
        this.$refs.payablesDetail.getInfo({ BillNo: row.BillNo })
      })
    },
    // 改变显隐列缓存
    updateColumns(data = []) {
      localStorage.setItem(this.userId + '.payablesDetailColumns', JSON.stringify(data))
    },
    // 保存搜索条件到localStorage
    saveSearchParams() {
      const searchParams = {
        settleOrg: this.queryParams.settleOrg,
        dateRange: this.dateRange,
        contactUnitType: this.queryParams.contactUnitType,
        customerNumber: this.queryParams.customerNumber,
        customerName: this.queryParams.customerName,
        noAudit: this.queryParams.noAudit,
        noShowForNoLeft: this.queryParams.noShowForNoLeft
      }
      localStorage.setItem(this.userId + '.payablesDetailSearchParams', JSON.stringify(searchParams))
    },
    // 列表
    getList() {
      this.loading = true
      this.queryParams.beginDate = this.parseTime(this.dateRange[0], '{y}-{m}-{d}')
      this.queryParams.endDate = this.parseTime(this.dateRange[1], '{y}-{m}-{d}')
      this.queryParams.startRow = Math.max(0, (this.queryParams.pageNum - 1) * this.queryParams.limit)
      const query = { ...this.queryParams, settleOrg: this.queryParams.settleOrg.join(',') }
      delete query.pageNum
      getPayablesDetailList(query).then(res => {
        const { code, data, msg } = res
        if (code === 200) {
          const { data: list, total } = data
          this.list = list
          this.total = total
        } else this.$message.error(msg)
        this.loading = false
      })
    },
    // 重置搜索表单
    resetQueryForm() {
      this.resetForm('queryForm')
      this.queryParams.settleOrg = [this.ApplicationOrgNumber[0].value]
      this.queryParams.contactUnitType = 'BD_Supplier'
      this.queryParams.customerNumber = undefined
      this.customerName = undefined
      this.supplierName = undefined
      this.dateRange = [new Date('2023-08-01'), new Date()]
    },
    // 客户搜索
    handleCustomerSearch(data) {
      this.queryParams.customerNumber = data.Number
      this.queryParams.customerName = data.Name
      this.handleQuery()
    },
    // 供应商搜索
    handleSupplierSearch(data) {
      this.queryParams.customerNumber = data.FNumber
      this.queryParams.customerName = data.FName
      this.handleQuery()
    },
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      const { settleOrg = undefined, contactUnitType = undefined, customerNumber = undefined } = this.$route.query
      if (settleOrg || contactUnitType || customerNumber) {
        this.$router.replace({
          query: {}
        })
      }
      this.saveSearchParams()
      this.getList()
    },
    // 重置搜索
    handleResetQuery() {
      const { settleOrg = undefined, contactUnitType = undefined, customerNumber = undefined } = this.$route.query
      if (settleOrg || contactUnitType || customerNumber) {
        this.$router.replace({
          query: {}
        })
      }
      this.resetQueryForm()
      this.handleQuery()
    },
    // 往来单位类型改变
    handleContactUnitTypeChange(value) {
      const { settleOrg = undefined, contactUnitType = undefined, customerNumber = undefined } = this.$route.query
      if (settleOrg || contactUnitType || customerNumber) {
        this.$router.replace({
          query: {}
        })
      }
      this.resetQueryForm()
      this.queryParams.contactUnitType = value
      this.handleQuery()
    },
    // 表格行样式
    tableRowClassName({ row }) {
      if (row.ContactUnitNumber.includes('小计') && row.ContactUnitName === '') return 'highlight-row'
    }
  }
}
</script>
<style scoped lang="scss">
@import '@/assets/styles/custom-new.scss';
.el-table.custom-table {
  ::v-deep .el-table__row.highlight-row {
    td.el-table__cell {
      background-color: #f5f5f5 !important;
    }
    &:hover {
      td.el-table__cell {
        background-color: #f5f5f5 !important;
      }
    }
  }
}
.custom-dialog.custom-customer-dialog ::v-deep {
  .el-dialog__body {
    padding: 0;
  }
}
</style>
