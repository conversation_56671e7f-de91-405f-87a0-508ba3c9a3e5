<template>
  <div class="newBox bgcf9 vh-85">
    <!-- 搜索 -->
    <div class="custom-search flex" style="padding-top: 18px">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" @submit.native.prevent>
        <el-form-item label="结算组织" prop="settleOrg">
          <el-select v-model="queryParams.settleOrg" placeholder="请选择结算组织" multiple collapse-tags>
            <el-option v-for="org in ApplicationOrgNumber" :key="org.value" :label="org.label" :value="org.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="时间范围" prop="dateRange">
          <el-date-picker v-model="dateRange" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd" clearable :picker-options="dateOptions" style="width: 230px"></el-date-picker>
        </el-form-item>
        <el-form-item label="期末余额" prop="periodAmount">
          <el-input v-model="queryParams.periodAmount" placeholder="请输入期末余额" style="width: 110px" @input="queryParams.periodAmount = queryParams.periodAmount.replace(/[^\d-]/g, '')" />
        </el-form-item>
        <el-form-item label="至" prop="periodToAmount">
          <el-input v-model="queryParams.periodToAmount" placeholder="请输入期末余额至" style="width: 110px" @input="queryParams.periodToAmount = queryParams.periodToAmount.replace(/[^\d-]/g, '')" />
        </el-form-item>
        <el-form-item label="往来单位类型" prop="contactUnitType">
          <el-select v-model="queryParams.contactUnitType" placeholder="请选择往来单位类型" @change="handleContactUnitTypeChange">
            <el-option v-for="type in ContactUnitTypeOptions" :key="type.value" :label="type.label" :value="type.value"></el-option>
          </el-select>
        </el-form-item>
        <customer-search-select :keyword.sync="queryParams.customerNumber" @callBack="handleQuery" v-if="queryParams.contactUnitType === 'BD_Customer'" />
        <supplier-search-select :keyword.sync="queryParams.customerNumber" @callBack="handleQuery" v-if="queryParams.contactUnitType === 'BD_Supplier'" />
        <el-form-item label="包括未审核单据" prop="noAudit">
          <el-switch v-model="queryParams.noAudit" active-text="是" inactive-text="否" @change="handleQuery" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="small" @click="handleResetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <!-- 列表 -->
    <div style="padding: 15px 20px">
      <el-table v-loading="loading" ref="allTable" border :data="list" style="width: 100%" class="custom-table" :default-sort="{ prop: 'leftAmount', order: 'descending' }">
        <!-- 往来单位编码 -->
        <el-table-column align="center" prop="ContactUnitNumber" label="往来单位编码" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <template v-if="row.ContactUnitNumber.includes('合计')">{{ row.ContactUnitNumber }}</template>
            <template v-else>
              <span class="table-link" @click="handleDetail(row)" title="点击查看详情" v-if="checkPermi(['kingdee:payables:list'])">{{ row.ContactUnitNumber }}</span>
              <span v-else>{{ row.ContactUnitNumber }}</span>
            </template>
          </template>
        </el-table-column>
        <!-- 往来单位名称 -->
        <el-table-column align="center" prop="ContactUnitName" label="往来单位名称" show-overflow-tooltip></el-table-column>
        <!-- 币别 -->
        <el-table-column align="center" prop="CurrencyForName" label="币别" show-overflow-tooltip></el-table-column>
        <!-- 期初余额 -->
        <el-table-column align="center" prop="InitAmount" label="期初余额" show-overflow-tooltip></el-table-column>
        <!-- 本期应付 -->
        <el-table-column align="center" prop="Amount" label="本期应付" show-overflow-tooltip></el-table-column>
        <!-- 本期付款 -->
        <el-table-column align="center" prop="RealAmount" label="本期付款" show-overflow-tooltip></el-table-column>
        <!-- 本期冲销额 -->
        <el-table-column align="center" prop="OffAmount" label="本期冲销额" show-overflow-tooltip></el-table-column>
        <!-- 期末余额 -->
        <el-table-column align="center" prop="leftAmount" label="期末余额" show-overflow-tooltip sortable></el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="custom-pagination">
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.limit" @pagination="getList" />
      </div>
    </div>
  </div>
</template>
<script>
import { kingdee } from '@/minix'
import { getPayablesSummary } from '@/api/kingdee/payables'
import { checkPermi } from '@/utils/permission'
import customerSearchSelect from '@/components/SearchSelect/customer'
import supplierSearchSelect from '@/components/SearchSelect/supplier'

export default {
  name: 'PayablesTotal',
  props: {
    isDialog: {
      type: Boolean,
      default: false
    }
  },
  mixins: [kingdee],
  components: { customerSearchSelect, supplierSearchSelect },
  data() {
    return {
      // 搜索
      queryParams: {
        pageNum: 1, // 当前页码
        startRow: 0, // 开始行索引
        limit: 10, // 每页条数
        settleOrg: ['100073'],
        contactUnitType: 'BD_Supplier',
        beginDate: undefined,
        endDate: undefined,
        customerNumber: undefined,
        periodAmount: '-9999999999', // 期末余额
        periodToAmount: '9999999999', // 期末余额至
        noAudit: false // 是否包括未审核
      },
      dateRange: [new Date('2023-08-01'), new Date()],
      dateOptions: {
        shortcuts: [
          {
            text: '最近一周',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setDate(start.getDate() - 7)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近一个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setMonth(start.getMonth() - 1)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近三个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setMonth(start.getMonth() - 3)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近六个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setMonth(start.getMonth() - 6)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近一年',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setFullYear(start.getFullYear() - 1)
              picker.$emit('pick', [start, end])
            }
          }
        ]
      },
      // 列表
      list: [],
      total: 0,
      loading: false
    }
  },
  created() {
    // 获取期末余额
    this.queryParams.periodAmount = localStorage.getItem(this.userId + '.receivableTotal.periodAmount') || '-9999999999'
    this.queryParams.periodToAmount = localStorage.getItem(this.userId + '.receivableTotal.periodToAmount') || '9999999999'
    // 获取列表
    if (!this.isDialog) this.getList()
  },
  methods: {
    checkPermi,
    // 列表
    getList() {
      this.loading = true
      this.queryParams.beginDate = this.parseTime(this.dateRange[0], '{y}-{m}-{d}')
      this.queryParams.endDate = this.parseTime(this.dateRange[1], '{y}-{m}-{d}')
      this.queryParams.startRow = Math.max(0, (this.queryParams.pageNum - 1) * this.queryParams.limit)
      const query = { ...this.queryParams, limit: this.queryParams.limit * 3, settleOrg: this.queryParams.settleOrg.join(',') }
      delete query.pageNum
      getPayablesSummary(query).then(async res => {
        const { code, msg, data } = res
        if (code === 200) {
          const { data: list, total } = data
          const newList = list.filter(item => item.ContactUnitName)
          this.list = newList
          if (total / 3 === 0) this.total = parseInt(total / 3)
          else this.total = parseInt((total - 1) / 3)
        } else this.$message.error(msg)
        this.loading = false
      })
    },
    // 往来单位类型改变
    handleContactUnitTypeChange(value) {
      this.dateRange = [new Date('2023-08-01'), new Date()]
      this.resetForm('queryForm')
      this.queryParams.contactUnitType = value
      this.queryParams.customerNumber = undefined
      this.handleQuery()
    },
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      // 记录期末余额至浏览器缓存
      localStorage.setItem(this.userId + '.payablesTotal.periodAmount', this.queryParams.periodAmount)
      localStorage.setItem(this.userId + '.payablesTotal.periodToAmount', this.queryParams.periodToAmount)
      this.getList()
    },
    // 重置搜索
    handleResetQuery() {
      this.dateRange = [new Date('2023-08-01'), new Date()]
      this.resetForm('queryForm')
      this.queryParams.customerNumber = undefined
      this.handleQuery()
    },
    // 详情
    handleDetail(row) {
      this.$router.push({
        path: '/kingdee/payablesDetail',
        query: {
          settleOrg: this.queryParams.settleOrg,
          contactUnitType: this.queryParams.contactUnitType,
          customerNumber: row.ContactUnitNumber,
          customerName: row.ContactUnitName
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
@import '@/assets/styles/custom-new.scss';
.el-table.custom-table {
  ::v-deep .el-table__row.highlight-row {
    td.el-table__cell {
      background-color: #f5f5f5 !important;
    }
    &:hover {
      td.el-table__cell {
        background-color: #f5f5f5 !important;
      }
    }
  }
}
</style>
