<template>
  <div class="newBox bgcf9 vh-85">
    <!-- 搜索 -->
    <div class="custom-search flex" style="padding-top: 18px" :style="{ justifyContent: showSearch ? 'space-between' : 'flex-end' }">
      <el-form :model="queryParams" ref="queryForm" size="small" v-show="showSearch" :inline="true" @submit.native.prevent>
        <!-- 结算组织 -->
        <el-form-item label="结算组织" prop="settleOrg">
          <el-select v-model="queryParams.settleOrg" placeholder="请选择结算组织" clearable>
            <el-option v-for="item in ApplicationOrgId" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <!-- 单据编号 -->
        <el-form-item label="单据编号" prop="billNo">
          <el-input v-model="queryParams.billNo" placeholder="请输入单据编号" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <!-- 供应商 -->
        <el-form-item label="供应商" prop="supplierName">
          <el-input v-model="queryParams.supplierName" placeholder="请输入供应商" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <!-- 单据状态 -->
        <el-form-item label="单据状态" prop="documentStatus">
          <el-select v-model="queryParams.documentStatus" placeholder="请选择单据状态" clearable>
            <el-option v-for="item in DocumentStatusOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <!-- 物料名称 -->
        <el-form-item label="物料名称" prop="materialName">
          <el-input v-model="queryParams.materialName" placeholder="请输入物料名称" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <!-- 业务日期 -->
        <el-form-item label="业务日期" prop="date">
          <el-date-picker v-model="queryParams.date" placeholder="请选择业务日期" type="date" value-format="yyyy-MM-dd" size="small"></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="small" @click="handleResetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns" isSetitem @updateColumns="updateColumns"></right-toolbar>
    </div>
    <!-- 列表 -->
    <div style="padding: 15px 20px">
      <el-table v-loading="loading" ref="allTable" border :data="list" row-key="id" style="width: 100%" class="custom-table" :span-method="objectSpanMethod">
        <el-table-column align="center" type="index" label="序号" v-if="columns[0].visible"></el-table-column>
        <!-- 单据类型 -->
        <el-table-column align="center" prop="BillType" label="单据类型" show-overflow-tooltip v-if="columns[1].visible"></el-table-column>
        <!-- 业务类型 -->
        <el-table-column align="center" prop="businessBillType" label="业务类型" show-overflow-tooltip v-if="columns[2].visible">
          <template slot-scope="{ row }">{{ row.businessBillType && getOptionLabel(businessBillTypeOptions, row.businessBillType) }}</template>
        </el-table-column>
        <!-- 业务日期 -->
        <el-table-column align="center" prop="Date" label="业务日期" show-overflow-tooltip v-if="columns[3].visible">
          <template slot-scope="{ row }">{{ parseTime(row.Date, '{y}-{m}-{d}') }}</template>
        </el-table-column>
        <!-- 供应商 -->
        <el-table-column align="center" prop="supplierName" label="供应商" show-overflow-tooltip v-if="columns[4].visible"></el-table-column>
        <!-- 单据编号 -->
        <el-table-column align="center" prop="BillNo" label="单据编号" show-overflow-tooltip v-if="columns[5].visible">
          <template slot-scope="{ row }">
            <span class="table-link" @click="handleDetail(row)">{{ row.BillNo }}</span>
          </template>
        </el-table-column>
        <!-- 币别 -->
        <el-table-column align="center" prop="currency" label="币别" show-overflow-tooltip v-if="columns[6].visible"></el-table-column>
        <!-- 价税合计 -->
        <el-table-column align="center" prop="allAmountFor" label="价税合计" show-overflow-tooltip v-if="columns[7].visible"></el-table-column>
        <!-- 到期日 -->
        <el-table-column align="center" prop="endDate" label="到期日" show-overflow-tooltip v-if="columns[8].visible">
          <template slot-scope="{ row }">{{ parseTime(row.endDate, '{y}-{m}-{d}') }}</template>
        </el-table-column>
        <!-- 结算组织 -->
        <el-table-column align="center" prop="settleOrgName" label="结算组织" show-overflow-tooltip v-if="columns[9].visible"></el-table-column>
        <!-- 采购组织 -->
        <el-table-column align="center" prop="purchaseOrgName" label="采购组织" show-overflow-tooltip v-if="columns[10].visible"></el-table-column>
        <!-- 单据状态 -->
        <el-table-column align="center" prop="DocumentStatus" label="单据状态" show-overflow-tooltip v-if="columns[11].visible">
          <template slot-scope="{ row }">{{ getDocumentStatusLabel(row.DocumentStatus) }}</template>
        </el-table-column>
        <!-- 物料编码 -->
        <el-table-column align="center" prop="MaterialNumber" label="物料编码" show-overflow-tooltip v-if="columns[12].visible">
          <template slot-scope="{ row }">
            <span class="table-link" @click="handleMaterialNumber(row.MaterialNumber)">{{ row.MaterialNumber }}</span>
          </template>
        </el-table-column>
        <!-- 物料名称 -->
        <el-table-column align="center" prop="MaterialName" label="物料名称" show-overflow-tooltip v-if="columns[13].visible"></el-table-column>
        <!-- 计价单位 -->
        <el-table-column align="center" prop="priceUnitName" label="计价单位" show-overflow-tooltip v-if="columns[14].visible"></el-table-column>
        <!-- 单价 -->
        <el-table-column align="center" prop="Price" label="单价" show-overflow-tooltip v-if="columns[15].visible"></el-table-column>
        <!-- 计价数量 -->
        <el-table-column align="center" prop="PriceQty" label="计价数量" show-overflow-tooltip v-if="columns[16].visible"></el-table-column>
        <!-- 含税单价 -->
        <el-table-column align="center" prop="TaxPrice" label="含税单价" show-overflow-tooltip v-if="columns[17].visible"></el-table-column>
        <!-- 不含税金额 -->
        <el-table-column align="center" prop="fnoTaxAmount" label="不含税金额" show-overflow-tooltip v-if="columns[18].visible">
          <template slot-scope="{ row }">{{ parseFloat((row.Price * row.PriceQty).toFixed(3)) }}</template>
        </el-table-column>
        <!-- 价税合计 -->
        <el-table-column align="center" prop="ftotalAmount" label="价税合计" show-overflow-tooltip v-if="columns[19].visible">
          <template slot-scope="{ row }">{{ parseFloat((row.TaxPrice * row.PriceQty).toFixed(3)) }}</template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="custom-pagination">
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.limit" @pagination="getList" :local="userId + '.payablesPageSize'" />
      </div>
    </div>
    <!-- 详情 -->
    <payables-detail ref="payablesDetail" @callBack="handleDetailCallBack" @update="handleUpdate" v-if="showDetail" />
    <!-- 物料详情 -->
    <material-detail ref="materialDetail" v-if="showMaterialDetail" />
  </div>
</template>
<script>
import PayablesDetail from '@/views/kingdee/payables/detail'
import MaterialDetail from '@/views/kingdee/material/detail'
import { kingdee } from '@/minix'
import { getPayablesList } from '@/api/kingdee/payables'

export default {
  name: 'PayablesList',
  mixins: [kingdee],
  components: { PayablesDetail, MaterialDetail },
  data() {
    return {
      // 搜索
      queryParams: {
        pageNum: 1, // 当前页码
        startRow: 0, // 开始行索引
        limit: 10, // 每页条数
        settleOrg: undefined, // 结算组织
        billNo: undefined, // 单据编号
        supplierName: undefined, // 供应商-模糊搜索
        supplierName2: undefined, // 供应商-精确匹配
        documentStatus: undefined, // 单据状态
        materialName: undefined, // 物料名称
        date: undefined // 业务日期
      },
      // 列表
      list: [],
      total: 0,
      loading: false,
      showDetail: false,
      showMaterialDetail: false,
      // 业务类型
      businessBillTypeOptions: [
        { label: '费用采购', value: 'FY' },
        { label: '标准采购', value: 'CG' }
      ],
      // 列表显隐
      showSearch: true,
      // 列信息
      columns: [
        { key: 0, label: `序号`, visible: true },
        { key: 1, label: `单据类型`, visible: true },
        { key: 2, label: `业务类型`, visible: true },
        { key: 3, label: `业务日期`, visible: true },
        { key: 4, label: `供应商`, visible: true },
        { key: 5, label: `单据编号`, visible: true },
        { key: 6, label: `币别`, visible: true },
        { key: 7, label: `价税合计`, visible: true },
        { key: 8, label: `到期日`, visible: true },
        { key: 9, label: `结算组织`, visible: true },
        { key: 10, label: `采购组织`, visible: true },
        { key: 11, label: `单据状态`, visible: true },
        { key: 12, label: `物料编码`, visible: true },
        { key: 13, label: `物料名称`, visible: true },
        { key: 14, label: `计价单位`, visible: true },
        { key: 15, label: `单价`, visible: true },
        { key: 16, label: `计价数量`, visible: true },
        { key: 17, label: `含税单价`, visible: true },
        { key: 18, label: `不含税金额`, visible: true },
        { key: 19, label: `价税合计`, visible: true }
      ]
    }
  },
  computed: {
    // 获取当前用户ID
    userId() {
      return this.$store.getters.info.userId
    }
  },
  created() {
    const columns = localStorage.getItem(this.userId + '.payablesColumns')
    if (columns) this.columns = JSON.parse(columns)
    // 读取缓存的分页大小
    const cachedPageSize = localStorage.getItem(this.userId + '.payablesPageSize')
    if (cachedPageSize) {
      this.queryParams.limit = parseInt(cachedPageSize)
    }
    // 读取保存的搜索条件和分页状态
    this.loadQueryParams()
    // 获取列表
    this.getList()
  },
  methods: {
    // 保存搜索条件和分页状态
    saveQueryParams() {
      const paramsToSave = {
        settleOrg: this.queryParams.settleOrg,
        billNo: this.queryParams.billNo,
        supplierName: this.queryParams.supplierName,
        documentStatus: this.queryParams.documentStatus,
        materialName: this.queryParams.materialName,
        date: this.queryParams.date
      }
      localStorage.setItem(this.userId + '.payablesQueryParams', JSON.stringify(paramsToSave))
    },
    // 读取保存的搜索条件和分页状态
    loadQueryParams() {
      const savedParams = localStorage.getItem(this.userId + '.payablesQueryParams')
      if (savedParams) {
        const params = JSON.parse(savedParams)
        // 合并保存的参数到当前查询参数中
        Object.assign(this.queryParams, params)
      }
    },
    // 改变显隐列缓存
    updateColumns(data = []) {
      localStorage.setItem(this.userId + '.payablesColumns', JSON.stringify(data))
    },
    // 列表
    // prettier-ignore
    getList() {
      this.loading = true
      this.queryParams.startRow = Math.max(0, (this.queryParams.pageNum - 1) * this.queryParams.limit)
      const query = { ...this.queryParams }
      delete query.pageNum
      getPayablesList(query).then(res => {
        const { code, data, msg } = res
        if (code === 200) {
          const { data: list, total } = data
          this.list = list
          this.total = total
        } else this.$message.error(msg)
      }).finally(() => {
        this.loading = false
      })
    },
    // 相同字段合并
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      const visibleColumns = this.columns.filter(col => col.visible)
      const currentColumnKey = visibleColumns[columnIndex]?.key
      if (currentColumnKey >= 1 && currentColumnKey <= 11) {
        const _row = this.calculateSpan(this.list, 'BillNo')[rowIndex]
        const _col = _row > 0 ? 1 : 0
        return {
          rowspan: _row,
          colspan: _col
        }
      }
    },
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      // 保存当前查询状态
      this.saveQueryParams()
      this.getList()
    },
    // 重置搜索
    handleResetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 物料编码
    handleMaterialNumber(number) {
      this.showMaterialDetail = true
      this.$nextTick(() => {
        this.$refs.materialDetail.getInfo(number)
      })
    },
    // 详情
    handleDetail(row) {
      this.showDetail = true
      this.$nextTick(() => {
        this.$refs.payablesDetail.getInfo(row)
      })
    },
    // 详情回调
    handleDetailCallBack(flag) {
      this.showDetail = false
      if (flag) this.getList()
    },
    // 更新
    handleUpdate(data) {
      const { billNo, newStatus } = data
      const index = this.list.findIndex(item => item.BillNo === billNo)
      if (index !== -1) this.list[index].DocumentStatus = newStatus
    }
  }
}
</script>
<style scoped lang="scss">
@import '@/assets/styles/custom-new.scss';
</style>
