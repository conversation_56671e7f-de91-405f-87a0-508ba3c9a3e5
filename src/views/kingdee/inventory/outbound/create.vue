<template>
  <div>
    <el-dialog v-dialogDragBox :title="title" :visible.sync="open" width="90%" class="custom-dialog" :before-close="beforeClose" append-to-body>
      <div style="padding: 0 20px">
        <el-form :model="form" :rules="rules" ref="form" label-width="6em" class="custom-form">
          <el-row :gutter="20" style="display: flex; flex-wrap: wrap">
            <!-- 单据类型 -->
            <el-col :span="6">
              <el-form-item label="单据类型" prop="fbilltypeid">
                <el-select v-model="form.fbilltypeid" filterable placeholder="请选择单据类型" class="full-width" @change="handleChangeBillType">
                  <el-option v-for="item in billTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 业务类型 -->
            <el-col :span="6">
              <el-form-item label="业务类型" prop="fbiztype">
                <el-select v-model="form.fbiztype" filterable placeholder="请选择业务类型" class="full-width" disabled>
                  <el-option v-for="item in businessTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 日期 -->
            <el-col :span="6">
              <el-form-item label="日期" prop="fdate">
                <el-date-picker v-model="form.fdate" type="date" placeholder="请选择日期" class="full-width" value-format="yyyy-MM-dd"></el-date-picker>
              </el-form-item>
            </el-col>
            <!-- 库存组织 -->
            <el-col :span="6">
              <el-form-item label="库存组织" prop="fstockorgid">
                <el-select v-model="form.fstockorgid" filterable placeholder="请选择库存组织" class="full-width" @change="handleChangeStockOrg">
                  <el-option v-for="item in ApplicationOrgId" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 领用组织 -->
            <el-col :span="6">
              <el-form-item label="领用组织" prop="fpickorgid">
                <el-select v-model="form.fpickorgid" filterable placeholder="请选择领用组织" class="full-width" @change="handleChangePickOrg">
                  <el-option v-for="item in ApplicationOrgId" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 库存方向 -->
            <el-col :span="6">
              <el-form-item label="库存方向" prop="fstockdirect">
                <el-select v-model="form.fstockdirect" filterable placeholder="请选择库存方向" class="full-width">
                  <el-option v-for="item in stockDirectOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 客户 -->
            <el-col :span="6">
              <el-form-item label="客户" prop="fcustname">
                <customer-search-select :useOrg="form.fpickorgid" :showLabel="false" :keyword.sync="form.fcustname" style="width: 100%" isBack @callBack="handleCustomerSearchSelect($event)" />
              </el-form-item>
            </el-col>
            <!-- 领料部门 -->
            <el-col :span="6">
              <el-form-item label="领料部门" prop="fdeptid">
                <el-select v-model="form.fdeptid" filterable placeholder="请选择领料部门" class="full-width" @change="handleChangeStockDept">
                  <el-option v-for="item in stockDeptList" :key="item.FNumber" :label="item.FName" :value="item.FNumber"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 领料人 -->
            <el-col :span="6">
              <el-form-item label="领料人" prop="fpickerid">
                <el-select v-model="form.fpickerid" filterable placeholder="请选择领料人" class="full-width">
                  <el-option v-for="item in calculateApplicantList" :key="item.Number" :label="item.Name" :value="item.Number"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 仓管员 -->
            <el-col :span="6">
              <el-form-item label="仓管员" prop="fstockerid">
                <el-select v-model="form.fstockerid" filterable placeholder="请选择仓管员" class="full-width">
                  <el-option v-for="item in calculateStockerList" :key="item.Number" :label="item.Name" :value="item.Number">
                    <span style="float: left">{{ item.Name }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.FDept }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 跟单员 -->
            <el-col :span="6">
              <el-form-item label="跟单员" prop="fscmjGdyName">
                <staff-search-select :useOrg="form.fstockorgid" placeholder="请选择跟单员" :showLabel="false" :keyword.sync="form.fscmjGdyName" style="width: 100%" isBack @callBack="handleStaffSearchSelect($event)" />
              </el-form-item>
            </el-col>
            <!-- 货主类型 -->
            <el-col :span="6">
              <el-form-item label="货主类型" prop="fownertypeid">
                <el-select v-model="form.fownertypeid" filterable placeholder="请选择货主类型" class="full-width" @change="handleChangeOwnerType">
                  <el-option v-for="item in OwnerTypeIdHeadOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 货主 -->
            <el-col :span="6">
              <template v-if="form.fownertypeid === 'BD_Supplier'">
                <el-form-item label="货主" prop="fownerName">
                  <supplier-search-select :useOrg="form.fstockorgid" :showLabel="false" :keyword.sync="form.fownerName" style="width: 100%" isBack @callBack="handleSupplierSearchSelect($event)" />
                </el-form-item>
              </template>
              <template v-else-if="form.fownertypeid === 'BD_Customer'">
                <el-form-item label="货主" prop="fownerName">
                  <customer-search-select :useOrg="form.fstockorgid" :showLabel="false" :keyword.sync="form.fownerName" style="width: 100%" isBack @callBack="handleOwnerCustomerSearchSelect($event)" />
                </el-form-item>
              </template>
              <template v-else>
                <el-form-item label="货主" prop="fownerid">
                  <el-select v-model="form.fownerid" filterable placeholder="请选择货主" class="full-width">
                    <el-option v-for="item in ApplicationOrgId" :key="item.value" :label="item.label" :value="item.value"></el-option>
                  </el-select>
                </el-form-item>
              </template>
            </el-col>
            <!-- 备注 -->
            <el-col :span="12">
              <el-form-item label="备注" prop="fnote">
                <el-input v-model="form.fnote" placeholder="请输入备注" class="full-width"></el-input>
              </el-form-item>
            </el-col>
            <!-- 明细信息 -->
            <el-col :span="24">
              <el-table :data="form.entities" class="full-width custom-table custom-table-cell0" stripe show-summary :summary-method="getSummary">
                <!-- 序号 -->
                <el-table-column type="index" label="序号" width="50" align="center"></el-table-column>
                <!-- 物料编码 -->
                <el-table-column label="物料编码" align="center" show-overflow-tooltip>
                  <template slot-scope="{ row }">
                    <template v-if="isCreate">
                      <material-search-select ref="materialSearchSelect" :keyword.sync="row.fmaterialid" :useOrg="form.fstockorgid" size="small" :showLabel="false" @callBack="handleMaterialSearchSelect($event, row)" :options="[{ Number: row.fmaterialid, Name: row.fmaterialname, Specification: row.fmaterialmodel }]" />
                    </template>
                    <template v-else>
                      <span class="table-link" @click="handleMaterialNumber(row.fmaterialid)">{{ row.fmaterialid }}</span>
                    </template>
                  </template>
                </el-table-column>
                <!-- 物料名称 -->
                <el-table-column label="物料名称" align="center" prop="fmaterialname" show-overflow-tooltip>
                  <template slot-scope="{ row }">
                    <span class="table-link" @click="handleMaterialNumber(row.fmaterialid)" v-if="isCreate">{{ row.fmaterialname }}</span>
                    <span v-else>{{ row.fmaterialname }}</span>
                  </template>
                </el-table-column>
                <!-- 规格型号 -->
                <el-table-column label="规格型号" align="center" prop="fmaterialmodel" show-overflow-tooltip></el-table-column>
                <!-- 单位 -->
                <el-table-column label="单位" align="center" min-width="100">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.funitid`" :rules="rules.funitid">
                      <el-select v-model="scope.row.funitid" placeholder="请选择单位" class="full-width" size="small">
                        <el-option v-for="item in UnitList" :key="item.FNumber" :label="item.FName" :value="item.FNumber"></el-option>
                      </el-select>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 实发数量 -->
                <el-table-column label="实发数量" align="center" min-width="120" prop="fqty">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.fqty`" :rules="rules.fqty">
                      <el-input v-model="scope.row.fqty" placeholder="请输入数量" class="full-width" size="small" @input="handleQtyChange(scope.row)"></el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 发货仓库 -->
                <el-table-column label="发货仓库" align="center" min-width="120">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.fstockname`" :rules="rules.fstockname">
                      <stock-search-select size="small" :useOrg="form.fstockorgid" :showLabel="false" :keyword.sync="scope.row.fstockname" class="full-width" @callBack="handleStockSelect($event, scope.row)" />
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 批号 -->
                <el-table-column label="批号" align="center" min-width="120">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.flot`" :rules="rules.flot">
                      <el-input v-model="scope.row.flot" placeholder="请输入批号" class="full-width" size="small"></el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 货主 -->
                <el-table-column label="货主" align="center" prop="fownerName" show-overflow-tooltip></el-table-column>
                <!-- 备注 -->
                <el-table-column label="备注" align="center" min-width="100">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.fentrynote`" :rules="rules.fentrynote">
                      <el-input v-model="scope.row.fentrynote" placeholder="请输入备注" class="full-width" size="small"></el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 结算单价 -->
                <el-table-column label="结算单价" align="center" min-width="120">
                  <template slot-scope="scope">
                    <el-form-item label-width="0" :prop="`entities.${scope.$index}.fscmjjsprice`" :rules="rules.fscmjjsprice">
                      <el-input v-model="scope.row.fscmjjsprice" placeholder="请输入单价" class="full-width" size="small" @input="handlePriceChange(scope.row)"></el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 结算金额 -->
                <el-table-column label="结算金额" align="center" prop="fscmjjsamount" show-overflow-tooltip></el-table-column>
              </el-table>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn" @click="handleClose()">取消</el-button>
        <el-button class="custom-dialog-btn primary" :loading="loading" @click="handleSubmit">确定</el-button>
      </div>
    </el-dialog>
    <!-- 其他出库单详情 -->
    <mis-delivery-detail ref="misDeliveryDetail" />
  </div>
</template>
<script>
import { kingdee } from '@/minix'
import { isNumber, isNumberLength } from '@/utils/validate'
import StockSearchSelect from '@/components/SearchSelect/stock'
import CustomerSearchSelect from '@/components/SearchSelect/customer'
import SupplierSearchSelect from '@/components/SearchSelect/supplier'
import staffSearchSelect from '@/components/SearchSelect/staff'
import { getSalesmanList } from '@/api/kingdee'
import { getOutboundDetail2, saveOutbound, deleteOutboundV2 } from '@/api/kingdee/inventory/outbound'
import MisDeliveryDetail from '@/views/kingdee/inventory/outbound/detail'
import materialSearchSelect from '@/components/SearchSelect/material'
import { getMaterialDetail } from '@/api/kingdee'

export default {
  name: 'OutboundCreate',
  mixins: [kingdee],
  components: { StockSearchSelect, CustomerSearchSelect, SupplierSearchSelect, staffSearchSelect, MisDeliveryDetail, materialSearchSelect },
  data() {
    return {
      fid: undefined,
      form: {},
      rules: {
        fbilltypeid: [{ required: true, message: '请选择单据类型', trigger: 'change' }],
        fstockorgid: [{ required: true, message: '请选择库存组织', trigger: 'change' }],
        fstockdirect: [{ required: true, message: '请选择库存方向', trigger: 'change' }],
        fdate: [{ required: true, message: '请选择日期', trigger: 'change' }],
        fbiztype: [{ required: true, message: '请选择业务类型', trigger: 'change' }],
        fownertypeid: [{ required: true, message: '请选择货主类型', trigger: 'change' }],
        funitid: [{ required: true, message: '请选择单位', trigger: 'change' }],
        fstockname: [{ required: true, message: '请选择发货仓库', trigger: 'change' }]
      },
      title: '新增采购入库单',
      open: false,
      loading: false,
      // 单据类型
      billTypeOptions: [
        { value: 'QTCKD01_SYS', label: '标准其他出库单', businessType: '0' },
        { value: 'QTCKD02_SYS', label: '资产出库', businessType: '1' },
        { value: 'QTCKD04_SYS', label: '费用物料出库', businessType: '3' },
        { value: 'QTCKD05_SYS', label: '库存调整', businessType: '4' },
        { value: 'QTCKD06_SYS', label: '异价调出单', businessType: '0' },
        { value: 'QTCKD07_SYS', label: '工程项目出库单', businessType: '0' },
        { value: 'QTCKD08_SYS', label: '低值易耗出库单', businessType: '0' }
      ],
      // 业务类型
      businessTypeOptions: [
        { value: '0', label: '物料领用' },
        { value: '1', label: '资产领用' },
        { value: '2', label: 'VMI业务' },
        { value: '3', label: '费用物料领用' },
        { value: '4', label: '库存调整' },
        { value: '5', label: '数量核算' }
      ],
      // 库存方向
      stockDirectOptions: [
        { value: 'GENERAL', label: '普通' },
        { value: 'RETURN', label: '退货' }
      ],
      // 货主类型
      OwnerTypeIdHeadOptions: [
        { value: 'BD_OwnerOrg', label: '业务组织' },
        { value: 'BD_Supplier', label: '供应商' },
        { value: 'BD_Customer', label: '客户' }
      ],
      applicantList: [], // 领料人
      stockerList: [], // 仓管员
      isPush: false,
      isCreate: false,
      hasSuccessfully: false // 是否已成功提交
    }
  },
  computed: {
    // 过滤后的部门列表 - 领料部门
    stockDeptList() {
      return this.ApplicationDeptId.filter(item => item.FUseOrg === this.form.fpickorgid)
    },
    // 领料人列表
    calculateApplicantList() {
      return this.getProcessedEmployeeList(this.applicantList, this.form.fdeptid)
    },
    // 仓管员列表
    calculateStockerList() {
      const list = this.stockerList.map(item => ({
        ...item,
        Number: item.Number ? item.Number.split('_')[0] : item.Number
      }))
      return list.filter((item, index, arr) => {
        return arr.findIndex(obj => obj.Number === item.Number) === index
      })
    }
  },
  methods: {
    // 通用的员工列表处理方法
    getProcessedEmployeeList(employeeList, deptId) {
      if (!deptId) return []
      const dept = this.ApplicationDeptId.find(item => item.FNumber === deptId) || {}
      const deptName = dept.FName || ''
      const filteredList = employeeList.filter(item => item.FDept === deptName)
      // 处理员工编号，只取前三位数字
      return filteredList.map(item => ({
        ...item,
        Number: item.Number ? item.Number.split('_')[0] : item.Number
      }))
    },
    // 获取员工列表的通用方法
    async fetchEmployeeList(orgId, operatorType) {
      if (!orgId) return []
      try {
        const params = { bizOrg: orgId, OperatorType: operatorType }
        const res = await getSalesmanList(params)
        const { code, msg, data } = res

        if (code === 200) {
          return data?.data || []
        } else {
          this.$message.error(msg)
          return []
        }
      } catch (error) {
        console.error(`获取${operatorType}列表失败:`, error)
        return []
      }
    },
    // 领料人远程方法
    async ApplicantRemoteMethod() {
      this.applicantList = await this.fetchEmployeeList(this.form.fpickorgid, 'XSY')
    },
    // 仓管员远程方法
    async StockerRemoteMethod() {
      this.stockerList = await this.fetchEmployeeList(this.form.fstockorgid, 'WHY')
    },
    // 关闭弹窗前的处理
    beforeClose() {
      this.handleClose()
    },
    // 关闭弹窗
    async handleClose(flag = false) {
      if (this.fid && !this.isCreate && !this.hasSuccessfully) {
        try {
          await deleteOutboundV2({ id: this.fid })
          this.fid = undefined
          this.$nextTick(() => {
            this.open = false
            this.$emit('callBack', flag)
          })
        } catch (error) {
          this.$message.error(error.message)
        }
      } else {
        this.open = false
        this.$emit('callBack', flag)
      }
    },
    // 表单重置
    reset() {
      this.form = {
        entities: [], // 明细信息
        fbilltypeid: undefined, // 单据类型
        fbiztype: undefined, // 业务类型
        fcustid: undefined, // 客户
        fcustname: undefined, // 客户名称
        fdate: undefined, // 日期
        fdeptid: undefined, // 领料部门
        fid: undefined, // 主键
        fnote: undefined, // 备注
        fownerid: undefined, // 货主
        fownerName: undefined, // 货主名称
        fownertypeid: undefined, // 货主类型
        fpickerid: undefined, // 领料人
        fpickorgid: undefined, // 领用组织
        fscmjgdy: undefined, // 跟单员
        fscmjGdyName: undefined, // 跟单员名称
        fstockdirect: undefined, // 库存方向，默认普通
        fstockerid: undefined, // 仓管员
        fstockorgid: undefined // 库存组织
      }
      this.resetForm('form')
      this.isPush = false
      this.isCreate = false
      this.hasSuccessfully = false
    },
    // 创建其他出库单
    async handleCreate(arr = []) {
      this.reset()
      this.isCreate = true
      // 添加加载动画
      const loading = this.$loading({
        lock: true,
        text: '加载物料信息中...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      try {
        const entities = []
        // 使用 Promise.all 等待所有物料详情请求完成
        const materialPromises = arr
          .filter(item => item.materialCode)
          .map(async item => {
            try {
              const res = await getMaterialDetail({ number: item.materialCode })
              if (res.code === 200) {
                const materialInfo = res?.data?.result?.result || {}
                return {
                  fmaterialid: materialInfo.Number || undefined, // 物料编码
                  fmaterialname: (materialInfo.Name && this.getString(materialInfo.Name)) || undefined, // 物料名称
                  fmaterialmodel: (materialInfo.Specification && this.getString(materialInfo.Specification)) || undefined, // 规格型号
                  funitid: materialInfo?.MaterialBase?.[0]?.BaseUnitId?.Number || undefined, // 单位
                  fqty: item?.fqty || undefined, // 实发数量
                  fstockid: undefined, // 发货仓库
                  fstockname: undefined, // 发货仓库名称
                  fkeepertypeid: 'BD_KeeperOrg', // 保管者类型
                  fkeeperid: undefined, // 保管者
                  fkeepername: undefined, // 保管者名称
                  flot: undefined, // 批号
                  fownerid: undefined, // 货主
                  fownerName: undefined, // 货主名称
                  fentrynote: item?.fentrynote || undefined, // 备注
                  fscmjjsprice: undefined, // 结算单价
                  fscmjjsamount: undefined // 结算金额
                }
              }
              return null
            } catch (error) {
              console.error(`获取物料详情失败 (${item.materialCode}):`, error)
              return null
            }
          })
        const results = await Promise.all(materialPromises)
        entities.push(...results.filter(Boolean))
        // 设置表单数据
        this.form = {
          entities, // 明细信息
          fbilltypeid: this.billTypeOptions[0].value || undefined, // 单据类型
          fbiztype: this.billTypeOptions[0].businessType || undefined, // 业务类型
          fdate: new Date(), // 日期
          fstockorgid: '102', // 库存组织
          fstockdirect: 'GENERAL', // 库存方向，默认普通
          fnote: '报修报废' // 备注
        }
        this.$nextTick(() => {
          this.handleChangeStockOrg('102')
        })
        this.title = '新增其他出库单'
        this.open = true
      } catch (error) {
        console.error('创建其他出库单失败:', error)
        this.$message.error('加载物料信息失败，请重试')
      } finally {
        loading.close()
      }
    },
    // 数据初始化
    async initPush(fid, type = undefined) {
      if (!fid) {
        this.$message.warning('参数错误，请刷新页面重试')
        return
      }
      const loading = this.$loading({
        lock: true,
        text: '加载中...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      try {
        this.reset()
        this.isPush = type == 'push'
        const res = await getOutboundDetail2({ fid })
        const { code, msg, data } = res
        if (code === 200) {
          this.processInitialData(data, fid)
          this.open = true
          // 异步加载员工列表
          this.$nextTick(() => {
            this.ApplicantRemoteMethod()
            this.StockerRemoteMethod()
          })
        } else {
          this.$message.error(msg)
        }
      } catch (error) {
        console.error('初始化数据失败:', error)
        this.$message.error('加载数据失败，请重试')
      } finally {
        loading.close()
      }
    },
    // 处理初始化数据
    processInitialData(data, fid) {
      const info = data?.result?.result || {}
      const detailEntities = info?.BillEntry || []
      // 处理明细数据
      const entities = detailEntities.map(item => ({
        fentryid: item?.Id || undefined, // 使用时需检查
        fmaterialid: item?.MaterialId?.Number || undefined, // 物料编码
        fmaterialname: (item?.MaterialId?.Name && this.getString(item?.MaterialId?.Name)) || undefined, // 物料名称
        fmaterialmodel: (item?.MaterialId?.Specification && this.getString(item?.MaterialId?.Specification)) || undefined, // 规格型号
        funitid: item?.UnitID?.Number || undefined, // 单位
        fqty: item?.Qty || undefined, // 实发数量
        fstockid: item?.StockId?.Number || undefined, // 发货仓库
        fstockname: (item?.StockId?.Name && this.getString(item?.StockId?.Name)) || undefined, // 发货仓库名称
        fkeepertypeid: item?.KeeperTypeId || 'BD_KeeperOrg', // 保管者类型
        fkeeperid: item?.KeeperId?.Number || undefined, // 保管者
        fkeepername: (item?.KeeperId?.Name && this.getString(item?.KeeperId?.Name)) || undefined, // 保管者名称
        flot: item?.Lot?.Number || undefined, // 批号
        fownerid: item?.OwnerId?.Number || undefined, // 货主
        fownerName: (item?.OwnerId?.Name && this.getString(item?.OwnerId.Name)) || undefined, // 货主名称
        fentrynote: item?.EntryNote || undefined, // 备注
        fscmjjsprice: item?.F_SCMJ_JSPrice || undefined, // 结算单价
        fscmjjsamount: item?.F_SCMJ_JSAmount || undefined // 结算金额
      }))
      // 设置表单数据
      this.form = {
        entities, // 明细信息
        fbilltypeid: info?.BillTypeID?.Number || undefined, // 单据类型
        fbiztype: info?.BizType || undefined, // 业务类型
        fcustid: info?.CustId?.Number || undefined, // 客户
        fcustname: (info?.CustId?.Name && this.getString(info?.CustId.Name)) || undefined, // 客户名称
        fdate: info?.Date || undefined, // 日期
        fdeptid: info?.DeptId?.Number || undefined, // 领料部门
        fid, // 主键
        fnote: info?.Note || undefined, // 备注
        fownerid: info?.OwnerIdHead?.Number || undefined, // 货主
        fownerName: (info?.OwnerIdHead?.Name && this.getString(info?.OwnerIdHead.Name)) || undefined, // 货主名称
        fownertypeid: info?.OwnerTypeIdHead || undefined, // 货主类型
        fpickerid: info?.PickerId?.Number || undefined, // 领料人
        fpickorgid: info?.PickOrgId?.Number || undefined, // 领用组织
        fscmjgdy: info?.F_SCMJ_GDY?.Number || undefined, // 跟单员
        fscmjGdyName: (info?.F_SCMJ_GDY?.Name && this.getString(info?.F_SCMJ_GDY.Name)) || undefined, // 跟单员名称
        fstockdirect: info?.StockDirect || 'GENERAL', // 库存方向，默认普通
        fstockerid: info?.StockerId?.Number || undefined, // 仓管员
        fstockorgid: info?.StockOrgId?.Number || undefined // 库存组织
      }
      this.fid = fid
      this.title = '新增其他出库单'
    },
    // 获取表格合计
    getSummary(param) {
      const { columns, data } = param
      const summaryFields = ['fqty', 'fscmjjsamount']
      return columns.map((column, index) => {
        if (index === 0) return '合计'
        if (summaryFields.includes(column.property)) {
          return `${this.calculateTotal(data, column.property)}`
        }
        return ''
      })
    },
    // 计算合计
    calculateTotal(data, key) {
      const total = data.reduce((sum, item) => {
        const value = parseFloat(item[key]) || 0
        return sum + value
      }, 0)
      return parseFloat(total.toFixed(5))
    },
    // 处理单据类型
    handleChangeBillType(value) {
      const billType = this.billTypeOptions.find(item => item.value === value)
      if (billType) this.form.fbiztype = billType.businessType
    },
    // 改变库存组织
    handleChangeStockOrg(value) {
      const obj = this.ApplicationOrgId.find(item => item.value === value)
      this.$set(this.form, 'fpickorgid', value)
      this.$set(this.form, 'fcustid', undefined)
      this.$set(this.form, 'fcustname', undefined)
      this.$set(this.form, 'fdeptid', undefined)
      this.$set(this.form, 'fpickerid', undefined)
      this.$set(this.form, 'fstockerid', undefined)
      this.$set(this.form, 'fscmjgdy', undefined)
      this.$set(this.form, 'fscmjGdyName', undefined)
      this.$set(this.form, 'fownertypeid', 'BD_OwnerOrg')
      this.$set(this.form, 'fownerid', value)
      this.$set(this.form, 'fownerName', obj.FName)
      // 重新获取员工列表
      this.StockerRemoteMethod()
      this.ApplicantRemoteMethod()
    },
    // 改变领用组织
    handleChangePickOrg(value) {
      this.$set(this.form, 'fcustid', undefined)
      this.$set(this.form, 'fcustname', undefined)
      this.$set(this.form, 'fdeptid', undefined)
      this.$set(this.form, 'fpickerid', undefined)
      // 重新获取领料人列表
      this.ApplicantRemoteMethod()
    },
    // 改变领用部门
    handleChangeStockDept(value) {
      this.$set(this.form, 'fpickerid', undefined)
    },
    // 客户回调
    handleCustomerSearchSelect(event) {
      this.$set(this.form, 'fcustid', event.Number)
      this.$set(this.form, 'fcustname', event.Name)
    },
    // 跟单员回调
    handleStaffSearchSelect(event) {
      this.$set(this.form, 'fscmjGdy', event.FNumber)
      this.$set(this.form, 'fscmjGdyName', event.FName)
    },
    // 改变货主类型
    handleChangeOwnerType(value) {
      this.$set(this.form, 'fownerid', undefined)
      this.$set(this.form, 'fownerName', undefined)
    },
    // 货主为供应商回调
    handleSupplierSearchSelect(event) {
      this.$set(this.form, 'fownerid', event.FNumber)
      this.$set(this.form, 'fownerName', event.FName)
    },
    // 货主为客户回调（第二个，用于货主选择）
    handleOwnerCustomerSearchSelect(event) {
      this.$set(this.form, 'fownerid', event.Number)
      this.$set(this.form, 'fownerName', event.Name)
    },
    // 数量变化处理
    handleQtyChange(row) {
      const qty = parseFloat(row.fqty) || 0
      const price = parseFloat(row.fscmjjsprice) || 0
      row.fscmjjsamount = parseFloat((qty * price).toFixed(5))
      this.$forceUpdate()
    },
    // 单价变化处理
    handlePriceChange(row) {
      const qty = parseFloat(row.fqty) || 0
      const price = parseFloat(row.fscmjjsprice) || 0
      row.fscmjjsamount = parseFloat((qty * price).toFixed(5))
      this.$forceUpdate()
    },
    // 选择仓库
    handleStockSelect(stock, row) {
      this.$set(row, 'fstockid', stock.Number)
      this.$set(row, 'fstockname', stock.Name)
    },
    // 提交表单
    async handleSubmit() {
      try {
        const valid = await this.$refs.form.validate()
        if (!valid) return
        // 处理明细信息，设置保管者类型和保管者
        const processedForm = {
          ...this.form,
          entities: this.form.entities.map(item => ({
            ...item,
            fkeepertypeid: item.fkeepertypeid || 'BD_KeeperOrg', // 保管者类型为业务组织
            fkeeperid: item.fkeeperid || this.form.fstockorgid // 保管者为库存组织
          }))
        }
        this.loading = true
        const res = await saveOutbound(processedForm)
        const { code, msg } = res
        if (code === 200) {
          this.$message.success('操作成功')
          this.hasSuccessfully = true
          if (this.isPush) {
            const { number } = res.data
            if (number) {
              this.open = false
              this.$nextTick(() => {
                this.$refs.misDeliveryDetail.getInfo({ BillNo: number })
              })
            } else {
              this.open = false
              this.$emit('callBack', true)
            }
          } else {
            this.open = false
            this.$emit('callBack', true)
          }
        } else {
          this.$message.error(msg)
        }
      } catch (error) {
        console.error('提交失败:', error)
      } finally {
        this.loading = false
      }
    },
    // 物料详情
    handleMaterialNumber(number) {
      if (number && this.$parent.handleMaterialNumber) {
        this.$parent.handleMaterialNumber(number)
      }
    }
  }
}
</script>
<style scoped lang="scss">
@import '@/assets/styles/custom-new.scss';
.full-width {
  width: 100%;
}
::v-deep {
  .custom-table {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    border-bottom: 0;
    margin-bottom: 20px;
    .el-form-item {
      margin-top: 10px !important;
      margin-bottom: 10px !important;
      .el-form-item__error {
        top: 95%;
        padding-top: 0;
      }
    }
    .el-input--prefix .el-input__inner {
      padding-left: 20px;
      padding-right: 20px;
    }
    .el-input--prefix.el-date-editor--date .el-input__inner {
      padding-left: 30px;
    }
    .el-date-editor--datetime .el-input__inner {
      padding-left: 30px;
      padding-right: 30px;
    }
  }
}
</style>
