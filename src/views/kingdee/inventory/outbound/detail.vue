<template>
  <div>
    <el-dialog v-dialogDragBox title="其他出库单详情" :visible.sync="open" width="90%" class="custom-dialog" :before-close="beforeClose">
      <div style="padding: 0 20px">
        <div class="kindeeButton" :key="info.DocumentStatus">
          <template v-for="(btn, index) in buttons">
            <el-button :key="index" :type="btn.type" size="medium" v-if="checkStatus(btn.status)" @click="handleKingdeeDo(btn.action)">{{ btn.text }}</el-button>
            <el-button :key="'disabled-' + index" :type="btn.type" size="medium" disabled v-else>{{ btn.text }}</el-button>
          </template>
        </div>
        <el-tabs v-model="activeName" type="card" @tab-click="handleClick" class="custom-tabs">
          <el-tab-pane label="基本信息" name="first"></el-tab-pane>
          <el-tab-pane label="其他" name="second"></el-tab-pane>
        </el-tabs>
        <el-descriptions border :column="4" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 4 - 7em - 22px)' }" v-show="activeName === 'first'">
          <!-- 单据编号 -->
          <el-descriptions-item label="单据编号">{{ info.BillNo }}</el-descriptions-item>
          <!-- 单据类型 -->
          <el-descriptions-item label="单据类型">{{ info.BillTypeID && getString(info.BillTypeID.Name) }}</el-descriptions-item>
          <!-- 库存组织 -->
          <el-descriptions-item label="库存组织">{{ info.StockOrgId && getString(info.StockOrgId.Name) }}</el-descriptions-item>
          <!-- 领用组织 -->
          <el-descriptions-item label="领用组织">{{ info.PickOrgId && getString(info.PickOrgId.Name) }}</el-descriptions-item>
          <!-- 库存方向 -->
          <el-descriptions-item label="库存方向">{{ info.StockDirect && StockDirect[info.StockDirect] }}</el-descriptions-item>
          <!-- 日期 -->
          <el-descriptions-item label="日期">{{ info.Date && parseTime(info.Date, '{y}-{m}-{d}') }}</el-descriptions-item>
          <!-- 客户 -->
          <el-descriptions-item label="客户">{{ info.CustId && getString(info.CustId.Name) }}</el-descriptions-item>
          <!-- 领料部门 -->
          <el-descriptions-item label="领料部门">{{ info.DeptId && getString(info.DeptId.Name) }}</el-descriptions-item>
          <!-- 领料人 -->
          <el-descriptions-item label="领料人">{{ info.PickerId && getString(info.PickerId.Name) }}</el-descriptions-item>
          <!-- 仓管员 -->
          <el-descriptions-item label="仓管员">{{ info.StockerId && getString(info.StockerId.Name) }}</el-descriptions-item>
          <!-- 单据状态 -->
          <el-descriptions-item label="单据状态">{{ getDocumentStatusLabel(info.DocumentStatus) }}</el-descriptions-item>
          <!-- 跟单员 -->
          <el-descriptions-item label="跟单员">{{ info.F_SCMJ_GDY && getString(info.F_SCMJ_GDY.Name) }}</el-descriptions-item>
          <!-- 业务类型 -->
          <el-descriptions-item label="业务类型">{{ info.BizType && BizType[info.BizType] }}</el-descriptions-item>
          <!-- 货主类型 -->
          <el-descriptions-item label="货主类型">{{ info.OwnerTypeIdHead && getOptionLabel(OwnerTypeIdHeadOptions, info.OwnerTypeIdHead) }}</el-descriptions-item>
          <!-- 货主 -->
          <el-descriptions-item label="货主">{{ info.OwnerIdHead && getString(info.OwnerIdHead.Name) }}</el-descriptions-item>
          <!-- 备注 -->
          <el-descriptions-item label="备注" :span="4">{{ info.Note }}</el-descriptions-item>
        </el-descriptions>
        <el-descriptions border :column="3" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 3 - 7em - 22px)' }" v-show="activeName === 'second'">
          <!-- 创建人 -->
          <el-descriptions-item label="创建人">{{ info.CreatorId && info.CreatorId.Name }}</el-descriptions-item>
          <!-- 创建日期 -->
          <el-descriptions-item label="创建日期">{{ info.CreateDate && parseTime(info.CreateDate, '{y}-{m}-{d}') }}</el-descriptions-item>
          <!-- 最后修改人 -->
          <el-descriptions-item label="最后修改人">{{ info.ModifierId && info.ModifierId.Name }}</el-descriptions-item>
          <!-- 最后修改日期 -->
          <el-descriptions-item label="最后修改日期">{{ info.ModifyDate && parseTime(info.ModifyDate, '{y}-{m}-{d}') }}</el-descriptions-item>
          <!-- 审核人 -->
          <el-descriptions-item label="审核人">{{ info.ApproverId && info.ApproverId.Name }}</el-descriptions-item>
          <!-- 审核日期 -->
          <el-descriptions-item label="审核日期">{{ info.ApproveDate && parseTime(info.ApproveDate, '{y}-{m}-{d}') }}</el-descriptions-item>
          <!-- 作废状态 -->
          <el-descriptions-item label="作废状态">{{ info.CancelStatus && info.CancelStatus === 'A' ? '未作废' : '已作废' }}</el-descriptions-item>
          <!-- 作废人 -->
          <el-descriptions-item label="作废人">{{ info.CancellerId && info.CancellerId.Name }}</el-descriptions-item>
          <!-- 作废日期 -->
          <el-descriptions-item label="作废日期">{{ info.CancelDate && parseTime(info.CancelDate, '{y}-{m}-{d}') }}</el-descriptions-item>
        </el-descriptions>
        <!-- 明细信息 -->
        <el-tabs v-model="detailActiveName" type="card" @tab-click="handleDetailClick" class="custom-tabs">
          <el-tab-pane label="明细信息" name="Dfirst"></el-tab-pane>
          <el-tab-pane label="物料数据" name="Dsecond"></el-tab-pane>
        </el-tabs>
        <el-table ref="detailTable" :data="info.BillEntry" class="custom-table" highlight-current-row @current-change="handleCurrentChange" :setCurrentRow="setCurrentRow" v-show="detailActiveName === 'Dfirst'" show-summary :summary-method="getSummary">
          <!-- 序号 -->
          <el-table-column type="index" label="序号" align="center"></el-table-column>
          <!-- 物料编码 -->
          <el-table-column label="物料编码" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.MaterialId && scope.row.MaterialId.Number }}</template>
          </el-table-column>
          <!-- 物料名称 -->
          <el-table-column label="物料名称" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.MaterialId && getString(scope.row.MaterialId.Name) }}</template>
          </el-table-column>
          <!-- 规格型号 -->
          <el-table-column label="规格型号" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.MaterialId && getString(scope.row.MaterialId.Specification) }}</template>
          </el-table-column>
          <!-- 单位 -->
          <el-table-column label="单位" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.UnitID && getString(scope.row.UnitID.Name) }}</template>
          </el-table-column>
          <!-- 实发数量 -->
          <el-table-column prop="BaseQty" label="实发数量" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.BaseQty }}</template>
          </el-table-column>
          <!-- 发货仓库 -->
          <el-table-column label="发货仓库" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.StockId && getString(scope.row.StockId.Name) }}</template>
          </el-table-column>
          <!-- 仓位 -->
          <el-table-column label="仓位" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.StockLocId && getString(scope.row.StockLocId.Name) }}</template>
          </el-table-column>
          <!-- 成本价 -->
          <el-table-column label="成本价" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.Price }}</template>
          </el-table-column>
          <!-- 总成本 -->
          <el-table-column prop="Amount" label="总成本" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.Amount }}</template>
          </el-table-column>
          <!-- 批号 -->
          <el-table-column label="批号" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.Lot && scope.row.Lot.Number }}</template>
          </el-table-column>
          <!-- 货主 -->
          <el-table-column label="货主" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.OwnerId && getString(scope.row.OwnerId.Name) }}</template>
          </el-table-column>
          <!-- 备注 -->
          <el-table-column label="备注" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.EntryNote }}</template>
          </el-table-column>
          <!-- 结算单价 -->
          <el-table-column label="结算单价" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.F_SCMJ_JSPrice }}</template>
          </el-table-column>
          <!-- 结算金额 -->
          <el-table-column prop="F_SCMJ_JSAmount" label="结算金额" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.F_SCMJ_JSAmount }}</template>
          </el-table-column>
        </el-table>
        <el-descriptions border :column="3" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 3 - 7em - 22px)' }" v-show="detailActiveName === 'Dsecond'">
          <!-- 物料编码 -->
          <el-descriptions-item label="物料编码">{{ setCurrentRow && setCurrentRow.MaterialId && setCurrentRow.MaterialId.Number }}</el-descriptions-item>
          <!-- 物料名称 -->
          <el-descriptions-item label="物料名称">{{ setCurrentRow && setCurrentRow.MaterialId && getString(setCurrentRow.MaterialId.Name) }}</el-descriptions-item>
          <!-- 规格型号 -->
          <el-descriptions-item label="规格型号">{{ setCurrentRow && setCurrentRow.MaterialId && getString(setCurrentRow.MaterialId.Specification) }}</el-descriptions-item>
          <!-- 辅助属性(空白) -->
          <el-descriptions-item label="辅助属性">{{ setCurrentRow && setCurrentRow.AuxPropId && getString(setCurrentRow.AuxPropId.Name) }}</el-descriptions-item>
          <!-- 单位 -->
          <el-descriptions-item label="单位">{{ setCurrentRow && setCurrentRow.UnitID && getString(setCurrentRow.UnitID.Name) }}</el-descriptions-item>
          <!-- 实发数量 -->
          <el-descriptions-item label="实发数量">{{ setCurrentRow && setCurrentRow.BaseQty }}</el-descriptions-item>
          <!-- 辅单位 -->
          <el-descriptions-item label="辅单位">{{ setCurrentRow && setCurrentRow.ExtAuxUnitId && getString(setCurrentRow.ExtAuxUnitId.Name) }}</el-descriptions-item>
          <!-- 实发数量(辅单位) -->
          <el-descriptions-item label="实发数量(辅单位)">{{ setCurrentRow && setCurrentRow.ExtAuxUnitQty }}</el-descriptions-item>
          <!-- 发货仓库 -->
          <el-descriptions-item label="发货仓库">{{ setCurrentRow && setCurrentRow.StockId && getString(setCurrentRow.StockId.Name) }}</el-descriptions-item>
          <!-- 仓位 -->
          <el-descriptions-item label="仓位">{{ setCurrentRow && setCurrentRow.StockLocId && getString(setCurrentRow.StockLocId.Name) }}</el-descriptions-item>
          <!-- 批号 -->
          <el-descriptions-item label="批号">{{ setCurrentRow && setCurrentRow.Lot && setCurrentRow.Lot.Number }}</el-descriptions-item>
          <!-- 保质期单位 -->
          <el-descriptions-item label="保质期单位">{{ setCurrentRow && setCurrentRow.KFPeriodUnit }}</el-descriptions-item>
          <!-- 保质期 -->
          <el-descriptions-item label="保质期">{{ setCurrentRow && setCurrentRow.KFPeriod }}</el-descriptions-item>
          <!-- 生产日期 -->
          <el-descriptions-item label="生产日期">{{ setCurrentRow && setCurrentRow.ProduceDate && parseTime(setCurrentRow.ProduceDate, '{y}-{m}-{d}') }}</el-descriptions-item>
          <!-- 有效期至 -->
          <el-descriptions-item label="有效期至">{{ setCurrentRow && setCurrentRow.ExpiryDate && parseTime(setCurrentRow.ExpiryDate, '{y}-{m}-{d}') }}</el-descriptions-item>
          <!-- 库存状态 -->
          <el-descriptions-item label="库存状态">{{ setCurrentRow && setCurrentRow.StockStatusId && getString(setCurrentRow.StockStatusId.Name) }}</el-descriptions-item>
          <!-- BOM版本 -->
          <el-descriptions-item label="BOM版本">{{ setCurrentRow && setCurrentRow.BomId && getString(setCurrentRow.BomId.Name) }}</el-descriptions-item>
          <!-- 计划跟踪号 -->
          <el-descriptions-item label="计划跟踪号">{{ setCurrentRow && setCurrentRow.MtoNo }}</el-descriptions-item>
          <!-- 货主 -->
          <el-descriptions-item label="货主">{{ setCurrentRow && setCurrentRow.OwnerId && getString(setCurrentRow.OwnerId.Name) }}</el-descriptions-item>
          <!-- 保管者类型 -->
          <el-descriptions-item label="保管者类型">{{ setCurrentRow && setCurrentRow.KeeperTypeId && KeeperType[setCurrentRow.KeeperTypeId] }}</el-descriptions-item>
          <!-- 保管者 -->
          <el-descriptions-item label="保管者">{{ setCurrentRow && setCurrentRow.KeeperId && getString(setCurrentRow.KeeperId.Name) }}</el-descriptions-item>
          <!-- 备注 -->
          <el-descriptions-item label="备注">{{ setCurrentRow && setCurrentRow.EntryNote }}</el-descriptions-item>
          <!-- 源单类型 -->
          <el-descriptions-item label="源单类型">{{ setCurrentRow && setCurrentRow.SrcBillTypeId && getOptionLabel(SrcBillTypeOptions, setCurrentRow.SrcBillTypeId) }}</el-descriptions-item>
          <!-- 源单编号 -->
          <el-descriptions-item label="源单编号">{{ setCurrentRow && setCurrentRow.SrcBillNo }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { kingdee } from '@/minix'
import { getOutboundDetail, submitOutbound, auditOutbound, cancelOutbound, deleteOutbound, unAuditOutbound } from '@/api/kingdee/inventory/outbound'

export default {
  name: 'OutboundDetail',
  mixins: [kingdee],
  data() {
    return {
      open: false,
      info: {},
      setCurrentRow: {},
      activeName: 'first',
      detailActiveName: 'Dfirst',
      buttons: [
        { text: '提交', type: 'primary', action: 'submit', status: ['A', 'D'] },
        { text: '审核', type: 'warning', action: 'audit', status: ['B'] },
        { text: '撤销', type: 'info', action: 'revoke', status: ['B'] },
        { text: '删除', type: 'danger', action: 'delete', status: ['A', 'D'] },
        { text: '反审', type: 'warning', action: 'unAudit', status: ['C'] }
        // { text: '下推', type: 'success', action: 'push', status: ['C'] }
      ],
      // 库存方向
      StockDirect: {
        GENERAL: '普通',
        INVENTORY: '库存'
      },
      // 业务类型
      BizType: {
        0: '物料领用',
        1: '资产领用',
        2: 'VMI业务',
        3: '费用物料领用',
        4: '库存调整',
        5: '数量核算'
      },
      // 货主类型
      OwnerTypeIdHeadOptions: [
        { label: '业务组织', value: 'BD_OwnerOrg' },
        { label: '供应商', value: 'BD_Supplier' },
        { label: '客户', value: 'BD_Customer' }
      ],
      // 保管者类型
      KeeperType: {
        BD_KeeperOrg: '业务组织',
        BD_Supplier: '供应商',
        BD_Customer: '客户'
      },
      // 源单类型
      SrcBillTypeOptions: [
        { value: 'CMK_LS_StoresDelivery', label: '门店配货单' },
        { value: 'CMK_RT_SendGift', label: '门店派发单' },
        { value: 'CMK_VIP_PointsExchange', label: '积分兑换单' },
        { value: 'CP_BBCOrder', label: 'BBC订单' },
        { value: 'CP_SaleOrder', label: '网上订单' },
        { value: 'CRM_RepairOrder', label: '服务工单' },
        { value: 'ENG_BomExpandBill', label: 'BOM正向展开模型(禁止发布)' },
        { value: 'ESS_ChannelOutStock', label: '渠道出库单' },
        { value: 'MEM_FEEREUESTGOODS', label: '助销品申请单' },
        { value: 'RPM_RPStatements', label: '返利结算单' },
        { value: 'SAL_DELIVERYNOTICE', label: '发货通知单' },
        { value: 'SAL_RETURNNOTICE', label: '退货通知单' },
        { value: 'STK_InStock', label: '采购入库单' },
        { value: 'STK_MisDelivery', label: '其他出库单' },
        { value: 'STK_OutStockApply', label: '出库申请单' }
      ]
    }
  },
  methods: {
    // 检查状态
    checkStatus(allowedStatus) {
      return allowedStatus.includes(this.info.DocumentStatus)
    },
    // 切换tab
    handleClick(tab, event) {
      this.activeName = tab.name
    },
    handleDetailClick(tab, event) {
      this.detailActiveName = tab.name
    },
    // 获取详情
    getInfo(row = {}) {
      if (!row.BillNo) {
        this.$message.error('参数错误，请重新选择')
        return
      }
      getOutboundDetail({ billNo: row.BillNo }).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          const { result } = data
          this.activeName = 'first'
          this.detailActiveName = 'Dfirst'
          this.open = !!result?.result
          this.info = result?.result || {}
          this.$nextTick(() => {
            this.setCurrentRow = result?.result?.BillEntry?.[0] || {}
            if (this.$refs.detailTable) {
              this.$refs.detailTable.setCurrentRow(this.setCurrentRow)
              this.$refs.detailTable.bodyWrapper.scrollLeft = 0
            }
          })
        } else this.$message.error(msg)
      })
    },
    // 当前行
    handleCurrentChange(val) {
      this.setCurrentRow = val
    },
    // 关闭弹框
    beforeClose() {
      this.handleClose(false)
      this.$emit('update', { billNo: this.info.BillNo, newStatus: this.info.DocumentStatus })
    },
    // 关闭
    handleClose(flag = false) {
      this.open = false
      this.$emit('callBack', flag)
    },
    // 金蝶操作
    // prettier-ignore
    handleKingdeeDo(type) {
      const number = this.info.BillNo
      if (!number) return
      switch (type) {
        case 'submit':
          // 提交
          this.$modal.confirm('确认要提交该其他出库单吗？').then(() => {
            submitOutbound({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('提交成功')
                this.$set(this.info, 'DocumentStatus', 'B')
              } else this.$message.error(msg)
            })
          })
          break
        case 'audit':
          // 审核
          this.$modal.confirm('确认要审核该其他出库单吗？').then(() => {
            auditOutbound({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('审核成功')
                this.$set(this.info, 'DocumentStatus', 'C')
              } else this.$message.error(msg)
            })
          })
          break
        case 'revoke':
          // 撤销
          this.$modal.confirm('确认要撤销该其他出库单吗？').then(() => {
            cancelOutbound({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('撤销成功')
                this.$set(this.info, 'DocumentStatus', 'D')
              } else this.$message.error(msg)
            })
          })
          break
        case 'delete':
          // 删除
          this.$modal.confirm('确认要删除该其他出库单吗？').then(() => {
            deleteOutbound({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('删除成功')
                this.handleClose(true)
              } else this.$message.error(msg)
            })
          })
          break
        case 'unAudit':
          // 反审
          this.$modal.confirm('确认要反审该其他出库单吗？').then(() => {
            unAuditOutbound({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('反审成功')
                this.$set(this.info, 'DocumentStatus', 'D')
              } else this.$message.error(msg)
            })
          })
          break
        case 'push':          
          // 下推
          this.pushForm = {
            number: this.info.BillNo,
            target: this.pushTarget[0].value || undefined
          }
          this.pushOpen = true
          break
      }
    },
    // 获取表格合计
    getSummary(param) {
      const { columns, data } = param
      const summaryFields = ['BaseQty', 'Amount', 'F_SCMJ_JSAmount']
      return columns.map((column, index) => {
        if (index === 0) return '合计'
        if (summaryFields.includes(column.property)) {
          return `${this.calculateTotal(data, column.property)}`
        }
        return ''
      })
    },
    // 计算合计
    calculateTotal(data, key) {
      const total = data.reduce((sum, item) => {
        const value = parseFloat(item[key]) || 0
        return sum + value
      }, 0)
      return parseFloat(total.toFixed(5))
    }
  }
}
</script>
<style scoped lang="scss">
@import '@/assets/styles/custom-new.scss';
::v-deep {
  .el-descriptions__header {
    margin-bottom: 0;
  }
  .el-table.custom-table .el-table__body-wrapper .el-table__row:hover td.el-table__cell {
    background-color: #eaf1fe !important;
  }
}
.customTitle {
  font-size: 16px;
  font-weight: normal;
  line-height: 60px;
  color: $font;
}
.kindeeButton {
  display: flex;
  align-items: center;
}
.custom-push-target {
  .el-col.el-col-12 {
    .el-radio {
      height: 30px;
      display: flex;
      align-items: center;
    }
  }
}
.custom-tabs {
  margin-top: 15px;
  ::v-deep {
    .el-tabs__header {
      .el-tabs__nav {
        border: 0;
      }
      .el-tabs__item {
        background-color: #eef2f8;
        border-radius: 5px 5px 0 0;
        border-left-width: 0;
        border-right-width: 0;
        &.is-active {
          background-color: $blue;
          color: $white;
        }
      }
      .el-tabs__item + .el-tabs__item {
        margin-left: 2px;
      }
    }
  }
}
</style>
