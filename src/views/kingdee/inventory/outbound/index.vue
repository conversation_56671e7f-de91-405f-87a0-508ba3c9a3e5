<template>
  <div class="newBox bgcf9 vh-85">
    <!-- 搜索 -->
    <div class="custom-search flex" style="padding-top: 18px" :style="{ justifyContent: showSearch ? 'space-between' : 'flex-end' }">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" @submit.native.prevent>
        <el-form-item label="库存组织" prop="stockOrg">
          <el-select v-model="queryParams.stockOrg" placeholder="请选择库存组织" clearable>
            <el-option v-for="item in ApplicationOrgId" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="单据编号" prop="billNo">
          <el-input v-model="queryParams.billNo" placeholder="请输入单据编号" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="物料名称" prop="name">
          <el-input v-model="queryParams.name" placeholder="请输入物料名称" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="单据状态" prop="documentStatus">
          <el-select v-model="queryParams.documentStatus" placeholder="请选择单据状态" clearable>
            <el-option v-for="item in DocumentStatusOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="small" @click="handleResetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns" isSetitem @updateColumns="updateColumns"></right-toolbar>
    </div>

    <!-- 列表 -->
    <div style="padding: 15px 20px">
      <el-table v-loading="loading" ref="allTable" border :data="list" row-key="id" style="width: 100%" class="custom-table" :span-method="objectSpanMethod">
        <el-table-column align="center" type="index" label="序号" v-if="columns[0].visible"></el-table-column>
        <!-- 单据类型 -->
        <el-table-column align="center" prop="BillType" label="单据类型" show-overflow-tooltip v-if="columns[1].visible"></el-table-column>
        <!-- 库存方向 -->
        <el-table-column align="center" prop="StockDirect" label="库存方向" show-overflow-tooltip v-if="columns[2].visible">
          <template slot-scope="{ row }">{{ StockDirect[row.StockDirect] }}</template>
        </el-table-column>
        <!-- 单据状态 -->
        <el-table-column align="center" prop="DocumentStatus" label="单据状态" show-overflow-tooltip v-if="columns[3].visible">
          <template slot-scope="{ row }">{{ getDocumentStatusLabel(row.DocumentStatus) }}</template>
        </el-table-column>
        <!-- 业务类型 -->
        <el-table-column align="center" prop="BizType" label="业务类型" show-overflow-tooltip v-if="columns[4].visible">
          <template slot-scope="{ row }">{{ BizType[row.BizType] }}</template>
        </el-table-column>
        <!-- 单据编号 -->
        <el-table-column align="center" prop="BillNo" label="单据编号" show-overflow-tooltip v-if="columns[5].visible">
          <template slot-scope="{ row }">
            <span class="table-link" @click="handleDetail(row)">{{ row.BillNo }}</span>
          </template>
        </el-table-column>
        <!-- 日期 -->
        <el-table-column align="center" prop="Date" label="日期" show-overflow-tooltip v-if="columns[6].visible">
          <template slot-scope="{ row }">{{ parseTime(row.Date, '{y}-{m}-{d}') }}</template>
        </el-table-column>
        <!-- 物料编码 -->
        <el-table-column align="center" prop="MaterialNumber" label="物料编码" show-overflow-tooltip v-if="columns[7].visible">
          <template slot-scope="{ row }">
            <span class="table-link" @click="handleMaterialNumber(row.MaterialNumber)">{{ row.MaterialNumber }}</span>
          </template>
        </el-table-column>
        <!-- 物料名称 -->
        <el-table-column align="center" prop="MaterialName" label="物料名称" show-overflow-tooltip v-if="columns[8].visible"></el-table-column>
        <!-- 规格型号 -->
        <el-table-column align="center" prop="MaterialModel" label="规格型号" show-overflow-tooltip v-if="columns[9].visible"></el-table-column>
        <!-- 单位 -->
        <el-table-column align="center" prop="Unit" label="单位" show-overflow-tooltip v-if="columns[10].visible"></el-table-column>
        <!-- 实发数量 -->
        <el-table-column align="center" prop="BaseQty" label="实发数量" show-overflow-tooltip v-if="columns[11].visible"></el-table-column>
        <!-- 发货仓库 -->
        <el-table-column align="center" prop="StockName" label="发货仓库" show-overflow-tooltip v-if="columns[12].visible"></el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="custom-pagination">
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.limit" @pagination="getList" />
      </div>
    </div>
    <!-- 物料详情 -->
    <material-detail ref="materialDetail" @callBack="handleCallBack" v-if="showMaterialDetail" />
    <!-- 详情 -->
    <outbound-detail ref="outboundDetail" @callBack="handleCallBack" @update="handleUpdate" v-if="showOutboundDetail" />
  </div>
</template>
<script>
import { kingdee } from '@/minix'
import { getOutboundList } from '@/api/kingdee/inventory/outbound'
import MaterialDetail from '@/views/kingdee/material/detail'
import OutboundDetail from './detail'

export default {
  name: 'Outbound',
  mixins: [kingdee],
  components: { MaterialDetail, OutboundDetail },
  data() {
    return {
      showMaterialDetail: false,
      showOutboundDetail: false,
      // 搜索
      queryParams: {
        pageNum: 1, // 当前页码
        startRow: 0, // 开始行索引
        limit: 10, // 最大行数
        stockOrg: undefined, // 库存组织
        billNo: undefined, // 单据编号
        name: undefined, // 物料名称
        number: undefined, // 物料编码
        documentStatus: undefined // 单据状态
      },
      // 列表
      list: [],
      total: 0,
      loading: false,
      // 库存方向
      StockDirect: {
        GENERAL: '普通',
        INVENTORY: '库存'
      },
      // 业务类型
      BizType: {
        0: '物料领用',
        1: '资产领用',
        2: 'VMI业务',
        3: '费用物料领用',
        4: '库存调整',
        5: '数量核算'
      },
      showSearch: true, // 列表显隐
      // 列信息
      columns: [
        { key: 0, label: `序号`, visible: true },
        { key: 1, label: `单据类型`, visible: true },
        { key: 2, label: `库存方向`, visible: true },
        { key: 3, label: `单据状态`, visible: true },
        { key: 4, label: `业务类型`, visible: true },
        { key: 5, label: `单据编号`, visible: true },
        { key: 6, label: `日期`, visible: true },
        { key: 7, label: `物料编码`, visible: true },
        { key: 8, label: `物料名称`, visible: true },
        { key: 9, label: `规格型号`, visible: true },
        { key: 10, label: `单位`, visible: true },
        { key: 11, label: `实发数量`, visible: true },
        { key: 12, label: `发货仓库`, visible: true }
      ]
    }
  },
  computed: {
    // 获取当前用户ID
    userId() {
      return this.$store.getters.info.userId
    }
  },
  created() {
    const columns = localStorage.getItem(this.userId + '.outboundColumns')
    if (columns) this.columns = JSON.parse(columns)
    // 获取列表
    this.getList()
  },
  methods: {
    // 改变显隐列缓存
    updateColumns(data = []) {
      localStorage.setItem(this.userId + '.outboundColumns', JSON.stringify(data))
    },
    // 列表
    // prettier-ignore
    getList() {
      this.loading = true
      this.queryParams.startRow = Math.max(0, (this.queryParams.pageNum - 1) * this.queryParams.limit)
      const query = { ...this.queryParams }
      delete query.pageNum
      getOutboundList(query).then(res => {
        const { code, data, msg } = res
        if (code === 200) {
          const { data: list, total } = data
          this.list = list
          this.total = total
        } else this.$message.error(msg)
      }).finally(() => {
        this.loading = false
      })
    },
    // 相同字段合并
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      const visibleColumns = this.columns.filter(col => col.visible)
      const currentColumnKey = visibleColumns[columnIndex]?.key
      if (currentColumnKey >= 1 && currentColumnKey <= 6) {
        const _row = this.calculateSpan(this.list, 'BillNo')[rowIndex]
        const _col = _row > 0 ? 1 : 0
        return {
          rowspan: _row,
          colspan: _col
        }
      }
    },
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 重置搜索
    handleResetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 查看物料
    handleMaterialNumber(number) {
      this.showMaterialDetail = true
      this.$nextTick(() => {
        this.$refs.materialDetail.getInfo(number)
      })
    },
    // 关闭物料详情
    handleCallBack(flag = false) {
      this.showMaterialDetail = false
      this.showOutboundDetail = false
      if (flag) this.getList()
    },
    // 查看详情
    handleDetail(row) {
      this.showOutboundDetail = true
      this.$nextTick(() => {
        this.$refs.outboundDetail.getInfo(row)
      })
    },
    // 更新
    handleUpdate(data) {
      const { billNo, newStatus } = data
      const index = this.list.findIndex(item => item.BillNo === billNo)
      if (index !== -1) this.list[index].DocumentStatus = newStatus
    }
  }
}
</script>
<style scoped lang="scss">
@import '@/assets/styles/custom-new.scss';
</style>
