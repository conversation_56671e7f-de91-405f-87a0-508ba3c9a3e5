<template>
  <div class="newBox" :class="{ 'vh-85': !isPopup, bgcf9: !isPopup }">
    <!-- 搜索 -->
    <div class="custom-search flex" style="padding-top: 18px">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" @submit.native.prevent>
        <!-- 使用组织 -->
        <el-form-item label="使用组织" prop="useOrg" v-if="!useOrg || showUseOrg">
          <el-select v-model="queryParams.useOrg" placeholder="请选择使用组织" clearable>
            <el-option v-for="item in ApplicationOrgId" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <!-- 员工编号 -->
        <el-form-item label="员工编号" prop="number">
          <el-input v-model="queryParams.number" placeholder="请输入员工编号" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <!-- 员工姓名 -->
        <el-form-item label="员工姓名" prop="name">
          <el-input v-model="queryParams.name" placeholder="请输入员工姓名" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="small" @click="handleResetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <!-- 列表 -->
    <div style="padding: 15px 20px">
      <el-table v-loading="loading" ref="allTable" border :data="list" row-key="FNumber" style="width: 100%" class="custom-table" @row-click="handleRowClick" :highlight-current-row="isPopup">
        <el-table-column align="center" type="index" label="序号" v-if="!isPopup"></el-table-column>
        <!-- 单选按钮 -->
        <el-table-column align="center" label="选择" width="60" v-if="isPopup">
          <template slot-scope="scope">
            <el-radio v-model="selectedStaff.FNumber" :label="scope.row.FNumber" v-removeAriaHidden><span /></el-radio>
          </template>
        </el-table-column>
        <!-- 使用组织 -->
        <el-table-column align="center" prop="FUseOrg" label="使用组织" show-overflow-tooltip></el-table-column>
        <!-- 员工编号 -->
        <el-table-column align="center" prop="FNumber" label="员工编号" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <span class="table-link" @click="handleDetail(row)">{{ row.FNumber }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="FName" label="员工姓名" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" prop="FMobile" label="联系电话" show-overflow-tooltip></el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="custom-pagination">
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.limit" @pagination="getList" />
      </div>
    </div>
    <!-- 详情 -->
    <detail ref="detail" v-if="showDetail" />
  </div>
</template>
<script>
import { getStaffList } from '@/api/kingdee'
import { kingdee } from '@/minix'
import detail from './detail'

export default {
  name: 'Staff',
  props: {
    isPopup: {
      type: Boolean,
      default: false
    },
    useOrg: {
      type: String,
      default: undefined
    },
    showUseOrg: {
      type: Boolean,
      default: true
    }
  },
  mixins: [kingdee],
  components: { detail },
  data() {
    return {
      // 搜索
      queryParams: {
        pageNum: 1, // 当前页码
        startRow: 0, // 开始行索引
        limit: 10, // 最大行数
        useOrg: undefined, // 使用组织
        number: undefined, // 员工编号
        name: undefined, // 员工姓名-模糊匹配
        name2: undefined // 员工姓名-精确匹配
      },
      // 列表
      list: [],
      total: 0,
      loading: false,
      showDetail: false,
      // 选中的
      selectedStaff: {}
    }
  },
  created() {
    this.queryParams.useOrg = this.useOrg || this.ApplicationOrgId[0].value || undefined
    // 获取列表
    if (!this.isPopup) this.getList()
  },
  methods: {
    // 列表
    // prettier-ignore
    getList() {
      this.loading = true
      this.queryParams.startRow = Math.max(0, (this.queryParams.pageNum - 1) * this.queryParams.limit)
      const query = { ...this.queryParams }
      delete query.pageNum
      getStaffList(query).then(res => {
        const { code, data, msg } = res
        if (code === 200) {
          const { data: list, total } = data
          this.list = list
          this.total = total
        } else this.$message.error(msg)
      }).finally(() => {
        this.loading = false
      })
    },
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 重置搜索
    handleResetQuery(obj = {}) {
      this.resetForm('queryForm')
      if (this.isPopup) this.selectedStaff = obj || {}
      this.handleQuery()
    },
    // 行点击
    handleRowClick(row) {
      this.selectedStaff = row
      if (this.isPopup) this.$emit('selectStaff', row)
    },
    // 详情
    handleDetail(row) {
      this.showDetail = true
      this.$nextTick(() => {
        this.$refs.detail.getInfo(row)
      })
    }
  }
}
</script>
<style scoped lang="scss">
@import '@/assets/styles/custom-new.scss';
</style>
