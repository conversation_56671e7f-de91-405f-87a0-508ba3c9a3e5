<template>
  <div>
    <el-dialog v-dialogDragBox title="员工详情" :visible.sync="open" width="90%" class="custom-dialog" @close="handleClose">
      <div style="padding: 0 20px">
        <el-descriptions border :column="3" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 3 - 7em - 22px)' }">
          <template slot="title">
            <div class="customTitle">基本信息</div>
          </template>
          <!-- 创建组织 -->
          <el-descriptions-item label="创建组织">{{ info.CreateOrgId && getString(info.CreateOrgId.Name) }}</el-descriptions-item>
          <!-- 使用组织 -->
          <el-descriptions-item :span="2" label="使用组织">{{ info.UseOrgId && getString(info.UseOrgId.Name) }}</el-descriptions-item>
          <!-- 员工姓名 -->
          <el-descriptions-item label="员工姓名">{{ info.Name && getString(info.Name) }}</el-descriptions-item>
          <!-- 员工编号 -->
          <el-descriptions-item label="员工编号">{{ info.FStaffNumber }}</el-descriptions-item>
          <!-- 性别 -->
          <el-descriptions-item label="性别">{{ info.F_SCMJ_XB && getOptionLabel(GenderOptions, info.F_SCMJ_XB) }}</el-descriptions-item>
        </el-descriptions>
        <el-descriptions border :column="3" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 3 - 7em - 22px)' }">
          <template slot="title">
            <div class="customTitle">基本信息</div>
          </template>
          <!-- 移动电话 -->
          <el-descriptions-item label="移动电话">{{ info.Mobile }}</el-descriptions-item>
          <!-- 固定电话 -->
          <el-descriptions-item label="固定电话">{{ info.FTel }}</el-descriptions-item>
          <!-- 电子邮箱 -->
          <el-descriptions-item label="电子邮箱">{{ info.Email }}</el-descriptions-item>
          <!-- 联系地址 -->
          <el-descriptions-item label="联系地址" :span="3">{{ info.Address && getString(info.Address) }}</el-descriptions-item>
          <!-- 描述 -->
          <el-descriptions-item label="描述" :span="3">{{ info.Description && getString(info.Description) }}</el-descriptions-item>
        </el-descriptions>
        <div class="customTitle">明细信息</div>
        <el-table ref="detailTable" :data="info.PostEntity" class="custom-table" highlight-current-row @current-change="handleCurrentChange" :setCurrentRow="setCurrentRow">
          <!-- 序号 -->
          <el-table-column type="index" label="序号" align="center"></el-table-column>
          <!-- 工作组织 -->
          <el-table-column label="工作组织" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.WorkOrgId && getString(scope.row.WorkOrgId.Name) }}</template>
          </el-table-column>
          <!-- 所属部门 -->
          <el-table-column label="所属部门" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.PostDept && getString(scope.row.PostDept.Name) }}</template>
          </el-table-column>
          <!-- 就任岗位 -->
          <el-table-column label="就任岗位" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.Post && getString(scope.row.Post.Name) }}</template>
          </el-table-column>
          <!-- 任岗开始日期 -->
          <el-table-column label="任岗开始日期" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.StaffStartDate && parseTime(scope.row.StaffStartDate, '{y}-{m}-{d}') }}</template>
          </el-table-column>
          <!-- 是否主任岗 -->
          <el-table-column label="是否主任岗" align="center">
            <template slot-scope="scope">
              <el-checkbox v-model="scope.row.IsFirstPost" disabled></el-checkbox>
            </template>
          </el-table-column>
          <!-- 禁用状态 -->
          <el-table-column label="禁用状态" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.StaffForbidStatus && getOptionLabel(StaffForbidStatusOptions, scope.row.StaffForbidStatus) }}</template>
          </el-table-column>
          <!-- 部门全称 -->
          <el-table-column label="部门全称" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.PostDept && scope.row.PostDept.FullName && getString(scope.row.PostDept.FullName) }}</template>
          </el-table-column>
          <!-- 业务员类型 -->
          <el-table-column label="业务员类型" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.FOperatorType && getOptionLabel(OperatorTypeOptions, scope.row.FOperatorType) }}</template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getStaffDetail } from '@/api/kingdee'
import { kingdee } from '@/minix'

export default {
  mixins: [kingdee],
  data() {
    return {
      open: false,
      info: {},
      setCurrentRow: {},
      // 性别
      GenderOptions: [
        { label: '男', value: '0' },
        { label: '女', value: '1' }
      ],
      // 禁用状态
      StaffForbidStatusOptions: [
        { label: '否', value: 'A' },
        { label: '是', value: 'B' }
      ],
      // 业务员类型
      OperatorTypeOptions: [
        { label: '销售员', value: 'XSY' },
        { label: '采购员', value: 'CGY' },
        { label: '仓管员', value: 'WHY' },
        { label: '计划员', value: 'JHY' },
        { label: '财务人员', value: 'CWRY' },
        { label: '质检员', value: 'ZJY' },
        { label: '服务人员', value: 'FWRY' },
        { label: '驾驶员', value: 'JSY' },
        { label: '程序员', value: 'CXY' }
      ]
    }
  },
  methods: {
    // 获取员工信息
    getInfo(data = {}) {
      const billNo = data.FNumber
      if (!billNo) {
        this.$message.error('参数错误，请重新选择')
        return
      }
      getStaffDetail({ billNo }).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          const { result } = data
          this.open = !!result?.result
          this.info = result?.result || {}
          this.$nextTick(() => {
            this.setCurrentRow = result?.result?.PostEntity?.[0] || {}
            if (this.$refs.detailTable) {
              this.$refs.detailTable.setCurrentRow(this.setCurrentRow)
              this.$refs.detailTable.bodyWrapper.scrollLeft = 0
            }
          })
        } else this.$message.error(msg)
      })
    },
    // 当前行
    handleCurrentChange(val) {
      this.setCurrentRow = val
    },
    // 关闭
    handleClose() {
      this.open = false
      this.$emit('callBack')
    }
  }
}
</script>
<style scoped lang="scss">
@import '@/assets/styles/custom-new.scss';
::v-deep {
  .el-descriptions__header {
    margin-bottom: 0;
  }
  .el-table.custom-table .el-table__body-wrapper .el-table__row:hover td.el-table__cell {
    background-color: #eaf1fe !important;
  }
}
.customTitle {
  font-size: 16px;
  font-weight: normal;
  line-height: 60px;
  color: $font;
}
.kindeeButton {
  display: flex;
  align-items: center;
}
.custom-push-target {
  .el-col.el-col-12 {
    .el-radio {
      height: 30px;
      display: flex;
      align-items: center;
    }
  }
}
</style>
