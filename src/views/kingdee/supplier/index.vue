<template>
  <div class="newBox" :class="{ 'vh-85': !isPopup, bgcf9: !isPopup }">
    <!-- 搜索 -->
    <div class="custom-search flex" style="padding-top: 18px">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" @submit.native.prevent>
        <el-form-item label="供应商名称" prop="name">
          <el-input v-model="queryParams.name" placeholder="请输入供应商名称" clearable @keyup.enter.native="handleQuery" :style="{ width: (!isPopup && showUseOrg) || showUseOrg || isUseOrg ? '170px' : 'auto' }" />
        </el-form-item>
        <el-form-item label="使用组织" prop="useOrg" v-if="(!isPopup && showUseOrg) || showUseOrg || isUseOrg">
          <el-select v-model="queryParams.useOrg" placeholder="请选择使用组织" :style="{ width: (!isPopup && showUseOrg) || showUseOrg || isUseOrg ? '150px' : 'auto' }">
            <el-option v-for="org in ApplicationOrgId" :key="org.value" :label="org.label" :value="org.value" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="small" @click="handleResetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <!-- 列表 -->
    <div style="padding: 15px 20px">
      <el-table v-loading="loading" ref="allTable" border :data="list" row-key="FNumber" style="width: 100%" class="custom-table" @row-click="handleRowClick" :highlight-current-row="isPopup">
        <!-- 单选按钮 -->
        <el-table-column align="center" label="选择" width="60" v-if="isPopup">
          <template slot-scope="scope">
            <el-radio v-model="selectedCustomer.FNumber" :label="scope.row.FNumber" v-removeAriaHidden><span /></el-radio>
          </template>
        </el-table-column>
        <!-- 供应商编码 -->
        <el-table-column align="center" prop="FNumber" label="供应商编码" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <span class="table-link">{{ row.FNumber }}</span>
          </template>
        </el-table-column>
        <!-- 供应商名称 -->
        <el-table-column align="center" prop="FName" label="供应商名称" show-overflow-tooltip></el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="custom-pagination">
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.limit" @pagination="getList" />
      </div>
    </div>
  </div>
</template>
<script>
import { getSupplierList } from '@/api/kingdee'
import { kingdee } from '@/minix'

export default {
  props: {
    isPopup: {
      type: Boolean,
      default: false
    },
    useOrg: {
      type: String,
      default: undefined
    },
    showUseOrg: {
      type: Boolean,
      default: true
    }
  },
  mixins: [kingdee],
  data() {
    return {
      // 搜索
      queryParams: {
        pageNum: 1, // 当前页码
        startRow: 0, // 开始行索引
        limit: 10, // 最大行数
        groupOrg: undefined, // 供应商分组
        useOrg: undefined, // 使用组织
        name: undefined, // 模糊搜索
        name2: undefined, // 精确搜索
        number: undefined // 编码
      },
      // 列表
      list: [],
      total: 0,
      loading: false,
      // 选中的
      selectedCustomer: {},
      // 是否显示使用组织
      isUseOrg: false
    }
  },
  created() {
    this.queryParams.useOrg = this.useOrg || this.ApplicationOrgId[0].value || undefined
    // 获取列表
    if (!this.isPopup) this.getList()
  },
  methods: {
    // 列表
    getList() {
      this.loading = true
      this.queryParams.startRow = Math.max(0, (this.queryParams.pageNum - 1) * this.queryParams.limit)
      const query = { ...this.queryParams }
      delete query.pageNum
      getSupplierList(query).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          const { data: list, total } = data
          this.list = list
          this.total = total
        } else this.$message.error(msg)
        this.loading = false
      })
    },
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      if (this.isPopup && !this.showUseOrg) this.queryParams.useOrg = this.useOrg || this.ApplicationOrgId[0].value || undefined
      this.getList()
    },
    // 重置搜索
    handleResetQuery(isUseOrg = false, selectedSupplier = {}) {
      this.resetForm('queryForm')
      this.queryParams.useOrg = this.useOrg || this.ApplicationOrgId[0].value || undefined
      if (this.isPopup) this.selectedCustomer = selectedSupplier
      if (!this.isPopup && this.showUseOrg) this.isUseOrg = isUseOrg
      this.handleQuery()
    },
    // 行点击
    handleRowClick(row) {
      this.selectedCustomer = row
      if (this.isPopup) this.$emit('selectSupplier', row)
    }
  }
}
</script>
<style scoped lang="scss">
@import '@/assets/styles/custom-new.scss';
</style>
