<template>
  <div>
    <el-dialog v-dialogDragBox title="客户详情" :visible.sync="open" width="90%" class="custom-dialog" :before-close="beforeClose" append-to-body>
      <div style="padding: 0 20px">
        <div class="kindeeButton" :key="info.DocumentStatus">
          <template v-for="(btn, index) in buttons">
            <el-button :key="index" :type="btn.type" size="medium" v-if="checkStatus(btn.status)" @click="handleKingdeeDo(btn.action)">{{ btn.text }}</el-button>
            <el-button :key="'disabled-' + index" :type="btn.type" size="medium" disabled v-else>{{ btn.text }}</el-button>
          </template>
        </div>
        <el-descriptions border :column="4" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 4 - 7em - 22px)' }">
          <template slot="title">
            <div class="customTitle">客户</div>
          </template>
          <el-descriptions-item label="创建组织">{{ info.CreateOrgId && info.CreateOrgId.Name && getString(info.CreateOrgId.Name, 1) }}</el-descriptions-item>
          <el-descriptions-item label="客户编码">{{ info.Number }}</el-descriptions-item>
          <el-descriptions-item label="使用组织">{{ info.UseOrgId && info.UseOrgId.Name && getString(info.UseOrgId.Name, 1) }}</el-descriptions-item>
          <el-descriptions-item label="客户名称">{{ info.Name && getString(info.Name) }}</el-descriptions-item>
          <el-descriptions-item label="简称">{{ info.ShortName && getString(info.ShortName) }}</el-descriptions-item>
        </el-descriptions>
        <el-descriptions border :column="4" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 4 - 7em - 22px)' }">
          <template slot="title">
            <div class="customTitle">基本信息</div>
          </template>
          <el-descriptions-item label="国家">{{ info.COUNTRY && getString(info.COUNTRY.FDataValue) }}</el-descriptions-item>
          <el-descriptions-item label="地区">{{ info.PROVINCIAL && getString(info.PROVINCIAL.FDataValue) }}</el-descriptions-item>
          <el-descriptions-item label="省份">{{ info.BD_CUSTOMEREXT && info.BD_CUSTOMEREXT[0] && info.BD_CUSTOMEREXT[0].PROVINCE && getString(info.BD_CUSTOMEREXT[0].PROVINCE.FDataValue) }}</el-descriptions-item>
          <el-descriptions-item label="城市">{{ info.BD_CUSTOMEREXT && info.BD_CUSTOMEREXT[0] && info.BD_CUSTOMEREXT[0].CITY && getString(info.BD_CUSTOMEREXT[0].CITY.FDataValue) }}</el-descriptions-item>
          <el-descriptions-item label="通讯地址">{{ info.ADDRESS }}</el-descriptions-item>
          <el-descriptions-item label="邮政编码">{{ info.FZIP }}</el-descriptions-item>
          <el-descriptions-item label="公司网址">{{ info.WEBSITE }}</el-descriptions-item>
          <el-descriptions-item label="法人代表">{{ info.LegalPerson }}</el-descriptions-item>
          <el-descriptions-item label="注册资本">{{ info.RegisterFund }}</el-descriptions-item>
          <el-descriptions-item label="创立日期">{{ info.FoundDate && parseTime(info.FoundDate, '{y}-{m}-{d}') }}</el-descriptions-item>
          <el-descriptions-item label="行业">{{ info.Domains }}</el-descriptions-item>
          <el-descriptions-item label="注册地址">{{ info.RegisterAddress }}</el-descriptions-item>
          <el-descriptions-item label="联系电话">{{ info.TEL }}</el-descriptions-item>
          <el-descriptions-item label="传真">{{ info.FAX }}</el-descriptions-item>
          <el-descriptions-item label="公司类别">{{ info.CompanyType }}</el-descriptions-item>
          <el-descriptions-item label="公司性质">{{ info.CompanyNature }}</el-descriptions-item>
          <el-descriptions-item label="公司规模">{{ info.CompanyScale }}</el-descriptions-item>
          <el-descriptions-item label="发票抬头">{{ info.INVOICETITLE }}</el-descriptions-item>
          <el-descriptions-item label="纳税登记号">{{ info.FTAXREGISTERCODE }}</el-descriptions-item>
          <el-descriptions-item label="开户银行">{{ info.INVOICEBANKNAME }}</el-descriptions-item>
          <el-descriptions-item label="银行账号">{{ info.INVOICEBANKACCOUNT }}</el-descriptions-item>
          <el-descriptions-item label="开票联系电话">{{ info.INVOICETEL }}</el-descriptions-item>
          <el-descriptions-item label="开票通讯地址">{{ info.INVOICEADDRESS }}</el-descriptions-item>
          <el-descriptions-item label="统一社会信用代码">{{ info.SOCIALCRECODE }}</el-descriptions-item>
          <el-descriptions-item label="对应供应商">{{ info.FSUPPLIERID && getString(info.FSUPPLIERID.Name) }}</el-descriptions-item>
          <el-descriptions-item label="对应集团客户">{{ info.FGROUPCUSTID && getString(info.FGROUPCUSTID.Name) }}</el-descriptions-item>
          <el-descriptions-item label="集团客户">
            <el-checkbox v-model="info.IsGroup" disabled></el-checkbox>
          </el-descriptions-item>
          <el-descriptions-item label="默认付款方">
            <el-checkbox v-model="info.IsDefPayer" disabled></el-checkbox>
          </el-descriptions-item>
          <el-descriptions-item label="不校验可发量">
            <el-checkbox v-model="info.UncheckExpectQty" disabled></el-checkbox>
          </el-descriptions-item>
          <el-descriptions-item label="客户类别">{{ info.CustTypeId && getString(info.CustTypeId.FDataValue) }}</el-descriptions-item>
          <el-descriptions-item label="客户级别">{{ info.F_SCMJ_Assistant && getString(info.F_SCMJ_Assistant.FDataValue) }}</el-descriptions-item>
          <el-descriptions-item label="客户分组">{{ info.FGroup && getString(info.FGroup.Name) }}</el-descriptions-item>
          <el-descriptions-item label="对应组织">{{ info.CorrespondOrgId && getString(info.CorrespondOrgId.Name) }}</el-descriptions-item>
          <el-descriptions-item label="备注">{{ info.Description && getString(info.Description) }}</el-descriptions-item>
        </el-descriptions>
        <div class="customTitle">对应子账户信息</div>
        <el-table ref="detailTable" :data="info.BD_CUSTSUBACCOUNT" class="custom-table" highlight-current-row>
          <!-- 子账户类型 -->
          <el-table-column label="子账户类型" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.FSUBACCOUNTTYPE }}</template>
          </el-table-column>
          <!-- 子账户号 -->
          <el-table-column label="子账户号" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.FSUBACCOUNT }}</template>
          </el-table-column>
        </el-table>
        <div class="customTitle">联系人</div>
        <el-table ref="detailTable" :data="info.BD_CUSTLOCATION" class="custom-table" highlight-current-row>
          <!-- 联系人编码 -->
          <el-table-column label="联系人编码" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.ContactId }}</template>
          </el-table-column>
          <!-- 联系人名称 -->
          <el-table-column label="联系人名称" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.CONTACT }}</template>
          </el-table-column>
          <!-- 职务 -->
          <el-table-column label="职务" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.Job }}</template>
          </el-table-column>
          <!-- 地点编码 -->
          <el-table-column label="地点编码" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.BIZLOCNUMBER }}</template>
          </el-table-column>
          <!-- 地点名称 -->
          <el-table-column label="地点名称" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.BIZLOCATION }}</template>
          </el-table-column>
          <!-- 办公电话 -->
          <el-table-column label="办公电话" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.OFFICEPHONE }}</template>
          </el-table-column>
          <!-- 移动电话 -->
          <el-table-column label="移动电话" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.MOBILEPHONE }}</template>
          </el-table-column>
          <!-- 默认 -->
          <el-table-column label="默认" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
              <el-checkbox v-model="scope.row.ISDEFAULT" disabled></el-checkbox>
            </template>
          </el-table-column>
          <!-- 传真 -->
          <el-table-column label="传真" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.FAX }}</template>
          </el-table-column>
          <!-- 邮箱 -->
          <el-table-column label="邮箱" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.Email }}</template>
          </el-table-column>
          <!-- 详细地址 -->
          <el-table-column label="详细地址" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.BizAddress }}</template>
          </el-table-column>
          <!-- 禁用状态 -->
          <el-table-column label="禁用状态" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.ForbidContactStatus }}</template>
          </el-table-column>
        </el-table>
        <el-descriptions border :column="4" :labelStyle="{ 'text-align': 'right', width: 'calc(7em + 20px)' }" :contentStyle="{ width: 'calc((90vw - 20px) / 4 - 7em - 22px)' }">
          <template slot="title">
            <div class="customTitle">商务信息</div>
          </template>
          <!-- 结算币别 -->
          <el-descriptions-item label="结算币别">{{ info.TRADINGCURRID && getString(info.TRADINGCURRID.Name) }}</el-descriptions-item>
          <!-- 销售部门 -->
          <el-descriptions-item label="销售部门">{{ info.SALDEPTID && getString(info.SALDEPTID.Name) }}</el-descriptions-item>
          <!-- 销售组 -->
          <el-descriptions-item label="销售组">{{ info.SALGROUPID && getString(info.SALGROUPID.Name) }}</el-descriptions-item>
          <!-- 销售员 -->
          <el-descriptions-item label="销售员">{{ info.SELLER && getString(info.SELLER.Name) }}</el-descriptions-item>
          <!-- 冻结状态 -->
          <el-descriptions-item label="冻结状态">{{ info.BD_CUSTOMEREXT && info.BD_CUSTOMEREXT[0] && info.BD_CUSTOMEREXT[0].FreezeStatus && getOptionLabel(FreezeStatusOptions, info.BD_CUSTOMEREXT[0].FreezeStatus) }}</el-descriptions-item>
          <!-- 冻结范围 -->
          <el-descriptions-item label="冻结范围">{{ info.BD_CUSTOMEREXT && info.BD_CUSTOMEREXT[0] && info.BD_CUSTOMEREXT[0].FreezeLimit }}</el-descriptions-item>
          <!-- 冻结人 -->
          <el-descriptions-item label="冻结人">{{ info.BD_CUSTOMEREXT && info.BD_CUSTOMEREXT[0] && info.BD_CUSTOMEREXT[0].FreezeOperator && getString(info.BD_CUSTOMEREXT[0].FreezeOperator.Name) }}</el-descriptions-item>
          <!-- 冻结日期 -->
          <el-descriptions-item label="冻结日期">{{ info.BD_CUSTOMEREXT && info.BD_CUSTOMEREXT[0] && info.BD_CUSTOMEREXT[0].FreezeDate && parseTime(info.BD_CUSTOMEREXT[0].FreezeDate, '{y}-{m}-{d}') }}</el-descriptions-item>
          <!-- 保证金比例(%) -->
          <el-descriptions-item label="保证金比例(%)">{{ info.BD_CUSTOMEREXT && info.BD_CUSTOMEREXT[0] && info.BD_CUSTOMEREXT[0].MarginLevel }}</el-descriptions-item>
          <!-- 结算卡 -->
          <el-descriptions-item label="结算卡">{{ info.BD_CUSTOMEREXT && info.BD_CUSTOMEREXT[0] && info.BD_CUSTOMEREXT[0].FDebitCard }}</el-descriptions-item>
          <!-- 结算方 -->
          <el-descriptions-item label="结算方">{{ info.BD_CUSTOMEREXT && info.BD_CUSTOMEREXT[0] && info.BD_CUSTOMEREXT[0].SettleId && getString(info.BD_CUSTOMEREXT[0].SettleId.Name) }}</el-descriptions-item>
          <!-- 付款方 -->
          <el-descriptions-item label="付款方">{{ info.BD_CUSTOMEREXT && info.BD_CUSTOMEREXT[0] && info.BD_CUSTOMEREXT[0].ChargeId && getString(info.BD_CUSTOMEREXT[0].ChargeId.Name) }}</el-descriptions-item>
          <!-- 默认联系人 -->
          <el-descriptions-item label="默认联系人">{{ info.BD_CUSTOMEREXT && info.BD_CUSTOMEREXT[0] && info.BD_CUSTOMEREXT[0].DefaultContact && getString(info.BD_CUSTOMEREXT[0].DefaultContact.Name) }}</el-descriptions-item>
          <!-- 结算方式 -->
          <el-descriptions-item label="结算方式">{{ info.SETTLETYPEID && getString(info.SETTLETYPEID.Name) }}</el-descriptions-item>
          <!-- 收款条件 -->
          <el-descriptions-item label="收款条件">{{ info.RECCONDITIONID && getString(info.RECCONDITIONID.Name) }}</el-descriptions-item>
          <!-- 价目表 -->
          <el-descriptions-item label="价目表">{{ info.PRICELISTID && getString(info.PRICELISTID.Name) }}</el-descriptions-item>
          <!-- 折扣表 -->
          <el-descriptions-item label="折扣表">{{ info.DISCOUNTLISTID && getString(info.DISCOUNTLISTID.Name) }}</el-descriptions-item>
          <!-- 联系人必录 -->
          <el-descriptions-item label="联系人必录">
            <el-checkbox :value="IsContractMustInPut" disabled></el-checkbox>
          </el-descriptions-item>
          <!-- 发票类型 -->
          <el-descriptions-item label="发票类型">{{ info.InvoiceType && getOptionLabel(InvoiceTypeOptions, info.InvoiceType) }}</el-descriptions-item>
          <!-- 默认税率 -->
          <el-descriptions-item label="默认税率">{{ info.TaxRate && getString(info.TaxRate.Name) }}</el-descriptions-item>
          <!-- 客户优先级 -->
          <el-descriptions-item label="客户优先级">{{ info.Priority }}</el-descriptions-item>
          <!-- 运输提前期 -->
          <el-descriptions-item label="运输提前期">{{ info.TRANSLEADTIME }}</el-descriptions-item>
          <!-- 税分类 -->
          <el-descriptions-item label="税分类">{{ info.TaxType && getString(info.TaxType.FDataValue, 1) }}</el-descriptions-item>
          <!-- 启用信用管理 -->
          <el-descriptions-item label="启用信用管理">
            <el-checkbox :value="info.FISCREDITCHECK" disabled></el-checkbox>
          </el-descriptions-item>
        </el-descriptions>
        <div class="customTitle">银行信息</div>
        <el-table ref="detailTable" :data="info.BD_CUSTBANK" class="custom-table" highlight-current-row>
          <!-- 开户国家 -->
          <el-table-column label="开户国家" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.COUNTRY && getString(scope.row.COUNTRY.Name) }}</template>
          </el-table-column>
          <!-- 银行账号 -->
          <el-table-column label="银行账号" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.BANKCODE }}</template>
          </el-table-column>
          <!-- 账户名称 -->
          <el-table-column label="账户名称" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.ACCOUNTNAME }}</template>
          </el-table-column>
          <!-- 收款银行 -->
          <el-table-column label="收款银行" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.BankTypeRec && getString(scope.row.BankTypeRec.Name) }}</template>
          </el-table-column>
          <!-- 网点名称 -->
          <el-table-column label="网点名称" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.FTextBankDetail }}</template>
          </el-table-column>
          <!-- 银行网点 -->
          <el-table-column label="银行网点" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.BankDetail && getString(scope.row.BankDetail.Name) }}</template>
          </el-table-column>
          <!-- 开户行地址 -->
          <el-table-column label="开户行地址" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.OpenAddressRec }}</template>
          </el-table-column>
          <!-- 开户银行 -->
          <el-table-column label="开户银行" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.OPENBANKNAME && scope.row.OPENBANKNAME.join(',') }}</template>
          </el-table-column>
          <!-- 联行号 -->
          <el-table-column label="联行号" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.CNAPS }}</template>
          </el-table-column>
          <!-- 币别 -->
          <el-table-column label="币别" align="center" show-overflow-tooltip>
            <template slot-scope="scope">{{ scope.row.CURRENCYID && getString(scope.row.CURRENCYID.Name) }}</template>
          </el-table-column>
          <!-- 默认 -->
          <el-table-column label="默认" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
              <el-checkbox :value="scope.row.ISDEFAULT" disabled></el-checkbox>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { getCustomerDetail, auditCustomer, revokeCustomer, deleteCustomer, submitCustomer, unAuditCustomer } from '@/api/kingdee/customer'
import { kingdee } from '@/minix'

export default {
  mixins: [kingdee],
  data() {
    return {
      open: false,
      info: {},
      // 冻结状态
      FreezeStatusOptions: [
        { label: '正常', value: 'A' },
        { label: '冻结', value: 'B' }
      ],
      // 发票类型
      InvoiceTypeOptions: [
        { label: '增值税专用发票', value: '1' },
        { label: '普通发票', value: '2' }
      ],
      buttons: [
        { text: '提交', type: 'primary', action: 'submit', status: ['A', 'D'] },
        { text: '审核', type: 'warning', action: 'audit', status: ['B'] },
        { text: '撤销', type: 'info', action: 'revoke', status: ['B'] },
        { text: '删除', type: 'danger', action: 'delete', status: ['A', 'D'] },
        { text: '反审', type: 'warning', action: 'unAudit', status: ['C'] }
      ]
    }
  },
  computed: {
    // 联系人必录
    IsContractMustInPut() {
      return this.info.BD_CUSTOMEREXT && this.info.BD_CUSTOMEREXT[0] && this.info.BD_CUSTOMEREXT[0].IsContractMustInPut
    }
  },
  methods: {
    // 检查状态
    checkStatus(allowedStatus) {
      return allowedStatus.includes(this.info.DocumentStatus)
    },
    // 获取详情
    getInfo(row = {}) {
      if (!row.Number) {
        this.$message.error('参数错误，请重新选择')
        return
      }
      getCustomerDetail({ billNo: row.Number }).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          const { result } = data
          this.open = !!result?.result
          this.info = result?.result || {}
          this.$nextTick(() => {
            if (this.$refs.detailTable) {
              this.$refs.detailTable.bodyWrapper.scrollLeft = 0
            }
          })
        } else this.$message.error(msg)
      })
    },
    beforeClose() {
      this.handleClose()
      this.$emit('update', { billNo: this.info.Number, newStatus: this.info.DocumentStatus })
    },
    // 关闭
    handleClose(flag = false) {
      this.open = false
      this.$emit('callBack', flag)
    },
    // 金蝶操作
    // prettier-ignore
    handleKingdeeDo(type) {
      const number = this.info.Number
      if (!number) return
      switch (type) {
        case 'submit':
          // 提交
          this.$modal.confirm('确认要提交该客户吗？').then(() => {
            submitCustomer({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('提交成功')
                this.$set(this.info, 'DocumentStatus', 'B')
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'audit':
          // 审核
          this.$modal.confirm('确认要审核该客户吗？').then(() => {
            auditCustomer({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('审核成功')
                this.$set(this.info, 'DocumentStatus', 'C')
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'revoke':
          // 撤销
          this.$modal.confirm('确认要撤销该客户吗？').then(() => {
            revokeCustomer({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('撤销成功')
                this.$set(this.info, 'DocumentStatus', 'D')
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'unAudit':
          // 反审核
          this.$modal.confirm('确认要反审核该客户吗？').then(() => {
            unAuditCustomer({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('反审核成功')
                this.$set(this.info, 'DocumentStatus', 'D')
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
        case 'delete':
          // 删除
          this.$modal.confirm('确认要删除该客户吗？').then(() => {
            deleteCustomer({ number }).then(res => {
              const { code, msg } = res
              if (code === 200) {
                this.$message.success('删除成功')
                this.handleClose(true)
              } else this.$message.error(msg)
            })
          }).catch(() => {})
          break
      }
    }
  }
}
</script>
<style scoped lang="scss">
@import '@/assets/styles/custom-new.scss';
::v-deep {
  .el-descriptions__header {
    margin-bottom: 0;
  }
  .el-table.custom-table .el-table__body-wrapper .el-table__row:hover td.el-table__cell {
    background-color: #eaf1fe !important;
  }
}
.customTitle {
  font-size: 16px;
  font-weight: normal;
  line-height: 60px;
  color: $font;
}
.kindeeButton {
  display: flex;
  align-items: center;
}
.custom-push-target {
  .el-col.el-col-12 {
    .el-radio {
      height: 30px;
      display: flex;
      align-items: center;
    }
  }
}
</style>
