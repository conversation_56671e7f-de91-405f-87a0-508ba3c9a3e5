<template>
  <div>
    <el-dialog v-dialogDragBox :title="title" :visible.sync="open" width="1150px" class="custom-dialog" @close="handleClose" append-to-body>
      <div style="padding: 0 20px">
        <el-form :model="form" :rules="rules" ref="form" label-width="6.5em">
          <el-row :gutter="10">
            <el-col :span="12">
              <el-form-item label="使用组织" prop="fuseOrgNumber">
                <el-select v-model="form.fuseOrgNumber" placeholder="请选择使用组织" style="width: 100%">
                  <el-option v-for="org in ApplicationOrgId" :key="org.value" :label="org.label" :value="org.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="纳税登记号" prop="ftaxRegisterCode">
                <el-input v-model="form.ftaxRegisterCode" placeholder="请输入纳税登记号" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="客户名称" prop="fname">
                <el-input v-model="form.fname" placeholder="请输入客户名称" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="客户简称" prop="fshortName">
                <el-input v-model="form.fshortName" placeholder="请输入客户简称" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="发票抬头" prop="finvoiceTitle">
                <el-input v-model="form.finvoiceTitle" placeholder="请输入发票抬头" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="联系电话" prop="ftel">
                <el-input v-model="form.ftel" placeholder="请输入联系电话" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="开户银行" prop="finvoiceBankName">
                <el-input v-model="form.finvoiceBankName" placeholder="请输入开户银行" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="银行帐号" prop="finvoiceBankAccount">
                <el-input v-model="form.finvoiceBankAccount" placeholder="请输入银行帐号" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <el-button class="custom-dialog-btn" @click="open = false">取 消</el-button>
        <el-button type="primary" class="custom-dialog-btn primary" @click="handleSubmit">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { kingdee } from '@/minix'
import { saveCustomer } from '@/api/kingdee/customer'

export default {
  mixins: [kingdee],
  data() {
    return {
      open: false,
      title: '新增客户',
      form: {},
      rules: {
        fuseOrgNumber: [{ required: true, message: '请选择使用组织', trigger: 'change' }],
        fname: [{ required: true, message: '请输入客户名称', trigger: 'blur' }],
        finvoiceTitle: [{ required: true, message: '请输入发票抬头', trigger: 'blur' }]
      }
    }
  },
  methods: {
    // 表单重置
    reset() {
      this.form = {
        finvoiceBankAccount: undefined, // 开户行账号
        finvoiceBankName: undefined, // 开户行名称
        finvoiceTitle: undefined, // 发票抬头
        fname: undefined, // 客户名称
        fshortName: undefined, // 客户简称
        ftaxRegisterCode: undefined, // 纳税登记号
        ftel: undefined, // 联系电话
        fuseOrgNumber: undefined // 使用组织
      }
      this.resetForm('form')
    },
    // 打开弹窗
    handleOpen() {
      this.reset()
      this.title = '新增客户'
      this.open = true
    },
    // 提交
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          saveCustomer(this.form).then(res => {
            const { code, msg } = res
            if (code === 200) {
              this.$message.success('新增成功!')
              this.open = false
              this.$emit('callBack')
            } else this.$message.error(msg)
          })
        }
      })
    },
    // 关闭弹窗
    handleClose() {
      this.open = false
      this.$emit('callBack')
    }
  }
}
</script>
<style scoped lang="scss">
@import '@/assets/styles/custom-new.scss';
</style>
