<template>
  <div class="newBox" :class="{ 'vh-85': !isPopup, bgcf9: !isPopup }">
    <!-- 搜索 -->
    <div class="custom-search flex" style="padding-top: 18px" :style="{ justifyContent: showSearch ? 'space-between' : 'flex-end' }">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" @submit.native.prevent>
        <el-form-item label="客户名称" prop="name">
          <el-input v-model="queryParams.name" placeholder="请输入客户名称" clearable @keyup.enter.native="handleQuery" :style="{ width: (!isPopup && showUseOrg) || showUseOrg || isUseOrg ? '170px' : 'auto' }" />
        </el-form-item>
        <el-form-item label="客户分组" prop="groupOrg">
          <el-select v-model="queryParams.groupOrg" placeholder="请选择客户分组" clearable :style="{ width: (!isPopup && showUseOrg) || showUseOrg || isUseOrg ? '150px' : 'auto' }">
            <el-option v-for="item in ApplicationOrgId" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="使用组织" prop="useOrg" v-if="(!isPopup && showUseOrg) || showUseOrg || isUseOrg">
          <el-select v-model="queryParams.useOrg" placeholder="请选择使用组织" :style="{ width: (!isPopup && showUseOrg) || showUseOrg || isUseOrg ? '150px' : 'auto' }">
            <el-option v-for="org in ApplicationOrgId" :key="org.value" :label="org.label" :value="org.value" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="small" @click="handleResetQuery">重置</el-button>
          <el-button type="primary" icon="el-icon-plus" size="small" @click="handleCreate" v-hasPermi="['kingdee:customer:create']">新增</el-button>
        </el-form-item>
      </el-form>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns" isSetitem @updateColumns="updateColumns"></right-toolbar>
    </div>
    <!-- 列表 -->
    <div style="padding: 15px 20px">
      <el-table v-loading="loading" ref="allTable" border :data="list" row-key="Number" style="width: 100%" class="custom-table" @row-click="handleRowClick" :highlight-current-row="isPopup">
        <!-- 单选按钮 -->
        <el-table-column align="center" label="选择" width="60" v-if="isPopup">
          <template slot-scope="scope">
            <el-radio v-model="selectedCustomer.Number" :label="scope.row.Number" v-removeAriaHidden><span /></el-radio>
          </template>
        </el-table-column>
        <!-- 客户编码 -->
        <el-table-column align="center" prop="Number" label="客户编码" show-overflow-tooltip v-if="columns[0].visible">
          <template slot-scope="{ row }">
            <span class="table-link" @click="handleDetail(row)">{{ row.Number }}</span>
          </template>
        </el-table-column>
        <!-- 客户名称 -->
        <el-table-column align="center" prop="Name" label="客户名称" show-overflow-tooltip v-if="columns[1].visible"></el-table-column>
        <!-- 简称 -->
        <el-table-column align="center" prop="ShortName" label="简称" show-overflow-tooltip v-if="columns[2].visible"></el-table-column>
        <!-- 单据状态 -->
        <el-table-column align="center" prop="DocumentStatus" label="单据状态" show-overflow-tooltip v-if="columns[3].visible">
          <template slot-scope="scope">{{ getDocumentStatusLabel(scope.row.DocumentStatus) }}</template>
        </el-table-column>
        <!-- 禁用状态 -->
        <el-table-column align="center" prop="ForbidStatus" label="禁用状态" show-overflow-tooltip v-if="columns[4].visible">
          <template slot-scope="scope">{{ scope.row.ForbidStatus === 'A' ? '否' : '是' }}</template>
        </el-table-column>
        <!-- 使用组织 -->
        <el-table-column align="center" prop="UseOrg" label="使用组织" show-overflow-tooltip v-if="columns[5].visible"></el-table-column>
        <!-- 审核人 -->
        <el-table-column align="center" prop="Approver" label="审核人" show-overflow-tooltip v-if="columns[6].visible"></el-table-column>
        <!-- 审核日期 -->
        <el-table-column align="center" prop="ApproveDate" label="审核日期" show-overflow-tooltip v-if="columns[7].visible">
          <template slot-scope="scope">{{ parseTime(scope.row.ApproveDate, '{y}-{m}-{d}') }}</template>
        </el-table-column>
        <!-- 客户分组 -->
        <el-table-column align="center" prop="Group" label="客户分组" show-overflow-tooltip v-if="columns[8].visible"></el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="custom-pagination">
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.limit" @pagination="getList" />
      </div>
    </div>
    <!-- 新增 -->
    <create ref="create" @callBack="getList" v-if="isCreate" />
    <!-- 详情 -->
    <detail ref="detail" @callBack="handleDetailCallBack" @update="handleUpdate" v-if="isDetail" />
  </div>
</template>
<script>
import { getCustomerList } from '@/api/kingdee/customer'
import { kingdee } from '@/minix'
import create from './create'
import detail from './detail'

export default {
  name: 'Customer',
  props: {
    isPopup: {
      type: Boolean,
      default: false
    },
    useOrg: {
      type: String,
      default: undefined
    },
    showUseOrg: {
      type: Boolean,
      default: true
    }
  },
  mixins: [kingdee],
  components: { create, detail },
  data() {
    return {
      // 搜索
      queryParams: {
        pageNum: 1, // 当前页码
        startRow: 0, // 开始行索引
        limit: 10, // 最大行数
        groupOrg: undefined, // 客户分组
        useOrg: undefined, // 使用组织
        name: undefined, // 模糊搜索
        name2: undefined // 精确搜索
      },
      // 列表
      list: [],
      total: 0,
      loading: false,
      // 选中的
      selectedCustomer: {},
      // 是否新增
      isCreate: false,
      // 是否详情
      isDetail: false,
      // 是否显示使用组织
      isUseOrg: false,
      // 列表显隐
      showSearch: true,
      // 列表显隐列
      columns: [
        { key: 0, label: `客户编码`, visible: true },
        { key: 1, label: `客户名称`, visible: true },
        { key: 2, label: `简称`, visible: true },
        { key: 3, label: `单据状态`, visible: true },
        { key: 4, label: `禁用状态`, visible: true },
        { key: 5, label: `使用组织`, visible: true },
        { key: 6, label: `审核人`, visible: true },
        { key: 7, label: `审核日期`, visible: true },
        { key: 8, label: `客户分组`, visible: true }
      ]
    }
  },
  created() {
    const columns = localStorage.getItem(this.userId + '.customerColumns')
    if (columns) this.columns = JSON.parse(columns)
    if (this.companyId == 14) this.queryParams.useOrg = this.useOrg || this.ApplicationOrgId[0].value || undefined
    else this.queryParams.useOrg = undefined
    // 获取列表
    if (!this.isPopup) this.getList()
  },
  computed: {
    companyId() {
      return this.$store.state.user.companyId
    },
    // 获取当前用户ID
    userId() {
      return this.$store.getters.info.userId
    }
  },
  methods: {
    // 改变显隐列缓存
    updateColumns(data = []) {
      localStorage.setItem(this.userId + '.customerColumns', JSON.stringify(data))
    },
    // 列表
    getList() {
      this.loading = true
      this.queryParams.startRow = Math.max(0, (this.queryParams.pageNum - 1) * this.queryParams.limit)
      const query = { ...this.queryParams }
      delete query.pageNum
      getCustomerList(query).then(res => {
        const { code, msg, data } = res
        if (code === 200) {
          const { data: list, total } = data
          this.list = list
          this.total = total
        } else this.$message.error(msg)
        this.loading = false
      })
    },
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      if (this.isPopup && !this.showUseOrg) this.queryParams.useOrg = this.useOrg || this.ApplicationOrgId[0].value || undefined
      this.getList()
    },
    // 重置搜索
    handleResetQuery(isUseOrg = false) {
      this.resetForm('queryForm')
      if (this.isPopup) this.selectedCustomer = {}
      if (!this.isPopup && this.showUseOrg) this.isUseOrg = isUseOrg
      if (this.companyId == 14) this.queryParams.useOrg = this.useOrg || this.ApplicationOrgId[0].value || undefined
      else this.queryParams.useOrg = undefined
      this.handleQuery()
    },
    // 行点击
    handleRowClick(row) {
      this.selectedCustomer = row
      if (this.isPopup) this.$emit('selectCustomer', row)
    },
    // 新增
    handleCreate() {
      this.isCreate = true
      this.isDetail = false
      this.$nextTick(() => {
        this.$refs.create.handleOpen()
      })
    },
    // 详情
    handleDetail(row) {
      this.isCreate = false
      this.isDetail = true
      this.$nextTick(() => {
        this.$refs.detail.getInfo(row)
      })
    },
    // 详情回调
    handleDetailCallBack(flag) {
      this.isDetail = false
      if (flag) this.getList()
    },
    // 更新
    handleUpdate(data) {
      const { billNo, newStatus } = data
      const index = this.list.findIndex(item => item.Number === billNo)
      if (index !== -1) this.list[index].DocumentStatus = newStatus
    }
  }
}
</script>
<style scoped lang="scss">
@import '@/assets/styles/custom-new.scss';
</style>
