<template>
    <div class="newBox bgcf9 vh-85">
        <div class="custom-search flex" style="padding-top: 18px">
            <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" @submit.native.prevent>
                <el-form-item label="工序名称" prop="name">
                    <el-input v-model="queryParams.name" placeholder="请输入工序名称" clearable
                        @keyup.enter.native="handleQuery" />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
                    <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
                </el-form-item>
                <!-- <el-form-item label="" prop="status">
                    <el-select v-model="status" placeholder="请选择状态" @change="handleQuery">
                        <el-option v-for="item in statusOptions" :key="item.value" :label="item.label"
                            :value="item.value"></el-option>
                    </el-select>
                </el-form-item> -->
                <el-form-item>
                    <el-button type="primary" icon="el-icon-plus" size="small" @click="handleAdd">新增工序</el-button>
                </el-form-item>
            </el-form>
        </div>
        <div class="Box">
            <el-table v-loading="loading" ref="table" stripe :data="list" row-key="id" style="width: 100%"
                class="custom-table">
                <el-table-column align="center" type="index" label="序号"></el-table-column>
                <el-table-column label="工序名称" align="center" prop="name" show-overflow-tooltip></el-table-column>
                <el-table-column label="工序描述" align="center" prop="description" show-overflow-tooltip
                    min-width="130"></el-table-column>
                <el-table-column label="所属分类" align="center" show-overflow-tooltip>
                    <template slot-scope="{ row }">
                        {{ row.cla && row.cla.name }}
                    </template>
                </el-table-column>
                <el-table-column label="所用设备" align="center" show-overflow-tooltip>
                    <template slot-scope="{ row }">
                        {{ convertData(row) }}
                    </template>
                </el-table-column>
                <el-table-column label="创建时间" align="center" prop="createTime"></el-table-column>
                <el-table-column label="创建人" align="center" prop="createBy"></el-table-column>
                <el-table-column label="状态" align="center" prop="status">
                    <template slot-scope="{ row }">
                        <span class="table-tag" :class="row.status === 1 ? 'primary' : 'danger'">{{ row.status === 1 ?
                            '启用' : '禁用' }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="操作" align="center" width="220px">
                    <template slot-scope="{ row }">
                        <!-- <el-button class="table-btn" @click="handleView(row)">查看详情</el-button> -->
                        <el-button class="table-btn primary" @click="handleEdit(row)">修改</el-button>
                        <el-button class="table-btn primary" v-if="row.status != 1"
                            @click="handleStatus(row, 1)">启用</el-button>
                        <el-button class="table-btn danger" v-if="row.status == 1"
                            @click="handleStatus(row, 0)">禁用</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <div class="custom-pagination">
                <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
                    :limit.sync="queryParams.pageSize" @pagination="getList" />
            </div>
        </div>

        <!--新增/修改-->
        <el-dialog v-dialogDragBox :title="title" :visible.sync="open" width="1150px" class="custom-dialog">
            <div style="padding: 0 20px">
                <el-form ref="form" :model="form" :rules="rules" label-width="120px">
                    <el-row :gutter="10">
                        <el-col :span="12">
                            <el-form-item label="工序名称" prop="name">
                                <el-input v-model="form.name" placeholder="请输入工序名称"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="所属分类" prop="classId">
                                <treeselect v-model="form.classId" :options="craftOptions" :normalizer="normalizer"
                                    :show-count="true" placeholder="请选择工序分类" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label="物料备注" prop="desc">
                                <el-input v-model="form.desc" type="textarea" :autosize="{ minRows: 3, maxRows: 9 }"
                                    placeholder="请输入物料备注"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <!-- {{ form.equipmentId }} -->
                            <el-form-item label="所用设备" prop="equipmentIds">
                                <el-cascader ref="equipment" clearable :options="equipmentOptions" class="w100"
                                    :props="optionProps" @change="handleSelect" placeholder="请选择所用设备"
                                    v-model="form.equipmentId" :show-all-levels="false">
                                    <template slot-scope="{ node, data }">
                                        <span @click="handleEquipment(node)"
                                            :style="{ color: node.level == 2 ? '#2E73F3' : '' }">{{ data.label + (
                                                node.level == 2 ? ' >' : '') }}</span>
                                    </template>
                                </el-cascader>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
            <div slot="footer">
                <el-button class="custom-dialog-btn" @click="open = false">取消</el-button>
                <el-button class="custom-dialog-btn primary" @click="submitForm">确定</el-button>
            </div>
        </el-dialog>

        <!--设备详情-->
        <el-dialog v-dialogDragBox title="设备详情" :visible.sync="equipmentViewShow" width="1150px" class="custom-dialog">
            <div style="padding:10px 30px; display: flex;">
                <div class="imgBox">
                    <el-image :src="formatProductImg(equipmentView)" fit="cover"
                        :preview-src-list="imgList(equipmentView)">
                        <div slot="error" class="image-slot">
                            <i class="el-icon-picture-outline"></i>
                        </div>
                    </el-image>
                </div>
                <div class="textBox">
                    <el-descriptions class="tableBox" :column="1" border>
                        <el-descriptions-item>
                            <template slot="label">
                                设备名称
                            </template>
                            {{ equipmentView.name }}
                        </el-descriptions-item>
                        <el-descriptions-item>
                            <template slot="label">
                                设备品牌
                            </template>
                            {{ equipmentView.brand }}
                        </el-descriptions-item>
                        <el-descriptions-item labelClassName="h105" contentClassName="h105"
                            :contentStyle="{ overflowY: 'scroll' }">
                            <template slot="label">
                                设备用途
                            </template>
                            {{ equipmentView.description }}
                        </el-descriptions-item>
                        <el-descriptions-item>
                            <template slot="label">
                                设备用途分类
                            </template>
                            {{ equipmentView.cla && equipmentView.cla.name }}
                        </el-descriptions-item>
                        <el-descriptions-item>
                            <template slot="label">
                                创建人
                            </template>
                            {{ equipmentView.createBy }}
                        </el-descriptions-item>
                    </el-descriptions>
                </div>
            </div>
        </el-dialog>
    </div>
</template>
<script>
import { getProcessInfoList, getProcessClassList2, addProcessInfo, editProcessInfo, getProcessEquipmentList, getEquipmentList } from '@/api/bom'
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

export default {
  name: 'Craft',
    components: { Treeselect },
    data() {
        return {
            status: 1,
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                status: undefined,
                name: undefined
            },
            list: [],
            total: 0,
            loading: false,
            statusOptions: [
                { label: '启用', value: 1, type: 'success' },
                { label: '禁用', value: 0, type: 'danger' }
            ],
            open: false,
            title: '新增工序',
            form: {
                equipmentId: [['9']]
            },
            rules: {
                name: [
                    { required: true, message: '请输入工序名称', trigger: 'blur' },
                ],
                classId: [
                    { required: true, message: '请选择所属工序分类', trigger: 'change' },
                ],
                equipmentIds: [
                    { required: true, message: '请选择使用设备', trigger: 'blur' },
                ],
            },
            craftOptions: [],
            equipmentOptions: [],
            optionProps: {
                multiple: true
            },
            equipmentViewShow: false,
            equipmentView: {},
            equipmentList: []
        }
    },
    created() {
        this.getList()
        this.init()
    },
    methods: {
        init() {
            getProcessClassList2().then(res => {
                const { code, msg, data } = res
                if (code === 200) {
                    this.craftOptions = data
                } else this.$message.error(msg)
            })
            getProcessEquipmentList({
                pageNum: 1,
                pageSize: 10000,
                status: 1,
            }).then(res => {
                const { code, msg, rows } = res
                if (code === 200) {
                    rows.forEach(el => {
                        this.equipmentOptions.push({
                            id: el.id,
                            value: 'classId' + el.id,
                            label: el.name,
                            children: [],
                        })
                    })
                    this.equipmentOptions.forEach(el => {
                        getEquipmentList({
                            classId: el.id,
                            pageNum: 1,
                            pageSize: 10000,
                        }).then((response) => {
                            const { code, msg, rows } = response
                            if (code == 200) {
                                if (rows.length > 0) {
                                    rows.forEach((item) => {
                                        el.children.push({
                                            value: item.id,
                                            label: item.name,
                                            leaf: true,
                                            ...item
                                        });
                                    });
                                } else {
                                    this.$set(el, 'disabled', true)
                                }
                            } else this.$message.error(msg)
                        });
                    })
                } else this.$message.error(msg)
            })
        },

        // 搜索
        handleQuery() {
            this.queryParams.pageNum = 1
            this.getList()
        },
        // 重置搜索
        resetQuery() {
            this.resetForm('queryForm')
            this.handleQuery()
        },
        // 获取列表
        getList() {
            this.loading = true
            // this.queryParams.status = this.status
            getProcessInfoList(this.queryParams).then(res => {
                const { code, msg, rows, total } = res
                if (code === 200) {
                    this.list = rows
                    this.total = total
                    this.loading = false
                } else this.$message.error(msg)
            })
        },
        // 刷新列表
        refreshList() {
            getProcessInfoList(this.queryParams).then(res => {
                const { code, msg, rows, total } = res
                if (code === 200) {
                    this.$set(this, 'list', rows)
                    this.$set(this, 'total', total)
                } else this.$message.error(msg)
            })
        },
        // 修改状态
        // prettier-ignore
        handleStatus(row, val = 0) {
            const title = `是否${val === 1 ? '启用' : '禁用'}该工序？`
            this.$confirm(title, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                editProcessInfo({ processId: row.id, status: val }).then(res => {
                    const { code, msg } = res
                    if (code === 200) {
                        this.$message.success('操作成功')
                        this.refreshList()
                    } else this.$message.error(msg)
                })
            }).catch(() => { })
        },
        // 新增
        handleAdd() {
            this.form = {
                name: undefined,
                desc: undefined,
                classId: undefined,
                equipmentIds: undefined,
                equipmentId: []
            }
            this.title = '新增工序'
            this.open = true
        },
        // 修改
        handleEdit(row) {
            this.form = {
                processId: row.id,
                name: row.name,
                desc: row.description,
                equipmentIds: [],
                classId: row.classId
            }
            let arr = []
            row.equipments.forEach(el => {
                let arr1 = ['classId' + el.equipment.classId, el.equipment.id]
                this.form.equipmentIds.push(el.equipment.id)
                arr.push(arr1)
            })
            this.$set(this.form, 'equipmentId', arr)
            console.log(this.form.equipmentIds)
            this.title = '修改工序'
            this.open = true
        },
        handleEquipment(node) {
            if (node.level == 2) {
                this.equipmentView = node.data
                this.equipmentViewShow = true
            }
        },
        handleSelect(val) {
            this.form.equipmentIds = []
            val.forEach(el => {
                this.form.equipmentIds.push(el[1])
            })
        },
        submitForm() {
            this.$refs['form'].validate((valid) => {
                if (valid) {
                    if (this.form.processId != null) {
                        editProcessInfo(this.form).then(res => {
                            const { code, msg } = res
                            if (code === 200) {
                                this.$message.success('修改成功')
                                this.open = false
                                this.getList()
                            } else this.$message.error(msg)
                        })
                    } else {
                        addProcessInfo(this.form).then(res => {
                            if (res.code === 200) {
                                this.$message.success('新增成功')
                                this.open = false
                                this.getList()
                            } else this.$message.error(msg)
                        })
                    }
                } else {
                    console.log('error submit!!');
                    return false;
                }
            });
        },
        /** 转换数据结构 */
        normalizer(node) {
            if (node.children && !node.children.length) {
                delete node.children;
            }
            return {
                id: node.id,
                label: node.name,
                children: node.children
            };
        },
        convertData(row) {
            if (row.equipments) {
                let arr = []
                row.equipments.forEach(el => {
                    if (el.equipment) {
                        arr.push(el.equipment.name)
                    }
                });
                return arr.join('、')
            } else {
                let str = ''
                return str
            }
        },
        // 图片处理
        imgList(row) {
            if (row.img_oss) {
                return row.img_oss.split(',')
            } else if (row.img) {
                let arr = row.img.split(',')
                arr.forEach(el => {
                    el = this.imgPath + el
                })
                return arr
            } else {
                let arr = []
                return arr
            }
        }
    }
}
</script>

<style scoped lang="scss">
@import '~@/assets/styles/custom-new.scss';

.Box {
    padding: 15px 20px;
}

.table-tag {
    &.primary {
        color: $blue;
    }

    &.danger {
        color: $red;
    }
}

::v-deep {
    .el-button.table-btn {
        padding: 0;
    }
}

.w100 {
    width: 100%;
}

.imgBox {
    width: 290px;
    height: 290px;

    ::v-deep img {
        width: 290px;
        height: 290px;
    }
}

.textBox {
    flex: 1;
    margin-left: 20px;
    height: 290px;

    .tableBox {
        ::v-deep .el-descriptions-item__label {
            width: 135px;
            box-sizing: border-box;
            padding-left: 30px;
        }

        ::v-deep .el-descriptions-row {

            .h105 {
                height: 105px;
            }
        }


    }
}
</style>
