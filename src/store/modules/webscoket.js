import store from '@/store'
import { parseTime, uniqueJsonArrByField } from '@/utils/ruoyi'
import { isJSON } from '@/utils'
import { getProvisionals, getFriends, saveChatNum, saveFriends, saveProvisionals, getChatNum, saveGroups, getGroups, updateGroupsInCache } from '@/utils/record'

const state = {
  websock: null,
  websock_url: '',
  isconnect: false,
  // 心跳timer
  hearbeat_timer: null,
  // 心跳发送频率 - 调整为30秒，减少频率
  hearbeat_interval: 30 * 1000,
  // 心跳超时时间
  hearbeat_timeout: 5000,
  // 是否自动重连
  is_reonnect: true,
  // 重连timer
  reconnect_timer: null,
  // 重连频率
  reconnect_interval: 5 * 1000,
  isReconnecting: false,
  reconnect_count: 0,
  reconnect_lock: false,
  maxReconnectTime: 5,
  userId: '',
  curUser: {},
  friends: getCache('friends') || [],
  provisionals: getCache('provisionals') || [],
  chatNum: getCache('chatNum') || 0,
  activeId: '',
  activeInfo: getCache('activeInfo') || {},
  msgList: [],
  applyFriends: [],
  userChange: 0,
  isOffline: false,
  onLine: [],
  groups: getCache('groups') || [],
  lastGroupUpdateTime: 0, // 防止重复请求的时间戳
  pendingGroupId: null, // 待打开的新创建群组ID
  isProcessingPendingGroup: false, // 正在处理待打开群组的标志
  isInitialConnection: true, // 标识是否处于初始连接阶段
  processedGroupJoinMessages: new Set(), // 记录已处理的群组加入消息，防重复
  initialConnectionCompleteTime: 0, // 初始连接完成时间
  lastCmd17RequestTime: 0, // 上次cmd17请求时间
  cmd17RequestThrottleTime: 2000, // cmd17请求节流时间（2秒）
  pendingCmd17Request: null, // 待执行的cmd17请求定时器
  cmd17RequestCount: 0, // cmd17请求计数器
  cmd17RequestReasons: [] // 记录最近的cmd17请求原因
}
const getters = {
  isconnect(state) {
    return state.isconnect
  },
  curUser(state) {
    return state.curUser
  },
  friends(state) {
    return state.friends
  },
  provisionals(state) {
    return state.provisionals
  },
  chatNum(state) {
    return state.chatNum
  },
  activeId(state) {
    return state.activeId
  },
  activeInfo(state) {
    return state.activeInfo
  },
  msgList(state) {
    return state.msgList
  },
  userId(state) {
    return state.userId
  },
  applyFriends(state) {
    return state.applyFriends
  },
  userChange(state) {
    return state.userChange
  },
  isOffline(state) {
    return state.isOffline
  },
  groups(state) {
    return state.groups
  }
}
// mutations 进行数据状态的变化
const mutations = {
  WEBSOCKET_INIT(state, url) {
    state.websock = new WebSocket(url)
  },
  WEBSOCKET_URL(state, url) {
    state.websock_url = url
  },
  // 连接成功
  WEBSOCKET_OPEN(state, data) {
    state.isconnect = data
  },
  // 设置当前用户
  SET_CURUSER(state, data) {
    state.curUser = data
  },
  // 设置好友
  SET_FRIENDS(state, data) {
    state.friends = data
    setCache('friends', data)
  },
  // 设置好友申请列表
  SET_APPLYFRIENDS(state, data) {
    state.applyFriends = data
  },
  // 设置临时会话
  SET_PROVISIONALS(state, data) {
    state.provisionals = data
    setCache('provisionals', data)
  },
  // 设置未读消息
  SET_CHATNUM(state, data) {
    state.chatNum = data
    setCache('chatNum', data)
  },
  // 设置当前会话
  SET_ACTIVEID(state, data) {
    state.activeId = data
  },
  // 设置当前会话信息
  SET_ACTIVEINFO(state, data) {
    state.activeInfo = data
    setCache('activeInfo', data)
  },
  // 设置消息列表
  SET_MSGLIST(state, data) {
    state.msgList = data
  },
  // 设置用户ID
  SET_USERID(state, data) {
    state.userId = data
    const userId = getCache('userId')
    if (userId && userId != data) {
      removeCache('friends')
      removeCache('provisionals')
      store.dispatch('WEBSOCKET_GETPROVISIONALS', [])
      removeCache('chatNum')
      store.dispatch('WEBSOCKET_SETCHATNUM', 0)
    }
    setCache('userId', data)
  },
  // 设置自动重连
  SET_RECONNECT(state, data) {
    state.is_reonnect = data
  },
  // 设置是否有用户登录或退出
  SET_USERCHANGE(state, data) {
    state.userChange = data
  },
  // 设置当前用户是否在其他登录
  SET_OFFLINE(state, data) {
    state.isOffline = data
  },
  SET_GROUPS(state, data) {
    state.groups = data
    setCache('groups', data)
  }
}
const actions = {
  WEBSOCKET_INIT({ commit }, url) {
    if (!!state.isconnect || !url) return
    commit('WEBSOCKET_URL', url)
    commit('WEBSOCKET_INIT', url)
    state.websock.onopen = function () {
      state.hearbeat_timer && clearInterval(state.hearbeat_timer)
      state.reconnect_timer && clearTimeout(state.reconnect_timer)
      commit('WEBSOCKET_OPEN', 1)
      // 启动心跳检测
      store.dispatch('WEBSOCKET_HEARTBEAT')
      // 初始化缓存数据
      if (state.friends.length === 0) {
        state.friends = getFriends()
      }
      if (state.provisionals.length === 0) {
        state.provisionals = getProvisionals()
      }
      if (state.chatNum === 0) {
        state.chatNum = getChatNum()
      }
      if (state.groups.length === 0) {
        state.groups = getGroups()
      }
    }
    state.websock.οnerrοr = function (e) {
      console.log('WebSocket连接发生错误')
      // 清除心跳检测
      state.hearbeat_timer && clearInterval(state.hearbeat_timer)
      reconnect()
    }
    state.websock.onclose = function (e) {
      console.log(new Date(), 'WebSocket连接已关闭')
      // 清除心跳检测
      state.hearbeat_timer && clearInterval(state.hearbeat_timer)
      const { friends, provisionals, chatNum, groups } = state
      saveFriends(friends)
      saveProvisionals(provisionals)
      saveChatNum(chatNum)
      saveGroups(groups)
      reconnect()
    }
    state.websock.onmessage = function (callBack) {
      const _data = JSON.parse(callBack.data)
      if (_data.command == 6) {
        // 登陆命令返回状态处理
        COMMAND_LOGIN_RESP(_data)
      } else if (_data.command == 9 || _data.command == 10) {
        // 有用户登录或退出，刷新用户实时列表
        COMMAND_USER_LOGIN_OUT(_data)
      } else if (_data.command == 11) {
        // 接收到聊天响应处理
        COMMAND_CHAT_RESP(_data)
      } else if (_data.command == 18) {
        // 获取用户信息响应处理
        COMMAND_GET_USER_RESP(_data)
      } else if (_data.command == 20 && _data.code == 10016) {
        // 处理离线消息
        COMMAND_GET_MESSAGE_RESP(_data, 0)
      } else if (_data.command == 20 && _data.code == 10018) {
        // 处理历史消息
        const msgObj = _data.data
        if (msgObj) COMMAND_GET_MESSAGE_RESP(_data, 1)
      } else if (_data.command == 21) {
        // 在线收到好友申请
        COMMAND_ADD_FRIEND_APPLY_ONLINE(_data)
      } else if (_data.command == 22) {
        //加入群组的消息通知处理;
        COMMAND_ADD_FRIEND_RESP(_data)
      } else if (_data.command == 23) {
        // 添加好友申请
        COMMAND_ADD_FRIEND_APPLY_RESP(_data)
      } else if (_data.command == 25) {
        // 处理好友申请
        COMMAND_ADD_FRIEND_APPLY_HANDLE()
      } else if (_data.command == 26) {
        // 申请添加好友被同意
        COMMAND_ADD_FRIEND_APPLY_AGREE(_data)
      } else if (_data.command == 28 && _data.code == 10026) {
        // 群组操作完成，需要重新获取用户信息
        COMMAND_GROUP_OPERATION_COMPLETE(_data)
      }
    }
  },
  // 登陆
  WEBSOCKET_OPEN({ commit }, data) {
    commit('WEBSOCKET_OPEN', data)
  },
  //发送消息
  WEBSOCKET_SEND({ commit }, data) {
    let _msg = JSON.stringify(data)
    if (!!state.isconnect) state.websock.send(_msg)
    else clearInterval(state.hearbeat_interval)
  },
  //关闭websocket
  WEBSOCKET_CLOSE({ commit }) {
    state.isconnect && state.websock.close()
    commit('WEBSOCKET_OPEN', 0)
    commit('WEBSOCKET_URL', null)
    state.hearbeat_timer && clearInterval(state.hearbeat_timer)
    state.reconnect_timer && clearTimeout(state.reconnect_timer)
  },
  // 获取当前用户
  WEBSOCKET_GETCURUSER({ commit }, data) {
    commit('SET_CURUSER', data)
  },
  // 获取好友
  WEBSOCKET_GETFRIENDS({ commit }, data) {
    commit('SET_FRIENDS', data)
  },
  // 获取好友申请列表
  WEBSOCKET_GETAPPLYFRIENDS({ commit }, data) {
    commit('SET_APPLYFRIENDS', data)
  },
  // 获取临时会话
  WEBSOCKET_GETPROVISIONALS({ commit }, data) {
    commit('SET_PROVISIONALS', data)
  },
  // 设置未读消息
  WEBSOCKET_SETCHATNUM({ commit }, data) {
    commit('SET_CHATNUM', data)
  },
  // 设置当前会话
  WEBSOCKET_SETACTIVEID({ commit }, data) {
    commit('SET_ACTIVEID', data)
  },
  // 设置当前会话信息
  WEBSOCKET_SETACTIVEINFO({ commit }, data) {
    commit('SET_ACTIVEINFO', data)
  },
  // 设置消息列表
  WEBSOCKET_SETMSGLIST({ commit }, data) {
    commit('SET_MSGLIST', data)
  },
  // 设置用户ID
  WEBSOCKET_SETUSERID({ commit }, data) {
    commit('SET_USERID', data)
  },
  // 设置自动重连
  WEBSOCKET_SETRECONNECT({ commit }, data) {
    commit('SET_RECONNECT', data)
  },
  // 设置是否有用户登录或退出
  WEBSOCKET_SETUSERCHANGE({ commit }, data) {
    commit('SET_USERCHANGE', data)
  },
  // 重新连接
  WEBSOCKET_RECONNECT({ commit }) {
    if (state.websock && !!state.isconnect) store.dispatch('WEBSOCKET_CLOSE')
    store.dispatch('WEBSOCKET_INIT', state.websock_url)
  },
  // 心跳检测
  WEBSOCKET_HEARTBEAT({ commit }) {
    state.hearbeat_timer && clearInterval(state.hearbeat_timer)
    state.hearbeat_timer = setInterval(() => {
      const heartCmd = { cmd: 13, hbbyte: -127 }
      store.dispatch('WEBSOCKET_SEND', heartCmd)
    }, state.hearbeat_interval)
  },
  // 设置当前用户是否在其他登录
  WEBSOCKET_SETOFFLINE({ commit }, data) {
    commit('SET_OFFLINE', data)
  },
  WEBSOCKET_GETGROUPS({ commit }, data) {
    commit('SET_GROUPS', data)
  }
}
export default { state, getters, actions, mutations }
// 重连
function reconnect() {
  store.dispatch('WEBSOCKET_OPEN', 0)
  state.reconnect_timer && clearTimeout(state.reconnect_timer)
  if (state.is_reonnect) {
    // 重连时重置初始连接状态，避免重连后的历史消息触发重复请求
    state.isInitialConnection = true
    state.initialConnectionCompleteTime = 0
    state.processedGroupJoinMessages.clear()
    // 重连时重置cmd17请求节流状态
    state.lastCmd17RequestTime = 0
    state.pendingCmd17Request && clearTimeout(state.pendingCmd17Request)
    state.pendingCmd17Request = null
    state.cmd17RequestCount = 0
    state.cmd17RequestReasons = []
    state.reconnect_timer = setTimeout(() => {
      store.dispatch('WEBSOCKET_RECONNECT')
    }, state.reconnect_interval)
  }
}
// cmd17请求节流函数
function throttleCmd17Request(reason = 'general') {
  const now = Date.now()
  const userId = state.userId || store.getters.name
  if (!userId) {
    console.log(`[WebSocket] 跳过cmd17请求 - 用户ID为空`)
    return
  }

  // 如果处于初始连接阶段，跳过非登录相关的请求
  if (state.isInitialConnection && reason !== '登录成功') {
    console.log(`[WebSocket] 跳过cmd17请求 - 初始连接阶段: ${reason}`)
    return
  }

  // 如果正在处理群组创建流程，跳过其他请求
  if (state.pendingGroupId && reason !== '群组创建成功') {
    console.log(`[WebSocket] 跳过cmd17请求 - 正在处理群组创建: ${reason}`)
    return
  }

  // 如果距离上次请求不足节流时间，则延迟执行
  if (now - state.lastCmd17RequestTime < state.cmd17RequestThrottleTime) {
    // 清除之前的待执行请求
    if (state.pendingCmd17Request) {
      clearTimeout(state.pendingCmd17Request)
      console.log(`[WebSocket] 清除之前的待执行cmd17请求`)
    }
    // 延迟到节流时间结束后执行
    const delay = state.cmd17RequestThrottleTime - (now - state.lastCmd17RequestTime)
    state.pendingCmd17Request = setTimeout(() => {
      state.cmd17RequestCount++
      state.cmd17RequestReasons.push(`${new Date().toLocaleTimeString()}: ${reason} (延迟)`)
      // 只保留最近10条记录
      if (state.cmd17RequestReasons.length > 10) {
        state.cmd17RequestReasons.shift()
      }

      console.log(`[WebSocket] 延迟执行cmd17请求 - 原因: ${reason}, 总计: ${state.cmd17RequestCount}`)
      console.log(`[WebSocket] 最近请求原因:`, state.cmd17RequestReasons)

      const userCmd = { cmd: 17, type: 2, userId }
      store.dispatch('WEBSOCKET_SEND', userCmd)
      state.lastCmd17RequestTime = Date.now()
      state.pendingCmd17Request = null
    }, delay)
    console.log(`[WebSocket] cmd17请求被节流，延迟${delay}ms执行 - 原因: ${reason}`)
    return
  }
  // 可以立即执行
  state.cmd17RequestCount++
  state.cmd17RequestReasons.push(`${new Date().toLocaleTimeString()}: ${reason}`)
  // 只保留最近10条记录
  if (state.cmd17RequestReasons.length > 10) {
    state.cmd17RequestReasons.shift()
  }

  console.log(`[WebSocket] 立即执行cmd17请求 - 原因: ${reason}, 总计: ${state.cmd17RequestCount}`)
  console.log(`[WebSocket] 最近请求原因:`, state.cmd17RequestReasons)

  const userCmd = { cmd: 17, type: 2, userId }
  store.dispatch('WEBSOCKET_SEND', userCmd)
  state.lastCmd17RequestTime = now
}
// 登陆命令返回状态处理
function COMMAND_LOGIN_RESP(data) {
  const userId = state.userId || store.getters.name
  if (10007 == data.code) {
    const msgCmd = { cmd: 19, type: 0, userId }
    const applyCmd = { cmd: 23, userId }
    // 使用节流函数发送cmd17请求
    throttleCmd17Request('登录成功')
    setTimeout(() => {
      store.dispatch('WEBSOCKET_SEND', msgCmd)
    }, 1)
    setTimeout(() => {
      store.dispatch('WEBSOCKET_SEND', applyCmd)
    }, 2)
    // 设置初始连接完成时间，用于区分初始连接和后续的新群组创建
    setTimeout(() => {
      state.isInitialConnection = false
      state.initialConnectionCompleteTime = Date.now()
      // 清理可能的历史处理记录
      state.processedGroupJoinMessages.clear()
    }, 5000) // 5秒后认为初始连接阶段结束

    store.dispatch('WEBSOCKET_SETRECONNECT', true)
    store.dispatch('WEBSOCKET_HEARTBEAT')
  }
  if (10008 == data.code) {
    store.dispatch('WEBSOCKET_SETRECONNECT', false)
    store.dispatch('WEBSOCKET_CLOSE')
  }
}
// 有用户登录或退出，刷新用户实时列表
function COMMAND_USER_LOGIN_OUT(data) {
  if (data.command == 10) store.dispatch('WEBSOCKET_SETUSERCHANGE', 1)
  else {
    store.dispatch('WEBSOCKET_SETUSERCHANGE', 0)
    const { userId } = data.user || ''
    if (userId) {
      const index = state.onLine.findIndex(item => item === userId)
      if (index == -1) {
        state.onLine.push(userId)
        store.dispatch('WEBSOCKET_SETOFFLINE', false)
      } else store.dispatch('WEBSOCKET_SETOFFLINE', true)
    }
    // 检查是否是群组创建成功消息
    if (data.group && data.msg && data.msg.includes('加入群组成功')) {
      // 创建消息标识符，用于防重复处理
      const messageId = `${data.group}_${data.msg}`
      // 如果处于初始连接阶段，记录但不处理（避免初始连接时的历史消息触发重复请求）
      if (state.isInitialConnection) {
        state.processedGroupJoinMessages.add(messageId)
        return
      }
      // 检查是否已经处理过这个消息（防重复）
      if (state.processedGroupJoinMessages.has(messageId)) {
        return
      }
      // 记录已处理的消息
      state.processedGroupJoinMessages.add(messageId)
      // 保存新创建的群组ID，稍后在用户信息更新后自动打开
      state.pendingGroupId = data.group
      // 触发重新获取用户信息，这会包含新创建的群组
      setTimeout(() => {
        throttleCmd17Request('群组创建成功')
      }, 500)
    }
  }
}
// 接收到聊天响应处理
function COMMAND_CHAT_RESP(data) {
  const userId = state.userId || store.getters.name
  let friends = state.friends
  let provisionals = state.provisionals
  let groups = state.groups || []
  let chatNum = state.chatNum
  const activeId = state.activeId
  const chatObj = data.data
  const createTime = parseTime(chatObj.createTime, '{y}/{m}/{d} {h}:{i}:{s}')
  const from = chatObj.from
  if (from == userId) return
  const content = chatObj.content
  // 如果是群组消息
  if (chatObj.groupId && chatObj.chatType === 1) {
    const groupId = chatObj.groupId
    console.log(`[WebSocket] 收到群组消息:`, {
      groupId,
      from,
      content: content.substring(0, 50) + (content.length > 50 ? '...' : ''),
      chatType: chatObj.chatType,
      activeId,
      groupsCount: groups.length
    })

    const gIdx = groups.findIndex(item => item.groupId === groupId)
    if (gIdx !== -1) {
      let msg
      if (isJSON(content)) msg = JSON.parse(content).title || content
      else msg = content
      const messageItem = {
        userId: from,
        nick: chatObj.nick,
        avatar: chatObj.avatar,
        msg: content,
        createTime,
        id: chatObj.id
      }
      groups[gIdx].msgList = [...(groups[gIdx].msgList || []), messageItem]
      groups[gIdx].msg = msg
      groups[gIdx].createTime = createTime
      // 如果不是当前活跃的群组，增加未读数量
      if (activeId !== groupId) {
        groups[gIdx].num = (groups[gIdx].num || 0) + 1
        chatNum += 1
        store.dispatch('WEBSOCKET_SETCHATNUM', chatNum)
        saveChatNum(chatNum)
      }
      store.dispatch('WEBSOCKET_GETGROUPS', groups)
      saveGroups(groups)
      // 如果是当前活跃的群组，更新消息列表
      if (activeId === groupId) {
        store.dispatch('WEBSOCKET_SETMSGLIST', groups[gIdx].msgList)
      }
      console.log(`[WebSocket] 群组消息处理完成: ${groupId}, 消息数量: ${groups[gIdx].msgList.length}`)
    } else {
      console.log(`[WebSocket] 警告: 未找到群组 ${groupId}，当前群组列表:`, groups.map(g => ({ id: g.groupId, name: g.name })))
      // 如果是新创建的群组，可能还没有同步到本地，尝试触发用户信息更新
      if (!state.pendingGroupId) {
        console.log(`[WebSocket] 尝试更新用户信息以获取新群组: ${groupId}`)
        throttleCmd17Request('群组消息-未找到群组')
      }
    }
    return
  }
  // 个人消息处理逻辑（原有逻辑）
  const user = getOnlineUserById(from)
  if (user) {
    const idx = friends.findIndex(item => item.userId === user.userId)
    if (idx !== -1) {
      let msg
      if (isJSON(content)) msg = JSON.parse(content).title || content
      else msg = content
      friends[idx].msg = msg
      friends[idx].createTime = createTime
      friends[idx].isDel = false
      if (activeId !== user.userId || activeId == 100 || activeId == 200 || activeId == 300) friends[idx].num += 1
      const item = { userId: user.userId, nick: user.nick, avatar: user.avatar, msg: content, createTime, id: chatObj.id }
      friends[idx].msgList = [...(friends[idx].msgList || []), item]
      store.dispatch('WEBSOCKET_GETFRIENDS', friends)
      saveFriends(friends)
      if (activeId === from) store.dispatch('WEBSOCKET_SETMSGLIST', friends[idx].msgList)
      if (activeId !== user.userId || activeId == 100 || activeId == 200 || activeId == 300) {
        chatNum += 1
        store.dispatch('WEBSOCKET_SETCHATNUM', chatNum)
        saveChatNum(chatNum)
      }
    }
  } else {
    let msg
    if (isJSON(content)) msg = JSON.parse(content).title || content
    else msg = content
    const userItem = {
      userId: from,
      nick: chatObj.nick,
      avatar: chatObj.avatar,
      num: 1,
      msg,
      createTime,
      isDel: false,
      msgList: [{ userId: from, nick: chatObj.nick, avatar: chatObj.avatar, msg: content, createTime, id: chatObj.id }]
    }
    const idx = provisionals.findIndex(item => item.userId === from)
    if (idx !== -1 && !chatObj.groupId) {
      provisionals[idx].msg = msg
      provisionals[idx].createTime = createTime
      provisionals[idx].isDel = false
      if (activeId !== from) provisionals[idx].num += 1
      const item = { userId: from, nick: chatObj.nick, avatar: chatObj.avatar, msg: content, createTime, id: chatObj.id }
      provisionals[idx].msgList = [...(provisionals[idx].msgList || []), item]
      store.dispatch('WEBSOCKET_GETPROVISIONALS', provisionals)
      saveProvisionals(provisionals)
      if (activeId === from) store.dispatch('WEBSOCKET_SETMSGLIST', provisionals[idx].msgList)
      if (activeId !== from) {
        chatNum += 1
        store.dispatch('WEBSOCKET_SETCHATNUM', chatNum)
        saveChatNum(chatNum)
      }
    } else {
      chatNum += 1
      store.dispatch('WEBSOCKET_SETCHATNUM', chatNum)
      saveChatNum(chatNum)
      provisionals.push(userItem)
      store.dispatch('WEBSOCKET_GETPROVISIONALS', provisionals)
      saveProvisionals(provisionals)
    }
  }
}
function getOnlineUserById(userId) {
  const friends = state.friends
  for (let u = 0; u < friends.length; u++) {
    const user = friends[u]
    if (user.userId == userId) return user
  }
}
// 获取用户信息响应处理
function COMMAND_GET_USER_RESP(data) {
  store.dispatch('WEBSOCKET_GETCURUSER', data.data)
  initOnlineUsers()
}
// 获取在线用户
function initOnlineUsers() {
  // 检查是否有待打开的新创建群组，使用互斥锁防止重复处理
  let groupIdToOpen = null
  if (state.pendingGroupId && !state.isProcessingPendingGroup) {
    state.isProcessingPendingGroup = true // 设置处理标志
    groupIdToOpen = state.pendingGroupId
    state.pendingGroupId = null // 立即清除，防止重复处理
  }
  // 初始化缓存数据
  if (state.friends.length === 0) {
    state.friends = getFriends()
  }
  if (state.provisionals.length === 0) {
    state.provisionals = getProvisionals()
  }
  if (state.chatNum === 0) {
    state.chatNum = getChatNum()
  }
  if (state.groups.length === 0) {
    state.groups = getGroups()
  }
  const friends = state.curUser.friends
  const groups = state.curUser.groups || []
  const localFriends = getFriends()
  const localGroups = getGroups()
  let onlineUser = []
  // 初始化好友列表
  for (let g = 0; g < friends.length; g++) {
    const group = friends[g]
    const users = group['users']
    for (let u = 0; u < users.length; u++) {
      const user = users[u]
      const item = { userId: user.userId, nick: user.nick, ext: user.ext || {}, avatar: user.avatar, num: 0, msg: undefined, msgList: [], show: false, isTop: false, isDel: false }
      onlineUser.push(item)
    }
  }
  // 初始化群组列表（排除默认群组）
  let onlineGroups = []
  for (let i = 0; i < groups.length; i++) {
    const group = groups[i]
    if (group.groupId !== '100') {
      // 排除默认群组
      const groupItem = {
        groupId: group.groupId,
        name: group.name,
        avatar: group.avatar,
        users: group.users || [],
        num: 0,
        msg: undefined,
        msgList: [],
        createTime: undefined,
        leader: group.leader
      }
      onlineGroups.push(groupItem)
    }
  }
  // 判断缓存好友列表里面的好友是否在其他地方已被删除，如已删除从当前的好友列表中删除
  let cacheFriends = getCache('friends') || []
  cacheFriends = cacheFriends.filter(item => {
    const idx = onlineUser.findIndex(online => online.userId === item.userId)
    return idx !== -1
  })
  const nowFriends = uniqueJsonArrByField([...onlineUser, ...cacheFriends], 'userId')
  nowFriends.map(item => {
    localFriends.map(localItem => {
      if (item.userId == localItem.userId) {
        item.isTop = localItem.isTop
        item.isDel = item.num ? false : localItem.isDel
        item.num = item.num || localItem.num
        item.msgList = uniqueJsonArrByField([...(item.msgList || []), ...(localItem.msgList || [])], ['id', 'createTime'])
        if (!!item.msgList.length) {
          item.msgList.sort((a, b) => new Date(a.createTime || '2000-01-01').getTime() - new Date(b.createTime || '2000-01-01').getTime())
          item.msg = item.msgList[item.msgList.length - 1].msg
          item.createTime = item.msgList[item.msgList.length - 1].createTime
        }
      }
    })
  })
  // 过滤本地缓存群组，只保留服务器端仍然存在的群组
  let cacheGroups = getCache('groups') || []
  cacheGroups = cacheGroups.filter(item => {
    const idx = onlineGroups.findIndex(online => online.groupId === item.groupId)
    return idx !== -1 // 只保留服务器仍然存在的群组
  })
  // 合并线上群组和过滤后的本地缓存群组
  const nowGroups = uniqueJsonArrByField([...onlineGroups, ...cacheGroups], 'groupId')
  nowGroups.map(item => {
    cacheGroups.map(localItem => {
      if (item.groupId === localItem.groupId) {
        item.num = item.num || localItem.num || 0
        item.msgList = uniqueJsonArrByField([...(item.msgList || []), ...(localItem.msgList || [])], ['id', 'createTime'])
        if (!!item.msgList.length) {
          item.msgList.sort((a, b) => new Date(a.createTime || '2000-01-01').getTime() - new Date(b.createTime || '2000-01-01').getTime())
          item.msg = item.msgList[item.msgList.length - 1].msg
          item.createTime = item.msgList[item.msgList.length - 1].createTime
        }
      }
    })
  })
  console.log(`[WebSocket] 群组同步完成: 服务器群组${onlineGroups.length}个, 本地缓存群组${localGroups.length}个, 最终群组${nowGroups.length}个`)
  // 检查是否有群组被解散，打印日志便于调试
  const removedGroups = localGroups.filter(local => !onlineGroups.find(online => online.groupId === local.groupId))
  if (removedGroups.length > 0) {
    console.log(
      `[WebSocket] 发现${removedGroups.length}个已解散的群组:`,
      removedGroups.map(g => ({ id: g.groupId, name: g.name }))
    )
  }
  store.dispatch('WEBSOCKET_GETFRIENDS', nowFriends)
  store.dispatch('WEBSOCKET_GETGROUPS', nowGroups)
  store.dispatch('WEBSOCKET_GETPROVISIONALS', getProvisionals())
  store.dispatch('WEBSOCKET_SETCHATNUM', getChatNum())
  // 如果当前正在群组聊天，更新activeInfo为最新的群组信息
  const activeId = state.activeId
  const activeInfo = state.activeInfo
  if (activeId && activeInfo && activeInfo.groupId) {
    const updatedGroup = nowGroups.find(group => group.groupId === activeId)
    if (updatedGroup) {
      // 保持原有的消息列表和其他状态，只更新群组成员信息
      const updatedActiveInfo = {
        ...activeInfo,
        users: updatedGroup.users,
        name: updatedGroup.name,
        avatar: updatedGroup.avatar,
        leader: updatedGroup.leader
      }
      store.dispatch('WEBSOCKET_SETACTIVEINFO', updatedActiveInfo)
      console.log(`[WebSocket] 更新当前活跃群组信息: ${updatedGroup.name} (${updatedGroup.groupId})`)
    } else {
      // 当前群组已不存在（可能被解散或用户被移除），清除活跃状态
      console.log(`[WebSocket] 当前活跃群组已被解散，清除聊天界面: ${activeInfo.name || activeId}`)
      store.dispatch('WEBSOCKET_SETACTIVEID', '')
      store.dispatch('WEBSOCKET_SETACTIVEINFO', {})
      store.dispatch('WEBSOCKET_SETMSGLIST', [])
      // 清除相关缓存
      removeCache('activeInfo')
    }
  }

  // 临时好友添加成功之后删除临时好友的数据
  let provisionals = state.provisionals
  const friendList = friends[0]['users']
  if (provisionals.length) {
    friendList.forEach(item => {
      const idx = provisionals.findIndex(provisional => provisional.userId === item.userId)
      if (idx !== -1) provisionals.splice(idx, 1)
    })
    // 判断临时好友列表是否在其他地方已被添加，如已添加从当前的临时好友列表中删除
    let cacheProvisionals = getCache('provisionals') || []
    cacheProvisionals = cacheProvisionals.filter(item => {
      const idx = friendList.findIndex(friend => friend.userId === item.userId)
      return idx === -1
    })
    const nowProvisionals = uniqueJsonArrByField([...provisionals, ...cacheProvisionals], 'userId')
    store.dispatch('WEBSOCKET_GETPROVISIONALS', nowProvisionals)
  }
  // 如果有需要打开的群组，在所有数据处理完成后触发
  if (groupIdToOpen) {
    const targetGroup = nowGroups.find(group => group.groupId === groupIdToOpen)
    if (targetGroup) {
      // 触发自动打开群组聊天的事件
      setTimeout(() => {
        // 通过Vue的事件总线或直接调用Chat组件的方法
        if (window.vueApp && window.vueApp.$refs && window.vueApp.$refs.chat) {
          window.vueApp.$refs.chat.handleGroupChat(groupIdToOpen)
        } else {
          // 如果直接调用不可行，使用事件触发
          const event = new CustomEvent('openNewGroup', { detail: { groupId: groupIdToOpen } })
          window.dispatchEvent(event)
        }
        // 处理完成，清除标志
        state.isProcessingPendingGroup = false
      }, 1000)
    } else {
      // 处理完成，清除标志
      state.isProcessingPendingGroup = false
    }
  }
}
// 处理离线/历史消息
function COMMAND_GET_MESSAGE_RESP(data, type) {
  // 处理离线消息
  if (!type) friendOfflineMessage(data.data)
  // 处理历史消息
  if (!!type) friendHistoryMessage(data.data)
}
// 处理离线消息
function friendOfflineMessage(msgObj) {
  const newFriends = msgObj.friends
  const newGroups = msgObj.groups
  let friends = state.friends
  let provisionals = state.provisionals
  let chatNum = getChatNum()
  const localFriends = getFriends() || []
  const localProvisionals = getProvisionals() || []
  for (let key in newFriends) {
    const friend = newFriends[key]
    for (let index in friend) {
      const userId = friend[index].from
      const createTime = parseTime(friend[index].createTime, '{y}/{m}/{d} {h}:{i}:{s}')
      const fIdx = friends.findIndex(item => item.userId === userId)
      const pIdx = provisionals.findIndex(item => item.userId === userId)
      const lfIdx = localFriends.findIndex(item => item.userId === userId)
      const lpIdx = localProvisionals.findIndex(item => item.userId === userId)
      if (fIdx != -1) {
        let msg
        if (isJSON(friend[index].content)) msg = JSON.parse(friend[index].content).title || friend[index].content
        else msg = friend[index].content
        const item = {
          userId,
          nick: friend[index].nick || friends[fIdx].nick,
          avatar: friend[index].avatar || friends[fIdx].avatar,
          msg: friend[index].content,
          createTime,
          id: friend[index].id
        }
        friends[fIdx].msgList.push(item)
        friends[fIdx].num = lfIdx != -1 ? (localFriends[lfIdx].num += 1) : (friends[fIdx].num += 1)
        friends[fIdx].msg = msg
        friends[fIdx].createTime = createTime
        store.dispatch('WEBSOCKET_GETFRIENDS', friends)
        chatNum += 1
        store.dispatch('WEBSOCKET_SETCHATNUM', chatNum)
      } else if (pIdx != -1) {
        let msg
        if (isJSON(friend[index].content)) msg = JSON.parse(friend[index].content).title || friend[index].content
        else msg = friend[index].content
        const item = {
          userId,
          nick: friend[index].nick,
          avatar: friend[index].avatar,
          msg: friend[index].content,
          createTime,
          id: friend[index].id
        }
        provisionals[pIdx].msgList.push(item)
        provisionals[pIdx].num = lpIdx != -1 ? (localProvisionals[lpIdx].num += 1) : (provisionals[pIdx].num += 1)
        provisionals[pIdx].msg = msg
        provisionals[pIdx].createTime = createTime
        store.dispatch('WEBSOCKET_GETPROVISIONALS', provisionals)
        chatNum += 1
        store.dispatch('WEBSOCKET_SETCHATNUM', chatNum)
      } else {
        const item = {
          userId,
          nick: friend[index].nick,
          avatar: friend[index].avatar,
          num: 1,
          msg: friend[index].content,
          createTime,
          msgList: [{ userId, nick: friend[index].nick, avatar: friend[index].avatar, msg: friend[index].content, createTime, id: friend[index].id }],
          show: false,
          isTop: false,
          isDel: false
        }
        provisionals.push(item)
        store.dispatch('WEBSOCKET_GETPROVISIONALS', provisionals)
        chatNum += 1
        store.dispatch('WEBSOCKET_SETCHATNUM', chatNum)
      }
    }
  }
  for (let gkey in newGroups) {
    const group = newGroups[gkey]
    for (let gindex in group) {
      const guserId = group[gindex].from
      const createTime = parseTime(group[gindex].createTime, '{y}/{m}/{d} {h}:{i}:{s}')
      const gFidx = friends.findIndex(item => item.userId == guserId)
      if (gFidx != -1) {
        let gmsg
        if (isJSON(group[gindex].content)) gmsg = JSON.parse(group[gindex].content).title || group[gindex].content
        else gmsg = group[gindex].content
        const item = {
          userId: guserId,
          nick: group[gindex].nick || friends[gFidx].nick,
          avatar: group[gindex].avatar || friends[gFidx].avatar,
          msg: group[gindex].content,
          createTime,
          id: group[gindex].id
        }
        friends[gFidx].msgList.push(item)
        friends[gFidx].num += 1
        friends[gFidx].msg = gmsg
        friends[gFidx].createTime = createTime
        store.dispatch('WEBSOCKET_GETFRIENDS', friends)
        chatNum += 1
        store.dispatch('WEBSOCKET_SETCHATNUM', chatNum)
      }
    }
  }
}
// 处理历史消息
function friendHistoryMessage(msgObj) {
  const activeId = state.activeId
  const newFriends = msgObj.friends || {}
  let friends = state.friends
  let provisionals = state.provisionals
  let groups = state.groups || []
  // 检查是否是群组历史消息
  const isGroupMessage = groups.some(g => g.groupId === activeId)
  if (isGroupMessage) {
    // 处理群组历史消息
    const gIdx = groups.findIndex(item => item.groupId === activeId)
    if (gIdx !== -1) {
      const group = groups[gIdx]
      const historyMsg = newFriends[activeId] || []
      if (!!historyMsg.length) {
        let arr = []
        historyMsg.map(item => {
          arr.push({
            userId: item.from,
            nick: item.nick || '未知用户',
            avatar: item.avatar || '',
            msg: item.content,
            createTime: parseTime(item.createTime, '{y}/{m}/{d} {h}:{i}:{s}'),
            id: item.id
          })
        })
        let msgList = uniqueJsonArrByField([...arr, ...(groups[gIdx].msgList || [])], ['id', 'createTime'])
        msgList.sort((a, b) => new Date(a.createTime || '2000-01-01').getTime() - new Date(b.createTime || '2000-01-01').getTime())
        groups[gIdx].msgList = msgList
        if (msgList.length > 0) {
          let msg
          if (isJSON(msgList[msgList.length - 1].msg)) msg = JSON.parse(msgList[msgList.length - 1].msg).title || msgList[msgList.length - 1].msg
          else msg = msgList[msgList.length - 1].msg
          groups[gIdx].msg = msg
          groups[gIdx].createTime = msgList[msgList.length - 1].createTime
        }
        store.dispatch('WEBSOCKET_GETGROUPS', groups)
        store.dispatch('WEBSOCKET_SETMSGLIST', msgList)
        saveGroups(groups)
      }
    }
    return
  }

  // 个人消息历史处理逻辑（原有逻辑）
  const fIdx = friends.findIndex(item => item.userId === activeId)
  const pIdx = provisionals.findIndex(item => item.userId === activeId)
  if (fIdx != -1) {
    const friend = friends[fIdx]
    const historyMsg = newFriends[activeId] || []
    if (!!historyMsg.length) {
      let arr = []
      historyMsg.map(item => {
        arr.push({ userId: item.from, nick: item.nick || friend.nick, avatar: item.avatar || friend.avatar, msg: item.content, createTime: parseTime(item.createTime, '{y}/{m}/{d} {h}:{i}:{s}'), id: item.id })
      })
      let msgList = uniqueJsonArrByField([...arr, ...(friends[fIdx].msgList || [])], ['id', 'createTime'])
      msgList.sort((a, b) => new Date(a.createTime || '2000-01-01').getTime() - new Date(b.createTime || '2000-01-01').getTime())
      friends[fIdx].msgList = msgList
      if (msgList.length > 0) {
        let msg
        if (isJSON(msgList[msgList.length - 1].msg)) msg = JSON.parse(msgList[msgList.length - 1].msg).title || msgList[msgList.length - 1].msg
        else msg = msgList[msgList.length - 1].msg
        friends[fIdx].msg = msg
        friends[fIdx].createTime = msgList[msgList.length - 1].createTime
      }
      store.dispatch('WEBSOCKET_GETFRIENDS', friends)
      store.dispatch('WEBSOCKET_SETMSGLIST', msgList)
    }
  }
  if (pIdx != -1) {
    const historyMsg = newFriends[activeId] || []
    if (!!historyMsg.length) {
      let arr = []
      historyMsg.map(item => {
        arr.push({ userId: item.from, nick: item.nick, avatar: item.avatar, msg: item.content, createTime: parseTime(item.createTime, '{y}/{m}/{d} {h}:{i}:{s}'), id: item.id })
      })
      let msgList = uniqueJsonArrByField([...arr, ...(provisionals[pIdx].msgList || [])], ['id', 'createTime'])
      msgList.sort((a, b) => new Date(a.createTime || '2000-01-01').getTime() - new Date(b.createTime || '2000-01-01').getTime())
      provisionals[pIdx].msgList = msgList
      if (msgList.length > 0) {
        let msg
        if (isJSON(msgList[msgList.length - 1].msg)) msg = JSON.parse(msgList[msgList.length - 1].msg).title || msgList[msgList.length - 1].msg
        else msg = msgList[msgList.length - 1].msg
        provisionals[pIdx].msg = msg
        provisionals[pIdx].createTime = msgList[msgList.length - 1].createTime
      }
      store.dispatch('WEBSOCKET_GETPROVISIONALS', provisionals)
      store.dispatch('WEBSOCKET_SETMSGLIST', msgList)
    }
  }
}
//添加好友响应状态处理;
function COMMAND_ADD_FRIEND_RESP(data) {
  const res = isJSON(data.data) ? JSON.parse(data.data) : data.data
  const type = res.type || ''

  console.log(`[WebSocket] 收到添加好友响应:`, {
    type,
    data: res,
    pendingGroupId: state.pendingGroupId,
    isInitialConnection: state.isInitialConnection
  })

  // 如果正在处理群组创建流程，跳过cmd17请求
  if (state.pendingGroupId) {
    console.log(`[WebSocket] 跳过添加好友响应处理 - 正在处理群组创建流程`)
    return
  }

  if (type != 4) {
    throttleCmd17Request('添加好友响应')
  }
}
// 添加好友申请
function COMMAND_ADD_FRIEND_APPLY_RESP(data) {
  const res = data.data
  const result = res.filter(item => item.status === 0)
  store.dispatch('WEBSOCKET_GETAPPLYFRIENDS', result)
}
// 在线收到好友申请
function COMMAND_ADD_FRIEND_APPLY_ONLINE(data) {
  const res = data.data
  const index = state.applyFriends.findIndex(item => item.from === res.from)
  let result = [...state.applyFriends]
  if (index < 0) result = [res, ...state.applyFriends]
  store.dispatch('WEBSOCKET_GETAPPLYFRIENDS', result)
}
// 处理好友申请
function COMMAND_ADD_FRIEND_APPLY_HANDLE() {
  console.log(`[WebSocket] 收到处理好友申请消息:`, {
    pendingGroupId: state.pendingGroupId,
    isInitialConnection: state.isInitialConnection
  })

  // 如果正在处理群组创建流程，跳过cmd17请求
  if (state.pendingGroupId) {
    console.log(`[WebSocket] 跳过处理好友申请 - 正在处理群组创建流程`)
    return
  }

  // 重新请求个人信息好友列表及好友申请
  const userId = state.userId || store.getters.name
  const applyCmd = { cmd: 23, userId }
  throttleCmd17Request('处理好友申请')
  setTimeout(() => {
    store.dispatch('WEBSOCKET_SEND', applyCmd)
  }, 1)
}
// 处理好友申请响应
function COMMAND_ADD_FRIEND_APPLY_AGREE(data) {
  const index = state.friends.findIndex(item => item.userId === data.data.to)
  if (index < 0) {
    const item = { userId: data.data.to, nick: data.data.toNick, ext: { friendType: 'user' }, avatar: data.data.toAvatar, num: 0, msg: '', createTime: '', msgList: [], show: false, isTop: false, isDel: false }
    const friends = [item, ...state.friends]
    store.dispatch('WEBSOCKET_GETFRIENDS', friends)
  }
}
// 处理群组操作完成
function COMMAND_GROUP_OPERATION_COMPLETE(data) {
  const activeInfo = state.activeInfo
  const currentUserId = state.userId || store.getters.name
  const operationGroupId = data.groupId || (activeInfo && activeInfo.groupId)

  console.log(`[WebSocket] 收到群组操作完成消息:`, {
    code: data.code,
    msg: data.msg,
    groupId: operationGroupId,
    pendingGroupId: state.pendingGroupId,
    isInitialConnection: state.isInitialConnection
  })

  // 如果当前有待打开的群组，说明这是群组创建流程，不需要重复处理
  if (state.pendingGroupId) {
    console.log(`[WebSocket] 跳过群组操作完成处理 - 正在处理群组创建流程`)
    return
  }

  // 如果是初始连接阶段，也跳过处理，避免历史消息触发重复请求
  if (state.isInitialConnection) {
    console.log(`[WebSocket] 跳过群组操作完成处理 - 处于初始连接阶段`)
    return
  }

  if (operationGroupId) {
    const currentGroups = state.groups || []
    const updatedGroups = currentGroups.filter(group => group.groupId !== operationGroupId)
    if (updatedGroups.length !== currentGroups.length) {
      console.log(`[WebSocket] 群组被移除: ${operationGroupId}`)
      store.dispatch('WEBSOCKET_GETGROUPS', updatedGroups)
      saveGroups(updatedGroups)
      updateGroupsInCache(updatedGroups, currentUserId)
      // 清除当前会话状态（如果是退出的群组）
      const isCurrentActiveGroup = activeInfo && activeInfo.groupId === operationGroupId
      if (isCurrentActiveGroup) {
        store.dispatch('WEBSOCKET_SETACTIVEID', '')
        store.dispatch('WEBSOCKET_SETACTIVEINFO', {})
        store.dispatch('WEBSOCKET_SETMSGLIST', [])
        removeCache('activeInfo')
      }
      removeCache('groups')
    }
  }

  // 防重复请求 - 增加更严格的条件判断
  const now = Date.now()
  if (!state.lastGroupUpdateTime || now - state.lastGroupUpdateTime > 2000) { // 增加到2秒间隔
    state.lastGroupUpdateTime = now
    // 只有在非群组创建相关的消息时才发送cmd17请求
    if (!data.msg || (!data.msg.includes('群组') && !data.msg.includes('加入'))) {
      console.log(`[WebSocket] 发送cmd17请求 - 群组操作完成`)
      throttleCmd17Request('群组操作完成')
      setTimeout(() => {
        store.dispatch('WEBSOCKET_SEND', { cmd: 23, userId: currentUserId })
      }, 100)
    } else {
      console.log(`[WebSocket] 跳过cmd17请求 - 群组相关消息: ${data.msg}`)
    }
  } else {
    console.log(`[WebSocket] 跳过cmd17请求 - 防重复机制`)
  }
}
// 设置缓存
function setCache(key, value) {
  return localStorage.setItem(key, JSON.stringify(value))
}
// 获取缓存
function getCache(key) {
  return JSON.parse(localStorage.getItem(key))
}
// 删除缓存
function removeCache(key) {
  return localStorage.removeItem(key)
}
