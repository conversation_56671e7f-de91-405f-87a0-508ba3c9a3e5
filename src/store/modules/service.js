import store from '@/store'
import { parseTime, uniqueJsonArrByField } from '@/utils/ruoyi'
import { isJSON } from '@/utils'

const state = {
  service_websock: null,
  service_websockUrl: null,
  // 心跳timer
  service_hearbeat_timer: null,
  // 心跳发送频率
  service_hearbeat_interval: 6 * 1000,
  // 是否自动重连
  service_is_reonnect: true,
  // 重连次数
  service_reconnect_count: 3,
  // 已发起重连次数
  service_reconnect_current: 1,
  // 重连timer
  service_reconnect_timer: null,
  // 重连频率
  service_reconnect_interval: 6 * 1000,
  service_isconnect: 0,
  service_curUser: {},
  service_friends: getCache('service_friends') || [],
  service_provisionals: getCache('service_provisionals') || [],
  service_chatNum: getCache('service_chatNum') || 0,
  service_activeId: null,
  service_activeInfo: getCache('service_activeInfo') || {},
  service_msgList: [],
  service_userId: null
}
const getters = {
  service_isconnect(state) {
    return state.service_isconnect
  },
  service_curUser(state) {
    return state.service_curUser
  },
  service_friends(state) {
    return state.service_friends
  },
  service_provisionals(state) {
    return state.service_provisionals
  },
  service_chatNum(state) {
    return state.service_chatNum
  },
  service_activeId(state) {
    return state.service_activeId
  },
  service_activeInfo(state) {
    return state.service_activeInfo
  },
  service_msgList(state) {
    return state.service_msgList
  },
  service_userId(state) {
    return state.service_userId
  }
}
// mutations 进行数据状态的变化
const mutations = {
  SERVICE_WEBSOCKET_INIT(state, url) {
    state.service_websock = new WebSocket(url)
  },
  SERVICE_WEBSOCKET_URL(state, url) {
    state.service_websockUrl = url
  },
  // 连接成功
  SERVICE_WEBSOCKET_OPEN(state, data) {
    state.service_isconnect = data
  },
  // 设置当前用户
  SERVICE_SET_CURUSER(state, data) {
    state.service_curUser = data
    setCache('service_curUser', data)
  },
  // 设置好友
  SERVICE_SET_FRIENDS(state, data) {
    state.service_friends = data
    setCache('service_friends', data)
  },
  // 设置临时会话
  SERVICE_SET_PROVISIONALS(state, data) {
    state.service_provisionals = data
    setCache('service_provisionals', data)
  },
  // 设置未读消息
  SERVICE_SET_CHATNUM(state, data) {
    state.service_chatNum = data
    setCache('service_chatNum', data)
  },
  // 设置当前会话
  SERVICE_SET_ACTIVEID(state, data) {
    state.service_activeId = data
  },
  // 设置当前会话信息
  SERVICE_SET_ACTIVEINFO(state, data) {
    state.service_activeInfo = data
    setCache('service_activeInfo', data)
  },
  // 设置消息列表
  SERVICE_SET_MSGLIST(state, data) {
    state.service_msgList = data
  },
  // 设置用户ID
  SERVICE_SET_USERID(state, data) {
    state.service_userId = data
    const userId = getCache('userId')
    if (userId && userId != data) {
      removeCache('service_friends')
      removeCache('service_provisionals')
      store.dispatch('SERVICE_WEBSOCKET_GETPROVISIONALS', [])
      removeCache('service_chatNum')
      store.dispatch('SERVICE_WEBSOCKET_SETCHATNUM', 0)
    }
    setCache('userId', data)
  },
  // 设置自动重连
  SERVICE_SET_RECONNECT(state, data) {
    state.service_is_reonnect = data
  }
}
const actions = {
  SERVICE_WEBSOCKET_INIT({ commit }, url) {
    if (!!state.service_isconnect || !url) return
    commit('SERVICE_WEBSOCKET_URL', url)
    commit('SERVICE_WEBSOCKET_INIT', url)
    state.service_websock.onopen = function () {
      state.service_hearbeat_timer && clearInterval(state.service_hearbeat_timer)
      state.service_reconnect_timer && clearTimeout(state.service_reconnect_timer)
      commit('SERVICE_WEBSOCKET_OPEN', 1)
    }
    state.service_websock.οnerrοr = function (e) {
      console.log('WebSocket连接发生错误')
      reconnect()
    }
    state.service_websock.onclose = function (e) {
      console.log('WebSocket连接已关闭')
      reconnect()
    }
    state.service_websock.onmessage = function (callBack) {
      const _data = JSON.parse(callBack.data)
      if (_data.command == 6) {
        // 登陆命令返回状态处理
        COMMAND_LOGIN_RESP(_data)
      } else if (_data.command == 11) {
        // 接收到聊天响应处理
        COMMAND_CHAT_RESP(_data)
      } else if (_data.command == 18) {
        // 获取用户信息响应处理
        COMMAND_GET_USER_RESP(_data)
      } else if (_data.command == 20 && _data.code == 10016) {
        // 处理离线消息
        COMMAND_GET_MESSAGE_RESP(_data, 0)
      } else if (_data.command == 20 && _data.code == 10018) {
        // 处理历史消息
        const msgObj = _data.data
        if (msgObj) COMMAND_GET_MESSAGE_RESP(_data, 1)
      } else if (_data.command == 22) {
        //加入群组的消息通知处理;
        COMMAND_ADD_FRIEND_RESP()
      }
    }
  },
  // 登陆
  SERVICE_WEBSOCKET_OPEN({ commit }, data) {
    commit('SERVICE_WEBSOCKET_OPEN', data)
  },
  //发送消息
  SERVICE_WEBSOCKET_SEND({ commit }, data) {
    let _msg = JSON.stringify(data)
    if (!!state.service_isconnect) state.service_websock.send(_msg)
    else clearInterval(state.service_hearbeat_interval)
  },
  //关闭service_websocket
  SERVICE_WEBSOCKET_CLOSE({ commit }) {
    state.service_isconnect && state.service_websock.close()
    commit('SERVICE_WEBSOCKET_OPEN', 0)
    commit('SERVICE_WEBSOCKET_URL', null)
    state.service_hearbeat_timer && clearInterval(state.service_hearbeat_timer)
    state.service_reconnect_timer && clearTimeout(state.service_reconnect_timer)
  },
  // 获取当前用户
  SERVICE_WEBSOCKET_GETCURUSER({ commit }, data) {
    commit('SERVICE_SET_CURUSER', data)
  },
  // 获取好友
  SERVICE_WEBSOCKET_GETFRIENDS({ commit }, data) {
    commit('SERVICE_SET_FRIENDS', data)
  },
  // 获取临时会话
  SERVICE_WEBSOCKET_GETPROVISIONALS({ commit }, data) {
    commit('SERVICE_SET_PROVISIONALS', data)
  },
  // 设置未读消息
  SERVICE_WEBSOCKET_SETCHATNUM({ commit }, data) {
    commit('SERVICE_SET_CHATNUM', data)
  },
  // 设置当前会话
  SERVICE_WEBSOCKET_SETACTIVEID({ commit }, data) {
    commit('SERVICE_SET_ACTIVEID', data)
  },
  // 设置当前会话信息
  SERVICE_WEBSOCKET_SETACTIVEINFO({ commit }, data) {
    commit('SERVICE_SET_ACTIVEINFO', data)
  },
  // 设置消息列表
  SERVICE_WEBSOCKET_SETMSGLIST({ commit }, data) {
    commit('SERVICE_SET_MSGLIST', data)
  },
  // 设置用户ID
  SERVICE_WEBSOCKET_SETUSERID({ commit }, data) {
    commit('SERVICE_SET_USERID', data)
  },
  // 设置自动重连
  SERVICE_WEBSOCKET_SETRECONNECT({ commit }, data) {
    commit('SERVICE_SET_RECONNECT', data)
  },
  // 重新连接
  SERVICE_WEBSOCKET_RECONNECT({ commit }) {
    if (state.service_websock && !!state.service_isconnect) store.dispatch('SERVICE_WEBSOCKET_CLOSE')
    store.dispatch('SERVICE_WEBSOCKET_INIT', state.service_websockUrl)
  },
  // 心跳检测
  SERVICE_WEBSOCKET_HEARTBEAT({ commit }) {
    state.service_hearbeat_timer && clearInterval(state.service_hearbeat_timer)
    state.service_hearbeat_timer = setInterval(() => {
      const heartCmd = { cmd: 13, hbbyte: -127 }
      store.dispatch('SERVICE_WEBSOCKET_SEND', heartCmd)
    }, state.service_hearbeat_interval)
  }
}
export default { state, getters, actions, mutations }
// 重连
function reconnect() {
  store.dispatch('SERVICE_WEBSOCKET_OPEN', 0)
  state.service_reconnect_timer && clearTimeout(state.service_reconnect_timer)
  if (state.service_is_reonnect) {
    state.service_reconnect_timer = setTimeout(() => {
      store.dispatch('SERVICE_WEBSOCKET_RECONNECT')
    }, state.service_reconnect_interval)
  }
}
// 登陆命令返回状态处理
function COMMAND_LOGIN_RESP(data) {
  const userId = state.service_userId || store.getters.service
  if (10007 == data.code) {
    const userCmd = { cmd: 17, type: 2, userId }
    const msgCmd = { cmd: 19, type: 0, userId }
    store.dispatch('SERVICE_WEBSOCKET_SEND', userCmd)
    setTimeout(() => {
      store.dispatch('SERVICE_WEBSOCKET_SEND', msgCmd)
    }, 1)
    store.dispatch('SERVICE_WEBSOCKET_SETRECONNECT', true)
    store.dispatch('SERVICE_WEBSOCKET_HEARTBEAT')
  }
  if (10008 == data.code) {
    store.dispatch('SERVICE_WEBSOCKET_SETRECONNECT', false)
    store.dispatch('SERVICE_WEBSOCKET_CLOSE')
  }
}
// 接收到聊天响应处理
function COMMAND_CHAT_RESP(data) {
  const userId = state.service_userId || store.getters.service
  let service_friends = state.service_friends
  let service_provisionals = state.service_provisionals
  let service_chatNum = state.service_chatNum
  const service_activeId = state.service_activeId
  const chatObj = data.data
  const createTime = parseTime(chatObj.createTime, '{y}/{m}/{d} {h}:{i}:{s}')
  const ext = chatObj.ext || {}
  const from = chatObj.from
  if (from == userId) return
  const content = chatObj.content
  const user = getOnlineUserById(from)
  if (user) {
    const idx = service_friends.findIndex(item => item.userId === user.userId)
    if (idx !== -1) {
      let msg
      if (isJSON(content)) msg = JSON.parse(content).title || content
      else msg = content
      service_friends[idx].msg = msg
      service_friends[idx].createTime = createTime
      service_friends[idx].isDel = false
      if (service_activeId !== user.userId) service_friends[idx].num += 1
      const item = { userId: user.userId, nick: user.nick, avatar: user.avatar, msg: content, createTime, id: chatObj.id }
      service_friends[idx].msgList = [...service_friends[idx].msgList, item]
      store.dispatch('SERVICE_WEBSOCKET_GETFRIENDS', service_friends)
      if (service_activeId === from) store.dispatch('SERVICE_WEBSOCKET_SETMSGLIST', service_friends[idx].msgList)
      if (service_activeId !== user.userId) {
        service_chatNum += 1
        store.dispatch('SERVICE_WEBSOCKET_SETCHATNUM', service_chatNum)
      }
    }
  } else {
    let msg
    if (isJSON(content)) msg = JSON.parse(content).title || content
    else msg = content
    const userItem = {
      userId: from,
      nick: chatObj.nick,
      avatar: chatObj.avatar,
      num: 1,
      msg,
      createTime,
      msgList: [{ userId: from, nick: chatObj.nick, avatar: chatObj.avatar, msg: content, createTime, id: chatObj.id }]
    }
    const idx = service_provisionals.findIndex(item => item.userId === from)
    if (idx !== -1 && !chatObj.groupId) {
      service_provisionals[idx].msg = msg
      service_provisionals[idx].createTime = createTime
      provisionals[idx].isDel = false
      if (service_activeId !== from) service_provisionals[idx].num += 1
      const item = { userId: from, nick: chatObj.nick, avatar: chatObj.avatar, msg: content, createTime, id: chatObj.id }
      service_provisionals[idx].msgList = [...service_provisionals[idx].msgList, item]
      store.dispatch('SERVICE_WEBSOCKET_GETPROVISIONALS', service_provisionals)
      if (service_activeId === from) store.dispatch('SERVICE_WEBSOCKET_SETMSGLIST', service_provisionals[idx].msgList)
      if (service_activeId !== from) {
        service_chatNum += 1
        store.dispatch('SERVICE_WEBSOCKET_SETCHATNUM', service_chatNum)
      }
    } else {
      service_chatNum += 1
      store.dispatch('SERVICE_WEBSOCKET_SETCHATNUM', service_chatNum)
      service_provisionals.push(userItem)
      store.dispatch('SERVICE_WEBSOCKET_GETPROVISIONALS', service_provisionals)
    }
  }
}
function getOnlineUserById(userId) {
  const service_friends = state.service_friends
  for (let u = 0; u < service_friends.length; u++) {
    const user = service_friends[u]
    if (user.userId == userId) return user
  }
}
// 获取用户信息响应处理
function COMMAND_GET_USER_RESP(data) {
  store.dispatch('SERVICE_WEBSOCKET_GETCURUSER', data.data)
  initOnlineUsers()
}
// 获取在线用户
function initOnlineUsers() {
  const service_friends = state.service_curUser.friends
  let onlineUser = []
  for (let g = 0; g < service_friends.length; g++) {
    const group = service_friends[g]
    const users = group['users']
    for (let u = 0; u < users.length; u++) {
      const user = users[u]
      const item = { userId: user.userId, ext: user.ext || {}, nick: user.nick, avatar: user.avatar, num: 0, msg: undefined, msgList: [] }
      onlineUser.push(item)
    }
  }
  // 判断缓存好友列表里面的好友是否在其他地方已被删除，如已删除从当前的好友列表中删除
  let cacheFriends = getCache('service_friends') || []
  cacheFriends = cacheFriends.filter(item => {
    const idx = onlineUser.findIndex(online => online.userId === item.userId)
    return idx !== -1
  })
  const nowFriends = uniqueJsonArrByField([...onlineUser, ...cacheFriends], 'userId')
  store.dispatch('SERVICE_WEBSOCKET_GETFRIENDS', nowFriends)
  // 临时好友添加成功之后删除临时好友的数据
  let service_provisionals = state.service_provisionals
  const friendList = service_friends.length ? service_friends[0]['users'] : []
  if (service_provisionals.length) {
    friendList.forEach(item => {
      const idx = service_provisionals.findIndex(provisional => provisional.userId === item.userId)
      if (idx !== -1) service_provisionals.splice(idx, 1)
    })
    // 判断临时好友列表是否在其他地方已被添加，如已添加从当前的临时好友列表中删除
    let cacheProvisionals = getCache('service_provisionals') || []
    cacheProvisionals = cacheProvisionals.filter(item => {
      const idx = friendList.findIndex(friend => friend.userId === item.userId)
      return idx === -1
    })
    const nowProvisionals = uniqueJsonArrByField([...service_provisionals, ...cacheProvisionals], 'userId')
    store.dispatch('SERVICE_WEBSOCKET_GETPROVISIONALS', nowProvisionals)
  }
}
// 处理离线/历史消息
function COMMAND_GET_MESSAGE_RESP(data, type) {
  // 处理离线消息
  if (!type) friendOfflineMessage(data.data)
  // 处理历史消息
  if (!!type) friendHistoryMessage(data.data)
}
// 处理离线消息
function friendOfflineMessage(msgObj) {
  const newFriends = msgObj.friends
  const newGroups = msgObj.groups
  let service_friends = state.service_friends
  let service_provisionals = state.service_provisionals
  let service_chatNum = state.service_chatNum
  for (let key in newFriends) {
    const friend = newFriends[key]
    for (let index in friend) {
      const userId = friend[index].from
      const createTime = parseTime(friend[index].createTime, '{y}/{m}/{d} {h}:{i}:{s}')
      const fIdx = service_friends.findIndex(item => item.userId === userId)
      const pIdx = service_provisionals.findIndex(item => item.userId === userId)
      if (fIdx != -1) {
        let msg
        if (isJSON(friend[index].content)) msg = JSON.parse(friend[index].content).title || friend[index].content
        else msg = friend[index].content
        const item = {
          userId,
          nick: friend[index].nick || service_friends[fIdx].nick,
          avatar: friend[index].avatar || service_friends[fIdx].avatar,
          msg: friend[index].content,
          createTime,
          id: friend[index].id
        }
        service_friends[fIdx].msgList.push(item)
        service_friends[fIdx].num += 1
        service_friends[fIdx].msg = msg
        service_friends[fIdx].createTime = createTime
        store.dispatch('SERVICE_WEBSOCKET_GETFRIENDS', service_friends)
        service_chatNum += 1
        store.dispatch('SERVICE_WEBSOCKET_SETCHATNUM', service_chatNum)
      } else if (pIdx != -1) {
        let msg
        if (isJSON(friend[index].content)) msg = JSON.parse(friend[index].content).title || friend[index].content
        else msg = friend[index].content
        const item = {
          userId,
          nick: friend[index].nick,
          avatar: friend[index].avatar,
          msg: friend[index].content,
          createTime,
          id: friend[index].id
        }
        service_provisionals[pIdx].msgList.push(item)
        service_provisionals[pIdx].num += 1
        service_provisionals[pIdx].msg = msg
        service_provisionals[pIdx].createTime = createTime
        store.dispatch('SERVICE_WEBSOCKET_GETPROVISIONALS', service_provisionals)
        service_chatNum += 1
        store.dispatch('SERVICE_WEBSOCKET_SETCHATNUM', service_chatNum)
      } else {
        const item = {
          userId,
          nick: friend[index].nick,
          avatar: friend[index].avatar,
          num: 1,
          msg: friend[index].content,
          createTime,
          msgList: [{ userId, nick: friend[index].nick, avatar: friend[index].avatar, msg: friend[index].content, createTime, id: friend[index].id }],
          show: false,
          isTop: false,
          isDel: false
        }
        service_provisionals.push(item)
        store.dispatch('SERVICE_WEBSOCKET_GETPROVISIONALS', service_provisionals)
        service_chatNum += 1
        store.dispatch('SERVICE_WEBSOCKET_SETCHATNUM', service_chatNum)
      }
    }
  }
  for (let gkey in newGroups) {
    const group = newGroups[gkey]
    for (let gindex in group) {
      const guserId = group[gindex].from
      const createTime = parseTime(group[gindex].createTime, '{y}/{m}/{d} {h}:{i}:{s}')
      const gFidx = service_friends.findIndex(item => item.userId == guserId)
      if (gFidx != -1) {
        let gmsg
        if (isJSON(group[gindex].content)) gmsg = JSON.parse(group[gindex].content).title || group[gindex].content
        else gmsg = group[gindex].content
        const item = {
          userId: guserId,
          nick: group[gindex].nick || service_friends[gFidx].nick,
          avatar: group[gindex].avatar || service_friends[gFidx].avatar,
          msg: group[gindex].content,
          createTime,
          id: group[gindex].id
        }
        service_friends[gFidx].msgList.push(item)
        service_friends[gFidx].num += 1
        service_friends[gFidx].msg = gmsg
        service_friends[gFidx].createTime = createTime
        store.dispatch('SERVICE_WEBSOCKET_GETFRIENDS', service_friends)
        service_chatNum += 1
        store.dispatch('SERVICE_WEBSOCKET_SETCHATNUM', service_chatNum)
      }
    }
  }
}
// 处理历史消息
function friendHistoryMessage(msgObj) {
  const service_activeId = state.service_activeId
  const newFriends = { ...msgObj.friends, ...msgObj.groups }
  let service_friends = state.service_friends
  let service_provisionals = state.service_provisionals
  const fIdx = service_friends.findIndex(item => item.userId === service_activeId)
  const pIdx = service_provisionals.findIndex(item => item.userId === service_activeId)
  if (fIdx != -1) {
    const friend = service_friends[fIdx]
    const historyMsg = newFriends[service_activeId] || []
    if (!!historyMsg.length) {
      let arr = []
      historyMsg.map(item => {
        arr.push({ userId: item.from, nick: item.nick || friend.nick, avatar: item.avatar || friend.avatar, msg: item.content, createTime: parseTime(item.createTime, '{y}/{m}/{d} {h}:{i}:{s}'), id: item.id })
      })
      let msgList = uniqueJsonArrByField([...arr, ...service_friends[fIdx].msgList], ['id', 'createTime'])
      msgList.sort((a, b) => new Date(a.createTime || '2000-01-01').getTime() - new Date(b.createTime || '2000-01-01').getTime())
      service_friends[fIdx].msgList = msgList
      let msg
      if (isJSON(msgList[msgList.length - 1].msg)) msg = JSON.parse(msgList[msgList.length - 1].msg).title || msgList[msgList.length - 1].msg
      else msg = msgList[msgList.length - 1].msg
      service_friends[fIdx].msg = msg
      service_friends[fIdx].createTime = msgList[msgList.length - 1].createTime
      store.dispatch('SERVICE_WEBSOCKET_GETFRIENDS', service_friends)
      store.dispatch('SERVICE_WEBSOCKET_SETMSGLIST', msgList)
    }
  }
  if (pIdx != -1) {
    const historyMsg = newFriends[service_activeId] || []
    if (!!historyMsg.length) {
      let arr = []
      historyMsg.map(item => {
        arr.push({ userId: item.from, nick: item.nick, avatar: item.avatar, msg: item.content, createTime: parseTime(item.createTime, '{y}/{m}/{d} {h}:{i}:{s}'), id: item.id })
      })
      let msgList = uniqueJsonArrByField([...arr, ...service_provisionals[pIdx].msgList], ['id', 'createTime'])
      msgList.sort((a, b) => new Date(a.createTime || '2000-01-01').getTime() - new Date(b.createTime || '2000-01-01').getTime())
      service_provisionals[pIdx].msgList = msgList
      let msg
      if (isJSON(msgList[msgList.length - 1].msg)) msg = JSON.parse(msgList[msgList.length - 1].msg).title || msgList[msgList.length - 1].msg
      else msg = msgList[msgList.length - 1].msg
      service_provisionals[pIdx].msg = msg
      service_provisionals[pIdx].createTime = msgList[msgList.length - 1].createTime
      store.dispatch('SERVICE_WEBSOCKET_GETPROVISIONALS', service_provisionals)
      store.dispatch('SERVICE_WEBSOCKET_SETMSGLIST', msgList)
    }
  }
}
//添加好友响应状态处理;
function COMMAND_ADD_FRIEND_RESP() {
  const userId = state.service_userId || store.getters.service
  const onlineUserCmd = { cmd: 17, type: 2, userId }
  store.dispatch('SERVICE_WEBSOCKET_SEND', onlineUserCmd)
}
// 设置缓存
function setCache(key, value) {
  return localStorage.setItem(key, JSON.stringify(value))
}
// 获取缓存
function getCache(key) {
  return JSON.parse(localStorage.getItem(key))
}
// 删除缓存
function removeCache(key) {
  return localStorage.removeItem(key)
}
