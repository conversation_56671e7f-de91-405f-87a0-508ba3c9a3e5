import { getPurchaseApplyPre } from '@/api/kingdee'

const kingdee = {
  state: {
    info: {}
  },
  mutations: {
    SET_INFO: (state, data) => {
      state.info = data
    }
  },
  actions: {
    KingdeeInfo({ commit }) {
      return new Promise((resolve, reject) => {
        getPurchaseApplyPre().then(res => {
          commit('SET_INFO', res.data)
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    }
  }
}
export default kingdee
