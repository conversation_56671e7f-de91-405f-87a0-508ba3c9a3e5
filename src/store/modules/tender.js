const state = {
  tender: new Array()
}
const mutations = {
  SET_TENDER: (state, { key, value }) => {
    if (key !== null && key !== '') {
      state.tender.push({
        key: key,
        value: value
      })
    }
  },
  REMOVE_TENDER: (state, key) => {
    const index = state.tender.findIndex(item => item.key === key)
    if(index !== -1) state.tender.splice(index, 1)
  },
}
const actions = {
  setTender({ commit }, data) {
    commit('SET_TENDER', data)
  },
  removeTender({ commit }, key) {
    commit('REMOVE_TENDER', key)
  }
}
export default {
  namespaced: true,
  state,
  mutations,
  actions
}
