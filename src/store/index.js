import Vue from 'vue'
import Vuex from 'vuex'
import app from './modules/app'
import dict from './modules/dict'
import user from './modules/user'
import tagsView from './modules/tagsView'
import permission from './modules/permission'
import settings from './modules/settings'
import getters from './getters'
import tender from "./modules/tender";
import webscoket from './modules/webscoket'
import service from './modules/service'
import kingdee from './modules/kingdee'
Vue.use(Vuex)

const store = new Vuex.Store({
  modules: {
    app,
    dict,
    user,
    tagsView,
    permission,
    settings,
    tender,
    webscoket,
    service,
    kingdee
  },
  getters
})

export default store
