.crm-popover {
  padding: 0 !important;
  border-color: #2e73f3 !important;
  &.el-popper {
    .popper__arrow {
      width: 10px !important;
      height: 10px !important;
      transform: rotate(45deg) !important;
      background-color: #fff !important;
      margin: 0 !important;
      &:after {
        display: none;
      }
    }
  }
  &.el-popper[x-placement^='top'] {
    .popper__arrow {
      border-top: 0 !important;
      border-right: 1px solid #2e73f3 !important;
      border-bottom: 1px solid #2e73f3 !important;
      border-left: 0 !important;
    }
  }
  &.el-popper[x-placement^='bottom'] {
    .popper__arrow {
      border-top: 1px solid #2e73f3 !important;
      border-right: 0 !important;
      border-bottom: 0 !important;
      border-left: 1px solid #2e73f3 !important;
    }
  }
  &.el-popper[x-placement^='left'] {
    .popper__arrow {
      border-top: 1px solid #2e73f3 !important;
      border-right: 1px solid #2e73f3 !important;
      border-bottom: 0 !important;
      border-left: 0 !important;
    }
  }
  &.el-popper[x-placement^='right'] {
    .popper__arrow {
      border-top: 0 !important;
      border-right: 0 !important;
      border-bottom: 1px solid #2e73f3 !important;
      border-left: 1px solid #2e73f3 !important;
    }
  }
}
.crm-dropdown {
  border: 1px solid #2e73f3;
  padding-top: 0;
  padding-bottom: 0;
  &.el-popper {
    .popper__arrow {
      width: 10px !important;
      height: 10px !important;
      transform: rotate(45deg) !important;
      background-color: #fff !important;
      margin: 0 !important;
      &:after {
        display: none;
      }
    }
  }
  &.el-popper[x-placement^='top'] {
    .popper__arrow {
      border-top: 0 !important;
      border-right: 1px solid #2e73f3 !important;
      border-bottom: 1px solid #2e73f3 !important;
      border-left: 0 !important;
    }
  }
  &.el-popper[x-placement^='bottom'] {
    .popper__arrow {
      border-top: 1px solid #2e73f3 !important;
      border-right: 0 !important;
      border-bottom: 0 !important;
      border-left: 1px solid #2e73f3 !important;
    }
  }
  &.el-popper[x-placement^='left'] {
    .popper__arrow {
      border-top: 1px solid #2e73f3 !important;
      border-right: 1px solid #2e73f3 !important;
      border-bottom: 0 !important;
      border-left: 0 !important;
    }
  }
  &.el-popper[x-placement^='right'] {
    .popper__arrow {
      border-top: 0 !important;
      border-right: 0 !important;
      border-bottom: 1px solid #2e73f3 !important;
      border-left: 1px solid #2e73f3 !important;
    }
  }
}
