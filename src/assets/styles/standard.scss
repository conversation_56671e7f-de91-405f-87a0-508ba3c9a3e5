::v-deep .standard {
  &-box {
    width: 100%;
    background-color: #f9f9f9;
  }
  &-container {
    display: flex;
    flex-direction: column;
    margin: 0 auto;
    width: 1200px;
    padding-bottom: 30px;
  }
  &-header {
    flex-shrink: 0;
    position: relative;
    padding-top: 15px;
    height: 65px;
    margin-bottom: 20px;
    &-tabs {
      height: 50px;
      .el-tabs__header {
        margin-bottom: 0;
        .el-tabs__nav-wrap {
          &:after {
            height: 1px;
            background-color: #d9d9d9;
          }
          .el-tabs__active-bar {
            background-color: #333333;
          }
          .el-tabs__item {
            height: 50px;
            line-height: 50px;
            font-size: 16px;
            color: #666666;
            &.is-active {
              font-size: 18px;
              font-weight: 500;
              color: #333333;
            }
          }
        }
      }
    }
    &-location {
      line-height: 50px;
      border-bottom: 1px solid #d9d9d9;
      span {
        font-size: 14px;
        color: #999999;
        cursor: pointer;
        &::after {
          content: '>';
          margin: 0 5px;
        }
        &:last-child {
          color: #2e73f3;
          &::after {
            content: '';
          }
        }
        &:hover {
          color: #2e73f3;
        }
      }
    }
    &-search {
      background-color: transparent;
      position: absolute;
      right: 0;
      top: 20px;
      width: 345px;
      height: 36px;
      border-radius: 5px;
      border: 1px solid #212c57;
      padding: 1px 2px;
      display: flex;
      align-items: center;
      .el-input {
        background-color: transparent;
        border: none;
        .el-input__inner {
          background-color: transparent;
          border: none;
        }
      }
      .el-button {
        background-color: #212c57;
        color: #ffffff;
        font-size: 14px;
        padding: 6px 15px;
        .el-icon-search {
          font-size: 16px;
        }
      }
    }
    &-bg {
      .standard-header-tabs {
        .el-tabs__header {
          .el-tabs__nav-wrap {
            &:after {
              height: 1px;
              background-color: rgba(255, 255, 255, 0.22);
            }
            .el-tabs__active-bar {
              background-color: #ffffff;
            }
            .el-tabs__item {
              color: rgba(255, 255, 255, 0.6);
              &.is-active {
                color: #ffffff;
              }
            }
          }
        }
      }
      .standard-header-search {
        background-color: rgba(255, 255, 255, 0.2);
        border-color: #ffffff;
        .el-input {
          .el-input__inner {
            color: #ffffff;
          }
        }
        .el-button {
          background-color: #ffffff;
          color: #212c57;
        }
      }
    }
  }
  &-content {
    flex-shrink: 1;
    display: flex;
    justify-content: space-between;
    &-left {
      width: 260px;
      border-radius: 5px;
      .el-menu {
        border-radius: 5px;
        overflow: hidden;
        border-right: 0;
        background-color: #f8f9fb;
        box-shadow: 0 0 26px 0 rgba(0, 0, 0, 0.15);
        .el-menu {
          border-radius: 0;
        }
        .el-submenu {
          .el-submenu__title {
            font-size: 16px;
            height: 54px;
            line-height: 54px;
            position: relative;
            span {
              padding-left: 40px;
            }
            .el-submenu__icon-arrow {
              font-size: 16px;
              color: #999999;
            }
          }
          .el-menu {
            .el-menu-item-group {
              background-color: #e8ecf4;
              .el-menu-item-group__title {
                display: none;
              }
              .el-menu-item {
                font-size: 14px;
                line-height: 45px;
                height: 45px;
                span {
                  padding-left: 40px;
                }
                &:hover,
                &.is-active {
                  background-color: transparent;
                }
              }
            }
          }
          &:hover,
          &.is-active {
            .el-submenu__title {
              background-color: #2e73f3;
              color: #ffffff;
              .el-submenu__icon-arrow {
                color: #ffffff;
              }
            }
          }
          &.is-active {
            .el-submenu__title {
              &::after {
                content: '';
                position: absolute;
                bottom: 0;
                left: 68px;
                border-bottom: 8px solid #e8ecf3;
                border-left: 8px solid transparent;
                border-right: 8px solid transparent;
                width: 0;
                height: 0;
              }
            }
          }
        }
      }
    }
    &-right {
      width: 920px;
      display: flex;
      padding-top: 42px;
      flex-wrap: wrap;
      background-color: rgba(255, 255, 255, 0.2);
      border-radius: 5px;
      position: relative;
      &::before {
        content: '';
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        left: -10px;
        border-top: 10px solid transparent;
        border-right: 10px solid rgba(255, 255, 255, 0.2);
        border-bottom: 10px solid transparent;
        width: 0;
        height: 0;
      }
      &-item {
        width: 115px;
        height: 110px;
        border-radius: 5px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        text-align: center;
        color: #ffffff;
        cursor: pointer;
        transition: background-color 0.3s;
        &:hover,
        &.active {
          background-color: #ffffff;
          color: #333333;
        }
        b {
          font-family: PangMenZhengDao, Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
          font-size: 16px;
          line-height: 25px;
        }
        span {
          padding: 0 10px;
          font-size: 18px;
          line-height: 28px;
        }
      }
    }
    &-left-height {
      height: 635px;
    }
  }
  &-bg {
    background-color: #212c57;
    .standard-container {
      background-image: url('~@/assets/images/standard-bg.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }
  }
  &-list {
    width: 920px;
    display: flex;
    flex-direction: column;
    &-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px;
      border: 1px solid #e0e0e0;
      border-radius: 10px;
      padding: 10px 15px 10px;
      &-img {
        width: 135px;
        height: 135px;
        border: 1px solid #e0e0e0;
        border-radius: 5px;
        flex: 0 0 135px;
        margin-right: 10px;
        cursor: pointer;
        overflow: hidden;
        img {
          width: 100%;
          height: 100%;
          transition: all 0.3s;
        }
        &:hover {
          border-color: #2e73f3;
          img {
            transform: scale(1.2);
          }
        }
      }
      &-svg {
        width: 135px;
        height: 135px;
        border: 1px solid #e0e0e0;
        border-radius: 5px;
        flex: 0 0 135px;
        margin-right: 20px;
        overflow: hidden;
        cursor: pointer;
        svg {
          width: 100% !important;
          height: 100% !important;
          transition: all 0.3s;
        }
        &:hover {
          border-color: #2e73f3;
          svg {
            transform: scale(1.2);
          }
        }
      }
      .flexCloumn {
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        flex: 1;
      }
      &-title {
        display: flex;
        align-items: center;
        height: 32px;
        line-height: 32px;
        b {
          font-size: 20px;
          font-weight: 500;
          color: #333333;
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          cursor: pointer;
          &:hover {
            color: #2e73f3;
          }
        }
        .top {
          display: flex;
          align-items: center;
          margin-right: 15px;
          margin-left: 15px;
          color: #999999;
          cursor: pointer;
          span {
            font-size: 12px;
            margin-left: 5px;
          }
          &:hover {
            color: #2e73f3;
            .top-icon {
              background-image: url('~@/assets/images/topYes.png');
            }
          }
        }
        .top-icon {
          width: 20px;
          height: 20px;
          background-image: url('~@/assets/images/topNo.png');
          background-size: 100% 100%;
          background-repeat: no-repeat;
          &.active {
            background-image: url('~@/assets/images/topYes.png');
            margin-right: 15px;
            margin-left: 15px;
          }
        }
        .el-icon-star-on {
          color: #2e73f3;
          font-size: 24px;
        }
        .star {
          display: flex;
          align-items: center;
          height: 32px;
          padding: 0 10px;
          border: 1px solid #c9cbcd;
          border-radius: 5px;
          color: #999999;
          cursor: pointer;
          .el-icon-star-off {
            font-size: 22px;
          }
          span {
            font-size: 12px;
            margin-left: 5px;
          }
          &:hover {
            color: #2e73f3;
            border-color: #2e73f3;
          }
        }
      }
      &-desc {
        display: flex;
        align-items: center;
        margin: 10px 0;
        span {
          font-size: 12px;
          color: #666666;
        }
        b {
          font-size: 14px;
          font-weight: 500;
          color: #333333;
          margin-left: 10px;
        }
      }
      &-btn {
        display: flex;
        justify-content: space-between;
        align-items: center;
        &-left {
          display: flex;
          align-items: center;
          span {
            display: inline-block;
            height: 38px;
            line-height: 38px;
            background-color: #f1f1f3;
            padding: 0 13px;
            margin-right: 10px;
            border-radius: 5px;
            font-size: 12px;
            color: #2e73f3;
            cursor: pointer;
            &:hover {
              background-color: #2e73f3;
              color: #ffffff;
            }
          }
        }
        &-right {
          display: flex;
          align-items: center;
          height: 38px;
          line-height: 38px;
          border: 1px solid #2e73f3;
          color: #2e73f3;
          border-radius: 30px;
          padding: 0 15px;
          cursor: pointer;
          span {
            font-size: 16px;
            margin-right: 6px;
          }
          i {
            font-size: 22px;
          }
          &:hover {
            color: #ffffff;
            background-color: #2e73f3;
          }
        }
      }
      &:hover {
        border-color: #2e73f3;
        box-shadow: 0 2px 19px 0 rgba(0, 0, 0, 0.1);
      }
    }
  }
  &-location {
    display: flex;
    align-items: center;
    position: relative;
    margin-bottom: 20px;
    &-box {
      display: flex;
      align-items: center;
      background-color: #dedee0;
      padding: 0 10px 0 20px;
      height: 52px;
      border-radius: 5px;
      margin-right: 25px;
      &::before {
        content: '';
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        left: 0;
        left: -10px;
        border-top: 10px solid transparent;
        border-right: 10px solid #dedee0;
        border-bottom: 10px solid transparent;
        width: 0;
        height: 0;
      }
      &-title {
        font-size: 12px;
        color: #666666;
      }
      &-item {
        margin-left: 10px;
        display: flex;
        align-items: center;
        padding: 0 10px 0 20px;
        font-size: 14px;
        line-height: 40px;
        color: #333333;
        cursor: pointer;
        border: 1px solid transparent;
        border-bottom: 0;
        z-index: 2;
        span {
          margin-right: 15px;
        }
        i {
          font-size: 20px;
        }
        &.active {
          background-color: #ffffff;
          color: #2e73f3;
          border-color: #2e73f3;
          border-radius: 5px 5px 0 0;
        }
      }
      &-list {
        z-index: 1;
        position: absolute;
        top: 46px;
        left: 0;
        width: 920px;
        padding: 20px 19px;
        background-color: #ffffff;
        border: 1px solid #2e73f3;
        border-radius: 5px;
        box-shadow: 0 0 26px 0 rgba(0, 0, 0, 0.15);
        display: flex;
        flex-wrap: wrap;
        .list-item {
          width: 110px;
          height: 68px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          text-align: center;
          cursor: pointer;
          margin: 5px 0;
          b {
            font-family: PangMenZhengDao, Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
            font-size: 16px;
            line-height: 25px;
          }
          span {
            font-size: 18px;
            line-height: 20px;
          }
          &:hover,
          &.active {
            border-radius: 5px;
            border: 1px solid #2e73f3;
            background-color: #ecf2ff;
            b {
              color: #2e73f3;
            }
            span {
              color: #2e73f3;
            }
          }
        }
      }
    }
    &-text {
      display: flex;
      align-items: center;
      font-size: 14px;
      color: #333333;
      .primary {
        color: #2e73f3;
      }
    }
  }
  &-detail {
    &-title {
      font-size: 32px;
      line-height: 40px;
      color: #333333;
      font-weight: 500;
      margin-bottom: 10px;
    }
    &-desc {
      display: flex;
      align-items: center;
      font-size: 16px;
      span {
        color: #999999;
      }
      b {
        color: #333333;
        font-weight: 500;
      }
    }
    &-tabs {
      display: flex;
      align-items: flex-end;
      margin-top: 20px;
      span.item {
        display: inline-block;
        height: 42px;
        line-height: 42px;
        background: linear-gradient(to bottom, #f1f1f3, #e7e7e9);
        padding: 0 50px;
        font-size: 14px;
        color: #666666;
        cursor: pointer;
        transition: all 0.3s;
        &:hover,
        &.active {
          height: 48px;
          line-height: 48px;
          background: #ffffff;
          font-size: 16px;
          color: #333333;
          border-radius: 5px 5px 0 0;
          box-shadow: 0 0 26px 0 rgba(0, 0, 0, 0.12);
          z-index: 2;
        }
        &:first-child {
          border-top-left-radius: 5px;
        }
        &:last-child {
          border-top-right-radius: 5px;
        }
      }
    }
    &-content {
      padding: 20px;
      background-color: #ffffff;
      border-radius: 0 5px 5px 5px;
      box-shadow: 0 0 26px 0 rgba(0, 0, 0, 0.12);
      min-height: 500px;
      z-index: 2;
    }
  }
  &-drawing {
    padding: 15px;
    border: 1px solid #ededed;
    border-radius: 5px;
    &-title {
      display: flex;
      align-items: center;
      &-select {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 52px;
        border: 1px solid #cbd6e2;
        border-radius: 5px;
        padding: 0 10px 0 20px;
        span.tip {
          font-size: 12px;
          color: #999999;
        }
        .el-select {
          width: 130px;
          .el-input__inner {
            border: 0 !important;
          }
        }
      }
      &-tip {
        margin-left: 30px;
        font-size: 12px;
        color: #999999;
      }
    }
    &-content {
      width: 100%;
      margin-top: 20px;
      svg {
        width: 100%;
        height: 700px;
      }
    }
  }
  // 规格参数、技术条件和引入标准、引用标准
  &-html {
    .mode-head {
      display: none;
      // display: inline-block;
      margin-bottom: 5px;
      .head-text {
        font-size: 16px;
        font-weight: 700;
        color: #2a3f60;
        line-height: 30px;
        position: relative;
        z-index: 2;
      }
      .bottom-line {
        width: auto;
        height: 6px;
        background: linear-gradient(90deg, #ffd300, #fff);
        position: relative;
        top: -10px;
        z-index: 1;
      }
    }
    .model-box {
      display: flex;
      align-items: center;
      border-bottom: 1px solid #d9d9d9;
      margin-bottom: 20px;
      .item {
        font-size: 14px;
        color: #666666;
        margin-left: 10px;
        cursor: pointer;
        padding: 0 30px;
        line-height: 38px;
        border-bottom: 2px solid transparent;
        margin-bottom: -1px;
        &.spec-active {
          color: #2e73f3 !important;
          border-bottom-color: #2e73f3;
        }
      }
    }
    .f-overflow {
      &::-webkit-scrollbar-track-piece {
        background-color: #f8f8f8;
      }
      &::-webkit-scrollbar {
        width: 8px;
        height: 8px;
      }
      &::-webkit-scrollbar-thumb {
        background-color: #ddd;
        background-clip: padding-box;
        min-height: 28px;
        border-radius: 5px;
      }
      &::-webkit-scrollbar-thumb:hover {
        background-color: #bbb;
      }
    }
    .standard-table-list {
      position: relative;
      display: flex;
      margin-bottom: 20px;
      &-left {
        width: 120px;
        position: relative;
        z-index: 5;
        .left-box {
          border-bottom: 1px solid #bec4d2;
          background: #f1f1f3;
          border-right: 1px solid #bec4d2;
          .item-name {
            text-align: center;
            border-left: 1px solid #bec4d2;
            border-top: 1px solid #bec4d2;
            line-height: 20px;
            padding: 15px 0;
            font-size: 14px;
            color: #333;
            font-weight: 500;
            box-sizing: border-box;
            white-space: nowrap;
            overflow-x: auto;
          }
          .unit-name {
            background-color: #f8f9fb;
            font-weight: 400;
          }
        }
      }
      &-right {
        position: absolute;
        left: 120px;
        top: 0;
        right: 0;
        bottom: 0;
        overflow-x: auto;
        height: -moz-fit-content;
        height: fit-content;
        .standard-table-list-right-view {
          white-space: nowrap;
          display: flex;
          .standard-table-list-li {
            display: inline-block;
            width: 80px;
            .right-box {
              border-bottom: 1px solid #bec4d2;
              .item-name {
                text-align: center;
                border-right: 1px solid #bec4d2;
                border-top: 1px solid #bec4d2;
                font-size: 14px;
                line-height: 20px;
                font-weight: 400;
                color: #333;
                box-sizing: border-box;
                list-style: none;
                padding: 15px 12px;
              }
              .item-title {
                background: #f8f9fb;
                cursor: pointer;
              }
              &.backBox {
                background-color: #ecf2fe;
              }
            }
            &:hover,
            &__active {
              background-color: #ecf2fe;
              font-weight: 700;
            }
          }
        }
      }
    }
    .condition-table {
      font-size: 14px;
      color: #333;
      font-weight: 500;
      background-color: #fff;
      border: 1px solid #bec4d2;
      border-radius: 5px;
      overflow: hidden;
      .layui-table {
        background-color: #fff;
        width: 100%;
        border-collapse: collapse;
        thead tr td {
          border: 0;
        }
        tr {
          .subname {
            background-color: #f8f9fb;
            color: #666;
            font-size: 12px;
            line-height: 20px;
            font-weight: 400;
            padding: 16px 0;
            border-bottom: 1px solid #bec4d2;
            .tab_div {
              min-width: 100px;
            }
          }
          .content {
            background-color: #fff;
            font-size: 14px;
            line-height: 2;
            color: #333;
          }
          td {
            text-align: center;
            background-color: #fff;
            border: 1px solid #bec4d2;
            padding: 5px 0;
            li {
              list-style: none;
              font-size: 14px;
              line-height: 2;
              color: #333;
            }
            &:first-child {
              border-left: 0;
            }
            &:last-child {
              border-right: 0;
            }
          }
          &:last-child {
            td {
              border-bottom: 0;
            }
          }
        }
        tbody {
          tr {
            td.subname {
              background-color: #f0f3f9;
            }
            td[rowspan='2'],
            td[colspan='2'],
            td[rowspan='1'][colspan='2'] {
              &.subname {
                background-color: #e3e7f0 !important;
              }
            }
            &:nth-child(2n) {
              td {
                background-color: #f1f1f3;
                &.subname {
                  background-color: #f0f3f9;
                }
              }
            }
          }
        }
      }
    }
    .ant-table-wrapper {
      zoom: 1;
      &::after,
      &::before {
        content: '';
        display: table;
      }
      &::after {
        clear: both;
      }
      .ant-spin-nested-loading {
        position: relative;
        .ant-spin-container {
          position: relative;
          transition: opacity 0.3s;
          .ant-table {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
            color: rgba(0, 0, 0, 0.65);
            font-size: 14px;
            font-variant: tabular-nums;
            line-height: 1.5;
            list-style: none;
            font-feature-settings: 'tnum';
            position: relative;
            clear: both;
            table {
              width: 100%;
              text-align: left;
              border-radius: 4px 4px 0 0;
              border-collapse: separate;
              border-spacing: 0;
              border: 1px solid #bec4d2;
              border-radius: 5px;
              overflow: hidden;
              .ant-table-thead {
                border-radius: 5px 5px 0 0;
                tr {
                  th {
                    font-weight: 500;
                    text-align: left;
                    background: #fafafa;
                    border-bottom: 1px solid #bec4d2;
                    transition: background 0.3s ease;
                    padding: 16px;
                    overflow-wrap: break-word;
                    color: #666666;
                    background-color: #f8f9fb;
                    .ant-table-header-column {
                      display: inline-block;
                      max-width: 100%;
                      vertical-align: top;
                      .ant-table-column-sorter {
                        display: table-cell;
                        vertical-align: middle;
                      }
                    }
                    &.ant-table-row-cell-last {
                      padding-left: 30px;
                    }
                  }
                }
              }
              .ant-table-tbody {
                border-radius: 0 0 5px 5px;
                tr.ant-table-row {
                  td {
                    border-bottom: 1px solid #bec4d2;
                    border-right: 1px solid #bec4d2;
                    transition: background 0.3s;
                    padding: 16px;
                    overflow-wrap: break-word;
                    color: #333333;
                    .standard-disabled {
                      padding-left: 14px;
                      color: #333333;
                    }
                    &:first-child {
                      padding: 16px 0;
                      width: 60px;
                      text-align: center;
                    }
                    &:last-child {
                      border-right: none;
                    }
                  }
                  &:nth-child(2n) {
                    td {
                      background-color: #f1f1f3;
                    }
                  }
                  &:last-child {
                    td {
                      border-bottom: none;
                    }
                  }
                  &:hover td {
                    background-color: #ecf2fe;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
