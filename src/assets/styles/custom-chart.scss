@import '~@/assets/styles/custom-new.scss';
.data {
  background-color: $white;
  border-radius: 5px;
  height: 415px;
  box-shadow: 0 0 26px 0 rgba(0, 0, 0, 0.05);
  padding: 20px 0 15px;
  margin-bottom: 15px;
  &.height-520 {
    height: 520px;
  }
  &-title {
    width: 100%;
    height: 20px;
    padding: 0 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    span {
      font-size: 14px;
      color: $font;
    }
    i {
      font-size: 18px;
      color: $disabled;
      cursor: pointer;
      &:hover {
        color: $blue;
      }
    }
  }
  &-info {
    width: 100%;
    padding: 0 20px;
    margin-top: 20px;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    &-item {
      width: calc(50% - 5px);
      height: 90px;
      background-color: #f7f7f9;
      border-radius: 10px;
      display: inline-flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      &.inline {
        flex-direction: row;
        justify-content: flex-start;
        padding-left: 15px;
        .data-info-item-title {
          margin-right: 15px;
        }
      }
      &.position-relative {
        position: relative;
      }
      &-title {
        font-size: 12px;
        color: $info;
        line-height: 20px;
      }
      &-num {
        font-size: 24px;
        color: $font;
        line-height: 30px;
        &.red {
          color: $red;
        }
        &.green {
          color: $success;
        }
        &.orange {
          color: $orange;
        }
      }
      &-tab {
        width: 100%;
        display: inline-flex;
        flex-direction: row;
        align-items: center;
        border-radius: 10px 10px 0 0;
        overflow: hidden;
        position: absolute;
        top: 0;
        left: 0;
        font-size: 12px;
        color: $disabled;
        background-color: #e8ecf0;
        span {
          display: inline-block;
          line-height: 20px;
          cursor: pointer;
          padding: 0 10px;
          &.active {
            color: $font;
            background-color: #f7f7f9;
          }
        }
      }
    }
  }
  &-chart {
    height: calc(100% - 140px);
    position: relative;
    width: calc(100% - 20px);
    margin-left: 20px;
    &-info {
      position: relative;
      z-index: 1;
    }
    &-item {
      width: 100%;
      height: 61px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      &:first-child {
        margin-top: 20px;
      }
      .title {
        display: inline-flex;
        flex-direction: column;
        b {
          font-size: 14px;
          font-weight: 500;
          color: $font;
        }
        span {
          font-size: 12px;
          color: $info;
        }
      }
      .rate {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
        margin-right: 20px;
        i {
          font-size: 18px;
          margin-right: 5px;
        }
        &.red {
          color: $red;
        }
        &.green {
          color: $success;
        }
      }
      .desc {
        font-size: 14px;
        color: $info;
      }
    }
  }
  &-select {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 2;
    height: 36px;
    width: 150px;
    &.relative {
      position: relative;
      margin-bottom: 5px;
    }
    ::v-deep {
      .el-input__inner {
        height: 36px;
        line-height: 36px;
        border-radius: 5px;
        border-color: #cbd6e2;
        color: $font;
        padding-right: 0;
      }
      .el-input__icon {
        line-height: 36px;
      }
      .el-select .el-input .el-select__caret {
        font-size: 24px;
        display: inline-flex;
        align-items: center;
      }
      .el-icon-arrow-up:before {
        content: '\e78f';
      }
    }
  }
  &-tab {
    width: calc(100% - 20px);
    margin-right: 20px;
    border: 1px solid #cbd6e2;
    border-radius: 10px;
    margin-bottom: 15px;
    &-title {
      display: flex;
      align-items: center;
      width: 100%;
      height: 28px;
      border-bottom: 1px solid #cbd6e2;
      background-color: #f7f7f9;
      border-radius: 10px 10px 0 0;
      span {
        display: inline-flex;
        align-items: center;
        font-size: 12px;
        color: $disabled;
        padding: 0 15px;
        cursor: pointer;
        &.active {
          height: 36px;
          border: 1px solid #cbd6e2;
          border-bottom: 0;
          border-radius: 8px 8px 0 0;
          background-color: $white;
          color: $font;
          margin-top: -8px;
        }
        &:first-child {
          margin-left: -1px;
        }
      }
    }
    &-info {
      display: flex;
      align-items: center;
      flex-direction: row;
      padding: 18px 0;
      .data-info-item {
        height: 66px;
        background-color: transparent;
        border-radius: 0;
        &:last-child {
          border-left: 1px solid #e8e8f0;
        }
        &.inline {
          justify-content: center;
        }
      }
    }
  }
}
.moreBox {
  padding: 20px;
  background-color: $white;
  box-shadow: 0 0 26px 0 rgba(0, 0, 0, 0.05);
  border-radius: 5px;
  .el-page-header {
    padding: 0;
    margin-bottom: 20px;
  }
  &-title {
    font-size: 20px;
    line-height: 20px;
    padding-bottom: 20px;
    color: $font;
  }
  &-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: calc(100% - 20px);
    margin-right: 20px;
    height: 236px;
    padding: 32px 0;
    background-color: #f7f7f9;
    border-radius: 10px;
    .moreBox-info-item + .moreBox-info-item {
      border-left: 1px solid #e8e8f0;
    }
    &-item {
      width: 50%;
      height: 100%;
      display: inline-flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      .item-title {
        font-size: 12px;
        line-height: 20px;
        color: $info;
      }
      .item-num {
        font-size: 36px;
        line-height: 42px;
        color: $font;
        -webkit-text-stroke: 1px #333333;
        text-stroke: 1px #333333;
        -webkit-background-clip: text;
        &.orange {
          color: $orange;
          -webkit-text-stroke: 1px $orange;
          text-stroke: 1px $orange;
          -webkit-background-clip: text;
        }
      }
      .item-bg {
        width: 100%;
        height: 35px;
        background-position: center center;
        background-repeat: no-repeat;
        &.gray {
          background-image: url('~@/assets/images/item-bg-gray.png');
        }
      }
      .item-contrast {
        display: flex;
        align-items: center;
        justify-content: center;
        .c666 {
          color: $info;
          font-size: 12px;
          i {
            font-size: 16px;
          }
        }
        .orange {
          color: $orange;
          font-size: 20px;
        }
        .green {
          color: $success;
          font-size: 20px;
          i {
            margin: 0 5px;
          }
        }
        .red {
          color: $red;
          font-size: 20px;
          i {
            margin: 0 5px;
          }
        }
      }
    }
  }
  &-list {
    border: 1px solid #cbd6e2;
    border-radius: 10px;
    overflow: hidden;
    &.isTop {
      border-bottom-left-radius: 0;
      border-bottom-right-radius: 0;
    }
    &.isBottom {
      border-top-left-radius: 0;
      border-top-right-radius: 0;
      border-top: 0;
    }
    &-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      height: 70px;
      padding: 0 20px;
      background-color: #f2f4f6;
      span.title {
        font-size: 20px;
        color: $info;
      }
    }
    &-chart {
      .chart-chart {
        width: 250px;
      }
      .chart-info {
        width: calc(100% - 250px);
        display: flex;
        flex-wrap: wrap;
        flex-direction: row;
        .chart-item {
          width: 50%;
          display: flex;
          flex-direction: row;
          flex-wrap: wrap;
          border-bottom: 1px solid #e8e8f0;
          &.flex-column {
            flex-direction: column;
          }
          &-title {
            display: inline-flex;
            align-items: center;
            font-size: 14px;
            font-weight: 500;
            line-height: 20px;
            color: $font;
            margin-bottom: 15px;
            margin-top: 20px;
            &:before {
              display: inline-block;
              content: '';
              width: 20px;
              height: 20px;
              border-radius: 50%;
              margin-right: 20px;
            }
            &.color-b3ceff {
              &:before {
                background-color: #b3ceff;
              }
            }
            &.color-ffe09c {
              &:before {
                background-color: #ffe09c;
              }
            }
            &.color-b4ceff {
              &:before {
                background-color: #b4ceff;
              }
            }
            &.color-ffe19b {
              &:before {
                background-color: #ffe19b;
              }
            }
            &.color-ffb2c8 {
              &:before {
                background-color: #ffb2c8;
              }
            }
          }
          &-info {
            display: flex;
            align-items: center;
            width: 100%;
            height: 20px;
            padding-left: 40px;
            margin-bottom: 20px;
            .title {
              font-size: 14px;
              color: $info;
            }
            i {
              font-size: 16px;
              margin-left: 5px;
            }
            .mum {
              font-size: 16px;
              color: $info;
              margin-left: 5px;
            }
            &.red {
              i {
                color: $red;
              }
              .num {
                color: $red;
              }
            }
            &.green {
              i {
                color: $success;
              }
              .num {
                color: $success;
              }
            }
            &.orange {
              i {
                color: $orange;
              }
              .num {
                color: $orange;
              }
            }
          }
          &.border-bottom-0 {
            border-bottom: 0;
          }
        }
      }
    }
    &-top {
      padding: 20px;
      .top-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        height: 85px;
        border-bottom: 1px solid #e8e8f0;
        &:last-child {
          border-bottom: 0;
        }
        &-title {
          font-size: 16px;
          font-weight: 500;
          color: $font;
        }
        &-desc {
          font-size: 14px;
          color: $info;
        }
        &-btn {
          font-style: normal;
          padding: 5px 10px;
          background-color: #2e73f3;
          color: #ffffff;
          border-radius: 5px;
          margin-left: 10px;
          cursor: pointer;
          &:hover {
            opacity: 0.8;
          }
        }
      }
    }
  }
}
