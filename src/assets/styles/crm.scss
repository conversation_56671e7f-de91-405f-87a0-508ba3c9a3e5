.flex1 {
  flex: 1;
  overflow: hidden;
}
.border-zero {
  border: 0 !important;
}
.crm-descriptions {
  width: 100%;
  background-color: #fff;
  border: 1px solid #ebedf3;
  display: flex;
  flex-wrap: wrap;
  &-item {
    display: flex;
    align-items: center;
    width: 50%;
    border-bottom: 1px solid #ebedf3;
    padding: 10px 18px;
    line-height: 22px;
    &.no-border {
      border-bottom: none;
    }
    &.lang {
      width: 100%;
    }
    &.hasbg {
      background-color: #f8faff;
    }
    &-label {
      width: 85px;
      text-align: right;
      font-size: 12px;
      color: #666666;
    }
    &-content {
      flex: 1;
      font-weight: 500;
      font-size: 14px;
      color: #333333;
      padding-left: 30px;
      .primary {
        color: #2e73f3;
      }
      .price {
        color: #f02323;
      }
      .discount {
        font-weight: 400;
        font-size: 12px;
        color: #666666;
        padding-left: 10px;
      }
      .product-cost {
        font-size: 12px;
        color: #666;
        margin-right: 5px;
        &:before {
          content: '=';
          display: inline-block;
          color: #2e73f3;
          margin: 0 5px;
        }
      }
      .product-plus {
        font-size: 12px;
        color: #666;
        margin-right: 5px;
        &:before {
          content: '+';
          display: inline-block;
          color: #2e73f3;
          margin: 0 5px;
        }
      }
      .pointer {
        transition: all 0.3s;
        &:hover {
          color: #2e73f3;
        }
      }
    }
  }
  &.card {
    gap: 20px;
    border: 0;
    .crm-descriptions-item {
      width: calc((100% - 80px) / 5);
      border: 1px solid #ebedf3;
      background-color: #f8faff;
      border-radius: 5px;
      flex-direction: column;
      align-items: flex-start;
      &-label {
        width: 100%;
        text-align: left;
      }
      &-content {
        padding-left: 0;
      }
    }
  }
}
.crm-table-title {
  font-weight: 500;
  font-size: 16px;
  line-height: 20px;
  margin: 15px 0;
}
.crm-table-total {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  line-height: 28px;
  background-color: #eff5ff;
  border: 1px solid #2e73f3;
  border-radius: 5px;
  padding: 10px 30px;
  font-size: 14px;
  color: #666;
  margin-top: 10px;
  .primary {
    font-weight: 500;
    font-size: 18px;
    color: #2e73f3;
  }
  .orange {
    font-weight: 500;
    font-size: 18px;
    color: #f02323;
  }
  .yellow {
    font-weight: 500;
    font-size: 14px;
    color: #f35d09;
  }
  .default {
    font-weight: 500;
    font-size: 14px;
    color: #333333;
  }
  &-desc {
    border: 0;
    background-color: transparent;
    padding: 10px 0;
    margin-top: 0;
    font-size: 12px;
    .crm-table-total-item + .crm-table-total-item {
      margin-left: 30px;
    }
  }
}
.crm-deliver-dropdown {
  width: 500px;
  &-title {
    background-color: #f2f3f9;
    border-bottom: 1px solid #cbd6e2;
    font-size: 14px;
    color: #666;
    text-align: center;
    line-height: 40px;
    border-radius: 5px 5px 0 0;
    position: relative;
    z-index: 2;
  }
  &-form {
    padding: 20px 20px 15px;
  }
  &-content {
    background-color: #f8f9fb;
    &-title {
      font-size: 12px;
      color: #666;
      line-height: 20px;
      border-bottom: 1px solid #cbd6e2;
      padding: 15px 15px 6px;
    }
    &-list {
      padding: 5px 15px 0;
    }
    &-item {
      display: flex;
      align-items: flex-start;
      padding: 15px 0;
      border-bottom: 1px solid #cbd6e2;
      .avatar {
        flex-shrink: 0;
      }
      .content {
        flex: 1;
        padding-left: 10px;
        line-height: 30px;
        .info {
          font-weight: 500;
          font-size: 14px;
          color: #333333;
        }
        .desc {
          font-size: 12px;
          color: #666;
          padding: 0 10px;
        }
        .primary {
          font-size: 12px;
          color: #2e73f3;
        }
      }
      &:last-child {
        border-bottom: none;
      }
    }
  }
  &-btns {
    border-radius: 0 0 5px 5px;
    display: flex;
    justify-content: flex-end;
    padding: 15px 20px;
    border-top: 1px solid #cbd6e2;
    background-color: #f5f5f5;
  }
}
.crm-tag {
  border-color: #bfd6ff;
  position: relative;
  padding-right: 15px;
  margin-right: 5px;
  &.border-none {
    border-color: transparent;
  }
  ::v-deep {
    .el-icon-close {
      position: absolute;
      top: -5px;
      right: -5px;
      background-color: rgba(0, 0, 0, 0.8);
      color: #fff;
      &:hover {
        background-color: #2e73f3;
      }
    }
  }
}
.create-tabs {
  width: 100%;
  height: 46px;
  background-color: #f2f3f9;
  border-bottom: 1px solid #cbd6e2;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-right: 20px;
  margin-bottom: 20px;
  &-info {
    display: flex;
    align-items: center;
    position: relative;
  }
  &-item {
    height: 46px;
    line-height: 46px;
    width: 135px;
    text-align: center;
    font-size: 14px;
    color: #666;
    cursor: pointer;
    transition: all 0.3s;
    &.active {
      height: 52px;
      line-height: 52px;
      border: 1px solid #cbd6e2;
      border-radius: 5px 5px 0 0;
      background-color: #fff;
      border-bottom-color: #fff;
      margin-top: -5px;
      font-size: 16px;
      color: #2e73f3;
    }
  }
}
.create-config {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 14px;
  line-height: 40px;
  color: #666;
  transition: all 0.3s;
  i {
    font-size: 20px;
    color: #2e73f3;
    margin-right: 5px;
  }
  &:hover {
    color: #2e73f3;
  }
  & + .create-config {
    margin-left: 20px;
  }
}
.create-user {
  &-config {
    &-title {
      font-size: 12px;
      color: #999;
      line-height: 40px;
      text-align: center;
      background-color: #f2f3f9;
      border-radius: 5px 5px 0 0;
      position: relative;
      z-index: 2;
      .tips {
        font-size: 12px;
        color: #999;
        position: absolute;
        left: 20px;
        top: 50%;
        transform: translateY(-50%);
      }
    }
    &-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 10px 20px;
      font-size: 14px;
      line-height: 20px;
      color: #666;
    }
    &-sort {
      display: flex;
      align-items: center;
      gap: 20px;
      margin: 0 10px;
      padding: 15px 10px;
      font-size: 12px;
      line-height: 20px;
      color: #999;
      border-top: 1px solid #e1e2e2;
    }
  }
  &-form {
    &-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 32px;
      border-bottom: 1px solid #e1e2e2;
      font-weight: 500;
      font-size: 14px;
      color: #666;
      margin-bottom: 20px;
    }
    &-plus {
      display: flex;
      align-items: center;
      padding: 0 20px;
      margin-bottom: 5px;
      span {
        font-size: 14px;
        color: #666;
        margin-right: 50px;
      }
    }
    &-item {
      display: flex;
      align-items: center;
      border: 1px solid #dcdfe6;
      border-radius: 5px;
      ::v-deep {
        .el-select {
          flex: 1;
        }
        .el-input__inner {
          border-color: transparent;
        }
        .create-config {
          padding: 0 20px;
          position: relative;
          &:before {
            content: '';
            display: inline-block;
            width: 1px;
            height: 28px;
            background-color: #d6d6d6;
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            left: 0;
          }
        }
      }
    }
    &-suffix {
      padding: 0 20px;
      position: relative;
      &:before {
        content: '';
        display: inline-block;
        width: 1px;
        height: 28px;
        background-color: #d6d6d6;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        left: 0;
      }
    }
    &-table {
      background-color: #f0f3f9;
      padding: 20px 20px 0;
      margin-bottom: 20px;
      position: relative;
      &-title {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
        font-size: 12px;
        color: #999;
      }
      &.triangle {
        &::after {
          display: inline-block;
          content: '';
          position: absolute;
          top: -8px;
          left: 100px;
          width: 0;
          height: 0;
          border-left: 8px solid transparent;
          border-right: 8px solid transparent;
          border-bottom: 8px solid #f0f3f9;
        }
        &.triangle-55 {
          &::after {
            left: 55px;
          }
        }
        &.triangle-150 {
          &::after {
            left: 150px;
          }
        }
      }
    }
    &-custom {
      border: 1px solid #cbd6e2;
      border-radius: 5px;
      overflow: hidden;
      .title {
        padding: 8px 20px;
        border-bottom: 1px solid #cbd6e2;
        background-color: #f8f9fb;
        font-size: 12px;
        color: #999;
        line-height: 20px;
      }
      .content {
        padding: 20px 20px 10px;
        background-color: #fff;
        .crm-tag {
          margin-right: 10px;
          margin-bottom: 10px;
        }
      }
    }
  }
}
.table-text-btn {
  font-size: 12px;
  line-height: 20px;
  color: #999;
  padding: 6px 10px;
  border-radius: 5px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  &:hover {
    color: #2e74f3;
  }
  &.active {
    background-color: #e1ecff;
    color: #2e74f3;
  }
  &.danger {
    &:hover {
      color: #f02323;
    }
  }
}
.mt-10 {
  margin-top: 10px;
}
.ml-10 {
  margin-left: 10px;
}
.mt-15 {
  margin-top: 15px;
}
.pb-20 {
  padding-bottom: 20px;
}
.mr-5 {
  margin-right: 5px;
}
.mb-20 {
  margin-bottom: 20px;
}
.ml-30 {
  margin-left: 30px;
}
.ml-50 {
  margin-left: 50px;
}
.ml-5 {
  margin-left: 5px;
}
.ml-15 {
  margin-left: 15px;
}
.ml-20 {
  margin-left: 20px;
}
.pr-20 {
  padding-right: 20px;
}
.pl-20 {
  padding-left: 20px;
}
.mr-20 {
  margin-right: 20px;
}
.crm-search {
  padding-top: 10px !important;
  padding-bottom: 0 !important;
  border-bottom: 1px solid #f9f9f9 !important;
}
.crm-popover-more {
  padding: 5px 0;
  &-item {
    font-size: 14px;
    line-height: 36px;
    color: #666;
    cursor: pointer;
    transition: all 0.3s;
    padding: 0 20px;
    &:hover {
      background-color: #2e73f3;
      color: #fff;
    }
  }
}
.crm-approval-flow {
  padding: 20px;
  &-card {
    margin-bottom: 20px;
  }
  &-title {
    font-weight: 500;
    font-size: 14px;
    color: #666666;
    line-height: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e1e2e2;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  &-list {
    display: flex;
    flex-wrap: wrap;
  }
  &-item {
    cursor: pointer;
    margin-top: 20px;
    margin-right: 20px;
    height: 68px;
    border: 1px solid #cbd6e2;
    background-color: #f8f9fb;
    border-radius: 5px;
    padding-left: 20px;
    padding-right: 20px;
    display: flex;
    align-items: center;
    transition: all 0.3s;
    .icon {
      font-size: 20px;
      color: #2e73f3;
      margin-right: 10px;
    }
    .title {
      font-size: 16px;
      color: #333;
      line-height: 20px;
    }
    &:hover {
      background-color: #2e73f3;
      border-color: #2e73f3;
      box-shadow: 0px 1px 19px 0px rgba(46, 115, 243, 0.23);
      .icon,
      .title {
        color: #fff;
      }
    }
  }
}
.crm-flow-setting {
  border: 1px solid #cbd6e2;
  border-top: 0;
  border-radius: 0 0 5px 5px;
  padding: 15px;
  &-tabs {
    &.el-tabs {
      ::v-deep {
        .el-tabs__header {
          margin-bottom: 0;
          background-color: #f2f3f9;
          border-radius: 5px 5px 0 0;
          border-color: #cbd6e2;
          .el-tabs__nav-wrap {
            margin-bottom: -2px;
          }
          .el-tabs__nav {
            border: 0;
            .el-tabs__item {
              border: 1px solid transparent;
              border-bottom: 0;
              &:first-child {
                border-left: 1px solid transparent;
              }
              &.is-active {
                background-color: #fff;
                border-color: #cbd6e2;
                border-radius: 5px 5px 0 0;
              }
            }
          }
        }
        .el-tabs__content {
          display: none;
        }
      }
    }
  }
  &-switch {
    font-size: 14px;
    color: #666;
    line-height: 20px;
    margin-bottom: 15px;
    span {
      margin-right: 20px;
    }
  }
  &-title {
    font-size: 14px;
    color: #666;
    height: 50px;
    padding: 0 20px;
    border: 1px solid #cbd6e2;
    background-color: #f8f9fb;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  &-tips {
    font-size: 12px;
    color: #999;
    line-height: 20px;
    display: flex;
    align-items: center;
    margin-top: 10px;
    i {
      margin-right: 5px;
    }
  }
}
.crm-table-btn {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 12px;
  color: #999;
  transition: all 0.3s;
  i {
    font-size: 20px;
    color: #2e73f3;
    margin-right: 5px;
  }
  &:hover {
    color: #2e73f3;
  }
  &.danger {
    i {
      color: #999;
    }
    &:hover {
      color: #f02323;
      i {
        color: #f02323;
      }
    }
  }
  & + .crm-table-btn {
    margin-left: 20px;
  }
}
