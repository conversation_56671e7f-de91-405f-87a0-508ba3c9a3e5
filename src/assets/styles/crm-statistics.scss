.flex1 {
  flex: 1;
  overflow: hidden;
}
.classify-time {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  font-size: 16px;
  color: #666;
  span:first-child {
    color: #333;
  }
  span + span {
    margin-left: 15px;
  }
}
.statistics {
  &-container {
    padding: 20px;
    .el-col .el-col {
      margin-bottom: 20px;
    }
  }
  &-card {
    background: #ffffff;
    box-shadow: 0px 0px 26px 0px rgba(0, 0, 0, 0.05);
    border-radius: 5px;
    border: 1px solid #dce3eb;
    &-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 20px;
      height: 50px;
      border-bottom: 1px solid #cbd6e2;
      background-color: #f8f9fb;
      border-radius: 5px 5px 0 0;
      .title {
        font-weight: 500;
        font-size: 16px;
        color: #666666;
        flex: 0 0 130px;
        display: flex;
        align-items: center;
      }
      .tips {
        font-weight: 400;
        font-size: 12px;
        color: #666;
        margin-left: 20px;
        flex-shrink: 0;
      }
    }
    &-tabs {
      flex: 1;
      margin-left: -21px;
      margin-top: -3px;
      margin-bottom: -2px;
      &.el-tabs {
        ::v-deep {
          .el-tabs__header {
            margin-bottom: 0;
            border-radius: 5px 5px 0 0;
            border-bottom: 0;
            .el-tabs__nav {
              border: 0;
              .el-tabs__item {
                height: 51px;
                line-height: 51px;
                border: 1px solid transparent;
                border-bottom: 0;
                &:first-child {
                  border-left: 1px solid transparent;
                }
                &.is-active {
                  background-color: #fff;
                  border-color: #cbd6e2;
                  font-weight: 500;
                  font-size: 16px;
                  color: #666666;
                  border-radius: 5px 5px 0 0;
                }
              }
            }
          }
          .el-tabs__content {
            display: none;
          }
        }
      }
    }
    &-footer {
      background-color: #f0f3f9;
      padding: 0 20px;
      height: 62px;
      display: flex;
      align-items: center;
      border-radius: 0 0 5px 5px;
      .text {
        font-size: 12px;
        color: #999;
      }
      .value {
        font-weight: 500px;
        font-size: 14px;
        color: #f35d09;
        margin: 0 10px;
      }
    }
    &-notes {
      display: flex;
      flex-wrap: wrap;
      padding: 20px;
      height: 240px;
      &-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: calc(100% / 3);
        padding: 20px 0;
        .value {
          font-size: 20px;
          line-height: 40px;
          color: #999;
          &.primary {
            color: #2e73f3;
          }
          &.orange {
            color: #f35d09;
          }
        }
        .text {
          font-size: 12px;
          color: #666;
          line-height: 20px;
        }
      }
    }
    &-total {
      display: flex;
      align-items: center;
      padding: 15px 20px;
      height: 240px;
      &-progress {
        flex-shrink: 0;
        position: relative;
        width: 132px;
        margin-right: 50px;
        .big-progress {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
        }
        .small-progress {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
        }
        .info {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          width: 90px;
          height: 90px;
          border-radius: 50%;
          overflow: hidden;
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          .text {
            font-size: 12px;
            color: #999999;
            line-height: 20px;
          }
          .value {
            font-size: 20px;
            color: #31c776;
            line-height: 23px;
          }
        }
      }
      &-info {
        padding-right: 10px;
        flex: 1;
        display: flex;
        flex-direction: column;
        .title {
          font-size: 12px;
          color: #666666;
          line-height: 20px;
        }
        .item {
          display: flex;
          align-items: center;
          margin-top: 25px;
          margin-bottom: 25px;
          height: 30px;
          .point {
            flex-shrink: 0;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background-color: #2e73f3;
            margin-right: 8px;
          }
          .text {
            flex-shrink: 0;
            font-weight: 500;
            font-size: 14px;
            color: #666666;
          }
          .value {
            flex: 1;
            font-size: 20px;
            color: #2e73f3;
            text-align: right;
            margin-right: 10px;
          }
          .unit {
            flex-shrink: 0;
            font-size: 12px;
            color: #999999;
          }
          &.success {
            .point {
              background-color: #31c776;
            }
            .value {
              color: #31c776;
            }
          }
        }
      }
    }
    &-ranking {
      &-tabs {
        &.el-tabs {
          ::v-deep {
            .el-tabs__header {
              margin-bottom: 0;
              .el-tabs__nav-wrap {
                padding-left: 20px;
              }
            }
          }
        }
      }
      &-list {
        overflow-x: hidden;
        overflow-y: auto;
        &-item {
          display: flex;
          align-items: center;
          padding: 7px 20px;
          .number {
            flex-shrink: 0;
            display: inline-block;
            font-size: 12px;
            color: #666666;
            width: 20px;
            height: 20px;
            margin-right: 5px;
            text-align: center;
            &.number-1 {
              background: url('~@/assets/images/statistics-icon-1.png') no-repeat center center;
              background-size: 100% 100%;
            }
            &.number-2 {
              background: url('~@/assets/images/statistics-icon-2.png') no-repeat center center;
              background-size: 100% 100%;
            }
            &.number-3 {
              background: url('~@/assets/images/statistics-icon-3.png') no-repeat center center;
              background-size: 100% 100%;
            }
          }
          .avatar {
            flex-shrink: 0;
            width: 38px;
            height: 38px;
            margin-right: 10px;
          }
          .name {
            flex-shrink: 0;
            width: 70px;
            font-size: 14px;
            color: #333333;
            margin-right: 10px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          .progress {
            flex: 1;
            margin-right: 20px;
          }
          .value {
            flex-shrink: 0;
            font-size: 14px;
            color: #2e73f3;
          }
          &:nth-child(even) {
            background-color: #f0f3f9;
          }
        }
        &::-webkit-scrollbar {
          width: 5px;
        }
        &::-webkit-scrollbar-track {
          background-color: #f1f1f1;
          opacity: 0.15;
        }
        &::-webkit-scrollbar-thumb {
          background-color: #888888;
          border-radius: 5px;
        }
        &::-webkit-scrollbar-thumb:hover {
          background: #555555;
        }
      }
    }
    &-orderChart {
      display: flex;
      flex-direction: column;
      &-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 15px 20px;
        &-type {
          display: flex;
          align-items: center;
          justify-content: space-between;
          background: #e2e6f3;
          border-radius: 7px;
          padding: 3px;
          gap: 10px;
          &-item {
            cursor: pointer;
            font-size: 12px;
            color: #666666;
            line-height: 16px;
            cursor: pointer;
            transition: all 0.3s;
            padding: 7px 18px;
            border-radius: 5px;
            &.active,
            &:hover {
              background-color: #ffffff;
              box-shadow: 0px 1px 9px 0px rgba(0, 0, 0, 0.18);
              font-weight: 500;
              color: #333333;
            }
          }
        }
        &-time {
          width: 100px;
        }
        &-tips {
          font-size: 12px;
          color: #666666;
          line-height: 20px;
          display: flex;
          align-items: center;
          margin-left: 50px;
          &::before {
            content: '';
            display: inline-block;
            width: 15px;
            height: 3px;
            background-color: #2e73f3;
            border-radius: 5px;
            margin-right: 10px;
          }
        }
        &-title {
          font-weight: 500;
          font-size: 20px;
          color: #333333;
          line-height: 20px;
        }
      }
    }
    &-selected {
      padding: 20px;
      .selected-box {
        display: flex;
        align-items: center;
        height: 62px;
        background: #f0f3f9;
        border-radius: 5px;
        .selected-item {
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 12px;
          color: #999999;
          padding-left: 20px;
        }
      }
    }
  }
  &-theme {
    float: right;
    background-color: #ebedf3;
    border-radius: 5px;
    &-item {
      display: inline-block;
      padding: 0 10px;
      text-align: center;
      font-size: 12px;
      line-height: 32px;
      color: #666666;
      cursor: pointer;
      transition: all 0.3s;
      &.active,
      &:hover {
        color: #fff;
        background: #2e73f3;
        box-shadow: 0px 1px 9px 0px rgba(0, 0, 0, 0.18);
        border-radius: 5px;
      }
    }
  }
}
$bgLight: #f9f9f9;
$bgDark: #20184c;
.lightTheme {
  background-color: $bgLight;
}
.darkTheme {
  background-color: $bgDark;
  .custom-search {
    background-color: $bgDark;
    ::v-deep {
      .el-form-item__label {
        color: #c3bfd8;
      }
    }
  }
  .classify {
    background-color: $bgDark !important;
    border-bottom-color: transparent !important;
    &-item {
      color: #c3bfd8;
      &:hover,
      &.active {
        color: #fff;
        border-bottom-color: #fff;
      }
    }
    &-time {
      color: #c3bfd8;
      span:first-child {
        color: #fff;
      }
    }
  }
  .statistics {
    &-container {
      background-color: #110c2b;
    }
    &-card {
      border-color: #462adc;
      &-header {
        background-color: #201560;
        border-bottom-color: #462adc;
        .title {
          color: #fff;
        }
      }
      &-total {
        background-color: #20184c;
        &-progress {
          .info .text {
            color: #c3bfd8;
          }
        }
        &-info {
          .title {
            color: #c3bfd8;
          }
          .item {
            .text {
              color: #c3bfd8;
            }
          }
        }
      }
      &-notes {
        background-color: #20184c;
        &-item {
          .text {
            color: #c3bfd8;
          }
        }
      }
      &-footer {
        background-color: #201560;
        .text {
          color: #c3bfd8;
        }
      }
      &-tabs.el-tabs {
        ::v-deep {
          .el-tabs__header {
            .el-tabs__nav {
              .el-tabs__item {
                color: #c3bfd8;
                &.is-active {
                  border-color: #462adc;
                  background-color: #20184c;
                  color: #fff;
                }
              }
            }
          }
        }
      }
      &-orderChart {
        &-header {
          background-color: #201560;
          &-type {
            background-color: #39306a;
            &-item {
              color: #c3bfd8;
              &.active,
              &:hover {
                color: #2e73f3;
              }
            }
          }
          &-title {
            color: #fff;
          }

        }
      }
      &-ranking {
        &-tabs.el-tabs {
          ::v-deep {
            .el-tabs__header {
              background-color: #20184c;
              .el-tabs__nav {
                .el-tabs__item {
                  color: #c3bfd8;
                  &.is-active {
                    color: #fff;
                  }
                }
                .el-tabs__active-bar {
                  background-color: #fff;
                }
              }
            }
          }
        }
        &-list {
          background-color: #20184c;
          &-item {
            .name {
              color: #fff;
            }
            &:nth-child(even) {
              background-color: #201560;
            }
          }
        }
      }
      &-selected {
        background-color: #201560;
        .selected-box {
          background: #39306a;
          .selected-item {
            color: #fff;
          }
        }
      }
    }
    &-theme {
      background-color: #372e69;
      &-item {
        color: #c3bfd8;
        &.active,
        &:hover {
          color: #2e73f3;
          background: #fff;
        }
      }
    }
  }
}

.search_criteria {
  display: flex;
  align-items: center;
  height: 62px;
  background: #F0F3F9;
  border-radius: 5px;
  border: 1px solid #CBD6E2;
  margin-bottom: 15px;

  .search_criteria_left {
      padding-left: 15px;
      padding-right: 20px;
  }

  .search_criteria_right {
      flex: 1;
      display: flex;
      background: #FFFFFF;
      height: 100%;
      padding-left: 20px;
      padding-right: 15px;
      align-items: center;

      .right_item {
          display: flex;
          align-items: center;
          margin-right: 20px;

          .title {
              font-size: 12px;
              color: #999999;
              margin-right: 5px;
              flex-shrink: 0;
          }
      }
  }
}