$white: #ffffff;
$blue: #2e73f3;
$red: #ec2454;
$lose: #cdcdcd;
$font: #333333;
$info: #666666;
$bgColor: #f0f3f9;
$orange: #f35d09;
$disabled: #999999;
p {
  margin: 0;
  padding: 0;
}
.flex {
  display: flex;
}
.inline-flex {
  display: inline-flex;
  height: 100%;
  align-items: center;
}
.flex-column {
  flex-direction: column;
}
.flex-wrap {
  flex-wrap: wrap;
}
.flex-align-start {
  align-items: flex-start;
}
.webContainer {
  width: 1200px;
  margin: 0 auto;
}
.webBox {
  background-color: #f9f9f9;
}
// 头部
.webHeader {
  width: 100%;
  min-width: 1200px;
  height: 112px;
  background-color: $blue;
  .webContainer {
    height: 100%;
    justify-content: space-between;
    align-items: center;
  }
  &-logo {
    display: inline-flex;
    align-items: center;
    justify-content: space-around;
    color: $white;
    cursor: pointer;
    i {
      font-size: 50px;
      margin-left: 15px;
      margin-right: 15px;
    }
    b,
    span {
      display: block;
      font-size: 16px;
    }
    b {
      font-size: 19px;
      font-weight: normal;
    }
  }
  &-search {
    display: flex;
    align-items: center;
    width: 666px;
    height: 50px;
    padding-left: 15px;
    background-color: $white;
    border-radius: 100px;
    position: relative;
    overflow: hidden;
    ::v-deep .el-input {
      .el-input__inner {
        height: 50px;
        border-width: 0;
        padding-left: 0;
      }
    }
    &-select {
      flex-shrink: 0;
      width: calc(4em + 25px);
      .el-input__inner {
        padding-left: 0px;
      }
    }
    &-input {
      flex: 1;
      overflow: hidden;
    }
    &-btn {
      flex-shrink: 0;
      margin-right: 5px;
      cursor: pointer;
      width: 115px;
      height: 40px;
      line-height: 40px;
      background-color: $blue;
      border-radius: 100px;
      border-width: 0;
      font-size: 16px;
      color: $white;
      &:hover {
        opacity: 0.8;
      }
    }
  }
  &-login {
    display: inline-flex;
    justify-content: flex-end;
    align-items: center;
    color: $white;
    width: 195px;
    padding-right: 15px;
    &-icon {
      font-size: 22px;
    }
    &-img {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      margin: 0 15px;
    }
    &-name {
      font-size: 16px;
      cursor: pointer;
    }
  }
}
// 导航
.webNav {
  width: 100%;
  min-height: 40px;
  padding: 10px 0;
  background-color: $white;
  box-shadow: 0 1px 5px 0 rgba(0, 0, 0, 0.05);
  &-item {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: nowrap;
    font-size: 14px;
    padding: 0 10px;
    margin: 0 10px;
    cursor: pointer;
    &:hover,
    &.active {
      color: $blue;
    }
  }
}
// 搜索结果
::v-deep .searchBox {
  // position: absolute;
  // top: 50%;
  // left: 50%;
  // transform: translate(-50%, -50%);
  // margin-top: 0 !important;
  .el-dialog__body {
    padding: 20px 30px;
  }
  &-item {
    width: 100%;
    position: relative;
    display: inline-flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-bottom: 20px;
    cursor: pointer;
    &-img {
      width: 52px;
      height: 52px;
      margin: auto;
      border: 1px solid #ededed;
      overflow: hidden;
      border-radius: 5px;
      img {
        transition: all 0.5s;
      }
      .el-image__error {
        font-size: 12px;
      }
    }
    &-icon {
      width: 52px;
      height: 52px;
      margin: auto;
      border: 1px solid #ededed;
      overflow: hidden;
      border-radius: 5px;
      display: inline-flex;
      justify-content: center;
      align-items: center;
      color: $disabled;
    }
    &-name,
    &-spec {
      display: block;
      font-weight: normal;
      width: 100%;
      height: 20px;
      line-height: 20px;
      text-align: center;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    &-name {
      font-size: 14px;
      color: $info;
      margin-top: 5px;
    }
    &-spec {
      font-size: 12px;
      color: $disabled;
    }
    &:hover {
      .searchBox-item {
        &-img {
          img {
            transform: scale(1.2);
          }
        }
        &-name,
        &-spec {
          color: $blue;
        }
      }
    }
  }
  &-pagination {
    margin: 10px 0 30px;
  }
}
// 底部
.webFooter {
  width: 100%;
  min-width: 1200px;
  background-color: $white;
  .webContainer {
    display: flex;
    align-items: flex-start;
    padding: 10px 0 30px;
    justify-content: space-between;
  }
  &-title {
    font-size: 14px;
    color: #292f48;
    line-height: 26px;
  }
  &-company {
    width: 280px;
    &-logo {
      display: flex;
      align-items: center;
      justify-content: center;
      min-width: 220px;
      max-width: 280px;
      min-height: 80px;
      border-radius: 5px;
      border: 1px solid #ededed;
      background-color: #fbfbfb;
      margin-top: 5px;
      margin-bottom: 15px;
      padding: 15px 0;
      img {
        width: 52px;
        height: 50px;
        margin-right: 10px;
      }
      &-flex {
        display: flex;
        flex-direction: column;
        b {
          font-weight: 500;
          font-size: 20px;
          line-height: 23px;
          color: $blue;
        }
        span {
          font-weight: 500;
          font-size: 17px;
          line-height: 20px;
          color: $blue;
        }
      }
    }
    &-t1 {
      font-size: 14px;
      line-height: 22px;
      color: $info;
    }
    &-t2 {
      font-size: 14px;
      line-height: 22px;
      color: $font;
    }
    &-t3 {
      margin-top: 5px;
      margin-bottom: 5px;
      font-weight: 500;
      font-size: 16px;
      line-height: 24px;
      color: #000000;
    }
    &-t4 {
      font-size: 20px;
      line-height: 30px;
      color: #373f5e;
    }
  }
  &-nav {
    width: 360px;
    &-list {
      width: 50%;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      padding-right: 10px;
    }
    &-item {
      display: block;
      width: auto;
      min-width: 70px;
      margin-bottom: 15px;
      padding: 0 8px;
      line-height: 24px;
      font-size: 14px;
      color: $info;
      background-color: #f2f7ff;
      border-radius: 5px;
      cursor: pointer;
      transition: all 0.3s;
      &:hover {
        background-color: $blue;
        color: $white;
      }
    }
    &-code {
      width: 90px;
      height: 90px;
      border: 1px solid #cbd7e2;
      border-radius: 5px;
      img {
        width: 100%;
        height: 100%;
      }
    }
  }
  &-tool {
    width: 150px;
    ul {
      width: calc(100% + 10px);
      li {
        width: 70px;
        height: 70px;
        float: left;
        background-color: #f8faff;
        margin-right: 10px;
        margin-bottom: 10px;
        display: inline-flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        img {
          width: 26px;
          height: 26px;
        }
        span {
          margin-top: 8px;
          font-size: 12px;
          line-height: 20px;
          color: $info;
        }
        &:hover {
          span {
            color: $blue;
          }
        }
      }
    }
  }
  &-msg {
    width: 380px;
    ul {
      li {
        display: inline-flex;
        align-items: center;
        font-size: 14px;
        color: $info;
        height: 22px;
        line-height: 22px;
        margin-bottom: 7px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        cursor: pointer;
        i {
          font-style: normal;
          width: 18px;
          height: 18px;
          line-height: 18px;
          text-align: center;
          border-radius: 50%;
          background-color: #d6dff1;
          color: $white;
          margin-right: 10px;
        }
        &:hover {
          color: $blue;
        }
        &:nth-child(-n + 3) {
          i {
            background-color: #3ec5ff;
          }
        }
      }
    }
  }
  &-about {
    width: 370px;
    &-info {
      display: inline-flex;
      height: 90px;
      &-icon {
        display: inline-flex;
        justify-content: center;
        align-items: center;
        width: 60px;
        height: 100%;
        background-color: #93b8fd;
        img {
          width: 20px;
          height: 20px;
        }
      }
      &-box {
        display: inline-flex;
        flex-direction: column;
        justify-content: center;
        width: 240px;
        background-color: #ededed;
      }
      &-item {
        font-size: 14px;
        line-height: 22px;
        text-indent: 20px;
      }
    }
    &-edit {
      display: inline-flex;
      align-items: center;
      margin: 15px 0;
      span {
        font-size: 14px;
        color: #292f48;
      }
      button {
        background-color: $blue;
        color: $white;
        min-width: 90px;
        height: 24px;
        font-size: 14px;
        border-width: 0;
        border-radius: 5px;
        margin-left: 5px;
        cursor: pointer;
        &:hover {
          opacity: 0.8;
        }
      }
    }
    &-desc {
      span {
        font-size: 14px;
        line-height: 1em;
        color: #292f48;
      }
    }
  }
  &-copyright {
    background-color: #292f48;
    color: rgba(255, 255, 255, 0.52);
    text-align: center;
    font-size: 12px;
    line-height: 28px;
    padding: 20px 0;
    a:hover {
      color: $blue;
    }
    &-link {
      cursor: pointer;
      margin-left: 5px;
      &:hover {
        color: $blue;
      }
    }
  }
  .navigation {
    min-width: 46px;
    height: 180px;
    border: 0 solid;
    position: fixed;
    top: 60%;
    left: 51%;
    margin-left: 610px;
    &-item {
      display: flex;
      flex-direction: column;
      height: auto;
      background: $white;
      box-shadow: 0 0 13px 0 rgba(0, 0, 0, 0.15);
      border-radius: 100px;
      span {
        display: inline-block;
        border-radius: 100px;
        font-size: 14px;
        text-align: center;
        height: 46px;
        min-width: 46px;
        line-height: 46px;
        transition: 0.1s linear;
        cursor: pointer;
        transform: scale(0.85);
        &:hover {
          background-color: $blue;
          color: $white;
        }
      }
    }
    &-backtop {
      width: 46px;
      height: 46px;
      background: #ffffff;
      box-shadow: 0 0 13px 0 rgba(0, 0, 0, 0.15);
      border-radius: 100px;
      display: flex;
      transition: 0.2s;
      cursor: pointer;
      margin-top: 20px;
      img {
        width: 20px;
        height: 20px;
        margin: auto;
      }
      &:hover {
        box-shadow: 0 0 13px 0 rgba(0, 0, 0, 0.35);
      }
    }
  }
}
// 中间内容
.webMain {
  width: 100%;
  &-header {
    display: flex;
    justify-content: space-between;
    background-color: #f9f9f9;
    border-radius: 6px;
    box-shadow: 0 0 26px 0 rgba(0, 0, 0, 0.05);
    margin-top: 10px;
    &.flex-end {
      justify-content: flex-end;
    }
  }
  &-left {
    width: 200px;
    &.left-fixed {
      width: 205px;
      padding-right: 5px;
      height: 100%;
      overflow-y: auto;
      position: fixed;
      left: 50%;
      transform: translateX(calc(-50% - 500px));
      top: 0;
      z-index: 2;
      &::-webkit-scrollbar {
        width: 2px;
      }
      &::-webkit-scrollbar-track {
        background-color: #f1f1f1;
        opacity: 0.15;
      }
      &::-webkit-scrollbar-thumb {
        background-color: #cccccc;
        border-radius: 5px;
      }
      &::-webkit-scrollbar-thumb:hover {
        background: #555555;
      }
    }
    &-ad {
      width: 100%;
      height: auto;
      position: relative;
      margin-bottom: 5px;
      cursor: pointer;
      display: inline-flex;
      overflow: hidden;
      &-title {
        position: absolute;
        left: 0;
        bottom: -66px;
        display: inline-flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: 66px;
        font-size: 14px;
        background-color: rgba(4, 4, 4, 0.6);
        border-top-left-radius: 30px;
        border-top-right-radius: 30px;
        transition: 0.2s;
        color: $white;
        span {
          display: inline-flex;
          align-items: center;
          &:after,
          &:before {
            display: inline-block;
            content: '';
            width: 26px;
            height: 1px;
            background: rgba(255, 255, 255, 0.56);
            margin: 0 5px;
          }
        }
        b {
          font-size: 16px;
          margin-top: 5px;
          font-weight: 500;
        }
      }
      &:hover {
        .webMain-left-ad-title {
          bottom: 0;
        }
      }
    }
    &-product {
      margin-top: 10px;
      font-size: 14px;
      color: $info;
      background-color: $white;
      border-radius: 5px;
      box-shadow: 0 0 26px 0 rgba(0, 0, 0, 0.05);
      padding: 10px;
      border: 1px solid #e5e5e5;
      margin-bottom: 10px;
      &-title {
        width: 100%;
        margin-bottom: 10px;
      }
      &-list {
        display: inline-flex;
        justify-content: flex-start;
        flex-wrap: wrap;
        span {
          display: inline-block;
          padding: 0 10px;
          line-height: 35px;
          background-color: #efefef;
          margin-bottom: 10px;
          margin-right: 10px;
          border-radius: 5px;
          cursor: pointer;
          transition: 0.3s;
          &:hover {
            background-color: $blue;
            color: $white;
          }
        }
      }
    }
  }
  &-right {
    width: 990px;
    &-kline {
      width: 100%;
      box-shadow: 0 0 26px 0 rgba(0, 0, 0, 0.05);
      position: relative;
      border-radius: 5px;
      &-title {
        line-height: 30px;
        font-size: 14px;
        display: flex;
        justify-content: space-between;
        width: 100%;
        color: $info;
        background-color: #eceef3;
        .kline-title-item {
          span {
            display: inline-block;
            cursor: pointer;
            padding-left: 20px;
            padding-right: 20px;
            &.active {
              background-color: $white;
            }
          }
        }
        .kline-title-close {
          display: none;
          padding-right: 5px;
          cursor: pointer;
          font-size: 16px;
        }
        &:hover {
          .kline-title-close {
            display: inline-block;
          }
        }
      }
    }
  }
  &-newProduct {
    border: 1px solid #e5e5e5;
    margin: 15px 0;
    border-radius: 5px;
    box-shadow: 0 0 26px 0 rgba(0, 0, 0, 0.05);
    background-color: $white;
    padding: 10px 20px;
    &-title {
      display: inline-flex;
      align-items: center;
      font-size: 14px;
      color: $info;
      margin-bottom: 20px;
    }
  }
  &-adBox {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
    &-item {
      max-width: 390px;
      height: auto;
    }
  }
}
// 产品分类
.productNav {
  width: 100%;
  border-bottom: 1px solid #e3e3e3;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 50px;
  line-height: 50px;
  margin-top: 5px;
  position: relative;
  gap: 10px;
  .flex-shrink {
    flex-shrink: 0;
  }
  .flex1 {
    flex: 1;
    overflow: hidden;
  }
  &-item {
    height: 51px;
    width: 100%;
    display: flex;
    align-items: center;
    &-tabs {
      height: 51px;
      margin-right: 10px;
      ::v-deep {
        .el-tabs__header {
          margin-bottom: 0;
          .el-tabs__nav-wrap {
            &::after {
              display: none;
            }
            &.is-scrollable {
              padding: 0;
            }
            .el-tabs__nav-prev,
            .el-tabs__nav-next {
              display: none;
            }
            .el-tabs__active-bar {
              background-color: #000000;
            }
            .el-tabs__item {
              padding: 0 15px;
              &:hover {
                color: #000000;
              }
              &.is-active {
                font-size: 16px;
                color: #000000;
              }
            }
          }
        }
      }
    }
    &-item {
      display: inline-block;
      padding: 0 10px;
      margin: 0 8px;
      cursor: pointer;
      font-size: 14px;
      &.active {
        font-size: 16px;
        line-height: 49px;
        border-bottom: 2px solid #000000;
        margin-bottom: -1px;
      }
    }
    &-setting {
      display: inline-flex;
      align-items: center;
      cursor: pointer;
      padding-right: 6px;
      font-size: 14px;
      color: $blue;
      i {
        font-size: 16px;
        margin-right: 5px;
      }
      &:hover {
        opacity: 0.8;
      }
    }
    &-add {
      font-size: 14px;
      display: inline-flex;
      align-items: center;
      color: $disabled;
      cursor: pointer;
      padding-right: 10px;
      i {
        font-size: 16px;
        margin: 0 6px;
      }
      &:before {
        content: '';
        display: inline-block;
        width: 1px;
        height: 16px;
        background-color: #e3e3e3;
      }
      &:hover {
        color: $blue;
      }
    }
  }
  &-vip {
    height: 50px;
    display: inline-flex;
    align-items: center;
    cursor: pointer;
    padding-right: 5px;
    span {
      display: inline-block;
      font-size: 14px;
      color: $disabled;
      margin-left: 10px;
    }
  }
}
.productSecondNav {
  width: 100%;
  margin-top: 15px;
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  background-color: $white;
  box-shadow: 0 0 26px 0 rgba(0, 0, 0, 0.05);
  .levelBox {
    width: 16.66667%;
    height: 30px;
    border: 0 solid;
    margin: 5px 0;
    color: $info;
    text-align: center;
    line-height: 30px;
    font-size: 14px;
    padding: 0 5px;
    position: relative;
    cursor: pointer;
    &-name {
      width: 100%;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      &:hover,
      &.active {
        color: $blue;
      }
    }
  }
  .levelSonBox {
    width: 100%;
    position: absolute;
    top: 30px;
    left: 0;
    z-index: 9999;
    box-shadow: 0 1px 15px 0 rgba(0, 0, 0, 0.25);
    font-size: 12px;
    &-item {
      width: 100%;
      height: 35px;
      border: 0 solid;
      background-color: #ffffff;
      line-height: 35px;
      cursor: pointer;
      position: relative;
      z-index: 10000;
    }
    &-name {
      &:hover,
      &.active {
        color: $white;
        background-color: $blue;
      }
    }
  }
  .levelGrandsonBox {
    width: 200px;
    position: absolute;
    left: 100%;
    z-index: 10000;
    box-shadow: 0 1px 15px 0 rgba(0, 0, 0, 0.25);
    top: 0;
    &-item {
      width: 100%;
      height: 35px;
      border: 0 solid;
      background-color: #f2f4f8;
      line-height: 35px;
      cursor: pointer;
      position: relative;
    }
    &-name {
      &:hover,
      &.active {
        background-color: #d3dae7;
        color: $blue;
      }
    }
  }
}
.navProductList {
  width: 100%;
  min-height: 395px;
  margin-top: 15px;
  background-color: $white;
  box-shadow: 0 0 26px 0 rgba(0, 0, 0, 0.05);
  border-radius: 5px;
  padding: 10px;
}
// 滞销品
.deadPorduct {
  margin-top: 15px;
  width: 100%;
  @for $i from 1 through 10 {
    &-box#{$i} {
      width: calc(100% + (#{$i} - 1) * 12px);
      display: flex;
      align-items: center;
      flex-wrap: wrap;
    }
  }
  &-item {
    @for $i from 1 through 10 {
      &.item#{$i} {
        width: calc((100% - (#{$i} - 1) * 24px) / #{$i});
        &:nth-child(#{$i}n) {
          margin-right: 0;
        }
      }
    }
    border-radius: 8px;
    background: $white;
    margin-bottom: 25px;
    margin-right: 12px;
    cursor: pointer;
    overflow: hidden;
    .item-image {
      ::v-deep .el-image {
        width: 100%;
        height: 0;
        padding-bottom: 100%;
        position: relative;
        img {
          position: absolute;
          top: 0;
          left: 0;
          transition: all 0.3s;
        }
        .image-slot {
          position: absolute;
          top: 0;
          left: 0;
          display: inline-flex;
          width: 100%;
          height: 100%;
          align-items: center;
          justify-content: center;
          background-color: #f1f1f1;
          i {
            font-size: 60px;
          }
        }
      }
    }
    .item-title {
      font-size: 14px;
      font-weight: 400;
      color: $info;
      height: 40px;
      line-height: 20px;
      padding: 0 10px;
      overflow: hidden;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      margin-top: 15px;
    }
    .item-brand {
      padding: 0 10px;
      font-size: 12px;
      color: $info;
      line-height: 20px;
      margin-top: 7px;
    }
    .item-price {
      padding: 0 10px;
      font-size: 12px;
      color: #f43f3f;
      line-height: 20px;
      margin-top: 7px;
      b {
        font-size: 18px;
        font-weight: 500;
      }
    }
    .item-num {
      margin: 20px 10px;
      font-size: 12px;
      color: $info;
      height: 30px;
      line-height: 30px;
      border: 1px solid $blue;
      border-radius: 5px;
      span {
        padding: 0 5px;
      }
      b {
        color: $blue;
        font-size: 14px;
        font-weight: 500;
      }
      em {
        display: inline-block;
        height: 28px;
        line-height: 28px;
        float: right;
        font-style: normal;
        color: $white;
        background-color: $blue;
        padding-right: 10px;
        padding-left: 3px;
        position: relative;
        &:before {
          position: absolute;
          left: -15px;
          top: -1px;
          content: '';
          width: 14px;
          height: 30px;
          border-style: solid;
          border-width: 15px 15px 15px 0;
          border-color: transparent $blue transparent transparent;
        }
      }
    }
    &:hover {
      box-shadow: 0 1px 18px 0 rgba(0, 0, 0, 0.15);
      .item-title {
        color: $font;
        font-weight: 550;
      }
      .item-image {
        ::v-deep .el-image {
          img {
            transform: scale(1.2);
          }
        }
      }
    }
  }
}
::v-deep {
  .deadNav {
    margin-top: 15px;
    box-shadow: 0 1px 9px 0 rgba(0, 0, 0, 0.07);
    border-radius: 10px;
    overflow: hidden;
    &-label {
      width: 100px;
      background-color: #f2f4f8;
      text-align: center !important;
    }
    &-content {
      padding: 0 !important;
      span {
        display: inline-block;
        line-height: 30px;
        font-size: 14px;
        padding: 0 14px;
        cursor: pointer;
        margin: 5px;
        position: relative;
        transition: all 0.3s;
        &.active {
          background: $blue;
          color: $white;
          border-radius: 5px;
        }
        b {
          font-weight: normal;
          display: inline-block;
          width: 100%;
          height: 100%;
          position: relative;
        }
        i {
          position: absolute;
          top: -6px;
          right: -6px;
          z-index: 99999;
          background-color: #ffffff;
          color: #000000;
          border-radius: 50%;
        }
      }
    }
  }
}
// 滞销品下单
.deadView {
  &-title {
    font-size: 18px;
    color: $info;
    margin-top: 5px;
    line-height: 46px;
    border-bottom: 1px solid #eef0f8;
    padding-left: 20px;
  }
  &-order {
    padding: 18px 0 18px 20px;
    font-size: 14px;
    line-height: 20px;
    color: $info;
    span + span {
      margin-left: 120px;
    }
  }
  &-info {
    padding: 20px;
    background-color: $white;
    border-radius: 5px;
    overflow: hidden;
    &-img {
      width: 190px;
      height: 190px;
      border: 1px solid #efefef;
      border-radius: 8px;
      overflow: hidden;
      float: left;
      ::v-deep .el-image {
        width: 100%;
        height: 100%;
        .image-slot {
          display: inline-flex;
          width: 100%;
          height: 100%;
          align-items: center;
          justify-content: center;
          background-color: #f1f1f1;
          i {
            font-size: 60px;
          }
        }
      }
    }
    &-con {
      float: left;
      margin-left: 20px;
      width: calc(100% - 210px);
      .deadtitle {
        font-size: 14px;
        font-weight: 500;
        color: $info;
        line-height: 32px;
      }
      .deadbrand {
        font-size: 12px;
        color: $info;
        line-height: 20px;
        margin-top: 7px;
      }
      .deadprice {
        font-size: 12px;
        color: #f43f3f;
        line-height: 20px;
        margin-top: 7px;
        border-bottom: 1px solid #eef0f8;
        b {
          font-size: 18px;
          font-weight: 500;
        }
      }
      .deadnum {
        font-size: 12px;
        color: $info;
        height: 30px;
        line-height: 30px;
        border-radius: 5px;
        span:first-child {
          display: inline-block;
          min-width: 5.5em;
          text-align: justify;
          text-align-last: justify;
        }
        ::v-deep .el-input__inner {
          text-align: center;
        }
      }
    }
    &-total {
      width: 100%;
      border: 1px solid #ec4545;
      background: #fff5f5;
      border-radius: 5px;
      line-height: 48px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 20px;
      margin-top: 35px;
      .text {
        font-size: 14px;
        color: $info;
        b {
          font-size: 18px;
          font-weight: 500;
          color: $orange;
        }
        &:first-child {
          b {
            padding: 0 18px;
          }
        }
      }
    }
  }
  &-button {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    margin: 30px auto 50px;
    ::v-deep .el-button {
      width: 270px;
      line-height: 26px;
    }
  }
}
.order-success {
  padding: 50px;
  margin: 50px;
  background-color: $white;
  display: flex;
  flex-direction: column;
  align-items: center;
  &-img {
    font-size: 90px;
    color: #31c776;
  }
  &-title {
    font-size: 20px;
    line-height: 20px;
    margin-top: 30px;
    margin-bottom: 20px;
    color: $font;
  }
  &-tip {
    font-size: 14px;
    line-height: 30px;
    color: $red;
  }
  &-info {
    font-size: 14px;
    line-height: 30px;
    color: $info;
  }
  &-button {
    display: flex;
    flex-direction: row;
    margin-top: 20px;
    ::v-deep .el-button {
      width: 230px;
      line-height: 26px;
      &.is-plain {
        background-color: $white;
        &:hover {
          background-color: $blue;
        }
      }
    }
  }
}
.promotional-product-list {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  position: relative;
  height: 375px;
  .promotional-product-item {
    position: relative;
    width: 190px;
    height: 375px;
    background-color: $white;
    border-radius: 5px;
    box-shadow: 0 0 5px 0 rgba(0, 0, 0, 0.1);
    transition: all 0.1s;
    cursor: pointer;
    .item-image-box {
      position: relative;
      width: 100%;
      height: 190px;
      overflow: hidden;
      border-radius: 8px;
      .tip {
        position: absolute;
        top: 0;
        left: 0;
        z-index: 2;
        width: 50px;
        height: 30px;
        line-height: 30px;
        text-align: center;
        font-size: 14px;
        color: $white;
        background-color: #f43f3f;
        border-radius: 0 0 20px 0;
        text-transform: uppercase;
      }
      .image {
        width: 100%;
        height: 100%;
      }
      .num {
        position: absolute;
        left: 0;
        bottom: 0;
        z-index: 2;
        background-color: rgba(0, 0, 0, 0.5);
        width: 100%;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        color: $white;
        b {
          font-size: 14px;
          margin: 0 5px;
        }
      }
    }
    .padding-10 {
      padding: 10px;
    }
    .item-title {
      height: 40px;
      line-height: 20px;
      font-size: 14px;
      color: $info;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      white-space: normal;
      margin: 5px 0;
    }
    .item-brand,
    .item-num {
      font-size: 12px;
      line-height: 20px;
      color: $info;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .item-price {
      display: flex;
      align-items: center;
      font-size: 12px;
      color: #f43f3f;
      margin: 5px 0;
      b {
        font-weight: 500;
        font-size: 24px;
        margin: 0 5px;
      }
    }
    .item-original {
      display: flex;
      align-items: center;
      margin: 10px 0;
      .item-original-tip {
        font-size: 12px;
        color: $white;
        line-height: 22px;
        padding: 0 5px;
        border-radius: 5px;
        background: linear-gradient(144deg, #f39323 0%, #f02222 100%);
        margin-right: 5px;
        position: relative;
        &:before {
          position: absolute;
          top: -10px;
          left: 50%;
          transform: translateX(-50%);
          border: 5px solid transparent;
          border-bottom-color: #f26723;
          content: '';
          display: inline-block;
          width: 0;
          height: 0;
        }
      }
      .item-original-price {
        font-size: 12px;
        color: $disabled;
        text-decoration: line-through;
      }
    }
    .item-button {
      transition: all 0.1s;
      width: 100%;
      height: 0;
      line-height: 30px;
      text-align: center;
      font-size: 14px;
      color: $white;
      background-color: #f43f3f;
      border-radius: 5px;
      overflow: hidden;
      cursor: pointer;
    }
    &:hover {
      height: 405px;
      box-shadow: 0 1px 18px 0 rgba(98, 11, 11, 0.39);
      .item-title {
        color: $font;
      }
      .item-button {
        height: 30px;
      }
    }
  }
  .promotional-product-item + .promotional-product-item {
    margin-left: 12px;
  }
}
