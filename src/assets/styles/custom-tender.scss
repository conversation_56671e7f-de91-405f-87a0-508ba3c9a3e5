.tender {
  &-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px 0;
    background-color: #f9f9f9;
  }
  &-card {
    width: 1200px;
    background-color: #fff;
    box-shadow: 0px 1px 9px 0px rgba(0, 0, 0, 0.07);
    border-radius: 5px;
  }
  &-card + &-card {
    margin-top: 20px;
  }
  &-tabs {
    display: flex;
    align-items: center;
    padding: 5px 20px 0;
    border-bottom: 1px solid #ededed;
    margin-bottom: 20px;
    &-item {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 52px;
      padding: 0 12px;
      min-width: 90px;
      font-size: 16px;
      color: #666;
      cursor: pointer;
      transition: transform 0.35s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      &::after {
        content: '';
        display: block;
        width: 0;
        height: 2px;
        background-color: #2e73f3;
        position: absolute;
        bottom: -1px;
        transition: width 0.35s cubic-bezier(0.4, 0, 0.2, 1);
      }
      &.active {
        color: #2e73f3;
        font-weight: 500;
        font-size: 18px;
        &::after {
          width: 100%;
        }
      }
      &:hover {
        color: #2e73f3;
        &::after {
          width: 100%;
        }
      }
      & + & {
        margin-left: 20px;
      }
    }
  }
  &-search {
    &-bar {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      padding: 0 20px;
      height: 50px;
      gap: 15px;
    }
    &-inputs {
      width: 700px;
      height: 100%;
      padding: 5px;
      display: flex;
      align-items: center;
      border: 1px solid #cbd6e2;
      border-radius: 5px;
      background-color: #f5f5f5;
    }
    &-input {
      ::v-deep &.el-input {
        flex: 1;
        .el-input__inner {
          border: none;
          background-color: transparent;
          &:focus {
            box-shadow: none;
            border-color: transparent;
          }
        }
      }
    }
    &-btn {
      width: 110px;
    }
    &-select {
      width: 150px;
      ::v-deep {
        .el-input__inner {
          height: 50px;
          line-height: 50px;
        }
      }
    }
  }
  &-search-items {
    display: flex;
    align-items: center;
    padding: 0 20px;
    margin-bottom: 20px;
  }
  &-form-title {
    display: inline-block;
    font-size: 12px;
    color: #999;
    text-align: left;
    width: 90px;
  }
  &-info-type {
    display: flex;
    align-items: center;
    &-item {
      display: flex;
      align-items: center;
      height: 26px;
      line-height: 26px;
      font-size: 14px;
      color: #666;
      padding: 0 15px;
      cursor: pointer;
      transition: color 0.35s cubic-bezier(0.4, 0, 0.2, 1);
      &.active {
        color: #fff;
        background-color: #2e73f3;
        font-weight: 500;
        border-radius: 5px;
        &.is-active {
          color: #fff;
        }
      }
      &.active-text {
        color: #2e73f3;
        font-weight: 500;
      }
      i.el-icon-caret-bottom {
        margin-left: 3px;
        transition: transform 0.3s ease;
      }
      &.is-active {
        color: #2e73f3;
        i.el-icon-caret-bottom {
          transform: rotate(-180deg);
        }
      }
      &-wrapper {
        margin-left: 10px;
        padding-left: 10px;
        position: relative;
        &::before {
          content: '';
          display: block;
          width: 1px;
          height: 20px;
          background-color: #cbd6e2;
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
        }
        &.none {
          margin-left: 0;
          padding-left: 0;
          &::before {
            display: none;
          }
        }
      }
    }
  }
  &-info-check {
    display: flex;
    flex-direction: column;
    .el-checkbox {
      line-height: 38px;
      margin-right: 0;
    }
    .el-checkbox-group {
      display: flex;
      flex-direction: column;
    }
  }
  &-info-cascader {
    border-radius: 10px;
    overflow: hidden;
    ::v-deep {
      .el-cascader-menu__wrap {
        height: 250px;
      }
      .el-cascader-node.in-active-path,
      .el-cascader-node.is-active,
      .el-cascader-node.is-selectable.in-checked-path {
        font-weight: normal;
      }
      .el-cascader-menu:nth-child(1) {
        .el-cascader-node:not(.is-disabled):focus,
        .el-cascader-node:not(.is-disabled):hover {
          background-color: #dbe1ed;
        }
      }
      .el-cascader-menu:nth-child(2) {
        background-color: #f2f4f8;
        .el-cascader-node:not(.is-disabled):focus,
        .el-cascader-node:not(.is-disabled):hover {
          background-color: #d3dae7;
        }
      }
      .el-cascader-menu:nth-child(3) {
        background-color: #d3dae7;
        .el-cascader-node:not(.is-disabled):focus,
        .el-cascader-node:not(.is-disabled):hover {
          background-color: #909fbb;
          color: #fff;
        }
      }
    }
  }
  &-info-amount {
    display: flex;
    flex-direction: column;
    padding: 10px 0;
    &-item {
      cursor: pointer;
      font-size: 14px;
      line-height: 30px;
      padding: 0 20px;
      color: #666;
      transition: all 0.35s cubic-bezier(0.4, 0, 0.2, 1);
      &:hover,
      &.active {
        background-color: #2e73f3;
        color: #fff;
      }
    }
    &-custom {
      position: relative;
      color: #666;
      transition: all 0.35s cubic-bezier(0.4, 0, 0.2, 1);
      .title {
        cursor: pointer;
        font-size: 14px;
        line-height: 30px;
        padding: 0 20px;
      }
      &-wrapper {
        display: none;
        position: absolute;
        left: 100%;
        top: 50%;
        transform: translateY(-50%);
        padding-left: 5px;
      }
      &-input {
        display: flex;
        align-items: center;
        border: 1px solid #2e73f3;
        border-radius: 5px;
        box-shadow: 0px 1px 12px 0px rgba(0, 0, 0, 0.25);
        background: #f2f4f8;
        height: 46px;
        padding-left: 15px;
        font-size: 14px;
        color: #999;
        gap: 15px;
        .input {
          width: 130px;
          ::v-deep .el-input__suffix {
            display: flex;
            align-items: center;
          }
        }
        .confirm {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 58px;
          height: 100%;
          background-color: #2e73f3;
          color: #fff;
          font-size: 14px;
          border-radius: 0 5px 5px 0;
          cursor: pointer;
          transition: opacity 0.35s cubic-bezier(0.4, 0, 0.2, 1);
          &:hover {
            opacity: 0.8;
          }
        }
      }
      &:hover {
        background-color: #2e73f3;
        color: #fff;
        .tender-info-amount-custom-wrapper {
          display: block;
        }
      }
    }
  }
  &-collapse-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 10px;
    &-item {
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      color: #2e73f3;
      padding: 10px 0;
      cursor: pointer;
      i {
        margin-left: 5px;
      }
    }
  }
  &-item {
    padding: 20px;
    border-bottom: 1px solid #ededed;
    transition: background-color 0.35s cubic-bezier(0.4, 0, 0.2, 1);
    &-head {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 10px;
    }
    &-title {
      font-size: 14px;
      color: #333;
      font-weight: 500;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      cursor: pointer;
      &:hover {
        color: #2e73f3;
      }
    }
    &-time {
      font-size: 12px;
      color: #999;
      padding-left: 20px;
    }
    &-foot {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    &-tag {
      display: flex;
      align-items: center;
      font-size: 12px;
      line-height: 26px;
      color: #999;
      flex: 1;
      overflow: hidden;
      span.tag-item {
        padding: 0 15px;
        border-radius: 5px;
        background-color: #f5f5f5;
        margin-right: 10px;
        transition: background-color 0.35s cubic-bezier(0.4, 0, 0.2, 1);
        flex-shrink: 0;
      }
      .tag-comment {
        display: flex;
        align-items: center;
        font-size: 12px;
        line-height: 26px;
        color: #999;
        flex: 1;
        overflow: hidden;
        &-title {
          color: #999;
          flex-shrink: 0;
        }
        &-info {
          color: #f35d09;
          flex-shrink: 0;
          margin-right: 15px;
          &.is-ellipsis {
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            margin-right: 30px;
          }
        }
      }
    }
    &-collect {
      display: flex;
      align-items: center;
      font-size: 14px;
      color: #666;
      cursor: pointer;
      transition: color 0.35s cubic-bezier(0.4, 0, 0.2, 1);
      i {
        font-size: 20px;
        margin-right: 5px;
      }
      &:hover {
        color: #2e73f3;
      }
      &.is-collected {
        i {
          font-size: 25px;
          color: #2e73f3;
        }
      }
    }
    &:last-child {
      border-bottom: none;
    }
    &:hover {
      background-color: #f0f4f9;
      .tag-item {
        background-color: #fafafa;
      }
    }
  }
  &-ranking {
    width: 1200px;
    margin-top: 20px;
    &-item {
      display: flex;
      align-items: center;
      padding: 10px 20px;
      background-color: #fff;
      border-radius: 5px;
      box-shadow: 0px 1px 9px 0px rgba(0, 0, 0, 0.07);
      margin-bottom: 10px;
      border: 1px solid #fff;
      transition: all 0.35s;
      &-number {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 38px;
        height: 38px;
        border: 1px solid #eaedf2;
        background: #f5f5f5;
        font-weight: 500;
        font-size: 16px;
        color: #999999;
        border-radius: 50%;
        margin-right: 30px;
        &.item-number-1 {
          background: url('~@/assets/images/statistics-icon-1.png') no-repeat center center #fff8d8;
          background-size: 26px 26px;
          border-color: #ffedbb;
          text-indent: -9999px;
        }
        &.item-number-2 {
          background: url('~@/assets/images/statistics-icon-2.png') no-repeat center center #eaf0ff;
          background-size: 26px 26px;
          border-color: #dfe4f5;
          text-indent: -9999px;
        }
        &.item-number-3 {
          background: url('~@/assets/images/statistics-icon-3.png') no-repeat center center #fff7f0;
          background-size: 26px 26px;
          border-color: #f7efe7;
          text-indent: -9999px;
        }
      }
      &-info {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: space-between;
      }
      &-title {
        flex: 1;
        font-weight: 500;
        font-size: 14px;
        color: #333333;
        line-height: 20px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        cursor: pointer;
        &:hover {
          color: #2e73f3;
        }
      }
      &-foot {
        display: flex;
        align-items: center;
        justify-content: space-between;
      }
      &-tag {
        display: flex;
        align-items: center;
        font-size: 12px;
        line-height: 26px;
        color: #999;
        span.tag-item {
          padding: 0 15px;
          border-radius: 5px;
          background-color: #f5f5f5;
          margin-right: 10px;
          transition: background-color 0.35s cubic-bezier(0.4, 0, 0.2, 1);
        }
      }
      &-desc {
        flex-shrink: 0;
        display: flex;
        align-items: center;
        font-size: 12px;
        line-height: 20px;
        .text {
          color: #666666;
        }
        .amount {
          color: #ff6d00;
          margin-right: 50px;
          font-weight: 500;
        }
        .count {
          color: #2e73f3;
          font-weight: 500;
          cursor: pointer;
        }
      }
      &:hover {
        background-color: #f6f9ff;
        border-color: #2e73f3;
        .tag-item {
          background-color: #ffffff;
        }
      }
    }
  }
  &-pagination {
    width: 1200px;
    .pagination-container {
      background-color: transparent;
      height: 45px;
      ::v-deep .el-pagination {
        width: 100%;
        text-align: right;
        .el-pagination__total {
          float: left;
        }
      }
    }
  }
  &-padding {
    padding: 20px;
  }
  &-detail {
    &-tip {
      background-color: #ffefe5;
      color: #f35d09;
      display: flex;
      width: 100%;
      height: 65px;
      font-size: 14px;
      align-items: center;
      justify-content: center;
      gap: 5px;
    }
    &-breadcrumb {
      display: flex;
      align-items: center;
      font-size: 12px;
      width: 1200px;
      .breadcrumb-item {
        color: #999;
        cursor: pointer;
        &:hover {
          color: #2e73f3;
          &::before {
            color: #999;
          }
        }
        &:last-child {
          color: #2e73f3;
        }
      }
      .breadcrumb-item + .breadcrumb-item {
        margin-left: 5px;
        &::before {
          color: #999;
          display: inline-block;
          content: '>';
          margin-right: 5px;
        }
      }
    }
    &-keyword {
      width: 1200px;
      padding: 10px 20px;
      border: 1px solid #cbd6e2;
      margin: 15px 0;
      .tip {
        display: inline-block;
        font-size: 12px;
        line-height: 20px;
        color: #999;
        margin-right: 10px;
      }
      .keyword-item {
        display: inline-block;
        font-size: 14px;
        line-height: 20px;
        color: #666;
        cursor: pointer;
        &:hover {
          color: #2e73f3;
        }
      }
      .keyword-item + .keyword-item {
        margin-left: 50px;
      }
    }
    &-title {
      font-size: 20px;
      line-height: 1;
      color: #333;
      font-weight: 500;
      margin-bottom: 15px;
    }
    &-tag {
      display: flex;
      align-items: center;
      font-size: 12px;
      line-height: 26px;
      color: #999;
      margin-bottom: 10px;
      span.tag-item {
        padding: 0 15px;
        border-radius: 5px;
        background-color: #f5f5f5;
        margin-right: 10px;
        transition: background-color 0.35s cubic-bezier(0.4, 0, 0.2, 1);
      }
    }
    &-time-collect {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 30px;
      .time {
        font-size: 12px;
        color: #666666;
        line-height: 20px;
      }
      .collect {
        display: flex;
        align-items: center;
        font-size: 14px;
        color: #666;
        cursor: pointer;
        transition: color 0.35s cubic-bezier(0.4, 0, 0.2, 1);
        i {
          font-size: 20px;
          margin-right: 5px;
        }
        &:hover {
          color: #2e73f3;
        }
        &.is-collected {
          i {
            font-size: 25px;
            color: #2e73f3;
          }
        }
      }
    }
    &-tabs {
      display: flex;
      align-items: center;
      border-bottom: 1px solid #d5dde6;
      .tabs-item {
        font-size: 12px;
        color: #666;
        cursor: pointer;
        padding: 10px 20px;
        position: relative;
        &:first-child {
          font-size: 16px;
          color: #2e73f3;
          font-weight: 500;
          &::after {
            content: '';
            display: block;
            width: 100%;
            height: 2px;
            background-color: #2e73f3;
            position: absolute;
            bottom: -1px;
            left: 0;
          }
        }
      }
    }
    &-htitle {
      font-weight: 500;
      font-size: 16px;
      color: #333333;
      line-height: 20px;
      margin-bottom: 10px;
      &.padding {
        padding-top: 20px;
      }
    }
    &-description {
      ::v-deep .el-descriptions__body {
        .el-descriptions-item__label {
          width: 120px !important;
          background-color: #f8faff;
          font-size: 12px;
          color: #666666;
          border-color: #ebedf3;
          text-align: center;
        }
        .el-descriptions-item__content {
          width: 460px;
          border-color: #ebedf3;
          font-weight: 500;
          font-size: 14px;
          line-height: 24px;
          color: #333333;
          padding-top: 20px;
          padding-bottom: 20px;
          .flex {
            display: flex;
            align-items: center;
            justify-content: space-between;
          }
          .link {
            display: inline-block;
            color: #2e73f3;
            cursor: pointer;
            &:hover {
              opacity: 0.8;
              .link-symbol {
                color: #333;
              }
            }
            .link-symbol {
              margin-left: 5px;
              color: #333;
            }
          }
        }
      }
    }
    &-content {
      font-size: 14px;
      line-height: 24px;
      color: #333;
      img,
      image {
        max-width: 100%;
        height: auto;
      }
    }
    &-pagination {
      width: 1200px;
      padding: 30px 0 45px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .pagination-box {
        flex: 1;
        overflow: hidden;
        padding-right: 20px;
        display: flex;
        flex-direction: column;
        .pagination-item {
          font-weight: 400;
          font-size: 16px;
          color: #666666;
          line-height: 30px;
          cursor: pointer;
          transition: color 0.35s cubic-bezier(0.4, 0, 0.2, 1);
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          &:hover {
            color: #2e73f3;
          }
        }
      }
      .pagination-back {
        width: 115px;
        height: 46px;
        background-color: #2e73f3;
        font-size: 16px;
        color: #ffffff;
        border-radius: 5px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: opacity 0.35s cubic-bezier(0.4, 0, 0.2, 1);
        &:hover {
          opacity: 0.8;
        }
      }
    }
  }
  &-keyword {
    &-wrapper {
      margin: 0 20px 20px;
      border: 1px solid #cbd6e2;
      border-radius: 5px;
      overflow: hidden;
      transition: all 0.35s cubic-bezier(0.4, 0, 0.2, 1);
    }
    &-common {
      margin: 0 20px 20px;
      border-radius: 5px;
      background-color: #f0f3f9;
      padding: 12px 10px;
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      &-title {
        font-size: 12px;
        line-height: 30px;
        color: #999;
        margin-right: 10px;
      }
      .tender-keyword-item {
        cursor: pointer;
      }
    }
    &-input {
      padding: 15px;
      background-color: #f0f3f9;
      display: flex;
      align-items: center;
      border-radius: 5px 5px 0 0;
      .input {
        width: 240px;
        ::v-deep {
          .el-input__inner {
            border-color: #2e73f3;
          }
          .el-input__suffix {
            right: 2px;
            display: flex;
            align-items: center;
          }
        }
      }
    }
    &-mode {
      padding-left: 30px;
      .title {
        font-size: 12px;
        color: #999999;
      }
    }
    &-list {
      border-radius: 0 0 5px 5px;
      padding: 15px;
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
    }
    &-item {
      position: relative;
      padding: 0 15px;
      line-height: 30px;
      background-color: #2e73f3;
      border-radius: 5px;
      .text {
        display: inline-block;
        font-weight: 500;
        font-size: 14px;
        color: #ffffff;
      }
      .icon {
        display: none;
        align-items: center;
        justify-content: center;
        width: 16px;
        height: 16px;
        line-height: 16px;
        border-radius: 50%;
        background-color: rgba(0, 0, 0, 0.8);
        color: #ffffff;
        font-size: 10px;
        position: absolute;
        top: -5px;
        right: -5px;
        cursor: pointer;
        transition: opacity 0.35s cubic-bezier(0.4, 0, 0.2, 1);
        &:hover {
          opacity: 0.8;
        }
      }
      &:hover {
        .icon {
          display: flex;
        }
      }
    }
    &-edit {
      &-title {
        display: flex;
        align-items: center;
        padding-left: 20px;
        font-size: 14px;
        line-height: 20px;
        color: #666;
        border-left: 1px solid #cbd6e2;
        cursor: pointer;
        transition: color 0.35s cubic-bezier(0.4, 0, 0.2, 1);
        i {
          font-size: 20px;
          margin-right: 5px;
        }
        &:hover {
          color: #2e73f3;
        }
      }
      &-list {
        display: flex;
        flex-direction: column;
        .title {
          background-color: #f2f4f6;
          padding: 6px 20px;
          font-size: 14px;
          line-height: 20px;
          color: #666;
          border-radius: 10px 10px 0 0;
        }
        .box {
          max-height: 300px;
          overflow-y: auto;
        }
        .item {
          padding: 0 20px;
          font-size: 12px;
          color: #666666;
          line-height: 30px;
          border-bottom: 1px solid #f2f4f6;
          &:hover {
            background-color: #f2f4f6;
          }
          &:last-child {
            border-bottom: none;
          }
        }
        .plus {
          display: flex;
          align-items: center;
          padding: 0 20px;
          font-size: 12px;
          line-height: 40px;
          color: #2e73f3;
          cursor: pointer;
          transition: color 0.35s cubic-bezier(0.4, 0, 0.2, 1);
          i {
            font-size: 16px;
            margin-right: 5px;
          }
        }
      }
    }
  }
}

.expirationTime {
  width: 1200px;
  height: 70px;
  background-color: #ff8d4d;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  position: relative;
  margin: 0 auto 20px;
  color: $white;
  padding: 0 50px;
  &-close {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    right: 15px;
    width: 24px;
    height: 24px;
    cursor: pointer;
    i {
      font-size: 24px;
      color: #a65c32;
    }
  }
  &-title {
    display: inline-flex;
    align-items: center;
    font-size: 20px;
    margin-right: 50px;
    b {
      display: inline-block;
      width: 50px;
      line-height: 50px;
      border-radius: 5px;
      border: 1px solid #cbd7e2;
      font-size: 32px;
      color: #f35d09;
      text-align: center;
      background-color: $white;
      margin: 0 8px;
    }
  }
  &-tip {
    font-size: 14px;
    font-weight: 500;
    margin-right: 15px;
  }
  &-desc {
    font-size: 14px;
    opacity: 0.9;
  }
}
.tenderExpire {
  display: flex;
  width: 100%;
  flex-direction: column;
  align-items: center;
  padding: 10px 30px;
  &-icon {
    font-size: 36px;
    color: #ed4040;
  }
  &-title {
    font-size: 14px;
    line-height: 20px;
    font-weight: 500;
    color: #333333;
    margin: 20px 0 10px;
  }
  &-desc {
    font-size: 12px;
    line-height: 20px;
    color: #2e73f3;
    margin-bottom: 45px;
  }
  &-button {
    width: 270px;
    height: 50px;
    line-height: 50px;
    text-align: center;
    border-radius: 5px;
    background-color: #2e73f3;
    color: #ffffff;
    cursor: pointer;
  }
}
.tender-detail-comment {
  width: 1200px;
  .comment-title {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 20px;
    color: #333333;
    &-tip {
      font-size: 12px;
      color: #999999;
      margin-left: 10px;
      font-weight: normal;
    }
  }
  .comment-con {
    width: 100%;
    box-sizing: border-box;
    &-answer {
      width: 100%;
      display: flex;
      align-items: flex-start;
      position: relative;
      margin: 10px 0;
      background: #f8f9fa;
      border-radius: 8px;
      &-img {
        width: 50px;
        height: 50px;
        margin-right: 15px;
        border-radius: 50%;
        border: 1px solid #c5cad9;
        object-fit: cover;
      }
      &-input {
        width: 100%;
        display: flex;
        flex-direction: column;
        .comment-tabs {
          display: flex;
          align-items: flex-end;
          background: transparent;
          .tab-item {
            padding: 0 20px;
            height: 38px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            color: #666666;
            background: #f2f3f9;
            cursor: pointer;
            border-radius: 6px 6px 0 0;
            position: relative;
            &:hover {
              background: #e8e9f0;
              color: #666666;
            }
            &.active {
              background: #ffffff;
              color: #333333;
              height: 46px;
              border: 1px solid #c5cad9;
              border-bottom: 1px solid #ffffff;
              z-index: 2;
              margin-bottom: -1px;
              &:hover {
                background: #ffffff;
                color: #333333;
              }
            }
          }
        }
        ::v-deep {
          .el-textarea__inner {
            font-family: inherit;
            border: 1px solid #c5cad9;
            padding: 10px;
            height: 120px;
            resize: none;
          }
        }
        &:not(.reply-input) {
          ::v-deep .el-textarea__inner {
            border-top-left-radius: 0;
          }
        }
      }
      &-button {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 50px;
        border: 1px solid #c5cad9;
        border-top: none;
        border-radius: 0 0 8px 8px;
        padding: 0 20px;
        background-color: #ffffff;
        .left-section {
          display: flex;
          align-items: center;
          gap: 15px;
        }
        .quick-reply-widget {
          position: relative;
          .quick-reply-trigger {
            font-size: 18px;
            cursor: pointer;
            transition: all 0.3s;
            padding: 4px;
            &:hover {
              color: #2e73f3 !important;
            }
          }
          .quick-reply-panel {
            position: absolute;
            top: 100%;
            left: 0;
            margin-top: 8px;
            background: #f8f8f8;
            border: 1px solid #2e73f3;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 1000;
            width: 450px;
            .panel-header {
              display: flex;
              align-items: center;
              justify-content: space-between;
              padding: 12px 16px;
              border-bottom: 1px solid #f0f0f0;
              .header-left {
                display: flex;
                align-items: center;
                gap: 12px;
                .panel-title {
                  font-size: 14px;
                  color: #999999;
                  font-weight: 500;
                }
                .header-actions {
                  display: flex;
                  align-items: center;
                  .edit-btn {
                    display: flex;
                    align-items: center;
                    gap: 4px;
                    cursor: pointer;
                    color: #999999;
                    font-size: 14px;
                    padding: 4px 8px;
                    border-radius: 4px;
                    transition: all 0.3s;

                    i {
                      font-size: 14px;
                    }

                    span {
                      font-size: 14px;
                    }

                    &:hover {
                      color: #2e73f3;
                    }

                    &.active {
                      color: #2e73f3;
                    }
                  }
                }
              }
              .header-right {
                .add-btn {
                  display: flex;
                  align-items: center;
                  gap: 4px;
                  cursor: pointer;
                  color: #999999;
                  font-size: 14px;
                  padding: 4px 8px;
                  border-radius: 4px;
                  transition: all 0.3s;
                  i {
                    font-size: 14px;
                  }
                  span {
                    font-size: 14px;
                  }
                  &:hover {
                    color: #2e73f3;
                  }
                }
              }
            }
            .reply-tags {
              padding: 12px 16px;
              display: flex;
              flex-wrap: wrap;
              gap: 8px;
              .reply-tag {
                display: inline-flex;
                align-items: center;
                padding: 6px 12px;
                background: #e5e5e5;
                border-radius: 4px;
                cursor: pointer;
                font-size: 13px;
                color: #333333;
                transition: all 0.3s;
                position: relative;
                &:hover {
                  background: #e0ebff;
                  color: #2e73f3;
                }
                .tag-text {
                  white-space: nowrap;
                  padding-right: 6px;
                }
                .delete-icon {
                  position: absolute;
                  top: -4px;
                  right: -4px;
                  font-size: 14px;
                  color: #626262;
                  width: 16px;
                  height: 16px;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  border-radius: 50%;
                  cursor: pointer;
                  transition: all 0.3s;
                  &:hover {
                    color: #2e73f3;
                  }
                }
              }
            }
            .add-reply-form {
              padding: 12px 16px;
              border-top: 1px solid #f0f0f0;
              ::v-deep .el-textarea__inner {
                border-color: #2e73f3;
                border-width: 1px;
                font-size: 13px;
                font-family: inherit;
                border-radius: 6px;
                padding: 8px 12px;
                box-shadow: 0px 1px 10px 0px rgba(0, 0, 0, 0.25);
                transition: all 0.3s;
              }
              .form-actions {
                margin-top: 12px;
                ::v-deep .el-button {
                  padding: 8px 24px;
                  font-size: 13px;
                  border-radius: 6px;
                  background-color: #2e73f3;
                  border-color: #2e73f3;
                  &:hover {
                    background-color: #1a5fdf;
                    border-color: #1a5fdf;
                  }
                }
              }
            }
          }
        }
        .button-group {
          display: flex;
          align-items: center;
          gap: 10px;
        }
        .button {
          cursor: pointer;
          font-size: 16px;
          color: #ffffff;
          line-height: 32px;
          width: 80px;
          border-radius: 22px;
          background-color: #2e73f3;
          text-align: center;
          transition: all 0.3s;
          &:hover {
            background-color: #1a5fdf;
          }
          &.disabled {
            cursor: not-allowed;
            background-color: #cbd6e2;
            &:hover {
              background-color: #cbd6e2;
            }
          }
          &.cancel-button {
            background-color: #909399;
            color: #ffffff;
            &:hover {
              background-color: #73767a;
            }
          }
        }
      }
    }
    &-w100 {
      width: 100%;
    }
    &-item {
      width: 100%;
      display: flex;
      align-items: flex-start;
      position: relative;
      padding: 15px 0;
      border-bottom: 1px solid #f0f0f0;
      &:last-child {
        border-bottom: none;
      }
      &.children {
        background-color: #f1f1f3;
        border-radius: 8px;
        margin: 10px 0;
        padding: 15px;
        border-bottom: none;
        .children {
          padding-right: 0;
        }
      }
      &-img {
        width: 50px;
        height: 50px;
        margin-right: 15px;
        border-radius: 50%;
        border: 1px solid #c5cad9;
        object-fit: cover;
      }
      &-con {
        width: 100%;
        display: flex;
        flex-direction: column;
        &-title {
          font-size: 16px;
          font-weight: 600;
          color: #333333;
          margin-bottom: 8px;
        }
        &-con {
          font-size: 14px;
          line-height: 1.6;
          color: #666666;
          margin-bottom: 10px;
          word-break: break-word;
        }
        &-time {
          display: flex;
          align-items: center;
          justify-content: space-between;
          font-size: 12px;
          color: #999999;
          .anwser {
            margin-left: 15px;
            cursor: pointer;
            color: #666666;
            transition: color 0.3s;
            span {
              margin-left: 5px;
            }
            &:hover {
              color: #2e73f3;
            }
          }
        }
        &-open {
          font-size: 14px;
          color: #666666;
          cursor: pointer;
          margin-top: 10px;
          transition: color 0.3s;
          &:hover {
            color: #333333;
          }
        }
      }
    }
  }
  .inline-flex {
    display: inline-flex;
    align-items: center;
  }
  .dept-name {
    color: #999999;
    font-size: 12px;
    font-weight: normal;
    margin-left: 15px;
  }
  .objective-name {
    font-size: 12px;
    color: #333;
    font-weight: normal;
    background: #e5e5e5;
    padding: 5px 15px;
    border-radius: 5px;
    margin-left: 25px;
    position: relative;
    &:before {
      content: '';
      display: inline-block;
      border-width: 8px 8px 8px 0;
      border-style: solid;
      border-color: transparent #e5e5e5 transparent transparent;
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      left: -8px;
    }
  }
  // 复选框样式
  .comment-checkboxes {
    background: #fff;
    border: 1px solid #c5cad9;
    border-bottom: 0;
    border-radius: 5px 5px 0 0;
    padding: 20px 15px;
    ::v-deep .el-checkbox.is-disabled.is-checked .el-checkbox__inner {
      background-color: #2e73f3;
      border-color: #2e73f3;
      &:after {
        border-color: #fff;
      }
    }
  }
}
