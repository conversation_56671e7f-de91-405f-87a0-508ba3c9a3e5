@import '~@/assets/chat/iconfont.css';
$blue: #2e73f3;
$pinkRed: #ec2454;
$red: #f43f3f;
$white: white;
$black: #333333;
$gray: #666666;
$disabled: #999999;
.chatIcon {
  font-size: 22px;
}
.chatDialog {
  background-color: $white;
  width: 920px;
  height: 88vh;
  min-height: 600px;
  margin: 0 auto;
  border: 1px solid $blue;
  box-shadow: 0 0 9px 0 rgba(0, 0, 0, 0.32);
  border-radius: 10px;
  overflow: hidden;
  display: flex;
  &Left {
    display: flex;
    flex-direction: column;
    width: 25%;
    height: 100%;
    background-color: #f2f4f8;
    flex-shrink: 0;
    .leftTabs {
      flex-shrink: 0;
      height: 38px;
      display: flex;
      align-items: center;
      background-color: $white;
      box-shadow: 0 1px 0 0 rgba(0, 0, 0, 0.12);
      ::v-deep {
        .el-badge__content.is-fixed {
          top: 10px;
          right: 20px;
        }
      }
      &Item {
        line-height: 35px;
        margin: 0 15px;
        font-size: 14px;
        cursor: pointer;
        height: 100%;
        display: flex;
        background: transparent;
        border: 0;
        border-bottom: 3px solid transparent;
        &:hover,
        &.active {
          font-size: 14px;
          font-weight: 500;
          color: $blue;
          border-bottom-color: $blue;
        }
      }
    }
    .leftSearch {
      display: flex;
      flex-shrink: 0;
      padding: 10px 15px;
      ::v-deep {
        .input-suffix {
          height: 100%;
          display: inline-flex;
          align-items: center;
          color: #cecece;
          font-size: 18px;
          cursor: pointer;
        }
      }
      &.mySearch {
        background-color: #cbd3e2;
        align-items: center;
        .mySearchTabs {
          flex-shrink: 0;
          display: flex;
          align-items: center;
          height: 28px;
          margin-right: 15px;
          border-radius: 5px;
          overflow: hidden;
          .mySearchTabsItem {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 30px;
            height: 100%;
            font-size: 14px;
            cursor: pointer;
            border: 1px solid $blue;
            color: $blue;
            transition: all 0.3s;
            &:first-child {
              border-radius: 5px 0 0 5px;
            }
            &:last-child {
              border-radius: 0 5px 5px 0;
            }
            &:hover,
            &.active {
              background-color: $blue;
              color: $white;
            }
          }
        }
      }
    }
    .leftList {
      flex: 1;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      .leftListLetter {
        padding: 5px 10px 5px 15px;
        color: $disabled;
        box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
      }
      .leftListItem {
        display: flex;
        align-items: center;
        padding: 10px 10px 10px 15px;
        height: 60px;
        cursor: pointer;
        transition: all 0.1s;
        &:hover,
        &.active {
          background-color: #dee1eb;
        }
        .itemAvatar {
          width: 38px;
          height: 38px;
          border-radius: 5px;
          overflow: hidden;
          img {
            width: 100%;
            height: 100%;
          }
        }
        .itemContent {
          flex: 1;
          overflow: hidden;
          padding-left: 8px;
          font-size: 12px;
          &Top {
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 20px;
            &Name {
              flex: 1;
              color: $black;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
              em {
                font-style: normal;
                color: $disabled;
              }
            }
            &Time {
              color: $disabled;
              margin-left: 5px;
            }
          }
          &Bottom {
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 20px;
            &Text {
              flex: 1;
              color: $disabled;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
            }
            &Num {
              display: flex;
              align-items: center;
              justify-content: center;
            }
          }
        }
      }
    }
  }
  &Right {
    width: 75%;
    display: flex;
    flex-direction: column;
    border-left: 1px solid #dadada;
    .rightTitle {
      height: 38px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      background-color: #eceef3;
      font-size: 14px;
      color: $black;
      padding-left: 15px;
      padding-right: 15px;
      &Flex {
        display: flex;
        align-items: center;
        em {
          font-style: normal;
          color: $disabled;
        }
      }
      i {
        font-size: 18px;
        cursor: pointer;
        transition: all 0.5s;
        &:hover {
          transform: rotate(360deg);
        }
      }
      .clean {
        font-size: 16px;
        cursor: pointer;
        margin-right: 10px;
        &:hover {
          color: $blue;
        }
      }
    }
    .rightInfo {
      width: 100%;
      display: flex;
      padding: 10px 10px 10px 0;
      height: calc(100% - 38px);
      &.right0 {
        padding-right: 0;
      }
      .infoLeft {
        width: calc(100% - 160px);
        flex: 1;
        display: flex;
        flex-direction: column;
        &List {
          display: flex;
          height: 80%;
          padding: 0 0 10px 10px;
          &.system {
            height: 100%;
            padding-bottom: 0;
          }
        }
        .friend {
          &Item {
            float: left;
            width: calc(100% - 20px);
            padding: 20px 30px;
            background-color: #eef1f6;
            border-radius: 10px;
            display: flex;
            justify-content: space-between;
            margin: 5px 10px;
            &.cloumn {
              flex-direction: column;
            }
            &Flex {
              display: flex;
              align-items: center;
              justify-content: flex-start;
            }
            &Img {
              width: 50px;
              height: 50px;
              overflow: hidden;
              img {
                width: 100%;
                height: 100%;
              }
              &.round {
                border-radius: 50%;
              }
            }
            &Name {
              font-size: 14px;
              font-weight: 500;
              color: $black;
              margin: 0 15px;
              min-width: 130px;
            }
            &Audit {
              width: 80px;
              line-height: 30px;
              text-align: center;
              border-radius: 5px;
              border-width: 1px;
              border-style: solid;
              cursor: pointer;
              &.error {
                background-color: #f5f5f5;
                border-color: $red;
                color: $red;
                &:hover {
                  border-color: #e7e7e7;
                  color: $disabled;
                }
              }
              &.primary {
                border-color: transparent;
                background: linear-gradient(107deg, #39a8f9 0%, #2787f8 100%);
                color: $white;
              }
              &:hover {
                opacity: 0.8;
              }
            }
            .friendItemAudit + .friendItemAudit {
              margin-left: 15px;
            }
            &Plus {
              display: flex;
              align-items: center;
              justify-content: center;
              width: 80px;
              height: 30px;
              text-align: center;
              border-radius: 15px;
              border-width: 1px;
              border-style: solid;
              cursor: pointer;
              font-size: 10px;
              &.error {
                background-color: #f5f5f5;
                border-color: $red;
                color: $red;
                &:hover {
                  border-color: #e7e7e7;
                  color: $disabled;
                }
              }
              &.primary {
                border-color: $blue;
                background-color: $blue;
                color: $white;
              }
              &:hover {
                opacity: 0.8;
              }
              i {
                margin-right: 5px;
              }
            }
            &Info {
              display: flex;
              align-items: center;
              border-top: 1px solid #d6dbe3;
              border-bottom: 1px solid #d6dbe3;
              font-size: 12px;
              line-height: 20px;
              padding: 15px 0;
              margin-top: 15px;
              span {
                color: $gray;
              }
              b {
                font-weight: 500;
                color: $black;
              }
            }
            &Send {
              margin-top: 15px;
              cursor: pointer;
              width: 110px;
              height: 32px;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 14px;
              background: linear-gradient(107deg, #39a8f9 0%, #2787f8 100%);
              color: $white;
              border-radius: 5px;
              opacity: 0.8;
              &:hover {
                opacity: 1;
              }
              i {
                margin-right: 5px;
              }
            }
          }
        }
        .system {
          &Item {
            cursor: pointer;
            float: left;
            width: calc(100% - 20px);
            margin: 8px 15px 8px 5px;
            padding: 13px 20px;
            box-shadow: 0 0 9px 0 rgba(0, 0, 0, 0.11);
            border-radius: 5px;
            &:hover {
              box-shadow: 0 0 9px 0 rgba(0, 0, 0, 0.22);
            }
            &Flex {
              flex: 1;
              overflow: hidden;
              display: flex;
              align-items: center;
              justify-content: flex-start;
              &.start {
                align-items: flex-start;
                justify-content: space-between;
              }
            }
            &Top {
              height: 20px;
              display: flex;
              align-items: center;
              justify-content: space-between;
              margin-bottom: 5px;
              &Title {
                font-size: 14px;
                color: $black;
                align-items: center;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
              }
              &Tip {
                flex-shrink: 0;
                font-size: 10px;
                height: 16px;
                padding-left: 5px;
                padding-right: 10px;
                color: $white;
                margin-left: 20px;
                position: relative;
                border-radius: 0 3px 3px 0;
                background-color: #cccccc;
                &:before {
                  position: absolute;
                  left: -16px;
                  content: '';
                  width: 0;
                  height: 0;
                  border-width: 8px;
                  border-style: solid;
                  border-color: transparent #cccccc transparent transparent;
                }
                &.noRead {
                  background-color: $red;
                  &:before {
                    border-color: transparent $red transparent transparent;
                  }
                }
              }
              &Time {
                margin-left: 20px;
                font-size: 12px;
                color: $disabled;
              }
            }
            ::v-deep {
              .systemItem {
                &Info {
                  font-size: 12px;
                  line-height: 20px;
                  color: $disabled;
                  max-height: 60px;
                  a {
                    color: $blue;
                  }
                }
                &Img {
                  width: 60px;
                  height: 60px;
                  flex: 0 0 60px;
                  margin-left: 10px;
                  overflow: hidden;
                  border-radius: 3px;
                  img {
                    width: 100%;
                    height: 100%;
                  }
                }
                &Promotion {
                  display: flex;
                  flex-direction: column;
                  align-items: center;
                  margin: 5px -20px -15px;
                  background-color: #f8f9fb;
                  &Item {
                    width: 100%;
                    display: flex;
                    padding: 15px 20px;
                    transition: all 0.3s;
                    &:hover {
                      box-shadow: 0 0 9px 0 rgba(0, 0, 0, 0.11);
                    }
                  }
                  &Img {
                    width: 50px;
                    height: 50px;
                    border: 1px solid #d9d9d9;
                    border-radius: 5px;
                    flex: 0 0 50px;
                    margin-right: 15px;
                    overflow: hidden;
                    img {
                      width: 100%;
                      height: 100%;
                    }
                  }
                  &Flex {
                    display: flex;
                    flex-direction: column;
                    flex: 1;
                    overflow: hidden;
                  }
                  &Title {
                    font-size: 14px;
                    line-height: 20px;
                    margin-bottom: 8px;
                    color: $gray;
                    font-weight: 500;
                  }
                  &Info {
                    font-size: 12px;
                    line-height: 20px;
                    color: $gray;
                    span {
                      color: $disabled;
                      margin-left: 30px;
                      margin-right: 10px;
                      &:first-child {
                        margin-left: 0;
                      }
                    }
                    b {
                      font-weight: 500;
                      &.price {
                        color: $red;
                      }
                    }
                  }
                  &More {
                    display: flex;
                    justify-content: center;
                    font-size: 12px;
                    line-height: 20px;
                    color: $disabled;
                    cursor: pointer;
                    margin: 10px 0;
                    &.down:after {
                      content: '∨';
                      font-size: 20px;
                      display: inline-block;
                    }
                    &.up:after {
                      content: '∧';
                      font-size: 20px;
                      display: inline-block;
                    }
                    &:hover {
                      color: $blue;
                    }
                  }
                }
              }
            }
          }
        }
        &Item {
          width: 100%;
          float: left;
          display: flex;
          flex-direction: column;
          margin-bottom: 10px;
          .itemTime {
            text-align: center;
            color: #a3a3a3;
            font-size: 12px;
            line-height: 22px;
          }
          .itemFlex {
            display: flex;
            align-items: flex-start;
            justify-content: flex-start;
            &Img {
              width: 50px;
              height: 50px;
              flex: 0 0 50px;
              margin-right: 10px;
              border-radius: 50%;
              border: 1px solid #dadada;
              overflow: hidden;
              img {
                width: 100%;
                height: 100%;
              }
            }
            &Info {
              display: flex;
              align-items: flex-start;
              flex-direction: column;
              max-width: 70%;
            }
            &Name {
              font-size: 12px;
              line-height: 20px;
              margin: 6px 0;
              color: $gray;
            }
            ::v-deep {
              .itemFlex {
                &Msg {
                  font-size: 12px;
                  line-height: 18px;
                  padding: 6px 10px;
                  color: $black;
                  background: #f2f4f8;
                  border-radius: 0 10px 10px 10px;
                  a {
                    color: $blue;
                  }
                }
                &Product {
                  cursor: pointer;
                  display: flex;
                  padding: 10px 15px;
                  background: #f2f4f8;
                  border-radius: 0 10px 10px 10px;
                  transition: all 0.3s;
                  &:hover {
                    box-shadow: 0 0 9px 0 rgba(0, 0, 0, 0.11);
                  }
                  .productImg {
                    width: 60px;
                    height: 60px;
                    border: 1px solid #dadada;
                    border-radius: 5px;
                    overflow: hidden;
                    flex: 0 0 60px;
                    margin-top: 4px;
                    margin-right: 15px;
                    img {
                      width: 100%;
                      height: 100%;
                    }
                  }
                  .productInfo {
                    flex: 1;
                    display: flex;
                    flex-direction: column;
                    justify-content: space-between;
                    font-size: 12px;
                    line-height: 18px;
                    overflow: hidden;
                    .productName {
                      color: $black;
                      overflow: hidden;
                      white-space: nowrap;
                      text-overflow: ellipsis;
                      margin-bottom: 4px;
                    }
                    .productItem {
                      margin-top: 4px;
                      span {
                        color: $gray;
                      }
                      b {
                        color: $black;
                        font-weight: normal;
                      }
                    }
                  }
                }
                &Card {
                  cursor: pointer;
                  display: flex;
                  flex-direction: column;
                  padding: 10px 15px;
                  background: #f2f4f8;
                  border-radius: 0 10px 10px 10px;
                  position: relative;
                  overflow: hidden;
                  font-size: 12px;
                  line-height: 18px;
                  min-width: 200px;
                  transition: all 0.3s;
                  &:hover {
                    box-shadow: 0 0 9px 0 rgba(0, 0, 0, 0.11);
                  }
                  .cardTip {
                    padding: 0 5px;
                    color: $white;
                    background: linear-gradient(270deg, #2e73f3 0%, #9fc1ff 100%);
                    border-radius: 5px 0 10px 0;
                    position: absolute;
                    right: 0;
                    bottom: 0;
                    line-height: 20px;
                  }
                  .cardTitle {
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    margin-bottom: 4px;
                    span {
                      color: $gray;
                    }
                    b {
                      color: $black;
                      font-weight: normal;
                    }
                  }
                  .cardItem {
                    margin-top: 4px;
                    span {
                      color: $gray;
                    }
                    b {
                      color: $black;
                      font-weight: normal;
                      &.price {
                        color: $red;
                      }
                    }
                  }
                }
                &Imgfile {
                  cursor: pointer;
                  display: flex;
                  justify-content: flex-end;
                  img {
                    width: auto;
                    max-width: 90%;
                    height: auto;
                    max-height: 150px;
                    border-radius: 5px;
                    transition: all 0.3s;
                    &:hover {
                      box-shadow: 0 0 9px 0 rgba(0, 0, 0, 0.5);
                    }
                  }
                }
                &System {
                  margin-top: 5px;
                  padding: 6px 10px;
                  color: $black;
                  background: #f2f4f8;
                  border-radius: 0 10px 10px 10px;
                  cursor: pointer;
                  transition: all 0.3s;
                  &:hover {
                    box-shadow: 0 0 9px 0 rgba(0, 0, 0, 0.11);
                  }
                  .systemItemTitle {
                    font-size: 14px;
                    line-height: 30px;
                    color: $black;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                  }
                  .systemItemFlex {
                    display: flex;
                    align-items: flex-start;
                  }
                  .systemItemInfo {
                    font-size: 12px;
                    line-height: 20px;
                    color: $disabled;
                    max-height: 60px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    -webkit-line-clamp: 3;
                    -webkit-box-orient: vertical;
                  }
                  .systemItemImg {
                    width: 60px;
                    height: 60px;
                    flex: 0 0 60px;
                    margin-left: 10px;
                    overflow: hidden;
                    border-radius: 3px;
                    img {
                      width: 100%;
                      height: 100%;
                    }
                  }
                }
              }
            }
          }
          &.self {
            .itemFlex {
              justify-content: flex-end;
              &Img {
                margin-left: 10px;
              }
              &Info {
                margin-top: 6px;
                align-items: flex-end;
              }
              &Name {
                text-align: right;
              }
              ::v-deep {
                .itemFlex {
                  &Msg {
                    border-radius: 10px 0 10px 10px;
                  }
                  &Product {
                    border-radius: 10px 0 10px 10px;
                  }
                  &Card {
                    border-radius: 10px 0 10px 10px;
                  }
                  &System {
                    border-radius: 10px 0 10px 10px;
                  }
                }
              }
            }
          }
        }
        &Input {
          flex-shrink: 0;
          display: flex;
          flex-direction: column;
          height: 20%;
          border-top: 1px solid #dadada;
          .input-textarea {
            display: flex;
            height: calc(100% - 32px);
            ::v-deep {
              .el-textarea__inner {
                border: 0;
                font-family: inherit;
                padding-right: 0;
                padding-left: 10px;
              }
            }
          }
          .inputButton {
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding-left: 10px;
            &Flex {
              display: flex;
              align-items: center;
              .inputButtonIcon + .inputButtonIcon {
                margin-left: 20px;
              }
            }
            &Submit {
              display: flex;
              align-items: center;
              justify-content: center;
              background: linear-gradient(107deg, #39a8f9 0%, #2787f8 100%);
              border-radius: 5px;
              width: 60px;
              height: 32px;
              cursor: pointer;
              color: $white;
              border: 0;
              &:hover {
                opacity: 0.8;
              }
              &.disabled {
                background: #f5f5f5;
                color: $disabled;
                cursor: not-allowed;
              }
            }
          }
        }
      }
      .infoRight {
        flex: 0 0 150px;
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 150px;
        padding: 20px 20px 0;
        background-color: #eceef3;
        border-radius: 10px;
        margin-left: 10px;
        &Img {
          width: 80px;
          height: 80px;
          border-radius: 50%;
        }
        &Title {
          width: 100%;
          font-weight: 500;
          font-size: 12px;
          margin-top: 5px;
          margin-bottom: 10px;
          line-height: 20px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          text-align: center;
          color: $gray;
        }
        &Btn {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 72px;
          height: 20px;
          font-size: 10px;
          border-radius: 10px;
          cursor: pointer;
          &.primary {
            border: 1px solid $blue;
            color: $blue;
            background-color: $white;
            &:hover {
              background-color: $blue;
              color: $white;
            }
          }
          &.danger {
            border: 1px solid $pinkRed;
            color: $pinkRed;
            background-color: $white;
            &:hover {
              background-color: #f5f5f5;
              border-color: #f5f5f5;
              color: $disabled;
            }
          }
        }
      }
    }
  }
  &Empty {
    display: flex;
    flex-direction: column;
    width: 75%;
    height: 100%;
    color: #bec9d2;
    align-items: center;
    justify-content: center;
    border-left: 1px solid #dadada;
  }
  .chatDialogRight .rightInfo .infoLeftItem.self .itemFlex .itemFlexInfo.sendProducts {
    border-radius: 10px 0 10px 10px;
  }

  // 翻译相关样式
  .translation-container {
    margin-top: 8px;
    padding: 6px 10px;
    background-color: #f8f9fa;
    border-radius: 6px;
    border-left: 3px solid #2e73f3;
    font-size: 12px;

    .translation-loading {
      display: flex;
      align-items: center;
      color: #666;

      i {
        margin-right: 5px;
        animation: rotating 1s linear infinite;
      }

      span {
        font-size: 12px;
      }
    }

    .translation-result {
      .translation-text {
        color: #333;
        line-height: 1.4;
        word-break: break-word;
      }
    }

    .translation-error {
      color: #f56c6c;
      font-size: 12px;
    }
  }

  // 自己发送的消息的翻译样式
  .infoLeftItem.self .translation-container {
    background-color: #e8f4fd;
    border-left-color: #409eff;
  }

  @keyframes rotating {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
  .sendProducts {
    cursor: pointer;
    display: flex;
    flex-direction: column;
    padding: 10px 15px;
    background: #f2f4f8;
    border-radius: 0 10px 10px 10px;
    transition: all 0.3s;
    width: 100%;
    &ItemBox {
      display: flex;
      flex-direction: column;
      width: calc(100% + 30px);
      margin: -10px -15px 0;
    }
    &Item {
      display: flex;
      align-items: center;
      width: 100%;
      padding: 10px 15px;
      &:hover {
        box-shadow: 0 0 9px 0 rgba(0, 0, 0, 0.11);
      }
    }
    &Img {
      width: 60px;
      height: 60px;
      border: 1px solid #dadada;
      border-radius: 5px;
      flex: 0 0 60px;
      margin-right: 15px;
      overflow: hidden;
    }
    &Info {
      flex: 1;
      overflow: hidden;
      display: flex;
      flex-direction: column;
    }
    &Title {
      font-size: 14px;
      margin-bottom: 10px;
      color: $black;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    &Desc {
      font-size: 12px;
      line-height: 18px;
    }
    &More {
      display: flex;
      width: 100%;
      justify-content: center;
      font-size: 12px;
      line-height: 20px;
      color: $disabled;
      cursor: pointer;
      margin-top: 5px;
      &.down:after {
        content: '∨';
        font-size: 20px;
        display: inline-block;
      }
      &.up:after {
        content: '∧';
        font-size: 20px;
        display: inline-block;
      }
      &:hover {
        color: $blue;
      }
    }
  }
}
