@font-face {
  font-family: "chatfont"; /* Project id 4576273 */
  src: url('iconfont.woff2?t=1717645516214') format('woff2'),
       url('iconfont.woff?t=1717645516214') format('woff'),
       url('iconfont.ttf?t=1717645516214') format('truetype');
}

.chatfont {
  font-family: "chatfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.chat-tupian:before {
  content: "\e601";
}

.chat-biaoqing:before {
  content: "\e604";
}

.chat-tongxunlu:before {
  content: "\e7de";
}

.chat-liaotian:before {
  content: "\e605";
}

.chat-pingtai:before {
  content: "\e606";
}

.chat-chanpin:before {
  content: "\e607";
}

.chat-baojia:before {
  content: "\e600";
}

.chat-hetong:before {
  content: "\e603";
}

