import Vue from 'vue'
import Cookies from 'js-cookie'
import Bai<PERSON>M<PERSON> from 'vue-baidu-map';

import VueResource from 'vue-resource';

import vueEsign from 'vue-esign'

import Element from 'element-ui'
import './assets/styles/element-variables.scss'
import formCreate from '@form-create/element-ui'

import '@/assets/styles/index.scss' // global css
import '@/assets/styles/ruoyi.scss' // ruoyi css
import App from './App'
import store from './store'
import router from './router'
import directive from './directive' // directive
import plugins from './plugins' // plugins
import { download } from '@/utils/request'

import './assets/icons' // icon
import './permission' // permission control

import SlideVerify from 'vue-monoplasty-slide-verify';
Vue.use(SlideVerify);

Vue.use(VueResource);

import htmlToPdf from "@/utils/htmlToPdf";
Vue.use(htmlToPdf);

import { getDicts } from "@/api/system/dict/data";
import { getConfigKey } from "@/api/system/config";
import { parseTime, resetForm, addDateRange, selectDictLabel, selectDictLabels, handleTree } from "@/utils/ruoyi";
import { getOssImgUrl } from "@/utils";
// 分页组件
import Pagination from "@/components/Pagination";
// 自定义表格工具组件
import RightToolbar from "@/components/RightToolbar"
// 富文本组件
import Editor from "@/components/Editor"
// 文件上传组件
import FileUpload from "@/components/FileUpload"
// 图片上传组件
import ImageUpload from "@/components/ImageUpload"
// 图片预览组件
import ImagePreview from "@/components/ImagePreview"
// 字典标签组件
import DictTag from '@/components/DictTag'
// 头部标签组件
import VueMeta from 'vue-meta'
// 字典数据组件
import DictData from '@/components/DictData'
// PDF预览组件
import Pdf from 'vue-pdf'
// 更新组件
import Update from '@/components/Update'
// dialog弹框头部
import DialogHeader from "@/components/DialogHeader";

import 'element-ui/lib/theme-chalk/index.css'
// table滚动加载
import elTableInfiniteScroll from 'el-table-infinite-scroll'
// 时间倒计时
import Countdown from '@/components/countDown'
// 全局翻译
import GlobalTranslate from '@/components/GlobalTranslate'

// 全局方法挂载
Vue.prototype.getDicts = getDicts
Vue.prototype.getConfigKey = getConfigKey
Vue.prototype.parseTime = parseTime
Vue.prototype.resetForm = resetForm
Vue.prototype.addDateRange = addDateRange
Vue.prototype.selectDictLabel = selectDictLabel
Vue.prototype.selectDictLabels = selectDictLabels
Vue.prototype.download = download
Vue.prototype.handleTree = handleTree
Vue.prototype.getOssImgUrl = getOssImgUrl
Vue.prototype.imgPath = 'https://www.ziyouke.net/prod-api'
// Vue.prototype.imgPath = 'http://192.168.3.48:8766'
Vue.prototype.formatProductImg = function (img = {}) {
  if (JSON.stringify(img) === '{}') {
    return ''
  }
  if (img.picture_oss) {
    if (img.picture_oss.includes(',')) {
      return img.picture_oss.split(',')[0]
    }
    return img.picture_oss
  }
  if (img.picture1_oss) {
    if (img.picture1_oss.includes(',')) {
      return img.picture1_oss.split(',')[0]
    }
    return img.picture1_oss
  }
  if (img.diagram_oss) {
    if (img.diagram_oss.includes(',')) {
      return img.diagram_oss.split(',')[0]
    }
    return img.diagram_oss
  }
  if (img.picture) {
    if (img.picture.includes(',')) {
      return this.imgPath + img.picture.split(',')[0]
    }
    return this.imgPath + img.picture
  }
  if (img.picture1) {
    if (img.picture1.includes(',')) {
      return this.imgPath + img.picture1.split(',')[0]
    }
    return this.imgPath + img.picture1
  }
  if (img.diagram) {
    if (img.diagram.includes(',')) {
      return this.imgPath + img.diagram.split(',')[0]
    }
    return this.imgPath + img.diagram
  }
  if (img.img_oss) {
    if (img.img_oss.includes(',')) {
      return img.img_oss.split(',')[0]
    }
    return img.img_oss
  }
  if (img.img) {
    if (img.img.includes(',')) {
      return this.imgPath + img.img.split(',')[0]
    }
    return this.imgPath + img.img
  }
  return ''
}

// import VueUeditorWrap from 'vue-ueditor-wrap'
// Vue.component('VueUeditorWrap', VueUeditorWrap)

// 全局组件挂载
Vue.component('DictTag', DictTag)
Vue.component('Pagination', Pagination)
Vue.component('RightToolbar', RightToolbar)
Vue.component('Editor', Editor)
Vue.component('FileUpload', FileUpload)
Vue.component('ImageUpload', ImageUpload)
Vue.component('ImagePreview', ImagePreview)
Vue.component('Pdf', Pdf)
Vue.component('Update', Update)
Vue.component('DialogHeader', DialogHeader)
Vue.component('Countdown', Countdown)
Vue.component('GlobalTranslate', GlobalTranslate)

Vue.use(directive)
Vue.use(plugins)
Vue.use(VueMeta)

Vue.use(Element)
Vue.use(formCreate)
Vue.use(vueEsign)
Vue.use(elTableInfiniteScroll)

Vue.use(BaiduMap, {
  ak: 'q5wRzbxZbdpCLrxOcgCWXN1QnKYGnHm2',
});

DictData.install()

/**
 * If you don't want to use mock-server
 * you want to use MockJs for mock api
 * you can execute: mockXHR()
 *
 * Currently MockJs will be used in the production environment,
 * please remove it before going online! ! !
 */

Vue.use(Element, {
  size: Cookies.get('size') || 'medium' // set element-ui default size
})

Vue.config.productionTip = false

Vue.directive('removeAriaHidden', {
  bind(el, binding) {
    let ariaEls = el.querySelectorAll('.el-radio__original');
    ariaEls.forEach((item) => {
      item.removeAttribute('aria-hidden');
    });
  }
});

new Vue({
  el: '#app',
  router,
  store,
  render: h => h(App)
})
