import request from '@/utils/request'
/**
 * 应收款列表
 * @param {Number} params.startRow - 开始行索引
 * @param {number} params.limit - 最大行数
 * @param {string} params.useOrg - 使用组织
 * @param {string} params.billNo - 单据编号
 * @param {string} params.customerName - 客户
 */
export function getReceivableList(params) {
  return request({
    url: '/pur/ar/receivable/list',
    method: 'get',
    params
  })
}
/**
 * 应收款详情
 * @param {string} params.billNo - 单据编号
 */
export function getReceivableDetail(params) {
  return request({
    url: '/pur/ar/receivable/detail',
    method: 'get',
    params
  })
}
/**
 * 应收汇总
 * @param {number} params.startRow - 开始行索引
 * @param {number} params.limit - 最大行数
 * @param {string} params.settleOrg - 结算组织
 * @param {string} params.startDate - 开始日期(yyyy-MM-dd)
 * @param {string} params.endDate - 结束日期(yyyy-MM-dd)
 * @param {string} params.customerCode - 客户编码
 */
export function getReceivableSummary(params) {
  return request({
    url: '/pur/ar/sumReport/list',
    method: 'get',
    params,
    timeout: 30 * 60 * 1000 // 30分钟
  })
}
/* 应收款明细：列表
 * @param {number} params.startRow - 开始行索引
 * @param {number} params.limit - 最大行数
 * @param {string} params.settleOrg - 结算组织
 * @param {string} params.startDate - 开始日期(yyyy-MM-dd)
 * @param {string} params.endDate - 结束日期(yyyy-MM-dd)
 * @param {string} params.customerNumber - 客户编码
 */
export function getReceivableDetailList(params) {
  return request({
    url: '/pur/ar/payDetailReport/list',
    method: 'get',
    params
  })
}
/**
 * 应收账龄分析
 * @param {string} params.startRow - 开始行索引
 * @param {string} params.limit - 最大行数
 * @param {string} params.settleOrg - 结算组织
 * @param {string} params.date - 日期(yyyy-MM-dd)
 * @param {string} params.customerNumber - 客户编码
 * @param {string} params.contactUnitType - 往来单位类型
 *
 */
export function getReceivableAging(params) {
  return request({
    url: '/pur/ar/agingAnalysis/list',
    method: 'get',
    params
  })
}
