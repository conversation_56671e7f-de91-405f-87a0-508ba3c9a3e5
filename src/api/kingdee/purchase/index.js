import request from '@/utils/request'

/**
 * 审核采购申请单
 *
 * @param {number} data.number - 单据编码
 */
export function auditPurchaseApply(data) {
  return request({
    url: '/pur/requisition/audit',
    method: 'post',
    data
  })
}
/**
 * 审核采购申请单v2
 *
 * @param {number} data.fid - 单据编码
 */
export function auditPurchaseApplyV2(data) {
  return request({
    url: '/pur/requisition/audit.v2',
    method: 'post',
    data
  })
}
/**
 * 撤销采购申请单
 *
 * @param {number} data.number - 单据编码
 */
export function cancelPurchaseApply(data) {
  return request({
    url: '/pur/requisition/cancelAssign',
    method: 'post',
    data
  })
}
/**
 * 撤销采购申请单v2
 * @param {number} data.fid - 单据编码
 */
export function cancelPurchaseApplyV2(data) {
  return request({
    url: '/pur/requisition/cancelAssign.v2',
    method: 'post',
    data
  })
}
/**
 * 删除采购申请单
 *
 * @param {number} data.number - 单据编码
 */
export function deletePurchaseApply(data) {
  return request({
    url: '/pur/requisition/delete',
    method: 'post',
    data
  })
}
/**
 * 删除采购申请单v2
 * @param {number} data.fid - 单据编码
 */
export function deletePurchaseApplyV2(data) {
  return request({
    url: '/pur/requisition/delete.v2',
    method: 'post',
    data
  })
}
/**
 * 采购申请单详情
 *
 * @param {number} params.number - 单据编码
 * @param {boolean} params.refresh - 刷新
 */
export function getPurchaseApplyDetail(params) {
  return request({
    url: '/pur/requisition/detail',
    method: 'get',
    params
  })
}
/**
 * 采购申请单详情V2
 * @param {number} params.number - 单据编码
 */
export function getPurchaseApplyDetailV2(params) {
  return request({
    url: '/pur/requisition/detail.v2',
    method: 'get',
    params
  })
}
/**
 * 采购申请单详情V3
 * @param {number} params.fid - 暂存单据ID
 */
export function getPurchaseApplyDetailV3(params) {
  return request({
    url: '/pur/requisition/detail.v3',
    method: 'get',
    params
  })
}
/**
 * 下推采购申请单
 *
 * @param {number} data.number - 单据编码
 */
export function pushPurchaseApply(data) {
  return request({
    url: '/pur/requisition/push',
    method: 'post',
    data
  })
}
/**
 * 下推采购申请单v2
 * @param {number} data.fid - 单据编码
 */
export function pushPurchaseApplyV2(data) {
  return request({
    url: '/pur/requisition/push.v2',
    method: 'post',
    data
  })
}
// 新增采购申请单
export function addPurchaseApply(data) {
  return request({
    url: '/pur/requisition/save',
    method: 'post',
    data
  })
}
/**
 * 新增采购申请单V2
 * @param {object} data - 新增采购申请单V2
 */
export function addPurchaseApplyV2(data) {
  return request({
    url: '/pur/requisition/save.v2',
    method: 'post',
    data
  })
}
/**
 * 提交采购申请单
 *
 * @param {number} data.number - 单据编码
 */
export function submitPurchaseApply(data) {
  return request({
    url: '/pur/requisition/submit',
    method: 'post',
    data
  })
}
/**
 * 提交采购申请单v2
 * @param {number} data.fid - 单据编码
 */
export function submitPurchaseApplyV2(data) {
  return request({
    url: '/pur/requisition/submit.v2',
    method: 'post',
    data
  })
}
/**
 * 反审核采购申请单
 *
 * @param {number} data.number - 单据编码
 */
export function unAuditPurchaseApply(data) {
  return request({
    url: '/pur/requisition/unAudit',
    method: 'post',
    data
  })
}
/**
 * 反审核采购申请单v2
 * @param {number} data.fid - 单据编码
 */
export function unAuditPurchaseApplyV2(data) {
  return request({
    url: '/pur/requisition/unAudit.v2',
    method: 'post',
    data
  })
}
/**
 * 获取采购申请单列表
 * @param {number} params.startRow - 开始行索引
 * @param {number} params.limit - 最大行数
 * @param {string} params.appOrg - 申请组织
 * @param {string} params.billNo - 单据编号
 * @param {string} params.creator - 创建人
 * @param {string} params.materialNumber - 物料编码
 * @param {string} params.documentStatus - 单据状态
 */
export function getPurchaseApplyList(params) {
  return request({
    url: '/pur/requisition/list',
    method: 'get',
    params
  })
}
/**
 * 采购申请单删除V3
 * @param {Object} data
 */
export function deletePurchaseApplyV3(data) {
  return request({
    url: '/pur/requisition/delete.v3',
    method: 'post',
    data
  })
}
