import request from '@/utils/request'
/**
 * 收料通知单列表
 * @param {Object} params - 查询参数
 * @param {number} params.startRow - 开始行索引
 * @param {number} params.limit - 最大行数
 * @param {number} params.stockOrg - 收料组织
 * @param {string} params.billNo - 单据编号
 * @param {string} params.materialNumber - 物料编码
 * @param {string} params.documentStatus - 单据状态
 * @param {string} params.supplierName - 供应商
 */
export function getNotificationList(params) {
  return request({
    url: '/pur/receive/bill/list',
    method: 'get',
    params
  })
}

/**
 * 收料通知单详情
 * @param {Object} params - 查询参数
 * @param {string} params.billNo - 单据编号
 */
export function getNotificationDetail(params) {
  return request({
    url: '/pur/receive/bill/detail',
    method: 'get',
    params
  })
}

/**
 * 收料通知单审核
 * @param {Object} data - 审核数据
 * @param {string} data.number - 单据编号
 */
export function auditNotification(data) {
  return request({
    url: '/pur/receive/bill/audit',
    method: 'post',
    data
  })
}

/**
 * 收料通知单撤销
 * @param {Object} data - 撤销数据
 * @param {string} data.number - 单据编码
 */
export function revokeNotification(data) {
  return request({
    url: '/pur/receive/bill/cancelAssign',
    method: 'post',
    data
  })
}

/**
 * 收料通知单删除
 * @param {Object} data - 删除数据
 * @param {string} data.number - 单据编码
 */
export function deleteNotification(data) {
  return request({
    url: '/pur/receive/bill/delete',
    method: 'post',
    data
  })
}

/**
 * 收料通知单下推
 * @param {Object} data - 下推数据
 * @param {string} data.number - 单据编码
 */
export function pushDownNotification(data) {
  return request({
    url: '/pur/receive/bill/push',
    method: 'post',
    data
  })
}

/**
 * 收料通知单提交
 * @param {Object} data - 提交数据
 * @param {string} data.number - 单据编码
 */
export function submitNotification(data) {
  return request({
    url: '/pur/receive/bill/submit',
    method: 'post',
    data
  })
}

/**
 * 收料通知单反审核
 * @param {Object} data - 反审核数据
 * @param {string} data.number - 单据编码
 */
export function unauditNotification(data) {
  return request({
    url: '/pur/receive/bill/unAudit',
    method: 'post',
    data
  })
}
/**
 * 收料通知单：详情2
 * @param {Object} params - 查询参数
 * @param {string} params.fid - 单据ID
 */
export function getNotificationDetail2(params) {
  return request({
    url: '/pur/receive/bill/detail2',
    method: 'get',
    params
  })
}
/**
 * 收料通知单：新增
 * @param {Object} data - 新增数据
 */
export function createNotification(data) {
  return request({
    url: '/pur/receive/bill/save',
    method: 'post',
    data
  })
}
/**
 * 收料通知单删除V2
 * @param {Object} data
 */
export function deleteNotificationV2(data) {
  return request({
    url: '/pur/receive/bill/delete.v2',
    method: 'post',
    data
  })
}
