import request from '@/utils/request'
/**
 * 采购退料单列表
 * @param {Object} params - 查询参数
 * @param {number} params.startRow - 开始行索引
 * @param {number} params.limit - 最大行数
 * @param {number} params.stockOrg - 退料组织
 * @param {string} params.billNo - 单据编号
 * @param {string} params.materialNumber - 物料编码
 * @param {string} params.documentStatus - 单据状态
 * @param {string} params.supplierName - 供应商
 *
 */
export function getPurchaseReturnList(params) {
  return request({
    url: '/pur/mrb/list',
    method: 'get',
    params
  })
}
/**
 * 采购退料单详情
 * @param {Object} params - 查询参数
 * @param {string} params.billNo - 单据编号
 */
export function getPurchaseReturnDetail(params) {
  return request({
    url: '/pur/mrb/detail',
    method: 'get',
    params
  })
}
/**
 * 采购退料单审核
 * @param {Object} data - 审核数据
 * @param {string} data.number - 单据编号
 */
export function auditPurchaseReturn(data) {
  return request({
    url: '/pur/mrb/audit',
    method: 'post',
    data
  })
}

/**
 * 采购退料单撤销
 * @param {Object} data - 撤销数据
 * @param {string} data.number - 单据编码
 */
export function revokePurchaseReturn(data) {
  return request({
    url: '/pur/mrb/cancelAssign',
    method: 'post',
    data
  })
}

/**
 * 采购退料单删除
 * @param {Object} data - 删除数据
 * @param {string} data.number - 单据编码
 */
export function deletePurchaseReturn(data) {
  return request({
    url: '/pur/mrb/delete',
    method: 'post',
    data
  })
}

/**
 * 采购退料单下推
 * @param {Object} data - 下推数据
 * @param {string} data.number - 单据编码
 */
export function pushDownPurchaseReturn(data) {
  return request({
    url: '/pur/mrb/push',
    method: 'post',
    data
  })
}

/**
 * 采购退料单提交
 * @param {Object} data - 提交数据
 * @param {string} data.number - 单据编码
 */
export function submitPurchaseReturn(data) {
  return request({
    url: '/pur/mrb/submit',
    method: 'post',
    data
  })
}

/**
 * 采购退料单反审核
 * @param {Object} data - 反审核数据
 * @param {string} data.number - 单据编码
 */
export function unauditPurchaseReturn(data) {
  return request({
    url: '/pur/mrb/unAudit',
    method: 'post',
    data
  })
}
/**
 * 采购退料单：详情2
 * @param {Object} params - 查询参数
 * @param {string} params.fid - 单据ID
 */
export function getPurchaseReturnDetail2(params) {
  return request({
    url: '/pur/mrb/detail2',
    method: 'get',
    params
  })
}
/**
 * 采购退料单：新增
 * @param {Object} data - 新增数据
 */
export function addPurchaseReturn(data) {
  return request({
    url: '/pur/mrb/save',
    method: 'post',
    data
  })
}
/**
 * 采购退料单删除V2
 * @param {Object} data
 */
export function deletePurchaseReturnV2(data) {
  return request({
    url: '/pur/mrb/delete.v2',
    method: 'post',
    data
  })
}
