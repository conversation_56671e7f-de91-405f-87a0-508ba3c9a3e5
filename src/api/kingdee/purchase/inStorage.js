import request from '@/utils/request'
/**
 * 采购入库单列表
 * @param {Object} params - 查询参数
 * @param {number} params.startRow - 开始行索引
 * @param {number} params.limit - 最大行数
 * @param {number} params.useOrg - 使用组织
 * @param {string} params.billNo - 单据编号
 * @param {string} params.materialNumber - 物料编码
 * @param {string} params.documentStatus - 单据状态
 * @param {string} params.supplierName - 供应商
 */
export function getInStorageList(params) {
  return request({
    url: '/pur/stk/inStock/list',
    method: 'get',
    params
  })
}

/**
 * 采购入库单详情
 * @param {Object} params - 查询参数
 * @param {string} params.billNo - 单据编号
 */
export function getInStorageDetail(params) {
  return request({
    url: '/pur/stk/inStock/detail',
    method: 'get',
    params
  })
}

/**
 * 采购入库单审核
 * @param {Object} data - 审核数据
 * @param {string} data.number - 单据编号
 */
export function auditInStorage(data) {
  return request({
    url: '/pur/stk/inStock/audit',
    method: 'post',
    data
  })
}

/**
 * 采购入库单撤销
 * @param {Object} data - 撤销数据
 * @param {string} data.number - 单据编码
 */
export function revokeInStorage(data) {
  return request({
    url: '/pur/stk/inStock/cancelAssign',
    method: 'post',
    data
  })
}

/**
 * 采购入库单删除
 * @param {Object} data - 删除数据
 * @param {string} data.number - 单据编码
 */
export function deleteInStorage(data) {
  return request({
    url: '/pur/stk/inStock/delete',
    method: 'post',
    data
  })
}

/**
 * 采购入库单下推
 * @param {Object} data - 下推数据
 * @param {string} data.number - 单据编码
 */
export function pushDownInStorage(data) {
  return request({
    url: '/pur/stk/inStock/push',
    method: 'post',
    data
  })
}

/**
 * 采购入库单提交
 * @param {Object} data - 提交数据
 * @param {string} data.number - 单据编码
 */
export function submitInStorage(data) {
  return request({
    url: '/pur/stk/inStock/submit',
    method: 'post',
    data
  })
}

/**
 * 采购入库单反审核
 * @param {Object} data - 反审核数据
 * @param {string} data.number - 单据编码
 */
export function unauditInStorage(data) {
  return request({
    url: '/pur/stk/inStock/unAudit',
    method: 'post',
    data
  })
}
/**
 * 采购入库单:详情2
 * @param {Object} params - 查询参数
 * @param {string} params.number - 单据编码
 */
export function getInStorageDetail2(params) {
  return request({
    url: '/pur/stk/inStock/detail2',
    method: 'get',
    params
  })
}
/**
 * 采购入库单:新增
 * @param {Object} data - 新增数据
 */
export function createInStorage(data) {
  return request({
    url: '/pur/stk/inStock/save',
    method: 'post',
    data
  })
}
/**
 * 采购入库单删除V2
 * @param {Object} data
 */
export function deleteInStorageV2(data) {
  return request({
    url: '/pur/stk/inStock/delete.v2',
    method: 'post',
    data
  })
}
