import request from '@/utils/request'
/**
 * 期初采购退料单列表
 * @param {Object} params - 查询参数
 * @param {number} params.startRow - 开始行索引
 * @param {number} params.limit - 最大行数
 * @param {number} params.stockOrg - 退料组织
 * @param {string} params.billNo - 单据编号
 * @param {string} params.materialNumber - 物料编码
 * @param {string} params.documentStatus - 单据状态
 * @param {string} params.supplierName - 供应商
 */
export function getInitPurchaseReturnList(params) {
  return request({
    url: '/pur/init/mrs/list',
    method: 'get',
    params
  })
}

/**
 * 期初采购退料单详情
 * @param {Object} params - 查询参数
 * @param {string} params.billNo - 单据编号
 */
export function getInitPurchaseReturnDetail(params) {
  return request({
    url: '/pur/init/mrs/detail',
    method: 'get',
    params
  })
}

/**
 * 期初采购退料单审核
 * @param {Object} data - 审核数据
 * @param {string} data.number - 单据编号
 */
export function auditInitPurchaseReturn(data) {
  return request({
    url: '/pur/init/mrs/audit',
    method: 'post',
    data
  })
}

/**
 * 期初采购退料单撤销
 * @param {Object} data - 撤销数据
 * @param {string} data.number - 单据编码
 */
export function revokeInitPurchaseReturn(data) {
  return request({
    url: '/pur/init/mrs/cancelAssign',
    method: 'post',
    data
  })
}

/**
 * 期初采购退料单删除
 * @param {Object} data - 删除数据
 * @param {string} data.number - 单据编码
 */
export function deleteInitPurchaseReturn(data) {
  return request({
    url: '/pur/init/mrs/delete',
    method: 'post',
    data
  })
}

/**
 * 期初采购退料单提交
 * @param {Object} data - 提交数据
 * @param {string} data.number - 单据编码
 */
export function submitInitPurchaseReturn(data) {
  return request({
    url: '/pur/init/mrs/submit',
    method: 'post',
    data
  })
}

/**
 * 期初采购退料单反审核
 * @param {Object} data - 反审核数据
 * @param {string} data.number - 单据编码
 */
export function unauditInitPurchaseReturn(data) {
  return request({
    url: '/pur/init/mrs/unAudit',
    method: 'post',
    data
  })
}
/**
 * 期初采购退料单删除V2
 * @param {Object} data
 */
export function deleteInitPurchaseReturnV2(data) {
  return request({
    url: '/pur/init/mrs/delete.v2',
    method: 'post',
    data
  })
}
