import request from '@/utils/request'
/**
 * 采购订单变更单列表
 * @param {Object} params - 查询参数
 * @param {number} params.startRow - 开始行索引
 * @param {number} params.limit - 最大行数
 * @param {string} params.purchaseOrg - 采购组织
 * @param {string} params.billNo - 单据编号
 * @param {string} params.materialNumber - 物料编码
 * @param {string} params.documentStatus - 单据状态
 * @param {string} params.supplierName - 供应商
 */
export function getOrderChangeList(params) {
  return request({
    url: '/pur/po/change/list',
    method: 'get',
    params
  })
}

/**
 * 采购订单变更单详情
 * @param {Object} params - 查询参数
 * @param {string} params.billNo - 单据编号
 */
export function getOrderChangeDetail(params) {
  return request({
    url: '/pur/po/change/detail',
    method: 'get',
    params
  })
}

/**
 * 采购订单变更单审核
 * @param {Object} data - 审核数据
 * @param {string} data.number - 单据编号
 */
export function auditOrderChange(data) {
  return request({
    url: '/pur/po/change/audit',
    method: 'post',
    data
  })
}

/**
 * 采购订单变更单撤销
 * @param {Object} data - 撤销数据
 * @param {string} data.number - 单据编码
 */
export function revokeOrderChange(data) {
  return request({
    url: '/pur/po/change/cancelAssign',
    method: 'post',
    data
  })
}

/**
 * 采购订单变更单删除
 * @param {Object} data - 删除数据
 * @param {string} data.number - 单据编码
 */
export function deleteOrderChange(data) {
  return request({
    url: '/pur/po/change/delete',
    method: 'post',
    data
  })
}

/**
 * 采购订单变更单提交
 * @param {Object} data - 提交数据
 * @param {string} data.number - 单据编码
 */
export function submitOrderChange(data) {
  return request({
    url: '/pur/po/change/submit',
    method: 'post',
    data
  })
}

/**
 * 采购订单变更单反审核
 * @param {Object} data - 反审核数据
 * @param {string} data.number - 单据编码
 */
export function unauditOrderChange(data) {
  return request({
    url: '/pur/po/change/unAudit',
    method: 'post',
    data
  })
}

/**
 * 采购订单变更单详情2
 * @param {Object} params - 查询参数
 * @param {string} params.fid - 单据ID
 */
export function getOrderChangeDetail2(params) {
  return request({
    url: '/pur/po/change/detail2',
    method: 'get',
    params
  })
}

/**
 * 采购订单变更单保存
 * @param {Object} data - 保存数据
 */
export function saveOrderChange(data) {
  return request({
    url: '/pur/po/change/save',
    method: 'post',
    data
  })
}
/**
 * 采购订单变更单删除V2
 * @param {Object} data
 */
export function deleteOrderChangeV2(data) {
  return request({
    url: '/pur/po/change/delete.v2',
    method: 'post',
    data
  })
}
