import request from '@/utils/request'

/**
 * 销售订单列表
 * @param {Object} params - 查询参数
 * @param {number} params.startRow - 开始行索引
 * @param {number} params.limit - 最大行数
 * @param {number} params.saleDept - 销售部门
 * @param {string} params.billNo - 单据编号
 * @param {string} params.materialNumber - 物料编码
 * @param {string} params.documentStatus - 单据状态
 * @param {string} params.customerName - 客户名称
 */
export function getSaleOrderList(params) {
  return request({
    url: '/pur/sale/order/list',
    method: 'get',
    params
  })
}

/**
 * 销售订单详情
 * @param {Object} params - 查询参数
 * @param {string} params.billNo - 单据编号
 */
export function getSaleOrderDetail(params) {
  return request({
    url: '/pur/sale/order/detail',
    method: 'get',
    params
  })
}

/**
 * 销售订单审核
 * @param {Object} data - 审核数据
 * @param {string} data.number - 单据编号
 */
export function auditSaleOrder(data) {
  return request({
    url: '/pur/sale/order/audit',
    method: 'post',
    data
  })
}

/**
 * 销售订单撤销
 * @param {Object} data - 撤销数据
 * @param {string} data.number - 单据编码
 */
export function revokeSaleOrder(data) {
  return request({
    url: '/pur/sale/order/cancelAssign',
    method: 'post',
    data
  })
}

/**
 * 销售订单删除
 * @param {Object} data - 删除数据
 * @param {string} data.number - 单据编码
 */
export function deleteSaleOrder(data) {
  return request({
    url: '/pur/sale/order/delete',
    method: 'post',
    data
  })
}

/**
 * 销售订单下推
 * @param {Object} data - 下推数据
 * @param {string} data.number - 单据编码
 */
export function pushDownSaleOrder(data) {
  return request({
    url: '/pur/sale/order/push',
    method: 'post',
    data
  })
}

/**
 * 销售订单提交
 * @param {Object} data - 提交数据
 * @param {string} data.number - 单据编码
 */
export function submitSaleOrder(data) {
  return request({
    url: '/pur/sale/order/submit',
    method: 'post',
    data
  })
}

/**
 * 销售订单反审核
 * @param {Object} data - 反审核数据
 * @param {string} data.number - 单据编码
 */
export function unauditSaleOrder(data) {
  return request({
    url: '/pur/sale/order/unAudit',
    method: 'post',
    data
  })
}
/**
 * 销售订单保存
 * @param {Object} data - 保存数据
 */
export function saveSaleOrder(data) {
  return request({
    url: '/pur/sale/order/save',
    method: 'post',
    data
  })
}
/**
 * 销售订单：合同查看详情
 * @param {Object} params - 查询参数
 * @param {string} params.contractId - 合同ID
 */
export function getContractDetail(params) {
  return request({
    url: '/pur/sale/order/contract/detail',
    method: 'get',
    params
  })
}
/**
 * 销售订单删除V2
 * @param {Object} data
 */
export function deleteSaleOrderV2(data) {
  return request({
    url: '/pur/sale/order/delete.v2',
    method: 'post',
    data
  })
}
