import request from '@/utils/request'
/**
 * 退料申请单列表
 * @param {Object} params - 查询参数
 * @param {number} params.startRow - 开始行索引
 * @param {number} params.limit - 最大行数
 * @param {number} params.appOrg - 申请组织
 * @param {string} params.billNo - 单据编号
 * @param {string} params.materialNumber - 物料编码
 * @param {string} params.documentStatus - 单据状态
 * @param {string} params.supplierName - 供应商
 */
export function getReturnApplyList(params) {
  return request({
    url: '/pur/mr/app/list',
    method: 'get',
    params
  })
}

/**
 * 退料申请单详情
 * @param {Object} params - 查询参数
 * @param {string} params.billNo - 单据编号
 */
export function getReturnApplyDetail(params) {
  return request({
    url: '/pur/mr/app/detail',
    method: 'get',
    params
  })
}

/**
 * 退料申请单审核
 * @param {Object} data - 审核数据
 * @param {string} data.number - 单据编号
 */
export function auditReturnApply(data) {
  return request({
    url: '/pur/mr/app/audit',
    method: 'post',
    data
  })
}

/**
 * 退料申请单撤销
 * @param {Object} data - 撤销数据
 * @param {string} data.number - 单据编码
 */
export function revokeReturnApply(data) {
  return request({
    url: '/pur/mr/app/cancelAssign',
    method: 'post',
    data
  })
}

/**
 * 退料申请单删除
 * @param {Object} data - 删除数据
 * @param {string} data.number - 单据编码
 */
export function deleteReturnApply(data) {
  return request({
    url: '/pur/mr/app/delete',
    method: 'post',
    data
  })
}

/**
 * 退料申请单下推
 * @param {Object} data - 下推数据
 * @param {string} data.number - 单据编码
 */
export function pushDownReturnApply(data) {
  return request({
    url: '/pur/mr/app/push',
    method: 'post',
    data
  })
}

/**
 * 退料申请单提交
 * @param {Object} data - 提交数据
 * @param {string} data.number - 单据编码
 */
export function submitReturnApply(data) {
  return request({
    url: '/pur/mr/app/submit',
    method: 'post',
    data
  })
}

/**
 * 退料申请单反审核
 * @param {Object} data - 反审核数据
 * @param {string} data.number - 单据编码
 */
export function unauditReturnApply(data) {
  return request({
    url: '/pur/mr/app/unAudit',
    method: 'post',
    data
  })
}

/**
 * 退料申请单保存
 * @param {Object} data - 保存数据
 */
export function saveReturnApply(data) {
  return request({
    url: '/pur/mr/app/save',
    method: 'post',
    data
  })
}
/**
 * 退料申请单删除V2
 * @param {Object} data
 */
export function deleteReturnApplyV2(data) {
  return request({
    url: '/pur/mr/app/delete.v2',
    method: 'post',
    data
  })
}
