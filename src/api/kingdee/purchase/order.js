import request from '@/utils/request'
/**
 * 审核采购订单
 *
 * @param {number} data.number - 订单编码
 */
export function auditPurchaseOrder(data) {
  return request({
    url: '/pur/purchase/order/audit',
    method: 'post',
    data
  })
}
/**
 * 撤销采购订单
 *
 * @param {number} data.number - 订单编码
 */
export function cancelPurchaseOrder(data) {
  return request({
    url: '/pur/purchase/order/cancelAssign',
    method: 'post',
    data
  })
}
/**
 * 删除采购订单
 *
 * @param {number} data.number - 订单编码
 */
export function deletePurchaseOrder(data) {
  return request({
    url: '/pur/purchase/order/delete',
    method: 'post',
    data
  })
}
/**
 * 采购订单详情
 * @param {number} params.billNo - 单据编号
 */
export function getPurchaseOrderDetail(params) {
  return request({
    url: '/pur/purchase/order/detail',
    method: 'get',
    params
  })
}
/**
 * 采购订单详情
 * @param {number} params.fid - FID
 */
export function getPurchaseOrderDetailByFid(params) {
  return request({
    url: '/pur/purchase/order/detail2',
    method: 'get',
    params
  })
}
/**
 * 采购订单列表
 * @param {Number} params.startRow - 开始行索引
 * @param {number} params.limit - 最大行数
 * @param {string} params.billNo - 单据编号
 * @param {string} params.materialNumber - 物料编码
 * @param {string} params.supplierName - 供应商名称
 */
export function getPurchaseOrderList(params) {
  return request({
    url: '/pur/purchase/order/list',
    method: 'get',
    params
  })
}
/**
 * 下推采购订单
 *
 * @param {number} data.number - 订单编码
 */
export function pushPurchaseOrder(data) {
  return request({
    url: '/pur/purchase/order/push',
    method: 'post',
    data
  })
}
/**
 * 提交采购订单
 *
 * @param {number} data.number - 订单编码
 */
export function submitPurchaseOrder(data) {
  return request({
    url: '/pur/purchase/order/submit',
    method: 'post',
    data
  })
}
/**
 * 反审核采购订单
 *
 * @param {number} data.number - 订单编码
 */
export function unAuditPurchaseOrder(data) {
  return request({
    url: '/pur/purchase/order/unAudit',
    method: 'post',
    data
  })
}
/**
 * 新增采购订单
 */
export function addPurchaseOrder(data) {
  return request({
    url: '/pur/purchase/order/save',
    method: 'post',
    data
  })
}
/**
 * 采购订单删除V2
 * @param {Object} data
 */
export function deletePurchaseOrderV2(data) {
  return request({
    url: '/pur/purchase/order/delete.v2',
    method: 'post',
    data
  })
}
