import request from '@/utils/request'
/**
 * 退货通知单：列表
 * @param {Object} params - 列表参数
 * @param {number} params.startRow - 起始行
 * @param {number} params.limit - 每页条数
 * @param {string} params.stockOrg - 库存组织
 * @param {string} params.billNo - 单据编号
 * @param {string} params.name - 客户名称-模糊搜索
 * @param {string} params.name2 - 客户名称-精确匹配
 */
export function getReturnNoticeList(params) {
  return request({
    url: '/pur/sale/return/notice/list',
    method: 'get',
    params
  })
}
/**
 * 退货通知单：详情
 * @param {Object} params - 详情参数
 * @param {string} params.number - 单据编号
 */
export function getReturnNoticeDetail(params) {
  return request({
    url: '/pur/sale/return/notice/detail',
    method: 'get',
    params
  })
}
/**
 * 退货通知单：审核
 * @param {Object} data - 审核数据
 * @param {string} data.number - 单据编号
 */
export function auditReturnNotice(data) {
  return request({
    url: '/pur/sale/return/notice/audit',
    method: 'post',
    data
  })
}

/**
 * 退货通知单：撤销
 * @param {Object} data - 撤销数据
 * @param {string} data.number - 单据编号
 */
export function revokeReturnNotice(data) {
  return request({
    url: '/pur/sale/return/notice/cancelAssign',
    method: 'post',
    data
  })
}

/**
 * 退货通知单：删除
 * @param {Object} data - 删除数据
 * @param {string} data.number - 单据编号
 */
export function deleteReturnNotice(data) {
  return request({
    url: '/pur/sale/return/notice/delete',
    method: 'post',
    data
  })
}

/**
 * 退货通知单：下推
 * @param {Object} data - 下推数据
 * @param {string} data.number - 单据编号
 */
export function pushReturnNotice(data) {
  return request({
    url: '/pur/sale/return/notice/push',
    method: 'post',
    data
  })
}

/**
 * 退货通知单：提交
 * @param {Object} data - 提交数据
 * @param {string} data.number - 单据编号
 */
export function submitReturnNotice(data) {
  return request({
    url: '/pur/sale/return/notice/submit',
    method: 'post',
    data
  })
}

/**
 * 退货通知单：反审核
 * @param {Object} data - 反审核数据
 * @param {string} data.number - 单据编号
 */
export function unauditReturnNotice(data) {
  return request({
    url: '/pur/sale/return/notice/unaudit',
    method: 'post',
    data
  })
}
/**
 * 退货通知单删除V2
 * @param {Object} data
 */
export function deleteReturnNoticeV2(data) {
  return request({
    url: '/pur/sale/return/notice/delete.v2',
    method: 'post',
    data
  })
}