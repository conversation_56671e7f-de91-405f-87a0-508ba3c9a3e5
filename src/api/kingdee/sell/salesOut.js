import request from '@/utils/request'
/**
 * 销售出库单：列表
 * @param {Object} params - 列表参数
 * @param {number} params.startRow - 起始行
 * @param {number} params.limit - 每页条数
 * @param {string} params.deliveryOrg - 发货组织
 * @param {string} params.billNo - 单据编号
 * @param {string} params.name - 客户名称-模糊搜索
 * @param {string} params.name2 - 客户名称-精确匹配
 */
export function getSalesOutList(params) {
  return request({
    url: '/pur/sale/out/stock/list',
    method: 'get',
    params
  })
}
/**
 * 销售出库单：详情
 * @param {Object} params - 详情参数
 * @param {string} params.number - 单据编号
 */
export function getSalesOutDetail(params) {
  return request({
    url: '/pur/sale/out/stock/detail',
    method: 'get',
    params
  })
}
/**
 * 销售出库单：审核
 * @param {Object} data - 审核数据
 * @param {string} data.number - 单据编号
 */
export function auditSalesOut(data) {
  return request({
    url: '/pur/sale/out/stock/audit',
    method: 'post',
    data
  })
}

/**
 * 销售出库单：撤销
 * @param {Object} data - 撤销数据
 * @param {string} data.number - 单据编号
 */
export function revokeSalesOut(data) {
  return request({
    url: '/pur/sale/out/stock/cancelAssign',
    method: 'post',
    data
  })
}

/**
 * 销售出库单：删除
 * @param {Object} data - 删除数据
 * @param {string} data.number - 单据编号
 */
export function deleteSalesOut(data) {
  return request({
    url: '/pur/sale/out/stock/delete',
    method: 'post',
    data
  })
}

/**
 * 销售出库单：下推
 * @param {Object} data - 下推数据
 * @param {string} data.number - 单据编号
 */
export function pushSalesOut(data) {
  return request({
    url: '/pur/sale/out/stock/push',
    method: 'post',
    data
  })
}

/**
 * 销售出库单：提交
 * @param {Object} data - 提交数据
 * @param {string} data.number - 单据编号
 */
export function submitSalesOut(data) {
  return request({
    url: '/pur/sale/out/stock/submit',
    method: 'post',
    data
  })
}

/**
 * 销售出库单：反审核
 * @param {Object} data - 反审核数据
 * @param {string} data.number - 单据编号
 */
export function unAuditSalesOut(data) {
  return request({
    url: '/pur/sale/out/stock/unAudit',
    method: 'post',
    data
  })
}
/**
 * 销售出库单：详情2
 * @param {Object} data - 详情数据
 * @param {string} data.fid - 单据ID
 */
export function getSalesOutDetail2(params) {
  return request({
    url: '/pur/sale/out/stock/detail2',
    method: 'get',
    params
  })
}
/**
 * 销售出库单：新增
 * @param {Object} data - 新增数据
 */
export function createSalesOut(data) {
  return request({
    url: '/pur/sale/out/stock/save',
    method: 'post',
    data
  })
}
/**
 * 销售出库单删除V2
 * @param {Object} data
 */
export function deleteSalesOutV2(data) {
  return request({
    url: '/pur/sale/out/stock/delete.v2',
    method: 'post',
    data
  })
}
