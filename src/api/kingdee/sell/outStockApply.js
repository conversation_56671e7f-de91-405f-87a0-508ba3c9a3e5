import request from '@/utils/request'
/**
 * 出库申请单:列表
 * @param {Object} params - 列表参数
 * @param {number} params.startRow - 开始行索引
 * @param {number} params.limit - 每页条数
 * @param {string} params.stockOrg - 申请组织
 * @param {string} params.billNo - 单据编号
 * @param {string} params.custName - 客户名称-模糊搜索
 * @param {string} params.custName2 - 客户名称-精确匹配
 * @param {string} params.documentStatus - 单据状态
 * @param {string} params.materialName - 物料名称
 * @param {string} params.date - 申请日期
 */
export function getOutStockApplyList(params) {
  return request({
    url: '/pur/stk/OutStockApply/list',
    method: 'get',
    params
  })
}
/**
 * 出库申请单：审核
 * @param {Object} data - 审核数据
 * @param {string} data.billNo - 单据编号
 */
export function auditOutStockApply(data) {
  return request({
    url: '/pur/stk/OutStockApply/audit',
    method: 'post',
    data
  })
}

/**
 * 出库申请单：撤销
 * @param {Object} data - 撤销数据
 * @param {string} data.billNo - 单据编号
 */
export function revokeOutStockApply(data) {
  return request({
    url: '/pur/stk/OutStockApply/cancelAssign',
    method: 'post',
    data
  })
}

/**
 * 出库申请单：删除
 * @param {Object} data - 删除数据
 * @param {string} data.billNo - 单据编号
 */
export function deleteOutStockApply(data) {
  return request({
    url: '/pur/stk/OutStockApply/delete',
    method: 'post',
    data
  })
}

/**
 * 出库申请单：详情
 * @param {Object} params - 详情参数
 * @param {string} params.billNo - 单据编号
 */
export function getOutStockApplyDetail(params) {
  return request({
    url: '/pur/stk/OutStockApply/detail',
    method: 'get',
    params
  })
}

/**
 * 出库申请单：详情2
 * @param {Object} params - 详情参数
 * @param {string} params.fid - 单据id
 */
export function getOutStockApplyDetail2(params) {
  return request({
    url: '/pur/stk/OutStockApply/detail2',
    method: 'get',
    params
  })
}

/**
 * 出库申请单：下推
 * @param {Object} data - 下推数据
 * @param {string} data.billNo - 单据编号
 */
export function pushOutStockApply(data) {
  return request({
    url: '/pur/stk/OutStockApply/push',
    method: 'post',
    data
  })
}

/**
 * 出库申请单：保存
 * @param {Object} data - 保存数据
 */
export function saveOutStockApply(data) {
  return request({
    url: '/pur/stk/OutStockApply/save',
    method: 'post',
    data
  })
}

/**
 * 出库申请单：提交
 * @param {Object} data - 提交数据
 * @param {string} data.billNo - 单据编号
 */
export function submitOutStockApply(data) {
  return request({
    url: '/pur/stk/OutStockApply/submit',
    method: 'post',
    data
  })
}

/**
 * 出库申请单：反审核
 * @param {Object} data - 反审核数据
 * @param {string} data.billNo - 单据编号
 */
export function unAuditOutStockApply(data) {
  return request({
    url: '/pur/stk/OutStockApply/unAudit',
    method: 'post',
    data
  })
}
/**
 * 出库申请单删除V2
 * @param {Object} data
 */
export function deleteOutStockApplyV2(data) {
  return request({
    url: '/pur/stk/OutStockApply/delete.v2',
    method: 'post',
    data
  })
}
