import request from '@/utils/request'
/**
 * 发货通知单：列表
 * @param {Object} params - 查询参数
 * @param {number} params.startRow - 开始行索引
 * @param {number} params.limit - 最大行数
 * @param {string} params.deliveryOrg - 发货组织
 * @param {string} params.billNo - 单据编号
 * @param {string} params.name - 客户名称--模糊搜索
 * @param {string} params.name2 - 客户简称--精确匹配
 */
export function getShippingNoteList(params) {
  return request({
    url: '/pur/delivery/notice/list',
    method: 'get',
    params
  })
}
/**
 * 发货通知单：审核
 * @param {Object} data - 审核数据
 * @param {string} data.number - 单据编号
 */
export function auditShippingNote(data) {
  return request({
    url: '/pur/delivery/notice/audit',
    method: 'post',
    data
  })
}
/**
 * 发货通知单：撤销
 * @param {Object} data - 撤销数据
 * @param {string} data.number - 单据编号
 */
export function cancelShippingNote(data) {
  return request({
    url: '/pur/delivery/notice/cancelAssign',
    method: 'post',
    data
  })
}
/**
 * 发货通知单：删除
 * @param {Object} data - 删除数据
 * @param {string} data.number - 单据编号
 */
export function deleteShippingNote(data) {
  return request({
    url: '/pur/delivery/notice/delete',
    method: 'post',
    data
  })
}

/**
 * 发货通知单：详情
 * @param {Object} params - 查询参数
 * @param {string} params.number - 单据编号
 */
export function getShippingNoteDetail(params) {
  return request({
    url: '/pur/delivery/notice/detail',
    method: 'get',
    params
  })
}

/**
 * 发货通知单：详情2
 * @param {Object} params - 查询参数
 * @param {string} params.fid - 单据id
 */
export function getShippingNoteDetail2(params) {
  return request({
    url: '/pur/delivery/notice/detail2',
    method: 'get',
    params
  })
}

/**
 * 发货通知单：下推
 * @param {Object} data - 下推数据
 * @param {string} data.number - 单据编号
 */
export function pushShippingNote(data) {
  return request({
    url: '/pur/delivery/notice/push',
    method: 'post',
    data
  })
}

/**
 * 发货通知单：提交
 * @param {Object} data - 提交数据
 * @param {string} data.number - 单据编号
 */
export function submitShippingNote(data) {
  return request({
    url: '/pur/delivery/notice/submit',
    method: 'post',
    data
  })
}

/**
 * 发货通知单：反审核
 * @param {Object} data - 反审核数据
 * @param {string} data.number - 单据编号
 */
export function unAuditShippingNote(data) {
  return request({
    url: '/pur/delivery/notice/unAudit',
    method: 'post',
    data
  })
}

/**
 * 发货通知单：新增
 * @param {Object} data - 新增数据
 */
export function createShippingNote(data) {
  return request({
    url: '/pur/delivery/notice/save',
    method: 'post',
    data
  })
}
/**
 * 发货通知单删除V2
 * @param {Object} data
 */
export function deleteShippingNoteV2(data) {
  return request({
    url: '/pur/delivery/notice/delete.v2',
    method: 'post',
    data
  })
}
