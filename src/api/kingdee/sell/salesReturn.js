import request from '@/utils/request'
/**
 * 销售退货单：列表
 * @param {Object} params - 列表参数
 * @param {number} params.startRow - 开始行索引
 * @param {number} params.limit - 每页条数
 * @param {string} params.billNo - 单据编号
 * @param {string} params.stockOrg - 库存组织
 * @param {string} params.name - 客户名称-模糊搜索
 * @param {string} params.name2 - 客户名称-精确匹配
 */
export function getSalesReturnList(params) {
  return request({
    url: '/pur/sale/return/stock/list',
    method: 'get',
    params
  })
}
/**
 * 销售退货单：审核
 * @param {Object} data - 审核数据
 * @param {string} data.number - 单据编号
 */
export function auditSalesReturn(data) {
  return request({
    url: '/pur/sale/return/stock/audit',
    method: 'post',
    data
  })
}

/**
 * 销售退货单：撤销
 * @param {Object} data - 撤销数据
 * @param {string} data.number - 单据编号
 */
export function revokeSalesReturn(data) {
  return request({
    url: '/pur/sale/return/stock/cancelAssign',
    method: 'post',
    data
  })
}

/**
 * 销售退货单：删除
 * @param {Object} data - 删除数据
 * @param {string} data.number - 单据编号
 */
export function deleteSalesReturn(data) {
  return request({
    url: '/pur/sale/return/stock/delete',
    method: 'post',
    data
  })
}

/**
 * 销售退货单：详情
 * @param {Object} params - 详情参数
 * @param {string} params.number - 单据编号
 */
export function getSalesReturnDetail(params) {
  return request({
    url: '/pur/sale/return/stock/detail',
    method: 'get',
    params
  })
}
/**
 * 销售退货单：详情2
 * @param {Object} params - 详情参数
 * @param {string} params.fid - 单据ID
 */
export function getSalesReturnDetail2(params) {
  return request({
    url: '/pur/sale/return/stock/detail2',
    method: 'get',
    params
  })
}
/**
 * 销售退货单：下推
 * @param {Object} data - 下推数据
 * @param {string} data.number - 单据编号
 * @param {string} data.target - 目标单据类型
 */
export function pushSalesReturn(data) {
  return request({
    url: '/pur/sale/return/stock/push',
    method: 'post',
    data
  })
}

/**
 * 销售退货单：提交
 * @param {Object} data - 提交数据
 * @param {string} data.number - 单据编号
 */
export function submitSalesReturn(data) {
  return request({
    url: '/pur/sale/return/stock/submit',
    method: 'post',
    data
  })
}

/**
 * 销售退货单：反审核
 * @param {Object} data - 反审核数据
 * @param {string} data.number - 单据编号
 */
export function unauditSalesReturn(data) {
  return request({
    url: '/pur/sale/return/stock/unAudit',
    method: 'post',
    data
  })
}
/**
 * 销售退货单：新增
 * @param {Object} data - 新增数据
 */
export function addSalesReturn(data) {
  return request({
    url: '/pur/sale/return/stock/save',
    method: 'post',
    data
  })
}
/**
 * 销售退货单删除V2
 * @param {Object} data
 */
export function deleteSalesReturnV2(data) {
  return request({
    url: '/pur/sale/return/stock/delete.v2',
    method: 'post',
    data
  })
}
