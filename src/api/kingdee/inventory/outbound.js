import request from '@/utils/request'
/**
 * 其他出库单审核
 * @param {Object} data
 */
export function auditOutbound(data) {
  return request({
    url: '/pur/stk/mis/delivery/audit',
    method: 'post',
    data
  })
}

/**
 * 其他出库单撤销
 * @param {Object} data
 */
export function cancelOutbound(data) {
  return request({
    url: '/pur/stk/mis/delivery/cancelAssign',
    method: 'post',
    data
  })
}

/**
 * 其他出库单删除
 * @param {Object} data
 */
export function deleteOutbound(data) {
  return request({
    url: '/pur/stk/mis/delivery/delete',
    method: 'post',
    data
  })
}

/**
 * 其他出库单详情
 * @param {Object} params
 */
export function getOutboundDetail(params) {
  return request({
    url: '/pur/stk/mis/delivery/detail',
    method: 'get',
    params
  })
}

/**
 * 其他出库单详情2
 * @param {Object} params
 */
export function getOutboundDetail2(params) {
  return request({
    url: '/pur/stk/mis/delivery/detail2',
    method: 'get',
    params
  })
}

/**
 * 其他出库单列表
 * @param {Object} params
 */
export function getOutboundList(params) {
  return request({
    url: '/pur/stk/mis/delivery/list',
    method: 'get',
    params
  })
}

/**
 * 其他出库单下推
 * @param {Object} data
 */
export function pushOutbound(data) {
  return request({
    url: '/pur/stk/mis/delivery/push',
    method: 'post',
    data
  })
}

/**
 * 其他出库单保存
 * @param {Object} data
 */
export function saveOutbound(data) {
  return request({
    url: '/pur/stk/mis/delivery/save',
    method: 'post',
    data
  })
}

/**
 * 其他出库单提交
 * @param {Object} data
 */
export function submitOutbound(data) {
  return request({
    url: '/pur/stk/mis/delivery/submit',
    method: 'post',
    data
  })
}

/**
 * 其他出库单反审核
 * @param {Object} data
 */
export function unAuditOutbound(data) {
  return request({
    url: '/pur/stk/mis/delivery/unAudit',
    method: 'post',
    data
  })
}
/**
 * 其他出库单删除V2
 * @param {Object} data
 */
export function deleteOutboundV2(data) {
  return request({
    url: '/pur/stk/mis/delivery/delete.v2',
    method: 'post',
    data
  })
}
