import request from '@/utils/request'

/**
 * 物料单位换算：保存
 * @param {Object} data 请求参数
 */
export function saveMaterialUnitConvert(data) {
  return request({
    url: '/pur/material/unit/convert',
    method: 'post',
    data
  })
}

/**
 * 物料单位换算：审核
 * @param {Object} data 请求参数
 */
export function auditMaterialUnitConvert(data) {
  return request({
    url: '/pur/material/unit/convert/audit',
    method: 'post',
    data
  })
}

/**
 * 物料单位换算：撤销
 * @param {Object} data 请求参数
 */
export function cancelMaterialUnitConvert(data) {
  return request({
    url: '/pur/material/unit/convert/cancelAssign',
    method: 'post',
    data
  })
}

/**
 * 物料单位换算：删除
 * @param {Object} data 请求参数
 */
export function deleteMaterialUnitConvert(data) {
  return request({
    url: '/pur/material/unit/convert/delete',
    method: 'post',
    data
  })
}

/**
 * 物料单位换算：详情
 * @param {Object} params 请求参数
 */
export function getMaterialUnitConvertDetail(params) {
  return request({
    url: '/pur/material/unit/convert/detail',
    method: 'get',
    params
  })
}

/**
 * 物料单位换算：详情2
 * @param {Object} params 请求参数
 */
export function getMaterialUnitConvertDetail2(params) {
  return request({
    url: '/pur/material/unit/convert/detail2',
    method: 'get',
    params
  })
}

/**
 * 物料单位换算：反禁用
 * @param {Object} data 请求参数
 */
export function enableMaterialUnitConvert(data) {
  return request({
    url: '/pur/material/unit/convert/enable',
    method: 'post',
    data
  })
}

/**
 * 物料单位换算：禁用
 * @param {Object} data 请求参数
 */
export function forbidMaterialUnitConvert(data) {
  return request({
    url: '/pur/material/unit/convert/forbid',
    method: 'post',
    data
  })
}

/**
 * 物料单位换算：列表
 * @param {Object} params 请求参数
 * @param {Number} params.startRow 开始行索引
 * @param {Number} params.limit 最大行数
 * @param {String} params.number 物料编码(包含)
 * @param {String} params.materialName 物料名称(包含)
 * @param {String} params.forbidStatus 禁用状态
 */
export function getMaterialUnitConvertList(params) {
  return request({
    url: '/pur/material/unit/convert/list',
    method: 'get',
    params
  })
}

/**
 * 物料单位换算：提交
 * @param {Object} data 请求参数
 */
export function submitMaterialUnitConvert(data) {
  return request({
    url: '/pur/material/unit/convert/submit',
    method: 'post',
    data
  })
}

/**
 * 物料单位换算：反审核
 * @param {Object} data 请求参数
 */
export function unAuditMaterialUnitConvert(data) {
  return request({
    url: '/pur/material/unit/convert/unAudit',
    method: 'post',
    data
  })
}
/**
 * 物料单位换算删除V2
 * @param {Object} data
 */
export function deleteMaterialUnitConvertV2(data) {
  return request({
    url: '/pur/material/unit/convert/delete.v2',
    method: 'post',
    data
  })
}
