import request from '@/utils/request'

// 库存预警：添加产品
export function inventoryAlertCreate(data) {
  return request({
    url: '/inventory/alert',
    method: 'post',
    data
  })
}
// 库存预警：删除产品
export function inventoryAlertDelete(data) {
  return request({
    url: '/inventory/alert/delete',
    method: 'post',
    data
  })
}
// 库存预警：列表
export function inventoryAlertList(params) {
  return request({
    url: '/inventory/alert/list',
    method: 'get',
    params
  })
}
// 库存预警：添加|编辑库存组织
export function inventoryAlertStock(data) {
  return request({
    url: '/inventory/alert/stock',
    method: 'post',
    data
  })
}
// 库存预警：删除库存组织
export function inventoryAlertStockDelete(data) {
  return request({
    url: '/inventory/alert/stock/delete',
    method: 'post',
    data
  })
}
// 库存预警：刷新库存组织数据
export function inventoryAlertStockRefresh(data) {
  return request({
    url: '/inventory/alert/stock/refresh',
    method: 'post',
    data
  })
}
// 库存预警：置顶产品
export function inventoryAlertTop(data) {
  return request({
    url: '/inventory/alert/top',
    method: 'put',
    data
  })
}
// 库存预警：取消置顶
export function inventoryAlertUnTop(data) {
  return request({
    url: '/inventory/alert/un/top',
    method: 'put',
    data
  })
}
