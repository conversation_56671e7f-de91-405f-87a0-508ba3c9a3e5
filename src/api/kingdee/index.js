import request from '@/utils/request'
// 前置参数
export function getPurchaseApplyPre(params) {
  return request({
    url: '/pur/requisition/params',
    method: 'get',
    params
  })
}
/**
 * 员工任岗信息列表
 *
 * @param {string} params.name - 员工姓名
 */
export function getStaffPostList(params) {
  return request({
    url: '/pur/requisition/staff',
    method: 'get',
    params
  })
}
/**
 * 业务员列表
 * @param {string} params.bizOrg - 使用组织
 * @param {string} params.OperatorType - 业务员类型 xsy:销售员,cgy:采购员,why:仓管员,jhy:计划员,cwry:财务人员,zjy:质检员,fwry:服务人员,jsy:驾驶员,cxy:程序员
 * @param {number} params.name - 业务员姓名
 */
export function getSalesmanList(params) {
  return request({
    url: '/pur/bd/operator',
    method: 'get',
    params
  })
}
/**
 * 供应商列表
 * @param {string} params.name - 供应商名称
 * @param {string} params.useOrg - 使用组织
 * @param {number} params.startRow - 开始行索引
 * @param {number} params.limit - 最大行数
 * @param {number} params.number - 编码
 */
export function getSupplierList(params) {
  return request({
    url: '/pur/requisition/supplier',
    method: 'get',
    params
  })
}
/**
 * 物料详情
 * @param {string} params.number - 物料编码
 */
export function getMaterialDetail(params) {
  return request({
    url: '/pur/material/detail',
    method: 'get',
    params
  })
}
/**
 * 物料列表
 * @param {number} params.startRow - 开始行索引
 * @param {number} params.limit - 最大行数
 * @param {string} params.useOrg - 使用组织
 * @param {string} params.number - 物料编码
 * @param {string} params.name - 物料名称
 * @param {string} params.specification - 规格型号
 */
export function getMaterialList(params) {
  return request({
    url: '/pur/material/list',
    method: 'get',
    params
  })
}
/* 保存产品对应金蝶产品编码
 * @param {string} data.materialNumber - 物料编码
 * @param {string} data.productId - 产品ID
 * @param {string} data.source - 产品来源
 */
export function saveProductKingdeeCode(data) {
  return request({
    url: '/pur/product/materialNumber',
    method: 'post',
    data
  })
}
/**
 * 获取产品对应金蝶产品编码
 * @param {string} params.productId - 产品ID
 * @param {string} params.source - 产品来源
 */
export function getProductKingdeeCode(params) {
  return request({
    url: '/pur/product/materialNumber',
    method: 'get',
    params
  })
}
/**
 * 根据金蝶物料编码查询私域产品
 * @param {string} params.materialNumber - 金蝶物料编码
 */
export function getPrivateProductByMaterialNumber(params) {
  return request({
    url: '/system/privateduct/get/by/material/code',
    method: 'get',
    params
  })
}
/**
 * 员工列表
 * @param {number} params.startRow - 开始行索引
 * @param {number} params.limit - 最大行数
 * @param {string} params.useOrg - 使用组织
 * @param {string} params.number - 员工编号
 * @param {string} params.name - 员工姓名-模糊匹配
 * @param {string} params.name2 - 员工姓名-精确匹配
 */
export function getStaffList(params) {
  return request({
    url: '/pur/empinfo/list',
    method: 'get',
    params
  })
}
/**
 * 员工详情
 * @param {string} params.billNo - 员工编号
 */
export function getStaffDetail(params) {
  return request({
    url: '/pur/empinfo/detail',
    method: 'get',
    params
  })
}
/**
 * 物料：即时库存列表
 * @param {number} params.startRow - 开始行索引
 * @param {number} params.limit - 最大行数
 * @param {string} params.stockOrg - 库存组织
 * @param {string} params.number - 物料编码
 * @param {string} params.stockNum - 仓库编码
 * @param {string} params.stockName - 仓库名称
 */
export function getMaterialStockList(params) {
  return request({
    url: '/pur/stk/inventory',
    method: 'get',
    params
  })
}
/**
 * 仓库列表
 * @param {number} params.startRow - 开始行索引
 * @param {number} params.limit - 最大行数
 * @param {string} params.useOrg - 使用组织
 * @param {string} params.number - 仓库编码
 * @param {string} params.name - 仓库名称
 */
export function getStockList(params) {
  return request({
    url: '/pur/bd/stock',
    method: 'get',
    params
  })
}
