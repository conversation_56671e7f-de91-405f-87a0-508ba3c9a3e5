import request from '@/utils/request'
/**
 * 应付单列表
 * @param {number} params.startRow - 开始行索引
 * @param {number} params.limit - 最大行数
 * @param {string} params.settleOrg - 结算组织
 * @param {string} params.billNo - 单据编号
 * @param {string} params.supplierName - 供应商-模糊搜索
 * @param {string} params.supplierName2 - 供应商-精确匹配
 */
export function getPayablesList(params) {
  return request({
    url: '/pur/ap/payable/list',
    method: 'get',
    params
  })
}
/**
 * 应付单详情
 * @param {string} params.billNo - 单据编号
 */
export function getPayablesDetail(params) {
  return request({
    url: '/pur/ap/payable/detail',
    method: 'get',
    params
  })
}
/**
 * 审核应付单
 * @param {string} params.number - 单据编号
 */
export function auditPayables(data) {
  return request({
    url: '/pur/ap/payable/audit',
    method: 'post',
    data
  })
}
/**
 * 撤销应付单
 * @param {string} params.number - 单据编号
 */
export function revokePayables(data) {
  return request({
    url: '/pur/ap/payable/cancelAssign',
    method: 'post',
    data
  })
}
/**
 * 删除应付单
 * @param {string} params.number - 单据编号
 */
export function deletePayables(data) {
  return request({
    url: '/pur/ap/payable/delete',
    method: 'post',
    data
  })
}
/**
 * 下推应付单
 * @param {string} params.number - 单据编号
 * @param {string} params.target - 目标单据类型
 */
export function pushPayables(data) {
  return request({
    url: '/pur/ap/payable/push',
    method: 'post',
    data
  })
}
/**
 * 提交应付单
 * @param {string} params.number - 单据编号
 */
export function submitPayables(data) {
  return request({
    url: '/pur/ap/payable/submit',
    method: 'post',
    data
  })
}
/**
 * 反审核应付单
 * @param {string} params.number - 单据编号
 */
export function unAuditPayables(data) {
  return request({
    url: '/pur/ap/payable/unAudit',
    method: 'post',
    data
  })
}
/* 应付款汇总列表
 * @param {number} params.startRow - 开始行索引
 * @param {number} params.limit - 最大行数
 * @param {string} params.settleOrg - 结算组织
 * @param {string} params.startDate - 开始日期(yyyy-MM-dd)
 * @param {string} params.endDate - 结束日期(yyyy-MM-dd)
 * @param {string} params.customerNumber - 客户编码
 */
export function getPayablesSummary(params) {
  return request({
    url: '/pur/ap/sumReport/list',
    method: 'get',
    params
  })
}
/**
 * 应收账龄分析
 * @param {string} params.startRow - 开始行索引
 * @param {string} params.limit - 最大行数
 * @param {string} params.settleOrg - 结算组织
 * @param {string} params.date - 日期(yyyy-MM-dd)
 * @param {string} params.customerNumber - 客户编码
 * @param {string} params.contactUnitType - 往来单位类型
 *
 */
export function getPayablesAging(params) {
  return request({
    url: '/pur/ap/agingAnalysis/list',
    method: 'get',
    params
  })
}
/**
 * 应付框明细
 * @param {string} params.startRow - 开始行索引
 * @param {string} params.limit - 最大行数
 * @param {string} params.settleOrg - 结算组织
 * @param {string} params.beginDate - 开始日期(yyyy-MM-dd)
 * @param {string} params.endDate - 结束日期(yyyy-MM-dd)
 * @param {string} params.customerNumber - 客户编码
 * @param {string} params.contactUnitType - 往来单位类型
 */
export function getPayablesDetailList(params) {
  return request({
    url: '/pur/ap/payDetailReport/list',
    method: 'get',
    params
  })
}
/**
 * 应付单：详情2
 * @param {string} params.fid - 主键ID
 */
export function getPayablesDetail2(params) {
  return request({
    url: '/pur/ap/payable/detail2',
    method: 'get',
    params
  })
}
/**
 * 应付单：保存
 * @param {string} data - 单据数据
 */
export function savePayables(data) {
  return request({
    url: '/pur/ap/payable/save',
    method: 'post',
    data
  })
}
/**
 * 删除应付单V2
 * @param {string} params.id - 单据ID
 */
export function deletePayablesV2(data) {
  return request({
    url: '/pur/ap/payable/delete.v2',
    method: 'post',
    data
  })
}
