import request from '@/utils/request'
/**
 * 辅助资料：审核
 * @param {Object} data
 */
export function auditEngineering(data) {
  return request({
    url: '/pur/bos/assistant/data/detail/audit',
    method: 'post',
    data
  })
}
/**
 * 辅助资料：撤销
 * @param {Object} data
 */
export function cancelEngineering(data) {
  return request({
    url: '/pur/bos/assistant/data/detail/cancelAssign',
    method: 'post',
    data
  })
}
/**
 * 辅助资料删除
 * @param {Object} data
 */
export function deleteEngineering(data) {
  return request({
    url: '/pur/bos/assistant/data/detail/delete',
    method: 'post',
    data
  })
}
/**
 * 辅助资料详情2
 * @param {Object} params
 */
export function getEngineeringDetail2(params) {
  return request({
    url: '/pur/bos/assistant/data/detail/detail2',
    method: 'get',
    params
  })
}
/**
 * 辅助资料反禁用
 * @param {Object} data
 */
export function enableEngineering(data) {
  return request({
    url: '/pur/bos/assistant/data/detail/enable',
    method: 'post',
    data
  })
}
/**
 * 辅助资料禁用
 * @param {Object} data
 */
export function forbidEngineering(data) {
  return request({
    url: '/pur/bos/assistant/data/detail/forbid',
    method: 'post',
    data
  })
}
/**
 * 辅助资料列表
 * @param {Object} params
 */
export function getEngineeringList(params) {
  return request({
    url: '/pur/bos/assistant/data/detail/list',
    method: 'get',
    params
  })
}
/**
 * 辅助资料保存
 * @param {Object} data
 */
export function saveEngineering(data) {
  return request({
    url: '/pur/bos/assistant/data/detail/save',
    method: 'post',
    data
  })
}
/**
 * 辅助资料提交
 * @param {Object} data
 */
export function submitEngineering(data) {
  return request({
    url: '/pur/bos/assistant/data/detail/submit',
    method: 'post',
    data
  })
}
/**
 * 辅助资料反审核
 * @param {Object} data
 */
export function unAuditEngineering(data) {
  return request({
    url: '/pur/bos/assistant/data/detail/unAudit',
    method: 'post',
    data
  })
}
/**
 * 辅助资料分类列表
 * @param {Object} params
 */
export function getEngineeringCategoryList(params) {
  return request({
    url: '/pur/bos/assistant/data/list',
    method: 'get',
    params
  })
}
