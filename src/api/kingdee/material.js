import request from '@/utils/request'

/**
 * 物料：审核
 * @param {Object} data
 * @param {String} data.number 物料编码
 */
export function auditMaterial(data) {
  return request({
    url: '/pur/material/audit',
    method: 'post',
    data
  })
}
/**
 * 物料：撤销
 * @param {Object} data
 * @param {String} data.number 物料编码
 */
export function cancelMaterial(data) {
  return request({
    url: '/pur/material/cancelAssign',
    method: 'post',
    data
  })
}

/**
 * 物料：删除
 * @param {Object} data
 * @param {String} data.number 物料编码
 */
export function deleteMaterial(data) {
  return request({
    url: '/pur/material/delete',
    method: 'post',
    data
  })
}
/**
 * 物料：分组查询
 */
export function queryMaterialGroupList(params) {
  return request({
    url: '/pur/material/group',
    method: 'get',
    params
  })
}
/**
 * 物料：保存
 */
export function saveMaterial(data) {
  return request({
    url: '/pur/material/save',
    method: 'post',
    data
  })
}

/**
 * 物料：提交
 */
export function submitMaterial(data) {
  return request({
    url: '/pur/material/submit',
    method: 'post',
    data
  })
}

/**
 * 物料：反审核
 */
export function unauditMaterial(data) {
  return request({
    url: '/pur/material/unAudit',
    method: 'post',
    data
  })
}
/**
 * 物料删除V2
 * @param {Object} data
 */
export function deleteMaterialV2(data) {
  return request({
    url: '/pur/material/delete.v2',
    method: 'post',
    data
  })
}
