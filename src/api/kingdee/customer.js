import request from '@/utils/request'

/**
 * 客户列表
 * @param {number} params.startRow - 开始行索引
 * @param {number} params.limit - 最大行数
 * @param {string} params.groupOrg - 客户分组
 * @param {string} params.useOrg - 使用组织
 * @param {string} params.name - 客户名称-模糊搜索
 * @param {string} params.name2 - 客户名称-精确匹配
 */
export function getCustomerList(params) {
  return request({
    url: '/pur/bd/customer/list',
    method: 'get',
    params,
    timeout: 30 * 60 * 1000 // 30分钟
  })
}
/**
 * 客户审核
 * @param {Object} data - 审核数据
 * @param {string} data.number - 客户编号
 */
export function auditCustomer(data) {
  return request({
    url: '/pur/bd/customer/audit',
    method: 'post',
    data
  })
}

/**
 * 客户撤销
 * @param {Object} data - 撤销数据
 * @param {string} data.number - 客户编号
 */
export function revokeCustomer(data) {
  return request({
    url: '/pur/bd/customer/cancelAssign',
    method: 'post',
    data
  })
}

/**
 * 客户删除
 * @param {Object} data - 删除数据
 * @param {string} data.number - 客户编号
 */
export function deleteCustomer(data) {
  return request({
    url: '/pur/bd/customer/delete',
    method: 'post',
    data
  })
}

/**
 * 客户详情
 * @param {Object} params - 详情参数
 * @param {string} params.number - 客户编号
 */
export function getCustomerDetail(params) {
  return request({
    url: '/pur/bd/customer/detail',
    method: 'get',
    params
  })
}

/**
 * 客户保存
 * @param {Object} data - 保存数据
 * @param {string} data.number - 客户编号
 */
export function saveCustomer(data) {
  return request({
    url: '/pur/bd/customer/save',
    method: 'post',
    data
  })
}

/**
 * 客户提交
 * @param {Object} data - 提交数据
 * @param {string} data.number - 客户编号
 */
export function submitCustomer(data) {
  return request({
    url: '/pur/bd/customer/submit',
    method: 'post',
    data
  })
}

/**
 * 客户反审核
 * @param {Object} data - 反审核数据
 * @param {string} data.number - 客户编号
 */
export function unAuditCustomer(data) {
  return request({
    url: '/pur/bd/customer/unAudit',
    method: 'post',
    data
  })
}
/**
 * 客户删除V2
 * @param {Object} data
 */
export function deleteCustomerV2(data) {
  return request({
    url: '/pur/bd/customer/delete.v2',
    method: 'post',
    data
  })
}
