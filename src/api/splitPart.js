import request from '@/utils/request'
// 拆零：拆零
export function splitPart(data) {
  return request({
    url: '/split/part',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    transformRequest: [
      function (data) {
        return stringify(data)
      }
    ]
  })
}
// bom：新增
export function createBom(data) {
  return request({
    url: '/split/part/bom/add',
    method: 'post',
    data
  })
}
// bom：检测必要项
export function checkBom(params) {
  return request({
    url: '/split/part/bom/check',
    method: 'get',
    params
  })
}
// bom：增加子物料
export function createBomChild(data) {
  return request({
    url: '/split/part/bom/detail/add',
    method: 'post',
    data
  })
}
// bom：删除子物料
export function deleteBomChild(params) {
  return request({
    url: '/split/part/bom/detail/delete',
    method: 'delete',
    params
  })
}
// bom：获取BOM列表
export function getBomList(params) {
  return request({
    url: '/split/part/bom/list',
    method: 'get',
    params
  })
}
// bom：状态切换
export function toggleBomStatus(data) {
  return request({
    url: '/split/part/bom/status',
    method: 'put',
    data
  })
}
// 拆零：详情
export function getSplitPartDetail(params) {
  return request({
    url: '/split/part/detail',
    method: 'get',
    params
  })
}
// 拆零：列表
export function getSplitPartList(params) {
  return request({
    url: '/split/part/list',
    method: 'get',
    params
  })
}
// 物料：新增
export function createMaterial(data) {
  return request({
    url: '/split/part/material',
    method: 'post',
    data
  })
}
// 物料：修改
export function updateMaterial(data) {
  return request({
    url: '/split/part/material',
    method: 'put',
    data
  })
}
// 物料：删除
export function deleteMaterial(data) {
  return request({
    url: '/split/part/material',
    method: 'delete',
    data
  })
}
// 物料：列表
export function getMaterialList(params) {
  return request({
    url: '/split/part/material/list',
    method: 'get',
    params
  })
}
// 物料：价格修改记录
export function getMaterialPriceRecord(params) {
  return request({
    url: '/split/part/material/price/log',
    method: 'get',
    params
  })
}
// 物料：保存拆零记录
export function saveMaterialSplitRecord(data) {
  return request({
    url: '/split/part/save/',
    method: 'post',
    data
  })
}

// 将参数转换成功 formdata 接收格式
function stringify(data) {
  const formData = new FormData()
  for (const key in data) {
    if (data.hasOwnProperty(key)) {
      if (data[key]) {
        if (data[key].constructor === Array) {
          if (data[key][0]) {
            if (data[key][0].constructor === Object) {
              formData.append(key, JSON.stringify(data[key]))
            } else {
              data[key].forEach((item, index) => {
                formData.append(key + `[${index}]`, item)
              })
            }
          } else {
            formData.append(key + '[]', '')
          }
        } else if (data[key].constructor === Object) {
          formData.append(key, JSON.stringify(data[key]))
        } else {
          formData.append(key, data[key])
        }
      } else {
        if (data[key] === 0) {
          formData.append(key, 0)
        } else if (data[key] === false) {
          formData.append(key, false)
        } else {
          formData.append(key, '')
        }
      }
    }
  }
  return formData
}
