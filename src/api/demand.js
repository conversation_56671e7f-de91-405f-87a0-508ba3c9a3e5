import request from '@/utils/request'

// 查询采购需求列表
export function listDemand(query) {
  return request({
    url: '/purchase/demand/list',
    method: 'get',
    params: query
  })
}

// 查询采购需求详细
export function getDemand(id) {
  return request({
    url: '/purchase/demand/getDemandById/' + id,
    method: 'get',
    headers: {
      isToken: false
    }
  })
}

// 查询私域需求
export function getPrivateDemand(id) {
  return request({
    url: '/system/private/getDemandById/' + id,
    method: 'get',
    headers: {
      isToken: false
    }
  })
}


// 发布采购需求
export function releaseDemand(data) {
  return request({
    url: '/purchase/demand/send',
    method: 'post',
    data: data
  })
}

// 发布采购需求
export function addDemand(data) {
  return request({
    url: '/purchase/demand',
    method: 'post',
    data: data
  })
}


// 修改采购需求
export function updateDemand(data) {
  return request({
    url: '/purchase/demand',
    method: 'put',
    data: data
  })
}

// 删除采购需求
export function delDemand(id) {
  return request({
    url: '/purchase/demand/' + id,
    method: 'delete'
  })
}
