import request from '@/utils/request'
/*
 * 标准产品分类
 */
export const getStandardCategory = params => {
  return request({
    url: '/fasten/standard/category/list',
    method: 'get',
    params
  })
}
/*
 * 标准页详情
 */
export const getStandardDetail = params => {
  return request({
    url: '/fasten/standard/info/detail',
    method: 'get',
    params
  })
}
/*
 * 标准列表
 */
export const getStandardList = params => {
  return request({
    url: '/fasten/standard/list',
    method: 'get',
    params
  })
}
/*
 * 标准分类
 */
export const getStandardClassify = params => {
  return request({
    url: '/fasten/standard/properties/list',
    method: 'get',
    params
  })
}
/**
 * 查询是否已收藏
 */
export const getStandardCollect = params => {
  return request({
    url: '/fasten/standard/is/store',
    method: 'get',
    params
  })
}
