import request from '@/utils/request'
// 公域供应商账期：设置账期
export function addSetdRceiptPeriod(data) {
  return request({
    url: '/purchasing/contract/set/receipt/period',
    method: 'post',
    data
  })
}
// 公域供应商账期：修改账期
export function updateSetReceiptPeriod(data) {
  return request({
    url: '/purchasing/contract/set/receipt/period',
    method: 'put',
    data
  })
}
// 公域供应商账期：查询签署过合同未设置账期的公域供应商
export function getSignedSupplierList(params) {
  return request({
    url: '/purchasing/contract/signed/supplier/list',
    method: 'get',
    params
  })
}
// 公域供应商账期：查询
export function getReceiptPeriod(params) {
  return request({
    url: '/purchasing/contract/get/receipt/period',
    method: 'get',
    params
  })
}
// 平台合同：申请付款
export function forSellerApply(data) {
    return request({
      url: '/purchasing/contract/for/seller/apply',
      method: 'put',
      data
    })
  }