import request from '@/utils/request'

// 获取路由
export const getRouters = () => {
  return request({
    url: '/getRouters',
    method: 'get'
  })
}
// 功能库：录入
export function menuCreate(data) {
  return request({
    url: '/com/function',
    method: 'post',
    data
  })
}
// 功能库：删除
export function menuDelete(data) {
  return request({
    url: '/com/function',
    method: 'delete',
    data
  })
}
// 功能库：查询
export function menuList(params) {
  return request({
    url: '/com/function/query',
    method: 'get',
    params
  })
}
// 功能库：排序
export function menuSort(data) {
  return request({
    url: '/com/function/sort',
    method: 'put',
    data
  })
}
