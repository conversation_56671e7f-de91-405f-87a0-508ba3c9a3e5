import request from '@/utils/request'
// 新闻分类列表
export function getArticleTypeList(params) {
  return request({
    url: '/news/type/list',
    method: 'get',
    params
  })
}
// 新增新闻分类
export function addArticleType(data) {
  return request({
    url: '/news/type',
    method: 'post',
    data
  })
}
// 删除新闻分类
export function deleteArticleType(params) {
  return request({
    url: '/news/type',
    method: 'delete',
    params
  })
}
// 修改新闻分类
export function updateArticleType(data) {
  return request({
    url: '/news/type',
    method: 'put',
    data
  })
}
// 外部新闻列表
export function getArticleOuterList(params) {
  return request({
    url: '/news/outer/list',
    method: 'get',
    params
  })
}
// 新闻列表
export function getArticleList(params) {
  return request({
    url: '/news/list',
    method: 'get',
    params
  })
}
// 新增新闻
export function addArticle(data) {
  return request({
    url: '/news',
    method: 'post',
    data
  })
}
// 删除新闻
export function deleteArticle(params) {
  return request({
    url: '/news',
    method: 'delete',
    params
  })
}
// 修改新闻
export function updateArticle(data) {
  return request({
    url: '/news',
    method: 'put',
    data
  })
}
// 首页新闻
export function getArticleHomeList(params) {
  return request({
    url: '/system/index/news',
    method: 'get',
    params
  })
}
// 新闻详情
export function getArticleDetail(params) {
  return request({
    url: '/news',
    method: 'get',
    params
  })
}
// 新闻推送
export function pushArticle(data) {
  return request({
    url: '/system/notice/app/send',
    method: 'post',
    data
  })
}
// 查询新闻评论
export function getArticleCommentList(params) {
  return request({
    url: '/news/comment',
    method: 'get',
    params
  })
}
// 新增新闻评论
export function addArticleComment(data) {
  return request({
    url: '/news/comment',
    method: 'post',
    data
  })
}
// 删除新闻评论
export function deleteArticleComment(params) {
  return request({
    url: '/news/comment',
    method: 'delete',
    params
  })
}
// 新闻点赞/吐槽
export function addArticleLike(data) {
  return request({
    url: '/news/like',
    method: 'post',
    data
  })
}
// 刷新新闻点赞/吐槽
export function updateArticleLike(params) {
  return request({
    url: '/news/like',
    method: 'get',
    params
  })
}
// 修改新闻推送状态
export function updateArticlePush(params) {
  return request({
    url: '/news/push',
    method: 'put',
    params
  })
}
