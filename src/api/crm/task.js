import request from '@/utils/request'

// 任务：新增
export function addCrmTask(data) {
  return request({
    url: '/crm/task',
    method: 'post',
    data
  })
}
// 任务: 列表
export function getTaskList(params) {
  return request({
    url: '/crm/task/list',
    method: 'get',
    params
  })
}
// 任务：取消
export function crmTaskCancel(data) {
  return request({
    url: '/crm/task/cancel',
    method: 'put',
    data
  })
}
// 任务：删除(无法恢复)
export function deletedTask(data) {
  return request({
    url: '/crm/task/delete',
    method: 'delete',
    data
  })
}
// 任务：完成
export function crmTaskDone(data) {
  return request({
    url: '/crm/task/done',
    method: 'put',
    data
  })
}
// 任务：未完成
export function crmTaskUndone(data) {
  return request({
    url: '/crm/task/unDone',
    method: 'put',
    data
  })
}
