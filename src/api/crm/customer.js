import request from '@/utils/request'

// 客户编辑
export function editCrmCustomer(data) {
  return request({
    url: '/crm/customer/edit',
    method: 'post',
    data
  })
}
// 客户查询跟进信息
export function getCrmCustomerFollow(params) {
  return request({
    url: '/crm/customer/follow/up',
    method: 'get',
    params
  })
}
// 客户写跟进
export function writeCrmCustomerFollow(data) {
  return request({
    url: '/crm/customer/follow/up',
    method: 'post',
    data
  })
}
// 客户详情
export function getCrmCustomerDetail(params) {
  return request({
    url: '/crm/customer/getById',
    method: 'get',
    params
  })
}
// 客户列表
export function getCrmCustomerList(params) {
  return request({
    url: '/crm/customer/list',
    method: 'get',
    params
  })
}
// 转交记录
export function getHandoverLog(params) {
  return request({
    url: '/crm/customer/get/handover/log',
    method: 'get',
    params
  })
}
// 客户转交
export function editHandover(data) {
  return request({
    url: '/crm/customer/handover',
    method: 'put',
    data
  })
}
// 客户删除
export function deletedCustomer(data) {
  return request({
    url: '/crm/customer/to/deleted',
    method: 'put',
    data
  })
}
// 设置参与人
export function setParticipants(data) {
  return request({
    url: '/crm/customer/set/participants',
    method: 'put',
    data
  })
}
// 转为公共客户
export function toPublic(data) {
  return request({
    url: '/crm/customer/to/public',
    method: 'put',
    data
  })
}
