import request from '@/utils/request'

// 增长率统计：新增客户
export function getStatisticsGrowthRateCustomer(params) {
  return request({
    url: '/crm/data/statistics/growthRate/customer',
    method: 'get',
    params
  })
}
// 增长率统计：成交总额
export function getStatisticsGrowthRateOrder(params) {
  return request({
    url: '/crm/data/statistics/growthRate/order',
    method: 'get',
    params
  })
}
// 增长率统计：回款总额
export function getStatisticsGrowthRateRepay(params) {
  return request({
    url: '/crm/data/statistics/growthRate/repay',
    method: 'get',
    params
  })
}
// 业绩排行:新增客户数
export function getStatisticsPerformanceWithCustomer(params) {
  return request({
    url: '/crm/data/statistics/performance/with/customer',
    method: 'get',
    params
  })
}
// 业绩排行:其他
export function getStatisticsPerformanceWithOther(params) {
  return request({
    url: '/crm/data/statistics/performance/with/other',
    method: 'get',
    params
  })
}
// 产品销量:按名称|类别
export function getStatisticsProductSales(params) {
  return request({
    url: '/crm/data/statistics/product/sales',
    method: 'get',
    params
  })
}
// 数据大屏：客户分析
export function getStatisticsScreenCustomerAnalysis(params) {
  return request({
    url: '/crm/data/statistics/screen/customer/analysis',
    method: 'get',
    params
  })
}
// 数据大屏：客户新增概览
export function getStatisticsScreenCustomerNewOverview(params) {
  return request({
    url: '/crm/data/statistics/screen/customer/newOverview',
    method: 'get',
    params
  })
}
// 数据大屏：{状态}客户数量
export function getStatisticsScreenCustomerWithStatus(params) {
  return request({
    url: '/crm/data/statistics/screen/customer/withStatus',
    method: 'get',
    params
  })
}
// 数据大屏：按时间统计
export function getStatisticsScreenGroupTime(params) {
  return request({
    url: '/crm/data/statistics/screen/group/time',
    method: 'get',
    params
  })
}
// 数据大屏：销售订单
export function getStatisticsScreenGroupOrder(params) {
  return request({
    url: '/crm/data/statistics/screen/order',
    method: 'get',
    params
  })
}
// 数据大屏：销售订单金额
export function getStatisticsScreenGroupOrderAmount(params) {
  return request({
    url: '/crm/data/statistics/screen/order/amount',
    method: 'get',
    params
  })
}
// 数据大屏：收款金额
export function getStatisticsScreenGroupReceivePaymentAmount(params) {
  return request({
    url: '/crm/data/statistics/screen/receive/payment/amount',
    method: 'get',
    params
  })
}
// 数据大屏：回款
export function getStatisticsScreenGroupRepay(params) {
  return request({
    url: '/crm/data/statistics/screen/repay',
    method: 'get',
    params
  })
}
// 数据大屏：回款金额
export function getStatisticsScreenGroupRepayAmount(params) {
  return request({
    url: '/crm/data/statistics/screen/repay/amount',
    method: 'get',
    params
  })
}
// 数据大屏：收款订单
export function getStatisticsScreenGroupRepayOrder(params) {
  return request({
    url: '/crm/data/statistics/screen/repay/order',
    method: 'get',
    params
  })
}

