import request from '@/utils/request'

// 审批：审批
export function putCrmApprove(data) {
  return request({
    url: '/crm/approve',
    method: 'put',
    data
  })
}
// 审批：所有任务
export function getAllTask(params) {
  return request({
    url: '/crm/approve/all/task',
    method: 'get',
    params
  })
}
// 审批模板：删除
export function deletedApprovalTemplate(data) {
  return request({
    url: '/crm/approve/approval/template',
    method: 'delete',
    data
  })
}
// 审批模板：查询内置
export function getApprovalTemplateBuildIn(params) {
  return request({
    url: '/crm/approve/approval/template/buildIn',
    method: 'get',
    params
  })
}
// 审批模板：默认审批人-抄送人维护
export function approvalTemplateDefaultUsers(data) {
  return request({
    url: '/crm/approve/approval/template/default/users',
    method: 'post',
    data
  })
}
// 审批模板：详情
export function getApprovalTemplateDetail(params) {
  return request({
    url: '/crm/approve/approval/template/detail',
    method: 'get',
    params
  })
}
// 审批模板：维护
export function approvalTemplateEdit(data) {
  return request({
    url: '/crm/approve/approval/template/edit',
    method: 'post',
    data
  })
}
// 审批模板：列表
export function getApprovalTemplateList(params) {
  return request({
    url: '/crm/approve/approval/template/list',
    method: 'get',
    params
  })
}
// 审批模板：标记常用
export function putApproveTemplateMark(data) {
  return request({
    url: '/crm/approve/approval/template/mark',
    method: 'put',
    data
  })
}
// 审批：抄送给我的
export function getApprovalCopyTask(params) {
  return request({
    url: '/crm/approve/copy/task',
    method: 'get',
    params
  })
}
// 审批：新建审批
export function putApproveCreate(data) {
  return request({
    url: '/crm/approve/create',
    method: 'put',
    data
  })
}
// 审批：任务删除
export function putApproveDeleteTask(data) {
  return request({
    url: '/crm/approve/delete/task',
    method: 'put',
    data
  })
}
// 审批：查询已办
export function getApprovalHistoryTaskForMe(params) {
  return request({
    url: '/crm/approve/history/task/for/me',
    method: 'get',
    params
  })
}
// 审批：我创建的
export function getApprovalOwnerTask(params) {
  return request({
    url: '/crm/approve/owner/task',
    method: 'get',
    params
  })
}
// 流程：新建
export function approvalProcessDef(data) {
  return request({
    url: '/crm/approve/process/def',
    method: 'post',
    data
  })
}
// 流程：修改
export function putApproveProcessDef(data) {
  return request({
    url: '/crm/approve/process/def',
    method: 'put',
    data
  })
}
// 流程：删除
export function deletedApprovalProcessDef(data) {
  return request({
    url: '/crm/approve/process/def',
    method: 'delete',
    data
  })
}
// 流程：列表
export function getApprovalProcessDefList(params) {
  return request({
    url: '/crm/approve/process/def/list',
    method: 'get',
    params
  })
}
// 审批：查看任务详情
export function getApprovalTaskDetail(params) {
  return request({
    url: '/crm/approve/task/detail',
    method: 'get',
    params
  })
}
// 审批：查询待办
export function getApprovalTaskList(params) {
  return request({
    url: '/crm/approve/task/list',
    method: 'get',
    params
  })
}
// 审批：待办转交
export function putApproveTaskTransfer(data) {
  return request({
    url: '/crm/approve/task/transfer',
    method: 'put',
    data
  })
}