import request from '@/utils/request'

// 字典编辑
export function editCrmDict(data) {
  return request({
    url: '/crm/dict/edit',
    method: 'post',
    data
  })
}
// 字典查询
export function getCrmDict(params) {
  return request({
    url: '/crm/dict/select',
    method: 'get',
    params
  })
}
// 字典联系人查询
export function getCrmDictContact(params) {
  return request({
    url: '/crm/dict/select2',
    method: 'get',
    params
  })
}
// 字典联系人编辑
export function editCrmDictContact(data) {
  return request({
    url: '/crm/liaison/edit',
    method: 'post',
    data
  })
}
