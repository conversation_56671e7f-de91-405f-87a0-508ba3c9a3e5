import request from '@/utils/request'
// 新增招标需求
export function addBidding(data) {
  return request({
    url: '/bidding/demand',
    method: 'post',
    data
  })
}
// 调整招标需求
export function updateBidding(data) {
  return request({
    url: '/bidding/demand',
    method: 'put',
    data
  })
}
// 招标需求详情
export function detailBidding(params) {
  return request({
    url: '/bidding/demand/detail',
    method: 'get',
    params
  })
}
// 下架-删除招标需求
export function deleteBidding(params) {
  return request({
    url: '/bidding/demand/detail',
    method: 'delete',
    params
  })
}
// 批量产品导入招标需求(已用）
// export function importBidding(data) {
//   return request({
//     url: '/bidding/demand/importData',
//     method: 'post',
//     data
//   })
// }
// 招标需求模板下载(已用）
// export function downloadBiddingTemplate() {
//   return request({
//     url: '/bidding/demand/importTemplate',
//     method: 'post'
//   })
// }
// 招标需求列表--首页
export function indexListBidding(params) {
  return request({
    url: '/bidding/demand/index/list',
    method: 'get',
    params
  })
}
// 招标需求列表
export function listBidding(params) {
  return request({
    url: '/bidding/demand/list',
    method: 'get',
    params
  })
}
// 招标需求产品详情
export function detailBiddingProduct(params) {
  return request({
    url: '/bidding/demand/product',
    method: 'get',
    params
  })
}
// 招标需求所有回复
export function allReplyBidding(params) {
  return request({
    url: '/bidding/demand/reply',
    method: 'get',
    params
  })
}
// 招标需求回复（仅限企业用户）
export function replyBidding(data) {
  return request({
    url: '/bidding/demand/reply',
    method: 'post',
    data
  })
}
// 首页招标需求--查询登陆人回复
export function replyBiddingByUser(params) {
  return request({
    url: '/bidding/demand/reply/withMe',
    method: 'get',
    params
  })
}
// 招标需求审核列表
export function auditListBidding(params) {
  return request({
    url: '/system/verify/bidding/demand/list',
    method: 'get',
    params
  })
}
// 招标需求审核
export function auditBidding(data) {
  return request({
    url: '/system/verify/bidding/demand',
    method: 'put',
    data
  })
}
// 修改招标需求产品
export function updateBiddingProduct(data) {
  return request({
    url: '/bidding/demand/product',
    method: 'put',
    data
  })
}
