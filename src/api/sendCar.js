import request from '@/utils/request'
/*
 * 车辆新增
 * model: 车型
 * name: 车辆名称
 * plateNumber: 车牌号
 */
export function addCar(data) {
  return request({
    url: '/dispatch/car',
    method: 'post',
    data
  })
}
/**
 * 车辆列表
 * name: 车辆名称
 * plateNumber: 车牌号
 * status: 状态
 */
export function getCarList(params) {
  return request({
    url: '/dispatch/car/list',
    method: 'get',
    params
  })
}
/**
 * 车辆状态修改
 * id: 车辆id
 * status: 状态
 */
export function updateCarStatus(data) {
  return request({
    url: '/dispatch/car/status',
    method: 'put',
    data
  })
}
/**
 * 派车单新增
 */
export function addSendCar(data) {
  return request({
    url: '/dispatch/car/document',
    method: 'post',
    data
  })
}
/**
 * 派车单列表
 */
export function getSendCarList(params) {
  return request({
    url: '/dispatch/car/document/list',
    method: 'get',
    params
  })
}
/**
 * 派车单修改
 */
export function updateSendCar(data) {
  return request({
    url: '/dispatch/car/document',
    method: 'put',
    data
  })
}
/**
 * 派车单导出
 */
export function exportSendCar(data) {
  return request({
    url: '/dispatch/car/document/export',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'
    },
    data
  })
}
/**
 * 派车单状态修改
 */
export function updateSendCarStatus(data) {
  return request({
    url: '/dispatch/car/document/status',
    method: 'put',
    data
  })
}
/**
 * 车辆: gps序列号修改
 */
export function updateCarGpsNumber(data) {
  return request({
    url: '/dispatch/car/gpsNumber',
    method: 'put',
    data
  })
}
