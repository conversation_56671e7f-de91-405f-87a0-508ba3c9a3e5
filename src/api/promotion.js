import request from '@/utils/request'
// 查询促销列表
export function getPromotionList(params) {
  return request({
    url: '/promotion/product/list',
    method: 'get',
    params
  })
}
// 新增促销产品
export function addPromotion(data) {
  return request({
    url: '/promotion/product',
    method: 'post',
    data
  })
}
// 删除促销产品
export function deletePromotion(params) {
  return request({
    url: '/promotion/product',
    method: 'delete',
    params
  })
}
// 修改促销产品
export function updatePromotion(data) {
  return request({
    url: '/promotion/product',
    method: 'put',
    data
  })
}
// 查询促销产品
export function getPromotion(params) {
  return request({
    url: '/promotion/product',
    method: 'get',
    params
  })
}
// 首页促销产品列表
export function getPromotionIndexList(params) {
  return request({
    url: '/system/index/promotion',
    method: 'get',
    params
  })
}
// 产品详情
export function getPromotionProductDetail(Id) {
  return request({
    url: '/system/product/' + Id,
    method: 'get'
  })
}
