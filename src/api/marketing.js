import request from '@/utils/request'
// 分类列表
export function categoryList(params) {
  return request({
    url: '/marketing/class/list',
    method: 'get',
    params
  })
}
// 新增分类
export function categoryAdd(data) {
  return request({
    url: '/marketing/class',
    method: 'post',
    data
  })
}
// 修改分类
export function categoryEdit(data) {
  return request({
    url: '/marketing/class',
    method: 'put',
    data
  })
}
// 删除分类
export function categoryDel(params) {
  return request({
    url: '/marketing/class',
    method: 'delete',
    params
  })
}
// 客户列表
export function customerList(params) {
  return request({
    url: '/marketing/custom/list',
    method: 'get',
    params
  })
}
// 新增客户
export function customerAdd(data) {
  return request({
    url: '/marketing/custom',
    method: 'post',
    data
  })
}
// 修改客户
export function customerEdit(data) {
  return request({
    url: '/marketing/custom',
    method: 'put',
    data
  })
}
// 删除客户
export function customerDel(params) {
  return request({
    url: '/marketing/custom',
    method: 'delete',
    params
  })
}
// 短信模板列表
export function smsTemplateList(params) {
  return request({
    url: '/marketing/message/template/list',
    method: 'get',
    params
  })
}
// 新增短信模板
export function smsTemplateAdd(data) {
  return request({
    url: '/marketing/message/template',
    method: 'post',
    data
  })
}
// 修改短信模板
export function smsTemplateEdit(data) {
  return request({
    url: '/marketing/message/template',
    method: 'put',
    data
  })
}
// 删除短信模板
export function smsTemplateDel(params) {
  return request({
    url: '/marketing/message/template',
    method: 'delete',
    params
  })
}
// 刷新短信模板审核状态(状态为0时才需要调用)
export function smsTemplateRefresh(params) {
  return request({
    url: '/marketing/message/refresh',
    method: 'get',
    params
  })
}
// 短信列表
export function smsList(params) {
  return request({
    url: '/marketing/message/list',
    method: 'get',
    params
  })
}
// 新建短信-新建定时发送短信
export function smsAdd(data) {
  return request({
    url: '/marketing/message',
    method: 'post',
    data
  })
}
// 发送短信
export function smsSend(params) {
  return request({
    url: '/marketing/message/send',
    method: 'post',
    params
  })
}
// 修改短信定时发送时间
export function smsEdit(data) {
  return request({
    url: '/marketing/schedule',
    method: 'put',
    data
  })
}
// 短信回执列表
export function smsReceiptList(params) {
  return request({
    url: '/marketing/report/list',
    method: 'get',
    params
  })
}
// 短信回执
export function smsReceipt(params) {
  return request({
    url: '/marketing/report/list',
    method: 'get',
    params
  })
}
