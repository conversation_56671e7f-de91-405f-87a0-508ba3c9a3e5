import request from '@/utils/request'
// 登录方法
export function login(username, password, code, uuid) {
  const data = {
    username,
    password,
    code,
    uuid
  }
  return request({
    url: '/login',
    headers: {
      isToken: false
    },
    method: 'post',
    data: data
  })
}
// 注册方法
export function register(data) {
  return request({
    url: '/register',
    headers: {
      isToken: false
    },
    method: 'post',
    data: data
  })
}
// 获取用户详细信息
export function getInfo() {
  return request({
    url: '/getInfo',
    method: 'get'
  })
}
// 退出方法
export function logout() {
  return request({
    url: '/logout',
    method: 'post',
    headers: {
      isToken: false
    }
  })
}
// 获取验证码
export function getCodeImg() {
  return request({
    url: '/captchaImage',
    headers: {
      isToken: false
    },
    method: 'get',
    timeout: 20000
  })
}
// 获取招投标查看期限
export function getTenderTime() {
  return request({
    url: '/bidding/deadline',
    method: 'get'
  })
}
// 微信授权登录
/**
 * @param {String} code
 * @param {String} state
 * @param {String} authSource wechat_open_pc 微信网页，wechat_open_app 微信app，alipay_pc 支付宝网页，alipay_app 支付宝app
 */
export function authLogin(params) {
  return request({
    url: '/auth3/login',
    method: 'get',
    params
  })
}
// 获取state
export function getState() {
  return request({
    url: '/auth3/state',
    method: 'get'
  })
}
// 短信验证码登录
export function smsLogin(data) {
  return request({
    url: '/login/phone',
    method: 'post',
    data
  })
}
// 第三方扫码登录后绑定手机号搜索信息
export function searchUsePhone(params) {
  return request({
    url: '/system/user/auth/user/bind/search',
    method: 'get',
    params
  })
}
// 第三方绑定手机号
export function putAuthbindPhone(data) {
  return request({
    url: '/system/user/profile/auth/user/bind/phone',
    method: 'put',
    data
  })
}
