import request from '@/utils/request'
// 工作交接：接手|撤回
export function workHandover(data) {
  return request({
    url: '/work/handover',
    method: 'put',
    data
  })
}
// 工作交接：添加
export function workHandoverAdd(data) {
  return request({
    url: '/work/handover/add',
    method: 'post',
    data
  })
}
// 工作交接：删除
export function workHandoverDelete(params) {
  return request({
    url: '/work/handover/delete',
    method: 'delete',
    params
  })
}
// 工作交接：列表
export function workHandoverList(params) {
  return request({
    url: '/work/handover/list',
    method: 'get',
    params
  })
}
// 工作交接：修改
export function workHandoverUpdate(data) {
  return request({
    url: '/work/handover/update',
    method: 'put',
    data
  })
}
// 工作交接：今日请假人数
export function workHandoverTodayLeave(params) {
  return request({
    url: '/work/handover/askForLeave/list',
    method: 'get',
    params
  })
}
