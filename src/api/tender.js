import request from '@/utils/request'
// 招投标列表
export function getTenderList(params) {
  return request({
    url: '/bidding/list',
    method: 'get',
    params
  })
}
// 查询关键词列表
export function getTenderKeywordList(params) {
  return request({
    url: '/bidding/keyword/list',
    method: 'get',
    params
  })
}
// 查询关键词是否存在
export function getTenderKeywordExist(params) {
  return request({
    url: '/bidding/keyword/exist',
    method: 'get',
    params
  })
}
// 查询关键词
export function getTenderKeyword(params) {
  return request({
    url: '/bidding/keyword',
    method: 'get',
    params
  })
}
// 新增关键词
export function addTenderKeyword(data) {
  return request({
    url: '/bidding/keyword',
    method: 'post',
    data
  })
}
// 删除关键词
export function deleteTenderKeyword(params) {
  return request({
    url: '/bidding/keyword',
    method: 'delete',
    params
  })
}
// 设置显示
export function setTenderShow(data) {
  return request({
    url: '/bidding/keyword',
    method: 'put',
    data
  })
}
// 招投标详情
export function getTenderDetail(params) {
  return request({
    url: '/bidding/detail',
    method: 'get',
    params
  })
}
// 招投标详情免登录
export function getTenderDetailNoLogin(params) {
  return request({
    url: '/system/index/bidding/detail',
    method: 'get',
    params
  })
}
// 河北招投标列表
export function getHebeiTenderList(params) {
  return request({
    url: '/bidding/hb/list',
    method: 'get',
    params
  })
}
// 河北招投标详情
export function getHebeiTenderDetail(params) {
  return request({
    url: '/bidding/hb/detail',
    method: 'get',
    params
  })
}
// 根据关键词查询公司信息
export function getCompanyInfo(params) {
  return request({
    url: '/data/search',
    method: 'get',
    params,
    baseURL: '/dataApi'
  })
}
// 远程搜索公司信息
export function getRemoteCompanyInfo(params) {
  return request({
    url: '/system/index/enterprise/remote/search',
    method: 'get',
    params
  })
}
// 根据公司名称查询详细信息
export function getCompanyDetail(params) {
  return request({
    url: '/companyBase/search',
    method: 'get',
    params,
    baseURL: '/dataApi'
  })
}
// 根据公司名称实时查询详细信息
export function getRealCompanyDetail(params) {
  return request({
    url: '/companyBase/getNewCompany',
    method: 'get',
    params,
    baseURL: '/realApi'
  })
}
// 根据输入内容模糊查询公司列表
export function getCompanyList(params) {
  return request({
    url: '/companyBase/searchMatch',
    method: 'get',
    params,
    baseURL: '/dataApi'
  })
}
/**
 * 招投标列表V2
 * params:
 * pageNum: 当前页码
 * pageSize: 每页条数
 * search: 搜索关键词
 * containsAll: 多个关键词是否包含全部
 * scope: 搜索范围
 * area: 省份
 * city: 城市
 * district: 区县
 * industry: 行业
 * subType: 招标公告
 * topType: 招标结果
 * term: 时间范围
 * startAmount: 开始金额
 * endAmount: 结束金额
 */
export function getTenderListV2(data) {
  return request({
    url: '/bidding/list/v2',
    method: 'post',
    data
  })
}
/**
 * 招标详情V2
 * params:
 * id: 招标ID
 */
export function getTenderDetailV2(params) {
  return request({
    url: '/bidding/detail/v2',
    method: 'get',
    params
  })
}
/**
 * 排行榜
 * params:
 * search: 搜索内容
 * containsAll: 多个关键词是否包含全部
 * scope: 搜索范围
 * area: 省份
 * city: 城市
 * district: 区县
 * industry: 行业
 * term: 时间范围
 * order: 排序 1-成交次数 2-金额,可用值:1,2
 */
export function getTenderRanking(data) {
  return request({
    url: '/bidding/ranking',
    method: 'post',
    data
  })
}
// 查询标书评论
export function getTenderComment(params) {
  return request({
    url: '/bidding/comment',
    method: 'get',
    params
  })
}
// 新增标书评论
// //评论目标 pt-发布平台 zab-招标单位 zob-中标单位 nr-内容
export function addTenderComment(data) {
  return request({
    url: '/bidding/comment',
    method: 'post',
    data
  })
}
// 删除标书评论
export function deleteTenderComment(params) {
  return request({
    url: '/bidding/comment',
    method: 'delete',
    params
  })
}
// 查看常用标签
export function getTenderLabel(params) {
  return request({
    url: '/bidding/comment/label',
    method: 'get',
    params
  })
}
// 新增常用标签
export function addTenderLabel(data) {
  return request({
    url: '/bidding/comment/label',
    method: 'post',
    data
  })
}
// 删除常用标签
export function deleteTenderLabel(data) {
  return request({
    url: '/bidding/comment/label',
    method: 'delete',
    data
  })
}
// 查询标书吐槽/点赞
export function getTenderLike(params) {
  return request({
    url: '/bidding/like',
    method: 'get',
    params
  })
}
// 标书评论点赞
// action 动作 like-点赞 belittle-贬低
export function addTenderLike(data) {
  return request({
    url: '/bidding/like',
    method: 'post',
    data
  })
}
