import request from '@/utils/request'
// 需求统计
export function demandStatistics(params) {
  return request({
    url: '/dashboard/demand',
    method: 'get',
    params
  })
}
// 合同统计
export function contractStatistics(params) {
  return request({
    url: '/dashboard/contract',
    method: 'get',
    params
  })
}
// 成交统计
export function dealStatistics(params) {
  return request({
    url: '/dashboard/deal',
    method: 'get',
    params
  })
}
// 用户统计
export function userStatistics(params) {
  return request({
    url: '/dashboard/user',
    method: 'get',
    params
  })
}
// 报价统计
export function quoteStatistics(params) {
  return request({
    url: '/dashboard/quote',
    method: 'get',
    params
  })
}
// 私域合同统计
export function privateContractStatistics(params) {
  return request({
    url: '/dashboard/self/contract',
    method: 'get',
    params
  })
}
// 私域成交统计
export function privateDealStatistics(params) {
  return request({
    url: '/dashboard/self/deal',
    method: 'get',
    params
  })
}
// 私域需求统计
export function privateDemandStatistics(params) {
  return request({
    url: '/dashboard/self/demand',
    method: 'get',
    params
  })
}
// 私域报价统计
export function privateQuoteStatistics(params) {
  return request({
    url: '/dashboard/self/quote',
    method: 'get',
    params
  })
}
// 私域用户统计
export function privateUserStatistics(params) {
  return request({
    url: '/dashboard/self/user',
    method: 'get',
    params
  })
}
// 私域产品成交统计
/*
 * @param {Object} params
 * @param {String} params.ft s单月 m复月
 * @param {String} params.month 月份
 * @param {String} params.pre 开始月份
 */
export function privateProductDealStatistics(params) {
  return request({
    url: '/dashboard/self/product/deal',
    method: 'get',
    params
  })
}
// 短信统计
export function privateMarketingStatistics(params) {
  return request({
    url: '/dashboard/marketing/statistics',
    method: 'get',
    params
  })
}
// 私域企业统计
export function privateSupplierDeals(params) {
  return request({
    url: '/dashboard/self/supplier/deal',
    method: 'get',
    params
  })
}