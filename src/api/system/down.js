import request from '@/utils/request'

// 查询下侧广告列表
export function listDown(query) {
  return request({
    url: '/system/down/list',
    method: 'get',
    params: query
  })
}

// 查询下侧广告详细
export function getDown(id) {
  return request({
    url: '/system/down/' + id,
    method: 'get'
  })
}

// 新增下侧广告
export function addDown(data) {
  return request({
    url: '/system/down',
    method: 'post',
    data: data
  })
}

// 修改下侧广告
export function updateDown(data) {
  return request({
    url: '/system/down',
    method: 'put',
    data: data
  })
}

// 删除下侧广告
export function delDown(id) {
  return request({
    url: '/system/down/' + id,
    method: 'delete'
  })
}
