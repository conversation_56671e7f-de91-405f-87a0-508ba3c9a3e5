import request from '@/utils/request'
import { parseStrEmpty } from '@/utils/ruoyi'
// 注册用户
export function registerUser(data) {
  return request({
    url: '/register/user',
    method: 'post',
    data: data
  })
}
//企业注册
export function registerCompany(data) {
  return request({
    url: '/register/company',
    method: 'post',
    data: data
  })
}
// 绑定微信
export function qrcode(data) {
  return request({
    url: '/common/wechat/qrcode',
    method: 'get',
    params: data
  })
}
//供应商详情
export function supplier(data) {
  return request({
    url: '/purchase/supplier',
    method: 'get',
    params: data
  })
}
//供应商详情
export function ediSupplier(data) {
  return request({
    url: '/purchase/supplier',
    method: 'put',
    data: data
  })
}
//雷达图
export function chart(data) {
  return request({
    url: '/purchase/comment/chart',
    method: 'get',
    params: data
  })
}
// 发送验证码
export function smsCode(data) {
  return request({
    url: '/common/send/smsCode',
    method: 'post',
    params: data
  })
}
// 发送邮箱验证码
export function sendEmailCode(params) {
  return request({
    url: '/common/send/mailCode',
    method: 'post',
    params
  })
}
// 查询用户列表
export function listUser(query) {
  return request({
    url: '/system/user/list',
    method: 'get',
    params: query
  })
}
// 查询用户详细
export function getUser(userId) {
  return request({
    url: '/system/user/' + parseStrEmpty(userId),
    method: 'get'
  })
}
// 新增用户
export function addUser(data) {
  return request({
    url: '/system/user',
    method: 'post',
    data: data
  })
}
// 修改用户
export function updateUser(data) {
  return request({
    url: '/system/user',
    method: 'put',
    data: data
  })
}
// 删除用户
export function delUser(userId) {
  return request({
    url: '/system/user/' + userId,
    method: 'delete'
  })
}
// 用户密码重置
export function resetUserPwd(userId, password) {
  const data = {
    userId,
    password
  }
  return request({
    url: '/system/user/resetPwd',
    method: 'put',
    data: data
  })
}
// 用户状态修改
export function changeUserStatus(userId, status) {
  const data = {
    userId,
    status
  }
  return request({
    url: '/system/user/changeStatus',
    method: 'put',
    data: data
  })
}
// 查询用户个人信息
export function getUserProfile() {
  return request({
    url: '/system/user/profile',
    method: 'get'
  })
}
// 修改用户个人信息
export function updateUserProfile(data) {
  return request({
    url: '/system/user/profile',
    method: 'put',
    data: data
  })
}
// 用户密码重置
export function updateUserPwd(oldPassword, newPassword) {
  const data = {
    oldPassword,
    newPassword
  }
  return request({
    url: '/system/user/profile/updatePwd',
    method: 'put',
    params: data
  })
}
// 用户头像上传
export function uploadAvatar(data) {
  return request({
    url: '/system/user/profile/avatar',
    method: 'post',
    data: data
  })
}
// 查询授权角色
export function getAuthRole(userId) {
  return request({
    url: '/system/user/authRole/' + userId,
    method: 'get'
  })
}
// 保存授权角色
export function updateAuthRole(data) {
  return request({
    url: '/system/user/authRole',
    method: 'put',
    params: data
  })
}
// 查询部门下拉树结构
export function deptTreeSelect() {
  return request({
    url: '/system/dept/tree/select',
    method: 'get'
  })
}
// 邀请用户搜索
export function inviteUserSearch(params) {
  return request({
    url: '/system/user/invite/search',
    method: 'get',
    params
  })
}
// 邀请用户
export function inviteUser(data) {
  return request({
    url: '/system/user/invite',
    method: 'post',
    data
  })
}
// 查询是否有邀请
export function inviteHas(params) {
  return request({
    url: '/system/user/has/invite',
    method: 'get',
    params
  })
}
// 用户确认邀请
export function inviteVerify(data) {
  return request({
    url: '/system/user/invite/confirm',
    method: 'post',
    data
  })
}
// 个人用户升级为供应商
export function userUpgrade(data) {
  return request({
    url: '/upgrade/company',
    method: 'post',
    data
  })
}
// 查询品牌
export function userBrandList(params) {
  return request({
    url: '/sys/brand/list',
    method: 'get',
    params
  })
}
// 新增品牌
export function userBrandAdd(data) {
  return request({
    url: '/sys/brand',
    method: 'post',
    data
  })
}
// 删除品牌
export function userBrandDel(data) {
  return request({
    url: '/sys/brand/',
    method: 'delete',
    data
  })
}
// 升级为VIP
export function userUpgradeVip(data) {
  return request({
    url: '/purchase/supplier/applyForVip',
    method: 'put',
    data
  })
}
// 重置密码
export function forgotPassword(data) {
  return request({
    url: '/reset/password',
    method: 'post',
    data
  })
}
// 申请企业认证
export function companyAuth(data) {
  return request({
    url: '/purchase/supplier/applyForCertify',
    method: 'put',
    data
  })
}
// 修改个人产品分类排序
export function updateSort(data) {
  return request({
    url: '/uoc/config/top/category',
    method: 'put',
    data
  })
}
// 设为企业客服
export function setService(data) {
  return request({
    url: '/jim/customer',
    method: 'put',
    data
  })
}
// 查询当前企业设置的企业客服
export function getService(params) {
  return request({
    url: '/jim/customer/info',
    method: 'get',
    params
  })
}
// 客服搜索用户
export function searchUser(params) {
  return request({
    url: '/jim/user/search',
    method: 'get',
    params
  })
}
// 查询供应商联系人信息
export function getSupplierContact(params) {
  return request({
    url: '/supplier/contact',
    method: 'get',
    params
  })
}
// 修改供应商联系人信息
export function updateSupplierContact(data) {
  return request({
    url: '/supplier/contact',
    method: 'put',
    data
  })
}
// 新增联系人（供应商）
export function addSupplierContact(data) {
  return request({
    url: '/supplier/contact',
    method: 'post',
    data
  })
}
// 文本翻译
// 参数：{sourceLanguage: '原文语言', sourceText: '翻译内容', targetLanguage: '翻译语言'}
export function translateText(data) {
  return request({
    url: '/common/translate',
    method: 'post',
    data
  })
}
