import request from '@/utils/request'

// 查询左侧广告列表
export function listLeft(query) {
  return request({
    url: '/system/left/list',
    method: 'get',
    params: query
  })
}

// 查询左侧广告详细
export function getLeft(id) {
  return request({
    url: '/system/left/' + id,
    method: 'get'
  })
}

// 新增左侧广告
export function addLeft(data) {
  return request({
    url: '/system/left',
    method: 'post',
    data: data
  })
}

// 修改左侧广告
export function updateLeft(data) {
  return request({
    url: '/system/left',
    method: 'put',
    data: data
  })
}

// 删除左侧广告
export function delLeft(id) {
  return request({
    url: '/system/left/' + id,
    method: 'delete'
  })
}
