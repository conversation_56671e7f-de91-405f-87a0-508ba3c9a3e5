import request from "@/utils/request";

// 甄选产品请求授权批次列表
export function listOptimal(query) {
  return request({
    url: "/system/verify/optimal/apply/list",
    method: "get",
    params: query,
  });
}

// 甄选产品请求授权批次详情
export function getOptimalDetail(query) {
  return request({
    url: "/system/verify/optimal/batch/detail",
    method: "get",
    params: query,
  });
}

// 甄选产品请求授权审核
export function optimalVerify(data) {
  return request({
    url: "/system/verify/optimal/verify",
    method: "put",
    data: data,
  });
}
