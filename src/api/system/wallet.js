import request from '@/utils/request'

// 查询账户余额
export function walletBalance(params) {
  return request({
    url: '/wallet/balance',
    method: 'get',
    params
  })
}

// 查询充值消费记录
export function walletLogList(params) {
  return request({
    url: '/wallet/log/list',
    method: 'get',
    params
  })
}

// 查询充值订单
export function walletRechargeOrder(params) {
  return request({
    url: '/wallet/query/recharge/order',
    method: 'get',
    params
  })
}

// 查询退款订单
export function walletRefundOrder(params) {
  return request({
    url: '/wallet/query/refund/order',
    method: 'get',
    params
  })
}

// 账户充值状态
export function walletRecharge(data) {
  return request({
    url: '/wallet/recharge',
    method: 'post',
    data
  })
}

// 账户退款状态
export function walletRefund(data) {
  return request({
    url: '/wallet/refund',
    method: 'post',
    data
  })
}

// 查询充值订单详情
export function walletOrderDetail(params) {
  return request({
    url: '/wallet/recharge/detail',
    method: 'get',
    params
  })
}

// 查询充值订单退款明细
export function walletRefundList(params) {
  return request({
    url: '/wallet/recharge/refund/list',
    method: 'get',
    params
  })
}
