import request from '@/utils/request'
// 查询私域产品列表
export function listPrivateduct(query) {
  return request({
    url: '/system/privateduct/list',
    method: 'get',
    params: query
  })
}
// 查询私域产品列表2(关联查询使用)
export function listPrivateduct2(query) {
  return request({
    url: '/system/privateduct/quote',
    method: 'get',
    params: query
  })
}
// 查询私域产品详细
export function getPrivateduct(id) {
  return request({
    url: '/system/privateduct/' + id,
    method: 'get'
  })
}
// 新增私域产品
export function addPrivateduct(data) {
  return request({
    url: '/system/privateduct',
    method: 'post',
    data: data
  })
}
// 修改私域产品
export function updatePrivateduct(data) {
  return request({
    url: '/system/privateduct',
    method: 'put',
    data: data
  })
}
// 删除私域产品
export function delPrivateduct(id) {
  return request({
    url: '/system/privateduct/' + id,
    method: 'delete'
  })
}
//上下架  1   0
export function shangxia(query) {
  return request({
    url: '/system/privateduct/online/switch',
    method: 'PUT',
    params: query
  })
}
// 推荐到公域产品
export function recommendProduct(params) {
  return request({
    url: '/system/privateduct/recommend',
    method: 'post',
    params
  })
}
// 我的推荐产品
export function recommendList(params) {
  return request({
    url: '/system/product/recommend',
    method: 'get',
    params
  })
}
// 共享给他人
export function shareProduct(data) {
  return request({
    url: '/system/privateduct/assign/to',
    method: 'put',
    data
  })
}
// 修改产品值修改图片
export function updateProductImg(data) {
  return request({
    url: '/system/privateduct/img',
    method: 'put',
    data
  })
}
