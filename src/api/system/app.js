import request from '@/utils/request'
// 列表
export function getBannerList(params) {
  return request({
    url: '/sys/banner/list',
    method: 'get',
    params
  })
}
// 新增
export function addBanner(data) {
  return request({
    url: '/sys/banner',
    method: 'post',
    data
  })
}
// 删除
export function deleteBanner(params) {
  return request({
    url: '/sys/banner',
    method: 'delete',
    params
  })
}
// 编辑
export function editBanner(data) {
  return request({
    url: '/sys/banner',
    method: 'put',
    data
  })
}
