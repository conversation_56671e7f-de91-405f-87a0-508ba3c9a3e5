import request from "@/utils/request";

// 查询产品列表
export function listProduct(query) {
  return request({
    url: "/system/product/list",
    method: "get",
    params: query,
  });
}

// 查询产品详细
export function getProduct(id) {
  return request({
    url: "/system/product/" + id,
    method: "get",
  });
}

// 新增产品
export function addProduct(data) {
  return request({
    url: "/system/product",
    method: "post",
    data: data,
  });
}

// 修改产品
export function updateProduct(data) {
  return request({
    url: "/system/product",
    method: "put",
    data: data,
  });
}

// 删除产品
export function delProduct(id) {
  return request({
    url: "/system/product/" + id,
    method: "delete",
  });
}

// 获取product-pc 热门产品
export function getHotProduct(query) {
  return request({
    url: "/system/index/getHotProduct",
    method: "get",
    params: query,
    headers: {
      isToken: false,
    },
  });
}
// 获取product-pc 新增产品
export function getNewProducts(query) {
  return request({
    url: "/system/index/getNewProducts",
    method: "get",
    params: query,
    headers: {
      isToken: false,
    },
  });
}
// 获取product-pc 消息
export function getNewMessage(query) {
  return request({
    url: "/system/index/getNewMessage",
    method: "get",
    params: query,
    headers: {
      isToken: false,
    },
  });
}
// 获取product-pc 公司
export function getRelevanceCompany(query) {
  return request({
    url: "/system/index/getRelevanceCompany",
    method: "get",
    params: query,
    headers: {
      isToken: false,
    },
  });
}

// 获取左侧广告

export function getLeftAd(query) {
  return request({
    url: "/system/index/getLeftAd",
    method: "get",
    params: query,
    headers: {
      isToken: false,
    },
  });
}


// 获取下侧广告
export function getDownAd(query) {
  return request({
    url: "/system/index/getDownAd",
    method: "get",
    params: query,
    headers: {
      isToken: false,
    },
  });
}

// 获取一级分类

export function getFirstLeveMenu(query) {
  return request({
    url: "/system/index/getFirstLeveMenu",
    method: "get",
    params: query,
    headers: {
      isToken: false,
    },
  });
}


export function getSubmenu(query) {
  return request({
    url: "/system/index/getSubmenu",
    method: "get",
    params: query,
    headers: {
      isToken: false,
    },
  });
}
export function getTypeProducts(query) {
  return request({
    url: "/system/index/getTypeProducts",
    method: "get",
    params: query,
    headers: {
      isToken: false,
    },
  });
}

export function getTypeNewProducts(query) {
  return request({
    url: "/system/index/getTypeNewProducts",
    method: "get",
    params: query,
    headers: {
      isToken: false,
    },
  });
}


export function getProductsByName(query) {
  return request({
    url: "/system/index/getProductsByName",
    method: "get",
    params: query,
    headers: {
      isToken: false,
    },
  });
}


export function getAllCate(query) {
  return request({
    url: "/system/index/category/all",
    method: "get",
    params: query,
    headers: {
      isToken: false,
    },
  });
}

// 查看产品是否已收藏
export function productCollect(params) {
  return request({
    url: '/system/product/is/store',
    method: 'get',
    params
  })
}

// 公域产品引入到私域产品
export function publicToPrivate(data) {
  return request({
    url: '/system/privateduct/introduce',
    method: 'post',
    data
  })
}

// 查询该公域产品是否已经引入到私域
export function isPublicToPrivate(params) {
  return request({
    url: '/system/privateduct/check/introduce',
    method: 'get',
    params
  })
}

// 设置产品是否热门
export function changeProductHot(data) {
  return request({
    url: '/system/product/hot/setOrCancel',
    method: 'put',
    data
  })
}

// 设置产品是否国标甄选
export function changeProductOptimal(data) {
  return request({
    url: '/system/product/optimal/setOrCancel',
    method: 'put',
    data
  })
}

// 甄选产品请求授权
export function productOptimalApply(data) {
  return request({
    url: '/system/product/optimal/apply',
    method: 'post',
    data
  })
}

// 查询已通过或者申请中甄选产品id
export function productOptimalPassedIds() {
  return request({
    url: '/system/product/optimal/passed/ids',
    method: 'get',
  })
}
// 查询已通过申请甄选产品
export function productOptimalPassedList() {
  return request({
    url: '/system/product/optimal/passed/list',
    method: 'get',
  })
}