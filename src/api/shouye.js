import request from '@/utils/request'

export function editlist(query) {
  return request({
    url: '/purchase/supplier',
    method: 'put',
    data: query
  })
}
// 查询最新升级公告
export function checkVersionLast(params) {
  return request({
    url: '/system/index/version/lasted',
    method: 'get',
    params
  })
}
// 查询历史版本公告
export function checkVersionList(params) {
  return request({
    url: '/system/index/version/list',
    method: 'get',
    params
  })
}
// 列表
// export function getsteels(query) {
//   return request({
//     url: "/system/product/prepare/list",
//     method: "get",
//     params: query,
//   });
// }
// 首页品牌列表
export function homeBrandList(params) {
  return request({
    url: '/system/index/brand/list',
    method: 'get',
    params
  })
}
// 首页求购信息列表
export function homePurchaseList(params) {
  return request({
    url: '/system/index/buying/leads/list',
    method: 'get',
    params
  })
}
