import request from '@/utils/request'
// 采购清单列表
export function purchasingList(params) {
  return request({
    url: '/purchasing/list',
    method: 'get',
    params
  })
}
// 采购清单分组列表(订单编号)
export function purchasingListGroup(params) {
  return request({
    url: '/purchasing/group/order/list',
    method: 'get',
    params
  })
}
// 新增采购清单
export function purchasingAdd(data) {
  return request({
    url: '/purchasing',
    method: 'post',
    data
  })
}
// 删除采购清单
export function purchasingDelete(data) {
  return request({
    url: '/purchasing',
    method: 'delete',
    data
  })
}
// 搜索关联产品
export function purchasingProduct(params) {
  return request({
    url: '/purchasing/product/search',
    method: 'get',
    params
  })
}
// 采购清单分组列表
export function purchasingGroupList(params) {
  return request({
    url: '/purchasing/group/list',
    method: 'get',
    params
  })
}
// 批量获取采购清单详情
export function purchasingDetails(params) {
  return request({
    url: '/purchasing/detail',
    method: 'get',
    params
  })
}
// 新增采购需求
export function purchasingDemandAdd(data) {
  return request({
    url: '/purchasing/demand',
    method: 'post',
    data
  })
}
// 查询采购需求列表
export function purchasingDemandList(params) {
  return request({
    url: '/purchasing/demand/list',
    method: 'get',
    params
  })
}
// // 获取采购需求回复数量(小红点)
// export function purchasingDemandReply(params) {
//   return request({
//     url: '/purchasing/demand/reply2',
//     method: 'get',
//     params
//   })
// }
// 获取采购需求回复数量(小红点)
export function purchasingDemandReply(params) {
  return request({
    url: '/purchasing/demand/reply3',
    method: 'get',
    params
  })
}
export function purchasingDemandReply3(params) {
  return request({
    url: '/purchasing/demand/reply3',
    method: 'get',
    params
  })
}
// 删除采购需求
export function purchasingDemandDelete(params) {
  return request({
    url: '/purchasing/demand',
    method: 'delete',
    params
  })
}
// 采购需求详细信息
export function purchasingDemandDetail(params) {
  return request({
    url: '/purchasing/demand/detail',
    method: 'get',
    params
  })
}
// 发布采购需求
export function purchasingDemandPublish(data) {
  return request({
    url: '/purchasing/demand/send',
    method: 'post',
    data
  })
}
// 检查需求是否可发布公域(已停止使用)
export function purchasingPublishCommon(data) {
  return request({
    url: '/purchasing/demand/send/check',
    method: 'post',
    data
  })
}
// 查询该需求已发布过的供应商
export function purchasingPublishSupplier(params) {
  return request({
    url: '/purchasing/demand/published/supplier',
    method: 'get',
    params
  })
}
// 供应商查看需求详细信息
export function supplierViewPurchasing(params) {
  return request({
    url: '/purchasing/demand/formFilling',
    method: 'get',
    params
  })
}
// 供应商回复
export function supplierReplyPruchasing(data) {
  return request({
    url: '/purchasing/demand/reply',
    method: 'post',
    data
  })
}
// 查询需求产品回复
export function purchasingProductReply(params) {
  return request({
    url: '/purchasing/demand/product/reply',
    method: 'get',
    params
  })
}
// 生成订单号
export function purchasingOrderNumber(data) {
  return request({
    url: '/purchasing/order/base',
    method: 'post',
    data
  })
}
// 生成订单
export function purchasingOrderSave(data) {
  return request({
    url: '/purchasing/order/save',
    method: 'post',
    data
  })
}
// 订单列表
export function purchasingOrderList(params) {
  return request({
    url: '/purchasing/order/list',
    method: 'get',
    params
  })
}
// 订单列表(供应商)
export function purchasingOrderListSupplier(params) {
  return request({
    url: '/purchasing/order/list/supplier',
    method: 'get',
    params
  })
}
// 订单详情
export function purchasingOrderDetail(params) {
  return request({
    url: '/purchasing/order/detail',
    method: 'get',
    params
  })
}
// 删除订单
export function purchasingOrderDelete(params) {
  return request({
    url: '/purchasing/order',
    method: 'delete',
    params
  })
}
// 获取合同编号
export function createContractSerial(params) {
  return request({
    url: '/purchasing/contract/serial',
    method: 'get',
    params
  })
}
// 生成合同
export function createContract(data) {
  return request({
    url: '/purchasing/contract',
    method: 'post',
    data
  })
}
// 合同列表
export function contractList(params) {
  return request({
    url: '/purchasing/contract/list',
    method: 'get',
    params
  })
}
// 订单合同列表
export function contractOrderList(params) {
  return request({
    url: '/purchasing/contract/order/list',
    method: 'get',
    params
  })
}
// 删除合同
export function contractDelete(params) {
  return request({
    url: '/purchasing/contract',
    method: 'delete',
    params
  })
}
// 平台需求
export function purchasingDemandSeller(params) {
  return request({
    url: '/purchasing/demand/for/seller',
    method: 'get',
    params
  })
}
// 二维码
export function createQrcode(params) {
  return request({
    url: '/common/qrcode',
    method: 'get',
    params
  })
}
// 合同详情
export function contractDetail(params) {
  return request({
    url: '/purchasing/contract/file',
    method: 'get',
    params
  })
}
// 发送合同
export function contractSend(params) {
  return request({
    url: '/purchasing/contract/send',
    method: 'get',
    params
  })
}
// 查看合同(发送合同之后)
export function contractView(params) {
  return request({
    url: '/purchasing/contract/file2',
    method: 'get',
    params
  })
}
// 合同签名
export function contractSign(data) {
  return request({
    url: '/purchasing/contract/signature',
    method: 'post',
    data
  })
}
// 合同发送记录
export function contractSendRecord(params) {
  return request({
    url: '/purchasing/contract/send/log',
    method: 'get',
    params
  })
}
// 新增合同模板
export function contractTemplateAdd(data) {
  return request({
    url: '/purchasing/contract/template',
    method: 'post',
    data
  })
}
// 删除合同模板
export function contractTemplateDelete(params) {
  return request({
    url: '/purchasing/contract/template',
    method: 'delete',
    params
  })
}
// 修改合同模板
export function contractTemplateUpdate(data) {
  return request({
    url: '/purchasing/contract/template',
    method: 'put',
    data
  })
}
// 合同模板列表
export function contractTemplateList(params) {
  return request({
    url: '/purchasing/contract/template/list',
    method: 'get',
    params
  })
}
// 合同模板设置默认模板
export function contractTemplateDefault(params) {
  return request({
    url: '/purchasing/contract/template/default',
    method: 'put',
    params
  })
}
// 查询提示规则
export function contractTemplateRule(params) {
  return request({
    url: '/tips/rule',
    method: 'get',
    params
  })
}
// 新增/修改提示规则
export function contractTemplateRuleAdd(data) {
  return request({
    url: '/tips/rule',
    method: 'post',
    data
  })
}
// 生成合同(采购清单)
export function createContractPurchasing(data) {
  return request({
    url: '/purchasing/contract/abnormal',
    method: 'post',
    data
  })
}
// 采购清单列表--采购完成列表
export function purchasingListComplete(params) {
  return request({
    url: '/purchasing/group/done/list',
    method: 'get',
    params
  })
}
// 产品分组
export function purchasingProductGroup(params) {
  return request({
    url: '/purchasing/demand/pre',
    method: 'get',
    params
  })
}
// 公域发布时产品名称分词
export function purchasingProductWord(params) {
  return request({
    url: '/purchasing/demand/analysis',
    method: 'get',
    params
  })
}
// 根据产品名称分词查询匹配公域供应商
export function purchasingProductWordSupplier(params) {
  return request({
    url: '/purchasing/demand/match/supplier',
    method: 'get',
    params
  })
}
// 销售合同列表
export function contractSaleList(params) {
  return request({
    url: '/purchasing/contract/for/seller',
    method: 'get',
    params
  })
}
// 已发送未签署的合同
export function contractUnsign(params) {
  return request({
    url: '/purchasing/contract/unSign/list',
    method: 'get',
    params
  })
}
// 需求发布产品关联私域供应商
export function purchasingDemandSupplier(params) {
  return request({
    url: '/purchasing/demand/match/private',
    method: 'get',
    params
  })
}
// 提交合同审批
export function contractApproval(data) {
  return request({
    url: '/wx/cp/apply/event',
    method: 'post',
    data
  })
}
// 查看合同审批申请记录
export function contractApprovalRecord(params) {
  return request({
    url: '/wx/cp/get/approval/history',
    method: 'get',
    params
  })
}
// 获取审批申请详情
export function contractApprovalDetail(params) {
  return request({
    url: '/wx/cp/get/approval/detail',
    method: 'get',
    params
  })
}
// 查询合同价格变更记录
export function contractPriceChange(params) {
  return request({
    url: '/purchasing/contract/detail',
    method: 'get',
    params
  })
}
// 需求发送记录查询记录详情 requstId
export function purchasingDemandSendRecord(params) {
  return request({
    url: '/purchasing/demand/send/history/detail',
    method: 'get',
    params
  })
}
// 需求分享详情
export function purchasingDemandShareDetail(params) {
  return request({
    url: '/system/index/demand/share/detail',
    method: 'get',
    params
  })
}
// 需求分享回复
export function purchasingDemandShareReply(data) {
  return request({
    url: '/purchasing/demand/share/reply',
    method: 'post',
    data
  })
}
// 设置参与者（合同参与者）
export function contractParticipant(data) {
  return request({
    url: '/business/participant/setting',
    method: 'post',
    data
  })
}
// 获取参与者（合同参与者）
export function contractParticipantList(params) {
  return request({
    url: '/business/participant/list',
    method: 'get',
    params
  })
}
