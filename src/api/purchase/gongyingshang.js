import request ,{download} from "@/utils/request";

// 列表
export function getlist(query) {
  return request({
    url: "/purchase/supplier/list",
    method: "get",
    params: query,
  });
}

//添加富文本
export function addText(query) {
  return request({
    url: "/rich/text",
    method: "post",
    data: query,
  });
}

//查询富文本
export function searchText(query) {
  return request({
    url: "/rich/text/detail",
    method: "get",
    params: query,
  });
}

// 新增
export function addlist(query) {
  return request({
    url: "/purchase/supplier",
    method: "post",
    data: query,
  });
}
//修改
export function editlist (query) {
  return request({
    url: "/purchase/supplier",
    method: "put",
    data: query,
  });
}
// 删除
export function dellist (query) {
  return request({
    url: "/purchase/supplier",
    method: "DELETE",
    params: query,
  });
}

// 修改排序
export function updateOrder(data) {
  return request({
    url: '/purchase/supplier/sort',
    method: 'put',
    data
  })
}
//
// export function getguiji(query) {
//   return request({
//     url: "/dis/vehicle/device/locus/get",
//     method: "get",
//     params: query,
//   });
// }
//
//


