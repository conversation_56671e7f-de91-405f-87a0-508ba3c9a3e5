import request ,{download} from "@/utils/request";

// 列表
export function getlist(query) {
  return request({
    url: "/system/review/list",
    method: "get",
    params: query,
  });
}

export function getlistb(query) {
  return request({
    url: "/system/review/list",
    method: "get",
    params: query,
  });
}
// 新增
export function add(query) {
  return request({
    url: "/system/review",
    method: "post",
    data: query,
  });
}
//审核
export function sh (query) {
  return request({
    url: "/system/review",
    method: "put",
    data: query,
  });
}
// 删除
export function dellist (query) {
  return request({
    url: "/system/review/"+ query.id,
    method: "DELETE",
    params: query,
  });
}
//
// export function getguiji(query) {
//   return request({
//     url: "/dis/vehicle/device/locus/get",
//     method: "get",
//     params: query,
//   });
// }
//
//
// 审核类目新增(审核通过)
export function auditCategoryAdd(data) {
  return request({
    url: "/system/verify/prepare/verify",
    method: "put",
    data
  });
}

// 审核类目列表
export  function auditCategoryList(params) {
  return request({
    url: "/system/verify/prepare/list",
    method: "get",
    params
  });
}

// 审核类目详情
export  function auditCategoryDetail(params) {
  return request({
    url: "/system/verify/prepare/detail",
    method: "get",
    params
  });
}


