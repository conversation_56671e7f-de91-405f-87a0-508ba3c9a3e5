import request ,{download} from "@/utils/request";

// 查询私域需求
export function getPrivateDemand(requestId) {
  return request({
    url: '/system/private/formFilling',
    method: 'get',
    headers: {
      isToken: false
    },
    params: {
      requestId: requestId
    }
  })
}

// 查询采购需求详细
export function getDemand(query) {
  return request({
    url: '/purchase/demand/formFilling',
    method: 'get',
    params: query,
  })
}
// 查询采购需求详细
export function formAdd(query) {
  return request({
    url: '/system/response/formAdd',
    method: 'post',
    data: query,
  })
}

