import request ,{download} from "@/utils/request";

// 列表
export function getlist(query) {
  return request({
    url: "/purchase/supplierAudit/list",
    method: "get",
    params: query,
  });
}

//审核
export function sh (query) {
  return request({
    url: "/purchase/supplierAudit",
    method: "put",
    data: query,
  });
}

//
// export function getguiji(query) {
//   return request({
//     url: "/dis/vehicle/device/locus/get",
//     method: "get",
//     params: query,
//   });
// }
//
//


