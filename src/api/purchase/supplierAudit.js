import request from '@/utils/request'

// 查询供应商审核列表
export function listSupplierAudit(query) {
  return request({
    url: '/purchase/supplierAudit/list',
    method: 'get',
    params: query
  })
}

// 新增供应商审核
export function addSupplierAudit(data) {
  return request({
    url: '/purchase/supplierAudit/add',
    method: 'post',
    data: data,
    headers: {
      isToken: false
    }
  })
}

// 修改供应商审核
export function updateSupplierAudit(data) {
  return request({
    url: '/purchase/supplierAudit',
    method: 'put',
    data: data
  })
}

// 删除供应商审核
export function delSupplierAudit(id) {
  return request({
    url: '/purchase/supplierAudit/',
    method: 'delete',
    params: {
      ids: id
    }
  })
}
