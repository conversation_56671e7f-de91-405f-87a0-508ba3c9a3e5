import request from "@/utils/request";

// 查询我的需求列表
export function demandForMeList(params) {
  return request({
    url: '/system/demand/forMe',
    method: 'get',
    params: params
  })
}

// 回复需求
export function replyDemand(data) {
  return request({
    url: '/system/response/formAdd',
    method: 'post',
    data: data
  })
}

//撤回需求
export function recallDemand(responseId) {
  return request({
    url: '/system/response/recall',
    method: 'put',
    data: {
      "id": responseId
    }
  })
}

export function demandDetail(requestId) {
  return request({
    url: '/purchase/demand/formFilling',
    method: 'get',
    params: {
      "requestId": requestId
    }
  })
}
