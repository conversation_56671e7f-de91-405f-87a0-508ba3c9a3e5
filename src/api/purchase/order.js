import request from '@/utils/request'

// 查询订单列表
export function listOrder(query) {
  return request({
    url: '/purchase/order/list',
    method: 'get',
    params: query
  })
}
export function privateList(query) {
  return request({
    url: '/purchase/order/privateList',
    method: 'get',
    params: query
  })
}


// 查询订单评价列表
export function evaluateListOrder(query) {
  return request({
    url: '/purchase/order/evaluateList',
    method: 'get',
    params: query
  })
}


// 查询订单详细
export function getOrder(id) {
  return request({
    url: '/purchase/order/' + id,
    method: 'get'
  })
}

// 新增订单
export function addOrder(data) {
  return request({
    url: '/purchase/order',
    method: 'post',
    data: data
  })
}
// 新增订单
export function createOrder(data) {
  return request({
    url: '/purchase/order/create',
    method: 'post',
    data: data
  })
}
// 订单完成
export function orderComplete(data) {
  return request({
    url: '/purchase/order/orderComplete',
    method: 'post',
    data: data
  })
}


export function privateCreate(data) {
  return request({
    url: '/system/privateOrder/create',
    method: 'post',
    data: data
  })
}


// 私域订单完成
export function privateOrderComplete(data) {
  return request({
    url: '/purchase/order/privateOrderComplete',
    method: 'post',
    data: data
  })
}



// 新增供应商评价
export function addOrderEvaluate(data) {
  return request({
    url: '/purchase/order/addEvaluate',
    method: 'post',
    data: data
  })
}

// 修改订单
export function updateOrder(data) {
  return request({
    url: '/purchase/order',
    method: 'put',
    data: data
  })
}

// 删除订单
export function delOrder(id) {
  return request({
    url: '/purchase/order/' + id,
    method: 'delete'
  })
}
