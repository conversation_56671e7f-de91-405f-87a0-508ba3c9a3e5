import request ,{download} from "@/utils/request";

//类目排序
export function sort (query) {
  return request({
    url: "/dysplasia/category/sort",
    method: "put",
    data: query,
  });
}

// 列表
export function getlist(query) {
  return request({
    url: "/dysplasia/category/list",
    method: "get",
    params: query,
  });
}

// 新增
export function addlist(query) {
  return request({
    url: "/dysplasia/category",
    method: "post",
    data: query,
  });
}
//修改
export function editlist (query) {
  return request({
    url: "/dysplasia/category",
    method: "put",
    data: query,
  });
}
// 删除
export function dellist (query) {
  return request({
    url: "/dysplasia/category",
    method: "DELETE",
    params: query,
  });
}