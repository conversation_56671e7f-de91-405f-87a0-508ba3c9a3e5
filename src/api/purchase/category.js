import request ,{download} from "@/utils/request";
// 新增审核类目
export function review(query) {
  return request({
    url: "/system/product/prepare/home",
    method: "post",
    data: query,
  });
}

//类目排序
export function sort (query) {
  return request({
    url: "/purchase/category/sort",
    method: "put",
    data: query,
  });
}

// 列表
export function getlist(query) {
  return request({
    url: "/purchase/category/list",
    method: "get",
    params: query,
  });
}

//查询类目下产品
export function getProductByCategory(query) {
  return request({
    url: "/system/product/getProductByCategory",
    method: "get",
    params: query,
  });
}

export function getlistb(query) {
  return request({
    url: "/purchase/category/list",
    method: "get",
    params: query,
  });
}
// 新增
export function addlist(query) {
  return request({
    url: "/purchase/category",
    method: "post",
    data: query,
  });
}
//修改
export function editlist (query) {
  return request({
    url: "/purchase/category",
    method: "put",
    data: query,
  });
}
// 删除
export function dellist (query) {
  return request({
    url: "/purchase/category",
    method: "DELETE",
    params: query,
  });
}
//
// export function getguiji(query) {
//   return request({
//     url: "/dis/vehicle/device/locus/get",
//     method: "get",
//     params: query,
//   });
// }
//
//


// 标签列表
export function labelList(query) {
  return request({
    url: "/label/all",
    method: "get",
    params: query,
  });
}

// 新增标签
export function labelAdd(query) {
  return request({
    url: "/label",
    method: "post",
    data: query,
  });
}

// 删除标签
export function labelDel (query) {
  return request({
    url: "/label/"+query,
    method: "DELETE"
  });
}

// 查询类目下VIP品牌
export function categoryBrand(params){
  return request({
    url: '/system/index/brand/list',
    method: 'get',
    params
  })
}

// 查询品牌分类产品
export function brandProduct(params){
  return request({
    url: '/system/index/brand/product',
    method: 'get',
    params
  })
}

// 查询品牌类目
export function brandCategory(params){
  return request({
    url: '/system/index/brand/category',
    method: 'get',
    params
  })
}