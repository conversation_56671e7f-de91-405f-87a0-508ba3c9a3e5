import request from '@/utils/request'
// 钢材价格列表
export function getKline(params) {
  return request({
    url: '/steel/prices/list',
    method: 'get',
    params
  })
}
// 新增-修改钢材价格
export function createKline(data) {
  return request({
    url: '/steel/prices',
    method: 'post',
    data
  })
}
// 删除钢材价格
export function deleteKline(params) {
  return request({
    url: '/steel/prices',
    method: 'delete',
    params
  })
}
//钢厂列表
export function getFactoryList(params) {
  return request({
    url: '/steel/plants/list',
    method: 'get',
    params
  })
}
//新增钢厂
export function createFactory(data) {
  return request({
    url: '/steel/plants',
    method: 'post',
    data
  })
}
//删除钢厂
export function deleteFactory(params) {
  return request({
    url: '/steel/plants',
    method: 'delete',
    params
  })
}
//修改钢厂
export function updateFactory(data) {
  return request({
    url: '/steel/plants',
    method: 'put',
    data
  })
}
//钢材列表
export function getSteelList(params) {
  return request({
    url: '/steel/types/list',
    method: 'get',
    params
  })
}
//新增钢材
export function createSteel(data) {
  return request({
    url: '/steel/types',
    method: 'post',
    data
  })
}
//修改钢材
export function updateSteel(data) {
  return request({
    url: '/steel/types',
    method: 'put',
    data
  })
}
//删除钢材
export function deleteSteel(params) {
  return request({
    url: '/steel/types',
    method: 'delete',
    params
  })
}
//首页K线
export function getHomeKline(params) {
  return request({
    url: '/system/index/steel/indexes',
    method: 'get',
    params
  })
}
// 黑色金属行情数据
export function getMetalMarket(params) {
  return request({
    url: '/system/index/black/metal',
    method: 'get',
    params
  })
}
