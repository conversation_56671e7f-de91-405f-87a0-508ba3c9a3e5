import request from '@/utils/request'
// 新增定制产品
export function addCustomization(data) {
  return request({
    url: '/customized/product',
    method: 'post',
    data
  })
}
// 修改定制产品(自建产品)
export function updateCustomization(data) {
  return request({
    url: '/customized/product/mine',
    method: 'put',
    data
  })
}
// 删除定制产品
export function deleteCustomization(params) {
  return request({
    url: '/customized/product',
    method: 'delete',
    params
  })
}
// 查询定制产品
export function getCustomization(params) {
  return request({
    url: '/customized/product/list',
    method: 'get',
    params
  })
}
// 受理产品定制
export function acceptCustomization(data) {
  return request({
    url: '/customized/product/accept',
    method: 'post',
    data
  })
}
// 发布定制需求
export function releaseCustomization(data) {
  return request({
    url: '/customized/product/send',
    method: 'post',
    data
  })
}
// h5定制需求详细信息
export function detailCustomization(params) {
  return request({
    url: '/customized/product/formFilling',
    method: 'get',
    params
  })
}
// h5定制需求提交-供应商回复
export function submitCustomization(data) {
  return request({
    url: '/customized/product/reply',
    method: 'post',
    data
  })
}
// 查看发送记录
export function getSendRecord(params) {
  return request({
    url: '/customized/product/send',
    method: 'get',
    params
  })
}
// 查看回复
export function getReply(params) {
  return request({
    url: '/customized/product/reply',
    method: 'get',
    params
  })
}
// 公开定制产品
export function publicCustomization(data) {
  return request({
    url: '/customized/product/open/product',
    method: 'post',
    data
  })
}
// 关闭公开定制产品
export function closePublicCustomization(data) {
  return request({
    url: '/customized/product/close/product',
    method: 'post',
    data
  })
}
// 新增定制需求
export function addCustomizationDemand(data) {
  return request({
    url: '/customized/product/demand',
    method: 'post',
    data
  })
}
// 修改定制需求（引用产品）
export function updateCustomizationDemand(data) {
  return request({
    url: '/customized/product/quote',
    method: 'put',
    data
  })
}
// 首页定制产品
export function getHomeCustomization(params) {
  return request({
    url: '/system/index/list',
    method: 'get',
    params
  })
}
// 查询定制需求产品
export function getDemandList(params) {
  return request({
    url: '/customized/product/demand/list',
    method: 'get',
    params
  })
}
// 新增定制产品-系统
export function addCustomizationSys(data) {
  return request({
    url: '/customized/product/sys',
    method: 'post',
    data
  })
}
// 修改定制产品-系统
export function updateCustomizationSys(data) {
  return request({
    url: '/customized/product/sys',
    method: 'put',
    data
  })
}
// 删除定制产品-系统
export function deleteCustomizationSys(params) {
  return request({
    url: '/customized/product/sys',
    method: 'delete',
    params
  })
}