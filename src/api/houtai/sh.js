import request ,{download} from "@/utils/request";

// 列表
export function getlist(query) {
  return request({
    url: "/system/product/prepare/list",
    method: "get",
    params: query,
  });
}

export function getlistb(query) {
  return request({
    url: "/system/review/list",
    method: "get",
    params: query,
  });
}
// 新增
export function add(query) {
  return request({
    url: "/system/product/prepare/upgrade",
    method: "post",
    data: query,
  });
}
//审核
export function sh (query) {
  return request({
    url: "/system/product/prepare/verify",
    method: "put",
    data: query,
  });
}
// 删除
export function dellist (query) {
  return request({
    url: "/system/review/"+ query.id,
    method: "DELETE",
    params: query,
  });
}
//
// export function getguiji(query) {
//   return request({
//     url: "/dis/vehicle/device/locus/get",
//     method: "get",
//     params: query,
//   });
// }
//
//


// 推荐产品列表
export function recommendProductList(params){
  return request({
    url: '/system/verify/product/list',
    method: 'get',
    params
  })
}

// 审核私域产品推荐
export function auitPrivateProduct(data){
  return request({
    url: '/system/verify/product',
    method: 'put',
    data
  })
}

// 撤回推荐到公域产品
export function revocationPrivateProduct(params){
  return request({
    url: '/system/privateduct/recommend/revoke',
    method: 'post',
    params
  })
}