import request ,{download} from "@/utils/request";

// 列表
export function getlist(query) {
  return request({
    url: "/store/list/"+ query.type,
    method: "get",
    params: query,
  });
}

export function getlistb(query) {
  return request({
    url: "/store/value/list",
    method: "get",
    params: query,
  });
}


export function gylist(query) {
  return request({
    url: "/store/user",
    method: "get",
    params: query,
  });
}

//调蓄
export function shoucGo(query) {
  return request({
    url: "/store/goto",
    method: "put",
    data: query,
  });
}

export function shoucTo(query) {
  return request({
    url: "/store/to",
    method: "put",
    data: query,
  });
}

export function delshouc(query) {
  return request({
    url: "/store/value/del",
    method: "put",
    data: query,
  });
}


export function addlist(query) {
  return request({
    url: "/store/dir",
    method: "post",
    data: query,
  });
}

export function editlist(query) {
  return request({
    url: "/store/dir",
    method: "put",
    data: query,
  });
}

export function dellist(query) {
  return request({
    url: "/store/dir",
    method: "DELETE",
    params: query,
  });
}

export function shengji(query) {
  return request({
    url: "/system/product/prepare/upgrade",
    method: "post",
    data: query,
  });
}


