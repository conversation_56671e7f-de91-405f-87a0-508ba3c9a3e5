import request ,{download} from "@/utils/request";

// 列表
export function getlist(query) {
  return request({
    url:  "/purchase/order/list",
    method: "get",
    params: query,
  });
}


export function wclist(query) {
  return request({
    url:  "/purchase/order/orderComplete/",
    method: "post",
    data: query,
  });
}

//评价
export function score(query) {
  return request({
    url:  "/purchase/comment/score",
    method: "post",
    data: query,
  });
}

// 查看评价
export function viewScore(params){
  return request({
    url: "/purchase/comment/detail",
    method: "get",
    params
  })
}

//查看评价
export function detail(query) {
  return request({
    url:  "/purchase/comment/order/detail",
    method: "get",
    params: query,
  });
}



//
// export function getguiji(query) {
//   return request({
//     url: "/dis/vehicle/device/locus/get",
//     method: "get",
//     params: query,
//   });
// }
//
//


