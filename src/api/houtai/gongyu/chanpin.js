import request, { download } from '@/utils/request'
// 列表
export function getlist(query) {
  return request({
    url: '/system/product/list',
    method: 'get',
    params: query
  })
}
export function getlistb(query) {
  return request({
    url: '/system/product/getProductByCategory',
    method: 'get',
    params: query
  })
}
//公域产品列表
export function gycplist(query) {
  return request({
    url: '/system/product/list',
    method: 'get',
    params: query
  })
}
// 新增
export function addlist(query) {
  return request({
    url: '/system/product',
    method: 'post',
    data: query
  })
}
//修改
export function editlist(query) {
  return request({
    url: '/system/product',
    method: 'put',
    data: query
  })
}
// 删除
export function dellist(query) {
  return request({
    url: '/system/product',
    method: 'DELETE',
    params: query
  })
}
//上下架  1   -1
export function shangxia(query) {
  return request({
    url: '/system/product/online/switch',
    method: 'PUT',
    data: query
  })
}
// 搜索类似产品
export function searchlist(params) {
  return request({
    url: '/system/product/search',
    method: 'get',
    params
  })
}
// 修改产品只修改图片
export function editimg(data) {
  return request({
    url: '/system/product/img',
    method: 'put',
    data
  })
}
// 产品排序
export function sortlist(data) {
  return request({
    url: '/system/product/sort',
    method: 'put',
    data
  })
}
// 根据分类ID查询下属产品数量
export function getnum(params) {
  return request({
    url: '/system/product/category/count',
    method: 'get',
    params
  })
}
//
// export function getguiji(query) {
//   return request({
//     url: "/dis/vehicle/device/locus/get",
//     method: "get",
//     params: query,
//   });
// }
//
//


