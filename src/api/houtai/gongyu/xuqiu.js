import request, {download} from "@/utils/request";

// 列表
export function getlist(query) {
  return request({
    url: "/purchase/demand/list",
    method: "get",
    params: query,
  });
}



export function fb(query) {
  return request({
    url: "/purchase/demand/send",
    method: "post",
    data: query,
  });
}

// 新增
export function addlist(query) {
  return request({
    url: "/purchase/demand",
    method: "post",
    data: query,
  });
}

//修改
export function editlist(query) {
  return request({
    url: "/purchase/demand",
    method: "put",
    data: query,
  });
}

// 删除
export function dellist(query) {
  return request({
    url: "/purchase/demand",
    method: "DELETE",
    params: query,
  });
}


export function scorder(query) {
  return request({
    url: "/purchase/order/create",
    method: "post",
    data: query,
  });
}

export function fabu(query) {
  return request({
    url: "/purchase/demand/send",
    method: "post",
    data: query,
  });
}

export function huifulist(query) {
  return request({
    url: "/system/response/list",
    method: "get",
    params: query,
  });
}

export function getstagetemplate(query) {
  return request({
    url: "/quote/getTemplateByStage/"+query,
    method: "get",
    params: query,
  });
}

export function getalltemplate(query) {
  return request({
    url: "/quote/getAllstages",
    method: "get",
    params: query,
  });
}

export function getbaojiatabs(query) {
  return request({
    url: "/quote/list",
    method: "get",
    params: query,
  });
}

export function getproductbyid(query) {
  return request({
    url: "/system/privateduct/"+query,
    method: "get",
    params: query,
  });
}

export function guancaijisuan(query) {
  return request({
    url: "/quote/pipeCount",
    method: "post",
    data: query,
  });
}

export function savequoteinfo(query) {
  return request({
    url: "/quote/saveQuoteInfo",
    method: "post",
    data: query,
  });
}

export function updatequoteinfo(query) {
  return request({
    url: "/quote/updateQuoteInfo",
    method: "post",
    data: query,
  });
}

export function getquoteInfos(query) {
  return request({
    url: "/quote/getQuoteInfos/"+query,
    method: "get",
    params: query,
  });
}

export function getquoteInfobyid(query) {
  return request({
    url: "/quote/getQuoteInfoById/"+query,
    method: "get",
    params: query,
  });
}



