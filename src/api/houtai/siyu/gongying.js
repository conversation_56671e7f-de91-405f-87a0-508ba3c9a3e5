import request, { download } from '@/utils/request'
// 列表
export function getlist(query) {
  return request({
    url: '/system/privateSup/list',
    method: 'get',
    params: query
  })
}
export function getchanpin(query) {
  return request({
    url: '/system/privateduct/list',
    method: 'get',
    params: query
  })
}
//私域供应商升级为公域供应商
export function upgrade(query) {
  return request({
    url: '/upgrade/supplier',
    method: 'post',
    data: query
  })
}
//公域产品列表
export function getlistb(query) {
  return request({
    url: '/system/product/list',
    method: 'get',
    params: query
  })
}
// 推荐列表列表
export function tjlist(query) {
  return request({
    url: '/system/privateSup/recommend/list',
    method: 'get',
    params: query
  })
}
//推荐到公域
export function privateSup(query) {
  return request({
    url: '/system/privateSup/recommend',
    method: 'put',
    params: query
  })
}
export function privateSupb(query) {
  return request({
    url: '/system/privateSup',
    method: 'get',
    params: query
  })
}
// 新增
export function addlist(query) {
  return request({
    url: '/system/privateSup',
    method: 'post',
    data: query
  })
}
//修改
export function editlist(query) {
  return request({
    url: '/system/privateSup',
    method: 'put',
    data: query
  })
}
// 删除
export function dellist(query) {
  return request({
    url: '/system/privateSup',
    method: 'DELETE',
    params: query
  })
}
// 启用/停用
export function changeStatus(params) {
  return request({
    url: '/system/privateSup/online/switch',
    method: 'put',
    params
  })
}
//
// export function getguiji(query) {
//   return request({
//     url: "/dis/vehicle/device/locus/get",
//     method: "get",
//     params: query,
//   });
// }
//
//
// 共享供应商
export function shareSupplier(data) {
  return request({
    url: '/system/privateSup/share',
    method: 'put',
    data
  })
}
// 供应商提示规则
export function getSupplierRule(params) {
  return request({
    url: '/tips/rule/supplier',
    method: 'get',
    params
  })
}
// 新增/修改供应商提示规则
export function addSupplierRule(data) {
  return request({
    url: '/tips/rule/supplier',
    method: 'post',
    data
  })
}
// 修改供应商类型
export function updateSupplierType(data) {
  return request({
    url: '/system/privateSup/tipRule',
    method: 'put',
    data
  })
}
