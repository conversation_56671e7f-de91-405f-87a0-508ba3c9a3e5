import request ,{download} from "@/utils/request";

// 列表
export function getlist(query) {
  return request({
    url: "/system/private/list",
    method: "get",
    params: query,
  });
}

export function getlistb(query) {
  return request({
    url: "/system/privateduct/list",
    method: "get",
    params: query,
  });
}

export function fabua(query) {
  return request({
    url: "/system/private/sendPri",
    method: "post",
    data: query,
  });
}

export function baojia(query) {
  return request({
    url: "/system/privateRe/list",
    method: "get",
    params: query,
  });
}
export function scorder(query) {
  return request({
    url: "/system/privateOrder/create",
    method: "post",
    data: query,
  });
}






// 新增
export function addlist(query) {
  return request({
    url: "/system/private",
    method: "post",
    data: query,
  });
}
//修改
export function editlist (query) {
  return request({
    url: "/system/private",
    method: "put",
    data: query,
  });
}
// 删除
export function dellist (query) {
  return request({
    url: "/system/private",
    method: "DELETE",
    params: query,
  });
}
//
// export function getguiji(query) {
//   return request({
//     url: "/dis/vehicle/device/locus/get",
//     method: "get",
//     params: query,
//   });
// }
//
//


