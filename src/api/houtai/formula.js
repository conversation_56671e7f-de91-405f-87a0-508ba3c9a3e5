import request, { download } from '@/utils/request'

//查询k线
export function k_s(query) {
  return request({
    url: '/quote/k/reference',
    method: 'get',
    params: query
  })
}

//批量修改保存草稿
export function draft_save(query) {
  return request({
    url: '/quote/price/draft',
    method: 'post',
    data: query
  })
}

//查询是否有批量修改保存草稿
export function draft_get(query) {
  return request({
    url: '/quote/price/draft',
    method: 'get',
    params: query
  })
}

//同步
export function draft_del(query) {
  return request({
    url: '/quote/price/draft',
    method: 'put',
    data: query
  })
}

//需要同步的报价列表
export function quote_sync(query) {
  return request({
    url: '/quote/price/sync/list',
    method: 'get',
    params: query
  })
}

//同步
export function sync_ok(query) {
  return request({
    url: '/quote/price/sync',
    method: 'put',
    data: query
  })
}

//取消同步
export function sync_no(query) {
  return request({
    url: '/quote/price/cancel/sync',
    method: 'put',
    data: query
  })
}

//根据选择的阶段模板查询报价以及片段价格
export function list_with(query) {
  return request({
    url: '/quote/list/with/template',
    method: 'get',
    params: query
  })
}

//修改报价描述
export function list_with_add(query) {
  return request({
    url: '/quote/price/update/with/template',
    method: 'put',
    data: query
  })
}

//查询关联了私域产品的阶段报价
export function reference_list(query) {
  return request({
    url: '/quote/private/product/reference',
    method: 'get',
    params: query
  })
}

//修改报价描述
export function dynamic(query) {
  return request({
    url: '/quote/template/dynamic',
    method: 'put',
    data: query
  })
}

//获取最新报价
export function new_price(query) {
  return request({
    url: '/quote/new/total',
    method: 'get',
    params: query
  })
}

//阶段价格列表
export function quota_list(query) {
  return request({
    url: '/quote/price/list',
    method: 'get',
    params: query
  })
}

//阶段价格历史
export function quota_history(query) {
  return request({
    url: '/quote/price/history/list',
    method: 'get',
    params: query
  })
}

//新增模板
export function quota_add(query) {
  return request({
    url: '/quote/price',
    method: 'post',
    data: query
  })
}

//删除quota模板
export function quota_del(query) {
  return request({
    url: '/quote/price',
    method: 'delete',
    data: query
  })
}

//修改模板
export function quota_edi(query) {
  return request({
    url: '/quote/price',
    method: 'put',
    data: query
  })
}

//公域公式列表
export function gygs(query) {
  return request({
    url: '/quote/common/product/reference',
    method: 'get',
    params: query
  })
}

//私域公式列表
export function sygs(query) {
  return request({
    url: '/quote/common/product/reference',
    method: 'get',
    params: query
  })
}

//搜索产品
export function search(query) {
  return request({
    url: '/quote/product/search',
    method: 'get',
    params: query
  })
}

//搜过已经公式的报价
export function spublish(query) {
  return request({
    url: '/quote/search/publish',
    method: 'get',
    params: query
  })
}

//阶段价格列表
export function jdprice(query) {
  return request({
    url: '/quote/price/list',
    method: 'get',
    params: query
  })
}

//查询所有字段
export function zdlist(query) {
  return request({
    url: '/quote/item/list',
    method: 'get',
    params: query
  })
}
//新增模板
export function tmpadd(query) {
  return request({
    url: '/quote/template',
    method: 'post',
    data: query
  })
}

//删除模板
export function tmpdel(query) {
  return request({
    url: '/quote/template',
    method: 'delete',
    data: query
  })
}

//新增字段
export function zdadd(query) {
  return request({
    url: '/quote/item',
    method: 'post',
    data: query
  })
}

//删除字段
export function zddel(query) {
  return request({
    url: '/quote/item',
    method: 'delete',
    data: query
  })
}

//新增产品
export function product(query) {
  return request({
    url: '/quote/product',
    method: 'post',
    data: query
  })
}

//切换发布状态
export function fb(query) {
  return request({
    url: '/quote/publish',
    method: 'put',
    data: query
  })
}

//完成报价
export function bj_fin(query) {
  return request({
    url: '/quote/change/edit',
    method: 'put',
    data: query
  })
}

//公式计算
export function jisuan(query) {
  return request({
    url: '/quote/formula',
    method: 'put',
    data: query
  })
}

//新增片段
export function pdadd(query) {
  return request({
    url: '/quote/fragment',
    method: 'post',
    data: query
  })
}

//删除片段
export function pddel(query) {
  return request({
    url: '/quote/fragment',
    method: 'delete',
    params: query
  })
}

//报价详情
export function quotesel(query) {
  return request({
    url: '/quote',
    method: 'get',
    params: query
  })
}

// 查询报价基础信息
export function getQuoteBase(params) {
  return request({
    url: '/quote/base/info',
    method: 'get',
    params
  })
}

//新增报价
export function quoteadd(query) {
  return request({
    url: '/quote',
    method: 'post',
    data: query
  })
}

//修改报价
export function quoteedi(query) {
  return request({
    url: '/quote',
    method: 'put',
    data: query
  })
}

//删除报价
export function quotedel(query) {
  return request({
    url: '/quote',
    method: 'delete',
    data: query
  })
}

//修改片段
export function pdedi(query) {
  return request({
    url: '/quote/fragment',
    method: 'put',
    data: query
  })
}

// 所有阶段
export function stage(query) {
  return request({
    url: '/quote/stage',
    method: 'get',
    params: query
  })
}

//阶段模版
export function stagetemp(query) {
  return request({
    url: '/quote/template/' + query.stage,
    method: 'get',
    params: query
  })
}

// 报价计算列表
export function list(query) {
  return request({
    url: '/quote/list',
    method: 'get',
    params: query
  })
}

// 列表
export function getlist(query) {
  return request({
    url: '/system/product/prepare/list',
    method: 'get',
    params: query
  })
}

export function getlistb(query) {
  return request({
    url: '/system/review/list',
    method: 'get',
    params: query
  })
}
// 新增
export function add(query) {
  return request({
    url: '/system/product/prepare/upgrade',
    method: 'post',
    data: query
  })
}
//审核
export function sh(query) {
  return request({
    url: '/system/product/prepare/verify',
    method: 'put',
    data: query
  })
}
// 删除
export function dellist(query) {
  return request({
    url: '/system/review/' + query.id,
    method: 'DELETE',
    params: query
  })
}

// 复制
export function listItemCopy(data) {
  return request({
    url: '/quote/copy',
    method: 'post',
    data
  })
}

// 关联报价数量
export function listPriceNum(params) {
  return request({
    url: '/quote/price/reference',
    method: 'get',
    params
  })
}

// 历史报价
export function listHistoryList(params) {
  return request({
    url: '/quote/history/list',
    method: 'get',
    params
  })
}

// 生成饼状图
export function createPieCharts(params) {
  return request({
    url: '/quote/pie/chart',
    method: 'get',
    params
  })
}

// 模板收藏目录
export function quoteTemplateDir(params) {
  return request({
    url: '/quote/template/dir',
    method: 'get',
    params
  })
}

// 新增模板收藏目录
export function quoteTemplateDirAdd(data) {
  return request({
    url: '/quote/template/dir',
    method: 'post',
    data
  })
}

// 收藏中的模板
export function quoteTemplateDirValue(params) {
  return request({
    url: '/quote/template/dir/value',
    method: 'get',
    params
  })
}

// 发布前置检查
export function publishPreCheck(params) {
  return request({
    url: '/quote/publish/check',
    method: 'get',
    params
  })
}

// 查询是否有批量修改保存草稿(二级报价)
export function quoteInnerDraft(params) {
  return request({
    url: '/quote/price/inner/draft',
    method: 'get',
    params
  })
}

// 批量修改保存草稿(二级报价)
export function quoteInnerDraftSave(data) {
  return request({
    url: '/quote/price/inner/draft',
    method: 'post',
    data
  })
}

// 忽略草稿(二级报价)
export function quoteInnerDraftIgnore(data) {
  return request({
    url: '/quote/price/inner/draft',
    method: 'put',
    data
  })
}

// 需要同步的报价列表(二级报价)
export function quoteInnerList(params) {
  return request({
    url: '/quote/price/inner/sync/list',
    method: 'get',
    params
  })
}

// 同步(二级报价)
export function quoteInnerSync(data) {
  return request({
    url: '/quote/price/inner/sync',
    method: 'put',
    data
  })
}

// 取消同步(二级报价)
export function quoteInnerCancelSync(data) {
  return request({
    url: '/quote/price/inner/cancel/sync',
    method: 'put',
    data
  })
}
// 新增快速报价
export function AddQuickQuote(data){
  return request({
    url: '/quote/quickly',
    method: 'post',
    data
  })
}
// 修改快速报价
export function UpdateQuickQuote(data){
  return request({
    url: '/quote/quickly',
    method: 'put',
    data
  })
}
