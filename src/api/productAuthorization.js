import request from '@/utils/request'
// 请求授权产品
export function productApply(data) {
  return request({
    url: '/auth/product/apply',
    method: 'post',
    data
  })
}
// 请求授权产品列表
export function productApplyList(params) {
  return request({
    url: '/auth/product/apply/list',
    method: 'get',
    params
  })
}
// 撤销请求授权产品
export function productCancelApply(data) {
    return request({
      url: '/auth/product/cancel/apply',
      method: 'post',
      data
    })
  }
// 请求授权产品列表(审核)
export function AuthProductApplyList(params) {
    return request({
      url: '/system/verify/auth/product/apply/list',
      method: 'get',
      params
    })
  }
// 请求授权产品审核
export function authProductVerify(data) {
  return request({
    url: '/system/verify/auth/product/verify',
    method: 'put',
    data
  })
}

