import request from '@/utils/request'
// 新增施工项目
export function createInstallLog(data) {
  return request({
    url: '/installation/',
    method: 'post',
    data
  })
}
// 修改施工项目
export function updateInstallLog(data) {
  return request({
    url: '/installation/',
    method: 'put',
    data
  })
}
// 施工项目审批
export function approvalInstallLog(data) {
  return request({
    url: '/installation/approval',
    method: 'put',
    data
  })
}
// 施工项目详情
export function detailInstallLog(params) {
  return request({
    url: '/installation/detail',
    method: 'get',
    params
  })
}
// 施工项目列表
export function listInstallLog(params) {
  return request({
    url: '/installation/list',
    method: 'get',
    params
  })
}
// 添加施工记录
export function addInstallLog(data) {
  return request({
    url: '/installation/log',
    method: 'put',
    data
  })
}
// 施工结束/结束项目
export function endInstallLog(data) {
  return request({
    url: '/installation/done',
    method: 'put',
    data
  })
}
// 施工记录补充
export function supplementInstallLog(data) {
  return request({
    url: '/installation/supplement',
    method: 'put',
    data
  })
}
