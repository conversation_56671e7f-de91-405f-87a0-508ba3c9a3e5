import request from '@/utils/request'
// 批量新增工作计划项目
export function workPlanProjectBatchAdd(data) {
	return request({
		url: '/work/plan/batch/project',
		method: 'post',
		data
	})
}
// 新增工作计划项目
export function workPlanProjectAdd(data) {
	return request({
		url: '/work/plan/project',
		method: 'post',
		data
	})
}
// 修改工作计划项目
export function workPlanProjectRevise(data) {
	return request({
		url: '/work/plan/project',
		method: 'put',
		data
	})
}
// 审批工作计划项目
export function workPlanProjectApproval(data) {
	return request({
		url: '/work/plan/project/approval',
		method: 'post',
		data
	})
}
// 工作计划项目记录
export function workPlanProjectApprovalDetail(params) {
	return request({
		url: '/work/plan/project/approval',
		method: 'get',
		params
	})
}
// 工作计划项目列表
export function workPlanProjectList(params) {
	return request({
		url: '/work/plan/project/list',
		method: 'get',
		params
	})
}
// 工作计划项目详情
export function workPlanProjectDetail(params) {
	return request({
		url: '/work/plan/project',
		method: 'get',
		params
	})
}
// 修改工作计划项目状态
export function workPlanProjectStatusRevise(data) {
	return request({
		url: '/work/plan/project/status',
		method: 'put',
		data
	})
}
// 查询常用周期
export function workPlanWeekOfYear(params) {
	return request({
		url: '/work/plan/weekOfYear',
		method: 'get',
		params
	})
}
// 查询常用输入信息
export function workPlanProjectCommonly(params) {
	return request({
		url: '/work/plan/project/commonly/info',
		method: 'get',
		params
	})
}
// 修改工作计划项目审批
export function workPlanProjectApprovalEdit(data) {
	return request({
		url: '/work/plan/project/approval/update',
		method: 'post',
		data
	})
}
// 删除工作计划
export function workPlanProjectDelete(params) {
	return request({
		url: '/work/plan/project',
		method: 'delete',
		params
	})
}