import request from '@/utils/request'
// 企业配置列表
export function getConfigList(params) {
  return request({
    url: '/uoc/config/list',
    method: 'get',
    params
  })
}
// 新增企业配置
export function addConfig(data) {
  return request({
    url: '/uoc/config',
    method: 'post',
    data
  })
}
// 修改企业配置
export function updateConfig(data) {
  return request({
    url: '/uoc/config',
    method: 'put',
    data
  })
}
// 查询企业配置
export function getConfigDetail(params) {
  return request({
    url: '/uoc/config/key',
    method: 'get',
    params
  })
}
// 查询企业配置2
export function getConfigDetail2(params) {
  return request({
    url: '/uoc/config/key2',
    method: 'get',
    params
  })
}

