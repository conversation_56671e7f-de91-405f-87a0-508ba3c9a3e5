import request from '@/utils/request'
// 查询在线用户列表
export function list(query) {
  return request({
    url: '/monitor/online/list',
    method: 'get',
    params: query
  })
}
// 强退用户
export function forceLogout(tokenId) {
  return request({
    url: '/monitor/online/' + tokenId,
    method: 'delete'
  })
}
// 查询用户实况列表
export function realtimeList(params) {
  return request({
    url: '/jim/online/user',
    method: 'get',
    params
  })
}
// 验证用户token是否过期
export function checkToken(tokenId) {
  return request({
    url: '/monitor/online/' + tokenId,
    method: 'get'
  })
}
// 查询用户是否已登录
export function checkUserOnline(data) {
  return request({
    url: '/preLogin',
    method: 'post',
    data
  })
}
