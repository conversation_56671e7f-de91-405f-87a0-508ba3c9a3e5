import request from '@/utils/request'
// 字段映照：全部数据
export function getAllLanguage() {
  return request({
    url: '/text/filed/mapping/listForIndex',
    method: 'get'
  })
}
// 字段映照：分页查询
export function getLanguagePage(params) {
  return request({
    url: '/text/filed/mapping/list',
    method: 'get',
    params
  })
}
// 字段映照：新增
export function addLanguage(data) {
  return request({
    url: '/text/filed/mapping',
    method: 'post',
    data
  })
}
// 字段映照：编辑
export function editLanguage(data) {
  return request({
    url: '/text/filed/mapping',
    method: 'put',
    data
  })
}
// 字段映照：删除
export function deleteLanguage(params) {
  return request({
    url: '/text/filed/mapping',
    method: 'delete',
    params
  })
}
