import request from '@/utils/request'
// ocr识别
export function ocr(url, data, headers) {
  return request({
    url,
    method: 'post',
    headers: {
      isToken: false,
      ...headers
    },
    data
  })
}
// 二要素实名认证
export function realName(url, data, headers) {
  return request({
    url,
    method: 'post',
    headers: {
      isToken: false,
      ...headers
    },
    data
  })
}
// 上传图片
export function upload(data) {
  return request({
    url: '/common/upload',
    method: 'post',
    data
  })
}
