import request from '@/utils/request'
// 企业详情
export function enterprisesDetail(params) {
  return request({
    url: '/enterprise/detail',
    method: 'get',
    params
  })
}
// 提交企业跟进信息
export function enterprisesFollowup(data) {
  return request({
    url: '/enterprise/followup',
    method: 'put',
    data
  })
}
// 企业列表
export function enterprisesList(params) {
  return request({
    url: '/enterprise/list',
    method: 'get',
    params
  })
}
// 刷新企业信息
export function enterprisesRefresh(params) {
  return request({
    url: '/enterprise/refresh',
    method: 'get',
    params
  })
}
// 新增款项档案
export function paymentAarchives(data) {
  return request({
    url: '/payment/archives',
    method: 'post',
    data
  })
}
// 款项档案审批
export function paymentAarchivesApproval(data) {
  return request({
    url: '/payment/archives/approval',
    method: 'put',
    data
  })
}
// 款项档案审批详情
export function paymentAarchivesApprovalDetail(params) {
  return request({
    url: '/payment/archives/approval/detail',
    method: 'get',
    params
  })
}
// 款项档案详情
export function paymentAarchivesDetail(params) {
  return request({
    url: '/payment/archives/detail',
    method: 'get',
    params
  })
}
// 款项档案提交跟进信息
export function paymentAarchivesFollowup(data) {
  return request({
    url: '/payment/archives/followup',
    method: 'put',
    data
  })
}
// 款项档案列表
export function paymentAarchivesList(params) {
  return request({
    url: '/payment/archives/list',
    method: 'get',
    params,
    timeout: 30 * 60 * 1000 
  })
}
// 修改款项档案
export function paymentAarchivesModify(data) {
  return request({
    url: '/payment/archives',
    method: 'put',
    data
  })
}
// 归档已结清
export function paymentAarchivesArchive(data) {
  return request({
    url: '/payment/archives/done',
    method: 'put',
    data
  })
}
// 重启款项档案
export function paymentRestart(data) {
  return request({
    url: '/payment/archives/re/circulation',
    method: 'post',
    data
  })
}
// 根据企业名称搜索公司
export function searchByName(params) {
  return request({
    url: '/enterprise/search/by/name',
    method: 'get',
    params
  })
}
// 删除款项档案
export function deletePaymentAarchives(data) {
  return request({
    url: '/payment/archives/del',
    method: 'delete',
    data
  })
}
// 款项档案状态切换
export function paymentAarchivesSwitch(data) {
  return request({
    url: '/payment/archives/switch',
    method: 'put',
    data
  })
}
// 查询收藏企业
export function enterpriseCollectionList(params) {
  return request({
    url: '/enterprise/store',
    method: 'get',
    params
  })
}
// 收藏企业
export function enterpriseCollection(data) {
  return request({
    url: '/enterprise/store',
    method: 'post',
    data
  })
}
// 查询收藏企业--根据税号查询
export function enterpriseCollectionDetail(params) {
  return request({
    url: '/enterprise/store/exist',
    method: 'get',
    params
  })
}
// 取消收藏企业
export function enterpriseCollectionCancel(params) {
  return request({
    url: '/enterprise/store',
    method: 'delete',
    params
  })
}
// 查询收藏企业历史
export function enterpriseCollectionHistory(params) {
  return request({
    url: '/enterprise/store/history',
    method: 'get',
    params
  })
}
// 查询是否有收藏企业
export function enterpriseCollectionExist(params) {
  return request({
    url: '/enterprise/store/length',
    method: 'get',
    params
  })
}
