import request from '@/utils/request'
// 用章管理：列表
export function signetList(params) {
  return request({
    url: '/using/signet/list',
    method: 'get',
    params
  })
}
// 用章管理：录入
export function signetAdd(data) {
  return request({
    url: '/using/signet',
    method: 'post',
    data
  })
}
// 用章管理：修改
export function signetEdit(data) {
  return request({
    url: '/using/signet',
    method: 'put',
    data
  })
}
// 用章管理：删除
export function signetDel(params) {
  return request({
    url: '/using/signet',
    method: 'delete',
    params
  })
}
