import request from '@/utils/request'
// 合同发送记录 contractId
export function getRecordList(params) {
  return request({
    url: '/purchasing/contract/send/log',
    method: 'get',
    params
  })
}
// 查询需求发布记录 demandId
export function getDemandRecordList(params) {
  return request({
    url: '/purchasing/demand/send/history',
    method: 'get',
    params
  })
}
// 查询短信发送详情 phone，bizId
export function getMsgDetail(params) {
  return request({
    url: '/sms/query/send/details',
    method: 'get',
    params
  })
}
// 重新发送需求短信
export function reSendDemandMsg(data) {
  return request({
    url: '/purchasing/demand/resend',
    method: 'post',
    data
  })
}
