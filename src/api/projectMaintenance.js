import request from '@/utils/request'
// 项目报备列表
export function projectList(params) {
  return request({
    url: '/sales/slip/list',
    method: 'get',
    params
  })
}
// 项目名称校验
export function projectCheck(params) {
  return request({
    url: '/sales/slip/pre/check',
    method: 'get',
    params
  })
}
// 新增项目报备
export function projectAdd(data) {
  return request({
    url: '/sales/slip',
    method: 'post',
    data
  })
}
// 修改项目报备
export function projectEdit(data) {
  return request({
    url: '/sales/slip',
    method: 'put',
    data
  })
}
// 删除项目报备
export function projectDel(params) {
  return request({
    url: '/sales/slip',
    method: 'delete',
    params
  })
}
// 调整状态
export function statusEdit(data) {
  return request({
    url: '/sales/slip/status',
    method: 'put',
    data
  })
}
