import request from '@/utils/request'
// 滞销品列表
export function listUnsalable(params) {
  return request({
    url: '/unsalable/product/list',
    method: 'get',
    params
  })
}
// 新增滞销品
export function addUnsalable(data) {
  return request({
    url: '/unsalable/product',
    method: 'post',
    data
  })
}
// 删除滞销品
export function delUnsalable(params) {
  return request({
    url: '/unsalable/product',
    method: 'delete',
    params
  })
}
// 修改滞销品
export function updateUnsalable(data) {
  return request({
    url: '/unsalable/product',
    method: 'put',
    data
  })
}
// 上下架滞销品
export function changeUnsalableStatus(data) {
  return request({
    url: '/unsalable/product/online/switch',
    method: 'put',
    data
  })
}
// 滞销品详情
export function getUnsalable(productId) {
  return request({
    url: `/unsalable/product/${productId}`,
    method: 'get'
  })
}
// 首页滞销品
export function listHomeUnsalable(params) {
  return request({
    url: '/system/index/unsalable/list',
    method: 'get',
    params
  })
}
// 滞销品生成订单号
export function createUnsalableOrder(data) {
  return request({
    url: '/unsalable/order/base',
    method: 'post',
    data
  })
}
// 滞销品生成订单
export function payUnsalableOrder(data) {
  return request({
    url: '/unsalable/order/save',
    method: 'post',
    data
  })
}
// 滞销品订单列表
export function listUnsalableOrder(params) {
  return request({
    url: '/unsalable/order/list',
    method: 'get',
    params
  })
}
// 滞销品供应商
export function listUnsalableSupplier(params) {
  return request({
    url: '/unsalable/order/list/supplier',
    method: 'get',
    params
  })
}
// 滞销品订单详情
export function getUnsalableOrder(params) {
  return request({
    url: '/unsalable/order/detail',
    method: 'get',
    params
  })
}
// 删除滞销品订单
export function delUnsalableOrder(params) {
  return request({
    url: '/unsalable/order',
    method: 'delete',
    params
  })
}
// 滞销品合同编号
export function getUnsalableContractNo(params) {
  return request({
    url: '/unsalable/contract/serial',
    method: 'get',
    params
  })
}
// 滞销品合同列表
export function listUnsalableContract(params) {
  return request({
    url: '/unsalable/contract/list',
    method: 'get',
    params
  })
}
// 滞销品订单合同列表
export function listUnsalableOrderContract(params) {
  return request({
    url: '/unsalable/contract/order/list',
    method: 'get',
    params
  })
}
// 滞销品生成合同
export function createUnsalableContract(data) {
  return request({
    url: '/unsalable/contract',
    method: 'post',
    data
  })
}
// 滞销品删除合同
export function delUnsalableContract(params) {
  return request({
    url: '/unsalable/contract',
    method: 'delete',
    params
  })
}
// 滞销品合同详情
export function getUnsalableContract(params) {
  return request({
    url: '/unsalable/contract/file',
    method: 'get',
    params
  })
}
// 滞销品签名查看合同
export function getUnsalableContractSign(params) {
  return request({
    url: '/unsalable/contract/file2',
    method: 'get',
    params
  })
}
// 滞销品合同签名
export function signUnsalableContract(data) {
  return request({
    url: '/unsalable/contract/signature',
    method: 'post',
    data
  })
}
// 滞销品供应商合同
export function listUnsalableSupplierContract(params) {
  return request({
    url: '/unsalable/contract/for/seller',
    method: 'get',
    params
  })
}
// 滞销品发送合同
export function sendUnsalableContract(params) {
  return request({
    url: '/unsalable/contract/send',
    method: 'get',
    params
  })
}
// 滞销品发送合同记录
export function listUnsalableContractLog(params) {
  return request({
    url: '/unsalable/contract/send/log',
    method: 'get',
    params
  })
}
// 查询滞销品价格是否有修改
export function checkUnsalablePrice(params) {
  return request({
    url: '/unsalable/product/config/tip',
    method: 'get',
    params
  })
}
// 更新价格/忽略价格
export function updateUnsalablePrice(data) {
  return request({
    url: '/unsalable/product/ignoreOrRefresh/price',
    method: 'put',
    data
  })
}
