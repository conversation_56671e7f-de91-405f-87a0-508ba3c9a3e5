import request from '@/utils/request'
// 新增生产任务单
export function addProduction(data) {
  return request({
    url: '/production/task/order',
    method: 'post',
    data
  })
}
// 获取生产任务单列表
export function getProductionList(params) {
  return request({
    url: '/production/task/order/list',
    method: 'get',
    params
  })
}
// 下派生产任务单
export function dispatchProduction(data) {
  return request({
    url: '/production/task/order/lower/faction',
    method: 'post',
    data
  })
}
// 更改标记
export function changeMark(data) {
  return request({
    url: '/production/task/order/change/mark',
    method: 'post',
    data
  })
}
// 下发单据
export function dispatchOrder(data) {
  return request({
    url: '/production/task/order/push',
    method: 'put',
    data
  })
}
