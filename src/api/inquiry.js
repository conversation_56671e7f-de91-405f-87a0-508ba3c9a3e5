import request from '@/utils/request'
// 内部询价列表
export function inquiryList(params) {
  return request({
    url: '/inquiry/list',
    method: 'get',
    params
  })
}
// 查询上次审批|抄送人
export function lastIdentity(params) {
  return request({
    url: '/inquiry/last/time/identity',
    method: 'get',
    params
  })
}
// 新增内部询价
export function inquiryAdd(data) {
  return request({
    url: '/inquiry/',
    method: 'post',
    data
  })
}
// 询价回复
export function inquiryReply(data) {
    return request({
      url: '/inquiry/reply',
      method: 'put',
      data
    })
  }