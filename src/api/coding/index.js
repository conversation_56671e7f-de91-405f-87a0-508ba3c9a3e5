import request from '@/utils/request'
// 编码产品列表
export function codingProductList(params) {
  return request({
    url: '/coding/rules/product/list',
    method: 'get',
    params
  })
}
// 新增编码产品
export function codingProductAdd(data) {
  return request({
    url: '/coding/rules/product',
    method: 'post',
    data
  })
}
// 修改编码产品
export function codingProductEdit(data) {
  return request({
    url: '/coding/rules/product',
    method: 'put',
    data
  })
}
// 删除编码产品
export function codingProductDel(params) {
  return request({
    url: '/coding/rules/product',
    method: 'delete',
    params
  })
}
// 查询所有编码规则
export function codingRuleList(params) {
  return request({
    url: '/coding/rules',
    method: 'get',
    params
  })
}
// 新增编码规则
export function codingRuleAdd(data) {
  return request({
    url: '/coding/rules',
    method: 'post',
    data
  })
}
// 删除编码规则
export function codingRuleDel(params) {
  return request({
    url: '/coding/rules',
    method: 'delete',
    params
  })
}
// 预生成产品编码
export function codingProductCode(params) {
  return request({
    url: '/coding/rules/code',
    method: 'get',
    params
  })
}
// 编码规则列表
export function codingRuleListAll(params) {
  return request({
    url: '/coding/rules/list',
    method: 'get',
    params
  })
}
// 修改编码规则
export function codingRuleEdit(data) {
  return request({
    url: '/coding/rules',
    method: 'put',
    data
  })
}
