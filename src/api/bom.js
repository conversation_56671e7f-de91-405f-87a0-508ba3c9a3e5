import request from '@/utils/request'
// 新增BOM
export function addBom(data) {
  return request({
    url: '/bom',
    method: 'post',
    data
  })
}
// 修改BOM
export function updateBom(data) {
  return request({
    url: '/bom',
    method: 'put',
    data
  })
}
// BOM详情
export function getBomDetail(params) {
  return request({
    url: '/bom/detail',
    method: 'get',
    params
  })
}
// BOM详情Code
export function getBomDetailByCode(params) {
  return request({
    url: '/bom/detailWithCode',
    method: 'get',
    params
  })
}
// 根据金蝶物料编码查询金蝶即时库存
export function getKingdeeStock(params) {
  return request({
    url: '/bom/inventory',
    method: 'get',
    params
  })
}
// BOM列表
export function getBomList(params) {
  return request({
    url: '/bom/list',
    method: 'get',
    params
  })
}
// 修改BOM状态
export function updateBomStatus(data) {
  return request({
    url: '/bom/status',
    method: 'put',
    data
  })
}
// 新增设备
export function addEquipment(data) {
  return request({
    url: '/equipment',
    method: 'post',
    data
  })
}
// 修改设备
export function editEquipment(data) {
  return request({
    url: '/equipment',
    method: 'put',
    data
  })
}
// 设备列表
export function getEquipmentList(params) {
  return request({
    url: '/equipment/list',
    method: 'get',
    params
  })
}
// 工艺|设备分类:新增
export function addProcessClass(data) {
  return request({
    url: '/process/class',
    method: 'post',
    data
  })
}
// 工艺|设备分类:修改
export function editProcessClass(data) {
  return request({
    url: '/process/class',
    method: 'put',
    data
  })
}
// 工艺分类:列表
export function getProcessClassList(params) {
  return request({
    url: '/process/class/list',
    method: 'get',
    params
  })
}
// 工艺分类:下拉
export function getProcessClassList2(params) {
  return request({
    url: '/process/class/list2',
    method: 'get',
    params
  })
}
// 设备分类:列表
export function getProcessEquipmentList(params) {
  return request({
    url: '/process/equipment/list',
    method: 'get',
    params
  })
}
// 新增工艺
export function addProcessInfo(data) {
  return request({
    url: '/process/info',
    method: 'post',
    data
  })
}
// 修改工艺
export function editProcessInfo(data) {
  return request({
    url: '/process/info',
    method: 'put',
    data
  })
}
// 工艺列表
export function getProcessInfoList(params) {
  return request({
    url: '/process/info/list',
    method: 'get',
    params
  })
}
// 产品标记记录
export function markProductBom(data) {
  return request({
    url: '/bom/product/mark',
    method: 'put',
    data
  })
}