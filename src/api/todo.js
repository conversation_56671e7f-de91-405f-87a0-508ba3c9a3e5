import request from '@/utils/request'
// 新增待办事项
export function todoItemsAdd(data) {
  return request({
    url: '/todo/items',
    method: 'post',
    data
  })
}
// 修改待办事项
export function todoItemsRevise(data) {
  return request({
    url: '/todo/items',
    method: 'put',
    data
  })
}
// 待办事项详情
export function todoItemsDetail(params) {
  return request({
    url: '/todo/items/detail',
    method: 'get',
    params
  })
}
// 待办事项-提交跟进信息
export function todoItemsFollowup(data) {
  return request({
    url: '/todo/items/followup',
    method: 'put',
    data
  })
}
// 待办事项列表
export function todoItemsList(params) {
  return request({
    url: '/todo/items/list',
    method: 'get',
    params
  })
}

// 修改待办事项状态
export function todoItemsStatus(data) {
  return request({
    url: '/todo/items/status',
    method: 'put',
    data
  })
}
// 日历列表
export function todoCalendarList(params) {
  return request({
    url: '/todo/calendar/list',
    method: 'get',
    params
  })
}
