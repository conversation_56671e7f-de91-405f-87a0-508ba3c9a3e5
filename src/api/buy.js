import request from '@/utils/request'
// 求购列表
export function getBuyList(params) {
  return request({
    url: '/buying/leads/list',
    method: 'get',
    params
  })
}
// 求购新增
export function addBuy(data) {
  return request({
    url: '/buying/leads',
    method: 'post',
    data
  })
}
// 求购修改
export function editBuy(data) {
  return request({
    url: '/buying/leads',
    method: 'put',
    data
  })
}
// 求购详情
export function detailBuy(params) {
  return request({
    url: '/buying/leads',
    method: 'get',
    params
  })
}
// 删除求购
export function deleteBuy(params) {
  return request({
    url: '/buying/leads',
    method: 'delete',
    params
  })
}
// 回复求购
export function replyBuy(data) {
  return request({
    url: '/buying/leads/reply',
    method: 'post',
    data
  })
}
// 首页回复求购
export function indexReplyBuy(params) {
  return request({
    url: '/buying/leads/reply/withMe',
    method: 'get',
    params
  })
}
// 求购所有回复
export function getBuyReplyList(params) {
  return request({
    url: '/buying/leads/reply',
    method: 'get',
    params
  })
}
// 撤回回复
export function cancelReply(params) {
  return request({
    url: '/buying/leads/reply/recall',
    method: 'post',
    params
  })
}
// 下架
export function offBuy(data) {
  return request({
    url: '/buying/leads/un/sale',
    method: 'put',
    data
  })
}
// 我要买货：助力提交
export function platformHelp(params) {
  return request({
    url: '/buying/leads/assist',
    method: 'post',
    params
  })
}
// 我要买货：助力列表
export function platformHelpList(params) {
  return request({
    url: '/buying/leads/assist',
    method: 'get',
    params
  })
}
