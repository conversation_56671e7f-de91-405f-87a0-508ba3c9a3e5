import request from '@/utils/request'
// 报修报废报告单: 新增
export function warrantyScrapAdd(data) {
    return request({
        url: '/warranty/scrap',
        method: 'post',
        data
    })
}
// 报修报废报告单: 审批
export function warrantyScrapApproval(data) {
    return request({
        url: '/warranty/scrap/approval',
        method: 'put',
        data
    })
}
// 报修报废报告单: 维护审批人
export function warrantyScrapApprovalUsersAdd(data) {
    return request({
        url: '/warranty/scrap/approval/users',
        method: 'post',
        data
    })
}
// 报修报废报告单: 查询审批人
export function warrantyScrapApprovalUsers(params) {
    return request({
        url: '/warranty/scrap/approval/users',
        method: 'get',
        params
    })
}
// 报修报废报告单: 列表
export function warrantyScrapList(params) {
    return request({
        url: '/warranty/scrap/list',
        method: 'get',
        params
    })
}
// 报修报废报告单: 详情
export function warrantyScrapDetail(params) {
    return request({
        url: '/warranty/scrap/detail',
        method: 'get',
        params
    })
}
// 报修报废报告单: 删除
export function warrantyScrapDelete(params) {
    return request({
        url: '/warranty/scrap',
        method: 'delete',
        params
    })
}