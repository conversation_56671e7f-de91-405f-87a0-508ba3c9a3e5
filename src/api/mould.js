import request from '@/utils/request'
// 模具：新增
export function mouldCreate(data) {
  return request({
    url: '/mould/bom',
    method: 'post',
    data
  })
}
// 模具：修改
export function mouldUpdate(data) {
  return request({
    url: '/mould/bom',
    method: 'put',
    data
  })
}
// 模具：修改状态
export function mouldUpdateStatus(data) {
  return request({
    url: '/mould/bom/status',
    method: 'put',
    data
  })
}
// 模具：详情
export function mouldDetail(params) {
  return request({
    url: '/mould/detail',
    method: 'get',
    params
  })
}
// 模具：列表
export function mouldList(params) {
  return request({
    url: '/mould/list',
    method: 'get',
    params
  })
}
