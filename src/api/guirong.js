import guirong from '@/utils/guirong'

export function login(data) {
  return guirong({
    url: '/login',
    headers: {
      isToken: false
    },
    method: 'post',
    data: data
  })
}
// 根据名称/统一社会信用代码查询公司列表
export function list(params, token) {
  return guirong({
    url: '/system/crawler/list',
    headers: {
      isToken: false,
      Authorization: token
    },
    method: 'get',
    params
  })
}
// 基础信息--股东信息
export function holderList(params, token) {
  return guirong({
    url: '/base/holder/list',
    headers: {
      isToken: false,
      Authorization: token
    },
    method: 'get',
    params
  })
}
// 基础信息--企业年报
export function annualList(params, token) {
  return guirong({
    url: '/base/annual/list',
    headers: {
      isToken: false,
      Authorization: token
    },
    method: 'get',
    params
  })
}
// 基础信息--分支机构
export function branchList(params, token) {
  return guirong({
    url: '/base/branch_info/list',
    headers: {
      isToken: false,
      Authorization: token
    },
    method: 'get',
    params
  })
}
// 基础信息--工商变更
export function changeList(params, token) {
  return guirong({
    url: '/base/business_change/list',
    headers: {
      isToken: false,
      Authorization: token
    },
    method: 'get',
    params
  })
}
// 基础信息--动产抵押
export function chattelList(params, token) {
  return guirong({
    url: '/base/chattel_mortgage/list',
    headers: {
      isToken: false,
      Authorization: token
    },
    method: 'get',
    params
  })
}
// 基础信息--控股子公司
export function childList(params, token) {
  return guirong({
    url: '/base/child_company/list',
    headers: {
      isToken: false,
      Authorization: token
    },
    method: 'get',
    params
  })
}
// 基础信息--工商信息
export function infoList(params, token) {
  return guirong({
    url: '/base/company_info/list',
    headers: {
      isToken: false,
      Authorization: token
    },
    method: 'get',
    params
  })
}
// 基础信息--股权穿透
export function penetrationList(params, token) {
  return guirong({
    url: '/base/equity_penetration/list',
    headers: {
      isToken: false,
      Authorization: token
    },
    method: 'get',
    params
  })
}
// 基础信息--高管信息
export function managerList(params, token) {
  return guirong({
    url: '/base/manager_info/list',
    headers: {
      isToken: false,
      Authorization: token
    },
    method: 'get',
    params
  })
}
// 基础信息--疑似关系
export function suspiciousList(params, token) {
  return guirong({
    url: '/base/suspicious_relationship/list',
    headers: {
      isToken: false,
      Authorization: token
    },
    method: 'get',
    params
  })
}
// 监管处罚--行政处罚
export function administrativeList(params, token) {
  return guirong({
    url: '/supervise/administrative_penalties/list',
    headers: {
      isToken: false,
      Authorization: token
    },
    method: 'get',
    params
  })
}
// 监管处罚--行政监管措施
export function regulatoryList(params, token) {
  return guirong({
    url: '/supervise/regulatory_measures/list',
    headers: {
      isToken: false,
      Authorization: token
    },
    method: 'get',
    params
  })
}
// 监管处罚--纪律处分
export function disciplinaryList(params, token) {
  return guirong({
    url: '/supervise/disciplinary_action/list',
    headers: {
      isToken: false,
      Authorization: token
    },
    method: 'get',
    params
  })
}
// 监管处罚--金融监管
export function financialList(params, token) {
  return guirong({
    url: '/supervise/financial_penalties/list',
    headers: {
      isToken: false,
      Authorization: token
    },
    method: 'get',
    params
  })
}
// 监管处罚--自律监管措施
export function selfRegulatoryList(params, token) {
  return guirong({
    url: '/supervise/regulatory_action/list',
    headers: {
      isToken: false,
      Authorization: token
    },
    method: 'get',
    params
  })
}
// 企业经营--招投标
export function biddingList(params, token) {
  return guirong({
    url: '/manage/bidding/list',
    headers: {
      isToken: false,
      Authorization: token
    },
    method: 'get',
    params
  })
}
// 企业经营--客户
export function customerList(params, token) {
  return guirong({
    url: '/manage/customer/list',
    headers: {
      isToken: false,
      Authorization: token
    },
    method: 'get',
    params
  })
}
// 企业经营--担保企业
export function guaranteeList(params, token) {
  return guirong({
    url: '/manage/guarantee_company/list',
    headers: {
      isToken: false,
      Authorization: token
    },
    method: 'get',
    params
  })
}
// 企业经营--信托融资担保
export function guaranteeOTList(params, token) {
  return guirong({
    url: '/manage/guaranteed_other/list',
    headers: {
      isToken: false,
      Authorization: token
    },
    method: 'get',
    params
  })
}
// 企业经营--债权担保
export function guaranteeZQList(params, token) {
  return guirong({
    url: '/manage/guaranteed_zhaiquan/list',
    headers: {
      isToken: false,
      Authorization: token
    },
    method: 'get',
    params
  })
}
// 企业经营--担保人
export function guarantorList(params, token) {
  return guirong({
    url: '/manage/guarantor/list',
    headers: {
      isToken: false,
      Authorization: token
    },
    method: 'get',
    params
  })
}
// 企业经营--土地信息
export function landList(params, token) {
  return guirong({
    url: '/manage/land_data/list',
    headers: {
      isToken: false,
      Authorization: token
    },
    method: 'get',
    params
  })
}
// 企业经营--榜单资质
export function listQualification(params, token) {
  return guirong({
    url: '/manage/list_qualifications/list',
    headers: {
      isToken: false,
      Authorization: token
    },
    method: 'get',
    params
  })
}
// 企业经营--作品著作权
export function paperList(params, token) {
  return guirong({
    url: '/manage/paper_list/list',
    headers: {
      isToken: false,
      Authorization: token
    },
    method: 'get',
    params
  })
}
// 企业经营--财产线索
export function propertyList(params, token) {
  return guirong({
    url: '/manage/property_clues/list',
    headers: {
      isToken: false,
      Authorization: token
    },
    method: 'get',
    params
  })
}
// 企业经营--提供担保
export function provideList(params, token) {
  return guirong({
    url: '/manage/provide_guarantee_other/list',
    headers: {
      isToken: false,
      Authorization: token
    },
    method: 'get',
    params
  })
}
// 企业经营--提供债权担保
export function provideZQList(params, token) {
  return guirong({
    url: '/manage/provide_guarantee_zhaiquan/list',
    headers: {
      isToken: false,
      Authorization: token
    },
    method: 'get',
    params
  })
}
// 企业经营--专利
export function rightList(params, token) {
  return guirong({
    url: '/manage/right_list/list',
    headers: {
      isToken: false,
      Authorization: token
    },
    method: 'get',
    params
  })
}
// 企业经营--软件著作权
export function softList(params, token) {
  return guirong({
    url: '/manage/soft_list/list',
    headers: {
      isToken: false,
      Authorization: token
    },
    method: 'get',
    params
  })
}
// 企业经营--供应商
export function supplierList(params, token) {
  return guirong({
    url: '/manage/supplier/list',
    headers: {
      isToken: false,
      Authorization: token
    },
    method: 'get',
    params
  })
}
// 司法诉讼--开庭公告
export function courtList(params, token) {
  return guirong({
    url: '/judiciary/court_announcement/list',
    headers: {
      isToken: false,
      Authorization: token
    },
    method: 'get',
    params
  })
}
// 司法诉讼--送达公告
export function deliveryList(params, token) {
  return guirong({
    url: '/judiciary/delivery_announcement/list',
    headers: {
      isToken: false,
      Authorization: token
    },
    method: 'get',
    params
  })
}
// 司法诉讼--失信被执行人
export function dishonestList(params, token) {
  return guirong({
    url: '/judiciary/dishonest_debtor/list',
    headers: {
      isToken: false,
      Authorization: token
    },
    method: 'get',
    params
  })
}
// 司法诉讼--被执行人
export function executedList(params, token) {
  return guirong({
    url: '/judiciary/executed_person/list',
    headers: {
      isToken: false,
      Authorization: token
    },
    method: 'get',
    params
  })
}
// 司法诉讼--立案信息
export function filingList(params, token) {
  return guirong({
    url: '/judiciary/filing_information/list',
    headers: {
      isToken: false,
      Authorization: token
    },
    method: 'get',
    params
  })
}
// 司法诉讼--终本案件
export function finalList(params, token) {
  return guirong({
    url: '/judiciary/final_case/list',
    headers: {
      isToken: false,
      Authorization: token
    },
    method: 'get',
    params
  })
}
// 司法诉讼--法院公告
export function legalList(params, token) {
  return guirong({
    url: '/judiciary/legal_announcement/list',
    headers: {
      isToken: false,
      Authorization: token
    },
    method: 'get',
    params
  })
}
// 司法诉讼--限制高消费
export function restrainingList(params, token) {
  return guirong({
    url: '/judiciary/restraining_orders/list',
    headers: {
      isToken: false,
      Authorization: token
    },
    method: 'get',
    params
  })
}
// 新闻公告--新闻公告研报
export function newsList(params, token) {
  return guirong({
    url: '/news/news/list',
    headers: {
      isToken: false,
      Authorization: token
    },
    method: 'get',
    params
  })
}
// 信用评级--其他领域信评
export function ratingOTList(params, token) {
  return guirong({
    url: '/credit/other_rating_list/list',
    headers: {
      isToken: false,
      Authorization: token
    },
    method: 'get',
    params
  })
}
// 信用评级--纳税信用等级
export function ratingTaxList(params, token) {
  return guirong({
    url: '/credit/tax_rating/list',
    headers: {
      isToken: false,
      Authorization: token
    },
    method: 'get',
    params
  })
}
