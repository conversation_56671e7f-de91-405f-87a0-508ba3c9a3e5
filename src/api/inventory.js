import request from '@/utils/request'
// 库存管理：历史
export function inventoryHistory(data) {
  return request({
    url: '/inventory/manage/history?number=' + data.number,
    method: 'get'
  })
}
// 库存管理：列表
export function inventoryList(params) {
  return request({
    url: '/inventory/manage/list',
    method: 'get',
    params
  })
}
// 物料即时库存：查询
export function inventoryQty(params) {
  return request({
    url: '/inventory/manage/qty',
    method: 'get',
    params
  })
}
// 库存管理：补充库存
export function inventorySupplement(data) {
  return request({
    url: '/inventory/manage/supplement',
    method: 'post',
    data
  })
}