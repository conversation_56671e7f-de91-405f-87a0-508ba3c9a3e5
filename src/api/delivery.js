import request from '@/utils/request'
// 发货清单: 新增
export function deliveryAdd(data) {
  return request({
    url: '/delivery/list',
    method: 'post',
    data
  })
}
// 发货清单: 详情
export function deliveryDetail(params) {
  return request({
    url: '/delivery/list/detail',
    method: 'get',
    params
  })
}
// 发货清单: 列表
export function deliveryList(params) {
  return request({
    url: '/delivery/list/list',
    method: 'get',
    params
  })
}
// 发货清单: 状态修改
export function deliveryEdit(data) {
  return request({
    url: '/delivery/list/status',
    method: 'put',
    data
  })
}
// 发货清单: 删除
export function deliveryDelete(data) {
  return request({
    url: '/delivery/list',
    method: 'delete',
    data
  })
}
// 发货清单: 确认退货
export function deliveryConfirmReturn(data) {
  return request({
    url: '/delivery/list/confirm/return',
    method: 'put',
    data
  })
}
// 发货清单: 确认收货（链接）
export function deliveryConfirmTake(data) {
  return request({
    url: '/delivery/list/confirm/take',
    method: 'put',
    data
  })
}
// 发货清单：退货（链接）
export function deliveryReturn(data) {
  return request({
    url: '/delivery/list/return',
    method: 'put',
    data
  })
}
// 发货清单: 发货
export function deliverySend(data) {
  return request({
    url: '/delivery/list/send',
    method: 'post',
    data
  })
}
// 发货清单：详情（链接）
export function deliveryLinkDetail(params) {
  return request({
    url: '/delivery/list/send/detail',
    method: 'get',
    params
  })
}
// 发货清单：发送记录
export function deliverySendLog(params) {
  return request({
    url: '/delivery/list/send/log',
    method: 'get',
    params
  })
}
// 收货清单：列表
export function deliveryTakeList(params) {
  return request({
    url: '/delivery/list/take/list',
    method: 'get',
    params
  })
}
