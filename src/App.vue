<template>
  <div id="app" @contextmenu="disableRightClick">
    <router-view />
    <theme-picker />
  </div>
</template>

<script>
import ThemePicker from '@/components/ThemePicker'
import { getToken } from '@/utils/auth'
import { checkToken } from '@/api/monitor/online'

export default {
  name: 'App',
  components: { ThemePicker },
  updated() {
    if (getToken() && !this.isconnect && (this.userType === '00' || this.userType === '05')) this.connect()
    if (getToken() && !!this.service && !this.service_isconnect && (this.userType == '00' || this.userType == '05')) this.service_connect()
  },
  watch: {
    isOffline: {
      deep: true,
      handler(val) {
        if (val) this.showOffline()
      }
    }
  },
  computed: {
    userType() {
      return this.$store.getters.userType
    },
    isconnect() {
      return this.$store.getters.isconnect
    },
    name() {
      return this.$store.getters.name
    },
    service_isconnect() {
      return this.$store.getters.service_isconnect
    },
    service() {
      return this.$store.getters.service
    },
    isOffline() {
      return this.$store.getters.isOffline
    },
    curUser() {
      return this.$store.getters.curUser
    }
  },
  metaInfo() {
    return {
      title: this.$store.state.settings.dynamicTitle && this.$store.state.settings.title,
      titleTemplate: title => {
        return title ? `${title} - ${process.env.VUE_APP_TITLE}` : process.env.VUE_APP_TITLE
      }
    }
  },
  mounted() {
    document.body.addEventListener('plugin_web_update_notice', e => {
      const { version, options } = e.detail
      window.location.reload()
    })
  },
  methods: {
    connect() {
      const url = process.env.VUE_APP_WS_URL + '?username=' + this.name + '&type=user&token=' + getToken()
      this.$store.dispatch('WEBSOCKET_INIT', url)
    },
    service_connect() {
      const url = process.env.VUE_APP_WS_URL + '?username=' + this.service + '&type=customner'
      this.$store.dispatch('SERVICE_WEBSOCKET_INIT', url)
    },
    showOffline() {
      if (!this.isOffline) return
      const token = this.curUser.ext && this.curUser.ext.token
      if (!token) return
      checkToken(token).then(res => {
        if (!res.data) {
          this.$alert('您的账号已在其他地方登录，已被迫下线。\n' + '如果这不是您本人操作，为保障账号安全，建议您修改密码。', '安全提示', {
            dangerouslyUseHTMLString: true,
            confirmButtonText: '确定',
            callback: action => {
              this.$store.dispatch('LogOut').then(() => {
                location.href = '/login'
              })
            }
          })
        } else this.$store.dispatch('WEBSOCKET_SETOFFLINE', false)
      })
    },
    disableRightClick(event) {
      event.preventDefault()
    }
  }
}
</script>
<style scoped>
#app .theme-picker {
  display: none;
  pointer-events: none;
  user-select: none;
}
</style>
<style>
.el-form-item__label:not(.custom-search *) {
  padding-top: 10px !important;
  padding-bottom: 10px !important;
  line-height: 20px !important;
}
.el-form-item__label {
  font-weight: normal !important;
}
.el-form-item__label:not(.el-dialog__body *) {
  width: auto !important;
}
</style>
