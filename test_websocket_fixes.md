# WebSocket 群组消息修复测试指南

## 修复内容总结

### 问题一：群主创建群组后成员收到多条重复信息 ✅ 已修复
**修复位置**: `src/store/modules/webscoket.js` - 多个函数的优化

**修复内容**:
1. **增强节流机制**: 在 `throttleCmd17Request` 函数中添加了更严格的条件判断
2. **状态检查优化**: 添加初始连接阶段和群组创建流程的状态检查
3. **函数级别保护**: 在 `COMMAND_ADD_FRIEND_RESP` 和 `COMMAND_ADD_FRIEND_APPLY_HANDLE` 中添加群组创建流程检查
4. **请求计数和追踪**: 添加了 `cmd17RequestCount` 和 `cmd17RequestReasons` 来追踪请求
5. **详细调试日志**: 添加了全面的调试日志系统

### 问题二：群主在刚创建的群组内发消息成员收不到
**修复位置**: `src/components/Chat/index copy 4.vue` - `handleSend` 方法

**修复内容**:
1. 在群组消息发送时添加了缺失的 `groupId` 字段
2. 添加了消息发送的调试日志

**额外优化**:
- 在 `src/store/modules/webscoket.js` 的 `COMMAND_CHAT_RESP` 函数中添加了群组消息接收的调试日志
- 在 `src/components/Chat/index.vue` 中也添加了消息发送的调试日志

## 测试步骤

### 测试环境准备
1. 确保有至少2个用户账号可以测试
2. 打开浏览器开发者工具的控制台，查看调试日志

### 测试场景一：验证重复cmd17请求修复
1. **操作**: 用户A创建一个包含用户B的群组
2. **预期结果**: 
   - 用户B只收到一次 `{"cmd":17,"type":2,"userId":"xxx"}` 请求
   - 控制台显示相关的调试日志，说明重复请求被正确过滤
3. **验证方法**: 
   - 查看网络面板，确认cmd17请求只发送一次
   - 查看控制台日志，确认重复处理被跳过

### 测试场景二：验证群组消息发送接收
1. **操作**: 
   - 用户A创建群组后，在群组中发送消息
   - 用户B打开该群组
2. **预期结果**:
   - 用户B能够看到用户A发送的消息
   - 控制台显示群组消息发送和接收的调试日志
3. **验证方法**:
   - 确认消息在群组聊天界面正确显示
   - 查看控制台日志，确认消息包含正确的groupId字段

### 测试场景三：验证双向群组消息
1. **操作**:
   - 用户A在群组中发送消息
   - 用户B在同一群组中回复消息
2. **预期结果**:
   - 双方都能看到对方的消息
   - 消息按时间顺序正确排列
3. **验证方法**:
   - 确认消息在两个用户的界面都正确显示
   - 查看控制台日志，确认消息处理流程正常

## 调试日志说明

### cmd17请求节流日志
```
[WebSocket] 立即执行cmd17请求 - 原因: 登录成功, 总计: 1
[WebSocket] 最近请求原因: ["11:01:38: 登录成功"]
[WebSocket] cmd17请求被节流，延迟2000ms执行 - 原因: 群组创建成功
[WebSocket] 跳过cmd17请求 - 正在处理群组创建: 添加好友响应
```

### 群组消息发送日志
```
[Chat] 发送群组消息: {groupId: "xxx", from: "xxx", content: "xxx", chatType: 1}
```

### 群组消息接收日志
```
[WebSocket] 收到群组消息: {groupId: "xxx", from: "xxx", content: "xxx", chatType: 1, activeId: "xxx", groupsCount: x}
[WebSocket] 群组消息处理完成: xxx, 消息数量: x
[WebSocket] 警告: 未找到群组 xxx，当前群组列表: [{id: "yyy", name: "群组1"}]
```

### 群组操作完成日志
```
[WebSocket] 收到群组操作完成消息: {code: 10026, msg: "xxx", groupId: "xxx", pendingGroupId: null, isInitialConnection: false}
[WebSocket] 跳过群组操作完成处理 - 正在处理群组创建流程
[WebSocket] 跳过cmd17请求 - 群组相关消息: xxx
```

### 添加好友响应日志
```
[WebSocket] 收到添加好友响应: {type: "xxx", data: {...}, pendingGroupId: null, isInitialConnection: false}
[WebSocket] 跳过添加好友响应处理 - 正在处理群组创建流程
```

## 回滚方案
如果修复出现问题，可以通过以下方式回滚：

1. **恢复COMMAND_GROUP_OPERATION_COMPLETE函数**:
   - 移除新增的条件判断和日志
   - 恢复原有的简单逻辑

2. **恢复群组消息发送逻辑**:
   - 移除 `chatCmd.groupId = activeId` 这行代码
   - 移除相关的调试日志

3. **移除调试日志**:
   - 移除所有新增的 `console.log` 语句

## 注意事项
1. 测试时请注意观察控制台日志，确认修复逻辑正确执行
2. 如果仍有问题，请检查网络面板中的WebSocket消息内容
3. 建议在生产环境部署前，先在测试环境充分验证
