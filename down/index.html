<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>自由客紧固件App下载</title>
    <link rel="stylesheet" type="text/css" href="css.css" />
    <script type="text/javascript">
      function ResetSize() {
        var htmlEle = document.documentElement
        var htmlWidth = window.innerWidth
        if (!htmlWidth) return
        if (htmlWidth >= 750) {
          htmlEle.style.fontSize = '10px'
        } else {
          htmlEle.style.fontSize = 10 * (htmlWidth / 750) + 'px'
        }
      }
      ResetSize()
      window.addEventListener('resize', ResetSize, false)
      window.addEventListener('orientationchange', ResetSize, false)

      function handleClick() {
        if (typeof WeixinJSBridge === 'object' && typeof WeixinJSBridge.invoke === 'function') {
          const element = document.getElementById('bg')
          element.className = 'bg'
          const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream
          const tip = document.getElementById('tip')
          if (isIOS) tip.className = 'tip ios'
          else tip.className = 'tip'
        } else {
          var src = 'http://m.ziyouke.net/ziyouke.apk'
          var iframe = document.createElement('iframe')
          iframe.style.display = 'none'
          iframe.src = 'javascript: \'<script>location.href="' + src + '"<\/script>\''
          document.getElementsByTagName('body')[0].appendChild(iframe)
        }
      }
      function handleHide() {
        const element = document.getElementById('bg')
        element.className = 'bg none'
      }
    </script>
  </head>
  <body>
    <div id="bg" onclick="handleHide()">
      <div id="tip"></div>
    </div>
    <div class="container">
      <div class="phone"></div>
      <div class="icon"></div>
      <div class="down" onclick="handleClick()"></div>
    </div>
  </body>
</html>
