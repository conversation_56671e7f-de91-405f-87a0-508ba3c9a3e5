html,
body {
  font-family: sans-serifl;
  padding: 0;
  margin: 0;
  background: url('img/bg.png') center top no-repeat #2e73f2;
  background-size: 100% auto;
}
body,
div,
dl,
dt,
dd,
ul,
ol,
li,
h1,
h2,
h3,
h4,
h5,
h6,
pre,
code,
form,
fieldset,
legend,
input,
textarea,
p,
blockquote,
th,
td,
hr,
button,
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
  margin: 0;
  padding: 0;
}
input,
select,
textarea {
  font-size: 100%;
}
.container {
  margin: 0 auto;
  width: 100%;
  min-height: 100vh;
  box-sizing: border-box;
  padding: 2rem 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.container .phone {
  width: calc(215 / 375 * 100%);
  height: 84.6em;
  background: url(img/phone.png) center no-repeat;
  background-size: 100% 100%;
}
.container .icon {
  width: calc(259 / 375 * 100%);
  height: 19.2rem;
  background: url(img/icon.png) center no-repeat;
  background-size: 100% 100%;
  margin-top: 1rem;
}
.container .down {
  width: calc(345 / 375 * 100%);
  height: 10rem;
  background: url(img/btn.png) center no-repeat;
  background-size: 100% 100%;
  margin-top: 5rem;
}
.bg {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1;
  width: 100%;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.6);
}
.bg .tip {
  position: fixed;
  top: 1rem;
  right: 5rem;
  z-index: 2;
  width: 47rem;
  height: 14rem;
  background: url(img/browser.png) center no-repeat;
  background-size: 100% 100%;
}
.bg .tip.ios {
  background: url(img/browser_ios.png) center no-repeat;
  background-size: 100% 100%;
}
.none {
  display: none;
}
