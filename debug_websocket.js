// WebSocket 调试工具
// 在浏览器控制台中运行此脚本来监控 WebSocket 状态

(function() {
  'use strict';
  
  // 获取 Vuex store
  const store = window.$nuxt ? window.$nuxt.$store : (window.vueApp ? window.vueApp.$store : null);
  
  if (!store) {
    console.error('无法找到 Vuex store');
    return;
  }
  
  // 监控 WebSocket 状态
  function monitorWebSocketState() {
    const state = store.state.webscoket || store.state.websocket;
    if (!state) {
      console.error('无法找到 WebSocket state');
      return;
    }
    
    console.log('=== WebSocket 状态监控 ===');
    console.log('连接状态:', state.isconnect);
    console.log('用户ID:', state.userId);
    console.log('当前活跃ID:', state.activeId);
    console.log('群组数量:', state.groups ? state.groups.length : 0);
    console.log('好友数量:', state.friends ? state.friends.length : 0);
    console.log('未读消息数:', state.chatNum);
    console.log('初始连接状态:', state.isInitialConnection);
    console.log('待打开群组ID:', state.pendingGroupId);
    console.log('正在处理待打开群组:', state.isProcessingPendingGroup);
    console.log('cmd17请求计数:', state.cmd17RequestCount);
    console.log('最近cmd17请求原因:', state.cmd17RequestReasons);
    console.log('上次cmd17请求时间:', new Date(state.lastCmd17RequestTime).toLocaleTimeString());
    console.log('========================');
  }
  
  // 监控群组列表
  function monitorGroups() {
    const state = store.state.webscoket || store.state.websocket;
    if (!state || !state.groups) {
      console.error('无法找到群组数据');
      return;
    }
    
    console.log('=== 群组列表监控 ===');
    state.groups.forEach((group, index) => {
      console.log(`群组 ${index + 1}:`, {
        id: group.groupId,
        name: group.name,
        成员数: group.users ? group.users.length : 0,
        未读数: group.num || 0,
        消息数: group.msgList ? group.msgList.length : 0,
        群主: group.leader
      });
    });
    console.log('==================');
  }
  
  // 监控消息发送
  function monitorMessageSending() {
    const originalSend = store.dispatch;
    store.dispatch = function(action, payload) {
      if (action === 'WEBSOCKET_SEND' && payload && payload.cmd === 11) {
        console.log('=== 发送消息监控 ===');
        console.log('消息类型:', payload.chatType === 1 ? '群组消息' : '个人消息');
        console.log('发送者:', payload.from);
        console.log('接收者:', payload.to);
        console.log('群组ID:', payload.groupId || '无');
        console.log('内容:', payload.content.substring(0, 50) + (payload.content.length > 50 ? '...' : ''));
        console.log('==================');
      } else if (action === 'WEBSOCKET_SEND' && payload && payload.cmd === 17) {
        console.log('=== cmd17请求监控 ===');
        console.log('用户ID:', payload.userId);
        console.log('类型:', payload.type);
        console.log('时间:', new Date().toLocaleTimeString());
        console.log('==================');
      }
      return originalSend.call(this, action, payload);
    };
  }
  
  // 重置cmd17计数器
  function resetCmd17Counter() {
    const state = store.state.webscoket || store.state.websocket;
    if (state) {
      state.cmd17RequestCount = 0;
      state.cmd17RequestReasons = [];
      console.log('cmd17计数器已重置');
    }
  }
  
  // 导出调试函数到全局
  window.WebSocketDebug = {
    monitorState: monitorWebSocketState,
    monitorGroups: monitorGroups,
    monitorMessages: monitorMessageSending,
    resetCounter: resetCmd17Counter,
    
    // 快速检查函数
    check: function() {
      monitorWebSocketState();
      monitorGroups();
    },
    
    // 开始全面监控
    startMonitoring: function() {
      console.log('开始 WebSocket 全面监控...');
      monitorMessageSending();
      
      // 每5秒输出一次状态
      const interval = setInterval(() => {
        console.log('\n--- 定时状态检查 ---');
        monitorWebSocketState();
      }, 5000);
      
      // 返回停止函数
      return function stopMonitoring() {
        clearInterval(interval);
        console.log('WebSocket 监控已停止');
      };
    }
  };
  
  console.log('WebSocket 调试工具已加载！');
  console.log('使用方法:');
  console.log('- WebSocketDebug.check() - 检查当前状态');
  console.log('- WebSocketDebug.monitorState() - 监控 WebSocket 状态');
  console.log('- WebSocketDebug.monitorGroups() - 监控群组列表');
  console.log('- WebSocketDebug.resetCounter() - 重置 cmd17 计数器');
  console.log('- WebSocketDebug.startMonitoring() - 开始全面监控');
  
})();
